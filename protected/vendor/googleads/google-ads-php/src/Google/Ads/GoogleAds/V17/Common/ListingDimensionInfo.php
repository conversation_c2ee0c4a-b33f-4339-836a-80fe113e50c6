<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v17/common/criteria.proto

namespace Google\Ads\GoogleAds\V17\Common;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Listing dimensions for listing group criterion.
 *
 * Generated from protobuf message <code>google.ads.googleads.v17.common.ListingDimensionInfo</code>
 */
class ListingDimensionInfo extends \Google\Protobuf\Internal\Message
{
    protected $dimension;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type \Google\Ads\GoogleAds\V17\Common\HotelIdInfo $hotel_id
     *           Advertiser-specific hotel ID.
     *     @type \Google\Ads\GoogleAds\V17\Common\HotelClassInfo $hotel_class
     *           Class of the hotel as a number of stars 1 to 5.
     *     @type \Google\Ads\GoogleAds\V17\Common\HotelCountryRegionInfo $hotel_country_region
     *           Country or Region the hotel is located in.
     *     @type \Google\Ads\GoogleAds\V17\Common\HotelStateInfo $hotel_state
     *           State the hotel is located in.
     *     @type \Google\Ads\GoogleAds\V17\Common\HotelCityInfo $hotel_city
     *           City the hotel is located in.
     *     @type \Google\Ads\GoogleAds\V17\Common\ProductCategoryInfo $product_category
     *           Category of a product offer.
     *     @type \Google\Ads\GoogleAds\V17\Common\ProductBrandInfo $product_brand
     *           Brand of a product offer.
     *     @type \Google\Ads\GoogleAds\V17\Common\ProductChannelInfo $product_channel
     *           Locality of a product offer.
     *     @type \Google\Ads\GoogleAds\V17\Common\ProductChannelExclusivityInfo $product_channel_exclusivity
     *           Availability of a product offer.
     *     @type \Google\Ads\GoogleAds\V17\Common\ProductConditionInfo $product_condition
     *           Condition of a product offer.
     *     @type \Google\Ads\GoogleAds\V17\Common\ProductCustomAttributeInfo $product_custom_attribute
     *           Custom attribute of a product offer.
     *     @type \Google\Ads\GoogleAds\V17\Common\ProductItemIdInfo $product_item_id
     *           Item id of a product offer.
     *     @type \Google\Ads\GoogleAds\V17\Common\ProductTypeInfo $product_type
     *           Type of a product offer.
     *     @type \Google\Ads\GoogleAds\V17\Common\ProductGroupingInfo $product_grouping
     *           Grouping of a product offer. This listing dimension is deprecated and it
     *           is supported only in Display campaigns.
     *     @type \Google\Ads\GoogleAds\V17\Common\ProductLabelsInfo $product_labels
     *           Labels of a product offer. This listing dimension is deprecated and it is
     *           supported only in Display campaigns.
     *     @type \Google\Ads\GoogleAds\V17\Common\ProductLegacyConditionInfo $product_legacy_condition
     *           Legacy condition of a product offer. This listing dimension is deprecated
     *           and it is supported only in Display campaigns.
     *     @type \Google\Ads\GoogleAds\V17\Common\ProductTypeFullInfo $product_type_full
     *           Full type of a product offer. This listing dimension is deprecated and it
     *           is supported only in Display campaigns.
     *     @type \Google\Ads\GoogleAds\V17\Common\ActivityIdInfo $activity_id
     *           Advertiser-specific activity ID.
     *     @type \Google\Ads\GoogleAds\V17\Common\ActivityRatingInfo $activity_rating
     *           Rating of the activity as a number 1 to 5, where 5 is the best.
     *     @type \Google\Ads\GoogleAds\V17\Common\ActivityCountryInfo $activity_country
     *           The country where the travel activity is available.
     *     @type \Google\Ads\GoogleAds\V17\Common\ActivityStateInfo $activity_state
     *           The state where the travel activity is available.
     *     @type \Google\Ads\GoogleAds\V17\Common\ActivityCityInfo $activity_city
     *           The city where the travel activity is available.
     *     @type \Google\Ads\GoogleAds\V17\Common\UnknownListingDimensionInfo $unknown_listing_dimension
     *           Unknown dimension. Set when no other listing dimension is set.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Ads\GoogleAds\V17\Common\Criteria::initOnce();
        parent::__construct($data);
    }

    /**
     * Advertiser-specific hotel ID.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.common.HotelIdInfo hotel_id = 2;</code>
     * @return \Google\Ads\GoogleAds\V17\Common\HotelIdInfo|null
     */
    public function getHotelId()
    {
        return $this->readOneof(2);
    }

    public function hasHotelId()
    {
        return $this->hasOneof(2);
    }

    /**
     * Advertiser-specific hotel ID.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.common.HotelIdInfo hotel_id = 2;</code>
     * @param \Google\Ads\GoogleAds\V17\Common\HotelIdInfo $var
     * @return $this
     */
    public function setHotelId($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Common\HotelIdInfo::class);
        $this->writeOneof(2, $var);

        return $this;
    }

    /**
     * Class of the hotel as a number of stars 1 to 5.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.common.HotelClassInfo hotel_class = 3;</code>
     * @return \Google\Ads\GoogleAds\V17\Common\HotelClassInfo|null
     */
    public function getHotelClass()
    {
        return $this->readOneof(3);
    }

    public function hasHotelClass()
    {
        return $this->hasOneof(3);
    }

    /**
     * Class of the hotel as a number of stars 1 to 5.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.common.HotelClassInfo hotel_class = 3;</code>
     * @param \Google\Ads\GoogleAds\V17\Common\HotelClassInfo $var
     * @return $this
     */
    public function setHotelClass($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Common\HotelClassInfo::class);
        $this->writeOneof(3, $var);

        return $this;
    }

    /**
     * Country or Region the hotel is located in.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.common.HotelCountryRegionInfo hotel_country_region = 4;</code>
     * @return \Google\Ads\GoogleAds\V17\Common\HotelCountryRegionInfo|null
     */
    public function getHotelCountryRegion()
    {
        return $this->readOneof(4);
    }

    public function hasHotelCountryRegion()
    {
        return $this->hasOneof(4);
    }

    /**
     * Country or Region the hotel is located in.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.common.HotelCountryRegionInfo hotel_country_region = 4;</code>
     * @param \Google\Ads\GoogleAds\V17\Common\HotelCountryRegionInfo $var
     * @return $this
     */
    public function setHotelCountryRegion($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Common\HotelCountryRegionInfo::class);
        $this->writeOneof(4, $var);

        return $this;
    }

    /**
     * State the hotel is located in.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.common.HotelStateInfo hotel_state = 5;</code>
     * @return \Google\Ads\GoogleAds\V17\Common\HotelStateInfo|null
     */
    public function getHotelState()
    {
        return $this->readOneof(5);
    }

    public function hasHotelState()
    {
        return $this->hasOneof(5);
    }

    /**
     * State the hotel is located in.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.common.HotelStateInfo hotel_state = 5;</code>
     * @param \Google\Ads\GoogleAds\V17\Common\HotelStateInfo $var
     * @return $this
     */
    public function setHotelState($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Common\HotelStateInfo::class);
        $this->writeOneof(5, $var);

        return $this;
    }

    /**
     * City the hotel is located in.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.common.HotelCityInfo hotel_city = 6;</code>
     * @return \Google\Ads\GoogleAds\V17\Common\HotelCityInfo|null
     */
    public function getHotelCity()
    {
        return $this->readOneof(6);
    }

    public function hasHotelCity()
    {
        return $this->hasOneof(6);
    }

    /**
     * City the hotel is located in.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.common.HotelCityInfo hotel_city = 6;</code>
     * @param \Google\Ads\GoogleAds\V17\Common\HotelCityInfo $var
     * @return $this
     */
    public function setHotelCity($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Common\HotelCityInfo::class);
        $this->writeOneof(6, $var);

        return $this;
    }

    /**
     * Category of a product offer.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.common.ProductCategoryInfo product_category = 24;</code>
     * @return \Google\Ads\GoogleAds\V17\Common\ProductCategoryInfo|null
     */
    public function getProductCategory()
    {
        return $this->readOneof(24);
    }

    public function hasProductCategory()
    {
        return $this->hasOneof(24);
    }

    /**
     * Category of a product offer.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.common.ProductCategoryInfo product_category = 24;</code>
     * @param \Google\Ads\GoogleAds\V17\Common\ProductCategoryInfo $var
     * @return $this
     */
    public function setProductCategory($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Common\ProductCategoryInfo::class);
        $this->writeOneof(24, $var);

        return $this;
    }

    /**
     * Brand of a product offer.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.common.ProductBrandInfo product_brand = 15;</code>
     * @return \Google\Ads\GoogleAds\V17\Common\ProductBrandInfo|null
     */
    public function getProductBrand()
    {
        return $this->readOneof(15);
    }

    public function hasProductBrand()
    {
        return $this->hasOneof(15);
    }

    /**
     * Brand of a product offer.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.common.ProductBrandInfo product_brand = 15;</code>
     * @param \Google\Ads\GoogleAds\V17\Common\ProductBrandInfo $var
     * @return $this
     */
    public function setProductBrand($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Common\ProductBrandInfo::class);
        $this->writeOneof(15, $var);

        return $this;
    }

    /**
     * Locality of a product offer.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.common.ProductChannelInfo product_channel = 8;</code>
     * @return \Google\Ads\GoogleAds\V17\Common\ProductChannelInfo|null
     */
    public function getProductChannel()
    {
        return $this->readOneof(8);
    }

    public function hasProductChannel()
    {
        return $this->hasOneof(8);
    }

    /**
     * Locality of a product offer.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.common.ProductChannelInfo product_channel = 8;</code>
     * @param \Google\Ads\GoogleAds\V17\Common\ProductChannelInfo $var
     * @return $this
     */
    public function setProductChannel($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Common\ProductChannelInfo::class);
        $this->writeOneof(8, $var);

        return $this;
    }

    /**
     * Availability of a product offer.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.common.ProductChannelExclusivityInfo product_channel_exclusivity = 9;</code>
     * @return \Google\Ads\GoogleAds\V17\Common\ProductChannelExclusivityInfo|null
     */
    public function getProductChannelExclusivity()
    {
        return $this->readOneof(9);
    }

    public function hasProductChannelExclusivity()
    {
        return $this->hasOneof(9);
    }

    /**
     * Availability of a product offer.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.common.ProductChannelExclusivityInfo product_channel_exclusivity = 9;</code>
     * @param \Google\Ads\GoogleAds\V17\Common\ProductChannelExclusivityInfo $var
     * @return $this
     */
    public function setProductChannelExclusivity($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Common\ProductChannelExclusivityInfo::class);
        $this->writeOneof(9, $var);

        return $this;
    }

    /**
     * Condition of a product offer.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.common.ProductConditionInfo product_condition = 10;</code>
     * @return \Google\Ads\GoogleAds\V17\Common\ProductConditionInfo|null
     */
    public function getProductCondition()
    {
        return $this->readOneof(10);
    }

    public function hasProductCondition()
    {
        return $this->hasOneof(10);
    }

    /**
     * Condition of a product offer.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.common.ProductConditionInfo product_condition = 10;</code>
     * @param \Google\Ads\GoogleAds\V17\Common\ProductConditionInfo $var
     * @return $this
     */
    public function setProductCondition($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Common\ProductConditionInfo::class);
        $this->writeOneof(10, $var);

        return $this;
    }

    /**
     * Custom attribute of a product offer.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.common.ProductCustomAttributeInfo product_custom_attribute = 16;</code>
     * @return \Google\Ads\GoogleAds\V17\Common\ProductCustomAttributeInfo|null
     */
    public function getProductCustomAttribute()
    {
        return $this->readOneof(16);
    }

    public function hasProductCustomAttribute()
    {
        return $this->hasOneof(16);
    }

    /**
     * Custom attribute of a product offer.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.common.ProductCustomAttributeInfo product_custom_attribute = 16;</code>
     * @param \Google\Ads\GoogleAds\V17\Common\ProductCustomAttributeInfo $var
     * @return $this
     */
    public function setProductCustomAttribute($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Common\ProductCustomAttributeInfo::class);
        $this->writeOneof(16, $var);

        return $this;
    }

    /**
     * Item id of a product offer.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.common.ProductItemIdInfo product_item_id = 11;</code>
     * @return \Google\Ads\GoogleAds\V17\Common\ProductItemIdInfo|null
     */
    public function getProductItemId()
    {
        return $this->readOneof(11);
    }

    public function hasProductItemId()
    {
        return $this->hasOneof(11);
    }

    /**
     * Item id of a product offer.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.common.ProductItemIdInfo product_item_id = 11;</code>
     * @param \Google\Ads\GoogleAds\V17\Common\ProductItemIdInfo $var
     * @return $this
     */
    public function setProductItemId($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Common\ProductItemIdInfo::class);
        $this->writeOneof(11, $var);

        return $this;
    }

    /**
     * Type of a product offer.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.common.ProductTypeInfo product_type = 12;</code>
     * @return \Google\Ads\GoogleAds\V17\Common\ProductTypeInfo|null
     */
    public function getProductType()
    {
        return $this->readOneof(12);
    }

    public function hasProductType()
    {
        return $this->hasOneof(12);
    }

    /**
     * Type of a product offer.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.common.ProductTypeInfo product_type = 12;</code>
     * @param \Google\Ads\GoogleAds\V17\Common\ProductTypeInfo $var
     * @return $this
     */
    public function setProductType($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Common\ProductTypeInfo::class);
        $this->writeOneof(12, $var);

        return $this;
    }

    /**
     * Grouping of a product offer. This listing dimension is deprecated and it
     * is supported only in Display campaigns.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.common.ProductGroupingInfo product_grouping = 17;</code>
     * @return \Google\Ads\GoogleAds\V17\Common\ProductGroupingInfo|null
     */
    public function getProductGrouping()
    {
        return $this->readOneof(17);
    }

    public function hasProductGrouping()
    {
        return $this->hasOneof(17);
    }

    /**
     * Grouping of a product offer. This listing dimension is deprecated and it
     * is supported only in Display campaigns.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.common.ProductGroupingInfo product_grouping = 17;</code>
     * @param \Google\Ads\GoogleAds\V17\Common\ProductGroupingInfo $var
     * @return $this
     */
    public function setProductGrouping($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Common\ProductGroupingInfo::class);
        $this->writeOneof(17, $var);

        return $this;
    }

    /**
     * Labels of a product offer. This listing dimension is deprecated and it is
     * supported only in Display campaigns.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.common.ProductLabelsInfo product_labels = 18;</code>
     * @return \Google\Ads\GoogleAds\V17\Common\ProductLabelsInfo|null
     */
    public function getProductLabels()
    {
        return $this->readOneof(18);
    }

    public function hasProductLabels()
    {
        return $this->hasOneof(18);
    }

    /**
     * Labels of a product offer. This listing dimension is deprecated and it is
     * supported only in Display campaigns.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.common.ProductLabelsInfo product_labels = 18;</code>
     * @param \Google\Ads\GoogleAds\V17\Common\ProductLabelsInfo $var
     * @return $this
     */
    public function setProductLabels($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Common\ProductLabelsInfo::class);
        $this->writeOneof(18, $var);

        return $this;
    }

    /**
     * Legacy condition of a product offer. This listing dimension is deprecated
     * and it is supported only in Display campaigns.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.common.ProductLegacyConditionInfo product_legacy_condition = 19;</code>
     * @return \Google\Ads\GoogleAds\V17\Common\ProductLegacyConditionInfo|null
     */
    public function getProductLegacyCondition()
    {
        return $this->readOneof(19);
    }

    public function hasProductLegacyCondition()
    {
        return $this->hasOneof(19);
    }

    /**
     * Legacy condition of a product offer. This listing dimension is deprecated
     * and it is supported only in Display campaigns.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.common.ProductLegacyConditionInfo product_legacy_condition = 19;</code>
     * @param \Google\Ads\GoogleAds\V17\Common\ProductLegacyConditionInfo $var
     * @return $this
     */
    public function setProductLegacyCondition($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Common\ProductLegacyConditionInfo::class);
        $this->writeOneof(19, $var);

        return $this;
    }

    /**
     * Full type of a product offer. This listing dimension is deprecated and it
     * is supported only in Display campaigns.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.common.ProductTypeFullInfo product_type_full = 20;</code>
     * @return \Google\Ads\GoogleAds\V17\Common\ProductTypeFullInfo|null
     */
    public function getProductTypeFull()
    {
        return $this->readOneof(20);
    }

    public function hasProductTypeFull()
    {
        return $this->hasOneof(20);
    }

    /**
     * Full type of a product offer. This listing dimension is deprecated and it
     * is supported only in Display campaigns.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.common.ProductTypeFullInfo product_type_full = 20;</code>
     * @param \Google\Ads\GoogleAds\V17\Common\ProductTypeFullInfo $var
     * @return $this
     */
    public function setProductTypeFull($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Common\ProductTypeFullInfo::class);
        $this->writeOneof(20, $var);

        return $this;
    }

    /**
     * Advertiser-specific activity ID.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.common.ActivityIdInfo activity_id = 21;</code>
     * @return \Google\Ads\GoogleAds\V17\Common\ActivityIdInfo|null
     */
    public function getActivityId()
    {
        return $this->readOneof(21);
    }

    public function hasActivityId()
    {
        return $this->hasOneof(21);
    }

    /**
     * Advertiser-specific activity ID.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.common.ActivityIdInfo activity_id = 21;</code>
     * @param \Google\Ads\GoogleAds\V17\Common\ActivityIdInfo $var
     * @return $this
     */
    public function setActivityId($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Common\ActivityIdInfo::class);
        $this->writeOneof(21, $var);

        return $this;
    }

    /**
     * Rating of the activity as a number 1 to 5, where 5 is the best.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.common.ActivityRatingInfo activity_rating = 22;</code>
     * @return \Google\Ads\GoogleAds\V17\Common\ActivityRatingInfo|null
     */
    public function getActivityRating()
    {
        return $this->readOneof(22);
    }

    public function hasActivityRating()
    {
        return $this->hasOneof(22);
    }

    /**
     * Rating of the activity as a number 1 to 5, where 5 is the best.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.common.ActivityRatingInfo activity_rating = 22;</code>
     * @param \Google\Ads\GoogleAds\V17\Common\ActivityRatingInfo $var
     * @return $this
     */
    public function setActivityRating($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Common\ActivityRatingInfo::class);
        $this->writeOneof(22, $var);

        return $this;
    }

    /**
     * The country where the travel activity is available.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.common.ActivityCountryInfo activity_country = 23;</code>
     * @return \Google\Ads\GoogleAds\V17\Common\ActivityCountryInfo|null
     */
    public function getActivityCountry()
    {
        return $this->readOneof(23);
    }

    public function hasActivityCountry()
    {
        return $this->hasOneof(23);
    }

    /**
     * The country where the travel activity is available.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.common.ActivityCountryInfo activity_country = 23;</code>
     * @param \Google\Ads\GoogleAds\V17\Common\ActivityCountryInfo $var
     * @return $this
     */
    public function setActivityCountry($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Common\ActivityCountryInfo::class);
        $this->writeOneof(23, $var);

        return $this;
    }

    /**
     * The state where the travel activity is available.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.common.ActivityStateInfo activity_state = 25;</code>
     * @return \Google\Ads\GoogleAds\V17\Common\ActivityStateInfo|null
     */
    public function getActivityState()
    {
        return $this->readOneof(25);
    }

    public function hasActivityState()
    {
        return $this->hasOneof(25);
    }

    /**
     * The state where the travel activity is available.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.common.ActivityStateInfo activity_state = 25;</code>
     * @param \Google\Ads\GoogleAds\V17\Common\ActivityStateInfo $var
     * @return $this
     */
    public function setActivityState($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Common\ActivityStateInfo::class);
        $this->writeOneof(25, $var);

        return $this;
    }

    /**
     * The city where the travel activity is available.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.common.ActivityCityInfo activity_city = 26;</code>
     * @return \Google\Ads\GoogleAds\V17\Common\ActivityCityInfo|null
     */
    public function getActivityCity()
    {
        return $this->readOneof(26);
    }

    public function hasActivityCity()
    {
        return $this->hasOneof(26);
    }

    /**
     * The city where the travel activity is available.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.common.ActivityCityInfo activity_city = 26;</code>
     * @param \Google\Ads\GoogleAds\V17\Common\ActivityCityInfo $var
     * @return $this
     */
    public function setActivityCity($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Common\ActivityCityInfo::class);
        $this->writeOneof(26, $var);

        return $this;
    }

    /**
     * Unknown dimension. Set when no other listing dimension is set.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.common.UnknownListingDimensionInfo unknown_listing_dimension = 14;</code>
     * @return \Google\Ads\GoogleAds\V17\Common\UnknownListingDimensionInfo|null
     */
    public function getUnknownListingDimension()
    {
        return $this->readOneof(14);
    }

    public function hasUnknownListingDimension()
    {
        return $this->hasOneof(14);
    }

    /**
     * Unknown dimension. Set when no other listing dimension is set.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.common.UnknownListingDimensionInfo unknown_listing_dimension = 14;</code>
     * @param \Google\Ads\GoogleAds\V17\Common\UnknownListingDimensionInfo $var
     * @return $this
     */
    public function setUnknownListingDimension($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Common\UnknownListingDimensionInfo::class);
        $this->writeOneof(14, $var);

        return $this;
    }

    /**
     * @return string
     */
    public function getDimension()
    {
        return $this->whichOneof("dimension");
    }

}

