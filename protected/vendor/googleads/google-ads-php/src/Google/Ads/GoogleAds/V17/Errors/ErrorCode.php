<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v17/errors/errors.proto

namespace Google\Ads\GoogleAds\V17\Errors;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * The error reason represented by type and enum.
 *
 * Generated from protobuf message <code>google.ads.googleads.v17.errors.ErrorCode</code>
 */
class ErrorCode extends \Google\Protobuf\Internal\Message
{
    protected $error_code;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int $request_error
     *           An error caused by the request
     *     @type int $bidding_strategy_error
     *           An error with a Bidding Strategy mutate.
     *     @type int $url_field_error
     *           An error with a URL field mutate.
     *     @type int $list_operation_error
     *           An error with a list operation.
     *     @type int $query_error
     *           An error with an AWQL query
     *     @type int $mutate_error
     *           An error with a mutate
     *     @type int $field_mask_error
     *           An error with a field mask
     *     @type int $authorization_error
     *           An error encountered when trying to authorize a user.
     *     @type int $internal_error
     *           An unexpected server-side error.
     *     @type int $quota_error
     *           An error with the amount of quota remaining.
     *     @type int $ad_error
     *           An error with an Ad Group Ad mutate.
     *     @type int $ad_group_error
     *           An error with an Ad Group mutate.
     *     @type int $campaign_budget_error
     *           An error with a Campaign Budget mutate.
     *     @type int $campaign_error
     *           An error with a Campaign mutate.
     *     @type int $video_campaign_error
     *           An error with a Video Campaign mutate.
     *     @type int $authentication_error
     *           Indicates failure to properly authenticate user.
     *     @type int $ad_group_criterion_customizer_error
     *           The reasons for the ad group criterion customizer error.
     *     @type int $ad_group_criterion_error
     *           Indicates failure to properly authenticate user.
     *     @type int $ad_group_customizer_error
     *           The reasons for the ad group customizer error.
     *     @type int $ad_customizer_error
     *           The reasons for the ad customizer error
     *     @type int $ad_group_ad_error
     *           The reasons for the ad group ad error
     *     @type int $ad_sharing_error
     *           The reasons for the ad sharing error
     *     @type int $adx_error
     *           The reasons for the adx error
     *     @type int $asset_error
     *           The reasons for the asset error
     *     @type int $asset_group_asset_error
     *           The reasons for the asset group asset error
     *     @type int $asset_group_listing_group_filter_error
     *           The reasons for the asset group listing group filter error
     *     @type int $asset_group_error
     *           The reasons for the asset group error
     *     @type int $asset_set_asset_error
     *           The reasons for the asset set asset error
     *     @type int $asset_set_link_error
     *           The reasons for the asset set link error
     *     @type int $asset_set_error
     *           The reasons for the asset set error
     *     @type int $bidding_error
     *           The reasons for the bidding errors
     *     @type int $campaign_criterion_error
     *           The reasons for the campaign criterion error
     *     @type int $campaign_conversion_goal_error
     *           The reasons for the campaign conversion goal error
     *     @type int $campaign_customizer_error
     *           The reasons for the campaign customizer error.
     *     @type int $collection_size_error
     *           The reasons for the collection size error
     *     @type int $conversion_goal_campaign_config_error
     *           The reasons for the conversion goal campaign config error
     *     @type int $country_code_error
     *           The reasons for the country code error
     *     @type int $criterion_error
     *           The reasons for the criterion error
     *     @type int $custom_conversion_goal_error
     *           The reasons for the custom conversion goal error
     *     @type int $customer_customizer_error
     *           The reasons for the customer customizer error.
     *     @type int $customer_error
     *           The reasons for the customer error
     *     @type int $customizer_attribute_error
     *           The reasons for the customizer attribute error.
     *     @type int $date_error
     *           The reasons for the date error
     *     @type int $date_range_error
     *           The reasons for the date range error
     *     @type int $distinct_error
     *           The reasons for the distinct error
     *     @type int $feed_attribute_reference_error
     *           The reasons for the feed attribute reference error
     *     @type int $function_error
     *           The reasons for the function error
     *     @type int $function_parsing_error
     *           The reasons for the function parsing error
     *     @type int $id_error
     *           The reasons for the id error
     *     @type int $image_error
     *           The reasons for the image error
     *     @type int $language_code_error
     *           The reasons for the language code error
     *     @type int $media_bundle_error
     *           The reasons for the media bundle error
     *     @type int $media_upload_error
     *           The reasons for media uploading errors.
     *     @type int $media_file_error
     *           The reasons for the media file error
     *     @type int $merchant_center_error
     *           Container for enum describing possible merchant center errors.
     *     @type int $multiplier_error
     *           The reasons for the multiplier error
     *     @type int $new_resource_creation_error
     *           The reasons for the new resource creation error
     *     @type int $not_empty_error
     *           The reasons for the not empty error
     *     @type int $null_error
     *           The reasons for the null error
     *     @type int $operator_error
     *           The reasons for the operator error
     *     @type int $range_error
     *           The reasons for the range error
     *     @type int $recommendation_error
     *           The reasons for error in applying a recommendation
     *     @type int $recommendation_subscription_error
     *           The reasons for the recommendation subscription error.
     *     @type int $region_code_error
     *           The reasons for the region code error
     *     @type int $setting_error
     *           The reasons for the setting error
     *     @type int $string_format_error
     *           The reasons for the string format error
     *     @type int $string_length_error
     *           The reasons for the string length error
     *     @type int $operation_access_denied_error
     *           The reasons for the operation access denied error
     *     @type int $resource_access_denied_error
     *           The reasons for the resource access denied error
     *     @type int $resource_count_limit_exceeded_error
     *           The reasons for the resource count limit exceeded error
     *     @type int $youtube_video_registration_error
     *           The reasons for YouTube video registration errors.
     *     @type int $ad_group_bid_modifier_error
     *           The reasons for the ad group bid modifier error
     *     @type int $context_error
     *           The reasons for the context error
     *     @type int $field_error
     *           The reasons for the field error
     *     @type int $shared_set_error
     *           The reasons for the shared set error
     *     @type int $shared_criterion_error
     *           The reasons for the shared criterion error
     *     @type int $campaign_shared_set_error
     *           The reasons for the campaign shared set error
     *     @type int $conversion_action_error
     *           The reasons for the conversion action error
     *     @type int $conversion_adjustment_upload_error
     *           The reasons for the conversion adjustment upload error
     *     @type int $conversion_custom_variable_error
     *           The reasons for the conversion custom variable error
     *     @type int $conversion_upload_error
     *           The reasons for the conversion upload error
     *     @type int $conversion_value_rule_error
     *           The reasons for the conversion value rule error
     *     @type int $conversion_value_rule_set_error
     *           The reasons for the conversion value rule set error
     *     @type int $header_error
     *           The reasons for the header error.
     *     @type int $database_error
     *           The reasons for the database error.
     *     @type int $policy_finding_error
     *           The reasons for the policy finding error.
     *     @type int $enum_error
     *           The reason for enum error.
     *     @type int $keyword_plan_error
     *           The reason for keyword plan error.
     *     @type int $keyword_plan_campaign_error
     *           The reason for keyword plan campaign error.
     *     @type int $keyword_plan_campaign_keyword_error
     *           The reason for keyword plan campaign keyword error.
     *     @type int $keyword_plan_ad_group_error
     *           The reason for keyword plan ad group error.
     *     @type int $keyword_plan_ad_group_keyword_error
     *           The reason for keyword plan ad group keyword error.
     *     @type int $keyword_plan_idea_error
     *           The reason for keyword idea error.
     *     @type int $account_budget_proposal_error
     *           The reasons for account budget proposal errors.
     *     @type int $user_list_error
     *           The reasons for the user list error
     *     @type int $change_event_error
     *           The reasons for the change event error
     *     @type int $change_status_error
     *           The reasons for the change status error
     *     @type int $feed_error
     *           The reasons for the feed error
     *     @type int $geo_target_constant_suggestion_error
     *           The reasons for the geo target constant suggestion error.
     *     @type int $campaign_draft_error
     *           The reasons for the campaign draft error
     *     @type int $feed_item_error
     *           The reasons for the feed item error
     *     @type int $label_error
     *           The reason for the label error.
     *     @type int $billing_setup_error
     *           The reasons for the billing setup error
     *     @type int $customer_client_link_error
     *           The reasons for the customer client link error
     *     @type int $customer_manager_link_error
     *           The reasons for the customer manager link error
     *     @type int $feed_mapping_error
     *           The reasons for the feed mapping error
     *     @type int $customer_feed_error
     *           The reasons for the customer feed error
     *     @type int $ad_group_feed_error
     *           The reasons for the ad group feed error
     *     @type int $campaign_feed_error
     *           The reasons for the campaign feed error
     *     @type int $custom_interest_error
     *           The reasons for the custom interest error
     *     @type int $campaign_experiment_error
     *           The reasons for the campaign experiment error
     *     @type int $extension_feed_item_error
     *           The reasons for the extension feed item error
     *     @type int $ad_parameter_error
     *           The reasons for the ad parameter error
     *     @type int $feed_item_validation_error
     *           The reasons for the feed item validation error
     *     @type int $extension_setting_error
     *           The reasons for the extension setting error
     *     @type int $feed_item_set_error
     *           The reasons for the feed item set error
     *     @type int $feed_item_set_link_error
     *           The reasons for the feed item set link error
     *     @type int $feed_item_target_error
     *           The reasons for the feed item target error
     *     @type int $policy_violation_error
     *           The reasons for the policy violation error
     *     @type int $partial_failure_error
     *           The reasons for the mutate job error
     *     @type int $policy_validation_parameter_error
     *           The reasons for the policy validation parameter error
     *     @type int $size_limit_error
     *           The reasons for the size limit error
     *     @type int $offline_user_data_job_error
     *           The reasons for the offline user data job error.
     *     @type int $not_allowlisted_error
     *           The reasons for the not allowlisted error
     *     @type int $manager_link_error
     *           The reasons for the manager link error
     *     @type int $currency_code_error
     *           The reasons for the currency code error
     *     @type int $experiment_error
     *           The reasons for the experiment error
     *     @type int $access_invitation_error
     *           The reasons for the access invitation error
     *     @type int $reach_plan_error
     *           The reasons for the reach plan error
     *     @type int $invoice_error
     *           The reasons for the invoice error
     *     @type int $payments_account_error
     *           The reasons for errors in payments accounts service
     *     @type int $time_zone_error
     *           The reasons for the time zone error
     *     @type int $asset_link_error
     *           The reasons for the asset link error
     *     @type int $user_data_error
     *           The reasons for the user data error.
     *     @type int $batch_job_error
     *           The reasons for the batch job error
     *     @type int $account_link_error
     *           The reasons for the account link status change error
     *     @type int $third_party_app_analytics_link_error
     *           The reasons for the third party app analytics link mutate error
     *     @type int $customer_user_access_error
     *           The reasons for the customer user access mutate error
     *     @type int $custom_audience_error
     *           The reasons for the custom audience error
     *     @type int $audience_error
     *           The reasons for the audience error
     *     @type int $search_term_insight_error
     *           The reasons for the Search term insight error
     *     @type int $smart_campaign_error
     *           The reasons for the Smart campaign error
     *     @type int $experiment_arm_error
     *           The reasons for the experiment arm error
     *     @type int $audience_insights_error
     *           The reasons for the Audience Insights error
     *     @type int $product_link_error
     *           The reasons for the product link error
     *     @type int $customer_sk_ad_network_conversion_value_schema_error
     *           The reasons for the customer SK Ad network conversion value schema error
     *     @type int $currency_error
     *           The reasons for the currency errors.
     *     @type int $asset_group_signal_error
     *           The reasons for the asset group hint error
     *     @type int $product_link_invitation_error
     *           The reasons for the product link invitation error
     *     @type int $customer_lifecycle_goal_error
     *           The reasons for the customer lifecycle goal error
     *     @type int $campaign_lifecycle_goal_error
     *           The reasons for the campaign lifecycle goal error
     *     @type int $identity_verification_error
     *           The reasons for an identity verification error.
     *     @type int $user_list_customer_type_error
     *           The reasons for a user list customer type error.
     *     @type int $shopping_product_error
     *           The reasons for error in querying shopping product.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Ads\GoogleAds\V17\Errors\Errors::initOnce();
        parent::__construct($data);
    }

    /**
     * An error caused by the request
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.RequestErrorEnum.RequestError request_error = 1;</code>
     * @return int
     */
    public function getRequestError()
    {
        return $this->readOneof(1);
    }

    public function hasRequestError()
    {
        return $this->hasOneof(1);
    }

    /**
     * An error caused by the request
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.RequestErrorEnum.RequestError request_error = 1;</code>
     * @param int $var
     * @return $this
     */
    public function setRequestError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\RequestErrorEnum\RequestError::class);
        $this->writeOneof(1, $var);

        return $this;
    }

    /**
     * An error with a Bidding Strategy mutate.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.BiddingStrategyErrorEnum.BiddingStrategyError bidding_strategy_error = 2;</code>
     * @return int
     */
    public function getBiddingStrategyError()
    {
        return $this->readOneof(2);
    }

    public function hasBiddingStrategyError()
    {
        return $this->hasOneof(2);
    }

    /**
     * An error with a Bidding Strategy mutate.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.BiddingStrategyErrorEnum.BiddingStrategyError bidding_strategy_error = 2;</code>
     * @param int $var
     * @return $this
     */
    public function setBiddingStrategyError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\BiddingStrategyErrorEnum\BiddingStrategyError::class);
        $this->writeOneof(2, $var);

        return $this;
    }

    /**
     * An error with a URL field mutate.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.UrlFieldErrorEnum.UrlFieldError url_field_error = 3;</code>
     * @return int
     */
    public function getUrlFieldError()
    {
        return $this->readOneof(3);
    }

    public function hasUrlFieldError()
    {
        return $this->hasOneof(3);
    }

    /**
     * An error with a URL field mutate.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.UrlFieldErrorEnum.UrlFieldError url_field_error = 3;</code>
     * @param int $var
     * @return $this
     */
    public function setUrlFieldError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\UrlFieldErrorEnum\UrlFieldError::class);
        $this->writeOneof(3, $var);

        return $this;
    }

    /**
     * An error with a list operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.ListOperationErrorEnum.ListOperationError list_operation_error = 4;</code>
     * @return int
     */
    public function getListOperationError()
    {
        return $this->readOneof(4);
    }

    public function hasListOperationError()
    {
        return $this->hasOneof(4);
    }

    /**
     * An error with a list operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.ListOperationErrorEnum.ListOperationError list_operation_error = 4;</code>
     * @param int $var
     * @return $this
     */
    public function setListOperationError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\ListOperationErrorEnum\ListOperationError::class);
        $this->writeOneof(4, $var);

        return $this;
    }

    /**
     * An error with an AWQL query
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.QueryErrorEnum.QueryError query_error = 5;</code>
     * @return int
     */
    public function getQueryError()
    {
        return $this->readOneof(5);
    }

    public function hasQueryError()
    {
        return $this->hasOneof(5);
    }

    /**
     * An error with an AWQL query
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.QueryErrorEnum.QueryError query_error = 5;</code>
     * @param int $var
     * @return $this
     */
    public function setQueryError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\QueryErrorEnum\QueryError::class);
        $this->writeOneof(5, $var);

        return $this;
    }

    /**
     * An error with a mutate
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.MutateErrorEnum.MutateError mutate_error = 7;</code>
     * @return int
     */
    public function getMutateError()
    {
        return $this->readOneof(7);
    }

    public function hasMutateError()
    {
        return $this->hasOneof(7);
    }

    /**
     * An error with a mutate
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.MutateErrorEnum.MutateError mutate_error = 7;</code>
     * @param int $var
     * @return $this
     */
    public function setMutateError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\MutateErrorEnum\MutateError::class);
        $this->writeOneof(7, $var);

        return $this;
    }

    /**
     * An error with a field mask
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.FieldMaskErrorEnum.FieldMaskError field_mask_error = 8;</code>
     * @return int
     */
    public function getFieldMaskError()
    {
        return $this->readOneof(8);
    }

    public function hasFieldMaskError()
    {
        return $this->hasOneof(8);
    }

    /**
     * An error with a field mask
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.FieldMaskErrorEnum.FieldMaskError field_mask_error = 8;</code>
     * @param int $var
     * @return $this
     */
    public function setFieldMaskError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\FieldMaskErrorEnum\FieldMaskError::class);
        $this->writeOneof(8, $var);

        return $this;
    }

    /**
     * An error encountered when trying to authorize a user.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.AuthorizationErrorEnum.AuthorizationError authorization_error = 9;</code>
     * @return int
     */
    public function getAuthorizationError()
    {
        return $this->readOneof(9);
    }

    public function hasAuthorizationError()
    {
        return $this->hasOneof(9);
    }

    /**
     * An error encountered when trying to authorize a user.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.AuthorizationErrorEnum.AuthorizationError authorization_error = 9;</code>
     * @param int $var
     * @return $this
     */
    public function setAuthorizationError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\AuthorizationErrorEnum\AuthorizationError::class);
        $this->writeOneof(9, $var);

        return $this;
    }

    /**
     * An unexpected server-side error.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.InternalErrorEnum.InternalError internal_error = 10;</code>
     * @return int
     */
    public function getInternalError()
    {
        return $this->readOneof(10);
    }

    public function hasInternalError()
    {
        return $this->hasOneof(10);
    }

    /**
     * An unexpected server-side error.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.InternalErrorEnum.InternalError internal_error = 10;</code>
     * @param int $var
     * @return $this
     */
    public function setInternalError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\InternalErrorEnum\InternalError::class);
        $this->writeOneof(10, $var);

        return $this;
    }

    /**
     * An error with the amount of quota remaining.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.QuotaErrorEnum.QuotaError quota_error = 11;</code>
     * @return int
     */
    public function getQuotaError()
    {
        return $this->readOneof(11);
    }

    public function hasQuotaError()
    {
        return $this->hasOneof(11);
    }

    /**
     * An error with the amount of quota remaining.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.QuotaErrorEnum.QuotaError quota_error = 11;</code>
     * @param int $var
     * @return $this
     */
    public function setQuotaError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\QuotaErrorEnum\QuotaError::class);
        $this->writeOneof(11, $var);

        return $this;
    }

    /**
     * An error with an Ad Group Ad mutate.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.AdErrorEnum.AdError ad_error = 12;</code>
     * @return int
     */
    public function getAdError()
    {
        return $this->readOneof(12);
    }

    public function hasAdError()
    {
        return $this->hasOneof(12);
    }

    /**
     * An error with an Ad Group Ad mutate.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.AdErrorEnum.AdError ad_error = 12;</code>
     * @param int $var
     * @return $this
     */
    public function setAdError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\AdErrorEnum\AdError::class);
        $this->writeOneof(12, $var);

        return $this;
    }

    /**
     * An error with an Ad Group mutate.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.AdGroupErrorEnum.AdGroupError ad_group_error = 13;</code>
     * @return int
     */
    public function getAdGroupError()
    {
        return $this->readOneof(13);
    }

    public function hasAdGroupError()
    {
        return $this->hasOneof(13);
    }

    /**
     * An error with an Ad Group mutate.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.AdGroupErrorEnum.AdGroupError ad_group_error = 13;</code>
     * @param int $var
     * @return $this
     */
    public function setAdGroupError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\AdGroupErrorEnum\AdGroupError::class);
        $this->writeOneof(13, $var);

        return $this;
    }

    /**
     * An error with a Campaign Budget mutate.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.CampaignBudgetErrorEnum.CampaignBudgetError campaign_budget_error = 14;</code>
     * @return int
     */
    public function getCampaignBudgetError()
    {
        return $this->readOneof(14);
    }

    public function hasCampaignBudgetError()
    {
        return $this->hasOneof(14);
    }

    /**
     * An error with a Campaign Budget mutate.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.CampaignBudgetErrorEnum.CampaignBudgetError campaign_budget_error = 14;</code>
     * @param int $var
     * @return $this
     */
    public function setCampaignBudgetError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\CampaignBudgetErrorEnum\CampaignBudgetError::class);
        $this->writeOneof(14, $var);

        return $this;
    }

    /**
     * An error with a Campaign mutate.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.CampaignErrorEnum.CampaignError campaign_error = 15;</code>
     * @return int
     */
    public function getCampaignError()
    {
        return $this->readOneof(15);
    }

    public function hasCampaignError()
    {
        return $this->hasOneof(15);
    }

    /**
     * An error with a Campaign mutate.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.CampaignErrorEnum.CampaignError campaign_error = 15;</code>
     * @param int $var
     * @return $this
     */
    public function setCampaignError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\CampaignErrorEnum\CampaignError::class);
        $this->writeOneof(15, $var);

        return $this;
    }

    /**
     * An error with a Video Campaign mutate.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.VideoCampaignErrorEnum.VideoCampaignError video_campaign_error = 182;</code>
     * @return int
     */
    public function getVideoCampaignError()
    {
        return $this->readOneof(182);
    }

    public function hasVideoCampaignError()
    {
        return $this->hasOneof(182);
    }

    /**
     * An error with a Video Campaign mutate.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.VideoCampaignErrorEnum.VideoCampaignError video_campaign_error = 182;</code>
     * @param int $var
     * @return $this
     */
    public function setVideoCampaignError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\VideoCampaignErrorEnum\VideoCampaignError::class);
        $this->writeOneof(182, $var);

        return $this;
    }

    /**
     * Indicates failure to properly authenticate user.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.AuthenticationErrorEnum.AuthenticationError authentication_error = 17;</code>
     * @return int
     */
    public function getAuthenticationError()
    {
        return $this->readOneof(17);
    }

    public function hasAuthenticationError()
    {
        return $this->hasOneof(17);
    }

    /**
     * Indicates failure to properly authenticate user.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.AuthenticationErrorEnum.AuthenticationError authentication_error = 17;</code>
     * @param int $var
     * @return $this
     */
    public function setAuthenticationError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\AuthenticationErrorEnum\AuthenticationError::class);
        $this->writeOneof(17, $var);

        return $this;
    }

    /**
     * The reasons for the ad group criterion customizer error.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.AdGroupCriterionCustomizerErrorEnum.AdGroupCriterionCustomizerError ad_group_criterion_customizer_error = 161;</code>
     * @return int
     */
    public function getAdGroupCriterionCustomizerError()
    {
        return $this->readOneof(161);
    }

    public function hasAdGroupCriterionCustomizerError()
    {
        return $this->hasOneof(161);
    }

    /**
     * The reasons for the ad group criterion customizer error.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.AdGroupCriterionCustomizerErrorEnum.AdGroupCriterionCustomizerError ad_group_criterion_customizer_error = 161;</code>
     * @param int $var
     * @return $this
     */
    public function setAdGroupCriterionCustomizerError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\AdGroupCriterionCustomizerErrorEnum\AdGroupCriterionCustomizerError::class);
        $this->writeOneof(161, $var);

        return $this;
    }

    /**
     * Indicates failure to properly authenticate user.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.AdGroupCriterionErrorEnum.AdGroupCriterionError ad_group_criterion_error = 18;</code>
     * @return int
     */
    public function getAdGroupCriterionError()
    {
        return $this->readOneof(18);
    }

    public function hasAdGroupCriterionError()
    {
        return $this->hasOneof(18);
    }

    /**
     * Indicates failure to properly authenticate user.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.AdGroupCriterionErrorEnum.AdGroupCriterionError ad_group_criterion_error = 18;</code>
     * @param int $var
     * @return $this
     */
    public function setAdGroupCriterionError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\AdGroupCriterionErrorEnum\AdGroupCriterionError::class);
        $this->writeOneof(18, $var);

        return $this;
    }

    /**
     * The reasons for the ad group customizer error.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.AdGroupCustomizerErrorEnum.AdGroupCustomizerError ad_group_customizer_error = 159;</code>
     * @return int
     */
    public function getAdGroupCustomizerError()
    {
        return $this->readOneof(159);
    }

    public function hasAdGroupCustomizerError()
    {
        return $this->hasOneof(159);
    }

    /**
     * The reasons for the ad group customizer error.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.AdGroupCustomizerErrorEnum.AdGroupCustomizerError ad_group_customizer_error = 159;</code>
     * @param int $var
     * @return $this
     */
    public function setAdGroupCustomizerError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\AdGroupCustomizerErrorEnum\AdGroupCustomizerError::class);
        $this->writeOneof(159, $var);

        return $this;
    }

    /**
     * The reasons for the ad customizer error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.AdCustomizerErrorEnum.AdCustomizerError ad_customizer_error = 19;</code>
     * @return int
     */
    public function getAdCustomizerError()
    {
        return $this->readOneof(19);
    }

    public function hasAdCustomizerError()
    {
        return $this->hasOneof(19);
    }

    /**
     * The reasons for the ad customizer error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.AdCustomizerErrorEnum.AdCustomizerError ad_customizer_error = 19;</code>
     * @param int $var
     * @return $this
     */
    public function setAdCustomizerError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\AdCustomizerErrorEnum\AdCustomizerError::class);
        $this->writeOneof(19, $var);

        return $this;
    }

    /**
     * The reasons for the ad group ad error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.AdGroupAdErrorEnum.AdGroupAdError ad_group_ad_error = 21;</code>
     * @return int
     */
    public function getAdGroupAdError()
    {
        return $this->readOneof(21);
    }

    public function hasAdGroupAdError()
    {
        return $this->hasOneof(21);
    }

    /**
     * The reasons for the ad group ad error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.AdGroupAdErrorEnum.AdGroupAdError ad_group_ad_error = 21;</code>
     * @param int $var
     * @return $this
     */
    public function setAdGroupAdError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\AdGroupAdErrorEnum\AdGroupAdError::class);
        $this->writeOneof(21, $var);

        return $this;
    }

    /**
     * The reasons for the ad sharing error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.AdSharingErrorEnum.AdSharingError ad_sharing_error = 24;</code>
     * @return int
     */
    public function getAdSharingError()
    {
        return $this->readOneof(24);
    }

    public function hasAdSharingError()
    {
        return $this->hasOneof(24);
    }

    /**
     * The reasons for the ad sharing error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.AdSharingErrorEnum.AdSharingError ad_sharing_error = 24;</code>
     * @param int $var
     * @return $this
     */
    public function setAdSharingError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\AdSharingErrorEnum\AdSharingError::class);
        $this->writeOneof(24, $var);

        return $this;
    }

    /**
     * The reasons for the adx error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.AdxErrorEnum.AdxError adx_error = 25;</code>
     * @return int
     */
    public function getAdxError()
    {
        return $this->readOneof(25);
    }

    public function hasAdxError()
    {
        return $this->hasOneof(25);
    }

    /**
     * The reasons for the adx error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.AdxErrorEnum.AdxError adx_error = 25;</code>
     * @param int $var
     * @return $this
     */
    public function setAdxError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\AdxErrorEnum\AdxError::class);
        $this->writeOneof(25, $var);

        return $this;
    }

    /**
     * The reasons for the asset error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.AssetErrorEnum.AssetError asset_error = 107;</code>
     * @return int
     */
    public function getAssetError()
    {
        return $this->readOneof(107);
    }

    public function hasAssetError()
    {
        return $this->hasOneof(107);
    }

    /**
     * The reasons for the asset error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.AssetErrorEnum.AssetError asset_error = 107;</code>
     * @param int $var
     * @return $this
     */
    public function setAssetError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\AssetErrorEnum\AssetError::class);
        $this->writeOneof(107, $var);

        return $this;
    }

    /**
     * The reasons for the asset group asset error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.AssetGroupAssetErrorEnum.AssetGroupAssetError asset_group_asset_error = 149;</code>
     * @return int
     */
    public function getAssetGroupAssetError()
    {
        return $this->readOneof(149);
    }

    public function hasAssetGroupAssetError()
    {
        return $this->hasOneof(149);
    }

    /**
     * The reasons for the asset group asset error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.AssetGroupAssetErrorEnum.AssetGroupAssetError asset_group_asset_error = 149;</code>
     * @param int $var
     * @return $this
     */
    public function setAssetGroupAssetError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\AssetGroupAssetErrorEnum\AssetGroupAssetError::class);
        $this->writeOneof(149, $var);

        return $this;
    }

    /**
     * The reasons for the asset group listing group filter error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.AssetGroupListingGroupFilterErrorEnum.AssetGroupListingGroupFilterError asset_group_listing_group_filter_error = 155;</code>
     * @return int
     */
    public function getAssetGroupListingGroupFilterError()
    {
        return $this->readOneof(155);
    }

    public function hasAssetGroupListingGroupFilterError()
    {
        return $this->hasOneof(155);
    }

    /**
     * The reasons for the asset group listing group filter error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.AssetGroupListingGroupFilterErrorEnum.AssetGroupListingGroupFilterError asset_group_listing_group_filter_error = 155;</code>
     * @param int $var
     * @return $this
     */
    public function setAssetGroupListingGroupFilterError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\AssetGroupListingGroupFilterErrorEnum\AssetGroupListingGroupFilterError::class);
        $this->writeOneof(155, $var);

        return $this;
    }

    /**
     * The reasons for the asset group error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.AssetGroupErrorEnum.AssetGroupError asset_group_error = 148;</code>
     * @return int
     */
    public function getAssetGroupError()
    {
        return $this->readOneof(148);
    }

    public function hasAssetGroupError()
    {
        return $this->hasOneof(148);
    }

    /**
     * The reasons for the asset group error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.AssetGroupErrorEnum.AssetGroupError asset_group_error = 148;</code>
     * @param int $var
     * @return $this
     */
    public function setAssetGroupError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\AssetGroupErrorEnum\AssetGroupError::class);
        $this->writeOneof(148, $var);

        return $this;
    }

    /**
     * The reasons for the asset set asset error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.AssetSetAssetErrorEnum.AssetSetAssetError asset_set_asset_error = 153;</code>
     * @return int
     */
    public function getAssetSetAssetError()
    {
        return $this->readOneof(153);
    }

    public function hasAssetSetAssetError()
    {
        return $this->hasOneof(153);
    }

    /**
     * The reasons for the asset set asset error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.AssetSetAssetErrorEnum.AssetSetAssetError asset_set_asset_error = 153;</code>
     * @param int $var
     * @return $this
     */
    public function setAssetSetAssetError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\AssetSetAssetErrorEnum\AssetSetAssetError::class);
        $this->writeOneof(153, $var);

        return $this;
    }

    /**
     * The reasons for the asset set link error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.AssetSetLinkErrorEnum.AssetSetLinkError asset_set_link_error = 154;</code>
     * @return int
     */
    public function getAssetSetLinkError()
    {
        return $this->readOneof(154);
    }

    public function hasAssetSetLinkError()
    {
        return $this->hasOneof(154);
    }

    /**
     * The reasons for the asset set link error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.AssetSetLinkErrorEnum.AssetSetLinkError asset_set_link_error = 154;</code>
     * @param int $var
     * @return $this
     */
    public function setAssetSetLinkError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\AssetSetLinkErrorEnum\AssetSetLinkError::class);
        $this->writeOneof(154, $var);

        return $this;
    }

    /**
     * The reasons for the asset set error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.AssetSetErrorEnum.AssetSetError asset_set_error = 152;</code>
     * @return int
     */
    public function getAssetSetError()
    {
        return $this->readOneof(152);
    }

    public function hasAssetSetError()
    {
        return $this->hasOneof(152);
    }

    /**
     * The reasons for the asset set error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.AssetSetErrorEnum.AssetSetError asset_set_error = 152;</code>
     * @param int $var
     * @return $this
     */
    public function setAssetSetError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\AssetSetErrorEnum\AssetSetError::class);
        $this->writeOneof(152, $var);

        return $this;
    }

    /**
     * The reasons for the bidding errors
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.BiddingErrorEnum.BiddingError bidding_error = 26;</code>
     * @return int
     */
    public function getBiddingError()
    {
        return $this->readOneof(26);
    }

    public function hasBiddingError()
    {
        return $this->hasOneof(26);
    }

    /**
     * The reasons for the bidding errors
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.BiddingErrorEnum.BiddingError bidding_error = 26;</code>
     * @param int $var
     * @return $this
     */
    public function setBiddingError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\BiddingErrorEnum\BiddingError::class);
        $this->writeOneof(26, $var);

        return $this;
    }

    /**
     * The reasons for the campaign criterion error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.CampaignCriterionErrorEnum.CampaignCriterionError campaign_criterion_error = 29;</code>
     * @return int
     */
    public function getCampaignCriterionError()
    {
        return $this->readOneof(29);
    }

    public function hasCampaignCriterionError()
    {
        return $this->hasOneof(29);
    }

    /**
     * The reasons for the campaign criterion error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.CampaignCriterionErrorEnum.CampaignCriterionError campaign_criterion_error = 29;</code>
     * @param int $var
     * @return $this
     */
    public function setCampaignCriterionError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\CampaignCriterionErrorEnum\CampaignCriterionError::class);
        $this->writeOneof(29, $var);

        return $this;
    }

    /**
     * The reasons for the campaign conversion goal error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.CampaignConversionGoalErrorEnum.CampaignConversionGoalError campaign_conversion_goal_error = 166;</code>
     * @return int
     */
    public function getCampaignConversionGoalError()
    {
        return $this->readOneof(166);
    }

    public function hasCampaignConversionGoalError()
    {
        return $this->hasOneof(166);
    }

    /**
     * The reasons for the campaign conversion goal error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.CampaignConversionGoalErrorEnum.CampaignConversionGoalError campaign_conversion_goal_error = 166;</code>
     * @param int $var
     * @return $this
     */
    public function setCampaignConversionGoalError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\CampaignConversionGoalErrorEnum\CampaignConversionGoalError::class);
        $this->writeOneof(166, $var);

        return $this;
    }

    /**
     * The reasons for the campaign customizer error.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.CampaignCustomizerErrorEnum.CampaignCustomizerError campaign_customizer_error = 160;</code>
     * @return int
     */
    public function getCampaignCustomizerError()
    {
        return $this->readOneof(160);
    }

    public function hasCampaignCustomizerError()
    {
        return $this->hasOneof(160);
    }

    /**
     * The reasons for the campaign customizer error.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.CampaignCustomizerErrorEnum.CampaignCustomizerError campaign_customizer_error = 160;</code>
     * @param int $var
     * @return $this
     */
    public function setCampaignCustomizerError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\CampaignCustomizerErrorEnum\CampaignCustomizerError::class);
        $this->writeOneof(160, $var);

        return $this;
    }

    /**
     * The reasons for the collection size error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.CollectionSizeErrorEnum.CollectionSizeError collection_size_error = 31;</code>
     * @return int
     */
    public function getCollectionSizeError()
    {
        return $this->readOneof(31);
    }

    public function hasCollectionSizeError()
    {
        return $this->hasOneof(31);
    }

    /**
     * The reasons for the collection size error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.CollectionSizeErrorEnum.CollectionSizeError collection_size_error = 31;</code>
     * @param int $var
     * @return $this
     */
    public function setCollectionSizeError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\CollectionSizeErrorEnum\CollectionSizeError::class);
        $this->writeOneof(31, $var);

        return $this;
    }

    /**
     * The reasons for the conversion goal campaign config error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.ConversionGoalCampaignConfigErrorEnum.ConversionGoalCampaignConfigError conversion_goal_campaign_config_error = 165;</code>
     * @return int
     */
    public function getConversionGoalCampaignConfigError()
    {
        return $this->readOneof(165);
    }

    public function hasConversionGoalCampaignConfigError()
    {
        return $this->hasOneof(165);
    }

    /**
     * The reasons for the conversion goal campaign config error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.ConversionGoalCampaignConfigErrorEnum.ConversionGoalCampaignConfigError conversion_goal_campaign_config_error = 165;</code>
     * @param int $var
     * @return $this
     */
    public function setConversionGoalCampaignConfigError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\ConversionGoalCampaignConfigErrorEnum\ConversionGoalCampaignConfigError::class);
        $this->writeOneof(165, $var);

        return $this;
    }

    /**
     * The reasons for the country code error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.CountryCodeErrorEnum.CountryCodeError country_code_error = 109;</code>
     * @return int
     */
    public function getCountryCodeError()
    {
        return $this->readOneof(109);
    }

    public function hasCountryCodeError()
    {
        return $this->hasOneof(109);
    }

    /**
     * The reasons for the country code error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.CountryCodeErrorEnum.CountryCodeError country_code_error = 109;</code>
     * @param int $var
     * @return $this
     */
    public function setCountryCodeError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\CountryCodeErrorEnum\CountryCodeError::class);
        $this->writeOneof(109, $var);

        return $this;
    }

    /**
     * The reasons for the criterion error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.CriterionErrorEnum.CriterionError criterion_error = 32;</code>
     * @return int
     */
    public function getCriterionError()
    {
        return $this->readOneof(32);
    }

    public function hasCriterionError()
    {
        return $this->hasOneof(32);
    }

    /**
     * The reasons for the criterion error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.CriterionErrorEnum.CriterionError criterion_error = 32;</code>
     * @param int $var
     * @return $this
     */
    public function setCriterionError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\CriterionErrorEnum\CriterionError::class);
        $this->writeOneof(32, $var);

        return $this;
    }

    /**
     * The reasons for the custom conversion goal error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.CustomConversionGoalErrorEnum.CustomConversionGoalError custom_conversion_goal_error = 150;</code>
     * @return int
     */
    public function getCustomConversionGoalError()
    {
        return $this->readOneof(150);
    }

    public function hasCustomConversionGoalError()
    {
        return $this->hasOneof(150);
    }

    /**
     * The reasons for the custom conversion goal error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.CustomConversionGoalErrorEnum.CustomConversionGoalError custom_conversion_goal_error = 150;</code>
     * @param int $var
     * @return $this
     */
    public function setCustomConversionGoalError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\CustomConversionGoalErrorEnum\CustomConversionGoalError::class);
        $this->writeOneof(150, $var);

        return $this;
    }

    /**
     * The reasons for the customer customizer error.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.CustomerCustomizerErrorEnum.CustomerCustomizerError customer_customizer_error = 158;</code>
     * @return int
     */
    public function getCustomerCustomizerError()
    {
        return $this->readOneof(158);
    }

    public function hasCustomerCustomizerError()
    {
        return $this->hasOneof(158);
    }

    /**
     * The reasons for the customer customizer error.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.CustomerCustomizerErrorEnum.CustomerCustomizerError customer_customizer_error = 158;</code>
     * @param int $var
     * @return $this
     */
    public function setCustomerCustomizerError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\CustomerCustomizerErrorEnum\CustomerCustomizerError::class);
        $this->writeOneof(158, $var);

        return $this;
    }

    /**
     * The reasons for the customer error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.CustomerErrorEnum.CustomerError customer_error = 90;</code>
     * @return int
     */
    public function getCustomerError()
    {
        return $this->readOneof(90);
    }

    public function hasCustomerError()
    {
        return $this->hasOneof(90);
    }

    /**
     * The reasons for the customer error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.CustomerErrorEnum.CustomerError customer_error = 90;</code>
     * @param int $var
     * @return $this
     */
    public function setCustomerError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\CustomerErrorEnum\CustomerError::class);
        $this->writeOneof(90, $var);

        return $this;
    }

    /**
     * The reasons for the customizer attribute error.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.CustomizerAttributeErrorEnum.CustomizerAttributeError customizer_attribute_error = 151;</code>
     * @return int
     */
    public function getCustomizerAttributeError()
    {
        return $this->readOneof(151);
    }

    public function hasCustomizerAttributeError()
    {
        return $this->hasOneof(151);
    }

    /**
     * The reasons for the customizer attribute error.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.CustomizerAttributeErrorEnum.CustomizerAttributeError customizer_attribute_error = 151;</code>
     * @param int $var
     * @return $this
     */
    public function setCustomizerAttributeError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\CustomizerAttributeErrorEnum\CustomizerAttributeError::class);
        $this->writeOneof(151, $var);

        return $this;
    }

    /**
     * The reasons for the date error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.DateErrorEnum.DateError date_error = 33;</code>
     * @return int
     */
    public function getDateError()
    {
        return $this->readOneof(33);
    }

    public function hasDateError()
    {
        return $this->hasOneof(33);
    }

    /**
     * The reasons for the date error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.DateErrorEnum.DateError date_error = 33;</code>
     * @param int $var
     * @return $this
     */
    public function setDateError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\DateErrorEnum\DateError::class);
        $this->writeOneof(33, $var);

        return $this;
    }

    /**
     * The reasons for the date range error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.DateRangeErrorEnum.DateRangeError date_range_error = 34;</code>
     * @return int
     */
    public function getDateRangeError()
    {
        return $this->readOneof(34);
    }

    public function hasDateRangeError()
    {
        return $this->hasOneof(34);
    }

    /**
     * The reasons for the date range error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.DateRangeErrorEnum.DateRangeError date_range_error = 34;</code>
     * @param int $var
     * @return $this
     */
    public function setDateRangeError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\DateRangeErrorEnum\DateRangeError::class);
        $this->writeOneof(34, $var);

        return $this;
    }

    /**
     * The reasons for the distinct error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.DistinctErrorEnum.DistinctError distinct_error = 35;</code>
     * @return int
     */
    public function getDistinctError()
    {
        return $this->readOneof(35);
    }

    public function hasDistinctError()
    {
        return $this->hasOneof(35);
    }

    /**
     * The reasons for the distinct error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.DistinctErrorEnum.DistinctError distinct_error = 35;</code>
     * @param int $var
     * @return $this
     */
    public function setDistinctError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\DistinctErrorEnum\DistinctError::class);
        $this->writeOneof(35, $var);

        return $this;
    }

    /**
     * The reasons for the feed attribute reference error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.FeedAttributeReferenceErrorEnum.FeedAttributeReferenceError feed_attribute_reference_error = 36;</code>
     * @return int
     */
    public function getFeedAttributeReferenceError()
    {
        return $this->readOneof(36);
    }

    public function hasFeedAttributeReferenceError()
    {
        return $this->hasOneof(36);
    }

    /**
     * The reasons for the feed attribute reference error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.FeedAttributeReferenceErrorEnum.FeedAttributeReferenceError feed_attribute_reference_error = 36;</code>
     * @param int $var
     * @return $this
     */
    public function setFeedAttributeReferenceError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\FeedAttributeReferenceErrorEnum\FeedAttributeReferenceError::class);
        $this->writeOneof(36, $var);

        return $this;
    }

    /**
     * The reasons for the function error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.FunctionErrorEnum.FunctionError function_error = 37;</code>
     * @return int
     */
    public function getFunctionError()
    {
        return $this->readOneof(37);
    }

    public function hasFunctionError()
    {
        return $this->hasOneof(37);
    }

    /**
     * The reasons for the function error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.FunctionErrorEnum.FunctionError function_error = 37;</code>
     * @param int $var
     * @return $this
     */
    public function setFunctionError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\FunctionErrorEnum\FunctionError::class);
        $this->writeOneof(37, $var);

        return $this;
    }

    /**
     * The reasons for the function parsing error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.FunctionParsingErrorEnum.FunctionParsingError function_parsing_error = 38;</code>
     * @return int
     */
    public function getFunctionParsingError()
    {
        return $this->readOneof(38);
    }

    public function hasFunctionParsingError()
    {
        return $this->hasOneof(38);
    }

    /**
     * The reasons for the function parsing error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.FunctionParsingErrorEnum.FunctionParsingError function_parsing_error = 38;</code>
     * @param int $var
     * @return $this
     */
    public function setFunctionParsingError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\FunctionParsingErrorEnum\FunctionParsingError::class);
        $this->writeOneof(38, $var);

        return $this;
    }

    /**
     * The reasons for the id error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.IdErrorEnum.IdError id_error = 39;</code>
     * @return int
     */
    public function getIdError()
    {
        return $this->readOneof(39);
    }

    public function hasIdError()
    {
        return $this->hasOneof(39);
    }

    /**
     * The reasons for the id error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.IdErrorEnum.IdError id_error = 39;</code>
     * @param int $var
     * @return $this
     */
    public function setIdError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\IdErrorEnum\IdError::class);
        $this->writeOneof(39, $var);

        return $this;
    }

    /**
     * The reasons for the image error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.ImageErrorEnum.ImageError image_error = 40;</code>
     * @return int
     */
    public function getImageError()
    {
        return $this->readOneof(40);
    }

    public function hasImageError()
    {
        return $this->hasOneof(40);
    }

    /**
     * The reasons for the image error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.ImageErrorEnum.ImageError image_error = 40;</code>
     * @param int $var
     * @return $this
     */
    public function setImageError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\ImageErrorEnum\ImageError::class);
        $this->writeOneof(40, $var);

        return $this;
    }

    /**
     * The reasons for the language code error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.LanguageCodeErrorEnum.LanguageCodeError language_code_error = 110;</code>
     * @return int
     */
    public function getLanguageCodeError()
    {
        return $this->readOneof(110);
    }

    public function hasLanguageCodeError()
    {
        return $this->hasOneof(110);
    }

    /**
     * The reasons for the language code error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.LanguageCodeErrorEnum.LanguageCodeError language_code_error = 110;</code>
     * @param int $var
     * @return $this
     */
    public function setLanguageCodeError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\LanguageCodeErrorEnum\LanguageCodeError::class);
        $this->writeOneof(110, $var);

        return $this;
    }

    /**
     * The reasons for the media bundle error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.MediaBundleErrorEnum.MediaBundleError media_bundle_error = 42;</code>
     * @return int
     */
    public function getMediaBundleError()
    {
        return $this->readOneof(42);
    }

    public function hasMediaBundleError()
    {
        return $this->hasOneof(42);
    }

    /**
     * The reasons for the media bundle error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.MediaBundleErrorEnum.MediaBundleError media_bundle_error = 42;</code>
     * @param int $var
     * @return $this
     */
    public function setMediaBundleError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\MediaBundleErrorEnum\MediaBundleError::class);
        $this->writeOneof(42, $var);

        return $this;
    }

    /**
     * The reasons for media uploading errors.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.MediaUploadErrorEnum.MediaUploadError media_upload_error = 116;</code>
     * @return int
     */
    public function getMediaUploadError()
    {
        return $this->readOneof(116);
    }

    public function hasMediaUploadError()
    {
        return $this->hasOneof(116);
    }

    /**
     * The reasons for media uploading errors.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.MediaUploadErrorEnum.MediaUploadError media_upload_error = 116;</code>
     * @param int $var
     * @return $this
     */
    public function setMediaUploadError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\MediaUploadErrorEnum\MediaUploadError::class);
        $this->writeOneof(116, $var);

        return $this;
    }

    /**
     * The reasons for the media file error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.MediaFileErrorEnum.MediaFileError media_file_error = 86;</code>
     * @return int
     */
    public function getMediaFileError()
    {
        return $this->readOneof(86);
    }

    public function hasMediaFileError()
    {
        return $this->hasOneof(86);
    }

    /**
     * The reasons for the media file error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.MediaFileErrorEnum.MediaFileError media_file_error = 86;</code>
     * @param int $var
     * @return $this
     */
    public function setMediaFileError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\MediaFileErrorEnum\MediaFileError::class);
        $this->writeOneof(86, $var);

        return $this;
    }

    /**
     * Container for enum describing possible merchant center errors.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.MerchantCenterErrorEnum.MerchantCenterError merchant_center_error = 162;</code>
     * @return int
     */
    public function getMerchantCenterError()
    {
        return $this->readOneof(162);
    }

    public function hasMerchantCenterError()
    {
        return $this->hasOneof(162);
    }

    /**
     * Container for enum describing possible merchant center errors.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.MerchantCenterErrorEnum.MerchantCenterError merchant_center_error = 162;</code>
     * @param int $var
     * @return $this
     */
    public function setMerchantCenterError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\MerchantCenterErrorEnum\MerchantCenterError::class);
        $this->writeOneof(162, $var);

        return $this;
    }

    /**
     * The reasons for the multiplier error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.MultiplierErrorEnum.MultiplierError multiplier_error = 44;</code>
     * @return int
     */
    public function getMultiplierError()
    {
        return $this->readOneof(44);
    }

    public function hasMultiplierError()
    {
        return $this->hasOneof(44);
    }

    /**
     * The reasons for the multiplier error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.MultiplierErrorEnum.MultiplierError multiplier_error = 44;</code>
     * @param int $var
     * @return $this
     */
    public function setMultiplierError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\MultiplierErrorEnum\MultiplierError::class);
        $this->writeOneof(44, $var);

        return $this;
    }

    /**
     * The reasons for the new resource creation error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.NewResourceCreationErrorEnum.NewResourceCreationError new_resource_creation_error = 45;</code>
     * @return int
     */
    public function getNewResourceCreationError()
    {
        return $this->readOneof(45);
    }

    public function hasNewResourceCreationError()
    {
        return $this->hasOneof(45);
    }

    /**
     * The reasons for the new resource creation error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.NewResourceCreationErrorEnum.NewResourceCreationError new_resource_creation_error = 45;</code>
     * @param int $var
     * @return $this
     */
    public function setNewResourceCreationError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\NewResourceCreationErrorEnum\NewResourceCreationError::class);
        $this->writeOneof(45, $var);

        return $this;
    }

    /**
     * The reasons for the not empty error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.NotEmptyErrorEnum.NotEmptyError not_empty_error = 46;</code>
     * @return int
     */
    public function getNotEmptyError()
    {
        return $this->readOneof(46);
    }

    public function hasNotEmptyError()
    {
        return $this->hasOneof(46);
    }

    /**
     * The reasons for the not empty error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.NotEmptyErrorEnum.NotEmptyError not_empty_error = 46;</code>
     * @param int $var
     * @return $this
     */
    public function setNotEmptyError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\NotEmptyErrorEnum\NotEmptyError::class);
        $this->writeOneof(46, $var);

        return $this;
    }

    /**
     * The reasons for the null error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.NullErrorEnum.NullError null_error = 47;</code>
     * @return int
     */
    public function getNullError()
    {
        return $this->readOneof(47);
    }

    public function hasNullError()
    {
        return $this->hasOneof(47);
    }

    /**
     * The reasons for the null error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.NullErrorEnum.NullError null_error = 47;</code>
     * @param int $var
     * @return $this
     */
    public function setNullError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\NullErrorEnum\NullError::class);
        $this->writeOneof(47, $var);

        return $this;
    }

    /**
     * The reasons for the operator error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.OperatorErrorEnum.OperatorError operator_error = 48;</code>
     * @return int
     */
    public function getOperatorError()
    {
        return $this->readOneof(48);
    }

    public function hasOperatorError()
    {
        return $this->hasOneof(48);
    }

    /**
     * The reasons for the operator error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.OperatorErrorEnum.OperatorError operator_error = 48;</code>
     * @param int $var
     * @return $this
     */
    public function setOperatorError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\OperatorErrorEnum\OperatorError::class);
        $this->writeOneof(48, $var);

        return $this;
    }

    /**
     * The reasons for the range error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.RangeErrorEnum.RangeError range_error = 49;</code>
     * @return int
     */
    public function getRangeError()
    {
        return $this->readOneof(49);
    }

    public function hasRangeError()
    {
        return $this->hasOneof(49);
    }

    /**
     * The reasons for the range error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.RangeErrorEnum.RangeError range_error = 49;</code>
     * @param int $var
     * @return $this
     */
    public function setRangeError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\RangeErrorEnum\RangeError::class);
        $this->writeOneof(49, $var);

        return $this;
    }

    /**
     * The reasons for error in applying a recommendation
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.RecommendationErrorEnum.RecommendationError recommendation_error = 58;</code>
     * @return int
     */
    public function getRecommendationError()
    {
        return $this->readOneof(58);
    }

    public function hasRecommendationError()
    {
        return $this->hasOneof(58);
    }

    /**
     * The reasons for error in applying a recommendation
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.RecommendationErrorEnum.RecommendationError recommendation_error = 58;</code>
     * @param int $var
     * @return $this
     */
    public function setRecommendationError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\RecommendationErrorEnum\RecommendationError::class);
        $this->writeOneof(58, $var);

        return $this;
    }

    /**
     * The reasons for the recommendation subscription error.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.RecommendationSubscriptionErrorEnum.RecommendationSubscriptionError recommendation_subscription_error = 180;</code>
     * @return int
     */
    public function getRecommendationSubscriptionError()
    {
        return $this->readOneof(180);
    }

    public function hasRecommendationSubscriptionError()
    {
        return $this->hasOneof(180);
    }

    /**
     * The reasons for the recommendation subscription error.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.RecommendationSubscriptionErrorEnum.RecommendationSubscriptionError recommendation_subscription_error = 180;</code>
     * @param int $var
     * @return $this
     */
    public function setRecommendationSubscriptionError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\RecommendationSubscriptionErrorEnum\RecommendationSubscriptionError::class);
        $this->writeOneof(180, $var);

        return $this;
    }

    /**
     * The reasons for the region code error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.RegionCodeErrorEnum.RegionCodeError region_code_error = 51;</code>
     * @return int
     */
    public function getRegionCodeError()
    {
        return $this->readOneof(51);
    }

    public function hasRegionCodeError()
    {
        return $this->hasOneof(51);
    }

    /**
     * The reasons for the region code error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.RegionCodeErrorEnum.RegionCodeError region_code_error = 51;</code>
     * @param int $var
     * @return $this
     */
    public function setRegionCodeError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\RegionCodeErrorEnum\RegionCodeError::class);
        $this->writeOneof(51, $var);

        return $this;
    }

    /**
     * The reasons for the setting error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.SettingErrorEnum.SettingError setting_error = 52;</code>
     * @return int
     */
    public function getSettingError()
    {
        return $this->readOneof(52);
    }

    public function hasSettingError()
    {
        return $this->hasOneof(52);
    }

    /**
     * The reasons for the setting error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.SettingErrorEnum.SettingError setting_error = 52;</code>
     * @param int $var
     * @return $this
     */
    public function setSettingError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\SettingErrorEnum\SettingError::class);
        $this->writeOneof(52, $var);

        return $this;
    }

    /**
     * The reasons for the string format error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.StringFormatErrorEnum.StringFormatError string_format_error = 53;</code>
     * @return int
     */
    public function getStringFormatError()
    {
        return $this->readOneof(53);
    }

    public function hasStringFormatError()
    {
        return $this->hasOneof(53);
    }

    /**
     * The reasons for the string format error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.StringFormatErrorEnum.StringFormatError string_format_error = 53;</code>
     * @param int $var
     * @return $this
     */
    public function setStringFormatError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\StringFormatErrorEnum\StringFormatError::class);
        $this->writeOneof(53, $var);

        return $this;
    }

    /**
     * The reasons for the string length error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.StringLengthErrorEnum.StringLengthError string_length_error = 54;</code>
     * @return int
     */
    public function getStringLengthError()
    {
        return $this->readOneof(54);
    }

    public function hasStringLengthError()
    {
        return $this->hasOneof(54);
    }

    /**
     * The reasons for the string length error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.StringLengthErrorEnum.StringLengthError string_length_error = 54;</code>
     * @param int $var
     * @return $this
     */
    public function setStringLengthError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\StringLengthErrorEnum\StringLengthError::class);
        $this->writeOneof(54, $var);

        return $this;
    }

    /**
     * The reasons for the operation access denied error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.OperationAccessDeniedErrorEnum.OperationAccessDeniedError operation_access_denied_error = 55;</code>
     * @return int
     */
    public function getOperationAccessDeniedError()
    {
        return $this->readOneof(55);
    }

    public function hasOperationAccessDeniedError()
    {
        return $this->hasOneof(55);
    }

    /**
     * The reasons for the operation access denied error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.OperationAccessDeniedErrorEnum.OperationAccessDeniedError operation_access_denied_error = 55;</code>
     * @param int $var
     * @return $this
     */
    public function setOperationAccessDeniedError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\OperationAccessDeniedErrorEnum\OperationAccessDeniedError::class);
        $this->writeOneof(55, $var);

        return $this;
    }

    /**
     * The reasons for the resource access denied error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.ResourceAccessDeniedErrorEnum.ResourceAccessDeniedError resource_access_denied_error = 56;</code>
     * @return int
     */
    public function getResourceAccessDeniedError()
    {
        return $this->readOneof(56);
    }

    public function hasResourceAccessDeniedError()
    {
        return $this->hasOneof(56);
    }

    /**
     * The reasons for the resource access denied error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.ResourceAccessDeniedErrorEnum.ResourceAccessDeniedError resource_access_denied_error = 56;</code>
     * @param int $var
     * @return $this
     */
    public function setResourceAccessDeniedError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\ResourceAccessDeniedErrorEnum\ResourceAccessDeniedError::class);
        $this->writeOneof(56, $var);

        return $this;
    }

    /**
     * The reasons for the resource count limit exceeded error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.ResourceCountLimitExceededErrorEnum.ResourceCountLimitExceededError resource_count_limit_exceeded_error = 57;</code>
     * @return int
     */
    public function getResourceCountLimitExceededError()
    {
        return $this->readOneof(57);
    }

    public function hasResourceCountLimitExceededError()
    {
        return $this->hasOneof(57);
    }

    /**
     * The reasons for the resource count limit exceeded error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.ResourceCountLimitExceededErrorEnum.ResourceCountLimitExceededError resource_count_limit_exceeded_error = 57;</code>
     * @param int $var
     * @return $this
     */
    public function setResourceCountLimitExceededError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\ResourceCountLimitExceededErrorEnum\ResourceCountLimitExceededError::class);
        $this->writeOneof(57, $var);

        return $this;
    }

    /**
     * The reasons for YouTube video registration errors.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.YoutubeVideoRegistrationErrorEnum.YoutubeVideoRegistrationError youtube_video_registration_error = 117;</code>
     * @return int
     */
    public function getYoutubeVideoRegistrationError()
    {
        return $this->readOneof(117);
    }

    public function hasYoutubeVideoRegistrationError()
    {
        return $this->hasOneof(117);
    }

    /**
     * The reasons for YouTube video registration errors.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.YoutubeVideoRegistrationErrorEnum.YoutubeVideoRegistrationError youtube_video_registration_error = 117;</code>
     * @param int $var
     * @return $this
     */
    public function setYoutubeVideoRegistrationError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\YoutubeVideoRegistrationErrorEnum\YoutubeVideoRegistrationError::class);
        $this->writeOneof(117, $var);

        return $this;
    }

    /**
     * The reasons for the ad group bid modifier error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.AdGroupBidModifierErrorEnum.AdGroupBidModifierError ad_group_bid_modifier_error = 59;</code>
     * @return int
     */
    public function getAdGroupBidModifierError()
    {
        return $this->readOneof(59);
    }

    public function hasAdGroupBidModifierError()
    {
        return $this->hasOneof(59);
    }

    /**
     * The reasons for the ad group bid modifier error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.AdGroupBidModifierErrorEnum.AdGroupBidModifierError ad_group_bid_modifier_error = 59;</code>
     * @param int $var
     * @return $this
     */
    public function setAdGroupBidModifierError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\AdGroupBidModifierErrorEnum\AdGroupBidModifierError::class);
        $this->writeOneof(59, $var);

        return $this;
    }

    /**
     * The reasons for the context error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.ContextErrorEnum.ContextError context_error = 60;</code>
     * @return int
     */
    public function getContextError()
    {
        return $this->readOneof(60);
    }

    public function hasContextError()
    {
        return $this->hasOneof(60);
    }

    /**
     * The reasons for the context error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.ContextErrorEnum.ContextError context_error = 60;</code>
     * @param int $var
     * @return $this
     */
    public function setContextError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\ContextErrorEnum\ContextError::class);
        $this->writeOneof(60, $var);

        return $this;
    }

    /**
     * The reasons for the field error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.FieldErrorEnum.FieldError field_error = 61;</code>
     * @return int
     */
    public function getFieldError()
    {
        return $this->readOneof(61);
    }

    public function hasFieldError()
    {
        return $this->hasOneof(61);
    }

    /**
     * The reasons for the field error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.FieldErrorEnum.FieldError field_error = 61;</code>
     * @param int $var
     * @return $this
     */
    public function setFieldError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\FieldErrorEnum\FieldError::class);
        $this->writeOneof(61, $var);

        return $this;
    }

    /**
     * The reasons for the shared set error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.SharedSetErrorEnum.SharedSetError shared_set_error = 62;</code>
     * @return int
     */
    public function getSharedSetError()
    {
        return $this->readOneof(62);
    }

    public function hasSharedSetError()
    {
        return $this->hasOneof(62);
    }

    /**
     * The reasons for the shared set error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.SharedSetErrorEnum.SharedSetError shared_set_error = 62;</code>
     * @param int $var
     * @return $this
     */
    public function setSharedSetError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\SharedSetErrorEnum\SharedSetError::class);
        $this->writeOneof(62, $var);

        return $this;
    }

    /**
     * The reasons for the shared criterion error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.SharedCriterionErrorEnum.SharedCriterionError shared_criterion_error = 63;</code>
     * @return int
     */
    public function getSharedCriterionError()
    {
        return $this->readOneof(63);
    }

    public function hasSharedCriterionError()
    {
        return $this->hasOneof(63);
    }

    /**
     * The reasons for the shared criterion error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.SharedCriterionErrorEnum.SharedCriterionError shared_criterion_error = 63;</code>
     * @param int $var
     * @return $this
     */
    public function setSharedCriterionError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\SharedCriterionErrorEnum\SharedCriterionError::class);
        $this->writeOneof(63, $var);

        return $this;
    }

    /**
     * The reasons for the campaign shared set error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.CampaignSharedSetErrorEnum.CampaignSharedSetError campaign_shared_set_error = 64;</code>
     * @return int
     */
    public function getCampaignSharedSetError()
    {
        return $this->readOneof(64);
    }

    public function hasCampaignSharedSetError()
    {
        return $this->hasOneof(64);
    }

    /**
     * The reasons for the campaign shared set error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.CampaignSharedSetErrorEnum.CampaignSharedSetError campaign_shared_set_error = 64;</code>
     * @param int $var
     * @return $this
     */
    public function setCampaignSharedSetError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\CampaignSharedSetErrorEnum\CampaignSharedSetError::class);
        $this->writeOneof(64, $var);

        return $this;
    }

    /**
     * The reasons for the conversion action error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.ConversionActionErrorEnum.ConversionActionError conversion_action_error = 65;</code>
     * @return int
     */
    public function getConversionActionError()
    {
        return $this->readOneof(65);
    }

    public function hasConversionActionError()
    {
        return $this->hasOneof(65);
    }

    /**
     * The reasons for the conversion action error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.ConversionActionErrorEnum.ConversionActionError conversion_action_error = 65;</code>
     * @param int $var
     * @return $this
     */
    public function setConversionActionError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\ConversionActionErrorEnum\ConversionActionError::class);
        $this->writeOneof(65, $var);

        return $this;
    }

    /**
     * The reasons for the conversion adjustment upload error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.ConversionAdjustmentUploadErrorEnum.ConversionAdjustmentUploadError conversion_adjustment_upload_error = 115;</code>
     * @return int
     */
    public function getConversionAdjustmentUploadError()
    {
        return $this->readOneof(115);
    }

    public function hasConversionAdjustmentUploadError()
    {
        return $this->hasOneof(115);
    }

    /**
     * The reasons for the conversion adjustment upload error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.ConversionAdjustmentUploadErrorEnum.ConversionAdjustmentUploadError conversion_adjustment_upload_error = 115;</code>
     * @param int $var
     * @return $this
     */
    public function setConversionAdjustmentUploadError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\ConversionAdjustmentUploadErrorEnum\ConversionAdjustmentUploadError::class);
        $this->writeOneof(115, $var);

        return $this;
    }

    /**
     * The reasons for the conversion custom variable error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.ConversionCustomVariableErrorEnum.ConversionCustomVariableError conversion_custom_variable_error = 143;</code>
     * @return int
     */
    public function getConversionCustomVariableError()
    {
        return $this->readOneof(143);
    }

    public function hasConversionCustomVariableError()
    {
        return $this->hasOneof(143);
    }

    /**
     * The reasons for the conversion custom variable error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.ConversionCustomVariableErrorEnum.ConversionCustomVariableError conversion_custom_variable_error = 143;</code>
     * @param int $var
     * @return $this
     */
    public function setConversionCustomVariableError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\ConversionCustomVariableErrorEnum\ConversionCustomVariableError::class);
        $this->writeOneof(143, $var);

        return $this;
    }

    /**
     * The reasons for the conversion upload error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.ConversionUploadErrorEnum.ConversionUploadError conversion_upload_error = 111;</code>
     * @return int
     */
    public function getConversionUploadError()
    {
        return $this->readOneof(111);
    }

    public function hasConversionUploadError()
    {
        return $this->hasOneof(111);
    }

    /**
     * The reasons for the conversion upload error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.ConversionUploadErrorEnum.ConversionUploadError conversion_upload_error = 111;</code>
     * @param int $var
     * @return $this
     */
    public function setConversionUploadError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\ConversionUploadErrorEnum\ConversionUploadError::class);
        $this->writeOneof(111, $var);

        return $this;
    }

    /**
     * The reasons for the conversion value rule error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.ConversionValueRuleErrorEnum.ConversionValueRuleError conversion_value_rule_error = 145;</code>
     * @return int
     */
    public function getConversionValueRuleError()
    {
        return $this->readOneof(145);
    }

    public function hasConversionValueRuleError()
    {
        return $this->hasOneof(145);
    }

    /**
     * The reasons for the conversion value rule error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.ConversionValueRuleErrorEnum.ConversionValueRuleError conversion_value_rule_error = 145;</code>
     * @param int $var
     * @return $this
     */
    public function setConversionValueRuleError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\ConversionValueRuleErrorEnum\ConversionValueRuleError::class);
        $this->writeOneof(145, $var);

        return $this;
    }

    /**
     * The reasons for the conversion value rule set error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.ConversionValueRuleSetErrorEnum.ConversionValueRuleSetError conversion_value_rule_set_error = 146;</code>
     * @return int
     */
    public function getConversionValueRuleSetError()
    {
        return $this->readOneof(146);
    }

    public function hasConversionValueRuleSetError()
    {
        return $this->hasOneof(146);
    }

    /**
     * The reasons for the conversion value rule set error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.ConversionValueRuleSetErrorEnum.ConversionValueRuleSetError conversion_value_rule_set_error = 146;</code>
     * @param int $var
     * @return $this
     */
    public function setConversionValueRuleSetError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\ConversionValueRuleSetErrorEnum\ConversionValueRuleSetError::class);
        $this->writeOneof(146, $var);

        return $this;
    }

    /**
     * The reasons for the header error.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.HeaderErrorEnum.HeaderError header_error = 66;</code>
     * @return int
     */
    public function getHeaderError()
    {
        return $this->readOneof(66);
    }

    public function hasHeaderError()
    {
        return $this->hasOneof(66);
    }

    /**
     * The reasons for the header error.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.HeaderErrorEnum.HeaderError header_error = 66;</code>
     * @param int $var
     * @return $this
     */
    public function setHeaderError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\HeaderErrorEnum\HeaderError::class);
        $this->writeOneof(66, $var);

        return $this;
    }

    /**
     * The reasons for the database error.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.DatabaseErrorEnum.DatabaseError database_error = 67;</code>
     * @return int
     */
    public function getDatabaseError()
    {
        return $this->readOneof(67);
    }

    public function hasDatabaseError()
    {
        return $this->hasOneof(67);
    }

    /**
     * The reasons for the database error.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.DatabaseErrorEnum.DatabaseError database_error = 67;</code>
     * @param int $var
     * @return $this
     */
    public function setDatabaseError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\DatabaseErrorEnum\DatabaseError::class);
        $this->writeOneof(67, $var);

        return $this;
    }

    /**
     * The reasons for the policy finding error.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.PolicyFindingErrorEnum.PolicyFindingError policy_finding_error = 68;</code>
     * @return int
     */
    public function getPolicyFindingError()
    {
        return $this->readOneof(68);
    }

    public function hasPolicyFindingError()
    {
        return $this->hasOneof(68);
    }

    /**
     * The reasons for the policy finding error.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.PolicyFindingErrorEnum.PolicyFindingError policy_finding_error = 68;</code>
     * @param int $var
     * @return $this
     */
    public function setPolicyFindingError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\PolicyFindingErrorEnum\PolicyFindingError::class);
        $this->writeOneof(68, $var);

        return $this;
    }

    /**
     * The reason for enum error.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.EnumErrorEnum.EnumError enum_error = 70;</code>
     * @return int
     */
    public function getEnumError()
    {
        return $this->readOneof(70);
    }

    public function hasEnumError()
    {
        return $this->hasOneof(70);
    }

    /**
     * The reason for enum error.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.EnumErrorEnum.EnumError enum_error = 70;</code>
     * @param int $var
     * @return $this
     */
    public function setEnumError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\EnumErrorEnum\EnumError::class);
        $this->writeOneof(70, $var);

        return $this;
    }

    /**
     * The reason for keyword plan error.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.KeywordPlanErrorEnum.KeywordPlanError keyword_plan_error = 71;</code>
     * @return int
     */
    public function getKeywordPlanError()
    {
        return $this->readOneof(71);
    }

    public function hasKeywordPlanError()
    {
        return $this->hasOneof(71);
    }

    /**
     * The reason for keyword plan error.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.KeywordPlanErrorEnum.KeywordPlanError keyword_plan_error = 71;</code>
     * @param int $var
     * @return $this
     */
    public function setKeywordPlanError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\KeywordPlanErrorEnum\KeywordPlanError::class);
        $this->writeOneof(71, $var);

        return $this;
    }

    /**
     * The reason for keyword plan campaign error.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.KeywordPlanCampaignErrorEnum.KeywordPlanCampaignError keyword_plan_campaign_error = 72;</code>
     * @return int
     */
    public function getKeywordPlanCampaignError()
    {
        return $this->readOneof(72);
    }

    public function hasKeywordPlanCampaignError()
    {
        return $this->hasOneof(72);
    }

    /**
     * The reason for keyword plan campaign error.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.KeywordPlanCampaignErrorEnum.KeywordPlanCampaignError keyword_plan_campaign_error = 72;</code>
     * @param int $var
     * @return $this
     */
    public function setKeywordPlanCampaignError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\KeywordPlanCampaignErrorEnum\KeywordPlanCampaignError::class);
        $this->writeOneof(72, $var);

        return $this;
    }

    /**
     * The reason for keyword plan campaign keyword error.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.KeywordPlanCampaignKeywordErrorEnum.KeywordPlanCampaignKeywordError keyword_plan_campaign_keyword_error = 132;</code>
     * @return int
     */
    public function getKeywordPlanCampaignKeywordError()
    {
        return $this->readOneof(132);
    }

    public function hasKeywordPlanCampaignKeywordError()
    {
        return $this->hasOneof(132);
    }

    /**
     * The reason for keyword plan campaign keyword error.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.KeywordPlanCampaignKeywordErrorEnum.KeywordPlanCampaignKeywordError keyword_plan_campaign_keyword_error = 132;</code>
     * @param int $var
     * @return $this
     */
    public function setKeywordPlanCampaignKeywordError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\KeywordPlanCampaignKeywordErrorEnum\KeywordPlanCampaignKeywordError::class);
        $this->writeOneof(132, $var);

        return $this;
    }

    /**
     * The reason for keyword plan ad group error.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.KeywordPlanAdGroupErrorEnum.KeywordPlanAdGroupError keyword_plan_ad_group_error = 74;</code>
     * @return int
     */
    public function getKeywordPlanAdGroupError()
    {
        return $this->readOneof(74);
    }

    public function hasKeywordPlanAdGroupError()
    {
        return $this->hasOneof(74);
    }

    /**
     * The reason for keyword plan ad group error.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.KeywordPlanAdGroupErrorEnum.KeywordPlanAdGroupError keyword_plan_ad_group_error = 74;</code>
     * @param int $var
     * @return $this
     */
    public function setKeywordPlanAdGroupError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\KeywordPlanAdGroupErrorEnum\KeywordPlanAdGroupError::class);
        $this->writeOneof(74, $var);

        return $this;
    }

    /**
     * The reason for keyword plan ad group keyword error.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.KeywordPlanAdGroupKeywordErrorEnum.KeywordPlanAdGroupKeywordError keyword_plan_ad_group_keyword_error = 133;</code>
     * @return int
     */
    public function getKeywordPlanAdGroupKeywordError()
    {
        return $this->readOneof(133);
    }

    public function hasKeywordPlanAdGroupKeywordError()
    {
        return $this->hasOneof(133);
    }

    /**
     * The reason for keyword plan ad group keyword error.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.KeywordPlanAdGroupKeywordErrorEnum.KeywordPlanAdGroupKeywordError keyword_plan_ad_group_keyword_error = 133;</code>
     * @param int $var
     * @return $this
     */
    public function setKeywordPlanAdGroupKeywordError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\KeywordPlanAdGroupKeywordErrorEnum\KeywordPlanAdGroupKeywordError::class);
        $this->writeOneof(133, $var);

        return $this;
    }

    /**
     * The reason for keyword idea error.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.KeywordPlanIdeaErrorEnum.KeywordPlanIdeaError keyword_plan_idea_error = 76;</code>
     * @return int
     */
    public function getKeywordPlanIdeaError()
    {
        return $this->readOneof(76);
    }

    public function hasKeywordPlanIdeaError()
    {
        return $this->hasOneof(76);
    }

    /**
     * The reason for keyword idea error.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.KeywordPlanIdeaErrorEnum.KeywordPlanIdeaError keyword_plan_idea_error = 76;</code>
     * @param int $var
     * @return $this
     */
    public function setKeywordPlanIdeaError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\KeywordPlanIdeaErrorEnum\KeywordPlanIdeaError::class);
        $this->writeOneof(76, $var);

        return $this;
    }

    /**
     * The reasons for account budget proposal errors.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.AccountBudgetProposalErrorEnum.AccountBudgetProposalError account_budget_proposal_error = 77;</code>
     * @return int
     */
    public function getAccountBudgetProposalError()
    {
        return $this->readOneof(77);
    }

    public function hasAccountBudgetProposalError()
    {
        return $this->hasOneof(77);
    }

    /**
     * The reasons for account budget proposal errors.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.AccountBudgetProposalErrorEnum.AccountBudgetProposalError account_budget_proposal_error = 77;</code>
     * @param int $var
     * @return $this
     */
    public function setAccountBudgetProposalError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\AccountBudgetProposalErrorEnum\AccountBudgetProposalError::class);
        $this->writeOneof(77, $var);

        return $this;
    }

    /**
     * The reasons for the user list error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.UserListErrorEnum.UserListError user_list_error = 78;</code>
     * @return int
     */
    public function getUserListError()
    {
        return $this->readOneof(78);
    }

    public function hasUserListError()
    {
        return $this->hasOneof(78);
    }

    /**
     * The reasons for the user list error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.UserListErrorEnum.UserListError user_list_error = 78;</code>
     * @param int $var
     * @return $this
     */
    public function setUserListError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\UserListErrorEnum\UserListError::class);
        $this->writeOneof(78, $var);

        return $this;
    }

    /**
     * The reasons for the change event error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.ChangeEventErrorEnum.ChangeEventError change_event_error = 136;</code>
     * @return int
     */
    public function getChangeEventError()
    {
        return $this->readOneof(136);
    }

    public function hasChangeEventError()
    {
        return $this->hasOneof(136);
    }

    /**
     * The reasons for the change event error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.ChangeEventErrorEnum.ChangeEventError change_event_error = 136;</code>
     * @param int $var
     * @return $this
     */
    public function setChangeEventError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\ChangeEventErrorEnum\ChangeEventError::class);
        $this->writeOneof(136, $var);

        return $this;
    }

    /**
     * The reasons for the change status error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.ChangeStatusErrorEnum.ChangeStatusError change_status_error = 79;</code>
     * @return int
     */
    public function getChangeStatusError()
    {
        return $this->readOneof(79);
    }

    public function hasChangeStatusError()
    {
        return $this->hasOneof(79);
    }

    /**
     * The reasons for the change status error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.ChangeStatusErrorEnum.ChangeStatusError change_status_error = 79;</code>
     * @param int $var
     * @return $this
     */
    public function setChangeStatusError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\ChangeStatusErrorEnum\ChangeStatusError::class);
        $this->writeOneof(79, $var);

        return $this;
    }

    /**
     * The reasons for the feed error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.FeedErrorEnum.FeedError feed_error = 80;</code>
     * @return int
     */
    public function getFeedError()
    {
        return $this->readOneof(80);
    }

    public function hasFeedError()
    {
        return $this->hasOneof(80);
    }

    /**
     * The reasons for the feed error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.FeedErrorEnum.FeedError feed_error = 80;</code>
     * @param int $var
     * @return $this
     */
    public function setFeedError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\FeedErrorEnum\FeedError::class);
        $this->writeOneof(80, $var);

        return $this;
    }

    /**
     * The reasons for the geo target constant suggestion error.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.GeoTargetConstantSuggestionErrorEnum.GeoTargetConstantSuggestionError geo_target_constant_suggestion_error = 81;</code>
     * @return int
     */
    public function getGeoTargetConstantSuggestionError()
    {
        return $this->readOneof(81);
    }

    public function hasGeoTargetConstantSuggestionError()
    {
        return $this->hasOneof(81);
    }

    /**
     * The reasons for the geo target constant suggestion error.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.GeoTargetConstantSuggestionErrorEnum.GeoTargetConstantSuggestionError geo_target_constant_suggestion_error = 81;</code>
     * @param int $var
     * @return $this
     */
    public function setGeoTargetConstantSuggestionError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\GeoTargetConstantSuggestionErrorEnum\GeoTargetConstantSuggestionError::class);
        $this->writeOneof(81, $var);

        return $this;
    }

    /**
     * The reasons for the campaign draft error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.CampaignDraftErrorEnum.CampaignDraftError campaign_draft_error = 82;</code>
     * @return int
     */
    public function getCampaignDraftError()
    {
        return $this->readOneof(82);
    }

    public function hasCampaignDraftError()
    {
        return $this->hasOneof(82);
    }

    /**
     * The reasons for the campaign draft error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.CampaignDraftErrorEnum.CampaignDraftError campaign_draft_error = 82;</code>
     * @param int $var
     * @return $this
     */
    public function setCampaignDraftError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\CampaignDraftErrorEnum\CampaignDraftError::class);
        $this->writeOneof(82, $var);

        return $this;
    }

    /**
     * The reasons for the feed item error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.FeedItemErrorEnum.FeedItemError feed_item_error = 83;</code>
     * @return int
     */
    public function getFeedItemError()
    {
        return $this->readOneof(83);
    }

    public function hasFeedItemError()
    {
        return $this->hasOneof(83);
    }

    /**
     * The reasons for the feed item error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.FeedItemErrorEnum.FeedItemError feed_item_error = 83;</code>
     * @param int $var
     * @return $this
     */
    public function setFeedItemError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\FeedItemErrorEnum\FeedItemError::class);
        $this->writeOneof(83, $var);

        return $this;
    }

    /**
     * The reason for the label error.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.LabelErrorEnum.LabelError label_error = 84;</code>
     * @return int
     */
    public function getLabelError()
    {
        return $this->readOneof(84);
    }

    public function hasLabelError()
    {
        return $this->hasOneof(84);
    }

    /**
     * The reason for the label error.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.LabelErrorEnum.LabelError label_error = 84;</code>
     * @param int $var
     * @return $this
     */
    public function setLabelError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\LabelErrorEnum\LabelError::class);
        $this->writeOneof(84, $var);

        return $this;
    }

    /**
     * The reasons for the billing setup error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.BillingSetupErrorEnum.BillingSetupError billing_setup_error = 87;</code>
     * @return int
     */
    public function getBillingSetupError()
    {
        return $this->readOneof(87);
    }

    public function hasBillingSetupError()
    {
        return $this->hasOneof(87);
    }

    /**
     * The reasons for the billing setup error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.BillingSetupErrorEnum.BillingSetupError billing_setup_error = 87;</code>
     * @param int $var
     * @return $this
     */
    public function setBillingSetupError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\BillingSetupErrorEnum\BillingSetupError::class);
        $this->writeOneof(87, $var);

        return $this;
    }

    /**
     * The reasons for the customer client link error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.CustomerClientLinkErrorEnum.CustomerClientLinkError customer_client_link_error = 88;</code>
     * @return int
     */
    public function getCustomerClientLinkError()
    {
        return $this->readOneof(88);
    }

    public function hasCustomerClientLinkError()
    {
        return $this->hasOneof(88);
    }

    /**
     * The reasons for the customer client link error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.CustomerClientLinkErrorEnum.CustomerClientLinkError customer_client_link_error = 88;</code>
     * @param int $var
     * @return $this
     */
    public function setCustomerClientLinkError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\CustomerClientLinkErrorEnum\CustomerClientLinkError::class);
        $this->writeOneof(88, $var);

        return $this;
    }

    /**
     * The reasons for the customer manager link error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.CustomerManagerLinkErrorEnum.CustomerManagerLinkError customer_manager_link_error = 91;</code>
     * @return int
     */
    public function getCustomerManagerLinkError()
    {
        return $this->readOneof(91);
    }

    public function hasCustomerManagerLinkError()
    {
        return $this->hasOneof(91);
    }

    /**
     * The reasons for the customer manager link error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.CustomerManagerLinkErrorEnum.CustomerManagerLinkError customer_manager_link_error = 91;</code>
     * @param int $var
     * @return $this
     */
    public function setCustomerManagerLinkError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\CustomerManagerLinkErrorEnum\CustomerManagerLinkError::class);
        $this->writeOneof(91, $var);

        return $this;
    }

    /**
     * The reasons for the feed mapping error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.FeedMappingErrorEnum.FeedMappingError feed_mapping_error = 92;</code>
     * @return int
     */
    public function getFeedMappingError()
    {
        return $this->readOneof(92);
    }

    public function hasFeedMappingError()
    {
        return $this->hasOneof(92);
    }

    /**
     * The reasons for the feed mapping error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.FeedMappingErrorEnum.FeedMappingError feed_mapping_error = 92;</code>
     * @param int $var
     * @return $this
     */
    public function setFeedMappingError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\FeedMappingErrorEnum\FeedMappingError::class);
        $this->writeOneof(92, $var);

        return $this;
    }

    /**
     * The reasons for the customer feed error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.CustomerFeedErrorEnum.CustomerFeedError customer_feed_error = 93;</code>
     * @return int
     */
    public function getCustomerFeedError()
    {
        return $this->readOneof(93);
    }

    public function hasCustomerFeedError()
    {
        return $this->hasOneof(93);
    }

    /**
     * The reasons for the customer feed error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.CustomerFeedErrorEnum.CustomerFeedError customer_feed_error = 93;</code>
     * @param int $var
     * @return $this
     */
    public function setCustomerFeedError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\CustomerFeedErrorEnum\CustomerFeedError::class);
        $this->writeOneof(93, $var);

        return $this;
    }

    /**
     * The reasons for the ad group feed error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.AdGroupFeedErrorEnum.AdGroupFeedError ad_group_feed_error = 94;</code>
     * @return int
     */
    public function getAdGroupFeedError()
    {
        return $this->readOneof(94);
    }

    public function hasAdGroupFeedError()
    {
        return $this->hasOneof(94);
    }

    /**
     * The reasons for the ad group feed error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.AdGroupFeedErrorEnum.AdGroupFeedError ad_group_feed_error = 94;</code>
     * @param int $var
     * @return $this
     */
    public function setAdGroupFeedError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\AdGroupFeedErrorEnum\AdGroupFeedError::class);
        $this->writeOneof(94, $var);

        return $this;
    }

    /**
     * The reasons for the campaign feed error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.CampaignFeedErrorEnum.CampaignFeedError campaign_feed_error = 96;</code>
     * @return int
     */
    public function getCampaignFeedError()
    {
        return $this->readOneof(96);
    }

    public function hasCampaignFeedError()
    {
        return $this->hasOneof(96);
    }

    /**
     * The reasons for the campaign feed error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.CampaignFeedErrorEnum.CampaignFeedError campaign_feed_error = 96;</code>
     * @param int $var
     * @return $this
     */
    public function setCampaignFeedError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\CampaignFeedErrorEnum\CampaignFeedError::class);
        $this->writeOneof(96, $var);

        return $this;
    }

    /**
     * The reasons for the custom interest error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.CustomInterestErrorEnum.CustomInterestError custom_interest_error = 97;</code>
     * @return int
     */
    public function getCustomInterestError()
    {
        return $this->readOneof(97);
    }

    public function hasCustomInterestError()
    {
        return $this->hasOneof(97);
    }

    /**
     * The reasons for the custom interest error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.CustomInterestErrorEnum.CustomInterestError custom_interest_error = 97;</code>
     * @param int $var
     * @return $this
     */
    public function setCustomInterestError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\CustomInterestErrorEnum\CustomInterestError::class);
        $this->writeOneof(97, $var);

        return $this;
    }

    /**
     * The reasons for the campaign experiment error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.CampaignExperimentErrorEnum.CampaignExperimentError campaign_experiment_error = 98;</code>
     * @return int
     */
    public function getCampaignExperimentError()
    {
        return $this->readOneof(98);
    }

    public function hasCampaignExperimentError()
    {
        return $this->hasOneof(98);
    }

    /**
     * The reasons for the campaign experiment error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.CampaignExperimentErrorEnum.CampaignExperimentError campaign_experiment_error = 98;</code>
     * @param int $var
     * @return $this
     */
    public function setCampaignExperimentError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\CampaignExperimentErrorEnum\CampaignExperimentError::class);
        $this->writeOneof(98, $var);

        return $this;
    }

    /**
     * The reasons for the extension feed item error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.ExtensionFeedItemErrorEnum.ExtensionFeedItemError extension_feed_item_error = 100;</code>
     * @return int
     */
    public function getExtensionFeedItemError()
    {
        return $this->readOneof(100);
    }

    public function hasExtensionFeedItemError()
    {
        return $this->hasOneof(100);
    }

    /**
     * The reasons for the extension feed item error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.ExtensionFeedItemErrorEnum.ExtensionFeedItemError extension_feed_item_error = 100;</code>
     * @param int $var
     * @return $this
     */
    public function setExtensionFeedItemError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\ExtensionFeedItemErrorEnum\ExtensionFeedItemError::class);
        $this->writeOneof(100, $var);

        return $this;
    }

    /**
     * The reasons for the ad parameter error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.AdParameterErrorEnum.AdParameterError ad_parameter_error = 101;</code>
     * @return int
     */
    public function getAdParameterError()
    {
        return $this->readOneof(101);
    }

    public function hasAdParameterError()
    {
        return $this->hasOneof(101);
    }

    /**
     * The reasons for the ad parameter error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.AdParameterErrorEnum.AdParameterError ad_parameter_error = 101;</code>
     * @param int $var
     * @return $this
     */
    public function setAdParameterError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\AdParameterErrorEnum\AdParameterError::class);
        $this->writeOneof(101, $var);

        return $this;
    }

    /**
     * The reasons for the feed item validation error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.FeedItemValidationErrorEnum.FeedItemValidationError feed_item_validation_error = 102;</code>
     * @return int
     */
    public function getFeedItemValidationError()
    {
        return $this->readOneof(102);
    }

    public function hasFeedItemValidationError()
    {
        return $this->hasOneof(102);
    }

    /**
     * The reasons for the feed item validation error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.FeedItemValidationErrorEnum.FeedItemValidationError feed_item_validation_error = 102;</code>
     * @param int $var
     * @return $this
     */
    public function setFeedItemValidationError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\FeedItemValidationErrorEnum\FeedItemValidationError::class);
        $this->writeOneof(102, $var);

        return $this;
    }

    /**
     * The reasons for the extension setting error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.ExtensionSettingErrorEnum.ExtensionSettingError extension_setting_error = 103;</code>
     * @return int
     */
    public function getExtensionSettingError()
    {
        return $this->readOneof(103);
    }

    public function hasExtensionSettingError()
    {
        return $this->hasOneof(103);
    }

    /**
     * The reasons for the extension setting error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.ExtensionSettingErrorEnum.ExtensionSettingError extension_setting_error = 103;</code>
     * @param int $var
     * @return $this
     */
    public function setExtensionSettingError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\ExtensionSettingErrorEnum\ExtensionSettingError::class);
        $this->writeOneof(103, $var);

        return $this;
    }

    /**
     * The reasons for the feed item set error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.FeedItemSetErrorEnum.FeedItemSetError feed_item_set_error = 140;</code>
     * @return int
     */
    public function getFeedItemSetError()
    {
        return $this->readOneof(140);
    }

    public function hasFeedItemSetError()
    {
        return $this->hasOneof(140);
    }

    /**
     * The reasons for the feed item set error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.FeedItemSetErrorEnum.FeedItemSetError feed_item_set_error = 140;</code>
     * @param int $var
     * @return $this
     */
    public function setFeedItemSetError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\FeedItemSetErrorEnum\FeedItemSetError::class);
        $this->writeOneof(140, $var);

        return $this;
    }

    /**
     * The reasons for the feed item set link error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.FeedItemSetLinkErrorEnum.FeedItemSetLinkError feed_item_set_link_error = 141;</code>
     * @return int
     */
    public function getFeedItemSetLinkError()
    {
        return $this->readOneof(141);
    }

    public function hasFeedItemSetLinkError()
    {
        return $this->hasOneof(141);
    }

    /**
     * The reasons for the feed item set link error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.FeedItemSetLinkErrorEnum.FeedItemSetLinkError feed_item_set_link_error = 141;</code>
     * @param int $var
     * @return $this
     */
    public function setFeedItemSetLinkError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\FeedItemSetLinkErrorEnum\FeedItemSetLinkError::class);
        $this->writeOneof(141, $var);

        return $this;
    }

    /**
     * The reasons for the feed item target error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.FeedItemTargetErrorEnum.FeedItemTargetError feed_item_target_error = 104;</code>
     * @return int
     */
    public function getFeedItemTargetError()
    {
        return $this->readOneof(104);
    }

    public function hasFeedItemTargetError()
    {
        return $this->hasOneof(104);
    }

    /**
     * The reasons for the feed item target error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.FeedItemTargetErrorEnum.FeedItemTargetError feed_item_target_error = 104;</code>
     * @param int $var
     * @return $this
     */
    public function setFeedItemTargetError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\FeedItemTargetErrorEnum\FeedItemTargetError::class);
        $this->writeOneof(104, $var);

        return $this;
    }

    /**
     * The reasons for the policy violation error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.PolicyViolationErrorEnum.PolicyViolationError policy_violation_error = 105;</code>
     * @return int
     */
    public function getPolicyViolationError()
    {
        return $this->readOneof(105);
    }

    public function hasPolicyViolationError()
    {
        return $this->hasOneof(105);
    }

    /**
     * The reasons for the policy violation error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.PolicyViolationErrorEnum.PolicyViolationError policy_violation_error = 105;</code>
     * @param int $var
     * @return $this
     */
    public function setPolicyViolationError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\PolicyViolationErrorEnum\PolicyViolationError::class);
        $this->writeOneof(105, $var);

        return $this;
    }

    /**
     * The reasons for the mutate job error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.PartialFailureErrorEnum.PartialFailureError partial_failure_error = 112;</code>
     * @return int
     */
    public function getPartialFailureError()
    {
        return $this->readOneof(112);
    }

    public function hasPartialFailureError()
    {
        return $this->hasOneof(112);
    }

    /**
     * The reasons for the mutate job error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.PartialFailureErrorEnum.PartialFailureError partial_failure_error = 112;</code>
     * @param int $var
     * @return $this
     */
    public function setPartialFailureError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\PartialFailureErrorEnum\PartialFailureError::class);
        $this->writeOneof(112, $var);

        return $this;
    }

    /**
     * The reasons for the policy validation parameter error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.PolicyValidationParameterErrorEnum.PolicyValidationParameterError policy_validation_parameter_error = 114;</code>
     * @return int
     */
    public function getPolicyValidationParameterError()
    {
        return $this->readOneof(114);
    }

    public function hasPolicyValidationParameterError()
    {
        return $this->hasOneof(114);
    }

    /**
     * The reasons for the policy validation parameter error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.PolicyValidationParameterErrorEnum.PolicyValidationParameterError policy_validation_parameter_error = 114;</code>
     * @param int $var
     * @return $this
     */
    public function setPolicyValidationParameterError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\PolicyValidationParameterErrorEnum\PolicyValidationParameterError::class);
        $this->writeOneof(114, $var);

        return $this;
    }

    /**
     * The reasons for the size limit error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.SizeLimitErrorEnum.SizeLimitError size_limit_error = 118;</code>
     * @return int
     */
    public function getSizeLimitError()
    {
        return $this->readOneof(118);
    }

    public function hasSizeLimitError()
    {
        return $this->hasOneof(118);
    }

    /**
     * The reasons for the size limit error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.SizeLimitErrorEnum.SizeLimitError size_limit_error = 118;</code>
     * @param int $var
     * @return $this
     */
    public function setSizeLimitError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\SizeLimitErrorEnum\SizeLimitError::class);
        $this->writeOneof(118, $var);

        return $this;
    }

    /**
     * The reasons for the offline user data job error.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.OfflineUserDataJobErrorEnum.OfflineUserDataJobError offline_user_data_job_error = 119;</code>
     * @return int
     */
    public function getOfflineUserDataJobError()
    {
        return $this->readOneof(119);
    }

    public function hasOfflineUserDataJobError()
    {
        return $this->hasOneof(119);
    }

    /**
     * The reasons for the offline user data job error.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.OfflineUserDataJobErrorEnum.OfflineUserDataJobError offline_user_data_job_error = 119;</code>
     * @param int $var
     * @return $this
     */
    public function setOfflineUserDataJobError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\OfflineUserDataJobErrorEnum\OfflineUserDataJobError::class);
        $this->writeOneof(119, $var);

        return $this;
    }

    /**
     * The reasons for the not allowlisted error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.NotAllowlistedErrorEnum.NotAllowlistedError not_allowlisted_error = 137;</code>
     * @return int
     */
    public function getNotAllowlistedError()
    {
        return $this->readOneof(137);
    }

    public function hasNotAllowlistedError()
    {
        return $this->hasOneof(137);
    }

    /**
     * The reasons for the not allowlisted error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.NotAllowlistedErrorEnum.NotAllowlistedError not_allowlisted_error = 137;</code>
     * @param int $var
     * @return $this
     */
    public function setNotAllowlistedError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\NotAllowlistedErrorEnum\NotAllowlistedError::class);
        $this->writeOneof(137, $var);

        return $this;
    }

    /**
     * The reasons for the manager link error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.ManagerLinkErrorEnum.ManagerLinkError manager_link_error = 121;</code>
     * @return int
     */
    public function getManagerLinkError()
    {
        return $this->readOneof(121);
    }

    public function hasManagerLinkError()
    {
        return $this->hasOneof(121);
    }

    /**
     * The reasons for the manager link error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.ManagerLinkErrorEnum.ManagerLinkError manager_link_error = 121;</code>
     * @param int $var
     * @return $this
     */
    public function setManagerLinkError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\ManagerLinkErrorEnum\ManagerLinkError::class);
        $this->writeOneof(121, $var);

        return $this;
    }

    /**
     * The reasons for the currency code error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.CurrencyCodeErrorEnum.CurrencyCodeError currency_code_error = 122;</code>
     * @return int
     */
    public function getCurrencyCodeError()
    {
        return $this->readOneof(122);
    }

    public function hasCurrencyCodeError()
    {
        return $this->hasOneof(122);
    }

    /**
     * The reasons for the currency code error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.CurrencyCodeErrorEnum.CurrencyCodeError currency_code_error = 122;</code>
     * @param int $var
     * @return $this
     */
    public function setCurrencyCodeError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\CurrencyCodeErrorEnum\CurrencyCodeError::class);
        $this->writeOneof(122, $var);

        return $this;
    }

    /**
     * The reasons for the experiment error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.ExperimentErrorEnum.ExperimentError experiment_error = 123;</code>
     * @return int
     */
    public function getExperimentError()
    {
        return $this->readOneof(123);
    }

    public function hasExperimentError()
    {
        return $this->hasOneof(123);
    }

    /**
     * The reasons for the experiment error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.ExperimentErrorEnum.ExperimentError experiment_error = 123;</code>
     * @param int $var
     * @return $this
     */
    public function setExperimentError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\ExperimentErrorEnum\ExperimentError::class);
        $this->writeOneof(123, $var);

        return $this;
    }

    /**
     * The reasons for the access invitation error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.AccessInvitationErrorEnum.AccessInvitationError access_invitation_error = 124;</code>
     * @return int
     */
    public function getAccessInvitationError()
    {
        return $this->readOneof(124);
    }

    public function hasAccessInvitationError()
    {
        return $this->hasOneof(124);
    }

    /**
     * The reasons for the access invitation error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.AccessInvitationErrorEnum.AccessInvitationError access_invitation_error = 124;</code>
     * @param int $var
     * @return $this
     */
    public function setAccessInvitationError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\AccessInvitationErrorEnum\AccessInvitationError::class);
        $this->writeOneof(124, $var);

        return $this;
    }

    /**
     * The reasons for the reach plan error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.ReachPlanErrorEnum.ReachPlanError reach_plan_error = 125;</code>
     * @return int
     */
    public function getReachPlanError()
    {
        return $this->readOneof(125);
    }

    public function hasReachPlanError()
    {
        return $this->hasOneof(125);
    }

    /**
     * The reasons for the reach plan error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.ReachPlanErrorEnum.ReachPlanError reach_plan_error = 125;</code>
     * @param int $var
     * @return $this
     */
    public function setReachPlanError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\ReachPlanErrorEnum\ReachPlanError::class);
        $this->writeOneof(125, $var);

        return $this;
    }

    /**
     * The reasons for the invoice error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.InvoiceErrorEnum.InvoiceError invoice_error = 126;</code>
     * @return int
     */
    public function getInvoiceError()
    {
        return $this->readOneof(126);
    }

    public function hasInvoiceError()
    {
        return $this->hasOneof(126);
    }

    /**
     * The reasons for the invoice error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.InvoiceErrorEnum.InvoiceError invoice_error = 126;</code>
     * @param int $var
     * @return $this
     */
    public function setInvoiceError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\InvoiceErrorEnum\InvoiceError::class);
        $this->writeOneof(126, $var);

        return $this;
    }

    /**
     * The reasons for errors in payments accounts service
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.PaymentsAccountErrorEnum.PaymentsAccountError payments_account_error = 127;</code>
     * @return int
     */
    public function getPaymentsAccountError()
    {
        return $this->readOneof(127);
    }

    public function hasPaymentsAccountError()
    {
        return $this->hasOneof(127);
    }

    /**
     * The reasons for errors in payments accounts service
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.PaymentsAccountErrorEnum.PaymentsAccountError payments_account_error = 127;</code>
     * @param int $var
     * @return $this
     */
    public function setPaymentsAccountError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\PaymentsAccountErrorEnum\PaymentsAccountError::class);
        $this->writeOneof(127, $var);

        return $this;
    }

    /**
     * The reasons for the time zone error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.TimeZoneErrorEnum.TimeZoneError time_zone_error = 128;</code>
     * @return int
     */
    public function getTimeZoneError()
    {
        return $this->readOneof(128);
    }

    public function hasTimeZoneError()
    {
        return $this->hasOneof(128);
    }

    /**
     * The reasons for the time zone error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.TimeZoneErrorEnum.TimeZoneError time_zone_error = 128;</code>
     * @param int $var
     * @return $this
     */
    public function setTimeZoneError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\TimeZoneErrorEnum\TimeZoneError::class);
        $this->writeOneof(128, $var);

        return $this;
    }

    /**
     * The reasons for the asset link error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.AssetLinkErrorEnum.AssetLinkError asset_link_error = 129;</code>
     * @return int
     */
    public function getAssetLinkError()
    {
        return $this->readOneof(129);
    }

    public function hasAssetLinkError()
    {
        return $this->hasOneof(129);
    }

    /**
     * The reasons for the asset link error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.AssetLinkErrorEnum.AssetLinkError asset_link_error = 129;</code>
     * @param int $var
     * @return $this
     */
    public function setAssetLinkError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\AssetLinkErrorEnum\AssetLinkError::class);
        $this->writeOneof(129, $var);

        return $this;
    }

    /**
     * The reasons for the user data error.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.UserDataErrorEnum.UserDataError user_data_error = 130;</code>
     * @return int
     */
    public function getUserDataError()
    {
        return $this->readOneof(130);
    }

    public function hasUserDataError()
    {
        return $this->hasOneof(130);
    }

    /**
     * The reasons for the user data error.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.UserDataErrorEnum.UserDataError user_data_error = 130;</code>
     * @param int $var
     * @return $this
     */
    public function setUserDataError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\UserDataErrorEnum\UserDataError::class);
        $this->writeOneof(130, $var);

        return $this;
    }

    /**
     * The reasons for the batch job error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.BatchJobErrorEnum.BatchJobError batch_job_error = 131;</code>
     * @return int
     */
    public function getBatchJobError()
    {
        return $this->readOneof(131);
    }

    public function hasBatchJobError()
    {
        return $this->hasOneof(131);
    }

    /**
     * The reasons for the batch job error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.BatchJobErrorEnum.BatchJobError batch_job_error = 131;</code>
     * @param int $var
     * @return $this
     */
    public function setBatchJobError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\BatchJobErrorEnum\BatchJobError::class);
        $this->writeOneof(131, $var);

        return $this;
    }

    /**
     * The reasons for the account link status change error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.AccountLinkErrorEnum.AccountLinkError account_link_error = 134;</code>
     * @return int
     */
    public function getAccountLinkError()
    {
        return $this->readOneof(134);
    }

    public function hasAccountLinkError()
    {
        return $this->hasOneof(134);
    }

    /**
     * The reasons for the account link status change error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.AccountLinkErrorEnum.AccountLinkError account_link_error = 134;</code>
     * @param int $var
     * @return $this
     */
    public function setAccountLinkError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\AccountLinkErrorEnum\AccountLinkError::class);
        $this->writeOneof(134, $var);

        return $this;
    }

    /**
     * The reasons for the third party app analytics link mutate error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.ThirdPartyAppAnalyticsLinkErrorEnum.ThirdPartyAppAnalyticsLinkError third_party_app_analytics_link_error = 135;</code>
     * @return int
     */
    public function getThirdPartyAppAnalyticsLinkError()
    {
        return $this->readOneof(135);
    }

    public function hasThirdPartyAppAnalyticsLinkError()
    {
        return $this->hasOneof(135);
    }

    /**
     * The reasons for the third party app analytics link mutate error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.ThirdPartyAppAnalyticsLinkErrorEnum.ThirdPartyAppAnalyticsLinkError third_party_app_analytics_link_error = 135;</code>
     * @param int $var
     * @return $this
     */
    public function setThirdPartyAppAnalyticsLinkError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\ThirdPartyAppAnalyticsLinkErrorEnum\ThirdPartyAppAnalyticsLinkError::class);
        $this->writeOneof(135, $var);

        return $this;
    }

    /**
     * The reasons for the customer user access mutate error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.CustomerUserAccessErrorEnum.CustomerUserAccessError customer_user_access_error = 138;</code>
     * @return int
     */
    public function getCustomerUserAccessError()
    {
        return $this->readOneof(138);
    }

    public function hasCustomerUserAccessError()
    {
        return $this->hasOneof(138);
    }

    /**
     * The reasons for the customer user access mutate error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.CustomerUserAccessErrorEnum.CustomerUserAccessError customer_user_access_error = 138;</code>
     * @param int $var
     * @return $this
     */
    public function setCustomerUserAccessError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\CustomerUserAccessErrorEnum\CustomerUserAccessError::class);
        $this->writeOneof(138, $var);

        return $this;
    }

    /**
     * The reasons for the custom audience error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.CustomAudienceErrorEnum.CustomAudienceError custom_audience_error = 139;</code>
     * @return int
     */
    public function getCustomAudienceError()
    {
        return $this->readOneof(139);
    }

    public function hasCustomAudienceError()
    {
        return $this->hasOneof(139);
    }

    /**
     * The reasons for the custom audience error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.CustomAudienceErrorEnum.CustomAudienceError custom_audience_error = 139;</code>
     * @param int $var
     * @return $this
     */
    public function setCustomAudienceError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\CustomAudienceErrorEnum\CustomAudienceError::class);
        $this->writeOneof(139, $var);

        return $this;
    }

    /**
     * The reasons for the audience error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.AudienceErrorEnum.AudienceError audience_error = 164;</code>
     * @return int
     */
    public function getAudienceError()
    {
        return $this->readOneof(164);
    }

    public function hasAudienceError()
    {
        return $this->hasOneof(164);
    }

    /**
     * The reasons for the audience error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.AudienceErrorEnum.AudienceError audience_error = 164;</code>
     * @param int $var
     * @return $this
     */
    public function setAudienceError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\AudienceErrorEnum\AudienceError::class);
        $this->writeOneof(164, $var);

        return $this;
    }

    /**
     * The reasons for the Search term insight error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.SearchTermInsightErrorEnum.SearchTermInsightError search_term_insight_error = 174;</code>
     * @return int
     */
    public function getSearchTermInsightError()
    {
        return $this->readOneof(174);
    }

    public function hasSearchTermInsightError()
    {
        return $this->hasOneof(174);
    }

    /**
     * The reasons for the Search term insight error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.SearchTermInsightErrorEnum.SearchTermInsightError search_term_insight_error = 174;</code>
     * @param int $var
     * @return $this
     */
    public function setSearchTermInsightError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\SearchTermInsightErrorEnum\SearchTermInsightError::class);
        $this->writeOneof(174, $var);

        return $this;
    }

    /**
     * The reasons for the Smart campaign error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.SmartCampaignErrorEnum.SmartCampaignError smart_campaign_error = 147;</code>
     * @return int
     */
    public function getSmartCampaignError()
    {
        return $this->readOneof(147);
    }

    public function hasSmartCampaignError()
    {
        return $this->hasOneof(147);
    }

    /**
     * The reasons for the Smart campaign error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.SmartCampaignErrorEnum.SmartCampaignError smart_campaign_error = 147;</code>
     * @param int $var
     * @return $this
     */
    public function setSmartCampaignError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\SmartCampaignErrorEnum\SmartCampaignError::class);
        $this->writeOneof(147, $var);

        return $this;
    }

    /**
     * The reasons for the experiment arm error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.ExperimentArmErrorEnum.ExperimentArmError experiment_arm_error = 156;</code>
     * @return int
     */
    public function getExperimentArmError()
    {
        return $this->readOneof(156);
    }

    public function hasExperimentArmError()
    {
        return $this->hasOneof(156);
    }

    /**
     * The reasons for the experiment arm error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.ExperimentArmErrorEnum.ExperimentArmError experiment_arm_error = 156;</code>
     * @param int $var
     * @return $this
     */
    public function setExperimentArmError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\ExperimentArmErrorEnum\ExperimentArmError::class);
        $this->writeOneof(156, $var);

        return $this;
    }

    /**
     * The reasons for the Audience Insights error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.AudienceInsightsErrorEnum.AudienceInsightsError audience_insights_error = 167;</code>
     * @return int
     */
    public function getAudienceInsightsError()
    {
        return $this->readOneof(167);
    }

    public function hasAudienceInsightsError()
    {
        return $this->hasOneof(167);
    }

    /**
     * The reasons for the Audience Insights error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.AudienceInsightsErrorEnum.AudienceInsightsError audience_insights_error = 167;</code>
     * @param int $var
     * @return $this
     */
    public function setAudienceInsightsError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\AudienceInsightsErrorEnum\AudienceInsightsError::class);
        $this->writeOneof(167, $var);

        return $this;
    }

    /**
     * The reasons for the product link error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.ProductLinkErrorEnum.ProductLinkError product_link_error = 169;</code>
     * @return int
     */
    public function getProductLinkError()
    {
        return $this->readOneof(169);
    }

    public function hasProductLinkError()
    {
        return $this->hasOneof(169);
    }

    /**
     * The reasons for the product link error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.ProductLinkErrorEnum.ProductLinkError product_link_error = 169;</code>
     * @param int $var
     * @return $this
     */
    public function setProductLinkError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\ProductLinkErrorEnum\ProductLinkError::class);
        $this->writeOneof(169, $var);

        return $this;
    }

    /**
     * The reasons for the customer SK Ad network conversion value schema error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.CustomerSkAdNetworkConversionValueSchemaErrorEnum.CustomerSkAdNetworkConversionValueSchemaError customer_sk_ad_network_conversion_value_schema_error = 170;</code>
     * @return int
     */
    public function getCustomerSkAdNetworkConversionValueSchemaError()
    {
        return $this->readOneof(170);
    }

    public function hasCustomerSkAdNetworkConversionValueSchemaError()
    {
        return $this->hasOneof(170);
    }

    /**
     * The reasons for the customer SK Ad network conversion value schema error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.CustomerSkAdNetworkConversionValueSchemaErrorEnum.CustomerSkAdNetworkConversionValueSchemaError customer_sk_ad_network_conversion_value_schema_error = 170;</code>
     * @param int $var
     * @return $this
     */
    public function setCustomerSkAdNetworkConversionValueSchemaError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\CustomerSkAdNetworkConversionValueSchemaErrorEnum\CustomerSkAdNetworkConversionValueSchemaError::class);
        $this->writeOneof(170, $var);

        return $this;
    }

    /**
     * The reasons for the currency errors.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.CurrencyErrorEnum.CurrencyError currency_error = 171;</code>
     * @return int
     */
    public function getCurrencyError()
    {
        return $this->readOneof(171);
    }

    public function hasCurrencyError()
    {
        return $this->hasOneof(171);
    }

    /**
     * The reasons for the currency errors.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.CurrencyErrorEnum.CurrencyError currency_error = 171;</code>
     * @param int $var
     * @return $this
     */
    public function setCurrencyError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\CurrencyErrorEnum\CurrencyError::class);
        $this->writeOneof(171, $var);

        return $this;
    }

    /**
     * The reasons for the asset group hint error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.AssetGroupSignalErrorEnum.AssetGroupSignalError asset_group_signal_error = 176;</code>
     * @return int
     */
    public function getAssetGroupSignalError()
    {
        return $this->readOneof(176);
    }

    public function hasAssetGroupSignalError()
    {
        return $this->hasOneof(176);
    }

    /**
     * The reasons for the asset group hint error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.AssetGroupSignalErrorEnum.AssetGroupSignalError asset_group_signal_error = 176;</code>
     * @param int $var
     * @return $this
     */
    public function setAssetGroupSignalError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\AssetGroupSignalErrorEnum\AssetGroupSignalError::class);
        $this->writeOneof(176, $var);

        return $this;
    }

    /**
     * The reasons for the product link invitation error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.ProductLinkInvitationErrorEnum.ProductLinkInvitationError product_link_invitation_error = 177;</code>
     * @return int
     */
    public function getProductLinkInvitationError()
    {
        return $this->readOneof(177);
    }

    public function hasProductLinkInvitationError()
    {
        return $this->hasOneof(177);
    }

    /**
     * The reasons for the product link invitation error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.ProductLinkInvitationErrorEnum.ProductLinkInvitationError product_link_invitation_error = 177;</code>
     * @param int $var
     * @return $this
     */
    public function setProductLinkInvitationError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\ProductLinkInvitationErrorEnum\ProductLinkInvitationError::class);
        $this->writeOneof(177, $var);

        return $this;
    }

    /**
     * The reasons for the customer lifecycle goal error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.CustomerLifecycleGoalErrorEnum.CustomerLifecycleGoalError customer_lifecycle_goal_error = 178;</code>
     * @return int
     */
    public function getCustomerLifecycleGoalError()
    {
        return $this->readOneof(178);
    }

    public function hasCustomerLifecycleGoalError()
    {
        return $this->hasOneof(178);
    }

    /**
     * The reasons for the customer lifecycle goal error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.CustomerLifecycleGoalErrorEnum.CustomerLifecycleGoalError customer_lifecycle_goal_error = 178;</code>
     * @param int $var
     * @return $this
     */
    public function setCustomerLifecycleGoalError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\CustomerLifecycleGoalErrorEnum\CustomerLifecycleGoalError::class);
        $this->writeOneof(178, $var);

        return $this;
    }

    /**
     * The reasons for the campaign lifecycle goal error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.CampaignLifecycleGoalErrorEnum.CampaignLifecycleGoalError campaign_lifecycle_goal_error = 179;</code>
     * @return int
     */
    public function getCampaignLifecycleGoalError()
    {
        return $this->readOneof(179);
    }

    public function hasCampaignLifecycleGoalError()
    {
        return $this->hasOneof(179);
    }

    /**
     * The reasons for the campaign lifecycle goal error
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.CampaignLifecycleGoalErrorEnum.CampaignLifecycleGoalError campaign_lifecycle_goal_error = 179;</code>
     * @param int $var
     * @return $this
     */
    public function setCampaignLifecycleGoalError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\CampaignLifecycleGoalErrorEnum\CampaignLifecycleGoalError::class);
        $this->writeOneof(179, $var);

        return $this;
    }

    /**
     * The reasons for an identity verification error.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.IdentityVerificationErrorEnum.IdentityVerificationError identity_verification_error = 181;</code>
     * @return int
     */
    public function getIdentityVerificationError()
    {
        return $this->readOneof(181);
    }

    public function hasIdentityVerificationError()
    {
        return $this->hasOneof(181);
    }

    /**
     * The reasons for an identity verification error.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.IdentityVerificationErrorEnum.IdentityVerificationError identity_verification_error = 181;</code>
     * @param int $var
     * @return $this
     */
    public function setIdentityVerificationError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\IdentityVerificationErrorEnum\IdentityVerificationError::class);
        $this->writeOneof(181, $var);

        return $this;
    }

    /**
     * The reasons for a user list customer type error.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.UserListCustomerTypeErrorEnum.UserListCustomerTypeError user_list_customer_type_error = 183;</code>
     * @return int
     */
    public function getUserListCustomerTypeError()
    {
        return $this->readOneof(183);
    }

    public function hasUserListCustomerTypeError()
    {
        return $this->hasOneof(183);
    }

    /**
     * The reasons for a user list customer type error.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.UserListCustomerTypeErrorEnum.UserListCustomerTypeError user_list_customer_type_error = 183;</code>
     * @param int $var
     * @return $this
     */
    public function setUserListCustomerTypeError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\UserListCustomerTypeErrorEnum\UserListCustomerTypeError::class);
        $this->writeOneof(183, $var);

        return $this;
    }

    /**
     * The reasons for error in querying shopping product.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.ShoppingProductErrorEnum.ShoppingProductError shopping_product_error = 184;</code>
     * @return int
     */
    public function getShoppingProductError()
    {
        return $this->readOneof(184);
    }

    public function hasShoppingProductError()
    {
        return $this->hasOneof(184);
    }

    /**
     * The reasons for error in querying shopping product.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.errors.ShoppingProductErrorEnum.ShoppingProductError shopping_product_error = 184;</code>
     * @param int $var
     * @return $this
     */
    public function setShoppingProductError($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V17\Errors\ShoppingProductErrorEnum\ShoppingProductError::class);
        $this->writeOneof(184, $var);

        return $this;
    }

    /**
     * @return string
     */
    public function getErrorCode()
    {
        return $this->whichOneof("error_code");
    }

}

