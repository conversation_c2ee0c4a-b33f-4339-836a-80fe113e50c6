<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v17/services/product_link_service.proto

namespace Google\Ads\GoogleAds\V17\Services;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Request message for
 * [ProductLinkService.CreateProductLink][google.ads.googleads.v17.services.ProductLinkService.CreateProductLink].
 *
 * Generated from protobuf message <code>google.ads.googleads.v17.services.CreateProductLinkRequest</code>
 */
class CreateProductLinkRequest extends \Google\Protobuf\Internal\Message
{
    /**
     * Required. The ID of the customer for which the product link is created.
     *
     * Generated from protobuf field <code>string customer_id = 1 [(.google.api.field_behavior) = REQUIRED];</code>
     */
    protected $customer_id = '';
    /**
     * Required. The product link to be created.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.ProductLink product_link = 2 [(.google.api.field_behavior) = REQUIRED];</code>
     */
    protected $product_link = null;

    /**
     * @param string                                          $customerId  Required. The ID of the customer for which the product link is created.
     * @param \Google\Ads\GoogleAds\V17\Resources\ProductLink $productLink Required. The product link to be created.
     *
     * @return \Google\Ads\GoogleAds\V17\Services\CreateProductLinkRequest
     *
     * @experimental
     */
    public static function build(string $customerId, \Google\Ads\GoogleAds\V17\Resources\ProductLink $productLink): self
    {
        return (new self())
            ->setCustomerId($customerId)
            ->setProductLink($productLink);
    }

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $customer_id
     *           Required. The ID of the customer for which the product link is created.
     *     @type \Google\Ads\GoogleAds\V17\Resources\ProductLink $product_link
     *           Required. The product link to be created.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Ads\GoogleAds\V17\Services\ProductLinkService::initOnce();
        parent::__construct($data);
    }

    /**
     * Required. The ID of the customer for which the product link is created.
     *
     * Generated from protobuf field <code>string customer_id = 1 [(.google.api.field_behavior) = REQUIRED];</code>
     * @return string
     */
    public function getCustomerId()
    {
        return $this->customer_id;
    }

    /**
     * Required. The ID of the customer for which the product link is created.
     *
     * Generated from protobuf field <code>string customer_id = 1 [(.google.api.field_behavior) = REQUIRED];</code>
     * @param string $var
     * @return $this
     */
    public function setCustomerId($var)
    {
        GPBUtil::checkString($var, True);
        $this->customer_id = $var;

        return $this;
    }

    /**
     * Required. The product link to be created.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.ProductLink product_link = 2 [(.google.api.field_behavior) = REQUIRED];</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\ProductLink|null
     */
    public function getProductLink()
    {
        return $this->product_link;
    }

    public function hasProductLink()
    {
        return isset($this->product_link);
    }

    public function clearProductLink()
    {
        unset($this->product_link);
    }

    /**
     * Required. The product link to be created.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.ProductLink product_link = 2 [(.google.api.field_behavior) = REQUIRED];</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\ProductLink $var
     * @return $this
     */
    public function setProductLink($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\ProductLink::class);
        $this->product_link = $var;

        return $this;
    }

}

