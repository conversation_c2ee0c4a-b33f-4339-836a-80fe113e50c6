<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v16/enums/target_impression_share_location.proto

namespace Google\Ads\GoogleAds\V16\Enums;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Container for enum describing where on the first search results page the
 * automated bidding system should target impressions for the
 * TargetImpressionShare bidding strategy.
 *
 * Generated from protobuf message <code>google.ads.googleads.v16.enums.TargetImpressionShareLocationEnum</code>
 */
class TargetImpressionShareLocationEnum extends \Google\Protobuf\Internal\Message
{

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Ads\GoogleAds\V16\Enums\TargetImpressionShareLocation::initOnce();
        parent::__construct($data);
    }

}

