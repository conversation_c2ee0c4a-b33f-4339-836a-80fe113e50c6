<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v15/common/metrics.proto

namespace Google\Ads\GoogleAds\V15\Common;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Metrics data.
 *
 * Generated from protobuf message <code>google.ads.googleads.v15.common.Metrics</code>
 */
class Metrics extends \Google\Protobuf\Internal\Message
{
    /**
     * The percent of your ad impressions that are shown as the very first ad
     * above the organic search results.
     *
     * Generated from protobuf field <code>optional double absolute_top_impression_percentage = 183;</code>
     */
    protected $absolute_top_impression_percentage = null;
    /**
     * Average cost of viewable impressions (`active_view_impressions`).
     *
     * Generated from protobuf field <code>optional double active_view_cpm = 184;</code>
     */
    protected $active_view_cpm = null;
    /**
     * Active view measurable clicks divided by active view viewable impressions.
     * This metric is reported only for the Display Network.
     *
     * Generated from protobuf field <code>optional double active_view_ctr = 185;</code>
     */
    protected $active_view_ctr = null;
    /**
     * A measurement of how often your ad has become viewable on a Display
     * Network site.
     *
     * Generated from protobuf field <code>optional int64 active_view_impressions = 186;</code>
     */
    protected $active_view_impressions = null;
    /**
     * The ratio of impressions that could be measured by Active View over the
     * number of served impressions.
     *
     * Generated from protobuf field <code>optional double active_view_measurability = 187;</code>
     */
    protected $active_view_measurability = null;
    /**
     * The cost of the impressions you received that were measurable by Active
     * View.
     *
     * Generated from protobuf field <code>optional int64 active_view_measurable_cost_micros = 188;</code>
     */
    protected $active_view_measurable_cost_micros = null;
    /**
     * The number of times your ads are appearing on placements in positions
     * where they can be seen.
     *
     * Generated from protobuf field <code>optional int64 active_view_measurable_impressions = 189;</code>
     */
    protected $active_view_measurable_impressions = null;
    /**
     * The percentage of time when your ad appeared on an Active View enabled site
     * (measurable impressions) and was viewable (viewable impressions).
     *
     * Generated from protobuf field <code>optional double active_view_viewability = 190;</code>
     */
    protected $active_view_viewability = null;
    /**
     * All conversions from interactions (as oppose to view through conversions)
     * divided by the number of ad interactions.
     *
     * Generated from protobuf field <code>optional double all_conversions_from_interactions_rate = 191;</code>
     */
    protected $all_conversions_from_interactions_rate = null;
    /**
     * The value of all conversions.
     *
     * Generated from protobuf field <code>optional double all_conversions_value = 192;</code>
     */
    protected $all_conversions_value = null;
    /**
     * The value of all conversions. When this column is selected with date, the
     * values in date column means the conversion date. Details for the
     * by_conversion_date columns are available at
     * https://support.google.com/google-ads/answer/9549009.
     *
     * Generated from protobuf field <code>double all_conversions_value_by_conversion_date = 240;</code>
     */
    protected $all_conversions_value_by_conversion_date = 0.0;
    /**
     * All of new customers' lifetime conversion value. If you have set up
     * customer acquisition goal at either account level or campaign level, this
     * will include the additional conversion value from new customers for both
     * biddable and non-biddable conversions. If your campaign has adopted the
     * customer acquisition goal and selected "bid higher for new customers",
     * these values will be included in "all_conversions_value". See
     * https://support.google.com/google-ads/answer/******** for more details.
     *
     * Generated from protobuf field <code>optional double all_new_customer_lifetime_value = 294;</code>
     */
    protected $all_new_customer_lifetime_value = null;
    /**
     * The total number of conversions. This includes all conversions regardless
     * of the value of include_in_conversions_metric.
     *
     * Generated from protobuf field <code>optional double all_conversions = 193;</code>
     */
    protected $all_conversions = null;
    /**
     * The total number of conversions. This includes all conversions regardless
     * of the value of include_in_conversions_metric. When this column is selected
     * with date, the values in date column means the conversion date. Details for
     * the by_conversion_date columns are available at
     * https://support.google.com/google-ads/answer/9549009.
     *
     * Generated from protobuf field <code>double all_conversions_by_conversion_date = 241;</code>
     */
    protected $all_conversions_by_conversion_date = 0.0;
    /**
     * The value of all conversions divided by the total cost of ad interactions
     * (such as clicks for text ads or views for video ads).
     *
     * Generated from protobuf field <code>optional double all_conversions_value_per_cost = 194;</code>
     */
    protected $all_conversions_value_per_cost = null;
    /**
     * The number of times people clicked the "Call" button to call a store during
     * or after clicking an ad. This number doesn't include whether or not calls
     * were connected, or the duration of any calls.
     * This metric applies to feed items only.
     *
     * Generated from protobuf field <code>optional double all_conversions_from_click_to_call = 195;</code>
     */
    protected $all_conversions_from_click_to_call = null;
    /**
     * The number of times people clicked a "Get directions" button to navigate to
     * a store after clicking an ad.
     * This metric applies to feed items only.
     *
     * Generated from protobuf field <code>optional double all_conversions_from_directions = 196;</code>
     */
    protected $all_conversions_from_directions = null;
    /**
     * The value of all conversions from interactions divided by the total number
     * of interactions.
     *
     * Generated from protobuf field <code>optional double all_conversions_from_interactions_value_per_interaction = 197;</code>
     */
    protected $all_conversions_from_interactions_value_per_interaction = null;
    /**
     * The number of times people clicked a link to view a store's menu after
     * clicking an ad.
     * This metric applies to feed items only.
     *
     * Generated from protobuf field <code>optional double all_conversions_from_menu = 198;</code>
     */
    protected $all_conversions_from_menu = null;
    /**
     * The number of times people placed an order at a store after clicking an ad.
     * This metric applies to feed items only.
     *
     * Generated from protobuf field <code>optional double all_conversions_from_order = 199;</code>
     */
    protected $all_conversions_from_order = null;
    /**
     * The number of other conversions (for example, posting a review or saving a
     * location for a store) that occurred after people clicked an ad.
     * This metric applies to feed items only.
     *
     * Generated from protobuf field <code>optional double all_conversions_from_other_engagement = 200;</code>
     */
    protected $all_conversions_from_other_engagement = null;
    /**
     * Estimated number of times people visited a store after clicking an ad.
     * This metric applies to feed items only.
     *
     * Generated from protobuf field <code>optional double all_conversions_from_store_visit = 201;</code>
     */
    protected $all_conversions_from_store_visit = null;
    /**
     * The number of times that people were taken to a store's URL after clicking
     * an ad.
     * This metric applies to feed items only.
     *
     * Generated from protobuf field <code>optional double all_conversions_from_store_website = 202;</code>
     */
    protected $all_conversions_from_store_website = null;
    /**
     * This metric is part of the Auction Insights report, and tells how often
     * the ads of another participant showed as the very first ad above the
     * organic search results.
     * This percentage is computed only over the auctions that you appeared in
     * the page.
     * This metric is not publicly available.
     *
     * Generated from protobuf field <code>optional double auction_insight_search_absolute_top_impression_percentage = 258;</code>
     */
    protected $auction_insight_search_absolute_top_impression_percentage = null;
    /**
     * This metric is part of the Auction Insights report, and tells the
     * percentage of impressions that another participant obtained, over the total
     * number of impressions that your ads were eligible for.
     * Any value below 0.1 is reported as 0.0999.
     * This metric is not publicly available.
     *
     * Generated from protobuf field <code>optional double auction_insight_search_impression_share = 259;</code>
     */
    protected $auction_insight_search_impression_share = null;
    /**
     * This metric is part of the Auction Insights report, and tells the
     * percentage of impressions that your ads outranked (showed above)
     * another participant in the auction, compared to the total number of
     * impressions that your ads were eligible for.
     * Any value below 0.1 is reported as 0.0999.
     * This metric is not publicly available.
     *
     * Generated from protobuf field <code>optional double auction_insight_search_outranking_share = 260;</code>
     */
    protected $auction_insight_search_outranking_share = null;
    /**
     * This metric is part of the Auction Insights report, and tells how often
     * another participant's ad received an impression when your ad also received
     * an impression.
     * This metric is not publicly available.
     *
     * Generated from protobuf field <code>optional double auction_insight_search_overlap_rate = 261;</code>
     */
    protected $auction_insight_search_overlap_rate = null;
    /**
     * This metric is part of the Auction Insights report, and tells how often
     * another participant's ad was shown in a higher position than yours, when
     * both of your ads were shown at the same page.
     * This metric is not publicly available.
     *
     * Generated from protobuf field <code>optional double auction_insight_search_position_above_rate = 262;</code>
     */
    protected $auction_insight_search_position_above_rate = null;
    /**
     * This metric is part of the Auction Insights report, and tells how often
     * the ads of another participant showed above the organic search results.
     * This percentage is computed only over the auctions that you appeared in
     * the page.
     * This metric is not publicly available.
     *
     * Generated from protobuf field <code>optional double auction_insight_search_top_impression_percentage = 263;</code>
     */
    protected $auction_insight_search_top_impression_percentage = null;
    /**
     * The average amount you pay per interaction. This amount is the total cost
     * of your ads divided by the total number of interactions.
     *
     * Generated from protobuf field <code>optional double average_cost = 203;</code>
     */
    protected $average_cost = null;
    /**
     * The total cost of all clicks divided by the total number of clicks
     * received.
     *
     * Generated from protobuf field <code>optional double average_cpc = 204;</code>
     */
    protected $average_cpc = null;
    /**
     * The average amount that you've been charged for an ad engagement. This
     * amount is the total cost of all ad engagements divided by the total number
     * of ad engagements.
     *
     * Generated from protobuf field <code>optional double average_cpe = 205;</code>
     */
    protected $average_cpe = null;
    /**
     * Average cost-per-thousand impressions (CPM).
     *
     * Generated from protobuf field <code>optional double average_cpm = 206;</code>
     */
    protected $average_cpm = null;
    /**
     * The average amount you pay each time someone views your ad.
     * The average CPV is defined by the total cost of all ad views divided by
     * the number of views.
     *
     * Generated from protobuf field <code>optional double average_cpv = 207;</code>
     */
    protected $average_cpv = null;
    /**
     * Average number of pages viewed per session.
     *
     * Generated from protobuf field <code>optional double average_page_views = 208;</code>
     */
    protected $average_page_views = null;
    /**
     * Total duration of all sessions (in seconds) / number of sessions. Imported
     * from Google Analytics.
     *
     * Generated from protobuf field <code>optional double average_time_on_site = 209;</code>
     */
    protected $average_time_on_site = null;
    /**
     * An indication of how other advertisers are bidding on similar products.
     *
     * Generated from protobuf field <code>optional double benchmark_average_max_cpc = 210;</code>
     */
    protected $benchmark_average_max_cpc = null;
    /**
     * Number of app installs.
     *
     * Generated from protobuf field <code>optional double biddable_app_install_conversions = 254;</code>
     */
    protected $biddable_app_install_conversions = null;
    /**
     * Number of in-app actions.
     *
     * Generated from protobuf field <code>optional double biddable_app_post_install_conversions = 255;</code>
     */
    protected $biddable_app_post_install_conversions = null;
    /**
     * An indication on how other advertisers' Shopping ads for similar products
     * are performing based on how often people who see their ad click on it.
     *
     * Generated from protobuf field <code>optional double benchmark_ctr = 211;</code>
     */
    protected $benchmark_ctr = null;
    /**
     * Percentage of clicks where the user only visited a single page on your
     * site. Imported from Google Analytics.
     *
     * Generated from protobuf field <code>optional double bounce_rate = 212;</code>
     */
    protected $bounce_rate = null;
    /**
     * The number of clicks.
     *
     * Generated from protobuf field <code>optional int64 clicks = 131;</code>
     */
    protected $clicks = null;
    /**
     * The number of times your ad or your site's listing in the unpaid
     * results was clicked. See the help page at
     * https://support.google.com/google-ads/answer/3097241 for details.
     *
     * Generated from protobuf field <code>optional int64 combined_clicks = 156;</code>
     */
    protected $combined_clicks = null;
    /**
     * The number of times your ad or your site's listing in the unpaid
     * results was clicked (combined_clicks) divided by combined_queries. See the
     * help page at https://support.google.com/google-ads/answer/3097241 for
     * details.
     *
     * Generated from protobuf field <code>optional double combined_clicks_per_query = 157;</code>
     */
    protected $combined_clicks_per_query = null;
    /**
     * The number of searches that returned pages from your site in the unpaid
     * results or showed one of your text ads. See the help page at
     * https://support.google.com/google-ads/answer/3097241 for details.
     *
     * Generated from protobuf field <code>optional int64 combined_queries = 158;</code>
     */
    protected $combined_queries = null;
    /**
     * The estimated percent of times that your ad was eligible to show
     * on the Display Network but didn't because your budget was too low.
     * Note: Content budget lost impression share is reported in the range of 0
     * to 0.9. Any value above 0.9 is reported as 0.9001.
     *
     * Generated from protobuf field <code>optional double content_budget_lost_impression_share = 159;</code>
     */
    protected $content_budget_lost_impression_share = null;
    /**
     * The impressions you've received on the Display Network divided
     * by the estimated number of impressions you were eligible to receive.
     * Note: Content impression share is reported in the range of 0.1 to 1. Any
     * value below 0.1 is reported as 0.0999.
     *
     * Generated from protobuf field <code>optional double content_impression_share = 160;</code>
     */
    protected $content_impression_share = null;
    /**
     * The last date/time a conversion tag for this conversion action successfully
     * fired and was seen by Google Ads. This firing event may not have been the
     * result of an attributable conversion (for example, because the tag was
     * fired from a browser that did not previously click an ad from an
     * appropriate advertiser). The date/time is in the customer's time zone.
     *
     * Generated from protobuf field <code>optional string conversion_last_received_request_date_time = 161;</code>
     */
    protected $conversion_last_received_request_date_time = null;
    /**
     * The date of the most recent conversion for this conversion action. The date
     * is in the customer's time zone.
     *
     * Generated from protobuf field <code>optional string conversion_last_conversion_date = 162;</code>
     */
    protected $conversion_last_conversion_date = null;
    /**
     * The estimated percentage of impressions on the Display Network
     * that your ads didn't receive due to poor Ad Rank.
     * Note: Content rank lost impression share is reported in the range of 0
     * to 0.9. Any value above 0.9 is reported as 0.9001.
     *
     * Generated from protobuf field <code>optional double content_rank_lost_impression_share = 163;</code>
     */
    protected $content_rank_lost_impression_share = null;
    /**
     * Conversions from interactions divided by the number of ad interactions
     * (such as clicks for text ads or views for video ads). This only includes
     * conversion actions which include_in_conversions_metric attribute is set to
     * true. If you use conversion-based bidding, your bid strategies will
     * optimize for these conversions.
     *
     * Generated from protobuf field <code>optional double conversions_from_interactions_rate = 164;</code>
     */
    protected $conversions_from_interactions_rate = null;
    /**
     * The value of conversions. This only includes conversion actions which
     * include_in_conversions_metric attribute is set to true. If you use
     * conversion-based bidding, your bid strategies will optimize for these
     * conversions.
     *
     * Generated from protobuf field <code>optional double conversions_value = 165;</code>
     */
    protected $conversions_value = null;
    /**
     * The value of conversions. This only includes conversion actions which
     * include_in_conversions_metric attribute is set to true. If you use
     * conversion-based bidding, your bid strategies will optimize for these
     * conversions. When this column is selected with date, the values in date
     * column means the conversion date. Details for the by_conversion_date
     * columns are available at
     * https://support.google.com/google-ads/answer/9549009.
     *
     * Generated from protobuf field <code>double conversions_value_by_conversion_date = 242;</code>
     */
    protected $conversions_value_by_conversion_date = 0.0;
    /**
     * New customers' lifetime conversion value. If you have set up
     * customer acquisition goal at either account level or campaign level, this
     * will include the additional conversion value from new customers for
     * biddable conversions. If your campaign has adopted the customer
     * acquisition goal and selected "bid higher for new customers", these values
     * will be included in "conversions_value" for optimization. See
     * https://support.google.com/google-ads/answer/******** for more details.
     *
     * Generated from protobuf field <code>optional double new_customer_lifetime_value = 293;</code>
     */
    protected $new_customer_lifetime_value = null;
    /**
     * The value of conversions divided by the cost of ad interactions. This only
     * includes conversion actions which include_in_conversions_metric attribute
     * is set to true. If you use conversion-based bidding, your bid strategies
     * will optimize for these conversions.
     *
     * Generated from protobuf field <code>optional double conversions_value_per_cost = 166;</code>
     */
    protected $conversions_value_per_cost = null;
    /**
     * The value of conversions from interactions divided by the number of ad
     * interactions. This only includes conversion actions which
     * include_in_conversions_metric attribute is set to true. If you use
     * conversion-based bidding, your bid strategies will optimize for these
     * conversions.
     *
     * Generated from protobuf field <code>optional double conversions_from_interactions_value_per_interaction = 167;</code>
     */
    protected $conversions_from_interactions_value_per_interaction = null;
    /**
     * The number of conversions. This only includes conversion actions which
     * include_in_conversions_metric attribute is set to true. If you use
     * conversion-based bidding, your bid strategies will optimize for these
     * conversions.
     *
     * Generated from protobuf field <code>optional double conversions = 168;</code>
     */
    protected $conversions = null;
    /**
     * The number of conversions. This only includes conversion actions which
     * include_in_conversions_metric attribute is set to true. If you use
     * conversion-based bidding, your bid strategies will optimize for these
     * conversions. When this column is selected with date, the values in date
     * column means the conversion date. Details for the by_conversion_date
     * columns are available at
     * https://support.google.com/google-ads/answer/9549009.
     *
     * Generated from protobuf field <code>double conversions_by_conversion_date = 243;</code>
     */
    protected $conversions_by_conversion_date = 0.0;
    /**
     * The sum of your cost-per-click (CPC) and cost-per-thousand impressions
     * (CPM) costs during this period.
     *
     * Generated from protobuf field <code>optional int64 cost_micros = 169;</code>
     */
    protected $cost_micros = null;
    /**
     * The cost of ad interactions divided by all conversions.
     *
     * Generated from protobuf field <code>optional double cost_per_all_conversions = 170;</code>
     */
    protected $cost_per_all_conversions = null;
    /**
     * The cost of ad interactions divided by conversions. This only includes
     * conversion actions which include_in_conversions_metric attribute is set to
     * true. If you use conversion-based bidding, your bid strategies will
     * optimize for these conversions.
     *
     * Generated from protobuf field <code>optional double cost_per_conversion = 171;</code>
     */
    protected $cost_per_conversion = null;
    /**
     * The cost of ad interactions divided by current model attributed
     * conversions. This only includes conversion actions which
     * include_in_conversions_metric attribute is set to true. If you use
     * conversion-based bidding, your bid strategies will optimize for these
     * conversions.
     *
     * Generated from protobuf field <code>optional double cost_per_current_model_attributed_conversion = 172;</code>
     */
    protected $cost_per_current_model_attributed_conversion = null;
    /**
     * Conversions from when a customer clicks on a Google Ads ad on one device,
     * then converts on a different device or browser.
     * Cross-device conversions are already included in all_conversions.
     *
     * Generated from protobuf field <code>optional double cross_device_conversions = 173;</code>
     */
    protected $cross_device_conversions = null;
    /**
     * The sum of the value of cross-device conversions, in micros.
     *
     * Generated from protobuf field <code>optional int64 cross_device_conversions_value_micros = 312;</code>
     */
    protected $cross_device_conversions_value_micros = null;
    /**
     * The number of clicks your ad receives (Clicks) divided by the number
     * of times your ad is shown (Impressions).
     *
     * Generated from protobuf field <code>optional double ctr = 174;</code>
     */
    protected $ctr = null;
    /**
     * Shows how your historic conversions data would look under the attribution
     * model you've currently selected. This only includes conversion actions
     * which include_in_conversions_metric attribute is set to true. If you use
     * conversion-based bidding, your bid strategies will optimize for these
     * conversions.
     *
     * Generated from protobuf field <code>optional double current_model_attributed_conversions = 175;</code>
     */
    protected $current_model_attributed_conversions = null;
    /**
     * Current model attributed conversions from interactions divided by the
     * number of ad interactions (such as clicks for text ads or views for video
     * ads). This only includes conversion actions which
     * include_in_conversions_metric attribute is set to true. If you use
     * conversion-based bidding, your bid strategies will optimize for these
     * conversions.
     *
     * Generated from protobuf field <code>optional double current_model_attributed_conversions_from_interactions_rate = 176;</code>
     */
    protected $current_model_attributed_conversions_from_interactions_rate = null;
    /**
     * The value of current model attributed conversions from interactions divided
     * by the number of ad interactions. This only includes conversion actions
     * which include_in_conversions_metric attribute is set to true. If you use
     * conversion-based bidding, your bid strategies will optimize for these
     * conversions.
     *
     * Generated from protobuf field <code>optional double current_model_attributed_conversions_from_interactions_value_per_interaction = 177;</code>
     */
    protected $current_model_attributed_conversions_from_interactions_value_per_interaction = null;
    /**
     * The value of current model attributed conversions. This only includes
     * conversion actions which include_in_conversions_metric attribute is set to
     * true. If you use conversion-based bidding, your bid strategies will
     * optimize for these conversions.
     *
     * Generated from protobuf field <code>optional double current_model_attributed_conversions_value = 178;</code>
     */
    protected $current_model_attributed_conversions_value = null;
    /**
     * The value of current model attributed conversions divided by the cost of ad
     * interactions. This only includes conversion actions which
     * include_in_conversions_metric attribute is set to true. If you use
     * conversion-based bidding, your bid strategies will optimize for these
     * conversions.
     *
     * Generated from protobuf field <code>optional double current_model_attributed_conversions_value_per_cost = 179;</code>
     */
    protected $current_model_attributed_conversions_value_per_cost = null;
    /**
     * How often people engage with your ad after it's shown to them. This is the
     * number of ad expansions divided by the number of times your ad is shown.
     *
     * Generated from protobuf field <code>optional double engagement_rate = 180;</code>
     */
    protected $engagement_rate = null;
    /**
     * The number of engagements.
     * An engagement occurs when a viewer expands your Lightbox ad. Also, in the
     * future, other ad types may support engagement metrics.
     *
     * Generated from protobuf field <code>optional int64 engagements = 181;</code>
     */
    protected $engagements = null;
    /**
     * Average lead value based on clicks.
     *
     * Generated from protobuf field <code>optional double hotel_average_lead_value_micros = 213;</code>
     */
    protected $hotel_average_lead_value_micros = null;
    /**
     * Commission bid rate in micros. A 20% commission is represented as
     * 200,000.
     *
     * Generated from protobuf field <code>optional int64 hotel_commission_rate_micros = 256;</code>
     */
    protected $hotel_commission_rate_micros = null;
    /**
     * Expected commission cost. The result of multiplying the commission value
     * times the hotel_commission_rate in advertiser currency.
     *
     * Generated from protobuf field <code>optional double hotel_expected_commission_cost = 257;</code>
     */
    protected $hotel_expected_commission_cost = null;
    /**
     * The average price difference between the price offered by reporting hotel
     * advertiser and the cheapest price offered by the competing advertiser.
     *
     * Generated from protobuf field <code>optional double hotel_price_difference_percentage = 214;</code>
     */
    protected $hotel_price_difference_percentage = null;
    /**
     * The number of impressions that hotel partners could have had given their
     * feed performance.
     *
     * Generated from protobuf field <code>optional int64 hotel_eligible_impressions = 215;</code>
     */
    protected $hotel_eligible_impressions = null;
    /**
     * The creative historical quality score.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.enums.QualityScoreBucketEnum.QualityScoreBucket historical_creative_quality_score = 80;</code>
     */
    protected $historical_creative_quality_score = 0;
    /**
     * The quality of historical landing page experience.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.enums.QualityScoreBucketEnum.QualityScoreBucket historical_landing_page_quality_score = 81;</code>
     */
    protected $historical_landing_page_quality_score = 0;
    /**
     * The historical quality score.
     *
     * Generated from protobuf field <code>optional int64 historical_quality_score = 216;</code>
     */
    protected $historical_quality_score = null;
    /**
     * The historical search predicted click through rate (CTR).
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.enums.QualityScoreBucketEnum.QualityScoreBucket historical_search_predicted_ctr = 83;</code>
     */
    protected $historical_search_predicted_ctr = 0;
    /**
     * The number of times the ad was forwarded to someone else as a message.
     *
     * Generated from protobuf field <code>optional int64 gmail_forwards = 217;</code>
     */
    protected $gmail_forwards = null;
    /**
     * The number of times someone has saved your Gmail ad to their inbox as a
     * message.
     *
     * Generated from protobuf field <code>optional int64 gmail_saves = 218;</code>
     */
    protected $gmail_saves = null;
    /**
     * The number of clicks to the landing page on the expanded state of Gmail
     * ads.
     *
     * Generated from protobuf field <code>optional int64 gmail_secondary_clicks = 219;</code>
     */
    protected $gmail_secondary_clicks = null;
    /**
     * The number of times a store's location-based ad was shown.
     * This metric applies to feed items only.
     *
     * Generated from protobuf field <code>optional int64 impressions_from_store_reach = 220;</code>
     */
    protected $impressions_from_store_reach = null;
    /**
     * Count of how often your ad has appeared on a search results page or
     * website on the Google Network.
     *
     * Generated from protobuf field <code>optional int64 impressions = 221;</code>
     */
    protected $impressions = null;
    /**
     * How often people interact with your ad after it is shown to them.
     * This is the number of interactions divided by the number of times your ad
     * is shown.
     *
     * Generated from protobuf field <code>optional double interaction_rate = 222;</code>
     */
    protected $interaction_rate = null;
    /**
     * The number of interactions.
     * An interaction is the main user action associated with an ad format-clicks
     * for text and shopping ads, views for video ads, and so on.
     *
     * Generated from protobuf field <code>optional int64 interactions = 223;</code>
     */
    protected $interactions = null;
    /**
     * The types of payable and free interactions.
     *
     * Generated from protobuf field <code>repeated .google.ads.googleads.v15.enums.InteractionEventTypeEnum.InteractionEventType interaction_event_types = 100;</code>
     */
    private $interaction_event_types;
    /**
     * The percentage of clicks filtered out of your total number of clicks
     * (filtered + non-filtered clicks) during the reporting period.
     *
     * Generated from protobuf field <code>optional double invalid_click_rate = 224;</code>
     */
    protected $invalid_click_rate = null;
    /**
     * Number of clicks Google considers illegitimate and doesn't charge you for.
     *
     * Generated from protobuf field <code>optional int64 invalid_clicks = 225;</code>
     */
    protected $invalid_clicks = null;
    /**
     * Number of message chats initiated for Click To Message impressions that
     * were message tracking eligible.
     *
     * Generated from protobuf field <code>optional int64 message_chats = 226;</code>
     */
    protected $message_chats = null;
    /**
     * Number of Click To Message impressions that were message tracking eligible.
     *
     * Generated from protobuf field <code>optional int64 message_impressions = 227;</code>
     */
    protected $message_impressions = null;
    /**
     * Number of message chats initiated (message_chats) divided by the number
     * of message impressions (message_impressions).
     * Rate at which a user initiates a message chat from an ad impression with
     * a messaging option and message tracking enabled.
     * Note that this rate can be more than 1.0 for a given message impression.
     *
     * Generated from protobuf field <code>optional double message_chat_rate = 228;</code>
     */
    protected $message_chat_rate = null;
    /**
     * The percentage of mobile clicks that go to a mobile-friendly page.
     *
     * Generated from protobuf field <code>optional double mobile_friendly_clicks_percentage = 229;</code>
     */
    protected $mobile_friendly_clicks_percentage = null;
    /**
     * Total optimization score uplift of all recommendations.
     *
     * Generated from protobuf field <code>optional double optimization_score_uplift = 247;</code>
     */
    protected $optimization_score_uplift = null;
    /**
     * URL for the optimization score page in the Google Ads web interface.
     * This metric can be selected from `customer` or `campaign`, and can be
     * segmented by `segments.recommendation_type`. For example, `SELECT
     * metrics.optimization_score_url, segments.recommendation_type FROM
     * customer` will return a URL for each unique (customer, recommendation_type)
     * combination.
     *
     * Generated from protobuf field <code>optional string optimization_score_url = 248;</code>
     */
    protected $optimization_score_url = null;
    /**
     * The number of times someone clicked your site's listing in the unpaid
     * results for a particular query. See the help page at
     * https://support.google.com/google-ads/answer/3097241 for details.
     *
     * Generated from protobuf field <code>optional int64 organic_clicks = 230;</code>
     */
    protected $organic_clicks = null;
    /**
     * The number of times someone clicked your site's listing in the unpaid
     * results (organic_clicks) divided by the total number of searches that
     * returned pages from your site (organic_queries). See the help page at
     * https://support.google.com/google-ads/answer/3097241 for details.
     *
     * Generated from protobuf field <code>optional double organic_clicks_per_query = 231;</code>
     */
    protected $organic_clicks_per_query = null;
    /**
     * The number of listings for your site in the unpaid search results. See the
     * help page at https://support.google.com/google-ads/answer/3097241 for
     * details.
     *
     * Generated from protobuf field <code>optional int64 organic_impressions = 232;</code>
     */
    protected $organic_impressions = null;
    /**
     * The number of times a page from your site was listed in the unpaid search
     * results (organic_impressions) divided by the number of searches returning
     * your site's listing in the unpaid results (organic_queries). See the help
     * page at https://support.google.com/google-ads/answer/3097241 for details.
     *
     * Generated from protobuf field <code>optional double organic_impressions_per_query = 233;</code>
     */
    protected $organic_impressions_per_query = null;
    /**
     * The total number of searches that returned your site's listing in the
     * unpaid results. See the help page at
     * https://support.google.com/google-ads/answer/3097241 for details.
     *
     * Generated from protobuf field <code>optional int64 organic_queries = 234;</code>
     */
    protected $organic_queries = null;
    /**
     * Percentage of first-time sessions (from people who had never visited your
     * site before). Imported from Google Analytics.
     *
     * Generated from protobuf field <code>optional double percent_new_visitors = 235;</code>
     */
    protected $percent_new_visitors = null;
    /**
     * Number of offline phone calls.
     *
     * Generated from protobuf field <code>optional int64 phone_calls = 236;</code>
     */
    protected $phone_calls = null;
    /**
     * Number of offline phone impressions.
     *
     * Generated from protobuf field <code>optional int64 phone_impressions = 237;</code>
     */
    protected $phone_impressions = null;
    /**
     * Number of phone calls received (phone_calls) divided by the number of
     * times your phone number is shown (phone_impressions).
     *
     * Generated from protobuf field <code>optional double phone_through_rate = 238;</code>
     */
    protected $phone_through_rate = null;
    /**
     * Your clickthrough rate (Ctr) divided by the average clickthrough rate of
     * all advertisers on the websites that show your ads. Measures how your ads
     * perform on Display Network sites compared to other ads on the same sites.
     *
     * Generated from protobuf field <code>optional double relative_ctr = 239;</code>
     */
    protected $relative_ctr = null;
    /**
     * The percentage of the customer's Shopping or Search ad impressions that are
     * shown in the most prominent Shopping position. See
     * https://support.google.com/google-ads/answer/7501826
     * for details. Any value below 0.1 is reported as 0.0999.
     *
     * Generated from protobuf field <code>optional double search_absolute_top_impression_share = 136;</code>
     */
    protected $search_absolute_top_impression_share = null;
    /**
     * The number estimating how often your ad wasn't the very first ad above the
     * organic search results due to a low budget. Note: Search
     * budget lost absolute top impression share is reported in the range of 0 to
     * 0.9. Any value above 0.9 is reported as 0.9001.
     *
     * Generated from protobuf field <code>optional double search_budget_lost_absolute_top_impression_share = 137;</code>
     */
    protected $search_budget_lost_absolute_top_impression_share = null;
    /**
     * The estimated percent of times that your ad was eligible to show on the
     * Search Network but didn't because your budget was too low. Note: Search
     * budget lost impression share is reported in the range of 0 to 0.9. Any
     * value above 0.9 is reported as 0.9001.
     *
     * Generated from protobuf field <code>optional double search_budget_lost_impression_share = 138;</code>
     */
    protected $search_budget_lost_impression_share = null;
    /**
     * The number estimating how often your ad didn't show anywhere above the
     * organic search results due to a low budget. Note: Search
     * budget lost top impression share is reported in the range of 0 to 0.9. Any
     * value above 0.9 is reported as 0.9001.
     *
     * Generated from protobuf field <code>optional double search_budget_lost_top_impression_share = 139;</code>
     */
    protected $search_budget_lost_top_impression_share = null;
    /**
     * The number of clicks you've received on the Search Network
     * divided by the estimated number of clicks you were eligible to receive.
     * Note: Search click share is reported in the range of 0.1 to 1. Any value
     * below 0.1 is reported as 0.0999.
     *
     * Generated from protobuf field <code>optional double search_click_share = 140;</code>
     */
    protected $search_click_share = null;
    /**
     * The impressions you've received divided by the estimated number of
     * impressions you were eligible to receive on the Search Network for search
     * terms that matched your keywords exactly (or were close variants of your
     * keyword), regardless of your keyword match types. Note: Search exact match
     * impression share is reported in the range of 0.1 to 1. Any value below 0.1
     * is reported as 0.0999.
     *
     * Generated from protobuf field <code>optional double search_exact_match_impression_share = 141;</code>
     */
    protected $search_exact_match_impression_share = null;
    /**
     * The impressions you've received on the Search Network divided
     * by the estimated number of impressions you were eligible to receive.
     * Note: Search impression share is reported in the range of 0.1 to 1. Any
     * value below 0.1 is reported as 0.0999.
     *
     * Generated from protobuf field <code>optional double search_impression_share = 142;</code>
     */
    protected $search_impression_share = null;
    /**
     * The number estimating how often your ad wasn't the very first ad above the
     * organic search results due to poor Ad Rank.
     * Note: Search rank lost absolute top impression share is reported in the
     * range of 0 to 0.9. Any value above 0.9 is reported as 0.9001.
     *
     * Generated from protobuf field <code>optional double search_rank_lost_absolute_top_impression_share = 143;</code>
     */
    protected $search_rank_lost_absolute_top_impression_share = null;
    /**
     * The estimated percentage of impressions on the Search Network
     * that your ads didn't receive due to poor Ad Rank.
     * Note: Search rank lost impression share is reported in the range of 0 to
     * 0.9. Any value above 0.9 is reported as 0.9001.
     *
     * Generated from protobuf field <code>optional double search_rank_lost_impression_share = 144;</code>
     */
    protected $search_rank_lost_impression_share = null;
    /**
     * The number estimating how often your ad didn't show anywhere above the
     * organic search results due to poor Ad Rank.
     * Note: Search rank lost top impression share is reported in the range of 0
     * to 0.9. Any value above 0.9 is reported as 0.9001.
     *
     * Generated from protobuf field <code>optional double search_rank_lost_top_impression_share = 145;</code>
     */
    protected $search_rank_lost_top_impression_share = null;
    /**
     * The impressions you've received in the top location (anywhere above the
     * organic search results) compared to the estimated number of impressions you
     * were eligible to receive in the top location.
     * Note: Search top impression share is reported in the range of 0.1 to 1. Any
     * value below 0.1 is reported as 0.0999.
     *
     * Generated from protobuf field <code>optional double search_top_impression_share = 146;</code>
     */
    protected $search_top_impression_share = null;
    /**
     * Search volume range for a search term insight category.
     *
     * Generated from protobuf field <code>optional .google.ads.googleads.v15.common.SearchVolumeRange search_volume = 295;</code>
     */
    protected $search_volume = null;
    /**
     * A measure of how quickly your page loads after clicks on your mobile ads.
     * The score is a range from 1 to 10, 10 being the fastest.
     *
     * Generated from protobuf field <code>optional int64 speed_score = 147;</code>
     */
    protected $speed_score = null;
    /**
     * The average Target CPA, or unset if not available (for example, for
     * campaigns that had traffic from portfolio bidding strategies or non-tCPA).
     *
     * Generated from protobuf field <code>optional int64 average_target_cpa_micros = 290;</code>
     */
    protected $average_target_cpa_micros = null;
    /**
     * The average Target ROAS, or unset if not available (for example, for
     * campaigns that had traffic from portfolio bidding strategies or non-tROAS).
     *
     * Generated from protobuf field <code>optional double average_target_roas = 250;</code>
     */
    protected $average_target_roas = null;
    /**
     * The percent of your ad impressions that are shown anywhere above the
     * organic search results.
     *
     * Generated from protobuf field <code>optional double top_impression_percentage = 148;</code>
     */
    protected $top_impression_percentage = null;
    /**
     * The percentage of ad clicks to Accelerated Mobile Pages (AMP) landing pages
     * that reach a valid AMP page.
     *
     * Generated from protobuf field <code>optional double valid_accelerated_mobile_pages_clicks_percentage = 149;</code>
     */
    protected $valid_accelerated_mobile_pages_clicks_percentage = null;
    /**
     * The value of all conversions divided by the number of all conversions.
     *
     * Generated from protobuf field <code>optional double value_per_all_conversions = 150;</code>
     */
    protected $value_per_all_conversions = null;
    /**
     * The value of all conversions divided by the number of all conversions. When
     * this column is selected with date, the values in date column means the
     * conversion date. Details for the by_conversion_date columns are available
     * at https://support.google.com/google-ads/answer/9549009.
     *
     * Generated from protobuf field <code>optional double value_per_all_conversions_by_conversion_date = 244;</code>
     */
    protected $value_per_all_conversions_by_conversion_date = null;
    /**
     * The value of conversions divided by the number of conversions. This only
     * includes conversion actions which include_in_conversions_metric attribute
     * is set to true. If you use conversion-based bidding, your bid strategies
     * will optimize for these conversions.
     *
     * Generated from protobuf field <code>optional double value_per_conversion = 151;</code>
     */
    protected $value_per_conversion = null;
    /**
     * The value of conversions divided by the number of conversions. This only
     * includes conversion actions which include_in_conversions_metric attribute
     * is set to true. If you use conversion-based bidding, your bid strategies
     * will optimize for these conversions. When this column is selected with
     * date, the values in date column means the conversion date. Details for the
     * by_conversion_date columns are available at
     * https://support.google.com/google-ads/answer/9549009.
     *
     * Generated from protobuf field <code>optional double value_per_conversions_by_conversion_date = 245;</code>
     */
    protected $value_per_conversions_by_conversion_date = null;
    /**
     * The value of current model attributed conversions divided by the number of
     * the conversions. This only includes conversion actions which
     * include_in_conversions_metric attribute is set to true. If you use
     * conversion-based bidding, your bid strategies will optimize for these
     * conversions.
     *
     * Generated from protobuf field <code>optional double value_per_current_model_attributed_conversion = 152;</code>
     */
    protected $value_per_current_model_attributed_conversion = null;
    /**
     * Percentage of impressions where the viewer watched all of your video.
     *
     * Generated from protobuf field <code>optional double video_quartile_p100_rate = 132;</code>
     */
    protected $video_quartile_p100_rate = null;
    /**
     * Percentage of impressions where the viewer watched 25% of your video.
     *
     * Generated from protobuf field <code>optional double video_quartile_p25_rate = 133;</code>
     */
    protected $video_quartile_p25_rate = null;
    /**
     * Percentage of impressions where the viewer watched 50% of your video.
     *
     * Generated from protobuf field <code>optional double video_quartile_p50_rate = 134;</code>
     */
    protected $video_quartile_p50_rate = null;
    /**
     * Percentage of impressions where the viewer watched 75% of your video.
     *
     * Generated from protobuf field <code>optional double video_quartile_p75_rate = 135;</code>
     */
    protected $video_quartile_p75_rate = null;
    /**
     * The number of views your TrueView video ad receives divided by its number
     * of impressions, including thumbnail impressions for TrueView in-display
     * ads.
     *
     * Generated from protobuf field <code>optional double video_view_rate = 153;</code>
     */
    protected $video_view_rate = null;
    /**
     * The number of times your video ads were viewed.
     *
     * Generated from protobuf field <code>optional int64 video_views = 154;</code>
     */
    protected $video_views = null;
    /**
     * The total number of view-through conversions.
     * These happen when a customer sees an image or rich media ad, then later
     * completes a conversion on your site without interacting with (for example,
     * clicking on) another ad.
     *
     * Generated from protobuf field <code>optional int64 view_through_conversions = 155;</code>
     */
    protected $view_through_conversions = null;
    /**
     * The number of iOS Store Kit Ad Network conversions.
     *
     * Generated from protobuf field <code>int64 sk_ad_network_installs = 246;</code>
     */
    protected $sk_ad_network_installs = 0;
    /**
     * The total number of iOS Store Kit Ad Network conversions.
     *
     * Generated from protobuf field <code>int64 sk_ad_network_total_conversions = 292;</code>
     */
    protected $sk_ad_network_total_conversions = 0;
    /**
     * Clicks from properties not owned by the publisher for which the traffic
     * the publisher has paid for or acquired through incentivized activity
     *
     * Generated from protobuf field <code>int64 publisher_purchased_clicks = 264;</code>
     */
    protected $publisher_purchased_clicks = 0;
    /**
     * Clicks from properties for which the traffic the publisher has not paid
     * for or acquired through incentivized activity
     *
     * Generated from protobuf field <code>int64 publisher_organic_clicks = 265;</code>
     */
    protected $publisher_organic_clicks = 0;
    /**
     * Clicks from traffic which is not identified as "Publisher Purchased" or
     * "Publisher Organic"
     *
     * Generated from protobuf field <code>int64 publisher_unknown_clicks = 266;</code>
     */
    protected $publisher_unknown_clicks = 0;
    /**
     * Number of call button clicks on any location surface after a chargeable ad
     * event (click or impression). This measure is coming from Asset based
     * location.
     *
     * Generated from protobuf field <code>optional double all_conversions_from_location_asset_click_to_call = 267;</code>
     */
    protected $all_conversions_from_location_asset_click_to_call = null;
    /**
     * Number of driving directions clicks on any location surface after a
     * chargeable ad event (click or impression). This measure is coming
     * from Asset based location.
     *
     * Generated from protobuf field <code>optional double all_conversions_from_location_asset_directions = 268;</code>
     */
    protected $all_conversions_from_location_asset_directions = null;
    /**
     * Number of menu link clicks on any location surface after a chargeable ad
     * event (click or impression). This measure is coming from Asset based
     * location.
     *
     * Generated from protobuf field <code>optional double all_conversions_from_location_asset_menu = 269;</code>
     */
    protected $all_conversions_from_location_asset_menu = null;
    /**
     * Number of order clicks on any location surface after a chargeable ad event
     * (click or impression). This measure is coming from Asset based
     * location.
     *
     * Generated from protobuf field <code>optional double all_conversions_from_location_asset_order = 270;</code>
     */
    protected $all_conversions_from_location_asset_order = null;
    /**
     * Number of other types of local action clicks on any location surface after
     * a chargeable ad event (click or impression). This measure is coming
     * from Asset based location.
     *
     * Generated from protobuf field <code>optional double all_conversions_from_location_asset_other_engagement = 271;</code>
     */
    protected $all_conversions_from_location_asset_other_engagement = null;
    /**
     * Estimated number of visits to the store after a chargeable
     * ad event (click or impression). This measure is coming from Asset
     * based location.
     *
     * Generated from protobuf field <code>optional double all_conversions_from_location_asset_store_visits = 272;</code>
     */
    protected $all_conversions_from_location_asset_store_visits = null;
    /**
     * Number of website URL clicks on any location surface after a chargeable ad
     * event (click or impression). This measure is coming from Asset based
     * location.
     *
     * Generated from protobuf field <code>optional double all_conversions_from_location_asset_website = 273;</code>
     */
    protected $all_conversions_from_location_asset_website = null;
    /**
     * Number of impressions in which the store location was shown or the location
     * was used for targeting. This measure is coming from Asset based
     * location.
     *
     * Generated from protobuf field <code>optional int64 eligible_impressions_from_location_asset_store_reach = 274;</code>
     */
    protected $eligible_impressions_from_location_asset_store_reach = null;
    /**
     * Number of call button clicks on any location surface after an impression.
     * This measure is coming from Asset based location.
     *
     * Generated from protobuf field <code>optional double view_through_conversions_from_location_asset_click_to_call = 275;</code>
     */
    protected $view_through_conversions_from_location_asset_click_to_call = null;
    /**
     * Number of driving directions clicks on any location surface after an
     * impression. This measure is coming from Asset based location.
     *
     * Generated from protobuf field <code>optional double view_through_conversions_from_location_asset_directions = 276;</code>
     */
    protected $view_through_conversions_from_location_asset_directions = null;
    /**
     * Number of menu link clicks on any location surface after an impression.
     * This measure is coming from Asset based location.
     *
     * Generated from protobuf field <code>optional double view_through_conversions_from_location_asset_menu = 277;</code>
     */
    protected $view_through_conversions_from_location_asset_menu = null;
    /**
     * Number of order clicks on any location surface after an impression. This
     * measure is coming from Asset based location.
     *
     * Generated from protobuf field <code>optional double view_through_conversions_from_location_asset_order = 278;</code>
     */
    protected $view_through_conversions_from_location_asset_order = null;
    /**
     * Number of other types of local action clicks on any location surface after
     * an impression. This measure is coming from Asset based location.
     *
     * Generated from protobuf field <code>optional double view_through_conversions_from_location_asset_other_engagement = 279;</code>
     */
    protected $view_through_conversions_from_location_asset_other_engagement = null;
    /**
     * Estimated number of visits to the store after an impression.
     * This measure is coming from Asset based location.
     *
     * Generated from protobuf field <code>optional double view_through_conversions_from_location_asset_store_visits = 280;</code>
     */
    protected $view_through_conversions_from_location_asset_store_visits = null;
    /**
     * Number of website URL clicks on any location surface after an impression.
     * This measure is coming from Asset based location.
     *
     * Generated from protobuf field <code>optional double view_through_conversions_from_location_asset_website = 281;</code>
     */
    protected $view_through_conversions_from_location_asset_website = null;
    /**
     * Orders is the total number of purchase conversions you received attributed
     * to your ads.
     * How it works: You report conversions with cart data for
     * completed purchases on your website. If a conversion is attributed to
     * previous interactions with your ads (clicks for text or Shopping ads, views
     * for video ads etc.) it's counted as an order.
     * Example: Someone clicked on a Shopping ad for a hat then bought the same
     * hat and a shirt in an order on your website. Even though they bought 2
     * products, this would count as 1 order.
     * This metric is only available if you report conversions with cart data.
     *
     * Generated from protobuf field <code>optional double orders = 296;</code>
     */
    protected $orders = null;
    /**
     * Average order value is the average revenue you made per order attributed to
     * your ads.
     * How it works: You report conversions with cart data for completed purchases
     * on your website. Average order value is the total revenue from your orders
     * divided by the total number of orders.
     * Example: You received 3 orders which made $10, $15 and $20 worth of
     * revenue. The average order value is $15 = ($10 + $15 + $20)/3.
     * This metric is only available if you report conversions with cart data.
     *
     * Generated from protobuf field <code>optional int64 average_order_value_micros = 297;</code>
     */
    protected $average_order_value_micros = null;
    /**
     * Average cart size is the average number of products in each order
     * attributed to your ads.
     * How it works: You report conversions with cart data for completed purchases
     * on your website. Average cart size is the total number of products sold
     * divided by the total number of orders you received.
     * Example: You received 2 orders, the first included 3 products and the
     * second included 2. The average cart size is 2.5 products = (3+2)/2.
     * This metric is only available if you report conversions with cart data.
     *
     * Generated from protobuf field <code>optional double average_cart_size = 298;</code>
     */
    protected $average_cart_size = null;
    /**
     * Cost of goods sold (COGS) is the total cost of the products you sold in
     * orders attributed to your ads.
     * How it works: You can add a cost of goods sold value to every product in
     * Merchant Center. If you report conversions with cart data, the products you
     * sold are matched with their cost of goods sold value and this can be used
     * to calculate the gross profit you made on each order.
     * Example: Someone clicked on a Shopping ad for a hat then bought the same
     * hat and a shirt. The hat has a cost of goods sold value of $3, the shirt
     * has a cost of goods sold value of $5. The cost of goods sold for this order
     * is $8 = $3 + $5.
     * This metric is only available if you report conversions with cart data.
     *
     * Generated from protobuf field <code>optional int64 cost_of_goods_sold_micros = 299;</code>
     */
    protected $cost_of_goods_sold_micros = null;
    /**
     * Gross profit is the profit you made from orders attributed to your ads
     * minus the cost of goods sold (COGS).
     * How it works: Gross profit is the revenue you made from sales attributed to
     * your ads minus cost of goods sold. Gross profit calculations only include
     * products that have a cost of goods sold value in Merchant Center.
     * Example: Someone clicked on a Shopping ad for a hat then bought the same
     * hat and a shirt in an order from your website. The hat is priced $10 and
     * the shirt is priced $20. The hat has a cost of goods sold value of $3, but
     * the shirt has no cost of goods sold value. Gross profit for this order will
     * only take into account the hat, so it's $7 = $10 - $3.
     * This metric is only available if you report conversions with cart data.
     *
     * Generated from protobuf field <code>optional int64 gross_profit_micros = 300;</code>
     */
    protected $gross_profit_micros = null;
    /**
     * Gross profit margin is the percentage gross profit you made from orders
     * attributed to your ads, after taking out the cost of goods sold (COGS).
     * How it works: You report conversions with cart data for completed purchases
     * on your website. Gross profit margin is the gross profit you made divided
     * by your total revenue and multiplied by 100%. Gross profit margin
     * calculations only include products that have a cost of goods sold value in
     * Merchant Center.
     * Example: Someone bought a hat and a shirt in an order on your website. The
     * hat is priced $10 and has a cost of goods sold value of $3. The shirt is
     * priced $20 but has no cost of goods sold value. Gross profit margin for
     * this order will only take into account the hat because it has a cost of
     * goods sold value, so it's 70% = ($10 - $3)/$10 x 100%.
     * This metric is only available if you report conversions with cart data.
     *
     * Generated from protobuf field <code>optional double gross_profit_margin = 301;</code>
     */
    protected $gross_profit_margin = null;
    /**
     * Revenue is the total amount you made from orders attributed to your ads.
     * How it works: You report conversions with cart data for completed purchases
     * on your website. Revenue is the total value of all the orders you received
     * attributed to your ads, minus any discount.
     * Example: Someone clicked on a Shopping ad  for a hat then bought the same
     * hat and a shirt in an order from your website. The hat is priced $10 and
     * the shirt is priced $20. The entire order has a $5 discount. The revenue
     * from this order is $25 = ($10 + $20) - $5.
     * This metric is only available if you report conversions with cart data.
     *
     * Generated from protobuf field <code>optional int64 revenue_micros = 302;</code>
     */
    protected $revenue_micros = null;
    /**
     * Units sold is the total number of products sold from orders attributed to
     * your ads.
     * How it works: You report conversions with cart data for completed purchases
     * on your website. Units sold is the total number of products sold from all
     * orders attributed to your ads.
     * Example: Someone clicked on a Shopping ad for a hat then bought the same
     * hat, a shirt and a jacket. The units sold in this order is 3.
     * This metric is only available if you report conversions with cart data.
     *
     * Generated from protobuf field <code>optional double units_sold = 303;</code>
     */
    protected $units_sold = null;
    /**
     * Cross-sell cost of goods sold (COGS) is the total cost of products sold as
     * a result of advertising a different product.
     * How it works: You report conversions with cart data for completed purchases
     * on your website. If the ad that was interacted with before the purchase has
     * an associated product (see Shopping Ads) then this product is considered
     * the advertised product. Any product included in the order the customer
     * places is a sold product. If these products don't match then this is
     * considered cross-sell. Cross-sell cost of goods sold is the total cost of
     * the products sold that weren't advertised.
     * Example: Someone clicked on a Shopping ad for a hat then bought the same
     * hat and a shirt. The hat has a cost of goods sold value of $3, the shirt
     * has a cost of goods sold value of $5. The cross-sell cost of goods sold for
     * this order is $5.
     * This metric is only available if you report conversions with cart data.
     *
     * Generated from protobuf field <code>optional int64 cross_sell_cost_of_goods_sold_micros = 304;</code>
     */
    protected $cross_sell_cost_of_goods_sold_micros = null;
    /**
     * Cross-sell gross profit is the profit you made from products sold as a
     * result of advertising a different product, minus cost of goods sold (COGS).
     * How it works: You report conversions with cart data for completed purchases
     * on your website. If the ad that was interacted with before the purchase has
     * an associated product (see Shopping Ads) then this product is considered
     * the advertised product. Any product included in the purchase is a sold
     * product. If these products don't match then this is considered cross-sell.
     * Cross-sell gross profit is the revenue you made from cross-sell attributed
     * to your ads minus the cost of the goods sold.
     * Example: Someone clicked on a Shopping ad for a hat then bought the same
     * hat and a shirt. The shirt is priced $20 and has a cost of goods sold value
     * of $5. The cross-sell gross profit of this order is $15 = $20 - $5.
     * This metric is only available if you report conversions with cart data.
     *
     * Generated from protobuf field <code>optional int64 cross_sell_gross_profit_micros = 305;</code>
     */
    protected $cross_sell_gross_profit_micros = null;
    /**
     * Cross-sell revenue is the total amount you made from products sold as a
     * result of advertising a different product.
     * How it works: You report conversions with cart data for completed purchases
     * on your website. If the ad that was interacted with before the purchase has
     * an associated product (see Shopping Ads) then this product is considered
     * the advertised product. Any product included in the order the customer
     * places is a sold product. If these products don't match then this is
     * considered cross-sell. Cross-sell revenue is the total value you made from
     * cross-sell attributed to your ads.
     * Example: Someone clicked on a Shopping ad for a hat then bought the same
     * hat and a shirt. The hat is priced $10 and the shirt is priced $20. The
     * cross-sell revenue of this order is $20.
     * This metric is only available if you report conversions with cart data.
     *
     * Generated from protobuf field <code>optional int64 cross_sell_revenue_micros = 306;</code>
     */
    protected $cross_sell_revenue_micros = null;
    /**
     * Cross-sell units sold is the total number of products sold as a result of
     * advertising a different product.
     * How it works: You report conversions with cart data for completed purchases
     * on your website. If the ad that was interacted with before the purchase has
     * an associated product (see Shopping Ads) then this product is considered
     * the advertised product. Any product included in the order the customer
     * places is a sold product. If these products don't match then this is
     * considered cross-sell. Cross-sell units sold is the total number of
     * cross-sold products from all orders attributed to your ads.
     * Example: Someone clicked on a Shopping ad for a hat then bought the same
     * hat, a shirt and a jacket. The cross-sell units sold in this order is 2.
     * This metric is only available if you report conversions with cart data.
     *
     * Generated from protobuf field <code>optional double cross_sell_units_sold = 307;</code>
     */
    protected $cross_sell_units_sold = null;
    /**
     * Lead cost of goods sold (COGS) is the total cost of products sold as a
     * result of advertising the same product.
     * How it works: You report conversions with cart data for completed purchases
     * on your website. If the ad that was interacted with has an associated
     * product (see Shopping Ads) then this product is considered the advertised
     * product. Any product included in the order the customer places is a sold
     * product. If the advertised and sold products match, then the cost of these
     * goods is counted under lead cost of goods sold.
     * Example: Someone clicked on a Shopping ad for a hat then bought the same
     * hat and a shirt. The hat has a cost of goods sold value of $3, the shirt
     * has a cost of goods sold value of $5. The lead cost of goods sold for this
     * order is $3.
     * This metric is only available if you report conversions with cart data.
     *
     * Generated from protobuf field <code>optional int64 lead_cost_of_goods_sold_micros = 308;</code>
     */
    protected $lead_cost_of_goods_sold_micros = null;
    /**
     * Lead gross profit is the profit you made from products sold as a result of
     * advertising the same product, minus cost of goods sold (COGS).
     * How it works: You report conversions with cart data for completed purchases
     * on your website. If the ad that was interacted with before the purchase has
     * an associated product (see Shopping Ads) then this product is considered
     * the advertised product. Any product included in the order the customer
     * places is a sold product. If the advertised and sold products match, then
     * the revenue you made from these sales minus the cost of goods sold is your
     * lead gross profit.
     * Example: Someone clicked on a Shopping ad for a hat then bought the same
     * hat and a shirt. The hat is priced $10 and has a cost of goods sold value
     * of $3. The lead gross profit of this order is $7 = $10 - $3.
     * This metric is only available if you report conversions with cart data.
     *
     * Generated from protobuf field <code>optional int64 lead_gross_profit_micros = 309;</code>
     */
    protected $lead_gross_profit_micros = null;
    /**
     * Lead revenue is the total amount you made from products sold as a result of
     * advertising the same product.
     * How it works: You report conversions with cart data for completed purchases
     * on your website. If the ad that was interacted with before the purchase has
     * an associated product (see Shopping Ads) then this product is considered
     * the advertised product. Any product included in the order the customer
     * places is a sold product. If the advertised and sold products match, then
     * the total value you made from the sales of these products is shown under
     * lead revenue.
     * Example: Someone clicked on a Shopping ad for a hat then bought the same
     * hat and a shirt. The hat is priced $10 and the shirt is priced $20. The
     * lead revenue of this order is $10.
     * This metric is only available if you report conversions with cart data.
     *
     * Generated from protobuf field <code>optional int64 lead_revenue_micros = 310;</code>
     */
    protected $lead_revenue_micros = null;
    /**
     * Lead units sold is the total number of products sold as a result of
     * advertising the same product.
     * How it works: You report conversions with cart data for completed purchases
     * on your website. If the ad that was interacted with before the purchase has
     * an associated product (see Shopping Ads) then this product is considered
     * the advertised product. Any product included in the order the customer
     * places is a sold product. If the advertised and sold products match, then
     * the total number of these products sold is shown under lead units sold.
     * Example: Someone clicked on a Shopping ad for a hat then bought the same
     * hat, a shirt and a jacket. The lead units sold in this order is 1.
     * This metric is only available if you report conversions with cart data.
     *
     * Generated from protobuf field <code>optional double lead_units_sold = 311;</code>
     */
    protected $lead_units_sold = null;
    /**
     * The number of unique users who saw your ad during the requested time
     * period. This metric cannot be aggregated, and can only be requested for
     * date ranges of 92 days or less. This metric is available for following
     * campaign types - Display, Video, Discovery and App.
     *
     * Generated from protobuf field <code>optional int64 unique_users = 319;</code>
     */
    protected $unique_users = null;
    /**
     * The average number of times a unique user saw your ad during the requested
     * time period. This metric cannot be aggregated, and can only be requested
     * for date ranges of 92 days or less. This metric is available for following
     * campaign types - Display, Video, Discovery and App.
     *
     * Generated from protobuf field <code>optional double average_impression_frequency_per_user = 320;</code>
     */
    protected $average_impression_frequency_per_user = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type float $absolute_top_impression_percentage
     *           The percent of your ad impressions that are shown as the very first ad
     *           above the organic search results.
     *     @type float $active_view_cpm
     *           Average cost of viewable impressions (`active_view_impressions`).
     *     @type float $active_view_ctr
     *           Active view measurable clicks divided by active view viewable impressions.
     *           This metric is reported only for the Display Network.
     *     @type int|string $active_view_impressions
     *           A measurement of how often your ad has become viewable on a Display
     *           Network site.
     *     @type float $active_view_measurability
     *           The ratio of impressions that could be measured by Active View over the
     *           number of served impressions.
     *     @type int|string $active_view_measurable_cost_micros
     *           The cost of the impressions you received that were measurable by Active
     *           View.
     *     @type int|string $active_view_measurable_impressions
     *           The number of times your ads are appearing on placements in positions
     *           where they can be seen.
     *     @type float $active_view_viewability
     *           The percentage of time when your ad appeared on an Active View enabled site
     *           (measurable impressions) and was viewable (viewable impressions).
     *     @type float $all_conversions_from_interactions_rate
     *           All conversions from interactions (as oppose to view through conversions)
     *           divided by the number of ad interactions.
     *     @type float $all_conversions_value
     *           The value of all conversions.
     *     @type float $all_conversions_value_by_conversion_date
     *           The value of all conversions. When this column is selected with date, the
     *           values in date column means the conversion date. Details for the
     *           by_conversion_date columns are available at
     *           https://support.google.com/google-ads/answer/9549009.
     *     @type float $all_new_customer_lifetime_value
     *           All of new customers' lifetime conversion value. If you have set up
     *           customer acquisition goal at either account level or campaign level, this
     *           will include the additional conversion value from new customers for both
     *           biddable and non-biddable conversions. If your campaign has adopted the
     *           customer acquisition goal and selected "bid higher for new customers",
     *           these values will be included in "all_conversions_value". See
     *           https://support.google.com/google-ads/answer/******** for more details.
     *     @type float $all_conversions
     *           The total number of conversions. This includes all conversions regardless
     *           of the value of include_in_conversions_metric.
     *     @type float $all_conversions_by_conversion_date
     *           The total number of conversions. This includes all conversions regardless
     *           of the value of include_in_conversions_metric. When this column is selected
     *           with date, the values in date column means the conversion date. Details for
     *           the by_conversion_date columns are available at
     *           https://support.google.com/google-ads/answer/9549009.
     *     @type float $all_conversions_value_per_cost
     *           The value of all conversions divided by the total cost of ad interactions
     *           (such as clicks for text ads or views for video ads).
     *     @type float $all_conversions_from_click_to_call
     *           The number of times people clicked the "Call" button to call a store during
     *           or after clicking an ad. This number doesn't include whether or not calls
     *           were connected, or the duration of any calls.
     *           This metric applies to feed items only.
     *     @type float $all_conversions_from_directions
     *           The number of times people clicked a "Get directions" button to navigate to
     *           a store after clicking an ad.
     *           This metric applies to feed items only.
     *     @type float $all_conversions_from_interactions_value_per_interaction
     *           The value of all conversions from interactions divided by the total number
     *           of interactions.
     *     @type float $all_conversions_from_menu
     *           The number of times people clicked a link to view a store's menu after
     *           clicking an ad.
     *           This metric applies to feed items only.
     *     @type float $all_conversions_from_order
     *           The number of times people placed an order at a store after clicking an ad.
     *           This metric applies to feed items only.
     *     @type float $all_conversions_from_other_engagement
     *           The number of other conversions (for example, posting a review or saving a
     *           location for a store) that occurred after people clicked an ad.
     *           This metric applies to feed items only.
     *     @type float $all_conversions_from_store_visit
     *           Estimated number of times people visited a store after clicking an ad.
     *           This metric applies to feed items only.
     *     @type float $all_conversions_from_store_website
     *           The number of times that people were taken to a store's URL after clicking
     *           an ad.
     *           This metric applies to feed items only.
     *     @type float $auction_insight_search_absolute_top_impression_percentage
     *           This metric is part of the Auction Insights report, and tells how often
     *           the ads of another participant showed as the very first ad above the
     *           organic search results.
     *           This percentage is computed only over the auctions that you appeared in
     *           the page.
     *           This metric is not publicly available.
     *     @type float $auction_insight_search_impression_share
     *           This metric is part of the Auction Insights report, and tells the
     *           percentage of impressions that another participant obtained, over the total
     *           number of impressions that your ads were eligible for.
     *           Any value below 0.1 is reported as 0.0999.
     *           This metric is not publicly available.
     *     @type float $auction_insight_search_outranking_share
     *           This metric is part of the Auction Insights report, and tells the
     *           percentage of impressions that your ads outranked (showed above)
     *           another participant in the auction, compared to the total number of
     *           impressions that your ads were eligible for.
     *           Any value below 0.1 is reported as 0.0999.
     *           This metric is not publicly available.
     *     @type float $auction_insight_search_overlap_rate
     *           This metric is part of the Auction Insights report, and tells how often
     *           another participant's ad received an impression when your ad also received
     *           an impression.
     *           This metric is not publicly available.
     *     @type float $auction_insight_search_position_above_rate
     *           This metric is part of the Auction Insights report, and tells how often
     *           another participant's ad was shown in a higher position than yours, when
     *           both of your ads were shown at the same page.
     *           This metric is not publicly available.
     *     @type float $auction_insight_search_top_impression_percentage
     *           This metric is part of the Auction Insights report, and tells how often
     *           the ads of another participant showed above the organic search results.
     *           This percentage is computed only over the auctions that you appeared in
     *           the page.
     *           This metric is not publicly available.
     *     @type float $average_cost
     *           The average amount you pay per interaction. This amount is the total cost
     *           of your ads divided by the total number of interactions.
     *     @type float $average_cpc
     *           The total cost of all clicks divided by the total number of clicks
     *           received.
     *     @type float $average_cpe
     *           The average amount that you've been charged for an ad engagement. This
     *           amount is the total cost of all ad engagements divided by the total number
     *           of ad engagements.
     *     @type float $average_cpm
     *           Average cost-per-thousand impressions (CPM).
     *     @type float $average_cpv
     *           The average amount you pay each time someone views your ad.
     *           The average CPV is defined by the total cost of all ad views divided by
     *           the number of views.
     *     @type float $average_page_views
     *           Average number of pages viewed per session.
     *     @type float $average_time_on_site
     *           Total duration of all sessions (in seconds) / number of sessions. Imported
     *           from Google Analytics.
     *     @type float $benchmark_average_max_cpc
     *           An indication of how other advertisers are bidding on similar products.
     *     @type float $biddable_app_install_conversions
     *           Number of app installs.
     *     @type float $biddable_app_post_install_conversions
     *           Number of in-app actions.
     *     @type float $benchmark_ctr
     *           An indication on how other advertisers' Shopping ads for similar products
     *           are performing based on how often people who see their ad click on it.
     *     @type float $bounce_rate
     *           Percentage of clicks where the user only visited a single page on your
     *           site. Imported from Google Analytics.
     *     @type int|string $clicks
     *           The number of clicks.
     *     @type int|string $combined_clicks
     *           The number of times your ad or your site's listing in the unpaid
     *           results was clicked. See the help page at
     *           https://support.google.com/google-ads/answer/3097241 for details.
     *     @type float $combined_clicks_per_query
     *           The number of times your ad or your site's listing in the unpaid
     *           results was clicked (combined_clicks) divided by combined_queries. See the
     *           help page at https://support.google.com/google-ads/answer/3097241 for
     *           details.
     *     @type int|string $combined_queries
     *           The number of searches that returned pages from your site in the unpaid
     *           results or showed one of your text ads. See the help page at
     *           https://support.google.com/google-ads/answer/3097241 for details.
     *     @type float $content_budget_lost_impression_share
     *           The estimated percent of times that your ad was eligible to show
     *           on the Display Network but didn't because your budget was too low.
     *           Note: Content budget lost impression share is reported in the range of 0
     *           to 0.9. Any value above 0.9 is reported as 0.9001.
     *     @type float $content_impression_share
     *           The impressions you've received on the Display Network divided
     *           by the estimated number of impressions you were eligible to receive.
     *           Note: Content impression share is reported in the range of 0.1 to 1. Any
     *           value below 0.1 is reported as 0.0999.
     *     @type string $conversion_last_received_request_date_time
     *           The last date/time a conversion tag for this conversion action successfully
     *           fired and was seen by Google Ads. This firing event may not have been the
     *           result of an attributable conversion (for example, because the tag was
     *           fired from a browser that did not previously click an ad from an
     *           appropriate advertiser). The date/time is in the customer's time zone.
     *     @type string $conversion_last_conversion_date
     *           The date of the most recent conversion for this conversion action. The date
     *           is in the customer's time zone.
     *     @type float $content_rank_lost_impression_share
     *           The estimated percentage of impressions on the Display Network
     *           that your ads didn't receive due to poor Ad Rank.
     *           Note: Content rank lost impression share is reported in the range of 0
     *           to 0.9. Any value above 0.9 is reported as 0.9001.
     *     @type float $conversions_from_interactions_rate
     *           Conversions from interactions divided by the number of ad interactions
     *           (such as clicks for text ads or views for video ads). This only includes
     *           conversion actions which include_in_conversions_metric attribute is set to
     *           true. If you use conversion-based bidding, your bid strategies will
     *           optimize for these conversions.
     *     @type float $conversions_value
     *           The value of conversions. This only includes conversion actions which
     *           include_in_conversions_metric attribute is set to true. If you use
     *           conversion-based bidding, your bid strategies will optimize for these
     *           conversions.
     *     @type float $conversions_value_by_conversion_date
     *           The value of conversions. This only includes conversion actions which
     *           include_in_conversions_metric attribute is set to true. If you use
     *           conversion-based bidding, your bid strategies will optimize for these
     *           conversions. When this column is selected with date, the values in date
     *           column means the conversion date. Details for the by_conversion_date
     *           columns are available at
     *           https://support.google.com/google-ads/answer/9549009.
     *     @type float $new_customer_lifetime_value
     *           New customers' lifetime conversion value. If you have set up
     *           customer acquisition goal at either account level or campaign level, this
     *           will include the additional conversion value from new customers for
     *           biddable conversions. If your campaign has adopted the customer
     *           acquisition goal and selected "bid higher for new customers", these values
     *           will be included in "conversions_value" for optimization. See
     *           https://support.google.com/google-ads/answer/******** for more details.
     *     @type float $conversions_value_per_cost
     *           The value of conversions divided by the cost of ad interactions. This only
     *           includes conversion actions which include_in_conversions_metric attribute
     *           is set to true. If you use conversion-based bidding, your bid strategies
     *           will optimize for these conversions.
     *     @type float $conversions_from_interactions_value_per_interaction
     *           The value of conversions from interactions divided by the number of ad
     *           interactions. This only includes conversion actions which
     *           include_in_conversions_metric attribute is set to true. If you use
     *           conversion-based bidding, your bid strategies will optimize for these
     *           conversions.
     *     @type float $conversions
     *           The number of conversions. This only includes conversion actions which
     *           include_in_conversions_metric attribute is set to true. If you use
     *           conversion-based bidding, your bid strategies will optimize for these
     *           conversions.
     *     @type float $conversions_by_conversion_date
     *           The number of conversions. This only includes conversion actions which
     *           include_in_conversions_metric attribute is set to true. If you use
     *           conversion-based bidding, your bid strategies will optimize for these
     *           conversions. When this column is selected with date, the values in date
     *           column means the conversion date. Details for the by_conversion_date
     *           columns are available at
     *           https://support.google.com/google-ads/answer/9549009.
     *     @type int|string $cost_micros
     *           The sum of your cost-per-click (CPC) and cost-per-thousand impressions
     *           (CPM) costs during this period.
     *     @type float $cost_per_all_conversions
     *           The cost of ad interactions divided by all conversions.
     *     @type float $cost_per_conversion
     *           The cost of ad interactions divided by conversions. This only includes
     *           conversion actions which include_in_conversions_metric attribute is set to
     *           true. If you use conversion-based bidding, your bid strategies will
     *           optimize for these conversions.
     *     @type float $cost_per_current_model_attributed_conversion
     *           The cost of ad interactions divided by current model attributed
     *           conversions. This only includes conversion actions which
     *           include_in_conversions_metric attribute is set to true. If you use
     *           conversion-based bidding, your bid strategies will optimize for these
     *           conversions.
     *     @type float $cross_device_conversions
     *           Conversions from when a customer clicks on a Google Ads ad on one device,
     *           then converts on a different device or browser.
     *           Cross-device conversions are already included in all_conversions.
     *     @type int|string $cross_device_conversions_value_micros
     *           The sum of the value of cross-device conversions, in micros.
     *     @type float $ctr
     *           The number of clicks your ad receives (Clicks) divided by the number
     *           of times your ad is shown (Impressions).
     *     @type float $current_model_attributed_conversions
     *           Shows how your historic conversions data would look under the attribution
     *           model you've currently selected. This only includes conversion actions
     *           which include_in_conversions_metric attribute is set to true. If you use
     *           conversion-based bidding, your bid strategies will optimize for these
     *           conversions.
     *     @type float $current_model_attributed_conversions_from_interactions_rate
     *           Current model attributed conversions from interactions divided by the
     *           number of ad interactions (such as clicks for text ads or views for video
     *           ads). This only includes conversion actions which
     *           include_in_conversions_metric attribute is set to true. If you use
     *           conversion-based bidding, your bid strategies will optimize for these
     *           conversions.
     *     @type float $current_model_attributed_conversions_from_interactions_value_per_interaction
     *           The value of current model attributed conversions from interactions divided
     *           by the number of ad interactions. This only includes conversion actions
     *           which include_in_conversions_metric attribute is set to true. If you use
     *           conversion-based bidding, your bid strategies will optimize for these
     *           conversions.
     *     @type float $current_model_attributed_conversions_value
     *           The value of current model attributed conversions. This only includes
     *           conversion actions which include_in_conversions_metric attribute is set to
     *           true. If you use conversion-based bidding, your bid strategies will
     *           optimize for these conversions.
     *     @type float $current_model_attributed_conversions_value_per_cost
     *           The value of current model attributed conversions divided by the cost of ad
     *           interactions. This only includes conversion actions which
     *           include_in_conversions_metric attribute is set to true. If you use
     *           conversion-based bidding, your bid strategies will optimize for these
     *           conversions.
     *     @type float $engagement_rate
     *           How often people engage with your ad after it's shown to them. This is the
     *           number of ad expansions divided by the number of times your ad is shown.
     *     @type int|string $engagements
     *           The number of engagements.
     *           An engagement occurs when a viewer expands your Lightbox ad. Also, in the
     *           future, other ad types may support engagement metrics.
     *     @type float $hotel_average_lead_value_micros
     *           Average lead value based on clicks.
     *     @type int|string $hotel_commission_rate_micros
     *           Commission bid rate in micros. A 20% commission is represented as
     *           200,000.
     *     @type float $hotel_expected_commission_cost
     *           Expected commission cost. The result of multiplying the commission value
     *           times the hotel_commission_rate in advertiser currency.
     *     @type float $hotel_price_difference_percentage
     *           The average price difference between the price offered by reporting hotel
     *           advertiser and the cheapest price offered by the competing advertiser.
     *     @type int|string $hotel_eligible_impressions
     *           The number of impressions that hotel partners could have had given their
     *           feed performance.
     *     @type int $historical_creative_quality_score
     *           The creative historical quality score.
     *     @type int $historical_landing_page_quality_score
     *           The quality of historical landing page experience.
     *     @type int|string $historical_quality_score
     *           The historical quality score.
     *     @type int $historical_search_predicted_ctr
     *           The historical search predicted click through rate (CTR).
     *     @type int|string $gmail_forwards
     *           The number of times the ad was forwarded to someone else as a message.
     *     @type int|string $gmail_saves
     *           The number of times someone has saved your Gmail ad to their inbox as a
     *           message.
     *     @type int|string $gmail_secondary_clicks
     *           The number of clicks to the landing page on the expanded state of Gmail
     *           ads.
     *     @type int|string $impressions_from_store_reach
     *           The number of times a store's location-based ad was shown.
     *           This metric applies to feed items only.
     *     @type int|string $impressions
     *           Count of how often your ad has appeared on a search results page or
     *           website on the Google Network.
     *     @type float $interaction_rate
     *           How often people interact with your ad after it is shown to them.
     *           This is the number of interactions divided by the number of times your ad
     *           is shown.
     *     @type int|string $interactions
     *           The number of interactions.
     *           An interaction is the main user action associated with an ad format-clicks
     *           for text and shopping ads, views for video ads, and so on.
     *     @type array<int>|\Google\Protobuf\Internal\RepeatedField $interaction_event_types
     *           The types of payable and free interactions.
     *     @type float $invalid_click_rate
     *           The percentage of clicks filtered out of your total number of clicks
     *           (filtered + non-filtered clicks) during the reporting period.
     *     @type int|string $invalid_clicks
     *           Number of clicks Google considers illegitimate and doesn't charge you for.
     *     @type int|string $message_chats
     *           Number of message chats initiated for Click To Message impressions that
     *           were message tracking eligible.
     *     @type int|string $message_impressions
     *           Number of Click To Message impressions that were message tracking eligible.
     *     @type float $message_chat_rate
     *           Number of message chats initiated (message_chats) divided by the number
     *           of message impressions (message_impressions).
     *           Rate at which a user initiates a message chat from an ad impression with
     *           a messaging option and message tracking enabled.
     *           Note that this rate can be more than 1.0 for a given message impression.
     *     @type float $mobile_friendly_clicks_percentage
     *           The percentage of mobile clicks that go to a mobile-friendly page.
     *     @type float $optimization_score_uplift
     *           Total optimization score uplift of all recommendations.
     *     @type string $optimization_score_url
     *           URL for the optimization score page in the Google Ads web interface.
     *           This metric can be selected from `customer` or `campaign`, and can be
     *           segmented by `segments.recommendation_type`. For example, `SELECT
     *           metrics.optimization_score_url, segments.recommendation_type FROM
     *           customer` will return a URL for each unique (customer, recommendation_type)
     *           combination.
     *     @type int|string $organic_clicks
     *           The number of times someone clicked your site's listing in the unpaid
     *           results for a particular query. See the help page at
     *           https://support.google.com/google-ads/answer/3097241 for details.
     *     @type float $organic_clicks_per_query
     *           The number of times someone clicked your site's listing in the unpaid
     *           results (organic_clicks) divided by the total number of searches that
     *           returned pages from your site (organic_queries). See the help page at
     *           https://support.google.com/google-ads/answer/3097241 for details.
     *     @type int|string $organic_impressions
     *           The number of listings for your site in the unpaid search results. See the
     *           help page at https://support.google.com/google-ads/answer/3097241 for
     *           details.
     *     @type float $organic_impressions_per_query
     *           The number of times a page from your site was listed in the unpaid search
     *           results (organic_impressions) divided by the number of searches returning
     *           your site's listing in the unpaid results (organic_queries). See the help
     *           page at https://support.google.com/google-ads/answer/3097241 for details.
     *     @type int|string $organic_queries
     *           The total number of searches that returned your site's listing in the
     *           unpaid results. See the help page at
     *           https://support.google.com/google-ads/answer/3097241 for details.
     *     @type float $percent_new_visitors
     *           Percentage of first-time sessions (from people who had never visited your
     *           site before). Imported from Google Analytics.
     *     @type int|string $phone_calls
     *           Number of offline phone calls.
     *     @type int|string $phone_impressions
     *           Number of offline phone impressions.
     *     @type float $phone_through_rate
     *           Number of phone calls received (phone_calls) divided by the number of
     *           times your phone number is shown (phone_impressions).
     *     @type float $relative_ctr
     *           Your clickthrough rate (Ctr) divided by the average clickthrough rate of
     *           all advertisers on the websites that show your ads. Measures how your ads
     *           perform on Display Network sites compared to other ads on the same sites.
     *     @type float $search_absolute_top_impression_share
     *           The percentage of the customer's Shopping or Search ad impressions that are
     *           shown in the most prominent Shopping position. See
     *           https://support.google.com/google-ads/answer/7501826
     *           for details. Any value below 0.1 is reported as 0.0999.
     *     @type float $search_budget_lost_absolute_top_impression_share
     *           The number estimating how often your ad wasn't the very first ad above the
     *           organic search results due to a low budget. Note: Search
     *           budget lost absolute top impression share is reported in the range of 0 to
     *           0.9. Any value above 0.9 is reported as 0.9001.
     *     @type float $search_budget_lost_impression_share
     *           The estimated percent of times that your ad was eligible to show on the
     *           Search Network but didn't because your budget was too low. Note: Search
     *           budget lost impression share is reported in the range of 0 to 0.9. Any
     *           value above 0.9 is reported as 0.9001.
     *     @type float $search_budget_lost_top_impression_share
     *           The number estimating how often your ad didn't show anywhere above the
     *           organic search results due to a low budget. Note: Search
     *           budget lost top impression share is reported in the range of 0 to 0.9. Any
     *           value above 0.9 is reported as 0.9001.
     *     @type float $search_click_share
     *           The number of clicks you've received on the Search Network
     *           divided by the estimated number of clicks you were eligible to receive.
     *           Note: Search click share is reported in the range of 0.1 to 1. Any value
     *           below 0.1 is reported as 0.0999.
     *     @type float $search_exact_match_impression_share
     *           The impressions you've received divided by the estimated number of
     *           impressions you were eligible to receive on the Search Network for search
     *           terms that matched your keywords exactly (or were close variants of your
     *           keyword), regardless of your keyword match types. Note: Search exact match
     *           impression share is reported in the range of 0.1 to 1. Any value below 0.1
     *           is reported as 0.0999.
     *     @type float $search_impression_share
     *           The impressions you've received on the Search Network divided
     *           by the estimated number of impressions you were eligible to receive.
     *           Note: Search impression share is reported in the range of 0.1 to 1. Any
     *           value below 0.1 is reported as 0.0999.
     *     @type float $search_rank_lost_absolute_top_impression_share
     *           The number estimating how often your ad wasn't the very first ad above the
     *           organic search results due to poor Ad Rank.
     *           Note: Search rank lost absolute top impression share is reported in the
     *           range of 0 to 0.9. Any value above 0.9 is reported as 0.9001.
     *     @type float $search_rank_lost_impression_share
     *           The estimated percentage of impressions on the Search Network
     *           that your ads didn't receive due to poor Ad Rank.
     *           Note: Search rank lost impression share is reported in the range of 0 to
     *           0.9. Any value above 0.9 is reported as 0.9001.
     *     @type float $search_rank_lost_top_impression_share
     *           The number estimating how often your ad didn't show anywhere above the
     *           organic search results due to poor Ad Rank.
     *           Note: Search rank lost top impression share is reported in the range of 0
     *           to 0.9. Any value above 0.9 is reported as 0.9001.
     *     @type float $search_top_impression_share
     *           The impressions you've received in the top location (anywhere above the
     *           organic search results) compared to the estimated number of impressions you
     *           were eligible to receive in the top location.
     *           Note: Search top impression share is reported in the range of 0.1 to 1. Any
     *           value below 0.1 is reported as 0.0999.
     *     @type \Google\Ads\GoogleAds\V15\Common\SearchVolumeRange $search_volume
     *           Search volume range for a search term insight category.
     *     @type int|string $speed_score
     *           A measure of how quickly your page loads after clicks on your mobile ads.
     *           The score is a range from 1 to 10, 10 being the fastest.
     *     @type int|string $average_target_cpa_micros
     *           The average Target CPA, or unset if not available (for example, for
     *           campaigns that had traffic from portfolio bidding strategies or non-tCPA).
     *     @type float $average_target_roas
     *           The average Target ROAS, or unset if not available (for example, for
     *           campaigns that had traffic from portfolio bidding strategies or non-tROAS).
     *     @type float $top_impression_percentage
     *           The percent of your ad impressions that are shown anywhere above the
     *           organic search results.
     *     @type float $valid_accelerated_mobile_pages_clicks_percentage
     *           The percentage of ad clicks to Accelerated Mobile Pages (AMP) landing pages
     *           that reach a valid AMP page.
     *     @type float $value_per_all_conversions
     *           The value of all conversions divided by the number of all conversions.
     *     @type float $value_per_all_conversions_by_conversion_date
     *           The value of all conversions divided by the number of all conversions. When
     *           this column is selected with date, the values in date column means the
     *           conversion date. Details for the by_conversion_date columns are available
     *           at https://support.google.com/google-ads/answer/9549009.
     *     @type float $value_per_conversion
     *           The value of conversions divided by the number of conversions. This only
     *           includes conversion actions which include_in_conversions_metric attribute
     *           is set to true. If you use conversion-based bidding, your bid strategies
     *           will optimize for these conversions.
     *     @type float $value_per_conversions_by_conversion_date
     *           The value of conversions divided by the number of conversions. This only
     *           includes conversion actions which include_in_conversions_metric attribute
     *           is set to true. If you use conversion-based bidding, your bid strategies
     *           will optimize for these conversions. When this column is selected with
     *           date, the values in date column means the conversion date. Details for the
     *           by_conversion_date columns are available at
     *           https://support.google.com/google-ads/answer/9549009.
     *     @type float $value_per_current_model_attributed_conversion
     *           The value of current model attributed conversions divided by the number of
     *           the conversions. This only includes conversion actions which
     *           include_in_conversions_metric attribute is set to true. If you use
     *           conversion-based bidding, your bid strategies will optimize for these
     *           conversions.
     *     @type float $video_quartile_p100_rate
     *           Percentage of impressions where the viewer watched all of your video.
     *     @type float $video_quartile_p25_rate
     *           Percentage of impressions where the viewer watched 25% of your video.
     *     @type float $video_quartile_p50_rate
     *           Percentage of impressions where the viewer watched 50% of your video.
     *     @type float $video_quartile_p75_rate
     *           Percentage of impressions where the viewer watched 75% of your video.
     *     @type float $video_view_rate
     *           The number of views your TrueView video ad receives divided by its number
     *           of impressions, including thumbnail impressions for TrueView in-display
     *           ads.
     *     @type int|string $video_views
     *           The number of times your video ads were viewed.
     *     @type int|string $view_through_conversions
     *           The total number of view-through conversions.
     *           These happen when a customer sees an image or rich media ad, then later
     *           completes a conversion on your site without interacting with (for example,
     *           clicking on) another ad.
     *     @type int|string $sk_ad_network_installs
     *           The number of iOS Store Kit Ad Network conversions.
     *     @type int|string $sk_ad_network_total_conversions
     *           The total number of iOS Store Kit Ad Network conversions.
     *     @type int|string $publisher_purchased_clicks
     *           Clicks from properties not owned by the publisher for which the traffic
     *           the publisher has paid for or acquired through incentivized activity
     *     @type int|string $publisher_organic_clicks
     *           Clicks from properties for which the traffic the publisher has not paid
     *           for or acquired through incentivized activity
     *     @type int|string $publisher_unknown_clicks
     *           Clicks from traffic which is not identified as "Publisher Purchased" or
     *           "Publisher Organic"
     *     @type float $all_conversions_from_location_asset_click_to_call
     *           Number of call button clicks on any location surface after a chargeable ad
     *           event (click or impression). This measure is coming from Asset based
     *           location.
     *     @type float $all_conversions_from_location_asset_directions
     *           Number of driving directions clicks on any location surface after a
     *           chargeable ad event (click or impression). This measure is coming
     *           from Asset based location.
     *     @type float $all_conversions_from_location_asset_menu
     *           Number of menu link clicks on any location surface after a chargeable ad
     *           event (click or impression). This measure is coming from Asset based
     *           location.
     *     @type float $all_conversions_from_location_asset_order
     *           Number of order clicks on any location surface after a chargeable ad event
     *           (click or impression). This measure is coming from Asset based
     *           location.
     *     @type float $all_conversions_from_location_asset_other_engagement
     *           Number of other types of local action clicks on any location surface after
     *           a chargeable ad event (click or impression). This measure is coming
     *           from Asset based location.
     *     @type float $all_conversions_from_location_asset_store_visits
     *           Estimated number of visits to the store after a chargeable
     *           ad event (click or impression). This measure is coming from Asset
     *           based location.
     *     @type float $all_conversions_from_location_asset_website
     *           Number of website URL clicks on any location surface after a chargeable ad
     *           event (click or impression). This measure is coming from Asset based
     *           location.
     *     @type int|string $eligible_impressions_from_location_asset_store_reach
     *           Number of impressions in which the store location was shown or the location
     *           was used for targeting. This measure is coming from Asset based
     *           location.
     *     @type float $view_through_conversions_from_location_asset_click_to_call
     *           Number of call button clicks on any location surface after an impression.
     *           This measure is coming from Asset based location.
     *     @type float $view_through_conversions_from_location_asset_directions
     *           Number of driving directions clicks on any location surface after an
     *           impression. This measure is coming from Asset based location.
     *     @type float $view_through_conversions_from_location_asset_menu
     *           Number of menu link clicks on any location surface after an impression.
     *           This measure is coming from Asset based location.
     *     @type float $view_through_conversions_from_location_asset_order
     *           Number of order clicks on any location surface after an impression. This
     *           measure is coming from Asset based location.
     *     @type float $view_through_conversions_from_location_asset_other_engagement
     *           Number of other types of local action clicks on any location surface after
     *           an impression. This measure is coming from Asset based location.
     *     @type float $view_through_conversions_from_location_asset_store_visits
     *           Estimated number of visits to the store after an impression.
     *           This measure is coming from Asset based location.
     *     @type float $view_through_conversions_from_location_asset_website
     *           Number of website URL clicks on any location surface after an impression.
     *           This measure is coming from Asset based location.
     *     @type float $orders
     *           Orders is the total number of purchase conversions you received attributed
     *           to your ads.
     *           How it works: You report conversions with cart data for
     *           completed purchases on your website. If a conversion is attributed to
     *           previous interactions with your ads (clicks for text or Shopping ads, views
     *           for video ads etc.) it's counted as an order.
     *           Example: Someone clicked on a Shopping ad for a hat then bought the same
     *           hat and a shirt in an order on your website. Even though they bought 2
     *           products, this would count as 1 order.
     *           This metric is only available if you report conversions with cart data.
     *     @type int|string $average_order_value_micros
     *           Average order value is the average revenue you made per order attributed to
     *           your ads.
     *           How it works: You report conversions with cart data for completed purchases
     *           on your website. Average order value is the total revenue from your orders
     *           divided by the total number of orders.
     *           Example: You received 3 orders which made $10, $15 and $20 worth of
     *           revenue. The average order value is $15 = ($10 + $15 + $20)/3.
     *           This metric is only available if you report conversions with cart data.
     *     @type float $average_cart_size
     *           Average cart size is the average number of products in each order
     *           attributed to your ads.
     *           How it works: You report conversions with cart data for completed purchases
     *           on your website. Average cart size is the total number of products sold
     *           divided by the total number of orders you received.
     *           Example: You received 2 orders, the first included 3 products and the
     *           second included 2. The average cart size is 2.5 products = (3+2)/2.
     *           This metric is only available if you report conversions with cart data.
     *     @type int|string $cost_of_goods_sold_micros
     *           Cost of goods sold (COGS) is the total cost of the products you sold in
     *           orders attributed to your ads.
     *           How it works: You can add a cost of goods sold value to every product in
     *           Merchant Center. If you report conversions with cart data, the products you
     *           sold are matched with their cost of goods sold value and this can be used
     *           to calculate the gross profit you made on each order.
     *           Example: Someone clicked on a Shopping ad for a hat then bought the same
     *           hat and a shirt. The hat has a cost of goods sold value of $3, the shirt
     *           has a cost of goods sold value of $5. The cost of goods sold for this order
     *           is $8 = $3 + $5.
     *           This metric is only available if you report conversions with cart data.
     *     @type int|string $gross_profit_micros
     *           Gross profit is the profit you made from orders attributed to your ads
     *           minus the cost of goods sold (COGS).
     *           How it works: Gross profit is the revenue you made from sales attributed to
     *           your ads minus cost of goods sold. Gross profit calculations only include
     *           products that have a cost of goods sold value in Merchant Center.
     *           Example: Someone clicked on a Shopping ad for a hat then bought the same
     *           hat and a shirt in an order from your website. The hat is priced $10 and
     *           the shirt is priced $20. The hat has a cost of goods sold value of $3, but
     *           the shirt has no cost of goods sold value. Gross profit for this order will
     *           only take into account the hat, so it's $7 = $10 - $3.
     *           This metric is only available if you report conversions with cart data.
     *     @type float $gross_profit_margin
     *           Gross profit margin is the percentage gross profit you made from orders
     *           attributed to your ads, after taking out the cost of goods sold (COGS).
     *           How it works: You report conversions with cart data for completed purchases
     *           on your website. Gross profit margin is the gross profit you made divided
     *           by your total revenue and multiplied by 100%. Gross profit margin
     *           calculations only include products that have a cost of goods sold value in
     *           Merchant Center.
     *           Example: Someone bought a hat and a shirt in an order on your website. The
     *           hat is priced $10 and has a cost of goods sold value of $3. The shirt is
     *           priced $20 but has no cost of goods sold value. Gross profit margin for
     *           this order will only take into account the hat because it has a cost of
     *           goods sold value, so it's 70% = ($10 - $3)/$10 x 100%.
     *           This metric is only available if you report conversions with cart data.
     *     @type int|string $revenue_micros
     *           Revenue is the total amount you made from orders attributed to your ads.
     *           How it works: You report conversions with cart data for completed purchases
     *           on your website. Revenue is the total value of all the orders you received
     *           attributed to your ads, minus any discount.
     *           Example: Someone clicked on a Shopping ad  for a hat then bought the same
     *           hat and a shirt in an order from your website. The hat is priced $10 and
     *           the shirt is priced $20. The entire order has a $5 discount. The revenue
     *           from this order is $25 = ($10 + $20) - $5.
     *           This metric is only available if you report conversions with cart data.
     *     @type float $units_sold
     *           Units sold is the total number of products sold from orders attributed to
     *           your ads.
     *           How it works: You report conversions with cart data for completed purchases
     *           on your website. Units sold is the total number of products sold from all
     *           orders attributed to your ads.
     *           Example: Someone clicked on a Shopping ad for a hat then bought the same
     *           hat, a shirt and a jacket. The units sold in this order is 3.
     *           This metric is only available if you report conversions with cart data.
     *     @type int|string $cross_sell_cost_of_goods_sold_micros
     *           Cross-sell cost of goods sold (COGS) is the total cost of products sold as
     *           a result of advertising a different product.
     *           How it works: You report conversions with cart data for completed purchases
     *           on your website. If the ad that was interacted with before the purchase has
     *           an associated product (see Shopping Ads) then this product is considered
     *           the advertised product. Any product included in the order the customer
     *           places is a sold product. If these products don't match then this is
     *           considered cross-sell. Cross-sell cost of goods sold is the total cost of
     *           the products sold that weren't advertised.
     *           Example: Someone clicked on a Shopping ad for a hat then bought the same
     *           hat and a shirt. The hat has a cost of goods sold value of $3, the shirt
     *           has a cost of goods sold value of $5. The cross-sell cost of goods sold for
     *           this order is $5.
     *           This metric is only available if you report conversions with cart data.
     *     @type int|string $cross_sell_gross_profit_micros
     *           Cross-sell gross profit is the profit you made from products sold as a
     *           result of advertising a different product, minus cost of goods sold (COGS).
     *           How it works: You report conversions with cart data for completed purchases
     *           on your website. If the ad that was interacted with before the purchase has
     *           an associated product (see Shopping Ads) then this product is considered
     *           the advertised product. Any product included in the purchase is a sold
     *           product. If these products don't match then this is considered cross-sell.
     *           Cross-sell gross profit is the revenue you made from cross-sell attributed
     *           to your ads minus the cost of the goods sold.
     *           Example: Someone clicked on a Shopping ad for a hat then bought the same
     *           hat and a shirt. The shirt is priced $20 and has a cost of goods sold value
     *           of $5. The cross-sell gross profit of this order is $15 = $20 - $5.
     *           This metric is only available if you report conversions with cart data.
     *     @type int|string $cross_sell_revenue_micros
     *           Cross-sell revenue is the total amount you made from products sold as a
     *           result of advertising a different product.
     *           How it works: You report conversions with cart data for completed purchases
     *           on your website. If the ad that was interacted with before the purchase has
     *           an associated product (see Shopping Ads) then this product is considered
     *           the advertised product. Any product included in the order the customer
     *           places is a sold product. If these products don't match then this is
     *           considered cross-sell. Cross-sell revenue is the total value you made from
     *           cross-sell attributed to your ads.
     *           Example: Someone clicked on a Shopping ad for a hat then bought the same
     *           hat and a shirt. The hat is priced $10 and the shirt is priced $20. The
     *           cross-sell revenue of this order is $20.
     *           This metric is only available if you report conversions with cart data.
     *     @type float $cross_sell_units_sold
     *           Cross-sell units sold is the total number of products sold as a result of
     *           advertising a different product.
     *           How it works: You report conversions with cart data for completed purchases
     *           on your website. If the ad that was interacted with before the purchase has
     *           an associated product (see Shopping Ads) then this product is considered
     *           the advertised product. Any product included in the order the customer
     *           places is a sold product. If these products don't match then this is
     *           considered cross-sell. Cross-sell units sold is the total number of
     *           cross-sold products from all orders attributed to your ads.
     *           Example: Someone clicked on a Shopping ad for a hat then bought the same
     *           hat, a shirt and a jacket. The cross-sell units sold in this order is 2.
     *           This metric is only available if you report conversions with cart data.
     *     @type int|string $lead_cost_of_goods_sold_micros
     *           Lead cost of goods sold (COGS) is the total cost of products sold as a
     *           result of advertising the same product.
     *           How it works: You report conversions with cart data for completed purchases
     *           on your website. If the ad that was interacted with has an associated
     *           product (see Shopping Ads) then this product is considered the advertised
     *           product. Any product included in the order the customer places is a sold
     *           product. If the advertised and sold products match, then the cost of these
     *           goods is counted under lead cost of goods sold.
     *           Example: Someone clicked on a Shopping ad for a hat then bought the same
     *           hat and a shirt. The hat has a cost of goods sold value of $3, the shirt
     *           has a cost of goods sold value of $5. The lead cost of goods sold for this
     *           order is $3.
     *           This metric is only available if you report conversions with cart data.
     *     @type int|string $lead_gross_profit_micros
     *           Lead gross profit is the profit you made from products sold as a result of
     *           advertising the same product, minus cost of goods sold (COGS).
     *           How it works: You report conversions with cart data for completed purchases
     *           on your website. If the ad that was interacted with before the purchase has
     *           an associated product (see Shopping Ads) then this product is considered
     *           the advertised product. Any product included in the order the customer
     *           places is a sold product. If the advertised and sold products match, then
     *           the revenue you made from these sales minus the cost of goods sold is your
     *           lead gross profit.
     *           Example: Someone clicked on a Shopping ad for a hat then bought the same
     *           hat and a shirt. The hat is priced $10 and has a cost of goods sold value
     *           of $3. The lead gross profit of this order is $7 = $10 - $3.
     *           This metric is only available if you report conversions with cart data.
     *     @type int|string $lead_revenue_micros
     *           Lead revenue is the total amount you made from products sold as a result of
     *           advertising the same product.
     *           How it works: You report conversions with cart data for completed purchases
     *           on your website. If the ad that was interacted with before the purchase has
     *           an associated product (see Shopping Ads) then this product is considered
     *           the advertised product. Any product included in the order the customer
     *           places is a sold product. If the advertised and sold products match, then
     *           the total value you made from the sales of these products is shown under
     *           lead revenue.
     *           Example: Someone clicked on a Shopping ad for a hat then bought the same
     *           hat and a shirt. The hat is priced $10 and the shirt is priced $20. The
     *           lead revenue of this order is $10.
     *           This metric is only available if you report conversions with cart data.
     *     @type float $lead_units_sold
     *           Lead units sold is the total number of products sold as a result of
     *           advertising the same product.
     *           How it works: You report conversions with cart data for completed purchases
     *           on your website. If the ad that was interacted with before the purchase has
     *           an associated product (see Shopping Ads) then this product is considered
     *           the advertised product. Any product included in the order the customer
     *           places is a sold product. If the advertised and sold products match, then
     *           the total number of these products sold is shown under lead units sold.
     *           Example: Someone clicked on a Shopping ad for a hat then bought the same
     *           hat, a shirt and a jacket. The lead units sold in this order is 1.
     *           This metric is only available if you report conversions with cart data.
     *     @type int|string $unique_users
     *           The number of unique users who saw your ad during the requested time
     *           period. This metric cannot be aggregated, and can only be requested for
     *           date ranges of 92 days or less. This metric is available for following
     *           campaign types - Display, Video, Discovery and App.
     *     @type float $average_impression_frequency_per_user
     *           The average number of times a unique user saw your ad during the requested
     *           time period. This metric cannot be aggregated, and can only be requested
     *           for date ranges of 92 days or less. This metric is available for following
     *           campaign types - Display, Video, Discovery and App.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Ads\GoogleAds\V15\Common\Metrics::initOnce();
        parent::__construct($data);
    }

    /**
     * The percent of your ad impressions that are shown as the very first ad
     * above the organic search results.
     *
     * Generated from protobuf field <code>optional double absolute_top_impression_percentage = 183;</code>
     * @return float
     */
    public function getAbsoluteTopImpressionPercentage()
    {
        return isset($this->absolute_top_impression_percentage) ? $this->absolute_top_impression_percentage : 0.0;
    }

    public function hasAbsoluteTopImpressionPercentage()
    {
        return isset($this->absolute_top_impression_percentage);
    }

    public function clearAbsoluteTopImpressionPercentage()
    {
        unset($this->absolute_top_impression_percentage);
    }

    /**
     * The percent of your ad impressions that are shown as the very first ad
     * above the organic search results.
     *
     * Generated from protobuf field <code>optional double absolute_top_impression_percentage = 183;</code>
     * @param float $var
     * @return $this
     */
    public function setAbsoluteTopImpressionPercentage($var)
    {
        GPBUtil::checkDouble($var);
        $this->absolute_top_impression_percentage = $var;

        return $this;
    }

    /**
     * Average cost of viewable impressions (`active_view_impressions`).
     *
     * Generated from protobuf field <code>optional double active_view_cpm = 184;</code>
     * @return float
     */
    public function getActiveViewCpm()
    {
        return isset($this->active_view_cpm) ? $this->active_view_cpm : 0.0;
    }

    public function hasActiveViewCpm()
    {
        return isset($this->active_view_cpm);
    }

    public function clearActiveViewCpm()
    {
        unset($this->active_view_cpm);
    }

    /**
     * Average cost of viewable impressions (`active_view_impressions`).
     *
     * Generated from protobuf field <code>optional double active_view_cpm = 184;</code>
     * @param float $var
     * @return $this
     */
    public function setActiveViewCpm($var)
    {
        GPBUtil::checkDouble($var);
        $this->active_view_cpm = $var;

        return $this;
    }

    /**
     * Active view measurable clicks divided by active view viewable impressions.
     * This metric is reported only for the Display Network.
     *
     * Generated from protobuf field <code>optional double active_view_ctr = 185;</code>
     * @return float
     */
    public function getActiveViewCtr()
    {
        return isset($this->active_view_ctr) ? $this->active_view_ctr : 0.0;
    }

    public function hasActiveViewCtr()
    {
        return isset($this->active_view_ctr);
    }

    public function clearActiveViewCtr()
    {
        unset($this->active_view_ctr);
    }

    /**
     * Active view measurable clicks divided by active view viewable impressions.
     * This metric is reported only for the Display Network.
     *
     * Generated from protobuf field <code>optional double active_view_ctr = 185;</code>
     * @param float $var
     * @return $this
     */
    public function setActiveViewCtr($var)
    {
        GPBUtil::checkDouble($var);
        $this->active_view_ctr = $var;

        return $this;
    }

    /**
     * A measurement of how often your ad has become viewable on a Display
     * Network site.
     *
     * Generated from protobuf field <code>optional int64 active_view_impressions = 186;</code>
     * @return int|string
     */
    public function getActiveViewImpressions()
    {
        return isset($this->active_view_impressions) ? $this->active_view_impressions : 0;
    }

    public function hasActiveViewImpressions()
    {
        return isset($this->active_view_impressions);
    }

    public function clearActiveViewImpressions()
    {
        unset($this->active_view_impressions);
    }

    /**
     * A measurement of how often your ad has become viewable on a Display
     * Network site.
     *
     * Generated from protobuf field <code>optional int64 active_view_impressions = 186;</code>
     * @param int|string $var
     * @return $this
     */
    public function setActiveViewImpressions($var)
    {
        GPBUtil::checkInt64($var);
        $this->active_view_impressions = $var;

        return $this;
    }

    /**
     * The ratio of impressions that could be measured by Active View over the
     * number of served impressions.
     *
     * Generated from protobuf field <code>optional double active_view_measurability = 187;</code>
     * @return float
     */
    public function getActiveViewMeasurability()
    {
        return isset($this->active_view_measurability) ? $this->active_view_measurability : 0.0;
    }

    public function hasActiveViewMeasurability()
    {
        return isset($this->active_view_measurability);
    }

    public function clearActiveViewMeasurability()
    {
        unset($this->active_view_measurability);
    }

    /**
     * The ratio of impressions that could be measured by Active View over the
     * number of served impressions.
     *
     * Generated from protobuf field <code>optional double active_view_measurability = 187;</code>
     * @param float $var
     * @return $this
     */
    public function setActiveViewMeasurability($var)
    {
        GPBUtil::checkDouble($var);
        $this->active_view_measurability = $var;

        return $this;
    }

    /**
     * The cost of the impressions you received that were measurable by Active
     * View.
     *
     * Generated from protobuf field <code>optional int64 active_view_measurable_cost_micros = 188;</code>
     * @return int|string
     */
    public function getActiveViewMeasurableCostMicros()
    {
        return isset($this->active_view_measurable_cost_micros) ? $this->active_view_measurable_cost_micros : 0;
    }

    public function hasActiveViewMeasurableCostMicros()
    {
        return isset($this->active_view_measurable_cost_micros);
    }

    public function clearActiveViewMeasurableCostMicros()
    {
        unset($this->active_view_measurable_cost_micros);
    }

    /**
     * The cost of the impressions you received that were measurable by Active
     * View.
     *
     * Generated from protobuf field <code>optional int64 active_view_measurable_cost_micros = 188;</code>
     * @param int|string $var
     * @return $this
     */
    public function setActiveViewMeasurableCostMicros($var)
    {
        GPBUtil::checkInt64($var);
        $this->active_view_measurable_cost_micros = $var;

        return $this;
    }

    /**
     * The number of times your ads are appearing on placements in positions
     * where they can be seen.
     *
     * Generated from protobuf field <code>optional int64 active_view_measurable_impressions = 189;</code>
     * @return int|string
     */
    public function getActiveViewMeasurableImpressions()
    {
        return isset($this->active_view_measurable_impressions) ? $this->active_view_measurable_impressions : 0;
    }

    public function hasActiveViewMeasurableImpressions()
    {
        return isset($this->active_view_measurable_impressions);
    }

    public function clearActiveViewMeasurableImpressions()
    {
        unset($this->active_view_measurable_impressions);
    }

    /**
     * The number of times your ads are appearing on placements in positions
     * where they can be seen.
     *
     * Generated from protobuf field <code>optional int64 active_view_measurable_impressions = 189;</code>
     * @param int|string $var
     * @return $this
     */
    public function setActiveViewMeasurableImpressions($var)
    {
        GPBUtil::checkInt64($var);
        $this->active_view_measurable_impressions = $var;

        return $this;
    }

    /**
     * The percentage of time when your ad appeared on an Active View enabled site
     * (measurable impressions) and was viewable (viewable impressions).
     *
     * Generated from protobuf field <code>optional double active_view_viewability = 190;</code>
     * @return float
     */
    public function getActiveViewViewability()
    {
        return isset($this->active_view_viewability) ? $this->active_view_viewability : 0.0;
    }

    public function hasActiveViewViewability()
    {
        return isset($this->active_view_viewability);
    }

    public function clearActiveViewViewability()
    {
        unset($this->active_view_viewability);
    }

    /**
     * The percentage of time when your ad appeared on an Active View enabled site
     * (measurable impressions) and was viewable (viewable impressions).
     *
     * Generated from protobuf field <code>optional double active_view_viewability = 190;</code>
     * @param float $var
     * @return $this
     */
    public function setActiveViewViewability($var)
    {
        GPBUtil::checkDouble($var);
        $this->active_view_viewability = $var;

        return $this;
    }

    /**
     * All conversions from interactions (as oppose to view through conversions)
     * divided by the number of ad interactions.
     *
     * Generated from protobuf field <code>optional double all_conversions_from_interactions_rate = 191;</code>
     * @return float
     */
    public function getAllConversionsFromInteractionsRate()
    {
        return isset($this->all_conversions_from_interactions_rate) ? $this->all_conversions_from_interactions_rate : 0.0;
    }

    public function hasAllConversionsFromInteractionsRate()
    {
        return isset($this->all_conversions_from_interactions_rate);
    }

    public function clearAllConversionsFromInteractionsRate()
    {
        unset($this->all_conversions_from_interactions_rate);
    }

    /**
     * All conversions from interactions (as oppose to view through conversions)
     * divided by the number of ad interactions.
     *
     * Generated from protobuf field <code>optional double all_conversions_from_interactions_rate = 191;</code>
     * @param float $var
     * @return $this
     */
    public function setAllConversionsFromInteractionsRate($var)
    {
        GPBUtil::checkDouble($var);
        $this->all_conversions_from_interactions_rate = $var;

        return $this;
    }

    /**
     * The value of all conversions.
     *
     * Generated from protobuf field <code>optional double all_conversions_value = 192;</code>
     * @return float
     */
    public function getAllConversionsValue()
    {
        return isset($this->all_conversions_value) ? $this->all_conversions_value : 0.0;
    }

    public function hasAllConversionsValue()
    {
        return isset($this->all_conversions_value);
    }

    public function clearAllConversionsValue()
    {
        unset($this->all_conversions_value);
    }

    /**
     * The value of all conversions.
     *
     * Generated from protobuf field <code>optional double all_conversions_value = 192;</code>
     * @param float $var
     * @return $this
     */
    public function setAllConversionsValue($var)
    {
        GPBUtil::checkDouble($var);
        $this->all_conversions_value = $var;

        return $this;
    }

    /**
     * The value of all conversions. When this column is selected with date, the
     * values in date column means the conversion date. Details for the
     * by_conversion_date columns are available at
     * https://support.google.com/google-ads/answer/9549009.
     *
     * Generated from protobuf field <code>double all_conversions_value_by_conversion_date = 240;</code>
     * @return float
     */
    public function getAllConversionsValueByConversionDate()
    {
        return $this->all_conversions_value_by_conversion_date;
    }

    /**
     * The value of all conversions. When this column is selected with date, the
     * values in date column means the conversion date. Details for the
     * by_conversion_date columns are available at
     * https://support.google.com/google-ads/answer/9549009.
     *
     * Generated from protobuf field <code>double all_conversions_value_by_conversion_date = 240;</code>
     * @param float $var
     * @return $this
     */
    public function setAllConversionsValueByConversionDate($var)
    {
        GPBUtil::checkDouble($var);
        $this->all_conversions_value_by_conversion_date = $var;

        return $this;
    }

    /**
     * All of new customers' lifetime conversion value. If you have set up
     * customer acquisition goal at either account level or campaign level, this
     * will include the additional conversion value from new customers for both
     * biddable and non-biddable conversions. If your campaign has adopted the
     * customer acquisition goal and selected "bid higher for new customers",
     * these values will be included in "all_conversions_value". See
     * https://support.google.com/google-ads/answer/******** for more details.
     *
     * Generated from protobuf field <code>optional double all_new_customer_lifetime_value = 294;</code>
     * @return float
     */
    public function getAllNewCustomerLifetimeValue()
    {
        return isset($this->all_new_customer_lifetime_value) ? $this->all_new_customer_lifetime_value : 0.0;
    }

    public function hasAllNewCustomerLifetimeValue()
    {
        return isset($this->all_new_customer_lifetime_value);
    }

    public function clearAllNewCustomerLifetimeValue()
    {
        unset($this->all_new_customer_lifetime_value);
    }

    /**
     * All of new customers' lifetime conversion value. If you have set up
     * customer acquisition goal at either account level or campaign level, this
     * will include the additional conversion value from new customers for both
     * biddable and non-biddable conversions. If your campaign has adopted the
     * customer acquisition goal and selected "bid higher for new customers",
     * these values will be included in "all_conversions_value". See
     * https://support.google.com/google-ads/answer/******** for more details.
     *
     * Generated from protobuf field <code>optional double all_new_customer_lifetime_value = 294;</code>
     * @param float $var
     * @return $this
     */
    public function setAllNewCustomerLifetimeValue($var)
    {
        GPBUtil::checkDouble($var);
        $this->all_new_customer_lifetime_value = $var;

        return $this;
    }

    /**
     * The total number of conversions. This includes all conversions regardless
     * of the value of include_in_conversions_metric.
     *
     * Generated from protobuf field <code>optional double all_conversions = 193;</code>
     * @return float
     */
    public function getAllConversions()
    {
        return isset($this->all_conversions) ? $this->all_conversions : 0.0;
    }

    public function hasAllConversions()
    {
        return isset($this->all_conversions);
    }

    public function clearAllConversions()
    {
        unset($this->all_conversions);
    }

    /**
     * The total number of conversions. This includes all conversions regardless
     * of the value of include_in_conversions_metric.
     *
     * Generated from protobuf field <code>optional double all_conversions = 193;</code>
     * @param float $var
     * @return $this
     */
    public function setAllConversions($var)
    {
        GPBUtil::checkDouble($var);
        $this->all_conversions = $var;

        return $this;
    }

    /**
     * The total number of conversions. This includes all conversions regardless
     * of the value of include_in_conversions_metric. When this column is selected
     * with date, the values in date column means the conversion date. Details for
     * the by_conversion_date columns are available at
     * https://support.google.com/google-ads/answer/9549009.
     *
     * Generated from protobuf field <code>double all_conversions_by_conversion_date = 241;</code>
     * @return float
     */
    public function getAllConversionsByConversionDate()
    {
        return $this->all_conversions_by_conversion_date;
    }

    /**
     * The total number of conversions. This includes all conversions regardless
     * of the value of include_in_conversions_metric. When this column is selected
     * with date, the values in date column means the conversion date. Details for
     * the by_conversion_date columns are available at
     * https://support.google.com/google-ads/answer/9549009.
     *
     * Generated from protobuf field <code>double all_conversions_by_conversion_date = 241;</code>
     * @param float $var
     * @return $this
     */
    public function setAllConversionsByConversionDate($var)
    {
        GPBUtil::checkDouble($var);
        $this->all_conversions_by_conversion_date = $var;

        return $this;
    }

    /**
     * The value of all conversions divided by the total cost of ad interactions
     * (such as clicks for text ads or views for video ads).
     *
     * Generated from protobuf field <code>optional double all_conversions_value_per_cost = 194;</code>
     * @return float
     */
    public function getAllConversionsValuePerCost()
    {
        return isset($this->all_conversions_value_per_cost) ? $this->all_conversions_value_per_cost : 0.0;
    }

    public function hasAllConversionsValuePerCost()
    {
        return isset($this->all_conversions_value_per_cost);
    }

    public function clearAllConversionsValuePerCost()
    {
        unset($this->all_conversions_value_per_cost);
    }

    /**
     * The value of all conversions divided by the total cost of ad interactions
     * (such as clicks for text ads or views for video ads).
     *
     * Generated from protobuf field <code>optional double all_conversions_value_per_cost = 194;</code>
     * @param float $var
     * @return $this
     */
    public function setAllConversionsValuePerCost($var)
    {
        GPBUtil::checkDouble($var);
        $this->all_conversions_value_per_cost = $var;

        return $this;
    }

    /**
     * The number of times people clicked the "Call" button to call a store during
     * or after clicking an ad. This number doesn't include whether or not calls
     * were connected, or the duration of any calls.
     * This metric applies to feed items only.
     *
     * Generated from protobuf field <code>optional double all_conversions_from_click_to_call = 195;</code>
     * @return float
     */
    public function getAllConversionsFromClickToCall()
    {
        return isset($this->all_conversions_from_click_to_call) ? $this->all_conversions_from_click_to_call : 0.0;
    }

    public function hasAllConversionsFromClickToCall()
    {
        return isset($this->all_conversions_from_click_to_call);
    }

    public function clearAllConversionsFromClickToCall()
    {
        unset($this->all_conversions_from_click_to_call);
    }

    /**
     * The number of times people clicked the "Call" button to call a store during
     * or after clicking an ad. This number doesn't include whether or not calls
     * were connected, or the duration of any calls.
     * This metric applies to feed items only.
     *
     * Generated from protobuf field <code>optional double all_conversions_from_click_to_call = 195;</code>
     * @param float $var
     * @return $this
     */
    public function setAllConversionsFromClickToCall($var)
    {
        GPBUtil::checkDouble($var);
        $this->all_conversions_from_click_to_call = $var;

        return $this;
    }

    /**
     * The number of times people clicked a "Get directions" button to navigate to
     * a store after clicking an ad.
     * This metric applies to feed items only.
     *
     * Generated from protobuf field <code>optional double all_conversions_from_directions = 196;</code>
     * @return float
     */
    public function getAllConversionsFromDirections()
    {
        return isset($this->all_conversions_from_directions) ? $this->all_conversions_from_directions : 0.0;
    }

    public function hasAllConversionsFromDirections()
    {
        return isset($this->all_conversions_from_directions);
    }

    public function clearAllConversionsFromDirections()
    {
        unset($this->all_conversions_from_directions);
    }

    /**
     * The number of times people clicked a "Get directions" button to navigate to
     * a store after clicking an ad.
     * This metric applies to feed items only.
     *
     * Generated from protobuf field <code>optional double all_conversions_from_directions = 196;</code>
     * @param float $var
     * @return $this
     */
    public function setAllConversionsFromDirections($var)
    {
        GPBUtil::checkDouble($var);
        $this->all_conversions_from_directions = $var;

        return $this;
    }

    /**
     * The value of all conversions from interactions divided by the total number
     * of interactions.
     *
     * Generated from protobuf field <code>optional double all_conversions_from_interactions_value_per_interaction = 197;</code>
     * @return float
     */
    public function getAllConversionsFromInteractionsValuePerInteraction()
    {
        return isset($this->all_conversions_from_interactions_value_per_interaction) ? $this->all_conversions_from_interactions_value_per_interaction : 0.0;
    }

    public function hasAllConversionsFromInteractionsValuePerInteraction()
    {
        return isset($this->all_conversions_from_interactions_value_per_interaction);
    }

    public function clearAllConversionsFromInteractionsValuePerInteraction()
    {
        unset($this->all_conversions_from_interactions_value_per_interaction);
    }

    /**
     * The value of all conversions from interactions divided by the total number
     * of interactions.
     *
     * Generated from protobuf field <code>optional double all_conversions_from_interactions_value_per_interaction = 197;</code>
     * @param float $var
     * @return $this
     */
    public function setAllConversionsFromInteractionsValuePerInteraction($var)
    {
        GPBUtil::checkDouble($var);
        $this->all_conversions_from_interactions_value_per_interaction = $var;

        return $this;
    }

    /**
     * The number of times people clicked a link to view a store's menu after
     * clicking an ad.
     * This metric applies to feed items only.
     *
     * Generated from protobuf field <code>optional double all_conversions_from_menu = 198;</code>
     * @return float
     */
    public function getAllConversionsFromMenu()
    {
        return isset($this->all_conversions_from_menu) ? $this->all_conversions_from_menu : 0.0;
    }

    public function hasAllConversionsFromMenu()
    {
        return isset($this->all_conversions_from_menu);
    }

    public function clearAllConversionsFromMenu()
    {
        unset($this->all_conversions_from_menu);
    }

    /**
     * The number of times people clicked a link to view a store's menu after
     * clicking an ad.
     * This metric applies to feed items only.
     *
     * Generated from protobuf field <code>optional double all_conversions_from_menu = 198;</code>
     * @param float $var
     * @return $this
     */
    public function setAllConversionsFromMenu($var)
    {
        GPBUtil::checkDouble($var);
        $this->all_conversions_from_menu = $var;

        return $this;
    }

    /**
     * The number of times people placed an order at a store after clicking an ad.
     * This metric applies to feed items only.
     *
     * Generated from protobuf field <code>optional double all_conversions_from_order = 199;</code>
     * @return float
     */
    public function getAllConversionsFromOrder()
    {
        return isset($this->all_conversions_from_order) ? $this->all_conversions_from_order : 0.0;
    }

    public function hasAllConversionsFromOrder()
    {
        return isset($this->all_conversions_from_order);
    }

    public function clearAllConversionsFromOrder()
    {
        unset($this->all_conversions_from_order);
    }

    /**
     * The number of times people placed an order at a store after clicking an ad.
     * This metric applies to feed items only.
     *
     * Generated from protobuf field <code>optional double all_conversions_from_order = 199;</code>
     * @param float $var
     * @return $this
     */
    public function setAllConversionsFromOrder($var)
    {
        GPBUtil::checkDouble($var);
        $this->all_conversions_from_order = $var;

        return $this;
    }

    /**
     * The number of other conversions (for example, posting a review or saving a
     * location for a store) that occurred after people clicked an ad.
     * This metric applies to feed items only.
     *
     * Generated from protobuf field <code>optional double all_conversions_from_other_engagement = 200;</code>
     * @return float
     */
    public function getAllConversionsFromOtherEngagement()
    {
        return isset($this->all_conversions_from_other_engagement) ? $this->all_conversions_from_other_engagement : 0.0;
    }

    public function hasAllConversionsFromOtherEngagement()
    {
        return isset($this->all_conversions_from_other_engagement);
    }

    public function clearAllConversionsFromOtherEngagement()
    {
        unset($this->all_conversions_from_other_engagement);
    }

    /**
     * The number of other conversions (for example, posting a review or saving a
     * location for a store) that occurred after people clicked an ad.
     * This metric applies to feed items only.
     *
     * Generated from protobuf field <code>optional double all_conversions_from_other_engagement = 200;</code>
     * @param float $var
     * @return $this
     */
    public function setAllConversionsFromOtherEngagement($var)
    {
        GPBUtil::checkDouble($var);
        $this->all_conversions_from_other_engagement = $var;

        return $this;
    }

    /**
     * Estimated number of times people visited a store after clicking an ad.
     * This metric applies to feed items only.
     *
     * Generated from protobuf field <code>optional double all_conversions_from_store_visit = 201;</code>
     * @return float
     */
    public function getAllConversionsFromStoreVisit()
    {
        return isset($this->all_conversions_from_store_visit) ? $this->all_conversions_from_store_visit : 0.0;
    }

    public function hasAllConversionsFromStoreVisit()
    {
        return isset($this->all_conversions_from_store_visit);
    }

    public function clearAllConversionsFromStoreVisit()
    {
        unset($this->all_conversions_from_store_visit);
    }

    /**
     * Estimated number of times people visited a store after clicking an ad.
     * This metric applies to feed items only.
     *
     * Generated from protobuf field <code>optional double all_conversions_from_store_visit = 201;</code>
     * @param float $var
     * @return $this
     */
    public function setAllConversionsFromStoreVisit($var)
    {
        GPBUtil::checkDouble($var);
        $this->all_conversions_from_store_visit = $var;

        return $this;
    }

    /**
     * The number of times that people were taken to a store's URL after clicking
     * an ad.
     * This metric applies to feed items only.
     *
     * Generated from protobuf field <code>optional double all_conversions_from_store_website = 202;</code>
     * @return float
     */
    public function getAllConversionsFromStoreWebsite()
    {
        return isset($this->all_conversions_from_store_website) ? $this->all_conversions_from_store_website : 0.0;
    }

    public function hasAllConversionsFromStoreWebsite()
    {
        return isset($this->all_conversions_from_store_website);
    }

    public function clearAllConversionsFromStoreWebsite()
    {
        unset($this->all_conversions_from_store_website);
    }

    /**
     * The number of times that people were taken to a store's URL after clicking
     * an ad.
     * This metric applies to feed items only.
     *
     * Generated from protobuf field <code>optional double all_conversions_from_store_website = 202;</code>
     * @param float $var
     * @return $this
     */
    public function setAllConversionsFromStoreWebsite($var)
    {
        GPBUtil::checkDouble($var);
        $this->all_conversions_from_store_website = $var;

        return $this;
    }

    /**
     * This metric is part of the Auction Insights report, and tells how often
     * the ads of another participant showed as the very first ad above the
     * organic search results.
     * This percentage is computed only over the auctions that you appeared in
     * the page.
     * This metric is not publicly available.
     *
     * Generated from protobuf field <code>optional double auction_insight_search_absolute_top_impression_percentage = 258;</code>
     * @return float
     */
    public function getAuctionInsightSearchAbsoluteTopImpressionPercentage()
    {
        return isset($this->auction_insight_search_absolute_top_impression_percentage) ? $this->auction_insight_search_absolute_top_impression_percentage : 0.0;
    }

    public function hasAuctionInsightSearchAbsoluteTopImpressionPercentage()
    {
        return isset($this->auction_insight_search_absolute_top_impression_percentage);
    }

    public function clearAuctionInsightSearchAbsoluteTopImpressionPercentage()
    {
        unset($this->auction_insight_search_absolute_top_impression_percentage);
    }

    /**
     * This metric is part of the Auction Insights report, and tells how often
     * the ads of another participant showed as the very first ad above the
     * organic search results.
     * This percentage is computed only over the auctions that you appeared in
     * the page.
     * This metric is not publicly available.
     *
     * Generated from protobuf field <code>optional double auction_insight_search_absolute_top_impression_percentage = 258;</code>
     * @param float $var
     * @return $this
     */
    public function setAuctionInsightSearchAbsoluteTopImpressionPercentage($var)
    {
        GPBUtil::checkDouble($var);
        $this->auction_insight_search_absolute_top_impression_percentage = $var;

        return $this;
    }

    /**
     * This metric is part of the Auction Insights report, and tells the
     * percentage of impressions that another participant obtained, over the total
     * number of impressions that your ads were eligible for.
     * Any value below 0.1 is reported as 0.0999.
     * This metric is not publicly available.
     *
     * Generated from protobuf field <code>optional double auction_insight_search_impression_share = 259;</code>
     * @return float
     */
    public function getAuctionInsightSearchImpressionShare()
    {
        return isset($this->auction_insight_search_impression_share) ? $this->auction_insight_search_impression_share : 0.0;
    }

    public function hasAuctionInsightSearchImpressionShare()
    {
        return isset($this->auction_insight_search_impression_share);
    }

    public function clearAuctionInsightSearchImpressionShare()
    {
        unset($this->auction_insight_search_impression_share);
    }

    /**
     * This metric is part of the Auction Insights report, and tells the
     * percentage of impressions that another participant obtained, over the total
     * number of impressions that your ads were eligible for.
     * Any value below 0.1 is reported as 0.0999.
     * This metric is not publicly available.
     *
     * Generated from protobuf field <code>optional double auction_insight_search_impression_share = 259;</code>
     * @param float $var
     * @return $this
     */
    public function setAuctionInsightSearchImpressionShare($var)
    {
        GPBUtil::checkDouble($var);
        $this->auction_insight_search_impression_share = $var;

        return $this;
    }

    /**
     * This metric is part of the Auction Insights report, and tells the
     * percentage of impressions that your ads outranked (showed above)
     * another participant in the auction, compared to the total number of
     * impressions that your ads were eligible for.
     * Any value below 0.1 is reported as 0.0999.
     * This metric is not publicly available.
     *
     * Generated from protobuf field <code>optional double auction_insight_search_outranking_share = 260;</code>
     * @return float
     */
    public function getAuctionInsightSearchOutrankingShare()
    {
        return isset($this->auction_insight_search_outranking_share) ? $this->auction_insight_search_outranking_share : 0.0;
    }

    public function hasAuctionInsightSearchOutrankingShare()
    {
        return isset($this->auction_insight_search_outranking_share);
    }

    public function clearAuctionInsightSearchOutrankingShare()
    {
        unset($this->auction_insight_search_outranking_share);
    }

    /**
     * This metric is part of the Auction Insights report, and tells the
     * percentage of impressions that your ads outranked (showed above)
     * another participant in the auction, compared to the total number of
     * impressions that your ads were eligible for.
     * Any value below 0.1 is reported as 0.0999.
     * This metric is not publicly available.
     *
     * Generated from protobuf field <code>optional double auction_insight_search_outranking_share = 260;</code>
     * @param float $var
     * @return $this
     */
    public function setAuctionInsightSearchOutrankingShare($var)
    {
        GPBUtil::checkDouble($var);
        $this->auction_insight_search_outranking_share = $var;

        return $this;
    }

    /**
     * This metric is part of the Auction Insights report, and tells how often
     * another participant's ad received an impression when your ad also received
     * an impression.
     * This metric is not publicly available.
     *
     * Generated from protobuf field <code>optional double auction_insight_search_overlap_rate = 261;</code>
     * @return float
     */
    public function getAuctionInsightSearchOverlapRate()
    {
        return isset($this->auction_insight_search_overlap_rate) ? $this->auction_insight_search_overlap_rate : 0.0;
    }

    public function hasAuctionInsightSearchOverlapRate()
    {
        return isset($this->auction_insight_search_overlap_rate);
    }

    public function clearAuctionInsightSearchOverlapRate()
    {
        unset($this->auction_insight_search_overlap_rate);
    }

    /**
     * This metric is part of the Auction Insights report, and tells how often
     * another participant's ad received an impression when your ad also received
     * an impression.
     * This metric is not publicly available.
     *
     * Generated from protobuf field <code>optional double auction_insight_search_overlap_rate = 261;</code>
     * @param float $var
     * @return $this
     */
    public function setAuctionInsightSearchOverlapRate($var)
    {
        GPBUtil::checkDouble($var);
        $this->auction_insight_search_overlap_rate = $var;

        return $this;
    }

    /**
     * This metric is part of the Auction Insights report, and tells how often
     * another participant's ad was shown in a higher position than yours, when
     * both of your ads were shown at the same page.
     * This metric is not publicly available.
     *
     * Generated from protobuf field <code>optional double auction_insight_search_position_above_rate = 262;</code>
     * @return float
     */
    public function getAuctionInsightSearchPositionAboveRate()
    {
        return isset($this->auction_insight_search_position_above_rate) ? $this->auction_insight_search_position_above_rate : 0.0;
    }

    public function hasAuctionInsightSearchPositionAboveRate()
    {
        return isset($this->auction_insight_search_position_above_rate);
    }

    public function clearAuctionInsightSearchPositionAboveRate()
    {
        unset($this->auction_insight_search_position_above_rate);
    }

    /**
     * This metric is part of the Auction Insights report, and tells how often
     * another participant's ad was shown in a higher position than yours, when
     * both of your ads were shown at the same page.
     * This metric is not publicly available.
     *
     * Generated from protobuf field <code>optional double auction_insight_search_position_above_rate = 262;</code>
     * @param float $var
     * @return $this
     */
    public function setAuctionInsightSearchPositionAboveRate($var)
    {
        GPBUtil::checkDouble($var);
        $this->auction_insight_search_position_above_rate = $var;

        return $this;
    }

    /**
     * This metric is part of the Auction Insights report, and tells how often
     * the ads of another participant showed above the organic search results.
     * This percentage is computed only over the auctions that you appeared in
     * the page.
     * This metric is not publicly available.
     *
     * Generated from protobuf field <code>optional double auction_insight_search_top_impression_percentage = 263;</code>
     * @return float
     */
    public function getAuctionInsightSearchTopImpressionPercentage()
    {
        return isset($this->auction_insight_search_top_impression_percentage) ? $this->auction_insight_search_top_impression_percentage : 0.0;
    }

    public function hasAuctionInsightSearchTopImpressionPercentage()
    {
        return isset($this->auction_insight_search_top_impression_percentage);
    }

    public function clearAuctionInsightSearchTopImpressionPercentage()
    {
        unset($this->auction_insight_search_top_impression_percentage);
    }

    /**
     * This metric is part of the Auction Insights report, and tells how often
     * the ads of another participant showed above the organic search results.
     * This percentage is computed only over the auctions that you appeared in
     * the page.
     * This metric is not publicly available.
     *
     * Generated from protobuf field <code>optional double auction_insight_search_top_impression_percentage = 263;</code>
     * @param float $var
     * @return $this
     */
    public function setAuctionInsightSearchTopImpressionPercentage($var)
    {
        GPBUtil::checkDouble($var);
        $this->auction_insight_search_top_impression_percentage = $var;

        return $this;
    }

    /**
     * The average amount you pay per interaction. This amount is the total cost
     * of your ads divided by the total number of interactions.
     *
     * Generated from protobuf field <code>optional double average_cost = 203;</code>
     * @return float
     */
    public function getAverageCost()
    {
        return isset($this->average_cost) ? $this->average_cost : 0.0;
    }

    public function hasAverageCost()
    {
        return isset($this->average_cost);
    }

    public function clearAverageCost()
    {
        unset($this->average_cost);
    }

    /**
     * The average amount you pay per interaction. This amount is the total cost
     * of your ads divided by the total number of interactions.
     *
     * Generated from protobuf field <code>optional double average_cost = 203;</code>
     * @param float $var
     * @return $this
     */
    public function setAverageCost($var)
    {
        GPBUtil::checkDouble($var);
        $this->average_cost = $var;

        return $this;
    }

    /**
     * The total cost of all clicks divided by the total number of clicks
     * received.
     *
     * Generated from protobuf field <code>optional double average_cpc = 204;</code>
     * @return float
     */
    public function getAverageCpc()
    {
        return isset($this->average_cpc) ? $this->average_cpc : 0.0;
    }

    public function hasAverageCpc()
    {
        return isset($this->average_cpc);
    }

    public function clearAverageCpc()
    {
        unset($this->average_cpc);
    }

    /**
     * The total cost of all clicks divided by the total number of clicks
     * received.
     *
     * Generated from protobuf field <code>optional double average_cpc = 204;</code>
     * @param float $var
     * @return $this
     */
    public function setAverageCpc($var)
    {
        GPBUtil::checkDouble($var);
        $this->average_cpc = $var;

        return $this;
    }

    /**
     * The average amount that you've been charged for an ad engagement. This
     * amount is the total cost of all ad engagements divided by the total number
     * of ad engagements.
     *
     * Generated from protobuf field <code>optional double average_cpe = 205;</code>
     * @return float
     */
    public function getAverageCpe()
    {
        return isset($this->average_cpe) ? $this->average_cpe : 0.0;
    }

    public function hasAverageCpe()
    {
        return isset($this->average_cpe);
    }

    public function clearAverageCpe()
    {
        unset($this->average_cpe);
    }

    /**
     * The average amount that you've been charged for an ad engagement. This
     * amount is the total cost of all ad engagements divided by the total number
     * of ad engagements.
     *
     * Generated from protobuf field <code>optional double average_cpe = 205;</code>
     * @param float $var
     * @return $this
     */
    public function setAverageCpe($var)
    {
        GPBUtil::checkDouble($var);
        $this->average_cpe = $var;

        return $this;
    }

    /**
     * Average cost-per-thousand impressions (CPM).
     *
     * Generated from protobuf field <code>optional double average_cpm = 206;</code>
     * @return float
     */
    public function getAverageCpm()
    {
        return isset($this->average_cpm) ? $this->average_cpm : 0.0;
    }

    public function hasAverageCpm()
    {
        return isset($this->average_cpm);
    }

    public function clearAverageCpm()
    {
        unset($this->average_cpm);
    }

    /**
     * Average cost-per-thousand impressions (CPM).
     *
     * Generated from protobuf field <code>optional double average_cpm = 206;</code>
     * @param float $var
     * @return $this
     */
    public function setAverageCpm($var)
    {
        GPBUtil::checkDouble($var);
        $this->average_cpm = $var;

        return $this;
    }

    /**
     * The average amount you pay each time someone views your ad.
     * The average CPV is defined by the total cost of all ad views divided by
     * the number of views.
     *
     * Generated from protobuf field <code>optional double average_cpv = 207;</code>
     * @return float
     */
    public function getAverageCpv()
    {
        return isset($this->average_cpv) ? $this->average_cpv : 0.0;
    }

    public function hasAverageCpv()
    {
        return isset($this->average_cpv);
    }

    public function clearAverageCpv()
    {
        unset($this->average_cpv);
    }

    /**
     * The average amount you pay each time someone views your ad.
     * The average CPV is defined by the total cost of all ad views divided by
     * the number of views.
     *
     * Generated from protobuf field <code>optional double average_cpv = 207;</code>
     * @param float $var
     * @return $this
     */
    public function setAverageCpv($var)
    {
        GPBUtil::checkDouble($var);
        $this->average_cpv = $var;

        return $this;
    }

    /**
     * Average number of pages viewed per session.
     *
     * Generated from protobuf field <code>optional double average_page_views = 208;</code>
     * @return float
     */
    public function getAveragePageViews()
    {
        return isset($this->average_page_views) ? $this->average_page_views : 0.0;
    }

    public function hasAveragePageViews()
    {
        return isset($this->average_page_views);
    }

    public function clearAveragePageViews()
    {
        unset($this->average_page_views);
    }

    /**
     * Average number of pages viewed per session.
     *
     * Generated from protobuf field <code>optional double average_page_views = 208;</code>
     * @param float $var
     * @return $this
     */
    public function setAveragePageViews($var)
    {
        GPBUtil::checkDouble($var);
        $this->average_page_views = $var;

        return $this;
    }

    /**
     * Total duration of all sessions (in seconds) / number of sessions. Imported
     * from Google Analytics.
     *
     * Generated from protobuf field <code>optional double average_time_on_site = 209;</code>
     * @return float
     */
    public function getAverageTimeOnSite()
    {
        return isset($this->average_time_on_site) ? $this->average_time_on_site : 0.0;
    }

    public function hasAverageTimeOnSite()
    {
        return isset($this->average_time_on_site);
    }

    public function clearAverageTimeOnSite()
    {
        unset($this->average_time_on_site);
    }

    /**
     * Total duration of all sessions (in seconds) / number of sessions. Imported
     * from Google Analytics.
     *
     * Generated from protobuf field <code>optional double average_time_on_site = 209;</code>
     * @param float $var
     * @return $this
     */
    public function setAverageTimeOnSite($var)
    {
        GPBUtil::checkDouble($var);
        $this->average_time_on_site = $var;

        return $this;
    }

    /**
     * An indication of how other advertisers are bidding on similar products.
     *
     * Generated from protobuf field <code>optional double benchmark_average_max_cpc = 210;</code>
     * @return float
     */
    public function getBenchmarkAverageMaxCpc()
    {
        return isset($this->benchmark_average_max_cpc) ? $this->benchmark_average_max_cpc : 0.0;
    }

    public function hasBenchmarkAverageMaxCpc()
    {
        return isset($this->benchmark_average_max_cpc);
    }

    public function clearBenchmarkAverageMaxCpc()
    {
        unset($this->benchmark_average_max_cpc);
    }

    /**
     * An indication of how other advertisers are bidding on similar products.
     *
     * Generated from protobuf field <code>optional double benchmark_average_max_cpc = 210;</code>
     * @param float $var
     * @return $this
     */
    public function setBenchmarkAverageMaxCpc($var)
    {
        GPBUtil::checkDouble($var);
        $this->benchmark_average_max_cpc = $var;

        return $this;
    }

    /**
     * Number of app installs.
     *
     * Generated from protobuf field <code>optional double biddable_app_install_conversions = 254;</code>
     * @return float
     */
    public function getBiddableAppInstallConversions()
    {
        return isset($this->biddable_app_install_conversions) ? $this->biddable_app_install_conversions : 0.0;
    }

    public function hasBiddableAppInstallConversions()
    {
        return isset($this->biddable_app_install_conversions);
    }

    public function clearBiddableAppInstallConversions()
    {
        unset($this->biddable_app_install_conversions);
    }

    /**
     * Number of app installs.
     *
     * Generated from protobuf field <code>optional double biddable_app_install_conversions = 254;</code>
     * @param float $var
     * @return $this
     */
    public function setBiddableAppInstallConversions($var)
    {
        GPBUtil::checkDouble($var);
        $this->biddable_app_install_conversions = $var;

        return $this;
    }

    /**
     * Number of in-app actions.
     *
     * Generated from protobuf field <code>optional double biddable_app_post_install_conversions = 255;</code>
     * @return float
     */
    public function getBiddableAppPostInstallConversions()
    {
        return isset($this->biddable_app_post_install_conversions) ? $this->biddable_app_post_install_conversions : 0.0;
    }

    public function hasBiddableAppPostInstallConversions()
    {
        return isset($this->biddable_app_post_install_conversions);
    }

    public function clearBiddableAppPostInstallConversions()
    {
        unset($this->biddable_app_post_install_conversions);
    }

    /**
     * Number of in-app actions.
     *
     * Generated from protobuf field <code>optional double biddable_app_post_install_conversions = 255;</code>
     * @param float $var
     * @return $this
     */
    public function setBiddableAppPostInstallConversions($var)
    {
        GPBUtil::checkDouble($var);
        $this->biddable_app_post_install_conversions = $var;

        return $this;
    }

    /**
     * An indication on how other advertisers' Shopping ads for similar products
     * are performing based on how often people who see their ad click on it.
     *
     * Generated from protobuf field <code>optional double benchmark_ctr = 211;</code>
     * @return float
     */
    public function getBenchmarkCtr()
    {
        return isset($this->benchmark_ctr) ? $this->benchmark_ctr : 0.0;
    }

    public function hasBenchmarkCtr()
    {
        return isset($this->benchmark_ctr);
    }

    public function clearBenchmarkCtr()
    {
        unset($this->benchmark_ctr);
    }

    /**
     * An indication on how other advertisers' Shopping ads for similar products
     * are performing based on how often people who see their ad click on it.
     *
     * Generated from protobuf field <code>optional double benchmark_ctr = 211;</code>
     * @param float $var
     * @return $this
     */
    public function setBenchmarkCtr($var)
    {
        GPBUtil::checkDouble($var);
        $this->benchmark_ctr = $var;

        return $this;
    }

    /**
     * Percentage of clicks where the user only visited a single page on your
     * site. Imported from Google Analytics.
     *
     * Generated from protobuf field <code>optional double bounce_rate = 212;</code>
     * @return float
     */
    public function getBounceRate()
    {
        return isset($this->bounce_rate) ? $this->bounce_rate : 0.0;
    }

    public function hasBounceRate()
    {
        return isset($this->bounce_rate);
    }

    public function clearBounceRate()
    {
        unset($this->bounce_rate);
    }

    /**
     * Percentage of clicks where the user only visited a single page on your
     * site. Imported from Google Analytics.
     *
     * Generated from protobuf field <code>optional double bounce_rate = 212;</code>
     * @param float $var
     * @return $this
     */
    public function setBounceRate($var)
    {
        GPBUtil::checkDouble($var);
        $this->bounce_rate = $var;

        return $this;
    }

    /**
     * The number of clicks.
     *
     * Generated from protobuf field <code>optional int64 clicks = 131;</code>
     * @return int|string
     */
    public function getClicks()
    {
        return isset($this->clicks) ? $this->clicks : 0;
    }

    public function hasClicks()
    {
        return isset($this->clicks);
    }

    public function clearClicks()
    {
        unset($this->clicks);
    }

    /**
     * The number of clicks.
     *
     * Generated from protobuf field <code>optional int64 clicks = 131;</code>
     * @param int|string $var
     * @return $this
     */
    public function setClicks($var)
    {
        GPBUtil::checkInt64($var);
        $this->clicks = $var;

        return $this;
    }

    /**
     * The number of times your ad or your site's listing in the unpaid
     * results was clicked. See the help page at
     * https://support.google.com/google-ads/answer/3097241 for details.
     *
     * Generated from protobuf field <code>optional int64 combined_clicks = 156;</code>
     * @return int|string
     */
    public function getCombinedClicks()
    {
        return isset($this->combined_clicks) ? $this->combined_clicks : 0;
    }

    public function hasCombinedClicks()
    {
        return isset($this->combined_clicks);
    }

    public function clearCombinedClicks()
    {
        unset($this->combined_clicks);
    }

    /**
     * The number of times your ad or your site's listing in the unpaid
     * results was clicked. See the help page at
     * https://support.google.com/google-ads/answer/3097241 for details.
     *
     * Generated from protobuf field <code>optional int64 combined_clicks = 156;</code>
     * @param int|string $var
     * @return $this
     */
    public function setCombinedClicks($var)
    {
        GPBUtil::checkInt64($var);
        $this->combined_clicks = $var;

        return $this;
    }

    /**
     * The number of times your ad or your site's listing in the unpaid
     * results was clicked (combined_clicks) divided by combined_queries. See the
     * help page at https://support.google.com/google-ads/answer/3097241 for
     * details.
     *
     * Generated from protobuf field <code>optional double combined_clicks_per_query = 157;</code>
     * @return float
     */
    public function getCombinedClicksPerQuery()
    {
        return isset($this->combined_clicks_per_query) ? $this->combined_clicks_per_query : 0.0;
    }

    public function hasCombinedClicksPerQuery()
    {
        return isset($this->combined_clicks_per_query);
    }

    public function clearCombinedClicksPerQuery()
    {
        unset($this->combined_clicks_per_query);
    }

    /**
     * The number of times your ad or your site's listing in the unpaid
     * results was clicked (combined_clicks) divided by combined_queries. See the
     * help page at https://support.google.com/google-ads/answer/3097241 for
     * details.
     *
     * Generated from protobuf field <code>optional double combined_clicks_per_query = 157;</code>
     * @param float $var
     * @return $this
     */
    public function setCombinedClicksPerQuery($var)
    {
        GPBUtil::checkDouble($var);
        $this->combined_clicks_per_query = $var;

        return $this;
    }

    /**
     * The number of searches that returned pages from your site in the unpaid
     * results or showed one of your text ads. See the help page at
     * https://support.google.com/google-ads/answer/3097241 for details.
     *
     * Generated from protobuf field <code>optional int64 combined_queries = 158;</code>
     * @return int|string
     */
    public function getCombinedQueries()
    {
        return isset($this->combined_queries) ? $this->combined_queries : 0;
    }

    public function hasCombinedQueries()
    {
        return isset($this->combined_queries);
    }

    public function clearCombinedQueries()
    {
        unset($this->combined_queries);
    }

    /**
     * The number of searches that returned pages from your site in the unpaid
     * results or showed one of your text ads. See the help page at
     * https://support.google.com/google-ads/answer/3097241 for details.
     *
     * Generated from protobuf field <code>optional int64 combined_queries = 158;</code>
     * @param int|string $var
     * @return $this
     */
    public function setCombinedQueries($var)
    {
        GPBUtil::checkInt64($var);
        $this->combined_queries = $var;

        return $this;
    }

    /**
     * The estimated percent of times that your ad was eligible to show
     * on the Display Network but didn't because your budget was too low.
     * Note: Content budget lost impression share is reported in the range of 0
     * to 0.9. Any value above 0.9 is reported as 0.9001.
     *
     * Generated from protobuf field <code>optional double content_budget_lost_impression_share = 159;</code>
     * @return float
     */
    public function getContentBudgetLostImpressionShare()
    {
        return isset($this->content_budget_lost_impression_share) ? $this->content_budget_lost_impression_share : 0.0;
    }

    public function hasContentBudgetLostImpressionShare()
    {
        return isset($this->content_budget_lost_impression_share);
    }

    public function clearContentBudgetLostImpressionShare()
    {
        unset($this->content_budget_lost_impression_share);
    }

    /**
     * The estimated percent of times that your ad was eligible to show
     * on the Display Network but didn't because your budget was too low.
     * Note: Content budget lost impression share is reported in the range of 0
     * to 0.9. Any value above 0.9 is reported as 0.9001.
     *
     * Generated from protobuf field <code>optional double content_budget_lost_impression_share = 159;</code>
     * @param float $var
     * @return $this
     */
    public function setContentBudgetLostImpressionShare($var)
    {
        GPBUtil::checkDouble($var);
        $this->content_budget_lost_impression_share = $var;

        return $this;
    }

    /**
     * The impressions you've received on the Display Network divided
     * by the estimated number of impressions you were eligible to receive.
     * Note: Content impression share is reported in the range of 0.1 to 1. Any
     * value below 0.1 is reported as 0.0999.
     *
     * Generated from protobuf field <code>optional double content_impression_share = 160;</code>
     * @return float
     */
    public function getContentImpressionShare()
    {
        return isset($this->content_impression_share) ? $this->content_impression_share : 0.0;
    }

    public function hasContentImpressionShare()
    {
        return isset($this->content_impression_share);
    }

    public function clearContentImpressionShare()
    {
        unset($this->content_impression_share);
    }

    /**
     * The impressions you've received on the Display Network divided
     * by the estimated number of impressions you were eligible to receive.
     * Note: Content impression share is reported in the range of 0.1 to 1. Any
     * value below 0.1 is reported as 0.0999.
     *
     * Generated from protobuf field <code>optional double content_impression_share = 160;</code>
     * @param float $var
     * @return $this
     */
    public function setContentImpressionShare($var)
    {
        GPBUtil::checkDouble($var);
        $this->content_impression_share = $var;

        return $this;
    }

    /**
     * The last date/time a conversion tag for this conversion action successfully
     * fired and was seen by Google Ads. This firing event may not have been the
     * result of an attributable conversion (for example, because the tag was
     * fired from a browser that did not previously click an ad from an
     * appropriate advertiser). The date/time is in the customer's time zone.
     *
     * Generated from protobuf field <code>optional string conversion_last_received_request_date_time = 161;</code>
     * @return string
     */
    public function getConversionLastReceivedRequestDateTime()
    {
        return isset($this->conversion_last_received_request_date_time) ? $this->conversion_last_received_request_date_time : '';
    }

    public function hasConversionLastReceivedRequestDateTime()
    {
        return isset($this->conversion_last_received_request_date_time);
    }

    public function clearConversionLastReceivedRequestDateTime()
    {
        unset($this->conversion_last_received_request_date_time);
    }

    /**
     * The last date/time a conversion tag for this conversion action successfully
     * fired and was seen by Google Ads. This firing event may not have been the
     * result of an attributable conversion (for example, because the tag was
     * fired from a browser that did not previously click an ad from an
     * appropriate advertiser). The date/time is in the customer's time zone.
     *
     * Generated from protobuf field <code>optional string conversion_last_received_request_date_time = 161;</code>
     * @param string $var
     * @return $this
     */
    public function setConversionLastReceivedRequestDateTime($var)
    {
        GPBUtil::checkString($var, True);
        $this->conversion_last_received_request_date_time = $var;

        return $this;
    }

    /**
     * The date of the most recent conversion for this conversion action. The date
     * is in the customer's time zone.
     *
     * Generated from protobuf field <code>optional string conversion_last_conversion_date = 162;</code>
     * @return string
     */
    public function getConversionLastConversionDate()
    {
        return isset($this->conversion_last_conversion_date) ? $this->conversion_last_conversion_date : '';
    }

    public function hasConversionLastConversionDate()
    {
        return isset($this->conversion_last_conversion_date);
    }

    public function clearConversionLastConversionDate()
    {
        unset($this->conversion_last_conversion_date);
    }

    /**
     * The date of the most recent conversion for this conversion action. The date
     * is in the customer's time zone.
     *
     * Generated from protobuf field <code>optional string conversion_last_conversion_date = 162;</code>
     * @param string $var
     * @return $this
     */
    public function setConversionLastConversionDate($var)
    {
        GPBUtil::checkString($var, True);
        $this->conversion_last_conversion_date = $var;

        return $this;
    }

    /**
     * The estimated percentage of impressions on the Display Network
     * that your ads didn't receive due to poor Ad Rank.
     * Note: Content rank lost impression share is reported in the range of 0
     * to 0.9. Any value above 0.9 is reported as 0.9001.
     *
     * Generated from protobuf field <code>optional double content_rank_lost_impression_share = 163;</code>
     * @return float
     */
    public function getContentRankLostImpressionShare()
    {
        return isset($this->content_rank_lost_impression_share) ? $this->content_rank_lost_impression_share : 0.0;
    }

    public function hasContentRankLostImpressionShare()
    {
        return isset($this->content_rank_lost_impression_share);
    }

    public function clearContentRankLostImpressionShare()
    {
        unset($this->content_rank_lost_impression_share);
    }

    /**
     * The estimated percentage of impressions on the Display Network
     * that your ads didn't receive due to poor Ad Rank.
     * Note: Content rank lost impression share is reported in the range of 0
     * to 0.9. Any value above 0.9 is reported as 0.9001.
     *
     * Generated from protobuf field <code>optional double content_rank_lost_impression_share = 163;</code>
     * @param float $var
     * @return $this
     */
    public function setContentRankLostImpressionShare($var)
    {
        GPBUtil::checkDouble($var);
        $this->content_rank_lost_impression_share = $var;

        return $this;
    }

    /**
     * Conversions from interactions divided by the number of ad interactions
     * (such as clicks for text ads or views for video ads). This only includes
     * conversion actions which include_in_conversions_metric attribute is set to
     * true. If you use conversion-based bidding, your bid strategies will
     * optimize for these conversions.
     *
     * Generated from protobuf field <code>optional double conversions_from_interactions_rate = 164;</code>
     * @return float
     */
    public function getConversionsFromInteractionsRate()
    {
        return isset($this->conversions_from_interactions_rate) ? $this->conversions_from_interactions_rate : 0.0;
    }

    public function hasConversionsFromInteractionsRate()
    {
        return isset($this->conversions_from_interactions_rate);
    }

    public function clearConversionsFromInteractionsRate()
    {
        unset($this->conversions_from_interactions_rate);
    }

    /**
     * Conversions from interactions divided by the number of ad interactions
     * (such as clicks for text ads or views for video ads). This only includes
     * conversion actions which include_in_conversions_metric attribute is set to
     * true. If you use conversion-based bidding, your bid strategies will
     * optimize for these conversions.
     *
     * Generated from protobuf field <code>optional double conversions_from_interactions_rate = 164;</code>
     * @param float $var
     * @return $this
     */
    public function setConversionsFromInteractionsRate($var)
    {
        GPBUtil::checkDouble($var);
        $this->conversions_from_interactions_rate = $var;

        return $this;
    }

    /**
     * The value of conversions. This only includes conversion actions which
     * include_in_conversions_metric attribute is set to true. If you use
     * conversion-based bidding, your bid strategies will optimize for these
     * conversions.
     *
     * Generated from protobuf field <code>optional double conversions_value = 165;</code>
     * @return float
     */
    public function getConversionsValue()
    {
        return isset($this->conversions_value) ? $this->conversions_value : 0.0;
    }

    public function hasConversionsValue()
    {
        return isset($this->conversions_value);
    }

    public function clearConversionsValue()
    {
        unset($this->conversions_value);
    }

    /**
     * The value of conversions. This only includes conversion actions which
     * include_in_conversions_metric attribute is set to true. If you use
     * conversion-based bidding, your bid strategies will optimize for these
     * conversions.
     *
     * Generated from protobuf field <code>optional double conversions_value = 165;</code>
     * @param float $var
     * @return $this
     */
    public function setConversionsValue($var)
    {
        GPBUtil::checkDouble($var);
        $this->conversions_value = $var;

        return $this;
    }

    /**
     * The value of conversions. This only includes conversion actions which
     * include_in_conversions_metric attribute is set to true. If you use
     * conversion-based bidding, your bid strategies will optimize for these
     * conversions. When this column is selected with date, the values in date
     * column means the conversion date. Details for the by_conversion_date
     * columns are available at
     * https://support.google.com/google-ads/answer/9549009.
     *
     * Generated from protobuf field <code>double conversions_value_by_conversion_date = 242;</code>
     * @return float
     */
    public function getConversionsValueByConversionDate()
    {
        return $this->conversions_value_by_conversion_date;
    }

    /**
     * The value of conversions. This only includes conversion actions which
     * include_in_conversions_metric attribute is set to true. If you use
     * conversion-based bidding, your bid strategies will optimize for these
     * conversions. When this column is selected with date, the values in date
     * column means the conversion date. Details for the by_conversion_date
     * columns are available at
     * https://support.google.com/google-ads/answer/9549009.
     *
     * Generated from protobuf field <code>double conversions_value_by_conversion_date = 242;</code>
     * @param float $var
     * @return $this
     */
    public function setConversionsValueByConversionDate($var)
    {
        GPBUtil::checkDouble($var);
        $this->conversions_value_by_conversion_date = $var;

        return $this;
    }

    /**
     * New customers' lifetime conversion value. If you have set up
     * customer acquisition goal at either account level or campaign level, this
     * will include the additional conversion value from new customers for
     * biddable conversions. If your campaign has adopted the customer
     * acquisition goal and selected "bid higher for new customers", these values
     * will be included in "conversions_value" for optimization. See
     * https://support.google.com/google-ads/answer/******** for more details.
     *
     * Generated from protobuf field <code>optional double new_customer_lifetime_value = 293;</code>
     * @return float
     */
    public function getNewCustomerLifetimeValue()
    {
        return isset($this->new_customer_lifetime_value) ? $this->new_customer_lifetime_value : 0.0;
    }

    public function hasNewCustomerLifetimeValue()
    {
        return isset($this->new_customer_lifetime_value);
    }

    public function clearNewCustomerLifetimeValue()
    {
        unset($this->new_customer_lifetime_value);
    }

    /**
     * New customers' lifetime conversion value. If you have set up
     * customer acquisition goal at either account level or campaign level, this
     * will include the additional conversion value from new customers for
     * biddable conversions. If your campaign has adopted the customer
     * acquisition goal and selected "bid higher for new customers", these values
     * will be included in "conversions_value" for optimization. See
     * https://support.google.com/google-ads/answer/******** for more details.
     *
     * Generated from protobuf field <code>optional double new_customer_lifetime_value = 293;</code>
     * @param float $var
     * @return $this
     */
    public function setNewCustomerLifetimeValue($var)
    {
        GPBUtil::checkDouble($var);
        $this->new_customer_lifetime_value = $var;

        return $this;
    }

    /**
     * The value of conversions divided by the cost of ad interactions. This only
     * includes conversion actions which include_in_conversions_metric attribute
     * is set to true. If you use conversion-based bidding, your bid strategies
     * will optimize for these conversions.
     *
     * Generated from protobuf field <code>optional double conversions_value_per_cost = 166;</code>
     * @return float
     */
    public function getConversionsValuePerCost()
    {
        return isset($this->conversions_value_per_cost) ? $this->conversions_value_per_cost : 0.0;
    }

    public function hasConversionsValuePerCost()
    {
        return isset($this->conversions_value_per_cost);
    }

    public function clearConversionsValuePerCost()
    {
        unset($this->conversions_value_per_cost);
    }

    /**
     * The value of conversions divided by the cost of ad interactions. This only
     * includes conversion actions which include_in_conversions_metric attribute
     * is set to true. If you use conversion-based bidding, your bid strategies
     * will optimize for these conversions.
     *
     * Generated from protobuf field <code>optional double conversions_value_per_cost = 166;</code>
     * @param float $var
     * @return $this
     */
    public function setConversionsValuePerCost($var)
    {
        GPBUtil::checkDouble($var);
        $this->conversions_value_per_cost = $var;

        return $this;
    }

    /**
     * The value of conversions from interactions divided by the number of ad
     * interactions. This only includes conversion actions which
     * include_in_conversions_metric attribute is set to true. If you use
     * conversion-based bidding, your bid strategies will optimize for these
     * conversions.
     *
     * Generated from protobuf field <code>optional double conversions_from_interactions_value_per_interaction = 167;</code>
     * @return float
     */
    public function getConversionsFromInteractionsValuePerInteraction()
    {
        return isset($this->conversions_from_interactions_value_per_interaction) ? $this->conversions_from_interactions_value_per_interaction : 0.0;
    }

    public function hasConversionsFromInteractionsValuePerInteraction()
    {
        return isset($this->conversions_from_interactions_value_per_interaction);
    }

    public function clearConversionsFromInteractionsValuePerInteraction()
    {
        unset($this->conversions_from_interactions_value_per_interaction);
    }

    /**
     * The value of conversions from interactions divided by the number of ad
     * interactions. This only includes conversion actions which
     * include_in_conversions_metric attribute is set to true. If you use
     * conversion-based bidding, your bid strategies will optimize for these
     * conversions.
     *
     * Generated from protobuf field <code>optional double conversions_from_interactions_value_per_interaction = 167;</code>
     * @param float $var
     * @return $this
     */
    public function setConversionsFromInteractionsValuePerInteraction($var)
    {
        GPBUtil::checkDouble($var);
        $this->conversions_from_interactions_value_per_interaction = $var;

        return $this;
    }

    /**
     * The number of conversions. This only includes conversion actions which
     * include_in_conversions_metric attribute is set to true. If you use
     * conversion-based bidding, your bid strategies will optimize for these
     * conversions.
     *
     * Generated from protobuf field <code>optional double conversions = 168;</code>
     * @return float
     */
    public function getConversions()
    {
        return isset($this->conversions) ? $this->conversions : 0.0;
    }

    public function hasConversions()
    {
        return isset($this->conversions);
    }

    public function clearConversions()
    {
        unset($this->conversions);
    }

    /**
     * The number of conversions. This only includes conversion actions which
     * include_in_conversions_metric attribute is set to true. If you use
     * conversion-based bidding, your bid strategies will optimize for these
     * conversions.
     *
     * Generated from protobuf field <code>optional double conversions = 168;</code>
     * @param float $var
     * @return $this
     */
    public function setConversions($var)
    {
        GPBUtil::checkDouble($var);
        $this->conversions = $var;

        return $this;
    }

    /**
     * The number of conversions. This only includes conversion actions which
     * include_in_conversions_metric attribute is set to true. If you use
     * conversion-based bidding, your bid strategies will optimize for these
     * conversions. When this column is selected with date, the values in date
     * column means the conversion date. Details for the by_conversion_date
     * columns are available at
     * https://support.google.com/google-ads/answer/9549009.
     *
     * Generated from protobuf field <code>double conversions_by_conversion_date = 243;</code>
     * @return float
     */
    public function getConversionsByConversionDate()
    {
        return $this->conversions_by_conversion_date;
    }

    /**
     * The number of conversions. This only includes conversion actions which
     * include_in_conversions_metric attribute is set to true. If you use
     * conversion-based bidding, your bid strategies will optimize for these
     * conversions. When this column is selected with date, the values in date
     * column means the conversion date. Details for the by_conversion_date
     * columns are available at
     * https://support.google.com/google-ads/answer/9549009.
     *
     * Generated from protobuf field <code>double conversions_by_conversion_date = 243;</code>
     * @param float $var
     * @return $this
     */
    public function setConversionsByConversionDate($var)
    {
        GPBUtil::checkDouble($var);
        $this->conversions_by_conversion_date = $var;

        return $this;
    }

    /**
     * The sum of your cost-per-click (CPC) and cost-per-thousand impressions
     * (CPM) costs during this period.
     *
     * Generated from protobuf field <code>optional int64 cost_micros = 169;</code>
     * @return int|string
     */
    public function getCostMicros()
    {
        return isset($this->cost_micros) ? $this->cost_micros : 0;
    }

    public function hasCostMicros()
    {
        return isset($this->cost_micros);
    }

    public function clearCostMicros()
    {
        unset($this->cost_micros);
    }

    /**
     * The sum of your cost-per-click (CPC) and cost-per-thousand impressions
     * (CPM) costs during this period.
     *
     * Generated from protobuf field <code>optional int64 cost_micros = 169;</code>
     * @param int|string $var
     * @return $this
     */
    public function setCostMicros($var)
    {
        GPBUtil::checkInt64($var);
        $this->cost_micros = $var;

        return $this;
    }

    /**
     * The cost of ad interactions divided by all conversions.
     *
     * Generated from protobuf field <code>optional double cost_per_all_conversions = 170;</code>
     * @return float
     */
    public function getCostPerAllConversions()
    {
        return isset($this->cost_per_all_conversions) ? $this->cost_per_all_conversions : 0.0;
    }

    public function hasCostPerAllConversions()
    {
        return isset($this->cost_per_all_conversions);
    }

    public function clearCostPerAllConversions()
    {
        unset($this->cost_per_all_conversions);
    }

    /**
     * The cost of ad interactions divided by all conversions.
     *
     * Generated from protobuf field <code>optional double cost_per_all_conversions = 170;</code>
     * @param float $var
     * @return $this
     */
    public function setCostPerAllConversions($var)
    {
        GPBUtil::checkDouble($var);
        $this->cost_per_all_conversions = $var;

        return $this;
    }

    /**
     * The cost of ad interactions divided by conversions. This only includes
     * conversion actions which include_in_conversions_metric attribute is set to
     * true. If you use conversion-based bidding, your bid strategies will
     * optimize for these conversions.
     *
     * Generated from protobuf field <code>optional double cost_per_conversion = 171;</code>
     * @return float
     */
    public function getCostPerConversion()
    {
        return isset($this->cost_per_conversion) ? $this->cost_per_conversion : 0.0;
    }

    public function hasCostPerConversion()
    {
        return isset($this->cost_per_conversion);
    }

    public function clearCostPerConversion()
    {
        unset($this->cost_per_conversion);
    }

    /**
     * The cost of ad interactions divided by conversions. This only includes
     * conversion actions which include_in_conversions_metric attribute is set to
     * true. If you use conversion-based bidding, your bid strategies will
     * optimize for these conversions.
     *
     * Generated from protobuf field <code>optional double cost_per_conversion = 171;</code>
     * @param float $var
     * @return $this
     */
    public function setCostPerConversion($var)
    {
        GPBUtil::checkDouble($var);
        $this->cost_per_conversion = $var;

        return $this;
    }

    /**
     * The cost of ad interactions divided by current model attributed
     * conversions. This only includes conversion actions which
     * include_in_conversions_metric attribute is set to true. If you use
     * conversion-based bidding, your bid strategies will optimize for these
     * conversions.
     *
     * Generated from protobuf field <code>optional double cost_per_current_model_attributed_conversion = 172;</code>
     * @return float
     */
    public function getCostPerCurrentModelAttributedConversion()
    {
        return isset($this->cost_per_current_model_attributed_conversion) ? $this->cost_per_current_model_attributed_conversion : 0.0;
    }

    public function hasCostPerCurrentModelAttributedConversion()
    {
        return isset($this->cost_per_current_model_attributed_conversion);
    }

    public function clearCostPerCurrentModelAttributedConversion()
    {
        unset($this->cost_per_current_model_attributed_conversion);
    }

    /**
     * The cost of ad interactions divided by current model attributed
     * conversions. This only includes conversion actions which
     * include_in_conversions_metric attribute is set to true. If you use
     * conversion-based bidding, your bid strategies will optimize for these
     * conversions.
     *
     * Generated from protobuf field <code>optional double cost_per_current_model_attributed_conversion = 172;</code>
     * @param float $var
     * @return $this
     */
    public function setCostPerCurrentModelAttributedConversion($var)
    {
        GPBUtil::checkDouble($var);
        $this->cost_per_current_model_attributed_conversion = $var;

        return $this;
    }

    /**
     * Conversions from when a customer clicks on a Google Ads ad on one device,
     * then converts on a different device or browser.
     * Cross-device conversions are already included in all_conversions.
     *
     * Generated from protobuf field <code>optional double cross_device_conversions = 173;</code>
     * @return float
     */
    public function getCrossDeviceConversions()
    {
        return isset($this->cross_device_conversions) ? $this->cross_device_conversions : 0.0;
    }

    public function hasCrossDeviceConversions()
    {
        return isset($this->cross_device_conversions);
    }

    public function clearCrossDeviceConversions()
    {
        unset($this->cross_device_conversions);
    }

    /**
     * Conversions from when a customer clicks on a Google Ads ad on one device,
     * then converts on a different device or browser.
     * Cross-device conversions are already included in all_conversions.
     *
     * Generated from protobuf field <code>optional double cross_device_conversions = 173;</code>
     * @param float $var
     * @return $this
     */
    public function setCrossDeviceConversions($var)
    {
        GPBUtil::checkDouble($var);
        $this->cross_device_conversions = $var;

        return $this;
    }

    /**
     * The sum of the value of cross-device conversions, in micros.
     *
     * Generated from protobuf field <code>optional int64 cross_device_conversions_value_micros = 312;</code>
     * @return int|string
     */
    public function getCrossDeviceConversionsValueMicros()
    {
        return isset($this->cross_device_conversions_value_micros) ? $this->cross_device_conversions_value_micros : 0;
    }

    public function hasCrossDeviceConversionsValueMicros()
    {
        return isset($this->cross_device_conversions_value_micros);
    }

    public function clearCrossDeviceConversionsValueMicros()
    {
        unset($this->cross_device_conversions_value_micros);
    }

    /**
     * The sum of the value of cross-device conversions, in micros.
     *
     * Generated from protobuf field <code>optional int64 cross_device_conversions_value_micros = 312;</code>
     * @param int|string $var
     * @return $this
     */
    public function setCrossDeviceConversionsValueMicros($var)
    {
        GPBUtil::checkInt64($var);
        $this->cross_device_conversions_value_micros = $var;

        return $this;
    }

    /**
     * The number of clicks your ad receives (Clicks) divided by the number
     * of times your ad is shown (Impressions).
     *
     * Generated from protobuf field <code>optional double ctr = 174;</code>
     * @return float
     */
    public function getCtr()
    {
        return isset($this->ctr) ? $this->ctr : 0.0;
    }

    public function hasCtr()
    {
        return isset($this->ctr);
    }

    public function clearCtr()
    {
        unset($this->ctr);
    }

    /**
     * The number of clicks your ad receives (Clicks) divided by the number
     * of times your ad is shown (Impressions).
     *
     * Generated from protobuf field <code>optional double ctr = 174;</code>
     * @param float $var
     * @return $this
     */
    public function setCtr($var)
    {
        GPBUtil::checkDouble($var);
        $this->ctr = $var;

        return $this;
    }

    /**
     * Shows how your historic conversions data would look under the attribution
     * model you've currently selected. This only includes conversion actions
     * which include_in_conversions_metric attribute is set to true. If you use
     * conversion-based bidding, your bid strategies will optimize for these
     * conversions.
     *
     * Generated from protobuf field <code>optional double current_model_attributed_conversions = 175;</code>
     * @return float
     */
    public function getCurrentModelAttributedConversions()
    {
        return isset($this->current_model_attributed_conversions) ? $this->current_model_attributed_conversions : 0.0;
    }

    public function hasCurrentModelAttributedConversions()
    {
        return isset($this->current_model_attributed_conversions);
    }

    public function clearCurrentModelAttributedConversions()
    {
        unset($this->current_model_attributed_conversions);
    }

    /**
     * Shows how your historic conversions data would look under the attribution
     * model you've currently selected. This only includes conversion actions
     * which include_in_conversions_metric attribute is set to true. If you use
     * conversion-based bidding, your bid strategies will optimize for these
     * conversions.
     *
     * Generated from protobuf field <code>optional double current_model_attributed_conversions = 175;</code>
     * @param float $var
     * @return $this
     */
    public function setCurrentModelAttributedConversions($var)
    {
        GPBUtil::checkDouble($var);
        $this->current_model_attributed_conversions = $var;

        return $this;
    }

    /**
     * Current model attributed conversions from interactions divided by the
     * number of ad interactions (such as clicks for text ads or views for video
     * ads). This only includes conversion actions which
     * include_in_conversions_metric attribute is set to true. If you use
     * conversion-based bidding, your bid strategies will optimize for these
     * conversions.
     *
     * Generated from protobuf field <code>optional double current_model_attributed_conversions_from_interactions_rate = 176;</code>
     * @return float
     */
    public function getCurrentModelAttributedConversionsFromInteractionsRate()
    {
        return isset($this->current_model_attributed_conversions_from_interactions_rate) ? $this->current_model_attributed_conversions_from_interactions_rate : 0.0;
    }

    public function hasCurrentModelAttributedConversionsFromInteractionsRate()
    {
        return isset($this->current_model_attributed_conversions_from_interactions_rate);
    }

    public function clearCurrentModelAttributedConversionsFromInteractionsRate()
    {
        unset($this->current_model_attributed_conversions_from_interactions_rate);
    }

    /**
     * Current model attributed conversions from interactions divided by the
     * number of ad interactions (such as clicks for text ads or views for video
     * ads). This only includes conversion actions which
     * include_in_conversions_metric attribute is set to true. If you use
     * conversion-based bidding, your bid strategies will optimize for these
     * conversions.
     *
     * Generated from protobuf field <code>optional double current_model_attributed_conversions_from_interactions_rate = 176;</code>
     * @param float $var
     * @return $this
     */
    public function setCurrentModelAttributedConversionsFromInteractionsRate($var)
    {
        GPBUtil::checkDouble($var);
        $this->current_model_attributed_conversions_from_interactions_rate = $var;

        return $this;
    }

    /**
     * The value of current model attributed conversions from interactions divided
     * by the number of ad interactions. This only includes conversion actions
     * which include_in_conversions_metric attribute is set to true. If you use
     * conversion-based bidding, your bid strategies will optimize for these
     * conversions.
     *
     * Generated from protobuf field <code>optional double current_model_attributed_conversions_from_interactions_value_per_interaction = 177;</code>
     * @return float
     */
    public function getCurrentModelAttributedConversionsFromInteractionsValuePerInteraction()
    {
        return isset($this->current_model_attributed_conversions_from_interactions_value_per_interaction) ? $this->current_model_attributed_conversions_from_interactions_value_per_interaction : 0.0;
    }

    public function hasCurrentModelAttributedConversionsFromInteractionsValuePerInteraction()
    {
        return isset($this->current_model_attributed_conversions_from_interactions_value_per_interaction);
    }

    public function clearCurrentModelAttributedConversionsFromInteractionsValuePerInteraction()
    {
        unset($this->current_model_attributed_conversions_from_interactions_value_per_interaction);
    }

    /**
     * The value of current model attributed conversions from interactions divided
     * by the number of ad interactions. This only includes conversion actions
     * which include_in_conversions_metric attribute is set to true. If you use
     * conversion-based bidding, your bid strategies will optimize for these
     * conversions.
     *
     * Generated from protobuf field <code>optional double current_model_attributed_conversions_from_interactions_value_per_interaction = 177;</code>
     * @param float $var
     * @return $this
     */
    public function setCurrentModelAttributedConversionsFromInteractionsValuePerInteraction($var)
    {
        GPBUtil::checkDouble($var);
        $this->current_model_attributed_conversions_from_interactions_value_per_interaction = $var;

        return $this;
    }

    /**
     * The value of current model attributed conversions. This only includes
     * conversion actions which include_in_conversions_metric attribute is set to
     * true. If you use conversion-based bidding, your bid strategies will
     * optimize for these conversions.
     *
     * Generated from protobuf field <code>optional double current_model_attributed_conversions_value = 178;</code>
     * @return float
     */
    public function getCurrentModelAttributedConversionsValue()
    {
        return isset($this->current_model_attributed_conversions_value) ? $this->current_model_attributed_conversions_value : 0.0;
    }

    public function hasCurrentModelAttributedConversionsValue()
    {
        return isset($this->current_model_attributed_conversions_value);
    }

    public function clearCurrentModelAttributedConversionsValue()
    {
        unset($this->current_model_attributed_conversions_value);
    }

    /**
     * The value of current model attributed conversions. This only includes
     * conversion actions which include_in_conversions_metric attribute is set to
     * true. If you use conversion-based bidding, your bid strategies will
     * optimize for these conversions.
     *
     * Generated from protobuf field <code>optional double current_model_attributed_conversions_value = 178;</code>
     * @param float $var
     * @return $this
     */
    public function setCurrentModelAttributedConversionsValue($var)
    {
        GPBUtil::checkDouble($var);
        $this->current_model_attributed_conversions_value = $var;

        return $this;
    }

    /**
     * The value of current model attributed conversions divided by the cost of ad
     * interactions. This only includes conversion actions which
     * include_in_conversions_metric attribute is set to true. If you use
     * conversion-based bidding, your bid strategies will optimize for these
     * conversions.
     *
     * Generated from protobuf field <code>optional double current_model_attributed_conversions_value_per_cost = 179;</code>
     * @return float
     */
    public function getCurrentModelAttributedConversionsValuePerCost()
    {
        return isset($this->current_model_attributed_conversions_value_per_cost) ? $this->current_model_attributed_conversions_value_per_cost : 0.0;
    }

    public function hasCurrentModelAttributedConversionsValuePerCost()
    {
        return isset($this->current_model_attributed_conversions_value_per_cost);
    }

    public function clearCurrentModelAttributedConversionsValuePerCost()
    {
        unset($this->current_model_attributed_conversions_value_per_cost);
    }

    /**
     * The value of current model attributed conversions divided by the cost of ad
     * interactions. This only includes conversion actions which
     * include_in_conversions_metric attribute is set to true. If you use
     * conversion-based bidding, your bid strategies will optimize for these
     * conversions.
     *
     * Generated from protobuf field <code>optional double current_model_attributed_conversions_value_per_cost = 179;</code>
     * @param float $var
     * @return $this
     */
    public function setCurrentModelAttributedConversionsValuePerCost($var)
    {
        GPBUtil::checkDouble($var);
        $this->current_model_attributed_conversions_value_per_cost = $var;

        return $this;
    }

    /**
     * How often people engage with your ad after it's shown to them. This is the
     * number of ad expansions divided by the number of times your ad is shown.
     *
     * Generated from protobuf field <code>optional double engagement_rate = 180;</code>
     * @return float
     */
    public function getEngagementRate()
    {
        return isset($this->engagement_rate) ? $this->engagement_rate : 0.0;
    }

    public function hasEngagementRate()
    {
        return isset($this->engagement_rate);
    }

    public function clearEngagementRate()
    {
        unset($this->engagement_rate);
    }

    /**
     * How often people engage with your ad after it's shown to them. This is the
     * number of ad expansions divided by the number of times your ad is shown.
     *
     * Generated from protobuf field <code>optional double engagement_rate = 180;</code>
     * @param float $var
     * @return $this
     */
    public function setEngagementRate($var)
    {
        GPBUtil::checkDouble($var);
        $this->engagement_rate = $var;

        return $this;
    }

    /**
     * The number of engagements.
     * An engagement occurs when a viewer expands your Lightbox ad. Also, in the
     * future, other ad types may support engagement metrics.
     *
     * Generated from protobuf field <code>optional int64 engagements = 181;</code>
     * @return int|string
     */
    public function getEngagements()
    {
        return isset($this->engagements) ? $this->engagements : 0;
    }

    public function hasEngagements()
    {
        return isset($this->engagements);
    }

    public function clearEngagements()
    {
        unset($this->engagements);
    }

    /**
     * The number of engagements.
     * An engagement occurs when a viewer expands your Lightbox ad. Also, in the
     * future, other ad types may support engagement metrics.
     *
     * Generated from protobuf field <code>optional int64 engagements = 181;</code>
     * @param int|string $var
     * @return $this
     */
    public function setEngagements($var)
    {
        GPBUtil::checkInt64($var);
        $this->engagements = $var;

        return $this;
    }

    /**
     * Average lead value based on clicks.
     *
     * Generated from protobuf field <code>optional double hotel_average_lead_value_micros = 213;</code>
     * @return float
     */
    public function getHotelAverageLeadValueMicros()
    {
        return isset($this->hotel_average_lead_value_micros) ? $this->hotel_average_lead_value_micros : 0.0;
    }

    public function hasHotelAverageLeadValueMicros()
    {
        return isset($this->hotel_average_lead_value_micros);
    }

    public function clearHotelAverageLeadValueMicros()
    {
        unset($this->hotel_average_lead_value_micros);
    }

    /**
     * Average lead value based on clicks.
     *
     * Generated from protobuf field <code>optional double hotel_average_lead_value_micros = 213;</code>
     * @param float $var
     * @return $this
     */
    public function setHotelAverageLeadValueMicros($var)
    {
        GPBUtil::checkDouble($var);
        $this->hotel_average_lead_value_micros = $var;

        return $this;
    }

    /**
     * Commission bid rate in micros. A 20% commission is represented as
     * 200,000.
     *
     * Generated from protobuf field <code>optional int64 hotel_commission_rate_micros = 256;</code>
     * @return int|string
     */
    public function getHotelCommissionRateMicros()
    {
        return isset($this->hotel_commission_rate_micros) ? $this->hotel_commission_rate_micros : 0;
    }

    public function hasHotelCommissionRateMicros()
    {
        return isset($this->hotel_commission_rate_micros);
    }

    public function clearHotelCommissionRateMicros()
    {
        unset($this->hotel_commission_rate_micros);
    }

    /**
     * Commission bid rate in micros. A 20% commission is represented as
     * 200,000.
     *
     * Generated from protobuf field <code>optional int64 hotel_commission_rate_micros = 256;</code>
     * @param int|string $var
     * @return $this
     */
    public function setHotelCommissionRateMicros($var)
    {
        GPBUtil::checkInt64($var);
        $this->hotel_commission_rate_micros = $var;

        return $this;
    }

    /**
     * Expected commission cost. The result of multiplying the commission value
     * times the hotel_commission_rate in advertiser currency.
     *
     * Generated from protobuf field <code>optional double hotel_expected_commission_cost = 257;</code>
     * @return float
     */
    public function getHotelExpectedCommissionCost()
    {
        return isset($this->hotel_expected_commission_cost) ? $this->hotel_expected_commission_cost : 0.0;
    }

    public function hasHotelExpectedCommissionCost()
    {
        return isset($this->hotel_expected_commission_cost);
    }

    public function clearHotelExpectedCommissionCost()
    {
        unset($this->hotel_expected_commission_cost);
    }

    /**
     * Expected commission cost. The result of multiplying the commission value
     * times the hotel_commission_rate in advertiser currency.
     *
     * Generated from protobuf field <code>optional double hotel_expected_commission_cost = 257;</code>
     * @param float $var
     * @return $this
     */
    public function setHotelExpectedCommissionCost($var)
    {
        GPBUtil::checkDouble($var);
        $this->hotel_expected_commission_cost = $var;

        return $this;
    }

    /**
     * The average price difference between the price offered by reporting hotel
     * advertiser and the cheapest price offered by the competing advertiser.
     *
     * Generated from protobuf field <code>optional double hotel_price_difference_percentage = 214;</code>
     * @return float
     */
    public function getHotelPriceDifferencePercentage()
    {
        return isset($this->hotel_price_difference_percentage) ? $this->hotel_price_difference_percentage : 0.0;
    }

    public function hasHotelPriceDifferencePercentage()
    {
        return isset($this->hotel_price_difference_percentage);
    }

    public function clearHotelPriceDifferencePercentage()
    {
        unset($this->hotel_price_difference_percentage);
    }

    /**
     * The average price difference between the price offered by reporting hotel
     * advertiser and the cheapest price offered by the competing advertiser.
     *
     * Generated from protobuf field <code>optional double hotel_price_difference_percentage = 214;</code>
     * @param float $var
     * @return $this
     */
    public function setHotelPriceDifferencePercentage($var)
    {
        GPBUtil::checkDouble($var);
        $this->hotel_price_difference_percentage = $var;

        return $this;
    }

    /**
     * The number of impressions that hotel partners could have had given their
     * feed performance.
     *
     * Generated from protobuf field <code>optional int64 hotel_eligible_impressions = 215;</code>
     * @return int|string
     */
    public function getHotelEligibleImpressions()
    {
        return isset($this->hotel_eligible_impressions) ? $this->hotel_eligible_impressions : 0;
    }

    public function hasHotelEligibleImpressions()
    {
        return isset($this->hotel_eligible_impressions);
    }

    public function clearHotelEligibleImpressions()
    {
        unset($this->hotel_eligible_impressions);
    }

    /**
     * The number of impressions that hotel partners could have had given their
     * feed performance.
     *
     * Generated from protobuf field <code>optional int64 hotel_eligible_impressions = 215;</code>
     * @param int|string $var
     * @return $this
     */
    public function setHotelEligibleImpressions($var)
    {
        GPBUtil::checkInt64($var);
        $this->hotel_eligible_impressions = $var;

        return $this;
    }

    /**
     * The creative historical quality score.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.enums.QualityScoreBucketEnum.QualityScoreBucket historical_creative_quality_score = 80;</code>
     * @return int
     */
    public function getHistoricalCreativeQualityScore()
    {
        return $this->historical_creative_quality_score;
    }

    /**
     * The creative historical quality score.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.enums.QualityScoreBucketEnum.QualityScoreBucket historical_creative_quality_score = 80;</code>
     * @param int $var
     * @return $this
     */
    public function setHistoricalCreativeQualityScore($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V15\Enums\QualityScoreBucketEnum\QualityScoreBucket::class);
        $this->historical_creative_quality_score = $var;

        return $this;
    }

    /**
     * The quality of historical landing page experience.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.enums.QualityScoreBucketEnum.QualityScoreBucket historical_landing_page_quality_score = 81;</code>
     * @return int
     */
    public function getHistoricalLandingPageQualityScore()
    {
        return $this->historical_landing_page_quality_score;
    }

    /**
     * The quality of historical landing page experience.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.enums.QualityScoreBucketEnum.QualityScoreBucket historical_landing_page_quality_score = 81;</code>
     * @param int $var
     * @return $this
     */
    public function setHistoricalLandingPageQualityScore($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V15\Enums\QualityScoreBucketEnum\QualityScoreBucket::class);
        $this->historical_landing_page_quality_score = $var;

        return $this;
    }

    /**
     * The historical quality score.
     *
     * Generated from protobuf field <code>optional int64 historical_quality_score = 216;</code>
     * @return int|string
     */
    public function getHistoricalQualityScore()
    {
        return isset($this->historical_quality_score) ? $this->historical_quality_score : 0;
    }

    public function hasHistoricalQualityScore()
    {
        return isset($this->historical_quality_score);
    }

    public function clearHistoricalQualityScore()
    {
        unset($this->historical_quality_score);
    }

    /**
     * The historical quality score.
     *
     * Generated from protobuf field <code>optional int64 historical_quality_score = 216;</code>
     * @param int|string $var
     * @return $this
     */
    public function setHistoricalQualityScore($var)
    {
        GPBUtil::checkInt64($var);
        $this->historical_quality_score = $var;

        return $this;
    }

    /**
     * The historical search predicted click through rate (CTR).
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.enums.QualityScoreBucketEnum.QualityScoreBucket historical_search_predicted_ctr = 83;</code>
     * @return int
     */
    public function getHistoricalSearchPredictedCtr()
    {
        return $this->historical_search_predicted_ctr;
    }

    /**
     * The historical search predicted click through rate (CTR).
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.enums.QualityScoreBucketEnum.QualityScoreBucket historical_search_predicted_ctr = 83;</code>
     * @param int $var
     * @return $this
     */
    public function setHistoricalSearchPredictedCtr($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V15\Enums\QualityScoreBucketEnum\QualityScoreBucket::class);
        $this->historical_search_predicted_ctr = $var;

        return $this;
    }

    /**
     * The number of times the ad was forwarded to someone else as a message.
     *
     * Generated from protobuf field <code>optional int64 gmail_forwards = 217;</code>
     * @return int|string
     */
    public function getGmailForwards()
    {
        return isset($this->gmail_forwards) ? $this->gmail_forwards : 0;
    }

    public function hasGmailForwards()
    {
        return isset($this->gmail_forwards);
    }

    public function clearGmailForwards()
    {
        unset($this->gmail_forwards);
    }

    /**
     * The number of times the ad was forwarded to someone else as a message.
     *
     * Generated from protobuf field <code>optional int64 gmail_forwards = 217;</code>
     * @param int|string $var
     * @return $this
     */
    public function setGmailForwards($var)
    {
        GPBUtil::checkInt64($var);
        $this->gmail_forwards = $var;

        return $this;
    }

    /**
     * The number of times someone has saved your Gmail ad to their inbox as a
     * message.
     *
     * Generated from protobuf field <code>optional int64 gmail_saves = 218;</code>
     * @return int|string
     */
    public function getGmailSaves()
    {
        return isset($this->gmail_saves) ? $this->gmail_saves : 0;
    }

    public function hasGmailSaves()
    {
        return isset($this->gmail_saves);
    }

    public function clearGmailSaves()
    {
        unset($this->gmail_saves);
    }

    /**
     * The number of times someone has saved your Gmail ad to their inbox as a
     * message.
     *
     * Generated from protobuf field <code>optional int64 gmail_saves = 218;</code>
     * @param int|string $var
     * @return $this
     */
    public function setGmailSaves($var)
    {
        GPBUtil::checkInt64($var);
        $this->gmail_saves = $var;

        return $this;
    }

    /**
     * The number of clicks to the landing page on the expanded state of Gmail
     * ads.
     *
     * Generated from protobuf field <code>optional int64 gmail_secondary_clicks = 219;</code>
     * @return int|string
     */
    public function getGmailSecondaryClicks()
    {
        return isset($this->gmail_secondary_clicks) ? $this->gmail_secondary_clicks : 0;
    }

    public function hasGmailSecondaryClicks()
    {
        return isset($this->gmail_secondary_clicks);
    }

    public function clearGmailSecondaryClicks()
    {
        unset($this->gmail_secondary_clicks);
    }

    /**
     * The number of clicks to the landing page on the expanded state of Gmail
     * ads.
     *
     * Generated from protobuf field <code>optional int64 gmail_secondary_clicks = 219;</code>
     * @param int|string $var
     * @return $this
     */
    public function setGmailSecondaryClicks($var)
    {
        GPBUtil::checkInt64($var);
        $this->gmail_secondary_clicks = $var;

        return $this;
    }

    /**
     * The number of times a store's location-based ad was shown.
     * This metric applies to feed items only.
     *
     * Generated from protobuf field <code>optional int64 impressions_from_store_reach = 220;</code>
     * @return int|string
     */
    public function getImpressionsFromStoreReach()
    {
        return isset($this->impressions_from_store_reach) ? $this->impressions_from_store_reach : 0;
    }

    public function hasImpressionsFromStoreReach()
    {
        return isset($this->impressions_from_store_reach);
    }

    public function clearImpressionsFromStoreReach()
    {
        unset($this->impressions_from_store_reach);
    }

    /**
     * The number of times a store's location-based ad was shown.
     * This metric applies to feed items only.
     *
     * Generated from protobuf field <code>optional int64 impressions_from_store_reach = 220;</code>
     * @param int|string $var
     * @return $this
     */
    public function setImpressionsFromStoreReach($var)
    {
        GPBUtil::checkInt64($var);
        $this->impressions_from_store_reach = $var;

        return $this;
    }

    /**
     * Count of how often your ad has appeared on a search results page or
     * website on the Google Network.
     *
     * Generated from protobuf field <code>optional int64 impressions = 221;</code>
     * @return int|string
     */
    public function getImpressions()
    {
        return isset($this->impressions) ? $this->impressions : 0;
    }

    public function hasImpressions()
    {
        return isset($this->impressions);
    }

    public function clearImpressions()
    {
        unset($this->impressions);
    }

    /**
     * Count of how often your ad has appeared on a search results page or
     * website on the Google Network.
     *
     * Generated from protobuf field <code>optional int64 impressions = 221;</code>
     * @param int|string $var
     * @return $this
     */
    public function setImpressions($var)
    {
        GPBUtil::checkInt64($var);
        $this->impressions = $var;

        return $this;
    }

    /**
     * How often people interact with your ad after it is shown to them.
     * This is the number of interactions divided by the number of times your ad
     * is shown.
     *
     * Generated from protobuf field <code>optional double interaction_rate = 222;</code>
     * @return float
     */
    public function getInteractionRate()
    {
        return isset($this->interaction_rate) ? $this->interaction_rate : 0.0;
    }

    public function hasInteractionRate()
    {
        return isset($this->interaction_rate);
    }

    public function clearInteractionRate()
    {
        unset($this->interaction_rate);
    }

    /**
     * How often people interact with your ad after it is shown to them.
     * This is the number of interactions divided by the number of times your ad
     * is shown.
     *
     * Generated from protobuf field <code>optional double interaction_rate = 222;</code>
     * @param float $var
     * @return $this
     */
    public function setInteractionRate($var)
    {
        GPBUtil::checkDouble($var);
        $this->interaction_rate = $var;

        return $this;
    }

    /**
     * The number of interactions.
     * An interaction is the main user action associated with an ad format-clicks
     * for text and shopping ads, views for video ads, and so on.
     *
     * Generated from protobuf field <code>optional int64 interactions = 223;</code>
     * @return int|string
     */
    public function getInteractions()
    {
        return isset($this->interactions) ? $this->interactions : 0;
    }

    public function hasInteractions()
    {
        return isset($this->interactions);
    }

    public function clearInteractions()
    {
        unset($this->interactions);
    }

    /**
     * The number of interactions.
     * An interaction is the main user action associated with an ad format-clicks
     * for text and shopping ads, views for video ads, and so on.
     *
     * Generated from protobuf field <code>optional int64 interactions = 223;</code>
     * @param int|string $var
     * @return $this
     */
    public function setInteractions($var)
    {
        GPBUtil::checkInt64($var);
        $this->interactions = $var;

        return $this;
    }

    /**
     * The types of payable and free interactions.
     *
     * Generated from protobuf field <code>repeated .google.ads.googleads.v15.enums.InteractionEventTypeEnum.InteractionEventType interaction_event_types = 100;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getInteractionEventTypes()
    {
        return $this->interaction_event_types;
    }

    /**
     * The types of payable and free interactions.
     *
     * Generated from protobuf field <code>repeated .google.ads.googleads.v15.enums.InteractionEventTypeEnum.InteractionEventType interaction_event_types = 100;</code>
     * @param array<int>|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setInteractionEventTypes($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::ENUM, \Google\Ads\GoogleAds\V15\Enums\InteractionEventTypeEnum\InteractionEventType::class);
        $this->interaction_event_types = $arr;

        return $this;
    }

    /**
     * The percentage of clicks filtered out of your total number of clicks
     * (filtered + non-filtered clicks) during the reporting period.
     *
     * Generated from protobuf field <code>optional double invalid_click_rate = 224;</code>
     * @return float
     */
    public function getInvalidClickRate()
    {
        return isset($this->invalid_click_rate) ? $this->invalid_click_rate : 0.0;
    }

    public function hasInvalidClickRate()
    {
        return isset($this->invalid_click_rate);
    }

    public function clearInvalidClickRate()
    {
        unset($this->invalid_click_rate);
    }

    /**
     * The percentage of clicks filtered out of your total number of clicks
     * (filtered + non-filtered clicks) during the reporting period.
     *
     * Generated from protobuf field <code>optional double invalid_click_rate = 224;</code>
     * @param float $var
     * @return $this
     */
    public function setInvalidClickRate($var)
    {
        GPBUtil::checkDouble($var);
        $this->invalid_click_rate = $var;

        return $this;
    }

    /**
     * Number of clicks Google considers illegitimate and doesn't charge you for.
     *
     * Generated from protobuf field <code>optional int64 invalid_clicks = 225;</code>
     * @return int|string
     */
    public function getInvalidClicks()
    {
        return isset($this->invalid_clicks) ? $this->invalid_clicks : 0;
    }

    public function hasInvalidClicks()
    {
        return isset($this->invalid_clicks);
    }

    public function clearInvalidClicks()
    {
        unset($this->invalid_clicks);
    }

    /**
     * Number of clicks Google considers illegitimate and doesn't charge you for.
     *
     * Generated from protobuf field <code>optional int64 invalid_clicks = 225;</code>
     * @param int|string $var
     * @return $this
     */
    public function setInvalidClicks($var)
    {
        GPBUtil::checkInt64($var);
        $this->invalid_clicks = $var;

        return $this;
    }

    /**
     * Number of message chats initiated for Click To Message impressions that
     * were message tracking eligible.
     *
     * Generated from protobuf field <code>optional int64 message_chats = 226;</code>
     * @return int|string
     */
    public function getMessageChats()
    {
        return isset($this->message_chats) ? $this->message_chats : 0;
    }

    public function hasMessageChats()
    {
        return isset($this->message_chats);
    }

    public function clearMessageChats()
    {
        unset($this->message_chats);
    }

    /**
     * Number of message chats initiated for Click To Message impressions that
     * were message tracking eligible.
     *
     * Generated from protobuf field <code>optional int64 message_chats = 226;</code>
     * @param int|string $var
     * @return $this
     */
    public function setMessageChats($var)
    {
        GPBUtil::checkInt64($var);
        $this->message_chats = $var;

        return $this;
    }

    /**
     * Number of Click To Message impressions that were message tracking eligible.
     *
     * Generated from protobuf field <code>optional int64 message_impressions = 227;</code>
     * @return int|string
     */
    public function getMessageImpressions()
    {
        return isset($this->message_impressions) ? $this->message_impressions : 0;
    }

    public function hasMessageImpressions()
    {
        return isset($this->message_impressions);
    }

    public function clearMessageImpressions()
    {
        unset($this->message_impressions);
    }

    /**
     * Number of Click To Message impressions that were message tracking eligible.
     *
     * Generated from protobuf field <code>optional int64 message_impressions = 227;</code>
     * @param int|string $var
     * @return $this
     */
    public function setMessageImpressions($var)
    {
        GPBUtil::checkInt64($var);
        $this->message_impressions = $var;

        return $this;
    }

    /**
     * Number of message chats initiated (message_chats) divided by the number
     * of message impressions (message_impressions).
     * Rate at which a user initiates a message chat from an ad impression with
     * a messaging option and message tracking enabled.
     * Note that this rate can be more than 1.0 for a given message impression.
     *
     * Generated from protobuf field <code>optional double message_chat_rate = 228;</code>
     * @return float
     */
    public function getMessageChatRate()
    {
        return isset($this->message_chat_rate) ? $this->message_chat_rate : 0.0;
    }

    public function hasMessageChatRate()
    {
        return isset($this->message_chat_rate);
    }

    public function clearMessageChatRate()
    {
        unset($this->message_chat_rate);
    }

    /**
     * Number of message chats initiated (message_chats) divided by the number
     * of message impressions (message_impressions).
     * Rate at which a user initiates a message chat from an ad impression with
     * a messaging option and message tracking enabled.
     * Note that this rate can be more than 1.0 for a given message impression.
     *
     * Generated from protobuf field <code>optional double message_chat_rate = 228;</code>
     * @param float $var
     * @return $this
     */
    public function setMessageChatRate($var)
    {
        GPBUtil::checkDouble($var);
        $this->message_chat_rate = $var;

        return $this;
    }

    /**
     * The percentage of mobile clicks that go to a mobile-friendly page.
     *
     * Generated from protobuf field <code>optional double mobile_friendly_clicks_percentage = 229;</code>
     * @return float
     */
    public function getMobileFriendlyClicksPercentage()
    {
        return isset($this->mobile_friendly_clicks_percentage) ? $this->mobile_friendly_clicks_percentage : 0.0;
    }

    public function hasMobileFriendlyClicksPercentage()
    {
        return isset($this->mobile_friendly_clicks_percentage);
    }

    public function clearMobileFriendlyClicksPercentage()
    {
        unset($this->mobile_friendly_clicks_percentage);
    }

    /**
     * The percentage of mobile clicks that go to a mobile-friendly page.
     *
     * Generated from protobuf field <code>optional double mobile_friendly_clicks_percentage = 229;</code>
     * @param float $var
     * @return $this
     */
    public function setMobileFriendlyClicksPercentage($var)
    {
        GPBUtil::checkDouble($var);
        $this->mobile_friendly_clicks_percentage = $var;

        return $this;
    }

    /**
     * Total optimization score uplift of all recommendations.
     *
     * Generated from protobuf field <code>optional double optimization_score_uplift = 247;</code>
     * @return float
     */
    public function getOptimizationScoreUplift()
    {
        return isset($this->optimization_score_uplift) ? $this->optimization_score_uplift : 0.0;
    }

    public function hasOptimizationScoreUplift()
    {
        return isset($this->optimization_score_uplift);
    }

    public function clearOptimizationScoreUplift()
    {
        unset($this->optimization_score_uplift);
    }

    /**
     * Total optimization score uplift of all recommendations.
     *
     * Generated from protobuf field <code>optional double optimization_score_uplift = 247;</code>
     * @param float $var
     * @return $this
     */
    public function setOptimizationScoreUplift($var)
    {
        GPBUtil::checkDouble($var);
        $this->optimization_score_uplift = $var;

        return $this;
    }

    /**
     * URL for the optimization score page in the Google Ads web interface.
     * This metric can be selected from `customer` or `campaign`, and can be
     * segmented by `segments.recommendation_type`. For example, `SELECT
     * metrics.optimization_score_url, segments.recommendation_type FROM
     * customer` will return a URL for each unique (customer, recommendation_type)
     * combination.
     *
     * Generated from protobuf field <code>optional string optimization_score_url = 248;</code>
     * @return string
     */
    public function getOptimizationScoreUrl()
    {
        return isset($this->optimization_score_url) ? $this->optimization_score_url : '';
    }

    public function hasOptimizationScoreUrl()
    {
        return isset($this->optimization_score_url);
    }

    public function clearOptimizationScoreUrl()
    {
        unset($this->optimization_score_url);
    }

    /**
     * URL for the optimization score page in the Google Ads web interface.
     * This metric can be selected from `customer` or `campaign`, and can be
     * segmented by `segments.recommendation_type`. For example, `SELECT
     * metrics.optimization_score_url, segments.recommendation_type FROM
     * customer` will return a URL for each unique (customer, recommendation_type)
     * combination.
     *
     * Generated from protobuf field <code>optional string optimization_score_url = 248;</code>
     * @param string $var
     * @return $this
     */
    public function setOptimizationScoreUrl($var)
    {
        GPBUtil::checkString($var, True);
        $this->optimization_score_url = $var;

        return $this;
    }

    /**
     * The number of times someone clicked your site's listing in the unpaid
     * results for a particular query. See the help page at
     * https://support.google.com/google-ads/answer/3097241 for details.
     *
     * Generated from protobuf field <code>optional int64 organic_clicks = 230;</code>
     * @return int|string
     */
    public function getOrganicClicks()
    {
        return isset($this->organic_clicks) ? $this->organic_clicks : 0;
    }

    public function hasOrganicClicks()
    {
        return isset($this->organic_clicks);
    }

    public function clearOrganicClicks()
    {
        unset($this->organic_clicks);
    }

    /**
     * The number of times someone clicked your site's listing in the unpaid
     * results for a particular query. See the help page at
     * https://support.google.com/google-ads/answer/3097241 for details.
     *
     * Generated from protobuf field <code>optional int64 organic_clicks = 230;</code>
     * @param int|string $var
     * @return $this
     */
    public function setOrganicClicks($var)
    {
        GPBUtil::checkInt64($var);
        $this->organic_clicks = $var;

        return $this;
    }

    /**
     * The number of times someone clicked your site's listing in the unpaid
     * results (organic_clicks) divided by the total number of searches that
     * returned pages from your site (organic_queries). See the help page at
     * https://support.google.com/google-ads/answer/3097241 for details.
     *
     * Generated from protobuf field <code>optional double organic_clicks_per_query = 231;</code>
     * @return float
     */
    public function getOrganicClicksPerQuery()
    {
        return isset($this->organic_clicks_per_query) ? $this->organic_clicks_per_query : 0.0;
    }

    public function hasOrganicClicksPerQuery()
    {
        return isset($this->organic_clicks_per_query);
    }

    public function clearOrganicClicksPerQuery()
    {
        unset($this->organic_clicks_per_query);
    }

    /**
     * The number of times someone clicked your site's listing in the unpaid
     * results (organic_clicks) divided by the total number of searches that
     * returned pages from your site (organic_queries). See the help page at
     * https://support.google.com/google-ads/answer/3097241 for details.
     *
     * Generated from protobuf field <code>optional double organic_clicks_per_query = 231;</code>
     * @param float $var
     * @return $this
     */
    public function setOrganicClicksPerQuery($var)
    {
        GPBUtil::checkDouble($var);
        $this->organic_clicks_per_query = $var;

        return $this;
    }

    /**
     * The number of listings for your site in the unpaid search results. See the
     * help page at https://support.google.com/google-ads/answer/3097241 for
     * details.
     *
     * Generated from protobuf field <code>optional int64 organic_impressions = 232;</code>
     * @return int|string
     */
    public function getOrganicImpressions()
    {
        return isset($this->organic_impressions) ? $this->organic_impressions : 0;
    }

    public function hasOrganicImpressions()
    {
        return isset($this->organic_impressions);
    }

    public function clearOrganicImpressions()
    {
        unset($this->organic_impressions);
    }

    /**
     * The number of listings for your site in the unpaid search results. See the
     * help page at https://support.google.com/google-ads/answer/3097241 for
     * details.
     *
     * Generated from protobuf field <code>optional int64 organic_impressions = 232;</code>
     * @param int|string $var
     * @return $this
     */
    public function setOrganicImpressions($var)
    {
        GPBUtil::checkInt64($var);
        $this->organic_impressions = $var;

        return $this;
    }

    /**
     * The number of times a page from your site was listed in the unpaid search
     * results (organic_impressions) divided by the number of searches returning
     * your site's listing in the unpaid results (organic_queries). See the help
     * page at https://support.google.com/google-ads/answer/3097241 for details.
     *
     * Generated from protobuf field <code>optional double organic_impressions_per_query = 233;</code>
     * @return float
     */
    public function getOrganicImpressionsPerQuery()
    {
        return isset($this->organic_impressions_per_query) ? $this->organic_impressions_per_query : 0.0;
    }

    public function hasOrganicImpressionsPerQuery()
    {
        return isset($this->organic_impressions_per_query);
    }

    public function clearOrganicImpressionsPerQuery()
    {
        unset($this->organic_impressions_per_query);
    }

    /**
     * The number of times a page from your site was listed in the unpaid search
     * results (organic_impressions) divided by the number of searches returning
     * your site's listing in the unpaid results (organic_queries). See the help
     * page at https://support.google.com/google-ads/answer/3097241 for details.
     *
     * Generated from protobuf field <code>optional double organic_impressions_per_query = 233;</code>
     * @param float $var
     * @return $this
     */
    public function setOrganicImpressionsPerQuery($var)
    {
        GPBUtil::checkDouble($var);
        $this->organic_impressions_per_query = $var;

        return $this;
    }

    /**
     * The total number of searches that returned your site's listing in the
     * unpaid results. See the help page at
     * https://support.google.com/google-ads/answer/3097241 for details.
     *
     * Generated from protobuf field <code>optional int64 organic_queries = 234;</code>
     * @return int|string
     */
    public function getOrganicQueries()
    {
        return isset($this->organic_queries) ? $this->organic_queries : 0;
    }

    public function hasOrganicQueries()
    {
        return isset($this->organic_queries);
    }

    public function clearOrganicQueries()
    {
        unset($this->organic_queries);
    }

    /**
     * The total number of searches that returned your site's listing in the
     * unpaid results. See the help page at
     * https://support.google.com/google-ads/answer/3097241 for details.
     *
     * Generated from protobuf field <code>optional int64 organic_queries = 234;</code>
     * @param int|string $var
     * @return $this
     */
    public function setOrganicQueries($var)
    {
        GPBUtil::checkInt64($var);
        $this->organic_queries = $var;

        return $this;
    }

    /**
     * Percentage of first-time sessions (from people who had never visited your
     * site before). Imported from Google Analytics.
     *
     * Generated from protobuf field <code>optional double percent_new_visitors = 235;</code>
     * @return float
     */
    public function getPercentNewVisitors()
    {
        return isset($this->percent_new_visitors) ? $this->percent_new_visitors : 0.0;
    }

    public function hasPercentNewVisitors()
    {
        return isset($this->percent_new_visitors);
    }

    public function clearPercentNewVisitors()
    {
        unset($this->percent_new_visitors);
    }

    /**
     * Percentage of first-time sessions (from people who had never visited your
     * site before). Imported from Google Analytics.
     *
     * Generated from protobuf field <code>optional double percent_new_visitors = 235;</code>
     * @param float $var
     * @return $this
     */
    public function setPercentNewVisitors($var)
    {
        GPBUtil::checkDouble($var);
        $this->percent_new_visitors = $var;

        return $this;
    }

    /**
     * Number of offline phone calls.
     *
     * Generated from protobuf field <code>optional int64 phone_calls = 236;</code>
     * @return int|string
     */
    public function getPhoneCalls()
    {
        return isset($this->phone_calls) ? $this->phone_calls : 0;
    }

    public function hasPhoneCalls()
    {
        return isset($this->phone_calls);
    }

    public function clearPhoneCalls()
    {
        unset($this->phone_calls);
    }

    /**
     * Number of offline phone calls.
     *
     * Generated from protobuf field <code>optional int64 phone_calls = 236;</code>
     * @param int|string $var
     * @return $this
     */
    public function setPhoneCalls($var)
    {
        GPBUtil::checkInt64($var);
        $this->phone_calls = $var;

        return $this;
    }

    /**
     * Number of offline phone impressions.
     *
     * Generated from protobuf field <code>optional int64 phone_impressions = 237;</code>
     * @return int|string
     */
    public function getPhoneImpressions()
    {
        return isset($this->phone_impressions) ? $this->phone_impressions : 0;
    }

    public function hasPhoneImpressions()
    {
        return isset($this->phone_impressions);
    }

    public function clearPhoneImpressions()
    {
        unset($this->phone_impressions);
    }

    /**
     * Number of offline phone impressions.
     *
     * Generated from protobuf field <code>optional int64 phone_impressions = 237;</code>
     * @param int|string $var
     * @return $this
     */
    public function setPhoneImpressions($var)
    {
        GPBUtil::checkInt64($var);
        $this->phone_impressions = $var;

        return $this;
    }

    /**
     * Number of phone calls received (phone_calls) divided by the number of
     * times your phone number is shown (phone_impressions).
     *
     * Generated from protobuf field <code>optional double phone_through_rate = 238;</code>
     * @return float
     */
    public function getPhoneThroughRate()
    {
        return isset($this->phone_through_rate) ? $this->phone_through_rate : 0.0;
    }

    public function hasPhoneThroughRate()
    {
        return isset($this->phone_through_rate);
    }

    public function clearPhoneThroughRate()
    {
        unset($this->phone_through_rate);
    }

    /**
     * Number of phone calls received (phone_calls) divided by the number of
     * times your phone number is shown (phone_impressions).
     *
     * Generated from protobuf field <code>optional double phone_through_rate = 238;</code>
     * @param float $var
     * @return $this
     */
    public function setPhoneThroughRate($var)
    {
        GPBUtil::checkDouble($var);
        $this->phone_through_rate = $var;

        return $this;
    }

    /**
     * Your clickthrough rate (Ctr) divided by the average clickthrough rate of
     * all advertisers on the websites that show your ads. Measures how your ads
     * perform on Display Network sites compared to other ads on the same sites.
     *
     * Generated from protobuf field <code>optional double relative_ctr = 239;</code>
     * @return float
     */
    public function getRelativeCtr()
    {
        return isset($this->relative_ctr) ? $this->relative_ctr : 0.0;
    }

    public function hasRelativeCtr()
    {
        return isset($this->relative_ctr);
    }

    public function clearRelativeCtr()
    {
        unset($this->relative_ctr);
    }

    /**
     * Your clickthrough rate (Ctr) divided by the average clickthrough rate of
     * all advertisers on the websites that show your ads. Measures how your ads
     * perform on Display Network sites compared to other ads on the same sites.
     *
     * Generated from protobuf field <code>optional double relative_ctr = 239;</code>
     * @param float $var
     * @return $this
     */
    public function setRelativeCtr($var)
    {
        GPBUtil::checkDouble($var);
        $this->relative_ctr = $var;

        return $this;
    }

    /**
     * The percentage of the customer's Shopping or Search ad impressions that are
     * shown in the most prominent Shopping position. See
     * https://support.google.com/google-ads/answer/7501826
     * for details. Any value below 0.1 is reported as 0.0999.
     *
     * Generated from protobuf field <code>optional double search_absolute_top_impression_share = 136;</code>
     * @return float
     */
    public function getSearchAbsoluteTopImpressionShare()
    {
        return isset($this->search_absolute_top_impression_share) ? $this->search_absolute_top_impression_share : 0.0;
    }

    public function hasSearchAbsoluteTopImpressionShare()
    {
        return isset($this->search_absolute_top_impression_share);
    }

    public function clearSearchAbsoluteTopImpressionShare()
    {
        unset($this->search_absolute_top_impression_share);
    }

    /**
     * The percentage of the customer's Shopping or Search ad impressions that are
     * shown in the most prominent Shopping position. See
     * https://support.google.com/google-ads/answer/7501826
     * for details. Any value below 0.1 is reported as 0.0999.
     *
     * Generated from protobuf field <code>optional double search_absolute_top_impression_share = 136;</code>
     * @param float $var
     * @return $this
     */
    public function setSearchAbsoluteTopImpressionShare($var)
    {
        GPBUtil::checkDouble($var);
        $this->search_absolute_top_impression_share = $var;

        return $this;
    }

    /**
     * The number estimating how often your ad wasn't the very first ad above the
     * organic search results due to a low budget. Note: Search
     * budget lost absolute top impression share is reported in the range of 0 to
     * 0.9. Any value above 0.9 is reported as 0.9001.
     *
     * Generated from protobuf field <code>optional double search_budget_lost_absolute_top_impression_share = 137;</code>
     * @return float
     */
    public function getSearchBudgetLostAbsoluteTopImpressionShare()
    {
        return isset($this->search_budget_lost_absolute_top_impression_share) ? $this->search_budget_lost_absolute_top_impression_share : 0.0;
    }

    public function hasSearchBudgetLostAbsoluteTopImpressionShare()
    {
        return isset($this->search_budget_lost_absolute_top_impression_share);
    }

    public function clearSearchBudgetLostAbsoluteTopImpressionShare()
    {
        unset($this->search_budget_lost_absolute_top_impression_share);
    }

    /**
     * The number estimating how often your ad wasn't the very first ad above the
     * organic search results due to a low budget. Note: Search
     * budget lost absolute top impression share is reported in the range of 0 to
     * 0.9. Any value above 0.9 is reported as 0.9001.
     *
     * Generated from protobuf field <code>optional double search_budget_lost_absolute_top_impression_share = 137;</code>
     * @param float $var
     * @return $this
     */
    public function setSearchBudgetLostAbsoluteTopImpressionShare($var)
    {
        GPBUtil::checkDouble($var);
        $this->search_budget_lost_absolute_top_impression_share = $var;

        return $this;
    }

    /**
     * The estimated percent of times that your ad was eligible to show on the
     * Search Network but didn't because your budget was too low. Note: Search
     * budget lost impression share is reported in the range of 0 to 0.9. Any
     * value above 0.9 is reported as 0.9001.
     *
     * Generated from protobuf field <code>optional double search_budget_lost_impression_share = 138;</code>
     * @return float
     */
    public function getSearchBudgetLostImpressionShare()
    {
        return isset($this->search_budget_lost_impression_share) ? $this->search_budget_lost_impression_share : 0.0;
    }

    public function hasSearchBudgetLostImpressionShare()
    {
        return isset($this->search_budget_lost_impression_share);
    }

    public function clearSearchBudgetLostImpressionShare()
    {
        unset($this->search_budget_lost_impression_share);
    }

    /**
     * The estimated percent of times that your ad was eligible to show on the
     * Search Network but didn't because your budget was too low. Note: Search
     * budget lost impression share is reported in the range of 0 to 0.9. Any
     * value above 0.9 is reported as 0.9001.
     *
     * Generated from protobuf field <code>optional double search_budget_lost_impression_share = 138;</code>
     * @param float $var
     * @return $this
     */
    public function setSearchBudgetLostImpressionShare($var)
    {
        GPBUtil::checkDouble($var);
        $this->search_budget_lost_impression_share = $var;

        return $this;
    }

    /**
     * The number estimating how often your ad didn't show anywhere above the
     * organic search results due to a low budget. Note: Search
     * budget lost top impression share is reported in the range of 0 to 0.9. Any
     * value above 0.9 is reported as 0.9001.
     *
     * Generated from protobuf field <code>optional double search_budget_lost_top_impression_share = 139;</code>
     * @return float
     */
    public function getSearchBudgetLostTopImpressionShare()
    {
        return isset($this->search_budget_lost_top_impression_share) ? $this->search_budget_lost_top_impression_share : 0.0;
    }

    public function hasSearchBudgetLostTopImpressionShare()
    {
        return isset($this->search_budget_lost_top_impression_share);
    }

    public function clearSearchBudgetLostTopImpressionShare()
    {
        unset($this->search_budget_lost_top_impression_share);
    }

    /**
     * The number estimating how often your ad didn't show anywhere above the
     * organic search results due to a low budget. Note: Search
     * budget lost top impression share is reported in the range of 0 to 0.9. Any
     * value above 0.9 is reported as 0.9001.
     *
     * Generated from protobuf field <code>optional double search_budget_lost_top_impression_share = 139;</code>
     * @param float $var
     * @return $this
     */
    public function setSearchBudgetLostTopImpressionShare($var)
    {
        GPBUtil::checkDouble($var);
        $this->search_budget_lost_top_impression_share = $var;

        return $this;
    }

    /**
     * The number of clicks you've received on the Search Network
     * divided by the estimated number of clicks you were eligible to receive.
     * Note: Search click share is reported in the range of 0.1 to 1. Any value
     * below 0.1 is reported as 0.0999.
     *
     * Generated from protobuf field <code>optional double search_click_share = 140;</code>
     * @return float
     */
    public function getSearchClickShare()
    {
        return isset($this->search_click_share) ? $this->search_click_share : 0.0;
    }

    public function hasSearchClickShare()
    {
        return isset($this->search_click_share);
    }

    public function clearSearchClickShare()
    {
        unset($this->search_click_share);
    }

    /**
     * The number of clicks you've received on the Search Network
     * divided by the estimated number of clicks you were eligible to receive.
     * Note: Search click share is reported in the range of 0.1 to 1. Any value
     * below 0.1 is reported as 0.0999.
     *
     * Generated from protobuf field <code>optional double search_click_share = 140;</code>
     * @param float $var
     * @return $this
     */
    public function setSearchClickShare($var)
    {
        GPBUtil::checkDouble($var);
        $this->search_click_share = $var;

        return $this;
    }

    /**
     * The impressions you've received divided by the estimated number of
     * impressions you were eligible to receive on the Search Network for search
     * terms that matched your keywords exactly (or were close variants of your
     * keyword), regardless of your keyword match types. Note: Search exact match
     * impression share is reported in the range of 0.1 to 1. Any value below 0.1
     * is reported as 0.0999.
     *
     * Generated from protobuf field <code>optional double search_exact_match_impression_share = 141;</code>
     * @return float
     */
    public function getSearchExactMatchImpressionShare()
    {
        return isset($this->search_exact_match_impression_share) ? $this->search_exact_match_impression_share : 0.0;
    }

    public function hasSearchExactMatchImpressionShare()
    {
        return isset($this->search_exact_match_impression_share);
    }

    public function clearSearchExactMatchImpressionShare()
    {
        unset($this->search_exact_match_impression_share);
    }

    /**
     * The impressions you've received divided by the estimated number of
     * impressions you were eligible to receive on the Search Network for search
     * terms that matched your keywords exactly (or were close variants of your
     * keyword), regardless of your keyword match types. Note: Search exact match
     * impression share is reported in the range of 0.1 to 1. Any value below 0.1
     * is reported as 0.0999.
     *
     * Generated from protobuf field <code>optional double search_exact_match_impression_share = 141;</code>
     * @param float $var
     * @return $this
     */
    public function setSearchExactMatchImpressionShare($var)
    {
        GPBUtil::checkDouble($var);
        $this->search_exact_match_impression_share = $var;

        return $this;
    }

    /**
     * The impressions you've received on the Search Network divided
     * by the estimated number of impressions you were eligible to receive.
     * Note: Search impression share is reported in the range of 0.1 to 1. Any
     * value below 0.1 is reported as 0.0999.
     *
     * Generated from protobuf field <code>optional double search_impression_share = 142;</code>
     * @return float
     */
    public function getSearchImpressionShare()
    {
        return isset($this->search_impression_share) ? $this->search_impression_share : 0.0;
    }

    public function hasSearchImpressionShare()
    {
        return isset($this->search_impression_share);
    }

    public function clearSearchImpressionShare()
    {
        unset($this->search_impression_share);
    }

    /**
     * The impressions you've received on the Search Network divided
     * by the estimated number of impressions you were eligible to receive.
     * Note: Search impression share is reported in the range of 0.1 to 1. Any
     * value below 0.1 is reported as 0.0999.
     *
     * Generated from protobuf field <code>optional double search_impression_share = 142;</code>
     * @param float $var
     * @return $this
     */
    public function setSearchImpressionShare($var)
    {
        GPBUtil::checkDouble($var);
        $this->search_impression_share = $var;

        return $this;
    }

    /**
     * The number estimating how often your ad wasn't the very first ad above the
     * organic search results due to poor Ad Rank.
     * Note: Search rank lost absolute top impression share is reported in the
     * range of 0 to 0.9. Any value above 0.9 is reported as 0.9001.
     *
     * Generated from protobuf field <code>optional double search_rank_lost_absolute_top_impression_share = 143;</code>
     * @return float
     */
    public function getSearchRankLostAbsoluteTopImpressionShare()
    {
        return isset($this->search_rank_lost_absolute_top_impression_share) ? $this->search_rank_lost_absolute_top_impression_share : 0.0;
    }

    public function hasSearchRankLostAbsoluteTopImpressionShare()
    {
        return isset($this->search_rank_lost_absolute_top_impression_share);
    }

    public function clearSearchRankLostAbsoluteTopImpressionShare()
    {
        unset($this->search_rank_lost_absolute_top_impression_share);
    }

    /**
     * The number estimating how often your ad wasn't the very first ad above the
     * organic search results due to poor Ad Rank.
     * Note: Search rank lost absolute top impression share is reported in the
     * range of 0 to 0.9. Any value above 0.9 is reported as 0.9001.
     *
     * Generated from protobuf field <code>optional double search_rank_lost_absolute_top_impression_share = 143;</code>
     * @param float $var
     * @return $this
     */
    public function setSearchRankLostAbsoluteTopImpressionShare($var)
    {
        GPBUtil::checkDouble($var);
        $this->search_rank_lost_absolute_top_impression_share = $var;

        return $this;
    }

    /**
     * The estimated percentage of impressions on the Search Network
     * that your ads didn't receive due to poor Ad Rank.
     * Note: Search rank lost impression share is reported in the range of 0 to
     * 0.9. Any value above 0.9 is reported as 0.9001.
     *
     * Generated from protobuf field <code>optional double search_rank_lost_impression_share = 144;</code>
     * @return float
     */
    public function getSearchRankLostImpressionShare()
    {
        return isset($this->search_rank_lost_impression_share) ? $this->search_rank_lost_impression_share : 0.0;
    }

    public function hasSearchRankLostImpressionShare()
    {
        return isset($this->search_rank_lost_impression_share);
    }

    public function clearSearchRankLostImpressionShare()
    {
        unset($this->search_rank_lost_impression_share);
    }

    /**
     * The estimated percentage of impressions on the Search Network
     * that your ads didn't receive due to poor Ad Rank.
     * Note: Search rank lost impression share is reported in the range of 0 to
     * 0.9. Any value above 0.9 is reported as 0.9001.
     *
     * Generated from protobuf field <code>optional double search_rank_lost_impression_share = 144;</code>
     * @param float $var
     * @return $this
     */
    public function setSearchRankLostImpressionShare($var)
    {
        GPBUtil::checkDouble($var);
        $this->search_rank_lost_impression_share = $var;

        return $this;
    }

    /**
     * The number estimating how often your ad didn't show anywhere above the
     * organic search results due to poor Ad Rank.
     * Note: Search rank lost top impression share is reported in the range of 0
     * to 0.9. Any value above 0.9 is reported as 0.9001.
     *
     * Generated from protobuf field <code>optional double search_rank_lost_top_impression_share = 145;</code>
     * @return float
     */
    public function getSearchRankLostTopImpressionShare()
    {
        return isset($this->search_rank_lost_top_impression_share) ? $this->search_rank_lost_top_impression_share : 0.0;
    }

    public function hasSearchRankLostTopImpressionShare()
    {
        return isset($this->search_rank_lost_top_impression_share);
    }

    public function clearSearchRankLostTopImpressionShare()
    {
        unset($this->search_rank_lost_top_impression_share);
    }

    /**
     * The number estimating how often your ad didn't show anywhere above the
     * organic search results due to poor Ad Rank.
     * Note: Search rank lost top impression share is reported in the range of 0
     * to 0.9. Any value above 0.9 is reported as 0.9001.
     *
     * Generated from protobuf field <code>optional double search_rank_lost_top_impression_share = 145;</code>
     * @param float $var
     * @return $this
     */
    public function setSearchRankLostTopImpressionShare($var)
    {
        GPBUtil::checkDouble($var);
        $this->search_rank_lost_top_impression_share = $var;

        return $this;
    }

    /**
     * The impressions you've received in the top location (anywhere above the
     * organic search results) compared to the estimated number of impressions you
     * were eligible to receive in the top location.
     * Note: Search top impression share is reported in the range of 0.1 to 1. Any
     * value below 0.1 is reported as 0.0999.
     *
     * Generated from protobuf field <code>optional double search_top_impression_share = 146;</code>
     * @return float
     */
    public function getSearchTopImpressionShare()
    {
        return isset($this->search_top_impression_share) ? $this->search_top_impression_share : 0.0;
    }

    public function hasSearchTopImpressionShare()
    {
        return isset($this->search_top_impression_share);
    }

    public function clearSearchTopImpressionShare()
    {
        unset($this->search_top_impression_share);
    }

    /**
     * The impressions you've received in the top location (anywhere above the
     * organic search results) compared to the estimated number of impressions you
     * were eligible to receive in the top location.
     * Note: Search top impression share is reported in the range of 0.1 to 1. Any
     * value below 0.1 is reported as 0.0999.
     *
     * Generated from protobuf field <code>optional double search_top_impression_share = 146;</code>
     * @param float $var
     * @return $this
     */
    public function setSearchTopImpressionShare($var)
    {
        GPBUtil::checkDouble($var);
        $this->search_top_impression_share = $var;

        return $this;
    }

    /**
     * Search volume range for a search term insight category.
     *
     * Generated from protobuf field <code>optional .google.ads.googleads.v15.common.SearchVolumeRange search_volume = 295;</code>
     * @return \Google\Ads\GoogleAds\V15\Common\SearchVolumeRange|null
     */
    public function getSearchVolume()
    {
        return $this->search_volume;
    }

    public function hasSearchVolume()
    {
        return isset($this->search_volume);
    }

    public function clearSearchVolume()
    {
        unset($this->search_volume);
    }

    /**
     * Search volume range for a search term insight category.
     *
     * Generated from protobuf field <code>optional .google.ads.googleads.v15.common.SearchVolumeRange search_volume = 295;</code>
     * @param \Google\Ads\GoogleAds\V15\Common\SearchVolumeRange $var
     * @return $this
     */
    public function setSearchVolume($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Common\SearchVolumeRange::class);
        $this->search_volume = $var;

        return $this;
    }

    /**
     * A measure of how quickly your page loads after clicks on your mobile ads.
     * The score is a range from 1 to 10, 10 being the fastest.
     *
     * Generated from protobuf field <code>optional int64 speed_score = 147;</code>
     * @return int|string
     */
    public function getSpeedScore()
    {
        return isset($this->speed_score) ? $this->speed_score : 0;
    }

    public function hasSpeedScore()
    {
        return isset($this->speed_score);
    }

    public function clearSpeedScore()
    {
        unset($this->speed_score);
    }

    /**
     * A measure of how quickly your page loads after clicks on your mobile ads.
     * The score is a range from 1 to 10, 10 being the fastest.
     *
     * Generated from protobuf field <code>optional int64 speed_score = 147;</code>
     * @param int|string $var
     * @return $this
     */
    public function setSpeedScore($var)
    {
        GPBUtil::checkInt64($var);
        $this->speed_score = $var;

        return $this;
    }

    /**
     * The average Target CPA, or unset if not available (for example, for
     * campaigns that had traffic from portfolio bidding strategies or non-tCPA).
     *
     * Generated from protobuf field <code>optional int64 average_target_cpa_micros = 290;</code>
     * @return int|string
     */
    public function getAverageTargetCpaMicros()
    {
        return isset($this->average_target_cpa_micros) ? $this->average_target_cpa_micros : 0;
    }

    public function hasAverageTargetCpaMicros()
    {
        return isset($this->average_target_cpa_micros);
    }

    public function clearAverageTargetCpaMicros()
    {
        unset($this->average_target_cpa_micros);
    }

    /**
     * The average Target CPA, or unset if not available (for example, for
     * campaigns that had traffic from portfolio bidding strategies or non-tCPA).
     *
     * Generated from protobuf field <code>optional int64 average_target_cpa_micros = 290;</code>
     * @param int|string $var
     * @return $this
     */
    public function setAverageTargetCpaMicros($var)
    {
        GPBUtil::checkInt64($var);
        $this->average_target_cpa_micros = $var;

        return $this;
    }

    /**
     * The average Target ROAS, or unset if not available (for example, for
     * campaigns that had traffic from portfolio bidding strategies or non-tROAS).
     *
     * Generated from protobuf field <code>optional double average_target_roas = 250;</code>
     * @return float
     */
    public function getAverageTargetRoas()
    {
        return isset($this->average_target_roas) ? $this->average_target_roas : 0.0;
    }

    public function hasAverageTargetRoas()
    {
        return isset($this->average_target_roas);
    }

    public function clearAverageTargetRoas()
    {
        unset($this->average_target_roas);
    }

    /**
     * The average Target ROAS, or unset if not available (for example, for
     * campaigns that had traffic from portfolio bidding strategies or non-tROAS).
     *
     * Generated from protobuf field <code>optional double average_target_roas = 250;</code>
     * @param float $var
     * @return $this
     */
    public function setAverageTargetRoas($var)
    {
        GPBUtil::checkDouble($var);
        $this->average_target_roas = $var;

        return $this;
    }

    /**
     * The percent of your ad impressions that are shown anywhere above the
     * organic search results.
     *
     * Generated from protobuf field <code>optional double top_impression_percentage = 148;</code>
     * @return float
     */
    public function getTopImpressionPercentage()
    {
        return isset($this->top_impression_percentage) ? $this->top_impression_percentage : 0.0;
    }

    public function hasTopImpressionPercentage()
    {
        return isset($this->top_impression_percentage);
    }

    public function clearTopImpressionPercentage()
    {
        unset($this->top_impression_percentage);
    }

    /**
     * The percent of your ad impressions that are shown anywhere above the
     * organic search results.
     *
     * Generated from protobuf field <code>optional double top_impression_percentage = 148;</code>
     * @param float $var
     * @return $this
     */
    public function setTopImpressionPercentage($var)
    {
        GPBUtil::checkDouble($var);
        $this->top_impression_percentage = $var;

        return $this;
    }

    /**
     * The percentage of ad clicks to Accelerated Mobile Pages (AMP) landing pages
     * that reach a valid AMP page.
     *
     * Generated from protobuf field <code>optional double valid_accelerated_mobile_pages_clicks_percentage = 149;</code>
     * @return float
     */
    public function getValidAcceleratedMobilePagesClicksPercentage()
    {
        return isset($this->valid_accelerated_mobile_pages_clicks_percentage) ? $this->valid_accelerated_mobile_pages_clicks_percentage : 0.0;
    }

    public function hasValidAcceleratedMobilePagesClicksPercentage()
    {
        return isset($this->valid_accelerated_mobile_pages_clicks_percentage);
    }

    public function clearValidAcceleratedMobilePagesClicksPercentage()
    {
        unset($this->valid_accelerated_mobile_pages_clicks_percentage);
    }

    /**
     * The percentage of ad clicks to Accelerated Mobile Pages (AMP) landing pages
     * that reach a valid AMP page.
     *
     * Generated from protobuf field <code>optional double valid_accelerated_mobile_pages_clicks_percentage = 149;</code>
     * @param float $var
     * @return $this
     */
    public function setValidAcceleratedMobilePagesClicksPercentage($var)
    {
        GPBUtil::checkDouble($var);
        $this->valid_accelerated_mobile_pages_clicks_percentage = $var;

        return $this;
    }

    /**
     * The value of all conversions divided by the number of all conversions.
     *
     * Generated from protobuf field <code>optional double value_per_all_conversions = 150;</code>
     * @return float
     */
    public function getValuePerAllConversions()
    {
        return isset($this->value_per_all_conversions) ? $this->value_per_all_conversions : 0.0;
    }

    public function hasValuePerAllConversions()
    {
        return isset($this->value_per_all_conversions);
    }

    public function clearValuePerAllConversions()
    {
        unset($this->value_per_all_conversions);
    }

    /**
     * The value of all conversions divided by the number of all conversions.
     *
     * Generated from protobuf field <code>optional double value_per_all_conversions = 150;</code>
     * @param float $var
     * @return $this
     */
    public function setValuePerAllConversions($var)
    {
        GPBUtil::checkDouble($var);
        $this->value_per_all_conversions = $var;

        return $this;
    }

    /**
     * The value of all conversions divided by the number of all conversions. When
     * this column is selected with date, the values in date column means the
     * conversion date. Details for the by_conversion_date columns are available
     * at https://support.google.com/google-ads/answer/9549009.
     *
     * Generated from protobuf field <code>optional double value_per_all_conversions_by_conversion_date = 244;</code>
     * @return float
     */
    public function getValuePerAllConversionsByConversionDate()
    {
        return isset($this->value_per_all_conversions_by_conversion_date) ? $this->value_per_all_conversions_by_conversion_date : 0.0;
    }

    public function hasValuePerAllConversionsByConversionDate()
    {
        return isset($this->value_per_all_conversions_by_conversion_date);
    }

    public function clearValuePerAllConversionsByConversionDate()
    {
        unset($this->value_per_all_conversions_by_conversion_date);
    }

    /**
     * The value of all conversions divided by the number of all conversions. When
     * this column is selected with date, the values in date column means the
     * conversion date. Details for the by_conversion_date columns are available
     * at https://support.google.com/google-ads/answer/9549009.
     *
     * Generated from protobuf field <code>optional double value_per_all_conversions_by_conversion_date = 244;</code>
     * @param float $var
     * @return $this
     */
    public function setValuePerAllConversionsByConversionDate($var)
    {
        GPBUtil::checkDouble($var);
        $this->value_per_all_conversions_by_conversion_date = $var;

        return $this;
    }

    /**
     * The value of conversions divided by the number of conversions. This only
     * includes conversion actions which include_in_conversions_metric attribute
     * is set to true. If you use conversion-based bidding, your bid strategies
     * will optimize for these conversions.
     *
     * Generated from protobuf field <code>optional double value_per_conversion = 151;</code>
     * @return float
     */
    public function getValuePerConversion()
    {
        return isset($this->value_per_conversion) ? $this->value_per_conversion : 0.0;
    }

    public function hasValuePerConversion()
    {
        return isset($this->value_per_conversion);
    }

    public function clearValuePerConversion()
    {
        unset($this->value_per_conversion);
    }

    /**
     * The value of conversions divided by the number of conversions. This only
     * includes conversion actions which include_in_conversions_metric attribute
     * is set to true. If you use conversion-based bidding, your bid strategies
     * will optimize for these conversions.
     *
     * Generated from protobuf field <code>optional double value_per_conversion = 151;</code>
     * @param float $var
     * @return $this
     */
    public function setValuePerConversion($var)
    {
        GPBUtil::checkDouble($var);
        $this->value_per_conversion = $var;

        return $this;
    }

    /**
     * The value of conversions divided by the number of conversions. This only
     * includes conversion actions which include_in_conversions_metric attribute
     * is set to true. If you use conversion-based bidding, your bid strategies
     * will optimize for these conversions. When this column is selected with
     * date, the values in date column means the conversion date. Details for the
     * by_conversion_date columns are available at
     * https://support.google.com/google-ads/answer/9549009.
     *
     * Generated from protobuf field <code>optional double value_per_conversions_by_conversion_date = 245;</code>
     * @return float
     */
    public function getValuePerConversionsByConversionDate()
    {
        return isset($this->value_per_conversions_by_conversion_date) ? $this->value_per_conversions_by_conversion_date : 0.0;
    }

    public function hasValuePerConversionsByConversionDate()
    {
        return isset($this->value_per_conversions_by_conversion_date);
    }

    public function clearValuePerConversionsByConversionDate()
    {
        unset($this->value_per_conversions_by_conversion_date);
    }

    /**
     * The value of conversions divided by the number of conversions. This only
     * includes conversion actions which include_in_conversions_metric attribute
     * is set to true. If you use conversion-based bidding, your bid strategies
     * will optimize for these conversions. When this column is selected with
     * date, the values in date column means the conversion date. Details for the
     * by_conversion_date columns are available at
     * https://support.google.com/google-ads/answer/9549009.
     *
     * Generated from protobuf field <code>optional double value_per_conversions_by_conversion_date = 245;</code>
     * @param float $var
     * @return $this
     */
    public function setValuePerConversionsByConversionDate($var)
    {
        GPBUtil::checkDouble($var);
        $this->value_per_conversions_by_conversion_date = $var;

        return $this;
    }

    /**
     * The value of current model attributed conversions divided by the number of
     * the conversions. This only includes conversion actions which
     * include_in_conversions_metric attribute is set to true. If you use
     * conversion-based bidding, your bid strategies will optimize for these
     * conversions.
     *
     * Generated from protobuf field <code>optional double value_per_current_model_attributed_conversion = 152;</code>
     * @return float
     */
    public function getValuePerCurrentModelAttributedConversion()
    {
        return isset($this->value_per_current_model_attributed_conversion) ? $this->value_per_current_model_attributed_conversion : 0.0;
    }

    public function hasValuePerCurrentModelAttributedConversion()
    {
        return isset($this->value_per_current_model_attributed_conversion);
    }

    public function clearValuePerCurrentModelAttributedConversion()
    {
        unset($this->value_per_current_model_attributed_conversion);
    }

    /**
     * The value of current model attributed conversions divided by the number of
     * the conversions. This only includes conversion actions which
     * include_in_conversions_metric attribute is set to true. If you use
     * conversion-based bidding, your bid strategies will optimize for these
     * conversions.
     *
     * Generated from protobuf field <code>optional double value_per_current_model_attributed_conversion = 152;</code>
     * @param float $var
     * @return $this
     */
    public function setValuePerCurrentModelAttributedConversion($var)
    {
        GPBUtil::checkDouble($var);
        $this->value_per_current_model_attributed_conversion = $var;

        return $this;
    }

    /**
     * Percentage of impressions where the viewer watched all of your video.
     *
     * Generated from protobuf field <code>optional double video_quartile_p100_rate = 132;</code>
     * @return float
     */
    public function getVideoQuartileP100Rate()
    {
        return isset($this->video_quartile_p100_rate) ? $this->video_quartile_p100_rate : 0.0;
    }

    public function hasVideoQuartileP100Rate()
    {
        return isset($this->video_quartile_p100_rate);
    }

    public function clearVideoQuartileP100Rate()
    {
        unset($this->video_quartile_p100_rate);
    }

    /**
     * Percentage of impressions where the viewer watched all of your video.
     *
     * Generated from protobuf field <code>optional double video_quartile_p100_rate = 132;</code>
     * @param float $var
     * @return $this
     */
    public function setVideoQuartileP100Rate($var)
    {
        GPBUtil::checkDouble($var);
        $this->video_quartile_p100_rate = $var;

        return $this;
    }

    /**
     * Percentage of impressions where the viewer watched 25% of your video.
     *
     * Generated from protobuf field <code>optional double video_quartile_p25_rate = 133;</code>
     * @return float
     */
    public function getVideoQuartileP25Rate()
    {
        return isset($this->video_quartile_p25_rate) ? $this->video_quartile_p25_rate : 0.0;
    }

    public function hasVideoQuartileP25Rate()
    {
        return isset($this->video_quartile_p25_rate);
    }

    public function clearVideoQuartileP25Rate()
    {
        unset($this->video_quartile_p25_rate);
    }

    /**
     * Percentage of impressions where the viewer watched 25% of your video.
     *
     * Generated from protobuf field <code>optional double video_quartile_p25_rate = 133;</code>
     * @param float $var
     * @return $this
     */
    public function setVideoQuartileP25Rate($var)
    {
        GPBUtil::checkDouble($var);
        $this->video_quartile_p25_rate = $var;

        return $this;
    }

    /**
     * Percentage of impressions where the viewer watched 50% of your video.
     *
     * Generated from protobuf field <code>optional double video_quartile_p50_rate = 134;</code>
     * @return float
     */
    public function getVideoQuartileP50Rate()
    {
        return isset($this->video_quartile_p50_rate) ? $this->video_quartile_p50_rate : 0.0;
    }

    public function hasVideoQuartileP50Rate()
    {
        return isset($this->video_quartile_p50_rate);
    }

    public function clearVideoQuartileP50Rate()
    {
        unset($this->video_quartile_p50_rate);
    }

    /**
     * Percentage of impressions where the viewer watched 50% of your video.
     *
     * Generated from protobuf field <code>optional double video_quartile_p50_rate = 134;</code>
     * @param float $var
     * @return $this
     */
    public function setVideoQuartileP50Rate($var)
    {
        GPBUtil::checkDouble($var);
        $this->video_quartile_p50_rate = $var;

        return $this;
    }

    /**
     * Percentage of impressions where the viewer watched 75% of your video.
     *
     * Generated from protobuf field <code>optional double video_quartile_p75_rate = 135;</code>
     * @return float
     */
    public function getVideoQuartileP75Rate()
    {
        return isset($this->video_quartile_p75_rate) ? $this->video_quartile_p75_rate : 0.0;
    }

    public function hasVideoQuartileP75Rate()
    {
        return isset($this->video_quartile_p75_rate);
    }

    public function clearVideoQuartileP75Rate()
    {
        unset($this->video_quartile_p75_rate);
    }

    /**
     * Percentage of impressions where the viewer watched 75% of your video.
     *
     * Generated from protobuf field <code>optional double video_quartile_p75_rate = 135;</code>
     * @param float $var
     * @return $this
     */
    public function setVideoQuartileP75Rate($var)
    {
        GPBUtil::checkDouble($var);
        $this->video_quartile_p75_rate = $var;

        return $this;
    }

    /**
     * The number of views your TrueView video ad receives divided by its number
     * of impressions, including thumbnail impressions for TrueView in-display
     * ads.
     *
     * Generated from protobuf field <code>optional double video_view_rate = 153;</code>
     * @return float
     */
    public function getVideoViewRate()
    {
        return isset($this->video_view_rate) ? $this->video_view_rate : 0.0;
    }

    public function hasVideoViewRate()
    {
        return isset($this->video_view_rate);
    }

    public function clearVideoViewRate()
    {
        unset($this->video_view_rate);
    }

    /**
     * The number of views your TrueView video ad receives divided by its number
     * of impressions, including thumbnail impressions for TrueView in-display
     * ads.
     *
     * Generated from protobuf field <code>optional double video_view_rate = 153;</code>
     * @param float $var
     * @return $this
     */
    public function setVideoViewRate($var)
    {
        GPBUtil::checkDouble($var);
        $this->video_view_rate = $var;

        return $this;
    }

    /**
     * The number of times your video ads were viewed.
     *
     * Generated from protobuf field <code>optional int64 video_views = 154;</code>
     * @return int|string
     */
    public function getVideoViews()
    {
        return isset($this->video_views) ? $this->video_views : 0;
    }

    public function hasVideoViews()
    {
        return isset($this->video_views);
    }

    public function clearVideoViews()
    {
        unset($this->video_views);
    }

    /**
     * The number of times your video ads were viewed.
     *
     * Generated from protobuf field <code>optional int64 video_views = 154;</code>
     * @param int|string $var
     * @return $this
     */
    public function setVideoViews($var)
    {
        GPBUtil::checkInt64($var);
        $this->video_views = $var;

        return $this;
    }

    /**
     * The total number of view-through conversions.
     * These happen when a customer sees an image or rich media ad, then later
     * completes a conversion on your site without interacting with (for example,
     * clicking on) another ad.
     *
     * Generated from protobuf field <code>optional int64 view_through_conversions = 155;</code>
     * @return int|string
     */
    public function getViewThroughConversions()
    {
        return isset($this->view_through_conversions) ? $this->view_through_conversions : 0;
    }

    public function hasViewThroughConversions()
    {
        return isset($this->view_through_conversions);
    }

    public function clearViewThroughConversions()
    {
        unset($this->view_through_conversions);
    }

    /**
     * The total number of view-through conversions.
     * These happen when a customer sees an image or rich media ad, then later
     * completes a conversion on your site without interacting with (for example,
     * clicking on) another ad.
     *
     * Generated from protobuf field <code>optional int64 view_through_conversions = 155;</code>
     * @param int|string $var
     * @return $this
     */
    public function setViewThroughConversions($var)
    {
        GPBUtil::checkInt64($var);
        $this->view_through_conversions = $var;

        return $this;
    }

    /**
     * The number of iOS Store Kit Ad Network conversions.
     *
     * Generated from protobuf field <code>int64 sk_ad_network_installs = 246;</code>
     * @return int|string
     */
    public function getSkAdNetworkInstalls()
    {
        return $this->sk_ad_network_installs;
    }

    /**
     * The number of iOS Store Kit Ad Network conversions.
     *
     * Generated from protobuf field <code>int64 sk_ad_network_installs = 246;</code>
     * @param int|string $var
     * @return $this
     */
    public function setSkAdNetworkInstalls($var)
    {
        GPBUtil::checkInt64($var);
        $this->sk_ad_network_installs = $var;

        return $this;
    }

    /**
     * The total number of iOS Store Kit Ad Network conversions.
     *
     * Generated from protobuf field <code>int64 sk_ad_network_total_conversions = 292;</code>
     * @return int|string
     */
    public function getSkAdNetworkTotalConversions()
    {
        return $this->sk_ad_network_total_conversions;
    }

    /**
     * The total number of iOS Store Kit Ad Network conversions.
     *
     * Generated from protobuf field <code>int64 sk_ad_network_total_conversions = 292;</code>
     * @param int|string $var
     * @return $this
     */
    public function setSkAdNetworkTotalConversions($var)
    {
        GPBUtil::checkInt64($var);
        $this->sk_ad_network_total_conversions = $var;

        return $this;
    }

    /**
     * Clicks from properties not owned by the publisher for which the traffic
     * the publisher has paid for or acquired through incentivized activity
     *
     * Generated from protobuf field <code>int64 publisher_purchased_clicks = 264;</code>
     * @return int|string
     */
    public function getPublisherPurchasedClicks()
    {
        return $this->publisher_purchased_clicks;
    }

    /**
     * Clicks from properties not owned by the publisher for which the traffic
     * the publisher has paid for or acquired through incentivized activity
     *
     * Generated from protobuf field <code>int64 publisher_purchased_clicks = 264;</code>
     * @param int|string $var
     * @return $this
     */
    public function setPublisherPurchasedClicks($var)
    {
        GPBUtil::checkInt64($var);
        $this->publisher_purchased_clicks = $var;

        return $this;
    }

    /**
     * Clicks from properties for which the traffic the publisher has not paid
     * for or acquired through incentivized activity
     *
     * Generated from protobuf field <code>int64 publisher_organic_clicks = 265;</code>
     * @return int|string
     */
    public function getPublisherOrganicClicks()
    {
        return $this->publisher_organic_clicks;
    }

    /**
     * Clicks from properties for which the traffic the publisher has not paid
     * for or acquired through incentivized activity
     *
     * Generated from protobuf field <code>int64 publisher_organic_clicks = 265;</code>
     * @param int|string $var
     * @return $this
     */
    public function setPublisherOrganicClicks($var)
    {
        GPBUtil::checkInt64($var);
        $this->publisher_organic_clicks = $var;

        return $this;
    }

    /**
     * Clicks from traffic which is not identified as "Publisher Purchased" or
     * "Publisher Organic"
     *
     * Generated from protobuf field <code>int64 publisher_unknown_clicks = 266;</code>
     * @return int|string
     */
    public function getPublisherUnknownClicks()
    {
        return $this->publisher_unknown_clicks;
    }

    /**
     * Clicks from traffic which is not identified as "Publisher Purchased" or
     * "Publisher Organic"
     *
     * Generated from protobuf field <code>int64 publisher_unknown_clicks = 266;</code>
     * @param int|string $var
     * @return $this
     */
    public function setPublisherUnknownClicks($var)
    {
        GPBUtil::checkInt64($var);
        $this->publisher_unknown_clicks = $var;

        return $this;
    }

    /**
     * Number of call button clicks on any location surface after a chargeable ad
     * event (click or impression). This measure is coming from Asset based
     * location.
     *
     * Generated from protobuf field <code>optional double all_conversions_from_location_asset_click_to_call = 267;</code>
     * @return float
     */
    public function getAllConversionsFromLocationAssetClickToCall()
    {
        return isset($this->all_conversions_from_location_asset_click_to_call) ? $this->all_conversions_from_location_asset_click_to_call : 0.0;
    }

    public function hasAllConversionsFromLocationAssetClickToCall()
    {
        return isset($this->all_conversions_from_location_asset_click_to_call);
    }

    public function clearAllConversionsFromLocationAssetClickToCall()
    {
        unset($this->all_conversions_from_location_asset_click_to_call);
    }

    /**
     * Number of call button clicks on any location surface after a chargeable ad
     * event (click or impression). This measure is coming from Asset based
     * location.
     *
     * Generated from protobuf field <code>optional double all_conversions_from_location_asset_click_to_call = 267;</code>
     * @param float $var
     * @return $this
     */
    public function setAllConversionsFromLocationAssetClickToCall($var)
    {
        GPBUtil::checkDouble($var);
        $this->all_conversions_from_location_asset_click_to_call = $var;

        return $this;
    }

    /**
     * Number of driving directions clicks on any location surface after a
     * chargeable ad event (click or impression). This measure is coming
     * from Asset based location.
     *
     * Generated from protobuf field <code>optional double all_conversions_from_location_asset_directions = 268;</code>
     * @return float
     */
    public function getAllConversionsFromLocationAssetDirections()
    {
        return isset($this->all_conversions_from_location_asset_directions) ? $this->all_conversions_from_location_asset_directions : 0.0;
    }

    public function hasAllConversionsFromLocationAssetDirections()
    {
        return isset($this->all_conversions_from_location_asset_directions);
    }

    public function clearAllConversionsFromLocationAssetDirections()
    {
        unset($this->all_conversions_from_location_asset_directions);
    }

    /**
     * Number of driving directions clicks on any location surface after a
     * chargeable ad event (click or impression). This measure is coming
     * from Asset based location.
     *
     * Generated from protobuf field <code>optional double all_conversions_from_location_asset_directions = 268;</code>
     * @param float $var
     * @return $this
     */
    public function setAllConversionsFromLocationAssetDirections($var)
    {
        GPBUtil::checkDouble($var);
        $this->all_conversions_from_location_asset_directions = $var;

        return $this;
    }

    /**
     * Number of menu link clicks on any location surface after a chargeable ad
     * event (click or impression). This measure is coming from Asset based
     * location.
     *
     * Generated from protobuf field <code>optional double all_conversions_from_location_asset_menu = 269;</code>
     * @return float
     */
    public function getAllConversionsFromLocationAssetMenu()
    {
        return isset($this->all_conversions_from_location_asset_menu) ? $this->all_conversions_from_location_asset_menu : 0.0;
    }

    public function hasAllConversionsFromLocationAssetMenu()
    {
        return isset($this->all_conversions_from_location_asset_menu);
    }

    public function clearAllConversionsFromLocationAssetMenu()
    {
        unset($this->all_conversions_from_location_asset_menu);
    }

    /**
     * Number of menu link clicks on any location surface after a chargeable ad
     * event (click or impression). This measure is coming from Asset based
     * location.
     *
     * Generated from protobuf field <code>optional double all_conversions_from_location_asset_menu = 269;</code>
     * @param float $var
     * @return $this
     */
    public function setAllConversionsFromLocationAssetMenu($var)
    {
        GPBUtil::checkDouble($var);
        $this->all_conversions_from_location_asset_menu = $var;

        return $this;
    }

    /**
     * Number of order clicks on any location surface after a chargeable ad event
     * (click or impression). This measure is coming from Asset based
     * location.
     *
     * Generated from protobuf field <code>optional double all_conversions_from_location_asset_order = 270;</code>
     * @return float
     */
    public function getAllConversionsFromLocationAssetOrder()
    {
        return isset($this->all_conversions_from_location_asset_order) ? $this->all_conversions_from_location_asset_order : 0.0;
    }

    public function hasAllConversionsFromLocationAssetOrder()
    {
        return isset($this->all_conversions_from_location_asset_order);
    }

    public function clearAllConversionsFromLocationAssetOrder()
    {
        unset($this->all_conversions_from_location_asset_order);
    }

    /**
     * Number of order clicks on any location surface after a chargeable ad event
     * (click or impression). This measure is coming from Asset based
     * location.
     *
     * Generated from protobuf field <code>optional double all_conversions_from_location_asset_order = 270;</code>
     * @param float $var
     * @return $this
     */
    public function setAllConversionsFromLocationAssetOrder($var)
    {
        GPBUtil::checkDouble($var);
        $this->all_conversions_from_location_asset_order = $var;

        return $this;
    }

    /**
     * Number of other types of local action clicks on any location surface after
     * a chargeable ad event (click or impression). This measure is coming
     * from Asset based location.
     *
     * Generated from protobuf field <code>optional double all_conversions_from_location_asset_other_engagement = 271;</code>
     * @return float
     */
    public function getAllConversionsFromLocationAssetOtherEngagement()
    {
        return isset($this->all_conversions_from_location_asset_other_engagement) ? $this->all_conversions_from_location_asset_other_engagement : 0.0;
    }

    public function hasAllConversionsFromLocationAssetOtherEngagement()
    {
        return isset($this->all_conversions_from_location_asset_other_engagement);
    }

    public function clearAllConversionsFromLocationAssetOtherEngagement()
    {
        unset($this->all_conversions_from_location_asset_other_engagement);
    }

    /**
     * Number of other types of local action clicks on any location surface after
     * a chargeable ad event (click or impression). This measure is coming
     * from Asset based location.
     *
     * Generated from protobuf field <code>optional double all_conversions_from_location_asset_other_engagement = 271;</code>
     * @param float $var
     * @return $this
     */
    public function setAllConversionsFromLocationAssetOtherEngagement($var)
    {
        GPBUtil::checkDouble($var);
        $this->all_conversions_from_location_asset_other_engagement = $var;

        return $this;
    }

    /**
     * Estimated number of visits to the store after a chargeable
     * ad event (click or impression). This measure is coming from Asset
     * based location.
     *
     * Generated from protobuf field <code>optional double all_conversions_from_location_asset_store_visits = 272;</code>
     * @return float
     */
    public function getAllConversionsFromLocationAssetStoreVisits()
    {
        return isset($this->all_conversions_from_location_asset_store_visits) ? $this->all_conversions_from_location_asset_store_visits : 0.0;
    }

    public function hasAllConversionsFromLocationAssetStoreVisits()
    {
        return isset($this->all_conversions_from_location_asset_store_visits);
    }

    public function clearAllConversionsFromLocationAssetStoreVisits()
    {
        unset($this->all_conversions_from_location_asset_store_visits);
    }

    /**
     * Estimated number of visits to the store after a chargeable
     * ad event (click or impression). This measure is coming from Asset
     * based location.
     *
     * Generated from protobuf field <code>optional double all_conversions_from_location_asset_store_visits = 272;</code>
     * @param float $var
     * @return $this
     */
    public function setAllConversionsFromLocationAssetStoreVisits($var)
    {
        GPBUtil::checkDouble($var);
        $this->all_conversions_from_location_asset_store_visits = $var;

        return $this;
    }

    /**
     * Number of website URL clicks on any location surface after a chargeable ad
     * event (click or impression). This measure is coming from Asset based
     * location.
     *
     * Generated from protobuf field <code>optional double all_conversions_from_location_asset_website = 273;</code>
     * @return float
     */
    public function getAllConversionsFromLocationAssetWebsite()
    {
        return isset($this->all_conversions_from_location_asset_website) ? $this->all_conversions_from_location_asset_website : 0.0;
    }

    public function hasAllConversionsFromLocationAssetWebsite()
    {
        return isset($this->all_conversions_from_location_asset_website);
    }

    public function clearAllConversionsFromLocationAssetWebsite()
    {
        unset($this->all_conversions_from_location_asset_website);
    }

    /**
     * Number of website URL clicks on any location surface after a chargeable ad
     * event (click or impression). This measure is coming from Asset based
     * location.
     *
     * Generated from protobuf field <code>optional double all_conversions_from_location_asset_website = 273;</code>
     * @param float $var
     * @return $this
     */
    public function setAllConversionsFromLocationAssetWebsite($var)
    {
        GPBUtil::checkDouble($var);
        $this->all_conversions_from_location_asset_website = $var;

        return $this;
    }

    /**
     * Number of impressions in which the store location was shown or the location
     * was used for targeting. This measure is coming from Asset based
     * location.
     *
     * Generated from protobuf field <code>optional int64 eligible_impressions_from_location_asset_store_reach = 274;</code>
     * @return int|string
     */
    public function getEligibleImpressionsFromLocationAssetStoreReach()
    {
        return isset($this->eligible_impressions_from_location_asset_store_reach) ? $this->eligible_impressions_from_location_asset_store_reach : 0;
    }

    public function hasEligibleImpressionsFromLocationAssetStoreReach()
    {
        return isset($this->eligible_impressions_from_location_asset_store_reach);
    }

    public function clearEligibleImpressionsFromLocationAssetStoreReach()
    {
        unset($this->eligible_impressions_from_location_asset_store_reach);
    }

    /**
     * Number of impressions in which the store location was shown or the location
     * was used for targeting. This measure is coming from Asset based
     * location.
     *
     * Generated from protobuf field <code>optional int64 eligible_impressions_from_location_asset_store_reach = 274;</code>
     * @param int|string $var
     * @return $this
     */
    public function setEligibleImpressionsFromLocationAssetStoreReach($var)
    {
        GPBUtil::checkInt64($var);
        $this->eligible_impressions_from_location_asset_store_reach = $var;

        return $this;
    }

    /**
     * Number of call button clicks on any location surface after an impression.
     * This measure is coming from Asset based location.
     *
     * Generated from protobuf field <code>optional double view_through_conversions_from_location_asset_click_to_call = 275;</code>
     * @return float
     */
    public function getViewThroughConversionsFromLocationAssetClickToCall()
    {
        return isset($this->view_through_conversions_from_location_asset_click_to_call) ? $this->view_through_conversions_from_location_asset_click_to_call : 0.0;
    }

    public function hasViewThroughConversionsFromLocationAssetClickToCall()
    {
        return isset($this->view_through_conversions_from_location_asset_click_to_call);
    }

    public function clearViewThroughConversionsFromLocationAssetClickToCall()
    {
        unset($this->view_through_conversions_from_location_asset_click_to_call);
    }

    /**
     * Number of call button clicks on any location surface after an impression.
     * This measure is coming from Asset based location.
     *
     * Generated from protobuf field <code>optional double view_through_conversions_from_location_asset_click_to_call = 275;</code>
     * @param float $var
     * @return $this
     */
    public function setViewThroughConversionsFromLocationAssetClickToCall($var)
    {
        GPBUtil::checkDouble($var);
        $this->view_through_conversions_from_location_asset_click_to_call = $var;

        return $this;
    }

    /**
     * Number of driving directions clicks on any location surface after an
     * impression. This measure is coming from Asset based location.
     *
     * Generated from protobuf field <code>optional double view_through_conversions_from_location_asset_directions = 276;</code>
     * @return float
     */
    public function getViewThroughConversionsFromLocationAssetDirections()
    {
        return isset($this->view_through_conversions_from_location_asset_directions) ? $this->view_through_conversions_from_location_asset_directions : 0.0;
    }

    public function hasViewThroughConversionsFromLocationAssetDirections()
    {
        return isset($this->view_through_conversions_from_location_asset_directions);
    }

    public function clearViewThroughConversionsFromLocationAssetDirections()
    {
        unset($this->view_through_conversions_from_location_asset_directions);
    }

    /**
     * Number of driving directions clicks on any location surface after an
     * impression. This measure is coming from Asset based location.
     *
     * Generated from protobuf field <code>optional double view_through_conversions_from_location_asset_directions = 276;</code>
     * @param float $var
     * @return $this
     */
    public function setViewThroughConversionsFromLocationAssetDirections($var)
    {
        GPBUtil::checkDouble($var);
        $this->view_through_conversions_from_location_asset_directions = $var;

        return $this;
    }

    /**
     * Number of menu link clicks on any location surface after an impression.
     * This measure is coming from Asset based location.
     *
     * Generated from protobuf field <code>optional double view_through_conversions_from_location_asset_menu = 277;</code>
     * @return float
     */
    public function getViewThroughConversionsFromLocationAssetMenu()
    {
        return isset($this->view_through_conversions_from_location_asset_menu) ? $this->view_through_conversions_from_location_asset_menu : 0.0;
    }

    public function hasViewThroughConversionsFromLocationAssetMenu()
    {
        return isset($this->view_through_conversions_from_location_asset_menu);
    }

    public function clearViewThroughConversionsFromLocationAssetMenu()
    {
        unset($this->view_through_conversions_from_location_asset_menu);
    }

    /**
     * Number of menu link clicks on any location surface after an impression.
     * This measure is coming from Asset based location.
     *
     * Generated from protobuf field <code>optional double view_through_conversions_from_location_asset_menu = 277;</code>
     * @param float $var
     * @return $this
     */
    public function setViewThroughConversionsFromLocationAssetMenu($var)
    {
        GPBUtil::checkDouble($var);
        $this->view_through_conversions_from_location_asset_menu = $var;

        return $this;
    }

    /**
     * Number of order clicks on any location surface after an impression. This
     * measure is coming from Asset based location.
     *
     * Generated from protobuf field <code>optional double view_through_conversions_from_location_asset_order = 278;</code>
     * @return float
     */
    public function getViewThroughConversionsFromLocationAssetOrder()
    {
        return isset($this->view_through_conversions_from_location_asset_order) ? $this->view_through_conversions_from_location_asset_order : 0.0;
    }

    public function hasViewThroughConversionsFromLocationAssetOrder()
    {
        return isset($this->view_through_conversions_from_location_asset_order);
    }

    public function clearViewThroughConversionsFromLocationAssetOrder()
    {
        unset($this->view_through_conversions_from_location_asset_order);
    }

    /**
     * Number of order clicks on any location surface after an impression. This
     * measure is coming from Asset based location.
     *
     * Generated from protobuf field <code>optional double view_through_conversions_from_location_asset_order = 278;</code>
     * @param float $var
     * @return $this
     */
    public function setViewThroughConversionsFromLocationAssetOrder($var)
    {
        GPBUtil::checkDouble($var);
        $this->view_through_conversions_from_location_asset_order = $var;

        return $this;
    }

    /**
     * Number of other types of local action clicks on any location surface after
     * an impression. This measure is coming from Asset based location.
     *
     * Generated from protobuf field <code>optional double view_through_conversions_from_location_asset_other_engagement = 279;</code>
     * @return float
     */
    public function getViewThroughConversionsFromLocationAssetOtherEngagement()
    {
        return isset($this->view_through_conversions_from_location_asset_other_engagement) ? $this->view_through_conversions_from_location_asset_other_engagement : 0.0;
    }

    public function hasViewThroughConversionsFromLocationAssetOtherEngagement()
    {
        return isset($this->view_through_conversions_from_location_asset_other_engagement);
    }

    public function clearViewThroughConversionsFromLocationAssetOtherEngagement()
    {
        unset($this->view_through_conversions_from_location_asset_other_engagement);
    }

    /**
     * Number of other types of local action clicks on any location surface after
     * an impression. This measure is coming from Asset based location.
     *
     * Generated from protobuf field <code>optional double view_through_conversions_from_location_asset_other_engagement = 279;</code>
     * @param float $var
     * @return $this
     */
    public function setViewThroughConversionsFromLocationAssetOtherEngagement($var)
    {
        GPBUtil::checkDouble($var);
        $this->view_through_conversions_from_location_asset_other_engagement = $var;

        return $this;
    }

    /**
     * Estimated number of visits to the store after an impression.
     * This measure is coming from Asset based location.
     *
     * Generated from protobuf field <code>optional double view_through_conversions_from_location_asset_store_visits = 280;</code>
     * @return float
     */
    public function getViewThroughConversionsFromLocationAssetStoreVisits()
    {
        return isset($this->view_through_conversions_from_location_asset_store_visits) ? $this->view_through_conversions_from_location_asset_store_visits : 0.0;
    }

    public function hasViewThroughConversionsFromLocationAssetStoreVisits()
    {
        return isset($this->view_through_conversions_from_location_asset_store_visits);
    }

    public function clearViewThroughConversionsFromLocationAssetStoreVisits()
    {
        unset($this->view_through_conversions_from_location_asset_store_visits);
    }

    /**
     * Estimated number of visits to the store after an impression.
     * This measure is coming from Asset based location.
     *
     * Generated from protobuf field <code>optional double view_through_conversions_from_location_asset_store_visits = 280;</code>
     * @param float $var
     * @return $this
     */
    public function setViewThroughConversionsFromLocationAssetStoreVisits($var)
    {
        GPBUtil::checkDouble($var);
        $this->view_through_conversions_from_location_asset_store_visits = $var;

        return $this;
    }

    /**
     * Number of website URL clicks on any location surface after an impression.
     * This measure is coming from Asset based location.
     *
     * Generated from protobuf field <code>optional double view_through_conversions_from_location_asset_website = 281;</code>
     * @return float
     */
    public function getViewThroughConversionsFromLocationAssetWebsite()
    {
        return isset($this->view_through_conversions_from_location_asset_website) ? $this->view_through_conversions_from_location_asset_website : 0.0;
    }

    public function hasViewThroughConversionsFromLocationAssetWebsite()
    {
        return isset($this->view_through_conversions_from_location_asset_website);
    }

    public function clearViewThroughConversionsFromLocationAssetWebsite()
    {
        unset($this->view_through_conversions_from_location_asset_website);
    }

    /**
     * Number of website URL clicks on any location surface after an impression.
     * This measure is coming from Asset based location.
     *
     * Generated from protobuf field <code>optional double view_through_conversions_from_location_asset_website = 281;</code>
     * @param float $var
     * @return $this
     */
    public function setViewThroughConversionsFromLocationAssetWebsite($var)
    {
        GPBUtil::checkDouble($var);
        $this->view_through_conversions_from_location_asset_website = $var;

        return $this;
    }

    /**
     * Orders is the total number of purchase conversions you received attributed
     * to your ads.
     * How it works: You report conversions with cart data for
     * completed purchases on your website. If a conversion is attributed to
     * previous interactions with your ads (clicks for text or Shopping ads, views
     * for video ads etc.) it's counted as an order.
     * Example: Someone clicked on a Shopping ad for a hat then bought the same
     * hat and a shirt in an order on your website. Even though they bought 2
     * products, this would count as 1 order.
     * This metric is only available if you report conversions with cart data.
     *
     * Generated from protobuf field <code>optional double orders = 296;</code>
     * @return float
     */
    public function getOrders()
    {
        return isset($this->orders) ? $this->orders : 0.0;
    }

    public function hasOrders()
    {
        return isset($this->orders);
    }

    public function clearOrders()
    {
        unset($this->orders);
    }

    /**
     * Orders is the total number of purchase conversions you received attributed
     * to your ads.
     * How it works: You report conversions with cart data for
     * completed purchases on your website. If a conversion is attributed to
     * previous interactions with your ads (clicks for text or Shopping ads, views
     * for video ads etc.) it's counted as an order.
     * Example: Someone clicked on a Shopping ad for a hat then bought the same
     * hat and a shirt in an order on your website. Even though they bought 2
     * products, this would count as 1 order.
     * This metric is only available if you report conversions with cart data.
     *
     * Generated from protobuf field <code>optional double orders = 296;</code>
     * @param float $var
     * @return $this
     */
    public function setOrders($var)
    {
        GPBUtil::checkDouble($var);
        $this->orders = $var;

        return $this;
    }

    /**
     * Average order value is the average revenue you made per order attributed to
     * your ads.
     * How it works: You report conversions with cart data for completed purchases
     * on your website. Average order value is the total revenue from your orders
     * divided by the total number of orders.
     * Example: You received 3 orders which made $10, $15 and $20 worth of
     * revenue. The average order value is $15 = ($10 + $15 + $20)/3.
     * This metric is only available if you report conversions with cart data.
     *
     * Generated from protobuf field <code>optional int64 average_order_value_micros = 297;</code>
     * @return int|string
     */
    public function getAverageOrderValueMicros()
    {
        return isset($this->average_order_value_micros) ? $this->average_order_value_micros : 0;
    }

    public function hasAverageOrderValueMicros()
    {
        return isset($this->average_order_value_micros);
    }

    public function clearAverageOrderValueMicros()
    {
        unset($this->average_order_value_micros);
    }

    /**
     * Average order value is the average revenue you made per order attributed to
     * your ads.
     * How it works: You report conversions with cart data for completed purchases
     * on your website. Average order value is the total revenue from your orders
     * divided by the total number of orders.
     * Example: You received 3 orders which made $10, $15 and $20 worth of
     * revenue. The average order value is $15 = ($10 + $15 + $20)/3.
     * This metric is only available if you report conversions with cart data.
     *
     * Generated from protobuf field <code>optional int64 average_order_value_micros = 297;</code>
     * @param int|string $var
     * @return $this
     */
    public function setAverageOrderValueMicros($var)
    {
        GPBUtil::checkInt64($var);
        $this->average_order_value_micros = $var;

        return $this;
    }

    /**
     * Average cart size is the average number of products in each order
     * attributed to your ads.
     * How it works: You report conversions with cart data for completed purchases
     * on your website. Average cart size is the total number of products sold
     * divided by the total number of orders you received.
     * Example: You received 2 orders, the first included 3 products and the
     * second included 2. The average cart size is 2.5 products = (3+2)/2.
     * This metric is only available if you report conversions with cart data.
     *
     * Generated from protobuf field <code>optional double average_cart_size = 298;</code>
     * @return float
     */
    public function getAverageCartSize()
    {
        return isset($this->average_cart_size) ? $this->average_cart_size : 0.0;
    }

    public function hasAverageCartSize()
    {
        return isset($this->average_cart_size);
    }

    public function clearAverageCartSize()
    {
        unset($this->average_cart_size);
    }

    /**
     * Average cart size is the average number of products in each order
     * attributed to your ads.
     * How it works: You report conversions with cart data for completed purchases
     * on your website. Average cart size is the total number of products sold
     * divided by the total number of orders you received.
     * Example: You received 2 orders, the first included 3 products and the
     * second included 2. The average cart size is 2.5 products = (3+2)/2.
     * This metric is only available if you report conversions with cart data.
     *
     * Generated from protobuf field <code>optional double average_cart_size = 298;</code>
     * @param float $var
     * @return $this
     */
    public function setAverageCartSize($var)
    {
        GPBUtil::checkDouble($var);
        $this->average_cart_size = $var;

        return $this;
    }

    /**
     * Cost of goods sold (COGS) is the total cost of the products you sold in
     * orders attributed to your ads.
     * How it works: You can add a cost of goods sold value to every product in
     * Merchant Center. If you report conversions with cart data, the products you
     * sold are matched with their cost of goods sold value and this can be used
     * to calculate the gross profit you made on each order.
     * Example: Someone clicked on a Shopping ad for a hat then bought the same
     * hat and a shirt. The hat has a cost of goods sold value of $3, the shirt
     * has a cost of goods sold value of $5. The cost of goods sold for this order
     * is $8 = $3 + $5.
     * This metric is only available if you report conversions with cart data.
     *
     * Generated from protobuf field <code>optional int64 cost_of_goods_sold_micros = 299;</code>
     * @return int|string
     */
    public function getCostOfGoodsSoldMicros()
    {
        return isset($this->cost_of_goods_sold_micros) ? $this->cost_of_goods_sold_micros : 0;
    }

    public function hasCostOfGoodsSoldMicros()
    {
        return isset($this->cost_of_goods_sold_micros);
    }

    public function clearCostOfGoodsSoldMicros()
    {
        unset($this->cost_of_goods_sold_micros);
    }

    /**
     * Cost of goods sold (COGS) is the total cost of the products you sold in
     * orders attributed to your ads.
     * How it works: You can add a cost of goods sold value to every product in
     * Merchant Center. If you report conversions with cart data, the products you
     * sold are matched with their cost of goods sold value and this can be used
     * to calculate the gross profit you made on each order.
     * Example: Someone clicked on a Shopping ad for a hat then bought the same
     * hat and a shirt. The hat has a cost of goods sold value of $3, the shirt
     * has a cost of goods sold value of $5. The cost of goods sold for this order
     * is $8 = $3 + $5.
     * This metric is only available if you report conversions with cart data.
     *
     * Generated from protobuf field <code>optional int64 cost_of_goods_sold_micros = 299;</code>
     * @param int|string $var
     * @return $this
     */
    public function setCostOfGoodsSoldMicros($var)
    {
        GPBUtil::checkInt64($var);
        $this->cost_of_goods_sold_micros = $var;

        return $this;
    }

    /**
     * Gross profit is the profit you made from orders attributed to your ads
     * minus the cost of goods sold (COGS).
     * How it works: Gross profit is the revenue you made from sales attributed to
     * your ads minus cost of goods sold. Gross profit calculations only include
     * products that have a cost of goods sold value in Merchant Center.
     * Example: Someone clicked on a Shopping ad for a hat then bought the same
     * hat and a shirt in an order from your website. The hat is priced $10 and
     * the shirt is priced $20. The hat has a cost of goods sold value of $3, but
     * the shirt has no cost of goods sold value. Gross profit for this order will
     * only take into account the hat, so it's $7 = $10 - $3.
     * This metric is only available if you report conversions with cart data.
     *
     * Generated from protobuf field <code>optional int64 gross_profit_micros = 300;</code>
     * @return int|string
     */
    public function getGrossProfitMicros()
    {
        return isset($this->gross_profit_micros) ? $this->gross_profit_micros : 0;
    }

    public function hasGrossProfitMicros()
    {
        return isset($this->gross_profit_micros);
    }

    public function clearGrossProfitMicros()
    {
        unset($this->gross_profit_micros);
    }

    /**
     * Gross profit is the profit you made from orders attributed to your ads
     * minus the cost of goods sold (COGS).
     * How it works: Gross profit is the revenue you made from sales attributed to
     * your ads minus cost of goods sold. Gross profit calculations only include
     * products that have a cost of goods sold value in Merchant Center.
     * Example: Someone clicked on a Shopping ad for a hat then bought the same
     * hat and a shirt in an order from your website. The hat is priced $10 and
     * the shirt is priced $20. The hat has a cost of goods sold value of $3, but
     * the shirt has no cost of goods sold value. Gross profit for this order will
     * only take into account the hat, so it's $7 = $10 - $3.
     * This metric is only available if you report conversions with cart data.
     *
     * Generated from protobuf field <code>optional int64 gross_profit_micros = 300;</code>
     * @param int|string $var
     * @return $this
     */
    public function setGrossProfitMicros($var)
    {
        GPBUtil::checkInt64($var);
        $this->gross_profit_micros = $var;

        return $this;
    }

    /**
     * Gross profit margin is the percentage gross profit you made from orders
     * attributed to your ads, after taking out the cost of goods sold (COGS).
     * How it works: You report conversions with cart data for completed purchases
     * on your website. Gross profit margin is the gross profit you made divided
     * by your total revenue and multiplied by 100%. Gross profit margin
     * calculations only include products that have a cost of goods sold value in
     * Merchant Center.
     * Example: Someone bought a hat and a shirt in an order on your website. The
     * hat is priced $10 and has a cost of goods sold value of $3. The shirt is
     * priced $20 but has no cost of goods sold value. Gross profit margin for
     * this order will only take into account the hat because it has a cost of
     * goods sold value, so it's 70% = ($10 - $3)/$10 x 100%.
     * This metric is only available if you report conversions with cart data.
     *
     * Generated from protobuf field <code>optional double gross_profit_margin = 301;</code>
     * @return float
     */
    public function getGrossProfitMargin()
    {
        return isset($this->gross_profit_margin) ? $this->gross_profit_margin : 0.0;
    }

    public function hasGrossProfitMargin()
    {
        return isset($this->gross_profit_margin);
    }

    public function clearGrossProfitMargin()
    {
        unset($this->gross_profit_margin);
    }

    /**
     * Gross profit margin is the percentage gross profit you made from orders
     * attributed to your ads, after taking out the cost of goods sold (COGS).
     * How it works: You report conversions with cart data for completed purchases
     * on your website. Gross profit margin is the gross profit you made divided
     * by your total revenue and multiplied by 100%. Gross profit margin
     * calculations only include products that have a cost of goods sold value in
     * Merchant Center.
     * Example: Someone bought a hat and a shirt in an order on your website. The
     * hat is priced $10 and has a cost of goods sold value of $3. The shirt is
     * priced $20 but has no cost of goods sold value. Gross profit margin for
     * this order will only take into account the hat because it has a cost of
     * goods sold value, so it's 70% = ($10 - $3)/$10 x 100%.
     * This metric is only available if you report conversions with cart data.
     *
     * Generated from protobuf field <code>optional double gross_profit_margin = 301;</code>
     * @param float $var
     * @return $this
     */
    public function setGrossProfitMargin($var)
    {
        GPBUtil::checkDouble($var);
        $this->gross_profit_margin = $var;

        return $this;
    }

    /**
     * Revenue is the total amount you made from orders attributed to your ads.
     * How it works: You report conversions with cart data for completed purchases
     * on your website. Revenue is the total value of all the orders you received
     * attributed to your ads, minus any discount.
     * Example: Someone clicked on a Shopping ad  for a hat then bought the same
     * hat and a shirt in an order from your website. The hat is priced $10 and
     * the shirt is priced $20. The entire order has a $5 discount. The revenue
     * from this order is $25 = ($10 + $20) - $5.
     * This metric is only available if you report conversions with cart data.
     *
     * Generated from protobuf field <code>optional int64 revenue_micros = 302;</code>
     * @return int|string
     */
    public function getRevenueMicros()
    {
        return isset($this->revenue_micros) ? $this->revenue_micros : 0;
    }

    public function hasRevenueMicros()
    {
        return isset($this->revenue_micros);
    }

    public function clearRevenueMicros()
    {
        unset($this->revenue_micros);
    }

    /**
     * Revenue is the total amount you made from orders attributed to your ads.
     * How it works: You report conversions with cart data for completed purchases
     * on your website. Revenue is the total value of all the orders you received
     * attributed to your ads, minus any discount.
     * Example: Someone clicked on a Shopping ad  for a hat then bought the same
     * hat and a shirt in an order from your website. The hat is priced $10 and
     * the shirt is priced $20. The entire order has a $5 discount. The revenue
     * from this order is $25 = ($10 + $20) - $5.
     * This metric is only available if you report conversions with cart data.
     *
     * Generated from protobuf field <code>optional int64 revenue_micros = 302;</code>
     * @param int|string $var
     * @return $this
     */
    public function setRevenueMicros($var)
    {
        GPBUtil::checkInt64($var);
        $this->revenue_micros = $var;

        return $this;
    }

    /**
     * Units sold is the total number of products sold from orders attributed to
     * your ads.
     * How it works: You report conversions with cart data for completed purchases
     * on your website. Units sold is the total number of products sold from all
     * orders attributed to your ads.
     * Example: Someone clicked on a Shopping ad for a hat then bought the same
     * hat, a shirt and a jacket. The units sold in this order is 3.
     * This metric is only available if you report conversions with cart data.
     *
     * Generated from protobuf field <code>optional double units_sold = 303;</code>
     * @return float
     */
    public function getUnitsSold()
    {
        return isset($this->units_sold) ? $this->units_sold : 0.0;
    }

    public function hasUnitsSold()
    {
        return isset($this->units_sold);
    }

    public function clearUnitsSold()
    {
        unset($this->units_sold);
    }

    /**
     * Units sold is the total number of products sold from orders attributed to
     * your ads.
     * How it works: You report conversions with cart data for completed purchases
     * on your website. Units sold is the total number of products sold from all
     * orders attributed to your ads.
     * Example: Someone clicked on a Shopping ad for a hat then bought the same
     * hat, a shirt and a jacket. The units sold in this order is 3.
     * This metric is only available if you report conversions with cart data.
     *
     * Generated from protobuf field <code>optional double units_sold = 303;</code>
     * @param float $var
     * @return $this
     */
    public function setUnitsSold($var)
    {
        GPBUtil::checkDouble($var);
        $this->units_sold = $var;

        return $this;
    }

    /**
     * Cross-sell cost of goods sold (COGS) is the total cost of products sold as
     * a result of advertising a different product.
     * How it works: You report conversions with cart data for completed purchases
     * on your website. If the ad that was interacted with before the purchase has
     * an associated product (see Shopping Ads) then this product is considered
     * the advertised product. Any product included in the order the customer
     * places is a sold product. If these products don't match then this is
     * considered cross-sell. Cross-sell cost of goods sold is the total cost of
     * the products sold that weren't advertised.
     * Example: Someone clicked on a Shopping ad for a hat then bought the same
     * hat and a shirt. The hat has a cost of goods sold value of $3, the shirt
     * has a cost of goods sold value of $5. The cross-sell cost of goods sold for
     * this order is $5.
     * This metric is only available if you report conversions with cart data.
     *
     * Generated from protobuf field <code>optional int64 cross_sell_cost_of_goods_sold_micros = 304;</code>
     * @return int|string
     */
    public function getCrossSellCostOfGoodsSoldMicros()
    {
        return isset($this->cross_sell_cost_of_goods_sold_micros) ? $this->cross_sell_cost_of_goods_sold_micros : 0;
    }

    public function hasCrossSellCostOfGoodsSoldMicros()
    {
        return isset($this->cross_sell_cost_of_goods_sold_micros);
    }

    public function clearCrossSellCostOfGoodsSoldMicros()
    {
        unset($this->cross_sell_cost_of_goods_sold_micros);
    }

    /**
     * Cross-sell cost of goods sold (COGS) is the total cost of products sold as
     * a result of advertising a different product.
     * How it works: You report conversions with cart data for completed purchases
     * on your website. If the ad that was interacted with before the purchase has
     * an associated product (see Shopping Ads) then this product is considered
     * the advertised product. Any product included in the order the customer
     * places is a sold product. If these products don't match then this is
     * considered cross-sell. Cross-sell cost of goods sold is the total cost of
     * the products sold that weren't advertised.
     * Example: Someone clicked on a Shopping ad for a hat then bought the same
     * hat and a shirt. The hat has a cost of goods sold value of $3, the shirt
     * has a cost of goods sold value of $5. The cross-sell cost of goods sold for
     * this order is $5.
     * This metric is only available if you report conversions with cart data.
     *
     * Generated from protobuf field <code>optional int64 cross_sell_cost_of_goods_sold_micros = 304;</code>
     * @param int|string $var
     * @return $this
     */
    public function setCrossSellCostOfGoodsSoldMicros($var)
    {
        GPBUtil::checkInt64($var);
        $this->cross_sell_cost_of_goods_sold_micros = $var;

        return $this;
    }

    /**
     * Cross-sell gross profit is the profit you made from products sold as a
     * result of advertising a different product, minus cost of goods sold (COGS).
     * How it works: You report conversions with cart data for completed purchases
     * on your website. If the ad that was interacted with before the purchase has
     * an associated product (see Shopping Ads) then this product is considered
     * the advertised product. Any product included in the purchase is a sold
     * product. If these products don't match then this is considered cross-sell.
     * Cross-sell gross profit is the revenue you made from cross-sell attributed
     * to your ads minus the cost of the goods sold.
     * Example: Someone clicked on a Shopping ad for a hat then bought the same
     * hat and a shirt. The shirt is priced $20 and has a cost of goods sold value
     * of $5. The cross-sell gross profit of this order is $15 = $20 - $5.
     * This metric is only available if you report conversions with cart data.
     *
     * Generated from protobuf field <code>optional int64 cross_sell_gross_profit_micros = 305;</code>
     * @return int|string
     */
    public function getCrossSellGrossProfitMicros()
    {
        return isset($this->cross_sell_gross_profit_micros) ? $this->cross_sell_gross_profit_micros : 0;
    }

    public function hasCrossSellGrossProfitMicros()
    {
        return isset($this->cross_sell_gross_profit_micros);
    }

    public function clearCrossSellGrossProfitMicros()
    {
        unset($this->cross_sell_gross_profit_micros);
    }

    /**
     * Cross-sell gross profit is the profit you made from products sold as a
     * result of advertising a different product, minus cost of goods sold (COGS).
     * How it works: You report conversions with cart data for completed purchases
     * on your website. If the ad that was interacted with before the purchase has
     * an associated product (see Shopping Ads) then this product is considered
     * the advertised product. Any product included in the purchase is a sold
     * product. If these products don't match then this is considered cross-sell.
     * Cross-sell gross profit is the revenue you made from cross-sell attributed
     * to your ads minus the cost of the goods sold.
     * Example: Someone clicked on a Shopping ad for a hat then bought the same
     * hat and a shirt. The shirt is priced $20 and has a cost of goods sold value
     * of $5. The cross-sell gross profit of this order is $15 = $20 - $5.
     * This metric is only available if you report conversions with cart data.
     *
     * Generated from protobuf field <code>optional int64 cross_sell_gross_profit_micros = 305;</code>
     * @param int|string $var
     * @return $this
     */
    public function setCrossSellGrossProfitMicros($var)
    {
        GPBUtil::checkInt64($var);
        $this->cross_sell_gross_profit_micros = $var;

        return $this;
    }

    /**
     * Cross-sell revenue is the total amount you made from products sold as a
     * result of advertising a different product.
     * How it works: You report conversions with cart data for completed purchases
     * on your website. If the ad that was interacted with before the purchase has
     * an associated product (see Shopping Ads) then this product is considered
     * the advertised product. Any product included in the order the customer
     * places is a sold product. If these products don't match then this is
     * considered cross-sell. Cross-sell revenue is the total value you made from
     * cross-sell attributed to your ads.
     * Example: Someone clicked on a Shopping ad for a hat then bought the same
     * hat and a shirt. The hat is priced $10 and the shirt is priced $20. The
     * cross-sell revenue of this order is $20.
     * This metric is only available if you report conversions with cart data.
     *
     * Generated from protobuf field <code>optional int64 cross_sell_revenue_micros = 306;</code>
     * @return int|string
     */
    public function getCrossSellRevenueMicros()
    {
        return isset($this->cross_sell_revenue_micros) ? $this->cross_sell_revenue_micros : 0;
    }

    public function hasCrossSellRevenueMicros()
    {
        return isset($this->cross_sell_revenue_micros);
    }

    public function clearCrossSellRevenueMicros()
    {
        unset($this->cross_sell_revenue_micros);
    }

    /**
     * Cross-sell revenue is the total amount you made from products sold as a
     * result of advertising a different product.
     * How it works: You report conversions with cart data for completed purchases
     * on your website. If the ad that was interacted with before the purchase has
     * an associated product (see Shopping Ads) then this product is considered
     * the advertised product. Any product included in the order the customer
     * places is a sold product. If these products don't match then this is
     * considered cross-sell. Cross-sell revenue is the total value you made from
     * cross-sell attributed to your ads.
     * Example: Someone clicked on a Shopping ad for a hat then bought the same
     * hat and a shirt. The hat is priced $10 and the shirt is priced $20. The
     * cross-sell revenue of this order is $20.
     * This metric is only available if you report conversions with cart data.
     *
     * Generated from protobuf field <code>optional int64 cross_sell_revenue_micros = 306;</code>
     * @param int|string $var
     * @return $this
     */
    public function setCrossSellRevenueMicros($var)
    {
        GPBUtil::checkInt64($var);
        $this->cross_sell_revenue_micros = $var;

        return $this;
    }

    /**
     * Cross-sell units sold is the total number of products sold as a result of
     * advertising a different product.
     * How it works: You report conversions with cart data for completed purchases
     * on your website. If the ad that was interacted with before the purchase has
     * an associated product (see Shopping Ads) then this product is considered
     * the advertised product. Any product included in the order the customer
     * places is a sold product. If these products don't match then this is
     * considered cross-sell. Cross-sell units sold is the total number of
     * cross-sold products from all orders attributed to your ads.
     * Example: Someone clicked on a Shopping ad for a hat then bought the same
     * hat, a shirt and a jacket. The cross-sell units sold in this order is 2.
     * This metric is only available if you report conversions with cart data.
     *
     * Generated from protobuf field <code>optional double cross_sell_units_sold = 307;</code>
     * @return float
     */
    public function getCrossSellUnitsSold()
    {
        return isset($this->cross_sell_units_sold) ? $this->cross_sell_units_sold : 0.0;
    }

    public function hasCrossSellUnitsSold()
    {
        return isset($this->cross_sell_units_sold);
    }

    public function clearCrossSellUnitsSold()
    {
        unset($this->cross_sell_units_sold);
    }

    /**
     * Cross-sell units sold is the total number of products sold as a result of
     * advertising a different product.
     * How it works: You report conversions with cart data for completed purchases
     * on your website. If the ad that was interacted with before the purchase has
     * an associated product (see Shopping Ads) then this product is considered
     * the advertised product. Any product included in the order the customer
     * places is a sold product. If these products don't match then this is
     * considered cross-sell. Cross-sell units sold is the total number of
     * cross-sold products from all orders attributed to your ads.
     * Example: Someone clicked on a Shopping ad for a hat then bought the same
     * hat, a shirt and a jacket. The cross-sell units sold in this order is 2.
     * This metric is only available if you report conversions with cart data.
     *
     * Generated from protobuf field <code>optional double cross_sell_units_sold = 307;</code>
     * @param float $var
     * @return $this
     */
    public function setCrossSellUnitsSold($var)
    {
        GPBUtil::checkDouble($var);
        $this->cross_sell_units_sold = $var;

        return $this;
    }

    /**
     * Lead cost of goods sold (COGS) is the total cost of products sold as a
     * result of advertising the same product.
     * How it works: You report conversions with cart data for completed purchases
     * on your website. If the ad that was interacted with has an associated
     * product (see Shopping Ads) then this product is considered the advertised
     * product. Any product included in the order the customer places is a sold
     * product. If the advertised and sold products match, then the cost of these
     * goods is counted under lead cost of goods sold.
     * Example: Someone clicked on a Shopping ad for a hat then bought the same
     * hat and a shirt. The hat has a cost of goods sold value of $3, the shirt
     * has a cost of goods sold value of $5. The lead cost of goods sold for this
     * order is $3.
     * This metric is only available if you report conversions with cart data.
     *
     * Generated from protobuf field <code>optional int64 lead_cost_of_goods_sold_micros = 308;</code>
     * @return int|string
     */
    public function getLeadCostOfGoodsSoldMicros()
    {
        return isset($this->lead_cost_of_goods_sold_micros) ? $this->lead_cost_of_goods_sold_micros : 0;
    }

    public function hasLeadCostOfGoodsSoldMicros()
    {
        return isset($this->lead_cost_of_goods_sold_micros);
    }

    public function clearLeadCostOfGoodsSoldMicros()
    {
        unset($this->lead_cost_of_goods_sold_micros);
    }

    /**
     * Lead cost of goods sold (COGS) is the total cost of products sold as a
     * result of advertising the same product.
     * How it works: You report conversions with cart data for completed purchases
     * on your website. If the ad that was interacted with has an associated
     * product (see Shopping Ads) then this product is considered the advertised
     * product. Any product included in the order the customer places is a sold
     * product. If the advertised and sold products match, then the cost of these
     * goods is counted under lead cost of goods sold.
     * Example: Someone clicked on a Shopping ad for a hat then bought the same
     * hat and a shirt. The hat has a cost of goods sold value of $3, the shirt
     * has a cost of goods sold value of $5. The lead cost of goods sold for this
     * order is $3.
     * This metric is only available if you report conversions with cart data.
     *
     * Generated from protobuf field <code>optional int64 lead_cost_of_goods_sold_micros = 308;</code>
     * @param int|string $var
     * @return $this
     */
    public function setLeadCostOfGoodsSoldMicros($var)
    {
        GPBUtil::checkInt64($var);
        $this->lead_cost_of_goods_sold_micros = $var;

        return $this;
    }

    /**
     * Lead gross profit is the profit you made from products sold as a result of
     * advertising the same product, minus cost of goods sold (COGS).
     * How it works: You report conversions with cart data for completed purchases
     * on your website. If the ad that was interacted with before the purchase has
     * an associated product (see Shopping Ads) then this product is considered
     * the advertised product. Any product included in the order the customer
     * places is a sold product. If the advertised and sold products match, then
     * the revenue you made from these sales minus the cost of goods sold is your
     * lead gross profit.
     * Example: Someone clicked on a Shopping ad for a hat then bought the same
     * hat and a shirt. The hat is priced $10 and has a cost of goods sold value
     * of $3. The lead gross profit of this order is $7 = $10 - $3.
     * This metric is only available if you report conversions with cart data.
     *
     * Generated from protobuf field <code>optional int64 lead_gross_profit_micros = 309;</code>
     * @return int|string
     */
    public function getLeadGrossProfitMicros()
    {
        return isset($this->lead_gross_profit_micros) ? $this->lead_gross_profit_micros : 0;
    }

    public function hasLeadGrossProfitMicros()
    {
        return isset($this->lead_gross_profit_micros);
    }

    public function clearLeadGrossProfitMicros()
    {
        unset($this->lead_gross_profit_micros);
    }

    /**
     * Lead gross profit is the profit you made from products sold as a result of
     * advertising the same product, minus cost of goods sold (COGS).
     * How it works: You report conversions with cart data for completed purchases
     * on your website. If the ad that was interacted with before the purchase has
     * an associated product (see Shopping Ads) then this product is considered
     * the advertised product. Any product included in the order the customer
     * places is a sold product. If the advertised and sold products match, then
     * the revenue you made from these sales minus the cost of goods sold is your
     * lead gross profit.
     * Example: Someone clicked on a Shopping ad for a hat then bought the same
     * hat and a shirt. The hat is priced $10 and has a cost of goods sold value
     * of $3. The lead gross profit of this order is $7 = $10 - $3.
     * This metric is only available if you report conversions with cart data.
     *
     * Generated from protobuf field <code>optional int64 lead_gross_profit_micros = 309;</code>
     * @param int|string $var
     * @return $this
     */
    public function setLeadGrossProfitMicros($var)
    {
        GPBUtil::checkInt64($var);
        $this->lead_gross_profit_micros = $var;

        return $this;
    }

    /**
     * Lead revenue is the total amount you made from products sold as a result of
     * advertising the same product.
     * How it works: You report conversions with cart data for completed purchases
     * on your website. If the ad that was interacted with before the purchase has
     * an associated product (see Shopping Ads) then this product is considered
     * the advertised product. Any product included in the order the customer
     * places is a sold product. If the advertised and sold products match, then
     * the total value you made from the sales of these products is shown under
     * lead revenue.
     * Example: Someone clicked on a Shopping ad for a hat then bought the same
     * hat and a shirt. The hat is priced $10 and the shirt is priced $20. The
     * lead revenue of this order is $10.
     * This metric is only available if you report conversions with cart data.
     *
     * Generated from protobuf field <code>optional int64 lead_revenue_micros = 310;</code>
     * @return int|string
     */
    public function getLeadRevenueMicros()
    {
        return isset($this->lead_revenue_micros) ? $this->lead_revenue_micros : 0;
    }

    public function hasLeadRevenueMicros()
    {
        return isset($this->lead_revenue_micros);
    }

    public function clearLeadRevenueMicros()
    {
        unset($this->lead_revenue_micros);
    }

    /**
     * Lead revenue is the total amount you made from products sold as a result of
     * advertising the same product.
     * How it works: You report conversions with cart data for completed purchases
     * on your website. If the ad that was interacted with before the purchase has
     * an associated product (see Shopping Ads) then this product is considered
     * the advertised product. Any product included in the order the customer
     * places is a sold product. If the advertised and sold products match, then
     * the total value you made from the sales of these products is shown under
     * lead revenue.
     * Example: Someone clicked on a Shopping ad for a hat then bought the same
     * hat and a shirt. The hat is priced $10 and the shirt is priced $20. The
     * lead revenue of this order is $10.
     * This metric is only available if you report conversions with cart data.
     *
     * Generated from protobuf field <code>optional int64 lead_revenue_micros = 310;</code>
     * @param int|string $var
     * @return $this
     */
    public function setLeadRevenueMicros($var)
    {
        GPBUtil::checkInt64($var);
        $this->lead_revenue_micros = $var;

        return $this;
    }

    /**
     * Lead units sold is the total number of products sold as a result of
     * advertising the same product.
     * How it works: You report conversions with cart data for completed purchases
     * on your website. If the ad that was interacted with before the purchase has
     * an associated product (see Shopping Ads) then this product is considered
     * the advertised product. Any product included in the order the customer
     * places is a sold product. If the advertised and sold products match, then
     * the total number of these products sold is shown under lead units sold.
     * Example: Someone clicked on a Shopping ad for a hat then bought the same
     * hat, a shirt and a jacket. The lead units sold in this order is 1.
     * This metric is only available if you report conversions with cart data.
     *
     * Generated from protobuf field <code>optional double lead_units_sold = 311;</code>
     * @return float
     */
    public function getLeadUnitsSold()
    {
        return isset($this->lead_units_sold) ? $this->lead_units_sold : 0.0;
    }

    public function hasLeadUnitsSold()
    {
        return isset($this->lead_units_sold);
    }

    public function clearLeadUnitsSold()
    {
        unset($this->lead_units_sold);
    }

    /**
     * Lead units sold is the total number of products sold as a result of
     * advertising the same product.
     * How it works: You report conversions with cart data for completed purchases
     * on your website. If the ad that was interacted with before the purchase has
     * an associated product (see Shopping Ads) then this product is considered
     * the advertised product. Any product included in the order the customer
     * places is a sold product. If the advertised and sold products match, then
     * the total number of these products sold is shown under lead units sold.
     * Example: Someone clicked on a Shopping ad for a hat then bought the same
     * hat, a shirt and a jacket. The lead units sold in this order is 1.
     * This metric is only available if you report conversions with cart data.
     *
     * Generated from protobuf field <code>optional double lead_units_sold = 311;</code>
     * @param float $var
     * @return $this
     */
    public function setLeadUnitsSold($var)
    {
        GPBUtil::checkDouble($var);
        $this->lead_units_sold = $var;

        return $this;
    }

    /**
     * The number of unique users who saw your ad during the requested time
     * period. This metric cannot be aggregated, and can only be requested for
     * date ranges of 92 days or less. This metric is available for following
     * campaign types - Display, Video, Discovery and App.
     *
     * Generated from protobuf field <code>optional int64 unique_users = 319;</code>
     * @return int|string
     */
    public function getUniqueUsers()
    {
        return isset($this->unique_users) ? $this->unique_users : 0;
    }

    public function hasUniqueUsers()
    {
        return isset($this->unique_users);
    }

    public function clearUniqueUsers()
    {
        unset($this->unique_users);
    }

    /**
     * The number of unique users who saw your ad during the requested time
     * period. This metric cannot be aggregated, and can only be requested for
     * date ranges of 92 days or less. This metric is available for following
     * campaign types - Display, Video, Discovery and App.
     *
     * Generated from protobuf field <code>optional int64 unique_users = 319;</code>
     * @param int|string $var
     * @return $this
     */
    public function setUniqueUsers($var)
    {
        GPBUtil::checkInt64($var);
        $this->unique_users = $var;

        return $this;
    }

    /**
     * The average number of times a unique user saw your ad during the requested
     * time period. This metric cannot be aggregated, and can only be requested
     * for date ranges of 92 days or less. This metric is available for following
     * campaign types - Display, Video, Discovery and App.
     *
     * Generated from protobuf field <code>optional double average_impression_frequency_per_user = 320;</code>
     * @return float
     */
    public function getAverageImpressionFrequencyPerUser()
    {
        return isset($this->average_impression_frequency_per_user) ? $this->average_impression_frequency_per_user : 0.0;
    }

    public function hasAverageImpressionFrequencyPerUser()
    {
        return isset($this->average_impression_frequency_per_user);
    }

    public function clearAverageImpressionFrequencyPerUser()
    {
        unset($this->average_impression_frequency_per_user);
    }

    /**
     * The average number of times a unique user saw your ad during the requested
     * time period. This metric cannot be aggregated, and can only be requested
     * for date ranges of 92 days or less. This metric is available for following
     * campaign types - Display, Video, Discovery and App.
     *
     * Generated from protobuf field <code>optional double average_impression_frequency_per_user = 320;</code>
     * @param float $var
     * @return $this
     */
    public function setAverageImpressionFrequencyPerUser($var)
    {
        GPBUtil::checkDouble($var);
        $this->average_impression_frequency_per_user = $var;

        return $this;
    }

}

