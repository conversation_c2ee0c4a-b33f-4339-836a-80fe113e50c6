<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v17/services/audience_insights_service.proto

namespace Google\Ads\GoogleAds\V17\Services;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Response message for
 * [AudienceInsightsService.GenerateSuggestedTargetingInsights][google.ads.googleads.v17.services.AudienceInsightsService.GenerateSuggestedTargetingInsights].
 *
 * Generated from protobuf message <code>google.ads.googleads.v17.services.GenerateSuggestedTargetingInsightsResponse</code>
 */
class GenerateSuggestedTargetingInsightsResponse extends \Google\Protobuf\Internal\Message
{
    /**
     * Suggested insights for targetable audiences.
     *
     * Generated from protobuf field <code>repeated .google.ads.googleads.v17.services.TargetingSuggestionMetrics suggestions = 1;</code>
     */
    private $suggestions;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type array<\Google\Ads\GoogleAds\V17\Services\TargetingSuggestionMetrics>|\Google\Protobuf\Internal\RepeatedField $suggestions
     *           Suggested insights for targetable audiences.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Ads\GoogleAds\V17\Services\AudienceInsightsService::initOnce();
        parent::__construct($data);
    }

    /**
     * Suggested insights for targetable audiences.
     *
     * Generated from protobuf field <code>repeated .google.ads.googleads.v17.services.TargetingSuggestionMetrics suggestions = 1;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getSuggestions()
    {
        return $this->suggestions;
    }

    /**
     * Suggested insights for targetable audiences.
     *
     * Generated from protobuf field <code>repeated .google.ads.googleads.v17.services.TargetingSuggestionMetrics suggestions = 1;</code>
     * @param array<\Google\Ads\GoogleAds\V17\Services\TargetingSuggestionMetrics>|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setSuggestions($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::MESSAGE, \Google\Ads\GoogleAds\V17\Services\TargetingSuggestionMetrics::class);
        $this->suggestions = $arr;

        return $this;
    }

}

