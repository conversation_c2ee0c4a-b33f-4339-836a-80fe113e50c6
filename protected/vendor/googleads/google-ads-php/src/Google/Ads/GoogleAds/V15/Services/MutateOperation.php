<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v15/services/google_ads_service.proto

namespace Google\Ads\GoogleAds\V15\Services;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * A single operation (create, update, remove) on a resource.
 *
 * Generated from protobuf message <code>google.ads.googleads.v15.services.MutateOperation</code>
 */
class MutateOperation extends \Google\Protobuf\Internal\Message
{
    protected $operation;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type \Google\Ads\GoogleAds\V15\Services\AdGroupAdLabelOperation $ad_group_ad_label_operation
     *           An ad group ad label mutate operation.
     *     @type \Google\Ads\GoogleAds\V15\Services\AdGroupAdOperation $ad_group_ad_operation
     *           An ad group ad mutate operation.
     *     @type \Google\Ads\GoogleAds\V15\Services\AdGroupAssetOperation $ad_group_asset_operation
     *           An ad group asset mutate operation.
     *     @type \Google\Ads\GoogleAds\V15\Services\AdGroupBidModifierOperation $ad_group_bid_modifier_operation
     *           An ad group bid modifier mutate operation.
     *     @type \Google\Ads\GoogleAds\V15\Services\AdGroupCriterionCustomizerOperation $ad_group_criterion_customizer_operation
     *           An ad group criterion customizer mutate operation.
     *     @type \Google\Ads\GoogleAds\V15\Services\AdGroupCriterionLabelOperation $ad_group_criterion_label_operation
     *           An ad group criterion label mutate operation.
     *     @type \Google\Ads\GoogleAds\V15\Services\AdGroupCriterionOperation $ad_group_criterion_operation
     *           An ad group criterion mutate operation.
     *     @type \Google\Ads\GoogleAds\V15\Services\AdGroupCustomizerOperation $ad_group_customizer_operation
     *           An ad group customizer mutate operation.
     *     @type \Google\Ads\GoogleAds\V15\Services\AdGroupExtensionSettingOperation $ad_group_extension_setting_operation
     *           An ad group extension setting mutate operation.
     *     @type \Google\Ads\GoogleAds\V15\Services\AdGroupFeedOperation $ad_group_feed_operation
     *           An ad group feed mutate operation.
     *     @type \Google\Ads\GoogleAds\V15\Services\AdGroupLabelOperation $ad_group_label_operation
     *           An ad group label mutate operation.
     *     @type \Google\Ads\GoogleAds\V15\Services\AdGroupOperation $ad_group_operation
     *           An ad group mutate operation.
     *     @type \Google\Ads\GoogleAds\V15\Services\AdOperation $ad_operation
     *           An ad mutate operation.
     *     @type \Google\Ads\GoogleAds\V15\Services\AdParameterOperation $ad_parameter_operation
     *           An ad parameter mutate operation.
     *     @type \Google\Ads\GoogleAds\V15\Services\AssetOperation $asset_operation
     *           An asset mutate operation.
     *     @type \Google\Ads\GoogleAds\V15\Services\AssetGroupAssetOperation $asset_group_asset_operation
     *           An asset group asset mutate operation.
     *     @type \Google\Ads\GoogleAds\V15\Services\AssetGroupListingGroupFilterOperation $asset_group_listing_group_filter_operation
     *           An asset group listing group filter mutate operation.
     *     @type \Google\Ads\GoogleAds\V15\Services\AssetGroupSignalOperation $asset_group_signal_operation
     *           An asset group signal mutate operation.
     *     @type \Google\Ads\GoogleAds\V15\Services\AssetGroupOperation $asset_group_operation
     *           An asset group mutate operation.
     *     @type \Google\Ads\GoogleAds\V15\Services\AssetSetAssetOperation $asset_set_asset_operation
     *           An asset set asset mutate operation.
     *     @type \Google\Ads\GoogleAds\V15\Services\AssetSetOperation $asset_set_operation
     *           An asset set mutate operation.
     *     @type \Google\Ads\GoogleAds\V15\Services\AudienceOperation $audience_operation
     *           An audience mutate operation.
     *     @type \Google\Ads\GoogleAds\V15\Services\BiddingDataExclusionOperation $bidding_data_exclusion_operation
     *           A bidding data exclusion mutate operation.
     *     @type \Google\Ads\GoogleAds\V15\Services\BiddingSeasonalityAdjustmentOperation $bidding_seasonality_adjustment_operation
     *           A bidding seasonality adjustment mutate operation.
     *     @type \Google\Ads\GoogleAds\V15\Services\BiddingStrategyOperation $bidding_strategy_operation
     *           A bidding strategy mutate operation.
     *     @type \Google\Ads\GoogleAds\V15\Services\CampaignAssetOperation $campaign_asset_operation
     *           A campaign asset mutate operation.
     *     @type \Google\Ads\GoogleAds\V15\Services\CampaignAssetSetOperation $campaign_asset_set_operation
     *           A campaign asset mutate operation.
     *     @type \Google\Ads\GoogleAds\V15\Services\CampaignBidModifierOperation $campaign_bid_modifier_operation
     *           A campaign bid modifier mutate operation.
     *     @type \Google\Ads\GoogleAds\V15\Services\CampaignBudgetOperation $campaign_budget_operation
     *           A campaign budget mutate operation.
     *     @type \Google\Ads\GoogleAds\V15\Services\CampaignConversionGoalOperation $campaign_conversion_goal_operation
     *           A campaign conversion goal mutate operation.
     *     @type \Google\Ads\GoogleAds\V15\Services\CampaignCriterionOperation $campaign_criterion_operation
     *           A campaign criterion mutate operation.
     *     @type \Google\Ads\GoogleAds\V15\Services\CampaignCustomizerOperation $campaign_customizer_operation
     *           A campaign customizer mutate operation.
     *     @type \Google\Ads\GoogleAds\V15\Services\CampaignDraftOperation $campaign_draft_operation
     *           A campaign draft mutate operation.
     *     @type \Google\Ads\GoogleAds\V15\Services\CampaignExtensionSettingOperation $campaign_extension_setting_operation
     *           A campaign extension setting mutate operation.
     *     @type \Google\Ads\GoogleAds\V15\Services\CampaignFeedOperation $campaign_feed_operation
     *           A campaign feed mutate operation.
     *     @type \Google\Ads\GoogleAds\V15\Services\CampaignGroupOperation $campaign_group_operation
     *           A campaign group mutate operation.
     *     @type \Google\Ads\GoogleAds\V15\Services\CampaignLabelOperation $campaign_label_operation
     *           A campaign label mutate operation.
     *     @type \Google\Ads\GoogleAds\V15\Services\CampaignOperation $campaign_operation
     *           A campaign mutate operation.
     *     @type \Google\Ads\GoogleAds\V15\Services\CampaignSharedSetOperation $campaign_shared_set_operation
     *           A campaign shared set mutate operation.
     *     @type \Google\Ads\GoogleAds\V15\Services\ConversionActionOperation $conversion_action_operation
     *           A conversion action mutate operation.
     *     @type \Google\Ads\GoogleAds\V15\Services\ConversionCustomVariableOperation $conversion_custom_variable_operation
     *           A conversion custom variable mutate operation.
     *     @type \Google\Ads\GoogleAds\V15\Services\ConversionGoalCampaignConfigOperation $conversion_goal_campaign_config_operation
     *           A conversion goal campaign config mutate operation.
     *     @type \Google\Ads\GoogleAds\V15\Services\ConversionValueRuleOperation $conversion_value_rule_operation
     *           A conversion value rule mutate operation.
     *     @type \Google\Ads\GoogleAds\V15\Services\ConversionValueRuleSetOperation $conversion_value_rule_set_operation
     *           A conversion value rule set mutate operation.
     *     @type \Google\Ads\GoogleAds\V15\Services\CustomConversionGoalOperation $custom_conversion_goal_operation
     *           A custom conversion goal mutate operation.
     *     @type \Google\Ads\GoogleAds\V15\Services\CustomerAssetOperation $customer_asset_operation
     *           A customer asset mutate operation.
     *     @type \Google\Ads\GoogleAds\V15\Services\CustomerConversionGoalOperation $customer_conversion_goal_operation
     *           A customer conversion goal mutate operation.
     *     @type \Google\Ads\GoogleAds\V15\Services\CustomerCustomizerOperation $customer_customizer_operation
     *           A customer customizer mutate operation.
     *     @type \Google\Ads\GoogleAds\V15\Services\CustomerExtensionSettingOperation $customer_extension_setting_operation
     *           A customer extension setting mutate operation.
     *     @type \Google\Ads\GoogleAds\V15\Services\CustomerFeedOperation $customer_feed_operation
     *           A customer feed mutate operation.
     *     @type \Google\Ads\GoogleAds\V15\Services\CustomerLabelOperation $customer_label_operation
     *           A customer label mutate operation.
     *     @type \Google\Ads\GoogleAds\V15\Services\CustomerNegativeCriterionOperation $customer_negative_criterion_operation
     *           A customer negative criterion mutate operation.
     *     @type \Google\Ads\GoogleAds\V15\Services\CustomerOperation $customer_operation
     *           A customer mutate operation.
     *     @type \Google\Ads\GoogleAds\V15\Services\CustomizerAttributeOperation $customizer_attribute_operation
     *           A customizer attribute mutate operation.
     *     @type \Google\Ads\GoogleAds\V15\Services\ExperimentOperation $experiment_operation
     *           An experiment mutate operation.
     *     @type \Google\Ads\GoogleAds\V15\Services\ExperimentArmOperation $experiment_arm_operation
     *           An experiment arm mutate operation.
     *     @type \Google\Ads\GoogleAds\V15\Services\ExtensionFeedItemOperation $extension_feed_item_operation
     *           An extension feed item mutate operation.
     *     @type \Google\Ads\GoogleAds\V15\Services\FeedItemOperation $feed_item_operation
     *           A feed item mutate operation.
     *     @type \Google\Ads\GoogleAds\V15\Services\FeedItemSetOperation $feed_item_set_operation
     *           A feed item set mutate operation.
     *     @type \Google\Ads\GoogleAds\V15\Services\FeedItemSetLinkOperation $feed_item_set_link_operation
     *           A feed item set link mutate operation.
     *     @type \Google\Ads\GoogleAds\V15\Services\FeedItemTargetOperation $feed_item_target_operation
     *           A feed item target mutate operation.
     *     @type \Google\Ads\GoogleAds\V15\Services\FeedMappingOperation $feed_mapping_operation
     *           A feed mapping mutate operation.
     *     @type \Google\Ads\GoogleAds\V15\Services\FeedOperation $feed_operation
     *           A feed mutate operation.
     *     @type \Google\Ads\GoogleAds\V15\Services\KeywordPlanAdGroupOperation $keyword_plan_ad_group_operation
     *           A keyword plan ad group operation.
     *     @type \Google\Ads\GoogleAds\V15\Services\KeywordPlanAdGroupKeywordOperation $keyword_plan_ad_group_keyword_operation
     *           A keyword plan ad group keyword operation.
     *     @type \Google\Ads\GoogleAds\V15\Services\KeywordPlanCampaignKeywordOperation $keyword_plan_campaign_keyword_operation
     *           A keyword plan campaign keyword operation.
     *     @type \Google\Ads\GoogleAds\V15\Services\KeywordPlanCampaignOperation $keyword_plan_campaign_operation
     *           A keyword plan campaign operation.
     *     @type \Google\Ads\GoogleAds\V15\Services\KeywordPlanOperation $keyword_plan_operation
     *           A keyword plan operation.
     *     @type \Google\Ads\GoogleAds\V15\Services\LabelOperation $label_operation
     *           A label mutate operation.
     *     @type \Google\Ads\GoogleAds\V15\Services\RecommendationSubscriptionOperation $recommendation_subscription_operation
     *           A recommendation subscription mutate operation.
     *     @type \Google\Ads\GoogleAds\V15\Services\RemarketingActionOperation $remarketing_action_operation
     *           A remarketing action mutate operation.
     *     @type \Google\Ads\GoogleAds\V15\Services\SharedCriterionOperation $shared_criterion_operation
     *           A shared criterion mutate operation.
     *     @type \Google\Ads\GoogleAds\V15\Services\SharedSetOperation $shared_set_operation
     *           A shared set mutate operation.
     *     @type \Google\Ads\GoogleAds\V15\Services\SmartCampaignSettingOperation $smart_campaign_setting_operation
     *           A Smart campaign setting mutate operation.
     *     @type \Google\Ads\GoogleAds\V15\Services\UserListOperation $user_list_operation
     *           A user list mutate operation.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Ads\GoogleAds\V15\Services\GoogleAdsService::initOnce();
        parent::__construct($data);
    }

    /**
     * An ad group ad label mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.AdGroupAdLabelOperation ad_group_ad_label_operation = 17;</code>
     * @return \Google\Ads\GoogleAds\V15\Services\AdGroupAdLabelOperation|null
     */
    public function getAdGroupAdLabelOperation()
    {
        return $this->readOneof(17);
    }

    public function hasAdGroupAdLabelOperation()
    {
        return $this->hasOneof(17);
    }

    /**
     * An ad group ad label mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.AdGroupAdLabelOperation ad_group_ad_label_operation = 17;</code>
     * @param \Google\Ads\GoogleAds\V15\Services\AdGroupAdLabelOperation $var
     * @return $this
     */
    public function setAdGroupAdLabelOperation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Services\AdGroupAdLabelOperation::class);
        $this->writeOneof(17, $var);

        return $this;
    }

    /**
     * An ad group ad mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.AdGroupAdOperation ad_group_ad_operation = 1;</code>
     * @return \Google\Ads\GoogleAds\V15\Services\AdGroupAdOperation|null
     */
    public function getAdGroupAdOperation()
    {
        return $this->readOneof(1);
    }

    public function hasAdGroupAdOperation()
    {
        return $this->hasOneof(1);
    }

    /**
     * An ad group ad mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.AdGroupAdOperation ad_group_ad_operation = 1;</code>
     * @param \Google\Ads\GoogleAds\V15\Services\AdGroupAdOperation $var
     * @return $this
     */
    public function setAdGroupAdOperation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Services\AdGroupAdOperation::class);
        $this->writeOneof(1, $var);

        return $this;
    }

    /**
     * An ad group asset mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.AdGroupAssetOperation ad_group_asset_operation = 56;</code>
     * @return \Google\Ads\GoogleAds\V15\Services\AdGroupAssetOperation|null
     */
    public function getAdGroupAssetOperation()
    {
        return $this->readOneof(56);
    }

    public function hasAdGroupAssetOperation()
    {
        return $this->hasOneof(56);
    }

    /**
     * An ad group asset mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.AdGroupAssetOperation ad_group_asset_operation = 56;</code>
     * @param \Google\Ads\GoogleAds\V15\Services\AdGroupAssetOperation $var
     * @return $this
     */
    public function setAdGroupAssetOperation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Services\AdGroupAssetOperation::class);
        $this->writeOneof(56, $var);

        return $this;
    }

    /**
     * An ad group bid modifier mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.AdGroupBidModifierOperation ad_group_bid_modifier_operation = 2;</code>
     * @return \Google\Ads\GoogleAds\V15\Services\AdGroupBidModifierOperation|null
     */
    public function getAdGroupBidModifierOperation()
    {
        return $this->readOneof(2);
    }

    public function hasAdGroupBidModifierOperation()
    {
        return $this->hasOneof(2);
    }

    /**
     * An ad group bid modifier mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.AdGroupBidModifierOperation ad_group_bid_modifier_operation = 2;</code>
     * @param \Google\Ads\GoogleAds\V15\Services\AdGroupBidModifierOperation $var
     * @return $this
     */
    public function setAdGroupBidModifierOperation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Services\AdGroupBidModifierOperation::class);
        $this->writeOneof(2, $var);

        return $this;
    }

    /**
     * An ad group criterion customizer mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.AdGroupCriterionCustomizerOperation ad_group_criterion_customizer_operation = 77;</code>
     * @return \Google\Ads\GoogleAds\V15\Services\AdGroupCriterionCustomizerOperation|null
     */
    public function getAdGroupCriterionCustomizerOperation()
    {
        return $this->readOneof(77);
    }

    public function hasAdGroupCriterionCustomizerOperation()
    {
        return $this->hasOneof(77);
    }

    /**
     * An ad group criterion customizer mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.AdGroupCriterionCustomizerOperation ad_group_criterion_customizer_operation = 77;</code>
     * @param \Google\Ads\GoogleAds\V15\Services\AdGroupCriterionCustomizerOperation $var
     * @return $this
     */
    public function setAdGroupCriterionCustomizerOperation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Services\AdGroupCriterionCustomizerOperation::class);
        $this->writeOneof(77, $var);

        return $this;
    }

    /**
     * An ad group criterion label mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.AdGroupCriterionLabelOperation ad_group_criterion_label_operation = 18;</code>
     * @return \Google\Ads\GoogleAds\V15\Services\AdGroupCriterionLabelOperation|null
     */
    public function getAdGroupCriterionLabelOperation()
    {
        return $this->readOneof(18);
    }

    public function hasAdGroupCriterionLabelOperation()
    {
        return $this->hasOneof(18);
    }

    /**
     * An ad group criterion label mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.AdGroupCriterionLabelOperation ad_group_criterion_label_operation = 18;</code>
     * @param \Google\Ads\GoogleAds\V15\Services\AdGroupCriterionLabelOperation $var
     * @return $this
     */
    public function setAdGroupCriterionLabelOperation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Services\AdGroupCriterionLabelOperation::class);
        $this->writeOneof(18, $var);

        return $this;
    }

    /**
     * An ad group criterion mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.AdGroupCriterionOperation ad_group_criterion_operation = 3;</code>
     * @return \Google\Ads\GoogleAds\V15\Services\AdGroupCriterionOperation|null
     */
    public function getAdGroupCriterionOperation()
    {
        return $this->readOneof(3);
    }

    public function hasAdGroupCriterionOperation()
    {
        return $this->hasOneof(3);
    }

    /**
     * An ad group criterion mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.AdGroupCriterionOperation ad_group_criterion_operation = 3;</code>
     * @param \Google\Ads\GoogleAds\V15\Services\AdGroupCriterionOperation $var
     * @return $this
     */
    public function setAdGroupCriterionOperation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Services\AdGroupCriterionOperation::class);
        $this->writeOneof(3, $var);

        return $this;
    }

    /**
     * An ad group customizer mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.AdGroupCustomizerOperation ad_group_customizer_operation = 75;</code>
     * @return \Google\Ads\GoogleAds\V15\Services\AdGroupCustomizerOperation|null
     */
    public function getAdGroupCustomizerOperation()
    {
        return $this->readOneof(75);
    }

    public function hasAdGroupCustomizerOperation()
    {
        return $this->hasOneof(75);
    }

    /**
     * An ad group customizer mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.AdGroupCustomizerOperation ad_group_customizer_operation = 75;</code>
     * @param \Google\Ads\GoogleAds\V15\Services\AdGroupCustomizerOperation $var
     * @return $this
     */
    public function setAdGroupCustomizerOperation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Services\AdGroupCustomizerOperation::class);
        $this->writeOneof(75, $var);

        return $this;
    }

    /**
     * An ad group extension setting mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.AdGroupExtensionSettingOperation ad_group_extension_setting_operation = 19;</code>
     * @return \Google\Ads\GoogleAds\V15\Services\AdGroupExtensionSettingOperation|null
     */
    public function getAdGroupExtensionSettingOperation()
    {
        return $this->readOneof(19);
    }

    public function hasAdGroupExtensionSettingOperation()
    {
        return $this->hasOneof(19);
    }

    /**
     * An ad group extension setting mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.AdGroupExtensionSettingOperation ad_group_extension_setting_operation = 19;</code>
     * @param \Google\Ads\GoogleAds\V15\Services\AdGroupExtensionSettingOperation $var
     * @return $this
     */
    public function setAdGroupExtensionSettingOperation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Services\AdGroupExtensionSettingOperation::class);
        $this->writeOneof(19, $var);

        return $this;
    }

    /**
     * An ad group feed mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.AdGroupFeedOperation ad_group_feed_operation = 20;</code>
     * @return \Google\Ads\GoogleAds\V15\Services\AdGroupFeedOperation|null
     */
    public function getAdGroupFeedOperation()
    {
        return $this->readOneof(20);
    }

    public function hasAdGroupFeedOperation()
    {
        return $this->hasOneof(20);
    }

    /**
     * An ad group feed mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.AdGroupFeedOperation ad_group_feed_operation = 20;</code>
     * @param \Google\Ads\GoogleAds\V15\Services\AdGroupFeedOperation $var
     * @return $this
     */
    public function setAdGroupFeedOperation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Services\AdGroupFeedOperation::class);
        $this->writeOneof(20, $var);

        return $this;
    }

    /**
     * An ad group label mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.AdGroupLabelOperation ad_group_label_operation = 21;</code>
     * @return \Google\Ads\GoogleAds\V15\Services\AdGroupLabelOperation|null
     */
    public function getAdGroupLabelOperation()
    {
        return $this->readOneof(21);
    }

    public function hasAdGroupLabelOperation()
    {
        return $this->hasOneof(21);
    }

    /**
     * An ad group label mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.AdGroupLabelOperation ad_group_label_operation = 21;</code>
     * @param \Google\Ads\GoogleAds\V15\Services\AdGroupLabelOperation $var
     * @return $this
     */
    public function setAdGroupLabelOperation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Services\AdGroupLabelOperation::class);
        $this->writeOneof(21, $var);

        return $this;
    }

    /**
     * An ad group mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.AdGroupOperation ad_group_operation = 5;</code>
     * @return \Google\Ads\GoogleAds\V15\Services\AdGroupOperation|null
     */
    public function getAdGroupOperation()
    {
        return $this->readOneof(5);
    }

    public function hasAdGroupOperation()
    {
        return $this->hasOneof(5);
    }

    /**
     * An ad group mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.AdGroupOperation ad_group_operation = 5;</code>
     * @param \Google\Ads\GoogleAds\V15\Services\AdGroupOperation $var
     * @return $this
     */
    public function setAdGroupOperation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Services\AdGroupOperation::class);
        $this->writeOneof(5, $var);

        return $this;
    }

    /**
     * An ad mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.AdOperation ad_operation = 49;</code>
     * @return \Google\Ads\GoogleAds\V15\Services\AdOperation|null
     */
    public function getAdOperation()
    {
        return $this->readOneof(49);
    }

    public function hasAdOperation()
    {
        return $this->hasOneof(49);
    }

    /**
     * An ad mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.AdOperation ad_operation = 49;</code>
     * @param \Google\Ads\GoogleAds\V15\Services\AdOperation $var
     * @return $this
     */
    public function setAdOperation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Services\AdOperation::class);
        $this->writeOneof(49, $var);

        return $this;
    }

    /**
     * An ad parameter mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.AdParameterOperation ad_parameter_operation = 22;</code>
     * @return \Google\Ads\GoogleAds\V15\Services\AdParameterOperation|null
     */
    public function getAdParameterOperation()
    {
        return $this->readOneof(22);
    }

    public function hasAdParameterOperation()
    {
        return $this->hasOneof(22);
    }

    /**
     * An ad parameter mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.AdParameterOperation ad_parameter_operation = 22;</code>
     * @param \Google\Ads\GoogleAds\V15\Services\AdParameterOperation $var
     * @return $this
     */
    public function setAdParameterOperation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Services\AdParameterOperation::class);
        $this->writeOneof(22, $var);

        return $this;
    }

    /**
     * An asset mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.AssetOperation asset_operation = 23;</code>
     * @return \Google\Ads\GoogleAds\V15\Services\AssetOperation|null
     */
    public function getAssetOperation()
    {
        return $this->readOneof(23);
    }

    public function hasAssetOperation()
    {
        return $this->hasOneof(23);
    }

    /**
     * An asset mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.AssetOperation asset_operation = 23;</code>
     * @param \Google\Ads\GoogleAds\V15\Services\AssetOperation $var
     * @return $this
     */
    public function setAssetOperation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Services\AssetOperation::class);
        $this->writeOneof(23, $var);

        return $this;
    }

    /**
     * An asset group asset mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.AssetGroupAssetOperation asset_group_asset_operation = 65;</code>
     * @return \Google\Ads\GoogleAds\V15\Services\AssetGroupAssetOperation|null
     */
    public function getAssetGroupAssetOperation()
    {
        return $this->readOneof(65);
    }

    public function hasAssetGroupAssetOperation()
    {
        return $this->hasOneof(65);
    }

    /**
     * An asset group asset mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.AssetGroupAssetOperation asset_group_asset_operation = 65;</code>
     * @param \Google\Ads\GoogleAds\V15\Services\AssetGroupAssetOperation $var
     * @return $this
     */
    public function setAssetGroupAssetOperation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Services\AssetGroupAssetOperation::class);
        $this->writeOneof(65, $var);

        return $this;
    }

    /**
     * An asset group listing group filter mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.AssetGroupListingGroupFilterOperation asset_group_listing_group_filter_operation = 78;</code>
     * @return \Google\Ads\GoogleAds\V15\Services\AssetGroupListingGroupFilterOperation|null
     */
    public function getAssetGroupListingGroupFilterOperation()
    {
        return $this->readOneof(78);
    }

    public function hasAssetGroupListingGroupFilterOperation()
    {
        return $this->hasOneof(78);
    }

    /**
     * An asset group listing group filter mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.AssetGroupListingGroupFilterOperation asset_group_listing_group_filter_operation = 78;</code>
     * @param \Google\Ads\GoogleAds\V15\Services\AssetGroupListingGroupFilterOperation $var
     * @return $this
     */
    public function setAssetGroupListingGroupFilterOperation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Services\AssetGroupListingGroupFilterOperation::class);
        $this->writeOneof(78, $var);

        return $this;
    }

    /**
     * An asset group signal mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.AssetGroupSignalOperation asset_group_signal_operation = 80;</code>
     * @return \Google\Ads\GoogleAds\V15\Services\AssetGroupSignalOperation|null
     */
    public function getAssetGroupSignalOperation()
    {
        return $this->readOneof(80);
    }

    public function hasAssetGroupSignalOperation()
    {
        return $this->hasOneof(80);
    }

    /**
     * An asset group signal mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.AssetGroupSignalOperation asset_group_signal_operation = 80;</code>
     * @param \Google\Ads\GoogleAds\V15\Services\AssetGroupSignalOperation $var
     * @return $this
     */
    public function setAssetGroupSignalOperation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Services\AssetGroupSignalOperation::class);
        $this->writeOneof(80, $var);

        return $this;
    }

    /**
     * An asset group mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.AssetGroupOperation asset_group_operation = 62;</code>
     * @return \Google\Ads\GoogleAds\V15\Services\AssetGroupOperation|null
     */
    public function getAssetGroupOperation()
    {
        return $this->readOneof(62);
    }

    public function hasAssetGroupOperation()
    {
        return $this->hasOneof(62);
    }

    /**
     * An asset group mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.AssetGroupOperation asset_group_operation = 62;</code>
     * @param \Google\Ads\GoogleAds\V15\Services\AssetGroupOperation $var
     * @return $this
     */
    public function setAssetGroupOperation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Services\AssetGroupOperation::class);
        $this->writeOneof(62, $var);

        return $this;
    }

    /**
     * An asset set asset mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.AssetSetAssetOperation asset_set_asset_operation = 71;</code>
     * @return \Google\Ads\GoogleAds\V15\Services\AssetSetAssetOperation|null
     */
    public function getAssetSetAssetOperation()
    {
        return $this->readOneof(71);
    }

    public function hasAssetSetAssetOperation()
    {
        return $this->hasOneof(71);
    }

    /**
     * An asset set asset mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.AssetSetAssetOperation asset_set_asset_operation = 71;</code>
     * @param \Google\Ads\GoogleAds\V15\Services\AssetSetAssetOperation $var
     * @return $this
     */
    public function setAssetSetAssetOperation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Services\AssetSetAssetOperation::class);
        $this->writeOneof(71, $var);

        return $this;
    }

    /**
     * An asset set mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.AssetSetOperation asset_set_operation = 72;</code>
     * @return \Google\Ads\GoogleAds\V15\Services\AssetSetOperation|null
     */
    public function getAssetSetOperation()
    {
        return $this->readOneof(72);
    }

    public function hasAssetSetOperation()
    {
        return $this->hasOneof(72);
    }

    /**
     * An asset set mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.AssetSetOperation asset_set_operation = 72;</code>
     * @param \Google\Ads\GoogleAds\V15\Services\AssetSetOperation $var
     * @return $this
     */
    public function setAssetSetOperation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Services\AssetSetOperation::class);
        $this->writeOneof(72, $var);

        return $this;
    }

    /**
     * An audience mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.AudienceOperation audience_operation = 81;</code>
     * @return \Google\Ads\GoogleAds\V15\Services\AudienceOperation|null
     */
    public function getAudienceOperation()
    {
        return $this->readOneof(81);
    }

    public function hasAudienceOperation()
    {
        return $this->hasOneof(81);
    }

    /**
     * An audience mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.AudienceOperation audience_operation = 81;</code>
     * @param \Google\Ads\GoogleAds\V15\Services\AudienceOperation $var
     * @return $this
     */
    public function setAudienceOperation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Services\AudienceOperation::class);
        $this->writeOneof(81, $var);

        return $this;
    }

    /**
     * A bidding data exclusion mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.BiddingDataExclusionOperation bidding_data_exclusion_operation = 58;</code>
     * @return \Google\Ads\GoogleAds\V15\Services\BiddingDataExclusionOperation|null
     */
    public function getBiddingDataExclusionOperation()
    {
        return $this->readOneof(58);
    }

    public function hasBiddingDataExclusionOperation()
    {
        return $this->hasOneof(58);
    }

    /**
     * A bidding data exclusion mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.BiddingDataExclusionOperation bidding_data_exclusion_operation = 58;</code>
     * @param \Google\Ads\GoogleAds\V15\Services\BiddingDataExclusionOperation $var
     * @return $this
     */
    public function setBiddingDataExclusionOperation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Services\BiddingDataExclusionOperation::class);
        $this->writeOneof(58, $var);

        return $this;
    }

    /**
     * A bidding seasonality adjustment mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.BiddingSeasonalityAdjustmentOperation bidding_seasonality_adjustment_operation = 59;</code>
     * @return \Google\Ads\GoogleAds\V15\Services\BiddingSeasonalityAdjustmentOperation|null
     */
    public function getBiddingSeasonalityAdjustmentOperation()
    {
        return $this->readOneof(59);
    }

    public function hasBiddingSeasonalityAdjustmentOperation()
    {
        return $this->hasOneof(59);
    }

    /**
     * A bidding seasonality adjustment mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.BiddingSeasonalityAdjustmentOperation bidding_seasonality_adjustment_operation = 59;</code>
     * @param \Google\Ads\GoogleAds\V15\Services\BiddingSeasonalityAdjustmentOperation $var
     * @return $this
     */
    public function setBiddingSeasonalityAdjustmentOperation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Services\BiddingSeasonalityAdjustmentOperation::class);
        $this->writeOneof(59, $var);

        return $this;
    }

    /**
     * A bidding strategy mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.BiddingStrategyOperation bidding_strategy_operation = 6;</code>
     * @return \Google\Ads\GoogleAds\V15\Services\BiddingStrategyOperation|null
     */
    public function getBiddingStrategyOperation()
    {
        return $this->readOneof(6);
    }

    public function hasBiddingStrategyOperation()
    {
        return $this->hasOneof(6);
    }

    /**
     * A bidding strategy mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.BiddingStrategyOperation bidding_strategy_operation = 6;</code>
     * @param \Google\Ads\GoogleAds\V15\Services\BiddingStrategyOperation $var
     * @return $this
     */
    public function setBiddingStrategyOperation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Services\BiddingStrategyOperation::class);
        $this->writeOneof(6, $var);

        return $this;
    }

    /**
     * A campaign asset mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.CampaignAssetOperation campaign_asset_operation = 52;</code>
     * @return \Google\Ads\GoogleAds\V15\Services\CampaignAssetOperation|null
     */
    public function getCampaignAssetOperation()
    {
        return $this->readOneof(52);
    }

    public function hasCampaignAssetOperation()
    {
        return $this->hasOneof(52);
    }

    /**
     * A campaign asset mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.CampaignAssetOperation campaign_asset_operation = 52;</code>
     * @param \Google\Ads\GoogleAds\V15\Services\CampaignAssetOperation $var
     * @return $this
     */
    public function setCampaignAssetOperation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Services\CampaignAssetOperation::class);
        $this->writeOneof(52, $var);

        return $this;
    }

    /**
     * A campaign asset mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.CampaignAssetSetOperation campaign_asset_set_operation = 73;</code>
     * @return \Google\Ads\GoogleAds\V15\Services\CampaignAssetSetOperation|null
     */
    public function getCampaignAssetSetOperation()
    {
        return $this->readOneof(73);
    }

    public function hasCampaignAssetSetOperation()
    {
        return $this->hasOneof(73);
    }

    /**
     * A campaign asset mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.CampaignAssetSetOperation campaign_asset_set_operation = 73;</code>
     * @param \Google\Ads\GoogleAds\V15\Services\CampaignAssetSetOperation $var
     * @return $this
     */
    public function setCampaignAssetSetOperation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Services\CampaignAssetSetOperation::class);
        $this->writeOneof(73, $var);

        return $this;
    }

    /**
     * A campaign bid modifier mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.CampaignBidModifierOperation campaign_bid_modifier_operation = 7;</code>
     * @return \Google\Ads\GoogleAds\V15\Services\CampaignBidModifierOperation|null
     */
    public function getCampaignBidModifierOperation()
    {
        return $this->readOneof(7);
    }

    public function hasCampaignBidModifierOperation()
    {
        return $this->hasOneof(7);
    }

    /**
     * A campaign bid modifier mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.CampaignBidModifierOperation campaign_bid_modifier_operation = 7;</code>
     * @param \Google\Ads\GoogleAds\V15\Services\CampaignBidModifierOperation $var
     * @return $this
     */
    public function setCampaignBidModifierOperation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Services\CampaignBidModifierOperation::class);
        $this->writeOneof(7, $var);

        return $this;
    }

    /**
     * A campaign budget mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.CampaignBudgetOperation campaign_budget_operation = 8;</code>
     * @return \Google\Ads\GoogleAds\V15\Services\CampaignBudgetOperation|null
     */
    public function getCampaignBudgetOperation()
    {
        return $this->readOneof(8);
    }

    public function hasCampaignBudgetOperation()
    {
        return $this->hasOneof(8);
    }

    /**
     * A campaign budget mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.CampaignBudgetOperation campaign_budget_operation = 8;</code>
     * @param \Google\Ads\GoogleAds\V15\Services\CampaignBudgetOperation $var
     * @return $this
     */
    public function setCampaignBudgetOperation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Services\CampaignBudgetOperation::class);
        $this->writeOneof(8, $var);

        return $this;
    }

    /**
     * A campaign conversion goal mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.CampaignConversionGoalOperation campaign_conversion_goal_operation = 67;</code>
     * @return \Google\Ads\GoogleAds\V15\Services\CampaignConversionGoalOperation|null
     */
    public function getCampaignConversionGoalOperation()
    {
        return $this->readOneof(67);
    }

    public function hasCampaignConversionGoalOperation()
    {
        return $this->hasOneof(67);
    }

    /**
     * A campaign conversion goal mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.CampaignConversionGoalOperation campaign_conversion_goal_operation = 67;</code>
     * @param \Google\Ads\GoogleAds\V15\Services\CampaignConversionGoalOperation $var
     * @return $this
     */
    public function setCampaignConversionGoalOperation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Services\CampaignConversionGoalOperation::class);
        $this->writeOneof(67, $var);

        return $this;
    }

    /**
     * A campaign criterion mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.CampaignCriterionOperation campaign_criterion_operation = 13;</code>
     * @return \Google\Ads\GoogleAds\V15\Services\CampaignCriterionOperation|null
     */
    public function getCampaignCriterionOperation()
    {
        return $this->readOneof(13);
    }

    public function hasCampaignCriterionOperation()
    {
        return $this->hasOneof(13);
    }

    /**
     * A campaign criterion mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.CampaignCriterionOperation campaign_criterion_operation = 13;</code>
     * @param \Google\Ads\GoogleAds\V15\Services\CampaignCriterionOperation $var
     * @return $this
     */
    public function setCampaignCriterionOperation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Services\CampaignCriterionOperation::class);
        $this->writeOneof(13, $var);

        return $this;
    }

    /**
     * A campaign customizer mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.CampaignCustomizerOperation campaign_customizer_operation = 76;</code>
     * @return \Google\Ads\GoogleAds\V15\Services\CampaignCustomizerOperation|null
     */
    public function getCampaignCustomizerOperation()
    {
        return $this->readOneof(76);
    }

    public function hasCampaignCustomizerOperation()
    {
        return $this->hasOneof(76);
    }

    /**
     * A campaign customizer mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.CampaignCustomizerOperation campaign_customizer_operation = 76;</code>
     * @param \Google\Ads\GoogleAds\V15\Services\CampaignCustomizerOperation $var
     * @return $this
     */
    public function setCampaignCustomizerOperation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Services\CampaignCustomizerOperation::class);
        $this->writeOneof(76, $var);

        return $this;
    }

    /**
     * A campaign draft mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.CampaignDraftOperation campaign_draft_operation = 24;</code>
     * @return \Google\Ads\GoogleAds\V15\Services\CampaignDraftOperation|null
     */
    public function getCampaignDraftOperation()
    {
        return $this->readOneof(24);
    }

    public function hasCampaignDraftOperation()
    {
        return $this->hasOneof(24);
    }

    /**
     * A campaign draft mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.CampaignDraftOperation campaign_draft_operation = 24;</code>
     * @param \Google\Ads\GoogleAds\V15\Services\CampaignDraftOperation $var
     * @return $this
     */
    public function setCampaignDraftOperation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Services\CampaignDraftOperation::class);
        $this->writeOneof(24, $var);

        return $this;
    }

    /**
     * A campaign extension setting mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.CampaignExtensionSettingOperation campaign_extension_setting_operation = 26;</code>
     * @return \Google\Ads\GoogleAds\V15\Services\CampaignExtensionSettingOperation|null
     */
    public function getCampaignExtensionSettingOperation()
    {
        return $this->readOneof(26);
    }

    public function hasCampaignExtensionSettingOperation()
    {
        return $this->hasOneof(26);
    }

    /**
     * A campaign extension setting mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.CampaignExtensionSettingOperation campaign_extension_setting_operation = 26;</code>
     * @param \Google\Ads\GoogleAds\V15\Services\CampaignExtensionSettingOperation $var
     * @return $this
     */
    public function setCampaignExtensionSettingOperation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Services\CampaignExtensionSettingOperation::class);
        $this->writeOneof(26, $var);

        return $this;
    }

    /**
     * A campaign feed mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.CampaignFeedOperation campaign_feed_operation = 27;</code>
     * @return \Google\Ads\GoogleAds\V15\Services\CampaignFeedOperation|null
     */
    public function getCampaignFeedOperation()
    {
        return $this->readOneof(27);
    }

    public function hasCampaignFeedOperation()
    {
        return $this->hasOneof(27);
    }

    /**
     * A campaign feed mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.CampaignFeedOperation campaign_feed_operation = 27;</code>
     * @param \Google\Ads\GoogleAds\V15\Services\CampaignFeedOperation $var
     * @return $this
     */
    public function setCampaignFeedOperation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Services\CampaignFeedOperation::class);
        $this->writeOneof(27, $var);

        return $this;
    }

    /**
     * A campaign group mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.CampaignGroupOperation campaign_group_operation = 9;</code>
     * @return \Google\Ads\GoogleAds\V15\Services\CampaignGroupOperation|null
     */
    public function getCampaignGroupOperation()
    {
        return $this->readOneof(9);
    }

    public function hasCampaignGroupOperation()
    {
        return $this->hasOneof(9);
    }

    /**
     * A campaign group mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.CampaignGroupOperation campaign_group_operation = 9;</code>
     * @param \Google\Ads\GoogleAds\V15\Services\CampaignGroupOperation $var
     * @return $this
     */
    public function setCampaignGroupOperation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Services\CampaignGroupOperation::class);
        $this->writeOneof(9, $var);

        return $this;
    }

    /**
     * A campaign label mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.CampaignLabelOperation campaign_label_operation = 28;</code>
     * @return \Google\Ads\GoogleAds\V15\Services\CampaignLabelOperation|null
     */
    public function getCampaignLabelOperation()
    {
        return $this->readOneof(28);
    }

    public function hasCampaignLabelOperation()
    {
        return $this->hasOneof(28);
    }

    /**
     * A campaign label mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.CampaignLabelOperation campaign_label_operation = 28;</code>
     * @param \Google\Ads\GoogleAds\V15\Services\CampaignLabelOperation $var
     * @return $this
     */
    public function setCampaignLabelOperation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Services\CampaignLabelOperation::class);
        $this->writeOneof(28, $var);

        return $this;
    }

    /**
     * A campaign mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.CampaignOperation campaign_operation = 10;</code>
     * @return \Google\Ads\GoogleAds\V15\Services\CampaignOperation|null
     */
    public function getCampaignOperation()
    {
        return $this->readOneof(10);
    }

    public function hasCampaignOperation()
    {
        return $this->hasOneof(10);
    }

    /**
     * A campaign mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.CampaignOperation campaign_operation = 10;</code>
     * @param \Google\Ads\GoogleAds\V15\Services\CampaignOperation $var
     * @return $this
     */
    public function setCampaignOperation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Services\CampaignOperation::class);
        $this->writeOneof(10, $var);

        return $this;
    }

    /**
     * A campaign shared set mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.CampaignSharedSetOperation campaign_shared_set_operation = 11;</code>
     * @return \Google\Ads\GoogleAds\V15\Services\CampaignSharedSetOperation|null
     */
    public function getCampaignSharedSetOperation()
    {
        return $this->readOneof(11);
    }

    public function hasCampaignSharedSetOperation()
    {
        return $this->hasOneof(11);
    }

    /**
     * A campaign shared set mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.CampaignSharedSetOperation campaign_shared_set_operation = 11;</code>
     * @param \Google\Ads\GoogleAds\V15\Services\CampaignSharedSetOperation $var
     * @return $this
     */
    public function setCampaignSharedSetOperation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Services\CampaignSharedSetOperation::class);
        $this->writeOneof(11, $var);

        return $this;
    }

    /**
     * A conversion action mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.ConversionActionOperation conversion_action_operation = 12;</code>
     * @return \Google\Ads\GoogleAds\V15\Services\ConversionActionOperation|null
     */
    public function getConversionActionOperation()
    {
        return $this->readOneof(12);
    }

    public function hasConversionActionOperation()
    {
        return $this->hasOneof(12);
    }

    /**
     * A conversion action mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.ConversionActionOperation conversion_action_operation = 12;</code>
     * @param \Google\Ads\GoogleAds\V15\Services\ConversionActionOperation $var
     * @return $this
     */
    public function setConversionActionOperation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Services\ConversionActionOperation::class);
        $this->writeOneof(12, $var);

        return $this;
    }

    /**
     * A conversion custom variable mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.ConversionCustomVariableOperation conversion_custom_variable_operation = 55;</code>
     * @return \Google\Ads\GoogleAds\V15\Services\ConversionCustomVariableOperation|null
     */
    public function getConversionCustomVariableOperation()
    {
        return $this->readOneof(55);
    }

    public function hasConversionCustomVariableOperation()
    {
        return $this->hasOneof(55);
    }

    /**
     * A conversion custom variable mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.ConversionCustomVariableOperation conversion_custom_variable_operation = 55;</code>
     * @param \Google\Ads\GoogleAds\V15\Services\ConversionCustomVariableOperation $var
     * @return $this
     */
    public function setConversionCustomVariableOperation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Services\ConversionCustomVariableOperation::class);
        $this->writeOneof(55, $var);

        return $this;
    }

    /**
     * A conversion goal campaign config mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.ConversionGoalCampaignConfigOperation conversion_goal_campaign_config_operation = 69;</code>
     * @return \Google\Ads\GoogleAds\V15\Services\ConversionGoalCampaignConfigOperation|null
     */
    public function getConversionGoalCampaignConfigOperation()
    {
        return $this->readOneof(69);
    }

    public function hasConversionGoalCampaignConfigOperation()
    {
        return $this->hasOneof(69);
    }

    /**
     * A conversion goal campaign config mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.ConversionGoalCampaignConfigOperation conversion_goal_campaign_config_operation = 69;</code>
     * @param \Google\Ads\GoogleAds\V15\Services\ConversionGoalCampaignConfigOperation $var
     * @return $this
     */
    public function setConversionGoalCampaignConfigOperation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Services\ConversionGoalCampaignConfigOperation::class);
        $this->writeOneof(69, $var);

        return $this;
    }

    /**
     * A conversion value rule mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.ConversionValueRuleOperation conversion_value_rule_operation = 63;</code>
     * @return \Google\Ads\GoogleAds\V15\Services\ConversionValueRuleOperation|null
     */
    public function getConversionValueRuleOperation()
    {
        return $this->readOneof(63);
    }

    public function hasConversionValueRuleOperation()
    {
        return $this->hasOneof(63);
    }

    /**
     * A conversion value rule mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.ConversionValueRuleOperation conversion_value_rule_operation = 63;</code>
     * @param \Google\Ads\GoogleAds\V15\Services\ConversionValueRuleOperation $var
     * @return $this
     */
    public function setConversionValueRuleOperation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Services\ConversionValueRuleOperation::class);
        $this->writeOneof(63, $var);

        return $this;
    }

    /**
     * A conversion value rule set mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.ConversionValueRuleSetOperation conversion_value_rule_set_operation = 64;</code>
     * @return \Google\Ads\GoogleAds\V15\Services\ConversionValueRuleSetOperation|null
     */
    public function getConversionValueRuleSetOperation()
    {
        return $this->readOneof(64);
    }

    public function hasConversionValueRuleSetOperation()
    {
        return $this->hasOneof(64);
    }

    /**
     * A conversion value rule set mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.ConversionValueRuleSetOperation conversion_value_rule_set_operation = 64;</code>
     * @param \Google\Ads\GoogleAds\V15\Services\ConversionValueRuleSetOperation $var
     * @return $this
     */
    public function setConversionValueRuleSetOperation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Services\ConversionValueRuleSetOperation::class);
        $this->writeOneof(64, $var);

        return $this;
    }

    /**
     * A custom conversion goal mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.CustomConversionGoalOperation custom_conversion_goal_operation = 68;</code>
     * @return \Google\Ads\GoogleAds\V15\Services\CustomConversionGoalOperation|null
     */
    public function getCustomConversionGoalOperation()
    {
        return $this->readOneof(68);
    }

    public function hasCustomConversionGoalOperation()
    {
        return $this->hasOneof(68);
    }

    /**
     * A custom conversion goal mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.CustomConversionGoalOperation custom_conversion_goal_operation = 68;</code>
     * @param \Google\Ads\GoogleAds\V15\Services\CustomConversionGoalOperation $var
     * @return $this
     */
    public function setCustomConversionGoalOperation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Services\CustomConversionGoalOperation::class);
        $this->writeOneof(68, $var);

        return $this;
    }

    /**
     * A customer asset mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.CustomerAssetOperation customer_asset_operation = 57;</code>
     * @return \Google\Ads\GoogleAds\V15\Services\CustomerAssetOperation|null
     */
    public function getCustomerAssetOperation()
    {
        return $this->readOneof(57);
    }

    public function hasCustomerAssetOperation()
    {
        return $this->hasOneof(57);
    }

    /**
     * A customer asset mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.CustomerAssetOperation customer_asset_operation = 57;</code>
     * @param \Google\Ads\GoogleAds\V15\Services\CustomerAssetOperation $var
     * @return $this
     */
    public function setCustomerAssetOperation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Services\CustomerAssetOperation::class);
        $this->writeOneof(57, $var);

        return $this;
    }

    /**
     * A customer conversion goal mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.CustomerConversionGoalOperation customer_conversion_goal_operation = 66;</code>
     * @return \Google\Ads\GoogleAds\V15\Services\CustomerConversionGoalOperation|null
     */
    public function getCustomerConversionGoalOperation()
    {
        return $this->readOneof(66);
    }

    public function hasCustomerConversionGoalOperation()
    {
        return $this->hasOneof(66);
    }

    /**
     * A customer conversion goal mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.CustomerConversionGoalOperation customer_conversion_goal_operation = 66;</code>
     * @param \Google\Ads\GoogleAds\V15\Services\CustomerConversionGoalOperation $var
     * @return $this
     */
    public function setCustomerConversionGoalOperation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Services\CustomerConversionGoalOperation::class);
        $this->writeOneof(66, $var);

        return $this;
    }

    /**
     * A customer customizer mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.CustomerCustomizerOperation customer_customizer_operation = 79;</code>
     * @return \Google\Ads\GoogleAds\V15\Services\CustomerCustomizerOperation|null
     */
    public function getCustomerCustomizerOperation()
    {
        return $this->readOneof(79);
    }

    public function hasCustomerCustomizerOperation()
    {
        return $this->hasOneof(79);
    }

    /**
     * A customer customizer mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.CustomerCustomizerOperation customer_customizer_operation = 79;</code>
     * @param \Google\Ads\GoogleAds\V15\Services\CustomerCustomizerOperation $var
     * @return $this
     */
    public function setCustomerCustomizerOperation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Services\CustomerCustomizerOperation::class);
        $this->writeOneof(79, $var);

        return $this;
    }

    /**
     * A customer extension setting mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.CustomerExtensionSettingOperation customer_extension_setting_operation = 30;</code>
     * @return \Google\Ads\GoogleAds\V15\Services\CustomerExtensionSettingOperation|null
     */
    public function getCustomerExtensionSettingOperation()
    {
        return $this->readOneof(30);
    }

    public function hasCustomerExtensionSettingOperation()
    {
        return $this->hasOneof(30);
    }

    /**
     * A customer extension setting mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.CustomerExtensionSettingOperation customer_extension_setting_operation = 30;</code>
     * @param \Google\Ads\GoogleAds\V15\Services\CustomerExtensionSettingOperation $var
     * @return $this
     */
    public function setCustomerExtensionSettingOperation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Services\CustomerExtensionSettingOperation::class);
        $this->writeOneof(30, $var);

        return $this;
    }

    /**
     * A customer feed mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.CustomerFeedOperation customer_feed_operation = 31;</code>
     * @return \Google\Ads\GoogleAds\V15\Services\CustomerFeedOperation|null
     */
    public function getCustomerFeedOperation()
    {
        return $this->readOneof(31);
    }

    public function hasCustomerFeedOperation()
    {
        return $this->hasOneof(31);
    }

    /**
     * A customer feed mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.CustomerFeedOperation customer_feed_operation = 31;</code>
     * @param \Google\Ads\GoogleAds\V15\Services\CustomerFeedOperation $var
     * @return $this
     */
    public function setCustomerFeedOperation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Services\CustomerFeedOperation::class);
        $this->writeOneof(31, $var);

        return $this;
    }

    /**
     * A customer label mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.CustomerLabelOperation customer_label_operation = 32;</code>
     * @return \Google\Ads\GoogleAds\V15\Services\CustomerLabelOperation|null
     */
    public function getCustomerLabelOperation()
    {
        return $this->readOneof(32);
    }

    public function hasCustomerLabelOperation()
    {
        return $this->hasOneof(32);
    }

    /**
     * A customer label mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.CustomerLabelOperation customer_label_operation = 32;</code>
     * @param \Google\Ads\GoogleAds\V15\Services\CustomerLabelOperation $var
     * @return $this
     */
    public function setCustomerLabelOperation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Services\CustomerLabelOperation::class);
        $this->writeOneof(32, $var);

        return $this;
    }

    /**
     * A customer negative criterion mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.CustomerNegativeCriterionOperation customer_negative_criterion_operation = 34;</code>
     * @return \Google\Ads\GoogleAds\V15\Services\CustomerNegativeCriterionOperation|null
     */
    public function getCustomerNegativeCriterionOperation()
    {
        return $this->readOneof(34);
    }

    public function hasCustomerNegativeCriterionOperation()
    {
        return $this->hasOneof(34);
    }

    /**
     * A customer negative criterion mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.CustomerNegativeCriterionOperation customer_negative_criterion_operation = 34;</code>
     * @param \Google\Ads\GoogleAds\V15\Services\CustomerNegativeCriterionOperation $var
     * @return $this
     */
    public function setCustomerNegativeCriterionOperation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Services\CustomerNegativeCriterionOperation::class);
        $this->writeOneof(34, $var);

        return $this;
    }

    /**
     * A customer mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.CustomerOperation customer_operation = 35;</code>
     * @return \Google\Ads\GoogleAds\V15\Services\CustomerOperation|null
     */
    public function getCustomerOperation()
    {
        return $this->readOneof(35);
    }

    public function hasCustomerOperation()
    {
        return $this->hasOneof(35);
    }

    /**
     * A customer mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.CustomerOperation customer_operation = 35;</code>
     * @param \Google\Ads\GoogleAds\V15\Services\CustomerOperation $var
     * @return $this
     */
    public function setCustomerOperation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Services\CustomerOperation::class);
        $this->writeOneof(35, $var);

        return $this;
    }

    /**
     * A customizer attribute mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.CustomizerAttributeOperation customizer_attribute_operation = 70;</code>
     * @return \Google\Ads\GoogleAds\V15\Services\CustomizerAttributeOperation|null
     */
    public function getCustomizerAttributeOperation()
    {
        return $this->readOneof(70);
    }

    public function hasCustomizerAttributeOperation()
    {
        return $this->hasOneof(70);
    }

    /**
     * A customizer attribute mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.CustomizerAttributeOperation customizer_attribute_operation = 70;</code>
     * @param \Google\Ads\GoogleAds\V15\Services\CustomizerAttributeOperation $var
     * @return $this
     */
    public function setCustomizerAttributeOperation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Services\CustomizerAttributeOperation::class);
        $this->writeOneof(70, $var);

        return $this;
    }

    /**
     * An experiment mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.ExperimentOperation experiment_operation = 82;</code>
     * @return \Google\Ads\GoogleAds\V15\Services\ExperimentOperation|null
     */
    public function getExperimentOperation()
    {
        return $this->readOneof(82);
    }

    public function hasExperimentOperation()
    {
        return $this->hasOneof(82);
    }

    /**
     * An experiment mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.ExperimentOperation experiment_operation = 82;</code>
     * @param \Google\Ads\GoogleAds\V15\Services\ExperimentOperation $var
     * @return $this
     */
    public function setExperimentOperation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Services\ExperimentOperation::class);
        $this->writeOneof(82, $var);

        return $this;
    }

    /**
     * An experiment arm mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.ExperimentArmOperation experiment_arm_operation = 83;</code>
     * @return \Google\Ads\GoogleAds\V15\Services\ExperimentArmOperation|null
     */
    public function getExperimentArmOperation()
    {
        return $this->readOneof(83);
    }

    public function hasExperimentArmOperation()
    {
        return $this->hasOneof(83);
    }

    /**
     * An experiment arm mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.ExperimentArmOperation experiment_arm_operation = 83;</code>
     * @param \Google\Ads\GoogleAds\V15\Services\ExperimentArmOperation $var
     * @return $this
     */
    public function setExperimentArmOperation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Services\ExperimentArmOperation::class);
        $this->writeOneof(83, $var);

        return $this;
    }

    /**
     * An extension feed item mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.ExtensionFeedItemOperation extension_feed_item_operation = 36;</code>
     * @return \Google\Ads\GoogleAds\V15\Services\ExtensionFeedItemOperation|null
     */
    public function getExtensionFeedItemOperation()
    {
        return $this->readOneof(36);
    }

    public function hasExtensionFeedItemOperation()
    {
        return $this->hasOneof(36);
    }

    /**
     * An extension feed item mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.ExtensionFeedItemOperation extension_feed_item_operation = 36;</code>
     * @param \Google\Ads\GoogleAds\V15\Services\ExtensionFeedItemOperation $var
     * @return $this
     */
    public function setExtensionFeedItemOperation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Services\ExtensionFeedItemOperation::class);
        $this->writeOneof(36, $var);

        return $this;
    }

    /**
     * A feed item mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.FeedItemOperation feed_item_operation = 37;</code>
     * @return \Google\Ads\GoogleAds\V15\Services\FeedItemOperation|null
     */
    public function getFeedItemOperation()
    {
        return $this->readOneof(37);
    }

    public function hasFeedItemOperation()
    {
        return $this->hasOneof(37);
    }

    /**
     * A feed item mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.FeedItemOperation feed_item_operation = 37;</code>
     * @param \Google\Ads\GoogleAds\V15\Services\FeedItemOperation $var
     * @return $this
     */
    public function setFeedItemOperation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Services\FeedItemOperation::class);
        $this->writeOneof(37, $var);

        return $this;
    }

    /**
     * A feed item set mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.FeedItemSetOperation feed_item_set_operation = 53;</code>
     * @return \Google\Ads\GoogleAds\V15\Services\FeedItemSetOperation|null
     */
    public function getFeedItemSetOperation()
    {
        return $this->readOneof(53);
    }

    public function hasFeedItemSetOperation()
    {
        return $this->hasOneof(53);
    }

    /**
     * A feed item set mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.FeedItemSetOperation feed_item_set_operation = 53;</code>
     * @param \Google\Ads\GoogleAds\V15\Services\FeedItemSetOperation $var
     * @return $this
     */
    public function setFeedItemSetOperation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Services\FeedItemSetOperation::class);
        $this->writeOneof(53, $var);

        return $this;
    }

    /**
     * A feed item set link mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.FeedItemSetLinkOperation feed_item_set_link_operation = 54;</code>
     * @return \Google\Ads\GoogleAds\V15\Services\FeedItemSetLinkOperation|null
     */
    public function getFeedItemSetLinkOperation()
    {
        return $this->readOneof(54);
    }

    public function hasFeedItemSetLinkOperation()
    {
        return $this->hasOneof(54);
    }

    /**
     * A feed item set link mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.FeedItemSetLinkOperation feed_item_set_link_operation = 54;</code>
     * @param \Google\Ads\GoogleAds\V15\Services\FeedItemSetLinkOperation $var
     * @return $this
     */
    public function setFeedItemSetLinkOperation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Services\FeedItemSetLinkOperation::class);
        $this->writeOneof(54, $var);

        return $this;
    }

    /**
     * A feed item target mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.FeedItemTargetOperation feed_item_target_operation = 38;</code>
     * @return \Google\Ads\GoogleAds\V15\Services\FeedItemTargetOperation|null
     */
    public function getFeedItemTargetOperation()
    {
        return $this->readOneof(38);
    }

    public function hasFeedItemTargetOperation()
    {
        return $this->hasOneof(38);
    }

    /**
     * A feed item target mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.FeedItemTargetOperation feed_item_target_operation = 38;</code>
     * @param \Google\Ads\GoogleAds\V15\Services\FeedItemTargetOperation $var
     * @return $this
     */
    public function setFeedItemTargetOperation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Services\FeedItemTargetOperation::class);
        $this->writeOneof(38, $var);

        return $this;
    }

    /**
     * A feed mapping mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.FeedMappingOperation feed_mapping_operation = 39;</code>
     * @return \Google\Ads\GoogleAds\V15\Services\FeedMappingOperation|null
     */
    public function getFeedMappingOperation()
    {
        return $this->readOneof(39);
    }

    public function hasFeedMappingOperation()
    {
        return $this->hasOneof(39);
    }

    /**
     * A feed mapping mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.FeedMappingOperation feed_mapping_operation = 39;</code>
     * @param \Google\Ads\GoogleAds\V15\Services\FeedMappingOperation $var
     * @return $this
     */
    public function setFeedMappingOperation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Services\FeedMappingOperation::class);
        $this->writeOneof(39, $var);

        return $this;
    }

    /**
     * A feed mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.FeedOperation feed_operation = 40;</code>
     * @return \Google\Ads\GoogleAds\V15\Services\FeedOperation|null
     */
    public function getFeedOperation()
    {
        return $this->readOneof(40);
    }

    public function hasFeedOperation()
    {
        return $this->hasOneof(40);
    }

    /**
     * A feed mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.FeedOperation feed_operation = 40;</code>
     * @param \Google\Ads\GoogleAds\V15\Services\FeedOperation $var
     * @return $this
     */
    public function setFeedOperation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Services\FeedOperation::class);
        $this->writeOneof(40, $var);

        return $this;
    }

    /**
     * A keyword plan ad group operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.KeywordPlanAdGroupOperation keyword_plan_ad_group_operation = 44;</code>
     * @return \Google\Ads\GoogleAds\V15\Services\KeywordPlanAdGroupOperation|null
     */
    public function getKeywordPlanAdGroupOperation()
    {
        return $this->readOneof(44);
    }

    public function hasKeywordPlanAdGroupOperation()
    {
        return $this->hasOneof(44);
    }

    /**
     * A keyword plan ad group operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.KeywordPlanAdGroupOperation keyword_plan_ad_group_operation = 44;</code>
     * @param \Google\Ads\GoogleAds\V15\Services\KeywordPlanAdGroupOperation $var
     * @return $this
     */
    public function setKeywordPlanAdGroupOperation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Services\KeywordPlanAdGroupOperation::class);
        $this->writeOneof(44, $var);

        return $this;
    }

    /**
     * A keyword plan ad group keyword operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.KeywordPlanAdGroupKeywordOperation keyword_plan_ad_group_keyword_operation = 50;</code>
     * @return \Google\Ads\GoogleAds\V15\Services\KeywordPlanAdGroupKeywordOperation|null
     */
    public function getKeywordPlanAdGroupKeywordOperation()
    {
        return $this->readOneof(50);
    }

    public function hasKeywordPlanAdGroupKeywordOperation()
    {
        return $this->hasOneof(50);
    }

    /**
     * A keyword plan ad group keyword operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.KeywordPlanAdGroupKeywordOperation keyword_plan_ad_group_keyword_operation = 50;</code>
     * @param \Google\Ads\GoogleAds\V15\Services\KeywordPlanAdGroupKeywordOperation $var
     * @return $this
     */
    public function setKeywordPlanAdGroupKeywordOperation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Services\KeywordPlanAdGroupKeywordOperation::class);
        $this->writeOneof(50, $var);

        return $this;
    }

    /**
     * A keyword plan campaign keyword operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.KeywordPlanCampaignKeywordOperation keyword_plan_campaign_keyword_operation = 51;</code>
     * @return \Google\Ads\GoogleAds\V15\Services\KeywordPlanCampaignKeywordOperation|null
     */
    public function getKeywordPlanCampaignKeywordOperation()
    {
        return $this->readOneof(51);
    }

    public function hasKeywordPlanCampaignKeywordOperation()
    {
        return $this->hasOneof(51);
    }

    /**
     * A keyword plan campaign keyword operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.KeywordPlanCampaignKeywordOperation keyword_plan_campaign_keyword_operation = 51;</code>
     * @param \Google\Ads\GoogleAds\V15\Services\KeywordPlanCampaignKeywordOperation $var
     * @return $this
     */
    public function setKeywordPlanCampaignKeywordOperation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Services\KeywordPlanCampaignKeywordOperation::class);
        $this->writeOneof(51, $var);

        return $this;
    }

    /**
     * A keyword plan campaign operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.KeywordPlanCampaignOperation keyword_plan_campaign_operation = 45;</code>
     * @return \Google\Ads\GoogleAds\V15\Services\KeywordPlanCampaignOperation|null
     */
    public function getKeywordPlanCampaignOperation()
    {
        return $this->readOneof(45);
    }

    public function hasKeywordPlanCampaignOperation()
    {
        return $this->hasOneof(45);
    }

    /**
     * A keyword plan campaign operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.KeywordPlanCampaignOperation keyword_plan_campaign_operation = 45;</code>
     * @param \Google\Ads\GoogleAds\V15\Services\KeywordPlanCampaignOperation $var
     * @return $this
     */
    public function setKeywordPlanCampaignOperation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Services\KeywordPlanCampaignOperation::class);
        $this->writeOneof(45, $var);

        return $this;
    }

    /**
     * A keyword plan operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.KeywordPlanOperation keyword_plan_operation = 48;</code>
     * @return \Google\Ads\GoogleAds\V15\Services\KeywordPlanOperation|null
     */
    public function getKeywordPlanOperation()
    {
        return $this->readOneof(48);
    }

    public function hasKeywordPlanOperation()
    {
        return $this->hasOneof(48);
    }

    /**
     * A keyword plan operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.KeywordPlanOperation keyword_plan_operation = 48;</code>
     * @param \Google\Ads\GoogleAds\V15\Services\KeywordPlanOperation $var
     * @return $this
     */
    public function setKeywordPlanOperation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Services\KeywordPlanOperation::class);
        $this->writeOneof(48, $var);

        return $this;
    }

    /**
     * A label mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.LabelOperation label_operation = 41;</code>
     * @return \Google\Ads\GoogleAds\V15\Services\LabelOperation|null
     */
    public function getLabelOperation()
    {
        return $this->readOneof(41);
    }

    public function hasLabelOperation()
    {
        return $this->hasOneof(41);
    }

    /**
     * A label mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.LabelOperation label_operation = 41;</code>
     * @param \Google\Ads\GoogleAds\V15\Services\LabelOperation $var
     * @return $this
     */
    public function setLabelOperation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Services\LabelOperation::class);
        $this->writeOneof(41, $var);

        return $this;
    }

    /**
     * A recommendation subscription mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.RecommendationSubscriptionOperation recommendation_subscription_operation = 86;</code>
     * @return \Google\Ads\GoogleAds\V15\Services\RecommendationSubscriptionOperation|null
     */
    public function getRecommendationSubscriptionOperation()
    {
        return $this->readOneof(86);
    }

    public function hasRecommendationSubscriptionOperation()
    {
        return $this->hasOneof(86);
    }

    /**
     * A recommendation subscription mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.RecommendationSubscriptionOperation recommendation_subscription_operation = 86;</code>
     * @param \Google\Ads\GoogleAds\V15\Services\RecommendationSubscriptionOperation $var
     * @return $this
     */
    public function setRecommendationSubscriptionOperation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Services\RecommendationSubscriptionOperation::class);
        $this->writeOneof(86, $var);

        return $this;
    }

    /**
     * A remarketing action mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.RemarketingActionOperation remarketing_action_operation = 43;</code>
     * @return \Google\Ads\GoogleAds\V15\Services\RemarketingActionOperation|null
     */
    public function getRemarketingActionOperation()
    {
        return $this->readOneof(43);
    }

    public function hasRemarketingActionOperation()
    {
        return $this->hasOneof(43);
    }

    /**
     * A remarketing action mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.RemarketingActionOperation remarketing_action_operation = 43;</code>
     * @param \Google\Ads\GoogleAds\V15\Services\RemarketingActionOperation $var
     * @return $this
     */
    public function setRemarketingActionOperation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Services\RemarketingActionOperation::class);
        $this->writeOneof(43, $var);

        return $this;
    }

    /**
     * A shared criterion mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.SharedCriterionOperation shared_criterion_operation = 14;</code>
     * @return \Google\Ads\GoogleAds\V15\Services\SharedCriterionOperation|null
     */
    public function getSharedCriterionOperation()
    {
        return $this->readOneof(14);
    }

    public function hasSharedCriterionOperation()
    {
        return $this->hasOneof(14);
    }

    /**
     * A shared criterion mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.SharedCriterionOperation shared_criterion_operation = 14;</code>
     * @param \Google\Ads\GoogleAds\V15\Services\SharedCriterionOperation $var
     * @return $this
     */
    public function setSharedCriterionOperation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Services\SharedCriterionOperation::class);
        $this->writeOneof(14, $var);

        return $this;
    }

    /**
     * A shared set mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.SharedSetOperation shared_set_operation = 15;</code>
     * @return \Google\Ads\GoogleAds\V15\Services\SharedSetOperation|null
     */
    public function getSharedSetOperation()
    {
        return $this->readOneof(15);
    }

    public function hasSharedSetOperation()
    {
        return $this->hasOneof(15);
    }

    /**
     * A shared set mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.SharedSetOperation shared_set_operation = 15;</code>
     * @param \Google\Ads\GoogleAds\V15\Services\SharedSetOperation $var
     * @return $this
     */
    public function setSharedSetOperation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Services\SharedSetOperation::class);
        $this->writeOneof(15, $var);

        return $this;
    }

    /**
     * A Smart campaign setting mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.SmartCampaignSettingOperation smart_campaign_setting_operation = 61;</code>
     * @return \Google\Ads\GoogleAds\V15\Services\SmartCampaignSettingOperation|null
     */
    public function getSmartCampaignSettingOperation()
    {
        return $this->readOneof(61);
    }

    public function hasSmartCampaignSettingOperation()
    {
        return $this->hasOneof(61);
    }

    /**
     * A Smart campaign setting mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.SmartCampaignSettingOperation smart_campaign_setting_operation = 61;</code>
     * @param \Google\Ads\GoogleAds\V15\Services\SmartCampaignSettingOperation $var
     * @return $this
     */
    public function setSmartCampaignSettingOperation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Services\SmartCampaignSettingOperation::class);
        $this->writeOneof(61, $var);

        return $this;
    }

    /**
     * A user list mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.UserListOperation user_list_operation = 16;</code>
     * @return \Google\Ads\GoogleAds\V15\Services\UserListOperation|null
     */
    public function getUserListOperation()
    {
        return $this->readOneof(16);
    }

    public function hasUserListOperation()
    {
        return $this->hasOneof(16);
    }

    /**
     * A user list mutate operation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.services.UserListOperation user_list_operation = 16;</code>
     * @param \Google\Ads\GoogleAds\V15\Services\UserListOperation $var
     * @return $this
     */
    public function setUserListOperation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Services\UserListOperation::class);
        $this->writeOneof(16, $var);

        return $this;
    }

    /**
     * @return string
     */
    public function getOperation()
    {
        return $this->whichOneof("operation");
    }

}

