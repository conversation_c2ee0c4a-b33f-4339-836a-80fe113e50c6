<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v15/enums/lead_form_field_user_input_type.proto

namespace Google\Ads\GoogleAds\V15\Enums\LeadFormFieldUserInputTypeEnum;

use UnexpectedValueException;

/**
 * Enum describing the input type of a lead form field.
 *
 * Protobuf type <code>google.ads.googleads.v15.enums.LeadFormFieldUserInputTypeEnum.LeadFormFieldUserInputType</code>
 */
class LeadFormFieldUserInputType
{
    /**
     * Not specified.
     *
     * Generated from protobuf enum <code>UNSPECIFIED = 0;</code>
     */
    const UNSPECIFIED = 0;
    /**
     * Used for return value only. Represents value unknown in this version.
     *
     * Generated from protobuf enum <code>UNKNOWN = 1;</code>
     */
    const UNKNOWN = 1;
    /**
     * The user will be asked to fill in their given and family name. This field
     * cannot be set at the same time as GIVE<PERSON>_NAME or FAMILY_NAME.
     *
     * Generated from protobuf enum <code>FULL_NAME = 2;</code>
     */
    const FULL_NAME = 2;
    /**
     * The user will be asked to fill in their email address.
     *
     * Generated from protobuf enum <code>EMAIL = 3;</code>
     */
    const EMAIL = 3;
    /**
     * The user will be asked to fill in their phone number.
     *
     * Generated from protobuf enum <code>PHONE_NUMBER = 4;</code>
     */
    const PHONE_NUMBER = 4;
    /**
     * The user will be asked to fill in their zip code.
     *
     * Generated from protobuf enum <code>POSTAL_CODE = 5;</code>
     */
    const POSTAL_CODE = 5;
    /**
     * The user will be asked to fill in their street address.
     *
     * Generated from protobuf enum <code>STREET_ADDRESS = 8;</code>
     */
    const STREET_ADDRESS = 8;
    /**
     * The user will be asked to fill in their city.
     *
     * Generated from protobuf enum <code>CITY = 9;</code>
     */
    const CITY = 9;
    /**
     * The user will be asked to fill in their region part of the address (for
     * example, state for US, province for Canada).
     *
     * Generated from protobuf enum <code>REGION = 10;</code>
     */
    const REGION = 10;
    /**
     * The user will be asked to fill in their country.
     *
     * Generated from protobuf enum <code>COUNTRY = 11;</code>
     */
    const COUNTRY = 11;
    /**
     * The user will be asked to fill in their work email address.
     *
     * Generated from protobuf enum <code>WORK_EMAIL = 12;</code>
     */
    const WORK_EMAIL = 12;
    /**
     * The user will be asked to fill in their company name.
     *
     * Generated from protobuf enum <code>COMPANY_NAME = 13;</code>
     */
    const COMPANY_NAME = 13;
    /**
     * The user will be asked to fill in their work phone.
     *
     * Generated from protobuf enum <code>WORK_PHONE = 14;</code>
     */
    const WORK_PHONE = 14;
    /**
     * The user will be asked to fill in their job title.
     *
     * Generated from protobuf enum <code>JOB_TITLE = 15;</code>
     */
    const JOB_TITLE = 15;
    /**
     * The user will be asked to fill in their CPF for Brazil users.
     *
     * Generated from protobuf enum <code>GOVERNMENT_ISSUED_ID_CPF_BR = 16;</code>
     */
    const GOVERNMENT_ISSUED_ID_CPF_BR = 16;
    /**
     * The user will be asked to fill in their DNI for Argentina users.
     *
     * Generated from protobuf enum <code>GOVERNMENT_ISSUED_ID_DNI_AR = 17;</code>
     */
    const GOVERNMENT_ISSUED_ID_DNI_AR = 17;
    /**
     * The user will be asked to fill in their DNI for Peru users.
     *
     * Generated from protobuf enum <code>GOVERNMENT_ISSUED_ID_DNI_PE = 18;</code>
     */
    const GOVERNMENT_ISSUED_ID_DNI_PE = 18;
    /**
     * The user will be asked to fill in their RUT for Chile users.
     *
     * Generated from protobuf enum <code>GOVERNMENT_ISSUED_ID_RUT_CL = 19;</code>
     */
    const GOVERNMENT_ISSUED_ID_RUT_CL = 19;
    /**
     * The user will be asked to fill in their CC for Colombia users.
     *
     * Generated from protobuf enum <code>GOVERNMENT_ISSUED_ID_CC_CO = 20;</code>
     */
    const GOVERNMENT_ISSUED_ID_CC_CO = 20;
    /**
     * The user will be asked to fill in their CI for Ecuador users.
     *
     * Generated from protobuf enum <code>GOVERNMENT_ISSUED_ID_CI_EC = 21;</code>
     */
    const GOVERNMENT_ISSUED_ID_CI_EC = 21;
    /**
     * The user will be asked to fill in their RFC for Mexico users.
     *
     * Generated from protobuf enum <code>GOVERNMENT_ISSUED_ID_RFC_MX = 22;</code>
     */
    const GOVERNMENT_ISSUED_ID_RFC_MX = 22;
    /**
     * The user will be asked to fill in their first name. This
     * field can not be set at the same time as FULL_NAME.
     *
     * Generated from protobuf enum <code>FIRST_NAME = 23;</code>
     */
    const FIRST_NAME = 23;
    /**
     * The user will be asked to fill in their last name. This
     * field can not be set at the same time as FULL_NAME.
     *
     * Generated from protobuf enum <code>LAST_NAME = 24;</code>
     */
    const LAST_NAME = 24;
    /**
     * Question: "Which model are you interested in?"
     * Category: "Auto"
     * This field is subject to a limit of 5 qualifying questions per form and
     * cannot be used if values are set using custom_question_fields.
     *
     * Generated from protobuf enum <code>VEHICLE_MODEL = 1001;</code>
     */
    const VEHICLE_MODEL = 1001;
    /**
     * Question: "Which type of vehicle are you interested in?"
     * Category: "Auto"
     * This field is subject to a limit of 5 qualifying questions per form and
     * cannot be used if values are set using custom_question_fields.
     *
     * Generated from protobuf enum <code>VEHICLE_TYPE = 1002;</code>
     */
    const VEHICLE_TYPE = 1002;
    /**
     * Question: "What is your preferred dealership?"
     * Category: "Auto"
     * This field is subject to a limit of 5 qualifying questions per form and
     * cannot be used if values are set using custom_question_fields.
     *
     * Generated from protobuf enum <code>PREFERRED_DEALERSHIP = 1003;</code>
     */
    const PREFERRED_DEALERSHIP = 1003;
    /**
     * Question: "When do you plan on purchasing a vehicle?"
     * Category: "Auto"
     * This field is subject to a limit of 5 qualifying questions per form and
     * cannot be used if values are set using custom_question_fields.
     *
     * Generated from protobuf enum <code>VEHICLE_PURCHASE_TIMELINE = 1004;</code>
     */
    const VEHICLE_PURCHASE_TIMELINE = 1004;
    /**
     * Question: "Do you own a vehicle?"
     * Category: "Auto"
     * This field is subject to a limit of 5 qualifying questions per form and
     * cannot be used if values are set using custom_question_fields.
     *
     * Generated from protobuf enum <code>VEHICLE_OWNERSHIP = 1005;</code>
     */
    const VEHICLE_OWNERSHIP = 1005;
    /**
     * Question: "What vehicle ownership option are you interested in?"
     * Category: "Auto"
     * This field is subject to a limit of 5 qualifying questions per form and
     * cannot be used if values are set using custom_question_fields.
     *
     * Generated from protobuf enum <code>VEHICLE_PAYMENT_TYPE = 1009;</code>
     */
    const VEHICLE_PAYMENT_TYPE = 1009;
    /**
     * Question: "What type of vehicle condition are you interested in?"
     * Category: "Auto"
     * This field is subject to a limit of 5 qualifying questions per form and
     * cannot be used if values are set using custom_question_fields.
     *
     * Generated from protobuf enum <code>VEHICLE_CONDITION = 1010;</code>
     */
    const VEHICLE_CONDITION = 1010;
    /**
     * Question: "What size is your company?"
     * Category: "Business"
     * This field is subject to a limit of 5 qualifying questions per form and
     * cannot be used if values are set using custom_question_fields.
     *
     * Generated from protobuf enum <code>COMPANY_SIZE = 1006;</code>
     */
    const COMPANY_SIZE = 1006;
    /**
     * Question: "What is your annual sales volume?"
     * Category: "Business"
     * This field is subject to a limit of 5 qualifying questions per form and
     * cannot be used if values are set using custom_question_fields.
     *
     * Generated from protobuf enum <code>ANNUAL_SALES = 1007;</code>
     */
    const ANNUAL_SALES = 1007;
    /**
     * Question: "How many years have you been in business?"
     * Category: "Business"
     * This field is subject to a limit of 5 qualifying questions per form and
     * cannot be used if values are set using custom_question_fields.
     *
     * Generated from protobuf enum <code>YEARS_IN_BUSINESS = 1008;</code>
     */
    const YEARS_IN_BUSINESS = 1008;
    /**
     * Question: "What is your job department?"
     * Category: "Business"
     * This field is subject to a limit of 5 qualifying questions per form and
     * cannot be used if values are set using custom_question_fields.
     *
     * Generated from protobuf enum <code>JOB_DEPARTMENT = 1011;</code>
     */
    const JOB_DEPARTMENT = 1011;
    /**
     * Question: "What is your job role?"
     * Category: "Business"
     * This field is subject to a limit of 5 qualifying questions per form and
     * cannot be used if values are set using custom_question_fields.
     *
     * Generated from protobuf enum <code>JOB_ROLE = 1012;</code>
     */
    const JOB_ROLE = 1012;
    /**
     * Question: "Are you over 18 years of age?"
     * Category: "Demographics"
     * This field is subject to a limit of 5 qualifying questions per form and
     * cannot be used if values are set using custom_question_fields.
     *
     * Generated from protobuf enum <code>OVER_18_AGE = 1078;</code>
     */
    const OVER_18_AGE = 1078;
    /**
     * Question: "Are you over 19 years of age?"
     * Category: "Demographics"
     * This field is subject to a limit of 5 qualifying questions per form and
     * cannot be used if values are set using custom_question_fields.
     *
     * Generated from protobuf enum <code>OVER_19_AGE = 1079;</code>
     */
    const OVER_19_AGE = 1079;
    /**
     * Question: "Are you over 20 years of age?"
     * Category: "Demographics"
     * This field is subject to a limit of 5 qualifying questions per form and
     * cannot be used if values are set using custom_question_fields.
     *
     * Generated from protobuf enum <code>OVER_20_AGE = 1080;</code>
     */
    const OVER_20_AGE = 1080;
    /**
     * Question: "Are you over 21 years of age?"
     * Category: "Demographics"
     * This field is subject to a limit of 5 qualifying questions per form and
     * cannot be used if values are set using custom_question_fields.
     *
     * Generated from protobuf enum <code>OVER_21_AGE = 1081;</code>
     */
    const OVER_21_AGE = 1081;
    /**
     * Question: "Are you over 22 years of age?"
     * Category: "Demographics"
     * This field is subject to a limit of 5 qualifying questions per form and
     * cannot be used if values are set using custom_question_fields.
     *
     * Generated from protobuf enum <code>OVER_22_AGE = 1082;</code>
     */
    const OVER_22_AGE = 1082;
    /**
     * Question: "Are you over 23 years of age?"
     * Category: "Demographics"
     * This field is subject to a limit of 5 qualifying questions per form and
     * cannot be used if values are set using custom_question_fields.
     *
     * Generated from protobuf enum <code>OVER_23_AGE = 1083;</code>
     */
    const OVER_23_AGE = 1083;
    /**
     * Question: "Are you over 24 years of age?"
     * Category: "Demographics"
     * This field is subject to a limit of 5 qualifying questions per form and
     * cannot be used if values are set using custom_question_fields.
     *
     * Generated from protobuf enum <code>OVER_24_AGE = 1084;</code>
     */
    const OVER_24_AGE = 1084;
    /**
     * Question: "Are you over 25 years of age?"
     * Category: "Demographics"
     * This field is subject to a limit of 5 qualifying questions per form and
     * cannot be used if values are set using custom_question_fields.
     *
     * Generated from protobuf enum <code>OVER_25_AGE = 1085;</code>
     */
    const OVER_25_AGE = 1085;
    /**
     * Question: "Are you over 26 years of age?"
     * Category: "Demographics"
     * This field is subject to a limit of 5 qualifying questions per form and
     * cannot be used if values are set using custom_question_fields.
     *
     * Generated from protobuf enum <code>OVER_26_AGE = 1086;</code>
     */
    const OVER_26_AGE = 1086;
    /**
     * Question: "Are you over 27 years of age?"
     * Category: "Demographics"
     * This field is subject to a limit of 5 qualifying questions per form and
     * cannot be used if values are set using custom_question_fields.
     *
     * Generated from protobuf enum <code>OVER_27_AGE = 1087;</code>
     */
    const OVER_27_AGE = 1087;
    /**
     * Question: "Are you over 28 years of age?"
     * Category: "Demographics"
     * This field is subject to a limit of 5 qualifying questions per form and
     * cannot be used if values are set using custom_question_fields.
     *
     * Generated from protobuf enum <code>OVER_28_AGE = 1088;</code>
     */
    const OVER_28_AGE = 1088;
    /**
     * Question: "Are you over 29 years of age?"
     * Category: "Demographics"
     * This field is subject to a limit of 5 qualifying questions per form and
     * cannot be used if values are set using custom_question_fields.
     *
     * Generated from protobuf enum <code>OVER_29_AGE = 1089;</code>
     */
    const OVER_29_AGE = 1089;
    /**
     * Question: "Are you over 30 years of age?"
     * Category: "Demographics"
     * This field is subject to a limit of 5 qualifying questions per form and
     * cannot be used if values are set using custom_question_fields.
     *
     * Generated from protobuf enum <code>OVER_30_AGE = 1090;</code>
     */
    const OVER_30_AGE = 1090;
    /**
     * Question: "Are you over 31 years of age?"
     * Category: "Demographics"
     * This field is subject to a limit of 5 qualifying questions per form and
     * cannot be used if values are set using custom_question_fields.
     *
     * Generated from protobuf enum <code>OVER_31_AGE = 1091;</code>
     */
    const OVER_31_AGE = 1091;
    /**
     * Question: "Are you over 32 years of age?"
     * Category: "Demographics"
     * This field is subject to a limit of 5 qualifying questions per form and
     * cannot be used if values are set using custom_question_fields.
     *
     * Generated from protobuf enum <code>OVER_32_AGE = 1092;</code>
     */
    const OVER_32_AGE = 1092;
    /**
     * Question: "Are you over 33 years of age?"
     * Category: "Demographics"
     * This field is subject to a limit of 5 qualifying questions per form and
     * cannot be used if values are set using custom_question_fields.
     *
     * Generated from protobuf enum <code>OVER_33_AGE = 1093;</code>
     */
    const OVER_33_AGE = 1093;
    /**
     * Question: "Are you over 34 years of age?"
     * Category: "Demographics"
     * This field is subject to a limit of 5 qualifying questions per form and
     * cannot be used if values are set using custom_question_fields.
     *
     * Generated from protobuf enum <code>OVER_34_AGE = 1094;</code>
     */
    const OVER_34_AGE = 1094;
    /**
     * Question: "Are you over 35 years of age?"
     * Category: "Demographics"
     * This field is subject to a limit of 5 qualifying questions per form and
     * cannot be used if values are set using custom_question_fields.
     *
     * Generated from protobuf enum <code>OVER_35_AGE = 1095;</code>
     */
    const OVER_35_AGE = 1095;
    /**
     * Question: "Are you over 36 years of age?"
     * Category: "Demographics"
     * This field is subject to a limit of 5 qualifying questions per form and
     * cannot be used if values are set using custom_question_fields.
     *
     * Generated from protobuf enum <code>OVER_36_AGE = 1096;</code>
     */
    const OVER_36_AGE = 1096;
    /**
     * Question: "Are you over 37 years of age?"
     * Category: "Demographics"
     * This field is subject to a limit of 5 qualifying questions per form and
     * cannot be used if values are set using custom_question_fields.
     *
     * Generated from protobuf enum <code>OVER_37_AGE = 1097;</code>
     */
    const OVER_37_AGE = 1097;
    /**
     * Question: "Are you over 38 years of age?"
     * Category: "Demographics"
     * This field is subject to a limit of 5 qualifying questions per form and
     * cannot be used if values are set using custom_question_fields.
     *
     * Generated from protobuf enum <code>OVER_38_AGE = 1098;</code>
     */
    const OVER_38_AGE = 1098;
    /**
     * Question: "Are you over 39 years of age?"
     * Category: "Demographics"
     * This field is subject to a limit of 5 qualifying questions per form and
     * cannot be used if values are set using custom_question_fields.
     *
     * Generated from protobuf enum <code>OVER_39_AGE = 1099;</code>
     */
    const OVER_39_AGE = 1099;
    /**
     * Question: "Are you over 40 years of age?"
     * Category: "Demographics"
     * This field is subject to a limit of 5 qualifying questions per form and
     * cannot be used if values are set using custom_question_fields.
     *
     * Generated from protobuf enum <code>OVER_40_AGE = 1100;</code>
     */
    const OVER_40_AGE = 1100;
    /**
     * Question: "Are you over 41 years of age?"
     * Category: "Demographics"
     * This field is subject to a limit of 5 qualifying questions per form and
     * cannot be used if values are set using custom_question_fields.
     *
     * Generated from protobuf enum <code>OVER_41_AGE = 1101;</code>
     */
    const OVER_41_AGE = 1101;
    /**
     * Question: "Are you over 42 years of age?"
     * Category: "Demographics"
     * This field is subject to a limit of 5 qualifying questions per form and
     * cannot be used if values are set using custom_question_fields.
     *
     * Generated from protobuf enum <code>OVER_42_AGE = 1102;</code>
     */
    const OVER_42_AGE = 1102;
    /**
     * Question: "Are you over 43 years of age?"
     * Category: "Demographics"
     * This field is subject to a limit of 5 qualifying questions per form and
     * cannot be used if values are set using custom_question_fields.
     *
     * Generated from protobuf enum <code>OVER_43_AGE = 1103;</code>
     */
    const OVER_43_AGE = 1103;
    /**
     * Question: "Are you over 44 years of age?"
     * Category: "Demographics"
     * This field is subject to a limit of 5 qualifying questions per form and
     * cannot be used if values are set using custom_question_fields.
     *
     * Generated from protobuf enum <code>OVER_44_AGE = 1104;</code>
     */
    const OVER_44_AGE = 1104;
    /**
     * Question: "Are you over 45 years of age?"
     * Category: "Demographics"
     * This field is subject to a limit of 5 qualifying questions per form and
     * cannot be used if values are set using custom_question_fields.
     *
     * Generated from protobuf enum <code>OVER_45_AGE = 1105;</code>
     */
    const OVER_45_AGE = 1105;
    /**
     * Question: "Are you over 46 years of age?"
     * Category: "Demographics"
     * This field is subject to a limit of 5 qualifying questions per form and
     * cannot be used if values are set using custom_question_fields.
     *
     * Generated from protobuf enum <code>OVER_46_AGE = 1106;</code>
     */
    const OVER_46_AGE = 1106;
    /**
     * Question: "Are you over 47 years of age?"
     * Category: "Demographics"
     * This field is subject to a limit of 5 qualifying questions per form and
     * cannot be used if values are set using custom_question_fields.
     *
     * Generated from protobuf enum <code>OVER_47_AGE = 1107;</code>
     */
    const OVER_47_AGE = 1107;
    /**
     * Question: "Are you over 48 years of age?"
     * Category: "Demographics"
     * This field is subject to a limit of 5 qualifying questions per form and
     * cannot be used if values are set using custom_question_fields.
     *
     * Generated from protobuf enum <code>OVER_48_AGE = 1108;</code>
     */
    const OVER_48_AGE = 1108;
    /**
     * Question: "Are you over 49 years of age?"
     * Category: "Demographics"
     * This field is subject to a limit of 5 qualifying questions per form and
     * cannot be used if values are set using custom_question_fields.
     *
     * Generated from protobuf enum <code>OVER_49_AGE = 1109;</code>
     */
    const OVER_49_AGE = 1109;
    /**
     * Question: "Are you over 50 years of age?"
     * Category: "Demographics"
     * This field is subject to a limit of 5 qualifying questions per form and
     * cannot be used if values are set using custom_question_fields.
     *
     * Generated from protobuf enum <code>OVER_50_AGE = 1110;</code>
     */
    const OVER_50_AGE = 1110;
    /**
     * Question: "Are you over 51 years of age?"
     * Category: "Demographics"
     * This field is subject to a limit of 5 qualifying questions per form and
     * cannot be used if values are set using custom_question_fields.
     *
     * Generated from protobuf enum <code>OVER_51_AGE = 1111;</code>
     */
    const OVER_51_AGE = 1111;
    /**
     * Question: "Are you over 52 years of age?"
     * Category: "Demographics"
     * This field is subject to a limit of 5 qualifying questions per form and
     * cannot be used if values are set using custom_question_fields.
     *
     * Generated from protobuf enum <code>OVER_52_AGE = 1112;</code>
     */
    const OVER_52_AGE = 1112;
    /**
     * Question: "Are you over 53 years of age?"
     * Category: "Demographics"
     * This field is subject to a limit of 5 qualifying questions per form and
     * cannot be used if values are set using custom_question_fields.
     *
     * Generated from protobuf enum <code>OVER_53_AGE = 1113;</code>
     */
    const OVER_53_AGE = 1113;
    /**
     * Question: "Are you over 54 years of age?"
     * Category: "Demographics"
     * This field is subject to a limit of 5 qualifying questions per form and
     * cannot be used if values are set using custom_question_fields.
     *
     * Generated from protobuf enum <code>OVER_54_AGE = 1114;</code>
     */
    const OVER_54_AGE = 1114;
    /**
     * Question: "Are you over 55 years of age?"
     * Category: "Demographics"
     * This field is subject to a limit of 5 qualifying questions per form and
     * cannot be used if values are set using custom_question_fields.
     *
     * Generated from protobuf enum <code>OVER_55_AGE = 1115;</code>
     */
    const OVER_55_AGE = 1115;
    /**
     * Question: "Are you over 56 years of age?"
     * Category: "Demographics"
     * This field is subject to a limit of 5 qualifying questions per form and
     * cannot be used if values are set using custom_question_fields.
     *
     * Generated from protobuf enum <code>OVER_56_AGE = 1116;</code>
     */
    const OVER_56_AGE = 1116;
    /**
     * Question: "Are you over 57 years of age?"
     * Category: "Demographics"
     * This field is subject to a limit of 5 qualifying questions per form and
     * cannot be used if values are set using custom_question_fields.
     *
     * Generated from protobuf enum <code>OVER_57_AGE = 1117;</code>
     */
    const OVER_57_AGE = 1117;
    /**
     * Question: "Are you over 58 years of age?"
     * Category: "Demographics"
     * This field is subject to a limit of 5 qualifying questions per form and
     * cannot be used if values are set using custom_question_fields.
     *
     * Generated from protobuf enum <code>OVER_58_AGE = 1118;</code>
     */
    const OVER_58_AGE = 1118;
    /**
     * Question: "Are you over 59 years of age?"
     * Category: "Demographics"
     * This field is subject to a limit of 5 qualifying questions per form and
     * cannot be used if values are set using custom_question_fields.
     *
     * Generated from protobuf enum <code>OVER_59_AGE = 1119;</code>
     */
    const OVER_59_AGE = 1119;
    /**
     * Question: "Are you over 60 years of age?"
     * Category: "Demographics"
     * This field is subject to a limit of 5 qualifying questions per form and
     * cannot be used if values are set using custom_question_fields.
     *
     * Generated from protobuf enum <code>OVER_60_AGE = 1120;</code>
     */
    const OVER_60_AGE = 1120;
    /**
     * Question: "Are you over 61 years of age?"
     * Category: "Demographics"
     * This field is subject to a limit of 5 qualifying questions per form and
     * cannot be used if values are set using custom_question_fields.
     *
     * Generated from protobuf enum <code>OVER_61_AGE = 1121;</code>
     */
    const OVER_61_AGE = 1121;
    /**
     * Question: "Are you over 62 years of age?"
     * Category: "Demographics"
     * This field is subject to a limit of 5 qualifying questions per form and
     * cannot be used if values are set using custom_question_fields.
     *
     * Generated from protobuf enum <code>OVER_62_AGE = 1122;</code>
     */
    const OVER_62_AGE = 1122;
    /**
     * Question: "Are you over 63 years of age?"
     * Category: "Demographics"
     * This field is subject to a limit of 5 qualifying questions per form and
     * cannot be used if values are set using custom_question_fields.
     *
     * Generated from protobuf enum <code>OVER_63_AGE = 1123;</code>
     */
    const OVER_63_AGE = 1123;
    /**
     * Question: "Are you over 64 years of age?"
     * Category: "Demographics"
     * This field is subject to a limit of 5 qualifying questions per form and
     * cannot be used if values are set using custom_question_fields.
     *
     * Generated from protobuf enum <code>OVER_64_AGE = 1124;</code>
     */
    const OVER_64_AGE = 1124;
    /**
     * Question: "Are you over 65 years of age?"
     * Category: "Demographics"
     * This field is subject to a limit of 5 qualifying questions per form and
     * cannot be used if values are set using custom_question_fields.
     *
     * Generated from protobuf enum <code>OVER_65_AGE = 1125;</code>
     */
    const OVER_65_AGE = 1125;
    /**
     * Question: "Which program are you interested in?"
     * Category: "Education"
     * This field is subject to a limit of 5 qualifying questions per form and
     * cannot be used if values are set using custom_question_fields.
     *
     * Generated from protobuf enum <code>EDUCATION_PROGRAM = 1013;</code>
     */
    const EDUCATION_PROGRAM = 1013;
    /**
     * Question: "Which course are you interested in?"
     * Category: "Education"
     * This field is subject to a limit of 5 qualifying questions per form and
     * cannot be used if values are set using custom_question_fields.
     *
     * Generated from protobuf enum <code>EDUCATION_COURSE = 1014;</code>
     */
    const EDUCATION_COURSE = 1014;
    /**
     * Question: "Which product are you interested in?"
     * Category: "General"
     * This field is subject to a limit of 5 qualifying questions per form and
     * cannot be used if values are set using custom_question_fields.
     *
     * Generated from protobuf enum <code>PRODUCT = 1016;</code>
     */
    const PRODUCT = 1016;
    /**
     * Question: "Which service are you interested in?"
     * Category: "General"
     * This field is subject to a limit of 5 qualifying questions per form and
     * cannot be used if values are set using custom_question_fields.
     *
     * Generated from protobuf enum <code>SERVICE = 1017;</code>
     */
    const SERVICE = 1017;
    /**
     * Question: "Which offer are you interested in?"
     * Category: "General"
     * This field is subject to a limit of 5 qualifying questions per form and
     * cannot be used if values are set using custom_question_fields.
     *
     * Generated from protobuf enum <code>OFFER = 1018;</code>
     */
    const OFFER = 1018;
    /**
     * Question: "Which category are you interested in?"
     * Category: "General"
     * This field is subject to a limit of 5 qualifying questions per form and
     * cannot be used if values are set using custom_question_fields.
     *
     * Generated from protobuf enum <code>CATEGORY = 1019;</code>
     */
    const CATEGORY = 1019;
    /**
     * Question: "What is your preferred method of contact?"
     * Category: "General"
     * This field is subject to a limit of 5 qualifying questions per form and
     * cannot be used if values are set using custom_question_fields.
     *
     * Generated from protobuf enum <code>PREFERRED_CONTACT_METHOD = 1020;</code>
     */
    const PREFERRED_CONTACT_METHOD = 1020;
    /**
     * Question: "What is your preferred location?"
     * Category: "General"
     * This field is subject to a limit of 5 qualifying questions per form and
     * cannot be used if values are set using custom_question_fields.
     *
     * Generated from protobuf enum <code>PREFERRED_LOCATION = 1021;</code>
     */
    const PREFERRED_LOCATION = 1021;
    /**
     * Question: "What is the best time to contact you?"
     * Category: "General"
     * This field is subject to a limit of 5 qualifying questions per form and
     * cannot be used if values are set using custom_question_fields.
     *
     * Generated from protobuf enum <code>PREFERRED_CONTACT_TIME = 1022;</code>
     */
    const PREFERRED_CONTACT_TIME = 1022;
    /**
     * Question: "When are you looking to make a purchase?"
     * Category: "General"
     * This field is subject to a limit of 5 qualifying questions per form and
     * cannot be used if values are set using custom_question_fields.
     *
     * Generated from protobuf enum <code>PURCHASE_TIMELINE = 1023;</code>
     */
    const PURCHASE_TIMELINE = 1023;
    /**
     * Question: "How many years of work experience do you have?"
     * Category: "Jobs"
     * This field is subject to a limit of 5 qualifying questions per form and
     * cannot be used if values are set using custom_question_fields.
     *
     * Generated from protobuf enum <code>YEARS_OF_EXPERIENCE = 1048;</code>
     */
    const YEARS_OF_EXPERIENCE = 1048;
    /**
     * Question: "What industry do you work in?"
     * Category: "Jobs"
     * This field is subject to a limit of 5 qualifying questions per form and
     * cannot be used if values are set using custom_question_fields.
     *
     * Generated from protobuf enum <code>JOB_INDUSTRY = 1049;</code>
     */
    const JOB_INDUSTRY = 1049;
    /**
     * Question: "What is your highest level of education?"
     * Category: "Jobs"
     * This field is subject to a limit of 5 qualifying questions per form and
     * cannot be used if values are set using custom_question_fields.
     *
     * Generated from protobuf enum <code>LEVEL_OF_EDUCATION = 1050;</code>
     */
    const LEVEL_OF_EDUCATION = 1050;
    /**
     * Question: "What type of property are you looking for?"
     * Category: "Real Estate"
     * This field is subject to a limit of 5 qualifying questions per form and
     * cannot be used if values are set using custom_question_fields.
     *
     * Generated from protobuf enum <code>PROPERTY_TYPE = 1024;</code>
     */
    const PROPERTY_TYPE = 1024;
    /**
     * Question: "What do you need a realtor's help with?"
     * Category: "Real Estate"
     * This field is subject to a limit of 5 qualifying questions per form and
     * cannot be used if values are set using custom_question_fields.
     *
     * Generated from protobuf enum <code>REALTOR_HELP_GOAL = 1025;</code>
     */
    const REALTOR_HELP_GOAL = 1025;
    /**
     * Question: "What neighborhood are you interested in?"
     * Category: "Real Estate"
     * This field is subject to a limit of 5 qualifying questions per form and
     * cannot be used if values are set using custom_question_fields.
     *
     * Generated from protobuf enum <code>PROPERTY_COMMUNITY = 1026;</code>
     */
    const PROPERTY_COMMUNITY = 1026;
    /**
     * Question: "What price range are you looking for?"
     * Category: "Real Estate"
     * This field is subject to a limit of 5 qualifying questions per form and
     * cannot be used if values are set using custom_question_fields.
     *
     * Generated from protobuf enum <code>PRICE_RANGE = 1027;</code>
     */
    const PRICE_RANGE = 1027;
    /**
     * Question: "How many bedrooms are you looking for?"
     * Category: "Real Estate"
     * This field is subject to a limit of 5 qualifying questions per form and
     * cannot be used if values are set using custom_question_fields.
     *
     * Generated from protobuf enum <code>NUMBER_OF_BEDROOMS = 1028;</code>
     */
    const NUMBER_OF_BEDROOMS = 1028;
    /**
     * Question: "Are you looking for a fully furnished property?"
     * Category: "Real Estate"
     * This field is subject to a limit of 5 qualifying questions per form and
     * cannot be used if values are set using custom_question_fields.
     *
     * Generated from protobuf enum <code>FURNISHED_PROPERTY = 1029;</code>
     */
    const FURNISHED_PROPERTY = 1029;
    /**
     * Question: "Are you looking for properties that allow pets?"
     * Category: "Real Estate"
     * This field is subject to a limit of 5 qualifying questions per form and
     * cannot be used if values are set using custom_question_fields.
     *
     * Generated from protobuf enum <code>PETS_ALLOWED_PROPERTY = 1030;</code>
     */
    const PETS_ALLOWED_PROPERTY = 1030;
    /**
     * Question: "What is the next product you plan to purchase?"
     * Category: "Retail"
     * This field is subject to a limit of 5 qualifying questions per form and
     * cannot be used if values are set using custom_question_fields.
     *
     * Generated from protobuf enum <code>NEXT_PLANNED_PURCHASE = 1031;</code>
     */
    const NEXT_PLANNED_PURCHASE = 1031;
    /**
     * Question: "Would you like to sign up for an event?"
     * Category: "Retail"
     * This field is subject to a limit of 5 qualifying questions per form and
     * cannot be used if values are set using custom_question_fields.
     *
     * Generated from protobuf enum <code>EVENT_SIGNUP_INTEREST = 1033;</code>
     */
    const EVENT_SIGNUP_INTEREST = 1033;
    /**
     * Question: "Where are you interested in shopping?"
     * Category: "Retail"
     * This field is subject to a limit of 5 qualifying questions per form and
     * cannot be used if values are set using custom_question_fields.
     *
     * Generated from protobuf enum <code>PREFERRED_SHOPPING_PLACES = 1034;</code>
     */
    const PREFERRED_SHOPPING_PLACES = 1034;
    /**
     * Question: "What is your favorite brand?"
     * Category: "Retail"
     * This field is subject to a limit of 5 qualifying questions per form and
     * cannot be used if values are set using custom_question_fields.
     *
     * Generated from protobuf enum <code>FAVORITE_BRAND = 1035;</code>
     */
    const FAVORITE_BRAND = 1035;
    /**
     * Question: "Which type of valid commercial license do you have?"
     * Category: "Transportation"
     * This field is subject to a limit of 5 qualifying questions per form and
     * cannot be used if values are set using custom_question_fields.
     *
     * Generated from protobuf enum <code>TRANSPORTATION_COMMERCIAL_LICENSE_TYPE = 1036;</code>
     */
    const TRANSPORTATION_COMMERCIAL_LICENSE_TYPE = 1036;
    /**
     * Question: "Interested in booking an event?"
     * Category: "Travel"
     * This field is subject to a limit of 5 qualifying questions per form and
     * cannot be used if values are set using custom_question_fields.
     *
     * Generated from protobuf enum <code>EVENT_BOOKING_INTEREST = 1038;</code>
     */
    const EVENT_BOOKING_INTEREST = 1038;
    /**
     * Question: "What is your destination country?"
     * Category: "Travel"
     * This field is subject to a limit of 5 qualifying questions per form and
     * cannot be used if values are set using custom_question_fields.
     *
     * Generated from protobuf enum <code>DESTINATION_COUNTRY = 1039;</code>
     */
    const DESTINATION_COUNTRY = 1039;
    /**
     * Question: "What is your destination city?"
     * Category: "Travel"
     * This field is subject to a limit of 5 qualifying questions per form and
     * cannot be used if values are set using custom_question_fields.
     *
     * Generated from protobuf enum <code>DESTINATION_CITY = 1040;</code>
     */
    const DESTINATION_CITY = 1040;
    /**
     * Question: "What is your departure country?"
     * Category: "Travel"
     * This field is subject to a limit of 5 qualifying questions per form and
     * cannot be used if values are set using custom_question_fields.
     *
     * Generated from protobuf enum <code>DEPARTURE_COUNTRY = 1041;</code>
     */
    const DEPARTURE_COUNTRY = 1041;
    /**
     * Question: "What is your departure city?"
     * Category: "Travel"
     * This field is subject to a limit of 5 qualifying questions per form and
     * cannot be used if values are set using custom_question_fields.
     *
     * Generated from protobuf enum <code>DEPARTURE_CITY = 1042;</code>
     */
    const DEPARTURE_CITY = 1042;
    /**
     * Question: "What is your departure date?"
     * Category: "Travel"
     * This field is subject to a limit of 5 qualifying questions per form and
     * cannot be used if values are set using custom_question_fields.
     *
     * Generated from protobuf enum <code>DEPARTURE_DATE = 1043;</code>
     */
    const DEPARTURE_DATE = 1043;
    /**
     * Question: "What is your return date?"
     * Category: "Travel"
     * This field is subject to a limit of 5 qualifying questions per form and
     * cannot be used if values are set using custom_question_fields.
     *
     * Generated from protobuf enum <code>RETURN_DATE = 1044;</code>
     */
    const RETURN_DATE = 1044;
    /**
     * Question: "How many people are you traveling with?"
     * Category: "Travel"
     * This field is subject to a limit of 5 qualifying questions per form and
     * cannot be used if values are set using custom_question_fields.
     *
     * Generated from protobuf enum <code>NUMBER_OF_TRAVELERS = 1045;</code>
     */
    const NUMBER_OF_TRAVELERS = 1045;
    /**
     * Question: "What is your travel budget?"
     * Category: "Travel"
     * This field is subject to a limit of 5 qualifying questions per form and
     * cannot be used if values are set using custom_question_fields.
     *
     * Generated from protobuf enum <code>TRAVEL_BUDGET = 1046;</code>
     */
    const TRAVEL_BUDGET = 1046;
    /**
     * Question: "Where do you want to stay during your travel?"
     * Category: "Travel"
     * This field is subject to a limit of 5 qualifying questions per form and
     * cannot be used if values are set using custom_question_fields.
     *
     * Generated from protobuf enum <code>TRAVEL_ACCOMMODATION = 1047;</code>
     */
    const TRAVEL_ACCOMMODATION = 1047;

    private static $valueToName = [
        self::UNSPECIFIED => 'UNSPECIFIED',
        self::UNKNOWN => 'UNKNOWN',
        self::FULL_NAME => 'FULL_NAME',
        self::EMAIL => 'EMAIL',
        self::PHONE_NUMBER => 'PHONE_NUMBER',
        self::POSTAL_CODE => 'POSTAL_CODE',
        self::STREET_ADDRESS => 'STREET_ADDRESS',
        self::CITY => 'CITY',
        self::REGION => 'REGION',
        self::COUNTRY => 'COUNTRY',
        self::WORK_EMAIL => 'WORK_EMAIL',
        self::COMPANY_NAME => 'COMPANY_NAME',
        self::WORK_PHONE => 'WORK_PHONE',
        self::JOB_TITLE => 'JOB_TITLE',
        self::GOVERNMENT_ISSUED_ID_CPF_BR => 'GOVERNMENT_ISSUED_ID_CPF_BR',
        self::GOVERNMENT_ISSUED_ID_DNI_AR => 'GOVERNMENT_ISSUED_ID_DNI_AR',
        self::GOVERNMENT_ISSUED_ID_DNI_PE => 'GOVERNMENT_ISSUED_ID_DNI_PE',
        self::GOVERNMENT_ISSUED_ID_RUT_CL => 'GOVERNMENT_ISSUED_ID_RUT_CL',
        self::GOVERNMENT_ISSUED_ID_CC_CO => 'GOVERNMENT_ISSUED_ID_CC_CO',
        self::GOVERNMENT_ISSUED_ID_CI_EC => 'GOVERNMENT_ISSUED_ID_CI_EC',
        self::GOVERNMENT_ISSUED_ID_RFC_MX => 'GOVERNMENT_ISSUED_ID_RFC_MX',
        self::FIRST_NAME => 'FIRST_NAME',
        self::LAST_NAME => 'LAST_NAME',
        self::VEHICLE_MODEL => 'VEHICLE_MODEL',
        self::VEHICLE_TYPE => 'VEHICLE_TYPE',
        self::PREFERRED_DEALERSHIP => 'PREFERRED_DEALERSHIP',
        self::VEHICLE_PURCHASE_TIMELINE => 'VEHICLE_PURCHASE_TIMELINE',
        self::VEHICLE_OWNERSHIP => 'VEHICLE_OWNERSHIP',
        self::VEHICLE_PAYMENT_TYPE => 'VEHICLE_PAYMENT_TYPE',
        self::VEHICLE_CONDITION => 'VEHICLE_CONDITION',
        self::COMPANY_SIZE => 'COMPANY_SIZE',
        self::ANNUAL_SALES => 'ANNUAL_SALES',
        self::YEARS_IN_BUSINESS => 'YEARS_IN_BUSINESS',
        self::JOB_DEPARTMENT => 'JOB_DEPARTMENT',
        self::JOB_ROLE => 'JOB_ROLE',
        self::OVER_18_AGE => 'OVER_18_AGE',
        self::OVER_19_AGE => 'OVER_19_AGE',
        self::OVER_20_AGE => 'OVER_20_AGE',
        self::OVER_21_AGE => 'OVER_21_AGE',
        self::OVER_22_AGE => 'OVER_22_AGE',
        self::OVER_23_AGE => 'OVER_23_AGE',
        self::OVER_24_AGE => 'OVER_24_AGE',
        self::OVER_25_AGE => 'OVER_25_AGE',
        self::OVER_26_AGE => 'OVER_26_AGE',
        self::OVER_27_AGE => 'OVER_27_AGE',
        self::OVER_28_AGE => 'OVER_28_AGE',
        self::OVER_29_AGE => 'OVER_29_AGE',
        self::OVER_30_AGE => 'OVER_30_AGE',
        self::OVER_31_AGE => 'OVER_31_AGE',
        self::OVER_32_AGE => 'OVER_32_AGE',
        self::OVER_33_AGE => 'OVER_33_AGE',
        self::OVER_34_AGE => 'OVER_34_AGE',
        self::OVER_35_AGE => 'OVER_35_AGE',
        self::OVER_36_AGE => 'OVER_36_AGE',
        self::OVER_37_AGE => 'OVER_37_AGE',
        self::OVER_38_AGE => 'OVER_38_AGE',
        self::OVER_39_AGE => 'OVER_39_AGE',
        self::OVER_40_AGE => 'OVER_40_AGE',
        self::OVER_41_AGE => 'OVER_41_AGE',
        self::OVER_42_AGE => 'OVER_42_AGE',
        self::OVER_43_AGE => 'OVER_43_AGE',
        self::OVER_44_AGE => 'OVER_44_AGE',
        self::OVER_45_AGE => 'OVER_45_AGE',
        self::OVER_46_AGE => 'OVER_46_AGE',
        self::OVER_47_AGE => 'OVER_47_AGE',
        self::OVER_48_AGE => 'OVER_48_AGE',
        self::OVER_49_AGE => 'OVER_49_AGE',
        self::OVER_50_AGE => 'OVER_50_AGE',
        self::OVER_51_AGE => 'OVER_51_AGE',
        self::OVER_52_AGE => 'OVER_52_AGE',
        self::OVER_53_AGE => 'OVER_53_AGE',
        self::OVER_54_AGE => 'OVER_54_AGE',
        self::OVER_55_AGE => 'OVER_55_AGE',
        self::OVER_56_AGE => 'OVER_56_AGE',
        self::OVER_57_AGE => 'OVER_57_AGE',
        self::OVER_58_AGE => 'OVER_58_AGE',
        self::OVER_59_AGE => 'OVER_59_AGE',
        self::OVER_60_AGE => 'OVER_60_AGE',
        self::OVER_61_AGE => 'OVER_61_AGE',
        self::OVER_62_AGE => 'OVER_62_AGE',
        self::OVER_63_AGE => 'OVER_63_AGE',
        self::OVER_64_AGE => 'OVER_64_AGE',
        self::OVER_65_AGE => 'OVER_65_AGE',
        self::EDUCATION_PROGRAM => 'EDUCATION_PROGRAM',
        self::EDUCATION_COURSE => 'EDUCATION_COURSE',
        self::PRODUCT => 'PRODUCT',
        self::SERVICE => 'SERVICE',
        self::OFFER => 'OFFER',
        self::CATEGORY => 'CATEGORY',
        self::PREFERRED_CONTACT_METHOD => 'PREFERRED_CONTACT_METHOD',
        self::PREFERRED_LOCATION => 'PREFERRED_LOCATION',
        self::PREFERRED_CONTACT_TIME => 'PREFERRED_CONTACT_TIME',
        self::PURCHASE_TIMELINE => 'PURCHASE_TIMELINE',
        self::YEARS_OF_EXPERIENCE => 'YEARS_OF_EXPERIENCE',
        self::JOB_INDUSTRY => 'JOB_INDUSTRY',
        self::LEVEL_OF_EDUCATION => 'LEVEL_OF_EDUCATION',
        self::PROPERTY_TYPE => 'PROPERTY_TYPE',
        self::REALTOR_HELP_GOAL => 'REALTOR_HELP_GOAL',
        self::PROPERTY_COMMUNITY => 'PROPERTY_COMMUNITY',
        self::PRICE_RANGE => 'PRICE_RANGE',
        self::NUMBER_OF_BEDROOMS => 'NUMBER_OF_BEDROOMS',
        self::FURNISHED_PROPERTY => 'FURNISHED_PROPERTY',
        self::PETS_ALLOWED_PROPERTY => 'PETS_ALLOWED_PROPERTY',
        self::NEXT_PLANNED_PURCHASE => 'NEXT_PLANNED_PURCHASE',
        self::EVENT_SIGNUP_INTEREST => 'EVENT_SIGNUP_INTEREST',
        self::PREFERRED_SHOPPING_PLACES => 'PREFERRED_SHOPPING_PLACES',
        self::FAVORITE_BRAND => 'FAVORITE_BRAND',
        self::TRANSPORTATION_COMMERCIAL_LICENSE_TYPE => 'TRANSPORTATION_COMMERCIAL_LICENSE_TYPE',
        self::EVENT_BOOKING_INTEREST => 'EVENT_BOOKING_INTEREST',
        self::DESTINATION_COUNTRY => 'DESTINATION_COUNTRY',
        self::DESTINATION_CITY => 'DESTINATION_CITY',
        self::DEPARTURE_COUNTRY => 'DEPARTURE_COUNTRY',
        self::DEPARTURE_CITY => 'DEPARTURE_CITY',
        self::DEPARTURE_DATE => 'DEPARTURE_DATE',
        self::RETURN_DATE => 'RETURN_DATE',
        self::NUMBER_OF_TRAVELERS => 'NUMBER_OF_TRAVELERS',
        self::TRAVEL_BUDGET => 'TRAVEL_BUDGET',
        self::TRAVEL_ACCOMMODATION => 'TRAVEL_ACCOMMODATION',
    ];

    public static function name($value)
    {
        if (!isset(self::$valueToName[$value])) {
            throw new UnexpectedValueException(sprintf(
                    'Enum %s has no name defined for value %s', __CLASS__, $value));
        }
        return self::$valueToName[$value];
    }


    public static function value($name)
    {
        $const = __CLASS__ . '::' . strtoupper($name);
        if (!defined($const)) {
            throw new UnexpectedValueException(sprintf(
                    'Enum %s has no value defined for name %s', __CLASS__, $name));
        }
        return constant($const);
    }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(LeadFormFieldUserInputType::class, \Google\Ads\GoogleAds\V15\Enums\LeadFormFieldUserInputTypeEnum_LeadFormFieldUserInputType::class);

