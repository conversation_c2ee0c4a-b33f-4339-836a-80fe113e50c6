<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v17/services/google_ads_service.proto

namespace Google\Ads\GoogleAds\V17\Services;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * A returned row from the query.
 *
 * Generated from protobuf message <code>google.ads.googleads.v17.services.GoogleAdsRow</code>
 */
class GoogleAdsRow extends \Google\Protobuf\Internal\Message
{
    /**
     * The account budget in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AccountBudget account_budget = 42;</code>
     */
    protected $account_budget = null;
    /**
     * The account budget proposal referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AccountBudgetProposal account_budget_proposal = 43;</code>
     */
    protected $account_budget_proposal = null;
    /**
     * The AccountLink referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AccountLink account_link = 143;</code>
     */
    protected $account_link = null;
    /**
     * The Ad referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.Ad ad = 227;</code>
     */
    protected $ad = null;
    /**
     * The ad group referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AdGroup ad_group = 3;</code>
     */
    protected $ad_group = null;
    /**
     * The ad referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AdGroupAd ad_group_ad = 16;</code>
     */
    protected $ad_group_ad = null;
    /**
     * The ad group ad asset combination view in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AdGroupAdAssetCombinationView ad_group_ad_asset_combination_view = 193;</code>
     */
    protected $ad_group_ad_asset_combination_view = null;
    /**
     * The ad group ad asset view in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AdGroupAdAssetView ad_group_ad_asset_view = 131;</code>
     */
    protected $ad_group_ad_asset_view = null;
    /**
     * The ad group ad label referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AdGroupAdLabel ad_group_ad_label = 120;</code>
     */
    protected $ad_group_ad_label = null;
    /**
     * The ad group asset referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AdGroupAsset ad_group_asset = 154;</code>
     */
    protected $ad_group_asset = null;
    /**
     * The ad group asset set referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AdGroupAssetSet ad_group_asset_set = 196;</code>
     */
    protected $ad_group_asset_set = null;
    /**
     * The ad group audience view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AdGroupAudienceView ad_group_audience_view = 57;</code>
     */
    protected $ad_group_audience_view = null;
    /**
     * The bid modifier referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AdGroupBidModifier ad_group_bid_modifier = 24;</code>
     */
    protected $ad_group_bid_modifier = null;
    /**
     * The criterion referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AdGroupCriterion ad_group_criterion = 17;</code>
     */
    protected $ad_group_criterion = null;
    /**
     * The ad group criterion customizer referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AdGroupCriterionCustomizer ad_group_criterion_customizer = 187;</code>
     */
    protected $ad_group_criterion_customizer = null;
    /**
     * The ad group criterion label referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AdGroupCriterionLabel ad_group_criterion_label = 121;</code>
     */
    protected $ad_group_criterion_label = null;
    /**
     * The ad group criterion simulation referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AdGroupCriterionSimulation ad_group_criterion_simulation = 110;</code>
     */
    protected $ad_group_criterion_simulation = null;
    /**
     * The ad group customizer referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AdGroupCustomizer ad_group_customizer = 185;</code>
     */
    protected $ad_group_customizer = null;
    /**
     * The ad group extension setting referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AdGroupExtensionSetting ad_group_extension_setting = 112;</code>
     */
    protected $ad_group_extension_setting = null;
    /**
     * The ad group feed referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AdGroupFeed ad_group_feed = 67;</code>
     */
    protected $ad_group_feed = null;
    /**
     * The ad group label referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AdGroupLabel ad_group_label = 115;</code>
     */
    protected $ad_group_label = null;
    /**
     * The ad group simulation referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AdGroupSimulation ad_group_simulation = 107;</code>
     */
    protected $ad_group_simulation = null;
    /**
     * The ad parameter referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AdParameter ad_parameter = 130;</code>
     */
    protected $ad_parameter = null;
    /**
     * The age range view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AgeRangeView age_range_view = 48;</code>
     */
    protected $age_range_view = null;
    /**
     * The ad schedule view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AdScheduleView ad_schedule_view = 89;</code>
     */
    protected $ad_schedule_view = null;
    /**
     * The domain category referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.DomainCategory domain_category = 91;</code>
     */
    protected $domain_category = null;
    /**
     * The asset referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.Asset asset = 105;</code>
     */
    protected $asset = null;
    /**
     * The asset field type view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AssetFieldTypeView asset_field_type_view = 168;</code>
     */
    protected $asset_field_type_view = null;
    /**
     * The channel aggregate asset view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.ChannelAggregateAssetView channel_aggregate_asset_view = 222;</code>
     */
    protected $channel_aggregate_asset_view = null;
    /**
     * The campaign aggregate asset view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CampaignAggregateAssetView campaign_aggregate_asset_view = 224;</code>
     */
    protected $campaign_aggregate_asset_view = null;
    /**
     * The asset group asset referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AssetGroupAsset asset_group_asset = 173;</code>
     */
    protected $asset_group_asset = null;
    /**
     * The asset group signal referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AssetGroupSignal asset_group_signal = 191;</code>
     */
    protected $asset_group_signal = null;
    /**
     * The asset group listing group filter referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AssetGroupListingGroupFilter asset_group_listing_group_filter = 182;</code>
     */
    protected $asset_group_listing_group_filter = null;
    /**
     * The asset group product group view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AssetGroupProductGroupView asset_group_product_group_view = 189;</code>
     */
    protected $asset_group_product_group_view = null;
    /**
     * The asset group top combination view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AssetGroupTopCombinationView asset_group_top_combination_view = 199;</code>
     */
    protected $asset_group_top_combination_view = null;
    /**
     * The asset group referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AssetGroup asset_group = 172;</code>
     */
    protected $asset_group = null;
    /**
     * The asset set asset referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AssetSetAsset asset_set_asset = 180;</code>
     */
    protected $asset_set_asset = null;
    /**
     * The asset set referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AssetSet asset_set = 179;</code>
     */
    protected $asset_set = null;
    /**
     * The asset set type view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AssetSetTypeView asset_set_type_view = 197;</code>
     */
    protected $asset_set_type_view = null;
    /**
     * The batch job referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.BatchJob batch_job = 139;</code>
     */
    protected $batch_job = null;
    /**
     * The bidding data exclusion referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.BiddingDataExclusion bidding_data_exclusion = 159;</code>
     */
    protected $bidding_data_exclusion = null;
    /**
     * The bidding seasonality adjustment referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.BiddingSeasonalityAdjustment bidding_seasonality_adjustment = 160;</code>
     */
    protected $bidding_seasonality_adjustment = null;
    /**
     * The bidding strategy referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.BiddingStrategy bidding_strategy = 18;</code>
     */
    protected $bidding_strategy = null;
    /**
     * The bidding strategy simulation referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.BiddingStrategySimulation bidding_strategy_simulation = 158;</code>
     */
    protected $bidding_strategy_simulation = null;
    /**
     * The billing setup referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.BillingSetup billing_setup = 41;</code>
     */
    protected $billing_setup = null;
    /**
     * The call view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CallView call_view = 152;</code>
     */
    protected $call_view = null;
    /**
     * The campaign budget referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CampaignBudget campaign_budget = 19;</code>
     */
    protected $campaign_budget = null;
    /**
     * The campaign referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.Campaign campaign = 2;</code>
     */
    protected $campaign = null;
    /**
     * The campaign asset referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CampaignAsset campaign_asset = 142;</code>
     */
    protected $campaign_asset = null;
    /**
     * The campaign asset set referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CampaignAssetSet campaign_asset_set = 181;</code>
     */
    protected $campaign_asset_set = null;
    /**
     * The campaign audience view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CampaignAudienceView campaign_audience_view = 69;</code>
     */
    protected $campaign_audience_view = null;
    /**
     * The campaign bid modifier referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CampaignBidModifier campaign_bid_modifier = 26;</code>
     */
    protected $campaign_bid_modifier = null;
    /**
     * The CampaignConversionGoal referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CampaignConversionGoal campaign_conversion_goal = 175;</code>
     */
    protected $campaign_conversion_goal = null;
    /**
     * The campaign criterion referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CampaignCriterion campaign_criterion = 20;</code>
     */
    protected $campaign_criterion = null;
    /**
     * The campaign customizer referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CampaignCustomizer campaign_customizer = 186;</code>
     */
    protected $campaign_customizer = null;
    /**
     * The campaign draft referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CampaignDraft campaign_draft = 49;</code>
     */
    protected $campaign_draft = null;
    /**
     * The campaign extension setting referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CampaignExtensionSetting campaign_extension_setting = 113;</code>
     */
    protected $campaign_extension_setting = null;
    /**
     * The campaign feed referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CampaignFeed campaign_feed = 63;</code>
     */
    protected $campaign_feed = null;
    /**
     * Campaign Group referenced in AWQL query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CampaignGroup campaign_group = 25;</code>
     */
    protected $campaign_group = null;
    /**
     * The campaign label referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CampaignLabel campaign_label = 108;</code>
     */
    protected $campaign_label = null;
    /**
     * The campaign lifecycle goal referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CampaignLifecycleGoal campaign_lifecycle_goal = 213;</code>
     */
    protected $campaign_lifecycle_goal = null;
    /**
     * The campaign search term insight referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CampaignSearchTermInsight campaign_search_term_insight = 204;</code>
     */
    protected $campaign_search_term_insight = null;
    /**
     * Campaign Shared Set referenced in AWQL query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CampaignSharedSet campaign_shared_set = 30;</code>
     */
    protected $campaign_shared_set = null;
    /**
     * The campaign simulation referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CampaignSimulation campaign_simulation = 157;</code>
     */
    protected $campaign_simulation = null;
    /**
     * The carrier constant referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CarrierConstant carrier_constant = 66;</code>
     */
    protected $carrier_constant = null;
    /**
     * The ChangeEvent referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.ChangeEvent change_event = 145;</code>
     */
    protected $change_event = null;
    /**
     * The ChangeStatus referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.ChangeStatus change_status = 37;</code>
     */
    protected $change_status = null;
    /**
     * The CombinedAudience referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CombinedAudience combined_audience = 148;</code>
     */
    protected $combined_audience = null;
    /**
     * The Audience referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.Audience audience = 190;</code>
     */
    protected $audience = null;
    /**
     * The conversion action referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.ConversionAction conversion_action = 103;</code>
     */
    protected $conversion_action = null;
    /**
     * The conversion custom variable referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.ConversionCustomVariable conversion_custom_variable = 153;</code>
     */
    protected $conversion_custom_variable = null;
    /**
     * The ConversionGoalCampaignConfig referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.ConversionGoalCampaignConfig conversion_goal_campaign_config = 177;</code>
     */
    protected $conversion_goal_campaign_config = null;
    /**
     * The conversion value rule referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.ConversionValueRule conversion_value_rule = 164;</code>
     */
    protected $conversion_value_rule = null;
    /**
     * The conversion value rule set referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.ConversionValueRuleSet conversion_value_rule_set = 165;</code>
     */
    protected $conversion_value_rule_set = null;
    /**
     * The ClickView referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.ClickView click_view = 122;</code>
     */
    protected $click_view = null;
    /**
     * The currency constant referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CurrencyConstant currency_constant = 134;</code>
     */
    protected $currency_constant = null;
    /**
     * The CustomAudience referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CustomAudience custom_audience = 147;</code>
     */
    protected $custom_audience = null;
    /**
     * The CustomConversionGoal referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CustomConversionGoal custom_conversion_goal = 176;</code>
     */
    protected $custom_conversion_goal = null;
    /**
     * The CustomInterest referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CustomInterest custom_interest = 104;</code>
     */
    protected $custom_interest = null;
    /**
     * The customer referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.Customer customer = 1;</code>
     */
    protected $customer = null;
    /**
     * The customer asset referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CustomerAsset customer_asset = 155;</code>
     */
    protected $customer_asset = null;
    /**
     * The customer asset set referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CustomerAssetSet customer_asset_set = 195;</code>
     */
    protected $customer_asset_set = null;
    /**
     * The accessible bidding strategy referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AccessibleBiddingStrategy accessible_bidding_strategy = 169;</code>
     */
    protected $accessible_bidding_strategy = null;
    /**
     * The customer customizer referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CustomerCustomizer customer_customizer = 184;</code>
     */
    protected $customer_customizer = null;
    /**
     * The CustomerManagerLink referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CustomerManagerLink customer_manager_link = 61;</code>
     */
    protected $customer_manager_link = null;
    /**
     * The CustomerClientLink referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CustomerClientLink customer_client_link = 62;</code>
     */
    protected $customer_client_link = null;
    /**
     * The CustomerClient referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CustomerClient customer_client = 70;</code>
     */
    protected $customer_client = null;
    /**
     * The CustomerConversionGoal referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CustomerConversionGoal customer_conversion_goal = 174;</code>
     */
    protected $customer_conversion_goal = null;
    /**
     * The customer extension setting referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CustomerExtensionSetting customer_extension_setting = 114;</code>
     */
    protected $customer_extension_setting = null;
    /**
     * The customer feed referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CustomerFeed customer_feed = 64;</code>
     */
    protected $customer_feed = null;
    /**
     * The customer label referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CustomerLabel customer_label = 124;</code>
     */
    protected $customer_label = null;
    /**
     * The customer lifecycle goal referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CustomerLifecycleGoal customer_lifecycle_goal = 212;</code>
     */
    protected $customer_lifecycle_goal = null;
    /**
     * The customer negative criterion referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CustomerNegativeCriterion customer_negative_criterion = 88;</code>
     */
    protected $customer_negative_criterion = null;
    /**
     * The customer search term insight referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CustomerSearchTermInsight customer_search_term_insight = 205;</code>
     */
    protected $customer_search_term_insight = null;
    /**
     * The CustomerUserAccess referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CustomerUserAccess customer_user_access = 146;</code>
     */
    protected $customer_user_access = null;
    /**
     * The CustomerUserAccessInvitation referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CustomerUserAccessInvitation customer_user_access_invitation = 150;</code>
     */
    protected $customer_user_access_invitation = null;
    /**
     * The customizer attribute referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CustomizerAttribute customizer_attribute = 178;</code>
     */
    protected $customizer_attribute = null;
    /**
     * The detail placement view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.DetailPlacementView detail_placement_view = 118;</code>
     */
    protected $detail_placement_view = null;
    /**
     * The detailed demographic referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.DetailedDemographic detailed_demographic = 166;</code>
     */
    protected $detailed_demographic = null;
    /**
     * The display keyword view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.DisplayKeywordView display_keyword_view = 47;</code>
     */
    protected $display_keyword_view = null;
    /**
     * The distance view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.DistanceView distance_view = 132;</code>
     */
    protected $distance_view = null;
    /**
     * The dynamic search ads search term view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.DynamicSearchAdsSearchTermView dynamic_search_ads_search_term_view = 106;</code>
     */
    protected $dynamic_search_ads_search_term_view = null;
    /**
     * The expanded landing page view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.ExpandedLandingPageView expanded_landing_page_view = 128;</code>
     */
    protected $expanded_landing_page_view = null;
    /**
     * The extension feed item referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.ExtensionFeedItem extension_feed_item = 85;</code>
     */
    protected $extension_feed_item = null;
    /**
     * The feed referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.Feed feed = 46;</code>
     */
    protected $feed = null;
    /**
     * The feed item referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.FeedItem feed_item = 50;</code>
     */
    protected $feed_item = null;
    /**
     * The feed item set referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.FeedItemSet feed_item_set = 149;</code>
     */
    protected $feed_item_set = null;
    /**
     * The feed item set link referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.FeedItemSetLink feed_item_set_link = 151;</code>
     */
    protected $feed_item_set_link = null;
    /**
     * The feed item target referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.FeedItemTarget feed_item_target = 116;</code>
     */
    protected $feed_item_target = null;
    /**
     * The feed mapping referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.FeedMapping feed_mapping = 58;</code>
     */
    protected $feed_mapping = null;
    /**
     * The feed placeholder view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.FeedPlaceholderView feed_placeholder_view = 97;</code>
     */
    protected $feed_placeholder_view = null;
    /**
     * The gender view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.GenderView gender_view = 40;</code>
     */
    protected $gender_view = null;
    /**
     * The geo target constant referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.GeoTargetConstant geo_target_constant = 23;</code>
     */
    protected $geo_target_constant = null;
    /**
     * The geographic view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.GeographicView geographic_view = 125;</code>
     */
    protected $geographic_view = null;
    /**
     * The group placement view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.GroupPlacementView group_placement_view = 119;</code>
     */
    protected $group_placement_view = null;
    /**
     * The hotel group view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.HotelGroupView hotel_group_view = 51;</code>
     */
    protected $hotel_group_view = null;
    /**
     * The hotel performance view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.HotelPerformanceView hotel_performance_view = 71;</code>
     */
    protected $hotel_performance_view = null;
    /**
     * The hotel reconciliation referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.HotelReconciliation hotel_reconciliation = 188;</code>
     */
    protected $hotel_reconciliation = null;
    /**
     * The income range view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.IncomeRangeView income_range_view = 138;</code>
     */
    protected $income_range_view = null;
    /**
     * The keyword view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.KeywordView keyword_view = 21;</code>
     */
    protected $keyword_view = null;
    /**
     * The keyword plan referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.KeywordPlan keyword_plan = 32;</code>
     */
    protected $keyword_plan = null;
    /**
     * The keyword plan campaign referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.KeywordPlanCampaign keyword_plan_campaign = 33;</code>
     */
    protected $keyword_plan_campaign = null;
    /**
     * The keyword plan campaign keyword referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.KeywordPlanCampaignKeyword keyword_plan_campaign_keyword = 140;</code>
     */
    protected $keyword_plan_campaign_keyword = null;
    /**
     * The keyword plan ad group referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.KeywordPlanAdGroup keyword_plan_ad_group = 35;</code>
     */
    protected $keyword_plan_ad_group = null;
    /**
     * The keyword plan ad group referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.KeywordPlanAdGroupKeyword keyword_plan_ad_group_keyword = 141;</code>
     */
    protected $keyword_plan_ad_group_keyword = null;
    /**
     * The keyword theme constant referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.KeywordThemeConstant keyword_theme_constant = 163;</code>
     */
    protected $keyword_theme_constant = null;
    /**
     * The label referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.Label label = 52;</code>
     */
    protected $label = null;
    /**
     * The landing page view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.LandingPageView landing_page_view = 126;</code>
     */
    protected $landing_page_view = null;
    /**
     * The language constant referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.LanguageConstant language_constant = 55;</code>
     */
    protected $language_constant = null;
    /**
     * The location view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.LocationView location_view = 123;</code>
     */
    protected $location_view = null;
    /**
     * The managed placement view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.ManagedPlacementView managed_placement_view = 53;</code>
     */
    protected $managed_placement_view = null;
    /**
     * The media file referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.MediaFile media_file = 90;</code>
     */
    protected $media_file = null;
    /**
     * The local services employee referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.LocalServicesEmployee local_services_employee = 223;</code>
     */
    protected $local_services_employee = null;
    /**
     * The local services verification artifact referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.LocalServicesVerificationArtifact local_services_verification_artifact = 211;</code>
     */
    protected $local_services_verification_artifact = null;
    /**
     * The mobile app category constant referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.MobileAppCategoryConstant mobile_app_category_constant = 87;</code>
     */
    protected $mobile_app_category_constant = null;
    /**
     * The mobile device constant referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.MobileDeviceConstant mobile_device_constant = 98;</code>
     */
    protected $mobile_device_constant = null;
    /**
     * Offline conversion upload client summary.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.OfflineConversionUploadClientSummary offline_conversion_upload_client_summary = 216;</code>
     */
    protected $offline_conversion_upload_client_summary = null;
    /**
     * The offline user data job referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.OfflineUserDataJob offline_user_data_job = 137;</code>
     */
    protected $offline_user_data_job = null;
    /**
     * The operating system version constant referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.OperatingSystemVersionConstant operating_system_version_constant = 86;</code>
     */
    protected $operating_system_version_constant = null;
    /**
     * The paid organic search term view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.PaidOrganicSearchTermView paid_organic_search_term_view = 129;</code>
     */
    protected $paid_organic_search_term_view = null;
    /**
     * The qualifying question referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.QualifyingQuestion qualifying_question = 202;</code>
     */
    protected $qualifying_question = null;
    /**
     * The parental status view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.ParentalStatusView parental_status_view = 45;</code>
     */
    protected $parental_status_view = null;
    /**
     * The per store view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.PerStoreView per_store_view = 198;</code>
     */
    protected $per_store_view = null;
    /**
     * The product category referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.ProductCategoryConstant product_category_constant = 208;</code>
     */
    protected $product_category_constant = null;
    /**
     * The product group view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.ProductGroupView product_group_view = 54;</code>
     */
    protected $product_group_view = null;
    /**
     * The product link referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.ProductLink product_link = 194;</code>
     */
    protected $product_link = null;
    /**
     * The product link invitation in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.ProductLinkInvitation product_link_invitation = 209;</code>
     */
    protected $product_link_invitation = null;
    /**
     * The recommendation referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.Recommendation recommendation = 22;</code>
     */
    protected $recommendation = null;
    /**
     * The recommendation subscription referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.RecommendationSubscription recommendation_subscription = 220;</code>
     */
    protected $recommendation_subscription = null;
    /**
     * The search term view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.SearchTermView search_term_view = 68;</code>
     */
    protected $search_term_view = null;
    /**
     * The shared set referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.SharedCriterion shared_criterion = 29;</code>
     */
    protected $shared_criterion = null;
    /**
     * The shared set referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.SharedSet shared_set = 27;</code>
     */
    protected $shared_set = null;
    /**
     * The Smart campaign setting referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.SmartCampaignSetting smart_campaign_setting = 167;</code>
     */
    protected $smart_campaign_setting = null;
    /**
     * The shopping performance view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.ShoppingPerformanceView shopping_performance_view = 117;</code>
     */
    protected $shopping_performance_view = null;
    /**
     * The shopping product referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.ShoppingProduct shopping_product = 226;</code>
     */
    protected $shopping_product = null;
    /**
     * The Smart campaign search term view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.SmartCampaignSearchTermView smart_campaign_search_term_view = 170;</code>
     */
    protected $smart_campaign_search_term_view = null;
    /**
     * The AccountLink referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.ThirdPartyAppAnalyticsLink third_party_app_analytics_link = 144;</code>
     */
    protected $third_party_app_analytics_link = null;
    /**
     * The topic view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.TopicView topic_view = 44;</code>
     */
    protected $topic_view = null;
    /**
     * The travel activity group view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.TravelActivityGroupView travel_activity_group_view = 201;</code>
     */
    protected $travel_activity_group_view = null;
    /**
     * The travel activity performance view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.TravelActivityPerformanceView travel_activity_performance_view = 200;</code>
     */
    protected $travel_activity_performance_view = null;
    /**
     * The experiment referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.Experiment experiment = 133;</code>
     */
    protected $experiment = null;
    /**
     * The experiment arm referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.ExperimentArm experiment_arm = 183;</code>
     */
    protected $experiment_arm = null;
    /**
     * The user interest referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.UserInterest user_interest = 59;</code>
     */
    protected $user_interest = null;
    /**
     * The life event referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.LifeEvent life_event = 161;</code>
     */
    protected $life_event = null;
    /**
     * The user list referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.UserList user_list = 38;</code>
     */
    protected $user_list = null;
    /**
     * The user list customer type in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.UserListCustomerType user_list_customer_type = 225;</code>
     */
    protected $user_list_customer_type = null;
    /**
     * The user location view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.UserLocationView user_location_view = 135;</code>
     */
    protected $user_location_view = null;
    /**
     * The remarketing action referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.RemarketingAction remarketing_action = 60;</code>
     */
    protected $remarketing_action = null;
    /**
     * The topic constant referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.TopicConstant topic_constant = 31;</code>
     */
    protected $topic_constant = null;
    /**
     * The video referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.Video video = 39;</code>
     */
    protected $video = null;
    /**
     * The webpage view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.WebpageView webpage_view = 162;</code>
     */
    protected $webpage_view = null;
    /**
     * The lead form user submission referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.LeadFormSubmissionData lead_form_submission_data = 192;</code>
     */
    protected $lead_form_submission_data = null;
    /**
     * The local services lead referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.LocalServicesLead local_services_lead = 210;</code>
     */
    protected $local_services_lead = null;
    /**
     * The local services lead conversationreferenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.LocalServicesLeadConversation local_services_lead_conversation = 214;</code>
     */
    protected $local_services_lead_conversation = null;
    /**
     * The android privacy shared key google ad group referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AndroidPrivacySharedKeyGoogleAdGroup android_privacy_shared_key_google_ad_group = 217;</code>
     */
    protected $android_privacy_shared_key_google_ad_group = null;
    /**
     * The android privacy shared key google campaign referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AndroidPrivacySharedKeyGoogleCampaign android_privacy_shared_key_google_campaign = 218;</code>
     */
    protected $android_privacy_shared_key_google_campaign = null;
    /**
     * The android privacy shared key google network type referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AndroidPrivacySharedKeyGoogleNetworkType android_privacy_shared_key_google_network_type = 219;</code>
     */
    protected $android_privacy_shared_key_google_network_type = null;
    /**
     * The metrics.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.common.Metrics metrics = 4;</code>
     */
    protected $metrics = null;
    /**
     * The segments.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.common.Segments segments = 102;</code>
     */
    protected $segments = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type \Google\Ads\GoogleAds\V17\Resources\AccountBudget $account_budget
     *           The account budget in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\AccountBudgetProposal $account_budget_proposal
     *           The account budget proposal referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\AccountLink $account_link
     *           The AccountLink referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\Ad $ad
     *           The Ad referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\AdGroup $ad_group
     *           The ad group referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\AdGroupAd $ad_group_ad
     *           The ad referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\AdGroupAdAssetCombinationView $ad_group_ad_asset_combination_view
     *           The ad group ad asset combination view in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\AdGroupAdAssetView $ad_group_ad_asset_view
     *           The ad group ad asset view in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\AdGroupAdLabel $ad_group_ad_label
     *           The ad group ad label referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\AdGroupAsset $ad_group_asset
     *           The ad group asset referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\AdGroupAssetSet $ad_group_asset_set
     *           The ad group asset set referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\AdGroupAudienceView $ad_group_audience_view
     *           The ad group audience view referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\AdGroupBidModifier $ad_group_bid_modifier
     *           The bid modifier referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\AdGroupCriterion $ad_group_criterion
     *           The criterion referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\AdGroupCriterionCustomizer $ad_group_criterion_customizer
     *           The ad group criterion customizer referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\AdGroupCriterionLabel $ad_group_criterion_label
     *           The ad group criterion label referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\AdGroupCriterionSimulation $ad_group_criterion_simulation
     *           The ad group criterion simulation referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\AdGroupCustomizer $ad_group_customizer
     *           The ad group customizer referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\AdGroupExtensionSetting $ad_group_extension_setting
     *           The ad group extension setting referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\AdGroupFeed $ad_group_feed
     *           The ad group feed referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\AdGroupLabel $ad_group_label
     *           The ad group label referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\AdGroupSimulation $ad_group_simulation
     *           The ad group simulation referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\AdParameter $ad_parameter
     *           The ad parameter referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\AgeRangeView $age_range_view
     *           The age range view referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\AdScheduleView $ad_schedule_view
     *           The ad schedule view referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\DomainCategory $domain_category
     *           The domain category referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\Asset $asset
     *           The asset referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\AssetFieldTypeView $asset_field_type_view
     *           The asset field type view referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\ChannelAggregateAssetView $channel_aggregate_asset_view
     *           The channel aggregate asset view referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\CampaignAggregateAssetView $campaign_aggregate_asset_view
     *           The campaign aggregate asset view referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\AssetGroupAsset $asset_group_asset
     *           The asset group asset referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\AssetGroupSignal $asset_group_signal
     *           The asset group signal referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\AssetGroupListingGroupFilter $asset_group_listing_group_filter
     *           The asset group listing group filter referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\AssetGroupProductGroupView $asset_group_product_group_view
     *           The asset group product group view referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\AssetGroupTopCombinationView $asset_group_top_combination_view
     *           The asset group top combination view referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\AssetGroup $asset_group
     *           The asset group referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\AssetSetAsset $asset_set_asset
     *           The asset set asset referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\AssetSet $asset_set
     *           The asset set referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\AssetSetTypeView $asset_set_type_view
     *           The asset set type view referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\BatchJob $batch_job
     *           The batch job referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\BiddingDataExclusion $bidding_data_exclusion
     *           The bidding data exclusion referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\BiddingSeasonalityAdjustment $bidding_seasonality_adjustment
     *           The bidding seasonality adjustment referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\BiddingStrategy $bidding_strategy
     *           The bidding strategy referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\BiddingStrategySimulation $bidding_strategy_simulation
     *           The bidding strategy simulation referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\BillingSetup $billing_setup
     *           The billing setup referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\CallView $call_view
     *           The call view referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\CampaignBudget $campaign_budget
     *           The campaign budget referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\Campaign $campaign
     *           The campaign referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\CampaignAsset $campaign_asset
     *           The campaign asset referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\CampaignAssetSet $campaign_asset_set
     *           The campaign asset set referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\CampaignAudienceView $campaign_audience_view
     *           The campaign audience view referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\CampaignBidModifier $campaign_bid_modifier
     *           The campaign bid modifier referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\CampaignConversionGoal $campaign_conversion_goal
     *           The CampaignConversionGoal referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\CampaignCriterion $campaign_criterion
     *           The campaign criterion referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\CampaignCustomizer $campaign_customizer
     *           The campaign customizer referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\CampaignDraft $campaign_draft
     *           The campaign draft referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\CampaignExtensionSetting $campaign_extension_setting
     *           The campaign extension setting referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\CampaignFeed $campaign_feed
     *           The campaign feed referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\CampaignGroup $campaign_group
     *           Campaign Group referenced in AWQL query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\CampaignLabel $campaign_label
     *           The campaign label referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\CampaignLifecycleGoal $campaign_lifecycle_goal
     *           The campaign lifecycle goal referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\CampaignSearchTermInsight $campaign_search_term_insight
     *           The campaign search term insight referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\CampaignSharedSet $campaign_shared_set
     *           Campaign Shared Set referenced in AWQL query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\CampaignSimulation $campaign_simulation
     *           The campaign simulation referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\CarrierConstant $carrier_constant
     *           The carrier constant referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\ChangeEvent $change_event
     *           The ChangeEvent referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\ChangeStatus $change_status
     *           The ChangeStatus referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\CombinedAudience $combined_audience
     *           The CombinedAudience referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\Audience $audience
     *           The Audience referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\ConversionAction $conversion_action
     *           The conversion action referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\ConversionCustomVariable $conversion_custom_variable
     *           The conversion custom variable referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\ConversionGoalCampaignConfig $conversion_goal_campaign_config
     *           The ConversionGoalCampaignConfig referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\ConversionValueRule $conversion_value_rule
     *           The conversion value rule referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\ConversionValueRuleSet $conversion_value_rule_set
     *           The conversion value rule set referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\ClickView $click_view
     *           The ClickView referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\CurrencyConstant $currency_constant
     *           The currency constant referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\CustomAudience $custom_audience
     *           The CustomAudience referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\CustomConversionGoal $custom_conversion_goal
     *           The CustomConversionGoal referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\CustomInterest $custom_interest
     *           The CustomInterest referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\Customer $customer
     *           The customer referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\CustomerAsset $customer_asset
     *           The customer asset referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\CustomerAssetSet $customer_asset_set
     *           The customer asset set referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\AccessibleBiddingStrategy $accessible_bidding_strategy
     *           The accessible bidding strategy referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\CustomerCustomizer $customer_customizer
     *           The customer customizer referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\CustomerManagerLink $customer_manager_link
     *           The CustomerManagerLink referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\CustomerClientLink $customer_client_link
     *           The CustomerClientLink referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\CustomerClient $customer_client
     *           The CustomerClient referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\CustomerConversionGoal $customer_conversion_goal
     *           The CustomerConversionGoal referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\CustomerExtensionSetting $customer_extension_setting
     *           The customer extension setting referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\CustomerFeed $customer_feed
     *           The customer feed referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\CustomerLabel $customer_label
     *           The customer label referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\CustomerLifecycleGoal $customer_lifecycle_goal
     *           The customer lifecycle goal referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\CustomerNegativeCriterion $customer_negative_criterion
     *           The customer negative criterion referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\CustomerSearchTermInsight $customer_search_term_insight
     *           The customer search term insight referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\CustomerUserAccess $customer_user_access
     *           The CustomerUserAccess referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\CustomerUserAccessInvitation $customer_user_access_invitation
     *           The CustomerUserAccessInvitation referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\CustomizerAttribute $customizer_attribute
     *           The customizer attribute referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\DetailPlacementView $detail_placement_view
     *           The detail placement view referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\DetailedDemographic $detailed_demographic
     *           The detailed demographic referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\DisplayKeywordView $display_keyword_view
     *           The display keyword view referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\DistanceView $distance_view
     *           The distance view referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\DynamicSearchAdsSearchTermView $dynamic_search_ads_search_term_view
     *           The dynamic search ads search term view referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\ExpandedLandingPageView $expanded_landing_page_view
     *           The expanded landing page view referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\ExtensionFeedItem $extension_feed_item
     *           The extension feed item referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\Feed $feed
     *           The feed referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\FeedItem $feed_item
     *           The feed item referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\FeedItemSet $feed_item_set
     *           The feed item set referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\FeedItemSetLink $feed_item_set_link
     *           The feed item set link referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\FeedItemTarget $feed_item_target
     *           The feed item target referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\FeedMapping $feed_mapping
     *           The feed mapping referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\FeedPlaceholderView $feed_placeholder_view
     *           The feed placeholder view referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\GenderView $gender_view
     *           The gender view referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\GeoTargetConstant $geo_target_constant
     *           The geo target constant referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\GeographicView $geographic_view
     *           The geographic view referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\GroupPlacementView $group_placement_view
     *           The group placement view referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\HotelGroupView $hotel_group_view
     *           The hotel group view referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\HotelPerformanceView $hotel_performance_view
     *           The hotel performance view referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\HotelReconciliation $hotel_reconciliation
     *           The hotel reconciliation referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\IncomeRangeView $income_range_view
     *           The income range view referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\KeywordView $keyword_view
     *           The keyword view referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\KeywordPlan $keyword_plan
     *           The keyword plan referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\KeywordPlanCampaign $keyword_plan_campaign
     *           The keyword plan campaign referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\KeywordPlanCampaignKeyword $keyword_plan_campaign_keyword
     *           The keyword plan campaign keyword referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\KeywordPlanAdGroup $keyword_plan_ad_group
     *           The keyword plan ad group referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\KeywordPlanAdGroupKeyword $keyword_plan_ad_group_keyword
     *           The keyword plan ad group referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\KeywordThemeConstant $keyword_theme_constant
     *           The keyword theme constant referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\Label $label
     *           The label referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\LandingPageView $landing_page_view
     *           The landing page view referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\LanguageConstant $language_constant
     *           The language constant referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\LocationView $location_view
     *           The location view referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\ManagedPlacementView $managed_placement_view
     *           The managed placement view referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\MediaFile $media_file
     *           The media file referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\LocalServicesEmployee $local_services_employee
     *           The local services employee referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\LocalServicesVerificationArtifact $local_services_verification_artifact
     *           The local services verification artifact referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\MobileAppCategoryConstant $mobile_app_category_constant
     *           The mobile app category constant referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\MobileDeviceConstant $mobile_device_constant
     *           The mobile device constant referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\OfflineConversionUploadClientSummary $offline_conversion_upload_client_summary
     *           Offline conversion upload client summary.
     *     @type \Google\Ads\GoogleAds\V17\Resources\OfflineUserDataJob $offline_user_data_job
     *           The offline user data job referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\OperatingSystemVersionConstant $operating_system_version_constant
     *           The operating system version constant referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\PaidOrganicSearchTermView $paid_organic_search_term_view
     *           The paid organic search term view referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\QualifyingQuestion $qualifying_question
     *           The qualifying question referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\ParentalStatusView $parental_status_view
     *           The parental status view referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\PerStoreView $per_store_view
     *           The per store view referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\ProductCategoryConstant $product_category_constant
     *           The product category referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\ProductGroupView $product_group_view
     *           The product group view referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\ProductLink $product_link
     *           The product link referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\ProductLinkInvitation $product_link_invitation
     *           The product link invitation in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\Recommendation $recommendation
     *           The recommendation referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\RecommendationSubscription $recommendation_subscription
     *           The recommendation subscription referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\SearchTermView $search_term_view
     *           The search term view referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\SharedCriterion $shared_criterion
     *           The shared set referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\SharedSet $shared_set
     *           The shared set referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\SmartCampaignSetting $smart_campaign_setting
     *           The Smart campaign setting referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\ShoppingPerformanceView $shopping_performance_view
     *           The shopping performance view referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\ShoppingProduct $shopping_product
     *           The shopping product referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\SmartCampaignSearchTermView $smart_campaign_search_term_view
     *           The Smart campaign search term view referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\ThirdPartyAppAnalyticsLink $third_party_app_analytics_link
     *           The AccountLink referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\TopicView $topic_view
     *           The topic view referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\TravelActivityGroupView $travel_activity_group_view
     *           The travel activity group view referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\TravelActivityPerformanceView $travel_activity_performance_view
     *           The travel activity performance view referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\Experiment $experiment
     *           The experiment referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\ExperimentArm $experiment_arm
     *           The experiment arm referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\UserInterest $user_interest
     *           The user interest referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\LifeEvent $life_event
     *           The life event referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\UserList $user_list
     *           The user list referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\UserListCustomerType $user_list_customer_type
     *           The user list customer type in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\UserLocationView $user_location_view
     *           The user location view referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\RemarketingAction $remarketing_action
     *           The remarketing action referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\TopicConstant $topic_constant
     *           The topic constant referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\Video $video
     *           The video referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\WebpageView $webpage_view
     *           The webpage view referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\LeadFormSubmissionData $lead_form_submission_data
     *           The lead form user submission referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\LocalServicesLead $local_services_lead
     *           The local services lead referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\LocalServicesLeadConversation $local_services_lead_conversation
     *           The local services lead conversationreferenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\AndroidPrivacySharedKeyGoogleAdGroup $android_privacy_shared_key_google_ad_group
     *           The android privacy shared key google ad group referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\AndroidPrivacySharedKeyGoogleCampaign $android_privacy_shared_key_google_campaign
     *           The android privacy shared key google campaign referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Resources\AndroidPrivacySharedKeyGoogleNetworkType $android_privacy_shared_key_google_network_type
     *           The android privacy shared key google network type referenced in the query.
     *     @type \Google\Ads\GoogleAds\V17\Common\Metrics $metrics
     *           The metrics.
     *     @type \Google\Ads\GoogleAds\V17\Common\Segments $segments
     *           The segments.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Ads\GoogleAds\V17\Services\GoogleAdsService::initOnce();
        parent::__construct($data);
    }

    /**
     * The account budget in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AccountBudget account_budget = 42;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\AccountBudget|null
     */
    public function getAccountBudget()
    {
        return $this->account_budget;
    }

    public function hasAccountBudget()
    {
        return isset($this->account_budget);
    }

    public function clearAccountBudget()
    {
        unset($this->account_budget);
    }

    /**
     * The account budget in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AccountBudget account_budget = 42;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\AccountBudget $var
     * @return $this
     */
    public function setAccountBudget($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\AccountBudget::class);
        $this->account_budget = $var;

        return $this;
    }

    /**
     * The account budget proposal referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AccountBudgetProposal account_budget_proposal = 43;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\AccountBudgetProposal|null
     */
    public function getAccountBudgetProposal()
    {
        return $this->account_budget_proposal;
    }

    public function hasAccountBudgetProposal()
    {
        return isset($this->account_budget_proposal);
    }

    public function clearAccountBudgetProposal()
    {
        unset($this->account_budget_proposal);
    }

    /**
     * The account budget proposal referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AccountBudgetProposal account_budget_proposal = 43;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\AccountBudgetProposal $var
     * @return $this
     */
    public function setAccountBudgetProposal($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\AccountBudgetProposal::class);
        $this->account_budget_proposal = $var;

        return $this;
    }

    /**
     * The AccountLink referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AccountLink account_link = 143;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\AccountLink|null
     */
    public function getAccountLink()
    {
        return $this->account_link;
    }

    public function hasAccountLink()
    {
        return isset($this->account_link);
    }

    public function clearAccountLink()
    {
        unset($this->account_link);
    }

    /**
     * The AccountLink referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AccountLink account_link = 143;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\AccountLink $var
     * @return $this
     */
    public function setAccountLink($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\AccountLink::class);
        $this->account_link = $var;

        return $this;
    }

    /**
     * The Ad referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.Ad ad = 227;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\Ad|null
     */
    public function getAd()
    {
        return $this->ad;
    }

    public function hasAd()
    {
        return isset($this->ad);
    }

    public function clearAd()
    {
        unset($this->ad);
    }

    /**
     * The Ad referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.Ad ad = 227;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\Ad $var
     * @return $this
     */
    public function setAd($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\Ad::class);
        $this->ad = $var;

        return $this;
    }

    /**
     * The ad group referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AdGroup ad_group = 3;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\AdGroup|null
     */
    public function getAdGroup()
    {
        return $this->ad_group;
    }

    public function hasAdGroup()
    {
        return isset($this->ad_group);
    }

    public function clearAdGroup()
    {
        unset($this->ad_group);
    }

    /**
     * The ad group referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AdGroup ad_group = 3;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\AdGroup $var
     * @return $this
     */
    public function setAdGroup($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\AdGroup::class);
        $this->ad_group = $var;

        return $this;
    }

    /**
     * The ad referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AdGroupAd ad_group_ad = 16;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\AdGroupAd|null
     */
    public function getAdGroupAd()
    {
        return $this->ad_group_ad;
    }

    public function hasAdGroupAd()
    {
        return isset($this->ad_group_ad);
    }

    public function clearAdGroupAd()
    {
        unset($this->ad_group_ad);
    }

    /**
     * The ad referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AdGroupAd ad_group_ad = 16;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\AdGroupAd $var
     * @return $this
     */
    public function setAdGroupAd($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\AdGroupAd::class);
        $this->ad_group_ad = $var;

        return $this;
    }

    /**
     * The ad group ad asset combination view in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AdGroupAdAssetCombinationView ad_group_ad_asset_combination_view = 193;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\AdGroupAdAssetCombinationView|null
     */
    public function getAdGroupAdAssetCombinationView()
    {
        return $this->ad_group_ad_asset_combination_view;
    }

    public function hasAdGroupAdAssetCombinationView()
    {
        return isset($this->ad_group_ad_asset_combination_view);
    }

    public function clearAdGroupAdAssetCombinationView()
    {
        unset($this->ad_group_ad_asset_combination_view);
    }

    /**
     * The ad group ad asset combination view in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AdGroupAdAssetCombinationView ad_group_ad_asset_combination_view = 193;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\AdGroupAdAssetCombinationView $var
     * @return $this
     */
    public function setAdGroupAdAssetCombinationView($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\AdGroupAdAssetCombinationView::class);
        $this->ad_group_ad_asset_combination_view = $var;

        return $this;
    }

    /**
     * The ad group ad asset view in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AdGroupAdAssetView ad_group_ad_asset_view = 131;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\AdGroupAdAssetView|null
     */
    public function getAdGroupAdAssetView()
    {
        return $this->ad_group_ad_asset_view;
    }

    public function hasAdGroupAdAssetView()
    {
        return isset($this->ad_group_ad_asset_view);
    }

    public function clearAdGroupAdAssetView()
    {
        unset($this->ad_group_ad_asset_view);
    }

    /**
     * The ad group ad asset view in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AdGroupAdAssetView ad_group_ad_asset_view = 131;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\AdGroupAdAssetView $var
     * @return $this
     */
    public function setAdGroupAdAssetView($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\AdGroupAdAssetView::class);
        $this->ad_group_ad_asset_view = $var;

        return $this;
    }

    /**
     * The ad group ad label referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AdGroupAdLabel ad_group_ad_label = 120;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\AdGroupAdLabel|null
     */
    public function getAdGroupAdLabel()
    {
        return $this->ad_group_ad_label;
    }

    public function hasAdGroupAdLabel()
    {
        return isset($this->ad_group_ad_label);
    }

    public function clearAdGroupAdLabel()
    {
        unset($this->ad_group_ad_label);
    }

    /**
     * The ad group ad label referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AdGroupAdLabel ad_group_ad_label = 120;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\AdGroupAdLabel $var
     * @return $this
     */
    public function setAdGroupAdLabel($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\AdGroupAdLabel::class);
        $this->ad_group_ad_label = $var;

        return $this;
    }

    /**
     * The ad group asset referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AdGroupAsset ad_group_asset = 154;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\AdGroupAsset|null
     */
    public function getAdGroupAsset()
    {
        return $this->ad_group_asset;
    }

    public function hasAdGroupAsset()
    {
        return isset($this->ad_group_asset);
    }

    public function clearAdGroupAsset()
    {
        unset($this->ad_group_asset);
    }

    /**
     * The ad group asset referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AdGroupAsset ad_group_asset = 154;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\AdGroupAsset $var
     * @return $this
     */
    public function setAdGroupAsset($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\AdGroupAsset::class);
        $this->ad_group_asset = $var;

        return $this;
    }

    /**
     * The ad group asset set referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AdGroupAssetSet ad_group_asset_set = 196;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\AdGroupAssetSet|null
     */
    public function getAdGroupAssetSet()
    {
        return $this->ad_group_asset_set;
    }

    public function hasAdGroupAssetSet()
    {
        return isset($this->ad_group_asset_set);
    }

    public function clearAdGroupAssetSet()
    {
        unset($this->ad_group_asset_set);
    }

    /**
     * The ad group asset set referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AdGroupAssetSet ad_group_asset_set = 196;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\AdGroupAssetSet $var
     * @return $this
     */
    public function setAdGroupAssetSet($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\AdGroupAssetSet::class);
        $this->ad_group_asset_set = $var;

        return $this;
    }

    /**
     * The ad group audience view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AdGroupAudienceView ad_group_audience_view = 57;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\AdGroupAudienceView|null
     */
    public function getAdGroupAudienceView()
    {
        return $this->ad_group_audience_view;
    }

    public function hasAdGroupAudienceView()
    {
        return isset($this->ad_group_audience_view);
    }

    public function clearAdGroupAudienceView()
    {
        unset($this->ad_group_audience_view);
    }

    /**
     * The ad group audience view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AdGroupAudienceView ad_group_audience_view = 57;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\AdGroupAudienceView $var
     * @return $this
     */
    public function setAdGroupAudienceView($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\AdGroupAudienceView::class);
        $this->ad_group_audience_view = $var;

        return $this;
    }

    /**
     * The bid modifier referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AdGroupBidModifier ad_group_bid_modifier = 24;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\AdGroupBidModifier|null
     */
    public function getAdGroupBidModifier()
    {
        return $this->ad_group_bid_modifier;
    }

    public function hasAdGroupBidModifier()
    {
        return isset($this->ad_group_bid_modifier);
    }

    public function clearAdGroupBidModifier()
    {
        unset($this->ad_group_bid_modifier);
    }

    /**
     * The bid modifier referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AdGroupBidModifier ad_group_bid_modifier = 24;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\AdGroupBidModifier $var
     * @return $this
     */
    public function setAdGroupBidModifier($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\AdGroupBidModifier::class);
        $this->ad_group_bid_modifier = $var;

        return $this;
    }

    /**
     * The criterion referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AdGroupCriterion ad_group_criterion = 17;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\AdGroupCriterion|null
     */
    public function getAdGroupCriterion()
    {
        return $this->ad_group_criterion;
    }

    public function hasAdGroupCriterion()
    {
        return isset($this->ad_group_criterion);
    }

    public function clearAdGroupCriterion()
    {
        unset($this->ad_group_criterion);
    }

    /**
     * The criterion referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AdGroupCriterion ad_group_criterion = 17;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\AdGroupCriterion $var
     * @return $this
     */
    public function setAdGroupCriterion($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\AdGroupCriterion::class);
        $this->ad_group_criterion = $var;

        return $this;
    }

    /**
     * The ad group criterion customizer referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AdGroupCriterionCustomizer ad_group_criterion_customizer = 187;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\AdGroupCriterionCustomizer|null
     */
    public function getAdGroupCriterionCustomizer()
    {
        return $this->ad_group_criterion_customizer;
    }

    public function hasAdGroupCriterionCustomizer()
    {
        return isset($this->ad_group_criterion_customizer);
    }

    public function clearAdGroupCriterionCustomizer()
    {
        unset($this->ad_group_criterion_customizer);
    }

    /**
     * The ad group criterion customizer referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AdGroupCriterionCustomizer ad_group_criterion_customizer = 187;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\AdGroupCriterionCustomizer $var
     * @return $this
     */
    public function setAdGroupCriterionCustomizer($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\AdGroupCriterionCustomizer::class);
        $this->ad_group_criterion_customizer = $var;

        return $this;
    }

    /**
     * The ad group criterion label referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AdGroupCriterionLabel ad_group_criterion_label = 121;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\AdGroupCriterionLabel|null
     */
    public function getAdGroupCriterionLabel()
    {
        return $this->ad_group_criterion_label;
    }

    public function hasAdGroupCriterionLabel()
    {
        return isset($this->ad_group_criterion_label);
    }

    public function clearAdGroupCriterionLabel()
    {
        unset($this->ad_group_criterion_label);
    }

    /**
     * The ad group criterion label referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AdGroupCriterionLabel ad_group_criterion_label = 121;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\AdGroupCriterionLabel $var
     * @return $this
     */
    public function setAdGroupCriterionLabel($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\AdGroupCriterionLabel::class);
        $this->ad_group_criterion_label = $var;

        return $this;
    }

    /**
     * The ad group criterion simulation referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AdGroupCriterionSimulation ad_group_criterion_simulation = 110;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\AdGroupCriterionSimulation|null
     */
    public function getAdGroupCriterionSimulation()
    {
        return $this->ad_group_criterion_simulation;
    }

    public function hasAdGroupCriterionSimulation()
    {
        return isset($this->ad_group_criterion_simulation);
    }

    public function clearAdGroupCriterionSimulation()
    {
        unset($this->ad_group_criterion_simulation);
    }

    /**
     * The ad group criterion simulation referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AdGroupCriterionSimulation ad_group_criterion_simulation = 110;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\AdGroupCriterionSimulation $var
     * @return $this
     */
    public function setAdGroupCriterionSimulation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\AdGroupCriterionSimulation::class);
        $this->ad_group_criterion_simulation = $var;

        return $this;
    }

    /**
     * The ad group customizer referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AdGroupCustomizer ad_group_customizer = 185;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\AdGroupCustomizer|null
     */
    public function getAdGroupCustomizer()
    {
        return $this->ad_group_customizer;
    }

    public function hasAdGroupCustomizer()
    {
        return isset($this->ad_group_customizer);
    }

    public function clearAdGroupCustomizer()
    {
        unset($this->ad_group_customizer);
    }

    /**
     * The ad group customizer referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AdGroupCustomizer ad_group_customizer = 185;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\AdGroupCustomizer $var
     * @return $this
     */
    public function setAdGroupCustomizer($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\AdGroupCustomizer::class);
        $this->ad_group_customizer = $var;

        return $this;
    }

    /**
     * The ad group extension setting referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AdGroupExtensionSetting ad_group_extension_setting = 112;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\AdGroupExtensionSetting|null
     */
    public function getAdGroupExtensionSetting()
    {
        return $this->ad_group_extension_setting;
    }

    public function hasAdGroupExtensionSetting()
    {
        return isset($this->ad_group_extension_setting);
    }

    public function clearAdGroupExtensionSetting()
    {
        unset($this->ad_group_extension_setting);
    }

    /**
     * The ad group extension setting referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AdGroupExtensionSetting ad_group_extension_setting = 112;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\AdGroupExtensionSetting $var
     * @return $this
     */
    public function setAdGroupExtensionSetting($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\AdGroupExtensionSetting::class);
        $this->ad_group_extension_setting = $var;

        return $this;
    }

    /**
     * The ad group feed referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AdGroupFeed ad_group_feed = 67;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\AdGroupFeed|null
     */
    public function getAdGroupFeed()
    {
        return $this->ad_group_feed;
    }

    public function hasAdGroupFeed()
    {
        return isset($this->ad_group_feed);
    }

    public function clearAdGroupFeed()
    {
        unset($this->ad_group_feed);
    }

    /**
     * The ad group feed referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AdGroupFeed ad_group_feed = 67;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\AdGroupFeed $var
     * @return $this
     */
    public function setAdGroupFeed($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\AdGroupFeed::class);
        $this->ad_group_feed = $var;

        return $this;
    }

    /**
     * The ad group label referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AdGroupLabel ad_group_label = 115;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\AdGroupLabel|null
     */
    public function getAdGroupLabel()
    {
        return $this->ad_group_label;
    }

    public function hasAdGroupLabel()
    {
        return isset($this->ad_group_label);
    }

    public function clearAdGroupLabel()
    {
        unset($this->ad_group_label);
    }

    /**
     * The ad group label referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AdGroupLabel ad_group_label = 115;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\AdGroupLabel $var
     * @return $this
     */
    public function setAdGroupLabel($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\AdGroupLabel::class);
        $this->ad_group_label = $var;

        return $this;
    }

    /**
     * The ad group simulation referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AdGroupSimulation ad_group_simulation = 107;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\AdGroupSimulation|null
     */
    public function getAdGroupSimulation()
    {
        return $this->ad_group_simulation;
    }

    public function hasAdGroupSimulation()
    {
        return isset($this->ad_group_simulation);
    }

    public function clearAdGroupSimulation()
    {
        unset($this->ad_group_simulation);
    }

    /**
     * The ad group simulation referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AdGroupSimulation ad_group_simulation = 107;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\AdGroupSimulation $var
     * @return $this
     */
    public function setAdGroupSimulation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\AdGroupSimulation::class);
        $this->ad_group_simulation = $var;

        return $this;
    }

    /**
     * The ad parameter referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AdParameter ad_parameter = 130;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\AdParameter|null
     */
    public function getAdParameter()
    {
        return $this->ad_parameter;
    }

    public function hasAdParameter()
    {
        return isset($this->ad_parameter);
    }

    public function clearAdParameter()
    {
        unset($this->ad_parameter);
    }

    /**
     * The ad parameter referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AdParameter ad_parameter = 130;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\AdParameter $var
     * @return $this
     */
    public function setAdParameter($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\AdParameter::class);
        $this->ad_parameter = $var;

        return $this;
    }

    /**
     * The age range view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AgeRangeView age_range_view = 48;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\AgeRangeView|null
     */
    public function getAgeRangeView()
    {
        return $this->age_range_view;
    }

    public function hasAgeRangeView()
    {
        return isset($this->age_range_view);
    }

    public function clearAgeRangeView()
    {
        unset($this->age_range_view);
    }

    /**
     * The age range view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AgeRangeView age_range_view = 48;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\AgeRangeView $var
     * @return $this
     */
    public function setAgeRangeView($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\AgeRangeView::class);
        $this->age_range_view = $var;

        return $this;
    }

    /**
     * The ad schedule view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AdScheduleView ad_schedule_view = 89;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\AdScheduleView|null
     */
    public function getAdScheduleView()
    {
        return $this->ad_schedule_view;
    }

    public function hasAdScheduleView()
    {
        return isset($this->ad_schedule_view);
    }

    public function clearAdScheduleView()
    {
        unset($this->ad_schedule_view);
    }

    /**
     * The ad schedule view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AdScheduleView ad_schedule_view = 89;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\AdScheduleView $var
     * @return $this
     */
    public function setAdScheduleView($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\AdScheduleView::class);
        $this->ad_schedule_view = $var;

        return $this;
    }

    /**
     * The domain category referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.DomainCategory domain_category = 91;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\DomainCategory|null
     */
    public function getDomainCategory()
    {
        return $this->domain_category;
    }

    public function hasDomainCategory()
    {
        return isset($this->domain_category);
    }

    public function clearDomainCategory()
    {
        unset($this->domain_category);
    }

    /**
     * The domain category referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.DomainCategory domain_category = 91;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\DomainCategory $var
     * @return $this
     */
    public function setDomainCategory($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\DomainCategory::class);
        $this->domain_category = $var;

        return $this;
    }

    /**
     * The asset referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.Asset asset = 105;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\Asset|null
     */
    public function getAsset()
    {
        return $this->asset;
    }

    public function hasAsset()
    {
        return isset($this->asset);
    }

    public function clearAsset()
    {
        unset($this->asset);
    }

    /**
     * The asset referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.Asset asset = 105;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\Asset $var
     * @return $this
     */
    public function setAsset($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\Asset::class);
        $this->asset = $var;

        return $this;
    }

    /**
     * The asset field type view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AssetFieldTypeView asset_field_type_view = 168;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\AssetFieldTypeView|null
     */
    public function getAssetFieldTypeView()
    {
        return $this->asset_field_type_view;
    }

    public function hasAssetFieldTypeView()
    {
        return isset($this->asset_field_type_view);
    }

    public function clearAssetFieldTypeView()
    {
        unset($this->asset_field_type_view);
    }

    /**
     * The asset field type view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AssetFieldTypeView asset_field_type_view = 168;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\AssetFieldTypeView $var
     * @return $this
     */
    public function setAssetFieldTypeView($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\AssetFieldTypeView::class);
        $this->asset_field_type_view = $var;

        return $this;
    }

    /**
     * The channel aggregate asset view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.ChannelAggregateAssetView channel_aggregate_asset_view = 222;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\ChannelAggregateAssetView|null
     */
    public function getChannelAggregateAssetView()
    {
        return $this->channel_aggregate_asset_view;
    }

    public function hasChannelAggregateAssetView()
    {
        return isset($this->channel_aggregate_asset_view);
    }

    public function clearChannelAggregateAssetView()
    {
        unset($this->channel_aggregate_asset_view);
    }

    /**
     * The channel aggregate asset view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.ChannelAggregateAssetView channel_aggregate_asset_view = 222;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\ChannelAggregateAssetView $var
     * @return $this
     */
    public function setChannelAggregateAssetView($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\ChannelAggregateAssetView::class);
        $this->channel_aggregate_asset_view = $var;

        return $this;
    }

    /**
     * The campaign aggregate asset view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CampaignAggregateAssetView campaign_aggregate_asset_view = 224;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\CampaignAggregateAssetView|null
     */
    public function getCampaignAggregateAssetView()
    {
        return $this->campaign_aggregate_asset_view;
    }

    public function hasCampaignAggregateAssetView()
    {
        return isset($this->campaign_aggregate_asset_view);
    }

    public function clearCampaignAggregateAssetView()
    {
        unset($this->campaign_aggregate_asset_view);
    }

    /**
     * The campaign aggregate asset view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CampaignAggregateAssetView campaign_aggregate_asset_view = 224;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\CampaignAggregateAssetView $var
     * @return $this
     */
    public function setCampaignAggregateAssetView($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\CampaignAggregateAssetView::class);
        $this->campaign_aggregate_asset_view = $var;

        return $this;
    }

    /**
     * The asset group asset referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AssetGroupAsset asset_group_asset = 173;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\AssetGroupAsset|null
     */
    public function getAssetGroupAsset()
    {
        return $this->asset_group_asset;
    }

    public function hasAssetGroupAsset()
    {
        return isset($this->asset_group_asset);
    }

    public function clearAssetGroupAsset()
    {
        unset($this->asset_group_asset);
    }

    /**
     * The asset group asset referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AssetGroupAsset asset_group_asset = 173;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\AssetGroupAsset $var
     * @return $this
     */
    public function setAssetGroupAsset($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\AssetGroupAsset::class);
        $this->asset_group_asset = $var;

        return $this;
    }

    /**
     * The asset group signal referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AssetGroupSignal asset_group_signal = 191;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\AssetGroupSignal|null
     */
    public function getAssetGroupSignal()
    {
        return $this->asset_group_signal;
    }

    public function hasAssetGroupSignal()
    {
        return isset($this->asset_group_signal);
    }

    public function clearAssetGroupSignal()
    {
        unset($this->asset_group_signal);
    }

    /**
     * The asset group signal referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AssetGroupSignal asset_group_signal = 191;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\AssetGroupSignal $var
     * @return $this
     */
    public function setAssetGroupSignal($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\AssetGroupSignal::class);
        $this->asset_group_signal = $var;

        return $this;
    }

    /**
     * The asset group listing group filter referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AssetGroupListingGroupFilter asset_group_listing_group_filter = 182;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\AssetGroupListingGroupFilter|null
     */
    public function getAssetGroupListingGroupFilter()
    {
        return $this->asset_group_listing_group_filter;
    }

    public function hasAssetGroupListingGroupFilter()
    {
        return isset($this->asset_group_listing_group_filter);
    }

    public function clearAssetGroupListingGroupFilter()
    {
        unset($this->asset_group_listing_group_filter);
    }

    /**
     * The asset group listing group filter referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AssetGroupListingGroupFilter asset_group_listing_group_filter = 182;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\AssetGroupListingGroupFilter $var
     * @return $this
     */
    public function setAssetGroupListingGroupFilter($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\AssetGroupListingGroupFilter::class);
        $this->asset_group_listing_group_filter = $var;

        return $this;
    }

    /**
     * The asset group product group view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AssetGroupProductGroupView asset_group_product_group_view = 189;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\AssetGroupProductGroupView|null
     */
    public function getAssetGroupProductGroupView()
    {
        return $this->asset_group_product_group_view;
    }

    public function hasAssetGroupProductGroupView()
    {
        return isset($this->asset_group_product_group_view);
    }

    public function clearAssetGroupProductGroupView()
    {
        unset($this->asset_group_product_group_view);
    }

    /**
     * The asset group product group view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AssetGroupProductGroupView asset_group_product_group_view = 189;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\AssetGroupProductGroupView $var
     * @return $this
     */
    public function setAssetGroupProductGroupView($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\AssetGroupProductGroupView::class);
        $this->asset_group_product_group_view = $var;

        return $this;
    }

    /**
     * The asset group top combination view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AssetGroupTopCombinationView asset_group_top_combination_view = 199;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\AssetGroupTopCombinationView|null
     */
    public function getAssetGroupTopCombinationView()
    {
        return $this->asset_group_top_combination_view;
    }

    public function hasAssetGroupTopCombinationView()
    {
        return isset($this->asset_group_top_combination_view);
    }

    public function clearAssetGroupTopCombinationView()
    {
        unset($this->asset_group_top_combination_view);
    }

    /**
     * The asset group top combination view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AssetGroupTopCombinationView asset_group_top_combination_view = 199;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\AssetGroupTopCombinationView $var
     * @return $this
     */
    public function setAssetGroupTopCombinationView($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\AssetGroupTopCombinationView::class);
        $this->asset_group_top_combination_view = $var;

        return $this;
    }

    /**
     * The asset group referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AssetGroup asset_group = 172;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\AssetGroup|null
     */
    public function getAssetGroup()
    {
        return $this->asset_group;
    }

    public function hasAssetGroup()
    {
        return isset($this->asset_group);
    }

    public function clearAssetGroup()
    {
        unset($this->asset_group);
    }

    /**
     * The asset group referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AssetGroup asset_group = 172;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\AssetGroup $var
     * @return $this
     */
    public function setAssetGroup($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\AssetGroup::class);
        $this->asset_group = $var;

        return $this;
    }

    /**
     * The asset set asset referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AssetSetAsset asset_set_asset = 180;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\AssetSetAsset|null
     */
    public function getAssetSetAsset()
    {
        return $this->asset_set_asset;
    }

    public function hasAssetSetAsset()
    {
        return isset($this->asset_set_asset);
    }

    public function clearAssetSetAsset()
    {
        unset($this->asset_set_asset);
    }

    /**
     * The asset set asset referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AssetSetAsset asset_set_asset = 180;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\AssetSetAsset $var
     * @return $this
     */
    public function setAssetSetAsset($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\AssetSetAsset::class);
        $this->asset_set_asset = $var;

        return $this;
    }

    /**
     * The asset set referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AssetSet asset_set = 179;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\AssetSet|null
     */
    public function getAssetSet()
    {
        return $this->asset_set;
    }

    public function hasAssetSet()
    {
        return isset($this->asset_set);
    }

    public function clearAssetSet()
    {
        unset($this->asset_set);
    }

    /**
     * The asset set referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AssetSet asset_set = 179;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\AssetSet $var
     * @return $this
     */
    public function setAssetSet($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\AssetSet::class);
        $this->asset_set = $var;

        return $this;
    }

    /**
     * The asset set type view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AssetSetTypeView asset_set_type_view = 197;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\AssetSetTypeView|null
     */
    public function getAssetSetTypeView()
    {
        return $this->asset_set_type_view;
    }

    public function hasAssetSetTypeView()
    {
        return isset($this->asset_set_type_view);
    }

    public function clearAssetSetTypeView()
    {
        unset($this->asset_set_type_view);
    }

    /**
     * The asset set type view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AssetSetTypeView asset_set_type_view = 197;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\AssetSetTypeView $var
     * @return $this
     */
    public function setAssetSetTypeView($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\AssetSetTypeView::class);
        $this->asset_set_type_view = $var;

        return $this;
    }

    /**
     * The batch job referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.BatchJob batch_job = 139;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\BatchJob|null
     */
    public function getBatchJob()
    {
        return $this->batch_job;
    }

    public function hasBatchJob()
    {
        return isset($this->batch_job);
    }

    public function clearBatchJob()
    {
        unset($this->batch_job);
    }

    /**
     * The batch job referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.BatchJob batch_job = 139;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\BatchJob $var
     * @return $this
     */
    public function setBatchJob($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\BatchJob::class);
        $this->batch_job = $var;

        return $this;
    }

    /**
     * The bidding data exclusion referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.BiddingDataExclusion bidding_data_exclusion = 159;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\BiddingDataExclusion|null
     */
    public function getBiddingDataExclusion()
    {
        return $this->bidding_data_exclusion;
    }

    public function hasBiddingDataExclusion()
    {
        return isset($this->bidding_data_exclusion);
    }

    public function clearBiddingDataExclusion()
    {
        unset($this->bidding_data_exclusion);
    }

    /**
     * The bidding data exclusion referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.BiddingDataExclusion bidding_data_exclusion = 159;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\BiddingDataExclusion $var
     * @return $this
     */
    public function setBiddingDataExclusion($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\BiddingDataExclusion::class);
        $this->bidding_data_exclusion = $var;

        return $this;
    }

    /**
     * The bidding seasonality adjustment referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.BiddingSeasonalityAdjustment bidding_seasonality_adjustment = 160;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\BiddingSeasonalityAdjustment|null
     */
    public function getBiddingSeasonalityAdjustment()
    {
        return $this->bidding_seasonality_adjustment;
    }

    public function hasBiddingSeasonalityAdjustment()
    {
        return isset($this->bidding_seasonality_adjustment);
    }

    public function clearBiddingSeasonalityAdjustment()
    {
        unset($this->bidding_seasonality_adjustment);
    }

    /**
     * The bidding seasonality adjustment referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.BiddingSeasonalityAdjustment bidding_seasonality_adjustment = 160;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\BiddingSeasonalityAdjustment $var
     * @return $this
     */
    public function setBiddingSeasonalityAdjustment($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\BiddingSeasonalityAdjustment::class);
        $this->bidding_seasonality_adjustment = $var;

        return $this;
    }

    /**
     * The bidding strategy referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.BiddingStrategy bidding_strategy = 18;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\BiddingStrategy|null
     */
    public function getBiddingStrategy()
    {
        return $this->bidding_strategy;
    }

    public function hasBiddingStrategy()
    {
        return isset($this->bidding_strategy);
    }

    public function clearBiddingStrategy()
    {
        unset($this->bidding_strategy);
    }

    /**
     * The bidding strategy referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.BiddingStrategy bidding_strategy = 18;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\BiddingStrategy $var
     * @return $this
     */
    public function setBiddingStrategy($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\BiddingStrategy::class);
        $this->bidding_strategy = $var;

        return $this;
    }

    /**
     * The bidding strategy simulation referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.BiddingStrategySimulation bidding_strategy_simulation = 158;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\BiddingStrategySimulation|null
     */
    public function getBiddingStrategySimulation()
    {
        return $this->bidding_strategy_simulation;
    }

    public function hasBiddingStrategySimulation()
    {
        return isset($this->bidding_strategy_simulation);
    }

    public function clearBiddingStrategySimulation()
    {
        unset($this->bidding_strategy_simulation);
    }

    /**
     * The bidding strategy simulation referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.BiddingStrategySimulation bidding_strategy_simulation = 158;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\BiddingStrategySimulation $var
     * @return $this
     */
    public function setBiddingStrategySimulation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\BiddingStrategySimulation::class);
        $this->bidding_strategy_simulation = $var;

        return $this;
    }

    /**
     * The billing setup referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.BillingSetup billing_setup = 41;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\BillingSetup|null
     */
    public function getBillingSetup()
    {
        return $this->billing_setup;
    }

    public function hasBillingSetup()
    {
        return isset($this->billing_setup);
    }

    public function clearBillingSetup()
    {
        unset($this->billing_setup);
    }

    /**
     * The billing setup referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.BillingSetup billing_setup = 41;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\BillingSetup $var
     * @return $this
     */
    public function setBillingSetup($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\BillingSetup::class);
        $this->billing_setup = $var;

        return $this;
    }

    /**
     * The call view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CallView call_view = 152;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\CallView|null
     */
    public function getCallView()
    {
        return $this->call_view;
    }

    public function hasCallView()
    {
        return isset($this->call_view);
    }

    public function clearCallView()
    {
        unset($this->call_view);
    }

    /**
     * The call view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CallView call_view = 152;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\CallView $var
     * @return $this
     */
    public function setCallView($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\CallView::class);
        $this->call_view = $var;

        return $this;
    }

    /**
     * The campaign budget referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CampaignBudget campaign_budget = 19;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\CampaignBudget|null
     */
    public function getCampaignBudget()
    {
        return $this->campaign_budget;
    }

    public function hasCampaignBudget()
    {
        return isset($this->campaign_budget);
    }

    public function clearCampaignBudget()
    {
        unset($this->campaign_budget);
    }

    /**
     * The campaign budget referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CampaignBudget campaign_budget = 19;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\CampaignBudget $var
     * @return $this
     */
    public function setCampaignBudget($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\CampaignBudget::class);
        $this->campaign_budget = $var;

        return $this;
    }

    /**
     * The campaign referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.Campaign campaign = 2;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\Campaign|null
     */
    public function getCampaign()
    {
        return $this->campaign;
    }

    public function hasCampaign()
    {
        return isset($this->campaign);
    }

    public function clearCampaign()
    {
        unset($this->campaign);
    }

    /**
     * The campaign referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.Campaign campaign = 2;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\Campaign $var
     * @return $this
     */
    public function setCampaign($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\Campaign::class);
        $this->campaign = $var;

        return $this;
    }

    /**
     * The campaign asset referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CampaignAsset campaign_asset = 142;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\CampaignAsset|null
     */
    public function getCampaignAsset()
    {
        return $this->campaign_asset;
    }

    public function hasCampaignAsset()
    {
        return isset($this->campaign_asset);
    }

    public function clearCampaignAsset()
    {
        unset($this->campaign_asset);
    }

    /**
     * The campaign asset referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CampaignAsset campaign_asset = 142;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\CampaignAsset $var
     * @return $this
     */
    public function setCampaignAsset($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\CampaignAsset::class);
        $this->campaign_asset = $var;

        return $this;
    }

    /**
     * The campaign asset set referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CampaignAssetSet campaign_asset_set = 181;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\CampaignAssetSet|null
     */
    public function getCampaignAssetSet()
    {
        return $this->campaign_asset_set;
    }

    public function hasCampaignAssetSet()
    {
        return isset($this->campaign_asset_set);
    }

    public function clearCampaignAssetSet()
    {
        unset($this->campaign_asset_set);
    }

    /**
     * The campaign asset set referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CampaignAssetSet campaign_asset_set = 181;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\CampaignAssetSet $var
     * @return $this
     */
    public function setCampaignAssetSet($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\CampaignAssetSet::class);
        $this->campaign_asset_set = $var;

        return $this;
    }

    /**
     * The campaign audience view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CampaignAudienceView campaign_audience_view = 69;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\CampaignAudienceView|null
     */
    public function getCampaignAudienceView()
    {
        return $this->campaign_audience_view;
    }

    public function hasCampaignAudienceView()
    {
        return isset($this->campaign_audience_view);
    }

    public function clearCampaignAudienceView()
    {
        unset($this->campaign_audience_view);
    }

    /**
     * The campaign audience view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CampaignAudienceView campaign_audience_view = 69;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\CampaignAudienceView $var
     * @return $this
     */
    public function setCampaignAudienceView($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\CampaignAudienceView::class);
        $this->campaign_audience_view = $var;

        return $this;
    }

    /**
     * The campaign bid modifier referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CampaignBidModifier campaign_bid_modifier = 26;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\CampaignBidModifier|null
     */
    public function getCampaignBidModifier()
    {
        return $this->campaign_bid_modifier;
    }

    public function hasCampaignBidModifier()
    {
        return isset($this->campaign_bid_modifier);
    }

    public function clearCampaignBidModifier()
    {
        unset($this->campaign_bid_modifier);
    }

    /**
     * The campaign bid modifier referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CampaignBidModifier campaign_bid_modifier = 26;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\CampaignBidModifier $var
     * @return $this
     */
    public function setCampaignBidModifier($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\CampaignBidModifier::class);
        $this->campaign_bid_modifier = $var;

        return $this;
    }

    /**
     * The CampaignConversionGoal referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CampaignConversionGoal campaign_conversion_goal = 175;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\CampaignConversionGoal|null
     */
    public function getCampaignConversionGoal()
    {
        return $this->campaign_conversion_goal;
    }

    public function hasCampaignConversionGoal()
    {
        return isset($this->campaign_conversion_goal);
    }

    public function clearCampaignConversionGoal()
    {
        unset($this->campaign_conversion_goal);
    }

    /**
     * The CampaignConversionGoal referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CampaignConversionGoal campaign_conversion_goal = 175;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\CampaignConversionGoal $var
     * @return $this
     */
    public function setCampaignConversionGoal($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\CampaignConversionGoal::class);
        $this->campaign_conversion_goal = $var;

        return $this;
    }

    /**
     * The campaign criterion referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CampaignCriterion campaign_criterion = 20;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\CampaignCriterion|null
     */
    public function getCampaignCriterion()
    {
        return $this->campaign_criterion;
    }

    public function hasCampaignCriterion()
    {
        return isset($this->campaign_criterion);
    }

    public function clearCampaignCriterion()
    {
        unset($this->campaign_criterion);
    }

    /**
     * The campaign criterion referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CampaignCriterion campaign_criterion = 20;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\CampaignCriterion $var
     * @return $this
     */
    public function setCampaignCriterion($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\CampaignCriterion::class);
        $this->campaign_criterion = $var;

        return $this;
    }

    /**
     * The campaign customizer referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CampaignCustomizer campaign_customizer = 186;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\CampaignCustomizer|null
     */
    public function getCampaignCustomizer()
    {
        return $this->campaign_customizer;
    }

    public function hasCampaignCustomizer()
    {
        return isset($this->campaign_customizer);
    }

    public function clearCampaignCustomizer()
    {
        unset($this->campaign_customizer);
    }

    /**
     * The campaign customizer referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CampaignCustomizer campaign_customizer = 186;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\CampaignCustomizer $var
     * @return $this
     */
    public function setCampaignCustomizer($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\CampaignCustomizer::class);
        $this->campaign_customizer = $var;

        return $this;
    }

    /**
     * The campaign draft referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CampaignDraft campaign_draft = 49;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\CampaignDraft|null
     */
    public function getCampaignDraft()
    {
        return $this->campaign_draft;
    }

    public function hasCampaignDraft()
    {
        return isset($this->campaign_draft);
    }

    public function clearCampaignDraft()
    {
        unset($this->campaign_draft);
    }

    /**
     * The campaign draft referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CampaignDraft campaign_draft = 49;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\CampaignDraft $var
     * @return $this
     */
    public function setCampaignDraft($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\CampaignDraft::class);
        $this->campaign_draft = $var;

        return $this;
    }

    /**
     * The campaign extension setting referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CampaignExtensionSetting campaign_extension_setting = 113;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\CampaignExtensionSetting|null
     */
    public function getCampaignExtensionSetting()
    {
        return $this->campaign_extension_setting;
    }

    public function hasCampaignExtensionSetting()
    {
        return isset($this->campaign_extension_setting);
    }

    public function clearCampaignExtensionSetting()
    {
        unset($this->campaign_extension_setting);
    }

    /**
     * The campaign extension setting referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CampaignExtensionSetting campaign_extension_setting = 113;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\CampaignExtensionSetting $var
     * @return $this
     */
    public function setCampaignExtensionSetting($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\CampaignExtensionSetting::class);
        $this->campaign_extension_setting = $var;

        return $this;
    }

    /**
     * The campaign feed referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CampaignFeed campaign_feed = 63;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\CampaignFeed|null
     */
    public function getCampaignFeed()
    {
        return $this->campaign_feed;
    }

    public function hasCampaignFeed()
    {
        return isset($this->campaign_feed);
    }

    public function clearCampaignFeed()
    {
        unset($this->campaign_feed);
    }

    /**
     * The campaign feed referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CampaignFeed campaign_feed = 63;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\CampaignFeed $var
     * @return $this
     */
    public function setCampaignFeed($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\CampaignFeed::class);
        $this->campaign_feed = $var;

        return $this;
    }

    /**
     * Campaign Group referenced in AWQL query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CampaignGroup campaign_group = 25;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\CampaignGroup|null
     */
    public function getCampaignGroup()
    {
        return $this->campaign_group;
    }

    public function hasCampaignGroup()
    {
        return isset($this->campaign_group);
    }

    public function clearCampaignGroup()
    {
        unset($this->campaign_group);
    }

    /**
     * Campaign Group referenced in AWQL query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CampaignGroup campaign_group = 25;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\CampaignGroup $var
     * @return $this
     */
    public function setCampaignGroup($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\CampaignGroup::class);
        $this->campaign_group = $var;

        return $this;
    }

    /**
     * The campaign label referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CampaignLabel campaign_label = 108;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\CampaignLabel|null
     */
    public function getCampaignLabel()
    {
        return $this->campaign_label;
    }

    public function hasCampaignLabel()
    {
        return isset($this->campaign_label);
    }

    public function clearCampaignLabel()
    {
        unset($this->campaign_label);
    }

    /**
     * The campaign label referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CampaignLabel campaign_label = 108;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\CampaignLabel $var
     * @return $this
     */
    public function setCampaignLabel($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\CampaignLabel::class);
        $this->campaign_label = $var;

        return $this;
    }

    /**
     * The campaign lifecycle goal referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CampaignLifecycleGoal campaign_lifecycle_goal = 213;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\CampaignLifecycleGoal|null
     */
    public function getCampaignLifecycleGoal()
    {
        return $this->campaign_lifecycle_goal;
    }

    public function hasCampaignLifecycleGoal()
    {
        return isset($this->campaign_lifecycle_goal);
    }

    public function clearCampaignLifecycleGoal()
    {
        unset($this->campaign_lifecycle_goal);
    }

    /**
     * The campaign lifecycle goal referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CampaignLifecycleGoal campaign_lifecycle_goal = 213;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\CampaignLifecycleGoal $var
     * @return $this
     */
    public function setCampaignLifecycleGoal($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\CampaignLifecycleGoal::class);
        $this->campaign_lifecycle_goal = $var;

        return $this;
    }

    /**
     * The campaign search term insight referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CampaignSearchTermInsight campaign_search_term_insight = 204;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\CampaignSearchTermInsight|null
     */
    public function getCampaignSearchTermInsight()
    {
        return $this->campaign_search_term_insight;
    }

    public function hasCampaignSearchTermInsight()
    {
        return isset($this->campaign_search_term_insight);
    }

    public function clearCampaignSearchTermInsight()
    {
        unset($this->campaign_search_term_insight);
    }

    /**
     * The campaign search term insight referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CampaignSearchTermInsight campaign_search_term_insight = 204;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\CampaignSearchTermInsight $var
     * @return $this
     */
    public function setCampaignSearchTermInsight($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\CampaignSearchTermInsight::class);
        $this->campaign_search_term_insight = $var;

        return $this;
    }

    /**
     * Campaign Shared Set referenced in AWQL query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CampaignSharedSet campaign_shared_set = 30;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\CampaignSharedSet|null
     */
    public function getCampaignSharedSet()
    {
        return $this->campaign_shared_set;
    }

    public function hasCampaignSharedSet()
    {
        return isset($this->campaign_shared_set);
    }

    public function clearCampaignSharedSet()
    {
        unset($this->campaign_shared_set);
    }

    /**
     * Campaign Shared Set referenced in AWQL query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CampaignSharedSet campaign_shared_set = 30;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\CampaignSharedSet $var
     * @return $this
     */
    public function setCampaignSharedSet($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\CampaignSharedSet::class);
        $this->campaign_shared_set = $var;

        return $this;
    }

    /**
     * The campaign simulation referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CampaignSimulation campaign_simulation = 157;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\CampaignSimulation|null
     */
    public function getCampaignSimulation()
    {
        return $this->campaign_simulation;
    }

    public function hasCampaignSimulation()
    {
        return isset($this->campaign_simulation);
    }

    public function clearCampaignSimulation()
    {
        unset($this->campaign_simulation);
    }

    /**
     * The campaign simulation referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CampaignSimulation campaign_simulation = 157;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\CampaignSimulation $var
     * @return $this
     */
    public function setCampaignSimulation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\CampaignSimulation::class);
        $this->campaign_simulation = $var;

        return $this;
    }

    /**
     * The carrier constant referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CarrierConstant carrier_constant = 66;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\CarrierConstant|null
     */
    public function getCarrierConstant()
    {
        return $this->carrier_constant;
    }

    public function hasCarrierConstant()
    {
        return isset($this->carrier_constant);
    }

    public function clearCarrierConstant()
    {
        unset($this->carrier_constant);
    }

    /**
     * The carrier constant referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CarrierConstant carrier_constant = 66;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\CarrierConstant $var
     * @return $this
     */
    public function setCarrierConstant($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\CarrierConstant::class);
        $this->carrier_constant = $var;

        return $this;
    }

    /**
     * The ChangeEvent referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.ChangeEvent change_event = 145;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\ChangeEvent|null
     */
    public function getChangeEvent()
    {
        return $this->change_event;
    }

    public function hasChangeEvent()
    {
        return isset($this->change_event);
    }

    public function clearChangeEvent()
    {
        unset($this->change_event);
    }

    /**
     * The ChangeEvent referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.ChangeEvent change_event = 145;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\ChangeEvent $var
     * @return $this
     */
    public function setChangeEvent($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\ChangeEvent::class);
        $this->change_event = $var;

        return $this;
    }

    /**
     * The ChangeStatus referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.ChangeStatus change_status = 37;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\ChangeStatus|null
     */
    public function getChangeStatus()
    {
        return $this->change_status;
    }

    public function hasChangeStatus()
    {
        return isset($this->change_status);
    }

    public function clearChangeStatus()
    {
        unset($this->change_status);
    }

    /**
     * The ChangeStatus referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.ChangeStatus change_status = 37;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\ChangeStatus $var
     * @return $this
     */
    public function setChangeStatus($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\ChangeStatus::class);
        $this->change_status = $var;

        return $this;
    }

    /**
     * The CombinedAudience referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CombinedAudience combined_audience = 148;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\CombinedAudience|null
     */
    public function getCombinedAudience()
    {
        return $this->combined_audience;
    }

    public function hasCombinedAudience()
    {
        return isset($this->combined_audience);
    }

    public function clearCombinedAudience()
    {
        unset($this->combined_audience);
    }

    /**
     * The CombinedAudience referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CombinedAudience combined_audience = 148;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\CombinedAudience $var
     * @return $this
     */
    public function setCombinedAudience($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\CombinedAudience::class);
        $this->combined_audience = $var;

        return $this;
    }

    /**
     * The Audience referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.Audience audience = 190;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\Audience|null
     */
    public function getAudience()
    {
        return $this->audience;
    }

    public function hasAudience()
    {
        return isset($this->audience);
    }

    public function clearAudience()
    {
        unset($this->audience);
    }

    /**
     * The Audience referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.Audience audience = 190;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\Audience $var
     * @return $this
     */
    public function setAudience($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\Audience::class);
        $this->audience = $var;

        return $this;
    }

    /**
     * The conversion action referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.ConversionAction conversion_action = 103;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\ConversionAction|null
     */
    public function getConversionAction()
    {
        return $this->conversion_action;
    }

    public function hasConversionAction()
    {
        return isset($this->conversion_action);
    }

    public function clearConversionAction()
    {
        unset($this->conversion_action);
    }

    /**
     * The conversion action referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.ConversionAction conversion_action = 103;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\ConversionAction $var
     * @return $this
     */
    public function setConversionAction($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\ConversionAction::class);
        $this->conversion_action = $var;

        return $this;
    }

    /**
     * The conversion custom variable referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.ConversionCustomVariable conversion_custom_variable = 153;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\ConversionCustomVariable|null
     */
    public function getConversionCustomVariable()
    {
        return $this->conversion_custom_variable;
    }

    public function hasConversionCustomVariable()
    {
        return isset($this->conversion_custom_variable);
    }

    public function clearConversionCustomVariable()
    {
        unset($this->conversion_custom_variable);
    }

    /**
     * The conversion custom variable referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.ConversionCustomVariable conversion_custom_variable = 153;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\ConversionCustomVariable $var
     * @return $this
     */
    public function setConversionCustomVariable($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\ConversionCustomVariable::class);
        $this->conversion_custom_variable = $var;

        return $this;
    }

    /**
     * The ConversionGoalCampaignConfig referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.ConversionGoalCampaignConfig conversion_goal_campaign_config = 177;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\ConversionGoalCampaignConfig|null
     */
    public function getConversionGoalCampaignConfig()
    {
        return $this->conversion_goal_campaign_config;
    }

    public function hasConversionGoalCampaignConfig()
    {
        return isset($this->conversion_goal_campaign_config);
    }

    public function clearConversionGoalCampaignConfig()
    {
        unset($this->conversion_goal_campaign_config);
    }

    /**
     * The ConversionGoalCampaignConfig referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.ConversionGoalCampaignConfig conversion_goal_campaign_config = 177;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\ConversionGoalCampaignConfig $var
     * @return $this
     */
    public function setConversionGoalCampaignConfig($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\ConversionGoalCampaignConfig::class);
        $this->conversion_goal_campaign_config = $var;

        return $this;
    }

    /**
     * The conversion value rule referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.ConversionValueRule conversion_value_rule = 164;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\ConversionValueRule|null
     */
    public function getConversionValueRule()
    {
        return $this->conversion_value_rule;
    }

    public function hasConversionValueRule()
    {
        return isset($this->conversion_value_rule);
    }

    public function clearConversionValueRule()
    {
        unset($this->conversion_value_rule);
    }

    /**
     * The conversion value rule referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.ConversionValueRule conversion_value_rule = 164;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\ConversionValueRule $var
     * @return $this
     */
    public function setConversionValueRule($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\ConversionValueRule::class);
        $this->conversion_value_rule = $var;

        return $this;
    }

    /**
     * The conversion value rule set referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.ConversionValueRuleSet conversion_value_rule_set = 165;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\ConversionValueRuleSet|null
     */
    public function getConversionValueRuleSet()
    {
        return $this->conversion_value_rule_set;
    }

    public function hasConversionValueRuleSet()
    {
        return isset($this->conversion_value_rule_set);
    }

    public function clearConversionValueRuleSet()
    {
        unset($this->conversion_value_rule_set);
    }

    /**
     * The conversion value rule set referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.ConversionValueRuleSet conversion_value_rule_set = 165;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\ConversionValueRuleSet $var
     * @return $this
     */
    public function setConversionValueRuleSet($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\ConversionValueRuleSet::class);
        $this->conversion_value_rule_set = $var;

        return $this;
    }

    /**
     * The ClickView referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.ClickView click_view = 122;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\ClickView|null
     */
    public function getClickView()
    {
        return $this->click_view;
    }

    public function hasClickView()
    {
        return isset($this->click_view);
    }

    public function clearClickView()
    {
        unset($this->click_view);
    }

    /**
     * The ClickView referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.ClickView click_view = 122;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\ClickView $var
     * @return $this
     */
    public function setClickView($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\ClickView::class);
        $this->click_view = $var;

        return $this;
    }

    /**
     * The currency constant referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CurrencyConstant currency_constant = 134;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\CurrencyConstant|null
     */
    public function getCurrencyConstant()
    {
        return $this->currency_constant;
    }

    public function hasCurrencyConstant()
    {
        return isset($this->currency_constant);
    }

    public function clearCurrencyConstant()
    {
        unset($this->currency_constant);
    }

    /**
     * The currency constant referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CurrencyConstant currency_constant = 134;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\CurrencyConstant $var
     * @return $this
     */
    public function setCurrencyConstant($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\CurrencyConstant::class);
        $this->currency_constant = $var;

        return $this;
    }

    /**
     * The CustomAudience referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CustomAudience custom_audience = 147;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\CustomAudience|null
     */
    public function getCustomAudience()
    {
        return $this->custom_audience;
    }

    public function hasCustomAudience()
    {
        return isset($this->custom_audience);
    }

    public function clearCustomAudience()
    {
        unset($this->custom_audience);
    }

    /**
     * The CustomAudience referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CustomAudience custom_audience = 147;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\CustomAudience $var
     * @return $this
     */
    public function setCustomAudience($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\CustomAudience::class);
        $this->custom_audience = $var;

        return $this;
    }

    /**
     * The CustomConversionGoal referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CustomConversionGoal custom_conversion_goal = 176;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\CustomConversionGoal|null
     */
    public function getCustomConversionGoal()
    {
        return $this->custom_conversion_goal;
    }

    public function hasCustomConversionGoal()
    {
        return isset($this->custom_conversion_goal);
    }

    public function clearCustomConversionGoal()
    {
        unset($this->custom_conversion_goal);
    }

    /**
     * The CustomConversionGoal referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CustomConversionGoal custom_conversion_goal = 176;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\CustomConversionGoal $var
     * @return $this
     */
    public function setCustomConversionGoal($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\CustomConversionGoal::class);
        $this->custom_conversion_goal = $var;

        return $this;
    }

    /**
     * The CustomInterest referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CustomInterest custom_interest = 104;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\CustomInterest|null
     */
    public function getCustomInterest()
    {
        return $this->custom_interest;
    }

    public function hasCustomInterest()
    {
        return isset($this->custom_interest);
    }

    public function clearCustomInterest()
    {
        unset($this->custom_interest);
    }

    /**
     * The CustomInterest referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CustomInterest custom_interest = 104;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\CustomInterest $var
     * @return $this
     */
    public function setCustomInterest($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\CustomInterest::class);
        $this->custom_interest = $var;

        return $this;
    }

    /**
     * The customer referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.Customer customer = 1;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\Customer|null
     */
    public function getCustomer()
    {
        return $this->customer;
    }

    public function hasCustomer()
    {
        return isset($this->customer);
    }

    public function clearCustomer()
    {
        unset($this->customer);
    }

    /**
     * The customer referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.Customer customer = 1;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\Customer $var
     * @return $this
     */
    public function setCustomer($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\Customer::class);
        $this->customer = $var;

        return $this;
    }

    /**
     * The customer asset referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CustomerAsset customer_asset = 155;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\CustomerAsset|null
     */
    public function getCustomerAsset()
    {
        return $this->customer_asset;
    }

    public function hasCustomerAsset()
    {
        return isset($this->customer_asset);
    }

    public function clearCustomerAsset()
    {
        unset($this->customer_asset);
    }

    /**
     * The customer asset referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CustomerAsset customer_asset = 155;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\CustomerAsset $var
     * @return $this
     */
    public function setCustomerAsset($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\CustomerAsset::class);
        $this->customer_asset = $var;

        return $this;
    }

    /**
     * The customer asset set referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CustomerAssetSet customer_asset_set = 195;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\CustomerAssetSet|null
     */
    public function getCustomerAssetSet()
    {
        return $this->customer_asset_set;
    }

    public function hasCustomerAssetSet()
    {
        return isset($this->customer_asset_set);
    }

    public function clearCustomerAssetSet()
    {
        unset($this->customer_asset_set);
    }

    /**
     * The customer asset set referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CustomerAssetSet customer_asset_set = 195;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\CustomerAssetSet $var
     * @return $this
     */
    public function setCustomerAssetSet($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\CustomerAssetSet::class);
        $this->customer_asset_set = $var;

        return $this;
    }

    /**
     * The accessible bidding strategy referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AccessibleBiddingStrategy accessible_bidding_strategy = 169;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\AccessibleBiddingStrategy|null
     */
    public function getAccessibleBiddingStrategy()
    {
        return $this->accessible_bidding_strategy;
    }

    public function hasAccessibleBiddingStrategy()
    {
        return isset($this->accessible_bidding_strategy);
    }

    public function clearAccessibleBiddingStrategy()
    {
        unset($this->accessible_bidding_strategy);
    }

    /**
     * The accessible bidding strategy referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AccessibleBiddingStrategy accessible_bidding_strategy = 169;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\AccessibleBiddingStrategy $var
     * @return $this
     */
    public function setAccessibleBiddingStrategy($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\AccessibleBiddingStrategy::class);
        $this->accessible_bidding_strategy = $var;

        return $this;
    }

    /**
     * The customer customizer referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CustomerCustomizer customer_customizer = 184;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\CustomerCustomizer|null
     */
    public function getCustomerCustomizer()
    {
        return $this->customer_customizer;
    }

    public function hasCustomerCustomizer()
    {
        return isset($this->customer_customizer);
    }

    public function clearCustomerCustomizer()
    {
        unset($this->customer_customizer);
    }

    /**
     * The customer customizer referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CustomerCustomizer customer_customizer = 184;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\CustomerCustomizer $var
     * @return $this
     */
    public function setCustomerCustomizer($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\CustomerCustomizer::class);
        $this->customer_customizer = $var;

        return $this;
    }

    /**
     * The CustomerManagerLink referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CustomerManagerLink customer_manager_link = 61;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\CustomerManagerLink|null
     */
    public function getCustomerManagerLink()
    {
        return $this->customer_manager_link;
    }

    public function hasCustomerManagerLink()
    {
        return isset($this->customer_manager_link);
    }

    public function clearCustomerManagerLink()
    {
        unset($this->customer_manager_link);
    }

    /**
     * The CustomerManagerLink referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CustomerManagerLink customer_manager_link = 61;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\CustomerManagerLink $var
     * @return $this
     */
    public function setCustomerManagerLink($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\CustomerManagerLink::class);
        $this->customer_manager_link = $var;

        return $this;
    }

    /**
     * The CustomerClientLink referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CustomerClientLink customer_client_link = 62;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\CustomerClientLink|null
     */
    public function getCustomerClientLink()
    {
        return $this->customer_client_link;
    }

    public function hasCustomerClientLink()
    {
        return isset($this->customer_client_link);
    }

    public function clearCustomerClientLink()
    {
        unset($this->customer_client_link);
    }

    /**
     * The CustomerClientLink referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CustomerClientLink customer_client_link = 62;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\CustomerClientLink $var
     * @return $this
     */
    public function setCustomerClientLink($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\CustomerClientLink::class);
        $this->customer_client_link = $var;

        return $this;
    }

    /**
     * The CustomerClient referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CustomerClient customer_client = 70;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\CustomerClient|null
     */
    public function getCustomerClient()
    {
        return $this->customer_client;
    }

    public function hasCustomerClient()
    {
        return isset($this->customer_client);
    }

    public function clearCustomerClient()
    {
        unset($this->customer_client);
    }

    /**
     * The CustomerClient referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CustomerClient customer_client = 70;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\CustomerClient $var
     * @return $this
     */
    public function setCustomerClient($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\CustomerClient::class);
        $this->customer_client = $var;

        return $this;
    }

    /**
     * The CustomerConversionGoal referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CustomerConversionGoal customer_conversion_goal = 174;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\CustomerConversionGoal|null
     */
    public function getCustomerConversionGoal()
    {
        return $this->customer_conversion_goal;
    }

    public function hasCustomerConversionGoal()
    {
        return isset($this->customer_conversion_goal);
    }

    public function clearCustomerConversionGoal()
    {
        unset($this->customer_conversion_goal);
    }

    /**
     * The CustomerConversionGoal referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CustomerConversionGoal customer_conversion_goal = 174;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\CustomerConversionGoal $var
     * @return $this
     */
    public function setCustomerConversionGoal($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\CustomerConversionGoal::class);
        $this->customer_conversion_goal = $var;

        return $this;
    }

    /**
     * The customer extension setting referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CustomerExtensionSetting customer_extension_setting = 114;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\CustomerExtensionSetting|null
     */
    public function getCustomerExtensionSetting()
    {
        return $this->customer_extension_setting;
    }

    public function hasCustomerExtensionSetting()
    {
        return isset($this->customer_extension_setting);
    }

    public function clearCustomerExtensionSetting()
    {
        unset($this->customer_extension_setting);
    }

    /**
     * The customer extension setting referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CustomerExtensionSetting customer_extension_setting = 114;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\CustomerExtensionSetting $var
     * @return $this
     */
    public function setCustomerExtensionSetting($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\CustomerExtensionSetting::class);
        $this->customer_extension_setting = $var;

        return $this;
    }

    /**
     * The customer feed referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CustomerFeed customer_feed = 64;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\CustomerFeed|null
     */
    public function getCustomerFeed()
    {
        return $this->customer_feed;
    }

    public function hasCustomerFeed()
    {
        return isset($this->customer_feed);
    }

    public function clearCustomerFeed()
    {
        unset($this->customer_feed);
    }

    /**
     * The customer feed referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CustomerFeed customer_feed = 64;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\CustomerFeed $var
     * @return $this
     */
    public function setCustomerFeed($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\CustomerFeed::class);
        $this->customer_feed = $var;

        return $this;
    }

    /**
     * The customer label referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CustomerLabel customer_label = 124;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\CustomerLabel|null
     */
    public function getCustomerLabel()
    {
        return $this->customer_label;
    }

    public function hasCustomerLabel()
    {
        return isset($this->customer_label);
    }

    public function clearCustomerLabel()
    {
        unset($this->customer_label);
    }

    /**
     * The customer label referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CustomerLabel customer_label = 124;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\CustomerLabel $var
     * @return $this
     */
    public function setCustomerLabel($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\CustomerLabel::class);
        $this->customer_label = $var;

        return $this;
    }

    /**
     * The customer lifecycle goal referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CustomerLifecycleGoal customer_lifecycle_goal = 212;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\CustomerLifecycleGoal|null
     */
    public function getCustomerLifecycleGoal()
    {
        return $this->customer_lifecycle_goal;
    }

    public function hasCustomerLifecycleGoal()
    {
        return isset($this->customer_lifecycle_goal);
    }

    public function clearCustomerLifecycleGoal()
    {
        unset($this->customer_lifecycle_goal);
    }

    /**
     * The customer lifecycle goal referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CustomerLifecycleGoal customer_lifecycle_goal = 212;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\CustomerLifecycleGoal $var
     * @return $this
     */
    public function setCustomerLifecycleGoal($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\CustomerLifecycleGoal::class);
        $this->customer_lifecycle_goal = $var;

        return $this;
    }

    /**
     * The customer negative criterion referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CustomerNegativeCriterion customer_negative_criterion = 88;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\CustomerNegativeCriterion|null
     */
    public function getCustomerNegativeCriterion()
    {
        return $this->customer_negative_criterion;
    }

    public function hasCustomerNegativeCriterion()
    {
        return isset($this->customer_negative_criterion);
    }

    public function clearCustomerNegativeCriterion()
    {
        unset($this->customer_negative_criterion);
    }

    /**
     * The customer negative criterion referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CustomerNegativeCriterion customer_negative_criterion = 88;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\CustomerNegativeCriterion $var
     * @return $this
     */
    public function setCustomerNegativeCriterion($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\CustomerNegativeCriterion::class);
        $this->customer_negative_criterion = $var;

        return $this;
    }

    /**
     * The customer search term insight referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CustomerSearchTermInsight customer_search_term_insight = 205;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\CustomerSearchTermInsight|null
     */
    public function getCustomerSearchTermInsight()
    {
        return $this->customer_search_term_insight;
    }

    public function hasCustomerSearchTermInsight()
    {
        return isset($this->customer_search_term_insight);
    }

    public function clearCustomerSearchTermInsight()
    {
        unset($this->customer_search_term_insight);
    }

    /**
     * The customer search term insight referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CustomerSearchTermInsight customer_search_term_insight = 205;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\CustomerSearchTermInsight $var
     * @return $this
     */
    public function setCustomerSearchTermInsight($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\CustomerSearchTermInsight::class);
        $this->customer_search_term_insight = $var;

        return $this;
    }

    /**
     * The CustomerUserAccess referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CustomerUserAccess customer_user_access = 146;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\CustomerUserAccess|null
     */
    public function getCustomerUserAccess()
    {
        return $this->customer_user_access;
    }

    public function hasCustomerUserAccess()
    {
        return isset($this->customer_user_access);
    }

    public function clearCustomerUserAccess()
    {
        unset($this->customer_user_access);
    }

    /**
     * The CustomerUserAccess referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CustomerUserAccess customer_user_access = 146;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\CustomerUserAccess $var
     * @return $this
     */
    public function setCustomerUserAccess($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\CustomerUserAccess::class);
        $this->customer_user_access = $var;

        return $this;
    }

    /**
     * The CustomerUserAccessInvitation referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CustomerUserAccessInvitation customer_user_access_invitation = 150;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\CustomerUserAccessInvitation|null
     */
    public function getCustomerUserAccessInvitation()
    {
        return $this->customer_user_access_invitation;
    }

    public function hasCustomerUserAccessInvitation()
    {
        return isset($this->customer_user_access_invitation);
    }

    public function clearCustomerUserAccessInvitation()
    {
        unset($this->customer_user_access_invitation);
    }

    /**
     * The CustomerUserAccessInvitation referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CustomerUserAccessInvitation customer_user_access_invitation = 150;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\CustomerUserAccessInvitation $var
     * @return $this
     */
    public function setCustomerUserAccessInvitation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\CustomerUserAccessInvitation::class);
        $this->customer_user_access_invitation = $var;

        return $this;
    }

    /**
     * The customizer attribute referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CustomizerAttribute customizer_attribute = 178;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\CustomizerAttribute|null
     */
    public function getCustomizerAttribute()
    {
        return $this->customizer_attribute;
    }

    public function hasCustomizerAttribute()
    {
        return isset($this->customizer_attribute);
    }

    public function clearCustomizerAttribute()
    {
        unset($this->customizer_attribute);
    }

    /**
     * The customizer attribute referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.CustomizerAttribute customizer_attribute = 178;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\CustomizerAttribute $var
     * @return $this
     */
    public function setCustomizerAttribute($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\CustomizerAttribute::class);
        $this->customizer_attribute = $var;

        return $this;
    }

    /**
     * The detail placement view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.DetailPlacementView detail_placement_view = 118;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\DetailPlacementView|null
     */
    public function getDetailPlacementView()
    {
        return $this->detail_placement_view;
    }

    public function hasDetailPlacementView()
    {
        return isset($this->detail_placement_view);
    }

    public function clearDetailPlacementView()
    {
        unset($this->detail_placement_view);
    }

    /**
     * The detail placement view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.DetailPlacementView detail_placement_view = 118;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\DetailPlacementView $var
     * @return $this
     */
    public function setDetailPlacementView($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\DetailPlacementView::class);
        $this->detail_placement_view = $var;

        return $this;
    }

    /**
     * The detailed demographic referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.DetailedDemographic detailed_demographic = 166;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\DetailedDemographic|null
     */
    public function getDetailedDemographic()
    {
        return $this->detailed_demographic;
    }

    public function hasDetailedDemographic()
    {
        return isset($this->detailed_demographic);
    }

    public function clearDetailedDemographic()
    {
        unset($this->detailed_demographic);
    }

    /**
     * The detailed demographic referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.DetailedDemographic detailed_demographic = 166;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\DetailedDemographic $var
     * @return $this
     */
    public function setDetailedDemographic($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\DetailedDemographic::class);
        $this->detailed_demographic = $var;

        return $this;
    }

    /**
     * The display keyword view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.DisplayKeywordView display_keyword_view = 47;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\DisplayKeywordView|null
     */
    public function getDisplayKeywordView()
    {
        return $this->display_keyword_view;
    }

    public function hasDisplayKeywordView()
    {
        return isset($this->display_keyword_view);
    }

    public function clearDisplayKeywordView()
    {
        unset($this->display_keyword_view);
    }

    /**
     * The display keyword view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.DisplayKeywordView display_keyword_view = 47;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\DisplayKeywordView $var
     * @return $this
     */
    public function setDisplayKeywordView($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\DisplayKeywordView::class);
        $this->display_keyword_view = $var;

        return $this;
    }

    /**
     * The distance view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.DistanceView distance_view = 132;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\DistanceView|null
     */
    public function getDistanceView()
    {
        return $this->distance_view;
    }

    public function hasDistanceView()
    {
        return isset($this->distance_view);
    }

    public function clearDistanceView()
    {
        unset($this->distance_view);
    }

    /**
     * The distance view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.DistanceView distance_view = 132;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\DistanceView $var
     * @return $this
     */
    public function setDistanceView($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\DistanceView::class);
        $this->distance_view = $var;

        return $this;
    }

    /**
     * The dynamic search ads search term view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.DynamicSearchAdsSearchTermView dynamic_search_ads_search_term_view = 106;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\DynamicSearchAdsSearchTermView|null
     */
    public function getDynamicSearchAdsSearchTermView()
    {
        return $this->dynamic_search_ads_search_term_view;
    }

    public function hasDynamicSearchAdsSearchTermView()
    {
        return isset($this->dynamic_search_ads_search_term_view);
    }

    public function clearDynamicSearchAdsSearchTermView()
    {
        unset($this->dynamic_search_ads_search_term_view);
    }

    /**
     * The dynamic search ads search term view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.DynamicSearchAdsSearchTermView dynamic_search_ads_search_term_view = 106;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\DynamicSearchAdsSearchTermView $var
     * @return $this
     */
    public function setDynamicSearchAdsSearchTermView($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\DynamicSearchAdsSearchTermView::class);
        $this->dynamic_search_ads_search_term_view = $var;

        return $this;
    }

    /**
     * The expanded landing page view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.ExpandedLandingPageView expanded_landing_page_view = 128;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\ExpandedLandingPageView|null
     */
    public function getExpandedLandingPageView()
    {
        return $this->expanded_landing_page_view;
    }

    public function hasExpandedLandingPageView()
    {
        return isset($this->expanded_landing_page_view);
    }

    public function clearExpandedLandingPageView()
    {
        unset($this->expanded_landing_page_view);
    }

    /**
     * The expanded landing page view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.ExpandedLandingPageView expanded_landing_page_view = 128;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\ExpandedLandingPageView $var
     * @return $this
     */
    public function setExpandedLandingPageView($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\ExpandedLandingPageView::class);
        $this->expanded_landing_page_view = $var;

        return $this;
    }

    /**
     * The extension feed item referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.ExtensionFeedItem extension_feed_item = 85;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\ExtensionFeedItem|null
     */
    public function getExtensionFeedItem()
    {
        return $this->extension_feed_item;
    }

    public function hasExtensionFeedItem()
    {
        return isset($this->extension_feed_item);
    }

    public function clearExtensionFeedItem()
    {
        unset($this->extension_feed_item);
    }

    /**
     * The extension feed item referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.ExtensionFeedItem extension_feed_item = 85;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\ExtensionFeedItem $var
     * @return $this
     */
    public function setExtensionFeedItem($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\ExtensionFeedItem::class);
        $this->extension_feed_item = $var;

        return $this;
    }

    /**
     * The feed referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.Feed feed = 46;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\Feed|null
     */
    public function getFeed()
    {
        return $this->feed;
    }

    public function hasFeed()
    {
        return isset($this->feed);
    }

    public function clearFeed()
    {
        unset($this->feed);
    }

    /**
     * The feed referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.Feed feed = 46;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\Feed $var
     * @return $this
     */
    public function setFeed($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\Feed::class);
        $this->feed = $var;

        return $this;
    }

    /**
     * The feed item referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.FeedItem feed_item = 50;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\FeedItem|null
     */
    public function getFeedItem()
    {
        return $this->feed_item;
    }

    public function hasFeedItem()
    {
        return isset($this->feed_item);
    }

    public function clearFeedItem()
    {
        unset($this->feed_item);
    }

    /**
     * The feed item referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.FeedItem feed_item = 50;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\FeedItem $var
     * @return $this
     */
    public function setFeedItem($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\FeedItem::class);
        $this->feed_item = $var;

        return $this;
    }

    /**
     * The feed item set referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.FeedItemSet feed_item_set = 149;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\FeedItemSet|null
     */
    public function getFeedItemSet()
    {
        return $this->feed_item_set;
    }

    public function hasFeedItemSet()
    {
        return isset($this->feed_item_set);
    }

    public function clearFeedItemSet()
    {
        unset($this->feed_item_set);
    }

    /**
     * The feed item set referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.FeedItemSet feed_item_set = 149;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\FeedItemSet $var
     * @return $this
     */
    public function setFeedItemSet($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\FeedItemSet::class);
        $this->feed_item_set = $var;

        return $this;
    }

    /**
     * The feed item set link referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.FeedItemSetLink feed_item_set_link = 151;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\FeedItemSetLink|null
     */
    public function getFeedItemSetLink()
    {
        return $this->feed_item_set_link;
    }

    public function hasFeedItemSetLink()
    {
        return isset($this->feed_item_set_link);
    }

    public function clearFeedItemSetLink()
    {
        unset($this->feed_item_set_link);
    }

    /**
     * The feed item set link referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.FeedItemSetLink feed_item_set_link = 151;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\FeedItemSetLink $var
     * @return $this
     */
    public function setFeedItemSetLink($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\FeedItemSetLink::class);
        $this->feed_item_set_link = $var;

        return $this;
    }

    /**
     * The feed item target referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.FeedItemTarget feed_item_target = 116;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\FeedItemTarget|null
     */
    public function getFeedItemTarget()
    {
        return $this->feed_item_target;
    }

    public function hasFeedItemTarget()
    {
        return isset($this->feed_item_target);
    }

    public function clearFeedItemTarget()
    {
        unset($this->feed_item_target);
    }

    /**
     * The feed item target referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.FeedItemTarget feed_item_target = 116;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\FeedItemTarget $var
     * @return $this
     */
    public function setFeedItemTarget($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\FeedItemTarget::class);
        $this->feed_item_target = $var;

        return $this;
    }

    /**
     * The feed mapping referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.FeedMapping feed_mapping = 58;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\FeedMapping|null
     */
    public function getFeedMapping()
    {
        return $this->feed_mapping;
    }

    public function hasFeedMapping()
    {
        return isset($this->feed_mapping);
    }

    public function clearFeedMapping()
    {
        unset($this->feed_mapping);
    }

    /**
     * The feed mapping referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.FeedMapping feed_mapping = 58;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\FeedMapping $var
     * @return $this
     */
    public function setFeedMapping($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\FeedMapping::class);
        $this->feed_mapping = $var;

        return $this;
    }

    /**
     * The feed placeholder view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.FeedPlaceholderView feed_placeholder_view = 97;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\FeedPlaceholderView|null
     */
    public function getFeedPlaceholderView()
    {
        return $this->feed_placeholder_view;
    }

    public function hasFeedPlaceholderView()
    {
        return isset($this->feed_placeholder_view);
    }

    public function clearFeedPlaceholderView()
    {
        unset($this->feed_placeholder_view);
    }

    /**
     * The feed placeholder view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.FeedPlaceholderView feed_placeholder_view = 97;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\FeedPlaceholderView $var
     * @return $this
     */
    public function setFeedPlaceholderView($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\FeedPlaceholderView::class);
        $this->feed_placeholder_view = $var;

        return $this;
    }

    /**
     * The gender view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.GenderView gender_view = 40;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\GenderView|null
     */
    public function getGenderView()
    {
        return $this->gender_view;
    }

    public function hasGenderView()
    {
        return isset($this->gender_view);
    }

    public function clearGenderView()
    {
        unset($this->gender_view);
    }

    /**
     * The gender view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.GenderView gender_view = 40;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\GenderView $var
     * @return $this
     */
    public function setGenderView($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\GenderView::class);
        $this->gender_view = $var;

        return $this;
    }

    /**
     * The geo target constant referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.GeoTargetConstant geo_target_constant = 23;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\GeoTargetConstant|null
     */
    public function getGeoTargetConstant()
    {
        return $this->geo_target_constant;
    }

    public function hasGeoTargetConstant()
    {
        return isset($this->geo_target_constant);
    }

    public function clearGeoTargetConstant()
    {
        unset($this->geo_target_constant);
    }

    /**
     * The geo target constant referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.GeoTargetConstant geo_target_constant = 23;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\GeoTargetConstant $var
     * @return $this
     */
    public function setGeoTargetConstant($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\GeoTargetConstant::class);
        $this->geo_target_constant = $var;

        return $this;
    }

    /**
     * The geographic view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.GeographicView geographic_view = 125;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\GeographicView|null
     */
    public function getGeographicView()
    {
        return $this->geographic_view;
    }

    public function hasGeographicView()
    {
        return isset($this->geographic_view);
    }

    public function clearGeographicView()
    {
        unset($this->geographic_view);
    }

    /**
     * The geographic view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.GeographicView geographic_view = 125;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\GeographicView $var
     * @return $this
     */
    public function setGeographicView($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\GeographicView::class);
        $this->geographic_view = $var;

        return $this;
    }

    /**
     * The group placement view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.GroupPlacementView group_placement_view = 119;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\GroupPlacementView|null
     */
    public function getGroupPlacementView()
    {
        return $this->group_placement_view;
    }

    public function hasGroupPlacementView()
    {
        return isset($this->group_placement_view);
    }

    public function clearGroupPlacementView()
    {
        unset($this->group_placement_view);
    }

    /**
     * The group placement view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.GroupPlacementView group_placement_view = 119;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\GroupPlacementView $var
     * @return $this
     */
    public function setGroupPlacementView($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\GroupPlacementView::class);
        $this->group_placement_view = $var;

        return $this;
    }

    /**
     * The hotel group view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.HotelGroupView hotel_group_view = 51;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\HotelGroupView|null
     */
    public function getHotelGroupView()
    {
        return $this->hotel_group_view;
    }

    public function hasHotelGroupView()
    {
        return isset($this->hotel_group_view);
    }

    public function clearHotelGroupView()
    {
        unset($this->hotel_group_view);
    }

    /**
     * The hotel group view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.HotelGroupView hotel_group_view = 51;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\HotelGroupView $var
     * @return $this
     */
    public function setHotelGroupView($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\HotelGroupView::class);
        $this->hotel_group_view = $var;

        return $this;
    }

    /**
     * The hotel performance view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.HotelPerformanceView hotel_performance_view = 71;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\HotelPerformanceView|null
     */
    public function getHotelPerformanceView()
    {
        return $this->hotel_performance_view;
    }

    public function hasHotelPerformanceView()
    {
        return isset($this->hotel_performance_view);
    }

    public function clearHotelPerformanceView()
    {
        unset($this->hotel_performance_view);
    }

    /**
     * The hotel performance view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.HotelPerformanceView hotel_performance_view = 71;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\HotelPerformanceView $var
     * @return $this
     */
    public function setHotelPerformanceView($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\HotelPerformanceView::class);
        $this->hotel_performance_view = $var;

        return $this;
    }

    /**
     * The hotel reconciliation referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.HotelReconciliation hotel_reconciliation = 188;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\HotelReconciliation|null
     */
    public function getHotelReconciliation()
    {
        return $this->hotel_reconciliation;
    }

    public function hasHotelReconciliation()
    {
        return isset($this->hotel_reconciliation);
    }

    public function clearHotelReconciliation()
    {
        unset($this->hotel_reconciliation);
    }

    /**
     * The hotel reconciliation referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.HotelReconciliation hotel_reconciliation = 188;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\HotelReconciliation $var
     * @return $this
     */
    public function setHotelReconciliation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\HotelReconciliation::class);
        $this->hotel_reconciliation = $var;

        return $this;
    }

    /**
     * The income range view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.IncomeRangeView income_range_view = 138;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\IncomeRangeView|null
     */
    public function getIncomeRangeView()
    {
        return $this->income_range_view;
    }

    public function hasIncomeRangeView()
    {
        return isset($this->income_range_view);
    }

    public function clearIncomeRangeView()
    {
        unset($this->income_range_view);
    }

    /**
     * The income range view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.IncomeRangeView income_range_view = 138;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\IncomeRangeView $var
     * @return $this
     */
    public function setIncomeRangeView($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\IncomeRangeView::class);
        $this->income_range_view = $var;

        return $this;
    }

    /**
     * The keyword view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.KeywordView keyword_view = 21;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\KeywordView|null
     */
    public function getKeywordView()
    {
        return $this->keyword_view;
    }

    public function hasKeywordView()
    {
        return isset($this->keyword_view);
    }

    public function clearKeywordView()
    {
        unset($this->keyword_view);
    }

    /**
     * The keyword view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.KeywordView keyword_view = 21;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\KeywordView $var
     * @return $this
     */
    public function setKeywordView($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\KeywordView::class);
        $this->keyword_view = $var;

        return $this;
    }

    /**
     * The keyword plan referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.KeywordPlan keyword_plan = 32;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\KeywordPlan|null
     */
    public function getKeywordPlan()
    {
        return $this->keyword_plan;
    }

    public function hasKeywordPlan()
    {
        return isset($this->keyword_plan);
    }

    public function clearKeywordPlan()
    {
        unset($this->keyword_plan);
    }

    /**
     * The keyword plan referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.KeywordPlan keyword_plan = 32;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\KeywordPlan $var
     * @return $this
     */
    public function setKeywordPlan($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\KeywordPlan::class);
        $this->keyword_plan = $var;

        return $this;
    }

    /**
     * The keyword plan campaign referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.KeywordPlanCampaign keyword_plan_campaign = 33;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\KeywordPlanCampaign|null
     */
    public function getKeywordPlanCampaign()
    {
        return $this->keyword_plan_campaign;
    }

    public function hasKeywordPlanCampaign()
    {
        return isset($this->keyword_plan_campaign);
    }

    public function clearKeywordPlanCampaign()
    {
        unset($this->keyword_plan_campaign);
    }

    /**
     * The keyword plan campaign referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.KeywordPlanCampaign keyword_plan_campaign = 33;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\KeywordPlanCampaign $var
     * @return $this
     */
    public function setKeywordPlanCampaign($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\KeywordPlanCampaign::class);
        $this->keyword_plan_campaign = $var;

        return $this;
    }

    /**
     * The keyword plan campaign keyword referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.KeywordPlanCampaignKeyword keyword_plan_campaign_keyword = 140;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\KeywordPlanCampaignKeyword|null
     */
    public function getKeywordPlanCampaignKeyword()
    {
        return $this->keyword_plan_campaign_keyword;
    }

    public function hasKeywordPlanCampaignKeyword()
    {
        return isset($this->keyword_plan_campaign_keyword);
    }

    public function clearKeywordPlanCampaignKeyword()
    {
        unset($this->keyword_plan_campaign_keyword);
    }

    /**
     * The keyword plan campaign keyword referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.KeywordPlanCampaignKeyword keyword_plan_campaign_keyword = 140;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\KeywordPlanCampaignKeyword $var
     * @return $this
     */
    public function setKeywordPlanCampaignKeyword($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\KeywordPlanCampaignKeyword::class);
        $this->keyword_plan_campaign_keyword = $var;

        return $this;
    }

    /**
     * The keyword plan ad group referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.KeywordPlanAdGroup keyword_plan_ad_group = 35;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\KeywordPlanAdGroup|null
     */
    public function getKeywordPlanAdGroup()
    {
        return $this->keyword_plan_ad_group;
    }

    public function hasKeywordPlanAdGroup()
    {
        return isset($this->keyword_plan_ad_group);
    }

    public function clearKeywordPlanAdGroup()
    {
        unset($this->keyword_plan_ad_group);
    }

    /**
     * The keyword plan ad group referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.KeywordPlanAdGroup keyword_plan_ad_group = 35;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\KeywordPlanAdGroup $var
     * @return $this
     */
    public function setKeywordPlanAdGroup($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\KeywordPlanAdGroup::class);
        $this->keyword_plan_ad_group = $var;

        return $this;
    }

    /**
     * The keyword plan ad group referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.KeywordPlanAdGroupKeyword keyword_plan_ad_group_keyword = 141;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\KeywordPlanAdGroupKeyword|null
     */
    public function getKeywordPlanAdGroupKeyword()
    {
        return $this->keyword_plan_ad_group_keyword;
    }

    public function hasKeywordPlanAdGroupKeyword()
    {
        return isset($this->keyword_plan_ad_group_keyword);
    }

    public function clearKeywordPlanAdGroupKeyword()
    {
        unset($this->keyword_plan_ad_group_keyword);
    }

    /**
     * The keyword plan ad group referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.KeywordPlanAdGroupKeyword keyword_plan_ad_group_keyword = 141;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\KeywordPlanAdGroupKeyword $var
     * @return $this
     */
    public function setKeywordPlanAdGroupKeyword($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\KeywordPlanAdGroupKeyword::class);
        $this->keyword_plan_ad_group_keyword = $var;

        return $this;
    }

    /**
     * The keyword theme constant referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.KeywordThemeConstant keyword_theme_constant = 163;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\KeywordThemeConstant|null
     */
    public function getKeywordThemeConstant()
    {
        return $this->keyword_theme_constant;
    }

    public function hasKeywordThemeConstant()
    {
        return isset($this->keyword_theme_constant);
    }

    public function clearKeywordThemeConstant()
    {
        unset($this->keyword_theme_constant);
    }

    /**
     * The keyword theme constant referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.KeywordThemeConstant keyword_theme_constant = 163;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\KeywordThemeConstant $var
     * @return $this
     */
    public function setKeywordThemeConstant($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\KeywordThemeConstant::class);
        $this->keyword_theme_constant = $var;

        return $this;
    }

    /**
     * The label referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.Label label = 52;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\Label|null
     */
    public function getLabel()
    {
        return $this->label;
    }

    public function hasLabel()
    {
        return isset($this->label);
    }

    public function clearLabel()
    {
        unset($this->label);
    }

    /**
     * The label referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.Label label = 52;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\Label $var
     * @return $this
     */
    public function setLabel($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\Label::class);
        $this->label = $var;

        return $this;
    }

    /**
     * The landing page view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.LandingPageView landing_page_view = 126;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\LandingPageView|null
     */
    public function getLandingPageView()
    {
        return $this->landing_page_view;
    }

    public function hasLandingPageView()
    {
        return isset($this->landing_page_view);
    }

    public function clearLandingPageView()
    {
        unset($this->landing_page_view);
    }

    /**
     * The landing page view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.LandingPageView landing_page_view = 126;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\LandingPageView $var
     * @return $this
     */
    public function setLandingPageView($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\LandingPageView::class);
        $this->landing_page_view = $var;

        return $this;
    }

    /**
     * The language constant referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.LanguageConstant language_constant = 55;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\LanguageConstant|null
     */
    public function getLanguageConstant()
    {
        return $this->language_constant;
    }

    public function hasLanguageConstant()
    {
        return isset($this->language_constant);
    }

    public function clearLanguageConstant()
    {
        unset($this->language_constant);
    }

    /**
     * The language constant referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.LanguageConstant language_constant = 55;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\LanguageConstant $var
     * @return $this
     */
    public function setLanguageConstant($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\LanguageConstant::class);
        $this->language_constant = $var;

        return $this;
    }

    /**
     * The location view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.LocationView location_view = 123;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\LocationView|null
     */
    public function getLocationView()
    {
        return $this->location_view;
    }

    public function hasLocationView()
    {
        return isset($this->location_view);
    }

    public function clearLocationView()
    {
        unset($this->location_view);
    }

    /**
     * The location view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.LocationView location_view = 123;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\LocationView $var
     * @return $this
     */
    public function setLocationView($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\LocationView::class);
        $this->location_view = $var;

        return $this;
    }

    /**
     * The managed placement view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.ManagedPlacementView managed_placement_view = 53;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\ManagedPlacementView|null
     */
    public function getManagedPlacementView()
    {
        return $this->managed_placement_view;
    }

    public function hasManagedPlacementView()
    {
        return isset($this->managed_placement_view);
    }

    public function clearManagedPlacementView()
    {
        unset($this->managed_placement_view);
    }

    /**
     * The managed placement view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.ManagedPlacementView managed_placement_view = 53;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\ManagedPlacementView $var
     * @return $this
     */
    public function setManagedPlacementView($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\ManagedPlacementView::class);
        $this->managed_placement_view = $var;

        return $this;
    }

    /**
     * The media file referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.MediaFile media_file = 90;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\MediaFile|null
     */
    public function getMediaFile()
    {
        return $this->media_file;
    }

    public function hasMediaFile()
    {
        return isset($this->media_file);
    }

    public function clearMediaFile()
    {
        unset($this->media_file);
    }

    /**
     * The media file referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.MediaFile media_file = 90;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\MediaFile $var
     * @return $this
     */
    public function setMediaFile($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\MediaFile::class);
        $this->media_file = $var;

        return $this;
    }

    /**
     * The local services employee referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.LocalServicesEmployee local_services_employee = 223;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\LocalServicesEmployee|null
     */
    public function getLocalServicesEmployee()
    {
        return $this->local_services_employee;
    }

    public function hasLocalServicesEmployee()
    {
        return isset($this->local_services_employee);
    }

    public function clearLocalServicesEmployee()
    {
        unset($this->local_services_employee);
    }

    /**
     * The local services employee referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.LocalServicesEmployee local_services_employee = 223;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\LocalServicesEmployee $var
     * @return $this
     */
    public function setLocalServicesEmployee($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\LocalServicesEmployee::class);
        $this->local_services_employee = $var;

        return $this;
    }

    /**
     * The local services verification artifact referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.LocalServicesVerificationArtifact local_services_verification_artifact = 211;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\LocalServicesVerificationArtifact|null
     */
    public function getLocalServicesVerificationArtifact()
    {
        return $this->local_services_verification_artifact;
    }

    public function hasLocalServicesVerificationArtifact()
    {
        return isset($this->local_services_verification_artifact);
    }

    public function clearLocalServicesVerificationArtifact()
    {
        unset($this->local_services_verification_artifact);
    }

    /**
     * The local services verification artifact referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.LocalServicesVerificationArtifact local_services_verification_artifact = 211;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\LocalServicesVerificationArtifact $var
     * @return $this
     */
    public function setLocalServicesVerificationArtifact($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\LocalServicesVerificationArtifact::class);
        $this->local_services_verification_artifact = $var;

        return $this;
    }

    /**
     * The mobile app category constant referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.MobileAppCategoryConstant mobile_app_category_constant = 87;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\MobileAppCategoryConstant|null
     */
    public function getMobileAppCategoryConstant()
    {
        return $this->mobile_app_category_constant;
    }

    public function hasMobileAppCategoryConstant()
    {
        return isset($this->mobile_app_category_constant);
    }

    public function clearMobileAppCategoryConstant()
    {
        unset($this->mobile_app_category_constant);
    }

    /**
     * The mobile app category constant referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.MobileAppCategoryConstant mobile_app_category_constant = 87;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\MobileAppCategoryConstant $var
     * @return $this
     */
    public function setMobileAppCategoryConstant($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\MobileAppCategoryConstant::class);
        $this->mobile_app_category_constant = $var;

        return $this;
    }

    /**
     * The mobile device constant referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.MobileDeviceConstant mobile_device_constant = 98;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\MobileDeviceConstant|null
     */
    public function getMobileDeviceConstant()
    {
        return $this->mobile_device_constant;
    }

    public function hasMobileDeviceConstant()
    {
        return isset($this->mobile_device_constant);
    }

    public function clearMobileDeviceConstant()
    {
        unset($this->mobile_device_constant);
    }

    /**
     * The mobile device constant referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.MobileDeviceConstant mobile_device_constant = 98;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\MobileDeviceConstant $var
     * @return $this
     */
    public function setMobileDeviceConstant($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\MobileDeviceConstant::class);
        $this->mobile_device_constant = $var;

        return $this;
    }

    /**
     * Offline conversion upload client summary.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.OfflineConversionUploadClientSummary offline_conversion_upload_client_summary = 216;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\OfflineConversionUploadClientSummary|null
     */
    public function getOfflineConversionUploadClientSummary()
    {
        return $this->offline_conversion_upload_client_summary;
    }

    public function hasOfflineConversionUploadClientSummary()
    {
        return isset($this->offline_conversion_upload_client_summary);
    }

    public function clearOfflineConversionUploadClientSummary()
    {
        unset($this->offline_conversion_upload_client_summary);
    }

    /**
     * Offline conversion upload client summary.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.OfflineConversionUploadClientSummary offline_conversion_upload_client_summary = 216;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\OfflineConversionUploadClientSummary $var
     * @return $this
     */
    public function setOfflineConversionUploadClientSummary($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\OfflineConversionUploadClientSummary::class);
        $this->offline_conversion_upload_client_summary = $var;

        return $this;
    }

    /**
     * The offline user data job referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.OfflineUserDataJob offline_user_data_job = 137;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\OfflineUserDataJob|null
     */
    public function getOfflineUserDataJob()
    {
        return $this->offline_user_data_job;
    }

    public function hasOfflineUserDataJob()
    {
        return isset($this->offline_user_data_job);
    }

    public function clearOfflineUserDataJob()
    {
        unset($this->offline_user_data_job);
    }

    /**
     * The offline user data job referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.OfflineUserDataJob offline_user_data_job = 137;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\OfflineUserDataJob $var
     * @return $this
     */
    public function setOfflineUserDataJob($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\OfflineUserDataJob::class);
        $this->offline_user_data_job = $var;

        return $this;
    }

    /**
     * The operating system version constant referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.OperatingSystemVersionConstant operating_system_version_constant = 86;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\OperatingSystemVersionConstant|null
     */
    public function getOperatingSystemVersionConstant()
    {
        return $this->operating_system_version_constant;
    }

    public function hasOperatingSystemVersionConstant()
    {
        return isset($this->operating_system_version_constant);
    }

    public function clearOperatingSystemVersionConstant()
    {
        unset($this->operating_system_version_constant);
    }

    /**
     * The operating system version constant referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.OperatingSystemVersionConstant operating_system_version_constant = 86;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\OperatingSystemVersionConstant $var
     * @return $this
     */
    public function setOperatingSystemVersionConstant($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\OperatingSystemVersionConstant::class);
        $this->operating_system_version_constant = $var;

        return $this;
    }

    /**
     * The paid organic search term view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.PaidOrganicSearchTermView paid_organic_search_term_view = 129;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\PaidOrganicSearchTermView|null
     */
    public function getPaidOrganicSearchTermView()
    {
        return $this->paid_organic_search_term_view;
    }

    public function hasPaidOrganicSearchTermView()
    {
        return isset($this->paid_organic_search_term_view);
    }

    public function clearPaidOrganicSearchTermView()
    {
        unset($this->paid_organic_search_term_view);
    }

    /**
     * The paid organic search term view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.PaidOrganicSearchTermView paid_organic_search_term_view = 129;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\PaidOrganicSearchTermView $var
     * @return $this
     */
    public function setPaidOrganicSearchTermView($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\PaidOrganicSearchTermView::class);
        $this->paid_organic_search_term_view = $var;

        return $this;
    }

    /**
     * The qualifying question referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.QualifyingQuestion qualifying_question = 202;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\QualifyingQuestion|null
     */
    public function getQualifyingQuestion()
    {
        return $this->qualifying_question;
    }

    public function hasQualifyingQuestion()
    {
        return isset($this->qualifying_question);
    }

    public function clearQualifyingQuestion()
    {
        unset($this->qualifying_question);
    }

    /**
     * The qualifying question referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.QualifyingQuestion qualifying_question = 202;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\QualifyingQuestion $var
     * @return $this
     */
    public function setQualifyingQuestion($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\QualifyingQuestion::class);
        $this->qualifying_question = $var;

        return $this;
    }

    /**
     * The parental status view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.ParentalStatusView parental_status_view = 45;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\ParentalStatusView|null
     */
    public function getParentalStatusView()
    {
        return $this->parental_status_view;
    }

    public function hasParentalStatusView()
    {
        return isset($this->parental_status_view);
    }

    public function clearParentalStatusView()
    {
        unset($this->parental_status_view);
    }

    /**
     * The parental status view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.ParentalStatusView parental_status_view = 45;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\ParentalStatusView $var
     * @return $this
     */
    public function setParentalStatusView($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\ParentalStatusView::class);
        $this->parental_status_view = $var;

        return $this;
    }

    /**
     * The per store view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.PerStoreView per_store_view = 198;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\PerStoreView|null
     */
    public function getPerStoreView()
    {
        return $this->per_store_view;
    }

    public function hasPerStoreView()
    {
        return isset($this->per_store_view);
    }

    public function clearPerStoreView()
    {
        unset($this->per_store_view);
    }

    /**
     * The per store view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.PerStoreView per_store_view = 198;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\PerStoreView $var
     * @return $this
     */
    public function setPerStoreView($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\PerStoreView::class);
        $this->per_store_view = $var;

        return $this;
    }

    /**
     * The product category referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.ProductCategoryConstant product_category_constant = 208;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\ProductCategoryConstant|null
     */
    public function getProductCategoryConstant()
    {
        return $this->product_category_constant;
    }

    public function hasProductCategoryConstant()
    {
        return isset($this->product_category_constant);
    }

    public function clearProductCategoryConstant()
    {
        unset($this->product_category_constant);
    }

    /**
     * The product category referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.ProductCategoryConstant product_category_constant = 208;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\ProductCategoryConstant $var
     * @return $this
     */
    public function setProductCategoryConstant($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\ProductCategoryConstant::class);
        $this->product_category_constant = $var;

        return $this;
    }

    /**
     * The product group view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.ProductGroupView product_group_view = 54;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\ProductGroupView|null
     */
    public function getProductGroupView()
    {
        return $this->product_group_view;
    }

    public function hasProductGroupView()
    {
        return isset($this->product_group_view);
    }

    public function clearProductGroupView()
    {
        unset($this->product_group_view);
    }

    /**
     * The product group view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.ProductGroupView product_group_view = 54;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\ProductGroupView $var
     * @return $this
     */
    public function setProductGroupView($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\ProductGroupView::class);
        $this->product_group_view = $var;

        return $this;
    }

    /**
     * The product link referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.ProductLink product_link = 194;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\ProductLink|null
     */
    public function getProductLink()
    {
        return $this->product_link;
    }

    public function hasProductLink()
    {
        return isset($this->product_link);
    }

    public function clearProductLink()
    {
        unset($this->product_link);
    }

    /**
     * The product link referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.ProductLink product_link = 194;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\ProductLink $var
     * @return $this
     */
    public function setProductLink($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\ProductLink::class);
        $this->product_link = $var;

        return $this;
    }

    /**
     * The product link invitation in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.ProductLinkInvitation product_link_invitation = 209;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\ProductLinkInvitation|null
     */
    public function getProductLinkInvitation()
    {
        return $this->product_link_invitation;
    }

    public function hasProductLinkInvitation()
    {
        return isset($this->product_link_invitation);
    }

    public function clearProductLinkInvitation()
    {
        unset($this->product_link_invitation);
    }

    /**
     * The product link invitation in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.ProductLinkInvitation product_link_invitation = 209;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\ProductLinkInvitation $var
     * @return $this
     */
    public function setProductLinkInvitation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\ProductLinkInvitation::class);
        $this->product_link_invitation = $var;

        return $this;
    }

    /**
     * The recommendation referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.Recommendation recommendation = 22;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\Recommendation|null
     */
    public function getRecommendation()
    {
        return $this->recommendation;
    }

    public function hasRecommendation()
    {
        return isset($this->recommendation);
    }

    public function clearRecommendation()
    {
        unset($this->recommendation);
    }

    /**
     * The recommendation referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.Recommendation recommendation = 22;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\Recommendation $var
     * @return $this
     */
    public function setRecommendation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\Recommendation::class);
        $this->recommendation = $var;

        return $this;
    }

    /**
     * The recommendation subscription referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.RecommendationSubscription recommendation_subscription = 220;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\RecommendationSubscription|null
     */
    public function getRecommendationSubscription()
    {
        return $this->recommendation_subscription;
    }

    public function hasRecommendationSubscription()
    {
        return isset($this->recommendation_subscription);
    }

    public function clearRecommendationSubscription()
    {
        unset($this->recommendation_subscription);
    }

    /**
     * The recommendation subscription referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.RecommendationSubscription recommendation_subscription = 220;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\RecommendationSubscription $var
     * @return $this
     */
    public function setRecommendationSubscription($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\RecommendationSubscription::class);
        $this->recommendation_subscription = $var;

        return $this;
    }

    /**
     * The search term view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.SearchTermView search_term_view = 68;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\SearchTermView|null
     */
    public function getSearchTermView()
    {
        return $this->search_term_view;
    }

    public function hasSearchTermView()
    {
        return isset($this->search_term_view);
    }

    public function clearSearchTermView()
    {
        unset($this->search_term_view);
    }

    /**
     * The search term view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.SearchTermView search_term_view = 68;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\SearchTermView $var
     * @return $this
     */
    public function setSearchTermView($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\SearchTermView::class);
        $this->search_term_view = $var;

        return $this;
    }

    /**
     * The shared set referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.SharedCriterion shared_criterion = 29;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\SharedCriterion|null
     */
    public function getSharedCriterion()
    {
        return $this->shared_criterion;
    }

    public function hasSharedCriterion()
    {
        return isset($this->shared_criterion);
    }

    public function clearSharedCriterion()
    {
        unset($this->shared_criterion);
    }

    /**
     * The shared set referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.SharedCriterion shared_criterion = 29;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\SharedCriterion $var
     * @return $this
     */
    public function setSharedCriterion($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\SharedCriterion::class);
        $this->shared_criterion = $var;

        return $this;
    }

    /**
     * The shared set referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.SharedSet shared_set = 27;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\SharedSet|null
     */
    public function getSharedSet()
    {
        return $this->shared_set;
    }

    public function hasSharedSet()
    {
        return isset($this->shared_set);
    }

    public function clearSharedSet()
    {
        unset($this->shared_set);
    }

    /**
     * The shared set referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.SharedSet shared_set = 27;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\SharedSet $var
     * @return $this
     */
    public function setSharedSet($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\SharedSet::class);
        $this->shared_set = $var;

        return $this;
    }

    /**
     * The Smart campaign setting referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.SmartCampaignSetting smart_campaign_setting = 167;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\SmartCampaignSetting|null
     */
    public function getSmartCampaignSetting()
    {
        return $this->smart_campaign_setting;
    }

    public function hasSmartCampaignSetting()
    {
        return isset($this->smart_campaign_setting);
    }

    public function clearSmartCampaignSetting()
    {
        unset($this->smart_campaign_setting);
    }

    /**
     * The Smart campaign setting referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.SmartCampaignSetting smart_campaign_setting = 167;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\SmartCampaignSetting $var
     * @return $this
     */
    public function setSmartCampaignSetting($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\SmartCampaignSetting::class);
        $this->smart_campaign_setting = $var;

        return $this;
    }

    /**
     * The shopping performance view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.ShoppingPerformanceView shopping_performance_view = 117;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\ShoppingPerformanceView|null
     */
    public function getShoppingPerformanceView()
    {
        return $this->shopping_performance_view;
    }

    public function hasShoppingPerformanceView()
    {
        return isset($this->shopping_performance_view);
    }

    public function clearShoppingPerformanceView()
    {
        unset($this->shopping_performance_view);
    }

    /**
     * The shopping performance view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.ShoppingPerformanceView shopping_performance_view = 117;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\ShoppingPerformanceView $var
     * @return $this
     */
    public function setShoppingPerformanceView($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\ShoppingPerformanceView::class);
        $this->shopping_performance_view = $var;

        return $this;
    }

    /**
     * The shopping product referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.ShoppingProduct shopping_product = 226;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\ShoppingProduct|null
     */
    public function getShoppingProduct()
    {
        return $this->shopping_product;
    }

    public function hasShoppingProduct()
    {
        return isset($this->shopping_product);
    }

    public function clearShoppingProduct()
    {
        unset($this->shopping_product);
    }

    /**
     * The shopping product referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.ShoppingProduct shopping_product = 226;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\ShoppingProduct $var
     * @return $this
     */
    public function setShoppingProduct($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\ShoppingProduct::class);
        $this->shopping_product = $var;

        return $this;
    }

    /**
     * The Smart campaign search term view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.SmartCampaignSearchTermView smart_campaign_search_term_view = 170;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\SmartCampaignSearchTermView|null
     */
    public function getSmartCampaignSearchTermView()
    {
        return $this->smart_campaign_search_term_view;
    }

    public function hasSmartCampaignSearchTermView()
    {
        return isset($this->smart_campaign_search_term_view);
    }

    public function clearSmartCampaignSearchTermView()
    {
        unset($this->smart_campaign_search_term_view);
    }

    /**
     * The Smart campaign search term view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.SmartCampaignSearchTermView smart_campaign_search_term_view = 170;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\SmartCampaignSearchTermView $var
     * @return $this
     */
    public function setSmartCampaignSearchTermView($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\SmartCampaignSearchTermView::class);
        $this->smart_campaign_search_term_view = $var;

        return $this;
    }

    /**
     * The AccountLink referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.ThirdPartyAppAnalyticsLink third_party_app_analytics_link = 144;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\ThirdPartyAppAnalyticsLink|null
     */
    public function getThirdPartyAppAnalyticsLink()
    {
        return $this->third_party_app_analytics_link;
    }

    public function hasThirdPartyAppAnalyticsLink()
    {
        return isset($this->third_party_app_analytics_link);
    }

    public function clearThirdPartyAppAnalyticsLink()
    {
        unset($this->third_party_app_analytics_link);
    }

    /**
     * The AccountLink referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.ThirdPartyAppAnalyticsLink third_party_app_analytics_link = 144;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\ThirdPartyAppAnalyticsLink $var
     * @return $this
     */
    public function setThirdPartyAppAnalyticsLink($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\ThirdPartyAppAnalyticsLink::class);
        $this->third_party_app_analytics_link = $var;

        return $this;
    }

    /**
     * The topic view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.TopicView topic_view = 44;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\TopicView|null
     */
    public function getTopicView()
    {
        return $this->topic_view;
    }

    public function hasTopicView()
    {
        return isset($this->topic_view);
    }

    public function clearTopicView()
    {
        unset($this->topic_view);
    }

    /**
     * The topic view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.TopicView topic_view = 44;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\TopicView $var
     * @return $this
     */
    public function setTopicView($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\TopicView::class);
        $this->topic_view = $var;

        return $this;
    }

    /**
     * The travel activity group view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.TravelActivityGroupView travel_activity_group_view = 201;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\TravelActivityGroupView|null
     */
    public function getTravelActivityGroupView()
    {
        return $this->travel_activity_group_view;
    }

    public function hasTravelActivityGroupView()
    {
        return isset($this->travel_activity_group_view);
    }

    public function clearTravelActivityGroupView()
    {
        unset($this->travel_activity_group_view);
    }

    /**
     * The travel activity group view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.TravelActivityGroupView travel_activity_group_view = 201;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\TravelActivityGroupView $var
     * @return $this
     */
    public function setTravelActivityGroupView($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\TravelActivityGroupView::class);
        $this->travel_activity_group_view = $var;

        return $this;
    }

    /**
     * The travel activity performance view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.TravelActivityPerformanceView travel_activity_performance_view = 200;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\TravelActivityPerformanceView|null
     */
    public function getTravelActivityPerformanceView()
    {
        return $this->travel_activity_performance_view;
    }

    public function hasTravelActivityPerformanceView()
    {
        return isset($this->travel_activity_performance_view);
    }

    public function clearTravelActivityPerformanceView()
    {
        unset($this->travel_activity_performance_view);
    }

    /**
     * The travel activity performance view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.TravelActivityPerformanceView travel_activity_performance_view = 200;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\TravelActivityPerformanceView $var
     * @return $this
     */
    public function setTravelActivityPerformanceView($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\TravelActivityPerformanceView::class);
        $this->travel_activity_performance_view = $var;

        return $this;
    }

    /**
     * The experiment referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.Experiment experiment = 133;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\Experiment|null
     */
    public function getExperiment()
    {
        return $this->experiment;
    }

    public function hasExperiment()
    {
        return isset($this->experiment);
    }

    public function clearExperiment()
    {
        unset($this->experiment);
    }

    /**
     * The experiment referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.Experiment experiment = 133;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\Experiment $var
     * @return $this
     */
    public function setExperiment($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\Experiment::class);
        $this->experiment = $var;

        return $this;
    }

    /**
     * The experiment arm referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.ExperimentArm experiment_arm = 183;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\ExperimentArm|null
     */
    public function getExperimentArm()
    {
        return $this->experiment_arm;
    }

    public function hasExperimentArm()
    {
        return isset($this->experiment_arm);
    }

    public function clearExperimentArm()
    {
        unset($this->experiment_arm);
    }

    /**
     * The experiment arm referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.ExperimentArm experiment_arm = 183;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\ExperimentArm $var
     * @return $this
     */
    public function setExperimentArm($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\ExperimentArm::class);
        $this->experiment_arm = $var;

        return $this;
    }

    /**
     * The user interest referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.UserInterest user_interest = 59;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\UserInterest|null
     */
    public function getUserInterest()
    {
        return $this->user_interest;
    }

    public function hasUserInterest()
    {
        return isset($this->user_interest);
    }

    public function clearUserInterest()
    {
        unset($this->user_interest);
    }

    /**
     * The user interest referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.UserInterest user_interest = 59;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\UserInterest $var
     * @return $this
     */
    public function setUserInterest($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\UserInterest::class);
        $this->user_interest = $var;

        return $this;
    }

    /**
     * The life event referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.LifeEvent life_event = 161;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\LifeEvent|null
     */
    public function getLifeEvent()
    {
        return $this->life_event;
    }

    public function hasLifeEvent()
    {
        return isset($this->life_event);
    }

    public function clearLifeEvent()
    {
        unset($this->life_event);
    }

    /**
     * The life event referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.LifeEvent life_event = 161;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\LifeEvent $var
     * @return $this
     */
    public function setLifeEvent($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\LifeEvent::class);
        $this->life_event = $var;

        return $this;
    }

    /**
     * The user list referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.UserList user_list = 38;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\UserList|null
     */
    public function getUserList()
    {
        return $this->user_list;
    }

    public function hasUserList()
    {
        return isset($this->user_list);
    }

    public function clearUserList()
    {
        unset($this->user_list);
    }

    /**
     * The user list referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.UserList user_list = 38;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\UserList $var
     * @return $this
     */
    public function setUserList($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\UserList::class);
        $this->user_list = $var;

        return $this;
    }

    /**
     * The user list customer type in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.UserListCustomerType user_list_customer_type = 225;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\UserListCustomerType|null
     */
    public function getUserListCustomerType()
    {
        return $this->user_list_customer_type;
    }

    public function hasUserListCustomerType()
    {
        return isset($this->user_list_customer_type);
    }

    public function clearUserListCustomerType()
    {
        unset($this->user_list_customer_type);
    }

    /**
     * The user list customer type in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.UserListCustomerType user_list_customer_type = 225;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\UserListCustomerType $var
     * @return $this
     */
    public function setUserListCustomerType($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\UserListCustomerType::class);
        $this->user_list_customer_type = $var;

        return $this;
    }

    /**
     * The user location view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.UserLocationView user_location_view = 135;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\UserLocationView|null
     */
    public function getUserLocationView()
    {
        return $this->user_location_view;
    }

    public function hasUserLocationView()
    {
        return isset($this->user_location_view);
    }

    public function clearUserLocationView()
    {
        unset($this->user_location_view);
    }

    /**
     * The user location view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.UserLocationView user_location_view = 135;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\UserLocationView $var
     * @return $this
     */
    public function setUserLocationView($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\UserLocationView::class);
        $this->user_location_view = $var;

        return $this;
    }

    /**
     * The remarketing action referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.RemarketingAction remarketing_action = 60;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\RemarketingAction|null
     */
    public function getRemarketingAction()
    {
        return $this->remarketing_action;
    }

    public function hasRemarketingAction()
    {
        return isset($this->remarketing_action);
    }

    public function clearRemarketingAction()
    {
        unset($this->remarketing_action);
    }

    /**
     * The remarketing action referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.RemarketingAction remarketing_action = 60;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\RemarketingAction $var
     * @return $this
     */
    public function setRemarketingAction($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\RemarketingAction::class);
        $this->remarketing_action = $var;

        return $this;
    }

    /**
     * The topic constant referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.TopicConstant topic_constant = 31;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\TopicConstant|null
     */
    public function getTopicConstant()
    {
        return $this->topic_constant;
    }

    public function hasTopicConstant()
    {
        return isset($this->topic_constant);
    }

    public function clearTopicConstant()
    {
        unset($this->topic_constant);
    }

    /**
     * The topic constant referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.TopicConstant topic_constant = 31;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\TopicConstant $var
     * @return $this
     */
    public function setTopicConstant($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\TopicConstant::class);
        $this->topic_constant = $var;

        return $this;
    }

    /**
     * The video referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.Video video = 39;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\Video|null
     */
    public function getVideo()
    {
        return $this->video;
    }

    public function hasVideo()
    {
        return isset($this->video);
    }

    public function clearVideo()
    {
        unset($this->video);
    }

    /**
     * The video referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.Video video = 39;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\Video $var
     * @return $this
     */
    public function setVideo($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\Video::class);
        $this->video = $var;

        return $this;
    }

    /**
     * The webpage view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.WebpageView webpage_view = 162;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\WebpageView|null
     */
    public function getWebpageView()
    {
        return $this->webpage_view;
    }

    public function hasWebpageView()
    {
        return isset($this->webpage_view);
    }

    public function clearWebpageView()
    {
        unset($this->webpage_view);
    }

    /**
     * The webpage view referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.WebpageView webpage_view = 162;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\WebpageView $var
     * @return $this
     */
    public function setWebpageView($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\WebpageView::class);
        $this->webpage_view = $var;

        return $this;
    }

    /**
     * The lead form user submission referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.LeadFormSubmissionData lead_form_submission_data = 192;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\LeadFormSubmissionData|null
     */
    public function getLeadFormSubmissionData()
    {
        return $this->lead_form_submission_data;
    }

    public function hasLeadFormSubmissionData()
    {
        return isset($this->lead_form_submission_data);
    }

    public function clearLeadFormSubmissionData()
    {
        unset($this->lead_form_submission_data);
    }

    /**
     * The lead form user submission referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.LeadFormSubmissionData lead_form_submission_data = 192;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\LeadFormSubmissionData $var
     * @return $this
     */
    public function setLeadFormSubmissionData($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\LeadFormSubmissionData::class);
        $this->lead_form_submission_data = $var;

        return $this;
    }

    /**
     * The local services lead referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.LocalServicesLead local_services_lead = 210;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\LocalServicesLead|null
     */
    public function getLocalServicesLead()
    {
        return $this->local_services_lead;
    }

    public function hasLocalServicesLead()
    {
        return isset($this->local_services_lead);
    }

    public function clearLocalServicesLead()
    {
        unset($this->local_services_lead);
    }

    /**
     * The local services lead referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.LocalServicesLead local_services_lead = 210;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\LocalServicesLead $var
     * @return $this
     */
    public function setLocalServicesLead($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\LocalServicesLead::class);
        $this->local_services_lead = $var;

        return $this;
    }

    /**
     * The local services lead conversationreferenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.LocalServicesLeadConversation local_services_lead_conversation = 214;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\LocalServicesLeadConversation|null
     */
    public function getLocalServicesLeadConversation()
    {
        return $this->local_services_lead_conversation;
    }

    public function hasLocalServicesLeadConversation()
    {
        return isset($this->local_services_lead_conversation);
    }

    public function clearLocalServicesLeadConversation()
    {
        unset($this->local_services_lead_conversation);
    }

    /**
     * The local services lead conversationreferenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.LocalServicesLeadConversation local_services_lead_conversation = 214;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\LocalServicesLeadConversation $var
     * @return $this
     */
    public function setLocalServicesLeadConversation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\LocalServicesLeadConversation::class);
        $this->local_services_lead_conversation = $var;

        return $this;
    }

    /**
     * The android privacy shared key google ad group referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AndroidPrivacySharedKeyGoogleAdGroup android_privacy_shared_key_google_ad_group = 217;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\AndroidPrivacySharedKeyGoogleAdGroup|null
     */
    public function getAndroidPrivacySharedKeyGoogleAdGroup()
    {
        return $this->android_privacy_shared_key_google_ad_group;
    }

    public function hasAndroidPrivacySharedKeyGoogleAdGroup()
    {
        return isset($this->android_privacy_shared_key_google_ad_group);
    }

    public function clearAndroidPrivacySharedKeyGoogleAdGroup()
    {
        unset($this->android_privacy_shared_key_google_ad_group);
    }

    /**
     * The android privacy shared key google ad group referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AndroidPrivacySharedKeyGoogleAdGroup android_privacy_shared_key_google_ad_group = 217;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\AndroidPrivacySharedKeyGoogleAdGroup $var
     * @return $this
     */
    public function setAndroidPrivacySharedKeyGoogleAdGroup($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\AndroidPrivacySharedKeyGoogleAdGroup::class);
        $this->android_privacy_shared_key_google_ad_group = $var;

        return $this;
    }

    /**
     * The android privacy shared key google campaign referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AndroidPrivacySharedKeyGoogleCampaign android_privacy_shared_key_google_campaign = 218;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\AndroidPrivacySharedKeyGoogleCampaign|null
     */
    public function getAndroidPrivacySharedKeyGoogleCampaign()
    {
        return $this->android_privacy_shared_key_google_campaign;
    }

    public function hasAndroidPrivacySharedKeyGoogleCampaign()
    {
        return isset($this->android_privacy_shared_key_google_campaign);
    }

    public function clearAndroidPrivacySharedKeyGoogleCampaign()
    {
        unset($this->android_privacy_shared_key_google_campaign);
    }

    /**
     * The android privacy shared key google campaign referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AndroidPrivacySharedKeyGoogleCampaign android_privacy_shared_key_google_campaign = 218;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\AndroidPrivacySharedKeyGoogleCampaign $var
     * @return $this
     */
    public function setAndroidPrivacySharedKeyGoogleCampaign($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\AndroidPrivacySharedKeyGoogleCampaign::class);
        $this->android_privacy_shared_key_google_campaign = $var;

        return $this;
    }

    /**
     * The android privacy shared key google network type referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AndroidPrivacySharedKeyGoogleNetworkType android_privacy_shared_key_google_network_type = 219;</code>
     * @return \Google\Ads\GoogleAds\V17\Resources\AndroidPrivacySharedKeyGoogleNetworkType|null
     */
    public function getAndroidPrivacySharedKeyGoogleNetworkType()
    {
        return $this->android_privacy_shared_key_google_network_type;
    }

    public function hasAndroidPrivacySharedKeyGoogleNetworkType()
    {
        return isset($this->android_privacy_shared_key_google_network_type);
    }

    public function clearAndroidPrivacySharedKeyGoogleNetworkType()
    {
        unset($this->android_privacy_shared_key_google_network_type);
    }

    /**
     * The android privacy shared key google network type referenced in the query.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.resources.AndroidPrivacySharedKeyGoogleNetworkType android_privacy_shared_key_google_network_type = 219;</code>
     * @param \Google\Ads\GoogleAds\V17\Resources\AndroidPrivacySharedKeyGoogleNetworkType $var
     * @return $this
     */
    public function setAndroidPrivacySharedKeyGoogleNetworkType($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Resources\AndroidPrivacySharedKeyGoogleNetworkType::class);
        $this->android_privacy_shared_key_google_network_type = $var;

        return $this;
    }

    /**
     * The metrics.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.common.Metrics metrics = 4;</code>
     * @return \Google\Ads\GoogleAds\V17\Common\Metrics|null
     */
    public function getMetrics()
    {
        return $this->metrics;
    }

    public function hasMetrics()
    {
        return isset($this->metrics);
    }

    public function clearMetrics()
    {
        unset($this->metrics);
    }

    /**
     * The metrics.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.common.Metrics metrics = 4;</code>
     * @param \Google\Ads\GoogleAds\V17\Common\Metrics $var
     * @return $this
     */
    public function setMetrics($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Common\Metrics::class);
        $this->metrics = $var;

        return $this;
    }

    /**
     * The segments.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.common.Segments segments = 102;</code>
     * @return \Google\Ads\GoogleAds\V17\Common\Segments|null
     */
    public function getSegments()
    {
        return $this->segments;
    }

    public function hasSegments()
    {
        return isset($this->segments);
    }

    public function clearSegments()
    {
        unset($this->segments);
    }

    /**
     * The segments.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.common.Segments segments = 102;</code>
     * @param \Google\Ads\GoogleAds\V17\Common\Segments $var
     * @return $this
     */
    public function setSegments($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Common\Segments::class);
        $this->segments = $var;

        return $this;
    }

}

