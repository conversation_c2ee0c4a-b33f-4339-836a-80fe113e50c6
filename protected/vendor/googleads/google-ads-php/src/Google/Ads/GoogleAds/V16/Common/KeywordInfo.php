<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v16/common/criteria.proto

namespace Google\Ads\GoogleAds\V16\Common;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * A keyword criterion.
 *
 * Generated from protobuf message <code>google.ads.googleads.v16.common.KeywordInfo</code>
 */
class KeywordInfo extends \Google\Protobuf\Internal\Message
{
    /**
     * The text of the keyword (at most 80 characters and 10 words).
     *
     * Generated from protobuf field <code>optional string text = 3;</code>
     */
    protected $text = null;
    /**
     * The match type of the keyword.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.KeywordMatchTypeEnum.KeywordMatchType match_type = 2;</code>
     */
    protected $match_type = 0;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $text
     *           The text of the keyword (at most 80 characters and 10 words).
     *     @type int $match_type
     *           The match type of the keyword.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Ads\GoogleAds\V16\Common\Criteria::initOnce();
        parent::__construct($data);
    }

    /**
     * The text of the keyword (at most 80 characters and 10 words).
     *
     * Generated from protobuf field <code>optional string text = 3;</code>
     * @return string
     */
    public function getText()
    {
        return isset($this->text) ? $this->text : '';
    }

    public function hasText()
    {
        return isset($this->text);
    }

    public function clearText()
    {
        unset($this->text);
    }

    /**
     * The text of the keyword (at most 80 characters and 10 words).
     *
     * Generated from protobuf field <code>optional string text = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setText($var)
    {
        GPBUtil::checkString($var, True);
        $this->text = $var;

        return $this;
    }

    /**
     * The match type of the keyword.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.KeywordMatchTypeEnum.KeywordMatchType match_type = 2;</code>
     * @return int
     */
    public function getMatchType()
    {
        return $this->match_type;
    }

    /**
     * The match type of the keyword.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.KeywordMatchTypeEnum.KeywordMatchType match_type = 2;</code>
     * @param int $var
     * @return $this
     */
    public function setMatchType($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V16\Enums\KeywordMatchTypeEnum\KeywordMatchType::class);
        $this->match_type = $var;

        return $this;
    }

}

