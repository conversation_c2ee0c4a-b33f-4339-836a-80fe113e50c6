<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v16/enums/conversion_action_counting_type.proto

namespace Google\Ads\GoogleAds\V16\Enums;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Container for enum describing the conversion deduplication mode for
 * conversion optimizer.
 *
 * Generated from protobuf message <code>google.ads.googleads.v16.enums.ConversionActionCountingTypeEnum</code>
 */
class ConversionActionCountingTypeEnum extends \Google\Protobuf\Internal\Message
{

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Ads\GoogleAds\V16\Enums\ConversionActionCountingType::initOnce();
        parent::__construct($data);
    }

}

