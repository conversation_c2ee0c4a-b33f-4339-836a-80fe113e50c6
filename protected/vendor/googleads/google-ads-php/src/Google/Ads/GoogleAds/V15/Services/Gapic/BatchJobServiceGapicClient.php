<?php
/*
 * Copyright 2023 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/*
 * GENERATED CODE WARNING
 * Generated by gapic-generator-php from the file
 * https://github.com/googleapis/googleapis/blob/master/google/ads/googleads/v15/services/batch_job_service.proto
 * Updates to the above are reflected here through a refresh process.
 */

namespace Google\Ads\GoogleAds\V15\Services\Gapic;

use Google\Ads\GoogleAds\V15\Resources\BatchJob\BatchJobMetadata;
use Google\Ads\GoogleAds\V15\Services\AddBatchJobOperationsRequest;
use Google\Ads\GoogleAds\V15\Services\AddBatchJobOperationsResponse;
use Google\Ads\GoogleAds\V15\Services\BatchJobOperation;
use Google\Ads\GoogleAds\V15\Services\ListBatchJobResultsRequest;
use Google\Ads\GoogleAds\V15\Services\ListBatchJobResultsResponse;
use Google\Ads\GoogleAds\V15\Services\MutateBatchJobRequest;
use Google\Ads\GoogleAds\V15\Services\MutateBatchJobResponse;
use Google\Ads\GoogleAds\V15\Services\MutateOperation;
use Google\Ads\GoogleAds\V15\Services\RunBatchJobRequest;
use Google\ApiCore\ApiException;
use Google\ApiCore\CredentialsWrapper;
use Google\ApiCore\GapicClientTrait;
use Google\ApiCore\LongRunning\OperationsClient;
use Google\ApiCore\OperationResponse;
use Google\ApiCore\PathTemplate;
use Google\ApiCore\RequestParamsHeaderDescriptor;
use Google\ApiCore\RetrySettings;
use Google\ApiCore\Transport\TransportInterface;
use Google\ApiCore\ValidationException;
use Google\Auth\FetchAuthTokenInterface;
use Google\LongRunning\Operation;

/**
 * Service Description: Service to manage batch jobs.
 *
 * This class provides the ability to make remote calls to the backing service through method
 * calls that map to API methods. Sample code to get started:
 *
 * ```
 * $batchJobServiceClient = new BatchJobServiceClient();
 * try {
 *     $formattedResourceName = $batchJobServiceClient->batchJobName('[CUSTOMER_ID]', '[BATCH_JOB_ID]');
 *     $mutateOperations = [];
 *     $response = $batchJobServiceClient->addBatchJobOperations($formattedResourceName, $mutateOperations);
 * } finally {
 *     $batchJobServiceClient->close();
 * }
 * ```
 *
 * Many parameters require resource names to be formatted in a particular way. To
 * assist with these names, this class includes a format method for each type of
 * name, and additionally a parseName method to extract the individual identifiers
 * contained within formatted names that are returned by the API.
 *
 * This service has a new (beta) implementation. See {@see
 * \Google\Ads\GoogleAds\V15\Services\Client\BatchJobServiceClient} to use the new
 * surface.
 */
class BatchJobServiceGapicClient
{
    use GapicClientTrait;

    /** The name of the service. */
    const SERVICE_NAME = 'google.ads.googleads.v15.services.BatchJobService';

    /** The default address of the service. */
    const SERVICE_ADDRESS = 'googleads.googleapis.com';

    /** The default port of the service. */
    const DEFAULT_SERVICE_PORT = 443;

    /** The name of the code generator, to be included in the agent header. */
    const CODEGEN_NAME = 'gapic';

    /** The default scopes required by the service. */
    public static $serviceScopes = [
        'https://www.googleapis.com/auth/adwords',
    ];

    private static $accessibleBiddingStrategyNameTemplate;

    private static $adNameTemplate;

    private static $adGroupNameTemplate;

    private static $adGroupAdNameTemplate;

    private static $adGroupAdLabelNameTemplate;

    private static $adGroupAssetNameTemplate;

    private static $adGroupBidModifierNameTemplate;

    private static $adGroupCriterionNameTemplate;

    private static $adGroupCriterionCustomizerNameTemplate;

    private static $adGroupCriterionLabelNameTemplate;

    private static $adGroupCustomizerNameTemplate;

    private static $adGroupExtensionSettingNameTemplate;

    private static $adGroupFeedNameTemplate;

    private static $adGroupLabelNameTemplate;

    private static $adParameterNameTemplate;

    private static $assetNameTemplate;

    private static $assetGroupNameTemplate;

    private static $assetGroupAssetNameTemplate;

    private static $assetGroupListingGroupFilterNameTemplate;

    private static $assetGroupSignalNameTemplate;

    private static $assetSetNameTemplate;

    private static $assetSetAssetNameTemplate;

    private static $audienceNameTemplate;

    private static $batchJobNameTemplate;

    private static $biddingDataExclusionNameTemplate;

    private static $biddingSeasonalityAdjustmentNameTemplate;

    private static $biddingStrategyNameTemplate;

    private static $campaignNameTemplate;

    private static $campaignAssetNameTemplate;

    private static $campaignAssetSetNameTemplate;

    private static $campaignBidModifierNameTemplate;

    private static $campaignBudgetNameTemplate;

    private static $campaignConversionGoalNameTemplate;

    private static $campaignCriterionNameTemplate;

    private static $campaignCustomizerNameTemplate;

    private static $campaignDraftNameTemplate;

    private static $campaignExtensionSettingNameTemplate;

    private static $campaignFeedNameTemplate;

    private static $campaignGroupNameTemplate;

    private static $campaignLabelNameTemplate;

    private static $campaignSharedSetNameTemplate;

    private static $combinedAudienceNameTemplate;

    private static $conversionActionNameTemplate;

    private static $conversionCustomVariableNameTemplate;

    private static $conversionGoalCampaignConfigNameTemplate;

    private static $conversionValueRuleNameTemplate;

    private static $conversionValueRuleSetNameTemplate;

    private static $customConversionGoalNameTemplate;

    private static $customerNameTemplate;

    private static $customerAssetNameTemplate;

    private static $customerConversionGoalNameTemplate;

    private static $customerCustomizerNameTemplate;

    private static $customerExtensionSettingNameTemplate;

    private static $customerFeedNameTemplate;

    private static $customerLabelNameTemplate;

    private static $customerNegativeCriterionNameTemplate;

    private static $customizerAttributeNameTemplate;

    private static $experimentNameTemplate;

    private static $experimentArmNameTemplate;

    private static $extensionFeedItemNameTemplate;

    private static $feedNameTemplate;

    private static $feedItemNameTemplate;

    private static $feedItemSetNameTemplate;

    private static $feedItemSetLinkNameTemplate;

    private static $feedItemTargetNameTemplate;

    private static $feedMappingNameTemplate;

    private static $geoTargetConstantNameTemplate;

    private static $keywordPlanNameTemplate;

    private static $keywordPlanAdGroupNameTemplate;

    private static $keywordPlanAdGroupKeywordNameTemplate;

    private static $keywordPlanCampaignNameTemplate;

    private static $keywordPlanCampaignKeywordNameTemplate;

    private static $labelNameTemplate;

    private static $languageConstantNameTemplate;

    private static $mobileAppCategoryConstantNameTemplate;

    private static $recommendationSubscriptionNameTemplate;

    private static $remarketingActionNameTemplate;

    private static $sharedCriterionNameTemplate;

    private static $sharedSetNameTemplate;

    private static $smartCampaignSettingNameTemplate;

    private static $topicConstantNameTemplate;

    private static $userInterestNameTemplate;

    private static $userListNameTemplate;

    private static $pathTemplateMap;

    private $operationsClient;

    private static function getClientDefaults()
    {
        return [
            'serviceName' => self::SERVICE_NAME,
            'apiEndpoint' => self::SERVICE_ADDRESS . ':' . self::DEFAULT_SERVICE_PORT,
            'clientConfig' => __DIR__ . '/../resources/batch_job_service_client_config.json',
            'descriptorsConfigPath' => __DIR__ . '/../resources/batch_job_service_descriptor_config.php',
            'gcpApiConfigPath' => __DIR__ . '/../resources/batch_job_service_grpc_config.json',
            'credentialsConfig' => [
                'defaultScopes' => self::$serviceScopes,
            ],
            'transportConfig' => [
                'rest' => [
                    'restClientConfigPath' => __DIR__ . '/../resources/batch_job_service_rest_client_config.php',
                ],
            ],
        ];
    }

    private static function getAccessibleBiddingStrategyNameTemplate()
    {
        if (self::$accessibleBiddingStrategyNameTemplate == null) {
            self::$accessibleBiddingStrategyNameTemplate = new PathTemplate('customers/{customer_id}/accessibleBiddingStrategies/{bidding_strategy_id}');
        }

        return self::$accessibleBiddingStrategyNameTemplate;
    }

    private static function getAdNameTemplate()
    {
        if (self::$adNameTemplate == null) {
            self::$adNameTemplate = new PathTemplate('customers/{customer_id}/ads/{ad_id}');
        }

        return self::$adNameTemplate;
    }

    private static function getAdGroupNameTemplate()
    {
        if (self::$adGroupNameTemplate == null) {
            self::$adGroupNameTemplate = new PathTemplate('customers/{customer_id}/adGroups/{ad_group_id}');
        }

        return self::$adGroupNameTemplate;
    }

    private static function getAdGroupAdNameTemplate()
    {
        if (self::$adGroupAdNameTemplate == null) {
            self::$adGroupAdNameTemplate = new PathTemplate('customers/{customer_id}/adGroupAds/{ad_group_id}~{ad_id}');
        }

        return self::$adGroupAdNameTemplate;
    }

    private static function getAdGroupAdLabelNameTemplate()
    {
        if (self::$adGroupAdLabelNameTemplate == null) {
            self::$adGroupAdLabelNameTemplate = new PathTemplate('customers/{customer_id}/adGroupAdLabels/{ad_group_id}~{ad_id}~{label_id}');
        }

        return self::$adGroupAdLabelNameTemplate;
    }

    private static function getAdGroupAssetNameTemplate()
    {
        if (self::$adGroupAssetNameTemplate == null) {
            self::$adGroupAssetNameTemplate = new PathTemplate('customers/{customer_id}/adGroupAssets/{ad_group_id}~{asset_id}~{field_type}');
        }

        return self::$adGroupAssetNameTemplate;
    }

    private static function getAdGroupBidModifierNameTemplate()
    {
        if (self::$adGroupBidModifierNameTemplate == null) {
            self::$adGroupBidModifierNameTemplate = new PathTemplate('customers/{customer_id}/adGroupBidModifiers/{ad_group_id}~{criterion_id}');
        }

        return self::$adGroupBidModifierNameTemplate;
    }

    private static function getAdGroupCriterionNameTemplate()
    {
        if (self::$adGroupCriterionNameTemplate == null) {
            self::$adGroupCriterionNameTemplate = new PathTemplate('customers/{customer_id}/adGroupCriteria/{ad_group_id}~{criterion_id}');
        }

        return self::$adGroupCriterionNameTemplate;
    }

    private static function getAdGroupCriterionCustomizerNameTemplate()
    {
        if (self::$adGroupCriterionCustomizerNameTemplate == null) {
            self::$adGroupCriterionCustomizerNameTemplate = new PathTemplate('customers/{customer_id}/adGroupCriterionCustomizers/{ad_group_id}~{criterion_id}~{customizer_attribute_id}');
        }

        return self::$adGroupCriterionCustomizerNameTemplate;
    }

    private static function getAdGroupCriterionLabelNameTemplate()
    {
        if (self::$adGroupCriterionLabelNameTemplate == null) {
            self::$adGroupCriterionLabelNameTemplate = new PathTemplate('customers/{customer_id}/adGroupCriterionLabels/{ad_group_id}~{criterion_id}~{label_id}');
        }

        return self::$adGroupCriterionLabelNameTemplate;
    }

    private static function getAdGroupCustomizerNameTemplate()
    {
        if (self::$adGroupCustomizerNameTemplate == null) {
            self::$adGroupCustomizerNameTemplate = new PathTemplate('customers/{customer_id}/adGroupCustomizers/{ad_group_id}~{customizer_attribute_id}');
        }

        return self::$adGroupCustomizerNameTemplate;
    }

    private static function getAdGroupExtensionSettingNameTemplate()
    {
        if (self::$adGroupExtensionSettingNameTemplate == null) {
            self::$adGroupExtensionSettingNameTemplate = new PathTemplate('customers/{customer_id}/adGroupExtensionSettings/{ad_group_id}~{extension_type}');
        }

        return self::$adGroupExtensionSettingNameTemplate;
    }

    private static function getAdGroupFeedNameTemplate()
    {
        if (self::$adGroupFeedNameTemplate == null) {
            self::$adGroupFeedNameTemplate = new PathTemplate('customers/{customer_id}/adGroupFeeds/{ad_group_id}~{feed_id}');
        }

        return self::$adGroupFeedNameTemplate;
    }

    private static function getAdGroupLabelNameTemplate()
    {
        if (self::$adGroupLabelNameTemplate == null) {
            self::$adGroupLabelNameTemplate = new PathTemplate('customers/{customer_id}/adGroupLabels/{ad_group_id}~{label_id}');
        }

        return self::$adGroupLabelNameTemplate;
    }

    private static function getAdParameterNameTemplate()
    {
        if (self::$adParameterNameTemplate == null) {
            self::$adParameterNameTemplate = new PathTemplate('customers/{customer_id}/adParameters/{ad_group_id}~{criterion_id}~{parameter_index}');
        }

        return self::$adParameterNameTemplate;
    }

    private static function getAssetNameTemplate()
    {
        if (self::$assetNameTemplate == null) {
            self::$assetNameTemplate = new PathTemplate('customers/{customer_id}/assets/{asset_id}');
        }

        return self::$assetNameTemplate;
    }

    private static function getAssetGroupNameTemplate()
    {
        if (self::$assetGroupNameTemplate == null) {
            self::$assetGroupNameTemplate = new PathTemplate('customers/{customer_id}/assetGroups/{asset_group_id}');
        }

        return self::$assetGroupNameTemplate;
    }

    private static function getAssetGroupAssetNameTemplate()
    {
        if (self::$assetGroupAssetNameTemplate == null) {
            self::$assetGroupAssetNameTemplate = new PathTemplate('customers/{customer_id}/assetGroupAssets/{asset_group_id}~{asset_id}~{field_type}');
        }

        return self::$assetGroupAssetNameTemplate;
    }

    private static function getAssetGroupListingGroupFilterNameTemplate()
    {
        if (self::$assetGroupListingGroupFilterNameTemplate == null) {
            self::$assetGroupListingGroupFilterNameTemplate = new PathTemplate('customers/{customer_id}/assetGroupListingGroupFilters/{asset_group_id}~{listing_group_filter_id}');
        }

        return self::$assetGroupListingGroupFilterNameTemplate;
    }

    private static function getAssetGroupSignalNameTemplate()
    {
        if (self::$assetGroupSignalNameTemplate == null) {
            self::$assetGroupSignalNameTemplate = new PathTemplate('customers/{customer_id}/assetGroupSignals/{asset_group_id}~{criterion_id}');
        }

        return self::$assetGroupSignalNameTemplate;
    }

    private static function getAssetSetNameTemplate()
    {
        if (self::$assetSetNameTemplate == null) {
            self::$assetSetNameTemplate = new PathTemplate('customers/{customer_id}/assetSets/{asset_set_id}');
        }

        return self::$assetSetNameTemplate;
    }

    private static function getAssetSetAssetNameTemplate()
    {
        if (self::$assetSetAssetNameTemplate == null) {
            self::$assetSetAssetNameTemplate = new PathTemplate('customers/{customer_id}/assetSetAssets/{asset_set_id}~{asset_id}');
        }

        return self::$assetSetAssetNameTemplate;
    }

    private static function getAudienceNameTemplate()
    {
        if (self::$audienceNameTemplate == null) {
            self::$audienceNameTemplate = new PathTemplate('customers/{customer_id}/audiences/{audience_id}');
        }

        return self::$audienceNameTemplate;
    }

    private static function getBatchJobNameTemplate()
    {
        if (self::$batchJobNameTemplate == null) {
            self::$batchJobNameTemplate = new PathTemplate('customers/{customer_id}/batchJobs/{batch_job_id}');
        }

        return self::$batchJobNameTemplate;
    }

    private static function getBiddingDataExclusionNameTemplate()
    {
        if (self::$biddingDataExclusionNameTemplate == null) {
            self::$biddingDataExclusionNameTemplate = new PathTemplate('customers/{customer_id}/biddingDataExclusions/{seasonality_event_id}');
        }

        return self::$biddingDataExclusionNameTemplate;
    }

    private static function getBiddingSeasonalityAdjustmentNameTemplate()
    {
        if (self::$biddingSeasonalityAdjustmentNameTemplate == null) {
            self::$biddingSeasonalityAdjustmentNameTemplate = new PathTemplate('customers/{customer_id}/biddingSeasonalityAdjustments/{seasonality_event_id}');
        }

        return self::$biddingSeasonalityAdjustmentNameTemplate;
    }

    private static function getBiddingStrategyNameTemplate()
    {
        if (self::$biddingStrategyNameTemplate == null) {
            self::$biddingStrategyNameTemplate = new PathTemplate('customers/{customer_id}/biddingStrategies/{bidding_strategy_id}');
        }

        return self::$biddingStrategyNameTemplate;
    }

    private static function getCampaignNameTemplate()
    {
        if (self::$campaignNameTemplate == null) {
            self::$campaignNameTemplate = new PathTemplate('customers/{customer_id}/campaigns/{campaign_id}');
        }

        return self::$campaignNameTemplate;
    }

    private static function getCampaignAssetNameTemplate()
    {
        if (self::$campaignAssetNameTemplate == null) {
            self::$campaignAssetNameTemplate = new PathTemplate('customers/{customer_id}/campaignAssets/{campaign_id}~{asset_id}~{field_type}');
        }

        return self::$campaignAssetNameTemplate;
    }

    private static function getCampaignAssetSetNameTemplate()
    {
        if (self::$campaignAssetSetNameTemplate == null) {
            self::$campaignAssetSetNameTemplate = new PathTemplate('customers/{customer_id}/campaignAssetSets/{campaign_id}~{asset_set_id}');
        }

        return self::$campaignAssetSetNameTemplate;
    }

    private static function getCampaignBidModifierNameTemplate()
    {
        if (self::$campaignBidModifierNameTemplate == null) {
            self::$campaignBidModifierNameTemplate = new PathTemplate('customers/{customer_id}/campaignBidModifiers/{campaign_id}~{criterion_id}');
        }

        return self::$campaignBidModifierNameTemplate;
    }

    private static function getCampaignBudgetNameTemplate()
    {
        if (self::$campaignBudgetNameTemplate == null) {
            self::$campaignBudgetNameTemplate = new PathTemplate('customers/{customer_id}/campaignBudgets/{campaign_budget_id}');
        }

        return self::$campaignBudgetNameTemplate;
    }

    private static function getCampaignConversionGoalNameTemplate()
    {
        if (self::$campaignConversionGoalNameTemplate == null) {
            self::$campaignConversionGoalNameTemplate = new PathTemplate('customers/{customer_id}/campaignConversionGoals/{campaign_id}~{category}~{source}');
        }

        return self::$campaignConversionGoalNameTemplate;
    }

    private static function getCampaignCriterionNameTemplate()
    {
        if (self::$campaignCriterionNameTemplate == null) {
            self::$campaignCriterionNameTemplate = new PathTemplate('customers/{customer_id}/campaignCriteria/{campaign_id}~{criterion_id}');
        }

        return self::$campaignCriterionNameTemplate;
    }

    private static function getCampaignCustomizerNameTemplate()
    {
        if (self::$campaignCustomizerNameTemplate == null) {
            self::$campaignCustomizerNameTemplate = new PathTemplate('customers/{customer_id}/campaignCustomizers/{campaign_id}~{customizer_attribute_id}');
        }

        return self::$campaignCustomizerNameTemplate;
    }

    private static function getCampaignDraftNameTemplate()
    {
        if (self::$campaignDraftNameTemplate == null) {
            self::$campaignDraftNameTemplate = new PathTemplate('customers/{customer_id}/campaignDrafts/{base_campaign_id}~{draft_id}');
        }

        return self::$campaignDraftNameTemplate;
    }

    private static function getCampaignExtensionSettingNameTemplate()
    {
        if (self::$campaignExtensionSettingNameTemplate == null) {
            self::$campaignExtensionSettingNameTemplate = new PathTemplate('customers/{customer_id}/campaignExtensionSettings/{campaign_id}~{extension_type}');
        }

        return self::$campaignExtensionSettingNameTemplate;
    }

    private static function getCampaignFeedNameTemplate()
    {
        if (self::$campaignFeedNameTemplate == null) {
            self::$campaignFeedNameTemplate = new PathTemplate('customers/{customer_id}/campaignFeeds/{campaign_id}~{feed_id}');
        }

        return self::$campaignFeedNameTemplate;
    }

    private static function getCampaignGroupNameTemplate()
    {
        if (self::$campaignGroupNameTemplate == null) {
            self::$campaignGroupNameTemplate = new PathTemplate('customers/{customer_id}/campaignGroups/{campaign_group_id}');
        }

        return self::$campaignGroupNameTemplate;
    }

    private static function getCampaignLabelNameTemplate()
    {
        if (self::$campaignLabelNameTemplate == null) {
            self::$campaignLabelNameTemplate = new PathTemplate('customers/{customer_id}/campaignLabels/{campaign_id}~{label_id}');
        }

        return self::$campaignLabelNameTemplate;
    }

    private static function getCampaignSharedSetNameTemplate()
    {
        if (self::$campaignSharedSetNameTemplate == null) {
            self::$campaignSharedSetNameTemplate = new PathTemplate('customers/{customer_id}/campaignSharedSets/{campaign_id}~{shared_set_id}');
        }

        return self::$campaignSharedSetNameTemplate;
    }

    private static function getCombinedAudienceNameTemplate()
    {
        if (self::$combinedAudienceNameTemplate == null) {
            self::$combinedAudienceNameTemplate = new PathTemplate('customers/{customer_id}/combinedAudiences/{combined_audience_id}');
        }

        return self::$combinedAudienceNameTemplate;
    }

    private static function getConversionActionNameTemplate()
    {
        if (self::$conversionActionNameTemplate == null) {
            self::$conversionActionNameTemplate = new PathTemplate('customers/{customer_id}/conversionActions/{conversion_action_id}');
        }

        return self::$conversionActionNameTemplate;
    }

    private static function getConversionCustomVariableNameTemplate()
    {
        if (self::$conversionCustomVariableNameTemplate == null) {
            self::$conversionCustomVariableNameTemplate = new PathTemplate('customers/{customer_id}/conversionCustomVariables/{conversion_custom_variable_id}');
        }

        return self::$conversionCustomVariableNameTemplate;
    }

    private static function getConversionGoalCampaignConfigNameTemplate()
    {
        if (self::$conversionGoalCampaignConfigNameTemplate == null) {
            self::$conversionGoalCampaignConfigNameTemplate = new PathTemplate('customers/{customer_id}/conversionGoalCampaignConfigs/{campaign_id}');
        }

        return self::$conversionGoalCampaignConfigNameTemplate;
    }

    private static function getConversionValueRuleNameTemplate()
    {
        if (self::$conversionValueRuleNameTemplate == null) {
            self::$conversionValueRuleNameTemplate = new PathTemplate('customers/{customer_id}/conversionValueRules/{conversion_value_rule_id}');
        }

        return self::$conversionValueRuleNameTemplate;
    }

    private static function getConversionValueRuleSetNameTemplate()
    {
        if (self::$conversionValueRuleSetNameTemplate == null) {
            self::$conversionValueRuleSetNameTemplate = new PathTemplate('customers/{customer_id}/conversionValueRuleSets/{conversion_value_rule_set_id}');
        }

        return self::$conversionValueRuleSetNameTemplate;
    }

    private static function getCustomConversionGoalNameTemplate()
    {
        if (self::$customConversionGoalNameTemplate == null) {
            self::$customConversionGoalNameTemplate = new PathTemplate('customers/{customer_id}/customConversionGoals/{goal_id}');
        }

        return self::$customConversionGoalNameTemplate;
    }

    private static function getCustomerNameTemplate()
    {
        if (self::$customerNameTemplate == null) {
            self::$customerNameTemplate = new PathTemplate('customers/{customer_id}');
        }

        return self::$customerNameTemplate;
    }

    private static function getCustomerAssetNameTemplate()
    {
        if (self::$customerAssetNameTemplate == null) {
            self::$customerAssetNameTemplate = new PathTemplate('customers/{customer_id}/customerAssets/{asset_id}~{field_type}');
        }

        return self::$customerAssetNameTemplate;
    }

    private static function getCustomerConversionGoalNameTemplate()
    {
        if (self::$customerConversionGoalNameTemplate == null) {
            self::$customerConversionGoalNameTemplate = new PathTemplate('customers/{customer_id}/customerConversionGoals/{category}~{source}');
        }

        return self::$customerConversionGoalNameTemplate;
    }

    private static function getCustomerCustomizerNameTemplate()
    {
        if (self::$customerCustomizerNameTemplate == null) {
            self::$customerCustomizerNameTemplate = new PathTemplate('customers/{customer_id}/customerCustomizers/{customizer_attribute_id}');
        }

        return self::$customerCustomizerNameTemplate;
    }

    private static function getCustomerExtensionSettingNameTemplate()
    {
        if (self::$customerExtensionSettingNameTemplate == null) {
            self::$customerExtensionSettingNameTemplate = new PathTemplate('customers/{customer_id}/customerExtensionSettings/{extension_type}');
        }

        return self::$customerExtensionSettingNameTemplate;
    }

    private static function getCustomerFeedNameTemplate()
    {
        if (self::$customerFeedNameTemplate == null) {
            self::$customerFeedNameTemplate = new PathTemplate('customers/{customer_id}/customerFeeds/{feed_id}');
        }

        return self::$customerFeedNameTemplate;
    }

    private static function getCustomerLabelNameTemplate()
    {
        if (self::$customerLabelNameTemplate == null) {
            self::$customerLabelNameTemplate = new PathTemplate('customers/{customer_id}/customerLabels/{label_id}');
        }

        return self::$customerLabelNameTemplate;
    }

    private static function getCustomerNegativeCriterionNameTemplate()
    {
        if (self::$customerNegativeCriterionNameTemplate == null) {
            self::$customerNegativeCriterionNameTemplate = new PathTemplate('customers/{customer_id}/customerNegativeCriteria/{criterion_id}');
        }

        return self::$customerNegativeCriterionNameTemplate;
    }

    private static function getCustomizerAttributeNameTemplate()
    {
        if (self::$customizerAttributeNameTemplate == null) {
            self::$customizerAttributeNameTemplate = new PathTemplate('customers/{customer_id}/customizerAttributes/{customizer_attribute_id}');
        }

        return self::$customizerAttributeNameTemplate;
    }

    private static function getExperimentNameTemplate()
    {
        if (self::$experimentNameTemplate == null) {
            self::$experimentNameTemplate = new PathTemplate('customers/{customer_id}/experiments/{trial_id}');
        }

        return self::$experimentNameTemplate;
    }

    private static function getExperimentArmNameTemplate()
    {
        if (self::$experimentArmNameTemplate == null) {
            self::$experimentArmNameTemplate = new PathTemplate('customers/{customer_id}/experimentArms/{trial_id}~{trial_arm_id}');
        }

        return self::$experimentArmNameTemplate;
    }

    private static function getExtensionFeedItemNameTemplate()
    {
        if (self::$extensionFeedItemNameTemplate == null) {
            self::$extensionFeedItemNameTemplate = new PathTemplate('customers/{customer_id}/extensionFeedItems/{feed_item_id}');
        }

        return self::$extensionFeedItemNameTemplate;
    }

    private static function getFeedNameTemplate()
    {
        if (self::$feedNameTemplate == null) {
            self::$feedNameTemplate = new PathTemplate('customers/{customer_id}/feeds/{feed_id}');
        }

        return self::$feedNameTemplate;
    }

    private static function getFeedItemNameTemplate()
    {
        if (self::$feedItemNameTemplate == null) {
            self::$feedItemNameTemplate = new PathTemplate('customers/{customer_id}/feedItems/{feed_id}~{feed_item_id}');
        }

        return self::$feedItemNameTemplate;
    }

    private static function getFeedItemSetNameTemplate()
    {
        if (self::$feedItemSetNameTemplate == null) {
            self::$feedItemSetNameTemplate = new PathTemplate('customers/{customer_id}/feedItemSets/{feed_id}~{feed_item_set_id}');
        }

        return self::$feedItemSetNameTemplate;
    }

    private static function getFeedItemSetLinkNameTemplate()
    {
        if (self::$feedItemSetLinkNameTemplate == null) {
            self::$feedItemSetLinkNameTemplate = new PathTemplate('customers/{customer_id}/feedItemSetLinks/{feed_id}~{feed_item_set_id}~{feed_item_id}');
        }

        return self::$feedItemSetLinkNameTemplate;
    }

    private static function getFeedItemTargetNameTemplate()
    {
        if (self::$feedItemTargetNameTemplate == null) {
            self::$feedItemTargetNameTemplate = new PathTemplate('customers/{customer_id}/feedItemTargets/{feed_id}~{feed_item_id}~{feed_item_target_type}~{feed_item_target_id}');
        }

        return self::$feedItemTargetNameTemplate;
    }

    private static function getFeedMappingNameTemplate()
    {
        if (self::$feedMappingNameTemplate == null) {
            self::$feedMappingNameTemplate = new PathTemplate('customers/{customer_id}/feedMappings/{feed_id}~{feed_mapping_id}');
        }

        return self::$feedMappingNameTemplate;
    }

    private static function getGeoTargetConstantNameTemplate()
    {
        if (self::$geoTargetConstantNameTemplate == null) {
            self::$geoTargetConstantNameTemplate = new PathTemplate('geoTargetConstants/{criterion_id}');
        }

        return self::$geoTargetConstantNameTemplate;
    }

    private static function getKeywordPlanNameTemplate()
    {
        if (self::$keywordPlanNameTemplate == null) {
            self::$keywordPlanNameTemplate = new PathTemplate('customers/{customer_id}/keywordPlans/{keyword_plan_id}');
        }

        return self::$keywordPlanNameTemplate;
    }

    private static function getKeywordPlanAdGroupNameTemplate()
    {
        if (self::$keywordPlanAdGroupNameTemplate == null) {
            self::$keywordPlanAdGroupNameTemplate = new PathTemplate('customers/{customer_id}/keywordPlanAdGroups/{keyword_plan_ad_group_id}');
        }

        return self::$keywordPlanAdGroupNameTemplate;
    }

    private static function getKeywordPlanAdGroupKeywordNameTemplate()
    {
        if (self::$keywordPlanAdGroupKeywordNameTemplate == null) {
            self::$keywordPlanAdGroupKeywordNameTemplate = new PathTemplate('customers/{customer_id}/keywordPlanAdGroupKeywords/{keyword_plan_ad_group_keyword_id}');
        }

        return self::$keywordPlanAdGroupKeywordNameTemplate;
    }

    private static function getKeywordPlanCampaignNameTemplate()
    {
        if (self::$keywordPlanCampaignNameTemplate == null) {
            self::$keywordPlanCampaignNameTemplate = new PathTemplate('customers/{customer_id}/keywordPlanCampaigns/{keyword_plan_campaign_id}');
        }

        return self::$keywordPlanCampaignNameTemplate;
    }

    private static function getKeywordPlanCampaignKeywordNameTemplate()
    {
        if (self::$keywordPlanCampaignKeywordNameTemplate == null) {
            self::$keywordPlanCampaignKeywordNameTemplate = new PathTemplate('customers/{customer_id}/keywordPlanCampaignKeywords/{keyword_plan_campaign_keyword_id}');
        }

        return self::$keywordPlanCampaignKeywordNameTemplate;
    }

    private static function getLabelNameTemplate()
    {
        if (self::$labelNameTemplate == null) {
            self::$labelNameTemplate = new PathTemplate('customers/{customer_id}/labels/{label_id}');
        }

        return self::$labelNameTemplate;
    }

    private static function getLanguageConstantNameTemplate()
    {
        if (self::$languageConstantNameTemplate == null) {
            self::$languageConstantNameTemplate = new PathTemplate('languageConstants/{criterion_id}');
        }

        return self::$languageConstantNameTemplate;
    }

    private static function getMobileAppCategoryConstantNameTemplate()
    {
        if (self::$mobileAppCategoryConstantNameTemplate == null) {
            self::$mobileAppCategoryConstantNameTemplate = new PathTemplate('mobileAppCategoryConstants/{mobile_app_category_id}');
        }

        return self::$mobileAppCategoryConstantNameTemplate;
    }

    private static function getRecommendationSubscriptionNameTemplate()
    {
        if (self::$recommendationSubscriptionNameTemplate == null) {
            self::$recommendationSubscriptionNameTemplate = new PathTemplate('customers/{customer_id}/recommendationSubscriptions/{recommendation_type}');
        }

        return self::$recommendationSubscriptionNameTemplate;
    }

    private static function getRemarketingActionNameTemplate()
    {
        if (self::$remarketingActionNameTemplate == null) {
            self::$remarketingActionNameTemplate = new PathTemplate('customers/{customer_id}/remarketingActions/{remarketing_action_id}');
        }

        return self::$remarketingActionNameTemplate;
    }

    private static function getSharedCriterionNameTemplate()
    {
        if (self::$sharedCriterionNameTemplate == null) {
            self::$sharedCriterionNameTemplate = new PathTemplate('customers/{customer_id}/sharedCriteria/{shared_set_id}~{criterion_id}');
        }

        return self::$sharedCriterionNameTemplate;
    }

    private static function getSharedSetNameTemplate()
    {
        if (self::$sharedSetNameTemplate == null) {
            self::$sharedSetNameTemplate = new PathTemplate('customers/{customer_id}/sharedSets/{shared_set_id}');
        }

        return self::$sharedSetNameTemplate;
    }

    private static function getSmartCampaignSettingNameTemplate()
    {
        if (self::$smartCampaignSettingNameTemplate == null) {
            self::$smartCampaignSettingNameTemplate = new PathTemplate('customers/{customer_id}/smartCampaignSettings/{campaign_id}');
        }

        return self::$smartCampaignSettingNameTemplate;
    }

    private static function getTopicConstantNameTemplate()
    {
        if (self::$topicConstantNameTemplate == null) {
            self::$topicConstantNameTemplate = new PathTemplate('topicConstants/{topic_id}');
        }

        return self::$topicConstantNameTemplate;
    }

    private static function getUserInterestNameTemplate()
    {
        if (self::$userInterestNameTemplate == null) {
            self::$userInterestNameTemplate = new PathTemplate('customers/{customer_id}/userInterests/{user_interest_id}');
        }

        return self::$userInterestNameTemplate;
    }

    private static function getUserListNameTemplate()
    {
        if (self::$userListNameTemplate == null) {
            self::$userListNameTemplate = new PathTemplate('customers/{customer_id}/userLists/{user_list_id}');
        }

        return self::$userListNameTemplate;
    }

    private static function getPathTemplateMap()
    {
        if (self::$pathTemplateMap == null) {
            self::$pathTemplateMap = [
                'accessibleBiddingStrategy' => self::getAccessibleBiddingStrategyNameTemplate(),
                'ad' => self::getAdNameTemplate(),
                'adGroup' => self::getAdGroupNameTemplate(),
                'adGroupAd' => self::getAdGroupAdNameTemplate(),
                'adGroupAdLabel' => self::getAdGroupAdLabelNameTemplate(),
                'adGroupAsset' => self::getAdGroupAssetNameTemplate(),
                'adGroupBidModifier' => self::getAdGroupBidModifierNameTemplate(),
                'adGroupCriterion' => self::getAdGroupCriterionNameTemplate(),
                'adGroupCriterionCustomizer' => self::getAdGroupCriterionCustomizerNameTemplate(),
                'adGroupCriterionLabel' => self::getAdGroupCriterionLabelNameTemplate(),
                'adGroupCustomizer' => self::getAdGroupCustomizerNameTemplate(),
                'adGroupExtensionSetting' => self::getAdGroupExtensionSettingNameTemplate(),
                'adGroupFeed' => self::getAdGroupFeedNameTemplate(),
                'adGroupLabel' => self::getAdGroupLabelNameTemplate(),
                'adParameter' => self::getAdParameterNameTemplate(),
                'asset' => self::getAssetNameTemplate(),
                'assetGroup' => self::getAssetGroupNameTemplate(),
                'assetGroupAsset' => self::getAssetGroupAssetNameTemplate(),
                'assetGroupListingGroupFilter' => self::getAssetGroupListingGroupFilterNameTemplate(),
                'assetGroupSignal' => self::getAssetGroupSignalNameTemplate(),
                'assetSet' => self::getAssetSetNameTemplate(),
                'assetSetAsset' => self::getAssetSetAssetNameTemplate(),
                'audience' => self::getAudienceNameTemplate(),
                'batchJob' => self::getBatchJobNameTemplate(),
                'biddingDataExclusion' => self::getBiddingDataExclusionNameTemplate(),
                'biddingSeasonalityAdjustment' => self::getBiddingSeasonalityAdjustmentNameTemplate(),
                'biddingStrategy' => self::getBiddingStrategyNameTemplate(),
                'campaign' => self::getCampaignNameTemplate(),
                'campaignAsset' => self::getCampaignAssetNameTemplate(),
                'campaignAssetSet' => self::getCampaignAssetSetNameTemplate(),
                'campaignBidModifier' => self::getCampaignBidModifierNameTemplate(),
                'campaignBudget' => self::getCampaignBudgetNameTemplate(),
                'campaignConversionGoal' => self::getCampaignConversionGoalNameTemplate(),
                'campaignCriterion' => self::getCampaignCriterionNameTemplate(),
                'campaignCustomizer' => self::getCampaignCustomizerNameTemplate(),
                'campaignDraft' => self::getCampaignDraftNameTemplate(),
                'campaignExtensionSetting' => self::getCampaignExtensionSettingNameTemplate(),
                'campaignFeed' => self::getCampaignFeedNameTemplate(),
                'campaignGroup' => self::getCampaignGroupNameTemplate(),
                'campaignLabel' => self::getCampaignLabelNameTemplate(),
                'campaignSharedSet' => self::getCampaignSharedSetNameTemplate(),
                'combinedAudience' => self::getCombinedAudienceNameTemplate(),
                'conversionAction' => self::getConversionActionNameTemplate(),
                'conversionCustomVariable' => self::getConversionCustomVariableNameTemplate(),
                'conversionGoalCampaignConfig' => self::getConversionGoalCampaignConfigNameTemplate(),
                'conversionValueRule' => self::getConversionValueRuleNameTemplate(),
                'conversionValueRuleSet' => self::getConversionValueRuleSetNameTemplate(),
                'customConversionGoal' => self::getCustomConversionGoalNameTemplate(),
                'customer' => self::getCustomerNameTemplate(),
                'customerAsset' => self::getCustomerAssetNameTemplate(),
                'customerConversionGoal' => self::getCustomerConversionGoalNameTemplate(),
                'customerCustomizer' => self::getCustomerCustomizerNameTemplate(),
                'customerExtensionSetting' => self::getCustomerExtensionSettingNameTemplate(),
                'customerFeed' => self::getCustomerFeedNameTemplate(),
                'customerLabel' => self::getCustomerLabelNameTemplate(),
                'customerNegativeCriterion' => self::getCustomerNegativeCriterionNameTemplate(),
                'customizerAttribute' => self::getCustomizerAttributeNameTemplate(),
                'experiment' => self::getExperimentNameTemplate(),
                'experimentArm' => self::getExperimentArmNameTemplate(),
                'extensionFeedItem' => self::getExtensionFeedItemNameTemplate(),
                'feed' => self::getFeedNameTemplate(),
                'feedItem' => self::getFeedItemNameTemplate(),
                'feedItemSet' => self::getFeedItemSetNameTemplate(),
                'feedItemSetLink' => self::getFeedItemSetLinkNameTemplate(),
                'feedItemTarget' => self::getFeedItemTargetNameTemplate(),
                'feedMapping' => self::getFeedMappingNameTemplate(),
                'geoTargetConstant' => self::getGeoTargetConstantNameTemplate(),
                'keywordPlan' => self::getKeywordPlanNameTemplate(),
                'keywordPlanAdGroup' => self::getKeywordPlanAdGroupNameTemplate(),
                'keywordPlanAdGroupKeyword' => self::getKeywordPlanAdGroupKeywordNameTemplate(),
                'keywordPlanCampaign' => self::getKeywordPlanCampaignNameTemplate(),
                'keywordPlanCampaignKeyword' => self::getKeywordPlanCampaignKeywordNameTemplate(),
                'label' => self::getLabelNameTemplate(),
                'languageConstant' => self::getLanguageConstantNameTemplate(),
                'mobileAppCategoryConstant' => self::getMobileAppCategoryConstantNameTemplate(),
                'recommendationSubscription' => self::getRecommendationSubscriptionNameTemplate(),
                'remarketingAction' => self::getRemarketingActionNameTemplate(),
                'sharedCriterion' => self::getSharedCriterionNameTemplate(),
                'sharedSet' => self::getSharedSetNameTemplate(),
                'smartCampaignSetting' => self::getSmartCampaignSettingNameTemplate(),
                'topicConstant' => self::getTopicConstantNameTemplate(),
                'userInterest' => self::getUserInterestNameTemplate(),
                'userList' => self::getUserListNameTemplate(),
            ];
        }

        return self::$pathTemplateMap;
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * accessible_bidding_strategy resource.
     *
     * @param string $customerId
     * @param string $biddingStrategyId
     *
     * @return string The formatted accessible_bidding_strategy resource.
     */
    public static function accessibleBiddingStrategyName($customerId, $biddingStrategyId)
    {
        return self::getAccessibleBiddingStrategyNameTemplate()->render([
            'customer_id' => $customerId,
            'bidding_strategy_id' => $biddingStrategyId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a ad resource.
     *
     * @param string $customerId
     * @param string $adId
     *
     * @return string The formatted ad resource.
     */
    public static function adName($customerId, $adId)
    {
        return self::getAdNameTemplate()->render([
            'customer_id' => $customerId,
            'ad_id' => $adId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a ad_group
     * resource.
     *
     * @param string $customerId
     * @param string $adGroupId
     *
     * @return string The formatted ad_group resource.
     */
    public static function adGroupName($customerId, $adGroupId)
    {
        return self::getAdGroupNameTemplate()->render([
            'customer_id' => $customerId,
            'ad_group_id' => $adGroupId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a ad_group_ad
     * resource.
     *
     * @param string $customerId
     * @param string $adGroupId
     * @param string $adId
     *
     * @return string The formatted ad_group_ad resource.
     */
    public static function adGroupAdName($customerId, $adGroupId, $adId)
    {
        return self::getAdGroupAdNameTemplate()->render([
            'customer_id' => $customerId,
            'ad_group_id' => $adGroupId,
            'ad_id' => $adId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * ad_group_ad_label resource.
     *
     * @param string $customerId
     * @param string $adGroupId
     * @param string $adId
     * @param string $labelId
     *
     * @return string The formatted ad_group_ad_label resource.
     */
    public static function adGroupAdLabelName($customerId, $adGroupId, $adId, $labelId)
    {
        return self::getAdGroupAdLabelNameTemplate()->render([
            'customer_id' => $customerId,
            'ad_group_id' => $adGroupId,
            'ad_id' => $adId,
            'label_id' => $labelId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * ad_group_asset resource.
     *
     * @param string $customerId
     * @param string $adGroupId
     * @param string $assetId
     * @param string $fieldType
     *
     * @return string The formatted ad_group_asset resource.
     */
    public static function adGroupAssetName($customerId, $adGroupId, $assetId, $fieldType)
    {
        return self::getAdGroupAssetNameTemplate()->render([
            'customer_id' => $customerId,
            'ad_group_id' => $adGroupId,
            'asset_id' => $assetId,
            'field_type' => $fieldType,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * ad_group_bid_modifier resource.
     *
     * @param string $customerId
     * @param string $adGroupId
     * @param string $criterionId
     *
     * @return string The formatted ad_group_bid_modifier resource.
     */
    public static function adGroupBidModifierName($customerId, $adGroupId, $criterionId)
    {
        return self::getAdGroupBidModifierNameTemplate()->render([
            'customer_id' => $customerId,
            'ad_group_id' => $adGroupId,
            'criterion_id' => $criterionId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * ad_group_criterion resource.
     *
     * @param string $customerId
     * @param string $adGroupId
     * @param string $criterionId
     *
     * @return string The formatted ad_group_criterion resource.
     */
    public static function adGroupCriterionName($customerId, $adGroupId, $criterionId)
    {
        return self::getAdGroupCriterionNameTemplate()->render([
            'customer_id' => $customerId,
            'ad_group_id' => $adGroupId,
            'criterion_id' => $criterionId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * ad_group_criterion_customizer resource.
     *
     * @param string $customerId
     * @param string $adGroupId
     * @param string $criterionId
     * @param string $customizerAttributeId
     *
     * @return string The formatted ad_group_criterion_customizer resource.
     */
    public static function adGroupCriterionCustomizerName($customerId, $adGroupId, $criterionId, $customizerAttributeId)
    {
        return self::getAdGroupCriterionCustomizerNameTemplate()->render([
            'customer_id' => $customerId,
            'ad_group_id' => $adGroupId,
            'criterion_id' => $criterionId,
            'customizer_attribute_id' => $customizerAttributeId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * ad_group_criterion_label resource.
     *
     * @param string $customerId
     * @param string $adGroupId
     * @param string $criterionId
     * @param string $labelId
     *
     * @return string The formatted ad_group_criterion_label resource.
     */
    public static function adGroupCriterionLabelName($customerId, $adGroupId, $criterionId, $labelId)
    {
        return self::getAdGroupCriterionLabelNameTemplate()->render([
            'customer_id' => $customerId,
            'ad_group_id' => $adGroupId,
            'criterion_id' => $criterionId,
            'label_id' => $labelId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * ad_group_customizer resource.
     *
     * @param string $customerId
     * @param string $adGroupId
     * @param string $customizerAttributeId
     *
     * @return string The formatted ad_group_customizer resource.
     */
    public static function adGroupCustomizerName($customerId, $adGroupId, $customizerAttributeId)
    {
        return self::getAdGroupCustomizerNameTemplate()->render([
            'customer_id' => $customerId,
            'ad_group_id' => $adGroupId,
            'customizer_attribute_id' => $customizerAttributeId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * ad_group_extension_setting resource.
     *
     * @param string $customerId
     * @param string $adGroupId
     * @param string $extensionType
     *
     * @return string The formatted ad_group_extension_setting resource.
     */
    public static function adGroupExtensionSettingName($customerId, $adGroupId, $extensionType)
    {
        return self::getAdGroupExtensionSettingNameTemplate()->render([
            'customer_id' => $customerId,
            'ad_group_id' => $adGroupId,
            'extension_type' => $extensionType,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * ad_group_feed resource.
     *
     * @param string $customerId
     * @param string $adGroupId
     * @param string $feedId
     *
     * @return string The formatted ad_group_feed resource.
     */
    public static function adGroupFeedName($customerId, $adGroupId, $feedId)
    {
        return self::getAdGroupFeedNameTemplate()->render([
            'customer_id' => $customerId,
            'ad_group_id' => $adGroupId,
            'feed_id' => $feedId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * ad_group_label resource.
     *
     * @param string $customerId
     * @param string $adGroupId
     * @param string $labelId
     *
     * @return string The formatted ad_group_label resource.
     */
    public static function adGroupLabelName($customerId, $adGroupId, $labelId)
    {
        return self::getAdGroupLabelNameTemplate()->render([
            'customer_id' => $customerId,
            'ad_group_id' => $adGroupId,
            'label_id' => $labelId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a ad_parameter
     * resource.
     *
     * @param string $customerId
     * @param string $adGroupId
     * @param string $criterionId
     * @param string $parameterIndex
     *
     * @return string The formatted ad_parameter resource.
     */
    public static function adParameterName($customerId, $adGroupId, $criterionId, $parameterIndex)
    {
        return self::getAdParameterNameTemplate()->render([
            'customer_id' => $customerId,
            'ad_group_id' => $adGroupId,
            'criterion_id' => $criterionId,
            'parameter_index' => $parameterIndex,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a asset
     * resource.
     *
     * @param string $customerId
     * @param string $assetId
     *
     * @return string The formatted asset resource.
     */
    public static function assetName($customerId, $assetId)
    {
        return self::getAssetNameTemplate()->render([
            'customer_id' => $customerId,
            'asset_id' => $assetId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a asset_group
     * resource.
     *
     * @param string $customerId
     * @param string $assetGroupId
     *
     * @return string The formatted asset_group resource.
     */
    public static function assetGroupName($customerId, $assetGroupId)
    {
        return self::getAssetGroupNameTemplate()->render([
            'customer_id' => $customerId,
            'asset_group_id' => $assetGroupId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * asset_group_asset resource.
     *
     * @param string $customerId
     * @param string $assetGroupId
     * @param string $assetId
     * @param string $fieldType
     *
     * @return string The formatted asset_group_asset resource.
     */
    public static function assetGroupAssetName($customerId, $assetGroupId, $assetId, $fieldType)
    {
        return self::getAssetGroupAssetNameTemplate()->render([
            'customer_id' => $customerId,
            'asset_group_id' => $assetGroupId,
            'asset_id' => $assetId,
            'field_type' => $fieldType,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * asset_group_listing_group_filter resource.
     *
     * @param string $customerId
     * @param string $assetGroupId
     * @param string $listingGroupFilterId
     *
     * @return string The formatted asset_group_listing_group_filter resource.
     */
    public static function assetGroupListingGroupFilterName($customerId, $assetGroupId, $listingGroupFilterId)
    {
        return self::getAssetGroupListingGroupFilterNameTemplate()->render([
            'customer_id' => $customerId,
            'asset_group_id' => $assetGroupId,
            'listing_group_filter_id' => $listingGroupFilterId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * asset_group_signal resource.
     *
     * @param string $customerId
     * @param string $assetGroupId
     * @param string $criterionId
     *
     * @return string The formatted asset_group_signal resource.
     */
    public static function assetGroupSignalName($customerId, $assetGroupId, $criterionId)
    {
        return self::getAssetGroupSignalNameTemplate()->render([
            'customer_id' => $customerId,
            'asset_group_id' => $assetGroupId,
            'criterion_id' => $criterionId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a asset_set
     * resource.
     *
     * @param string $customerId
     * @param string $assetSetId
     *
     * @return string The formatted asset_set resource.
     */
    public static function assetSetName($customerId, $assetSetId)
    {
        return self::getAssetSetNameTemplate()->render([
            'customer_id' => $customerId,
            'asset_set_id' => $assetSetId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * asset_set_asset resource.
     *
     * @param string $customerId
     * @param string $assetSetId
     * @param string $assetId
     *
     * @return string The formatted asset_set_asset resource.
     */
    public static function assetSetAssetName($customerId, $assetSetId, $assetId)
    {
        return self::getAssetSetAssetNameTemplate()->render([
            'customer_id' => $customerId,
            'asset_set_id' => $assetSetId,
            'asset_id' => $assetId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a audience
     * resource.
     *
     * @param string $customerId
     * @param string $audienceId
     *
     * @return string The formatted audience resource.
     */
    public static function audienceName($customerId, $audienceId)
    {
        return self::getAudienceNameTemplate()->render([
            'customer_id' => $customerId,
            'audience_id' => $audienceId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a batch_job
     * resource.
     *
     * @param string $customerId
     * @param string $batchJobId
     *
     * @return string The formatted batch_job resource.
     */
    public static function batchJobName($customerId, $batchJobId)
    {
        return self::getBatchJobNameTemplate()->render([
            'customer_id' => $customerId,
            'batch_job_id' => $batchJobId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * bidding_data_exclusion resource.
     *
     * @param string $customerId
     * @param string $seasonalityEventId
     *
     * @return string The formatted bidding_data_exclusion resource.
     */
    public static function biddingDataExclusionName($customerId, $seasonalityEventId)
    {
        return self::getBiddingDataExclusionNameTemplate()->render([
            'customer_id' => $customerId,
            'seasonality_event_id' => $seasonalityEventId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * bidding_seasonality_adjustment resource.
     *
     * @param string $customerId
     * @param string $seasonalityEventId
     *
     * @return string The formatted bidding_seasonality_adjustment resource.
     */
    public static function biddingSeasonalityAdjustmentName($customerId, $seasonalityEventId)
    {
        return self::getBiddingSeasonalityAdjustmentNameTemplate()->render([
            'customer_id' => $customerId,
            'seasonality_event_id' => $seasonalityEventId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * bidding_strategy resource.
     *
     * @param string $customerId
     * @param string $biddingStrategyId
     *
     * @return string The formatted bidding_strategy resource.
     */
    public static function biddingStrategyName($customerId, $biddingStrategyId)
    {
        return self::getBiddingStrategyNameTemplate()->render([
            'customer_id' => $customerId,
            'bidding_strategy_id' => $biddingStrategyId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a campaign
     * resource.
     *
     * @param string $customerId
     * @param string $campaignId
     *
     * @return string The formatted campaign resource.
     */
    public static function campaignName($customerId, $campaignId)
    {
        return self::getCampaignNameTemplate()->render([
            'customer_id' => $customerId,
            'campaign_id' => $campaignId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * campaign_asset resource.
     *
     * @param string $customerId
     * @param string $campaignId
     * @param string $assetId
     * @param string $fieldType
     *
     * @return string The formatted campaign_asset resource.
     */
    public static function campaignAssetName($customerId, $campaignId, $assetId, $fieldType)
    {
        return self::getCampaignAssetNameTemplate()->render([
            'customer_id' => $customerId,
            'campaign_id' => $campaignId,
            'asset_id' => $assetId,
            'field_type' => $fieldType,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * campaign_asset_set resource.
     *
     * @param string $customerId
     * @param string $campaignId
     * @param string $assetSetId
     *
     * @return string The formatted campaign_asset_set resource.
     */
    public static function campaignAssetSetName($customerId, $campaignId, $assetSetId)
    {
        return self::getCampaignAssetSetNameTemplate()->render([
            'customer_id' => $customerId,
            'campaign_id' => $campaignId,
            'asset_set_id' => $assetSetId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * campaign_bid_modifier resource.
     *
     * @param string $customerId
     * @param string $campaignId
     * @param string $criterionId
     *
     * @return string The formatted campaign_bid_modifier resource.
     */
    public static function campaignBidModifierName($customerId, $campaignId, $criterionId)
    {
        return self::getCampaignBidModifierNameTemplate()->render([
            'customer_id' => $customerId,
            'campaign_id' => $campaignId,
            'criterion_id' => $criterionId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * campaign_budget resource.
     *
     * @param string $customerId
     * @param string $campaignBudgetId
     *
     * @return string The formatted campaign_budget resource.
     */
    public static function campaignBudgetName($customerId, $campaignBudgetId)
    {
        return self::getCampaignBudgetNameTemplate()->render([
            'customer_id' => $customerId,
            'campaign_budget_id' => $campaignBudgetId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * campaign_conversion_goal resource.
     *
     * @param string $customerId
     * @param string $campaignId
     * @param string $category
     * @param string $source
     *
     * @return string The formatted campaign_conversion_goal resource.
     */
    public static function campaignConversionGoalName($customerId, $campaignId, $category, $source)
    {
        return self::getCampaignConversionGoalNameTemplate()->render([
            'customer_id' => $customerId,
            'campaign_id' => $campaignId,
            'category' => $category,
            'source' => $source,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * campaign_criterion resource.
     *
     * @param string $customerId
     * @param string $campaignId
     * @param string $criterionId
     *
     * @return string The formatted campaign_criterion resource.
     */
    public static function campaignCriterionName($customerId, $campaignId, $criterionId)
    {
        return self::getCampaignCriterionNameTemplate()->render([
            'customer_id' => $customerId,
            'campaign_id' => $campaignId,
            'criterion_id' => $criterionId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * campaign_customizer resource.
     *
     * @param string $customerId
     * @param string $campaignId
     * @param string $customizerAttributeId
     *
     * @return string The formatted campaign_customizer resource.
     */
    public static function campaignCustomizerName($customerId, $campaignId, $customizerAttributeId)
    {
        return self::getCampaignCustomizerNameTemplate()->render([
            'customer_id' => $customerId,
            'campaign_id' => $campaignId,
            'customizer_attribute_id' => $customizerAttributeId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * campaign_draft resource.
     *
     * @param string $customerId
     * @param string $baseCampaignId
     * @param string $draftId
     *
     * @return string The formatted campaign_draft resource.
     */
    public static function campaignDraftName($customerId, $baseCampaignId, $draftId)
    {
        return self::getCampaignDraftNameTemplate()->render([
            'customer_id' => $customerId,
            'base_campaign_id' => $baseCampaignId,
            'draft_id' => $draftId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * campaign_extension_setting resource.
     *
     * @param string $customerId
     * @param string $campaignId
     * @param string $extensionType
     *
     * @return string The formatted campaign_extension_setting resource.
     */
    public static function campaignExtensionSettingName($customerId, $campaignId, $extensionType)
    {
        return self::getCampaignExtensionSettingNameTemplate()->render([
            'customer_id' => $customerId,
            'campaign_id' => $campaignId,
            'extension_type' => $extensionType,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * campaign_feed resource.
     *
     * @param string $customerId
     * @param string $campaignId
     * @param string $feedId
     *
     * @return string The formatted campaign_feed resource.
     */
    public static function campaignFeedName($customerId, $campaignId, $feedId)
    {
        return self::getCampaignFeedNameTemplate()->render([
            'customer_id' => $customerId,
            'campaign_id' => $campaignId,
            'feed_id' => $feedId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * campaign_group resource.
     *
     * @param string $customerId
     * @param string $campaignGroupId
     *
     * @return string The formatted campaign_group resource.
     */
    public static function campaignGroupName($customerId, $campaignGroupId)
    {
        return self::getCampaignGroupNameTemplate()->render([
            'customer_id' => $customerId,
            'campaign_group_id' => $campaignGroupId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * campaign_label resource.
     *
     * @param string $customerId
     * @param string $campaignId
     * @param string $labelId
     *
     * @return string The formatted campaign_label resource.
     */
    public static function campaignLabelName($customerId, $campaignId, $labelId)
    {
        return self::getCampaignLabelNameTemplate()->render([
            'customer_id' => $customerId,
            'campaign_id' => $campaignId,
            'label_id' => $labelId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * campaign_shared_set resource.
     *
     * @param string $customerId
     * @param string $campaignId
     * @param string $sharedSetId
     *
     * @return string The formatted campaign_shared_set resource.
     */
    public static function campaignSharedSetName($customerId, $campaignId, $sharedSetId)
    {
        return self::getCampaignSharedSetNameTemplate()->render([
            'customer_id' => $customerId,
            'campaign_id' => $campaignId,
            'shared_set_id' => $sharedSetId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * combined_audience resource.
     *
     * @param string $customerId
     * @param string $combinedAudienceId
     *
     * @return string The formatted combined_audience resource.
     */
    public static function combinedAudienceName($customerId, $combinedAudienceId)
    {
        return self::getCombinedAudienceNameTemplate()->render([
            'customer_id' => $customerId,
            'combined_audience_id' => $combinedAudienceId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * conversion_action resource.
     *
     * @param string $customerId
     * @param string $conversionActionId
     *
     * @return string The formatted conversion_action resource.
     */
    public static function conversionActionName($customerId, $conversionActionId)
    {
        return self::getConversionActionNameTemplate()->render([
            'customer_id' => $customerId,
            'conversion_action_id' => $conversionActionId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * conversion_custom_variable resource.
     *
     * @param string $customerId
     * @param string $conversionCustomVariableId
     *
     * @return string The formatted conversion_custom_variable resource.
     */
    public static function conversionCustomVariableName($customerId, $conversionCustomVariableId)
    {
        return self::getConversionCustomVariableNameTemplate()->render([
            'customer_id' => $customerId,
            'conversion_custom_variable_id' => $conversionCustomVariableId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * conversion_goal_campaign_config resource.
     *
     * @param string $customerId
     * @param string $campaignId
     *
     * @return string The formatted conversion_goal_campaign_config resource.
     */
    public static function conversionGoalCampaignConfigName($customerId, $campaignId)
    {
        return self::getConversionGoalCampaignConfigNameTemplate()->render([
            'customer_id' => $customerId,
            'campaign_id' => $campaignId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * conversion_value_rule resource.
     *
     * @param string $customerId
     * @param string $conversionValueRuleId
     *
     * @return string The formatted conversion_value_rule resource.
     */
    public static function conversionValueRuleName($customerId, $conversionValueRuleId)
    {
        return self::getConversionValueRuleNameTemplate()->render([
            'customer_id' => $customerId,
            'conversion_value_rule_id' => $conversionValueRuleId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * conversion_value_rule_set resource.
     *
     * @param string $customerId
     * @param string $conversionValueRuleSetId
     *
     * @return string The formatted conversion_value_rule_set resource.
     */
    public static function conversionValueRuleSetName($customerId, $conversionValueRuleSetId)
    {
        return self::getConversionValueRuleSetNameTemplate()->render([
            'customer_id' => $customerId,
            'conversion_value_rule_set_id' => $conversionValueRuleSetId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * custom_conversion_goal resource.
     *
     * @param string $customerId
     * @param string $goalId
     *
     * @return string The formatted custom_conversion_goal resource.
     */
    public static function customConversionGoalName($customerId, $goalId)
    {
        return self::getCustomConversionGoalNameTemplate()->render([
            'customer_id' => $customerId,
            'goal_id' => $goalId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a customer
     * resource.
     *
     * @param string $customerId
     *
     * @return string The formatted customer resource.
     */
    public static function customerName($customerId)
    {
        return self::getCustomerNameTemplate()->render([
            'customer_id' => $customerId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * customer_asset resource.
     *
     * @param string $customerId
     * @param string $assetId
     * @param string $fieldType
     *
     * @return string The formatted customer_asset resource.
     */
    public static function customerAssetName($customerId, $assetId, $fieldType)
    {
        return self::getCustomerAssetNameTemplate()->render([
            'customer_id' => $customerId,
            'asset_id' => $assetId,
            'field_type' => $fieldType,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * customer_conversion_goal resource.
     *
     * @param string $customerId
     * @param string $category
     * @param string $source
     *
     * @return string The formatted customer_conversion_goal resource.
     */
    public static function customerConversionGoalName($customerId, $category, $source)
    {
        return self::getCustomerConversionGoalNameTemplate()->render([
            'customer_id' => $customerId,
            'category' => $category,
            'source' => $source,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * customer_customizer resource.
     *
     * @param string $customerId
     * @param string $customizerAttributeId
     *
     * @return string The formatted customer_customizer resource.
     */
    public static function customerCustomizerName($customerId, $customizerAttributeId)
    {
        return self::getCustomerCustomizerNameTemplate()->render([
            'customer_id' => $customerId,
            'customizer_attribute_id' => $customizerAttributeId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * customer_extension_setting resource.
     *
     * @param string $customerId
     * @param string $extensionType
     *
     * @return string The formatted customer_extension_setting resource.
     */
    public static function customerExtensionSettingName($customerId, $extensionType)
    {
        return self::getCustomerExtensionSettingNameTemplate()->render([
            'customer_id' => $customerId,
            'extension_type' => $extensionType,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * customer_feed resource.
     *
     * @param string $customerId
     * @param string $feedId
     *
     * @return string The formatted customer_feed resource.
     */
    public static function customerFeedName($customerId, $feedId)
    {
        return self::getCustomerFeedNameTemplate()->render([
            'customer_id' => $customerId,
            'feed_id' => $feedId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * customer_label resource.
     *
     * @param string $customerId
     * @param string $labelId
     *
     * @return string The formatted customer_label resource.
     */
    public static function customerLabelName($customerId, $labelId)
    {
        return self::getCustomerLabelNameTemplate()->render([
            'customer_id' => $customerId,
            'label_id' => $labelId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * customer_negative_criterion resource.
     *
     * @param string $customerId
     * @param string $criterionId
     *
     * @return string The formatted customer_negative_criterion resource.
     */
    public static function customerNegativeCriterionName($customerId, $criterionId)
    {
        return self::getCustomerNegativeCriterionNameTemplate()->render([
            'customer_id' => $customerId,
            'criterion_id' => $criterionId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * customizer_attribute resource.
     *
     * @param string $customerId
     * @param string $customizerAttributeId
     *
     * @return string The formatted customizer_attribute resource.
     */
    public static function customizerAttributeName($customerId, $customizerAttributeId)
    {
        return self::getCustomizerAttributeNameTemplate()->render([
            'customer_id' => $customerId,
            'customizer_attribute_id' => $customizerAttributeId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a experiment
     * resource.
     *
     * @param string $customerId
     * @param string $trialId
     *
     * @return string The formatted experiment resource.
     */
    public static function experimentName($customerId, $trialId)
    {
        return self::getExperimentNameTemplate()->render([
            'customer_id' => $customerId,
            'trial_id' => $trialId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * experiment_arm resource.
     *
     * @param string $customerId
     * @param string $trialId
     * @param string $trialArmId
     *
     * @return string The formatted experiment_arm resource.
     */
    public static function experimentArmName($customerId, $trialId, $trialArmId)
    {
        return self::getExperimentArmNameTemplate()->render([
            'customer_id' => $customerId,
            'trial_id' => $trialId,
            'trial_arm_id' => $trialArmId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * extension_feed_item resource.
     *
     * @param string $customerId
     * @param string $feedItemId
     *
     * @return string The formatted extension_feed_item resource.
     */
    public static function extensionFeedItemName($customerId, $feedItemId)
    {
        return self::getExtensionFeedItemNameTemplate()->render([
            'customer_id' => $customerId,
            'feed_item_id' => $feedItemId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a feed
     * resource.
     *
     * @param string $customerId
     * @param string $feedId
     *
     * @return string The formatted feed resource.
     */
    public static function feedName($customerId, $feedId)
    {
        return self::getFeedNameTemplate()->render([
            'customer_id' => $customerId,
            'feed_id' => $feedId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a feed_item
     * resource.
     *
     * @param string $customerId
     * @param string $feedId
     * @param string $feedItemId
     *
     * @return string The formatted feed_item resource.
     */
    public static function feedItemName($customerId, $feedId, $feedItemId)
    {
        return self::getFeedItemNameTemplate()->render([
            'customer_id' => $customerId,
            'feed_id' => $feedId,
            'feed_item_id' => $feedItemId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * feed_item_set resource.
     *
     * @param string $customerId
     * @param string $feedId
     * @param string $feedItemSetId
     *
     * @return string The formatted feed_item_set resource.
     */
    public static function feedItemSetName($customerId, $feedId, $feedItemSetId)
    {
        return self::getFeedItemSetNameTemplate()->render([
            'customer_id' => $customerId,
            'feed_id' => $feedId,
            'feed_item_set_id' => $feedItemSetId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * feed_item_set_link resource.
     *
     * @param string $customerId
     * @param string $feedId
     * @param string $feedItemSetId
     * @param string $feedItemId
     *
     * @return string The formatted feed_item_set_link resource.
     */
    public static function feedItemSetLinkName($customerId, $feedId, $feedItemSetId, $feedItemId)
    {
        return self::getFeedItemSetLinkNameTemplate()->render([
            'customer_id' => $customerId,
            'feed_id' => $feedId,
            'feed_item_set_id' => $feedItemSetId,
            'feed_item_id' => $feedItemId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * feed_item_target resource.
     *
     * @param string $customerId
     * @param string $feedId
     * @param string $feedItemId
     * @param string $feedItemTargetType
     * @param string $feedItemTargetId
     *
     * @return string The formatted feed_item_target resource.
     */
    public static function feedItemTargetName($customerId, $feedId, $feedItemId, $feedItemTargetType, $feedItemTargetId)
    {
        return self::getFeedItemTargetNameTemplate()->render([
            'customer_id' => $customerId,
            'feed_id' => $feedId,
            'feed_item_id' => $feedItemId,
            'feed_item_target_type' => $feedItemTargetType,
            'feed_item_target_id' => $feedItemTargetId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a feed_mapping
     * resource.
     *
     * @param string $customerId
     * @param string $feedId
     * @param string $feedMappingId
     *
     * @return string The formatted feed_mapping resource.
     */
    public static function feedMappingName($customerId, $feedId, $feedMappingId)
    {
        return self::getFeedMappingNameTemplate()->render([
            'customer_id' => $customerId,
            'feed_id' => $feedId,
            'feed_mapping_id' => $feedMappingId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * geo_target_constant resource.
     *
     * @param string $criterionId
     *
     * @return string The formatted geo_target_constant resource.
     */
    public static function geoTargetConstantName($criterionId)
    {
        return self::getGeoTargetConstantNameTemplate()->render([
            'criterion_id' => $criterionId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a keyword_plan
     * resource.
     *
     * @param string $customerId
     * @param string $keywordPlanId
     *
     * @return string The formatted keyword_plan resource.
     */
    public static function keywordPlanName($customerId, $keywordPlanId)
    {
        return self::getKeywordPlanNameTemplate()->render([
            'customer_id' => $customerId,
            'keyword_plan_id' => $keywordPlanId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * keyword_plan_ad_group resource.
     *
     * @param string $customerId
     * @param string $keywordPlanAdGroupId
     *
     * @return string The formatted keyword_plan_ad_group resource.
     */
    public static function keywordPlanAdGroupName($customerId, $keywordPlanAdGroupId)
    {
        return self::getKeywordPlanAdGroupNameTemplate()->render([
            'customer_id' => $customerId,
            'keyword_plan_ad_group_id' => $keywordPlanAdGroupId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * keyword_plan_ad_group_keyword resource.
     *
     * @param string $customerId
     * @param string $keywordPlanAdGroupKeywordId
     *
     * @return string The formatted keyword_plan_ad_group_keyword resource.
     */
    public static function keywordPlanAdGroupKeywordName($customerId, $keywordPlanAdGroupKeywordId)
    {
        return self::getKeywordPlanAdGroupKeywordNameTemplate()->render([
            'customer_id' => $customerId,
            'keyword_plan_ad_group_keyword_id' => $keywordPlanAdGroupKeywordId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * keyword_plan_campaign resource.
     *
     * @param string $customerId
     * @param string $keywordPlanCampaignId
     *
     * @return string The formatted keyword_plan_campaign resource.
     */
    public static function keywordPlanCampaignName($customerId, $keywordPlanCampaignId)
    {
        return self::getKeywordPlanCampaignNameTemplate()->render([
            'customer_id' => $customerId,
            'keyword_plan_campaign_id' => $keywordPlanCampaignId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * keyword_plan_campaign_keyword resource.
     *
     * @param string $customerId
     * @param string $keywordPlanCampaignKeywordId
     *
     * @return string The formatted keyword_plan_campaign_keyword resource.
     */
    public static function keywordPlanCampaignKeywordName($customerId, $keywordPlanCampaignKeywordId)
    {
        return self::getKeywordPlanCampaignKeywordNameTemplate()->render([
            'customer_id' => $customerId,
            'keyword_plan_campaign_keyword_id' => $keywordPlanCampaignKeywordId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a label
     * resource.
     *
     * @param string $customerId
     * @param string $labelId
     *
     * @return string The formatted label resource.
     */
    public static function labelName($customerId, $labelId)
    {
        return self::getLabelNameTemplate()->render([
            'customer_id' => $customerId,
            'label_id' => $labelId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * language_constant resource.
     *
     * @param string $criterionId
     *
     * @return string The formatted language_constant resource.
     */
    public static function languageConstantName($criterionId)
    {
        return self::getLanguageConstantNameTemplate()->render([
            'criterion_id' => $criterionId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * mobile_app_category_constant resource.
     *
     * @param string $mobileAppCategoryId
     *
     * @return string The formatted mobile_app_category_constant resource.
     */
    public static function mobileAppCategoryConstantName($mobileAppCategoryId)
    {
        return self::getMobileAppCategoryConstantNameTemplate()->render([
            'mobile_app_category_id' => $mobileAppCategoryId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * recommendation_subscription resource.
     *
     * @param string $customerId
     * @param string $recommendationType
     *
     * @return string The formatted recommendation_subscription resource.
     */
    public static function recommendationSubscriptionName($customerId, $recommendationType)
    {
        return self::getRecommendationSubscriptionNameTemplate()->render([
            'customer_id' => $customerId,
            'recommendation_type' => $recommendationType,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * remarketing_action resource.
     *
     * @param string $customerId
     * @param string $remarketingActionId
     *
     * @return string The formatted remarketing_action resource.
     */
    public static function remarketingActionName($customerId, $remarketingActionId)
    {
        return self::getRemarketingActionNameTemplate()->render([
            'customer_id' => $customerId,
            'remarketing_action_id' => $remarketingActionId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * shared_criterion resource.
     *
     * @param string $customerId
     * @param string $sharedSetId
     * @param string $criterionId
     *
     * @return string The formatted shared_criterion resource.
     */
    public static function sharedCriterionName($customerId, $sharedSetId, $criterionId)
    {
        return self::getSharedCriterionNameTemplate()->render([
            'customer_id' => $customerId,
            'shared_set_id' => $sharedSetId,
            'criterion_id' => $criterionId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a shared_set
     * resource.
     *
     * @param string $customerId
     * @param string $sharedSetId
     *
     * @return string The formatted shared_set resource.
     */
    public static function sharedSetName($customerId, $sharedSetId)
    {
        return self::getSharedSetNameTemplate()->render([
            'customer_id' => $customerId,
            'shared_set_id' => $sharedSetId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * smart_campaign_setting resource.
     *
     * @param string $customerId
     * @param string $campaignId
     *
     * @return string The formatted smart_campaign_setting resource.
     */
    public static function smartCampaignSettingName($customerId, $campaignId)
    {
        return self::getSmartCampaignSettingNameTemplate()->render([
            'customer_id' => $customerId,
            'campaign_id' => $campaignId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * topic_constant resource.
     *
     * @param string $topicId
     *
     * @return string The formatted topic_constant resource.
     */
    public static function topicConstantName($topicId)
    {
        return self::getTopicConstantNameTemplate()->render([
            'topic_id' => $topicId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * user_interest resource.
     *
     * @param string $customerId
     * @param string $userInterestId
     *
     * @return string The formatted user_interest resource.
     */
    public static function userInterestName($customerId, $userInterestId)
    {
        return self::getUserInterestNameTemplate()->render([
            'customer_id' => $customerId,
            'user_interest_id' => $userInterestId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a user_list
     * resource.
     *
     * @param string $customerId
     * @param string $userListId
     *
     * @return string The formatted user_list resource.
     */
    public static function userListName($customerId, $userListId)
    {
        return self::getUserListNameTemplate()->render([
            'customer_id' => $customerId,
            'user_list_id' => $userListId,
        ]);
    }

    /**
     * Parses a formatted name string and returns an associative array of the components in the name.
     * The following name formats are supported:
     * Template: Pattern
     * - accessibleBiddingStrategy: customers/{customer_id}/accessibleBiddingStrategies/{bidding_strategy_id}
     * - ad: customers/{customer_id}/ads/{ad_id}
     * - adGroup: customers/{customer_id}/adGroups/{ad_group_id}
     * - adGroupAd: customers/{customer_id}/adGroupAds/{ad_group_id}~{ad_id}
     * - adGroupAdLabel: customers/{customer_id}/adGroupAdLabels/{ad_group_id}~{ad_id}~{label_id}
     * - adGroupAsset: customers/{customer_id}/adGroupAssets/{ad_group_id}~{asset_id}~{field_type}
     * - adGroupBidModifier: customers/{customer_id}/adGroupBidModifiers/{ad_group_id}~{criterion_id}
     * - adGroupCriterion: customers/{customer_id}/adGroupCriteria/{ad_group_id}~{criterion_id}
     * - adGroupCriterionCustomizer: customers/{customer_id}/adGroupCriterionCustomizers/{ad_group_id}~{criterion_id}~{customizer_attribute_id}
     * - adGroupCriterionLabel: customers/{customer_id}/adGroupCriterionLabels/{ad_group_id}~{criterion_id}~{label_id}
     * - adGroupCustomizer: customers/{customer_id}/adGroupCustomizers/{ad_group_id}~{customizer_attribute_id}
     * - adGroupExtensionSetting: customers/{customer_id}/adGroupExtensionSettings/{ad_group_id}~{extension_type}
     * - adGroupFeed: customers/{customer_id}/adGroupFeeds/{ad_group_id}~{feed_id}
     * - adGroupLabel: customers/{customer_id}/adGroupLabels/{ad_group_id}~{label_id}
     * - adParameter: customers/{customer_id}/adParameters/{ad_group_id}~{criterion_id}~{parameter_index}
     * - asset: customers/{customer_id}/assets/{asset_id}
     * - assetGroup: customers/{customer_id}/assetGroups/{asset_group_id}
     * - assetGroupAsset: customers/{customer_id}/assetGroupAssets/{asset_group_id}~{asset_id}~{field_type}
     * - assetGroupListingGroupFilter: customers/{customer_id}/assetGroupListingGroupFilters/{asset_group_id}~{listing_group_filter_id}
     * - assetGroupSignal: customers/{customer_id}/assetGroupSignals/{asset_group_id}~{criterion_id}
     * - assetSet: customers/{customer_id}/assetSets/{asset_set_id}
     * - assetSetAsset: customers/{customer_id}/assetSetAssets/{asset_set_id}~{asset_id}
     * - audience: customers/{customer_id}/audiences/{audience_id}
     * - batchJob: customers/{customer_id}/batchJobs/{batch_job_id}
     * - biddingDataExclusion: customers/{customer_id}/biddingDataExclusions/{seasonality_event_id}
     * - biddingSeasonalityAdjustment: customers/{customer_id}/biddingSeasonalityAdjustments/{seasonality_event_id}
     * - biddingStrategy: customers/{customer_id}/biddingStrategies/{bidding_strategy_id}
     * - campaign: customers/{customer_id}/campaigns/{campaign_id}
     * - campaignAsset: customers/{customer_id}/campaignAssets/{campaign_id}~{asset_id}~{field_type}
     * - campaignAssetSet: customers/{customer_id}/campaignAssetSets/{campaign_id}~{asset_set_id}
     * - campaignBidModifier: customers/{customer_id}/campaignBidModifiers/{campaign_id}~{criterion_id}
     * - campaignBudget: customers/{customer_id}/campaignBudgets/{campaign_budget_id}
     * - campaignConversionGoal: customers/{customer_id}/campaignConversionGoals/{campaign_id}~{category}~{source}
     * - campaignCriterion: customers/{customer_id}/campaignCriteria/{campaign_id}~{criterion_id}
     * - campaignCustomizer: customers/{customer_id}/campaignCustomizers/{campaign_id}~{customizer_attribute_id}
     * - campaignDraft: customers/{customer_id}/campaignDrafts/{base_campaign_id}~{draft_id}
     * - campaignExtensionSetting: customers/{customer_id}/campaignExtensionSettings/{campaign_id}~{extension_type}
     * - campaignFeed: customers/{customer_id}/campaignFeeds/{campaign_id}~{feed_id}
     * - campaignGroup: customers/{customer_id}/campaignGroups/{campaign_group_id}
     * - campaignLabel: customers/{customer_id}/campaignLabels/{campaign_id}~{label_id}
     * - campaignSharedSet: customers/{customer_id}/campaignSharedSets/{campaign_id}~{shared_set_id}
     * - combinedAudience: customers/{customer_id}/combinedAudiences/{combined_audience_id}
     * - conversionAction: customers/{customer_id}/conversionActions/{conversion_action_id}
     * - conversionCustomVariable: customers/{customer_id}/conversionCustomVariables/{conversion_custom_variable_id}
     * - conversionGoalCampaignConfig: customers/{customer_id}/conversionGoalCampaignConfigs/{campaign_id}
     * - conversionValueRule: customers/{customer_id}/conversionValueRules/{conversion_value_rule_id}
     * - conversionValueRuleSet: customers/{customer_id}/conversionValueRuleSets/{conversion_value_rule_set_id}
     * - customConversionGoal: customers/{customer_id}/customConversionGoals/{goal_id}
     * - customer: customers/{customer_id}
     * - customerAsset: customers/{customer_id}/customerAssets/{asset_id}~{field_type}
     * - customerConversionGoal: customers/{customer_id}/customerConversionGoals/{category}~{source}
     * - customerCustomizer: customers/{customer_id}/customerCustomizers/{customizer_attribute_id}
     * - customerExtensionSetting: customers/{customer_id}/customerExtensionSettings/{extension_type}
     * - customerFeed: customers/{customer_id}/customerFeeds/{feed_id}
     * - customerLabel: customers/{customer_id}/customerLabels/{label_id}
     * - customerNegativeCriterion: customers/{customer_id}/customerNegativeCriteria/{criterion_id}
     * - customizerAttribute: customers/{customer_id}/customizerAttributes/{customizer_attribute_id}
     * - experiment: customers/{customer_id}/experiments/{trial_id}
     * - experimentArm: customers/{customer_id}/experimentArms/{trial_id}~{trial_arm_id}
     * - extensionFeedItem: customers/{customer_id}/extensionFeedItems/{feed_item_id}
     * - feed: customers/{customer_id}/feeds/{feed_id}
     * - feedItem: customers/{customer_id}/feedItems/{feed_id}~{feed_item_id}
     * - feedItemSet: customers/{customer_id}/feedItemSets/{feed_id}~{feed_item_set_id}
     * - feedItemSetLink: customers/{customer_id}/feedItemSetLinks/{feed_id}~{feed_item_set_id}~{feed_item_id}
     * - feedItemTarget: customers/{customer_id}/feedItemTargets/{feed_id}~{feed_item_id}~{feed_item_target_type}~{feed_item_target_id}
     * - feedMapping: customers/{customer_id}/feedMappings/{feed_id}~{feed_mapping_id}
     * - geoTargetConstant: geoTargetConstants/{criterion_id}
     * - keywordPlan: customers/{customer_id}/keywordPlans/{keyword_plan_id}
     * - keywordPlanAdGroup: customers/{customer_id}/keywordPlanAdGroups/{keyword_plan_ad_group_id}
     * - keywordPlanAdGroupKeyword: customers/{customer_id}/keywordPlanAdGroupKeywords/{keyword_plan_ad_group_keyword_id}
     * - keywordPlanCampaign: customers/{customer_id}/keywordPlanCampaigns/{keyword_plan_campaign_id}
     * - keywordPlanCampaignKeyword: customers/{customer_id}/keywordPlanCampaignKeywords/{keyword_plan_campaign_keyword_id}
     * - label: customers/{customer_id}/labels/{label_id}
     * - languageConstant: languageConstants/{criterion_id}
     * - mobileAppCategoryConstant: mobileAppCategoryConstants/{mobile_app_category_id}
     * - recommendationSubscription: customers/{customer_id}/recommendationSubscriptions/{recommendation_type}
     * - remarketingAction: customers/{customer_id}/remarketingActions/{remarketing_action_id}
     * - sharedCriterion: customers/{customer_id}/sharedCriteria/{shared_set_id}~{criterion_id}
     * - sharedSet: customers/{customer_id}/sharedSets/{shared_set_id}
     * - smartCampaignSetting: customers/{customer_id}/smartCampaignSettings/{campaign_id}
     * - topicConstant: topicConstants/{topic_id}
     * - userInterest: customers/{customer_id}/userInterests/{user_interest_id}
     * - userList: customers/{customer_id}/userLists/{user_list_id}
     *
     * The optional $template argument can be supplied to specify a particular pattern,
     * and must match one of the templates listed above. If no $template argument is
     * provided, or if the $template argument does not match one of the templates
     * listed, then parseName will check each of the supported templates, and return
     * the first match.
     *
     * @param string $formattedName The formatted name string
     * @param string $template      Optional name of template to match
     *
     * @return array An associative array from name component IDs to component values.
     *
     * @throws ValidationException If $formattedName could not be matched.
     */
    public static function parseName($formattedName, $template = null)
    {
        $templateMap = self::getPathTemplateMap();
        if ($template) {
            if (!isset($templateMap[$template])) {
                throw new ValidationException("Template name $template does not exist");
            }

            return $templateMap[$template]->match($formattedName);
        }

        foreach ($templateMap as $templateName => $pathTemplate) {
            try {
                return $pathTemplate->match($formattedName);
            } catch (ValidationException $ex) {
                // Swallow the exception to continue trying other path templates
            }
        }

        throw new ValidationException("Input did not match any known format. Input: $formattedName");
    }

    /**
     * Return an OperationsClient object with the same endpoint as $this.
     *
     * @return OperationsClient
     */
    public function getOperationsClient()
    {
        return $this->operationsClient;
    }

    /**
     * Resume an existing long running operation that was previously started by a long
     * running API method. If $methodName is not provided, or does not match a long
     * running API method, then the operation can still be resumed, but the
     * OperationResponse object will not deserialize the final response.
     *
     * @param string $operationName The name of the long running operation
     * @param string $methodName    The name of the method used to start the operation
     *
     * @return OperationResponse
     */
    public function resumeOperation($operationName, $methodName = null)
    {
        $options = isset($this->descriptors[$methodName]['longRunning']) ? $this->descriptors[$methodName]['longRunning'] : [];
        $operation = new OperationResponse($operationName, $this->getOperationsClient(), $options);
        $operation->reload();
        return $operation;
    }

    /**
     * Constructor.
     *
     * @param array $options {
     *     Optional. Options for configuring the service API wrapper.
     *
     *     @type string $apiEndpoint
     *           The address of the API remote host. May optionally include the port, formatted
     *           as "<uri>:<port>". Default 'googleads.googleapis.com:443'.
     *     @type string|array|FetchAuthTokenInterface|CredentialsWrapper $credentials
     *           The credentials to be used by the client to authorize API calls. This option
     *           accepts either a path to a credentials file, or a decoded credentials file as a
     *           PHP array.
     *           *Advanced usage*: In addition, this option can also accept a pre-constructed
     *           {@see \Google\Auth\FetchAuthTokenInterface} object or
     *           {@see \Google\ApiCore\CredentialsWrapper} object. Note that when one of these
     *           objects are provided, any settings in $credentialsConfig will be ignored.
     *     @type array $credentialsConfig
     *           Options used to configure credentials, including auth token caching, for the
     *           client. For a full list of supporting configuration options, see
     *           {@see \Google\ApiCore\CredentialsWrapper::build()} .
     *     @type bool $disableRetries
     *           Determines whether or not retries defined by the client configuration should be
     *           disabled. Defaults to `false`.
     *     @type string|array $clientConfig
     *           Client method configuration, including retry settings. This option can be either
     *           a path to a JSON file, or a PHP array containing the decoded JSON data. By
     *           default this settings points to the default client config file, which is
     *           provided in the resources folder.
     *     @type string|TransportInterface $transport
     *           The transport used for executing network requests. May be either the string
     *           `rest` or `grpc`. Defaults to `grpc` if gRPC support is detected on the system.
     *           *Advanced usage*: Additionally, it is possible to pass in an already
     *           instantiated {@see \Google\ApiCore\Transport\TransportInterface} object. Note
     *           that when this object is provided, any settings in $transportConfig, and any
     *           $apiEndpoint setting, will be ignored.
     *     @type array $transportConfig
     *           Configuration options that will be used to construct the transport. Options for
     *           each supported transport type should be passed in a key for that transport. For
     *           example:
     *           $transportConfig = [
     *               'grpc' => [...],
     *               'rest' => [...],
     *           ];
     *           See the {@see \Google\ApiCore\Transport\GrpcTransport::build()} and
     *           {@see \Google\ApiCore\Transport\RestTransport::build()} methods for the
     *           supported options.
     *     @type callable $clientCertSource
     *           A callable which returns the client cert as a string. This can be used to
     *           provide a certificate and private key to the transport layer for mTLS.
     * }
     *
     * @throws ValidationException
     */
    public function __construct(array $options = [])
    {
        $clientOptions = $this->buildClientOptions($options);
        $this->setClientOptions($clientOptions);
        $this->operationsClient = $this->createOperationsClient($clientOptions);
    }

    /**
     * Add operations to the batch job.
     *
     * List of thrown errors:
     * [AuthenticationError]()
     * [AuthorizationError]()
     * [BatchJobError]()
     * [HeaderError]()
     * [InternalError]()
     * [QuotaError]()
     * [RequestError]()
     * [ResourceCountLimitExceededError]()
     *
     * Sample code:
     * ```
     * $batchJobServiceClient = new BatchJobServiceClient();
     * try {
     *     $formattedResourceName = $batchJobServiceClient->batchJobName('[CUSTOMER_ID]', '[BATCH_JOB_ID]');
     *     $mutateOperations = [];
     *     $response = $batchJobServiceClient->addBatchJobOperations($formattedResourceName, $mutateOperations);
     * } finally {
     *     $batchJobServiceClient->close();
     * }
     * ```
     *
     * @param string            $resourceName     Required. The resource name of the batch job.
     * @param MutateOperation[] $mutateOperations Required. The list of mutates being added.
     *
     *                                            Operations can use negative integers as temp ids to signify dependencies
     *                                            between entities created in this batch job. For example, a customer with
     *                                            id = 1234 can create a campaign and an ad group in that same campaign by
     *                                            creating a campaign in the first operation with the resource name
     *                                            explicitly set to "customers/1234/campaigns/-1", and creating an ad group
     *                                            in the second operation with the campaign field also set to
     *                                            "customers/1234/campaigns/-1".
     * @param array             $optionalArgs     {
     *     Optional.
     *
     *     @type string $sequenceToken
     *           A token used to enforce sequencing.
     *
     *           The first AddBatchJobOperations request for a batch job should not set
     *           sequence_token. Subsequent requests must set sequence_token to the value of
     *           next_sequence_token received in the previous AddBatchJobOperations
     *           response.
     *     @type RetrySettings|array $retrySettings
     *           Retry settings to use for this call. Can be a {@see RetrySettings} object, or an
     *           associative array of retry settings parameters. See the documentation on
     *           {@see RetrySettings} for example usage.
     * }
     *
     * @return \Google\Ads\GoogleAds\V15\Services\AddBatchJobOperationsResponse
     *
     * @throws ApiException if the remote call fails
     */
    public function addBatchJobOperations($resourceName, $mutateOperations, array $optionalArgs = [])
    {
        $request = new AddBatchJobOperationsRequest();
        $requestParamHeaders = [];
        $request->setResourceName($resourceName);
        $request->setMutateOperations($mutateOperations);
        $requestParamHeaders['resource_name'] = $resourceName;
        if (isset($optionalArgs['sequenceToken'])) {
            $request->setSequenceToken($optionalArgs['sequenceToken']);
        }

        $requestParams = new RequestParamsHeaderDescriptor($requestParamHeaders);
        $optionalArgs['headers'] = isset($optionalArgs['headers']) ? array_merge($requestParams->getHeader(), $optionalArgs['headers']) : $requestParams->getHeader();
        return $this->startCall('AddBatchJobOperations', AddBatchJobOperationsResponse::class, $optionalArgs, $request)->wait();
    }

    /**
     * Returns the results of the batch job. The job must be done.
     * Supports standard list paging.
     *
     * List of thrown errors:
     * [AuthenticationError]()
     * [AuthorizationError]()
     * [BatchJobError]()
     * [HeaderError]()
     * [InternalError]()
     * [QuotaError]()
     * [RequestError]()
     *
     * Sample code:
     * ```
     * $batchJobServiceClient = new BatchJobServiceClient();
     * try {
     *     $formattedResourceName = $batchJobServiceClient->batchJobName('[CUSTOMER_ID]', '[BATCH_JOB_ID]');
     *     // Iterate over pages of elements
     *     $pagedResponse = $batchJobServiceClient->listBatchJobResults($formattedResourceName);
     *     foreach ($pagedResponse->iteratePages() as $page) {
     *         foreach ($page as $element) {
     *             // doSomethingWith($element);
     *         }
     *     }
     *     // Alternatively:
     *     // Iterate through all elements
     *     $pagedResponse = $batchJobServiceClient->listBatchJobResults($formattedResourceName);
     *     foreach ($pagedResponse->iterateAllElements() as $element) {
     *         // doSomethingWith($element);
     *     }
     * } finally {
     *     $batchJobServiceClient->close();
     * }
     * ```
     *
     * @param string $resourceName Required. The resource name of the batch job whose results are being
     *                             listed.
     * @param array  $optionalArgs {
     *     Optional.
     *
     *     @type string $pageToken
     *           A page token is used to specify a page of values to be returned.
     *           If no page token is specified (the default), the first page
     *           of values will be returned. Any page token used here must have
     *           been generated by a previous call to the API.
     *     @type int $pageSize
     *           The maximum number of resources contained in the underlying API
     *           response. The API may return fewer values in a page, even if
     *           there are additional values to be retrieved.
     *     @type int $responseContentType
     *           The response content type setting. Determines whether the mutable resource
     *           or just the resource name should be returned.
     *           For allowed values, use constants defined on {@see \Google\Ads\GoogleAds\V15\Enums\ResponseContentTypeEnum\ResponseContentType}
     *     @type RetrySettings|array $retrySettings
     *           Retry settings to use for this call. Can be a {@see RetrySettings} object, or an
     *           associative array of retry settings parameters. See the documentation on
     *           {@see RetrySettings} for example usage.
     * }
     *
     * @return \Google\ApiCore\PagedListResponse
     *
     * @throws ApiException if the remote call fails
     */
    public function listBatchJobResults($resourceName, array $optionalArgs = [])
    {
        $request = new ListBatchJobResultsRequest();
        $requestParamHeaders = [];
        $request->setResourceName($resourceName);
        $requestParamHeaders['resource_name'] = $resourceName;
        if (isset($optionalArgs['pageToken'])) {
            $request->setPageToken($optionalArgs['pageToken']);
        }

        if (isset($optionalArgs['pageSize'])) {
            $request->setPageSize($optionalArgs['pageSize']);
        }

        if (isset($optionalArgs['responseContentType'])) {
            $request->setResponseContentType($optionalArgs['responseContentType']);
        }

        $requestParams = new RequestParamsHeaderDescriptor($requestParamHeaders);
        $optionalArgs['headers'] = isset($optionalArgs['headers']) ? array_merge($requestParams->getHeader(), $optionalArgs['headers']) : $requestParams->getHeader();
        return $this->getPagedListResponse('ListBatchJobResults', $optionalArgs, ListBatchJobResultsResponse::class, $request);
    }

    /**
     * Mutates a batch job.
     *
     * List of thrown errors:
     * [AuthenticationError]()
     * [AuthorizationError]()
     * [HeaderError]()
     * [InternalError]()
     * [QuotaError]()
     * [RequestError]()
     * [ResourceCountLimitExceededError]()
     *
     * Sample code:
     * ```
     * $batchJobServiceClient = new BatchJobServiceClient();
     * try {
     *     $customerId = 'customer_id';
     *     $operation = new BatchJobOperation();
     *     $response = $batchJobServiceClient->mutateBatchJob($customerId, $operation);
     * } finally {
     *     $batchJobServiceClient->close();
     * }
     * ```
     *
     * @param string            $customerId   Required. The ID of the customer for which to create a batch job.
     * @param BatchJobOperation $operation    Required. The operation to perform on an individual batch job.
     * @param array             $optionalArgs {
     *     Optional.
     *
     *     @type RetrySettings|array $retrySettings
     *           Retry settings to use for this call. Can be a {@see RetrySettings} object, or an
     *           associative array of retry settings parameters. See the documentation on
     *           {@see RetrySettings} for example usage.
     * }
     *
     * @return \Google\Ads\GoogleAds\V15\Services\MutateBatchJobResponse
     *
     * @throws ApiException if the remote call fails
     */
    public function mutateBatchJob($customerId, $operation, array $optionalArgs = [])
    {
        $request = new MutateBatchJobRequest();
        $requestParamHeaders = [];
        $request->setCustomerId($customerId);
        $request->setOperation($operation);
        $requestParamHeaders['customer_id'] = $customerId;
        $requestParams = new RequestParamsHeaderDescriptor($requestParamHeaders);
        $optionalArgs['headers'] = isset($optionalArgs['headers']) ? array_merge($requestParams->getHeader(), $optionalArgs['headers']) : $requestParams->getHeader();
        return $this->startCall('MutateBatchJob', MutateBatchJobResponse::class, $optionalArgs, $request)->wait();
    }

    /**
     * Runs the batch job.
     *
     * The Operation.metadata field type is BatchJobMetadata. When finished, the
     * long running operation will not contain errors or a response. Instead, use
     * ListBatchJobResults to get the results of the job.
     *
     * List of thrown errors:
     * [AuthenticationError]()
     * [AuthorizationError]()
     * [BatchJobError]()
     * [HeaderError]()
     * [InternalError]()
     * [QuotaError]()
     * [RequestError]()
     *
     * Sample code:
     * ```
     * $batchJobServiceClient = new BatchJobServiceClient();
     * try {
     *     $formattedResourceName = $batchJobServiceClient->batchJobName('[CUSTOMER_ID]', '[BATCH_JOB_ID]');
     *     $operationResponse = $batchJobServiceClient->runBatchJob($formattedResourceName);
     *     $operationResponse->pollUntilComplete();
     *     if ($operationResponse->operationSucceeded()) {
     *         // operation succeeded and returns no value
     *     } else {
     *         $error = $operationResponse->getError();
     *         // handleError($error)
     *     }
     *     // Alternatively:
     *     // start the operation, keep the operation name, and resume later
     *     $operationResponse = $batchJobServiceClient->runBatchJob($formattedResourceName);
     *     $operationName = $operationResponse->getName();
     *     // ... do other work
     *     $newOperationResponse = $batchJobServiceClient->resumeOperation($operationName, 'runBatchJob');
     *     while (!$newOperationResponse->isDone()) {
     *         // ... do other work
     *         $newOperationResponse->reload();
     *     }
     *     if ($newOperationResponse->operationSucceeded()) {
     *         // operation succeeded and returns no value
     *     } else {
     *         $error = $newOperationResponse->getError();
     *         // handleError($error)
     *     }
     * } finally {
     *     $batchJobServiceClient->close();
     * }
     * ```
     *
     * @param string $resourceName Required. The resource name of the BatchJob to run.
     * @param array  $optionalArgs {
     *     Optional.
     *
     *     @type RetrySettings|array $retrySettings
     *           Retry settings to use for this call. Can be a {@see RetrySettings} object, or an
     *           associative array of retry settings parameters. See the documentation on
     *           {@see RetrySettings} for example usage.
     * }
     *
     * @return \Google\ApiCore\OperationResponse
     *
     * @throws ApiException if the remote call fails
     */
    public function runBatchJob($resourceName, array $optionalArgs = [])
    {
        $request = new RunBatchJobRequest();
        $requestParamHeaders = [];
        $request->setResourceName($resourceName);
        $requestParamHeaders['resource_name'] = $resourceName;
        $requestParams = new RequestParamsHeaderDescriptor($requestParamHeaders);
        $optionalArgs['headers'] = isset($optionalArgs['headers']) ? array_merge($requestParams->getHeader(), $optionalArgs['headers']) : $requestParams->getHeader();
        return $this->startOperationsCall('RunBatchJob', $optionalArgs, $request, $this->getOperationsClient())->wait();
    }
}
