<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v16/enums/product_link_invitation_status.proto

namespace Google\Ads\GoogleAds\V16\Enums\ProductLinkInvitationStatusEnum;

use UnexpectedValueException;

/**
 * Describes the possible statuses for an invitation between a Google Ads
 * customer and another account.
 *
 * Protobuf type <code>google.ads.googleads.v16.enums.ProductLinkInvitationStatusEnum.ProductLinkInvitationStatus</code>
 */
class ProductLinkInvitationStatus
{
    /**
     * Not specified.
     *
     * Generated from protobuf enum <code>UNSPECIFIED = 0;</code>
     */
    const UNSPECIFIED = 0;
    /**
     * Used for return value only. Represents value unknown in this version.
     *
     * Generated from protobuf enum <code>UNKNOWN = 1;</code>
     */
    const UNKNOWN = 1;
    /**
     * The invitation is accepted.
     *
     * Generated from protobuf enum <code>ACCEPTED = 2;</code>
     */
    const ACCEPTED = 2;
    /**
     * An invitation has been sent to the other account. A user on the other
     * account may now accept the invitation by setting the status to ACCEPTED.
     *
     * Generated from protobuf enum <code>REQUESTED = 3;</code>
     */
    const REQUESTED = 3;
    /**
     * This invitation has been sent by a user on the other account. It may be
     * accepted by a user on this account by setting the status to ACCEPTED.
     *
     * Generated from protobuf enum <code>PENDING_APPROVAL = 4;</code>
     */
    const PENDING_APPROVAL = 4;
    /**
     * The invitation is revoked by the user who sent the invitation.
     *
     * Generated from protobuf enum <code>REVOKED = 5;</code>
     */
    const REVOKED = 5;
    /**
     * The invitation has been rejected by the invitee.
     *
     * Generated from protobuf enum <code>REJECTED = 6;</code>
     */
    const REJECTED = 6;
    /**
     * The invitation has timed out before being accepted by the
     * invitee.
     *
     * Generated from protobuf enum <code>EXPIRED = 7;</code>
     */
    const EXPIRED = 7;

    private static $valueToName = [
        self::UNSPECIFIED => 'UNSPECIFIED',
        self::UNKNOWN => 'UNKNOWN',
        self::ACCEPTED => 'ACCEPTED',
        self::REQUESTED => 'REQUESTED',
        self::PENDING_APPROVAL => 'PENDING_APPROVAL',
        self::REVOKED => 'REVOKED',
        self::REJECTED => 'REJECTED',
        self::EXPIRED => 'EXPIRED',
    ];

    public static function name($value)
    {
        if (!isset(self::$valueToName[$value])) {
            throw new UnexpectedValueException(sprintf(
                    'Enum %s has no name defined for value %s', __CLASS__, $value));
        }
        return self::$valueToName[$value];
    }


    public static function value($name)
    {
        $const = __CLASS__ . '::' . strtoupper($name);
        if (!defined($const)) {
            throw new UnexpectedValueException(sprintf(
                    'Enum %s has no value defined for name %s', __CLASS__, $name));
        }
        return constant($const);
    }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(ProductLinkInvitationStatus::class, \Google\Ads\GoogleAds\V16\Enums\ProductLinkInvitationStatusEnum_ProductLinkInvitationStatus::class);

