<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v17/enums/feed_mapping_status.proto

namespace Google\Ads\GoogleAds\V17\Enums;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Container for enum describing possible statuses of a feed mapping.
 *
 * Generated from protobuf message <code>google.ads.googleads.v17.enums.FeedMappingStatusEnum</code>
 */
class FeedMappingStatusEnum extends \Google\Protobuf\Internal\Message
{

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Ads\GoogleAds\V17\Enums\FeedMappingStatus::initOnce();
        parent::__construct($data);
    }

}

