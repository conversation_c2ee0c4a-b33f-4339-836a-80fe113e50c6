<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v16/common/segments.proto

namespace Google\Ads\GoogleAds\V16\Common;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Segment only fields.
 *
 * Generated from protobuf message <code>google.ads.googleads.v16.common.Segments</code>
 */
class Segments extends \Google\Protobuf\Internal\Message
{
    /**
     * Activity account ID.
     *
     * Generated from protobuf field <code>optional int64 activity_account_id = 148;</code>
     */
    protected $activity_account_id = null;
    /**
     * The city where the travel activity is available.
     *
     * Generated from protobuf field <code>optional string activity_city = 185;</code>
     */
    protected $activity_city = null;
    /**
     * The country where the travel activity is available.
     *
     * Generated from protobuf field <code>optional string activity_country = 186;</code>
     */
    protected $activity_country = null;
    /**
     * Activity rating.
     *
     * Generated from protobuf field <code>optional int64 activity_rating = 149;</code>
     */
    protected $activity_rating = null;
    /**
     * The state where the travel activity is available.
     *
     * Generated from protobuf field <code>optional string activity_state = 187;</code>
     */
    protected $activity_state = null;
    /**
     * Advertiser supplied activity ID.
     *
     * Generated from protobuf field <code>optional string external_activity_id = 150;</code>
     */
    protected $external_activity_id = null;
    /**
     * Ad Destination type.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.AdDestinationTypeEnum.AdDestinationType ad_destination_type = 136;</code>
     */
    protected $ad_destination_type = 0;
    /**
     * Ad network type.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.AdNetworkTypeEnum.AdNetworkType ad_network_type = 3;</code>
     */
    protected $ad_network_type = 0;
    /**
     * Resource name of the ad group.
     *
     * Generated from protobuf field <code>optional string ad_group = 158;</code>
     */
    protected $ad_group = null;
    /**
     * Resource name of the asset group.
     *
     * Generated from protobuf field <code>optional string asset_group = 159;</code>
     */
    protected $asset_group = null;
    /**
     * Domain (visible URL) of a participant in the Auction Insights report.
     *
     * Generated from protobuf field <code>optional string auction_insight_domain = 145;</code>
     */
    protected $auction_insight_domain = null;
    /**
     * Budget campaign association status.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.common.BudgetCampaignAssociationStatus budget_campaign_association_status = 134;</code>
     */
    protected $budget_campaign_association_status = null;
    /**
     * Resource name of the campaign.
     *
     * Generated from protobuf field <code>optional string campaign = 157;</code>
     */
    protected $campaign = null;
    /**
     * Click type.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.ClickTypeEnum.ClickType click_type = 26;</code>
     */
    protected $click_type = 0;
    /**
     * Resource name of the conversion action.
     *
     * Generated from protobuf field <code>optional string conversion_action = 113 [(.google.api.resource_reference) = {</code>
     */
    protected $conversion_action = null;
    /**
     * Conversion action category.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.ConversionActionCategoryEnum.ConversionActionCategory conversion_action_category = 53;</code>
     */
    protected $conversion_action_category = 0;
    /**
     * Conversion action name.
     *
     * Generated from protobuf field <code>optional string conversion_action_name = 114;</code>
     */
    protected $conversion_action_name = null;
    /**
     * This segments your conversion columns by the original conversion and
     * conversion value versus the delta if conversions were adjusted. False row
     * has the data as originally stated; While true row has the delta between
     * data now and the data as originally stated. Summing the two together
     * results post-adjustment data.
     *
     * Generated from protobuf field <code>optional bool conversion_adjustment = 115;</code>
     */
    protected $conversion_adjustment = null;
    /**
     * Conversion attribution event type.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.ConversionAttributionEventTypeEnum.ConversionAttributionEventType conversion_attribution_event_type = 2;</code>
     */
    protected $conversion_attribution_event_type = 0;
    /**
     * An enum value representing the number of days between the impression and
     * the conversion.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.ConversionLagBucketEnum.ConversionLagBucket conversion_lag_bucket = 50;</code>
     */
    protected $conversion_lag_bucket = 0;
    /**
     * An enum value representing the number of days between the impression and
     * the conversion or between the impression and adjustments to the conversion.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.ConversionOrAdjustmentLagBucketEnum.ConversionOrAdjustmentLagBucket conversion_or_adjustment_lag_bucket = 51;</code>
     */
    protected $conversion_or_adjustment_lag_bucket = 0;
    /**
     * Date to which metrics apply.
     * yyyy-MM-dd format, for example, 2018-04-17.
     *
     * Generated from protobuf field <code>optional string date = 79;</code>
     */
    protected $date = null;
    /**
     * Day of the week, for example, MONDAY.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.DayOfWeekEnum.DayOfWeek day_of_week = 5;</code>
     */
    protected $day_of_week = 0;
    /**
     * Device to which metrics apply.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.DeviceEnum.Device device = 1;</code>
     */
    protected $device = 0;
    /**
     * External conversion source.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.ExternalConversionSourceEnum.ExternalConversionSource external_conversion_source = 55;</code>
     */
    protected $external_conversion_source = 0;
    /**
     * Resource name of the geo target constant that represents an airport.
     *
     * Generated from protobuf field <code>optional string geo_target_airport = 116;</code>
     */
    protected $geo_target_airport = null;
    /**
     * Resource name of the geo target constant that represents a canton.
     *
     * Generated from protobuf field <code>optional string geo_target_canton = 117;</code>
     */
    protected $geo_target_canton = null;
    /**
     * Resource name of the geo target constant that represents a city.
     *
     * Generated from protobuf field <code>optional string geo_target_city = 118;</code>
     */
    protected $geo_target_city = null;
    /**
     * Resource name of the geo target constant that represents a country.
     *
     * Generated from protobuf field <code>optional string geo_target_country = 119;</code>
     */
    protected $geo_target_country = null;
    /**
     * Resource name of the geo target constant that represents a county.
     *
     * Generated from protobuf field <code>optional string geo_target_county = 120;</code>
     */
    protected $geo_target_county = null;
    /**
     * Resource name of the geo target constant that represents a district.
     *
     * Generated from protobuf field <code>optional string geo_target_district = 121;</code>
     */
    protected $geo_target_district = null;
    /**
     * Resource name of the geo target constant that represents a metro.
     *
     * Generated from protobuf field <code>optional string geo_target_metro = 122;</code>
     */
    protected $geo_target_metro = null;
    /**
     * Resource name of the geo target constant that represents the most
     * specific location.
     *
     * Generated from protobuf field <code>optional string geo_target_most_specific_location = 123;</code>
     */
    protected $geo_target_most_specific_location = null;
    /**
     * Resource name of the geo target constant that represents a postal code.
     *
     * Generated from protobuf field <code>optional string geo_target_postal_code = 124;</code>
     */
    protected $geo_target_postal_code = null;
    /**
     * Resource name of the geo target constant that represents a province.
     *
     * Generated from protobuf field <code>optional string geo_target_province = 125;</code>
     */
    protected $geo_target_province = null;
    /**
     * Resource name of the geo target constant that represents a region.
     *
     * Generated from protobuf field <code>optional string geo_target_region = 126;</code>
     */
    protected $geo_target_region = null;
    /**
     * Resource name of the geo target constant that represents a state.
     *
     * Generated from protobuf field <code>optional string geo_target_state = 127;</code>
     */
    protected $geo_target_state = null;
    /**
     * Hotel booking window in days.
     *
     * Generated from protobuf field <code>optional int64 hotel_booking_window_days = 135;</code>
     */
    protected $hotel_booking_window_days = null;
    /**
     * Hotel center ID.
     *
     * Generated from protobuf field <code>optional int64 hotel_center_id = 80;</code>
     */
    protected $hotel_center_id = null;
    /**
     * Hotel check-in date. Formatted as yyyy-MM-dd.
     *
     * Generated from protobuf field <code>optional string hotel_check_in_date = 81;</code>
     */
    protected $hotel_check_in_date = null;
    /**
     * Hotel check-in day of week.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.DayOfWeekEnum.DayOfWeek hotel_check_in_day_of_week = 9;</code>
     */
    protected $hotel_check_in_day_of_week = 0;
    /**
     * Hotel city.
     *
     * Generated from protobuf field <code>optional string hotel_city = 82;</code>
     */
    protected $hotel_city = null;
    /**
     * Hotel class.
     *
     * Generated from protobuf field <code>optional int32 hotel_class = 83;</code>
     */
    protected $hotel_class = null;
    /**
     * Hotel country.
     *
     * Generated from protobuf field <code>optional string hotel_country = 84;</code>
     */
    protected $hotel_country = null;
    /**
     * Hotel date selection type.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.HotelDateSelectionTypeEnum.HotelDateSelectionType hotel_date_selection_type = 13;</code>
     */
    protected $hotel_date_selection_type = 0;
    /**
     * Hotel length of stay.
     *
     * Generated from protobuf field <code>optional int32 hotel_length_of_stay = 85;</code>
     */
    protected $hotel_length_of_stay = null;
    /**
     * Hotel rate rule ID.
     *
     * Generated from protobuf field <code>optional string hotel_rate_rule_id = 86;</code>
     */
    protected $hotel_rate_rule_id = null;
    /**
     * Hotel rate type.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.HotelRateTypeEnum.HotelRateType hotel_rate_type = 74;</code>
     */
    protected $hotel_rate_type = 0;
    /**
     * Hotel price bucket.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.HotelPriceBucketEnum.HotelPriceBucket hotel_price_bucket = 78;</code>
     */
    protected $hotel_price_bucket = 0;
    /**
     * Hotel state.
     *
     * Generated from protobuf field <code>optional string hotel_state = 87;</code>
     */
    protected $hotel_state = null;
    /**
     * Hour of day as a number between 0 and 23, inclusive.
     *
     * Generated from protobuf field <code>optional int32 hour = 88;</code>
     */
    protected $hour = null;
    /**
     * Only used with feed item metrics.
     * Indicates whether the interaction metrics occurred on the feed item itself
     * or a different extension or ad unit.
     *
     * Generated from protobuf field <code>optional bool interaction_on_this_extension = 89;</code>
     */
    protected $interaction_on_this_extension = null;
    /**
     * Keyword criterion.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.common.Keyword keyword = 61;</code>
     */
    protected $keyword = null;
    /**
     * Month as represented by the date of the first day of a month. Formatted as
     * yyyy-MM-dd.
     *
     * Generated from protobuf field <code>optional string month = 90;</code>
     */
    protected $month = null;
    /**
     * Month of the year, for example, January.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.MonthOfYearEnum.MonthOfYear month_of_year = 18;</code>
     */
    protected $month_of_year = 0;
    /**
     * Partner hotel ID.
     *
     * Generated from protobuf field <code>optional string partner_hotel_id = 91;</code>
     */
    protected $partner_hotel_id = null;
    /**
     * Placeholder type. This is only used with feed item metrics.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.PlaceholderTypeEnum.PlaceholderType placeholder_type = 20;</code>
     */
    protected $placeholder_type = 0;
    /**
     * Aggregator ID of the product.
     *
     * Generated from protobuf field <code>optional int64 product_aggregator_id = 132;</code>
     */
    protected $product_aggregator_id = null;
    /**
     * Category (level 1) of the product.
     *
     * Generated from protobuf field <code>optional string product_category_level1 = 161;</code>
     */
    protected $product_category_level1 = null;
    /**
     * Category (level 2) of the product.
     *
     * Generated from protobuf field <code>optional string product_category_level2 = 162;</code>
     */
    protected $product_category_level2 = null;
    /**
     * Category (level 3) of the product.
     *
     * Generated from protobuf field <code>optional string product_category_level3 = 163;</code>
     */
    protected $product_category_level3 = null;
    /**
     * Category (level 4) of the product.
     *
     * Generated from protobuf field <code>optional string product_category_level4 = 164;</code>
     */
    protected $product_category_level4 = null;
    /**
     * Category (level 5) of the product.
     *
     * Generated from protobuf field <code>optional string product_category_level5 = 165;</code>
     */
    protected $product_category_level5 = null;
    /**
     * Brand of the product.
     *
     * Generated from protobuf field <code>optional string product_brand = 97;</code>
     */
    protected $product_brand = null;
    /**
     * Channel of the product.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.ProductChannelEnum.ProductChannel product_channel = 30;</code>
     */
    protected $product_channel = 0;
    /**
     * Channel exclusivity of the product.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.ProductChannelExclusivityEnum.ProductChannelExclusivity product_channel_exclusivity = 31;</code>
     */
    protected $product_channel_exclusivity = 0;
    /**
     * Condition of the product.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.ProductConditionEnum.ProductCondition product_condition = 32;</code>
     */
    protected $product_condition = 0;
    /**
     * Resource name of the geo target constant for the country of sale of the
     * product.
     *
     * Generated from protobuf field <code>optional string product_country = 98;</code>
     */
    protected $product_country = null;
    /**
     * Custom attribute 0 of the product.
     *
     * Generated from protobuf field <code>optional string product_custom_attribute0 = 99;</code>
     */
    protected $product_custom_attribute0 = null;
    /**
     * Custom attribute 1 of the product.
     *
     * Generated from protobuf field <code>optional string product_custom_attribute1 = 100;</code>
     */
    protected $product_custom_attribute1 = null;
    /**
     * Custom attribute 2 of the product.
     *
     * Generated from protobuf field <code>optional string product_custom_attribute2 = 101;</code>
     */
    protected $product_custom_attribute2 = null;
    /**
     * Custom attribute 3 of the product.
     *
     * Generated from protobuf field <code>optional string product_custom_attribute3 = 102;</code>
     */
    protected $product_custom_attribute3 = null;
    /**
     * Custom attribute 4 of the product.
     *
     * Generated from protobuf field <code>optional string product_custom_attribute4 = 103;</code>
     */
    protected $product_custom_attribute4 = null;
    /**
     * Feed label of the product.
     *
     * Generated from protobuf field <code>optional string product_feed_label = 147;</code>
     */
    protected $product_feed_label = null;
    /**
     * Item ID of the product.
     *
     * Generated from protobuf field <code>optional string product_item_id = 104;</code>
     */
    protected $product_item_id = null;
    /**
     * Resource name of the language constant for the language of the product.
     *
     * Generated from protobuf field <code>optional string product_language = 105;</code>
     */
    protected $product_language = null;
    /**
     * Merchant ID of the product.
     *
     * Generated from protobuf field <code>optional int64 product_merchant_id = 133;</code>
     */
    protected $product_merchant_id = null;
    /**
     * Store ID of the product.
     *
     * Generated from protobuf field <code>optional string product_store_id = 106;</code>
     */
    protected $product_store_id = null;
    /**
     * Title of the product.
     *
     * Generated from protobuf field <code>optional string product_title = 107;</code>
     */
    protected $product_title = null;
    /**
     * Type (level 1) of the product.
     *
     * Generated from protobuf field <code>optional string product_type_l1 = 108;</code>
     */
    protected $product_type_l1 = null;
    /**
     * Type (level 2) of the product.
     *
     * Generated from protobuf field <code>optional string product_type_l2 = 109;</code>
     */
    protected $product_type_l2 = null;
    /**
     * Type (level 3) of the product.
     *
     * Generated from protobuf field <code>optional string product_type_l3 = 110;</code>
     */
    protected $product_type_l3 = null;
    /**
     * Type (level 4) of the product.
     *
     * Generated from protobuf field <code>optional string product_type_l4 = 111;</code>
     */
    protected $product_type_l4 = null;
    /**
     * Type (level 5) of the product.
     *
     * Generated from protobuf field <code>optional string product_type_l5 = 112;</code>
     */
    protected $product_type_l5 = null;
    /**
     * Quarter as represented by the date of the first day of a quarter.
     * Uses the calendar year for quarters, for example, the second quarter of
     * 2018 starts on 2018-04-01. Formatted as yyyy-MM-dd.
     *
     * Generated from protobuf field <code>optional string quarter = 128;</code>
     */
    protected $quarter = null;
    /**
     * Recommendation type.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.RecommendationTypeEnum.RecommendationType recommendation_type = 140;</code>
     */
    protected $recommendation_type = 0;
    /**
     * Type of the search engine results page.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.SearchEngineResultsPageTypeEnum.SearchEngineResultsPageType search_engine_results_page_type = 70;</code>
     */
    protected $search_engine_results_page_type = 0;
    /**
     * A search term subcategory. An empty string denotes the catch-all
     * subcategory for search terms that didn't fit into another subcategory.
     *
     * Generated from protobuf field <code>optional string search_subcategory = 155;</code>
     */
    protected $search_subcategory = null;
    /**
     * A search term.
     *
     * Generated from protobuf field <code>optional string search_term = 156;</code>
     */
    protected $search_term = null;
    /**
     * Match type of the keyword that triggered the ad, including variants.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.SearchTermMatchTypeEnum.SearchTermMatchType search_term_match_type = 22;</code>
     */
    protected $search_term_match_type = 0;
    /**
     * Position of the ad.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.SlotEnum.Slot slot = 23;</code>
     */
    protected $slot = 0;
    /**
     * Primary dimension of applied conversion value rules.
     * NO_RULE_APPLIED shows the total recorded value of conversions that
     * do not have a value rule applied.
     * ORIGINAL shows the original value of conversions to which a value rule
     * has been applied.
     * GEO_LOCATION, DEVICE, AUDIENCE show the net adjustment after value
     * rules were applied.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.ConversionValueRulePrimaryDimensionEnum.ConversionValueRulePrimaryDimension conversion_value_rule_primary_dimension = 138;</code>
     */
    protected $conversion_value_rule_primary_dimension = 0;
    /**
     * Resource name of the ad group criterion that represents webpage criterion.
     *
     * Generated from protobuf field <code>optional string webpage = 129;</code>
     */
    protected $webpage = null;
    /**
     * Week as defined as Monday through Sunday, and represented by the date of
     * Monday. Formatted as yyyy-MM-dd.
     *
     * Generated from protobuf field <code>optional string week = 130;</code>
     */
    protected $week = null;
    /**
     * Year, formatted as yyyy.
     *
     * Generated from protobuf field <code>optional int32 year = 131;</code>
     */
    protected $year = null;
    /**
     * iOS Store Kit Ad Network conversion value.
     * Null value means this segment is not applicable, for example, non-iOS
     * campaign.
     *
     * Generated from protobuf field <code>optional int64 sk_ad_network_conversion_value = 137;</code>
     */
    protected $sk_ad_network_conversion_value = null;
    /**
     * iOS Store Kit Ad Network user type.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.SkAdNetworkUserTypeEnum.SkAdNetworkUserType sk_ad_network_user_type = 141;</code>
     */
    protected $sk_ad_network_user_type = 0;
    /**
     * iOS Store Kit Ad Network ad event type.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.SkAdNetworkAdEventTypeEnum.SkAdNetworkAdEventType sk_ad_network_ad_event_type = 142;</code>
     */
    protected $sk_ad_network_ad_event_type = 0;
    /**
     * App where the ad that drove the iOS Store Kit Ad Network install was
     * shown. Null value means this segment is not applicable, for example,
     * non-iOS campaign, or was not present in any postbacks sent by Apple.
     *
     * Generated from protobuf field <code>optional .google.ads.googleads.v16.common.SkAdNetworkSourceApp sk_ad_network_source_app = 143;</code>
     */
    protected $sk_ad_network_source_app = null;
    /**
     * iOS Store Kit Ad Network attribution credit
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.SkAdNetworkAttributionCreditEnum.SkAdNetworkAttributionCredit sk_ad_network_attribution_credit = 144;</code>
     */
    protected $sk_ad_network_attribution_credit = 0;
    /**
     * iOS Store Kit Ad Network coarse conversion value.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.SkAdNetworkCoarseConversionValueEnum.SkAdNetworkCoarseConversionValue sk_ad_network_coarse_conversion_value = 151;</code>
     */
    protected $sk_ad_network_coarse_conversion_value = 0;
    /**
     * Website where the ad that drove the iOS Store Kit Ad Network install was
     * shown. Null value means this segment is not applicable, for example,
     * non-iOS campaign, or was not present in any postbacks sent by Apple.
     *
     * Generated from protobuf field <code>optional string sk_ad_network_source_domain = 152;</code>
     */
    protected $sk_ad_network_source_domain = null;
    /**
     * The source type where the ad that drove the iOS Store Kit Ad Network
     * install was shown. Null value means this segment is not applicable, for
     * example, non-iOS campaign, or neither source domain nor source app were
     * present in any postbacks sent by Apple.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.SkAdNetworkSourceTypeEnum.SkAdNetworkSourceType sk_ad_network_source_type = 153;</code>
     */
    protected $sk_ad_network_source_type = 0;
    /**
     * iOS Store Kit Ad Network postback sequence index.
     *
     * Generated from protobuf field <code>optional int64 sk_ad_network_postback_sequence_index = 154;</code>
     */
    protected $sk_ad_network_postback_sequence_index = null;
    /**
     * Only used with CustomerAsset, CampaignAsset and AdGroupAsset metrics.
     * Indicates whether the interaction metrics occurred on the asset itself
     * or a different asset or ad unit.
     * Interactions (for example, clicks) are counted across all the parts of the
     * served ad (for example, Ad itself and other components like Sitelinks) when
     * they are served together. When interaction_on_this_asset is true, it means
     * the interactions are on this specific asset and when
     * interaction_on_this_asset is false, it means the interactions is not on
     * this specific asset but on other parts of the served ad this asset is
     * served with.
     *
     * Generated from protobuf field <code>optional .google.ads.googleads.v16.common.AssetInteractionTarget asset_interaction_target = 139;</code>
     */
    protected $asset_interaction_target = null;
    /**
     * This is for segmenting conversions by whether the user is a new customer
     * or a returning customer. This segmentation is typically used to measure
     * the impact of customer acquisition goal.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.ConvertingUserPriorEngagementTypeAndLtvBucketEnum.ConvertingUserPriorEngagementTypeAndLtvBucket new_versus_returning_customers = 160;</code>
     */
    protected $new_versus_returning_customers = 0;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int|string $activity_account_id
     *           Activity account ID.
     *     @type string $activity_city
     *           The city where the travel activity is available.
     *     @type string $activity_country
     *           The country where the travel activity is available.
     *     @type int|string $activity_rating
     *           Activity rating.
     *     @type string $activity_state
     *           The state where the travel activity is available.
     *     @type string $external_activity_id
     *           Advertiser supplied activity ID.
     *     @type int $ad_destination_type
     *           Ad Destination type.
     *     @type int $ad_network_type
     *           Ad network type.
     *     @type string $ad_group
     *           Resource name of the ad group.
     *     @type string $asset_group
     *           Resource name of the asset group.
     *     @type string $auction_insight_domain
     *           Domain (visible URL) of a participant in the Auction Insights report.
     *     @type \Google\Ads\GoogleAds\V16\Common\BudgetCampaignAssociationStatus $budget_campaign_association_status
     *           Budget campaign association status.
     *     @type string $campaign
     *           Resource name of the campaign.
     *     @type int $click_type
     *           Click type.
     *     @type string $conversion_action
     *           Resource name of the conversion action.
     *     @type int $conversion_action_category
     *           Conversion action category.
     *     @type string $conversion_action_name
     *           Conversion action name.
     *     @type bool $conversion_adjustment
     *           This segments your conversion columns by the original conversion and
     *           conversion value versus the delta if conversions were adjusted. False row
     *           has the data as originally stated; While true row has the delta between
     *           data now and the data as originally stated. Summing the two together
     *           results post-adjustment data.
     *     @type int $conversion_attribution_event_type
     *           Conversion attribution event type.
     *     @type int $conversion_lag_bucket
     *           An enum value representing the number of days between the impression and
     *           the conversion.
     *     @type int $conversion_or_adjustment_lag_bucket
     *           An enum value representing the number of days between the impression and
     *           the conversion or between the impression and adjustments to the conversion.
     *     @type string $date
     *           Date to which metrics apply.
     *           yyyy-MM-dd format, for example, 2018-04-17.
     *     @type int $day_of_week
     *           Day of the week, for example, MONDAY.
     *     @type int $device
     *           Device to which metrics apply.
     *     @type int $external_conversion_source
     *           External conversion source.
     *     @type string $geo_target_airport
     *           Resource name of the geo target constant that represents an airport.
     *     @type string $geo_target_canton
     *           Resource name of the geo target constant that represents a canton.
     *     @type string $geo_target_city
     *           Resource name of the geo target constant that represents a city.
     *     @type string $geo_target_country
     *           Resource name of the geo target constant that represents a country.
     *     @type string $geo_target_county
     *           Resource name of the geo target constant that represents a county.
     *     @type string $geo_target_district
     *           Resource name of the geo target constant that represents a district.
     *     @type string $geo_target_metro
     *           Resource name of the geo target constant that represents a metro.
     *     @type string $geo_target_most_specific_location
     *           Resource name of the geo target constant that represents the most
     *           specific location.
     *     @type string $geo_target_postal_code
     *           Resource name of the geo target constant that represents a postal code.
     *     @type string $geo_target_province
     *           Resource name of the geo target constant that represents a province.
     *     @type string $geo_target_region
     *           Resource name of the geo target constant that represents a region.
     *     @type string $geo_target_state
     *           Resource name of the geo target constant that represents a state.
     *     @type int|string $hotel_booking_window_days
     *           Hotel booking window in days.
     *     @type int|string $hotel_center_id
     *           Hotel center ID.
     *     @type string $hotel_check_in_date
     *           Hotel check-in date. Formatted as yyyy-MM-dd.
     *     @type int $hotel_check_in_day_of_week
     *           Hotel check-in day of week.
     *     @type string $hotel_city
     *           Hotel city.
     *     @type int $hotel_class
     *           Hotel class.
     *     @type string $hotel_country
     *           Hotel country.
     *     @type int $hotel_date_selection_type
     *           Hotel date selection type.
     *     @type int $hotel_length_of_stay
     *           Hotel length of stay.
     *     @type string $hotel_rate_rule_id
     *           Hotel rate rule ID.
     *     @type int $hotel_rate_type
     *           Hotel rate type.
     *     @type int $hotel_price_bucket
     *           Hotel price bucket.
     *     @type string $hotel_state
     *           Hotel state.
     *     @type int $hour
     *           Hour of day as a number between 0 and 23, inclusive.
     *     @type bool $interaction_on_this_extension
     *           Only used with feed item metrics.
     *           Indicates whether the interaction metrics occurred on the feed item itself
     *           or a different extension or ad unit.
     *     @type \Google\Ads\GoogleAds\V16\Common\Keyword $keyword
     *           Keyword criterion.
     *     @type string $month
     *           Month as represented by the date of the first day of a month. Formatted as
     *           yyyy-MM-dd.
     *     @type int $month_of_year
     *           Month of the year, for example, January.
     *     @type string $partner_hotel_id
     *           Partner hotel ID.
     *     @type int $placeholder_type
     *           Placeholder type. This is only used with feed item metrics.
     *     @type int|string $product_aggregator_id
     *           Aggregator ID of the product.
     *     @type string $product_category_level1
     *           Category (level 1) of the product.
     *     @type string $product_category_level2
     *           Category (level 2) of the product.
     *     @type string $product_category_level3
     *           Category (level 3) of the product.
     *     @type string $product_category_level4
     *           Category (level 4) of the product.
     *     @type string $product_category_level5
     *           Category (level 5) of the product.
     *     @type string $product_brand
     *           Brand of the product.
     *     @type int $product_channel
     *           Channel of the product.
     *     @type int $product_channel_exclusivity
     *           Channel exclusivity of the product.
     *     @type int $product_condition
     *           Condition of the product.
     *     @type string $product_country
     *           Resource name of the geo target constant for the country of sale of the
     *           product.
     *     @type string $product_custom_attribute0
     *           Custom attribute 0 of the product.
     *     @type string $product_custom_attribute1
     *           Custom attribute 1 of the product.
     *     @type string $product_custom_attribute2
     *           Custom attribute 2 of the product.
     *     @type string $product_custom_attribute3
     *           Custom attribute 3 of the product.
     *     @type string $product_custom_attribute4
     *           Custom attribute 4 of the product.
     *     @type string $product_feed_label
     *           Feed label of the product.
     *     @type string $product_item_id
     *           Item ID of the product.
     *     @type string $product_language
     *           Resource name of the language constant for the language of the product.
     *     @type int|string $product_merchant_id
     *           Merchant ID of the product.
     *     @type string $product_store_id
     *           Store ID of the product.
     *     @type string $product_title
     *           Title of the product.
     *     @type string $product_type_l1
     *           Type (level 1) of the product.
     *     @type string $product_type_l2
     *           Type (level 2) of the product.
     *     @type string $product_type_l3
     *           Type (level 3) of the product.
     *     @type string $product_type_l4
     *           Type (level 4) of the product.
     *     @type string $product_type_l5
     *           Type (level 5) of the product.
     *     @type string $quarter
     *           Quarter as represented by the date of the first day of a quarter.
     *           Uses the calendar year for quarters, for example, the second quarter of
     *           2018 starts on 2018-04-01. Formatted as yyyy-MM-dd.
     *     @type int $recommendation_type
     *           Recommendation type.
     *     @type int $search_engine_results_page_type
     *           Type of the search engine results page.
     *     @type string $search_subcategory
     *           A search term subcategory. An empty string denotes the catch-all
     *           subcategory for search terms that didn't fit into another subcategory.
     *     @type string $search_term
     *           A search term.
     *     @type int $search_term_match_type
     *           Match type of the keyword that triggered the ad, including variants.
     *     @type int $slot
     *           Position of the ad.
     *     @type int $conversion_value_rule_primary_dimension
     *           Primary dimension of applied conversion value rules.
     *           NO_RULE_APPLIED shows the total recorded value of conversions that
     *           do not have a value rule applied.
     *           ORIGINAL shows the original value of conversions to which a value rule
     *           has been applied.
     *           GEO_LOCATION, DEVICE, AUDIENCE show the net adjustment after value
     *           rules were applied.
     *     @type string $webpage
     *           Resource name of the ad group criterion that represents webpage criterion.
     *     @type string $week
     *           Week as defined as Monday through Sunday, and represented by the date of
     *           Monday. Formatted as yyyy-MM-dd.
     *     @type int $year
     *           Year, formatted as yyyy.
     *     @type int|string $sk_ad_network_conversion_value
     *           iOS Store Kit Ad Network conversion value.
     *           Null value means this segment is not applicable, for example, non-iOS
     *           campaign.
     *     @type int $sk_ad_network_user_type
     *           iOS Store Kit Ad Network user type.
     *     @type int $sk_ad_network_ad_event_type
     *           iOS Store Kit Ad Network ad event type.
     *     @type \Google\Ads\GoogleAds\V16\Common\SkAdNetworkSourceApp $sk_ad_network_source_app
     *           App where the ad that drove the iOS Store Kit Ad Network install was
     *           shown. Null value means this segment is not applicable, for example,
     *           non-iOS campaign, or was not present in any postbacks sent by Apple.
     *     @type int $sk_ad_network_attribution_credit
     *           iOS Store Kit Ad Network attribution credit
     *     @type int $sk_ad_network_coarse_conversion_value
     *           iOS Store Kit Ad Network coarse conversion value.
     *     @type string $sk_ad_network_source_domain
     *           Website where the ad that drove the iOS Store Kit Ad Network install was
     *           shown. Null value means this segment is not applicable, for example,
     *           non-iOS campaign, or was not present in any postbacks sent by Apple.
     *     @type int $sk_ad_network_source_type
     *           The source type where the ad that drove the iOS Store Kit Ad Network
     *           install was shown. Null value means this segment is not applicable, for
     *           example, non-iOS campaign, or neither source domain nor source app were
     *           present in any postbacks sent by Apple.
     *     @type int|string $sk_ad_network_postback_sequence_index
     *           iOS Store Kit Ad Network postback sequence index.
     *     @type \Google\Ads\GoogleAds\V16\Common\AssetInteractionTarget $asset_interaction_target
     *           Only used with CustomerAsset, CampaignAsset and AdGroupAsset metrics.
     *           Indicates whether the interaction metrics occurred on the asset itself
     *           or a different asset or ad unit.
     *           Interactions (for example, clicks) are counted across all the parts of the
     *           served ad (for example, Ad itself and other components like Sitelinks) when
     *           they are served together. When interaction_on_this_asset is true, it means
     *           the interactions are on this specific asset and when
     *           interaction_on_this_asset is false, it means the interactions is not on
     *           this specific asset but on other parts of the served ad this asset is
     *           served with.
     *     @type int $new_versus_returning_customers
     *           This is for segmenting conversions by whether the user is a new customer
     *           or a returning customer. This segmentation is typically used to measure
     *           the impact of customer acquisition goal.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Ads\GoogleAds\V16\Common\Segments::initOnce();
        parent::__construct($data);
    }

    /**
     * Activity account ID.
     *
     * Generated from protobuf field <code>optional int64 activity_account_id = 148;</code>
     * @return int|string
     */
    public function getActivityAccountId()
    {
        return isset($this->activity_account_id) ? $this->activity_account_id : 0;
    }

    public function hasActivityAccountId()
    {
        return isset($this->activity_account_id);
    }

    public function clearActivityAccountId()
    {
        unset($this->activity_account_id);
    }

    /**
     * Activity account ID.
     *
     * Generated from protobuf field <code>optional int64 activity_account_id = 148;</code>
     * @param int|string $var
     * @return $this
     */
    public function setActivityAccountId($var)
    {
        GPBUtil::checkInt64($var);
        $this->activity_account_id = $var;

        return $this;
    }

    /**
     * The city where the travel activity is available.
     *
     * Generated from protobuf field <code>optional string activity_city = 185;</code>
     * @return string
     */
    public function getActivityCity()
    {
        return isset($this->activity_city) ? $this->activity_city : '';
    }

    public function hasActivityCity()
    {
        return isset($this->activity_city);
    }

    public function clearActivityCity()
    {
        unset($this->activity_city);
    }

    /**
     * The city where the travel activity is available.
     *
     * Generated from protobuf field <code>optional string activity_city = 185;</code>
     * @param string $var
     * @return $this
     */
    public function setActivityCity($var)
    {
        GPBUtil::checkString($var, True);
        $this->activity_city = $var;

        return $this;
    }

    /**
     * The country where the travel activity is available.
     *
     * Generated from protobuf field <code>optional string activity_country = 186;</code>
     * @return string
     */
    public function getActivityCountry()
    {
        return isset($this->activity_country) ? $this->activity_country : '';
    }

    public function hasActivityCountry()
    {
        return isset($this->activity_country);
    }

    public function clearActivityCountry()
    {
        unset($this->activity_country);
    }

    /**
     * The country where the travel activity is available.
     *
     * Generated from protobuf field <code>optional string activity_country = 186;</code>
     * @param string $var
     * @return $this
     */
    public function setActivityCountry($var)
    {
        GPBUtil::checkString($var, True);
        $this->activity_country = $var;

        return $this;
    }

    /**
     * Activity rating.
     *
     * Generated from protobuf field <code>optional int64 activity_rating = 149;</code>
     * @return int|string
     */
    public function getActivityRating()
    {
        return isset($this->activity_rating) ? $this->activity_rating : 0;
    }

    public function hasActivityRating()
    {
        return isset($this->activity_rating);
    }

    public function clearActivityRating()
    {
        unset($this->activity_rating);
    }

    /**
     * Activity rating.
     *
     * Generated from protobuf field <code>optional int64 activity_rating = 149;</code>
     * @param int|string $var
     * @return $this
     */
    public function setActivityRating($var)
    {
        GPBUtil::checkInt64($var);
        $this->activity_rating = $var;

        return $this;
    }

    /**
     * The state where the travel activity is available.
     *
     * Generated from protobuf field <code>optional string activity_state = 187;</code>
     * @return string
     */
    public function getActivityState()
    {
        return isset($this->activity_state) ? $this->activity_state : '';
    }

    public function hasActivityState()
    {
        return isset($this->activity_state);
    }

    public function clearActivityState()
    {
        unset($this->activity_state);
    }

    /**
     * The state where the travel activity is available.
     *
     * Generated from protobuf field <code>optional string activity_state = 187;</code>
     * @param string $var
     * @return $this
     */
    public function setActivityState($var)
    {
        GPBUtil::checkString($var, True);
        $this->activity_state = $var;

        return $this;
    }

    /**
     * Advertiser supplied activity ID.
     *
     * Generated from protobuf field <code>optional string external_activity_id = 150;</code>
     * @return string
     */
    public function getExternalActivityId()
    {
        return isset($this->external_activity_id) ? $this->external_activity_id : '';
    }

    public function hasExternalActivityId()
    {
        return isset($this->external_activity_id);
    }

    public function clearExternalActivityId()
    {
        unset($this->external_activity_id);
    }

    /**
     * Advertiser supplied activity ID.
     *
     * Generated from protobuf field <code>optional string external_activity_id = 150;</code>
     * @param string $var
     * @return $this
     */
    public function setExternalActivityId($var)
    {
        GPBUtil::checkString($var, True);
        $this->external_activity_id = $var;

        return $this;
    }

    /**
     * Ad Destination type.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.AdDestinationTypeEnum.AdDestinationType ad_destination_type = 136;</code>
     * @return int
     */
    public function getAdDestinationType()
    {
        return $this->ad_destination_type;
    }

    /**
     * Ad Destination type.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.AdDestinationTypeEnum.AdDestinationType ad_destination_type = 136;</code>
     * @param int $var
     * @return $this
     */
    public function setAdDestinationType($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V16\Enums\AdDestinationTypeEnum\AdDestinationType::class);
        $this->ad_destination_type = $var;

        return $this;
    }

    /**
     * Ad network type.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.AdNetworkTypeEnum.AdNetworkType ad_network_type = 3;</code>
     * @return int
     */
    public function getAdNetworkType()
    {
        return $this->ad_network_type;
    }

    /**
     * Ad network type.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.AdNetworkTypeEnum.AdNetworkType ad_network_type = 3;</code>
     * @param int $var
     * @return $this
     */
    public function setAdNetworkType($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V16\Enums\AdNetworkTypeEnum\AdNetworkType::class);
        $this->ad_network_type = $var;

        return $this;
    }

    /**
     * Resource name of the ad group.
     *
     * Generated from protobuf field <code>optional string ad_group = 158;</code>
     * @return string
     */
    public function getAdGroup()
    {
        return isset($this->ad_group) ? $this->ad_group : '';
    }

    public function hasAdGroup()
    {
        return isset($this->ad_group);
    }

    public function clearAdGroup()
    {
        unset($this->ad_group);
    }

    /**
     * Resource name of the ad group.
     *
     * Generated from protobuf field <code>optional string ad_group = 158;</code>
     * @param string $var
     * @return $this
     */
    public function setAdGroup($var)
    {
        GPBUtil::checkString($var, True);
        $this->ad_group = $var;

        return $this;
    }

    /**
     * Resource name of the asset group.
     *
     * Generated from protobuf field <code>optional string asset_group = 159;</code>
     * @return string
     */
    public function getAssetGroup()
    {
        return isset($this->asset_group) ? $this->asset_group : '';
    }

    public function hasAssetGroup()
    {
        return isset($this->asset_group);
    }

    public function clearAssetGroup()
    {
        unset($this->asset_group);
    }

    /**
     * Resource name of the asset group.
     *
     * Generated from protobuf field <code>optional string asset_group = 159;</code>
     * @param string $var
     * @return $this
     */
    public function setAssetGroup($var)
    {
        GPBUtil::checkString($var, True);
        $this->asset_group = $var;

        return $this;
    }

    /**
     * Domain (visible URL) of a participant in the Auction Insights report.
     *
     * Generated from protobuf field <code>optional string auction_insight_domain = 145;</code>
     * @return string
     */
    public function getAuctionInsightDomain()
    {
        return isset($this->auction_insight_domain) ? $this->auction_insight_domain : '';
    }

    public function hasAuctionInsightDomain()
    {
        return isset($this->auction_insight_domain);
    }

    public function clearAuctionInsightDomain()
    {
        unset($this->auction_insight_domain);
    }

    /**
     * Domain (visible URL) of a participant in the Auction Insights report.
     *
     * Generated from protobuf field <code>optional string auction_insight_domain = 145;</code>
     * @param string $var
     * @return $this
     */
    public function setAuctionInsightDomain($var)
    {
        GPBUtil::checkString($var, True);
        $this->auction_insight_domain = $var;

        return $this;
    }

    /**
     * Budget campaign association status.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.common.BudgetCampaignAssociationStatus budget_campaign_association_status = 134;</code>
     * @return \Google\Ads\GoogleAds\V16\Common\BudgetCampaignAssociationStatus|null
     */
    public function getBudgetCampaignAssociationStatus()
    {
        return $this->budget_campaign_association_status;
    }

    public function hasBudgetCampaignAssociationStatus()
    {
        return isset($this->budget_campaign_association_status);
    }

    public function clearBudgetCampaignAssociationStatus()
    {
        unset($this->budget_campaign_association_status);
    }

    /**
     * Budget campaign association status.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.common.BudgetCampaignAssociationStatus budget_campaign_association_status = 134;</code>
     * @param \Google\Ads\GoogleAds\V16\Common\BudgetCampaignAssociationStatus $var
     * @return $this
     */
    public function setBudgetCampaignAssociationStatus($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V16\Common\BudgetCampaignAssociationStatus::class);
        $this->budget_campaign_association_status = $var;

        return $this;
    }

    /**
     * Resource name of the campaign.
     *
     * Generated from protobuf field <code>optional string campaign = 157;</code>
     * @return string
     */
    public function getCampaign()
    {
        return isset($this->campaign) ? $this->campaign : '';
    }

    public function hasCampaign()
    {
        return isset($this->campaign);
    }

    public function clearCampaign()
    {
        unset($this->campaign);
    }

    /**
     * Resource name of the campaign.
     *
     * Generated from protobuf field <code>optional string campaign = 157;</code>
     * @param string $var
     * @return $this
     */
    public function setCampaign($var)
    {
        GPBUtil::checkString($var, True);
        $this->campaign = $var;

        return $this;
    }

    /**
     * Click type.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.ClickTypeEnum.ClickType click_type = 26;</code>
     * @return int
     */
    public function getClickType()
    {
        return $this->click_type;
    }

    /**
     * Click type.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.ClickTypeEnum.ClickType click_type = 26;</code>
     * @param int $var
     * @return $this
     */
    public function setClickType($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V16\Enums\ClickTypeEnum\ClickType::class);
        $this->click_type = $var;

        return $this;
    }

    /**
     * Resource name of the conversion action.
     *
     * Generated from protobuf field <code>optional string conversion_action = 113 [(.google.api.resource_reference) = {</code>
     * @return string
     */
    public function getConversionAction()
    {
        return isset($this->conversion_action) ? $this->conversion_action : '';
    }

    public function hasConversionAction()
    {
        return isset($this->conversion_action);
    }

    public function clearConversionAction()
    {
        unset($this->conversion_action);
    }

    /**
     * Resource name of the conversion action.
     *
     * Generated from protobuf field <code>optional string conversion_action = 113 [(.google.api.resource_reference) = {</code>
     * @param string $var
     * @return $this
     */
    public function setConversionAction($var)
    {
        GPBUtil::checkString($var, True);
        $this->conversion_action = $var;

        return $this;
    }

    /**
     * Conversion action category.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.ConversionActionCategoryEnum.ConversionActionCategory conversion_action_category = 53;</code>
     * @return int
     */
    public function getConversionActionCategory()
    {
        return $this->conversion_action_category;
    }

    /**
     * Conversion action category.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.ConversionActionCategoryEnum.ConversionActionCategory conversion_action_category = 53;</code>
     * @param int $var
     * @return $this
     */
    public function setConversionActionCategory($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V16\Enums\ConversionActionCategoryEnum\ConversionActionCategory::class);
        $this->conversion_action_category = $var;

        return $this;
    }

    /**
     * Conversion action name.
     *
     * Generated from protobuf field <code>optional string conversion_action_name = 114;</code>
     * @return string
     */
    public function getConversionActionName()
    {
        return isset($this->conversion_action_name) ? $this->conversion_action_name : '';
    }

    public function hasConversionActionName()
    {
        return isset($this->conversion_action_name);
    }

    public function clearConversionActionName()
    {
        unset($this->conversion_action_name);
    }

    /**
     * Conversion action name.
     *
     * Generated from protobuf field <code>optional string conversion_action_name = 114;</code>
     * @param string $var
     * @return $this
     */
    public function setConversionActionName($var)
    {
        GPBUtil::checkString($var, True);
        $this->conversion_action_name = $var;

        return $this;
    }

    /**
     * This segments your conversion columns by the original conversion and
     * conversion value versus the delta if conversions were adjusted. False row
     * has the data as originally stated; While true row has the delta between
     * data now and the data as originally stated. Summing the two together
     * results post-adjustment data.
     *
     * Generated from protobuf field <code>optional bool conversion_adjustment = 115;</code>
     * @return bool
     */
    public function getConversionAdjustment()
    {
        return isset($this->conversion_adjustment) ? $this->conversion_adjustment : false;
    }

    public function hasConversionAdjustment()
    {
        return isset($this->conversion_adjustment);
    }

    public function clearConversionAdjustment()
    {
        unset($this->conversion_adjustment);
    }

    /**
     * This segments your conversion columns by the original conversion and
     * conversion value versus the delta if conversions were adjusted. False row
     * has the data as originally stated; While true row has the delta between
     * data now and the data as originally stated. Summing the two together
     * results post-adjustment data.
     *
     * Generated from protobuf field <code>optional bool conversion_adjustment = 115;</code>
     * @param bool $var
     * @return $this
     */
    public function setConversionAdjustment($var)
    {
        GPBUtil::checkBool($var);
        $this->conversion_adjustment = $var;

        return $this;
    }

    /**
     * Conversion attribution event type.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.ConversionAttributionEventTypeEnum.ConversionAttributionEventType conversion_attribution_event_type = 2;</code>
     * @return int
     */
    public function getConversionAttributionEventType()
    {
        return $this->conversion_attribution_event_type;
    }

    /**
     * Conversion attribution event type.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.ConversionAttributionEventTypeEnum.ConversionAttributionEventType conversion_attribution_event_type = 2;</code>
     * @param int $var
     * @return $this
     */
    public function setConversionAttributionEventType($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V16\Enums\ConversionAttributionEventTypeEnum\ConversionAttributionEventType::class);
        $this->conversion_attribution_event_type = $var;

        return $this;
    }

    /**
     * An enum value representing the number of days between the impression and
     * the conversion.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.ConversionLagBucketEnum.ConversionLagBucket conversion_lag_bucket = 50;</code>
     * @return int
     */
    public function getConversionLagBucket()
    {
        return $this->conversion_lag_bucket;
    }

    /**
     * An enum value representing the number of days between the impression and
     * the conversion.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.ConversionLagBucketEnum.ConversionLagBucket conversion_lag_bucket = 50;</code>
     * @param int $var
     * @return $this
     */
    public function setConversionLagBucket($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V16\Enums\ConversionLagBucketEnum\ConversionLagBucket::class);
        $this->conversion_lag_bucket = $var;

        return $this;
    }

    /**
     * An enum value representing the number of days between the impression and
     * the conversion or between the impression and adjustments to the conversion.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.ConversionOrAdjustmentLagBucketEnum.ConversionOrAdjustmentLagBucket conversion_or_adjustment_lag_bucket = 51;</code>
     * @return int
     */
    public function getConversionOrAdjustmentLagBucket()
    {
        return $this->conversion_or_adjustment_lag_bucket;
    }

    /**
     * An enum value representing the number of days between the impression and
     * the conversion or between the impression and adjustments to the conversion.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.ConversionOrAdjustmentLagBucketEnum.ConversionOrAdjustmentLagBucket conversion_or_adjustment_lag_bucket = 51;</code>
     * @param int $var
     * @return $this
     */
    public function setConversionOrAdjustmentLagBucket($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V16\Enums\ConversionOrAdjustmentLagBucketEnum\ConversionOrAdjustmentLagBucket::class);
        $this->conversion_or_adjustment_lag_bucket = $var;

        return $this;
    }

    /**
     * Date to which metrics apply.
     * yyyy-MM-dd format, for example, 2018-04-17.
     *
     * Generated from protobuf field <code>optional string date = 79;</code>
     * @return string
     */
    public function getDate()
    {
        return isset($this->date) ? $this->date : '';
    }

    public function hasDate()
    {
        return isset($this->date);
    }

    public function clearDate()
    {
        unset($this->date);
    }

    /**
     * Date to which metrics apply.
     * yyyy-MM-dd format, for example, 2018-04-17.
     *
     * Generated from protobuf field <code>optional string date = 79;</code>
     * @param string $var
     * @return $this
     */
    public function setDate($var)
    {
        GPBUtil::checkString($var, True);
        $this->date = $var;

        return $this;
    }

    /**
     * Day of the week, for example, MONDAY.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.DayOfWeekEnum.DayOfWeek day_of_week = 5;</code>
     * @return int
     */
    public function getDayOfWeek()
    {
        return $this->day_of_week;
    }

    /**
     * Day of the week, for example, MONDAY.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.DayOfWeekEnum.DayOfWeek day_of_week = 5;</code>
     * @param int $var
     * @return $this
     */
    public function setDayOfWeek($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V16\Enums\DayOfWeekEnum\DayOfWeek::class);
        $this->day_of_week = $var;

        return $this;
    }

    /**
     * Device to which metrics apply.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.DeviceEnum.Device device = 1;</code>
     * @return int
     */
    public function getDevice()
    {
        return $this->device;
    }

    /**
     * Device to which metrics apply.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.DeviceEnum.Device device = 1;</code>
     * @param int $var
     * @return $this
     */
    public function setDevice($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V16\Enums\DeviceEnum\Device::class);
        $this->device = $var;

        return $this;
    }

    /**
     * External conversion source.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.ExternalConversionSourceEnum.ExternalConversionSource external_conversion_source = 55;</code>
     * @return int
     */
    public function getExternalConversionSource()
    {
        return $this->external_conversion_source;
    }

    /**
     * External conversion source.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.ExternalConversionSourceEnum.ExternalConversionSource external_conversion_source = 55;</code>
     * @param int $var
     * @return $this
     */
    public function setExternalConversionSource($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V16\Enums\ExternalConversionSourceEnum\ExternalConversionSource::class);
        $this->external_conversion_source = $var;

        return $this;
    }

    /**
     * Resource name of the geo target constant that represents an airport.
     *
     * Generated from protobuf field <code>optional string geo_target_airport = 116;</code>
     * @return string
     */
    public function getGeoTargetAirport()
    {
        return isset($this->geo_target_airport) ? $this->geo_target_airport : '';
    }

    public function hasGeoTargetAirport()
    {
        return isset($this->geo_target_airport);
    }

    public function clearGeoTargetAirport()
    {
        unset($this->geo_target_airport);
    }

    /**
     * Resource name of the geo target constant that represents an airport.
     *
     * Generated from protobuf field <code>optional string geo_target_airport = 116;</code>
     * @param string $var
     * @return $this
     */
    public function setGeoTargetAirport($var)
    {
        GPBUtil::checkString($var, True);
        $this->geo_target_airport = $var;

        return $this;
    }

    /**
     * Resource name of the geo target constant that represents a canton.
     *
     * Generated from protobuf field <code>optional string geo_target_canton = 117;</code>
     * @return string
     */
    public function getGeoTargetCanton()
    {
        return isset($this->geo_target_canton) ? $this->geo_target_canton : '';
    }

    public function hasGeoTargetCanton()
    {
        return isset($this->geo_target_canton);
    }

    public function clearGeoTargetCanton()
    {
        unset($this->geo_target_canton);
    }

    /**
     * Resource name of the geo target constant that represents a canton.
     *
     * Generated from protobuf field <code>optional string geo_target_canton = 117;</code>
     * @param string $var
     * @return $this
     */
    public function setGeoTargetCanton($var)
    {
        GPBUtil::checkString($var, True);
        $this->geo_target_canton = $var;

        return $this;
    }

    /**
     * Resource name of the geo target constant that represents a city.
     *
     * Generated from protobuf field <code>optional string geo_target_city = 118;</code>
     * @return string
     */
    public function getGeoTargetCity()
    {
        return isset($this->geo_target_city) ? $this->geo_target_city : '';
    }

    public function hasGeoTargetCity()
    {
        return isset($this->geo_target_city);
    }

    public function clearGeoTargetCity()
    {
        unset($this->geo_target_city);
    }

    /**
     * Resource name of the geo target constant that represents a city.
     *
     * Generated from protobuf field <code>optional string geo_target_city = 118;</code>
     * @param string $var
     * @return $this
     */
    public function setGeoTargetCity($var)
    {
        GPBUtil::checkString($var, True);
        $this->geo_target_city = $var;

        return $this;
    }

    /**
     * Resource name of the geo target constant that represents a country.
     *
     * Generated from protobuf field <code>optional string geo_target_country = 119;</code>
     * @return string
     */
    public function getGeoTargetCountry()
    {
        return isset($this->geo_target_country) ? $this->geo_target_country : '';
    }

    public function hasGeoTargetCountry()
    {
        return isset($this->geo_target_country);
    }

    public function clearGeoTargetCountry()
    {
        unset($this->geo_target_country);
    }

    /**
     * Resource name of the geo target constant that represents a country.
     *
     * Generated from protobuf field <code>optional string geo_target_country = 119;</code>
     * @param string $var
     * @return $this
     */
    public function setGeoTargetCountry($var)
    {
        GPBUtil::checkString($var, True);
        $this->geo_target_country = $var;

        return $this;
    }

    /**
     * Resource name of the geo target constant that represents a county.
     *
     * Generated from protobuf field <code>optional string geo_target_county = 120;</code>
     * @return string
     */
    public function getGeoTargetCounty()
    {
        return isset($this->geo_target_county) ? $this->geo_target_county : '';
    }

    public function hasGeoTargetCounty()
    {
        return isset($this->geo_target_county);
    }

    public function clearGeoTargetCounty()
    {
        unset($this->geo_target_county);
    }

    /**
     * Resource name of the geo target constant that represents a county.
     *
     * Generated from protobuf field <code>optional string geo_target_county = 120;</code>
     * @param string $var
     * @return $this
     */
    public function setGeoTargetCounty($var)
    {
        GPBUtil::checkString($var, True);
        $this->geo_target_county = $var;

        return $this;
    }

    /**
     * Resource name of the geo target constant that represents a district.
     *
     * Generated from protobuf field <code>optional string geo_target_district = 121;</code>
     * @return string
     */
    public function getGeoTargetDistrict()
    {
        return isset($this->geo_target_district) ? $this->geo_target_district : '';
    }

    public function hasGeoTargetDistrict()
    {
        return isset($this->geo_target_district);
    }

    public function clearGeoTargetDistrict()
    {
        unset($this->geo_target_district);
    }

    /**
     * Resource name of the geo target constant that represents a district.
     *
     * Generated from protobuf field <code>optional string geo_target_district = 121;</code>
     * @param string $var
     * @return $this
     */
    public function setGeoTargetDistrict($var)
    {
        GPBUtil::checkString($var, True);
        $this->geo_target_district = $var;

        return $this;
    }

    /**
     * Resource name of the geo target constant that represents a metro.
     *
     * Generated from protobuf field <code>optional string geo_target_metro = 122;</code>
     * @return string
     */
    public function getGeoTargetMetro()
    {
        return isset($this->geo_target_metro) ? $this->geo_target_metro : '';
    }

    public function hasGeoTargetMetro()
    {
        return isset($this->geo_target_metro);
    }

    public function clearGeoTargetMetro()
    {
        unset($this->geo_target_metro);
    }

    /**
     * Resource name of the geo target constant that represents a metro.
     *
     * Generated from protobuf field <code>optional string geo_target_metro = 122;</code>
     * @param string $var
     * @return $this
     */
    public function setGeoTargetMetro($var)
    {
        GPBUtil::checkString($var, True);
        $this->geo_target_metro = $var;

        return $this;
    }

    /**
     * Resource name of the geo target constant that represents the most
     * specific location.
     *
     * Generated from protobuf field <code>optional string geo_target_most_specific_location = 123;</code>
     * @return string
     */
    public function getGeoTargetMostSpecificLocation()
    {
        return isset($this->geo_target_most_specific_location) ? $this->geo_target_most_specific_location : '';
    }

    public function hasGeoTargetMostSpecificLocation()
    {
        return isset($this->geo_target_most_specific_location);
    }

    public function clearGeoTargetMostSpecificLocation()
    {
        unset($this->geo_target_most_specific_location);
    }

    /**
     * Resource name of the geo target constant that represents the most
     * specific location.
     *
     * Generated from protobuf field <code>optional string geo_target_most_specific_location = 123;</code>
     * @param string $var
     * @return $this
     */
    public function setGeoTargetMostSpecificLocation($var)
    {
        GPBUtil::checkString($var, True);
        $this->geo_target_most_specific_location = $var;

        return $this;
    }

    /**
     * Resource name of the geo target constant that represents a postal code.
     *
     * Generated from protobuf field <code>optional string geo_target_postal_code = 124;</code>
     * @return string
     */
    public function getGeoTargetPostalCode()
    {
        return isset($this->geo_target_postal_code) ? $this->geo_target_postal_code : '';
    }

    public function hasGeoTargetPostalCode()
    {
        return isset($this->geo_target_postal_code);
    }

    public function clearGeoTargetPostalCode()
    {
        unset($this->geo_target_postal_code);
    }

    /**
     * Resource name of the geo target constant that represents a postal code.
     *
     * Generated from protobuf field <code>optional string geo_target_postal_code = 124;</code>
     * @param string $var
     * @return $this
     */
    public function setGeoTargetPostalCode($var)
    {
        GPBUtil::checkString($var, True);
        $this->geo_target_postal_code = $var;

        return $this;
    }

    /**
     * Resource name of the geo target constant that represents a province.
     *
     * Generated from protobuf field <code>optional string geo_target_province = 125;</code>
     * @return string
     */
    public function getGeoTargetProvince()
    {
        return isset($this->geo_target_province) ? $this->geo_target_province : '';
    }

    public function hasGeoTargetProvince()
    {
        return isset($this->geo_target_province);
    }

    public function clearGeoTargetProvince()
    {
        unset($this->geo_target_province);
    }

    /**
     * Resource name of the geo target constant that represents a province.
     *
     * Generated from protobuf field <code>optional string geo_target_province = 125;</code>
     * @param string $var
     * @return $this
     */
    public function setGeoTargetProvince($var)
    {
        GPBUtil::checkString($var, True);
        $this->geo_target_province = $var;

        return $this;
    }

    /**
     * Resource name of the geo target constant that represents a region.
     *
     * Generated from protobuf field <code>optional string geo_target_region = 126;</code>
     * @return string
     */
    public function getGeoTargetRegion()
    {
        return isset($this->geo_target_region) ? $this->geo_target_region : '';
    }

    public function hasGeoTargetRegion()
    {
        return isset($this->geo_target_region);
    }

    public function clearGeoTargetRegion()
    {
        unset($this->geo_target_region);
    }

    /**
     * Resource name of the geo target constant that represents a region.
     *
     * Generated from protobuf field <code>optional string geo_target_region = 126;</code>
     * @param string $var
     * @return $this
     */
    public function setGeoTargetRegion($var)
    {
        GPBUtil::checkString($var, True);
        $this->geo_target_region = $var;

        return $this;
    }

    /**
     * Resource name of the geo target constant that represents a state.
     *
     * Generated from protobuf field <code>optional string geo_target_state = 127;</code>
     * @return string
     */
    public function getGeoTargetState()
    {
        return isset($this->geo_target_state) ? $this->geo_target_state : '';
    }

    public function hasGeoTargetState()
    {
        return isset($this->geo_target_state);
    }

    public function clearGeoTargetState()
    {
        unset($this->geo_target_state);
    }

    /**
     * Resource name of the geo target constant that represents a state.
     *
     * Generated from protobuf field <code>optional string geo_target_state = 127;</code>
     * @param string $var
     * @return $this
     */
    public function setGeoTargetState($var)
    {
        GPBUtil::checkString($var, True);
        $this->geo_target_state = $var;

        return $this;
    }

    /**
     * Hotel booking window in days.
     *
     * Generated from protobuf field <code>optional int64 hotel_booking_window_days = 135;</code>
     * @return int|string
     */
    public function getHotelBookingWindowDays()
    {
        return isset($this->hotel_booking_window_days) ? $this->hotel_booking_window_days : 0;
    }

    public function hasHotelBookingWindowDays()
    {
        return isset($this->hotel_booking_window_days);
    }

    public function clearHotelBookingWindowDays()
    {
        unset($this->hotel_booking_window_days);
    }

    /**
     * Hotel booking window in days.
     *
     * Generated from protobuf field <code>optional int64 hotel_booking_window_days = 135;</code>
     * @param int|string $var
     * @return $this
     */
    public function setHotelBookingWindowDays($var)
    {
        GPBUtil::checkInt64($var);
        $this->hotel_booking_window_days = $var;

        return $this;
    }

    /**
     * Hotel center ID.
     *
     * Generated from protobuf field <code>optional int64 hotel_center_id = 80;</code>
     * @return int|string
     */
    public function getHotelCenterId()
    {
        return isset($this->hotel_center_id) ? $this->hotel_center_id : 0;
    }

    public function hasHotelCenterId()
    {
        return isset($this->hotel_center_id);
    }

    public function clearHotelCenterId()
    {
        unset($this->hotel_center_id);
    }

    /**
     * Hotel center ID.
     *
     * Generated from protobuf field <code>optional int64 hotel_center_id = 80;</code>
     * @param int|string $var
     * @return $this
     */
    public function setHotelCenterId($var)
    {
        GPBUtil::checkInt64($var);
        $this->hotel_center_id = $var;

        return $this;
    }

    /**
     * Hotel check-in date. Formatted as yyyy-MM-dd.
     *
     * Generated from protobuf field <code>optional string hotel_check_in_date = 81;</code>
     * @return string
     */
    public function getHotelCheckInDate()
    {
        return isset($this->hotel_check_in_date) ? $this->hotel_check_in_date : '';
    }

    public function hasHotelCheckInDate()
    {
        return isset($this->hotel_check_in_date);
    }

    public function clearHotelCheckInDate()
    {
        unset($this->hotel_check_in_date);
    }

    /**
     * Hotel check-in date. Formatted as yyyy-MM-dd.
     *
     * Generated from protobuf field <code>optional string hotel_check_in_date = 81;</code>
     * @param string $var
     * @return $this
     */
    public function setHotelCheckInDate($var)
    {
        GPBUtil::checkString($var, True);
        $this->hotel_check_in_date = $var;

        return $this;
    }

    /**
     * Hotel check-in day of week.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.DayOfWeekEnum.DayOfWeek hotel_check_in_day_of_week = 9;</code>
     * @return int
     */
    public function getHotelCheckInDayOfWeek()
    {
        return $this->hotel_check_in_day_of_week;
    }

    /**
     * Hotel check-in day of week.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.DayOfWeekEnum.DayOfWeek hotel_check_in_day_of_week = 9;</code>
     * @param int $var
     * @return $this
     */
    public function setHotelCheckInDayOfWeek($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V16\Enums\DayOfWeekEnum\DayOfWeek::class);
        $this->hotel_check_in_day_of_week = $var;

        return $this;
    }

    /**
     * Hotel city.
     *
     * Generated from protobuf field <code>optional string hotel_city = 82;</code>
     * @return string
     */
    public function getHotelCity()
    {
        return isset($this->hotel_city) ? $this->hotel_city : '';
    }

    public function hasHotelCity()
    {
        return isset($this->hotel_city);
    }

    public function clearHotelCity()
    {
        unset($this->hotel_city);
    }

    /**
     * Hotel city.
     *
     * Generated from protobuf field <code>optional string hotel_city = 82;</code>
     * @param string $var
     * @return $this
     */
    public function setHotelCity($var)
    {
        GPBUtil::checkString($var, True);
        $this->hotel_city = $var;

        return $this;
    }

    /**
     * Hotel class.
     *
     * Generated from protobuf field <code>optional int32 hotel_class = 83;</code>
     * @return int
     */
    public function getHotelClass()
    {
        return isset($this->hotel_class) ? $this->hotel_class : 0;
    }

    public function hasHotelClass()
    {
        return isset($this->hotel_class);
    }

    public function clearHotelClass()
    {
        unset($this->hotel_class);
    }

    /**
     * Hotel class.
     *
     * Generated from protobuf field <code>optional int32 hotel_class = 83;</code>
     * @param int $var
     * @return $this
     */
    public function setHotelClass($var)
    {
        GPBUtil::checkInt32($var);
        $this->hotel_class = $var;

        return $this;
    }

    /**
     * Hotel country.
     *
     * Generated from protobuf field <code>optional string hotel_country = 84;</code>
     * @return string
     */
    public function getHotelCountry()
    {
        return isset($this->hotel_country) ? $this->hotel_country : '';
    }

    public function hasHotelCountry()
    {
        return isset($this->hotel_country);
    }

    public function clearHotelCountry()
    {
        unset($this->hotel_country);
    }

    /**
     * Hotel country.
     *
     * Generated from protobuf field <code>optional string hotel_country = 84;</code>
     * @param string $var
     * @return $this
     */
    public function setHotelCountry($var)
    {
        GPBUtil::checkString($var, True);
        $this->hotel_country = $var;

        return $this;
    }

    /**
     * Hotel date selection type.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.HotelDateSelectionTypeEnum.HotelDateSelectionType hotel_date_selection_type = 13;</code>
     * @return int
     */
    public function getHotelDateSelectionType()
    {
        return $this->hotel_date_selection_type;
    }

    /**
     * Hotel date selection type.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.HotelDateSelectionTypeEnum.HotelDateSelectionType hotel_date_selection_type = 13;</code>
     * @param int $var
     * @return $this
     */
    public function setHotelDateSelectionType($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V16\Enums\HotelDateSelectionTypeEnum\HotelDateSelectionType::class);
        $this->hotel_date_selection_type = $var;

        return $this;
    }

    /**
     * Hotel length of stay.
     *
     * Generated from protobuf field <code>optional int32 hotel_length_of_stay = 85;</code>
     * @return int
     */
    public function getHotelLengthOfStay()
    {
        return isset($this->hotel_length_of_stay) ? $this->hotel_length_of_stay : 0;
    }

    public function hasHotelLengthOfStay()
    {
        return isset($this->hotel_length_of_stay);
    }

    public function clearHotelLengthOfStay()
    {
        unset($this->hotel_length_of_stay);
    }

    /**
     * Hotel length of stay.
     *
     * Generated from protobuf field <code>optional int32 hotel_length_of_stay = 85;</code>
     * @param int $var
     * @return $this
     */
    public function setHotelLengthOfStay($var)
    {
        GPBUtil::checkInt32($var);
        $this->hotel_length_of_stay = $var;

        return $this;
    }

    /**
     * Hotel rate rule ID.
     *
     * Generated from protobuf field <code>optional string hotel_rate_rule_id = 86;</code>
     * @return string
     */
    public function getHotelRateRuleId()
    {
        return isset($this->hotel_rate_rule_id) ? $this->hotel_rate_rule_id : '';
    }

    public function hasHotelRateRuleId()
    {
        return isset($this->hotel_rate_rule_id);
    }

    public function clearHotelRateRuleId()
    {
        unset($this->hotel_rate_rule_id);
    }

    /**
     * Hotel rate rule ID.
     *
     * Generated from protobuf field <code>optional string hotel_rate_rule_id = 86;</code>
     * @param string $var
     * @return $this
     */
    public function setHotelRateRuleId($var)
    {
        GPBUtil::checkString($var, True);
        $this->hotel_rate_rule_id = $var;

        return $this;
    }

    /**
     * Hotel rate type.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.HotelRateTypeEnum.HotelRateType hotel_rate_type = 74;</code>
     * @return int
     */
    public function getHotelRateType()
    {
        return $this->hotel_rate_type;
    }

    /**
     * Hotel rate type.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.HotelRateTypeEnum.HotelRateType hotel_rate_type = 74;</code>
     * @param int $var
     * @return $this
     */
    public function setHotelRateType($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V16\Enums\HotelRateTypeEnum\HotelRateType::class);
        $this->hotel_rate_type = $var;

        return $this;
    }

    /**
     * Hotel price bucket.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.HotelPriceBucketEnum.HotelPriceBucket hotel_price_bucket = 78;</code>
     * @return int
     */
    public function getHotelPriceBucket()
    {
        return $this->hotel_price_bucket;
    }

    /**
     * Hotel price bucket.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.HotelPriceBucketEnum.HotelPriceBucket hotel_price_bucket = 78;</code>
     * @param int $var
     * @return $this
     */
    public function setHotelPriceBucket($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V16\Enums\HotelPriceBucketEnum\HotelPriceBucket::class);
        $this->hotel_price_bucket = $var;

        return $this;
    }

    /**
     * Hotel state.
     *
     * Generated from protobuf field <code>optional string hotel_state = 87;</code>
     * @return string
     */
    public function getHotelState()
    {
        return isset($this->hotel_state) ? $this->hotel_state : '';
    }

    public function hasHotelState()
    {
        return isset($this->hotel_state);
    }

    public function clearHotelState()
    {
        unset($this->hotel_state);
    }

    /**
     * Hotel state.
     *
     * Generated from protobuf field <code>optional string hotel_state = 87;</code>
     * @param string $var
     * @return $this
     */
    public function setHotelState($var)
    {
        GPBUtil::checkString($var, True);
        $this->hotel_state = $var;

        return $this;
    }

    /**
     * Hour of day as a number between 0 and 23, inclusive.
     *
     * Generated from protobuf field <code>optional int32 hour = 88;</code>
     * @return int
     */
    public function getHour()
    {
        return isset($this->hour) ? $this->hour : 0;
    }

    public function hasHour()
    {
        return isset($this->hour);
    }

    public function clearHour()
    {
        unset($this->hour);
    }

    /**
     * Hour of day as a number between 0 and 23, inclusive.
     *
     * Generated from protobuf field <code>optional int32 hour = 88;</code>
     * @param int $var
     * @return $this
     */
    public function setHour($var)
    {
        GPBUtil::checkInt32($var);
        $this->hour = $var;

        return $this;
    }

    /**
     * Only used with feed item metrics.
     * Indicates whether the interaction metrics occurred on the feed item itself
     * or a different extension or ad unit.
     *
     * Generated from protobuf field <code>optional bool interaction_on_this_extension = 89;</code>
     * @return bool
     */
    public function getInteractionOnThisExtension()
    {
        return isset($this->interaction_on_this_extension) ? $this->interaction_on_this_extension : false;
    }

    public function hasInteractionOnThisExtension()
    {
        return isset($this->interaction_on_this_extension);
    }

    public function clearInteractionOnThisExtension()
    {
        unset($this->interaction_on_this_extension);
    }

    /**
     * Only used with feed item metrics.
     * Indicates whether the interaction metrics occurred on the feed item itself
     * or a different extension or ad unit.
     *
     * Generated from protobuf field <code>optional bool interaction_on_this_extension = 89;</code>
     * @param bool $var
     * @return $this
     */
    public function setInteractionOnThisExtension($var)
    {
        GPBUtil::checkBool($var);
        $this->interaction_on_this_extension = $var;

        return $this;
    }

    /**
     * Keyword criterion.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.common.Keyword keyword = 61;</code>
     * @return \Google\Ads\GoogleAds\V16\Common\Keyword|null
     */
    public function getKeyword()
    {
        return $this->keyword;
    }

    public function hasKeyword()
    {
        return isset($this->keyword);
    }

    public function clearKeyword()
    {
        unset($this->keyword);
    }

    /**
     * Keyword criterion.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.common.Keyword keyword = 61;</code>
     * @param \Google\Ads\GoogleAds\V16\Common\Keyword $var
     * @return $this
     */
    public function setKeyword($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V16\Common\Keyword::class);
        $this->keyword = $var;

        return $this;
    }

    /**
     * Month as represented by the date of the first day of a month. Formatted as
     * yyyy-MM-dd.
     *
     * Generated from protobuf field <code>optional string month = 90;</code>
     * @return string
     */
    public function getMonth()
    {
        return isset($this->month) ? $this->month : '';
    }

    public function hasMonth()
    {
        return isset($this->month);
    }

    public function clearMonth()
    {
        unset($this->month);
    }

    /**
     * Month as represented by the date of the first day of a month. Formatted as
     * yyyy-MM-dd.
     *
     * Generated from protobuf field <code>optional string month = 90;</code>
     * @param string $var
     * @return $this
     */
    public function setMonth($var)
    {
        GPBUtil::checkString($var, True);
        $this->month = $var;

        return $this;
    }

    /**
     * Month of the year, for example, January.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.MonthOfYearEnum.MonthOfYear month_of_year = 18;</code>
     * @return int
     */
    public function getMonthOfYear()
    {
        return $this->month_of_year;
    }

    /**
     * Month of the year, for example, January.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.MonthOfYearEnum.MonthOfYear month_of_year = 18;</code>
     * @param int $var
     * @return $this
     */
    public function setMonthOfYear($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V16\Enums\MonthOfYearEnum\MonthOfYear::class);
        $this->month_of_year = $var;

        return $this;
    }

    /**
     * Partner hotel ID.
     *
     * Generated from protobuf field <code>optional string partner_hotel_id = 91;</code>
     * @return string
     */
    public function getPartnerHotelId()
    {
        return isset($this->partner_hotel_id) ? $this->partner_hotel_id : '';
    }

    public function hasPartnerHotelId()
    {
        return isset($this->partner_hotel_id);
    }

    public function clearPartnerHotelId()
    {
        unset($this->partner_hotel_id);
    }

    /**
     * Partner hotel ID.
     *
     * Generated from protobuf field <code>optional string partner_hotel_id = 91;</code>
     * @param string $var
     * @return $this
     */
    public function setPartnerHotelId($var)
    {
        GPBUtil::checkString($var, True);
        $this->partner_hotel_id = $var;

        return $this;
    }

    /**
     * Placeholder type. This is only used with feed item metrics.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.PlaceholderTypeEnum.PlaceholderType placeholder_type = 20;</code>
     * @return int
     */
    public function getPlaceholderType()
    {
        return $this->placeholder_type;
    }

    /**
     * Placeholder type. This is only used with feed item metrics.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.PlaceholderTypeEnum.PlaceholderType placeholder_type = 20;</code>
     * @param int $var
     * @return $this
     */
    public function setPlaceholderType($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V16\Enums\PlaceholderTypeEnum\PlaceholderType::class);
        $this->placeholder_type = $var;

        return $this;
    }

    /**
     * Aggregator ID of the product.
     *
     * Generated from protobuf field <code>optional int64 product_aggregator_id = 132;</code>
     * @return int|string
     */
    public function getProductAggregatorId()
    {
        return isset($this->product_aggregator_id) ? $this->product_aggregator_id : 0;
    }

    public function hasProductAggregatorId()
    {
        return isset($this->product_aggregator_id);
    }

    public function clearProductAggregatorId()
    {
        unset($this->product_aggregator_id);
    }

    /**
     * Aggregator ID of the product.
     *
     * Generated from protobuf field <code>optional int64 product_aggregator_id = 132;</code>
     * @param int|string $var
     * @return $this
     */
    public function setProductAggregatorId($var)
    {
        GPBUtil::checkInt64($var);
        $this->product_aggregator_id = $var;

        return $this;
    }

    /**
     * Category (level 1) of the product.
     *
     * Generated from protobuf field <code>optional string product_category_level1 = 161;</code>
     * @return string
     */
    public function getProductCategoryLevel1()
    {
        return isset($this->product_category_level1) ? $this->product_category_level1 : '';
    }

    public function hasProductCategoryLevel1()
    {
        return isset($this->product_category_level1);
    }

    public function clearProductCategoryLevel1()
    {
        unset($this->product_category_level1);
    }

    /**
     * Category (level 1) of the product.
     *
     * Generated from protobuf field <code>optional string product_category_level1 = 161;</code>
     * @param string $var
     * @return $this
     */
    public function setProductCategoryLevel1($var)
    {
        GPBUtil::checkString($var, True);
        $this->product_category_level1 = $var;

        return $this;
    }

    /**
     * Category (level 2) of the product.
     *
     * Generated from protobuf field <code>optional string product_category_level2 = 162;</code>
     * @return string
     */
    public function getProductCategoryLevel2()
    {
        return isset($this->product_category_level2) ? $this->product_category_level2 : '';
    }

    public function hasProductCategoryLevel2()
    {
        return isset($this->product_category_level2);
    }

    public function clearProductCategoryLevel2()
    {
        unset($this->product_category_level2);
    }

    /**
     * Category (level 2) of the product.
     *
     * Generated from protobuf field <code>optional string product_category_level2 = 162;</code>
     * @param string $var
     * @return $this
     */
    public function setProductCategoryLevel2($var)
    {
        GPBUtil::checkString($var, True);
        $this->product_category_level2 = $var;

        return $this;
    }

    /**
     * Category (level 3) of the product.
     *
     * Generated from protobuf field <code>optional string product_category_level3 = 163;</code>
     * @return string
     */
    public function getProductCategoryLevel3()
    {
        return isset($this->product_category_level3) ? $this->product_category_level3 : '';
    }

    public function hasProductCategoryLevel3()
    {
        return isset($this->product_category_level3);
    }

    public function clearProductCategoryLevel3()
    {
        unset($this->product_category_level3);
    }

    /**
     * Category (level 3) of the product.
     *
     * Generated from protobuf field <code>optional string product_category_level3 = 163;</code>
     * @param string $var
     * @return $this
     */
    public function setProductCategoryLevel3($var)
    {
        GPBUtil::checkString($var, True);
        $this->product_category_level3 = $var;

        return $this;
    }

    /**
     * Category (level 4) of the product.
     *
     * Generated from protobuf field <code>optional string product_category_level4 = 164;</code>
     * @return string
     */
    public function getProductCategoryLevel4()
    {
        return isset($this->product_category_level4) ? $this->product_category_level4 : '';
    }

    public function hasProductCategoryLevel4()
    {
        return isset($this->product_category_level4);
    }

    public function clearProductCategoryLevel4()
    {
        unset($this->product_category_level4);
    }

    /**
     * Category (level 4) of the product.
     *
     * Generated from protobuf field <code>optional string product_category_level4 = 164;</code>
     * @param string $var
     * @return $this
     */
    public function setProductCategoryLevel4($var)
    {
        GPBUtil::checkString($var, True);
        $this->product_category_level4 = $var;

        return $this;
    }

    /**
     * Category (level 5) of the product.
     *
     * Generated from protobuf field <code>optional string product_category_level5 = 165;</code>
     * @return string
     */
    public function getProductCategoryLevel5()
    {
        return isset($this->product_category_level5) ? $this->product_category_level5 : '';
    }

    public function hasProductCategoryLevel5()
    {
        return isset($this->product_category_level5);
    }

    public function clearProductCategoryLevel5()
    {
        unset($this->product_category_level5);
    }

    /**
     * Category (level 5) of the product.
     *
     * Generated from protobuf field <code>optional string product_category_level5 = 165;</code>
     * @param string $var
     * @return $this
     */
    public function setProductCategoryLevel5($var)
    {
        GPBUtil::checkString($var, True);
        $this->product_category_level5 = $var;

        return $this;
    }

    /**
     * Brand of the product.
     *
     * Generated from protobuf field <code>optional string product_brand = 97;</code>
     * @return string
     */
    public function getProductBrand()
    {
        return isset($this->product_brand) ? $this->product_brand : '';
    }

    public function hasProductBrand()
    {
        return isset($this->product_brand);
    }

    public function clearProductBrand()
    {
        unset($this->product_brand);
    }

    /**
     * Brand of the product.
     *
     * Generated from protobuf field <code>optional string product_brand = 97;</code>
     * @param string $var
     * @return $this
     */
    public function setProductBrand($var)
    {
        GPBUtil::checkString($var, True);
        $this->product_brand = $var;

        return $this;
    }

    /**
     * Channel of the product.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.ProductChannelEnum.ProductChannel product_channel = 30;</code>
     * @return int
     */
    public function getProductChannel()
    {
        return $this->product_channel;
    }

    /**
     * Channel of the product.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.ProductChannelEnum.ProductChannel product_channel = 30;</code>
     * @param int $var
     * @return $this
     */
    public function setProductChannel($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V16\Enums\ProductChannelEnum\ProductChannel::class);
        $this->product_channel = $var;

        return $this;
    }

    /**
     * Channel exclusivity of the product.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.ProductChannelExclusivityEnum.ProductChannelExclusivity product_channel_exclusivity = 31;</code>
     * @return int
     */
    public function getProductChannelExclusivity()
    {
        return $this->product_channel_exclusivity;
    }

    /**
     * Channel exclusivity of the product.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.ProductChannelExclusivityEnum.ProductChannelExclusivity product_channel_exclusivity = 31;</code>
     * @param int $var
     * @return $this
     */
    public function setProductChannelExclusivity($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V16\Enums\ProductChannelExclusivityEnum\ProductChannelExclusivity::class);
        $this->product_channel_exclusivity = $var;

        return $this;
    }

    /**
     * Condition of the product.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.ProductConditionEnum.ProductCondition product_condition = 32;</code>
     * @return int
     */
    public function getProductCondition()
    {
        return $this->product_condition;
    }

    /**
     * Condition of the product.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.ProductConditionEnum.ProductCondition product_condition = 32;</code>
     * @param int $var
     * @return $this
     */
    public function setProductCondition($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V16\Enums\ProductConditionEnum\ProductCondition::class);
        $this->product_condition = $var;

        return $this;
    }

    /**
     * Resource name of the geo target constant for the country of sale of the
     * product.
     *
     * Generated from protobuf field <code>optional string product_country = 98;</code>
     * @return string
     */
    public function getProductCountry()
    {
        return isset($this->product_country) ? $this->product_country : '';
    }

    public function hasProductCountry()
    {
        return isset($this->product_country);
    }

    public function clearProductCountry()
    {
        unset($this->product_country);
    }

    /**
     * Resource name of the geo target constant for the country of sale of the
     * product.
     *
     * Generated from protobuf field <code>optional string product_country = 98;</code>
     * @param string $var
     * @return $this
     */
    public function setProductCountry($var)
    {
        GPBUtil::checkString($var, True);
        $this->product_country = $var;

        return $this;
    }

    /**
     * Custom attribute 0 of the product.
     *
     * Generated from protobuf field <code>optional string product_custom_attribute0 = 99;</code>
     * @return string
     */
    public function getProductCustomAttribute0()
    {
        return isset($this->product_custom_attribute0) ? $this->product_custom_attribute0 : '';
    }

    public function hasProductCustomAttribute0()
    {
        return isset($this->product_custom_attribute0);
    }

    public function clearProductCustomAttribute0()
    {
        unset($this->product_custom_attribute0);
    }

    /**
     * Custom attribute 0 of the product.
     *
     * Generated from protobuf field <code>optional string product_custom_attribute0 = 99;</code>
     * @param string $var
     * @return $this
     */
    public function setProductCustomAttribute0($var)
    {
        GPBUtil::checkString($var, True);
        $this->product_custom_attribute0 = $var;

        return $this;
    }

    /**
     * Custom attribute 1 of the product.
     *
     * Generated from protobuf field <code>optional string product_custom_attribute1 = 100;</code>
     * @return string
     */
    public function getProductCustomAttribute1()
    {
        return isset($this->product_custom_attribute1) ? $this->product_custom_attribute1 : '';
    }

    public function hasProductCustomAttribute1()
    {
        return isset($this->product_custom_attribute1);
    }

    public function clearProductCustomAttribute1()
    {
        unset($this->product_custom_attribute1);
    }

    /**
     * Custom attribute 1 of the product.
     *
     * Generated from protobuf field <code>optional string product_custom_attribute1 = 100;</code>
     * @param string $var
     * @return $this
     */
    public function setProductCustomAttribute1($var)
    {
        GPBUtil::checkString($var, True);
        $this->product_custom_attribute1 = $var;

        return $this;
    }

    /**
     * Custom attribute 2 of the product.
     *
     * Generated from protobuf field <code>optional string product_custom_attribute2 = 101;</code>
     * @return string
     */
    public function getProductCustomAttribute2()
    {
        return isset($this->product_custom_attribute2) ? $this->product_custom_attribute2 : '';
    }

    public function hasProductCustomAttribute2()
    {
        return isset($this->product_custom_attribute2);
    }

    public function clearProductCustomAttribute2()
    {
        unset($this->product_custom_attribute2);
    }

    /**
     * Custom attribute 2 of the product.
     *
     * Generated from protobuf field <code>optional string product_custom_attribute2 = 101;</code>
     * @param string $var
     * @return $this
     */
    public function setProductCustomAttribute2($var)
    {
        GPBUtil::checkString($var, True);
        $this->product_custom_attribute2 = $var;

        return $this;
    }

    /**
     * Custom attribute 3 of the product.
     *
     * Generated from protobuf field <code>optional string product_custom_attribute3 = 102;</code>
     * @return string
     */
    public function getProductCustomAttribute3()
    {
        return isset($this->product_custom_attribute3) ? $this->product_custom_attribute3 : '';
    }

    public function hasProductCustomAttribute3()
    {
        return isset($this->product_custom_attribute3);
    }

    public function clearProductCustomAttribute3()
    {
        unset($this->product_custom_attribute3);
    }

    /**
     * Custom attribute 3 of the product.
     *
     * Generated from protobuf field <code>optional string product_custom_attribute3 = 102;</code>
     * @param string $var
     * @return $this
     */
    public function setProductCustomAttribute3($var)
    {
        GPBUtil::checkString($var, True);
        $this->product_custom_attribute3 = $var;

        return $this;
    }

    /**
     * Custom attribute 4 of the product.
     *
     * Generated from protobuf field <code>optional string product_custom_attribute4 = 103;</code>
     * @return string
     */
    public function getProductCustomAttribute4()
    {
        return isset($this->product_custom_attribute4) ? $this->product_custom_attribute4 : '';
    }

    public function hasProductCustomAttribute4()
    {
        return isset($this->product_custom_attribute4);
    }

    public function clearProductCustomAttribute4()
    {
        unset($this->product_custom_attribute4);
    }

    /**
     * Custom attribute 4 of the product.
     *
     * Generated from protobuf field <code>optional string product_custom_attribute4 = 103;</code>
     * @param string $var
     * @return $this
     */
    public function setProductCustomAttribute4($var)
    {
        GPBUtil::checkString($var, True);
        $this->product_custom_attribute4 = $var;

        return $this;
    }

    /**
     * Feed label of the product.
     *
     * Generated from protobuf field <code>optional string product_feed_label = 147;</code>
     * @return string
     */
    public function getProductFeedLabel()
    {
        return isset($this->product_feed_label) ? $this->product_feed_label : '';
    }

    public function hasProductFeedLabel()
    {
        return isset($this->product_feed_label);
    }

    public function clearProductFeedLabel()
    {
        unset($this->product_feed_label);
    }

    /**
     * Feed label of the product.
     *
     * Generated from protobuf field <code>optional string product_feed_label = 147;</code>
     * @param string $var
     * @return $this
     */
    public function setProductFeedLabel($var)
    {
        GPBUtil::checkString($var, True);
        $this->product_feed_label = $var;

        return $this;
    }

    /**
     * Item ID of the product.
     *
     * Generated from protobuf field <code>optional string product_item_id = 104;</code>
     * @return string
     */
    public function getProductItemId()
    {
        return isset($this->product_item_id) ? $this->product_item_id : '';
    }

    public function hasProductItemId()
    {
        return isset($this->product_item_id);
    }

    public function clearProductItemId()
    {
        unset($this->product_item_id);
    }

    /**
     * Item ID of the product.
     *
     * Generated from protobuf field <code>optional string product_item_id = 104;</code>
     * @param string $var
     * @return $this
     */
    public function setProductItemId($var)
    {
        GPBUtil::checkString($var, True);
        $this->product_item_id = $var;

        return $this;
    }

    /**
     * Resource name of the language constant for the language of the product.
     *
     * Generated from protobuf field <code>optional string product_language = 105;</code>
     * @return string
     */
    public function getProductLanguage()
    {
        return isset($this->product_language) ? $this->product_language : '';
    }

    public function hasProductLanguage()
    {
        return isset($this->product_language);
    }

    public function clearProductLanguage()
    {
        unset($this->product_language);
    }

    /**
     * Resource name of the language constant for the language of the product.
     *
     * Generated from protobuf field <code>optional string product_language = 105;</code>
     * @param string $var
     * @return $this
     */
    public function setProductLanguage($var)
    {
        GPBUtil::checkString($var, True);
        $this->product_language = $var;

        return $this;
    }

    /**
     * Merchant ID of the product.
     *
     * Generated from protobuf field <code>optional int64 product_merchant_id = 133;</code>
     * @return int|string
     */
    public function getProductMerchantId()
    {
        return isset($this->product_merchant_id) ? $this->product_merchant_id : 0;
    }

    public function hasProductMerchantId()
    {
        return isset($this->product_merchant_id);
    }

    public function clearProductMerchantId()
    {
        unset($this->product_merchant_id);
    }

    /**
     * Merchant ID of the product.
     *
     * Generated from protobuf field <code>optional int64 product_merchant_id = 133;</code>
     * @param int|string $var
     * @return $this
     */
    public function setProductMerchantId($var)
    {
        GPBUtil::checkInt64($var);
        $this->product_merchant_id = $var;

        return $this;
    }

    /**
     * Store ID of the product.
     *
     * Generated from protobuf field <code>optional string product_store_id = 106;</code>
     * @return string
     */
    public function getProductStoreId()
    {
        return isset($this->product_store_id) ? $this->product_store_id : '';
    }

    public function hasProductStoreId()
    {
        return isset($this->product_store_id);
    }

    public function clearProductStoreId()
    {
        unset($this->product_store_id);
    }

    /**
     * Store ID of the product.
     *
     * Generated from protobuf field <code>optional string product_store_id = 106;</code>
     * @param string $var
     * @return $this
     */
    public function setProductStoreId($var)
    {
        GPBUtil::checkString($var, True);
        $this->product_store_id = $var;

        return $this;
    }

    /**
     * Title of the product.
     *
     * Generated from protobuf field <code>optional string product_title = 107;</code>
     * @return string
     */
    public function getProductTitle()
    {
        return isset($this->product_title) ? $this->product_title : '';
    }

    public function hasProductTitle()
    {
        return isset($this->product_title);
    }

    public function clearProductTitle()
    {
        unset($this->product_title);
    }

    /**
     * Title of the product.
     *
     * Generated from protobuf field <code>optional string product_title = 107;</code>
     * @param string $var
     * @return $this
     */
    public function setProductTitle($var)
    {
        GPBUtil::checkString($var, True);
        $this->product_title = $var;

        return $this;
    }

    /**
     * Type (level 1) of the product.
     *
     * Generated from protobuf field <code>optional string product_type_l1 = 108;</code>
     * @return string
     */
    public function getProductTypeL1()
    {
        return isset($this->product_type_l1) ? $this->product_type_l1 : '';
    }

    public function hasProductTypeL1()
    {
        return isset($this->product_type_l1);
    }

    public function clearProductTypeL1()
    {
        unset($this->product_type_l1);
    }

    /**
     * Type (level 1) of the product.
     *
     * Generated from protobuf field <code>optional string product_type_l1 = 108;</code>
     * @param string $var
     * @return $this
     */
    public function setProductTypeL1($var)
    {
        GPBUtil::checkString($var, True);
        $this->product_type_l1 = $var;

        return $this;
    }

    /**
     * Type (level 2) of the product.
     *
     * Generated from protobuf field <code>optional string product_type_l2 = 109;</code>
     * @return string
     */
    public function getProductTypeL2()
    {
        return isset($this->product_type_l2) ? $this->product_type_l2 : '';
    }

    public function hasProductTypeL2()
    {
        return isset($this->product_type_l2);
    }

    public function clearProductTypeL2()
    {
        unset($this->product_type_l2);
    }

    /**
     * Type (level 2) of the product.
     *
     * Generated from protobuf field <code>optional string product_type_l2 = 109;</code>
     * @param string $var
     * @return $this
     */
    public function setProductTypeL2($var)
    {
        GPBUtil::checkString($var, True);
        $this->product_type_l2 = $var;

        return $this;
    }

    /**
     * Type (level 3) of the product.
     *
     * Generated from protobuf field <code>optional string product_type_l3 = 110;</code>
     * @return string
     */
    public function getProductTypeL3()
    {
        return isset($this->product_type_l3) ? $this->product_type_l3 : '';
    }

    public function hasProductTypeL3()
    {
        return isset($this->product_type_l3);
    }

    public function clearProductTypeL3()
    {
        unset($this->product_type_l3);
    }

    /**
     * Type (level 3) of the product.
     *
     * Generated from protobuf field <code>optional string product_type_l3 = 110;</code>
     * @param string $var
     * @return $this
     */
    public function setProductTypeL3($var)
    {
        GPBUtil::checkString($var, True);
        $this->product_type_l3 = $var;

        return $this;
    }

    /**
     * Type (level 4) of the product.
     *
     * Generated from protobuf field <code>optional string product_type_l4 = 111;</code>
     * @return string
     */
    public function getProductTypeL4()
    {
        return isset($this->product_type_l4) ? $this->product_type_l4 : '';
    }

    public function hasProductTypeL4()
    {
        return isset($this->product_type_l4);
    }

    public function clearProductTypeL4()
    {
        unset($this->product_type_l4);
    }

    /**
     * Type (level 4) of the product.
     *
     * Generated from protobuf field <code>optional string product_type_l4 = 111;</code>
     * @param string $var
     * @return $this
     */
    public function setProductTypeL4($var)
    {
        GPBUtil::checkString($var, True);
        $this->product_type_l4 = $var;

        return $this;
    }

    /**
     * Type (level 5) of the product.
     *
     * Generated from protobuf field <code>optional string product_type_l5 = 112;</code>
     * @return string
     */
    public function getProductTypeL5()
    {
        return isset($this->product_type_l5) ? $this->product_type_l5 : '';
    }

    public function hasProductTypeL5()
    {
        return isset($this->product_type_l5);
    }

    public function clearProductTypeL5()
    {
        unset($this->product_type_l5);
    }

    /**
     * Type (level 5) of the product.
     *
     * Generated from protobuf field <code>optional string product_type_l5 = 112;</code>
     * @param string $var
     * @return $this
     */
    public function setProductTypeL5($var)
    {
        GPBUtil::checkString($var, True);
        $this->product_type_l5 = $var;

        return $this;
    }

    /**
     * Quarter as represented by the date of the first day of a quarter.
     * Uses the calendar year for quarters, for example, the second quarter of
     * 2018 starts on 2018-04-01. Formatted as yyyy-MM-dd.
     *
     * Generated from protobuf field <code>optional string quarter = 128;</code>
     * @return string
     */
    public function getQuarter()
    {
        return isset($this->quarter) ? $this->quarter : '';
    }

    public function hasQuarter()
    {
        return isset($this->quarter);
    }

    public function clearQuarter()
    {
        unset($this->quarter);
    }

    /**
     * Quarter as represented by the date of the first day of a quarter.
     * Uses the calendar year for quarters, for example, the second quarter of
     * 2018 starts on 2018-04-01. Formatted as yyyy-MM-dd.
     *
     * Generated from protobuf field <code>optional string quarter = 128;</code>
     * @param string $var
     * @return $this
     */
    public function setQuarter($var)
    {
        GPBUtil::checkString($var, True);
        $this->quarter = $var;

        return $this;
    }

    /**
     * Recommendation type.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.RecommendationTypeEnum.RecommendationType recommendation_type = 140;</code>
     * @return int
     */
    public function getRecommendationType()
    {
        return $this->recommendation_type;
    }

    /**
     * Recommendation type.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.RecommendationTypeEnum.RecommendationType recommendation_type = 140;</code>
     * @param int $var
     * @return $this
     */
    public function setRecommendationType($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V16\Enums\RecommendationTypeEnum\RecommendationType::class);
        $this->recommendation_type = $var;

        return $this;
    }

    /**
     * Type of the search engine results page.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.SearchEngineResultsPageTypeEnum.SearchEngineResultsPageType search_engine_results_page_type = 70;</code>
     * @return int
     */
    public function getSearchEngineResultsPageType()
    {
        return $this->search_engine_results_page_type;
    }

    /**
     * Type of the search engine results page.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.SearchEngineResultsPageTypeEnum.SearchEngineResultsPageType search_engine_results_page_type = 70;</code>
     * @param int $var
     * @return $this
     */
    public function setSearchEngineResultsPageType($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V16\Enums\SearchEngineResultsPageTypeEnum\SearchEngineResultsPageType::class);
        $this->search_engine_results_page_type = $var;

        return $this;
    }

    /**
     * A search term subcategory. An empty string denotes the catch-all
     * subcategory for search terms that didn't fit into another subcategory.
     *
     * Generated from protobuf field <code>optional string search_subcategory = 155;</code>
     * @return string
     */
    public function getSearchSubcategory()
    {
        return isset($this->search_subcategory) ? $this->search_subcategory : '';
    }

    public function hasSearchSubcategory()
    {
        return isset($this->search_subcategory);
    }

    public function clearSearchSubcategory()
    {
        unset($this->search_subcategory);
    }

    /**
     * A search term subcategory. An empty string denotes the catch-all
     * subcategory for search terms that didn't fit into another subcategory.
     *
     * Generated from protobuf field <code>optional string search_subcategory = 155;</code>
     * @param string $var
     * @return $this
     */
    public function setSearchSubcategory($var)
    {
        GPBUtil::checkString($var, True);
        $this->search_subcategory = $var;

        return $this;
    }

    /**
     * A search term.
     *
     * Generated from protobuf field <code>optional string search_term = 156;</code>
     * @return string
     */
    public function getSearchTerm()
    {
        return isset($this->search_term) ? $this->search_term : '';
    }

    public function hasSearchTerm()
    {
        return isset($this->search_term);
    }

    public function clearSearchTerm()
    {
        unset($this->search_term);
    }

    /**
     * A search term.
     *
     * Generated from protobuf field <code>optional string search_term = 156;</code>
     * @param string $var
     * @return $this
     */
    public function setSearchTerm($var)
    {
        GPBUtil::checkString($var, True);
        $this->search_term = $var;

        return $this;
    }

    /**
     * Match type of the keyword that triggered the ad, including variants.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.SearchTermMatchTypeEnum.SearchTermMatchType search_term_match_type = 22;</code>
     * @return int
     */
    public function getSearchTermMatchType()
    {
        return $this->search_term_match_type;
    }

    /**
     * Match type of the keyword that triggered the ad, including variants.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.SearchTermMatchTypeEnum.SearchTermMatchType search_term_match_type = 22;</code>
     * @param int $var
     * @return $this
     */
    public function setSearchTermMatchType($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V16\Enums\SearchTermMatchTypeEnum\SearchTermMatchType::class);
        $this->search_term_match_type = $var;

        return $this;
    }

    /**
     * Position of the ad.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.SlotEnum.Slot slot = 23;</code>
     * @return int
     */
    public function getSlot()
    {
        return $this->slot;
    }

    /**
     * Position of the ad.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.SlotEnum.Slot slot = 23;</code>
     * @param int $var
     * @return $this
     */
    public function setSlot($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V16\Enums\SlotEnum\Slot::class);
        $this->slot = $var;

        return $this;
    }

    /**
     * Primary dimension of applied conversion value rules.
     * NO_RULE_APPLIED shows the total recorded value of conversions that
     * do not have a value rule applied.
     * ORIGINAL shows the original value of conversions to which a value rule
     * has been applied.
     * GEO_LOCATION, DEVICE, AUDIENCE show the net adjustment after value
     * rules were applied.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.ConversionValueRulePrimaryDimensionEnum.ConversionValueRulePrimaryDimension conversion_value_rule_primary_dimension = 138;</code>
     * @return int
     */
    public function getConversionValueRulePrimaryDimension()
    {
        return $this->conversion_value_rule_primary_dimension;
    }

    /**
     * Primary dimension of applied conversion value rules.
     * NO_RULE_APPLIED shows the total recorded value of conversions that
     * do not have a value rule applied.
     * ORIGINAL shows the original value of conversions to which a value rule
     * has been applied.
     * GEO_LOCATION, DEVICE, AUDIENCE show the net adjustment after value
     * rules were applied.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.ConversionValueRulePrimaryDimensionEnum.ConversionValueRulePrimaryDimension conversion_value_rule_primary_dimension = 138;</code>
     * @param int $var
     * @return $this
     */
    public function setConversionValueRulePrimaryDimension($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V16\Enums\ConversionValueRulePrimaryDimensionEnum\ConversionValueRulePrimaryDimension::class);
        $this->conversion_value_rule_primary_dimension = $var;

        return $this;
    }

    /**
     * Resource name of the ad group criterion that represents webpage criterion.
     *
     * Generated from protobuf field <code>optional string webpage = 129;</code>
     * @return string
     */
    public function getWebpage()
    {
        return isset($this->webpage) ? $this->webpage : '';
    }

    public function hasWebpage()
    {
        return isset($this->webpage);
    }

    public function clearWebpage()
    {
        unset($this->webpage);
    }

    /**
     * Resource name of the ad group criterion that represents webpage criterion.
     *
     * Generated from protobuf field <code>optional string webpage = 129;</code>
     * @param string $var
     * @return $this
     */
    public function setWebpage($var)
    {
        GPBUtil::checkString($var, True);
        $this->webpage = $var;

        return $this;
    }

    /**
     * Week as defined as Monday through Sunday, and represented by the date of
     * Monday. Formatted as yyyy-MM-dd.
     *
     * Generated from protobuf field <code>optional string week = 130;</code>
     * @return string
     */
    public function getWeek()
    {
        return isset($this->week) ? $this->week : '';
    }

    public function hasWeek()
    {
        return isset($this->week);
    }

    public function clearWeek()
    {
        unset($this->week);
    }

    /**
     * Week as defined as Monday through Sunday, and represented by the date of
     * Monday. Formatted as yyyy-MM-dd.
     *
     * Generated from protobuf field <code>optional string week = 130;</code>
     * @param string $var
     * @return $this
     */
    public function setWeek($var)
    {
        GPBUtil::checkString($var, True);
        $this->week = $var;

        return $this;
    }

    /**
     * Year, formatted as yyyy.
     *
     * Generated from protobuf field <code>optional int32 year = 131;</code>
     * @return int
     */
    public function getYear()
    {
        return isset($this->year) ? $this->year : 0;
    }

    public function hasYear()
    {
        return isset($this->year);
    }

    public function clearYear()
    {
        unset($this->year);
    }

    /**
     * Year, formatted as yyyy.
     *
     * Generated from protobuf field <code>optional int32 year = 131;</code>
     * @param int $var
     * @return $this
     */
    public function setYear($var)
    {
        GPBUtil::checkInt32($var);
        $this->year = $var;

        return $this;
    }

    /**
     * iOS Store Kit Ad Network conversion value.
     * Null value means this segment is not applicable, for example, non-iOS
     * campaign.
     *
     * Generated from protobuf field <code>optional int64 sk_ad_network_conversion_value = 137;</code>
     * @return int|string
     */
    public function getSkAdNetworkConversionValue()
    {
        return isset($this->sk_ad_network_conversion_value) ? $this->sk_ad_network_conversion_value : 0;
    }

    public function hasSkAdNetworkConversionValue()
    {
        return isset($this->sk_ad_network_conversion_value);
    }

    public function clearSkAdNetworkConversionValue()
    {
        unset($this->sk_ad_network_conversion_value);
    }

    /**
     * iOS Store Kit Ad Network conversion value.
     * Null value means this segment is not applicable, for example, non-iOS
     * campaign.
     *
     * Generated from protobuf field <code>optional int64 sk_ad_network_conversion_value = 137;</code>
     * @param int|string $var
     * @return $this
     */
    public function setSkAdNetworkConversionValue($var)
    {
        GPBUtil::checkInt64($var);
        $this->sk_ad_network_conversion_value = $var;

        return $this;
    }

    /**
     * iOS Store Kit Ad Network user type.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.SkAdNetworkUserTypeEnum.SkAdNetworkUserType sk_ad_network_user_type = 141;</code>
     * @return int
     */
    public function getSkAdNetworkUserType()
    {
        return $this->sk_ad_network_user_type;
    }

    /**
     * iOS Store Kit Ad Network user type.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.SkAdNetworkUserTypeEnum.SkAdNetworkUserType sk_ad_network_user_type = 141;</code>
     * @param int $var
     * @return $this
     */
    public function setSkAdNetworkUserType($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V16\Enums\SkAdNetworkUserTypeEnum\SkAdNetworkUserType::class);
        $this->sk_ad_network_user_type = $var;

        return $this;
    }

    /**
     * iOS Store Kit Ad Network ad event type.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.SkAdNetworkAdEventTypeEnum.SkAdNetworkAdEventType sk_ad_network_ad_event_type = 142;</code>
     * @return int
     */
    public function getSkAdNetworkAdEventType()
    {
        return $this->sk_ad_network_ad_event_type;
    }

    /**
     * iOS Store Kit Ad Network ad event type.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.SkAdNetworkAdEventTypeEnum.SkAdNetworkAdEventType sk_ad_network_ad_event_type = 142;</code>
     * @param int $var
     * @return $this
     */
    public function setSkAdNetworkAdEventType($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V16\Enums\SkAdNetworkAdEventTypeEnum\SkAdNetworkAdEventType::class);
        $this->sk_ad_network_ad_event_type = $var;

        return $this;
    }

    /**
     * App where the ad that drove the iOS Store Kit Ad Network install was
     * shown. Null value means this segment is not applicable, for example,
     * non-iOS campaign, or was not present in any postbacks sent by Apple.
     *
     * Generated from protobuf field <code>optional .google.ads.googleads.v16.common.SkAdNetworkSourceApp sk_ad_network_source_app = 143;</code>
     * @return \Google\Ads\GoogleAds\V16\Common\SkAdNetworkSourceApp|null
     */
    public function getSkAdNetworkSourceApp()
    {
        return $this->sk_ad_network_source_app;
    }

    public function hasSkAdNetworkSourceApp()
    {
        return isset($this->sk_ad_network_source_app);
    }

    public function clearSkAdNetworkSourceApp()
    {
        unset($this->sk_ad_network_source_app);
    }

    /**
     * App where the ad that drove the iOS Store Kit Ad Network install was
     * shown. Null value means this segment is not applicable, for example,
     * non-iOS campaign, or was not present in any postbacks sent by Apple.
     *
     * Generated from protobuf field <code>optional .google.ads.googleads.v16.common.SkAdNetworkSourceApp sk_ad_network_source_app = 143;</code>
     * @param \Google\Ads\GoogleAds\V16\Common\SkAdNetworkSourceApp $var
     * @return $this
     */
    public function setSkAdNetworkSourceApp($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V16\Common\SkAdNetworkSourceApp::class);
        $this->sk_ad_network_source_app = $var;

        return $this;
    }

    /**
     * iOS Store Kit Ad Network attribution credit
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.SkAdNetworkAttributionCreditEnum.SkAdNetworkAttributionCredit sk_ad_network_attribution_credit = 144;</code>
     * @return int
     */
    public function getSkAdNetworkAttributionCredit()
    {
        return $this->sk_ad_network_attribution_credit;
    }

    /**
     * iOS Store Kit Ad Network attribution credit
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.SkAdNetworkAttributionCreditEnum.SkAdNetworkAttributionCredit sk_ad_network_attribution_credit = 144;</code>
     * @param int $var
     * @return $this
     */
    public function setSkAdNetworkAttributionCredit($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V16\Enums\SkAdNetworkAttributionCreditEnum\SkAdNetworkAttributionCredit::class);
        $this->sk_ad_network_attribution_credit = $var;

        return $this;
    }

    /**
     * iOS Store Kit Ad Network coarse conversion value.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.SkAdNetworkCoarseConversionValueEnum.SkAdNetworkCoarseConversionValue sk_ad_network_coarse_conversion_value = 151;</code>
     * @return int
     */
    public function getSkAdNetworkCoarseConversionValue()
    {
        return $this->sk_ad_network_coarse_conversion_value;
    }

    /**
     * iOS Store Kit Ad Network coarse conversion value.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.SkAdNetworkCoarseConversionValueEnum.SkAdNetworkCoarseConversionValue sk_ad_network_coarse_conversion_value = 151;</code>
     * @param int $var
     * @return $this
     */
    public function setSkAdNetworkCoarseConversionValue($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V16\Enums\SkAdNetworkCoarseConversionValueEnum\SkAdNetworkCoarseConversionValue::class);
        $this->sk_ad_network_coarse_conversion_value = $var;

        return $this;
    }

    /**
     * Website where the ad that drove the iOS Store Kit Ad Network install was
     * shown. Null value means this segment is not applicable, for example,
     * non-iOS campaign, or was not present in any postbacks sent by Apple.
     *
     * Generated from protobuf field <code>optional string sk_ad_network_source_domain = 152;</code>
     * @return string
     */
    public function getSkAdNetworkSourceDomain()
    {
        return isset($this->sk_ad_network_source_domain) ? $this->sk_ad_network_source_domain : '';
    }

    public function hasSkAdNetworkSourceDomain()
    {
        return isset($this->sk_ad_network_source_domain);
    }

    public function clearSkAdNetworkSourceDomain()
    {
        unset($this->sk_ad_network_source_domain);
    }

    /**
     * Website where the ad that drove the iOS Store Kit Ad Network install was
     * shown. Null value means this segment is not applicable, for example,
     * non-iOS campaign, or was not present in any postbacks sent by Apple.
     *
     * Generated from protobuf field <code>optional string sk_ad_network_source_domain = 152;</code>
     * @param string $var
     * @return $this
     */
    public function setSkAdNetworkSourceDomain($var)
    {
        GPBUtil::checkString($var, True);
        $this->sk_ad_network_source_domain = $var;

        return $this;
    }

    /**
     * The source type where the ad that drove the iOS Store Kit Ad Network
     * install was shown. Null value means this segment is not applicable, for
     * example, non-iOS campaign, or neither source domain nor source app were
     * present in any postbacks sent by Apple.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.SkAdNetworkSourceTypeEnum.SkAdNetworkSourceType sk_ad_network_source_type = 153;</code>
     * @return int
     */
    public function getSkAdNetworkSourceType()
    {
        return $this->sk_ad_network_source_type;
    }

    /**
     * The source type where the ad that drove the iOS Store Kit Ad Network
     * install was shown. Null value means this segment is not applicable, for
     * example, non-iOS campaign, or neither source domain nor source app were
     * present in any postbacks sent by Apple.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.SkAdNetworkSourceTypeEnum.SkAdNetworkSourceType sk_ad_network_source_type = 153;</code>
     * @param int $var
     * @return $this
     */
    public function setSkAdNetworkSourceType($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V16\Enums\SkAdNetworkSourceTypeEnum\SkAdNetworkSourceType::class);
        $this->sk_ad_network_source_type = $var;

        return $this;
    }

    /**
     * iOS Store Kit Ad Network postback sequence index.
     *
     * Generated from protobuf field <code>optional int64 sk_ad_network_postback_sequence_index = 154;</code>
     * @return int|string
     */
    public function getSkAdNetworkPostbackSequenceIndex()
    {
        return isset($this->sk_ad_network_postback_sequence_index) ? $this->sk_ad_network_postback_sequence_index : 0;
    }

    public function hasSkAdNetworkPostbackSequenceIndex()
    {
        return isset($this->sk_ad_network_postback_sequence_index);
    }

    public function clearSkAdNetworkPostbackSequenceIndex()
    {
        unset($this->sk_ad_network_postback_sequence_index);
    }

    /**
     * iOS Store Kit Ad Network postback sequence index.
     *
     * Generated from protobuf field <code>optional int64 sk_ad_network_postback_sequence_index = 154;</code>
     * @param int|string $var
     * @return $this
     */
    public function setSkAdNetworkPostbackSequenceIndex($var)
    {
        GPBUtil::checkInt64($var);
        $this->sk_ad_network_postback_sequence_index = $var;

        return $this;
    }

    /**
     * Only used with CustomerAsset, CampaignAsset and AdGroupAsset metrics.
     * Indicates whether the interaction metrics occurred on the asset itself
     * or a different asset or ad unit.
     * Interactions (for example, clicks) are counted across all the parts of the
     * served ad (for example, Ad itself and other components like Sitelinks) when
     * they are served together. When interaction_on_this_asset is true, it means
     * the interactions are on this specific asset and when
     * interaction_on_this_asset is false, it means the interactions is not on
     * this specific asset but on other parts of the served ad this asset is
     * served with.
     *
     * Generated from protobuf field <code>optional .google.ads.googleads.v16.common.AssetInteractionTarget asset_interaction_target = 139;</code>
     * @return \Google\Ads\GoogleAds\V16\Common\AssetInteractionTarget|null
     */
    public function getAssetInteractionTarget()
    {
        return $this->asset_interaction_target;
    }

    public function hasAssetInteractionTarget()
    {
        return isset($this->asset_interaction_target);
    }

    public function clearAssetInteractionTarget()
    {
        unset($this->asset_interaction_target);
    }

    /**
     * Only used with CustomerAsset, CampaignAsset and AdGroupAsset metrics.
     * Indicates whether the interaction metrics occurred on the asset itself
     * or a different asset or ad unit.
     * Interactions (for example, clicks) are counted across all the parts of the
     * served ad (for example, Ad itself and other components like Sitelinks) when
     * they are served together. When interaction_on_this_asset is true, it means
     * the interactions are on this specific asset and when
     * interaction_on_this_asset is false, it means the interactions is not on
     * this specific asset but on other parts of the served ad this asset is
     * served with.
     *
     * Generated from protobuf field <code>optional .google.ads.googleads.v16.common.AssetInteractionTarget asset_interaction_target = 139;</code>
     * @param \Google\Ads\GoogleAds\V16\Common\AssetInteractionTarget $var
     * @return $this
     */
    public function setAssetInteractionTarget($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V16\Common\AssetInteractionTarget::class);
        $this->asset_interaction_target = $var;

        return $this;
    }

    /**
     * This is for segmenting conversions by whether the user is a new customer
     * or a returning customer. This segmentation is typically used to measure
     * the impact of customer acquisition goal.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.ConvertingUserPriorEngagementTypeAndLtvBucketEnum.ConvertingUserPriorEngagementTypeAndLtvBucket new_versus_returning_customers = 160;</code>
     * @return int
     */
    public function getNewVersusReturningCustomers()
    {
        return $this->new_versus_returning_customers;
    }

    /**
     * This is for segmenting conversions by whether the user is a new customer
     * or a returning customer. This segmentation is typically used to measure
     * the impact of customer acquisition goal.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.ConvertingUserPriorEngagementTypeAndLtvBucketEnum.ConvertingUserPriorEngagementTypeAndLtvBucket new_versus_returning_customers = 160;</code>
     * @param int $var
     * @return $this
     */
    public function setNewVersusReturningCustomers($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V16\Enums\ConvertingUserPriorEngagementTypeAndLtvBucketEnum\ConvertingUserPriorEngagementTypeAndLtvBucket::class);
        $this->new_versus_returning_customers = $var;

        return $this;
    }

}

