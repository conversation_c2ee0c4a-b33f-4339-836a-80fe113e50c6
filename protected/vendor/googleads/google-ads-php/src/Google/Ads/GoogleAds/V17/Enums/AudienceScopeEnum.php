<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v17/enums/audience_scope.proto

namespace Google\Ads\GoogleAds\V17\Enums;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Defines the scope an audience can be used in.
 *
 * Generated from protobuf message <code>google.ads.googleads.v17.enums.AudienceScopeEnum</code>
 */
class AudienceScopeEnum extends \Google\Protobuf\Internal\Message
{

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Ads\GoogleAds\V17\Enums\AudienceScope::initOnce();
        parent::__construct($data);
    }

}

