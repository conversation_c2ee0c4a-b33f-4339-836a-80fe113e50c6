<?php

/**
 * Copyright 2022 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

// Generated code ; DO NOT EDIT.

namespace Google\Ads\GoogleAds\Lib\V15;

use Google\Ads\GoogleAds\Constants;
use Google\Ads\GoogleAds\Lib\ConfigurationTrait;
use Google\Ads\GoogleAds\Lib\InsecureCredentialsWrapper;
use Google\Ads\GoogleAds\V17\Services\Client\AccountBudgetProposalServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\AccountLinkServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\AdGroupAdLabelServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\AdGroupAdServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\AdGroupAssetServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\AdGroupAssetSetServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\AdGroupBidModifierServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\AdGroupCriterionCustomizerServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\AdGroupCriterionLabelServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\AdGroupCriterionServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\AdGroupCustomizerServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\AdGroupExtensionSettingServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\AdGroupFeedServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\AdGroupLabelServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\AdGroupServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\AdParameterServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\AdServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\AssetGroupAssetServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\AssetGroupListingGroupFilterServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\AssetGroupServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\AssetGroupSignalServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\AssetServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\AssetSetAssetServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\AssetSetServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\AudienceInsightsServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\AudienceServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\BatchJobServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\BiddingDataExclusionServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\BiddingSeasonalityAdjustmentServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\BiddingStrategyServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\BillingSetupServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\BrandSuggestionServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\CampaignAssetServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\CampaignAssetSetServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\CampaignBidModifierServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\CampaignBudgetServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\CampaignConversionGoalServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\CampaignCriterionServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\CampaignCustomizerServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\CampaignDraftServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\CampaignExtensionSettingServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\CampaignFeedServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\CampaignGroupServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\CampaignLabelServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\CampaignLifecycleGoalServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\CampaignServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\CampaignSharedSetServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\ConversionActionServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\ConversionAdjustmentUploadServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\ConversionCustomVariableServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\ConversionGoalCampaignConfigServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\ConversionUploadServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\ConversionValueRuleServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\ConversionValueRuleSetServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\CustomAudienceServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\CustomConversionGoalServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\CustomerAssetServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\CustomerAssetSetServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\CustomerClientLinkServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\CustomerConversionGoalServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\CustomerCustomizerServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\CustomerExtensionSettingServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\CustomerFeedServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\CustomerLabelServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\CustomerLifecycleGoalServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\CustomerManagerLinkServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\CustomerNegativeCriterionServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\CustomerServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\CustomerSkAdNetworkConversionValueSchemaServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\CustomerUserAccessInvitationServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\CustomerUserAccessServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\CustomInterestServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\CustomizerAttributeServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\ExperimentArmServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\ExperimentServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\ExtensionFeedItemServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\FeedItemServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\FeedItemSetLinkServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\FeedItemSetServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\FeedItemTargetServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\FeedMappingServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\FeedServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\GeoTargetConstantServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\GoogleAdsFieldServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\GoogleAdsServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\InvoiceServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\KeywordPlanAdGroupKeywordServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\KeywordPlanAdGroupServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\KeywordPlanCampaignKeywordServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\KeywordPlanCampaignServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\KeywordPlanIdeaServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\KeywordPlanServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\KeywordThemeConstantServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\LabelServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\OfflineUserDataJobServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\PaymentsAccountServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\ProductLinkInvitationServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\ProductLinkServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\ReachPlanServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\RecommendationServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\RecommendationSubscriptionServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\RemarketingActionServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\SharedCriterionServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\SharedSetServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\SmartCampaignSettingServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\SmartCampaignSuggestServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\ThirdPartyAppAnalyticsLinkServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\TravelAssetSuggestionServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\UserDataServiceClient;
use Google\Ads\GoogleAds\V17\Services\Client\UserListServiceClient;
use Google\ApiCore\GrpcSupportTrait;
use Grpc\ChannelCredentials;

/**
 * Contains service client factory methods.
 */
trait ServiceClientFactoryTrait
{
    use ConfigurationTrait;
    use GrpcSupportTrait;

    private static $CREDENTIALS_LOADER_KEY = 'credentials';
    private static $DEVELOPER_TOKEN_KEY = 'developer-token';
    private static $LOGIN_CUSTOMER_ID_KEY = 'login-customer-id';
    private static $LINKED_CUSTOMER_ID_KEY = 'linked-customer-id';
    private static $SERVICE_ADDRESS_KEY = 'serviceAddress';
    private static $DEFAULT_SERVICE_ADDRESS = 'googleads.googleapis.com';
    private static $TRANSPORT_KEY = 'transport';
    private static $UNARY_MIDDLEWARES = 'unary-middlewares';
    private static $STREAMING_MIDDLEWARES = 'streaming-middlewares';

    /**
     * Gets the Google Ads client options for making API calls.
     *
     * @return array the client options
     */
    public function getGoogleAdsClientOptions(): array
    {
        $clientOptions = [
            self::$CREDENTIALS_LOADER_KEY => $this->getGrpcChannelIsSecure()
                ? $this->getOAuth2Credential()
                : new InsecureCredentialsWrapper($this->getOAuth2Credential()),
            self::$DEVELOPER_TOKEN_KEY => $this->getDeveloperToken()
        ];
        if (!empty($this->getLoginCustomerId())) {
            $clientOptions += [self::$LOGIN_CUSTOMER_ID_KEY => strval($this->getLoginCustomerId())];
        }
        if (!empty($this->getLinkedCustomerId())) {
            $clientOptions += [
                self::$LINKED_CUSTOMER_ID_KEY => strval($this->getLinkedCustomerId())
            ];
        }
        if (!empty($this->getEndpoint())) {
            $clientOptions += [self::$SERVICE_ADDRESS_KEY => $this->getEndpoint()];
        }
        $clientOptions['libName'] = Constants::LIBRARY_NAME;
        $clientOptions['libVersion'] = Constants::LIBRARY_VERSION;
        $clientOptions['transportConfig'] = [
            'grpc' => [
                'stubOpts' => [
                    // Inbound headers may exceed default (8kb) max header size.
                    // Sets max header size to 16MB, which should be more than necessary.
                    'grpc.max_metadata_size' => 16 * 1024 * 1024,
                    // Sets max response size to 64MB, since large responses will often exceed the
                    // default (4MB).
                    'grpc.max_receive_message_length' => 64 * 1024 * 1024
                ],
                'interceptors' => [new GoogleAdsFailuresInterceptor()]
            ]
        ];
        if (!empty($this->getLogger())) {
            $googleAdsLoggingInterceptor = new GoogleAdsLoggingInterceptor(
                new GoogleAdsCallLogger(
                    $this->getLogger(),
                    $this->getLogLevel(),
                    $this->getEndpoint() ?: self::$DEFAULT_SERVICE_ADDRESS
                )
            );
            array_unshift(
                $clientOptions['transportConfig']['grpc']['interceptors'],
                $googleAdsLoggingInterceptor
            );
        }
        array_push(
            $clientOptions['transportConfig']['grpc']['interceptors'],
            ...$this->getGrpcInterceptors()
        );
        if (!empty($this->getProxy())) {
            putenv('http_proxy=' . $this->getProxy());
        }
        if (!empty($this->getTransport())) {
            $clientOptions += [self::$TRANSPORT_KEY => $this->getTransport()];
        }
        if (
            self::getGrpcDependencyStatus()
            && (!$this->getGrpcChannelIsSecure() || !empty($this->getGrpcChannelCredential()))
        ) {
            $channelCredentials = $this->getGrpcChannelIsSecure()
                ? $this->getGrpcChannelCredential()
                : ChannelCredentials::createInsecure();
            $clientOptions['transportConfig']['grpc']['stubOpts'] += [
                self::$CREDENTIALS_LOADER_KEY => $channelCredentials
            ];
        }
        $clientOptions += [
            self::$UNARY_MIDDLEWARES => $this->getUnaryMiddlewares(),
            self::$STREAMING_MIDDLEWARES => $this->getStreamingMiddlewares()
        ];

        return $clientOptions;
    }

    /**
     * @return AccountBudgetProposalServiceClient|\Google\Ads\GoogleAds\V17\Services\Client\AccountBudgetProposalServiceClient
     */
    public function getAccountBudgetProposalServiceClient(): AccountBudgetProposalServiceClient
    {
        return $this->useGapicV2Source()
            ? new AccountBudgetProposalServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\AccountBudgetProposalServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return AccountLinkServiceClient|\Google\Ads\GoogleAds\V17\Services\Client\AccountLinkServiceClient
     */
    public function getAccountLinkServiceClient(): AccountLinkServiceClient
    {
        return $this->useGapicV2Source()
            ? new AccountLinkServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\AccountLinkServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return AdGroupAdLabelServiceClient|\Google\Ads\GoogleAds\V17\Services\Client\AdGroupAdLabelServiceClient
     */
    public function getAdGroupAdLabelServiceClient(): AdGroupAdLabelServiceClient
    {
        return $this->useGapicV2Source()
            ? new AdGroupAdLabelServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\AdGroupAdLabelServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return AdGroupAdServiceClient|\Google\Ads\GoogleAds\V17\Services\Client\AdGroupAdServiceClient
     */
    public function getAdGroupAdServiceClient(): AdGroupAdServiceClient
    {
        return $this->useGapicV2Source()
            ? new AdGroupAdServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\AdGroupAdServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return AdGroupAssetServiceClient|\Google\Ads\GoogleAds\V17\Services\Client\AdGroupAssetServiceClient
     */
    public function getAdGroupAssetServiceClient(): AdGroupAssetServiceClient
    {
        return $this->useGapicV2Source()
            ? new AdGroupAssetServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\AdGroupAssetServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return AdGroupAssetSetServiceClient|\Google\Ads\GoogleAds\V17\Services\Client\AdGroupAssetSetServiceClient
     */
    public function getAdGroupAssetSetServiceClient(): AdGroupAssetSetServiceClient
    {
        return $this->useGapicV2Source()
            ? new AdGroupAssetSetServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\AdGroupAssetSetServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return AdGroupBidModifierServiceClient|\Google\Ads\GoogleAds\V17\Services\Client\AdGroupBidModifierServiceClient
     */
    public function getAdGroupBidModifierServiceClient(): AdGroupBidModifierServiceClient
    {
        return $this->useGapicV2Source()
            ? new AdGroupBidModifierServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\AdGroupBidModifierServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return AdGroupCriterionCustomizerServiceClient|\Google\Ads\GoogleAds\V17\Services\Client\AdGroupCriterionCustomizerServiceClient
     */
    public function getAdGroupCriterionCustomizerServiceClient(): AdGroupCriterionCustomizerServiceClient
    {
        return $this->useGapicV2Source()
            ? new AdGroupCriterionCustomizerServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\AdGroupCriterionCustomizerServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return AdGroupCriterionLabelServiceClient|\Google\Ads\GoogleAds\V17\Services\Client\AdGroupCriterionLabelServiceClient
     */
    public function getAdGroupCriterionLabelServiceClient(): AdGroupCriterionLabelServiceClient
    {
        return $this->useGapicV2Source()
            ? new AdGroupCriterionLabelServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\AdGroupCriterionLabelServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return AdGroupCriterionServiceClient|\Google\Ads\GoogleAds\V17\Services\Client\AdGroupCriterionServiceClient
     */
    public function getAdGroupCriterionServiceClient(): AdGroupCriterionServiceClient
    {
        return $this->useGapicV2Source()
            ? new AdGroupCriterionServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\AdGroupCriterionServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return AdGroupCustomizerServiceClient|\Google\Ads\GoogleAds\V17\Services\Client\AdGroupCustomizerServiceClient
     */
    public function getAdGroupCustomizerServiceClient(): AdGroupCustomizerServiceClient
    {
        return $this->useGapicV2Source()
            ? new AdGroupCustomizerServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\AdGroupCustomizerServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return AdGroupExtensionSettingServiceClient|\Google\Ads\GoogleAds\V17\Services\Client\AdGroupExtensionSettingServiceClient
     */
    public function getAdGroupExtensionSettingServiceClient(): AdGroupExtensionSettingServiceClient
    {
        return $this->useGapicV2Source()
            ? new AdGroupExtensionSettingServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\AdGroupExtensionSettingServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return AdGroupFeedServiceClient|\Google\Ads\GoogleAds\V17\Services\Client\AdGroupFeedServiceClient
     */
    public function getAdGroupFeedServiceClient(): AdGroupFeedServiceClient
    {
        return $this->useGapicV2Source()
            ? new AdGroupFeedServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\AdGroupFeedServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return AdGroupLabelServiceClient|\Google\Ads\GoogleAds\V17\Services\Client\AdGroupLabelServiceClient
     */
    public function getAdGroupLabelServiceClient(): AdGroupLabelServiceClient
    {
        return $this->useGapicV2Source()
            ? new AdGroupLabelServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\AdGroupLabelServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return AdGroupServiceClient|\Google\Ads\GoogleAds\V17\Services\Client\AdGroupServiceClient
     */
    public function getAdGroupServiceClient(): AdGroupServiceClient
    {
        return $this->useGapicV2Source()
            ? new AdGroupServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\AdGroupServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return AdParameterServiceClient|\Google\Ads\GoogleAds\V17\Services\Client\AdParameterServiceClient
     */
    public function getAdParameterServiceClient(): AdParameterServiceClient
    {
        return $this->useGapicV2Source()
            ? new AdParameterServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\AdParameterServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return AdServiceClient|\Google\Ads\GoogleAds\V17\Services\Client\AdServiceClient
     */
    public function getAdServiceClient(): AdServiceClient
    {
        return $this->useGapicV2Source()
            ? new AdServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\AdServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return AssetGroupAssetServiceClient|\Google\Ads\GoogleAds\V17\Services\Client\AssetGroupAssetServiceClient
     */
    public function getAssetGroupAssetServiceClient(): AssetGroupAssetServiceClient
    {
        return $this->useGapicV2Source()
            ? new AssetGroupAssetServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\AssetGroupAssetServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return AssetGroupListingGroupFilterServiceClient|\Google\Ads\GoogleAds\V17\Services\Client\AssetGroupListingGroupFilterServiceClient
     */
    public function getAssetGroupListingGroupFilterServiceClient(): AssetGroupListingGroupFilterServiceClient
    {
        return $this->useGapicV2Source()
            ? new AssetGroupListingGroupFilterServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\AssetGroupListingGroupFilterServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return AssetGroupServiceClient|\Google\Ads\GoogleAds\V17\Services\Client\AssetGroupServiceClient
     */
    public function getAssetGroupServiceClient(): AssetGroupServiceClient
    {
        return $this->useGapicV2Source()
            ? new AssetGroupServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\AssetGroupServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return AssetGroupSignalServiceClient|\Google\Ads\GoogleAds\V17\Services\Client\AssetGroupSignalServiceClient
     */
    public function getAssetGroupSignalServiceClient(): AssetGroupSignalServiceClient
    {
        return $this->useGapicV2Source()
            ? new AssetGroupSignalServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\AssetGroupSignalServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return AssetServiceClient|\Google\Ads\GoogleAds\V17\Services\Client\AssetServiceClient
     */
    public function getAssetServiceClient(): AssetServiceClient
    {
        return $this->useGapicV2Source()
            ? new AssetServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\AssetServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return AssetSetAssetServiceClient|\Google\Ads\GoogleAds\V17\Services\Client\AssetSetAssetServiceClient
     */
    public function getAssetSetAssetServiceClient(): AssetSetAssetServiceClient
    {
        return $this->useGapicV2Source()
            ? new AssetSetAssetServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\AssetSetAssetServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return AssetSetServiceClient|\Google\Ads\GoogleAds\V17\Services\Client\AssetSetServiceClient
     */
    public function getAssetSetServiceClient(): AssetSetServiceClient
    {
        return $this->useGapicV2Source()
            ? new AssetSetServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\AssetSetServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return AudienceInsightsServiceClient|\Google\Ads\GoogleAds\V17\Services\Client\AudienceInsightsServiceClient
     */
    public function getAudienceInsightsServiceClient(): AudienceInsightsServiceClient
    {
        return $this->useGapicV2Source()
            ? new AudienceInsightsServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\AudienceInsightsServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return AudienceServiceClient|\Google\Ads\GoogleAds\V17\Services\Client\AudienceServiceClient
     */
    public function getAudienceServiceClient(): AudienceServiceClient
    {
        return $this->useGapicV2Source()
            ? new AudienceServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\AudienceServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return BatchJobServiceClient|\Google\Ads\GoogleAds\V17\Services\Client\BatchJobServiceClient
     */
    public function getBatchJobServiceClient(): BatchJobServiceClient
    {
        return $this->useGapicV2Source()
            ? new BatchJobServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\BatchJobServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return BiddingDataExclusionServiceClient|\Google\Ads\GoogleAds\V17\Services\Client\BiddingDataExclusionServiceClient
     */
    public function getBiddingDataExclusionServiceClient(): BiddingDataExclusionServiceClient
    {
        return $this->useGapicV2Source()
            ? new BiddingDataExclusionServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\BiddingDataExclusionServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return BiddingSeasonalityAdjustmentServiceClient|\Google\Ads\GoogleAds\V17\Services\Client\BiddingSeasonalityAdjustmentServiceClient
     */
    public function getBiddingSeasonalityAdjustmentServiceClient(): BiddingSeasonalityAdjustmentServiceClient
    {
        return $this->useGapicV2Source()
            ? new BiddingSeasonalityAdjustmentServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\BiddingSeasonalityAdjustmentServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return BiddingStrategyServiceClient|\Google\Ads\GoogleAds\V17\Services\Client\BiddingStrategyServiceClient
     */
    public function getBiddingStrategyServiceClient(): BiddingStrategyServiceClient
    {
        return $this->useGapicV2Source()
            ? new BiddingStrategyServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\BiddingStrategyServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return BillingSetupServiceClient|\Google\Ads\GoogleAds\V17\Services\Client\BillingSetupServiceClient
     */
    public function getBillingSetupServiceClient(): BillingSetupServiceClient
    {
        return $this->useGapicV2Source()
            ? new BillingSetupServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\BillingSetupServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return BrandSuggestionServiceClient|\Google\Ads\GoogleAds\V17\Services\Client\BrandSuggestionServiceClient
     */
    public function getBrandSuggestionServiceClient(): BrandSuggestionServiceClient
    {
        return $this->useGapicV2Source()
            ? new BrandSuggestionServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\BrandSuggestionServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return CampaignAssetServiceClient|\Google\Ads\GoogleAds\V17\Services\Client\CampaignAssetServiceClient
     */
    public function getCampaignAssetServiceClient(): CampaignAssetServiceClient
    {
        return $this->useGapicV2Source()
            ? new CampaignAssetServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\CampaignAssetServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return CampaignAssetSetServiceClient|\Google\Ads\GoogleAds\V17\Services\Client\CampaignAssetSetServiceClient
     */
    public function getCampaignAssetSetServiceClient(): CampaignAssetSetServiceClient
    {
        return $this->useGapicV2Source()
            ? new CampaignAssetSetServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\CampaignAssetSetServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return CampaignBidModifierServiceClient|\Google\Ads\GoogleAds\V17\Services\Client\CampaignBidModifierServiceClient
     */
    public function getCampaignBidModifierServiceClient(): CampaignBidModifierServiceClient
    {
        return $this->useGapicV2Source()
            ? new CampaignBidModifierServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\CampaignBidModifierServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return CampaignBudgetServiceClient|\Google\Ads\GoogleAds\V17\Services\Client\CampaignBudgetServiceClient
     */
    public function getCampaignBudgetServiceClient(): CampaignBudgetServiceClient
    {
        return $this->useGapicV2Source()
            ? new CampaignBudgetServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\CampaignBudgetServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return CampaignConversionGoalServiceClient|\Google\Ads\GoogleAds\V17\Services\Client\CampaignConversionGoalServiceClient
     */
    public function getCampaignConversionGoalServiceClient(): CampaignConversionGoalServiceClient
    {
        return $this->useGapicV2Source()
            ? new CampaignConversionGoalServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\CampaignConversionGoalServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return CampaignCriterionServiceClient|\Google\Ads\GoogleAds\V17\Services\Client\CampaignCriterionServiceClient
     */
    public function getCampaignCriterionServiceClient(): CampaignCriterionServiceClient
    {
        return $this->useGapicV2Source()
            ? new CampaignCriterionServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\CampaignCriterionServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return CampaignCustomizerServiceClient|\Google\Ads\GoogleAds\V17\Services\Client\CampaignCustomizerServiceClient
     */
    public function getCampaignCustomizerServiceClient(): CampaignCustomizerServiceClient
    {
        return $this->useGapicV2Source()
            ? new CampaignCustomizerServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\CampaignCustomizerServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return CampaignDraftServiceClient|\Google\Ads\GoogleAds\V17\Services\Client\CampaignDraftServiceClient
     */
    public function getCampaignDraftServiceClient(): CampaignDraftServiceClient
    {
        return $this->useGapicV2Source()
            ? new CampaignDraftServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\CampaignDraftServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return CampaignExtensionSettingServiceClient|\Google\Ads\GoogleAds\V17\Services\Client\CampaignExtensionSettingServiceClient
     */
    public function getCampaignExtensionSettingServiceClient(): CampaignExtensionSettingServiceClient
    {
        return $this->useGapicV2Source()
            ? new CampaignExtensionSettingServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\CampaignExtensionSettingServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return CampaignFeedServiceClient|\Google\Ads\GoogleAds\V17\Services\Client\CampaignFeedServiceClient
     */
    public function getCampaignFeedServiceClient(): CampaignFeedServiceClient
    {
        return $this->useGapicV2Source()
            ? new CampaignFeedServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\CampaignFeedServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return CampaignGroupServiceClient|\Google\Ads\GoogleAds\V17\Services\Client\CampaignGroupServiceClient
     */
    public function getCampaignGroupServiceClient(): CampaignGroupServiceClient
    {
        return $this->useGapicV2Source()
            ? new CampaignGroupServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\CampaignGroupServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return CampaignLabelServiceClient|\Google\Ads\GoogleAds\V17\Services\Client\CampaignLabelServiceClient
     */
    public function getCampaignLabelServiceClient(): CampaignLabelServiceClient
    {
        return $this->useGapicV2Source()
            ? new CampaignLabelServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\CampaignLabelServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return CampaignLifecycleGoalServiceClient|\Google\Ads\GoogleAds\V17\Services\Client\CampaignLifecycleGoalServiceClient
     */
    public function getCampaignLifecycleGoalServiceClient(): CampaignLifecycleGoalServiceClient
    {
        return $this->useGapicV2Source()
            ? new CampaignLifecycleGoalServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\CampaignLifecycleGoalServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return CampaignServiceClient|\Google\Ads\GoogleAds\V17\Services\Client\CampaignServiceClient
     */
    public function getCampaignServiceClient(): CampaignServiceClient
    {
        return $this->useGapicV2Source()
            ? new CampaignServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\CampaignServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return CampaignSharedSetServiceClient|\Google\Ads\GoogleAds\V17\Services\Client\CampaignSharedSetServiceClient
     */
    public function getCampaignSharedSetServiceClient(): CampaignSharedSetServiceClient
    {
        return $this->useGapicV2Source()
            ? new CampaignSharedSetServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\CampaignSharedSetServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return ConversionActionServiceClient|\Google\Ads\GoogleAds\V17\Services\Client\ConversionActionServiceClient
     */
    public function getConversionActionServiceClient(): ConversionActionServiceClient
    {
        return $this->useGapicV2Source()
            ? new ConversionActionServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\ConversionActionServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return ConversionAdjustmentUploadServiceClient|\Google\Ads\GoogleAds\V17\Services\Client\ConversionAdjustmentUploadServiceClient
     */
    public function getConversionAdjustmentUploadServiceClient(): ConversionAdjustmentUploadServiceClient
    {
        return $this->useGapicV2Source()
            ? new ConversionAdjustmentUploadServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\ConversionAdjustmentUploadServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return ConversionCustomVariableServiceClient|\Google\Ads\GoogleAds\V17\Services\Client\ConversionCustomVariableServiceClient
     */
    public function getConversionCustomVariableServiceClient(): ConversionCustomVariableServiceClient
    {
        return $this->useGapicV2Source()
            ? new ConversionCustomVariableServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\ConversionCustomVariableServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return ConversionGoalCampaignConfigServiceClient
     */
    public function getConversionGoalCampaignConfigServiceClient(): ConversionGoalCampaignConfigServiceClient
    {
        return $this->useGapicV2Source()
            ? new ConversionGoalCampaignConfigServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\ConversionGoalCampaignConfigServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return ConversionUploadServiceClient|\Google\Ads\GoogleAds\V17\Services\Client\ConversionUploadServiceClient
     */
    public function getConversionUploadServiceClient(): ConversionUploadServiceClient
    {
        return $this->useGapicV2Source()
            ? new ConversionUploadServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\ConversionUploadServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return ConversionValueRuleServiceClient
     */
    public function getConversionValueRuleServiceClient(): ConversionValueRuleServiceClient
    {
        return $this->useGapicV2Source()
            ? new ConversionValueRuleServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\ConversionValueRuleServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return ConversionValueRuleSetServiceClient
     */
    public function getConversionValueRuleSetServiceClient(): ConversionValueRuleSetServiceClient
    {
        return $this->useGapicV2Source()
            ? new ConversionValueRuleSetServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\ConversionValueRuleSetServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return CustomAudienceServiceClient
     */
    public function getCustomAudienceServiceClient(): CustomAudienceServiceClient
    {
        return $this->useGapicV2Source()
            ? new CustomAudienceServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\CustomAudienceServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return CustomConversionGoalServiceClient
     */
    public function getCustomConversionGoalServiceClient(): CustomConversionGoalServiceClient
    {
        return $this->useGapicV2Source()
            ? new CustomConversionGoalServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\CustomConversionGoalServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return CustomerAssetServiceClient
     */
    public function getCustomerAssetServiceClient(): CustomerAssetServiceClient
    {
        return $this->useGapicV2Source()
            ? new CustomerAssetServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\CustomerAssetServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return CustomerAssetSetServiceClient
     */
    public function getCustomerAssetSetServiceClient(): CustomerAssetSetServiceClient
    {
        return $this->useGapicV2Source()
            ? new CustomerAssetSetServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\CustomerAssetSetServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return CustomerClientLinkServiceClient
     */
    public function getCustomerClientLinkServiceClient(): CustomerClientLinkServiceClient
    {
        return $this->useGapicV2Source()
            ? new CustomerClientLinkServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\CustomerClientLinkServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return CustomerConversionGoalServiceClient
     */
    public function getCustomerConversionGoalServiceClient(): CustomerConversionGoalServiceClient
    {
        return $this->useGapicV2Source()
            ? new CustomerConversionGoalServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\CustomerConversionGoalServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return CustomerCustomizerServiceClient
     */
    public function getCustomerCustomizerServiceClient(): CustomerCustomizerServiceClient
    {
        return $this->useGapicV2Source()
            ? new CustomerCustomizerServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\CustomerCustomizerServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return CustomerExtensionSettingServiceClient
     */
    public function getCustomerExtensionSettingServiceClient(): CustomerExtensionSettingServiceClient
    {
        return $this->useGapicV2Source()
            ? new CustomerExtensionSettingServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\CustomerExtensionSettingServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return CustomerFeedServiceClient
     */
    public function getCustomerFeedServiceClient(): CustomerFeedServiceClient
    {
        return $this->useGapicV2Source()
            ? new CustomerFeedServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\CustomerFeedServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return CustomerLabelServiceClient
     */
    public function getCustomerLabelServiceClient(): CustomerLabelServiceClient
    {
        return $this->useGapicV2Source()
            ? new CustomerLabelServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\CustomerLabelServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return CustomerLifecycleGoalServiceClient
     */
    public function getCustomerLifecycleGoalServiceClient(): CustomerLifecycleGoalServiceClient
    {
        return $this->useGapicV2Source()
            ? new CustomerLifecycleGoalServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\CustomerLifecycleGoalServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return CustomerManagerLinkServiceClient
     */
    public function getCustomerManagerLinkServiceClient(): CustomerManagerLinkServiceClient
    {
        return $this->useGapicV2Source()
            ? new CustomerManagerLinkServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\CustomerManagerLinkServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return CustomerNegativeCriterionServiceClient
     */
    public function getCustomerNegativeCriterionServiceClient(): CustomerNegativeCriterionServiceClient
    {
        return $this->useGapicV2Source()
            ? new CustomerNegativeCriterionServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\CustomerNegativeCriterionServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return CustomerServiceClient
     */
    public function getCustomerServiceClient(): CustomerServiceClient
    {
        return $this->useGapicV2Source()
            ? new CustomerServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\CustomerServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return CustomerSkAdNetworkConversionValueSchemaServiceClient
     */
    public function getCustomerSkAdNetworkConversionValueSchemaServiceClient(): CustomerSkAdNetworkConversionValueSchemaServiceClient
    {
        return $this->useGapicV2Source()
            ? new CustomerSkAdNetworkConversionValueSchemaServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\CustomerSkAdNetworkConversionValueSchemaServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return CustomerUserAccessInvitationServiceClient
     */
    public function getCustomerUserAccessInvitationServiceClient(): CustomerUserAccessInvitationServiceClient
    {
        return $this->useGapicV2Source()
            ? new CustomerUserAccessInvitationServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\CustomerUserAccessInvitationServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return CustomerUserAccessServiceClient
     */
    public function getCustomerUserAccessServiceClient(): CustomerUserAccessServiceClient
    {
        return $this->useGapicV2Source()
            ? new CustomerUserAccessServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\CustomerUserAccessServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return CustomInterestServiceClient
     */
    public function getCustomInterestServiceClient(): CustomInterestServiceClient
    {
        return $this->useGapicV2Source()
            ? new CustomInterestServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\CustomInterestServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return CustomizerAttributeServiceClient
     */
    public function getCustomizerAttributeServiceClient(): CustomizerAttributeServiceClient
    {
        return $this->useGapicV2Source()
            ? new CustomizerAttributeServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\CustomizerAttributeServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return ExperimentArmServiceClient
     */
    public function getExperimentArmServiceClient(): ExperimentArmServiceClient
    {
        return $this->useGapicV2Source()
            ? new ExperimentArmServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\ExperimentArmServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return ExperimentServiceClient
     */
    public function getExperimentServiceClient(): ExperimentServiceClient
    {
        return $this->useGapicV2Source()
            ? new ExperimentServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\ExperimentServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return ExtensionFeedItemServiceClient
     */
    public function getExtensionFeedItemServiceClient(): ExtensionFeedItemServiceClient
    {
        return $this->useGapicV2Source()
            ? new ExtensionFeedItemServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\ExtensionFeedItemServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return FeedItemServiceClient
     */
    public function getFeedItemServiceClient(): FeedItemServiceClient
    {
        return $this->useGapicV2Source()
            ? new FeedItemServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\FeedItemServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return FeedItemSetLinkServiceClient
     */
    public function getFeedItemSetLinkServiceClient(): FeedItemSetLinkServiceClient
    {
        return $this->useGapicV2Source()
            ? new FeedItemSetLinkServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\FeedItemSetLinkServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return FeedItemSetServiceClient
     */
    public function getFeedItemSetServiceClient(): FeedItemSetServiceClient
    {
        return $this->useGapicV2Source()
            ? new FeedItemSetServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\FeedItemSetServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return FeedItemTargetServiceClient
     */
    public function getFeedItemTargetServiceClient(): FeedItemTargetServiceClient
    {
        return $this->useGapicV2Source()
            ? new FeedItemTargetServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\FeedItemTargetServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return FeedMappingServiceClient
     */
    public function getFeedMappingServiceClient(): FeedMappingServiceClient
    {
        return $this->useGapicV2Source()
            ? new FeedMappingServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\FeedMappingServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return FeedServiceClient
     */
    public function getFeedServiceClient(): FeedServiceClient
    {
        return $this->useGapicV2Source()
            ? new FeedServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\FeedServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return GeoTargetConstantServiceClient
     */
    public function getGeoTargetConstantServiceClient(): GeoTargetConstantServiceClient
    {
        return $this->useGapicV2Source()
            ? new GeoTargetConstantServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\GeoTargetConstantServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return GoogleAdsFieldServiceClient
     */
    public function getGoogleAdsFieldServiceClient(): GoogleAdsFieldServiceClient
    {
        return $this->useGapicV2Source()
            ? new GoogleAdsFieldServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\GoogleAdsFieldServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return GoogleAdsServiceClient
     */
    public function getGoogleAdsServiceClient(): GoogleAdsServiceClient
    {
        return $this->useGapicV2Source()
            ? new GoogleAdsServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\GoogleAdsServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return InvoiceServiceClient
     */
    public function getInvoiceServiceClient(): InvoiceServiceClient
    {
        return $this->useGapicV2Source()
            ? new InvoiceServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\InvoiceServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return KeywordPlanAdGroupKeywordServiceClient
     */
    public function getKeywordPlanAdGroupKeywordServiceClient(): KeywordPlanAdGroupKeywordServiceClient
    {
        return $this->useGapicV2Source()
            ? new KeywordPlanAdGroupKeywordServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\KeywordPlanAdGroupKeywordServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return KeywordPlanAdGroupServiceClient
     */
    public function getKeywordPlanAdGroupServiceClient(): KeywordPlanAdGroupServiceClient
    {
        return $this->useGapicV2Source()
            ? new KeywordPlanAdGroupServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\KeywordPlanAdGroupServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return KeywordPlanCampaignKeywordServiceClient
     */
    public function getKeywordPlanCampaignKeywordServiceClient(): KeywordPlanCampaignKeywordServiceClient
    {
        return $this->useGapicV2Source()
            ? new KeywordPlanCampaignKeywordServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\KeywordPlanCampaignKeywordServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return KeywordPlanCampaignServiceClient
     */
    public function getKeywordPlanCampaignServiceClient(): KeywordPlanCampaignServiceClient
    {
        return $this->useGapicV2Source()
            ? new KeywordPlanCampaignServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\KeywordPlanCampaignServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return KeywordPlanIdeaServiceClient
     */
    public function getKeywordPlanIdeaServiceClient(): KeywordPlanIdeaServiceClient
    {
        return $this->useGapicV2Source()
            ? new KeywordPlanIdeaServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\KeywordPlanIdeaServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return KeywordPlanServiceClient
     */
    public function getKeywordPlanServiceClient(): KeywordPlanServiceClient
    {
        return $this->useGapicV2Source()
            ? new KeywordPlanServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\KeywordPlanServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return KeywordThemeConstantServiceClient
     */
    public function getKeywordThemeConstantServiceClient(): KeywordThemeConstantServiceClient
    {
        return $this->useGapicV2Source()
            ? new KeywordThemeConstantServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\KeywordThemeConstantServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return LabelServiceClient
     */
    public function getLabelServiceClient(): LabelServiceClient
    {
        return $this->useGapicV2Source()
            ? new LabelServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\LabelServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return OfflineUserDataJobServiceClient
     */
    public function getOfflineUserDataJobServiceClient(): OfflineUserDataJobServiceClient
    {
        return $this->useGapicV2Source()
            ? new OfflineUserDataJobServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\OfflineUserDataJobServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return PaymentsAccountServiceClient
     */
    public function getPaymentsAccountServiceClient(): PaymentsAccountServiceClient
    {
        return $this->useGapicV2Source()
            ? new PaymentsAccountServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\PaymentsAccountServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return ProductLinkInvitationServiceClient
     */
    public function getProductLinkInvitationServiceClient(): ProductLinkInvitationServiceClient
    {
        return $this->useGapicV2Source()
            ? new ProductLinkInvitationServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\ProductLinkInvitationServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return ProductLinkServiceClient
     */
    public function getProductLinkServiceClient(): ProductLinkServiceClient
    {
        return $this->useGapicV2Source()
            ? new ProductLinkServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\ProductLinkServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return ReachPlanServiceClient
     */
    public function getReachPlanServiceClient(): ReachPlanServiceClient
    {
        return $this->useGapicV2Source()
            ? new ReachPlanServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\ReachPlanServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return RecommendationServiceClient
     */
    public function getRecommendationServiceClient(): RecommendationServiceClient
    {
        return $this->useGapicV2Source()
            ? new RecommendationServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\RecommendationServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return RecommendationSubscriptionServiceClient
     */
    public function getRecommendationSubscriptionServiceClient(): RecommendationSubscriptionServiceClient
    {
        return $this->useGapicV2Source()
            ? new RecommendationSubscriptionServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\RecommendationSubscriptionServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return RemarketingActionServiceClient
     */
    public function getRemarketingActionServiceClient(): RemarketingActionServiceClient
    {
        return $this->useGapicV2Source()
            ? new RemarketingActionServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\RemarketingActionServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return SharedCriterionServiceClient
     */
    public function getSharedCriterionServiceClient(): SharedCriterionServiceClient
    {
        return $this->useGapicV2Source()
            ? new SharedCriterionServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\SharedCriterionServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return SharedSetServiceClient
     */
    public function getSharedSetServiceClient(): SharedSetServiceClient
    {
        return $this->useGapicV2Source()
            ? new SharedSetServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\SharedSetServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return SmartCampaignSettingServiceClient
     */
    public function getSmartCampaignSettingServiceClient(): SmartCampaignSettingServiceClient
    {
        return $this->useGapicV2Source()
            ? new SmartCampaignSettingServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\SmartCampaignSettingServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return SmartCampaignSuggestServiceClient
     */
    public function getSmartCampaignSuggestServiceClient(): SmartCampaignSuggestServiceClient
    {
        return $this->useGapicV2Source()
            ? new SmartCampaignSuggestServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\SmartCampaignSuggestServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return ThirdPartyAppAnalyticsLinkServiceClient
     */
    public function getThirdPartyAppAnalyticsLinkServiceClient(): ThirdPartyAppAnalyticsLinkServiceClient
    {
        return $this->useGapicV2Source()
            ? new ThirdPartyAppAnalyticsLinkServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\ThirdPartyAppAnalyticsLinkServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return TravelAssetSuggestionServiceClient
     */
    public function getTravelAssetSuggestionServiceClient(): TravelAssetSuggestionServiceClient
    {
        return $this->useGapicV2Source()
            ? new TravelAssetSuggestionServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\TravelAssetSuggestionServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return UserDataServiceClient
     */
    public function getUserDataServiceClient(): UserDataServiceClient
    {
        return $this->useGapicV2Source()
            ? new UserDataServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\UserDataServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }

    /**
     * @return UserListServiceClient
     */
    public function getUserListServiceClient(): UserListServiceClient
    {
        return $this->useGapicV2Source()
            ? new UserListServiceClient($this->getGoogleAdsClientOptions())
            : new \Google\Ads\GoogleAds\V17\Services\Client\UserListServiceClient(
                $this->getGoogleAdsClientOptions()
            );
    }
}
