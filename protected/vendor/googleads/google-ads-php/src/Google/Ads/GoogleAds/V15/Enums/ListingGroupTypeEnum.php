<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v15/enums/listing_group_type.proto

namespace Google\Ads\GoogleAds\V15\Enums;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Container for enum describing the type of the listing group.
 *
 * Generated from protobuf message <code>google.ads.googleads.v15.enums.ListingGroupTypeEnum</code>
 */
class ListingGroupTypeEnum extends \Google\Protobuf\Internal\Message
{

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Ads\GoogleAds\V15\Enums\ListingGroupType::initOnce();
        parent::__construct($data);
    }

}

