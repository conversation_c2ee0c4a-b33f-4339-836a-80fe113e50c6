{"name": "googleads/google-ads-php", "homepage": "https://github.com/googleads/google-ads-php", "description": "Google Ads API client for PHP", "require": {"php": ">=8.1", "google/gax": "^1.19.1", "grpc/grpc": "^1.36.0", "google/protobuf": "^3.21.5 || ^4.26", "monolog/monolog": "^1.26 || ^2.0 || ^3.0"}, "require-dev": {"phpunit/phpunit": "^9.5", "squizlabs/php_codesniffer": "^3.5", "ext-bcmath": "*", "ext-grpc": "*", "ext-protobuf": "*", "ulrichsg/getopt-php": "^3.4", "react/http": "^1.2.0", "composer/composer": "^2.0"}, "suggest": {"react/http": "To run the AuthenticateInWebApplication.php example", "ext-protobuf": "For better performance, use the C implementation of Protobuf", "google/protobuf": "In case the C implementation of Protobuf is not suitable, use the PHP one", "ext-grpc": "To be able to use the gRPC transport, use the C implementation of gRPC", "grpc/grpc": "In case the C implementation of gRPC is not suitable, use the PHP one to enable other transports"}, "license": "Apache-2.0", "autoload": {"psr-4": {"Google\\Ads\\GoogleAds\\": "src/Google/Ads/GoogleAds", "GPBMetadata\\Google\\Ads\\GoogleAds\\": "metadata/Google/Ads/GoogleAds"}}, "autoload-dev": {"psr-4": {"Google\\Ads\\GoogleAds\\": "tests/Google/Ads/GoogleAds", "GPBMetadata\\Google\\Ads\\GoogleAds\\": "tests/metadata/Google/Ads/GoogleAds", "Google\\Ads\\GoogleAds\\Examples\\": "examples"}}, "authors": [{"name": "Google", "homepage": "https://github.com/googleads/google-ads-php/contributors"}], "scripts": {"test": ["phpunit tests"], "remove-api-version-support": ["Google\\Ads\\GoogleAds\\Util\\ApiVersionSupport::remove"]}}