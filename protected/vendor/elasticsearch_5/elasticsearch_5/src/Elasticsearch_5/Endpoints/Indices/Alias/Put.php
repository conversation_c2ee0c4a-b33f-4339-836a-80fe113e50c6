<?php

namespace Elasticsearch_5\Endpoints\Indices\Alias;

use Elasticsearch_5\Endpoints\AbstractEndpoint;
use Elasticsearch_5\Common\Exceptions;

/**
 * Class Put
 *
 * @category Elasticsearch_5
 * @package  Elasticsearch_5\Endpoints\Indices\Alias
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.apache.org/licenses/LICENSE-2.0 Apache2
 * @link     http://elastic.co
 */
class Put extends AbstractEndpoint
{
    // The name of the alias to be created or updated
    private $name;

    /**
     * @param array $body
     *
     * @return $this
     * @throws \Elasticsearch_5\Common\Exceptions\InvalidArgumentException
     */
    public function setBody($body)
    {
        if (isset($body) !== true) {
            return $this;
        }

        $this->body = $body;

        return $this;
    }

    /**
     * @param $name
     *
     * @return $this
     */
    public function setName($name)
    {
        if (isset($name) !== true) {
            return $this;
        }

        $this->name = $name;

        return $this;
    }

    /**
     * @return string
     * @throws \Elasticsearch_5\Common\Exceptions\RuntimeException
     */
    public function getURI()
    {
        if (isset($this->name) !== true) {
            throw new Exceptions\RuntimeException(
                'name is required for Put'
            );
        }

        if (isset($this->index) !== true) {
            throw new Exceptions\RuntimeException(
                'index is required for Put'
            );
        }
        $index = $this->index;
        $name = $this->name;
        $uri = "/$index/_alias/$name";

        return $uri;
    }

    /**
     * @return string[]
     */
    public function getParamWhitelist()
    {
        return array(
            'timeout',
            'master_timeout',
        );
    }

    /**
     * @return string
     */
    public function getMethod()
    {
        return 'PUT';
    }
}
