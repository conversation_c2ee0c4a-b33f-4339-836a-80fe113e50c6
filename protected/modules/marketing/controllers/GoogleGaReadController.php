<?php

use common\library\google_ads\conversion\ConversionService;
use common\library\google_ads\ga\GaSiteList;
/**
 * Created by PhpStorm.
 * User: bing
 * Date: 2020/01/07
 * Time: 15:45
 */

class GoogleGaReadController extends MarketingBaseController
{

    /**
     * 获取Ga的web list
     * @param int $cur_page
     * @param int $page_size
     */
    public function actionGaWebSiteList($cur_page = 1, $page_size = 10)
    {
        $this->validate([
            'cur_page' => 'integer',
            'page_size' => 'integer',
        ]);

        $user = User::getLoginUser();
        $clientId = $user->getClientId();
        $googleGa = new GaSiteList($clientId);
        $googleGa->setCmsFlag(false);
        $googleGa->setOrderBy('create_time');
        $googleGa->setOrder('DESC');
        $googleGa->setOffset(($cur_page-1) *  $page_size);
        $googleGa->getFormatter()->webSiteListInfoSetting();
        $googleGa->setLimit($page_size);
        $this->success([
            'list' => $googleGa->find(),
            'count' => $googleGa->count()
        ]);
    }


    public function actionIsExistAccounts()
    {
        // 获取google的ga账号，判断是否存在google账号
        $user = User::getLoginUser();
        $clientId = $user->getClientId();
        $gaService = new \common\library\google_ads\ga\GaService($clientId);
        list($accessId, $gaAccountId) = $gaService->getFistGaAccount();

        $data = [
            'access_id' => $accessId,
            'ga_account_id' => $gaAccountId,
            'is_exist_ga' => $gaAccountId? 1: 0
        ];

        $this->success($data);
    }

    /**
     * GA 询盘列表
     * @param int $cur_page
     * @param int $page_size
     * @return void
     */
    public function actionGaLeadList($cur_page = 1, $page_size = 10)
    {
        $this->validate([
            'cur_page' => 'integer',
            'page_size' => 'integer',
        ]);

        $user = User::getLoginUser();
        $clientId = $user->getClientId();
        $googleGa = new GaSiteList($clientId);
        $googleGa->setOffset(($cur_page-1) *  $page_size);
        $googleGa->setLimit($page_size);
        $googleGa->setOrderBy('create_time');
        $googleGa->setOrder('DESC');
        $googleGa->getFormatter()->leadOwnerWebSiteListInfoSetting();
        $count = $googleGa->count();
        $result = $count > 0 ? $googleGa->find() : [];
        $this->success([
         'list' => $result,
         'count' => $count
        ]);
    }

    public function actionCheckWebSite($web_site_name = '', $web_site_url = '')
    {
        $this->validate([
            'web_site_name' => 'string|max:128',
            'web_site_url' => 'url'
        ]);

        $user = User::getLoginUser();
        $clientId = $user->getClientId();

        $gaSite = new \common\library\google_ads\ga\GaSite($clientId);
        $gaSite->web_site_name = $web_site_name;
        $gaSite->web_site_url = $web_site_url;
        $gaSite->validateSite(false);

        $this->success([]);
    }

    /**
     * WebSiteIds
     */
    public function actionWebSiteIds()
    {
        $user = User::getLoginUser();
        $clientId = $user->getClientId();
        // smart+ 所有网站
        $cmsSiteFlag = null;

        // mkt lite （只有第三方网站）
        if(!\common\library\privilege_v3\PrivilegeService::getInstance($clientId)->hasFunctional(\common\library\privilege_v3\PrivilegeConstants::FUNCTIONAL_MARKETING_SITE_CUSTOM))
        {
            $cmsSiteFlag = false;
        }

        $listObj = new GaSiteList($clientId);
        $listObj->setEnableFlag(\common\components\BaseObject::ENABLE_FLAG_TRUE);
//        $listObj->setCmsFlag($cmsSiteFlag);
        $listObj->setOrderBy('create_time');
        $listObj->setOrder('DESC');
        $listObj->getFormatter()->webSiteIdsInfoSetting();
        $data = $listObj->find();
        $this->success($data);

    }

}
