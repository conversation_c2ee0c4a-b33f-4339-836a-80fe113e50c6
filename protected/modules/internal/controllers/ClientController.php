<?php

/**
 * Created by PhpStorm.
 * User: cayley
 * Date: 2017/10/19
 * Time: 上午10:44
 */

use common\library\account\Account;
use common\library\alimail\AliMailOrganizationObj;
use common\library\alimail\AliyunAlimailApi;
use common\library\ames\AmesConstants;
use common\library\ames\EventHelper;
use common\library\cms\site\Site;
use common\library\privilege_v3\PrivilegeConstants;
use common\library\privilege_v3\PrivilegeService;

class ClientController extends  InternalController
{
    public function filters()
    {
        return CMap::mergeArray(parent::filters(), [
            'client'=> 'client - error,create,update,init,stats,updateUserNum,updateDiskSpace,updateEdmCount,clearEdm,deleteUserMail,exportData,initSite,updateSystem,cleanCache,resetDTCAccountPassword,recycleEdmCount,updateKCoin,clearUsedStandardKCoin,fixShopGa',
        ]);
    }
    private $updateFiled =['name','full_name','homepage','logo','tel','fax','email','linkman','country',
        'province','city','category_ids','master_account','client_type','user_num','edm_count','edm_used_count',
        'disk_space','used_space','comment','business_license','taxpayer_id','mysql_set_id','mongo_set_id','pgsql_set_id',
        'create_user','create_by','update_user','status','enable_flag','activated_flag','activated_time','version',
        'valid_from','valid_to','products','is_oversea','address','translated_name','currency_id', 'use_zone',
    ];

    private function filterField($data)
    {
        if( empty($data) )
            return [];

        foreach ( $data  as $field =>$value )
        {
            if( !in_array($field, $this->updateFiled) )
                unset($data[$field]);

        }

        return $data;
    }

    /**
     * 批量更新或写入数据
     * time: 11:06 AM
     * user: huagongzi
     * @param $datas
     * @return false|string
     */
    public function actionUpdateClientContractRelation($datas){
        if(!is_array($datas)){
            $datas = json_decode($datas, true);
        }

        // 参数检查
        if(empty($datas) || (count($datas)  > 100)){
            throw new RuntimeException("参数不能为空且数组元素不能超过100个");
        }

        // 以client_id的值为键
        $datas     = ArrayUtil::index($datas,'client_id');
        $clientIds = array_filter(array_keys($datas));

        // 通过clientid查找存在的记录
        $items = common\library\account\Helper::findByClientIds($clientIds);
        $items = ArrayUtil::index($items,'client_id');

        // 统计要更新的记录并更新数据和缓存
        $updateClientIds = array_intersect($clientIds,array_keys($items));

        foreach ($updateClientIds as $clientId){
            if(isset($datas[$clientId])){
                $clientContractRelation = common\library\account\ClientContractRelation::getClient($clientId);
                $attributes             = $clientContractRelation->getAttributes();

                // 更新字段值
                $clientContractRelation->update_time = date("Y-m-d H:i:s", time());
                foreach ($datas[$clientId] as $key => $value){
                    if(isset($attributes[$key])){
                        $clientContractRelation->$key = $value;
                    }
                }

                // 更新记录
                $clientContractRelation->update();
            }
        }

        // 统计要insert的记录并写入数据
        $insertClientIds = array_diff($clientIds,array_keys($items));
        $insertDatas     = [];

        foreach ($insertClientIds as $clientId){
            if(isset($datas[$clientId])){
                array_push($insertDatas, $datas[$clientId]);
            }
        }

        // 批量写入数据
        if(!empty($insertDatas)){
            common\library\account\Helper::batchInsert($insertDatas);
        }

        // 写入缓存
        $checkAmesCanAuthMap = \common\library\ames\Helper::batchCheckCanAuthAmes($clientIds);
        foreach ($clientIds as $clientId) {
            $client = \common\library\account\Client::getClient($clientId);
            $client->setExtentAttributes([\common\library\account\Client::EXTERNAL_KEY_AMES_CAN_AUTH => $checkAmesCanAuthMap[$clientId] ?? 1]);
            $client->saveExtentAttributes();
        }

        return $this->success();
    }

    public function actionUserList($client_id){

        $adminUserId = PrivilegeService::getInstance($client_id)->getAdminUserId();
        if( !$adminUserId )
            throw new RuntimeException('未配置crm管理员账号');
        User::setLoginUserById($adminUserId);

        $user = User::getLoginUser();
        $client = \common\library\account\Client::getClient($client_id);
        $listObj = new \common\library\account\UserList();
        $listObj->setClientId($user->getClientId());
        $listObj->setEnableFlag(\common\library\account\UserInfo::ENABLE_TRUE);
        $listObj->getFormatter()->setShowBindingUserList(1);
        $listObj->getFormatter()->setShowFrozenable(1);
        $listObj->getFormatter()->setShowGlobalFreezeFlag(1);
        $listObj->setFilterBindingUserFlag(1);
        $listObj->setIncludeInviteUserFlag(1);

        $userList =  $listObj->find();

        $list = array(
            'user_list' => $userList,
            'user_num' => $client->user_num,
            'used_user_num' => count($userList)
        );


        return $this->success($list);
    }

    public function actionCreate($master_account,$name, $full_name, $create_user, $data='')
    {
        $client = new \common\library\account\Client();
        $client->name = $name;
        $client->full_name = $full_name;

        $client->master_account = $master_account;


        $client->create_user = $create_user;
        $client->create_time = date('Y-m-d H:i:s');

        $data = $this->filterField(json_decode($data, true));
        if( !empty($data) )
        {
            foreach ( $data as $k =>$v)
                $client->{$k} = $v;
        }
        $client->dc_id = Yii::app()->params['dc_id'] ?? DbSet::DC_ID_CN;

        if (!$client->save())
            throw new RuntimeException('保存公司资料失败 ');

        LogUtil::info(__FILE__ . " create client:{$client->client_id} maser_account:{$master_account} name:{$name} full_name:{$full_name}".
         "create_user: {$create_user} data:".json_encode($data));

        $this->success(['client_id' => $client->client_id]);
    }

    public function actionUpdate($client_id, $data)
    {
        $data = $this->filterField(json_decode($data, true));
        if(empty($data)) {
            throw new RuntimeException('data is empty');
        }

        LogUtil::info('update client '.$client_id.' data:'.json_encode($data));

        (new \common\library\validation\Validator(
            $data,
            [
                'valid_from' => 'sometimes|required|date',
                'valid_to' => 'sometimes|required|date',
                'tel' => 'string',
                'email' => 'email',
                'activated_time' => 'date',
                'user_num' => 'sometimes|required|numeric',
            ]
        ))->validate();

        $client = \common\library\account\Client::getClient($client_id);
        if( $client->isNew() )
            throw  new RuntimeException(\Yii::t('cms', 'client not exist'));

        foreach ( $data as $k =>$v)
            $client->{$k} = $v;

        $oldAttributes = $client->getOldAttributes();
        $newAttributes = $client->getAttributes();
        if ($oldAttributes['valid_to'] != $newAttributes['valid_to'])
        {
            $clientPrivilege = new \common\library\privilege_v3\ClientPrivilegeService($client_id);
            $clientPrivilege->syncCrmValidTo($newAttributes['valid_to']);
        }


        $client->update(array_keys($data));

        $this->success('');
    }

    public function actionInit($client_id, $version = PrivilegeConstants::CRM_SYSTEM_ID, $admin_user_id=0, array $module_id = [], $is_trial = 0, $scene = 0, $business_id = 0)
    {

        $versions = explode(',', $version);

//        $greyClientIds = \common\library\GreyEnvHelper::getGreyClientIds();
//        if(in_array($client_id, $greyClientIds) || in_array($version, ['crm_pro','crm_smart'])){
//            $forwardUrl = "api/internal/client/init";
//            $params = [
//                'client_id' => $client_id,
//                'version' => $version,
//                'admin_user_id' => $admin_user_id,
//            ];
//
//            $result = \common\library\GreyEnvHelper::forwardOmg($forwardUrl, $params, 15);
//            return $this->success($result);
//        }

        // 防并发处理
        $redis = RedisService::getInstance('redis');
        $lockKey = 'init_client:lock:'. $client_id . $business_id;
        $command = $redis->createCommand('set');
        $command->setArguments([$lockKey, 1, 'EX', 10, 'NX']);//10s 延长init接口锁时间 5s->10s
        $lock = $redis->executeCommand($command);


        LogUtil::info("Fix iniClient $client_id");
        LogUtil::info("iniClient $client_id ,version: $version , admin_user_id: {$admin_user_id}, moduleIds: " . json_encode($module_id) . " lock:{$lock}" . " trial: {$is_trial}" .  " scene: {$scene}");
        if (!$lock) {
            $this->success(['lock'=>true, 'msg'=> '没有执行, 因为没抢到锁']);
        }

        // 多 version 判断
        if (!empty(array_diff($versions, PrivilegeConstants::ALLOW_SYSTEMS))) {
            throw new ProcessException("初始化失败[client_id:{$client_id}],{$version}中有不存在的版本");
        }

        $crmFlag = false;
        try {

            $admin_user_id = !empty($admin_user_id)? $admin_user_id: PrivilegeService::getInstance($client_id)->getAdminUserId();
            if (!$admin_user_id)
            {
                throw new ProcessException("初始化失败[client_id:{$client_id}],未获取到AdminUser");
            }

            $user = User::getUserObject($admin_user_id);

            if( $user->getClientId() != $client_id)
            {
                throw  new ProcessException("初始化失败[client_id:{$client_id}] , AdminUser与client_id不匹配");
            }

            User::setLoginUserById($admin_user_id);

            $client = \common\library\account\Client::getClient($client_id);

            if (!$client->mysql_set_id)
            {
                if (\Yii::app()->params['env'] == 'test') {
                    $module = \common\library\account\service\DbService::MODULE_V5;
                } else {
                    if (in_array(PrivilegeConstants::CRM_PRO_SYSTEM_ID, $versions)) {
                        $module = \common\library\account\service\DbService::MODULE_PRO;
                    } elseif (in_array(PrivilegeConstants::OKKI_PERSONAL_LEADS_AI_ID, $versions)) {
                        $module = \common\library\account\service\DbService::MODULE_PERSONAL;
                    } elseif (in_array(PrivilegeConstants::OKKI_PERSONAL_LEADS_AI_BASIC_ID, $versions)){
                        $module = \common\library\account\service\DbService::MODULE_PERSONAL;
                    } elseif (in_array(PrivilegeConstants::OKKI_PERSONAL_LEADS_AI_GLOBAL_BASIC_ID, $versions)){
                        $module = \common\library\account\service\DbService::MODULE_PERSONAL_GLOBAL;
                    } elseif (in_array(PrivilegeConstants::OKKI_ALI_BASIC_ID, $versions)) {
                        $module = \common\library\account\service\DbService::MODULE_ALI_BASIC;
                    } elseif (array_intersect([PrivilegeConstants::TW_LITE_AI_ID, PrivilegeConstants::TW_SMART_AI_ID, PrivilegeConstants::HK_LITE_AI_ID, PrivilegeConstants::HK_SMART_AI_ID, PrivilegeConstants::HK_PRO_AI_ID], $versions)) {
                        $module = \common\library\account\service\DbService::MODULE_TW;
                    }  elseif(in_array(PrivilegeConstants::OKKI_LEADS_AI_JP_ID, $versions)) {
                        $module = \common\library\account\service\DbService::MODULE_GLOBAL;
                    }
                    else {
                        $module = \common\library\account\service\DbService::MODULE_V5;
                    }
                }

                if ($scene == \common\library\account\Client::SCENE_OF_SALE_RACE) {
                    $module = \common\library\account\service\DbService::MODULE_SALE_RACE;
                }

                $callableFun = function () use($module) {

                return \common\library\account\service\DbService::getDefaultDbSetId($module);

                };

                //加锁，防并发处理
                $lockKey = 'change_db_set' . ':' . $module;

                $redis = RedisService::cache();
                list($mysql, $pg) = RedisService::mutexRun($redis, $lockKey, $callableFun, 3);

                if(!$mysql['set_id'] || !$pg['set_id'])
                {
                    throw  new ProcessException("无可用set");
                }

                $client->mysql_set_id = $mysql['set_id'];
                $client->mongo_set_id = 0;
                $client->pgsql_set_id = $pg['set_id'];
                $client->update(['mysql_set_id', 'mongo_set_id', 'pgsql_set_id']);

                LogUtil::info("init db for client {$client_id}  version: {$version}  result : mysql  {$mysql['set_id']} -  {$mysql['name']}   pgsql  {$pg['set_id']} -  {$pg['name']} ");

                //通知dbproxy同步dbset
                \common\library\account\service\DbService::syncDbProxy($client_id);

            }

            // 任意客户均可试用
            if ($is_trial) {
                $client->status = \common\library\account\Client::STATUS_OF_TRIAL;
                $client->update('status');
            } elseif ($client->isTrial()) {
                $client->status = \common\library\account\Client::STATUS_OF_NORMAL;
                $client->update('status');
            }

            // 台湾的这两个版本 && 香港版本 需要加AI模块的东西
            if (array_intersect([PrivilegeConstants::TW_LITE_AI_ID, PrivilegeConstants::TW_SMART_AI_ID, PrivilegeConstants::HK_LITE_AI_ID, PrivilegeConstants::HK_SMART_AI_ID, PrivilegeConstants::HK_PRO_AI_ID], $versions)) {
                $module_id = array_unique(array_merge($module_id, [PrivilegeConstants::MODULE_OKKI_AI]));
            }

            // crm 下不存在init的system才 初始化
            foreach ($versions as $initVersion) {

                $systemModule = \common\library\privilege_v3\Helper::getSystemModuleById($initVersion);

                // todo 临时处理
                if (PrivilegeConstants::SYSTEM_MODULE_SHOP == $systemModule)
                {
                    $module_id = array_unique(array_merge($module_id, [PrivilegeConstants::MODULE_OKKI_SHOP]));
                    LogUtil::info("update moduleIds: " . json_encode($module_id));
                }

                // todo 初始化 crm 系统配置
                if (PrivilegeConstants::SYSTEM_MODULE_CRM == $systemModule)
                {
                    $crmFlag = true;

                    // 没有在该分支保存 client_system 数据 原因：
                    if (!PrivilegeClientSystem::checkInit($client_id, $systemModule))
                    {
                        CustomerOptionService::iniClient($user, $initVersion);
                    }

                    // todo @junelian
                    try {
                        //初始化商机的销售阶段
                        $opportunityStageService = new \common\library\opportunity\stage\OpportunityStageService($client_id);
                        $opportunityStageService->systemInitOpportunitySalesFlowAndStage();
                    } catch (\Throwable $exception) {
                        LogUtil::exception($exception);
                    }
                }

                //初始化版本权限 内部进行 system module 拆分
                PrivilegeService::getInstance($client_id)->initClient($initVersion, $admin_user_id,true,false, $module_id, $business_id);

                // todo 初始化 crm 系统配置
                if (PrivilegeConstants::SYSTEM_MODULE_CRM == $systemModule)
                {
                    //初始化审批流自定义配置
                    \common\library\approval_flow\Helper::initApprovalFlowCustomConfig($client_id);

                    //迁移旧版审批配置
                    $config = new \common\library\approval_flow\migrate\Config($admin_user_id);
                    $config->init = true;
                    $config->run();

                    // 初始化业绩 规则
                    common\library\performance_v2\Helper::initClientPrepareRuleByClient($client_id);
                    // 初始化业绩 团队墙
                    common\library\performance_v2\Helper::initClientTeamWallSetting($client_id);
                    // 初始化任务默认
                    \common\library\task\Helper::initPresetTask($client_id);

                    // 初始化实例数据
                    (new \common\library\example\CompanyExample($client_id, $admin_user_id))->create();

                    //圈战客户初始化权限
                    if ($scene == \common\library\account\Client::SCENE_OF_SALE_RACE) {
                        $raceConfig = new \common\library\alibaba\sales_race\InitConfig($client_id);
                        $raceConfig->run();
                    }

                    //工作报告初始化
                    \common\library\work_journal\Helper::InitSystemWorkJournalTemplate($client_id);

                    try {
                        // 同步TM聊天记录
                        \common\library\ai_agent\Helper::addToSyncTmMessageList($client_id);
                    } catch (\Throwable $e) {
                        \LogUtil::error("addToSyncTmMessageList_Error", [
                            'clientId' => $client_id,
                            'exception' => $e->getMessage(),
                        ]);
                    }
                }

                //初始化新版商机，Smart、新版Pro、Pro+（旧版Pro）才开启新版商机功能
                if (in_array($initVersion, [PrivilegeConstants::CRM_SMART_SYSTEM_ID, PrivilegeConstants::CRM_PRO_SYSTEM_ID, PrivilegeConstants::OKKI_PRO_SYSTEM_ID, PrivilegeConstants::TW_SMART_AI_ID])) {
                    \common\library\privilege_v3\Helper::switchModule($client_id, \common\library\account\Client::EXTERNAL_KEY_OPPORTUNITY_V2_SWITCH, 1);

                    // pro || (smart && 账号数达到20) 可以开启新版评分功能
                    $systemUserNum = \common\library\privilege_v3\PrivilegeService::getInstance($client_id)->getSystemUserNum(\common\library\privilege_v3\PrivilegeConstants::SYSTEM_MODULE_CRM);
                    if (
                        in_array($initVersion, [PrivilegeConstants::CRM_PRO_SYSTEM_ID, PrivilegeConstants::OKKI_PRO_SYSTEM_ID]) ||
                        (in_array($initVersion, [PrivilegeConstants::CRM_SMART_SYSTEM_ID, PrivilegeConstants::TW_SMART_AI_ID]) && ($systemUserNum['user_num'] ?? 0) >= 20)
                    ) {
                        \common\library\privilege_v3\Helper::switchModule($client_id, \common\library\account\Client::EXTERNAL_KEY_ASSESS_DIMENSION_SWITCH, 1);
                    }
                }

                //Lite、Smart、新版Pro、Pro+（旧版Pro）才开启新版线索功能、和新版公海客群功能
                if (in_array($initVersion, [
                        PrivilegeConstants::CRM_LITE_SYSTEM_ID,
                        PrivilegeConstants::CRM_LITE_2021_ID,
                        PrivilegeConstants::CRM_LITE_2023_ID,
                        PrivilegeConstants::CRM_SMART_SYSTEM_ID,
                        PrivilegeConstants::CRM_PRO_SYSTEM_ID,
                        PrivilegeConstants::OKKI_PRO_SYSTEM_ID,
                        PrivilegeConstants::TW_LITE_AI_ID,
                        PrivilegeConstants::TW_SMART_AI_ID,
                    ]
                )) {
                    \common\library\privilege_v3\Helper::switchModule($client_id, \common\library\account\Client::EXTERNAL_KEY_LEAD_V2_SWITCH, 1);
                    \common\library\privilege_v3\Helper::switchModule($client_id, \common\library\account\Client::EXTERNAL_KEY_CUSTOMER_PUBLIC_V2_SWITCH, 1);
                }

                // 切换版本同权限赋权后操作
                \common\library\privilege_v3\functional\FunctionalHandler::refresh($client_id);
            }

        } catch (Exception $e) {
            LogUtil::error("init client error : client_id:{$client_id} user_id:{$admin_user_id} error:".$e->getMessage().$e->getTraceAsString());
            //将初始化所得的异常邮件通知
            throw new ProcessException($e->getMessage(),  $e->getCode(), $e);
        } finally
        {
            $redis->del([$lockKey]);

            if ($crmFlag)
            {
                // crm初始化 判断是否正常执行
                \common\library\CommandRunner::run('privilege', 'afterInit', [
                    'client_id' => $client_id,
                    'request_params' => "'".json_encode($_REQUEST) . "'"
                ], '/tmp/privilege_after_init.log', 0);
            }
        }

        // 检查缓存
        if (count($client->getExtentAttributes() ?: []) <= 10) {
            \LogUtil::info("error_update_client_external_info", [
                'ext_info' => $client->getExtentAttributes(),
            ]);
            $client->deleteCache();
            $client->getExtentAttributes();
        }

        $this->success(['lock'=> false, 'msg' => '初始化执行完成']);

    }

    public function actionStats($client_id, $show_user_info=0, $show_admin_user=0, $show_k_coin_info=0)
    {
//        $greyClientIds = \common\library\GreyEnvHelper::getGreyClientIds();
//        if(in_array($client_id, $greyClientIds)){
//            $forwardUrl = "api/internal/client/stats";
//            $params = [
//                'client_id' => $client_id,
//                'show_user_info' => $show_user_info,
//                'show_admin_user' => $show_admin_user,
//            ];
//
//            $result = \common\library\GreyEnvHelper::forwardOmg($forwardUrl, $params, 2);
//            return $this->success($result);
//        }


        $data =  \common\modules\internal\library\account\ClientService::stats($client_id, $show_user_info, $show_admin_user, $show_k_coin_info);

        $this->success($data);
    }

    /**
     * system id 为空情况下默认更新 system module 为 crm 下的数据 （兼容旧逻辑）
     *
     *
     * @param $client_id
     * @param $user_num
     * @param $operate
     * @param $desc
     * @param $contract_id
     * @param $system_id
     * @return false|string|void
     * @throws CDbException
     */
    public function actionUpdateUserNum($client_id, $user_num=0, $operate= 'add', $desc='', $contract_id=0, $system_id = '', $business_id = 0)
    {
        $this->validate([
            'client_id' => 'required|int',
            'user_num' => 'required|int',
            'operate' => 'required|string',
            'desc' => 'string',
            'contract_id' => 'string',
            'system_id' => 'string',
            'business_id' => 'int'
        ]);

        $data = \common\modules\internal\library\account\ClientService::updateUserNum($client_id, $user_num, $operate, $desc, $contract_id, $system_id, $business_id);
        $this->success($data);
    }

    public function actionUpdateWabaCount($client_id, $waba_count=0, $operate= 'add', $desc='', $contract_id=0)
    {
        $adminUserId = PrivilegeService::getInstance($client_id)->getAdminUserId();
        if (!$adminUserId)
        {
            throw new ProcessException("增加waba数失败[client_id:{$client_id}],未获取到AdminUser");
        }

        $user = User::getUserObject($adminUserId);

        if( $user->getClientId() != $client_id)
        {
            throw  new ProcessException("增加waba数失败[client_id:{$adminUserId}] , AdminUser与client_id不匹配");
        }

        User::setLoginUserById($adminUserId);

        //敏感操作,直接走数据库,不读缓存
        $client = Client::findByClientId($client_id);
        if( !$client )
            throw  new RuntimeException(\Yii::t('cms', 'client not exist'));

        if(!in_array($operate,['add', 'minus', 'cover'])){
            throw  new RuntimeException('未知操作'.$operate);
        }

        $service = new \common\library\waba\WabaService($client_id, $adminUserId);
        if($operate == 'add'){
            //应os组需求, 执行过的合同不再执行了, 直接返回成功
            $record = ClientResourceRecord::findByContractId($contract_id, ClientResourceRecord::RECORD_WABA_COUNT);
            if( !empty($record) )
            {
                LogUtil::info("该合同已经执行了, 无需再次执行, 直接赶回成功. client_id:{$client_id} contract_id: {$contract_id}");
                return  $this->success([
                    'client_id' => $client_id,
                    'old_waba_count' => 0,
                    'new_waba_count' => 0,
                ]);
            }
        }

        $ret = $service->updateBalance((string)$operate, (int)$waba_count, (string)$contract_id);
        $oldCount = $ret['old_count'] ?? 0;
        $newCount = $ret['new_count'] ?? 0;
        if( !empty($ret))
        {
            ClientResourceRecord::addRecord($client_id,ClientResourceRecord::RECORD_WABA_COUNT, $oldCount, $newCount, $desc, $contract_id);

        }

        LogUtil::info("更新了增购会话数: client_id:{$client_id} old {$oldCount} => new {$newCount}");

        $this->success([
            'client_id' => $client_id,
            'old_waba_count' => $oldCount,
            'new_waba_count' => $newCount,
        ]);
    }

    public function actionGetWabaCount($client_id)
    {
        $adminUserId = PrivilegeService::getInstance($client_id)->getAdminUserId();
        if (!$adminUserId)
        {
            throw new ProcessException("未获取到AdminUser");
        }
        User::setLoginUserById($adminUserId);

        $WABAService = \common\library\waba\WabaService::getInstance($client_id, $adminUserId);

        $left = $WABAService->getConversationLeftNum();

        $this->success([
            'client_id' => $client_id,
            'free_left_num' => $left['free_left_num'],
            'service_left_num' => $left['service_left_num'],
            'balance_left_num' => $left['balance_left_num']
        ]);
    }


    public function actionUpdateEdmCount($client_id, $edm_count=0, $operate= 'add', $desc='', $contract_id=0)
    {
        $adminUserId = PrivilegeService::getInstance($client_id)->getAdminUserId();
        if (!$adminUserId)
        {
            throw new ProcessException("增加edm数失败[client_id:{$client_id}],未获取到AdminUser");
        }

        $user = User::getUserObject($adminUserId);

        if( $user->getClientId() != $client_id)
        {
            throw  new ProcessException("增加edm数失败[client_id:{$adminUserId}] , AdminUser与client_id不匹配");
        }

        User::setLoginUserById($adminUserId);

        //敏感操作,直接走数据库,不读缓存
        $client = Client::findByClientId($client_id);
        if( !$client )
            throw  new RuntimeException(\Yii::t('cms', 'client not exist'));

        $oldEdmCount = $client->edm_count;

        $edmRecord = new \common\library\edm\record\EdmRecord($client_id);
        $edmRecord->user_id = 0;
        $edmRecord->reward_type = \common\library\edm\record\EdmRecord::EDM_RECORD_BUY;

        switch ( $operate )
        {
            case 'add':
                //应os组需求, 执行过的合同不再执行了, 直接返回成功
                $record = ClientResourceRecord::findByContractId($contract_id, ClientResourceRecord::RECORD_EDM_COUNT);
                if( !empty($record) )
                {
                    LogUtil::info("该合同已经执行了, 无需再次执行, 直接赶回成功. client_id:{$client_id} contract_id: {$contract_id} current_edm_count:{$oldEdmCount}");
                    return  $this->success([
                        'client_id' => $client_id,
                        'old_edm_count' => $oldEdmCount,
                        'new_edm_count' => $oldEdmCount,
                    ]);
                }

                $edmRecord->reward_count = $edm_count;

                $client->edm_count +=  $edm_count;

                break;
            case  'minus':

                if( $edm_count > $client->edm_count)
                {
                    $client->edm_count = 0;
                    $edmRecord->reward_count = $client->edm_count;
                }else
                {
                    $client->edm_count -=  $edm_count;
                    $edmRecord->reward_count = $edm_count;
                }

                break;
            case  'cover':
                $client->edm_count =  $edm_count;
                $edmRecord->reward_count = $edm_count;
                $edmRecord->detail = "将公司营销数重置为$edm_count";
                break;
            default:
                throw  new RuntimeException('未知操作'.$operate);
        }

        $edmRecord->save();

        $newEdmCount = $client->edm_count;
        $ret = $client->update(['edm_count']);
        if( $ret !== false)
        {
            ClientResourceRecord::addRecord($client_id,ClientResourceRecord::RECORD_EDM_COUNT, $oldEdmCount, $newEdmCount, $desc, $contract_id);
            //更新缓存
            $clientObj = (new \common\library\account\Client())->loadByModel($client, true);
            $clientObj->deleteCache();
        }

        LogUtil::info("更新了购买空间数: client_id:{$client_id} old {$oldEdmCount} => new {$newEdmCount}");

        $client = \common\library\account\Client::getClient($user->getClientId());
        $clientExtentAttrs = $client->getExtentAttributes();
        $crmVersion = intval($clientExtentAttrs[\common\library\account\Client::EXTERNAL_KEY_CRM_VERSION] ?? 0);
        LogUtil::info("更新了购买edm数: client_id:{$client_id}  crmVersion {$crmVersion}");
        $okkiLeadsFlag = \common\library\ames\Helper::getOkkiLeadsVersionFlag($crmVersion);
        // 退款扣减的情况跳过分配
        // 个人版用户只有一个账号，分配在主账号下
        if(($okkiLeadsFlag || $client->client_type == \common\library\account\Client::CLIENT_TYPE_PERSONAL) && $newEdmCount != 0){   // 分配条件：okki_leads独立售卖版，单账号用户  自动分配归属在主账号下
            LogUtil::info("更新了购买edm数: client_id:{$client_id} old {$oldEdmCount} => new {$newEdmCount} crmVersion {$crmVersion} allocateEdmCount {$newEdmCount}");
            $user->setClient($clientObj);
            $user->info()->allocateEdmCount($newEdmCount);
        }

        $this->success([
            'client_id' => $client_id,
            'old_edm_count' => $oldEdmCount,
            'new_edm_count' => $newEdmCount,
        ]);
    }

    public function actionUpdateDiskSpace($client_id, $disk_space=0, $operate= 'add', $desc='', $contract_id=0)
    {
        //敏感操作,直接走数据库,不读缓存
        $client = Client::findByClientId($client_id);
        if( !$client )
            throw  new RuntimeException(\Yii::t('cms', 'client not exist'));

        $oldDiskSpace = $client->disk_space;
        switch ( $operate )
        {
            case 'add':
                if( $contract_id )
                {
                    //应os组需求, 执行过的合同不再执行了, 直接返回成功
                    $record = ClientResourceRecord::findByContractId($contract_id, ClientResourceRecord::RECORD_DISK_SPACE);
                    if( !empty($record) )
                    {
                        LogUtil::info("该合同已经执行了, 无需再次执行, 直接赶回成功. client_id:{$client_id} contract_id: {$contract_id} current_disk_space:{$oldDiskSpace}");
                        return  $this->success([
                            'client_id' => $client_id,
                            'old_disk_space' => $oldDiskSpace,
                            'new_disk_space' => $oldDiskSpace,
                        ]);
                    }

                }
                $client->disk_space +=  $disk_space;
                break;
            case  'minus':
                if( $disk_space > $client->disk_space)
                {
                    $client->disk_space = 0;
                }else
                {
                    $client->disk_space -=  $disk_space;
                }
                break;
            case  'cover':
                $client->disk_space =  $disk_space;
                break;
            default:
                throw  new RuntimeException('未知操作'.$operate);
        }

        $newDiskSpace = $client->disk_space;
        $ret = $client->update(['disk_space']);
        if( $ret !== false)
        {
            ClientResourceRecord::addRecord($client_id,ClientResourceRecord::RECORD_DISK_SPACE, $oldDiskSpace, $newDiskSpace, $desc, $contract_id);
            //更新缓存

            $clientObj = (new \common\library\account\Client())->loadByModel($client, true);
            $clientObj->deleteCache();

//            //空间数变化时, 需要重新计算使用空间
            $date = '';
            $log = "/tmp/refreshSpace-{$client_id}.log";
            \common\library\CommandRunner::run(
                'space',
                'oneClientTaskDispatcher',
                [
                    'clientId' => $client_id,
                    'date' => $date
                ],
                $log
            );
        }

        LogUtil::info("更新了购买空间数: client_id:{$client_id}  old {$oldDiskSpace} => new {$newDiskSpace}");

        $this->success([
            'client_id' => $client_id,
            'old_disk_space' => $oldDiskSpace,
            'new_disk_space' => $newDiskSpace,
        ]);
    }

    public function actionPaidList()
    {
        $systemIds = array_keys(require \Yii::getPathOfAlias("application.config") . '/crm-privilege-v3.php');
        $sql = 'SELECT a.client_id, GROUP_CONCAT(DISTINCT a.system_id) AS system_ids, GROUP_CONCAT(DISTINCT b.user_id) AS user_ids FROM tbl_privilege_client_system as a '
             . 'LEFT JOIN tbl_privilege_role_user as b ON a.client_id=b.client_id '
             . 'WHERE a.enable_flag=1 AND a.is_paid=1 AND b.enable_flag=1 AND a.system_id IN (\''. implode('\',\'', $systemIds) .'\') GROUP BY a.client_id';
        $records = \Yii::app()->account_base_db->createCommand($sql)->queryAll(true);

        if (!$records) {
            return $this->success([]);
        }

        foreach ($records as &$record) {
            $record['system_ids'] = array_values(array_filter(array_unique(explode(',', $record['system_ids']))));
            $record['user_ids'] = array_values(array_filter(array_unique(explode(',', $record['user_ids']))));
        }

        unset($record);
        $this->success($records);
    }

    public function actionDxClientList()
    {
        $systemIds = require \Yii::getPathOfAlias("application.config") . '/crm-privilege-v3.php';
        foreach ($systemIds as $key => $item) {
            if (empty($item['functional']['crm.functional.dx']) && empty($item['functional']['dx.functional.base'])) {
                unset($systemIds[$key]);
            }
        }

        $systemIds = array_keys($systemIds);
        $sql = 'SELECT a.client_id, GROUP_CONCAT(DISTINCT a.system_id) AS system_ids, GROUP_CONCAT(DISTINCT b.user_id) AS user_ids FROM tbl_privilege_client_system as a '
             . 'LEFT JOIN tbl_privilege_role_user as b ON a.client_id=b.client_id '
             . 'WHERE a.enable_flag=1 AND a.is_paid=1 AND b.enable_flag=1 AND a.system_id IN (\''. implode('\',\'', $systemIds) .'\') GROUP BY a.client_id';
        $records = \Yii::app()->account_base_db->createCommand($sql)->queryAll(true);

        if (!$records) {
            return $this->success([]);
        }

        $clientIds = [];
        foreach ($records as &$record) {
            $clientIds[] = $record['client_id'];
            $record['system_ids'] = array_values(array_filter(array_unique(explode(',', $record['system_ids']))));
            $record['user_ids'] = array_values(array_filter(array_unique(explode(',', $record['user_ids']))));
        }
        unset($record);

        $moduleIds = require \Yii::getPathOfAlias("application.config") . '/crm-module-privilege.php';
        foreach ($moduleIds as $key => $item) {
            if (empty($item['functional']['crm.functional.dx']) && empty($item['functional']['dx.functional.base'])) {
                unset($moduleIds[$key]);
            }
        }

        $moduleIds = array_keys($moduleIds);
        $sql = 'SELECT a.client_id, GROUP_CONCAT(DISTINCT a.module_id) AS module_ids, GROUP_CONCAT(DISTINCT b.user_id) AS user_ids FROM tbl_privilege_client_module as a '
            . 'LEFT JOIN tbl_privilege_role_user as b ON a.client_id=b.client_id '
            . 'WHERE a.enable_flag=1 AND b.enable_flag=1 AND a.module_id IN (\''. implode('\',\'', $moduleIds) .'\') GROUP BY a.client_id';

        $moduleRecords = \Yii::app()->account_base_db->createCommand($sql)->queryAll(true);
        foreach ($moduleRecords as $moduleRecord) {
            if (in_array($moduleRecord['client_id'], $clientIds)) {
                continue;
            }
            $records[] = [
                'client_id' => $moduleRecord['client_id'],
                'system_ids' => ['crm_basic'],
                'user_ids' => array_values(array_filter(array_unique(explode(',', $moduleRecord['user_ids']))))
            ];
        }

        $this->success($records);
    }

    /**
     * 获取clientId下未分配的和已分配用户但未使用的
     * @param $client_id
     */
    public function actionGetRemainEdm($client_id)
    {
        $adminUserId = PrivilegeService::getInstance($client_id)->getAdminUserId();
        if (!$adminUserId)
        {
            throw new ProcessException("未获取到AdminUser");
        }
        User::setLoginUserById($adminUserId);
        // 停止edmTask任务
        \common\library\edm\Helper::forceFinishEdmTask($client_id);
        // 计算剩余edm数
        $remainTotalEdmCount =  \common\library\edm\Helper::getRemainEdm($client_id);
        $this->success([
            'client_id' => $client_id,
            'remain_edm_count' => $remainTotalEdmCount
        ]);
    }

    /**
     * @param $client_id
     * @param $edm_freeze_flag 1冻结，0解冻
     * @throws CDbException
     */
    public function actionFreezeEdm($client_id, int $edm_freeze_flag)
    {
        $adminUserId = PrivilegeService::getInstance($client_id)->getAdminUserId();
        if(!$adminUserId)
        {
            throw new ProcessException("未获取到AdminUser");
        }
        User::setLoginUserById($adminUserId);

        if(!in_array($edm_freeze_flag, [0,1])) {
            throw  new RuntimeException('edm_freeze_flag only 0 or 1');
        }

        $key = \common\library\account\Client::SETTING_KEY_EDM_FREEZE_FLAG;
        $values = [$key=>$edm_freeze_flag];
        \ClientSetting::setValues($client_id, $values);
        // 设置更新缓存
        $client = \common\library\account\Client::getClient($client_id);
        $client->cacheHashMSet($client->getCacheSettingAttributesKey(), $values);
        $this->success('');
    }

    /**
     * 由于未来可能续约原因，执行关闭系统时，os要调用要清空原剩余的edm数量
     * @param $client_id
     * @throws CDbException
     * @throws CException
     */
    public function actionClearEdm($client_id)
    {
        $adminUserId = PrivilegeService::getInstance($client_id)->getAdminUserId();
        if (!$adminUserId)
        {
            throw new ProcessException("未获取到AdminUser");
        }

        User::setLoginUserById($adminUserId);

        $key = \common\library\account\Client::SETTING_KEY_EDM_FREEZE_FLAG;
        $freezeEdmValue = \ClientSetting::getValue($client_id, $key);
        $client = \Client::findByClientId($client_id);
        $validTo = $client->valid_to;

        if($freezeEdmValue == 0)
        {
            throw new \RuntimeException("该公司clientId={$client_id}, edm没有冻结，不能清空");
        }

        if(time() < strtotime($validTo))
        {
            throw new \RuntimeException("该公司clientId={$client_id}, 还没到关闭系统时间={$validTo},暂不清空。若到了关闭时间会执行清空");
        }

        // 清空并更新缓存
        $cacheClient = \common\library\account\Client::getClient($client_id);
        \common\library\account\Client::cleanCacheMap($client_id);
        $cacheClient->setModel($client);
        $cacheClient->updateCache();

       // edm 冻结，当前时间大于等于关闭系统时间，则满足条件
        if($freezeEdmValue == 1 && $client_id > 0)
        {
            \LogUtil::info("clientId={$client_id}, current edm count=".$client->edm_count);
            // client清空为0
            $cacheClient->deductEdmCount($client->edm_count);

            $db = \UserInfo::model()->getDbConnection();
            $sql = "SELECT user_id,current_count_company FROM tbl_user_info WHERE client_id={$client_id} ";
            $result = $db->createCommand($sql)->queryAll();
            \LogUtil::info("clientId={$client_id},user current edm count=".json_encode($result));

            // user更新为0
            foreach($result as $item)
            {
                try {
                    $userId = $item['user_id'];
                    $userInfo = new \common\library\account\UserInfo($userId, $client_id);
                    $userInfo->current_count_company = 0;
                    $userInfo->update(['current_count_company']);
                }catch (\Exception $e)
                {
                    \LogUtil::info("clientId={$client_id}, update user current_count_company error msg=".$e->getMessage());
                }
            }

            // edm冻结标志解除，未来可能续约，否则会影响
            $values = [ $key=> 0];
            \ClientSetting::setValues($client_id, $values);
            // 设置更新缓存
            $client = \common\library\account\Client::getClient($client_id);
            $client->cacheHashMSet($client->getCacheSettingAttributesKey(), $values);
        }

        $this->success('');
    }

    /**
     * 删除client下所有的绑定邮箱
     * @param $client_id
     */
    public function actionDeleteUserMail($client_id)
    {
        $adminUserId = PrivilegeService::getInstance($client_id)->getAdminUserId();
        if (!$adminUserId)
        {
            throw new ProcessException("未获取到AdminUser");
        }

        User::setLoginUserById($adminUserId);

        $listObj = new UserMailList();
        $listObj->setClientId($client_id);
        $listObj->setEnableFlag(1);
        $allUserMailList = $listObj->find();

        foreach ($allUserMailList as $userMail)
        {
            if (!empty($userMail['user_id'])) {
                $userMailService = new UserMailService($userMail['user_id'], $userMail['user_mail_id']);
                $userMailService->delete();
            }
        }

        $this->success('');
    }

    /**
     * @param $client_id
     * @param $operator_type 'invite, cancel_invite, etc'
     */
    public function actionUpdateUseUserNum($client_id, $operator_type)
    {
        LogUtil::info("UpdateUseUserNum begin clientId:{$client_id};type:{$operator_type}");

        //敏感操作,直接走数据库,不读缓存
        $client = Client::findByClientId($client_id);
        if (!$client)
            throw  new RuntimeException(\Yii::t('cms', 'client not exist'));

        $clientObj = (new \common\library\account\Client())->loadByModel($client);
        $newUsedUserNum = $clientObj->getUsedUserNum();

        $attrs = $clientObj->getExtentAttributes([\common\library\account\Client::EXTERNAL_KEY_USED_USER_NUM]);
        $oldUsedUserNum = $attrs[\common\library\account\Client::EXTERNAL_KEY_USED_USER_NUM];

        if($oldUsedUserNum != $newUsedUserNum){
            //已使用的子账号数,不包含主账号
            $clientObj->updateUsedNum($newUsedUserNum);

            LogUtil::info("update user_num new :". ($newUsedUserNum)." old:{$oldUsedUserNum} \n");
        }

        $this->success([
            'client_id' => $client_id,
            'old_used_user_num' => $oldUsedUserNum,
            'new_used_user_num' => $newUsedUserNum,
        ]);
    }

    public function actionCleanCache($client_id)
    {
        \common\library\account\Helper::cleancClientCache($client_id);
        return $this->success();
    }

    public function actionEnableAmesFunction($client_id)
    {
        $uniqId = uniqid();
        LogUtil::info("EnableAmesFunction {$uniqId} start clientId:{$client_id}");

        $client = \common\library\account\Client::getClient($client_id);

        if ($client->isNew()) {
            return $this->success([]);
        }

        if ($client->valid_to <= date('Y-m-d H:i:s')) {
            LogUtil::info("EnableAmesFunction {$uniqId} valid_to:" . $client->valid_to);
            return $this->success([]);
        }

        $privilegeService = PrivilegeService::getInstance($client_id);
        $mainSystemId = $privilegeService->getMainSystemId();
        if (in_array($mainSystemId, [PrivilegeConstants::DX_SYSTEM_ID, PrivilegeConstants::CRM_PRO_SYSTEM_ID, PrivilegeConstants::CRM_SYSTEM_ID])) {
            LogUtil::info("EnableAmesFunction {$uniqId} mainSystemId: {$mainSystemId}");
            return $this->success([]);
        }

        $checkAmesCanAuthMap = \common\library\ames\Helper::batchCheckCanAuthAmes([$client_id]);
        if (empty($checkAmesCanAuthMap[$client_id])) {
            LogUtil::info("EnableAmesFunction {$uniqId} checkAmesCanAuthMap:" . json_encode($checkAmesCanAuthMap));
            return $this->success([]);
        }

        $privilegeService->assignFunction(\common\library\privilege_v3\PrivilegeConstants::FUNCTIONAL_AMES);
        $client = \common\library\account\Client::getClient($client_id);
        $client->setExtentAttributes([\common\library\account\Client::EXTERNAL_KEY_AMES_CAN_AUTH => $checkAmesCanAuthMap[$client_id]]);
        $client->saveExtentAttributes();
        LogUtil::info("EnableAmesFunction {$uniqId} end:" . json_encode($checkAmesCanAuthMap));
        return $this->success([]);
    }

    public function actionExportData($client_id, $data_type, $task_id)
    {
        $client = Client::findByClientId($client_id);
        $adminUserId = PrivilegeService::getInstance($client_id)->getAdminUserId();
        User::setLoginUserById($adminUserId);

        if (!$client)
            throw  new RuntimeException(\Yii::t('cms', 'client not exist'));

        $AllType = explode(',', $data_type);

        if (empty($AllType) || array_diff($AllType, \common\modules\internal\library\export\Helper::$allowMap)) {
            throw new Exception(\Yii::t('file', 'The type of export is incorrect'));
        }

        foreach ($AllType as $type) {
            \common\modules\internal\library\export\Helper::createExportTask($type, $client_id, $adminUserId, $task_id);
        }
        $this->success([]);
    }

    /**
     * 删除指定key 的cache缓存
     *
     * @param $key
     * @param $client_ids
     * @return false|string|void
     */
    public function actionDeleteCacheByKey($key, $client_ids)
    {
        $this->validate([
            'key' => 'required|string',
            'client_ids' => 'required|string'
        ]);

        if (!in_array($key, \common\library\account\Client::CACHE_PREFIX_CAN_DELETE_BY_INTERNAL_LIST))
            return $this->fail(ErrorCode::CODE_FAIL, ' key 不支持删除!');

        $clientIds = explode(',', $client_ids);

        $deleteKeys = [];
        array_walk($clientIds, function ($clientId) use ($key, &$deleteKeys) {
           if (empty($clientId))
               return ;

           $deleteKeys[] = $key . ':' . $clientId;
        });

        if (empty($deleteKeys))
            return $this->fail(ErrorCode::CODE_FAIL, ' client 为空!');

        $redis = RedisService::cache();
        $value = $redis->mget($deleteKeys);

        \LogUtil::info('value before delete : ' .json_encode($value));
        $redis->del($deleteKeys);
        $this->success();
    }


    /**
     * 初始化站点
     * @param $client_id
     * @param $site_name 店铺名
     * @return void
     */
    public function actionInitSite($client_id, $site_name) {
        if (empty($client_id) || empty($site_name)) {
            throw new RuntimeException('client_id or site_name is empty');
        }
        $now = date('Y-m-d H:i:s');
        $site_id = \common\library\cms\site\SiteService::initSite(['client_id' => $client_id, 'site_name' => $site_name, 'create_time' => $now, "update_time" => $now]);
        if (!$site_id) {
            throw new RuntimeException('create site fail');
        }
        $this->success(['client_id' => $client_id, 'site_id' => $site_id]);
    }

    /**
     * @param $client_id
     * @param $site_id
     * @return bool|void
     * shops初始化ga4媒体资源
     * 从原站点初始化流程移除
     */
    public function actionInitGaProperty($client_id, $site_id)
    {
        $this->validate([
            'client_id' => 'required|int',
            'site_id' => 'required|int'
        ]);
        $siteInfo = new Site($site_id);
        if ($siteInfo->isNew() || $siteInfo->client_id != $client_id) {
            throw new RuntimeException('site_id not match client_id');
        }
        $siteInfo = $siteInfo->getAttributes(['site_id','client_id', 'create_user','site_name']);
        $gaSite = new \common\library\google_ads\ga\GaSite($client_id);
        $gaSite->loadByCmsSiteId($site_id);
        if (!$gaSite->isNew()) {
            if (!empty($gaSite->property_id)) {
                return $this->success(['property_id' => $gaSite->property_id]);
            }
            return \common\library\cms\site\SiteService::fixGaPropertyId($gaSite);
        }
        $siteDomain = CmsSiteDomain::model()->find('client_id = :client_id and site_id = :site_id', array(':client_id' => $client_id, ':site_id' => $site_id));
        $domain = $siteDomain->domain ?? '';
        if (empty($domain)) {
            throw new RuntimeException('site domain not exists');
        }
        $siteInfo['domain'] = $domain;
        $propertyId = \common\library\cms\site\SiteService::bindGaAccountWebSite($siteInfo);
        $this->success(['property_id' => $propertyId]);
    }

    /**
     * 更新站点
     * @param $client_id
     * @param $site_id
     * @param $data
     * @return void
     */
    public function actionUpdateSite($client_id, $site_id, $data){
        $data = json_decode($data, true);
        $rules = [
            'valid_from' => 'sometimes|required|date',
            'valid_to' => 'sometimes|required|date',
            'welcome' => 'string',
            'copyright' => 'string',
            'idc_number' => 'string',
            'idc_link' => 'string',
            'favicon' => 'string',
            'template_id' => 'numeric',
        ];
        $data = array_intersect_key($data,$rules);
        if(empty($data)) {
            throw new RuntimeException('data is empty');
        }

        LogUtil::info(sprintf("更新站点开始， client_id:%s, site_id:%s, data:%s", $client_id, $site_id, json_encode($data)));

        (new \common\library\validation\Validator($data,$rules))->validate();
        $res = common\library\cms\site\SiteService::updateSite($client_id, $site_id, $data);
        if (!$res) {
            throw new RuntimeException('update site fail');
        }
        $this->success();
    }


    /**
     * 更新系统信息
     *
     * @param $client_id
     * @param $data
     *  格式 [
     *          'system_id'(对应 os version) => $datum
     *       ]
     * @return void
     */
    public function actionUpdateSystem($client_id, $data, $business_id = 0)
    {
        $this->validate([
            'client_id' => 'required|int',
            'data' => 'required|string'
        ]);

        $data = json_decode($data, true);

        LogUtil::info('update client '.$client_id.' data:'.json_encode($data));

        if (empty($data))
            return $this->success();

        // 简单foreach
        foreach ($data as $systemId => $datum) {

            $datum = $this->filterField($datum);
            if(empty($datum)) {
                throw new RuntimeException('data is empty');
            }

            (new \common\library\validation\Validator(
                $datum,
                [
                    'valid_from' => 'required|date',
                    'valid_to' => 'required|date',
                ]
            ))->validate();

            $systemModule = \common\library\privilege_v3\Helper::getSystemModuleById($systemId);
            $object = \PrivilegeClientSystem::model()
                ->find('client_id=:client_id and system_module=:system_module and system_id=:system_id and business_id=:business_id', [
                    ':client_id' => $client_id,
                    ':system_module' => $systemModule,
                    ':system_id' => $systemId,
                    ':business_id' => $business_id,
                ]);

            if ($object) {
                $object->update_time = date('Y-m-d H:i:s');
                $object->valid_to = $datum['valid_to'];
                $object->valid_from = $datum['valid_from'];
                $succeed = $object->save();
            } else {
                $object = new \PrivilegeClientSystem();
                $object->client_id = $client_id;
                $object->system_module = $systemModule;
                $object->system_id = $systemId;
                $object->valid_to = $datum['valid_to'];
                $object->valid_from = $datum['valid_from'];
                $object->enable_flag = PrivilegeConstants::ENABLE_FLAG_FALSE;
                $object->create_time = date('Y-m-d H:i:s');
                $object->business_id = $business_id;
                $succeed = $object->save();
            }

        }

        // 清除缓存
        $clientPrivilegeService = new \common\library\privilege_v3\ClientPrivilegeService($client_id);
        $clientPrivilegeService->flushSystemCache();
        $clientPrivilegeService->recalculationClient();
        return $this->success();
    }

    /**
     * @throws Throwable
     * @throws \Predis\Connection\ConnectionException
     * @throws \Predis\Protocol\ProtocolException
     * @throws \Predis\Response\ServerException
     */
    public function actionResetDTCAccountPassword($client_id)
    {
        $this->validate([
            'client_id' => 'required|integer'
        ]);

        $upgradeService = new \common\library\dtc\UpgradeService($client_id);
        $upgradeService->upgrade();
        if ($upgradeService->isPass())
            return $this->success($upgradeService->getResult());

        return $this->fail(ErrorCode::CODE_FAIL, $upgradeService->getErrorLog());
    }

    /**
     * 回收公司edm数
     *
     * @param $client_id
     * @param $user_id
     * @return false|string
     * @throws Throwable
     */
    public function actionRecycleEdmCount($client_id, $user_id = '')
    {
        $this->validate([
            'client_id' => 'required|int'
        ]);

        // 设置登录态
        $adminUserId = PrivilegeService::getInstance($client_id)->getAdminUserId();
        User::setLoginUserById($adminUserId);

        $userList = new \common\library\account\UserList();
        $userList->setClientId($client_id);

        if (!empty($user_id))
        {
            $userIds = explode(',', $user_id);
            $userList->setUserIds($userIds);
        }

        $userIdList = $userList->findUserId();

        foreach ($userIdList as $userId) {

            $userInfo = new \common\library\account\UserInfo($userId);

            //回收公司营销数
            $currentCompanyCount = $userInfo->current_count_company;
            if ($currentCompanyCount)
            {
                $userInfo->freeEdmCount($currentCompanyCount, \common\library\edm\record\EdmRecord::EDM_RECORD_DELETE_USER_RETURN);

                \LogUtil::info("user_id : {$userInfo->user_id} 回收edmCount:{$currentCompanyCount} ");
            }

        }

        return $this->success(['client_id' => $client_id, 'recycle_user_ids' => $userIdList]);

    }

    /**
     * 更新k币数
     * 标准k币 在模块初始化时 cover 更新 强限制操作类型
     * 增购k币 存在cover｜add 操作
     * @param $client_id
     * @param $operate
     * @param $desc
     * @param $k_coin_count
     * @param $type
     * @return false|string
     */
    public function actionUpdateKCoin($client_id, $desc, $k_coin_count, $type)
    {
        $this->validate([
            'client_id' => 'required',
            'desc' => 'required',
            'k_coin_count' => 'required',
            'type' => 'required|in:1,2,3'
        ]);

        \LogUtil::info(__FUNCTION__ . "params : {$client_id} {$desc} {$k_coin_count} {$type}");

        User::setLoginUserById(PrivilegeService::getInstance($client_id)->getAdminUserId());

        \common\library\ai_agent\billing\Helper::processBilling($client_id, $type, 0, $k_coin_count);

        return $this->success($client_id);
    }

    /**
     * 清空已使用标准k币数
     * @param $client_id
     * @param $desc
     * @return false|string
     */
    public function actionClearUsedStandardKCoin($client_id, $desc)
    {
        $this->validate([
            'client_id' => 'required'
        ]);

        \LogUtil::info(__FUNCTION__ . "params : {$client_id} {$desc}");

        $externalKeys = [
            \common\library\account\Client::EXTERNAL_KEY_K_COIN_STANDARD_TOTAL_AMOUNT,
            \common\library\account\Client::EXTERNAL_KEY_K_COIN_STANDARD_AVAILABLE_AMOUNT,
        ];

        $client = \common\library\account\Client::getClient($client_id);
        $externalDataList = $client->getExtentAttributes($externalKeys);

        \LogUtil::info(__FUNCTION__ . " client_id {$client_id} standard_k_coin {$externalDataList[\common\library\account\Client::EXTERNAL_KEY_K_COIN_STANDARD_AVAILABLE_AMOUNT]} and now reset to {$externalDataList[\common\library\account\Client::EXTERNAL_KEY_K_COIN_STANDARD_TOTAL_AMOUNT]}");
        $client->setExtentAttributes([\common\library\account\Client::EXTERNAL_KEY_K_COIN_STANDARD_AVAILABLE_AMOUNT => $externalDataList[\common\library\account\Client::EXTERNAL_KEY_K_COIN_STANDARD_TOTAL_AMOUNT]]);
        $client->saveExtentAttributes();

        // 升级场景会需要触发重新分配逻辑
        User::setLoginUserById(PrivilegeService::getInstance($client_id)->getAdminUserId());
        \common\library\ai_agent\billing\Helper::reallocationKCoin($client_id, \AiBillingRecord::BILLING_TYPE_PACKAGE_PURCHASE);

        return $this->success($client_id);
    }

    public function actionFixShopGa($client_id, $site_id)
    {
        $this->validate([
            'client_id' => 'required|int',
            'site_id' => 'required|int'
        ]);
        $siteInfo = new Site($site_id);
        if ($siteInfo->isNew() || $siteInfo->client_id != $client_id) {
            throw new RuntimeException('site_id not match client_id');
        }
        $siteInfo = $siteInfo->getAttributes(['site_id','client_id', 'create_user','site_name']);
        $gaSite = new \common\library\google_ads\ga\GaSite($client_id);
        $gaSite->loadByCmsSiteId($site_id);
        if (!$gaSite->isNew()) {
            if (!empty($gaSite->property_id)) {
                throw new RuntimeException('ga property exists');
            }
            return \common\library\cms\site\SiteService::fixGaPropertyId($gaSite);
        }
        $siteDomain = CmsSiteDomain::model()->find('client_id = :client_id and site_id = :site_id', array(':client_id' => $client_id, ':site_id' => $site_id));
        $domain = $siteDomain->domain ?? '';
        if (empty($domain)) {
            throw new RuntimeException('site domain not exists');
        }
        $siteInfo['domain'] = $domain;
        \common\library\cms\site\SiteService::bindGaAccountWebSite($siteInfo);
        \common\library\cms\site\SiteService::recordSiteStatus($siteInfo);
    }

    public function actionInitAlimail($client_id, $order_time=12)
    {
        LogUtil::info("InitAlimail, client: $client_id, order_time: $order_time");

        $newOrderTime = 0;
        if ($order_time < 12) {
            return $this->fail(ErrorCode::CODE_FAIL, "时长不能小于 12 月");
        }
        else if ($order_time >= 12 && $order_time < 24) {
            $newOrderTime = 12;
        }
        else if ($order_time >= 24) {
            $newOrderTime = 24;
        }

        $key = 'ali_mail_flag';
        $client = \common\library\account\Client::getClient($client_id);
        $flag = $client->getExtentAttributes([$key])[$key] ?? \common\library\alimail\AliMailOrganizationObj::ALI_MAIL_FLAG_NO_PERMISSION;
        if ($flag == \common\library\alimail\AliMailOrganizationObj::ALI_MAIL_FLAG_NO_PERMISSION) {
            $client->setExtentAttributes([$key => \common\library\alimail\AliMailOrganizationObj::ALI_MAIL_FLAG_NOT_INITIALIZED]);
            $client->saveExtentAttributes();
        }

        // 创建阿里组织
        $m = AliMailOrganizationObj::checkAliMailFlag($client_id, AliMailOrganizationObj::ALI_MAIL_FLAG_NOT_INITIALIZED);
        if ($m == '') {
            // 生成虚假域名
            $prefix = $client->client_type != 1 ? 'dev' : 'prod';
            $domain = 'fake' . $prefix . substr(md5($client_id),0,46) . '.com';
            LogUtil::info("random domain:" . $domain);

            // 调接口创建, 新购
            $accountNum = 5;
            $resCreate = AliyunAlimailApi::client($client->client_type != 1)->createInstance($domain, $accountNum, $newOrderTime);
            LogUtil::info("AliyunAlimailApi createInstance result:" . json_encode($resCreate, JSON_PRETTY_PRINT));
            if (!$resCreate) {
                return $this->fail(ErrorCode::CODE_FAIL, "组织创建失败");
            }

            $instanceId = $resCreate['Data']['InstanceId'] ?? "";
            $orderId = $resCreate['Data']['OrderId'] ?? "";

            // 创建以后信息保存到 db
            $startTime = date('Y-m-d H:i:s');
            $endTime = date('Y-m-d H:i:s', strtotime("+$newOrderTime months"));

            $o = new AliMailOrganizationObj($client_id);
            $o->client_id = $client_id;
            $o->instance_id = $instanceId;
            $o->domain = $domain;
            $o->order_id = $orderId;
            $o->order_time = $newOrderTime;
            $o->purchase_time = $order_time;
            $o->account_num = $accountNum;
            $o->detect_status = AliMailOrganization::DETECT_STATUS_ALI_MAIL_INITIALING;
            $o->start_time = $startTime;
            $o->end_time = $endTime;
            $o->create_time = date('Y-m-d H:i:s');
            $res = $o->save();
            if (!$res) {
                LogUtil::error("AliMailOrganization db 创建失败, client: $client_id, instanceId: $instanceId, orderId: $orderId");
                return $this->fail(ErrorCode::CODE_FAIL, '组织创建失败 1');
            }

            $client = \common\library\account\Client::getClient($client_id);
            $client->setExtentAttributes([\common\library\account\Client::EXTERNAL_KEY_ALI_MAIL_FLAG => AliMailOrganizationObj::ALI_MAIL_FLAG_DEFAULT_INITIALIZED_HIDDEN]);
            $client->saveExtentAttributes();

            try {
                $innerApi = new \common\library\api\InnerApi('oss_api');
                $innerApi->setAccessLog(true);
                $response = $innerApi->call('notifyAlimailRegister', ['client_id' => $client_id,]);
            } catch (\Exception $e) {
                \LogUtil::error("OS内部服务异常 interface[notifyAlimailRegister] clientId[{$client_id}] msg[{$e->getMessage()} trace[{$e->getTraceAsString()}]");
            }
        }

        return $this->success(['client_id' => $client->client_id, 'succeed' => true]);
    }

    /**
     * 更新Whatsapp Cloud 营销号数量和沟通号数量
     * @param $client_id
     * @param $marketing_count //营销号
     * @param $normal_count //沟通号
     * @param $contract_id //os的 op_订单号
     * @param $desc
     * @return false|string
     */
    public function actionUpdateWhatsappCloudCount($client_id, $marketing_count = 0, $normal_count = 0, $contract_id = '', $desc = '')
    {
        $this->validate([
            'client_id' => 'required'
        ]);

        LogUtil::info("UpdateWhatsappCloudCount client_id: {$client_id} ,marketing_count:{$marketing_count} ,normal_count:{$normal_count} ,contract_id:{$contract_id} ,desc:{$desc}");

        $client = \common\library\account\Client::getClient($client_id);

        $client->setExtentAttributes([
            \common\library\account\Client::EXTERNAL_KEY_WHATSAPP_CLOUD_MARKETING_ACCOUNT_LIMIT => $marketing_count,
        ]);
        $client->saveExtentAttributes();

        $client->setExtentAttributes([
            \common\library\account\Client::EXTERNAL_KEY_WHATSAPP_CLOUD_NORMAL_ACCOUNT_LIMIT => $normal_count,
        ]);
        $client->saveExtentAttributes();

        return $this->success($client_id);
    }

}
