<?php
/**
 * User: wuyiwai
 * Date: 2018/9/19
 */

return [
/*    [
        'name' => 'system-config',
        'icon' => 'el-icon-star-on',
        'title' => '线上配置',
        'children' =>[
            [
                'name' => 'page-lua-config',
                'title' => 'LUA配置',
                'path' => '/prometheus/nginx/pageLuaConfig',
                'is_hidden' => 1,
            ],
            [
                'name' => 'page-lua-config',
                'title' => '灰度配置V2',
                'path' => '/prometheus/nginxSetting/pageLuaConfig',
            ],
            [
                'name' => 'envoy-rate-limit',
                'title' => 'Envoy网关限流',
                'path' => '/prometheus/nginxSetting/pageRateLimit',
            ],
            [
                'name' => 'db-config',
                'title' => '数据库配置',
                'path' => '/prometheus/db/index',
            ],
            [
                'name' => 'dc-config',
                'title' => '数据中心配置',
                'path' => '/prometheus/dc/index',
            ],
            [
                'name' => 'page-crontab-config',
                'title' => 'crontab',
                'path' => '/prometheus/crontab/index',
            ],
            [
                'name' => 'page-fpm',
                'title' => 'FPM',
                'path' => '/prometheus/fpm/pageFpm',
            ],
            [
                'name' => 'ahas-config',
                'title' => '熔断限流',
                'path' => '/prometheus/ahas/index',
            ],
            [
                'name' => 'config-center',
                'title' => '配置中心',
                'path' => '/prometheus/configCenter/index',
            ],
            [
                'name' => 'config-center',
                'title' => '配置中心-编辑组',
                'path' => '/prometheus/configCenter/editGroup',
                'is_hidden' => 1,
            ],
            [
                'name' => 'config-center',
                'title' => '配置中心-配置编辑',
                'path' => '/prometheus/configCenter/editData',
                'is_hidden' => 1,
            ],
            [
                'name' => 'config-center',
                'title' => '配置中心-配置编辑',
                'path' => '/prometheus/configCenter/dataItemManager',
                'is_hidden' => 1,
            ],
            [
                'name' => 'config-center',
                'title' => '配置中心-订阅编辑',
                'path' => '/prometheus/configCenter/editAppConfig',
                'is_hidden' => 1,
            ],
        ]
    ],
    [
        'name' => 'iteration',
        'icon' => 'el-icon-suitcase-1',
        'title' => '发布上线',
        'children' =>[
            [
                'name' => 'iteration-index',
                'title' => '迭代发布',
                'path' => '/prometheus/release/index',
            ],
            [
                'name' => 'release-event',
                'title' => '发布事件',
                'path' => '/prometheus/releaseEvent/index',
            ],
            [
                'name' => 'iteration-release-plan',
                'title' => '迭代发布计划',
                'path' => '/prometheus/release/releaseInfo',
                'is_hidden' => 1,
            ],
            [
                'name' => 'script-index',
                'title' => '上线脚本',
                'path' => '/prometheus/script/index',
            ],
            [
                'name' => 'db_change',
                'title' => 'DB变更',
                'path' => '/prometheus/dbChange/index',
            ],
            [
                'name' => 'script-info',
                'title' => '迭代详情',
                'path' => '/prometheus/script/info',
                'is_hidden' => 1,
            ],
            [
                'name' => 'batch-info',
                'title' => '批次详情',
                'path' => '/prometheus/script/executionDetails',
                'is_hidden' => 1,
            ],
            [
                'name' => 'gitlab_manage',
                'title' => 'gitlab管理',
                'path' => '/prometheus/gitlabManage/index',
            ],
        ]
    ],*/
    [
        'title' => '账号查询',
        'name' => 'account',
        'icon' => 'el-icon-zoom-in',
        'children' =>[
            [
                'name' => 'account-index',
                'title' => '用户查询',
                'path' => '/prometheus/account/index',
            ],
            [
                'name' => 'account-limit',
                'title' => '修改客户上限',
                'path' => '/prometheus/account/limit',
            ],
            [
                'name' => 'account-mobile',
                'title' => '修改手机绑定',
                'path' => '/prometheus/account/mobile',
            ],
            [
                'name' => 'account-phone-code',
                'title' => '手机校验码查询',
                'path' => '/prometheus/phoneCode/index',
            ],
            [
                'name' => 'account-login-event',
                'title' => '登录记录查询',
                'path' => '/prometheus/account/loginEvent',
            ],
            [
                'name' => 'risk-detail',
                'title' => '登录交叉查询',
                'path' => '/prometheus/risk/detailView',
            ],
            [
                'name' => 'account-password-log',
                'title' => '密码记录查询',
                'path' => '/prometheus/account/passwordLog',
            ],
            [
                'name' => 'account-task-index',
                'title' => '导入任务查询',
                'path' => '/prometheus/account/task'
            ],
            [
                'name' => 'disk-space',
                'title' => '云空间配置',
                'path' => '/prometheus/account/diskSpace'
            ],
            [
                'name' => 'account-manage',
                'title' => '登录管理',
                'path' => '/prometheus/account/loginMangeView',
            ],
            [
                'name' => 'account-sns',
                'title' => '社交账号聊天',
                'path' => '/prometheus/account/sns',
            ],
            [
                'name'  => 'account-client',
                'title' => '购买版本查询',
                'path'  => '/prometheus/account/client',
            ],
            [
                'name'  => 'account-client-exposeLimit',
                'title' => '公司群发单显限制调整',
                'path'  => '/prometheus/account/clientExposeLimit',
            ],
            [
                'name'  => 'account-fTImport',
                'title' => 'FT迁移信息',
                'path'  => '/prometheus/account/fTImport',
            ],
            [
                'name'  => 'account-system-functional',
                'title' => '版本权限查询',
                'path'  => '/prometheus/account/systemFunctional',
            ],
            [
                'name' => 'test-account-manage',
                'title' => '测试账号管理',
                'path' => '/prometheus/account/testAccountManage',
            ]

        ]
    ],
    [
        'name' => 'super-password',
        'icon' => 'el-icon-view',
        'title' => '超级密码',
        'children' =>[
            [
                'name' => 'super-password',
                'title' => '超级密码生成',
                'path' => '/prometheus/superPassword/create',
            ],
            [
                'name' => 'super-password-list',
                'title' => '生成记录列表',
                'path' => '/prometheus/superPassword/list',
            ],
            [
                'name' => 'user-list',
                'title' => '超密用户列表',
                'path' => '/prometheus/superPassword/user',
            ],
        ]
    ],
    [
        'name' => 'log',
        'title' => '日志查询',
        'icon' => 'el-icon-document',
        'children' =>[
            [
                'name' => 'error-log-index',
                'title' => '错误日志',
                'path' => '/prometheus/errorLog/index',
            ],
            [
                'name' => 'error-log-info',
                'title' => '错误详情',
                'path' => '/prometheus/errorLog/info',
                'is_hidden' => 1,
            ],
            [
                'name' => 'error-notify-index',
                'title' => '错误通知',
                'path' => '/prometheus/errorNotify/index',
            ],
            [
                'name' => 'log-select',
                'title' => '日志查询',
                'path' => '/prometheus/log/index',
                'is_hidden' => 1,
            ],
            [
                'name' => 'operate-log-currency_rate',
                'title' => '自定义汇率操作日志',
                'path' => '/prometheus/operateLog/currencyRate',
            ],
            [
                'name' => 'aliyun-log-select',
                'title' => '查询阿里日志',
                'path' => '/prometheus/log/aliyunLog',
            ],
            [
                'name' => 'aliyun-trace',
                'title' => '链路追踪',
                'path' => '/prometheus/log/aliyunTrace',
            ],
            [
                'name' => 'aliyun-trace-all',
                'title' => '全栈可观测',
                'path' => '/prometheus/log/aliyunTraceAll',
            ],
            [
                'name' => 'operate-log-index',
                'title' => '操作日志',
                'path' => '/prometheus/operateLog/index',
            ],
        ]
    ],
    [
        'title' => '账号安全',
        'name' => 'security',
        'icon' => 'el-icon-lock',
        'children' => [
            [
                'name'  => 'security-device-manage',
                'title' => '账号设备管理',
                'path'  => '/prometheus/security/deviceManage'
            ],
            [
                'name'  => 'ali-login-service-data',
                'title' => '阿里登录风险识别数据',
                'path'  => '/prometheus/security/loginData'
            ]
        ]
    ],
    [
        'name' => 'workorder-tools',
        'icon' => 'el-icon-s-tools',
        'title' => '工单工具',
        'children' => [
            [
                'name' => 'tools-index',
                'title' => '快速定位工具',
                'path' => '/prometheus/tools/index',
            ]
        ]
    ],
    [
        'name' => 'mail',
        'title' => '邮件工具',
        'icon' => 'el-icon-message',
        'children' => [
            [
                'name' => 'mail-index',
                'title' => '邮件查询',
                'path' => '/prometheus/mail/index',
            ],
            [
                'name' => 'mail-fail',
                'title' => '发送失败邮件查询',
                'path' => '/prometheus/mail/failListView',
            ],
            [
                'name' => 'mail-revert',
                'title' => '邮件恢复',
                'path' => '/prometheus/mail/revertView',
            ],
            [
                'name' => 'mail-rebuild-index',
                'title' => '重建索引',
                'path' => '/prometheus/mail/rebuildIndexView',
            ],
            [
                'name' => 'mail-unread-server',
                'title' => '未读数服务',
                'path' => '/prometheus/mail/unreadServer',
            ],
            [
                'name' => 'mail-fix-data',
                'title' => '邮件卡片信息修复',
                'path' => '/prometheus/mail/fixIndexView',
            ],
            [
                'name' => 'user-mail-expose-service',
                'title' => '群发单显邮箱服务',
                'path' => '/prometheus/mail/userMailExposeService',
            ],
            [
                'name' => 'user-mail-black-list',
                'title' => '黑名单查询',
                'path' => '/prometheus/mail/mailAddressBlackListInfo',
            ],
            [
                'name' => 'mail-system-template-list',
                'title' => '邮件模板',
                'path' => '/prometheus/mail/mailSystemTemplate',
            ],
            [
                'name' => 'instance-transfer',
                'title' => '阿里云邮实例迁移',
                'path' => '/prometheus/mail/instanceTransfer',
            ],
            [
                'name' => 'mail-rule-list',
                'title' => '邮件收发件规则',
                'path' => '/prometheus/mail/mailRuleList',
            ],
            [
                'name' => 'mail-email-relation',
                'title' => '客户邮件工单处理',
                'path' => '/prometheus/mail/mailEmailRelation',
            ],
            [
                'name' => 'mail-assistant',
                'title' => '邮件详情 & 下载',
                'path' => '/prometheus/mailAssistant/mailDetail',
            ],
            [
                'name' => 'mail-assistant',
                'title' => '文件夹邮件概况',
                'path' => '/prometheus/mailAssistant/mailFolder',
            ],
            [
                'name' => 'mail-assistant',
                'title' => '邮箱配置信息',
                'path' => '/prometheus/mailAssistant/mailConfig',
            ],
            [
                'name' => 'mail-assistant',
                'title' => '一键智能诊断',
                'path' => '/prometheus/mailAssistant/mailDoctor',
            ],
            [
                'name' => 'mail-assistant',
                'title' => '是否能正常登陆',
                'path' => '/prometheus/mailAssistant/checkLogin',
            ],
            [
                'name' => 'mail-assistant',
                'title' => '当前的收件间隔',
                'path' => '/prometheus/mailAssistant/retrInterval',
            ],
            [
                'name' => 'mail-assistant',
                'title' => '收件信息概要',
                'path' => '/prometheus/mailAssistant/retrSummary',
            ],
            [
                'name' => 'mail-assistant',
                'title' => '邮箱解绑记录',
                'path' => '/prometheus/mailAssistant/mailTransfer',
            ],
            [
                'name' => 'mail-assistant',
                'title' => 'SPF诊断报告',
                'path' => '/prometheus/mailAssistant/mailSpfDoctor',
            ],
            [
                'name' => 'mail-assistant',
                'title' => '小满代理IP查询',
                'path' => '/prometheus/mailAssistant/mailProxyIp',
            ],
            [
                'name' => 'mail-assistant',
                'title' => '邮箱代理IP查询',
                'path' => '/prometheus/mailAssistant/mailProxyUserMail',
            ],
            [
                'name' => 'mail-assistant',
                'title' => '邮箱特殊代理规则',
                'path' => '/prometheus/mailAssistant/mailProxyRule',
            ],
            [
                'name' => 'mail-assistant',
                'title' => '小满整数转IP查询',
                'path' => '/prometheus/mailAssistant/mailProxyLongToIP',
            ],
            [
                'name' => 'mail-assistant',
                'title' => '小满邮箱代理IP白名单配置',
                'path' => '/prometheus/mailAssistant/mailProxyUserMailWhite',
            ],
            //工单组用不到，下线了
            /*[
                'name' => 'mail-assistant',
                'title' => '邮箱助手访问命令',
                'path' => '/prometheus/mailAssistant/telnetCmd',
            ],*/
            [
                'name' => 'mail-assistant',
                'title' => '邮件服务日志',
                'path' => '/prometheus/mailAssistant/mailLog',
            ],
            [
                'name' => 'mail-assistant',
                'title' => '企业邮箱工具',
                'path' => '/prometheus/mailAssistant/mailBoxDetail',
            ],[
                'name' => 'mail-assistant',
                'title' => '邮件工单百宝箱',
                'path' => '/prometheus/mailAssistant/ticketTool',
            ],
            [
                'name' => 'mail-assistant',
                'title' => '发件追踪域名切换',
                'path' => '/prometheus/mailAssistant/mailChangeTrackDomain',
            ]
        ]
    ],
    [
        'name' => 'edm',
        'title' => 'EDM工具',
        'icon' => 'el-icon-s-promotion',
        'children' => [
            [
                'name' => 'edm-panel',
                'title' => '基础信息',
                'path' => '/prometheus/edm/panel',
            ],
            [
                'name' => 'edm-record',
                'title' => '营销流水',
                'path' => '/prometheus/edm/record',
            ],
            [
                'name' => 'edm-sender',
                'title' => '第三方发件服务',
                'path' => '/prometheus/edm/sender',
            ],
            [
                'name' => 'edm-auto',
                'title' => '自动化营销',
                'path' => '/prometheus/edm/auto',
            ],
            [
                'name' => 'mx-type-list',
                'title' => 'MX 名单管理',
                'path' => '/prometheus/edm/mxtype',
            ],
            [
                'name' => 'server-query',
                'title' => '投递模拟查询',
                'path' => '/prometheus/edm/serverQuery',
            ],
            [
                'name' => 'edm-reward-record',
                'title' => 'edm奖励',
                'path' => '/prometheus/edm/rewardRecord',
            ],
            [
                'name' => 'edm-channel-setting',
                'title' => 'edm渠道设置',
                'path' => '/prometheus/edm/channelSetting',
            ],
            [
                'name' => 'edm-special-whitelist',
                'title' => 'edm特殊白名单管理',
                'path' => '/prometheus/edm/specialWhiteList',
            ],
            [
                'name' => 'edm-client-settings',
                'title' => 'client专属edm渠道配置',
                'path' => '/prometheus/edm/edmClientSettings',
            ],
            [
                'name' => 'edm-channel-statistics',
                'title' => 'edm渠道统计',
                'path' => '/prometheus/edm/channelStatistics',
            ],
            [
                'name' => 'edm-grey-setting',
                'title' => 'edm灰度设置',
                'path' => '/prometheus/edm/greySetting',
            ]
        ]
    ],
    [
        'name' => 'operator-tools',
        'icon' => 'el-icon-s-tools',
        'title' => '运营工具',
        'children' => [
            [
                'name' => 'user-guide',
                'title' => '新手任务数据导出',
                'path' => '/prometheus/dataExport/index',
            ],
            [
                'name' => 'risk-account',
                'title' => '高风险账号',
                'path' => '/prometheus/risk/accountView',
            ],
            [
                'name' => 'operating-mail',
                'title' => '运营邮件发送',
                'path' => '/prometheus/operatingMail/index'
            ],
            [
                'name' => 'account-leads-account',
                'title' => 'leads独立版多账号查询',
                'path' => '/prometheus/account/leadsAccountList',
            ],
            [
                'name' => 'account-leads-account-user',
                'title' => 'leads独立版多账号用户',
                'path' => '/prometheus/account/LeadsAccountUser',
            ],
            [
                'name' => 'upgrade-list',
                'title' => '客户运营升级名单',
                'path' => '/prometheus/account/getUpgradeList',
            ],
            [
                'name' => 'holiday-index',
                'title' => '节日管理',
                'path' => '/prometheus/holiday/index',
            ],
            [
                'name' => 'ticket-online',
                'title' => '工单管理',
                'path' => '/prometheus/ticket/index'
            ],
        ]
    ],
    [
        'name' => 'privilege-open-tools',
        'icon' => 'el-icon-s-goods',
        'title' => '功能开放',
        'children' => [
            [
                'name' => 'tms-open',
                'title' => 'TMS权限开通',
                'path' => '/prometheus/tms/index',
            ],
            [
                'name' => 'customer-open',
                'title' => '客户权限开通',
                'path' => '/prometheus/customer/open',
            ],
            [
                'name' => 'custom-statistic-report-functional-open',
                'title' => '自定义报表功能开放',
                'path' => '/prometheus/privilegeOpen/addFunctionalCustomStatisticReportIndex',
            ],
            [
                'name' => 'trial-management',
                'title' => 'OMS试用权限管理',
                'path' => '/prometheus/oms/index',
            ],
            [
                'name' => 'oms-module-index',
                'title' => 'OMS权限管理',
                'path' => '/prometheus/oms/omsModuleIndex',
            ],
            [
                'name' => 'erp_service_open',
                'title' => 'erp服务开放',
                'path' => '/prometheus/privilegeOpen/erpServiceOpenIndex',
            ],
        ],
    ],
    [
        'path' => 'client',
        'name' => 'client',
        'title' => '客户工具',
        'icon' => 'el-icon-news',
        'children' => [
            [
                'name' => 'client-move-to-public',
                'title' => '移入公海名单列表',
                'path' => '/prometheus/client/moveToPublic',
            ]
        ]
    ],
    [
        'name' => 'Workbench-tools',
        'title' => '流程规则管理工具',
        'icon' => 'el-icon-key',
        'children' => [
            [
                'name' => 'workflow-index',
                'title' => '任务/工作流工具',
                'path' => '/prometheus/workflow/Index',
            ],
            [
                'name' => 'task-info',
                'title' => '任务信息查询-修正',
                'path' => '/prometheus/task/info',
            ],
            [
                'name' => 'todoFeed-todoFeed',
                'title' => '待办事件',
                'path' => '/prometheus/todoFeed/todoFeed',
            ],
        ]
    ],
    [
        'name' => 'statics-tools',
        'title' => '统计分析',
        'icon' => 'el-icon-pie-chart',
        'children' => [
            [
                'name' => 'performance-index',
                'title' => '绩效模块',
                'path' => '/prometheus/statisticReport/performance',
            ],
            [
                'name' => 'statistic-report-push',
                'title' => '订阅推送',
                'path' => '/prometheus/statisticReport/push',
            ],
            [
                'name' => 'statistic-report-rules',
                'title' => '统计分析规则查看',
                'path' => '/prometheus/statisticReport/rules',
            ],
            [
                'name' => 'insight-list-control',
                'title' => '统计分析Insight',
                'path' => '/prometheus/statisticReport/insightList',
            ],
            [
                'name' => 'insight-assistant',
                'title' => '统计分析AI助手',
                'path' => '/prometheus/statisticReport/insightAssistant',
            ],
            [
                'name' => 'report_generation',
                'title' => '统计分析AI报表生成',
                'path' => '/prometheus/statisticReport/ReportGeneration',
            ],
//            [
//                'name' => 'statistic-team-wall-cache',
//                'title' => '团队墙缓存清除',
//                'path' => '/prometheus/statisticReport/teamWallCache',
//            ],
        ]
    ],
    [
        'name' => 'performance',
        'title' => '绩效',
        'icon' => 'el-icon-data-line',
        'children' => [
            [
                'name' => 'limit-adjust',
                'title' => '绩效规则上限调整',
                'path' => '/prometheus/performance/limitAdjust',
            ]
        ]
    ],
    [
        'name' => 'ai',
        'title' => '自动化',
        'icon' => 'el-icon-magic-stick',
        'children' => [
            [
                'name' => 'ai-classify',
                'title' => '自动化查询',
                'path' => '/prometheus/ai/classify',
            ],
            [
                'name' => 'ai-customer',
                'title' => '自动建档模拟',
                'path' => '/prometheus/ai/customer',
            ],
            [
                'name' => 'ai-opportunity',
                'title' => '商机自动化',
                'path' => '/prometheus/ai/opportunity',
            ],
            [
                'name' => 'ai-lead',
                'title' => '线索自动化',
                'path' => '/prometheus/ai/lead',
            ],
            [
                'name' => 'ai-mailrule',
                'title' => '非业务邮件识别规则配置',
                'path' => '/prometheus/ai/mailRules',
            ],
            [
                'name' => 'ai-advice',
                'title' => '建档建议重跑任务',
                'path' => '/prometheus/ai/advice',
            ]
        ]
    ],
    [
        'name' => 'ai-service',
        'title' => 'OKKI AI',
        'icon' => 'el-icon-monitor',
        'children' => [
            [
                'name' => 'GPT-DEMO',
                'title' => 'GPT-DEMO',
                'path' => '/prometheus/aiService/GPTDemo',
            ],
            [
                'name' => 'ai-agent-cost',
                'title' => 'Ai成本评估',
                'path' => '/prometheus/aiService/aiAgentCost',
            ],
            [
                'name' => 'ai-billing',
                'title' => 'Ai用户花费',
                'path' => '/prometheus/aiService/aiBilling',
            ],
            [
                'name' => 'ai-version',
                'title' => 'Ai版本相关',
                'path' => '/prometheus/aiService/aiVersion',
            ],
            [
                'name' => 'ai-statistics',
                'title' => 'Ai数据统计',
                'path' => '/prometheus/aiService/dataStatistics',
            ],
            [
                'name' => 'ai-statistics',
                'title' => 'Ai-connector看板',
                'path' => '/prometheus/aiService/connectorStatistics',
            ],
            [
                'name' => 'ai-statistics',
                'title' => 'Ai-connector模型管理',
                'path' => '/prometheus/aiConnector/modelManagement',
            ],
            [
                'name' => 'ai-function',
                'title' => 'Ai功能相关',
                'path' => '/prometheus/aiService/aiFunction',
            ],
            [
                'name' => 'ai-backtrace',
                'title' => 'AI调用记录回溯',
                'path' => '/prometheus/aiService/aiBacktrace',
            ],
            [
                'name' => 'ai-risk',
                'title' => 'Ai风控相关',
                'path' => '/prometheus/aiService/aiRisk',
            ],
            [
                'name' => 'auto-evaluation',
                'title' => '自动化评测',
                'path' => '/prometheus/aiService/autoEvaluation',
            ],
            [
                'name' => 'process-bad-case',
                'title' => 'Bad Case',
                'path' => '/prometheus/aiService/processBadCase',
            ],
            [
                'name' => 'ai-time-cost',
                'title' => 'Ai耗时统计',
                'path' => '/prometheus/aiService/aiTimeCost',
            ],
            [
                'name' => 'company-qc-white-list',
                'title' => '开通客户谈单检测白名单',
                'path' => '/prometheus/aiService/qualityCheckWhiteList',
            ],
            [
                'name' => 'func-list',
                'title' => '功能名单相关',
                'path' => '/prometheus/aiService/funcList',
            ],
            [
                'name' => 'warning-setting',
                'title' => 'AI告警配置',
                'path' => '/prometheus/aiService/warningSetting',
            ],
            [
                'name' => 'ai-workbench-white-list',
                'title' => 'AI工作台白名单',
                'path' => '/prometheus/aiService/aiWorkbenchWhiteList',
            ],
        ]
    ],
    [
        'name' => 'ai-sdr-manage',
        'title' => 'AI SDR',
        'icon' => 'el-icon-monitor',
        'children' => [
            [
                'name' => 'ai-sdr',
                'title' => 'AI SDR管理',
                'path' => '/prometheus/aiSdr/Sdr',
            ],
        ]

    ],
    [
        'name' => 'discovery',
        'title' => 'Discovery',
        'icon' => 'el-icon-news',
        'children' => [
            [
                'name' => 'risk-account-list',
                'title' => '风险账号列表',
                'path' => '/prometheus/riskAccount/index',
            ],
            [
                'name' => 'access-statistics',
                'title' => '访问日志统计',
                'path' => '/prometheus/dx/accessStat',
            ],
            [
                'name' => 'company-set',
                'title' => '公司订正',
                'path' => '/prometheus/dx/company',
            ],
            [
                'name' => 'contacts-set',
                'title' => '联系人订正',
                'path' => '/prometheus/dx/contacts',
            ],
            [
                'name' => 'logger-set',
                'title' => '订正日志',
                'path' => '/prometheus/dx/logger',
            ]
        ]
    ],
    [
        "name" => 'social-media',
        "title" => "社媒/建站工具",
        'icon' => 'el-icon-postcard',
        'children' => [
            [
                'name' => 'tms-facebook-page',
                'title' => 'FB公共主页管理',
                'path' => '/prometheus/facebookPage/index',
            ],
            [
                'name' => 'track-sclid',
                'title' => '网站追踪',
                'path' => '/prometheus/siteTrack/indexView',
            ],
            [
                'name' => 'ga-config',
                'title' => 'ga账号配置',
                'path' => '/prometheus/siteTrack/gaConfig',
            ],
            [
                'name' => 'mcc-config',
                'title' => 'mcc账号配置',
                'path' => '/prometheus/siteTrack/mccConfigView'
            ],
            [
                'name' => 'bind-ads-mcc',
                'title' => 'ads绑定mcc账号',
                'path' => '/prometheus/siteTrack/bindAdsToMccView'
            ],
            [
                'name' => 'sync-dispatcher',
                'title' => '同步调度日志',
                'path' => '/prometheus/siteTrack/syncDispatcherView',
            ],
            [
                'name' => 'cms-site',
                'title' => '网站管理',
                'path' => '/prometheus/siteTrack/cmsView',
            ],
            [
                'name' => 'selectors-setting',
                'title' => '社媒代码配置',
                'path' => '/prometheus/socialMedia/index',
            ],
        ],
    ],
    [
        'name' => 'ames-tools',
        'title' => 'AMES工具',
        'icon' => 'el-icon-box',
        'children' => [
            [
                'name' => 'ames-open',
                'title' => 'ames权限开通',
                'path' => '/prometheus/ames/privilege',
            ],
            [
                'name' => 'ames-client-type',
                'title' => '企业打标/基本版本权限',
                'path' => '/prometheus/ames/clientType',
            ],
            [
                'name' => 'ames-open-tool',
                'title' => '断约客户授权ames',
                'path' => '/prometheus/ames/openAmesIndex',
            ],
            [
                'name' => 'init-client',
                'title' => '内部测试公司变更client版本',
                'path' => '/prometheus/client/InitClient',
            ],
            [
                'name' => 'update-client-type',
                'title' => '更新clientType',
                'path' => '/prometheus/client/updateClientTypeIndex',
            ],
        ]
    ],
    [
        'name' => 'queue',
        'icon' => 'el-icon-c-scale-to-original',
        'title' => '队列服务',
        'children' => [
            [
                'name' => 'queue-chart',
                'title' => '消息监控',
                'path' => '/prometheus/queue/index',
            ],
            [
                'name' => 'search-queue-stat',
                'title' => '搜索队列',
                'path' => '/prometheus/queue/queueView'
            ],
            [
                'name' => 'aliyun-queue-select',
                'title' => 'RocketMQ',
                'path' => '/prometheus/queue/aliyunQueue',
            ],
            [
                'name' => 'aliyun-queue-status',
                'title' => 'RocketMQ状态',
                'path' => '/prometheus/queue/aliyunQueueStatus',
            ],
            [
                'name' => 'wecom-queue-stat',
                'title' => '企微消息同步消费者',
                'path' => '/prometheus/queue/wecomQueueStatus'
            ]
        ]
    ],
    [
        'name' => 'alibaba',
        'icon' => 'el-icon-shopping-cart-full',
        'title' => '阿里对接',
        'children' => [
            [
                'name' => 'queue-stat',
                'title' => '队列概况',
                'path' => '/prometheus/alibaba/queueView',
            ],
            [
                'name' => 'store-manage',
                'title' => '店铺查询',
                'path' => '/prometheus/alibaba/index',
            ],
            [
                'name' => 'ali-account-manage',
                'title' => '账号查询',
                'path' => '/prometheus/alibaba/acount',
            ],
            [
                'name' => 'sync-task',
                'title' => '同步任务查询',
                'path' => '/prometheus/alibaba/syncTask',
            ],
            [
                'name' => 'company-info',
                'title' => '客户查询',
                'path' => '/prometheus/alibaba/companyInfo',
            ],
            [
                'name' => 'manual-handle-message',
                'title' => '手动处理消息',
                'path' => '/prometheus/alibaba/manualHandleMessage',
            ],
            [
                'name' => 'alibaba-user-logs',
                'title' => '用户配置修改记录',
                'path' => '/prometheus/alibaba/userLogs',
            ],
            [
                'name' => 'buyer-info',
                'title' => '买家信息查询',
                'path' => '/prometheus/alibaba/buyerInfo',
            ],
            [
                'name' => 'ali-message',
                'title' => '阿里消息',
                'path' => '/prometheus/alibaba/message',
            ],
            [
                'name' => 'alibaba-api',
                'title' => '阿里接口',
                'path' => '/prometheus/alibaba/alibabaApi',
            ],
            [
                'name' => 'visitor-marketing-status',
                'title' => '潜客运营状态查询',
                'path' => '/prometheus/alibaba/visitorMarketingStatus',
            ],
        ]
    ],
    [
        'name' => 'assistant',
        'icon' => 'el-icon-thumb',
        'title' => '销售助手',
        'children' => [
            [
                'name' => 'assistant-control',
                'title' => '插件控制',
                'path' => '/prometheus/assistant/control',
            ],
            [
                'name' => 'assistant-index',
                'title' => '展示逻辑',
                'path' => '/prometheus/assistant/index',
            ],
            [
                'name' => 'assistant-account-route',
                'title' => '账号环境配置',
                'path' => '/prometheus/assistant/accountRoute',
            ],
            [
                'name' => 'tms-speechcraft',
                'title' => '成单话术管理',
                'path' => '/prometheus/speechcraft/index',
            ],
            [
                'name' => 'trade-document',
                'title' => '外贸文档管理',
                'path' => '/prometheus/tradeDocument/index',
            ],
            [
                'name' => 'tms-suggestion',
                'title' => '成单建议管理',
                'path' => '/prometheus/tmsTipsSuggestion/index'
            ],
            [
                'name' => 'wecom-management',
                'title' => '企微对接',
                'path' => '/prometheus/wecom/management',
            ],
        ],
    ],
    [
        'name' => 'okki-io',
        'icon' => 'el-icon-thumb',
        'title' => 'OKKI-IO',
        'children' => [
            [
                'name' => 'io-management',
                'title' => 'io管理',
                'path' => '/prometheus/okkiIo/index',
            ],
            [
                'name' => 'io-privilege',
                'title' => '权益发放',
                'path' => '/prometheus/okkiIo/privilege'
            ]
        ],
    ],
    [
        'name' => 'okki-leads',
        'icon' => 'el-icon-thumb',
        'title' => 'OKKI-LEADS',
        'children' => [
            [
                'name' => 'recommend-square-user-config',
                'title' => '推荐广场用户配置',
                'path' => '/prometheus/okkiLeads/index',
            ],
            [
                'name' => 'b2b-inquiry',
                'title' => 'B2B询盘',
                'path' => '/prometheus/okkiLeads/inquiry',
            ]
        ],
    ],
    [
        'name' => 'whatsapp-cloud',
        'icon' => 'el-icon-thumb',
        'title' => 'whatsapp云端',
        'children' => [
            [
                'name' => 'whatsapp-cloud',
                'title' => '云端可观测',
                'path' => '/prometheus/whatsappCloud/index',
            ]
        ],
    ],
    [
        'name' => 'dev-tools',
        'icon' => 'el-icon-cold-drink',
        'title' => '开发工具',
        'children' =>[
            [
                'name' => 'privilegeConfig-index',
                'title' => '系统权限列表',
                'path' => '/prometheus/privilegeConfig/Index',
            ],
            [
                'name' => 'page-queue-crontab-config',
                'title' => '定时任务',
                'path' => '/prometheus/queueCrontab/index',
            ],
            [
                'name' => 'db-config',
                'title' => '数据库配置',
                'path' => '/prometheus/db/index',
            ],
            [
                'name' => 'db-select-index',
                'title' => '数据库查询',
                'path' => '/prometheus/dbSelect/index',
            ],
            [
                'name' => 'apifox-api',
                'title' => 'apifox接口查询',
                'path' => '/prometheus/apifox/api',
            ],
            [
                'name' => 'test-center-management',
                'title' => '智齿测试客服管理',
                'path' => '/prometheus/tapd/index',
            ],
            [
                'name' => 'sobot-management',
                'title' => '智齿客服管理',
                'path' => '/prometheus/sobot/index',
            ],
            [
                'name' => 'apifox-management',
                'title' => '自动化测试管理',
                'path' => '/prometheus/apifox/index',
            ],
            [
                'name' => 'oss-list',
                'title' => 'OSS资源管理',
                'path' => '/prometheus/oss/index',
            ],
            [
                'name' => 'template-list',
                'title' => '消息模板管理',
                'path' => '/prometheus/messageTemplate/index',
            ],
            [
                'name' => 'ahas-iframe',
                'title' => '阿里云AHAS管理',
                'path' => '/prometheus/ahas/index',
            ],
            [
                'name' => 'sentry-management',
                'title' => '前端sentry看板',
                'path' => '/prometheus/sentry/index'
            ],
            [
                'name' => 'tapd-plugin',
                'title' => 'TAPD插件授权',
                'path' => '/prometheus/tapdPlugin/index'
            ],
            [
                'name' => 'upload-file-grey-setting',
                'title' => '上传文件灰度设置',
                'path' => '/prometheus/uploadFile/greySetting',
            ],
            [
                'name' => 'time-consuming',
                'title' => '耗时测试',
                'path' => '/prometheus/timeConsuming/timeConsuming',
            ],
        ]
    ],
    [
        'name' => 'app-desktop-tools',
        'icon' => 'el-icon-setting',
        'title' => 'APP/Desktop工具',
        'children' => [
            [
                'name' => 'welcome-set',
                'title' => '应用欢迎页面',
                'path' => '/prometheus/carbon/welcome',
            ],
            [
                'name' => 'option-set',
                'title' => '选项配置',
                'path' => '/prometheus/carbon/option',
            ],
            [
                'name' => 'publish-set',
                'title' => '发布',
                'path' => '/prometheus/carbon/publish',
            ],
            [
                'name' => 'push-message',
                'title' => '客户端-推送消息',
                'path' => '/prometheus/carbon/pushMessage',
            ],
            [
                'name' => 'download-set',
                'title' => 'Download Center',
                'path' => '/prometheus/lantern/downloadCenter',
            ],
            [
                'name' => 'pb-visualization',
                'title' => 'PB接口',
                'path' => '/prometheus/pb/visualization',
            ],
        ]
    ],
    [
        'name' => 'oms-tools',
        'icon' => 'el-icon-s-shop',
        'title' => 'OMS工具',
        'children' => [
            [
                'name' => 'order-tools',
                'title' => '销售订单工具',
                'path' => '/prometheus/oms/orderTools',
            ],
            [
                'name' => 'ali-order-sync-tools',
                'title' => '阿里订单同步工具',
                'path' => '/prometheus/oms/alibabaSyncTools',
            ],
            [
                'name' => 'order-profit-tools',
                'title' => '订单毛利工具',
                'path' => '/prometheus/oms/orderProfitTools',
            ],
            [
                'name' => 'inquiry-collaboration',
                'title' => '询价系统放量工具',
                'path' => '/prometheus/oms/inquiryCollaboration',
            ]
        ],
    ],
    [
        'name' => 'openapi',
        'title' => '开放平台',
        'icon' => 'el-icon-odometer',
        'children' => [
            [
                'name' => 'rate-limit-config',
                'title' => '限流配置',
                'path' => '/prometheus/openapi/rateLimitConfig'
            ],
            [
                'name' => 'test-request',
                'title' => '请求接口',
                'path' => '/prometheus/openapi/testRequest'
            ],
            [
                'name' => 'qeasy-account',
                'title' => 'ERP查询',
                'path' => '/prometheus/openapi/qeasyAccount'
            ],
        ],
    ],
    [
        'name' => 'privilege-service-account',
        'icon' => 'el-icon-s-goods',
        'title' => '客服账号',
        'children' => [
            [
                'name' => 'service-account-index',
                'title' => '客服账号管理',
                'path' => '/prometheus/serviceAccount/index',
            ],
        ],
    ],
    [
        'name' => 'pgListener',
        'title' => 'PG日志同步监控',
        'icon' => 'el-icon-s-data',
        'children' => [
            [
                'name' => 'cpu-index',
                'title' => 'CPU监控',
                'path' => '/prometheus/pgListener/cpuIndex'
            ],
            [
                'name' => 'slot-index',
                'title' => 'PG复制槽查询/删除',
                'path' => '/prometheus/pgListener/getSlotIndex'
            ],
        ],
    ],
    [
        'name' => 'exp-tool',
        'title' => '体验系统工具',
        'icon' => 'el-icon-setting',
        'children' => [
            [
                'name' => 'exp-list',
                'title' => '列表',
                'path' => '/prometheus/exp/list'
            ]
        ],
    ],
    [
        'name' => 'dataWorks-setting',
        'title' => 'Dataworks',
        'icon' => 'el-icon-key',
        'children' => [
            [
                'name' => 'dataSource',
                'title' => '数据源设置',
                'path' => '/prometheus/dataworks/dataSource',
            ],
        ]
    ],
    [
        'name' => 'prometheus-setting',
        'title' => 'Prometheus设置',
        'icon' => 'el-icon-key',
        'children' => [
            [
                'name' => 'privilege-index',
                'title' => '权限管理',
                'path' => '/prometheus/privilege/Index',
            ],
        ]
    ],
];
