<?php
$template = <<<HTML
<el-tabs @tab-click="tabClick"  v-model="activeTab">

  
    <el-tab-pane  name="generate-data-preset-question" label="报表生成预先问题配置" style="margin-top: 15px">
        <div style="margin-top: 30px">
            <el-button type="primary" icon="el-icon-refresh" style="float: right" @click="presetQuestionList">刷新列表</el-button>
            <el-button type="primary" icon="el-icon-sort" style="float: right;margin-right: 30px" @click="generateOrderData">排序</el-button>
            <el-button type="primary" icon="el-icon-circle-plus-outline"  @click="addPresetQuestion()" style="margin-left: -10px">新增预设问题</el-button>
        </div>
        
        <el-dialog :visible="orderShow" @close="closeOrderShow" title="问题排序">
            <div style="display: flex; justify-content: center;">
                <template>
                  <el-transfer
                    filterable
                    filter-placeholder="请输入名称"
                    v-model="changeOrderData"
                    :titles="['问题列表', '排序列表']"
                    :data="orderData"
                    target-order="push"
                    >
                  </el-transfer>
                </template>
                
            </div>
            
            <div style="display: flex; justify-content: center;">
                <span style="color: red;font-size: 12px;margin-top: 20px">*注意：排序列表会按照选中顺序插入到首位，原本位置内容会向下移动</span>
            </div>
           
            
            
            <div slot="footer" style="margin-bottom: 20px">
              <el-row style="float: right;margin-right: 35%">
                <el-button plain @click="closeOrderShow">取消</el-button>
              </el-row>
              <el-row style="margin-right: 60%">
                <el-button plain  @click="saveOrder">保存</el-button>
              </el-row>
            </div>
        </el-dialog>
    
        <div style="margin-top: 30px;float: none">
            <template>
                <el-table
                  :data="presetQuestions"
                  border
                  style="width: 100%"
                  @sort-change="handleSortChange">
                  <el-table-column
                    fixed
                    align="center"
                    type="index"
                    label="序号"
                    width="50">
                  </el-table-column>
                  
                   <el-table-column
                    prop="ownerType"
                    label="查看范围"
                    sortable
                    align="center"
                    :formatter="ownerTypeFormatter"
                    >
                  </el-table-column>
                  
                  <el-table-column
                    prop="desc"
                    label="预先问题描述"
                    align="center"
                    sortable>
                  </el-table-column>
                  <el-table-column
                    prop="desc_ft"
                    label="预先问题描述(繁体)"
                    align="center"
                    sortable>
                  </el-table-column>
                  <el-table-column
                    prop="desc_en"
                    label="预先问题描述(英语)"
                    align="center"
                    sortable>
                  </el-table-column>
                   <el-table-column
                    prop="desc_ja"
                    label="预先问题描述(日语)"
                    align="center"
                    sortable>
                  </el-table-column>
                  <el-table-column
                    prop="desc_sp"
                    label="预先问题描述(西班牙语)"
                    align="center"
                    sortable>
                  </el-table-column>
                  
                  <el-table-column
                    prop="sql"
                    label="SQL"
                    sortable>
                    <template slot-scope="scope">
                        <el-tooltip placement="left" :enterable="true" effect="dark" transition="el-fade-in-linear" :open-delay="400">
                          <div slot="content" style="max-width: 500px; white-space: pre-wrap; overflow: auto;">
                            <pre v-html="content(scope.row.sql)"></pre>
                          </div>
                          <div style="overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 3;-webkit-box-orient: vertical;">
                            {{ scope.row.sql }}
                          </div>
                        </el-tooltip>
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop="greyClientIds"
                    label="灰度Client"
                    sortable
                    header-align="center"
                    >
                    <template slot-scope="scope">
                        <el-tooltip placement="left" :enterable="true" effect="dark" transition="el-fade-in-linear" :open-delay="400">
                          <div slot="content" style="max-width: 500px; white-space: pre-wrap; overflow: auto;">
                            <pre v-html="scope.row.greyClientIds"></pre>
                          </div>
                          <div style="overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 3;-webkit-box-orient: vertical;text-align: center;">
                            {{ scope.row.greyClientIds }}
                          </div>
                        </el-tooltip>
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop="clickCount"
                    label="访问次数"
                    sortable
                    align="center"
                    >
                  </el-table-column>
                  
                    <el-table-column
                    prop="tag"
                    label="标签"
                    sortable
                    align="center"
                    >
                  </el-table-column>
                  </el-table-column>          
                    <el-table-column
                    prop="success_rate"
                    label="回答成功率(%)"
                    sortable
                    align="center"
                    >
                  </el-table-column>
                   <el-table-column label="问题类型" sortable align="center">
                      <template slot-scope="scope">
                        <el-tag
                          v-for="tag in scope.row.showTag"
                          :key="tag"
                          size="small"
                          style="margin: 5px 0;"
                          :type="(showTagOption.find(option => option.value === tag) || {type: 'default'}).type">
                         {{ (showTagOption.find(option => option.value === tag) || {label: tag}).label }}
                        </el-tag>
                      </template>
                    </el-table-column>
                    
                  <el-table-column
                    label="操作"
                    align="center"
                    sortable>
                    <template slot-scope="scope">
                      <el-button @click="editPresetQuestion(scope.row)" type="text" size="small">编辑</el-button>                      
                      <el-popconfirm
                        confirm-button-text="确定"
                        cancel-button-text="取消"
                        title="确定要删除吗？"
                        @confirm="deletePresetQuestion(scope.row)"
                        @cancel="handleCancel">
                          <el-button slot="reference" type="text" size="small">删除</el-button>
                        </el-popconfirm>
                    </template>
                  </el-table-column>
                </el-table>
            </template>
        </div>
        
        <el-dialog :visible="presetAddShow" @close="closePresetQuestionAdd" width="50%">
        
            <el-form label-width="180px" label-position="left" :inline="true"  class="demo-form-inline">
        
        <div>
                <el-form-item label="* 问题描述：">
                    <label slot="label"><span style="color: red;font-weight: bold;">*</span> 问题描述：</label>
                    <el-input v-model="desc" style="width: 200%">
                </el-form-item>
                 </div>
                 
                 <div>
                 <el-form-item label="* 问题描述(繁体)：">
                    <label slot="label"><span style="color: red;font-weight: bold;">*</span> 问题描述(繁体)：</label>
                    <el-input v-model="descFT" style="width: 200%">
                </el-form-item>
                 </div>
                 
                  <div>
                 <el-form-item label="* 问题描述(英语)：">
                    <label slot="label"><span style="color: red;font-weight: bold;">*</span> 问题描述(英语)：</label>
                    <el-input v-model="descEN" style="width: 200%">
                </el-form-item>
                 </div>
                 
                  <div>
                 <el-form-item label="* 问题描述(日语)：">
                    <label slot="label"><span style="color: red;font-weight: bold;">*</span> 问题描述(日语)：</label>
                    <el-input v-model="descJA" style="width: 200%">
                </el-form-item>
                 </div>
                 
                  <div>
                 <el-form-item label="* 问题描述(西班牙语)：">
                    <label slot="label"><span style="color: red;font-weight: bold;">*</span> 问题描述(西班牙语)：</label>
                    <el-input v-model="descSP" style="width: 200%">
                </el-form-item>
                 </div>
                
                <div>
                  <div style="margin-bottom: 10px;font-size: 14px">
                      <label slot="label"><span style="color: red;font-weight: bold;"></span> Sql：</label>
                  </div>
                  <div>
                      <el-input type="textarea" :autosize="{ minRows: 3}" v-model="sql" style="width: 95%">
                      </el-input>
                  </div>
                </div>
                
              <div>
                <el-form-item
                  label="查看范围"
                  align="center"
                  style="margin-top: 20px"
                >
                
                     <el-select v-model="ownerType" placeholder="请选择">
                          <el-option label="所有人"    :value="0"></el-option>
                          <el-option label="管理者"    :value="1"></el-option>
                     </el-select>
                </el-form-item>
            </div>
            
            
           
         
           <el-form-item
                  label="标签选择"
                  align="center"
                  style="margin-top: 20px"
                >
                     <el-select v-model="tag" placeholder="请选择">
                          <el-option label="无" value=""></el-option>
                          <el-option label="new" value="new"></el-option>
                          <el-option label="hot" value="hot"></el-option>
                     </el-select>
            </el-form-item>
            
             <el-form-item
                  label="问题类型"
                  align="center"
                  style="margin-top: 20px"
                >
                 <el-select v-model="showTag" multiple placeholder="默认为通用类型" >
                    <el-option
                         v-for="item in showTagOption"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                       ></el-option>
                 </el-select>
            </el-form-item>
            
             <div></div>
                
                
                <el-form-item
                  label="灰度配置："
                  align="center"
                  style="margin-top: 20px">
                  <el-switch
                    :value="switchDslGrey"
                    active-color="#13ce66"
                    inactive-color="#ff4949"
                    @change="clickSwitchDslGrey()">
                  </el-switch>
                </el-form-item>
                
                <div></div>
                
                <template v-if="switchDslGrey">
                    <el-form-item label="* 灰度ClientIds：">
                        <label slot="label"><span style="color: red;font-weight: bold;">*</span> 灰度ClientIds：</label>
                        <el-input v-model="greyClientIds" style="width: 108%">
                        <span style="color: red;font-size: 12px;margin-top: -20px">*注意：灰度 clientIds 使用英文 "," 分隔</span>
                    </el-form-item>
                </template>
                
            </el-form>
            
            <div slot="footer" style="margin-bottom: 20px">
              <el-row style="float: right;margin-right: 35%">
                <el-button plain @click="closePresetQuestionAdd">取消</el-button>
              </el-row>
              <el-row style="margin-right: 60%">
                <el-button plain  @click="savePresetQuestion">保存</el-button>
              </el-row>
            </div>
            
        </el-dialog>
        
        <el-dialog :visible="presetEditShow" @close="closePresetQuestionEdit" width="50%">
        
            <el-form label-width="150px" label-position="left" :inline="true"  class="demo-form-inline">
        
        <div>
                <el-form-item label="* 问题描述：">
                    <label slot="label"><span style="color: red;font-weight: bold;">*</span> 问题描述：</label>
                    <el-input v-model="editDesc" style="width: 200%">
                </el-form-item>
                </div>
                <div>
                 <el-form-item label="* 问题描述(繁体)：">
                    <label slot="label"><span style="color: red;font-weight: bold;">*</span> 问题描述(繁体)：</label>
                    <el-input v-model="descFT" style="width: 200%">
                </el-form-item>
                 </div>
                  <div>
                 <el-form-item label="* 问题描述(英语)：">
                    <label slot="label"><span style="color: red;font-weight: bold;">*</span> 问题描述(英语)：</label>
                    <el-input v-model="descEN" style="width: 200%">
                </el-form-item>
                 </div>
                 
                  <div>
                 <el-form-item label="* 问题描述(日语)：">
                    <label slot="label"><span style="color: red;font-weight: bold;">*</span> 问题描述(日语)：</label>
                    <el-input v-model="descJA" style="width: 200%">
                </el-form-item>
                 </div>
                 
                  <div>
                 <el-form-item label="* 问题描述(西班牙语)：">
                    <label slot="label"><span style="color: red;font-weight: bold;">*</span> 问题描述(西班牙语)：</label>
                    <el-input v-model="descSP" style="width: 200%">
                </el-form-item>
                 </div>
                
                <div>
                  <div style="margin-bottom: 10px;font-size: 14px">
                      <label slot="label"><span style="color: red;font-weight: bold;">*</span> Sql：</label>
                  </div>
                  <div>
                      <el-input type="textarea" :autosize="{ minRows: 3}" v-model="editSql" style="width: 95%">
                      </el-input>
                  </div>
                </div>
                
                
                <div>
                <el-form-item
                  label="查看范围"
                  align="center"
                  style="margin-top: 20px"
                >
                
                     <el-select v-model="ownerType" placeholder="请选择">
                          <el-option label="所有人"    :value="0"></el-option>
                          <el-option label="管理者"    :value="1"></el-option>
                     </el-select>
                </el-form-item>
                </div>
                
                <div>
                
                <el-form-item
                  label="排序："
                  align="center"
                  style="margin-top: 20px"
                >
                    <el-select v-model="newIndex" filterable placeholder="请选择">
                      <el-option
                        v-for="item in orderIndex"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value">
                      </el-option>
                    </el-select>
                </el-form-item>
                
                <div></div>
                
                <el-form-item
                  label="灰度配置："
                  align="center"
                  style="margin-top: 20px">
                  <el-switch
                    :value="editSwitchDslGrey"
                    active-color="#13ce66"
                    inactive-color="#ff4949"
                    @change="clickEditSwitchDslGrey()">
                  </el-switch>
                </el-form-item>
                
                <div></div>
                
                <template v-if="editSwitchDslGrey">
                    <el-form-item label="* 灰度ClientIds：">
                        <label slot="label"><span style="color: red;font-weight: bold;">*</span> 灰度ClientIds：</label>
                        <el-input v-model="editGreyClientIds" style="width: 108%">
                        <span style="color: red;font-size: 12px;margin-top: -20px">*注意：灰度 clientIds 使用英文 "," 分隔</span>
                    </el-form-item>
                </template>
                
           <el-form-item
                  label="标签选择"
                  align="center"
                  style="margin-top: 20px"
                >
                 <el-select v-model="tag" placeholder="请选择">
                      <el-option label="无" value=""></el-option>
                      <el-option label="new" value="new"></el-option>
                      <el-option label="hot" value="hot"></el-option>
                 </el-select>
            </el-form-item>
            
            <el-form-item
                  label="问题类型"
                  align="center"
                  style="margin-top: 20px"
                >
                 <el-select v-model="showTag" multiple placeholder="默认为通用类型" >
                      <el-option
                         v-for="item in showTagOption"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                       ></el-option>
                 </el-select>
            </el-form-item>
            
            </el-form>
            
            <div slot="footer" style="margin-bottom: 20px">
              <el-row style="float: right;margin-right: 35%">
                <el-button plain @click="closePresetQuestionEdit">取消</el-button>
              </el-row>
              <el-row style="margin-right: 60%">
                <el-button plain  @click="editSavePresetQuestion">保存</el-button>
              </el-row>
            </div>
            
        </el-dialog>
        
    </el-tab-pane>
    
    <el-tab-pane  name="vector-data-config" label="向量库数据配置" style="margin-top: 15px">
    
        <div style="margin-top: 30px">
            <el-form label-position="left" :inline="true"  class="demo-form-inline" style="float: left">
                <el-form-item label="* 选择文档：">
                    <label slot="label"><span style="color: red;font-weight: bold;">*</span> 选择文档：</label>
                    <el-select v-model="selectDocId" filterable clearable placeholder="请选择向量文档" @change="vectorDetailList">
                          <el-option
                            v-for="item in vectorDocOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value">
                          </el-option>
                        </el-select>
                </el-form-item>
                <el-form-item label="查询问题：">
                    <label slot="label">查询问题：</label>
                    <el-input v-model="searchQuestion">
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" icon="el-icon-search" @click="vectorDetailList">搜索</el-button>
                </el-form-item>
             </el-form>
         
            <el-button type="primary" icon="el-icon-refresh" style="float: right" @click="vectorDetailList()">刷新列表</el-button>
            <el-button type="primary" icon="el-icon-circle-plus-outline"  @click="showVectorDetailAddMethod()" style="float: right;margin-right: 10px">新增问题</el-button>
        </div>
        
        <div style="margin-top: 30px;float: none">
            <template>
                <el-table
                  :data="vectorDetails"
                  border
                  style="width: 100%">
                  <el-table-column
                    prop="text"
                    label="问题"
                    align="center"
                    sortable>
                  </el-table-column>
                  <el-table-column
                    prop="additional_info"
                    label="答案"
                    align="center"
                    sortable>
                    <template slot-scope="scope">
                        <el-tooltip placement="left" :enterable="true" effect="dark" transition="el-fade-in-linear" :open-delay="400">
                          <div slot="content" style="max-width: 500px; white-space: pre-wrap; overflow: auto;">
                            <pre v-html="contentVector(scope.row.additional_info)"></pre>
                          </div>
                          <div style="overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 3;-webkit-box-orient: vertical;">
                            {{ scope.row.additional_info }}
                          </div>
                        </el-tooltip>
                    </template>
                  </el-table-column>
                  <el-table-column
                    label="操作"
                    align="center">
                    <template slot-scope="scope">
                      <el-button @click="showVectorDetailEditMethod(scope.row)" type="text" size="small">编辑</el-button>
<!--                      <el-button @click="deleteVectorDetail(scope.row)" type="text" size="small">删除</el-button>-->
                       <el-popconfirm
                        confirm-button-text="确定"
                        cancel-button-text="取消"
                        title="确定要删除吗？"
                        @confirm="deleteVectorDetail(scope.row)"
                        @cancel="handleCancel">
                          <el-button slot="reference" type="text" size="small">删除</el-button>
                        </el-popconfirm>
                    </template>
                  </el-table-column>
                </el-table>
            </template>
            <div class="block">
              <el-pagination
                @size-change="vectorDetailList"
                @current-change="vectorDetailList"
                :current-page.sync="vectorCurrentPage"
                :page-size.sync="vectorCurrentPageSize"
                :page-sizes="[50, 100, 200, 400]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="vectorTotal"
                style="margin-top: 30px;margin-left: 40%;">
              </el-pagination>
            </div>
        </div>
        
        <el-dialog :visible="showVectorDetailEdit" @close="closeVectorDetailEdit" width="50%">      
            <el-form label-width="150px" label-position="left" :inline="true"  class="demo-form-inline">
                <el-form-item label="* 问题：">
                    <label slot="label"><span style="color: red;font-weight: bold;">*</span> 问题：</label>
                    <el-input v-model="editText" style="width: 200%">
                </el-form-item>
                
                <div>
                  <div style="margin-bottom: 10px;font-size: 14px">
                      <label slot="label"><span style="color: red;font-weight: bold;">*</span> 答案：</label>
                  </div>
                  <div>
                      <el-input type="textarea" :autosize="{ minRows: 3}" v-model="editAdditionalInfo" style="width: 95%">
                      </el-input>
                  </div>
                </div>
            </el-form>
            
            <div slot="footer" style="margin-bottom: 20px">
              <el-row style="float: right;margin-right: 35%">
                <el-button plain @click="closeVectorDetailEdit">取消</el-button>
              </el-row>
              <el-row style="margin-right: 60%">
                <el-button plain  @click="editVectorDetail">保存</el-button>
              </el-row>
            </div>
        </el-dialog>
        
        <el-dialog :visible="showVectorDetailAdd" @close="closeVectorDetailAdd" width="50%">      
            <el-form label-width="150px" label-position="left" :inline="true"  class="demo-form-inline">
            
                <el-form-item>
                    <label slot="label"><span style="color: red;font-weight: bold;">*</span> 文档ID：</label>
                    <el-select v-model="addDocId" filterable clearable placeholder="请选择向量文档">
                      <el-option
                        v-for="item in vectorDocOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value">
                      </el-option>
                    </el-select>
                </el-form-item>
                
                <div></div>
                
                <el-form-item label="* 问题：">
                    <label slot="label"><span style="color: red;font-weight: bold;">*</span> 问题：</label>
                    <el-input v-model="addText" style="width: 110%">
                </el-form-item>
                
                <div>
                  <div style="margin-bottom: 10px;font-size: 14px">
                      <label slot="label"><span style="color: red;font-weight: bold;">*</span> 答案：</label>
                  </div>
                  <div>
                      <el-input type="textarea" :autosize="{ minRows: 3}" v-model="addAdditionalInfo" style="width: 95%">
                      </el-input>
                  </div>
                </div>
            </el-form>
            
            <div slot="footer" style="margin-bottom: 20px">
              <el-row style="float: right;margin-right: 35%">
                <el-button plain @click="closeVectorDetailAdd">取消</el-button>
              </el-row>
              <el-row style="margin-right: 60%">
                <el-button plain  @click="addVectorDetail">保存</el-button>
              </el-row>
            </div>
        </el-dialog>
        
    </el-tab-pane>
    
    
     <el-tab-pane  name="generate-data-table-config" label="报表生成数据表配置" style="margin-top: 15px">
    
        <div style="margin-top: 30px">
            <el-select v-model="selectedDbTbl" filterable clearable placeholder="请选择表名">
              <el-option
                v-for="item in dbTblList"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
            <div style="margin-top: 30px">
            <el-button type="primary" icon="el-icon-circle-plus-outline"  @click="showTableColumnsAddMethod()" style="float: right;margin-right: 10px">新增字段</el-button>
            <el-button type="primary" icon="el-icon-refresh"  @click="tableInfoList()">刷新列表</el-button>
            <el-button type="primary" icon="el-icon-circle-plus-outline"  @click="showAddTableInfo" style="float: right;margin-right: 10px">新增表结构</el-button>
            </div>
        </div>
        
        <div style="margin-top: 30px;float: none">
            <template>
                <el-table
                  :data="tableData"
                  border
                  style="width: 100%">
                  <el-table-column
                    prop="name"
                    label="字段名"
                    align="center"
                    sortable>
                  </el-table-column>
                  <el-table-column
                    prop="mapping"
                    label="源表字段"
                    align="center"
                    sortable>
                  </el-table-column>
                 <el-table-column
                    prop="to_user_comment"
                    label="用户侧字段解释"
                    align="center"
                    sortable>
                  </el-table-column>
                  <el-table-column
                    prop="comment"
                    label="字段解释"
                    align="center"
                    sortable>
                  </el-table-column>
                  <el-table-column
                    prop="type"
                    label="字段类型"
                    align="center"
                    sortable>
                  </el-table-column>
                  <el-table-column
                    prop="nullable"
                    label="是否可以为空"
                    align="center">
                    <template slot-scope="scope">
                    {{scope.row.nullable}}
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop="default"
                    label="默认值"
                    align="center">
                  </el-table-column>
                 <el-table-column
                    prop="is_primary_key"
                    label="是否为主键"
                    align="center">
                    <template slot-scope="scope">
                    {{scope.row.is_primary_key}}
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop="fuzzy_match_flag"
                    label="是否支持模糊查询"
                    align="center">
                    <template slot-scope="scope">
                    {{scope.row.fuzzy_match_flag}}
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop="time_delay"
                    label="同步时延"
                    align="center">
                    <template slot-scope="scope">
                    {{scope.row.time_delay}}
                    </template>
                  </el-table-column>
                  
                <el-table-column
                    label="操作"
                    align="center">
                    <template slot-scope="scope">
                      <el-button @click="showEditTableColumn(scope.row)" type="text" size="small">编辑</el-button>
<!--                      <el-button @click="deleteTableColum(scope.row)" type="text" size="small">删除</el-button>-->
                       <el-popconfirm
                        confirm-button-text="确定"
                        cancel-button-text="取消"
                        title="确定要删除吗？"
                        @confirm="deleteTableColum(scope.row)"
                        @cancel="handleCancel">
                          <el-button slot="reference" type="text" size="small">删除</el-button>
                        </el-popconfirm>
                    </template>
                  </el-table-column>
                </el-table>
            </template>
        </div>
        
        <el-dialog :visible="showTableColumAdd" @close="closeTableColumnsAdd" width="50%">      
        <div>
            <el-form label-width="150px" label-position="left" :inline="true"  class="demo-form-inline">
                <el-form-item label="* 字段：">
                    <label slot="label"><span style="color: red;font-weight: bold;">*</span> 字段：</label>
                    <el-input v-model="columnName" style="width: 100%">
                </el-form-item>
               <el-form-item label="* 字段类型：">
                    <label slot="label"><span style="color: red;font-weight: bold;">*</span> 字段类型：</label>
                    <el-input v-model="columnType" style="width: 100%">
                </el-form-item>
               <el-form-item label="* 默认值：">
                    <label slot="label"><span style="color: red;font-weight: bold;">*</span> 默认值：</label>
                    <el-input v-model="columnDefault" style="width: 100%">
                </el-form-item>
              <el-form-item label="源表字段：">
                    <label slot="label"><span style="color: red;font-weight: bold;">*</span> 源表字段：</label>
                    <el-input v-model="columnMapping" style="width: 100%">
               </el-form-item>
               
               <el-form-item label="* 是否为空：">
                         <el-switch
                            v-model="columnNullable"
                            active-color="#13ce66"
                            inactive-color="#ff4949">
                    </el-switch>
                </el-form-item>
              <el-form-item label="* 是否为主键：">
                         <el-switch
                            v-model="columnIsPrimary"
                            active-color="#13ce66"
                            inactive-color="#ff4949">
                    </el-switch>
                </el-form-item>
                
                
               <el-form-item label="* 是否支持模糊匹配：">
                         <el-switch
                            v-model="fuzzyMatchFlag"
                            active-color="#13ce66"
                            inactive-color="#ff4949">
                    </el-switch>
                </el-form-item>
                
                
            <div>
                  <div style="margin-bottom: 10px;font-size: 14px">
                      <label slot="label"><span style="color: #00ff66;font-weight: bold;">*</span> 用户侧字段解释：</label>
                  </div>
              <div>
                      <el-input type="textarea" :autosize="{ minRows: 3}" v-model="toUserComment" style="width: 95%">
                      </el-input>
                  </div>
                </div>
                
                <div>
                  <div style="margin-bottom: 10px;font-size: 14px">
                      <label slot="label"><span style="color: red;font-weight: bold;">*</span> 字段解释：</label>
                  </div>
              <div>
                      <el-input type="textarea" :autosize="{ minRows: 3}" v-model="columnComment" style="width: 95%">
                      </el-input>
                  </div>
                </div>
                
                <div>
                  <div style="margin-bottom: 10px;font-size: 14px">
                      <label slot="label"><span style="color: red;font-weight: bold;">*</span> 同步时延：</label>
                  </div>
              <div>
                      <el-input type="textarea" :autosize="{ minRows: 3}" v-model="columnTimeDelay" style="width: 95%">
                      </el-input>
                  </div>
                </div>
            </div>
            </el-form>
            
            <div slot="footer" style="margin-bottom: 20px">
              <el-row style="float: right;margin-right: 35%">
                <el-button plain @click="closeTableColumnsAdd">取消</el-button>
              </el-row>
              <el-row style="margin-right: 60%">
                <el-button plain  @click="addTableColumn">保存</el-button>
              </el-row>
            </div>
        </el-dialog>
        
        
      <el-dialog :visible="showTableColumEdit" @close="closeTableEditTableColumn" width="50%">      
        <div>
            <el-form label-width="150px" label-position="left" :inline="true"  class="demo-form-inline">
                <el-form-item label="* 字段：">
                    <label slot="label"><span style="color: red;font-weight: bold;">*</span> 字段：</label>
                    <el-input v-model="editColumnInfo.name" style="width: 100%">
                </el-form-item>
               <el-form-item label="* 字段类型：">
                    <label slot="label"><span style="color: red;font-weight: bold;">*</span> 字段类型：</label>
                    <el-input v-model="editColumnInfo.type" style="width: 100%">
                </el-form-item>
               <el-form-item label="* 默认值：">
                    <label slot="label"><span style="color: red;font-weight: bold;">*</span> 默认值：</label>
                    <el-input v-model="editColumnInfo.default" style="width: 100%">
                </el-form-item>
               <el-form-item label="源表字段：">
                    <label slot="label"><span style="color: red;font-weight: bold;">*</span> 源表字段：</label>
                    <el-input v-model="editColumnInfo.mapping" style="width: 100%">
               </el-form-item>
               <el-form-item label="* 是否为空：">
                         <el-switch
                            v-model="editColumnInfo.nullable"
                            active-color="#13ce66"
                            inactive-color="#ff4949">
                    </el-switch>
                </el-form-item>
              <el-form-item label="* 是否为主键：">
                         <el-switch
                            v-model="editColumnInfo.is_primary_key"
                            active-color="#13ce66"
                            inactive-color="#ff4949">
                    </el-switch>
                </el-form-item>
                
               <el-form-item label="* 是否支持模糊查询：">
                         <el-switch
                            v-model="editColumnInfo.fuzzy_match_flag"
                            active-color="#13ce66"
                            inactive-color="#ff4949">
                    </el-switch>
                </el-form-item>
                
                <div>
                  <div style="margin-bottom: 10px;font-size: 14px">
                      <label slot="label"><span style="color: #00ff80;font-weight: bold;">*</span> 用户侧字段展示：</label>
                  </div>
              <div>
                      <el-input type="textarea" :autosize="{ minRows: 3}" v-model="editColumnInfo.to_user_comment" style="width: 95%">
                      </el-input>
                  </div>
                </div>
                
                <div>
                  <div style="margin-bottom: 10px;font-size: 14px">
                      <label slot="label"><span style="color: red;font-weight: bold;">*</span> 字段解释：</label>
                  </div>
              <div>
                      <el-input type="textarea" :autosize="{ minRows: 3}" v-model="editColumnInfo.comment" style="width: 95%">
                      </el-input>
                  </div>
                </div>
                
              <div>
                  <div style="margin-bottom: 10px;font-size: 14px">
                      <label slot="label"><span style="color: red;font-weight: bold;">*</span> 同步时延：</label>
                  </div>
              <div>
                      <el-input type="textarea" :autosize="{ minRows: 3}" v-model="editColumnInfo.time_delay" style="width: 95%">
                      </el-input>
                  </div>
                </div>
            </div>
            </el-form>
            
            <div slot="footer" style="margin-bottom: 20px">
              <el-row style="float: right;margin-right: 35%">
                <el-button plain @click="closeTableEditTableColumn">取消</el-button>
              </el-row>
              <el-row style="margin-right: 60%">
                <el-button plain  @click="editTableColumn">保存</el-button>
              </el-row>
            </div>
        </el-dialog>
        
        
        <el-dialog :visible="showAddTable" @close="closeAddTableInfo" width="50%">      
        <div>
                <div>
                  <div style="margin-bottom: 10px;font-size: 14px">
                      <label slot="label"><span style="color: red;font-weight: bold;">*</span> 新表json,注意按照格式添加</label>
                  </div>
              <div>
              <el-input type="textarea" :autosize="{ minRows: 3}" v-model="addTableInfoItem" style="width: 95%">
                      </el-input>
                  </div>
                </div>
            </div>
            </el-form>
            
            <div slot="footer" style="margin-bottom: 20px">
              <el-row style="float: right;margin-right: 35%">
                <el-button plain @click="closeAddTableInfo">取消</el-button>
              </el-row>
              <el-row style="margin-right: 60%">
                <el-button plain  @click="addTableInfo(addTableInfoItem)">保存</el-button>
              </el-row>
              
        </el-dialog>
        
        
    </el-tab-pane>
    
    <el-tab-pane  name="useFrequency" label="应用次数配置" style="margin-top: 15px">
    
        <div>
            <h2>Step1：类型次数配置</h2>
            
            <div>
                <div style="margin-top: 10px">
                    <el-button type="primary" icon="el-icon-edit-outline" @click="editFrequencyDialogVisible = true">编辑次数</el-button>
                    <el-button type="primary" icon="el-icon-refresh"  @click="initUseFrequencyData()" style="margin-right: 15px">刷新列表</el-button>
                </div>
            </div>
            <div style="margin-top: 20px">
              <el-table
                :data="useFrequencyData"
                border
                style="width: 100%">
                <el-table-column
                  type="index"
                  label="序号"
                  width="100px"
                  align="center">
                </el-table-column>
                <el-table-column
                  prop="consume_mode"
                  label="模块">
                  <template slot-scope="scope">
                    <template v-if="scope.row.consume_mode === 'daily_consumption'">
                       <el-tag color="#E4F2F0" style="color:#00758F">每日消耗</el-tag>
                    </template>
                    <template v-else>
                       <el-tag color="#FCE7D4" style="color:#E85C41">每月消耗</el-tag>
                    </template>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="system"
                  label="版本">
                  <template slot-scope="scope">
                    <template v-if="scope.row.system === 'leadsAI'">
                       <el-tag color="#87e8de" style="color:#00474f">leadsAI</el-tag>
                    </template>
                    <template v-if="scope.row.system === 'liteAI'">
                       <el-tag color="#ffd591" style="color:#654321">liteAI</el-tag>
                    </template>
                    <template v-if="scope.row.system === 'smartAI'">
                       <el-tag color="#91d5ff" style="color:#002766">smartAI</el-tag>
                    </template>
                    <template v-if="scope.row.system === 'proAI'">
                       <el-tag color="#ffadd2" style="color:#8b0000">proAI</el-tag>
                    </template>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="frequency"
                  label="次数/人">
                </el-table-column>
              </el-table>
            </div>
            
            <el-dialog
              title="编辑应用次数"
              :visible.sync="editFrequencyDialogVisible"
              width="30%"
              center>
              <el-form :label-position="left" label-width="20%">
                <el-form-item label="模块">
                  <template>
                    <el-select v-model="editFrequencyModule" filterable placeholder="请选择对应模块" style="width: 80%" @change="changeModule">
                      <el-option
                        v-for="item in moduleOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value">
                      </el-option>
                    </el-select>
                  </template>
                </el-form-item>
                <el-form-item label="版本">
                  <template>
                    <el-select v-model="editFrequencySystem" filterable placeholder="请选择对应版本" style="width: 80%">
                      <el-option
                        v-for="item in systemOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value">
                      </el-option>
                    </el-select>
                  </template>
                </el-form-item>
                <el-form-item label="次数">
                  <el-input v-model="editFrequency" style="width: 80%" ></el-input>
                </el-form-item>
              </el-form>
              <span slot="footer" class="dialog-footer">
                <el-button @click="editFrequencyDialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="saveUseFrequency">确 定</el-button>
              </span>
            </el-dialog>
            
            <el-divider></el-divider>
        </div>
        
        <div>
            <h2>Step2：Client次数配置</h2>
            
            <div>
                <div style="margin-top: 10px">
                    Client：
                    <el-input
                      placeholder="ClientId"
                      v-model="frequencyClientId"
                      style="width: 300px"
                      >
                    </el-input>
                    <el-button type="primary" icon="el-icon-search" @click="searchClientFrequency" style="margin-left: 10px">查询Client配置次数</el-button>
                
                    <el-button type="primary" icon="el-icon-refresh"  @click="searchClientFrequency" style="margin-right: 15px;float: right">刷新列表</el-button>
                    <el-button type="primary" icon="el-icon-edit-outline" @click="editClientFrequencyDialogVisible = true" style="float: right">编辑次数</el-button>
                </div>
            </div>
            <div style="margin-top: 20px">
              <el-table
                :data="clientConsumptionData"
                border
                style="width: 100%">
                <el-table-column
                  type="index"
                  label="序号"
                  width="100px"
                  align="center">
                </el-table-column>
                <el-table-column
                  prop="consume_mode"
                  label="模块">
                  <template slot-scope="scope">
                    <template v-if="scope.row.consume_mode === 'daily_consumption'">
                       <el-tag color="#E4F2F0" style="color:#00758F">每日消耗</el-tag>
                    </template>
                    <template v-else>
                       <el-tag color="#FCE7D4" style="color:#E85C41">每月消耗</el-tag>
                    </template>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="system"
                  label="版本">
                  <template slot-scope="scope">
                    <template v-if="scope.row.system === 'leadsAI'">
                       <el-tag color="#87e8de" style="color:#00474f">leadsAI</el-tag>
                    </template>
                    <template v-if="scope.row.system === 'liteAI'">
                       <el-tag color="#ffd591" style="color:#654321">liteAI</el-tag>
                    </template>
                    <template v-if="scope.row.system === 'smartAI'">
                       <el-tag color="#91d5ff" style="color:#002766">smartAI</el-tag>
                    </template>
                    <template v-if="scope.row.system === 'proAI'">
                       <el-tag color="#ffadd2" style="color:#8b0000">proAI</el-tag>
                    </template>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="frequency"
                  label="次数/人">
                </el-table-column>
              </el-table>
            </div>
            
            <el-dialog
              title="编辑应用次数"
              :visible.sync="editClientFrequencyDialogVisible"
              width="30%"
              center>
              <el-form label-position="left" label-width="20%" style="margin-left: 5%">
                <el-form-item label="ClientId">
                  <el-input
                      placeholder="ClientId"
                      v-model="editFrequencyClientId"
                      style="width: 80%"
                  >
                </el-form-item>
                <el-form-item label="模块">
                  <template>
                    <el-select v-model="editClientFrequencyModule" filterable placeholder="请选择对应模块" style="width: 80%" @change="changeModule">
                      <el-option
                        v-for="item in moduleOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value">
                      </el-option>
                    </el-select>
                  </template>
                </el-form-item>
                <el-form-item label="次数">
                  <el-input v-model="editClientFrequency" style="width: 80%" ></el-input>
                </el-form-item>
              </el-form>
              <span slot="footer" class="dialog-footer">
                <el-button @click="editClientFrequencyDialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="saveClientUseFrequency">确 定</el-button>
              </span>
            </el-dialog>
            
            <el-divider></el-divider>
        </div>
        
    </el-tab-pane>
    
    <el-tab-pane  name="ai-auto-generate-sql" label="一键生成sql" style="margin-top: 15px">
        <el-form :inline="true"  class="demo-form-inline">
        <el-form-item>
           <template>
                  <div>
                    <el-input
                      size="medium"
                      type="textarea"
                      placeholder="请输入内容"
                      v-model="question"
                      autosize="{ minRows: 2, maxRows: 6 }"
                      :style="{ width: '100%', height: '100%'}">
                    </el-input>
                  </div>
                </template>
            </el-form-item>
             
            <el-form-item>
                <el-button type="primary" icon="el-icon-search" @click="generateSql" :loading="generateLoading">生成sql</el-button>
                <el-button type="primary" icon="el-icon-search" @click="resetGenerateSqlTask">重置sql生成任务</el-button>
            </el-form-item>
        </el-form>
        
  <el-table
    :data="generateSqlData"
    border
    style="width: 100%">
    <el-table-column
      prop="question"
      label="问题"
      width="180">
    </el-table-column>
    <el-table-column
      prop="sql"
      label="SQL"
      width="180">
    </el-table-column>
    <el-table-column
      prop="execSql"
      label="实际执行sql">
            <template slot-scope="scope">
            <div class="scrollable-cell" style="max-height: 200px; overflow-y: auto;">
                <el-button type="text" icon="el-icon-copy-document"
                           @click="copyAnswer(scope.row.execSql)"></el-button>
                {{ scope.row.execSql}}

            </div>
        </template>
    </el-table-column>
    <el-table-column
      prop="answer"
      label="回答(图表展示请在jolie账号查看)">
      <template slot-scope="scope">
            <div class="scrollable-cell" style="max-height: 200px; overflow-y: auto;">
                <el-button type="text" icon="el-icon-copy-document"
                           @click="copyAnswer(scope.row.answer)"></el-button>
                {{scope.row.answer}}

            </div>
        </template>
    </el-table-column>
        <el-table-column
      prop="request_data"
      label="gpt请求详细信息">
            <template slot-scope="scope">
            <div class="scrollable-cell" style="max-height: 200px; overflow-y: auto;">
                <el-button type="text" icon="el-icon-copy-document"
                           @click="copyAnswer(scope.row.request_data)"></el-button>
                {{scope.row.request_data}}

            </div>
        </template>
    </el-table-column>
        <el-table-column
      prop="response_data"
      label="gpt响应信息">
      
               <template slot-scope="scope">
            <div class="scrollable-cell" style="max-height: 200px; overflow-y: auto;">
                <el-button type="text" icon="el-icon-copy-document"
                           @click="copyAnswer(scope.row.response_data)"></el-button>
                {{ scope.row.response_data}}

            </div>
        </template>
        
    </el-table-column>
    
        <el-table-column
      prop="error_msg"
      label="错误信息">
    </el-table-column>
    
  </el-table>
        
    
        
        <el-divider></el-divider>
        
       
    </el-tab-pane>
    

HTML;
?>

Vue.component('ai-function', {
    template: `<?php echo $template?>`,
    data: function() {
        return {
            // 向量数据配置
            vectorDocOptions : [],
            selectDocId : '',
            vectorDetails : [],
            showVectorDetailEdit : false,
            editAdditionalInfo: '',
            editText: '',
            editDetailId: 0,
            showVectorDetailAdd : false,
            addText : '',
            addAdditionalInfo : '',
            addDocId : '',
            vectorTotal : 0,
            vectorCurrentPage : 1,
            vectorCurrentPageSize : 50,
            searchQuestion : '',

            // 报表生成预设问题
            activeTab : 'generate-data-preset-question',
            presetQuestions : [],
            desc : '',
            sql : '',
            presetAddShow: false,
            orderShow : false,
            presetEditShow: false,
            switchDslConfig : false,
            switchDslGrey : false,
            editSwitchDslGrey : false,
            greyClientIds : '',
            editGreyClientIds : '',
            dslConfigShow : false,
            editDesc : '',
            editOldDesc : '',
            editSql : '',
            editForm : '',
            newIndex : 0,
            oldIndex : 0,
            ownerType : 0,
            tag : '',
            descJA:'',
            descFT:'',
            descEN:'',
            descSP:'',
            showTag: [],
            showTagOption: [
                {
                    label: '通用类型',
                    value: 'all',
                    type: 'success'
                },
                {
                    label: 'AI客群',
                    value: 'AiSwarm',
                    type: 'success'
                },
                {
                    label: '客户筛选',
                    value: 'company',
                    type: 'info'
                },
                {
                    label: '订单筛选',
                    value: 'order',
                    type: 'warning'
                },
                {
                    label: '商机筛选',
                    value: 'opportunity',
                    type: 'danger'
                }
            ],
            form: {
                items: [
                    {
                        filed: '',
                        type: '',
                        value: '',
                        valueType: ''
                    }
                ]
            },
            formatForm: {
                items: [
                    {
                        filed: '',
                        type: '',
                        value: '',
                        valueType: '',
                        startValue : '',
                        startValueType : ''
                    }
                ]
            },
            editConfig : false,
            typeOptions: [
                {
                    label : "区间时间",
                    value : 'betweenTime'
                },
                {
                    label : "固定时间",
                    value : 'fixedTime'
                },
                {
                    label : "相对时间",
                    value : 'relativeTime'
                }
            ],
            preValueTypeOptions: [
                {
                    label : "天前零时",
                    value : 'beforeDay'
                },
                {
                    label : "周前零时",
                    value : 'beforeWeek'
                },
                {
                    label : "本周初",
                    value : 'week'
                },
                {
                    label : "月前零时",
                    value : 'beforeMonth'
                },
                {
                    label : "本月初",
                    value : 'month'
                },
                {
                    label : "年前零时",
                    value : 'beforeYear'
                },
                {
                    label : "今年初",
                    value : 'year'
                }
            ],
            afterValueTypeOptions: [
                {
                    label : "现在",
                    value : 'now'
                },
                {
                    label : "周前结束时",
                    value : 'afterWeek'
                },
                {
                    label : "天前结束时",
                    value : 'afterDay'
                },
                {
                    label : "本周末",
                    value : 'week'
                },
                {
                    label : "月前结束时",
                    value : 'afterMonth'
                },
                {
                    label : "本月末",
                    value : 'month'
                },
                {
                    label : "年前结束时",
                    value : 'afterYear'
                },
                {
                    label : "今年末",
                    value : 'year'
                }
            ],
            relativeValueTypeOptions: [
                {
                    label : "天前 至 现在",
                    value : 'beforeDay'
                },
                {
                    label : "月前 至 现在",
                    value : 'beforeMonth'
                },
                {
                    label : "年前 至 现在",
                    value : 'beforeYear'
                }
            ],
            orderData : [],
            changeOrderData : [],
            orderIndex : [],
            useFrequencyData : [],
            editFrequencyDialogVisible : false,
            moduleOptions : [
                {
                    label : "按日消耗",
                    value : 'daily_consumption'
                },
                {
                    label : "按月消耗",
                    value : 'monthly_consumption'
                }
            ],
            systemOptions : [
                {
                    label : "leadsAI",
                    value : 'leadsAI'
                },
                {
                    label : "liteAI",
                    value : 'liteAI'
                },
                {
                    label : "smartAI",
                    value : 'smartAI'
                },
                {
                    label : "proAI",
                    value : 'proAI'
                }
            ],
            editFrequencyModule : '',
            editFrequencySystem : '',
            editFrequency : '',

            editClientFrequencyDialogVisible : false,
            frequencyClientId : '',
            clientConsumptionData : [],
            editFrequencyClientId : '',
            editClientFrequencyModule : '',
            editClientFrequency : '',
            dbTblInfo: {},
            selectedDbTbl: 'tbl_company',
            showTableColumAdd : false,
            columnName : '',
            columnType: '',
            columnDefault : '',
            columnComment : '',
            columnNullable : false,
            columnIsPrimary: false,
            showTableColumEdit : false,
            editColumnInfo : {},
            showAddTable : false,
            addTableInfoItem : '',
            columnMapping: '',
            columnTimeDelay: '',
            fuzzyMatchFlag : false,
            toUserComment:'',

            question: '',
            generateSqlData : [],
            generateLoading : false,
            generateTotalCount : 0,
            generateFinishCount : 0

        }
    },
    created: function () {
        this.presetQuestionList();
    },
    computed:{
        dbTblList   () {
            return Object.keys(this.dbTblInfo).map(item => ({ label: item, value: item }))
        },
        tableData() {
            return this.dbTblInfo?.[this.selectedDbTbl]?.columns
        },
        tableDesc() {
            return this.dbTblInfo?.[this.selectedDbTbl]?.desc
        }
    },
    watch: {
        activeTab(newTab, oldTab) {
            if (newTab === 'ai-auto-generate-sql') {
                // 当 tab 切换到 "ai-auto-generate-sql" 时开始轮询
                this.startPolling();
            } else if (oldTab === 'ai-auto-generate-sql') {
                // 当 tab 切换离开 "ai-auto-generate-sql" 时停止轮询
                this.stopPolling();
            }
        }
    },
    methods: {
        vectorDocList() {
            this.vectorDocOptions = [];

            this.$http.post('/prometheus/aiService/VectorDocList')
                .then(function (response) {
                    if (response.data.code === 0 && response.data.data) {

                        var vectorDocLists = response.data.data;

                        for (var i = 0; i < vectorDocLists.length; i++) {
                            this.vectorDocOptions.push({
                                'value': vectorDocLists[i]['doc_id'],
                                'label': vectorDocLists[i]['doc_name']
                            });
                        }

                    } else {
                        this.$message.error(response.data.msg);
                    }
                });
        },
        vectorDetailList() {
            this.vectorDetails = [];

            if (this.selectDocId === '') {
                return;
            }

            var params = {
                "docId" : this.selectDocId,
                "question" : this.searchQuestion,
                "page" : this.vectorCurrentPage,
                "pageSize" : this.vectorCurrentPageSize
            }

            this.$http.post('/prometheus/aiService/VectorDetailList', params)
                .then(function (response) {
                    if (response.data.code === 0 && response.data.data) {
                        this.vectorDetails = response.data.data.list;
                        this.vectorTotal = response.data.data.count;
                    } else {
                        this.$message.error(response.data.msg);
                    }
                });
        },
        closeVectorDetailEdit() {
            this.showVectorDetailEdit = false;
        },
        closeVectorDetailAdd() {
            this.addText = '';
            this.addAdditionalInfo = '';
            this.addDocId = '';
            this.showVectorDetailAdd = false;
        },
        showVectorDetailEditMethod(row) {
            this.editDetailId = row.detail_id;
            this.editText = row.text;

            var newContent = JSON.parse(row.additional_info);
            this.editAdditionalInfo = newContent['answer'];

            this.showVectorDetailEdit = true;
        },
        showVectorDetailAddMethod() {
            this.showVectorDetailAdd = true;
        },
        editVectorDetail() {
            var params = {
                "detailId": this.editDetailId,
                "text": this.editText,
                "additionalInfo": this.editAdditionalInfo
            }

            this.$http.post('/prometheus/aiService/editVectorDetail', params)
                .then(function (response) {
                    if (response.data.code === 0 && response.data.data) {
                        this.$message.success('更新成功！');
                        this.vectorDetailList();
                        this.closeVectorDetailEdit();
                    } else {
                        this.$message.error(response.data.msg);
                    }
                });
        },
        addVectorDetail() {
            var params = {
                "text": this.addText,
                "additionalInfo": this.addAdditionalInfo,
                'docId' : this.addDocId
            }

            this.$http.post('/prometheus/aiService/saveVectorDetail', params)
                .then(function (response) {
                    if (response.data.code === 0 && response.data.data) {
                        this.$message.success('新增成功！');

                        this.selectDocId = this.addDocId;
                        this.vectorDetailList();
                        this.closeVectorDetailAdd();
                    } else {
                        this.$message.error(response.data.msg);
                    }
                });
        },
        deleteVectorDetail(row){
            var params = {
                "detailId": row.detail_id,
                "docId": row.doc_id
            }

            this.$http.post('/prometheus/aiService/DeleteVectorDetail', params)
                .then(function (response) {
                    if (response.data.code === 0 && response.data.data) {
                        this.$message.success('删除成功！');
                        this.vectorDetailList();
                    } else {
                        this.$message.error(response.data.msg);
                    }
                });
        },


        handleIndexChange(oldValue,newValue) {
            console.log('修改前的值：', oldValue);
            console.log('行号：', newValue);
            // 在这里可以调用你的函数，传入修改前的值和行号作为参数
        },
        tabClick: function (tab, event) {
            this.client_id = '';
            this.user_id = '';

            switch (tab.name)
            {
                case 'generate-data-preset-question':
                    this.presetQuestionList();
                    break;
                case 'vector-data-config':
                    this.vectorDocList();
                    break;
                case 'generate-data-table-config':
                    this.tableInfoList();
                    break;
                case 'useFrequency':
                    this.initUseFrequencyData();
                    break;
            }
        },
        addPresetQuestion(){
            this.presetAddShow = true;
        },
        closeOrderShow(){
            this.orderShow = false;
            this.changeOrderData = [];
            this.ownerType = '';
            this.tag = '';
        },
        closePresetQuestionAdd(){
            this.presetAddShow = false;

            this.desc = '';
            this.descJA = '';
            this.descSP = '';
            this.descFT = '';
            this.descEN = '';
            this.sql = '';

            this.form = {
                items: [
                    {
                        filed: '',
                        type: '',
                        value: '',
                        valueType: '',
                        startValue : '',
                        startValueType : ''
                    }
                ]
            };

            this.switchDslConfig = false;
            this.dslConfigShow = false;
            this.greyClientIds = '';
            this.switchDslGrey = false;
            this.ownerType = '';
            this.tag = '';
            this.showTag = [];
        },
        saveOrder(){
            var params = {
                'presetQuestion' : this.changeOrderData
            }

            this.$http.post('/prometheus/aiService/PresetQuestionOrder', params)
                .then(function (response) {
                    if (response.data.code === 0 && response.data.data) {
                        this.$message.success('排序成功！');
                    } else {
                        this.$message.error(response.data.msg);
                    }
                });

            this.closeOrderShow();
            this.presetQuestionList();
        },
        closePresetQuestionEdit(){
            this.presetEditShow = false;
            this.editGreyClientIds = '';
            this.editSwitchDslGrey = false;
            this.ownerType = '';
            this.tag = '';
            this.showTag = [];
            this.descJA = '';
            this.desc = '';
            this.descSP = '';
            this.descEN = '';
            this.descFT = '';
        },
        clickSwitchDslConfig() {
            this.switchDslConfig = !this.switchDslConfig
            this.dslConfigShow = !this.dslConfigShow
        },
        clickSwitchDslGrey() {
            this.switchDslGrey = !this.switchDslGrey
            this.greyClientIds = '';
        },
        clickEditSwitchDslGrey() {
            this.editSwitchDslGrey = !this.editSwitchDslGrey
            this.editGreyClientIds = '';
        },
        addItem() {
            this.form.items.push(
                {
                    filed: '',
                    type: '',
                    value: '',
                    valueType: ''
                }
            );
        },
        addEditItem() {
            this.editForm.items.push(
                {
                    filed: '',
                    type: '',
                    value: '',
                    valueType: ''
                }
            );
        },
        removeItem(index) {
            if (this.form.items.length > 1) {
                this.form.items.splice(index, 1);
            }
        },
        removeEditItem(index) {
            if (this.editForm.items.length > 1) {
                this.editForm.items.splice(index, 1);
            }
        },
        savePresetQuestion() {
            var params = {
                'desc': this.desc,
                'sql' : this.sql,
                'greyClientIds' : this.greyClientIds,
                'ownerType' : this.ownerType,
                'tag' : this.tag,
                'showTag' : this.showTag,
                'desc_ja' : this.descJA,
                'desc_en' : this.descEN,
                'desc_sp' : this.descSP,
                'desc_ft' : this.descFT
            }

            this.$http.post('/prometheus/aiService/SavePresetQuestion', params)
                .then(function (response) {
                    if (response.data.code === 0 && response.data.data) {
                        this.$message.success('新增成功！');
                    } else {
                        this.$message.error(response.data.msg);
                    }
                });

            this.closePresetQuestionAdd();
            setTimeout(this.presetQuestionList, 100);
        },
        editSavePresetQuestion() {
            var params = {
                'desc': this.editDesc,
                'oldDesc': this.editOldDesc,
                'sql' : this.editSql,
                'greyClientIds' : this.editGreyClientIds,
                'oldIndex' : this.oldIndex,
                'newIndex' : this.newIndex,
                'ownerType' : this.ownerType,
                'tag' : this.tag,
                'showTag' : this.showTag,
                'desc_ja' : this.descJA,
                'desc_en' : this.descEN,
                'desc_sp' : this.descSP,
                'desc_ft' : this.descFT
            }

            this.$http.post('/prometheus/aiService/editPresetQuestion', params)
                .then(function (response) {
                    if (response.data.code === 0 && response.data.data) {
                        this.$message.success('修改成功！');
                    } else {
                        this.$message.error(response.data.msg);
                    }
                });

            this.presetEditShow = false;
            setTimeout(this.presetQuestionList, 100);
        },
        presetQuestionList() {
            this.orderIndex = [];

            this.$http.post('/prometheus/aiService/PresetQuestionList')
                .then(function (response) {
                    if (response.data.code === 0 && response.data.data) {
                        this.presetQuestions = response.data.data

                        var newIndex = 1;
                        for (var i = 0; i < this.presetQuestions.length; i++) {
                            this.orderIndex.push({
                                'value' : i,
                                'label' : "第 " + newIndex + " 位"
                            });
                            newIndex++;
                        }
                    } else {
                        this.$message.error(response.data.msg);
                    }
                });

        },
        generateOrderData() {
            this.orderShow = true;
            this.orderData = [];

            this.$http.post('/prometheus/aiService/PresetQuestionList')
                .then(function (response) {
                    if (response.data.code === 0 && response.data.data) {
                        var presetQuestions = response.data.data

                        for (var i = 0; i < presetQuestions.length; i++) {
                            this.orderData.push({
                                'key': presetQuestions[i]['desc'],
                                'label': presetQuestions[i]['desc']
                            });
                        }
                    } else {
                        this.$message.error(response.data.msg);
                    }
                });
        },
        content(content) {
            return content;
        },
        contentVector(content) {
            var newContent = JSON.parse(content);

            try {
                var answer = newContent['answer'];
                answer = JSON.parse(answer);
                return JSON.stringify(answer, null, '\t');
            } catch (error) {
                console.error("JSON转换失败，返回原始content。错误信息: ", error);
                return content;
            }
        },
        deletePresetQuestion(row) {
            var params = {
                'presetQuestion': row
            }

            this.$http.post('/prometheus/aiService/DeletePresetQuestion', params)
                .then(function (response) {
                    if (response.data.code === 0 && response.data.data) {
                        this.$message.success('删除成功！');
                    } else {
                        this.$message.error(response.data.msg);
                    }
                });

            setTimeout(this.presetQuestionList, 300);
        },
        editPresetQuestion(row) {
            this.presetEditShow = true;

            this.editDesc = row.desc;
            this.editSql = row.sql;
            this.descJA = row.desc_ja
            this.descFT = row.desc_ft
            this.descEN = row.desc_en
            this.descSP = row.desc_sp

            this.ownerType = row.ownerType;

            this.tag = row.tag;
            if (row.ownerType === '') {
                this.ownerType = 0
            }

            this.showTag = row.showTag;

            if (row.dslConfig !== '') {
                this.editConfig = true;
            }

            if (row.greyClientIds !== ''){
                this.editSwitchDslGrey = true;
            }
            this.editGreyClientIds = row.greyClientIds;

            this.newIndex = row.indexValue;
            this.oldIndex = row.indexValue;

            this.editOldDesc = row.desc;
        },
        handleSortChange({ newIndex, oldIndex }) {
            const movedItem = this.presetQuestions.splice(oldIndex, 1)[0];
            this.presetQuestions.splice(newIndex, 0, movedItem);
        },

        ownerTypeFormatter(row, column, cellValue) {
            const ownerTypes = {
                0: '所有人',
                1: '管理者',
                2: '非管理者'
            };
            return ownerTypes[cellValue] || '所有人';
        },

        // 次数配置
        initUseFrequencyData() {

            this.$http.get('/prometheus/aiService/ConsumeModelUseFrequency')
                .then(function (response) {
                    if (response.data.code === 0 && response.data.data) {
                        this.useFrequencyData = response.data.data;
                    } else {
                        this.$message.error(response.data.msg);
                    }
                });

        },

        saveUseFrequency() {
            var params = {
                'consumeMode' : this.editFrequencyModule,
                'system' : this.editFrequencySystem,
                'frequency' : this.editFrequency
            };

            this.$http.post('/prometheus/aiService/saveUseFrequency', params)
                .then(function (response) {
                    if (response.data.code === 0 && response.data.data) {
                        this.$message.success('保存成功！');
                        this.initUseFrequencyData();
                        this.editFrequencyDialogVisible = false;
                    } else {
                        this.$message.error(response.data.msg);
                    }
                });
        },

        changeModule() {
            if (this.editFrequencyModule === 'monthly_consumption') {
                this.systemOptions = this.systemOptions.filter(option => option.value !== 'leadsAI' && option.value !== 'liteAI');
            } else {
                if (!this.systemOptions.find(option => option.value === 'leadsAI')) {
                    this.systemOptions.push({
                        label: "leadsAI",
                        value: 'leadsAI'
                    });
                }

                if (!this.systemOptions.find(option => option.value === 'liteAI')) {
                    this.systemOptions.push({
                        label: "liteAI",
                        value: 'liteAI'
                    });
                }
            }
        },

        searchClientFrequency() {

            this.editFrequencyClientId = this.frequencyClientId;

            var params = {
                'clientId' : this.frequencyClientId
            }

            this.$http.post('/prometheus/aiService/ClientConsumeModelUseFrequency', params)
                .then(function (response) {
                    if (response.data.code === 0 && response.data.data) {
                        this.clientConsumptionData = response.data.data
                    } else {
                        this.$message.error(response.data.msg);
                    }
                });
        },

        saveClientUseFrequency() {

            this.frequencyClientId = this.editFrequencyClientId;

            var params = {
                'clientId' : this.editFrequencyClientId,
                'consumeMode' : this.editClientFrequencyModule,
                'frequency' : this.editClientFrequency
            };

            this.$http.post('/prometheus/aiService/SaveClientUseFrequency', params)
                .then(function (response) {
                    if (response.data.code === 0 && response.data.data) {
                        this.$message.success('保存成功！');
                        this.searchClientFrequency();
                        this.editClientFrequencyDialogVisible = false;
                    } else {
                        this.$message.error(response.data.msg);
                    }
                });

        },
        tableInfoList() {
            return this.$http.post('/prometheus/aiService/GetGenerateDataTableInfo')
                .then(function (response) {
                    if (response.data.code === 0 && response.data.data) {
                        this.dbTblInfo = response.data.data.list
                    } else {
                        this.$message.error(response.data.msg);
                    }
                });
        },
        saveTableInfo() {
            var params = {
                "tableJson": JSON.stringify(this.dbTblInfo)
            }
            return this.$http.post('/prometheus/aiService/SaveGenerateDataTableInfo',params,{'Content-Type': 'application/json'})
        },
        showTableColumnsAddMethod() {
            this.showTableColumAdd = true;
        },
        closeTableColumnsAdd() {
            this.showTableColumAdd = false;
        },
        async addTableColumn() {
            const tableColumn = {
                'name' : this.columnName,
                'type' : this.columnType,
                'default' : this.columnDefault,
                'nullable' : this.columnNullable,
                'comment' : this.columnComment,
                'is_primary_key' : this.columnIsPrimary,
                'mapping' : this.columnMapping,
                'time_delay' : this.time_delay,
                'fuzzy_match_flag' : this.fuzzyMatchFlag,
                'to_user_comment': this.toUserComment
            }
            this.dbTblInfo[this.selectedDbTbl].columns.push?.(tableColumn)

            await this.saveTableInfo()

            this.closeTableColumnsAdd()
            await this.tableInfoList()
        },

         showEditTableColumn(item) {
            this.showTableColumEdit = true;
            this.editColumnInfo = item

        },
        closeTableEditTableColumn() {
            this.showTableColumEdit = false;
            this.tableInfoList()
        },

        async editTableColumn() {
            await this.saveTableInfo()
            await this.closeTableEditTableColumn()
        },

        async deleteTableColum(columnToDelete) {
            const index = this.dbTblInfo[this.selectedDbTbl].columns.findIndex(column => {
                return column.name === columnToDelete.name;
            });

            if (index !== -1) {
                // 如果找到了对应的列，就从数组中移除
                this.dbTblInfo[this.selectedDbTbl].columns.splice(index, 1);
            }
            await this.saveTableInfo();
            await this.tableInfoList();
        },

        async addTableInfo() {
            this.addTableInfoItem = JSON.parse(this.addTableInfoItem)
            Object.entries(this.addTableInfoItem).forEach(([keyName, value]) => {
                console.log(keyName, value)
                this.dbTblInfo[keyName] = value
            })
            console.log(this.dbTblInfo)
            await this.saveTableInfo();
            this.closeAddTableInfo();
        },
        showAddTableInfo() {
            this.showAddTable = true;
        },
        closeAddTableInfo() {
            this.showAddTable = false;
            this.tableInfoList();
        },
        getGenerateSql() {
            var params = {
                "questions": this.question.split("\n")
            }
            return this.$http.post('/prometheus/aiService/BuildPreQuestion',params,{'Content-Type': 'application/json'})
                .then(function (response) {
                if (response.data.code === 0 && response.data.data) {
                    this.generateLoading = true
                }
            });
        },

         async generateSql() {
            await this.getGenerateSql()
             this.getGenerateSqlResult()
        },
        async resetGenerateSqlTask() {
            await this.$http.get('/prometheus/aiService/ResetBuildPreQuestionTask')
            this.getGenerateSqlResult()
        },

        copyAnswer(answer) {
            const textarea = document.createElement('textarea');
            textarea.value = answer;
            document.body.appendChild(textarea);
            textarea.select();
            document.execCommand('copy');
            document.body.removeChild(textarea);
            this.$message({
                message: '已复制',
                type: 'success',
                duration: 1500
            });
        },
        getGenerateSqlResult() {
            this.$http.get('/prometheus/aiService/GetBuildPreQuestionResult')
                .then(function (response) {
                    if (response.data.code === 0 && response.data.data) {
                        this.generateSqlData = response.data.data.list
                        this.generateTotalCount = response.data.data.count
                        this.generateFinishCount = response.data.data.finishCount
                        this.generateLoading = (this.generateTotalCount !== this.generateFinishCount)
                    }
                })
        },
        startPolling() {
            // 初始数据获取
            this.getGenerateSqlResult();
            // 每隔 5 秒获取一次数据
            this.intervalId = setInterval(this.getGenerateSqlResult, 20000);
        },
        stopPolling() {
            // 清除定时器
            if (this.intervalId) {
                clearInterval(this.intervalId);
                this.intervalId = null;
            }
        },
        mounted() {
            console.log(this.activeTab)
            // 初始检查当前激活的 tab
            if (this.activeTab === 'ai-auto-generate-sql') {
                this.startPolling();
            }
        },
        beforeDestroy() {
            // 清除定时器
            this.stopPolling()
        },

    }
})