<?php
$template = <<<HTML
<el-tabs>
    <el-tab-pane label="Ai-connector调用情况" style="margin-top: 15px">
        <el-radio-group v-model="selectChart">
              <el-radio-button label="调用次数"></el-radio-button>
              <el-radio-button label="错误统计"></el-radio-button>
              <el-radio-button label="错误日志明细"></el-radio-button>
              <el-radio-button label="调用量走势"></el-radio-button>
        </el-radio-group>
        <el-date-picker v-model="date" align="right" value-format="yyyy-MM-dd" type="date" placeholder="选择日期"></el-date-picker>
         <el-select v-show="selectChart === '调用量走势'" v-model="service" @change="loadCallChart()" filterable allow-create placeholder="选择模型" style="width: 260px">
            <el-option v-for="i_service in services" :key="i_service" :label="i_service" :value="i_service"></el-option>
        </el-select>
        
        <div v-show="selectChart === '调用次数'">
            <h3>当日调用次数</h3>
            <div id="model_call_count" style="width: 1600px;height: 500px;" ></div>
            
            <h3>每日调用量</h3>
            <div id="weekly_call_trend" style="width: 1600px;height: 500px;" ></div>
        </div>
        <div v-show="selectChart === '错误统计'" style="width: calc(100%);height: 1400px;" id="error_charts">
            <el-row type="flex" class="row-bg" justify="center">
                    <el-col :span="16">
                         <div style="line-height: 42px;">
                         <span style="font-size: 18px;font-weight:bold;">最近节点报错</span>
                             <el-radio-group v-model="errorTimeRange" size="mini">
                                <el-radio-button label="10分钟"></el-radio-button>
                                <el-radio-button label="30分钟"></el-radio-button>
                                <el-radio-button label="1小时"></el-radio-button>
                                <el-radio-button label="2小时"></el-radio-button>
                                <el-radio-button label="6小时"></el-radio-button>
                                <el-radio-button label="12小时"></el-radio-button>
                                <el-radio-button label="24小时"></el-radio-button>
                                <el-radio-button label="48小时"></el-radio-button>
                            </el-radio-group>
                         </div> 
                         <div id="node_error_count" style="width: 100%;height: 400px;"></div>
                    </el-col>
                    <el-col :span="8">
                         <h3>指定节点模型报错比例</h3>
                          <div id="node_models_error_count" style="width: 100%;height: 400px;"></div>
                    </el-col>
            </el-row>
           <el-row type="flex" class="row-bg" justify="center">
                <el-col :span="12">
                    <h3>一周错误率走势</h3>
                    <div id="error_rate" style="width: 100%;height: 400px;"></div>
                </el-col>
                <el-col :span="12">
                   
                </el-col>
            </el-row>
            <el-row type="flex" class="row-bg" justify="center">
                <el-col :span="12">
                    <h3>分业务报错次数</h3>
                    <div id="error_1" style="width: 300px;height: 300px;"></div>
                </el-col>
                <el-col :span="12">
                    <h3>分模型报错次数</h3>
                    <div id="error_2" style="width: 100%;height: 300px;"></div>
                </el-col>
            </el-row>
        </div>
        <div v-show="selectChart === '错误日志明细'" style="padding-top: 15px;">
            <el-table :data="errorDetails" style="width: 100%"  v-loading="loading">
                <el-table-column prop="datetime" label="时间" width="180"></el-table-column>
                <el-table-column prop="caller_module" label="caller_module" width="160"></el-table-column>
                <el-table-column prop="caller_scene" label="caller_scene" width="160"></el-table-column>
                <el-table-column prop="model" label="model" width="180"></el-table-column>
                <el-table-column prop="resource" label="resource" width="180"></el-table-column>
                <el-table-column prop="error" label="错误信息"> </el-table-column>
            </el-table>
        </div>
        <div v-show="selectChart === '调用量走势'" style="padding-top: 15px;">
            <div id="model_call_chart_tpm" style="width: 1600px;height: 500px;" ></div>
            <div id="model_call_chart_rpm" style="width: 1600px;height: 500px;" ></div>
        </div>
    </el-tab-pane>
</el-tabs>
HTML;
?>

Vue.component('ai-statistics', {
    template: `<?php echo $template ?>`,
    data: function () {
        return {
            selectChart: '调用次数',
            errorDetails: [],
            loading: true,
            date: '',
            errorTimeRange: '2小时',
            service: 'azure-openai-gpt-4-turbo',
            services: [],
        }
    },
    mounted: function () {
        this.loadCallTimesChart();
    },

    computed: {},
    created() {

    },
    watch: {
        selectChart(chart) {
            console.log("切换到: " + chart)
            this.refreshCharts();
        },
        date(date) {
            this.refreshCharts();
        },
        errorTimeRange() {
            this.refreshCharts();
        },
    },
    methods: {
        refreshCharts() {
            switch (this.selectChart) {
                case '调用次数':
                    this.loadCallTimesChart();
                    break;
                case '错误统计':
                    this.loadErrorCountChart();
                    break;
                case '错误日志明细':
                    this.loadErrorDetail();
                    break;
                case '调用量走势':
                    this.loadCallChart();
                    break;
            }
        },
        loadCallTimesChart() {
            this.$http.post('/prometheus/aiService/GetConnectorStatistics', {date: this.date})
                .then(function (response) {
                    if (response.data.code === 0 && response.data.data) {
                        let data = response.data.data;
                        var echartOption = this.generateModelCountOptions(data.call_times.models, data.call_times.data, data.call_times.modules);
                        echarts.init(document.getElementById('model_call_count')).setOption(echartOption, true);

                        let options = this.generateBarOptions(data.weekly_call_count, 'line');
                        echarts.init(document.getElementById('weekly_call_trend')).setOption(options, true);
                    } else {
                        this.$message.error(response.data.msg);
                    }
                });
        },

        loadErrorDetail() {
            let that = this;
            that.loading = true;
            this.$http.post('/prometheus/aiService/GetConnectorErrorDetail', {date: this.date})
                .then(function (response) {
                    if (response.data.code === 0 && response.data.data) {
                        let data = response.data.data;
                        let errors = [];
                        for (var i = 0; i < data.errors.length; i++) {
                            errors.push(JSON.parse(data.errors[i]));
                        }
                        that.errorDetails = errors;
                    } else {
                        this.$message.error(response.data.msg);
                    }
                }).finally(function () {
                    that.loading = false;
            });
        },

        loadCallChart() {
            let that = this;
            that.loading = true;
            this.$http.post('/prometheus/aiService/GetConnectorCallCount', {service: this.service, date: this.date})
                .then(function (response) {
                    if (response.data.code === 0 && response.data.data) {
                        let usage = response.data.data.usage;
                        that.services = response.data.data.services;
                        if (!usage) {
                            this.$message.error("暂无数据");
                            return;
                        }
                        that.renderUsage(usage)
                    } else {
                        this.$message.error(response.data.msg);
                    }
                }).finally(function () {
                that.loading = false;
            });
        },

        loadErrorCountChart() {
            let that = this;
            this.$http.post('/prometheus/aiService/GetConnectorErrors', {date: this.date, errorTimeRange: this.errorTimeRange})
                .then(function (response) {
                    if (response.data.code === 0 && response.data.data) {
                        let data = response.data.data;
                        // 按节点
                        let errorChart = (echarts.init(document.getElementById('node_error_count')));
                        errorChart.off('click');
                        let rows = [];
                        for(let nodeName of data.node_error.nodes){
                            rows.push(Object.values(data.node_error.range_error_count[nodeName]))
                        }
                        errorChart.setOption(that.generateModelCountOptions(data.node_error.time_ranges, rows, data.node_error.nodes), true);
                        errorChart.on('click', function(params) {
                            console.log(params);
                            let clickedNode = params.seriesName;
                            let clickedTime = params.name;
                            console.log("点击 node=" + clickedNode + ", time=" + clickedTime);

                            let items = [];
                            let model_error_count = data.node_error.model_error_count;
                            for (let model_name in model_error_count[clickedNode][clickedTime]) {
                                items.push({'value': model_error_count[clickedNode][clickedTime][model_name], name: model_name});
                            }

                            let pieChart = (echarts.init(document.getElementById('node_models_error_count')));
                            pieChart.setOption(that.generatePieOptions(items, clickedNode + " "+ clickedTime))
                        });

                        // 按错误场景
                        (echarts.init(document.getElementById('error_1')))
                            .setOption(that.generateBarOptions(data.error_count_by_module), true);

                        // 按模型
                        (echarts.init(document.getElementById('error_2')))
                            .setOption(that.generateBarOptions(data.error_count_by_model), true);

                        // 错误次数走势
                        let options = that.generateRateOptions(data.weekly_call_count, data.weekly_error_count);
                        (echarts.init(document.getElementById('error_rate')))
                            .setOption(options, true);
                    }
                });
        },

        renderUsage(usage) {
            console.log(usage);
            let tpmChart = (echarts.init(document.getElementById('model_call_chart_tpm')));
            let rpmChart = (echarts.init(document.getElementById('model_call_chart_rpm')));
            tpmChart.off('click');
            rpmChart.off('click');
            let times = Object.keys(usage);
            let tokenUsageLine = Object.values(usage).map(item => item['token'])
            let tokenLimitLine = Object.values(usage).map(item => item['tpm'])
            let reqUsageLine = Object.values(usage).map(item => item['req'])
            let reqLimitLine = Object.values(usage).map(item => item['rpm'])

            let tpmOption = this.getStackedLines("Token限制", times, {'tokenUsage': tokenUsageLine, 'tokenLimit': tokenLimitLine});
            let rpmOption = this.getStackedLines("rpm限制", times, {'reqUsage': reqUsageLine, 'reqLimit': reqLimitLine});


            tpmChart.setOption(tpmOption);
            rpmChart.setOption(rpmOption);
        },

        getStackedLines(title, xData, yLines) {
            let option = {
                title: {
                    text: title
                },
                tooltip: {
                    trigger: 'axis'
                },
                legend: {
                    data: Object.keys(yLines)
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                toolbox: {
                    feature: {
                        saveAsImage: {}
                    }
                },
                xAxis: {
                    type: 'category',
                    boundaryGap: false,
                    data: xData
                },
                yAxis: {
                    type: 'value'
                },
                series: []
            };

            for (let s in yLines) {
                option.series.push({
                    name: s,
                    type: 'line',
                    data: yLines[s]
                })
            }

            return option;
        },

        generateRateOptions(weekly_call_count, weekly_error_count){
            // 计算错误率
            let weekly_error_rate = {};
            for (let date in weekly_error_count) {
                weekly_error_rate[date] = Number(weekly_error_count[date] / weekly_call_count[date]) * 100;
                weekly_error_rate[date] = (weekly_error_rate[date]).toFixed(2);
            }
            console.log(weekly_error_rate)



            let options = option = {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'cross',
                        crossStyle: {
                            color: '#999'
                        }
                    }
                },
                legend: {
                    data: ['错误次数', '错误率']
                },
                xAxis: [
                    {
                        type: 'category',
                        data: Object.keys(weekly_error_count),
                        axisPointer: {
                            type: 'shadow'
                        },
                        axisLabel: {
                            rotate: 30
                        }
                    }
                ],
                yAxis: [
                    {
                        type: 'value',
                        name: '错误次数',
                        min: 0,
                        axisLabel: {
                            formatter: '{value} 次'
                        }
                    },
                    {
                        type: 'value',
                        name: '错误率',
                        min: 0,
                        max: 100,
                        axisLabel: {
                            formatter: '{value} %'
                        }
                    }
                ],
                series: [
                    {
                        name: '错误次数',
                        type: 'bar',
                        tooltip: {
                        },
                        data: Object.values(weekly_error_count),
                        label: {
                            show: true,
                            position: 'top',
                            formatter: '{c}次'
                        }
                    },

                    {
                        name: '错误率',
                        type: 'line',
                        yAxisIndex: 1,
                        tooltip: {
                        },
                        data: Object.values(weekly_error_rate),
                        label: {
                            show: true,
                            position: 'top',
                            formatter: '{c}%'
                        }
                    }
                ]
            };

            return options;
        },

        generatePieOptions(items, title){
            let option = {
                title: {
                    text: title,
                    left: 'center'
                },
                tooltip: {
                    trigger: 'item'
                },
                series: [
                    {
                        type: 'pie',
                        radius: '50%',
                        data: items
                    }
                ]
            };
            return option;
        },

        generateBarOptions(data, type){
            let option = {
                tooltip: {},
                xAxis: {
                    type: 'category',
                    data: Object.keys(data)
                },
                yAxis: {
                    type: 'value'
                },
                series: [
                    {
                        data: Object.values(data),
                        type: type ? type : 'bar',
                        smooth: true,
                        label: {
                            show: true,
                            position: 'top',
                            formatter: '{c}'
                        }
                    }
                ]
            };

            return option;
        },
        generateModelCountOptions(models, models_count, scenes) {
            const rawData = models_count;
            const grid = {
                left: 100,
                right: 100,
                top: 50,
                bottom: 75
            };
            const series = scenes.map((name, sid) => {
                return {
                    name,
                    type: 'bar',
                    stack: 'total',
                    barWidth: '60%',
                    label: {
                        show: true
                    },
                    data: rawData[sid].map((d, did) => d)
                };
            });
            return {
                tooltip: {},
                legend: {
                    selectedMode: false
                },
                grid,
                yAxis: {
                    type: 'value'
                },
                xAxis: {
                    type: 'category',
                    data: models,
                    axisLabel: {
                        rotate: 30
                    }
                },
                series
            };
        },
    }
})
