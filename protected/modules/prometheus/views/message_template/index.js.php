<?php
$template = <<<HTML
<div>
    <el-tabs>
        <el-tab-pane label="消息模板">
             <el-form :inline="true"  class="demo-form-inline">
                <el-form-item>
                    <el-button type="primary" @click="addTemplate = true">新增</el-button>
                </el-form-item>
            </el-form>

            <el-table
              :data="template_list"
              style="width: 100%">
              <el-table-column
                prop="id"
                label="消息ID"
              >
              </el-table-column>
              <el-table-column
                prop="aliyun.templateCode"
                label="模板编号"
              >
              </el-table-column>
              <el-table-column
                prop="aliyun.templateContent"
                label="模板内容"
              >
              </el-table-column>
              <el-table-column
                label="操作"
              >
               <template slot-scope="scope">
                  <el-button @click="deleteTemplate(scope.row.id)">删除</el-button>
               </template>
              </el-table-column>
            </el-table>
            <el-pagination
              layout="prev, pager, next"
              :page-size="page_size"
              :current-page.sync="cur_page"
              :total="template_count">
            </el-pagination>
        </el-tab-pane>
    </el-tabs>
    
    <el-dialog
            title="新增模板"
            :visible.sync="addTemplate"
            @close="handleCloseForm"
            width="30%">
        <el-form ref="form" :model="dialogData">
            <el-form-item label="模板ID">
                <el-input v-model="dialogData.id"></el-input>
            </el-form-item>

            <el-form-item label="模板编号">
                <el-input v-model="dialogData.aliyun.templateCode"></el-input>
            </el-form-item>
            <el-form-item label="模板内容">
                <el-input type="textarea" v-model="dialogData.aliyun.templateContent"></el-input>
            </el-form-item>
         
            <el-form-item>
                <el-button type="primary" @click="submit()">新增</el-button>
            </el-form-item>
        </el-form>
    </el-dialog>
</div>
HTML;
?>

Vue.component('template-list', {
    template: `<?php echo $template?>`,
    data: function () {
        return {
            template_list: [],
            template_count: 0,
            cur_page: 1,
            page_size: 10,
            addTemplate: false,
            dialogData: {
                id: '',
                aliyun: {
                    templateCode: '',
                    templateContent: ''
                }
            }
        }
    },
    filters: {},
    watch: {
        cur_page: function () {
            this.searchBtn();
        }
    },
    created: function () {
        this.searchBtn();
    },
    methods: {
        searchBtn: function () {
            const api = axios.create({withCredentials: true});

            const params = {
                page: this.cur_page,
                page_size: this.page_size
            };
            api.get('/prometheus/messageTemplate/list', {params: params})
                .then(function (response) {
                    const data = response.data.data;
                    if (response.data.code === 0) {
                        this.template_list = data.list.data;
                        this.template_count = data.list.total;

                        this.addTemplate = false;
                    } else {
                        this.$message.error(data.msg);
                    }
                }.bind(this)).catch(function (error) {
                this.$message.error('网络错误');
            }.bind(this));
        },
        deleteTemplate: function (id) {
            console.log(id);
            this.$confirm('确认删除此个模板, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                const api = axios.create({withCredentials: true});
                api.post('/prometheus/messageTemplate/delete?id=' + id).then(function (response) {
                    if (response.data.code === 0) {
                        this.$message.success('操作成功');
                        this.clearDialog();
                        this.searchBtn();
                    } else {
                        this.$message.error(response.data.msg);
                    }
                }.bind(this)).catch(function () {
                    this.$message.error('系统错误');
                }.bind(this));
            }).catch(() => {
            });
        },
        submit: function () {
            var _this = this;
            if (this.dialogData.id === '') {
                this.$message.error('模板ID不能为空');
                return;
            }
            if (this.dialogData.aliyun.templateCode === '') {
                this.$message.error('模板编号不能为空');
                return;
            }
            if (this.dialogData.aliyun.templateContent === '') {
                this.$message.error('模板内容不能为空');
                return;
            }

            const api = axios.create({withCredentials: true});
            api.post('/prometheus/messageTemplate/create', this.dialogData).then(function (response) {
                if (response.data.code === 0) {
                    this.clearDialog();
                    this.searchBtn();
                    this.$message.success('操作成功');
                } else {
                    this.$message.error(response.data.msg);
                }
            }.bind(this)).catch(function () {
                this.$message.error('系统错误');
            }.bind(this));
        },
        handleCloseForm: function () {
            this.addTemplate = false;
            this.clearDialog();
        },
        clearDialog: function () {
            this.dialogData.id = '';
            this.dialogData.aliyun.templateCode = '';
            this.dialogData.aliyun.templateContent = '';
        }
    }
})
