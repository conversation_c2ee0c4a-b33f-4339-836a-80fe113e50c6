<?php
\common\modules\prometheus\library\VueUtil::loadComponent(['siteTrack/component/event_view.js']);

$template = <<<HTML
<div>
    <el-form :inline="true" class="demo-form-inline">
        <el-form-item label="">
            <el-form-item label="clientId">
                <el-input v-model="clientId" placeholder="输入client_id"></el-input>
            </el-form-item>
            <el-form-item label="siteId">
                <el-input v-model="siteId" placeholder="输入site_id"></el-input>
            </el-form-item>             
            <el-form-item label="sessionId">
                <el-input v-model="sessionId" placeholder="输入sessionId"></el-input>
            </el-form-item>
            <el-form-item label="IP">
                <el-input v-model="ip" placeholder="输入ip"></el-input>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="refresh">查询</el-button>
            </el-form-item>
            <el-form-item label="开始日期">
                <el-date-picker v-model="startDate" type="date" placeholder="选择日期" value-format="yyyy-MM-dd">
                </el-date-picker>
            </el-form-item>
            <el-form-item label="结束日期">
                <el-date-picker v-model="endDate" type="date" placeholder="选择日期" value-format="yyyy-MM-dd">
                </el-date-picker>
            </el-form-item>

        </el-form-item>
    </el-form>
    
     <el-table
          :data="list"
          v-loading="loading"
          :row-class-name="tableRowClassName"
          @row-click="clickRow"
          style="width: 100%">
          <el-table-column
            prop='site_session_id'
            label="site_session_id"
            width="100">
          </el-table-column>
          <el-table-column
            prop='site_id'
            label="site_id"
            width="100">
          </el-table-column>
          <el-table-column
            prop="device_id"
            label="device_id"
            :show-overflow-tooltip="true"	
            width="100">
          </el-table-column>
          <el-table-column
            prop="gclid"
            label="gclid"
            :show-overflow-tooltip="true"
            width="100">
          </el-table-column>
          <el-table-column
            prop="domain"
            label="域名"
            :show-overflow-tooltip="true"
            width="100">
          </el-table-column>
           <el-table-column
            prop="view_count"
            label="页面数"
            width="100">
          </el-table-column>
           <el-table-column
            prop="click_count"
            label="点击数"
            width="100">
          </el-table-column>
           <el-table-column
            prop="form_count"
            label="表单数"
            width="100">
          </el-table-column>
           <el-table-column
            prop="exit_count"
            label="关闭数"
            width="100">
          </el-table-column>
           <el-table-column
            prop="heartbeat_count"
            label="心跳数"
            width="100">
          </el-table-column>
           <el-table-column
            prop="language"
            label="语言"
            width="100">
          </el-table-column>
           <el-table-column
            prop="ip"
            label="ip"
            width="100">
          </el-table-column>
           <el-table-column
            prop="platform"
            label="系统"
            width="100">
          </el-table-column>
          <el-table-column
            prop="browser"
            label="浏览器"
            width="100">
          </el-table-column>
          <el-table-column
            prop="device_type"
            label="设备类型"
            width="100">
          </el-table-column>
          <el-table-column
            label="屏幕"
            width="100">
            <template slot-scope="scope">
              {{ scope.row.screen_width}} *  {{ scope.row.screen_height}} 
            </template>
          </el-table-column>
          <el-table-column
            prop="origin_refer_url"
            label="来源地址"
            :show-overflow-tooltip="true"
            width="100">
          </el-table-column>
          
          <el-table-column
            prop="start_time"
            label="开始访问"
            width="180">
          </el-table-column>          
          <el-table-column
            prop="end_time"
            label="结束访问"
            width="180">
            <template slot-scope="scope">
            {{ scope.row|endTime}}
            </template>
          </el-table-column>
          <el-table-column
            label="访问时长"
            width="180">
            <template slot-scope="scope">
            {{ scope.row|timeFormat}}
            </template>
          </el-table-column>
        </el-table>            
        
        <el-pagination
            layout="prev, pager, next"
            :page-size="pageSize"
            :current-page.sync="curPage"
            :total="count"
            >
          </el-pagination>
        
        
        <eventView ref="eventView" v-bind:siteSessionId="selectSiteSessionId" v-bind:clientId="selectClientId" v-bind:siteId="selectSiteId"></eventView>
</div>
HTML;
?>

var  siteSessionViewComponents = {
    template: `<?php echo $template?>`,
    components: {
        eventView: eventViewComponents
    },
    data: function () {
        return {
            selectSiteSessionId: '',
            selectClientId: '',
            selectSiteId: '',
            loading: false,
            clientId: '',
            siteId: '',
            sessionId:'',
            ip:'',
            startDate: formatTime((new Date).setDate((new Date()).getDate() - 10), 'yyyy-MM-dd'),
            endDate: formatTime(new Date(), 'yyyy-MM-dd'),
            curPage:1,
            pageSize:20,
            list: [],
            count: 0
        }
    },
    created: function () {
    },
    watch: {
        selectDate: function () {
            this.curPage=1
            this.pageSize=20;
            this.refresh();
        },
        curPage: function() {
            this.refresh();
        }
    },
    methods: {
        refresh: function () {
            if( this.clientId.length == 0 )
            {
                this.$message.error('sid不能为空');
            }

            this.loading= true
            this.$nextTick(function () {
                this.$http.get('/prometheus/siteTrack/siteSessionList?client_id=' + this.clientId + '&site_id='+this.siteId +'&site_session_id='+this.sessionId+'&ip='+this.ip +'&start_time='+this.startDate+ '&end_time='+ this.endDate + '&cur_page=' + this.curPage+'&page_size='+this.pageSize)
                    .then(function (response) {
                        if (response.data.code == 0 && response.data.data) {
                            this.list = response.data.data.list;
                            this.count = response.data.data.count;
                            if( this.list.length ===0  && this.curPage >1 )
                                this.$message.error('该页没有数据了');
                        }
                        else {
                            this.$message.error(response.data.msg);
                        }

                        this.loading = false
                    }).catch(function (reason) { this.loading = false });
            })

        },
        tableRowClassName: function({row, rowIndex}) {
            if (row.is_default ==1)
                return 'warning-row';

            return '';
        },
        clickRow: function (selection) {
            console.log(selection)
            this.selectSiteSessionId = selection.site_session_id
            this.selectClientId = selection.client_id
            this.selectSiteId = selection.site_id
            this.$refs.eventView.show();
        }
    },
    filters: {
        prettyJson: function (data) {
            return JSON.stringify(data, null, 2);
        },
        endTime: function (row) {
            if( row.start_time == row.end_time)
                return '--';
            return row.end_time;
        },
        timeFormat: function (row) {
            return ''
        }
    }
}

// 日期格式化
function formatTime(time, format) {
    var t = new Date(time);
    var tf = function(i){return (i < 10 ? '0' : '') + i};
    return format.replace(/yyyy|MM|dd|HH|mm|ss/g, function(a){
        switch(a){
            case 'yyyy':
                return tf(t.getFullYear());
                break;
            case 'MM':
                return tf(t.getMonth() + 1);
                break;
            case 'mm':
                return tf(t.getMinutes());
                break;
            case 'dd':
                return tf(t.getDate());
                break;
            case 'HH':
                return tf(t.getHours());
                break;
            case 'ss':
                return tf(t.getSeconds());
                break;
        }
    })
}
