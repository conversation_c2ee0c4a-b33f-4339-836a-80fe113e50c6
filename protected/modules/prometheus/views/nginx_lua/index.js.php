<?php

//include 'lua_config_view.js.php';
include 'lua_config_grey_setting.js.php';
include 'lua_config_apply_setting.js.php';
//include 'lua_config_fpm_setting.js.php';
//include 'lua_config_router_setting.js.php';
include 'lua_config_apply_confirm.js.php';
include 'lua_config_envoy_sync.js.php';
include 'lua_config_envoy_config.js.php';
include 'lua_config_envoy_router.js.php';
include 'lua_config_envoy_second_router.js.php';
include 'lua_config_envoy_rate_limit_path.js.php';
//include 'lua_config_app_desktop_grey_setting.js.php';
include 'lua_config_parse_params_setting.js.php';
include 'lua_config_apply_batch_list.js.php';
include 'remote_envoy_config.js.php';

$template = <<<HTML
<div style="position: relative">
    <el-select v-model="selectDc" placeholder="数据中心" style="position: absolute; right: 0; top: -12px; z-index: 1">
        <el-option
                label="数据中心"
                v-for="(item, index) in dcList"
                :key="index"
                :label="item.name"
                :value="item.dc_id">
        </el-option>
    </el-select>
    <el-row>
        <el-col>
            <div class="grid-content bg-purple-dark" style="text-align: center;color: red;">当前数据中心：{{showDcName}}</div>
        </el-col>
    </el-row>
    <el-tabs v-model='activeName' @tab-click="tabClick">
<!--        <el-tab-pane label="线上配置" name="luaConfig">-->
<!--            <luaConfigView ref="luaConfigView" :select-dc="selectDc" :all-system-list="allSystemList"></luaConfigView>-->
<!--        </el-tab-pane>-->
        
        <el-tab-pane label="灰度设置" name="greySetting">
            <greySetting ref="greySetting" :select-dc="selectDc" :all-system-list="allSystemList"></greySetting>
        </el-tab-pane>   
         
<!--        <el-tab-pane label="灰度匹配规则设置-app/desktop" name="conditionGreySetting">-->
<!--            <conditionGreySetting ref="conditionGreySetting" :select-dc="selectDc" :system-list="systemList"></conditionGreySetting>-->
<!--        </el-tab-pane>-->
        
        <el-tab-pane label="参数解析配置-callback/inner" name="parseParamsRuleSetting">
            <parseParamsRuleSetting ref="parseParamsRuleSetting" :select-dc="selectDc" :internal-system-list="internalSystemList"></parseParamsRuleSetting>
        </el-tab-pane>   
            
        <el-tab-pane label="执行设置" name="applySetting">
            <applySetting ref="applySetting" :select-dc="selectDc" :all-system-list="allSystemList"></applySetting>
        </el-tab-pane>
        
<!--        <el-tab-pane label="机器设置" name="fpmSetting">-->
<!--            <fpmSetting ref="fpmSetting" :select-dc="selectDc" :all-system-list="allSystemList"></fpmSetting>-->
<!--        </el-tab-pane>-->
        
<!--        <el-tab-pane label="请求路由设置" name="routerSetting">-->
<!--            <routerSetting ref="routerSetting" :select-dc="selectDc" :all-system-list="allSystemList"></routerSetting>-->
<!--        </el-tab-pane>-->
       
         <el-tab-pane label="Envoy网关配置" name="envoyConfig">
            <envoyConfig ref="envoyConfig" :select-dc="selectDc" :all-system-list="allSystemList"></envoyConfig>
        </el-tab-pane>
        
        <el-tab-pane label="Envoy路由设置" name="envoyRouter">
            <envoyRouter ref="envoyRouter" :select-dc="selectDc" :select-dpreviewEnvoySettingc="selectDc"></envoyRouter>
        </el-tab-pane>
        
        <el-tab-pane label="Envoy技术灰度" name="envoySecondRouter">
            <envoySecondRouter ref="envoySecondRouter" :select-dc="selectDc" :all-system-list="allSystemList"></envoySecondRouter>
        </el-tab-pane>
        
        <el-tab-pane label="Envoy限流路径" name="envoyRateLimitPath">
            <envoyRateLimitPath ref="envoyRateLimitPath" :select-dc="selectDc" :select-dpreviewEnvoySettingc="selectDc"></envoyRateLimitPath>
        </el-tab-pane>
         
<!--        <el-tab-pane label="预览Envoy配置" name="envoySync">-->
<!--            <envoySync ref="envoySync" :select-dc="selectDc"></envoySync>-->
<!--        </el-tab-pane>    -->
        
        <el-tab-pane label="Envoy线上配置" name="remoteEnvoyConfig">
            <remoteEnvoyConfig ref="remoteEnvoyConfig" :select-dc="selectDc" :all-system-list="allSystemList"></remoteEnvoyConfig>
        </el-tab-pane>  
                
        <el-tab-pane label="预览Envoy配置&生效" name="applyConfirm">
            <applyConfirm ref="applyConfirm" :all-system-list="allSystemList" :select-dc="selectDc" :config-describe-list="configDescribeList"  :access-set-grey-system-list="accessSetGreySystemList"></applyConfirm>
        </el-tab-pane>
        
        <el-tab-pane label="生效记录列表" name="applyBatchList">
            <applyBatchList ref="applyBatchList"/>
        </el-tab-pane>
        

    </el-tabs>
</div> 
HTML;
?>

Vue.component('page-lua-config', {
    template: `<?php echo $template?>`,
    components: {
        //luaConfigView: luaConfigViewComponents,
        greySetting: luaConfigGreySetting,
        applySetting: luaConfigApplySetting,
        //fpmSetting: luaConfigFpmSetting,
        //routerSetting: luaConfigRouterSetting,
        applyConfirm: luaConfigApplyConfirm,
        applyBatchList: luaConfigApplyBatchList,
        envoySync: luaConfigEnvoySync,
        remoteEnvoyConfig: remoteEnvoyConfig,
        envoyConfig: luaConfigEnvoyConfig,
        envoyRouter: luaConfigEnvoyRouter,
        envoySecondRouter: luaConfigEnvoySecondRouter,
        envoyRateLimitPath: luaConfigEnvoyRateLimitPath,
        //conditionGreySetting: luaConfigConditionGreySetting,
        parseParamsRuleSetting: luaParseParamsRuleSetting
    },
    data: function () {
        return {
            activeName: 'greySetting',
            showDcName: 'cn',
            dcList: [],
            configDescribeList: [],
            systemList: [],
            internalSystemList: [],
            allSystemList: [],
            accessSetGreySystemList: [],
            selectDc: 1,
        }
    },
    created: function () {
        this.$http.get('/prometheus/nginx/filterList')
            .then(function (response) {
                if (response.data.code == 0 && response.data.data) {
                    this.dcList = response.data.data.dc_list;
                    this.configDescribeList = response.data.data.config_describe_list;
                    this.systemList = response.data.data.system_list;
                    this.internalSystemList = response.data.data.internal_system_list;
                    this.allSystemList = response.data.data.all_system_list;
                    this.accessSetGreySystemList = response.data.data.access_set_grey_system_list;
                }
                else {
                    this.$message.error(response.data.msg);
                }
            });
    },
    watch: {
        selectDc: function () {
            this.showDcName = this.dcList[this.selectDc].name;
            this.refreshByTabName(this.activeName);
        }
    },
    methods: {
        tabClick: function (tab) {
            this.refreshByTabName(tab.name)
        },
        refreshByTabName: function (name) {
            if (name == 'greySetting')
                this.$refs.greySetting.refreshGreySetting();
            else if (name == 'applySetting')
                this.$refs.applySetting.refreshApplySetting();
            else if (name == 'applyConfirm')
                this.$refs.applyConfirm.previewEnvoySetting();
            else if (name == 'applyBatchList')
                this.$refs.applyBatchList.getBatchList();
            else if (name == 'envoyRouter')
                this.$refs.envoyRouter.refreshRouterSetting();
            else if (name == 'envoySecondRouter')
                this.$refs.envoySecondRouter.refreshRouterSetting();
            else if (name == 'envoyRateLimitPath')
                this.$refs.envoyRateLimitPath.refreshRateLimitPath();
            else if (name == 'envoyConfig')
                this.$refs.envoyConfig.refreshSetting();
            else if (name == 'envoySync')
                this.$refs.envoySync.previewEnvoySetting();
            else if (name == 'remoteEnvoyConfig')
                this.$refs.remoteEnvoyConfig.remoteEnvoySetting();
            else if (name == 'parseParamsRuleSetting')
                this.$refs.parseParamsRuleSetting.refreshParseParamsSetting();
            //else if (name == 'luaConfig')
            //        this.$refs.luaConfigView.refreshLuaConfig();
            //else if (name == 'fpmSetting')
            //    this.$refs.fpmSetting.refreshFpmSetting();
            //else if (name == 'conditionGreySetting'){
            //    this.$refs.conditionGreySetting.refreshSetting();
            //}
            //else if (name == 'routerSetting')
            //    this.$refs.routerSetting.refreshRouterSetting();
        }
    },
    filters: {}
})
