<?php
/**
 * Created by PhpStorm.
 * Author: <PERSON>(<PERSON><PERSON><PERSON>)
 * Date: 2022/3/2
 * Time: 3:31 下午
 */
include 'export.js.php';

$template = <<<HTML
<div>
<exportDialog :visible="showExportDialog" :relation_id="relation_id" :plan_id="plan_id" :batch_id="batch_id" @close="closeExportDialog"></exportDialog>
<el-row>
    <el-col>
        <el-button icon="el-icon-bach" size="small" @click="getReturnUrl">返回</el-button></a>
    </el-col>
</el-row>
<el-row :gutter="20" style="margin: 20px" v-if="plan_id === undefined">
    <el-col :span="6"><el-progress type="circle" :percentage="calculateExecuteProcess" :status="batch_percentage_status"/></el-col>
    <el-col :span="5" style="height: 126px; margin-top: 50px">
        <div>正在处理：{{batch_stat.successRecordNum+batch_stat.failRecordNum}}/{{batch_stat.totalRecordNum}}</div>
    </el-col>
    <el-col :span="8" style="height: 126px;margin-top: 50px">
        <div>任务名称：{{task_name}}</div>
    </el-col>
    <el-col :span="5" style="height: 126px;margin-top: 50px"><div>失败{{batch_stat.failRecordNum}}个</div></el-col>
</el-row>
<el-row :gutter="20" style="margin: 20px" v-if="plan_id === undefined">
    <el-col :span="3">执行代码快照: </el-col>
    <el-col :span="21"><a :href=git_link target="_blank">{{git_link}}</a></el-col>
</el-row>
<div v-if="plan_id != undefined">
    <el-row :gutter="20" style="margin: 20px">
        <el-col :span="6"><div>执行规则: {{plan.dispatch_type}}</div></el-col>
        <el-col :span="18"><el-progress :percentage="calculatePlanPercentage" :status="calculatePlanExecuteProcess"/></el-col>
    </el-row>
    <el-tabs v-model="active_dc_tab" @tab-click="handleDcTabClick">
        <el-tab-pane v-for="(dc_name,dc_id) in filtered_dc_config_list" :key="dc_id" :label="dc_name" :name="dc_id">
        </el-tab-pane>
    </el-tabs>
    <el-collapse v-model="activeCollapse">
        <el-collapse-item title="批次执行进度" name="1">
            <ul>
                <template v-for="(item, index) in batch_list">
                    <el-row :key="index" :gutter="10" justify="space-around" align="middle" style="margin: 20px" v-if="item.dc_id == active_dc_tab">
                        <el-col :span="5">{{item.task_name}}</el-col>
                        <el-col :span="15" style="margin: 0px"><el-progress :percentage="item.percentage" :status="item.status"/></el-col>
                        <el-col :span="2"><el-tag :type="item.tag_type" size="small">{{item.execute_status_text}}</el-tag></el-col>
                        <el-col :span="2">
                            <el-button icon="el-icon-close" circle size="mini" type="warning"
                             @click="interruptBatch(item.batch_id)"
                             v-if="item.status == 2"/>
                            </el-col>
                    </el-row>
                    <el-row :gutter="10" justify="space-around" align="middle" style="margin: 20px" v-if="item.git_info && item.dc_id == active_dc_tab">
                        <el-col :span="5">执行代码快照: </el-col>
                        <el-col :span="19"><a :href=item.git_info.link target="_blank">{{item.git_info.link}}</a></el-col>
                    </el-row> 
                </template>
            </ul>
        </el-collapse-item>
    </el-collapse>
</div>
<div style="margin: 20px" align="center">明细数据非实时数据，执行结束后会自动刷新，如执行中需要查看请点击查询按钮</div>
<el-form :inline="true" align="center">
    <el-form-item label="执行状态">
        <el-select v-model="query_status" clearable @change="resetRecordPage">
            <el-option
                v-for="item in executeStatus"
                :key="item.value"
                :label="item.label"
                :value="item.value"/>
        </el-select>
    </el-form-item>
    <el-form-item label="任务名称" v-if="plan_id !== undefined">
        <el-select
            v-model="query_task"
            clearable fiterable
            @change="resetRecordPage">
            <el-option 
                v-for="item in query_task_list"
                :key="item.task_id"
                :value="item"
                :label="item.task_name"/>
        </el-select>
    </el-form-item>
    <el-form-item><el-button icon="el-icon-search" type="primary" @click="getRecordList"></el-button></el-form-item>
    <el-form-item>
        <el-button 
            icon="el-icon-refresh-left"
            type="danger"
            @click="reRunPlan"
            v-if="plan_id != undefined && plan_status != 2"
            :disabled="plan.execute_status == 3">失败重跑</el-button>
        <el-button 
            icon="el-icon-refresh-left"
            type="danger"
            @click="reRunBatch"
            v-if="plan_id == undefined && is_last_executed && batch_status != 2"
            :disabled="batch_status == 3">失败重跑</el-button>
    </el-form-item>
    <el-form-item>
        <el-button 
            icon="el-icon-video-pause"
            v-if="plan_status == 2 && plan_id != undefined"
            type="warning"
            @click="interruptPlan">停止执行</el-button>
    </el-form-item>
    <el-form-item>
        <el-button
            icon="el-icon-video-pause"
            v-if="plan_id == undefined && batch_status == 2"
            type="warning"
            @click="interruptBatch(batch_id)">停止执行</el-button>
    </el-form-item>
    <el-form-item>
        <el-button icon="el-icon-download"
            type="success"
            @click="exportRecord"
        >导出明细</el-button>
    </el-form-item>
</el-form>
<el-table :data="record_list" style="width:100%">
    <el-table-column prop="task_class" label="taskClass" fixed width="100"/>
    <el-table-column prop="dispatch_refer_id" label="执行关联Id" width="120"/>
    <el-table-column prop="record_id" label="明细Id" width="100"/>
    <el-table-column prop="dc_id" label="数据中心" width="80">
        <template slot-scope="scope">
            {{dc_config_list[scope.row.dc_id]}}
        </template>
    </el-table-column>
    <el-table-column prop="task_principal" label="任务负责人" width="180"/>
    <el-table-column prop="plan_description" label="计划说明" width="150"/>
    <el-table-column prop="create_user" label="执行人" width="180"/>
    <el-table-column prop="create_time" label="执行开始时间" width="160"/>
    <el-table-column prop="execute_end_time" label="执行结束时间" width="160" align="center" :formatter="formatExecuteTime"/>
    <el-table-column prop="error_msg" label="失败信息" min-width="200"/>
    <el-table-column prop="execute_status" fixed="right" label="执行状态" width="100" align="center">
        <template slot-scope="scope">
            <el-tag v-if="scope.row.execute_status==1">未执行</el-tag>
            <el-tag type="warning" v-if="scope.row.execute_status==2">执行中</el-tag>
            <el-tag type="success" v-if="scope.row.execute_status==3">成功</el-tag>
            <el-tag type="danger" v-if="scope.row.execute_status==4">失败</el-tag>
        </template>
    </el-table-column>
</el-table>
<el-pagination
    @size-change="handleSizeChange"
    @current-change="handleCurrentChange"
    :current-page="page"
    :page-size="page_size"
    layout="total, prev, pager, next"
    :total="total"
></el-pagination>
</div>
HTML;

echo <<<JS
Vue.component('batch-info', {
    template: `$template`,
    components: {
        exportDialog: exportDialog
    },
    data: function () {
        return {
            tab: undefined, // 一级目录
            active_tab: undefined, // 二级目录
            return_to: undefined,
            source: undefined,
            
            page_size: 20,
            page: 1,
            total: 0,

            showExportDialog: false,

            interrupt_signal: false,            
            execute_process: 0,
            batch_percentage_status: undefined,
            batch_stat: {
                failRecordNum: 0,
                successRecordNum: 0,
                totalRecordNum: 0,
            },
            batch_status: 1,
            is_last_executed: false,
            task_name: '',
            git_link: '',
            
            activeCollapse: [],
            plan_id: undefined,
            plan: {
            },
            plan_status: undefined,
            batch_id: undefined,
            batch_list: [],
            batch_stat_list: [],
            relation_id: undefined,
            record_list: [],
            
            query_status: undefined,
            active_dc_tab: '',
            query_task_list: [],
            query_task: {},
            
            executeStatus: [
                {
                    label: '未执行',
                    value: 1,
                },
                {
                    label: '执行中',
                    value: 2,
                }, 
                {
                    label: '成功',
                    value: 3,
                }, 
                {
                    label: '失败',
                    value: 4
                }
            ],
            recordErrorList: [],
            dc_config_list : {}, //{1:'cn',2:'sg'}
            filtered_dc_config_list: {} // Filtered DC list based on batch_list
        }
    },
    created: function () {
        this.init();
        this.getDcConfigList();
    },
    beforeDestroy: function() {
        if(this.source) {
            this.source.close();
        }
    },
    watch: {
        plan_status(newVal, oldVal) {
            if (oldVal == 2) {
                this.getRecordList();
            }
        },
        batch_status(newVal, oldVal) {
            if (oldVal == 2) {
                this.getRecordList();
            }
        },
        active_dc_tab(newVal, oldVal) {
            if(newVal) {
                this.initSSE();
            }
        }
    }, 
    computed: {
        calculateExecuteProcess: function () {
            result = 0;
            switch (this.batch_status) {
                case 1:
                    return 0;
                case 3:
                    result = 1;
                    this.batch_percentage_status = 'success';
                    break;
                case 4:
                    this.batch_percentage_status = 'exception';
                    result = this.batch_stat.totalRecordNum==0?1:this.batch_stat.failRecordNum/this.batch_stat.totalRecordNum
                    break;
                case 5:
                    this.batch_percentage_status = 'warning';
                    result = this.batch_stat.failRecordNum/this.batch_stat.totalRecordNum
                    break;
                default:
                    result = (this.batch_stat.successRecordNum+this.batch_stat.failRecordNum)/this.batch_stat.totalRecordNum;
                    break;
            }
            return Math.round(result*100);
        },
        calculatePlanExecuteProcess: function () {
            switch (this.plan_status) {
                case 3:
                    return 'success';
                case 4:
                    return 'warning';
                case 5:
                    return 'exception';
                default:
                    return;
            }
        },
        calculatePlanPercentage: function () {
            if (this.batch_stat_list.length != 0) {
                this.plan.totalRecordNum = 0;
                this.plan.executeRecordNum = 0;
                for (var batch of this.batch_list) {
                    var stat = this.batch_stat_list.find(item => item.id == batch.batch_id);
                    if (stat == undefined) {
                        batch.percentage = 0;
                        continue;
                    }
                    
                    this.plan.totalRecordNum += stat.totalRecordNum;
                    this.plan.executeRecordNum += stat.successRecordNum+stat.failRecordNum;
                    
                    batch.execute_status = Number(stat.batchStatus);
                    if (batch.execute_status == 1) {
                        batch.percentage = 0;
                    } else {
                        batch.percentage = stat.totalRecordNum==0?100:Math.round((stat.successRecordNum+stat.failRecordNum)/stat.totalRecordNum*100);
                    }
                    switch (batch.execute_status) {
                        case 1:
                            batch.execute_status_text = "未执行";
                            batch.tag_type = "info";                  
                            break;
                        case 2:
                            batch.execute_status_text = "执行中";
                            batch.tag_type = "warning";
                            break;
                        case 3:
                            batch.status = 'success';
                            batch.execute_status_text = "成功";
                            batch.tag_type = "success";
                            break;
                        case 4:
                            batch.status = 'exception';
                            batch.execute_status_text = "失败";
                            batch.tag_type = 'danger';
                            break;
                        case 5:
                            batch.status = 'warning';
                            batch.execute_status_text = "部分失败";
                            batch.tag_type = "info";
                            break;
                        default:
                            break;
                        }
                }
                
                if (this.plan.totalRecordNum == 0) {
                    return 0;
                } else {
                    return Math.round(this.plan.executeRecordNum/this.plan.totalRecordNum*100);
                }
            } else {
                return 0;
            }
        },
    },
    methods: {
        getReturnUrl() {
            postData = {
                return_to: this.return_to,
                script_relation_id: this.relation_id
            };
            if (this.active_tab != undefined) {
                postData.active_tab = this.active_tab;
            }
            if (this.tab != undefined) {
                postData.tab = this.tab;
            }
            this.\$http.post("/prometheus/script/redirect", postData).then(function (response) {
                if (response.data.code == 0) {
                    window.location.href = response.data.data;
                    window.location.reload();
                } else {
                    this.\$message.error(response.data.msg)
                }
            })
        },
        // style
        formatExecuteTime(row, column, cellValue, index) {
            if (cellValue === '1970-01-01 00:00:01') {
                return '-';
            } else {
                return cellValue;
            }
        },
        
        initSSE() {
            if(this.source) {
                this.source.close();
            }
            
            if(typeof(EventSource)!=="undefined") {
                let that = this;
                sourceUrl = "/prometheus/script/";
                if (this.plan_id !== undefined) {
                    sourceUrl += 'planExecuteStatus?plan_id='+this.plan_id;
                    this.source = new EventSource(sourceUrl);
                    this.source.onmessage = function(event) {
                        response = JSON.parse(event.data);
                        that.batch_stat_list = Object.values(response.batchStatList);
                        that.plan_status = Number(response.planExecuteStatus);
                        if (that.plan_status != 2) {
                            that.source.close();
                        }
                    };
                } else {
                    sourceUrl += "batchExecuteStatus?batch_id="+this.batch_id+"&dc_id="+this.active_dc_tab;
                    this.source = new EventSource(sourceUrl);
                    // 连接异常时会触发 error 事件并自动重连
                    this.source.onmessage = function(event) {
                        response = JSON.parse(event.data);
                        that.batch_stat = response;
                        that.batch_status = Number(response.batchStatus);
                        // 不是执行中就关闭链接
                        if (that.batch_status != 2) {
                            that.source.close();
                        }
                    };
                }
                
                this.source.addEventListener('queryerror', function (event) {
                    that.\$message.error(event.data);
                    that.getRecordList();
                    that.source.close();
                });
                
                setTimeout(function () {
                    that.source.close();
                }, 3600*1000); // 一小时后停止前端停止请求脚本执行状态
            } else {
                this.\$message.error("浏览器不支持EventSource, 请手动刷新页面获取最新数据！");
            }
        },
        init() {
            this.return_to = this.\$route.query.return_to;
            this.active_tab = this.\$route.query.active_tab;
            this.tab = this.\$route.query.tab;
            this.plan_id = this.\$route.query.plan_id;
            this.batch_id = this.\$route.query.batch_id;
            this.relation_id = this.\$route.query.relation_id;
            
            if (this.plan_id !== undefined) {
                this.\$http.get('/prometheus/script/getPlan?plan_id='+this.plan_id+"&show_last_executed_task=1")
                .then(function (response) {
                    if (response.data.code === 0 && response.data.data) {
                        this.plan = response.data.data;
                        this.query_task_list = this.plan.last_execute_task;
                        this.query_task_list = this.query_task_list.map(function (item) { 
                            return {
                                value: item.task_name,
                                task_name: item.task_name,
                                task_id: item.task_id
                            }
                        })
                        this.getBatchList();
                    } else {
                        this.\$message.error(response.data.msg)
                    }
                })
            } else {
                url = '/prometheus/script/getBatchInfo?batch_id='+this.batch_id;
                this.\$http.get(url).then(function (response) {
                    if (response.data.code === 0 && response.data.data) {
                        batchInfo = response.data.data.batch;
                        this.git_link = batchInfo.git_info?batchInfo.git_info.link:'';
                        this.task_name = batchInfo.task_name;
                        this.is_last_executed = response.data.data.is_last_executed;
                        this.batch_status = Number(batchInfo.execute_status);
                    }
                });
            }
        },
        getBatchList() {
            url = '/prometheus/script/getBatchList?page_size=-1';
            this.\$http.post(url, {
                relation_id: this.relation_id,
                batch_id: this.plan.last_execute_batch
            }).then(function (response) {
                if (response.data.code === 0 && response.data.data) {
                    this.batch_list = response.data.data.list;
                    // Filter dc_config_list based on batch_list dc_ids
                    const batchDcIds = new Set(this.batch_list.map(batch => batch.dc_id));
                    this.filtered_dc_config_list = Object.keys(this.dc_config_list).reduce((acc, dcId) => {
                        if (batchDcIds.has(dcId)) {
                            acc[dcId] = this.dc_config_list[dcId];
                        }
                        return acc;
                    }, {});
                    // Set default active tab to first available DC
                    if(Object.keys(this.filtered_dc_config_list).length > 0) {
                        this.active_dc_tab = Object.keys(this.filtered_dc_config_list)[0];
                        this.getRecordList();
                    }
                } else {
                    this.\$message.error(response.data.msg);
                }
            })
        },
        getDcConfigList() {
            this.\$http.get('/prometheus/scriptComm/getDcConfigList')
            .then(function (response) {
                if (response.data.code === 0 && response.data.data) {
                    this.dc_config_list = response.data.data.reduce((acc, cur) => {
                        acc[cur.dc_id] = cur.dc_name;
                        return acc;
                    }, {});
                } else {
                    this.\$message.error(response.data.msg);
                }
            })
        },
        handleDcTabClick(tab) {
            this.active_dc_tab = tab.name;
            this.getRecordList();
        },
        resetRecordPage() {
            this.page = 1;
        },
        getRecordList() {
            url = '/prometheus/script/getExecutionRecordList'
            postData = {
                page_size: this.page_size,
                page: this.page,
                dc_id: this.active_dc_tab
            };
            if (this.plan_id != undefined) {
                postData.plan_id = this.plan_id;
            } else {
                postData.batch_id = this.batch_id;
            }
            if (this.query_status != undefined && this.query_status != '') {
                postData.execution_status = [this.query_status];
            }
            if (this.query_task.task_name != undefined && this.query_task.task_name != '') {
                postData.task_id = this.query_task.task_id;
            }
            this.\$http.post(url, postData).then(function (response) {
                if (response.data.code === 0 && response.data.data) {
                    this.record_list = response.data.data.list;
                    this.total = response.data.data.total;
                } else {
                    this.\$message.error('请求执行明细列表失败！');
                }
            })
        },
        reRunPlan() {
            this.\$http.get('/prometheus/script/reRunPlan?plan_id='+this.plan_id)
            .then(function (response) {
                if (response.data.code === 0) {
                    window.location.reload();
                } else {
                    this.\$message.error(response.data.msg);
                }
            })
        },
        reRunBatch() {
            this.\$http.get('/prometheus/script/reRunBatch?batch_id='+this.batch_id+"&relation_id="+this.relation_id)
            .then(function (response) {
                if (response.data.code === 0) {
                    window.location.reload();
                } else {
                    this.\$message.error(response.data.msg)
                }
            })
        },
        interruptPlan() {
            this.interrupt_signal = true;
            this.\$http.get('/prometheus/script/interruptPlan?plan_id='+this.plan_id)
            .then(function (response) {
                if (response.data.code !== 0) {
                    this.\$message.error(response.data.msg)
                }
            })
        },
        interruptBatch(batch_id) {
            this.interrupt_signal = true;
            this.\$http.get('/prometheus/script/interruptBatch?batch_id='+batch_id)
            .then(function (response) {
                if (response.data.code !== 0) {
                    this.\$message.error(response.data.msg)
                }
            })
        },
        handleSizeChange(pageSize) {
            this.page_size = pageSize;
            this.handleCurrentChange(this.page);
        },
        handleCurrentChange(currentPage) {
            this.page = currentPage;
            this.getRecordList();
        },
        exportRecord() {
            this.showExportDialog = true;
        },
        closeExportDialog() {
            this.showExportDialog = false;
        }
    }
})
JS;

?>