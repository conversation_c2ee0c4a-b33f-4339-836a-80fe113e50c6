<?php

namespace common\modules\prometheus\library\ahas;

use AlibabaCloud\Client\AlibabaCloud;
use common\library\account\service\DbService;
use <PERSON><PERSON>\Sidekick\Config\ConfigLoader;

class AhasService
{

    protected $accessKeyId;
    protected $accessKeySecret;

    protected $defaultSlowRtMs = 1800;
    protected $defaultThreshold = '0.6';

    protected $template = [
        'single_db' => [
            'Strategy' => "0", // 0:慢调用比例 1:异常比例
            'Threshold' => "0.6", //阈值类型所对应的降级阈值，超过该值时会触发接口的降级。
            'RecoveryTimeoutMs' => 10 * 1000, //熔断时长。在该时间段内，该接口的请求都会快速失败，单位毫秒。
            'StatDurationMs' => 20 * 1000 , //统计窗口时长，单位毫秒。默认值为1000。
            'SlowRtMs' => 1800, // 慢调用RT。请求的响应时间超过该值时统计为慢调用。阈值类型为“慢调用比例”时需设置该字段。我们数据库链接超时配置是2秒
            'MinRequestAmount' => 100, //触发熔断的最小请求数目，若当前统计窗口请求数小于此值，即使达到熔断条件规则也不会触发。默认值为5。
            'HalfOpenBaseAmountPerStep' => 100, //熔断恢复每步最小通过数目，默认值为5。
            'HalfOpenRecoveryStepNum' => 1, //熔断恢复阶段数，默认值为1。
        ],
        'db_proxy' => [
            'Strategy' => "0", // 0:慢调用比例 1:异常比例
            'Threshold' => "0.6", //阈值类型所对应的降级阈值，超过该值时会触发接口的降级。
            'RecoveryTimeoutMs' => 10 * 1000, //熔断时长。在该时间段内，该接口的请求都会快速失败，单位毫秒。
            'StatDurationMs' => 20 * 1000 , //统计窗口时长，单位毫秒。默认值为1000。
            'SlowRtMs' => 1800, // 慢调用RT。请求的响应时间超过该值时统计为慢调用。阈值类型为“慢调用比例”时需设置该字段。我们数据库链接超时配置是2秒
            'MinRequestAmount' => 100, //触发熔断的最小请求数目，若当前统计窗口请求数小于此值，即使达到熔断条件规则也不会触发。默认值为5。
            'HalfOpenBaseAmountPerStep' => 100, //熔断恢复每步最小通过数目，默认值为5。
            'HalfOpenRecoveryStepNum' => 1, //熔断恢复阶段数，默认值为1。
        ],
        'client_db' => [
            'Strategy' => "0", // 0:慢调用比例 1:异常比例
            'Threshold' => "0.6", //阈值类型所对应的降级阈值，超过该值时会触发接口的降级。
            'RecoveryTimeoutMs' => 10 * 1000, //熔断时长。在该时间段内，该接口的请求都会快速失败，单位毫秒。
            'StatDurationMs' => 20 * 1000 , //统计窗口时长，单位毫秒。默认值为1000。
            'SlowRtMs' => 1800, // 慢调用RT。请求的响应时间超过该值时统计为慢调用。阈值类型为“慢调用比例”时需设置该字段。我们数据库链接超时配置是2秒
            'MinRequestAmount' => 100, //触发熔断的最小请求数目，若当前统计窗口请求数小于此值，即使达到熔断条件规则也不会触发。默认值为5。
            'HalfOpenBaseAmountPerStep' => 100, //熔断恢复每步最小通过数目，默认值为5。
            'HalfOpenRecoveryStepNum' => 1, //熔断恢复阶段数，默认值为1。
        ],
        'redis' => [
            'Strategy' => "0", // 0:慢调用比例 1:异常比例
            'Threshold' => "0.6", //阈值类型所对应的降级阈值，超过该值时会触发接口的降级。
            'RecoveryTimeoutMs' => 10 * 1000, //熔断时长。在该时间段内，该接口的请求都会快速失败，单位毫秒。
            'StatDurationMs' => 20 * 1000 , //统计窗口时长，单位毫秒。默认值为1000。
            'SlowRtMs' => 1800, // 慢调用RT。请求的响应时间超过该值时统计为慢调用。阈值类型为“慢调用比例”时需设置该字段。我们数据库链接超时配置是2秒
            'MinRequestAmount' => 100, //触发熔断的最小请求数目，若当前统计窗口请求数小于此值，即使达到熔断条件规则也不会触发。默认值为5。
            'HalfOpenBaseAmountPerStep' => 100, //熔断恢复每步最小通过数目，默认值为5。
            'HalfOpenRecoveryStepNum' => 1, //熔断恢复阶段数，默认值为1。
        ],
        'inner_api' => [
            'Strategy' => "0", // 0:慢调用比例 1:异常比例
            'Threshold' => "0.6", //阈值类型所对应的降级阈值，超过该值时会触发接口的降级。
            'RecoveryTimeoutMs' => 10 * 1000, //熔断时长。在该时间段内，该接口的请求都会快速失败，单位毫秒。
            'StatDurationMs' => 20 * 1000 , //统计窗口时长，单位毫秒。默认值为1000。
            'SlowRtMs' => 20000, // 慢调用RT。请求的响应时间超过该值时统计为慢调用。阈值类型为“慢调用比例”时需设置该字段。我们数据库链接超时配置是2秒
            'MinRequestAmount' => 100, //触发熔断的最小请求数目，若当前统计窗口请求数小于此值，即使达到熔断条件规则也不会触发。默认值为5。
            'HalfOpenBaseAmountPerStep' => 100, //熔断恢复每步最小通过数目，默认值为5。
            'HalfOpenRecoveryStepNum' => 1, //熔断恢复阶段数，默认值为1。
        ],
        'alibaba_api' => [
            'Strategy' => "0", // 0:慢调用比例 1:异常比例
            'Threshold' => "0.8", //阈值类型所对应的降级阈值，超过该值时会触发接口的降级。
            'RecoveryTimeoutMs' => 10 * 1000, //熔断时长。在该时间段内，该接口的请求都会快速失败，单位毫秒。
            'StatDurationMs' => 20 * 1000 , //统计窗口时长，单位毫秒。默认值为1000。
            'SlowRtMs' => 6000, // 慢调用RT。请求的响应时间超过该值时统计为慢调用。阈值类型为“慢调用比例”时需设置该字段。我们数据库链接超时配置是2秒
            'MinRequestAmount' => 100, //触发熔断的最小请求数目，若当前统计窗口请求数小于此值，即使达到熔断条件规则也不会触发。默认值为5。
            'HalfOpenBaseAmountPerStep' => 100, //熔断恢复每步最小通过数目，默认值为5。
            'HalfOpenRecoveryStepNum' => 1, //熔断恢复阶段数，默认值为1。
        ],
    ];

    public function __construct( )
    {
        $config = \Yii::app()->params['ahas_access_key'];
        $this->accessKeyId = 'LTAI5tEjurbhqL4bpYhvMVRY';//$config['access_key_id'];
        $this->accessKeySecret = '******************************';//$config['access_key_secret'];

        AlibabaCloud::accessKeyClient(
            $this->accessKeyId,
            $this->accessKeySecret
        )
            ->regionId('cn-hangzhou')
            ->asDefaultClient();
    }

    protected function request($action,$options = [])
    {
        $result = AlibabaCloud::rpc()
            ->product('ahas-openapi')
            // ->scheme('https') // https | http
            ->version('2019-09-01')
            ->action($action)
            ->method('POST')
            ->host('ahas-vpc.cn-hangzhou.aliyuncs.com')
            ->options($options)
            ->request();
        return $result;
    }

    public function applications($namespace)
    {
        $result = $this->request('ListActiveApps',[
            'query' => [
                'AppType' => "0",
                'Namespace' => $namespace,
            ],
        ]);
        $preUrl = 'https://ahas.console.aliyun.com/flowProtection/systemGuard/SystemGuardSummary?region=cn-hangzhou';
        $list =  array_map(function ($item)use($preUrl){
            $item['AppType'] = $item['AppType'] == 0 ? '主机应用' : '集群应用';
            $item['LastHealthPingTime'] = date('Y-m-d H:i:s',$item['LastHealthPingTime']/1000);
            $item['Url'] = "$preUrl&appName=${item['AppName']}&ns=${item['Namespace']}";
            return $item;
        }, $result->toArray()['Data']??[]);

        return $list;
    }

    public function applicationInfo($namespace, $appName)
    {
        $degradeRules = $this->listDegradeRulesOfApp($namespace, $appName, true);
        $flowRules = $this->listFlowRulesOfApp($namespace, $appName);
        $isolationRules = $this->listIsolationRulesOfApp($namespace, $appName);
        $systemRules = $this->listSystemRules($namespace, $appName);
        $hotParamRules = $this->listHotParamRulesOfApp($namespace, $appName);
        $data = [
            'AppName' => $appName,
            'Namespace' => $namespace,
            'degradeRules' => $degradeRules['Datas'],
            'flowRules' => $flowRules['Datas'],
            'isolationRules' => $isolationRules['Datas'],
            'systemRules' => $systemRules['Datas'],
            'hotParamRules' => $hotParamRules['Datas'],
        ];
        return $data;
    }

    private function getDegradeTemplateRule($template_key, $resource)
    {
        $template =  $this->template[$template_key];
        $strategy = $template['Strategy'];
        $key = $resource .'-'.$strategy;
        $rule = $template;
        $rule['Resource'] = $resource;
        $rule['ResourceType'] = $template_key;
        $rule['Key'] = $key;
        return $rule;
    }

    public function  syncDefaultDegradeRules($namespace, $appName)
    {
        $defaultRules = [];
        //获取当前应用的降级规则
        $degradeRules = $this->listDegradeRulesOfApp($namespace, $appName);
        $rulesMap = [];
        foreach ($degradeRules['Datas'] as $degradeRule) {
            //这里仅保留我们的资源
            $key = $degradeRule['Resource'] .'-'.$degradeRule['Strategy'];
            $rulesMap[$key] = $degradeRule;
        }

        $configName = \Yii::app()->params['env'] == 'test' ? 'dc-test.php' : 'dc-cn.php';;
        $configs = ConfigLoader::getInstance()
            ->setDcKey($configName)->getDcConfig();
        $singeDbList = [];
        $mysqlClientDBList = DbService::getDbInstanceList(\DbSet::TYPE_MYSQL);
        $pgsqlClientDBList = DbService::getDbInstanceList(\DbSet::TYPE_PGSQL);
        $dbPoxyList = [];
        $redisList = [];
        foreach ($configs as $key =>  $item) {
            if(!isset($item['type'])){
                continue;
            }
            if( $key == 'db_proxy_mysql' || $key == 'db_proxy_pgsql')
            {
                $dbPoxyList[] = $item;
                continue;
            }

            if( $item['type'] == 'redis' )
            {
                $redisList[] = $item;
                continue;
            }

            if ($item['type'] == 'mysql' || $item['type'] == 'pgsql')
            {
                $singeDbList[] = $item;
                continue;
            }
        }
        // 单点库规则处理
        foreach ($singeDbList as $item)
        {
            $resource = $item['type'].':'.$item['host'].':'.$item['port'];
            $rule  = $this->getDegradeTemplateRule('single_db', $resource);
            $key = $rule['Key'];
            if( isset($defaultRules[$key]) )
            {
                continue;
            }
            isset($rulesMap[$key]) && $rule['RuleId'] = $rulesMap[$key]['RuleId'];
            $defaultRules[$key]  = $rule;
        }

        //获取数据库实例列表- mysql
        foreach (  $mysqlClientDBList as $item)
        {
            $resource = 'mysql:'.$item['host'].':'.$item['port'];
            $rule  = $this->getDegradeTemplateRule('client_db', $resource);
            $key = $rule['Key'];
            if( isset($defaultRules[$key]) )
            {
                continue;
            }
            isset($rulesMap[$key]) && $rule['RuleId'] = $rulesMap[$key]['RuleId'];
            $defaultRules[$key]  = $rule;
        }

        //获取数据库实例列表- pgsql
        foreach ( $pgsqlClientDBList as $item)
        {
            $resource = 'pgsql:'.$item['host'].':'.$item['port'];
            $rule  = $this->getDegradeTemplateRule('client_db', $resource);
            $key = $rule['Key'];
            if( isset($defaultRules[$key]) )
            {
                continue;
            }
            isset($rulesMap[$key]) && $rule['RuleId'] = $rulesMap[$key]['RuleId'];
            $defaultRules[$key]  = $rule;
        }

        // redis规则处
        foreach ($redisList as $item) {

            if( is_array($item['host'])) {
                $resource = 'redis:' . str_replace('tcp://', '', $item['host'][0]);
            }
            else
            {
                $resource =  'redis:' . $item['host'] . ':' . $item['port'];
            }
            $rule  = $this->getDegradeTemplateRule('redis', $resource);
            $key = $rule['Key'];
            if (isset($defaultRules[$key])) {
                continue;
            }
            isset($rulesMap[$key]) && $rule['RuleId'] = $rulesMap[$key]['RuleId'];
            $defaultRules[$key] = $rule;
        }

        // 数据库代理规则处理
        foreach ($dbPoxyList as $item) {
            foreach ( $item['extra_info']['dbPorts'] as $port)
            {
                $resource = $item['type'] . ':' . $item['host'] . ':' . $port;
                $rule  = $this->getDegradeTemplateRule('db_proxy', $resource);
                $key = $rule['Key'];
                if (isset($defaultRules[$key])) {
                    continue;
                }
                isset($rulesMap[$key]) && $rule['RuleId'] = $rulesMap[$key]['RuleId'];
                $defaultRules[$key] = $rule;
            }
        }

        //inner_api
        foreach(\Yii::app()->params['inner_api'] ??[] as $module => $item){
            foreach($item['interface'] ??[] as $interface => $val){
                $resource = 'inner_api:'.$module.':'.$interface;
                $rule  = $this->getDegradeTemplateRule('inner_api', $resource);
                $key = $rule['Key'];
                if( isset($defaultRules[$key]) )
                {
                    continue;
                }
                isset($rulesMap[$key]) && $rule['RuleId'] = $rulesMap[$key]['RuleId'];
                $defaultRules[$key]  = $rule;
            }
        }

        //alibaba_api
        $sdkPath = __DIR__.'/../../../../vendor/xiaoman/alibaba-sdk/src/taobao/top/request';
        if(is_dir($sdkPath)){
            foreach(scandir($sdkPath) as $file){
                if($file == '.' || $file == '..'){
                    continue;
                }
                $namespace = "xiaoman\\AlibabaSdk\\taobao\\top\\request\\";
                $filename = pathinfo($file)['filename'];
                $class = $namespace.$filename;
                $req = new $class();
                $apiMethodName = $req->getApiMethodName();

                $resource = 'alibaba_api:'.$apiMethodName;
                $rule  = $this->getDegradeTemplateRule('inner_api', $resource);
                $key = $rule['Key'];
                if( isset($defaultRules[$key]) )
                {
                    continue;
                }
                isset($rulesMap[$key]) && $rule['RuleId'] = $rulesMap[$key]['RuleId'];
                $defaultRules[$key]  = $rule;

            }
        }

        //更新降级规则配置
        foreach ($defaultRules as $rule) {
            if(isset($rule['RuleId'])) {;
                $this->modifyDegradeRule($rule['RuleId'], $namespace, $appName, $rule['Resource'], $rule);
                continue;
            }
            $this->createDegradeRule($namespace, $appName, $rule['Resource'], $rule);
        }
        return count($defaultRules);

    }

    public function resources($resource_type='')
    {
        /**
         * @var \CDbConnection $db
         */
        $db = \Yii::app()->prometheus_db;
        $sql = 'select * from tbl_ahas_resources';
        $params=[];
        if(  $resource_type )
        {
            $sql .= ' where resource_type=:resource_type';
            $params = [':resource_type' => $resource_type];
        }
        $result = $db->createCommand($sql)->queryAll(true, $params);
        return $result;
    }


    public function discoverSources()
    {

        $configName = \Yii::app()->params['env'] == 'test' ? 'dc-test.php' : 'dc-cn.php';
        $configs = ConfigLoader::getInstance()
            ->setDcKey($configName)->getDcConfig();

        $resources = [];
        $mysqlClientDBList = DbService::getDbInstanceList(\DbSet::TYPE_MYSQL);
        $pgsqlClientDBList = DbService::getDbInstanceList(\DbSet::TYPE_PGSQL);
        foreach ($configs as $key =>  $item)
        {
            if(!isset($item['type'])){
                continue;
            }

            if( $key == 'db_proxy_mysql' || $key == 'db_proxy_pgsql')
            {
                foreach ( $item['extra_info']['dbPorts'] as $port)
                {
                    $resources[] = ['resource_type'=> 'db_proxy', 'resource' => $item['type'] . ':' . $item['host'] . ':' . $port];
                }
                continue;
            }

            if( $item['type'] == 'redis' )
            {
                $resources[] = ['resource_type'=> 'redis', 'resource' =>'redis:' .(is_array($item['host'])? str_replace('tcp://', '', $item['host'][0]):($item['host'] . ':' . $item['port']))];
                continue;
            }

            if ($item['type'] == 'mysql' || $item['type'] == 'pgsql')
            {
                $resources[] = ['resource_type'=> 'single_db', 'resource' =>$item['type'].':'.$item['host'].':'.$item['port']];
                continue;
            }
        }

        //获取数据库实例列表- mysql
        foreach (  $mysqlClientDBList as $item)
        {
            $resources[] = ['resource_type'=> 'client_db', 'resource' => 'mysql:'.$item['host'].':'.$item['port']];
        }

        //获取数据库实例列表- pgsql
        foreach ( $pgsqlClientDBList as $item)
        {
            $resources[] = ['resource_type'=> 'client_db', 'resource' =>'pgsql:'.$item['host'].':'.$item['port']];
        }

        //inner_api
        foreach(\Yii::app()->params['inner_api'] ??[] as $module => $item){
            foreach($item['interface'] ??[] as $interface => $val){
                $resources[] = ['resource_type'=> 'inner_api', 'resource' =>'inner_api:'.$module.':'.$interface];
            }
        }

        //alibaba_api
        $sdkPath = __DIR__.'/../../../../vendor/xiaoman/alibaba-sdk/src/taobao/top/request';
        if(is_dir($sdkPath)){
            foreach(scandir($sdkPath) as $file){
                if($file == '.' || $file == '..'){
                    continue;
                }
                $namespace = "xiaoman\\AlibabaSdk\\taobao\\top\\request\\";
                $filename = pathinfo($file)['filename'];
                $class = $namespace.$filename;
                $req = new $class();
                $apiMethodName = $req->getApiMethodName();
                $resources[] = ['resource_type'=> 'alibaba_api', 'resource' =>'alibaba_api:'.$apiMethodName];
            }
        }

        $result = 0;
        $existResources = array_column($this->resources(),'resource','resource');

        $existTemplates = array_column($this->degradeRuleTemplates(1),'resource');

        foreach ( $resources as $item)
        {
            if( isset($existResources[$item['resource']]) )
            {
                continue;
            }

            $this->saveResources($item);
            $result++;

            //初始化模版
            if( in_array($item['resource'], $existTemplates) )
            {
                continue;
            }
            $rule  = $this->getDegradeTemplateRule($item['resource_type'], $item['resource']);

            $template = [
                'template_key' => $rule['ResourceType'].':'.$rule['Resource'].':'.mt_rand(1000,9999),
                'resource' => $rule['Resource'],
                'template_type' => 1,
                'resource_type' => $rule['ResourceType'],
                'strategy' => $rule['Strategy'],
                'threshold' => $rule['Threshold'],
                'recovery_timeout_ms' => $rule['RecoveryTimeoutMs'],
                'stat_duration_ms' => $rule['StatDurationMs'],
                'slow_rt_ms' => $rule['SlowRtMs'],
                'min_request_amount' => $rule['MinRequestAmount'],
                'half_open_base_amount_per_step' => $rule['HalfOpenBaseAmountPerStep'],
                'half_open_recovery_step_num' => $rule['HalfOpenRecoveryStepNum'],
                'description' => ''
            ];
            $this->saveDegradeRuleTemplate($template);
        }


        return $result;

    }
    public function saveResources($data)
    {
        /**
         * @var \CDbConnection $db
         */
        $db = \Yii::app()->prometheus_db;

        $params = [
            ':resource_type' => $data['resource_type'],
            ':resource' => $data['resource'],
            ':description' => $data['description']??'',
        ];
        $sql = 'insert into tbl_ahas_resources(resource,resource_type,description) values(:resource,:resource_type,:description) ON DUPLICATE KEY UPDATE  description=:description';
        $db->createCommand($sql)->execute($params);
    }

    public function degradeRuleTemplates($template_type='', $resource = '')
    {
        /**
         * @var \CDbConnection $db
         */
        $db = \Yii::app()->prometheus_db;
        $sql = 'select * from tbl_ahas_template_degrade_rules where enable_flag=:enable_flag';

        $params = [':enable_flag' => 1];
        if( $template_type )
        {
            $sql .= ' and template_type=:template_type';
            $params[':template_type'] = $template_type;
        }
        if( $resource )
        {
            $sql .= ' and resource=:resource';
            $params[':resource'] = $resource;
        }
        $result = $db->createCommand($sql)->queryAll(true,$params);

        foreach($result as &$item){
            $item['enable'] = (bool)$item['enable'];
        }

        return $result;
    }

    public function discoverDegradeRuleTemplate($template_type)
    {
        $templateList = $this->degradeRuleTemplates($template_type);
    }

    public function getDegradeRule($data)
    {
        /**
         * @var \CDbConnection $db
         */
        $db = \Yii::app()->prometheus_db;
        $sql = 'select * from tbl_ahas_degrade_rules where 1=1';
        $params = [];
        if (isset($data['rule_id'])) {
            $sql .= ' and  rule_id=:rule_id';
            $params[':rule_id'] = $data['rule_id'];
        }
        return $db->createCommand($sql)->queryRow(true, $params);
    }

    public function degradeResourceRules($template_id=0,$resource_type='',$resource='',$app_name = '', $namespace='')
    {
        /**
         * @var \CDbConnection $db
         */
        $db = \Yii::app()->prometheus_db;
        $sql = 'select * from tbl_ahas_degrade_rules where 1=1';
        $params = [];
        if ($template_id) {
            $sql .= ' and  template_id=:template_id';
            $params[':template_id'] = $template_id;
        }

        if ($resource_type) {
            $sql .= ' and resource_type=:resource_type';
            $params[':resource_type'] = $resource_type;
        }

        if ($namespace) {
            $sql .= ' and namespace=:namespace';
            $params[':namespace'] = $namespace;
        }

        if ($app_name) {
            $sql .= ' and app_name=:app_name';
            $params[':app_name'] = $app_name;
        }

        if ($resource) {
            $sql .= ' and resource=:resource';
            $params[':resource'] = $resource;
        }

        $result = $db->createCommand($sql)->queryAll(true, $params);

        foreach($result as &$item){
            $item['enable'] = (bool)$item['enable'];
        }

        return $result;
    }

    public function getAppList($resource)
    {
        /**
         * @var \CDbConnection $db
         */
        $db = \Yii::app()->prometheus_db;
        $sql = 'select namespace,app_name,active_demotion from tbl_ahas_degrade_rules where resource=:resource group by namespace,app_name';

        return $db->createCommand($sql)->queryAll(true, [':resource' => $resource]);
    }

    public function saveDegradeRuleTemplate($data,$template_id=0)
    {
        /**
         * @var \CDbConnection $db
         */
        $db = \Yii::app()->prometheus_db;

        $params = [
            ':template_key' => $data['template_key'],
            ':template_type' => $data['template_type'],
            ':resource_type' => $data['resource_type'],
            ':resource' => $data['resource'],
            ':strategy' => $data['strategy'],
            ':threshold' => $data['threshold'],
            ':recovery_timeout_ms' => $data['recovery_timeout_ms'],
            ':stat_duration_ms' => $data['stat_duration_ms'],
            ':slow_rt_ms' => $data['slow_rt_ms'],
            ':min_request_amount' => $data['min_request_amount'],
            ':half_open_base_amount_per_step' => $data['half_open_base_amount_per_step'],
            ':half_open_recovery_step_num' => $data['half_open_recovery_step_num'],
            ':description' => $data['description'],
            ':update_time' => date('Y-m-d H:i:s'),
        ];

        if ($template_id) {
            $sql = 'update tbl_ahas_template_degrade_rules set template_key=:template_key,template_type=:template_type,resource_type=:resource_type,strategy=:strategy,threshold=:threshold,
                                           recovery_timeout_ms=:recovery_timeout_ms,stat_duration_ms=:stat_duration_ms,slow_rt_ms=:slow_rt_ms,min_request_amount=:min_request_amount ,
                                           half_open_base_amount_per_step=:half_open_base_amount_per_step ,min_request_amount=:min_request_amount ,half_open_base_amount_per_step=:half_open_base_amount_per_step,
                                           half_open_recovery_step_num=:half_open_recovery_step_num,description=:description,update_time=:update_time
                                       where template_id=:template_id';
            $params[':template_id'] = $template_id;
            $db->createCommand($sql)->execute($params);
            return $template_id;
        } else {
            $sql = 'insert into tbl_ahas_template_degrade_rules(template_key,template_type,resource_type,resource,strategy,threshold,recovery_timeout_ms,stat_duration_ms,slow_rt_ms,min_request_amount,half_open_base_amount_per_step,half_open_recovery_step_num,description,create_time,update_time) 
                values(:template_key,:template_type,:resource_type,:resource,:strategy,:threshold,:recovery_timeout_ms,:stat_duration_ms,:slow_rt_ms,:min_request_amount,:half_open_base_amount_per_step,:half_open_recovery_step_num,:description,:create_time,:update_time)';
            $params[':create_time'] = date('Y-m-d H:i:s');
            $db->createCommand($sql)->execute($params);
            return $db->getLastInsertID();
        }
    }

        public function saveResource($name,$rules)
    {
        $result = $this->request('DescribeNamespaces');
        return $result->toArray()['Data']??[];
    }

    public function  batchOpenDegradeRules($namespace, $appName)
    {
        $result = 0;
        //获取当前应用的降级规则
        $degradeRules = $this->listDegradeRulesOfApp($namespace, $appName);
        foreach ($degradeRules['Datas'] as $degradeRule) {
            if( $degradeRule['Enable'] ) {
                continue;
            }
            $this->enableDegradeRule($degradeRule['RuleId']);
            $result ++;
        }
        return $result;

    }


    public function  batchCloseDegradeRules($namespace, $appName)
    {
        $result = 0;
        //获取当前应用的降级规则
        $degradeRules = $this->listDegradeRulesOfApp($namespace, $appName);
        foreach ($degradeRules['Datas'] as $degradeRule) {
            if( !$degradeRule['Enable'] ) {
                continue;
            }
            $this->disableDegradeRule($degradeRule['RuleId']);
            $result ++;
        }
        return $result;

    }

    public function  batchCleanDegradeRules($namespace, $appName)
    {
        $result = 0;
        //获取当前应用的降级规则
        $degradeRules = $this->listDegradeRulesOfApp($namespace, $appName);
        foreach ($degradeRules['Datas'] as $degradeRule) {
            if( $degradeRule['Enable'] ) {
                continue;
            }
            $this->deleteDegradeRule($degradeRule['RuleId']);
            $result ++;
        }
        return $result;

    }
    public function createDegradeRule($namespace,$app_name, $resource, $settings)
    {
        $data = [
            'Namespace' => $namespace,
            'AppName' => $app_name,
            'Resource' => $resource,
            'Strategy' => $settings['Strategy'],
            'Threshold' => $settings['Threshold'],
            'RecoveryTimeoutMs' => $settings['RecoveryTimeoutMs'],
            'StatDurationMs' => $settings['StatDurationMs'],
            'SlowRtMs' => $settings['SlowRtMs'],
            'MinRequestAmount' => $settings['MinRequestAmount'],
            'HalfOpenBaseAmountPerStep' => $settings['HalfOpenBaseAmountPerStep'],
            'HalfOpenRecoveryStepNum' => $settings['HalfOpenRecoveryStepNum'],
        ];
        $result = $this->request('CreateDegradeRule',[
            'query' => $data,
        ]);
        $roleId = $result['Data']['RuleId'];
        if( !$roleId)
            throw new \RuntimeException('保存失败');

        $rowData = ['rule_id' => $roleId];
        foreach ($data as $key => $value)
        {
            $key = strtolower(preg_replace('/(?!^)([[:upper:]])/', '_$0', $key));
            $rowData[$key] = $value;
        }
        if(isset($settings['ResourceType']))
            $rowData['resource_type'] = $settings['ResourceType'];

        if(isset($settings['TemplateId']))
            $rowData['template_id'] = $settings['TemplateId'];

        /**
         * @var \CDbConnection $db
         */
        $db = \Yii::app()->prometheus_db;
        $table =  'tbl_ahas_degrade_rules';
        $db->getCommandBuilder()->createInsertCommand($table, $rowData)->execute();
        return $result->toArray();
    }

    public function modifyDegradeRule($ruleId,$namespace,$app_name, $resource, $settings)
    {
        $data = [
            'RuleId' => $ruleId,
            'Namespace' => $namespace,
            'AppName' => $app_name,
            'Resource' => $resource,
            'Strategy' => $settings['Strategy'],
            'Threshold' => $settings['Threshold'],
            'RecoveryTimeoutMs' => $settings['RecoveryTimeoutMs'],
            'StatDurationMs' => $settings['StatDurationMs'],
            'SlowRtMs' => $settings['SlowRtMs'],
            'MinRequestAmount' => $settings['MinRequestAmount'],
            'HalfOpenBaseAmountPerStep' => $settings['HalfOpenBaseAmountPerStep'],
            'HalfOpenRecoveryStepNum' => $settings['HalfOpenRecoveryStepNum'],
        ];
        $result = $this->request('ModifyDegradeRule',[
            'query' => $data
        ]);

        $rowData = [];
        foreach ($data as $key => $value)
        {
            $key = strtolower(preg_replace('/(?!^)([[:upper:]])/', '_$0', $key));
            $rowData[$key] = $value;
        }
        if(isset($settings['ResourceType']))
            $rowData['resource_type'] = $settings['ResourceType'];

        if(isset($settings['TemplateId']))
            $rowData['template_id'] = $settings['TemplateId'];

        if(isset($settings['ActiveDemotion']))
            $rowData['active_demotion'] = $settings['ActiveDemotion'];

        /**
         * @var \CDbConnection $db
         */
        $db = \Yii::app()->prometheus_db;
        $table =  'tbl_ahas_degrade_rules';
        $criteria = new \CDbCriteria();
        $criteria->condition = 'rule_id=:rule_id';
        $criteria->params = [':rule_id' => $ruleId];
        $db->getCommandBuilder()->createUpdateCommand($table, $rowData, $criteria)->execute();
        return $result->toArray();
    }

    public function deleteDegradeRule($ruleId)
    {
        $result =  $this->request('DeleteDegradeRule',[
            'query' => [
                'RuleId' => $ruleId,
            ],
        ]);

        /**
         * @var \CDbConnection $db
         */
        $db = \Yii::app()->prometheus_db;
        $table =  'tbl_ahas_degrade_rules';
        $criteria = new \CDbCriteria();
        $criteria->condition = 'rule_id=:rule_id';
        $criteria->params = [':rule_id' => $ruleId];
        $db->getCommandBuilder()->createDeleteCommand($table, $criteria)->execute();

        return $result->toArray();
    }

    public function enableDegradeRule($ruleId)
    {
        $result =  $this->request('EnableDegradeRule',[
            'query' => [
                'RuleId' => $ruleId,
            ],
        ]);
        /**
         * @var \CDbConnection $db
         */
        $db = \Yii::app()->prometheus_db;
        $table =  'tbl_ahas_degrade_rules';
        $criteria = new \CDbCriteria();
        $criteria->condition = 'rule_id=:rule_id';
        $criteria->params = [':rule_id' => $ruleId];
        $db->getCommandBuilder()->createUpdateCommand($table,['enable' =>1] ,$criteria)->execute();

        return $result->toArray();
    }

    public function  disableDegradeRule($ruleId)
    {
        $result = $this->request('DisableDegradeRule',[
            'query' => [
                'RuleId' => $ruleId,
            ],
        ]);
        /**
         * @var \CDbConnection $db
         */
        $db = \Yii::app()->prometheus_db;
        $table =  'tbl_ahas_degrade_rules';
        $criteria = new \CDbCriteria();
        $criteria->condition = 'rule_id=:rule_id';
        $criteria->params = [':rule_id' => $ruleId];
        $db->getCommandBuilder()->createUpdateCommand($table,['enable' =>0] ,$criteria)->execute();
        return $result->toArray();
    }

    public function activeDemotionRule($ruleId)
    {

    }


    public function listDegradeRulesOfApp($namespace, $appName, $show_demotion = false)
    {
        $result = $this->request('ListDegradeRulesOfApp',[
            'query' => [
                'Namespace' => $namespace,
                'AppName' => $appName,
                'PageSize' => 10000,
            ],
        ]);

        $data = $result->toArray()['Data'];

        if($show_demotion){
            $degradeRules = $this->degradeResourceRules('','','', $appName, $namespace);
            $degradeRules = array_column($degradeRules, null, 'rule_id');

            foreach ($data['Datas'] as &$item) {

                $item['active_demotion'] = $degradeRules[$item['RuleId']]['active_demotion']??0;
            }

        }
        return $data;
    }

    public function listDegradeRulesOfResource($namespace, $resource)
    {
        $result = $this->request('ListDegradeRulesOfResource',[
            'query' => [
                'Namespace' => $namespace,
                'Resource' => $resource,
                'PageSize' => 10000,
            ],
        ]);
        return $result->toArray()['Data'];
    }

    public function listFlowRulesOfApp($namespace, $appName)
    {
        $result = $this->request('ListFlowRulesOfApp',[
            'query' => [
                'Namespace' => $namespace,
                'AppName' => $appName,
                'PageSize' => 10000,
            ],
        ]);
        return $result->toArray()['Data'];
    }

    public function listFlowRulesOfResource($namespace, $resource)
    {
        $result = $this->request('ListFlowRulesOfResource',[
            'query' => [
                'Namespace' => $namespace,
                'Resource' => $resource,
                'PageSize' => 10000,
            ],
        ]);
        return $result->toArray()['Data'];
    }

    public function listIsolationRulesOfApp($namespace, $appName)
    {
        $result = $this->request('ListIsolationRulesOfApp',[
            'query' => [
                'Namespace' => $namespace,
                'AppName' => $appName,
                'PageSize' => 10000,
            ],
        ]);
        return $result->toArray()['Data'];
    }

    public function listIsolationRulesOfResource($namespace, $resource)
    {
        $result = $this->request('ListIsolationRulesOfResource',[
            'query' => [
                'Namespace' => $namespace,
                'Resource' => $resource,
                'PageSize' => 10000,
            ],
        ]);
        return $result->toArray()['Data'];
    }

    public function listSystemRules($namespace, $appName)
    {
        $result = $this->request('ListSystemRules',[
            'query' => [
                'Namespace' => $namespace,
                'AppName' => $appName,
                'PageSize' => 10000,
            ],
        ]);
        return $result->toArray()['Data'];
    }

    public function listHotParamRulesOfApp($namespace, $appName)
    {
        $result = $this->request('ListHotParamRulesOfApp',[
            'query' => [
                'Namespace' => $namespace,
                'AppName' => $appName,
                'PageSize' => 10000,
            ],
        ]);
        return $result->toArray()['Data'];
    }

    public function listHotParamRulesOfResource($namespace, $appName)
    {
        $result = $this->request('ListHotParamRulesOfResource',[
            'query' => [
                'Namespace' => $namespace,
                'AppName' => $appName,
                'PageSize' => 10000,
            ],
        ]);
        return $result->toArray()['Data'];
    }

    public function changeTemplateStatus($template_id,$flag = 1)
    {
        if( !$template_id)
            throw new \RuntimeException(\Yii::t('common','{param} cannot be empty',['{param}'=>"template_id"]));
        /**
         * @var \CDbConnection $db
         */
        $db = \Yii::app()->prometheus_db;
        $params = [
            ':template_id' => $template_id,
            ':enable' => $flag?1:0
        ];
        $sql = 'update tbl_ahas_template_degrade_rules set enable=:enable where template_id=:template_id';
        return $db->createCommand($sql)->execute($params);
    }
    public function deleteTemplate($template_id)
    {
        if( !$template_id)
            throw new \RuntimeException(\Yii::t('common','{param} cannot be empty',['{param}'=>"template_id"]));

        /**
         * @var \CDbConnection $db
         */
        $db = \Yii::app()->prometheus_db;
        $params = [
            ':template_id' => $template_id,
            ':enable_flag' => 0
        ];
        $sql = 'update tbl_ahas_template_degrade_rules set enable_flag=:enable_flag where template_id=:template_id';
        return $db->createCommand($sql)->execute($params);
    }


    public function applyAppForResource($resource, $namespace, $app_name)
    {
        /**
         * @var \CDbConnection $db
         */
        $db = \Yii::app()->prometheus_db;

        $templates = $this->degradeRuleTemplates('', $resource);

        foreach($templates as $item){
            if(!$item['enable']){
                continue;
            }
            $settings['TemplateId'] = $item['template_id'];
            $settings['Strategy'] = $item['strategy'];
            $settings['Threshold'] = $item['threshold'];
            $settings['RecoveryTimeoutMs'] = $item['recovery_timeout_ms'];
            $settings['StatDurationMs'] = $item['stat_duration_ms'];
            $settings['SlowRtMs'] = $item['slow_rt_ms'];
            $settings['MinRequestAmount'] = $item['min_request_amount'];
            $settings['HalfOpenBaseAmountPerStep'] = $item['half_open_base_amount_per_step'];
            $settings['HalfOpenRecoveryStepNum'] = $item['half_open_recovery_step_num'];
            $settings['ResourceType'] = $item['resource_type'];


            $sql = 'select * from tbl_ahas_degrade_rules where namespace=:namespace and app_name=:app_name and resource=:resource and template_id=:template_id';

            $params = [
                ':namespace' => $namespace,
                ':app_name' => $app_name,
                ':resource' => $resource,
                ':template_id' => $item['template_id'],
            ];

            $row = $db->createCommand($sql)->queryRow(true, $params);

            if (!empty($row)) {
                $this->modifyDegradeRule($row['rule_id'],$namespace,$app_name, $resource, $settings);
            } else {
                $this->createDegradeRule($namespace,$app_name, $resource, $settings);
            }

        }

    }

    public function degradeBySource($resource)
    {
        $rules = $this->degradeResourceRules('','',$resource);

        $this->degradeRules($rules);

    }

    public function degradeByAppSource($namespace, $app_name, $resource)
    {
        $rules = $this->degradeResourceRules('','',$resource, $app_name, $namespace);

        $this->degradeRules($rules);
    }

    public function restoreByAppSource($namespace, $app_name, $resource)
    {
        $rules = $this->degradeResourceRules('','',$resource, $app_name,$namespace);

        $this->restoreRules($rules);
    }


    public function restoreBySource($resource)
    {
        $rules = $this->degradeResourceRules('','',$resource);

        $this->restoreRules($rules);

    }

    public function batchUpdateRules($resource)
    {
        $apps = $this->getAppList($resource);

        foreach($apps as $item){
            $this->applyAppForResource($resource, $item['namespace'], $item['app_name']);
        }

    }

    public function degradeDegradeRule($ruleId)
    {
        $rule[] = $this->getDegradeRule(['rule_id'=>$ruleId]);

        $this->degradeRules($rule);
    }

    public function restoreDegradeRule($ruleId)
    {
        $rule[] = $this->getDegradeRule(['rule_id'=>$ruleId]);

        $this->restoreRules($rule);
    }

    protected function restoreRules($rules)
    {
        /**
         * @var \CDbConnection $db
         */
        $db = \Yii::app()->prometheus_db;
        $templateIds = array_column($rules, 'template_id');

        $sql = "select * from tbl_ahas_template_degrade_rules where template_id IN(" . implode(',', $templateIds) . ")";

        $templates = $db->createCommand($sql)->queryAll();

        $templateMap = array_column($templates, null, 'template_id');

        foreach($rules as $item){
            if(!$item['enable']){
                continue;
            }

            if(!empty($templateMap[$item['template_id']])){
                $templateInfo = $templateMap[$item['template_id']];

                $settings['Strategy'] = $templateInfo['strategy'];
                $settings['Threshold'] = $templateInfo['threshold'];
                $settings['RecoveryTimeoutMs'] = $templateInfo['recovery_timeout_ms'];
                $settings['StatDurationMs'] = $templateInfo['stat_duration_ms'];
                $settings['SlowRtMs'] = $templateInfo['slow_rt_ms'];
                $settings['MinRequestAmount'] = $templateInfo['min_request_amount'];
                $settings['HalfOpenBaseAmountPerStep'] = $templateInfo['half_open_base_amount_per_step'];
                $settings['HalfOpenRecoveryStepNum'] = $templateInfo['half_open_recovery_step_num'];


            } else {
                $settings['Strategy'] = $item['strategy'];
                $settings['Threshold'] = $this->defaultThreshold;
                $settings['RecoveryTimeoutMs'] = $item['recovery_timeout_ms'];
                $settings['StatDurationMs'] = $item['stat_duration_ms'];
                $settings['SlowRtMs'] = $this->defaultSlowRtMs;
                $settings['MinRequestAmount'] = $item['min_request_amount'];
                $settings['HalfOpenBaseAmountPerStep'] = $item['half_open_base_amount_per_step'];
                $settings['HalfOpenRecoveryStepNum'] = $item['half_open_recovery_step_num'];

            }
            $settings['ActiveDemotion'] = 0;

            $this->modifyDegradeRule($item['rule_id'],$item['namespace'],$item['app_name'], $item['resource'], $settings);

        }
    }

    protected function degradeRules($rules)
    {
        $SlowRtMs = 1;
        $activeDemotion = 1;
        $threshold = 0.01;
        $recovery_timeout_ms = 1000 * 3600;
        $stat_duration_ms = 1;
        $min_request_amount = 1;

        foreach($rules as $item){
            if(!$item['enable']){
                continue;
            }

            $settings['Strategy'] = $item['strategy'];
            $settings['Threshold'] = $threshold;
            $settings['RecoveryTimeoutMs'] = $recovery_timeout_ms;
            $settings['StatDurationMs'] = $stat_duration_ms;
            $settings['SlowRtMs'] = $SlowRtMs;
            $settings['MinRequestAmount'] = $min_request_amount;
            $settings['HalfOpenBaseAmountPerStep'] = $item['half_open_base_amount_per_step'];
            $settings['HalfOpenRecoveryStepNum'] = $item['half_open_recovery_step_num'];
            $settings['ActiveDemotion'] = $activeDemotion;

            $this->modifyDegradeRule($item['rule_id'],$item['namespace'],$item['app_name'], $item['resource'], $settings);

        }
    }


    public function initTemplate()
    {
        $configName = \Yii::app()->params['env'] == 'test' ? 'dc-test.php' : 'dc-cn.php';
        $configs = ConfigLoader::getInstance()
            ->setDcKey($configName)->getDcConfig();

        $resources = [];
        $mysqlClientDBList = DbService::getDbInstanceList(\DbSet::TYPE_MYSQL);
        $pgsqlClientDBList = DbService::getDbInstanceList(\DbSet::TYPE_PGSQL);
        foreach ($configs as $key =>  $item)
        {
            if(!isset($item['type'])){
                continue;
            }

            if( $key == 'db_proxy_mysql' || $key == 'db_proxy_pgsql')
            {
                foreach ( $item['extra_info']['dbPorts'] as $port)
                {
                    $resources[] = ['resource_type'=> 'db_proxy', 'resource' => $item['type'] . ':' . $item['host'] . ':' . $port];

                }
                continue;
            }

            if( $item['type'] == 'redis' )
            {
                $resources[] = ['resource_type'=> 'redis', 'resource' =>'redis:' .(is_array($item['host'])? str_replace('tcp://', '', $item['host'][0]):($item['host'] . ':' . $item['port']))];

                continue;
            }

            if ($item['type'] == 'mysql' || $item['type'] == 'pgsql')
            {
                $resources[] = ['resource_type'=> 'single_db', 'resource' =>$item['type'].':'.$item['host'].':'.$item['port']];

            }
        }

        //获取数据库实例列表- mysql
        foreach (  $mysqlClientDBList as $item)
        {
            $resources[] = ['resource_type'=> 'client_db', 'resource' => 'mysql:'.$item['host'].':'.$item['port']];

        }

        //获取数据库实例列表- pgsql
        foreach ( $pgsqlClientDBList as $item)
        {
            $resources[] = ['resource_type'=> 'client_db', 'resource' =>'pgsql:'.$item['host'].':'.$item['port']];

        }

        //inner_api
        foreach(\Yii::app()->params['inner_api'] ??[] as $module => $item){
            foreach($item['interface'] ??[] as $interface => $val){
                $resources[] = ['resource_type'=> 'inner_api', 'resource' =>'inner_api:'.$module.':'.$interface];

            }
        }

        //alibaba_api
        $sdkPath = __DIR__.'/../../../../vendor/xiaoman/alibaba-sdk/src/taobao/top/request';
        if(is_dir($sdkPath)){
            foreach(scandir($sdkPath) as $file){
                if($file == '.' || $file == '..'){
                    continue;
                }
                $namespace = "xiaoman\\AlibabaSdk\\taobao\\top\\request\\";
                $filename = pathinfo($file)['filename'];
                $class = $namespace.$filename;
                $req = new $class();
                $apiMethodName = $req->getApiMethodName();
                $resources[] = ['resource_type'=> 'alibaba_api', 'resource' =>'alibaba_api:'.$apiMethodName];
            }
        }

        $existTemplates = array_column($this->degradeRuleTemplates(1),'resource');

        foreach ( $resources as $item)
        {
            if( in_array($item['resource'], $existTemplates) )
            {
                continue;
            }

            $rule  = $this->getDegradeTemplateRule($item['resource_type'], $item['resource']);

            $template = [
                'template_key' => $rule['ResourceType'].':'.$rule['Resource'].':'.mt_rand(1000,9999),
                'resource' => $rule['Resource'],
                'template_type' => 1,
                'resource_type' => $rule['ResourceType'],
                'strategy' => $rule['Strategy'],
                'threshold' => $rule['Threshold'],
                'recovery_timeout_ms' => $rule['RecoveryTimeoutMs'],
                'stat_duration_ms' => $rule['StatDurationMs'],
                'slow_rt_ms' => $rule['SlowRtMs'],
                'min_request_amount' => $rule['MinRequestAmount'],
                'half_open_base_amount_per_step' => $rule['HalfOpenBaseAmountPerStep'],
                'half_open_recovery_step_num' => $rule['HalfOpenRecoveryStepNum'],
                'description' => ''
            ];
            $this->saveDegradeRuleTemplate($template);
        }

    }

    public function initRules($namespace, $appName)
    {
        $appList = $this->applications($namespace);
        $appNames = array_column($appList, 'AppName');

        if(!in_array($appName, $appNames)){
            throw new \RuntimeException('应用名称不存在');
        }

        $templates = $this->degradeRuleTemplates(1);

        foreach($templates as $template){
            $this->applyAppForResource($template['resource'], $namespace, $appName);
        }


    }
    public function  batchOpenResourceRules($resource)
    {
        $result = 0;
        //获取对应资源的降级规则
        $rules = $this->degradeResourceRules('','',$resource);
        foreach ($rules as $rule) {
            if( $rule['enable'] ) {
                continue;
            }
            $this->enableDegradeRule($rule['rule_id']);
            $result ++;
        }
        return $result;

    }


    public function  batchCloseResourceRules($resource)
    {
        $result = 0;
        //获取对应资源的降级规则
        $rules = $this->degradeResourceRules('','',$resource);
        foreach ($rules as $rule) {
            if( !$rule['enable'] ) {
                continue;
            }
            $this->disableDegradeRule($rule['rule_id']);
            $result ++;
        }
        return $result;

    }

    public function  syncAppDegradeRules($namespace, $appName)
    {
        $degradeRules = $this->degradeResourceRules('','','', $appName, $namespace);

        foreach($degradeRules as $rule){
            $this->applyAppForResource($rule['resource'], $namespace, $appName);
        }
    }




}