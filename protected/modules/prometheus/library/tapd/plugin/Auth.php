<?php
/**
 * User: beenzhang
 * Date: 2022/12/13
 * Email: been<<EMAIL>>
 */

namespace common\modules\prometheus\library\tapd\plugin;

use common\library\account\service\LoginService;

class Auth
{
    const CACHE_PREFIX_KEY = 'plugin:tapd:auth:';

    const TRIPLE_DESC_ENCRYPT_KEY = '0kk1P1uG1N';

    const COOKIE_AUTH_KEY = 'tapd-token';
    const REQUEST_AUTH_KEY = 'auth_token';
    const TOKEN_EXPIRED = 60 * 60 * 24 * 365;

    const AUTH_ERROR_CODE_NO_LOGIN = 100001;
    const AUTH_ERROR_CODE_NO_PRIVILEGE = 100002;

    const ALLOW_CLIENT = [
        'test' => [],
        'production' => [3, 7],
    ];

    protected $allowClientMap = [];

    protected $opUserId;
    protected $opUser;

    public function __construct($opUserId = 0)
    {
        $this->opUserId = $opUserId;

        if ($opUserId > 0)
        {
            $this->opUser = \User::getUserObject($opUserId);
            if ($this->opUser->isEmpty())
                throw new \RuntimeException("user not found: $opUserId");
        }


        $this->allowClientMap = \Yii::app()->params['env'] == 'test' ? self::ALLOW_CLIENT['test'] : self::ALLOW_CLIENT['production'];
    }

    public function getRequestToken()
    {
        //优先取参数中的， 兼容cookie情况
        return $_REQUEST[self::REQUEST_AUTH_KEY]??$_COOKIE[self::COOKIE_AUTH_KEY]??'';
    }

    /**
     * @param $userId
     * @return false|string
     * @throws \ProcessException
     */
    public function generateToken($userId)
    {
        if (!$this->isAllowUse($this->opUser->getClientId()))
            throw new \ProcessException('获取TAPD插件token失败，当前操作人没有操作权限 userId:'.$this->opUser->getUserId());

        $crmUser = \User::getUserObject($userId);
        if ($crmUser->isEmpty())
            throw new \RuntimeException('获取TAPD插件token失败，userId不存在：'.$userId, self::AUTH_ERROR_CODE_NO_PRIVILEGE);

        if (!$this->isAllowUse($crmUser->getClientId()))
            throw new \ProcessException("获取TAPD插件token失败，当前user:{$userId}不支持获取token", self::AUTH_ERROR_CODE_NO_PRIVILEGE);

        $dateTime = time();
        $input = "{$crmUser->getClientId()},{$crmUser->getUserId()},{$dateTime}";

        $token = \SecurityUtil::tripleDESEncrypt($input, self::TRIPLE_DESC_ENCRYPT_KEY);

        $cache = \RedisService::cache();
        $key = $this->buildCacheKey($crmUser->getClientId(),$crmUser->getUserId());
        $cache->set($key, $token,'EX', self::TOKEN_EXPIRED);

        setcookie(self::COOKIE_AUTH_KEY, $token, [
            'expires' => time()+self::TOKEN_EXPIRED,
            'path'    => '/',
            'domain'  => LoginService::$cookieDomain,
            'samesite' => 'none',
            'secure'  => true,
            'httponly'  => true
        ]);

        return $token;
    }

    protected function buildCacheKey($clientId, $userId)
    {
        return self::CACHE_PREFIX_KEY."$clientId:$userId";
    }


    /**
     * @param $token
     * @return mixed
     * @throws \ProcessException
     */
    public function check($token)
    {
        if( !$token )
        {
            throw  new \RuntimeException('未登录', self::AUTH_ERROR_CODE_NO_LOGIN);
        }

        $data = \SecurityUtil::tripleDESDecrypt($token, self::TRIPLE_DESC_ENCRYPT_KEY);
        if (empty($data))
            throw new \RuntimeException('验证失败，账号无使用权限', self::AUTH_ERROR_CODE_NO_PRIVILEGE);

        list($clientId, $userId) = explode(',', $data);

        if (!$this->isAllowUse($clientId))
            throw new \RuntimeException('验证失败，账号无使用权限', self::AUTH_ERROR_CODE_NO_PRIVILEGE);

        $cache = \RedisService::cache();
        $key = $this->buildCacheKey($clientId,$userId);
        $cacheToken = $cache->get($key);

        if (!$cacheToken || $token !== $cacheToken)
            throw new \RuntimeException('验证失败，无权限或权限已过期，请重新生成token', self::AUTH_ERROR_CODE_NO_LOGIN);

        return $userId;
    }

    /**
     * @param $clientId
     * @return bool
     */
    public function isAllowUse($clientId)
    {
        if (empty($this->allowClientMap))
            return true;

        if (!in_array($clientId, $this->allowClientMap))
            return false;

        return true;
    }

    public function findAccountByNickname($nickname)
    {
        if( empty($nickname))
            return [];
        /**
         * @var $db \CDbConnection
         */
        $db = \Yii::app()->prometheus_db;
        $data = $db->createCommand('select email, account_id,tapd_nickname,tapd_name from tbl_other_platform_account where tapd_nickname=:tapd_nickname')
            ->queryRow(true,[':tapd_nickname'=> $nickname]);

        return $data?:[];
    }
}