<?php
/**
 * Created by PhpStorm.
 * User: young
 * Email: <<EMAIL>>
 * Date: 2025.01.14
 * Time: 09:59
 */

use common\modules\prometheus\library\other_platform\relate_prometheus\RelateAccountList;

class RelateAccountReadController extends PrometheusController
{
    public function actionIndex() {
//        $this->layout = false;
        $this->render('/relate_account/index.js', [
            'relate_account_manage_privilege' => $this->hasPrivilege('relate-account.base'),
        ]);
    }

    public function actionList(string $email = '') {
        $relateAccountList = new RelateAccountList();
        if (!empty($email)) {
            $relateAccountList->setEmails($email);
        }

        return $this->success([
            'list'  => $relateAccountList->find(),
            'count' => $relateAccountList->count(),
        ]);
    }
}