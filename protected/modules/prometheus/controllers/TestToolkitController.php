<?php

use common\library\account\external\UserInfoExternal;
use common\library\account\Helper;
use common\library\ai_agent\KnowledgeBaseAiAgent;
use common\library\ai_agent\utils\DslSqlAnalyzer;
use common\library\alibaba\customer\CustomerSyncHelper;
use common\library\alibaba\services\AlibabaTopClient;
use common\library\async_task\AsyncTask;
use common\library\async_task\AsyncTaskConstant;
use common\library\cash_collection\CashCollection;
use common\library\custom_field\CustomFieldService;
use common\library\email_identity\sync\CustomerSync;
use common\library\erp_service\qcloud\QCloudConstants;
use common\library\mail\MailContentHelper;
use common\library\notification\NotificationHelper;
use common\library\privilege_v3\PrivilegeConstants;
use common\library\privilege_v3\PrivilegeService;
use common\library\promotion_plan\PromotionPlanConstant;
use common\library\queue_v2\job\crontab\CompanyMoveToPublic;
use common\library\queue_v2\Queue;
use common\library\setting\user\UserSetting;
use common\library\statistics\config\FieldsConfig;
use common\library\todo\TodoConstant;
use common\library\version\CompanyVersion;
use common\library\version\Constant;
use common\models\client\CompanyHistoryPg;
use common\library\google_ads\recommendation\GoogleAdsRecommendationService;
use common\library\api\InnerApi;
use common\library\account\Client;
use common\library\acp\AlibabaAcpVaSyncSettingList;
use common\library\acp\AlibabaAcpVaSyncSettingBatchOperator;
use common\library\alibaba\store\AlibabaStore;
use common\library\performance_v2\rule\PerformanceV2Rule;
use common\library\department\DepartmentMember;
use common\library\account\UserList;
use common\library\performance_v2\goal_config\PerformanceV2GoalConfigList;

/**
 * Created by PhpStorm.
 * User: troy.li
 * Date: 2020-03-18
 * Time: 10:46 AM
 */

class TestToolkitController extends Controller
{
    // 给acp操作数据的client白名单
    private $acpWhiteClient = [ 1,3, 18133, 18120, 64293, 65994, 23412, 47508, 68569, 74240, 74269, 76227, 77393,
        77513, 6534, 79054, 59708, 28134,77011, 77011, 77011, 82175, 79700, 80156, 80156, 80156, 80356, 80367, 80421, 80430, 80583,
        80621, 80356, 80356, 80356, 79896, 80023, 80024, 80025, 80206, 80027, 77393, 80249, 80251, 80252, 80260, 80261, 80262, 80263,
        80264, 80265, 80266, 80267, 80496, 80789, 80791, 80792, 80795, 80796, 81149, 81150, 81151, 81152, 81153, 81154, 81582];

    // 在指定的client下做逻辑验证
    private $acpDebugClients = [6534];

    public const REDIS_KEY_EMAIL_CLIENTS = 'prometheus:aiService:getMailInfo:client_ids';

    public function filters()
    {
        return array(
            'login' => 'login - error,cancelAmesAuth,addUser,updateClientType,init,initUserConfig,finishTask,attend,acceptInvitation,DateRangeList,DepartmentUserIds,ResetDTCRollback,getPhoneCode,editLayoutConfig,calculateField,GetUserMessageContactMessage',
            'rateLimit' => 'rateLimit',
            'checkPrivilege' => 'checkPrivilege - error',
            'language' => 'language',
            'render' => 'render',
            'checkUserNum' => array('application.library.ClientUserNumFilter'),
            'expData' => 'expData',
            'validate' => 'validate - error',
            'env' => 'env',
        );
    }

    public function filterEnv($filterChain)
    {
        if (Yii::app()->params['env'] == 'production') {
            return $this->fail(-1, '无权限访问');
        }

        return $filterChain->run();
    }

    public function actionAddOkkiClient($size = 0, $version = '')
    {
        if (empty($size) || empty($version)) {
            return $this->fail(-1, 'empty');
        }
        $clientService = new \common\library\okki_personal\ClientService();
        $clientService->preInit($size, $version);
    }

    // 刷已经匹配信保订单等本地产品信息
    public function actionRefreshOrderProduct($client_id, $order_id)
    {
        $this->validate([
            'client_id' => 'required|int',
            'order_id' => 'required|int',
        ]);
        $user = \User::getLoginUser();
        if ($user->isEmpty()) {
            return $this->fail(-1,  "未登录无法操作");
        }
        $adminUserId = PrivilegeService::getInstance($client_id)->getAdminUserId();
        \User::setLoginUserById($adminUserId);
        $db = \PgActiveRecord::getDbByClientId($client_id);
        $orderSql = "select * from tbl_order where client_id = $client_id and order_id = $order_id and enable_flag = 1 limit 1";
        $orderInfo = $db->createCommand($orderSql)->queryRow();
        if (empty($orderInfo)) {
            return $this->fail(-1,  "订单不存在");
        }
        $orderProductSql = "select * from tbl_invoice_product_record where client_id = $client_id and refer_id = $order_id and enable_flag = 1";
        $orderProducts = $db->createCommand($orderProductSql)->queryAll();
        if (empty($orderProducts)) {
            return $this->fail(-1,  "订单产品不存在");
        }
        $skuIds  = array_unique(array_column($orderProducts, 'sku_id'));
        $skuApi  = new \common\library\product_v2\sku\SkuAPI($client_id);
        $skuList = $skuApi->webList(['sku_id' => $skuIds]);
        $skuMap  = array_column($skuList['list'], null, 'sku_id');

        $orderProductList = json_decode($orderInfo['product_list'], true);
        $orderProductList = array_column($orderProductList, null, 'unique_id');

        $isModify = false;
        foreach ($orderProducts as &$product) {
            $sku                       = $skuMap[$product['sku_id']] ?? [];
            if (empty($sku)) {
                continue;
            }
            $userId                    = !empty($product['user_id']) ? \common\library\util\PgsqlUtil::trimArray($product['user_id']) : [];
            $userId                    = is_array($userId) ? current($userId) : 0;
            $imageInfo                 = $sku['image_info'] ?? [];
            $productImages             = [];
            if (!empty($imageInfo['file_id'])) {
                $productImages = [[
                    'file_id'     => (string)$imageInfo['file_id'],
                    'user_id'     => (string)$userId,
                    'create_time' => xm_function_now(),
                ]];
            }
            $product['product_name']     = $sku['name'] ?? '';
            $product['product_cn_name']  = $sku['cn_name'] ?? '';
            $product['product_model']    = $sku['model'] ?? '';
            $product['product_image']    = $imageInfo['file_path'] ?? '';
            $product['product_images']   = $productImages;
            $product['sku_attributes']   = !empty($sku['attributes']) ? $sku['attributes'] : [];

            $recordId = $product['id'];
            if (isset($orderProductList[$recordId])) {
                $orderProductList[$recordId]['product_name']    = $product['product_name'];
                $orderProductList[$recordId]['product_cn_name'] = $product['product_cn_name'];
                $orderProductList[$recordId]['product_model']   = $product['product_model'];
                $orderProductList[$recordId]['product_image']   = $product['product_image'];
                $orderProductList[$recordId]['sku_attributes']  = $product['sku_attributes'];
            }
            $isModify = true;
        }
        unset($product);

        if ($isModify) {
            $orderModel = \common\models\client\Order::findById($order_id);
            if (!empty($orderModel)) {
                $productListStr = json_encode(array_values($orderProductList));
                $orderModel->product_list = $productListStr;
                $orderModel->update(['product_list']);
            }
            foreach ($orderProducts as $product) {
                $productRecord = \common\models\client\InvoiceProductRecord::model()->find('id=:id', [':id' => $product['id']]);
                $productRecord->product_name    = $product['product_name'];
                $productRecord->product_cn_name = $product['product_cn_name'];
                $productRecord->product_model   = $product['product_model'];
                $productRecord->product_image   = $product['product_image'];
                $productRecord->sku_attributes  = !empty($product['sku_attributes']) ? json_encode($product['sku_attributes']) : '{}';
                $productRecord->update(['product_name', 'product_cn_name', 'product_model', 'product_image', 'sku_attributes']);
            }
            return $this->success(['修改成功']);
        }
        return $this->success(['未修改']);
    }

    public function actionRunWaitApprovalRemindByClientId($client_id)
    {
        if (empty($client_id)) {
            return $this->fail(-1, '缺少client_id参数');
        }
        $obj = new ApprovalFlowCommand('', '');
        $obj->actionWaitApprovalRemindByClientId($client_id, 0);
        return $this->success([$client_id]);
    }

    public function actionRunWorkflow($rule_id, $refer_id = '', $dry_run = 1)
    {
        $user = \User::getLoginUser();
        $clientId = $user->getClientId();
        ProjectActiveRecord::setConnection(ProjectActiveRecord::getDbByClientId($clientId));
        PgActiveRecord::setConnection(PgActiveRecord::getDbByClientId($clientId));
        $rule = WorkflowRule::model()->findByPk($rule_id);
        $clientId = $rule['client_id'];
        $referIds = array_filter(explode(',', $refer_id));
        $trigger = new \common\library\workflow\trigger\RuleTrigger($clientId, $rule_id);
        $trigger->debug = true;
        if (count($referIds)) {
            $trigger->setReferIds($referIds);
        }
        $logs = $trigger->run($dry_run, 1000, false);
        /**
         * @var $handler \common\library\workflow\handler\HandlerRunner
         */
        return $this->success(json_decode($logs, true));
    }

    public function actionRefreshPrivilege($client_id)
    {
        $privilegeService = \common\library\privilege_v3\PrivilegeService::getInstance($client_id);
        $systemId = $privilegeService->getMainSystemId();
        $moduleIds = array_column(\PrivilegeClientModule::getModule($client_id), 'module_id');
        $adminUserId = $privilegeService->getAdminUserId();

        $privilegeService->initClient($systemId, $adminUserId, false, true, $moduleIds);

        $functionalIds = $privilegeService->getFunctionalIds(true);

        $result = [
            'system' => $systemId,
            'module' => $moduleIds,
            'admin' => $adminUserId,
            'functionalIds' => $functionalIds,
        ];

        return $this->success($result);
    }

    public function actionPrivilegeFieldDetail($user_id, $functional_id, $refer_type = 0)
    {
        $user = User::getUserObject($user_id);
        $clientId = $user->getClientId();

        $privilegeService = \common\library\privilege_v3\PrivilegeService::getInstance($clientId, $user_id);
        $roleList = current($privilegeService->getUserRoleList((array)$user_id));
        $privilegeField = new \common\library\privilege_v3\PrivilegeField($clientId);
        $roleFieldMap = $privilegeField->getBatchRoleFieldScope($functional_id, $privilegeService->getRoleIds());
        $referFieldMap = $privilegeField->getReferFieldScope($functional_id);
        $primaryFieldMap = $privilegeField->getPrimaryFieldScope($functional_id, $privilegeService->getRoleIds());

        $notEditable = array_reduce(\common\library\privilege_v3\Helper::getFieldIdByRoleScope(
            $clientId,
            $user_id,
            $functional_id,
            $refer_type,
            [\common\library\privilege_v3\PrivilegeConstants::FIELD_PRIVILEGE_SCOPE_READONLY,\common\library\privilege_v3\PrivilegeConstants::FIELD_PRIVILEGE_SCOPE_DISABLE,]
        ), 'array_merge', []);

        return $this->success(compact('roleFieldMap', 'referFieldMap', 'primaryFieldMap', 'notEditable'));
    }

    public function actionGetIndexDetail($userId, $model, $id)
    {
        User::setLoginUserById($userId);
        $class = '\\common\\models\\search\\'.$model;
        $res = $class::model()->findByPk($id)['_source'];

        return $this->success($res);
    }

    public function actionIndexOneCompany($userId, $id)
    {
        User::setLoginUserById($userId);
        $user = User::getLoginUser();
        $handler = new \common\library\server\search\CompanyHandler();
        $data = [
            'id'         => $id,
            'client_id'  => $user->getClientId(),
            'user_id'    => $user->getUserId(),
            'company_id' => $id,
            'type'       => Constants::SEARCH_INDEX_TYPE_UPDATE,
        ];

        $handler->initData($data);
        $ret = $handler->handle();

        $res = \common\models\search\CompanySearch::model()->findByPk($id)['_source'];
        return $this->success($res);
    }

    public function actionGoogleAdsRecommendation($client_id, $ads_account_id, $limit =10)
    {
        $service = new GoogleAdsRecommendationService($client_id, $ads_account_id);
        $result = $service->pullRecommendation($limit);

        return $this->success($result);
    }

    public function actionRunMoveToPublic($client_id, $notification = 0, $dry_run = 1)
    {
        $job = new CompanyMoveToPublic();
        $job->_debug = $dry_run;
        $job->_notification = $notification;
        $res = $job->process($client_id);

        return $this->success($res);
    }

    public function actionRunMoveToPublicTask($client_id)
    {
        if (empty($client_id)) {
            throw new RuntimeException("client_id不能为空");
        }
        $customerTask = new \common\library\server\crontab\task\CustomerTask();
        $customerTask->runAction('runMoveToPublic', [$client_id]);
        return $this->success([]);
    }

    public function actionSendApplyNotify($sendTo)
    {
        (new \common\library\apply_record\GoogleAdsRecommendApplyRecord())->notifyDaily($sendTo);
        (new \common\library\apply_record\GooglePageSpeedUrlRecord())->notifyDaily($sendTo);
        (new \common\library\apply_record\GooglePageSpeedApplyRecord())->notifyDaily($sendTo);
    }

    public function actionDepartmentInfo($clientId, $userId, array $departmentId, $privilege, $departmentRecursive = true)
    {
        $owners = \common\library\privilege_v3\Helper::getScopeDepartmentIds(
            $clientId,
            $userId,
            $departmentId,
            $privilege,
            $departmentRecursive
        );

        return $this->success($owners);
    }
    public function actionSendApplyNotify3($sendTo)
    {
        (new \common\library\apply_record\GoogleAdsRecommendApplyRecord())->notifyDaily($sendTo);
        (new \common\library\apply_record\GooglePageSpeedUrlRecord())->notifyDaily($sendTo);
        (new \common\library\apply_record\GooglePageSpeedApplyRecord())->notifyDaily($sendTo);
    }

    public function actionGetFileUrl($fileId, $userId)
    {
        User::setLoginUserById($userId);
        $file = new AliyunUpload();
        $file->loadByFileId($fileId, $userId);
        $file_url = $file->getFileUrl();
        $this->success($file_url);
    }

    public function actionPushDemoJob($content = '')
    {
        $content = ($content ?: date('Y-m-d H:i:s'));
        $job = new \common\library\queue_v2\job\demo\DemoJob($content);
        $messageId = \common\library\queue_v2\QueueService::dispatch($job);
        echo $messageId;
    }


    /*---------------------------------------九天进阶计划配合测试--------------------------------------------*/


    /**
     *  重置九天进阶计划
     *
     * @param int $client_id
     * @param $user_id
     * @param $task_id
     * @return false|string
     * @throws CDbException
     * @throws ProcessException
     */
    public function actionInitUserConfig(int $client_id, $user_id = [], $task_id = ''){
        $this->validate([
            'client_id' => 'required|integer',
        ]);

        $adminUserId = PrivilegeService::getInstance($client_id)->getAdminUserId();

        \User::setLoginUserById($adminUserId);
        if ($task_id){
            $setting = new UserSetting($client_id,$user_id,'nine_day_plan.task_list');
            if ($setting->isNew()){
                throw new RuntimeException('当前用户任务未初始化');
            }

            $taskList = array_column($setting->getValue(),null,'task_id');
            $taskList[$task_id]['is_finish'] = 0;
            $setting->value = json_encode(array_values($taskList));
            $setting->save();
        }else{
            $sql = "delete from tbl_user_setting where client_id = {$client_id} and `key` like 'nine_day_plan%'";
            if ($user_id){
                $sql .= "  and user_id = {$user_id} ";
            }
            $db = ProjectActiveRecord::getDbByClientId($client_id);
            $db->createCommand($sql)->execute();

            $sql = "delete from tbl_exam_record where client_id = {$client_id} and exam_name = 'promotion_plan' and exam_topic = 'nine_day_plan'";
            if ($user_id){
                $sql .= "  and user_id = {$user_id} ";
            }
            $res = \Yii::app()->db->createCommand($sql)->execute();

            $param = [
                'client_id' => $client_id,
                'timestamp' => time()
            ];
            $auth = \common\library\promotion_plan\Helper::makeAuth($param);
            $param['auth'] = $auth;

            $config = \Yii::app()->params['inner_api']['lighthouse'];
            $url = $config['host'] .'/lighthouse/advance-out/reset';
            $res = \HttpUtil::doPost($url, $param,2);
            \LogUtil::info("reqUrl={$url};reqParam=".json_encode($param).';resp='.$res);

            if (!$res){
                throw new RuntimeException('服务器故障，请稍后重试');
            }
            $res = json_decode($res,true) ?? [];
            if (!$res || $res['code'] != 0){
                throw new RuntimeException('服务器故障，请稍后重试');
            }
        }

        return $this->success('重置成功','');
    }

    /**
     *  发送九天进阶计划邀请
     *
     * @param $client_id
     * @param $scheme_id
     * @return void
     * @throws ProcessException
     * @throws Throwable
     */
    public function actionAttend($client_id,$scheme_id){
        $this->validate([
            'client_id' => 'required',
            'scheme_id' => 'required'
        ]);

        User::setLoginUserById(PrivilegeService::getInstance($client_id)->getAdminUserId());

        \common\library\promotion_plan\Helper::initUserConfig($client_id,'nine_day_plan',$scheme_id);
    }


    /**
     *  修改九天进阶计划任务完成状态
     *
     * @param int $client_id
     * @param int $user_id
     * @param $task_ids
     * @param $status
     * @return false|string
     * @throws ProcessException
     * @throws \Google\ApiCore\ApiException
     */
    public function actionEditTaskStatus(int $client_id,int $user_id,$task_ids = '', $status = 1)
    {
        $this->validate([
            'client_id' => 'required',
            'user_id' => 'required',
        ]);

        \User::setLoginUserById($user_id);
        $taskConfigKey = PromotionPlanConstant::KEY_MAP['nine_day_plan']['task_key'];
        \common\library\promotion_plan\Helper::getUserPromotionPlanConfig($client_id,$user_id,'nine_day_plan');

        $taskSetting = new UserSetting($client_id,$user_id,$taskConfigKey);
        $taskList = $taskSetting->getValue();

        $finishTaskIds = $task_ids ? explode(',',$task_ids) : array_column($taskList,'task_id');

        foreach ($taskList as &$task){
            if (!in_array($task['task_id'],$finishTaskIds)){
                continue;
            }
            $task['is_finish'] = (int)$status;
        }

        $taskSetting->value = json_encode($taskList);
        $taskSetting->save();

        \common\library\promotion_plan\Helper::finishTaskTrigger($client_id,$user_id,implode(',',$finishTaskIds));

        return $this->success([]);
    }

    /**
     * @param $key
     * @return false|string
     * @throws ProcessException
     * @throws Throwable
     * 用户接受邀请
     */
    public function actionAcceptInvitation($key){
        $user = User::getLoginUser();
        $this->validate([
            'key' => 'required'
        ]);


        \common\library\promotion_plan\Helper::initPlanTaskData($user->getClientId(),$user->getUserId(),$key);
        return $this->success([]);
    }

    /**
     * @param $key
     * @return false|string
     * @throws ProcessException
     * @throws Throwable
     * 用户接受邀请
     */
    public function actionInitUserData($key){
        $user = User::getLoginUser();
        $this->validate([
            'key' => 'required'
        ]);

        \common\library\promotion_plan\Helper::getUserPromotionPlanConfig($user->getClientId(),$user->getUserId(),$key);
        return $this->success([]);
    }

    /*
     * 测试使用 产生工作报告
     */
    public function actionBuilderWorkReport(int $userId,$type = 'daily', $date = '')
    {
        \User::setLoginUserById($userId);
        $user = \User::getLoginUser();
        $clientId = $user->getClientId();
        $userId = $user->getUserId();

        if (!in_array($type, ['week', 'daily', 'month'])) {
            return $this->fail(-1,'type 参数值不正确，请输入 daily, week, month');
        }

        if (!empty($date) && date('Y-m-d', strtotime($date)) != $date ) {
            return $this->fail(-1,'date 参数值不正确，请输入某个日期，例如：2021-06-01');
        }

        $data = \common\library\work_report\ReportService::builder($clientId, $userId, $type, $date, true);

        $this->success($data);
    }

    /*
     * 工作报告消息通知
     */
    public function actionSendWorkReportMail(int $userId, $type = 'daily', $date = '', $email = '')
    {
        $user = \User::getLoginUser();
        $clientId = $user->getClientId();

        if (!in_array($type, ['week', 'daily', 'month'])) {
            return $this->fail(-1,'type 参数值不正确，请输入 daily, week, month');
        }

        if (!empty($date) && date('Y-m-d', strtotime($date)) != $date ) {
            return $this->fail(-1,'date 参数值不正确，请输入某个日期，例如：2021-06-01');
        }

        $userSetting = new UserSetting($clientId, $userId, UserSetting::USER_LANGUAGE);
        $lang = $userSetting->getValue();

        $userSetting = new UserSetting($clientId, $userId, UserSetting::NOTIFICATION_MAIL_SETTING);
        $mailSetting = $userSetting->getValue();

        switch ($type) {
            case \common\library\work_report\Constant::CYCLE_DAILY:
                $notifyType = \common\library\notification\Constant::NOTIFICATION_TYPE_WORK_REPORT_DAILY;
                $key = \common\library\work_report\Constant::WEB_DAILY_REPORT_KEY;
                break;
            case \common\library\work_report\Constant::CYCLE_WEEK:
                $notifyType = \common\library\notification\Constant::NOTIFICATION_TYPE_WORK_REPORT_WEEK;
                $key = \common\library\work_report\Constant::WEB_WEEKLY_REPORT_KEY;
                break;
            case \common\library\work_report\Constant::CYCLE_MONTH:
                $notifyType = \common\library\notification\Constant::NOTIFICATION_TYPE_WORK_REPORT_MONTH;
                $key = \common\library\work_report\Constant::WEB_MONTHLY_REPORT_KEY;
                break;
            default:
                return $this->fail(-1,'type 参数值不正确，请输入 daily, week, month');
        }

        if (!isset($mailSetting[$notifyType]) || empty($mailSetting[$notifyType])) {
            echo '消息通知选项关闭中，无法发送';
            return;
        }

        $dateStr = date('Ym');
        $data = \common\library\work_report\ReportService::builder($clientId, $userId, $type, $date, true);
        $report = \common\library\work_report\ReportService::getWorkReport($clientId, $userId, $key, $data['end']);

        if ($report) {
            $report['user_language'] = $lang;
        }

        $privilegeService = PrivilegeService::getInstance($clientId);
        //没有新版工作台权限过滤掉
        if (!$privilegeService->hasFunctional(PrivilegeConstants::FUNCTIONAL_WORKBENCH_V2)) {
            echo 'dx版本不允许发送工作报告邮件';
            return false;
        }
        //圈战账号过滤掉
        if ($privilegeService->hasFunctional(PrivilegeConstants::FUNCTIONAL_ALI_SALES_RACE)) {
            echo '圈战账号不允许发送工作报告邮件';
            return false;
        }
        if ($type = \common\library\work_report\Constant::CYCLE_MONTH)
        {
            if ($privilegeService->getMainSystemId() == \common\library\privilege_v3\PrivilegeConstants::DX_SYSTEM_ID) {
                echo 'dx版本不允许发送工作月报邮件';
                return false;
            }
            $filterDate = date('Y-m-d', strtotime('-30 day'));
            $filterUsers = \common\library\task\Helper::filterUserBYLoginDate($clientId, $filterDate, [$userId]);
            if (!in_array($userId, $filterUsers))
            {
                echo "userId={$userId} sendWorkReportMail fail: user last_login_time <= {$filterDate} ";
                return false;
            }
        }
        try {
            LogUtil::info('notification html start');
            $api = new \common\library\api\InnerApi('subscribe_statistic_report');
            $api->setHttpMethod(\common\library\api\InnerApi::HTTP_METHOD_POST_JSON);
            $api->setTimeout(5);
            $response = $api->call('work_report_html', $report);

            if (isset($response['data']) && !empty($email)) {
                if($type == \common\library\work_report\Constant::CYCLE_MONTH) {
                    //与OS定义的tag edm_userId_clientId_Ym
                    $tag =  'edm_' . $userId . '_' . $clientId . '_' . $dateStr;
                    SystemNotifyMailUtil::sendMailAndAttach($report['desc'], $response['data'], $email, null, null, $tag, true);
                }else {
                    \SystemNotifyMailUtil::sendMail($report['desc'], $response['data'], [$email]);
                }

                echo '邮件通知已发送成功';
            }
        } catch (\Exception $e) {
            var_dump($e->getMessage());
            return false;
        }

        echo $response['data'];
    }

    public function actionSendWorkReportNotify(int $userId, $type = 'daily', $date = '')
    {
        \User::setLoginUserById($userId);
        $user = \User::getLoginUser();
        $clientId = $user->getClientId();
        $userId = $user->getUserId();

        if (!in_array($type, ['week', 'daily', 'month'])) {
            return $this->fail(-1,'type 参数值不正确，请输入 daily, week, month');
        }

        if (!empty($date) && date('Y-m-d', strtotime($date)) != $date ) {
            return $this->fail(-1,'date 参数值不正确，请输入某个日期，例如：2021-06-01');
        }

        $userSetting = new UserSetting($clientId, $userId, UserSetting::NOTIFICATION_PIN_SETTING);
        $mailSetting = $userSetting->getValue();
        switch ($type) {
            case 'daily':
                $notifyType = \common\library\notification\Constant::NOTIFICATION_TYPE_WORK_REPORT_DAILY;
                $key = \common\library\work_report\Constant::WEB_DAILY_REPORT_KEY;
                $reportType = \protobuf\PushData\PBWebReportType::TYPE_DAILY;
                break;
            case 'week':
                $notifyType = \common\library\notification\Constant::NOTIFICATION_TYPE_WORK_REPORT_WEEK;
                $key = \common\library\work_report\Constant::WEB_WEEKLY_REPORT_KEY;
                $reportType = \protobuf\PushData\PBWebReportType::TYPE_WEEK;
                break;
            case 'month':
                $notifyType = \common\library\notification\Constant::NOTIFICATION_TYPE_WORK_REPORT_MONTH;
                $key = \common\library\work_report\Constant::WEB_MONTHLY_REPORT_KEY;
                $reportType = \protobuf\PushData\PBWebReportType::TYPE_MONTH;
                break;
            default:
                return $this->fail(-1,'type 参数值不正确，请输入 daily, week, month');
        }

        if (!isset($mailSetting[$notifyType]) || empty($mailSetting[$notifyType])) {
            echo '消息通知选项关闭中，无法发送';
            return;
        }

        $data = \common\library\work_report\ReportService::builder($clientId, $userId, $type, $date, true);
        $report = \common\library\work_report\ReportService::getWorkReport($clientId, $userId, $key, $data['end']);

        if ($report) {
            $notification = new \common\library\notification\Notification($clientId, $notifyType);
            $notification->user_id = $userId;
            $notification->create_user_id = 0;
            $notification->setSourceData([
                'report_id' => $report['report_id'],
                'title' => $report['title'],
                'name' => $report['name'],
                'desc' => $report['desc']
            ]);

            \common\library\notification\PushHelper::pushNotification($clientId, $userId, $notification);

            $title = "";
            $desc = "";

            $hasPerformance = false;
            if (in_array($type,['week','month'])) {

                //标题：上周工作周报已更新
                //内容：
                //取数优先级：按顺序取第一个绩效不为0的数据；若绩效为0，则新建客户数>新建跟进数>邮件发送数>营销发送数；
                //其中，绩效优先取金额的字段，再取其他非金额的字段；
                //内容：
                //·绩效金额不为0：上周#绩效名称#为#币种##金额#，环比上升了/下降了7%了；
                //·绩效金额为0，绩效有非零指标：上周#绩效名称#为#数值#，环比上升了/下降了7%了；
                //·绩效都为0，关键指标不为0：上周新建客户数n家/新建跟进数n条/邮件发送数n封/营销发送数n封，环比提升75%/下降65%；
                //都为0：不推送消息
                //
                //若环比因为分母为0时，则环比提升75%/下降65%改为：实现了0的突破
                $reportPerformance = $report['reportObject']['performance']??[];
                $descKey = ($type == 'week') ? '周' : '月';
                foreach ($reportPerformance as $performance) {

                    //本周完成金额
                    $week_finish_amount = $performance['week_finish_amount']['value'] ?? null;
                    //环比趋势
                    $trend_ratio = $performance['trend_ratio'] ?? null;

                    if (!is_null($week_finish_amount)) {

                        //绩效金额不为0
                        if ($week_finish_amount!=0) {

                            $desc = "上{$descKey}".str_replace("本{$descKey}", '', ($performance['name'] ?? ''))."为".$week_finish_amount;

                            if (!is_null($trend_ratio) && $trend_ratio !='-') {
                                $desc .='，环比'.($trend_ratio>0?'提升':'下降').$trend_ratio.'%';
                            }

                            if ($trend_ratio =='-') {
                                $desc .= '，实现了0的突破';
                            }

                            $hasPerformance = true;
                            $title = "上{$descKey}工作周报已更新";
                            break;
                        }
                    }
                }
            }


            //·取数优先级：新建客户数>新建跟进数>邮件发送数>营销发送数
            //·昨日新建客户数n家/新建跟进数n条/邮件发送数n封/营销发送数n封，环比提升75%/下降65%；
            //若四组数据都为0，则不推送；
            if ((!$hasPerformance && in_array($type,['week', 'month'])) || $type == 'daily') {

                $reportXs1 = $report['reportObject']['work']['xs1']??[];

                if (!empty($reportXs1)) {

                    $reportKeys = [
                        'company_add_count' => '新建客户数',
                        'customer_remark_count' => '新建跟进数',
                        'mail_send_count' => '邮件发送数',
                        'edm_finish_count' => '营销发送数'
                    ];

                    $unitMap = [
                        'company_add_count' => '家',
                        'customer_remark_count' => '条',
                        'mail_send_count' => '封',
                        'edm_finish_count' => '封'
                    ];

                    foreach ($reportKeys as $reportKey => $titleName) {

                        $trend_ratio = $reportXs1[$reportKey]['trend_ratio'] ??'';

                        if(isset($reportXs1[$reportKey]['value'])
                            && $reportXs1[$reportKey]['value']>0) {

                            $desc = '昨日';
                            if ($type == 'week') {
                                $desc = '上周';
                            }

                            $desc .= $titleName.$reportXs1[$reportKey]['value'].($unitMap[$reportKey] ?? '条');
                            if(isset($reportXs1[$reportKey]['trend_ratio'])
                                && $reportXs1[$reportKey]['trend_ratio']!='-') {
                                $desc .='，环比'.($reportXs1[$reportKey]['trend_ratio']>0?'提升':'下降').($reportXs1[$reportKey]['trend_ratio']).'%';
                            }

                            if ($trend_ratio =='-') {
                                $desc .= '，实现了0的突破';
                            }

                            $title = "昨日工作日报已更新";

                            if ($type == 'week') {
                                $title = "上周工作周报已更新";
                            } elseif ($type == 'month') {
                                $title = "上月工作月报已更新";
                            }

                            break;
                        }
                    }
                }
            }


            if (!empty($title) && !empty($desc)) {
                \common\library\notification\PushHelper::pushDesktopWorkReport($clientId, $userId, $report['report_id'],$title, $desc,$reportType);
            }
        }
    }

    public function actionTestKey()
    {

        $skey = $_COOKIE[\common\library\account\service\LoginService::$prefix.\common\library\account\service\LoginService::$skeyName];
        $params = ['pskey' => $skey];

        $api = new InnerApi('passport_skey');
        $data = $api->call('check_skey', $params);

        return $this->success($data['data']);
    }


    /*----skyhe-----------------------------------------------------------------------------------------*/

    /**
     * 清理询盘设置功能权限
     * @param $client_id
     * @return false|string
     */
    public function actionCleanAmesInquiry($client_id)
    {
        $this->validate([
            'client_id' => 'required|not_empty'
        ]);
        \common\library\account\Client::setEnableCache(false, true);
        $clientList = \Client::model()->findAll("eid > 0 and client_id in ({$client_id})");
        $uniqId = uniqid('', true);
        \LogUtil::info('actionCleanAmesInquiry_info', [
            'uniqId' => $uniqId,
            'total' => count($clientList)
        ]);
        if (empty($clientList)) {
            return false;
        }
        $functionId = PrivilegeConstants::FUNCTIONAL_INQUIRY;//询盘
        $privilegeId = PrivilegeConstants::PRIVILEGE_SETTING_CUSTOMER_INQUIRY;//询盘设置
        foreach ($clientList as $clientInfo) {
            try {
                $privilegeService = PrivilegeService::getInstance($clientInfo->client_id);
                $privilegeService->getClientPrivilege()->removeFunctional($functionId);
                $roleIds = $privilegeService->getUserPrivilege()->getRoles()->getPrivilegeRoles($privilegeId);
                if (empty($roleIds)) {
                    \LogUtil::info('actionCleanAmesInquiry_info', [
                        'uniqId' => $uniqId,
                        'client_id' => $clientInfo->client_id,
                        'only delete function, no roleIds'
                    ]);
                    continue;
                }
                // 排除超管
                $roleIds = array_diff($roleIds, [PrivilegeConstants::ROLE_CRM_ADMIN]);
                foreach ($roleIds as $roleId) {
                    $privilegeService->getUserPrivilege()->getRoles()->removeRolePrivilege($roleId, $privilegeId);
                }
            } catch (\Throwable $t) {
                \LogUtil::error('actionCleanAmesInquiry_error', [
                    'uniqId' => $uniqId,
                    'client_id' => $clientInfo->client_id ?? 0,
                    'message' => $t->getMessage(),
                    'line' => $t->getLine(),
                ]);
            }
        }
        \LogUtil::info('actionCleanAmesInquiry_end', [
            'uniqId' => $uniqId,
        ]);
        return $this->success([$uniqId]);
    }

    /**
     * 历史ames企业同步普通用户
     * @param $client_id
     * @return false|string
     */
    public function actionNotifyAmes($client_id)
    {
        $this->validate([
            'client_id' => 'required|not_empty'
        ]);
        \common\library\account\Client::setEnableCache(false, true);
        $clientList = \Client::model()->findAll("eid > 0 and client_id in ({$client_id})");
        $uniqId = uniqid('', true);
        \LogUtil::info('actionNotifyAmes_info', [
            'uniqId' => $uniqId,
            'total' => count($clientList)
        ]);
        if (empty($clientList)) {
            return false;
        }
        $roleName = '普通用户';
        foreach ($clientList as $clientInfo) {
            try {
                $clientInfo = \common\library\account\Client::getClient($clientInfo->client_id);
                $privilegeService = PrivilegeService::getInstance($clientInfo->client_id);
                $roleId = $privilegeService->getRoleIdByName($roleName);
                if (empty($roleId)) {
                    \LogUtil::info('actionNotifyAmes_info', [
                        'uniqId' => $uniqId,
                        'client_id' => $clientInfo->client_id,
                        'no roleId',
                    ]);
                    continue;
                }
            } catch (\Throwable $t) {
                \LogUtil::error('actionNotifyAmes_error', [
                    'uniqId' => $uniqId,
                    'client_id' => $clientInfo->client_id ?? 0,
                    'message' => $t->getMessage(),
                    'line' => $t->getLine(),
                ]);
            }
        }
        \LogUtil::info('actionNotifyAmes_end', [
            'uniqId' => $uniqId,
        ]);
        return $this->success([$uniqId]);
    }

    /**
     * 模拟店铺启用
     * @param $eid
     */
    public function actionShopEnable($eid)
    {
        $this->validate([
            'eid' => 'required|not_empty'
        ]);
        $data = ['eid' => $eid];
        $result = \common\library\ames\service\InquiryService::processShopEnable($data);
        return $this->success([$result ? 1 : 0]);
    }


    /*---------------------------------------------------------------------------------------------*/

    public function actionCancelAmesAuth($client_id)
    {
        $client = new \common\library\account\Client($client_id);
        if ($client->client_type != \common\library\account\Client::CLIENT_TYPE_INTERNAL) {
            throw new RuntimeException("clientId={$client_id} 不是测试公司 暂无权限");
        }

        $authRecord = new \common\library\ames\AmesAuthRecord($client_id);
        if ($authRecord->isNew()) {
            throw new RuntimeException("该公司未授权");
        }

        if ($authRecord->enable_flag == AmesAuthRecord::DISABLE_FLAG) {
            throw new RuntimeException("该公司已经取消授权");
        }
        $authRecord->cancelAuth();

        $client = new \common\library\account\Client($client_id);
        $client->eid = 0;
        $client->save();

        $userIds = array_column(UserInfo::findAllByClientId($client_id), 'user_id');
        foreach ($userIds as $userId)
        {
            $userInfo = new \common\library\account\UserInfo($userId, $client_id);
            $userInfo->user_eid = 0;
            $userInfo->eid = 0;
            $userInfo->save();
        }
        return $this->success([]);
    }

    public function actionIcbuAmesMail(){
        $subject = 'Test IcbuMail';
        $content = 'Test IcbuMail content';
        $toArray = ['<EMAIL>','<EMAIL>'];
        $result = \SystemNotifyMailUtil::sendIcbuAmesMail($toArray, $subject, $content);
        $this->success($result);
    }

    /**
     * 初始化仓库
     * @param string $client_id
     * @param int $skip_privilege
     * @return false|string
     */
    public function actionInitWarehouse(string $client_id, int $skip_privilege = 0) {
        try {
            $privilegeService = \common\library\privilege_v3\PrivilegeService::getInstance($client_id);
            if (!$skip_privilege && !$privilegeService->hasFunctional(\common\library\privilege_v3\PrivilegeConstants::FUNCTIONAL_PURCHASE))
                throw new RuntimeException("client_id: {$client_id} 没有采购模块权限");

            \common\library\CommandRunner::run(
                'warehouse',
                'initWarehouse',
                [
                    'clientId' => $client_id,
                    'skip_privilege' => $skip_privilege,
                ], "/dev/null", 0);
        } catch (Exception $e) {
            return $this->fail(-1, $e->getMessage());
        }
        return $this->success();
    }

    // ======================== acp项目测试计划 start ===============================

    /**
     * 检查是否可以删除
     * time: 10:39 AM
     * user: huagongzi
     * @return void
     */
    protected function checkWhiteClient(int $clientId){
        if(!in_array($clientId,$this->acpWhiteClient)){
            throw new RuntimeException('指定client不在白名单内');
        }
    }

    /**
     * 删除va配置信息
     * time: 12:01 PM
     * user: huagongzi
     * @param $client_id
     * @return void
     */
    public function actionDeleteVaConfig($client_id, $store_id = 0, $force = false){

        // 再次做一下判断，防止别人乱用
        $this->checkWhiteClient($client_id);

        $client = new Client($client_id);

        if($client->isNew()){
            throw new RuntimeException('指定的client_id不存在');
        }

        $store = new AlibabaStore($client_id,$store_id);

        if($store->isNew()){
            throw new RuntimeException('指定的store_id不存在');
        }

        $vaModel = new AlibabaAcpVaSyncSettingBatchOperator($client_id);

        if($store_id){
            $vaModel->setStoreIds([$store_id]);
        } else {
            if(!$force){
                throw new RuntimeException('没有指定storeid将删除该client下所有的店铺配置，请加参数force=true');
            }
        }

        $res = $vaModel->delete();

        if($res){
            return $this->success(['delete' => $res]);
        } else {
            throw new RuntimeException('删除失败!');
        }

    }


    /**
     * 测试用：硬删除从acp同步下来的收款单
     * @params $client_id
     * @return void
     */
    public function actionHardDeleteCashCollectionFromAcp($clientId){
        $this->checkWhiteClient($clientId);
        $cashCollectionList = new \common\library\cash_collection\CashCollectionList($clientId);

        $cashCollectionList->setFields(['cash_collection_id']);
        $cashCollectionList->setCreateType(CashCollection::COLLECTION_CREATE_TYPE_ACP_SYNC);
        $cashCollectionList->setType(["Alibaba.com Pay"]);
        $cashCollectionList->setSkipPermission(true);
        $cashCollectionList->setLimit(100);
        $list = $cashCollectionList->find();

        if (empty($list)){
            throw new RuntimeException('没有从acp同步下来的回款单数据');
        }

        $ids = implode(',', array_column($list,'cash_collection_id'));

        $sql = "DElETE FROM tbl_cash_collection where client_id = {$clientId} and cash_collection_id IN ({$ids})";
        $db = \PgActiveRecord::getDbByClientId($clientId);
        $count = $db->createCommand($sql)->execute();

        LogUtil::info("sql -- [$sql]");

        $this->success([
            'count' => $count
        ]);
    }


    /**
     * 测试用： 硬删除 acp配置页中的 同步Alibaba.com Pay收款单信息 按钮记录
     * @param $clientId
     * @param $storeId
     * @return void
     * @throws ProcessException
     */
    public function actionHardDeleteAcpCashCollectionSetting($clientId,$storeId)
    {
        $this->checkWhiteClient($clientId);

        $sql = "DElETE FROM tbl_alibaba_cash_collection_detail_sync_setting where client_id = {$clientId} and store_id = {$storeId} LIMIT 1";
        $db = \Yii::app()->db;
        $db->createCommand($sql)->execute();

        LogUtil::info("sql -- [$sql]");
        $this->success();
    }

    /**
     * 测试用： 批量修改订单表下的资金账户信息
     * @param $client_id
     * @param $from_capital_account_id
     * @param $to_capital_account_id
     * @return void
     * @throws ProcessException
     */
    public function actionChangeOrderCapitalAccount($client_id,$from_capital_account_id,$to_capital_account_id = 0)
    {
        $this->checkWhiteClient($client_id);
        $db = PgActiveRecord::getDbByClientId($client_id);
        $sql = "SELECT order_id FROM tbl_order WHERE client_id = {$client_id} AND enable_flag = 1 AND capital_account_id = {$from_capital_account_id} LIMIT 100";
        $orderIds = $db->createCommand($sql)->queryColumn();
        if (empty($orderIds)){
            throw new RuntimeException("该资金账户下不存在订单");
        }
        $updateOrderIds = implode(',',$orderIds);

        $updateSql = "UPDATE tbl_order SET capital_account_id = {$to_capital_account_id} WHERE  order_id IN ({$updateOrderIds}) ";
        $count = $db->createCommand($updateSql)->execute();
        $this->success([
            'count' => $count,
            'order_ids' => $orderIds
        ]);


    }

    /**
     * acp外汇明细同步任务测试
     * time: 10:21 AM
     * user: huagongzi
     * @param $client_id
     * @param $store_id
     * @param $user_id
     * @param $task_id
     * @param $refresh_flag
     * @param $retry
     * @return false|string
     * @throws ProcessException
     */
    public function actionAcpFundDetailSyncTaskDebug($client_id, $store_id,$user_id,$task_id,$refresh_flag = 0,$retry = 0){
        User::setLoginUserById($user_id, $client_id);

        if(!in_array($client_id, $this->acpDebugClients)) {
            throw new ProcessException("指定client_id不在debug指定的名单内");
        }

        $taskSetting           = new \common\library\acp\AlibabaSyncTask($client_id, $store_id, $task_id, $user_id);
        $cashCollectionSetting = new \common\library\acp\AlibabaCashCollectionDetailSyncSetting($client_id, $store_id);

        if($taskSetting->isNew() || $cashCollectionSetting->isNew()){
            throw new ProcessException("外汇明细任务或外汇明细开关配置不存在,task_id:{$task_id}");
        }

        if($retry){
            $taskSetting->status = \common\library\acp\Constant::ACP_DATA_SYNC_TASK_FLAG_START;
        }

        $taskSetting->type = \common\library\acp\AlibabaSyncTask::TYPE_ACP_FUND_DETAIL;
        $taskSetting->save();

        $executor = new \common\library\acp\executors\AlibabaSyncFundDetailTaskExecutor($taskSetting);
        $executor->setRefreshAllFlag($refresh_flag);
        $executor->setRetry($retry);
        $executor->run();

        $logInfo = [
            "task_step"         => "start_sync_fund_detail_finish",
            "task_name"         => "syncFundDetail",
            "client_id"         => $client_id,
            "store_id"          => $store_id,
            "task_id"           => $task_id,
            "user_id"           => $user_id,
            "refresh_all_flag"  => $refresh_flag,
            "retry"             => $retry
        ];

        \LogUtil::info(json_encode($logInfo));

        return $this->success($logInfo);
    }

    public function actionNotifyErpService()
    {
        $user = User::getLoginUser();
        $userId = $user->getUserId();
        $clientId = $user->getClientId();

        $service = ErpService::findByClientIdAndType($clientId, ErpService::TYPE_KINGDEE);
        if (empty($service)) {
            return $this->success([]);
        }
        $serviceId = $service->service_id;

        NotificationHelper::erpServiceAuthSuccessMsg([$userId], $userId, $serviceId, ErpService::TYPE_KINGDEE);
        NotificationHelper::erpServiceAuthFailedMsg([$userId], $userId, $serviceId, ErpService::TYPE_KINGDEE);

        return $this->success([]);
    }

    // ============================ acp项目测试计划 end ===========================


    public function actionTriggerkingDeeToken(int $client_id = 0)
    {
        if (empty($client_id)) {
            return $this->fail(-1, "client_id is empty");
        }
        $kingdeeService = new \common\library\erp_service\kingdee\KingDeeApiService();
        $result = $kingdeeService->triggerToPushAppAuthorize(\common\library\erp_service\Helper::encrypt($client_id));
        return $this->success(!empty($result) ? json_decode($result, true) : []);
    }

    public function actionCleanLesseeToken(int $delete = 0)
    {
        $redis = \Yii::app()->redis;
        $admin = QCloudConstants::getLesseeManageUserInfo();
        $md5 = 'qcloud:' . md5(trim($admin['username']) . trim($admin['password']));
        if (empty($delete)) {
            $token = $redis->executeCommand('get', [$md5]);
            return $this->success([$token]);
        } else {
            $result = $redis->executeCommand('expire', [$md5, 0]);
            return $this->success([$result ? 1 : 0]);
        }
    }

    public function actionTriggerKingDeeToPush(int $client_id = 0)
    {
        if (empty($client_id)) {
            return $this->fail(-1, 'client_id empty');
        }
        $kingdeeService = new \common\library\erp_service\kingdee\KingDeeApiService();
        $result = $kingdeeService->triggerToPushAppAuthorize(\common\library\erp_service\Helper::encrypt($client_id));
        return $this->success(!empty($result) ? json_decode($result, true) : []);
    }

    public function actionDateRangeList(string $start_date, string $end_date, string $time_type = 'this_month'){
        $this->validate([
            'start_date' => 'string|required',
            'end_date'   => 'string|required',
        ]);
        $list = \common\library\performance_v2\Helper::getYearTimeTypeRange($start_date, $end_date, $time_type);
        return $this->success($list);
    }

    public function actionUserGoal(int $client_id, int $rule_id){

        $this->validate([
            'client_id' => 'integer|required',
            'rule_id'   => 'integer|required',
        ]);

        $ruleInfo = new PerformanceV2Rule($client_id,$rule_id);

        if($ruleInfo->performance_type == \PerformanceV2Goals::TARGET_RESULT_GOAL){
            return $this->success("仅支持过程目标查询");
        }

        $userList = (new UserList())->setClientId($client_id)->setEnableFlag(1)->setFields('user_id,client_id,email,nickname')->find();
        $userList = \ArrayUtil::index($userList, 'user_id');
        $result   = [];

        $configList = new PerformanceV2GoalConfigList($client_id);
        $configList->setRuleIds($rule_id);
        $configList->getFormatter()->setMergeList(true);
        $data = $configList->find();

        foreach ($data as $config){
            $config["user_max_account_map"] = common\library\performance_v2\Helper::getUserGoalListByConfig($client_id, $config['iterms']);
            $userInfoList                   = [];

            foreach ($config["user_max_account_map"] as $userId => $userInfo){
                if(isset($userList[$userId])){
                    $userList[$userId]['amount'] = $userInfo['amount'];
                    $userInfoList[]              = $userList[$userId];
                }
            }

            $ruleInfo = $ruleInfo->getAttributes();
            $itemInfo = [
                "rule_id"           => $ruleInfo['rule_id'],
                "name"              => $ruleInfo['name'],
                "description"       => $ruleInfo['description'],
                "client_id"         => $ruleInfo['client_id'],
                "user_total_amount" => array_sum(array_column($userInfoList,'amount')),
                "user_id_list"      => implode(',', array_column($userInfoList,'user_id')),
            ];
            $result = [
                'rule_info'      => $itemInfo,
                'user_info_list' => $userInfoList
            ];
        }

        return $this->success($result);
    }

    public function actionDepartmentUserIds(int $client_id,int $department_id){
        $userIds  = (new DepartmentMember($client_id))->getMemberUserIds($department_id, true);

        return $this->success(["department_user_ids" => implode(',', $userIds)]);
    }


    public function actionTestMailPerformance(int $client_id, int $mail_id)
    {
        if (!in_array($client_id,[6534]))
        {
            $this->success(['该client不能调用这个方法']);
            return ;
        }
        $adminUserId = \common\library\privilege_v3\PrivilegeService::getInstance($client_id)->getAdminUserId();
        User::setLoginUserById($adminUserId);

        $mail = new \common\library\mail\Mail($mail_id,$adminUserId);
        $callBack = new \common\library\mail\service\CallbackService($mail);
        $callBack->runPerformance();
        $this->success([$mail_id]);
    }

    /**
     * 运行系统工作报告提交提醒脚本
     *
     * @param string $setIds
     * @return void
     */
    public function actionTestRunWorkJournalRemind()
    {
        // 校验，只有测试环境才能运行
        if (\Yii::app()->params['env'] == 'test') {
            \common\library\CommandRunner::run('WorkJournal', 'WorkJournalSubmitRemind', [], '/dev/null', 0);
        }
        return $this->success();
    }

    public function actionSyncWideTable(int $clientId, int $type, int $companyId = 0, int $orderId = 0, int $opportunityId = 0)
    {
        // 校验，只有测试环境才能运行
        if (\Yii::app()->params['env'] == 'test') {
            \common\library\ai_agent\Helper::syncWideTable($clientId, $type, $companyId, $orderId, $opportunityId, 0);
        }
        return $this->success();
    }

    public function actionPushStatisticFeed($clientId=9650, $userId = null, $timeStamp = null)
    {
        if (\Yii::app()->params['env'] == 'test') {
            $timeStamp = $timeStamp ?? time();
            $params = [
                'clientIds' => implode(',', [$clientId]),
                'dryRun' => 0,
                'timeStamp' => $timeStamp,
            ];
            $insightAgent = new \common\library\ai_agent\InsightAiAgent($clientId, $userId);
            $insightAgent->setDryRun(0);
            $insightAgent->setSkipOthernessCheck(true);
            $insightAgent->process();
            \common\library\CommandRunner::run('Insight', 'GenerateGoalCompletionFeed', $params, '/dev/null', 0);
        }
        $this->success(['env' => \Yii::app()->params['env']]);
    }

    public function actionUpdateStatisticFeedStatus($clientId, $userId = null, $feedId = null)
    {
        if (\Yii::app()->params['env'] == 'test') {
            if ($userId && $feedId) {
                $feedObj = new \common\library\todo\Feed(TodoConstant::OBJECT_TYPE_STATISTIC, TodoConstant::TODO_TYPE_STATISTIC_INSIGHT);
                $feedObj->setAsync(false);
                $feedObj->updateFeedStatusByFeedId(TodoConstant::FEED_STATUS_UN_REREAD, [$feedId]);
            }else {
                \common\library\CommandRunner::run('xyy', 'RefreshFeed', ['clientIds' => $clientId], '/dev/null', 0);
            }
            $this->success();
        }
    }

    public function actionAutoRunassetAnalysis($config_id = null, $client_id = null)
    {
        if (\Yii::app()->params['env'] == 'test') {
            \common\library\CommandRunner::run('OkkiAi', 'aiAnalysisSubscription', ['dryRun' => 0, 'configId' => $config_id, 'clientIds' => $client_id], '/dev/null', 0);
        }
        return $this->success();
    }

    public function actionFreshCache($client_id = null, $user_id = null)
    {
        \common\library\CommandRunner::run('xyy', 'FreshCache', ['clientId' => $client_id, 'userId' => $user_id], '/dev/null', 0);
        $this->success();
    }

    public function actionSendSubscribeEmail($type = 1)
    {
        if ($type == \common\library\ai_agent\AssetAnalysisAiAgent::NOTICE_TYPE_SUCCESS) {
            $templateData = '
{"task_id":3515216072,"title":"\u60a8\u7684\u300cAI\u5ba2\u6237\u5206\u6790-20240507\u300d\u62a5\u544a\u5df2\u751f\u6210\uff0c\u8bf7\u67e5\u9605","desc":"","is_admin":false,"status":1,"echo_params":[{"key":"common.visible","label":"\u67e5\u770b\u8303\u56f4","value":"jolieyeah"},{"key":"common.date","label":"\u65f6\u95f4\u533a\u95f4","value":"2023-11-09~2024-05-07"}],"list":[{"favorite":0,"process_record_ids":[3515216073,3515216104,3515216123,3515216158],"title":"\u5ba2\u6237\u76d8\u70b9","conclusion":"\u6570\u636e\u8868\u660e\uff0c\u5c3d\u7ba1\u5927\u591a\u6570\u5ba2\u6237\u6765\u6e90\u548c\u56fd\u5bb6\u4fe1\u606f\u672a\u77e5\uff0c\u4f46\u963f\u91cc\u5df4\u5df4B2B\u5e73\u53f0\u662f\u5df2\u77e5\u6765\u6e90\u4e2d\u6700\u4e3b\u8981\u7684\u6e20\u9053\uff0c\u4e14\u6240\u6709\u4e0b\u5355\u5ba2\u6237\u5747\u6765\u81ea\u8be5\u5e73\u53f0\u3002ERP\u540c\u6b65\u3001\u5c0f\u6ee1\u53d1\u73b0\u548c\u73af\u7403\u5e02\u573a\u5e73\u53f0\u867d\u7136\u8f6c\u5316\u7387\u9ad8\uff0c\u4f46\u5ba2\u6237\u57fa\u6570\u53ef\u80fd\u8f83\u5c0f\u3002\u4f2f\u5229\u5179\u867d\u7136\u8f6c\u5316\u7387\u9ad8\uff0c\u4f46\u5ba2\u5355\u4ef7\u4e0d\u5982\u672a\u77e5\u5730\u533a\uff0c\u800c\u4e2d\u56fd\u5e02\u573a\u5c1a\u672a\u4ea7\u751f\u9500\u552e\u3002","suggestion":"\u9488\u5bf9\u672a\u77e5\u6765\u6e90\u548c\u5730\u533a\u7684\u9ad8\u5360\u6bd4\uff0c\u5efa\u8bae\u52a0\u5f3a\u6570\u636e\u6536\u96c6\u548c\u5206\u6790\u5de5\u4f5c\uff0c\u4ee5\u4fbf\u66f4\u597d\u5730\u7406\u89e3\u548c\u670d\u52a1\u8fd9\u90e8\u5206\u5ba2\u6237\u3002\u9274\u4e8e\u963f\u91cc\u5df4\u5df4B2B\u5e73\u53f0\u7684\u91cd\u8981\u6027\uff0c\u5e94\u52a0\u5927\u5728\u8be5\u5e73\u53f0\u7684\u8425\u9500\u529b\u5ea6\uff0c\u5e76\u63a2\u7d22\u63d0\u9ad8\u5ba2\u5355\u4ef7\u7684\u7b56\u7565\u3002\u5bf9\u4e8e\u4e2d\u56fd\u5e02\u573a\uff0c\u9700\u8981\u8c03\u67e5\u4e3a\u4f55\u8f6c\u5316\u7387\u548c\u5ba2\u5355\u4ef7\u4e3a\u96f6\u7684\u539f\u56e0\uff0c\u5e76\u5236\u5b9a\u76f8\u5e94\u7684\u5e02\u573a\u8fdb\u5165\u6216\u6539\u8fdb\u7b56\u7565\u3002","key":"customer_inventory","analysis_record_id":3515218188,"richness":1,"list":[{"richness":1,"refer_key":"customer_inventory","key":"customer_portrait","title":"\u5ba2\u6237\u753b\u50cf","list":[{"title":"\u6765\u6e90\u6e20\u9053\u5ba2\u6237\u6570\u91cf\u5206\u5e03","conclusion":"\u5927\u591a\u6570\u5ba2\u6237\u6765\u6e90\u672a\u77e5\uff0c\u5360\u6bd4\u9ad8\u8fbe69.23%\uff0c\u5176\u6b21\u662f\u963f\u91cc\u5df4\u5df4B2B\u5e73\u53f0\uff0c\u5360\u6bd417.95%\u3002","sub_key":101,"data":{"title":{"text":"\u6570\u636e\u8be6\u60c5"},"config":{"XAxis":[{"name":"\u5ba2\u6237\u6765\u6e90(\u5ba2\u6237)","field":"company.origin_list"}],"YAxis":[{"name":"\u5ba2\u6237\u6570","field":"row-company.company_id"},{"name":"\u8bb0\u5f55\u8ba1\u6570\u767e\u5206\u6bd4(\u5ba2\u6237)","field":"rowPercent-company.company_id","unit":"percent"}],"charType":[{"label":"\u8868\u683c","value":"table"}],"chatFlag":true},"content":[{"company.origin_list":"\u672a\u77e5","row-company.company_id":27,"rowPercent-company.company_id":69.23},{"company.origin_list":"\u963f\u91cc\u5df4\u5df4\uff08B2B\u5e73\u53f0\uff09","row-company.company_id":7,"rowPercent-company.company_id":17.95},{"company.origin_list":"\u73af\u7403\u8d44\u6e90\uff08B2B\u5e73\u53f0\uff09","row-company.company_id":2,"rowPercent-company.company_id":5.13},{"company.origin_list":"\u793e\u4ea4\u5e73\u53f0","row-company.company_id":2,"rowPercent-company.company_id":5.13},{"company.origin_list":"\u5c0f\u6ee1\u53d1\u73b0","row-company.company_id":2,"rowPercent-company.company_id":5.13},{"company.origin_list":"\u73af\u7403\u5e02\u573a\uff08B2B\u5e73\u53f0\uff09","row-company.company_id":2,"rowPercent-company.company_id":5.13},{"company.origin_list":"\u5b98\u7f51\u8be2\u76d8","row-company.company_id":1,"rowPercent-company.company_id":2.56},{"company.origin_list":"\u6f5c\u5ba2\u8fd0\u8425","row-company.company_id":1,"rowPercent-company.company_id":2.56},{"company.origin_list":"ERP\u540c\u6b65","row-company.company_id":1,"rowPercent-company.company_id":2.56}]},"report_detail_data":{"key":"kh4","data":[[{"key":"company.origin_list","type":"field","label":"\u672a\u77e5","value":"\u672a\u77e5","summaries":{"row-company.company_id":{"key":"row-company.company_id","value":27,"method":"row","refer_list":"companyList","report_item_unique_key":"4805f689fcd5b86482832395bb493003"},"rowPercent-company.company_id":{"key":"rowPercent-company.company_id","value":69.23,"method":"rowPercent"}}}],[{"key":"company.origin_list","type":"field","label":"\u963f\u91cc\u5df4\u5df4\uff08B2B\u5e73\u53f0\uff09","value":"\u963f\u91cc\u5df4\u5df4\uff08B2B\u5e73\u53f0\uff09","summaries":{"row-company.company_id":{"key":"row-company.company_id","value":7,"method":"row","refer_list":"companyList","report_item_unique_key":"4725802c2888a446595890730a3ca3b6"},"rowPercent-company.company_id":{"key":"rowPercent-company.company_id","value":17.95,"method":"rowPercent"}}}],[{"key":"company.origin_list","type":"field","label":"\u73af\u7403\u8d44\u6e90\uff08B2B\u5e73\u53f0\uff09","value":"\u73af\u7403\u8d44\u6e90\uff08B2B\u5e73\u53f0\uff09","summaries":{"row-company.company_id":{"key":"row-company.company_id","value":2,"method":"row","refer_list":"companyList","report_item_unique_key":"6057a5f06ffc57bdee3d3726d0296409"},"rowPercent-company.company_id":{"key":"rowPercent-company.company_id","value":5.13,"method":"rowPercent"}}}],[{"key":"company.origin_list","type":"field","label":"\u793e\u4ea4\u5e73\u53f0","value":"\u793e\u4ea4\u5e73\u53f0","summaries":{"row-company.company_id":{"key":"row-company.company_id","value":2,"method":"row","refer_list":"companyList","report_item_unique_key":"785d07a1ca5cded5ae6a8dc9679daf2f"},"rowPercent-company.company_id":{"key":"rowPercent-company.company_id","value":5.13,"method":"rowPercent"}}}],[{"key":"company.origin_list","type":"field","label":"\u5c0f\u6ee1\u53d1\u73b0","value":"\u5c0f\u6ee1\u53d1\u73b0","summaries":{"row-company.company_id":{"key":"row-company.company_id","value":2,"method":"row","refer_list":"companyList","report_item_unique_key":"e12b02219342b2c3986c0a138a31f408"},"rowPercent-company.company_id":{"key":"rowPercent-company.company_id","value":5.13,"method":"rowPercent"}}}],[{"key":"company.origin_list","type":"field","label":"\u73af\u7403\u5e02\u573a\uff08B2B\u5e73\u53f0\uff09","value":"\u73af\u7403\u5e02\u573a\uff08B2B\u5e73\u53f0\uff09","summaries":{"row-company.company_id":{"key":"row-company.company_id","value":2,"method":"row","refer_list":"companyList","report_item_unique_key":"513b9eb335d16a19f5b0bb19eb08a6aa"},"rowPercent-company.company_id":{"key":"rowPercent-company.company_id","value":5.13,"method":"rowPercent"}}}],[{"key":"company.origin_list","type":"field","label":"\u5b98\u7f51\u8be2\u76d8","value":"\u5b98\u7f51\u8be2\u76d8","summaries":{"row-company.company_id":{"key":"row-company.company_id","value":1,"method":"row","refer_list":"companyList","report_item_unique_key":"a53308d0486abcfc809d555cdbba51e4"},"rowPercent-company.company_id":{"key":"rowPercent-company.company_id","value":2.56,"method":"rowPercent"}}}],[{"key":"company.origin_list","type":"field","label":"\u6f5c\u5ba2\u8fd0\u8425","value":"\u6f5c\u5ba2\u8fd0\u8425","summaries":{"row-company.company_id":{"key":"row-company.company_id","value":1,"method":"row","refer_list":"companyList","report_item_unique_key":"a579397e8c8961f177f4a41dfff27ed4"},"rowPercent-company.company_id":{"key":"rowPercent-company.company_id","value":2.56,"method":"rowPercent"}}}],[{"key":"company.origin_list","type":"field","label":"ERP\u540c\u6b65","value":"ERP\u540c\u6b65","summaries":{"row-company.company_id":{"key":"row-company.company_id","value":1,"method":"row","refer_list":"companyList","report_item_unique_key":"e2b29a15410828fefb27df8f81c6029b"},"rowPercent-company.company_id":{"key":"rowPercent-company.company_id","value":2.56,"method":"rowPercent"}}}]],"desc":"\u67e5\u770b\u5ba2\u6237\u5f53\u524d\u5728\u5404\u4e2a\u7ef4\u5ea6\u4e0a\u7684\u5206\u5e03\u60c5\u51b5\uff0c\u638c\u63e1\u5ba2\u6237\u57fa\u672c\u5206\u5e03\u7279\u5f81  \u6570\u636e\u8303\u56f4\uff1a\u4f60\u6709\u6743\u9650\u67e5\u770b\u7684\u6240\u6709\u5ba2\u6237","name":"\u5ba2\u6237\u5206\u5e03","type":"group","chart":{"chartList":[{"group":["company.origin_list"],"option":[],"chartType":"pie","summaries":["row-company.company_id"]}]},"count":0,"title":"\u5ba2\u6237\u5206\u5e03","total":{"row-company.company_id":{"key":"row-company.company_id","value":39,"method":"row"},"rowPercent-company.company_id":{"key":"rowPercent-company.company_id","value":"-","method":"rowPercent"}},"config":{"field":[{"key":"company.origin_list","tip":"","type":"field","label":"\u5ba2\u6237\u6765\u6e90(\u5ba2\u6237)","field_type":"int"}],"group":[{"key":"company.origin_list","type":"row","extra":[],"label":"company.origin_list","order":"","field_type":""}],"order":[{"key":"company.origin_list","field":"row","order":"desc"}],"query":[{"type":"select_visible_user_id","field":"common.visible","label":"","value":[],"comment":"\u67e5\u770b\u8303\u56f4","multiple":1,"field_type":7},{"type":"date","field":"company.archive_time","label":"","value":{"end":"2024-05-07","start":"2023-11-09"},"period":"m","comment":"\u5efa\u6863\u65f6\u95f4","default":6,"multiple":0,"continuous":1,"field_type":4},{"type":3,"field":"company.owner_type","label":"","value":"","comment":"\u516c\u6d77\/\u79c1\u6d77","options":[],"multiple":0,"field_type":3},{"type":"select_trail_status","field":"company.trail_status","label":"","value":[],"comment":"\u5ba2\u6237\u9636\u6bb5","options":[],"multiple":1,"field_type":7},{"type":"select_biz_type","field":"company.biz_type","label":"","value":[],"comment":"\u5ba2\u6237\u7c7b\u578b","options":[],"multiple":1,"field_type":7},{"type":"select_country","field":"company.country","label":"","value":[],"comment":"\u56fd\u5bb6\u5730\u533a","options":[],"multiple":1,"field_type":7},{"type":"select_origin_list","field":"company.origin_list","label":"","value":[],"comment":"\u5ba2\u6237\u6765\u6e90","options":[],"multiple":1,"field_type":7},{"type":"select_star","field":"company.star","label":"","value":[],"comment":"\u5ba2\u6237\u661f\u7ea7","options":[],"multiple":1,"field_type":7},{"type":"select_company_group","field":"company.group_id","label":"","value":[],"comment":"\u5ba2\u6237\u5206\u7ec4","options":[],"multiple":1,"field_type":7},{"type":"select","field":"company.select_origin_scope","label":"","value":1,"comment":"\u6765\u6e90\u7c92\u5ea6","options":[{"label":"\u4e00\u7ea7","value":1},{"label":"\u4e8c\u7ea7","value":2}],"multiple":0,"field_type":3}],"total":{"total":{"switch":true},"detail":{"switch":true,"default":false},"subtotal":{"switch":true},"hide_zero":{"switch":true,"default":true}},"detail":[],"summaries":[{"key":"row-company.company_id","tip":"","type":"row","label":"\u5ba2\u6237\u6570","order":"","field_type":"","refer_list":"companyList","value_type":""},{"key":"rowPercent-company.company_id","tip":"","type":"rowPercent","label":"\u8bb0\u5f55\u8ba1\u6570\u767e\u5206\u6bd4(\u5ba2\u6237)","order":"","field_type":"","refer_list":"","value_type":""}]},"language":"zh-CN","relevance":[{"key":"kh1","name":"\u9636\u6bb5"},{"key":"kh2","name":"\u7c7b\u578b"},{"key":"kh3","name":"\u56fd\u5bb6\u5730\u533a"},{"key":"kh4","name":"\u6765\u6e90"},{"key":"kh5","name":"\u661f\u7ea7"},{"key":"kh6","name":"\u5206\u7ec4"},{"key":"kh8","name":"\u6d3b\u8dc3\u5ea6"},{"key":"kh13","name":"\u6765\u6e90\u5e97\u94fa"}],"permission":["crm.company.private.view"],"unique_key":"18b1321a6ae73b70b849b788ac26a6be","update_time":"2024-05-07 17:41:29","subscribe_flag":false,"client_currency":"","report_object_name":"","can_setting_filter_field":true},"richness":1,"report":{"params":[{"type":"date","field":"company.archive_time","value":{"end":"2024-05-07","start":"2023-11-09"},"field_name":"\u5efa\u6863\u65f6\u95f4"},{"type":"select","field":"company.select_origin_scope","value":1},{"type":3,"field":"company.owner_type","value":""}],"report_key":"kh4","name":"\u5ba2\u6237\u5206\u5e03"},"data_type":1,"analysis_record_id":3515216102},{"title":"\u56fd\u5bb6\u5730\u533a\u5ba2\u6237\u6570\u91cf\u5206\u5e03","conclusion":"\u7edd\u5927\u90e8\u5206\u5ba2\u6237\u7684\u56fd\u5bb6\u5730\u533a\u4fe1\u606f\u672a\u77e5\uff0c\u5360\u6bd492.31%\uff0c\u4ec5\u6709\u5c11\u6570\u5ba2\u6237\u6765\u81ea\u4f2f\u5229\u5179\u548c\u4e2d\u56fd\u3002","sub_key":102,"data":{"title":{"text":"\u6570\u636e\u8be6\u60c5"},"config":{"XAxis":[{"name":"\u56fd\u5bb6\u5730\u533a(\u5ba2\u6237)","field":"company.country"}],"YAxis":[{"name":"\u5ba2\u6237\u6570","field":"row-company.company_id"},{"name":"\u8bb0\u5f55\u8ba1\u6570\u767e\u5206\u6bd4(\u5ba2\u6237)","field":"rowPercent-company.company_id","unit":"percent"}],"charType":[{"label":"\u8868\u683c","value":"table"}],"chatFlag":true},"content":[{"company.country":"\u672a\u77e5","row-company.company_id":36,"rowPercent-company.company_id":92.31},{"company.country":"\u4f2f\u5229\u5179","row-company.company_id":2,"rowPercent-company.company_id":5.13},{"company.country":"\u4e2d\u56fd","row-company.company_id":1,"rowPercent-company.company_id":2.56}]},"report_detail_data":{"key":"kh3","data":[[{"key":"company.country","type":"field","label":"\u672a\u77e5","value":"\u672a\u77e5","summaries":{"row-company.company_id":{"key":"row-company.company_id","value":36,"method":"row","refer_list":"companyList","report_item_unique_key":"f3cdfc46f7d36899fdaeb08d8fc19a13"},"rowPercent-company.company_id":{"key":"rowPercent-company.company_id","value":92.31,"method":"rowPercent"}}}],[{"key":"company.country","type":"field","label":"\u4f2f\u5229\u5179","value":"\u4f2f\u5229\u5179","summaries":{"row-company.company_id":{"key":"row-company.company_id","value":2,"method":"row","refer_list":"companyList","report_item_unique_key":"cdc86c1c1bdf924b79f39309f6d1cc15"},"rowPercent-company.company_id":{"key":"rowPercent-company.company_id","value":5.13,"method":"rowPercent"}}}],[{"key":"company.country","type":"field","label":"\u4e2d\u56fd","value":"\u4e2d\u56fd","summaries":{"row-company.company_id":{"key":"row-company.company_id","value":1,"method":"row","refer_list":"companyList","report_item_unique_key":"c8f2006e8227857c539dfd17a372cda7"},"rowPercent-company.company_id":{"key":"rowPercent-company.company_id","value":2.56,"method":"rowPercent"}}}]],"desc":"\u67e5\u770b\u5ba2\u6237\u5f53\u524d\u5728\u5404\u4e2a\u7ef4\u5ea6\u4e0a\u7684\u5206\u5e03\u60c5\u51b5\uff0c\u638c\u63e1\u5ba2\u6237\u57fa\u672c\u5206\u5e03\u7279\u5f81  \u6570\u636e\u8303\u56f4\uff1a\u4f60\u6709\u6743\u9650\u67e5\u770b\u7684\u6240\u6709\u5ba2\u6237","name":"\u5ba2\u6237\u5206\u5e03","type":"group","chart":{"chartList":[{"group":["company.country"],"option":[],"chartType":"pie","summaries":["row-company.company_id"]}]},"count":0,"title":"\u5ba2\u6237\u5206\u5e03","total":{"row-company.company_id":{"key":"row-company.company_id","value":39,"method":"row"},"rowPercent-company.company_id":{"key":"rowPercent-company.company_id","value":"-","method":"rowPercent"}},"config":{"field":[{"key":"company.country","tip":"","type":"field","label":"\u56fd\u5bb6\u5730\u533a(\u5ba2\u6237)","field_type":"string"}],"group":[{"key":"company.country","type":"row","extra":[],"label":"company.country","order":"","field_type":""}],"order":[{"key":"company.country","field":"row","order":"desc"}],"query":[{"type":"select_visible_user_id","field":"common.visible","label":"","value":[],"comment":"\u67e5\u770b\u8303\u56f4","multiple":1,"field_type":7},{"type":"date","field":"company.archive_time","label":"","value":{"end":"2024-05-07","start":"2023-11-09"},"period":"m","comment":"\u5efa\u6863\u65f6\u95f4","default":6,"multiple":0,"continuous":1,"field_type":4},{"type":3,"field":"company.owner_type","label":"","value":"","comment":"\u516c\u6d77\/\u79c1\u6d77","options":[],"multiple":0,"field_type":3},{"type":"select_trail_status","field":"company.trail_status","label":"","value":[],"comment":"\u5ba2\u6237\u9636\u6bb5","options":[],"multiple":1,"field_type":7},{"type":"select_biz_type","field":"company.biz_type","label":"","value":[],"comment":"\u5ba2\u6237\u7c7b\u578b","options":[],"multiple":1,"field_type":7},{"type":"select_country","field":"company.country","label":"","value":[],"comment":"\u56fd\u5bb6\u5730\u533a","options":[],"multiple":1,"field_type":7},{"type":"select_origin_list","field":"company.origin_list","label":"","value":[],"comment":"\u5ba2\u6237\u6765\u6e90","options":[],"multiple":1,"field_type":7},{"type":"select_star","field":"company.star","label":"","value":[],"comment":"\u5ba2\u6237\u661f\u7ea7","options":[],"multiple":1,"field_type":7},{"type":"select_company_group","field":"company.group_id","label":"","value":[],"comment":"\u5ba2\u6237\u5206\u7ec4","options":[],"multiple":1,"field_type":7},{"type":"select","field":"common.select_regional_scope","label":"","value":3,"comment":"\u533a\u57df\u7c92\u5ea6","options":[{"label":"\u6309\u6d32\uff08\u4e00\u7ea7\uff09","value":1},{"label":"\u6309\u533a\u57df\uff08\u4e8c\u7ea7\uff09","value":2},{"label":"\u6309\u56fd\u5bb6\uff08\u4e09\u7ea7\uff09","value":3}],"multiple":0,"field_type":3}],"total":{"total":{"switch":true},"detail":{"switch":true,"default":false},"subtotal":{"switch":true},"hide_zero":{"switch":true,"default":true}},"detail":[],"summaries":[{"key":"row-company.company_id","tip":"","type":"row","label":"\u5ba2\u6237\u6570","order":"","field_type":"","refer_list":"companyList","value_type":""},{"key":"rowPercent-company.company_id","tip":"","type":"rowPercent","label":"\u8bb0\u5f55\u8ba1\u6570\u767e\u5206\u6bd4(\u5ba2\u6237)","order":"","field_type":"","refer_list":"","value_type":""}]},"language":"zh-CN","relevance":[{"key":"kh1","name":"\u9636\u6bb5"},{"key":"kh2","name":"\u7c7b\u578b"},{"key":"kh3","name":"\u56fd\u5bb6\u5730\u533a"},{"key":"kh4","name":"\u6765\u6e90"},{"key":"kh5","name":"\u661f\u7ea7"},{"key":"kh6","name":"\u5206\u7ec4"},{"key":"kh8","name":"\u6d3b\u8dc3\u5ea6"},{"key":"kh13","name":"\u6765\u6e90\u5e97\u94fa"}],"permission":["crm.company.private.view"],"unique_key":"52b078b60bc940faeb91dc9ce96f6d52","update_time":"2024-05-07 17:41:29","subscribe_flag":false,"client_currency":"","report_object_name":"","can_setting_filter_field":true},"richness":1,"report":{"params":[{"type":"date","field":"company.archive_time","value":{"end":"2024-05-07","start":"2023-11-09"},"field_name":"\u5efa\u6863\u65f6\u95f4"},{"type":"select","field":"common.select_regional_scope","value":3},{"type":3,"field":"company.owner_type","value":""}],"report_key":"kh3","name":"\u5ba2\u6237\u5206\u5e03"},"data_type":1,"analysis_record_id":3515216103}]},{"richness":1,"refer_key":"customer_inventory","key":"win_order_customer_portrait","title":"\u6210\u5355\u5ba2\u6237\u753b\u50cf","list":[{"title":"\u6210\u5355\u5ba2\u6237\u6765\u6e90\u6e20\u9053\u5206\u5e03","conclusion":"\u6570\u636e\u663e\u793a\uff0c\u6240\u6709\u7684\u5ba2\u6237\u90fd\u6765\u81ea\u4e8e\u963f\u91cc\u5df4\u5df4\uff08B2B\u5e73\u53f0\uff09\u3002","sub_key":103,"data":{"title":{"text":"\u6570\u636e\u8be6\u60c5"},"config":{"XAxis":[{"name":"\u5ba2\u6237\u6765\u6e90(\u5ba2\u6237)","field":"company.origin_list"}],"YAxis":[{"name":"\u5ba2\u6237\u6570","field":"row-company.company_id"}],"charType":[{"label":"\u8868\u683c","value":"table"}],"chatFlag":true},"content":[{"company.origin_list":"\u963f\u91cc\u5df4\u5df4\uff08B2B\u5e73\u53f0\uff09","row-company.company_id":1}]},"report_detail_data":{"key":"khdd3","data":[[{"key":"company.origin_list","type":"field","label":"\u963f\u91cc\u5df4\u5df4\uff08B2B\u5e73\u53f0\uff09","value":"\u963f\u91cc\u5df4\u5df4\uff08B2B\u5e73\u53f0\uff09","summaries":{"row-company.company_id":{"key":"row-company.company_id","value":1,"method":"row","refer_list":"companyList","report_item_unique_key":"880ceee5b2583b39ba593341d9b5d8ab"}}}]],"desc":"\u7edf\u8ba1\u5ba2\u6237\u8ba2\u5355\u7684\u91d1\u989d","name":"\u5ba2\u6237\u8ba2\u5355\u91d1\u989d\u60c5\u51b5","type":"group","chart":{"chartList":[{"group":["company.origin_list"],"option":[],"chartType":"pie","summaries":["row-company.company_id"]}]},"count":0,"title":"\u5ba2\u6237\u8ba2\u5355\u91d1\u989d\u60c5\u51b5","total":{"sum-order.amount":{"key":"sum-order.amount","value":13.77,"method":"sum"},"row-order.order_id":{"key":"row-order.order_id","value":1,"method":"row"},"row-company.company_id":{"key":"row-company.company_id","value":1,"method":"row"},"sumPercent-order.amount":{"key":"sumPercent-order.amount","value":"-","method":"sumPercent"},"sum-cash_collection.amount":{"key":"sum-cash_collection.amount","value":0,"method":"sum"},"sum-cash_collection.collect_amount":{"key":"sum-cash_collection.collect_amount","value":0,"method":"sum"},"row-cash_collection.cash_collection_id":{"key":"row-cash_collection.cash_collection_id","value":0,"method":"row"},"row-cash_collection.cash_collection_count":{"key":"row-cash_collection.cash_collection_count","value":0,"method":"row"}},"config":{"field":[{"key":"company.origin_list","tip":"","type":"field","label":"\u5ba2\u6237\u6765\u6e90(\u5ba2\u6237)","field_type":"7"}],"group":[{"key":"company.origin_list","type":"row","extra":[],"label":"company.origin_list","order":"","field_type":""}],"order":[{"key":"company.origin_list","field":"sum-order.amount","order":"desc"}],"query":[{"type":"select_visible_user_id","field":"common.visible","label":"","value":[],"comment":"\u67e5\u770b\u8303\u56f4","multiple":1,"field_type":7},{"type":"date","field":"order.account_date","label":"","value":{"end":"2024-05-07","start":"2023-11-09"},"period":"y","comment":"\u8ba2\u5355\u65e5\u671f","default":1,"multiple":0,"continuous":1,"field_type":4},{"type":3,"field":"order.status","label":"","value":["********","********","********","********","********","********"],"comment":"\u8ba2\u5355\u72b6\u6001(\u9500\u552e\u8ba2\u5355)","options":[],"multiple":1,"continuous":1,"field_type":7},{"type":3,"field":"order.currency","label":"","value":"","comment":"\u5e01\u79cd(\u9500\u552e\u8ba2\u5355)","options":[],"continuous":1,"field_type":3},{"type":"date","field":"order.create_time","label":"","value":{"end":null,"start":null},"comment":"\u521b\u5efa\u65e5\u671f(\u9500\u552e\u8ba2\u5355)","continuous":1,"field_type":4},{"type":3,"field":"company.owner_type","label":"","value":"","comment":"\u516c\u6d77\/\u79c1\u6d77(\u5ba2\u6237)","options":[],"continuous":1,"field_type":3},{"type":3,"field":"company.country","label":"","value":"","comment":"\u56fd\u5bb6\u5730\u533a(\u5ba2\u6237)","options":[],"continuous":1,"field_type":3},{"type":3,"field":"company.origin_list","label":"","value":"","comment":"\u5ba2\u6237\u6765\u6e90(\u5ba2\u6237)","options":[],"continuous":1,"field_type":3},{"type":3,"field":"company.star","label":"","value":"","comment":"\u5ba2\u6237\u661f\u7ea7(\u5ba2\u6237)","options":[],"continuous":1,"field_type":7},{"type":3,"field":"company.group_id","label":"","value":"","comment":"\u5ba2\u6237\u5206\u7ec4(\u5ba2\u6237)","options":[],"continuous":1,"field_type":3},{"type":3,"field":"company.trail_status","label":"","value":"","comment":"\u5ba2\u6237\u9636\u6bb5(\u5ba2\u6237)","options":[],"continuous":1,"field_type":3},{"type":1,"field":"company.serial_id","label":"","value":"","comment":"\u5ba2\u6237\u7f16\u53f7(\u5ba2\u6237)","continuous":1,"field_type":1},{"type":1,"field":"company.name","label":"","value":"","comment":"\u516c\u53f8\u540d\u79f0(\u5ba2\u6237)","continuous":1,"field_type":1},{"type":3,"field":"company.biz_type","label":"","value":"","comment":"\u5ba2\u6237\u7c7b\u578b(\u5ba2\u6237)","options":[],"continuous":1,"field_type":3},{"type":3,"field":"company.scale_id","label":"","value":"","comment":"\u89c4\u6a21(\u5ba2\u6237)","options":[],"continuous":1,"field_type":3},{"type":"date","field":"company.archive_time","label":"","value":{"end":null,"start":null},"comment":"\u521b\u5efa\u65f6\u95f4(\u5ba2\u6237)","continuous":1,"field_type":4},{"type":"date","field":"company.order_time","label":"","value":{"end":null,"start":null},"comment":"\u6700\u8fd1\u8054\u7cfb\u65f6\u95f4(\u5ba2\u6237)","continuous":1,"field_type":4},{"type":"select","field":"company.select_origin_scope","label":"","value":1,"comment":"\u6765\u6e90\u7c92\u5ea6","options":[{"label":"\u4e00\u7ea7","value":1},{"label":"\u4e8c\u7ea7","value":2}],"multiple":0,"field_type":3}],"total":{"total":{"switch":true},"detail":{"switch":true,"default":false},"subtotal":{"switch":true},"hide_zero":{"switch":true,"default":true}},"detail":[],"summaries":[{"key":"row-company.company_id","tip":"","type":"row","label":"\u5ba2\u6237\u6570","order":"","field_type":"","refer_list":"companyList","value_type":""}]},"language":"zh-CN","relevance":[{"key":"kh10","name":"\u5ba2\u6237"},{"key":"khdd2","name":"\u5ba2\u6237\u7c7b\u578b"},{"key":"khdd3","name":"\u5ba2\u6237\u6765\u6e90"},{"key":"khdd4","name":"\u5ba2\u6237\u661f\u7ea7"},{"key":"khdd5","name":"\u5ba2\u6237\u5206\u7ec4"},{"key":"khdd6","name":"\u56fd\u5bb6\u5730\u533a"},{"key":"khdd7","name":"\u6765\u6e90\u5e97\u94fa"}],"permission":["crm.company.private.view"],"unique_key":"a71a8259155e6624f79442bf4b1ac2d8","update_time":"2024-05-07 17:41:43","subscribe_flag":false,"client_currency":"","report_object_name":"","can_setting_filter_field":true},"richness":1,"report":{"params":[{"type":"date","field":"order.account_date","value":{"end":"2024-05-07","start":"2023-11-09"}},{"type":3,"field":"order.status","value":["********","********","********","********","********","********"]}],"report_key":"khdd3","name":"\u5ba2\u6237\u8ba2\u5355\u91d1\u989d\u60c5\u51b5"},"data_type":1,"analysis_record_id":**********},{"title":"\u6765\u6e90\u6e20\u9053\u7684\u5ba2\u6237\u5355\u4ef7\u4e0e\u8f6c\u5316\u7387\u5206\u5e03","conclusion":"ERP\u540c\u6b65\u3001\u5c0f\u6ee1\u53d1\u73b0\u548c\u73af\u7403\u5e02\u573a\uff08B2B\u5e73\u53f0\uff09\u7684\u8f6c\u5316\u7387\u8fbe\u5230\u4e86100%\uff0c\u5176\u4e2dERP\u540c\u6b65\u7684\u5ba2\u5355\u4ef7\u6700\u9ad8\uff0c\u4e3a24884.28\u5143\u3002\u963f\u91cc\u5df4\u5df4\uff08B2B\u5e73\u53f0\uff09\u7684\u8f6c\u5316\u7387\u4e3a42.86%\uff0c\u5ba2\u5355\u4ef7\u4e3a3068.63\u5143\uff0c\u76f8\u6bd4\u5176\u4ed6\u5e73\u53f0\u8f83\u4f4e\u3002","sub_key":104,"data":{"title":{"text":"\u6570\u636e\u8be6\u60c5"},"config":{"XAxis":[{"name":"\u5ba2\u6237\u6765\u6e90","field":"company.origin_list"}],"YAxis":[{"name":"\u8f6c\u5316\u7387","field":"formula-transform","unit":"percent"},{"name":"\u5ba2\u5355\u4ef7","field":"formula-avg_company_amount"}],"charType":[{"label":"\u8868\u683c","value":"table"}],"chatFlag":true},"content":[{"formula-transform":100,"company.origin_list":"ERP\u540c\u6b65","formula-avg_company_amount":24884.28},{"formula-transform":100,"company.origin_list":"\u5c0f\u6ee1\u53d1\u73b0","formula-avg_company_amount":13005.72},{"formula-transform":100,"company.origin_list":"\u73af\u7403\u5e02\u573a\uff08B2B\u5e73\u53f0\uff09","formula-avg_company_amount":13005.72},{"formula-transform":42.86,"company.origin_list":"\u963f\u91cc\u5df4\u5df4\uff08B2B\u5e73\u53f0\uff09","formula-avg_company_amount":3068.63},{"formula-transform":50,"company.origin_list":"\u73af\u7403\u8d44\u6e90\uff08B2B\u5e73\u53f0\uff09","formula-avg_company_amount":2644.83},{"formula-transform":100,"company.origin_list":"\u6f5c\u5ba2\u8fd0\u8425","formula-avg_company_amount":1763.94},{"formula-transform":0,"company.origin_list":"\u672a\u77e5","formula-avg_company_amount":0},{"formula-transform":0,"company.origin_list":"\u793e\u4ea4\u5e73\u53f0","formula-avg_company_amount":0},{"formula-transform":0,"company.origin_list":"\u5b98\u7f51\u8be2\u76d8","formula-avg_company_amount":0}]},"report_detail_data":{"key":"kh17","data":[[{"key":"company.origin_list","type":"field","label":"ERP\u540c\u6b65","value":"ERP\u540c\u6b65","summaries":{"formula-transform":{"key":"transform","value":100},"formula-avg_company_amount":{"key":"avg_company_amount","value":24884.28}}}],[{"key":"company.origin_list","type":"field","label":"\u5c0f\u6ee1\u53d1\u73b0","value":"\u5c0f\u6ee1\u53d1\u73b0","summaries":{"formula-transform":{"key":"transform","value":100},"formula-avg_company_amount":{"key":"avg_company_amount","value":13005.72}}}],[{"key":"company.origin_list","type":"field","label":"\u73af\u7403\u5e02\u573a\uff08B2B\u5e73\u53f0\uff09","value":"\u73af\u7403\u5e02\u573a\uff08B2B\u5e73\u53f0\uff09","summaries":{"formula-transform":{"key":"transform","value":100},"formula-avg_company_amount":{"key":"avg_company_amount","value":13005.72}}}],[{"key":"company.origin_list","type":"field","label":"\u963f\u91cc\u5df4\u5df4\uff08B2B\u5e73\u53f0\uff09","value":"\u963f\u91cc\u5df4\u5df4\uff08B2B\u5e73\u53f0\uff09","summaries":{"formula-transform":{"key":"transform","value":42.86},"formula-avg_company_amount":{"key":"avg_company_amount","value":3068.63}}}],[{"key":"company.origin_list","type":"field","label":"\u73af\u7403\u8d44\u6e90\uff08B2B\u5e73\u53f0\uff09","value":"\u73af\u7403\u8d44\u6e90\uff08B2B\u5e73\u53f0\uff09","summaries":{"formula-transform":{"key":"transform","value":50},"formula-avg_company_amount":{"key":"avg_company_amount","value":2644.83}}}],[{"key":"company.origin_list","type":"field","label":"\u6f5c\u5ba2\u8fd0\u8425","value":"\u6f5c\u5ba2\u8fd0\u8425","summaries":{"formula-transform":{"key":"transform","value":100},"formula-avg_company_amount":{"key":"avg_company_amount","value":1763.94}}}],[{"key":"company.origin_list","type":"field","label":"\u672a\u77e5","value":"\u672a\u77e5","summaries":{"formula-transform":{"key":"transform","value":0},"formula-avg_company_amount":{"key":"avg_company_amount","value":0}}}],[{"key":"company.origin_list","type":"field","label":"\u793e\u4ea4\u5e73\u53f0","value":"\u793e\u4ea4\u5e73\u53f0","summaries":{"formula-transform":{"key":"transform","value":0},"formula-avg_company_amount":{"key":"avg_company_amount","value":0}}}],[{"key":"company.origin_list","type":"field","label":"\u5b98\u7f51\u8be2\u76d8","value":"\u5b98\u7f51\u8be2\u76d8","summaries":{"formula-transform":{"key":"transform","value":0},"formula-avg_company_amount":{"key":"avg_company_amount","value":0}}}]],"desc":"\u4e86\u89e3\u65b0\u5ba2\u6237\u6210\u4ea4\u8f6c\u5316\u7387\u3001\u5ba2\u5355\u4ef7","name":"\u65b0\u5ba2\u6237\u6210\u4ea4\u8f6c\u5316\u60c5\u51b5","type":"group","chart":{"chartList":[{"group":["company.origin_list"],"option":[],"chartType":"horizontal-bar","summaries":["formula-avg_company_amount"]}]},"count":0,"title":"\u65b0\u5ba2\u6237\u6210\u4ea4\u8f6c\u5316\u60c5\u51b5","total":{"formula-transform":{"key":"transform","value":12.82},"row-company.company_id":{"key":"row-company.company_id","value":39,"method":"row"},"formula-avg_company_amount":{"key":"avg_company_amount","value":18104.36},"row-performance.company_id":{"key":"row-performance.company_id","value":5,"method":"row"},"sum-performance.indicator_value":{"key":"sum-performance.indicator_value","value":90521.81,"method":"sum"}},"config":{"field":[{"key":"company.origin_list","tip":"","type":"field","label":"\u5ba2\u6237\u6765\u6e90","field_type":7}],"group":[{"key":"company.origin_list","type":"row","extra":[],"label":"company.origin_list","order":"","field_type":"select"}],"order":[{"key":"company.origin_list","field":null,"order":"desc"}],"query":[{"type":"select_visible_user_id","field":"common.visible","label":"","value":[],"comment":"\u67e5\u770b\u8303\u56f4","multiple":1,"field_type":7},{"type":"date","field":"company.archive_time","label":"","value":{"end":"2024-05-07","start":"2023-11-09"},"period":"m","comment":"\u5efa\u6863\u65f6\u95f4","default":1,"multiple":0,"continuous":1,"field_type":4},{"type":7,"field":"company.ali_store_id","label":"","value":"","comment":"\u6765\u6e90\u5e97\u94fa","field_type":7},{"type":"select_trail_status","field":"company.trail_status","label":"","value":[],"comment":"\u5ba2\u6237\u9636\u6bb5","options":[],"multiple":1,"field_type":7},{"type":"select_biz_type","field":"company.biz_type","label":"","value":"","comment":"\u5ba2\u6237\u7c7b\u578b","options":[],"multiple":0,"field_type":3},{"type":"select_country","field":"company.country","label":"","value":[],"comment":"\u56fd\u5bb6\u5730\u533a","options":[],"multiple":1,"field_type":7},{"type":"select_origin_list","field":"company.origin_list","label":"","value":[],"comment":"\u5ba2\u6237\u6765\u6e90","options":[],"multiple":1,"field_type":7},{"type":"select_star","field":"company.star","label":"","value":[],"comment":"\u5ba2\u6237\u661f\u7ea7","options":[],"multiple":1,"field_type":7},{"type":"select_company_group","field":"company.group_id","label":"","value":[],"comment":"\u5ba2\u6237\u5206\u7ec4","options":[],"multiple":1,"field_type":7},{"type":"select","field":"company.select_origin_scope","label":"","value":1,"comment":"\u6765\u6e90\u7c92\u5ea6","options":[{"label":"\u4e00\u7ea7","value":1},{"label":"\u4e8c\u7ea7","value":2}],"multiple":0,"field_type":3}],"total":{"total":{"switch":true},"detail":{"switch":true,"default":false},"subtotal":{"switch":true},"hide_zero":{"switch":true,"default":true}},"detail":[],"summaries":[{"key":"formula-transform","tip":"\u6210\u4ea4\u8ba2\u5355\u5ba2\u6237\u6570\/\u65b0\u5efa\u5ba2\u6237\u6570","type":"percent","label":"\u8f6c\u5316\u7387","order":"","field_type":"percent","refer_list":"","value_type":""},{"key":"formula-avg_company_amount","tip":"\u6210\u4ea4\u8ba2\u5355\u91d1\u989d\/\u6210\u4ea4\u8ba2\u5355\u5ba2\u6237\u6570","type":"formula","label":"\u5ba2\u5355\u4ef7","order":"","field_type":"float","refer_list":"","value_type":""}]},"language":"zh-CN","relevance":[{"key":"kh14","name":"\u65f6\u95f4"},{"key":"kh15","name":"\u8ddf\u8fdb\u4eba"},{"key":"kh16","name":"\u56fd\u5bb6\u5730\u533a"},{"key":"kh17","name":"\u5ba2\u6237\u6765\u6e90"},{"key":"kh18","name":"\u6765\u6e90\u5e97\u94fa"}],"permission":["crm.company.private.view"],"unique_key":"5da68985e0218ff7bd3ed06839ecb793","update_time":"2024-05-07 17:41:43","subscribe_flag":false,"client_currency":"","report_object_name":"","can_setting_filter_field":true},"richness":1,"report":{"params":[{"type":"date","field":"company.archive_time","value":{"end":"2024-05-07","start":"2023-11-09"}},{"type":"select","field":"company.select_origin_scope","value":1}],"report_key":"kh17","name":"\u65b0\u5ba2\u6237\u6210\u4ea4\u8f6c\u5316\u60c5\u51b5"},"data_type":1,"analysis_record_id":3515216122},{"title":"\u6210\u5355\u5ba2\u6237\u56fd\u5bb6\u5730\u533a\u5206\u5e03","conclusion":"\u6570\u636e\u663e\u793a\uff0c\u4e2d\u56fd\u7684\u5ba2\u6237\u6570\u4e3a1\u3002","sub_key":105,"data":{"title":{"text":"\u6570\u636e\u8be6\u60c5"},"config":{"XAxis":[{"name":"\u56fd\u5bb6\u5730\u533a(\u5ba2\u6237)","field":"company.country"}],"YAxis":[{"name":"\u5ba2\u6237\u6570","field":"row-company.company_id"}],"charType":[{"label":"\u8868\u683c","value":"table"}],"chatFlag":true},"content":[{"company.country":"\u4e2d\u56fd","row-company.company_id":1}]},"report_detail_data":{"key":"khdd6","data":[[{"key":"company.country","type":"field","label":"\u4e2d\u56fd","value":"\u4e2d\u56fd","summaries":{"row-company.company_id":{"key":"row-company.company_id","value":1,"method":"row","refer_list":"companyList","report_item_unique_key":"ab7091a6b8d208cc89e280f200d41b43"}}}]],"desc":"\u7edf\u8ba1\u5ba2\u6237\u8ba2\u5355\u7684\u91d1\u989d","name":"\u5ba2\u6237\u8ba2\u5355\u91d1\u989d\u60c5\u51b5","type":"group","chart":{"chartList":[{"group":["company.country"],"option":[],"chartType":"pie","summaries":["row-company.company_id"]}]},"count":0,"title":"\u5ba2\u6237\u8ba2\u5355\u91d1\u989d\u60c5\u51b5","total":{"sum-order.amount":{"key":"sum-order.amount","value":13.77,"method":"sum"},"row-order.order_id":{"key":"row-order.order_id","value":1,"method":"row"},"row-company.company_id":{"key":"row-company.company_id","value":1,"method":"row"},"sumPercent-order.amount":{"key":"sumPercent-order.amount","value":"-","method":"sumPercent"},"sum-cash_collection.amount":{"key":"sum-cash_collection.amount","value":0,"method":"sum"},"sum-cash_collection.collect_amount":{"key":"sum-cash_collection.collect_amount","value":0,"method":"sum"},"row-cash_collection.cash_collection_id":{"key":"row-cash_collection.cash_collection_id","value":0,"method":"row"},"row-cash_collection.cash_collection_count":{"key":"row-cash_collection.cash_collection_count","value":0,"method":"row"}},"config":{"field":[{"key":"company.country","tip":"","type":"field","label":"\u56fd\u5bb6\u5730\u533a(\u5ba2\u6237)","field_type":"string"}],"group":[{"key":"company.country","type":"row","extra":[],"label":"company.country","order":"","field_type":""}],"order":[{"key":"company.country","field":"sum-order.amount","order":"desc"}],"query":[{"type":"select_visible_user_id","field":"common.visible","label":"","value":[],"comment":"\u67e5\u770b\u8303\u56f4","multiple":1,"field_type":7},{"type":"date","field":"order.account_date","label":"","value":{"end":"2024-05-07","start":"2023-11-09"},"period":"y","comment":"\u8ba2\u5355\u65e5\u671f","default":1,"multiple":0,"continuous":1,"field_type":4},{"type":3,"field":"order.status","label":"","value":["********","********","********","********","********","********"],"comment":"\u8ba2\u5355\u72b6\u6001(\u9500\u552e\u8ba2\u5355)","options":[],"multiple":1,"continuous":1,"field_type":7},{"type":3,"field":"order.currency","label":"","value":"","comment":"\u5e01\u79cd(\u9500\u552e\u8ba2\u5355)","options":[],"continuous":1,"field_type":3},{"type":"date","field":"order.create_time","label":"","value":{"end":null,"start":null},"comment":"\u521b\u5efa\u65e5\u671f(\u9500\u552e\u8ba2\u5355)","continuous":1,"field_type":4},{"type":3,"field":"company.owner_type","label":"","value":"","comment":"\u516c\u6d77\/\u79c1\u6d77(\u5ba2\u6237)","options":[],"continuous":1,"field_type":3},{"type":3,"field":"company.country","label":"","value":"","comment":"\u56fd\u5bb6\u5730\u533a(\u5ba2\u6237)","options":[],"continuous":1,"field_type":3},{"type":7,"field":"company.origin_list","label":"","value":"","comment":"\u5ba2\u6237\u6765\u6e90(\u5ba2\u6237)","options":[],"continuous":1,"field_type":7},{"type":3,"field":"company.star","label":"","value":"","comment":"\u5ba2\u6237\u661f\u7ea7(\u5ba2\u6237)","options":[],"continuous":1,"field_type":7},{"type":3,"field":"company.group_id","label":"","value":"","comment":"\u5ba2\u6237\u5206\u7ec4(\u5ba2\u6237)","options":[],"continuous":1,"field_type":3},{"type":3,"field":"company.trail_status","label":"","value":"","comment":"\u5ba2\u6237\u9636\u6bb5(\u5ba2\u6237)","options":[],"continuous":1,"field_type":3},{"type":1,"field":"company.serial_id","label":"","value":"","comment":"\u5ba2\u6237\u7f16\u53f7(\u5ba2\u6237)","continuous":1,"field_type":1},{"type":1,"field":"company.name","label":"","value":"","comment":"\u516c\u53f8\u540d\u79f0(\u5ba2\u6237)","continuous":1,"field_type":1},{"type":3,"field":"company.biz_type","label":"","value":"","comment":"\u5ba2\u6237\u7c7b\u578b(\u5ba2\u6237)","options":[],"continuous":1,"field_type":3},{"type":3,"field":"company.scale_id","label":"","value":"","comment":"\u89c4\u6a21(\u5ba2\u6237)","options":[],"continuous":1,"field_type":3},{"type":"date","field":"company.archive_time","label":"","value":{"end":null,"start":null},"comment":"\u521b\u5efa\u65f6\u95f4(\u5ba2\u6237)","continuous":1,"field_type":4},{"type":"date","field":"company.order_time","label":"","value":{"end":null,"start":null},"comment":"\u6700\u8fd1\u8054\u7cfb\u65f6\u95f4(\u5ba2\u6237)","continuous":1,"field_type":4},{"type":"select","field":"common.select_regional_scope","label":"","value":3,"comment":"\u533a\u57df\u7c92\u5ea6","options":[{"label":"\u6309\u6d32\uff08\u4e00\u7ea7\uff09","value":1},{"label":"\u6309\u533a\u57df\uff08\u4e8c\u7ea7\uff09","value":2},{"label":"\u6309\u56fd\u5bb6\uff08\u4e09\u7ea7\uff09","value":3}],"multiple":0,"field_type":3}],"total":{"total":{"switch":true},"detail":{"switch":true,"default":false},"subtotal":{"switch":true},"hide_zero":{"switch":true,"default":true}},"detail":[],"summaries":[{"key":"row-company.company_id","tip":"","type":"row","label":"\u5ba2\u6237\u6570","order":"","field_type":"","refer_list":"companyList","value_type":""}]},"language":"zh-CN","relevance":[{"key":"kh10","name":"\u5ba2\u6237"},{"key":"khdd2","name":"\u5ba2\u6237\u7c7b\u578b"},{"key":"khdd3","name":"\u5ba2\u6237\u6765\u6e90"},{"key":"khdd4","name":"\u5ba2\u6237\u661f\u7ea7"},{"key":"khdd5","name":"\u5ba2\u6237\u5206\u7ec4"},{"key":"khdd6","name":"\u56fd\u5bb6\u5730\u533a"},{"key":"khdd7","name":"\u6765\u6e90\u5e97\u94fa"}],"permission":["crm.company.private.view"],"unique_key":"5221da051f5b874e770af28769d76c22","update_time":"2024-05-07 17:42:05","subscribe_flag":false,"client_currency":"","report_object_name":"","can_setting_filter_field":true},"richness":1,"report":{"params":[{"type":"date","field":"order.account_date","value":{"end":"2024-05-07","start":"2023-11-09"}},{"type":"select","field":"common.select_regional_scope","value":3},{"type":3,"field":"order.status","value":["********","********","********","********","********","********"]}],"report_key":"khdd6","name":"\u5ba2\u6237\u8ba2\u5355\u91d1\u989d\u60c5\u51b5"},"data_type":1,"analysis_record_id":**********},{"title":"\u56fd\u5bb6\u5730\u533a\u7684\u5ba2\u6237\u5355\u4ef7\u4e0e\u8f6c\u5316\u7387\u5206\u5e03","conclusion":"\u4f2f\u5229\u5179\u7684\u8f6c\u5316\u7387\u6700\u9ad8\uff0c\u4e3a50%\uff0c\u4f46\u5ba2\u5355\u4ef7\u4f4e\u4e8e\u672a\u77e5\u5730\u533a\u3002\u672a\u77e5\u5730\u533a\u7684\u8f6c\u5316\u7387\u4e3a11.11%\uff0c\u4f46\u5ba2\u5355\u4ef7\u6700\u9ad8\uff0c\u4e3a7605.05\u5143\u3002\u4e2d\u56fd\u7684\u8f6c\u5316\u7387\u548c\u5ba2\u5355\u4ef7\u5747\u4e3a0\uff0c\u8868\u660e\u53ef\u80fd\u5b58\u5728\u6570\u636e\u6536\u96c6\u6216\u5e02\u573a\u6e17\u900f\u65b9\u9762\u7684\u95ee\u9898\u3002","sub_key":106,"data":{"title":{"text":"\u6570\u636e\u8be6\u60c5"},"config":{"XAxis":[{"name":"\u56fd\u5bb6\u5730\u533a","field":"company.country"}],"YAxis":[{"name":"\u8f6c\u5316\u7387","field":"formula-transform","unit":"percent"},{"name":"\u5ba2\u5355\u4ef7","field":"formula-avg_company_amount"}],"charType":[{"label":"\u8868\u683c","value":"table"}],"chatFlag":true},"content":[{"company.country":"\u672a\u77e5","formula-transform":11.11,"formula-avg_company_amount":7605.05},{"company.country":"\u4f2f\u5229\u5179","formula-transform":50,"formula-avg_company_amount":3179.57},{"company.country":"\u4e2d\u56fd","formula-transform":0,"formula-avg_company_amount":0}]},"report_detail_data":{"key":"kh16","data":[[{"key":"company.country","type":"field","label":"\u672a\u77e5","value":"\u672a\u77e5","summaries":{"formula-transform":{"key":"transform","value":11.11},"formula-avg_company_amount":{"key":"avg_company_amount","value":7605.05}}}],[{"key":"company.country","type":"field","label":"\u4f2f\u5229\u5179","value":"\u4f2f\u5229\u5179","summaries":{"formula-transform":{"key":"transform","value":50},"formula-avg_company_amount":{"key":"avg_company_amount","value":3179.57}}}],[{"key":"company.country","type":"field","label":"\u4e2d\u56fd","value":"\u4e2d\u56fd","summaries":{"formula-transform":{"key":"transform","value":0},"formula-avg_company_amount":{"key":"avg_company_amount","value":0}}}]],"desc":"\u4e86\u89e3\u65b0\u5ba2\u6237\u6210\u4ea4\u8f6c\u5316\u7387\u3001\u5ba2\u5355\u4ef7","name":"\u65b0\u5ba2\u6237\u6210\u4ea4\u8f6c\u5316\u60c5\u51b5","type":"group","chart":{"chartList":[{"group":["company.country"],"option":[],"chartType":"horizontal-bar","summaries":["formula-avg_company_amount"]}]},"count":0,"title":"\u65b0\u5ba2\u6237\u6210\u4ea4\u8f6c\u5316\u60c5\u51b5","total":{"formula-transform":{"key":"transform","value":12.82},"row-company.company_id":{"key":"row-company.company_id","value":39,"method":"row"},"formula-avg_company_amount":{"key":"avg_company_amount","value":6719.96},"row-performance.company_id":{"key":"row-performance.company_id","value":5,"method":"row"},"sum-performance.indicator_value":{"key":"sum-performance.indicator_value","value":33599.78,"method":"sum"}},"config":{"field":[{"key":"company.country","tip":"","type":"field","label":"\u56fd\u5bb6\u5730\u533a","field_type":7}],"group":[{"key":"company.country","type":"row","extra":[],"label":"company.country","order":"","field_type":"select"}],"order":[{"key":"company.country","field":null,"order":"desc"}],"query":[{"type":"select_visible_user_id","field":"common.visible","label":"","value":[],"comment":"\u67e5\u770b\u8303\u56f4","multiple":1,"field_type":7},{"type":"date","field":"company.archive_time","label":"","value":{"end":"2024-05-07","start":"2023-11-09"},"period":"m","comment":"\u5efa\u6863\u65f6\u95f4","default":1,"multiple":0,"continuous":1,"field_type":4},{"type":"select","field":"common.select_regional_scope","label":"","value":3,"comment":"\u533a\u57df\u7c92\u5ea6","options":[{"label":"\u6309\u6d32\uff08\u4e00\u7ea7\uff09","value":1},{"label":"\u6309\u533a\u57df\uff08\u4e8c\u7ea7\uff09","value":2},{"label":"\u6309\u56fd\u5bb6\uff08\u4e09\u7ea7\uff09","value":3}],"multiple":0,"field_type":3},{"base":1,"name":"\u6765\u6e90\u8be6\u60c5","type":7,"field":"company.ali_store_id","label":"","value":"","comment":"\u6765\u6e90\u8be6\u60c5\uff08\u5ba2\u6237\uff09","ext_info":"{}","field_type":7,"functional":["query","bucket","detail","group"],"enable_flag":1,"disable_flag":0},{"base":"1","name":"\u5ba2\u6237\u9636\u6bb5","type":"select_trail_status","field":"company.trail_status","label":"","value":"","comment":"\u5ba2\u6237\u9636\u6bb5\uff08\u5ba2\u6237\uff09","options":[],"ext_info":[],"field_type":3,"functional":["query","bucket","detail","group"],"enable_flag":"1","disable_flag":"0"},{"base":"1","name":"\u5ba2\u6237\u7c7b\u578b","type":"select_biz_type","field":"company.biz_type","label":"","value":"","comment":"\u5ba2\u6237\u7c7b\u578b\uff08\u5ba2\u6237\uff09","options":[],"ext_info":[],"field_type":3,"functional":["query","bucket","detail","group"],"enable_flag":"1","disable_flag":"0"},{"base":"1","name":"\u56fd\u5bb6\u5730\u533a","type":"select_country","field":"company.country","label":"","value":"","comment":"\u56fd\u5bb6\u5730\u533a\uff08\u5ba2\u6237\uff09","options":[],"ext_info":[],"field_type":3,"functional":["query","bucket","detail","group"],"enable_flag":"1","disable_flag":"0"},{"base":"1","name":"\u5ba2\u6237\u6765\u6e90","type":"select_origin_list","field":"company.origin_list","label":"","value":"","comment":"\u5ba2\u6237\u6765\u6e90\uff08\u5ba2\u6237\uff09","options":[],"ext_info":[],"field_type":3,"functional":["query","bucket","detail","group"],"enable_flag":"1","disable_flag":"0"},{"base":"1","name":"\u5ba2\u6237\u661f\u7ea7","type":"select_star","field":"company.star","label":"","value":"","comment":"\u5ba2\u6237\u661f\u7ea7\uff08\u5ba2\u6237\uff09","options":[],"ext_info":[],"field_type":7,"functional":["query","bucket","detail","group"],"enable_flag":"1","disable_flag":"0"},{"base":"1","name":"\u5ba2\u6237\u5206\u7ec4","type":"select_company_group","field":"company.group_id","label":"","value":"","comment":"\u5ba2\u6237\u5206\u7ec4\uff08\u5ba2\u6237\uff09","options":[],"ext_info":[],"field_type":3,"functional":["query","bucket","detail","group"],"enable_flag":"1","disable_flag":"0"},{"base":"1","name":"\u4ea7\u54c1\u603b\u91d1\u989d(USD)","type":"5","field":"order.product_total_amount_usd","label":"","value":"","comment":"\u4ea7\u54c1\u603b\u91d1\u989d(USD)\uff08\u8ba2\u5355\uff09","ext_info":[],"field_type":"5","functional":["query","summate","bucket","detail","group"],"enable_flag":"1","disable_flag":"0"},{"base":"1","name":"\u4ea7\u54c1\u603b\u91d1\u989d(CNY)","type":"5","field":"order.product_total_amount_rmb","label":"","value":"","comment":"\u4ea7\u54c1\u603b\u91d1\u989d(CNY)\uff08\u8ba2\u5355\uff09","ext_info":[],"field_type":"5","functional":["query","summate","bucket","detail","group"],"enable_flag":"1","disable_flag":"0"},{"base":"0","name":"\u65b0\u8001\u5ba2\u6237","type":"3","field":"order.3136949419","label":"","value":"","comment":"\u65b0\u8001\u5ba2\u6237\uff08\u8ba2\u5355\uff09","options":[{"label":"\u65b0\u5ba2\u6237","value":"\u65b0\u5ba2\u6237"},{"label":"\u8001\u5ba2\u6237","value":"\u8001\u5ba2\u6237"}],"ext_info":["\u65b0\u5ba2\u6237","\u8001\u5ba2\u6237"],"field_type":"3","functional":["query","bucket","detail","group"],"enable_flag":"1","disable_flag":"0"},{"base":"0","name":"\u6d4b\u8bd5666","type":"2","field":"order.3262964708","label":"","value":"","comment":"\u6d4b\u8bd5666\uff08\u8ba2\u5355\uff09","ext_info":[],"field_type":"2","functional":["query","detail","group"],"enable_flag":"1","disable_flag":"0"},{"base":"0","name":"jolie\u4e0b\u62c9\u5355\u9009","type":"3","field":"order.1107736675","label":"","value":"","comment":"jolie\u4e0b\u62c9\u5355\u9009\uff08\u8ba2\u5355\uff09","options":[{"label":"\u4e0b\u62c9\u5355\u900911111111","value":"\u4e0b\u62c9\u5355\u900911111111"},{"label":"\u4e0b\u62c9\u5355\u9009222222","value":"\u4e0b\u62c9\u5355\u9009222222"}],"ext_info":["\u4e0b\u62c9\u5355\u900911111111","\u4e0b\u62c9\u5355\u9009222222"],"field_type":"3","functional":["query","bucket","detail","group"],"enable_flag":"1","disable_flag":"0"},{"base":"0","name":"jolie\u4e0b\u62c9\u591a\u9009","type":"7","field":"order.1107736719","label":"","value":"","comment":"jolie\u4e0b\u62c9\u591a\u9009\uff08\u8ba2\u5355\uff09","options":[{"label":"\u4e0b\u62c9\u591a\u900911111","value":"\u4e0b\u62c9\u591a\u900911111"},{"label":"\u4e0b\u62c9\u591a\u90092222","value":"\u4e0b\u62c9\u591a\u90092222"},{"label":"\u4e0b\u62c9\u591a\u90093333","value":"\u4e0b\u62c9\u591a\u90093333"},{"label":"\u4e0b\u62c9\u591a\u90094444","value":"\u4e0b\u62c9\u591a\u90094444"}],"ext_info":["\u4e0b\u62c9\u591a\u900911111","\u4e0b\u62c9\u591a\u90092222","\u4e0b\u62c9\u591a\u90093333","\u4e0b\u62c9\u591a\u90094444"],"field_type":"7","functional":["query","bucket","detail","group"],"enable_flag":"1","disable_flag":"0"},{"base":"1","name":"\u521b\u5efa\u65e5\u671f","type":"4","field":"order.create_time","label":"","value":"","comment":"\u521b\u5efa\u65e5\u671f\uff08\u8ba2\u5355\uff09","ext_info":[],"field_type":"4","functional":["query","detail","group"],"enable_flag":"1","disable_flag":"0"},{"base":"1","name":"\u66f4\u65b0\u65e5\u671f","type":"4","field":"order.update_time","label":"","value":"","comment":"\u66f4\u65b0\u65e5\u671f\uff08\u8ba2\u5355\uff09","ext_info":[],"field_type":"4","functional":["query","detail","group"],"enable_flag":"1","disable_flag":"0"}],"total":{"total":{"switch":true},"detail":{"switch":true,"default":false},"subtotal":{"switch":true},"hide_zero":{"switch":true,"default":true}},"detail":[],"summaries":[{"key":"formula-transform","tip":"\u6210\u4ea4\u8ba2\u5355\u5ba2\u6237\u6570\/\u65b0\u5efa\u5ba2\u6237\u6570","type":"percent","label":"\u8f6c\u5316\u7387","order":"","field_type":"percent","refer_list":"","value_type":""},{"key":"formula-avg_company_amount","tip":"\u6210\u4ea4\u8ba2\u5355\u91d1\u989d\/\u6210\u4ea4\u8ba2\u5355\u5ba2\u6237\u6570","type":"formula","label":"\u5ba2\u5355\u4ef7","order":"","field_type":"float","refer_list":"","value_type":""}]},"language":"zh-CN","relevance":[{"key":"kh14","name":"\u65f6\u95f4"},{"key":"kh15","name":"\u8ddf\u8fdb\u4eba"},{"key":"kh16","name":"\u56fd\u5bb6\u5730\u533a"},{"key":"kh17","name":"\u5ba2\u6237\u6765\u6e90"},{"key":"kh18","name":"\u6765\u6e90\u5e97\u94fa"}],"permission":["crm.company.private.view"],"unique_key":"df64f00d3ffba254ce6070ae961401bf","update_time":"2024-05-07 17:42:05","subscribe_flag":false,"client_currency":"","report_object_name":"","can_setting_filter_field":true},"richness":1,"report":{"params":[{"type":"date","field":"company.archive_time","value":{"end":"2024-05-07","start":"2023-11-09"}},{"type":"select","field":"common.select_regional_scope","value":3}],"report_key":"kh16","name":"\u65b0\u5ba2\u6237\u6210\u4ea4\u8f6c\u5316\u60c5\u51b5"},"data_type":1,"analysis_record_id":3515216157}]}]},{"favorite":0,"process_record_ids":[3515218190,3515218192,3515218201,3515218206],"title":"\u5ba2\u6237\u589e\u957f\u4e0e\u8f6c\u5316","conclusion":"\u6574\u4f53\u5ba2\u6237\u8f6c\u5316\u7387\u5448\u4e0b\u964d\u8d8b\u52bf\uff0c\u7279\u522b\u662f2024\u5e741\u6708\u540e\uff0c\u53ef\u80fd\u4e0e\u5e02\u573a\u53d8\u5316\u6216\u9500\u552e\u7b56\u7565\u8c03\u6574\u6709\u5173\u3002\u4f2f\u5229\u5179\u7684\u6570\u636e\u663e\u793a\u6781\u7aef\u6ce2\u52a8\uff0c\u4ece100%\u8f6c\u5316\u7387\u52300%\uff0c\u8868\u660e\u53ef\u80fd\u5b58\u5728\u7684\u95ee\u9898\u3002\u963f\u91cc\u5df4\u5df4\u5e73\u53f0\u7684\u5ba2\u6237\u8f6c\u5316\u7387\u6ce2\u52a8\u8f83\u5927\uff0c\u8bf4\u660e\u67d0\u4e9b\u5468\u7684\u5ba2\u6237\u83b7\u53d6\u7b56\u7565\u53ef\u80fd\u66f4\u6709\u6548\u3002","suggestion":"\u5206\u67902024\u5e741\u6708\u540e\u7684\u5e02\u573a\u53d8\u5316\u548c\u9500\u552e\u7b56\u7565\uff0c\u627e\u51fa\u5bfc\u81f4\u8f6c\u5316\u7387\u4e0b\u964d\u7684\u539f\u56e0\uff0c\u5e76\u8fdb\u884c\u8c03\u6574\u3002\u9488\u5bf9\u4f2f\u5229\u5179\u5e02\u573a\u7684\u6781\u7aef\u6ce2\u52a8\uff0c\u6df1\u5165\u5206\u6790\u5ba2\u6237\u53c2\u4e0e\u5ea6\u548c\u9500\u552e\u7b56\u7565\u7684\u6709\u6548\u6027\uff0c\u4ee5\u786e\u5b9a\u6539\u8fdb\u63aa\u65bd\u3002\u5bf9\u4e8e\u963f\u91cc\u5df4\u5df4\u5e73\u53f0\uff0c\u5e94\u8bc6\u522b\u5e76\u590d\u5236\u90a3\u4e9b\u5468\u8f6c\u5316\u7387\u9ad8\u7684\u6210\u529f\u5ba2\u6237\u83b7\u53d6\u7b56\u7565\uff0c\u5e76\u63a2\u7d22\u5176\u80cc\u540e\u7684\u539f\u56e0\u3002","key":"new_customer_conversion","analysis_record_id":3515218221,"richness":1,"list":[{"refer_key":"new_customer_conversion","key":"overall_trend_of_change","title":"\u6574\u4f53\u53d8\u5316\u8d8b\u52bf","list":[{"title":"\u65b0\u589e\u5ba2\u6237\u6570\u53d8\u5316\u8d8b\u52bf","conclusion":"\u5ba2\u6237\u589e\u957f\u8d8b\u52bf\u663e\u793a\uff0c\u4ece2023\u5e747\u670817\u65e5\u81f32023\u5e747\u670823\u65e5\u4e4b\u95f4\u6709\u6700\u9ad8\u7684\u65b0\u5efa\u5ba2\u6237\u6570\uff0858\u4e2a\uff09\uff0c\u800c\u6700\u8fd1\u7684\u6570\u636e\u663e\u793a\u5ba2\u6237\u589e\u957f\u6709\u6240\u4e0b\u964d\uff0c\u7279\u522b\u662f\u57282023\u5e7412\u67084\u65e5\u81f32023\u5e7412\u670817\u65e5\u671f\u95f4\uff0c\u65b0\u5efa\u5ba2\u6237\u6570\u663e\u8457\u51cf\u5c11\u3002","sub_key":107,"data":{"title":{"text":"\u6570\u636e\u8be6\u60c5"},"config":{"XAxis":[{"name":"\u65e5\u671f(\u5ba2\u6237)","field":"company.date"}],"YAxis":[{"name":"\u65b0\u5efa\u5ba2\u6237\u6570","field":"mergeCount-company.create_ids"}],"charType":[{"label":"\u8868\u683c","value":"table"}],"chatFlag":true},"content":[]},"report_detail_data":[],"richness":0,"report":{"params":[{"type":"select","field":"common.select_cycle","value":"week"},{"type":"date","field":"company.select_date","value":{"end":"2024-05-07","start":"2023-11-09"}}],"report_key":"khts1","name":"\u65b0\u589e\u5ba2\u6237\u6570\u53d8\u5316\u8d8b\u52bf"},"data_type":0,"analysis_record_id":3515218189},{"title":"\u5ba2\u6237\u6210\u4ea4\u8f6c\u5316\u7387\u53d8\u5316\u8d8b\u52bf","conclusion":"\u6570\u636e\u663e\u793a\u5ba2\u6237\u6210\u4ea4\u8ba2\u5355\u6570\u5728\u4e0d\u540c\u65f6\u95f4\u6bb5\u6709\u6ce2\u52a8\uff0c\u4f46\u6574\u4f53\u8f6c\u5316\u7387\u5448\u4e0b\u964d\u8d8b\u52bf\uff0c\u7279\u522b\u662f\u57282024\u5e741\u6708\u4e4b\u540e\u8f6c\u5316\u7387\u663e\u8457\u4e0b\u964d\u3002\u5efa\u8bae\u5173\u6ce82024\u5e741\u6708\u540e\u7684\u5e02\u573a\u548c\u9500\u552e\u7b56\u7565\u8c03\u6574\uff0c\u4ee5\u63d0\u9ad8\u8f6c\u5316\u7387\u3002","sub_key":108,"data":{"title":{"text":"\u6570\u636e\u8be6\u60c5"},"config":{"XAxis":[{"name":"\u521b\u5efa\u65f6\u95f4(\u5ba2\u6237)","field":"company.archive_time"}],"YAxis":[{"name":"\u6210\u4ea4\u8ba2\u5355\u5ba2\u6237\u6570","field":"row-performance.company_id"},{"name":"\u8f6c\u5316\u7387","field":"formula-transform","unit":"percent"}],"charType":[{"label":"\u8868\u683c","value":"table"}],"chatFlag":true},"content":[{"formula-transform":0,"company.archive_time":"2024\/03\/25 ~ 2024\/03\/31","row-performance.company_id":0},{"formula-transform":8.33,"company.archive_time":"2024\/01\/29 ~ 2024\/02\/04","row-performance.company_id":1},{"formula-transform":14.29,"company.archive_time":"2024\/01\/22 ~ 2024\/01\/28","row-performance.company_id":1},{"formula-transform":20,"company.archive_time":"2024\/01\/08 ~ 2024\/01\/14","row-performance.company_id":1},{"formula-transform":0,"company.archive_time":"2024\/01\/01 ~ 2024\/01\/07","row-performance.company_id":0},{"formula-transform":100,"company.archive_time":"2023\/12\/04 ~ 2023\/12\/10","row-performance.company_id":1},{"formula-transform":50,"company.archive_time":"2023\/11\/20 ~ 2023\/11\/26","row-performance.company_id":1},{"formula-transform":0,"company.archive_time":"2023\/11\/06 ~ 2023\/11\/12","row-performance.company_id":0}]},"report_detail_data":{"key":"kh14","data":[[{"key":"company.archive_time","type":"field","label":"2024\/03\/25 ~ 2024\/03\/31","value":"2024\/03\/25 ~ 2024\/03\/31","summaries":{"formula-transform":{"key":"transform","value":0},"row-performance.company_id":{"key":"row-performance.company_id","value":0,"method":"row","refer_list":"companyList"}}}],[{"key":"company.archive_time","type":"field","label":"2024\/01\/29 ~ 2024\/02\/04","value":"2024\/01\/29 ~ 2024\/02\/04","summaries":{"formula-transform":{"key":"transform","value":8.33},"row-performance.company_id":{"key":"row-performance.company_id","value":1,"method":"row","refer_list":"companyList","report_item_unique_key":"88973552c572f70ff484fd593d280560"}}}],[{"key":"company.archive_time","type":"field","label":"2024\/01\/22 ~ 2024\/01\/28","value":"2024\/01\/22 ~ 2024\/01\/28","summaries":{"formula-transform":{"key":"transform","value":14.29},"row-performance.company_id":{"key":"row-performance.company_id","value":1,"method":"row","refer_list":"companyList","report_item_unique_key":"e15d0863cbc0cd4d3f92c66e9f6a3811"}}}],[{"key":"company.archive_time","type":"field","label":"2024\/01\/08 ~ 2024\/01\/14","value":"2024\/01\/08 ~ 2024\/01\/14","summaries":{"formula-transform":{"key":"transform","value":20},"row-performance.company_id":{"key":"row-performance.company_id","value":1,"method":"row","refer_list":"companyList","report_item_unique_key":"891dd8f42342266c2b58b83b6ec8715e"}}}],[{"key":"company.archive_time","type":"field","label":"2024\/01\/01 ~ 2024\/01\/07","value":"2024\/01\/01 ~ 2024\/01\/07","summaries":{"formula-transform":{"key":"transform","value":0},"row-performance.company_id":{"key":"row-performance.company_id","value":0,"method":"row","refer_list":"companyList"}}}],[{"key":"company.archive_time","type":"field","label":"2023\/12\/04 ~ 2023\/12\/10","value":"2023\/12\/04 ~ 2023\/12\/10","summaries":{"formula-transform":{"key":"transform","value":100},"row-performance.company_id":{"key":"row-performance.company_id","value":1,"method":"row","refer_list":"companyList","report_item_unique_key":"f087665787ef4f5e41fb92964bea4698"}}}],[{"key":"company.archive_time","type":"field","label":"2023\/11\/20 ~ 2023\/11\/26","value":"2023\/11\/20 ~ 2023\/11\/26","summaries":{"formula-transform":{"key":"transform","value":50},"row-performance.company_id":{"key":"row-performance.company_id","value":1,"method":"row","refer_list":"companyList","report_item_unique_key":"13f4fd2b7feb8337d0331f67d96bc2f3"}}}],[{"key":"company.archive_time","type":"field","label":"2023\/11\/06 ~ 2023\/11\/12","value":"2023\/11\/06 ~ 2023\/11\/12","summaries":{"formula-transform":{"key":"transform","value":0},"row-performance.company_id":{"key":"row-performance.company_id","value":0,"method":"row","refer_list":"companyList"}}}]],"desc":"\u4e86\u89e3\u65b0\u5ba2\u6237\u6210\u4ea4\u8f6c\u5316\u7387\u3001\u5ba2\u5355\u4ef7","name":"\u65b0\u5ba2\u6237\u6210\u4ea4\u8f6c\u5316\u60c5\u51b5","type":"group","chart":{"chartList":[{"group":["company.archive_time"],"option":[],"chartType":"horizontal-bar","summaries":["row-performance.company_id"]}]},"count":0,"title":"\u65b0\u5ba2\u6237\u6210\u4ea4\u8f6c\u5316\u60c5\u51b5","total":{"formula-transform":{"key":"transform","value":12.82},"row-company.company_id":{"key":"row-company.company_id","value":39,"method":"row"},"formula-avg_company_amount":{"key":"avg_company_amount","value":6719.96},"row-performance.company_id":{"key":"row-performance.company_id","value":5,"method":"row"},"sum-performance.indicator_value":{"key":"sum-performance.indicator_value","value":33599.78,"method":"sum"}},"config":{"field":[{"key":"company.archive_time","tip":"","type":"field","label":"\u521b\u5efa\u65f6\u95f4(\u5ba2\u6237)","field_type":"date"}],"group":[{"key":"company.archive_time","type":"row","extra":[],"label":"company.archive_time","order":"","field_type":"date"}],"order":[{"key":"company.archive_time","field":null,"order":"desc"}],"query":[{"type":"select_visible_user_id","field":"common.visible","label":"","value":[],"comment":"\u67e5\u770b\u8303\u56f4","multiple":1,"field_type":7},{"type":"date","field":"company.archive_time","label":"","value":{"end":"2024-05-07","start":"2023-11-09"},"period":"m","comment":"\u5efa\u6863\u65f6\u95f4","default":1,"multiple":0,"continuous":1,"field_type":4},{"type":"select","field":"common.select_cycle","label":"","value":"week","comment":"\u5468\u671f","options":[{"label":"\u6309\u5929","value":"day"},{"label":"\u6309\u5468","value":"week"},{"label":"\u6309\u6708","value":"month"},{"label":"\u6309\u5b63\u5ea6","value":"season"},{"label":"\u6309\u534a\u5e74","value":"half_a_year"}],"multiple":0,"field_type":3},{"type":7,"field":"company.ali_store_id","label":"","value":"","comment":"\u6765\u6e90\u5e97\u94fa","field_type":7},{"type":"select_trail_status","field":"company.trail_status","label":"","value":[],"comment":"\u5ba2\u6237\u9636\u6bb5","options":[],"multiple":1,"field_type":7},{"type":"select_biz_type","field":"company.biz_type","label":"","value":"","comment":"\u5ba2\u6237\u7c7b\u578b","options":[],"multiple":0,"field_type":3},{"type":"select_country","field":"company.country","label":"","value":[],"comment":"\u56fd\u5bb6\u5730\u533a","options":[],"multiple":1,"field_type":7},{"type":"select_origin_list","field":"company.origin_list","label":"","value":[],"comment":"\u5ba2\u6237\u6765\u6e90","options":[],"multiple":1,"field_type":7},{"type":"select_star","field":"company.star","label":"","value":[],"comment":"\u5ba2\u6237\u661f\u7ea7","options":[],"multiple":1,"field_type":7},{"type":"select_company_group","field":"company.group_id","label":"","value":[],"comment":"\u5ba2\u6237\u5206\u7ec4","options":[],"multiple":1,"field_type":7}],"total":{"total":{"switch":true},"detail":{"switch":true,"default":false},"subtotal":{"switch":true},"hide_zero":{"switch":true,"default":true}},"detail":[],"summaries":[{"key":"row-performance.company_id","tip":"\u7edf\u8ba1\u7b26\u5408\u76ee\u6807\u89c4\u5219\u3010\u6210\u4ea4\u8ba2\u5355\u91d1\u989d\u3011\u7684\u9500\u552e\u8ba2\u5355\u5173\u8054\u7684\u5ba2\u6237","type":"row","label":"\u6210\u4ea4\u8ba2\u5355\u5ba2\u6237\u6570","order":"","field_type":"","refer_list":"companyList","value_type":""},{"key":"formula-transform","tip":"\u6210\u4ea4\u8ba2\u5355\u5ba2\u6237\u6570\/\u65b0\u5efa\u5ba2\u6237\u6570","type":"percent","label":"\u8f6c\u5316\u7387","order":"","field_type":"percent","refer_list":"","value_type":""}]},"language":"zh-CN","relevance":[{"key":"kh14","name":"\u65f6\u95f4"},{"key":"kh15","name":"\u8ddf\u8fdb\u4eba"},{"key":"kh16","name":"\u56fd\u5bb6\u5730\u533a"},{"key":"kh17","name":"\u5ba2\u6237\u6765\u6e90"},{"key":"kh18","name":"\u6765\u6e90\u5e97\u94fa"}],"permission":["crm.company.private.view"],"unique_key":"f1935409942a75114613d0a608e3b2c7","update_time":"2024-05-07 17:43:11","subscribe_flag":false,"client_currency":"","report_object_name":"","can_setting_filter_field":true},"richness":1,"report":{"params":[{"type":"select","field":"common.select_cycle","value":"week"},{"type":"date","field":"company.archive_time","value":{"end":"2024-05-07","start":"2023-11-09"}}],"report_key":"kh14","name":"\u65b0\u5ba2\u6237\u6210\u4ea4\u8f6c\u5316\u60c5\u51b5"},"data_type":1,"analysis_record_id":3515218191}],"richness":1},{"richness":1,"refer_key":"new_customer_conversion","key":"top2_country_of_new_customer","title":"\u65b0\u589e\u5ba2\u6237\u6570TOP2\u56fd\u5bb6","list":[{"title":"Top1\u56fd\u5bb6\u5730\u533a\u65b0\u589e\u5ba2\u6237\u6570\u4e0e\u8f6c\u5316\u7387\u53d8\u5316\u8d8b\u52bf","conclusion":"\uff08\u4f2f\u5229\u5179\uff09\u6570\u636e\u663e\u793a\u57282024\u5e741\u67088\u65e5\u81f31\u670814\u65e5\u671f\u95f4\u65b0\u5efa\u4e86\u4e00\u4e2a\u5ba2\u6237\uff0c\u5e76\u4e14\u8f6c\u5316\u7387\u8fbe\u5230\u4e86100%\u3002\u7136\u800c\uff0c\u57282024\u5e741\u670829\u65e5\u81f32\u67084\u65e5\uff0c\u5c3d\u7ba1\u4e5f\u65b0\u5efa\u4e86\u4e00\u4e2a\u5ba2\u6237\uff0c\u4f46\u8f6c\u5316\u7387\u4e3a0%\u3002\u8fd9\u8868\u660e\u5728\u540e\u4e00\u65f6\u671f\u53ef\u80fd\u5b58\u5728\u5ba2\u6237\u53c2\u4e0e\u5ea6\u4e0b\u964d\u6216\u9500\u552e\u7b56\u7565\u6548\u679c\u4e0d\u4f73\u7684\u95ee\u9898\u3002","sub_key":109,"data":{"title":{"text":"\u6570\u636e\u8be6\u60c5"},"config":{"XAxis":[{"name":"\u521b\u5efa\u65f6\u95f4(\u5ba2\u6237)","field":"company.archive_time"}],"YAxis":[{"name":"\u65b0\u5efa\u5ba2\u6237\u6570","field":"row-company.company_id"},{"name":"\u8f6c\u5316\u7387","field":"formula-transform","unit":"percent"}],"charType":[{"label":"\u8868\u683c","value":"table"}],"chatFlag":true},"content":[{"formula-transform":0,"company.archive_time":"2024\/01\/29 ~ 2024\/02\/04","row-company.company_id":1},{"formula-transform":100,"company.archive_time":"2024\/01\/08 ~ 2024\/01\/14","row-company.company_id":1}]},"report_detail_data":{"key":"kh14","data":[[{"key":"company.archive_time","type":"field","label":"2024\/01\/29 ~ 2024\/02\/04","value":"2024\/01\/29 ~ 2024\/02\/04","summaries":{"formula-transform":{"key":"transform","value":0},"row-company.company_id":{"key":"row-company.company_id","value":1,"method":"row","refer_list":"companyList","report_item_unique_key":"8bc5612d9bb779d3aae65e98aa1ce968"}}}],[{"key":"company.archive_time","type":"field","label":"2024\/01\/08 ~ 2024\/01\/14","value":"2024\/01\/08 ~ 2024\/01\/14","summaries":{"formula-transform":{"key":"transform","value":100},"row-company.company_id":{"key":"row-company.company_id","value":1,"method":"row","refer_list":"companyList","report_item_unique_key":"12237fe6cb67fe10a6bb403d8b648c47"}}}]],"desc":"\u4e86\u89e3\u65b0\u5ba2\u6237\u6210\u4ea4\u8f6c\u5316\u7387\u3001\u5ba2\u5355\u4ef7","name":"\u65b0\u5ba2\u6237\u6210\u4ea4\u8f6c\u5316\u60c5\u51b5","type":"group","chart":{"chartList":[{"group":["company.archive_time"],"option":[],"chartType":"horizontal-bar","summaries":["row-company.company_id"]}]},"count":0,"title":"\u65b0\u5ba2\u6237\u6210\u4ea4\u8f6c\u5316\u60c5\u51b5","total":{"formula-transform":{"key":"transform","value":50},"row-company.company_id":{"key":"row-company.company_id","value":2,"method":"row"},"formula-avg_company_amount":{"key":"avg_company_amount","value":3179.57},"row-performance.company_id":{"key":"row-performance.company_id","value":1,"method":"row"},"sum-performance.indicator_value":{"key":"sum-performance.indicator_value","value":3179.57,"method":"sum"}},"config":{"field":[{"key":"company.archive_time","tip":"","type":"field","label":"\u521b\u5efa\u65f6\u95f4(\u5ba2\u6237)","field_type":"date"}],"group":[{"key":"company.archive_time","type":"row","extra":[],"label":"company.archive_time","order":"","field_type":"date"}],"order":[{"key":"company.archive_time","field":null,"order":"desc"}],"query":[{"type":"select_visible_user_id","field":"common.visible","label":"","value":[],"comment":"\u67e5\u770b\u8303\u56f4","multiple":1,"field_type":7},{"type":"date","field":"company.archive_time","label":"","value":{"end":"2024-05-07","start":"2023-11-09"},"period":"m","comment":"\u5efa\u6863\u65f6\u95f4","default":1,"multiple":0,"continuous":1,"field_type":4},{"type":"select","field":"common.select_cycle","label":"","value":"week","comment":"\u5468\u671f","options":[{"label":"\u6309\u5929","value":"day"},{"label":"\u6309\u5468","value":"week"},{"label":"\u6309\u6708","value":"month"},{"label":"\u6309\u5b63\u5ea6","value":"season"},{"label":"\u6309\u534a\u5e74","value":"half_a_year"}],"multiple":0,"field_type":3},{"type":7,"field":"company.ali_store_id","label":"","value":"","comment":"\u6765\u6e90\u5e97\u94fa","field_type":7},{"type":"select_trail_status","field":"company.trail_status","label":"","value":[],"comment":"\u5ba2\u6237\u9636\u6bb5","options":[],"multiple":1,"field_type":7},{"type":"select_biz_type","field":"company.biz_type","label":"","value":"","comment":"\u5ba2\u6237\u7c7b\u578b","options":[],"multiple":0,"field_type":3},{"type":"select_country","field":"company.country","label":"","value":["BZ"],"comment":"\u56fd\u5bb6\u5730\u533a","options":[],"multiple":1,"field_type":7},{"type":"select_origin_list","field":"company.origin_list","label":"","value":[],"comment":"\u5ba2\u6237\u6765\u6e90","options":[],"multiple":1,"field_type":7},{"type":"select_star","field":"company.star","label":"","value":[],"comment":"\u5ba2\u6237\u661f\u7ea7","options":[],"multiple":1,"field_type":7},{"type":"select_company_group","field":"company.group_id","label":"","value":[],"comment":"\u5ba2\u6237\u5206\u7ec4","options":[],"multiple":1,"field_type":7}],"total":{"total":{"switch":true},"detail":{"switch":true,"default":false},"subtotal":{"switch":true},"hide_zero":{"switch":true,"default":true}},"detail":[],"summaries":[{"key":"row-company.company_id","tip":"","type":"row","label":"\u65b0\u5efa\u5ba2\u6237\u6570","order":"","field_type":"","refer_list":"companyList","value_type":""},{"key":"formula-transform","tip":"\u6210\u4ea4\u8ba2\u5355\u5ba2\u6237\u6570\/\u65b0\u5efa\u5ba2\u6237\u6570","type":"percent","label":"\u8f6c\u5316\u7387","order":"","field_type":"percent","refer_list":"","value_type":""}]},"language":"zh-CN","relevance":[{"key":"kh14","name":"\u65f6\u95f4"},{"key":"kh15","name":"\u8ddf\u8fdb\u4eba"},{"key":"kh16","name":"\u56fd\u5bb6\u5730\u533a"},{"key":"kh17","name":"\u5ba2\u6237\u6765\u6e90"},{"key":"kh18","name":"\u6765\u6e90\u5e97\u94fa"}],"permission":["crm.company.private.view"],"unique_key":"151ac6df897640198ebcb0eba912ec08","update_time":"2024-05-07 17:43:23","subscribe_flag":false,"client_currency":"","report_object_name":"","can_setting_filter_field":true},"richness":1,"report":{"params":[{"type":"select","field":"common.select_cycle","value":"week"},{"type":"select_country","field":"company.country","value":["BZ"]},{"type":"date","field":"company.archive_time","value":{"end":"2024-05-07","start":"2023-11-09"}}],"report_key":"kh14","name":"\u65b0\u5ba2\u6237\u6210\u4ea4\u8f6c\u5316\u60c5\u51b5"},"data_type":1,"analysis_record_id":3515218199},{"title":"Top2\u56fd\u5bb6\u5730\u533a\u65b0\u589e\u5ba2\u6237\u6570\u4e0e\u8f6c\u5316\u7387\u53d8\u5316\u8d8b\u52bf","conclusion":"\uff08\u4fc4\u7f57\u65af\uff09\u6570\u636e\u663e\u793a\u5728\u89c2\u5bdf\u671f\u95f4\u5185\uff0c\u65b0\u5efa\u5ba2\u6237\u6570\u6709\u6ce2\u52a8\uff0c\u4f46\u8f6c\u5316\u7387\u6301\u7eed\u4e3a0%\uff0c\u8868\u660e\u5c3d\u7ba1\u6709\u65b0\u5ba2\u6237\u7684\u52a0\u5165\uff0c\u4f46\u6ca1\u6709\u4e00\u4e2a\u6210\u529f\u8f6c\u5316\u4e3a\u4ed8\u8d39\u7528\u6237\u3002\u8fd9\u53ef\u80fd\u610f\u5473\u7740\u5ba2\u6237\u83b7\u53d6\u7b56\u7565\u5728\u5438\u5f15\u6f5c\u5728\u5ba2\u6237\u65b9\u9762\u6709\u6548\uff0c\u4f46\u5728\u8f6c\u5316\u73af\u8282\u5b58\u5728\u95ee\u9898\uff0c\u9700\u8981\u8fdb\u4e00\u6b65\u5206\u6790\u5e76\u4f18\u5316\u8f6c\u5316\u6d41\u7a0b\u3002","sub_key":110,"data":{"title":{"text":"\u6570\u636e\u8be6\u60c5"},"config":{"XAxis":[{"name":"\u521b\u5efa\u65f6\u95f4(\u5ba2\u6237)","field":"company.archive_time"}],"YAxis":[{"name":"\u65b0\u5efa\u5ba2\u6237\u6570","field":"row-company.company_id"},{"name":"\u8f6c\u5316\u7387","field":"formula-transform","unit":"percent"}],"charType":[{"label":"\u8868\u683c","value":"table"}],"chatFlag":true},"content":[{"formula-transform":0,"company.archive_time":"2024\/01\/08 ~ 2024\/01\/14","row-company.company_id":1}]},"report_detail_data":[],"richness":0,"report":{"params":[{"type":"select","field":"common.select_cycle","value":"week"},{"type":"select_country","field":"company.country","value":["CN"]},{"type":"date","field":"company.archive_time","value":{"end":"2024-05-07","start":"2023-11-09"}}],"report_key":"kh14","name":"\u65b0\u5ba2\u6237\u6210\u4ea4\u8f6c\u5316\u60c5\u51b5"},"data_type":0,"analysis_record_id":3515218200}]},{"richness":1,"refer_key":"new_customer_conversion","key":"top2_source_of_new_customer","title":"\u65b0\u589e\u5ba2\u6237\u6570TOP2\u6765\u6e90\u6e20\u9053","list":[{"title":"Top1\u6765\u6e90\u6e20\u9053\u65b0\u589e\u5ba2\u6237\u6570\u4e0e\u8f6c\u5316\u7387\u53d8\u5316\u8d8b\u52bf","conclusion":"\uff08\u963f\u91cc\u5df4\u5df4\uff08B2B\u5e73\u53f0\uff09\uff09\u5728\u6240\u63d0\u4f9b\u7684\u65f6\u95f4\u6bb5\u5185\uff0c\u65b0\u5efa\u5ba2\u6237\u6570\u4fdd\u6301\u76f8\u5bf9\u7a33\u5b9a\uff0c\u4e3a\u6bcf\u54683\u4e2a\u62161\u4e2a\u3002\u7136\u800c\uff0c\u8f6c\u5316\u7387\u5728\u4e0d\u540c\u7684\u65f6\u95f4\u6bb5\u6709\u663e\u8457\u5dee\u5f02\uff0c\u4ece33.33%\u5230100%\u3002\u8fd9\u8868\u660e\u5728\u67d0\u4e9b\u5468\u5185\uff0c\u5ba2\u6237\u83b7\u53d6\u7b56\u7565\u53ef\u80fd\u66f4\u4e3a\u6709\u6548\u3002","sub_key":111,"data":{"title":{"text":"\u6570\u636e\u8be6\u60c5"},"config":{"XAxis":[{"name":"\u521b\u5efa\u65f6\u95f4(\u5ba2\u6237)","field":"company.archive_time"}],"YAxis":[{"name":"\u65b0\u5efa\u5ba2\u6237\u6570","field":"row-company.company_id"},{"name":"\u8f6c\u5316\u7387","field":"formula-transform","unit":"percent"}],"charType":[{"label":"\u8868\u683c","value":"table"}],"chatFlag":true},"content":[{"formula-transform":33.33,"company.archive_time":"2024\/01\/29 ~ 2024\/02\/04","row-company.company_id":3},{"formula-transform":33.33,"company.archive_time":"2024\/01\/08 ~ 2024\/01\/14","row-company.company_id":3},{"formula-transform":100,"company.archive_time":"2023\/12\/04 ~ 2023\/12\/10","row-company.company_id":1}]},"report_detail_data":{"key":"kh14","data":[[{"key":"company.archive_time","type":"field","label":"2024\/01\/29 ~ 2024\/02\/04","value":"2024\/01\/29 ~ 2024\/02\/04","summaries":{"formula-transform":{"key":"transform","value":33.33},"row-company.company_id":{"key":"row-company.company_id","value":3,"method":"row","refer_list":"companyList","report_item_unique_key":"2c81b368d97a22c1ca532649b830a62f"}}}],[{"key":"company.archive_time","type":"field","label":"2024\/01\/08 ~ 2024\/01\/14","value":"2024\/01\/08 ~ 2024\/01\/14","summaries":{"formula-transform":{"key":"transform","value":33.33},"row-company.company_id":{"key":"row-company.company_id","value":3,"method":"row","refer_list":"companyList","report_item_unique_key":"349606cc520f3f934d8911cbf41a23da"}}}],[{"key":"company.archive_time","type":"field","label":"2023\/12\/04 ~ 2023\/12\/10","value":"2023\/12\/04 ~ 2023\/12\/10","summaries":{"formula-transform":{"key":"transform","value":100},"row-company.company_id":{"key":"row-company.company_id","value":1,"method":"row","refer_list":"companyList","report_item_unique_key":"cbe03d8f032e1d81edc5db10d7edaa7d"}}}]],"desc":"\u4e86\u89e3\u65b0\u5ba2\u6237\u6210\u4ea4\u8f6c\u5316\u7387\u3001\u5ba2\u5355\u4ef7","name":"\u65b0\u5ba2\u6237\u6210\u4ea4\u8f6c\u5316\u60c5\u51b5","type":"group","chart":{"chartList":[{"group":["company.archive_time"],"option":[],"chartType":"horizontal-bar","summaries":["row-company.company_id"]}]},"count":0,"title":"\u65b0\u5ba2\u6237\u6210\u4ea4\u8f6c\u5316\u60c5\u51b5","total":{"formula-transform":{"key":"transform","value":42.86},"row-company.company_id":{"key":"row-company.company_id","value":7,"method":"row"},"formula-avg_company_amount":{"key":"avg_company_amount","value":2317.19},"row-performance.company_id":{"key":"row-performance.company_id","value":3,"method":"row"},"sum-performance.indicator_value":{"key":"sum-performance.indicator_value","value":6951.56,"method":"sum"}},"config":{"field":[{"key":"company.archive_time","tip":"","type":"field","label":"\u521b\u5efa\u65f6\u95f4(\u5ba2\u6237)","field_type":"date"}],"group":[{"key":"company.archive_time","type":"row","extra":[],"label":"company.archive_time","order":"","field_type":"date"}],"order":[{"key":"company.archive_time","field":null,"order":"desc"}],"query":[{"type":"select_visible_user_id","field":"common.visible","label":"","value":[],"comment":"\u67e5\u770b\u8303\u56f4","multiple":1,"field_type":7},{"type":"date","field":"company.archive_time","label":"","value":{"end":"2024-05-07","start":"2023-11-09"},"period":"m","comment":"\u5efa\u6863\u65f6\u95f4","default":1,"multiple":0,"continuous":1,"field_type":4},{"type":"select","field":"common.select_cycle","label":"","value":"week","comment":"\u5468\u671f","options":[{"label":"\u6309\u5929","value":"day"},{"label":"\u6309\u5468","value":"week"},{"label":"\u6309\u6708","value":"month"},{"label":"\u6309\u5b63\u5ea6","value":"season"},{"label":"\u6309\u534a\u5e74","value":"half_a_year"}],"multiple":0,"field_type":3},{"type":7,"field":"company.ali_store_id","label":"","value":"","comment":"\u6765\u6e90\u5e97\u94fa","field_type":7},{"type":"select_trail_status","field":"company.trail_status","label":"","value":[],"comment":"\u5ba2\u6237\u9636\u6bb5","options":[],"multiple":1,"field_type":7},{"type":"select_biz_type","field":"company.biz_type","label":"","value":"","comment":"\u5ba2\u6237\u7c7b\u578b","options":[],"multiple":0,"field_type":3},{"type":"select_country","field":"company.country","label":"","value":[],"comment":"\u56fd\u5bb6\u5730\u533a","options":[],"multiple":1,"field_type":7},{"type":"select_origin_list","field":"company.origin_list","label":"","value":[4],"comment":"\u5ba2\u6237\u6765\u6e90","options":[],"multiple":1,"field_type":7},{"type":"select_star","field":"company.star","label":"","value":[],"comment":"\u5ba2\u6237\u661f\u7ea7","options":[],"multiple":1,"field_type":7},{"type":"select_company_group","field":"company.group_id","label":"","value":[],"comment":"\u5ba2\u6237\u5206\u7ec4","options":[],"multiple":1,"field_type":7}],"total":{"total":{"switch":true},"detail":{"switch":true,"default":false},"subtotal":{"switch":true},"hide_zero":{"switch":true,"default":true}},"detail":[],"summaries":[{"key":"row-company.company_id","tip":"","type":"row","label":"\u65b0\u5efa\u5ba2\u6237\u6570","order":"","field_type":"","refer_list":"companyList","value_type":""},{"key":"formula-transform","tip":"\u6210\u4ea4\u8ba2\u5355\u5ba2\u6237\u6570\/\u65b0\u5efa\u5ba2\u6237\u6570","type":"percent","label":"\u8f6c\u5316\u7387","order":"","field_type":"percent","refer_list":"","value_type":""}]},"language":"zh-CN","relevance":[{"key":"kh14","name":"\u65f6\u95f4"},{"key":"kh15","name":"\u8ddf\u8fdb\u4eba"},{"key":"kh16","name":"\u56fd\u5bb6\u5730\u533a"},{"key":"kh17","name":"\u5ba2\u6237\u6765\u6e90"},{"key":"kh18","name":"\u6765\u6e90\u5e97\u94fa"}],"permission":["crm.company.private.view"],"unique_key":"57a7d4a22815096a23d98b22a10a1cf6","update_time":"2024-05-07 17:43:47","subscribe_flag":false,"client_currency":"","report_object_name":"","can_setting_filter_field":true},"richness":1,"report":{"params":[{"type":"select","field":"common.select_cycle","value":"week"},{"type":"select_origin_list","field":"company.origin_list","value":[4]},{"type":"date","field":"company.archive_time","value":{"end":"2024-05-07","start":"2023-11-09"}}],"report_key":"kh14","name":"\u65b0\u5ba2\u6237\u6210\u4ea4\u8f6c\u5316\u60c5\u51b5"},"data_type":1,"analysis_record_id":3515218204},{"title":"Top2\u6765\u6e90\u6e20\u9053\u65b0\u589e\u5ba2\u6237\u6570\u4e0e\u8f6c\u5316\u7387\u53d8\u5316\u8d8b\u52bf","conclusion":"\uff08\u5c55\u4f1a\uff09\u6570\u636e\u663e\u793a\uff0c\u5ba2\u6237\u589e\u957f\u57282023\u5e748\u670814\u65e5\u81f327\u65e5\u8fbe\u5230\u5cf0\u503c\uff0c\u4f46\u6b64\u540e\u65b0\u5efa\u5ba2\u6237\u6570\u6709\u6240\u4e0b\u964d\u3002\u5728\u6b64\u671f\u95f4\uff0c\u8f6c\u5316\u7387\u57289\u670818\u65e5\u81f324\u65e5\u8fbe\u5230\u6700\u9ad8\u70b910%\uff0c\u4f46\u5728\u5927\u591a\u6570\u5176\u4ed6\u65f6\u95f4\u6bb5\u8f6c\u5316\u7387\u4e3a0%\u3002\u8fd9\u8868\u660e\u5c3d\u7ba1\u5ba2\u6237\u589e\u957f\u6709\u6240\u6ce2\u52a8\uff0c\u4f46\u8f6c\u5316\u6548\u679c\u5e76\u4e0d\u7406\u60f3\uff0c\u9700\u8981\u8fdb\u4e00\u6b65\u5206\u6790\u539f\u56e0\u5e76\u4f18\u5316\u8f6c\u5316\u7b56\u7565\u3002","sub_key":112,"data":{"title":{"text":"\u6570\u636e\u8be6\u60c5"},"config":{"XAxis":[{"name":"\u521b\u5efa\u65f6\u95f4(\u5ba2\u6237)","field":"company.archive_time"}],"YAxis":[{"name":"\u65b0\u5efa\u5ba2\u6237\u6570","field":"row-company.company_id"},{"name":"\u8f6c\u5316\u7387","field":"formula-transform","unit":"percent"}],"charType":[{"label":"\u8868\u683c","value":"table"}],"chatFlag":true},"content":[{"formula-transform":50,"company.archive_time":"2024\/01\/29 ~ 2024\/02\/04","row-company.company_id":2}]},"report_detail_data":[],"richness":0,"report":{"params":[{"type":"select","field":"common.select_cycle","value":"week"},{"type":"select_origin_list","field":"company.origin_list","value":[5]},{"type":"date","field":"company.archive_time","value":{"end":"2024-05-07","start":"2023-11-09"}}],"report_key":"kh14","name":"\u65b0\u5ba2\u6237\u6210\u4ea4\u8f6c\u5316\u60c5\u51b5"},"data_type":0,"analysis_record_id":3515218205}]}]},{"favorite":0,"title":"\u5ba2\u6237\u590d\u8d2d","conclusion":"\u9886\u82f1\u4f5c\u4e3a\u552f\u4e00\u663e\u793a\u6709\u590d\u8d2d\u884c\u4e3a\u7684\u6e20\u9053\uff0c\u8868\u660e\u5176\u5728\u5ba2\u6237\u7ef4\u62a4\u65b9\u9762\u8868\u73b0\u51fa\u8272\uff0c\u5c24\u5176\u662f\u572842\u5929\u7684\u590d\u8d2d\u5468\u671f\u5185\u3002\u5fb7\u56fd\u5e02\u573a\u5c3d\u7ba1\u590d\u8d2d\u5ba2\u6237\u6570\u8f83\u5c11\uff0c\u4f46\u590d\u8d2d\u7387\u8fbe\u5230100%\uff0c\u663e\u793a\u51fa\u6781\u9ad8\u7684\u5ba2\u6237\u5fe0\u8bda\u5ea6\u3002\u7f8e\u56fd\u5e02\u573a\u7684\u590d\u8d2d\u5ba2\u6237\u6570\u8f83\u591a\uff0c\u4f46\u590d\u8d2d\u5468\u671f\u8f83\u957f\uff0c\u8868\u660e\u6709\u63d0\u5347\u590d\u8d2d\u9891\u7387\u7684\u7a7a\u95f4\u3002","suggestion":"\u9488\u5bf9\u9886\u82f1\u5e73\u53f0\uff0c\u5e94\u52a0\u5927\u8425\u9500\u529b\u5ea6\uff0c\u7ef4\u62a4\u548c\u63d0\u5347\u5ba2\u6237\u5fe0\u8bda\u5ea6\uff1b\u5bf9\u4e8e\u5fb7\u56fd\u5e02\u573a\uff0c\u53ef\u4ee5\u63a2\u7d22\u63d0\u9ad8\u5ba2\u6237\u6570\u91cf\u7684\u7b56\u7565\uff0c\u540c\u65f6\u4fdd\u6301\u9ad8\u590d\u8d2d\u7387\uff1b\u7f8e\u56fd\u5e02\u573a\u5e94\u7814\u7a76\u5ef6\u957f\u5ba2\u6237\u590d\u8d2d\u5468\u671f\u7684\u539f\u56e0\uff0c\u5e76\u9488\u5bf9\u6027\u5730\u63d0\u51fa\u89e3\u51b3\u65b9\u6848\uff1b\u5370\u5ea6\u5e02\u573a\u5219\u53ef\u4ee5\u8003\u8651\u589e\u52a0\u5ba2\u6237\u7c98\u6027\u7684\u63aa\u65bd\uff0c\u4ee5\u63d0\u9ad8\u5ba2\u6237\u7684\u590d\u8d2d\u7387\u3002","key":"customer_repurchase_situation","analysis_record_id":3515218226,"richness":0,"list":[{"refer_key":"customer_repurchase_situation","key":"generate_content_source_channels","title":"\u6765\u6e90\u6e20\u9053\u7684\u590d\u8d2d\u60c5\u51b5","list":[{"title":"\u590d\u8d2d\u7387\u5206\u5e03","conclusion":"\u9886\u82f1\u662f\u552f\u4e00\u663e\u793a\u6709\u590d\u8d2d\u884c\u4e3a\u7684\u5ba2\u6237\u6765\u6e90\uff0c\u590d\u8d2d\u7387\u4e3a24%\uff0c\u8868\u660e\u9886\u82f1\u5e73\u53f0\u7684\u5ba2\u6237\u8d28\u91cf\u76f8\u5bf9\u8f83\u9ad8\u3002","sub_key":113,"data":{"title":{"text":"\u6570\u636e\u8be6\u60c5"},"config":{"XAxis":[{"name":"\u5ba2\u6237\u6765\u6e90(\u5ba2\u6237)","field":"company.origin_list"}],"YAxis":[{"name":"\u590d\u8d2d\u5ba2\u6237\u6570","field":"row-company.repeat_customers_list"},{"name":"\u590d\u8d2d\u5ba2\u6237\u5360\u6bd4","field":"formula-company.repeat_customer_rate","unit":"percent"}],"charType":[{"label":"\u8868\u683c","value":"table"}],"chatFlag":true},"content":[{"company.origin_list":"\u6f5c\u5ba2\u8fd0\u8425","row-company.repeat_customers_list":0,"formula-company.repeat_customer_rate":0},{"company.origin_list":"\u5c0f\u6ee1\u53d1\u73b0","row-company.repeat_customers_list":0,"formula-company.repeat_customer_rate":0},{"company.origin_list":"ERP\u540c\u6b65","row-company.repeat_customers_list":0,"formula-company.repeat_customer_rate":0},{"company.origin_list":"\u73af\u7403\u5e02\u573a\uff08B2B\u5e73\u53f0\uff09","row-company.repeat_customers_list":0,"formula-company.repeat_customer_rate":0},{"company.origin_list":"\u963f\u91cc\u5df4\u5df4\uff08B2B\u5e73\u53f0\uff09","row-company.repeat_customers_list":0,"formula-company.repeat_customer_rate":0},{"company.origin_list":"\u73af\u7403\u8d44\u6e90\uff08B2B\u5e73\u53f0\uff09","row-company.repeat_customers_list":0,"formula-company.repeat_customer_rate":0}]},"report_detail_data":[],"richness":0,"report":{"params":[{"type":"select","field":"company.select_origin_scope","value":1},{"type":"date","field":"company.archive_time","value":{"end":"2024-05-07","start":"2023-11-09"}}],"report_key":"kh22","name":"\u5ba2\u6237\u590d\u8d2d\u60c5\u51b5\uff08\u57fa\u4e8e\u9500\u552e\u8ba2\u5355\uff09"},"data_type":0,"analysis_record_id":3515218222},{"title":"\u590d\u8d2d\u5468\u671f\u5206\u5e03","conclusion":"\u9886\u82f1\u5e73\u53f0\u7684\u5ba2\u6237\u5e73\u5747\u590d\u8d2d\u5468\u671f\u4e3a42\u5929\uff0c\u800c\u5176\u4ed6\u6e20\u9053\u672a\u663e\u793a\u590d\u8d2d\u884c\u4e3a\uff0c\u8bf4\u660e\u9886\u82f1\u5e73\u53f0\u7684\u5ba2\u6237\u66f4\u6709\u53ef\u80fd\u6210\u4e3a\u56de\u5934\u5ba2\u3002","sub_key":114,"data":{"title":{"text":"\u6570\u636e\u8be6\u60c5"},"config":{"XAxis":[{"name":"\u5ba2\u6237\u6765\u6e90(\u5ba2\u6237)","field":"company.origin_list"}],"YAxis":[{"name":"\u590d\u8d2d\u5ba2\u6237\u6570","field":"row-company.repeat_customers_list"},{"name":"\u590d\u8d2d\u5468\u671f\uff08\u5929\uff09","field":"formula-company.avg_repurchase_cycle"}],"charType":[{"label":"\u8868\u683c","value":"table"}],"chatFlag":true},"content":[{"company.origin_list":"\u6f5c\u5ba2\u8fd0\u8425","row-company.repeat_customers_list":0,"formula-company.avg_repurchase_cycle":0},{"company.origin_list":"\u5c0f\u6ee1\u53d1\u73b0","row-company.repeat_customers_list":0,"formula-company.avg_repurchase_cycle":0},{"company.origin_list":"ERP\u540c\u6b65","row-company.repeat_customers_list":0,"formula-company.avg_repurchase_cycle":0},{"company.origin_list":"\u73af\u7403\u5e02\u573a\uff08B2B\u5e73\u53f0\uff09","row-company.repeat_customers_list":0,"formula-company.avg_repurchase_cycle":0},{"company.origin_list":"\u963f\u91cc\u5df4\u5df4\uff08B2B\u5e73\u53f0\uff09","row-company.repeat_customers_list":0,"formula-company.avg_repurchase_cycle":0},{"company.origin_list":"\u73af\u7403\u8d44\u6e90\uff08B2B\u5e73\u53f0\uff09","row-company.repeat_customers_list":0,"formula-company.avg_repurchase_cycle":0}]},"report_detail_data":[],"richness":0,"report":{"params":[{"type":"select","field":"company.select_origin_scope","value":1},{"type":"date","field":"company.archive_time","value":{"end":"2024-05-07","start":"2023-11-09"}}],"report_key":"kh22","name":"\u5ba2\u6237\u590d\u8d2d\u60c5\u51b5\uff08\u57fa\u4e8e\u9500\u552e\u8ba2\u5355\uff09"},"data_type":0,"analysis_record_id":3515218223}]},{"refer_key":"customer_repurchase_situation","key":"generate_content_country_channels","title":"\u56fd\u5bb6\u5730\u533a\u7684\u590d\u8d2d\u60c5\u51b5","list":[{"title":"\u590d\u8d2d\u7387\u5206\u5e03","conclusion":"\u5fb7\u56fd\u7684\u590d\u8d2d\u7387\u8fbe\u5230100%\uff0c\u5c3d\u7ba1\u590d\u8d2d\u5ba2\u6237\u6570\u53ea\u67092\u4eba\uff0c\u8868\u660e\u5fb7\u56fd\u5e02\u573a\u7684\u5ba2\u6237\u5fe0\u8bda\u5ea6\u53ef\u80fd\u5f88\u9ad8\u3002\u7f8e\u56fd\u548c\u5370\u5ea6\u7684\u590d\u8d2d\u7387\u4e3a50%\uff0c\u4f46\u7f8e\u56fd\u7684\u590d\u8d2d\u5ba2\u6237\u6570\u662f\u5370\u5ea6\u76841.67\u500d\uff0c\u8bf4\u660e\u7f8e\u56fd\u5e02\u573a\u5728\u4fdd\u6301\u5ba2\u6237\u65b9\u9762\u8868\u73b0\u66f4\u597d\u3002","sub_key":115,"data":{"title":{"text":"\u6570\u636e\u8be6\u60c5"},"config":{"XAxis":[{"name":"\u56fd\u5bb6\u5730\u533a(\u5ba2\u6237)","field":"company.country"}],"YAxis":[{"name":"\u590d\u8d2d\u5ba2\u6237\u6570","field":"row-company.repeat_customers_list"},{"name":"\u590d\u8d2d\u5ba2\u6237\u5360\u6bd4","field":"formula-company.repeat_customer_rate","unit":"percent"}],"charType":[{"label":"\u8868\u683c","value":"table"}],"chatFlag":true},"content":[{"company.country":"\u672a\u77e5","row-company.repeat_customers_list":0,"formula-company.repeat_customer_rate":0},{"company.country":"\u4f2f\u5229\u5179","row-company.repeat_customers_list":0,"formula-company.repeat_customer_rate":0}]},"report_detail_data":[],"richness":0,"report":{"params":[{"type":"select","field":"common.select_regional_scope","value":3},{"type":"date","field":"company.archive_time","value":{"end":"2024-05-07","start":"2023-11-09"}}],"report_key":"kh21","name":"\u5ba2\u6237\u590d\u8d2d\u60c5\u51b5\uff08\u57fa\u4e8e\u9500\u552e\u8ba2\u5355\uff09"},"data_type":0,"analysis_record_id":3515218224},{"title":"\u590d\u8d2d\u5468\u671f\u5206\u5e03","conclusion":"\u5370\u5ea6\u7684\u590d\u8d2d\u5468\u671f\u6700\u77ed\uff0c\u4e3a5\u5929\uff0c\u53ef\u80fd\u8868\u660e\u5370\u5ea6\u5ba2\u6237\u7684\u8d2d\u4e70\u9891\u7387\u8f83\u9ad8\u3002\u5fb7\u56fd\u7684\u590d\u8d2d\u5468\u671f\u4e3a30\u5929\uff0c\u590d\u8d2d\u5ba2\u6237\u6570\u6700\u591a\uff0c\u4e3a3\u4eba\uff0c\u8bf4\u660e\u5fb7\u56fd\u5e02\u573a\u7684\u5ba2\u6237\u5728\u4e00\u4e2a\u8f83\u56fa\u5b9a\u7684\u5468\u671f\u5185\u8fdb\u884c\u590d\u8d2d\u3002\u7f8e\u56fd\u7684\u590d\u8d2d\u5468\u671f\u8f83\u957f\uff0c\u5e73\u5747\u4e3a79.5\u5929\uff0c\u4f46\u4e5f\u67092\u540d\u590d\u8d2d\u5ba2\u6237\uff0c\u53ef\u80fd\u610f\u5473\u7740\u7f8e\u56fd\u5e02\u573a\u7684\u5ba2\u6237\u5fe0\u8bda\u5ea6\u8f83\u9ad8\uff0c\u4f46\u8d2d\u4e70\u9891\u7387\u8f83\u4f4e\u3002","sub_key":116,"data":{"title":{"text":"\u6570\u636e\u8be6\u60c5"},"config":{"XAxis":[{"name":"\u56fd\u5bb6\u5730\u533a(\u5ba2\u6237)","field":"company.country"}],"YAxis":[{"name":"\u590d\u8d2d\u5ba2\u6237\u6570","field":"row-company.repeat_customers_list"},{"name":"\u590d\u8d2d\u5468\u671f\uff08\u5929\uff09","field":"formula-company.avg_repurchase_cycle"}],"charType":[{"label":"\u8868\u683c","value":"table"}],"chatFlag":true},"content":[{"company.country":"\u672a\u77e5","row-company.repeat_customers_list":0,"formula-company.avg_repurchase_cycle":0},{"company.country":"\u4f2f\u5229\u5179","row-company.repeat_customers_list":0,"formula-company.avg_repurchase_cycle":0}]},"report_detail_data":[],"richness":0,"report":{"params":[{"type":"select","field":"common.select_regional_scope","value":3},{"type":"date","field":"company.archive_time","value":{"end":"2024-05-07","start":"2023-11-09"}}],"report_key":"kh21","name":"\u5ba2\u6237\u590d\u8d2d\u60c5\u51b5\uff08\u57fa\u4e8e\u9500\u552e\u8ba2\u5355\uff09"},"data_type":0,"analysis_record_id":3515218225}]}],"process_record_ids":[]}],"finish_time":"1970-01-01 00:00:01","scope":{"crm.mail.view":{"scope":2},"crm.performance.view":{"scope":1},"crm.ai.lead.create":{"scope":2},"crm.ai.user":{"scope":2},"crm.cashCollection.setting":{"scope":2},"crm.cashCollectionInvoice.setting":{"scope":2},"crm.company.pool.assign":{"scope":2},"crm.company.pool.create":{"scope":2},"crm.company.pool.edit":{"scope":2},"crm.company.pool.hold":{"scope":2},"crm.company.pool.remove":{"scope":2},"crm.company.pool.view":{"scope":2},"crm.document.view":{"scope":2},"crm.email.approval.unlock":{"scope":2},"crm.lead.private.create":{"scope":2},"crm.lead.private.merge":{"scope":2},"crm.lead.private.new.transfer":{"scope":2},"crm.lead.private.report":{"scope":2},"crm.lead.private.transfer":{"scope":2},"crm.lead.private.view":{"scope":2},"crm.mail.export":{"scope":2},"crm.mail.hard.remove":{"scope":2},"crm.opportunity.approval.unlock":{"scope":2},"crm.opportunity.archive":{"scope":2},"crm.opportunity.create":{"scope":2},"crm.opportunity.edit":{"scope":2},"crm.opportunity.export":{"scope":2},"crm.opportunity.member.manage":{"scope":2},"crm.opportunity.remove":{"scope":2},"crm.opportunity.trail.create":{"scope":2},"crm.opportunity.transfer":{"scope":2},"crm.opportunity.view":{"scope":2},"crm.order.approval.unlock":{"scope":2},"crm.order.batch_change_handler":{"scope":2},"crm.order.batch_change_status":{"scope":2},"crm.order.batch_import":{"scope":2},"crm.order.calculate_gross_profit":{"scope":2},"crm.order.confirm.edit":{"scope":2},"crm.order.create":{"scope":2},"crm.order.edit":{"scope":2},"crm.order.edit.no.create":{"scope":2},"crm.order.edit.no.edit":{"scope":2},"crm.order.export":{"scope":2},"crm.order.generate.outbound":{"scope":2},"crm.order.link.cancel_complete":{"scope":2},"crm.order.link.finish":{"scope":2},"crm.order.link.ready_complete":{"scope":2},"crm.order.link.shipment_finish":{"scope":2},"crm.order.product.batch_import":{"scope":2},"crm.order.remove":{"scope":2},"crm.order.to_invoice_task":{"scope":2},"crm.order.to_outbound_task":{"scope":2},"crm.order.to_purchase":{"scope":2},"crm.order.to_purchase_task":{"scope":2},"crm.order.view":{"scope":2},"crm.performance.create":{"scope":2},"crm.performance.setting":{"scope":2},"crm.performance.team.wall":{"scope":2},"crm.platform.product.delete":{"scope":2},"crm.platform.product.match":{"scope":2},"crm.platform.product.view":{"scope":2},"crm.product.create":{"scope":2},"crm.product.delete":{"scope":2},"crm.product.edit":{"scope":2},"crm.product.edit.no.create":{"scope":2},"crm.product.edit.no.update":{"scope":2},"crm.product.export":{"scope":2},"crm.product.view":{"scope":2},"crm.setting.acp.manage":{"scope":2},"crm.setting.alibaba.store.manage":{"scope":2},"crm.setting.approvalflow.manage":{"scope":2},"crm.setting.capital_account.manage":{"scope":2},"crm.setting.contacts.manage":{"scope":2},"crm.setting.cost.invoice.manage":{"scope":2},"crm.setting.customer.client.tag.manage":{"scope":2},"crm.setting.customer.export":{"scope":2},"crm.setting.customer.manage":{"scope":2},"crm.setting.disk.manage":{"scope":2},"crm.setting.edm":{"scope":2},"crm.setting.email.manage":{"scope":2},"crm.setting.email.review.specify":{"scope":2},"crm.setting.email.template":{"scope":2},"crm.setting.exchange.manage":{"scope":2},"crm.setting.fields.manage":{"scope":2},"crm.setting.forwarder.manage":{"scope":2},"crm.setting.fund.manage":{"scope":2},"crm.setting.inventory.init.manage":{"scope":2},"crm.setting.inventory.reason.manage":{"scope":2},"crm.setting.invoice.export.template":{"scope":2},"crm.setting.lead.manage":{"scope":2},"crm.setting.mail.send.college.customer":{"scope":2},"crm.setting.mail.send.stranger":{"scope":2},"crm.setting.module.manage":{"scope":2},"crm.setting.opportunity.manage":{"scope":2},"crm.setting.order.export":{"scope":2},"crm.setting.order.manage":{"scope":2},"crm.setting.order_profit.manage":{"scope":2},"crm.setting.origin.manage":{"scope":2},"crm.setting.other.inbound.manage":{"scope":2},"crm.setting.other.outbound.manage":{"scope":2},"crm.setting.payment.invoice.manage":{"scope":2},"crm.setting.paypal.manage":{"scope":2},"crm.setting.privilege.manage":{"scope":2},"crm.setting.product.manage":{"scope":2},"crm.setting.purchase.inbound.manage":{"scope":2},"crm.setting.purchase.order.manage":{"scope":2},"crm.setting.purchase.return.manage":{"scope":2},"crm.setting.purchase.supplier.manage":{"scope":2},"crm.setting.quotation.manage":{"scope":2},"crm.setting.recycle.manage":{"scope":2},"crm.setting.sales.guide":{"scope":2},"crm.setting.sales.outbound.manage":{"scope":2},"crm.setting.shipping.invoice.manage":{"scope":2},"crm.setting.statistic.manage":{"scope":2},"crm.setting.task.manage":{"scope":2},"crm.setting.user.manage":{"scope":2},"crm.setting.warehouse.manage":{"scope":2},"crm.setting.workflow.manage":{"scope":2},"crm.speechcraft.view":{"scope":2},"crm.statistics.export":{"scope":2},"crm.statistics.view":{"scope":2},"crm.transport.view":{"scope":2},"crm.work.journal.template.setting":{"scope":2},"crm.work.journal.view":{"scope":2}}}
            ';
        }elseif ($type == \common\library\ai_agent\AssetAnalysisAiAgent::NOTICE_TYPE_FAIL_LESS_KCOIN) {
            $templateData = '
            {"task_id":3515015428,"title":"AI\u5BA2\u6237\u5206\u6790\u62A5\u544A\u751F\u6210\u5931\u8D25","desc":"\u672C\u6B21\u62A5\u544A\u5206\u6790\u6D88\u80173\u4E2AK\u5E01\uFF0C\u5F53\u524D\u60A8\u5269\u4F59K\u5E01\u6570\u91CF\u4E0D\u8DB3\uFF0C\u8BF7\u5206\u914D\u66F4\u591AK\u5E01","is_admin":true,"status":3,"echo_params":[],"list":[],"finish_time":"2024-05-06 15:32:08","scope":{"crm.admin":{"scope":4},"crm.user":{"scope":4},"crm.call.subordinate":{"scope":4},"crm.secondary.opportunity.view":{"scope":4},"crm.secondary.opportunity.trail.create":{"scope":4},"crm.secondary.opportunity.edit":{"scope":4},"crm.dx.admin":{"scope":4},"mkt.google.ads.view":{"scope":4},"mkt.google.ads.export":{"scope":4},"mkt.setting.google.ads.account":{"scope":4},"crm.ai.user":{"scope":4},"crm.ai.lead.create":{"scope":4},"crm.setting.email.manage":{"scope":4},"crm.setting.email.template":{"scope":4},"crm.setting.customer.manage":{"scope":4},"crm.setting.lead.manage":{"scope":4},"crm.setting.duplicate.manage":{"scope":4},"crm.setting.customer.client.tag.manage":{"scope":4},"crm.setting.customer.export":{"scope":4},"crm.setting.product.manage":{"scope":4},"crm.setting.quotation.manage":{"scope":4},"crm.setting.order.manage":{"scope":4},"crm.setting.customs.manage":{"scope":4},"crm.setting.edm":{"scope":4},"crm.setting.exchange.manage":{"scope":4},"crm.setting.statistic.manage":{"scope":4},"crm.setting.contacts.manage":{"scope":4},"crm.setting.email.review.specify":{"scope":4},"crm.setting.sales.guide":{"scope":4},"crm.setting.origin.manage":{"scope":4},"crm.setting.module.manage":{"scope":4},"crm.setting.fields.manage":{"scope":4},"crm.setting.opportunity.manage":{"scope":4},"crm.setting.assess.dimension.manage":{"scope":4},"crm.setting.paypal.manage":{"scope":4},"crm.setting.acp.manage":{"scope":4},"crm.setting.privilege.manage":{"scope":4},"crm.setting.user.manage":{"scope":4},"crm.setting.account.info":{"scope":4},"crm.setting.disk.manage":{"scope":4},"crm.ai.setting":{"scope":4},"crm.setting.workflow.manage":{"scope":4},"crm.setting.approvalflow.manage":{"scope":4},"crm.setting.recycle.manage":{"scope":4},"crm.setting.alibaba.store.manage":{"scope":4},"crm.setting.wecom.corp.manage":{"scope":4},"crm.setting.purchase.order.manage":{"scope":4},"crm.setting.purchase.supplier.manage":{"scope":4},"crm.setting.mail.send.stranger":{"scope":4},"crm.setting.mail.send.supplier.contact":{"scope":4},"crm.setting.mail.send.forwarder.contact":{"scope":4},"crm.setting.mail.send.private.contact":{"scope":4},"crm.setting.mail.send.college.customer":{"scope":4},"crm.setting.task.manage":{"scope":4},"crm.setting.purchase.inbound.manage":{"scope":4},"crm.setting.sales.outbound.manage":{"scope":4},"crm.setting.other.inbound.manage":{"scope":4},"crm.setting.other.outbound.manage":{"scope":4},"crm.setting.purchase.return.manage":{"scope":4},"crm.setting.inventory.init.manage":{"scope":4},"crm.setting.inventory.reason.manage":{"scope":4},"crm.cashCollection.setting":{"scope":4},"crm.cashCollectionInvoice.setting":{"scope":4},"crm.setting.payment.invoice.manage":{"scope":4},"crm.setting.cost.invoice.manage":{"scope":4},"crm.setting.capital_account.manage":{"scope":4},"crm.setting.warehouse.manage":{"scope":4},"crm.setting.invoice.export.template":{"scope":4},"crm.setting.fund.manage":{"scope":4},"crm.setting.order_profit.manage":{"scope":4},"crm.setting.shipping.invoice.manage":{"scope":4},"crm.setting.forwarder.manage":{"scope":4},"crm.business.card.rw":{"scope":4},"crm.business.card.scan":{"scope":4},"crm.business.card.transfer":{"scope":4},"crm.business.card.archive":{"scope":4},"crm.cashCollection.view":{"scope":4},"crm.cashCollection.create":{"scope":4},"crm.cashCollection.relate":{"scope":4},"crm.cashCollection.relate.cancel":{"scope":4},"crm.cashCollection.status.change":{"scope":4},"crm.cashCollection.remove":{"scope":4},"crm.cashCollection.download":{"scope":4},"crm.cashCollectionInvoice.view":{"scope":4},"crm.cashCollectionInvoice.create":{"scope":4},"crm.cashCollectionInvoice.edit":{"scope":4},"crm.cashCollectionInvoice.review":{"scope":4},"crm.cashCollectionInvoice.confirm_receive":{"scope":4},"crm.cashCollectionInvoice.cancel_receive":{"scope":4},"crm.cashCollectionInvoice.apportion":{"scope":4},"crm.cashCollectionInvoice.remove":{"scope":4},"crm.cashCollectionInvoice.download":{"scope":4},"crm.cashCollectionInvoice.approval.unlock":{"scope":4},"crm.cashCollectionInvoice.update_apportion":{"scope":4},"crm.cashCollectionInvoice.cancel_apportion":{"scope":4},"crm.company.pool.view":{"scope":4},"crm.company.pool.create":{"scope":4},"crm.company.pool.edit":{"scope":4},"crm.company.pool.remove":{"scope":4},"crm.company.pool.hold":{"scope":4},"crm.company.pool.assign":{"scope":4},"crm.company.pool.common.company.pool":{"scope":4},"crm.costInvoice.view":{"scope":4},"crm.costInvoice.create":{"scope":4},"crm.costInvoice.edit":{"scope":4},"crm.costInvoice.manage.user":{"scope":4},"crm.costInvoice.review":{"scope":4},"crm.costInvoice.remove":{"scope":4},"crm.costInvoice.download":{"scope":4},"crm.costInvoice.batch_import":{"scope":4},"crm.costInvoice.approval.unlock":{"scope":4},"crm.costInvoice.payment.close":{"scope":4},"crm.costInvoice.payment.continue":{"scope":4},"crm.company.private.view":{"scope":4},"crm.company.private.create":{"scope":4},"crm.company.private.edit":{"scope":4},"crm.company.private.transfer":{"scope":4},"crm.company.private.assign":{"scope":4},"crm.company.private.release.specify.user":{"scope":4},"crm.company.private.release":{"scope":4},"crm.company.private.share":{"scope":4},"crm.company.private.move.pool":{"scope":4},"crm.company.private.merge":{"scope":4},"crm.company.assess.first":{"scope":4},"crm.company.assess.second":{"scope":4},"crm.company.common.company.pool":{"scope":4},"crm.customs.view":{"scope":4},"crm.document.view":{"scope":4},"crm.document.create":{"scope":4},"crm.document.edit":{"scope":4},"crm.document.remove":{"scope":4},"crm.dx.user":{"scope":4},"crm.dx.lead.create":{"scope":4},"crm.followup.view":{"scope":4},"crm.forwarder.view":{"scope":4},"crm.forwarder.create":{"scope":4},"crm.forwarder.edit":{"scope":4},"crm.forwarder.remove":{"scope":4},"crm.forwarder.download":{"scope":4},"crm.inventory.flow.view":{"scope":4},"crm.inventory.flow.export":{"scope":4},"crm.inventory.query.view":{"scope":4},"crm.inventory.query.download":{"scope":4},"crm.lead.private.view":{"scope":4},"crm.lead.private.create":{"scope":4},"crm.lead.private.remove":{"scope":4},"crm.lead.private.edit":{"scope":4},"crm.lead.private.transfer":{"scope":4},"crm.lead.private.new.transfer":{"scope":4},"crm.lead.private.transform":{"scope":4},"crm.lead.private.report":{"scope":4},"crm.lead.private.merge":{"scope":4},"crm.lead.private.assess.first":{"scope":4},"crm.lead.private.assess.second":{"scope":4},"crm.mail.view":{"scope":4},"crm.mail.hard.remove":{"scope":4},"crm.email.approval.unlock":{"scope":4},"crm.mail.export":{"scope":4},"crm.edm.view":{"scope":4},"crm.edm.auto":{"scope":4},"crm.edm.export":{"scope":4},"crm.waba.marketing":{"scope":4},"crm.whatsapp.marketing":{"scope":4},"crm.auto.marketing.view":{"scope":4},"crm.auto.marketing.create":{"scope":4},"crm.auto.marketing.delete":{"scope":4},"crm.opportunity.view":{"scope":4},"crm.opportunity.create":{"scope":4},"crm.opportunity.remove":{"scope":4},"crm.opportunity.edit":{"scope":4},"crm.opportunity.transfer":{"scope":4},"crm.opportunity.member.manage":{"scope":4},"crm.opportunity.trail.create":{"scope":4},"crm.opportunity.export":{"scope":4},"crm.opportunity.approval.unlock":{"scope":4},"crm.opportunity.archive":{"scope":4},"crm.order.view":{"scope":4},"crm.order.create":{"scope":4},"crm.order.edit":{"scope":4},"crm.order.confirm.edit":{"scope":4},"crm.order.export":{"scope":4},"crm.order.remove":{"scope":4},"crm.order.edit.no.create":{"scope":4},"crm.order.edit.no.edit":{"scope":4},"crm.setting.order.export":{"scope":4},"crm.order.approval.unlock":{"scope":4},"crm.order.to_purchase":{"scope":4},"crm.order.generate.outbound":{"scope":4},"crm.order.link.ready_complete":{"scope":4},"crm.order.link.shipment_finish":{"scope":4},"crm.order.link.finish":{"scope":4},"crm.order.link.cancel_complete":{"scope":4},"crm.order.calculate_gross_profit":{"scope":4},"crm.order.batch_change_status":{"scope":4},"crm.order.batch_change_handler":{"scope":4},"crm.order.product.batch_import":{"scope":4},"crm.order.batch_import":{"scope":4},"crm.order.to_purchase_task":{"scope":4},"crm.order.to_outbound_task":{"scope":4},"crm.order.to_invoice_task":{"scope":4},"crm.order.profit.view":{"scope":4},"crm.order.profit.export":{"scope":4},"crm.order.profit.edit_tax_refund":{"scope":4},"crm.order.profit.import_tax_refund":{"scope":4},"crm.other.inbound.view":{"scope":4},"crm.other.inbound.create":{"scope":4},"crm.other.inbound.remove":{"scope":4},"crm.other.inbound.edit":{"scope":4},"crm.other.inbound.confirm":{"scope":4},"crm.other.inbound.print":{"scope":4},"crm.other.outbound.view":{"scope":4},"crm.other.outbound.create":{"scope":4},"crm.other.outbound.remove":{"scope":4},"crm.other.outbound.edit":{"scope":4},"crm.other.outbound.confirm":{"scope":4},"crm.other.outbound.print":{"scope":4},"crm.payment_invoice.view":{"scope":4},"crm.payment_invoice.create":{"scope":4},"crm.payment_invoice.edit":{"scope":4},"crm.payment_invoice.manage.user":{"scope":4},"crm.payment_invoice.confirm":{"scope":4},"crm.payment_invoice.export":{"scope":4},"crm.payment_invoice.disable":{"scope":4},"crm.payment_invoice.delete":{"scope":4},"crm.payment_invoice.review":{"scope":4},"crm.payment_invoice.approval.unlock":{"scope":4},"crm.performance.view":{"scope":4},"crm.performance.create":{"scope":4},"crm.performance.team.wall":{"scope":4},"crm.performance.setting":{"scope":4},"crm.platform.product.view":{"scope":4},"crm.platform.product.match":{"scope":4},"crm.platform.product.delete":{"scope":4},"crm.product.view":{"scope":4},"crm.product.create":{"scope":4},"crm.product.edit":{"scope":4},"crm.product.delete":{"scope":4},"crm.product.edit.no.create":{"scope":4},"crm.product.edit.no.update":{"scope":4},"crm.product.export":{"scope":4},"crm.purchase.inbound.view":{"scope":4},"crm.purchase.inbound.create":{"scope":4},"crm.purchase.inbound.remove":{"scope":4},"crm.purchase.inbound.edit":{"scope":4},"crm.purchase.inbound.confirm":{"scope":4},"crm.purchase.inbound.print":{"scope":4},"crm.purchase.order.view":{"scope":4},"crm.purchase.order.create":{"scope":4},"crm.purchase.order.remove":{"scope":4},"crm.purchase.order.edit":{"scope":4},"crm.purchase.order.change":{"scope":4},"crm.purchase.order.export":{"scope":4},"crm.purchase.order.approval.unlock":{"scope":4},"crm.purchase.order.download":{"scope":4},"crm.purchase.order.generate.inbound":{"scope":4},"crm.purchase.order.payment.close":{"scope":4},"crm.purchase.order.payment.continue":{"scope":4},"crm.purchase.order.batch_set_status":{"scope":4},"crm.purchase.order.batch_change_handler":{"scope":4},"crm.purchase.return.view":{"scope":4},"crm.purchase.return.create":{"scope":4},"crm.purchase.return.remove":{"scope":4},"crm.purchase.return.edit":{"scope":4},"crm.purchase.return.confirm":{"scope":4},"crm.purchase.return.print":{"scope":4},"crm.supplier.view":{"scope":4},"crm.supplier.create":{"scope":4},"crm.supplier.remove":{"scope":4},"crm.supplier.edit":{"scope":4},"crm.supplier.export":{"scope":4},"crm.supplier.product.view":{"scope":4},"crm.supplier.product.create":{"scope":4},"crm.supplier.product.edit":{"scope":4},"crm.supplier.product.remove":{"scope":4},"crm.supplier.product.export":{"scope":4},"crm.supplier.product.import":{"scope":4},"crm.purchase_by_order.view":{"scope":4},"crm.purchase_by_order.create":{"scope":4},"crm.quotation.view":{"scope":4},"crm.quotation.create":{"scope":4},"crm.quotation.edit":{"scope":4},"crm.quotation.remove":{"scope":4},"crm.setting.quotation.export":{"scope":4},"crm.quotation.approval.unlock":{"scope":4},"crm.sales.outbound.view":{"scope":4},"crm.sales.outbound.create":{"scope":4},"crm.sales.outbound.remove":{"scope":4},"crm.sales.outbound.edit":{"scope":4},"crm.sales.outbound.confirm":{"scope":4},"crm.sales.outbound.print":{"scope":4},"crm.sales.outbound.approval.unlock":{"scope":4},"crm.shipping_invoice.create":{"scope":4},"crm.shipping_invoice.view":{"scope":4},"crm.shipping_invoice.edit":{"scope":4},"crm.shipping_invoice.set_status":{"scope":4},"crm.shipping_invoice.change_handler":{"scope":4},"crm.shipping_invoice.export.template":{"scope":4},"crm.shipping_invoice.disable":{"scope":4},"crm.shipping_invoice.delete":{"scope":4},"crm.shipping_invoice.outbound.generate":{"scope":4},"crm.shipping_invoice.approval.unlock":{"scope":4},"crm.shipping_invoice.edit_declaration_record":{"scope":4},"crm.shipping_invoice.edit_packing_record":{"scope":4},"crm.facebook.view":{"scope":4},"crm.facebook.post.view":{"scope":4},"crm.facebook.lead.view":{"scope":4},"crm.waba.view":{"scope":4},"crm.waba.account.create":{"scope":4},"crm.socialmedia.view":{"scope":4},"crm.socialmedia.edit":{"scope":4},"crm.socialmedia.remove":{"scope":4},"crm.socialmedia.assign":{"scope":4},"crm.waba.manage.view":{"scope":4},"crm.waba.manage.template":{"scope":4},"crm.waba.manage.phone.create":{"scope":4},"crm.waba.manage.phone.allot":{"scope":4},"crm.waba.manage.marketing":{"scope":4},"crm.waba.manage.account.unbind":{"scope":4},"crm.instagram.account.assign":{"scope":4},"crm.instagram.account.create":{"scope":4},"crm.instagram.view":{"scope":4},"crm.instagram.account.unbind":{"scope":4},"crm.whatsapp.customer.unbind":{"scope":4},"crm.speechcraft.view":{"scope":4},"crm.speechcraft.create":{"scope":4},"crm.speechcraft.edit":{"scope":4},"crm.speechcraft.remove":{"scope":4},"crm.speechcraft.subordinate":{"scope":4},"crm.statistics.view":{"scope":4},"crm.statistics.export":{"scope":4},"crm.suggestion.edit":{"scope":4},"crm.transport.view":{"scope":4},"crm.work.journal.view":{"scope":4},"crm.work.journal.template.setting":{"scope":4},"crm.work.journal.edit":{"scope":4},"crm.work.journal.delete":{"scope":4},"mkt.site.statistic.view":{"scope":4},"mkt.site.statistic.export":{"scope":4},"mkt.site.monitor.view":{"scope":4},"mkt.setting.site.track":{"scope":4},"mkt.setting.role.manage":{"scope":4},"mkt.conversation.history.view":{"scope":4},"mkt.conversation.setting.view":{"scope":4},"mkt.conversation.setting.create":{"scope":4},"mkt.conversation.setting.edit":{"scope":4},"mkt.conversation.setting.delete":{"scope":4},"mkt.setting.custom.form.setting":{"scope":4},"mkt.setting.i18n":{"scope":4},"mkt.inquiry.view":{"scope":4},"mkt.inquiry.delete":{"scope":4},"mkt.inquiry.report.view":{"scope":4},"mkt.inquiry.report.export":{"scope":4},"mkt.site.manage.notification":{"scope":4},"mkt.inquiry.recycle":{"scope":4}}}
            ';
        }elseif ($type == \common\library\ai_agent\AssetAnalysisAiAgent::NOTICE_TYPE_FAIL_LESS_DATA) {
            $templateData = '
            {"task_id":3515015428,"title":"AI\u5ba2\u6237\u5206\u6790\u62a5\u544a\u751f\u6210\u5931\u8d25","desc":"\u7531\u4e8e\u6570\u636e\u91cf\u4e0d\u8db3\u5bfc\u81f4\u672c\u6b21\u5206\u6790\u5931\u8d25\uff08\u672c\u6b21\u672a\u6d88\u8017K\u5e01\uff09","is_admin":true,"status":2,"echo_params":[],"list":[],"finish_time":"2024-05-06 15:32:08","scope":{"crm.admin":{"scope":4},"crm.user":{"scope":4},"crm.call.subordinate":{"scope":4},"crm.secondary.opportunity.view":{"scope":4},"crm.secondary.opportunity.trail.create":{"scope":4},"crm.secondary.opportunity.edit":{"scope":4},"crm.dx.admin":{"scope":4},"mkt.google.ads.view":{"scope":4},"mkt.google.ads.export":{"scope":4},"mkt.setting.google.ads.account":{"scope":4},"crm.ai.user":{"scope":4},"crm.ai.lead.create":{"scope":4},"crm.setting.email.manage":{"scope":4},"crm.setting.email.template":{"scope":4},"crm.setting.customer.manage":{"scope":4},"crm.setting.lead.manage":{"scope":4},"crm.setting.duplicate.manage":{"scope":4},"crm.setting.customer.client.tag.manage":{"scope":4},"crm.setting.customer.export":{"scope":4},"crm.setting.product.manage":{"scope":4},"crm.setting.quotation.manage":{"scope":4},"crm.setting.order.manage":{"scope":4},"crm.setting.customs.manage":{"scope":4},"crm.setting.edm":{"scope":4},"crm.setting.exchange.manage":{"scope":4},"crm.setting.statistic.manage":{"scope":4},"crm.setting.contacts.manage":{"scope":4},"crm.setting.email.review.specify":{"scope":4},"crm.setting.sales.guide":{"scope":4},"crm.setting.origin.manage":{"scope":4},"crm.setting.module.manage":{"scope":4},"crm.setting.fields.manage":{"scope":4},"crm.setting.opportunity.manage":{"scope":4},"crm.setting.assess.dimension.manage":{"scope":4},"crm.setting.paypal.manage":{"scope":4},"crm.setting.acp.manage":{"scope":4},"crm.setting.privilege.manage":{"scope":4},"crm.setting.user.manage":{"scope":4},"crm.setting.account.info":{"scope":4},"crm.setting.disk.manage":{"scope":4},"crm.ai.setting":{"scope":4},"crm.setting.workflow.manage":{"scope":4},"crm.setting.approvalflow.manage":{"scope":4},"crm.setting.recycle.manage":{"scope":4},"crm.setting.alibaba.store.manage":{"scope":4},"crm.setting.wecom.corp.manage":{"scope":4},"crm.setting.purchase.order.manage":{"scope":4},"crm.setting.purchase.supplier.manage":{"scope":4},"crm.setting.mail.send.stranger":{"scope":4},"crm.setting.mail.send.supplier.contact":{"scope":4},"crm.setting.mail.send.forwarder.contact":{"scope":4},"crm.setting.mail.send.private.contact":{"scope":4},"crm.setting.mail.send.college.customer":{"scope":4},"crm.setting.task.manage":{"scope":4},"crm.setting.purchase.inbound.manage":{"scope":4},"crm.setting.sales.outbound.manage":{"scope":4},"crm.setting.other.inbound.manage":{"scope":4},"crm.setting.other.outbound.manage":{"scope":4},"crm.setting.purchase.return.manage":{"scope":4},"crm.setting.inventory.init.manage":{"scope":4},"crm.setting.inventory.reason.manage":{"scope":4},"crm.cashCollection.setting":{"scope":4},"crm.cashCollectionInvoice.setting":{"scope":4},"crm.setting.payment.invoice.manage":{"scope":4},"crm.setting.cost.invoice.manage":{"scope":4},"crm.setting.capital_account.manage":{"scope":4},"crm.setting.warehouse.manage":{"scope":4},"crm.setting.invoice.export.template":{"scope":4},"crm.setting.fund.manage":{"scope":4},"crm.setting.order_profit.manage":{"scope":4},"crm.setting.shipping.invoice.manage":{"scope":4},"crm.setting.forwarder.manage":{"scope":4},"crm.business.card.rw":{"scope":4},"crm.business.card.scan":{"scope":4},"crm.business.card.transfer":{"scope":4},"crm.business.card.archive":{"scope":4},"crm.cashCollection.view":{"scope":4},"crm.cashCollection.create":{"scope":4},"crm.cashCollection.relate":{"scope":4},"crm.cashCollection.relate.cancel":{"scope":4},"crm.cashCollection.status.change":{"scope":4},"crm.cashCollection.remove":{"scope":4},"crm.cashCollection.download":{"scope":4},"crm.cashCollectionInvoice.view":{"scope":4},"crm.cashCollectionInvoice.create":{"scope":4},"crm.cashCollectionInvoice.edit":{"scope":4},"crm.cashCollectionInvoice.review":{"scope":4},"crm.cashCollectionInvoice.confirm_receive":{"scope":4},"crm.cashCollectionInvoice.cancel_receive":{"scope":4},"crm.cashCollectionInvoice.apportion":{"scope":4},"crm.cashCollectionInvoice.remove":{"scope":4},"crm.cashCollectionInvoice.download":{"scope":4},"crm.cashCollectionInvoice.approval.unlock":{"scope":4},"crm.cashCollectionInvoice.update_apportion":{"scope":4},"crm.cashCollectionInvoice.cancel_apportion":{"scope":4},"crm.company.pool.view":{"scope":4},"crm.company.pool.create":{"scope":4},"crm.company.pool.edit":{"scope":4},"crm.company.pool.remove":{"scope":4},"crm.company.pool.hold":{"scope":4},"crm.company.pool.assign":{"scope":4},"crm.company.pool.common.company.pool":{"scope":4},"crm.costInvoice.view":{"scope":4},"crm.costInvoice.create":{"scope":4},"crm.costInvoice.edit":{"scope":4},"crm.costInvoice.manage.user":{"scope":4},"crm.costInvoice.review":{"scope":4},"crm.costInvoice.remove":{"scope":4},"crm.costInvoice.download":{"scope":4},"crm.costInvoice.batch_import":{"scope":4},"crm.costInvoice.approval.unlock":{"scope":4},"crm.costInvoice.payment.close":{"scope":4},"crm.costInvoice.payment.continue":{"scope":4},"crm.company.private.view":{"scope":4},"crm.company.private.create":{"scope":4},"crm.company.private.edit":{"scope":4},"crm.company.private.transfer":{"scope":4},"crm.company.private.assign":{"scope":4},"crm.company.private.release.specify.user":{"scope":4},"crm.company.private.release":{"scope":4},"crm.company.private.share":{"scope":4},"crm.company.private.move.pool":{"scope":4},"crm.company.private.merge":{"scope":4},"crm.company.assess.first":{"scope":4},"crm.company.assess.second":{"scope":4},"crm.company.common.company.pool":{"scope":4},"crm.customs.view":{"scope":4},"crm.document.view":{"scope":4},"crm.document.create":{"scope":4},"crm.document.edit":{"scope":4},"crm.document.remove":{"scope":4},"crm.dx.user":{"scope":4},"crm.dx.lead.create":{"scope":4},"crm.followup.view":{"scope":4},"crm.forwarder.view":{"scope":4},"crm.forwarder.create":{"scope":4},"crm.forwarder.edit":{"scope":4},"crm.forwarder.remove":{"scope":4},"crm.forwarder.download":{"scope":4},"crm.inventory.flow.view":{"scope":4},"crm.inventory.flow.export":{"scope":4},"crm.inventory.query.view":{"scope":4},"crm.inventory.query.download":{"scope":4},"crm.lead.private.view":{"scope":4},"crm.lead.private.create":{"scope":4},"crm.lead.private.remove":{"scope":4},"crm.lead.private.edit":{"scope":4},"crm.lead.private.transfer":{"scope":4},"crm.lead.private.new.transfer":{"scope":4},"crm.lead.private.transform":{"scope":4},"crm.lead.private.report":{"scope":4},"crm.lead.private.merge":{"scope":4},"crm.lead.private.assess.first":{"scope":4},"crm.lead.private.assess.second":{"scope":4},"crm.mail.view":{"scope":4},"crm.mail.hard.remove":{"scope":4},"crm.email.approval.unlock":{"scope":4},"crm.mail.export":{"scope":4},"crm.edm.view":{"scope":4},"crm.edm.auto":{"scope":4},"crm.edm.export":{"scope":4},"crm.waba.marketing":{"scope":4},"crm.whatsapp.marketing":{"scope":4},"crm.auto.marketing.view":{"scope":4},"crm.auto.marketing.create":{"scope":4},"crm.auto.marketing.delete":{"scope":4},"crm.opportunity.view":{"scope":4},"crm.opportunity.create":{"scope":4},"crm.opportunity.remove":{"scope":4},"crm.opportunity.edit":{"scope":4},"crm.opportunity.transfer":{"scope":4},"crm.opportunity.member.manage":{"scope":4},"crm.opportunity.trail.create":{"scope":4},"crm.opportunity.export":{"scope":4},"crm.opportunity.approval.unlock":{"scope":4},"crm.opportunity.archive":{"scope":4},"crm.order.view":{"scope":4},"crm.order.create":{"scope":4},"crm.order.edit":{"scope":4},"crm.order.confirm.edit":{"scope":4},"crm.order.export":{"scope":4},"crm.order.remove":{"scope":4},"crm.order.edit.no.create":{"scope":4},"crm.order.edit.no.edit":{"scope":4},"crm.setting.order.export":{"scope":4},"crm.order.approval.unlock":{"scope":4},"crm.order.to_purchase":{"scope":4},"crm.order.generate.outbound":{"scope":4},"crm.order.link.ready_complete":{"scope":4},"crm.order.link.shipment_finish":{"scope":4},"crm.order.link.finish":{"scope":4},"crm.order.link.cancel_complete":{"scope":4},"crm.order.calculate_gross_profit":{"scope":4},"crm.order.batch_change_status":{"scope":4},"crm.order.batch_change_handler":{"scope":4},"crm.order.product.batch_import":{"scope":4},"crm.order.batch_import":{"scope":4},"crm.order.to_purchase_task":{"scope":4},"crm.order.to_outbound_task":{"scope":4},"crm.order.to_invoice_task":{"scope":4},"crm.order.profit.view":{"scope":4},"crm.order.profit.export":{"scope":4},"crm.order.profit.edit_tax_refund":{"scope":4},"crm.order.profit.import_tax_refund":{"scope":4},"crm.other.inbound.view":{"scope":4},"crm.other.inbound.create":{"scope":4},"crm.other.inbound.remove":{"scope":4},"crm.other.inbound.edit":{"scope":4},"crm.other.inbound.confirm":{"scope":4},"crm.other.inbound.print":{"scope":4},"crm.other.outbound.view":{"scope":4},"crm.other.outbound.create":{"scope":4},"crm.other.outbound.remove":{"scope":4},"crm.other.outbound.edit":{"scope":4},"crm.other.outbound.confirm":{"scope":4},"crm.other.outbound.print":{"scope":4},"crm.payment_invoice.view":{"scope":4},"crm.payment_invoice.create":{"scope":4},"crm.payment_invoice.edit":{"scope":4},"crm.payment_invoice.manage.user":{"scope":4},"crm.payment_invoice.confirm":{"scope":4},"crm.payment_invoice.export":{"scope":4},"crm.payment_invoice.disable":{"scope":4},"crm.payment_invoice.delete":{"scope":4},"crm.payment_invoice.review":{"scope":4},"crm.payment_invoice.approval.unlock":{"scope":4},"crm.performance.view":{"scope":4},"crm.performance.create":{"scope":4},"crm.performance.team.wall":{"scope":4},"crm.performance.setting":{"scope":4},"crm.platform.product.view":{"scope":4},"crm.platform.product.match":{"scope":4},"crm.platform.product.delete":{"scope":4},"crm.product.view":{"scope":4},"crm.product.create":{"scope":4},"crm.product.edit":{"scope":4},"crm.product.delete":{"scope":4},"crm.product.edit.no.create":{"scope":4},"crm.product.edit.no.update":{"scope":4},"crm.product.export":{"scope":4},"crm.purchase.inbound.view":{"scope":4},"crm.purchase.inbound.create":{"scope":4},"crm.purchase.inbound.remove":{"scope":4},"crm.purchase.inbound.edit":{"scope":4},"crm.purchase.inbound.confirm":{"scope":4},"crm.purchase.inbound.print":{"scope":4},"crm.purchase.order.view":{"scope":4},"crm.purchase.order.create":{"scope":4},"crm.purchase.order.remove":{"scope":4},"crm.purchase.order.edit":{"scope":4},"crm.purchase.order.change":{"scope":4},"crm.purchase.order.export":{"scope":4},"crm.purchase.order.approval.unlock":{"scope":4},"crm.purchase.order.download":{"scope":4},"crm.purchase.order.generate.inbound":{"scope":4},"crm.purchase.order.payment.close":{"scope":4},"crm.purchase.order.payment.continue":{"scope":4},"crm.purchase.order.batch_set_status":{"scope":4},"crm.purchase.order.batch_change_handler":{"scope":4},"crm.purchase.return.view":{"scope":4},"crm.purchase.return.create":{"scope":4},"crm.purchase.return.remove":{"scope":4},"crm.purchase.return.edit":{"scope":4},"crm.purchase.return.confirm":{"scope":4},"crm.purchase.return.print":{"scope":4},"crm.supplier.view":{"scope":4},"crm.supplier.create":{"scope":4},"crm.supplier.remove":{"scope":4},"crm.supplier.edit":{"scope":4},"crm.supplier.export":{"scope":4},"crm.supplier.product.view":{"scope":4},"crm.supplier.product.create":{"scope":4},"crm.supplier.product.edit":{"scope":4},"crm.supplier.product.remove":{"scope":4},"crm.supplier.product.export":{"scope":4},"crm.supplier.product.import":{"scope":4},"crm.purchase_by_order.view":{"scope":4},"crm.purchase_by_order.create":{"scope":4},"crm.quotation.view":{"scope":4},"crm.quotation.create":{"scope":4},"crm.quotation.edit":{"scope":4},"crm.quotation.remove":{"scope":4},"crm.setting.quotation.export":{"scope":4},"crm.quotation.approval.unlock":{"scope":4},"crm.sales.outbound.view":{"scope":4},"crm.sales.outbound.create":{"scope":4},"crm.sales.outbound.remove":{"scope":4},"crm.sales.outbound.edit":{"scope":4},"crm.sales.outbound.confirm":{"scope":4},"crm.sales.outbound.print":{"scope":4},"crm.sales.outbound.approval.unlock":{"scope":4},"crm.shipping_invoice.create":{"scope":4},"crm.shipping_invoice.view":{"scope":4},"crm.shipping_invoice.edit":{"scope":4},"crm.shipping_invoice.set_status":{"scope":4},"crm.shipping_invoice.change_handler":{"scope":4},"crm.shipping_invoice.export.template":{"scope":4},"crm.shipping_invoice.disable":{"scope":4},"crm.shipping_invoice.delete":{"scope":4},"crm.shipping_invoice.outbound.generate":{"scope":4},"crm.shipping_invoice.approval.unlock":{"scope":4},"crm.shipping_invoice.edit_declaration_record":{"scope":4},"crm.shipping_invoice.edit_packing_record":{"scope":4},"crm.facebook.view":{"scope":4},"crm.facebook.post.view":{"scope":4},"crm.facebook.lead.view":{"scope":4},"crm.waba.view":{"scope":4},"crm.waba.account.create":{"scope":4},"crm.socialmedia.view":{"scope":4},"crm.socialmedia.edit":{"scope":4},"crm.socialmedia.remove":{"scope":4},"crm.socialmedia.assign":{"scope":4},"crm.waba.manage.view":{"scope":4},"crm.waba.manage.template":{"scope":4},"crm.waba.manage.phone.create":{"scope":4},"crm.waba.manage.phone.allot":{"scope":4},"crm.waba.manage.marketing":{"scope":4},"crm.waba.manage.account.unbind":{"scope":4},"crm.instagram.account.assign":{"scope":4},"crm.instagram.account.create":{"scope":4},"crm.instagram.view":{"scope":4},"crm.instagram.account.unbind":{"scope":4},"crm.whatsapp.customer.unbind":{"scope":4},"crm.speechcraft.view":{"scope":4},"crm.speechcraft.create":{"scope":4},"crm.speechcraft.edit":{"scope":4},"crm.speechcraft.remove":{"scope":4},"crm.speechcraft.subordinate":{"scope":4},"crm.statistics.view":{"scope":4},"crm.statistics.export":{"scope":4},"crm.suggestion.edit":{"scope":4},"crm.transport.view":{"scope":4},"crm.work.journal.view":{"scope":4},"crm.work.journal.template.setting":{"scope":4},"crm.work.journal.edit":{"scope":4},"crm.work.journal.delete":{"scope":4},"mkt.site.statistic.view":{"scope":4},"mkt.site.statistic.export":{"scope":4},"mkt.site.monitor.view":{"scope":4},"mkt.setting.site.track":{"scope":4},"mkt.setting.role.manage":{"scope":4},"mkt.conversation.history.view":{"scope":4},"mkt.conversation.setting.view":{"scope":4},"mkt.conversation.setting.create":{"scope":4},"mkt.conversation.setting.edit":{"scope":4},"mkt.conversation.setting.delete":{"scope":4},"mkt.setting.custom.form.setting":{"scope":4},"mkt.setting.i18n":{"scope":4},"mkt.inquiry.view":{"scope":4},"mkt.inquiry.delete":{"scope":4},"mkt.inquiry.report.view":{"scope":4},"mkt.inquiry.report.export":{"scope":4},"mkt.site.manage.notification":{"scope":4},"mkt.inquiry.recycle":{"scope":4}}}
            ';
        }
        $email = '<EMAIL>';
        try {
            $templateData = json_decode($templateData, true);
            \common\library\ai_agent\Helper::sendEmail($email, $templateData, 'subscribe_statistic_report', 'get_asset_analysis_subscribe_template', 1);
//            $service = new \common\library\statistics\subscribe\PushSubscribe();
//            $service->send($email, $templateData);
            $this->success(['templateData' => $templateData]);
        }catch (Exception $exception) {
            $this->fail($exception->getCode(), $exception->getMessage());
        }

    }

    /**
     * 重置dtc upgrade
     * @param $client_id
     * @return void
     */
    public function actionResetDTCRollback($client_id)
    {
        $this->validate([
            'client_id' => 'required|integer'
        ]);

        $rollback = new \common\library\dtc\RollbackService($client_id);
        $rollback->rollback();
        $this->success();
    }

    public function actionRuleDailyWorkflowRule(int $client_id, int $rule_id = 0, string $date = '')
    {
        if (\Yii::app()->params['env'] == 'test') {
            \LogUtil::info("start workflow daily for client: $client_id");
            $db = PgActiveRecord::getDbByClientId($client_id);
            $rule_id && $db->createCommand("delete from tbl_workflow_log where rule_id = {$rule_id}")->execute();
            ProjectActiveRecord::setConnection(ProjectActiveRecord::getDbByClientId($client_id));
            PgActiveRecord::setConnection($db);
            $trigger = new \common\library\workflow\trigger\DailyTrigger($client_id);
            $trigger->setRuleIds($rule_id);
            $trigger->setUseDate($date);
            $trigger->run(false);
            $this->success("client_id={$client_id}执行每日工作流成功");
        } else {
            $this->success('暂不支持线上调用');
        }
    }

    public function actionWorkflowRollback(int $rule_id)
    {
        $this->validate([
            'rule_id' => 'integer|not_empty',
        ]);
        $user = User::getLoginUser();
        $userId = $user->getUserId();
        $clientId = $user->getClientId();
        $rule = new \common\library\workflow\WorkflowRule($clientId, $rule_id);

        if (!$rule->isExist()) {
            throw new RuntimeException(\Yii::t('workflow', "工作流不存在"));
        }

        $rule->setOpUserId($userId);
        $rule->rollback();

        return $this->success([]);
    }
    public function actionCreateOrderByOpportunity($opportunity_id)
    {
        $user = \User::getLoginUser();
        $opportunity = new \common\library\opportunity\Opportunity($user->getClientId(), $opportunity_id);
        if (!$opportunity->isExist()) {
            throw new RuntimeException('商机不存在');
        }
        $order_id = \common\library\invoice\Helper::createOrderByOpportunity($opportunity);
        return $this->success(['order_id' => $order_id]);
    }

    /**
     * 测试验证 ames_mobile
     * @return void
     */
    public function actionUpdateAmesMobile($user_id, $ames_mobile)
    {
        $user = User::getLoginUser();
        if (empty($user->getUserId()))
            $this->fail(ErrorCode::CODE_FAIL, '请先登录');
        $userInfo = new \common\library\account\UserInfo($user_id);
        $before = $userInfo->ames_mobile;
        $userInfo->ames_mobile = $ames_mobile;
        $userInfo->update(['ames_mobile']);

        \LogUtil::info('account' . $user->getEmail() . ' update user_id : ' . $user_id . ' amesmobile from ' . $before . ' to ' . $ames_mobile);
        $this->success();
    }

    /**
     * 重置client和user的脚本
     */

    public function actionResetClientAndUserKCoin($clientId = 0,$userIds = '') {

        $userIds = explode(",",$userIds);
        if (empty($userId)) {
            $userList = new UserList();
            $userList->setClientId($clientId);
            $userList->setFields(['user_id']);
            $list = $userList->find();
            $userIds = array_column($list,'user_id');
        }

        foreach ($userIds as $userId) {
            $userInfo = new \common\library\account\UserInfo($userId,$clientId);
            $userInfo->setExtentAttributes([
                UserInfoExternal::EXTERNAL_KEY_K_COIN_AMOUNT => 0
            ]);
            $userInfo->saveExtentAttributes();
        }

        if (! empty($clientId)) {
            $client = Client::getClient($clientId);
            $client->setExtentAttributes([
                Client::EXTERNAL_KEY_K_COIN_STANDARD_TOTAL_AMOUNT => 0,
                Client::EXTERNAL_KEY_K_COIN_STANDARD_AVAILABLE_AMOUNT => 0,
                Client::EXTERNAL_KEY_K_COIN_ADDITIONAL_AVAILABLE_AMOUNT => 0,
                Client::EXTERNAL_KEY_K_COIN_ASSIGNABLE_TOTAL_AMOUNT => 0,
            ]);
            $client->saveExtentAttributes();
        }
        $this->success();
    }


    /**
     * 运行流失预警任务
     */
    public function actionTestRunLostWarningStatistic(int $clientId, int $date)
    {
        (new \common\library\server\crontab\task\StatisticWorkTask())->customerLostWarningStatistic($clientId, $date);
        return $this->success();
    }

    /**
     * @param int $clientId
     * @param string $dsl
     * @return void
     */
    public function actionGetDslSql(int $clientId, string $dsl, int $userId = 0)
    {
        if ($userId == 0) {
            $userId = PrivilegeService::getInstance($clientId)->getAdminUserId();
        }

        User::setLoginUserById($userId);

        try {
            $dsl = json_decode($dsl, true);
            $dsl = $dsl['DSL'] ?? $dsl;
            $sqlBuilder = new \common\library\ai_agent\utils\DslSqlBuilder($clientId, $userId, $dsl);
            $sql = $sqlBuilder->buildSql();
        } catch (\Throwable $exception) {
            $this->success([
                'message' => "生成sql出错,{$exception->getMessage()}"
            ]);
            return ;
        }

        try {
            $generateDataAiAgent = new \common\library\ai_agent\GenerateDataVectorAiAgent($clientId,$userId);
            $generateDataAiAgent->dslSqlAnalyzer = new DslSqlAnalyzer($clientId, $userId, $dsl, $sql);
            $dslInfo = $generateDataAiAgent->analysisDsl($dsl);
            $data = $generateDataAiAgent->execDslSql($dsl,$sql);
        } catch (\Throwable $exception) {
            $this->success([
                'sql' => $sql,
                'message' => "生成执行sql出错,{$exception->getMessage()}"
            ]);
            return ;
        }
        $this->success([
            'sql' => $sql,
            'data' => $data
        ]);

    }


    public function actionGetDefaultAssetAnalysisList()
    {
        $this->success([
            'data' =>  \common\library\ai_agent\config\AssetAnalysisConfig::getDefaultAssetAnalysisList()
        ]);
    }


    public function actionUnLockSpaceRefresh(int $client_id)
    {
        $this->validate([
            'client_id' => 'required'
        ]);

        $redis = RedisService::getInstance('redis');
        $lockKey = \common\library\space\SpaceService::REDIS_LOCK_REFRESH_PREFIX . $client_id;

        $redis->del([$lockKey]);

        return $this->success();
    }

    /**
     * 设置允许查询邮件的client_id
     */
    public function actionSetGetMailClientWhiteList(string $client_ids)
    {
        $clientIds = explode(',', $client_ids);
        $clientIds = array_filter(array_map('intval', $clientIds));
        $clientIds = array_values($clientIds);

        $ttl = 30 * 3600 * 24;  // 一个月过期
        RedisService::cache()->set(self::REDIS_KEY_EMAIL_CLIENTS, json_encode($clientIds), 'EX', $ttl);

        return $this->success();
    }


    /**
     * 获取邮件详细信息
     * @param int $client_id
     * @param int $mail_id
     * @return void
     */
    public function actionGetMailInfo(int $client_id, int $user_id, int $mail_id)
    {
        User::setLoginUserById($user_id);
        // 确认client_id是否符合
        $clientsWhiteList = RedisService::cache()->get(self::REDIS_KEY_EMAIL_CLIENTS);
        $clientsWhiteList = (array) json_decode($clientsWhiteList, true);

        if (!in_array($client_id, $clientsWhiteList, true)) {
            return $this->fail(-1, "不允许查询该client=$client_id");
        }

        //获取邮件详情
        $mail = new \common\library\mail\Mail($mail_id, $user_id);
        $mail->getFormatter()->setShowAttachList(true);
        $mail->getFormatter()->setSpecifyFields([
            'mail_id',
            'mail_type',
            'sender',
            'receiver',
            'folder_id',
            'delete_flag',
            'subject',
            'user_id',
            'user_mail_id',
            'create_time',
            'update_time',
            'receive_time',
            'reply_to',
            'forward_flag',
            'delete_flag',
            'send_status',
            'client_id',
            'reply_mail_id',
            'alias_id',
            'reply_to_mail_id',
            'source_mail_id',
            'conversation_id'
        ]);

        $data = $mail->getAttributes();

        $contentInfo = MailContentHelper::getContentAndPlainText($client_id, $data['user_mail_id'], $mail_id);
        $data['content'] = $contentInfo['plainText'] ?? '';

        return $this->success($data);
    }


    public function actionReportDetail($clientId ,$key, array $params = [], $user_id = 0)
    {
        $allowClientIds = [9650,72718,33859,339947,29208,47425,13343,353505,84846,27866,35824,51969,76012,86919,35528,80700,84337,354500,63509,351810,43116,73035,33205,83025,351735,341283,85677,29068,64052,48043,59219,58575,181,628,756,1064,1157,1838,1972,1984,2327,2419,2501,3764,4204,4317,4333,4611,4621,4648,4699,4862,5041,5117,6293,7126,7708,7797,9550,9901,10628,12665,13536,13878,14126,15460,16859,16987,16993,17111,17372,17586,19328,19416,19469,19902,20262,21327,22482,22599,23006,23660,23841,25240,26282,27037,27359,27377,28337,28339,29094,29312,29518,29598,29883,30760,31141,31235,31386,31668,31999,32315,32564,33209,33338,33554,33662,33736,34173,34386,34627,34646,34929,35193,35237,35311,35528,35708,35721,35824,36105,36204,36241,36262,36401,36412,36459,36644,36691,36728,36836,37515,37651,37688,37719,38128,38742,38746,38933,39261,40083,40320,40787,40829,41308,41446,41755,41999,42085,42284,42612,42867,43116,43602,43670,44213,44598,44698,44841,45018,45304,45858,46095,46097,46331,46451,47266,47280,47300,47425,47722,48279,48427,49206,49395,49667,49686,50108,51969,52015,52124,52224,52257,53180,54237,54369,55581,56521,57449,58575,58690,58912,58923,59047,59219,59932,60288,61225,61279,61637,61883,62109,62405,62954,63105,63208,63509,63938,63967,63980,64035,64052,66144,66207,66297,67468,67635,67950,68236,68355,68443,68493,70026,70337,70374,70538,70645,70754,70760,70858,71274,71488,72051,72921,73035,73387,73789,73910,73947,74243,74373,74589,74853,75155,75822,76012,76042,76263,76595,77013,77094,77143,77275,77478,77690,78347,78395,78570,78821,78988,79135,79412,79506,79639,79689,79759,79805,79876,79931,79986,80766,80962,81345,81375,81736,81862,81875,82088,82207,82442,83624,83860,84846,84909,85202,85677,85968,86297,86419,87280,87569,87823,88570,88928,89013,89054,89071,89091,333385,333553,334176,334315,334323,336056,336216,336340,336633,337058,337087,337091,337237,337391,337984,338030,338221,338712,339018,339106,339628,339803,339923,339947,340166,340245,340299,340392,340469,340491,340566,340777,341274,341283,341939,342034,342144,342594,343334,344491,344495,344736,345468,345535,346999,347422,347470,347614,349183,349296,349661,349934,350293,350358,350424,350726,350788,350916,350978,350983,350985,350992,350994,351011,351047,351199,351275,351279,351310,351390,351447,351449,351501,351547,351550,351551,351554,351641,351649,351658,351663,351676,351681,351703,351729,351737,351871,351916,351949,351963,352013,352033,352046,352057,352068,352101,352103,352138,352145,352155,352169,352180,352181,352193,352234,352267,352284,352313,352371,352392,352441,352513,352527,352587,352640,352722,352757,352761,352785,352797,352799,352814,352936,352981,353029,353056,353075,353167,353211,353279,353293,353294,353388,353446,353474,353487,353501,353505,353521,353560,353563,353648,353739,353782,353825,353842,353882,353887,353933,353946,354031,354065,354091,354104,354112,354139,354184,354284,354299,354318,354395,354396,354403,354487,354575,354605,354647,354669,354725,354730,354735,354785,354797,354844,354851,354935,354951,354972,354995,355064,355129,355153,355326,355394,355398,355440,355456,355481,355507,355531,355551,355598,355620,355669,355678,355774,355825,355871,355909,356008,356021,356047,356057,356185,356186,356197,356328,356426,356477,356574,356606,356608,356633,356647,356781,356783,356802,356952,356955,356991,357033,357045,357184,357210,357221,357247,357341,357451,357501,357504,357513,357522,357614,357627,357708,357866,357900,358062,358078,358114,358143,358270,358314,358458,358466];


        if (!in_array($clientId,$allowClientIds)) {
            throw new \RuntimeException("该client不在允许范围之内，请重新输入");
        }

        // 兼容前端穿过来的数据为数组
        $params = array_map(function ($item) {
            return is_array($item) ? json_encode($item) : $item;
        }, $params);

        $privilegeService = PrivilegeService::getInstance($clientId);
        $adminUser = empty($user_id) ? $privilegeService->getAdminUserId() : $user_id;

        User::setLoginUserById($adminUser);

        $user = User::getLoginUser();

        $report = new \common\library\statistics\render\report\Report($user->getClientId(), $key, $params);
        $report->setViewUserId($user->getUserId());
        $report->setForceRefresh(1);
        $report->setShowDetail(1);
        $report->setShowData(1);
        $data = $report->format();

        $formatData = self::formatReportData($data);

        $this->success([
            'report_data' => $data,
            'format' => $formatData
        ]);

    }

    public static function formatReportData(array $reportData)
    {
        $statisticField = [];

        // 根据报表内容获取报表文案
        $columnMap = [];
        $fields = [];
        foreach ($reportData['config']['field'] as $item)
        {
            $columnMap[$item['key']] = $item['label'];
            $fields[] = $item['key'];
        }
        $summaryItemKeys = [];
        foreach ($reportData['config']['summaries'] as $item)
        {
            // 二级筛选不需要展示 会导致format乱序
            if (str_contains($item['key'],'company.create_count_sub_key_')) continue;
            $columnMap[$item['key']] = $item['label'];
            $summaryItemKeys[] = $item['key'];
        }

        // 组装表头
        $msgList = [];
        $msgList[] = implode(' | ', array_values($columnMap));
        $msgList[] = implode(" | ", array_fill(0, count($columnMap), '---'));

        $dataList = $reportData['data'];

        if ($reportData['key'] == 'sjts2')
        {
            $msgList = [];
            $msgList[] = implode(' | ', ['销售阶段', '流入数', '转化数', '转化率', '停留数', '流失数', '流失率', '平均停留时间']);
            $msgList[] = implode(" ",array_fill(0,count(['销售阶段', '流入数', '转化数', '转化率', '停留数', '流失数', '流失率', '平均停留时间']),'|---')); // 拼装表头
            foreach ($dataList as $data)
            {
                if (!isset($data['into_number'])) {
                    continue;
                }
                $msgList[] = implode(' | ', [
                    $data['stage_name'],
                    $data['into_number'],
                    $data['conversion_number'],
                    $data['conversion_scale'],
                    $data['stay_number'],
                    $data['lost_number'],
                    $data['lost_scale'],
                    $data['avg_stay_time'],
                ]);
            }
        } else {
            foreach ($dataList as $dataItem)
            {
                $tempMsgInfo = [];

                $dataItemKeyByKey = array_column($dataItem, null, 'key');
                if (empty($dataItemKeyByKey)) {
                    continue;
                }

                foreach ($fields as $item)
                {
                    $dataItemLabel = $dataItemKeyByKey[$item]['label'] ?? '';
                    $tempMsgInfo[] = $dataItemLabel;
                }

                $tempDataItem = end($dataItem);
                if (!isset($tempDataItem['summaries'])) {
                    $dataItem = reset($dataItem);
                } else {
                    $dataItem = $tempDataItem;
                }

                $dataItemSummaryList = $dataItem['summaries'];
                foreach ($summaryItemKeys as $key)
                {
                    if (str_contains($key,'company.create_count_sub_key_')) continue;
                    $summaryItem = $dataItemSummaryList[$key] ?? [];
                    if (empty($summaryItem)) {
                        continue;
                    }

                    $summaryItemValue = ($summaryItem['value'] === '-') ? 0 : $summaryItem['value'];
                    $tempMsgInfo[] = $summaryItemValue;
                }

                $msgList[] = implode(' | ', $tempMsgInfo);
            }
        }




        // 组装表格数据的文本
        $reportMsg = ' | ' . implode(" | \n | ", $msgList) . " | ";

        return $reportMsg;

    }

    public static function buildHeader(array $reportData)
    {

        $columnMap = [];
        foreach ($reportData['config']['field'] as $item) {
            $columnMap[$item['key']] = $item['label'];
        }

        foreach ($reportData['config']['summaries'] as $item) {
            $columnMap[$item['key']] = $item['label'];
        }
        return $columnMap;
    }


    public function actionGetExternalFieldData($client_id, $refer_type = '', $field_type = '')
    {
        $allowClientIds = [9650,72718,33859,339947,29208,47425,13343,353505,84846,27866,35824,51969,76012,86919,35528,80700,84337,354500,63509,351810,43116,73035,33205,83025,351735,341283,85677,29068,64052,48043,59219,58575];

        if (!in_array($client_id, $allowClientIds)) {
            throw new \RuntimeException("不支持查询该client数据");
        }

        $fieldTypesMap = [
            0 => '特殊', // FIELD_TYPE_OTHER
            1 => '单行文本', // FIELD_TYPE_TEXT
            2 => '多行文本', // FIELD_TYPE_TEXTAREA
            3 => '单选', // FIELD_TYPE_SELECT
            4 => '日期', // FIELD_TYPE_DATE
            5 => '数值', // FIELD_TYPE_NUMBER
            6 => '图片', // FIELD_TYPE_IMAGE
            7 => '多选', // FIELD_TYPE_MULTIPLE_SELECT
            8 => '附件', // FIELD_TYPE_ATTACH
            9 => '关联字段', // FIELD_TYPE_FIELDS
            10 => '日期 - 时间', // FIELD_TYPE_DATETIME
            11 => '公式', // FIELD_TYPE_FORMULA
            12 => '汇总', // FIELD_TYPE_CALCULATE
            13 => '布尔', // FIELD_TYPE_BOOLEAN
            14 => '收件人', // FIELD_TYPE_RECEIVER
            15 => '发件人', // FIELD_TYPE_SENDER
            16 => '收件人域', // FIELD_TYPE_RECEIVER_DOMAIN
            17 => '状态', // FIELD_TYPE_STATUS
            18 => '阶段', // FIELD_TYPE_STAGE
            21 => '日期范围', // FIELD_TYPE_DATE_RANGE
            22 => '富文本', // FIELD_TYPE_RICH_TEXT
            23 => '引用字段', // FIELD_TYPE_QUOTE_FIELDS
        ];

        $refers = [\Constants::TYPE_OPPORTUNITY,\Constants::TYPE_ORDER,\Constants::TYPE_COMPANY];
        $result = [];
        foreach ($refers as $refer) {
            $fieldQuery = new \common\library\custom_field\FieldList($client_id);
            $fieldQuery->setType($refer);
            $fieldQuery->setIsList(0);
            $fieldQuery->setFields(['id', 'field_type', 'name', 'ext_info']);

            $fieldQuery->setDisableFlag(0);

            $fieldQuery->setBase(0);
            $fieldQuery->setEnableFlag(1);
            $fieldListData = $fieldQuery->find();

            $result = array_merge($result, $fieldListData);
        }



        foreach ($result as $index => $item)
        {
            $result[$index]['field_desc'] = $fieldTypesMap[$item['field_type']] ?? '特殊';
        }

        $this->success([
            'list' => $result,
        ]);
    }

    public function actionGetCustomerMail(int $clientId, int $companyId, string $startTime, string $endTime)
    {
        if (!in_array($clientId, [9650, 72718, 33859, 339947, 29208, 47425, 13343, 353505, 84846, 27866, 35824, 51969, 76012, 86919, 35528, 80700, 84337, 354500, 63509, 351810, 43116, 73035, 33205, 83025, 351735, 341283, 85677, 29068, 64052, 48043, 59219, 58575])) {
            throw new \RuntimeException('不在操作名单内！');
        }

        $mainUserId = PrivilegeService::getInstance($clientId)->getAdminUserId();
        \User::setLoginUserById($mainUserId);

        $list = new \common\library\trail\CompanyDynamicList($clientId, $companyId);
        $list->setOperatorUserId($mainUserId);
        $list->setBeginTime($startTime);
        $list->setEndTime($endTime);
        $list->setModuleIds([202, 201]);
        $list->setCompanyId($companyId);
        $list->setLimit(200);
        $list->getFormatter()->setShowCompanyId($companyId);
        $list->getFormatter()->setShowCaptureCard(true);
        $list->getFormatter()->setShowAdjustEmailDynamic(false);
        $list->getFormatter()->setShowCommentList(true);
        $ret = $list->find();

        $conversation = [];

        $mailIds = array_column($ret,'data');
        $mailIds = array_column($mailIds,'mail_id');

        $mailList = new \common\library\mail\MailList($clientId, $mainUserId);
        $mailList->setMailIds($mailIds);
        $mailList->setOrderBy('receive_time');
        $mailList->setOrder('desc');
        $mailList->setLimit(200);
        $listInfo = $mailList->find();

        $params = [];
        foreach ($listInfo as $info)
        {
            $params[] = [
                'clientId' => $clientId,
                'mailId' => $info['mail_id'],
                'userMailId' => $info['user_mail_id']
            ];
        }

        $mailIdToContentInfos = [];

        foreach (array_chunk($params, 40) as $tempParams)
        {
            $contentInfos = \common\library\mail\MailContentHelper::getContentAndPlainTextList($tempParams);
            $mailIdToContentInfos = array_merge($contentInfos, $mailIdToContentInfos);
        }

        $mailIdToContentInfo = array_column($mailIdToContentInfos, 'plainText', 'mailId');

        foreach ($ret as $item)
        {
            $data = $item['data'] ?? [];

            $time = date('Y-m-d', strtotime($data['receive_time']));
            $conversation[$time][] = [
                'company_id' => $companyId,
                'client_id' => $clientId,
                'customer_id' => implode(',', $item['customer_id']),
                'user_id' => $data['user_id'],
                'mail_id' => $data['mail_id'],
                'send_type' => $data['mail_type'],
                'sender' => $data['sender'],
                'receiver' => $data['receiver'],
                'receive_time' => $data['receive_time'],
                'subject' => $data['subject'],
                'content' => $mailIdToContentInfo[$data['mail_id']],
                'read_flag' => $data['read_flag'],
                'open_flag' => $data['open_flag'],
                'tag_list' => $data['tag_list'],
                'attachment_list' => $data['attachment_list'],
            ];
        }

        $result = [
            'clientId' => $clientId,
            'companyId' => $companyId,
            'conversation' => $conversation,
        ];

        $this->success($result);
    }

    /**
     * 获取最近一次未使用的验证码
     */
    public function actionGetPhoneCode($phone, $countryCode = '86')
    {
        $code = AccountService::getPhoneCode($phone, $countryCode);
        return $this->success($code);
    }

    // 给开发替换布局的schema
    public function actionEditLayoutConfig(int $client_id, string $object_name, string $page_type, $schema)
    {
        if (\Yii::app()->params['env'] != 'test') {
            throw new RuntimeException("仅支持测试环境使用");
        }

        $validatePageType = array_keys(\common\library\layout\LayoutConstants::LAYOUT_FIELD_SCENE_MAP);
        $this->validate([
            'page_type' => 'in:' . implode(',', $validatePageType),
        ]);

        $client = new Client($client_id);
        if ($client->isNew()) {
            throw new RuntimeException("client_id={$client_id}不存在");
        }
        \User::setLoginUserById($client->getMasterUser()->getUserId());

        $schema = json_decode($schema, true);
        if (!isset($schema['schema'])) {
            $schema['schema'] = $schema;
        }
        $schema = json_encode($schema);

        $layoutDecorateClass = \common\library\layout\init\Factory::make($client_id, $object_name);
        $layoutDecorateClass->setSchema($page_type, $schema);
        $layoutDecorateClass->setInitPageType($page_type);
        $layoutDecorateClass->init(true);

        $this->success("client_id={$client_id},初始化{$page_type}的布局配置");

    }

    public function actionCalculateField(int $client_id, string $object_name, $field)
    {
        if (\Yii::app()->params['env'] != 'test') {
            throw new RuntimeException("仅支持测试环境使用");
        }
        $client = new Client($client_id);
        if ($client->isNew()) {
            throw new RuntimeException("client_id={$client_id}不存在");
        }
        \User::setLoginUserById($client->getMasterUser()->getUserId());
        $fieldObj = new \common\library\object\field\Field($client_id);
        $fieldObj->loadField($object_name, $field);
        if ($fieldObj->isNew()) {
            $this->success("client_id={$client_id},object={$object_name},field={$field}，字段不存在");
        }
        $task = new AsyncTask($client_id);
        $type = 0;        // 公共模块，type为0
        $referId = 0;
        $extInfo = [
            'object_name' => $object_name,
            'calculate_fields' => $field,     // 可以为数组或者非数组
            'retry_time' => 0,
            'old_column' => $fieldObj->columns['data_key'],
            'new_column' => $fieldObj->columns['data_key']
        ];
        $status = \common\library\object\field\updator\calculator\FieldCalculateTask::CALCULATE_FIELD_TASK_STATUS_UNDO;

        $task->getOperator()->create($client_id, $type, \common\library\async_task\AsyncTaskConstant::SCENE_CALCULATE_SCENE, $referId, $status, $extInfo);

        $task = new \common\library\object\field\updator\calculator\FieldCalculateTask($client_id, $task->task_id);
        $task->run();
        $this->success("client_id={$client_id},object={$object_name},field={$field}，字段重算成功");
    }

    public function actionGenerateInsight($clientId = 9650)
    {

        if (!in_array($clientId, [9650,1,351474,3,18133,6534]))
        {
            throw new \RuntimeException("clientId不在 [9650,1,351474,3,18133,6534] 范围内");
        }
        $params = [
            'clientIds' => $clientId,
            'dryRun' => 1,
        ];

//            \common\library\CommandRunner::run('Insight', 'GenerateInsight', $params);
        \common\library\CommandRunner::run('Insight', 'NewGenerateInsight', $params);
        $this->success();
    }

    /**
     * 生成行动建议接口
     */
    public function actionGenerateCompanyPortrayal($code, $date_time)
    {
        if ($code == 'sticking_point') {
            // 校验，只有测试环境才能运行
            \common\library\CommandRunner::run('aiCompanyQualityCheck', 'HandleExternalModule', [
                'code' => $code,
                'dateTime' => $date_time
            ], '/dev/null');
        } else {
            // 校验，只有测试环境才能运行
            \common\library\CommandRunner::run('aiCompanyQualityCheck', 'HandleExternalModuleByCode', [
                'code' => $code,
                'dateTime' => $date_time
            ], '/dev/null');
        }

        return $this->success();
    }

    public function actionPushFollowSuggestionFeed($client_id = 0, $date_time = '')
    {
        if (empty($client_id))
        {
            $user = \User::getLoginUser();
            $client_id = $user->getClientId();
        }

        if (empty($date_time)) {
            $date_time = date('Y-m-d H:i:s');
        }

        // 校验，只有测试环境才能运行
        \common\library\CommandRunner::run('aiCompanyQualityCheck', 'PushFollowSuggestionFeed', [
            'clientId' => $client_id,
            'dateTime' => $date_time,
        ], '/dev/null');

        return $this->success();
    }

    public function actionGenerateCompanyCheck($day, $company_ids, $client_id = 0, $run_external = 0, $language = '')
    {
        if (empty($client_id))
        {
            $user = \User::getLoginUser();
            $client_id = $user->getClientId();
        }

        if(!empty($language)) {
            \Yii::app()->language = $language;
        }

        // 校验，只有测试环境才能运行
        \common\library\CommandRunner::run("AiCompanyQualityCheck","QcWithMultiAgents",[
            "clientId" => $client_id,
            "companyIds" => $company_ids,
            "day" => $day,
            'isFirstRun' => 1
        ],"/dev/null");


        return $this->success();
    }

    public function actionAddExternal($code, $client_id, $company_id, $date_time)
    {
        $key = \common\library\ai_agent\company_quality_check\pipeline\QcExternalServiceHelper::EXTERNAL_MODULE_PREFIX . "{$code}.{$date_time}";

        //1.hashmap 表示每个任务的完成状态 2.list 所有任务的集合
        $redis = \RedisService::getInstance('redis');
        $taskId = "{$client_id}-{$company_id}";
        $mapKey = $key . ".map";
        $listKey = $key . ".list";

        $redis->hset($mapKey, $taskId, 0);
        $redis->lpush($listKey, [$taskId]);

        return $this->success();
    }

    // 前端使用 通过object_name查询字段的控件和搜索组件
    public function actionGetFieldComponent(array $object_names)
    {
        $this->validate([
            'object_names' => 'required|not_empty',
            'object_names.*' => 'string|in:' . implode(',', array_keys(\common\library\object\object_define\Constant::OBJ_METADATA_MAP))
        ]);
        $user = \User::getLoginUser();

        $objNameToFieldList = [];
        foreach ($object_names as $objectName) {
            $filter = new \common\library\object\field\FieldFilter($user->getClientId());
            $filter->object_name = $objectName;
            $filter->enable_flag = 1;
            $list = $filter->find();
            $list->getFormatter()->displayFields(['system_type', 'object_name', 'field', 'field_name', 'field_type', 'array_flag', 'required', 'tips', 'ext_info', 'is_writable', 'show_flag']);
            $list->getFormatter()->setMainSubObjectNames([$objectName]);
            $list->getFormatter()->displayComponent(true);
            $list->getFormatter()->displayDecorateExtInfo(true);
            $fieldList = $list->getListAttributes();

            $searchFieldService = new \common\library\object\field\service\SearchFieldService($user->getClientId(), $objectName);
            try {
                //搜索的组件模块未必有适配 进行异常捕获
                $fieldToSearchComponentMap = array_column($searchFieldService->searchFieldList(), null, 'field');
            } catch (\Throwable $throwable) {
                $fieldToSearchComponentMap = [];
            }


            foreach ($fieldList as &$fieldInfo) {
                if (isset($fieldToSearchComponentMap[$fieldInfo['field']])) {
                    $searchComponentConfig = $fieldToSearchComponentMap[$fieldInfo['field']];
                    unset($searchComponentConfig['field']);
                    unset($searchComponentConfig['name']);
                    $fieldInfo['search_component_config'] = $searchComponentConfig;
                } else {
                    $fieldInfo['search_component_config'] = null;
                }
            }
            $objNameToFieldList[$objectName]['field_list'] = $fieldList;
        }

        $this->success($objNameToFieldList);
    }

    public function actionBatchConversationMailInfoList
    (
        $conversation_id,
        $user_id,
        $page = 1,
        $page_size = 20,

    )
    {
        $this->validate([
            'conversation_id' => 'required|integer',
            'user_id' => 'required',
            'page' => 'integer',
            'page_size' => 'integer|max:100',

        ]);

        User::setLoginUserById($user_id);
        $user = \User::getLoginUser();
        $clientId = $user->getClientId();

        $mailList = new \common\library\mail\MailList($clientId, $user_id);
        $mailList->setConversationId($conversation_id);
        $mailList->setDeleteFlag(0);
        $mailList->setNotInFolderIds([\Mail::FOLDER_JUNK_ID]);
        $mailList->setLimit($page_size);
        $mailList->setOffset(($page - 1) * $page_size);
        $mailList->getFormatter()->webListSetting();
        $mailList->getFormatter()->setShowPlainText(true);
        $list  = $mailList->find();
        $count = $mailList->count();

        $list = \common\library\mail_conversation\Helper::getMailInfoByMailIds($clientId,$list);

        $data = [
            'list'  => $list,
            'count' => intval($count)
        ];

        return $this->success($data);
    }

    public function actionQueryKnowledge(
        string $question,
        int $client_id,
        int $user_id
    )
    {
        $agent = new KnowledgeBaseAiAgent($client_id, $user_id);
        $agent->setSkipProcessRecord(true);
        $agent->setStreamMode(false);
        $agent->question = $question;
        $aiAgentProcessResponse = $agent->process();
        $answer = $aiAgentProcessResponse->answer;
        $agent->loadReferDocs();
        $agent->loadReferProducts();
        return $this->success(['debug' => array_merge($agent->debugInfo, [
            'docUserPrompt' => $agent->docUserPrompt,
            'productUserPrompt' => $agent->productUserPrompt,
            'finalPrompt' => $agent->finalPrompt,
            'answer' => $answer
        ])]);
    }

    public function actionGetAliCustomerPortrait($clientId, $companyId)
    {
//        $adminUser = PrivilegeService::getInstance($clientId)->getAdminUserId();
//        User::setLoginUserById($adminUser);

        $sql = "select * from tbl_alibaba_customer_portrait where client_id = {$clientId} and company_id = {$companyId}";
        $db = PgActiveRecord::getDbByClientId($clientId);
        $profileData = $db->createCommand($sql)->queryAll();

        if (empty($profileData)) {
            $this->success(
                [
                    'list' => $profileData
                ]
            );
        }
        $returnResult = [];
        $service = new \common\library\customer\service\AlibabaCustomerPortraitService();
        foreach ($profileData as $datum)
        {
            try {
                $returnResult[] = $service->processReturnData($datum);
            } catch (\Throwable $exception) {
                $returnResult[] = [];
            }
        }

        $this->success([
            'list' => $returnResult
        ]);
    }



    public function actionGenerateQcV3($clientId, $companyId)
    {
        //创建谈单监测v3任务
        \common\library\CommandRunner::run('AiCompanyQualityCheck', 'QcWithMultiAgents', [
            'clientId' => $clientId,
            'companyId' => $companyId,
            'day' => 180,
            'isFirstRun' => 0
        ],'/dev/null');

        return $this->success("创建成功");
    }

    /*
 * 生成跟进建议
 */
    public function actionGenerateAnalysis($client_id,$company_id,$date_time)
    {
        $this->validate([
            'client_id' => 'required|integer|not_empty',
            'company_id' => 'required|integer|not_empty',
            'date_time' => 'string'
        ]);

        $client = new Client($client_id);
        if($client->client_type != \common\library\account\Client::CLIENT_TYPE_INTERNAL) {
            $this->fail(-1,"不允许非测试账号");
        }

        $dateTime = date('Y-m-d', strtotime($date_time));

        $adminUserId = PrivilegeService::getInstance($client_id)->getAdminUserId();
        $agent = new \common\library\ai_agent\CompanyQualityCheckAiAgentV2($client_id, $adminUserId);
        $agent->analysis([
            "clientId" => $client_id,
            "companyId" => $company_id,
            "dateTime" => $dateTime
        ]);

        return $this->success();
    }

    public function actionExpiredFollowSuggestion($client_id)
    {
        $this->validate([
            'client_id' => 'required|integer|not_empty',
        ]);

        $client = new Client($client_id);
        if($client->client_type != \common\library\account\Client::CLIENT_TYPE_INTERNAL) {
            $this->fail(-1, "不允许非测试账号");
        }

        $env = \Yii::app()->params["env"];
        if(empty($env) || $env != "test") {
            $this->fail(-1, "不允许非测试环境");
        }

        \common\library\CommandRunner::runSync("AiAgent","ExpireFollowSuggestion",[
            "--clientIds=" . $client_id
        ]);
    }

    public function actionGetClientUserRelation($client_id)
    {
        $relations = (new \common\library\privilege_v3\UserPrivilegeRelationService($client_id))->getClientUserRelation();

        return $this->success($relations);
    }

    public function actionGetUserPrivilegeRelation(
        $client_id,
        $user_id,
        array $referUserIds = [],
    )
    {
        $relations = (new \common\library\privilege_v3\UserPrivilegeRelationService($client_id))->getUserPrivilegeRelation($user_id, $referUserIds);
        return $this->success($relations);
    }

    public function actionGetUserRoleAccessPrivilege(
        $client_id,
        $user_id,
        array $relations = [],
    )
    {
        $accessPrivileges = (new \common\library\privilege_v3\UserRolePrivilegeService($client_id))->getUserRoleAccessPrivilege($user_id, $relations);

        return $this->success($accessPrivileges);
    }


    public function actionGetUserRoleFieldPrivilege(
        $client_id,
        $user_id,
        array $relations = [],
    )
    {
        $fieldPrivileges = (new \common\library\privilege_v3\UserRolePrivilegeService($client_id))->getUserRoleFieldPrivilege($user_id, $relations);

        return $this->success($fieldPrivileges);
    }

    public function actionGetPrivilegeFieldListV2($role_id, array $functional_ids = [])
    {
        if (empty($functional_ids)) {
            $functional_ids = array_keys(PrivilegeConstants::FIELD_PRIVILEGE_FUNCTIONAL_MAP);
        }
        $this->validate([
            'role_id' => 'required|numeric',
            'functional_ids.*' => 'required|in:' . implode(',', array_keys(PrivilegeConstants::FIELD_PRIVILEGE_FUNCTIONAL_MAP)),
        ]);

        $ret = [];
        foreach ($functional_ids as $functional_id) {
            $api = new \common\library\privilege_v3\privilege_field\PrivilegeApi();
            $list = $api->getFunctionalRoleFieldListInfo($functional_id, $role_id);
            $ret[$functional_id] = $list;
        }

        $this->success($ret);
    }

    public function actionGetUserMessageContactMessage(int $client_id, int $user_contact_id, string $start_date, string $end_date)
    {
        LogUtil::info("GetUserMessageContactMessage",[
            'client_id' => $client_id,
            'user_contact_id' => $user_contact_id,
        ]);
        $env = \Yii::app()->params["env"];
        if ($env == 'prod') {
            return $this->fail("接口不支持Prod环境");
        }

        $privilegeService = PrivilegeService::getInstance($client_id);
        \User::setLoginUserById($privilegeService->getAdminUserId());

        $startTime = date('Y-m-d 00:00:00', strtotime($start_date));
        $endTime = date('Y-m-d 23:59:59', strtotime($end_date));

        $messagePdo = new \common\library\sns\customer\UserCustomerContactMessageList($client_id);
        $messagePdo->setUserContactId($user_contact_id);
        $messagePdo->setStartTime($startTime);
        $messagePdo->setEndTime($endTime);
        $messagePdo->setOrderBy('send_time');
        $messagePdo->setOrder('asc');
        $messageList = $messagePdo->find();

        $messageByDate = [];
        foreach ($messageList as $messageItem) {
            $sendTime = $messageItem['send_time'];
            $sendDate = date('Y-m-d', strtotime($sendTime));
            $messageByDate[$sendDate][] = $messageItem;
        }

        // 反查出sns_type
        $contactPdo = new \common\library\sns\customer\CustomerContactList($client_id);
        $contactPdo->selectColumns(['sns_type', 'user_contact_id']);
        $snsTypeMap = array_column($contactPdo->find(), 'sns_type', 'user_contact_id');
        $snsType = $snsTypeMap[$user_contact_id] ?? '';

        $channel = \common\library\sns\customer\CustomerContactService::SNS_TYPE_TO_CHANNEL_TYPE[$snsType] ?? -1;
        if ($channel == -1) {
            throw new RuntimeException("不支持的sns_type : {$snsType}");
        }

        // 转换body
        $ret = [];
        foreach ($messageByDate as $date => $messageArray) {
            $formatMessages = [];

            foreach ($messageArray as $message) {
                $body = \common\library\sns\message\MessageBuilder::loadMessage($message['type'], $message['body']);
                $role = $message['send_type'] == \common\library\okki_chat\MessageService::FROM_TYPE_OKKI ? 'Salesperson' : 'Customer';
                if (!empty($body = trim($body->getPromptText($channel)))) {
                    $formatMessages[] = $role . ": " . $body;
                }
            }

            $ret[] = [
                'date' => $date,
                'snsType' => $snsType,
                'content' => implode(PHP_EOL, $formatMessages),
            ];
        }

        $this->success($ret);
    }

    /**
     * 获取销售助手ai分析数据 =算法使用
     * @param $clientId
     * @param $companyId
     * @return void
     * @throws CDbException
     * @throws CException
     * @throws ProcessException
     */
    public function actionGetAliCustomerInfoByCompanyId($clientId, $companyId, $storeId = 0, $buyer_accountId = 0)
    {
        $adminUser = PrivilegeService::getInstance($clientId)->getAdminUserId();
        User::setLoginUserById($adminUser);
        $db = PgActiveRecord::getDbByClientId($clientId);

        // 公司信息
        $company = new common\library\customer_v3\company\orm\Company($clientId, $companyId);
        if (!$company->company_id) {
            throw new \RuntimeException("公司id错误，找不到company_id");
        }
        // 一些基本信息
        $homePage = $company->homepage;
        $companyName = $company->name;
        $remark = $company->remark;
        $country = $company->country;
        $start = $company->star;

        $return = [
            'homePage' => $homePage,
            'companyName' => $companyName,
            'remark' => $remark,
            'country' => $country,
            'start' => $start,
            'customer' => []
        ];

        $customerList = new \common\library\customer_v3\customer\CustomerList($clientId);
        $customerList->setCompanyId($companyId);
        $customerList = $customerList->find();

        foreach ($customerList as $customerItem)
        {
            $return['customer'][] = $customerItem;
        }



        // 画像信息
        $sql = "select * from tbl_ai_portrait_analysis where client_id = {$clientId} and module = 4 and refer_id = {$companyId}  and status = 2 order by analysis_id desc limit 1";
        $portraitInfo = $db->createCommand($sql)->queryAll();
        $portraitTag = [];
        $portraitResult = [];
        if (!empty($portraitInfo)) {
            $portraitInfo = $portraitInfo[0];
            $portraitTag = json_decode($portraitInfo['tags'], true);
            $portraitResult = json_decode($portraitInfo['analyze_result'], true);
        }
        $return = array_merge($return, [
            'portraitTag' => $portraitTag,
            'portraitResult' => $portraitResult,
        ]);
        // 国际站行为数据
        $alibabaInfo = [];
        $companyRelation = new \common\library\alibaba\customer\AlibabaCompanyRelation($clientId);
        $companyRelation->loadByCompanyId($companyId);
        if (!empty($companyRelation->alibaba_company_id)) {
            $alibabaCustomerPortraitService = new \common\library\customer\service\AlibabaCustomerPortraitService();
            // 找不到就加载以前的数据
            $alibabaInfo = $alibabaCustomerPortraitService->loadProfileData($companyRelation->alibaba_company_id);
        }


        try {
            if (empty($alibabaInfo) && !empty($storeId) && !empty($buyer_accountId))
            {
                $alibabaTopClient = \common\library\alibaba\services\AlibabaTopClient::getInstance();
                $store = new AlibabaStore($clientId, $storeId);
                $sessionKey = $store->access_token;
                if (empty($sessionKey)) {
                    $alibabaAccount = new \common\library\alibaba\oauth\AlibabaAccount($clientId);
                    $alibabaAccount->loadBySellAccountId($storeId);
                    $storeId = $alibabaAccount->store_id;
                    $store = new AlibabaStore($clientId, $storeId);
                    $sessionKey = $store->access_token;
                }

                if (empty($companyRelation->alibaba_company_id)) {
                    $buyerMemberInfo = \common\library\alibaba\customer\CustomerSyncHelper::getAlibabaBuyerInfo($sessionKey, $buyer_accountId);

                    $alibabaCompanyId = $buyerMemberInfo['alibaba_company_id'];
                } else {
                    $alibabaCompanyId = $companyRelation->alibaba_company_id;
                }

                $visitHistoryResponse = $alibabaTopClient->queryCustomerVisitHistory($sessionKey, $alibabaCompanyId);
                \LogUtil::info("alibabaTopClient visit history query response: " . json_encode($visitHistoryResponse));
                if (empty($visitHistoryResponse['result'])) {
                    throw new \RuntimeException(\Yii::t('common', 'The request to Alibaba top for visit history query timed out'));
                }
                $visitHistory = $visitHistoryResponse['result'];
                if (empty($visitHistory['success'])) {
                    throw new \RuntimeException(\Yii::t('common', 'visit history query returned not success'));
                }
                if (empty($visitHistory['data'])) {
                    throw new \RuntimeException(\Yii::t('common', 'visit history query returned null data'));
                }


                $visitHistory = $visitHistory['data'];
                $toDealData = [
                    "alibaba_company_id" => $companyRelation->alibaba_company_id,
                    'is_fans' => $visitHistory['is_fans'] ?? 0,
                    'latest_visit_products' => !empty($visitHistory['visit_product_list']['visit_product_list']) ? json_encode($visitHistory['visit_product_list']['visit_product_list']) : json_encode([]),
                    'focus_time' => $visitHistory['focus_time'] ?? null,
                    'action_time' => $visitHistory['action_time'] ?? null,
                    'footprint_ext_values' => !empty($visitHistory['ext_values']) ? json_encode(json_decode($visitHistory['ext_values'], true)) : json_encode([]),
                ];

                //  用户行为
                $lastSyncTime = new DateTime($existingProfile['latest_sync_time'] ?? '-2 day');
                $oneDayAgo = new DateTime('-1 day');

                $hiddenFields = array();
                if (empty($existingProfile) || $lastSyncTime < $oneDayAgo) {
                    //数据的 last_sync_time 在一天后，请求接口
                    $behaviorResponse = $alibabaTopClient->queryCustomerBehavior($sessionKey, $companyRelation->alibaba_company_id);
                    $behavior = [];

                    if (!empty($behaviorResponse['result'])
                        && !empty($behaviorResponse['result']['success'])
                        && !empty($behaviorResponse['result']['data'])) {
                        $behavior = $behaviorResponse['result']['data'];
                    }

                    foreach ($behavior as $key => $behaviorData) {
                        //数字类型返回-1 对象类型只有*字符串 表示用户隐藏
                        if ((is_numeric($behaviorData) && $behaviorData < 0)
                            || (is_array($behaviorData) && count(array_unique($behaviorData)) === 1 && in_array('*', $behaviorData))
                            || ($key == 'search_words' && !empty($behaviorData['search_words']) && count(array_unique($behaviorData['search_words'])) === 1 && in_array('*', $behaviorData['search_words']))
                            || ($key == 'preferred_industries' && !empty($behaviorData['preferred_industries']) && count(array_unique($behaviorData['preferred_industries'])) === 1 && in_array('*', $behaviorData['preferred_industries']))
                        ) {
                            if ($key == 'search_words') $key = 'latest_search_words';
                            $hiddenFields[] = $key;
                        }
                        if ($key == 'latest_inquiry_products' && !empty($behaviorData['latest_inquiry_products'])) {
                            $innerArray = $behaviorData['latest_inquiry_products'];
                            $isAllStars = count($innerArray) === count(array_filter(array_unique(array_column($innerArray, '*')), function ($value) {
                                    return $value === '*';
                                }));
                            if ($isAllStars) {
                                $hiddenFields[] = $key;
                            }
                        }
                    }

                    $toDealData = array_merge($toDealData, [
                        "product_view_count" => $behavior['product_view_count'] ?? 0,
                        "valid_inquiry_count" => $behavior['valid_inquiry_count'] ?? 0,
                        "valid_rfq_count" => $behavior['valid_rfq_count'] ?? 0,
                        "login_days" => $behavior['login_days'] ?? 0,
                        "spam_inquiry_marked_by_supplier_count" => $behavior['spam_inquiry_marked_by_supplier_count'] ?? 0,
                        "added_to_blacklist_count" => $behavior['added_to_blacklist_count'] ?? 0,
                        "total_order_volume" => $behavior['total_order_volume'] ?? 0,
                        "total_order_count" => $behavior['total_order_count'] ?? 0,
                        "trade_supplier_count" => $behavior['trade_supplier_count'] ?? 0,
                        "latest_search_words" => !empty($behavior['search_words']['search_words']) ? json_encode($behavior['search_words']['search_words']) : json_encode([]),
                        "preferred_industries" => !empty($behavior['preferred_industries']['preferred_industries']) ? json_encode($behavior['preferred_industries']['preferred_industries']) : json_encode([]),
                        "latest_inquiry_products" => !empty($behavior['latest_inquiry_products']['latest_inquiry_products']) ? json_encode($behavior['latest_inquiry_products']['latest_inquiry_products']) : json_encode([]),
                        "hidden_fields" => json_encode($hiddenFields),
                        "latest_sync_time" => xm_function_now(),
                        "update_time" => xm_function_now()
                    ]);
                }


                if (!empty($existingProfile) && $lastSyncTime > $oneDayAgo) {
                    $toDealData = array_merge($toDealData, [
                        "product_view_count" => $existingProfile['product_view_count'] ?? 0,
                        "valid_inquiry_count" => $existingProfile['valid_inquiry_count'] ?? 0,
                        "valid_rfq_count" => $existingProfile['valid_rfq_count'] ?? 0,
                        "login_days" => $existingProfile['login_days'] ?? 0,
                        "spam_inquiry_marked_by_supplier_count" => $existingProfile['spam_inquiry_marked_by_supplier_count'] ?? 0,
                        "added_to_blacklist_count" => $existingProfile['added_to_blacklist_count'] ?? 0,
                        "total_order_volume" => $existingProfile['total_order_volume'] ?? 0,
                        "total_order_count" => $existingProfile['total_order_count'] ?? 0,
                        "trade_supplier_count" => $existingProfile['trade_supplier_count'] ?? 0,
                        "latest_search_words" => !empty($existingProfile['search_words']) ? $existingProfile['search_words'] : json_encode([]),
                        "preferred_industries" => !empty($existingProfile['preferred_industries']) ? $existingProfile['preferred_industries'] : json_encode([]),
                        "latest_inquiry_products" => !empty($existingProfile['latest_inquiry_products']) ? $existingProfile['latest_inquiry_products'] : json_encode([]),
                        "hidden_fields" => !empty($existingProfile['hidden_fields']) ? $existingProfile['hidden_fields'] : json_encode([]),
                    ]);
                }
                $alibabaInfo = $toDealData;
            }
        } catch (\Throwable $exception) {
            $alibabaInfo = [];
            \LogUtil::info("获取失败",[
                'exception' => $exception->getMessage()
            ]);
        }

        $return = array_merge($return, [
            'alibabaInfo' => $alibabaInfo,
        ]);

        $this->success(['customer_info' => $return]);

    }


}
