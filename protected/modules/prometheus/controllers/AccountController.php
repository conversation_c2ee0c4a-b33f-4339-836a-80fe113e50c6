<?php

use common\library\account\UserList;
use common\library\export_v2\ExportFilter;
use common\library\privilege_v3\PrivilegeConstants;
use common\library\privilege_v3\PrivilegeService;
use common\library\promotion\PromotionFilter;
use common\library\usage_record\UsageRecordService;

/**
 * Copyright (c) 2012 - 2017 Xiaoman.All Rights Reserved
 * Author: nuxse
 * Data: 2017/6/5
 */
class AccountController extends PrometheusController
{


	public function actionSystemFunctional() {

		$this->render('/account/systemFunctional.js');

	}

	public function actionFTImport(){

		$this->render('/account/fTImport.js');
	}


//	public function actionClientManage() {
//
//		$this->render('/account/clientManage.js');
//	}

	public function actionClientExposeLimit() {

		$this->render('/account/clientExposeLimit.js');
	}


	public function actionClient() {

		$this->render('/account/client.js');
	}

    public function actionIndex()
    {
        $this->render('index', [
            'has_account_manage_delete_privilege' => $this->hasPrivilege('account.manage.delete'),
        ]);
    }

    public function actionLimit()
    {
        $this->render('limit');
    }

    public function actionLoginEvent()
    {
        $this->render('/account/login_event.js');
    }

    public function actionPasswordLog()
    {
        $this->render('/account/password_log.js');
    }

    public function actionTask()
    {
        $this->render('/account/task.js');
    }

    public function actionMobile()
    {
        $this->render('/account/mobile.js');
    }

    public function actionDiskSpace()
    {
        $this->render('/account/disk_space');
    }

    public function actionLoginMangeView()
    {
        $this->render('/account/login_mange.js');
    }

    public function actionSns()
    {
        $this->render('/account/sns.js');
    }

    public function actionTestAccountManage() {

        $this->render('/account/test_account_manage.js');
    }

    public function actionGetUpgradeList()
    {
        $this->render('/account/getUpgradeList.js');
    }

    /**
     * 根据用户email 或者 userId 获取用户详情数据
     * @param $search_key
     */
    public function actionInfo($type, $search_key)
    {
        try {
            if (empty($search_key)) {
                throw new RuntimeException('search_key is invalid');
            }
            $search_key = trim($search_key);
            switch ($type) {
                case 'user':
                    return $this->getInfoByUser($search_key);
                    break;
                case 'client':
                    return $this->getInfoByClient($search_key);
                    break;
                case 'email':
                    return $this->getInfoByEmail($search_key);
            }

        } catch (Exception $e) {
            return $this->fail($e->getCode(), $e->getMessage());
        }
    }


    private function getInfoByUser($search_key)
    {
        //获取user数据
        if (filter_var($search_key, FILTER_VALIDATE_EMAIL)) {
            $user = User::getUserObjectByEmail($search_key);
            if (!$user->getClientId()){
                // ames 邮箱查询
                $user_id = PrometheusService::getUserIdByAmesMail($search_key);
                $user = User::getUserObject($user_id);
            }

        } else {
            if (is_numeric($search_key)) {
                $user = User::getUserObject($search_key);
                if (!$user->getClientId()) {
                    // user_eid查询
                    $user_id = \common\library\account\Helper::getUserIdByUserEid($search_key);
                    $user = User::getUserObject($user_id);
                }
            } else {
                //用户名称模糊匹配
                $user = PrometheusService::getUserByNickname($search_key);
            }
        }
        if (!$user || !$user->getClientId()) {
            throw new RuntimeException('user not found');
        }
        //获取client数据
        $client = \common\library\account\Client::getClient($user->getClientId());
        if ($client->isNew()) {
            throw new RuntimeException('client not found');
        }
        $userInfoObj = $user->getInfoObject();

        //获取绑定邮件数据
        $userMailList = PrometheusService::getUserMailList($user->getUserId(), $user->getClientId());
        //检查是否具备查看邮箱密码权限
        $privilegeList = $this->getUserPrivilegeList();
        if (!in_array('account.mail_password', $privilegeList)) {
            $userMailListLen = count($userMailList);
            for ($i=0; $i<$userMailListLen; $i++){
                $userMailList[$i]['email_pwd'] = '***';
            }
        }

        //获取数据库连接信息
        $dbInfo = [];
        if ($this->isAdminRole() || in_array('account.db_info', $privilegeList)) {
            $dbInfo = PrometheusService::getDbInfo($client->client_id);
        }

        $userAccount = (new \common\library\account\Account())->loadById($user->getUserId());

        $data = [
            'userInfo' => [
                'user_id' => $user->getUserId(),
                'nickname' => $user->getNickname(),
                'account' => $user->getEmail(),
                'eid' => $userInfoObj->getEid() ,
                'user_eid' =>$userInfoObj->getUserEid(),
                'can_delete' => ($client->mysql_set_id == 0 || strtotime($client->valid_to) < time()),
                'can_freeze' => $this->hasPrivilege('account.manage')?1:0,
                'enable_flag' => $user->isEmpty()? 0 : $user->info()->enable_flag
            ],
            'bind_mails' => [
                'num' => count($userMailList),
                'list' => $userMailList
            ],
            'client' => [
                'client_id' => $user->getClientId(),
                'client_name' => $client ? $client->name : '',
                'master_account' => $client ? $client->master_account : '',
                'mysql_set_id' => $client->mysql_set_id,
                'pgsql_set_id' => $client->pgsql_set_id,
                'enable_flag' => $client->enable_flag,
                'systems' => implode(',', \common\library\privilege_v3\PrivilegeService::getInstance($client->client_id)->getSystemIds())
            ],
            'db' => $dbInfo,
            'clientPrivilegeList' => $this->getClientPrivilegeList($client->client_id),
            'userPrivilegeList' => $this->getClientUserPrivilegeList($client->client_id, $user->getUserId()),
            'userRoleList' => $this->getUserRoleList($client->client_id, $user->getUserId()),
            'clientUserList' => $this->getClientUserList($client->client_id),
            'wx_info' => $userAccount->getAttributes(['wx_openid','wx_nickname','wx_safelogin'])
        ];
        return $this->success($data);
    }

    private function getInfoByClient($search_key)
    {
        //client
        if (is_numeric($search_key)) {
            $client = \common\library\account\Client::getClient($search_key);
        } else {
            //client模糊匹配 取第一条数据  这里会有问题，模糊匹配到多条数据的时候数据返回不一定是需要的
            $client = PrometheusService::getClientByName($search_key);
        }
        if (!$client ||  $client->isNew()) {
            // eid 查询
            if (is_numeric($search_key)){
                $clientObj = \Client::findByEid($search_key);
                if (empty($clientObj)) {
                    throw new RuntimeException('client not found');
                }
                $client = \common\library\account\Client::getClient($clientObj->client_id);
            }else{
                throw new RuntimeException('client not found');
            }
        }
        $user = $client->getMasterUser();
        $userInfoObj = $user->getInfoObject();

        //获取绑定邮件数据
        $userMailList = PrometheusService::getUserMailList($user->getUserId(), $user->getClientId());
        //检查是否具备查看邮箱密码权限
        if (!$this->hasPrivilege('account.mail_password')) {
            $userMailListLen = count($userMailList);
            for ($i=0; $i<$userMailListLen; $i++){
                $userMailList[$i]['email_pwd'] = '***';
            }
        }

        //获取数据库连接信息
        $dbInfo = [];
        if ($this->hasPrivilege('account.db_info')) {
            $dbInfo = PrometheusService::getDbInfo($client->client_id);
        }
        $data = [
            'userInfo' => [
                'user_id' => $user->hasInfo()?$user->getUserId():'',
                'nickname' => $user->hasInfo()?$user->getNickname():'',
                'account' => $user->hasInfo()?$user->getEmail():'',
                'eid' => $user->hasInfo() ? $userInfoObj->getEid() : '',
                'user_eid' => $user->hasInfo() ? $userInfoObj->getUserEid() : '',
            ],
            'bind_mails' => [
                'num' => count($userMailList),
                'list' => $userMailList
            ],
            'client' => [
                'client_id' => $user->getClientId(),
                'client_name' => $client->name,
                'master_account' => $client->master_account,
                'mysql_set_id' => $client->mysql_set_id,
                'pgsql_set_id' => $client->pgsql_set_id,
                'systems' => implode(',', \common\library\privilege_v3\PrivilegeService::getInstance($client->client_id)->getSystemIds()),
                'valid_time' => $client->valid_from.' 至 '.$client->valid_to,
            ],
            'modules' => $this->getClientModulesList($client->client_id),
            'db' => $dbInfo,
            'clientPrivilegeList' => $this->getClientPrivilegeList($client->client_id),
            'userPrivilegeList' => $this->getClientUserPrivilegeList($client->client_id, $user->getUserId()),
            'clientUserList' => $this->getClientUserList($client->client_id),
            'clientDepartmentList' => $this->getClientDepartmentList($client->client_id),
            'isAllowRefreshPrivilege' => $this->hasPrivilege('account.refresh_privilege')
        ];
        return $this->success($data);
    }

    /**
     * 邮箱反查绑定的用户
     * @param $search_key
     * @return false|string
     * <AUTHOR>
     * @date 2019-03-20 17:06
     */
    private function getInfoByEmail($search_key)
    {
        if (filter_var($search_key, FILTER_VALIDATE_EMAIL)) {

            $user = \UserMail::model()->find("email_address=:email_address and enable_flag=1", [":email_address" => $search_key]);
            if (!$user) {
                throw new RuntimeException('email  not found');
            }

            //分两种情况
            if ($user->user_id) {
                //有user_id
                $user = User::getUserObject($user->user_id);

                if (!$user || !$user->getClientId()) {
                    throw new RuntimeException('user not found');
                }
                $userInfoObj = $user->getInfoObject();

                $userInfo = [
                    'user_id' => $user->getUserId(),
                    'nickname' => $user->getNickname(),
                    'account' => $user->getEmail(),
                    'eid' => $userInfoObj->getEid() ,
                    'user_eid' =>$userInfoObj->getUserEid(),
                ];

                $client_id = $user->getClientId();

                //获取client数据
                $client = \common\library\account\Client::getClient($user->getClientId());
                if ($client->isNew()) {
                    throw new RuntimeException('client not found');
                }

                //获取绑定邮件数据
                $userMailList = PrometheusService::getUserMailList($user->getUserId(), $user->getClientId());

            } else {

                //没有user_id
                $userInfo = [];
                $client_id = $user->client_id;

                //获取client数据
                $client = \common\library\account\Client::getClient($client_id);
                if ($client->isNew()) {
                    throw new RuntimeException('client not found');
                }

                //获取绑定邮件数据
                $userMailList = [];

            }

            //检查是否具备查看邮箱密码权限
            $privilegeList = $this->getUserPrivilegeList();
            if (!in_array('account.mail_password', $privilegeList)) {
                $userMailListLen = count($userMailList);
                for ($i = 0; $i < $userMailListLen; $i++) {
                    $userMailList[$i]['email_pwd'] = '***';
                }
            }

            //获取数据库连接信息
            $dbInfo = [];
            if ($this->isAdminRole() || in_array('account.db_info', $privilegeList)) {
                $dbInfo = PrometheusService::getDbInfo($client->client_id);
            }

            $data = [
                'bind_mails' => [
                    'num' => count($userMailList),
                    'list' => $userMailList
                ],
                'client' => [
                    'client_id' => $client_id,
                    'client_name' => $client ? $client->name : '',
                    'master_account' => $client ? $client->master_account : '',
                    'mysql_set_id' => $client->mysql_set_id,
                    'pgsql_set_id' => $client->pgsql_set_id,
                ],
                'db' => $dbInfo
            ];

            !empty($userInfo) && $userInfo['can_delete'] = ($client->mysql_set_id == 0 || strtotime($client->valid_to) < time());
            $data['userInfo'] = $userInfo;

            return $this->success($data);

        } else {
            throw new RuntimeException('email格式不对');
        }
    }


    private function getClientPrivilegeList($client_id)
    {
        $privilegeService = \common\library\privilege_v3\PrivilegeService::getInstance($client_id);
        $functionalIds = $privilegeService->getFunctionalIds();
        $result = [];
        foreach ($functionalIds as $functionalId) {
            $result[] = [
                'key' => $functionalId,
                'value' => \Yii::t('privilege', $functionalId),
            ];
        }
        return $result;
    }

    private function getClientModulesList($client_id)
    {
        $list = \PrivilegeClientModule::model()
            ->findAll('client_id=:client_id and enable_flag=:enable_flag', [
                ':client_id'   => $client_id,
                ':enable_flag' => PrivilegeConstants::ENABLE_FLAG_TRUE
            ]);

        //开通了试用权限取消移除
        $promotionFilter = new PromotionFilter($client_id);
        $promotionFilter->select(['module']);
        $data= $promotionFilter->find()->getAttributes(['module']);
        $trialModuleIds = array_column($data,'module');
        $result =  array_map(function ($item)use ($trialModuleIds){
            return ['key' => $item['module_id'], 'values' => implode(',',json_decode($item['functional'], true)), 'update_time' => $item['update_time'], 'create_time'=> $item['create_time'] ,'is_trial' => in_array($item['module_id'], $trialModuleIds)];
        }, $list);

        return $result;
    }

    private function getClientUserList($client_id)
    {
        $sql = "SELECT user_id,email,nickname,enable_flag FROM tbl_user_info WHERE client_id=:client_id";
        $list = \Yii::app()->account_base_db->createCommand($sql)->queryAll(true,[':client_id' => $client_id]);
        foreach ($list as &$row) {
            switch ($row['enable_flag']) {
                case \common\library\account\UserInfo::ENABLE_TRUE:
                    $row['enable_flag'] = '正常';
                    break;
                case \common\library\account\UserInfo::ENABLE_FLASE:
                    $row['enable_flag'] = '冻结';
                    break;
                case \common\library\account\UserInfo::ENABLE_DELETE:
                    $row['enable_flag'] = '删除';
                    break;
                default:
                    $row['enable_flag'] = '';
                    break;
            }
        }
        return $list;
    }

    private function getClientDepartmentList($client_id)
    {
        $sql = "SELECT id,name,parent_id,prefix,enable_flag,create_time,update_time FROM tbl_department WHERE client_id=:client_id order by id asc";
        $list = \Yii::app()->account_base_db->createCommand($sql)->queryAll(true,[':client_id' => $client_id]);
        foreach ($list as &$row) {
            switch ($row['enable_flag']) {
                case \common\library\account\UserInfo::ENABLE_TRUE:
                    $row['enable_flag'] = '正常';
                    break;
                case \common\library\account\UserInfo::ENABLE_FLASE:
                    $row['enable_flag'] = '删除';
                    break;
                default:
                    $row['enable_flag'] = '';
                    break;
            }
        }
        return $list;
    }

    private function getClientUserPrivilegeList($client_id, $user_id)
    {
        $result = [];
        $privilegeService = \common\library\privilege_v3\PrivilegeService::getInstance($client_id, $user_id);
        $privileges = $privilegeService->getPrivileges();
        $scopeName = [
            4 => '所有人',
            3 => '本部门及下级部门',
            2 => '本部门',
            1 => '本人',
            5 => '本人及下属',
            6 => '本人及下属及本部门',
        ];
        foreach ($privileges as $privilegeId => $privilegeInfo) {
            $result[] = [
                'key' => $privilegeId,
                'value' => \Yii::t('privilege', $privilegeId),
                'role' => $scopeName[$privilegeInfo['scope']] ?? '',
            ];
        }

        return $result;
    }

    protected function getUserRoleList($clientId, $userId)
    {
        $result = [];
        $userRoleList = \common\library\privilege_v3\PrivilegeService::getInstance($clientId, $userId)->getOneUserRoleList();

        foreach ($userRoleList as $roleId => $roleInfo) {
            $result[] = [
                'key' => $roleId,
                'value' => $roleInfo['role_name']
            ];
        }

        return $result;
    }

    /**
     *  查询用户上限
     * @param $client_id
     * @return string
     */
    public function actionClientInfo($client_id)
    {
        try {
            if (empty($client_id)) {
                throw  new RuntimeException('client_id is invalid');
            }

            $client = \common\library\account\Client::getClient($client_id);
            if ($client->isNew()) {
                throw new RuntimeException('client not found');
            }

            $data = [
                'client' => [
                    'client_id' => $client->client_id,
                    'name' => $client->name,
                    'user_num' => $client->user_num,
                    'disk_space' => Util::formatBytes($client->disk_space)
                ]
            ];
            return $this->success($data);
        }catch (Exception $e) {
            return $this->fail($e->getCode(), $e->getMessage());
        }
    }

    /**
     *  修改用户上限
     * @param $client_id
     * @return string
     */
    public function actionSetAccountCount($client_id, $system_id, $num = 0, $business_id = 0)
    {
        try {
            if (empty($client_id) || empty($system_id) || !is_numeric($num)) {
                throw  new RuntimeException('params is invalid');
            }

            $result = \common\modules\internal\library\account\ClientService::updateUserNum($client_id, $num, 'cover', '', 0, $system_id, $business_id);
            if (empty($result)) {
                throw new RuntimeException('修改用户上限失败');
            }

            return $this->success('');

        } catch (Exception $e) {
            return $this->fail($e->getCode(), $e->getMessage());
        }
    }

    public function actionLoginEventList($account, $cur_page = 1, $page_size = 50)
    {
        if (is_numeric($account))
        {
            $user = User::getUserObject($account);
        }
        else
        {
            $user = User::getUserObjectByEmail($account);
        }

        if ($user->isEmpty())
            throw new RuntimeException('找不到用户');

        $list = new \common\library\account\LoginEventList();
        if (is_numeric($account)) {
            $list->setUserId($user->getUserId());
        } else {
            $list->setAccount($user->getEmail());
        }
        $list->setIsVisible(null);
        $list->setLimit($page_size);
        $list->setOffset($page_size * ($cur_page - 1));

        return $this->success(['account' => $user->getEmail(), 'list' => $list->find(), 'count' => $list->count()]);
    }

    public function actionPasswordLogList($account, $cur_page=1, $page_size=50)
    {
        if (is_numeric($account))
        {
            $user = User::getUserObject($account);
        }
        else
        {
            $user = User::getUserObjectByEmail($account);
        }

        if ($user->isEmpty())
            throw new RuntimeException('找不到用户');

        $list = UserPasswordLog::findList($user->getEmail(),$page_size, ($cur_page -1)*$page_size);
        $count = UserPasswordLog::findCount($user->getEmail());
        $this->success(['account'=>$user->getEmail(), 'list'=> $list, 'count'=> $count]);

    }

    public function actionProductTask($account, $cur_page=1, $page_size=50)
    {
        if (is_numeric($account))
        {
            $user = User::getUserObject($account);
        }
        else
        {
            $user = User::getUserObjectByEmail($account);
        }

        if ($user->isEmpty())
            throw new RuntimeException('找不到用户');

        User::setLoginUser($user);

        $list = new ProductImportList($user->getUserId());
        $list->setOffset(($cur_page - 1) * $page_size);
        $list->setLimit($page_size);

        $importList = array();
        $total = $list->count();
        if ($total) {
            $importList = $list->find();
        }
        return $this->success(['count' => (int)$total, 'list' => $importList]);
    }

    public function actionExportTask($account, $cur_page = 1, $page_size = 50)
    {
        if (is_numeric($account)) {
            $user = User::getUserObject($account);
        } else {
            $user = User::getUserObjectByEmail($account);
        }

        if ($user->isEmpty())
            throw new RuntimeException('找不到用户');

        User::setLoginUser($user);

        $exportFilter = new  ExportFilter($this->getLoginUserClientId());
        $exportFilter->limit($page_size, $cur_page);
        $exportFilter->user_id = $this->getLoginUserId();
        $batchExport = $exportFilter->find();
        $batchExport->getFormatter()->webListSetting();
        $data = $batchExport->getAttributes();
        return $this->success(['count' => (int)$exportFilter->count(), 'list' => $data]);
    }

    public function actionEndProductTask($user_id, $task_id)
    {
        $user = User::getUserObject($user_id);
        User::setLoginUser($user);

        if( !$task_id )
            throw new RuntimeException('任务不存在');

        $task = new \common\library\product\import\ProductImportTask($user->getClientId(), $user_id);
        $task->loadByTaskId($task_id);

        $task->failed("manual ended");

        $this->success('');
    }


    public function actionCustomerTask($account, $cur_page=1, $page_size=50)
    {
        if (is_numeric($account))
        {
            $user = User::getUserObject($account);
        }
        else
        {
            $user = User::getUserObjectByEmail($account);
        }

        if ($user->isEmpty())
            throw new RuntimeException('找不到用户');

        User::setLoginUser($user);

        $list = new CustomerImportList($user->getUserId());
        $list->setType(Constants::TYPE_CUSTOMER);
        $list->setOffset(($cur_page - 1) * $page_size);
        $list->setLimit($page_size);

        return $this->success([
            'list' => $list->find(),
            'count' => (int)$list->count()
        ]);
    }


    public function actionEndCustomerTask($user_id, $task_id)
    {
        $user = User::getUserObject($user_id);
        User::setLoginUser($user);

        if( !$task_id )
            throw new RuntimeException('任务不存在');

        $task = new CustomerImportTask($task_id, Constants::TYPE_CUSTOMER);
        $task->fail('end');

        $this->success('');
    }

    public function actionLeadTask($account, $cur_page=1, $page_size=50)
    {
        if (is_numeric($account))
        {
            $user = User::getUserObject($account);
        }
        else
        {
            $user = User::getUserObjectByEmail($account);
        }

        if ($user->isEmpty())
            throw new RuntimeException('找不到用户');

        User::setLoginUser($user);

        $list = new CustomerImportList($user->getUserId());
        $list->setType(Constants::TYPE_LEAD);
        $list->setOffset(($cur_page - 1) * $page_size);
        $list->setLimit($page_size);

        return $this->success([
            'list' => $list->find(),
            'count' => (int)$list->count()
        ]);
    }


    public function actionEndLeadTask($user_id, $task_id)
    {
        $user = User::getUserObject($user_id);
        User::setLoginUser($user);

        if( !$task_id )
            throw new RuntimeException('任务不存在');

        $task = new CustomerImportTask($task_id, Constants::TYPE_LEAD);
        $task->fail('end');

        $this->success('');
    }


    public function actionDownloadFile($account, $file_id)
    {
        if (is_numeric($account))
        {
            $user = User::getUserObject($account);
        }
        else
        {
            $user = User::getUserObjectByEmail($account);
        }

        if ($user->isEmpty())
            throw new RuntimeException('找不到用户');

        User::setLoginUser($user);

        $file = new AliyunUpload();
        $file->loadByFileId($file_id, $user->getUserId());
        $this->redirect($file->getFileUrl());
    }

    public function actionFile($account, $file_id)
    {
        if (is_numeric($account))
        {
            $user = User::getUserObject($account);
        }
        else
        {
            $user = User::getUserObjectByEmail($account);
        }

        if ($user->isEmpty())
            throw new RuntimeException('找不到用户');

        User::setLoginUser($user);

        $file = new AliyunUpload();
        $file->loadByFileId($file_id, $user->getUserId());
        $data = [
            'file_id' => $file->getFileId(),
            'file_name' => $file->getFileName(),
            'file_size' => $file->getFileSize(),
            'file_url' => $file->getFileUrl(),
        ];

        $this->success([$data]);
    }

    public function actionLogout()
    {
        $prometheusKey = $_COOKIE['prometheus_key']??'';

        if( !empty($prometheusKey) ) {
            try {
                \common\library\account\service\LoginService::logout($prometheusKey);
            } catch (Exception $e) {

            }
            setcookie('prometheus_key',null,0,'xiaoman');
        }
        $this->redirect(Yii::app()->params['host']['login_url'] . '/login');
    }

    public function actionDeleteUser($account, $force = false)
    {
        $account = trim($account);
        try {
            //获取user数据
            if (filter_var($account, FILTER_VALIDATE_EMAIL)) {
                $user = User::getUserObjectByEmail($account);
            } else {
                if (is_numeric($account)) {
                    $user = User::getUserObject($account);
                } else {
                    //用户名称模糊匹配
                    $user = PrometheusService::getUserByNickname($account);
                }
            }
            if (!$user || !$user->getClientId()) {
                throw new RuntimeException('user not found');
            }
            if ($user->getInfo()['enable_flag'] == \common\library\account\UserInfo::ENABLE_DELETE) {
                return $this->success([
                    'deleted' => true,
                ]);
            }
            //获取client数据
            $client = \common\library\account\Client::getClient($user->getClientId());
            if ($client->isNew()) {
                throw new RuntimeException('client not found');
            }

            if (!$force && $client->mysql_set_id > 0 && strtotime($client->valid_to) > time()) {
                throw new RuntimeException('仅支持删除未购买或已过期用户');
            }

            $clientId = $client->client_id;
            $userId = $user->getUserId();
            \common\library\privilege_v3\PrivilegeService::getInstance($clientId, $userId)->removeRole(\common\library\privilege_v3\PrivilegeConstants::ROLE_CRM_ADMIN);

            $accountObj = (new \common\library\account\Account())->loadById($userId);
            if ($accountObj->enable_flag != \common\library\account\Account::ENABLE_FREEZE) {
                // 删除账号 移除所有系统
                InterfaceService::freezeUser($clientId, $userId, false, null, true);
                \LogUtil::info(__FUNCTION__ . " 禁用所有系统 ： user {$userId}");
            }

            $a = new \common\library\account\action\Delete($account);
            if (!$a->run()) {
                $error = $a->getErrorInfo();
                return $this->json($error['code'] ?? ErrorCode::CODE_FAIL, $error['msg'], null, false);
            }
            LogUtil::info(date('Y-m-d H:i:s') . "删除用户：{$account}");

            // 删除邮箱
            $userMailList = \UserMail::findAllByCondition($clientId, 'email_address = :email and user_id = :user_id', [':email' => $account, ':user_id' => $userId]);
            if ($userMailList) {
                foreach ($userMailList as $userMail) {
                    $userMail->email_address = 'del_' . $userMail->email_address;
                    $userMail->valid_flag = 1;
                    $userMail->update(['email_address', 'valid_flag']);
                    if ($userMail->client_id) {
                        \common\library\email\UserMailCacheableRepo::instance($userMail->client_id)->refreshCache($userMail->user_mail_id);
                    }
                }
            }
            if ($client->master_account == $account) {
                // 主账号清除
                $client->master_account = 'del_' . $account;
                $client->update(['master_account']);
                \LogUtil::info("clean client master account for $account");
                /**
                 * @var CDbConnection $xiaomanDb
                 */
                $xiaomanDb = Yii::app()->xiaoman_db;
                $deleted = $xiaomanDb->createCommand("update tbl_register set status=0 where client_id = $clientId and email = '$account'")->execute();
                \LogUtil::info("delete oss register info for $account");
            }

            $this->success([
                'deleted' => true,
            ]);
        } catch (Exception $e) {
            return $this->fail($e->getCode(), $e->getMessage());
        }
    }


    public function actionFreezeUser($user_id, $freeze)
    {
        $user = User::getUserObject($user_id);
        if( $user->isEmpty() || $user->info()->enable_flag == \common\library\account\UserInfo::ENABLE_DELETE)
            throw  new RuntimeException('操作失败, 用户不存在!');

        if( $user->isSuperAdmin() )
            throw  new RuntimeException('超级管理员禁止操作!');


        $clientId = $user->getClientId();
        //冻结
        if($freeze == 1){
            // prometheus 工具 禁用账号为 移除所有系统权限
            InterfaceService::freezeUser($clientId,$user_id, false, null, true);
            //回收未使用的公司营销数
            $accountObj = new \common\library\account\Account();
            $accountObj->loadById($user_id);
            $userInfo = $accountObj->getUserInfoObj();
            $currentCompanyCount = $userInfo->current_count_company;
            if ($currentCompanyCount)
            {
                \User::setLoginUserById($user_id);
                $userInfo->freeEdmCount($currentCompanyCount);
                \LogUtil::info("user_id : {$userInfo->user_id} 回收edmCount:{$currentCompanyCount} ");
            }
        }
        else{
            InterfaceService::unFreezeUser($clientId,$user_id);
        }

        return $this->success([
            'user_id' => $user_id,
            'freeze' => $freeze ? 1 : 0,
        ]);
    }

    /**
     * 手机号码反查绑定的用户
     * @param $search_key
     * @return false|string
     * <AUTHOR>
     * @date 2019-03-20 17:06
     */
    public function actionGetInfoByMobile($search_key)
    {
        $search_key = trim($search_key);
        if( empty($search_key) )
        {
            throw new RuntimeException('请输入内容');
        }

        $userInfo = UserInfo::findByMobile($search_key);

        if( !$userInfo && filter_var($search_key, FILTER_VALIDATE_EMAIL))
        {
            $userInfo = UserInfo::findByEmail($search_key);
        }

        if( !$userInfo )
        {
            throw new RuntimeException('没有找到相关用户');
        }

        $client_id = $userInfo->client_id;

        //获取client数据
        $client = \common\library\account\Client::getClient($client_id);

        $info = [
            'user_id' => $userInfo->user_id??'',
            'nickname' => $userInfo->nickname??'',
            'account' => $userInfo->email??'',
            'user_mobile' => $userInfo->user_mobile??'',
            'ames_mobile' => $userInfo->ames_mobile??'',
            'safe_login' => intval($userInfo->safe_login??0),
            'client_id' => $client_id,
            'client_name' => $client ? $client->name : '',
            'master_account' => $client ? $client->master_account : '',
        ];

        $this->success($info);
    }


    public function actionUnbindMobile($user_id, $user_mobile)
    {
        $user = new \common\library\account\UserInfo($user_id);

        if( $user->user_mobile != $user_mobile )
        {
            throw  new RuntimeException('此账号和手机对应不上，不能解绑手机！');
        }

        LogUtil::info(date('Y-m-d H:i:s'). "用户解绑手机：{$user->email}   {$user_id} {$user->user_mobile} ");

        $forceCloseSafeLogin = false;
        if($user->safe_login ){
            $user->closeSafeLogin();
            $forceCloseSafeLogin = true;
        }
        $user->unbindMobile();

        $opUser = User::getLoginUser();
        $opUserAccount = $opUser->getEmail();
        LogUtil::error("账号{$opUserAccount} 解绑了手机号码 {$user_mobile}  所属用户:{$user->email} ". ($forceCloseSafeLogin?'强制关闭了安全登陆':' 未开启安全登陆'));

        $this->success('');
    }

    public function actionSetDiskSpace($client_id , $space, $unit = 'GB')
    {
        try {
            if (empty($client_id) || !is_numeric($space)) {
                throw  new RuntimeException('params is invalid');
            }

            $client = PrometheusService::updateDiskSpace($client_id, $space, $unit);
            if (!$client) {
                throw new RuntimeException('修改失败');
            }

            $data = [
                'client' => [
                    'client_id' => $client->client_id,
                    'name' => $client->name,
                    'disk_space' => Util::formatBytes($client->disk_space)
                ]
            ];

            return $this->success($data);

        } catch (Exception $e) {
            return $this->fail($e->getCode(), $e->getMessage());
        }
    }

    public function actionUnlockAccount($account)
    {
       $ret =  \common\library\account\service\LoginService::releaseLockAccount($account);
       if( !$ret )
           throw new RuntimeException('解锁失败');

        $opUser = User::getLoginUser();
        $opUserAccount = $opUser->getEmail();
        LogUtil::error("账号{$opUserAccount} 解锁了账号 {$account}  ");
       $this->success('');
    }

    public function actionChangePassword($account, $password)
    {
        $password = hash('sha256', $password);
        $device_id = 'prometheus';
        $type = \common\library\account\action\Password::DIRECT_SET_PASSWORD_TYPE_RESET;
        $opUser = User::getLoginUser();
        $op_user_id = $opUser->getUserId();
        $changeUser = User::getUserObjectByEmail($account);
        if (!$changeUser) {
            throw new RuntimeException('not found');
        }
        $clientId = $changeUser->getClientId();
        if (Yii::app()->params['env'] != 'test') {
            $client = \common\library\account\Client::getClient($clientId);
            if ($client->client_type != \common\library\account\Client::CLIENT_TYPE_INTERNAL)
            {
                throw new RuntimeException("client: $clientId not allow!");
            }
        }

        $a = new \common\library\account\action\Password($account);
        $a->setNewPassword($password);
        $a->setVerify(false);
        $a->setType($type);
        $a->setOpUserId($op_user_id);
        $a->setDeviceId($device_id);

        if (!$a->run()) {
            $error = $a->getErrorInfo();
            return $this->json($error['code'] ?? ErrorCode::CODE_FAIL, $error['msg'], $error, false);
        }

        $opType = $type == \common\library\account\action\Password::DIRECT_SET_PASSWORD_TYPE_UPDATE ? '修改' : '重置';
        LogUtil::info(date('Y-m-d H:i:s') . "userId:{$op_user_id} {$opType} 账号的{$account}密码, device_id: {$device_id}");
        $this->success([
            'result' => true,
        ]);
    }

    public function actionDeleteUserEmail($account)
    {
        $account = trim($account);
        $userMail = \UserMail::findByEmail($account);
        if (!$userMail) {
            throw new RuntimeException('email  not found');
        }
        //分两种情况
        if ($userMail->user_id) {
            if (\User::getUserObject($userMail->user_id)->info()->isEnable()) {
                throw new RuntimeException('email bind user ' . $userMail->user_id);
            }
        }

        $userMail->enable_flag = 0;
        $userMail->email_address = 'del_' . $userMail->email_address;
        $userMail->valid_flag = 1;
        $userMail->update(['email_address', 'valid_flag', 'enable_flag']);

        if ($userMail->client_id) {
            \common\library\email\UserMailCacheableRepo::instance($userMail->client_id)->refreshCache($userMail->user_mail_id);
        }

        \LogUtil::info('delete emails ' . $account . " clientID: " . $userMail->client_id);

        return $this->success([
            'result' => true,
        ]);
    }

    public function actionRebuildDepartmentRecursive($client_id)
    {
        \common\library\department\Helper::rebuildDepartmentRecursive($client_id);
        $client = \common\library\account\Client::getClient($client_id);
        $client->deleteCache();

        return $this->success([]);
    }

    public function actionRefreshPrivilege($client_id)
    {
        if (empty($client_id)){
            throw  new RuntimeException('client_id is empty!');
        }

        $privilegeService = PrivilegeService::getInstance($client_id);
        $systemId = $privilegeService->getMainSystemId();
        if (!$systemId) {
            \LogUtil::info("skip for client:$client_id because no mainSy");
            throw  new RuntimeException('client is invalid!');
        }

        $moduleIds = $privilegeService->getModulePrivilege()->getModuleIds();
        $pgDb = \PgActiveRecord::getDbByClientId($client_id);
        $mysqlDb = \ProjectActiveRecord::getDbByClientId($client_id);
        if (!$pgDb || !$mysqlDb) {
            \LogUtil::info("client db is null：$client_id  ignore and continue");
            throw  new RuntimeException('client_id is invalid!');
        }

        \PgActiveRecord::setConnection($pgDb);
        \ProjectActiveRecord::setConnection($mysqlDb);
        $privilegeService->initClient($systemId, $privilegeService->getAdminUserId(), false, true, $moduleIds);
        return $this->success([]);
    }


    public function actionCleanClientCache($client_id)
    {
        \common\library\account\Helper::cleancClientCache($client_id);
        return $this->success([]);
    }


    public function actionUserCustomerContractList($client_id, $sns_type, $sns_id)
    {
        $contactList = \common\library\sns\customer\CustomerContactHelper::getContactListBySnsId(
            $client_id,
            $sns_type,
            $sns_id
        );

        return $this->success($contactList);
    }

    public function actionCleanUserContactMessage($client_id, $user_contact_id)
    {
        \common\library\sns\customer\CustomerContactHelper::cleanUserContactMessage(
            $client_id,
            $user_contact_id
        );

        return $this->success([]);
    }

    public function actionUnbindUserContact($client_id, $user_contact_id)
    {
        \common\library\sns\customer\CustomerContactHelper::unbindUserContact(
            $client_id,
            $user_contact_id
        );

        return $this->success([]);
    }

    public function actionAliasEmail($email)
    {
        $aliasMailList = \UserMailAlias::model()->findAllByAttributes([
            'alias_address' => $email,
            'enable_flag' => 1,
        ]);

        return $this->success(array_map(function ($alias) { return $alias->getAttributes();}, $aliasMailList));
    }

    public function actionDeleteAliasEmail($email)
    {
        LogUtil::info(date('Y-m-d H:i:s') . "解绑别名邮箱：{$email}");

        $aliasMail = \UserMailAlias::model()->findByAttributes([
            'alias_address' => $email,
            'enable_flag' => 1,
        ]);
        if ($aliasMail) {
            $aliasMail->enable_flag = 0;
            $aliasMail->update(['enable_flag']);
        }

        return $this->success([]);
    }

    public function actionLogoutAccount($account)
    {
        LogUtil::info(date('Y-m-d H:i:s') . "退出账号登录：{$account}");
        $userAccount = UserAccount::findByAccount($account);

        \common\library\account\service\LoginService::resetSession([$userAccount->user_id]);
        return $this->success();
    }

    public function actionCloseVerifyCode($account)
    {
        LogUtil::info(date('Y-m-d H:i:s') . "关闭验证码账号：{$account}");
        $userAccount = UserAccount::findByAccount($account);
        $acc = $userAccount->account;
        if (empty($acc)) {
            throw new RuntimeException('账号为空，无法清除');
        }
        $redis = RedisService::getInstance('redis_passport_session');
        $redis->del(['Passport:FailAccount:' . $acc]);
        return $this->success();
    }

    public function actionCheckPskey($key)
    {
        LogUtil::info(date('Y-m-d H:i:s') . "检查登录：{$key}");

        try {
          $content =  \common\library\account\service\LoginService::checkSkey($key);
          if( !empty($content['customData']))
          {
              $content['customData'] = json_decode($content['customData'], true);
          }
        }catch (\Throwable $e)
        {
            $content = $e->getMessage();
        }
        return $this->success($content);
    }

    public function actionCleanLoginRisk($key)
    {
        LogUtil::info(date('Y-m-d H:i:s') . "解除：{$key}");

        $ret = \common\library\risk\service\LoginRiskService::setRiskLoginData($key, 0);
        return $this->success('');
    }

    /**
     * 导出系统使用记录汇总
     * @param int $client_id
     * @param string $start_date    日期范围-开始
     * @param string $end_date      日期范围-结束
     * @throws ProcessException
     */
    public function actionExportUsageCount(int $client_id, string $start_date, string $end_date, int $user_id = 0)
    {
        // 查询client下的用户
        $users = $this->getClientUserList($client_id);
        $users = array_filter($users, static function ($user) use($user_id): bool {
            if ($user_id) {
                return ((int) $user['user_id']) === $user_id;
            }
            return $user['enable_flag'] !== '删除';
        });

        // 查询登录天数
        $sql = "select user_id, count(distinct date) as date_count
            from tbl_user_usage_record
            where client_id=:client_id and date between :start_date and :end_date";
        $params = [
            ':client_id' => $client_id,
            ':start_date' => $start_date,
            ':end_date' => $end_date
        ];
        if ($user_id) {
            $sql .= " and user_id=:user_id ";
            $params[':user_id'] = $user_id;
        }
        $sql .= ' group by user_id ';

        $db = ProjectActiveRecord::getDbByClientId($client_id);
        $rows = $db->createCommand($sql)->queryAll(true, $params);
        $userLoginCount = array_column($rows, 'date_count', 'user_id');


        // 查询各个设备登录天数
        $sql = "select distinct user_id, client_type, count(distinct date) as date_count
            from tbl_user_usage_record
            where client_id=:client_id and date between :start_date and :end_date";
        if ($user_id) {
            $sql .= " and user_id=:user_id ";
        }
        $sql .= ' group by client_type, user_id ';
        $rows = $db->createCommand($sql)->queryAll(true, $params);
        $userDeviceCount = [];
        foreach ($rows as $row) {
            $userDeviceCount[$row['user_id']][$row['client_type']] = $row['date_count'];
        }

        $results = [];
        $results[] = ['clientId', 'userId', 'nickname', 'email', '当月登录总天数', 'web端登录天数', 'android登录天数', 'iOS登录天数', 'Windows登录天数', 'MAC登录天数'];
        foreach ($users as $user) {
            $user_id    = $user['user_id'];
            $results[] = [
                'clientId'    => $client_id,
                'userId'      => $user_id,
                'nickname'    => $user['nickname'],
                'email'       => $user['email'],
                '当月登录总天数'   => $userLoginCount[$user_id] ?? 0,
                'web端登录天数'   => $userDeviceCount[$user_id][UsageRecordService::CLIENT_TYPE_WEB] ?? 0,
                'android登录天数' => $userDeviceCount[$user_id][UsageRecordService::CLIENT_TYPE_ANDROID] ?? 0,
                'iOS登录天数'     => $userDeviceCount[$user_id][UsageRecordService::CLIENT_TYPE_IOS] ?? 0,
                'Windows登录天数' => $userDeviceCount[$user_id][UsageRecordService::CLIENT_TYPE_PC] ?? 0,
                'MAC登录天数'     => $userDeviceCount[$user_id][UsageRecordService::CLIENT_TYPE_MACOS] ?? 0,
            ];
        }
        PrometheusService::exportCSV("系统使用记录汇总表", $results);
    }

    /**
     * 导出系统使用记录明细
     */
    public function actionExportUsageRecords(int $client_id, string $start_date, string $end_date, int $user_id = 0)
    {
        // 查询client下的用户
        $users = $this->getClientUserList($client_id);
        $users = array_column($users, NULL, 'user_id');
        $sql = "select client_id, user_id, date, ip, os_version, client_type, client_version, create_time, last_access_time
                from tbl_user_usage_record where client_id=:client_id and date between :start_date and :end_date";
        $params = [
            ':client_id'  => $client_id,
            ':start_date' => $start_date,
            ':end_date'   => $end_date
        ];
        if ($user_id) {
            $sql .= " and user_id=:user_id";
            $params[':user_id'] = $user_id;
        }
        $db = ProjectActiveRecord::getDbByClientId($client_id);
        $rows = $db->createCommand($sql)->queryAll(true, $params);


        $results = [];
        $results[] = ['clientId', 'userId', 'nickname', 'email', "date", "操作系统", "客户端", "客户端版本", "首次上报", "最后上报"];
        foreach ($rows as $row) {
            $userId    = $row['user_id'];
            $user = $users[$userId];
            $results[] = [
                'clientId' => $client_id,
                'userId'   => $userId,
                'nickname' => $user['nickname'],
                'email'    => $user['email'],
                "date"     => $row['date'],
                "操作系统"     => $row['os_version'],
                "客户端"      => $row['client_type'],
                "客户端版本"    => $row['client_version'],
                "首次上报"     => $row['create_time'],
                "最后上报"     => $row['last_access_time']
            ];
        }
        PrometheusService::exportCSV("系统使用记录明细表", $results);
    }

    public function actionGetClientByConditions($client_type = '', array $systemId = [],$functional = '' , $switch = '',$cur_page = 1 , $page_size = 10){
        $systemId = array_filter($systemId);
        if(empty($systemId) && empty($functional) && empty($switch)){
            throw new Exception("systemId and functional and switch are null");
        }
        $result = PrometheusService::getClientByConditions($systemId,$client_type,$functional,$switch);
        $count = count($result);
        $list = array_slice($result,($cur_page-1)*$page_size,$page_size);
        $result = [
                'count' => $count,
                'list' => $list
            ];
        return $this->success($result);
    }

    /**
     * 解绑账号微信
     *
     * @return void
     */
    public function actionUnBundlingWx($user_id)
    {
        $this->validate([
            'user_id' => 'required|integer'
        ]);

        $user = User::getLoginUser();

        $userAccount = new \common\library\account\Account();
        $userAccount->loadById($user_id);


        \LogUtil::info("[unBundlingWx] opUser [{$user->getUserId()}] unBundling user [{$user_id}]");

        \LogUtil::info("[unBundlingWx] before wx_openid [{$userAccount->wx_openid}] wx_nickname [{$userAccount->wx_nickname}] wx_safelogin [{$userAccount->wx_safelogin}]");

        $userAccount->wx_openid = '';
        $userAccount->wx_nickname = '';
        $userAccount->wx_safelogin = 0;

        $update = $userAccount->update();

        $this->success($update);
    }

    /**
     * 清空 userinfo 的缓存
     * @param $user_id
     * @return void
     */
    public function actionCleanUserInfoCache($user_id)
    {
        $this->validate([
            'user_id' => 'required|integer'
        ]);

        $opUser = User::getLoginUser();

        $userInfo = new \common\library\account\UserInfo($user_id);

        $userInfo->deleteCache();

        \LogUtil::info(__FUNCTION__ . " opUser [{$opUser->getUserId()}] delete user cache [{$user_id}]");

        $this->success();
    }

     /**
     *  leads独立版多账号管理
     */
    public function actionLeadsAccountList()
    {
        $this->render('/account/okkiLeadsAccount.js');
    }


    public function actionLeadsAccountListQuery($client_id = 0, $cur_page = 1 , $page_size = 2)
    {
        $this->validate([
            'client_id' => 'integer',
            'cur_page' => 'integer',
            'page_size' => 'integer',
        ]);


        $where = " 1=1 ";
        $params = [];

        if (!empty($client_id)) {
            $where .= " AND client_id = :client_id";
            $params[':client_id'] = $client_id;
        }

        $offset = ($cur_page - 1) * $page_size;

        $sql = "select client_id,count(user_id) as user_num from 
(
select a.user_id,a.client_id,b.system_id from 
tbl_user_info a LEFT JOIN 
tbl_privilege_client_system b on a.client_id=b.client_id 
where a.enable_flag=".\common\library\account\UserInfo::ENABLE_TRUE." and b.enable_flag=".\common\library\account\UserInfo::ENABLE_TRUE." and b.system_id='".PrivilegeConstants::OKKI_LEADS_ID."' 
) tmp 
GROUP BY client_id HAVING user_num>=2";

        $count = \Yii::app()->account_base_db->createCommand("SELECT count(*) FROM ($sql) as leadsuser  WHERE {$where}")->queryAll(true,$params);

        $list = \Yii::app()->account_base_db->createCommand("SELECT client_id,user_num FROM ($sql) as leadsuser  WHERE {$where} LIMIT {$page_size} OFFSET {$offset}")->queryAll(true, $params);

        return $this->success(['count' => $count, 'list' => $list]);

    }

    /**
     *  leads独立版多账号管理
     */
    public function actionLeadsAccountUser()
    {
        $this->render('/account/okkiLeadsAccountUser.js');
    }

    public function actionLeadsAccountUserQuery($client_id)
    {
        $this->validate([
            'client_id' => 'integer|required',
        ]);

        $list = $this->getClientUserList($client_id);
        return $this->success(['list' => $list]);

    }
    //参数type=1查询6.29~7.5，type=2查询7.6~7.12，type=3则查询7.13当天及之后点击
    public function actionUpgradeList($type = 1){
        $key = 'crm_upgrade_company_list';
        $redis = RedisService::cache();
        switch ($type){
            case 1:
                $timePeriod = strtotime('2023-07-05 00:00:00');
                $message = '6.29~7.5';
                break;
            case 2:
                $timePeriod = strtotime('2023-07-12 00:00:00');
                $message = '7.6~7.12';
                break;
            default:
                $timePeriod = '';
                $message = '7.13当天及之后点击';
        }
        $key .= $timePeriod;
        $result = $redis->hgetall($key);
        $this->success([
            'data' => $result,
            'client_ids' => array_keys($result),
            'message' => $message,
        ]);
    }

    public function actionGetUsers()
    {
        $clientId = User::getLoginUser()->getClientId();
        $userListObj = new UserList();
        $userListObj->setClientId($clientId);
        $userListObj->setEnableFlag(\common\library\account\UserInfo::ENABLE_TRUE);
        $list = $userListObj->find();
        return $this->success($list);
    }

    public function actionGetExpServiceAccountInfo($search_key)
    {
        try {
            if (empty($search_key)) {
                throw new RuntimeException('search_key is invalid');
            }
            $db = \Yii::app()->account_base_db;
            $sql = "select id, client_id, user_id, account, password,";
            if (is_numeric($search_key)) {
                //id搜索
                $expSql = $sql . " 1 as type from tbl_exp_account where user_id = {$search_key}";
                $serviceSql = $sql . " 2 as type from tbl_service_account where user_id = {$search_key}";
                $expAccount = $db->createCommand($expSql)->queryRow();
                $account = !empty($expAccount) ? $expAccount : $db->createCommand($serviceSql)->queryRow();
            } else {
                //账号搜索
                $expSql = $sql . " 1 as type from tbl_exp_account where account = '{$search_key}'";
                $serviceSql = $sql . " 2 as type from tbl_service_account where account = '{$search_key}'";
                $expAccount = $db->createCommand($expSql)->queryRow();
                $account = !empty($expAccount) ? $expAccount : $db->createCommand($serviceSql)->queryRow();
            }
            if ($account){
                $data = [
                    "account" => $account['account'],
                    "type" => $account['type'] == 1 ? "体验账号" : ($account['type'] == 2 ? "服务账号" : ""),
                    "pwd" => $account["password"],
                    "client_id" => $account["client_id"],
                    "user_id" => $account["user_id"],
                ];
                $this->success($data);
            }else{
                $this->fail(-1,"账号不存在");
            }
        } catch (Exception $e) {
            return $this->fail($e->getCode(), $e->getMessage());
        }
    }

}
