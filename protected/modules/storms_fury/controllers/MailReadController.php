<?php

use common\library\approval_flow\ReferLockList;
use common\library\email_identity\cards\Card;
use common\library\email_identity\EmailIdentity;
use common\library\mail\setting\todo\MailTodoList;
use common\library\privilege_v3\PrivilegeConstants;
use common\library\report\sensors\events\EventTranslate;
use common\library\task\TaskConstant;
use common\library\task\TaskList;
use common\modules\storms_fury\library\mail\Helper;
use common\modules\storms_fury\library\mail\MailExtraData;
use protobuf\Common\PBErrorCode;
use protobuf\CRMCommon\PBCompressionType;
use protobuf\CRMCommon\PBFileInfo;
use protobuf\Email\PBEmailMatch;
use protobuf\Email\PBEmailMatchReq;
use protobuf\Email\PBEmailMatchRsp;
use protobuf\Email\PBEmailsMatchReq;
use protobuf\Email\PBEmailsMatchRsp;
use protobuf\Email\PBSendTimeSuggestReq;
use protobuf\Email\PBSendTimeSuggestRsp;
use protobuf\MailSync\PBCommonData;
use protobuf\MailSync\PBCommonDataItem;
use protobuf\MailSync\PBEmailIdentity;
use protobuf\MailSync\PBEmailIdentityReq;
use protobuf\MailSync\PBEmailIdentityRsp;
use protobuf\MailSync\PBFailErrorReplaceList;
use protobuf\MailSync\PBMailAllInfo;
use protobuf\MailSync\PBMailAttachmentInfo;
use protobuf\MailSync\PBMailAttachmentReq;
use protobuf\MailSync\PBMailAttachmentRsp;
use protobuf\MailSync\PBMailBaseInfo;
use protobuf\MailSync\PBMailCardReq;
use protobuf\MailSync\PBMailCardRsp;
use protobuf\MailSync\PBMailCompareReq;
use protobuf\MailSync\PBMailCompareRsp;
use protobuf\MailSync\PBMailCompensateReq;
use protobuf\MailSync\PBMailCompensateRsp;
use protobuf\MailSync\PBMailContactSummary;
use protobuf\MailSync\PBMailContent;
use protobuf\MailSync\PBMailContentReq;
use protobuf\MailSync\PBMailContentRsp;
use protobuf\MailSync\PBMailDistributeDetail;
use protobuf\MailSync\PBMailDistributeReq;
use protobuf\MailSync\PBMailDistributeRsp;
use protobuf\MailSync\PBMailDraftInfo;
use protobuf\MailSync\PBMailExposeInfo;
use protobuf\MailSync\PBMailExtraData;
use protobuf\MailSync\PBMailExtraDataReq;
use protobuf\MailSync\PBMailExtraDataRsp;
use protobuf\MailSync\PBMailExtraStatusInfo;
use protobuf\MailSync\PBMailForwardDetail;
use protobuf\MailSync\PBMailInfo;
use protobuf\MailSync\PBMailInfoBit;
use protobuf\MailSync\PBMailSearchListReq;
use protobuf\MailSync\PBMailSearchListRsp;
use protobuf\MailSync\PBMailSendStatus;
use protobuf\MailSync\PBMailSession;
use protobuf\MailSync\PBMailStatusInfo;
use protobuf\MailSync\PBMailSummary;
use protobuf\MailSync\PBMailSyncReq;
use protobuf\MailSync\PBMailSyncRsp;
use protobuf\MailSync\PBMailTagDetail;
use protobuf\MailSync\PBMailTagInfo;
use protobuf\MailSync\PBMailTodoDetail;
use protobuf\MailSync\PBMailTodoSyncRsp;
use protobuf\MailSync\PBMailTrackDetail;
use protobuf\MailSync\PBMailVersion;
use protobuf\Email\PBLargeAttachListReq;
use protobuf\Email\PBLargeAttachListRsp;
use protobuf\Common\DeviceType;
use protobuf\MailSync\PBSubordinateMailAllInfo;
use protobuf\MailSync\PBSubordinateMailReq;
use protobuf\MailSync\PBSubordinateMailRsp;
use protobuf\MailSync\PBUserMailCompareReq;
use protobuf\MailSync\PBUserMailCompareRsp;
use protobuf\MailSync\PBUserMailVersion;
use protobuf\PushData\PBLatestVersionRsp;
use protobuf\PushData\PBLatestVersionReq;
use protobuf\PushData\PBPushMailVersion;
use protobuf\PushData\PBPushUserSettingVersion;

/**
 * Created by PhpStorm.
 * User: andy
 * Date: 2017/11/23
 * Time: 10:20
 */

class MailReadController extends StormsFuryController
{
    public function actionException()
    {
        throw new RuntimeException('oh, an error occurred');
    }

    /**
     * +version 一直增长 一次取出会导致oom
     * +设置chunk不合适,会导致多次查询,效率反而降低  随着version数据增多，查询次数会慢慢增加
     * +每次查询10W 后续数据需要等心跳触发
     */
    public function actionSyncInfo()
    {
        $request = new PBMailCompareReq();
        $this->buildRequest($request);

        $user = User::getLoginUser();
        $userId = $user->getUserId();
        $clientId = $user->getClientId();

        $db = ProjectActiveRecord::getDbByClientId($clientId);


        $userMailIdList = new UserMailList();
        $userMailIdList->setUserId($userId);
        $userMailIdList = $userMailIdList->find();

        $userMailIds = array_column($userMailIdList, 'user_mail_id');

        /**
         * @var $versionList PBMailVersion[]
         */
        $versionList = $request->getVersionList();

        $rsp = new PBMailCompareRsp();
        $result = $rsp->getSession();

        // 暂时先过滤桌面端同步的请求
        if ($_SERVER['HTTP_HOST'] == 'crm.xiaoman.cn' && in_array($clientId, [7,3,18617,17383,26208,41937,19559,40298,19832,24857,17628,42634,37170,20054,49686,70388,21815,75527,76144,52419,26026,70350,31848,67555,77614,27670,41729,35093,23186,71285,24544,27392])) {
            return $this->successPb($rsp);
        }

        if (in_array($userId, [55305132, 55265073])) {
            return $this->successPb($rsp);
        }

        foreach ($versionList as $elem)
        {
            $userMailId = $elem->getUserMailId();
            $version = $elem->getVersion();
            $maxMailId = $elem->getMaxMailId();
            if ($userMailId && !in_array($userMailId, $userMailIds))
                continue;

            $data = [];

            if ($userMailId)
            {
                if (!$version) // 需要同步老数据
                {
                    $sql = "select mail_id from tbl_mail_version where user_mail_id=$userMailId and type=1 order by version asc limit 1";
                    $syncMinMailId = $db->createCommand($sql)->queryScalar();

                    $constType = \common\library\version\Constant::MAIL_MODULE_ADD;

                    if ($syncMinMailId)
                    {
                        // 只取需要同步的
                        $sql = "select mail_id,$constType,0 from tbl_mail where user_mail_id=$userMailId and folder_id not in (0,9,10,11) and mail_id>$maxMailId and mail_id<$syncMinMailId order by mail_id asc";
                    }
                    else
                    {
                        $sql = "select mail_id,$constType,0 from tbl_mail where user_mail_id=$userMailId and folder_id not in (0,9,10,11) and mail_id>$maxMailId order by mail_id asc";
                    }
                    $data = $db->createCommand($sql)->queryAll(false);
                }
                $mailVersion = new \common\library\version\MailVersion($clientId, $userMailId);
                $maxVersion = $mailVersion->getMaxVersion();
                $data = array_merge($data, $db->createCommand("select mail_id, type, version from tbl_mail_version where user_mail_id=$userMailId and version>$version order by version asc limit 100000")->queryAll(false));
            }
            else
            {
                $constType = \common\library\version\Constant::MAIL_MODULE_DRAFT;
                if (!$version)
                {
                    $sql = "select mail_id from tbl_mail_draft_version where user_id=$userId and type=$constType order by version asc limit 1";
                    $syncMinMailId = $db->createCommand($sql)->queryScalar();

                    if ($syncMinMailId)
                    {
                        // 只取需要同步的
                        $sql = "select mail_id,$constType,0 from tbl_mail where user_id=$userId and folder_id=0 and mail_id>$maxMailId and mail_id<$syncMinMailId order by mail_id asc";
                    }
                    else
                    {
                        $sql = "select mail_id,$constType,0 from tbl_mail where user_id=$userId and folder_id=0 and mail_id>$maxMailId order by mail_id asc";
                    }
                    $data = $db->createCommand($sql)->queryAll(false);
                }
                $data = array_merge($data, $db->createCommand("select mail_id, type, version from tbl_mail_draft_version where user_id=$userId and version>$version order by version asc limit 100000")->queryAll(false));
                $mailDraftVersion = new \common\library\version\MailDraftVersion($clientId, $userId);
                $maxVersion = $mailDraftVersion->getMaxVersion();
            }

            $mapping = [];
            $total = count($data);
            foreach ($data as $index => $datum)
            {
                $mailId = $datum[0];
                $type = $datum[1];
                $ver = $datum[2];

                $syncInfo = 0;
                switch ($type)
                {
                    case \common\library\version\Constant::MAIL_MODULE_ADD:case \common\library\version\Constant::MAIL_MODULE_RECOVER:
                        $syncInfo = (1 << \common\library\version\Constant::MAIL_SYNC_BIT_ALL_INFO) | (1 << \common\library\version\Constant::MAIL_SYNC_BIT_TAG) | (1 << \common\library\version\Constant::MAIL_SYNC_BIT_EXTRA_STATUS_INFO);
                        break;
                    case \common\library\version\Constant::MAIL_MODULE_EDIT:
                    case \common\library\version\Constant::MAIL_MODULE_BOUNCE:
                        $syncInfo = (1 << \common\library\version\Constant::MAIL_SYNC_BIT_STATUS_INFO) | (1 << \common\library\version\Constant::MAIL_SYNC_BIT_EXTRA_STATUS_INFO);
                        break;
                    case \common\library\version\Constant::MAIL_MODULE_TAG:
                        $syncInfo = 1 << \common\library\version\Constant::MAIL_SYNC_BIT_TAG;
                        break;
                    case \common\library\version\Constant::MAIL_MODULE_REMOVE:
                        $syncInfo = (1 << \common\library\version\Constant::MAIL_SYNC_BIT_STATUS_INFO) | (1 << \common\library\version\Constant::MAIL_SYNC_BIT_TAG);
                        break;
                    case \common\library\version\Constant::MAIL_MODULE_DRAFT:
                        $syncInfo = 1 << \common\library\version\Constant::MAIL_SYNC_BIT_ALL_INFO | (1 << \common\library\version\Constant::MAIL_SYNC_BIT_DRAFT_INFO);
                        break;
                    case \common\library\version\Constant::MAIL_MODULE_APPROVAL:
                    case \common\library\version\Constant::MAIL_MODULE_REPLY:
                    case \common\library\version\Constant::MAIL_MODULE_TRACK:
                    case \common\library\version\Constant::MAIL_MODULE_RECEIPT:
                        $syncInfo = 1 << \common\library\version\Constant::MAIL_SYNC_BIT_EXTRA_STATUS_INFO;
                        break;
                    case \common\library\version\Constant::MAIL_MODULE_DRAFT_DELETE:
                        $syncInfo = 0;
                        break;
                    default:
                        LogUtil::info('unsupported mail version type ' . $type);
                        continue 2;
                }

                if (array_key_exists($mailId, $mapping))
                {
                    if ($syncInfo)
                        $mapping[$mailId][1] |= $syncInfo;
                    else
                        $mapping[$mailId][1] = 0;
                }
                else
                {
                    $mapping[$mailId] = [$mailId, $syncInfo, $ver];
                }
                if ($index == ($total - 1)) {
                    $mapping[$mailId][2] = $ver;
                }
            }

            $mapping = array_values($mapping);
            $session = new PBMailSession();
            $session->setUserMailId($userMailId);
            if (empty($data))
            {
                $session->setSessionId('');
                $session->setSize(0);
                $session->setVersion(0);
                $session->setExpiredTime(0);
            }
            else
            {
                $redis = RedisService::cache();
                $key = \common\library\version\Constant::SYNC_MAIL_VERSION_PREFIX . "{$clientId}_{$userId}_{$userMailId}_{$version}_{$maxMailId}";
                $count = count($mapping);
                $setValue = serialize($mapping);
//                $md5Key = md5($redis->keyPrefix.$key);
                LogUtil::info("MailRead_redisSyncInfo:{ $key } count: {$count} version: {$version} maxMailId: {$maxMailId} redisKey: {$clientId} {$userId} {$userMailId} ".time() .($count>1000?'  redisSyncMax ':'').' version:'. $this->requestHeader->getClientVersion());
                $expiredTime = 1200;
                $session->setSessionId($key);
                $session->setSize($count);
                $session->setVersion(max(array_column($data, 2)));
                $session->setExpiredTime($expiredTime);
                $session->setMaxVersion((int)$maxVersion);
                $redis->set($key, $setValue, 'EX', $expiredTime);
            }


            $result[] = $session;
        }

        $rsp->setSession($result);

        $this->successPb($rsp);
    }

    public function actionSync()
    {

        $request = new PBMailSyncReq();
        $this->buildRequest($request);

        $user = User::getLoginUser();
        $userId = $user->getUserId();
        $clientId = $user->getClientId();
        $userMailId = $request->getUserMailId();
        $sessionId = $request->getSessionId();
        $offset = $request->getOffset();
        $size = $request->getSize();

        $redis = RedisService::cache();
        $list = unserialize($redis->get($sessionId));
        if(empty($list)) {
            \LogUtil::info("user_id{$userId}user_mail_id{$userMailId}offset{$offset}size{$size}无效的sessionId{$sessionId}");
            throw new \RuntimeException("无效的sessionId{$sessionId}",1010);
        }
        $list = array_slice($list, $offset, $size);
        $version = 0;
        $addMailIds = [];
        $editMailIds = [];
        $tagMailIds = [];
        $draftIds = [];
        $deleteDraftIds = [];
        $extraStatusIds = [];

        foreach ($list as $elem)
        {
            $mailId = $elem[0];
            $syncInfo = $elem[1];
            $version = $elem[2];

            if (!$syncInfo)
            {
                $deleteDraftIds[] = $mailId;
            }

            if ($syncInfo & (1 << \common\library\version\Constant::MAIL_SYNC_BIT_ALL_INFO))
            {
                $addMailIds[] = $mailId;
            }
            else if ($syncInfo & (1 << \common\library\version\Constant::MAIL_SYNC_BIT_STATUS_INFO))
            {
                $editMailIds[] = $mailId;
            }

            if ($syncInfo & (1 << \common\library\version\Constant::MAIL_SYNC_BIT_TAG))
            {
                $tagMailIds[] = $mailId;
            }

            if ($syncInfo & (1 << \common\library\version\Constant::MAIL_SYNC_BIT_DRAFT_INFO))
            {
                $draftIds[] = $mailId;
            }

            if ($syncInfo & (1 << \common\library\version\Constant::MAIL_SYNC_BIT_EXTRA_STATUS_INFO)) {
                $extraStatusIds[] = $mailId;
            }
        }
//        \LogUtil::info("user_mail_id{$userMailId}".json_encode([
//                'deleteDraft' => $deleteDraftIds,
//                'add' => $addMailIds,
//                'edit' => $editMailIds,
//                'tag' => $tagMailIds,
//                'draft' => $draftIds,
//                'extraStatus' => $extraStatusIds,
//            ]));

        $rsp = new PBMailSyncRsp();
        $rsp->setUserMailId($userMailId);
        $rsp->setVersion(intval($version));
        $addList = $rsp->getAddList();
        $editList = $rsp->getEditList();
        $tagList = $rsp->getTagList();
        $draftList = $rsp->getDraftList();
        $deleteDraftList = $rsp->getDraftDeleteList();
        $extraStatusList = $rsp->getExtraStatusList();
        $extMap = [];
        /**
         * @var $draftMap PBMailDraftInfo[]
         */
        $draftMap = [];

        $db = ProjectActiveRecord::getDbByClientId($clientId);
        if (!empty($draftIds))
        {
            foreach ($draftIds as $elem)
            {
                $draftMap[$elem] = new PBMailDraftInfo();
            }

            $extMap = \MailExternal::findByMailIds($draftIds, ['plan_send_time', 'expose_flag', 'hash', 'subject_remark']);

            $data = MailAttach::getFormatMailAttach($draftIds);
            foreach ($data as $mailId => $list)
            {
                $inlineData = $list[1] ?? [];
                $attachData = $list[0] ?? [];
                $info = new PBMailAttachmentInfo();

                $aList = $info->getAttachmentList();
                foreach ($attachData as $datum)
                {
                    $file = new PBFileInfo();
                    $file->setUniqueKey($datum['md5_file'].'_'.$datum['file_size']);
                    $file->setFileId(intval($datum['file_id']));
                    $file->setFileName($datum['file_name'] ?? '');
                    $file->setFileUrl($datum['file_url']);
                    $file->setPreviewUrl($datum['preview_url']);
                    $file->setFileSize(intval($datum['file_size']));
                    $file->setExpiredTime($datum['expired_time']);

                    $aList[] = $file;
                }
                $info->setAttachmentList($aList);

                $iList = $info->getInlineImageList();
                foreach ($inlineData as $datum)
                {
                    $file = new PBFileInfo();
                    $file->setUniqueKey($datum['md5_file'].'_'.$datum['file_size']);
                    $file->setFileId(intval($datum['file_id']));
                    $file->setFileName($datum['file_name'] ?? '');
                    $file->setFileUrl($datum['file_url']);
                    $file->setPreviewUrl($datum['preview_url']);
                    $file->setFileSize(intval($datum['file_size']));
                    $file->setExpiredTime($datum['expired_time']);

                    $iList[] = $file;
                }
                $info->setInlineImageList($iList);
                $info->setMailId($mailId);

                $draftMap[$mailId]->setAttachInfo($info);
            }
        }

        //plan_send_time expose_flag hash only draft deals these 3 types
        if (!empty($addMailIds))
        {
            $externalFieldMap = \MailExternal::findByMailIds($addMailIds, ['mail_bounced_by', 'subject_remark', 'delay_send_flag','root_mail_id','source_mail_id', 'alias_id']);
            \LogUtil::info(print_r($externalFieldMap,true));
            $attachMap = MailAttach::getFormatMailAttach($addMailIds);
            $mailIdSql = implode(',', $addMailIds);

            $sql = "select mail_id, user_mail_id, mail_type,subject,sender,receiver,reply_to,cc,bcc,email_size, create_time,update_time,receive_time,
            attach_flag,urgent_flag,receipt_flag,time_flag,track_flag,reply_to_mail_id,
            folder_id,read_flag,open_flag,star_flag,reply_flag,forward_flag,delete_flag,distribute_flag,send_status,conversation_id,relate_company_flag,conference_flag from tbl_mail where mail_id in ($mailIdSql)";

            $reader = $db->createCommand($sql)->query();

            $tmpUserIds = [$userId];
            $remarkInfos = \common\library\mail\Helper::getRemarkInfoByUserIdsAndMailIds($clientId, $tmpUserIds, $addMailIds);
            $remarkMap = array_column($remarkInfos,'remark','mail_id');

            foreach ($reader as $elem)
            {
                $mailId = $elem['mail_id'];

                if ($this->isMobile() && isset($elem['folder_id']) && $elem['folder_id'] == \Mail::FOLDER_EXPOSE_ID) {
                    $elem['folder_id'] = \Mail::FOLDER_SEND_ID;
                }

                $base = new PBMailBaseInfo();
                $base->setUserMailId($elem['user_mail_id']);
                $base->setMailId($mailId);
                $base->setMailType($elem['mail_type']);
                $base->setSubject($elem['subject']);
                $base->setSender($elem['sender']);
                $base->setReceiver($elem['receiver']);
                $base->setCc($elem['cc']);
                $base->setBcc($elem['bcc']);
                $base->setEmailSize($elem['email_size']);
                $base->setCreateTime(strtotime($elem['create_time']));
                $base->setUpdateTime(strtotime($elem['update_time']));
                $base->setReplyTo($elem['reply_to']);

                if(strtotime($elem['receive_time']) == 0) {
                    $base->setReceiveTime(strtotime($elem['create_time']));
                }else{
                    $base->setReceiveTime(strtotime($elem['receive_time']));
                }


                $base->setAttachFlag($elem['attach_flag']);
                $base->setUrgentFlag($elem['urgent_flag']);
                $base->setReceiptFlag($elem['receipt_flag']);
                $base->setTimeFlag($elem['time_flag']);
                $base->setTrackFlag($elem['track_flag']);
                $base->setReplyToMailId($elem['reply_to_mail_id']);
                $base->setDelaySendFlag($externalFieldMap[$mailId]['delay_send_flag'] ?? 0);
                $base->setRootMailId($externalFieldMap[$mailId]['root_mail_id'] ?? 0);
                $base->setSourceMailId($externalFieldMap[$mailId]['source_mail_id'] ?? 0);
                $base->setAliasId($externalFieldMap[$mailId]['alias_id'] ?? 0);
                $base->setConferenceFlag($elem['conference_flag']);
                if (array_key_exists($mailId, $extMap))
                {
                    if (isset($extMap[$mailId]['hash']))
                        $base->setHash($extMap[$mailId]['hash']);
                    if (!empty($extMap[$mailId]['plan_send_time']))
                        $base->setPlanSendTime(strtotime($extMap[$mailId]['plan_send_time']));
                    if (isset($extMap[$mailId]['expose_flag']))
                        $base->setExposeFlag(empty($extMap[$mailId]['expose_flag']) ? 0 : 1);
                    if (isset($externalFieldMap[$mailId]['subject_remark']))
                        $base->setSubjectRemark($externalFieldMap[$mailId]['subject_remark']);
                }
                $base->setMailRemark($remarkMap[$mailId] ?? "");

                $status = new PBMailStatusInfo();
                $status->setMailId($mailId);
                $status->setFolderId($elem['folder_id']);
                $status->setReadFlag($elem['read_flag']);
                $status->setOpenFlag($elem['open_flag']);
                $status->setStarFlag($elem['star_flag']);
                $status->setReplyFlag($elem['reply_flag']);
                $status->setForwardFlag($elem['forward_flag']);
                $status->setDeleteFlag($elem['delete_flag']);
                $status->setDistributeFlag($elem['distribute_flag']);
                $status->setBouncedMailId((int)($externalFieldMap[$mailId]['mail_bounced_by'] ?? 0));
                $status->setSendStatus($elem['send_status']);
                $status->setConversationId($elem['conversation_id']);
                $status->setRelateCompanyFlag($elem['relate_company_flag']);

                // 附件信息
                $elemAttachment = $attachMap[$mailId] ?? [];
                $normalInlineData = $elemAttachment[1] ?? [];
                $normalAttachData = $elemAttachment[0] ?? [];

                $pbElemAttachmentInfo = new PBMailAttachmentInfo();
                $pbElemAttachmentInfo->setMailId($mailId);

                $pbAttachmentList = $pbElemAttachmentInfo->getAttachmentList();
                foreach ($normalAttachData as $datum)
                {
                    $file = new PBFileInfo();
                    $file->setUniqueKey($datum['md5_file'].'_'.$datum['file_size']);
                    $file->setFileId(intval($datum['file_id']));
                    $file->setFileName($datum['file_name'] ?? '');
                    $file->setFileUrl($datum['file_url']);
                    $file->setPreviewUrl($datum['preview_url']);
                    $file->setFileSize(intval($datum['file_size']));
                    $file->setExpiredTime($datum['expired_time']);
                    $pbAttachmentList[] = $file;
                }
                $pbElemAttachmentInfo->setAttachmentList($pbAttachmentList);

                $pbInlineImageList = $pbElemAttachmentInfo->getInlineImageList();
                foreach ($normalInlineData as $datum)
                {
                    $file = new PBFileInfo();
                    $file->setUniqueKey($datum['md5_file'].'_'.$datum['file_size']);
                    $file->setFileId(intval($datum['file_id']));
                    $file->setFileName($datum['file_name'] ?? '');
                    $file->setFileUrl($datum['file_url']);
                    $file->setPreviewUrl($datum['preview_url']);
                    $file->setFileSize(intval($datum['file_size']));
                    $file->setExpiredTime($datum['expired_time']);
                    $pbInlineImageList[] = $file;
                }
                $pbElemAttachmentInfo->setInlineImageList($pbInlineImageList);

                if(YII_DEBUG) {
                    \LogUtil::info('mail_id'.$mailId.'folder_id'.$elem['folder_id']);
                }

                if (array_key_exists($mailId, $draftMap) && ($elem['folder_id'] == Mail::FOLDER_DRAFT_ID || $elem['folder_id'] == Mail::FOLDER_HIDDEN_DRAFT_ID)) {
                    $draftMap[$mailId]->setBaseInfo($base);
                    $draftMap[$mailId]->setStatusInfo($status);
                } else {
                    unset($draftMap[$mailId]);
                    $info = new PBMailInfo();
                    $info->setBaseInfo($base);
                    $info->setStatusInfo($status);
                    $info->setAttachmentInfo($pbElemAttachmentInfo);
                    $addList[] = $info;
                }
            }
        }

        if (!empty($editMailIds))
        {
            $externalFieldMap = \MailExternal::findByMailIds($editMailIds, ['mail_bounced_by', 'subject_remark','risk_flag','risk_reason']);

            $mailIdSql = implode(',', $editMailIds);

            $sql = "select mail_id,folder_id,read_flag,open_flag,star_flag,reply_flag,forward_flag,delete_flag,distribute_flag,send_status,conversation_id,relate_company_flag from tbl_mail where mail_id in ($mailIdSql)";

            $reader = $db->createCommand($sql)->query();

            foreach ($reader as $elem)
            {
                if ($this->isMobile() && isset($elem['folder_id']) && $elem['folder_id'] == \Mail::FOLDER_EXPOSE_ID) {
                    $elem['folder_id'] = \Mail::FOLDER_SEND_ID;
                }

                $status = new PBMailStatusInfo();
                $status->setMailId($elem['mail_id']);
                $status->setFolderId($elem['folder_id']);
                $status->setReadFlag($elem['read_flag']);
                $status->setOpenFlag($elem['open_flag']);
                $status->setStarFlag($elem['star_flag']);
                $status->setReplyFlag($elem['reply_flag']);
                $status->setForwardFlag($elem['forward_flag']);
                $status->setDeleteFlag($elem['delete_flag']);
                $status->setDistributeFlag($elem['distribute_flag']);
                $status->setBouncedMailId((int)($externalFieldMap[$elem['mail_id']]['mail_bounced_by'] ?? 0));
                $status->setSubjectRemark($externalFieldMap[$elem['mail_id']]['subject_remark'] ?? '');
                $status->setSendStatus($elem['send_status']);
                $status->setConversationId($elem['conversation_id']);
                $status->setRelateCompanyFlag($elem['relate_company_flag']);

                if(YII_DEBUG) {
                    \LogUtil::info('mail_id'.$elem['mail_id'].'folder_id'.$elem['folder_id']);
                }

                $editList[] = $status;
            }
        }

        if (!empty($tagMailIds))
        {
            $mailIdSql = implode(',', $tagMailIds);

            $sql = "select mail_id,tag_id from tbl_mail_tag_assoc where mail_id in ($mailIdSql)";

            $data = $db->createCommand($sql)->queryAll(false);

            $map = [];
            foreach ($data as $elem)
            {
                $map[$elem[0]][] = $elem[1];
            }
            $noTagMaiLIds = array_diff($tagMailIds, array_keys($map));
            foreach ($noTagMaiLIds as $noTagMaiLId) {
                $map[$noTagMaiLId] = [];
            }

            foreach ($map as $mailId => $tags)
            {
                $pb = new PBMailTagInfo();
                $pb->setMailId($mailId);
                $pb->setTagList($tags);
                $tagList[] = $pb;
            }
        }

        if (!empty($deleteDraftIds))
        {
            foreach ($deleteDraftIds as $id)
                $deleteDraftList[] = $id;
        }

        if (!empty($extraStatusIds))
        {
            $mailIdSql = implode(',', $extraStatusIds);

            //审批数据
            $approvalMailType = \common\library\approval_flow\Constants::ENTITY_TYPE_MAIL;
            $approvalSql = "select refer_id,apply_form_id,status from tbl_approval_flow_apply_form where client_id={$clientId} and refer_type={$approvalMailType} and  refer_id in({$mailIdSql}) order by apply_form_id asc";
            $approvalData = array_column($db->createCommand($approvalSql)->queryAll(), null, 'refer_id');

            //追踪数据
            $trackSql = "select mail_id, reply_mail_id, view_count from tbl_mail_track where mail_id in ({$mailIdSql})";
            $trackData = array_column($db->createCommand($trackSql)->queryAll(), null, 'mail_id');

            // 回执发送标志
            $receiptSql = "select mail_id, `value` as read_receipt_flag from tbl_mail_external where mail_id in ({$mailIdSql}) and `key` = 'read_receipt_flag'";

            $result = $db->createCommand($receiptSql)->queryAll();
            $receiptMailIds = array_column($result,'mail_id');
            foreach (array_diff($extraStatusIds,$receiptMailIds) as $item) {
                $result[] = [
                    'mail_id' => $item,
                    'read_receipt_flag' => 2
                ];
            }
            $receiptData = array_column($result,null,'mail_id');
            $externalFieldMap = MailExternal::findByMailIds($extraStatusIds, ['risk_reason','receive_origin_sender']);

            $extraStatusData = array_replace_recursive($approvalData, $trackData, $receiptData,$externalFieldMap);
            foreach ($extraStatusData as $mailId => $elem)
            {
                $pb = new PBMailExtraStatusInfo();
                $pb->setMailId($mailId);
                $pb->setApprovalId($elem['apply_form_id'] ?? 0);
                $pb->setApprovalStatus($elem['status'] ?? 0);
                $pb->setReplyMailId($elem['reply_mail_id'] ?? 0);
                if (isset($elem['read_receipt_flag'])){
                    $pb->setReadReceiptFlag($elem['read_receipt_flag']);
                }
                if (array_key_exists('view_count', $elem))
                    $pb->setHasTrack(1);

                if (array_key_exists('risk_reason', $elem)) {
                    $riskReasons = json_decode($elem['risk_reason']);
                    $pb->setRiskReasons($riskReasons);
                    $pb->setRiskFlag(1);
                }
                if (array_key_exists('receive_origin_sender', $elem)) {
                    $pb->setReceiveOriginSender($elem['receive_origin_sender']);
                }
                $extraStatusList[] = $pb;

            }
        }
        $rsp->setAddList($addList);
        $rsp->setEditList($editList);
        $rsp->setTagList($tagList);
        $rsp->setDraftList(array_values($draftMap));
        $rsp->setDraftDeleteList($deleteDraftList);
        $rsp->setExtraStatusList($extraStatusList);

        \LogUtil::info($userMailId.'|'.$sessionId.'|'.$offset.'|'.$size);

        $this->successPb($rsp);
        return;
    }

    private $compensateMap = [];

    /**
     * @param $mailId
     * @return PBMailAllInfo
     */
    private function getPBAllMailInfo($mailId)
    {
        if (!array_key_exists($mailId, $this->compensateMap))
        {
            $obj = new PBMailAllInfo();
            $obj->setMailId($mailId);

            $this->compensateMap[$mailId] = $obj;
        }

        return $this->compensateMap[$mailId];
    }

    public function actionCompensate()
    {

        $user = User::getLoginUser();
        $loginUserId = $user->getUserId();
        $clientId = $user->getClientId();

        $request = new PBMailCompensateReq();
        $this->buildRequest($request);

        $flag = $request->getFlag();
        $userId = $request->getUserId();

        if (!$userId) {
            //不传user_id 时候默认取登录的
            $userId = $loginUserId;
            //判断是否下属
        } else if (!\common\library\department\Helper::canManageAnyUsers(
                    $user->getClientId(),
                    $loginUserId,
                    \common\library\privilege_v3\PrivilegeConstants::PRIVILEGE_EMAIL_VIEW,
                    $userId)
        ) {
            throw new RuntimeException(\Yii::t('account', 'No permission operation'), ErrorCode::CODE_FAIL);
        }

        $mailIds = iterator_to_array($request->getMailIds());
        if(count($mailIds) > 200) {
            $mailIds = array_slice($mailIds,0,200);
        }

        $rsp = new PBMailCompensateRsp();

        if (!$flag || empty($mailIds))
        {
            $this->successPb($rsp);
            return;
        }

        $mailIdsSql = implode(',', $mailIds);

        $needBase = $flag & (1 << PBMailInfoBit::MAIL_BASE_INFO_BIT);
        $needStatus = $flag & (1 << PBMailInfoBit::MAIL_STATUS_INFO_BIT);
        $needSendStatus = $flag & (1 << PBMailInfoBit::MAIL_SEND_STATUS_BIT);
        $needTag = $flag & (1 << PBMailInfoBit::MAIL_TAG_INFO_BIT);
        $needAttach = $flag & (1 << PBMailInfoBit::MAIL_ATTACH_INFO_BIT);
        $needSummary = $flag & (1 << PBMailInfoBit::MAIL_SUMMARY_BIT);
        $needContent = $flag & (1 << PBMailInfoBit::MAIL_CONTENT_BIT);
        $needDraftSummary = $flag & (1 << PBMailInfoBit::MAIL_DRAFT_SUMMARY_BIT);
        $needDraftContent = $flag & (1 << PBMailInfoBit::MAIL_DRAFT_CONTENT_BIT);
        $needTrack = $flag & (1 << PBMailInfoBit::MAIL_TRACK_DETAIL_BIT);
        $needFroward = $flag & (1 << PBMailInfoBit::MAIL_FORWARD_DETAIL_BIT);
        $needExtraStatus = $flag & (1 << PBMailInfoBit::MAIL_EXTRA_STATUS_BIT);
        $needTagDetail = $flag & (1 << PBMailInfoBit::MAIL_TAG_DETAIL_BIT);
        $needLargeAttach = $flag & (1 << PBMailInfoBit::MAIL_LARGE_ATTACH_BIT);//大附件
        $newDesktop = $flag & (1 << PBMailInfoBit::MAIL_NEW_DESKTOP_BIT);
        $newDesktopImport = $flag & (1 << PBMailInfoBit::MAIL_NEW_DESKTOP_IMPORT_BIT);
        $newTodoDetail = $flag & (1 << PBMailInfoBit::MAIL_TODO_DETAIL_BIT); //待办




	    if ($needBase || $needStatus || $needSendStatus || $newDesktop)
        {
            $db = ProjectActiveRecord::getDbByClientId($clientId);

            $field = 'mail_id';

            $sendStatusMessageMap = [];

            if ($needSendStatus)
            {
                $field .= ',send_status';

                $messages = $db->createCommand("select mail_id, fail_desc, fail_code from tbl_async_mail_task where mail_id in ($mailIdsSql) and status=1")->queryAll(true);

                $sendStatusMessageMap = array_column($messages, null, 'mail_id');

                $mailErrorCodeMap = array_column(\common\library\mail\Helper::getMailErrorCodeCache() , null , 'fail_code');

                $externalFieldMap = \MailExternal::findByMailIds($mailIds, ['expose_flag', 'root_mail_id']);

            }
            $extMap = [];
            if ($needBase) {
                $field .= ',user_mail_id,mail_type,subject,sender,receiver,reply_to,cc,bcc,email_size, create_time,update_time,receive_time,
            attach_flag,urgent_flag,receipt_flag,time_flag,track_flag,reply_to_mail_id';
                $extMap = \MailExternal::findByMailIds($mailIds, ['plan_send_time', 'expose_flag', 'subject_remark', 'delay_send_flag','expose_user_mail_info', 'sign_id']);

	            $baseMailExposeInfo = MailExpose::findByMailIds($mailIds);

            }
            if ($needStatus) {
                $field .= ',folder_id,read_flag,open_flag,star_flag,reply_flag,forward_flag,delete_flag,distribute_flag,conversation_id,relate_company_flag';
                $externalFieldMap = \MailExternal::findByMailIds($mailIds, ['mail_bounced_by', 'subject_remark']);
            }

		    if ($newDesktop) {

			    $field .= ', relate_company_flag';
			    $externalFieldMap = \MailExternal::findByMailIds($mailIds, ['root_mail_id', 'source_mail_id', 'risk_reason', 'receive_origin_sender']);
		    }

            $sql = "select $field from tbl_mail where mail_id in ($mailIdsSql)";

            $result = $db->createCommand($sql)->queryAll();

            foreach ($result as $elem)
            {
                if ($this->isMobile() && isset($elem['folder_id']) && $elem['folder_id'] == \Mail::FOLDER_EXPOSE_ID) {
                    $elem['folder_id'] = \Mail::FOLDER_SEND_ID;
                }

                if ($needSendStatus)
                {

                    $key = (($externalFieldMap[$elem['mail_id']]['expose_flag'] ?? 0) || !empty($externalFieldMap[$elem['mail_id']]['root_mail_id'])) ? 'expose' : 'normal';

                    $errCode = $sendStatusMessageMap[$elem['mail_id']]['fail_code'] ?? '';

                    $mailErrorCode = $mailErrorCodeMap[$errCode] ?? [];

                    $translateMessage = \common\library\mail\Helper::translateMessage($mailErrorCode) ?? '';

                    $failDesc = $sendStatusMessageMap[$elem['mail_id']]['fail_desc'] ?? '';

                    $sendStatus = new PBMailSendStatus();
                    $sendStatus->setMailId($elem['mail_id']);
                    $sendStatus->setSendStatus($elem['send_status']);

                    $sendStatus->setMessage($failDesc);

                    $sendStatus->setFailErrorCode($errCode);

                    $sendStatus->setFailErrorReplaceList((array)(\AsyncMailTask::SEND_FAILED_MSG[$errCode][$key]['replace_list'] ?? []));

                    if ($errCode == \AsyncMailTask::SEND_FAIL_CODE_ACCESS_DENIED && strripos($failDesc, '小满发件IP') == 0) {

                        $ipStr = explode(',', $failDesc)[0];
                        $ip = explode(':', $ipStr)[1] ?? '';
                        if (!empty($ip)) {
                            $translateMessage = str_replace('xiaoManIp', $ip, $translateMessage);
                        }
                    }

                    $sendStatus->setFailErrorTransferMessage($translateMessage);


                    $this->getPBAllMailInfo($elem['mail_id'])->setSendStatus($sendStatus);
                }

                if ($needBase)
                {
                    $base = new PBMailBaseInfo();
                    $base->setSignId($extMap[$elem['mail_id']]['sign_id'] ?? 0);
                    $base->setUserMailId($elem['user_mail_id']);
                    $base->setMailId($elem['mail_id']);
                    $base->setMailType($elem['mail_type']);
                    $base->setSubject($elem['subject']);
                    $base->setSender($elem['sender']);
                    $base->setReceiver($elem['receiver']);
                    $base->setCc($elem['cc']);
                    $base->setBcc($elem['bcc']);
                    $base->setEmailSize($elem['email_size']);
                    $base->setCreateTime(strtotime($elem['create_time']));
                    $base->setUpdateTime(strtotime($elem['update_time']));
                    $base->setReceiveTime(strtotime($elem['receive_time']));
                    $base->setAttachFlag($elem['attach_flag']);
                    $base->setUrgentFlag($elem['urgent_flag']);
                    $base->setReceiptFlag($elem['receipt_flag']);
                    $base->setTimeFlag($elem['time_flag']);
                    $base->setTrackFlag($elem['track_flag']);
                    $base->setReplyToMailId($elem['reply_to_mail_id']);
                    $base->setReplyTo($elem['reply_to']);
                    $planSendTime = $extMap[$elem['mail_id']]['plan_send_time'] ?? 0;
                    $planSendTime = $planSendTime ? strtotime($planSendTime) : 0;
                    $base->setPlanSendTime($planSendTime);
                    $base->setExposeFlag((int)($extMap[$elem['mail_id']]['expose_flag'] ?? 0));
                    $this->getPBAllMailInfo($elem['mail_id'])->setBaseInfo($base);
                    $base->setSubjectRemark($extMap[$elem['mail_id']]['subject_remark'] ?? '');
                    $base->setDelaySendFlag($extMap[$elem['mail_id']]['delay_send_flag'] ?? 0);

                    if($base->getExposeFlag())
                    {
                        $exposeUserMailInfo = $extMap[$elem['mail_id']]['expose_user_mail_info'] ?? [];
                        if(is_string($exposeUserMailInfo))
                        {
                            $exposeUserMailInfo = json_decode($exposeUserMailInfo, true) ?? [];
                        }
                        $exposeUserMailIds = $exposeUserMailInfo['user_mail_ids'] ?? [];
                        $exposeMinWaitTime = $exposeUserMailInfo['min_wait_time'] ?? 0;
                        $exposeMaxWaitTime = $exposeUserMailInfo['max_wait_time'] ?? 0;
	                    $sender = ($exposeUserMailInfo['sender'] ?? '') ?: '';
	                    $signMode = $exposeUserMailInfo['sign_mode'] ?? 0;
	                    $subject = json_decode($baseMailExposeInfo[$elem['mail_id']]['extra'] ?? null, true)['subject'] ?? [];
	                    $mailExposeInfo = new \protobuf\MailSync\PBMailExposeInfo();
                        $mailExposeInfo->setUserMailIds($exposeUserMailIds);
                        $mailExposeInfo->setMinWaitTime($exposeMinWaitTime);
                        $mailExposeInfo->setMaxWaitTime($exposeMaxWaitTime);
	                    $mailExposeInfo->setSender($sender);
	                    $mailExposeInfo->setSignMode($signMode);
	                    $mailExposeInfo->setSubject($subject);

                        $base->setMailExposeInfo($mailExposeInfo);
                    }
                }

                if ($needStatus)
                {
                    $status = new PBMailStatusInfo();
                    $status->setMailId($elem['mail_id']);
                    $status->setFolderId($elem['folder_id']);
                    $status->setReadFlag($elem['read_flag']);
                    $status->setOpenFlag($elem['open_flag']);
                    $status->setStarFlag($elem['star_flag']);
                    $status->setReplyFlag($elem['reply_flag']);
                    $status->setForwardFlag($elem['forward_flag']);
                    $status->setDeleteFlag($elem['delete_flag']);
                    $status->setDistributeFlag($elem['distribute_flag']);
                    $status->setBouncedMailId((int)($externalFieldMap[$elem['mail_id']]['mail_bounced_by'] ?? 0));
                    $status->setSubjectRemark($externalFieldMap[$elem['mail_id']]['subject_remark'] ?? '');
                    $status->setConversationId($elem['conversation_id']);
                    $status->setRelateCompanyFlag($elem['relate_company_flag']);

                    $this->getPBAllMailInfo($elem['mail_id'])->setStatusInfo($status);
                }

	            if ($newDesktop) {

		            $base = new PBMailBaseInfo();

		            $base->setRootMailId($externalFieldMap[$elem['mail_id']]['root_mail_id'] ?? 0);

					$base->setSourceMailId($externalFieldMap[$elem['mail_id']]['source_mail_id'] ?? 0);

		            $this->getPBAllMailInfo($elem['mail_id'])->setBaseInfo($base);


		            $status = new PBMailStatusInfo();

		            $status->setRelateCompanyFlag($elem['relate_company_flag'] ?? 0);

		            $this->getPBAllMailInfo($elem['mail_id'])->setStatusInfo($status);


		            $mailExtraStatus = new PBMailExtraStatusInfo();

		            $mailExtraStatus->setRiskFlag((isset($externalFieldMap[$elem['mail_id']]['risk_reason']) ? 1 : 0));

					$mailExtraStatus->setReceiveOriginSender($externalFieldMap[$elem['mail_id']]['receive_origin_sender'] ?? '');

					$this->getPBAllMailInfo($elem['mail_id'])->setExtraStatusInfo($mailExtraStatus);
	            }
            }
        }

        if ($needAttach)
        {
            $data = MailAttach::getFormatMailAttach($mailIds);

            foreach ($data as $mailId => $list)
            {
                $inlineData = $list[1] ?? [];
                $attachData = $list[0] ?? [];
                $info = new PBMailAttachmentInfo();

                $aList = $info->getAttachmentList();
                foreach ($attachData as $datum)
                {
                    $file = new PBFileInfo();
                    $file->setUniqueKey($datum['md5_file'].'_'.$datum['file_size']);
                    $file->setFileId($datum['file_id']);
                    $file->setFileName($datum['file_name']);
                    $file->setFileUrl($datum['file_url']);
                    $file->setPreviewUrl($datum['preview_url']);
                    $file->setFileSize($datum['file_size']);
                    $file->setExpiredTime($datum['expired_time']);

                    $aList[] = $file;
                }
                $info->setAttachmentList($aList);

                $iList = $info->getInlineImageList();
                foreach ($inlineData as $datum)
                {
                    $file = new PBFileInfo();
                    $file->setUniqueKey($datum['md5_file'].'_'.$datum['file_size']);
                    $file->setFileId($datum['file_id']);
                    $file->setFileName($datum['file_name']);
                    $file->setFileUrl($datum['file_url']);
                    $file->setPreviewUrl($datum['preview_url']);
                    $file->setFileSize($datum['file_size']);

                    $iList[] = $file;
                }
                $info->setInlineImageList($iList);

                $info->setMailId($mailId);

                $this->getPBAllMailInfo($mailId)->setAttachInfo($info);
            }
        }

        if ($needLargeAttach)
        {
            $data = \common\library\external_file\Helper::getMailLargeAttach($mailIds,$clientId);
            foreach ($data as $mailId => $mailLargeAttachDetail) {
                $largeAttachDetailList = $this->getPBAllMailInfo($mailId)->getLargeAttachList();
                foreach ($mailLargeAttachDetail as $item) {
                    $fileInfo = new PBFileInfo();
                    $fileInfo->setFileId($item['file_id']);
                    $fileInfo->setFileName($item['file_name']??"");
                    $fileInfo->setFileSize($item['file_size']??"");
                    $fileInfo->setReferId($item['refer_id']??0);
                    $fileInfo->setHash($item['hash']??"");
                    $fileInfo->setExpiredTime($item['expire_time']? strtotime($item['expire_time']) : 0);
                    $fileInfo->setDownloadHost($item['download_host']??"");
                    $fileInfo->setFileUrl($item['url']??"");
                    $fileInfo->setPreviewUrl($item['preview_url']??"");
                    $largeAttachDetailList[] = $fileInfo;
                    $this->getPBAllMailInfo($mailId)->setLargeAttachList($largeAttachDetailList);
                }
            }
        }

        if ($needTag)
        {
            $db = ProjectActiveRecord::getDbByClientId($clientId);

            $sql = "select mail_id,tag_id from tbl_mail_tag_assoc where mail_id in ($mailIdsSql)";

            $data = $db->createCommand($sql)->queryAll(false);

            $map = [];
            foreach ($data as $elem)
            {
                $map[$elem[0]][] = $elem[1];
            }

            foreach ($map as $mailId => $tags)
            {
                $pb = new PBMailTagInfo();
                $pb->setMailId($mailId);
                $pb->setTagList($tags);

                $this->getPBAllMailInfo($mailId)->setTagInfo($pb);
            }
        }

        if ($needSummary || $needContent)
        {
            $officialList = [];
            $mailInfos = [];
            $db = ProjectActiveRecord::getDbByClientId($clientId);
            $data = $db->createCommand("select mail_id,user_mail_id from tbl_mail where mail_id in ({$mailIdsSql})")->queryAll(false);
            foreach ($data as $elem)
            {
                list($mailId, $userMailId) = $elem;
                $officialList[$userMailId][] = $mailId;
                $mailInfos[] = [
                    'clientId' => $clientId,
                    'mailId' => $mailId,
                    'userMailId' => $userMailId,
                ];
            }

            // 只要摘要不要正文，从redis算，避免对数据库压力过大
            if ($needSummary)
            {
                $summaryList = \common\library\mail\MailContentHelper::getSummaryList($mailInfos);
                foreach ($summaryList as $summaryInfo) {
                    $mailId = $summaryInfo['mailId'];
                    $summary = $summaryInfo['summary'];
                    $summaryObj = new PBMailSummary();
                    $summaryObj->setMailId($mailId);
                    $summaryObj->setSummary($summary);
                    $this->getPBAllMailInfo($mailId)->setSummary($summaryObj);
                }
            }

            if ($needContent && count($mailIds) == 1) {
                if (!empty($data)) {
                    $mailInfo = $data[0];
                    $mailId = $mailInfo[0];
                    $userMailId = $mailInfo[1];
                    $contentInfo = \common\library\mail\MailContentHelper::getContentAndPlainText($clientId, $userMailId, $mailId);

                    $content = new PBMailContent();
                    $content->setMailId($mailId);
                    $content->setContent(snappy_compress($contentInfo['content'] ?? ''));
                    $content->setPlainText(snappy_compress($contentInfo['plainText'] ?? ''));
                    $content->setCompression(PBCompressionType::COMPRESSION_SNAPPY);
                    $this->getPBAllMailInfo($mailId)->setContent($content);
                }
            //批量拉取邮件正文摘要
            } else if ($needContent && count($mailIds) >1) {

                $contentInfos = \common\library\mail\MailContentHelper::getContentAndPlainTextList($mailInfos);
                foreach ($contentInfos as $contentInfo) {
                    if (empty($contentInfo['mailId'])) continue;
                    $content = new PBMailContent();
                    $content->setMailId($contentInfo['mailId']);
                    $content->setContent(snappy_compress($contentInfo['content'] ?? ''));
                    $content->setPlainText(snappy_compress($contentInfo['plainText'] ?? ''));
                    $content->setCompression(PBCompressionType::COMPRESSION_SNAPPY);
                    $this->getPBAllMailInfo($contentInfo)->setContent($content);
                }
            }
        }

        if ($needDraftSummary || $needDraftContent)
        {

            $mailDraftContentList = new \common\library\mail\draft\MailDraftContentList($userId);
            $mailDraftContentList->setMailId($mailIds);
            $mailDraftContentList->setNeedUnCompressionContent(false);
            $mailDraftContentList->setNeedUnCompressionPlainText(false);
            $cursor = $mailDraftContentList->find();

            foreach ($cursor as $elem)
            {
                if ($needDraftSummary)
                {
                    $summary = new PBMailSummary();
                    $summary->setMailId($elem['mail_id']);
                    if($elem['plain_text']) {
                        $summary->setSummary(mb_substr(trim(snappy_uncompress($elem['plain_text'])), 0, 100, 'utf-8'));
                    }
                    $this->getPBAllMailInfo($elem['mail_id'])->setSummary($summary);
                }

                if ($needDraftContent)
                {
                    $content = new PBMailContent();
                    $content->setMailId($elem['mail_id']);
                    $content->setContent($elem['content']);
                    $content->setPlainText($elem['plain_text']);
                    $content->setCompression(PBCompressionType::COMPRESSION_SNAPPY);

                    $this->getPBAllMailInfo($elem['mail_id'])->setContent($content);
                }
            }
        }

        if ($needTrack) {
            $mailTrackDetailList = \MailTrackDetail::findByMailIds($mailIds);
            foreach ($mailTrackDetailList as $mailId => $mailTrackDetail){

                $track_detail_list = $this->getPBAllMailInfo($mailId)->getTrackDetailList();
                foreach ($mailTrackDetail as $elem) {
                    $track_detail = new PBMailTrackDetail();
                    $track_detail->setMailId($elem['mail_id']);
                    $track_detail->setDetailId($elem['detail_id']);
                    if(!empty($elem['view_time'])) {
                        $track_detail->setViewTime(strtotime($elem['view_time']));
                    }
                    $track_detail->setViewCountry($elem['view_country']);
                    $track_detail->setViewCountryCode($elem['view_country_code'] ?? 0);
                    $track_detail->setViewProvince($elem['view_province']);
                    $track_detail->setViewCity($elem['view_city']);
                    $track_detail->setViewIp($elem['view_ip']);
                    $track_detail_list[] = $track_detail;
                }

                $this->getPBAllMailInfo($mailId)->setTrackDetailList($track_detail_list);
            }
        }

        if ($needFroward) {
            $mailFrowardList = \common\library\mail\Helper::forwardList($mailIds);
            foreach ($mailFrowardList as $mailId => $mailForwardDetail){

                $forwardDetailList = $this->getPBAllMailInfo($mailId)->getForwardDetailList();

                foreach ($mailForwardDetail as $elem) {
                    $forwardDetail = new PBMailForwardDetail();
                    $forwardDetail->setMailId($elem['mail_id']);
                    $forwardDetail->setForwardTime(strtotime($elem['receive_time']));
                    $receiver = implode(';',array_filter([$elem['receiver'],$elem['bcc'],$elem['cc']]));
                    $forwardDetail->setReceiver($receiver);
                    $forwardDetail->setForwardMailId($elem['forward_mail_id']);
                    $forwardDetailList[] = $forwardDetail;
                }
                $this->getPBAllMailInfo($mailId)->setForwardDetailList($forwardDetailList);
            }
        }
        if ($needExtraStatus) {

            $db = ProjectActiveRecord::getDbByClientId($clientId);

            $mailIdSql = implode(',', $mailIds);
            $approvalMailType = \common\library\approval_flow\Constants::ENTITY_TYPE_MAIL;
            $approvalSql = "select refer_id,apply_form_id,status,apply_user_id from tbl_approval_flow_apply_form where client_id={$clientId} and refer_type={$approvalMailType} and  refer_id in({$mailIdSql})";

            $approvalData = array_column($db->createCommand($approvalSql)->queryAll(), null, 'refer_id');




            $trackSql = "select mail_id, reply_mail_id, view_count from tbl_mail_track where mail_id in ({$mailIdSql})";
            $trackData = array_column($db->createCommand($trackSql)->queryAll(), null, 'mail_id');
            $mailRiskReasons = MailExternal::findByMailIds($mailIds, ['risk_reason']);

            $receiptSql = "select mail_id, `value` as read_receipt_flag from tbl_mail_external where mail_id in ({$mailIdSql}) and `key` = 'read_receipt_flag'";
            $receiptData = array_column($db->createCommand($receiptSql)->queryAll(), null, 'mail_id');

            $pinList = \Pin::getPinReferIds($userId, \Pin::TYPE_MAIL, $mailIds);
            $pinMap = empty($pinList) ? [] : array_fill_keys($pinList, []);

            $extraStatusData = array_replace_recursive($pinMap, $approvalData, $trackData, $mailRiskReasons, $receiptData);

            foreach ($extraStatusData as $mailId => $elem) {
                $mailExtraStatus = new PBMailExtraStatusInfo();
                $mailExtraStatus->setMailId($mailId);
                $mailExtraStatus->setApprovalId($elem['apply_form_id'] ?? 0);
                $mailExtraStatus->setApprovalStatus($elem['status'] ?? 0);
                $mailExtraStatus->setUserId($elem['apply_user_id'] ?? 0);
                $mailExtraStatus->setReplyMailId($elem['reply_mail_id'] ?? 0);
                $mailExtraStatus->setReadReceiptFlag($receiptData[$mailId]['read_receipt_flag'] ?? 2);
                $mailExtraStatus->setIsPin(in_array($mailId, $pinList) ? 1 : 0);

                if (array_key_exists('risk_reason', $elem)) {
                    $riskReasons = json_decode($elem['risk_reason']);
                    $mailExtraStatus->setRiskReasons($riskReasons);
                }

                if (array_key_exists('view_count', $elem))
                    $mailExtraStatus->setHasTrack(1);
                $this->getPBAllMailInfo($mailId)->setExtraStatusInfo($mailExtraStatus);
            }

        }

        if($needTagDetail){

            $mailTagListMap = \MailTagAssoc::getMailTagListByMailIds($mailIds);

            foreach ($mailTagListMap as $mailId => $mailTagDetail){

                $mailTagDetailList = $this->getPBAllMailInfo($mailId)->getTagList();

                foreach ($mailTagDetail as $elem){
                    $mailTagDetail =  new PBMailTagDetail();
                    $mailTagDetail->setAssocId($elem['assoc_id']);
                    $mailTagDetail->setTagId($elem['tag_id']);
                    $mailTagDetail->setTagName($elem['tag_name']);
                    $mailTagDetail->setTagColor($elem['tag_color']);
                    $mailTagDetail->setSystemFlag($elem['system_flag']);
                    $mailTagDetailList[] = $mailTagDetail;
                }
                $this->getPBAllMailInfo($mailId)->setTagList($mailTagDetailList);

            }
        }


	    if ($newDesktopImport) {

		    $db = ProjectActiveRecord::getDbByClientId($clientId);

		    $sql = 'SELECT mail_id, message_id, references_list
					FROM tbl_mail_thread
					WHERE client_id = '.$clientId.'
					  AND mail_id IN ('.$mailIdsSql.')
					';

		    $list = $db->createCommand($sql)->queryAll();

		    foreach ($list as $elem) {

			    $base = new PBMailBaseInfo();

			    $base->setMessageId($elem['message_id'] ?? '');

				$base->setReferencesList($elem['references_list'] ?? '');

			    $this->getPBAllMailInfo($elem['mail_id'])->setBaseInfo($base);
		    }

	    }

        if ($newTodoDetail) {
            //查询待办
            $todoList = new MailTodoList($userId);
            $todoList->setMailId($mailIds);
            $todoList = $todoList->find();
            foreach ($todoList as $todo) {
                $todoDetail =  new PBMailTodoDetail();
                $todoDetail->setMailId(intval($todo['mail_id']));
                $todoDetail->setUserId(intval($todo['user_id']));
                $todoDetail->setCompletedFlag($todo['completed_flag'] > 0);
                $todoDetail->setRemindFlag($todo['remind_flag'] > 0);
                $todoDetail->setProcessTime(strtotime($todo['process_time']));

                $this->getPBAllMailInfo($todo['mail_id'])->setTodoDetail($todoDetail);
            }

        }

        $data = $rsp->getData();
        foreach ($this->compensateMap as $elem){
            $data[] = $elem;
        }
        $rsp->setData($data);

        $this->successPb($rsp);
    }

    /**
     * @throws CDbException
     * @throws CException
     * @throws ProcessException
     */
    public function actionContent()
    {
        $user = User::getLoginUser();
        $userId = $user->getUserId();
        $clientId = $user->getClientId();

        $request = new PBMailContentReq();
        $this->buildRequest($request);

        $compression = $request->getCompression();
        $mailIds = $request->getMailIdList();
        $mailIds = array_filter(iterator_to_array($mailIds->getIterator()));
        if (empty($mailIds))
        {
            $this->successPb();
            return;
        }

        LogUtil::info("mailIds[" . json_encode($mailIds) . "] userId[$userId]");

        $db = ProjectActiveRecord::getDbByClientId($clientId);
        $mailIdsSql = implode(',', $mailIds);
        try {
            $sql = "select mail_id,mail_type,user_mail_id,folder_id,time_flag from tbl_mail where mail_id in ({$mailIdsSql})";
            $data = $db->createCommand($sql)->queryAll(false);
        } catch (CDbException $e) {
            LogUtil::error("mailIds[" . json_encode($mailIds) . "] -- sql[$sql]");
            throw $e;
        }

        $draftList = [];
        $officialList = [];

        foreach ($data as $elem)
        {
            list($mailId, $mailType, $userMailId, $folderId, $timeFlag) = $elem;

            if (\common\library\mail\Helper::isDraft($folderId, $timeFlag) || $mailType == Mail::MAIL_TYPE_UNKNOWN) {
                $draftList[] = $mailId;
            } else {
                $officialList[] = [
                    'clientId' => (int)$clientId,
                    'mailId' => (int)$mailId,
                    'userMailId' =>(int)$userMailId
                ];
            }
        }

        $rsp = new PBMailContentRsp();
        $contents = $rsp->getContentList();

        if($draftList){

            $mailDraftContentList = new \common\library\mail\draft\MailDraftContentList();
            $mailDraftContentList->setMailId($draftList);
            $mailDraftContentList->setNeedUnCompressionPlainText(false);
            $mailDraftContentList->setNeedUnCompressionContent(false);
            $draftContent = $mailDraftContentList->find();

            foreach ($draftContent as $elem)
            {
                $content = new PBMailContent();
                $content->setMailId($elem['mail_id']);
                $content->setCompression($compression);
                $content->setContent($compression ? snappy_uncompress($elem['content']) : $elem['content']);
                $content->setPlainText($compression ? snappy_uncompress($elem['plain_text']) : $elem['plain_text']);
                $contents[] = $content;
            }
            $rsp->setContentList($contents);

        }

        if (!empty($officialList) && count($mailIds) == 1)
        {
            $mailInfo = $data[0];
            $mailId = $mailInfo[0];
            $userMailId = $mailInfo[2];
            $contentInfo = \common\library\mail\MailContentHelper::getContentAndPlainText($clientId, $userMailId, $mailId);

            $content = new PBMailContent();
            $content->setMailId($mailId);
            $content->setContent($compression ? $contentInfo['content'] ?? '' : snappy_compress($contentInfo['content'] ?? ''));
            $content->setPlainText($compression ? $contentInfo['plainText'] ?? '' : snappy_compress($contentInfo['plainText'] ?? ''));
            $content->setCompression($compression);
            $contents[] = $content;
            $rsp->setContentList($contents);
        } else if(!empty($officialList) && count($mailIds) > 1){
            $contentInfos = \common\library\mail\MailContentHelper::getContentAndPlainTextList($officialList);
            foreach ($contentInfos as $contentInfo) {
                if (empty($contentInfo['mailId'])) continue;
                $content = new PBMailContent();
                $content->setMailId($contentInfo['mailId']);
                $content->setContent($compression ? $contentInfo['content'] ?? '' : snappy_compress($contentInfo['content'] ?? ''));
                $content->setPlainText($compression ? $contentInfo['plainText'] ?? '' : snappy_compress($contentInfo['plainText'] ?? ''));
                $content->setCompression($compression);
                $contents[] = $content;
            }
            $rsp->setContentList($contents);
        }

        $this->successPb($rsp);
    }

    /**
     *
     */
    public function actionAttachmentList()
    {
        $request = new PBMailAttachmentReq();
        $this->buildRequest($request);

        $user = User::getLoginUser();
        $userId = $user->getUserId();
        $clientId = $user->getClientId();
        $mailIds = $request->getMailIdList();
        $mailIds = iterator_to_array($mailIds->getIterator());
        $data = MailAttach::getFormatMailAttach($mailIds);
        $rsp = new PBMailAttachmentRsp();
        $result = $rsp->getAttchmentList();
        foreach ($data as $mailId => $list)
        {
            $inlineData = $list[1] ?? [];
            $attachData = $list[0] ?? [];
            $info = new PBMailAttachmentInfo();

            $aList = $info->getAttachmentList();
            foreach ($attachData as $datum)
            {
                $file = new PBFileInfo();
                $file->setUniqueKey($datum['md5_file'].'_'.$datum['file_size']);
                $file->setFileId($datum['file_id']);
                $file->setFileName($datum['file_name']);
                $file->setFileUrl($datum['file_url']);
                $file->setPreviewUrl($datum['preview_url']);
                $file->setFileSize($datum['file_size']);
                $file->setExpiredTime($datum['expired_time']);

                $aList[] = $file;
            }
            $info->setAttachmentList($aList);

            $iList = $info->getInlineImageList();
            foreach ($inlineData as $datum)
            {
                $file = new PBFileInfo();
                $file->setUniqueKey($datum['md5_file'].'_'.$datum['file_size']);
                $file->setFileId($datum['file_id']);
                $file->setFileName($datum['file_name']);
                $file->setFileUrl($datum['file_url']);
                $file->setPreviewUrl($datum['preview_url']);
                $file->setFileSize($datum['file_size']);
                $file->setExpiredTime($datum['expired_time']);

                $iList[] = $file;
            }
            $info->setInlineImageList($iList);

            $info->setMailId($mailId);
            $result[] = $info;
        }

        $this->successPb($rsp);
    }


    // 对齐web端下属邮件高级搜索
    public function actionSubordinateList()
    {
        $request = new PBMailSearchListReq();
        $this->buildRequest($request);
//        $request->setLimit(20);
//        $request->setMailDetailFlag(1);
//        $request->setUserId(-1);

//        $detailFlag = $request->getMailDetailFlag();
        $user_id = $request->getUserId()== -1?null:$request->getUserId();
        $user_mail_id =$request->getUserMailId();
        $keyword = $request->getKeyword();
        $keyword_type = $request->getKeywordType()??\common\library\mail\MailSearchList::KEYWORD_TYPE_ALL;
        $attach_flag = $request->getAttachFlag() == 0 ? null : $request->getAttachFlag();
        $mail_type = $request->getMailType();
        $sender = $request->getSender()==""?null:$request->getSender();
        $receiver = $request->getReceiver()==""?null:$request->getReceiver();
//        $receiver_or_sender = null;
        $start_date = $request->getStartDate();
        $end_date = $request->getEndDate();
        $customer_emails = iterator_to_array($request->getCustomerEmails());
        $company_id = $request->getCompanyId();
//        $customer_id = null;
        $pin = $request->getPin()?"1":null;
        $include_folder_ids = $request->getIncludeFolderIds()?iterator_to_array($request->getIncludeFolderIds()):[];
        $exclude_folder_ids = [];
        $reply_status = $request->getReplyStatus();
        $all_folders = null;
        $attach_names = iterator_to_array($request->getAttachNames());
        $attach_exts = iterator_to_array($request->getAttachExts());
        $tag_ids = iterator_to_array($request->getTagIds());
        $tag_all_flag = $request->getTagAllFlag()??false;
        $offset = $request->getOffset();
        $limit =$request->getLimit();
//        $page = $request->getOffset()??1;
//        $page_size = $request->getLimit();
//        $send_status = null;
        $report_item_unique_key= $request->getReportItemUniqueKey();
//        $archive_flag = null;



        $user_mail_id = array_filter((array)$user_mail_id);
        $user = User::getLoginUser();

        $rsp = new PBMailSearchListRsp();
        //搜索客户往来邮箱
        if ($company_id ) {
//        if ($company_id || $customer_id) {
            $emails = [];
//            if ($customer_id) {
//                $customer = new \common\library\customer\Customer($user->getClientId(), $customer_id);
//                if ($customer->isExist()) {
//                    $emails[] = $customer->email;
//                }
//            } else {
                $list = new \common\library\customer\CustomerList($user->getClientId());
                $list->setCompanyId($company_id);
                $list->setFields(['email']);
                $emails = array_column($list->find(), 'email');
//            }

            if (empty($customer_emails)) {
                $customer_emails = $emails;
            } else {
                $customer_emails = array_intersect($emails, $customer_emails);
            }

            $customer_emails = array_filter($customer_emails);
            if (empty($customer_emails)) {
                return $this->success($rsp);
            }

        }

        //判断是否是下属
        if ($user_id && empty($customer_emails)) {
            if (!\common\library\department\Helper::canManageAnyUsers(
                $user->getClientId(),
                $user->getUserId(),
                \common\library\privilege_v3\PrivilegeConstants::PRIVILEGE_EMAIL_VIEW,
                $user_id)
            ) {
                throw new RuntimeException(\Yii::t('account', 'No permission operation'), ErrorCode::CODE_FAIL);
            }
        }

        if (empty($user_mail_id)) {
            if (empty($user_id)) {
                // 全部下属
                $departmentPermission = new \common\library\department\DepartmentPermission($user->getClientId(), $user->getUserId());
                $departmentPermission->permission(PrivilegeConstants::PRIVILEGE_EMAIL_VIEW);
                $result = $departmentPermission->userList();
                $result = array_filter($result, function($item) use ($user) {
                    return $item['user_id'] != $user->getUserId();
                });
                $userIds = array_column($result, 'user_id');
            } else {
                // 指定下属
                $userIds = [$user_id];
            }

            if (!empty($userIds)) {
                $user_mail_id = array_column(\UserMail::findAllByUserIds($user->getClientId(), $userIds), 'user_mail_id');
            }
        }

        // 修复漏洞（读取任意用户邮件Bug-003）
        if (!empty($user_mail_id)) {
            $user_mail_id = array_column(\UserMail::findAllByUserMailId($user->getClientId(), $user_mail_id), 'user_mail_id');
        }

        if (empty($user_mail_id)) {
            return $this->success($rsp);
        }

        //指定文件下搜索  需要获取当前文件夹的子文件
        if(!empty($include_folder_ids)) {
            //找出当前文件与系统文件的差集
            $diffs = array_diff($include_folder_ids,\Mail::getAllSysFolderList());
            if(!empty($diffs)) {
                //找出自定义文件的子文件
                $allSubFolderIds = [];
                foreach ($diffs as $folder_id) {
                    $mailFolderList = new \common\library\mail\setting\folder\MailFolderList(\User::getLoginUser()->getUserId());
                    $mailFolderList->setFields(['folder_id']);
                    $mailFolderList->setParentId($folder_id);
                    $mailFolderList->setRecursive(true);
                    $subFolderIds = $mailFolderList->find();
                    $allSubFolderIds = array_merge($allSubFolderIds,array_column($subFolderIds,'folder_id'));
                }
                $include_folder_ids = array_merge($include_folder_ids,$allSubFolderIds);
            }
        }

        $mailList = new \common\library\mail\MailList($user->getClientId(),$user_id);
        $mailList->setSearchModel(\Constants::SEARCH_MODEL_SEARCHER);
        $mailList->setReportItemUniqueKey($report_item_unique_key);
        $mailList->searcher->setUserMailIds($user_mail_id);
        $mailList->searcher->setAttachFlag($attach_flag);
        $mailList->searcher->setMailType($mail_type);
        $mailList->searcher->setSender($sender);
        $mailList->searcher->setReceiver($receiver);
//        $mailList->searcher->setReceiverOrSender($receiver_or_sender);
        $mailList->searcher->setSearchDate($start_date, $end_date);
        $mailList->searcher->setCustomerEmails($customer_emails);
        $mailList->searcher->setIncludeFolderIds($include_folder_ids);
        $mailList->searcher->setReplyStatus($reply_status);
        $mailList->searcher->setLimit($limit);
        $mailList->searcher->setOffset($offset);
//        $mailList->searcher->setAllFolders((bool)$all_folders);
        $mailList->searcher->setTagIds($tag_ids);
        $mailList->searcher->setTagAllFlag($tag_all_flag);
        $mailList->searcher->setMailIdOnly(false);//需要获取高亮内容
        $mailList->searcher->setPinFlag($pin);
//        $mailList->searcher->setSendStatus($send_status);
//        $mailList->searcher->setArchiveFlag($archive_flag);

        // 优先使用旧的附件查询规则
        if ((!empty($attach_names)) || (!empty($attach_exts))) {
            $mailList->searcher->setAttachNames($attach_names);
            $mailList->searcher->setAttachExts($attach_exts);
        } elseif (!empty($keyword)) {
            if ($keyword_type == \common\library\mail\MailSearchList::KEYWORD_TYPE_ATTACH) {
                $mailList->searcher->setAttachNames([$keyword]);
                $mailList->searcher->setAttachFlag($attach_flag ?? 1);
            } else {
                $mailList->searcher->setKeywords($keyword);
                $mailList->searcher->setKeywordType($keyword_type);
            }
        }

//        if (!empty($exclude_folder_ids)) {
//            $mailList->searcher->setExcludeFolderIds($exclude_folder_ids);
//        } else {
            $mailList->searcher->setExcludeFolderIds(\Mail::getSearchExcludeFolderIdsForSubordinate());
//        }


        $mailList->getFormatter()->setShowCaptureCard(true);


        if (!empty($userIds)) {
            $mailList->setUserId($userIds);
        }

        //搜索邮件详情
        if($request->getMailDetailFlag()){
            $mailList->getFormatter()->appAdvanceSearchSetting();
            $data = $mailList->find();
            $count = $mailList->count();


            $rsp = new PBMailSearchListRsp();
            $rsp->setCount($count);
            $result = $rsp->getMailInfo();
            foreach ($data as $mail) {
                $mainInfo = $this->setMailInfo($mail);
                if ($mainInfo) {
                    $result[] = $mainInfo;
                }
            }
            $rsp->setMailInfo($result);
        } else {
            //只搜索Mail_id，结果从搜索器出
            $data = $mailList->searcher->find();
            $count = $mailList->searcher->count();
//            $rsp = new PBMailSearchListRsp();
            $rsp->setCount($count);
            $result = $rsp->getMailIds();

            foreach ($data as $mailId) {
                $result[] = $mailId;
            }
            $rsp->setMailIds($result);
        }
        $this->successPb($rsp);
    }

    public function actionSearchList()
    {

        $request = new PBMailSearchListReq();
        $this->buildRequest($request);

        $user = User::getLoginUser();
        $clientId = $user->getClientId();

        $userMailIds = [];
        $reportItemUniqueKey =$request->getReportItemUniqueKey();
        $customer_emails = iterator_to_array($request->getCustomerEmails());

        // -1的时候，查看全部
        if ($request->getUserId() == -1) {
            $allUserMailList = \common\library\mail\Helper::getUserMailList($user, 1);
            if ($allUserMailList && $allUserMailList['list'] && is_array($allUserMailList)) {
                $userMailIds = array_column($allUserMailList['list'],'user_mail_id');
            }
            $userId = 0;
        } else if ($request->getUserId()) {
            //判断是否是下属，是否有查看权限
            if (empty($customer_emails)) {
                if (!\common\library\department\Helper::canManageAnyUsers(
                    $user->getClientId(),
                    $user->getUserId(),
                    \common\library\privilege_v3\PrivilegeConstants::PRIVILEGE_EMAIL_VIEW,
                    $request->getUserId())
                ) {
                    throw new RuntimeException(\Yii::t('account', 'No permission operation'), ErrorCode::CODE_FAIL);
                }
            }
            $userId = $request->getUserId();
            $userMailIds = $request->getUserMailId();
        } else {
            $userId = $user->getUserId();
            $userMailIds = $request->getUserMailId();
        }

        //客户邮件
        $companyId = $request->getCompanyId();
        if ($companyId > 0) {
            $list = new \common\library\customer_v3\customer\CustomerList($clientId);
            $list->setCompanyId($companyId);
            $list->setFields(['email']);
            $emails = array_column($list->find(), 'email');
            if (empty($customer_emails)) {
                $customer_emails = $emails;
            } else {
                $customer_emails = array_intersect($emails, $customer_emails);
            }
            $customer_emails = array_filter($customer_emails);
            if (empty($customer_emails)) {
                $this->successPb();
                return;
            }
        }

        $list = new \common\library\mail\MailList($clientId, $userId);
        $list->setReportItemUniqueKey($reportItemUniqueKey);

        if ($request->getTodoRefer() > 0 && $request->getMailDetailFlag()) {
            switch ($request->getTodoRefer()) {
                case \common\library\todo\TodoConstant::TODO_TYPE_TASK_REPLY_MAIL:
                    $taskList = new TaskList($clientId, $userId);
                    $taskList->setType(TaskConstant::TASK_TYPE_REPLY_MAIL);
                    $taskList->setUserIds([$userId]);
                    $todoTaskIds = iterator_to_array($request->getTodoTaskIds());
                    if (!empty($todoTaskIds)) {
                        $ruleIds = [];
                        foreach ($todoTaskIds as $todoId) {
                            $ruleIds[] = substr($todoId,4); //截取事项id中的rule_id
                        }
                        $taskList->setRuleIds($ruleIds);
                    }
                    $taskList->setLimit($request->getLimit());
                    $taskList->setOffset($request->getOffset());
                    $taskList->setFilterDeletedTask(true);
                    $taskList->setOrderBy('end_time');
                    $taskList->setOrder('desc');
                    $taskList->setFields(['refer_id']);
                    $referIds = $taskList->find() ?? [];
                    $mailIds = array_column($referIds, 'refer_id');
                    $list->setMailIds($mailIds);
                    break;
                case \common\library\todo\TodoConstant::TODO_TYPE_PENDING_MAIL:
                    $list->getListStrategy('todo', [
                        'user_mail_id'    => null,
                        'tag_id'          => 0,
                        'folder_id'       => null,
                        'pin'             => 0,
                        'customer_emails' => [],
                        'todo_completed_flag' => 0,
                        'report_item_unique_key' => '',
                        'start_date'      => null,
                        'end_date'        => null,
                    ]);
                    break;
                case \common\library\todo\TodoConstant::TODO_TYPE_NOT_READ_EMAIL_TODAY:
                case \common\library\todo\TodoConstant::TODO_TYPE_NOT_READ_EMAIL_THIS_WEEK:
                    $list->getListStrategy('unread', [
                        'user_mail_id'    => null,
                        'tag_id'          => 0,
                        'folder_id'       => null,
                        'pin'             => 0,
                        'customer_emails' => [],
                        'todo_completed_flag' => 0,
                        'report_item_unique_key' => '',
                        'start_date'      => null,
                        'end_date'        => null,
                    ]);
                    break;
            }

        } else {
            $list->setSearchModel(Constants::SEARCH_MODEL_SEARCHER);
        }

        $list->searcher->setUserMailIds($userMailIds); //设置查询的user_ids
        $list->searcher->setKeywords($request->getKeyword());
        $list->searcher->setTagIds(iterator_to_array($request->getTagIds()));
        $list->searcher->setTagAllFlag($request->getTagAllFlag());
        $list->searcher->setKeywordType($request->getKeywordType());
        $list->searcher->setSearchDate($request->getStartDate(), $request->getEndDate());
        $list->searcher->setSender($request->getSender());
        $list->searcher->setReceiver($request->getReceiver());
        $list->searcher->setMailIdOnly(true);
        $list->searcher->setMailType($request->getMailType());
        $list->searcher->setCustomerEmails($customer_emails);
        $list->searcher->setOffset($request->getOffset());
        $list->searcher->setLimit($request->getLimit());
        if ($request->getPin()) {
            $list->searcher->setPinFlag($request->getPin());
        }
        if ($request->getAttachFlag()){
            $attachFlag = $request->getAttachFlag() > 1 ? 0 : 1;
            $list->searcher->setAttachFlag($attachFlag);
        }
        $list->searcher->setAttachNames(iterator_to_array($request->getAttachNames()));
        $list->searcher->setAttachExts(iterator_to_array($request->getAttachExts()));
        $list->searcher->setReplyStatus($request->getReplyStatus());
        $list->searcher->setIncludeFolderIds(iterator_to_array($request->getIncludeFolderIds()));

        //搜索邮件详情
        if($request->getMailDetailFlag()){
            $list->getFormatter()->appAdvanceSearchSetting();
            $data = $list->find();
            $count = $list->count();

            $rsp = new PBMailSearchListRsp();
            $rsp->setCount($count);
            $result = $rsp->getMailInfo();
            foreach ($data as $mail) {
                $mainInfo = $this->setMailInfo($mail);
                if ($mainInfo) {
                    $result[] = $mainInfo;
                }
            }
            $rsp->setMailInfo($result);
        } else {
            //只搜索Mail_id，结果从搜索器出
            $data = $list->searcher->find();
            $count = $list->searcher->count();
            $rsp = new PBMailSearchListRsp();
            $rsp->setCount($count);
            $result = $rsp->getMailIds();

            foreach ($data as $mailId) {
                $result[] = $mailId;
            }
            $rsp->setMailIds($result);
        }
        $this->successPb($rsp);
    }

    public function setMailInfo($mail)
    {
        if(!$mail){
            return false;
        }

        if ($this->isMobile() && isset($mail['folder_id']) && $mail['folder_id'] == \Mail::FOLDER_EXPOSE_ID) {
            $mail['folder_id'] = \Mail::FOLDER_SEND_ID;
        }

        $mainInfo = new PBMailAllInfo();

        $mailBaseInfo = new PBMailBaseInfo();
        $mailStatusInfo = new PBMailStatusInfo();
        $mailExtraStatusInfo = new PBMailExtraStatusInfo();
        $mailSummary = new PBMailSummary();
        $mailAttachmentInfo = new PBMailAttachmentInfo();
        $pbTrackDetailList = $mainInfo->getTrackDetailList();

        //main base info
        $mainInfo->setMailId($mail['mail_id']);
        $mailBaseInfo->setUserMailId($mail['user_mail_id']);
        //兼容空字符串为空
        $sign_id = $mail['sign_id'] ?? 0;
        $sign_id = str_replace(' ', '', $sign_id);
        if (empty($sign_id)) {
            $sign_id = 0;
        }
        $mailBaseInfo->setSignId(intval($sign_id));
        $mailBaseInfo->setUserId($mail['user_id']);
        $mailBaseInfo->setMailId($mail['mail_id']);
        $mailBaseInfo->setMailType($mail['mail_type']);
        $mailBaseInfo->setSubject($mail['subject']??"");
        $mailBaseInfo->setSender($mail['sender']);
        $mailBaseInfo->setReceiver($mail['receiver']);
        $mailBaseInfo->setReplyTo($mail['reply_to']);
        $mailBaseInfo->setCc($mail['cc']);
        $mailBaseInfo->setBcc($mail['bcc']);
        $mailBaseInfo->setCreateTime(strtotime($mail['create_time']));
        $mailBaseInfo->setUpdateTime(strtotime($mail['update_time']));
        $mailBaseInfo->setReceiveTime(strtotime($mail['receive_time']));
        $mailBaseInfo->setAttachFlag($mail['attach_flag']);
        $mailBaseInfo->setUrgentFlag($mail['urgent_flag']);
        $mailBaseInfo->setReceiptFlag($mail['receipt_flag']);
        $mailBaseInfo->setTimeFlag($mail['time_flag']);
        $mailBaseInfo->setAttachFlag($mail['attach_flag']);
        $mailBaseInfo->setTrackFlag($mail['track_flag']);
        $mailBaseInfo->setExposeFlag(isset($mail['expose_flag']) && $mail['expose_flag']? intval($mail['expose_flag']):0);
        $mailBaseInfo->setSendStatus($mail['send_status']);
        $mailBaseInfo->setSubjectRemark($mail['subject_remark'] ?? '');

        $planSendTime = 0;
        if($mail['plan_send_time']){
            $planSendTime = strtotime($mail['plan_send_time']);
        }
        $mailBaseInfo->setPlanSendTime($planSendTime);
        $mailBaseInfo->setEmailSize($mail['email_size']);


        // 邮件摘要
        $mailSummary->setSummary($mail['summary']);

        //邮件状态
        $mailStatusInfo->setFolderId($mail['folder_id']);
        $mailStatusInfo->setReplyFlag($mail['reply_flag']);
        $mailStatusInfo->setReadFlag($mail['read_flag']??0);
        $mailStatusInfo->setDeleteFlag($mail['delete_flag']);
        $mailStatusInfo->setDistributeFlag($mail['distribute_flag']);
        $mailStatusInfo->setForwardFlag($mail['forward_flag']);
        $mailStatusInfo->setStarFlag($mail['star_flag']);
        $mailStatusInfo->setOpenFlag($mail['open_flag']);
        $mailStatusInfo->setBouncedMailId($mail['bounced_mail_id'] ?? 0);
        $mailStatusInfo->setSubjectRemark($mail['subject_remark'] ?? '');
        $mailStatusInfo->setConversationId($mail['conversation_id']);
        $mailStatusInfo->setRelateCompanyFlag($mail['relate_company_flag']);
        $mailStatusInfo->setSendStatus($mail['send_status']);

        foreach ($mail['track']['list'] ?? [] as $trackItem)
        {
            $pbMailTrackDetail = new PBMailTrackDetail();
            $pbMailTrackDetail->setMailId($trackItem['mail_id']);
            $pbMailTrackDetail->setDetailId($trackItem['detail_id']);
            if (!empty($trackItem['view_time'])) {
                $pbMailTrackDetail->setViewTime(strtotime($trackItem['view_time']));
            }
            $pbMailTrackDetail->setViewCountry($trackItem['view_country']);
            $pbMailTrackDetail->setViewCountryCode($trackItem['view_country_code']);
            $pbMailTrackDetail->setViewProvince($trackItem['view_province']);
            $pbMailTrackDetail->setViewCity($trackItem['view_city']);
            $pbMailTrackDetail->setViewIp($trackItem['view_ip']);
            $pbTrackDetailList[] = $pbMailTrackDetail;
        }

        //拓展信息
        $mailExtraStatusInfo->setIsPin($mail['is_pin']);
        $mailExtraStatusInfo->setReplyMailId($mail['reply_mail_id']??0);
        $mailExtraStatusInfo->setHasTrack($mail['has_track']?1:0); //是否有追踪, 表明对方已查看
        if($mail['tag_list'] && is_array($mail['tag_list'])){
           $tagList = [];
           foreach ($mail['tag_list'] as $tags) {
               $mailTagDetail = new PBMailTagDetail();
               $mailTagDetail->setAssocId($tags['assoc_id']);
               $mailTagDetail->setTagId($tags['tag_id']);
               $mailTagDetail->setTagColor($tags['tag_color']);
               $mailTagDetail->setTagName($tags['tag_name']);
               $mailTagDetail->setSystemFlag($tags['system_flag']);
               $tagList[] = $mailTagDetail;
           }
            $mainInfo->setTagList($tagList);
       }
        if (!empty($mail['attachment_list'] ?? [])) {
            $mailAttachmentInfo->setMailId($mail['mail_id']);
            $pbAttachmentList = $mailAttachmentInfo->getAttachmentList();
            foreach ($mail['attachment_list'] as $attachmentItem)
            {
                $pbAttachmentItem = new PBFileInfo();
                $pbAttachmentItem->setUniqueKey($attachmentItem['md5_file'].'_'.$attachmentItem['file_size']);
                $pbAttachmentItem->setFileId($attachmentItem['file_id']);
                $pbAttachmentItem->setFileName($attachmentItem['file_name']);
                $pbAttachmentItem->setFileUrl($attachmentItem['file_url']);
                $pbAttachmentItem->setPreviewUrl($attachmentItem['preview_url']);
                $pbAttachmentItem->setFileSize($attachmentItem['file_size']);
                $pbAttachmentItem->setExpiredTime($attachmentItem['expired_time']);
                $pbAttachmentList[] = $pbAttachmentItem;
            }
            $mailAttachmentInfo->setAttachmentList($pbAttachmentList);
        }
        $mainInfo->setBaseInfo($mailBaseInfo);
        $mainInfo->setExtraStatusInfo($mailExtraStatusInfo);
        $mainInfo->setStatusInfo($mailStatusInfo);
        $mainInfo->setSummary($mailSummary);
        $mainInfo->setAttachInfo($mailAttachmentInfo);
        $mainInfo->setTrackDetailList($pbTrackDetailList);

        if (isset($mail['mail_card_list']) && !empty($mail['mail_card_list'])) {

            $mailCard = current($mail['mail_card_list']);
            $emailIdentity = new PBEmailIdentity();
            $emailIdentity->setCardType($mailCard['card_type']);
            $emailIdentity->setEmail($mailCard['email']??'');
            $emailIdentity->setName($mailCard['name']??'');
            //邮箱身份
            $mainInfo->setIdentity($emailIdentity);
        }

        if (isset($mail['mail_todo']) && !empty($mail['mail_todo'])) {
            $todo = $mail['mail_todo'];
            $todoDetail =  new PBMailTodoDetail();
            $todoDetail->setMailId(intval($todo['mail_id']));
            $todoDetail->setUserId(intval($todo['user_id']));
            $todoDetail->setCompletedFlag($todo['completed_flag'] > 0);
            $todoDetail->setRemindFlag($todo['remind_flag'] > 0);
            $todoDetail->setProcessTime(strtotime($todo['process_time']));
            //待办详情
            $mainInfo->setTodoDetail($todoDetail);
        }

        if (isset($mail['fail_error_ori_message']) && !empty($mail['fail_error_ori_message']))
        {
            $sendStatus = new PBMailSendStatus();
            $sendStatus->setMailId($mail['mail_id']);
            $sendStatus->setSendStatus($mail['send_status']);
            $sendStatus->setMessage($mail['fail_error_ori_message']);
            $mainInfo->setSendStatus($sendStatus);
        }

        return $mainInfo;
    }

    public function actionMailCard()
    {
        $request = new PBMailCardReq();
        $this->buildRequest($request);

        $email = $request->getEmail();
        if (empty($email)) {
            throw  new RuntimeException('email ' . \Yii::t('common', 'Parameter error'));
        }

        $user = User::getLoginUser();
        $cardService = new Card($user->getUserId());
        $card = $cardService->mailCard($email);

        $telList = $card['specify_customer_data']['tel_list'] ?? ($card['tel_list'] ?? []);
        $telList = array_map(function ($elem) {
            return $elem[0] . ' ' . $elem[1];
        }, $telList);
        $rsp = new PBMailCardRsp();

        $cardType = $card['card_type'];

        //移动端版本低于3.8.5暂时不支持card_type 类型9,10,11,12,需要暂时特殊处理为5
        $deviceType = $this->requestHeader->getDeviceType();
        if (in_array($deviceType, [DeviceType::ANDROID, DeviceType::IOS]) && !$this->compareClientVersion('3.8.5', true)) {
            $filterCardType = [EmailIdentity::CARD_TYPE_ADVICE, EmailIdentity::CARD_TYPE_LEAD, EmailIdentity::CARD_TYPE_COLLEAGUE_LEAD, EmailIdentity::CARD_TYPE_PUBLIC_LEAD];
            if (in_array($cardType, $filterCardType)) {
                $cardType = EmailIdentity::CARD_TYPE_STRANGER;
            }
        }
        $rsp->setCardType($cardType);
        $rsp->setEmail($card['email']);
        $rsp->setName($card['name']??'');
        $rsp->setNickname($card['short_name'] ?? '');
        $rsp->setPostition($card['specify_customer_data']['post'] ?? '');
        $rsp->setTelList($telList);
        $rsp->setIsBlack(boolval($card['in_black_list']));
        $rsp->setReferId($card['customer_id'] ?? 0);
        $rsp->setCompanyId($card['company_id'] ?? 0);
        $rsp->setLeadId($card['lead_id'] ?? 0);
        if ($cardType == EmailIdentity::CARD_TYPE_ADVICE) {
            $adviceData = $card['advice'] ?? [];
            if (!empty($adviceData)) {
                $companyData = $adviceData['company'];
                $customerData = $adviceData['customer'][0];

                $pbAdviceInfo = new \protobuf\MailSync\PBAdviceInfo();
                $pbAdviceCompany = new \protobuf\MailSync\PBAdviceCompanyInfo();
                $pbAdviceCustomer = new \protobuf\MailSync\PBAdviceCustomerInfo();

                $pbAdviceCompany->setName($companyData['name']);
                $pbAdviceCompany->setAddress($companyData['address']);
                $pbAdviceCompany->setCompanyHashId($companyData['company_hash_id']);
                $pbAdviceCompany->setHomepage($companyData['homepage']);

                $pbAdviceCustomer->setName($customerData['name']);
                if (!empty($customerData['tel_list'] ?? [])) {
                    $telList = array_map(function ($elem) {
                        return $elem[0] . ' ' . $elem[1];
                    }, $customerData['tel_list']);
                    $pbAdviceCustomer->setTelList($telList);
                }

                $pbAdviceInfo->setAdviceId($adviceData['advice_id']);
                $pbAdviceInfo->setUserId($adviceData['user_id']);
                $pbAdviceInfo->setUserName($adviceData['user_name']);
                $pbAdviceInfo->setCompany($pbAdviceCompany);
                $pbAdviceInfo->setCustomer($pbAdviceCustomer);
                $rsp->setAdvice($pbAdviceInfo);
            }
        }

        $this->successPb($rsp);
    }

    public function actionMailDistributeDetail()
    {
        $request = new PBMailDistributeReq();
        $this->buildRequest($request);

        $mail_id = $request->getMailId();
        $distributeDetailList = \common\library\mail\Helper::getMailDistributeList($mail_id);

        $response = new PBMailDistributeRsp();
        $responseDistributeList = $response->getDistributeDetailList();

        foreach ($distributeDetailList as $distributeDetail) {

            $distribute = new PBMailDistributeDetail();
            $distribute->setMailId($distributeDetail['mail_id']);
            $distribute->setUserId($distributeDetail['user_id']);
            $distribute->setSrcMailId($distributeDetail['src_mail_id']);
            $distribute->setSrcUserId($distributeDetail['src_user_id']);
            $distribute->setSrcReceiveTime(strtotime($distributeDetail['src_receive_time']));
            $distribute->setCreateUser($distributeDetail['create_user']);
            $distribute->setCreateTime(strtotime($distributeDetail['create_time']));
            $distribute->setSrcUserMail($distributeDetail['src_user_mail']);
            $distribute->setSrcUserNickname($distributeDetail['src_user_nickname']);
            $distribute->setUserMail($distributeDetail['user_mail']);
            $distribute->setUserNickname($distributeDetail['user_nickname']);

            $responseDistributeList[] = $distribute;
        }

        $this->successPb($response);
    }

    public function actionEmailSuggestion()
    {
        $req = new PBEmailMatchReq();
        $this->buildRequest($req);

        $keyword = $req->getKeyword();

        $user = User::getLoginUser();
        $mailSuggestion = new \common\library\mail\MailSuggestion($user->getUserId());
        $mailSuggestion->desktopListSetting();
        $data = $mailSuggestion->matchKeyword($keyword);

        $rsp = new PBEmailMatchRsp();

        $match_list = $rsp->getMatchList();

        foreach ($data as $datum) {
            $match = new PBEmailMatch();
            $match->setReferId(intval($datum['id']));
            $match->setName($datum['name']);
            $match->setEmail($datum['email']);
            $match->setCardType(intval($datum['card_type']));

            $match_list[] = $match;
        }

        $rsp->setMatchList($match_list);

        $this->successPb($rsp);
    }

    public function actionEmailBasicInfo()
    {
        $req = new PBEmailsMatchReq();
        $this->buildRequest($req);
        $user = User::getLoginUser();
        $emails = $req->getEmails();
	    $checkBlack = $req->getCheckBlack();

        $emailArray = explode(';', trim($emails));
        $emailArray = array_filter($emailArray);

        if (empty($emailArray)) {
            throw  new RuntimeException('email ' . \Yii::t('common', 'Parameter error'));
        }

        $cardService = new Card($user->getUserId());

	    $cardService->setCheckBlack((int)$checkBlack);

//	    TODO 检测不准确，临时去掉域名检测，后续需要java改

	    $map = [
		    'EMAIL_NORMAL'       => 0,
		    'EMAIL_BLACKLIST'    => 1,
		    'EMAIL_FORMAT_ERROR' => 3,
	    ];

        $emailResult = $cardService->matchEmail($emailArray, $map);

        $result = [];
        foreach ($emailResult as $value) {
            $result[$value['email']] = $value;
        }

        $rsp = new PBEmailsMatchRsp();
        $match_list = $rsp->getMatchList();
        foreach ($emailArray as $email) {
            $match = new PBEmailMatch();
            $email = trim(strtolower(\common\library\email\Util::getPureEmail($email)));
            $data = $result[$email] ?? [];
            if (empty($data)) {
                $emailName = explode('@' ,$email)[0];
                $data = ['id' => 0, 'name' => $emailName, 'email' => $email, 'card_type' => EmailIdentity::CARD_TYPE_STRANGER];
            }
            $match->setReferId($data['id'] ?? 0);
            $match->setName($data['name']);
            $match->setEmail($data['email']);
            $match->setCardType(intval($data['card_type']));
	        $match->setIsBlack($data['is_black'] ?? 0);

            $match_list[] = $match;

        }

        $rsp->setMatchList($match_list);

        $this->successPb($rsp);
    }

    /**
     * 待处理邮件列表
     * 当未传mailIds时，查询该用户所有待处理邮件
     */
    public function actionSyncTodo()
    {
        $user = User::getLoginUser();
        $todoList = new \common\library\mail\setting\todo\MailTodoList($user->getUserId());
        $mailTodoList = $todoList->find();

        $rsp = new PBMailTodoSyncRsp();
        $detailList = $rsp->getTodoList();
        foreach ($mailTodoList as $elem) {
            $todoDetail = new PBMailTodoDetail();
            $todoDetail->setMailId(intval($elem['mail_id']));
            $todoDetail->setUserId(intval($elem['user_id']));
            $todoDetail->setCompletedFlag($elem['completed_flag'] > 0 ? true : false);
            $todoDetail->setRemindFlag($elem['remind_flag'] > 0 ? true : false);
            $todoDetail->setProcessTime(strtotime($elem['process_time']));
            $detailList[] = $todoDetail;
        }

        $rsp->setTodoList($detailList);

        $this->successPb($rsp);
    }

    public function actionSendTimeSuggest()
    {

        $request = new PBSendTimeSuggestReq();
        $this->buildRequest($request);
        $mailId = $request->getMailId();
        $emails = iterator_to_array($request->getEmailList());
        $user = User::getLoginUser();

        $list = \common\library\mail\Helper::sendTimeSuggest($mailId,$emails,$user->getUserId(),$user->getClientId());
        $rsp = new PBSendTimeSuggestRsp();
        $suggestList = $rsp->getSuggestList();

        foreach ($list as $item){
            $suggestData = new \protobuf\Email\PBSendTimeSuggest();
            $suggestData->setEmail($item['email']);
            $suggestData->setTimezone($item['timezone']);
            $suggestData->setCountry($item['country']);
            $suggestData->setTime($item['time']);
            $suggestData->setLocationSendTime($item['local_work_date']);
            $suggestData->setBeijingSendTime($item['beijing_date']);
            $suggestData->setTimeShow($item['time_show']);
            $suggestList[] = $suggestData;
        }
        $rsp->setSuggestList($suggestList);
        $this->successPb($rsp);
    }

    public function actionEmailIdentity()
    {
        $request = new PBEmailIdentityReq();
        $this->buildRequest($request);
        $mailIds = iterator_to_array($request->getMailIds());
        $emails = iterator_to_array($request->getEmails());
        $detailFlag = $request->getDetailFalg();
        $response = new PBEmailIdentityRsp();
        $identityList = $response->getIdentityList();

        if (empty($mailIds) && empty($emails)) {
            $this->successPb($response);
            return;
        }

        $user = User::getLoginUser();

        $deviceType = 'desktop';
        if (!empty($this->requestHeader->getDeviceType()) && in_array($this->requestHeader->getDeviceType(), [DeviceType::OTHER, DeviceType::WINDOWS, DeviceType::MAC, DeviceType::ANDROID, DeviceType::IOS])) {
            $deviceType = DeviceType::name($this->requestHeader->getDeviceType());
        }

        $lock = new \common\library\util\RedLock(RedisService::cache(), strtolower($deviceType)."_api_mail_read_email_identity_{$user->getUserId()}");
        if (!$lock->lock(2))
            throw new RuntimeException("请求过于频繁");

        $findEmails = [];
        if (!empty($mailIds)) {
            $mailIds = array_unique($mailIds);
            if (count($mailIds) > 50) {
                $lock->unlock();
                $this->failPb(PBErrorCode::UNKNOWN_ERROR, 'Invalid parameter. mail_ids number had more than 50', ErrorCode::CODE_FAIL);
                return;
            }
            $mailList = new \common\library\mail\MailList($user->getClientId());
            $mailList->setFields('sender,receiver,cc,bcc,reply_to');
            $mailList->setMailIds($mailIds);
            $mailList->setOrderBy([]);
            $mailList = $mailList->find();
            foreach ($mailList as $mail) {
                $emails = "{$mail['sender']};{$mail['receiver']};{$mail['cc']};{$mail['bcc']};{$mail['reply_to']}";
                $emails = EmailUtil::findAllMailAddress($emails);
                $findEmails = array_merge($findEmails, $emails ?? []);
            }
        } else {
            $emails = array_unique($emails);
            if (count($emails) > 50) {
                $lock->unlock();
                $this->failPb(PBErrorCode::UNKNOWN_ERROR, 'Invalid parameter. emails number had more than 50', ErrorCode::CODE_FAIL);
                return;
            }
            $emails = implode(';', $emails);
            $findEmails = EmailUtil::findAllMailAddress($emails);
        }
        $findEmails = array_values(array_unique($findEmails));

        if (empty($findEmails)) {
            $lock->unlock();
            $this->successPb($response);
            return;
        }

        $emailIdentityList = new \common\library\email_identity\EmailIdentityList($user->getUserId());
        $emailIdentityList->setEmails($findEmails);
        $emailIdentityList->getFormatter()->mailContactSummarySetting();
        $emailIdentityList->getFormatter()->setAppFlag(true);
        if ($detailFlag) $emailIdentityList->getFormatter()->cardInfoSetting(true);

        $result = $emailIdentityList->find();
        $nofFoundEmails = array_diff($findEmails, array_column($result, 'email'));
        foreach ($nofFoundEmails as $item) {
            $result[] = [
                'id' => 0,
                'name' => '',
                'email' => $item,
                'card_type' => EmailIdentity::CARD_TYPE_STRANGER,
            ];
        }

        $deviceType = $this->requestHeader->getDeviceType();
        $filterCardType = [];
        if (in_array($deviceType, [DeviceType::ANDROID, DeviceType::IOS]) && !$this->compareClientVersion('3.8.5', true)) {
            $filterCardType = [EmailIdentity::CARD_TYPE_ADVICE, EmailIdentity::CARD_TYPE_LEAD, EmailIdentity::CARD_TYPE_COLLEAGUE_LEAD, EmailIdentity::CARD_TYPE_PUBLIC_LEAD];
        }

        $adviceCardList = [];
        if (empty($filterCardType)) {
            $strangeEmails = [];
            foreach ($result as $item) {
                if ($item['card_type'] == EmailIdentity::CARD_TYPE_STRANGER) {
                    $strangeEmails[] = $item['email'];
                }
            }
            $cardService = new Card($user->getUserId());
            $adviceCardList = array_keys($cardService->adviceCardList($strangeEmails));
        }

        foreach ($result as $item) {
            $identity = new PBEmailIdentity();
            $identity->setEmail($item['email']);
            if (in_array($item['card_type'], [
                EmailIdentity::CARD_TYPE_CONTACT,
                EmailIdentity::CARD_TYPE_STRANGER,
                EmailIdentity::CARD_TYPE_COLLEAGUES,
                EmailIdentity::CARD_TYPE_ADVICE
            ])) {
                $identity->setName($item['name']);
            } else {
                $identity->setCompanyName($item['name']);
            }

            //移动端版本低于3.8.5暂时不支持card_type 类型9,10,11,12,需要暂时特殊处理为5
            if (!empty($filterCardType) && in_array($item['card_type'], $filterCardType)) {
                $item['card_type'] = EmailIdentity::CARD_TYPE_STRANGER;
            } else {
                if (!empty($adviceCardList) && in_array($item['email'], $adviceCardList)) {
                    $item['card_type'] = EmailIdentity::CARD_TYPE_ADVICE;
                }
            }

            if ($detailFlag) {
                //处理tel_list
                $customerData = ($item['specify_customer_data'] ?? ($item['customer'] ?? []));
                $telList = $customerData['tel_list'] ?? ($item['tel_list'] ?? []);
                $telList = array_map(function ($elem) {
                    return $elem[0] . ' ' . $elem[1];
                }, $telList);

                $identity->setTelList($telList);
                $identity->setPosition($customerData['post'] ?? '');
                $identity->setCompanyId($item['company_id'] ?? 0);
                $identity->setLeadId($item['lead_id'] ?? 0);
                if (in_array($item['card_type'], [
                    EmailIdentity::CARD_TYPE_CONTACT,
                    EmailIdentity::CARD_TYPE_STRANGER,
                    EmailIdentity::CARD_TYPE_COLLEAGUES,
                    EmailIdentity::CARD_TYPE_ADVICE
                ])) {
                    $identity->setName($item['name']);
                } else {
                    $identity->setName($customerData['name'] ?? '');
                }

            }
            $identity->setCardType($item['card_type']);
            $identityList[] = $identity;
        }
        $response->setIdentityList($identityList);
        $lock->unlock();
        $this->successPb($response);
    }

    //获取邮件语言
    public function actionMailLanguage()
    {
        $req = new \protobuf\MailSync\PBMailLanguageReq();
        $this->buildRequest($req);
        $mailId = $req->getMailId();

        $language = \common\library\mail\Helper::getMailSourceLanguage($mailId);
        $rsp = new \protobuf\MailSync\PBMailLanguageRsp();
        $rsp->setLanguage($language);

        $this->successPb($rsp);
    }

    //邮件翻译
    public function actionMailTranslate()
    {
        $req = new \protobuf\MailSync\PBMailTranslateReq();
        $this->buildRequest($req);

        $user = User::getLoginUser();
        $userId = $user->getUserId();
        $clientId = $user->getClientId();

        $mailId = $req->getMailId();
        $source = $req->getSource();
        $target = $req->getTarget();

        $mail = new \common\library\mail\Mail($mailId);
        $translate = new \common\library\mail\MailTranslate($mail);
        $translate->setSource($source);
        $translate->setTarget($target);
        $result = $translate->translate();

        //记录最近一次翻译的语言
        $translate->setLastTranslateContentKey($userId);
        //记录用户选择的语言
        \common\library\mail\MailTranslateSetting::languageSetting($userId, $source, $target);

        //点击翻译，上报神策
        list($textLength, $htmlLength) = $translate->getTranslateLength();
        $sensorsEvent = new EventTranslate($clientId, $userId);

        $platformType = EventTranslate::PLATFORM_DESKTOP;
        if($this->requestHeader->getDeviceType() == \protobuf\Common\DeviceType::IOS)
        {
            $platformType = EventTranslate::PLATFORM_IOS;
        }else if($this->requestHeader->getDeviceType() == \protobuf\Common\DeviceType::ANDROID)
        {
            $platformType = EventTranslate::PLATFORM_ANDROID;
        }

        $sensorsEvent->setParams([
            'platform_type' => $platformType,
            'source_lang' => $source,
            'target_lang' => $target,
            'text_length' => $textLength,
            'html_length' => $htmlLength,
            'translate_length' => mb_strlen($result)
        ]);
        $sensorsEvent->report();

        $rsp = new \protobuf\MailSync\PBMailTranslateRsp();
        $rsp->setContent($result);
        $this->successPb($rsp);
    }

    public function actionLargeAttachList()
    {

        $req = new PBLargeAttachListReq();
        $this->buildRequest($req);

        $user = User::getLoginUser();
        $page = $req->getPage();
        $pageSize = $req->getPageSize();

        $list = new \common\library\external_file\FileList($user->getClientId(), $user->getUserId());
        $list->setLimit($pageSize);
        $list->setOffset(($page - 1) * $pageSize);
        $list->getFormatter()->listInfoSetting();
        $data = $list->find();
        $total = $list->count();

        $rsp = new PBLargeAttachListRsp();
        $attachList = $rsp->getAttachList();

        foreach ($data as $item) {

            if(!$item['file_id']){
                continue;
            }
            $largeAttach = new \protobuf\Email\PBLargeAttach();
            $largeAttach->setFileId($item['file_id']);
            $largeAttach->setExpireTime($item['expire_time']??"");


            if($item['file']){
                $fileInfo = new \protobuf\CRMCommon\PBFileInfo();
                $fileInfo->setFileId($item['file']['file_id']??0);
                $fileInfo->setFileName($item['file']['file_name']??"");
                $fileInfo->setFileSize($item['file']['file_size']??"");
                $fileInfo->setFileExt($item['file']['file_ext']??"");
                $fileInfo->setFileUrl($item['file']['file_src']??"");
                $largeAttach->setFileInfo($fileInfo);
            }
            if($item['user']){
                $userInfo = new \protobuf\CRMCommon\PBUserInfo();
                $userInfo->setUserId($item['user']['user_id']);
                $userInfo->setNickname($item['user']['nickname']??"");
                $userInfo->setAvatar($item['user']['avatar']??"");
                $position[] = $item['user']['position']??"";
                $userInfo->setPosition($position);
                $largeAttach->setUserInfo($userInfo);
            }

            $attachList[] = $largeAttach;
        }

        $rsp->setAttachList($attachList);
        $rsp->setCount($total);
        $this->successPb($rsp);
    }

    public function actionMailList()
    {
        $req = new \protobuf\MailSync\PBMailListReq();
        $this->buildRequest($req);

        $typeMap = [
            \protobuf\MailSync\PBMailListType::LIST_TYPE_RECEIVE => 'receive',
            \protobuf\MailSync\PBMailListType::LIST_TYPE_SEND => 'send',
            \protobuf\MailSync\PBMailListType::LIST_TYPE_TODO => 'todo',
            \protobuf\MailSync\PBMailListType::LIST_TYPE_TAG => 'tag',
            \protobuf\MailSync\PBMailListType::LIST_TYPE_UNREAD => 'unread',
            \protobuf\MailSync\PBMailListType::LIST_TYPE_FOLDER => 'folder',
        ];

        $companyFlagType = [
            \protobuf\MailSync\PBMailListCompanyFlagType::FLAG_TYPE_ALL => null,
            \protobuf\MailSync\PBMailListCompanyFlagType::FLAG_TYPE_TRUE => 1,
            \protobuf\MailSync\PBMailListCompanyFlagType::FLAG_TYPE_FALSE => 0,
        ];

        $relateTypeMap = [
            \protobuf\MailSync\PBMailListRelateType::MAIL_RELATE_TYPE_ALL => null,
            \protobuf\MailSync\PBMailListRelateType::MAIL_RELATE_TYPE_COMPANY => 1,
            \protobuf\MailSync\PBMailListRelateType::MAIL_RELATE_TYPE_COLLEAGUE => 2,
            \protobuf\MailSync\PBMailListRelateType::MAIL_RELATE_TYPE_CONTACT => 3,
            \protobuf\MailSync\PBMailListRelateType::MAIL_RELATE_TYPE_OTHER => 0,
        ];

        $type = $req->getType();
        $listType = $typeMap[$type] ?? $typeMap[\protobuf\MailSync\PBMailListType::LIST_TYPE_RECEIVE];
        $companyFlag = $req->getCompanyFlag();
        $relateType = $req->getRelateType();

        $listCompanyFlag = $companyFlag ? ($companyFlagType[$companyFlag] ?? null) : ($relateTypeMap[$relateType] ?? null);

        $userMailId = $req->getUserMailId();
        $folderId = $req->getFolderId();
        $offset = $req->getOffset();
        $limit = empty($req->getLimit()) ? 20 : $req->getLimit();
        $tagId = $req->getTagId();
        $pin = $req->getPin();
        $todoCompletedFlag = $req->getTodoCompletedFlag();

        if ($this->isMobile() && $listType == 'send') {
            $folderId = [\Mail::FOLDER_SEND_ID, \Mail::FOLDER_EXPOSE_ID];
        }

        $mailList = new \common\library\mail\MailList();
        $params = [
            'user_mail_id'    => $userMailId,
            'tag_id'          => $tagId,
            'folder_id'       => $folderId,
            'pin'             => $pin,
            'customer_emails' => [],
            'todo_completed_flag' => $todoCompletedFlag,
            'report_item_unique_key' => '',
        ];
        $mailList->getListStrategy($listType, $params);
        $mailList->setLimit($limit);
        $mailList->setOffset($offset);
        $mailList->setRelateCompanyFlag($listCompanyFlag);
        $count = $mailList->count();
        $mailIds = [];
        if ($count > 0) {
            $mailList->setFields('mail_id');
            $mailIds  = array_column($mailList->find(), 'mail_id');
        }
        $rsp = new \protobuf\MailSync\PBMailListRsp();
        $rsp->setMailIds($mailIds);
        $rsp->setCount($count);
        $this->successPb($rsp);
    }

    //待发送群发单显邮件列表
    public function actionExposeMailProgressList()
    {
        $user = User::getLoginUser();
        $userId = $user->getUserId();

        $req = new \protobuf\MailSync\PBExposeMailProgressListReq();
        $this->buildRequest($req);
        $mailId = $req->getMailId();
        $rsp = new \protobuf\MailSync\PBExposeMailProgressListRsp();
        $pbExposeList = $rsp->getExposeProgressList();

        $asyncMailList = new \common\library\mail\MailAsyncTaskList($userId);
        $asyncMailList->setMailId($mailId);
        $asyncMailList->setGroupFlag([
            AsyncMailTask::GROUP_FLAG_ROOT_MAIL,
            AsyncMailTask::GROUP_FLAG_CLIENT_ROOT_MAIL
        ]);
        $asyncMailList->setStatus(AsyncMailTask::STATUS_WAITING);
        $asyncMailList->setTimeFlag(AsyncMailTask::TASK_SENDING);
        $asyncMailTasks = $asyncMailList->find();
        foreach ($asyncMailTasks as $taskInfo) {
            if (!isset($taskInfo['mail_id'])) {
                continue;
            }
            $pbMailInfo = new \protobuf\MailSync\PBExposeMailInfo();
            $pbMailInfo->setMailId($taskInfo['mail_id']);
            $pbMailInfo->setSubject($taskInfo['subject']);
            $pbMailInfo->setTotal($taskInfo['total']);
            $pbMailInfo->setProcessedCount($taskInfo['processedCount'] ?? 0);
            if (!empty($taskInfo['time'])) $pbMailInfo->setTime(strtotime($taskInfo['time']));
            if (isset($taskInfo['expose_suspend_flag'])){
                $pbMailInfo->setExposeSuspendFlag($taskInfo['expose_suspend_flag']);
                if (!empty($taskInfo['expose_suspend_time'])) $pbMailInfo->setExposeSuspendTime(strtotime($taskInfo['expose_suspend_time']) ?? 0);
                $pbMailInfo->setSender($taskInfo['sender']);
            }
            $pbExposeList[] = $pbMailInfo;
        }
        $rsp->setExposeProgressList($pbExposeList);

        $this->successPb($rsp);
    }

    //获取邮箱的群发单显数量信息
    public function actionExposeCountInfo()
    {
        $req = new \protobuf\MailSync\PBExposeCountInfoReq();
        $this->buildRequest($req);
        $userMailIds = array_filter(iterator_to_array($req->getUserMailIds()));
        $planDateTimeStamp = $req->getPlanTime();
        $userMailIds = array_filter($userMailIds);
        if (empty($userMailIds))
        {
            $this->successPb();
            return;
        }

        $rsp = new \protobuf\MailSync\PBExposeCountInfoRsp();
        $pbInfoList = $rsp->getExposeCountInfoList();
        $planDate = empty($planDateTimeStamp) ? date('Y-m-d') : date('Y-m-d', $planDateTimeStamp);
        foreach ($userMailIds as $userMailId) {
            $key = \common\library\mail\service\ExposeService::$expose_prefix . '_' . $userMailId . '_' . $planDate . "_totals";
            $redisResult = \Yii::app()->cache->executeCommand('HVALS', [$key]);
            $count = 0;
            foreach ($redisResult as $item) {
                $count += count(explode(',', $item));
            }
            $emailObj = \common\library\email\Email::getEmailObject($userMailId);
            $pbInfo = new \protobuf\MailSync\PBExposeCountInfo();
            $pbInfo->setUserMailId($userMailId);
            $pbInfo->setUsedCount($count);
            $pbInfo->setTotalCount($emailObj->getExposeTotalNum());
            $pbInfoList[] = $pbInfo;
        }
        $rsp->setExposeCountInfoList($pbInfoList);
        $this->successPb($rsp);
    }

    public function actionUnreadStatistics()
    {
        $user = User::getLoginUser();
        $clientId = $user->getClientId();
        $userId = $user->getUserId();

        $rsp = new \protobuf\MailSync\PBUnreadStatisticsRsp();
        try {
            $udpData = \common\library\swoole\client\MailUnreadClient::unread($clientId, $userId);
        } catch (Exception $exception) {
            LogUtil::error($exception->getMessage());
            return  $this->successPb($rsp);
        }
        foreach ($udpData as &$item)
        {
            if (is_array($item)) {
                foreach ($item as &$elem) {
                    if (is_array($elem)) {
                        foreach ($elem as &$mem) {
                            if ((int)$mem < 0) {
                                $mem = 0;
                            }
                        }
                    } elseif (is_numeric($elem)) {
                        if ((int)$elem < 0) {
                            $elem = 0;
                        }
                    }
                }
            } else {
                if ((int)$item < 0) {
                    $item = 0;
                }
            }
        }
        $rsp->setAllFolder($udpData['all_folder']);

        $unreadInfo = $udpData['unread'];
        $pbUnread = new \protobuf\MailSync\PBUnreadInfo();
        $pbUnread->setCount($unreadInfo['count']);
        $pbUnread->setCompanyCount($unreadInfo['company_count']);
        $pbUnread->setColleagueCount($unreadInfo['colleague_count']);
        $pbUnread->setContactCount($unreadInfo['contact_count']);
        $pbUnread->setOtherCount($unreadInfo['other_count']);
        $rsp->setUnread($pbUnread);

        $pbFolders = $rsp->getFolder();
        $folderInfos = $udpData['folder'];
        foreach ($folderInfos as $folderId => $folderInfo)
        {
            $pbFolderInfo = new \protobuf\MailSync\PBUnreadInfo();
            $pbFolderInfo->setCount($folderInfo['count']);
            $pbFolderInfo->setCompanyCount($folderInfo['company_count']);
            $pbFolderInfo->setColleagueCount($folderInfo['colleague_count']);
            $pbFolderInfo->setContactCount($folderInfo['contact_count']);
            $pbFolderInfo->setOtherCount($folderInfo['other_count']);
            $pbFolders[$folderId] = $pbFolderInfo;
        }
        $junkFolderId = Mail::FOLDER_JUNK_ID;
        $pbJunkFolderInfo = new \protobuf\MailSync\PBUnreadInfo();
        $pbJunkFolderInfo->setCount($udpData['junk']);
        $pbFolders[$junkFolderId] = $pbJunkFolderInfo;
        $rsp->setFolder($pbFolders);

        $pbInboxList = $rsp->getInbox();
        $inboxInfos = $udpData['inbox'];
        foreach ($inboxInfos as $userMailId => $inboxInfo)
        {
            $pbInboxInfo = new \protobuf\MailSync\PBUnreadInfo();
            $pbInboxInfo->setCount($inboxInfo['count']);
            $pbInboxInfo->setCompanyCount($inboxInfo['company_count']);
            $pbInboxInfo->setColleagueCount($inboxInfo['colleague_count']);
            $pbInboxInfo->setContactCount($inboxInfo['contact_count']);
            $pbInboxInfo->setOtherCount($inboxInfo['other_count']);
            $pbInboxList[$userMailId] = $pbInboxInfo;
        }
        $rsp->setInbox($pbInboxList);

        $this->successPb($rsp);
    }

    public function actionSyncUserMailInfo()
    {
        $user = User::getLoginUser();
        $userId = $user->getUserId();
        $clientId = $user->getClientId();

        $userMailIdList = new UserMailList();
        $userMailIdList->setUserId($userId);
        $userMailIdList = $userMailIdList->find();
        $userMailIds = array_column($userMailIdList, 'user_mail_id');
        $userMailIdsSql = implode(',', $userMailIds);

        if (empty($userMailIds))
        {
            $this->successPb();
            return;
        }

        $req = new PBUserMailCompareReq();
        $this->buildRequest($req);
        $latestLimit = $req->getLatestLimit();
        $maxMailId = $req->getMaxMailId();
        $limit = ($latestLimit > 25000 | $latestLimit == 0) ? 25000 : $latestLimit;
        $buildMailIdSql = $maxMailId ? " and mail_id > {$maxMailId} " : '';

        $db = ProjectActiveRecord::getDbByClientId($clientId);
        // 查询每个userMailId对应的最大version
        $userMailIdCount = count($userMailIds);

        $folderListObj = new \common\library\mail\setting\folder\MailFolderList($userId);
        $folderListObj->setFields('folder_id');
        $folderList   = $folderListObj->find();
        $folderIdList = array_column($folderList, 'folder_id');
        $folderIds = array_merge([1,2], $folderIdList);

        $commonMailConditionSql = " user_mail_id in ({$userMailIdsSql}) and folder_id in (" . implode(',', $folderIds) . ") ";
        // 查最小的receive_time
        $selectMinReceiveTimeSql = "select receive_time from tbl_mail where $commonMailConditionSql ORDER BY receive_time DESC limit " . ($limit-1) . ",1";
        $minReceiveTime = $db->createCommand($selectMinReceiveTimeSql)->queryScalar();
        if (!$minReceiveTime) {
            $this->successPb();
            return;
        }
        // 根据mail_id正序排
        $selectMailSql = "select mail_id from tbl_mail where $commonMailConditionSql and receive_time>='{$minReceiveTime}' {$buildMailIdSql} order by receive_time desc  limit {$limit}";
        $data = $db->createCommand($selectMailSql)->queryColumn();

        $mapping = [];
        foreach ($data as $index => $mailId)
        {
            $syncInfo = (1 << \common\library\version\Constant::MAIL_SYNC_BIT_ALL_INFO) | (1 << \common\library\version\Constant::MAIL_SYNC_BIT_TAG) | (1 << \common\library\version\Constant::MAIL_SYNC_BIT_EXTRA_STATUS_INFO);
            if (array_key_exists($mailId, $mapping)) {
                $mapping[$mailId][1] = $syncInfo;
            } else {
                $mapping[$mailId] = [$mailId, $syncInfo, 0];
            }
        }
        $mapping = array_values($mapping);

        $rsp = new PBUserMailCompareRsp();
        if (empty($data))
        {
            $this->successPb();
            return;
        }

        $redis = RedisService::cache();
        $key = \common\library\version\Constant::SYNC_USER_MAIL_VERSION_PREFIX . "{$clientId}_{$userId}_init_mail_{$latestLimit}_{$maxMailId}";
        $count = count($mapping);
        LogUtil::info("redisSyncInfo: {$key} count:{$count}");
        $expiredTime = 1200;
        $redis->set($key, serialize($mapping), 'EX', $expiredTime);
        $rsp->setSessionId($key);
        $rsp->setSize(count($mapping));
        $rsp->setExpiredTime($expiredTime);

        $pbUserMailVersionList = $rsp->getUserMailVersion();
        foreach ($userMailIds as $userMailId)
        {
            $mailVersion = new \common\library\version\MailVersion($clientId, $userMailId);
            $maxVersion = $mailVersion->getMaxVersion();

            $pbUserMailVersion = new PBUserMailVersion();
            $pbUserMailVersion->setUserMailId($userMailId);
            $pbUserMailVersion->setMaxVersion($maxVersion);
            $pbUserMailVersionList[] = $pbUserMailVersion;
        }
        $rsp->setUserMailVersion($pbUserMailVersionList);
        $this->successPb($rsp);
    }
    // 划词翻译
    public function actionTranslateContent()
    {
        $req = new \protobuf\MailSync\PBTranslateContentReq();
        $this->buildRequest($req);
        $content = $req->getContent();
        $source = $req->getSource();
        $target = $req->getTarget();
        (new \common\library\validation\Validator(
            ['content' => $content, 'source' => $source, 'target' => $target],
            [
                'content' => 'not_empty|string|max:5000',
                'source' => 'not_empty|string',
                'target' => 'not_empty|string',
            ]
        ))->validate();

        $result = \common\library\mail\Helper::translateContent($source, $target, $content);

        $rsp = new \protobuf\MailSync\PBTranslateContentRsp();
        $rsp->setContent($result);
        $this->successPb($rsp);
    }

    // 邮件行为鉴权接口
    public function actionCheckMailAccess()
    {
        $req = new \protobuf\MailSync\PBCheckMailAccessReq();
        $this->buildRequest($req);
        $mailId = $req->getMailId();
        $accessType = $req->getAccessType();
        $mail = new \common\library\mail\Mail($mailId);
        switch ($accessType) {
            case \protobuf\MailSync\PBMailAccessType::ACCESS_READ:
                $mail->getAccessService()->checkReadAccess();
                break;
            case \protobuf\MailSync\PBMailAccessType::ACCESS_WRITE:
                $mail->getAccessService()->checkWriteAccess();
                break;
            case \protobuf\MailSync\PBMailAccessType::ACCESS_SEND:
                $mail->getAccessService()->checkSendAccess();
                break;
            case \protobuf\MailSync\PBMailAccessType::ACCESS_DELETE:
                $mail->getAccessService()->checkDeleteAccess();
                break;
            case \protobuf\MailSync\PBMailAccessType::ACCESS_REPLY:
                $mail->getAccessService()->checkReplyAccess();
                break;
            case \protobuf\MailSync\PBMailAccessType::ACCESS_FORWARD:
                $mail->getAccessService()->checkForwardAccess();
                break;
            default:
                throw new RuntimeException("不支持的邮件行为");
        }
        $this->successPb();
    }

    /**
     * 获取module、邮箱最新版本号
     * type 0-获取全部；1-获取module最新版本号；2-获取邮箱最新版本号
     * @throws ProcessException
     */
    public function actionLatestVersion(){
        $request = new PBLatestVersionReq();
        $this->buildRequest($request);
        $type = $request->getType();

        $user = User::getLoginUser();
        $userId = $user->getUserId();
        $clientId = $user->getClientId();

        $db = ProjectActiveRecord::getDbByClientId($clientId);
        $rsp = new PBLatestVersionRsp();

        if ($type == 1 || $type == 0) {
            $moduleList  = $rsp->getModuleList();
            $userVersion = $db->createCommand("select user_id, module, max(version) as version from tbl_user_module_version where user_id=$userId group by user_id, module")->queryAll();
            foreach (ModuleService::getUserModules() as $module) {
                $version = 0;
                foreach ($userVersion as $elem) {
                    if($elem['module'] == $module) {
                        $version = $elem['version'];
                    }
                }
                $ver = new PBPushUserSettingVersion();
                $ver->setUserId($userId);
                $ver->setVersion($version);
                $ver->setModule($module);
                $moduleList[] = $ver;
            }
            $rsp->setModuleList($moduleList);
        }

        if ($type == 2 || $type == 0) {
            $mailList  = $rsp->getMailList();

            // 邮箱草稿箱最新版本号
            $mailDraftVersion = new \common\library\version\MailDraftVersion($clientId, $userId);
            $version = intval($mailDraftVersion->getMaxVersion());
            $draftVersion= new PBPushMailVersion();
            $draftVersion->setUserMailId(0);
            $draftVersion->setVersion($version ?? 0);
            $mailList[] = $draftVersion;

            // 邮箱最新版本号
            $userMailList = new UserMailList();
            $userMailList->setClientId($clientId);
            $userMailList->setUserId($userId);
            $userMailList = $userMailList->find();
            foreach ($userMailList as $userMailInfo)
            {
                $userMailId = $userMailInfo['user_mail_id'];
                $mailVersion = new \common\library\version\MailVersion($clientId, $userMailId);
                $version = intval($mailVersion->getMaxVersion());
                $mailVer = new PBPushMailVersion();
                $mailVer->setUserMailId($userMailId);
                $mailVer->setVersion($version ?? 0);
                $mailList[] = $mailVer;
            }
            $rsp->setMailList($mailList);
        }

        $this->successPb($rsp);
    }

    public function actionConversationMailList()
    {
        $request = new \protobuf\MailSync\PBConversationMailListReq();
        $this->buildRequest($request);
        $conversationId = $request->getConversationId();
        $reqUserId = $request->getUserId();
        $curPage = $request->getCurPage() !== 0 ? $request->getCurPage() : 1;
        $pageSize = $request->getPageSize() !== 0 ? $request->getPageSize() : 10;
        $mailDetailFlag = $request->getMailDetailFlag();

        (new \common\library\validation\Validator(
            [
                'conversationId' => $conversationId,
                'pageSize' => $pageSize,
            ],
            [
                'conversationId' => 'not_empty|integer',
                'pageSize' => 'integer|max:100',
            ]
        ))->validate();

        $viewingUser = User::getLoginUser();
        $clientId = $viewingUser->getClientId();
        $viewingUserId = $viewingUser->getUserId();
        $listUserId = empty($reqUserId) ? $viewingUserId : $reqUserId;

        $list = new \common\library\mail\MailList($clientId, $listUserId);
        $list->setConversationId($conversationId);
        $list->setDeleteFlag(0);
        $list->setNotInFolderIds([\Mail::FOLDER_JUNK_ID]);
        $list->setLimit($pageSize);
        $list->setOffset($pageSize * ($curPage - 1));
        $count = $list->count();
        $rsp = new \protobuf\MailSync\PBConversationMailListRsp();
        if (empty($count)) {
            $this->successPb($rsp);
            return;
        }
        $rsp->setCount($count);
        if ($mailDetailFlag) {
            $list->getFormatter()->appConversationMailListSetting();
            $data = $list->find();

            $pbMailInfoList = $rsp->getMailInfo();
            foreach ($data as $mail) {
                $pbMailInfo = $this->setMailInfo($mail);
                if ($pbMailInfo) {
                    $pbMailInfoList[] = $pbMailInfo;
                }
            }
            $rsp->setMailInfo($pbMailInfoList);
        } else {
            $mailIds = array_column($list->find(), 'mail_id');
            $rsp->setMailIds($mailIds);
        }
        $this->successPb($rsp);
    }

    public function actionConversationStatistic()
    {
        $req = new \protobuf\MailSync\PBConversationStatisticReq();
        $this->buildRequest($req);
        $conversationIds = iterator_to_array($req->getConversationIds());
        if (count($conversationIds) > 100) {
            throw new RuntimeException("conversationIds params error");
        }


        $user = User::getLoginUser();
        $clientId = $user->getClientId();
        $conversationIds = array_values(array_filter($conversationIds));


        $statisticInfoList = \common\library\mail\Helper::getStatisticByConversationIds($clientId, $conversationIds);
        $rsp = new \protobuf\MailSync\PBConversationStatisticRsp();
        $pbList = $rsp->getConversationCountList();
        foreach ($statisticInfoList as $info)
        {
            $countInfo = new \protobuf\MailSync\PBConversationCountInfo();
            $countInfo->setConversationId($info['mail_conversation_id']);
            $countInfo->setCount($info['mail_count']);
            $pbList[] = $countInfo;
        }
        $rsp->setConversationCountList($pbList);
        $this->successPb($rsp);
    }

    public function actionMailLastTranslateOption()
    {
        $user = User::getLoginUser();
        $userId = $user->getUserId();

        list($source, $target) = \common\library\mail\MailTranslateSetting::getLanguageSetting($userId);
        $rsp = new \protobuf\MailSync\PBMailLastTranslateOptionRsp();
        $rsp->setSource($source);
        $rsp->setTarget($target);

        $this->successPb($rsp);
    }

    public function actionExtraData()
    {
        $user = User::getLoginUser();
        $userId = $user->getUserId();
        $request = new PBMailExtraDataReq();
        $this->buildRequest($request);

        $mailIds = iterator_to_array($request->getMailIds());
        $fields = iterator_to_array($request->getFields());

        $rsp = new PBMailExtraDataRsp();
        $extraData = (new MailExtraData($userId))->buildExtraData($mailIds,$fields);
        $rsp->setMailExtraData(array_values($extraData));

        return $this->successPb($rsp);
    }

    public function actionSubordinateMailInfo()
    {
        $user = User::getLoginUser();
        $loginUserId = $user->getUserId();
        $clientId = $user->getClientId();
        $request = new PBSubordinateMailReq();
        $this->buildRequest($request);

        $mailId = $request->getMailId();
        $skipViewPrivilege = $request->getSkipViewPrivilege();

        $pbSubordinateMailRsp = new PBSubordinateMailRsp();

        $db = ProjectActiveRecord::getDbByClientId($clientId);

        $sendStatusMessage = $db->createCommand("select fail_desc from tbl_async_mail_task where mail_id={$mailId} and status=1")->queryColumn()[0] ?? '';
        $extMap = \MailExternal::findByMailIds([$mailId], ['plan_send_time', 'expose_flag', 'subject_remark', 'delay_send_flag','expose_user_mail_info','mail_bounced_by', 'subject_remark','risk_reason','sign_id']);
        $baseMailExposeInfo = MailExpose::findByMailIds([$mailId]);
        $field = 'mail_id,user_id,send_status,user_mail_id,mail_type,subject,sender,receiver,reply_to,cc,bcc,email_size, create_time,update_time,receive_time,
    attach_flag,urgent_flag,receipt_flag,time_flag,track_flag,reply_to_mail_id,folder_id,read_flag,open_flag,star_flag,reply_flag,forward_flag,delete_flag,distribute_flag,conversation_id,relate_company_flag';
        $sql = "select $field from tbl_mail where mail_id={$mailId}";
        $baseInfo = $db->createCommand($sql)->queryRow(true);

        if(!empty($baseInfo)) {

            if(!$skipViewPrivilege) {
                $mail = new \common\library\mail\Mail($mailId);
                $operate_role = $mail->getAccessService()->checkReadAccess();
            }

            if ($this->isMobile() && isset($baseInfo['folder_id']) && $baseInfo['folder_id'] == \Mail::FOLDER_EXPOSE_ID) {
                $baseInfo['folder_id'] = \Mail::FOLDER_SEND_ID;
            }

            //send status
            $sendStatus = new PBMailSendStatus();
            $sendStatus->setMailId($mailId);
            $sendStatus->setSendStatus($baseInfo['send_status']);
            $sendStatus->setMessage($sendStatusMessage);
            $pbSubordinateMailRsp->setSendStatus($sendStatus);

            //base info
            $base = new PBMailBaseInfo();
            $base->setSignId(($baseInfo['sign_id'] ?? 0));
            $base->setUserMailId($baseInfo['user_mail_id']);
            $base->setMailId($baseInfo['mail_id']);
            $base->setMailType($baseInfo['mail_type']);
            $base->setSubject($baseInfo['subject']);
            $base->setSender($baseInfo['sender']);
            $base->setReceiver($baseInfo['receiver']);
            $base->setCc($baseInfo['cc']);
            $base->setBcc($baseInfo['bcc']);
            $base->setEmailSize($baseInfo['email_size']);
            $base->setCreateTime(strtotime($baseInfo['create_time']));
            $base->setUpdateTime(strtotime($baseInfo['update_time']));
            $base->setReceiveTime(strtotime($baseInfo['receive_time']));
            $base->setAttachFlag($baseInfo['attach_flag']);
            $base->setUrgentFlag($baseInfo['urgent_flag']);
            $base->setReceiptFlag($baseInfo['receipt_flag']);
            $base->setTimeFlag($baseInfo['time_flag']);
            $base->setTrackFlag($baseInfo['track_flag']);
            $base->setReplyToMailId($baseInfo['reply_to_mail_id']);
            $base->setReplyTo($baseInfo['reply_to']);
            $planSendTime = $extMap[$baseInfo['mail_id']]['plan_send_time'] ?? 0;
            $planSendTime = $planSendTime ? strtotime($planSendTime) : 0;
            $base->setPlanSendTime($planSendTime);
            $base->setExposeFlag((int)($extMap[$baseInfo['mail_id']]['expose_flag'] ?? 0));
            $base->setSubjectRemark($extMap[$baseInfo['mail_id']]['subject_remark'] ?? '');
            $base->setDelaySendFlag($extMap[$baseInfo['mail_id']]['delay_send_flag'] ?? 0);

            $exposeUserMailInfo = $extMap[$baseInfo['mail_id']]['expose_user_mail_info'] ?? [];
            if(is_string($exposeUserMailInfo)) {
                $exposeUserMailInfo = json_decode($exposeUserMailInfo, true) ?? [];
            }
            $exposeUserMailIds = $exposeUserMailInfo['user_mail_ids'] ?? [];
            $exposeMinWaitTime = $exposeUserMailInfo['min_wait_time'] ?? 0;
            $exposeMaxWaitTime = $exposeUserMailInfo['max_wait_time'] ?? 0;
            $sender = ($exposeUserMailInfo['sender'] ?? '') ?: '';
            $signMode = $exposeUserMailInfo['sign_mode'] ?? 0;
            $subject = json_decode($baseMailExposeInfo[$baseInfo['mail_id']]['extra'] ?? null, true)['subject'] ?? [];
            $mailExposeInfo = new PBMailExposeInfo();
            $mailExposeInfo->setUserMailIds($exposeUserMailIds);
            $mailExposeInfo->setMinWaitTime($exposeMinWaitTime);
            $mailExposeInfo->setMaxWaitTime($exposeMaxWaitTime);
            $mailExposeInfo->setSender($sender);
            $mailExposeInfo->setSignMode($signMode);
            $mailExposeInfo->setSubject($subject);

            $base->setMailExposeInfo($mailExposeInfo);

            $pbSubordinateMailRsp->setBaseInfo($base);

            //status info
            $status = new PBMailStatusInfo();
            $status->setMailId($baseInfo['mail_id']);
            $status->setFolderId($baseInfo['folder_id']);
            $status->setReadFlag($baseInfo['read_flag']);
            $status->setOpenFlag($baseInfo['open_flag']);
            $status->setStarFlag($baseInfo['star_flag']);
            $status->setReplyFlag($baseInfo['reply_flag']);
            $status->setForwardFlag($baseInfo['forward_flag']);
            $status->setDeleteFlag($baseInfo['delete_flag']);
            $status->setDistributeFlag($baseInfo['distribute_flag']);
            $status->setBouncedMailId((int)($extMap[$baseInfo['mail_id']]['mail_bounced_by'] ?? 0));
            $status->setSubjectRemark($extMap[$baseInfo['mail_id']]['subject_remark'] ?? '');
            $status->setConversationId($baseInfo['conversation_id']);
            $status->setRelateCompanyFlag($baseInfo['relate_company_flag']);

            $pbSubordinateMailRsp->setStatusInfo($status);

            //attach
            $attach = MailAttach::getFormatMailAttach([$mailId]);
            foreach ($attach as $mailId => $list)
            {
                $inlineData = $list[1] ?? [];
                $attachData = $list[0] ?? [];
                $info = new PBMailAttachmentInfo();

                $aList = $info->getAttachmentList();
                foreach ($attachData as $datum)
                {
                    $file = new PBFileInfo();
                    $file->setUniqueKey($datum['md5_file'].'_'.$datum['file_size']);
                    $file->setFileId($datum['file_id']);
                    $file->setFileName($datum['file_name']);
                    $file->setFileUrl($datum['file_url']);
                    $file->setFileSize($datum['file_size']);
                    $file->setExpiredTime($datum['expired_time']);

                    $aList[] = $file;
                }
                $info->setAttachmentList($aList);

                $iList = $info->getInlineImageList();
                foreach ($inlineData as $datum)
                {
                    $file = new PBFileInfo();
                    $file->setUniqueKey($datum['md5_file'].'_'.$datum['file_size']);
                    $file->setFileId($datum['file_id']);
                    $file->setFileName($datum['file_name']);
                    $file->setFileUrl($datum['file_url']);
                    $file->setPreviewUrl($datum['preview_url']);
                    $file->setFileSize($datum['file_size']);

                    $iList[] = $file;
                }
                $info->setInlineImageList($iList);

                $info->setMailId($mailId);

                $pbSubordinateMailRsp->setAttachInfo($info);
            }

            //large attach
            $data = \common\library\external_file\Helper::getMailLargeAttach([$mailId],$clientId);
            foreach ($data as $mail_id => $mailLargeAttachDetail) {
                $largeAttachDetailList = $pbSubordinateMailRsp->getLargeAttachList();
                foreach ($mailLargeAttachDetail as $item) {
                    $fileInfo = new PBFileInfo();
                    $fileInfo->setFileId($item['file_id']);
                    $fileInfo->setFileName($item['file_name']??"");
                    $fileInfo->setFileSize($item['file_size']??"");
                    $fileInfo->setReferId($item['refer_id']??0);
                    $fileInfo->setHash($item['hash']??"");
                    $fileInfo->setExpiredTime($item['expire_time']? strtotime($item['expire_time']) : 0);
                    $fileInfo->setDownloadHost($item['download_host']??"");
                    $fileInfo->setFileUrl($item['url']??"");
                    $largeAttachDetailList[] = $fileInfo;
                    $pbSubordinateMailRsp->setLargeAttachList($largeAttachDetailList);
                }
            }
            //tag
            $sql = "select tag_id from tbl_mail_tag_assoc where mail_id={$mailId}";
            $tagList = $db->createCommand($sql)->queryAll(true);
            if(!empty($tagList)) {
                $pb = new PBMailTagInfo();
                $pb->setMailId($mailId);
                $pb->setTagList(array_column($tagList,'tag_id'));
            }


            //summary content
            $param[] = [
                'clientId' => $clientId,
                'mailId' => $mailId,
                'userMailId' => $baseInfo['user_mail_id'],
            ];
            $summaryList = \common\library\mail\MailContentHelper::getSummaryList($param);
            foreach ($summaryList as $summaryInfo) {
                $mailId = $summaryInfo['mailId'];
                $summary = $summaryInfo['summary'];
                $summaryObj = new PBMailSummary();
                $summaryObj->setMailId($mailId);
                $summaryObj->setSummary($summary);
                $pbSubordinateMailRsp->setSummary($summaryObj);
            }

            $contentInfo = \common\library\mail\MailContentHelper::getContentAndPlainText($clientId, $baseInfo['user_mail_id'], $mailId);

            $content = new PBMailContent();
            $content->setMailId($mailId);
            $content->setContent(snappy_compress($contentInfo['content'] ?? ''));
            $content->setPlainText(snappy_compress($contentInfo['plainText'] ?? ''));
            $content->setCompression(PBCompressionType::COMPRESSION_SNAPPY);

            $pbSubordinateMailRsp->setContent($content);

            //track
            $mailTrackDetailList = \MailTrackDetail::findByMailIds([$mailId]);
            foreach ($mailTrackDetailList as $mailTrackDetail){

                $track_detail_list = $pbSubordinateMailRsp->getTrackDetailList();
                foreach ($mailTrackDetail as $elem) {
                    $track_detail = new PBMailTrackDetail();
                    $track_detail->setMailId($elem['mail_id']);
                    $track_detail->setDetailId($elem['detail_id']);
                    if(!empty($elem['view_time'])) {
                        $track_detail->setViewTime(strtotime($elem['view_time']));
                    }
                    $track_detail->setViewCountry($elem['view_country']);
                    $track_detail->setViewCountryCode($elem['view_country_code'] ?? 0);
                    $track_detail->setViewProvince($elem['view_province']);
                    $track_detail->setViewCity($elem['view_city']);
                    $track_detail->setViewIp($elem['view_ip']);
                    $track_detail_list[] = $track_detail;
                }

                $pbSubordinateMailRsp->setTrackDetailList($track_detail_list);
            }

            //froward
            $mailFrowardList = \common\library\mail\Helper::forwardList([$mailId]);
            foreach ($mailFrowardList as $mail_id => $mailForwardDetail){

                $forwardDetailList = $pbSubordinateMailRsp->getForwardDetailList();

                foreach ($mailForwardDetail as $elem) {
                    $forwardDetail = new PBMailForwardDetail();
                    $forwardDetail->setMailId($elem['mail_id']);
                    $forwardDetail->setForwardTime(strtotime($elem['receive_time']));
                    $receiver = implode(';',array_filter([$elem['receiver'],$elem['bcc'],$elem['cc']]));
                    $forwardDetail->setReceiver($receiver);
                    $forwardDetail->setForwardMailId($elem['forward_mail_id']);
                    $forwardDetailList[] = $forwardDetail;
                }
                $pbSubordinateMailRsp->setForwardDetailList($forwardDetailList);
            }

            //extra status

            $approvalMailType = \common\library\approval_flow\Constants::ENTITY_TYPE_MAIL;
            $approvalSql = "select refer_id,apply_form_id,status from tbl_approval_flow_apply_form where client_id={$clientId} and refer_type={$approvalMailType} and  refer_id={$mailId} order by apply_form_id desc  limit 1";
            $approvalData = $db->createCommand($approvalSql)->queryRow(true);

            $trackSql = "select mail_id, reply_mail_id, view_count from tbl_mail_track where mail_id={$mailId}";
            $trackData =  $db->createCommand($trackSql)->queryRow(true);

            $mailExtraStatus = new PBMailExtraStatusInfo();
            $mailExtraStatus->setMailId($mailId);
            $mailExtraStatus->setApprovalId($approvalData['approval_id'] ?? 0);
            $mailExtraStatus->setApprovalStatus($approvalData['status'] ?? 0);
            $mailExtraStatus->setReplyMailId($trackData['reply_mail_id'] ?? 0);

            if(!empty($extMap[$mailId]['risk_reason'])) {
                $riskReasons = json_decode($extMap[$mailId]['risk_reason']);
                $mailExtraStatus->setRiskReasons($riskReasons);
            }

            $mailExtraStatus->setHasTrack(empty($trackData)?0:1);

            $pbSubordinateMailRsp->setExtraStatusInfo($mailExtraStatus);

            //tagDetail
            $mailTagListMap = \MailTagAssoc::getMailTagListByMailIds([$mailId]);

            foreach ($mailTagListMap as $mail_id => $mailTagDetail){

                $mailTagDetailList = $pbSubordinateMailRsp->getTagList();

                foreach ($mailTagDetail as $elem){
                    $mailTagDetail =  new PBMailTagDetail();
                    $mailTagDetail->setAssocId($elem['assoc_id']);
                    $mailTagDetail->setTagId($elem['tag_id']);
                    $mailTagDetail->setTagName($elem['tag_name']);
                    $mailTagDetail->setTagColor($elem['tag_color']);
                    $mailTagDetail->setSystemFlag($elem['system_flag']);
                    $mailTagDetailList[] = $mailTagDetail;
                }
                $pbSubordinateMailRsp->setTagList($mailTagDetailList);

            }

            //下属邮件扩展数据

            $extraData = (new MailExtraData($loginUserId))->buildExtraData([$mailId],[
                'can_unlock',
                'large_attach_list',
                'receive_read_receipt',
                'view_count',
                'is_owner',
                'owner',
                'sign_id',
                'inquiry_type',
                'contact_summary',
                'mail_card_map',
                'alibaba_info',
                'mail_todo',
                'translate',
                'is_manageable'
            ]);

            //fix buildExtraData isManageable数据问题
            $extraDataItem = $extraData[$mailId] ?? null;
            if ($extraDataItem !== null) {
                $mail = new \common\library\mail\Mail($mailId);
                $isManageable = $mail->getAccessService()->isOwner() || $mail->getAccessService()->isDepartmentAdmin() || $mail->getAccessService()->isCooperation() || $mail->getAccessService()->isApproval() || $mail->getAccessService()->isVisible();
                $extraDataItem->setIsManageable($isManageable);
                $pbSubordinateMailRsp->setExtraData($extraDataItem);
            }

        }

        return $this->successPb($pbSubordinateMailRsp);

    }

    public function actionMailConversationList()
    {
        $user = User::getLoginUser();
        $userId = $user->getUserId();
        $clientId = $user->getClientId();

        $req = new \protobuf\MailSync\PBBaseMailListReq();
        $this->buildRequest($req);
        $userMailId = $req->getUserMailId();
        $folderId = iterator_to_array($req->getFolderIds());
        $excludeFolderIds = iterator_to_array($req->getExcludeFolderIds());
        $tagIds = iterator_to_array($req->getTagIds());
        $tagAllFlag = $req->getTagAllFlag();
        $pinFlag = $req->getPinFlag();
        $todoCompletedFlag = $req->getTodoCompletedFlag();
        $relateCompanyFlag = $req->getRelateCompanyFlag();
        $sender = $req->getSender();
        $receiver = $req->getReceiver();
        $readFlag = $req->getReadFlag();
        $urgentFlag = $req->getUrgentFlag();
        $attachFlag = $req->getAttachFlag();
        $startDate = $req->getStartDate();
        $endDate = $req->getEndDate();
        $trackType = $req->getTrackType();
        $anchorInfo = $req->getAnchorInfo();

        $anchor = $anchorInfo->getAnchor();
        $size = $anchorInfo->getSize() ?: 20;
        $anchorConversationId = $anchorInfo->getConversationId();
        $direction = $anchorInfo->getDirection();

        if ($this->isMobile() && is_array($folderId) && in_array(\Mail::FOLDER_SEND_ID, $folderId)) {
             array_push($folderId, \Mail::FOLDER_EXPOSE_ID);
        }

        $mailList = new \common\library\mail\MailConversationList();
        $mailList->getListStrategy('common', [
            'show_has_more' => true,
            'user_mail_id' => $userMailId,
            'folder_id' => $folderId,
            'exclude_folder_ids' => $excludeFolderIds,
            'tag_ids' => $tagIds,
            'tag_all_flag' => $tagAllFlag,
            'anchor' => $anchor,
            'size' => $size,
            'list_type' => \common\library\mail\MailList::LIST_TYPE_CONVERSATION_LIST,
            'read_flag' => $readFlag,
            'pin_flag' => $pinFlag,
            'urgent_flag' => $urgentFlag,
            'attach_flag' => $attachFlag,
            'todo_flag' => $todoCompletedFlag,
            'relate_company_flag' => $relateCompanyFlag,
            'track_type' => $trackType,
            'receiver' => $receiver,
            'sender' => $sender,
            'start_date' => $startDate,
            'end_date' => $endDate,
            'anchor_conversation_id' => $anchorConversationId,
            'direction' => $direction
        ]);

        $mailList->setDirection($direction);
        $mailList->getFormatter()->appConversationMailListSetting();
        $mailList->getFormatter()->setShowConversationInfo(true);
        $mailList->setListType(\common\library\mail\MailList::LIST_TYPE_CONVERSATION_LIST);
        //判断是往上还是往下
        if ($direction >0) {
            $mailList->setOrder('asc');
        } else {
            $mailList->setOrder('desc');
        }

        $mailList->setOrderBy(['receive_time','conversation_id']);//邮件的接受时间
        $mailList->getFormatter()->setShowTodo(true);
        $list = $mailList->find();

        $pbBaseMailListRsp = new \protobuf\MailSync\PBBaseMailListRsp();

        if (empty($list)) {
            return $this->successPb($pbBaseMailListRsp);
        }

        foreach($list as $index => $mail) {

            if ($index >= $size) {
                break;
            }

            $conversationId = $mail['conversation_id'] ?? 0;
            $conversationInfo = $mail['conversation_info'] ?? '';

            $mailInfo = new  \protobuf\MailSync\PBBaseMailInfo();
            $mailInfo->setConversationId($conversationId);

            $info = $this->setMailInfo($mail);
            $mailInfo->setMailInfo($info);

            if (!empty($conversationInfo)) {

                $pbConversationInfoItem = new \protobuf\MailSync\PBConversationInfoItem();
                $pbConversationInfoItem->setMailCount($conversationInfo['mail_count']??0);
                $pbConversationInfoItem->setLastMailReceiveTime(strtotime($mail['receive_time']??''));

                //会话中有一封子邮件未读即为未读
                $pbConversationInfoItem->setReadFlag($conversationInfo['read_flag'] ?? 1);
                //话中有一封子邮件已钉即为已钉
                $pbConversationInfoItem->setPinFlag($conversationInfo['pin_flag'] ?? 0);
                //所有子邮件中的所有附件
                $pbConversationInfoItem->setAttachCount($conversationInfo['attach_count'] ?? 0);
                //最近一封附件
                if (!empty($conversationInfo['last_attach'])) {
                    $fileInfo = $conversationInfo['last_attach'];
                    $pbAttachmentItem = new PBFileInfo();
                    $pbAttachmentItem->setUniqueKey($fileInfo['md5_file'].'_'.$fileInfo['file_size']);
                    $pbAttachmentItem->setFileId($fileInfo['file_id']);
                    $pbAttachmentItem->setFileName($fileInfo['file_name']);
                    $pbAttachmentItem->setFileUrl($fileInfo['file_url']);
                    $pbAttachmentItem->setFileSize($fileInfo['file_size']);
                    $pbAttachmentItem->setExpiredTime($fileInfo['expired_time']);
                    $pbConversationInfoItem->setLastAttachInfo($pbAttachmentItem);
                }
                //最近标签

                $tagList = [];
                $hasSystemFlag = false;
                //系统标签
                if (!empty($conversationInfo['tags']['system_flag'])) {

                    $systemTag = end($conversationInfo['tags']['system_flag']);
                    //拿最后一个systemTag
                    $mailTagDetail = new PBMailTagDetail();
                    $mailTagDetail->setAssocId($systemTag['assoc_id']??0);
                    $mailTagDetail->setTagId($systemTag['tag_id']??0);
                    $mailTagDetail->setTagColor($systemTag['tag_color']??0);
                    $mailTagDetail->setTagName($systemTag['tag_name']??'');
                    $mailTagDetail->setSystemFlag($systemTag['system_flag']??0);

                    $hasSystemFlag = true;
                    $tagList[] = $mailTagDetail;
                }
                //邮件的子邮件中前四个 tag 一个系统标签，其他为自定义标签
                if (!empty($conversationInfo['tags']['custom_tag'])) {

                    $customTags = array_slice($conversationInfo['tags']['custom_tag'], $hasSystemFlag == true ? -3 : -4, $hasSystemFlag == true ? 3 : 4);
                    foreach ($customTags as $customTag) {
                        //拿最后一个systemTag
                        $mailTagDetail = new PBMailTagDetail();
                        $mailTagDetail->setAssocId($customTag['assoc_id'] ?? 0);
                        $mailTagDetail->setTagId($customTag['tag_id'] ?? 0);
                        $mailTagDetail->setTagColor($customTag['tag_color'] ?? 0);
                        $mailTagDetail->setTagName($customTag['tag_name'] ?? '');
                        $mailTagDetail->setSystemFlag($customTag['system_flag'] ?? 0);
                        $tagList[] = $mailTagDetail;
                    }
                }

                //自定义标签
                if (!empty($tagList)) {
                    $pbConversationInfoItem->setTagList($tagList);
                }

                //子邮件id
                if (!empty($conversationInfo['sub_mail_ids'])) {
                    $pbConversationInfoItem->setMailIds($conversationInfo['sub_mail_ids']);
                }

                $mailInfo->setMailConversationItem($pbConversationInfoItem);
            }

            $result[] = $mailInfo;
        }

        if (!empty($result)) {
            $pbBaseMailListRsp->setMailInfo($result);
        }

        if ( count($list) > $size) {
            $pbBaseMailListRsp->setHasMore(true);
        }

        return $this->successPb($pbBaseMailListRsp);
    }

    /**
     * 游标非聚合邮件列表
     */
    public function actionNewMailList()
    {
        $user = User::getLoginUser();
        $userId = $user->getUserId();
        $clientId = $user->getClientId();

        $req = new \protobuf\MailSync\PBBaseMailListReq();
        $this->buildRequest($req);
        $userMailId = $req->getUserMailId();
        $folderId = iterator_to_array($req->getFolderIds());
        $excludeFolderIds = iterator_to_array($req->getExcludeFolderIds());
        $tagIds = iterator_to_array($req->getTagIds());
        $tagAllFlag = $req->getTagAllFlag();
        $pinFlag = $req->getPinFlag();
        $todoCompletedFlag = $req->getTodoCompletedFlag();
        $relateCompanyFlag = $req->getRelateCompanyFlag();
        $sender = $req->getSender();
        $receiver = $req->getReceiver();
        $readFlag = $req->getReadFlag();
        $urgentFlag = $req->getUrgentFlag();
        $attachFlag = $req->getAttachFlag();
        $startDate = $req->getStartDate();
        $endDate = $req->getEndDate();
        $trackType = $req->getTrackType();
        $anchorInfo = $req->getAnchorInfo();

        $anchor = $anchorInfo->getAnchor();
        $size = $anchorInfo->getSize() ?: 20;
        $anchorMailId = $anchorInfo->getMailId();
        $direction = $anchorInfo->getDirection();

        if ($this->isMobile() && is_array($folderId) && in_array(\Mail::FOLDER_SEND_ID, $folderId)) {
            array_push($folderId, \Mail::FOLDER_EXPOSE_ID);
        }

        $mailList = new \common\library\mail\MailList();
        $mailList->setOnlyUseSearchResult(false);
        $mailList->getListStrategy('common', [
            'show_has_more' => true,
            'user_mail_id' => $userMailId,
            'folder_id' => $folderId,
            'exclude_folder_ids' => $excludeFolderIds,
            'tag_ids' => $tagIds,
            'tag_all_flag' => $tagAllFlag,
            'anchor' => $anchor,
            'size' => $size,
            'list_type' => \common\library\mail\MailList::LIST_TYPE_MAIL_LIST,
            'read_flag' => $readFlag,
            'pin_flag' => $pinFlag,
            'urgent_flag' => $urgentFlag,
            'attach_flag' => $attachFlag,
            'todo_flag' => $todoCompletedFlag,
            'relate_company_flag' => $relateCompanyFlag,
            'track_type' => $trackType,
            'receiver' => $receiver,
            'sender' => $sender,
            'start_date' => $startDate,
            'end_date' => $endDate,
            'anchor_mail_id' => $anchorMailId,
            'direction' => $direction
        ]);

        //判断是往上还是往下
        if ($direction >0) {
            $mailList->setOrder('asc');
        } else {
            $mailList->setOrder('desc');
        }

        if ($anchorMailId!=0) {
            $mailList->setAnchorMailId($anchorMailId);
            $mailList->setOrderBy(['receive_time','mail_id']);//邮件的接受时间
        }
        $mailList->setDirection($direction);

        $mailList->getFormatter()->appConversationMailListSetting();
        $mailList->getFormatter()->setShowTodo(true);
        $list = $mailList->find();
        $pbBaseMailListRsp = new \protobuf\MailSync\PBBaseMailListRsp();
        if (empty($list)) {
            return $this->successPb($pbBaseMailListRsp);
        }

        //往上拉兼容邮件列表的顺序
        if ($direction > 0) {
            $list = array_reverse($list);
            //如果超出size 去掉多查询的一条记录
            if (count($list) > $size) {
                $list = array_slice($list, 1, $size);
            }
        }

        foreach($list as $index => $mail) {

            if ($index >= $size) {
                break;
            }

            $conversationId = $mail['conversation_id'] ?? 0;

            $mailInfo = new  \protobuf\MailSync\PBBaseMailInfo();
            $mailInfo->setConversationId($conversationId);

            $info = $this->setMailInfo($mail);
            $mailInfo->setMailInfo($info);

            $result[] = $mailInfo;
        }

        if (!empty($result)) {
            $pbBaseMailListRsp->setMailInfo($result);
        }

        if ( count($list) > $size) {
            $pbBaseMailListRsp->setHasMore(true);
        }

        return $this->successPb($pbBaseMailListRsp);
    }


    /**
     * 待办接口列表 不支持高级筛选
     */
    public function actionMailTodoList()
    {
        $user = User::getLoginUser();
        $userId = $user->getUserId();
        $clientId = $user->getClientId();

        $req = new \protobuf\MailSync\PBBaseMailListReq();
        $this->buildRequest($req);
        //0:待处理（全部） 1:待处理超时 2：待处理未超时 -1:全部
        $todoCompletedFlag = $req->getTodoCompletedFlag();

        //锚点
        $anchorInfo = $req->getAnchorInfo();
        $anchor = $anchorInfo->getAnchor();
        $size = $anchorInfo->getSize();
        //去重，存在同个处理时间有多个邮件的情况
        $anchorMailId = $anchorInfo->getMailId();
        //0 ：降序desc排序 1: 升序asc排序
        $direction = $anchorInfo->getDirection();

        $mailList = new \common\library\mail\TodoMailList();
        // 默认不搜索永久删除和隐藏的邮件
        $mailList->setNotInFolderIds([
            \Mail::FOLDER_DRAFT_ID,
            \Mail::FOLDER_DELETE_ID,
            \Mail::FOLDER_HIDDEN_DRAFT_ID,
        ]);
        $mailList->setTodoMailAnchor($anchor);
        $mailList->setMailTodoFlagAndOverTime($todoCompletedFlag,time());
        if ($anchorMailId!=0) {
            $mailList->setTodoMailAnchorMailId($anchorMailId);
        }
        $mailList->setTodoMailAnchorDirection($direction);
        // +1是为了判断has more
        $mailList->setLimit($size + 1);

        $mailList->getFormatter()->appConversationMailListSetting();
        $mailList->getFormatter()->setShowTodo(true);
        $list = $mailList->find();
        $pbBaseMailListRsp = new \protobuf\MailSync\PBBaseMailListRsp();
        if (empty($list)) {
            return $this->successPb($pbBaseMailListRsp);
        }

        foreach($list as $index => $mail) {

            if ($index >= $size) {
                break;
            }

            $conversationId = $mail['conversation_id'] ?? 0;

            $mailInfo = new  \protobuf\MailSync\PBBaseMailInfo();
            $mailInfo->setConversationId($conversationId);

            $info = $this->setMailInfo($mail);
            $mailInfo->setMailInfo($info);

            $result[] = $mailInfo;
        }

        if (!empty($result)) {
            $pbBaseMailListRsp->setMailInfo($result);
        }

        if ( count($list) > $size) {
            $pbBaseMailListRsp->setHasMore(true);
        }

        return $this->successPb($pbBaseMailListRsp);
    }

    public function actionConversationList()
    {
        $user = User::getLoginUser();
        $userId = $user->getUserId();
        $clientId = $user->getClientId();


        $req = new \protobuf\MailSync\PBBaseMailListReq();
        $this->buildRequest($req);

        $userMailId = $req->getUserMailId();
        $folderId = iterator_to_array($req->getFolderIds());
        $pinFlag = $req->getPinFlag();
        $todoCompletedFlag = $req->getTodoCompletedFlag();
        $relateCompanyFlag = $req->getRelateCompanyFlag();
        $urgentFlag = $req->getUrgentFlag();
        $attachFlag = $req->getAttachFlag();
        $anchorInfo = $req->getAnchorInfo();
        $readFlag = $req->getReadFlag();
        $tagIds = iterator_to_array($req->getTagIds());
        $replyFlag = $req->getTrackType();

        $anchor = $anchorInfo->getAnchor();
        $size = $anchorInfo->getSize() ?: 20;
        $anchorConversationId = $anchorInfo->getConversationId();
        $direction = $anchorInfo->getDirection();

        if ($this->isMobile() && is_array($folderId) && in_array(\Mail::FOLDER_SEND_ID, $folderId)) {
            array_push($folderId, \Mail::FOLDER_EXPOSE_ID);
        }

        $type = 'receive';
        //判断会话的查询type 收件箱 未读 自定义文件夹，标签
        if(in_array(\Mail::FOLDER_INBOX_ID,$folderId)) {
            $type = 'receive';
        }

        if(empty($folderId) && $readFlag==0) {
            $type = 'unread';
        }

        if(!empty($folderId) && !in_array(\Mail::FOLDER_INBOX_ID,$folderId)) {
            $type = 'folder';
        }

        if (!empty($tagIds)) {
            $type = 'tag';
        }

        //会话列表
        $conversationList = new \common\library\mail_conversation\ConversationList($clientId,$userId);
        $conversationList->setNeedCompareFromMail(false);
        $conversationList->getListStrategy($type, [
            "tag_id" => $tagIds,
            "pin"=> $pinFlag,
            "folder_id"=> $folderId,
            "todo_completed_flag"=> $todoCompletedFlag,
            "read_flag"=> $readFlag,
        ]);

        //增加可见范围
        if (!empty($userMailId)) {
            $conversationList->setUserMailIds($userMailId);
        }

        if ($pinFlag != -1) {
            $conversationList->setPinFlag($pinFlag);
        }

        if ($relateCompanyFlag != -1) {
            $conversationList->setContactTypes($relateCompanyFlag);
        }

        //兼容 0:待处理（全部） 1:待处理未超时 2：待处理超时 -1:全部
        if ($todoCompletedFlag != -1 && $todoCompletedFlag==0) {
            $conversationList->setTodoFlag(1);
        }

        if ($urgentFlag != -1) {
            $conversationList->setUrgentFlag($urgentFlag);
        }

        if ($attachFlag != -1) {
            $conversationList->setAttachFlag($attachFlag);
        }

        if ($replyFlag != -1) {
            $conversationList->setReplyFlag($replyFlag);
        }

        if ($readFlag!= -1) {
            $conversationList->setReadFlag($readFlag);
        }

        //锚点 has_more
        $conversationList->setAnchorInfo($anchor, $anchorConversationId,$direction);
        $conversationList->setLimit($size + 1);

        //判断是往上还是往下
        if ($direction >0) {
            $conversationList->setOrder('asc');
        } else {
            $conversationList->setOrder('desc');
        }
        //按照last_mail_receive_time以及mail_conversation_id排序
        if ($anchorConversationId!=0) {
            $conversationList->setConversationOrderBy(['last_mail_receive_time','mail_conversation_id']);
        }
        $conversationList->getFormatter()->appConversationMailListSetting();
        $list  = $conversationList->find();

        $pbBaseMailListRsp = new \protobuf\MailSync\PBBaseMailListRsp();

        if (empty($list)) {
            return $this->successPb($pbBaseMailListRsp);
        }

        //往上拉兼容邮件列表的顺序
        if ($direction > 0) {
            $list = array_reverse($list);
            //如果超出size 去掉多查询的一条记录
            if (count($list) > $size) {
                $list = array_slice($list, 1, $size);
            }
        }


        foreach($list as $index => $mail) {

            if ($index >= $size) {
                break;
            }

            $conversationId = $mail['conversation_id'] ?? 0;
            $conversationInfo = $mail['conversation_info'] ?? [];

            if (empty($conversationId)) {
                continue;
            }

            if (empty($conversationInfo)) {
                continue;
            }

            $mailInfo = new  \protobuf\MailSync\PBBaseMailInfo();
            $mailInfo->setConversationId($conversationId);
            $info = $this->setMailInfo($mail);
            $mailInfo->setMailInfo($info);

            if (!empty($conversationInfo)) {

                $pbConversationInfoItem = new \protobuf\MailSync\PBConversationInfoItem();
                $pbConversationInfoItem->setMailCount($conversationInfo['mail_count']??0);
                $pbConversationInfoItem->setReadFlag($conversationInfo['read_flag'] ?? 1);
                $pbConversationInfoItem->setPinFlag($conversationInfo['is_pin'] ?? 0);

                $lastMailReceiveTime = strtotime($conversationInfo['last_mail_receive_time']??'');
                if (empty($lastMailReceiveTime)) {
                    $lastMailReceiveTime = 0;
                }

                $pbConversationInfoItem->setLastMailReceiveTime($lastMailReceiveTime);
                $pbConversationInfoItem->setTodoFlag($conversationInfo['todo_flag'] ?? 0);
                $pbConversationInfoItem->setReplyFlag($conversationInfo['reply_flag'] ?? 0);
                $pbConversationInfoItem->setUrgentFlag($conversationInfo['urgent_flag'] ?? 0);
                //标签列表
                $tagList = [];
                if (!empty($conversationInfo['tag_list'])) {

                    foreach ($conversationInfo['tag_list'] as $tagItem) {
                        $mailTagDetail = new PBMailTagDetail();
                        $mailTagDetail->setTagId($tagItem['tag_id']??0);
                        $mailTagDetail->setTagColor($tagItem['tag_color']??0);
                        $mailTagDetail->setTagName($tagItem['tag_name']??'');
                        $mailTagDetail->setSystemFlag($tagItem['system_flag']??0);
                        $tagList[] = $mailTagDetail;
                    }
                }

                if (!empty($tagList)) {
                    $pbConversationInfoItem->setTagList($tagList);
                }

                //联系人
                $contactSummary = [];
                if (!empty($conversationInfo['contact_summary'])) {
                    foreach ($conversationInfo['contact_summary'] as $contactSummaryItem) {
                        $pbContactSummary = new PBMailContactSummary();
                        $pbContactSummary->setEmail($contactSummaryItem['email'] ?? '');
                        $pbContactSummary->setName($contactSummaryItem['name'] ?? '');
                        $pbContactSummary->setContactType($contactSummaryItem['card_type'] ?? 0);
                        $pbContactSummary->setId($contactSummaryItem['id'] ?? 0);
                        $contactSummary[] = $pbContactSummary;
                    }
                }

                if (!empty($contactSummary)) {
                    $pbConversationInfoItem->setContactSummary($contactSummary);
                }

                //最新附件
                if (!empty($conversationInfo['attach_info'])) {
                    $fileInfos = $conversationInfo['attach_info'];

                    $attachInfoArr = [];
                    foreach ($fileInfos as $fileInfo) {
                        $pbAttachmentItem = new PBFileInfo();
                        $pbAttachmentItem->setUniqueKey($fileInfo['md5_file'] . '_' . $fileInfo['file_size']);
                        $pbAttachmentItem->setFileId($fileInfo['file_id']);
                        $pbAttachmentItem->setFileName($fileInfo['file_name']);
                        $pbAttachmentItem->setFileUrl($fileInfo['file_url']);
                        $pbAttachmentItem->setFileSize($fileInfo['file_size']);
                        $pbAttachmentItem->setExpiredTime($fileInfo['expired_time']);
                        $attachInfoArr[] = $pbAttachmentItem;
                    }

                    $pbConversationInfoItem->setAttachInfo($attachInfoArr);
                }

                $mailInfo->setMailConversationItem($pbConversationInfoItem);
            }

            $result[] = $mailInfo;
        }

        if (!empty($result)) {
            $pbBaseMailListRsp->setMailInfo($result);
        }

        if ( count($list) > $size) {
            $pbBaseMailListRsp->setHasMore(true);
        }

        return $this->successPb($pbBaseMailListRsp);

    }


    /**
     * 下属邮件列表
     * 每个下属显示的最新的10个邮件？ 收件还是发件
     * 分页是按照用户分页
     */
    public  function actionSubordinateMailList()
    {
        $user = User::getLoginUser();
        $clientId = $user->getClientId();

        $userMailIds = [];
        $request = new PBMailSearchListReq();
        $this->buildRequest($request);
        $page_size = $request->getLimit()?:20;
        $page = $request->getOffset()?:1;

        $departmentPermission = new \common\library\department\DepartmentPermission($user->getClientId(), $user->getUserId());
        $departmentPermission->permission(PrivilegeConstants::PRIVILEGE_EMAIL_VIEW);
        $result = $departmentPermission->userList();

        $result = array_filter($result, function($item) use ($user) {
            return $item['user_id'] != $user->getUserId();
        });
        $userNameMap = array_column($result, 'nickname', 'user_id');
        $users = array_column($result, 'user_id');

        if (empty($users)) {
            return $this->successPb(new PBMailSearchListRsp());
        }

        //按照用户分页
        //先给users排序
        sort($users, SORT_ASC);
        $userCount = count($users);
        //给users分页
        $users = array_slice($users, ($page - 1) * $page_size, $page_size);

        if (empty($users)) {
            return $this->successPb(new PBMailSearchListRsp());
        }

        $mailList = new \common\library\mail\MailList();
        $mailList->setLimit(10 * count($users));
        $mailList->setOffset(0);
        $mailList->setUserId($users);
        $mailList->setDeleteFlag(false);

        //未读邮件 只搜索收件箱 和 自定义文件
        //获取用户自定义文件夹
        $folderListObj = new \common\library\mail\setting\folder\MailFolderList($user->getUserId());
        $folderListObj->setFields('folder_id');
        $folderList   = $folderListObj->find();
        $folderIdList = array_column($folderList, 'folder_id');
        array_push($folderIdList, \Mail::FOLDER_INBOX_ID, \Mail::FOLDER_SEND_ID);
        $mailList->setFolderIds($folderIdList);
        $mailList->getFormatter()->appUserGroupSetting();
        $mailList->setOrderBy(['user_id','receive_time']);
        $mailList->setOrder('desc');
        $list = $mailList->groupByUserMail(10);

        //按照user来分组
        $userGroupMails = [];
        foreach ($list as $mail) {
            $userName = $userNameMap[$mail['user_id']] ?? '';
            $userGroupMails[$mail['user_id']]['user_id'] = $mail['user_id'];
            $userGroupMails[$mail['user_id']]['user_name'] = $userName;
            $userGroupMails[$mail['user_id']]['mails'][] = $mail;
        }

        //安装userId升序排序
        ksort($userGroupMails);

        $rsp = new PBMailSearchListRsp();
        $rsp->setCount($userCount);
        $userMailInfo = [];

        foreach ($userGroupMails as $userGroupMail) {

            $groupInfo = new \protobuf\MailSync\PBMailUserGroupInfo();
            $groupInfo->setUserId($userGroupMail['user_id']);
            $groupInfo->setUserName($userGroupMail['user_name']);

            $mailInfos = [];
            foreach ($userGroupMail['mails'] as $mail) {
                $mainInfo = $this->setMailInfo($mail);
                $mailInfos[] = $mainInfo;
            }
            $groupInfo->setMailInfo($mailInfos);
            if ($groupInfo) {
                $userMailInfo[] = $groupInfo;
            }
        }
        $rsp->setUserMailInfo($userMailInfo);
        return $this->successPb($rsp);
    }
}
