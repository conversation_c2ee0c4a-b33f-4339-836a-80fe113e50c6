<?php
/**
 * Created by PhpStorm.
 * User: ganyaoyao
 * Date: 2019/12/19
 * Time: 09:42
 */

use common\library\tips\TipsConstants;

class TipsReadController extends StormsFuryController
{

    public function actionCompanyInfo()
    {
        $request = new \protobuf\Tips\PBTipsCompanyInfoReq();
        $this->buildRequest($request);
        $scene = $request->getScene();
        $id = $request->getId();
        $user = User::getLoginUser();
        $tipInfo = \common\library\tips\scene\TipsFactory::createTips($scene, $user->getUserId(), $id);

        $rsp = new \protobuf\Tips\PBTipsCompanyInfoRsp();
        if ($tipInfo->getCompanyInfo()) {
            $tips = $tipInfo->getCompanyInfo();
            $companyInfo = new \protobuf\Tips\PBTipsCompanyInfo();
            $companyInfo->setName($tips['name'] ?? "");
            $companyInfo->setCompanyHashId($tips['company_hash_id'] ?? "");
            $companyInfo->setCountry($tips['country'] ?? "");
            $companyInfo->setAddress($tips['address'] ?? "");
            $companyInfo->setSicName($tips['sicName'] ?? "");
            $companyInfo->setSales($tips['sales'] ?? "");
            $companyInfo->setFoundedYear($tips['foundedYear'] ?? 0);
            $companyInfo->setEmployees($tips['employees'] ?? "");

            $rsp->setCompanyInfo($companyInfo);
        }
        $this->successPb($rsp);
    }

    public function actionExchangeBizEntityId()
    {
        $request = new \protobuf\Tips\PBTipsExchangeBizEntityReq();
        $this->buildRequest($request);
        $scene = $request->getScene();
        $id = $request->getId();
        $user = User::getLoginUser();
        $tipInfo = \common\library\tips\scene\TipsFactory::createTips($scene, $user->getUserId(), $id);
        $rsp = new \protobuf\Tips\PBTipsExchangeBizEntityRsp();
        if ($tipInfo->exchangeBizEntityInfo()) {
            $tips = $tipInfo->exchangeBizEntityInfo();
            $rsp->setBizType($tips['biz_type'] ? intval($tips['biz_type']) : 0);
            $rsp->setDomain($tips['domain'] ?? "");
            $rsp->setCompanyHashId($tips['company_hash_id'] ?? "");
            $rsp->setBizEntityId($tips['biz_entity_id'] ?? "");
        }
        $this->successPb($rsp);
    }

    public function getUserMailIds($list){
        $userMailIds = [];
        $userMailIdMaps = [];
        foreach ($list as $feedItem){
            if($feedItem['user_id']
                && in_array($feedItem['type'],array(
                    TipsConstants::FEED_TYPE_MAIL_REMIND_REPLY,
                    TipsConstants::FEED_TYPE_MAIL_NOT_RESPOND,
                    TipsConstants::FEED_TYPE_MAIL_KEY_VIEWED,
                    TipsConstants::FEED_TYPE_MAIL_SAVE_CUSTOMERS))
                && isset($feedItem['refer_id'])){

                $userMailIds[$feedItem['user_id']][] = $feedItem['refer_id'];

            }
        }
        if(!$userMailIds) {
            return $userMailIdMaps;
        }
        foreach($userMailIds as $userId => $mailIds){
            $db = ProjectActiveRecord::getDbByUserId($userId);
            $mailIdsStr = join(',',$mailIds);
            $sql  = "select mail_id,user_mail_id from tbl_mail where user_id = {$userId} and mail_id in($mailIdsStr)";
            $mailList = $db->createCommand($sql)->queryAll();
            if($mailList){
               foreach ($mailList as $item){
                   $userMailIdMaps[$item['mail_id']] = $item['user_mail_id'];
               }
            }
        }
        return $userMailIdMaps;
    }

    //获取tips 列表数据
    public function actionTipsList(){

        $request = new \protobuf\Tips\PBTipsListReq();
        $this->buildRequest($request);
        $user = User::getLoginUser();
        $scene = $request->getScene();
        $id = $request->getId();
        $pageSize = $request->getPageSize()??10;
        $pageNo = $request->getPageNo()??1;

        $rsp = new \protobuf\Tips\PBTipsListRsp();
        $dispatcher = new \common\library\tips\Dispatcher($scene);
        $dispatcher->setId($id);
        $dispatcher->setPageSize($pageSize);
        $dispatcher->setPageNo($pageNo);
        $dispatcher->setServer('desktop');
        $list = $dispatcher->run();

        $feedList = $rsp->getFeeds();
        $list['feeds'] = $list['feeds'] ??[];

        $rsp->setLastViewFeedId($list['last_view_feed_id']??0);//最后一次返回Id
        $rsp->setSort($list['sort']??null);//排序内容

        if(!$list['feeds']){
            $this->successPb($rsp);
            return;
        }

        $userMailIdMaps = $this->getUserMailIds($list['feeds']);

//        echo "<pre>";
//        var_dump($list['feeds']);
//        exit();

        foreach ($list['feeds'] as $feedItem){

           $tipsInfo = new \protobuf\Tips\PBTipsInfo();
           $tipsInfo->setFeedId($feedItem['feed_id']);
           $tipsInfo->setMsgId($feedItem['msg_id']);
           $tipsInfo->setUserId($feedItem['user_id']);
           $tipsInfo->setBizEntityId($feedItem['biz_entity_id']);
           $tipsInfo->setBizType($feedItem['biz_type']);
           $tipsInfo->setReferId($feedItem['refer_id']);
           $tipsInfo->setReferType($feedItem['refer_type']);
           $tipsInfo->setType($feedItem['type']);
           $tipsInfo->setOrigin($feedItem['origin']);
           $tipsInfo->setOriginCreateTime($feedItem['origin_create_time']??"");
           $tipsInfo->setCreateTime($feedItem['create_time']??"");
           $tipsInfo->setUpdateTime($feedItem['update_time']??"");
           $tipsInfo->setReadFlag($feedItem['read_flag']?true:false);
           if(isset($feedItem['feed_type_id']) && $feedItem['feed_type_id']){
               $tipsInfo->setFeedTypeId($feedItem['feed_type_id']);
           }
           if(!$feedItem['data']){
                continue;
           }

           $feedItemData = $feedItem['data'];

           switch ($feedItem['type']){
               // type 1
               case \common\library\tips\TipsConstants::FEED_TYPE_OPPORTUNITY_FORGET:
                   $tipsOpprotunity =  new \protobuf\Tips\PBTipsOpprotunityInfo();
                   $tipsOpprotunity->setOpportunityId($feedItemData['opportunity_id']??"");
                   $tipsOpprotunity->setName($feedItemData['name']??"");
                   $tipsOpprotunity->setAccountDate($feedItemData['account_date']??"");
                   $tipsInfo->setOpprotunityInfo($tipsOpprotunity);
                   break;
               // type 2
               case \common\library\tips\TipsConstants::FEED_TYPE_MAIL_REMIND_REPLY:

                   $tipsMail =  new \protobuf\Tips\PBTipsMailInfo();
                   $tipsMail->setEmial($feedItemData['email']);
                   $tipsMail->setMailId($feedItemData['mail_id']);
                   $tipsMail->setSubject($feedItemData['subject']??"");
                   $tipsMail->setCompanyId($feedItemData['company_id']??0);
                   $tipsMail->setName($feedItemData['name']??"");
                   $tipsMail->setMailReceiveTime($feedItemData['mail_receive_time']??"");
                   $tipsMail->setFeedTypeId($feedItemData['feed_type_id']??"");
                   $tipsMail->setType($feedItemData['type']??"");
                   $userMailId = 0;
                   if(isset($feedItem['refer_id']) && $feedItem['refer_id']){
                       $userMailId = $userMailIdMaps[$feedItem['refer_id']]??0;
                   }
                   $tipsMail->setUserMailId($userMailId);
                   $tipsInfo->setMailInfo($tipsMail);
                   break;
               // type 3
               case \common\library\tips\TipsConstants::FEED_TYPE_MAIL_NOT_RESPOND:

                   $tipsMail =  new \protobuf\Tips\PBTipsMailInfo();
                   $tipsMail->setEmial($feedItemData['email']??"");
                   $tipsMail->setMailId(isset($feedItemData['mail_id'])?intval($feedItemData['mail_id']):0);
                   $tipsMail->setSubject($feedItemData['subject']??"");
                   $tipsMail->setMailTag(isset($feedItemData['mail_tag']) && $feedItemData['mail_tag'] ? intval($feedItemData['mail_tag']) : 0);
                   $tipsMail->setCompanyId(isset($feedItemData['company_id'])?intval($feedItemData['company_id']):0);
                   $tipsMail->setName($feedItemData['name']??"");
                   $tipsMail->setMailReceiveTime($feedItemData['mail_receive_time']??"");
                   $tipsMail->setFeedTypeId($feedItemData['feed_type_id']??"");
                   $tipsMail->setType($feedItemData['type']??"");
                   $userMailId = 0;
                   if(isset($feedItem['refer_id']) && $feedItem['refer_id']){
                       $userMailId = $userMailIdMaps[$feedItem['refer_id']]??0;
                   }
                   $tipsMail->setUserMailId($userMailId);
                   $tipsInfo->setMailInfo($tipsMail);
                   break;
               // type 4
               case \common\library\tips\TipsConstants::FEED_TYPE_MAIL_KEY_VIEWED:
                   $tipsMail =  new \protobuf\Tips\PBTipsMailInfo();
                   $tipsMail->setEmial($feedItemData['email']??"");
                   $tipsMail->setMailId(isset($feedItemData['mail_id'])?intval($feedItemData['mail_id']):0);
                   $tipsMail->setSubject($feedItemData['subject']??"");
                   $tipsMail->setMailTag(isset($feedItemData['mail_tag']) && $feedItemData['mail_tag'] ? intval($feedItemData['mail_tag']) : 0);
                   $tipsMail->setCompanyId(isset($feedItemData['company_id'])?intval($feedItemData['company_id']):0);
                   $tipsMail->setName($feedItemData['name']??"");
                   $tipsMail->setMailReceiveTime($feedItemData['mail_receive_time']??"");
                   $tipsMail->setFeedTypeId($feedItemData['feed_type_id']??"");
                   $tipsMail->setType($feedItemData['type']??"");
                   $userMailId = 0;
                   if(isset($feedItem['refer_id']) && $feedItem['refer_id']){
                       $userMailId = $userMailIdMaps[$feedItem['refer_id']]??0;
                   }
                   $tipsMail->setUserMailId($userMailId);
                   $tipsInfo->setMailInfo($tipsMail);
                   break;
               // type 5
               case \common\library\tips\TipsConstants::FEED_TYPE_CUSTOMERS_UPDATED:
                   $tipsCustomer = new \protobuf\Tips\PBTipsCustomerInfo();
                   $tipsCustomer->setCompanyId($feedItemData['company_id']??0);
                   $tipsCustomer->setName($feedItemData['name']??"");
                   $tipsCustomer->setFields(isset($feedItemData['fields']) && $feedItemData['fields']?json_encode($feedItemData['fields']):"");
                   $tipsCustomer->setType($feedItemData['type']??"");
                   $tipsCustomer->setRelation($feedItemData['relation']??0);
                   $tipsCustomer->setFeedTypeId($feedItemData['feed_type_id']??"");
                   $tipsCustomer->setNewCompanyId($feedItemData['new_company_id']??'');
                   $tipsCustomer->setCompanyHashId($feedItemData['company_hash_id']??'');
                   $tipsCustomer->setCompanyRawName($feedItemData['company_raw_name']??'');
                   $tipsCustomer->setNewCompanyName($feedItemData['new_company_name']??'');

                   if(isset($feedItemData['urls']) && $feedItemData['urls']){
                       $tipsCustomer->setUrls($feedItemData['urls']??[]);
                   }
                   $tipsCustomer->setDomain($feedItemData['domain']??'');
                   $tipsInfo->setCustomerInfo($tipsCustomer);
                   break;
               // type 6
               case \common\library\tips\TipsConstants::FEED_TYPE_CUSTOMERS_FIELD_NOT_FILLED:
                   $tipsCustomer = new \protobuf\Tips\PBTipsCustomerInfo();
                   $tipsCustomer->setCompanyId($feedItemData['company_id']);
                   $tipsCustomer->setName($feedItemData['name']??"");
                   $tipsCustomer->setFields($feedItemData['fields']?json_encode($feedItemData['fields']):"");
                   $tipsCustomer->setType($feedItemData['type']??"");
                   $tipsCustomer->setRelation($feedItemData['relation']??0);
                   $tipsCustomer->setFeedTypeId($feedItemData['feed_type_id']??"");
                   $tipsCustomer->setNewCompanyId($feedItemData['new_company_id']??0);
                   $tipsCustomer->setCompanyHashId($feedItemData['company_hash_id']??'');
                   $tipsCustomer->setCompanyRawName($feedItemData['company_raw_name']??'');
                   $tipsCustomer->setNewCompanyId($feedItemData['new_company_id']??0);
                   $tipsCustomer->setNewCompanyName($feedItemData['new_company_name']??'');
                   $tipsCustomer->setUrls($feedItemData['urls']??[]);
                   $tipsCustomer->setDomain($feedItemData['domain']??'');
                   $tipsInfo->setCustomerInfo($tipsCustomer);
                   break;

               // type 7
               case \common\library\tips\TipsConstants::FEED_TYPE_MAIL_SAVE_CUSTOMERS:
                   $tipsMailSaveCustomer = new \protobuf\Tips\PBTipsMailSaveCustomerInfo();
                   $tipsMailSaveCustomer->setAdviceId($feedItemData['advice_id']??0);
                   $tipsMailSaveCustomer->setNotAcceptType($feedItemData['not_accept_type']??0);
                   $tipsMailSaveCustomer->setMailId($feedItemData['mail_id']??0);
                   $tipsMailSaveCustomer->setSubject($feedItemData['subject']??"");
                   $tipsMailSaveCustomer->setEmail($feedItemData['email']??"");
                   $tipsMailSaveCustomer->setCustomerName($feedItemData['customer_name']??"");
                   $tipsMailSaveCustomer->setName($feedItemData['name']??"");
                   $tipsMailSaveCustomer->setCompanyId($feedItemData['company_id']);
                   $userMailId = 0;
                   if(isset($feedItem['refer_id']) && $feedItem['refer_id']){
                       $userMailId = $userMailIdMaps[$feedItem['refer_id']]??0;
                   }
                   $tipsMailSaveCustomer->setUserMailId($userMailId);
                   $tipsInfo->setMailSaveCustomerInfo($tipsMailSaveCustomer);
                   break;
                // type 8
               case \common\library\tips\TipsConstants::FEED_TYPE_OPPORTUNITY_UPDATED:
                   $tipsOpprotunity =  new \protobuf\Tips\PBTipsOpprotunityInfo();
                   $tipsOpprotunity->setOpportunityId(isset($feedItemData['opportunity_id']) && $feedItemData['opportunity_id']? intval($feedItemData['opportunity_id']) :0);
                   $tipsOpprotunity->setName($feedItemData['name']??"");
                   $tipsOpprotunity->setFields(isset($feedItemData['fields']) && $feedItemData['fields'] ?json_encode($feedItemData['fields']):"");
                   $tipsInfo->setOpprotunityInfo($tipsOpprotunity);
                   break;
               // type 12
               case \common\library\tips\TipsConstants::FEED_TYPE_COMPANY_RECOMMEND:
                    $feedItemList = [];
                    if(!isset($feedItemData[0])){
                        $feedItemList[] = $feedItemData;
                    }else{
                        $feedItemList = $feedItemData;
                    }
                   $tipsCompanyList = new \protobuf\Tips\PBTipsCompanyList();
                   $tipsCompany = $tipsCompanyList->getCompanyInfo();
                   foreach ($feedItemList as $companyItem){
                        $companyInfo = new \protobuf\Tips\PBTipsCompanyInfo();
                        $companyInfo->setName($companyItem['name']??"");
                        $companyInfo->setSales($companyItem['sales']??"");
                        $companyInfo->setSource($companyItem['source']??"");
                        $companyInfo->setCompanyHashId($companyItem['companyHashId']??"");
                        $companyInfo->setCountry($companyItem['country']??"");
                        $companyInfo->setDomain($companyItem['domain']??"");
                        $companyInfo->setHomePage($companyItem['homepage']??"");
                        $companyInfo->setIndustryTags($companyItem['industryTags']??"");
                        $companyInfo->setLogoUrl($companyItem['logoUrl']??"");
                        $companyInfo->setSicCode(isset($companyItem['sicCode']) && $companyItem['sicCode']? intval($companyItem['sicCode']) :0);
                        $companyInfo->setSicName($companyItem['sicName']??"");
                        $companyInfo->setSicCodeString($companyItem['sicCodeString']??"");
                        $companyInfo->setInterest(isset($companyItem['interest'])? intval($companyItem['interest']) : 0);
                        $companyInfo->setType($companyItem['type']??"");
                        $companyInfo->setFeedTypeId($companyItem['feed_type_id']??"");
                        $companyInfo->setCompanyRawName($companyItem['company_raw_name']??"");
                        $companyInfo->setRecommendCompanyId($companyItem['recommend_company_id']??"");
                        $companyInfo->setRecommendCompanyName($companyItem['recommend_company_name']??"");
                        $companyInfo->setCity($companyItem['city']??"");
                        $companyInfo->setFoundedYear($companyItem['founded_year']??0);
                        $companyInfo->setEmployees($companyItem['employees']??"");
                        $tipsCompany[] = $companyInfo;
                   }

                   $tipsCompanyList->setCompanyInfo($tipsCompany);
                   $tipsInfo->setCompanyList($tipsCompanyList);

                   break;
               // type 13
               case \common\library\tips\TipsConstants::FEED_TYPE_COMPANY_CONTACTS_NEW:

                   $tipsContact = new \protobuf\Tips\PBTipsContactInfo();
                   $tipsContact->setType($feedItemData['type']??"");
                   $tipsContact->setName($feedItemData['name']??"");
                   $tipsContact->setCompanyHashId($feedItemData['company_hash_id']??"");
                   $tipsContact->setCompanyRawName($feedItemData['company_raw_name']??"");
                   $tipsContact->setEmail($feedItemData['email']??"");
                   $tipsContact->setCount($feedItemData['count']??0);
                   $tipsContact->setCompanyId($feedItemData['company_id']??0);
                   $tipsContact->setCompanyName($feedItemData['company_name']??"");
                   $tipsContact->setFeedTypeId($feedItemData['feed_type_id']??"");
                   $tipsInfo->setContactInfo($tipsContact);

                   break;
               // type 14
               case \common\library\tips\TipsConstants::FEED_TYPE_COMPANY_CONTACTS_INVALID:

                   $tipsContact = new \protobuf\Tips\PBTipsContactInfo();
                   $tipsContact->setType($feedItemData['type']??"");
                   $tipsContact->setCompanyHashId($feedItemData['company_hash_id']??"");
                   $tipsContact->setCompanyRawName($feedItemData['company_raw_name']??"");
                   $tipsContact->setEmail($feedItemData['email']??"");
                   $tipsContact->setCount($feedItemData['count']??0);
                   $tipsContact->setName($feedItemData['name']??"");
                   $tipsContact->setCompanyId($feedItemData['company_id']??0);
                   $tipsContact->setCompanyName($feedItemData['company_name']??"");
                   $tipsContact->setFeedTypeId($feedItemData['feed_type_id']??"");
                   $tipsInfo->setContactInfo($tipsContact);
                   break;

               // type 15
               case \common\library\tips\TipsConstants::FEED_TYPE_COMPANY_BUSINESS:
                   $tipsContact = new \protobuf\Tips\PBTipsContactInfo();
                   $tipsContact->setType($feedItemData['type']??"");
                   $tipsContact->setCompanyHashId($feedItemData['company_hash_id']??"");
                   $tipsContact->setCompanyRawName($feedItemData['company_raw_name']??"");
                   $tipsContact->setEmail($feedItemData['email']??"");
                   $tipsContact->setCount($feedItemData['count']??0);
                   $tipsInfo->setContactInfo($tipsContact);
                   break;

               // type 16
               case \common\library\tips\TipsConstants::FEED_TYPE_CUSTOMS_NEW:
                   $tipsCiqInfo = new \protobuf\Tips\PBTipsCiqCustomerInfo();
                   $tipsCiqInfo->setId($feedItemData['id']??"");
                   $tipsCiqInfo->setCountry($feedItemData['country']??"");
                   $tipsCiqInfo->setCount($feedItemData['count']??0);
                   $tipsCiqInfo->setCompanyHashId($feedItemData['company_hash_id']??"");
                   $tipsCiqInfo->setCompanyRawName($feedItemData['company_raw_name']??"");
                   $tipsCiqInfo->setType($feedItemData['type']??"");
                   $tipsCiqInfo->setMoney(doubleval($feedItemData['money']??0));
                   $tipsCiqInfo->setAddress($feedItemData['address']??"");
                   $tipsCiqInfo->setUpdateTime($feedItemData['update_time']??"");
                   $tipsCiqInfo->setProductDesc($feedItemData['product_desc']??"");
                   $tipsCiqInfo->setExporter($feedItemData['exporter']??"");
                   $tipsCiqInfo->setExporterCountry($feedItemData['exporter_country']??"");
                   $tipsCiqInfo->setDate($feedItemData['date']??"");
                   $tipsCiqInfo->setHsCodeDesc($feedItemData['hscode_desc']??"");
                   $tipsCiqInfo->setHsCode($feedItemData['hscode']??"");
                   $tipsCiqInfo->setRankNo($feedItemData['rank_no']??0);
                   $tipsCiqInfo->setCountryCode($feedItemData['country_code']??'');
                   $tipsCiqInfo->setFeedTypeId($feedItemData['feed_type_id']??'');
                   if(isset($feedItemData['data']) && $feedItemData['data']){
                       $ciqRankList =  $tipsCiqInfo->getCiqRankList();
                       foreach ($feedItemData['data'] as $ciqItem){
                           $tipsCiqRank =new \protobuf\Tips\PBTipsCiqRank();
                           $tipsCiqRank->setHsCode(isset($ciqItem['hscode'])&& $ciqItem['hscode']?$ciqItem['hscode']:"");
                           $tipsCiqRank->setRankNo(isset($ciqItem['rank_no'])&& $ciqItem['rank_no']? intval($ciqItem['rank_no']):0);
                           $tipsCiqRank->setGrowRankNo(isset($ciqItem['grow_rank_no'])&& $ciqItem['grow_rank_no']? intval($ciqItem['grow_rank_no']):0);
                           $tipsCiqRank->setPrevRankNo(isset($ciqItem['prev_rank_no'])&& $ciqItem['prev_rank_no']? intval($ciqItem['prev_rank_no']):0);
                           $tipsCiqRank->setTraderName($ciqItem['trader_name']??"");
                           $tipsCiqRank->setTraderCountryCode($ciqItem['trader_country_code']??"");
                           $ciqRankList[] = $tipsCiqRank;
                       }
                       $tipsCiqInfo->setCiqRankList($ciqRankList);
                   }
                   $tipsInfo->setCiqInfo($tipsCiqInfo);

                   break;

               // type 17
               case \common\library\tips\TipsConstants::FEED_TYPE_GOOGLE_KEYWORD:
                   $googleKeywordInfo = new \protobuf\Tips\PBTipsGoogleKeywordInfo();
                   $googleKeywordInfo->setType($feedItemData['type']??"");
                   $googleKeywordInfo->setFeedTypeId($feedItemData['feed_type_id']??"");
                   $googleKeywordInfo->setCompanyHashId($feedItemData['company_hash_id']??"");
                   $googleKeywordInfo->setCompanyRawName($feedItemData['company_raw_name']??"");
                   if(isset($feedItemData['tags']) && $feedItemData['tags']){
                       $tagList = $googleKeywordInfo->getTagsList();
                       foreach ($feedItemData['tags'] as $tagItem){
                           $tagsInfo = new \protobuf\Tips\PBTagsInfo();
                           $tagsInfo->setTagType($tagItem['tagType']??"");
                           $tagsInfo->setTagValue($tagItem['tagValue']??"");
                           $tagsInfo->setCreateTime($tagItem['createTime']??"");
                           $tagsInfo->setUpdateTime($tagItem['updateTime']??"");
                           $tagsInfo->setTagValueLang($tagItem['tagValueLang']??"");
                           $tagList[] = $tagsInfo;
                       }
                       $googleKeywordInfo->setTagsList($tagList);
                   }
                   $tipsInfo->setGoogleKeywordInfo($googleKeywordInfo);
                   break;

               // type 18
               case \common\library\tips\TipsConstants::FEED_TYPE_DISCOVERY_KEYWORD:
                   $googleKeywordInfo = new \protobuf\Tips\PBTipsGoogleKeywordInfo();
                   $googleKeywordInfo->setType($feedItemData['type']??"");
                   $googleKeywordInfo->setFeedTypeId($feedItemData['feed_type_id']??"");
                   $googleKeywordInfo->setCompanyHashId($feedItemData['company_hash_id']??"");
                   $googleKeywordInfo->setCompanyRawName($feedItemData['company_raw_name']??"");
                   if(isset($feedItemData['tags']) && $feedItemData['tags']){
                       $tagList = $googleKeywordInfo->getTagsList();
                       foreach ($feedItemData['tags'] as $tagItem){
                           $tagsInfo = new \protobuf\Tips\PBTagsInfo();
                           $tagsInfo->setTagType($tagItem['tagType']??"");
                           $tagsInfo->setTagValue($tagItem['tagValue']??"");
                           $tagsInfo->setCreateTime($tagItem['createTime']??"");
                           $tagsInfo->setUpdateTime($tagItem['updateTime']??"");
                           $tagsInfo->setTagValueLang($tagItem['tagValueLang']??"");
                           $tagList[] = $tagsInfo;
                       }
                       $googleKeywordInfo->setTagsList($tagList);
                   }
                   $tipsInfo->setGoogleKeywordInfo($googleKeywordInfo);
                   break;

               // type 19 行业tip
               case \common\library\tips\TipsConstants::FEED_TYPE_SIC_CODE:
                   $industryInfo = new \protobuf\Tips\PBTipsIndustryInfo();
                   $industryInfo->setType($feedItemData['type']??'');
                   $industryInfo->setSicName($feedItemData['sicName']??'');
                   $industryInfo->setSicCode($feedItemData['siccode']??'');
                   $industryInfo->setCompanyCount($feedItemData['companyCount']??0);
                   $industryInfo->setFeedTypeId($feedItemData['feed_type_id']??'');
                   $industryInfo->setFeedTypeId($feedItemData['feed_type_id']??'');
                   $industryInfo->setCompanyHashId($feedItemData['company_hash_id']??'');
                   $industryInfo->setCompanyRawName($feedItemData['company_raw_name']??'');
                   $industryInfo->setPrimaryCompanyCount(isset($feedItemData['primaryCompanyCount']) && $feedItemData['primaryCompanyCount']? intval($feedItemData['primaryCompanyCount']):0);
                   $industryInfo->setPrimaryIndustryCode($feedItemData['primaryIndustryCode']??'');
                   $industryInfo->setPrimaryIndustryName($feedItemData['primaryIndustryName']??'');
                   $tipsInfo->setIndustryInfo($industryInfo);
                   break;

               // type 20
               case \common\library\tips\TipsConstants::FEED_TYPE_MAIL_NORMAL_NOT_RESPOND:

                   $tipsMail =  new \protobuf\Tips\PBTipsMailInfo();
                   $tipsMail->setEmial($feedItemData['email']??"");
                   $tipsMail->setMailId(isset($feedItemData['mail_id'])?intval($feedItemData['mail_id']):0);
                   $tipsMail->setSubject($feedItemData['subject']??"");
                   $tipsMail->setMailTag(isset($feedItemData['mail_tag']) && $feedItemData['mail_tag'] ? intval($feedItemData['mail_tag']) : 0);
                   $tipsMail->setCompanyId(isset($feedItemData['company_id'])?intval($feedItemData['company_id']):0);
                   $tipsMail->setName($feedItemData['name']??"");
                   $tipsMail->setMailReceiveTime($feedItemData['mail_receive_time']??"");
                   $tipsMail->setFeedTypeId($feedItemData['feed_type_id']??"");
                   $tipsMail->setType($feedItemData['type']??"");
                   $userMailId = 0;
                   if(isset($feedItem['refer_id']) && $feedItem['refer_id']){
                       $userMailId = $userMailIdMaps[$feedItem['refer_id']]??0;
                   }
                   $tipsMail->setUserMailId($userMailId);
                   $tipsInfo->setMailInfo($tipsMail);
                   break;
               default:
                   break;
           }
           $feedList[] = $tipsInfo;
        }

        $rsp->setFeeds($feedList);
        $this->successPb($rsp);
    }
}