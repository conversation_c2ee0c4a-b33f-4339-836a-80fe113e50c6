<?php
/**
 * Created by PhpStorm.
 * User: rivers
 * Date: 2020-08-12
 * Time: 10:52
 */

use common\library\notification\ConfigHelper;
use common\library\notification\Constant;
use common\library\notification\Notification;
use common\library\notification\NotificationBatchOperator;
use common\library\notification\NotificationHelper;
use common\library\notification\NotificationList;
use common\library\privilege_v3\PrivilegeConstants;
use common\library\privilege_v3\PrivilegeService;
use common\library\product_update\ProductUpdateList;
use common\library\setting\user\UserSetting;
use protobuf\CRMCommon\PBNoticeListModuleInfo;
use protobuf\CRMCommon\PBNoticeListRsp;
use protobuf\CRMCommon\PBNotificationApproveCloseInfo;
use protobuf\CRMCommon\PBNotificationApproveCustomizeInfo;
use protobuf\CRMCommon\PBNotificationApproveResultInfo;
use protobuf\CRMCommon\PBNotificationCompanyInfo;
use protobuf\CRMCommon\PBNotificationConflictEmails;
use protobuf\CRMCommon\PBNotificationContentInfo;
use protobuf\CRMCommon\PBNotificationCustomerAliSyncInfo;
use protobuf\CRMCommon\PBNotificationCustomerInfo;
use protobuf\CRMCommon\PBNotificationCustomerMergeInfo;
use protobuf\CRMCommon\PBNotificationDetailReq;
use protobuf\CRMCommon\PBNotificationDetailRsp;
use protobuf\CRMCommon\PBNotificationEmailInfo;
use protobuf\CRMCommon\PBNotificationGetPushSettingRsp;
use protobuf\CRMCommon\PBNotificationInfo;
use protobuf\CRMCommon\PBNotificationInvoiceInfo;
use protobuf\CRMCommon\PBNotificationLeadInfo;
use protobuf\CRMCommon\PBNotificationLeadTransferInfo;
use protobuf\CRMCommon\PBNotificationListReq;
use protobuf\CRMCommon\PBNotificationListRsp;
use protobuf\CRMCommon\PBNotificationMailInfo;
use protobuf\CRMCommon\PBNotificationModuleListRsp;
use protobuf\CRMCommon\PBNotificationOperatorUserInfo;
use protobuf\CRMCommon\PBNotificationPaypalInfo;
use protobuf\CRMCommon\PBNotificationProductInfo;
use protobuf\CRMCommon\PBNotificationProductList;
use protobuf\CRMCommon\PBNotificationProductUpdateListReq;
use protobuf\CRMCommon\PBNotificationProductUpdateListRsp;
use protobuf\CRMCommon\PBNotificationRemindTime;
use protobuf\CRMCommon\PBNotificationScheduleInfo;
use protobuf\CRMCommon\PBNotificationSystemAliAuthInfo;
use protobuf\CRMCommon\PBNotificationSystemAliExpiredInfo;
use protobuf\CRMCommon\PBNotificationSystemAliExpiredProcessList;
use protobuf\CRMCommon\PBNotificationSystemDiskSpaceWillOverLimitInfo;
use protobuf\CRMCommon\PBNotificationSystemInfo;
use protobuf\CRMCommon\PBNotificationWillMovePublic;
use protobuf\CRMCommon\PBNotificationWorkFlowCustomizeInfo;
use protobuf\CRMCommon\PBNotificationWorkFlowInfo;
use protobuf\CRMCommon\PBPushSettingInfo;
use protobuf\CRMCommon\PBScheduleRepeatRule;
use protobuf\CRMCommon\PBWeeklyStatisticData;
use protobuf\CRMCommon\PBWeeklyStatisticInfo;
use protobuf\CRMCommon\PBWeeklyStatisticValues;
use protobuf\CRMCommon\PBWorkFlowListData;

/**
 * Class NotificationReadController
 *
 * @package common\modules\storms_fury\controllers
 */
class NotificationReadController extends StormsFuryController {


	/**
	 * @param                    $data
	 * @param PBNotificationInfo $PBNotificationInfo
	 * @return void
	 */
	public function getPBScheduleInfo($data, PBNotificationInfo $PBNotificationInfo) {

		$scheduleInfo = new PBNotificationScheduleInfo();

		$scheduleInfo->setEditUserName($data['edit_user_name']);
		$scheduleInfo->setTitle($data['title']);
		$scheduleInfo->setStartTime(!empty($data['start_time']) ? strtotime($data['start_time']) : 0);
		$scheduleInfo->setEndTime(!empty($data['end_time']) ? strtotime($data['end_time']) : 0);
		$scheduleInfo->setColor($data['color']);
		$scheduleInfo->setReferName($data['refer_name'] ?? '');
		$scheduleInfo->setParticipant($data['participant'] ?? '');

		$pbRepeatRule = new PBScheduleRepeatRule();

		$pbRepeatRule->setType($data['repeat_rule']['type'] ?? '');
		$pbRepeatRule->setRepeatStart(!empty($data['repeat_rule']['repeat_start']) ? strtotime($data['repeat_rule']['repeat_start']) : 0);
		$pbRepeatRule->setRepeatEnd(!empty($data['repeat_rule']['repeat_end']) ? strtotime($data['repeat_rule']['repeat_end']) : 0);

		$scheduleInfo->setRepeatRule($pbRepeatRule);

		$remindTime = [];

		foreach ($data['remind_time'] ?? [] as $datum) {

			$pbRemindTime = new PBNotificationRemindTime();

			$pbRemindTime->setType($datum['type']);

			$pbRemindTime->setTime(!empty($datum['time']) ? strtotime($datum['time']) : 0);

			$remindTime[] = $pbRemindTime;
		}

		$scheduleInfo->setRemindTime($remindTime);

		$PBNotificationInfo->setScheduleInfo($scheduleInfo);
	}

	public function getPBLeadTransferInfo($data, PBNotificationInfo $PBNotificationInfo) {

		$PBNotificationLeadTransferInfo = new PBNotificationLeadTransferInfo();

		$PBNotificationLeadTransferInfo->setIsAli($data['is_ali']);

		$PBNotificationLeadTransferInfo->setCount($data['count']);

		$userInfo = [
			'user_id' => $data['op_user']['user_id'],
			'email'   => $data['op_user']['email'],
			'name'    => $data['op_user']['name'],
		];

		$PBNotificationOperatorUserInfo = $this->getPBNotificationOperatorUserInfo($userInfo);

		$PBNotificationLeadTransferInfo->setOpUser($PBNotificationOperatorUserInfo);


		$leadList = [];

		foreach ($data['list'] as $datum) {

			$PBNotificationLeadInfo = new PBNotificationLeadInfo();

			$PBNotificationLeadInfo->setLeadId($datum['lead_id']);

			$PBNotificationLeadInfo->setName($datum['name']);

			$leadList[] = $PBNotificationLeadInfo;
		}

		$PBNotificationLeadTransferInfo->setLeadInfo($leadList);

		$PBNotificationInfo->setLeadTransferInfo($PBNotificationLeadTransferInfo);
	}

	public function getPBCustomerWillMove($data, PBNotificationInfo $PBNotification) {

		$PBNotificationWillMovePublic = new PBNotificationWillMovePublic();

		$PBNotificationWillMovePublic->setNickname($data['nickname']);

		$PBNotificationWillMovePublic->setViewUrl($data['v4Url']);

		$PBNotificationWillMovePublic->setCustomerCount($data['customerCount']);

		$PBNotificationWillMovePublic->setDay($data['day']);

		$PBNotification->setWillMovePublic($PBNotificationWillMovePublic);
	}

	public function getPBCustomerInfo($data, PBNotificationInfo $PBNotification) {

		$PBNotificationCustomerInfo = new PBNotificationCustomerInfo();

		$PBNotificationCustomerInfo->setCount($data['count']);

		$PBNotificationCustomerInfo->setDomain($data['domain']);

		$userInfo = [
			'name' => $data['operator'],
		];

		$PBNotificationOperatorUserInfo = $this->getPBNotificationOperatorUserInfo($userInfo);

		$PBNotificationCustomerInfo->setOpUser($PBNotificationOperatorUserInfo);

		$companyInfo = [];

		foreach ($data['company_data'] as $datum) {

			$PBNotificationCompanyInfo = new PBNotificationCompanyInfo();

			$PBNotificationCompanyInfo->setCount($datum['count'] ?? 0);
			$PBNotificationCompanyInfo->setGroupId($datum['group_id']);
			$PBNotificationCompanyInfo->setGroupName($datum['group_name']);
			$PBNotificationCompanyInfo->setCompanyName($datum['company_name']);
			$PBNotificationCompanyInfo->setCompanyId($datum['company_id']);

			$companyInfo[] = $PBNotificationCompanyInfo;
		}

		$PBNotificationCustomerInfo->setCompanyInfo($companyInfo);

		$PBNotification->setCustomerInfo($PBNotificationCustomerInfo);
	}

	public function getPBEmailInfo($data, PBNotificationInfo $PBNotification) {

		$PBNotificationEmailInfo = new PBNotificationEmailInfo();

		$PBNotificationEmailInfo->setShowMail($data['show_mail']);


		$PBNotificationMailInfo = new PBNotificationMailInfo();

		$PBNotificationMailInfo->setIsSelf($data['mail_info']['is_self']);

		$userInfo = [
			'name' => $data['mail_info']['operator'],
		];

		$PBNotificationOperatorUserInfo = $this->getPBNotificationOperatorUserInfo($userInfo);

		$PBNotificationMailInfo->setOpUser($PBNotificationOperatorUserInfo);

		$PBNotificationMailInfo->setMailId($data['mail_info']['mail_id']);
		$PBNotificationMailInfo->setMailUserId($data['mail_info']['mail_user_id']);
		$PBNotificationMailInfo->setMailType($data['mail_info']['mail_type']);

		$PBNotificationEmailInfo->setMailInfo($PBNotificationMailInfo);


		$conflicts = [];

		foreach ($data['conflict_emails'] as $datum) {


			$PBNotificationConflictEmails = new PBNotificationConflictEmails();

			$PBNotificationConflictEmails->setCompanyId($datum['company_id']);
			$PBNotificationConflictEmails->setCompanyName($datum['company_name']);
			$PBNotificationConflictEmails->setCustomerId($datum['customer_id']);
			$PBNotificationConflictEmails->setCustomerName($datum['customer_name']);
			$PBNotificationConflictEmails->setEmail($datum['email']);
			$PBNotificationConflictEmails->setCompanyOwner($datum['company_owner']);
			$PBNotificationConflictEmails->setName($datum['name'] ?? '');
			$PBNotificationConflictEmails->setUserId($datum['user_id'] ?? '');

			$conflicts[] = $PBNotificationConflictEmails;
		}

		$PBNotificationEmailInfo->setConflictEmails($conflicts);

		$PBNotification->setEmailInfo($PBNotificationEmailInfo);
	}

	public function getPBCustomerMergeInfo($data, PBNotificationInfo $PBNotification) {

		$PBNotificationCustomerMergeInfo = new PBNotificationCustomerMergeInfo();

		$userInfo = [
			'user_id' => $data['operator_id'],
			'email'   => $data['operator_email'],
			'name'    => $data['operator_name'],
		];

		$PBNotificationOperatorUserInfo = $this->getPBNotificationOperatorUserInfo($userInfo);

		$PBNotificationCustomerMergeInfo->setOpUser($PBNotificationOperatorUserInfo);
		$PBNotificationCustomerMergeInfo->setMainCompanyId($data['main_company_id']);
		$PBNotificationCustomerMergeInfo->setMainCompanyName($data['main_company_name']);
		$PBNotificationCustomerMergeInfo->setMainCompanySerial($data['main_company_serial']);
		$PBNotificationCustomerMergeInfo->setAnotherCompanyId($data['another_company_id']);
		$PBNotificationCustomerMergeInfo->setAnotherCompanyName($data['another_company_name']);
		$PBNotificationCustomerMergeInfo->setAnotherCompanySerial($data['another_company_serial']);
		$PBNotificationCustomerMergeInfo->setNotifyUserList($data['notify_user_list'] ?? []);


		$PBNotification->setCustomerMergeInfo($PBNotificationCustomerMergeInfo);
	}

	public function getPBCustomerAliSyncInfo($data, PBNotificationInfo $PBNotification) {

		$PBNotificationCustomerAliSyncInfo = new PBNotificationCustomerAliSyncInfo();

		$PBNotificationCustomerAliSyncInfo->setFinishTime(empty($data['finish_time']) ? 0 : strtotime($data['finish_time']));
		$PBNotificationCustomerAliSyncInfo->setStoreName($data['store_name']);
		$PBNotificationCustomerAliSyncInfo->setSellerNickname($data['seller_account_info']['seller_nickname'] ?? '');
		$PBNotificationCustomerAliSyncInfo->setPublicCount($data['public_count'] ?? '');
		$PBNotificationCustomerAliSyncInfo->setPrivateCount($data['private_count'] ?? '');
		$PBNotificationCustomerAliSyncInfo->setAliPublicCount($data['ali_public_count'] ?? '');
		$PBNotificationCustomerAliSyncInfo->setAliPrivateCount($data['ali_private_count'] ?? '');
		$PBNotificationCustomerAliSyncInfo->setFileName($data['file_name'] ?? '');
		$PBNotificationCustomerAliSyncInfo->setResultUrl($data['result_url'] ?? '');
		$PBNotificationCustomerAliSyncInfo->setFailCount($data['fail_count'] ?? '');

		$PBNotification->setCustomerAliSyncInfo($PBNotificationCustomerAliSyncInfo);
	}

	public function getPBInvoiceOrderInfo($data, PBNotificationInfo $PBNotification) {

		$PBNotificationInvoiceInfo = new PBNotificationInvoiceInfo();

		$PBNotificationInvoiceInfo->setInvoiceId($data['order_id']);
		$PBNotificationInvoiceInfo->setInvoiceNo($data['order_no'] ?? '');
		$PBNotificationInvoiceInfo->setInvoiceName($data['order_name'] ?? '');
		$PBNotificationInvoiceInfo->setInvoiceUrl($data['order_url'] ?? '');

		$userInfo = [
			'name' => $data['op_user_name'] ?? '',
		];

		$PBNotificationOperatorUserInfo = $this->getPBNotificationOperatorUserInfo($userInfo);

		$PBNotificationInvoiceInfo->setOpUser($PBNotificationOperatorUserInfo);

		$PBNotification->setInvoiceInfo($PBNotificationInvoiceInfo);
	}

	public function getPBInvoiceQuotationInfo($data, PBNotificationInfo $PBNotification) {

		$PBNotificationInvoiceInfo = new PBNotificationInvoiceInfo();

		$PBNotificationInvoiceInfo->setInvoiceId($data['quotation_id']);
		$PBNotificationInvoiceInfo->setInvoiceNo($data['quotation_no'] ?? '');
		$PBNotificationInvoiceInfo->setInvoiceName($data['quotation_name'] ?? '');
		$PBNotificationInvoiceInfo->setInvoiceUrl($data['quotation_url'] ?? '');

		$userInfo = [
			'name' => $data['op_user_name'] ?? '',
		];

		$PBNotificationOperatorUserInfo = $this->getPBNotificationOperatorUserInfo($userInfo);

		$PBNotificationInvoiceInfo->setOpUser($PBNotificationOperatorUserInfo);

		$PBNotification->setInvoiceInfo($PBNotificationInvoiceInfo);
	}

	public function getPBInvokePaypalInfo($data, PBNotificationInfo $PBNotification) {

		$PBNotificationPaypalInfo = new PBNotificationPaypalInfo();

		$PBNotificationPaypalInfo->setAmount($data['amount']);
		$PBNotificationPaypalInfo->setCurrency($data['currency'] ?? '');
		$PBNotificationPaypalInfo->setInvoiceId($data['invoice_id'] ?? '');

		$PBNotification->setInvoicePaypalInfo($PBNotificationPaypalInfo);
	}

	public function getPBProductInfo($data, PBNotificationInfo $PBNotification) {

		$PBNotificationProductInfo = new PBNotificationProductInfo();

		$PBNotificationProductInfo->setTitle($data['title']);
		$PBNotificationProductInfo->setSubject($data['subject']);
		$PBNotificationProductInfo->setMailId($data['mail_id']);
		$PBNotificationProductInfo->setProductIds($data['productIds']);
		$PBNotificationProductInfo->setTotal($data['total']);
		$PBNotificationProductInfo->setTemplateId($data['template_id']);

		$PBNotification->setProductInfo($PBNotificationProductInfo);
	}

	public function getPBApprovalCustomizeInfo($data, PBNotificationInfo $PBNotification) {

		$PBNotificationApproveCustomizeInfo = new PBNotificationApproveCustomizeInfo();

		$PBNotificationApproveCustomizeInfo->setContentText($data['content_text']);

		$PBNotificationApproveCustomizeInfo->setSubject($data['subject']);

		$content = [];

		foreach ($data['content'] ?? [] as $datum) {

			$PBNotificationContentInfo = new PBNotificationContentInfo();

			$PBNotificationContentInfo->setText($datum['text']);

			$PBNotificationContentInfo->setActionText($datum['action']['text']);

			$PBNotificationContentInfo->setActionLinkId($datum['action']['link']['id']);

			$PBNotificationContentInfo->setActionLinkType($datum['action']['link']['type']);

			$content[] = $PBNotificationContentInfo;
		}

		$PBNotificationApproveCustomizeInfo->setContent($content);

		$PBNotification->setApproveCustomizeInfo($PBNotificationApproveCustomizeInfo);
	}

	public function getPBApproveCloseInfo($data, PBNotificationInfo $PBNotification) {


		$PBNotificationApproveCloseInfo = new PBNotificationApproveCloseInfo();

		$PBNotificationApproveCloseInfo->setTitle($data['title']);
		$PBNotificationApproveCloseInfo->setApprovalFlowTitle($data['approval_flow_title']);
		$PBNotificationApproveCloseInfo->setApplyFormMap($data['applyFormMap'] ?? []);
		$PBNotificationApproveCloseInfo->setApprovalFormMap($data['approvalFormMap'] ?? []);

		$PBNotification->setApproveCloseInfo($PBNotificationApproveCloseInfo);
	}

	public function getPBApproveResultInfo($data, PBNotificationInfo $PBNotification) {

		$PBNotificationApproveResultInfo = new PBNotificationApproveResultInfo();

		$PBNotificationApproveResultInfo->setUserId($data['user_id']);
		$PBNotificationApproveResultInfo->setApprovalId($data['approval_id']);
		$PBNotificationApproveResultInfo->setApprovedNotifyType($data['approved_notify_type']);
		$PBNotificationApproveResultInfo->setTitle($data['title']);
		$PBNotificationApproveResultInfo->setApprovalStatus($data['approval_status'] ?? 0);
		$PBNotificationApproveResultInfo->setContent($data['content'] ?? '');
		$PBNotificationApproveResultInfo->setApplyUserName($data['apply_user_name'] ?? '');

		$PBNotification->setApproveResultInfo($PBNotificationApproveResultInfo);
	}

	public function getPBWorkFlowCustomizeInfo($data, PBNotificationInfo $PBNotification) {

		$PBNotificationWorkFlowCustomizeInfo = new PBNotificationWorkFlowCustomizeInfo();

		$PBNotificationWorkFlowCustomizeInfo->setContent($data['content'] ?? '');

		$PBNotificationWorkFlowCustomizeInfo->setSubject($data['subject'] ?? '');

		$PBNotificationWorkFlowCustomizeInfo->setContentText($data['content_text'] ?? '');

		$PBNotification->setWorkflowCustomizeInfo($PBNotificationWorkFlowCustomizeInfo);
	}

	public function getPBWorkFlowInfo($data, PBNotificationInfo $PBNotification) {

		$PBNotificationWorkFlowInfo = new PBNotificationWorkFlowInfo();

		$PBNotificationWorkFlowInfo->setReferType($data['referType'] ?? 0);
		$PBNotificationWorkFlowInfo->setReferTypeName($data['referTypeName'] ?? '');
		$PBNotificationWorkFlowInfo->setField($data['field'] ?? '');
		$PBNotificationWorkFlowInfo->setFieldName($data['field_name'] ?? '');
		$PBNotificationWorkFlowInfo->setRuleId($data['ruleId'] ?? 0);
		$PBNotificationWorkFlowInfo->setCreateUserName($data['create_user_name'] ?? '');

		$listData = [];

		foreach ($data['listData'] ?? [] as $item) {

			$PBWorkFlowListData = new PBWorkFlowListData();

			$PBWorkFlowListData->setUrl($item['url'] ?? '');

			$PBWorkFlowListData->setName($item['name'] ?? '');

			$PBWorkFlowListData->setAssignUser($item['assign_user'] ?? '');

			$listData[] = $PBWorkFlowListData;
		}

		$PBNotificationWorkFlowInfo->setListData($listData);

		$PBNotification->setWorkflowInfo($PBNotificationWorkFlowInfo);
	}

	public function getPBDiskSpaceWillOverLimitInfo($data, PBNotificationInfo $PBNotification) {

		$PBNotificationSystemDiskSpaceWillOverLimitInfo = new PBNotificationSystemDiskSpaceWillOverLimitInfo();

		$PBNotificationSystemDiskSpaceWillOverLimitInfo->setDay($data['day']);
		$PBNotificationSystemDiskSpaceWillOverLimitInfo->setValidTo($data['valid_to']);
		$PBNotificationSystemDiskSpaceWillOverLimitInfo->setSpace($data['space']);
		$PBNotificationSystemDiskSpaceWillOverLimitInfo->setCanUserSpace($data['can_user_space']);
		$PBNotificationSystemDiskSpaceWillOverLimitInfo->setCurrentSpace($data['current_space']);
		$PBNotificationSystemDiskSpaceWillOverLimitInfo->setUsedTotal($data['used_total']);

		$PBNotification->setDiskSpaceWillOverLimit($PBNotificationSystemDiskSpaceWillOverLimitInfo);

	}

	public function getPBSystemInfo($data, PBNotificationInfo $PBNotification) {

		$PBNotificationSystemInfo = new PBNotificationSystemInfo();

		$PBNotificationSystemInfo->setContent($data['content']);

		$PBNotificationSystemInfo->setTitle($data['title']);

		$PBNotification->setSystemInfo($PBNotificationSystemInfo);
	}

	public function getPBAliAuthInfo($data, PBNotificationInfo $PBNotification) {

		$PBNotificationSystemAliAuthInfo = new PBNotificationSystemAliAuthInfo();

		$PBNotificationSystemAliAuthInfo->setTitle($data['title'] ?? '');
		$PBNotificationSystemAliAuthInfo->setStoreId($data['store_id'] ?? 0);
		$PBNotificationSystemAliAuthInfo->setStoreName($data['store_name']);
		$PBNotificationSystemAliAuthInfo->setBindTime(!empty($data['bind_time']) ? strtotime($data['bind_time']) : 0);
		$PBNotificationSystemAliAuthInfo->setExpireDay($data['expire_day']);

		$PBNotification->setAliAuthInfo($PBNotificationSystemAliAuthInfo);
	}


	public function getPBAliExpiredProcessInfo($data, PBNotificationInfo $PBNotification) {

		$PBNotificationSystemAliExpiredProcessList = new PBNotificationSystemAliExpiredProcessList();

		$list = [];

		foreach ($data['store_list'] as $datum) {

			$list[] = $this->getPBNotificationSystemAliExpiredInfo($datum);
		}

		$PBNotificationSystemAliExpiredProcessList->setInfo($list);

		$PBNotification->setAliExpiredProcessList($PBNotificationSystemAliExpiredProcessList);
	}


	public function getPBAliExpiredInfo($data, PBNotificationInfo $PBNotification) {

		$PBNotificationSystemAliExpiredInfo = $this->getPBNotificationSystemAliExpiredInfo($data);

		$PBNotification->setAliExpiredInfo($PBNotificationSystemAliExpiredInfo);
	}


	/**
	 * @param $data
	 * @return PBNotificationSystemAliExpiredInfo
	 */
	public function getPBNotificationSystemAliExpiredInfo($data): PBNotificationSystemAliExpiredInfo {

		$PBNotificationSystemAliExpiredInfo = new PBNotificationSystemAliExpiredInfo();

		$PBNotificationSystemAliExpiredInfo->setStoreId($data['store_id']);
		$PBNotificationSystemAliExpiredInfo->setStoreName($data['store_name']);
		$PBNotificationSystemAliExpiredInfo->setExpireDay($data['expire_day'] ?? 0);
		$PBNotificationSystemAliExpiredInfo->setExpireTime(!empty($data['expire_time']) ? strtotime($data['expire_time']) : 0);
		$PBNotificationSystemAliExpiredInfo->setSellerEmail($data['seller_account_info']['seller_email']);
		$PBNotificationSystemAliExpiredInfo->setSellerNickname($data['seller_account_info']['seller_nickname']);
		$PBNotificationSystemAliExpiredInfo->setSellerNickname(!empty($data['unbind_time']) ? strtotime($data['unbind_time']) : 0);

		return $PBNotificationSystemAliExpiredInfo;

	}

	/**
	 * @param $data ['user_id' => 1, 'email' => '<EMAIL>', 'name' => '1']
	 * @return PBNotificationOperatorUserInfo
	 */
	public function getPBNotificationOperatorUserInfo($data): PBNotificationOperatorUserInfo {

		$PBNotificationOperatorUserInfo = new PBNotificationOperatorUserInfo();

		$PBNotificationOperatorUserInfo->setUserId($data['user_id'] ?? 0);

		$PBNotificationOperatorUserInfo->setEmail($data['email'] ?? '');

		$PBNotificationOperatorUserInfo->setName($data['name'] ?? '');

		return $PBNotificationOperatorUserInfo;
	}

	public function getPBNotificationInfo($value): PBNotificationInfo {

		$PBNotificationInfo = new PBNotificationInfo();

		$PBNotificationInfo->setNotificationId($value['notification_id']);
		$PBNotificationInfo->setUserId($value['user_id']);
		$PBNotificationInfo->setCreateUserId($value['create_user_id']);
		$PBNotificationInfo->setType($value['type']);
		$PBNotificationInfo->setReferId($value['refer_id']);
		$PBNotificationInfo->setReadFlag($value['read_flag']);
		$PBNotificationInfo->setPinFlag($value['pin_flag']);
		$PBNotificationInfo->setEnableFlag($value['enable_flag']);
		$PBNotificationInfo->setCreateTime(!empty($value['create_time']) ? strtotime($value['create_time']) : 0);
		$PBNotificationInfo->setUpdateTime(!empty($value['create_time']) ? strtotime($value['update_time']) : 0);
		$PBNotificationInfo->setModule($value['module']);
		$PBNotificationInfo->setTitle(strtotime($value['title']));

		if (array_key_exists($value['type'], self::getPBSourceDataMapping())) {

			call_user_func_array([$this, self::getPBSourceDataMapping()[$value['type']]], [$value['data'], $PBNotificationInfo]);
		}

		return $PBNotificationInfo;
	}


	/**
	 * @return array
	 */
	public static function getPBSourceDataMapping(): array {

		return [

			Constant::NOTIFICATION_TYPE_SCHEDULE_NEW          => 'getPBScheduleInfo',
			Constant::NOTIFICATION_TYPE_SCHEDULE_DEL          => 'getPBScheduleInfo',
			Constant::NOTIFICATION_TYPE_SCHEDULE_EDIT         => 'getPBScheduleInfo',
			Constant::NOTIFICATION_TYPE_SCHEDULE_COMPLETE     => 'getPBScheduleInfo',
			Constant::NOTIFICATION_TYPE_SCHEDULE_COMMENT      => 'getPBScheduleInfo',
			Constant::NOTIFICATION_TYPE_SCHEDULE_EDIT_COMMENT => 'getPBScheduleInfo',

			Constant::NOTIFICATION_TYPE_LEAD_TRANSFER => 'getPBLeadTransferInfo',

			Constant::NOTIFICATION_TYPE_CUSTOMER_MOVE_PUBLIC   => 'getPBCustomerWillMove',
			Constant::NOTIFICATION_TYPE_CUSTOMER_SHARE         => 'getPBCustomerInfo',
			Constant::NOTIFICATION_TYPE_CUSTOMER_MAIL_CONFLICT => 'getPBEmailInfo',
			Constant::NOTIFICATION_TYPE_CUSTOMER_TRANSFER      => 'getPBCustomerInfo',
			Constant::NOTIFICATION_TYPE_CUSTOMER_CANCEL        => 'getPBCustomerInfo',
			Constant::NOTIFICATION_TYPE_CUSTOMER_MERGE         => 'getPBCustomerMergeInfo',
			Constant::NOTIFICATION_TYPE_CUSTOMER_ALIBABA_SYNC  => 'getPBCustomerAliSyncInfo',

			Constant::NOTIFICATION_TYPE_ORDER_HANDLER_ADD    => 'getPBInvoiceOrderInfo',
			Constant::NOTIFICATION_TYPE_ORDER_HANDLER_REMOVE => 'getPBInvoiceOrderInfo',
			Constant::NOTIFICATION_TYPE_ORDER_DELETE         => 'getPBInvoiceOrderInfo',
			Constant::NOTIFICATION_TYPE_QUOTATION_DELETE     => 'getPBInvoiceQuotationInfo',
			Constant::NOTIFICATION_TYPE_PAYPAL_RECEIPT       => 'getPBInvokePaypalInfo',


			Constant::NOTIFICATION_TYPE_AI_PRODUCT => 'getPBProductInfo',

			Constant::NOTIFICATION_TYPE_APPROVALFLOW_NOTIFY             => 'getPBApprovalCustomizeInfo',
			Constant::NOTIFICATION_TYPE_APPROVALFLOWCONFIG_CLOSE_NOTIFY => 'getPBApproveCloseInfo',
			Constant::NOTIFICATION_TYPE_APPROVALFLOW_RESULE_NOTITY      => 'getPBApproveResultInfo',
			Constant::NOTIFICATION_TYPE_APPROVALFLOW_TODO               => 'getPBApproveResultInfo',

			Constant::NOTIFICATION_TYPE_WORKFLOW_NOTIFY             => 'getPBWorkFlowCustomizeInfo',
			Constant::NOTIFICATION_TYPE_WORKFLOW_ASSIGN_NOTIFY      => 'getPBWorkFlowInfo',
			Constant::NOTIFICATION_TYPE_WORKFLOW_BE_ASSIGNED_NOTIFY => 'getPBWorkFlowInfo',

			Constant::NOTIFICATION_TYPE_DISK_SPACE_WILL_OVER_LIMIT    => 'getPBDiskSpaceWillOverLimitInfo',
			Constant::NOTIFICATION_TYPE_SYSTEM_NOTICE                 => 'getPBSystemInfo',
			Constant::NOTIFICATION_TYPE_ALIBABA_STORE_AUTH            => 'getPBAliAuthInfo',
			Constant::NOTIFICATION_TYPE_WORKFLOW_SYSTEM_NOTIFY        => 'getPBWorkFlowCustomizeInfo',
			Constant::NOTIFICATION_TYPE_ALIBABA_STORE_EXPIRED_PROCESS => 'getPBAliExpiredProcessInfo',
			Constant::NOTIFICATION_TYPE_ALIBABA_STORE_EXPIRED         => 'getPBAliExpiredInfo',
		];
	}


//	------------------------------------------------------------------------------------------------------------------------------------------------------------------------------


	public function actionList() {

		$req = new PBNotificationListReq();

		$this->buildRequest($req);

		$pageSize = $req->getPageSize() ?: 20;

		$curPage = $req->getCurPage() ?: 1;

		$module = $req->getModule() ?? Constant::MODULE_ALL;

		$user = User::getLoginUser();

		$listObj = new NotificationList($user->getClientId());
		if ($module != Constant::MODULE_ALL) {
			$moduleMap = ConfigHelper::getNotificationModuleSetting($user->getClientId(), $user->getUserId());
			$listObj->setType($moduleMap[$module] ?? null);
		}
		$listObj->setLoginUserId($user->getUserId());
		$listObj->setLimit($pageSize);
		$listObj->setOffset(($curPage - 1) * $pageSize);
		$listObj->getFormatter()->appListInfoSetting();
		$listObj->getFormatter()->setFormatSchedule(false);

		$excludeTypes = Constant::APP_EXCLUDE_TYPE;

		if ($this->requestHeader->getClientVersion() <= '3.8.1') {

			$excludeTypes = array_merge($excludeTypes, [Constant::NOTIFICATION_TYPE_CUSTOMER_ALIBABA_SYNC,
			                                            Constant::NOTIFICATION_TYPE_ALIBABA_STORE_AUTH]);
		}

		$listObj->setExcludeType($excludeTypes);
		$list = [];
		$count = $listObj->count();

		if ($count) {

			$list = $listObj->find();
		}

		$notificationList = [];

		foreach ($list ?? [] as $value) {

			$PBNotificationInfo = $this->getPBNotificationInfo($value);

			$notificationList[] = $PBNotificationInfo;
		}

		$rsp = new PBNotificationListRsp();

		$rsp->setCount($count);

		$rsp->setList($notificationList);

		$this->successPb($rsp);
	}

	public function actionDetail() {

		$req = new PBNotificationDetailReq();

		$this->buildRequest($req);

		$notification_id = $req->getNotificationId();

		$user = User::getLoginUser();
		$clientId = $user->getClientId();
		$userId = $user->getUserId();

		$list = new NotificationList($clientId);
		$list->setLoginUserId($userId);
		$list->setNotificationId($notification_id);
		$list->getFormatter()->appListInfoSetting();
		$list->getFormatter()->setFormatSchedule(false);

		$notification = $list->find();
		if (empty($notification)) {
			throw new RuntimeException(\Yii::t('common', 'Not exist'));
		}
		$operator = new NotificationBatchOperator($userId);
		$operator->setParams([
			'notification_id' => $notification_id,
			'read_flag'       => Notification::READ_FLAG_FALSE,
		]);
		$operator->read();

		$rsp = new PBNotificationDetailRsp();

		$rsp->setData($this->getPBNotificationInfo($notification[0]));

		$this->successPb($rsp);
	}


	/**
	 * @return false|string
	 * @throws CDbException
	 * @throws CException
	 * @throws ProcessException
	 */
	public function actionModuleList() {

		$user = User::getLoginUser();
		$clientId = $user->getClientId();
		$userId = $user->getUserId();

		$module = NotificationHelper::appModuleList($clientId, $userId, true);

		$rsp = new PBNotificationModuleListRsp();

		$list = [];

		foreach ($module as $value) {

			$pbList = new PBNoticeListModuleInfo();

			$pbList->setUnreadCount($value['unread_count']);
			$pbList->setModule($value['module']);
			$pbList->setPushFlag($value['push_flag']);
			$pbList->setTopFlag($value['top_flag']);

			if (is_array($value['last_notification'])) {

				if ($value['module'] == Constant::APP_MODULE_WEEKLY_STATISTIC) {

					continue;

				}

				if ($value['module'] == Constant::APP_MODULE_PRODUCT_UPDATE) {

					$productInfo = $this->getPBNotificationProductList($value['last_notification']);

					$pbList->setProductUpdateInfo($productInfo);

				} else {

					$last = $this->getPBNotificationInfo($value['last_notification']);

					$pbList->setLastNotification($last);
				}
			}

			$list[] = $pbList;
		}

		$rsp->setList($list);

		$this->successPb($rsp);
	}

	public function actionProductUpdateList() {

		$req = new PBNotificationProductUpdateListReq();

		$this->buildRequest($req);

		$cur_page = $req->getCurPage() ?: 1;

		$page_size = $req->getPageSize() ?: 20;

		$list = new ProductUpdateList();

		$list->setPage($cur_page);

		$list->setLimit($page_size);

		[$list, $count] = $list->find();

		$rsp = new PBNotificationProductUpdateListRsp();

		$rsp->setCount($count);

		$productList = [];

		foreach ($list ?? [] as $value) {

			$pbList = $this->getPBNotificationProductList($value);

			$productList[] = $pbList;
		}

		$rsp->setList($productList);

		$this->successPb($rsp);
	}

	public function getPBNotificationProductList($value) {

		$pbList = new PBNotificationProductList();

		$pbList->setId($value['id']);

		$pbList->setProductId($value['product_id']);

		$pbList->setVersion($value['version']);

		$pbList->setPublishedTime(strtotime($value['published_time']));

		$pbList->setDescription($value['description']);

		$pbList->setProductName($value['product_name']);

		$pbList->setTitle($value['title']);

		$pbList->setCreateTime(strtotime($value['create_time']));

		return $pbList;
	}


	public function actionGetPushSetting() {

		$user = User::getLoginUser();

		$clientId = $user->getClientId();

		$userId = $user->getUserId();

		$setting = new UserSetting($clientId, $userId, UserSetting::NOTIFICATION_APP_PUSH_SETTING);

		$rsp = new PBNotificationGetPushSettingRsp();

		$pushSettingList = [];

		foreach ($setting->getValue() ?? [] as $value) {

			$pbList = new PBPushSettingInfo();

			$pbList->setModule($value['module']);

			$pbList->setPushFlag($value['push_flag']);

			$pushSettingList[] = $pbList;
		}

		$rsp->setList($pushSettingList);

		$this->successPb($rsp);
	}


	public function actionUnreadCount() {

		$user = User::getLoginUser();
		$list = new NotificationList($user->getClientId());
		$list->setLoginUserId($user->getUserId());
		$list->setReadFlag(Notification::READ_FLAG_FALSE);
		$unreadCount = intval($list->count());

		$rsp = new PBNoticeListRsp();
		$rsp->setUnreadCount($unreadCount);
		$this->successPb($rsp);
	}
}