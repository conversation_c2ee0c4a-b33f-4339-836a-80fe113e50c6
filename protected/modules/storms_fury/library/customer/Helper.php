<?php

namespace common\modules\storms_fury\library\customer;

use AliPay\Exception;
use common\library\custom_field\company_field\Field;
use common\library\custom_field\CustomFieldService;
use common\library\customer\business_card\BusinessCard;
use common\library\customer\business_card\BusinessCardList;
use common\library\customer_v3\company\orm\Company;
use common\library\customer\service\CustomerRemarkService;
use common\library\setting\library\origin\Origin;
use common\library\setting\library\origin\OriginApi;
use common\library\trail\TrailConstants;
use common\library\util\TelUtil;
use common\modules\app\components\AppInfoService;
use protobuf\CRMCommon\PBFieldItem;
use protobuf\CRMCommon\PBFileInfo;
use protobuf\CRMCommon\PBStoreInfo;
use protobuf\Customer\PBBusinessCardSummaryInfo;
use protobuf\Customer\PBCompanyCardInfo;
use protobuf\Customer\PBCompanyDetailInfo;
use protobuf\Customer\PBCompanyFields;
use protobuf\Customer\PBCustomerCardInfo;
use protobuf\Customer\PBCustomerCategoryItem;
use protobuf\Customer\PBCustomerContact;
use protobuf\Customer\PBCustomerContactInfo;
use protobuf\Customer\PBCustomerContactMessageBodyAudioInfo;
use protobuf\Customer\PBCustomerContactMessageBodyContactInfo;
use protobuf\Customer\PBCustomerContactMessageBodyFileInfo;
use protobuf\Customer\PBCustomerContactMessageBodyImageInfo;
use protobuf\Customer\PBCustomerContactMessageBodyLocationInfo;
use protobuf\Customer\PBCustomerContactMessageBodyStickerInfo;
use protobuf\Customer\PBCustomerContactMessageBodyTemplateInfo;
use protobuf\Customer\PBCustomerContactMessageBodyTextInfo;
use protobuf\Customer\PBCustomerContactMessageBodyUnsupportedInfo;
use protobuf\Customer\PBCustomerContactMessageBodyVideoInfo;
use protobuf\Customer\PBCustomerDetailInfo;
use protobuf\Customer\PBCustomerFields;
use protobuf\Customer\PBCustomerScore;
use protobuf\Customer\PBImageInfo;
use protobuf\Customer\PBSearchExternalFieldInfo;
use protobuf\Customer\PBTelInfo;
use protobuf\Customer\PBTrailEdmInfo;
use protobuf\Customer\PBTrailInfo;
use protobuf\Customer\PBTrailMailInfo;
use protobuf\Customer\PBTrailOpportunityInfo;
use protobuf\Customer\PBTrailOrderInfo;
use protobuf\Customer\PBTrailPayPhoneInfo;
use protobuf\Customer\PBTrailPiInfo;
use protobuf\Customer\PBTrailQuotationInfo;
use protobuf\Customer\PBTrailRemarkInfo;
use protobuf\Customer\PBTrailSchedule;
use protobuf\Customer\PBFormFieldSystemInfo;
use protobuf\Customer\PBConfigFieldItem;

class Helper
{
    public static function formatCompanyListResponse($companyItem)
    {
            $companyDetailInfo = new PBCompanyDetailInfo();
            $companyDetailInfo->setCompanyId($companyItem['company_id']);
            $companyDetailInfo->setUserId($companyItem['user_id']);
            array_key_exists('name', $companyItem) && $companyDetailInfo->setName($companyItem['name']);
            array_key_exists('short_name', $companyItem) && $companyDetailInfo->setShortName($companyItem['short_name']??"");
            array_key_exists('star', $companyItem) && $companyDetailInfo->setStar($companyItem['star']);
            array_key_exists('trail_status', $companyItem) && $companyDetailInfo->setTrailStatus($companyItem['trail_status']['status_id']);
            array_key_exists('group_id', $companyItem) && $companyDetailInfo->setGroupId($companyItem['group_id']);
            //score
            $customerScore = new PBCustomerScore();
            $score = $companyItem['score'];
            $customerScore->setCompact($score['compact']);
            $customerScore->setActive($score['active']);
            $customerScore->setStar($score['star']);
            $customerScore->setData($score['data']);
            $customerScore->setTotal($score['total']);
            $companyDetailInfo->setScore($customerScore);

            //tag
            array_key_exists('tag', $companyItem) && $companyDetailInfo->setTag(array_column($companyItem['tag'], 'tag_id'));
            array_key_exists('pool_id', $companyItem) && $companyDetailInfo->setPoolId($companyItem['pool_id']?? -1); //formatter保证了未开通就不返回改字段
            array_key_exists('pin_flag', $companyItem) && $companyDetailInfo->setPinFlag($companyItem['pin_flag']);
            array_key_exists('serial_id', $companyItem) && $companyDetailInfo->setSerialId($companyItem['serial_id']);
            array_key_exists('origin', $companyItem) && $companyDetailInfo->setOrigin(((array)($companyItem['origin_list'] ?? []))[0] ?? 0);
            //categoryIds
            if (array_key_exists('category_ids', $companyItem)) {
                $itemCategoryIds = $companyItem['category_ids'];
                $pbCategoryIds = $companyDetailInfo->getCategoryIds();
                foreach ($itemCategoryIds as $categoryItem)
                {
                    $category = new PBCustomerCategoryItem();
                    $category->setCategoryId($categoryItem);
                    $pbCategoryIds[] = $category;
                }
            }

            array_key_exists('biz_type', $companyItem) && $companyDetailInfo->setBizType($companyItem['biz_type']);
            array_key_exists('timezone', $companyItem) && $companyDetailInfo->setTimezone($companyItem['timezone']);
            array_key_exists('country', $companyItem) && $companyDetailInfo->setCountry($companyItem['country']);
            array_key_exists('province', $companyItem) && $companyDetailInfo->setProvince($companyItem['province']);
            array_key_exists('city', $companyItem) && $companyDetailInfo->setCity($companyItem['city']);
            array_key_exists('scale_id', $companyItem) && $companyDetailInfo->setScaleId($companyItem['scale_id']);
            array_key_exists('homepage', $companyItem) && $companyDetailInfo->setHomepage($companyItem['homepage']);
            array_key_exists('fax', $companyItem) && $companyDetailInfo->setFax($companyItem['fax']);
            array_key_exists('tel', $companyItem) && $companyDetailInfo->setTel($companyItem['tel']);
            array_key_exists('tel_area_code', $companyItem) && $companyDetailInfo->setTelAreaCode($companyItem['tel_area_code']);
            array_key_exists('address', $companyItem) && $companyDetailInfo->setAddress($companyItem['address']);
            array_key_exists('remark', $companyItem) && $companyDetailInfo->setRemark($companyItem['remark']??'');
	        array_key_exists('annual_procurement', $companyItem) && $companyDetailInfo->setAnnualProcurement($companyItem['annual_procurement']??0);
	        array_key_exists('intention_level', $companyItem) && $companyDetailInfo->setIntentionLevel($companyItem['intention_level']??0);
            array_key_exists('next_follow_up_time', $companyItem) && $companyDetailInfo->setNextFollowUpTime($companyItem['next_follow_up_time']??'');
	        array_key_exists('release_count', $companyItem) && $companyDetailInfo->setReleaseCount($companyItem['release_count'] ?? 0);
	        array_key_exists('private_time', $companyItem) && $companyDetailInfo->setPrivateTime($companyItem['private_time'] ?? '');
	        array_key_exists('public_time', $companyItem) && $companyDetailInfo->setPublicTime($companyItem['public_time'] ?? '');
	        array_key_exists('recent_follow_up_time', $companyItem) && $companyDetailInfo->setRecentFollowUpTime($companyItem['recent_follow_up_time'] ?? '');
	        array_key_exists('edit_time', $companyItem) && $companyDetailInfo->setEditTime($companyItem['edit_time'] ?? '');
	        array_key_exists('last_edit_user', $companyItem) && $companyDetailInfo->setLastEditUser($companyItem['last_edit_user']['user_id'] ?? 0);
	        array_key_exists('create_user', $companyItem) && $companyDetailInfo->setCreateUser($companyItem['create_user']['user_id'] ?? 0);

		if (array_key_exists('alibaba_store_info', $companyItem)) {

			$storeInfo = [];

			foreach ($companyItem['alibaba_store_info'] as $k => $v) {

				$PBStoreInfo = new PBStoreInfo();

				$PBStoreInfo->setStoreId($v['store_id']);

				$PBStoreInfo->setStoreName($v['store_name']);

				$storeInfo[] = $PBStoreInfo;
			}

			$companyDetailInfo->setAlibabaStoreInfo($storeInfo);
		}

            //imageList
            if (array_key_exists('image_list', $companyItem)) {
                $itemImageList = $companyItem['image_list'];
                $pbImageList = $companyDetailInfo->getImageList();
                foreach ($itemImageList as $imageItem)
                {
                    $pbImage = new PBFileInfo();
                    $pbImage->setFileId($imageItem['file_id']);
                    $pbImage->setFileUrl($imageItem['file_path']);
                    $pbImage->setFileName($imageItem['file_name']);
                    $pbImage->setFileSize($imageItem['file_size']);
                    $pbImageList[] = $pbImage;
                }
                $companyDetailInfo->setImageList($pbImageList);
            }

            array_key_exists('create_time', $companyItem) && $companyDetailInfo->setCreateTime($companyItem['create_time']);
            array_key_exists('order_time', $companyItem) && $companyDetailInfo->setOrderTime($companyItem['order_time']);
            array_key_exists('archive_time', $companyItem) && $companyDetailInfo->setArchiveTime($companyItem['archive_time']);
            array_key_exists('archive_type', $companyItem) && $companyDetailInfo->setArchiveType($companyItem['archive_type']);
            $companyDetailInfo->setLastOwner($companyItem['last_owner_info']['user_id'] ?? 0);

            array_key_exists('deal_time', $companyItem) && $companyDetailInfo->setDealTime($companyItem['deal_time']??"");
            $companyDetailInfo->setPerformanceOrderCount($companyItem['performance_order_count']??0);
            $companyDetailInfo->setSuccessOpportunityCount($companyItem['success_opportunity_count']??0);


            //Customer最近联系人
            if (!empty($companyItem['customer'])) {
                $mainCustomerInfo = $companyItem['customer'];
                $pbCustomerDetailInfo = new PBCustomerDetailInfo();
                array_key_exists('customer_id', $mainCustomerInfo) &&$pbCustomerDetailInfo->setCustomerId($mainCustomerInfo['customer_id']);
                array_key_exists('company_id', $mainCustomerInfo) &&$pbCustomerDetailInfo->setCompanyId($mainCustomerInfo['company_id']);
                array_key_exists('name', $mainCustomerInfo) &&$pbCustomerDetailInfo->setName($mainCustomerInfo['name']);
                array_key_exists('email', $mainCustomerInfo) &&$pbCustomerDetailInfo->setEmail($mainCustomerInfo['email']);
                array_key_exists('birth', $mainCustomerInfo) && $pbCustomerDetailInfo->setBirth($mainCustomerInfo['birth'] ?? '');
                array_key_exists('gender', $mainCustomerInfo) && $pbCustomerDetailInfo->setGender($mainCustomerInfo['gender'] ?? 0);
                array_key_exists('remark', $mainCustomerInfo) && $pbCustomerDetailInfo->setRemark($mainCustomerInfo['remark'] ?? '');
                array_key_exists('post_grade', $mainCustomerInfo) && $pbCustomerDetailInfo->setPostGrade($mainCustomerInfo['post_grade'] ?? '');
                array_key_exists('post', $mainCustomerInfo) &&$pbCustomerDetailInfo->setPost($mainCustomerInfo['post']);
                //Customer tel_list
                if (array_key_exists('tel_list', $mainCustomerInfo)) {
                    $pbTelList = $pbCustomerDetailInfo->getTelList();
                    foreach ($mainCustomerInfo['tel_list'] ?? [] as $telInfoItem)
                    {
                        $pbTelInfo = new PBTelInfo();
                        $pbTelInfo->setAreaCode($telInfoItem[0]);
                        $pbTelInfo->setTel($telInfoItem[1]);
                        $pbTelList[] = $pbTelInfo;
                    }
                    $pbCustomerDetailInfo->setTelList($pbTelList);
                }

                //Customer contact
                if (array_key_exists('contact', $mainCustomerInfo)) {
                    $pbContactList = $pbCustomerDetailInfo->getContact();
                    if (is_array($mainCustomerInfo['contact'])) {
                        foreach ($mainCustomerInfo['contact'] as $contactInfoItem)
                        {
                            $pbContact = new PBCustomerContact();
                            $pbContact->setType($contactInfoItem['type']??0);
                            $contactItemValue = $contactInfoItem['value'] ?? '';
                            if (is_array($contactItemValue)) $contactItemValue = json_encode($contactItemValue);
                            $pbContact->setValue($contactItemValue);
                            $pbContactList[] = $pbContact;
                        }
                    }
                    $pbCustomerDetailInfo->setContact($pbContactList);
                }

                if (array_key_exists('image_list', $mainCustomerInfo)) {
                    $customerImageList = $mainCustomerInfo['image_list'] ?? [];
                    if ($customerImageList) {
                        $pbCustomerImageList = $pbCustomerDetailInfo->getImageList();
                        foreach ($customerImageList as $customerImageItem) {
                            if (!is_array($customerImageItem) || !isset($customerImageItem['file_id'])) {
                                continue;
                            }
                            $imageInfo = new PBFileInfo();
                            $imageInfo->setFileId($customerImageItem['file_id']);
                            $imageInfo->setFileName($customerImageItem['file_name']);
                            $imageInfo->setFileSize($customerImageItem['file_size']);
                            $imageInfo->setFileUrl($customerImageItem['file_path']);
                            $pbCustomerImageList[] = $imageInfo;
                        }
                        $pbCustomerDetailInfo->setImageList($pbCustomerImageList);
                    }
                }

                //customer字段
                $customerExternalFieldData =  $mainCustomerInfo['external_field_data'] ?? [];
                if ($customerExternalFieldData) {
                    $pbCustomerExternalFieldData = $pbCustomerDetailInfo->getExternalFieldData();
                    foreach ($customerExternalFieldData as $item) {
                        $id = $item['id'];
                        $value = json_encode($item['value']);
                        $pbCustomerExternalFieldData[$id] = $value;
                    }
                    $pbCustomerDetailInfo->setExternalFieldData($pbCustomerExternalFieldData);
                }


                $pbCustomerDetailInfo->setMainCustomerFlag($mainCustomerInfo['main_customer_flag']);
                $companyDetailInfo->setCustomer($pbCustomerDetailInfo);
            }

            //额外字段
            $externalFieldData = $companyItem['external_field_data'] ?? [];
            if ($externalFieldData) {
                $pbExternalFieldData = $companyDetailInfo->getExternalFieldData();
                foreach ($externalFieldData as $item) {
                    $id = $item['id'];
                    $value = json_encode($item['value']);
                    $pbExternalFieldData[$id] = $value;
                }
                $companyDetailInfo->setExternalFieldData($pbExternalFieldData);
            }

            //最近动态
            if (!empty($companyItem['last_trail'])) {
                $lastTrail = $companyItem['last_trail'];
                $referId = (int)$lastTrail['refer_id'];
                $module = (int)$lastTrail['module'];
                $trailInfo = new PBTrailInfo();
                $trailInfo->setTrailId((int)$lastTrail['trail_id']);
                $trailInfo->setReferId($referId);
                $trailInfo->setModule($module);
                $trailInfo->setType($lastTrail['type']);
                $trailInfo->setCreateUser($lastTrail['create_user']);
                $trailInfo->setCreateTime($lastTrail['create_time']);
                $trailInfo->setDeleteFlag((int)($lastTrail['delete_flag'] ?? 0));
                $trailInfo->setNodeTypeName($lastTrail['node_type_name'] ?? '');


                $data = $lastTrail['data'] ?? [];

                //针对module为1(快速跟进) 但是type为102(快速跟进邮件)，视作邮件类型(module=2)处理
                if ($lastTrail['type'] == TrailConstants::TYPE_REMARK_RELATE_MAIL) {
                    $lastTrail['module'] = TrailConstants::MODULE_MAIL;
                }

                switch ($module) {
                    //跟进:
                    case TrailConstants::MODULE_REMARK:

                        //日程
                        if ($lastTrail['type']==TrailConstants::TYPE_REMARK_SCHEDULE) {
                            $trailInfo->setNodeTypeName($lastTrail['node_type_name'] ?? '');
                            break;
                        }

                        $remarkInfo = new PBTrailRemarkInfo();
                        $remarkInfo->setContent($data['plain_content'] ?? '');
                        $trailInfo->setRemarkInfo($remarkInfo);
                        break;
                    //邮件
                    case TrailConstants::MODULE_MAIL:
                        $mailInfo = new PBTrailMailInfo();
                        $mailInfo->setSubject($data['subject'] ?? '');
                        $mailInfo->setIsYours($data['is_yours'] ??  false);
                        $mailInfo->setMailId($referId);
                        $trailInfo->setMailInfo($mailInfo);
                        break;
                    //EDMs
                    case TrailConstants::MODULE_EDM:
                        $edmInfo = new PBTrailEdmInfo();
                        $edmInfo->setStatus($data['status'] ?? 0);
                        $edmInfo->setSubject($data['subject'] ?? '');
                        $edmInfo->setTaskId($referId);
                        $trailInfo->setEdmInfo($edmInfo);
                        break;
                    //报价单
                    case TrailConstants::MODULE_QUOTATION:
                        $quotationInfo = new PBTrailQuotationInfo();
                        $quotationInfo->setQuotationId($data['quotation_id'] ?? 0);
                        $quotationInfo->setQuotationName($data['quotation_name'] ?? '');
                        $trailInfo->setQuotationInfo($quotationInfo);
                        break;
                    //PI动态
                    case TrailConstants::MODULE_PI:
                        $piInfo = new PBTrailPiInfo();
                        $piInfo->setPiId($data['pi_id']);
                        $piInfo->setPiName($data['pi_name']);
                        $trailInfo->setPiInfo($piInfo);
                        break;
                    //订单
                    case TrailConstants::MODULE_ORDER:
                        $orderInfo = new PBTrailOrderInfo();
                        $orderInfo->setOrderId($data['order_id'] ?? 0);
                        $orderInfo->setOrderName($data['order_name'] ?? '');
                        $trailInfo->setOrderInfo($orderInfo);
                        break;
                    //公费电话
                    case TrailConstants::MODULE_PAY_CALL:
                        $payPhoneInfo = new PBTrailPayPhoneInfo();
                        $payPhoneInfo->setCallId(intval($data['call_id'] ?? 0));
                        $payPhoneInfo->setFileExt($data['file_ext'] ?? '');
                        $payPhoneInfo->setRecordUrl($data['record_url'] ?? '');
                        $payPhoneInfo->setSeconds($data['seconds'] ?? '');
                        $payPhoneInfo->setTargetTel($data['target_tel'] ?? '');
                        $payPhoneInfo->setType($data['type'] ?? '');
                        $trailInfo->setPayPhoneInfo($payPhoneInfo);
                        break;
                    case TrailConstants::MODULE_OPPORTUNITY:
                        $opportunityInfo = new PBTrailOpportunityInfo();
                        $opportunityInfo->setOpportunityId($lastTrail['opportunity_id'] ?? 0);
                        $opportunityInfo->setContent($data['content'] ?? '');
                        $trailInfo->setOpportunityInfo($opportunityInfo);
                        break;
                    default:
                        break;
                }
                $companyDetailInfo->setLastTrailInfo($trailInfo);
            }

            //最近跟进动态
        if (!empty($companyItem['last_remark_trail'])) {
            $lastRemarkTrail = $companyItem['last_remark_trail'];
            $referId = (int)$lastRemarkTrail['refer_id'];
            $module = (int)$lastRemarkTrail['module'];
            $trailInfo = new PBTrailInfo();
            $trailInfo->setTrailId((int)$lastRemarkTrail['trail_id']);
            $trailInfo->setReferId($referId);
            $trailInfo->setModule($module);
            $trailInfo->setType($lastRemarkTrail['type']);
            $trailInfo->setCreateUser($lastRemarkTrail['create_user']);
            $trailInfo->setCreateTime($lastRemarkTrail['create_time']);
            $trailInfo->setDeleteFlag((int)($lastRemarkTrail['delete_flag'] ?? 0));

            $data = $lastRemarkTrail['data'] ?? [];

            //针对module为1(快速跟进) 但是type为102(快速跟进邮件)，视作邮件类型(module=2)处理
            if ($lastRemarkTrail['type'] == TrailConstants::TYPE_REMARK_RELATE_MAIL) {
                $lastRemarkTrail['module'] = TrailConstants::MODULE_MAIL;
            }

            switch ($module) {
                //跟进:
                case TrailConstants::MODULE_REMARK:
                    //日程
                    if ($lastRemarkTrail['type']==TrailConstants::TYPE_REMARK_SCHEDULE) {
                        $scheduleInfo = new PBTrailSchedule();
                        $scheduleInfo->setScheduleId($lastRemarkTrail['schedule_id']??0);
                        $scheduleInfo->setTitle($data['title'] ?? '');
                        $scheduleInfo->setRemark($data['remark'] ?? '');
                        $trailInfo->setScheduleInfo($scheduleInfo);
                        break;
                    }
                    $remarkInfo = new PBTrailRemarkInfo();
                    $remarkInfo->setContent($data['plain_content'] ?? '');
                    $trailInfo->setRemarkInfo($remarkInfo);
                    break;
                //邮件
                case TrailConstants::MODULE_MAIL:
                    $mailInfo = new PBTrailMailInfo();
                    $mailInfo->setSubject($data['subject'] ?? '');
                    $mailInfo->setIsYours($data['is_yours'] ??  false);
                    $mailInfo->setMailId($referId);
                    $trailInfo->setMailInfo($mailInfo);
                    break;
                //EDMs
                case TrailConstants::MODULE_EDM:
                    $edmInfo = new PBTrailEdmInfo();
                    $edmInfo->setStatus($data['status'] ?? 0);
                    $edmInfo->setSubject($data['subject'] ?? '');
                    $edmInfo->setTaskId($referId);
                    $trailInfo->setEdmInfo($edmInfo);
                    break;
                //报价单
                case TrailConstants::MODULE_QUOTATION:
                    $quotationInfo = new PBTrailQuotationInfo();
                    $quotationInfo->setQuotationId($data['quotation_id'] ?? 0);
                    $quotationInfo->setQuotationName($data['quotation_name'] ?? '');
                    $trailInfo->setQuotationInfo($quotationInfo);
                    break;
                //PI动态
                case TrailConstants::MODULE_PI:
                    $piInfo = new PBTrailPiInfo();
                    $piInfo->setPiId($data['pi_id'] ?? 0);
                    $piInfo->setPiName($data['pi_name'] ?? '');
                    $trailInfo->setPiInfo($piInfo);
                    break;
                //订单
                case TrailConstants::MODULE_ORDER:
                    $orderInfo = new PBTrailOrderInfo();
                    $orderInfo->setOrderId($data['order_id'] ?? 0);
                    $orderInfo->setOrderName($data['order_name'] ?? '');
                    $trailInfo->setOrderInfo($orderInfo);
                    break;
                //公费电话
                case TrailConstants::MODULE_PAY_CALL:
                    $payPhoneInfo = new PBTrailPayPhoneInfo();
                    $payPhoneInfo->setCallId(intval($data['call_id'] ?? 0));
                    $payPhoneInfo->setFileExt($data['file_ext'] ?? '');
                    $payPhoneInfo->setRecordUrl($data['record_url'] ?? '');
                    $payPhoneInfo->setSeconds($data['seconds'] ?? '');
                    $payPhoneInfo->setTargetTel($data['target_tel'] ?? '');
                    $payPhoneInfo->setType($data['type'] ?? '');
                    $trailInfo->setPayPhoneInfo($payPhoneInfo);
                    break;
                case TrailConstants::MODULE_OPPORTUNITY:
                    $opportunityInfo = new PBTrailOpportunityInfo();
                    $opportunityInfo->setOpportunityId($lastRemarkTrail['opportunity_id'] ?? 0);
                    $opportunityInfo->setContent($data['content'] ?? '');
                    $trailInfo->setOpportunityInfo($opportunityInfo);
                    break;
                default:
                    break;
            }
            $companyDetailInfo->setLastRemarkTrail($trailInfo);
        }

        return $companyDetailInfo;
    }

    /**
     * 将 PBSearchExternalFieldInfo 迭代对象 格式化成 数组
     * @param PBSearchExternalFieldInfo[] $fieldIter
     */
    public static function formatPBSearchExternalFieldInfoToArray(array $fieldIter)
    {
        $fieldArray = [];
        foreach ($fieldIter as $item) {
            if ($item->getKeyword() && $item->getKeyword() != '[]') {
                if (json_decode($item->getKeyword(), true)) {
                    $keyword = json_decode($item->getKeyword(), true);
                } else {
                    $keyword = $item->getKeyword();
                }
                $keyword = str_replace(array("↵"), "\n", $keyword);
                $fieldArray[] = [
                    'field_id' => $item->getFieldId(),
                    'keyword' => $keyword,
                    'match_type' => $item->getMatchType(),
                ];
            }
        }
        return $fieldArray;
    }

    public static function formatContactCompanyListResponse($companyItem, $key = 'latest_whatsapp_message')
    {
        $info = new \protobuf\Customer\PBCompanyWithContactMessageInfo();
        $info->setName($companyItem['name']);
        $info->setUserId($companyItem['user_id']);
        $info->setCompanyId($companyItem['company_id']);
        $info->setLatestWhatsappTime($companyItem['latest_whatsapp_time']);
        $info->setLatestWhatsappReceiveTime($companyItem['latest_whatsapp_receive_time']);
        $info->setLastWhatsappMessage(self::formatCustomerContactMessage($companyItem[$key] ?? []));
        return $info;
    }

    public static function formatCustomerContactMessage($messageItem)
    {
        $message = new \protobuf\Customer\PBCustomerContactMessageInfo();
        if (empty($messageItem)) {
            return $message;
        }
        $message->setSessionId($messageItem['user_contact_id']);
        $message->setId($messageItem['id']);
        $message->setMessageId($messageItem['message_id']);
        $message->setSendTime($messageItem['send_time']);
        $message->setSendType($messageItem['send_type']);
        $message->setType($messageItem['type']);
        $message->setSnsId($messageItem['sns_id'] ?? '');
        $message->setSnsAvatar($messageItem['sns_avatar'] ?? '');
        $message->setSnsNickname($messageItem['sns_nickname'] ?? '');
        $link = $messageItem['body']['link'] ?? [];
        $link = is_array($link) ? $link : [$link];
        switch (strtolower($messageItem['type'])) {
            case 'text':
                $body = new PBCustomerContactMessageBodyTextInfo();
                $body->setContent($messageItem['body'] ?? '');
                $message->setText($body);
                break;
            case 'image':
                $body = new PBCustomerContactMessageBodyImageInfo();
                $body->setLink($link);
                $message->setImage($body);
                break;
            case 'audio':
                $body = new PBCustomerContactMessageBodyAudioInfo();
                $body->setLink($link);
                $message->setAudio($body);
                break;
            case 'video':
                $body = new PBCustomerContactMessageBodyVideoInfo();
                $body->setLink($link);
                $message->setVideo($body);
                break;
            case 'file':
            case 'document':
                $messageItem['body'] = is_string($messageItem['body']) ? json_decode($messageItem['body'], true) : $messageItem['body'];
                $body = new PBCustomerContactMessageBodyFileInfo();
                $body->setFileName($messageItem['body']['fileName'] ?? '');
                $body->setFileSize($messageItem['body']['fileSize'] ?? '');
                $message->setFile($body);
                break;
            case 'contact':
                $messageItem['body'] = is_string($messageItem['body']) ? json_decode($messageItem['body'], true) : $messageItem['body'];
                $body = new PBCustomerContactMessageBodyContactInfo();
				$body->setInfo(json_encode($messageItem['body']));
                $message->setContact($body);
                break;
            case 'sticker':
                $body = new PBCustomerContactMessageBodyStickerInfo();
                $body->setLink($link);
                $message->setSticker($body);
                break;
            case 'location':
                $messageItem['body'] = is_string($messageItem['body']) ? json_decode($messageItem['body'], true) : $messageItem['body'];
                $body = new PBCustomerContactMessageBodyLocationInfo();
                $body->setAddress($messageItem['body']['address'] ?? '');
                $body->setLatitude($messageItem['body']['latitude'] ?? 0);
                $body->setLongitude($messageItem['body']['longitude'] ?? 0);
                $body->setName($messageItem['body']['name'] ?? '');
                $message->setLocation($body);
                break;
            case 'template':
                $messageItem['body'] = is_string($messageItem['body']) ? json_decode($messageItem['body'], true) : $messageItem['body'];
                $body = new PBCustomerContactMessageBodyTemplateInfo();
                $body->setStruct(json_encode($messageItem['body']['struct']));
                $body->setData(json_encode($messageItem['body']['data']));
                $message->setTemplate($body);
                break;
            default:
                $messageItem['body'] = is_string($messageItem['body']) ? json_decode($messageItem['body'], true) : $messageItem['body'];
                $body_data = $messageItem['body']['data'] ?? '';
                $body = new PBCustomerContactMessageBodyUnsupportedInfo();
                $body->setData(json_encode($body_data));
                $message->setUnsupported($body);
                break;
        }

        return $message;
    }

    public static function formatCustomerContact($contactItem)
    {
        $contact = new PBCustomerContactInfo();
        $contact->setSessionId($contactItem['user_contact_id']);
        $contact->setCompanyId($contactItem['company_id']);
        $contact->setCustomerId($contactItem['customer_id'] ?? 0);
        $contact->setSnsType($contactItem['sns_type']);
        $contact->setSnsId($contactItem['sns_id']);
        $contact->setSnsNickname($contactItem['sns_nickname']);
        $contact->setSnsAvatar($contactItem['sns_avatar']);
        $contact->setUserSnsId($contactItem['user_sns_id']);
        $contact->setUserSnsNickname($contactItem['user_sns_nickname']);
        $contact->setUserNickname($contactItem['user_nickname']);
        $contact->setUserSnsAvatar($contactItem['user_sns_avatar']);
        $contact->setMessageSyncCount($contactItem['message_sync_count']);
        $contact->setMessageSyncTime($contactItem['message_sync_time']);
        $contact->setLastMessage(self::formatCustomerContactMessage($contactItem['last_message'] ?? []));

        return $contact;
    }


    public static function formatBusinessCardRsp($businessCardItem)
    {
        $businessCardSummary = new PBBusinessCardSummaryInfo();
        $companyCardInfo = new PBCompanyCardInfo();
        $customerCardInfo = new PBCustomerCardInfo();

        $companyCardInfo->setName($businessCardItem['name']);
        $customerCardInfo->setName($businessCardItem['customer_name']);
        $customerCardInfo->setPost($businessCardItem['customer_post']);
        $businessCardSummary->setCreateTime($businessCardItem['create_time']);
        $businessCardSummary->setIsArchive($businessCardItem['is_archive']);
        $businessCardSummary->setBusinessCardId($businessCardItem['business_card_id']);
        $businessCardSummary->setErasable($businessCardItem['erasable']);
        $businessCardSummary->setTransferable($businessCardItem['transferable']);
        $businessCardSummary->setTransferable($businessCardItem['archivable']);

        $image = new PBImageInfo();
        $image->setId($businessCardItem['file_id']);
        $image->setUrl($businessCardItem['file_path']);
        $back_image = new PBImageInfo();
        $back_image->setId($businessCardItem['back_image_file_id']);
        $back_image->setUrl($businessCardItem['back_image_file_path']);

        $businessCardSummary->setImage($image);
        $businessCardSummary->setBackImage($back_image);
        $businessCardSummary->setCompanyCard($companyCardInfo);
        $businessCardSummary->setCustomerCard($customerCardInfo);

        return $businessCardSummary;
    }


    public static function formatBusinessCardFieldFormRsp($fieldsForm, &$objectFields, $vcard_flag = false, $clientId = 0)
    {
        $phoneNumberUtil = \libphonenumber\PhoneNumberUtil::getInstance();
        $result = [];
        $customerFlag = false;//多个邮箱会产生多个fieldItem,名片需要过滤多个，只需要返回一个
        foreach ($fieldsForm as $group){

            if($customerFlag){
                break;
            }

            $flag = AppInfoService::versionGreater('5.7.5');
            $temp = BusinessCard::FORM_FIELDS;
            if(!$flag){
                $temp = array_values(array_diff($temp, ['origin_list']));
            }
            foreach ($group['fields'] as $field){
                if(!in_array($field['id'], $temp) || in_array($field['id'], BusinessCard::EXCLUDE_FIELD[$field['type']] ?? [])){
                    continue;
                }

                if($field['id'] == 'tel' && !empty($field['value'])){
                    if ($vcard_flag) {
                        if (TelUtil::IsChinesePhone($field['value']['tel'])) {
                            $field['value']['area_code'] = '';
                        } else {
                            try {
                                $phoneNumber = $phoneNumberUtil->parse("+" . ltrim($field['value']['tel'], "+"));
                                $field['value']['tel'] = $phoneNumber->getNationalNumber();
                                $field['value']['area_code'] = $phoneNumber->getCountryCode();
                            } catch (\Exception $e) {
                                //第三方解析报错兼容
                                $field['value']['area_code'] = '';
                                \LogUtil::warning($e->getMessage() . ' tel = ' . json_encode($field['value']));
                            }
                        }
                    } else {
                        $field['value']['area_code'] = $field['value']['tel_area_code'];
                    }
                    unset($field['value']['tel_area_code']);
                    $field['format'] = json_encode($field['value']);
                }

                if($field['id'] == 'tel_list' && !empty($field['value']) && $vcard_flag){
                    $telList = $field['value'];
                    foreach ($telList as &$tel) {

                        if (empty($tel[0])) {
                            continue;
                        }

                        if (TelUtil::IsChinesePhone($tel[0])) {
                            //优先匹配中国手机号
                            $tel = ['86', $tel[0]];

                        } else {

                            try {
                                $phoneNumber = $phoneNumberUtil->parse("+" . ltrim($tel[0], "+"));
                                $tel = [$phoneNumber->getCountryCode(), $phoneNumber->getNationalNumber()];
                            } catch (\Exception $e) {
                                //第三方解析报错兼容
                                $tel = ['', $tel[0]];

                                \LogUtil::warning($e->getMessage() . ' tel_list = ' . json_encode($field['value']));
                                continue;
                            }

                        }
                    }
                    $field['value'] = json_encode($telList);
                    $field['format'] = json_encode($telList);
                }

                if($field['id'] == 'email'){
                    $customerFlag = true;
                    $emails = array_column($fieldsForm,'email') ?? [];
                    $field['value'] = json_encode($emails);
                    $field['format'] = json_encode($emails);
                }

                if($field['id'] == 'contact' && !empty($field['value'])){
                    foreach ($field['value'] as &$item){
                        $item['website'] = BusinessCard::OFFICIAL_WEBSITE[$item['type']] ?? '';
                    }

                    $field['format'] = is_array($field['value']) ? json_encode($field['value']) : (string) $field['value'];
                }
                if ((BusinessCard::BUSINESS_KEY_MAP[$field['id']] ?? $field['id']) == 'origin' && $vcard_flag) {
                    //新增来源
                    $field['value'] = ['origin_id' => Origin::SYS_ORIGIN_EXHIBITION, 'origin_name' => \Yii::t('field', '展会')];
                }


                $fieldItem = new PBFieldItem();
                $fieldItem->setId(BusinessCard::BUSINESS_KEY_MAP[$field['id']] ?? $field['id']);
                $fieldItem->setName($field['name']);
                $fieldItem->setFieldType(BusinessCard::FIELD_FIELD_TYPE_MAP[$field['id']] ?? $field['field_type']);
                $fieldItem->setFormat($field['format'] ?? '');
                $fieldItem->setValue(is_array($field['value']) ? json_encode($field['value']) : (string) $field['value']);
                $result[] = $fieldItem;
            }
        }
        $objectFields->setField($result);
    }

    public static function formatBusinessCardField($fields, &$objectFields)
    {
        $result = [];
        foreach ($fields as $field){
            $fieldItem = new PBFieldItem();
            $fieldItem->setId(BusinessCard::BUSINESS_KEY_MAP[$field['id']] ?? $field['id']);
            $fieldItem->setName($field['name']);
            $fieldItem->setFieldType(BusinessCard::FIELD_FIELD_TYPE_MAP[$field['id']] ?? $field['field_type']);
            $fieldItem->setValue(is_array($field['value']) ? json_encode($field['value']) : (string) $field['value']);
            $fieldItem->setFormat(is_array($field['value']) ? json_encode($field['value']) : (string) $field['value']);
            $result[] = $fieldItem;
        }
        $objectFields->setField($result);
    }

    public static function formatBackgroundFieldFormRsp($fieldsForm,$industry ='', $vcard_flag = false)
    {
        $phoneNumberUtil = \libphonenumber\PhoneNumberUtil::getInstance();
        $result = [];
        $fieldItem = new PBFieldItem();
        $fieldItem->setId('industry');
        $fieldItem->setName(\Yii::t('common', 'Industry'));
        $fieldItem->setFieldType(CustomFieldService::FIELD_TYPE_TEXT);
        $fieldItem->setFormat( $industry);
        $fieldItem->setValue( $industry);
        $result[] = $fieldItem;
        foreach ($fieldsForm as $group){
            foreach ($group['fields'] as $field){
                if(!in_array($field['id'], [
                    'industry',
                    'address',
                    'homepage',
                    'tel',
                ])){
                    continue;
                }

                if($field['id'] == 'tel' && !empty($field['value']) && $vcard_flag){
                    try{
                        $phoneNumber = $phoneNumberUtil->parse($field['value']['tel']);
                        $phoneNumber = '';
                    }catch (\Exception $e){
                        continue;
                    }
                    $field['value']['tel'] = $phoneNumber->getNationalNumber();
                    $field['value']['tel_area_code'] = $phoneNumber->getCountryCode();
                }

                if($field['id'] == 'homepage' && !empty($field['value'])){
                    //死链
					$domain = parse_url($field['value'])['host'] ?? '';
                    $req = new \Infra\Unifiedspider\Core\DomainRequest();
                    $req->setDomain($domain);
                    $req->setTimeout( new \Google\Protobuf\Duration(['seconds'=> 2]));

                    $client = \common\library\api\GRPCClientFactory::DomainServiceClient();
                    list($rsp, $status) = $client->GetDomainStatus($req)->wait();

                    $status = \Infra\Unifiedspider\Core\DomainStatus::name($rsp->getDomainStatus());
					if (!in_array(\Infra\Unifiedspider\Core\DomainStatus::value($status), [\Infra\Unifiedspider\Core\DomainStatus::VALID, \Infra\Unifiedspider\Core\DomainStatus::REDIRECT_OTHER])) {
						$field['format'] = '';
					}

                }



                $fieldItem = new PBFieldItem();
                $fieldItem->setId($field['id']);
                $fieldItem->setName($field['name']);
                $fieldItem->setFieldType($field['field_type']);
                $fieldItem->setFormat($field['format'] ?? '');
                $fieldItem->setValue(json_encode($field['value']) ?? '');
                $result[] = $fieldItem;
            }
        }

        return $result;
    }

    public static function formatTrailStatus($trailStatusInfo)
    {
        $trailStatus = new \protobuf\Customer\PBCustomerStatusInfo();
        $trailStatus->setStatusId($trailStatusInfo['status_id']??0);
        $trailStatus->setStatusName($trailStatusInfo['status_name']??'');
        $trailStatus->setStatusColor($trailStatusInfo['status_color']??'');
        return $trailStatus;
    }

    public static function formatCustomerScore($scoreInfo)
    {
        $score = new PBCustomerScore();
        $score->setStar($scoreInfo['star']);
        $score->setActive($scoreInfo['active']);
        $score->setCompact($scoreInfo['compact']);
        $score->setData($scoreInfo['data']);
        $score->setTotal($scoreInfo['total']);
        return $score;
    }

    public static function formatTag($tagInfo)
    {
        $tagItem = new \protobuf\CRMCommon\PBTagInfo();
        $tagItem->setName($tagInfo['tag_name']);
        $tagItem->setTagId($tagInfo['tag_id']);
        $tagItem->setColor($tagInfo['tag_color']);
        return $tagItem;
    }

    public static function formatCustomerDetail($customer)
    {
        $customerInfo = new PBCustomerDetailInfo();
        $customerInfo->setCustomerId($customer['customer_id']??0);
        $customerInfo->setEmail($customer['email']??'');
        $customerInfo->setName($customer['name']??'');
        return $customerInfo;
    }

    public static function formatFieldItem($fieldItem)
    {
        $pbFieldItem = new PBFieldItem();
        $pbFieldItem->setId($fieldItem['id']);
        $pbFieldItem->setBase($fieldItem['base']??0);
        $pbFieldItem->setType($fieldItem['type']??0);
        $pbFieldItem->setName($fieldItem['name']??'');
        $pbFieldItem->setFieldType($fieldItem['field_type']??0);
        $pbFieldItem->setEditRequired($fieldItem['edit_required']??0);
        $pbFieldItem->setDisableFlag($fieldItem['disable_flag']??0);
        $pbFieldItem->setOrder($fieldItem['order']??0);
        $pbFieldItem->setEnableFlag($fieldItem['enable_flag']??0);
        $pbFieldItem->setReadonly($fieldItem['readonly']??0);
        $pbFieldItem->setGroupId($fieldItem['group_id']??0);
        $pbFieldItem->setExtInfo(isset($fieldItem['ext_info']) ? json_encode($fieldItem['ext_info']) : '');
        $pbFieldItem->setRequire($fieldItem['require']??0);
        $pbFieldItem->setValue(is_array($fieldItem['value']) ? json_encode($fieldItem['value']): $fieldItem['value']);
        $pbFieldItem->setIsEditable($fieldItem['is_editable']??false);
        $pbFieldItem->setFormat($fieldItem['format']??'');
        return $pbFieldItem;
    }

    public static function formatFieldSystemInfo($item)
    {
        $systemInfo = new PBFormFieldSystemInfo();

        $systemInfo->setGroupId($item['group_id'] ?? 0);

        $systemInfo->setName($item['name'] ?? '');

        foreach($item['config']??[] as $configItem){
            $configInfo[] = self::formatConfigFieldInfo($configItem);
        }
        $systemInfo->setConfig($configInfo ?? []);

        foreach($item['fields']??[] as $configItem){
            $fieldInfo[] = self::formatFieldItem($configItem);
        }

        $systemInfo->setFields($fieldInfo ?? []);

        return $systemInfo;
    }


    public static function formatConfigFieldInfo($item)
    {
        $configInfo = new PBConfigFieldItem();

        $configInfo->setGroupId($item['group_id'] ?? 0);

        $configInfo->setName($item['name'] ?? '');

        foreach($item['fields']??[] as $configItem){
            $fieldInfo[] = self::formatFieldItem($configItem);
        }
        $configInfo->setFields($fieldInfo ?? []);

        return $configInfo;
    }

	/**
	 * @throws \Exception
	 */
	public static function handleBusinessCardInfo($clientId, $userId, $businessCardId, $companyId, $existedCompanyId=0){

		$businessCard = new BusinessCard($clientId, $businessCardId);

		$businessCard->company_id = $companyId ?? 0;
		$businessCard->is_archive = BusinessCardList::ARCHIVE_TYPE_OK;
		$businessCard->save();
		//名片的速记转为客户的快速记录动态
		$stenographyList = new \common\library\customer\business_card\stenography\StenographyList($clientId, $userId);
		$stenographyList->paramsMapping(['business_card_id' => $businessCardId]);
		$stenographyList->setOrderBy(['update_time']);
		$stenographyList->setOrder('desc');
		$count = $stenographyList->count();
		$stenographyList = $stenographyList->find();
		$content = '';
		$file_ids = [];
		foreach ($stenographyList as $key => $item){
			if (!empty($item['content'])) {
				$content .= ($count - 1 == $key) ? $item['content'] : $item['content'] . "\n";
			}
			$file_ids = array_merge($file_ids, $item['file_ids']);
		}

		if (empty($content) && !empty($file_ids)) {
			$content = '名片速记';
		}

		if(!empty($content)) {

			$file_ids = array_unique($file_ids);
			$remarkService = new CustomerRemarkService($clientId, $userId, $businessCard->company_id);

			$remarkService->setContent($content);

			$remarkService->setRemarkType(TrailConstants::TYPE_REMARK_ADD);

			$company = new Company($clientId, $companyId);

			if ($company->main_customer > 0) $remarkService->setCustomerId($company->main_customer);

			$remarkService->setFileIds($file_ids);

			$remarkService->setRemarkTime(date('Y-m-d H:i:s', time()));

			$remarkService->remark();
		}

        //插入建档日志
        $eventType = $existedCompanyId ? \common\library\customer\Constant::OPERATE_TYPE_ARCHIVE_EXISTED : \common\library\customer\Constant::OPERATE_TYPE_ARCHIVE_NEW;
        (new \common\library\customer\business_card\operateLog\BusinessCardOperateLog)->saveOperateLog(
            $clientId,
            $userId,
            $businessCardId,
            $eventType
        );

	}

    public static function formatBusinessCardSysFieldFormRsp($businessCard, &$systemFields)
    {
        $creator = \User::getUserObject($businessCard['user_id']) ?? null;
        $belonger = \User::getUserObject($businessCard['belonger_user_id']) ?? null;
        $CreatorFields = new PBFieldItem();
        $CreatorFields->setId("user_name");
        $CreatorFields->setName("创建人");
        $CreatorFields->setFieldType(BusinessCard::FIELD_FIELD_TYPE_MAP["user_name"]);
        $CreatorFields->setFormat($creator ? $creator->getNickname() : "");
        $CreatorFields->setValue($creator ? $creator->getNickname() : "");
        $BelongerFields = new PBFieldItem();
        $BelongerFields->setId("belonger_user_name");
        $BelongerFields->setName("归属人");
        $BelongerFields->setFieldType(BusinessCard::FIELD_FIELD_TYPE_MAP["belonger_user_name"]);
        $BelongerFields->setFormat($belonger ? $belonger->getNickname() : "");
        $BelongerFields->setValue($belonger ? $belonger->getNickname() : "");
        $CreateTimeFields = new PBFieldItem();
        $CreateTimeFields->setId("create_time");
        $CreateTimeFields->setName("创建时间");
        $CreateTimeFields->setFieldType(BusinessCard::FIELD_FIELD_TYPE_MAP["create_time"]);
        $CreateTimeFields->setFormat($businessCard['create_time']);
        $CreateTimeFields->setValue($businessCard['create_time']);

        $fields = [
            $CreatorFields, $BelongerFields, $CreateTimeFields
        ];
        $systemFields->setField($fields);

    }
}
