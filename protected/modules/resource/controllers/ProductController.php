<?php
/**
 * Created by PhpStorm.
 * User: Tony
 * Date: 18/6/28
 * Time: 上午10:42
 */

use common\library\custom_field\FieldList;
use common\library\custom_field\CustomFieldFormatter;
use common\library\oms\warehouse\WarehouseFilter;
use common\library\product\ProductList;
use common\library\product_v2\ProductConstant;
use common\models\client\ClientProduct;
use common\library\custom_field\CustomFieldService;
use common\library\oms\inventory\product_inventory\BatchProductInventory;
use common\library\oms\inventory\product_inventory\ProductInventoryFilter;
use common\library\product_v2\ProductFilter;
use common\library\product_v2\sku\ProductSkuFilter;
use \common\library\setting\library\attributes\AttributesApi;
use \common\library\product_v2\sku\SkuAPI;
use \common\library\setting\item\ItemSettingAPI;
use \common\library\setting\library\group\GroupApi;
use xiaoman\orm\database\data\Equal;

class ProductController extends ResourceController
{

    /**
     * 获取产品库设置的数据字段
     *
     * @param $client_id
     * @param $user_id
     */
    public function actionFields($client_id, $user_id)
    {
        User::setLoginUserById($user_id);
        $service = new FieldList($client_id);
        $service->setType(Constants::TYPE_PRODUCT);
        $service->setFormatterType(CustomFieldFormatter::TYPE_COLUMN_MAP2);
        $list = $service->find();
        // 匹配指定某些字段
        $list = $this->mapSpecifyColumns($list);
        $this->success($list);
    }

    /**
     * 获取产品分组列表
     *
     * @param $client_id
     * @param $user_id
     * @throws ProcessException
     */
    public function actionGroups($client_id, $user_id, $group_id=0)
    {
        User::setLoginUserById($user_id);
        $api = new \common\library\setting\library\group\GroupApi($client_id, Constants::TYPE_PRODUCT);
        $data = $api->listAll();

        if (!empty($group_id)) {
            $data = array_filter($data, function($item) use ($group_id) {
                return $item['id'] == $group_id;
            });
        }

        $this->success(array_values($data));
    }

    public function actionPushGroups($client_id, $user_id, $data)
    {
        LogUtil::info("push client_id={$client_id} user_id={$user_id} data={$data}");
        User::setLoginUserById($user_id);
        $requestData = json_decode($data, true);
        $group_id = $requestData['group_id'] ?? 0;
        $name = $requestData['name'] ?? '';
        $description = $requestData['description'] ?? null;
        $parent_id = $requestData['parent_id'] ?? '';
        $rank = $requestData['rank'] ?? null;

        $api = new GroupApi($client_id, Constants::TYPE_PRODUCT);
        if ($group_id) {
            $data = $api->edit($group_id, [
                'item_name' =>  $name,
                'description' => $description,
                'order_rank' => $rank,
                'parent_id'  => $parent_id === '' ? null : $parent_id,
            ]);
            if (!$data) {
                throw new RuntimeException(\Yii::t('customer', 'Group does not exist'));
            }
        } else {
            $group_id = $api->create([
                'item_name' =>  $name,
                'description' => $description,
                'order_rank' => $rank,
                'parent_id'  => $parent_id === '' ? null : $parent_id,
            ]);
            $data = $api->find($group_id);
        }

        $this->success($data);
    }

    /**
     * 产品分组删除
     * @param $client_id\
     * @param $user_id
     * @param $group_id
     * @return false|string
     * @throws ProcessException
     * @throws \xiaoman\orm\exception\OrmException
     * @throws \xiaoman\orm\exception\QueryException
     */
    public function actionRemoveGroups($client_id, $user_id, $group_id)
    {
        User::setLoginUserById($user_id);

        $groupApi = new GroupApi($client_id, Constants::TYPE_PRODUCT);
        if ($groupApi->isSystemSetting($group_id)) {
            throw new RuntimeException('System group cannot be deleted', 404);
        }

        $count = \common\library\customer\Helper::getProductGroupCompanyCount($user_id, $client_id, $group_id);
        if ($count > 0) {
            throw new RuntimeException('group is refer by company, can not delete', 404);
        }
        $groupApi->delete($group_id);
        return $this->success(['group_id' => $group_id]);
    }

    /**
     * 产品规格列表
     * @param $client_id
     * @param $user_id
     * @throws ProcessException
     * @throws \xiaoman\orm\exception\QueryException
     */
    public function actionSkuAttributeList($client_id, $user_id)
    {
        User::setLoginUserById($user_id);
        $api = new AttributesApi($client_id, Constants::TYPE_PRODUCT);
        $api->getParams()
            ->setOrder('layer','asc')
            ->setOrder('order_rank','asc')
            ->setOrder('item_id','desc');
        $list = $api->tree(0);

        $this->success($list);
    }

    /**
     * 产品规格新增或编辑
     * @param $client_id
     * @param $user_id
     * @param $data
     * @return false|string
     * @throws ProcessException
     * @throws Throwable
     * @throws \xiaoman\orm\exception\OrmException
     * @throws \xiaoman\orm\exception\QueryException
     */
    public function actionPushSkuAttribute($client_id, $user_id, $data)
    {
        User::setLoginUserById($user_id);
        $data = json_decode($data, true);
        $this->validate([
            'item_id' => 'integer',
            'item_name' => 'required_without:item_id|string|max:64',
            'node' => 'required|array',
            'node.*.item_id' => 'integer',
            'node.*.item_name' => 'required_without:node.*.item_id|string|max:64'
        ], $data);

        $itemId = $data['item_id'] ?? 0;
        $attributeApi = new AttributesApi($client_id, Constants::TYPE_PRODUCT);
        $itemSetting = $attributeApi->findOne($itemId);
        if( $itemSetting->isExist() )
        {
            $oldItemInfo = $attributeApi->tree($itemId, true,true);
            // 补充 item_name
            if (!empty($itemId) && empty($data['item_name'])) {
                $data['item_name'] = $itemSetting->item_name;
            }
            $oldNodeItemMap = !empty($oldItemInfo[0]['node']) ? array_column($oldItemInfo[0]['node'], 'item_name', 'item_id') : [];
            foreach ($data['node'] as $k => $datum) {
                if (!empty($datum['item_id']) && empty($datum['item_name'])) {
                    if (!isset($oldNodeItemMap[$datum['item_id']])) {
                        throw new \RuntimeException('node.item_id not found:' . $datum['item_id'], 404);
                    }
                    $data['node'][$k]['item_name'] = $oldNodeItemMap[$datum['item_id']];
                }
            }

            $skuApi = new SkuAPI($client_id, $user_id);

            if ($itemSetting->item_name != $data['item_name']) {
                // 被使用规格不允许改名
                $hasUsed = $skuApi->whetherUsingSkuAttr($data['item_id'] ?? 0);
                if ($hasUsed) {
                    throw new \RuntimeException('The specification has been used and cannot be modified', 404);
                }
            }

            if (!isset($hasUsed) || true === $hasUsed) {
                // 检查规格值是否被使用
                $existValues = $attributeApi->getSkuValuesOfAttr($itemId, $itemSetting);
                $existValues = $existValues->getAttributes(['item_id', 'item_name']);
                $modifiedValues = ItemSettingAPI::diffNodes($existValues, $data['node'], true);

                if ($skuApi->whetherUsingSkuValues($itemId, array_column($modifiedValues, 'item_id'))) {
                    throw new RuntimeException('The specification value has been used and cannot be modified or deleted', 404);
                }
            }

            $attributeApi->edit($itemId, [
                'item_name' => $data['item_name'],
            ], $data['node']);
        }
        else
        {
            $itemId = $attributeApi->create([
                'item_name' => $data['item_name']
            ], 0, 0, $data['node']);
        }

        // 反查所有item_id
        $attributeApi->getParams()->setShowReferenced($itemId);
        $newData = $attributeApi->tree($itemId, true,true);

        return $this->success(current($newData));
    }

    /**
     * 追加产品规格值
     * @param $client_id
     * @param $user_id
     * @param $data
     * @return false|string
     * @throws ProcessException
     * @throws Throwable
     */
    public function actionAppendSkuAttributeValue($client_id, $user_id, $data)
    {
        User::setLoginUserById($user_id);
        $data = json_decode($data, true);
        $this->validate([
            'item_id' => 'required|integer',
            'attributes' => 'required|array',
            'attributes.*' => 'required|string|max:64',
        ], $data);

        $itemId = $data['item_id'];
        $attributeApi = new AttributesApi($client_id, Constants::TYPE_PRODUCT);
        $itemSetting = $attributeApi->findOne($itemId);
        if (!$itemSetting->isExist()) {
            throw new \RuntimeException(\Yii::t('product', 'The specifications don\'t exist'), 404);
        }


        $attributes = array_unique(array_filter($data['attributes']));

        $existValues = $attributeApi->getSkuValuesExistNames($itemId, $itemSetting);
        $addAttributes = array_values(array_diff($attributes, $existValues));
        if (!empty($addAttributes)) {
            $attributeApi->addItem($itemId, $addAttributes, $user_id);
        }
        // 反查所有item_id
        $attributeApi->getParams()->setShowReferenced($itemId);
        $newData = $attributeApi->tree($itemId, true, true);

        return $this->success(current($newData));
    }

    /**
     * 产品规格删除
     * @param $client_id
     * @param $user_id
     * @param $item_id
     * @return false|string
     * @throws ProcessException
     * @throws \xiaoman\orm\exception\OrmException
     * @throws \xiaoman\orm\exception\QueryException
     */
    public function actionRemoveSkuAttribute($client_id, $user_id, $item_id)
    {
        User::setLoginUserById($user_id);
        $attributesApi = new AttributesApi($client_id, \Constants::TYPE_PRODUCT);
        if (!$attributesApi->exist($item_id)) {
            throw new RuntimeException('The specifications don\'t exist', 404);
        }

        $skuApi = new SkuAPI($client_id, $user_id);
        if ($skuApi->whetherUsingSkuAttr($item_id)) {
            throw new \RuntimeException('Delete failed, the specification has been used, and it is not allowed to delete', 404);
        }
        $attributesApi->delete($item_id);
        return $this->success(['item_id' => $item_id]);
    }

    /**
     * 推送产品数据到小满产品库
     *
     * @param $client_id
     * @param $user_id
     * @param $data
     */
    public function actionPush($client_id, $user_id, $data)
    {
        $requestData = json_decode($data, true);

        //base64 图片过大，不记录源数据日志，只记录md5
        $logData = $requestData;
        if (!empty($logData['images_base64'])) {
            $logData['images_base64_data_md5'] = array_map(function ($images_base64){
                    return md5($images_base64['data'] ?? '');
                }, $logData['images_base64']);
            unset($logData['images_base64']);
        }
        LogUtil::info("push client_id={$client_id} user_id={$user_id} data=" . json_encode($logData));

        $this->validate([
            'product_no' => 'string|max:255',
            'name'       => 'string|max:255',
            'model'      => 'string|max:255',
            'cn_name'       => 'string|max:255',
            'category_ids'  => 'array',
            'images_base64' => 'array',
            'images_base64.*.data' => 'string',
            'images_base64.*.file_name' => 'string',
            'info_json'         => 'array',
            'info_json.*.name'  => 'string',
            'info_json.*.value' => 'string',
        ], $requestData);
        $data = $this->filterPushFields($client_id, $requestData);
        $sku_list = $data['sku_items'] ?? [];
        $sub_product_sku_list = $data['sub_product_sku_items'] ?? [];
        $images = $data['images'] ?? null;
        $images = is_string($images) ? array_filter([$images]) : $images;
        $images_base64 = $data['images_base64'] ?? null;

        if (isset($data['sku_items'])) {
            unset($data['sku_items']);
        }

        if (isset($data['sub_product_sku_items'])) {
            unset($data['sub_product_sku_items']);
        }

        // 当用户输入是多规格或者组合产品时校验对应的sku或者组合产品参数不能为空
        if (!empty($data['product_type'])) {
            if ($data['product_type'] == ProductConstant::PRODUCT_TYPE_SKU && empty($sku_list)) {
                throw new \RuntimeException('When product_type = '.ProductConstant::PRODUCT_TYPE_SKU.', the parameter sku_items cannot be empty.', 404);
            }
            if ($data['product_type'] == ProductConstant::PRODUCT_TYPE_COMBINE && empty($sub_product_sku_list)) {
                throw new \RuntimeException('When product_type = '.ProductConstant::PRODUCT_TYPE_COMBINE.', the parameter sub_product_sku_items cannot be empty.', 404);
            }
        }

        foreach ($data as $field => $value) {
            if (is_numeric($field)) {
                $data['external_field_data'][$field] = $value;
            }
        }

        User::setLoginUserById($user_id);
        $isNew = false;

        if (!isset($data['product_id']) || empty($data['product_id'])) {
            $isNew = true;
            $data['source_type'] = \common\library\product_v2\ProductConstant::SOURCE_TYPE_ERP_IMPORT;
        }

        if (!$isNew && isset($data['product_id']) && !is_numeric($data['product_id'])) {
            throw new \RuntimeException('Param of product_id must be numeric', 404);
        }

        // 多规格产品 sku图片和参数处理
        if (!empty($sku_list)) {
            $skuImageFileIdMap = [];
            foreach ($sku_list as &$item) {
                if (!empty($item['sku_image_url']) && empty($item['image_file_id'])) {
                    if (isset($skuImageFileIdMap[$item['sku_image_url']])) {
                        $item['image_file_id'] = $skuImageFileIdMap[$item['sku_image_url']];
                    } else {
                        $imageInfo = $this->uploadImage($item['sku_image_url']);
                        $item['image_file_id'] = $imageInfo['id'] ?? 0;
                        if ($item['image_file_id'] > 0) {
                            $skuImageFileIdMap[$item['sku_image_url']] = $item['image_file_id'];
                        }
                    }
                }
                unset($item['sku_image_url']);
            }
            // 这里做下fob_type获取
            if (!isset($data['fob_type']) && !$isNew) {
                $data['fob_type'] = $this->getProductFobType($client_id, $data['product_id']);
            }
            $sku_list = $this->unpackSkuListFormData($sku_list, $data);
        }

        if (!empty($data['product_type'])
            && $data['product_type'] == ProductConstant::PRODUCT_TYPE_SPU
            && empty($sku_list)
        ) {
            //固定单价
            if (($data['fob_type'] ?? ProductConstant::FOB_TYPE_SKU) == ProductConstant::FOB_TYPE_SKU) {
                $data['fob'] = [
                    'price_currency' => $data['price_currency'] ?? \common\library\account\Client::getClient($client_id)->getMainCurrency(),
                    'fob_price' => $data['fob_price'] ?? 0,
                    'price_min' => $data['price_min'] ?? 0,
                    'price_max' => $data['price_max'] ?? 0,
                    'quantity' => $data['quantity'] ?? 0,
                    'fob_type' => $data['fob_type'] ?? ProductConstant::FOB_TYPE_SKU,
                    'gradient_price' => [
                        ['price' => '', 'quantity' => '']
                    ]
                ];
            }

            if (!empty($data['external_field_data']) && empty($data['sku_external_field_data'])) {
                $data['sku_external_field_data'] = $data['external_field_data'];
            }
        }

        if (isset($data['sku_attributes']) && !empty($data['sku_attributes'])) {
            $data['sku_attributes'] = $this->unpackSkuAttributesFormData($data['sku_attributes']);
        }

        try {
            // 产品图片更新逻辑：images 和 images_base64 两个字段一起判断
            // 1. 两个字段 !empty，则更新为两个字段的合并值
            // 2. 两个字段 !isset，则不更新
            // 3. 只传一个字段：
            //   2.1 !empty()，则更新为该字段值
            //   2.2 empty()，则更新图片为空
            if (is_array($images)) {
                $data['images'] = [];
                foreach ($images as $key => $image) {
                    if (is_string($image)) {
                        $data['images'][] = $this->uploadImage($image);
                    } else {
                        $data['images'][] = $image;
                    }
                }
            }

            if (is_array($images_base64)) {
                $data['images'] = $data['images'] ?? [];
                foreach ($images_base64 as $imageData) {
                    if (!empty($imageData['data'])) {
                        $data['images'][] = $this->uploadBase64File($imageData['data'], $imageData['file_name'] ?? '');
                    }
                }
            }

            // 对图片进行格式校验
            if(isset($data['images']) && is_array($data['images'])){
                foreach($data['images'] as $image){
                    if(empty($image['id']) || empty($image['src'])){
                        throw new \RuntimeException('Format of image field error', 400);
                    }
                }
            }

            $api = new \common\library\product_v2\ProductAPI($client_id, $user_id);
            if ($isNew) {
                $product = $api->create($data, $sku_list, false, $sub_product_sku_list);
            } else {
                $product = $api->edit($data['product_id'], $data, $sku_list, false, $sub_product_sku_list);
            }
        } catch (\Throwable $e) {
            LogUtil::info("error client_id={$client_id} product_id=" . ($data['product_id'] ?? 0) . " {$e->getMessage()}");
            throw new \RuntimeException($e->getMessage(), !empty($e->getCode()) ? $e->getCode() : 500);
        }

        // 保存 ERP 日志记录
        if (!empty($product->product_id)) {
            \common\library\erp\Helper::pushErpResourceLog(
                $client_id,
                $product->product_id,
                $product->product_no,
                \Constants::TYPE_PRODUCT,
                \common\library\erp\ErpConstant::TRANS_TYPE_OF_ERP_TO_CRM
            );
        }
        $result = ['product_id' => intval($product->product_id), 'product_no' => $product->product_no];
        $detail = $api->productInfo($product->product_id, [
                CustomFieldService::PRODUCT_GROUP_SKU
        ], null, \common\library\APIConstant::SCENE_OPEN_API);
        foreach ($detail['sku_items'] ?? [] as $skuItem) {
            $result['sku_items'][] = [
                'sku_id' => $skuItem['sku_id'],
                'sku_code' => $skuItem['sku_code'],
            ];
        }
        foreach ($detail['sub_product_sku_items'] ?? [] as $subItem) {
            $result['sub_product_sku_items'][] = [
                'relation_id' => $subItem['relation_id'],
                'product_id' => $subItem['product_id'],
                'sku_id' => $subItem['sku_id'],
                'sku_code' => $subItem['sku_code'],
            ];
        }
        $this->success($result);
    }

    /**
     * 产品删除
     * @param $client_id
     * @param $user_id
     * @param int $product_id
     * @return false|string
     * @throws ProcessException
     */
    public function actionRemove($client_id, $user_id, $product_id = 0)
    {
        User::setLoginUserById($user_id);
        $api = new \common\library\product_v2\ProductAPI($client_id, $user_id);
        $delCount = $api->delete(['product_id' => [$product_id]]);
        if ($delCount <= 0) {
            throw new \RuntimeException('delete product failure', 404);
        }
        return $this->success(['product_id' => $product_id]);
    }

    /**
     * 查询产品库列表
     * @param $client_id
     * @param $user_id
     * @param int $page_size
     * @param int $page
     * @param string $start_time
     * @param string $end_time
     * @param integer $product_type
     * @param integer $enable_flag
     * @throws ProcessException
     */
    public function actionList($client_id, $user_id, $page_size = 20, $page = 1, $start_time = '', $end_time = '', $time_type = 1, $product_type = 0, $enable_flag = 1)
    {
        User::setLoginUserById($user_id);

        $this->validate([
            'start_index'   => 'integer',
            'count'         => 'integer|max:1000',
            'start_time'    => 'date',
            'end_time'      => 'date',
        ], [
            'start_index' => $page,
            'count'       => $page_size,
            'start_time'  => $start_time,
            'end_time'    => $end_time,
        ]);

        $params = [
            'cur_page'     => $page,
            'page_size'    => $page_size,
            'sort_field'   => ['update_time', 'product_id'],
            'sort_type'    => 'desc',
            'product_type' => $product_type,
            'enable_flag'  => $enable_flag,
        ];

        if ($time_type == 1) {
            $params['update_time_start'] = $start_time;
            $params['update_time_end'] = $end_time;
        } else if ($time_type == 2) {
            $params['create_time_start'] = $start_time;
            $params['create_time_end'] = $end_time;
        }

        $api = new \common\library\product_v2\ProductAPI($client_id, $user_id);
        $data = $api->webList($params, \common\library\APIConstant::SCENE_OPEN_API);

        $this->success($data);
    }

    /**
     * 查询产品库列表
     * @param $client_id
     * @param $user_id
     * @param int $page_size
     * @param int $page
     * @param string $start_time
     * @param string $end_time
     * @throws ProcessException
     */
    public function actionSkuList($client_id, $user_id, $page_size = 20, $page = 1, $start_time = '', $end_time = '')
    {
        User::setLoginUserById($user_id);
        $params = [
            'update_time_start' => $start_time,
            'update_time_end' => $end_time,
            'cur_page' => $page,
            'page_size' => $page_size,
            'sort_field' => 'update_time',
            'sort_type' => 'desc'
        ];

        $api = new \common\library\product_v2\sku\SkuAPI($client_id, $user_id);
        $data = $api->webList($params, \common\library\APIConstant::SCENE_OPEN_API);

        $this->success($data);
    }

    /**
     * 查询产品详细数据
     * @param $client_id
     * @param $user_id
     * @param int $product_id
     * @param string $product_no
     * @return false|string
     * @throws ProcessException
     */
    public function actionInfo($client_id, $user_id, $product_id = 0, $product_no = '')
    {
        try {
            $this->validate([
                'product_id' => 'integer',
            ], [
                'product_id' => $product_id,
            ]);

            User::setLoginUserById($user_id);
            $api = new \common\library\product_v2\ProductAPI($client_id, $user_id);

            $groups = [
                CustomFieldService::PRODUCT_GROUP_BASIC,
                CustomFieldService::PRODUCT_GROUP_PRICE,
                CustomFieldService::PRODUCT_GROUP_PACKAGE,
                CustomFieldService::PRODUCT_GROUP_DESCRIBE,
                CustomFieldService::PRODUCT_GROUP_SKU,
                CustomFieldService::GROUP_SYSTEM_INFO,
                CustomFieldService::PRODUCT_GROUP_SIZE,
                CustomFieldService::PRODUCT_GROUP_CUSTOM,
                CustomFieldService::PRODUCT_GROUP_CARTON,
            ];

            $externalFields = [];
            $data = $api->productInfo($product_id, $groups, null, \common\library\APIConstant::SCENE_OPEN_API, $product_no);
            foreach ($data['data'] as $key => $item) {
                foreach ($item['fields'] as $field) {
                    if ($field['id'] == 'product_type') {
                        continue;
                    }
                    if ($field['base'] == 1) {
                        $data[$field['id']] = $field['format'];
                    } else {
                        $externalFields[$field['id']] = $field['format'];
                    }
                }
            }
            unset($data['data']);
            foreach ($externalFields as $field => $value) {
                $data[$field] = $value;
            }

            // 保存 ERP 日志记录
            if (!empty($data['product_id'])) {
                \common\library\erp\Helper::pushErpResourceLog(
                    $client_id,
                    $data['product_id'],
                    $data['product_no'],
                    \Constants::TYPE_PRODUCT,
                    \common\library\erp\ErpConstant::TRANS_TYPE_OF_CRM_TO_ERP
                );
            }
            // 产品表已经去掉gradient_price 为了兼容
            if (!empty($data['sku_items'])) {
                $firstSkuItem = current($data['sku_items']);
                $data['gradient_price'] = $firstSkuItem['fob']['gradient_price'] ?? [];
            } else {
                $data['gradient_price'] = [];
            }
            return $this->success($data);
        } catch (\RuntimeException $e) {
            throw new \RuntimeException('Not Found Resource', 404);
        }
    }

    /**
     * 更新字段过滤处理
     * @param $client_id
     * @param $data
     * @return array
     */
    private function filterPushFields($client_id, $data)
    {
        $fields = [
            'product_id',
            'product_no',
            'product_type',
            'name',
            'cn_name',
            'model',
            'brand',
            'hs_code',
            'place',
            'info_json',
            'quantity',
            'quantity_unit',
            'price_currency',
            'price_min',
            'price_max',
            'price_unit',
            'package_gross_weight',
            'package_unit',
            'package_volume',
            'package_remark',
            'package_size_length',
            'package_size_weight',
            'package_size_height',
            'product_size_length',
            'product_size_weight',
            'product_size_height',
            'product_volume',
            'product_net_weight',
            'count_per_package',
            'cost_currency',
            'cost',
            'cost_unit',
            'intro',
            'description',
            'product_remark',
            'images',
            'images_base64',
            'group_id',
            'minimum_order_quantity',
            'unit',
            'sku_items',
            'sku_attributes',
            'category_ids',
            'package_size',
            'cost_with_tax',
            'from_url',
            'fob_type',
            'fob_price',
            'gradient_price',
            'sub_product_sku_items',
            'disable_flag',
            'customs_name',
            'customs_cn_name',
            'carton_volume',
            'carton_net_weight',
            'carton_gross_weight',
            'count_per_carton',
            'package_type',
            'carton_size_length',
            'carton_size_weight',
            'carton_size_height',
        ];

        $specialFields = [
            'category_ids',
            'info_json',
            'images',
            'sku_items',
            'sub_product_sku_items',
            'sku_attributes',
            'gradient_price',
            'images_base64',
        ];

        $transformFields = [
            'minimum_order_quantity' => 'quantity',
            'cost_with_tax' => 'cost'
        ];

        $result = $data;
        $data = [];

        $service = new FieldList($client_id);
        $service->setType(Constants::TYPE_PRODUCT);
        $service->setFormatterType(CustomFieldFormatter::TYPE_COLUMN_MAP2);
        $list = $service->find();
        $fieldList = ArrayUtil::index($list, 'id');

        foreach ($result as $key => $item) {
            if (in_array($key, $fields)) {
                if (is_array($item) && !in_array($key, $specialFields)) {
                    foreach ($item as $k => $value) {
                        $data[$k] = $value;
                    }
                } elseif (isset($transformFields[$key])) {
                    $data[$transformFields[$key]] = $item;
                } else {
                    $data[$key] = $item;
                }
            }else if(is_numeric($key) && isset($fieldList[$key]) && !in_array($fieldList[$key]['field_type'], [CustomFieldService::FIELD_TYPE_ATTACH, CustomFieldService::FIELD_TYPE_IMAGE])) {
                $data['external_field_data'][$key] = $item;
            }
        }

        return $data;
    }

    private function formatInfo($clientId, $data)
    {
        $fields = [
            'product_id',
            'product_no',
            'name',
            'group_id',
            'category_ids',
            'images',
            'model',
            'brand',
            'hs_code',
            'place',
            'info_json',
            'quantity',
            'quantity_unit',
            'price_currency',
            'price_min',
            'price_max',
            'price_unit',
            'package_gross_weight',
            'package_unit',
            'package_volume',
            'package_remark',
            'count_per_package',
            'cost_currency',
            'cost',
            'cost_unit',
            'intro',
            'description',
            'product_remark',
            'fob',
            'cost_with_tax',
            'minimum_order_quantity',
            'unit',
            'create_time',
            'update_time'
        ];

        $result = $data;
        $data = [];
        $externalFieldData = $result['external_field_data'];

        foreach ($result as $key => $item) {
            if (is_string($item)) {
                $item = preg_replace('/[\"\']/', '', $item);
            }

            if ($key != 'external_field_data' && in_array($key, $fields)) {
                $data[$key] = $item;
            }

            if ($key == 'group_id') {
                $groupMap = !empty($item) ? \common\library\group\Helper::getGroupNameMap($clientId, Constants::TYPE_PRODUCT, [$item]) : ['未分组'];
                $data[$key] = $groupMap[$item];
            }

            if (in_array($key, ["price_currency","price_min","price_max","price_unit","quantity"])) {
                $data['fob'][$key] = $item;
            }

            if (in_array($key, ["cost_currency","cost","cost_unit"])) {
                $data['cost_with_tax'][$key] = $item;
            }

            if ($key == 'quantity') {
                $data[$key] = $item = floatval($item);
            }

            if (in_array($key, ["quantity","quantity_unit"])) {
                $data['minimum_order_quantity'][$key] = $item;
            }

            if ($key == 'category_ids') {
                $data['category_ids'] = '';
                if (count($item)) {
                    $categoryId = end($item);
                    $categoryMap = \Category::getNameMap('zh', [$categoryId]);
                    $data['category_ids'] = $categoryMap[$categoryId] ?? '';
                }
            }
        }

        foreach ($externalFieldData as $key => $item) {
            if ($key == '12826087063' && $item >= 1) {
                $item = number_format($item / 100, 2);
            }
            $data[$key] = $item;
        }

        return $data;
    }

    private function mapSpecifyColumns($fieldData) {
        $specifyColumns = [
            [
                "id" => "price_currency",
                "name" => "离岸价货币",
                "base" => 1,
                "field_type" => 1,
                "ext_info" => null,
                "require"=> 0,
                "default" => ""
            ],
            [
                "id" => "price_min",
                "name" => "离岸价最小价格",
                "base" => 1,
                "field_type" => 5,
                "ext_info" => null,
                "require" => 0,
                "default" => ""
            ],
            [
                "id" => "price_max",
                "name" => "离岸价最大价格",
                "base" => 1,
                "field_type" => 5,
                "ext_info" => null,
                "require" => 0,
                "default" => ""
            ],
            [
                "id" => "price_unit",
                "name" => "离岸价单位",
                "base" => 1,
                "field_type" => 1,
                "ext_info" => null,
                "require" => 0,
                "default" => ""
            ],
            [
                "id" => "quantity",
                "name" => "最小起订数量",
                "base" => 1,
                "field_type" => 5,
                "ext_info" => null,
                "require" => 0,
                "default" => 0
            ],
            [
                "id" => "quantity_unit",
                "name" => "最小起订数量单位",
                "base" => 1,
                "field_type" => 1,
                "ext_info" => null,
                "require" => 0,
                "default" => ""
            ],
            [
                "id" => "cost_currency",
                "name" => "含税成本价货币",
                "base" => 1,
                "field_type" => 1,
                "ext_info" => null,
                "require" => 1,
                "default" => ""
            ],
            [
                "id" => "cost",
                "name" => "含税成本价价格",
                "base" => 1,
                "field_type" => 5,
                "ext_info" => null,
                "require" => 1,
                "default" => 0
            ],
            [
                "id" => "cost_unit",
                "name" => "含税成本价单位",
                "base" => 1,
                "field_type" => 1,
                "ext_info" => null,
                "require" => 1,
                "default" => ""
            ],
            [
                "id" => "sub_product_count",
                "name" => "子产品数量",
                "base" => 1,
                "field_type" => 5,
                "ext_info" => null,
                "require" => 0,
                "default" => "",
                "is_list" => 0
            ],
            [
                "id" => "combine_product_count",
                "name" => "组合产品数量",
                "base" => 1,
                "field_type" => 5,
                "ext_info" => null,
                "require" => 0,
                "default" => "",
                "is_list" => 0
            ],
            [
                "id" => "package_size_length",
                "name" => "包装尺寸 (长cm)",
                "base" => 1,
                "field_type" => 5,
                "ext_info" => null,
                "require" => 0,
                "default" => "",
                "is_list" => 0
            ],
            [
                "id" => "package_size_weight",
                "name" => "包装尺寸 (宽cm)",
                "base" => 1,
                "field_type" => 5,
                "ext_info" => null,
                "require" => 0,
                "default" => "",
                "is_list" => 0
            ],
            [
                "id" => "package_size_height",
                "name" => "包装尺寸 (高cm)",
                "base" => 1,
                "field_type" => 5,
                "ext_info" => null,
                "require" => 0,
                "default" => "",
                "is_list" => 0
            ],
            [
                "id" => "associate_cost_flag",
                "name" => "是否关联配件成本",
                "base" => 1,
                "field_type" => 5,
                "ext_info" => null,
                "require" => 0,
                "default" => "",
                "is_list" => 0
            ],
            [
                "id" => "cost_type",
                "name" => "含税成本价类型",
                "base" => 1,
                "field_type" => 5,
                "ext_info" => null,
                "require" => 0,
                "default" => "",
                "is_list" => 0
            ],
            [
                "id" => "gradient_price",
                "name" => "阶梯离岸价",
                "base" => 1,
                "field_type" => 5,
                "ext_info" => null,
                "require" => 0,
                "default" => "",
                "is_list" => 0
            ],
            [
                "id" => "fob_type",
                "name" => "离岸价类型",
                "base" => 1,
                "field_type" => 5,
                "ext_info" => null,
                "require" => 0,
                "default" => "",
                "is_list" => 0
            ],
        ];

        $fields = array_column($specifyColumns, 'id');

        $newData = array_filter($fieldData, function($item) use($fields) {
            return !in_array($item['id'], $fields);
        });

        $list = array_values(array_merge($newData, $specifyColumns));
        return $list;
    }


    private function dataMigrate(array $skuList, array $productInfo)
    {
        // // 进到这里都是一个多规则产品 - 迁移兼容
        $migrateFields = [
            // spu => sku
            "price_currency" => 'price_currency',
            "cost_currency" => "cost_currency",
            "price_min" => "price_min",
            "price_max" => "price_max",
            "quantity" =>"quantity",
            "fob_type" => "fob_type",
            "gradient_price" => "gradient_price",
            "count_per_package" => "count_per_package",
            "package_gross_weight" => "package_gross_weight",
            "package_remark" => "package_remark",
            "package_size_length" => "package_size_length",
            "package_size_weight" => "package_size_weight",
            "package_size_height" => "package_size_height",
            "package_volume" => "package_volume",
            "package_unit" => "package_unit",
            "product_net_weight" =>"product_net_weight",
            "product_size_length" => "product_size_length",
            "product_size_height" =>"product_size_height",
            "product_size_weight" => "product_size_weight",
            "product_volume" => "product_volume",
            "sku_external_field_data" => "external_field_data",
            'sku_remark' => 'product_remark',
            'sku_description' => 'description'
        ];
        $fobType = $productInfo['fob_type'] ?? 0;
        foreach ($skuList as &$item) {
            foreach ($migrateFields as $spuField => $skuField) {
                if (isset($productInfo[$spuField])) {
                    $fieldVal = $productInfo[$spuField];
                    if (in_array($skuField, ['price_min', 'price_max', 'gradient_price'])
                        && $fobType == ProductConstant::FOB_TYPE_SKU) {
                        continue;
                    }
                    if ($skuField == 'fob_type' && $fieldVal == ProductConstant::FOB_TYPE_SKU) {
                        $fieldVal = ProductConstant::FOB_TYPE_SKU;
                    }
                    $item[$skuField] = $fieldVal;
                }
            }
        }
        return $skuList;
    }

    private function unpackSkuListFormData(array $formSkuList, array $productInfo)
    {
        $user = User::getLoginUser();
        $client_id = $user->getClientId();

        $skuList = [];
        $service = new FieldList($client_id);
        $service->setType(Constants::TYPE_PRODUCT);
        $service->setFormatterType(CustomFieldFormatter::TYPE_COLUMN_MAP2);
        $list = $service->find();
        $fieldList = ArrayUtil::index($list, 'id');

        //目前open-api不支持自定义附件、图片
        foreach ($formSkuList as $item) {
            $sku_external_field_data = [];
            foreach ($item as $item_key => $item_value) {
                if (is_numeric($item_key) && !is_array($item_value) && !in_array($fieldList[$item_key]['field_type'] ?? -1, [CustomFieldService::FIELD_TYPE_ATTACH, CustomFieldService::FIELD_TYPE_IMAGE])) {
                    $sku_external_field_data[$item_key] = $item_value;
                }
            }

            $attributes = [];
            $sort = 1;
            foreach ($item['attributes'] as $itemKey => $itemValue ) {
                // {"product_no":"WU1340","sku_items":[{"attributes":[{"value":[]}]}],"product_id":19387377627729,"name":"Water Pump","product_remark":"数环通自动更新商品","model":"禁用"} 用户传入这种，增加校验
                if (isset($itemValue['value']) && empty($itemValue['value'])) {
                    throw new \RuntimeException('sku_items.attributes parameter error: item_id or value item_id is empty', 404);
                }
                if (isset($itemValue['item_id'])) {
                    $itemKey = $itemValue['item_id'];
                    $itemValue = $itemValue['value']['item_id'];
                    // 防止出现传入item_id，但是缺少值和属性值的情况
                    if (empty($itemKey) || empty($itemValue)) {
                        throw new \RuntimeException('sku_items.attributes parameter error: item_id or value item_id is empty', 404);
                    }
                }
                //产品需求需要指定排序
                $attributes[$itemKey] = ['attr' => $itemValue, 'sort' => $sort];
                $sort++;
            }

            $skuList[] = [
                'sku_id' => $item['sku_id']??0,
                'sku_code' => $item['sku_code'],
                'fob_price' => $item['fob_price'] ?? 0,
                'image_file_id' => $item['image_file_id']??0,
                'disable_flag' => $item['disable_flag'] ?? 0,
                'attributes' =>  $attributes,
                'sku_external_field_data' => $sku_external_field_data ?? []
            ];
        }
        return $this->dataMigrate($skuList, $productInfo);
    }

    private function unpackSkuAttributesFormData(array $formSkuAttributes)
    {
        $skuAttributes = [];
        $sort = 1;
        foreach ($formSkuAttributes as $itemKey => $itemValue) {
            if (isset($itemValue['item_id'])) {
                $itemKey = $itemValue['item_id'];
                $itemValue = array_filter(array_column($itemValue['value'],'item_id'));
                // 防止出现传入item_id，但是缺少值和属性值的情况
                if (empty($itemKey) || empty($itemValue)) {
                    throw new \RuntimeException('sku_attributes.items parameter error: item_id or value item_id is empty', 404);
                }
            }
            $skuAttributes[$itemKey] = [
                'attrs' => $itemValue,
                'sort' => $sort
            ];
            $sort++;
        }

        return $skuAttributes;
    }

    /**
     * 产品枚举值
     * @param int $client_id
     * @param int $user_id
     * @param string $field
     * @return array
     * @throws ProcessException
     */
    public function actionProductEnums(int $client_id = 0, int $user_id = 0, string $field = '')
    {
        User::setLoginUserById($user_id);

        // 产品类型，1无规格、2多规格、3组合
        $productTypeList = array_map(function ($item) {
            return [
                'code' => $item['value'],
                'name' => $item['key'],
            ];
        }, \common\library\product_v2\ProductHelper::getProductTypeMap());

        // 离岸价设置类型,1单一区间定价,2阶梯定价,3规格定价
        $fobTypeList = [
            ['code' => \common\library\product_v2\ProductConstant::FOB_TYPE_PRICE, 'name' => '单一区间定价'],
            ['code' => \common\library\product_v2\ProductConstant::FOB_TYPE_GRADIENT, 'name' => '阶梯定价'],
            ['code' => \common\library\product_v2\ProductConstant::FOB_TYPE_SKU, 'name' => '规格定价'],
        ];

        // 创建方式, 0手动创建，1阿里巴巴同步，2环球资源同步，3自动创建，4文件导入，5ERP导入，6cms的产品从crm导入，7平台生成
        $sourceTypeList = array_map(function ($item) {
            return [
                'code' => $item['value'],
                'name' => $item['key'],
            ];
        }, \common\library\product_v2\ProductHelper::getProductSourceTypeMap());

        // 产品计量单位
        $unitList = array_map(function ($item) {
            return [
                'code' => $item,
                'name' => $item,
            ];
        }, \common\library\product_v2\ProductConstant::UNIT_LIST);

        $result = [
            'product_type_ist' => $productTypeList,
            'fob_type_ist' => $fobTypeList,
            'source_type_ist' => $sourceTypeList,
            'unit_list' => $unitList,
        ];

        if (!empty($field)) {
            $result = $result[$field] ?? [];
        }

        return $this->success($result);
    }

    /**
     * 库存列表
     * @param $client_id
     * @param $user_id
     * @param $page
     * @param $page_size
     * @param array $sku_id
     * @return false|string
     * @throws ProcessException
     * @throws \xiaoman\orm\exception\QueryException
     */
    public function actionInventoryList
    (
        $client_id,
        $user_id,
        $page = 1,
        $page_size = 20,
        string $sku_id = null
    )
    {
        $sku_id = explode(',', $sku_id);
        $this->validate([
            'sku_id'       => 'array|max:100',
            'sku_id.*'     => 'integer|min:1',
            'page'         => 'integer|min:1',
            'page_size'    => 'integer|min:1',
        ], compact(['sku_id', 'page', 'page_size']));

        User::setLoginUserById($user_id);
        //产品库存表
        $productInventoryFilter = new ProductInventoryFilter($client_id);
        $productInventoryFilter->select(['sku_id', 'product_id', 'enable_count', 'real_count', 'warehouse_id']);
        !empty($sku_id) && $productInventoryFilter->sku_id = $sku_id;

        //sku
        $skuFilter = new ProductSkuFilter($client_id);
        $skuFilter->select(['sku_code']);
        $skuFilter->enable_flag = 1;
        !empty($sku_id) && $skuFilter->sku_id = $sku_id;

        //产品
        $productFilter = new ProductFilter($client_id);
        $productFilter->select(['product_no', 'unit', 'disable_flag']);
        $productFilter->enable_flag = \Constants::ENABLE_FLAG_TRUE;

        //设置join
        $joinFilter = $skuFilter->initJoin();
        $joinFilter = $joinFilter->innerJoin($productInventoryFilter)
                                 ->on('sku_id', 'sku_id');
        $joinFilter = $joinFilter->innerJoin($productFilter)
                                 ->on('product_id', 'product_id');
        $joinFilter->joinlimit($page_size, ($page - 1) * $page_size);
        $batchProduct = $joinFilter->find();
        $data         = $joinFilter->rawData();
        $count        = $joinFilter->count();

        return $this->success([
            'count' => $count,
            'list'  => $data
        ]);
    }

    /**
     * 设置产品库存
     * @param $client_id
     * @param $user_id
     * @param $data
     * @return void
     * @throws ProcessException
     * @throws \xiaoman\orm\exception\OrmException
     * @throws \xiaoman\orm\exception\QueryException
     */
    public function actionPushInventory($client_id, $user_id, $data)
    {
        LogUtil::info("push client_id={$client_id} user_id={$user_id} data={$data}");
        User::setLoginUserById($user_id);
        $data = json_decode($data, true);

        $this->validate([
            'data' => 'required|array',
            'data.*.sku_id'         => 'required|numeric',
            'data.*.enable_count'   => 'required|numeric',
            'data.*.real_count'     => 'numeric',
            'data.*.inventory_type' => 'numeric',
            'data.*.warehouse_id'   => 'numeric',
        ], compact(['data']));

        $skuIds = array_column($data, 'sku_id');
        $skuFilter = new ProductSkuFilter($client_id);
        $skuFilter->sku_id     = $skuIds;
        $skuFilter->enable_flag = \Constants::ENABLE_FLAG_TRUE;
        $skuFilter->select(['sku_id','product_id', 'sku_code']);
        $skuProductMap = $skuFilter->rawData() ?? [];
        if (empty($skuProductMap)) {
            throw new \RuntimeException('Not Found Resource', 404);
        }
        $skuProductMap = array_column($skuProductMap, null, 'sku_id');

        $warehouseIds = array_column($data, 'warehouse_id');
        if (!empty($warehouseIds)) {
            $filter = new WarehouseFilter($client_id);
            $filter->select(['warehouse_id']);
            $filter->warehouse_id = array_values(array_unique($warehouseIds));
            $filter->enable_flag = \Constants::ENABLE_FLAG_TRUE;
            $existWarehouseIds = $filter->rawData();
            if (count($warehouseIds) != count($existWarehouseIds)){
                throw new \RuntimeException('部分仓库不存在或已删除', 404);
            }
        } else {
            try {
                // 返回异常，这里处理下，否则一直告警
                $defaultWarehouseId = \common\library\oms\warehouse\API::getDefaultWarehouseId($client_id);
            } catch (\Throwable $t) {
                throw new \RuntimeException($t->getMessage(), 404);
            }
        }

        $saveData = $incrementData = [];
        foreach ($data as $item)
        {
            if (!isset($skuProductMap[$item['sku_id']])) {
                continue;
            }

            $temp = [
                'sku_id'       => $item['sku_id'],
                'product_id'   => $skuProductMap[$item['sku_id']]['product_id'],
                'enable_count' => $item['enable_count'],
                'real_count'   => $item['real_count'] ?? $item['enable_count'],
                'warehouse_id'   => $item['warehouse_id'] ?? $defaultWarehouseId,
                'cost_unit_price_rmb' => 0,
                'cost_unit_price_usd' => 0,
            ];

            if (!empty($item['inventory_type']) && $item['inventory_type'] == 2) {
                $incrementData[] = $temp;
            } else {
                $saveData[] = $temp;
            }
        }

        $result = false;
        try {
            $saveData = array_chunk($saveData, 100);
            foreach ($saveData as $saveDatum)
            {
                $batchProductInventory = new BatchProductInventory($client_id);
                $batchProductInventory->initFromData($saveDatum);
                $result = $batchProductInventory->getOperator()->create();
            }
            $incrementData = array_chunk($incrementData, 100);
            foreach ($incrementData as $incrementDatum)
            {
                $batchProductInventory = new BatchProductInventory($client_id);
                $batchProductInventory->initFromData($incrementDatum);
                $result = $batchProductInventory->getOperator()->incrementCreate();
            }
        } catch (\Exception $e) {
            \LogUtil::error($e->getMessage());
        }

//        // 保存 ERP 日志记录
//        if ($result) {
//            \common\library\erp\Helper::pushErpResourceLog(
//                $client_id,
//                current(array_column($skuProductMap, 'sku_id')),
//                current(array_column($skuProductMap, 'sku_code')),
//                \Constants::TYPE_PRODUCT_TRANSFER,
//                \common\library\erp\ErpConstant::TRANS_TYPE_OF_ERP_TO_CRM
//            );
//        }

        $this->success('success');
    }

    /**
     * 仓库列表
     * @param $client_id
     * @param $user_id
     * @param $warehouse_id
     * @return void
     * @throws ProcessException
     */
    public function actionWarehouseList($client_id, $user_id, $warehouse_id = 0)
    {
        User::setLoginUserById($user_id);
        $user = User::getLoginUser();
        $filter = new WarehouseFilter($user->getClientId());
        $filter->select(['warehouse_id', 'warehouse_no', 'name', 'status', 'default_flag', 'manager', 'contact', 'address', 'remark']);
        if (!empty($warehouse_id)) {
            $filter->warehouse_id = $warehouse_id;
        }
        $filter->enable_flag = \Constants::ENABLE_FLAG_TRUE;
        $filter->order('default_flag','asc');
        $filter->order('status','asc');
        $filter->order('create_time','desc');

        $this->success([
            'list' => $filter->rawData() ?? [],
            'count' => $filter->count(),
        ]);
    }

    /**
     * 查询产品的fob_type属性
     * 产品迁移后不再使用该方法
     * @param $clientId
     * @param $productId
     * @return int|mixed 0 就是没查到
     * @throws \xiaoman\orm\exception\QueryException
     */
    private function getProductFobType($clientId, $productId)
    {
        if (empty($clientId) || empty($productId)) {
            return 0;
        }
        $filter = new ProductSkuFilter($clientId);
        $filter->enable_flag = 1;
        $filter->product_id = new Equal($productId);
        $filter->select(['fob_type']);
        $filter->limit(1);
        $filter->order('sku_id');
        $infos = $filter->find()->getListAttributes(['fob_type']);
        if (empty($infos)) {
            return 0;
        }
        return $infos[0]['fob_type'] ?? 0;
    }
}