{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "c86c7a37cff6089a055f99951e4f5b41", "packages": [{"name": "abraham/twitteroauth", "version": "4.0.1", "source": {"type": "git", "url": "https://github.com/abraham/twitteroauth.git", "reference": "b9302599e416e5c00742cf7f4455220897f8291d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/abraham/twitteroauth/zipball/b9302599e416e5c00742cf7f4455220897f8291d", "reference": "b9302599e416e5c00742cf7f4455220897f8291d", "shasum": ""}, "require": {"composer/ca-bundle": "^1.2", "ext-curl": "*", "php": "^7.4 || ^8.0 || ^8.1"}, "require-dev": {"php-vcr/php-vcr": "^1", "php-vcr/phpunit-testlistener-vcr": "dev-php-8", "phpmd/phpmd": "^2", "phpunit/phpunit": "^8 || ^9", "rector/rector": "^0.12.19 || ^0.13.0", "squizlabs/php_codesniffer": "^3"}, "type": "library", "autoload": {"psr-4": {"Abraham\\TwitterOAuth\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://abrah.am", "role": "Developer"}], "description": "The most popular PHP library for use with the Twitter OAuth REST API.", "homepage": "https://twitteroauth.com", "keywords": ["Twitter API", "Twitter oAuth", "api", "o<PERSON>h", "rest", "social", "twitter"], "support": {"issues": "https://github.com/abraham/twitteroauth/issues", "source": "https://github.com/abraham/twitteroauth"}, "time": "2022-08-18T23:30:33+00:00"}, {"name": "adbario/php-dot-notation", "version": "2.4.1", "source": {"type": "git", "url": "https://github.com/adbario/php-dot-notation.git", "reference": "3bfe67895d26697d20485343499532234eeb7c08"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/adbario/php-dot-notation/zipball/3bfe67895d26697d20485343499532234eeb7c08", "reference": "3bfe67895d26697d20485343499532234eeb7c08", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-json": "*", "php": "^5.5 || ^7.0 || ^8.0"}, "require-dev": {"phpunit/phpunit": "^4.8|^5.7|^6.6|^7.5|^8.5|^9.5", "squizlabs/php_codesniffer": "^3.6"}, "type": "library", "autoload": {"files": ["src/helpers.php"], "psr-4": {"Adbar\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "PHP dot notation access to arrays", "homepage": "https://github.com/adbario/php-dot-notation", "keywords": ["ArrayA<PERSON>ess", "dotnotation"], "support": {"issues": "https://github.com/adbario/php-dot-notation/issues", "source": "https://github.com/adbario/php-dot-notation/tree/2.4.1"}, "time": "2022-08-25T19:47:20+00:00"}, {"name": "alibabacloud/cams-20200606", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/alibabacloud-sdk-php/cams-20200606.git", "reference": "c3a8d5390a039e0275a7f22f17298e08ec2c046b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/alibabacloud-sdk-php/cams-20200606/zipball/c3a8d5390a039e0275a7f22f17298e08ec2c046b", "reference": "c3a8d5390a039e0275a7f22f17298e08ec2c046b", "shasum": ""}, "require": {"alibabacloud/darabonba-openapi": "^0.2.8", "alibabacloud/endpoint-util": "^0.1.0", "alibabacloud/openapi-util": "^0.1.10|^0.2.1", "alibabacloud/tea-utils": "^0.2.19", "php": ">5.5"}, "type": "library", "autoload": {"psr-4": {"AlibabaCloud\\SDK\\Cams\\*********\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Alibaba Cloud SDK", "email": "<EMAIL>"}], "description": "Alibaba Cloud cams (20200606) SDK Library for PHP", "support": {"source": "https://github.com/alibabacloud-sdk-php/cams-20200606/tree/1.1.0"}, "time": "2023-08-17T12:51:47+00:00"}, {"name": "alibabacloud/client", "version": "1.5.32", "source": {"type": "git", "url": "https://github.com/aliyun/openapi-sdk-php-client.git", "reference": "5bc6f6d660797dcee2c3aef29700ab41ee764f4d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/aliyun/openapi-sdk-php-client/zipball/5bc6f6d660797dcee2c3aef29700ab41ee764f4d", "reference": "5bc6f6d660797dcee2c3aef29700ab41ee764f4d", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"adbario/php-dot-notation": "^2.4.1", "clagiordano/weblibs-configmanager": "^1.0", "ext-curl": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-openssl": "*", "ext-simplexml": "*", "ext-xmlwriter": "*", "guzzlehttp/guzzle": "^6.3|^7.0", "mtdowling/jmespath.php": "^2.5", "php": ">=5.5"}, "require-dev": {"composer/composer": "^1.8", "drupal/coder": "^8.3", "ext-dom": "*", "ext-pcre": "*", "ext-sockets": "*", "ext-spl": "*", "league/climate": "^3.2.4", "mikey179/vfsstream": "^1.6", "monolog/monolog": "^1.24", "phpunit/phpunit": "^5.7|^6.6|^7.5|^8.5|^9.5", "psr/cache": "^1.0", "symfony/dotenv": "^3.4", "symfony/var-dumper": "^3.4"}, "suggest": {"ext-sockets": "To use client-side monitoring"}, "type": "library", "autoload": {"files": ["src/Functions.php"], "psr-4": {"AlibabaCloud\\Client\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Alibaba Cloud SDK", "email": "<EMAIL>", "homepage": "http://www.alibabacloud.com"}], "description": "Alibaba Cloud Client for PHP - Use Alibaba Cloud in your PHP project", "homepage": "https://www.alibabacloud.com/", "keywords": ["alibaba", "alibabacloud", "<PERSON><PERSON><PERSON>", "client", "cloud", "library", "sdk", "tool"], "support": {"issues": "https://github.com/aliyun/openapi-sdk-php-client/issues", "source": "https://github.com/aliyun/openapi-sdk-php-client"}, "time": "2022-12-09T04:05:55+00:00"}, {"name": "alibabacloud/credentials", "version": "1.1.4", "source": {"type": "git", "url": "https://github.com/aliyun/credentials-php.git", "reference": "e79d4151ad8924c0cf79d4fe0ec151b8d7663a25"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/aliyun/credentials-php/zipball/e79d4151ad8924c0cf79d4fe0ec151b8d7663a25", "reference": "e79d4151ad8924c0cf79d4fe0ec151b8d7663a25", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"adbario/php-dot-notation": "^2.2", "alibabacloud/tea": "^3.0", "ext-curl": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-openssl": "*", "ext-simplexml": "*", "ext-xmlwriter": "*", "guzzlehttp/guzzle": "^6.3|^7.0", "php": ">=5.6"}, "require-dev": {"composer/composer": "^1.8", "drupal/coder": "^8.3", "ext-dom": "*", "ext-pcre": "*", "ext-sockets": "*", "ext-spl": "*", "mikey179/vfsstream": "^1.6", "monolog/monolog": "^1.24", "phpunit/phpunit": "^4.8.35|^5.4.3", "psr/cache": "^1.0", "symfony/dotenv": "^3.4", "symfony/var-dumper": "^3.4"}, "suggest": {"ext-sockets": "To use client-side monitoring"}, "type": "library", "autoload": {"psr-4": {"AlibabaCloud\\Credentials\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Alibaba Cloud SDK", "email": "<EMAIL>", "homepage": "http://www.alibabacloud.com"}], "description": "Alibaba Cloud Credentials for PHP", "homepage": "https://www.alibabacloud.com/", "keywords": ["alibaba", "alibabacloud", "<PERSON><PERSON><PERSON>", "client", "cloud", "credentials", "library", "sdk", "tool"], "support": {"issues": "https://github.com/aliyun/credentials-php/issues", "source": "https://github.com/aliyun/credentials-php"}, "time": "2021-06-08T10:49:34+00:00"}, {"name": "alibabacloud/darabonba-openapi", "version": "0.2.8", "source": {"type": "git", "url": "https://github.com/alibabacloud-sdk-php/darabonba-openapi.git", "reference": "aabc61b3049caed6442ca332cf45d3b51af633ef"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/alibabacloud-sdk-php/darabonba-openapi/zipball/aabc61b3049caed6442ca332cf45d3b51af633ef", "reference": "aabc61b3049caed6442ca332cf45d3b51af633ef", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"alibabacloud/credentials": "^1.1", "alibabacloud/gateway-spi": "^1", "alibabacloud/openapi-util": "^0.1.10", "alibabacloud/tea-utils": "^0.2.16", "alibabacloud/tea-xml": "^0.2", "php": ">5.5"}, "type": "library", "autoload": {"psr-4": {"Darabonba\\OpenApi\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Alibaba Cloud SDK", "email": "<EMAIL>"}], "description": "Alibaba Cloud OpenApi Client", "support": {"issues": "https://github.com/alibabacloud-sdk-php/darabonba-openapi/issues", "source": "https://github.com/alibabacloud-sdk-php/darabonba-openapi/tree/0.2.8"}, "time": "2022-11-14T03:38:23+00:00"}, {"name": "alibabacloud/dkms-gcs-sdk", "version": "v0.2.2", "source": {"type": "git", "url": "https://github.com/aliyun/alibabacloud-dkms-gcs-php-sdk.git", "reference": "1266c073f0d62ad642e262ecdcd724835e9596d5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/aliyun/alibabacloud-dkms-gcs-php-sdk/zipball/1266c073f0d62ad642e262ecdcd724835e9596d5", "reference": "1266c073f0d62ad642e262ecdcd724835e9596d5", "shasum": ""}, "require": {"alibabacloud/tea": "^3.1", "alibabacloud/tea-utils": "^0.2.14", "ext-json": "*", "ext-openssl": "*", "google/protobuf": "^3.3", "php": ">=5.6"}, "type": "library", "autoload": {"psr-4": {"GPBMetadata\\": "openapi-util/src/Protobuf/GPBMetadata", "AlibabaCloud\\Dkms\\Gcs\\Sdk\\": "sdk/src/", "AlibabaCloud\\Dkms\\Gcs\\OpenApi\\": "openapi/src/", "AlibabaCloud\\Dkms\\Gcs\\OpenApi\\Util\\": "openapi-util/src/", "AlibabaCloud\\Dkms\\Gcs\\OpenApi\\Credential\\": "openapi-credential/src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Alibaba Cloud DKMS SDK", "email": "<EMAIL>"}], "description": "Aliyun Dkms Gcs SDK Library for PHP", "support": {"issues": "https://github.com/aliyun/alibabacloud-dkms-gcs-php-sdk/issues", "source": "https://github.com/aliyun/alibabacloud-dkms-gcs-php-sdk/tree/v0.2.2"}, "time": "2022-07-26T15:24:09+00:00"}, {"name": "alibabacloud/endpoint-util", "version": "0.1.1", "source": {"type": "git", "url": "https://github.com/alibabacloud-sdk-php/endpoint-util.git", "reference": "f3fe88a25d8df4faa3b0ae14ff202a9cc094e6c5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/alibabacloud-sdk-php/endpoint-util/zipball/f3fe88a25d8df4faa3b0ae14ff202a9cc094e6c5", "reference": "f3fe88a25d8df4faa3b0ae14ff202a9cc094e6c5", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">5.5"}, "require-dev": {"phpunit/phpunit": "^4.8.35|^5.4.3"}, "type": "library", "autoload": {"psr-4": {"AlibabaCloud\\Endpoint\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Alibaba Cloud SDK", "email": "<EMAIL>"}], "description": "Alibaba Cloud Endpoint Library for PHP", "support": {"source": "https://github.com/alibabacloud-sdk-php/endpoint-util/tree/0.1.1"}, "time": "2020-06-04T10:57:15+00:00"}, {"name": "alibabacloud/gateway-spi", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/alibabacloud-sdk-php/alibabacloud-gateway-spi.git", "reference": "7440f77750c329d8ab252db1d1d967314ccd1fcb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/alibabacloud-sdk-php/alibabacloud-gateway-spi/zipball/7440f77750c329d8ab252db1d1d967314ccd1fcb", "reference": "7440f77750c329d8ab252db1d1d967314ccd1fcb", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"alibabacloud/credentials": "^1.1", "php": ">5.5"}, "type": "library", "autoload": {"psr-4": {"Darabonba\\GatewaySpi\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Alibaba Cloud SDK", "email": "<EMAIL>"}], "description": "Alibaba Cloud Gateway SPI Client", "support": {"source": "https://github.com/alibabacloud-sdk-php/alibabacloud-gateway-spi/tree/1.0.0"}, "time": "2022-07-14T05:31:35+00:00"}, {"name": "alibabacloud/openapi-util", "version": "0.1.13", "source": {"type": "git", "url": "https://github.com/alibabacloud-sdk-php/openapi-util.git", "reference": "870e59984f05e104aa303c85b8214e339ba0a0ac"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/alibabacloud-sdk-php/openapi-util/zipball/870e59984f05e104aa303c85b8214e339ba0a0ac", "reference": "870e59984f05e104aa303c85b8214e339ba0a0ac", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"alibabacloud/tea": "^3.1", "alibabacloud/tea-utils": "^0.2", "lizhichao/one-sm": "^1.5", "php": ">5.5"}, "require-dev": {"phpunit/phpunit": "*"}, "type": "library", "autoload": {"psr-4": {"AlibabaCloud\\OpenApiUtil\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Alibaba Cloud SDK", "email": "<EMAIL>"}], "description": "Alibaba Cloud OpenApi <PERSON>", "support": {"issues": "https://github.com/alibabacloud-sdk-php/openapi-util/issues", "source": "https://github.com/alibabacloud-sdk-php/openapi-util/tree/0.2.0"}, "time": "2022-11-06T05:49:55+00:00"}, {"name": "alibabacloud/sdk", "version": "1.8.939", "source": {"type": "git", "url": "https://github.com/aliyun/openapi-sdk-php.git", "reference": "d315777da154c490e665000ab780c59283221d62"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/aliyun/openapi-sdk-php/zipball/d315777da154c490e665000ab780c59283221d62", "reference": "d315777da154c490e665000ab780c59283221d62", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"alibabacloud/client": "^1.5", "ext-curl": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-openssl": "*", "ext-simplexml": "*", "ext-xmlwriter": "*", "php": ">=5.5"}, "replace": {"alibabacloud/aas": "self.version", "alibabacloud/actiontrail": "self.version", "alibabacloud/adb": "self.version", "alibabacloud/aegis": "self.version", "alibabacloud/afs": "self.version", "alibabacloud/airec": "self.version", "alibabacloud/alidns": "self.version", "alibabacloud/alikafka": "self.version", "alibabacloud/alimt": "self.version", "alibabacloud/aliprobe": "self.version", "alibabacloud/aliyuncvc": "self.version", "alibabacloud/appmallsservice": "self.version", "alibabacloud/arms": "self.version", "alibabacloud/arms4finance": "self.version", "alibabacloud/baas": "self.version", "alibabacloud/batchcompute": "self.version", "alibabacloud/bss": "self.version", "alibabacloud/bssopenapi": "self.version", "alibabacloud/cas": "self.version", "alibabacloud/cbn": "self.version", "alibabacloud/ccc": "self.version", "alibabacloud/ccs": "self.version", "alibabacloud/cdn": "self.version", "alibabacloud/cds": "self.version", "alibabacloud/cf": "self.version", "alibabacloud/chatbot": "self.version", "alibabacloud/cloudapi": "self.version", "alibabacloud/cloudauth": "self.version", "alibabacloud/cloudesl": "self.version", "alibabacloud/cloudmarketing": "self.version", "alibabacloud/cloudphoto": "self.version", "alibabacloud/cloudwf": "self.version", "alibabacloud/cms": "self.version", "alibabacloud/commondriver": "self.version", "alibabacloud/companyreg": "self.version", "alibabacloud/cr": "self.version", "alibabacloud/crm": "self.version", "alibabacloud/cs": "self.version", "alibabacloud/csb": "self.version", "alibabacloud/cusanalyticsconline": "self.version", "alibabacloud/dataworkspublic": "self.version", "alibabacloud/dbs": "self.version", "alibabacloud/dcdn": "self.version", "alibabacloud/dds": "self.version", "alibabacloud/democenter": "self.version", "alibabacloud/dm": "self.version", "alibabacloud/dmsenterprise": "self.version", "alibabacloud/domain": "self.version", "alibabacloud/domainintl": "self.version", "alibabacloud/drcloud": "self.version", "alibabacloud/drds": "self.version", "alibabacloud/dts": "self.version", "alibabacloud/dybaseapi": "self.version", "alibabacloud/dyplsapi": "self.version", "alibabacloud/dypnsapi": "self.version", "alibabacloud/dysmsapi": "self.version", "alibabacloud/dyvmsapi": "self.version", "alibabacloud/eci": "self.version", "alibabacloud/ecs": "self.version", "alibabacloud/ecsinc": "self.version", "alibabacloud/edas": "self.version", "alibabacloud/ehpc": "self.version", "alibabacloud/elasticsearch": "self.version", "alibabacloud/emr": "self.version", "alibabacloud/ess": "self.version", "alibabacloud/facebody": "self.version", "alibabacloud/fnf": "self.version", "alibabacloud/foas": "self.version", "alibabacloud/ft": "self.version", "alibabacloud/goodstech": "self.version", "alibabacloud/gpdb": "self.version", "alibabacloud/green": "self.version", "alibabacloud/hbase": "self.version", "alibabacloud/hiknoengine": "self.version", "alibabacloud/hpc": "self.version", "alibabacloud/hsm": "self.version", "alibabacloud/httpdns": "self.version", "alibabacloud/idst": "self.version", "alibabacloud/imageaudit": "self.version", "alibabacloud/imageenhan": "self.version", "alibabacloud/imagerecog": "self.version", "alibabacloud/imagesearch": "self.version", "alibabacloud/imageseg": "self.version", "alibabacloud/imm": "self.version", "alibabacloud/industrybrain": "self.version", "alibabacloud/iot": "self.version", "alibabacloud/iqa": "self.version", "alibabacloud/itaas": "self.version", "alibabacloud/ivision": "self.version", "alibabacloud/ivpd": "self.version", "alibabacloud/jaq": "self.version", "alibabacloud/jarvis": "self.version", "alibabacloud/jarvispublic": "self.version", "alibabacloud/kms": "self.version", "alibabacloud/linkedmall": "self.version", "alibabacloud/linkface": "self.version", "alibabacloud/linkwan": "self.version", "alibabacloud/live": "self.version", "alibabacloud/lubancloud": "self.version", "alibabacloud/lubanruler": "self.version", "alibabacloud/market": "self.version", "alibabacloud/mopen": "self.version", "alibabacloud/mpserverless": "self.version", "alibabacloud/mts": "self.version", "alibabacloud/multimediaai": "self.version", "alibabacloud/nas": "self.version", "alibabacloud/netana": "self.version", "alibabacloud/nlp": "self.version", "alibabacloud/nlpautoml": "self.version", "alibabacloud/nlscloudmeta": "self.version", "alibabacloud/nlsfiletrans": "self.version", "alibabacloud/objectdet": "self.version", "alibabacloud/ocr": "self.version", "alibabacloud/ocs": "self.version", "alibabacloud/oms": "self.version", "alibabacloud/ons": "self.version", "alibabacloud/onsmqtt": "self.version", "alibabacloud/oos": "self.version", "alibabacloud/openanalytics": "self.version", "alibabacloud/ossadmin": "self.version", "alibabacloud/ots": "self.version", "alibabacloud/outboundbot": "self.version", "alibabacloud/petadata": "self.version", "alibabacloud/polardb": "self.version", "alibabacloud/productcatalog": "self.version", "alibabacloud/pts": "self.version", "alibabacloud/push": "self.version", "alibabacloud/pvtz": "self.version", "alibabacloud/qualitycheck": "self.version", "alibabacloud/ram": "self.version", "alibabacloud/rds": "self.version", "alibabacloud/reid": "self.version", "alibabacloud/retailcloud": "self.version", "alibabacloud/rkvstore": "self.version", "alibabacloud/ros": "self.version", "alibabacloud/rtc": "self.version", "alibabacloud/saf": "self.version", "alibabacloud/sas": "self.version", "alibabacloud/sasapi": "self.version", "alibabacloud/scdn": "self.version", "alibabacloud/schedulerx2": "self.version", "alibabacloud/skyeye": "self.version", "alibabacloud/slb": "self.version", "alibabacloud/smartag": "self.version", "alibabacloud/smc": "self.version", "alibabacloud/sms": "self.version", "alibabacloud/smsintl": "self.version", "alibabacloud/snsuapi": "self.version", "alibabacloud/sts": "self.version", "alibabacloud/taginner": "self.version", "alibabacloud/tesladam": "self.version", "alibabacloud/teslamaxcompute": "self.version", "alibabacloud/teslastream": "self.version", "alibabacloud/ubsms": "self.version", "alibabacloud/ubsmsinner": "self.version", "alibabacloud/uis": "self.version", "alibabacloud/unimkt": "self.version", "alibabacloud/visionai": "self.version", "alibabacloud/vod": "self.version", "alibabacloud/voicenavigator": "self.version", "alibabacloud/vpc": "self.version", "alibabacloud/vs": "self.version", "alibabacloud/wafopenapi": "self.version", "alibabacloud/welfareinner": "self.version", "alibabacloud/xspace": "self.version", "alibabacloud/xtrace": "self.version", "alibabacloud/yqbridge": "self.version", "alibabacloud/yundun": "self.version"}, "require-dev": {"composer/composer": "^1.8", "league/climate": "^3.2.4", "phpunit/phpunit": "^4.8", "symfony/dotenv": "^3.4", "symfony/var-dumper": "^3.4"}, "suggest": {"ext-sockets": "To use client-side monitoring"}, "type": "library", "autoload": {"psr-4": {"AlibabaCloud\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Alibaba Cloud SDK", "email": "<EMAIL>", "homepage": "http://www.alibabacloud.com"}], "description": "Alibaba Cloud SDK for PHP - Easier to Use Alibaba Cloud in your PHP project", "homepage": "https://www.alibabacloud.com/", "keywords": ["alibaba", "alibabacloud", "<PERSON><PERSON><PERSON>", "cloud", "library", "sdk"], "support": {"issues": "https://github.com/aliyun/openapi-sdk-php/issues", "source": "https://github.com/aliyun/openapi-sdk-php"}, "time": "2021-04-16T03:27:45+00:00"}, {"name": "alibabacloud/tea", "version": "3.1.24", "source": {"type": "git", "url": "https://github.com/aliyun/tea-php.git", "reference": "bb33395f47db3847d1940d6eb8ba1e56cd0623cb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/aliyun/tea-php/zipball/bb33395f47db3847d1940d6eb8ba1e56cd0623cb", "reference": "bb33395f47db3847d1940d6eb8ba1e56cd0623cb", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"adbario/php-dot-notation": "^2.3", "ext-curl": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-openssl": "*", "ext-simplexml": "*", "ext-xmlwriter": "*", "guzzlehttp/guzzle": "^6.3|^7.0", "php": ">=5.5"}, "require-dev": {"phpunit/phpunit": "*", "symfony/dotenv": "^3.4", "symfony/var-dumper": "^3.4"}, "suggest": {"ext-sockets": "To use client-side monitoring"}, "type": "library", "autoload": {"psr-4": {"AlibabaCloud\\Tea\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Alibaba Cloud SDK", "email": "<EMAIL>", "homepage": "http://www.alibabacloud.com"}], "description": "Client of Tea for PHP", "homepage": "https://www.alibabacloud.com/", "keywords": ["alibabacloud", "client", "cloud", "tea"], "support": {"issues": "https://github.com/aliyun/tea-php/issues", "source": "https://github.com/aliyun/tea-php"}, "time": "2022-07-18T11:27:29+00:00"}, {"name": "alibabacloud/tea-utils", "version": "0.2.19", "source": {"type": "git", "url": "https://github.com/alibabacloud-sdk-php/tea-utils.git", "reference": "8dfc1a93e9415818e93a621b644abbb84981aea4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/alibabacloud-sdk-php/tea-utils/zipball/8dfc1a93e9415818e93a621b644abbb84981aea4", "reference": "8dfc1a93e9415818e93a621b644abbb84981aea4", "shasum": ""}, "require": {"alibabacloud/tea": "^3.1", "php": ">5.5"}, "type": "library", "autoload": {"psr-4": {"AlibabaCloud\\Tea\\Utils\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Alibaba Cloud SDK", "email": "<EMAIL>"}], "description": "Alibaba Cloud Tea Utils for PHP", "support": {"issues": "https://github.com/aliyun/tea-util/issues", "source": "https://github.com/aliyun/tea-util"}, "time": "2023-06-26T09:49:19+00:00"}, {"name": "alibabacloud/tea-xml", "version": "0.2.3", "source": {"type": "git", "url": "https://github.com/alibabacloud-sdk-php/tea-xml.git", "reference": "4bd2303d71c968cb7ae4e487c5fa3023aed3ff3b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/alibabacloud-sdk-php/tea-xml/zipball/4bd2303d71c968cb7ae4e487c5fa3023aed3ff3b", "reference": "4bd2303d71c968cb7ae4e487c5fa3023aed3ff3b", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">5.5"}, "require-dev": {"phpunit/phpunit": "^4.8.35|^5.4.3", "symfony/var-dumper": "*"}, "type": "library", "autoload": {"psr-4": {"AlibabaCloud\\Tea\\XML\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Alibaba Cloud SDK", "email": "<EMAIL>"}], "description": "Alibaba Cloud Tea XML Library for PHP", "support": {"source": "https://github.com/alibabacloud-sdk-php/tea-xml/tree/0.2.3"}, "time": "2021-12-08T06:43:00+00:00"}, {"name": "aliyun/aliyun-mns-php-sdk", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/aliyun/aliyun-mns-php-sdk.git", "reference": "f4b32a964d6e411b6dcc0137e00e94c47dd8b3e1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/aliyun/aliyun-mns-php-sdk/zipball/f4b32a964d6e411b6dcc0137e00e94c47dd8b3e1", "reference": "f4b32a964d6e411b6dcc0137e00e94c47dd8b3e1", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"guzzlehttp/guzzle": ">=6.0.0", "php": ">=5.5.0", "psr/http-message": "^1.0"}, "type": "library", "autoload": {"psr-4": {"AliyunMNS\\": "AliyunMNS/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Aliyun MNS Team", "homepage": "http://www.aliyun.com/product/mns"}], "description": "Aliyun Message and Notification Service SDK for PHP, PHP>=5.5.0", "homepage": "https://github.com/aliyun/aliyun-mns-php-sdk", "keywords": ["<PERSON><PERSON><PERSON>", "message", "message service", "mns", "notification"], "time": "2019-06-14T04:35:52+00:00"}, {"name": "aliyunmq/mq-http-sdk", "version": "1.0.3", "source": {"type": "git", "url": "https://github.com/aliyunmq/mq-http-php-sdk.git", "reference": "b96c9814221f078847a84921960f67eab2a1ef0e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/aliyunmq/mq-http-php-sdk/zipball/b96c9814221f078847a84921960f67eab2a1ef0e", "reference": "b96c9814221f078847a84921960f67eab2a1ef0e", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"guzzlehttp/guzzle": ">=6.0.0", "php": ">=5.5.0"}, "type": "library", "autoload": {"psr-4": {"MQ\\": "MQ/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Aliyun Message Queue(MQ) Http PHP SDK, PHP>=5.5.0", "homepage": "https://github.com/aliyunmq/mq-http-php-sdk", "keywords": ["MQ", "alicloud", "<PERSON><PERSON><PERSON>", "message", "message queue", "queue"], "support": {"issues": "https://github.com/aliyunmq/mq-http-php-sdk/issues", "source": "https://github.com/aliyunmq/mq-http-php-sdk/tree/1.0.3"}, "time": "2021-01-05T06:03:55+00:00"}, {"name": "clagiordano/weblibs-configmanager", "version": "v1.1.0", "source": {"type": "git", "url": "https://github.com/clagiordano/weblibs-configmanager.git", "reference": "ecf584f5b3a27929175ff0abdba52f0131bef795"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/clagiordano/weblibs-configmanager/zipball/ecf584f5b3a27929175ff0abdba52f0131bef795", "reference": "ecf584f5b3a27929175ff0abdba52f0131bef795", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.4"}, "require-dev": {"clagiordano/phpunit-result-printer": "^1", "phpunit/phpunit": "^4.8"}, "type": "library", "autoload": {"psr-4": {"clagiordano\\weblibs\\configmanager\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0-or-later"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "weblibs-configmanager is a tool library for easily read and access to php config array file and direct read/write configuration file / object", "keywords": ["clag<PERSON><PERSON><PERSON>", "configuration", "manager", "tool", "weblibs"], "support": {"issues": "https://github.com/clagiordano/weblibs-configmanager/issues", "source": "https://github.com/clagiordano/weblibs-configmanager/tree/v1.1.0"}, "time": "2020-07-20T20:39:25+00:00"}, {"name": "clue/stream-filter", "version": "v1.5.0", "source": {"type": "git", "url": "https://github.com/clue/stream-filter.git", "reference": "aeb7d8ea49c7963d3b581378955dbf5bc49aa320"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/clue/stream-filter/zipball/aeb7d8ea49c7963d3b581378955dbf5bc49aa320", "reference": "aeb7d8ea49c7963d3b581378955dbf5bc49aa320", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3"}, "require-dev": {"phpunit/phpunit": "^9.3 || ^5.7 || ^4.8.36"}, "type": "library", "autoload": {"psr-4": {"Clue\\StreamFilter\\": "src/"}, "files": ["src/functions_include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A simple and modern approach to stream filtering in PHP", "homepage": "https://github.com/clue/php-stream-filter", "keywords": ["bucket brigade", "callback", "filter", "php_user_filter", "stream", "stream_filter_append", "stream_filter_register"], "support": {"issues": "https://github.com/clue/stream-filter/issues", "source": "https://github.com/clue/stream-filter/tree/v1.5.0"}, "funding": [{"url": "https://clue.engineering/support", "type": "custom"}, {"url": "https://github.com/clue", "type": "github"}], "time": "2020-10-02T12:38:20+00:00"}, {"name": "composer/ca-bundle", "version": "1.2.9", "source": {"type": "git", "url": "https://github.com/composer/ca-bundle.git", "reference": "78a0e288fdcebf92aa2318a8d3656168da6ac1a5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/ca-bundle/zipball/78a0e288fdcebf92aa2318a8d3656168da6ac1a5", "reference": "78a0e288fdcebf92aa2318a8d3656168da6ac1a5", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-openssl": "*", "ext-pcre": "*", "php": "^5.3.2 || ^7.0 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^0.12.55", "psr/log": "^1.0", "symfony/phpunit-bridge": "^4.2 || ^5", "symfony/process": "^2.5 || ^3.0 || ^4.0 || ^5.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.x-dev"}}, "autoload": {"psr-4": {"Composer\\CaBundle\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "Lets you find a path to the system CA bundle, and includes a fallback to the Mozilla CA bundle.", "keywords": ["cabundle", "cacert", "certificate", "ssl", "tls"], "support": {"irc": "irc://irc.freenode.org/composer", "issues": "https://github.com/composer/ca-bundle/issues", "source": "https://github.com/composer/ca-bundle/tree/1.2.9"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2021-01-12T12:10:35+00:00"}, {"name": "composer/semver", "version": "3.4.0", "source": {"type": "git", "url": "https://github.com/composer/semver.git", "reference": "35e8d0af4486141bc745f23a29cc2091eb624a32"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/semver/zipball/35e8d0af4486141bc745f23a29cc2091eb624a32", "reference": "35e8d0af4486141bc745f23a29cc2091eb624a32", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^5.3.2 || ^7.0 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^1.4", "symfony/phpunit-bridge": "^4.2 || ^5"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.x-dev"}}, "autoload": {"psr-4": {"Composer\\Semver\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.naderman.de"}, {"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://robbast.nl"}], "description": "Semver library that offers utilities, version constraint parsing and validation.", "keywords": ["semantic", "semver", "validation", "versioning"], "support": {"irc": "ircs://irc.libera.chat:6697/composer", "issues": "https://github.com/composer/semver/issues", "source": "https://github.com/composer/semver/tree/3.4.0"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2023-08-31T09:50:34+00:00"}, {"name": "doctrine/inflector", "version": "2.0.3", "source": {"type": "git", "url": "https://github.com/doctrine/inflector.git", "reference": "9cf661f4eb38f7c881cac67c75ea9b00bf97b210"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/inflector/zipball/9cf661f4eb38f7c881cac67c75ea9b00bf97b210", "reference": "9cf661f4eb38f7c881cac67c75ea9b00bf97b210", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^7.0", "phpstan/phpstan": "^0.11", "phpstan/phpstan-phpunit": "^0.11", "phpstan/phpstan-strict-rules": "^0.11", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Inflector\\": "lib/Doctrine/Inflector"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Inflector is a small library that can perform string manipulations with regard to upper/lowercase and singular/plural forms of words.", "homepage": "https://www.doctrine-project.org/projects/inflector.html", "keywords": ["inflection", "inflector", "lowercase", "manipulation", "php", "plural", "singular", "strings", "uppercase", "words"], "support": {"issues": "https://github.com/doctrine/inflector/issues", "source": "https://github.com/doctrine/inflector/tree/2.0.x"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Finflector", "type": "tidelift"}], "time": "2020-05-29T15:13:26+00:00"}, {"name": "elasticsearch/elasticsearch", "version": "v7.17.1", "source": {"type": "git", "url": "**************:elastic/elasticsearch-php.git", "reference": "f1b8918f411b837ce5f6325e829a73518fd50367"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/elastic/elasticsearch-php/zipball/f1b8918f411b837ce5f6325e829a73518fd50367", "reference": "f1b8918f411b837ce5f6325e829a73518fd50367", "shasum": ""}, "require": {"ext-json": ">=1.3.7", "ezimuel/ringphp": "^1.1.2", "php": "^7.3 || ^8.0", "psr/log": "^1|^2|^3"}, "require-dev": {"ext-yaml": "*", "ext-zip": "*", "mockery/mockery": "^1.2", "phpstan/phpstan": "^0.12", "phpunit/phpunit": "^9.3", "squizlabs/php_codesniffer": "^3.4", "symfony/finder": "~4.0"}, "suggest": {"ext-curl": "*", "monolog/monolog": "Allows for client-level logging and tracing"}, "type": "library", "autoload": {"files": ["src/autoload.php"], "psr-4": {"Elasticsearch\\": "src/Elasticsearch/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0", "LGPL-2.1-only"], "authors": [{"name": "<PERSON>"}, {"name": "<PERSON>"}], "description": "PHP Client for Elasticsearch", "keywords": ["client", "elasticsearch", "search"], "time": "2022-09-30T12:28:55+00:00"}, {"name": "ezimuel/guzzlestreams", "version": "3.0.1", "source": {"type": "git", "url": "https://github.com/ezimuel/guzzlestreams.git", "reference": "abe3791d231167f14eb80d413420d1eab91163a8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ezimuel/guzzlestreams/zipball/abe3791d231167f14eb80d413420d1eab91163a8", "reference": "abe3791d231167f14eb80d413420d1eab91163a8", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "~4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\Stream\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Fork of guzzle/streams (abandoned) to be used with elasticsearch-php", "homepage": "http://guzzlephp.org/", "keywords": ["Guzzle", "stream"], "support": {"source": "https://github.com/ezimuel/guzzlestreams/tree/3.0.1"}, "time": "2020-02-14T23:11:50+00:00"}, {"name": "ezimuel/ringphp", "version": "1.2.0", "source": {"type": "git", "url": "https://github.com/ezimuel/ringphp.git", "reference": "92b8161404ab1ad84059ebed41d9f757e897ce74"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ezimuel/ringphp/zipball/92b8161404ab1ad84059ebed41d9f757e897ce74", "reference": "92b8161404ab1ad84059ebed41d9f757e897ce74", "shasum": ""}, "require": {"ezimuel/guzzlestreams": "^3.0.1", "php": ">=5.4.0", "react/promise": "~2.0"}, "replace": {"guzzlehttp/ringphp": "self.version"}, "require-dev": {"ext-curl": "*", "phpunit/phpunit": "~9.0"}, "suggest": {"ext-curl": "Guzzle will use specific adapters if cURL is present"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\Ring\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Fork of guzzle/RingPHP (abandoned) to be used with elasticsearch-php", "support": {"source": "https://github.com/ezimuel/ringphp/tree/1.2.0"}, "time": "2021-11-16T11:51:30+00:00"}, {"name": "ezyang/htmlpurifier", "version": "v4.14.0", "source": {"type": "git", "url": "https://github.com/ezyang/htmlpurifier.git", "reference": "12ab42bd6e742c70c0a52f7b82477fcd44e64b75"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ezyang/htmlpurifier/zipball/12ab42bd6e742c70c0a52f7b82477fcd44e64b75", "reference": "12ab42bd6e742c70c0a52f7b82477fcd44e64b75", "shasum": ""}, "require": {"php": ">=5.2"}, "type": "library", "autoload": {"psr-0": {"HTMLPurifier": "library/"}, "files": ["library/HTMLPurifier.composer.php"], "exclude-from-classmap": ["/library/HTMLPurifier/Language/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-2.1-or-later"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://ezyang.com"}], "description": "Standards compliant HTML filter written in PHP", "homepage": "http://htmlpurifier.org/", "keywords": ["html"], "support": {"issues": "https://github.com/ezyang/htmlpurifier/issues", "source": "https://github.com/ezyang/htmlpurifier/tree/v4.14.0"}, "time": "2021-12-25T01:21:49+00:00"}, {"name": "firebase/php-jwt", "version": "v5.5.1", "source": {"type": "git", "url": "https://github.com/firebase/php-jwt.git", "reference": "83b609028194aa042ea33b5af2d41a7427de80e6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/firebase/php-jwt/zipball/83b609028194aa042ea33b5af2d41a7427de80e6", "reference": "83b609028194aa042ea33b5af2d41a7427de80e6", "shasum": ""}, "require": {"php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": ">=4.8 <=9"}, "suggest": {"paragonie/sodium_compat": "Support EdDSA (Ed25519) signatures when libsodium is not present"}, "type": "library", "autoload": {"psr-4": {"Firebase\\JWT\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A simple library to encode and decode JSON Web Tokens (JWT) in PHP. Should conform to the current spec.", "homepage": "https://github.com/firebase/php-jwt", "keywords": ["jwt", "php"], "support": {"issues": "https://github.com/firebase/php-jwt/issues", "source": "https://github.com/firebase/php-jwt/tree/v5.5.1"}, "time": "2021-11-08T20:18:51+00:00"}, {"name": "giggsey/libphonenumber-for-php", "version": "8.13.2", "source": {"type": "git", "url": "https://github.com/giggsey/libphonenumber-for-php.git", "reference": "362e04987729b9ef876c5e1661119b97894eeabd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/giggsey/libphonenumber-for-php/zipball/362e04987729b9ef876c5e1661119b97894eeabd", "reference": "362e04987729b9ef876c5e1661119b97894eeabd", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"giggsey/locale": "^1.7|^2.0", "php": ">=5.3.2", "symfony/polyfill-mbstring": "^1.17"}, "require-dev": {"pear/pear-core-minimal": "^1.9", "pear/pear_exception": "^1.0", "pear/versioncontrol_git": "^0.5", "phing/phing": "^2.7", "php-coveralls/php-coveralls": "^1.0|^2.0", "symfony/console": "^2.8|^3.0|^v4.4|^v5.2", "symfony/phpunit-bridge": "^4.2 || ^5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "8.x-dev"}}, "autoload": {"psr-4": {"libphonenumber\\": "src/"}, "exclude-from-classmap": ["/src/data/", "/src/carrier/data/", "/src/geocoding/data/", "/src/timezone/data/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://giggsey.com/"}], "description": "PHP Port of Google's libphonenumber", "homepage": "https://github.com/giggsey/libphonenumber-for-php", "keywords": ["geocoding", "geolocation", "libphonenumber", "mobile", "phonenumber", "validation"], "support": {"issues": "https://github.com/giggsey/libphonenumber-for-php/issues", "source": "https://github.com/giggsey/libphonenumber-for-php"}, "time": "2022-12-08T20:53:40+00:00"}, {"name": "giggsey/locale", "version": "2.2", "source": {"type": "git", "url": "https://github.com/giggsey/Locale.git", "reference": "9c1dca769253f6a3e81f9a5c167f53b6a54ab635"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/giggsey/Locale/zipball/9c1dca769253f6a3e81f9a5c167f53b6a54ab635", "reference": "9c1dca769253f6a3e81f9a5c167f53b6a54ab635", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.2"}, "require-dev": {"ext-json": "*", "pear/pear-core-minimal": "^1.9", "pear/pear_exception": "^1.0", "pear/versioncontrol_git": "^0.5", "phing/phing": "^2.7", "php-coveralls/php-coveralls": "^2.0", "phpunit/phpunit": "^8.5|^9.5", "symfony/console": "^5.0", "symfony/filesystem": "^5.0", "symfony/finder": "^5.0", "symfony/process": "^5.0"}, "type": "library", "autoload": {"psr-4": {"Giggsey\\Locale\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://giggsey.com/"}], "description": "Locale functions required by libphonenumber-for-php", "support": {"issues": "https://github.com/giggsey/Locale/issues", "source": "https://github.com/giggsey/Locale/tree/2.2"}, "time": "2022-04-06T07:33:59+00:00"}, {"name": "google/apiclient", "version": "v2.4.0", "source": {"type": "git", "url": "https://github.com/googleapis/google-api-php-client.git", "reference": "cd3c37998020d91ae4eafca4f26a92da4dabba83"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/googleapis/google-api-php-client/zipball/cd3c37998020d91ae4eafca4f26a92da4dabba83", "reference": "cd3c37998020d91ae4eafca4f26a92da4dabba83", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"firebase/php-jwt": "~2.0||~3.0||~4.0||~5.0", "google/apiclient-services": "~0.13", "google/auth": "^1.0", "guzzlehttp/guzzle": "~5.3.1||~6.0", "guzzlehttp/psr7": "^1.2", "monolog/monolog": "^1.17|^2.0", "php": ">=5.4", "phpseclib/phpseclib": "~0.3.10||~2.0"}, "require-dev": {"cache/filesystem-adapter": "^0.3.2", "dealerdirect/phpcodesniffer-composer-installer": "^0.5.0", "phpcompatibility/php-compatibility": "^9.2", "phpunit/phpunit": "~4.8.36", "squizlabs/php_codesniffer": "~2.3", "symfony/css-selector": "~2.1", "symfony/dom-crawler": "~2.1"}, "suggest": {"cache/filesystem-adapter": "For caching certs and tokens (using Google_Client::setCache)"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.x-dev"}}, "autoload": {"psr-0": {"Google_": "src/"}, "classmap": ["src/Google/Service/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "description": "Client library for Google APIs", "homepage": "http://developers.google.com/api-client-library/php", "keywords": ["google"], "support": {"issues": "https://github.com/googleapis/google-api-php-client/issues", "source": "https://github.com/googleapis/google-api-php-client/tree/v2.4.0"}, "time": "2019-09-11T17:38:10+00:00"}, {"name": "google/apiclient-services", "version": "v0.121", "source": {"type": "git", "url": "https://github.com/googleapis/google-api-php-client-services.git", "reference": "a33fd9ed19fe4e27f2ccebbf45646f38e7cb95af"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/googleapis/google-api-php-client-services/zipball/a33fd9ed19fe4e27f2ccebbf45646f38e7cb95af", "reference": "a33fd9ed19fe4e27f2ccebbf45646f38e7cb95af", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.4"}, "require-dev": {"phpunit/phpunit": "~4.8"}, "type": "library", "autoload": {"psr-0": {"Google_Service_": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "description": "Client library for Google APIs", "homepage": "http://developers.google.com/api-client-library/php", "keywords": ["google"], "time": "2019-11-03T00:23:34+00:00"}, {"name": "google/auth", "version": "v1.26.0", "source": {"type": "git", "url": "https://github.com/googleapis/google-auth-library-php.git", "reference": "f1f0d0319e2e7750ebfaa523c78819792a9ed9f7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/googleapis/google-auth-library-php/zipball/f1f0d0319e2e7750ebfaa523c78819792a9ed9f7", "reference": "f1f0d0319e2e7750ebfaa523c78819792a9ed9f7", "shasum": ""}, "require": {"firebase/php-jwt": "^5.5||^6.0", "guzzlehttp/guzzle": "^6.2.1|^7.0", "guzzlehttp/psr7": "^1.7|^2.0", "php": "^7.1||^8.0", "psr/cache": "^1.0|^2.0|^3.0", "psr/http-message": "^1.0"}, "require-dev": {"guzzlehttp/promises": "0.1.1|^1.3", "kelvinmo/simplejwt": "0.7.0", "phpseclib/phpseclib": "^2.0.31||^3.0", "phpspec/prophecy-phpunit": "^1.1||^2.0", "phpunit/phpunit": "^7.5||^9.0.0", "sebastian/comparator": ">=1.2.3", "squizlabs/php_codesniffer": "^3.5"}, "suggest": {"phpseclib/phpseclib": "May be used in place of OpenSSL for signing strings or for token management. Please require version ^2."}, "type": "library", "autoload": {"psr-4": {"Google\\Auth\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "description": "Google Auth Library for PHP", "homepage": "http://github.com/google/google-auth-library-php", "keywords": ["Authentication", "google", "oauth2"], "support": {"docs": "https://googleapis.github.io/google-auth-library-php/main/", "issues": "https://github.com/googleapis/google-auth-library-php/issues", "source": "https://github.com/googleapis/google-auth-library-php/tree/v1.26.0"}, "time": "2023-04-05T15:11:57+00:00"}, {"name": "google/common-protos", "version": "1.4.0", "source": {"type": "git", "url": "https://github.com/googleapis/common-protos-php.git", "reference": "b1ee63636d94fe88f6cff600a0f23fae06b6fa2e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/googleapis/common-protos-php/zipball/b1ee63636d94fe88f6cff600a0f23fae06b6fa2e", "reference": "b1ee63636d94fe88f6cff600a0f23fae06b6fa2e", "shasum": ""}, "require": {"google/protobuf": "^3.6.1"}, "require-dev": {"phpunit/phpunit": "^4.8.36", "sami/sami": "*"}, "type": "library", "autoload": {"psr-4": {"Google\\": "src", "GPBMetadata\\Google\\": "metadata"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "description": "Google API Common Protos for PHP", "homepage": "https://github.com/googleapis/common-protos-php", "keywords": ["google"], "support": {"issues": "https://github.com/googleapis/common-protos-php/issues", "source": "https://github.com/googleapis/common-protos-php/tree/1.4.0"}, "time": "2021-11-18T21:49:24+00:00"}, {"name": "google/gax", "version": "v1.19.1", "source": {"type": "git", "url": "https://github.com/googleapis/gax-php.git", "reference": "30f6b307faa9858bf58d967664467098dbbc354f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/googleapis/gax-php/zipball/30f6b307faa9858bf58d967664467098dbbc354f", "reference": "30f6b307faa9858bf58d967664467098dbbc354f", "shasum": ""}, "require": {"google/auth": "1.19.1||^1.25.0", "google/common-protos": "^1.3.1||^2.0||^3.0", "google/grpc-gcp": "^0.2", "google/longrunning": "^0.2", "google/protobuf": "^3.21.4", "grpc/grpc": "^1.13", "guzzlehttp/promises": "^1.3", "guzzlehttp/psr7": "^1.7.0||^2", "php": ">=7.0"}, "conflict": {"ext-protobuf": "<3.7.0"}, "require-dev": {"phpspec/prophecy": "^1.10", "phpunit/phpunit": "^5.5||^8.5", "squizlabs/php_codesniffer": "3.*", "yoast/phpunit-polyfills": "^1.0"}, "type": "library", "autoload": {"psr-4": {"Google\\ApiCore\\": "src", "GPBMetadata\\ApiCore\\": "metadata/ApiCore"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Google API Core for PHP", "homepage": "https://github.com/googleapis/gax-php", "keywords": ["google"], "support": {"issues": "https://github.com/googleapis/gax-php/issues", "source": "https://github.com/googleapis/gax-php/tree/v1.19.1"}, "time": "2023-03-16T19:58:19+00:00"}, {"name": "google/grpc-gcp", "version": "v0.2.1", "source": {"type": "git", "url": "https://github.com/GoogleCloudPlatform/grpc-gcp-php.git", "reference": "899d0112812a812df7692617a59f4076f0d01719"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/GoogleCloudPlatform/grpc-gcp-php/zipball/899d0112812a812df7692617a59f4076f0d01719", "reference": "899d0112812a812df7692617a59f4076f0d01719", "shasum": ""}, "require": {"google/auth": "^1.3", "google/protobuf": "^v3.3.0", "grpc/grpc": "^v1.13.0", "php": ">=5.5.0", "psr/cache": "^1.0.1||^2.0.0||^3.0.0"}, "require-dev": {"google/cloud-spanner": "^1.7", "phpunit/phpunit": "4.8.36"}, "type": "library", "autoload": {"psr-4": {"Grpc\\Gcp\\": "src/"}, "classmap": ["src/generated/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "description": "gRPC GCP library for channel management", "support": {"issues": "https://github.com/GoogleCloudPlatform/grpc-gcp-php/issues", "source": "https://github.com/GoogleCloudPlatform/grpc-gcp-php/tree/v0.2.1"}, "time": "2022-10-11T15:54:47+00:00"}, {"name": "google/longrunning", "version": "v0.2.5", "source": {"type": "git", "url": "https://github.com/googleapis/php-longrunning.git", "reference": "557817921225911100ea04ed8c59a4f8205b62f6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/googleapis/php-longrunning/zipball/557817921225911100ea04ed8c59a4f8205b62f6", "reference": "557817921225911100ea04ed8c59a4f8205b62f6", "shasum": ""}, "require-dev": {"google/gax": "^1.13.0", "phpunit/phpunit": "^4.8|^5.0|^8.0"}, "type": "library", "extra": {"component": {"id": "longrunning", "path": "<PERSON><PERSON><PERSON>ning", "entry": null, "target": "googleapis/php-longrunning"}}, "autoload": {"psr-4": {"Google\\LongRunning\\": "src/LongRunning", "Google\\ApiCore\\LongRunning\\": "src/ApiCore/LongRunning", "GPBMetadata\\Google\\Longrunning\\": "metadata/Longrunning"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "description": "Google LongRunning Client for PHP", "support": {"source": "https://github.com/googleapis/php-longrunning/tree/v0.2.5"}, "time": "2023-03-25T14:54:11+00:00"}, {"name": "google/protobuf", "version": "v3.22.2", "source": {"type": "git", "url": "https://github.com/protocolbuffers/protobuf-php.git", "reference": "ff28a64946708e13f2be627b5e5561f247ecf95c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/protocolbuffers/protobuf-php/zipball/ff28a64946708e13f2be627b5e5561f247ecf95c", "reference": "ff28a64946708e13f2be627b5e5561f247ecf95c", "shasum": ""}, "require": {"php": ">=7.0.0"}, "require-dev": {"phpunit/phpunit": ">=5.0.0"}, "suggest": {"ext-bcmath": "Need to support JSON deserialization"}, "type": "library", "autoload": {"psr-4": {"Google\\Protobuf\\": "src/Google/Protobuf", "GPBMetadata\\Google\\Protobuf\\": "src/GPBMetadata/Google/Protobuf"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "proto library for PHP", "homepage": "https://developers.google.com/protocol-buffers/", "keywords": ["proto"], "support": {"source": "https://github.com/protocolbuffers/protobuf-php/tree/v3.22.2"}, "time": "2023-03-10T17:52:03+00:00"}, {"name": "googleads/google-ads-php", "version": "v23.1.0", "source": {"type": "git", "url": "https://github.com/googleads/google-ads-php.git", "reference": "68ced4c6268ff1f4ec037a575246f300675bb7f1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/googleads/google-ads-php/zipball/68ced4c6268ff1f4ec037a575246f300675bb7f1", "reference": "68ced4c6268ff1f4ec037a575246f300675bb7f1", "shasum": ""}, "require": {"google/gax": "^1.19.1", "google/protobuf": "^3.21.5 || ^4.26", "grpc/grpc": "^1.36.0", "monolog/monolog": "^1.26 || ^2.0 || ^3.0", "php": ">=8.1"}, "require-dev": {"composer/composer": "^2.0", "ext-bcmath": "*", "ext-grpc": "*", "ext-protobuf": "*", "phpunit/phpunit": "^9.5", "react/http": "^1.2.0", "squizlabs/php_codesniffer": "^3.5", "ulrichsg/getopt-php": "^3.4"}, "suggest": {"ext-grpc": "To be able to use the gRPC transport, use the C implementation of gRPC", "ext-protobuf": "For better performance, use the C implementation of Protobuf", "google/protobuf": "In case the C implementation of Protobuf is not suitable, use the PHP one", "grpc/grpc": "In case the C implementation of gRPC is not suitable, use the PHP one to enable other transports", "react/http": "To run the AuthenticateInWebApplication.php example"}, "type": "library", "autoload": {"psr-4": {"Google\\Ads\\GoogleAds\\": "src/Google/Ads/GoogleAds", "GPBMetadata\\Google\\Ads\\GoogleAds\\": "metadata/Google/Ads/GoogleAds"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Google", "homepage": "https://github.com/googleads/google-ads-php/contributors"}], "description": "Google Ads API client for PHP", "homepage": "https://github.com/googleads/google-ads-php", "support": {"issues": "https://github.com/googleads/google-ads-php/issues", "source": "https://github.com/googleads/google-ads-php/tree/v23.1.0"}, "time": "2024-06-07T16:51:37+00:00"}, {"name": "greenlion/php-sql-parser", "version": "v4.6.0", "source": {"type": "git", "url": "https://github.com/greenlion/PHP-SQL-Parser.git", "reference": "f0e4645eb1612f0a295e3d35bda4c7740ae8c366"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/greenlion/PHP-SQL-Parser/zipball/f0e4645eb1612f0a295e3d35bda4c7740ae8c366", "reference": "f0e4645eb1612f0a295e3d35bda4c7740ae8c366", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3.2"}, "require-dev": {"analog/analog": "^1.0.6", "phpunit/phpunit": "^9.5.13", "squizlabs/php_codesniffer": "^1.5.1"}, "type": "library", "autoload": {"psr-0": {"PHPSQLParser\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://code.google.com/u/<EMAIL>/", "role": "Owner"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.phosco.info", "role": "Committer"}], "description": "A pure PHP SQL (non validating) parser w/ focus on MySQL dialect of SQL", "homepage": "https://github.com/greenlion/PHP-SQL-Parser", "keywords": ["creator", "mysql", "parser", "sql"], "support": {"issues": "https://github.com/greenlion/PHP-SQL-Parser/issues", "source": "https://github.com/greenlion/PHP-SQL-Parser"}, "time": "2023-03-09T20:54:23+00:00"}, {"name": "grpc/grpc", "version": "1.52.0", "source": {"type": "git", "url": "https://github.com/grpc/grpc-php.git", "reference": "98394cd601a587ca68294e6209bd713856969105"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/grpc/grpc-php/zipball/98394cd601a587ca68294e6209bd713856969105", "reference": "98394cd601a587ca68294e6209bd713856969105", "shasum": ""}, "require": {"php": ">=7.0.0"}, "require-dev": {"google/auth": "^v1.3.0"}, "suggest": {"ext-protobuf": "For better performance, install the protobuf C extension.", "google/protobuf": "To get started using grpc quickly, install the native protobuf library."}, "type": "library", "autoload": {"psr-4": {"Grpc\\": "src/lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "description": "gRPC library for PHP", "homepage": "https://grpc.io", "keywords": ["rpc"], "support": {"source": "https://github.com/grpc/grpc-php/tree/v1.52.0"}, "time": "2023-02-25T05:20:08+00:00"}, {"name": "guzzlehttp/guzzle", "version": "6.5.8", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle.git", "reference": "a52f0440530b54fa079ce76e8c5d196a42cad981"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle/zipball/a52f0440530b54fa079ce76e8c5d196a42cad981", "reference": "a52f0440530b54fa079ce76e8c5d196a42cad981", "shasum": ""}, "require": {"ext-json": "*", "guzzlehttp/promises": "^1.0", "guzzlehttp/psr7": "^1.9", "php": ">=5.5", "symfony/polyfill-intl-idn": "^1.17"}, "require-dev": {"ext-curl": "*", "phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.4 || ^7.0", "psr/log": "^1.1"}, "suggest": {"psr/log": "Required for using the Log middleware"}, "type": "library", "extra": {"branch-alias": {"dev-master": "6.5-dev"}}, "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "jereme<PERSON>@gmail.com", "homepage": "https://github.com/jeremeamia"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle is a PHP HTTP client library", "homepage": "http://guzzlephp.org/", "keywords": ["client", "curl", "framework", "http", "http client", "rest", "web service"], "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/6.5.8"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/guzzle", "type": "tidelift"}], "time": "2022-06-20T22:16:07+00:00"}, {"name": "guzzlehttp/promises", "version": "1.5.2", "source": {"type": "git", "url": "https://github.com/guzzle/promises.git", "reference": "b94b2807d85443f9719887892882d0329d1e2598"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/promises/zipball/b94b2807d85443f9719887892882d0329d1e2598", "reference": "b94b2807d85443f9719887892882d0329d1e2598", "shasum": ""}, "require": {"php": ">=5.5"}, "require-dev": {"symfony/phpunit-bridge": "^4.4 || ^5.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.5-dev"}}, "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle promises library", "keywords": ["promise"], "support": {"issues": "https://github.com/guzzle/promises/issues", "source": "https://github.com/guzzle/promises/tree/1.5.2"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/promises", "type": "tidelift"}], "time": "2022-08-28T14:55:35+00:00"}, {"name": "guzzlehttp/psr7", "version": "1.9.0", "source": {"type": "git", "url": "https://github.com/guzzle/psr7.git", "reference": "e98e3e6d4f86621a9b75f623996e6bbdeb4b9318"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/psr7/zipball/e98e3e6d4f86621a9b75f623996e6bbdeb4b9318", "reference": "e98e3e6d4f86621a9b75f623996e6bbdeb4b9318", "shasum": ""}, "require": {"php": ">=5.4.0", "psr/http-message": "~1.0", "ralouphie/getallheaders": "^2.0.5 || ^3.0.0"}, "provide": {"psr/http-message-implementation": "1.0"}, "require-dev": {"ext-zlib": "*", "phpunit/phpunit": "~4.8.36 || ^5.7.27 || ^6.5.14 || ^7.5.20 || ^8.5.8 || ^9.3.10"}, "suggest": {"laminas/laminas-httphandlerrunner": "Emit PSR-7 responses"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.9-dev"}}, "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\Psr7\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "psr-7", "request", "response", "stream", "uri", "url"], "support": {"issues": "https://github.com/guzzle/psr7/issues", "source": "https://github.com/guzzle/psr7/tree/1.9.0"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/psr7", "type": "tidelift"}], "time": "2022-06-20T21:43:03+00:00"}, {"name": "hashids/hashids", "version": "4.1.0", "source": {"type": "git", "url": "https://github.com/vinkla/hashids.git", "reference": "8cab111f78e0bd9c76953b082919fc9e251761be"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/vinkla/hashids/zipball/8cab111f78e0bd9c76953b082919fc9e251761be", "reference": "8cab111f78e0bd9c76953b082919fc9e251761be", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-mbstring": "*", "php": "^7.2 || ^8.0"}, "require-dev": {"phpunit/phpunit": "^8.0 || ^9.4", "squizlabs/php_codesniffer": "^3.5"}, "suggest": {"ext-bcmath": "Required to use BC Math arbitrary precision mathematics (*).", "ext-gmp": "Required to use GNU multiple precision mathematics (*)."}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.1-dev"}}, "autoload": {"psr-4": {"Hashids\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Generate short, unique, non-sequential ids (like YouTube and Bitly) from numbers", "homepage": "https://hashids.org/php", "keywords": ["bitly", "decode", "encode", "hash", "hashid", "hashids", "ids", "obfuscate", "youtube"], "support": {"issues": "https://github.com/vinkla/hashids/issues", "source": "https://github.com/vinkla/hashids/tree/4.1.0"}, "time": "2020-11-26T19:24:33+00:00"}, {"name": "hyperf/command", "version": "v2.1.0", "source": {"type": "git", "url": "https://github.com/hyperf/command.git", "reference": "5338cf65f4881857997f29d52fbcbff7811291e6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/command/zipball/5338cf65f4881857997f29d52fbcbff7811291e6", "reference": "5338cf65f4881857997f29d52fbcbff7811291e6", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"hyperf/utils": "~2.1.0", "php": ">=7.2", "psr/event-dispatcher": "^1.0", "symfony/console": "^5.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.9", "malukenho/docheader": "^0.1.6", "mockery/mockery": "^1.0", "phpunit/phpunit": "^9.4"}, "suggest": {"hyperf/di": "Required to use annotations."}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.1-dev"}}, "autoload": {"psr-4": {"Hyperf\\Command\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Command for hyperf", "keywords": ["command", "php", "swoole"], "support": {"issues": "https://github.com/hyperf/command/issues", "source": "https://github.com/hyperf/command/tree/v2.1.0"}, "time": "2020-12-27T06:12:45+00:00"}, {"name": "hyperf/contract", "version": "v2.1.16", "source": {"type": "git", "url": "https://github.com/hyperf/contract.git", "reference": "f45b49ef5b485f1f3fe482f76a1305cefc60f45a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/contract/zipball/f45b49ef5b485f1f3fe482f76a1305cefc60f45a", "reference": "f45b49ef5b485f1f3fe482f76a1305cefc60f45a", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.2"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.9", "malukenho/docheader": "^0.1.6", "mockery/mockery": "^1.0", "phpunit/phpunit": "^9.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.1-dev"}, "hyperf": []}, "autoload": {"psr-4": {"Hyperf\\Contract\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "The contracts of Hyperf.", "homepage": "https://hyperf.io", "keywords": ["hyperf", "php", "swoole"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "time": "2021-04-21T09:16:31+00:00"}, {"name": "hyperf/engine", "version": "v1.1.3", "source": {"type": "git", "url": "https://github.com/hyperf/engine.git", "reference": "71baee68eb591cfcd4adee960eeb09662ac186a4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/engine/zipball/71baee68eb591cfcd4adee960eeb09662ac186a4", "reference": "71baee68eb591cfcd4adee960eeb09662ac186a4", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.3"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.0", "phpstan/phpstan": "^0.12", "phpunit/phpunit": "^9.4", "swoole/ide-helper": "dev-master"}, "suggest": {"ext-swoole": ">=4.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"psr-4": {"Hyperf\\Engine\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "keywords": ["hyperf", "php"], "support": {"issues": "https://github.com/hyperf/engine/issues", "source": "https://github.com/hyperf/engine/tree/v1.1.3"}, "time": "2021-05-17T11:37:41+00:00"}, {"name": "hyperf/guzzle", "version": "v2.1.1", "source": {"type": "git", "url": "https://github.com/hyperf/guzzle.git", "reference": "dccd7e5262e470425f40f7d5c772bfd8a2baf694"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/guzzle/zipball/dccd7e5262e470425f40f7d5c772bfd8a2baf694", "reference": "dccd7e5262e470425f40f7d5c772bfd8a2baf694", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"guzzlehttp/guzzle": "^6.3|^7.0", "hyperf/utils": "~2.1.0", "php": ">=7.0", "psr/container": "^1.0", "psr/http-message": "^1.0"}, "require-dev": {"ezimuel/ringphp": "~1.0"}, "suggest": {"hyperf/pool": "Required to use pool handler."}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.1-dev"}, "hyperf": {"config": "Hyperf\\Guzzle\\ConfigProvider"}}, "autoload": {"psr-4": {"Hyperf\\Guzzle\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Swoole coroutine handler for guzzle", "keywords": ["Guzzle", "handler", "php", "swoole"], "support": {"issues": "https://github.com/hyperf/guzzle/issues", "source": "https://github.com/hyperf/guzzle/tree/v2.1.1"}, "time": "2020-12-28T03:05:18+00:00"}, {"name": "hyperf/utils", "version": "v2.1.14", "source": {"type": "git", "url": "https://github.com/hyperf/utils.git", "reference": "466d339af938ab334b2a228923236afe9f49e63e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/utils/zipball/466d339af938ab334b2a228923236afe9f49e63e", "reference": "466d339af938ab334b2a228923236afe9f49e63e", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"doctrine/inflector": "^2.0", "hyperf/contract": "~2.1.0", "hyperf/engine": "^1.1", "php": ">=7.2"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.9", "malukenho/docheader": "^0.1.6", "mockery/mockery": "^1.0", "phpunit/phpunit": "^9.4", "symfony/property-access": "^5.0", "symfony/serializer": "^5.0", "symfony/var-dumper": "^5.0"}, "suggest": {"ext-swoole": "Required to use methods related to swoole (>=4.5).", "hyperf/di": "Required to use ExceptionNormalizer", "symfony/property-access": "Required to use SymfonyNormalizer (^5.0)", "symfony/serializer": "Required to use SymfonyNormalizer (^5.0)", "symfony/var-dumper": "Required to use the dd function (^5.0)."}, "type": "library", "extra": {"hyperf": {"config": "Hyperf\\Utils\\ConfigProvider"}, "branch-alias": {"dev-master": "2.1-dev"}}, "autoload": {"files": ["src/Functions.php"], "psr-4": {"Hyperf\\Utils\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A tools package that could help developer solved the problem quickly.", "homepage": "https://hyperf.io", "keywords": ["hyperf", "php", "swoole", "utils"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "time": "2021-04-09T03:45:20+00:00"}, {"name": "littlesqx/spamassassin", "version": "v0.4.0", "source": {"type": "git", "url": "https://github.com/Littlesqx/php-spamassassin.git", "reference": "b6416322e0925ae5dbe2498a96f5d15b23b60e95"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Littlesqx/php-spamassassin/zipball/b6416322e0925ae5dbe2498a96f5d15b23b60e95", "reference": "b6416322e0925ae5dbe2498a96f5d15b23b60e95", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.1"}, "require-dev": {"phpunit/phpunit": "~6"}, "type": "library", "autoload": {"classmap": ["src/Spamassassin/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Spamd protocol client for PHP. PHP package that implements the spamd protocol specification", "keywords": ["spamassassin", "spamd"], "time": "2019-11-22T01:59:45+00:00"}, {"name": "lizhichao/one-sm", "version": "1.10", "source": {"type": "git", "url": "https://github.com/lizhichao/sm.git", "reference": "687a012a44a5bfd4d9143a0234e1060543be455a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/lizhichao/sm/zipball/687a012a44a5bfd4d9143a0234e1060543be455a", "reference": "687a012a44a5bfd4d9143a0234e1060543be455a", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.6"}, "type": "library", "autoload": {"psr-4": {"OneSm\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "国密sm3", "keywords": ["php", "sm3"], "support": {"issues": "https://github.com/lizhichao/sm/issues", "source": "https://github.com/lizhichao/sm/tree/1.10"}, "funding": [{"url": "https://www.vicsdf.com/img/w.jpg", "type": "custom"}, {"url": "https://www.vicsdf.com/img/z.jpg", "type": "custom"}], "time": "2021-05-26T06:19:22+00:00"}, {"name": "maennchen/zipstream-php", "version": "2.1.0", "source": {"type": "git", "url": "https://github.com/maennchen/ZipStream-PHP.git", "reference": "c4c5803cc1f93df3d2448478ef79394a5981cc58"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/maennchen/ZipStream-PHP/zipball/c4c5803cc1f93df3d2448478ef79394a5981cc58", "reference": "c4c5803cc1f93df3d2448478ef79394a5981cc58", "shasum": ""}, "require": {"myclabs/php-enum": "^1.5", "php": ">= 7.1", "psr/http-message": "^1.0", "symfony/polyfill-mbstring": "^1.0"}, "require-dev": {"ext-zip": "*", "guzzlehttp/guzzle": ">= 6.3", "mikey179/vfsstream": "^1.6", "phpunit/phpunit": ">= 7.5"}, "type": "library", "autoload": {"psr-4": {"ZipStream\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Jonatan Männchen", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "ZipStream is a library for dynamically streaming dynamic zip files from PHP without writing to the disk at all on the server.", "keywords": ["stream", "zip"], "support": {"issues": "https://github.com/maennchen/ZipStream-PHP/issues", "source": "https://github.com/maennchen/ZipStream-PHP/tree/master"}, "funding": [{"url": "https://opencollective.com/zipstream", "type": "open_collective"}], "time": "2020-05-30T13:11:16+00:00"}, {"name": "mailgun/mailgun-php", "version": "3.5.0", "source": {"type": "git", "url": "https://github.com/mailgun/mailgun-php.git", "reference": "f109b4a2f7914548932a422b9cfc3c3607be1b03"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mailgun/mailgun-php/zipball/f109b4a2f7914548932a422b9cfc3c3607be1b03", "reference": "f109b4a2f7914548932a422b9cfc3c3607be1b03", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.3 || ^8.0", "php-http/client-common": "^2.2.1", "php-http/discovery": "^1.9.1", "php-http/multipart-stream-builder": "^1.1.2", "psr/http-client": "^1.0.1", "webmozart/assert": "^1.9.1"}, "require-dev": {"nyholm/nsa": "^1.2.1", "nyholm/psr7": "^1.3.1", "php-http/guzzle7-adapter": "^0.1.1", "phpunit/phpunit": "^9.3"}, "suggest": {"guzzlehttp/psr7": "PSR-7 message implementation that also provides common utility methods", "php-http/curl-client": "cURL client for PHP-HTTP"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"psr-4": {"Mailgun\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Mailgun SDK provides methods for all API functions.", "support": {"issues": "https://github.com/mailgun/mailgun-php/issues", "source": "https://github.com/mailgun/mailgun-php/tree/3.5.0"}, "time": "2021-03-08T07:27:11+00:00"}, {"name": "markbaker/complex", "version": "3.0.1", "source": {"type": "git", "url": "https://github.com/MarkBaker/PHPComplex.git", "reference": "ab8bc271e404909db09ff2d5ffa1e538085c0f22"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/MarkBaker/PHPComplex/zipball/ab8bc271e404909db09ff2d5ffa1e538085c0f22", "reference": "ab8bc271e404909db09ff2d5ffa1e538085c0f22", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^0.7.0", "phpcompatibility/php-compatibility": "^9.0", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.3", "squizlabs/php_codesniffer": "^3.4"}, "type": "library", "autoload": {"psr-4": {"Complex\\": "classes/src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Class for working with complex numbers", "homepage": "https://github.com/MarkBaker/PHPComplex", "keywords": ["complex", "mathematics"], "support": {"issues": "https://github.com/MarkBaker/PHPComplex/issues", "source": "https://github.com/MarkBaker/PHPComplex/tree/3.0.1"}, "time": "2021-06-29T15:32:53+00:00"}, {"name": "markbaker/matrix", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/MarkBaker/PHPMatrix.git", "reference": "c66aefcafb4f6c269510e9ac46b82619a904c576"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/MarkBaker/PHPMatrix/zipball/c66aefcafb4f6c269510e9ac46b82619a904c576", "reference": "c66aefcafb4f6c269510e9ac46b82619a904c576", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^0.7.0", "phpcompatibility/php-compatibility": "^9.0", "phpdocumentor/phpdocumentor": "2.*", "phploc/phploc": "^4.0", "phpmd/phpmd": "2.*", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.3", "sebastian/phpcpd": "^4.0", "squizlabs/php_codesniffer": "^3.4"}, "type": "library", "autoload": {"psr-4": {"Matrix\\": "classes/src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Class for working with matrices", "homepage": "https://github.com/MarkBaker/PHPMatrix", "keywords": ["mathematics", "matrix", "vector"], "support": {"issues": "https://github.com/MarkBaker/PHPMatrix/issues", "source": "https://github.com/MarkBaker/PHPMatrix/tree/3.0.0"}, "time": "2021-07-01T19:01:15+00:00"}, {"name": "mongodb/mongodb", "version": "1.0.5", "source": {"type": "git", "url": "https://github.com/mongodb/mongo-php-library.git", "reference": "2f99156b29bc85582415d6a32bc31010d61a0a71"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mongodb/mongo-php-library/zipball/2f99156b29bc85582415d6a32bc31010d61a0a71", "reference": "2f99156b29bc85582415d6a32bc31010d61a0a71", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-mongodb": "^1.1.0", "php": ">=5.4"}, "type": "library", "autoload": {"psr-4": {"MongoDB\\": "src/"}, "files": ["src/functions.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "MongoDB driver library", "homepage": "https://jira.mongodb.org/browse/PHPLIB", "keywords": ["database", "driver", "mongodb", "persistence"], "time": "2017-02-16T18:35:09+00:00"}, {"name": "monolog/monolog", "version": "2.9.2", "source": {"type": "git", "url": "https://github.com/Seldaek/monolog.git", "reference": "437cb3628f4cf6042cc10ae97fc2b8472e48ca1f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/monolog/zipball/437cb3628f4cf6042cc10ae97fc2b8472e48ca1f", "reference": "437cb3628f4cf6042cc10ae97fc2b8472e48ca1f", "shasum": ""}, "require": {"php": ">=7.2", "psr/log": "^1.0.1 || ^2.0 || ^3.0"}, "provide": {"psr/log-implementation": "1.0.0 || 2.0.0 || 3.0.0"}, "require-dev": {"aws/aws-sdk-php": "^2.4.9 || ^3.0", "doctrine/couchdb": "~1.0@dev", "elasticsearch/elasticsearch": "^7 || ^8", "ext-json": "*", "graylog2/gelf-php": "^1.4.2 || ^2@dev", "guzzlehttp/guzzle": "^7.4", "guzzlehttp/psr7": "^2.2", "mongodb/mongodb": "^1.8", "php-amqplib/php-amqplib": "~2.4 || ^3", "phpspec/prophecy": "^1.15", "phpstan/phpstan": "^0.12.91", "phpunit/phpunit": "^8.5.14", "predis/predis": "^1.1 || ^2.0", "rollbar/rollbar": "^1.3 || ^2 || ^3", "ruflin/elastica": "^7", "swiftmailer/swiftmailer": "^5.3|^6.0", "symfony/mailer": "^5.4 || ^6", "symfony/mime": "^5.4 || ^6"}, "suggest": {"aws/aws-sdk-php": "Allow sending log messages to AWS services like DynamoDB", "doctrine/couchdb": "Allow sending log messages to a CouchDB server", "elasticsearch/elasticsearch": "Allow sending log messages to an Elasticsearch server via official client", "ext-amqp": "Allow sending log messages to an AMQP server (1.0+ required)", "ext-curl": "Required to send log messages using the IFTTTHandler, the LogglyHandler, the SendGridHandler, the SlackWebhookHandler or the TelegramBotHandler", "ext-mbstring": "Allow to work properly with unicode symbols", "ext-mongodb": "Allow sending log messages to a MongoDB server (via driver)", "ext-openssl": "Required to send log messages using SSL", "ext-sockets": "Allow sending log messages to a Syslog server (via UDP driver)", "graylog2/gelf-php": "Allow sending log messages to a GrayLog2 server", "mongodb/mongodb": "Allow sending log messages to a MongoDB server (via library)", "php-amqplib/php-amqplib": "Allow sending log messages to an AMQP server using php-amqplib", "rollbar/rollbar": "Allow sending log messages to Rollbar", "ruflin/elastica": "Allow sending log messages to an Elastic Search server"}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.x-dev"}}, "autoload": {"psr-4": {"Monolog\\": "src/Monolog"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "https://seld.be"}], "description": "Sends your logs to files, sockets, inboxes, databases and various web services", "homepage": "https://github.com/Seldaek/monolog", "keywords": ["log", "logging", "psr-3"], "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/2.9.2"}, "funding": [{"url": "https://github.com/Seldaek", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/monolog/monolog", "type": "tidelift"}], "time": "2023-10-27T15:25:26+00:00"}, {"name": "mtdowling/jmespath.php", "version": "2.6.0", "source": {"type": "git", "url": "https://github.com/jmespath/jmespath.php.git", "reference": "42dae2cbd13154083ca6d70099692fef8ca84bfb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jmespath/jmespath.php/zipball/42dae2cbd13154083ca6d70099692fef8ca84bfb", "reference": "42dae2cbd13154083ca6d70099692fef8ca84bfb", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^5.4 || ^7.0 || ^8.0", "symfony/polyfill-mbstring": "^1.17"}, "require-dev": {"composer/xdebug-handler": "^1.4", "phpunit/phpunit": "^4.8.36 || ^7.5.15"}, "bin": ["bin/jp.php"], "type": "library", "extra": {"branch-alias": {"dev-master": "2.6-dev"}}, "autoload": {"psr-4": {"JmesPath\\": "src/"}, "files": ["src/JmesPath.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Declaratively specify how to extract elements from a JSON document", "keywords": ["json", "jsonpath"], "support": {"issues": "https://github.com/jmespath/jmespath.php/issues", "source": "https://github.com/jmespath/jmespath.php/tree/2.6.0"}, "time": "2020-07-31T21:01:56+00:00"}, {"name": "myclabs/deep-copy", "version": "1.11.0", "source": {"type": "git", "url": "https://github.com/myclabs/DeepCopy.git", "reference": "14daed4296fae74d9e3201d2c4925d1acb7aa614"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/myclabs/DeepCopy/zipball/14daed4296fae74d9e3201d2c4925d1acb7aa614", "reference": "14daed4296fae74d9e3201d2c4925d1acb7aa614", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "conflict": {"doctrine/collections": "<1.6.8", "doctrine/common": "<2.13.3 || >=3,<3.2.2"}, "require-dev": {"doctrine/collections": "^1.6.8", "doctrine/common": "^2.13.3 || ^3.2.2", "phpunit/phpunit": "^7.5.20 || ^8.5.23 || ^9.5.13"}, "type": "library", "autoload": {"files": ["src/DeepCopy/deep_copy.php"], "psr-4": {"DeepCopy\\": "src/DeepCopy/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Create deep copies (clones) of your objects", "keywords": ["clone", "copy", "duplicate", "object", "object graph"], "support": {"issues": "https://github.com/myclabs/DeepCopy/issues", "source": "https://github.com/myclabs/DeepCopy/tree/1.11.0"}, "funding": [{"url": "https://tidelift.com/funding/github/packagist/myclabs/deep-copy", "type": "tidelift"}], "time": "2022-03-03T13:19:32+00:00"}, {"name": "myclabs/php-enum", "version": "1.8.3", "source": {"type": "git", "url": "https://github.com/myclabs/php-enum.git", "reference": "b942d263c641ddb5190929ff840c68f78713e937"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/myclabs/php-enum/zipball/b942d263c641ddb5190929ff840c68f78713e937", "reference": "b942d263c641ddb5190929ff840c68f78713e937", "shasum": ""}, "require": {"ext-json": "*", "php": "^7.3 || ^8.0"}, "require-dev": {"phpunit/phpunit": "^9.5", "squizlabs/php_codesniffer": "1.*", "vimeo/psalm": "^4.6.2"}, "type": "library", "autoload": {"psr-4": {"MyCLabs\\Enum\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP Enum contributors", "homepage": "https://github.com/myclabs/php-enum/graphs/contributors"}], "description": "PHP Enum implementation", "homepage": "http://github.com/myclabs/php-enum", "keywords": ["enum"], "support": {"issues": "https://github.com/myclabs/php-enum/issues", "source": "https://github.com/myclabs/php-enum/tree/1.8.3"}, "funding": [{"url": "https://github.com/mnapoli", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/myclabs/php-enum", "type": "tidelift"}], "time": "2021-07-05T08:18:36+00:00"}, {"name": "nesbot/carbon", "version": "2.48.1", "source": {"type": "git", "url": "https://github.com/CarbonPHP/carbon.git", "reference": "8d1f50f1436fb4b05e7127360483dd9c6e73da16"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/8d1f50f1436fb4b05e7127360483dd9c6e73da16", "reference": "8d1f50f1436fb4b05e7127360483dd9c6e73da16", "shasum": ""}, "require": {"ext-json": "*", "php": "^7.1.8 || ^8.0", "symfony/polyfill-mbstring": "^1.0", "symfony/translation": "^3.4 || ^4.0 || ^5.0"}, "require-dev": {"doctrine/orm": "^2.7", "friendsofphp/php-cs-fixer": "^2.14 || ^3.0", "kylekatarnls/multi-tester": "^2.0", "phpmd/phpmd": "^2.9", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^0.12.54", "phpunit/phpunit": "^7.5.20 || ^8.5.14", "squizlabs/php_codesniffer": "^3.4"}, "bin": ["bin/carbon"], "type": "library", "extra": {"laravel": {"providers": ["Carbon\\Laravel\\ServiceProvider"]}, "phpstan": {"includes": ["extension.neon"]}, "branch-alias": {"dev-3.x": "3.x-dev", "dev-master": "2.x-dev"}}, "autoload": {"psr-4": {"Carbon\\": "src/Carbon/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://nesbot.com"}, {"name": "kylekatarnls", "homepage": "http://github.com/kylekatarnls"}], "description": "An API extension for DateTime that supports 281 different languages.", "homepage": "http://carbon.nesbot.com", "keywords": ["date", "datetime", "time"], "support": {"issues": "https://github.com/briannesbitt/Carbon/issues", "source": "https://github.com/briannesbitt/Carbon"}, "funding": [{"url": "https://github.com/kylekatarnls", "type": "github"}, {"url": "https://opencollective.com/Carbon", "type": "open_collective"}, {"url": "https://tidelift.com/funding/github/packagist/nesbot/carbon", "type": "tidelift"}], "time": "2021-05-26T22:08:38+00:00"}, {"name": "opis/closure", "version": "3.6.2", "source": {"type": "git", "url": "https://github.com/opis/closure.git", "reference": "06e2ebd25f2869e54a306dda991f7db58066f7f6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/opis/closure/zipball/06e2ebd25f2869e54a306dda991f7db58066f7f6", "reference": "06e2ebd25f2869e54a306dda991f7db58066f7f6", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^5.4 || ^7.0 || ^8.0"}, "require-dev": {"jeremeamia/superclosure": "^2.0", "phpunit/phpunit": "^4.0 || ^5.0 || ^6.0 || ^7.0 || ^8.0 || ^9.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.6.x-dev"}}, "autoload": {"psr-4": {"Opis\\Closure\\": "src/"}, "files": ["functions.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Sorin Sarca", "email": "<EMAIL>"}], "description": "A library that can be used to serialize closures (anonymous functions) and arbitrary objects.", "homepage": "https://opis.io/closure", "keywords": ["anonymous functions", "closure", "function", "serializable", "serialization", "serialize"], "support": {"issues": "https://github.com/opis/closure/issues", "source": "https://github.com/opis/closure/tree/3.6.2"}, "time": "2021-04-09T13:42:10+00:00"}, {"name": "overtrue/socialite", "version": "2.0.23", "source": {"type": "git", "url": "https://github.com/overtrue/socialite.git", "reference": "0bc60597b589592243f074a4d9016aabd2e9cfb2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/overtrue/socialite/zipball/0bc60597b589592243f074a4d9016aabd2e9cfb2", "reference": "0bc60597b589592243f074a4d9016aabd2e9cfb2", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-json": "*", "guzzlehttp/guzzle": "^5.0|^6.0|^7.0", "php": ">=5.6", "symfony/http-foundation": "^2.7|^3.0|^4.0|^5.0"}, "require-dev": {"mockery/mockery": "~1.2", "phpunit/phpunit": "~6"}, "type": "library", "autoload": {"psr-4": {"Overtrue\\Socialite\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "overtrue", "email": "<EMAIL>"}], "description": "A collection of OAuth 2 packages that extracts from laravel/socialite.", "keywords": ["login", "o<PERSON>h", "qq", "social", "wechat", "weibo"], "support": {"issues": "https://github.com/overtrue/socialite/issues", "source": "https://github.com/overtrue/socialite/tree/2.0.23"}, "funding": [{"url": "https://www.patreon.com/overtrue", "type": "patreon"}], "time": "2020-12-14T03:30:08+00:00"}, {"name": "php-http/client-common", "version": "2.3.0", "source": {"type": "git", "url": "https://github.com/php-http/client-common.git", "reference": "e37e46c610c87519753135fb893111798c69076a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/client-common/zipball/e37e46c610c87519753135fb893111798c69076a", "reference": "e37e46c610c87519753135fb893111798c69076a", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.1 || ^8.0", "php-http/httplug": "^2.0", "php-http/message": "^1.6", "php-http/message-factory": "^1.0", "psr/http-client": "^1.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.0", "symfony/options-resolver": "^2.6 || ^3.4.20 || ~4.0.15 || ~4.1.9 || ^4.2.1 || ^5.0", "symfony/polyfill-php80": "^1.17"}, "require-dev": {"doctrine/instantiator": "^1.1", "guzzlehttp/psr7": "^1.4", "nyholm/psr7": "^1.2", "phpspec/phpspec": "^5.1 || ^6.0", "phpspec/prophecy": "^1.10.2", "phpunit/phpunit": "^7.5.15 || ^8.5 || ^9.3"}, "suggest": {"ext-json": "To detect JSON responses with the ContentTypePlugin", "ext-libxml": "To detect XML responses with the ContentTypePlugin", "php-http/cache-plugin": "PSR-6 Cache plugin", "php-http/logger-plugin": "PSR-3 Logger plugin", "php-http/stopwatch-plugin": "Symfony Stopwatch plugin"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.3.x-dev"}}, "autoload": {"psr-4": {"Http\\Client\\Common\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Common HTTP Client implementations and tools for HTTPlug", "homepage": "http://httplug.io", "keywords": ["client", "common", "http", "httplug"], "support": {"issues": "https://github.com/php-http/client-common/issues", "source": "https://github.com/php-http/client-common/tree/2.3.0"}, "time": "2020-07-21T10:04:13+00:00"}, {"name": "php-http/discovery", "version": "1.13.0", "source": {"type": "git", "url": "https://github.com/php-http/discovery.git", "reference": "788f72d64c43dc361e7fcc7464c3d947c64984a7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/discovery/zipball/788f72d64c43dc361e7fcc7464c3d947c64984a7", "reference": "788f72d64c43dc361e7fcc7464c3d947c64984a7", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.1 || ^8.0"}, "conflict": {"nyholm/psr7": "<1.0"}, "require-dev": {"graham-campbell/phpspec-skip-example-extension": "^5.0", "php-http/httplug": "^1.0 || ^2.0", "php-http/message-factory": "^1.0", "phpspec/phpspec": "^5.1 || ^6.1", "puli/composer-plugin": "1.0.0-beta10"}, "suggest": {"php-http/message": "Allow to use Guzzle, Diactoros or Slim Framework factories", "puli/composer-plugin": "Sets up Puli which is recommended for Discovery to work. Check http://docs.php-http.org/en/latest/discovery.html for more details."}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.9-dev"}}, "autoload": {"psr-4": {"Http\\Discovery\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Finds installed HTTPlug implementations and PSR-7 message factories", "homepage": "http://php-http.org", "keywords": ["adapter", "client", "discovery", "factory", "http", "message", "psr7"], "support": {"issues": "https://github.com/php-http/discovery/issues", "source": "https://github.com/php-http/discovery/tree/1.13.0"}, "time": "2020-11-27T14:49:42+00:00"}, {"name": "php-http/httplug", "version": "2.2.0", "source": {"type": "git", "url": "https://github.com/php-http/httplug.git", "reference": "191a0a1b41ed026b717421931f8d3bd2514ffbf9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/httplug/zipball/191a0a1b41ed026b717421931f8d3bd2514ffbf9", "reference": "191a0a1b41ed026b717421931f8d3bd2514ffbf9", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.1 || ^8.0", "php-http/promise": "^1.1", "psr/http-client": "^1.0", "psr/http-message": "^1.0"}, "require-dev": {"friends-of-phpspec/phpspec-code-coverage": "^4.1", "phpspec/phpspec": "^5.1 || ^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.x-dev"}}, "autoload": {"psr-4": {"Http\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Eric <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://sagikazarmark.hu"}], "description": "HTTPlug, the HTTP client abstraction for PHP", "homepage": "http://httplug.io", "keywords": ["client", "http"], "support": {"issues": "https://github.com/php-http/httplug/issues", "source": "https://github.com/php-http/httplug/tree/master"}, "time": "2020-07-13T15:43:23+00:00"}, {"name": "php-http/message", "version": "1.11.0", "source": {"type": "git", "url": "https://github.com/php-http/message.git", "reference": "fb0dbce7355cad4f4f6a225f537c34d013571f29"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/message/zipball/fb0dbce7355cad4f4f6a225f537c34d013571f29", "reference": "fb0dbce7355cad4f4f6a225f537c34d013571f29", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"clue/stream-filter": "^1.5", "php": "^7.1 || ^8.0", "php-http/message-factory": "^1.0.2", "psr/http-message": "^1.0"}, "provide": {"php-http/message-factory-implementation": "1.0"}, "require-dev": {"ergebnis/composer-normalize": "^2.6", "ext-zlib": "*", "guzzlehttp/psr7": "^1.0", "laminas/laminas-diactoros": "^2.0", "phpspec/phpspec": "^5.1 || ^6.3", "slim/slim": "^3.0"}, "suggest": {"ext-zlib": "Used with compressor/decompressor streams", "guzzlehttp/psr7": "Used with Guzzle PSR-7 Factories", "laminas/laminas-diactoros": "Used with Diactoros Factories", "slim/slim": "Used with Slim Framework PSR-7 implementation"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.10-dev"}}, "autoload": {"psr-4": {"Http\\Message\\": "src/"}, "files": ["src/filters.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "HTTP Message related tools", "homepage": "http://php-http.org", "keywords": ["http", "message", "psr-7"], "support": {"issues": "https://github.com/php-http/message/issues", "source": "https://github.com/php-http/message/tree/1.11.0"}, "time": "2021-02-01T08:54:58+00:00"}, {"name": "php-http/message-factory", "version": "v1.0.2", "source": {"type": "git", "url": "https://github.com/php-http/message-factory.git", "reference": "a478cb11f66a6ac48d8954216cfed9aa06a501a1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/message-factory/zipball/a478cb11f66a6ac48d8954216cfed9aa06a501a1", "reference": "a478cb11f66a6ac48d8954216cfed9aa06a501a1", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.4", "psr/http-message": "^1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"psr-4": {"Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Factory interfaces for PSR-7 HTTP Message", "homepage": "http://php-http.org", "keywords": ["factory", "http", "message", "stream", "uri"], "support": {"issues": "https://github.com/php-http/message-factory/issues", "source": "https://github.com/php-http/message-factory/tree/master"}, "time": "2015-12-19T14:08:53+00:00"}, {"name": "php-http/multipart-stream-builder", "version": "1.1.2", "source": {"type": "git", "url": "https://github.com/php-http/multipart-stream-builder.git", "reference": "121299c2aad475a19087bc6298a1c9aa4d5c1ecc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/multipart-stream-builder/zipball/121299c2aad475a19087bc6298a1c9aa4d5c1ecc", "reference": "121299c2aad475a19087bc6298a1c9aa4d5c1ecc", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.1 || ^8.0", "php-http/discovery": "^1.7", "php-http/message-factory": "^1.0.2", "psr/http-factory": "^1.0", "psr/http-message": "^1.0"}, "require-dev": {"nyholm/psr7": "^1.0", "php-http/message": "^1.5", "phpunit/phpunit": "^7.5.15 || ^8.5 || ^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "autoload": {"psr-4": {"Http\\Message\\MultipartStream\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A builder class that help you create a multipart stream", "homepage": "http://php-http.org", "keywords": ["factory", "http", "message", "multipart stream", "stream"], "support": {"issues": "https://github.com/php-http/multipart-stream-builder/issues", "source": "https://github.com/php-http/multipart-stream-builder/tree/1.1.2"}, "time": "2020-07-13T15:48:43+00:00"}, {"name": "php-http/promise", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/php-http/promise.git", "reference": "4c4c1f9b7289a2ec57cde7f1e9762a5789506f88"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/promise/zipball/4c4c1f9b7289a2ec57cde7f1e9762a5789506f88", "reference": "4c4c1f9b7289a2ec57cde7f1e9762a5789506f88", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"friends-of-phpspec/phpspec-code-coverage": "^4.3.2", "phpspec/phpspec": "^5.1.2 || ^6.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "autoload": {"psr-4": {"Http\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Promise used for asynchronous HTTP requests", "homepage": "http://httplug.io", "keywords": ["promise"], "support": {"issues": "https://github.com/php-http/promise/issues", "source": "https://github.com/php-http/promise/tree/1.1.0"}, "time": "2020-07-07T09:29:14+00:00"}, {"name": "phpoffice/phpspreadsheet", "version": "1.24.1", "source": {"type": "git", "url": "https://github.com/PHPOffice/PhpSpreadsheet.git", "reference": "69991111e05fca3ff7398e1e7fca9ebed33efec6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPOffice/PhpSpreadsheet/zipball/69991111e05fca3ff7398e1e7fca9ebed33efec6", "reference": "69991111e05fca3ff7398e1e7fca9ebed33efec6", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-ctype": "*", "ext-dom": "*", "ext-fileinfo": "*", "ext-gd": "*", "ext-iconv": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-simplexml": "*", "ext-xml": "*", "ext-xmlreader": "*", "ext-xmlwriter": "*", "ext-zip": "*", "ext-zlib": "*", "ezyang/htmlpurifier": "^4.13", "maennchen/zipstream-php": "^2.1", "markbaker/complex": "^3.0", "markbaker/matrix": "^3.0", "php": "^7.3 || ^8.0", "psr/http-client": "^1.0", "psr/http-factory": "^1.0", "psr/simple-cache": "^1.0 || ^2.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "dev-master", "dompdf/dompdf": "^1.0 || ^2.0", "friendsofphp/php-cs-fixer": "^3.2", "jpgraph/jpgraph": "^4.0", "mpdf/mpdf": "8.1.1", "phpcompatibility/php-compatibility": "^9.3", "phpstan/phpstan": "^1.1", "phpstan/phpstan-phpunit": "^1.0", "phpunit/phpunit": "^8.5 || ^9.0", "squizlabs/php_codesniffer": "^3.7", "tecnickcom/tcpdf": "^6.4"}, "suggest": {"dompdf/dompdf": "Option for rendering PDF with PDF Writer (doesn't yet support PHP8)", "jpgraph/jpgraph": "Option for rendering charts, or including charts with PDF or HTML Writers", "mpdf/mpdf": "Option for rendering PDF with PDF Writer", "tecnickcom/tcpdf": "Option for rendering PDF with PDF Writer (doesn't yet support PHP8)"}, "type": "library", "autoload": {"psr-4": {"PhpOffice\\PhpSpreadsheet\\": "src/PhpSpreadsheet"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://blog.maartenballiauw.be"}, {"name": "<PERSON>", "homepage": "https://markbakeruk.net"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://rootslabs.net"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}], "description": "PHPSpreadsheet - Read, Create and Write Spreadsheet documents in PHP - Spreadsheet engine", "homepage": "https://github.com/PHPOffice/PhpSpreadsheet", "keywords": ["OpenXML", "excel", "gnumeric", "ods", "php", "spreadsheet", "xls", "xlsx"], "support": {"issues": "https://github.com/PHPOffice/PhpSpreadsheet/issues", "source": "https://github.com/PHPOffice/PhpSpreadsheet/tree/1.24.1"}, "time": "2022-07-18T19:50:48+00:00"}, {"name": "phpseclib/phpseclib", "version": "2.0.30", "source": {"type": "git", "url": "https://github.com/phpseclib/phpseclib.git", "reference": "136b9ca7eebef78be14abf90d65c5e57b6bc5d36"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/136b9ca7eebef78be14abf90d65c5e57b6bc5d36", "reference": "136b9ca7eebef78be14abf90d65c5e57b6bc5d36", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3.3"}, "require-dev": {"phing/phing": "~2.7", "phpunit/phpunit": "^4.8.35|^5.7|^6.0|^9.4", "squizlabs/php_codesniffer": "~2.0"}, "suggest": {"ext-gmp": "Install the GMP (GNU Multiple Precision) extension in order to speed up arbitrary precision integer arithmetic operations.", "ext-libsodium": "SSH2/SFTP can make use of some algorithms provided by the libsodium-php extension.", "ext-mcrypt": "Install the Mcrypt extension in order to speed up a few other cryptographic operations.", "ext-openssl": "Install the OpenSSL extension in order to speed up a wide variety of cryptographic operations."}, "type": "library", "autoload": {"files": ["phpseclib/bootstrap.php"], "psr-4": {"phpseclib\\": "phpseclib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Lead Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "PHP Secure Communications Library - Pure-PHP implementations of RSA, AES, SSH2, SFTP, X.509 etc.", "homepage": "http://phpseclib.sourceforge.net", "keywords": ["BigInteger", "aes", "asn.1", "asn1", "blowfish", "crypto", "cryptography", "encryption", "rsa", "security", "sftp", "signature", "signing", "ssh", "twofish", "x.509", "x509"], "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/2.0.30"}, "funding": [{"url": "https://github.com/terrafrost", "type": "github"}, {"url": "https://www.patreon.com/phpseclib", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/phpseclib/phpseclib", "type": "tidelift"}], "time": "2020-12-17T05:42:04+00:00"}, {"name": "predis/predis", "version": "v1.1.10", "source": {"type": "git", "url": "https://github.com/predis/predis.git", "reference": "a2fb02d738bedadcffdbb07efa3a5e7bd57f8d6e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/predis/predis/zipball/a2fb02d738bedadcffdbb07efa3a5e7bd57f8d6e", "reference": "a2fb02d738bedadcffdbb07efa3a5e7bd57f8d6e", "shasum": ""}, "require": {"php": ">=5.3.9"}, "require-dev": {"phpunit/phpunit": "~4.8"}, "suggest": {"ext-curl": "Allows access to Webdis when paired with phpiredis", "ext-phpiredis": "Allows faster serialization and deserialization of the Redis protocol"}, "type": "library", "autoload": {"psr-4": {"Predis\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://clorophilla.net", "role": "Creator & Maintainer"}, {"name": "<PERSON>", "homepage": "https://till.im", "role": "Maintainer"}], "description": "Flexible and feature-complete Redis client for PHP and HHVM", "homepage": "http://github.com/predis/predis", "keywords": ["nosql", "predis", "redis"], "support": {"issues": "https://github.com/predis/predis/issues", "source": "https://github.com/predis/predis/tree/v1.1.10"}, "funding": [{"url": "https://github.com/sponsors/tillkruss", "type": "github"}], "time": "2022-01-05T17:46:08+00:00"}, {"name": "psr/cache", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/cache.git", "reference": "aa5030cfa5405eccfdcb1083ce040c2cb8d253bf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/cache/zipball/aa5030cfa5405eccfdcb1083ce040c2cb8d253bf", "reference": "aa5030cfa5405eccfdcb1083ce040c2cb8d253bf", "shasum": ""}, "require": {"php": ">=8.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Cache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for caching libraries", "keywords": ["cache", "psr", "psr-6"], "support": {"source": "https://github.com/php-fig/cache/tree/3.0.0"}, "time": "2021-02-03T23:26:27+00:00"}, {"name": "psr/container", "version": "1.1.1", "source": {"type": "git", "url": "https://github.com/php-fig/container.git", "reference": "8622567409010282b7aeebe4bb841fe98b58dcaf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/container/zipball/8622567409010282b7aeebe4bb841fe98b58dcaf", "reference": "8622567409010282b7aeebe4bb841fe98b58dcaf", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.2.0"}, "type": "library", "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "support": {"issues": "https://github.com/php-fig/container/issues", "source": "https://github.com/php-fig/container/tree/1.1.1"}, "time": "2021-03-05T17:36:06+00:00"}, {"name": "psr/event-dispatcher", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/event-dispatcher.git", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/event-dispatcher/zipball/dbefd12671e8a14ec7f180cab83036ed26714bb0", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\EventDispatcher\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Standard interfaces for event handling.", "keywords": ["events", "psr", "psr-14"], "support": {"issues": "https://github.com/php-fig/event-dispatcher/issues", "source": "https://github.com/php-fig/event-dispatcher/tree/1.0.0"}, "time": "2019-01-08T18:20:26+00:00"}, {"name": "psr/http-client", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/http-client.git", "reference": "2dfb5f6c5eff0e91e20e913f8c5452ed95b86621"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-client/zipball/2dfb5f6c5eff0e91e20e913f8c5452ed95b86621", "reference": "2dfb5f6c5eff0e91e20e913f8c5452ed95b86621", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.0 || ^8.0", "psr/http-message": "^1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for HTTP clients", "homepage": "https://github.com/php-fig/http-client", "keywords": ["http", "http-client", "psr", "psr-18"], "support": {"source": "https://github.com/php-fig/http-client/tree/master"}, "time": "2020-06-29T06:28:15+00:00"}, {"name": "psr/http-factory", "version": "1.0.2", "source": {"type": "git", "url": "https://github.com/php-fig/http-factory.git", "reference": "e616d01114759c4c489f93b099585439f795fe35"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-factory/zipball/e616d01114759c4c489f93b099585439f795fe35", "reference": "e616d01114759c4c489f93b099585439f795fe35", "shasum": ""}, "require": {"php": ">=7.0.0", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interfaces for PSR-7 HTTP message factories", "keywords": ["factory", "http", "message", "psr", "psr-17", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-factory/tree/1.0.2"}, "time": "2023-04-10T20:10:41+00:00"}, {"name": "psr/http-message", "version": "1.1", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "cb6ce4845ce34a8ad9e68117c10ee90a29919eba"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/cb6ce4845ce34a8ad9e68117c10ee90a29919eba", "reference": "cb6ce4845ce34a8ad9e68117c10ee90a29919eba", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-message/tree/1.1"}, "time": "2023-04-04T09:50:52+00:00"}, {"name": "psr/log", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "fe5ea303b0887d5caefd3d431c3e61ad47037001"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/fe5ea303b0887d5caefd3d431c3e61ad47037001", "reference": "fe5ea303b0887d5caefd3d431c3e61ad47037001", "shasum": ""}, "require": {"php": ">=8.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "support": {"source": "https://github.com/php-fig/log/tree/3.0.0"}, "time": "2021-07-14T16:46:02+00:00"}, {"name": "psr/simple-cache", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/simple-cache.git", "reference": "408d5eafb83c57f6365a3ca330ff23aa4a5fa39b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/simple-cache/zipball/408d5eafb83c57f6365a3ca330ff23aa4a5fa39b", "reference": "408d5eafb83c57f6365a3ca330ff23aa4a5fa39b", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\SimpleCache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interfaces for simple caching", "keywords": ["cache", "caching", "psr", "psr-16", "simple-cache"], "support": {"source": "https://github.com/php-fig/simple-cache/tree/master"}, "time": "2017-10-23T01:57:42+00:00"}, {"name": "ralouphie/getallheaders", "version": "3.0.3", "source": {"type": "git", "url": "https://github.com/ralouphie/getallheaders.git", "reference": "120b605dfeb996808c31b6477290a714d356e822"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/120b605dfeb996808c31b6477290a714d356e822", "reference": "120b605dfeb996808c31b6477290a714d356e822", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^5 || ^6.5"}, "type": "library", "autoload": {"files": ["src/getallheaders.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A polyfill for getallheaders.", "support": {"issues": "https://github.com/ralouphie/getallheaders/issues", "source": "https://github.com/ralouphie/getallheaders/tree/develop"}, "time": "2019-03-08T08:55:37+00:00"}, {"name": "react/promise", "version": "v2.7.1", "source": {"type": "git", "url": "https://github.com/reactphp/promise.git", "reference": "31ffa96f8d2ed0341a57848cbb84d88b89dd664d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/reactphp/promise/zipball/31ffa96f8d2ed0341a57848cbb84d88b89dd664d", "reference": "31ffa96f8d2ed0341a57848cbb84d88b89dd664d", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "~4.8"}, "type": "library", "autoload": {"psr-4": {"React\\Promise\\": "src/"}, "files": ["src/functions_include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A lightweight implementation of CommonJS Promises/A for PHP", "keywords": ["promise", "promises"], "time": "2019-01-07T21:25:54+00:00"}, {"name": "sabre/uri", "version": "2.2.2", "source": {"type": "git", "url": "https://github.com/sabre-io/uri.git", "reference": "7cb0f489578afad5006e85cd60f18ff33f2d440d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sabre-io/uri/zipball/7cb0f489578afad5006e85cd60f18ff33f2d440d", "reference": "7cb0f489578afad5006e85cd60f18ff33f2d440d", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "~2.17.1", "phpstan/phpstan": "^0.12", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.0"}, "type": "library", "autoload": {"files": ["lib/functions.php"], "psr-4": {"Sabre\\Uri\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://evertpot.com/", "role": "Developer"}], "description": "Functions for making sense out of URIs.", "homepage": "http://sabre.io/uri/", "keywords": ["rfc3986", "uri", "url"], "support": {"forum": "https://groups.google.com/group/sabredav-discuss", "issues": "https://github.com/sabre-io/uri/issues", "source": "https://github.com/fruux/sabre-uri"}, "time": "2021-11-04T09:29:58+00:00"}, {"name": "sabre/xml", "version": "2.2.5", "source": {"type": "git", "url": "https://github.com/sabre-io/xml.git", "reference": "a6af111850e7536d200d9637c34885cd3c77a86c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sabre-io/xml/zipball/a6af111850e7536d200d9637c34885cd3c77a86c", "reference": "a6af111850e7536d200d9637c34885cd3c77a86c", "shasum": ""}, "require": {"ext-dom": "*", "ext-xmlreader": "*", "ext-xmlwriter": "*", "lib-libxml": ">=2.6.20", "php": "^7.1 || ^8.0", "sabre/uri": ">=1.0,<3.0.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "~2.17.1", "phpstan/phpstan": "^0.12", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.0"}, "type": "library", "autoload": {"psr-4": {"Sabre\\Xml\\": "lib/"}, "files": ["lib/Deserializer/functions.php", "lib/Serializer/functions.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://evertpot.com/", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "sabre/xml is an XML library that you may not hate.", "homepage": "https://sabre.io/xml/", "keywords": ["XMLReader", "XMLWriter", "dom", "xml"], "support": {"forum": "https://groups.google.com/group/sabredav-discuss", "issues": "https://github.com/sabre-io/xml/issues", "source": "https://github.com/fruux/sabre-xml"}, "time": "2021-11-04T06:37:27+00:00"}, {"name": "sensorsdata/sa-sdk-php", "version": "v1.10.9", "source": {"type": "git", "url": "https://github.com/sensorsdata/sa-sdk-php.git", "reference": "9f86a0743adb90038aefebf50cc7c1b793e2d7b9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sensorsdata/sa-sdk-php/zipball/9f86a0743adb90038aefebf50cc7c1b793e2d7b9", "reference": "9f86a0743adb90038aefebf50cc7c1b793e2d7b9", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.5.0"}, "type": "library", "autoload": {"files": ["SensorsAnalytics.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP SDK for Sensors Analytics", "homepage": "http://sensorsdata.cn", "keywords": ["sdk", "sensorsdata"], "support": {"issues": "https://github.com/sensorsdata/sa-sdk-php/issues", "source": "https://github.com/sensorsdata/sa-sdk-php/tree/v1.10.9"}, "time": "2020-08-12T06:38:24+00:00"}, {"name": "smalot/pdfparser", "version": "v2.8.0", "source": {"type": "git", "url": "https://github.com/smalot/pdfparser.git", "reference": "268a620b96523eb4244c42931885024c8db8dae1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/smalot/pdfparser/zipball/268a620b96523eb4244c42931885024c8db8dae1", "reference": "268a620b96523eb4244c42931885024c8db8dae1", "shasum": ""}, "require": {"ext-iconv": "*", "ext-zlib": "*", "php": ">=7.1", "symfony/polyfill-mbstring": "^1.18"}, "type": "library", "autoload": {"psr-0": {"Smalot\\PdfParser\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Pdf parser library. Can read and extract information from pdf file.", "homepage": "https://www.pdfparser.org", "keywords": ["extract", "parse", "parser", "pdf", "text"], "support": {"issues": "https://github.com/smalot/pdfparser/issues", "source": "https://github.com/smalot/pdfparser/tree/v2.8.0"}, "time": "2023-12-01T11:13:56+00:00"}, {"name": "symfony/console", "version": "v5.2.8", "source": {"type": "git", "url": "https://github.com/symfony/console.git", "reference": "864568fdc0208b3eba3638b6000b69d2386e6768"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/console/zipball/864568fdc0208b3eba3638b6000b69d2386e6768", "reference": "864568fdc0208b3eba3638b6000b69d2386e6768", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.2.5", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php73": "^1.8", "symfony/polyfill-php80": "^1.15", "symfony/service-contracts": "^1.1|^2", "symfony/string": "^5.1"}, "conflict": {"symfony/dependency-injection": "<4.4", "symfony/dotenv": "<5.1", "symfony/event-dispatcher": "<4.4", "symfony/lock": "<4.4", "symfony/process": "<4.4"}, "provide": {"psr/log-implementation": "1.0"}, "require-dev": {"psr/log": "~1.0", "symfony/config": "^4.4|^5.0", "symfony/dependency-injection": "^4.4|^5.0", "symfony/event-dispatcher": "^4.4|^5.0", "symfony/lock": "^4.4|^5.0", "symfony/process": "^4.4|^5.0", "symfony/var-dumper": "^4.4|^5.0"}, "suggest": {"psr/log": "For using the console logger", "symfony/event-dispatcher": "", "symfony/lock": "", "symfony/process": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Console\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Eases the creation of beautiful and testable command line interfaces", "homepage": "https://symfony.com", "keywords": ["cli", "command line", "console", "terminal"], "support": {"source": "https://github.com/symfony/console/tree/v5.2.8"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-05-11T15:45:21+00:00"}, {"name": "symfony/deprecation-contracts", "version": "v2.5.4", "source": {"type": "git", "url": "https://github.com/symfony/deprecation-contracts.git", "reference": "605389f2a7e5625f273b53960dc46aeaf9c62918"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/605389f2a7e5625f273b53960dc46aeaf9c62918", "reference": "605389f2a7e5625f273b53960dc46aeaf9c62918", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "2.5-dev"}}, "autoload": {"files": ["function.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A generic function and convention to trigger deprecation notices", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/v2.5.4"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:11:13+00:00"}, {"name": "symfony/event-dispatcher-contracts", "version": "v1.1.13", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher-contracts.git", "reference": "1d5cd762abaa6b2a4169d3e77610193a7157129e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher-contracts/zipball/1d5cd762abaa6b2a4169d3e77610193a7157129e", "reference": "1d5cd762abaa6b2a4169d3e77610193a7157129e", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.1.3"}, "suggest": {"psr/event-dispatcher": "", "symfony/event-dispatcher-implementation": ""}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "1.1-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\EventDispatcher\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to dispatching event", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/event-dispatcher-contracts/tree/v1.1.13"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-01-02T09:41:36+00:00"}, {"name": "symfony/http-foundation", "version": "v5.2.4", "source": {"type": "git", "url": "https://github.com/symfony/http-foundation.git", "reference": "54499baea7f7418bce7b5ec92770fd0799e8e9bf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-foundation/zipball/54499baea7f7418bce7b5ec92770fd0799e8e9bf", "reference": "54499baea7f7418bce7b5ec92770fd0799e8e9bf", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1", "symfony/polyfill-mbstring": "~1.1", "symfony/polyfill-php80": "^1.15"}, "require-dev": {"predis/predis": "~1.0", "symfony/cache": "^4.4|^5.0", "symfony/expression-language": "^4.4|^5.0", "symfony/mime": "^4.4|^5.0"}, "suggest": {"symfony/mime": "To use the file extension guesser"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\HttpFoundation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Defines an object-oriented layer for the HTTP specification", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/http-foundation/tree/v5.2.4"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-02-25T17:16:57+00:00"}, {"name": "symfony/inflector", "version": "v5.4.45", "source": {"type": "git", "url": "https://github.com/symfony/inflector.git", "reference": "9acefb9d8017fd3382e1723bdb06132246b9cead"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/inflector/zipball/9acefb9d8017fd3382e1723bdb06132246b9cead", "reference": "9acefb9d8017fd3382e1723bdb06132246b9cead", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-php80": "^1.16", "symfony/string": "^5.4.41|^6.4.9"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Inflector\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Converts words between their singular and plural forms (English only)", "homepage": "https://symfony.com", "keywords": ["inflection", "pluralize", "singularize", "string", "symfony", "words"], "support": {"source": "https://github.com/symfony/inflector/tree/v5.4.45"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "abandoned": "EnglishInflector from the String component", "time": "2024-09-25T14:11:13+00:00"}, {"name": "symfony/options-resolver", "version": "v5.2.4", "source": {"type": "git", "url": "https://github.com/symfony/options-resolver.git", "reference": "5d0f633f9bbfcf7ec642a2b5037268e61b0a62ce"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/options-resolver/zipball/5d0f633f9bbfcf7ec642a2b5037268e61b0a62ce", "reference": "5d0f633f9bbfcf7ec642a2b5037268e61b0a62ce", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1", "symfony/polyfill-php73": "~1.0", "symfony/polyfill-php80": "^1.15"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\OptionsResolver\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an improved replacement for the array_replace PHP function", "homepage": "https://symfony.com", "keywords": ["config", "configuration", "options"], "support": {"source": "https://github.com/symfony/options-resolver/tree/v5.2.4"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-01-27T12:56:27+00:00"}, {"name": "symfony/polyfill-ctype", "version": "v1.31.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-ctype.git", "reference": "a3cc8b044a6ea513310cbd48ef7333b384945638"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/a3cc8b044a6ea513310cbd48ef7333b384945638", "reference": "a3cc8b044a6ea513310cbd48ef7333b384945638", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.2"}, "provide": {"ext-ctype": "*"}, "suggest": {"ext-ctype": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Ctype\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for ctype functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "ctype", "polyfill", "portable"], "support": {"source": "https://github.com/symfony/polyfill-ctype/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-intl-grapheme", "version": "v1.31.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-grapheme.git", "reference": "b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-grapheme/zipball/b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe", "reference": "b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.2"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Grapheme\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's grapheme_* functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "grapheme", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-grapheme/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-intl-idn", "version": "v1.27.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-idn.git", "reference": "639084e360537a19f9ee352433b84ce831f3d2da"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-idn/zipball/639084e360537a19f9ee352433b84ce831f3d2da", "reference": "639084e360537a19f9ee352433b84ce831f3d2da", "shasum": ""}, "require": {"php": ">=7.1", "symfony/polyfill-intl-normalizer": "^1.10", "symfony/polyfill-php72": "^1.10"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.27-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Idn\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's idn_to_ascii and idn_to_utf8 functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "idn", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-idn/tree/v1.27.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-11-03T14:55:06+00:00"}, {"name": "symfony/polyfill-intl-normalizer", "version": "v1.31.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-normalizer.git", "reference": "3833d7255cc303546435cb650316bff708a1c75c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/3833d7255cc303546435cb650316bff708a1c75c", "reference": "3833d7255cc303546435cb650316bff708a1c75c", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.2"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Normalizer\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's Normalizer class and related functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "intl", "normalizer", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-normalizer/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-mbstring", "version": "v1.31.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "85181ba99b2345b0ef10ce42ecac37612d9fd341"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/85181ba99b2345b0ef10ce42ecac37612d9fd341", "reference": "85181ba99b2345b0ef10ce42ecac37612d9fd341", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.2"}, "provide": {"ext-mbstring": "*"}, "suggest": {"ext-mbstring": "For best performance"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-mbstring/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-php72", "version": "v1.27.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php72.git", "reference": "869329b1e9894268a8a61dabb69153029b7a8c97"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php72/zipball/869329b1e9894268a8a61dabb69153029b7a8c97", "reference": "869329b1e9894268a8a61dabb69153029b7a8c97", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.27-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php72\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.2+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php72/tree/v1.27.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-11-03T14:55:06+00:00"}, {"name": "symfony/polyfill-php73", "version": "v1.22.1", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php73.git", "reference": "a678b42e92f86eca04b7fa4c0f6f19d097fb69e2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php73/zipball/a678b42e92f86eca04b7fa4c0f6f19d097fb69e2", "reference": "a678b42e92f86eca04b7fa4c0f6f19d097fb69e2", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.22-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Php73\\": ""}, "files": ["bootstrap.php"], "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.3+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php73/tree/v1.22.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-01-07T16:49:33+00:00"}, {"name": "symfony/polyfill-php80", "version": "v1.31.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php80.git", "reference": "60328e362d4c2c802a54fcbf04f9d3fb892b4cf8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php80/zipball/60328e362d4c2c802a54fcbf04f9d3fb892b4cf8", "reference": "60328e362d4c2c802a54fcbf04f9d3fb892b4cf8", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.2"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php80\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.0+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php80/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-php81", "version": "v1.31.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php81.git", "reference": "4a4cfc2d253c21a5ad0e53071df248ed48c6ce5c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php81/zipball/4a4cfc2d253c21a5ad0e53071df248ed48c6ce5c", "reference": "4a4cfc2d253c21a5ad0e53071df248ed48c6ce5c", "shasum": ""}, "require": {"php": ">=7.2"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php81\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.1+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php81/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/property-access", "version": "v4.4.44", "source": {"type": "git", "url": "https://github.com/symfony/property-access.git", "reference": "d49682f6f0764df725c95128213a38f7e0a9f358"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/property-access/zipball/d49682f6f0764df725c95128213a38f7e0a9f358", "reference": "d49682f6f0764df725c95128213a38f7e0a9f358", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.1.3", "symfony/inflector": "^3.4|^4.0|^5.0", "symfony/polyfill-php80": "^1.16"}, "require-dev": {"symfony/cache": "^3.4|^4.0|^5.0"}, "suggest": {"psr/cache-implementation": "To cache access methods."}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\PropertyAccess\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides functions to read and write from/to an object or array using a simple string notation", "homepage": "https://symfony.com", "keywords": ["access", "array", "extraction", "index", "injection", "object", "property", "property path", "reflection"], "support": {"source": "https://github.com/symfony/property-access/tree/v4.4.44"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-06-27T13:16:42+00:00"}, {"name": "symfony/service-contracts", "version": "v2.4.0", "source": {"type": "git", "url": "https://github.com/symfony/service-contracts.git", "reference": "f040a30e04b57fbcc9c6cbcf4dbaa96bd318b9bb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/service-contracts/zipball/f040a30e04b57fbcc9c6cbcf4dbaa96bd318b9bb", "reference": "f040a30e04b57fbcc9c6cbcf4dbaa96bd318b9bb", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.2.5", "psr/container": "^1.1"}, "suggest": {"symfony/service-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.4-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Service\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to writing services", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/service-contracts/tree/v2.4.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-04-01T10:43:52+00:00"}, {"name": "symfony/string", "version": "v5.4.45", "source": {"type": "git", "url": "https://github.com/symfony/string.git", "reference": "7f6807add88b1e2635f3c6de5e1ace631ed7cad2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/string/zipball/7f6807add88b1e2635f3c6de5e1ace631ed7cad2", "reference": "7f6807add88b1e2635f3c6de5e1ace631ed7cad2", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.2.5", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-intl-grapheme": "~1.0", "symfony/polyfill-intl-normalizer": "~1.0", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php80": "~1.15"}, "conflict": {"symfony/translation-contracts": ">=3.0"}, "require-dev": {"symfony/error-handler": "^4.4|^5.0|^6.0", "symfony/http-client": "^4.4|^5.0|^6.0", "symfony/translation-contracts": "^1.1|^2", "symfony/var-exporter": "^4.4|^5.0|^6.0"}, "type": "library", "autoload": {"files": ["Resources/functions.php"], "psr-4": {"Symfony\\Component\\String\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an object-oriented API to strings and deals with bytes, UTF-8 code points and grapheme clusters in a unified way", "homepage": "https://symfony.com", "keywords": ["grapheme", "i18n", "string", "unicode", "utf-8", "utf8"], "support": {"source": "https://github.com/symfony/string/tree/v5.4.45"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:11:13+00:00"}, {"name": "symfony/translation", "version": "v4.4.24", "source": {"type": "git", "url": "https://github.com/symfony/translation.git", "reference": "424d29dfcc15575af05196de0100d7b52f650602"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation/zipball/424d29dfcc15575af05196de0100d7b52f650602", "reference": "424d29dfcc15575af05196de0100d7b52f650602", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.1.3", "symfony/polyfill-mbstring": "~1.0", "symfony/translation-contracts": "^1.1.6|^2"}, "conflict": {"symfony/config": "<3.4", "symfony/dependency-injection": "<3.4", "symfony/http-kernel": "<4.4", "symfony/yaml": "<3.4"}, "provide": {"symfony/translation-implementation": "1.0|2.0"}, "require-dev": {"psr/log": "~1.0", "symfony/config": "^3.4|^4.0|^5.0", "symfony/console": "^3.4|^4.0|^5.0", "symfony/dependency-injection": "^3.4|^4.0|^5.0", "symfony/finder": "~2.8|~3.0|~4.0|^5.0", "symfony/http-kernel": "^4.4", "symfony/intl": "^3.4|^4.0|^5.0", "symfony/service-contracts": "^1.1.2|^2", "symfony/yaml": "^3.4|^4.0|^5.0"}, "suggest": {"psr/log-implementation": "To use logging capability in translator", "symfony/config": "", "symfony/yaml": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Translation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools to internationalize your application", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/translation/tree/v4.4.24"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-05-16T09:52:47+00:00"}, {"name": "symfony/translation-contracts", "version": "v2.4.0", "source": {"type": "git", "url": "https://github.com/symfony/translation-contracts.git", "reference": "95c812666f3e91db75385749fe219c5e494c7f95"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation-contracts/zipball/95c812666f3e91db75385749fe219c5e494c7f95", "reference": "95c812666f3e91db75385749fe219c5e494c7f95", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.2.5"}, "suggest": {"symfony/translation-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.4-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Translation\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to translation", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/translation-contracts/tree/v2.4.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-03-23T23:28:01+00:00"}, {"name": "symfony/workflow", "version": "v3.4.47", "source": {"type": "git", "url": "https://github.com/symfony/workflow.git", "reference": "fdd7bd981dd41a9ae0d9fdf6597e07cee0cf3e4a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/workflow/zipball/fdd7bd981dd41a9ae0d9fdf6597e07cee0cf3e4a", "reference": "fdd7bd981dd41a9ae0d9fdf6597e07cee0cf3e4a", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^5.5.9|>=7.0.8", "symfony/property-access": "^3.4|^4.3"}, "require-dev": {"psr/log": "~1.0", "symfony/dependency-injection": "~2.8|~3.0|~4.0", "symfony/event-dispatcher": "~2.1|~3.0|~4.0", "symfony/expression-language": "~2.8|~3.0|~4.0", "symfony/security-core": "~2.8|~3.0|~4.0", "symfony/validator": "~2.8|~3.4|~4.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Workflow\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Workflow Component", "homepage": "https://symfony.com", "keywords": ["petrinet", "place", "state", "statemachine", "transition", "workflow"], "support": {"source": "https://github.com/symfony/workflow/tree/v3.4.47"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-10-24T10:57:07+00:00"}, {"name": "symfony/yaml", "version": "v3.4.31", "source": {"type": "git", "url": "https://github.com/symfony/yaml.git", "reference": "3dc414b7db30695bae671a1d86013d03f4ae9834"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/yaml/zipball/3dc414b7db30695bae671a1d86013d03f4ae9834", "reference": "3dc414b7db30695bae671a1d86013d03f4ae9834", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^5.5.9|>=7.0.8", "symfony/polyfill-ctype": "~1.8"}, "conflict": {"symfony/console": "<3.4"}, "require-dev": {"symfony/console": "~3.4|~4.0"}, "suggest": {"symfony/console": "For validating YAML files using the lint command"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Yaml\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Yaml Component", "homepage": "https://symfony.com", "time": "2019-08-20T13:31:17+00:00"}, {"name": "twig/twig", "version": "v3.4.3", "source": {"type": "git", "url": "https://github.com/twigphp/Twig.git", "reference": "c38fd6b0b7f370c198db91ffd02e23b517426b58"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/twigphp/Twig/zipball/c38fd6b0b7f370c198db91ffd02e23b517426b58", "reference": "c38fd6b0b7f370c198db91ffd02e23b517426b58", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/polyfill-ctype": "^1.8", "symfony/polyfill-mbstring": "^1.3"}, "require-dev": {"psr/container": "^1.0", "symfony/phpunit-bridge": "^4.4.9|^5.0.9|^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Twig\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://fabien.potencier.org", "role": "Lead Developer"}, {"name": "Twig Team", "role": "Contributors"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Project Founder"}], "description": "Twig, the flexible, fast, and secure template language for PHP", "homepage": "https://twig.symfony.com", "keywords": ["templating"], "support": {"issues": "https://github.com/twigphp/Twig/issues", "source": "https://github.com/twigphp/Twig/tree/v3.4.3"}, "funding": [{"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/twig/twig", "type": "tidelift"}], "time": "2022-09-28T08:42:51+00:00"}, {"name": "webmozart/assert", "version": "1.10.0", "source": {"type": "git", "url": "https://github.com/webmozarts/assert.git", "reference": "6964c76c7804814a842473e0c8fd15bab0f18e25"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/webmozarts/assert/zipball/6964c76c7804814a842473e0c8fd15bab0f18e25", "reference": "6964c76c7804814a842473e0c8fd15bab0f18e25", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.2 || ^8.0", "symfony/polyfill-ctype": "^1.8"}, "conflict": {"phpstan/phpstan": "<0.12.20", "vimeo/psalm": "<4.6.1 || 4.6.2"}, "require-dev": {"phpunit/phpunit": "^8.5.13"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.10-dev"}}, "autoload": {"psr-4": {"Webmozart\\Assert\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Assertions to validate method input/output with nice error messages.", "keywords": ["assert", "check", "validate"], "support": {"issues": "https://github.com/webmozarts/assert/issues", "source": "https://github.com/webmozarts/assert/tree/1.10.0"}, "time": "2021-03-09T10:59:23+00:00"}, {"name": "whichbrowser/parser", "version": "v2.1.7", "source": {"type": "git", "url": "https://github.com/WhichBrowser/Parser-PHP.git", "reference": "1044880bc792dbce5948fbff22ae731c43c280d9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/WhichBrowser/Parser-PHP/zipball/1044880bc792dbce5948fbff22ae731c43c280d9", "reference": "1044880bc792dbce5948fbff22ae731c43c280d9", "shasum": ""}, "require": {"php": ">=5.4.0", "psr/cache": "^1.0 || ^2.0 || ^3.0"}, "require-dev": {"cache/array-adapter": "^1.1", "icomefromthenet/reverse-regex": "*******", "php-coveralls/php-coveralls": "^2.0", "phpunit/php-code-coverage": "^5.0 || ^7.0", "phpunit/phpunit": "^6.0 || ^8.0", "squizlabs/php_codesniffer": "^3.5", "symfony/yaml": "~3.4 || ~4.0"}, "suggest": {"cache/array-adapter": "Allows testing of the caching functionality"}, "type": "library", "autoload": {"psr-4": {"WhichBrowser\\": ["src/", "tests/src/"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Useragent sniffing library for PHP", "homepage": "http://whichbrowser.net", "keywords": ["browser", "sniffing", "ua", "useragent"], "support": {"issues": "https://github.com/WhichBrowser/Parser-PHP/issues", "source": "https://github.com/WhichBrowser/Parser-PHP/tree/v2.1.7"}, "time": "2022-04-19T20:14:54+00:00"}, {"name": "xiaoman-proto/infra-ai-domain", "version": "0.7.1", "dist": {"type": "zip", "url": "http://repo.xiaoman.cc/repository/composer/xiaoman-proto/infra-ai-domain/0.7.1/xiaoman-proto-infra-ai-domain-0.7.1.zip", "reference": "8a6ed38697dcafb9a602a48ee47a41230d5acda7", "shasum": "8a6ed38697dcafb9a602a48ee47a41230d5acda7"}, "require": {"ext-protobuf": "^v3.3.0", "google/common-protos": "^1.0", "grpc/grpc": "^v1.3.0", "xiaoman-proto/xiaoman-common": "^1.1.2"}, "type": "library", "autoload": {"psr-4": {"": ""}}, "description": "infra-ai-domain", "time": "2021-01-06T05:43:33+00:00"}, {"name": "xiaoman-proto/infra-ai-recommend", "version": "3.1.0", "dist": {"type": "zip", "url": "http://repo.xiaoman.cc/repository/composer/xiaoman-proto/infra-ai-recommend/3.1.0/xiaoman-proto-infra-ai-recommend-3.1.0.zip", "reference": "fa27e9672c3d234a9a30693f8db2a3ee6d2b8cb4", "shasum": "fa27e9672c3d234a9a30693f8db2a3ee6d2b8cb4"}, "require": {"ext-protobuf": "^v3.3.0", "google/common-protos": "^1.0", "grpc/grpc": "^v1.3.0", "xiaoman-proto/xiaoman-common": "^1.0.2"}, "type": "library", "autoload": {"psr-4": {"": ""}}, "description": "infra-ai-recommend", "time": "2019-09-10T03:45:29+00:00"}, {"name": "xiaoman-proto/infra-ai-tag", "version": "0.0.7", "dist": {"type": "zip", "url": "http://repo.xiaoman.cc/repository/composer/xiaoman-proto/infra-ai-tag/0.0.7/xiaoman-proto-infra-ai-tag-0.0.7.zip", "reference": "cced6d1860839fa9505add32e591eb572c1b9771", "shasum": "cced6d1860839fa9505add32e591eb572c1b9771"}, "require": {"ext-protobuf": "^v3.3.0", "google/common-protos": "^1.0", "grpc/grpc": "^v1.3.0", "xiaoman-proto/xiaoman-common": "^1.1.2"}, "type": "library", "autoload": {"psr-4": {"": ""}}, "description": "infra-ai-tag", "time": "2019-12-12T02:27:31+00:00"}, {"name": "xiaoman-proto/infra-common-whois", "version": "1.0.4", "dist": {"type": "zip", "url": "http://repo.xiaoman.cc/repository/composer/xiaoman-proto/infra-common-whois/1.0.4/xiaoman-proto-infra-common-whois-1.0.4.zip", "reference": "2919f708ad2cfc4ede40b0d53bd65a03dedf17db", "shasum": "2919f708ad2cfc4ede40b0d53bd65a03dedf17db"}, "require": {"ext-protobuf": "^v3.3.0", "google/common-protos": "^1.0", "grpc/grpc": "^v1.3.0", "xiaoman-proto/xiaoman-common": "^1.1.2"}, "type": "library", "autoload": {"psr-4": {"": ""}}, "description": "infra-common-whois", "time": "2020-07-15T02:16:09+00:00"}, {"name": "xiaoman-proto/infra-unifiedspider-email", "version": "1.0.2", "dist": {"type": "zip", "url": "http://repo.xiaoman.cc/repository/composer/xiaoman-proto/infra-unifiedspider-email/1.0.2/xiaoman-proto-infra-unifiedspider-email-1.0.2.zip", "reference": "2cb00073a1b24f4673cc95d28c276e46b42814cc", "shasum": "2cb00073a1b24f4673cc95d28c276e46b42814cc"}, "require": {"ext-protobuf": "^v3.3.0", "google/common-protos": "^1.0", "grpc/grpc": "^v1.3.0", "xiaoman-proto/xiaoman-common": "^1.1.2"}, "type": "library", "autoload": {"psr-4": {"": ""}}, "description": "infra-unifiedspider-email", "time": "2020-09-11T03:54:03+00:00"}, {"name": "xiaoman-proto/infra-unifiedspider-officialsite", "version": "1.0.0", "dist": {"type": "zip", "url": "http://repo.xiaoman.cc/repository/composer/xiaoman-proto/infra-unifiedspider-officialsite/1.0.0/xiaoman-proto-infra-unifiedspider-officialsite-1.0.0.zip", "reference": "6ebc40d5692347f9914e418c093c9100a52cc2cf", "shasum": "6ebc40d5692347f9914e418c093c9100a52cc2cf"}, "require": {"ext-protobuf": "^v3.3.0", "google/common-protos": "^1.0", "grpc/grpc": "^v1.3.0", "xiaoman-proto/xiaoman-common": "^1.0.2"}, "type": "library", "autoload": {"psr-4": {"": ""}}, "description": "infra-unifiedspider-officialsite", "time": "2019-09-25T06:22:37+00:00"}, {"name": "xiaoman-proto/xiaoman-common", "version": "1.1.2", "dist": {"type": "zip", "url": "http://repo.xiaoman.cc/repository/composer/xiaoman-proto/xiaoman-common/1.1.2/xiaoman-proto-xiaoman-common-1.1.2.zip", "reference": "367693205d24b0d060e64d342b89beea0d7a88e1", "shasum": "367693205d24b0d060e64d342b89beea0d7a88e1"}, "require": {"ext-protobuf": "^v3.3.0", "google/common-protos": "^1.0", "grpc/grpc": "^v1.3.0"}, "type": "library", "autoload": {"psr-4": {"": ""}}, "description": "xiaoman-common", "time": "2019-12-12T03:40:31+00:00"}, {"name": "xiaoman/alibaba-sdk", "version": "1.3.8", "dist": {"type": "zip", "url": "http://repo.xiaoman.cc/repository/composer/xiaoman/alibaba-sdk/1.3.8/xiaoman-alibaba-sdk-1.3.8.zip", "reference": "26cb797b917fad56c9b12e6b68a4ed897f2c0854", "shasum": "26cb797b917fad56c9b12e6b68a4ed897f2c0854"}, "require": {"php": ">=7.2|^8.0"}, "require-dev": {"phpunit/phpunit": "^8.5"}, "type": "library", "autoload": {"psr-4": {"xiaoman\\AlibabaSdk\\": "src/"}}, "autoload-dev": {"psr-4": {"tests\\": "tests"}}, "archive": {"exclude": [".gitlab-ci.yml", "vendor", ".idea", ".git", ".phpunit.result.cache", ".DS_Store"]}, "authors": [{"name": "cayley", "email": "<EMAIL>"}], "description": "xiaoman alibaba-sdk", "time": "2025-03-11T03:26:54+00:00"}, {"name": "xiaoman/gateway-auth", "version": "1.0.13", "dist": {"type": "zip", "url": "http://repo.xiaoman.cc/repository/composer/xiaoman/gateway-auth/1.0.13/xiaoman-gateway-auth-1.0.13.zip", "reference": "0485b3d76ce5f7753f2c0afcc04aa0b5bbaca67b", "shasum": "0485b3d76ce5f7753f2c0afcc04aa0b5bbaca67b"}, "require": {"composer-runtime-api": "*", "composer/semver": ">=2.0", "firebase/php-jwt": "^5.0 | ^6.0", "hyperf/contract": ">=2.0", "psr/http-message": ">=1.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.0", "hyperf/config": ">=2.0", "hyperf/di": ">=2.0", "hyperf/http-message": ">=2.0", "mockery/mockery": "^1.0", "pestphp/pest": "^2.30", "pestphp/pest-plugin-type-coverage": "^2.8", "php": ">=8.1", "phpstan/phpstan": "^1.0"}, "type": "library", "extra": {"hyperf": {"config": "Xiaoman\\GatewayAuth\\ConfigProvider"}}, "autoload": {"psr-4": {"Xiaoman\\GatewayAuth\\": "src/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"test": ["pest --coverage --min=80 --coverage-text --coverage-cobertura=coverage.cobertura.xml --include-path=./src"], "type-coverage": ["pest --type-coverage --min=80 --include-path=./src"], "analyse": ["phpstan analyse --memory-limit 1024M -l 0 ./src"], "cs-fix": ["php-cs-fixer fix $1"], "cs-check": ["php-cs-fixer check"]}, "authors": [{"name": "anhoder", "email": "<EMAIL>"}], "description": "Common Auth by Gateway Jwt", "time": "2024-12-20T11:55:31+00:00"}, {"name": "xiaoman/orm", "version": "dev-master", "dist": {"type": "zip", "url": "http://repo.xiaoman.cc/repository/composer/xiaoman/orm/dev-master/xiaoman-orm-dev-master.zip", "reference": "612e0547bbe7b486cfe7a8c026aad2a39dd06709", "shasum": "612e0547bbe7b486cfe7a8c026aad2a39dd06709"}, "require": {"ext-json": "*", "php": ">=7.3|^8.0"}, "type": "library", "autoload": {"psr-4": {"xiaoman\\orm\\": "src/"}, "files": ["src/functions/function.php"]}, "autoload-dev": {"psr-4": {"tests\\": "tests"}}, "authors": [{"name": "<PERSON>", "email": "and<PERSON><EMAIL>"}], "description": "xiaoman orm", "time": "2022-11-23T02:58:10+00:00"}, {"name": "xiaoman/queue", "version": "dev-release", "dist": {"type": "zip", "url": "http://repo.xiaoman.cc/repository/composer/xiaoman/queue/dev-release/xiaoman-queue-dev-release.zip", "reference": "d1628b9b4f946a34ee8add2ef1d6e132df21ce4b", "shasum": "d1628b9b4f946a34ee8add2ef1d6e132df21ce4b"}, "require": {"alibabacloud/ons": "^1.7", "aliyunmq/mq-http-sdk": ">=1.0.3", "ext-json": "*", "ext-swoole": ">=4.5", "hyperf/command": "^2.1", "hyperf/guzzle": "^2.1", "hyperf/utils": "^2.1", "monolog/monolog": "^2.2", "nesbot/carbon": "^2.46", "opis/closure": "^3.6", "php": ">=7.3", "predis/predis": "^1.1"}, "require-dev": {"mockery/mockery": "^1.4", "phpunit/phpunit": "^8.3", "swoole/ide-helper": "@dev"}, "suggest": {"php": ">=7.3"}, "bin": ["bin/queue"], "type": "library", "autoload": {"psr-4": {"Xiaoman\\Queue\\": "src"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests"}}, "scripts": {"test": ["./vendor/bin/phpunit --colors=always"]}, "license": ["MIT"], "authors": [{"name": "troy", "email": "<EMAIL>"}], "description": "A xiaoman queue library bas", "time": "2022-09-01T06:55:04+00:00"}, {"name": "xiaoman/sidekick", "version": "dev-master", "dist": {"type": "zip", "url": "http://repo.xiaoman.cc/repository/composer/xiaoman/sidekick/dev-master/xiaoman-sidekick-dev-master.zip", "reference": "6008d6e2e660d3c171c451aa43a5f1a9a0a58180", "shasum": "6008d6e2e660d3c171c451aa43a5f1a9a0a58180"}, "require": {"ext-sockets": "*", "monolog/monolog": "^2.3", "psr/log": "^1.0|^2.0|^3.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.18.0"}, "type": "library", "autoload": {"psr-4": {"Xiaoman\\Sidekick\\": "src/"}, "files": ["src/helpers.php"]}, "scripts": {"cs-fix": ["php-cs-fixer fix $1"]}, "description": "Xioaman Sidekick Library", "keywords": ["src", "swoole", "yii"], "time": "2023-07-25T08:34:51+00:00"}, {"name": "xiaoman/unique-file", "version": "dev-master", "source": {"type": "git", "url": "https://gitlab.xiaoman.cc/php/service/unique-file.git", "reference": "146f107755677cfb2cd95fd8a99006d03c9a158d"}, "require": {"ext-curl": "*", "ext-fileinfo": "*", "ext-json": "*", "guzzlehttp/guzzle": ">=6.5", "php": ">=7.3|^8.0"}, "default-branch": true, "type": "library", "autoload": {"psr-4": {"Xiaoman\\UniqueFile\\": "src/"}}, "autoload-dev": {"psr-4": {"tests\\": "tests"}}, "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "xiaoman unique-file", "time": "2023-09-06T08:40:55+00:00"}, {"name": "xiaoman/xm-proto-plus", "version": "v1.0.18", "source": {"type": "git", "url": "https://gitlab.xiaoman.cc/php/foundation/xm-proto-plus", "reference": "394ce3a00142f97f0e70191b90166a87980513e7"}, "require": {"ext-protobuf": "^v3.9.0", "ext-swoole": ">=4.5", "google/protobuf": ">=3.0", "php": ">=8.0", "symfony/polyfill-mbstring": "*", "symfony/polyfill-php80": "*", "symfony/polyfill-php81": "*"}, "require-dev": {"hyperf/hyperf": ">=3.0", "phpunit/phpunit": "^9.5", "swoole/ide-helper": "^4.5"}, "type": "library", "autoload": {"psr-4": {"Xiaoman\\ProtoPlus\\": "src/"}, "files": ["src/functions.php", "src/support.php"]}, "autoload-dev": {"psr-4": {"Xiaoman\\ProtoPlus\\Tests\\": "tests/"}}, "description": "make php-protobuf better", "time": "2024-12-16T13:21:47+00:00"}], "packages-dev": [{"name": "swoole/ide-helper", "version": "4.4.14", "source": {"type": "git", "url": "https://github.com/swoole/ide-helper.git", "reference": "d52d22addfdc877de807cc85e2d8ad8a3b17ad17"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/swoole/ide-helper/zipball/d52d22addfdc877de807cc85e2d8ad8a3b17ad17", "reference": "d52d22addfdc877de807cc85e2d8ad8a3b17ad17", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require-dev": {"guzzlehttp/guzzle": "~6.5.0", "squizlabs/php_codesniffer": "~3.5.0", "symfony/filesystem": "~4.3.0", "zendframework/zend-code": "~3.3.0"}, "type": "library", "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Team Swoole", "email": "<EMAIL>"}], "description": "IDE help files for Swoole.", "time": "2019-12-30T05:31:26+00:00"}], "aliases": [], "minimum-stability": "stable", "stability-flags": {"xiaoman/orm": 20, "xiaoman/queue": 20, "xiaoman/sidekick": 20, "xiaoman/unique-file": 20}, "prefer-stable": false, "prefer-lowest": false, "platform": {"ext-grpc": "^v1.3.0", "ext-protobuf": "^v3.9.0", "ext-json": "*", "ext-curl": "*", "ext-apcu": "*", "ext-sockets": "*"}, "platform-dev": {}, "plugin-api-version": "2.6.0"}