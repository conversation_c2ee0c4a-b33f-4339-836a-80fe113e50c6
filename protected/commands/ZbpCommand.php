<?php

use common\library\custom_field\CustomFieldService;
use common\library\invoice\export\InvoiceExportFactory;
use common\library\layout\LayoutConstants;
use common\library\layout\LayoutHelper;
use common\library\object\field\FieldConstant;
use common\library\object\field\FieldFilter;
use common\library\object\field\service\ScopeUserFieldService;
use common\library\object\field\updator\calculator\FieldRefreshTool;
use common\library\object\object_define\Constant;
use common\library\object\object_define\Constant as ObjConstant;
use common\library\object\object_relation\ObjectRelationConstant;
use common\library\oms\inventory\product_inventory\ProductInventoryFilter;
use common\library\oms\order\OrderFilter;
use common\library\oms\payment_invoice\PaymentInvoice;
use common\library\oms\warehouse\WarehouseFilter;
use common\library\privilege_v3\PrivilegeConstants;
use common\library\privilege_v3\PrivilegeCache;
use common\library\privilege_v3\PrivilegeService;
use common\library\product_v2\sku\ProductSkuFilter;
use common\library\server\binlog_to_biz\MessageJob;
use common\library\translate_v2\cache\TranslateCacheService;
use common\library\translate_v2\TranslateConstants;
use common\library\setting\library\origin\OriginApi;
use common\library\util\excel\ExcelReader;
use Symfony\Component\Console\Helper\Table;
use Symfony\Component\Console\Output\ConsoleOutput;
use xiaoman\orm\database\data\GT;
use xiaoman\orm\database\DBConstants;

class ZbpCommand extends CrontabCommand{

    /**
     * debugphp -d 'xdebug.client_port=9004' /data/codebase/bob/php-crm/protected/yiic-test zbp updateFunctionFields --clientId=333577
     */
    public function actionUpdateFunctionFields($clientId){
        $clientObject = \common\library\account\Client::getClient($clientId);
        $user = $clientObject->getMasterUser();
        \User::setLoginUser($user);
        $data = $this->fields2Update();
        $api = new \common\library\object\field\Api();

        foreach($data as $fieldId => $datum){
            echo $fieldId;
            echo PHP_EOL;
            $api->update($fieldId, $datum);
        }
    }

    public function fields2Update(){
        return [
            **********=>[
                'object_name' => \common\library\object\object_define\Constant::OBJ_ORDER_PRODUCT,
                'field' => 'cost_amount',
                'field_name' => '金额小计',
                'group_id' => \common\library\custom_field\CustomFieldService::ORDER_GROUP_PRODUCT,
                'field_type' => \common\library\object\field\FieldConstant::TYPE_PRICE,
                'array_flag' => 0,
                'function_type' => \common\library\object\field\FieldConstant::FUNCTION_TYPE_FORMULA,
                'ext_info' => [
                    "expression" => "{id.objOrderProduct.3495849221}*{id.objOrderProduct.count}*{id.objOrderProduct.unit_price}+{id.objOrderProduct.other_cost}",//计算公式
                    "empty" => 0,                                        //参与运算的字段值为空如何处理  0, null
                    "remark" => "",                                    //备注
                    "decimal" => 4,                                    //小数保存几位
                    "decimal_logic" => "floor"                //小数位取数逻辑
                ],
                'system_type' => 1,

                // 对于引用字段而言，这几个字段固定
                'allow_setting' => 1,
                'allow_setting_show' => 1,
                'allow_setting_required' => 0,
                'allow_setting_default' => 0,
                'allow_setting_tips' => 0,
                'is_writable' => 0,
                'required' => 0,
                'default_value' => 0,
                'tips' => '',
                'show_flag' => 1,
            ],
            3479360087=>[
                'object_name' => \common\library\object\object_define\Constant::OBJ_ORDER_PRODUCT,
                'field' => 'unit_price',
                'field_name' => '单价',
                'group_id' => \common\library\custom_field\CustomFieldService::ORDER_GROUP_PRODUCT,
                'field_type' => \common\library\object\field\FieldConstant::TYPE_PRICE,
                'array_flag' => 0,
                'function_type' => \common\library\object\field\FieldConstant::FUNCTION_TYPE_FORMULA,
                'ext_info' => [
                    "expression" => "{id.objOrderProduct.3495853038}/({id.objOrderProduct.3495852916}*{refer_id.objOrder.exchange_rate}/100)+({id.objOrderProduct.cost_with_tax}/(1+{id.objOrderProduct.vat_rate}/100))",//计算公式
                    "empty" => 0,                                        //参与运算的字段值为空如何处理  0, null
                    "remark" => "",                                    //备注
                    "decimal" => 4,                                    //小数保存几位
                    "decimal_logic" => "floor"                //小数位取数逻辑
                ],
                'system_type' => 1,

                // 对于引用字段而言，这几个字段固定
                'allow_setting' => 1,
                'allow_setting_show' => 1,
                'allow_setting_required' => 0,
                'allow_setting_default' => 0,
                'allow_setting_tips' => 0,
                'is_writable' => 0,
                'required' => 0,
                'default_value' => 0,
                'tips' => '',
                'show_flag' => 1,
            ],
        ];
    }

    // 对 333577 添加功能字段
    // debugphp -d 'xdebug.client_port=9004' /data/codebase/bob/php-crm/protected/yiic-test zbp addFunctionFields --clientId=333577
    public function actionAddFunctionFields($clientId){
        $clientObject = \common\library\account\Client::getClient($clientId);
        $user = $clientObject->getMasterUser();
        \User::setLoginUser($user);
        $data = $this->fields2Add2();
        $api = new \common\library\object\field\Api();
        $deleteFieldSql = "delete from tbl_field where client_id={$clientId} and (";
        $deleteRelationSql = "delete from tbl_field_relation where client_id={$clientId} and (";
        foreach($data as $datum){
            $deleteFieldSql .= " (field='{$datum['field']}' and object_name='{$datum['object_name']}') or ";
            $deleteRelationSql .= " (field='{$datum['field']}' and object_name='{$datum['object_name']}')  or ";
        }

        $deleteFieldSql = rtrim($deleteFieldSql,'or ').')';
        $deleteRelationSql = rtrim($deleteRelationSql,'or ').')';

        $db = \PgActiveRecord::getDbByClientId($clientId);
        $db->createCommand($deleteFieldSql)->execute();
        $db->createCommand($deleteRelationSql)->execute();

        foreach($data as $k => $datum){
            echo $k;
            echo PHP_EOL;
            $api->create($datum);
        }
    }

    public function fields2Add2(){
        return [
            [
                'object_name' => \common\library\object\object_define\Constant::OBJ_ORDER_PRODUCT,
                'field' => '5732340445',
                'field_name' => '产品尺寸（引）',
                'group_id' => \common\library\custom_field\CustomFieldService::ORDER_GROUP_PRODUCT,
                'field_type' => \common\library\object\field\FieldConstant::TYPE_TEXT,
                'array_flag' => 0,
                'function_type' => \common\library\object\field\FieldConstant::FUNCTION_TYPE_QUOTE,
                'ext_info' => [],
                'system_type' => 2,

                // 对于引用字段而言，这几个字段固定
                'allow_setting' => 1,
                'allow_setting_show' => 1,
                'allow_setting_required' => 0,
                'allow_setting_default' => 0,
                'allow_setting_tips' => 0,
                'is_writable' => 0,
                'required' => 0,
                'default_value' => 0,
                'tips' => '',
                'show_flag' => 1,
                'field_relations' => [
                    'relation_object_name' => 'objProductSku',
                    'relation_object_function_type' => \common\library\object\field\FieldConstant::FUNCTION_TYPE_NORMAL,
                    'relation_field' => 'sku_id',
                    'relation_object_field' => 'package_size',
                    'relation_object_relation_field' => 'sku_id'
                ]
            ],
        ];
    }

    protected function fields2Add(){
//        [**********,3670626515,1152738815,3580804704,1314181470,2493721613,1795328273,3115687443,1448503525,2663014790,2258577630,3860892549,2671211578,1089018323,1767948069,1742497472,1241869418,3478984586,3661340496,2390420035]
        // 对于汇总字段而言，外键是 relation_object_relation_field;
        // 对于引用、公式字段而言，外键是 relation_field;
        return [
            [
                'object_name' => \common\library\object\object_define\Constant::OBJ_PURCHASE_ORDER,
                'field' => '**********',
                'field_name' => '订单备注（引）',
                'group_id' => \common\library\custom_field\CustomFieldService::PURCHASE_ORDER_GROUP_BASIC,
                'field_type' => \common\library\object\field\FieldConstant::TYPE_TEXT,
                'array_flag' => 0,
                'function_type' => \common\library\object\field\FieldConstant::FUNCTION_TYPE_QUOTE,
                'ext_info' => [],
                'system_type' => 2,

                // 对于引用字段而言，这几个字段固定
                'allow_setting' => 1,
                'allow_setting_show' => 1,
                'allow_setting_required' => 0,
                'allow_setting_default' => 0,
                'allow_setting_tips' => 0,
                'is_writable' => 0,
                'required' => 0,
                'default_value' => '',
                'tips' => '',
                'show_flag' => 1,
//                'columns' => ["data_key" => "ext_varchar_1"],
                'field_relations' => [
                    'relation_object_name' => 'objOrder',
                    'relation_object_function_type' => \common\library\object\field\FieldConstant::FUNCTION_TYPE_NORMAL,
                    'relation_field' => 'refer_order_id',
                    'relation_object_field' => 'remark',
                    'relation_object_relation_field' => 'order_id'
                ]
            ],

            [
                'object_name' => \common\library\object\object_define\Constant::OBJ_PURCHASE_ORDER,
                'field' => 'addition_cost_amount',
                'field_name' => '附加费用总金额',
                'group_id' => \common\library\custom_field\CustomFieldService::PURCHASE_ORDER_GROUP_CHARGE,
                'field_type' => \common\library\object\field\FieldConstant::TYPE_NUMBER,
                'array_flag' => 0,
                'function_type' => \common\library\object\field\FieldConstant::FUNCTION_TYPE_QUOTE,
                'ext_info' => [],
                'system_type' => 1,

                // 对于引用字段而言，这几个字段固定
                'allow_setting' => 1,
                'allow_setting_show' => 1,
                'allow_setting_required' => 0,
                'allow_setting_default' => 0,
                'allow_setting_tips' => 0,
                'is_writable' => 0,
                'required' => 0,
                'default_value' => '',
                'tips' => '',
                'show_flag' => 1,
                'columns' => ["data_key" => "addition_cost_amount"],
                'field_relations' => [
                    'relation_object_name' => 'objOrder',
                    'relation_object_function_type' => \common\library\object\field\FieldConstant::FUNCTION_TYPE_NORMAL,
                    'relation_field' => 'refer_order_id',
                    'relation_object_field' => 'addition_cost_amount',
                    'relation_object_relation_field' => 'order_id'
                ]
            ],

            [
                'object_name' => \common\library\object\object_define\Constant::OBJ_PURCHASE_ORDER,
                'field' => '3670626515',
                'field_name' => '订单星级（引）',
                'group_id' => \common\library\custom_field\CustomFieldService::PURCHASE_ORDER_GROUP_BASIC,
                'field_type' => \common\library\object\field\FieldConstant::TYPE_SELECT,
                'array_flag' => 0,
                'function_type' => \common\library\object\field\FieldConstant::FUNCTION_TYPE_QUOTE,
                'ext_info' => [],
                'system_type' => 2,

                // 对于引用字段而言，这几个字段固定
                'allow_setting' => 1,
                'allow_setting_show' => 1,
                'allow_setting_required' => 0,
                'allow_setting_default' => 0,
                'allow_setting_tips' => 0,
                'is_writable' => 0,
                'required' => 0,
                'default_value' => '',
                'tips' => '',
                'show_flag' => 1,
//                'columns' => ["data_key" => "ext_varchar_2"],
                'field_relations' => [
                    'relation_object_name' => 'objOrder',
                    'relation_object_function_type' => \common\library\object\field\FieldConstant::FUNCTION_TYPE_NORMAL,
                    'relation_field' => 'refer_order_id',
                    'relation_object_field' => '3461890164',
                    'relation_object_relation_field' => 'order_id'
                ]
            ],

            [
                'object_name' => \common\library\object\object_define\Constant::OBJ_ORDER_PRODUCT,
                'field' => 'currency',
                'field_name' => '币种',
                'group_id' => 0,
                'field_type' => \common\library\object\field\FieldConstant::TYPE_SELECT,
                'array_flag' => 0,
                'function_type' => \common\library\object\field\FieldConstant::FUNCTION_TYPE_QUOTE,
                'ext_info' => [],
                'system_type' => 1,

                // 对于引用字段而言，这几个字段固定
                'allow_setting' => 1,
                'allow_setting_show' => 1,
                'allow_setting_required' => 0,
                'allow_setting_default' => 0,
                'allow_setting_tips' => 0,
                'is_writable' => 0,
                'required' => 0,
                'default_value' => '',
                'tips' => '',
                'show_flag' => 1,
                'columns' => ["data_key" => "currency"],
                'field_relations' => [
                    'relation_object_name' => 'objOrder',
                    'relation_object_function_type' => \common\library\object\field\FieldConstant::FUNCTION_TYPE_NORMAL,
                    'relation_field' => 'order_id',
                    'relation_object_field' => 'currency',
                    'relation_object_relation_field' => 'order_id'
                ]
            ],

            [
                'object_name' => \common\library\object\object_define\Constant::OBJ_PURCHASE_ORDER_PRODUCT,
                'field' => '1152738815',
                'field_name' => '采购明细优先级（引）',
                'group_id' => 0,
                'field_type' => \common\library\object\field\FieldConstant::TYPE_NUMBER,
                'array_flag' => 0,
                'function_type' => \common\library\object\field\FieldConstant::FUNCTION_TYPE_QUOTE,
                'ext_info' => [],
                'system_type' => 2,

                // 对于引用字段而言，这几个字段固定
                'allow_setting' => 1,
                'allow_setting_show' => 1,
                'allow_setting_required' => 0,
                'allow_setting_default' => 0,
                'allow_setting_tips' => 0,
                'is_writable' => 0,
                'required' => 0,
                'default_value' => '',
                'tips' => '',
                'show_flag' => 1,
                // 'columns' => ["data_key" => "ext_numeric_1"],
                'field_relations' => [
                    'relation_object_name' => 'objOrder',
                    'relation_object_function_type' => \common\library\object\field\FieldConstant::FUNCTION_TYPE_NORMAL,
                    'relation_field' => 'order_id',
                    'relation_object_field' => '3461890158',
                    'relation_object_relation_field' => 'order_id'
                ]
            ],

            [
                'object_name' => \common\library\object\object_define\Constant::OBJ_PURCHASE_ORDER_PRODUCT,
                'field' => '3580804704',
                'field_name' => '订单交货地址（引）',
                'group_id' => 0,
                'field_type' => \common\library\object\field\FieldConstant::TYPE_TEXTAREA,
                'array_flag' => 0,
                'function_type' => \common\library\object\field\FieldConstant::FUNCTION_TYPE_QUOTE,
                'ext_info' => [],
                'system_type' => 2,

                // 对于引用字段而言，这几个字段固定
                'allow_setting' => 1,
                'allow_setting_show' => 1,
                'allow_setting_required' => 0,
                'allow_setting_default' => 0,
                'allow_setting_tips' => 0,
                'is_writable' => 0,
                'required' => 0,
                'default_value' => '',
                'tips' => '',
                'show_flag' => 1,
                // 'columns' => ["data_key" => "ext_text_1"],
                'field_relations' => [
                    'relation_object_name' => 'objOrder',
                    'relation_object_function_type' => \common\library\object\field\FieldConstant::FUNCTION_TYPE_NORMAL,
                    'relation_field' => 'order_id',
                    'relation_object_field' => '3461890162',
                    'relation_object_relation_field' => 'order_id'
                ]
            ],

            [
                'object_name' => \common\library\object\object_define\Constant::OBJ_PURCHASE_ORDER_PRODUCT,
                'field' => '1314181470',
                'field_name' => '订单星级（引引）',
                'group_id' => 0,
                'field_type' => \common\library\object\field\FieldConstant::TYPE_SELECT,
                'array_flag' => 0,
                'function_type' => \common\library\object\field\FieldConstant::FUNCTION_TYPE_QUOTE,
                'ext_info' => [],
                'system_type' => 2,

                // 对于引用字段而言，这几个字段固定
                'allow_setting' => 1,
                'allow_setting_show' => 1,
                'allow_setting_required' => 0,
                'allow_setting_default' => 0,
                'allow_setting_tips' => 0,
                'is_writable' => 0,
                'required' => 0,
                'default_value' => '',
                'tips' => '',
                'show_flag' => 1,
                // 'columns' => ["data_key" => "ext_varchar_1"],
                'field_relations' => [
                    'relation_object_name' => \common\library\object\object_define\Constant::OBJ_PURCHASE_ORDER,
                    'relation_object_function_type' => \common\library\object\field\FieldConstant::FUNCTION_TYPE_QUOTE,
                    'relation_field' => 'purchase_order_id',
                    'relation_object_field' => '3670626515',
                    'relation_object_relation_field' => 'purchase_order_id'
                ]
            ],

            [
                'object_name' => \common\library\object\object_define\Constant::OBJ_ORDER,
                'field' => '1448503525',
                'field_name' => '平均采购单价',
                'group_id' => \common\library\custom_field\CustomFieldService::ORDER_GROUP_PRODUCT,
                'field_type' => \common\library\object\field\FieldConstant::TYPE_NUMBER,
                'array_flag' => 0,
                'function_type' => \common\library\object\field\FieldConstant::FUNCTION_TYPE_CALCULATE,
                'ext_info' => [
                    "expression" => "avg({order_id.objPurchaseOrderProduct.unit_price})",//计算公式
                    "empty" => 0,                                        //参与运算的字段值为空如何处理  0, null
                    "remark" => "",                                    //备注
                    "decimal" => 4,                                    //小数保存几位
                    "decimal_logic" => "floor"                //小数位取数逻辑
                ],
                'system_type' => 2,

                // 对于引用字段而言，这几个字段固定
                'allow_setting' => 1,
                'allow_setting_show' => 1,
                'allow_setting_required' => 0,
                'allow_setting_default' => 0,
                'allow_setting_tips' => 0,
                'is_writable' => 0,
                'required' => 0,
                'default_value' => 0,
                'tips' => '',
                'show_flag' => 1,
                // 'columns' => ["data_key" => "ext_numeric_3"],
                'field_relations' => [
                    'relation_object_name' => 'objPurchaseOrderProduct',
                    'relation_object_function_type' => \common\library\object\field\FieldConstant::FUNCTION_TYPE_NORMAL,
                    'relation_field' => 'order_id',
                    'relation_object_field' => 'unit_price',
                    'relation_object_relation_field' => 'order_id'
                ]
            ],

            [
                'object_name' => \common\library\object\object_define\Constant::OBJ_PURCHASE_ORDER,
                'field' => 'amount_rmb',
                'field_name' => '订单金额(CNY)',
                'group_id' => \common\library\custom_field\CustomFieldService::PURCHASE_ORDER_GROUP_CHARGE,
                'field_type' => \common\library\object\field\FieldConstant::TYPE_NUMBER,
                'array_flag' => 0,
                'function_type' => \common\library\object\field\FieldConstant::FUNCTION_TYPE_FORMULA,
                'ext_info' => [
                    "expression" => "{purchase_order_id.objPurchaseOrder.amount}*{purchase_order_id.objPurchaseOrder.exchange_rate}/100",//计算公式
                    "empty" => 0,                                        //参与运算的字段值为空如何处理  0, null
                    "remark" => "",                                    //备注
                    "decimal" => 4,                                    //小数保存几位
                    "decimal_logic" => "floor"                //小数位取数逻辑
                ],
                'system_type' => 1,

                // 对于引用字段而言，这几个字段固定
                'allow_setting' => 1,
                'allow_setting_show' => 1,
                'allow_setting_required' => 0,
                'allow_setting_default' => 0,
                'allow_setting_tips' => 0,
                'is_writable' => 0,
                'required' => 0,
                'default_value' => '',
                'tips' => '',
                'show_flag' => 1,
                 'columns' => ["data_key" => "amount_rmb"],
            ],

            [
                'object_name' => \common\library\object\object_define\Constant::OBJ_PURCHASE_ORDER,
                'field' => '1795328273',
                'field_name' => '采购订单折扣金额(CNY)',
                'group_id' => \common\library\custom_field\CustomFieldService::PURCHASE_ORDER_GROUP_BASIC,
                'field_type' => \common\library\object\field\FieldConstant::TYPE_NUMBER,
                'array_flag' => 0,
                'function_type' => \common\library\object\field\FieldConstant::FUNCTION_TYPE_FORMULA,
                'ext_info' => [
                    "expression" => "{purchase_order_id.objPurchaseOrder.amount_rmb}*{purchase_order_id.objPurchaseOrder.3461890166}",//计算公式
                    "empty" => 0,                                        //参与运算的字段值为空如何处理  0, null
                    "remark" => "",                                    //备注
                    "decimal" => 4,                                    //小数保存几位
                    "decimal_logic" => "floor"                //小数位取数逻辑
                ],
                'system_type' => 2,

                // 对于引用字段而言，这几个字段固定
                'allow_setting' => 1,
                'allow_setting_show' => 1,
                'allow_setting_required' => 0,
                'allow_setting_default' => 0,
                'allow_setting_tips' => 0,
                'is_writable' => 0,
                'required' => 0,
                'default_value' => 0,
                'tips' => '',
                'show_flag' => 1,
                // 'columns' => ["data_key" => "ext_numeric_4"],
            ],

            [
                'object_name' => \common\library\object\object_define\Constant::OBJ_PURCHASE_ORDER,
                'field' => '3115687443',
                'field_name' => '采购单占订单金额比例',
                'group_id' => \common\library\custom_field\CustomFieldService::PURCHASE_ORDER_GROUP_BASIC,
                'field_type' => \common\library\object\field\FieldConstant::TYPE_PERCENTAGE,
                'array_flag' => 0,
                'function_type' => \common\library\object\field\FieldConstant::FUNCTION_TYPE_FORMULA,
                'ext_info' => [
                    "expression" => "({purchase_order_id.objPurchaseOrder.amount}+{purchase_order_id.objPurchaseOrder.addition_cost_amount})/{refer_order_id.objOrder.amount}",//计算公式
                    "empty" => 0,                                        //参与运算的字段值为空如何处理  0, null
                    "remark" => "",                                    //备注
                    "decimal" => 4,                                    //小数保存几位
                    "decimal_logic" => "floor"                //小数位取数逻辑
                ],
                'system_type' => 2,

                // 对于引用字段而言，这几个字段固定
                'allow_setting' => 1,
                'allow_setting_show' => 1,
                'allow_setting_required' => 0,
                'allow_setting_default' => 0,
                'allow_setting_tips' => 0,
                'is_writable' => 0,
                'required' => 0,
                'default_value' => 0,
                'tips' => '',
                'show_flag' => 1,
                // 'columns' => ["data_key" => "ext_numeric_5"],
            ],

            [
                'object_name' => \common\library\object\object_define\Constant::OBJ_PURCHASE_ORDER,
                'field' => '3115687445',
                'field_name' => '采购单占订单金额比例+1',
                'group_id' => \common\library\custom_field\CustomFieldService::PURCHASE_ORDER_GROUP_BASIC,
                'field_type' => \common\library\object\field\FieldConstant::TYPE_PERCENTAGE,
                'array_flag' => 0,
                'function_type' => \common\library\object\field\FieldConstant::FUNCTION_TYPE_FORMULA,
                'ext_info' => [
                    "expression" => "{purchase_order_id.objPurchaseOrder.3115687443}+1",//计算公式
                    "empty" => 0,                                        //参与运算的字段值为空如何处理  0, null
                    "remark" => "",                                    //备注
                    "decimal" => 4,                                    //小数保存几位
                    "decimal_logic" => "floor"                //小数位取数逻辑
                ],
                'system_type' => 2,

                // 对于引用字段而言，这几个字段固定
                'allow_setting' => 1,
                'allow_setting_show' => 1,
                'allow_setting_required' => 0,
                'allow_setting_default' => 0,
                'allow_setting_tips' => 0,
                'is_writable' => 0,
                'required' => 0,
                'default_value' => 0,
                'tips' => '',
                'show_flag' => 1,
                // 'columns' => ["data_key" => "ext_numeric_6"],
            ],

            [
                'object_name' => \common\library\object\object_define\Constant::OBJ_ORDER_PRODUCT,
                'field' => '2663014790',
                'field_name' => '订单明细采购加权单价',
                'group_id' => \common\library\custom_field\CustomFieldService::ORDER_GROUP_PRODUCT,
                'field_type' => \common\library\object\field\FieldConstant::TYPE_PRICE,
                'array_flag' => 0,
                'function_type' => \common\library\object\field\FieldConstant::FUNCTION_TYPE_FORMULA,
                'ext_info' => [
                    "expression" => "{id.objOrderProduct.**********}*{order_id.objOrder.1448503525}",//计算公式
                    "empty" => 0,                                        //参与运算的字段值为空如何处理  0, null
                    "remark" => "",                                    //备注
                    "decimal" => 4,                                    //小数保存几位
                    "decimal_logic" => "floor"                //小数位取数逻辑
                ],
                'system_type' => 2,

                // 对于引用字段而言，这几个字段固定
                'allow_setting' => 1,
                'allow_setting_show' => 1,
                'allow_setting_required' => 0,
                'allow_setting_default' => 0,
                'allow_setting_tips' => 0,
                'is_writable' => 0,
                'required' => 0,
                'default_value' => 0,
                'tips' => '',
                'show_flag' => 1,
                // 'columns' => ["data_key" => "ext_numeric_1"],
            ],

            [
                'object_name' => \common\library\object\object_define\Constant::OBJ_PURCHASE_ORDER_PRODUCT,
                'field' => '2258577630',
                'field_name' => '采购明细含替补数总金额',
                'group_id' => \common\library\custom_field\CustomFieldService::PURCHASE_ORDER_GROUP_PRODUCT,
                'field_type' => \common\library\object\field\FieldConstant::TYPE_PRICE,
                'array_flag' => 0,
                'function_type' => \common\library\object\field\FieldConstant::FUNCTION_TYPE_FORMULA,
                'ext_info' => [
                    "expression" => "({purchase_order_product_id.objPurchaseOrderProduct.count}+{purchase_order_product_id.objPurchaseOrderProduct.3461890168})*{purchase_order_product_id.objPurchaseOrderProduct.unit_price}",//计算公式
                    "empty" => 0,                                        //参与运算的字段值为空如何处理  0, null
                    "remark" => "",                                    //备注
                    "decimal" => 4,                                    //小数保存几位
                    "decimal_logic" => "floor"                //小数位取数逻辑
                ],
                'system_type' => 2,

                // 对于引用字段而言，这几个字段固定
                'allow_setting' => 1,
                'allow_setting_show' => 1,
                'allow_setting_required' => 0,
                'allow_setting_default' => 0,
                'allow_setting_tips' => 0,
                'is_writable' => 0,
                'required' => 0,
                'default_value' => 0,
                'tips' => '',
                'show_flag' => 1,
                // 'columns' => ["data_key" => "ext_numeric_2"],
            ],

            [
                'object_name' => \common\library\object\object_define\Constant::OBJ_PURCHASE_ORDER,
                'field' => 'product_total_count',
                'field_name' => '产品总数量',
                'group_id' => \common\library\custom_field\CustomFieldService::PURCHASE_ORDER_GROUP_PRODUCT,
                'field_type' => \common\library\object\field\FieldConstant::TYPE_NUMBER,
                'array_flag' => 0,
                'function_type' => \common\library\object\field\FieldConstant::FUNCTION_TYPE_CALCULATE,
                'ext_info' => [
                    "expression" => "sum({purchase_order_id.objPurchaseOrderProduct.count})",//计算公式
                    "empty" => 0,                                        //参与运算的字段值为空如何处理  0, null
                    "remark" => "",                                    //备注
                    "decimal" => 4,                                    //小数保存几位
                    "decimal_logic" => "floor"                //小数位取数逻辑
                ],
                'system_type' => 1,

                // 对于引用字段而言，这几个字段固定
                'allow_setting' => 1,
                'allow_setting_show' => 1,
                'allow_setting_required' => 0,
                'allow_setting_default' => 0,
                'allow_setting_tips' => 0,
                'is_writable' => 0,
                'required' => 0,
                'default_value' => '',
                'tips' => '',
                'show_flag' => 1,
                 'columns' => ["data_key" => "product_total_count"],
                'field_relations' => [
                    'relation_object_name' => 'objPurchaseOrderProduct',
                    'relation_object_function_type' => \common\library\object\field\FieldConstant::FUNCTION_TYPE_NORMAL,
                    'relation_field' => 'purchase_order_id',
                    'relation_object_field' => 'count',
                    'relation_object_relation_field' => 'purchase_order_id',
                ]
            ],

            [
                'object_name' => \common\library\object\object_define\Constant::OBJ_ORDER,
                'field' => '2493721613',
                'field_name' => '采购平均成本（人民币）',
                'group_id' => \common\library\custom_field\CustomFieldService::ORDER_GROUP_FEE,
                'field_type' => \common\library\object\field\FieldConstant::TYPE_NUMBER,
                'array_flag' => 0,
                'function_type' => \common\library\object\field\FieldConstant::FUNCTION_TYPE_CALCULATE,
                'ext_info' => [
                    "expression" => "avg({refer_order_id.objPurchaseOrder.amount_rmb})",//计算公式
                    "empty" => 0,                                        //参与运算的字段值为空如何处理  0, null
                    "remark" => "",                                    //备注
                    "decimal" => 4,                                    //小数保存几位
                    "decimal_logic" => "floor"                //小数位取数逻辑
                ],
                'system_type' => 2,

                // 对于引用字段而言，这几个字段固定
                'allow_setting' => 1,
                'allow_setting_show' => 1,
                'allow_setting_required' => 0,
                'allow_setting_default' => 0,
                'allow_setting_tips' => 0,
                'is_writable' => 0,
                'required' => 0,
                'default_value' => 0,
                'tips' => '',
                'show_flag' => 1,
                // 'columns' => ["data_key" => "ext_numeric_2"],
                'field_relations' => [
                    'relation_object_name' => 'objPurchaseOrder',
                    'relation_object_function_type' => \common\library\object\field\FieldConstant::FUNCTION_TYPE_FORMULA,
                    'relation_field' => 'order_id',
                    'relation_object_field' => 'amount_rmb',
                    'relation_object_relation_field' => 'refer_order_id'
                ]
            ],

            [
                'object_name' => \common\library\object\object_define\Constant::OBJ_PURCHASE_ORDER_PRODUCT,
                'field' => '6799338369',
                'field_name' => '基数金额-采',
                'group_id' => \common\library\custom_field\CustomFieldService::PURCHASE_ORDER_GROUP_PRODUCT,
                'field_type' => \common\library\object\field\FieldConstant::TYPE_NUMBER,
                'array_flag' => 0,
                'function_type' => \common\library\object\field\FieldConstant::FUNCTION_TYPE_FORMULA,
                'ext_info' => [ // 采.基数 * 采明.明细总金额 / 100
                    "expression" => "{purchase_order_id.objPurchaseOrder.3471061495}*{purchase_order_product_id.objPurchaseOrderProduct.cost_amount}/100",
                    "empty" => 0,                                        //参与运算的字段值为空如何处理  0, null
                    "remark" => "",                                    //备注
                    "decimal" => 4,                                    //小数保存几位
                    "decimal_logic" => "floor"                //小数位取数逻辑
                ],
                'system_type' => 2,

                // 对于引用字段而言，这几个字段固定
                'allow_setting' => 1,
                'allow_setting_show' => 1,
                'allow_setting_required' => 0,
                'allow_setting_default' => 0,
                'allow_setting_tips' => 0,
                'is_writable' => 0,
                'required' => 0,
                'default_value' => '',
                'tips' => '',
                'show_flag' => 1,
                // 'columns' => ["data_key" => "amount_rmb"],
            ],

            [
                'object_name' => \common\library\object\object_define\Constant::OBJ_ORDER_PRODUCT,
                'field' => '7923641275',
                'field_name' => '总基数金额-订明',
                'group_id' => \common\library\custom_field\CustomFieldService::ORDER_GROUP_PRODUCT,
                'field_type' => \common\library\object\field\FieldConstant::TYPE_NUMBER,
                'array_flag' => 0,
                'function_type' => \common\library\object\field\FieldConstant::FUNCTION_TYPE_CALCULATE,
                'ext_info' => [ // sum(基数金额-采明)
                    "expression" => "sum({invoice_product_id.objPurchaseOrderProduct.6799338369})",
                    "empty" => 0,                                        //参与运算的字段值为空如何处理  0, null
                    "remark" => "",                                    //备注
                    "decimal" => 4,                                    //小数保存几位
                    "decimal_logic" => "floor"                //小数位取数逻辑
                ],
                'system_type' => 2,

                // 对于引用字段而言，这几个字段固定
                'allow_setting' => 1,
                'allow_setting_show' => 1,
                'allow_setting_required' => 0,
                'allow_setting_default' => 0,
                'allow_setting_tips' => 0,
                'is_writable' => 0,
                'required' => 0,
                'default_value' => '',
                'tips' => '',
                'show_flag' => 1,
                // 'columns' => ["data_key" => "amount_rmb"],
            ],

            [
                'object_name' => \common\library\object\object_define\Constant::OBJ_ORDER,
                'field' => '3630919642',
                'field_name' => '最大汇总基数金额-订单',
                'group_id' => \common\library\custom_field\CustomFieldService::ORDER_GROUP_BASIC,
                'field_type' => \common\library\object\field\FieldConstant::TYPE_NUMBER,
                'array_flag' => 0,
                'function_type' => \common\library\object\field\FieldConstant::FUNCTION_TYPE_CALCULATE,
                'ext_info' => [ // sum(基数金额-采明)
                    "expression" => "sum({order_id.objOrderProduct.7923641275})",
                    "empty" => 0,                                        //参与运算的字段值为空如何处理  0, null
                    "remark" => "",                                    //备注
                    "decimal" => 4,                                    //小数保存几位
                    "decimal_logic" => "floor"                //小数位取数逻辑
                ],
                'system_type' => 2,

                // 对于引用字段而言，这几个字段固定
                'allow_setting' => 1,
                'allow_setting_show' => 1,
                'allow_setting_required' => 0,
                'allow_setting_default' => 0,
                'allow_setting_tips' => 0,
                'is_writable' => 0,
                'required' => 0,
                'default_value' => '',
                'tips' => '',
                'show_flag' => 1,
                // 'columns' => ["data_key" => "amount_rmb"],
            ],

            [
                'object_name' => \common\library\object\object_define\Constant::OBJ_ORDER,
                'field' => '3470341324',
                'field_name' => '汇总含税成本价',
                'group_id' => \common\library\custom_field\CustomFieldService::ORDER_GROUP_BASIC,
                'field_type' => \common\library\object\field\FieldConstant::TYPE_NUMBER,
                'array_flag' => 0,
                'function_type' => \common\library\object\field\FieldConstant::FUNCTION_TYPE_CALCULATE,
                'ext_info' => [ // sum(基数金额-采明)
                    "expression" => "sum({order_id.objOrderProduct.cost_with_tax})",
                    "empty" => 0,                                        //参与运算的字段值为空如何处理  0, null
                    "remark" => "",                                    //备注
                    "decimal" => 4,                                    //小数保存几位
                    "decimal_logic" => "floor"                //小数位取数逻辑
                ],
                'system_type' => 2,

                // 对于引用字段而言，这几个字段固定
                'allow_setting' => 1,
                'allow_setting_show' => 1,
                'allow_setting_required' => 0,
                'allow_setting_default' => 0,
                'allow_setting_tips' => 0,
                'is_writable' => 0,
                'required' => 0,
                'default_value' => '',
                'tips' => '',
                'show_flag' => 1,
                // 'columns' => ["data_key" => "amount_rmb"],
            ],

            [
                'object_name' => \common\library\object\object_define\Constant::OBJ_PURCHASE_ORDER_PRODUCT,
                'field' => '7479886032',
                'field_name' => '明细基数率-采明',
                'group_id' => \common\library\custom_field\CustomFieldService::PURCHASE_ORDER_GROUP_PRODUCT,
                'field_type' => \common\library\object\field\FieldConstant::TYPE_NUMBER,
                'array_flag' => 0,
                'function_type' => \common\library\object\field\FieldConstant::FUNCTION_TYPE_FORMULA,
                'ext_info' => [ // 基数金额 / 总基数金额-订明
                    "expression" => "{purchase_order_product_id.objPurchaseOrderProduct.6799338369}/{invoice_product_id.objOrderProduct.7923641275}",
                    "empty" => 0,                                        //参与运算的字段值为空如何处理  0, null
                    "remark" => "",                                    //备注
                    "decimal" => 4,                                    //小数保存几位
                    "decimal_logic" => "floor"                //小数位取数逻辑
                ],
                'system_type' => 2,

                // 对于引用字段而言，这几个字段固定
                'allow_setting' => 1,
                'allow_setting_show' => 1,
                'allow_setting_required' => 0,
                'allow_setting_default' => 0,
                'allow_setting_tips' => 0,
                'is_writable' => 0,
                'required' => 0,
                'default_value' => '',
                'tips' => '',
                'show_flag' => 1,
                // 'columns' => ["data_key" => "amount_rmb"],
            ],
        ];
    }

    /**
     * debugphp -d 'xdebug.client_port=9004' /data/codebase/bob/php-crm/protected/yiic-test zbp TestUpdatorUpdate
     * 调试关联更新 - 更新普通字段场景
     */
    public function actionTestUpdatorUpdate(){
        $messageJob = json_decode('[{"messageId":"1735282701130-0","msgType":"UPDATE","clientId":"365286","tableName":"tbl_order","oldValues":{"handler":"{249526016}","update_time":"2024-12-2714:53:19","user_id":"{249526007,249526016}","client_id":"365286","order_id":"5140653906"},"newValues":{"handler":"{249526017}","update_time":"2024-12-2714:58:21","user_id":"{249526007,249526016,249526017}","client_id":"365286","order_id":"5140653906"},"timeStamp":1735282701129972700,"timeFormat":"2024-12-2714:58:21"}]', true);
        $message = new \common\library\server\binlog_to_biz\MessageJob($messageJob);
        $message->handle();
    }

    /**
     * debugphp -d 'xdebug.client_port=9004' /data/codebase/bob/php-crm/protected/yiic-test zbp TestUpdateRaw
     */
    public function actionTestUpdateRaw(){
        $message = json_decode('[{\"messageId\":\"1708222328458-0\",\"msgType\":\"UPDATE\",\"clientId\":\"334110\",\"tableName\":\"tbl_company\",\"oldValues\":{},\"newValues\":{}\",\"client_id\":\"334110\",\"company_id\":\"3478803966\"},\"timeStamp\":1708222328457721166,\"timeFormat\":\"2024-02-18 10:12:08\"}]', true);

        $clientId = 334110;
        $messageType = 'UPDATE';
        $message = json_decode($message, true);
//        $message = $this->prepareData($clientId, $message, $messageType);
//        if(!$message){
//            self::info("Fail to prepareData");
//            return false;
//        }
        $job = new MessageJob($message);
        $job->handle();
    }

    protected function prepareData($clientId, $messages, $msgType, $messageId=999){
        $prepareData = [];
        $listenConfig = $this->listenConfig();
        foreach($messages as $body){
            $body['OldValues'] = $body['OldValues'] ?? [];
            $body['NewValues'] = $body['NewValues'] ?? [];
            $oldValues = [];
            $newValues = [];
            $listenFieldConfig = $listenConfig[$body['TableName']]['listen_field'] ?? [];
            $reportFieldConfig = $listenConfig[$body['TableName']]['report_field'] ?? [];
            foreach ($listenFieldConfig as $field) {
                if (array_key_exists($field, $body['OldValues'])) {
                    if ($body['OldValues'][$field] !== $body['NewValues'][$field]) {
                        $oldValues[$field] = $body['OldValues'][$field];
                        $newValues[$field] = $body['NewValues'][$field];
                    }
                }
            }

            //需要监听的字段无变更
            if ($msgType == 'UPDATE' && empty($oldValues) && empty($newValues)) {
                return false;
            }

            //新建，编辑 数据取自NewValues  删除取自OldValues
            foreach ($reportFieldConfig as $field) {
                if ($msgType == 'DELETE') {
                    if(array_key_exists($field, $body['OldValues'])) {
                        $oldValues[$field] = $body['OldValues'][$field];
                        $newValues[$field] = $body['NewValues'][$field]??null;
                    }
                }else{
                    //新建&编辑从OldValues 获取数据
                    if(array_key_exists($field, $body['NewValues'])) {
                        $oldValues[$field] = $body['OldValues'][$field]??null;
                        $newValues[$field] = $body['NewValues'][$field];
                    }
                }
            }

            //需要监听的字段无变更
            if (empty($oldValues) && empty($newValues)) {
                return false;
            }

            $prepareData[] = [
                "messageId" => $messageId,
                'msgType' => $body['MsgType'],
                "clientId" => $clientId,
                'tableName' => $body['TableName'],
                'oldValues' => $oldValues,
                'newValues' => $newValues,
                'timeStamp' => $body['TimeStamp'],
                'timeFormat' => $body['TimeFormat']
            ];
        }
        return $prepareData;
    }

    protected function listenConfig()
    {
        $sql = "SELECT *  FROM prometheus.tbl_replication_listener WHERE db_type ='postgresql'";
        $result = \Yii::app()->prometheus_db->createCommand($sql)->queryAll();
        $listenConfig = [];
        // print_r($result);exit();
        foreach ($result as $item) {
            $listenConfig[$item['table']]['listen_field'] = json_decode($item['listen_field'],true);
            $listenConfig[$item['table']]['report_field'] = json_decode($item['report_field'],true);
        }

        return $listenConfig;
    }

    /**
     * debugphp -d 'xdebug.client_port=9004' /data/codebase/bob/php-crm/protected/yiic-test zbp TestUpdatorInsert
     * 调试关联更新 - 新增场景
     */
    public function actionTestUpdatorInsert(){
        $message = json_decode('[{"messageId":"1733219559123-0","msgType":"INSERT","clientId":"351352","tableName":"tbl_order","oldValues":{"client_id":null,"order_id":null},"newValues":{"client_id":"351352","order_id":"5995394"},"timeStamp":1733219559123039538,"timeFormat":"2024-12-0317:52:39"}]', true);
        $messageJob = new \common\library\server\binlog_to_biz\MessageJob($message);
        $messageJob->handle(\common\library\server\binlog_to_biz\MessageConsumer::RUN_MODE_NORMAL);
    }

    /**
     * debugphp -d 'xdebug.client_port=9004' /data/codebase/bob/php-crm/protected/yiic-test zbp TestUpdatorDelete
     * 调试关联更新 - 删除场景
     */
    public function actionTestUpdatorDelete(){
        $clientId = 333577;
        $message = [    // 删除订单 和 订单明细
            [
                "msgType" => 'UPDATE',
                'tableName' => 'tbl_order',
                "objectName" => "objOrder",
                "clientId" => $clientId,
                'oldValues' => [
                    'client_id' => 333577,
                    'order_id' => 3472817222,
                    'enable_flag' => 1
                ],
                'newValues' => [
                    'client_id' => 333577,
                    'order_id' => 3472817222,
                    'enable_flag' => 0
                ],
            ],
            [
                "msgType" => 'UPDATE',
                "objectName" => "objOrderProduct",
                'tableName' => 'tbl_invoice_product_record',
                "clientId" => $clientId,
                'oldValues' => [
                    'client_id' => 333577,
                    'id' => 3472817220,
                    'enable_flag' => 1
                ],
                'newValues' => [
                    'client_id' => 333577,
                    'id' => 3472817220,
                    'enable_flag' => 0
                ],
            ],      // 订单明细
            [
                "msgType" => 'UPDATE',
                'tableName' => 'tbl_invoice_product_record',
                "objectName" => "objOrderProduct",
                "clientId" => $clientId,
                'oldValues' => [
                    'client_id' => 333577,
                    'id' => 3472817219,
                    'enable_flag' => 1
                ],
                'newValues' => [
                    'client_id' => 333577,
                    'id' => 3472817219,
                    'enable_flag' => 0
                ],
            ],
            [
                "msgType" => 'UPDATE',
                'tableName' => 'tbl_invoice_product_record',
                "objectName" => "objOrderProduct",
                "clientId" => $clientId,
                'oldValues' => [
                    'client_id' => 333577,
                    'id' => 3472817221,
                    'enable_flag' => 1
                ],
                'newValues' => [
                    'client_id' => 333577,
                    'id' => 3472817221,
                    'enable_flag' => 0
                ],
            ],
        ];
        $messageJob = new \common\library\server\binlog_to_biz\MessageJob($message);
        $messageJob->handle();
    }

    /**
     * debugphp -d 'xdebug.client_port=9004' /data/codebase/bob/php-crm/protected/yiic-test zbp TestUpdatorRecover
     * 调试关联更新 - 回收箱恢复场景
     */
    public function actionTestUpdatorRecover(){
        $clientId = 333577;

        $message = [    // 恢复订单 和 订单明细
            [
                "msgType" => 'UPDATE',
                'tableName' => 'tbl_purchase_order_product',
                "objectName" => "objPurchaseOrderProduct",
                "clientId" => $clientId,
                'oldValues' => [
                    'client_id' => 333577,
                    'purchase_order_product_id' => 3472825895,
                    'enable_flag' => 0
                ],
                'newValues' => [
                    'client_id' => 333577,
                    'purchase_order_product_id' => 3472825895,
                    'enable_flag' => 1
                ],
            ],
            [
                "msgType" => 'UPDATE',
                'tableName' => 'tbl_purchase_order',
                "objectName" => "objPurchaseOrder",
                "clientId" => $clientId,
                'oldValues' => [
                    'client_id' => 333577,
                    'purchase_order_id' => 3472825330,
                    'enable_flag' => 0
                ],
                'newValues' => [
                    'client_id' => 333577,
                    'purchase_order_id' => 3472825330,
                    'enable_flag' => 1
                ],
            ],
            [
                "msgType" => 'UPDATE',
                'tableName' => 'tbl_purchase_order_product',
                "objectName" => "objPurchaseOrderProduct",
                "clientId" => $clientId,
                'oldValues' => [
                    'client_id' => 333577,
                    'purchase_order_product_id' => 3472824279,
                    'enable_flag' => 0
                ],
                'newValues' => [
                    'client_id' => 333577,
                    'purchase_order_product_id' => 3472824279,
                    'enable_flag' => 1
                ],
            ],
            [
                "msgType" => 'UPDATE',
                'tableName' => 'tbl_purchase_order_product',
                "clientId" => $clientId,
                "objectName" => "objPurchaseOrderProduct",
                'oldValues' => [
                    'client_id' => 333577,
                    'purchase_order_product_id' => 3472824280,
                    'enable_flag' => 0
                ],
                'newValues' => [
                    'client_id' => 333577,
                    'purchase_order_product_id' => 3472824280,
                    'enable_flag' => 1
                ],
            ],
            [
                "msgType" => 'UPDATE',
                'tableName' => 'tbl_purchase_order_product',
                "clientId" => $clientId,
                "objectName" => "objPurchaseOrderProduct",
                'oldValues' => [
                    'client_id' => 333577,
                    'purchase_order_product_id' => 3472825331,
                    'enable_flag' => 2
                ],
                'newValues' => [
                    'client_id' => 333577,
                    'purchase_order_product_id' => 3472825331,
                    'enable_flag' => 1
                ],
            ],
            [
                "msgType" => 'UPDATE',
                'tableName' => 'tbl_purchase_order_product',
                "clientId" => $clientId,
                "objectName" => "objPurchaseOrderProduct",
                'oldValues' => [
                    'client_id' => 333577,
                    'purchase_order_product_id' => 3472825332,
                    'enable_flag' => 2
                ],
                'newValues' => [
                    'client_id' => 333577,
                    'purchase_order_product_id' => 3472825332,
                    'enable_flag' => 1
                ],
            ],
            [
                "msgType" => 'UPDATE',
                'tableName' => 'tbl_purchase_order_product',
                "objectName" => "objPurchaseOrderProduct",
                "clientId" => $clientId,
                'oldValues' => [
                    'client_id' => 333577,
                    'purchase_order_product_id' => 3472825333,
                    'enable_flag' => 2
                ],
                'newValues' => [
                    'client_id' => 333577,
                    'purchase_order_product_id' => 3472825333,
                    'enable_flag' => 1
                ],
            ],
            [
                "msgType" => 'UPDATE',
                'tableName' => 'tbl_order',
                "objectName" => "objOrder",
                "clientId" => $clientId,
                'oldValues' => [
                    'client_id' => 333577,
                    'order_id' => 3472817222,
                    'enable_flag' => 0
                ],
                'newValues' => [
                    'client_id' => 333577,
                    'order_id' => 3472817222,
                    'enable_flag' => 1
                ],
            ],
            [
                "msgType" => 'UPDATE',
                "objectName" => "objOrderProduct",
                'tableName' => 'tbl_invoice_product_record',
                "clientId" => $clientId,
                'oldValues' => [
                    'client_id' => 333577,
                    'id' => 3472817220,
                    'enable_flag' => 0
                ],
                'newValues' => [
                    'client_id' => 333577,
                    'id' => 3472817220,
                    'enable_flag' => 1
                ],
            ],      // 订单明细
            [
                "msgType" => 'UPDATE',
                'tableName' => 'tbl_invoice_product_record',
                "objectName" => "objOrderProduct",
                "clientId" => $clientId,
                'oldValues' => [
                    'client_id' => 333577,
                    'id' => 3472817219,
                    'enable_flag' => 0
                ],
                'newValues' => [
                    'client_id' => 333577,
                    'id' => 3472817219,
                    'enable_flag' => 1
                ],
            ],
            [
                "msgType" => 'UPDATE',
                'tableName' => 'tbl_invoice_product_record',
                "objectName" => "objOrderProduct",
                "clientId" => $clientId,
                'oldValues' => [
                    'client_id' => 333577,
                    'id' => 3472817221,
                    'enable_flag' => 0
                ],
                'newValues' => [
                    'client_id' => 333577,
                    'id' => 3472817221,
                    'enable_flag' => 1
                ],
            ],
        ];
        $messageJob = new \common\library\server\binlog_to_biz\MessageJob($message);
        $messageJob->handle();
    }

    /**
     * debugphp -d 'xdebug.client_port=9004' /data/codebase/bob/php-crm/protected/yiic-test zbp TestUpdatorRelationTransfer
     * 调试关联更新 - 更新所关联的上游对象id的场景
     */
    public function actionTestUpdatorRelationTransfer(){
        $clientId = 14119;

        $message = json_decode('[{"messageId":"1718589928661-0","msgType":"UPDATE","clientId":"14119","tableName":"tbl_order","oldValues":{"client_id":"14119","opportunity_id":"0","update_time":"2024-06-1710:04:32","order_id":"3565158997"},"newValues":{"client_id":"14119","opportunity_id":"3452587915","update_time":"2024-06-1710:05:28","order_id":"3565158997"},"timeStamp":1718589928661231000,"timeFormat":"2024-06-1710:05:28","objectName":"objOrder","relation_field":["opportunity_id"]}]', true);
        $messageJob = new \common\library\server\binlog_to_biz\MessageJob($message);
        $messageJob->handle('test');
    }

    /**
     * debugphp -d 'xdebug.client_port=9004' /data/codebase/bob/php-crm/protected/yiic-test zbp TestOrm
     */
    public function actionTestOrm(){
        $clientId = 14119;
//        $filter = new \common\library\product_v2\ProductFilter($clientId);
//        $filter->update_time = new \xiaoman\orm\database\data\DateRange('2023-01-01', '2023-12-31');
//        $filter->limit(1);
//        echo json_encode($filter->rawData());
//        die;
        $filter = new \common\library\oms\order\OrderFilter($clientId);
        $filter->enable_flag = 1;
        $filter->update_time = new \xiaoman\orm\database\data\DateRange('2023-01-01', '2023-12-31');
        $filter->limit(1);
        echo json_encode($filter->rawData());
        echo PHP_EOL;
        echo $filter->count();
    }

    /**
     * debugphp -d 'xdebug.client_port=9004' /data/codebase/bob/php-crm/protected/yiic-test zbp TestSyncCalculator
     */
    public function actionTestSyncCalculator(){
        $data = '[{"id":1,"name":"基本信息","can_add_custom_field":true,"is_hide":false,"fields":{"3461890158":"","3461890162":"","3461890164":"一星","3470341324":"50.0000","handler":[*********],"order_no":"3","name":"weihao001","status":"3267646361","opportunity_id":"0","company_id":"3267646934","customer_id":"3267646933","price_contract":"","currency":"USD","exchange_rate":"716.25","exchange_rate_usd":"100.00000","company_name":"111","transport_mode":"","amount":"14014.00","company_phone":"","receive_remittance_way":"","tax_refund_type":"2","shipment_deadline":"","company_fax":"","create_time":"2024-01-16 09:48:14","price_contract_remark":"","shipment_port":"","target_port":"","company_address":"768 Turnbull Canyon Rd,City Of Industry,CA,91745,United States of America","receive_remittance_remark":"","update_time":"2024-01-16 09:48:14","shipment_deadline_remark":"qweqw","customer_name":"","insurance_remark":"","account_date":"2024-01-16 00:00:00","customer_phone":"********","more_or_less":"","users":[{"rate":100,"user_id":"*********"}],"departments":[{"rate":100,"department_id":"0"}],"bank_info":"","order_contract":"","create_user":"*********","customer_email":"","package_remark":"","customer_address":"","remark":"qweqw","marked":"","country":"US"},"list":[],"list_fields":[]},{"id":2,"name":"交易产品","can_add_custom_field":true,"is_hide":false,"fields":{"package_volume_amount":0,"package_gross_weight_amount":0,"product_total_count":60,"product_total_amount":"","amount_en_upper":"","product_total_amount_en_upper":""},"list":[{"**********":"","product_no":"7","product_id":"**********","product_image":"","product_name":"pink Polyester Animal","product_model":"pink Polyester Animal","sku_id":"**********","cost_with_tax":"30","unit_price":"100","gross_margin":70,"gross_profit_margin":70,"platform_product_info":"0","sku_attributes":[],"local_product_no":{"sku_code":"","product_id":0},"enable_count":0,"count":"20","other_cost":"2","unit":"Bag","package_unit":"","count_per_package":"","package_count":0,"package_volume":"","package_volume_subtotal":0,"package_gross_weight":"","package_gross_weight_subtotal":0,"description":"1.All Color, lock, hanger hole shape and all details same as clients pics. 2. Thickness 2.0mm as the material sample 3. Flower holes+ bottom round hole 4. Logo print as below pic 5. Each set with 3 bu","product_remark":"1.All Color, lock, hanger hole shape and all details same as clients pics. 2. Thickness 2.0mm as the material sample 3. Flower holes+ bottom round hole 4. Logo print as below pic 5. Each set with 3 bu","hs_code":"","vat_rate":"10.000000","tax_refund_rate":"5.000000","cost_amount":2002,"offer_data":"","unique_id":"3473506912","combine_product_id":"","combine_product_no":"","is_sub_product":"","product_cn_name":"pink Polyester Animal","product_disable_flag":"0","product_package_remark":"","product_type":"1","task_outbound_count":"","task_purchase_count":"","to_outbound_count":"","to_purchase_count":"","purchase_cost_unit_rmb":"","product_enable_flag":"1","refer_order_no":"3","todo_shipping_count":"20"},{"**********":"","3468961218":"","3468961479":"red Polyester Geometric","3468961771":"","3468961880":"0.0000","product_no":"6","product_id":"3461758403","product_image":"","product_name":"red Polyester Geometric","product_model":"red Polyester Geometric","sku_id":"3461758404","cost_with_tax":"20","unit_price":"300","gross_margin":280,"gross_profit_margin":93.3333,"platform_product_info":"0","sku_attributes":[],"local_product_no":{"sku_code":"","product_id":0},"enable_count":0,"count":"40","other_cost":"2","unit":"Ampere","package_unit":"","count_per_package":"","package_count":0,"package_volume":"","package_volume_subtotal":0,"package_gross_weight":"","package_gross_weight_subtotal":0,"description":"1.All Color, lock, hanger hole shape and all details same as clients pics. 2. Thickness 2.0mm as the material sample 3. Flower holes+ bottom round hole 4. Logo print as below pic 5. Each set with 2 bu","product_remark":"1.All Color, lock, hanger hole shape and all details same as clients pics. 2. Thickness 2.0mm as the material sample 3. Flower holes+ bottom round hole 4. Logo print as below pic 5. Each set with 2 bu","hs_code":"","vat_rate":"20.000000","tax_refund_rate":"10.000000","cost_amount":12002,"offer_data":"","unique_id":"3473506913","combine_product_id":"","combine_product_no":"","is_sub_product":"","product_cn_name":"red Polyester Geometric","product_disable_flag":"0","product_package_remark":"","product_type":"1","task_outbound_count":"","task_purchase_count":"","to_outbound_count":"","to_purchase_count":"","purchase_cost_unit_rmb":"","product_enable_flag":"1","refer_order_no":"3","todo_shipping_count":"40"}]},{"id":3,"name":"费用信息","can_add_custom_field":true,"is_hide":false,"fields":{"amount":14014,"addition_cost_amount":10,"addition_cost_amount_rmb":"71.6250","addition_cost_amount_usd":"10.0000","product_total_amount":14004,"product_total_amount_rmb":"100303.6500","product_total_amount_usd":"14004.0000","amount_usd":"14014.00000","amount_rmb":"100375.28","cash_collection_info.collect_amount_rmb":"","cash_collection_info.collect_amount_usd":"","cash_collection_percentage":"","cash_collection_info.not_collect_amount_rmb":"","cash_collection_info.not_collect_amount_usd":"","order_gross_margin":"12600.0000","order_gross_margin_cny":"90247.5000","order_gross_margin_usd":"12600.0000","cost_with_tax_total":1400},"list":[{"cost_name":"","percent_of_total_amount":{"percent_type":0,"percent_amount":10},"cost":"10","cost_item_relation_id":"3445260998","cost_remark":""}],"list_fields":[{"client_id":"333577","id":"cost_name","type":"2","base":"1","name":"费用名称","group_id":"3","field_type":"1","ext_info":[],"require":"1","edit_required":"0","disable_flag":"0","edit_hide":"0","default":"","edit_default":"0","hint":"","edit_hint":"1","is_editable":0,"is_list":"1","columns":["cost_name"],"relation_type":"0","relation_field":"","relation_field_type":"0","relation_field_name":"","relation_origin_type":"0","relation_origin_field":"","relation_origin_field_type":"0","order":"1447741785","app_order":"1447741785","enable_flag":"1","readonly":"0","create_time":"2023-04-12 18:39:06","update_time":"2023-04-12 18:39:06","unique_check":"0","unique_prevent":"1","unique_message":"","relation_origin_field_info":[],"value":"","edit_unique":"0","edit_unique_check":"1","edit_unique_prevent":"1","edit_unique_message":"1"},{"client_id":"333577","id":"percent_of_total_amount","type":"2","base":"1","name":"占产品总金额百分比","group_id":"3","field_type":"5","ext_info":[],"require":"1","edit_required":"0","disable_flag":"0","edit_hide":"0","default":"","edit_default":"0","hint":"","edit_hint":"1","is_editable":0,"is_list":"1","columns":["percent_type","percent_amount"],"relation_type":"0","relation_field":"","relation_field_type":"0","relation_field_name":"","relation_origin_type":"0","relation_origin_field":"","relation_origin_field_type":"0","order":"1302967607","app_order":"1302967607","enable_flag":"1","readonly":"0","create_time":"2023-04-12 18:39:06","update_time":"2023-04-12 18:39:06","unique_check":"0","unique_prevent":"1","unique_message":"","relation_origin_field_info":[],"value":{"percent_type":"","percent_amount":""},"edit_unique":"0","edit_unique_check":"1","edit_unique_prevent":"1","edit_unique_message":"1"},{"client_id":"333577","id":"cost","type":"2","base":"1","name":"金额","group_id":"3","field_type":"5","ext_info":[],"require":"1","edit_required":"0","disable_flag":"0","edit_hide":"0","default":"","edit_default":"0","hint":"","edit_hint":"1","is_editable":0,"is_list":"1","columns":["cost"],"relation_type":"0","relation_field":"","relation_field_type":"0","relation_field_name":"","relation_origin_type":"0","relation_origin_field":"","relation_origin_field_type":"0","order":"1278838577","app_order":"1278838577","enable_flag":"1","readonly":"0","create_time":"2023-04-12 18:39:06","update_time":"2023-04-12 18:39:06","unique_check":"0","unique_prevent":"1","unique_message":"","relation_origin_field_info":[],"value":"","edit_unique":"0","edit_unique_check":"1","edit_unique_prevent":"1","edit_unique_message":"1"},{"client_id":"333577","id":"cost_item_relation_id","type":"2","base":"1","name":"费用","group_id":"3","field_type":"5","ext_info":[],"require":"0","edit_required":"1","disable_flag":"0","edit_hide":"0","default":"","edit_default":"0","hint":"","edit_hint":"1","is_editable":0,"is_list":"1","columns":["cost_item_relation_id"],"relation_type":"0","relation_field":"","relation_field_type":"0","relation_field_name":"","relation_origin_type":"0","relation_origin_field":"","relation_origin_field_type":"0","order":"6","app_order":"6","enable_flag":"1","readonly":"0","create_time":"2023-04-12 18:39:06","update_time":"2023-04-12 18:39:06","unique_check":"0","unique_prevent":"1","unique_message":"","relation_origin_field_info":[],"value":"","edit_unique":"0","edit_unique_check":"1","edit_unique_prevent":"1","edit_unique_message":"1"},{"client_id":"333577","id":"cost_remark","type":"2","base":"1","name":"费用备注","group_id":"3","field_type":"1","ext_info":[],"require":"0","edit_required":"1","disable_flag":"0","edit_hide":"0","default":"","edit_default":"0","hint":"","edit_hint":"1","is_editable":0,"is_list":"1","columns":["cost_remark"],"relation_type":"0","relation_field":"","relation_field_type":"0","relation_field_name":"","relation_origin_type":"0","relation_origin_field":"","relation_origin_field_type":"0","order":"0","app_order":"0","enable_flag":"1","readonly":"0","create_time":"2023-04-12 18:39:06","update_time":"2023-04-12 18:39:06","unique_check":"0","unique_prevent":"1","unique_message":"","relation_origin_field_info":[],"value":"","edit_unique":"0","edit_unique_check":"1","edit_unique_prevent":"1","edit_unique_message":"1"}]},{"id":6,"name":"银行信息","can_add_custom_field":false,"is_hide":false,"fields":{"capital_account_id":"","capital_name":"","capital_bank":"","bank_account":"","capital_account_address":"","capital_account_remark":""},"list":[]}]';
        $data = json_decode($data, true);
        \User::setLoginUserByAccount('<EMAIL>');
        $user = \User::getLoginUser();
        $clientId = $user->getClientId();
        $orderId = **********;
        \common\library\approval_flow\Builder::$enableForApprovalFlow = true;
        \common\library\approval_flow\Builder::$matchOnly = false;
        $order = new \common\library\oms\order\Order($user->getClientId(),$orderId);
        $result = $order->getOperator()->editByGroupInfo($data);
        dd($result);
    }

    /**
     * debugphp -d 'xdebug.client_port=9004' /data/codebase/bob/php-crm/protected/yiic-test zbp TestFieldSettingEdit --functionType=2
     */
    public function actionTestFieldSettingEdit($functionType, $mode = 'update', $deleteFieldId = ''){
        \User::setLoginUserByAccount('<EMAIL>');
        $user = \User::getLoginUser();
        $clientId = $user->getClientId();
        $api = new \common\library\object\field\Api();
        if($mode=='update'){
            switch($functionType){
                case \common\library\object\field\FieldConstant::FUNCTION_TYPE_QUOTE:
                    $fieldInfo = [
                        'object_name' => \common\library\object\object_define\Constant::OBJ_PURCHASE_ORDER,
                        'field' => '**********',
                        'field_name' => '订单备注（引-改为国家）',
                        'group_id' => \common\library\custom_field\CustomFieldService::PURCHASE_ORDER_GROUP_BASIC,
                        'field_type' => \common\library\object\field\FieldConstant::TYPE_TEXT,
                        'array_flag' => 0,
                        'function_type' => \common\library\object\field\FieldConstant::FUNCTION_TYPE_QUOTE,
                        'ext_info' => [],
                        'system_type' => 2,

                        // 对于引用字段而言，这几个字段固定
                        'allow_setting' => 1,
                        'allow_setting_show' => 1,
                        'allow_setting_required' => 0,
                        'allow_setting_default' => 0,
                        'allow_setting_tips' => 0,
                        'is_writable' => 0,
                        'required' => 0,
                        'default_value' => '',
                        'tips' => '',
                        'show_flag' => 1,
                        'field_relations' => [
                            'relation_object_name' => 'objOrder',
                            'relation_object_function_type' => \common\library\object\field\FieldConstant::FUNCTION_TYPE_NORMAL,
                            'relation_field' => 'refer_order_id',
                            'relation_object_field' => 'country',
                            'relation_object_relation_field' => 'order_id'
                        ]
                    ];
                    break;

                case \common\library\object\field\FieldConstant::FUNCTION_TYPE_FORMULA:
                    $fieldInfo = [
                        'object_name' => \common\library\object\object_define\Constant::OBJ_PURCHASE_ORDER,
                        'field' => '3115687443',
                        'field_name' => '采购单占订单金额比例',
                        'group_id' => \common\library\custom_field\CustomFieldService::PURCHASE_ORDER_GROUP_BASIC,
                        'field_type' => \common\library\object\field\FieldConstant::TYPE_PERCENTAGE,
                        'array_flag' => 0,
                        'function_type' => \common\library\object\field\FieldConstant::FUNCTION_TYPE_FORMULA,
                        'ext_info' => [
                            "expression" => "({purchase_order_id.objPurchaseOrder.amount}+{purchase_order_id.objPurchaseOrder.addition_cost_amount})/({refer_order_id.objOrder.amount}+{purchase_order_id.objPurchaseOrder.amount})",//计算公式
                            "empty" => 0,                                        //参与运算的字段值为空如何处理  0, null
                            "remark" => "",                                    //备注
                            "decimal" => 4,                                    //小数保存几位
                            "decimal_logic" => "floor"                //小数位取数逻辑
                        ],
                        'system_type' => 2,

                        // 对于引用字段而言，这几个字段固定
                        'allow_setting' => 1,
                        'allow_setting_show' => 1,
                        'allow_setting_required' => 0,
                        'allow_setting_default' => 0,
                        'allow_setting_tips' => 0,
                        'is_writable' => 0,
                        'required' => 0,
                        'default_value' => 0,
                        'tips' => '',
                        'show_flag' => 1,
                        // 'columns' => ["data_key" => "ext_numeric_5"],
                    ];
                    break;

                case \common\library\object\field\FieldConstant::FUNCTION_TYPE_CALCULATE:
                    $fieldInfo = [
                        'object_name' => \common\library\object\object_define\Constant::OBJ_ORDER,
                        'field' => '1448503525',
                        'field_name' => '平均采购单价(改最大采购单价)',
                        'group_id' => \common\library\custom_field\CustomFieldService::ORDER_GROUP_PRODUCT,
                        'field_type' => \common\library\object\field\FieldConstant::TYPE_NUMBER,
                        'array_flag' => 0,
                        'function_type' => \common\library\object\field\FieldConstant::FUNCTION_TYPE_CALCULATE,
                        'ext_info' => [
                            "expression" => "max({order_id.objPurchaseOrderProduct.unit_price})",//计算公式
                            "empty" => 0,                                        //参与运算的字段值为空如何处理  0, null
                            "remark" => "",                                    //备注
                            "decimal" => 4,                                    //小数保存几位
                            "decimal_logic" => "floor"                //小数位取数逻辑
                        ],
                        'system_type' => 2,

                        // 对于引用字段而言，这几个字段固定
                        'allow_setting' => 1,
                        'allow_setting_show' => 1,
                        'allow_setting_required' => 0,
                        'allow_setting_default' => 0,
                        'allow_setting_tips' => 0,
                        'is_writable' => 0,
                        'required' => 0,
                        'default_value' => 0,
                        'tips' => '',
                        'show_flag' => 1,
                        // 'columns' => ["data_key" => "ext_numeric_3"],
                        'field_relations' => [
                            'relation_object_name' => 'objPurchaseOrderProduct',
                            'relation_object_function_type' => \common\library\object\field\FieldConstant::FUNCTION_TYPE_NORMAL,
                            'relation_field' => 'order_id',
                            'relation_object_field' => 'unit_price',
                            'relation_object_relation_field' => 'order_id'
                        ]
                    ];
                    break;
            }

            // 先查询field_id
            $fieldObj = new \common\library\object\field\Field($clientId);
            $fieldObj->loadField($fieldInfo['object_name'], $fieldInfo['field']);
            if($fieldObj->isNew()){
                throw new \RuntimeException('This is a new field!');
            }

            $api->update($fieldObj->field_id, $fieldInfo);
        }elseif($mode == 'create'){
            $fieldInfo = [
                'object_name' => \common\library\object\object_define\Constant::OBJ_PURCHASE_ORDER,
                'field' => '277546629',
                'field_name' => '采购单随机引用字段',
                'group_id' => \common\library\custom_field\CustomFieldService::PURCHASE_ORDER_GROUP_BASIC,
                'field_type' => \common\library\object\field\FieldConstant::TYPE_TEXT,
                'array_flag' => 0,
                'function_type' => \common\library\object\field\FieldConstant::FUNCTION_TYPE_QUOTE,
                'ext_info' => [],
                'system_type' => 2,
                'allow_setting' => 1,
                'allow_setting_show' => 1,
                'allow_setting_required' => 0,
                'allow_setting_default' => 0,
                'allow_setting_tips' => 0,
                'is_writable' => 0,
                'required' => 0,
                'default_value' => '',
                'tips' => '',
                'show_flag' => 1,
                'field_relations' => [
                    'relation_object_name' => 'objOrder',
                    'relation_object_function_type' => \common\library\object\field\FieldConstant::FUNCTION_TYPE_NORMAL,
                    'relation_field' => 'refer_order_id',
                    'relation_object_field' => 'shipment_deadline_remark',
                    'relation_object_relation_field' => 'order_id'
                ]
            ];
            $api->create($fieldInfo);
        }elseif($mode == 'delete'){
            // 这里传的是 fieldId 而不是 field
            $api->delete($deleteFieldId);
        }
    }

    /**
     * debugphp -d 'xdebug.client_port=9004' /data/codebase/bob/php-crm/protected/yiic-test zbp TestRefreshFields --clientId=333577 --objectName=objOrder --pids=**********,**********
     */
    public function actionTestRefreshFields($clientId, $objectName, $pids){
        $pids = explode(',', $pids);
        $clientObject = \common\library\account\Client::getClient($clientId);
        $user = $clientObject->getMasterUser();
        \User::setLoginUser($user);
        \common\library\object\field\updator\calculator\FieldCalculateTask::triggerByPid($clientId, $objectName, $pids);
    }

    /**
     * debugphp -d 'xdebug.client_port=9004' /data/codebase/bob/php-crm/protected/yiic-test zbp TestSyncCalculator2
     */
    public function actionTestSyncCalculator2(){
        $clientId=14119;
        $mainData = [
            "**********"=> 100,
            "**********"=> "0.00",
            "**********"=> "0.0000",
            "**********"=> "",
            "**********"=> "",
            "**********"=> "0.0000",
            "product_total_amount"=> 0,
            "product_total_amount_usd"=> "",
            "addition_cost_amount"=> 0,
            "amount"=> 0,
            "amount_rmb"=> "",
            "order_gross_margin"=> "0.0000",
            "currency"=> "CNY",
            "exchange_rate"=> 100,
            "exchange_rate_usd"=> 13.9675,
            "company_id"=> "",
            "opportunity_id"=> ""
        ];
        $subObjectData = [
        ];

        $syncCalculator = new \common\library\object\field\updator\calculator\SyncCalculator($clientId, 'objOrder');
        $syncCalculator->setMainData($mainData);    // 设置主对象数据
        $syncCalculator->setSubData('objOrderProduct', $subObjectData);  // 设置从对象数据，如果没有从对象，可以不设置
        $syncCalculator->calculate();       // 计算字段

        $orderBizRow = $syncCalculator->getMainData();     // 获取计算后的主对象数据，覆盖到原有的 orderRow
        $orderRecordBizRows = $syncCalculator->getSubData('objOrderProduct');

        echo json_encode($orderBizRow);
        echo PHP_EOL;
        echo json_encode($orderRecordBizRows);
        echo PHP_EOL;

//        \User::setLoginUserByAccount('<EMAIL>');
//        $user = \User::getLoginUser();
//        $clientId = $user->getClientId();
//        $orderId = *********1;
//        $order = new \common\library\oms\order\Order($clientId, $orderId);
//        $order->calculateFunctionalFields();
////        $order->update();
//        dd($order->getAttributes());
    }

    /**
     * 测试 Filter 的join的 on 能力
     * debugphp -d 'xdebug.client_port=9004' /data/codebase/bob/php-crm/protected/yiic-test zbp TestFilterJoin
     */
    public function actionTestFilterJoin(){
        $clientId = 333577;
        $orderFilter = new \common\library\oms\order\OrderFilter($clientId);
        $orderFilter->enable_flag = 1;
        $orderFilter->order_id = [
            **********,
            **********
        ];
        $batch = $orderFilter->find();
        $batch->getFormatter()->displayFields(array_merge($orderFilter->getMetadata()->columnFields(), \common\library\object\field\Helper::getExtendField(new \common\library\object\extend\ExtendMetadata())));
        $result = $batch->getAttributes();

        echo json_encode($result);
    }

    /**
     * 测试 Filter 的join的 on 能力
     * debugphp -d 'xdebug.client_port=9004' /data/codebase/bob/php-crm/protected/yiic-test zbp TestFormatHistory
     */
    public function actionTestFormatHistory($clientId, $orderId){
        try {
            $clientObject = \common\library\account\Client::getClient($clientId);
            $user = $clientObject->getMasterUser();
            \User::setLoginUser($user);
            [$setting,$subSetting] = \common\library\history\SettingFactory::create(\Constants::TYPE_ORDER);

            $setting->init();
            $filter = new \common\library\history\base\HistoryFilter($clientId);
            $filter->setSetting(new \common\library\history\order\OrderSetting());
            $filter->setBizId($orderId);
            $filter->order('create_time', 'desc');
            if($primaryKey = $filter->getMetadata()::objectIdKey()){
                $filter->order($primaryKey, 'desc');
            }
            $historyLists = $filter->rawData();

            $fieldFilter = new FieldFilter($clientId);
            $fieldFilter->object_name = [\common\library\object\object_define\Constant::OBJ_ORDER, \common\library\object\object_define\Constant::OBJ_ORDER_PRODUCT];
            $fieldFilter->enable_flag = \Constants::ENABLE_FLAG_TRUE;
            $fieldFilter->function_type = FieldConstant::CALCULATE_FUNCTION_FIELD_TYPE;
            $bizFieldList = $fieldFilter->rawData();

            foreach($bizFieldList as $bizField){
                if(!isset($bizFieldMap[$bizField['object_name']])){
                    $bizFieldMap[$bizField['object_name']] = [];
                }

                $bizFieldMap[$bizField['object_name']][$bizField['field']] = $bizField;
                if(in_array($bizField['function_type'], FieldConstant::CALCULATE_FUNCTION_FIELD_TYPE)){
                    $functionFieldMap[$bizField['field']] = $bizField;
                }
            }

            $pgUpdateFlatRows=[
                \common\library\object\object_define\Constant::OBJ_ORDER => [],
                \common\library\object\object_define\Constant::OBJ_ORDER_PRODUCT => []
            ];       // 第一维key是对象名称，第二维是行数据（混杂new和old的行），第三维是字段的kv
            $displayFunctionFields = [
                \common\library\object\object_define\Constant::OBJ_ORDER => [],
                \common\library\object\object_define\Constant::OBJ_ORDER_PRODUCT => []
            ];
            foreach ( $historyLists as $elem ) {
                // 判断 $elem 的变更字段中是否包含功能字段
                if($elem['order_product_id'] == 0){
                    $objectNameKey = \common\library\object\object_define\Constant::OBJ_ORDER;
                    $historyPidKey = 'order_id';
                }else{
                    $objectNameKey = \common\library\object\object_define\Constant::OBJ_ORDER_PRODUCT;
                    $historyPidKey = 'order_product_id';
                }
                $pidKey = \common\library\object\object_define\Constant::OBJ_PRIMARY_KEY_MAP[$objectNameKey];
                $uniqueKeyPrefix = md5($elem['order_id'].json_encode($elem['diff']));
                $newUniqueKey = $uniqueKeyPrefix.'_new';
                $oldUniqueKey = $uniqueKeyPrefix.'_old';
                $functionFieldNewLine = $functionFieldOldLine = [];

                foreach($elem['diff'] as $diff){
                    $fieldId = $diff['id'];
                    if(isset($functionFieldMap[$fieldId])){
                        $functionFieldNewLine[$fieldId] = $diff['new'];
                        $functionFieldOldLine[$fieldId] = $diff['old'];
                        $displayFunctionFields[$objectNameKey][]=$fieldId;
                    }
                }

                if(!empty($functionFieldNewLine)){
                    // 将主键id塞到line里
                    $functionFieldOldLine[$pidKey] = $functionFieldNewLine[$pidKey] = $elem[$historyPidKey];
                    $pgUpdateFlatRows[$objectNameKey][$newUniqueKey] = $functionFieldNewLine;
                    $pgUpdateFlatRows[$objectNameKey][$oldUniqueKey] = $functionFieldOldLine;
                }
            }

            // 格式化数据
            foreach($pgUpdateFlatRows as $objName => $objData){
                if(empty($objData)){
                    continue;
                }
                $formatTask = new \common\library\orm\pipeline\formatter\FieldV2FormatTask($clientId);
                $formatTask->setObjName($objName);
                $formatTask->setDisplayFields($displayFunctionFields[$objName]);
                $formatTask->setIsBizData(true);
                $formatTask->prepare($objData);
                foreach ($objData as $key => $datum){
                    $formatTask->setReferenceData($datum);
                    $formatTask->run($datum);
                    $objData[$key] = $datum;
                }

                echo json_encode($objData);
                echo PHP_EOL.PHP_EOL;
            }
        }catch (\Throwable $e){
            echo $e->getMessage().' | '.$e->getFile(). ' | '.$e->getLine();
            echo PHP_EOL;
            echo $e->getTraceAsString();
            echo PHP_EOL;
        }

    }

    /**
     * debugphp -d 'xdebug.client_port=9004' /data/codebase/bob/php-crm/protected/yiic-test zbp TestSortObjects
     */
    public function actionTestSortObjects(){
        echo json_encode(\common\library\object\object_relation\Helper::getSortObjects());
    }

    /**
     * debugphp -d 'xdebug.client_port=9004' /data/codebase/bob/php-crm/protected/yiic-test zbp TestRefreshFunctionFieldById --clientId=334110 --objName=objOrder --pids=3483686838
     */
    public function actionTestRefreshFunctionFieldById($clientId, $objName, $pids){
        $pids = explode(',', $pids);
        $functionFieldTool = new FieldRefreshTool($clientId);
        $functionFieldTool->refreshById($objName, $pids);
    }

    public function actionTestObjRelation(){
        $relations = \common\library\object\object_relation\Helper::getDownstreamObjRelations('objOrder', [ObjectRelationConstant::LOOKUP_SINGLE, ObjectRelationConstant::MASTER_DETAIL]);

        var_dump($relations);
    }

    /**
     * debugphp -d 'xdebug.client_port=9004' /data/codebase/bob/php-crm/protected/yiic-test zbp TestJoin
     */
    public function actionTestJoin(){
        $clientId = 334110;
        $orderFilter = new \common\library\oms\order\OrderFilter($clientId);
        $orderField = '3478680335';
//        $opportunityFilterField = '3478680706';
        $orderFilter->$orderField = '1111';
        $opportunityFilter = new \common\library\opportunity\orm\OpportunityFilter($clientId);
        $opportunityFilter->enable_flag = 1;
        $opportunityFilter->select(['opportunity_id', 'user_id'=>'opportunity_user_id']);

        $filter = $orderFilter->initJoin()->innerJoin($opportunityFilter)->on($orderFilter->getTableName(), 'opportunity_id', $opportunityFilter->getTableName(),'opportunity_id');
//        $orderFilter->select([
//            'order_id',
//            'order_no' =>'my_order_no',
//            'enable_flag' => 'my_enable_flag',
//        ]);
        $filter->limit(1);
        $result = $filter->rawData();
        echo json_encode($result);
    }

    /**
     * debugphp -d 'xdebug.client_port=9004' /data/codebase/bob/php-crm/protected/yiic-test zbp TestStreamConsumer
     */
    public function actionTestStreamConsumer(){
        $redis = \common\library\server\binlog_to_biz\RedisStream::instance();
        $i = 1;
        $redis->executeRaw(['xgroup', 'create', \common\library\server\binlog_to_biz\MessageConsumer::STREAM_NAME.'_test',\common\library\server\binlog_to_biz\MessageConsumer::STREAM_GROUP_NAME,'0-0']);

        $groupName = \common\library\server\binlog_to_biz\MessageConsumer::STREAM_GROUP_NAME;
        $streamName = \common\library\server\binlog_to_biz\MessageConsumer::STREAM_NAME.'_test';

        $startTime = microtime(true);
        while(true){
            // 每隔 0.2 秒生产一条数据
            $message = $redis->executeRaw(['xreadgroup', 'group', $groupName, 'consumer_zbp', 'count', 1, 'BLOCK','5000', 'streams', $streamName, '>']);

            if(empty($message)){
                echo microtime(true) - $startTime - 5;
                echo PHP_EOL;
                break;
            }
            $messageIdList = [];
            $messageList = $message[0][1];
            foreach ($messageList as $message) {
                //解包赋值
                $messageId = $message[0];
                $messageIdList[] = $messageId;
            }

            $redis->executeRaw(['xack', $streamName, $groupName, ...$messageIdList]);
            $redis->executeRaw(['xdel', $streamName, ...$messageIdList]);
            usleep(1000);      // 0.01秒
//            echo json_encode($message);
//            echo PHP_EOL;
            echo $i++;
            echo PHP_EOL;
        }
    }

    /**
     * debugphp -d 'xdebug.client_port=9004' /data/codebase/bob/php-crm/protected/yiic-test zbp TestStreamConsumer2
     */
    public function actionTestStreamConsumer2(){
        $redis = \common\library\server\binlog_to_biz\RedisStream::instance();
        $i = 1;
        $redis->executeRaw(['xgroup', 'create', \common\library\server\binlog_to_biz\MessageConsumer::STREAM_NAME.'_test',\common\library\server\binlog_to_biz\MessageConsumer::STREAM_GROUP_NAME,'0-0']);

        $groupName = \common\library\server\binlog_to_biz\MessageConsumer::STREAM_GROUP_NAME;
        $streamName = \common\library\server\binlog_to_biz\MessageConsumer::STREAM_NAME.'_test';

        $startTime = microtime(true);
        while(true){
            // 每隔 0.2 秒生产一条数据
            $message = $redis->executeRaw(['xreadgroup', 'group', $groupName, 'consumer_zbp2', 'count', 1, 'BLOCK','5000', 'streams', $streamName, '>']);

            if(empty($message)){
                echo microtime(true) - $startTime - 5;
                echo PHP_EOL;
                break;
            }
            $messageIdList = [];
            $messageList = $message[0][1];
            foreach ($messageList as $message) {
                //解包赋值
                $messageId = $message[0];
                $messageIdList[] = $messageId;
            }

            $redis->executeRaw(['xack', $streamName, $groupName, ...$messageIdList]);
            $redis->executeRaw(['xdel', $streamName, ...$messageIdList]);
            usleep(1000);      // 0.01秒
//            echo json_encode($message);
//            echo PHP_EOL;
            echo $i++;
            echo PHP_EOL;
        }
    }

    /**
     * debugphp -d 'xdebug.client_port=9004' /data/codebase/bob/php-crm/protected/yiic-test zbp TestStreamProducer
     */
    public function actionTestStreamProducer(){
        $redis = \common\library\server\binlog_to_biz\RedisStream::instance();
        $i = 1;
        while(true){
            // 每隔 0.05 秒生产一条数据
            $redis->executeRaw(['xadd',\common\library\server\binlog_to_biz\MessageConsumer::STREAM_NAME.'_test', 'MAXLEN', '=', '20000', '*', 'name', 'zbp'.rand(1,99999)]);
            usleep(500);
            echo $i++;
            echo PHP_EOL;
            if($i >= 2000){
                break;
            }
        }
    }

    /**
     * debugphp -d 'xdebug.client_port=9004' /data/codebase/bob/php-crm/protected/yiic-test zbp ClearPendingMsg
     */
    public function actionClearPendingMsg($periodSec=0){
        // xpending zbpstream zbpgroup IDLE  60000 - + 1000
        $redis = \common\library\server\binlog_to_biz\RedisStream::instance();
        $periodMsec = $periodSec * 1000;
        $batchNum = 500;
        $groupName = \common\library\server\binlog_to_biz\MessageConsumer::STREAM_GROUP_NAME;
        $streamName = \common\library\server\binlog_to_biz\MessageConsumer::STREAM_NAME.'_test';

        while(true){
            // idle表示超过了多少时间没有被确认，单位毫秒，这里每次读1000条
            if($periodMsec == 0){   // 不知道是不是redis版本比较老旧的问题，我们的redis不支持按照时间范围获取未确认的消息
                $pendingMsg = $redis->executeRaw(['xpending',$streamName, $groupName, '-', '+', $batchNum]);
            }else{
                $pendingMsg = $redis->executeRaw(['xpending',$streamName, $groupName, 'idle', $periodMsec, '-', '+', $batchNum]);
            }

            if(empty($pendingMsg)){
                break;
            }

            $pendingMsgIds = array_column($pendingMsg, 0);

            if(!empty($pendingMsgIds)){
                // 确认消息（消息从消费者的PEL列表移除）
                $ackResult = $redis->executeRaw(['xack', $streamName, $groupName, ...$pendingMsgIds]);

                // 从 stream 中移除指定的消息id
                $delResult = $redis->executeRaw(['xdel', $streamName, ...$pendingMsgIds]);
            }

            if(count($pendingMsg) < $batchNum){
                break;
            }
        }

        echo "清理pending数据完毕".PHP_EOL;
    }

    /**
     * debugphp -d 'xdebug.client_port=9004' /data/codebase/bob/php-crm/protected/yiic-test zbp PgRedisInfo
     */
    public function actionPgRedisInfo()
    {
        $redis = \common\library\server\binlog_to_biz\RedisStream::instance();
        $streamName = \common\library\server\binlog_to_biz\MessageConsumer::STREAM_NAME.'_test';
        $groupName = \common\library\server\binlog_to_biz\MessageConsumer::STREAM_GROUP_NAME;
        $lenOfStream = $redis->executeRaw(['xlen',$streamName]);
        $groupInfos = $redis->executeRaw(['xinfo','consumers', $streamName, $groupName]);
        $pendingInfos = $redis->executeRaw(['xpending',$streamName, $groupName, '-','+', 100]);
        $table = new Table(new ConsoleOutput());
        $table->setHeaderTitle('stream info')
            ->addRow(['length',$lenOfStream])
            ->render();

        $table = new Table(new ConsoleOutput());
        $table->setHeaderTitle('group info')
            ->setHeaders(['Serial_id','name','pending','idle']);
        foreach ($groupInfos as $index=>$groupInfo) {
            $table->addRow([$index,$groupInfo[1],$groupInfo[3],$groupInfo[5]]);
        }
        $table->render();

        $table = new Table(new ConsoleOutput());
        $table->setHeaderTitle('pending info')
            ->setHeaders(['serial_id','id','consumer_name','idle-time','delivered-times']);
        foreach ($pendingInfos as $index=>$pendingInfo) {
            $table->addRow([$index,$pendingInfo[0],$pendingInfo[1],$pendingInfo[2],$pendingInfo[3]]);
        }
        $table->render();
    }

    /**
     * debugphp -d 'xdebug.client_port=9004' /data/codebase/bob/php-crm/protected/yiic-test zbp InitField
     */
    public function actionInitField($clientId){
        $clientObject = \common\library\account\Client::getClient($clientId);
        $user = $clientObject->getMasterUser();
        \User::setLoginUser($user);
        CustomerOptionService::iniClient($user, \common\library\privilege_v3\PrivilegeConstants::OKKI_PRO_SYSTEM_ID);
//        CustomerOptionService::createDefaultCustomField($clientId, \common\library\privilege_v3\PrivilegeConstants::OKKI_PRO_SYSTEM_ID);
        echo 123;
    }

    // 临时脚本：修复用户订单金额
    public function actionFixOrderAmount($clientId = null, $grey = 0, $greyNum = null, $updateTime='2024-03-26 00:00:00'){
        self::info("actionFixOrderAmount start" . PHP_EOL);
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey && !is_null($greyNum)) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        $map = [];
        foreach ($clientIds as $clientId) {
            try {
                $orderFilter = new \common\library\oms\order\OrderFilter($clientId);
                $orderFilter->update_time = new \xiaoman\orm\database\data\GT($updateTime);
                $orderFilter->enable_flag = 1;
                $orderData = $orderFilter->rawData();

                $orderIds = array_column($orderData, 'order_id');

                $orderProductFilter = new \common\library\oms\order_product\OrderProductFilter($clientId);
                $orderProductFilter->enable_flag = 1;
                $orderProductFilter->refer_id = $orderIds;
                $orderProductFilter->combine_record_id = 0;
                $recordData = $orderProductFilter->rawData();

                $recordDataGroup = [];
                foreach($recordData as $recordDatum){
                    if(!isset($recordDataGroup[$recordDatum['refer_id']])){
                        $recordDataGroup[$recordDatum['refer_id']] = [];
                    }
                    $recordDataGroup[$recordDatum['refer_id']][] = $recordDatum;
                }

                $db = \PgActiveRecord::getDbByClientId($clientId);
                $wrongNum = 0;
                foreach($orderData as $datum){
                    $updateSql = 'update tbl_order set ';
                    if(empty($recordDataGroup[$datum['order_id']])){
                        continue;
                    }

                    $updateFlag = false;
                    // 真实的产品总金额
                    $productTotalAmount = array_sum(array_column($recordDataGroup[$datum['order_id']], 'cost_amount'));
                    if($datum['amount'] != $productTotalAmount + $datum['addition_cost_amount']){
                        $amount = $productTotalAmount + $datum['addition_cost_amount'];
                        $amount_rmb = round($amount * $datum['exchange_rate'] / 100, 5);
                        $amount_usd = round($amount * $datum['exchange_rate_usd'] / 100, 5);

                        $updateSql .= " amount = {$amount}, amount_rmb = {$amount_rmb}, amount_usd = {$amount_usd} ";
                        $updateFlag = true;
                    }

                    if($datum['product_total_amount'] != $productTotalAmount){
                        $productTotalAmountRmb = round($productTotalAmount * $datum['exchange_rate']/100, 5);
                        $productTotalAmountUsd = round($productTotalAmount * $datum['exchange_rate_usd']/100, 5);

                        if($updateSql){
                            $updateSql .= ', ';
                        }
                        $updateSql .= " product_total_amount = {$productTotalAmount}, product_total_amount_rmb = {$productTotalAmountRmb}, product_total_amount_usd = {$productTotalAmountUsd} ";
                        $updateFlag = true;
                    }

                    if($updateFlag){
                        $wrongNum++;
                        $updateSql .= ' where client_id='.$clientId.' and order_id='.$datum['order_id'];

                        echo $updateSql;
                        echo PHP_EOL;

                        // echo $db->createCommand($updateSql)->execute();
                        echo PHP_EOL;
                    }

                }
                if($wrongNum > 0){
                    $map[$clientId] = $wrongNum;
                }
                \PgActiveRecord::releaseDbByClientId($clientId);
            } catch (\Throwable $e) {
                self::info("FixOrderProductListExternalField for client_id:【{$clientId}】 failed, error on File:" . $e->getFile() . " | Line : " . $e->getLine() . " | Msg : " . $e->getMessage() . PHP_EOL);
                continue;
            }
        }

        echo json_encode($map);
    }

    // debugphp -d 'xdebug.client_port=9004' /data/codebase/bob/php-crm/protected/yiic-test zbp --taskClass=FindErrorField
    public function actionFindErrorField($clientId = null, $grey = 0, $greyNum = null){
        $moduleTypesMap = [
            \Constants::TYPE_COMPANY => [Constant::OBJ_COMPANY],
            \Constants::TYPE_CUSTOMER => [Constant::OBJ_CUSTOMER],
            \Constants::TYPE_OPPORTUNITY => [Constant::OBJ_OPPORTUNITY],
            \Constants::TYPE_PRODUCT => [Constant::OBJ_PRODUCT, Constant::OBJ_PRODUCT_SKU],
            \Constants::TYPE_ORDER => [Constant::OBJ_ORDER, Constant::OBJ_ORDER_PRODUCT],
            \Constants::TYPE_PURCHASE_ORDER => [Constant::OBJ_PURCHASE_ORDER, Constant::OBJ_PURCHASE_ORDER_PRODUCT, ],
            \Constants::TYPE_SUPPLIER => [Constant::OBJ_SUPPLIER, Constant::OBJ_SUPPLIER_CONTACT,Constant::OBJ_SUPPLIER_ACCOUNT ],
        ];

        self::info("actionFindErrorField start" . PHP_EOL);
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey && !is_null($greyNum)) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        $map = [];
        foreach ($clientIds as $clientId) {
            try{
                self::info("clientId:{$clientId} start" . PHP_EOL);
                $sql = "select id,name,type from tbl_custom_field where client_id={$clientId} and type in (".implode(',', array_keys($moduleTypesMap)).") and base = 0 and enable_flag=1";

                $newModuleTypes = [];
                $moduleTypeFlipMap = [];
                foreach($moduleTypesMap as $cusModule => $modules){
                    $newModuleTypes = array_merge($newModuleTypes, $modules);
                    foreach($modules as $module){
                        $moduleTypeFlipMap[$module] = $cusModule;
                    }
                }
                $fieldSql = "select * from tbl_field where client_id={$clientId} and object_name in ('".implode("','", $newModuleTypes)."') and system_type=2 and enable_flag=1";

                $mysqlDb = \ProjectActiveRecord::getDbByClientId($clientId);
                $customFieldInfos = $mysqlDb->createCommand($sql)->queryAll();

                $pgDb = \PgActiveRecord::getDbByClientId($clientId);
                $fieldInfos = $pgDb->createCommand($fieldSql)->queryAll();

                $customFieldMap = [];
                foreach($customFieldInfos as $customFieldInfo){
                    if(!isset($customFieldMap[$customFieldInfo['type']])){
                        $customFieldMap[$customFieldInfo['type']] = [];
                    }
                    $customFieldMap[$customFieldInfo['type']][$customFieldInfo['id']] = [];
                }

                $api = new \common\library\object\field\Api();
                foreach($fieldInfos as $fieldInfo){
                    if(!isset($customFieldMap[$moduleTypeFlipMap[$fieldInfo['object_name']]])){
                        continue;
                    }

                    $customFields = $customFieldMap[$moduleTypeFlipMap[$fieldInfo['object_name']]];
                    if(!isset($customFields[$fieldInfo['field']])){
                        $map[$clientId] = true;
                        echo $fieldInfo['field_name'];
                        echo PHP_EOL;
    //                $api->delete($fieldInfo['field_id']);
                    }
                }
                \PgActiveRecord::releaseDbByClientId($clientId);
                \ProjectActiveRecord::releaseDbByClientId($clientId);
            }catch (\Throwable $e) {
                self::info("FixOrderProductListExternalField for client_id:【{$clientId}】 failed, error on File:" . $e->getFile() . " | Line : " . $e->getLine() . " | Msg : " . $e->getMessage() . PHP_EOL);
                continue;
            }
        }
        self::info("wrong field client:".json_encode($map));
    }


    /**
     * 为青岛双普跑成本单价
     * https://www.tapd.cn/21404721/prong/stories/view/1121404721001073319
     * debugphp -d 'xdebug.client_port=9004' /data/codebase/bob/php-crm/protected/yiic-test zbp ImportCostUnitPrice --clientId=14119
     */
    public function actionImportCostUnitPrice($clientId,$fp)
    {
//        $fp = '/tmp/cost_unit_price_test.xlsx';
        $excelColumn = array_flip([
            'sku_code',     // 产品编号（全是无规格的）
            'product_name',     // 产品名称
            'model',    // 产品型号
            'enable_count',  // 可用库存
            'real_count',     // 实际库存
            'warehouse_name',  // 仓库
            'cost_unit_price_rmb',   // 成本单价（CNY）
            'cost_unit_price_usd',   // 成本单价(USD)
        ]);

        $excelReader = new ExcelReader($fp);
        $excelData = $excelReader->getData();
        array_shift($excelData);

        // 用户登录
        $clientObject = \common\library\account\Client::getClient($clientId);
        $user = $clientObject->getMasterUser();
        \User::setLoginUser($user);

        // 查询仓库id和产品id
        $uniqueSkuCode = array_unique(array_column($excelData, $excelColumn['sku_code']));
        $skuFilter = new ProductSkuFilter($clientId);
        $skuFilter->sku_code = $uniqueSkuCode;
        $skuFilter->enable_flag = 1;
        $skuFilter->select(['sku_id', 'sku_code']);
        $skuCodeMap = array_column($skuFilter->rawData(), 'sku_id', 'sku_code');

        // 统计没找到的产品编号
        $missSkuCode = array_diff($uniqueSkuCode, array_keys($skuCodeMap));

        if(!empty($missSkuCode)){
            self::info("存在找不到的产品编号：".json_encode($missSkuCode));
            die;
        }

        $uniqueWarehouseName = array_unique(array_column($excelData, $excelColumn['warehouse_name']));
        $warehouseFilter = new WarehouseFilter($clientId);
        $warehouseFilter->client_id = $clientId;
        $warehouseFilter->name = $uniqueWarehouseName;
        $warehouseFilter->enable_flag = 1;
        $warehouseFilter->select(['warehouse_id', 'name']);
        $warehouseMap = array_column($warehouseFilter->rawData(), 'warehouse_id', 'name');

        // 统计没找到的产品编号
        $missWarehouseName = array_diff($uniqueWarehouseName, array_keys($warehouseMap));
        if(!empty($missWarehouseName)){
            self::info("存在找不到的产品编号：".json_encode($missSkuCode));
            die;
        }

        // 查询指定产品在所有仓库的成本单价
        $inventoryFilter = new ProductInventoryFilter($clientId);
        $inventoryFilter->client_id = $clientId;
        $inventoryFilter->sku_id = array_values($skuCodeMap);
        $inventoryFilter->select(['sku_id', 'warehouse_id', 'cost_unit_price_rmb', 'cost_unit_price_usd']);
        $inventoryData = $inventoryFilter->rawData();
        file_put_contents('/tmp/zbp_inventory.json', json_encode($inventoryData));

        // 查询指定产品在销售出库单
        $outboundRecordFilter = new \common\library\oms\outbound_invoice\sale\record\SaleOutboundRecordFilter($clientId);
        $outboundRecordFilter->type = \Constants::TYPE_SALE_OUTBOUND_INVOICE;
        $outboundRecordFilter->delete_flag = 0;
        $outboundRecordFilter->sku_id = array_values($skuCodeMap);
        $outboundRecordFilter->select(['outbound_invoice_id','outbound_record_id','cost_unit_price_rmb','cost_unit_price_usd']);
        $outboundRecord = $outboundRecordFilter->rawData();
        file_put_contents('/tmp/zbp_outbound_cost_unit_price.json', json_encode($outboundRecord));

        // 开始修改
        $batchCount = 1000;
        $batchChunk = array_chunk($excelData, $batchCount);
        $db = \PgActiveRecord::getDbByClientId($clientId);
        $recordAffectRows = $inventoryAffectRows = $invoiceAffectRows = 0;
        foreach($batchChunk as $chunkKey => $chunk){
            $recordSql = 'update tbl_outbound_record set ';
            $inventorySql = 'update tbl_product_inventory set ';
            $rmbSql = ' cost_unit_price_rmb = case ';
            $usdSql = ' cost_unit_price_usd = case ';
            $targetSkuIds = [];

            foreach($chunk as $key => $datum){
                $rowNum = $chunkKey * $batchCount + $key + 2;
                $hasError = false;

                if(!isset($skuCodeMap[$datum[$excelColumn['sku_code']]])){
                    self::info("找不到产品编号对应的skuId，行数：【".$rowNum."】，产品编号：{$datum[$excelColumn['sku_code']]}");
                    $hasError = true;
                }

                if(!isset($warehouseMap[$datum[$excelColumn['warehouse_name']]])){
                    self::info("找不到仓库名称对应的warehouseId，行数：【".$rowNum."】，仓库名称：{$datum[$excelColumn['warehouse_name']]}");
                    $hasError = true;
                }

                if($hasError){
                    continue;
                }
                $skuId = $skuCodeMap[$datum[$excelColumn['sku_code']]];
                $warehouseId = $warehouseMap[$datum[$excelColumn['warehouse_name']]];
                $costUnitPriceRmb = is_numeric($datum[$excelColumn['cost_unit_price_rmb']]) ? round($datum[$excelColumn['cost_unit_price_rmb']], 4) : 0 ;
                $costUnitPriceUsd = is_numeric($datum[$excelColumn['cost_unit_price_usd']]) ? round($datum[$excelColumn['cost_unit_price_usd']], 4) : 0 ;

                $rmbSql .= " when sku_id={$skuId} and warehouse_id={$warehouseId} then {$costUnitPriceRmb} ";
                $usdSql .= " when sku_id={$skuId} and warehouse_id={$warehouseId} then {$costUnitPriceUsd} ";
                $targetSkuIds[] = $skuId;
            }

            if(!empty($targetSkuIds)){
                $targetSkuIds = implode(',', array_unique($targetSkuIds));
                $recordSql .= $rmbSql.' else cost_unit_price_rmb end,'.$usdSql." else cost_unit_price_usd end where client_id={$clientId} and sku_id in ({$targetSkuIds}) and warehouse_id in (". implode(',', array_values($warehouseMap)).") and delete_flag=0";
                $inventorySql .= $rmbSql.' else cost_unit_price_rmb end,'.$usdSql." else cost_unit_price_usd end where client_id={$clientId} and sku_id in ({$targetSkuIds}) and warehouse_id in (". implode(',', array_values($warehouseMap)).")";

                self::info("recordSql:".$recordSql);
                echo PHP_EOL;
                self::info("inventorySql:".$inventorySql);
                echo PHP_EOL;
                $recordAffectRows += $db->createCommand($recordSql)->execute();
                $inventoryAffectRows += $db->createCommand($inventorySql)->execute();
            }
        }

        // 最后变更单据的出库来源
        $uniqueInvoiceIdsChunk = array_chunk(array_unique(array_column($outboundRecord, 'outbound_invoice_id')), $batchCount);

        foreach($uniqueInvoiceIdsChunk as $invoiceIds){
            $targetInvoiceIds = implode(',', $invoiceIds);
            $invoiceSql = "update tbl_outbound_invoice set source_type=2 where client_id = {$clientId} and outbound_invoice_id in ({$targetInvoiceIds})";
            self::info("invoiceSql:".$invoiceSql);
            echo PHP_EOL;
            $invoiceAffectRows += $db->createCommand($invoiceSql)->execute();
        }

        self::info("结束, 库存影响行数：{$inventoryAffectRows}, 出库明细影响行数：{$recordAffectRows}, 出库单影响行数：{$invoiceAffectRows}");
    }


    /** 找出有哪些用户在订单使用了上游的指定字段作为关联字段，以及订单中的这些字段是否符合指定的数据类型
     * @param $clientId
     * @param $grey
     * @param $greyNum
     * @return void
     * debugphp -d 'xdebug.client_port=9004' /data/codebase/bob/php-crm/protected/yiic-test zbp FindOrderRelationFields --clientId=14119
     */
    public function actionFindOrderRelationFields($clientId = null, $grey = 0, $greyNum = null){
        self::info("FindOrderRelationFields start" . PHP_EOL);
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey && !is_null($greyNum)) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        $output = '/tmp/zbp_order_relation_fields.txt';
        if(file_exists($output)){
            unlink($output);
        }
        $targetClientNum = 0;
        foreach ($clientIds as $clientId) {
            $map = [];
            try {
                $fieldRelationFilter = new \common\library\object\field_relation\FieldRelationFilter($clientId);
                $fieldRelationFilter->enable_flag = 1;
                $fieldRelationFilter->object_name = 'objOrder';
                $fieldRelationFilter->function_type = 1;

                /** 目前可能存在问题的关联字段为
                 * 客户：annual_procurement 年采购额（多选）、intention_level 采购意向（多选）、product_group_ids 产品分组（多选）、star 星级（整型）、cus_tag 客户标签（多选）、origin_list 客户来源（多选）、category_ids 主营产品（多选）
                 * 商机：origin_list 商机来源（多选）
                 * 联系人：无（没有多选的字段）
                 * 产品和产品规格：产品类目（多选） 暂不考虑明细的关联字段
                 */
                $fieldRelationFilter->rawWhere(" and ((relation_object_name='objCompany' and relation_object_field in ('annual_procurement','intention_level','product_group_ids','star','cus_tag','origin_list','category_ids')) or (relation_object_name='objOpportunity' and relation_object_field in ('origin_list')))");
                $fieldRelationFilter->select(['field','relation_object_name','relation_object_field']);
                $fieldRelations = $fieldRelationFilter->rawData();

                if(empty($fieldRelations)){
                    continue;
                }

                $targetClientNum++;

                foreach($fieldRelations as $fieldRelation){
                    $map[] = $fieldRelation;
                }

                $cnt = 'clientId:'.$clientId.' | '.json_encode($map)."\n\n";
                file_put_contents($output, $cnt, FILE_APPEND);
                self::info("clientId:{$clientId}存在目标关联字段");
                \PgActiveRecord::releaseDbByClientId($clientId);
            } catch (\Throwable $e) {
                self::info("FindOrderRelationFields for client_id:【{$clientId}】 failed, error on File:" . $e->getFile() . " | Line : " . $e->getLine() . " | Msg : " . $e->getMessage() . PHP_EOL);
                continue;
            }
        }

        self::info("共 {$targetClientNum} 个client存在目标关联字段");
    }

    // ./yiic zbp TestCreatePurchase
    public function actionTestCreatePurchase(){
        $data = '{"handler":[3806196],"purchase_date":"2024-05-06 09:58:05","delivery_date":"2024-05-06 09:58:12","is_parts_model":true,"purchase_order_no":"","inbound_status":"","supplier_no":"","supplier_id":20199131722087,"supplier_contact":20199131820896,"currency":"CNY","exchange_rate":100,"exchange_rate_usd":13.8669,"remark":"","refer_order_id":4219993764610,"purchase_type":2,"20005609040928":995000,"20198665472220":0,"20198665362528":0,"20198666301784":1,"20198667784943":0,"20198668725991":10000,"20198668861194":10000,"20198668961399":1392.85,"20198668964369":"CNY","20198669153725":10000,"20198669026379":100,"20198669669285":"2022-10-18 14:56:47","20198670401153":"小 青","20198670581111":"2023-04-27 13:55:32","20198671612401":1392.85,"20198673092895":10000,"20198674217724":0,"20198677159237":0,"20198678620827":10000,"20198679902780":1392.85,"20198680103235":10000,"20198680254600":"sharon103","20198680296162":"小青PI#202210186141","20198681137180":"草稿订单","20198681602878":"2022-10-18","20198682778928":"Dubrava 212, 10040 Zagreb,Croatia","20198684253382":null,"20198682422914":"0385915059283","20198684341294":"COD","20198684494620":null,"20198684572447":null,"20198684710632":null,"20198684815880":null,"20198685959389":"<EMAIL>","20198685695707":"G.F.Hamster d.o.o.","20198686151947":"Nenad333","20198686912248":"已收定金","20198686972691":"sharon103 100%","20198687419679":"eva一级部门 100%","20198687461970":"中国","20198687562319":null,"20198687900309":null,"20198687749904":null,"20198688457036":null,"20198689077735":null,"20198690052566":null,"20198690652968":null,"20198691450140":"仅支持 支付宝或者微信 支付。","20198691574603":null,"20198691586830":null,"20198692126711":null,"20198692645084":null,"20198692903797":null,"20198693483838":null,"20198693635348":0,"20198694325447":null,"20198695371348":null,"20198698459436":null,"20198697154338":null,"20198699328888":null,"20198699897173":null,"20198700759382":null,"20198702974812":null,"20198704084911":null,"20198704240090":null,"20198705124572":null,"20198706543721":null,"20198706584791":null,"20198707855586":null,"20198707857509":null,"20198708344478":null,"20198709158531":null,"20198710014513":null,"20198710864715":null,"20198711033291":null,"20198712690922":null,"20198713470840":null,"20198715519607":null,"20198716082545":null,"20198716581647":null,"20198716682564":null,"20198717194121":null,"20198719813224":null,"20198723361578":null,"20198724082549":null,"20198724283157":null,"20198724717812":null,"20198727617287":null,"20198730554365":null,"20198731830206":null,"20198739653406":"无","product_total_count":100,"gradientPriceMap":{},"20198670401153_info":{"user_id":"2676385","email":"<EMAIL>","nickname":"小 青","avatar":"https://v4client.oss-cn-hangzhou.aliyuncs.com/other/img/2676385/SUdETHB2TWU0R0RncllMMk5lK1VMZS9UTEh3aTMyZE90RFVndmhlZ3JnRT0=.jpg","ames_email":""},"20198680254600_info":[{"user_id":"3806196","email":"<EMAIL>","nickname":"sharon103","avatar":"https://ximfiles.oss-cn-hangzhou.aliyuncs.com/image_d8e1010d-48dc-4ac5-8464-908798d5d637","ames_email":""}],"amount":5000,"addition_cost_amount":0,"product_total_amount":5000,"product_list":[{"is_sub_product":0,"combine_product_id":0,"combine_product_no":"","purchase_order_product_id":"","product_id":87611787,"product_no":"52","product_image":{"file_id":"87611785","file_name":"3_fef1ca687587f62e66b0442890edfbd1.jpg","file_size":"30344","mime_type":"image/jpeg","file_key":"******************************.jpg","file_path":"https://v4client.oss-cn-hangzhou.aliyuncs.com/******************************.jpg","file_preview_url":"https://v4client.oss-cn-hangzhou.aliyuncs.com/******************************.jpg?response-content-disposition=inline%3B%20filename%3D3_fef1ca687587f62e66b0442890edfbd1.jpg%3B%20filename%2A%3Dutf-8%27%273_fef1ca687587f62e66b0442890edfbd1.jpg&response-content-type=image%2Fjpeg&OSSAccessKeyId=LTAI5tCRVsUWyca4YrKZMPQP&Signature=pjvVgL1B0IzCB6BoN6GcQ3YueEQ%3D&Expires=1715824701"},"product_name":"蓝牙耳机B","product_cn_name":"","product_model":"蓝牙耳机B【jane05】","sku_id":405770534654,"unit":"Box","supplier_product_no":"","supplier_product_name":"","product_remark":"","order_no":"小青PI#202210186141","order_id":4219993764610,"transfer_invoice_serial_id":"","transfer_invoice_id":"","transfer_invoice_record_id":"","enable_count":0,"count":100,"have_inbound_count":"","wait_inbound_count":"","have_return_count":"","unit_price":50,"cost_amount":5000,"product_total_count":"","invoice_product_id":"4219993764417","task_inbound_count":"","to_inbound_count":"","product_disable_flag":"","ratio":0,"master_id":"","parts_total_count":"","is_master_product":"","product_total_count_no_parts":"","master_group_id":"","20005608654054":10000,"20005608689787":995000,"20198746505809":"https://v4client.oss-cn-hangzhou.aliyuncs.com/******************************.jpg","20198747008577":"蓝牙耳机B","20198748572521":"","20198749054913":"蓝牙耳机B【jane05】","20198749458540":0,"20198750592857":10000,"20198750594468":10000,"20198750656185":1,"20198751061039":10000,"20198751520446":0,"20198752352821":"","20198752407934":"","20198752543064":null,"20198753376760":0,"20198752956811":"","20198753533477":0,"20198754036192":0,"20198754482754":0,"20198755306945":0,"20198756444333":100,"20198756867860":"","20198757865614":"","20198756972259":"","20198758298573":"","20198758687555":"额测试","20198759116621":233,"20198759698847":112,"20198760386122":0,"20198760582005":0,"20198760774621":"http://yourway.en.alibaba.com/product/52056664-50406697/Little_white_man.html","20198760926861":"","20198760997806":"Box","20198761465524":"第三级产品分组","20198761578590":"蓝牙耳机B【jane05】","20198762302678":[{"id":"87611785","src":"https://v4client.oss-cn-hangzhou.aliyuncs.com/******************************.jpg"}],"20198762659822":"","20198763595788":"蓝牙耳机B","20198765284162":"104,","20198764805201":"52","20198765397207":"","20198765495834":"","20198798937862":"","20198799825511":[],"20198801970634":"","20198802409451":"","20198803310531":"","20198812587137":"","20198823831061":"","20198828433318":"","20198828575190":"","20198832195703":"草稿","20198832270338":"","20198832637166":"sharon103","20198832789158":"测试供应商","20198833988611":"测试供应商收款账号联系人","20198835982881":100,"20198836051251":13.8669,"20198836480120":"2024-05-06","20198837314473":"2024-05-06","20198838137007":"","20198838721034":100,"20198839343765":5000,"20198845532703":"","20198854270517":[{"id":"87611785","src":"https://v4client.oss-cn-hangzhou.aliyuncs.com/******************************.jpg"}],"**************":"","**************":"","**************":"额测试","sku_info":{"attributes_info":[],"product_type":1},"numIndex":1,"20198832637166_info":[{"user_id":"3806196","email":"<EMAIL>","nickname":"sharon103","avatar":"https://ximfiles.oss-cn-hangzhou.aliyuncs.com/image_d8e1010d-48dc-4ac5-8464-908798d5d637","ames_email":""}]}],"cost_list":[]}';

        $clientId=3;
        $clientObject = \common\library\account\Client::getClient($clientId);
        $user = $clientObject->getMasterUser();
        \User::setLoginUser($user);
        $purchaseOrder = \common\library\purchase\purchase_order\API::createByGroupInfo($user->getUserId(), $data);
        echo $purchaseOrder->purchase_order_id;
    }

    // ./yiic zbp UpdatePgListenConfig
    public function actionUpdatePgListenConfig(){
        $sql = 'update tbl_replication_listener set report_field = \'["client_id","id","type"]\' where `table`="tbl_invoice_product_record"';
        echo \Yii::app()->prometheus_db->createCommand($sql)->execute();
    }

    //debugphp -d 'xdebug.client_port=9004' /data/codebase/bob/php-crm/protected/yiic-test zbp TestMetadata
    public function actionTestMetadata(){
        $clientId = 14787;
        $orderMetadata = new \common\library\oms\test\order\OrderMetadata($clientId);
        $orderAttributes = $orderMetadata->getAttributes();
        $orderProductMetadata = new \common\library\oms\test\order\OrderProductMetadata($clientId);
        $orderProductAttributes = $orderProductMetadata->getAttributes();
        $a =1;
    }

    /**
     * debugphp -d 'xdebug.client_port=9004' /data/codebase/bob/php-crm/protected/yiic-test zbp TestSingleObject
     */
    public function actionTestSingleObject(){
        $clientId = 14787;
//        echo '1'.PHP_EOL;
//        $order = new \common\library\oms\test\order\Order($clientId, 3376901018);
//        echo $order->order_id.PHP_EOL;
//        echo '2'.PHP_EOL;
//        $orderProduct = new \common\library\oms\test\order\OrderProduct($clientId, 3393563742);
//        echo $orderProduct->id.PHP_EOL;

        $orderMetadata = new \common\library\oms\test\order\OrderMetadata($clientId);
        $orderMetadata->test();
        var_dump(current($orderMetadata->getAttributes()));
        $orderProductMetadata = new \common\library\oms\test\order\OrderMetadata($clientId);
        var_dump(current($orderProductMetadata->getAttributes()));
        $orderProductMetadata->test();

        echo $orderMetadata->test().PHP_EOL;
        echo $orderProductMetadata->test().PHP_EOL;

    }

    /**
     * debugphp -d 'xdebug.client_port=9004' /data/codebase/bob/php-crm/protected/yiic-test zbp DeleteField
     */
    public function actionDeleteField(){
        $clientId = 334110;
        $clientObject = \common\library\account\Client::getClient($clientId);
        $user = $clientObject->getMasterUser();
        \User::setLoginUser($user);
        $type = \Constants::TYPE_PURCHASE_ORDER;

        $ids = ['**********','**********','**********','**********','**********','**********','**********','**********','**********','**********','**********','**********','**********','**********','**********','**********','**********','**********','**********','**********','**********','**********','**********','**********','**********','**********','**********','**********','**********','**********','**********','**********','**********','**********','**********','**********','**********','**********','**********','**********','**********','**********','**********','**********','**********','**********','**********','**********','**********','**********','**********','**********','**********','**********','**********'];

        $service = new \common\library\custom_field\CustomFieldService($user->getClientId(), $type);
        $service->setType($type);
        foreach($ids as $id){
            try{
                var_dump($service->deleteField($id));
            }catch (\Throwable $e){
                echo $e->getMessage().' | File:'.$e->getFile().' | Line:'.$e->getLine().PHP_EOL;
            }

        }

    }

    /**
     * debugphp -d 'xdebug.client_port=9004' /data/codebase/bob/php-crm/protected/yiic-test zbp TransferInvoiceToPurchase
     */
    public function actionTransferInvoiceToPurchase()
    {
        $data = '[{"sku_id":3082160358,"product_id":3082160357,"invoice_product_id":3118095055,"product_remark":"白色的软糖","unit":"Bag","order_id":**********,"transfer_invoice_record_id":3118117270,"transfer_invoice_id":**********,"supplier_list":[{"supplier_id":**********,"unit_price":0,"count":1,"currency":"CNY"}],"delivery_date":"2022-07-23 00:00:00"},{"sku_id":**********,"product_id":**********,"invoice_product_id":**********,"product_remark":"","unit":"Bottle","order_id":**********,"transfer_invoice_record_id":**********,"transfer_invoice_id":**********,"supplier_list":[{"supplier_id":**********,"unit_price":0,"count":1.**********,"currency":"CNY"}],"delivery_date":"2022-07-23 00:00:00"}]';
        $params = [
            'data' => $data,
            'handler' => 0
        ];
        \User::setLoginUserByAccount('<EMAIL>');
        $data = json_decode($data, true);
        $user = User::getLoginUser();
        $purchaseProcess = new \common\library\purchase\purchase_from_transfer_invoice\TransferInvoiceToPurchaseProcess($user->getUserId());
        var_dump($purchaseProcess->run($data,0));
    }

    // debugphp -d 'xdebug.client_port=9004' /data/codebase/bob/php-crm/protected/yiic-test zbp TestBatchInsert
    public function actionTestBatchInsert()
    {
        $clientId = 334110;
        $clientObject = \common\library\account\Client::getClient($clientId);
        $user = $clientObject->getMasterUser();
        \User::setLoginUser($user);
        $data = [
            ['product_id' => **********, 'product_no' => 'hhh'],
            ['product_id' => **********, 'product_no' => 'xxx'],
        ];
        $batchProduct = new \common\library\product_v2\BatchProduct($user->getClientId());
        $batchProduct->getOperator()->batchInsert($data, DBConstants::INSERT_MODE_CONFLICT, [], ['product_id']);
    }

    //debugphp -d 'xdebug.client_port=9004' /data/codebase/bob/php-crm/protected/yiic-test zbp FindOrderProductRateDownStream
    public function actionFindOrderProductRateDownStream()
    {
        $sqlDbList = \common\library\account\service\DbService::getDbList(DbSet::TYPE_PGSQL);
        $targetClients = [];

        foreach ($sqlDbList as $dBSet) {
            try{
                $setId = $dBSet['set_id'];
                $db = \PgActiveRecord::getDbByDbSetId($setId);
                $sql = "select client_id, field, object_name from tbl_field_relation where relation_object_name = 'objOrderProduct' and relation_object_field in ('tax_refund_rate','vat_rate') and enable_flag=1";
                $result = $db->createCommand($sql)->queryAll();
                $targetClients = array_merge($targetClients, array_unique(array_column($result, 'client_id')));
                PgActiveRecord::releaseDbBySetId($setId);
                echo json_encode($targetClients);
            }catch (\Throwable $e){
                echo $e->getMessage().' | File:'.$e->getFile(). ' | Line:'.$e->getLine().PHP_EOL;
                continue;
            }
        }

        echo json_encode($targetClients);
    }

    //debugphp -d 'xdebug.client_port=9004' /data/codebase/bob/php-crm/protected/yiic-test zbp TestInitProfitFormula
    public function actionTestInitProfitFormula($clientId){
        $clientObject = \common\library\account\Client::getClient($clientId);
        $user = $clientObject->getMasterUser();
        \User::setLoginUser($user);
        (new \common\library\setting\library\order_profit\ProfitFormulaApi($clientId))->initFormula();
    }

    // debugphp -d 'xdebug.client_port=9004' /data/codebase/bob/php-crm/protected/yiic-test zbp TestFormat
    public function actionTestFormat(){
        $clientId = 334110;
        $orderFilter = new \common\library\oms\order\OrderFilter($clientId);
        $orderFilter->order_id =**********;
        $batch = $orderFilter->find();
        $batch->getFormatter()->displayAllFields();
        echo json_encode($batch->getAttributes());
    }

    // debugphp -d 'xdebug.client_port=9004' /data/codebase/bob/php-crm/protected/yiic-test zbp ProfitFormula
    public function actionProfitFormula($clientId = 14119){
        $clientObject = \common\library\account\Client::getClient($clientId);
        $user = $clientObject->getMasterUser();
        \User::setLoginUser($user);
        $orderId = **********;

        $orderFilter  = new OrderFilter($clientId);
        $orderFilter->order_id = $orderId;
        $orderFilter->selectAll();
        $orderBatch = $orderFilter->find();
        $orderBatch->getFormatter()->profitFormulaComputationSetting();
        $data = $orderBatch->getListAttributes();
        $orderList = array_column($data,null,'order_id');
        var_dump($data);
        $a = 1;
    }

    //debugphp -d 'xdebug.client_port=9004' /data/codebase/bob/php-crm/protected/yiic-test zbp EditPurchaseOrder
    public function actionEditPurchaseOrder(){
        $data = '{"**********":"","**********":"","**********":"","**********":"","**********":[],"**********":"","**********":"","**********":"","**********":"","**********":"","**********":"","**********":"","**********":"","**********":"","**********":"","*********3":"","*********7":"","3499328771":"","3499328775":"","3499328779":"","3499328783":"","3499328823":"","3499328834":"","3499328838":"","3499328842":"","3499328846":"","3499328850":"","3499328854":"","3499328858":"","3499328862":"","3499328866":"","3499328871":"","3499328875":"","3499328879":"","3499328883":"","3499328887":"","3499328891":"","3499328895":"","3499328899":"","3499328903":"","3499328907":"","3499328915":"","3499328919":"","3499328923":"","3499328927":"","3499328931":"","3499328935":"","3499328939":"","3499328943":"","3499328947":"","3499328951":"","3499328955":"","3499328959":"","3499328963":"","3499328967":"","3499328971":"","3499328975":"","3499328979":"","3499328983":{"tel":"","tel_area_code":""},"3499328987":"","3499328991":"","3499328995":[],"3499328999":"","3499329003":"","3499329007":"","3499329011":"","3499329015":"","3499329020":0,"3499329026":[],"3499329032":"","3499329036":"","3499329040":"","3499329044":0,"3499329048":"","3499329052":"","3499329056":"","3499329082":[],"3499329120":"","3499329124":"","3499329128":"","3513082812":null,"3513084350":null,"3513084372":null,"3513084382":null,"3513085296":null,"3513085305":null,"3513085314":null,"3513134736":null,"3513134741":null,"3513134746":null,"3513134751":null,"3513134756":null,"3513134767":null,"3513134772":null,"3513134777":null,"3513134782":null,"3513134788":null,"3513134793":null,"3513134798":null,"3513134803":null,"3513134808":null,"3513134813":null,"3513134818":"","3513134823":null,"3513134828":null,"3513134833":"","3513135014":null,"3513135020":"","3513135025":null,"3513135030":null,"3513135035":null,"3513135040":null,"3513135045":null,"3513135050":null,"3513135055":null,"3513135060":null,"3513135065":null,"3513135070":null,"3513135075":null,"3513135080":null,"3513135085":null,"3513135090":null,"3513135095":null,"3513135100":null,"3513135105":null,"3513135110":null,"3513135115":null,"3513135120":null,"3513135125":null,"3513135130":null,"3513135135":null,"3513135140":null,"3513135145":null,"3513135150":null,"3513135155":null,"3513135160":null,"3513135165":"","3513135170":null,"3513135175":null,"3513135180":null,"3513135185":"","3513135190":null,"3513135197":null,"3513135202":null,"3513135208":null,"3513135224":null,"3513135236":null,"3513135243":null,"3513135248":"","3513135361":null,"3513135372":null,"3513135377":null,"3513135386":"","3513135395":null,"3513135400":null,"3513135416":null,"3513135435":"","3560515450":0,"3562688299":0,"purchase_order_id":**********,"purchase_order_no":"CGDD2407220313","client_id":334110,"creator":249520846,"handler":[249520846],"supplier_id":3571052133,"supplier_contact":3571052134,"purchase_date":"2024-07-22","delivery_date":"","currency":"CNY","exchange_rate":100,"exchange_rate_usd":15,"amount":"0","product_total_amount":"0","addition_cost_amount":0,"product_total_count":0,"remark":null,"enable_flag":1,"create_type":1,"payment_status":1,"payment_amount":0,"inbound_status":1,"amount_rmb":0,"amount_usd":0,"product_total_amount_rmb":0,"product_total_amount_usd":0,"payment_amount_rmb":0,"payment_amount_usd":0,"refer_order_id":0,"purchase_type":2,"parts_total_count":"0.0000000000","product_total_count_no_parts":"0.0000000000","creator_info":{"user_id":"249520846","avatar":"","name":"field","nickname":"field"},"file_map":[],"supplier_no":"SU0017","last_approval_info":[],"lock_flag":0,"payment_invoice":{"has_payment_invoice":0,"payment_invoice_sum_amount":0},"has_refer_invoice":0,"refer_invoice":[],"payment_wait_amount":0,"has_pending_payment_invoice":0,"payment_wait_amount_rmb":0,"payment_wait_amount_usd":0,"can_unlock":false,"is_parts_model":true,"editPaddingWrap":{"productsPanel":{"record_list":{"0":{"3478691615":{"initialValue":{"carton_size_height":null,"carton_size_length":null,"carton_size_weight":null}},"initialValue":{"3478691420":[],"3478691424":[],"3478691428":[],"3478691432":[],"3478691436":[],"3478691440":0,"3478691444":"sku描述","3478691448":"产品链接","3478691452":"产品文本+edit","3478691456":"产品单选","3478691460":["产品多选","产品多选1"],"3478691464":"产品多行文本","3478691468":"2024-07-04","3478691472":550,"3478691476":[],"3478691480":"","3478691484":[],"3478691488":"67.0000","3478691492":"价格文本","3478691496":"价格单选","3478691500":["价格多选","价格多选1"],"3478691504":"价格多行文本","3478691508":"2024-07-04","3478691512":[],"3478691516":"","3478691520":{"product_size_height":"30","product_size_length":"40","product_size_weight":"50"},"3478691524":"550","3478691528":"550","3478691533":"尺寸文本","3478691537":"尺寸单选","3478691541":["尺寸多选","尺寸多选1"],"3478691545":"尺寸多行文本","3478691549":"2024-07-04","3478691553":[],"3478691557":"","3478691561":{"package_size_height":"450","package_size_length":"56","package_size_weight":"100"},"3478691565":"550","3478691569":"550","3478691573":"550","3478691577":"包装说明","3478691581":"包装文本","3478691586":"包装单选","3478691591":["包装下拉多选","包装下拉多选1"],"3478691595":"包装多行文本","3478691599":"2024-07-04","3478691603":550,"3478691607":[],"3478691611":"","3478691615":{"carton_size_height":null,"carton_size_length":null,"carton_size_weight":null},"3478691619":"550","3478691623":"550","3478691627":"550","3478691632":"550","3478691636":"包装方式","3478691640":"外箱文本","3478691644":"外箱单选","3478691648":["外箱多选","外箱多选1"],"3478691652":"外箱多行文本","3478691656":"2024-07-04","3478691660":550,"3478691664":[],"3478691668":"","3478691672":[],"3478691676":"海关编码","3478691680":"550.000000","3478691684":"报关中文名","3478691688":"报关英文名","3478691692":"550.000000","3478691696":"报关文本","3478691700":"报关单选","3478691704":["报关多选","报关多选1"],"3478691709":"报关多行文本","3478691713":"2024-07-04","3478691717":550,"3478691721":[],"3478691725":"","3478691729":"","3478691733":"更多文本","3478691737":"更多单选","3478691741":["更多多选","更多多选1"],"3478691745":"更多多行文本","3478691749":"2024-07-04","3478691753":[],"3478691757":"","3478691767":"","3478691771":"","3478691775":0,"3478691779":0,"3478691783":"","3478691787":"","3478691791":"","3478691795":"","3478691799":"","3478691803":"","3478691807":"","3478691811":"","3478691815":"","3478691819":"","3478691823":"","3478691827":"","3478691831":"","3478691835":"","3478691839":"","3478691843":[""],"3478691847":"","3478691851":"","3478691855":0,"3478691859":[],"3478691863":"","3478691867":"","3478691871":"","3478691875":"","3478691879":"","3478691883":"","3478691887":"","3478691891":"","3478691895":[""],"3478691899":"","3478691903":"","3478691907":[],"3478691911":"","3478691915":"","3478691919":0,"3478691923":0,"3478691927":"","3478691931":"","3478691935":[""],"3478691939":"","3478691943":"","3478691947":[],"3478691951":"","3478691955":"","3478691959":"","3478691963":"","3478691967":[""],"3478691971":"","3478691975":"","3478691979":0,"3478691983":[],"3478691987":"","3478691991":0,"3478691995":0,"3478691999":0,"3478692003":"","3478692007":"","3478692011":0,"3478692015":0,"3478692019":0,"3478692023":0,"3478692027":"","3478692031":"","3478692035":"","3478692039":[""],"3478692043":"","3478692047":"","3478692051":0,"3478692055":[],"3478692059":"","3478692063":"","3478692067":"","3478692071":"","3478692075":"","3478692079":"","3478692083":"","3478692087":[""],"3478692091":"","3478692095":"","3478692099":0,"3478692103":[],"3478692107":"","3478692111":0,"3478692115":0,"3478692119":"","3478692123":"","3478692127":[""],"3478692131":"","3478692135":"","3478692139":[],"3478692143":"","3513138190":"自动化供应商1719544682509","3513138332":null,"3513138338":null,"3513138343":null,"3513138348":null,"3513138353":null,"3513138358":null,"3513138363":null,"3513138368":null,"3513138373":null,"3513138378":null,"3513138383":null,"3513138388":null,"3513138393":null,"3513138420":null,"3513138425":null,"3513138430":null,"3513138435":null,"3513138443":null,"3513138448":null,"3513138453":null,"3513138458":null,"3513138463":null,"3513138470":null,"3513138485":"","3513138491":null,"3513138535":null,"3513138545":null,"3513138550":null,"3513138568":null,"3513138583":"","3513138594":"","3513138612":null,"3513138622":null,"3513138663":"","3513138673":null,"3513138736":null,"3513138752":"","3513138767":null,"3513138794":"","3513138834":null,"3513138864":"","3513138875":null,"3513138908":null,"3513139007":null,"3513139018":null,"3513139150":null,"3513139155":null,"3513139162":null,"3513139169":null,"3560515463":0,"3562688285":0,"3562688292":-28,"purchase_order_product_id":3593474070,"purchase_order_id":**********,"product_id":3580306826,"sku_id":3580306825,"invoice_product_id":0,"order_id":0,"supplier_id":3571052133,"client_id":334110,"unit":"Case","unit_price":7,"count":4,"cost_amount":28,"product_remark":"sku备注","enable_flag":1,"order_num":0,"create_time":"2024-07-22 13:57:58","update_time":"2024-07-22 13:57:58","is_master_product":0,"master_id":0,"product_image":[],"product_name":"多规格有配件2024-07-04","product_cn_name":"中文产品名称","product_model":"产品型号","product_no":"1693_1","sku_code":"1693_1","product_disable_flag":0,"sku_info":{"sku_id":3580306825,"product_type":2,"attributes_info":[{"item_id":"3566905721","item_name":"Ronald White","value":{"item_id":"3566905722","item_name":"Walker"}}]},"supplier_product_name":"","supplier_product_no":"","order_no":"","transfer_invoice_serial_id":"","transfer_invoice_id":"","transfer_invoice_record_id":"","warehouse_inbound_time":"","inbound_count":0,"have_inbound_count":0,"wait_inbound_count":4,"have_return_count":0,"task_inbound_count":0,"check_task_inbound_count":0,"to_inbound_count":4,"is_sub_product":0,"combine_product_id":0,"combine_product_no":"","master_product_info":[],"enable_count":0,"_index_id":"5c634871"}}}}},"product_list":[],"cost_list":[]}';

        $clientId = 334110;
        $purchase_order_id = **********;
        $clientObject = \common\library\account\Client::getClient($clientId);
        $user = $clientObject->getMasterUser();
        \User::setLoginUser($user);
        $isNewData = 1;
        $data = json_decode($data, true);
        $purchaseOrder = new \common\library\purchase\purchase_order\PurchaseOrder($clientId, $purchase_order_id);

        //确认状态的订单，走变更权限
        if ($purchaseOrder->getStatusService()->isEndingStatus($purchaseOrder->status)) {
            $purchaseOrder->setPurchaseOrderEditPrivilege(\common\library\privilege_v3\PrivilegeConstants::PRIVILEGE_CRM_PURCHASE_ORDER_CHANGE);
        }

        $purchaseOrder->isEditAble(true);
        $purchaseOrder->getOperator()->editByGroupInfo($data, []);
    }

    //debugphp -d 'xdebug.client_port=9004' /data/codebase/bob/php-crm/protected/yiic-test zbp testCriteria --clientId=1 --ruleId=**********
    public function actionTestCriteria($clientId, $ruleId){
        $sql = 'select criteria from tbl_workflow_rule where rule_id = '.$ruleId;
        $db = \PgActiveRecord::getDbByClientId($clientId);
        $data = $db->createCommand($sql)->queryRow();
        var_dump($data['criteria']);

        $data['filters'] = [
            [
                'filter_no' => 1,
                'value' => true,
            ],
            [
                'filter_no' => 2,
                'value' => true,
            ],
            [
                'filter_no' => 3,
                'value' => true,
            ],
            [
                'filter_no' => 4,
                'value' => false,
            ],
            [
                'filter_no' => 5,
                'value' => false,
            ],
        ];

        try{
//            $criteria = new \common\library\flow\component\CriteriaComponent();
            $criteria->init($data['filters'], 'filter_no', $data['criteria'], \common\library\layout\layout_rule\calculate\RuleExecuteFilter::class);
            $criteria->parseCriteria();
            $criteria->process();
            echo $criteria->print().PHP_EOL;
            var_dump($criteria->getResult());
        }catch (\Throwable $e){
            echo $e->getMessage(). " | File:".$e->getFile().' | Line:'.$e->getLine();
            echo PHP_EOL;
            echo $e->getTraceAsString();
        }

    }

    // debugphp -d 'xdebug.client_port=9004' /data/codebase/bob/php-crm/protected/yiic-test zbp TestFilterFeat2
    public function actionTestFilterFeat2(){
        try{
            $clientId = 14100;
            $filter = new \common\library\customer_v3\company\orm\CompanyFilter($clientId);
//        $filter->initJoin();
//        $filter->setRawSelect('distinct  C.*, C.order_time');
            $filter->select(['company_id']);
            $filter->distinct(1);
            $filter->setAlias('xxx');
            $filter->setRawJoin(" join tbl_opportunity as O on   O.main_user=11858673 ");
            $filter->setRawJoin(" join tbl_order on   tbl_order.company_id=xxx.company_id ");
//        $filter->cleanWhere();
//        $filter->rawWhere(" C.client_id=14100  and ( C.user_id='{}' or C.user_id @> ARRAY[11858673]::bigint[] ) AND C.is_archive=1 and O.stage_type in (1)");
            $filter->order('order_time');
            $filter->limit(1);
//        $filter->joinOrder('order_time');
//        $filter->rawOrder(function ($orderField) {
//
//            return "C." . $orderField . ' desc, O.company_id desc';
//        }, 'order_time');

            echo json_encode($filter->isJoinSence());

            $grammar = $filter->getCompileSqlGrammar();
            var_dump($grammar->getSql());
            var_dump($grammar->getParam());
            var_dump($grammar->getOrder());
            var_dump($grammar->getLimit());
            $result = $filter->count();
            var_dump($result);
        }catch(\Throwable $e){
            echo $e->getMessage() . ' | File:'.$e->getFile() . ' | Line:'.$e->getLine();
            echo $e->getTraceAsString();
        }

    }

    // 测试 Filter 的新特性
    // debugphp -d 'xdebug.client_port=9004' /data/codebase/bob/php-crm/protected/yiic-test zbp TestFilterFeat
    public function actionTestFilterFeat(){
        try{
            // 测试原生查询
            $clientId = 14119;
//        $sql = "select * from tbl_order where client_id=14119 and account_date >= '2024-07-01' and account_date <= '2024-07-20' limit 1";
//        $filter = new \common\library\oms\order\OrderFilter($clientId);
//        $result = $filter->queryAll($sql);
//        var_dump($result);
//        echo json_encode($result);

//            $sql = "select * from tbl_purchase_order where client_id=14119 and create_time >= '2024-07-01' and create_time <= '2024-07-20' limit 1";
//        $filter = new \common\library\purchase\purchase_order\PurchaseOrderFilter($clientId);
//        $result = $filter->queryAll($sql);
//        var_dump($result);
//        echo json_encode($result);
//        die;

            // 测试原生查询关联查询
//        $sql = "select * from tbl_order o inner join tbl_invoice_product_record p on o.order_id = p.refer_id where o.client_id=14119 and o.account_date >= '2024-07-01' and o.account_date <= '2024-07-20' limit 1";
//        $filter = new \common\library\oms\order\OrderFilter($clientId);
//        $subFilter = new \common\library\oms\test\order\OrderProductFilter($clientId);
//        $filter->initJoin();
//        $filter->innerJoin($subFilter);
//        $result = $filter->queryAll($sql);
//        var_dump($result);

//        $sql = "select * from tbl_purchase_order p inner join tbl_purchase_order_product pp on p.purchase_order_id = pp.purchase_order_id where p.client_id=14119 and p.create_time >= '2024-07-01' and p.create_time <= '2024-07-20' limit 1";
//        $filter = new \common\library\purchase\purchase_order\PurchaseOrderFilter($clientId);
//        $subFilter = new \common\library\purchase\purchase_order_product\PurchaseOrderProductFilter($clientId);
//        $filter->initJoin();
//        $filter->innerJoin($subFilter);
//        $result = $filter->queryAll($sql);
//        var_dump($result);

            // 测试join场景下使用原生排序
//            $filter = new \common\library\purchase\purchase_order\PurchaseOrderFilter($clientId);
//            $subFilter = new \common\library\purchase\purchase_order_product\PurchaseOrderProductFilter($clientId);
//            $filter->initJoin();
//            $filter->innerJoin($subFilter);
//            $filter->on($filter->getTableName(), 'purchase_order_id', $subFilter->getTableName(), 'purchase_order_product_id');
//            $filter->rawOrder(function($field){
//                return "natural_sort({$field}) desc";
//            }, 'purchase_order_no');
//            $filter->joinOrder('purchase_order_id','desc', $filter->getMetadata()::objectName());
//            $filter->joinlimit(10,10);
//            $filter->cleanOrder();                // 清空排序
//            $result = $filter->rawData();
//            var_dump($result);

            // 测试打印sql
//            $grammar = $filter->getCompileSqlGrammar();
//            var_dump($grammar->getSql());
//            var_dump($grammar->getParam());


            // 测试客户同学的需求SQL
            // select  distinct  C.*, C.order_time from tbl_company as C join tbl_opportunity as O on C.client_id=O.client_id AND C.company_id=O.company_id AND O.enable_flag = 1 and O.main_user=11858673 where C.client_id=14100  and ( C.user_id='{}' or C.user_id @> ARRAY[11858673]::bigint[] ) AND C.is_archive=1 and O.stage_type in (1)   order by C.order_time desc
//            $clientId=14100;
            $filter = new \common\library\customer_v3\company\CompanyFilter($clientId);
            $filter->initJoin();
            $filter->setRawSelect('distinct  C.*, C.order_time');
            $filter->setRawJoin(" as C join tbl_opportunity as O on C.client_id=O.client_id AND C.company_id=O.company_id AND O.enable_flag = 1 and O.main_user=11858673 ");
            $filter->cleanWhere();
            $filter->rawWhere(" C.client_id=14100  and ( C.user_id='{}' or C.user_id @> ARRAY[11858673]::bigint[] ) AND C.is_archive=1 and O.stage_type in (1)");
            $filter->rawOrder(function($orderField){
                return "C.".$orderField.' desc';
            }, 'order_time');
            $filter->limit(10,1);
//            $grammar = $filter->getCompileSqlGrammar();
//            var_dump($grammar->getSql());
//            var_dump($grammar->getParam());
            $result = $filter->rawData();
            var_dump($result);

        }catch (\Throwable $e){
            echo $e->getMessage(). '|File:'.$e->getFile().'|Line:'.$e->getLine();
            echo $e->getTraceAsString();
            die;
        }

    }

    // debugphp -d 'xdebug.client_port=9004' /data/codebase/bob/php-crm/protected/yiic-test zbp FixCostAmount
    public function actionFixCostAmount($clientId = null, $grey = 0, $greyNum = null){
        self::info("FindOrderRelationFields start" . PHP_EOL);
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey && !is_null($greyNum)) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        $targetClient = [];
        foreach ($clientIds as $clientId) {
            try{
                $selectSql = "select * from tbl_custom_field where client_id={$clientId} and id='cost_amount' and enable_flag=1 and type=2";
                $selectSql2 = "select * from tbl_field where client_id={$clientId} and field='cost_amount' and enable_flag=1 and object_name='objOrderProduct'";

                $db = \ProjectActiveRecord::getDbByClientId($clientId);
                $costAmountField = $db->createCommand($selectSql)->queryRow();
                $extInfo = json_decode($costAmountField['ext_info'], true);

                // 判断 cost_amout 是否需要修正
                if($costAmountField['field_type'] != \common\library\custom_field\CustomFieldService::FIELD_TYPE_FORMULA || empty($extInfo['is_editable'])){
                    \ProjectActiveRecord::releaseDbByClientId($clientId);
                    continue;
                }

                $targetClient[] = $clientId;
                self::info('targetClient:'. json_encode($targetClient));

                $pgDb = \PgActiveRecord::getDbByClientId($clientId);
                $costAmountField2 = $pgDb->createCommand($selectSql2)->queryRow();

                $extInfo2 = json_decode($costAmountField2['ext_info'], true);
                unset($extInfo['is_editable']);
                unset($extInfo2['is_editable']);

                var_dump($extInfo);
                var_dump($extInfo2);

                $updateSql = "update tbl_custom_field set ext_info=:ext_info where client_id={$clientId} and type=2 and id='cost_amount'";
                $updateSql2 = "update tbl_field set ext_info=:ext_info where client_id={$clientId} and field='cost_amount' and enable_flag=1 and object_name='objOrderProduct'";

//                $db->createCommand($updateSql)->execute(['ext_info' => json_encode($extInfo)]);
//                $pgDb->createCommand($updateSql2)->execute(['ext_info' => json_encode($extInfo2)]);
                \ProjectActiveRecord::releaseDbByClientId($clientId);
                \PgActiveRecord::releaseDbByClientId($clientId);
            } catch (\Throwable $e) {
                self::info("FixCostAmount for client_id:【{$clientId}】 failed, error on File:" . $e->getFile() . " | Line : " . $e->getLine() . " | Msg : " . $e->getMessage() . PHP_EOL);
                continue;
            }
        }

        self::info("finish, targetClient:".json_encode($targetClient));
    }

    // debugphp -d 'xdebug.client_port=9004' /data/codebase/bob/php-crm/protected/yiic-test zbp TestPrivilege --clientId=14119 --userId=******** --objectName=objCompany
    public static function actionTestPrivilege($clientId, $userId, $objectName){
        $functionId = \common\library\object\object_define\Constant::FUNCTIONAL_MAP[$objectName];
        $fieldStats = \common\library\privilege_v3\Helper::getPrivilegeFieldStats($clientId, $userId, $functionId);
        echo json_encode($fieldStats);
    }

    //debugphp -d 'xdebug.client_port=9004' /data/codebase/bob/php-crm/protected/yiic-test zbp FixExchangeRateUsd --clientId=355591
    public function actionFixExchangeRateUsd($clientId = null, $grey = 0, $greyNum = null, $startClientId = 0){
        self::info("FixExchangeRateUsd start" . PHP_EOL);
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey && !is_null($greyNum)){
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        $clientMap = [];
        foreach ($clientIds as $clientId) {
            if($clientId < $startClientId){
                continue;
            }

            try {
                $pgDb = \PgActiveRecord::getDbByClientId($clientId);
                $selectSql = "select * from tbl_field where client_id={$clientId} and object_name ='objOrder' and field ='exchange_rate_usd'";
                $selectRes = $pgDb->createCommand($selectSql)->queryRow();

                if(!empty($selectRes) && is_array($selectRes) && ($selectRes['columns'] == '{}' || $selectRes['columns'] == '[]' || $selectRes['columns'] == '')){
                    $clientMap[] = $clientId;
                    self::info("to fix client:".$clientId);
                }else{
                    continue;
                }

                $columns = '{"data_key": "exchange_rate_usd", "data_db_type": 5}';
                $updateSql = "update tbl_field set columns  = '{$columns}'::jsonb where  object_name ='objOrder' and field ='exchange_rate_usd' and  client_id = {$clientId} and (columns ='{}' or columns='[]')";
                $affectRow = $pgDb->createCommand($updateSql)->execute();

            } catch (\Throwable $e) {
                self::info("FixExchangeRateUsd for client_id:【{$clientId}】 failed, error on File:" . $e->getFile() . " | Line : " . $e->getLine() . " | Msg : " . $e->getMessage() . PHP_EOL);
                if(isset($selectRes)){
                    self::info('selectRes:'.json_encode($selectRes));
                }
                continue;
            }
        }

        self::info("Finish, clientMap:".json_encode($clientMap));
    }

    // ./yiic zbp TestPreview
    public function actionTestPreview($clientId=226, $invoice_id=24777174475936, $template_id=22089927987985,$type='excel',$name='【合同】WAP10-2408ALTM004'){
        ini_set("memory_limit", "512M");

        set_error_handler(function($errno, $errstr, $errfile, $errline) {
            // 打印或记录错误信息
            error_log("Error: $errstr in $errfile on line $errline");

            // 捕获调用栈
            $backtrace = debug_backtrace();
            foreach ($backtrace as $call) {
                if (isset($call['function'])) {
                    error_log("Called function: {$call['function']}");
                }
            }
        });

        set_exception_handler(function($exception) {
            // 打印或记录异常信息
            error_log("Exception: " . $exception->getMessage());

            // 捕获调用栈
            $backtrace = $exception->getTrace();
            foreach ($backtrace as $call) {
                if (isset($call['function'])) {
                    error_log("Called function: {$call['function']}");
                }
            }
        });

        $clientObject = \common\library\account\Client::getClient($clientId);
        $user = $clientObject->getMasterUser();
        \User::setLoginUser($user);

        $loginUser = \User::getLoginUser();

        $export = \common\library\invoice\export\InvoiceExportFactory::instance($loginUser->getClientId(), $loginUser->getUserId(), $invoice_id, $template_id)->make(\Constants::TYPE_ORDER);
        $export->setType($type);
        $export->setFileName($name);
        $export->setExportMethod(\common\library\invoice\export\AbstractInvoiceExport::METHOD_PREVIEW, false);
        $export->setModule('crm');
        echo json_encode($export->preview());
    }

    // debugphp -d 'xdebug.client_port=9004' /data/codebase/bob/php-crm/protected/yiic-test zbp TestOrderDefault
    public function actionTestOrderDefault(){
        $clientId = 14119;
        $clientObject = \common\library\account\Client::getClient($clientId);
        $user = $clientObject->getMasterUser();
        \User::setLoginUser($user);
//        $company = new common\library\customer_v3\company\Company($clientId);
//        $company->short_name = '1231';
//        $company->name = '45671';
//        $company->addUser(********);
        $company = new \common\library\customer_v3\company\Company($clientId);
        $company->score = json_decode('{"data": 67, "star": 80, "match": 0, "total": 30, "active": 0, "compact": 0, "user_match": [], "user_total": []}', true);
//        $company->create();

        $company->name = 'ttteeess9090sttt' . rand(333, 999);
        $company->create();

        echo $company->company_id;
        echo json_encode($company->user_id);

//        echo json_encode($company->getAttributes());
        var_dump($company->group_id);
    }

    // debugphp -d 'xdebug.client_port=9004' /data/codebase/bob/php-crm/protected/yiic-test zbp TestOrderDefault2
    public function actionTestOrderDefault2(){
        $clientId = 351352;
        $clientObject = \common\library\account\Client::getClient($clientId);
        $user = $clientObject->getMasterUser();
        \User::setLoginUser($user);
        $company = new \common\library\customer_v3\company\Company($clientId);
        $data = json_decode('{"company":{"**********":"","country":"","homepage":"","serial_id":"","name":"123","origin_list":"","short_name":"","cus_tag":"","trail_status":"","pool_id":0,"biz_type":"","intention_level":"","tel":"","annual_procurement":"","timezone":"","scale_id":0,"product_group_ids":"","star":"","group_id":"","fax":"","address":"","remark":"","image_list":"","ali_store_id":"","duplicate_flag":"","growth_level":"","latest_whatsapp_receive_time":"","latest_whatsapp_send_time":"","latest_whatsapp_business_send_time":"","latest_whatsapp_time":"","latest_write_follow_up_time":"","lead_archive_time":"","next_move_to_public_date":"","ongoing_opportunity_count":"","order_time":"","latest_whatsapp_business_receive_time":"","performance_order_count":"","latest_wechat_time":"","private_time":"","public_time":"","receive_mail_time":"","recent_follow_up_time":"","release_count":"","score":"","send_mail_time":"","success_opportunity_count":"","success_opportunity_first_time":"","tips_latest_update_time":"","transaction_order_first_time":"","user_id":"","latest_wechat_receive_time":"","alibaba_first_sync_time":"","alibaba_last_owner":"","alibaba_recent_sync_time":"","alibaba_user_id":"","archive_time":"","archive_type":"","ciq_latest_update_time":"","create_user":"","customer_count":"","deal_time":"","edit_time":"","edm_time":"","last_edit_user":"","latest_facebook_receive_time":"","last_owner":"","latest_wechat_send_time":"","latest_transaction_order_time":"","latest_success_opportunity_time":"","latest_send_ali_tm_time":"","latest_reply_ali_trade_time":"","latest_receive_ali_trade_time":"","latest_receive_ali_tm_time":"","latest_ins_time":"","latest_ins_send_time":"","latest_ins_receive_time":"","latest_facebook_send_time":"","success_opportunity_amount_avg_cny":"","success_opportunity_amount_avg_usd":"","success_opportunity_amount_cny":"","success_opportunity_amount_usd":"","transaction_order_amount":"","transaction_order_amount_avg":"","transaction_order_first_amount":""},"customers":[{"company_id":"","email":"","main_customer_flag":1,"forbidden_flag":"","source_customer_id":"","name":"123","contact":"","tel_list":"","post_grade":"","post":"","birth":"","gender":"","image_list":"","remark":""}],"opportunity":{}}', true);
        $company->score = json_decode('{"data": 67, "star": 80, "match": 0, "total": 30, "active": 0, "compact": 0, "user_match": [], "user_total": []}', true);
//        $company->name = 'zbp' . rand(333, 999);
//        $company->last_owner = '';
        $company->bindAttrbuties($data['company']);
        $company->create();

//        echo $company->company_id;
        var_dump($company->user_id);

//        echo json_encode($company->getAttributes());
        var_dump($company->group_id);
    }

    // debugphp -d 'xdebug.client_port=9004' /data/codebase/bob/php-crm/protected/yiic-test zbp TestClientInit
    public function actionTestClientInit(){
        $userId = 56597030;
        $user = new \User($userId);
        \User::setLoginUser($user);
        CustomerOptionService::iniClient($user, "tw_lite_ai");
    }

    // debugphp -d 'xdebug.client_port=9004' /data/codebase/bob/php-crm/protected/yiic-test zbp TestCustomer
    public function actionTestCustomer(){
        $clientId = 351352;
        $meta = new \common\library\customer_v3\customer\CustomerMetadata($clientId);
        $customerMapFields = $meta->getMappedAttributeKeys();
        var_dump($customerMapFields);

//        $meta = new \common\library\customer_v3\company\CompanyMetadata($clientId);
//        $companyMapFields = $meta->getMappedAttributeKeys();
//        var_dump($companyMapFields);

//        $meta = new \common\library\oms\order\OrderMetadata($clientId);
//        $companyMapFields = $meta->getMappedAttributeKeys();
//        var_dump($companyMapFields);
    }

    // debugphp -d 'xdebug.client_port=9004' /data/codebase/bob/php-crm/protected/yiic-test zbp Test
    public function actionTest(){
        var_dump(\Yii::app()->params['redis_pg_wal_pack_distribute']);
    }

    // debugphp -d 'xdebug.client_port=9004' /data/codebase/bob/php-crm/protected/yiic-test zbp TestPrivilegeV2
    public function actionTestPrivilegeV2(){
        $clientId = 351352;
        $objectName = 'objCompany';
        $clientObject = \common\library\account\Client::getClient($clientId);
        $user = $clientObject->getMasterUser();
        \User::setLoginUser($user);

        $fieldApi = new \common\library\object\field\Api();
        $fieldFilter = $fieldApi->buildFilter($clientId, ['object_name'=>$objectName]);
        $list = $fieldFilter->find();
        $list->getFormatter()->displayMultiLanguage(true);
        $list->getFormatter()->setObjName($objectName);
        $list->getFormatter()->displayFields($fieldFilter->getMetadata()->getColumnsKeys());
        $fieldList = array_column($list->getListAttributes(), null, 'field');     // 这里需要根据page_type来决定第三参 $scene

        $layoutApi = new \common\library\layout\LayoutApi($clientId, $objectName);
        echo json_encode($layoutApi->getPrivilegeFields($user, $fieldList));
    }

    // debugphp -d 'xdebug.client_port=9004' /data/codebase/bob/php-crm/protected/yiic-test zbp TestGetGrammar
    public function actionTestGetGrammar(){
        $clientId = 14119;
        $orderFilter = new OrderFilter($clientId);
        $orderFilter->order_no = '123';
        $orderFilter->limit(10);
        $grammar = $orderFilter->getCompileSqlGrammar();
        var_dump($grammar);
    }

    //debugphp -d 'xdebug.client_port=9004' /data/codebase/bob/php-crm/protected/yiic-test zbp FixCustomerFullTelList --clientId=351352
    public function actionFixCustomerFullTelList($clientId = null, $grey = 0, $greyNum = null, $startClientId = 0){
        self::info("FixCustomerFullTelList start" . PHP_EOL);
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey && !is_null($greyNum)){
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        $clientMap = [];
        $updateTime = '2024-10-21';
        foreach ($clientIds as $clientId) {
            if($clientId < $startClientId){
                continue;
            }

            try {
                $pgDb = \PgActiveRecord::getDbByClientId($clientId);
                $selectSql = "select company_id, customer_id, full_tel_list::text from tbl_customer where client_id={$clientId} and update_time > '{$updateTime}' and full_tel_list = '{\"\"}'";
                $selectRes = $pgDb->createCommand($selectSql)->queryAll();

                if(!empty($selectRes)){
                    $clientMap[] = $clientId;
                    self::info("to fix client:".$clientId. ', rows:'.json_encode($selectRes));
                }else{
                    continue;
                }

//                $updateCustomerId = implode(',', array_column($selectRes, 'customer_id'));
//                $updateSql = "update tbl_customer set full_tel_list='{}' where client_id={$clientId} and customer_id in ({$updateCustomerId}) and full_tel_list='{\"\"}'";
//                $affectRow = $pgDb->createCommand($updateSql)->execute();
//                self::info("client id: {$clientId}, affect rows:{$affectRow}");
            } catch (\Throwable $e) {
                self::info("FixCustomerFullTelList for client_id:【{$clientId}】 failed, error on File:" . $e->getFile() . " | Line : " . $e->getLine() . " | Msg : " . $e->getMessage() . PHP_EOL);
                if(isset($selectRes)){
                    self::info('selectRes:'.json_encode($selectRes));
                }
                continue;
            }
        }

        self::info("Finish, clientMap:".json_encode($clientMap));
    }

    //debugphp -d 'xdebug.client_port=9004' /data/codebase/bob/php-crm/protected/yiic-test zbp TestPrivilegeScope --clientId=351352 --userId=249521363
    public function actionTestPrivilegeScope($clientId, $userId){
        \User::setLoginUserById($userId);
        $user = \User::getLoginUser();
        $userIds = \common\library\privilege_v3\Helper::getPermissionScopeUser($user->getClientId(), $user->getUserId(), \common\library\privilege_v3\PrivilegeConstants::PRIVILEGE_CRM_ORDER_VIEW, true);

        var_dump($userIds);
    }

    //debugphp -d 'xdebug.client_port=9004' /data/codebase/bob/php-crm/protected/yiic-test zbp TestOrderInfo --clientId=351352 --userId=249521363
    public function actionTestOrderInfo($userId, $order_id){
        \User::setLoginUserById($userId);
        $user = \User::getLoginUser();
        $clientId = $user->getClientId();
        $groups = [2];
        $recycle_model = 0;
        $skip_view_privilege = false;
        $apply_form_id = 0;
        $show_system_info = 0;
//        $only_local_product = 0;
        $show_transfer = 0;
        $show_transfer_type = 0;
        $scene = \common\library\oms\order\OrderConstants::ORDER_INFO_SCENE_COMMON;
        $ignore_purchase_count_done = 0;
        $product_image_formatter = 0;
        $operate_type = '';
        $order = new \common\library\oms\order\Order($clientId, $order_id);

        if ($order->isNew()){
            throw new \RuntimeException(\Yii::t('invoice', 'order does not exist'));
        }

        if ($recycle_model) {
            $order->setScene(\common\library\oms\order\OrderConstants::ORDER_INFO_SCENE_RECYCLE);
        }

        if (!$order->isViewAble() && !$skip_view_privilege) {
            throw new RuntimeException(\Yii::t('account', 'No permission to view'), ErrorCode::CODE_INVOICE_NOT_PRIVILEGE_VIEW);
        }

        if ($apply_form_id > 0) {
            $applyForm = new \common\library\approval_flow\ApplyForm($apply_form_id, $this->getLoginUserId(), true);
            //只有审批中的才需要展示审批内容
            if (!$applyForm->isNew() && $applyForm->isApproving()) {
                $scene = \common\library\oms\order\OrderConstants::ORDER_INFO_SCENE_APPROVAL;
            }
        }

        if ($product_image_formatter) {
            $order->getFormatter()->setProductImageFormatter(true);
        }
        switch ($scene) {
            case \common\library\oms\order\OrderConstants::ORDER_INFO_SCENE_PUSH_DOWN:
                $order->getFormatter()->setProductImageFormatter(true);
                $order->getFormatter()->orderPushDownSetting($groups);
                $order->getFormatter()->displayProgressInfo(true);
                $order->getFormatter()->setShowTransferInfo($show_transfer, $show_transfer_type, $ignore_purchase_count_done);
                $order->getFormatter()->displayShippingCostList(true);

                break;
            case \common\library\oms\order\OrderConstants::ORDER_INFO_SCENE_COMMON:
                $order->getFormatter()->webInfoSetting($groups);
                $order->getFormatter()->displaySystemInfo($show_system_info);
                $order->getFormatter()->setShowTransferInfo($show_transfer, $show_transfer_type);
                $order->getFormatter()->displayOperatePrivilege(true);
                $order->getFormatter()->displayProgressInfo(true);
                $order->getFormatter()->setOperateButtonAccess([
                    'change_status',
                    'batch_change_status',
                    'member_manage',
                    'batch_member_manage',
                    'edit_serial',
                    'unlock',
                    'edit',
                    'copy',
                    'delete',
                    'create_schedule',
                    'export',
                    'associate_business',
                    'create_cash_collection_invoice',
                    'create_cash_collection',
                    'create_logistics',
                    'generate_purchase_order',
                    'assurance',
                    'eProceed',
                    'generate_order_outbound',
                    'create_stock_up_invoice' ,
                    'confirm_stock_up_done',
                    'confirm_shipping_done',
                    'confirm_end_done' ,
                    'cancel_end_done',
                    'create_purchase_product_transfer',
                    'create_inbound_product_transfer',
                    'create_outbound_product_transfer',
                    'refresh_gross_margin',
                    'create_transfer_invoice',
                    'generate_cost_invoice',
                    'generate_shipping_invoice',
                    'edit_attachment'
                ]);
                break;
            case \common\library\oms\order\OrderConstants::ORDER_INFO_SCENE_APPROVAL:
                //审批流的场景不能读宽表的数据
                $productList = null;
                if (!$applyForm->isNew() && $applyForm->isApproving()) {
                    $content = $applyForm->getContent();
                    $orderColumnFields = $order->getMetadata()->columnFields();
                    if (is_array($content)) {
                        $externalFieldData = $order->external_field_data ?? [];
                        foreach ($content as $field => $item) {
                            if (is_numeric($field)) {
                                $externalFieldData[$field] = $item['new'];
                                continue;
                            }

                            if ($field == 'product_list') {
                                $productList = $item['new'];
                                continue;
                            }

                            if (in_array($field, $orderColumnFields)) {
                                //这里的new值有含转义符号, 需要二次json_decode
                                if (in_array($field, ['file_list', 'cost_list'])) {
                                    $item['new'] = is_array($item['new']) ? $item['new'] : json_decode($item['new'], true);
                                }

                                $order->$field = $item['new'];
                            }
                        }
                        $order->external_field_data = $externalFieldData;
                    }
                }

                // 如果审批单没有订单明细的数据则查数据库
                if (!is_null($productList)) {
                    $order->setOrderProductList($productList);
                    $order->calculateFunctionalFields();
                    $order->refreshProductList();
                } else {
                    $order->loadOrderProductList();
                }

                $order->getFormatter()->setApprovalProductList($order->getOrderProductList());
                $order->getFormatter()->orderApprovalSetting($groups);
                $order->getFormatter()->setOperateButtonAccess([
                    'change_status',
                    'batch_change_status',
                    'member_manage',
                    'batch_member_manage',
                    'edit_serial',
                    'unlock',
                    'edit',
                    'copy',
                    'delete',
                    'create_schedule',
                    'export',
                    'associate_business',
                    'create_cash_collection',
                    'create_cash_collection_invoice',
                    'create_logistics',
                    'generate_purchase_order',
                    'assurance',
                    'eProceed',
                    'generate_order_outbound',
                    'create_stock_up_invoice' ,
                    'confirm_stock_up_done',
                    'confirm_shipping_done',
                    'confirm_end_done' ,
                    'cancel_end_done',
                    'create_purchase_product_transfer',
                    'create_inbound_product_transfer',
                    'create_outbound_product_transfer',
                    'refresh_gross_margin',
                    'create_transfer_invoice',
                    'generate_cost_invoice',
                    'generate_shipping_invoice',
                    'edit_attachment'
                ]);
                break;
        }

        try{
            $data = $order->getAttributes();
        }catch (\Throwable $e){
            echo 'File:'.$e->getFile().' | Line:'.$e->getLine().' | Msg:'.$e->getMessage().PHP_EOL;
            echo $e->getTraceAsString();
            die;
        }

        if (empty($data['service_fee_currency'])) {
            $data['service_fee_currency'] = common\library\account\Client::getClient($clientId)->getMainCurrency();
        }
        $data['can_unlock'] = $order->canUnlock();
        $data['data'] = \common\library\oms\order\Helper::formatInfoGroup($clientId, $groups, $data);
        if ($operate_type == \common\library\oms\order\OrderConstants::ORDER_OPERATE_TYPE_COPY) {
            $data = \common\library\oms\order\Helper::orderCopyProduct($data);
        }

        echo json_encode($data['data']);
    }

    //debugphp -d 'xdebug.client_port=9004' /data/codebase/bob/php-crm/protected/yiic-test zbp TestRefreshScopeUserField --clientId=351352 --objectName=objOrder --pids=**********,**********,**********
    public function actionTestRefreshScopeUserField($clientId, $objectName,$pids){
        $pids = explode(',', $pids);
        $metadataClass = ObjConstant::OBJ_METADATA_MAP[$objectName] ?? null;
        $metadata = new $metadataClass($clientId);
        $scopeUserService = new ScopeUserFieldService($clientId, $metadata);
        echo $scopeUserService->refreshScopeUserIdsByPids($pids);
    }

    //debugphp -d 'xdebug.client_port=9004' /data/codebase/bob/php-crm/protected/yiic-test zbp TestGetFieldScopeByUserRole --clientId=351352 --userId=*********
    public function actionTestGetFieldScopeByUserRole($clientId, $userId){
//        $service = new \common\library\privilege_v3\field\PrivilegeFieldService($userId, null);
//        $service->migrationFieldPrivilege();
//
//        // 刷新字段权限缓存
//        $privilegeField = new \common\library\privilege_v3\PrivilegeFieldV2($clientId);
//        $privilegeField->resetRoleFieldPrivilege([2114043]);

        $service = new \common\library\privilege_v3\object_service\UserObjectPrivilegeService($clientId);
        $service->setUserId($userId);
        $service->setFunctionalId(\common\library\privilege_v3\PrivilegeConstants::FUNCTIONAL_ORDER);
//        echo json_encode($service->getFieldScopeByUserRole());
        echo json_encode($service->getFieldIdByScope(\Constants::TYPE_ORDER, [
            PrivilegeConstants::FIELD_PRIVILEGE_SCOPE_READ_WRITE,
            PrivilegeConstants::FIELD_PRIVILEGE_SCOPE_REQUIRED,
            PrivilegeConstants::FIELD_PRIVILEGE_SCOPE_DISABLE, PrivilegeConstants::FIELD_PRIVILEGE_SCOPE_READONLY]));
    }

    // debugphp -d 'xdebug.client_port=9004' /data/codebase/bob/php-crm/protected/yiic-test zbp TranslateObject
    public function actionTranslateObject(){
        $configs = \common\library\object\object_define\Helper::getConfig();
        $mapCn = $mapEn = [];
        foreach($configs as $config){
            $mapCn[$config['object_name']] = $config['cn_name'];
            $mapEn[$config['object_name']] = $config['en_name'];
        }

        echo json_encode($mapCn).PHP_EOL.PHP_EOL;
        echo json_encode($mapEn).PHP_EOL.PHP_EOL;
    }

    // debugphp -d 'xdebug.client_port=9004' /data/codebase/bob/php-crm/protected/yiic-test zbp FindCostAmount
    public function actionFindCostAmount($clientId = null, $grey = 0, $greyNum = null){
        self::info("FindOrderRelationFields start" . PHP_EOL);
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey && !is_null($greyNum)) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(0, true), 'client_id');
        }

        $targetClient = [];
        foreach ($clientIds as $clientId) {
            try{
                $selectSql = "select * from tbl_custom_field where client_id={$clientId} and id='cost_amount' and enable_flag=1 and type in (2) and field_type != 11";

                $db = \ProjectActiveRecord::getDbByClientId($clientId);
                $costAmountFields = $db->createCommand($selectSql)->queryRow();

                // 判断 cost_amout 是否需要修正
                if(!empty($costAmountFields)){
                    $targetClient[] = $clientId;
                    self::info('targetClient:'. $clientId);
                }

                \ProjectActiveRecord::releaseDbByClientId($clientId);
            } catch (\Throwable $e) {
                self::info("FindCostAmount for client_id:【{$clientId}】 failed, error on File:" . $e->getFile() . " | Line : " . $e->getLine() . " | Msg : " . $e->getMessage() . PHP_EOL);
                continue;
            }
        }

        self::info("finish, targetClient:".json_encode($targetClient));
    }

    // 统计布局规则的使用情况
    // debugphp -d 'xdebug.client_port=9004' /data/codebase/bob/php-crm/protected/yiic-test zbp StatLayoutRuleUsing
    public function actionStatLayoutRuleUsing(){
        $sqlDbList = \common\library\account\service\DbService::getDbList(DbSet::TYPE_PGSQL);
        foreach ($sqlDbList as $dBSet) {
            $setId = $dBSet['set_id'];
            $setIds[] = $setId;
        }

        $targetClientId = [];
        $statSql = "select client_id from tbl_layout_rule group by client_id";
        foreach ($setIds as $setId) {
            try{
                echo "setId: $setId\n";
                $db = \PgActiveRecord::getDbByDbSetId($setId);
                $result = $db->createCommand($statSql)->queryAll();
                if(!empty($result)){
                    $targetClientId = array_merge($targetClientId, array_column($result,'client_id'));
                    self::info(json_encode("partial target client:".json_encode($result)));
                }
            }catch (\Throwable $e){
                $errmsg = "Error:" . $e->getMessage() . " | File:" . $e->getFile() . " | Line:" . $e->getLine();
                self::info($errmsg);
            } finally {
                if(!empty($db)){
                    \PgActiveRecord::releaseDbBySetId($setId);
                }
            }
        }

        self::info("all client ids:".json_encode($targetClientId));

        // 过滤掉内部client
        if(!empty($targetClientId)){
            $clientIdStr = implode(',', $targetClientId);
            $filterSql = "select client_id from tbl_client where client_id in ({$clientIdStr})";
            $accountDb = Yii::app()->account_base_db;
            $realResult = $accountDb->createCommand($filterSql)->queryAll();
            self::info("real client ids:".json_encode(arraycolumn($realResult,'client_id')));
        }
    }

    // debugphp -d 'xdebug.client_port=9004' /data/codebase/bob/php-crm/protected/yiic-test zbp TestAllSearchFields
    public function actionTestAllSearchFields(){
        $clientId=355745;
//        $clientId=351352;
        $show_field_key = 'customer.common.search.filter';
        $clientObject = \common\library\account\Client::getClient($clientId);
        $user = $clientObject->getMasterUser();
        \User::setLoginUser($user);
        $object_name = 'objCompany';
        $user = \User::getLoginUser();
        $clientId = $user->getClientId();

        $searchFieldService = new \common\library\object\field\service\SearchFieldService($clientId, $object_name);
        $searchFieldService->setOperateUser($user);
        $list = [];
        try{
            if ($object_name == ObjConstant::OBJ_PRODUCT_TRANSFER_PURCHASE) {
                $type = \common\library\object\object_define\Constant::OLD_TYPE_MAP[$object_name] ?? 0;
                $type && $list = $searchFieldService->noFieldAllSearchField($type, $user->getUserId(), 'list');
            } else {
                $list = $searchFieldService->allSearchField($show_field_key);
            }
        }catch(\Throwable $e){
            echo $e->getMessage().' | File:'.$e->getFile().' | Line:'.$e->getLine().PHP_EOL;
            echo $e->getTraceAsString();
        }


        echo json_encode($list);
    }

    // debugphp -d 'xdebug.client_port=9004' /data/codebase/bob/php-crm/protected/yiic-test zbp GetRecordPrivilege
    public function actionGetRecordPrivilege(){
        $clientId = 351352;
//        $objectBelongUserIds = [249521363];
        $operatorUserId = 249521363;
        $functionalId = 'crm.functional.order';
        $objectName = 'objOrder';
        $orderId = 3604786120;
        echo json_encode(\common\library\privilege_v3\Helper::getRecordFieldPrivilegeByObjectId($clientId, $orderId, $objectName , $operatorUserId, $functionalId));
//        echo json_encode(\common\library\privilege_v3\Helper::getRecordFieldPrivilegeByBelongUserId($clientId, $objectBelongUserIds, $operatorUserId, $functionalId));
//        $service = new \common\library\privilege_v3\object_service\RecordPrivilegeService($clientId);
//        $service->setUserId($operatorUserId);
//        $service->setFunctionalId($functionalId);
//        $service->setPrivilegeScopeUserIds([]);
//        echo json_encode($service->getFieldScope());
    }

    // debugphp -d 'xdebug.client_port=9004' /data/codebase/bob/php-crm/protected/yiic-test zbp TestCashCollectionFieldPrivilege
    public function actionTestCashCollectionFieldPrivilege(){
        $clientId = 351352;
        $service = new \common\library\privilege_v3\UserRolePrivilegeService($clientId);
        $res = $service->getUserRoleFieldPrivilege(*********, [1,2,3,4,5,6,7,8,9,10], PrivilegeConstants::FUNCTIONAL_CASH_COLLECTION);
        echo json_encode($res);
    }

    // debugphp -d 'xdebug.client_port=9004' /data/codebase/bob/php-crm/protected/yiic-test zbp TestJoinRawWhere
    public function actionTestJoinRawWhere(){
        $clientId = 351338;
//        $filter = new OrderFilter($clientId);
//        $filter->order_id = 123;
//        $recordFilter = new \common\library\oms\order_product\OrderProductFilter($clientId);
//        $recordFilter->enable_flag = 1;
//        $filter->initJoin();
//        $filter->innerJoin($recordFilter)
//            ->on($filter->getTableName(), 'order_id', $recordFilter->getTableName(), 'refer_id');
//        $filter->rawWhere(" and (scope_user_ids && Array[1,2,3]::bigint[] or scope_user_ids = '{}')");
//        $res = $filter->rawData();

//        $filter = new \common\library\product_v2\ProductFilter($clientId);
//        $filter->product_id = 123;
//        $recordFilter = new ProductSkuFilter($clientId);
//        $recordFilter->enable_flag = 1;
//        $filter->initJoin();
//        $filter->innerJoin($recordFilter)
//            ->on($filter->getTableName(), 'product_id', $recordFilter->getTableName(), 'product_id');
//        $filter->rawWhere(" and (scope_user_ids && Array[1,2,3]::bigint[] or scope_user_ids = '{}')");
//        $res = $filter->rawData();
//        $a = 1;

        $filter = new \common\library\oms\order_product\OrderProductFilter($clientId);
        $filter->order_id = 5122090852;
        $filter->enable_flag = 1;
        $oldOrderProductList = $filter->rawData();
        echo json_encode($oldOrderProductList);die;
    }

    // debugphp -d 'xdebug.client_port=9004' /data/codebase/bob/php-crm/protected/yiic-test zbp uniqueMap
    public function actionUniqueMap(){
        $map = [
            ObjConstant::OBJ_COMPANY =>[
                LayoutConstants::LAYOUT_PAGE_TYPE_LIST => [
                    "homepage", "fax", "tel_full_new", "address", "biz_type", "country_region", "annual_procurement", "scale_id", "origin_list", "product_group_ids", "short_name", "name", "order_time", "score", "timezone", "cus_tag", "serial_id", "trail_status", "group_id", "star", "intention_level", "category_ids", "image_list", "customer_count", "performance_order_count", "transaction_order_amount", "transaction_order_amount_avg", "transaction_order_first_amount", "success_opportunity_count", "ongoing_opportunity_count", "success_opportunity_amount_usd", "success_opportunity_amount_cny", "success_opportunity_amount_avg_cny", "success_opportunity_amount_avg_usd", "last_owner", "alibaba_user_id", "alibaba_last_owner", "last_edit_user", "create_user", "release_count", "ali_store_id", "archive_type", "customers", "relate_lead_ids", "swarm_ids", "tips_latest_update_time", "send_mail_time", "recent_follow_up_time", "receive_mail_time", "public_time", "private_time", "order_time", "next_follow_up_time", "lead_archive_time", "latest_write_follow_up_time", "latest_whatsapp_time", "latest_whatsapp_send_time", "latest_whatsapp_receive_time", "latest_whatsapp_business_send_time", "latest_whatsapp_business_receive_time", "latest_wechat_time", "latest_wechat_send_time", "latest_wechat_receive_time", "latest_send_ali_tm_time", "latest_reply_ali_trade_time", "latest_receive_ali_trade_time", "latest_receive_ali_tm_time", "latest_ins_time", "latest_ins_send_time", "latest_ins_receive_time", "latest_facebook_send_time", "latest_facebook_receive_time", "edm_time", "edit_time", "ciq_latest_update_time", "archive_time", "alibaba_recent_sync_time", "alibaba_first_sync_time", "deal_time", "public_type", "public_reason_id","homepage", "fax", "tel_full_new", "address", "biz_type", "country_region", "annual_procurement", "scale_id", "origin_list", "product_group_ids", "short_name", "name", "serial_id", "user_id", "last_owner", "archive_time", "trail_status", "order_time"
                ],
                LayoutConstants::LAYOUT_PAGE_TYPE_DETAIL => [
                    "name", "serial_id", "timezone", "user_id", "cus_tag", "score", "create_user", "archive_time", "last_edit_user", "edit_time", "last_owner", "alibaba_user_id", "alibaba_last_owner", "archive_type", "relate_lead_ids", "lead_archive_time", "main_lead_id", "ali_store_id", "swarm_ids", "alibaba_first_sync_time", "alibaba_recent_sync_time", "order_time", "recent_follow_up_time", "next_move_to_public_date", "private_time", "public_time", "release_count", "deal_time", "send_mail_time", "receive_mail_time", "edm_time", "latest_receive_ali_tm_time", "latest_send_ali_tm_time", "latest_receive_ali_trade_time", "latest_whatsapp_time", "latest_wechat_time", "latest_ins_time", "tips_latest_update_time", "latest_write_follow_up_time", "next_follow_up_time", "intention_level", "serial_id", "category_ids", "timezone", "image_list", "homepage", "fax", "tel_full_new", "address", "biz_type", "country_region", "annual_procurement", "scale_id", "origin_list", "product_group_ids", "short_name", "name", "cus_tag", "group_id", "trail_status", "star","name", "serial_id", "timezone", "cus_tag", "score", "create_user", "archive_time", "last_edit_user", "edit_time", "last_owner", "alibaba_user_id", "alibaba_last_owner", "archive_type", "relate_lead_ids", "lead_archive_time", "main_lead_id", "ali_store_id", "swarm_ids", "alibaba_first_sync_time", "alibaba_recent_sync_time", "order_time", "recent_follow_up_time", "private_time", "public_time", "public_type", "public_reason_id", "release_count", "deal_time", "send_mail_time", "receive_mail_time", "edm_time", "latest_receive_ali_tm_time", "latest_send_ali_tm_time", "latest_receive_ali_trade_time", "latest_whatsapp_time", "latest_wechat_time", "latest_ins_time", "tips_latest_update_time", "latest_write_follow_up_time", "next_follow_up_time", "intention_level", "serial_id", "category_ids", "timezone", "image_list", "homepage", "fax", "tel_full_new", "address", "biz_type", "country_region", "annual_procurement", "scale_id", "origin_list", "product_group_ids", "short_name", "name", "cus_tag", "group_id", "trail_status", "star"
                ],
                LayoutConstants::LAYOUT_PAGE_TYPE_EDIT => ["intention_level", "serial_id", "category_ids", "timezone", "image_list", "homepage", "fax", "tel_full_new", "address", "biz_type", "country_region", "annual_procurement", "scale_id", "origin_list", "product_group_ids", "short_name", "name", "cus_tag", "group_id", "trail_status", "star","intention_level", "serial_id", "category_ids", "timezone", "image_list", "homepage", "fax", "tel_full_new", "address", "biz_type", "country_region", "annual_procurement", "scale_id", "origin_list", "product_group_ids", "short_name", "name", "cus_tag", "group_id", "trail_status", "star"
                ]
            ],
            ObjConstant::OBJ_CUSTOMER=>[
                LayoutConstants::LAYOUT_PAGE_TYPE_LIST=>[
                    "tel_list", "contact", "post_grade", "post", "birth", "name", "remark", "email","main_customer_flag", "tel_list", "gender", "growth_level", "contact", "post_grade", "post", "birth", "name", "remark", "email"
                ],
                LayoutConstants::LAYOUT_PAGE_TYPE_DETAIL => [
                    "tel_list", "gender", "reach_status_time", "image_list", "suspected_invalid_email_flag", "reach_status", "growth_level", "forbidden_flag", "main_customer_flag", "contact", "post_grade", "post", "birth", "name", "remark", "email","tel_list", "gender", "reach_status_time", "image_list", "suspected_invalid_email_flag", "reach_status", "growth_level", "forbidden_flag", "main_customer_flag", "contact", "post_grade", "post", "birth", "name", "remark", "email"
                ],
                LayoutConstants::LAYOUT_PAGE_TYPE_EDIT => [
                    "tel_list", "gender", "reach_status_time", "image_list", "suspected_invalid_email_flag", "reach_status", "growth_level", "forbidden_flag", "main_customer_flag", "contact", "post_grade", "post", "birth", "name", "remark", "email","tel_list", "gender", "reach_status_time", "image_list", "suspected_invalid_email_flag", "reach_status", "growth_level", "forbidden_flag", "main_customer_flag", "contact", "post_grade", "post", "birth", "name", "remark", "email"
                ]
            ]
        ];

        foreach($map as $obj => $itemMap){
            $commonFields = [];
            foreach($itemMap as $pageType => $fields){
                $fields = array_unique($fields);
                sort($fields);
                if(empty($commonFields)){
                    $commonFields = $fields;
                }else{
                    $commonFields = array_intersect($fields, $commonFields);
                }
//                self::info('object_name:'.$obj.' | page_type:'.$pageType.' | fields:'.json_encode($fields));
                $itemMap[$pageType] = $fields;
            }

            self::info('common_fields:'.json_encode(array_values($commonFields)));

            $diffFields = [];
            foreach($itemMap as $pageType => $fields){
                $diffFields[$pageType] = array_values(array_diff($fields, $commonFields));
            }

            self::info('diff_fields:'.json_encode($diffFields));
        }
    }

    // debugphp -d 'xdebug.client_port=9004' /data/codebase/bob/php-crm/protected/yiic-test zbp BuildFieldGroupSettings
    public function actionBuildFieldGroupSettings($clientId){
        $layoutFilter = new \common\library\layout\LayoutFilter($clientId);
        $result = $layoutFilter->rawData();
        foreach($result as $datum){
            $layout = new \common\library\layout\Layout($clientId, $datum['layout_id']);
            $fieldGroups = [];
            $defaultGroupName = 'field_group_other_company_info';
            $defaultSubObjectGroupName = 'field_group_customer_contact_info';
            $layoutConfig = $layout->layout_config;
            foreach($layoutConfig['field_configs'] as $groupName => $fieldInfos){
                if(!isset($fieldGroups[$groupName])){
                    $fieldGroups[$groupName] = [];
                }
                $fieldGroups[$groupName] = ['__nid'=>$groupName,'is_default'=>in_array($groupName, [$defaultGroupName, $defaultSubObjectGroupName]), 'fields' => $fieldInfos];

                if( $defaultSubObjectGroupName == $groupName ){
                    $fieldGroups[$groupName]['object_name'] = 'objCustomer';
                }
            }
            $layout->field_group_settings = array_values($fieldGroups);
            $layout->update();
        }
    }

    // debugphp -d 'xdebug.client_port=9004' /data/codebase/bob/php-crm/protected/yiic-test zbp TestBatchCalculateFunctionFields
    public function actionTestBatchCalculateFunctionFields()
    {
        $clientId = 364176;

        $clientObject = \common\library\account\Client::getClient($clientId);
        $user = $clientObject->getMasterUser();
        \User::setLoginUser($user);

        $mainObjName = 'objOrder';
        $orderData = [
            ["order_id"=>"order_id_1","company_id"=>**********,"**********"=>100,"currency"=>"USD"],
            ["order_id"=>"order_id_2","company_id"=>**********,"**********"=>200,"currency"=>"USD"],
        ];

        $orderProductData = [
            ["refer_id"=>"order_id_1","count"=>5,"cost_with_tax"=>50,"sku_id"=>**********,"unit_price"=>75,"other_cost"=>8,"id"=>"order_product_id_1"],
            ["refer_id"=>"order_id_1","count"=>9,"cost_with_tax"=>60,"sku_id"=>**********,"unit_price"=>79,"other_cost"=>9,"id"=>"order_product_id_2"],
            ["refer_id"=>"order_id_2","count"=>5,"cost_with_tax"=>50,"sku_id"=>**********,"unit_price"=>75,"other_cost"=>8,"id"=>"order_product_id_3"],
            ["refer_id"=>"order_id_2","count"=>9,"cost_with_tax"=>60,"sku_id"=>**********,"unit_price"=>79,"other_cost"=>9,"id"=>"order_product_id_4"]
        ];

        $calculator = new \common\library\object\field\updator\calculator\BatchSyncCalculator($clientId, $mainObjName);
        $calculator->addData('objOrder', $orderData);
        $calculator->addData('objOrderProduct', $orderProductData);
        $calculator->setMergeFormulaDefaultValueField(true);
//        $modifyFields = [
//            'objOrder' => ['**********'],
//            'objOrderProduct' => ['cost_with_tax', 'unit_price']
//        ];
//        $calculator->setObjNameToModifiedFieldsMap($modifyFields);
        $calculator->calculate();
        echo json_encode($calculator->getCalculatedData('objOrder'));
        echo json_encode($calculator->getCalculatedData('objOrderProduct'));
    }

    // debugphp -d 'xdebug.client_port=9004' /data/codebase/bob/php-crm/protected/yiic-test zbp TestBatchCalculateFunctionFields2
    public function actionTestBatchCalculateFunctionFields2()
    {
        $clientId = 364176;

        $clientObject = \common\library\account\Client::getClient($clientId);
        $user = $clientObject->getMasterUser();
        \User::setLoginUser($user);

//        $mainObjName = 'objOrder';
        $mainObjName = 'objOrderProduct';
//        $orderData = [
//            ["order_id"=>"**********","company_id"=>**********,"**********"=>100,"currency"=>"CNY"],
//        ];

        $orderProductData = [
            ["refer_id"=>"**********","count"=>5,"cost_with_tax"=>50,"sku_id"=>**********,"unit_price"=>75,"other_cost"=>8,"id"=>"**********","**********"=>10],
            ["refer_id"=>"**********","count"=>9,"cost_with_tax"=>60,"sku_id"=>**********,"unit_price"=>79,"other_cost"=>9,"id"=>"**********","**********"=>20],
        ];

        $calculator = new \common\library\object\field\updator\calculator\BatchSyncCalculator($clientId, $mainObjName);
//        $calculator->addData('objOrder', $orderData);
        $calculator->addData('objOrderProduct', $orderProductData);
//        $calculator->setMergeFormulaDefaultValueField(true);
//        $modifyFields = [
//            'objOrder' => ['**********'],
//            'objOrderProduct' => ['cost_with_tax', 'unit_price']
//        ];
//        $calculator->setObjNameToModifiedFieldsMap($modifyFields);
        $calculator->calculate();
//        echo json_encode($calculator->getCalculatedData('objOrder'));
        echo json_encode($calculator->getCalculatedData('objOrderProduct'));
    }

    public function actionGetLayoutConfig(){
        $clientId = 401703;
        $clientObject = \common\library\account\Client::getClient($clientId);
        $user = $clientObject->getMasterUser();
        \User::setLoginUser($user);
        $object_name='objCompany';
        $page_type = 'drawer';
        $device = 'web';
        $params = '{"object_id":**************}';
        $layoutDecorateClass = \common\library\layout\init\Factory::make($clientId, $object_name);

        $businessParams['page_type'] = $page_type;
        $businessParams['device'] = $device;
        $businessFilter = \common\library\object\common\BusinessTypeFilter::build($clientId, $object_name);
        $businessParams = $businessParams + (json_decode($params, true) ?: []);
        $businessType = $businessFilter->getBusinessType($businessParams);
        $api = new \common\library\layout\LayoutApi($clientId, $object_name, $page_type, \Constants::CLIENT_TYPE_WEB, $businessType);
        $layout = $api->getLayout();

        //防止默认布局没有
        if ($layout->isNew()) {
            $layoutDecorateClass->init();
            $layout = new \common\library\layout\Layout($clientId);
            $layout->loadBy($object_name, $page_type, $device, \common\library\layout\LayoutConstants::LAYOUT_DEFAULT_TRUE);
        }

    }

    // debugphp -d 'xdebug.client_port=9004' /data/codebase/bob/php-crm/protected/yiic-test zbp TestInitLayout
    public function actionTestInitLayout(){
        LayoutHelper::initLayout(423981);
    }

    // debugphp -d 'xdebug.client_port=9004' /data/codebase/bob/php-crm/protected/yiic-test zbp GetRoleCache
    public function actionGetRoleCache(){
        $clientId = 14119;
        $userPrivilegeService = new \common\library\privilege_v3\UserPrivilegeService($clientId);

        // 获取client的所有角色
        $cacheKey = $userPrivilegeService->getCacheKey(PrivilegeCache::CACHE_TYPE_OF_USER_ROLES);
        $r1 = $userPrivilegeService->getCache('redis')->hgetall($cacheKey);
        echo $cacheKey.PHP_EOL;
        echo json_encode($r1).PHP_EOL.PHP_EOL;

        $cacheKey = $userPrivilegeService->getCacheKey(PrivilegeCache::CACHE_TYPE_OF_ROLE_USERS);
        $r2 = $userPrivilegeService->getCache('redis')->hgetall($cacheKey);
        echo $cacheKey.PHP_EOL;
        echo json_encode($r2).PHP_EOL.PHP_EOL;

        $cacheKey = $userPrivilegeService->getCacheKey(PrivilegeCache::CACHE_TYPE_OF_SYSTEM_USERS);
        $r3 = $userPrivilegeService->getCache('redis')->hgetall($cacheKey);
        echo $cacheKey.PHP_EOL;
        echo json_encode($r3).PHP_EOL.PHP_EOL;

        $cacheKey = 'v3:crm:test_zbp';
        $data = ['10111'=>['biz_type_1'=>111,'biz_type_2'=>222],'10121'=>['biz_type_1'=>111,'biz_type_2'=>222]];
        $userPrivilegeService->getCache()->hmset($cacheKey, $data);
        $r4 = $userPrivilegeService->getCache('redis')->hgetall($cacheKey);
        echo $cacheKey.PHP_EOL;
        var_dump($r4);
        echo json_encode($r4).PHP_EOL.PHP_EOL;
    }

    // debugphp -d 'xdebug.client_port=9004' /data/codebase/bob/php-crm/protected/yiic-test zbp TestBatchFunctionFieldVal
    public function actionTestBatchFunctionFieldVal(){
        $clientId = 14119;
        $clientObject = \common\library\account\Client::getClient($clientId);
        $user = $clientObject->getMasterUser();
        \User::setLoginUser($user);
        $data = '{"objInquiryFeedback":[{"inquiry_feedback_id":"363dffd6","purchase_quote":"","supplier_id":"","purchase_quote_cny":"","inquiry_collaboration_id":**********,"inquiry_product_id":**********},{"inquiry_feedback_id":"ea3cae79","purchase_quote":"","supplier_id":"","purchase_quote_cny":"","inquiry_collaboration_id":**********,"inquiry_product_id":**********}]}';
//        $data = '{"objInquiryFeedback":[{"inquiry_feedback_id":"4e372107","purchase_quote_cny":"","inquiry_collaboration_id":**************},{"inquiry_feedback_id":"8018ac5e","purchase_quote_cny":"","inquiry_collaboration_id":**************},{"inquiry_feedback_id":"12aef9e6","purchase_quote_cny":"","inquiry_collaboration_id":**************}]}';
        $modified_fields = '[]';
        $user = \User::getLoginUser();
        $clientId = $user->getClientId();
        $data = json_decode($data, true);
        $modified_fields = json_decode($modified_fields, true);
        $mergeFormulaDefaultValueField = true;
        $list = \common\library\object\field\service\FunctionFieldService::batchFunctionFieldValue($clientId, $data, $modified_fields, $mergeFormulaDefaultValueField);

        echo json_encode($list);
    }

    // debugphp -d 'xdebug.client_port=9004' /data/codebase/bob/php-crm/protected/yiic-test zbp TestTranslate
    public function actionTestTranslate(){
        $clientId = 364176;
        $clientObject = \common\library\account\Client::getClient($clientId);
        $user = $clientObject->getMasterUser();
        \User::setLoginUser($user);
        //增加功能权限
        PrivilegeService::getInstance($clientId)->assignFunction([
            PrivilegeConstants::FUNCTIONAL_TRANSLATE_SETTING,
        ]);
        \common\library\account\Helper::setClientSettingValue($clientId, \common\library\account\Client::SETTING_KEY_CLIENT_TRANSLATE_SWITCH_MAP, json_encode(['en' => 1]));
        $module = 4;
        $translateType = 2001;
        $service = new TranslateCacheService($clientId, TranslateConstants::SOURCE_TYPE_FRONT);
//        $service->refreshSystem();
//        $service->refreshByType($module, $translateType);
        $list = $service->setModule((array)$module)->setType((array)$translateType)->list();
        echo json_encode($list);

    }

    // debugphp -d 'xdebug.client_port=9004' /data/codebase/bob/php-crm/protected/yiic-test zbp TestInitClientLayout
    public function actionTestInitClientLayout(){
        $clientId = 364176;
        $privilegeService = new PrivilegeService($clientId);
        echo json_encode($privilegeService->initClient('okki_pro', *********, false));

    }


    // debugphp -d 'xdebug.client_port=9004' /data/codebase/bob/php-crm/protected/yiic-test zbp GetSubOriginList
    public function actionGetSubOriginList(){
        $clientId = 351352;
        LayoutHelper::initLayout($clientId, true);
    }

    // debugphp -d 'xdebug.client_port=9004' /data/codebase/bob/php-crm/protected/yiic-test zbp TestRawJoin
    public function actionTestRawJoin(){
        $filter = new \common\library\customer_v3\company\orm\CompanyFilter(14119);
        $filter->setAlias('tc');
        $filter->setRawJoin('inner join tbl_relate_company_swarm_public as rcs0 on tc.company_id=rcs0.company_id and rcs0.swarm_id=38778370388140');
        $filter->limit(10, 1);
        $filter->order('company_id', 'desc');
        $grammar = $filter->getCompileSqlGrammar();
        var_dump($grammar->getSql());
        // result sql: SELECT * FROM  tbl_company as tc inner join tbl_relate_company_swarm_public as rcs0 on tc.company_id=rcs0.company_id and rcs0.swarm_id=38778370388140  WHERE tc.client_id=:where_client_id_3 AND tc.is_archive=:where_is_archive_4   ORDER BY tc."company_id" desc nulls last  LIMIT 10
        $filter->rawData();
        echo $filter->count();
    }

    // debugphp -d 'xdebug.client_port=9004' /data/codebase/bob/php-crm/protected/yiic-test zbp TestPaymentInfo
    public function  actionTestPaymentInfo(){
//        \User::setLoginUserById(55431235);      // 主账号：55431235，问题账号：56299535
//        $clientId = 57460;
//        $invoice_id=44876474551748;

        $clientId = 14119;
        \User::setLoginUserById(11858714);
        $invoice_id=5224673047;

        $paymentInvoice = new \common\library\oms\payment_invoice\PaymentInvoice($clientId, $invoice_id);
        $paymentInvoice->canRead();
        $paymentInvoice->approvalFieldPrivilege();
        $paymentInvoice->getFormatter()->appInfoSetting();
        echo json_encode($paymentInvoice->getAttributes());
    }

    // debugphp -d 'xdebug.client_port=9004' /data/codebase/bob/php-crm/protected/yiic-test zbp TestMarketingAuto
    public function actionTestMarketingAuto($client_id = 0)
    {
        $adminUserId = \common\library\privilege_v3\PrivilegeService::getInstance(14826)->getAdminUserId();
        \User::setLoginUserById($adminUserId);
        $companyList = new \common\library\customer_v3\company\list\CompanyList($adminUserId);

        $customerList = new \common\library\customer_v3\customer\CustomerList(14826);
        $customerList->setAlias('B');

        [$customerWhere,$customerParams] = $customerList->buildParams(true, false);

        $fields = ['B.customer_id', 'B.company_id', 'B.email', 'B.name', 'B.post', 'A.name as company_name'];

        $companyList->setAlias('A');
        $companyList->joinCustomer('B', $customerWhere, $customerParams);
        $companyList->setFields($fields);

        var_dump($companyList->find());

//        $grammar = $companyList->getFilter()->getCompileSqlGrammar();
//        echo $grammar->getSql();


    }

    // debugphp -d 'xdebug.client_port=9004' /data/codebase/bob/php-crm/protected/yiic-test zbp TestPrivilegeJoin
    public function actionTestPrivilegeJoin(){
        $clientId = 351352;
        \User::setLoginUserById(*********);
        $order = new \common\library\oms\order\Order($clientId, 5262894797);
        $order->update();
    }

    // debugphp -d 'xdebug.client_port=9004' /data/codebase/bob/php-crm/protected/yiic-test zbp TestPrivilegeJoin2
    public function actionTestPrivilegeJoin2(){
        $clientId = 423981;
        // 初始化布局
        LayoutHelper::initLayout($clientId, true);
    }

    // debugphp -d 'xdebug.client_port=9004' /data/codebase/bob/php-crm/protected/yiic-test zbp TestCusTagFormatter
    public function actionTestCusTagFormatter(){
        $clientId = 423981;
        \User::setLoginUserById(*********);
        $companyFilter = new \common\library\customer_v3\company\orm\CompanyFilter($clientId);
        $companyFilter->company_id = 5265165270;
        $batch = $companyFilter->find();
        $batch->getFormatter()->displayFields(["company_id","user_id","duplicate_flag","ali_store_id","name","short_name","serial_id","pool_id","trail_status","country","province","city","score","tag","last_trail","order_time","country_region","tel_full_new","group_id","public_reason_id","origin_list","last_owner","create_user","last_edit_user","main_customer","client_tag_list","last_trail_id","last_remark_trail_id"]);
        $batch->getFormatter()->displayListLayoutFieldInfo('private');
        $res = $batch->getAttributes();
        echo json_encode($res);
        var_dump(
            $res[0]['cus_tag_info']
        );
    }

    public function actionDisableField(){
//        $sql1 = "update tbl_field set enable_flag=0 where client_id=14833 and field='24605767872524'";
//        $sql2 = "update tbl_custom_field set enable_flag=0 where client_id=14833 and id=24605767872524";
//        $db = \PgActiveRecord::getDbByClientId(14833);
//        $mydb = \ProjectActiveRecord::getDbByClientId(14833);
//        echo $db->createCommand($sql1)->execute();
//        echo $mydb->createCommand($sql2)->execute();
        $clientId = 14781;
        $objectId = 3216731424;
        $objectName = 'objCompany';
        $viewUserId = *********;
        $functionId = PrivilegeConstants::FUNCTIONAL_CUSTOMER;
        $objectScene = 'view';
        $privilegeFieldStats = \common\library\privilege_v3\Helper::getRecordFieldPrivilegeByObjectId($clientId, $objectId, $objectName, $viewUserId, $functionId, $objectScene);
        echo json_encode($privilegeFieldStats);
    }
}