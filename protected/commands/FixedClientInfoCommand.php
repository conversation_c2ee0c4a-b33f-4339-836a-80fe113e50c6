<?php

use common\library\account\Client;
use common\library\ciq\Constant;
use common\library\lead\Lead;
use common\library\privilege_v3\ClientPrivilegeService;
use common\library\privilege_v3\Helper;
use common\library\privilege_v3\PrivilegeConstants;
use common\library\privilege_v3\PrivilegeService;
use common\models\base\PrivilegeRoleAccess as PrivilegeRoleAccessModel;
//use common\library\lead\Lead;

class FixedClientInfoCommand extends CrontabCommand
{


    public function actionWecom($clientId,$greyNum=null)
    {
        $dbSetId = 0;
        if (\Yii::app()->params['env'] == 'test')
            $dbSetId = 3;
//        $clientId  = explode(',', $clientId);
        $greyNum = explode(',', $greyNum);
        $clientListData = $this->getClientList($clientId,false,null,null, 0, 0, $greyNum, $dbSetId,null);

        echo 'client count:'.count($clientListData);
        LogUtil::info('start');
        $num = 0;
        foreach ($clientListData as $clientIdInfo)
        {

            $clientId = $clientIdInfo['client_id'];

            $privilegeService = PrivilegeService::getInstance($clientId);
            $mainSystemId = $privilegeService->getMainSystemId();
            if(!in_array($mainSystemId, [
                PrivilegeConstants::OKKI_PRO_SYSTEM_ID,
                PrivilegeConstants::CRM_SMART_SYSTEM_ID,
                PrivilegeConstants::CRM_PRO_SYSTEM_ID,
                PrivilegeConstants::CRM_LITE_SYSTEM_ID,
                PrivilegeConstants::CRM_LITE_2021_ID,
                PrivilegeConstants::CRM_LITE_2023_ID,
            ])){
                continue;
            }


            $functional = [
                \common\library\privilege_v3\PrivilegeConstants::FUNCTIONAL_WECOM_SETTING,
            ];

            PrivilegeService::getInstance($clientId)->assignFunction($functional);

            $num++;
            echo 'actionWecom num:'.$num.'---client_id:'.$clientId."\n";
            LogUtil::info('actionWecom num:'.$num.'---client_id:'.$clientId);
        }
        LogUtil::info('over');
    }


    /**
     * okki_pro 添加 crm.functional.facebooklead
     * @param $clientId
     * @param null $greyNum
     */
    public function actionOkkiproAssignFacebooklead($clientId,$greyNum=null)
    {
        $dbSetId = 0;
        if (\Yii::app()->params['env'] == 'test')
            $dbSetId = 3;
        $clientId  = explode(',', $clientId);
        $greyNum = explode(',', $greyNum);
        $clientListData = $this->getClientList($clientId,false,null,null, 0, 0, $greyNum, $dbSetId,null);

        \LogUtil::info('start');
        $num = 1;
        foreach ($clientListData as $clientIdInfo)
        {
            $clientId = $clientIdInfo['client_id'];

            $privilegeService = PrivilegeService::getInstance($clientId);
            $mainSystemId = $privilegeService->getMainSystemId();

            if (!in_array($mainSystemId, [PrivilegeConstants::OKKI_PRO_SYSTEM_ID])) {
                continue;
            }

            $num++;
            $userId = Client::getClient($clientId)->getMasterUser()->getUserId();
            if(!$userId){
                return false;
            }
            \User::setLoginUserById($userId);

            \LogUtil::info('actionOkkiproAssignFacebooklead num:'.$num.'---client_id:'.$clientId.'---user_id:'.$userId);
            echo 'actionOkkiproAssignFacebooklead num:'.$num.'---client_id:'.$clientId.'---user_id:'.$userId."\n";;
            $functional = [
                \common\library\privilege_v3\PrivilegeConstants::FUNCTIONAL_FACEBOOK_LEAD,
            ];
            PrivilegeService::getInstance($clientId)->assignFunction($functional);
        }
        \LogUtil::info('over');
    }



    /**
     *  lite2023版本添加MKT权限
     * @param $clientId
     * @param null $greyNum
     */
    public function actionLite2023AssignMkt($clientId,$greyNum=null)
    {
        $dbSetId = 0;
        if (\Yii::app()->params['env'] == 'test')
            $dbSetId = 3;
        $clientId  = explode(',', $clientId);
        $greyNum = explode(',', $greyNum);
        $clientListData = $this->getClientList($clientId,false,null,null, 0, 0, $greyNum, $dbSetId,null);

        LogUtil::info('start');
        $num = 1;
        foreach ($clientListData as $clientIdInfo)
        {
            $clientId = $clientIdInfo['client_id'];

            $num++;
            $this->actionAssignFunctionalForLite2023AssignMkt($clientId,$num);
        }
        LogUtil::info('over');
    }



    public function actionAssignFunctionalForLite2023AssignMkt($clientId, $num)
    {

        $userId = Client::getClient($clientId)->getMasterUser()->getUserId();
        if(!$userId){
            return false;
        }
        \User::setLoginUserById($userId);

        self::info('actionAssignFunctionalForLite2023AssignMkt num:'.$num.'---client_id:'.$clientId.'---user_id:'.$userId);
        $functional = [
            \common\library\privilege_v3\PrivilegeConstants::FUNCTIONAL_MARKETING_ADS,
            \common\library\privilege_v3\PrivilegeConstants::FUNCTIONAL_MARKETING_CONVERSATION,
        ];
        PrivilegeService::getInstance($clientId)->assignFunction($functional);
    }


    public function actionSmartLeads($clientId,$greyNum=null)
    {
        $dbSetId = 0;
        if (\Yii::app()->params['env'] == 'test')
            $dbSetId = 3;
        $clientId  = explode(',', $clientId);
        $greyNum = explode(',', $greyNum);
        $clientListData = $this->getClientList($clientId,false,null,null, 0, 0, $greyNum, $dbSetId,null);

        LogUtil::info('start');
        $num = 0;
        foreach ($clientListData as $clientIdInfo)
        {

            $clientId = $clientIdInfo['client_id'];
            $clientIdInfo = \common\library\account\Client::getClient($clientId);

            echo '1';

            if($clientIdInfo['valid_to']<=date("Y-m-d H:i:s",time())){    //判断是否有效用户
                continue;
            }

            echo '2';
            $privilegeService = PrivilegeService::getInstance($clientId);
            $mainSystemId = $privilegeService->getMainSystemId();
            if (!in_array($mainSystemId, [PrivilegeConstants::CRM_SMART_SYSTEM_ID,PrivilegeConstants::CRM_LITE_2021_ID,PrivilegeConstants::CRM_LITE_SYSTEM_ID])) {
                continue;
            }

            echo '3';
            if (in_array($mainSystemId, [PrivilegeConstants::CRM_LITE_2021_ID,PrivilegeConstants::CRM_LITE_SYSTEM_ID])) {
                if($clientIdInfo['valid_to']>'2024-03-31 23:59:59'){    // lite 升级2024年3月31日前的到约用户
                    continue;
                }
            }
            echo '4';
            $hasFunctional = [
                \common\library\privilege_v3\PrivilegeConstants::FUNCTIONAL_OKKI_LEADS_BASE
            ];
            $existsFunctionalIds = Helper::hasFunctional($clientId, $hasFunctional);
            if ($existsFunctionalIds) {
                continue;
            }

            echo '5';
            $moduleId = 'okki_leads';
            $modulePrivilege = $privilegeService->getModulePrivilege();
            $modulePrivilege->addModule($moduleId);
            $moduleIds = $modulePrivilege->getModuleIds();

            \PgActiveRecord::setConnection(\PgActiveRecord::getDbByClientId($clientId));
            \ProjectActiveRecord::setConnection(\ProjectActiveRecord::getDbByClientId($clientId));
            $privilegeService->initClient($mainSystemId, $privilegeService->getAdminUserId(), false, true, $moduleIds);
            \LogUtil::info("refresh for client:$clientId current:$mainSystemId module: " . implode(',', $moduleIds));
            \PgActiveRecord::resetConnection();
            \ProjectActiveRecord::resetConnection();

            $num++;
            echo 'actionSmartLeads num:'.$num.'---client_id:'.$clientId."\n";
            LogUtil::info('actionSmartLeads num:'.$num.'---client_id:'.$clientId);
        }
        LogUtil::info('over');
    }


    // 统计lite2023使用网站聊天的情况
    public function actionLite2023UseMKT($clientId,$greyNum=null)
    {
        $dbSetId = 0;
        if (\Yii::app()->params['env'] == 'test')
            $dbSetId = 3;
        //$clientId  = explode(',', $clientId);
        $greyNum = explode(',', $greyNum);
        $clientListData = $this->getClientList($clientId,false,null,null, 0, 0, $greyNum, $dbSetId,null);

        LogUtil::info('start');
        $num = 0;
        foreach ($clientListData as $clientIdInfo)
        {

            $clientId = $clientIdInfo['client_id'];
            if($clientIdInfo['valid_to']<=date("Y-m-d H:i:s",time())){    //判断是否有效用户
                continue;
            }

            $privilegeService = PrivilegeService::getInstance($clientId);
            $mainSystemId = $privilegeService->getMainSystemId();
            if (!in_array($mainSystemId, [PrivilegeConstants::CRM_LITE_2023_ID])) {
                continue;
            }

            $db = \Yii::app()->getDb();
            $sql = "select client_id from v4_admin.tbl_google_ga_site where enable_flag=1 and  client_id={$clientId} ";
            $siteData = $db->createCommand($sql)->queryAll(true);
            if (!empty($siteData)) {
                continue;
            }

            $num++;
            echo 'Lite2023UseMKT num:'.$num.'---client_id:'.$clientId."\n";
            LogUtil::info('Lite2023UseMKT num:'.$num.'---client_id:'.$clientId);
        }
        LogUtil::info('over');
    }

    /**
     * leads 2.0的菜单
     * @param $clientId
     * @param null $greyNum
     */
    public function actionLeadsMenuV2($clientId,$greyNum=null)
    {
        $dbSetId = 0;
        if (\Yii::app()->params['env'] == 'test')
            $dbSetId = 3;
        $clientId  = explode(',', $clientId);
        $greyNum = explode(',', $greyNum);
        $clientListData = $this->getClientList($clientId,false,null,null, 0, 0, $greyNum, $dbSetId,null);

        LogUtil::info('start');
        $num = 1;
        foreach ($clientListData as $clientIdInfo)
        {
            $clientId = $clientIdInfo['client_id'];

            $num++;
            $this->actionAssignFunctionalForLeadsMenuV2($clientId,$num);
        }
        LogUtil::info('over');
    }



    public function actionAssignFunctionalForLeadsMenuV2($clientId, $num)
    {
        $hasFunctional = [
            \common\library\privilege_v3\PrivilegeConstants::FUNCTIONAL_OKKI_LEADS_BASE
        ];
        $existsFunctionalIds = Helper::hasFunctional($clientId, $hasFunctional);
        if (!$existsFunctionalIds) {
            return false;
        }
        $userId = Client::getClient($clientId)->getMasterUser()->getUserId();
        if(!$userId){
            return false;
        }
        \User::setLoginUserById($userId);

        self::info('actionAssignFunctionalForLeadsMenuV2 num:'.$num.'---client_id:'.$clientId.'---user_id:'.$userId);
        $functional = [
            \common\library\privilege_v3\PrivilegeConstants::FUNCTIONAL_LEADS_MENU_V2,
        ];
        PrivilegeService::getInstance($clientId)->assignFunction($functional);

        // 升级后置为0   为下一批放量通知做准备
        $client = \common\library\account\Client::getClient($clientId);
        $client->setExtentAttributes([
            \common\library\account\Client::EXTERNAL_KEY_LEADS_V2_MSG_SWITCH => '0',
        ]);
        $client->saveExtentAttributes();
    }


    /**
     * leads 2.0的菜单  leads2.0降级为leads1.0
     * @param $clientId
     * @param null $greyNum
     */
    public function actionRemoveLeadsMenuV2($clientId,$greyNum=null)
    {
        $dbSetId = 0;
        if (\Yii::app()->params['env'] == 'test')
            $dbSetId = 3;
        $greyNum = explode(',', $greyNum);
        $clientListData = $this->getClientList($clientId,false,null,null, 0, 0, $greyNum, $dbSetId,null);

        LogUtil::info('start');
        $num = 1;
        foreach ($clientListData as $clientIdInfo)
        {
            $clientId = $clientIdInfo['client_id'];

            $num++;
            $this->actionRemoveFunctionalForLeadsMenuV2($clientId,$num);
        }
        LogUtil::info('over');
    }



    public function actionRemoveFunctionalForLeadsMenuV2($clientId, $num)
    {
        $userId = Client::getClient($clientId)->getMasterUser()->getUserId();
        if(!$userId){
            return false;
        }
        \User::setLoginUserById($userId);

        self::info('actionAssignFunctionalForLeadsMenuV2 num:'.$num.'---client_id:'.$clientId.'---user_id:'.$userId);
        $functional = [
            \common\library\privilege_v3\PrivilegeConstants::FUNCTIONAL_LEADS_MENU_V2,
        ];
        PrivilegeService::getInstance($clientId)->removeFunction($functional);
    }




    // Leads2.0（10%放量）前通知条
    public function actionLeadsV2Msg($clientId,$greyNum=null)
    {
        $dbSetId = 0;
        if (\Yii::app()->params['env'] == 'test')
            $dbSetId = 3;
        $clientId  = explode(',', $clientId);
        $greyNum = explode(',', $greyNum);
        $clientListData = $this->getClientList($clientId,false,null,null, 0, 0, $greyNum, $dbSetId,null);

        LogUtil::info('start');
        $num = 1;
        foreach ($clientListData as $clientIdInfo)
        {
            $clientId = $clientIdInfo['client_id'];
            if($clientIdInfo['valid_to']<=date("Y-m-d H:i:s",time())){    //判断是否有效用户
                continue;
            }

            $num++;
            $this->actionAssignLeadsV2Msg($clientId);
            LogUtil::info('num:'.$num.'---client_id:'.$clientId);
        }
        LogUtil::info('over');
    }


    public function actionAssignLeadsV2Msg($clientId)
    {

        $hasFunctional = [
            \common\library\privilege_v3\PrivilegeConstants::FUNCTIONAL_OKKI_LEADS_BASE
        ];
        $existsFunctionalIds = Helper::hasFunctional($clientId, $hasFunctional);
        if (!$existsFunctionalIds) {
            return false;
        }

        $client = \common\library\account\Client::getClient($clientId);
        $salesGuideSwitch = $client->getExtentAttributes([Client::EXTERNAL_KEY_LEADS_V2_MSG_SWITCH])[Client::EXTERNAL_KEY_LEADS_V2_MSG_SWITCH] ?? 0;
        if ($salesGuideSwitch) {
            return false;
        }

        // 配置facebook总开关
        $client->setExtentAttributes([
            \common\library\account\Client::EXTERNAL_KEY_LEADS_V2_MSG_SWITCH => '1',
        ]);
        $client->saveExtentAttributes();

    }
    /**
     * lite2023 添加 群发权限
     * @param $clientId
     * @param null $greyNum
     */
    public function actionLite2023WhatsappChatGroup($clientId,$greyNum=null)
    {
        $dbSetId = 0;
        if (\Yii::app()->params['env'] == 'test')
            $dbSetId = 3;
        $greyNum = explode(',', $greyNum);
        $clientListData = $this->getClientList($clientId,false,null,null, 0, 0, $greyNum, $dbSetId,null);

        LogUtil::info('start');
        $num = 1;
        foreach ($clientListData as $clientIdInfo)
        {
            $clientId = $clientIdInfo['client_id'];

            $privilegeService = PrivilegeService::getInstance($clientId);
            $mainSystemId = $privilegeService->getMainSystemId();

            $systemMsg = "clientId[{$clientId}|] -- mainSystemId[{$mainSystemId}]";
            if (!in_array($mainSystemId, [PrivilegeConstants::CRM_LITE_2023_ID])) {
                //self::info($systemMsg . " -- 跳过");
                continue;
            }

            $num++;
            $this->actionAssignFunctionalForWhatsappChatGroup($clientId,$num);
        }
        LogUtil::info('over');
    }



    public function actionAssignFunctionalForWhatsappChatGroup($clientId, $num)
    {
        $userId = Client::getClient($clientId)->getMasterUser()->getUserId();
        if(!$userId){
            return false;
        }
        \User::setLoginUserById($userId);

        self::info('actionAssignFunctionalForWhatsappChatGroup num:'.$num.'---client_id:'.$clientId.'---user_id:'.$userId);
        $functional = [
            \common\library\privilege_v3\PrivilegeConstants::FUNCTIONAL_WHATSAPP_CHAT_GROUP,
        ];
        PrivilegeService::getInstance($clientId)->assignFunction($functional);
    }




    // 给尾数为1的用户授权  部门管理员默认授权下属话术
    public function actionExecuteWhatsappChatGroup($clientId,$greyNum=null)
    {
        $dbSetId = 0;
        if (\Yii::app()->params['env'] == 'test')
            $dbSetId = 3;
        $greyNum = explode(',', $greyNum);
        $clientListData = $this->getClientList($clientId,false,null,null, 0, 0, $greyNum, $dbSetId,null);

        LogUtil::info('start');
        $num = 1;
        foreach ($clientListData as $clientIdInfo)
        {
            $clientId = $clientIdInfo['client_id'];

            $num++;
            $this->actionAssignWhatsappChatGroup($clientId);     // 添加Functional
            LogUtil::info('num:'.$num.'---client_id:'.$clientId);
        }
        LogUtil::info('over');
    }

    public function actionAssignWhatsappChatGroup($clientId)
    {
        $hasFunctional = [
            \common\library\privilege_v3\PrivilegeConstants::FUNCTIONAL_OKKI_LEADS_BASE
        ];
        $existsFunctionalIds = Helper::hasFunctional($clientId, $hasFunctional);
        if (!$existsFunctionalIds) {
            return false;
        }
        $userId = Client::getClient($clientId)->getMasterUser()->getUserId();
        if(!$userId){
            return false;
        }

        \User::setLoginUserById($userId);

        self::info('actionAssignWhatsappChatGroup --client_id:'.$clientId.'---user_id:'.$userId);
        $functional = [
            \common\library\privilege_v3\PrivilegeConstants::FUNCTIONAL_WHATSAPP_CHAT_GROUP
        ];
        PrivilegeService::getInstance($clientId)->assignFunction($functional);

    }


    // 给尾数为1的用户授权  部门管理员默认授权下属话术
    public function actionExecuteSpeechcraftSubordinate($clientId,$greyNum=null)
    {
        $dbSetId = 0;
        if (\Yii::app()->params['env'] == 'test')
            $dbSetId = 3;
        $greyNum = explode(',', $greyNum);
        $clientListData = $this->getClientList($clientId,false,null,null, 0, 0, $greyNum, $dbSetId,null);

        LogUtil::info('start');
        $num = 1;
        foreach ($clientListData as $clientIdInfo)
        {
            $clientId = $clientIdInfo['client_id'];
            if($clientIdInfo['valid_to']<=date("Y-m-d H:i:s",time())){    //判断是否有效用户
                continue;
            }

            $privilegeService = PrivilegeService::getInstance($clientId);
            $mainSystemId = $privilegeService->getMainSystemId();

            $systemMsg = "clientId[{$clientId}|] -- mainSystemId[{$mainSystemId}]";
            if (!in_array($mainSystemId, [PrivilegeConstants::CRM_LITE_2021_ID, PrivilegeConstants::CRM_LITE_2023_ID, PrivilegeConstants::CRM_LITE_SYSTEM_ID, PrivilegeConstants::CRM_PRO_SYSTEM_ID, PrivilegeConstants::CRM_SMART_SYSTEM_ID, PrivilegeConstants::OKKI_PRO_SYSTEM_ID])) {
                self::info($systemMsg . " -- 跳过");
                continue;
            }

            $num++;
            $this->actionAssignSpeechcraftSubordinate($clientId);     // 添加Functional
            LogUtil::info('num:'.$num.'---client_id:'.$clientId);
        }
        LogUtil::info('over');
    }

    public function actionAssignSpeechcraftSubordinate($clientId)
    {
        $roleService = PrivilegeService::getInstance($clientId)->getUserPrivilege()->getRoles();
        $roleInfo = $roleService->getRoleInfoByName('部门管理员');
        if ($roleInfo)  {
            $roleService->assignPrivilege($roleInfo['role_id'], $roleInfo['scope'], [
                PrivilegeConstants::PRIVILEGE_CRM_SPEECHCRAFT_SUBORDINATE,
            ]);
        }

    }

    public function actionLeads($clientId,$greyNum=null)
    {

        $dbSetId = 0;
        if (\Yii::app()->params['env'] == 'test')
            $dbSetId = 3;
        $greyNum = explode(',', $greyNum);
        $clientListData = $this->getClientList($clientId,false,null,null, 0, 0, $greyNum, $dbSetId,null);


        LogUtil::info('start');
        foreach ($clientListData as $clientIdInfo)
        {
            $clientId = $clientIdInfo['client_id'];

            //开通OKKI Leads的用户
            $functional = [
                \common\library\privilege_v3\PrivilegeConstants::FUNCTIONAL_OKKI_LEADS_BASE,
            ];
            $existsFunctionalIds = Helper::hasFunctional($clientId, $functional);
            if (!$existsFunctionalIds) {
                return false;
            }

            //来源为小满发现的线索/建档客户
            //建档/线索时间在2022年11月1日之后
            $pgdb = \PgActiveRecord::getDbByClientId($clientId);
            $last_lead_id = 0;
            do
            {
                $sql = "select client_id,lead_id from tbl_lead where client_id={$clientId} and origin=".\common\library\setting\library\origin\Origin::SYS_ORIGIN_ID_DISCOVERY."  and create_time>='2022-11-01 00:00:01'  and lead_id > $last_lead_id  order by lead_id asc limit 1000";
                echo $sql.'...',PHP_EOL;
                $result = $pgdb->createCommand($sql)->queryAll();
                echo count($result),PHP_EOL;
                LogUtil::info('lead count:'.count($result).'----sql:'.$sql);
                if(!$result) break;
                foreach ($result as $item) {
                    $last_lead_id = $item['lead_id'];
                }
                $leadIds = array_column($result,'lead_id');
                $sql = "update tbl_lead set origin=".\common\library\setting\library\origin\Origin::SYS_ORIGIN_ID_OKKI_LEADS." where lead_id in (".implode(',',$leadIds).") and origin=".\common\library\setting\library\origin\Origin::SYS_ORIGIN_ID_DISCOVERY;
                $pgdb->createCommand($sql)->execute();
            } while($result);


            $last_company_id = 0;
            do
            {
                $sql = "select client_id,company_id from tbl_company where client_id={$clientId} and origin=".\common\library\setting\library\origin\Origin::SYS_ORIGIN_ID_DISCOVERY."  and create_time>='2022-11-01 00:00:01'  and company_id > $last_company_id  order by company_id asc limit 1000";
                echo $sql.'...',PHP_EOL;
                $result = $pgdb->createCommand($sql)->queryAll();
                echo count($result),PHP_EOL;
                LogUtil::info('company count:'.count($result).'----sql:'.$sql);
                if(!$result) break;
                foreach ($result as $item) {
                    $last_company_id = $item['company_id'];
                }
                $company_Ids = array_column($result,'company_id');
                $sql = "update tbl_company set origin=".\common\library\setting\library\origin\Origin::SYS_ORIGIN_ID_OKKI_LEADS." where company_id in (".implode(',',$company_Ids).") and origin=".\common\library\setting\library\origin\Origin::SYS_ORIGIN_ID_DISCOVERY;
                $pgdb->createCommand($sql)->execute();
            } while($result);

        }
        LogUtil::info('over');
    }



    public function actionExecuteSearchciq($clientId,$greyNum=null)
    {

        $dbSetId = 0;
        if (\Yii::app()->params['env'] == 'test')
            $dbSetId = 3;
        $greyNum = explode(',', $greyNum);
        $clientListData = $this->getClientList($clientId,false,null,null, 0, 0, $greyNum, $dbSetId,null);


        LogUtil::info('start');
        foreach ($clientListData as $clientIdInfo)
        {
            $clientId = $clientIdInfo['client_id'];

            $userIds = array_column(\UserInfo::model()->findAll('client_id=' . $clientId), 'user_id');
            foreach ($userIds as $userId) {
                \User::setLoginUserById($userId);

                $redis = \RedisService::queuePersistent();
                $redisKey = Constant::SEARCH_HISTORY_KEY."_{$clientId}_{$userId}_PRODUCT";
                $list = $redis->lrange($redisKey,0,4);
                $list = array_unique($list);
                $value = array_values($list);

                $key = 'customs.product.search.keyword.history';
                if(!empty($value)){
                    $searchHistory = new \common\library\discovery\api\SearchHistory($clientId, $userId);
                    $searchHistory->setKey($key);
                    $searchHistory->setValue($value);
                    $searchHistory->saveSearchKeyword();
                }

                $redis = \RedisService::queuePersistent();
                $redisKey = Constant::SEARCH_HISTORY_KEY."_{$clientId}_{$userId}_COMPANY";
                $list = $redis->lrange($redisKey,0,4);
                $list = array_unique($list);
                $value = array_values($list);

                $key = 'customs.company.search.keyword.history';
                if(!empty($value)) {
                    $searchHistory = new \common\library\discovery\api\SearchHistory($clientId, $userId);
                    $searchHistory->setKey($key);
                    $searchHistory->setValue($value);
                    $searchHistory->saveSearchKeyword();
                }
            }
        }
        LogUtil::info('over');
    }



    public function actionExecuteSearch($clientId,$greyNum=null)
    {

        $dbSetId = 0;
        if (\Yii::app()->params['env'] == 'test')
            $dbSetId = 3;
        $greyNum = explode(',', $greyNum);
        $clientListData = $this->getClientList($clientId,false,null,null, 0, 0, $greyNum, $dbSetId,null);


        LogUtil::info('start');
        foreach ($clientListData as $clientIdInfo)
        {
            $clientId = $clientIdInfo['client_id'];


            $userIds = array_column(\UserInfo::model()->findAll('client_id=' . $clientId), 'user_id');
            foreach ($userIds as $userId) {
                \User::setLoginUserById($userId);
                $key = 'dx.search.keyword.history';
                $setting = new \common\library\setting\user\UserSetting($clientId, $userId, $key);
                $value = $setting->getValue();
                if(!empty($value)){
                    $searchHistory = new \common\library\discovery\api\SearchHistory($clientId, $userId);
                    $searchHistory->setKey($key);
                    $searchHistory->setValue($value);
                    $searchHistory->saveSearchKeyword();
                }
                $key = 'bing.search.keyword.history';
                $setting = new \common\library\setting\user\UserSetting($clientId, $userId, $key);
                $value = $setting->getValue();
                if(!empty($value)) {
                    $searchHistory = new \common\library\discovery\api\SearchHistory($clientId, $userId);
                    $searchHistory->setKey($key);
                    $searchHistory->setValue($value);
                    $searchHistory->saveSearchKeyword();
                }
            }


        }
        LogUtil::info('over');
    }


    // 给尾数为1的用户授权
    public function actionExecuteLeads($clientId,$greyNum=null)
    {
        $dbSetId = 0;
        if (\Yii::app()->params['env'] == 'test')
            $dbSetId = 3;
        $greyNum = explode(',', $greyNum);
        $clientListData = $this->getClientList($clientId,false,null,null, 0, 0, $greyNum, $dbSetId,null);


        LogUtil::info('start');
        $num = 1;
        foreach ($clientListData as $clientIdInfo)
        {
            $clientId = $clientIdInfo['client_id'];
            $privilegeService = PrivilegeService::getInstance($clientId);
            $mainSystemId = $privilegeService->getMainSystemId();

            if (!in_array($mainSystemId, [PrivilegeConstants::OKKI_LEADS_ID])) {
                return false;
            }

            $functional = [
                \common\library\privilege_v3\PrivilegeConstants::FUNCTIONAL_ACCOUNT_MANAGE
            ];
            $existsFunctionalIds = Helper::hasFunctional($clientId, $functional);
            if ($existsFunctionalIds) {
                return false;
            }

            $userId = Client::getClient($clientId)->getMasterUser()->getUserId();
            if(!$userId){
                return false;
            }
            \User::setLoginUserById($userId);
            \LogUtil::info("[ADD_FUNCTIONAL] client_id: {$clientId}, systems_id: " . $mainSystemId);
            PrivilegeService::getInstance($clientId)->assignFunction($functional);
            // 更新缓存
            $service = new ClientPrivilegeService($clientId);
            $service->getFunctionalIds();

        }
        LogUtil::info('over');
    }


    // 给尾数为1的用户授权
    public function actionExecuteWaba($clientId,$greyNum=null)
    {
        $dbSetId = 0;
        if (\Yii::app()->params['env'] == 'test')
            $dbSetId = 3;
        $greyNum = explode(',', $greyNum);
        $clientListData = $this->getClientList($clientId,false,null,null, 0, 0, $greyNum, $dbSetId,null);

        LogUtil::info('start');
        $num = 1;
        foreach ($clientListData as $clientIdInfo)
        {
            $clientId = $clientIdInfo['client_id'];
//            if($clientIdInfo['valid_to']<=date("Y-m-d H:i:s",time())){    //判断是否有效用户
//                continue;
//            }

            $privilegeService = PrivilegeService::getInstance($clientId);
            $mainSystemId = $privilegeService->getMainSystemId();

            $systemMsg = "clientId[{$clientId}|] -- mainSystemId[{$mainSystemId}]";
            if (!in_array($mainSystemId, [PrivilegeConstants::CRM_LITE_2021_ID, PrivilegeConstants::CRM_LITE_2023_ID, PrivilegeConstants::CRM_LITE_SYSTEM_ID, PrivilegeConstants::CRM_PRO_SYSTEM_ID, PrivilegeConstants::CRM_SMART_SYSTEM_ID, PrivilegeConstants::CRM_SMART_SYSTEM_ID])) {
                //self::info($systemMsg . " -- 跳过");
                continue;
            }

            $num++;
            $this->actionAssignFunctionalForWaba($clientId,$num);     //由于facebook和waba组在一起   已授权过facebook权限的，给facebook上一级的functional，默认显示

        }
        LogUtil::info('over');
    }



    public function actionAssignFunctionalForWaba($clientId, $num)
    {
        $facebook_functional = [
            \common\library\privilege_v3\PrivilegeConstants::FUNCTIONAL_FACEBOOK,
            \common\library\privilege_v3\PrivilegeConstants::FUNCTIONAL_SOCIALMEDIA
        ];
        $existsFunctionalIds = Helper::hasFunctional($clientId, $facebook_functional);
        if (!$existsFunctionalIds) {
            return false;
        }
        $userId = Client::getClient($clientId)->getMasterUser()->getUserId();
        if(!$userId){
            return false;
        }
        \User::setLoginUserById($userId);

        self::info('actionAssignFunctionalForWaba num:'.$num.'---client_id:'.$clientId.'---user_id:'.$userId);
        $functional = [
            \common\library\privilege_v3\PrivilegeConstants::FUNCTIONAL_SOCIALMEDIA_INTERACT,
            \common\library\privilege_v3\PrivilegeConstants::FUNCTIONAL_SOCIALMEDIA_MANAGE
        ];
        PrivilegeService::getInstance($clientId)->assignFunction($functional);


    }



    // 给尾数为1的用户授权
    public function actionExecuteFacebook($clientId,$greyNum=null)
    {
        $dbSetId = 0;
        if (\Yii::app()->params['env'] == 'test')
            $dbSetId = 3;
        $greyNum = explode(',', $greyNum);
        $clientListData = $this->getClientList($clientId,false,null,null, 0, 0, $greyNum, $dbSetId,null);

        LogUtil::info('start');
        $num = 1;
        foreach ($clientListData as $clientIdInfo)
        {
            $clientId = $clientIdInfo['client_id'];
            if($clientIdInfo['valid_to']<=date("Y-m-d H:i:s",time())){    //判断是否有效用户
                continue;
            }
//            if($clientId%10 != 1){     // 尾数为1
//                continue;
//            }
//            $item = \Yii::app()->db->createCommand("SELECT client_id FROM tbl_client_info_external WHERE client_id=".$clientIdInfo['client_id']." and `value` in (13,14,15,16) ")->queryColumn();
//            if(empty($item)){    //    版本是：lite（lite2020，lite2021）、smart、pro的client
//                continue;
//            }

            $privilegeService = PrivilegeService::getInstance($clientId);
            $mainSystemId = $privilegeService->getMainSystemId();

            $systemMsg = "clientId[{$clientId}|] -- mainSystemId[{$mainSystemId}]";
            if (!in_array($mainSystemId, [PrivilegeConstants::CRM_LITE_2021_ID, PrivilegeConstants::CRM_LITE_2023_ID, PrivilegeConstants::CRM_LITE_SYSTEM_ID, PrivilegeConstants::CRM_PRO_SYSTEM_ID, PrivilegeConstants::CRM_SMART_SYSTEM_ID, PrivilegeConstants::CRM_SMART_SYSTEM_ID])) {
                self::info($systemMsg . " -- 跳过");
                continue;
            }

            $num++;
            //$this->actionOpenFacebookPermission($clientId);   //添加权限包
            $this->actionAssignFunctional($clientId);     // 添加Functional
            LogUtil::info('num:'.$num.'---client_id:'.$clientId);
        }
        LogUtil::info('over');
    }


    public function actionAssignFunctional($clientId)
    {

        $client = \common\library\account\Client::getClient($clientId);
        $salesGuideSwitch = $client->getExtentAttributes([Client::EXTERNAL_KEY_FACEBOOK_SWITCH])[Client::EXTERNAL_KEY_FACEBOOK_SWITCH] ?? 0;
        if ($salesGuideSwitch) {
            return false;
        }

        // 配置facebook总开关
        $client->setExtentAttributes([
            \common\library\account\Client::EXTERNAL_KEY_FACEBOOK_SWITCH => '1',
        ]);
        $client->saveExtentAttributes();

        \User::setLoginUserById(Client::getClient($clientId)->getMasterUser()->getUserId());
        $functional = [
                        \common\library\privilege_v3\PrivilegeConstants::FUNCTIONAL_FACEBOOK,
                        \common\library\privilege_v3\PrivilegeConstants::FUNCTIONAL_SOCIALMEDIA
                      ];
        PrivilegeService::getInstance($clientId)->assignFunction($functional);



    }



    public function actionOpenFacebookPermission(string $client_id) {

        //过滤已开通过的client
        $clientIds = explode(',', $client_id);
        $runClientIds = [];
        foreach ($clientIds as $clientId) {
            $admin = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();
            User::setLoginUserById($admin);
            $client = Client::getClient($clientId);
            $salesGuideSwitch = $client->getExtentAttributes([Client::EXTERNAL_KEY_FACEBOOK_SWITCH])[Client::EXTERNAL_KEY_FACEBOOK_SWITCH] ?? 0;
            if (!$salesGuideSwitch) {
                $runClientIds[] = $clientId;
            }

        }
        $runClientIds = implode(',', $runClientIds);
        if (empty($runClientIds)) {
            return false;
        }
        try {
            foreach ($clientIds as $clientId) {
                $client = \common\library\account\Client::getClient($clientId);
                if(!$client->full_name) {
                    return false;
                }
                //开通Facebook的为其添加试用权限
                $promotion = new \common\library\promotion\Promotion($clientId);
                $promotion->module = PrivilegeConstants::MODULE_FACEBOOK;
                $promotion->getOperator()->create();

                // 配置facebook总开关
                $client->setExtentAttributes([
                    \common\library\account\Client::EXTERNAL_KEY_FACEBOOK_SWITCH => '1',
                ]);
                $client->saveExtentAttributes();
            }
        } catch (Exception $e) {
            return false;
        }
        return true;
    }




    public function actionExecute($clientId,$greyNum=null)
    {
        if ($clientId)
        {
            $clientIds = [$clientId];
        } else {
            $dbSetId = 0;
            if (\Yii::app()->params['env'] == 'test')
                $dbSetId = 3;

            $greyNum = explode(',', $greyNum);
            $clientListData = $this->getClientList($clientId,false,null,null, 0, 0, $greyNum, $dbSetId,null);
            $clientIds = array_column($clientListData,'client_id');
        }

        foreach ($clientIds as $clientId)
        {
            $this->actionLeadTransformTask($clientId);      // tbl_lead_transform_task
            $this->actionReportGroupData($clientId);        // tbl_report_group_data
        }
    }


    public function actionLeadTransformTask($clientId)
    {
        ini_set("display_errors", "On");
        ini_set("memory_limit", "15000M");
        error_reporting(E_ALL & ~E_NOTICE);
        $db = \ProjectActiveRecord::getDbByClientId($clientId);
        $pgdb = \PgActiveRecord::getDbByClientId($clientId);

        if(!$pgdb || !$db){
            $msg = 'db is null';
            echo $msg,PHP_EOL;
            LogUtil::info($msg);
            return false;
        }

        $last_lead_id = 0;
        do
        {
            $sql = "select client_id,lead_id from tbl_lead where client_id={$clientId} and lead_id > $last_lead_id order by lead_id asc limit 1000";
            echo $sql.'...',PHP_EOL;
            $result = $pgdb->createCommand($sql)->queryAll();
            echo count($result),PHP_EOL;
            LogUtil::info('count:'.count($result).'----sql:'.$sql);
            if(!$result) break;
            foreach ($result as $item) {
                $last_lead_id = $item['lead_id'];
            }
            $leadIds = array_column($result,'lead_id');
            $sql = "update tbl_lead_transform_task set client_id='{$clientId}' where lead_id in (".implode(',',$leadIds).") and client_id=0";
            $db->createCommand($sql)->execute();

        } while($result);

    }



    public function actionExecuteDb($table='')
    {
        $type = 3;
        $list = \common\library\account\service\DbService::getDbList($type);
        foreach ($list as $set)
        {
            $db = PgActiveRecord::getDbByDbSetId($set['set_id']);
            $testSql = 'select company_id from tbl_company limit 1';
            try {
                $db->createCommand($testSql)->queryScalar();
            }catch (\Exception $exception)
            {
                continue;
            }
            if($table=='tbl_edm_email_track_detail')  $this->actionEdmEmailTrackDetail(0,$db);    // tbl_edm_email_track_detail
        }
    }

    public function actionEdmEmailTrackDetail($clientId = 0,$db=null)
    {
        ini_set("display_errors", "On");
        ini_set("memory_limit", "15000M");
        error_reporting(E_ALL & ~E_NOTICE);
        if($clientId) $db = \PgActiveRecord::getDbByClientId($clientId);

        if(!$db){
            $msg = 'db is null';
            echo $msg,PHP_EOL;
            LogUtil::info($msg);
            return false;
        }


        $detail_id = 0;
        do
        {
            $sql = "select user_id,detail_id from tbl_edm_email_track_detail where detail_id > $detail_id order by detail_id asc limit 1000";
            echo $sql.'...',PHP_EOL;
            $result = $db->createCommand($sql)->queryAll();
            echo count($result),PHP_EOL;
            LogUtil::info('count:'.count($result).'----sql:'.$sql);
            foreach ($result as $item) {
                $detail_id = $item['detail_id'];
                $userId = $item['user_id'];

                $user = \User::getUserObject($userId);
                $client_id = $user->getClientId();

                if(empty($client_id)) continue;

                $sql = "update tbl_edm_email_track_detail set client_id='{$client_id}' where detail_id='{$detail_id}' and client_id=0 ";
                $db->createCommand($sql)->execute();
            }
        } while($result);

    }


    public function actionReportGroupData($clientId)
    {
        ini_set("display_errors", "On");
        ini_set("memory_limit", "15000M");
        error_reporting(E_ALL & ~E_NOTICE);
        if($clientId) $db = \PgActiveRecord::getDbByClientId($clientId);

        if(!$db){
            $msg = 'db is null';
            echo $msg,PHP_EOL;
            LogUtil::info($msg);
            return false;
        }

        $sql = "SELECT user_id FROM tbl_user_info WHERE client_id=:client_id";
        $list = \Yii::app()->account_base_db->createCommand($sql)->queryAll(true,[':client_id' => $clientId]);
        foreach ($list as $userRow) {
            $sql = "select unique_key from tbl_report_data where user_id={$userRow['user_id']}   ";
            echo $sql.'...',PHP_EOL;
            $result = $db->createCommand($sql)->queryColumn();
            echo count($result),PHP_EOL;
            LogUtil::info('count:'.count($result).'----sql:'.$sql);
            $data_chunk = array_chunk($result, 1000);
            foreach ($data_chunk as $chunk) {
                $sql = "update tbl_report_group_data set client_id='{$clientId}' where unique_report_key in ('".implode("','",$chunk)."') and client_id=0";
                $db->createCommand($sql)->execute();
            }
        }

    }



    public function actionInitCorp()
    {
        $sql = "select * from tbl_wecom_account where enable_flag=1 ";
        $datalist = Yii::app()->db->createCommand($sql)->queryAll();

        foreach ($datalist as $item){
            if(empty($item['corp_param'])){
                continue;
            }
            $clientId = $item['client_id'];
            $userId = $item['user_id'];
            $wecomCorp = new \common\library\wecom\corp\WecomCorp($clientId,$userId);
            $wecomCorp = $wecomCorp->getCorpInfoByCorpid($item['corpid']);

            $wecomCorp->create_user = $item['user_id'];
            $wecomCorp->client_id = $clientId;
            $wecomCorp->create_time = $item['create_time'];
            $wecomCorp->update_time = $item['update_time'];
            $wecomCorp->bind_time = $item['bind_time'];
            $wecomCorp->access_token = $item['access_token'] ?? '';
            $wecomCorp->wecom_open_userid = $item['wecom_open_userid'];
            $wecomCorp->permanent_code = $item['permanent_code'] ?? '';
            $wecomCorp->corpid = $item['corpid'];
            $wecomCorp->wecom_userid = $item['wecom_userid'] ?? '';
            $wecomCorp->agentid = $item['agentid'] ?? '';
            $wecomCorp->expire_time = $item['expire_time'];
            $wecomCorp->oauth_flag = $item['oauth_flag'];
            $wecomCorp->corp_name = $item['corp_name'];
            $wecomCorp->corp_full_name = $item['corp_name'];
            $wecomCorp->corp_param = $item['corp_param'];
            $wecomCorp->wecom_account_id = $item['id'];
            $wecomCorp->enable_flag = $item['enable_flag'];

            $wecomCorp->save();
            var_dump($wecomCorp->id);
//            exit();
        }

    }


}