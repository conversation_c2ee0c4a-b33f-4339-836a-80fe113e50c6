<?php
/**
 * Created by PhpStorm.
 * User: cayley
 * Date: 2018/9/4
 * Time: 下午2:10
 */

use common\library\account\Helper;
use common\library\alibaba\services\AlibabaProductGroup;
use common\library\google_ads\track\TrackSessionList;
use common\library\push\Browser;
use common\library\setting\library\group\Group;
use Swoole\Process;
use Swoole\Process\Pool;
use xiaoman\AlibabaSdk\taobao\top\request\AlibabaMemberInfoGetRequest;

class CayleyCommand extends CrontabCommand
{


    public function actionTest2()
    {

        $clientId = 15053;
        $admin = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();
        User::setLoginUserById($admin);
        $user = User::getLoginUser();
        $c = new \common\library\invoice\sn_rule\SNRuleService($user->getClientId(), Constants::TYPE_ORDER);
//        $a = $c->generateSerialNumber();
//        var_dump($a);die;
        $c->changeRuleNumber(0, 2);

        $db = \common\models\client\Order::getDbByClientId($user->getClientId());
        $list = $db->createCommand("SELECT * FROM tbl_order WHERE client_id=:client_id AND create_time>=:create_time ORDER BY create_time")
            ->queryAll(true, [
                ':client_id' => $user->getClientId(),
                ':create_time' => '2019-09-01 00:00:00'
            ]);

        foreach ( $list as $item)
       {
           $no = $c->generateSerialNumber();
           self::info(" create_time :{$item['create_time']} client_id:{$clientId}  order_id:{$item['order_id']} old: {$item['order_no']}, new: {$no}");
           $sql = "update tbl_order set order_no ='{$no}' where order_id={$item['order_id']} and client_id={$clientId}";
           self::info($sql);
           $db->createCommand($sql)->execute();
       }
    }

    public function actionTest3()
    {

        $clientId = 15053;
        $admin = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();
        User::setLoginUserById($admin);
        $user = User::getLoginUser();
        $c = new \common\library\invoice\sn_rule\SNRuleService($user->getClientId(), Constants::TYPE_QUOTATION);
//        $a = $c->generateSerialNumber();
//        var_dump($a);die;
        $c->changeRuleNumber(0, 2);

        $db = \common\models\client\Order::getDbByClientId($user->getClientId());
        $list = $db->createCommand("SELECT * FROM tbl_quotation WHERE client_id=:client_id AND create_time>=:create_time ORDER BY create_time")
            ->queryAll(true, [
                ':client_id' => $user->getClientId(),
                ':create_time' => '2019-09-01 00:00:00'
            ]);

        foreach ( $list as $item)
        {
            $no = $c->generateSerialNumber();
            self::info(" create_time :{$item['create_time']} client_id:{$clientId}  quotation_id:{$item['quotation_id']} old: {$item['quotation_no']}, new: {$no}");
            $sql = "update tbl_quotation set quotation_no ='{$no}' where quotation_id={$item['quotation_id']} and client_id={$clientId}";
            self::info($sql);
            $db->createCommand($sql)->execute();
        }
    }

    public function actionTest4()
    {
        /** @var CDbConnection $db */
        $db = Yii::app()->db;
        $list = $db->createCommand('select * from tbl_user_mail')->queryAll();
        foreach ($list as $item)
        {
//            if( $item['user_mail_id'] != 765)
//                continue;

            self::info("{$item['client_id']}, 0, {$item['user_id']}, {$item['user_mail_id']}");
            UserMailService::transfer($item['client_id'], 0, $item['user_id'], $item['user_mail_id']);
        }
    }

    public function actionTest5()
    {

        $result = [];
        $resultPath = '/tmp/department_admin_new.csv';
        $fp = fopen($resultPath, 'w');
        fputcsv($fp, ['client_id', 'user_id', '所属部门', '管理部门', '超级管理员']);

        /** @var CDbConnection $db */
        $db = Yii::app()->account_base_db;
        $sql = 'SELECT m.client_id, m.user_id, count(m.department_id) as department_count, t.count , GROUP_CONCAT(m.department_id) as all_department_id, t.m_department_id  from tbl_department_member m LEFT JOIN (SELECT user_id, count(1) as count, GROUP_CONCAT(department_id)as m_department_id from tbl_department_member where user_type=1 GROUP BY user_id ) t on t.user_id = m.user_id group by  m.user_id  HAVING department_count >1 and  t.count>=1 and department_count !=t.count>=1';
        $list = $db->createCommand($sql)->queryAll();
        foreach ($list as $item)
        {
            $ids = explode(',', $item['all_department_id']);
            $mIds = explode(',', $item['m_department_id']);
            $depList = $db->createCommand('select id, prefix from tbl_department where id in (' . $item['all_department_id'] . ')')->queryAll();
//            var_dump($depList);die;
            $depMap = array_column($depList, 'prefix', 'id');

            $allIds = array_reduce($depMap, function ($carry, $item) {
                $carry = array_merge(explode('-', $item), $carry);
                $carry = array_unique(array_filter($carry));
                return $carry;
            }, []);

            $allIds = array_merge($allIds, $ids);
            $allDepList = $db->createCommand('select id, name from tbl_department where id in (' . implode(',', $allIds) . ')')->queryAll();
            $nameMap = array_column($allDepList, 'name', 'id');
            $nameMap[0] = '我的企业';

            $mItem = [];
            $aItem = [];
            $in = false;
            foreach ($ids as $id)
            {
                if ($id == 0)
                {
                    $aItem[] = $id;
                    continue;
                }

                $prefix = $depMap[$id];
                $aItem[] = $prefix . $id;
            }

            foreach ($mIds as $id)
            {
                if ($id == 0)
                {
                    $mItem[] = $id;
                    continue;
                }

                $prefix = $depMap[$id];
                $mItem[] = $prefix . $id;
            }

            foreach ($mItem as $e)
            {
                if ($e === 0)
                {
                    var_dump($e);
//                    $in = '是';
                    break;
                }

                foreach ($mItem as $el)
                {
                    if ($e != $el && strstr($e, $el) !== false)
                    {
                        var_dump(strstr($e, $el));
//                        $in = '是';
                    }
                }
            }

            $aItem = array_map(function ($item) use ($nameMap) {
                $arr = explode('-', $item);
                $newArr = [];
                foreach ($arr as $i)
                    $newArr[] = $nameMap[$i];

                return implode('>', $newArr);
            }, $aItem);

            $mItem = array_map(function ($item) use ($nameMap) {
                $arr = explode('-', $item);
                $newArr = [];
                foreach ($arr as $i)
                    $newArr[] = $nameMap[$i];

                return implode('>', $newArr);
            }, $mItem);

            $in = $db->createCommand("select user_id from tbl_user_privilege where user_id={$item['user_id']} and privilege='crm.admin'")->queryScalar();

            $data = [
                'client_id' => $item['client_id'],
                'user_id' => $item['user_id'],
                'aItem' => implode("\n", $aItem),
                'mItem' => implode("\n", $mItem),
                'in' => $in ? '是' : '否',
            ];

//var_dump($data);die;
            fputcsv($fp, $data);

        }

        fclose($fp);
    }


    public function actionTest6()
    {


        $redis = Yii::app()->redis;
//        $redis->executeCommand("set", array(ProjectActiveRecord::AUTO_INCREMENT_KEY, '**********'));
        $redis->executeCommand("set", array(ProjectActiveRecord::AUTO_INCREMENT_KEY, '**********'));

        $a = ProjectActiveRecord::produceAutoIncrementId();
        var_dump($a);

        die;

        $result = [];
        $resultPath = '/tmp/client_privilege_new.csv';
        $fp = fopen($resultPath, 'w');
        fputcsv($fp, ['client_id', '权限', '用户']);

        /** @var CDbConnection $db */
        $db = Yii::app()->account_base_db;
        $sql = "SELECT client_id, privilege, count(1), GROUP_CONCAT(DISTINCT user_id) as user_ids  from tbl_user_privilege where enable_flag =1 and privilege not in ('enterprise.admin', 'mk.admin', 'dx.admin','mk.user','dx.user','crm.user','crm.admin') GROUP BY client_id , privilege";
        $list = $db->createCommand($sql)->queryAll();
        $map = [];
        foreach ($list as $item)
        {
            $userIds = explode(',', $item['user_ids']);
            sort($userIds);
            $key = implode(',', $userIds);
            $map[$item['client_id']][$key][] = $item['privilege'];
        }

        $res = [];

        foreach ($map as $clientId => $list)
        {
            foreach ($list as $userStr => $privilege)
            {
                $data = [
                    'client_id' => $clientId,
                    'privilege' => implode("\n", $privilege),
                    'user_id' => $userStr
                ];


                fputcsv($fp, $data);
            }
        }

        fclose($fp);

    }


    public function actionTest7()
    {
        $result = [];
        $resultPath = '/tmp/client_user_privilege_new.csv';
        $fp = fopen($resultPath, 'w');
        fputcsv($fp, ['权限', 'clientId', '用户数']);

        /** @var CDbConnection $db */
        $db = Yii::app()->account_base_db;
        $sql = "SELECT user_id, count(1) as count ,client_id, GROUP_CONCAT(DISTINCT privilege) as privileges  from tbl_user_privilege where enable_flag =1 and privilege not in ('enterprise.admin', 'mk.admin', 'dx.admin','mk.user','dx.user','crm.user','crm.admin') GROUP BY user_id   HAVING count  >1";
        $list = $db->createCommand($sql)->queryAll();
        $map = [];
        foreach ($list as $item)
        {
            $privileges = explode(',', $item['privileges']);
            sort($privileges);
            $key = implode(',', $privileges);
            $map[$key][] = [
                'client_id' => $item['client_id'],
                'user_id' => $item['user_id']
            ];
        }

//        var_dump($map);die;
        foreach ($map as $privilegeKey => $item)
        {
            $clientIds = array_unique(array_column($item, 'client_id'));
            $userIds = array_column($item, 'user_id');
            foreach ($clientIds as $clientId)
            {
                $clientUserIds = $db->createCommand('select user_id from tbl_user_info where client_id =' . $clientId . ' and user_id in(' . implode(',', $userIds) . ')')->queryColumn();
                $privileges = explode(',', $privilegeKey);
                $data = [
                    'client_id' => $clientId,
                    'privilege' => implode("\n", $privileges),
//                    'client_id' => count(array_unique($clientIds)),
                    'user_id' => count($clientUserIds)
                ];

                fputcsv($fp, $data);
            }

        }

        fclose($fp);
    }


    public function actionOrder($clientId=0)
    {
        $clientList = self::getClientList($clientId);
        foreach ($clientList as $client)
        {
            $clientId = $client['client_id'];

            if( in_array($clientId, [322,10659]))
                continue;
//            self::info('handle : '.$clientId);

            try
            {
                $adminUserId = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId($clientId);
                User::setLoginUserById($adminUserId);
            }catch ( \Exception $e)
            {
                self::info('error: '.$clientId.' '.$e->getMessage());
                continue;
            }

            $user = User::getLoginUser();
            $db = \common\models\client\Order::getDbByClientId($user->getClientId());
            $model = \common\models\client\Order::model();

            $list = $db->createCommand("SELECT * FROM tbl_order WHERE client_id=:client_id AND create_time>=:create_time ORDER BY create_time")
                ->queryAll(true, [
                    ':client_id' => $user->getClientId(),
                    ':create_time' => '2018-09-25 00:00:00'
                ]);

            $c = new \common\library\invoice\sn_rule\SNRuleService($user->getClientId(), Constants::TYPE_ORDER);
            foreach ($list as $item)
            {
                $count = $model->count("client_id=:client_id AND order_no=:order_no", [
                    ':client_id' => $user->getClientId(),
                    ':order_no' => $item['order_no']
                ]);

                if( $count <=1)
                    continue;

                do
                {
                    $no = $c->generateSerialNumber();
//                    $no = 'test';
                    self::info('client_id:'.$clientId.' order_id: ' . $item['order_id'] . ' no:' . $no . ' create_tome' .
                        $item['create_time'].'old:'.$item['order_no']. 'count: '.$count);

//                    $while = false;

                    $result = $model->find("client_id=:client_id AND order_no=:order_no", [
                        ':client_id' => $user->getClientId(),
                        ':order_no' => $no
                    ]);

                    if( !empty($result) )
                    {
                        $while = true;
                    }else
                    {
                        $model->updateByPk($item['order_id'], ['order_no' => $no]);
                        $while = false;
                    }




                }while($while);

            }
        }
    }


    public function actionQuotation($clientId=0)
    {
        $clientList = self::getClientList($clientId);
        foreach ($clientList as $client)
        {
            $clientId = $client['client_id'];

//            if( in_array($clientId, [322,10659]))
//                continue;
//            self::info('handle : '.$clientId);

            try
            {
                $adminUserId = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();
                User::setLoginUserById($adminUserId);
            }catch ( \Exception $e)
            {
                self::info('error: '.$clientId.' '.$e->getMessage());
                continue;
            }

            $user = User::getLoginUser();
            $db = \common\models\client\Quotation::getDbByClientId($user->getClientId());
            $model = \common\models\client\Quotation::model();

            $list = $db->createCommand("SELECT * FROM tbl_quotation WHERE client_id=:client_id AND create_time>=:create_time ORDER BY create_time")
                ->queryAll(true, [
                    ':client_id' => $user->getClientId(),
                    ':create_time' => '2018-09-25 00:00:00'
                ]);

            $c = new \common\library\invoice\sn_rule\SNRuleService($user->getClientId(), Constants::TYPE_QUOTATION);
            foreach ($list as $item)
            {
                $count = $model->count("client_id=:client_id AND quotation_no=:quotation_no", [
                    ':client_id' => $user->getClientId(),
                    ':quotation_no' => $item['quotation_no']
                ]);

                if( $count <=1)
                    continue;

                do
                {
                    $no = $c->generateSerialNumber();
//                    $no = 'test';
                    self::info('client_id:'.$clientId.' quotation_id: ' . $item['quotation_id'] . ' no:' . $no . ' create_tome' .
                        $item['create_time'].'old:'.$item['quotation_no']. 'count: '.$count);

//                    $while = false;

                    $result = $model->find("client_id=:client_id AND quotation_no=:quotation_no", [
                        ':client_id' => $user->getClientId(),
                        ':quotation_no' => $no
                    ]);

                    if( !empty($result) )
                    {
                        $while = true;
                    }else
                    {
                        $model->updateByPk($item['quotation_id'], ['quotation_no' => $no]);
                        $while = false;
                    }




                }while($while);

            }
        }
    }



    public function actionProduct($clientId=0)
    {
        $clientList = self::getClientList($clientId);
        foreach ($clientList as $client)
        {
            $clientId = $client['client_id'];

//            if( in_array($clientId, [322,10659]))
//                continue;
//            self::info('handle : '.$clientId);

            try
            {
                $adminUserId = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId($clientId);
                User::setLoginUserById($adminUserId);
            }catch ( \Exception $e)
            {
                self::info('error: '.$clientId.' '.$e->getMessage());
                continue;
            }

            $user = User::getLoginUser();
            $db = \common\models\client\ClientProduct::getDbByClientId($user->getClientId());
            $model = \common\models\client\ClientProduct::model();
            $list = $db->createCommand("SELECT product_id, product_no, create_time FROM tbl_product WHERE client_id=:client_id AND create_time >= :create_time ORDER BY create_time")
                    ->queryAll(true, [':client_id' => $user->getClientId(), ':create_time' => '2018-09-25 00:00:00']);

            $c = new \common\library\invoice\sn_rule\SNRuleService($user->getClientId(), Constants::TYPE_PRODUCT);
            foreach ($list as $item)
            {
                $count = $model->count([
                    'client_id' => intval($user->getClientId()),
                    'product_no' => $item['product_no']
                ]);

                if( $count <=1)
                    continue;

                do
                {
//                    $no = $c->generateSerialNumber();
                    $no = 'test';
                    self::info('client_id:'.$clientId.' product_id: ' . $item['product_id'] . ' no:' . $no . ' create_tome' .
                        $item['create_time'].'old:'.$item['product_no']. 'count: '.$count);

                    $while = false;

//                    $result = $model->find("client_id=:client_id AND product_no=:product_no", [
//                        ':client_id' => $user->getClientId(),
//                        ':product_no' => $no
//                    ]);
//
//                    if( !empty($result) )
//                    {
//                        $while = true;
//                    }else
//                    {
//                        $model->updateByPk($item['product_id'], ['product_no' => $no]);
//
//                        $while = false;
//                    }




                }while($while);

            }
        }
    }


    public function actionTest8()
    {


        $data = UniqueFileHandler::filter('0d3541ce76bcc3ac6662c806b80dd959', 'image.png', '7564820');
        var_dump($data);

        die;

        $client = \common\library\account\Client::getClient(8557);

        $key = $client->getPkKey(8557);
        var_dump($key);
        \common\components\CacheObject::getCache()->del([$key]);
        var_dump(\common\components\CacheObject::getCache()->get($key));
        $client->deleteCache();

        var_dump(\common\library\account\Client::getClient(8557)->getAttributes());

        die;

    }


    public function actionMail()
    {

//        var_dump(111);
//        die;
        $client_id = 18234;
        $userId = \common\library\privilege_v3\PrivilegeService::getInstance($client_id)->getAdminUserId();
        $userId = ********;
        $user = User::getUserObject($userId);
        User::setLoginUser($user);
        $client = \common\library\account\Client::getClient($client_id);
        $client->mysql_set_id = 23;
        $client->mongo_set_id = 0;
        $client->pgsql_set_id = 68;

        $client->update(['mysql_set_id', 'mongo_set_id', 'pgsql_set_id']);

        CustomerOptionService::iniClient($user, 'crm_smart');

//初始化版本权限
        \common\library\privilege_v3\PrivilegeService::getInstance($client_id)->initClient('crm_smart', $userId);

        die;
        User::setLoginUserById(30393);
        $user = User::getLoginUser();
        $clientId = $user->getClientId();
        $userId = $user->getUserId();
        $emailId = ********;//<EMAIL>

        $listObj = new \common\library\mail\MailList($clientId, $userId);
        $listObj->setUserMailId($emailId);

        //过滤已删除,草稿,隐藏草稿,java, 永久删除
        $listObj->setNotInFolderIds([
            \Mail::FOLDER_DRAFT_ID,
            \Mail::FOLDER_TRASH_ID,
            \Mail::FOLDER_DELETE_ID,
            \Mail::FOLDER_IMPORT_ID,
            \Mail::FOLDER_HIDDEN_DRAFT_ID,
        ]);
        $listObj->setFields('client_id,user_id,mail_id, user_mail_id');
        $listObj->setOrderBy('receive_time');
        $listObj->setOrder('asc');
        $list = $listObj->find();
        $count = count($list);
        self::info('count:'.$count);
        foreach ( $list as $i =>$item )
        {
            self::info("begin mail_id:{$item['mail_id']}");

            $time1 = microtime(true);
            \common\library\ai\classify\Dispatcher::callback($item['client_id'], $item['user_id'], $item['user_mail_id'],$item['mail_id']);
            $time2 = microtime(true);
            self::info("end mail_id:{$item['mail_id']} time:".(round(($time2-$time1)/1000,3)).'s');

        }

    }

    public function actionTest11()
    {
        $result = [];
        $resultPath = '/tmp/schedule.csv';
        $fp = fopen($resultPath, 'w');
        fputcsv($fp, ['day', '创建日程的人数', '创建的日程数']);

        $clientList = $this->getClientList();
        $result = [];
        foreach ( $clientList as $client)
        {
            self::info("client: {$client['client_id']}");
            $db = PgActiveRecord::getDbByClientId($client['client_id']);
            $list = $db->createCommand("select to_char(create_time, 'YYYY-MM-DD') as day , count(distinct create_user_id) as user_count, count(1) as count  from tbl_schedule where  client_id={$client['client_id']} and create_time >='2018-08-01 00:00:00' group  by  day")->queryAll();
            foreach ($list as $item)
            {
                $day = $item['day'];
                if( !isset($result[$day]))
                {
                    $result[$day] = [
                        'day' => $day,
                        'user_count' => 0,
                        'count' => 0
                    ];
                }
                $result[$day]['user_count'] += $item['user_count'];
                $result[$day]['count'] += $item['count'];
            }
        }


        foreach ($result as $item )
        {
            fputcsv($fp, [
                'day' => $item['day'],
                'user_count' => $item['user_count'],
                'count' => $item['count'],
            ]);
        }

        fclose($fp);
    }


    public function actionGrpc()
    {
        $i = 0;
        $errorCount = 0;
        // 初始化客户端
//        $client = new \Infra\Ai\Recommend\V2\RecommendServiceClient("ai-recommend-v2.vpcslb.com:8080", [
//            'credentials' => \Grpc\ChannelCredentials::createInsecure()
//        ]);
        while (true) {
        // 设置请求

//            $request = new \Infra\Ai\Recommend\V2\RecommendCompanyRequest();
////            $request->setClientId(7);
////            $request->setUserId(15202575);
////            $request->setQueue("ai-recommend");
////            $request->setSceneId(\Infra\Ai\Recommend\V2\UserCompanyFeedback\SceneId::SCENE_AI_RECOMMEND);
////            $request->setSize(1);
////            $request->setSlideSize(0);
////            $request->setSceneUrl("test");
////        // 发起请求
////            list($reply, $status) = $client->RecommendCompany($request)->wait();
////        // 响应
////            if ($status->code != 0) {
////                var_dump($status);
////                $errorCount ++;
////                LogUtil::info("errorCount: $errorCount, status: {$status->code}, details:{$status->details} ".json_encode($status));
////
////            }

            $i++;
            $json = HttpUtil::doGet('http://omgk.xiaoman.cn/api/test/grpc');
            $json = json_decode($json, true);
            if( !$json )
            {
                LogUtil::error("curl error $i");
                $errorCount ++;
            }


            if( $json['data']['code'] !=0 )
            {
                $errorCount ++;
                LogUtil::info("errorCount: $errorCount, status:  ".json_encode($json['data']));
            }

            if( $i % 1000 ===0 )
            {
                print( "curl i: $i  errorCount: $errorCount \n  ");
            }

        //    $client->close();
        }

    }


    public function actionAddOpportunityAi($clientId=0, $last=-1)
    {
        $clientList = self::getClientList($clientId, false,1, null,0, 0,$last);

        foreach ( $clientList as $clientItem )
        {
            if( Yii::app()->params['env'] == 'test' && $clientItem['mysql_set_id'] != '3')
            {
                continue;
            }

            $service =  \common\library\privilege_v3\PrivilegeService::getInstance($clientItem['client_id']);
            $adminUserId = $service->getAdminUserId();
            if( !$adminUserId )
            {
                self::info("continue client_id: {$clientItem['client_id']}  adminUser not exist: {$adminUserId}");
                continue;
            }

            User::setLoginUserById($adminUserId);


            if( !$service->hasFunctional([\common\library\privilege_v3\PrivilegeConstants::FUNCTIONAL_AI_CLASSIFY_OPPORTUNITY]))
            {
                continue;
            }


            //有商机自动化权限的, 都可以进行有编辑权限
            $service->assignFunction(\common\library\privilege_v3\PrivilegeConstants::FUNCTIONAL_AI_CLASSIFY_OPPORTUNITY_SETTING_EDIT);
            $system = $service->getMainSystemId();
            $service->flushCache();

            self::info(" finish client: {$clientItem['client_id']} system: {$system} adminUser: {$adminUserId}");

        }
    }


    public function actionStatisticsMail()
    {
        ini_set("memory_limit", "15000M");
        $clientList = $this->getClientList();

        $result = [];
        $resultPath = '/tmp/mail-2017-2019.csv';
        $fp = fopen($resultPath, 'w');
        fputcsv($fp, ['年月', '新增收件', '人均收件', '新增发件', '人均发件']);

        $start = '2017-01-01';
        $end = '2019-12-31';
        foreach ( $clientList as $client)
        {
            $db = ProjectActiveRecord::getDbByClientId($client['client_id']);
            self::info("client: {$client['client_id']}");
            $sql = "select send_mail_count,receive_mail_count,date,user_id from tbl_user_statistics_day where client_id={$client['client_id']} and date >='{$start}' and date <='{$end}'";
            $list = $db->createCommand($sql)->queryAll();
            foreach ( $list as $item )
            {
                $month = substr($item['date'], 0,7);
                if( isset($result[$month]) )
                {
                    $result[$month]['send_mail_count'] += $item['send_mail_count'];
                    $result[$month]['receive_mail_count'] += $item['receive_mail_count'];
                    $result[$month]['user_ids'][$item['user_id']] = $item['user_id'];
                }else
                {
                    $result[$month] = [
                        'send_mail_count' =>$item['send_mail_count'],
                        'receive_mail_count' =>$item['receive_mail_count'],
                        'user_ids' => [$item['user_id']],
                    ];
                }
            }

            unset($list);

        }


        ksort($result);
        foreach ( $result as $month => $item)
        {
            $data = [
                'month' => $month,
                'receive_mail_count' => $item['receive_mail_count'],
                'avg_receive_mail_count' => round($item['receive_mail_count']/count($item['user_ids']), 2),
                'send_mail_count' => $item['send_mail_count'],
                'avg_send_mail_count' => round($item['send_mail_count']/count($item['user_ids']), 2),
            ];

            fputcsv($fp, $data);
        }

        fclose($fp);


    }

    public function actionAiFileCallFail()
    {
        /**
         * @var  CDbConnection $db
         */
        $db = Yii::app()->account_base_db;

        $privileges = [
            \common\library\privilege_v3\PrivilegeConstants::FUNCTIONAL_AI_CLASSIFY_OPPORTUNITY,
            \common\library\privilege_v3\PrivilegeConstants::FUNCTIONAL_AI_CLASSIFY_CUSTOMER,
            \common\library\privilege_v3\PrivilegeConstants::FUNCTIONAL_AI_CLASSIFY_EMAIL,
            ];

        $privileges = array_map(function ($item) {
            return \Util::escapeDoubleQuoteSql($item);
        }, $privileges);
        $privilegeStr = '\'' . implode("','", $privileges) . '\'';

        //没索引, 但可以接受, 数据量不多
        $clientIds = $db->createCommand("select distinct  client_id from tbl_client_privilege where privilege in ({$privilegeStr}) and enable_flag=1")->queryColumn();
        self::info("具有{$privilegeStr}功能的企业共有" . count($clientIds) . ' date:' . date('Y-m-d H:i:s'));
        $total = 0;
        foreach ( $clientIds as $clientId )
        {

            $db = ProjectActiveRecord::getDbByClientId($clientId);

            $sql = "select count(1) as count from tbl_ai_mail_classify where client_id={$clientId} and file_extract_call=-1 and create_time>='2019-11-01 00:00:00'";
            $count = $db->createCommand($sql)->queryScalar();

            $total += $count;

            self::info("AiFileCallFail clientId:{$clientId} count: {$count}");
        }


        self::info("执行完成 date:" . date('Y-m-d H:i:s'). 'total:' .$total);
    }

    public function actionTest110()
    {
        \common\library\account\service\LoginService::releaseLockAccount('<EMAIL>');
        die;
        User::setLoginUserById(********);

        $user = User::getLoginUser();
        $setting = new \common\library\setting\user\UserSetting($user->getClientId(), $user->getUserId(),\common\library\setting\user\UserSetting::PRIVATE_COMPANY_LIST_FIELD);
        $value = json_decode($setting->value, true);
        array_unshift($value, ['id' => 'name', 'width' => 260,]);
        $setting->value = $value;
        $setting->save();
        var_dump($value);die;
    }

    public function actionTest111()
    {
        $clientId  =15797;
        $eventId = ***********;
        $sclid = '61ae2c9d7caf0908bb46e4a94996299d';
        $listObj = new TrackSessionList($clientId);
        $listObj->setSiteSessionId($sclid);
        $sclidProfile = $listObj->find()[0];


        $eventListObj = new \common\library\google_ads\track\TrackSiteSessionEventList($clientId);
        $eventListObj->setSiteSessionId($sclid);
        $eventListObj->setEventId($eventId);
        $eventListObj->setFields(['event_id','site_id','site_session_id', 'gclid','type','title','url','refer_url','data','create_time']);
        $event = $eventListObj->find()[0];


        $data = array_merge($sclidProfile, $event);
//
//        $params = new \common\library\google_ads\track\Params();
//        $params->initByStorage($data);
//
////        dd($params->eventData);
//
//        $e = new \common\library\google_ads\track\ParamsExtract($params);

//        var_dump($params->eventData, $e->extract());
    }

    public function actionTest222()
    {
        $clientId = '14143';
        $gclidService = new \common\library\google_ads\track\GclidService($clientId);
//        $gclidService->setGclid('Cj0KCQjwpfHzBRCiARIsAHHzyZrzMeIH6aNGmsCHLamsAdaVjHeLKH5xyo2sDYtfshhLtjNUFSFSIpAaAhK2EALw_wcB');
//        $gclidService->setGclid('Cj0KCQjwpfHzBRCiARIsAHHzyZoMwFy6OCCUxdWG1ZCfvFcSSP8QvkI4HXQmrlpUKqOsKL-ijN-421saApW4EALw_wcB');
//        $gclidService->setGclid('EAIaIQobChMIsNHfvfm56AIVS-WaCh17aAdnEAAYASAAEgK4h_D_BwE');
//        $gclidService->setGclid('Cj0KCQjwyPbzBRDsARIsAFh15JY8ZTsc0D6lscAm-k5cYm7kl2AWkt47O12pA0ixPO_9zxJ7dQPhJNUaAkpHEALw_wcB');
        $gclidService->setGclid('Cj0KCQjwyPbzBRDsARIsAFh15JbuMrRfk_FoKL6crk2EOK7CTwYHVdWh0fGfmq_QUyb-PY61rnKXZrcaAjE8EALw_wcB');

        $info = $gclidService->findGclidReportInfo();
        var_dump($info);
    }

    public function actionTest333()
    {
        $redis = RedisService::getInstance('redis_queue');

//        $redis->select('1');
//        $ret = $redis->del([
//            'crm:error_queue',
//            'ai_opportunity_company',
//            'ai_opportunity_email'
//        ]);
//
//        var_dump($redis->get('crm:new_feed_count_55272870_12984209304'));
//
//        die;

//        $redis = Yii::app()->redis;
        $index = 0;
//        $redis->executeCommand('select', ['0']);

        $total = 0;
        do {

            $ret = $redis->scan($index, array('match'=>'crm:ip:*', 'count' =>'1000'));

//            $ret = $redis->executeCommand("scan", array($index, 'match', 'crm:ip:*', 'count', '1000'));

            $keys = $ret[1];

            if( !empty($keys) )
            {
                foreach ($keys as $key)
                {
                   $value =  $redis->get($key);
                   echo  "$key  => $value  \n";
                }
                $affect = $redis->del($keys);
//                $affect =0;
                echo "deleting index $index affect rows $affect\n";
            }


            $count = count($keys);
            $total += $count;
//            echo "scan index $index affect rows $count\n";
            $index = $ret[0];
        } while ($index);

        var_dump($count);

//


//        var_dump($ret);

        die;
    }


    public function actionCleanIndex($dryRun=1)
    {
        $deleteSetNos =[];
        $i = $dryRun * 100;
        $max = $i+100;
        for ($i; $i <= $max; $i++) {
            $deleteSetNos[] = $i;
        }

//        var_dump($deleteSetNos);die;
//        $db = \ExpClient::model()->getDbConnection();
//        $tableName = \ExpClient::model()->tableName();
//        $date = date('Y-m-d H:i:s');
//        $sql = "select set_no from $tableName where expired_at > '{$date}' and enable_flag=1 and locked=1 and ready=1";
//        $inUsingSetNos = $db->createCommand($sql)->queryColumn();
//
//        $deleteSetNos = array_diff($setNos, $inUsingSetNos);
//        echo "in using count:" . count($inUsingSetNos) . "\n";
        echo "delete count:" . count($deleteSetNos) . "\n";
        foreach ($deleteSetNos as $deleteSetNo) {
            $onlineParams = Yii::app()->params['elastic_search'];
            \Yii::app()->params['exp_elastic_search_index_postfix'] = 'exp_' . $deleteSetNo;
            $v5ElasticSearchOnlineDb = \Elasticsearch_5\ClientBuilder::fromConfig($onlineParams);
            $v5ElasticSearch = new \common\library\exp\V5ElasticSearch($v5ElasticSearchOnlineDb, $v5ElasticSearchOnlineDb);
            try{
                $v5ElasticSearch->deleteIndex();
            }catch (\Exception $exception )
            {
                self::info($exception->getMessage().$exception->getTraceAsString());
            }

        }
    }


    public function actionTestKey()
    {
        $redis = \common\library\cache\CacheRepository::instance('redis')->getDriver()->getConnection();
       $value =   $redis->getByMultipleKey('crm:table:{user_account}:account:<EMAIL>');

       var_dump($value);
    }

    public function actionUserLanguage()
    {
        $total = 0;
        $clientList = $this->getClientList();
        $resultPath = '/tmp/client-language-en.csv';
        @unlink($resultPath);
        $fp = fopen($resultPath, 'w');
        fputcsv($fp, ['client_id', 'client_name', 'count']);

        foreach ( $clientList as $client )
        {
            $clientId = $client['client_id'];
            $db = ProjectActiveRecord::getDbByClientId($clientId);
            $sql = "select count(1) as count from tbl_user_setting where client_id={$clientId} and  `key`='USER_LANGUAGE' and  `value`='en'";
            $count = $db->createCommand($sql)->queryScalar();

            self::info("client_id: {$clientId}  count:{$count}");
            $total += $count;

            if( $count )
            {
                $clientObj = new \common\library\account\Client($clientId);
                fputcsv($fp, [$clientId, $clientObj->full_name, $count]);
            }

        }

        fclose($fp);

        self::info("total: ".$total);
    }


    public function actionTestALibaba()
    {
        $sessionKey = '50002200317c0euabquFTEonrZpsh18f5cb28bykzFoqdvocZBetfKvwHFLBItfwMTSy';
        $buyerMemberId ='*********';
//        $buyerMemberId ='*********';
        $services = \common\library\alibaba\services\AlibabaTopClient::getInstance();
        $services->getClient()->appkey ='********';
        $services->getClient()->secretKey ='70b2dc18c5312add75fc1a312e00012d';
        $rsp = $services->getCustomer($sessionKey,$buyerMemberId);
        var_dump($rsp);
    }


    public function actionPassword()
    {
        $resultPath = '/tmp/client_password.csv';
        @unlink($resultPath);
        $fp = fopen($resultPath, 'w');
        fputcsv($fp, ['client_id', 'email', 'password', 'safe_login', 'name', 'position']);

        $passwordArr = [
            '1q2w3e4r',
            '1q2w3e4R',
            '1q2w3e',
            'crm123654',
            'q1w2e3R4',
            'Xiaomankf123456',
            'Smartkf123',
            'Prokf123',
            'Litekf123',
        ];

        $result =[];
        $db = UserAccount::model()->getDbConnection();

        $accountList = [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
        ];

        $email = array_map(function ($item) {
            return \Util::escapeDoubleQuoteSql($item);
        }, $accountList);
        $emailStr = '\'' . implode("','", $email) . '\'';

        $sql = 'select client_id, user_id,email, safe_login,family_name,second_name,position from tbl_user_info  where client_id in (7,17383) and enable_flag =1';
//        $sql .= ' and email  in ('.$emailStr.')';

        $userList = $db->createCommand($sql)->queryAll();
        foreach ($userList as $item )
        {
            $userId = $item['user_id'];
            $userAccount = UserAccount::findByUserId($userId);

            foreach ( $passwordArr as $password)
            {
                $hashPin = hash('sha256',  hash('sha256',$password) . $userAccount->pwd_salt);

                if( $hashPin == $userAccount->pwd_hash )
                {
//                    $result[$item['user_id']] =  ['client_id'=>  $item['client_id'], 'account'=>  $item['email'], 'password' => $password, 'safe_login' => $item['safe_login'], 'name' =>  $item['family_name'].' '.$item['second_name'], 'position' => $item['position']];
                    fputcsv($fp, [$item['client_id'], $item['email'], $password, $item['safe_login'], $item['family_name'].' '.$item['second_name'], $item['position']]);
                    self::info("简单密码: client_id: {$item['client_id']} {$item['email']}, {$password}, 安全登陆 : {$item['safe_login']} 姓名:{$item['family_name']}{$item['second_name']} 职位:{$item['position']} ");
                }
            }
        }

        fclose($fp);

    }

    public function actionDiff()
    {
        $clientId = '20294';
        $taskId = '***********';

        $adminUserId = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();
        User::setLoginUserById($adminUserId, $clientId);

        $task = new \common\library\alibaba\customer\AlibabaCustomerSyncTask($clientId, $taskId);
        $store = new \common\library\alibaba\store\AlibabaStore($task->client_id, $task->store_id);
        $sessionKey = $store->getAccessToken();
        if( empty($sessionKey ))
        {
            var_dump('111');
            die;
        }

        $listObj = new \common\library\alibaba\services\AlibabaCustomerList($sessionKey);
        $listObj->setPageSize(50);

        $number =0;
        $begin=0;
        $lastTime='1998-01-01';
        $totalCount=0;
        do
        {

            $listObj->setCustomerIdBegin($begin);
            $listObj->setLastSyncEndTime($lastTime);
            $rsp =  $listObj->find();
            $rsp = is_object($rsp)? \common\library\alibaba\services\AlibabaTopClient::object2array($rsp) :$rsp;
            if( !isset($rsp['data_list']) )
            {
                if( !$begin)
                {
                    throw new \RuntimeException('同步错误:'.json_encode($rsp));
                }
            }

            if( isset($rsp['total']) && !$begin )
            {
                $totalCount = $rsp['total'];
            }

            $dataList= $rsp['data_list']['customer_open_dto']??[];

            foreach ( $dataList as $data)
            {
                $begin= $data['customer_id'];
                $number++;
            }

        }while(!empty($dataList));

        var_dump($totalCount, $lastTime, $begin, $number);

    }

    public function actionDbStat($host)
    {
        $dbList = DbSet::model()->findAll('host=:host', [':host' => $host]);
        $totalCompanyCount = 0;
        $totalCustomerCount = 0;
        $totalLeadCount = 0;


        foreach ($dbList as $item)
        {
            $db = PgActiveRecord::getDbByDbSetId($item['set_id']);
            $companyCount = $db->createCommand('SELECT count(1) from tbl_company')->queryScalar();
            $customerCount = $db->createCommand('SELECT count(1) from tbl_customer')->queryScalar();
            $leadCount = $db->createCommand('SELECT count(1) from tbl_lead')->queryScalar();
            self::info("{$item['name']} company: {$companyCount} customer: {$customerCount} lead: {$leadCount}");

            $totalCompanyCount += $companyCount;
            $totalCustomerCount += $customerCount;
            $totalLeadCount += $leadCount;
        }

        self::info("{$host} company: {$totalCompanyCount} customer: {$totalCustomerCount} lead: {$totalLeadCount}");
    }


    public function actionDbStat1($host)
    {
        $dbList = DbSet::model()->findAll('host=:host', [':host' => $host]);
//        $dbList = DbSet::model()->findAll('type=:type', [':type' => DbSet::TYPE_MYSQL]);
        $totalCount = 0;
        foreach ($dbList as $item)
        {
            try
            {
                if( strpos($item['name'], 'exp'))
                    continue;

                $db = ProjectActiveRecord::getDbByDbSetId($item['set_id']);
                $count = $db->createCommand('SELECT count(1) from tbl_alibaba_customer_sync_task where status=2')->queryScalar();
                self::info("{$item['name']} count: {$count}");

                $totalCount += $count;
                $db->setActive(false);
            }catch (\Exception $e)
            {

            }

        }

        self::info("{$host} } total: {$totalCount} ");
    }

    public function actionTestK8()
    {
        $sessionKey = '50002800747df1Rer7MFOyRseeboteovESRiXhaw3lvvdjfuiV0gtPhkYTc15df28e1b';
        $tradeId = '65885382001022011';
        $aliOrder = new \common\library\alibaba\services\AlibabaOrder($sessionKey,$tradeId);
        $data = $aliOrder->getInfo([\common\library\alibaba\services\AlibabaOrder::DATA_SELECT_STATUS_ACTION,\common\library\alibaba\services\AlibabaOrder::DATE_SELECT_DRAFT_ROLE]);
        if(!isset($data['value']) || !$data['value'])
        {
            var_dump(111);
        }
        var_dump($data);
    }

    public function actionTestK9()
    {
        $redis = \common\library\alibaba\services\queue\QueueHelper::getRedis(true, true);
// zset 返回小于当前时间的消息
        var_dump(111);
        $ret = $redis->zrangebyscore(\common\library\alibaba\services\queue\QueueHelper::DELAY_QUEUE_KEY, 0, time(), ['limit' =>  [0,1]]);
        //获取消息 && 从队列中移除该消息
        $member = $ret[0];
        $memoryUsage = round(memory_get_usage(true) / (1024 * 1024), 2) . ' MB';
        var_dump($ret,$memoryUsage);
//        $msgRet = $redis->zrem(QueueHelper::DELAY_QUEUE_KEY, $member);
        $data = json_decode($member, true);
        var_dump($data);
    }

    public function actionTestK12()
    {

        $services = \common\library\alibaba\services\AlibabaTopClient::getInstance();
        $req = new AlibabaMemberInfoGetRequest();
        $req->setBizType(\common\library\alibaba\Constant::ALIBABA_XIAOMAN_KEY);
        $rsp =  $services->execute($req, '50002401a46lulypdXbFQgrsVGhfRjZHs0DdYBPrcKu2vQmXx4xaxDqlPr132e85b7Cr');
        var_dump($rsp);
    }

    public function actionTestK13()
    {
        $customerId = 'customer_id';
        $pageSize = 50;
        $begin =0;
        $total =0;
        $i = 0;
        $sessionKey = '50002100209zd7ZoabUuh1e008cb2tDlno0vsDC1HzlqyDRvcedbt4OrUHNE41TqULYd';
        $list = new \common\library\alibaba\services\AlibabaCustomerList($sessionKey);
        $list->setPageSize($pageSize);
        do
        {
            $list->setCustomerIdBegin($begin);

            $rsp = $list->find();
            if( $begin ==0 )
            {
                $total = $rsp['total'];
            }

            $dataList= $rsp['data_list']['customer_open_dto']??[];
            foreach ($dataList as $item)
            {
                $i++;
                $begin = $item['customer_id'];

//                if( $item['company_name'] =='Aera Jewelry')
//                {
//                    var_dump($item);die;
//                }
//
//                foreach ($item['contact_open_co_list']['contact_open_co']??[] as $elem)
//                {
//                    $email = $elem['email_list']['string'][0]??'';
//                    self::info($email);
//                    if( $email == '<EMAIL>')
//                    {
//                        var_dump($item);die;
//                    }
//                }



//                if( $item['annual_procurement'] != "NA")
//                    dd($item);
//                if( $customerId == $item['customer_id'])
//                {
//                    dd($begin,$item);
//                }
//
//                $alibabaCompany = new \common\library\alibaba\services\AlibabaCompany($sessionKey, $item['customer_id']);
//                $noteRsp = $alibabaCompany->getNote(1 ,10);
//                if( !empty($noteRsp))
//                {
//                    dd($item, $noteRsp);
//                }


            }

            var_dump("$begin $i/$total");
        }while(!empty($dataList));
    }

    public function actionTestNote()
    {
        User::setLoginUserById('55257782');
        $alibabaCompanyId = '1891192205';
        $sessionKey = '50002000705df1Rer19e2fffd7HoO4RPAD2JVglxBvqHXhFv5pxWEnIvm0TsXPgc15Ve';
        $alibabaCompany = new \common\library\alibaba\services\AlibabaCompany($sessionKey, $alibabaCompanyId);
        $data = $alibabaCompany->getInfo();

        $clientId = User::getLoginUser()->getClientId();
        $userId = User::getLoginUser()->getUserId();
        $storeId  = '*********';

        $processor = new \common\library\alibaba\customer\AlibabaCustomerSyncProcessor($clientId, $userId, $storeId);
        $processor->setSessionKey($sessionKey);
        $processor->initDefaultSetting();
        $res = $processor->processWithLock($alibabaCompanyId, $data);
        var_dump($processor->getError(), $processor->getResult());
    }

    public function actionTestE()
    {
        $taskId = '1107461738';
        $user = User::getUserObjectByEmail('<EMAIL>');
        User::setLoginUserById($user->getUserId());
        $task = new \common\library\alibaba\customer\AlibabaCustomerSyncTask(User::getLoginUser()->getClientId(), $taskId);
        $task->status =1;
        $task->save();
        $executor = new \common\library\alibaba\customer\AlibabaCustomerSyncTaskExecutor($task);
        $executor->run();
        var_dump($task->getAttributes());
    }

    public function actionTestA()
    {
        var_dump(time()> 1621469233,  1621469233, time());
        $sessionKey = '50002200243zd7ZoabtuIulLGwyovAg2hXgLwhrpHdgiq9q0tEKcjPn16612b41UUNXg';
        $sessionKey = '50002800546WslhqLeU8JIo1uvhdbnVgMRerQFadbp5pWvkjmulkxdQHBM1434f9f9Zr';
        $services = \common\library\alibaba\services\AlibabaTopClient::getInstance();
        $services->getClient()->gatewayUrl = \common\library\alibaba\services\AlibabaTopClient::getGatewaySslUrl();
        $req = new AlibabaMemberInfoGetRequest();
//        $req = new AlibabaSellerTradeQueryDrafttypeRequest();
        $req->setBizType(\common\library\alibaba\Constant::ALIBABA_XIAOMAN_KEY);
        $rsp =  $services->getClient()->execute($req, $sessionKey);
        var_dump($rsp);
    }
    public function actionTestDb()
    {
        $count = 0;
        do
        {
            $clientId = 36000;
            $db = ProjectActiveRecord::reuseDbByClientId($clientId);
            $resClientId = $db->createCommand('select client_id from tbl_mail where client_id='.$clientId.' limit 1')->queryScalar();
            if( $clientId != $resClientId ){
                var_dump('error mysql'.$clientId);die;
            }
            var_dump('mysql:'.$clientId.' '. $resClientId);

            if( rand(1,5) == 1 )
            {
                var_dump($clientId. ' mysql Active false');
                $db->setActive(false);
            }

            $db = PgActiveRecord::reuseDbByClientId($clientId);
            $resClientId = $db->createCommand('select client_id from tbl_company where client_id='.$clientId.' limit 1')->queryScalar();
            if( $clientId != $resClientId ){
                var_dump('error pg'.$clientId);die;
            }
            var_dump('pgsql:'.$clientId.' '. $resClientId);
            if( rand(1,5) == 1 )
            {
                var_dump($clientId. ' pgsql Active false');
                $db->setActive(false);
            }

            $clientId = 37602;
            $db = ProjectActiveRecord::reuseDbByClientId($clientId);
            $resClientId = $db->createCommand('select client_id from tbl_mail where client_id='.$clientId.' limit 1')->queryScalar();
            if( $clientId != $resClientId ){
                var_dump('error mysql'.$clientId);die;
            }

            var_dump('mysql:'.$clientId.' '. $resClientId);
            if( rand(1,5) == 1 )
            {
                var_dump($clientId. ' mysql Active false');
                $db->setActive(false);
            }

            $db = PgActiveRecord::reuseDbByClientId($clientId);
            $resClientId = $db->createCommand('select client_id from tbl_company where client_id='.$clientId.' limit 1')->queryScalar();
            if( $clientId != $resClientId ){
                var_dump('error pg'.$clientId);die;
            }
            var_dump('pgsql:'.$clientId.' '. $resClientId);
            if( rand(1,5) == 1 )
            {
                var_dump($clientId. ' pgsql Active false');
                $db->setActive(false);
            }

            $clientId = 7;
            $db = ProjectActiveRecord::reuseDbByClientId($clientId);
            $resClientId = $db->createCommand('select client_id from tbl_mail where client_id='.$clientId.' limit 1')->queryScalar();
            if( $clientId != $resClientId ){
                var_dump('error mysql'.$clientId);die;
            }
            var_dump('mysql:'.$clientId.' '. $resClientId);

            if( rand(1,5) == 1 )
            {
                var_dump($clientId. ' mysql Active false');
                $db->setActive(false);
            }

            $db = PgActiveRecord::reuseDbByClientId($clientId);
            $resClientId = $db->createCommand('select client_id from tbl_company where client_id='.$clientId.' limit 1')->queryScalar();
            if( $clientId != $resClientId ){
                var_dump('error pg'.$clientId);die;
            }
            var_dump('pgsql:'.$clientId.' '. $resClientId);
            if( rand(1,5) == 1 )
            {
                var_dump($clientId. ' pgsql Active false');
                $db->setActive(false);
            }

            $clientId = 35780;
            $db = ProjectActiveRecord::reuseDbByClientId($clientId);
            $resClientId = $db->createCommand('select client_id from tbl_mail where client_id='.$clientId.' limit 1')->queryScalar();
            if( $clientId != $resClientId ){
                var_dump('error mysql'.$clientId);die;
            }
            var_dump('mysql:'.$clientId.' '. $resClientId);

            if( rand(1,5) == 1 )
            {
                var_dump($clientId. ' mysql Active false');
                $db->setActive(false);
            }

            $db = PgActiveRecord::reuseDbByClientId($clientId);
            $resClientId = $db->createCommand('select client_id from tbl_company where client_id='.$clientId.' limit 1')->queryScalar();
            if( $clientId != $resClientId ){
                var_dump('error pg'.$clientId);die;
            }
            var_dump('pgsql:'.$clientId.' '. $resClientId);
            if( rand(1,5) == 1 )
            {
                var_dump($clientId. ' pgsql Active false');
                $db->setActive(false);
            }


            $clientId = 36827;
            $db = ProjectActiveRecord::reuseDbByClientId($clientId);
            $resClientId = $db->createCommand('select client_id from tbl_mail where client_id='.$clientId.' limit 1')->queryScalar();
            if( $clientId != $resClientId ){
                var_dump('error mysql'.$clientId);die;
            }
            var_dump('mysql:'.$clientId.' '. $resClientId);

            if( rand(1,5) == 1 )
            {
                var_dump($clientId. ' mysql Active false');
                $db->setActive(false);
            }

            $db = PgActiveRecord::reuseDbByClientId($clientId);
            $resClientId = $db->createCommand('select client_id from tbl_company where client_id='.$clientId.' limit 1')->queryScalar();
            if( $clientId != $resClientId ){
                var_dump('error pg'.$clientId);die;
            }
            var_dump('pgsql:'.$clientId.' '. $resClientId);
            if( rand(1,5) == 1 )
            {
                var_dump($clientId. ' pgsql Active false');
                $db->setActive(false);
            }
        }while(++$count <200);
    }


    public function actionOrderMem()
    {
        User::setLoginUserById(11859021);
        $clientId = User::getLoginUser()->getClientId();
        $userId = User::getLoginUser()->getUserId();
        $i =0;
        do
        {
            $order = new \common\library\invoice\Order($userId,1116346586);
            $order->save();
            var_dump(round(memory_get_usage(true) / 1024, 2));
            $i++;
        }while($i <100);

        var_dump(round(memory_get_usage(true) / 1024, 2));

    }

    public function actionFindField()
    {
        $clientList =   $this->getClientList();
        foreach ($clientList as $item ){
            $clientId = $item['client_id'];

            //灰度期间特殊处理, 上线后去掉
            if( !\common\library\GreyEnvHelper::isGreyClient($clientId))
            {
                continue;
            }

            $adminUserId = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();
            if(!$adminUserId)
            {
                continue;
            }

            User::setLoginUserById($adminUserId);

            $fieldList = new \common\library\custom_field\FieldList($clientId);
            $fieldList->setType(\Constants::TYPE_CUSTOMER);
            $fieldList->setDisableFlag(null);
            $fieldList->setEditDefault(null);
            $fieldList->setId(['email']);
            $fieldList->setFields(['id', 'name', 'type', 'field_type', 'unique_check', 'unique_prevent', 'unique_message']);
            $fieldList = $fieldList->find();


            foreach ($fieldList as $k => $field)
            {
                if (!$field['unique_check'])
                {
                    self::info("关闭了邮箱判重 {$clientId} {$field['id']} {$field['unique_check']}");
                }
            }
        }

    }


    public function actionFindMail()
    {
        $resultPath = '/tmp/钓鱼邮件标签情况.csv';
        @unlink($resultPath);
        $fp = fopen($resultPath, 'w');
        fputcsv($fp, ['client_id', 'user_id', '标签id', '标签名称', '邮件数']);

        $total = 0;
        $dbList = \common\library\account\service\DbService::getMysqlList();
        foreach ($dbList as $item ) {
            $setId = $item['set_id'];

            $db = ProjectActiveRecord::getDbByDbSetId($setId);
            $sql = "select tag_id, client_id,user_id ,tag_name,enable_flag from tbl_general_tag where tag_name like '%骗子%' or  tag_name like '%钓鱼%'";
            $list = $db->createCommand($sql)->queryAll();

            foreach ($list as $elem )
            {
                $selectSql = "select count(1)   from tbl_mail_tag_assoc where user_id={$elem['user_id']} and tag_id={$elem['tag_id']}";
                $mailCount= $db->createCommand($selectSql)->queryScalar();
                $total +=  $mailCount;
                fputcsv($fp, [$elem['client_id'], $elem['user_id'], $elem['tag_id'], $elem['tag_name'], $mailCount]);
                self::info("{$elem['tag_id']} {$elem['client_id']} {$elem['user_id']} ,{$elem['tag_name']}, {$elem['enable_flag']} {$mailCount}");
            }
        }

        self::info($total);
    }

    public function actionCustomer()
    {
        $clientId = '40469';
//        $userId = '55343545';
        $userId = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();
        User::setLoginUserById($userId);
        $company_id = 60698913857;
        $ret =  \common\library\alibaba\customer\AlibabaCustomerService::syncSingleCustomer($clientId, $userId, 0, 0, $company_id);
        var_dump($ret);die;
    }

    public function actionExport($client_id=0)
    {
        ini_set("memory_limit", "15000M");
        $resultPath = '/tmp/桌面通知类型开启情况.csv';
        @unlink($resultPath);
        $fp = fopen($resultPath, 'w');
        fputcsv($fp, ['client_id', 'user_id', '通知类型', '是否开启', '默认情况']);
        $clientList = $this->getClientList($client_id);
        $key = \common\library\setting\user\UserSetting::NOTIFICATION_BROWSER_PUSH_SETTING;
        $mapName = [
            Browser::TYPE_MAIL_TODO_REMIND     => '待处理邮件通知',
            Browser::TYPE_NEW_MAIL             => '新邮件通知',
            Browser::TYPE_NEW_ALIBABA_MESSAGE  => '阿里巴巴国际站新消息提醒',
            Browser::TYPE_MAIL_AI_EVENTS       => '自动化: OKKI 自动创建/更新产品通知',
            Browser::TYPE_AI_CLASSIFY_NEW_LEAD => '自动化: OKKI 自动创建线索通知',
        ];
        foreach ($clientList as $client)
        {
            $clientId = $client['client_id'];
            $db = ProjectActiveRecord::getDbByClientId($clientId);
            $list = $db->createCommand("select client_id,user_id,`value` from tbl_user_setting where client_id = {$clientId} and `key` = '{$key}'")->queryAll();
            foreach ($list as $item )
            {
                $value = json_decode($item['value'], true);
                fputcsv($fp, [$item['client_id'], $item['user_id'], $mapName[Browser::TYPE_MAIL_TODO_REMIND], $value[Browser::TYPE_MAIL_TODO_REMIND]??1, 1]);
                fputcsv($fp, [$item['client_id'], $item['user_id'], $mapName[Browser::TYPE_NEW_MAIL], $value[Browser::TYPE_NEW_MAIL]??1, 1]);
                fputcsv($fp, [$item['client_id'], $item['user_id'], $mapName[Browser::TYPE_NEW_ALIBABA_MESSAGE], $value[Browser::TYPE_NEW_ALIBABA_MESSAGE]??1, 1]);
                fputcsv($fp, [$item['client_id'], $item['user_id'], $mapName[Browser::TYPE_MAIL_AI_EVENTS], $value[Browser::TYPE_MAIL_AI_EVENTS]??1, 1]);
                fputcsv($fp, [$item['client_id'], $item['user_id'], $mapName[Browser::TYPE_AI_CLASSIFY_NEW_LEAD], $value[Browser::TYPE_AI_CLASSIFY_NEW_LEAD]??1, 1]);
            }

            self::info("client_id: {$clientId} count: ".count($list));

        }

        fclose($fp);
    }

    public function actionProduct1()
    {
        $clientId = '6688';
        $storeId = 244059865;

       $token =  (new \common\library\alibaba\store\AlibabaStore($clientId, $storeId))->access_token;
        $aliGroupId = 820140521;
        $groupInfo = new AlibabaProductGroup($token, $aliGroupId);
        $currentGroupInfo = $groupInfo->getInfo();
        var_dump($currentGroupInfo);die;
        $list = new \common\library\alibaba\services\AlibabaProductList($token);
        $list->setPageSize(50);
        $rsp = $list->find();
        var_dump($rsp);

    }

    public function actionQueue()
    {
        $params = [
            'host' => 'r-bp1l6s0f6ng45ty02g.redis.rds.aliyuncs.com',
            'port' => '6379',
            'database' => '0',
            'password' => 'HJ3SEomeyP7dSX4QrwnBksUkmJgHiN',
            'read_write_timeout' => 0,
            'persistent' => true
        ];
        $failRedis =  new  \common\components\PredisClient($params, []);
        self::info("fail redis: retry key count:".($failRedis->zcard(\common\library\report\sensors\SensorsReportConsumer::SENSORS_EVENT_RETRY_KEY)));
        self::info("fail redis: key count:".($failRedis->llen(\common\library\report\sensors\SensorsReportConsumer::SENSORS_EVENT_KEY)));

        $redis = RedisService::queuePersistent();
        self::info("real redis: retry key count:".($redis->zcard(\common\library\report\sensors\SensorsReportConsumer::SENSORS_EVENT_RETRY_KEY)));
        self::info("real redis: key count:".($redis->llen(\common\library\report\sensors\SensorsReportConsumer::SENSORS_EVENT_KEY)));

        $count = 0;
        while (true)
        {
            $ret = $redis->brpop([\common\library\report\sensors\SensorsReportConsumer::SENSORS_EVENT_KEY], 0);
            if($ret)
            {
                try
                {
                    if( !empty($ret[1]) )
                    {
                        $failRedis->lpush(\common\library\report\sensors\SensorsReportConsumer::SENSORS_EVENT_KEY, [$ret[1]]);
                    }
                }catch (\Throwable $e)
                {
                    $code = $e->getCode();
                    $errorMsg = $e->getMessage();
                    self::info("Error:code={$code},errorMsg={$errorMsg} data:".$ret[1]);
                }
            }

            $count++;
            if( $count % 1000 == 0)
            {
                self::info("push fail redis: ".$count);
            }
        }

    }

    public function actionQueueMu()
    {
        $pool = new \Swoole\Process\Pool(50);
        $pool->set(['enable_coroutine' => true]);
        $pool->on('WorkerStart', function (\Swoole\Process\Pool $pool, $workerId) {
            $this->actionQueue();
        });
        $pool->on('WorkerStop', function (\Swoole\Process\Pool $pool, $workerId) {
            echo("[Worker #{$workerId}] WorkerStop\n");
        });
        $pool->start();
    }

    public function actionQueueStat()
    {
        $params = [
            'host' => 'r-bp1l6s0f6ng45ty02g.redis.rds.aliyuncs.com',
            'port' => '6379',
            'database' => '0',
            'password' => 'HJ3SEomeyP7dSX4QrwnBksUkmJgHiN',
            'read_write_timeout' => 0,
            'persistent' => true
        ];
        $failRedis = new  \common\components\PredisClient($params, []);
        self::info("fail redis: retry key count:" . ($failRedis->zcard(\common\library\report\sensors\SensorsReportConsumer::SENSORS_EVENT_RETRY_KEY)));
        self::info("fail redis: key count:" . ($failRedis->llen(\common\library\report\sensors\SensorsReportConsumer::SENSORS_EVENT_KEY)));

        $redis = RedisService::queuePersistent();
        self::info("real redis: retry key count:" . ($redis->zcard(\common\library\report\sensors\SensorsReportConsumer::SENSORS_EVENT_RETRY_KEY)));
        self::info("real redis: key count:" . ($redis->llen(\common\library\report\sensors\SensorsReportConsumer::SENSORS_EVENT_KEY)));
    }

    public function actionTestProduct()
    {
        User::setLoginUserById(********);
        $search  = new \common\library\product_v2\search\ProductSearcher(User::getLoginUser()->getClientId());
        $search->paramsMapping([
            'product_model_keyword' => '7210036'
        ]);

        $ids = $search->findIds();
        var_dump($ids);
    }

    public function actionTestError()
    {
        for ($i=0 ; $i<10; $i++)
        {
            $e = new ProcessException('测试钉钉通知, 请忽略');
            \common\library\report\error\ErrorReport::phpError(new \CExceptionEvent(null, $e), $e->getTrace());
        }
    }


    public function actionTestSend()
    {
        $accessToken = '7238e98e575459a388e5b70559b6162c836d4e82a10ad197c310f8cfd157749a';
        $message = \common\library\report\dingtalk\DingTalkRobot::create($accessToken);
        $nickname ='618';
        $typeName = '阿里';
        $sourceName= '信保订单';
        $groupName='河南亮橙';
        $companyName='中宝电气有限公司';
        $currentSymbol='$';
        $amount='618';
        $msgText = "## 英雄之战--喜报 \n ![](https://tfile.xiaoman.cn/alibaba-sales-race/banner.png)\n**{$nickname}** 新签一笔{$typeName}{$sourceName}订单，大家恭喜TA吧！\n#### 所属军团：{$groupName} \n#### 所属公司：{$companyName} \n #### 订单金额：{$currentSymbol}{$amount} \n > 本消息由阿里巴巴委托小满科技生成";
        $at = $message->sendBotMessage([
            "msgtype"=> "markdown",
            "markdown" => [
                'title' => '英雄之战--喜报',
                "text"=> $msgText
            ]
        ]);

        var_dump($at);
    }

    public function actionCiq()
    {

        $a= new \common\library\product_v2\crawl\ProductCrawApi(6688, ********);
        $a->saveAuthInfo('eyJ2ZXIiOiIxLjAiLCJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.************************************************************************.wVjNTZxacwiKOY-neUW-2utfsxK5f-ry_ASMV2hIRbw');
        die;

        $clientId = '43414';
        $userIds = \common\library\account\Helper::getRealUserIds($clientId);
        foreach (\common\library\ciq\Constant::SEARCH_TYPE_LIST as $type)
        {
            foreach ($userIds as $userId)
            {
               $list = \common\library\ciq\CiqService::listSearchHistory($clientId, $userId, $type);
               foreach ($list as $item)
               {
                   self::info($item);
               }
            }
        }
    }

    public function actionFixCustomerOrderTime($greyNum)
    {
        ini_set("memory_limit", "1500M");
        $resultPath = "/tmp/fixCustomerOrderTime-{$greyNum}.csv";
        @unlink($resultPath);
        $fp = fopen($resultPath, 'w');
        fputcsv($fp, ['client_id', 'company_id', 'new_order_time', 'old_order_time']);
        $clientIds =  array_column($this->getClientList(0, false, null, null, 0,0,$greyNum), 'client_id');
        foreach ($clientIds as $clientId)
        {
            $mysql = ProjectActiveRecord::getDbByClientId($clientId);
            $pgsql = PgActiveRecord::getDbByClientId($clientId);

            $tm1 =    \CustomerOptionService::checkReference($clientId, 'refer_contact_send_message');
            $tm2 =    \CustomerOptionService::checkReference($clientId, 'refer_contact_receive_message');
            $referModified =    \CustomerOptionService::checkReference($clientId, 'refer_modified_data');
            $tmFlag = $tm1||$tm2;

            $groupData = \common\library\group\Helper::getCustomerGroupMoveToPublicSetting($clientId);
            $groupMap = [];
            $startTimeMap = [];
            $moveGroup = [];

            foreach ($groupData as $elem)
            {
                $groupMap[$elem['id']] = intval($elem['public_time']) * 86400;
                $startTimeMap[$elem['id']] = strtotime(date('Y-m-d',strtotime($elem['start_public_time'])));//确保是00:00:00
                if($elem['public_time'])
                {
                    $moveGroup[] = $elem['id'];
                }

            }

            $companyList = $pgsql->createCommand("select company_id,ali_store_id, order_time, group_id,edit_time,user_id from tbl_company where client_id=:client_id and order_time=edm_time and edm_time >= '2021-11-05 00:00:00' and edm_time <= '2021-11-05 08:43:00' and is_archive = 1")
                ->queryAll(true,[':client_id' => $clientId]);

            if( empty($companyList) )
                continue;

            foreach ($companyList as $item)
            {
                if( $item['ali_store_id'] !='{}'  && $tmFlag)
                {
                    self::info("continue  client: {$clientId}, company_id: {$item['company_id']} store_id:{$item['ali_store_id']} tm {$tmFlag}");
                    continue;
                }

                $maxTime = $pgsql->createCommand('select max(create_time) from tbl_dynamic_trail where client_id=:client_id and company_id=:company_id and enable_flag=1')
                    ->queryScalar([
                    ':client_id' => $clientId,
                    ':company_id' => $item['company_id']
                ]);

                $maxTimeInt = $maxTime ? strtotime($maxTime):0;
                if( $referModified && strtotime($item['edit_time']) > $maxTimeInt)
                {
                    $maxTime = $item['edit_time'];
                    $maxTimeInt = $maxTime ? strtotime($maxTime):0;
                }

                $publicFlag = $item['user_id'] != '{}' && isset($groupMap[$item['group_id']]) && $groupMap[$item['group_id']]>0;

                if( $publicFlag &&  ($maxTimeInt + $groupMap[$item['group_id']] < (time() - 3 * 86400))  )
                {
                    self::info("continue  client: {$clientId}, company_id: {$item['company_id']}  public max: {$maxTime} {$groupMap[$item['group_id']]}");
                    continue;
                }

                if( $publicFlag  && $maxTimeInt <= time() - 30*86400)
                {
                    self::info("continue  client: {$clientId}, company_id: {$item['company_id']}  30 day  {$maxTime} ");
                    continue;
                }


                if( $item['user_id'] != '{}' &&  $maxTimeInt <= time() - 90*86400)
                {
                    self::info("continue  client: {$clientId}, company_id: {$item['company_id']}  90 day  {$maxTime} ");
                    continue;
                }

                if( $maxTimeInt <=0 )
                {
                    self::info("continue  client: {$clientId}, company_id: {$item['company_id']}  0 day  {$maxTime} ");
                }

                $ret = 0;
                $ret =  $pgsql->createCommand("update tbl_company set order_time=:time where company_id=:company_id and order_time=edm_time and edm_time >= '2021-11-05 00:00:00' and edm_time <= '2021-11-05 08:43:00'")
                    ->execute([
                        ':company_id' => $item['company_id'],
                        ':time' => $maxTime
                    ]);

                fputcsv($fp, [$clientId, $item['company_id'], $maxTime,$item['order_time']]);
                self::info("update order_time client: {$clientId}, company_id: {$item['company_id']}, order_time: {$item['order_time']}  => {$maxTime} ret: {$ret}");
            }

            User::cleanUserMap();
            \common\library\account\Client::cleanCacheMap();
            ProjectActiveRecord::releaseDbByClientId($clientId);
            PgActiveRecord::releaseDbByClientId($clientId);
        }

        @fclose($fp);

    }


    public function actionDumpPlatformProductClient()
    {
        $date = date('Y-m-d');
        $resultPath = "/tmp/dumpPlatformProductClient-{$date}.csv";
        @unlink($resultPath);
        $fp = fopen($resultPath, 'w');
        fputcsv($fp, ['client_id', 'relation_count']);

        $clientIds =  array_column($this->getClientList(0, false, null, null, 0,0), 'client_id');
        foreach ($clientIds as $clientId)
        {
            $pgsql = PgActiveRecord::getDbByClientId($clientId);
            $count = $pgsql->createCommand("select count(1) from tbl_platform_product_relation where client_id={$clientId}")->queryScalar();
            if( !$count )
                continue;

            self::info("client: {$clientId} count: {$count}");
            fputcsv($fp, [$clientId, $count]);
            User::cleanUserMap();
            \common\library\account\Client::cleanCacheMap();
            ProjectActiveRecord::releaseDbByClientId($clientId);
            PgActiveRecord::releaseDbByClientId($clientId);
        }
    }

    public function actionCleanInquiryTrailRecordQueue()
    {
        $count = \RedisService::queuePersistent()->llen(\common\library\cms\inquiry\viewproduct\InquiryViewProductService::INQUIRY_TRAIL_QUEUE_KEY);
        self::info("len: {$count}");
        \RedisService::queuePersistent()->del([\common\library\cms\inquiry\viewproduct\InquiryViewProductService::INQUIRY_TRAIL_QUEUE_KEY]);
    }

    public function actionLog()
    {
        LogUtil::info('cayley_test');
    }

    public function actionGreyFeRoute()
    {
        $data = array (
            0 =>
                array (
                    'config_name' => 'crm-fpm',
                    'path_prefixes' => '/',
                    'cluster' => 'crm-web-fe-shell',
                    'port' => '8080',
                    'create_time' => '2022-12-20 14:31:50',
                    'update_time' => '2022-12-20 14:31:50',
                ),
            1 =>
                array (
                    'config_name' => 'crm-fpm',
                    'path_prefixes' => '/assistant_subapp',
                    'cluster' => 'crm-flutter-fe-assistant',
                    'port' => '8080',
                    'enable_flag' => '1',
                    'create_time' => '2023-09-19 11:37:42',
                    'update_time' => '2023-09-19 11:38:00',
                ),
            2 =>
                array (
                    'config_name' => 'crm-fpm',
                    'path_prefixes' => '/common_qiankun',
                    'cluster' => 'crm-web-fe-common-qiankun',
                    'port' => '8080',
                    'enable_flag' => '1',
                    'create_time' => '2022-12-20 14:34:13',
                    'update_time' => '2022-12-20 14:34:13',
                ),
            3 =>
                array (
                    'config_name' => 'crm-fpm',
                    'path_prefixes' => '/crm_dist',
                    'cluster' => 'crm-web-fe-customer',
                    'port' => '8080',
                    'enable_flag' => '1',
                    'create_time' => '2022-12-20 14:32:24',
                    'update_time' => '2022-12-20 14:32:24',
                ),
            4 =>
                array (
                    'config_name' => 'crm-fpm',
                    'path_prefixes' => '/crm_subapp',
                    'cluster' => 'crm-web-fe-galio-crm',
                    'port' => '8080',
                    'enable_flag' => '1',
                    'create_time' => '2023-03-16 16:38:06',
                    'update_time' => '2023-03-16 16:38:06',
                ),
            5 =>
                array (
                    'config_name' => 'crm-fpm',
                    'path_prefixes' => '/database_subapp',
                    'cluster' => 'crm-web-fe-galio-database',
                    'port' => '8080',
                    'enable_flag' => '1',
                    'create_time' => '2023-04-25 11:49:00',
                    'update_time' => '2023-04-25 11:49:00',
                ),
            6 =>
                array (
                    'config_name' => 'crm-fpm',
                    'path_prefixes' => '/dx_dist',
                    'cluster' => 'crm-web-fe-discovery',
                    'port' => '8080',
                    'enable_flag' => '1',
                    'create_time' => '2022-12-20 14:33:23',
                    'update_time' => '2022-12-20 14:33:23',
                ),
            7 =>
                array (
                    'config_name' => 'crm-fpm',
                    'path_prefixes' => '/dx_subapp',
                    'cluster' => 'crm-web-fe-galio-dx',
                    'port' => '8080',
                    'enable_flag' => '1',
                    'create_time' => '2023-06-05 14:24:33',
                    'update_time' => '2023-06-05 14:24:33',
                ),
            8 =>
                array (
                    'config_name' => 'crm-fpm',
                    'path_prefixes' => '/fb_subapp',
                    'cluster' => 'crm-web-fe-facebook',
                    'port' => '8080',
                    'enable_flag' => '1',
                    'create_time' => '2023-01-06 14:06:41',
                    'update_time' => '2023-01-06 14:06:41',
                ),
            9 =>
                array (
                    'config_name' => 'crm-fpm',
                    'path_prefixes' => '/goal_subapp',
                    'cluster' => 'crm-web-fe-galio-goal',
                    'port' => '8080',
                    'enable_flag' => '1',
                    'create_time' => '2023-03-16 16:38:34',
                    'update_time' => '2023-03-16 16:38:34',
                ),
            10 =>
                array (
                    'config_name' => 'crm-fpm',
                    'path_prefixes' => '/leads_subapp',
                    'cluster' => 'crm-web-fe-leads',
                    'port' => '8080',
                    'enable_flag' => '1',
                    'create_time' => '2023-01-06 14:07:01',
                    'update_time' => '2023-01-06 14:07:01',
                ),
            11 =>
                array (
                    'config_name' => 'crm-fpm',
                    'path_prefixes' => '/mail_dist',
                    'cluster' => 'crm-web-fe-mail',
                    'port' => '8080',
                    'enable_flag' => '1',
                    'create_time' => '2022-12-20 14:32:41',
                    'update_time' => '2022-12-20 14:32:41',
                ),
            12 =>
                array (
                    'config_name' => 'crm-fpm',
                    'path_prefixes' => '/mail_subapp',
                    'cluster' => 'crm-web-fe-galio-mail',
                    'port' => '8080',
                    'enable_flag' => '1',
                    'create_time' => '2023-01-06 14:08:38',
                    'update_time' => '2023-01-06 14:08:38',
                ),
            13 =>
                array (
                    'config_name' => 'crm-fpm',
                    'path_prefixes' => '/mk_dist',
                    'cluster' => 'crm-web-fe-marketing',
                    'port' => '8080',
                    'enable_flag' => '1',
                    'create_time' => '2022-12-20 14:33:42',
                    'update_time' => '2022-12-20 14:33:42',
                ),
            14 =>
                array (
                    'config_name' => 'crm-fpm',
                    'path_prefixes' => '/mk_subapp',
                    'cluster' => 'crm-web-fe-galio-mk',
                    'port' => '8080',
                    'enable_flag' => '1',
                    'create_time' => '2023-07-19 11:01:56',
                    'update_time' => '2023-07-19 11:01:56',
                ),
            15 =>
                array (
                    'config_name' => 'crm-fpm',
                    'path_prefixes' => '/new_discovery_subapp',
                    'cluster' => 'crm-web-fe-dx-2022',
                    'port' => '8080',
                    'enable_flag' => '1',
                    'create_time' => '2023-01-06 14:08:09',
                    'update_time' => '2023-01-06 14:08:09',
                ),
            16 =>
                array (
                    'config_name' => 'crm-fpm',
                    'path_prefixes' => '/oms_subapp',
                    'cluster' => 'crm-web-fe-galio-oms',
                    'port' => '8080',
                    'enable_flag' => '1',
                    'create_time' => '2023-01-06 14:08:55',
                    'update_time' => '2023-01-06 14:08:55',
                ),
            17 =>
                array (
                    'config_name' => 'crm-fpm',
                    'path_prefixes' => '/sales_dist',
                    'cluster' => 'crm-web-fe-sales',
                    'port' => '8080',
                    'enable_flag' => '1',
                    'create_time' => '2022-12-20 14:33:05',
                    'update_time' => '2022-12-20 14:33:05',
                ),
            18 =>
                array (
                    'config_name' => 'crm-fpm',
                    'path_prefixes' => '/sales_subapp',
                    'cluster' => 'crm-web-fe-galio-sales',
                    'port' => '8080',
                    'enable_flag' => '1',
                    'create_time' => '2023-03-16 16:37:29',
                    'update_time' => '2023-03-16 16:37:29',
                ),
            19 =>
                array (
                    'config_name' => 'crm-fpm',
                    'path_prefixes' => '/talk_subapp',
                    'cluster' => 'crm-web-fe-talk',
                    'port' => '8080',
                    'enable_flag' => '1',
                    'create_time' => '2023-01-06 14:07:42',
                    'update_time' => '2023-01-06 14:07:42',
                ),
            20 =>
                array (
                    'config_name' => 'crm-fpm',
                    'path_prefixes' => '/tm/extension',
                    'cluster' => 'crm-assistant-fe',
                    'port' => '8080',
                    'enable_flag' => '1',
                    'create_time' => '2023-04-27 11:51:27',
                    'update_time' => '2023-04-27 11:51:27',
                ),
            21 =>
                array (
                    'config_name' => 'crm-fpm',
                    'path_prefixes' => '/tms_subapp',
                    'cluster' => 'crm-web-fe-tms',
                    'port' => '8080',
                    'enable_flag' => '1',
                    'create_time' => '2022-12-20 14:34:30',
                    'update_time' => '2022-12-20 14:34:30',
                ),
            22 =>
                array (
                    'config_name' => 'crm-fpm',
                    'path_prefixes' => '/valar_subapp',
                    'cluster' => 'crm-web-fe-valar',
                    'port' => '8080',
                    'enable_flag' => '1',
                    'create_time' => '2022-12-20 14:33:57',
                    'update_time' => '2022-12-20 14:33:57',
                ),
        );


        $new_config_name = 'beta-crm';
        foreach ($data as $item)
        {
            $item['cluster'] = 'nginx-ingress';
            $item['port'] = '80';
            $sql = "INSERT INTO tbl_envoy_routes (`config_name`,`path_prefixes`,`cluster`,`port`, `enable_flag`) 
VALUES ('{$new_config_name}','{$item['path_prefixes']}','{$item['cluster']}',{$item['port']},{$item['enable_flag']})
ON duplicate key update `cluster` = '{$item['cluster']}',`port` = {$item['port']};";
            self::info($sql);
            //!!!已增加DC_ID 后续执行请留意！！！！
//            $db->createCommand($sql)->execute();
        }
    }


    public function actionCopy($new_config_name = 'beta-crm', $dryRun = 1)
    {
        //!!!已增加DC_ID 后续执行请留意！！！！
        $old_config_name = 'crm-fpm';
        $db = Yii::app()->prometheus_db;
        $data = $db->createCommand("select * from tbl_envoy_routes WHERE config_name='{$old_config_name}' order by path_prefixes asc")->queryAll();
        foreach ($data as $item)
        {
            $sql = "INSERT INTO tbl_envoy_routes (`config_name`,`path_prefixes`,`cluster`,`port`, `enable_flag`) 
VALUES ('{$new_config_name}','{$item['path_prefixes']}','{$item['cluster']}',{$item['port']},{$item['enable_flag']})
ON duplicate key update `cluster` = '{$item['cluster']}',`port` = {$item['port']};";
            self::info($sql);
            if (!$dryRun) {
                $db->createCommand($sql)->execute();
            }
        }
    }

    /**
     * @deprecated tbl_fpm_apply_rule表废弃
     * */
    public function actionInitApplySetting($dryRun = 1)
    {
        $now = xm_function_now();
        $sql = "insert into tbl_fpm_apply_rule 
            (`system`,dc_id,apply_rule,nginx_var,nginx_value,enable_flag,create_time,update_time) 
        values 
            ('personal-crm',1,'normal','code_env','production',1,'{$now}','{$now}'),
            ('personal-crm',1,'normal','php_index','index.php',1,'{$now}','{$now}'),
            ('personal-crm',1,'grey','code_env','grey',1,'{$now}','{$now}'),
            ('personal-crm',1,'grey','php_index','index-grey.php',1,'{$now}','{$now}')
        ";

        $db = Yii::app()->prometheus_db;
        self::info($sql);
        if (!$dryRun) {
            $db->createCommand($sql)->execute();
        }
    }

    public function actionCopyFe($new_config_name = 'beta-crm', $dryRun = 1)
    {
        //!!!已增加DC_ID 后续执行请留意！！！！
        $old_config_name = 'crm-fpm';
        $db = Yii::app()->prometheus_db;
        $data = $db->createCommand("select * from tbl_envoy_routes WHERE config_name='{$old_config_name}' and port ='8080' order by path_prefixes asc")->queryAll();
        foreach ($data as $item)
        {
            $item['cluster'] = 'nginx-ingress';
            $item['port'] = '80';
            $sql = "INSERT INTO tbl_envoy_routes (`config_name`,`path_prefixes`,`cluster`,`port`, `enable_flag`) 
VALUES ('{$new_config_name}','{$item['path_prefixes']}','{$item['cluster']}',{$item['port']},{$item['enable_flag']})
ON duplicate key update `cluster` = '{$item['cluster']}',`port` = {$item['port']};";
            self::info($sql);
            if (!$dryRun) {
                $db->createCommand($sql)->execute();
            }
        }
    }

    public function actionTest88()
    {

        $memoryUsage1 = round(memory_get_usage(true) / (1024 * 1024), 2) . ' MB';
//        $aliAccountMap = array_column(\common\library\alibaba\customer\CustomerSyncHelper::getAllClientStore(), null, 'taobao_user_id');
        $aliAccountMap = array_column($this->getAllClientStore(), null, 'taobao_user_id');
        $memoryUsage2 = round(memory_get_usage(true) / (1024 * 1024), 2) . ' MB';
        var_dump($memoryUsage1,$memoryUsage2);

    }

    public function getAllClientStore()
    {

        $sql = "select a.client_id,a.store_id,a.user_id,a.access_token, a.taobao_user_id from tbl_alibaba_account a
                join tbl_alibaba_store s on a.store_id=s.store_id and a.client_id=s.client_id
                where a.oauth_flag in(2,3) and s.enable_flag=1";
        /**
         * @var $db DbConnection
         */
        $db = \Yii::app()->db;
        $aliAccountlist= $db->getPdoInstance()->query($sql)->fetchAll();

        return $aliAccountlist;
    }
}



