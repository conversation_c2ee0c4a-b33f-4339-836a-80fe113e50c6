<?php

use common\library\custom_field\CustomFieldService;
use common\library\custom_field\FieldExportService;
use common\library\oms\command\OmsFieldCommand;
use common\library\privilege_v3\PrivilegeConstants;
use common\library\privilege_v3\PrivilegeService;

/**
 * Created by PhpStorm.
 * User: keven
 * Date: 2015/5/15
 * Time: 20:26
 */
class CustomFieldCommand extends CrontabCommand
{
    use OmsFieldCommand;
    const SCOPE_GREY = 0;//灰度
    const SCOPE_ALl = 1;//全量

    // php ./yiic-test customField fixCashCollectionFieldCount
    public function actionFixCashCollectionFieldCount()
    {
        $updateSql = "update v4_admin.tbl_system_field set field_type = 5  where type = 3 and id = 'count' limit 1";
        $sql = "select * from tbl_system_field where type = 3 and id = 'count'";
        print_r(\Yii::app()->db->createCommand($sql)->queryAll());
        \Yii::app()->db->createCommand($updateSql)->execute();

        $clients = $this->getClientList();
//        $clients = [['client_id' => 344190]];

        foreach ($clients as $client) {
            $clientId = $client['client_id'];
            $db = ProjectActiveRecord::getDbByClientId($clientId);

            $checkSql = "select * from tbl_custom_field where client_id = {$clientId} and type =3 and id = 'count' and field_type = 1 and base = 1 ";
            if ($db->createCommand($checkSql)->queryRow()) {
                // 更新
                $updateField = "update tbl_custom_field set field_type=5 where client_id={$clientId} and `type`= 3 and id='count' and base=1 limit 1";
                $db->createCommand($updateField)->execute();
                self::info("actionFixCashCollectionFieldCount_".$clientId);
            }
        }
    }

    // php ./yiic-test customField fixFobField
    public function actionFixFobField()
    {
        // ["price_currency","fob_price","price_min","price_max","quantity","fob_type","gradient_price"]


        $systemColums = 'price_currency,fob_price,price_min,price_max,quantity,fob_type,gradient_price';
        $updateSql = "update v4_admin.tbl_system_field set `columns` = '{$systemColums}'  where type = 1 and id = 'fob' limit 1";
        $sql = "select * from tbl_system_field where type = 1 and id = 'fob'";
        print_r(\Yii::app()->db->createCommand($sql)->queryAll());
        \Yii::app()->db->createCommand($updateSql)->execute();

        $clients = $this->getClientList();
//        $clients = [['client_id' => 14119]];

        $columns = '["price_currency","fob_price","price_min","price_max","quantity","fob_type","gradient_price"]';
        foreach ($clients as $client) {
            $clientId = $client['client_id'];
            $db = ProjectActiveRecord::getDbByClientId($clientId);

            $checkSql = "select * from tbl_custom_field where client_id = {$clientId} and type =1 and id = 'fob' and columns like '%[\\\"[%'";
            if ($db->createCommand($checkSql)->queryRow()) {
                // 更新
                $updateField = "update tbl_custom_field set columns='{$columns}' where client_id={$clientId} and `type`= 1 and id='fob' limit 1";
                $db->createCommand($updateField)->execute();
                self::info("actionFixFobField_".$clientId);
            }
        }
    }

    public function actionInitFieldsFromClients($client_id = 0)
    {
        $client_list = Client::model()->findAll('`client_id` >= :client_id', ['client_id' => $client_id]);
        foreach ($client_list as $client) {
            if ($client->mysql_set_id == 0 || $client->mongo_set_id == 0) {
                continue;
            }
            $this->actionInitFields($client->client_id);
        }

        echo "操作完成\n";
    }

    /**
     * 初始化客户的系统字段, 包括公司, 联系人, 订单, 报价单, 产品
     *
     * @param $client_id
     *
     * @throws Exception
     */
    public function actionInitFields($client_id)
    {
        $this->log("开始初始化client_id={$client_id}的系统字段");
        $user_id = \common\library\privilege_v3\PrivilegeService::getInstance($client_id)->getAdminUserId();
        if (!$user_id) {
            $this->log("找不到管理员用户，跳过 client_id={$client_id}");
            return;
        }
        User::setLoginUserById($user_id);
        CustomerOptionService::createDefaultCustomField($client_id);
    }

    private function log($string)
    {
        echo $string . "\n";
        LogUtil::info($string);
    }

    /**
     * 全量同步系统字段
     */
    public function actionSync()
    {
        //获取系统字段
        $system_fields = Yii::app()->db->createCommand('SELECT * FROM tbl_system_field')->queryAll(true);

        //获取所有client 遍历
        $clients = $this->getClientList();
        $count = 0;
        foreach ($clients as $client) {
            $db = ProjectActiveRecord::getDbByClientId($client['client_id']);
            if ($db) {//测试环境存在有问题的client
                //同步规则：system_field 表中存在 ，目标表中不存在则插入
                $this->syncForClient($system_fields, $client['client_id']);
            }
            $count++;
        }

        echo '共需处理：', count($clients), PHP_EOL;
        echo '成功处理：', $count, PHP_EOL;
    }


    /**
     * 指定client
     *
     * @param $client_id
     */
    public function actionSyncByClient($clientId, $grey = 1, $expFlag = 0)
    {
        if ($clientId) {
            $clientIds = [$clientId];
        } elseif ($grey) {
            $clientIds = \common\library\GreyEnvHelper::getGreyClientIds();
        } elseif($expFlag){
            $clientIds= \common\library\account\Client::getExpClientIds();
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        //获取系统字段
        $system_fields = Yii::app()->db->createCommand('SELECT * FROM tbl_system_field')->queryAll(true);

        //灰度的公司，加上销售流程系统字段
        $greyClientIds = \common\library\GreyEnvHelper::getGreyClientIds();

        foreach ($clientIds as $clientId) {
            //测试环境跳过
//            if(in_array($clientId,[2,3127,14248,14249])){
//                continue;
//            }

            $this->syncSystem2Customer($system_fields, $clientId,$greyClientIds);
            $this->syncSystem2ExportSetting($system_fields, $clientId);
            $this->syncSystem2FieldGroup($system_fields, $clientId);

            $privilegeField = new \common\library\privilege_v3\PrivilegeField($clientId);
            $privilegeField->flushCache();
        }
    }

    /**
     * 更新导出设置的字段为可导出
     * @param $client_id
     */
    public function actionSetFieldExportSetting($clientId, $id)
    {

        $sql = "update tbl_field_export_setting set is_exportable = 1 where client_id = {$clientId} and id = '{$id}' and export_type in (" . CustomerExportTask::TYPE_ORDER .",". CustomerExportTask::TYPE_QUOTATION . ") and is_exportable = 0;";
        $ret = \ProjectActiveRecord::getDbByClientId($clientId)->createCommand($sql)->execute();

        echo "SetFieldExportSetting client_id:{$clientId} id: {$id} \n";
    }

    //刷新线索线索AI状态为线索状态
    public function actionReferLeadStatusField($clientId, $grey = 1,$expFlag = 0)
    {
        if ($clientId) {
            $clientIds = [$clientId];
        } elseif ($grey) {
            $clientIds = \common\library\GreyEnvHelper::getGreyClientIds();
        } elseif($expFlag){
            $clientIds= \common\library\account\Client::getExpClientIds();
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }
        foreach ($clientIds as $clientId) {
            $db = ProjectActiveRecord::getDbByClientId($clientId);
            if (!$db) {
                echo "skip for {$clientId} \n";
                continue;
            }
            $sql = "update tbl_custom_field set name = '线索动态状态' where client_id = {$clientId} and type = 7 and id = 'trail_status';";
            $db->getCommandBuilder()->createSqlCommand($sql, [])->execute();

            $this->actionSyncByClient($clientId);

            //刷新线索排序字段
            $clearClientCache = $this->referLeadUserInfoExternal($clientId);
            if ($clearClientCache) {
                \common\library\CommandRunner::run(
                    'cache',
                    'cleanUserInfo',
                    [
                        'clientId' => $clientId,
                    ],
                    '/dev/null',
                    0
                );
            }
            $msg = "referLeadStatusType:client_id:{$clientId}";
            self::log($msg);
        }
    }

    protected function referLeadUserInfoExternal($clientId){
        $clearClientCache = false;
        $key = 'lead_list_sort';
        $userExternalInfoList = UserInfoExternal::model()->findAllByAttributes([
            'client_id' => $clientId,
            'key' => $key,
        ]);
        $updateList = [];
        foreach ($userExternalInfoList as $userExternalInfo) {
            if(!$userExternalInfo->getAttributes() && !isset($userExternalInfo->getAttributes()['value'])){
                continue;
            }
            $userExternalInfoData = $userExternalInfo->getAttributes();
            $data = json_decode($userExternalInfoData['value'],true);
            if(!in_array('ai_status',$data)){
                continue;
            }
            foreach ($data as $index => $item){
                if($item == 'ai_status'){
                    $data[$index] = 'status';
                }
            }
            $tpmData = ['user_id' =>$userExternalInfoData['user_id'],'value' => json_encode($data)];
            $updateList[] = $tpmData;
        }
        if (count($updateList)) {
            $clearClientCache = true;
            $this->batchUpdateUserInfoExternal($clientId,$key,$updateList);
        }
        return $clearClientCache;
    }

    protected function batchUpdateUserInfoExternal($clientId,$key,$values)
    {
        $updates = [];
        foreach ($values as $value) {
            $updates[] = "UPDATE tbl_user_info_external SET `value` = '{$value['value']}' WHERE client_id = {$clientId} AND user_id = {$value['user_id']} and `key` = '{$key}'";
        }
        if (count($updates)) {
            $updateSql = implode(';', $updates);
            $count = Yii::app()->db->createCommand($updateSql)->execute();
            echo "batchUpdateUserSettings client_id:{$clientId} count {$count} \n";
        }
    }



    /**
     * 指定client--商机灰度阶段用到--指定公司同步单据的商机字段
     *
     * @param $client_id
     */
    public function actionSyncByClientOpportunity($client_id)
    {
        //获取系统字段
        $system_fields = Yii::app()->db->createCommand('SELECT * FROM tbl_system_field')->queryAll(true);
        //商机灰度阶段用到--指定公司同步单据的商机字段
        $orderField = [
            'id'                  => 'opportunity_id',
            'type'                => '2',
            'group_id'            => '1',
            'base'                => '1',
            'name'                => '商机',
            'field_type'          => '3',
            'require'             => '0',
            'edit_required'       => '1',
            'disable_flag'        => '0',
            'edit_hide'           => '0',
            'default'             => '',
            'edit_default'        => '1',
            'hint'                => '',
            'edit_hint'           => '0',
            'is_exportable'       => '1',
            'is_editable'         => '1',
            'is_list'             => '0',
            'columns'             => 'opportunity_id',
            'relation_type'       => '0',
            'relation_field'      => '',
            'relation_field_type' => '0',
            'relation_field_name' => '',
            'export_scenario'     => '2',
            'export_group'        => '1',
            'order'               => '13',
            'app_order'           => '11',
            'readonly'            => '0',
            'create_time'         => '2018-08-08 08:08:08',
            'update_time'         => '2018-08-08 08:08:08'
        ];

        $quotationField = [
            'id'                  => 'opportunity_id',
            'type'                => '3',
            'group_id'            => '1',
            'base'                => '1',
            'name'                => '商机',
            'field_type'          => '3',
            'require'             => '0',
            'edit_required'       => '1',
            'disable_flag'        => '0',
            'edit_hide'           => '0',
            'default'             => '',
            'edit_default'        => '1',
            'hint'                => '',
            'edit_hint'           => '0',
            'is_exportable'       => '1',
            'is_editable'         => '1',
            'is_list'             => '0',
            'columns'             => 'opportunity_id',
            'relation_type'       => '0',
            'relation_field'      => '',
            'relation_field_type' => '0',
            'relation_field_name' => '',
            'export_scenario'     => '3',
            'export_group'        => '1',
            'order'               => '12',
            'app_order'           => '10',
            'readonly'            => '0',
            'create_time'         => '2018-08-08 08:08:08',
            'update_time'         => '2018-08-08 08:08:08'
        ];

        $system_fields[] = $orderField;         //订单中增加商机字段
        $system_fields[] = $quotationField;     //报价单中增加商机字段

        $this->syncSystem2Customer($system_fields, $client_id);
        $this->syncSystem2ExportSetting($system_fields, $client_id);
        $this->syncSystem2FieldGroup($system_fields, $client_id);
    }

    public function actionDeleteField($type,$field,$clientId,$grey=0,$expFlag=0)
    {

        if ($clientId) {
            $clientIds = [$clientId];
        } elseif ($grey) {
            $clientIds = \common\library\GreyEnvHelper::getGreyClientIds();
        } elseif($expFlag){
            $clientIds= \common\library\account\Client::getExpClientIds();
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        $clients = $this->getClientList();
        $count = 0;
        foreach ($clientIds as $clientId) {
            $db = ProjectActiveRecord::getDbByClientId($clientId);
            if ($db) {
                $fields = explode(',',$field);
                foreach ($fields as $fieldItem){
                    $sql = "delete from tbl_custom_field where client_id = '{$clientId}'  and type = {$type} and id = '{$fieldItem}'";
                    $affectedRows = \ProjectActiveRecord::getDbByClientId($clientId)->createCommand($sql)->execute();

                    $sql = "delete from tbl_field_group where client_id = '{$clientId}'  and type = {$type} and id = '{$fieldItem}'";
                    $fRows = \ProjectActiveRecord::getDbByClientId($clientId)->createCommand($sql)->execute();

                    $sql = "delete from tbl_field_export_setting where client_id = '{$clientId}'  and type = {$type} and id = '{$fieldItem}'";
                    $exportRows = \ProjectActiveRecord::getDbByClientId($clientId)->createCommand($sql)->execute();

                    $msg = "client_id:[$clientId],field:[$fieldItem],res:[$affectedRows] frows:[$fRows]  eRrows:[$exportRows]\r\n";
                    echo $msg;
                }
            }
            $privilegeField = new \common\library\privilege_v3\PrivilegeField($clientId);
            $privilegeField->flushCache();
            $count++;
        }

        echo '共需处理：', count($clients), PHP_EOL;
        echo '成功处理：', $count, PHP_EOL;
    }


    /** 删除对应类型的自定义字段
     * @param $type
     * @param $field
     * @throws CDbException
     * @throws ProcessException
     */
    public function actionDeleteOpportunityField($type,$field)
    {
        $clients = $this->getClientList();
        $count = 0;
        foreach ($clients as $client) {
            $clientId = $client['client_id'];
            $db = ProjectActiveRecord::getDbByClientId($clientId);
            if ($db) {
                $sql = "delete from tbl_custom_field where client_id = '{$clientId}'  and type = {$type} and id = '{$field}'";
                $affectedRows = \ProjectActiveRecord::getDbByClientId($clientId)->createCommand($sql)->execute();
                $msg = "client_id:[$clientId],res:[$affectedRows]\r\n";
                echo $msg;
            }
            $count++;
        }

        echo '共需处理：', count($clients), PHP_EOL;
        echo '成功处理：', $count, PHP_EOL;
    }


    /** 删除经纬度
     * @param $clientId
     * @param int $grey
     * @param int $expFlag
     */
    public function actionDeleteLonlat($clientId, $grey = 1,$expFlag = 0)
    {
        if ($clientId) {
            $clientIds = [$clientId];
        } elseif ($grey) {
            $clientIds = \common\library\GreyEnvHelper::getGreyClientIds();
        } elseif($expFlag){
            $clientIds= \common\library\account\Client::getExpClientIds();
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }
        $count = 0;
        foreach ($clientIds as $clientId) {

            $db = ProjectActiveRecord::getDbByClientId($clientId);
            if ($db) {
                $sql = "delete from tbl_custom_field where client_id = '{$clientId}' and id = 'lonlat' and type in(4,7)";
                $affectedRows = \ProjectActiveRecord::getDbByClientId($clientId)->createCommand($sql)->execute();
                $msg = "client_id:[$clientId],res:[$affectedRows]\r\n";
                echo $msg;
            }
            $count++;
        }

        echo '共需处理：', count($clientIds), PHP_EOL;
        echo '成功处理：', $count, PHP_EOL;
    }


    /**删除商机结单日期字段
     * @param $clientId
     * @param int $grey
     * @param int $expFlag
     */
    public function actionDeleteOpportunityAccountDate($clientId, $grey = 1,$expFlag = 0)
    {
        if ($clientId) {
            $clientIds = [$clientId];
        } elseif ($grey) {
            $clientIds = \common\library\GreyEnvHelper::getGreyClientIds();
        } elseif($expFlag){
            $clientIds= \common\library\account\Client::getExpClientIds();
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }
        $count = 0;
        foreach ($clientIds as $clientId) {

            $db = ProjectActiveRecord::getDbByClientId($clientId);
            if ($db) {
                $sql = "delete from tbl_custom_field where client_id = '{$clientId}' and id = 'account_date' and type=9";
                $affectedRows = \ProjectActiveRecord::getDbByClientId($clientId)->createCommand($sql)->execute();
                $msg = "client_id:[$clientId],res:[$affectedRows]\r\n";
                echo $msg;
            }
            $count++;
        }

        echo '共需处理：', count($clientIds), PHP_EOL;
        echo '成功处理：', $count, PHP_EOL;
    }


    /** 同步到自定义表
     * @param $system_fields
     * @param $client_id
     * @param array $greyClientIds
     * @throws CDbException
     * @throws ProcessException
     */
    private function syncSystem2Customer($system_fields, $client_id,$greyClientIds = [])
    {
        //获取自定义字段表的数据
        $sql = "SELECT * FROM tbl_custom_field WHERE client_id={$client_id} and base=1";
        $customer_system_fields = ProjectActiveRecord::getDbByClientId($client_id)->createCommand($sql)->queryAll(true);

        $diff = $this->diff($system_fields, $customer_system_fields);
        if (empty($diff)) {

            LogUtil::info("client_id:{$client_id} tbl_custom_field 未检测到字段差异,略过");
            echo "client_id:{$client_id} tbl_custom_field 未检测到字段差异,略过", PHP_EOL;
            return;
        }

        $custom_field_insert = [];
        //默认删除字段
        $notEnables = CustomFieldService::DEFAULT_NOT_ENABLE;

        foreach ($diff as $key => $value) {
            $field = $system_fields[$key];

            $enableFlag = array_key_exists($field['type'], $notEnables) && in_array($field['id'],
                $notEnables[$field['type']]) ? 0 : 1;

            //商机-来源线索，如果有线索模块就打开，否则关闭
            if($field['type'] == Constants::TYPE_OPPORTUNITY && $field['id'] == 'main_lead_id'){
                if (PrivilegeService::getInstance($client_id)->hasFunctional(PrivilegeConstants::FUNCTIONAL_LEAD)) {
                    $enableFlag = CustomFieldService::FIELD_NORMAL_FLAG;
                }else{
                    $enableFlag  = CustomFieldService::FIELD_DELETE_FLAG;
                }
            }
//            //如果是灰度的client id,就把新增的字段打开，上线后删除

            /*
            if($field['type']== Constants::TYPE_COMPANY && in_array($field['id'],['annual_procurement','intention_level'])){
                if(in_array($client_id,$greyClientIds)){
                    $enableFlag = CustomFieldService::FIELD_NORMAL_FLAG;
                    $field['disable_flag'] = 0;
                }else{
                    $enableFlag = CustomFieldService::FIELD_DELETE_FLAG;
                }
            }
            */

            $custom_field_insert[] = [
                'enable_flag'         => $enableFlag,
                'client_id'           => $client_id,
                'id'                  => $field['id'],
                'type'                => $field['type'],
                'base'                => $field['base'],
                'name'                => $field['name'],
                'field_type'          => $field['field_type'],
                'ext_info'            => $field['ext_info'] ?? '',
                'require'             => $field['require'],
                'edit_required'       => $field['edit_required'],
                'disable_flag'        => $field['disable_flag'],
                'edit_hide'           => $field['edit_hide'],
                'default'             => $field['default'],
                'edit_default'        => $field['edit_default'],
                'hint'                => $field['hint'],
                'edit_hint'           => $field['edit_hint'],
                'is_editable'         => $field['is_editable'],
                'is_list'             => $field['is_list'],
                'columns'             => json_encode(explode(',', $field['columns'])),
                'order'               => $field['order'] ?? 0,
                'app_order'           => $field['app_order'] ?? 0,
                'readonly'            => $field['readonly'],
                'relation_type'       => $field['relation_type'],
                'relation_field'      => $field['relation_field'],
                'relation_field_type' => $field['relation_field_type'],
                'relation_field_name' => $field['relation_field_name'],
                'create_time'         => date('Y-m-d H:i:s'),
                'update_time'         => date('Y-m-d H:i:s')
            ];
        }

        ProjectActiveRecord::getDbByClientId($client_id)->getCommandBuilder()->createMultipleInsertCommand('tbl_custom_field',
            $custom_field_insert)->execute();
        LogUtil::info("client_id:{$client_id} 插入 custom_field：" . json_encode($custom_field_insert));
        echo "client_id:{$client_id} 插入 custom_field：" . json_encode($custom_field_insert), PHP_EOL;
    }

    /**
     * 同步到tbl_field_export_setting
     *c
     * @param $system_fields
     * @param $client_id
     */
    private function syncSystem2ExportSetting($system_fields, $client_id)
    {
        $sql = "SELECT * FROM tbl_field_export_setting WHERE client_id={$client_id}";
        $client_export_setting_fields = ProjectActiveRecord::getDbByClientId($client_id)->createCommand($sql)->queryAll(true);
        $diff = $this->diffWithExportScenario($system_fields, $client_export_setting_fields);
        if (empty($diff)) {

            LogUtil::info("client_id:{$client_id} tbl_field_export_setting 未检测到字段差异,略过");
            echo "client_id:{$client_id} tbl_field_export_setting 未检测到字段差异,略过", PHP_EOL;
            return;
        }

        $client_export_setting_fields_insert = [];
        foreach ($diff as $key => $value) {
            $field = $system_fields[$key];
            foreach ($value['export_scenario'] as $export_scenario) {
                if ($field['export_group'] > 0) {
                    $client_export_setting_fields_insert[] = [
                        'client_id'     => $client_id,
                        'type'          => $field['type'],
                        'export_type'   => $export_scenario,
                        'id'            => $field['id'],
                        'is_exportable' => $field['is_exportable'],
                        'group_id'      => FieldExportService::SPECIAL_EXPORT_GROUP_MAP[$export_scenario][$field['type']][$field['export_group']] ?? $field['export_group'],
                        'create_time'   => date('Y-m-d H:i:s'),
                        'update_time'   => date('Y-m-d H:i:s'),
                    ];
                }
            }
        }

        if (empty($client_export_setting_fields_insert)) {

            LogUtil::info("client_id:{$client_id} tbl_field_export_setting 未检测到字段差异,略过");
            echo "client_id:{$client_id} tbl_field_export_setting 未检测到字段差异,略过", PHP_EOL;
            return;
        }

        ProjectActiveRecord::getDbByClientId($client_id)->getCommandBuilder()->createMultipleInsertCommand('tbl_field_export_setting',
            $client_export_setting_fields_insert)->execute();
        LogUtil::info("client_id:{$client_id} 插入 field_export_setting：" . json_encode($client_export_setting_fields_insert));
        echo "client_id:{$client_id} 插入 field_export_setting：" . json_encode($client_export_setting_fields_insert), PHP_EOL;
    }

    /**
     * 同步到tbl_field_group
     *
     * @param $system_fields
     * @param $client_id
     */
    private function syncSystem2FieldGroup($system_fields, $client_id)
    {
        $sql = "SELECT * FROM tbl_field_group WHERE client_id={$client_id}";
        $client_group_fields = ProjectActiveRecord::getDbByClientId($client_id)->createCommand($sql)->queryAll(true);
        $diff = $this->diff($system_fields, $client_group_fields);
        if (empty($diff)) {
            LogUtil::info("client_id:{$client_id} tbl_field_group 未检测到字段差异,略过");
            echo "client_id:{$client_id} tbl_field_group 未检测到字段差异,略过", PHP_EOL;
            return;
        }

        $client_group_fields_insert = [];
        foreach ($diff as $key => $value) {
            $field = $system_fields[$key];
            foreach (explode(',', $field['group_id']) as $group_id) {
                $client_group_fields_insert[] = [
                    'client_id'   => $client_id,
                    'type'        => $field['type'],
                    'id'          => $field['id'],
                    'group_id'    => $group_id,
                    'create_time' => date('Y-m-d H:i:s'),
                    'update_time' => date('Y-m-d H:i:s'),
                ];
            }
        }

        if (empty($client_group_fields_insert)) {

            LogUtil::info("client_id:{$client_id} tbl_field_group 未检测到字段差异,略过");
            echo "client_id:{$client_id} tbl_field_group 未检测到字段差异,略过", PHP_EOL;
            return;
        }

        ProjectActiveRecord::getDbByClientId($client_id)->getCommandBuilder()->createMultipleInsertCommand('tbl_field_group',
            $client_group_fields_insert)->execute();
        LogUtil::info("client_id:{$client_id} 插入 tbl_field_group：" . json_encode($client_group_fields_insert));
        echo "client_id:{$client_id} 插入 tbl_field_group：" . json_encode($client_group_fields_insert), PHP_EOL;
    }

    /**
     * 获取差异
     *
     * @param $systemFields
     * @param $targetFields
     *
     * @return array
     */
    private function diff($systemFields, $targetFields)
    {
        $systemFieldsTemp = [];
        $targetFieldsTemp = [];
        foreach ($systemFields as $key => $value) {
            $systemFieldsTemp[$key] = $value['id'] . $value['type'];
        }
        foreach ($targetFields as $key => $value) {
            $targetFieldsTemp[$key] = $value['id'] . $value['type'];
        }

        return array_diff($systemFieldsTemp, $targetFieldsTemp);
    }

    /**
     * 获取系统字段和导出字段配置的差异
     * @param $systemFields
     * @param $fieldExportSetting
     * @return array
     */
    private function diffWithExportScenario($systemFields, $fieldExportSetting)
    {
        $systemFieldsTemp = [];
        $fieldExportSettingTemp = [];
        foreach ($systemFields as $key => $value) {
            foreach (explode(',', $value['export_scenario']) as $exportScenario) {
                $systemFieldsTemp[$key.'-'.$exportScenario] = $value['id'] .'-'.$value['type'].'-'.$exportScenario;
            }
        }
        foreach ($fieldExportSetting as $key => $value) {
            $fieldExportSettingTemp[$key.'-'.$value['export_type']] = $value['id'] .'-'. $value['type'].'-'. $value['export_type'];
        }
        $diff = array_diff($systemFieldsTemp, $fieldExportSettingTemp);
        foreach ($diff as $key =>$item) {
            $ids = explode('-', $key);
            $index =  $ids[0];
            $exportScenario = $ids[1];
            $diffFields[$index]['export_scenario'][] = $exportScenario;
        }
        return $diffFields ?? [];
    }

    public function actionFixSystemFieldType($map = [])
    {
        /* $fix_map = [
             ['condition' => ['type' => Constants::TYPE_OPPORTUNITY, 'id' => 'currency'], 'update' => ['edit_default' => 1]],
         ];*/


        /*
         * 修改payee名称为财务确认人
         */
//        $fix_map = [
//            ['condition' => ['type' => Constants::TYPE_CASH_COLLECTION, 'id' => 'payee'], 'update' => ['name' => '财务确认人']],
//        ];
        /*
         * 修改币种field_type为3
         */
//        $fix_map = [
//            ['condition' => ['type' => Constants::TYPE_CASH_COLLECTION, 'id' => 'currency'], 'update' => ['field_type' => '3']],
//        ];
//
//        $fix_map = [
//            ['condition' => ['type' => Constants::TYPE_ORDER, 'name' => '联系人邮箱', 'id' => 'customer_emial'], 'update' => ['id' => 'customer_email']],
//        ];
//
//        $fix_map = [
//            ['condition' => ['type' => Constants::TYPE_CASH_COLLECTION, 'id' => 'collection_date'], 'update' => ['require' => 1, 'edit_required' => 0]],
//        ];

        $fix_map = [
            ['condition' => ['type' => Constants::TYPE_ORDER, 'id' => 'exchange_rate'], 'update' => ['field_type' => 0]],
        ];

        $fix_map = [
            ['condition' => ['type' => Constants::TYPE_OPPORTUNITY, 'id' => 'currency'], 'update' => ['edit_default' => 1]],
        ];

        $fix_map = [
            ['condition' => ['type' => Constants::TYPE_OPPORTUNITY, 'id' => 'flow_id'], 'update' => ['require' => 1]],
        ];

        $fix_map = $map ?: $fix_map;

        /** @var CDbConnection $admin_db */
        $admin_db = Yii::app()->db;
        echo "update db v4_admin" . PHP_EOL;
        foreach ($fix_map as $item) {
            $criteria = new CDbCriteria();
            foreach ($item['condition'] as $key => $condition) {
                $criteria->addCondition("$key=:$key");
            }
            $criteria->params += $item['condition'];
            if (isset($item['update']['enable_flag']) && $item['update']['enable_flag'] == 0) {
                $result = $admin_db->getCommandBuilder()->createDeleteCommand('tbl_system_field', $criteria)->execute();
            } else {
                $result = $admin_db->getCommandBuilder()->createUpdateCommand('tbl_system_field', $item['update'],
                    $criteria)->execute();
            }
            echo 'result: ' . $result . ', update tbl_system_field: ' . json_encode($item) . PHP_EOL;
        }

        $client_dbs = \common\library\account\service\DbService::getMysqlList();
        foreach ($client_dbs as $client_db) {
            if (substr($client_db['name'],0,3)!='v5_') {
                continue;
            }
            echo "update db {$client_db['name']}" . PHP_EOL;
            $db = ProjectActiveRecord::getDbByDbSetId($client_db['set_id']);
            foreach ($fix_map as $item) {
                $criteria = new CDbCriteria();
                foreach ($item['condition'] as $key => $condition) {
                    $criteria->addCondition("$key=:$key");
                }
                $criteria->params += $item['condition'];
                $result = $db->getCommandBuilder()->createUpdateCommand('tbl_custom_field', $item['update'],
                    $criteria)->execute();
                echo 'result: ' . $result . ', update tbl_custom_field: ' . json_encode($item) . PHP_EOL;
            }
        }
    }

    public function syncSystemFields($type, $settings)
    {
        $date = date('Y-m-d H:i:s');
        $defaultSetting = [
            'hint'                => '',
            'edit_hint'           => 1,
            'edit_required'       => 1,
            'edit_hide'           => 1,
            'disable_flag'        => 0,
            'edit_default'        => 0,
            'type'                => $type,
            'create_time'         => $date,
            'update_time'         => $date,
            'export_group'        => 0,
            'relation_type'       => 0,
            'relation_field'      => '',
            'relation_field_type' => 0,
            'is_editable'         => 1,
            'readonly'            => 0,
            'ext_info'            => '',
        ];

        if (empty($settings)) {
            echo '没有系统需要更新';
            return 0;
        }

        $existsSettings = \common\models\admin\SystemField::model()->findAllByAttributes(['type' => $type]);
        $existsSettings = array_combine(array_column($existsSettings, 'id'), $existsSettings);

        // 排序
        $sorter = new \common\library\util\sort\Sorter(new \common\library\util\sort\SystemFieldSortRepository($type,
            'order'));
        $sorter->setResort(true);
        foreach ($settings as $setting) {
            $setting['type'] = $type;
            $isExist = array_key_exists($id = $setting['id'], $existsSettings);
            //order
            if ($isExist) {
                $newSetting = $existsSettings[$id];
            } else {
                $setting = array_merge($defaultSetting, $setting);
                $newSetting = new \common\models\admin\SystemField();
            }
            $setting['columns'] = $setting['columns'] ?? $setting['id'];
            $newSetting->setAttributes($setting, false);

            $sorter->setId($setting['id'], false);
            $orderNum = 0;
            $appOrderNum = 0;

            if (isset($setting['order']) || isset($setting['app_order'])) {
                $orderNum = $setting['order'] ?? 0;
                $appOrderNum = $setting['app_order'] ?? 0;
            } else {
                if (isset($setting['after'])) {
                    $sorter->setSortField('order');
                    $orderNum = $sorter->after($setting['after']);
                    $sorter->setSortField('app_order');
                    $appOrderNum = $sorter->after($setting['after']);
                } elseif (isset($setting['before'])) {
                    $sorter->setSortField('order');
                    $orderNum = $sorter->before($setting['before']);
                    $sorter->setSortField('app_order');
                    $appOrderNum = $sorter->before($setting['before']);
                }
            }
            $newSetting->order = $orderNum;
            $newSetting->app_order = $appOrderNum;
            $result = $newSetting->save();

            echo "result {$result} update field " . json_encode($newSetting->getAttributes(),
                    JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n"
                . 'update order count ' . $newSetting->order . ' and app order count ' . $newSetting->app_order;
        }
    }

//    protected function initSystemFields($type, $settings)
//    {
//        $date = date('Y-m-d H:i:s');
//        $deleteCount = \common\models\admin\SystemField::model()->deleteAllByAttributes(['type' => $type]);
//        echo '删除系统定义字段类型: ' . $type . ', 详情: ' . json_encode($deleteCount) . PHP_EOL;
//
//        $admin_db = Yii::app()->db;
//        echo "update db v4_admin" . PHP_EOL;
//
//        $order = count($settings);
//        array_walk($settings, function (&$setting) use ($type, $date, &$order) {
//            $setting = array_merge([
//                'order'               => --$order,
//                'app_order'           => $order,
//                'hint'                => '',
//                'edit_hint'           => 1,
//                'edit_required'       => 1,
//                'edit_hide'           => 1,
//                'disable_flag'        => 0,
//                'edit_default'        => 0,
//                'type'                => $type,
//                'create_time'         => $date,
//                'update_time'         => $date,
//                'export_group'        => 0,
//                'relation_type'       => 0,
//                'relation_field'      => '',
//                'relation_field_type' => 0,
//                'is_editable'         => 1,
//            ], $setting);
//            $setting['columns'] = isset($setting['columns']) ? $setting['columns'] : $setting['id'];
//        });
//
//        $result = $admin_db->getCommandBuilder()->createMultipleInsertCommand('tbl_system_field', $settings)->execute();
//        echo 'result: ' . $result . ', insert tbl_system_field: ' . json_encode($settings) . PHP_EOL;
//    }

    public function actionInitCustomerSystemFields()
    {
        $companySettings = [
//            [
//                'id' => 'timezone',
//                'name' => '时区',
//                'require' => 0,
//                'edit_required' => 1,
//                'edit_hide' => 0,
//                'edit_hint' => 0,
//                'hint' => '',
//                'default' => '',
//                'edit_default' => 0,
//                'columns' => 'timezone',
//                'export_scenario' => '2,3',
//                'field_type' => \common\library\custom_field\CustomFieldService::FIELD_TYPE_SELECT,
//                'group_id' => \common\library\custom_field\CustomFieldService::COMPANY_GROUP_CHARACTERISTIC,
//            ],
//            [
//                'id' => 'archive_type',
//                'name' => '自动创建',
//                'require' => 0,
//                'edit_required' => 0,
//                'edit_hide' => 1,
//                'edit_hint' => 0,
//                'hint' => '',
//                'default' => '否',
//                'edit_default' => 0,
//                'columns' => 'archive_type',
//                'export_scenario' => '',
//                'is_editable' => 1,
//                'after' => 'remark',
//                'readonly' => 1,
//                'order' => 2,
//                'app_order' => 2,
//                'field_type' => \common\library\custom_field\CustomFieldService::FIELD_TYPE_TEXT,
//                'group_id' => \common\library\custom_field\CustomFieldService::COMPANY_GROUP_OTHER,
//            ],
        ];
        $customerSettings = [];

        $this->syncSystemFields(Constants::TYPE_COMPANY, $companySettings);
        $this->syncSystemFields(Constants::TYPE_CUSTOMER, $customerSettings);
    }

    public function actionInitLeadSystemFields()
    {
        $leadSettings = [
            [
                'id'            => 'name',
                'name'          => '线索名称',
                'require'       => 1,
                'field_type'    => CustomFieldService::FIELD_TYPE_TEXT,
                'edit_hint'     => 0,
                'edit_required' => 0,
                'edit_hide'     => 0,
                'edit_default'  => 0,
            ],
            [
                'id'                  => 'company_name',
                'name'                => '公司名称',
                'require'             => 0,
                'field_type'          => CustomFieldService::FIELD_TYPE_TEXT,
                'relation_type'       => Constants::TYPE_COMPANY,
                'relation_field'      => 'name',
                'relation_field_type' => CustomFieldService::FIELD_TYPE_TEXT,
            ],
            [
                'id'                  => 'short_name',
                'name'                => '简称',
                'require'             => 0,
                'disable_flag'        => 1,
                'field_type'          => CustomFieldService::FIELD_TYPE_TEXT,
                'relation_type'       => Constants::TYPE_COMPANY,
                'relation_field'      => 'short_name',
                'relation_field_type' => CustomFieldService::FIELD_TYPE_TEXT,
            ],
            [
                'id'                  => 'origin',
                'name'                => '来源',
                'require'             => 0,
                'field_type'          => CustomFieldService::FIELD_TYPE_SELECT,
                'relation_type'       => Constants::TYPE_COMPANY,
                'relation_field'      => 'origin',
                'relation_field_type' => CustomFieldService::FIELD_TYPE_SELECT,
            ],
            [
                'id'                  => 'serial_id',
                'name'                => '线索编号',
                'require'             => 0,
                'field_type'          => CustomFieldService::FIELD_TYPE_TEXT,
                'relation_type'       => Constants::TYPE_COMPANY,
                'relation_field'      => 'serial_id',
                'relation_field_type' => CustomFieldService::FIELD_TYPE_TEXT,
                'is_editable'         => 0,
            ],
            [
                'id'                  => 'scale_id',
                'name'                => '规模',
                'disable_flag'        => 1,
                'require'             => 0,
                'field_type'          => CustomFieldService::FIELD_TYPE_SELECT,
                'relation_type'       => Constants::TYPE_COMPANY,
                'relation_field'      => 'scale_id',
                'relation_field_type' => CustomFieldService::FIELD_TYPE_SELECT,
            ],
            [
                'id'                  => 'group_id',
                'name'                => '分组',
                'require'             => 0,
                'field_type'          => CustomFieldService::FIELD_TYPE_SELECT,
                'relation_type'       => Constants::TYPE_COMPANY,
                'relation_field'      => 'group_id',
                'relation_field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                'disable_flag'        => 1,
            ],
//            [
//                'id'                  => 'biz_type',
//                'name'                => '线索类型',
//                'require'            => 0,
//                'field_type'          => CustomFieldService::FIELD_TYPE_TEXT,
//                'relation_type'       => Constants::TYPE_COMPANY,
//                'relation_field'      => 'biz_type',
//                'relation_field_type' => CustomFieldService::FIELD_TYPE_TEXT,
//            ],
            [
                'id'                  => 'category_ids',
                'name'                => '主营产品',
                'disable_flag'        => 1,
                'require'             => 0,
                'field_type'          => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                'relation_type'       => Constants::TYPE_COMPANY,
                'relation_field'      => 'category_ids',
                'relation_field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
            ],
            [
                'id'                  => 'country',
                'name'                => '国家',
                'require'             => 0,
                'field_type'          => CustomFieldService::FIELD_TYPE_TEXT,
                'relation_type'       => Constants::TYPE_COMPANY,
                'relation_field'      => 'country',
                'relation_field_type' => CustomFieldService::FIELD_TYPE_TEXT,
            ],
            [
                'id'                  => 'province',
                'name'                => '省份',
                'require'             => 0,
                'field_type'          => CustomFieldService::FIELD_TYPE_TEXT,
                'relation_type'       => Constants::TYPE_COMPANY,
                'relation_field'      => 'province',
                'relation_field_type' => CustomFieldService::FIELD_TYPE_TEXT,
            ],
            [
                'id'                  => 'city',
                'name'                => '城市',
                'require'             => 0,
                'field_type'          => CustomFieldService::FIELD_TYPE_TEXT,
                'relation_type'       => Constants::TYPE_COMPANY,
                'relation_field'      => 'city',
                'relation_field_type' => CustomFieldService::FIELD_TYPE_TEXT,
            ],
            [
                'id'                  => 'address',
                'name'                => '联系地址',
                'require'             => 0,
                'field_type'          => CustomFieldService::FIELD_TYPE_TEXT,
                'relation_type'       => Constants::TYPE_COMPANY,
                'relation_field'      => 'address',
                'relation_field_type' => CustomFieldService::FIELD_TYPE_TEXT,
            ],
            [
                'id'                  => 'homepage',
                'name'                => '主页',
                'require'             => 0,
                'field_type'          => CustomFieldService::FIELD_TYPE_TEXT,
                'relation_type'       => Constants::TYPE_COMPANY,
                'relation_field'      => 'homepage',
                'relation_field_type' => CustomFieldService::FIELD_TYPE_TEXT,
            ],
            [
                'id'                  => 'fax',
                'name'                => '传真',
                'disable_flag'        => 1,
                'require'             => 0,
                'field_type'          => CustomFieldService::FIELD_TYPE_TEXT,
                'relation_type'       => Constants::TYPE_COMPANY,
                'relation_field'      => 'fax',
                'relation_field_type' => CustomFieldService::FIELD_TYPE_TEXT,
            ],
            [
                'id'                  => 'tel',
                'name'                => '座机',
                'disable_flag'        => 1,
                'require'             => 0,
                'field_type'          => CustomFieldService::FIELD_TYPE_TEXT,
                'relation_type'       => Constants::TYPE_COMPANY,
                'relation_field'      => 'tel',
                'relation_field_type' => CustomFieldService::FIELD_TYPE_TEXT,
            ],
            [
                'id'                  => 'star',
                'name'                => '等级',
                'require'             => 0,
                'field_type'          => CustomFieldService::FIELD_TYPE_NUMBER,
                'relation_type'       => Constants::TYPE_COMPANY,
                'relation_field'      => 'star',
                'relation_field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                'disable_flag'        => 1,
            ],
            [
                'id'                  => 'trail_status',
                'name'                => '线索动态状态',
                'require'             => 0,
                'field_type'          => CustomFieldService::FIELD_TYPE_SELECT,
                'relation_type'       => Constants::TYPE_COMPANY,
                'relation_field'      => 'trail_status',
                'relation_field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                'disable_flag'        => 1,
            ],
            [
                'id'         => 'image_list',
                'name'       => '图片',
                'require'    => 0,
                'hint'       => '',
                'edit_hint'  => '',
                'field_type' => CustomFieldService::FIELD_TYPE_IMAGE,
            ],
            [
                'id'                  => 'remark',
                'name'                => '线索备注',
                'require'             => 0,
                'field_type'          => CustomFieldService::FIELD_TYPE_TEXTAREA,
                'relation_type'       => Constants::TYPE_COMPANY,
                'relation_field'      => 'remark',
                'relation_field_type' => CustomFieldService::FIELD_TYPE_TEXTAREA,
            ],
            [
                'id'                  => 'tag',
                'name'                => '标签',
                'require'             => 0,
                'disable_flag'        => 1,
                'field_type'          => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                'relation_type'       => Constants::TYPE_COMPANY,
                'relation_field'      => 'tag',
                'relation_field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
            ],
            [
                'id'            => 'ai_tags',
                'name'          => '系统标签',
                'require'       => 0,
                'field_type'    => CustomFieldService::FIELD_TYPE_TEXT,
                'edit_hint'     => 0,
                'edit_required' => 0,
                'edit_hide'     => 0,
                'edit_default'  => 0,
                'is_editable'   => 0,
            ],
            [
                'id'            => 'status',
                'name'          => '线索状态',
                'require'       => 0,
                'field_type'    => CustomFieldService::FIELD_TYPE_TEXT,
                'edit_hint'     => 0,
                'edit_required' => 0,
                'edit_hide'     => 0,
                'edit_default'  => 0,
                'is_editable'   => 0,
            ],
        ];

        $leadCustomerSettings = [
            [
                'id'                  => 'name',
                'name'                => '联系人名称',
                'require'             => 0,
                'field_type'          => CustomFieldService::FIELD_TYPE_TEXT,
                'relation_type'       => Constants::TYPE_CUSTOMER,
                'relation_field'      => 'name',
                'relation_field_type' => CustomFieldService::FIELD_TYPE_TEXT,
            ],
            [
                'id'                  => 'email',
                'name'                => '邮箱',
                'require'             => 0,
                'field_type'          => CustomFieldService::FIELD_TYPE_TEXT,
                'relation_type'       => Constants::TYPE_CUSTOMER,
                'relation_field'      => 'email',
                'relation_field_type' => CustomFieldService::FIELD_TYPE_TEXT,
            ],
            [
                'id'                  => 'tel_list',
                'name'                => '联系电话',
                'require'             => 0,
                'field_type'          => CustomFieldService::FIELD_TYPE_TEXT,
                'relation_type'       => Constants::TYPE_CUSTOMER,
                'relation_field'      => 'tel_list',
                'relation_field_type' => CustomFieldService::FIELD_TYPE_TEXT,
            ],
            [
                'id'                  => 'main_customer_flag',
                'name'                => '主要联系人',
                'require'             => 0,
                'field_type'          => CustomFieldService::FIELD_TYPE_TEXT,
                'relation_type'       => Constants::TYPE_CUSTOMER,
                'relation_field'      => 'main_customer_flag',
                'relation_field_type' => CustomFieldService::FIELD_TYPE_TEXT,
            ],
            [
                'id'                  => 'birth',
                'name'                => '生日',
                'disable_flag'        => 1,
                'require'             => 0,
                'field_type'          => CustomFieldService::FIELD_TYPE_DATE,
                'relation_type'       => Constants::TYPE_CUSTOMER,
                'relation_field'      => 'birth',
                'relation_field_type' => CustomFieldService::FIELD_TYPE_TEXT,
            ],
            [
                'id'                  => 'gender',
                'name'                => '性别',
                'require'             => 0,
                'field_type'          => CustomFieldService::FIELD_TYPE_SELECT,
                'relation_type'       => Constants::TYPE_CUSTOMER,
                'relation_field'      => 'gender',
                'relation_field_type' => CustomFieldService::FIELD_TYPE_SELECT,
            ],
            [
                'id'                  => 'post',
                'name'                => '职位',
                'require'             => 0,
                'field_type'          => CustomFieldService::FIELD_TYPE_TEXT,
                'relation_type'       => Constants::TYPE_CUSTOMER,
                'relation_field'      => 'post',
                'relation_field_type' => CustomFieldService::FIELD_TYPE_TEXT,
            ],
            [
                'id'                  => 'post_grade',
                'name'                => '职级',
                'disable_flag'        => 1,
                'require'             => 0,
                'field_type'          => CustomFieldService::FIELD_TYPE_SELECT,
                'relation_type'       => Constants::TYPE_CUSTOMER,
                'relation_field'      => 'post_grade',
                'relation_field_type' => CustomFieldService::FIELD_TYPE_SELECT,
            ],
            [
                'id'                  => 'contact',
                'name'                => '社交平台',
                'require'             => 0,
                'field_type'          => CustomFieldService::FIELD_TYPE_TEXT,
                'relation_type'       => Constants::TYPE_CUSTOMER,
                'relation_field'      => 'contact',
                'relation_field_type' => CustomFieldService::FIELD_TYPE_TEXT,
            ],
            [
                'id'                  => 'remark',
                'name'                => '备注',
                'require'             => 0,
                'field_type'          => CustomFieldService::FIELD_TYPE_TEXTAREA,
                'relation_type'       => Constants::TYPE_COMPANY,
                'relation_field'      => 'remark',
                'relation_field_type' => CustomFieldService::FIELD_TYPE_TEXTAREA,
            ],
            [
                'id'                  => 'tag',
                'name'                => '标签',
                'require'             => 0,
                'field_type'          => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                'relation_type'       => Constants::TYPE_CUSTOMER,
                'relation_field'      => 'tag',
                'relation_field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                'disable_flag'        => 1,
            ],
            [
                'id'         => 'image_list',
                'name'       => '图片',
                'require'    => 0,
                'hint'       => '',
                'edit_hint'  => '',
                'field_type' => CustomFieldService::FIELD_TYPE_IMAGE,
            ],
        ];

        $this->syncSystemFields(Constants::TYPE_LEAD, $leadSettings);
        $this->syncSystemFields(Constants::TYPE_LEAD_CUSTOMER, $leadCustomerSettings);
    }

    /**
     * 全量同步系统字段
     */
    public function actionSyncType($type, $clientId = null)
    {
        $types = explode(',', $type);
        foreach ($types as $type) {
            //获取系统字段
            $system_fields = Yii::app()->db->createCommand('SELECT * FROM tbl_system_field where type = ' . $type)->queryAll(true);
            if (empty($clientId)) {
                //获取所有client 遍历
                $clients = $this->getClientList();
                $count = 0;
                echo '共需处理：', count($clients), PHP_EOL;
                foreach ($clients as $client) {
                    echo '开始处理：', $client->client_id . ' ', PHP_EOL;
                    //同步规则：system_field 表中存在 ，目标表中不存在则插入
                    try {
                        $this->syncForClient($system_fields, $client->client_id);
                        $count++;
                    } catch (Throwable $e) {
                        LogUtil::error('同步字段出错， client_id: ' . $client->client_id . ', type: ' . $type);
                        LogUtil::error($e->getMessage());
                    }
                }
                echo '成功处理：', $count, PHP_EOL;
            } else {
                $this->syncForClient($system_fields, $clientId);
                echo '成功处理client：', $clientId, PHP_EOL;
            }
        }
    }

    protected function syncForClient($fields, $clientId)
    {
        $this->syncSystem2Customer($fields, $clientId);
        $this->syncSystem2ExportSetting($fields, $clientId);
        $this->syncSystem2FieldGroup($fields, $clientId);
    }

    public function actionFixExternalFieldSystemSetting($clientId = null)
    {
        $fixSetting = function ($clientId) {
            $table = \common\models\client\CustomField::model()->tableName();
            $set = "edit_hide = 1";
            $sql = "UPDATE $table SET $set WHERE client_id=:client_id and type in (4,5,7,8) and base=0 and edit_hide = 0";
            $params = [
                ':client_id' => $clientId,
            ];
            $count = ProjectActiveRecord::getDbByClientId($clientId)->createCommand($sql)->execute($params);
            echo '完成：client_id ', $clientId . ', 更新 ' . $count . ' 个自定义字段', PHP_EOL;
        };
        if ($clientId) {
            $fixSetting($clientId);
        } else {
            $clients = $this->getClientList();
            $count = 0;
            echo '共需处理：', count($clients), PHP_EOL;
            foreach ($clients as $client) {
                echo '开始处理：', $client->client_id . ' ', PHP_EOL;
                try {
                    $fixSetting($client->client_id);
                    $count++;
                } catch (Throwable $e) {
                    LogUtil::error('修复字段出错， client_id: ' . $client->client_id);
                    LogUtil::error($e->getMessage());
                }
            }
            echo '成功处理：', $count, PHP_EOL;
        }
    }

    public function actionCustomDisableSetting($clientId = 0)
    {
        $disable = [
            Constants::TYPE_LEAD          => ['star', 'tag', 'group_id',],
            Constants::TYPE_LEAD_CUSTOMER => ['tag',],
        ];
        $enable = [
            Constants::TYPE_LEAD => ['province', 'city',],
        ];

        $fixSetting = function ($clientId, $setting, $flag = 1) {
            $table = \common\models\client\CustomField::model()->tableName();
            foreach ($setting as $type => $ids) {
                if (empty($ids)) {
                    continue;
                }
                $set = "disable_flag = {$flag}, edit_hide = 0";
                $sql = "UPDATE $table SET $set WHERE client_id=:client_id and type =:type and base=1 and id in ('" . implode('\', \'',
                        $ids) . "')";
                $params = [
                    ':client_id' => $clientId,
                    ':type'      => $type,
                ];
                $count = ProjectActiveRecord::getDbByClientId($clientId)->createCommand($sql)->execute($params);
                echo '完成：client_id ', $clientId . ', 变更 ' . $count . ' 个自定义字段', PHP_EOL;
            }
        };

        if ($clientId) {
            $fixSetting($clientId, $enable, 0);
            $fixSetting($clientId, $disable, 1);
        } else {
            $clients = $this->getClientList();
            $count = 0;
            echo '共需处理：', count($clients), PHP_EOL;
            foreach ($clients as $client) {
                $clientId = $client->client_id;
                echo '开始处理：', $client->client_id . ' ', PHP_EOL;
                try {
                    $fixSetting($clientId, $enable, 0);
                    $fixSetting($clientId, $disable, 1);
                    $count++;
                } catch (Throwable $e) {
                    LogUtil::error('修复字段出错， client_id: ' . $client->client_id);
                    LogUtil::error($e->getMessage());
                    echo $e->getMessage();
                }
            }
            echo '成功处理：', $count, PHP_EOL;
        }
    }

    public function actionInitOpportunitySystemFields()
    {
        $opportunitySettings = [
            [
                'id'            => 'name',
                'name'          => '商机名称',
                'require'       => 1,
                'field_type'    => CustomFieldService::FIELD_TYPE_TEXT,
                'edit_required' => 0,
                'edit_hide'     => 0,
                'edit_default'  => 0,
                'edit_hint'     => 0,
                'is_editable'   => 1,
                'order'         => 201,
                'app_order'     => 201
            ],
            [
                'id'            => 'company_id',
                'name'          => '客户',
                'require'       => 1,
                'field_type'    => CustomFieldService::FIELD_TYPE_SELECT,
                'edit_required' => 0,
                'edit_hide'     => 0,
                'edit_default'  => 0,
                'edit_hint'     => 0,
                'is_editable'   => 0,
                'order'         => 200,
                'app_order'     => 200
            ],
            [
                'id'            => 'currency',
                'name'          => '币种',
                'require'       => 1,
                'field_type'    => CustomFieldService::FIELD_TYPE_TEXT,
                'edit_required' => 0,
                'edit_hide'     => 0,
                'edit_default'  => 0,
                'edit_hint'     => 0,
                'is_editable'   => 1,
                'order'         => 199,
                'app_order'     => 199
            ],
            [
                'id'            => 'exchange_rate',
                'name'          => '汇率',
                'require'       => 1,
                'field_type'    => CustomFieldService::FIELD_TYPE_TEXT,
                'edit_required' => 0,
                'edit_hide'     => 0,
                'edit_default'  => 0,
                'edit_hint'     => 0,
                'is_editable'   => 1,
                'order'         => 198,
                'app_order'     => 198
            ],
            [
                'id'            => 'customer_id',
                'name'          => '关联联系人',
                'require'       => 0,
                'field_type'    => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                'edit_required' => 1,
                'edit_hide'     => 0,
                'edit_default'  => 0,
                'edit_hint'     => 0,
                'is_editable'   => 1,
                'order'         => 197,
                'app_order'     => 197
            ],
//            [
//                'id'            => 'serial_id',
//                'name'          => '商机编号',
//                'require'       => 0,
//                'field_type'    => CustomFieldService::FIELD_TYPE_TEXT,
//                'is_editable'   => 0,
//                'edit_required' => 0,
//                'edit_hide'     => 0,
//                'edit_default'  => 0,
//            ],
            [
                'id'            => 'amount',
                'name'          => '销售金额',
                'require'       => 0,
                'field_type'    => CustomFieldService::FIELD_TYPE_NUMBER,
                'edit_required' => 1,
                'edit_hide'     => 0,
                'edit_default'  => 0,
                'edit_hint'     => 0,
                'is_editable'   => 1,
                'order'         => 196,
                'app_order'     => 196
            ],
            [
                'id'            => 'flow_id',
                'name'          => '销售流程',
                'require'       => 0,
                'field_type'    => CustomFieldService::FIELD_TYPE_SELECT,
                'edit_required' => 0,
                'edit_hide'     => 0,
                'edit_default'  => 0,
                'edit_hint'     => 0,
                'is_editable'   => 0,
                'order'         => 195,
                'app_order'     => 195
            ],
            [
                'id'            => 'account_date',
                'name'          => '结束日期',
                'require'       => 1,
                'field_type'    => CustomFieldService::FIELD_TYPE_DATE,
                'edit_required' => 0,
                'edit_hide'     => 0,
                'edit_default'  => 0,
                'edit_hint'     => 0,
                'is_editable'   => 1,
                'order'         => 194,
                'app_order'     => 194
            ],
            [
                'id'            => 'stage',
                'name'          => '销售阶段',
                'require'       => 1,
                'field_type'    => CustomFieldService::FIELD_TYPE_SELECT,
                'edit_required' => 0,
                'edit_hide'     => 0,
                'edit_default'  => 0,
                'edit_hint'     => 0,
                'is_editable'   => 1,
                'order'         => 193,
                'app_order'     => 193
            ],
            [
                'id'            => 'origin',
                'name'          => '商机来源',
                'require'       => 0,
                'field_type'    => CustomFieldService::FIELD_TYPE_SELECT,
                'edit_required' => 1,
                'edit_hide'     => 0,
                'edit_default'  => 0,
                'edit_hint'     => 0,
                'is_editable'   => 1,
                'order'         => 192,
                'app_order'     => 192
            ],
            [
                'id'            => 'type',
                'name'          => '商机类型',
                'require'       => 0,
                'field_type'    => CustomFieldService::FIELD_TYPE_SELECT,
                'edit_required' => 1,
                'edit_hide'     => 0,
                'edit_default'  => 0,
                'edit_hint'     => 0,
                'is_editable'   => 1,
                'order'         => 191,
                'app_order'     => 191
            ],
           /* [
                'id'            => 'fail_type',
                'name'          => '输单原因',
                'require'       => 0,
                'field_type'    => CustomFieldService::FIELD_TYPE_SELECT,
                'edit_required' => 1,
                'edit_hide'     => 0,
                'edit_default'  => 0,
                'edit_hint'     => 0,
                'is_editable'   => 1,
            ],
            [
                'id'            => 'fail_remark',
                'name'          => '输单描述',
                'require'       => 0,
                'field_type'    => CustomFieldService::FIELD_TYPE_TEXTAREA,
                'edit_required' => 1,
                'edit_hide'     => 0,
                'edit_default'  => 0,
                'edit_hint'     => 0,
                'is_editable'   => 1,
            ],*/
            [
                'id'            => 'remark',
                'name'          => '备注',
                'require'       => 0,
                'field_type'    => CustomFieldService::FIELD_TYPE_TEXTAREA,
                'edit_required' => 1,
                'edit_hide'     => 0,
                'edit_default'  => 0,
                'edit_hint'     => 0,
                'is_editable'   => 1,
                'order'         => 190,
                'app_order'     => 190
            ],
        ];

        $this->syncSystemFields(Constants::TYPE_OPPORTUNITY, $opportunitySettings);
    }

    public function actionInitCashCollectionSystemFields()
    {
        $cashCollectionSettings = [
            [
                'id'            => 'company_id',
                'name'          => '回款单-关联客户',
                'require'       => 0,
                'field_type'    => CustomFieldService::FIELD_TYPE_NUMBER,
                'edit_required' => 0,
                'edit_hide'     => 0,
                'edit_default'  => 0,
                'edit_hint'     => 0,
                'is_editable'   => 0,
                'ext_info'      => '',
                'order'         => 100,
                'app_order'     => 100
            ],
            [
                'id'            => 'order_id',
                'name'          => '回款单-关联订单',
                'require'       => 0,
                'field_type'    => CustomFieldService::FIELD_TYPE_NUMBER,
                'edit_required' => 1,
                'edit_hide'     => 0,
                'edit_default'  => 0,
                'edit_hint'     => 0,
                'is_editable'   => 0,
                'ext_info'      => '',
                'order'         => 99,
                'app_order'     => 99
            ],
            [
                'id'            => 'user_id',
                'name'          => '回款单-负责人',
                'require'       => 0,
                'field_type'    => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                'edit_required' => 1,
                'edit_hide'     => 0,
                'edit_default'  => 0,
                'edit_hint'     => 0,
                'is_editable'   => 0,
                'ext_info'      => '',
                'order'         => 98,
                'app_order'     => 98
            ],
            [
                'id'            => 'opportunity_id',
                'name'          => '回款单-关联商机',
                'require'       => 0,
                'field_type'    => CustomFieldService::FIELD_TYPE_NUMBER,
                'edit_required' => 1,
                'edit_hide'     => 0,
                'edit_default'  => 0,
                'edit_hint'     => 0,
                'is_editable'   => 0,
                'ext_info'      => '',
                'order'         => 88,
                'app_order'     => 88
            ],
            [
                'id'            => 'collection_date',
                'name'          => '回款单-回款日期',
                'require'       => 1,
                'field_type'    => CustomFieldService::FIELD_TYPE_DATE,
                'edit_required' => 1,
                'edit_hide'     => 0,
                'edit_default'  => 0,
                'edit_hint'     => 0,
                'is_editable'   => 1,
                'ext_info'      => '',
                'order'         => 93,
                'app_order'     => 93
            ],
            [
                'id'            => 'amount',
                'name'          => '回款单-本次回款金额',
                'require'       => 1,
                'field_type'    => CustomFieldService::FIELD_TYPE_NUMBER,
                'edit_required' => 0,
                'edit_hide'     => 0,
                'edit_default'  => 0,
                'edit_hint'     => 0,
                'is_editable'   => 1,
                'ext_info'      => '',
                'order'         => 96,
                'app_order'     => 96
            ],
            [
                'id'            => 'currency',
                'name'          => '币种',
                'require'       => 1,
                'field_type'    => CustomFieldService::FIELD_TYPE_TEXT,
                'edit_required' => 0,
                'edit_hide'     => 0,
                'edit_default'  => 0,
                'edit_hint'     => 0,
                'is_editable'   => 1,
                'ext_info'      => '',
                'order'         => 95,
                'app_order'     => 95
            ],
            [
                'id'            => 'exchange_rate',
                'name'          => '汇率',
                'require'       => 1,
                'field_type'    => CustomFieldService::FIELD_TYPE_NUMBER,
                'edit_required' => 0,
                'edit_hide'     => 0,
                'edit_default'  => 0,
                'edit_hint'     => 0,
                'is_editable'   => 1,
                'ext_info'      => '',
                'order'         => 94,
                'app_order'     => 94
            ],
            [
                'id'            => 'type',
                'name'          => '回款单-回款方式',
                'require'       => 0,
                'field_type'    => CustomFieldService::FIELD_TYPE_NUMBER,
                'edit_required' => 1,
                'edit_hide'     => 0,
                'edit_default'  => 0,
                'edit_hint'     => 0,
                'is_editable'   => 1,
                'ext_info'      => '',
                'order'         => 92,
                'app_order'     => 92
            ],
            [
                'id'            => 'finance_verify_name',
                'name'          => '回款单-财务确认人',
                'require'       => 0,
                'field_type'    => CustomFieldService::FIELD_TYPE_TEXT,
                'edit_required' => 1,
                'edit_hide'     => 0,
                'edit_default'  => 0,
                'edit_hint'     => 0,
                'is_editable'   => 1,
                'ext_info'      => '',
                'order'         => 90,
                'app_order'     => 90
            ],
            [
                'id'            => 'finance_verify_date',
                'name'          => '回款单-财务确认时间',
                'require'       => 0,
                'field_type'    => CustomFieldService::FIELD_TYPE_DATE,
                'edit_required' => 1,
                'edit_hide'     => 0,
                'edit_default'  => 0,
                'edit_hint'     => 0,
                'is_editable'   => 1,
                'ext_info'      => '',
                'order'         => 89,
                'app_order'     => 89
            ],
            [
                'id'            => 'payee',
                'name'          => '回款单-收款人',
                'require'       => 0,
                'field_type'    => CustomFieldService::FIELD_TYPE_TEXT,
                'edit_required' => 1,
                'edit_hide'     => 0,
                'edit_default'  => 0,
                'edit_hint'     => 0,
                'is_editable'   => 1,
                'ext_info'      => '',
                'order'         => 91,
                'app_order'     => 91
            ],
            [
                'id'            => 'department_id',
                'name'          => '回款单-归属部门',
                'require'       => 0,
                'field_type'    => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                'edit_required' => 1,
                'edit_hide'     => 0,
                'edit_default'  => 0,
                'edit_hint'     => 0,
                'is_editable'   => 0,
                'ext_info'      => '',
                'order'         => 97,
                'app_order'     => 97
            ],
            [
                'id'            => 'comment',
                'name'          => '回款单-备注',
                'require'       => 0,
                'field_type'    => CustomFieldService::FIELD_TYPE_TEXTAREA,
                'edit_required' => 1,
                'edit_hide'     => 0,
                'edit_default'  => 0,
                'edit_hint'     => 0,
                'is_editable'   => 1,
                'ext_info'      => '',
                'order'         => 0,
                'app_order'     => 0
            ],
            [
                'id'            => 'refer_type',
                'name'          => '回款单-关联类型',
                'require'       => 1,
                'field_type'    => CustomFieldService::FIELD_TYPE_SELECT,
                'edit_required' => 1,
                'edit_hide'     => 1,
                'edit_default'  => 0,
                'edit_hint'     => 0,
                'is_editable'   => 1,
                'ext_info'      => '',
                'order'         => 0,
                'app_order'     => 0
            ],
        ];

        $this->syncSystemFields(Constants::TYPE_CASH_COLLECTION, $cashCollectionSettings);
    }

    public function actionInitOpportunityProductSystemFields()
    {
        $opportunityProductSettings = [
            [
                'id'            => 'product_no',
                'name'          => '商机-产品编号',
                'require'       => 0,
                'field_type'    => CustomFieldService::FIELD_TYPE_TEXT,
                'edit_required' => 0,
                'edit_hide'     => 0,
                'edit_default'  => 0,
                'edit_hint'     => 0,
                'is_editable'   => 0,
                'ext_info'      => '',
                'is_list'       => 1,
                'group_id'      => CustomFieldService::OPPORTUNITY_GROUP_PRODUCT,
                'order'         => 101,
                'app_order'     => 101,
            ],
            [
                'id'            => 'product_id',
                'name'          => '商机-产品id',
                'require'       => 1,
                'field_type'    => CustomFieldService::FIELD_TYPE_OTHER,
                'edit_required' => 0,
                'edit_hide'     => 0,
                'disable_flag'  => 1,
                'edit_default'  => 0,
                'edit_hint'     => 0,
                'is_editable'   => 0,
                'ext_info'      => '',
                'is_list'       => 1,
                'group_id'      => CustomFieldService::OPPORTUNITY_GROUP_PRODUCT,
                'order'         => 100,
                'app_order'     => 100
            ],
            [
                'id'            => 'opportunity_product_id',
                'name'          => '商机-商机产品唯一id',
                'require'       => 1,
                'field_type'    => CustomFieldService::FIELD_TYPE_OTHER,
                'edit_required' => 0,
                'edit_hide'     => 0,
                'disable_flag'  => 1,
                'edit_default'  => 0,
                'edit_hint'     => 0,
                'is_editable'   => 0,
                'ext_info'      => '',
                'is_list'       => 1,
                'group_id'      => CustomFieldService::OPPORTUNITY_GROUP_PRODUCT,
                'order'         => 102,
                'app_order'     => 102
            ],
            [
                'id'            => 'product_name',
                'name'          => '商机-产品名称',
                'require'       => 0,
                'field_type'    => CustomFieldService::FIELD_TYPE_TEXT,
                'edit_required' => 1,
                'edit_hide'     => 0,
                'edit_default'  => 0,
                'edit_hint'     => 0,
                'is_editable'   => 1,
                'ext_info'      => '',
                'is_list'       => 1,
                'group_id'      => CustomFieldService::OPPORTUNITY_GROUP_PRODUCT,
                'order'         => 99,
                'app_order'     => 99
            ],
            [
                'id'            => 'product_model',
                'name'          => '商机-产品型号',
                'require'       => 0,
                'field_type'    => CustomFieldService::FIELD_TYPE_TEXT,
                'edit_required' => 1,
                'edit_hide'     => 0,
                'edit_default'  => 0,
                'edit_hint'     => 0,
                'is_editable'   => 1,
                'ext_info'      => '',
                'is_list'       => 1,
                'group_id'      => CustomFieldService::OPPORTUNITY_GROUP_PRODUCT,
                'order'         => 98,
                'app_order'     => 98
            ],
            [
                'id'            => 'unit_price',
                'name'          => '商机-产品销售价格',
                'require'       => 0,
                'field_type'    => CustomFieldService::FIELD_TYPE_NUMBER,
                'edit_required' => 0,
                'edit_hide'     => 0,
                'edit_default'  => 0,
                'edit_hint'     => 0,
                'is_editable'   => 1,
                'ext_info'      => '',
                'is_list'       => 1,
                'group_id'      => CustomFieldService::OPPORTUNITY_GROUP_PRODUCT,
                'order'         => 97,
                'app_order'     => 97
            ],
            [
                'id'            => 'count',
                'name'          => '商机-产品销售数量',
                'require'       => 0,
                'field_type'    => CustomFieldService::FIELD_TYPE_NUMBER,
                'edit_required' => 0,
                'edit_hide'     => 0,
                'edit_default'  => 0,
                'edit_hint'     => 0,
                'is_editable'   => 1,
                'ext_info'      => '',
                'is_list'       => 1,
                'group_id'      => CustomFieldService::OPPORTUNITY_GROUP_PRODUCT,
                'order'         => 96,
                'app_order'     => 96
            ],
            [
                'id'            => 'other_cost',
                'name'          => '商机-产品其他费用',
                'require'       => 0,
                'field_type'    => CustomFieldService::FIELD_TYPE_NUMBER,
                'edit_required' => 1,
                'edit_hide'     => 0,
                'edit_default'  => 0,
                'edit_hint'     => 0,
                'is_editable'   => 1,
                'ext_info'      => '',
                'is_list'       => 1,
                'group_id'      => CustomFieldService::OPPORTUNITY_GROUP_PRODUCT,
                'order'         => 95,
                'app_order'     => 95
            ],
            [
                'id'            => 'cost_amount',
                'name'          => '商机-产品销售金额',
                'require'       => 0,
                'field_type'    => CustomFieldService::FIELD_TYPE_NUMBER,
                'edit_required' => 0,
                'edit_hide'     => 0,
                'edit_default'  => 0,
                'edit_hint'     => 0,
                'is_editable'   => 0,
                'ext_info'      => '',
                'is_list'       => 1,
                'group_id'      => CustomFieldService::OPPORTUNITY_GROUP_PRODUCT,
                'order'         => 94,
                'app_order'     => 94
            ],
            [
                'id'            => 'product_remark',
                'name'          => '商机-产品备注',
                'require'       => 0,
                'field_type'    => CustomFieldService::FIELD_TYPE_TEXTAREA,
                'edit_required' => 1,
                'edit_hide'     => 0,
                'edit_default'  => 0,
                'edit_hint'     => 0,
                'is_editable'   => 1,
                'ext_info'      => '',
                'is_list'       => 1,
                'group_id'      => CustomFieldService::OPPORTUNITY_GROUP_PRODUCT,
                'order'         => 92,
                'app_order'     => 92
            ]
        ];
        $opportunityProductFields = [
            [
                'id'            => 'product_total_count',
                'name'          => '商机-产品销售总数量',
                'require'       => 0,
                'field_type'    => CustomFieldService::FIELD_TYPE_NUMBER,
                'edit_required' => 0,
                'edit_hide'     => 0,
                'edit_default'  => 0,
                'edit_hint'     => 0,
                'is_editable'   => 0,
                'ext_info'      => '',
                'is_list'       => 0,
                'group_id'      => CustomFieldService::OPPORTUNITY_GROUP_PRODUCT,
                'order'         => 92,
                'app_order'     => 92
            ],
            [
                'id'            => 'product_other_amount',
                'name'          => '商机-产品其他总费用',
                'require'       => 0,
                'field_type'    => CustomFieldService::FIELD_TYPE_NUMBER,
                'edit_required' => 0,
                'edit_hide'     => 0,
                'edit_default'  => 0,
                'edit_hint'     => 0,
                'is_editable'   => 0,
                'ext_info'      => '',
                'is_list'       => 0,
                'group_id'      => CustomFieldService::OPPORTUNITY_GROUP_PRODUCT,
                'order'         => 91,
                'app_order'     => 91
            ],
            [
                'id'            => 'product_total_amount',
                'name'          => '商机-产品销售总金额',
                'require'       => 0,
                'field_type'    => CustomFieldService::FIELD_TYPE_NUMBER,
                'edit_required' => 0,
                'edit_hide'     => 0,
                'edit_default'  => 0,
                'edit_hint'     => 0,
                'is_editable'   => 0,
                'ext_info'      => '',
                'is_list'       => 0,
                'group_id'      => CustomFieldService::OPPORTUNITY_GROUP_PRODUCT,
                'order'         => 90,
                'app_order'     => 90
            ]
        ];
        $this->syncSystemFields(Constants::TYPE_OPPORTUNITY, $opportunityProductSettings);
        $this->syncSystemFields(Constants::TYPE_OPPORTUNITY, $opportunityProductFields);
    }



    public function actionCustomFieldFixOrder($clientId = 0)
    {

        $fixSetting = function ($clientId) {
            $table = \common\models\client\CustomField::model()->tableName();
            $set = "`order` = 10, `app_order` = 10";
            $sql = "UPDATE $table SET $set WHERE client_id=:client_id and type =:type and base=1 and id = 'pool_id'";
            $params = [
                ':client_id' => $clientId,
                ':type'      => Constants::TYPE_COMPANY,
            ];
            $count = ProjectActiveRecord::getDbByClientId($clientId)->createCommand($sql)->execute($params);
            echo '完成：client_id ', $clientId . ', 变更 ' . $count . ' 个自定义字段', PHP_EOL;
        };

        if ($clientId) {
            $fixSetting($clientId);
            $fixSetting($clientId);
        } else {
            $clients = $this->getClientList();
            $count = 0;
            echo '共需处理：', count($clients), PHP_EOL;
            foreach ($clients as $client) {
                $clientId = $client->client_id;
                echo '开始处理：', $client->client_id . ' ', PHP_EOL;
                try {
                    $fixSetting($clientId);
                    $fixSetting($clientId);
                    $count++;
                } catch (Throwable $e) {
                    LogUtil::error('修复字段出错， client_id: ' . $client->client_id);
                    LogUtil::error($e->getMessage());
                    echo $e->getMessage();
                }
            }
            echo '成功处理：', $count, PHP_EOL;
        }
    }

    /** 根据v4_admin.tbl_system_field 刷新tbl_custom_field客户字段排序
     * @param $clientId
     * @param int $grey
     * @param int $expFlag
     * @throws CDbException
     * @throws ProcessException
     */
    public function actionRefreshCustomerFieldOrder($clientId, $grey = 1,$expFlag = 0)
    {
        if ($clientId) {
            $clientIds = [$clientId];
        } elseif ($grey) {
            $clientIds = \common\library\GreyEnvHelper::getGreyClientIds();
        } elseif($expFlag){
            $clientIds= \common\library\account\Client::getExpClientIds();
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }
        sort($clientIds);

        $sql = "select id,name, `require`,`edit_required`,`order`  from tbl_system_field where type = 9 order by `order` desc";
        $systemFieldList = Yii::app()->db->getCommandBuilder()->createSqlCommand($sql)->queryAll();

        foreach ($clientIds as $clientId){

//            //测试环境跳过
            if(in_array($clientId,[2,3127])){
                continue;
            }

            $msg = sprintf("refreshCustomerOrder:client_id[%s]\r\n", $clientId);
            self::info($msg);
            $db = ProjectActiveRecord::getDbByClientId($clientId);
            $total = 0;
            foreach ($systemFieldList as $item){
                $order = $item['order'];
                //销售金额是选填
                if($item['id'] == 'amount'){
                    $sql = "UPDATE `tbl_custom_field` SET `order`={$order}, `app_order`={$order},`require`={$item['require']}, `edit_required`={$item['edit_required']} WHERE `type` = 9 AND `base` =1 AND `id` = '{$item['id']}' and client_id = {$clientId};";

                }else{
                    $sql = "UPDATE `tbl_custom_field` SET `order`={$order}, `app_order`={$order} WHERE `type` = 9 AND `base` =1 AND `id` = '{$item['id']}' and client_id = {$clientId};";
                }
                $count = $db->createCommand($sql)->execute();
                $total +=$count;
            }
            $msg = sprintf("refreshCustomerOrderEnd:client_id[%s] total[%s]\r\n", $clientId,$total);
            echo $msg;
            \LogUtil::info($msg);
        }

    }

    public function actionRefreshFieldOrder($clientId = 0)
    {
        $updateSort = function ($clientId) {
            $db = ProjectActiveRecord::getDbByClientId($clientId);
            $insertOrder = 10;
            $checkSql = "
                select `order` from `tbl_custom_field` where type = 4 and id = 'group_id' and client_id = {$clientId};
            ";
            $sort = $db->createCommand($checkSql)->queryScalar();
            if ($sort > $insertOrder) {
                return 0;
            }
            $sql = "
                UPDATE `tbl_custom_field` SET `order`=`order`+1, `app_order`=`app_order`+1 WHERE `type` = 4 AND `base` =1 AND `order` >= {$insertOrder} and client_id = {$clientId};
            ";
            $count = ProjectActiveRecord::getDbByClientId($clientId)->createCommand($sql)->execute();
            return $count;
        };
        if ($clientId) {
            $count = $updateSort($clientId);
            echo "client: {$clientId} 更新 {$count} \n";
        } else {
            $clients = $this->getClientList();
            foreach ($clients as $client) {
                $clientId = $client->client_id;
                $count = $updateSort($clientId);
                echo "client: {$clientId} 更新 {$count} \n";
            }
        }
    }

    /**
     * @param null $lastNumber
     * 开放产品字段"产品类型category_ids"为可设置必填、隐藏
     */
    public function actionSetProductCategory($lastNumber = null)
    {
        $greyClientIds = $this->getGreyClientIds($lastNumber);

        $num = 1;
        $clientCount = count($greyClientIds);
        foreach ($greyClientIds as $clientId) {
            echo "$clientId $num/$clientCount \n";
            $num++;

            $db = ProjectActiveRecord::getDbByClientId($clientId);
            if (!$db) {
                self::info("db not exists! client_id: $clientId");
                continue;
            }

            $type = Constants::TYPE_PRODUCT;
            $sql = "UPDATE `tbl_custom_field` SET `edit_required`=1,`edit_hide` = 1 
WHERE `client_id`={$clientId} and `type`={$type} and `id`='category_ids';";
            ProjectActiveRecord::getDbByClientId($clientId)->createCommand($sql)->execute();
        }
    }

    /**
     * @param null $lastNumber
     * @param int  $enableFlag
     * 开启、关闭 timezone字段用于灰度
     */
    public function actionTimezone($lastNumber = null, $enableFlag = 1)
    {
        $greyClientIds = $this->getGreyClientIds($lastNumber);

        $num = 1;
        $clientCount = count($greyClientIds);
        foreach ($greyClientIds as $clientId) {
            echo "$clientId $num/$clientCount \n";
            $num++;

            $db = ProjectActiveRecord::getDbByClientId($clientId);
            if (!$db) {
                self::info("db not exists! client_id: $clientId");
                continue;
            }

            $type = Constants::TYPE_COMPANY;
            $sql = "UPDATE `tbl_custom_field` SET `enable_flag`={$enableFlag} 
WHERE `client_id`={$clientId} and `type`={$type} and `id`='timezone';";
            ProjectActiveRecord::getDbByClientId($clientId)->createCommand($sql)->execute();
        }
    }

    /**
     * 改变country,使edit_hide=0,disable_flag=0
     *
     * @param int   $actionType 0:灰度 1:全量
     * @param array $clientIds 灰度cliendIds
     * @param null  $lastNumber 灰度时尾号
     *
     * @throws ProcessException
     */
    public function actionChangeCountry($actionType = self::SCOPE_GREY, $clientIds = [], $lastNumber = null)
    {
        if ($actionType == self::SCOPE_GREY) {
            $clientIds = empty($clientIds) ? $this->getGreyClientIds($lastNumber) : $clientIds;
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
//            $excludeClients = [];
//            $clientIds = array_merge(array_diff($clientIds, $excludeClients));
        }
        $count = 0;
        foreach ($clientIds as $clientId) {
            $db = ProjectActiveRecord::getDbByClientId($clientId);
            if (!$db) {
                self::info("Db not exists! client_id: $clientId");
                continue;
            }
            try {
                $updateSql = "update tbl_custom_field set edit_hide=0,disable_flag=0 where `type`=4 and `id`='country' and client_id={$clientId}";
                $ret = $db->createCommand($updateSql)->execute();
                //echo $updateSql."\n";
                $count++;
            } catch (Exception $e) {
                echo "----------打印错误\n";
                echo $e;
                echo "----------停止打印错误\n\n";
            }
        }

        echo "共需处理" . count($clientIds) . "条数据\n";
        echo "一共执行" . $count . "条数据\n";
    }

    /**
     * 更改Currency币种为下拉且可编辑
     * 更改字段field_type, edit_required, edit_default
     * (100 -> 311)
     *
     * @param int   $actionType 0:灰度 1:全量
     * @param array $clientIds 灰度cliendIds
     * @param null  $lastNumber 灰度时尾号
     *
     * @throws ProcessException
     */
    public function actionChangeCurrency($actionType = self::SCOPE_GREY, $clientIds = [], $lastNumber = null)
    {
        if ($actionType == self::SCOPE_GREY) {
            $clientIds = empty($clientIds) ? $this->getGreyClientIds($lastNumber) : $clientIds;
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
//            $excludeClients = [];剔除名单
//            $clientIds = array_merge(array_diff($clientIds, $excludeClients));
        }
        $count = 0;
        foreach ($clientIds as $clientId) {
            $db = ProjectActiveRecord::getDbByClientId($clientId);
            if (!$db) {
                self::info("Db not exists! client_id: $clientId");
                continue;
            }
            try {
                $updateSql = "update tbl_custom_field set field_type='3',edit_required=0,edit_default=1,`default`='USD' where `type`='9' and `id`='currency' and client_id={$clientId}";
                $ret = $db->createCommand($updateSql)->execute();
//                echo $updateSql."\n";
                $count++;
            } catch (Exception $e) {
                echo "----------打印错误\n";
                echo $e;
                echo "----------停止打印错误\n\n";
            }
        }

        echo "共需处理" . count($clientIds) . "条数据\n";
        echo "一共执行" . $count . "条数据\n";
    }

    /**
     * 批量调整timezone到country后面
     *
     * @param int   $actionType 0:灰度 1:全量
     * @param array $clientIds 灰度cliendIds
     * @param null  $lastNumber 灰度时尾号
     *
     * @throws ProcessException
     */
    public function actionChangeTestTimezoneIndex($actionType = self::SCOPE_GREY, $clientIds = [], $lastNumber = null)
    {
        if ($actionType == self::SCOPE_GREY) {
            $clientIds = empty($clientIds) ? $this->getGreyClientIds($lastNumber) : $clientIds;
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
//            $excludeClients = [];
//            $clientIds = array_merge(array_diff($clientIds, $excludeClients));
        }
        $count = 0;
        foreach ($clientIds as $clientId) {
            $db = ProjectActiveRecord::getDbByClientId($clientId);
            if (!$db) {
                self::info("Db not exists! client_id: $clientId");
                continue;
            }
            try {
                $fieldList = $db->createCommand("select id,`order` from tbl_custom_field where client_id={$clientId} and type='4' and enable_flag=1 and base=1 order by client_id ASC,`order` DESC")->queryAll();
                $fieldList = array_column($fieldList, 'order', 'id');
                $step = 2;
                $timezoneOrder = $fieldList['timezone'];
                if ($timezoneOrder == 0) {
                    $step = 3;
                    $updateSql = "update tbl_custom_field set `order`=1 where client_id=$clientId and type=4 and id='timezone'";
//                    echo $updateSql."\n";
                    $ret = $db->createCommand($updateSql)->execute();
                    $resultTimezoneOrder = 1;
                } else {
                    $resultTimezoneOrder = $timezoneOrder;
                }
                $updateSql = "update tbl_custom_field set `order`=`order`+$step where client_id=$clientId and type=4 and `order`>=$timezoneOrder and id<>'timezone'";
//                echo $updateSql."\n";
                $ret = $db->createCommand($updateSql)->execute();
                $updateSql = "update tbl_custom_field set `order`=$resultTimezoneOrder+1 where client_id=$clientId and type=4 and id='country'";
//                echo $updateSql."\n";
                $ret = $db->createCommand($updateSql)->execute();
                $count++;
            } catch (Exception $e) {
                echo "----------打印错误\n";
                echo $e;
                echo "----------停止打印错误\n\n";
            }
        }

        echo "共需处理" . count($clientIds) . "条数据\n";
        echo "一共执行" . $count . "条数据\n";
    }


    public function actionPerformanceField()
    {
        $invoiceSetting = [
            [
                'id'         => 'departments',
                'name'       => '业绩归属部门',
                'require'    => 0,
                'hint'       => '',
                'edit_hint'  => '',
                'after'      => 'users',
                'field_type' => CustomFieldService::FIELD_TYPE_OTHER,
            ],
        ];

        $opportunitySetting = [
            [
                'id'         => 'department',
                'name'       => '归属部门',
                'require'    => 0,
                'hint'       => '',
                'edit_hint'  => '',
                'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
            ],
        ];

        $this->syncSystemFields(Constants::TYPE_ORDER, $invoiceSetting);
        $this->syncSystemFields(Constants::TYPE_OPPORTUNITY, $opportunitySetting);
    }

    public function actionRemoveOrderRmbField($clientId, $grey = 1, $greyNum = null)
    {
        $fieldId = 'amount_rmb';

        if ($clientId) {
            $clientIds = [$clientId];
        } elseif ($grey && !is_null($greyNum)) {
            $clientIds = \common\library\GreyEnvHelper::getGreyClientIds();
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        foreach ($clientIds as $clientId) {
            $sql = "delete from tbl_custom_field where client_id = {$clientId} and type in (" . implode(',',
                    [Constants::TYPE_ORDER, Constants::TYPE_QUOTATION]) . ") and id = :field_id";
            $params = [
                ':field_id' => $fieldId
            ];
            $db = ProjectActiveRecord::getDbByClientId($clientId);
            if (!$db) {
                echo "skip for {$clientId} \n";
                continue;
            }

            $result = $db->getCommandBuilder()->createSqlCommand($sql, $params)->execute();
            echo "remove for {$clientId} \n";
        }
    }

    public function actionRemoveCustomerField($clientId, $grey, $type,$fieldId)
    {

        if ($clientId) {
            $clientIds = [$clientId];
        } elseif ($grey) {
            $clientIds = \common\library\GreyEnvHelper::getGreyClientIds();
        }else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        foreach ($clientIds as $clientId) {
            $sql = "delete from tbl_custom_field where client_id = {$clientId} and type ={$type} and id = :field_id";
            $params = [
                ':field_id' => $fieldId
            ];
            $db = ProjectActiveRecord::getDbByClientId($clientId);
            if (!$db) {
                echo "skip for {$clientId} \n";
                continue;
            }
            $result = $db->getCommandBuilder()->createSqlCommand($sql, $params)->execute();
            echo "remove for {$clientId} \n";
        }
    }



    //刷新客户编号字段类型
    public function actionReferSerialIdFieldType($clientId, $grey = 1,$expFlag = 0)
    {
        if ($clientId) {
            $clientIds = [$clientId];
        } elseif ($grey) {
            $clientIds = \common\library\GreyEnvHelper::getGreyClientIds();
        } elseif($expFlag){
            $clientIds= \common\library\account\Client::getExpClientIds();
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }
        foreach ($clientIds as $clientId) {
            if($clientId == 2){
                continue;
            }
            $sql = "update tbl_custom_field set field_type = 1 where client_id = {$clientId} and type = 4 and id = 'serial_id';";
            $db = ProjectActiveRecord::getDbByClientId($clientId);
            if (!$db) {
                echo "skip for {$clientId} \n";
                continue;
            }
            $result = $db->getCommandBuilder()->createSqlCommand($sql, [])->execute();
            $msg = "ReferSerialIdFieldType:client_id:{$clientId} res:{$result}";
            self::log($msg);
        }
    }

    /**
     * feature/performance-v2.0.k8 专用
     *
     * @param     $clientId
     * @param int $grey
     * @param int $enableFlag
     *
     * @throws Exception
     */
    public function actionEnablePerformanceField($clientId,  $grey = 1, $greyNum = null, $enableFlag = 1)
    {
        if ($clientId) {
            $clientIds = [$clientId];
        } elseif ($grey && !is_null($greyNum)) {
//            $clientIds = \common\library\GreyEnvHelper::getGreyClientIds();
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        foreach ($clientIds as $clientId) {
            $this->enablePerformanceField($clientId, $enableFlag);
        }
    }

    /**
     * feature/performance-v2.0.k8 专用
     *
     * @param     $clientId
     * @param int $enableFlag
     *
     * @throws Exception
     */
    public function enablePerformanceField($clientId, $enableFlag = 1)
    {
        $fieldSetting = [
            Constants::TYPE_ORDER       => [
                'departments' => '业绩归属部门',
            ],
            Constants::TYPE_OPPORTUNITY => [
                'department' => '归属部门',
            ],
        ];

        foreach ($fieldSetting as $type => $fieldIds) {
            foreach ($fieldIds as $fieldId => $name) {
                if (!ProjectActiveRecord::getDbByClientId($clientId)) {
                    continue;
                }
                ProjectActiveRecord::setConnection(ProjectActiveRecord::getDbByClientId($clientId));
                $sql = "update tbl_custom_field set enable_flag = :enable_flag where id=:id and client_id=:client_id and type=:type";
                $params = [
                    ':enable_flag' => $enableFlag,
                    ':id' => $fieldId,
                    ':client_id' => $clientId,
                    ':type' => $type,
                ];
                $succeed = \common\models\client\CustomField::model()->getCommandBuilder()->createSqlCommand($sql, $params)->execute();
                echo "update field {$fieldId} for {$clientId} as enable_flag={$enableFlag}";
            }
        }
    }

    public function actionFixFieldOrderOneByOne($clientId)
    {
        /**
         * [ type => [ before => next ] ]
         */
        $after = 'after';
        $before = 'before';
        $config = [
            Constants::TYPE_ORDER => [
                ['departments', $after, 'users'],
            ],
        ];

        foreach ($config as $type => $settingList) {
            $sorter = new \common\library\util\sort\Sorter(new \common\library\util\sort\CustomFieldSortRepository($clientId,
                $type));
            $sorter->setResort(true);
            foreach ($settingList as list($id, $method, $neighborId)) {
                $sorter->setId($id, true);
                $sorter->setSortField('order');
                $sorter->{$method}($neighborId);
                $sorter->setSortField('app_order');
                $sorter->{$method}($neighborId);
            }
        }
    }

    public function actionFixFieldOrderForGrey($greyNum=6)
    {
        $clientIds = $this->getGreyClientIds($greyNum);
        foreach ($clientIds as $clientId) {
            echo "start client_id: $clientId \n";
            if (!ProjectActiveRecord::getDbByClientId($clientId)) {
                continue;
            }
            \ProjectActiveRecord::setConnection(ProjectActiveRecord::getDbByClientId($clientId));
            \PgActiveRecord::setConnection(PgActiveRecord::getDbByClientId($clientId));
            $this->actionFixFieldOrder($clientId);
        }
    }

    public function actionFixFieldOrder($clientId)
    {
        $config = [
            Constants::TYPE_OPPORTUNITY => [
                'customer_id',
                'department',
                'currency',
                'exchange_rate',
                'amount',
                'account_date',
                'origin',
                'type'
            ],
            Constants::TYPE_ORDER       => [
                'name',
                'status',
                'company_id',
                'opportunity_id',
                'customer_id',
                'currency',
                'exchange_rate',
                'amount',
                'account_date',
                'users',
                'departments',
                'remark',
            ],
        ];
        foreach ($config as $type => $list) {
            $sorter = new \common\library\util\sort\Sorter(new \common\library\util\sort\CustomFieldSortRepository($clientId,
                $type));
            $sorter->setResort(true);
            $result = $sorter->sortList(
                $list,
                false,
                true
            );
            \LogUtil::info("resort client:{$clientId} type:{$type} sort as " . json_encode($result));
        }
    }


    public function actionRenameField()
    {
        $setting = [
            [
                'type' => Constants::TYPE_ORDER,
                'id'   => 'account_date',
                'name' => '结单日期'
            ]
        ];
        foreach ($setting as $field) {
            $params = [
                ':name' => $field['name'],
                ':type' => $field['type'],
                ':id'   => $field['id'],
            ];
            $sql = "update tbl_system_field set name=:name where type=:type and id=:id";
            Yii::app()->db->getCommandBuilder()->createSqlCommand($sql)->execute($params);
        }

        $sql = "update tbl_custom_field set name=:name where type=:type and id=:id";
        $client_dbs = \common\library\account\service\DbService::getMysqlList();
        foreach ($client_dbs as $client_db) {
            echo "update db {$client_db['name']}" . PHP_EOL;
            $db = ProjectActiveRecord::getDbByDbSetId($client_db['set_id']);
            foreach ($setting as $field) {
                $params = [
                    ':name' => $field['name'],
                    ':type' => $field['type'],
                    ':id'   => $field['id'],
                ];
                if ($db) {
                    $succeed = $db->getCommandBuilder()->createSqlCommand($sql)->execute($params);
                    \LogUtil::info('rename info: ' . json_encode($setting));
                }
            }
        }
    }

    public function actionInitPerformance($clientId, $grey = 0, $greyNum = null)
    {
        if ($clientId) {
            $clientIds = [$clientId];
        } elseif ($grey) {
            if (!is_null($greyNum)) {
                $clientIds = $this->getGreyClientIds($greyNum);
            }
//            $clientIds = \common\library\GreyEnvHelper::getGreyClientIds();
        }  else {
            $greyClientIds = array_merge($this->getGreyClientIds(6), [
                2113,18154,3,14121,  6688,18152,18120,18136,18133,18137,7, 2106,18303,16149,4210,904,8077,6401,10241,3215
            ]);
            $clientIds = array_column($this->getClientList(), 'client_id');
            $clientIds = array_diff($clientIds, $greyClientIds);
        }

        foreach ($clientIds as $clientId) {
            echo "start client_id: $clientId \n";
            if (!ProjectActiveRecord::getDbByClientId($clientId)) {
                continue;
            }
            \ProjectActiveRecord::setConnection(ProjectActiveRecord::getDbByClientId($clientId));
            \PgActiveRecord::setConnection(PgActiveRecord::getDbByClientId($clientId));
            $this->actionSyncByClient($clientId);
            $this->actionEnablePerformanceField($clientId);
            $this->actionRemoveOrderRmbField($clientId);
            $this->actionFixFieldOrder($clientId);
        }
    }

    /**
     * @param int    $step
     * @param int    $maxClientId
     * @param int    $dryRun
     * @param string $yiic
     *
     * @deprecated
     */
    public function actionInitPerformanceParallel($step = 2000, $maxClientId = 20000, $dryRun = 1, $yiic = '')
    {
//        for ($i = 0; $i < $maxClientId / $step; $i++) {
//            $startClientId = $i * $step + 1;
//            $endClientId = $step * ($i + 1);
//            $yiic = $yiic ?: Yii::getPathOfAlias('application') . "/" . Yii::app()->params['yiic'];
//            $logPath = Yii::app()->runtimePath . '/customfieldinitperformance' . $i . '.log';
//            $command = "nohup $yiic customField InitPerformanceAll --dryRun={$dryRun} --min={$startClientId} --max={$endClientId} > {$logPath} &";
//            echo "run " . ($command) . "\n";
//            exec($command);
//        }
    }

    public function actionInitPerformanceAll($dryRun, $min, $max)
    {
//        $greyClientIds = array_merge($this->getGreyClientIds(6), [
//            2113,18154,3,14121,  6688,18152,18120,18136,18133,18137,7, 2106,18303,16149,4210,904,8077,6401,10241,3215
//        ]);
        // 仅跳过内部测试公司
        $greyClientIds = [3,7,10];
        $clientIds = array_column($this->getClientList(), 'client_id');
        $clientIds = array_diff($clientIds, $greyClientIds);
        foreach ($clientIds as $clientId) {
            if ($clientId >= $min && $clientId <= $max) {
                echo "start {$clientId} \n";
                if (!$dryRun) {
                    $this->actionInitPerformance($clientId);
                }
            }
        }
    }

    public function actionRemoveCustomField($clientId, $grey = 1, $greyNum = 0)
    {
        if ($clientId) {
            $clientIds = [$clientId];
        } elseif ($grey && !is_null($greyNum)) {
//            $clientIds = \common\library\GreyEnvHelper::getGreyClientIds();
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        $fix_map = [
            ['condition' => ['type' => Constants::TYPE_CASH_COLLECTION, 'id' => 'finance_verify_name'], 'update' => ['enable_flag' => 0]],
            ['condition' => ['type' => Constants::TYPE_CASH_COLLECTION, 'id' => 'finance_verify_date'], 'update' => ['enable_flag' => 0]],
        ];

        foreach ($clientIds as $clientId) {
            $db = ProjectActiveRecord::getDbByClientId($clientId);
            if (!$db) {
                continue;
            }
            foreach ($fix_map as $item) {
                $criteria = new CDbCriteria();
                $item['condition']['client_id'] = $clientId;
                foreach ($item['condition'] as $key => $condition) {
                    $criteria->addCondition("$key=:$key");
                }
                $criteria->params += $item['condition'];
                $result = $db->getCommandBuilder()->createUpdateCommand('tbl_custom_field', $item['update'],
                    $criteria)->execute();
                echo 'result: ' . $result . ', update tbl_custom_field: ' . json_encode($item) . PHP_EOL;
            }

            $userSettingList = $db->createCommand("select user_id, client_id, `key`, `value` from tbl_user_setting where client_id=:client_id and `key` = :key ")->queryAll(true, [
                ':client_id' => $clientId,
                ':key' => \common\library\setting\user\UserSetting::CASH_COLLECTION_LIST_FIELD,
            ]);
            $updates = [];
            $removeIds = ['finance_verify.name', 'finance_verify.date'];
            foreach ($userSettingList as &$userSetting) {
                $value = json_decode($userSetting['value'], true);
                $value = json_encode(array_values(array_filter($value, function ($item) use ($removeIds) {
                    return !in_array($item['id'], $removeIds);
                })));
                $updates[] = "update tbl_user_setting set `value`='{$value}' where client_id={$userSetting['client_id']} and user_id={$userSetting['user_id']} and `key` = '{$userSetting['key']}'";
            }

            if (count($updates)) {
                $sql = implode(';', $updates);
                $updateCounts = $db->createCommand($sql)->execute();
                echo 'result: ' . $result . ', update tbl_user_setting: ' . json_encode(array_column($userSettingList, 'key', 'user_id')) . PHP_EOL;
            }
        }
    }


    //web-pro1.1 使得orderNo可以被编辑

    /**
     * @param int $type 1:灰度 2:全量
     * @param string $clientIdStr
     * @param int $lastNumber
     */
    public function actionChangeOrderNoEditAttribute($type=1, $clientIdStr='', $lastNumber=-1)
    {
        switch ($type) {
            //灰度
            case 1:
//                $clientList = $clientIdStr ? explode('|', $clientIdStr) : array_column($this->getClientList(), 'client_id');
                $clientList = $clientIdStr ? explode('|', $clientIdStr) : array_column($this->getClientList(0, false, null, null, 0, 0, $lastNumber), 'client_id');
                if (empty($clientList)) return;
                foreach ($clientList as $clientId) {
                    try {
                        $db = ProjectActiveRecord::getDbByClientId($clientId);
                        $updateSql = "update tbl_custom_field set is_editable='1',readonly=0 where client_id=$clientId and `id`='order_no' and `type`='2'";
                        $ret = $db->createCommand($updateSql)->execute();
                        echo $updateSql."\n";
                        echo "clientId $clientId done--------num = $ret;\n\n";
                    } catch (Throwable $throwable) {
                        echo $throwable->getMessage()."\n";
                        continue;
                    }
                }
                break;
            //全量
            case 2:
                $setList = \common\library\account\service\DbService::getDbList(DbSet::TYPE_MYSQL);
                foreach ($setList as $set) {
                    try {
                        $setId = $set['set_id'];
                        ProjectActiveRecord::setConnection(ProjectActiveRecord::getDbByDbSetId($setId));
                        $db = ProjectActiveRecord::getDbByDbSetId($setId);
                        $updateSql = "update tbl_custom_field set is_editable='1',readonly=0 where `id`='order_no' and `type`='2'";
                        $ret = $db->createCommand($updateSql)->execute();
                        echo "DB $setId done $ret client\n";
                    } catch (Throwable $throwable) {
                        echo $throwable->getMessage()."\n";
                        continue;
                    }
                }
                break;
            default:
                break;
        }
    }

    /**
     * @param      $clientId
     * @param int  $grey
     * @param null $greyNum
     *
     * @throws CDbException
     * @throws ProcessException
     *
     * feature/may-v1.k6
     */
    public function actionAddFieldForCashCollection($clientId, $grey = 1, $greyNum = null)
    {

        $cashCollectionField = [
            'id'                  => 'collect_status',
            'type'                => '10',
            'group_id'            => '1',
            'base'                => '1',
            'name'                => '回款状态',
            'field_type'          => CustomFieldService::FIELD_TYPE_SELECT,
            'require'             => '1',
            'edit_required'       => '0',
            'disable_flag'        => '0',
            'edit_hide'           => '0',
            'default'             => '0',
            'edit_default'        => '1',
            'hint'                => '',
            'edit_hint'           => '0',
            'is_exportable'       => '1',
            'is_editable'         => '1',
            'is_list'             => '0',
            'columns'             => 'collect_status',
            'relation_type'       => '0',
            'relation_field'      => '',
            'relation_field_type' => '0',
            'relation_field_name' => '',
            'export_scenario'     => '',
            'export_group'        => '',
            'order'               => '10',
            'app_order'           => '10',
            'readonly'            => '0',
            'create_time'         => date('Y-m-d H:i:s'),
            'update_time'         => date('Y-m-d H:i:s')
        ];


        $system_fields[] = $cashCollectionField;

        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey && !is_null($greyNum)) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        foreach ($clientIds as $clientId) {
            $conn = ProjectActiveRecord::getDbByClientId($clientId);
            ProjectActiveRecord::setConnection($conn);
            $this->syncSystem2Customer($system_fields, $clientId);
            $this->syncSystem2FieldGroup($system_fields, $clientId);
            echo '成功处理client：', $clientId, PHP_EOL;
        }
    }

    public function actionModifyReadonly($field_type, $field_id, $client_id = 0)
    {
        $clientIds = $client_id == 0 ? array_column($this->getClientList(), 'client_id') : explode(',', $client_id);

        foreach ($clientIds as $clientId) {
            $conn = ProjectActiveRecord::getDbByClientId($clientId);
            if (!$conn) continue;

            $conn->createCommand("UPDATE tbl_custom_field SET readonly=0 WHERE client_id=:client_id AND type=:type AND id=:id;")->execute([
                ':client_id' => $clientId,
                ':type' => $field_type,
                ':id' => $field_id,
            ]);

            echo "$clientId, done\n";
        }
    }

    /*
     * 2020 feature/may-v1.k6
     */
    public function actionAddSysFieldForCashCollection()
    {
        $cashCollectionSetting = [
            [
                'id'         => 'real_amount',
                'name'       => '实到账金额',
                'require'    => 1,
                'hint'       => '',
                'edit_hint'  => 1,
                'group_id'   => 1,
                'edit_required' => 0,
                'is_exportable' => 0,
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可编辑，1可以，0否
                'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                'default' => 0,
                'ext_info' => '[2]'
            ],
            [
                'id'         => 'bank_charge',
                'name'       => '手续费',
                'require'    => 0,
                'hint'       => '',
                'edit_hint'  => 1,
                'group_id'   => 1,
                'edit_required' => 1,
                'is_exportable' => 0,
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 1,// 是否可编辑，1可以，0否
                'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                'default' => 0,
                'ext_info' => '[2]'
            ],
            [
                'id'         => 'trade_no',
                'name'       => '交易号/票据号',
                'require'    => 0,
                'hint'       => '',
                'edit_hint'  => 1,
                'group_id'   => 1,
                'edit_required' => 1,
                'is_exportable' => 0,
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 1,// 是否可编辑，1可以，0否
                'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
            ],
        ];

        $this->syncSystemFields(Constants::TYPE_CASH_COLLECTION,$cashCollectionSetting);
    }

    public function actionFixCashCollectionReferType()
    {
        \common\models\admin\SystemField::model()->updateAll(['field_type' => 3], "type=10 and id='refer_type'");

        //获取所有client 遍历
        $clients = $this->getClientList();
        $count = 0;
        foreach ($clients as $client) {
            $db = ProjectActiveRecord::getDbByClientId($client['client_id']);
            if ($db) {//测试环境存在有问题的client
                echo 'client:', $client['client_id'], PHP_EOL;
                $db->createCommand("update tbl_custom_field set field_type=3 where type=10 and id='refer_type' and client_id={$client['client_id']}")->execute();
            }
            $count++;
        }

        echo '共需处理：', count($clients), PHP_EOL;
        echo '成功处理：', $count, PHP_EOL;

    }

    /*
     * 系统字段增加客户的经纬度
     *
     */
    public function actionAddSysFieldForCompany()
    {
        $companySetting = [
            [
                'id'         => 'lonlat',
                'name'       => '经维度坐标',
                'require'    => 0,
                'hint'       => '',
                'edit_hint'  => 0,
                'group_id'   => 4,
                'edit_required' => 0,
                'is_exportable' => 0,
                'disable_flag'  => 1,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可编辑，1可以，0否
                'order' => 19,
                'app_order' =>19,
                'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
            ],
        ];

        $this->syncSystemFields(Constants::TYPE_COMPANY, $companySetting);
    }

    /**
     * @param int $clientId
     * @param null $lastNumber
     */
    public function actionAddFieldForCompany($clientId = 1, $lastNumber = null)
    {

        $fieldInfo = [
            'id'                  => 'product_group_ids',
            'type'                => 4,// 公司
            'group_id'            => '3', // 管理信息分组
            'base'                => 1,
            'name'                => '产品分组',
            'field_type'          => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
            'require'             => 0,
            'edit_required'       => 0,
            'disable_flag'        => 0,//前端是否隐藏，1隐藏，0不隐藏
            'edit_hide'           => 1,// 是否可编辑，1可以，0否
            'default'             => '',
            'edit_default'        => 0,
            'hint'                => '',
            'edit_hint'           => 1,
            'is_exportable'       => 0,
            'is_editable'         => 1,
            'is_list'             => 0,
            'columns'             => 'product_group_ids',
            'relation_type'       => 0,
            'relation_field'      => '',
            'relation_field_type' => 0,
            'relation_field_name' => '',
            'export_scenario'     => '',
            'export_group'        => '',
            'readonly'            => 0,
            'order'               => 10,
            'app_order'           => 10,
            'create_time'         => date('Y-m-d H:i:s'),
            'update_time'         => date('Y-m-d H:i:s')
        ];


        $system_fields[] = $fieldInfo;

        if (empty($clientId)) {
            //获取所有client 遍历
            $clients = $this->getClientList(0, false, null, null, 0, 0, $lastNumber);
            $count = 0;
            echo '共需处理：', count($clients), PHP_EOL;
            foreach ($clients as $client) {
                echo '开始处理：', $client->client_id . ' ', PHP_EOL;
                //同步规则：system_field 表中存在 ，目标表中不存在则插入
                try {
                    $this->syncSystem2Customer($system_fields, $client->client_id);
                    $this->syncSystem2FieldGroup($system_fields, $client->client_id);
                    $count++;
                } catch (Throwable $e) {
                    LogUtil::error('同步字段出错， client_id: ' . $client->client_id);
                    LogUtil::error($e->getMessage());
                }
            }
            echo '成功处理：', $count, PHP_EOL;
        } else {
            $this->syncSystem2Customer($system_fields, $clientId);
            $this->syncSystem2FieldGroup($system_fields, $clientId);
            echo '成功处理client：', $clientId, PHP_EOL;
        }
    }

    /*
     * 系统字段增加线索的经纬度
     *
     */
    public function actionAddSysFieldForLead()
    {
        $companySetting = [
            [
                'id'         => 'lonlat',
                'name'       => '经维度坐标',
                'require'    => 0,
                'hint'       => '',
                'edit_hint'  => 0,
                'group_id'   => 1, // 基本信息分组
                'edit_required' => 0,
                'is_exportable' => 0,
                'order' => 10,
                'app_order' => 10,
                'disable_flag'  => 1,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 1,// 是否可编辑，1可以，0否
                'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
            ],
        ];

        $this->syncSystemFields(Constants::TYPE_LEAD, $companySetting);
    }

    /**
     * @param int $clientId
     * @param null $lastNumber
     */
    public function actionAddFieldForLead($clientId = 1, $lastNumber = null)
    {

        $fieldInfo = [
            'id'                  => 'lonlat',
            'type'                => Constants::TYPE_LEAD,// lead
            'group_id'            => '1', // 基本信息分组
            'base'                => 1,
            'name'                => '经维度坐标',
            'field_type'          => CustomFieldService::FIELD_TYPE_TEXT,
            'require'             => 0,
            'edit_required'       => 1,
            'disable_flag'        => 1,//前端是否隐藏，1隐藏，0不隐藏
            'edit_hide'           => 0,// 是否可编辑，1可以，0否
            'default'             => '',
            'edit_default'        => 0,
            'hint'                => '',
            'edit_hint'           => 0,
            'is_exportable'       => 0,
            'is_editable'         => 1,
            'is_list'             => 0,
            'columns'             => 'longitude',
            'relation_type'       => 0,
            'relation_field'      => '',
            'relation_field_type' => 0,
            'relation_field_name' => '',
            'export_scenario'     => '',
            'export_group'        => '',
            'order' => 10,
            'app_order' => 10,
            'readonly'            => 0,
            'create_time'         => date('Y-m-d H:i:s'),
            'update_time'         => date('Y-m-d H:i:s')
        ];

        $system_fields[] = $fieldInfo;

        if (empty($clientId)) {
            //获取所有client 遍历
            $clients = $this->getClientList(0, false, null, null, 0, 0, $lastNumber);
            $count = 0;
            echo '共需处理：', count($clients), PHP_EOL;
            foreach ($clients as $client) {
                echo '开始处理：', $client->client_id . ' ', PHP_EOL;
                //同步规则：system_field 表中存在 ，目标表中不存在则插入
                try {
                    $this->syncSystem2Customer($system_fields, $client->client_id);
                    $this->syncSystem2FieldGroup($system_fields, $client->client_id);
                    $count++;
                } catch (Throwable $e) {
                    LogUtil::error('同步字段出错， client_id: ' . $client->client_id);
                    LogUtil::error($e->getMessage());
                }
            }
            echo '成功处理：', $count, PHP_EOL;
        } else {
            $this->syncSystem2Customer($system_fields, $clientId);
            $this->syncSystem2FieldGroup($system_fields, $clientId);
            echo '成功处理client：', $clientId, PHP_EOL;
        }
    }

    /**
     * 商机的apporder重新设置排序
     */
    public function actionOpportunitySystemFieldAppOrderSet() {
        $field_data = [
            [
                'app_order' => 15,
                'id' => 'name'
            ],
            [
                'app_order' => 14,
                'id' => 'company_id'
            ],
            [
                'app_order' => 14,
                'id' => 'coustomer_id'
            ],
            [
                'app_order' => 12,
                'id' => 'department'
            ],
            [
                'app_order' => 11,
                'id' => 'amount',
            ],
            [
                'app_order' => 10,
                'id' => 'currency'
            ],
            [
                'app_order' => 9,
                'id' => 'exchange_rate'
            ],
            [
                'app_order' => 8,
                'id' => 'account_date'
            ],
            [
                'app_order' => 7,
                'id' => 'origin'
            ],
            [
                'app_order' => 6,
                'id' => 'type'
            ],
            [
                'app_order' => 5,
                'id' => 'flow_id'
            ],
            [
                'app_order' => 4,
                'id' => 'stage'
            ],
            [
                'app_order' => 3,
                'id' => 'fail_type'
            ],
            [
                'app_order' => 2,
                'id' => 'fail_remark'
            ],
            [
                'app_order' => 1,
                'id' => 'remark'
            ],
        ];

        $count = 0;

        foreach($field_data as $field) {
            $params = [
                ':app_order' => $field['app_order'],
                ':id' => $field['id'],
                ':type' => 9,
                ':group_id'   => 1,
            ];

            $sql = "update tbl_system_field set app_order=:app_order where id=:id and type=:type and group_id=:group_id";
            $n = Yii::app()->db->getCommandBuilder()->createSqlCommand($sql)->execute($params);
            $count += $n;
            echo "成功处理{$count}条数据";
        }
    }

    /**
     * 更新商机字段排序到字段自定义表
     * @param array $client_ids
     * @param int $startClient
     * @param null $lastNumber
     * @throws CDbException
     * @throws CException
     * @throws ProcessException
     */
    public function actionSyncOpportunitySystemFieldAppOrder(array $client_ids = [1], $startClient = 0, $lastNumber = null) {
        $params =[
            ':type' => 9,
            ':group_id'   => 1,
        ];

        $sql = "SELECT id,app_order FROM tbl_system_field WHERE type=:type AND group_id=:group_id";
        $result = Yii::app()->db->getCommandBuilder()->createSqlCommand($sql)->queryAll(true, $params);

        if(empty($client_ids)) {
            $clients = $this->getClientList(0, false, null, null, $startClient, 0, $lastNumber);
        }else {
            $clients = [];
            foreach($client_ids as $client_id) {
                $client = new \stdClass();
                $client->client_id = $client_id;
                array_push($clients, $client);
            }
        }

        foreach($clients as $client) {
            $client_id = $client->client_id;
            $Db = ProjectActiveRecord::getDbByClientId($client_id);
            foreach($result as $field) {
                $app_order = $field['app_order'];
                $id = $field['id'];
                $sql = "update tbl_custom_field set app_order = {$app_order} WHERE client_id={$client_id} and type=9 and base =1 and id='{$id}' ";
                $Db->createCommand($sql)->execute();
            }
            unset($Db);
            self::info("已完成的client_id:".$client_id);
            usleep(100);
        }
    }

    //改变customer的email,使其可编辑,非必填
    //ddl index (client_id type id)
    public function actionChangeCustomerEmailRequired(int $type = 1, string $greyClientIdsStr = '', int $greyNumber = 0)
    {
        switch ($type) {
            case 1: //灰度clientIds
                self::info("灰度clientIds为{$greyClientIdsStr}的clientId");
                $greyClientIds = explode(',', $greyClientIdsStr);
                foreach ($greyClientIds as $clientId) {
                    try {
                        self::info("clientId:[$clientId]");
                        $db = ProjectActiveRecord::getDbByClientId($clientId);
                        if (empty($db)) continue;
                        $updateSql = "UPDATE tbl_custom_field set is_editable = 1 , edit_required = 1 , `require` = 1 , edit_hide = 0 , disable_flag = 0 WHERE client_id = $clientId AND type = 5 AND id = 'email' AND base = 1";
                        $db->createCommand($updateSql)->execute();
                    } catch (Throwable $throwable) {
                        self::info($throwable->getMessage());
                    }
                }
                break;
            case 2: //灰度尾号
                self::info("灰度尾号为{$greyNumber}的clientId");
                $greyClientIds = array_column($this->getClientList(0, false, null, null, 0, 0, $greyNumber), 'client_id');
                foreach ($greyClientIds as $clientId) {
                    try {
                        self::info("clientId:[$clientId]");
                        $db = ProjectActiveRecord::getDbByClientId($clientId);
                        if (empty($db)) continue;
                        $updateSql = "UPDATE tbl_custom_field set is_editable = 1 , edit_required = 1 , `require` = 1 , edit_hide = 0 , disable_flag = 0 WHERE client_id = $clientId AND type = 5 AND id = 'email' AND base = 1";
                        $db->createCommand($updateSql)->execute();
                    } catch (Throwable $throwable) {
                        self::info($throwable->getMessage());
                    }
                }
                break;
            case 3: //全量
                self::info("全量client");
                $dbList = \common\library\account\service\DbService::getDbList(DbSet::TYPE_MYSQL);
                foreach ($dbList as $dbInfo) {
                    try {
                        $setId = $dbInfo['set_id'];
                        $dbName = $dbInfo['name'];
                        self::info("DB:[$dbName]");
                        $db = ProjectActiveRecord::getDbByDbSetId($setId);
                        if (empty($db)) continue;
                        ProjectActiveRecord::setConnection($db);
                        $updateSql = "UPDATE tbl_custom_field set is_editable = 1 , edit_required = 1 , `require` = 1 , edit_hide = 0 , disable_flag = 0 WHERE type = 5 AND id = 'email' AND base = 1";
                        $db->createCommand($updateSql)->execute();
                    } catch (Throwable $throwable) {
                        self::info($throwable->getMessage());
                    }
                }
                break;
        }
    }


    /*
     * 系统字段增加搜索关键字
     *
     */
    public function actionAddSysFieldForLeadADS()
    {
        $setting = [
            [
                'id'         => 'ad_keyword',
                'name'       => '搜索关键字',
                'require'    => 0,
                'hint'       => '',
                'edit_hint'  => '',
                'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
                'edit_required'       => 1,
                'disable_flag'        => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide'           => 0,// 是否可编辑，1可以，0否
                'default'             => '',
                'edit_default'        => 0,
                'is_exportable'       => 0,
                'is_editable'         => 1,
                'order' => 20,
                'app_order' =>20,
            ],
        ];

        $this->syncSystemFields(Constants::TYPE_LEAD,$setting);
    }

    /**
     * @param int $clientId
     * @param null $lastNumber
     */
    public function actionAddFieldForLeadADS($clientId = 1, $lastNumber = null)
    {

        $fieldInfo = [
            'id'                  => 'ad_keyword',
            'type'                => Constants::TYPE_LEAD,// 公司
            'group_id'            => 1, // 联系信息分组
            'base'                => 1,
            'name'                => '搜索关键字',
            'field_type'          => CustomFieldService::FIELD_TYPE_TEXT,
            'require'             => 0,
            'edit_required'       => 1,
            'disable_flag'        => 0,//前端是否隐藏，1隐藏，0不隐藏
            'edit_hide'           => 0,// 是否可编辑，1可以，0否
            'default'             => '',
            'edit_default'        => 0,
            'hint'                => '',
            'edit_hint'           => 0,
            'is_exportable'       => 0,
            'is_editable'         => 1,
            'is_list'             => 0,
            'columns'             => 'ad_keyword',
            'relation_type'       => 0,
            'relation_field'      => '',
            'relation_field_type' => 0,
            'relation_field_name' => '',
            'export_scenario'     => '',
            'export_group'        => '',
            'readonly'            => 0,
            'order' => 20,
            'app_order' =>20,
            'create_time'         => date('Y-m-d H:i:s'),
            'update_time'         => date('Y-m-d H:i:s')
        ];


        $system_fields[] = $fieldInfo;

        if (empty($clientId)) {
            //获取所有client 遍历
            $clients = $this->getClientList(0, false, null, null, 0, 0, $lastNumber);
            $count = 0;
            echo '共需处理：', count($clients), PHP_EOL;
            foreach ($clients as $client) {
                echo '开始处理：', $client->client_id . ' ', PHP_EOL;
                //同步规则：system_field 表中存在 ，目标表中不存在则插入
                try {
                    $this->syncSystem2Customer($system_fields, $client->client_id);
                    $this->syncSystem2FieldGroup($system_fields, $client->client_id);
                    $count++;
                } catch (Throwable $e) {
                    LogUtil::error('同步字段出错， client_id: ' . $client->client_id);
                    LogUtil::error($e->getMessage());
                }
            }
            echo '成功处理：', $count, PHP_EOL;
        } else {
            $this->syncSystem2Customer($system_fields, $clientId);
            $this->syncSystem2FieldGroup($system_fields, $clientId);
            echo '成功处理client：', $clientId, PHP_EOL;
        }
    }

    /**
     * @smart1.6
     */
    public function actionFixForPrivilegeField()
    {
        $fixMap = [
            ['condition' => ['type' => Constants::TYPE_ORDER, 'id' => 'create_user'], 'update' => ['is_editable' => 0, 'require' => 0]],
        ];

        $this->actionFixSystemFieldType($fixMap);
    }

    public function actionFixForUniqueField()
    {
        $fixMap = [
            ['condition' => ['type' => Constants::TYPE_COMPANY, 'id' => 'name'], 'update' => ['unique_check' => 1, 'unique_prevent' => 1]],
        ];

        $this->actionFixSystemFieldType($fixMap);
    }

    public function actionFixForEditHint()
    {
        $fixMap = [
            ['condition' => ['type' => Constants::TYPE_OPPORTUNITY, 'id' => 'remark'], 'update' => ['edit_hint' => 1]],
        ];

        $this->actionFixSystemFieldType($fixMap);
    }

    /**
     * @smart1.6
     */

    public function actionInitUniqueFieldSetting($clientId, $grey = 1, $greyNum = null)
    {
        $map = [
            ['condition' => ['type' => Constants::TYPE_ORDER, 'id' => 'create_user'], 'update' => ['is_editable' => 0, 'require' => 0]],
            ['condition' => ['type' => Constants::TYPE_CUSTOMER, 'id' => 'email'], 'update' => ['unique_check' => CustomFieldService::UNIQUE_CHECK_ENABLE,]],
//            ['condition' => ['type' => Constants::TYPE_CUSTOMER, 'base' => 0, 'field_type' => CustomFieldService::FIELD_TYPE_TEXT], 'update' => ['edit_unique' => 1,]],
            ['condition' => ['type' => Constants::TYPE_COMPANY, 'id' => 'serial_id',], 'update' => ['edit_hint' => 1,'is_editable' => 1, 'edit_required' => 0, 'disable_flag' => 0, 'edit_hide' => 0, 'require' => 0, 'unique_check' => CustomFieldService::UNIQUE_CHECK_ENABLE, 'unique_message' => '', 'readonly' => 0]],
            ['condition' => ['type' => Constants::TYPE_COMPANY, 'id' => 'name',], 'update' => ['edit_hint' => 1,]],
            ['condition' => ['type' => Constants::TYPE_ORDER, 'id' => 'order_no'], 'update' => ['is_editable' => 1, 'edit_required' => 0, 'require' => 0, 'edit_hide' => 0, 'disable_flag' => 0,'readonly' => 0,]],
            ['condition' => ['type' => Constants::TYPE_QUOTATION, 'id' => 'quotation_no'], 'update' => ['is_editable' => 1, 'edit_required' => 0, 'require' => 0, 'edit_hide' => 0, 'disable_flag' => 0,'readonly' => 0,]],
            ['condition' => ['type' => Constants::TYPE_PRODUCT, 'id' => 'product_no'], 'update' => ['is_editable' => 1, 'edit_required' => 0, 'require' => 0, 'edit_hide' => 0, 'disable_flag' => 0,'readonly' => 0,]],
            ['condition' => ['type' => Constants::TYPE_PRODUCT, 'id' => 'serial_id'], 'update' => ['is_editable' => 1, 'edit_required' => 0, 'require' => 0, 'edit_hide' => 0, 'disable_flag' => 0,'readonly' => 0,]],
            ['condition' => ['base' => 1], 'update' => ['edit_hint' => 1,]],
        ];
//        foreach (CustomFieldService::FIELD_UNIQUE_CHECK_MAP as $type => $fieldIds) {
//            foreach ($fieldIds as $fieldId) {
//                $map[] = ['condition' => ['type' => $type, 'id' => $fieldId], 'update' => ['edit_unique' => 1,]];
//            }
//        }
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey && !is_null($greyNum)) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $this->actionFixSystemFieldType($map);
        }
        foreach ($clientIds as $clientId) {
            $db = ProjectActiveRecord::getDbByClientId($clientId);
            if (!$db) {
                echo "skip for client {$clientId} \n";
                continue;
            }
            echo "update client {$clientId}" . PHP_EOL;
            foreach ($map as $item) {
                $item['condition']['client_id'] = $clientId;
                $criteria = new CDbCriteria();
                foreach ($item['condition'] as $key => $condition) {
                    $criteria->addCondition("$key=:$key");
                }
                $criteria->params += $item['condition'];
                $result = $db->getCommandBuilder()->createUpdateCommand('tbl_custom_field', $item['update'],
                    $criteria)->execute();
                echo 'result: ' . $result . ', update tbl_custom_field: ' . json_encode($item) . PHP_EOL;
            }
        }
    }


    /*
     * 单据产品添加unique_id
     */
    public function actionAddSysFieldForOrderProduct()
    {
        $companySetting = [
            [
                'id'         => 'unique_id',
                'name'       => 'unique_id',
                'require'    => 0,
                'hint'       => '',
                'edit_hint'  => 0,
                'group_id'   => 2, // 基本信息分组
                'edit_required' => 1,
                'is_exportable' => 0,
                'order' => 10,
                'app_order' => 10,
                'disable_flag'  => 1,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可编辑，1可以，0否
                'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                'is_list'       => 1,
            ],
        ];

        $this->syncSystemFields(Constants::TYPE_ORDER, $companySetting);
    }
    public function actionFixForCustomerField()
    {
        $fixMap = [
            ['condition' => ['type' => Constants::TYPE_CUSTOMER, 'id' => 'email'], 'update' => ['edit_required' => 1,]],
        ];

        $this->actionFixSystemFieldType($fixMap);
    }


    /**
     * @smart1.7
     */
    public function actionUpdateProductFields($clientId, $grey = 1, $greyNum = 0, $filterSystemId = '', $filterRoleName = '')
    {
        $all = false;
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey && !is_null($greyNum)) {
//            $clientIds = \common\library\GreyEnvHelper::getGreyClientIds();
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $all = true;
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        $settings = [
            // 基本信息
            [
                'id' => 'product_no',
                'type' => '1',
                'group_id' => '1',
                'base' => '1',
                'name' => '产品编号',
                'field_type' => '1',
                'require' => '0',
                'edit_required' => '0',
                'disable_flag' => '0',
                'edit_hide' => '0',
                'default' => '',
                'edit_default' => '0',
                'hint' => '',
                'edit_hint' => '1',
                'is_exportable' => '1',
                'is_editable' => '1',
                'is_list' => '0',
                'columns' => 'product_no',
                'relation_type' => '0',
                'relation_field' => '',
                'relation_field_type' => '0',
                'relation_field_name' => '',
                'export_scenario' => '2,3',
                'export_group' => '0',
                'order' => '21',
                'app_order' => '21',
                'readonly' => '0',
                'ext_info' => '',
            ],
            [
                'id' => 'name',
                'type' => '1',
                'group_id' => '1',
                'base' => '1',
                'name' => '产品名称',
                'field_type' => '1',
                'require' => '1',
                'edit_required' => '0',
                'disable_flag' => '0',
                'edit_hide' => '0',
                'default' => '',
                'edit_default' => '0',
                'hint' => '',
                'edit_hint' => '1',
                'is_exportable' => '1',
                'is_editable' => '1',
                'is_list' => '0',
                'columns' => 'name',
                'relation_type' => '0',
                'relation_field' => '',
                'relation_field_type' => '0',
                'relation_field_name' => '',
                'export_scenario' => '2,3',
                'export_group' => '0',
                'order' => '20',
                'app_order' => '20',
                'readonly' => '0',
                'ext_info' => '',
            ],
            [
                'id' => 'model',
                'type' => '1',
                'group_id' => '1',
                'base' => '1',
                'name' => '产品型号',
                'field_type' => '1',
                'require' => '0',
                'edit_required' => '1',
                'disable_flag' => '0',
                'edit_hide' => '1',
                'default' => '',
                'edit_default' => '0',
                'hint' => '',
                'edit_hint' => '1',
                'is_exportable' => '1',
                'is_editable' => '1',
                'is_list' => '0',
                'columns' => 'model',
                'relation_type' => '0',
                'relation_field' => '',
                'relation_field_type' => '0',
                'relation_field_name' => '',
                'export_scenario' => '2,3',
                'export_group' => '0',
                'order' => '19',
                'app_order' => '19',
                'readonly' => '0',
                'ext_info' => '',
            ],
            [
                'id' => 'group_id',
                'type' => '1',
                'group_id' => '1',
                'base' => '1',
                'name' => '产品分组',
                'field_type' => '3',
                'require' => '0',
                'edit_required' => '1',
                'disable_flag' => '0',
                'edit_hide' => '0',
                'default' => '',
                'edit_default' => '1',
                'hint' => '',
                'edit_hint' => '1',
                'is_exportable' => '1',
                'is_editable' => '1',
                'is_list' => '0',
                'columns' => 'group_id',
                'relation_type' => '0',
                'relation_field' => '',
                'relation_field_type' => '0',
                'relation_field_name' => '',
                'export_scenario' => '2,3',
                'export_group' => '7',
                'order' => '18',
                'app_order' => '18',
                'readonly' => '0',
                'ext_info' => '',
            ],
            [
                'id' => 'description',
                'type' => '1',
                'group_id' => '1',
                'base' => '1',
                'name' => '产品描述',
                'field_type' => '2',
                'require' => '0',
                'edit_required' => '1',
                'disable_flag' => '0',
                'edit_hide' => '1',
                'default' => '',
                'edit_default' => '0',
                'hint' => '',
                'edit_hint' => '1',
                'is_exportable' => '1',
                'is_editable' => '1',
                'is_list' => '0',
                'columns' => 'description',
                'relation_type' => '0',
                'relation_field' => '',
                'relation_field_type' => '0',
                'relation_field_name' => '',
                'export_scenario' => '2,3',
                'export_group' => '0',
                'order' => '17',
                'app_order' => '17',
                'readonly' => '0',
                'ext_info' => '',
            ],
            [
                'id' => 'images',
                'type' => '1',
                'group_id' => '1',
                'base' => '1',
                'name' => '产品图片',
                'field_type' => '6',
                'require' => '0',
                'edit_required' => '1',
                'disable_flag' => '0',
                'edit_hide' => '1',
                'default' => '',
                'edit_default' => '0',
                'hint' => '',
                'edit_hint' => '1',
                'is_exportable' => '1',
                'is_editable' => '1',
                'is_list' => '0',
                'columns' => 'images',
                'relation_type' => '0',
                'relation_field' => '',
                'relation_field_type' => '0',
                'relation_field_name' => '',
                'export_scenario' => '2,3',
                'export_group' => '7',
                'order' => '16',
                'app_order' => '16',
                'readonly' => '0',
                'ext_info' => '',
            ],
            // 价格信息
            [
                'id' => 'fob',
                'type' => '1',
                'group_id' => '2',
                'base' => '1',
                'name' => '离岸价',
                'field_type' => '0',
                'require' => '0',
                'edit_required' => '1',
                'disable_flag' => '0',
                'edit_hide' => '1',
                'default' => '',
                'edit_default' => '0',
                'hint' => '',
                'edit_hint' => '1',
                'is_exportable' => '1',
                'is_editable' => '1',
                'is_list' => '0',
                'columns' => 'price_currency,price_min,price_max',
                'relation_type' => '0',
                'relation_field' => '',
                'relation_field_type' => '0',
                'relation_field_name' => '',
                'export_scenario' => '2,3',
                'export_group' => '0',
                'order' => '15',
                'app_order' => '15',
                'readonly' => '0',
                'ext_info' => '',
            ],
            [
                'id' => 'cost_with_tax',
                'type' => '1',
                'group_id' => '2',
                'base' => '1',
                'name' => '含税成本价',
                'field_type' => '0',
                'require' => '0',
                'edit_required' => '1',
                'disable_flag' => '0',
                'edit_hide' => '1',
                'default' => '',
                'edit_default' => '0',
                'hint' => '',
                'edit_hint' => '1',
                'is_exportable' => '1',
                'is_editable' => '1',
                'is_list' => '0',
                'columns' => 'cost_currency,cost',
                'relation_type' => '0',
                'relation_field' => '',
                'relation_field_type' => '0',
                'relation_field_name' => '',
                'export_scenario' => '2,3',
                'export_group' => '0',
                'order' => '14',
                'app_order' => '14',
                'readonly' => '0',
                'ext_info' => '',
            ],
            [
                'id' => 'unit',
                'type' => '1',
                'group_id' => '2',
                'base' => '1',
                'name' => '计量单位',
                'field_type' => '3',
                'require' => '0',
                'edit_required' => '1',
                'disable_flag' => '0',
                'edit_hide' => '1',
                'default' => '',
                'edit_default' => '0',
                'hint' => '',
                'edit_hint' => '1',
                'is_exportable' => '1',
                'is_editable' => '1',
                'is_list' => '0',
                'columns' => 'unit',
                'relation_type' => '0',
                'relation_field' => '',
                'relation_field_type' => '0',
                'relation_field_name' => '',
                'export_scenario' => '2,3',
                'export_group' => '0',
                'order' => '13',
                'app_order' => '13',
                'readonly' => '0',
                'ext_info' => '',
            ],
            [
                'id' => 'minimum_order_quantity',
                'type' => '1',
                'group_id' => '2',
                'base' => '1',
                'name' => '最小起订量',
                'field_type' => '0',
                'require' => '0',
                'edit_required' => '1',
                'disable_flag' => '0',
                'edit_hide' => '1',
                'default' => '',
                'edit_default' => '0',
                'hint' => '',
                'edit_hint' => '1',
                'is_exportable' => '1',
                'is_editable' => '1',
                'is_list' => '0',
                'columns' => 'quantity',
                'relation_type' => '0',
                'relation_field' => '',
                'relation_field_type' => '0',
                'relation_field_name' => '',
                'export_scenario' => '2,3',
                'export_group' => '0',
                'order' => '12',
                'app_order' => '12',
                'readonly' => '0',
                'ext_info' => '',
            ],
            // 包装信息
            [
                'id'         => 'package_size',
                'name'       => '包装尺寸',
                'require'    => 0,
                'hint'       => '',
                'edit_hint'  => 0,
                'group_id'   => 3,
                'edit_required' => 1,
                'is_exportable' => 0,
                'order' => 11,
                'columns' => implode(',', ['package_size_length', 'package_size_weight', 'package_size_height']),
                'app_order' => 11,
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可编辑，1可以，0否
                'field_type' => CustomFieldService::FIELD_TYPE_OTHER,
                'is_list'       => 0,
            ],
            [
                'id' => 'package_volume',
                'type' => '1',
                'group_id' => '3',
                'base' => '1',
                'name' => '包装体积',
                'field_type' => '5',
                'require' => '0',
                'edit_required' => '1',
                'disable_flag' => '0',
                'edit_hide' => '1',
                'default' => '',
                'edit_default' => '0',
                'hint' => '',
                'edit_hint' => '1',
                'is_exportable' => '1',
                'is_editable' => '1',
                'is_list' => '0',
                'columns' => 'package_volume',
                'relation_type' => '0',
                'relation_field' => '',
                'relation_field_type' => '0',
                'relation_field_name' => '',
                'export_scenario' => '2,3',
                'export_group' => '0',
                'order' => '10',
                'app_order' => '10',
                'readonly' => '0',
                'ext_info' => '[2]',
            ],
            [
                'id' => 'package_gross_weight',
                'type' => '1',
                'group_id' => '3',
                'base' => '1',
                'name' => '包装毛重',
                'field_type' => '5',
                'require' => '0',
                'edit_required' => '1',
                'disable_flag' => '0',
                'edit_hide' => '1',
                'default' => '',
                'edit_default' => '0',
                'hint' => '',
                'edit_hint' => '1',
                'is_exportable' => '1',
                'is_editable' => '1',
                'is_list' => '0',
                'columns' => 'package_gross_weight',
                'relation_type' => '0',
                'relation_field' => '',
                'relation_field_type' => '0',
                'relation_field_name' => '',
                'export_scenario' => '2,3',
                'export_group' => '0',
                'order' => '9',
                'app_order' => '9',
                'readonly' => '0',
                'ext_info' => '[2]',
            ],
            [
                'id' => 'count_per_package',
                'type' => '1',
                'group_id' => '3',
                'base' => '1',
                'name' => '每包装产品数',
                'field_type' => '5',
                'require' => '0',
                'edit_required' => '1',
                'disable_flag' => '0',
                'edit_hide' => '1',
                'default' => '',
                'edit_default' => '0',
                'hint' => '',
                'edit_hint' => '1',
                'is_exportable' => '1',
                'is_editable' => '1',
                'is_list' => '0',
                'columns' => 'count_per_package',
                'relation_type' => '0',
                'relation_field' => '',
                'relation_field_type' => '0',
                'relation_field_name' => '',
                'export_scenario' => '2,3',
                'export_group' => '0',
                'order' => '8',
                'app_order' => '8',
                'readonly' => '0',
                'ext_info' => '',
            ],
            [
                'id' => 'package_remark',
                'type' => '1',
                'group_id' => '3',
                'base' => '1',
                'name' => '包装说明',
                'field_type' => '2',
                'require' => '0',
                'edit_required' => '1',
                'disable_flag' => '0',
                'edit_hide' => '1',
                'default' => '',
                'edit_default' => '0',
                'hint' => '',
                'edit_hint' => '1',
                'is_exportable' => '1',
                'is_editable' => '1',
                'is_list' => '0',
                'columns' => 'package_remark',
                'relation_type' => '0',
                'relation_field' => '',
                'relation_field_type' => '0',
                'relation_field_name' => '',
                'export_scenario' => '2,3',
                'export_group' => '7',
                'order' => '7',
                'app_order' => '7',
                'readonly' => '0',
                'ext_info' => '',
            ],
            [
                'id' => 'package_unit',
                'type' => '1',
                'group_id' => '3',
                'base' => '1',
                'name' => '包装单位',
                'field_type' => '3',
                'require' => '0',
                'edit_required' => '1',
                'disable_flag' => '1',
                'edit_hide' => '1',
                'default' => '',
                'edit_default' => '0',
                'hint' => '',
                'edit_hint' => '1',
                'is_exportable' => '1',
                'is_editable' => '1',
                'is_list' => '0',
                'columns' => 'package_unit',
                'relation_type' => '0',
                'relation_field' => '',
                'relation_field_type' => '0',
                'relation_field_name' => '',
                'export_scenario' => '2,3',
                'export_group' => '0',
                'order' => '6',
                'app_order' => '6',
                'readonly' => '0',
                'ext_info' => '',
            ],

            // 产品介绍
            [
                'id' => 'category_ids',
                'type' => '1',
                'group_id' => '4',
                'base' => '1',
                'name' => '产品类目',
                'field_type' => '0',
                'require' => '0',
                'edit_required' => '1',
                'disable_flag' => '0',
                'edit_hide' => '1',
                'default' => '',
                'edit_default' => '0',
                'hint' => '',
                'edit_hint' => '1',
                'is_exportable' => '1',
                'is_editable' => '1',
                'is_list' => '0',
                'columns' => 'category_ids',
                'relation_type' => '0',
                'relation_field' => '',
                'relation_field_type' => '0',
                'relation_field_name' => '',
                'export_scenario' => '2,3',
                'export_group' => '7',
                'order' => '5',
                'app_order' => '5',
                'readonly' => '0',
                'ext_info' => '',
            ],
            [
                'id' => 'hs_code',
                'type' => '1',
                'group_id' => '4',
                'base' => '1',
                'name' => '海关编码',
                'field_type' => '1',
                'require' => '0',
                'edit_required' => '1',
                'disable_flag' => '0',
                'edit_hide' => '1',
                'default' => '',
                'edit_default' => '0',
                'hint' => '',
                'edit_hint' => '1',
                'is_exportable' => '1',
                'is_editable' => '1',
                'is_list' => '0',
                'columns' => 'hs_code',
                'relation_type' => '0',
                'relation_field' => '',
                'relation_field_type' => '0',
                'relation_field_name' => '',
                'export_scenario' => '2,3',
                'export_group' => '7',
                'order' => '4',
                'app_order' => '4',
                'readonly' => '0',
                'ext_info' => '',
            ],
            [
                'id' => 'info_json',
                'type' => '1',
                'group_id' => '4',
                'base' => '1',
                'name' => '产品属性',
                'field_type' => '0',
                'require' => '0',
                'edit_required' => '0',
                'disable_flag' => '0',
                'edit_hide' => '0',
                'default' => '',
                'edit_default' => '0',
                'hint' => '',
                'edit_hint' => '1',
                'is_exportable' => '1',
                'is_editable' => '1',
                'is_list' => '0',
                'columns' => 'info_json',
                'relation_type' => '0',
                'relation_field' => '',
                'relation_field_type' => '0',
                'relation_field_name' => '',
                'export_scenario' => '2,3',
                'export_group' => '7',
                'order' => '3',
                'app_order' => '3',
                'readonly' => '0',
                'ext_info' => '',
            ],
            [
                'id' => 'from_url',
                'type' => '1',
                'group_id' => '4',
                'base' => '1',
                'name' => '产品链接',
                'field_type' => '1',
                'require' => '0',
                'edit_required' => '0',
                'disable_flag' => '0',
                'edit_hide' => '0',
                'default' => '',
                'edit_default' => '0',
                'hint' => '',
                'edit_hint' => '1',
                'is_exportable' => '1',
                'is_editable' => '1',
                'is_list' => '0',
                'columns' => 'from_url',
                'relation_type' => '0',
                'relation_field' => '',
                'relation_field_type' => '0',
                'relation_field_name' => '',
                'export_scenario' => '2,3',
                'export_group' => '7',
                'order' => '2',
                'app_order' => '2',
                'readonly' => '0',
                'ext_info' => '',
            ],
            [
                'id' => 'product_remark',
                'type' => '1',
                'group_id' => '4',
                'base' => '1',
                'name' => '产品备注',
                'field_type' => '2',
                'require' => '0',
                'edit_required' => '1',
                'disable_flag' => '0',
                'edit_hide' => '1',
                'default' => '',
                'edit_default' => '1',
                'hint' => '',
                'edit_hint' => '1',
                'is_exportable' => '1',
                'is_editable' => '1',
                'is_list' => '0',
                'columns' => 'product_remark',
                'relation_type' => '0',
                'relation_field' => '',
                'relation_field_type' => '0',
                'relation_field_name' => '',
                'export_scenario' => '2,3',
                'export_group' => '7',
                'order' => '1',
                'app_order' => '1',
                'readonly' => '0',
                'ext_info' => '',
            ],
        ];

        $fixMap = [
            ['condition' => ['type' => Constants::TYPE_PRODUCT, 'id' => 'package_unit'], 'update' => ['enable_flag' => 1, 'disable_flag' => 1]],
        ];
        if ($all) {
            $this->syncSystemFields(Constants::TYPE_PRODUCT, $settings);
            $this->updateCustomFieldByMap($fixMap, $clientId, $grey, $greyNum);
        }
        $updates = [];
        $date = date('Y-m-d H:i:s');
        foreach ($settings as &$setting) {
            $id = $setting['id'];
            $setting = array_replace([
                'hint'                => '',
                'edit_hint'           => 1,
                'edit_required'       => 1,
                'edit_hide'           => 1,
                'disable_flag'        => 0,
                'edit_default'        => 0,
                'type'                => Constants::TYPE_PRODUCT,
                'create_time'         => $date,
                'update_time'         => $date,
                'export_group'        => 0,
                'relation_type'       => 0,
                'relation_field'      => '',
                'relation_field_type' => 0,
                'is_editable'         => 1,
                'readonly'            => 0,
                'ext_info'            => '',
                'base'                => CustomFieldService::FIELD_BASE_OF_SYSTEM,
                'default'             => '',
                'relation_field_name' => '',
            ], $setting);
            $updates[$id]['condition'] =  ['type' => Constants::TYPE_PRODUCT, 'id' => $id];
            $setting['columns'] = json_encode(explode(',', $setting['columns']));
            $updates[$id]['update'] = $setting;
        }

        foreach ($clientIds as $clientId) {
            $db = ProjectActiveRecord::getDbByClientId($clientId);
            if (!$db) {
                echo "skip for client {$clientId} \n";
                continue;
            }
            echo "update client {$clientId}" . PHP_EOL;

            // 处理自定义字段
            $customFieldGroupList = $db->createCommand("SELECT * FROM tbl_field_group WHERE client_id = $clientId AND id REGEXP '[0-9]+' AND `type` = 1; ")->queryAll(true);
            $customFieldGroupMap = [];
            foreach ($customFieldGroupList as $customFieldGroupItem) {
                $customFieldGroupMap[$customFieldGroupItem['group_id']][] = $customFieldGroupItem['id'];
            }
            $moveGroup = [
                2 => 4,
                3 => 2,
                4 => 3,
                5 => 4,
                6 => 4
            ];
            $runBefore = $db->createCommand("select id from tbl_custom_field where client_id = $clientId and type = 1 and id = 'package_size' and enable_flag = 1")->queryScalar();
            if (!$runBefore) {
                $updateCustomFieldGroupSql = [];
                foreach ($moveGroup as $fromGroup => $toGroup) {
                    if (!empty($customFieldGroupMap[$fromGroup])) {
                        $updateCustomFieldGroupSql[] = "update tbl_field_group set group_id = {$toGroup} where client_id = $clientId and `type` = 1 and group_id = {$fromGroup} and id in (" . implode(',', $customFieldGroupMap[$fromGroup]) .")";
                    }
                }
                if (count($updateCustomFieldGroupSql)) {
                    $db->createCommand(implode(';', $updateCustomFieldGroupSql))->execute();
                }
            }

            $this->syncForClient($settings, $clientId);
            foreach ($updates as $item) {
                $item['condition']['client_id'] = $clientId;
                $criteria = new CDbCriteria();
                foreach ($item['condition'] as $key => $condition) {
                    $criteria->addCondition("$key=:$key");
                }
                $groupItem = [
                    'group_id' => $item['update']['group_id'],
                ];
                $criteria->params += $item['condition'];
                $result = $db->getCommandBuilder()->createUpdateCommand('tbl_custom_field', $item['update'],
                    $criteria)->execute();
                $result2 = $db->getCommandBuilder()->createUpdateCommand('tbl_field_group', $groupItem,
                    $criteria)->execute();
                echo 'result: ' . $result . ',  update tbl_custom_field: ' . json_encode($item) . PHP_EOL;
                echo 'result2: ' . $result2 . ',  update tbl_field_group: ' . json_encode($groupItem) . PHP_EOL;
            }
            (new \common\library\privilege_v3\PrivilegeField($clientId))->flushCache();
        }
    }

    /**
     * @mkt-1.0
     */
    public function actionInitMarketingProductFields()
    {
        $settings = [
            // 基本信息
            [
                'id' => 'name',
                'type' => '1',
                'group_id' => '1',
                'base' => '1',
                'name' => '产品名称',
                'field_type' => '1',
                'require' => '1',
                'edit_required' => '0',
                'disable_flag' => '0',
                'edit_hide' => '0',
                'default' => '',
                'edit_default' => '0',
                'hint' => '',
                'edit_hint' => '1',
                'is_exportable' => '1',
                'is_editable' => '1',
                'is_list' => '0',
                'columns' => 'name',
                'relation_type' => '0',
                'relation_field' => '',
                'relation_field_type' => '0',
                'relation_field_name' => '',
                'export_scenario' => '',
                'export_group' => '0',
                'order' => '21',
                'app_order' => '21',
                'readonly' => '0',
                'ext_info' => '',
            ],
            [
                'id' => 'category_ids',
                'type' => '1',
                'group_id' => '1',
                'base' => '1',
                'name' => '产品类型',
                'field_type' => '0',
                'require' => '1',
                'edit_required' => '1',
                'disable_flag' => '0',
                'edit_hide' => '1',
                'default' => '',
                'edit_default' => '0',
                'hint' => '',
                'edit_hint' => '1',
                'is_exportable' => '1',
                'is_editable' => '1',
                'is_list' => '0',
                'columns' => 'category_ids',
                'relation_type' => '0',
                'relation_field' => '',
                'relation_field_type' => '0',
                'relation_field_name' => '',
                'export_scenario' => '',
                'export_group' => '7',
                'order' => '20',
                'app_order' => '20',
                'readonly' => '0',
                'ext_info' => '',
            ],
            [
                'id' => 'group_id',
                'type' => '1',
                'group_id' => '1',
                'base' => '1',
                'name' => '产品分组',
                'field_type' => '3',
                'require' => '1',
                'edit_required' => '1',
                'disable_flag' => '0',
                'edit_hide' => '0',
                'default' => '',
                'edit_default' => '1',
                'hint' => '',
                'edit_hint' => '1',
                'is_exportable' => '1',
                'is_editable' => '1',
                'is_list' => '0',
                'columns' => 'group_id',
                'relation_type' => '0',
                'relation_field' => '',
                'relation_field_type' => '0',
                'relation_field_name' => '',
                'export_scenario' => '',
                'export_group' => '7',
                'order' => '19',
                'app_order' => '19',
                'readonly' => '0',
                'ext_info' => '',
            ],
//            [
//                'id' => 'info_json',
//                'type' => '1',
//                'group_id' => '1',
//                'base' => '1',
//                'name' => '产品属性',
//                'field_type' => '0',
//                'require' => '0',
//                'edit_required' => '0',
//                'disable_flag' => '0',
//                'edit_hide' => '0',
//                'default' => '',
//                'edit_default' => '0',
//                'hint' => '',
//                'edit_hint' => '1',
//                'is_exportable' => '1',
//                'is_editable' => '0',
//                'is_list' => '0',
//                'columns' => 'info_json',
//                'relation_type' => '0',
//                'relation_field' => '',
//                'relation_field_type' => '0',
//                'relation_field_name' => '',
//                'export_scenario' => '',
//                'export_group' => '7',
//                'order' => '3',
//                'app_order' => '3',
//                'readonly' => '0',
//                'ext_info' => '',
//            ],
            // 产品描述
            [
                'id' => 'images',
                'type' => '1',
                'group_id' => '2',
                'base' => '1',
                'name' => '产品图片',
                'field_type' => '6',
                'require' => '1',
                'edit_required' => '1',
                'disable_flag' => '0',
                'edit_hide' => '1',
                'default' => '',
                'edit_default' => '0',
                'hint' => '',
                'edit_hint' => '1',
                'is_exportable' => '1',
                'is_editable' => '1',
                'is_list' => '0',
                'columns' => 'images',
                'relation_type' => '0',
                'relation_field' => '',
                'relation_field_type' => '0',
                'relation_field_name' => '',
                'export_scenario' => '',
                'export_group' => '7',
                'order' => '17',
                'app_order' => '17',
                'readonly' => '0',
                'ext_info' => '',
            ],
            [
                'id' => 'description',
                'type' => '1',
                'group_id' => '2',
                'base' => '1',
                'name' => '产品详细描述',
                'field_type' => '2',
                'require' => '0',
                'edit_required' => '1',
                'disable_flag' => '0',
                'edit_hide' => '1',
                'default' => '',
                'edit_default' => '0',
                'hint' => '',
                'edit_hint' => '1',
                'is_exportable' => '1',
                'is_editable' => '1',
                'is_list' => '0',
                'columns' => 'description',
                'relation_type' => '0',
                'relation_field' => '',
                'relation_field_type' => '0',
                'relation_field_name' => '',
                'export_scenario' => '',
                'export_group' => '0',
                'order' => '16',
                'app_order' => '16',
                'readonly' => '0',
                'ext_info' => '',
            ],
            // 价格信息
            [
                'id' => 'unit',
                'type' => '1',
                'group_id' => '3',
                'base' => '1',
                'name' => '计量单位',
                'field_type' => '3',
                'require' => '0',
                'edit_required' => '1',
                'disable_flag' => '0',
                'edit_hide' => '1',
                'default' => '',
                'edit_default' => '0',
                'hint' => '',
                'edit_hint' => '1',
                'is_exportable' => '1',
                'is_editable' => '1',
                'is_list' => '0',
                'columns' => 'unit',
                'relation_type' => '0',
                'relation_field' => '',
                'relation_field_type' => '0',
                'relation_field_name' => '',
                'export_scenario' => '',
                'export_group' => '0',
                'order' => '15',
                'app_order' => '15',
                'readonly' => '0',
                'ext_info' => '',
            ],
            [
                'id' => 'fob',
                'type' => '1',
                'group_id' => '3',
                'base' => '1',
                'name' => '离岸价',
                'field_type' => '0',
                'require' => '0',
                'edit_required' => '1',
                'disable_flag' => '0',
                'edit_hide' => '1',
                'default' => '',
                'edit_default' => '0',
                'hint' => '',
                'edit_hint' => '1',
                'is_exportable' => '1',
                'is_editable' => '1',
                'is_list' => '0',
                'columns' => 'price_currency,price_min,price_max',
                'relation_type' => '0',
                'relation_field' => '',
                'relation_field_type' => '0',
                'relation_field_name' => '',
                'export_scenario' => '',
                'export_group' => '0',
                'order' => '14',
                'app_order' => '14',
                'readonly' => '0',
                'ext_info' => '',
            ],
            [
                'id' => 'minimum_order_quantity',
                'type' => '1',
                'group_id' => '3',
                'base' => '1',
                'name' => '最小起订量',
                'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                'require' => '0',
                'edit_required' => '1',
                'disable_flag' => '0',
                'edit_hide' => '1',
                'default' => '',
                'edit_default' => '0',
                'hint' => '',
                'edit_hint' => '1',
                'is_exportable' => '1',
                'is_editable' => '1',
                'is_list' => '0',
                'columns' => 'quantity',
                'relation_type' => '0',
                'relation_field' => '',
                'relation_field_type' => '0',
                'relation_field_name' => '',
                'export_scenario' => '',
                'export_group' => '0',
                'order' => '13',
                'app_order' => '13',
                'readonly' => '0',
                'ext_info' => '',
            ],
            [
                'id' => 'payment',
                'type' => '1',
                'group_id' => '3',
                'base' => '1',
                'name' => '支付方式',
                'field_type' => '3',
                'require' => '0',
                'edit_required' => '1',
                'disable_flag' => '0',
                'edit_hide' => '1',
                'default' => '',
                'edit_default' => '0',
                'hint' => '',
                'edit_hint' => '1',
                'is_exportable' => '1',
                'is_editable' => '1',
                'is_list' => '0',
                'columns' => 'payment',
                'relation_type' => '0',
                'relation_field' => '',
                'relation_field_type' => '0',
                'relation_field_name' => '',
                'export_scenario' => '',
                'export_group' => '0',
                'order' => '12',
                'app_order' => '12',
                'readonly' => '0',
                'ext_info' => '',
            ],
            // 物流信息
            [
                'id' => 'package_remark',
                'type' => '1',
                'group_id' => '4',
                'base' => '1',
                'name' => '常规包装',
                'field_type' => '2',
                'require' => '0',
                'edit_required' => '1',
                'disable_flag' => '0',
                'edit_hide' => '1',
                'default' => '',
                'edit_default' => '0',
                'hint' => '',
                'edit_hint' => '1',
                'is_exportable' => '1',
                'is_editable' => '1',
                'is_list' => '0',
                'columns' => 'package_remark',
                'relation_type' => '0',
                'relation_field' => '',
                'relation_field_type' => '0',
                'relation_field_name' => '',
                'export_scenario' => '',
                'export_group' => '7',
                'order' => '11',
                'app_order' => '11',
                'readonly' => '0',
                'ext_info' => '',
            ],
            [
                'id' => 'shipping_port',
                'type' => '1',
                'group_id' => '4',
                'base' => '1',
                'name' => '海运港口',
                'field_type' => '2',
                'require' => '0',
                'edit_required' => '1',
                'disable_flag' => '0',
                'edit_hide' => '1',
                'default' => '',
                'edit_default' => '0',
                'hint' => '',
                'edit_hint' => '1',
                'is_exportable' => '1',
                'is_editable' => '1',
                'is_list' => '0',
                'columns' => 'shipping_port',
                'relation_type' => '0',
                'relation_field' => '',
                'relation_field_type' => '0',
                'relation_field_name' => '',
                'export_scenario' => '',
                'export_group' => '7',
                'order' => '10',
                'app_order' => '10',
                'readonly' => '0',
                'ext_info' => '',
            ],
            [
                'id' => 'supply_ability',
                'type' => '1',
                'group_id' => '4',
                'base' => '1',
                'name' => '供货能力',
                'field_type' => '2',
                'require' => '0',
                'edit_required' => '1',
                'disable_flag' => '0',
                'edit_hide' => '1',
                'default' => '',
                'edit_default' => '0',
                'hint' => '',
                'edit_hint' => '1',
                'is_exportable' => '1',
                'is_editable' => '1',
                'is_list' => '0',
                'columns' => 'supply_ability',
                'relation_type' => '0',
                'relation_field' => '',
                'relation_field_type' => '0',
                'relation_field_name' => '',
                'export_scenario' => '',
                'export_group' => '7',
                'order' => '9',
                'app_order' => '9',
                'readonly' => '0',
                'ext_info' => '',
            ],
        ];
        $this->syncSystemFields(Constants::TYPE_CMS_PRODUCT, $settings);
    }

    /**
     * @mkt-1.3
     */
    public function actionInitMarketingProductVideoFile()
    {
        $settings = [
            [
                'id' => 'video_info',
                'type' => '1',
                'group_id' => '2',
                'base' => '1',
                'name' => '主图视频',
                'field_type' => '0',
                'require' => '0',
                'edit_required' => '1',
                'disable_flag' => '0',
                'edit_hide' => '1',
                'default' => '',
                'edit_default' => '0',
                'hint' => '',
                'edit_hint' => '1',
                'is_exportable' => '1',
                'is_editable' => '1',
                'is_list' => '0',
                'columns' => 'video_info',
                'relation_type' => '0',
                'relation_field' => '',
                'relation_field_type' => '0',
                'relation_field_name' => '',
                'export_scenario' => '',
                'export_group' => '0',
                'order' => '16',
                'app_order' => '16',
                'readonly' => '0',
                'ext_info' => '',
            ],
            [
                'id' => 'faq',
                'type' => '1',
                'group_id' => '5',
                'base' => '1',
                'name' => '产品问答',
                'field_type' => '2',
                'require' => '0',
                'edit_required' => '1',
                'disable_flag' => '0',
                'edit_hide' => '1',
                'default' => '',
                'edit_default' => '0',
                'hint' => '',
                'edit_hint' => '1',
                'is_exportable' => '1',
                'is_editable' => '1',
                'is_list' => '0',
                'columns' => 'description',
                'relation_type' => '0',
                'relation_field' => '',
                'relation_field_type' => '0',
                'relation_field_name' => '',
                'export_scenario' => '',
                'export_group' => '0',
                'order' => '16',
                'app_order' => '16',
                'readonly' => '0',
                'ext_info' => '',
            ]

        ];
        $this->syncSystemFields(Constants::TYPE_CMS_PRODUCT, $settings);
    }


    public function actionFixOrderGroup($clientId, $grey = 1, $expFlag = 0)
    {
        if ($clientId) {
            $clientIds = [$clientId];
        } elseif ($grey) {
            $clientIds = \common\library\GreyEnvHelper::getGreyClientIds();
        } elseif ($expFlag) {
            $clientIds = \common\library\account\Client::getExpClientIds();
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }
        $num = 1;
        $clientCount = count($clientIds);
        foreach ($clientIds as $clientId) {
            echo "$clientId $num/$clientCount \n";
            $num++;

            $db = ProjectActiveRecord::getDbByClientId($clientId);
            if (!$db) {
                self::info("db not exists! client_id: $clientId");
                continue;
            }

            $type = Constants::TYPE_ORDER;
            $sql = "UPDATE `tbl_custom_field` SET `is_editable`=1
WHERE `client_id`={$clientId} and `type`={$type} and `id`='country';";
            ProjectActiveRecord::getDbByClientId($clientId)->createCommand($sql)->execute();

            $sql = "update tbl_field_export_setting set group_id=1
 where export_type=2 and
 id IN ('country','amount_rmb','amount_usd','product_total_amount_usd','product_total_amount_rmb','cash_collection_info.collect_amount_rmb','cash_collection_info.collect_amount_usd','cash_collection_info.not_collect_amount_usd','cash_collection_info.not_collect_amount_rmb')
 and client_id={$clientId};";
            ProjectActiveRecord::getDbByClientId($clientId)->createCommand($sql)->execute();
        }
    }

    public function actionInitOrderField()
    {
        $settings = CustomerOptionService::getOrderSystemField();
        $this->syncSystemFields(Constants::TYPE_ORDER, $settings);
    }

    public function actionInitCustomerField()
    {
        $settings = CustomerOptionService::getCustomerSystemField();
        $this->syncSystemFields(Constants::TYPE_COMPANY, $settings);
    }

    /**
     * @mkt1.3
     * @param int $clientId
     * @param null $lastNumber
     */
    public function actionAddFieldMarketing($clientId = 3, $lastNumber = null) {
        $system_fields = [
            [
                'id' => 'video_info',
                'type' => '101',
                'group_id' => '2',
                'base' => '1',
                'name' => '主图视频',
                'field_type' => '0',
                'require' => '0',
                'edit_required' => '1',
                'disable_flag' => '0',
                'edit_hide' => '1',
                'default' => '',
                'edit_default' => '0',
                'hint' => '',
                'edit_hint' => '1',
                'is_exportable' => '1',
                'is_editable' => '1',
                'is_list' => '0',
                'columns' => 'video_info',
                'relation_type' => '0',
                'relation_field' => '',
                'relation_field_type' => '0',
                'relation_field_name' => '',
                'export_scenario' => '',
                'export_group' => '0',
                'order' => '17',
                'app_order' => '17',
                'readonly' => '0',
                'ext_info' => '',
                'create_time' => date('Y-m-d H:i:s'),
                'update_time' => date('Y-m-d H:i:s')
            ],
            [
                'id' => 'faq',
                'type' => '101',
                'group_id' => '5',
                'base' => '1',
                'name' => '产品问答',
                'field_type' => '2',
                'require' => '0',
                'edit_required' => '1',
                'disable_flag' => '0',
                'edit_hide' => '1',
                'default' => '',
                'edit_default' => '0',
                'hint' => '',
                'edit_hint' => '1',
                'is_exportable' => '1',
                'is_editable' => '1',
                'is_list' => '0',
                'columns' => 'faq',
                'relation_type' => '0',
                'relation_field' => '',
                'relation_field_type' => '0',
                'relation_field_name' => '',
                'export_scenario' => '',
                'export_group' => '0',
                'order' => '15',
                'app_order' => '15',
                'readonly' => '0',
                'ext_info' => '',
                'create_time' => date('Y-m-d H:i:s'),
                'update_time' => date('Y-m-d H:i:s')
            ],
        ];

        if (empty($clientId)) {
            //获取所有client 遍历
            $clients = $this->getClientList(0, false, null, null, 0, 0, $lastNumber);
            $count = 0;
            echo '共需处理：', count($clients), PHP_EOL;
            foreach ($clients as $client) {
                echo '开始处理：', $client->client_id . ' ', PHP_EOL;
                //同步规则：system_field 表中存在 ，目标表中不存在则插入
                try {
                    $this->syncSystem2Customer($system_fields, $client->client_id);
                    $this->syncSystem2FieldGroup($system_fields, $client->client_id);
                    $count++;
                } catch (Throwable $e) {
                    LogUtil::error('同步字段出错， client_id: ' . $client->client_id);
                    LogUtil::error($e->getMessage());
                }
            }
            echo '成功处理：', $count, PHP_EOL;
        } else {
            $this->syncSystem2Customer($system_fields, $clientId);
            $this->syncSystem2FieldGroup($system_fields, $clientId);
            echo '成功处理client：', $clientId, PHP_EOL;
        }
    }


    public function actionInitScheduleSystemFields()
    {
        $scheduleSettings = [
            [
                'id'            => 'title',
                'name'          => '日程-主题',
                'require'       => 0,
                'field_type'    => CustomFieldService::FIELD_TYPE_TEXT,
                'edit_required' => 0,
                'edit_hide'     => 0,
                'edit_default'  => 0,
                'edit_hint'     => 0,
                'is_editable'   => 0,
                'ext_info'      => '',
                'order'         => 1300,
                'app_order'     => 1300
            ],
            [
                'id'            => 'start_time',
                'name'          => '日程-开始日期和时间',
                'require'       => 0,
                'field_type'    => CustomFieldService::FIELD_TYPE_DATETIME,
                'edit_required' => 0,
                'edit_hide'     => 0,
                'edit_default'  => 0,
                'edit_hint'     => 0,
                'is_editable'   => 1,
                'ext_info'      => '',
                'order'         => 1200,
                'app_order'     => 1200
            ],
            [
                'id' => 'end_time',
                'name' => '日程-结束时间和日程',
                'require' => 0,
                'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
                'edit_required' => 0,
                'edit_hide' => 0,
                'edit_default' => 0,
                'edit_hint' => 0,
                'is_editable' => 1,
                'ext_info' => '',
                'order' => 1100,
                'app_order' => 1100
            ],
            [
                'id' => 'refer_type',
                'name' => '日程-对象类型',
                'require' => 0,
                'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
                'edit_required' => 0,
                'edit_hide' => 0,
                'edit_default' => 0,
                'edit_hint' => 0,
                'is_editable' => 0,
                'ext_info' => '',
                'order' => 1000,
                'app_order' => 1000
            ],
            [
                'id' => 'refer_info',
                'name' => '日程-关联对象',
                'require' => 0,
                'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
                'edit_required' => 0,
                'edit_hide' => 0,
                'edit_default' => 0,
                'edit_hint' => 0,
                'is_editable' => 0,
                'ext_info' => '',
                'order' => 900,
                'app_order' => 900
            ],
            [

                'id' => 'relation_mail_info',
                'name' => '日程-关联邮件',
                'require' => 0,
                'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
                'edit_required' => 0,
                'edit_hide' => 0,
                'edit_default' => 0,
                'edit_hint' => 0,
                'is_editable' => 0,
                'ext_info' => '',
                'order' => 800,
                'app_order' => 800
            ],
            [
                'id' => 'full_day_flag',
                'name' => '日程-全天',
                'require' => 0,
                'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
                'edit_required' => 0,
                'edit_hide' => 0,
                'edit_default' => 0,
                'edit_hint' => 0,
                'is_editable' => 0,
                'ext_info' => '',
                'order' => 700,
                'app_order' => 700
            ],
            [
                'id' => 'repeat_id',
                'name' => '日程-重复',
                'require' => 0,
                'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
                'edit_required' => 0,
                'edit_hide' => 0,
                'edit_default' => 0,
                'edit_hint' => 0,
                'is_editable' => 0,
                'ext_info' => '',
                'order' => 600,
                'app_order' => 600
            ],
            [
                'id' => 'remind_time',
                'name' => '日程-提醒时间',
                'require' => 0,
                'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
                'edit_required' => 0,
                'edit_hide' => 0,
                'edit_default' => 0,
                'edit_hint' => 0,
                'is_editable' => 0,
                'ext_info' => '',
                'order' => 500,
                'app_order' => 500
            ],
            [
                'id' => 'remark',
                'name' => '日程-备注',
                'require' => 0,
                'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
                'edit_required' => 0,
                'edit_hide' => 0,
                'edit_default' => 0,
                'edit_hint' => 0,
                'is_editable' => 0,
                'ext_info' => '',
                'order' => 400,
                'app_order' => 400
            ],
            [
                'id' => 'participant_user_list',
                'name' => '日程-参与人',
                'require' => 0,
                'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
                'edit_required' => 0,
                'edit_hide' => 0,
                'edit_default' => 0,
                'edit_hint' => 0,
                'is_editable' => 0,
                'ext_info' => '',
                'order' => 300,
                'app_order' => 300
            ],
            [
                'id' => 'create_user',
                'name' => '日程-创建人',
                'require' => 0,
                'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
                'edit_required' => 0,
                'edit_hide' => 0,
                'edit_default' => 0,
                'edit_hint' => 0,
                'is_editable' => 0,
                'ext_info' => '',
                'order' => 200,
                'app_order' => 200
            ],
            [
                'id' => 'create_time',
                'name' => '日程-创建时间',
                'require' => 0,
                'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
                'edit_required' => 0,
                'edit_hide' => 0,
                'edit_default' => 0,
                'edit_hint' => 0,
                'is_editable' => 0,
                'ext_info' => '',
                'order' => 100,
                'app_order' => 100
            ]

        ];

        $this->syncSystemFields(Constants::TYPE_SCHEDULE, $scheduleSettings);
    }


    public function updateCustomFieldByMap($map, $clientId, $grey =1, $greyNum = null)
    {
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey && !is_null($greyNum)) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $this->actionFixSystemFieldType($map);
            return;
        }
        foreach ($clientIds as $clientId) {
            $db = ProjectActiveRecord::getDbByClientId($clientId);
            if (!$db) {
                echo "skip for client {$clientId} \n";
                continue;
            }
            echo "update client {$clientId}" . PHP_EOL;
            foreach ($map as $item) {
                $item['condition']['client_id'] = $clientId;
                $criteria = new CDbCriteria();
                foreach ($item['condition'] as $key => $condition) {
                    $criteria->addCondition("$key=:$key");
                }
                $criteria->params += $item['condition'];
                $result = $db->getCommandBuilder()->createUpdateCommand('tbl_custom_field', $item['update'],
                    $criteria)->execute();
                echo 'result: ' . $result . ', update tbl_custom_field: ' . json_encode($item) . PHP_EOL;
            }
        }
    }


    public function actionChangeCollectionType()
    {
        $fix_map = [
            ['condition' => ['type' => Constants::TYPE_CASH_COLLECTION, 'id' => 'type'], 'update' => ['field_type' => CustomFieldService::FIELD_TYPE_SELECT]],
        ];
        $this->actionFixSystemFieldType($fix_map);
    }

    public function actionSortOneField($clientId, $type, $fixField, $moveField, $grey =1, $greyNum = null, $after = 1, $orderField = 'order', $dryRun = 1)
    {
        $all = false;
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey && !is_null($greyNum)) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $all = true;
            $clientIds = array_column($this->getClientList(), 'client_id');
        }
        if ($all) {
            $this->actionSortOneFieldForClient(0, $type, $fixField, $moveField, $after, $orderField, $dryRun);
        }
        foreach ($clientIds as $clientId) {
            $this->actionSortOneFieldForClient($clientId, $type, $fixField, $moveField, $after, $orderField, $dryRun);
        }

    }

    public function actionSortOneFieldForClient($clientId, $type, $fixField, $moveField, $after = 1, $orderField = 'order', $dryRun = 1)
    {
        echo "start client: $clientId \n";
        $clientSql = '';
        if (!$clientId) {
            $table = 'tbl_system_field';
            $db = Yii::app()->db;
        } else {
            $table = 'tbl_custom_field';
            $db = ProjectActiveRecord::getDbByClientId($clientId);
            $clientSql = "client_id = $clientId and ";
        }

        $sql = "select `id`, `{$orderField}` from $table where $clientSql `type` = $type and base=1 order by `{$orderField}` desc";
        $moveUp = function (array $fieldIds) use ($db, $table, $orderField, $dryRun, $clientSql) {
            $fieldSql = "('" . implode("','", $fieldIds) . "')";
            echo "↑ $fieldSql \n";
            $sql = "update $table set `$orderField`=`$orderField`+1 where $clientSql id in $fieldSql";
            if (!$dryRun) {
                $db->createCommand($sql)->execute();
            }
        };
        $updateOrder = function ($fieldId, $order) use ($db, $table, $orderField, $dryRun, $clientSql) {
            $sql = "update $table set `$orderField`=$order where $clientSql id = '{$fieldId}'";
            echo "更新 {$fieldId} $orderField as $order \n";
            if (!$dryRun) {
                $db->createCommand($sql)->execute();
            }
        };

        $fieldList = array_column($db->createCommand($sql)->queryAll(true), $orderField, 'id');
        if (!isset($fieldList[$fixField])) {
            echo "{$fixField}字段不存在 \n";
            return false;
        }
        if (!isset($fieldList[$moveField])) {
            echo "{$moveField}字段不存在 \n";
            return false;
        }
        if ($fieldList[$fixField] > $fieldList[$moveField]) {
            $bigOrder = $fieldList[$fixField];
            $smallOrder = $fieldList[$moveField];
        } else {
            $bigOrder = $fieldList[$moveField];
            $smallOrder = $fieldList[$fixField];
        }
        $beforeList = [];
        $betweenList = [];
        foreach ($fieldList as $fieldId => $order) {
            if ($order > $bigOrder) {
                $beforeList[] = $fieldId;
                continue;
            } else {
                if ($order < $bigOrder && $order > $smallOrder) {
                    $betweenList[] = $fieldId;
                    continue;
                }  else {
                    continue;
                }
            }
        }

        if ($after) {
            if ($fieldList[$fixField] > $fieldList[$moveField]) {
                if (empty($betweenList)) {
                    echo "跳过 $clientId \n";
                } else {
                    if ($fieldList[current($betweenList)] < $fieldList[$fixField] - 1) {
                        $updateOrder($moveField, $fieldList[$fixField] - 1);
                    } else {
                        $updateOrder($moveField, $fieldList[$fixField]);
                        array_push($beforeList, $fixField);
                        $moveUp($beforeList);
                    }
                }
            } else {
                $updateOrder($moveField, $fieldList[$fixField]);
                array_push($betweenList, $fixField);
                $moveUp($betweenList);
            }
        } else {
            if ($fieldList[$fixField] > $fieldList[$moveField]) {
                $updateOrder($moveField, $fieldList[$fixField] + 1);
                if (!empty($beforeList) && $fieldList[end($beforeList)] <= $fieldList[$fixField] + 1) {
                    $moveUp($beforeList);
                }
            } else {
                if (empty($betweenList)) {
                    echo "跳过 $clientId \n";
                } else {
                    $updateOrder($moveField, $fieldList[$fixField] + 1);
                    if ($fieldList[end($betweenList)] <= $fieldList[$fixField] + 1) {
                        $moveUp($betweenList);
                    }
                }
            }
        }
    }

    public function actionFixMarketingProductExport()
    {
//        $map = [
//            ['condition' => ['type' => Constants::TYPE_CMS_PRODUCT], 'update' => ['export_scenario' => '']],
//        ];
//
//        $this->actionFixSystemFieldType($map);

        $client_dbs = \common\library\account\service\DbService::getMysqlList();
        foreach ($client_dbs as $client_db) {
            if (substr($client_db['name'],0,3)!='v5_') {
                continue;
            }
            echo "update db {$client_db['name']}" . PHP_EOL;
            $db = ProjectActiveRecord::getDbByDbSetId($client_db['set_id']);
            $sql = "delete from tbl_field_export_setting where `type` = " . Constants::TYPE_CMS_PRODUCT;
            $result = $db->getCommandBuilder()->createSqlCommand($sql)->execute();
            echo 'result: ' . $result . ', update tbl_field_export_setting: ' . json_encode($result) . PHP_EOL;
        }
    }

    public function actionUpgradeSystemOrderField()
    {
        $db = \Yii::app()->db;

        // 1.(装运期限说明、收汇方式)字段名变更为（交货期、付款方式）
        $sql = "UPDATE tbl_system_field SET name='交货期' WHERE type=2 AND id='shipment_deadline_remark' LIMIT 1";
        $db->createCommand($sql)->execute();

        $sql = "UPDATE tbl_system_field SET name='付款方式' WHERE type=2 AND id='receive_remittance_way' LIMIT 1";
        $db->createCommand($sql)->execute();

        $sql = "UPDATE tbl_system_field SET name='订单日期' AND type=2 AND id='account_date' LIMIT 1";
        $db->createCommand($sql)->execute();

        // 业绩归属部门和当前处理人，产品说要设置为必填且启用，然后用户不能编辑
        $sql = "UPDATE tbl_system_field SET `require`=1, edit_hide=0, edit_required=0 AND type=2 AND id='departments' LIMIT 1";
        $db->createCommand($sql)->execute();

        // 业绩归属部门和当前处理人，产品说要设置为必填且启用，然后用户不能编辑
        $sql = "UPDATE tbl_system_field SET `require`=1, edit_hide=0, edit_required=0 AND type=2 AND id='handler' LIMIT 1";
        $db->createCommand($sql)->execute();


        $sql = "SELECT * FROM tbl_system_field WHERE type=2";
        $result = $db->createCommand($sql)->queryAll(true);

        foreach ($result as $item) {
            if (in_array($item['group_id'], [3, 4, 5])) {
                // 3.隐藏贸易信息相关组的（系统、自定义）字段 disable_flag=1，只保留（联系人电话、、客户地址、价格条款、付款方式、交货期、银行信息）启用
                if (in_array($item['id'], ['customer_phone', 'company_address', 'price_contract', 'receive_remittance_way', 'shipment_deadline_remark', 'bank_info'])) {
                    $sql = "UPDATE tbl_system_field SET `disable_flag`=0, `group_id`='1' WHERE type=2 AND id='{$item['id']}' LIMIT 1";
                } elseif (in_array($item['id'], ['cost_name', 'percent_of_total_amount', 'cost', 'addition_cost_amount', 'amount', 'product_total_amount'])) {
                    $sql = ""; // 这5个字段保留在贸易信息组
                } else {
                    $sql = "UPDATE tbl_system_field SET `disable_flag`=1, `group_id`='1' WHERE type=2 AND id='{$item['id']}' LIMIT 1";
                }

                if (!empty($sql)) {
                    $db->createCommand($sql)->execute();
                }
            }
        }
    }

    /**
     * 升级订单字段表 Smart 1.8
     *
     *
     *
     * @param int $clientId
     * @throws CDbException
     * @throws ProcessException
     */
    public function actionUpgradeOrderField($clientId = 0)
    {
        $clientIds = $this->getClientList($clientId, true);
        $type = 2;

        $tradeFieldData = [
            'receive_remittance_remark', //收汇方式说明
            'price_contract_remark', //价格条款说明
            'insurance_remark', // 保险说明
            'order_contract', // 订单条款
            'company_phone', // 客户电话
            'company_fax', // 客户传真
            'customer_email', // 联系人邮箱
            'customer_address', // 联系人地址
            'transport_mode', // 运输方式
            'shipment_deadline', // 装运期限
            'shipment_port', // 装运港口
            'target_port', // 目的港口
            'more_or_less', // 溢短装,
            'package_remark', // 包装说明
            'marked', // 唛头
        ];

        $productFieldData = [
            'cost_with_tax', // 含税成本价
            'gross_profit_margin', // 毛利润率
            'unit', // 计量单位
            'package_unit', // 包装单位
            'count_per_package', // 每包装产品数
            'package_volume', // 包装体积
            'package_gross_weight', // 包装毛重
            'package_volume_subtotal', // 包装体积小计
            'package_gross_weight_subtotal', // 包装毛重小计
        ];

        $fieldData = [
            'handler',
            'order_no',
            'name',
            'currency',
            'account_date',
            'company_id',
            'customer_id',
            'customer_phone',
            'company_address',
            'price_contract',
            'receive_remittance_way',
            'shipment_deadline_remark',
            'bank_info',
            'remark',
        ];

        foreach ($clientIds as $elem) {
            $db = \ProjectActiveRecord::getDbByClientId($elem->client_id);
            if (empty($db)) {
                continue;
            }

            // 0.贸易信息组和产品组部份系统字段隐藏
            $sql = "UPDATE tbl_custom_field SET disable_flag=1 WHERE client_id={$elem->client_id} AND type={$type} AND id IN ('". implode("','", $tradeFieldData) ."')";
            $db->createCommand($sql)->execute();

            $sql = "UPDATE tbl_custom_field SET disable_flag=1 WHERE client_id={$elem->client_id} AND type={$type} AND id IN ('". implode("','", $productFieldData) ."')";
            $db->createCommand($sql)->execute();

            // 1.(装运期限说明、收汇方式)字段名变更为（交货期、付款方式）
            $sql = "UPDATE tbl_custom_field SET name='交货期' WHERE client_id={$elem->client_id} AND type=2 AND id='shipment_deadline_remark' LIMIT 1";
            $db->createCommand($sql)->execute();

            $sql = "UPDATE tbl_custom_field SET name='付款方式', ext_info='[]' WHERE client_id={$elem->client_id} AND type=2 AND id='receive_remittance_way' LIMIT 1";
            $db->createCommand($sql)->execute();

            $sql = "UPDATE tbl_custom_field SET ext_info='[]' WHERE client_id={$elem->client_id} AND type=2 AND id='price_contract' LIMIT 1";
            $db->createCommand($sql)->execute();

            // 业绩归属部门和当前处理人，产品说要设置为必填且启用，然后用户不能编辑
            $sql = "UPDATE tbl_custom_field SET `require`=1, disable_flag=0, edit_hide=0, edit_required=0 WHERE client_id={$elem->client_id} AND type=2 AND id='departments' LIMIT 1";
            $db->createCommand($sql)->execute();

            // 业绩归属部门和当前处理人，产品说要设置为必填且启用，然后用户不能编辑
            $sql = "UPDATE tbl_custom_field SET `require`=1, disable_flag=0, edit_hide=0, edit_required=0 WHERE client_id={$elem->client_id} AND type=2 AND id='handler' LIMIT 1";
            $db->createCommand($sql)->execute();

            $sql = "SELECT a.* FROM tbl_custom_field as a INNER JOIN tbl_field_group as b ON a.client_id=b.client_id AND a.type=b.type AND a.id=b.id WHERE a.client_id={$elem->client_id} AND a.enable_flag=1 AND a.type=2 AND b.group_id IN (3, 4, 5)";
            $result = $db->createCommand($sql)->queryAll(true);
            foreach ($result as $item) {
                $sql = null;
                // 2.隐藏贸易信息相关组的（系统）字段 disable_flag=1，只保留（联系人电话、客户地址、价格条款、付款方式、交货期、银行信息）或者已设置过必填的字段启用
                if (in_array($item['id'], ['customer_phone', 'company_address', 'price_contract', 'receive_remittance_way', 'shipment_deadline_remark', 'bank_info']) || $item['require'] == 1) {
                    $sql = "UPDATE tbl_custom_field SET disable_flag=0 WHERE client_id={$elem->client_id} AND type='{$item['type']}' AND id='{$item['id']}' LIMIT 1";
                } elseif (in_array($item['id'], ['cost_name', 'percent_of_total_amount', 'cost', 'addition_cost_amount', 'amount', 'product_total_amount'])) {
                    $sql = ""; // 这5个字段特殊处理，还保留在贸易信息
                } elseif ($item['base'] == 1) {
                    $sql = "UPDATE tbl_custom_field SET disable_flag=1 WHERE client_id={$elem->client_id} AND type='{$item['type']}' AND id='{$item['id']}' LIMIT 1";
                }

                if (!empty($sql)) {
                    $db->createCommand($sql)->execute();
                }

                // 3.所有原贸易信息相关组的（系统、自定义）字段移动到基础信息组 group_id=1
                if (!in_array($item['id'], ['cost_name', 'percent_of_total_amount', 'cost', 'addition_cost_amount', 'amount', 'product_total_amount'])) {
                    $sql = "UPDATE tbl_field_group SET group_id=1 WHERE client_id={$elem->client_id} AND type='{$item['type']}' AND id='{$item['id']}' LIMIT 1";
                }

                if (!empty($sql))
                    $db->createCommand($sql)->execute();
            }

            // 变更订单字段的排序
            $sql = "SELECT count(1) as total FROM tbl_custom_field as a INNER JOIN tbl_field_group as b ON a.client_id=b.client_id AND a.type=b.type AND a.id=b.id WHERE a.client_id={$elem->client_id} AND a.type={$type} AND a.enable_flag=1 AND b.group_id=1";
            $fieldMax = $db->createCommand($sql)->queryScalar();

            foreach ($fieldData as $field) {
                $pc_order = $fieldMax;
                $sql = "UPDATE tbl_custom_field SET `order`={$pc_order} WHERE client_id={$elem->client_id} AND type={$type} AND id='{$field}' LIMIT 1";
                $db->createCommand($sql)->execute();
                $fieldMax--;
            }

            $sql = "SELECT a.* FROM tbl_custom_field as a INNER JOIN tbl_field_group as b ON a.client_id=b.client_id AND a.type=b.type AND a.id=b.id WHERE a.client_id={$elem->client_id} AND a.type={$type} AND a.enable_flag=1 AND b.group_id=1 AND a.base=1 ORDER BY a.create_time ASC";
            $result = $db->createCommand($sql)->queryAll(true);
            foreach ($result as $item) {
                if (in_array($item['id'], $fieldData)) {
                    continue;
                }

                $pc_order = $fieldMax;
                $sql = "UPDATE tbl_custom_field SET `order`={$pc_order} WHERE client_id={$elem->client_id} AND type={$type} AND id='{$item['id']}' LIMIT 1";
                $db->createCommand($sql)->execute();
                $fieldMax--;
            }

            $sql = "SELECT a.* FROM tbl_custom_field as a INNER JOIN tbl_field_group as b ON a.client_id=b.client_id AND a.type=b.type AND a.id=b.id WHERE a.client_id={$elem->client_id} AND a.type={$type} AND a.enable_flag=1 AND b.group_id=1 AND a.base=0 ORDER BY a.create_time ASC";
            $result = $db->createCommand($sql)->queryAll(true);
            foreach ($result as $item) {
                $pc_order = $fieldMax;
                $sql = "UPDATE tbl_custom_field SET `order`={$pc_order} WHERE client_id={$elem->client_id} AND type={$type} AND id='{$item['id']}' LIMIT 1";
                $db->createCommand($sql)->execute();
                $fieldMax--;
            }

            // 结单日期变更为订单日期
            $sql = "UPDATE tbl_custom_field SET name='订单日期' WHERE client_id={$elem->client_id} AND type={$type} AND id='account_date' LIMIT 1";
            $db->createCommand($sql)->execute();

            // 业绩归属部门变更为不可取消启用
            $sql = "UPDATE tbl_custom_field SET edit_hide=0 WHERE client_id={$elem->client_id} AND type={$type} AND id='departments' LIMIT 1";

            (new \common\library\privilege_v3\PrivilegeField($elem->client_id))->flushReferFieldPrivilege($type);
        }
    }

    public function actionShowFormulaColumn()
    {
        foreach ($this->getClientList() as $client) {
            if (in_array($client['client_id'], [7,10,2106,17383,18618,16149]) || ($client['client_id'] % 10) !=4) {
                continue;
            }
            $this->actionShowFormulaColumnByClientId($client['client_id']);
        }
    }

    public function actionShowFormulaColumnByClientId($clientId)
    {
        $db = ProjectActiveRecord::getDbByClientId($clientId);
        if (! $db) {
            return;
        }
        $db->createCommand("update tbl_custom_field set edit_required=0,edit_hide=0,readonly=0 where type=2  and field_type in (11, 12) and client_id={$clientId} ")->execute();
        echo "client:{$clientId} success\n";
    }

    /**
     *
     * marketing 1.4
     */
    public function actionHideMarketingProductPayment()
    {
        /** @var CDbConnection $admin_db */
        $admin_db = Yii::app()->db;
        $admin_db->createCommand("delete from tbl_system_field where `type` = 101 and id = 'payment'")->execute();

        $fix_map = [
            ['condition' => ['type' => Constants::TYPE_CMS_PRODUCT, 'id' => 'payment'], 'update' => ['enable_flag' => 0]],
        ];

        $client_dbs = \common\library\account\service\DbService::getMysqlList();
        foreach ($client_dbs as $client_db) {
            if (substr($client_db['name'],0,3)!='v5_') {
                continue;
            }
            echo "update db {$client_db['name']}" . PHP_EOL;
            $db = ProjectActiveRecord::getDbByDbSetId($client_db['set_id']);
            foreach ($fix_map as $item) {
                $criteria = new CDbCriteria();
                foreach ($item['condition'] as $key => $condition) {
                    $criteria->addCondition("$key=:$key");
                }
                $criteria->params += $item['condition'];
                $result = $db->getCommandBuilder()->createUpdateCommand('tbl_custom_field', $item['update'],
                    $criteria)->execute();
                echo 'result: ' . $result . ', update tbl_custom_field: ' . json_encode($item) . PHP_EOL;
            }
        }
    }


    /**
     * @param int $clientId
     * @param null $lastNumber
     */
    public function actionAddFieldForLeadInquiry($clientId, $grey = 0, $greyNum = null)
    {

        $system_fields = [
            [
                'id'                  => 'inquiry_origin',
                'type'                => Constants::TYPE_LEAD,// 公司
                'group_id'            => 1, // 联系信息分组
                'base'                => 1,
                'name'                => '访问来源',
                'field_type'          => CustomFieldService::FIELD_TYPE_SELECT,
                'require'             => 0,
                'edit_required'       => 1,
                'disable_flag'        => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide'           => 1,// 是否可编辑，1可以，0否
                'default'             => '',
                'edit_default'        => 0,
                'hint'                => '',
                'edit_hint'           => 1,
                'is_exportable'       => 0,
                'is_editable'         => 1,
                'is_list'             => 0,
                'columns'             => 'inquiry_origin',
                'relation_type'       => 0,
                'relation_field'      => '',
                'relation_field_type' => 0,
                'relation_field_name' => '',
                'export_scenario'     => '',
                'export_group'        => '',
                'readonly'            => 0,
                'order' => 2,
                'app_order' =>2,
                'create_time'         => date('Y-m-d H:i:s'),
                'update_time'         => date('Y-m-d H:i:s')
            ],
            [
                'id'                  => 'inquiry_country',
                'type'                => Constants::TYPE_LEAD,// 公司
                'group_id'            => 1, // 联系信息分组
                'base'                => 1,
                'name'                => '访客IP所在地',
                'field_type'          => CustomFieldService::FIELD_TYPE_TEXT,
                'require'             => 0,
                'edit_required'       => 1,
                'disable_flag'        => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide'           => 1,// 是否可编辑，1可以，0否
                'default'             => '',
                'edit_default'        => 0,
                'hint'                => '',
                'edit_hint'           => 1,
                'is_exportable'       => 0,
                'is_editable'         => 1,
                'is_list'             => 0,
                'columns'             => 'inquiry_country',
                'relation_type'       => 0,
                'relation_field'      => '',
                'relation_field_type' => 0,
                'relation_field_name' => '',
                'export_scenario'     => '',
                'export_group'        => '',
                'readonly'            => 0,
                'order' => 1,
                'app_order' =>1,
                'create_time'         => date('Y-m-d H:i:s'),
                'update_time'         => date('Y-m-d H:i:s')
            ],
        ];

        $all = false;
        $clientIds = [];
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey) {
            if (!is_null($greyNum)) {
                $clientIds = $this->getGreyClientIds($greyNum);
            }
        } else {
            $all = true;
            $clientIds = array_column($this->getClientList($clientId), 'client_id');
        }


        if ($all) {
            $this->syncSystemFields(Constants::TYPE_LEAD, $system_fields);
        }

        echo '共需处理：', count($clientIds), PHP_EOL;
        $count = 0;
        foreach ($clientIds as $clientId) {
            echo '开始处理：', $clientId . ' ', PHP_EOL;
            //同步规则：system_field 表中存在 ，目标表中不存在则插入
            try {
                $this->syncSystem2Customer($system_fields, $clientId);
                $this->syncSystem2FieldGroup($system_fields, $clientId);
                $count++;
            } catch (Throwable $e) {
                LogUtil::error('同步字段出错， client_id: ' . $clientId);
                LogUtil::error($e->getMessage());
            }
            echo '成功处理：', $count, PHP_EOL;
        }
    }


    /**
     * marketing 1.4.5
     */
    public function actionMarketingProductField($clientId, $grey = 1, $greyNum = null)
    {
        $fix_map = [
            ['condition' => ['type' => Constants::TYPE_CMS_PRODUCT, 'id' => 'description'], 'update' => ['enable_flag' => 0]],
            ['condition' => ['type' => Constants::TYPE_CMS_PRODUCT, 'id' => 'supply_ability'], 'update' => ['enable_flag' => 0]],
            ['condition' => ['type' => Constants::TYPE_CMS_PRODUCT, 'id' => 'shipping_port'], 'update' => ['enable_flag' => 0]],
            ['condition' => ['type' => Constants::TYPE_CMS_PRODUCT, 'id' => 'package_remark'], 'update' => ['enable_flag' => 0]],
            ['condition' => ['type' => Constants::TYPE_CMS_PRODUCT, 'id' => 'faq'], 'update' => ['enable_flag' => 0]],
        ];

        $this->updateCustomFieldByMap($fix_map, $clientId, $grey, $greyNum);

        // 迁移Description字段
        $migrateMap = [
            'description' => [
                'name' => 'Description',
                'group_id' => CustomFieldService::CMS_PRODUCT_GROUP_DETAIL,
                'type' => Constants::TYPE_CMS_PRODUCT,
                'field_type' => CustomFieldService::FIELD_TYPE_RICH_TEXT,
                'enable_flag' => 1,
                'require' => 0,
                'is_editable' => 1,
                'edit_hide' => 1,
                'ext_info' => '',
                'is_list' => 0,
                'base' => CustomFieldService::FIELD_BASE_OF_CUSTOM_ATTRIBUTE,
            ],
        ];
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey && !is_null($greyNum)) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }
        foreach ($clientIds as $clientId) {
            $userId = PrivilegeService::getInstance($clientId)->getAdminUserId();
            if (!$userId) {
                \LogUtil::info("clientId: $clientId skip");
                continue;
            }
            $db = ProjectActiveRecord::getDbByClientId($clientId);
            if (!$db) {
                \LogUtil::info("clientId: $clientId skip");
                continue;
            }
            User::setLoginUserById(PrivilegeService::getInstance($clientId)->getAdminUserId());
            $customFieldService = new CustomFieldService($clientId, Constants::TYPE_CMS_PRODUCT);
            foreach ($migrateMap as $fieldId => $newField) {
                if (!$customFieldService->getRepository()->isNameExist($newField['name'])) {
                    try {
                        $newFieldModel = $customFieldService->insertField($newField);
                    } catch (\Throwable $e) {
                        \LogUtil::info("clientId: $clientId description id fail");
                    }
                    \LogUtil::info("clientId: $clientId description id " . $newFieldModel['id']);
                } else {
                    \LogUtil::info("clientId: $clientId description existed");
                }
            }
        }
    }

    public function actionFixCmsProductExport($clientId = 0, $grey = 1, $greyNum = null, $dryRun = 1)
    {
        $clientIds = [];
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey) {
            if (!is_null($greyNum)) {
                $clientIds = $this->getGreyClientIds($greyNum);
            }
        } else {
            $clientIds = array_column($this->getClientList($clientId), 'client_id');
        }

        foreach ($clientIds as $clientId) {
            $sql = "delete from tbl_field_export_setting where client_id = {$clientId} and type = 101;";
            if (!$dryRun) {
                $db = ProjectActiveRecord::getDbByClientId($clientId);
                if ($db) {
                    $db->createCommand($sql)->execute();
                    echo "finish for $clientId \n";
                }
            } else {
                echo "run $sql \n";
            }
        }
    }

    function actionAddExportField($clientId = 0, $grey = 1, $expFlag = 0,$lastNumber=null)
    {

        $fields =  [
            Constants::TYPE_COMPANY =>
                [
                    'origin',//客户来源
                    'star', //客户星级
                    'group_id', //客户星级
                    'cus_tag', //客户标签
                    'trail_status', //客户状态
                    'country', //国家地区
                    'timezone',//时区
                    'biz_type', //客户类型
                    'scale_id', //规模
                    'tel',//座机
                ],
            Constants::TYPE_CUSTOMER => [
                'email',//邮箱
            ]

        ];

        $map = [];

        $sql = "(type=".Constants::TYPE_COMPANY." and id in( 'origin','star', 'group_id', 'cus_tag','trail_status', 'country', 'timezone','biz_type','scale_id', 'tel'))";
        $sql .= " or (type=".Constants::TYPE_CUSTOMER." and id in('email'))";

        foreach ($fields as $type =>  $field) {
            foreach ($field as $item) {
                if ($type == Constants::TYPE_COMPANY) {
                    $map[] = ['condition' => ['type' => Constants::TYPE_COMPANY, 'id' => $item], 'update' => ['export_group' => \common\library\custom_field\FieldExportService::ORDER_GROUP_COMPANY,'is_exportable' => 1]];
                }

                if ($type == Constants::TYPE_CUSTOMER) {
                    $map[] = ['condition' => ['type' => Constants::TYPE_CUSTOMER, 'id' => $item], 'update' => ['export_group' => \common\library\custom_field\FieldExportService::ORDER_GROUP_CUSTOMER,'is_exportable' => 1]];
                }
            }
        }



        $admin_db = Yii::app()->db;
        echo "update db v4_admin" . PHP_EOL;
        foreach ($map as $item) {
            $criteria = new CDbCriteria();
            foreach ($item['condition'] as $key => $condition) {
                $criteria->addCondition("$key=:$key");
            }
            $criteria->params += $item['condition'];
            if (isset($item['update']['enable_flag']) && $item['update']['enable_flag'] == 0) {
                $result = $admin_db->getCommandBuilder()->createDeleteCommand('tbl_system_field', $criteria)->execute();
            } else {
                $result = $admin_db->getCommandBuilder()->createUpdateCommand('tbl_system_field', $item['update'],
                    $criteria)->execute();
            }
            echo 'result: ' . $result . ', update tbl_system_field: ' . json_encode($item) . PHP_EOL;
        }

        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey) {
            $clientIds = \common\library\GreyEnvHelper::getGreyClientIds();
        } elseif ($expFlag) {
            $clientIds = \common\library\account\Client::getExpClientIds();
        } else {
            $clientIds = array_column($this->getClientList($clientId, false, null, null, 0, 0, $lastNumber), 'client_id');
        }

        $systemFields = [];

        $settings = \common\models\admin\SystemField::model()->findAll($sql);
        foreach ($settings as $setting) {
            $systemFields[] = $setting->getAttributes();
        }


        foreach ($clientIds as $clientId) {
            self::info("开始处理:$clientId");
            $this->syncSystem2ExportSetting($systemFields,$clientId);
            self::info("完成处理:$clientId");
        }

    }

    function actionAddQuotationField($clientId = 0, $grey = 1, $expFlag = 0,$lastNumber=null) {

        $fields = [
            [
                'id' => 'amount_rmb',
                'type' => Constants::TYPE_QUOTATION,
                'group_id' => CustomFieldService::QUOTATION_GROUP_BASIC,
                'base' => '1',
                'name' => '报价单金额（CNY）',
                'field_type' => '5',
                'require' => '0',
                'edit_required' => '0',
                'disable_flag' => '0',
                'edit_hide' => '0',
                'default' => '',
                'edit_default' => '0',
                'hint' => '',
                'edit_hint' => '1',
                'is_exportable' => '1',
                'is_editable' => '0',
                'is_list' => '0',
                'columns' => 'amount_rmb',
                'relation_type' => '0',
                'relation_field' => '',
                'relation_field_type' => '0',
                'relation_field_name' => '',
                'export_scenario' => Constants::TYPE_QUOTATION,
                'export_group' => \common\library\custom_field\FieldExportService::QUOTATION_GROUP_BASIC,
                'order' => '38',
                'app_order' => '38',
                'readonly' => '0',
                'ext_info' => '',
            ],
            [
                'id' => 'amount_usd',
                'type' => Constants::TYPE_QUOTATION,
                'group_id' => CustomFieldService::QUOTATION_GROUP_BASIC,
                'base' => '1',
                'name' => '报价单金额（USD）',
                'field_type' => '5',
                'require' => '0',
                'edit_required' => '0',
                'disable_flag' => '0',
                'edit_hide' => '0',
                'default' => '',
                'edit_default' => '0',
                'hint' => '',
                'edit_hint' => '1',
                'is_exportable' => '1',
                'is_editable' => '0',
                'is_list' => '0',
                'columns' => 'amount_usd',
                'relation_type' => '0',
                'relation_field' => '',
                'relation_field_type' => '0',
                'relation_field_name' => '',
                'export_scenario' => Constants::TYPE_QUOTATION,
                'export_group' => \common\library\custom_field\FieldExportService::QUOTATION_GROUP_BASIC,
                'order' => '37',
                'app_order' => '37',
                'readonly' => '0',
                'ext_info' => '',
            ]
        ];

        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey) {
            $clientIds = \common\library\GreyEnvHelper::getGreyClientIds();
        } elseif ($expFlag) {
            $clientIds = \common\library\account\Client::getExpClientIds();
        } else {
            $clientIds = array_column($this->getClientList($clientId, false, null, null, 0, 0, $lastNumber), 'client_id');
        }

        $this->syncSystemFields(Constants::TYPE_QUOTATION, $fields);

        echo '共需处理：', count($clientIds), PHP_EOL;
        $count = 0;
        foreach ($clientIds as $clientId) {
            echo '开始处理：', $clientId . ' ', PHP_EOL;
            //同步规则：system_field 表中存在 ，目标表中不存在则插入
            try {
                $this->syncSystem2Customer($fields, $clientId);
                $this->syncSystem2ExportSetting($fields,$clientId);
                $this->syncSystem2FieldGroup($fields, $clientId);
                $count++;
            } catch (Throwable $e) {
                LogUtil::error('同步字段出错， client_id: ' . $clientId);
                LogUtil::error($e->getMessage());
            }
            echo '成功处理：', $count, PHP_EOL;
        }


    }

    public function actionChangeSystemUniqueSetting()
    {
        //座机 是否判重：默认为是，暂不支持变更；判重处理：默认为阻止保存，可变更为「允许保存」
        //邮箱 客户的联系人邮箱字段的“判重处理”设置放开编辑限制，默认仍为“阻止保存”，但支持用户修改为“允许保存”。
        //联系电话 是否判重：默认为是，暂不支持变更；判重处理：默认为阻止保存，可变更为「允许保存」

        $sqlList = [];
        $sqlList[] = "update tbl_system_field set unique_check=1,unique_message='' 
WHERE type=4 and base=1 and id='name'";
        $sqlList[] = "update tbl_system_field set unique_check=1,unique_prevent=1,unique_message='' 
WHERE type=4 and base=1 and id='homepage'";
        $sqlList[] = "update tbl_system_field set unique_check=1,unique_prevent=1,unique_message='' 
WHERE type=4 and base=1 and id='tel'";
        $sqlList[] = "update tbl_system_field set unique_check=1,unique_prevent=1,unique_message='' 
WHERE type=5 and base=1 and id='email'";
        $sqlList[] = "update tbl_system_field set unique_check=1,unique_prevent=1,unique_message='' 
WHERE type=5 and base=1 and id='tel_list'";
        $sqlList[] = "update tbl_system_field set unique_check=1,unique_prevent=1,unique_message='' 
WHERE type=7 and base=1 and id='company_name'";
        $sqlList[] = "update tbl_system_field set unique_check=1,unique_prevent=1,unique_message='' 
WHERE type=7 and base=1 and id='homepage'";
        $sqlList[] = "update tbl_system_field set unique_check=1,unique_prevent=1,unique_message='' 
WHERE type=7 and base=1 and id='tel'";
        $sqlList[] = "update tbl_system_field set unique_check=1,unique_prevent=1,unique_message='' 
WHERE type=8 and base=1 and id='email'";
        $sqlList[] = "update tbl_system_field set unique_check=1,unique_prevent=1,unique_message='' 
WHERE type=8 and base=1 and id='tel_list'";

        $sql = implode(';',$sqlList);
        $data = Yii::app()->db->createCommand($sql)->execute();
        var_dump($data);
        die;
    }

    // ./yiic-omg customField changeUniqueSetting --clientId=0 --greyNum='1,9'
    public function actionChangeUniqueSetting($clientId, $greyNum = null)
    {
        if ($clientId)
        {
            $clientIds = [$clientId];
        } else {
            if (\Yii::app()->params['env'] == 'test')
                $dbSetId = 3;


            $greyNum = explode(',', $greyNum);
            $clientListData = $this->getClientList($clientId,false,null,null, 0, 0, $greyNum, 0,null);
            $clientIds = array_column($clientListData,'client_id');
        }

        $uniqueCheck = [
            [
                'type' => 4,
                'id' => ['name','homepage','tel'],
                'unique_check' => 1,
            ],
            [
                'type' => 5,
                'id' => ['email','tel_list','contact'],
                'unique_check' => 1,
            ],
            [
                'type' => 7,
                'id' => ['company_name','homepage','tel'],
                'unique_check' => 1,
            ],
            [
                'type' => 8,
                'id' => ['email','tel_list','contact'],
                'unique_check' => 1,
            ],
        ];

        $uniquePrevent = [
            [
                'type' => 4,
                'id' => ['name','tel'],
                'unique_prevent' => 1,
            ],
            [
                'type' => 5,
                'id' => ['email','tel_list'],
                'unique_prevent' => 1,
            ],
        ];

        $uniqueMessage = [
            [
                'type' => 4,
                'id' => 'tel',
                'unique_message' => '',
            ],
            [
                'type' => 5,
                'id' => 'email',
                'unique_message' => '',
            ],
            [
                'type' => 5,
                'id' => 'tel_list',
                'unique_message' => '',
            ],
            [
                'type' => 7,
                'id' => 'tel',
                'unique_message' => '',
            ],
            [
                'type' => 8,
                'id' => 'email',
                'unique_message' => '',
            ],
            [
                'type' => 8,
                'id' => 'tel_list',
                'unique_message' => '',
            ],
        ];

        $clientCount = count($clientIds);
        $num = 1;
        foreach ($clientIds as $clientId)
        {
            echo "$clientId $num/$clientCount \n";
            $num++;

            $sql = [];
            foreach ($uniqueCheck as $item)
            {
                if (empty($item['id']))
                    continue;

                $ids = "'".implode("','", $item['id'])."'";
                $sql[] = "update tbl_custom_field set unique_check={$item['unique_check']} WHERE type={$item['type']} and base=1 and id IN ($ids) and client_id=$clientId";
            }

            foreach ($uniquePrevent as $item)
            {
                if (empty($item['id']))
                    continue;

                $ids = "'".implode("','", $item['id'])."'";
                $sql[] = "update tbl_custom_field set unique_prevent={$item['unique_prevent']} WHERE type={$item['type']} and base=1 and id IN ($ids) and client_id=$clientId";

            }

            foreach ($uniqueMessage as $item)
            {
                if (empty($item['id']))
                    continue;

                $sql[] = "update tbl_custom_field set unique_message='{$item['unique_message']}' WHERE type={$item['type']} and base=1 and id='{$item['id']}' and client_id=$clientId";
            }

            if (empty($sql))
            {
                continue;
            }
            $sqlString = implode(';', $sql);
            $db = ProjectActiveRecord::getDbByClientId($clientId);
            $data = $db->createCommand($sqlString)->execute();
        }
    }

    // 往"tbl_custom_field"和"tbl_field_gourp"表填充"next_follow_up_time"记录
    public function actionAddCompanyNextFollowUpTimeCustomField()
    {
        $clientIdStr = '3,18133,18137,18154,18152,6688,6698,18120,18136,19709,18617';
        $clientIds = explode(',', $clientIdStr);
        foreach ($clientIds as $clientId)
        {
            self::info("clientId[$clientId]");
            $nowTime = "'" . date('Y-m-d H:i:s') . "'";
            $fieldInfo = [
                'client_id'           => $clientId,
                'id'                  => "'next_follow_up_time'",
                'type'                => Constants::TYPE_COMPANY,
                'base'                => 1,
                '`name`'              => "'下次跟进时间'",
                'field_type'          => 10,
                'ext_info'            => "''",
                '`require`'             => 0,
                'edit_required'       => 1,
                'disable_flag'        => 0,
                'edit_hide'           => 1,
                '`default`'           => "''",
                'edit_default'        => 1,
                'hint'                => "''",
                'edit_hint'           => 1,
                'is_editable'         => 1,
                'is_list'             => 0,
                '`columns`'             => "'" . json_encode(['next_follow_up_time']) . "'",
                'relation_type'       => 0,
                'relation_field'      => "''",
                'relation_field_type' => 0,
                'relation_field_name' => "''",
                '`order`'               => 1,
                'app_order'           => 1,
                'enable_flag'         => 1,
                'readonly'            => 0,
                'create_time'         => $nowTime,
                'update_time'         => $nowTime,
            ];
            $fieldGroupInfo = [
                'client_id'   => $clientId,
                'type'        => Constants::TYPE_COMPANY,
                'id'          => "'next_follow_up_time'",
                'group_id'    => 5,
                'create_time' => $nowTime,
                'update_time' => $nowTime,
            ];
            $fieldGroupColumnSql = '(' . implode(',', array_keys($fieldGroupInfo)) . ')';
            $fieldGroupValueSql = '(' . implode(',', $fieldGroupInfo) . ')';
            $insertFieldGroupSql = "INSERT INTO tbl_field_group {$fieldGroupColumnSql} values {$fieldGroupValueSql} ON DUPLICATE KEY UPDATE update_time = VALUES(update_time)";

            $fieldColumnSql = '(' . implode(',', array_keys($fieldInfo)) . ')';
            $fieldValueSql = '(' . implode(',', $fieldInfo) . ')';
            $insertFieldSql = "INSERT INTO tbl_custom_field {$fieldColumnSql} values {$fieldValueSql} ON DUPLICATE KEY UPDATE update_time = VALUES(update_time)";

            try {
                $db = ProjectActiveRecord::getDbByClientId($clientId);
                $db->createCommand($insertFieldGroupSql)->execute();
                $db->createCommand($insertFieldSql)->execute();
            } catch (Throwable $e) {
                self::info("{$e->getMessage()}");
            }
        }
    }

    // 往"tbl_custom_field"和"tbl_field_gourp"表填充"next_follow_up_time"记录
    public function actionAddLeadNextFollowUpTimeCustomField()
    {
//        $clientIds = array_column($this->getClientList(), 'client_id');
        $clientIdStr = '3,18133,18137,18154,18152,6688,6698,18120,18136,19709,18617';
        $clientIds = explode(',', $clientIdStr);
        foreach ($clientIds as $clientId)
        {
            self::info("clientId[$clientId]");
            $nowTime = "'" . date('Y-m-d H:i:s') . "'";
            $fieldInfo = [
                'client_id'           => $clientId,
                'id'                  => "'next_follow_up_time'",
                'type'                => Constants::TYPE_LEAD,
                'base'                => 1,
                '`name`'              => "'下次跟进时间'",
                'field_type'          => 10,
                'ext_info'            => "''",
                '`require`'             => 0,
                'edit_required'       => 1,
                'disable_flag'        => 0,
                'edit_hide'           => 1,
                '`default`'           => "''",
                'edit_default'        => 1,
                'hint'                => "''",
                'edit_hint'           => 1,
                'is_editable'         => 1,
                'is_list'             => 0,
                '`columns`'             => "'" . json_encode(['next_follow_up_time']) . "'",
                'relation_type'       => 0,
                'relation_field'      => "''",
                'relation_field_type' => 0,
                'relation_field_name' => "''",
                '`order`'               => 1,
                'app_order'           => 1,
                'enable_flag'         => 1,
                'readonly'            => 0,
                'create_time'         => $nowTime,
                'update_time'         => $nowTime,
            ];
            $fieldGroupInfo = [
                'client_id'   => $clientId,
                'type'        => Constants::TYPE_LEAD,
                'id'          => "'next_follow_up_time'",
                'group_id'    => 1,
                'create_time' => $nowTime,
                'update_time' => $nowTime,
            ];
            $fieldGroupColumnSql = '(' . implode(',', array_keys($fieldGroupInfo)) . ')';
            $fieldGroupValueSql = '(' . implode(',', $fieldGroupInfo) . ')';
            $insertFieldGroupSql = "INSERT INTO tbl_field_group {$fieldGroupColumnSql} values {$fieldGroupValueSql} ON DUPLICATE KEY UPDATE update_time = VALUES(update_time)";


            $fieldColumnSql = '(' . implode(',', array_keys($fieldInfo)) . ')';
            $fieldValueSql = '(' . implode(',', $fieldInfo) . ')';
            $insertFieldSql = "INSERT INTO tbl_custom_field {$fieldColumnSql} values {$fieldValueSql} ON DUPLICATE KEY UPDATE update_time = VALUES(update_time)";

            try {
                $db = ProjectActiveRecord::getDbByClientId($clientId);
                $db->createCommand($insertFieldGroupSql)->execute();
                $db->createCommand($insertFieldSql)->execute();
            } catch (Throwable $e) {
                self::info("{$e->getMessage()}");
            }
        }
    }

    // 往'tbl_user_setting'表写入company表头设置,增加'ali_store_id'和'next_follow_up_time'表头
    public function actionAddCompanyFieldUserSetting()
    {
        /**
         * @var $accountDb CDbConnection
         */
        $accountDb = Yii::app()->account_base_db;
        $fieldIds = ['ali_store_id', 'next_follow_up_time'];
        $clientIdStr = '3,18133,18137,18154,18152,6688,6698,18120,18136,19709,18617';
        $clientIds = explode(',', $clientIdStr);
        foreach ($clientIds as $clientId)
        {
            $selectUserIdSql = "select user_id from tbl_user_info where client_id = $clientId and enable_flag != 2";
            $userIds = $accountDb->createCommand($selectUserIdSql)->queryColumn();
            $db = ProjectActiveRecord::getDbByClientId($clientId);
            if (empty($db)) continue;
            ProjectActiveRecord::setConnection($db);
            $keys = [\common\library\setting\user\UserSetting::PRIVATE_COMPANY_LIST_FIELD, \common\library\setting\user\UserSetting::PUBLIC_COMPANY_LIST_FIELD];
            foreach ($keys as $key)
            {
                foreach ($userIds as $userId)
                {
                    try {
                        self::info("clientId[$clientId] userId[$userId] key[$key]");
                        $userSetting = new \common\library\setting\user\UserSetting($clientId, $userId, $key);
                        $value = json_decode($userSetting->value, true);
                        if (empty($value)) continue;
                        $valueIds = array_column($value, 'id');
                        foreach ($fieldIds as $fieldId)
                        {
                            if (in_array($fieldId, $valueIds)) continue;
                            $value[] = [
                                'id' => $fieldId,
                                'width' => 120,
                                'fixed' => 0,
                            ];
                        }
                        $userSetting->value = $value;
                        $userSetting->save();
                    } catch (Exception $e) {
                        self::info("clientId[$clientId] userId[$userId] key[$key] 错误[{$e->getMessage()}]");
                    }
                }
            }
        }
    }

    // 往'tbl_user_setting'表写入lead表头设置,增加'ali_store_id'和'next_follow_up_time'表头
    public function actionAddLeadFieldUserSetting()
    {
        /**
         * @var $accountDb CDbConnection
         */
        $accountDb = Yii::app()->account_base_db;
        $fieldIds = ['store_id', 'next_follow_up_time'];
        $clientIdStr = '3,18133,18137,18154,18152,6688,6698,18120,18136,19709,18617';
        $clientIds = explode(',', $clientIdStr);
        foreach ($clientIds as $clientId)
        {
            $selectUserIdSql = "select user_id from tbl_user_info where client_id = $clientId and enable_flag != 2";
            $userIds = $accountDb->createCommand($selectUserIdSql)->queryColumn();
            $db = ProjectActiveRecord::getDbByClientId($clientId);
            if (empty($db)) continue;
            ProjectActiveRecord::setConnection($db);
            $keys = [\common\library\setting\user\UserSetting::LEAD_PRIVATE_LIST_FIELD, \common\library\setting\user\UserSetting::LEAD_PUBLIC_LIST_FIELD];
            foreach ($keys as $key)
            {
                foreach ($userIds as $userId)
                {
                    try {
                        self::info("clientId[$clientId] userId[$userId] key[$key]");
                        $userSetting = new \common\library\setting\user\UserSetting($clientId, $userId, $key);
                        $value = json_decode($userSetting->value, true);
                        if (empty($value)) continue;
                        $valueIds = array_column($value, 'id');
                        foreach ($fieldIds as $fieldId)
                        {
                            if (in_array($fieldId, $valueIds)) continue;
                            $value[] = [
                                'id' => $fieldId,
                                'width' => 120,
                                'fixed' => '0',
                            ];
                        }
                        $userSetting->value = $value;
                        $userSetting->save();
                    } catch (Exception $e) {
                        self::info("clientId[$clientId] userId[$userId] key[$key] 错误[{$e->getMessage()}]");
                    }
                }
            }
        }
    }

    public function actionInitSupplierSystemFields()
    {
        $supplierSettings = [
            [
                'id'         => 'supplier_id',
                'name'       => '供应商id',
                'require'    => 0,
                'hint'       => 1,
                'edit_hint'  => 0,//是否可编辑字段提示：0-否，1-是
                'group_id'   => CustomFieldService::SUPPLIER_GROUP_BASIC,
                'edit_required' => 0,
                'is_exportable' => 0,
                'order' => 1,
                'app_order' => 1,
                'disable_flag'  => 1,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 0,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
            ],
            [
                'id'         => 'name',
                'name'       => '供应商名称',
                'require'    => 1,
                'hint'       => '',
                'edit_hint'  => 1,//是否可编辑字段提示：0-否，1-是
                'group_id'   => CustomFieldService::SUPPLIER_GROUP_BASIC,
                'edit_required' => 0,
                'is_exportable' => 0,
                'order' => 7,
                'app_order' => 7,
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 1,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
            ],
            [
                'id'         => 'user_id',
                'name'       => '跟进人',
                'require'    => 0,
                'hint'       => 1,
                'edit_hint'  => 1,//是否可编辑字段提示：0-否，1-是
                'group_id'   => CustomFieldService::SUPPLIER_GROUP_BASIC,
                'edit_required' => 0,
                'is_exportable' => 0,
                'order' => 1,
                'app_order' => 1,
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 1,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
                'readonly' => 1
            ],
            [
                'id'         => 'rate_id',
                'name'       => '供应商评级',
                'require'    => 0,
                'hint'       => '请选择',
                'edit_hint'  => 1,//是否可编辑字段提示：0-否，1-是
                'group_id'   => CustomFieldService::SUPPLIER_GROUP_BASIC,
                'edit_required' => 1,
                'is_exportable' => 0,
                'order' => 6,
                'app_order' => 6,
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 1,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 1,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
            ],
            [
                'id'         => 'homepage',
                'name'       => '主页',
                'require'    => 0,
                'hint'       => '',
                'edit_hint'  => 1,//是否可编辑字段提示：0-否，1-是
                'group_id'   => CustomFieldService::SUPPLIER_GROUP_BASIC,
                'edit_required' => 1,
                'is_exportable' => 0,
                'order' => 5,
                'app_order' => 5,
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 1,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 1,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
            ],
            [
                'id'         => 'address',
                'name'       => '供应商地址',
                'require'    => 0,
                'hint'       => '',
                'edit_hint'  => 1,//是否可编辑字段提示：0-否，1-是
                'group_id'   => CustomFieldService::SUPPLIER_GROUP_BASIC,
                'edit_required' => 1,
                'is_exportable' => 0,
                'order' => 4,
                'app_order' => 4,
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 1,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 1,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
            ],
            [
                'id'         => 'delivery_date',
                'name'       => '参考交期',
                'require'    => 0,
                'hint'       => '请选择',
                'edit_hint'  => 1,//是否可编辑字段提示：0-否，1-是
                'group_id'   => CustomFieldService::SUPPLIER_GROUP_BASIC,
                'edit_required' => 1,
                'is_exportable' => 0,
                'order' => 3,
                'app_order' => 3,
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 1,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 1,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
            ],
            [
                'id'         => 'remark',
                'name'       => '备注',
                'require'    => 0,
                'hint'       => '请输入',
                'edit_hint'  => 1,//是否可编辑字段提示：0-否，1-是
                'group_id'   => CustomFieldService::SUPPLIER_GROUP_BASIC,
                'edit_required' => 1,
                'is_exportable' => 0,
                'order' => 2,
                'app_order' => 2,
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 1,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 1,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_TEXTAREA,
            ],
            [
                'id'         => 'attachments',
                'name'       => '附件',
                'require'    => 0,
                'hint'       => '',
                'edit_hint'  => 1,//是否可编辑字段提示：0-否，1-是
                'group_id'   => CustomFieldService::SUPPLIER_GROUP_ATTACH,
                'edit_required' => 0,
                'is_exportable' => 0,
                'order' => 1,
                'app_order' => 1,
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 1,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_ATTACH,
            ],
            [
                'id'         => 'contact_name',
                'name'       => '联系人名称',
                'require'    => 1,
                'hint'       => '',
                'edit_hint'  => 1,//是否可编辑字段提示：0-否，1-是
                'group_id'   => CustomFieldService::SUPPLIER_GROUP_CONTACT,
                'edit_required' => 0,
                'is_exportable' => 0,
                'order' => 4,
                'app_order' => 4,
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 1,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
            ],
            [
                'id'         => 'phone',
                'name'       => '手机号',
                'require'    => 0,
                'hint'       => '',
                'edit_hint'  => 1,//是否可编辑字段提示：0-否，1-是
                'group_id'   => CustomFieldService::SUPPLIER_GROUP_CONTACT,
                'edit_required' => 1,
                'is_exportable' => 0,
                'order' => 3,
                'app_order' => 3,
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 1,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 1,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
            ],
            [
                'id'         => 'email',
                'name'       => '邮箱',
                'require'    => 0,
                'hint'       => '',
                'edit_hint'  => 1,//是否可编辑字段提示：0-否，1-是
                'group_id'   => CustomFieldService::SUPPLIER_GROUP_CONTACT,
                'edit_required' => 1,
                'is_exportable' => 0,
                'order' => 2,
                'app_order' => 2,
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 1,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 1,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
            ],
            [
                'id'         => 'social_platform',
                'name'       => '社交平台',
                'require'    => 0,
                'hint'       => '',
                'edit_hint'  => 1,//是否可编辑字段提示：0-否，1-是
                'group_id'   => CustomFieldService::SUPPLIER_GROUP_CONTACT,
                'edit_required' => 1,
                'is_exportable' => 0,
                'order' => 1,
                'app_order' => 1,
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 1,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 1,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
            ],
            [
                'id'         => 'main',
                'name'       => '主联系人标识',
                'require'    => 0,
                'hint'       => 1,
                'edit_hint'  => 0,//是否可编辑字段提示：0-否，1-是
                'group_id'   => CustomFieldService::SUPPLIER_GROUP_CONTACT,
                'edit_required' => 0,
                'is_exportable' => 0,
                'order' => 1,
                'app_order' => 1,
                'disable_flag'  => 1,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 0,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
            ],
            [
                'id'         => 'supplier_contact_id',
                'name'       => '联系人id',
                'require'    => 0,
                'hint'       => 1,
                'edit_hint'  => 0,//是否可编辑字段提示：0-否，1-是
                'group_id'   => CustomFieldService::SUPPLIER_GROUP_CONTACT,
                'edit_required' => 0,
                'is_exportable' => 0,
                'order' => 1,
                'app_order' => 1,
                'disable_flag'  => 1,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 0,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
            ],
            [
                'id'         => 'create_time',
                'name'       => '创建时间',
                'require'    => 0,
                'hint'       => '',
                'edit_hint'  => 1,//是否可编辑字段提示：0-否，1-是
                'group_id'   => CustomFieldService::SUPPLIER_GROUP_SYSTEM,
                'edit_required' => 0,
                'is_exportable' => 0,
                'order' => 1,
                'app_order' => 1,
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 0,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
            ],
            [
                'id'         => 'archive_user',
                'name'       => '创建人',
                'require'    => 0,
                'hint'       => '',
                'edit_hint'  => 1,//是否可编辑字段提示：0-否，1-是
                'group_id'   => CustomFieldService::SUPPLIER_GROUP_SYSTEM,
                'edit_required' => 0,
                'is_exportable' => 0,
                'order' => 1,
                'app_order' => 1,
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 0,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
            ],
            [
                'id'         => 'update_time',
                'name'       => '资料更新时间',
                'require'    => 0,
                'hint'       => '',
                'edit_hint'  => 1,//是否可编辑字段提示：0-否，1-是
                'group_id'   => CustomFieldService::SUPPLIER_GROUP_SYSTEM,
                'edit_required' => 0,
                'is_exportable' => 0,
                'order' => 1,
                'app_order' => 1,
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 0,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
            ],
            [
                'id'         => 'update_user',
                'name'       => '最新修改人',
                'require'    => 0,
                'hint'       => '',
                'edit_hint'  => 1,//是否可编辑字段提示：0-否，1-是
                'group_id'   => CustomFieldService::SUPPLIER_GROUP_SYSTEM,
                'edit_required' => 0,
                'is_exportable' => 0,
                'order' => 1,
                'app_order' => 1,
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 0,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
            ]
        ];

        $lastOrder = count($supplierSettings);
        foreach ($supplierSettings as &$supplierSetting)
        {
            $supplierSetting['order'] = $lastOrder;
            $supplierSetting['app_order'] = $lastOrder;
            $lastOrder--;
        }

        $this->syncSystemFields(Constants::TYPE_SUPPLIER, $supplierSettings);
    }

    public function actionAddSysFieldForPurchaseOrder()
    {
        $purchaseOrderSettings = [
            [
                'id'         => 'purchase_order_no',
                'name'       => '采购订单编号',
                'require'    => 0,
                'hint'       => '',
                'group_id'   => 1, // 基本信息分组
                'edit_required' => 0,
                'is_exportable' => 0,
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hint'  => 1,//是否可编辑字段提示：0-否，1-是
                'edit_hide' => 0,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 0,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
            ],
            [
                'id'         => 'status',
                'name'       => '采购状态',
                'require'    => 1,
                'hint'       => '',
                'group_id'   => 1, // 基本信息分组
                'edit_required' => 0,
                'is_exportable' => 0,
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hint'  => 0,//是否可编辑字段提示：0-否，1-是
                'edit_hide' => 0,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 1,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_STATUS,
            ],
            [
                'id'         => 'handler',
                'name'       => '当前处理人',
                'require'    => 1,
                'hint'       => '',
                'group_id'   => 1, // 基本信息分组
                'edit_required' => 0,
                'is_exportable' => 0,
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hint'  => 1,//是否可编辑字段提示：0-否，1-是
                'edit_hide' => 0,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 1,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_OTHER,
            ],
            [
                'id'         => 'supplier_id',
                'name'       => '供应商',
                'require'    => 1,
                'hint'       => '',
                'group_id'   => 1, // 基本信息分组
                'edit_required' => 0,
                'is_exportable' => 0,
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hint'  => 1,//是否可编辑字段提示：0-否，1-是
                'edit_hide' => 0,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 1,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_OTHER,
            ],
            [
                'id'         => 'supplier_contact',
                'name'       => '供应商联系人',
                'require'    => 1,
                'hint'       => '',
                'group_id'   => 1, // 基本信息分组
                'edit_required' => 1,
                'is_exportable' => 0,
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hint'  => 1,//是否可编辑字段提示：0-否，1-是
                'edit_hide' => 1,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 1,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
            ],
            [
                'id'         => 'currency',
                'name'       => '币种',
                'require'    => 1,
                'hint'       => '',
                'group_id'   => 1, // 基本信息分组
                'edit_required' => 0,
                'is_exportable' => 0,
                'readonly' => 0, //只读字段
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hint'  => 1,//是否可编辑字段提示：0-否，1-是
                'edit_hide' => 0,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 1,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
            ],
            [
                'id'         => 'exchange_rate',
                'name'       => '汇率',
                'require'    => 1,
                'hint'       => '',
                'group_id'   => 1, // 基本信息分组
                'edit_required' => 0,
                'is_exportable' => 0,
                'readonly' => 0, //只读字段
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hint'  => 1,//是否可编辑字段提示：0-否，1-是
                'edit_hide' => 0,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 1,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
            ],
            [
                'id'         => 'purchase_date',
                'name'       => '采购日期',
                'require'    => 1,
                'hint'       => '',
                'group_id'   => 1, // 基本信息分组
                'edit_required' => 1,
                'is_exportable' => 0,
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hint'  => 1,//是否可编辑字段提示：0-否，1-是
                'edit_hide' => 0,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'field_type' => CustomFieldService::FIELD_TYPE_DATE,
            ],
            [
                'id'         => 'delivery_date',
                'name'       => '交货日期',
                'require'    => 0,
                'hint'       => '',
                'group_id'   => 1, // 基本信息分组
                'edit_required' => 1,
                'is_exportable' => 0,
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hint'  => 1,//是否可编辑字段提示：0-否，1-是
                'edit_hide' => 1,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 1,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_DATE,
            ],
            [
                'id'         => 'remark',
                'name'       => '备注',
                'require'    => 0,
                'hint'       => '',
                'edit_hint'  => 1,//是否可编辑字段提示：0-否，1-是
                'group_id'   => 1, // 基本信息分组
                'edit_required' => 1,
                'is_exportable' => 0,
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 1,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 1,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_TEXTAREA,
            ],


            [
                'id'         => 'purchase_order_product_id',
                'name'       => '采购订单产品id',
                'require'    => 0,
                'hint'       => '',
                'edit_hint'  => 0,//是否可编辑字段提示：0-否，1-是
                'group_id'   => 2, // 商品分组
                'edit_required' => 0,
                'is_exportable' => 0,
                'is_list' => 1,
                'readonly' => 1, //只读字段
                'disable_flag'  => 1,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 1,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 0,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_OTHER,
            ],
            [
                'id'         => 'product_id',
                'name'       => '产品id',
                'require'    => 0,
                'hint'       => '',
                'edit_hint'  => 0,//是否可编辑字段提示：0-否，1-是
                'group_id'   => 2, // 商品分组
                'edit_required' => 0,
                'is_exportable' => 0,
                'is_list' => 1,
                'readonly' => 1, //只读字段
                'disable_flag'  => 1,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 1,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 0,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_OTHER,
            ],
            [
                'id'         => 'product_no',
                'name'       => '产品编号',
                'require'    => 0,
                'hint'       => '',
                'edit_hint'  => 0,//是否可编辑字段提示：0-否，1-是
                'group_id'   => 2, // 商品分组
                'edit_required' => 0,
                'is_exportable' => 0,
                'is_list' => 1,
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 1,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 0,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
            ],
            [
                'id'         => 'product_image',
                'name'       => '产品图片',
                'require'    => 0,
                'hint'       => '',
                'edit_hint'  => 1,//是否可编辑字段提示：0-否，1-是
                'group_id'   => 2, // 商品分组
                'edit_required' => 0,
                'is_exportable' => 0,
                'is_list' => 1,
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 1,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 0,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_IMAGE,
            ],
            [
                'id'         => 'product_name',
                'name'       => '产品名称',
                'require'    => 0,
                'hint'       => '',
                'edit_hint'  => 1,//是否可编辑字段提示：0-否，1-是
                'group_id'   => 2, // 商品分组
                'edit_required' => 0,
                'is_exportable' => 0,
                'is_list' => 1,
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 0,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
            ],
            [
                'id'         => 'product_model',
                'name'       => '产品型号',
                'require'    => 0,
                'hint'       => '',
                'edit_hint'  => 1,//是否可编辑字段提示：0-否，1-是
                'group_id'   => 2, // 商品分组
                'edit_required' => 0,
                'is_exportable' => 0,
                'is_list' => 1,
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 0,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
            ],
            [
                'id'         => 'unit',
                'name'       => '计量单位',
                'require'    => 0,
                'hint'       => '',
                'edit_hint'  => 1,//是否可编辑字段提示：0-否，1-是
                'group_id'   => 2, // 商品分组
                'edit_required' => 0,
                'is_exportable' => 0,
                'is_list' => 1,
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 0,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
            ],
            [
                'id'         => 'supplier_product_no',
                'name'       => '供应商产品编号',
                'require'    => 0,
                'hint'       => '',
                'edit_hint'  => 1,//是否可编辑字段提示：0-否，1-是
                'group_id'   => 2, // 商品分组
                'edit_required' => 1,
                'is_exportable' => 0,
                'is_list' => 1,
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 0,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
            ],
            [
                'id'         => 'supplier_product_name',
                'name'       => '供应商产品名称',
                'require'    => 0,
                'hint'       => '',
                'edit_hint'  => 1,//是否可编辑字段提示：0-否，1-是
                'group_id'   => 2, // 商品分组
                'edit_required' => 1,
                'is_exportable' => 0,
                'is_list' => 1,
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 0,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
            ],
            [
                'id'         => 'product_remark',
                'name'       => '产品备注',
                'require'    => 0,
                'hint'       => '',
                'edit_hint'  => 1,//是否可编辑字段提示：0-否，1-是
                'group_id'   => 2, // 商品分组
                'edit_required' => 0,
                'is_exportable' => 0,
                'is_list' => 1,
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 1,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_TEXTAREA,
            ],
            [
                'id'         => 'order_no',
                'name'       => '销售订单编号',
                'require'    => 0,
                'hint'       => '',
                'edit_hint'  => 1,//是否可编辑字段提示：0-否，1-是
                'group_id'   => 2, // 商品分组
                'edit_required' => 0,
                'is_exportable' => 0,
                'is_list' => 1,
                'readonly' => 1, //只读字段
                'disable_flag'  => 1,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 0,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
            ],
            [
                'id'         => 'order_id',
                'name'       => '销售订单ID',
                'require'    => 0,
                'hint'       => '',
                'edit_hint'  => 0,//是否可编辑字段提示：0-否，1-是
                'group_id'   => 2, // 商品分组
                'edit_required' => 0,
                'is_exportable' => 0,
                'is_list' => 1,
                'readonly' => 1, //只读字段
                'disable_flag'  => 1,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 0,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
            ],
            [
                'id'         => 'transfer_invoice_id',
                'name'       => '产品流转ID',
                'require'    => 0,
                'hint'       => '',
                'edit_hint'  => 0,//是否可编辑字段提示：0-否，1-是
                'group_id'   => 2, // 商品分组
                'edit_required' => 0,
                'is_exportable' => 0,
                'is_list' => 1,
                'readonly' => 1, //只读字段
                'disable_flag'  => 1,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 0,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
            ],
            [
                'id'         => 'transfer_invoice_serial_id',
                'name'       => '关联采购任务',
                'require'    => 0,
                'hint'       => '',
                'edit_hint'  => 0,//是否可编辑字段提示：0-否，1-是
                'group_id'   => 2, // 商品分组
                'edit_required' => 0,
                'is_exportable' => 0,
                'is_list' => 1,
                'readonly' => 0, //只读字段
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 0,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
            ],
            [
                'id'         => 'count',
                'name'       => '采购数量',
                'require'    => 1,
                'hint'       => '',
                'edit_hint'  => 1,//是否可编辑字段提示：0-否，1-是
                'group_id'   => 2, // 商品分组
                'edit_required' => 0,
                'is_exportable' => 0,
                'is_list' => 1,
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 1,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
            ],
            [
                'id'         => 'unit_price',
                'name'       => '采购单价',
                'require'    => 1,
                'hint'       => '',
                'edit_hint'  => 1,//是否可编辑字段提示：0-否，1-是
                'group_id'   => 2, // 商品分组
                'edit_required' => 0,
                'is_exportable' => 0,
                'is_list' => 1,
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 1,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
            ],
            [
                'id'         => 'cost_amount',
                'name'       => '金额小计',
                'require'    => 0,
                'hint'       => '',
                'edit_hint'  => 1,//是否可编辑字段提示：0-否，1-是
                'group_id'   => 2, // 商品分组
                'edit_required' => 0,
                'is_exportable' => 0,
                'is_list' => 1,
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 0,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
            ],
            [
                'id'         => 'product_total_count',
                'name'       => '产品总数量',
                'require'    => 0,
                'hint'       => '',
                'edit_hint'  => 1,//是否可编辑字段提示：0-否，1-是
                'group_id'   => 2, // 商品分组
                'edit_required' => 0,
                'is_exportable' => 0,
                'readonly' => 1,
                'disable_flag'  => 1,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 0,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
            ],

            [
                'id'         => 'amount',
                'name'       => '订单金额',
                'require'    => 0,
                'hint'       => '',
                'edit_hint'  => 1,//是否可编辑字段提示：0-否，1-是
                'group_id'   => 3, // 费用分组
                'edit_required' => 0,
                'is_exportable' => 0,
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 0,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
            ],
            [
                'id'         => 'addition_cost_amount',
                'name'       => '附加费用总金额',
                'require'    => 0,
                'hint'       => '',
                'edit_hint'  => 1,//是否可编辑字段提示：0-否，1-是
                'group_id'   => 3, // 费用分组
                'edit_required' => 0,
                'is_exportable' => 0,
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 0,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
            ],
            [
                'id'         => 'product_total_amount',
                'name'       => '产品总金额',
                'require'    => 0,
                'hint'       => '',
                'edit_hint'  => 1,//是否可编辑字段提示：0-否，1-是
                'group_id'   => 3, // 商品分组
                'edit_required' => 0,
                'is_exportable' => 0,
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 0,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
            ],
            [
                'id'         => 'cost_name',
                'name'       => '费用名称',
                'require'    => 0,
                'hint'       => '',
                'edit_hint'  => 1,//是否可编辑字段提示：0-否，1-是
                'group_id'   => 3, // 费用分组
                'edit_required' => 0,
                'is_exportable' => 0,
                'is_list' => 1,
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 1,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
            ],
            [
                'id'         => 'percent_of_total_amount',
                'name'       => '占产品总金额百分比',
                'require'    => 0,
                'hint'       => '',
                'edit_hint'  => 1,//是否可编辑字段提示：0-否，1-是
                'group_id'   => 3, // 费用分组
                'edit_required' => 0,
                'is_exportable' => 0,
                'is_list' => 1,
                'columns' => 'percent_type,percent_amount',
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 1,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
            ],
            [
                'id'         => 'cost',
                'name'       => '金额',
                'require'    => 0,
                'hint'       => '',
                'edit_hint'  => 1,//是否可编辑字段提示：0-否，1-是
                'group_id'   => 3, // 费用分组
                'edit_required' => 0,
                'is_exportable' => 0,
                'is_list' => 1,
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 1,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
            ],

            [
                'id'         => 'create_time',
                'name'       => '创建时间',
                'require'    => 0,
                'hint'       => '',
                'edit_hint'  => 0,//是否可编辑字段提示：0-否，1-是
                'group_id'   => CustomFieldService::PURCHASE_ORDER_GROUP_SYSTEM,
                'edit_required' => 0,
                'is_exportable' => 0,
                'order' => 1,
                'app_order' => 1,
                'readonly' => 1,
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 0,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
            ],
            [
                'id'         => 'creator',
                'name'       => '创建人',
                'require'    => 0,
                'hint'       => 1,
                'edit_hint'  => 0,//是否可编辑字段提示：0-否，1-是
                'group_id'   => CustomFieldService::PURCHASE_ORDER_GROUP_SYSTEM,
                'edit_required' => 0,
                'is_exportable' => 0,
                'order' => 1,
                'app_order' => 1,
                'readonly' => 1,
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 0,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
            ],
            [
                'id'         => 'update_time',
                'name'       => '资料更新时间',
                'require'    => 0,
                'hint'       => 1,
                'edit_hint'  => 0,//是否可编辑字段提示：0-否，1-是
                'group_id'   => CustomFieldService::PURCHASE_ORDER_GROUP_SYSTEM,
                'edit_required' => 0,
                'is_exportable' => 0,
                'order' => 1,
                'app_order' => 1,
                'readonly' => 1,
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 0,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
            ],
            [
                'id'         => 'modifier',
                'name'       => '最新修改人',
                'require'    => 0,
                'hint'       => '',
                'edit_hint'  => 0,//是否可编辑字段提示：0-否，1-是
                'group_id'   => CustomFieldService::PURCHASE_ORDER_GROUP_SYSTEM,
                'edit_required' => 0,
                'is_exportable' => 0,
                'order' => 1,
                'app_order' => 1,
                'readonly' => 1,
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 0,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
            ],
            [
                'id'         => 'create_type',
                'name'       => '创建方式',
                'require'    => 0,
                'hint'       => '',
                'edit_hint'  => 0,//是否可编辑字段提示：0-否，1-是
                'group_id'   => CustomFieldService::PURCHASE_ORDER_GROUP_SYSTEM,
                'edit_required' => 0,
                'is_exportable' => 0,
                'order' => 1,
                'app_order' => 1,
                'readonly' => 1,
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 0,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
            ],
        ];

        $lastOrder = count($purchaseOrderSettings);
        foreach ($purchaseOrderSettings as &$purchaseOrderSetting)
        {
            $purchaseOrderSetting['order'] = $lastOrder;
            $purchaseOrderSetting['app_order'] = $lastOrder;
            var_dump($lastOrder);
            $lastOrder--;
        }
//        var_dump($purchaseOrderSettings);
//        die;
        $this->syncSystemFields(Constants::TYPE_PURCHASE_ORDER, $purchaseOrderSettings);
    }

    //初始化采购订单的setting表
    public function actionInitPurchaseOrderExportFieldSetting($clientId = 0, $grey = 0, $greyNum = 0)
    {

        $fields =  [
            Constants::TYPE_PURCHASE_ORDER => [
                    'currency'=>//币种
                        [
                            'export_scenario' => Constants::TYPE_PURCHASE_ORDER,
                            'export_group' => FieldExportService::PURCHASE_ORDER_GROUP_BASIC
                        ],
                    'exchange_rate'=>//汇率
                        [
                            'export_scenario' => Constants::TYPE_PURCHASE_ORDER,
                            'export_group' => FieldExportService::PURCHASE_ORDER_GROUP_BASIC
                        ],
                    'handler'=>//当前处理人
                        [
                            'export_scenario' => Constants::TYPE_PURCHASE_ORDER,
                            'export_group' => FieldExportService::PURCHASE_ORDER_GROUP_BASIC
                        ],
                    'purchase_order_no'=>//采购订单编号
                        [
                            'export_scenario' => Constants::TYPE_PURCHASE_ORDER,
                            'export_group' => FieldExportService::PURCHASE_ORDER_GROUP_BASIC
                        ],
                    'purchase_date'=>//采购日期
                        [
                            'export_scenario' => Constants::TYPE_PURCHASE_ORDER,
                            'export_group' => FieldExportService::PURCHASE_ORDER_GROUP_BASIC
                        ],
                    'delivery_date'=>//交货日期
                        [
                            'export_scenario' => Constants::TYPE_PURCHASE_ORDER,
                            'export_group' => FieldExportService::PURCHASE_ORDER_GROUP_BASIC
                        ],
                    'supplier_id'=>//供应商
                        [
                            'export_scenario' => Constants::TYPE_PURCHASE_ORDER,
                            'export_group' => FieldExportService::PURCHASE_ORDER_GROUP_BASIC
                        ],
                    'supplier_contact'=>//联系人
                        [
                            'export_scenario' => Constants::TYPE_PURCHASE_ORDER,
                            'export_group' => FieldExportService::PURCHASE_ORDER_GROUP_BASIC
                        ],
                    'remark'=>//备注
                        [
                            'export_scenario' => Constants::TYPE_PURCHASE_ORDER,
                            'export_group' => FieldExportService::PURCHASE_ORDER_GROUP_BASIC
                        ],
                    'status'=>//订单状态
                        [
                            'export_scenario' => Constants::TYPE_PURCHASE_ORDER,
                            'export_group' => FieldExportService::PURCHASE_ORDER_GROUP_BASIC
                        ],
                    'product_total_amount'=>//产品总金额
                        [
                            'export_scenario' => Constants::TYPE_PURCHASE_ORDER,
                            'export_group' => FieldExportService::PURCHASE_ORDER_GROUP_BASIC
                        ],
                    'amount'=>//订单金额
                        [
                            'export_scenario' => Constants::TYPE_PURCHASE_ORDER,
                            'export_group' => FieldExportService::PURCHASE_ORDER_GROUP_BASIC
                        ],
                    'addition_cost_amount'=>//附加费用总金额
                        [
                            'export_scenario' => Constants::TYPE_PURCHASE_ORDER,
                            'export_group' => FieldExportService::PURCHASE_ORDER_GROUP_BASIC
                        ],
                    'product_total_count'=>//产品总数量
                        [
                            'export_scenario' => Constants::TYPE_PURCHASE_ORDER,
                            'export_group' => FieldExportService::PURCHASE_ORDER_GROUP_BASIC
                        ],
                    //采购产品字段-------------------
                    'product_no'=>[
                        'export_scenario' => Constants::TYPE_PURCHASE_ORDER,
                        'export_group' => FieldExportService::PURCHASE_ORDER_GROUP_EXCHANGE_PRODUCT
                    ],//编号
                    'product_image'=>[
                        'export_scenario' => Constants::TYPE_PURCHASE_ORDER,
                        'export_group' => FieldExportService::PURCHASE_ORDER_GROUP_EXCHANGE_PRODUCT
                    ],//图片
                    'product_name'=>[
                        'export_scenario' => Constants::TYPE_PURCHASE_ORDER,
                        'export_group' => FieldExportService::PURCHASE_ORDER_GROUP_EXCHANGE_PRODUCT
                    ],//名称
                    'product_model'=>[
                        'export_scenario' => Constants::TYPE_PURCHASE_ORDER,
                        'export_group' => FieldExportService::PURCHASE_ORDER_GROUP_EXCHANGE_PRODUCT
                    ],//型号

                    'product_remark'=>[
                        'export_scenario' => Constants::TYPE_PURCHASE_ORDER,
                        'export_group' => FieldExportService::PURCHASE_ORDER_GROUP_EXCHANGE_PRODUCT
                    ],//产品备注

                    'unit'=>[
                        'export_scenario' => Constants::TYPE_PURCHASE_ORDER,
                        'export_group' => FieldExportService::PURCHASE_ORDER_GROUP_EXCHANGE_PRODUCT
                    ],//计量单位

                    'unit_price'=>[
                        'export_scenario' => Constants::TYPE_PURCHASE_ORDER,
                        'export_group' => FieldExportService::PURCHASE_ORDER_GROUP_EXCHANGE_PRODUCT
                    ],//采购单价

                    'count'=>[
                        'export_scenario' => Constants::TYPE_PURCHASE_ORDER,
                        'export_group' => FieldExportService::PURCHASE_ORDER_GROUP_EXCHANGE_PRODUCT
                    ],//采购数量

                    'cost_amount'=>[
                        'export_scenario' => Constants::TYPE_PURCHASE_ORDER,
                        'export_group' => FieldExportService::PURCHASE_ORDER_GROUP_EXCHANGE_PRODUCT
                    ],//金额小计
                    'supplier_product_no'=>[
                        'export_scenario' => Constants::TYPE_PURCHASE_ORDER,
                        'export_group' => FieldExportService::PURCHASE_ORDER_GROUP_EXCHANGE_PRODUCT
                    ],//供应商产品编号
                    'supplier_product_name'=>[
                        'export_scenario' => Constants::TYPE_PURCHASE_ORDER,
                        'export_group' => FieldExportService::PURCHASE_ORDER_GROUP_EXCHANGE_PRODUCT
                    ],//供应商产品名称

                    //其他费用---------
                    'cost_name'=>
                        [
                            'export_scenario' => Constants::TYPE_PURCHASE_ORDER,
                            'export_group' => FieldExportService::PURCHASE_ORDER_GROUP_ADDITION_FEE
                        ],
                    'cost'=>
                        [
                            'export_scenario' => Constants::TYPE_PURCHASE_ORDER,
                            'export_group' => FieldExportService::PURCHASE_ORDER_GROUP_ADDITION_FEE
                        ],
                    'percent_of_total_amount'=>
                        [
                            'export_scenario' => Constants::TYPE_PURCHASE_ORDER,
                            'export_group' => FieldExportService::PURCHASE_ORDER_GROUP_ADDITION_FEE
                        ],
                ],
            Constants::TYPE_SUPPLIER => [
                    //供应商名称
                    'name'=>[
                        'export_scenario' => Constants::TYPE_PURCHASE_ORDER,
                        'export_group'=> FieldExportService::PURCHASE_ORDER_GROUP_SUPPLIER_INFO
                    ],
                    //供应商评级
                    'rate_id'=>[
                        'export_scenario' => Constants::TYPE_PURCHASE_ORDER,
                        'export_group'=> FieldExportService::PURCHASE_ORDER_GROUP_SUPPLIER_INFO
                    ],
                    //供应商主页
                    'homepage'=>[
                        'export_scenario' => Constants::TYPE_PURCHASE_ORDER,
                        'export_group'=> FieldExportService::PURCHASE_ORDER_GROUP_SUPPLIER_INFO
                    ],
                    //供应商地址
                    'address'=>[
                        'export_scenario' => Constants::TYPE_PURCHASE_ORDER,
                        'export_group'=> FieldExportService::PURCHASE_ORDER_GROUP_SUPPLIER_INFO
                    ],
                    //参考交期
                    'delivery_date'=>[
                        'export_scenario' => Constants::TYPE_PURCHASE_ORDER,
                        'export_group'=> FieldExportService::PURCHASE_ORDER_GROUP_SUPPLIER_INFO
                    ],
                    //备注
                    'remark'=>[
                        'export_scenario' => Constants::TYPE_PURCHASE_ORDER,
                        'export_group'=> FieldExportService::PURCHASE_ORDER_GROUP_SUPPLIER_INFO
                    ],
                    //姓名/昵称
                    'contact_name'=>[
                        'export_scenario' => Constants::TYPE_PURCHASE_ORDER,
                        'export_group'=> FieldExportService::PURCHASE_ORDER_GROUP_SUPPLIER_CONTACT
                    ],
                    //邮箱
                    'email'=>[
                        'export_scenario' => Constants::TYPE_PURCHASE_ORDER,
                        'export_group'=> FieldExportService::PURCHASE_ORDER_GROUP_SUPPLIER_CONTACT
                    ],
                    //电话/手机号
                    'phone'=>[
                        'export_scenario' => Constants::TYPE_PURCHASE_ORDER,
                        'export_group'=> FieldExportService::PURCHASE_ORDER_GROUP_SUPPLIER_CONTACT
                    ],
                    //社交平台
                    'social_platform' => [
                        'export_scenario' => Constants::TYPE_PURCHASE_ORDER,
                        'export_group' => FieldExportService::PURCHASE_ORDER_GROUP_SUPPLIER_CONTACT
                    ],
                ],
            //商品单页字段
            Constants::TYPE_PRODUCT => [
                'group_id'=>[
                    'export_scenario' => [2,3,Constants::TYPE_PURCHASE_ORDER],
                    'export_group' => FieldExportService::PURCHASE_ORDER_GROUP_PRODUCT_PAGE,
                ],//产品分组
                'package_remark'=>[
                    'export_scenario' => [2,3,Constants::TYPE_PURCHASE_ORDER],
                    'export_group' => FieldExportService::PURCHASE_ORDER_GROUP_PRODUCT_PAGE,
                ],//包装说明
                'category_ids'=>[
                    'export_scenario' => [2,3,Constants::TYPE_PURCHASE_ORDER],
                    'export_group' => FieldExportService::PURCHASE_ORDER_GROUP_PRODUCT_PAGE,
                ],//产品类目
                'hs_code'=>[
                    'export_scenario' => [2,3,Constants::TYPE_PURCHASE_ORDER],
                    'export_group' => FieldExportService::PURCHASE_ORDER_GROUP_PRODUCT_PAGE,
                ],//海关编码
                'info_json'=>[
                    'export_scenario' => [2,3,Constants::TYPE_PURCHASE_ORDER],
                    'export_group' => FieldExportService::PURCHASE_ORDER_GROUP_PRODUCT_PAGE,
                ],//产品属性
                'from_url'=>[
                    'export_scenario' => [2,3,Constants::TYPE_PURCHASE_ORDER],
                    'export_group' => FieldExportService::PURCHASE_ORDER_GROUP_PRODUCT_PAGE,
                ],//产品链接
                'product_remark'=>[
                    'export_scenario' => [2,3,Constants::TYPE_PURCHASE_ORDER],
                    'export_group' => FieldExportService::PURCHASE_ORDER_GROUP_PRODUCT_PAGE,
                ],//产品备注
                'images'=>[
                    'export_scenario' => [2,3,Constants::TYPE_PURCHASE_ORDER],
                    'export_group' => FieldExportService::PURCHASE_ORDER_GROUP_PRODUCT_PAGE,
                ],//产品图片
            ],
        ];

        //更新需要导出的系统字段
        $map = [];
        foreach ($fields as $type =>  $field) {
            foreach ($field as $id =>$item) {
                $map[] = ['condition' => ['type' => $type, 'id' => $id],
                    'update' => ['export_scenario'=> (is_array($item['export_scenario']) ? implode(',', $item['export_scenario']) : $item['export_scenario']), 'export_group' => $item['export_group'],'is_exportable' => 1]];
            }
        }
        $admin_db = Yii::app()->db;
        echo "update db v4_admin" . PHP_EOL;
        foreach ($map as $item) {
            $criteria = new CDbCriteria();
            foreach ($item['condition'] as $key => $condition) {
                $criteria->addCondition("$key=:$key");
            }
            $criteria->params += $item['condition'];
            if (isset($item['update']['enable_flag']) && $item['update']['enable_flag'] == 0) {
                $result = $admin_db->getCommandBuilder()->createDeleteCommand('tbl_system_field', $criteria)->execute(); //删除系统字段
            } else {
                $result = $admin_db->getCommandBuilder()->createUpdateCommand('tbl_system_field', $item['update'], $criteria)->execute();
            }
            echo 'result: ' . $result . ', update tbl_system_field: ' . json_encode($item) . PHP_EOL;
        }

        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        $systemFields = [];
        $sql = "type in (".Constants::TYPE_PURCHASE_ORDER.", ".Constants::TYPE_SUPPLIER.", ".Constants::TYPE_PRODUCT.") and is_exportable = 1";
        $settings = \common\models\admin\SystemField::model()->findAll($sql);
        foreach ($settings as $setting) {
            $systemFields[] = $setting->getAttributes();
        }
        foreach ($clientIds as $clientId) {
            $db = ProjectActiveRecord::getDbByClientId($clientId);
            if (!$db)
                continue;

            self::info("开始处理:$clientId");
            //自定义字段：采购、供应商、产品同步到export_setting
            $exportGroup = [
                Constants::TYPE_PURCHASE_ORDER => FieldExportService::PURCHASE_ORDER_GROUP_BASIC,
                Constants::TYPE_SUPPLIER => FieldExportService::PURCHASE_ORDER_GROUP_SUPPLIER_INFO,
                Constants::TYPE_PRODUCT => FieldExportService::PURCHASE_ORDER_GROUP_PRODUCT_PAGE,
            ];
            try {
                $this->syncSystem2ExportSetting($systemFields,$clientId);
                self::info("开始处理自定义字段");
                $this->syncCustomFields2ExportSetting($exportGroup, $clientId,Constants::TYPE_PURCHASE_ORDER);
                self::info("完成处理:$clientId");
            } catch (Exception $e)
            {
                self::info($e->getMessage());
            }

        }
    }

    public function syncCustomFields2ExportSetting(array $exportGroups, $clientId, $exportScenario)
    {
        $customFields = [];
        $types = array_keys($exportGroups);
        $sql = "select * from tbl_custom_field  where  client_id = {$clientId} and type in (" . implode(',', $types) . ") and  enable_flag = 1 and base = 0  and field_type != " . CustomFieldService::FIELD_TYPE_ATTACH;
        $fields = ProjectActiveRecord::getDbByClientId($clientId)->createCommand($sql)->queryAll(true);
        foreach ($fields as $customField) {
            $customField['is_exportable'] = 1;
            $customField['export_scenario'] = $exportScenario;
            $customField['export_group'] = $exportGroups[$customField['type']];
            $customFields[] = $customField;
        }
        if (empty($customFields)) {
            LogUtil::info("client_id:{$clientId}  未检测到自定义字段字段差异,略过");
            echo "client_id:{$clientId} 未检测到自定义字段字段差异,略过" . PHP_EOL;
            return;
        }
        $this->syncSystem2ExportSetting($customFields, $clientId);

    }



    public function actionChangeSupplierField()
    {
        //获取系统字段
        $system_fields = Yii::app()->db->createCommand('SELECT * FROM tbl_system_field')->queryAll(true);

        //获取所有client 遍历
        $clients = $this->getClientList();
        $count = 0;
        foreach ($clients as $client) {

            if ($client['client_id'] < 0) {
                continue;
            }
            $db = ProjectActiveRecord::getDbByClientId($client['client_id']);
            if ($db) {//测试环境存在有问题的client
                $sql = "delete FROM tbl_field_group where type=23 and id=0 and client_id={$client['client_id']};";
                $sql .= " delete FROM tbl_custom_field where type=23 and base=1 and client_id={$client['client_id']};";
                $db->createCommand($sql)->execute();

                //同步规则：system_field 表中存在 ，目标表中不存在则插入
                $this->syncForClient($system_fields, $client['client_id']);
            }
            $count++;
        }

        echo '共需处理：', count($clients), PHP_EOL;
        echo '成功处理：', $count, PHP_EOL;
    }



    public function actionMigrateProductOrderDispatcher($clientId,$greyNum=null)
    {
        if ($clientId)
        {
            $clientIds = [$clientId];
        } else {
            if (\Yii::app()->params['env'] == 'test')
                $dbSetId = 3;

            $greyNum = explode(',', $greyNum);
            $clientListData = $this->getClientList($clientId,false,null,null, 0, 0, $greyNum, $dbSetId,null);
            $clientIds = array_column($clientListData,'client_id');
        }
        foreach ($clientIds as $clientId)
        {
            $this->actionMigrateProductOrder($clientId);
        }
    }

    public function actionChangeField()
    {
        //获取系统字段
        $system_fields = Yii::app()->db->createCommand('SELECT * FROM tbl_system_field where type=22')->queryAll(true);

        //获取所有client 遍历
        $clients = $this->getClientList();
        $count = 0;
        foreach ($clients as $client) {

            if ($client['client_id'] < 33115) {
                continue;
            }

            $db = ProjectActiveRecord::getDbByClientId($client['client_id']);
            if ($db) {//测试环境存在有问题的client
                $sql = "delete FROM tbl_field_group where type=22 and id=0 and client_id={$client['client_id']};";
                $sql .= " delete FROM tbl_custom_field where type=22 and base=1 and client_id={$client['client_id']};";
                $db->createCommand($sql)->execute();

                //同步规则：system_field 表中存在 ，目标表中不存在则插入
                $this->syncForClient($system_fields, $client['client_id']);
            }
            $count++;
        }

        echo '共需处理：', count($clients), PHP_EOL;
        echo '成功处理：', $count, PHP_EOL;
    }

    /**
     * 系统字段表 产品类型某些字段重新分组和排序（执行一次即可）(上线后再执行)
     */
    public function actionChangeProductSystemFieldAndSort()
    {
        // 需要迁移字段
        $fieldIdMap = [
            [
                'id' => 'unit',//价格信息分组的计量单位分组更新为1
                'old_group' => 2,//原分组id
                'new_group' => 1 //新分组id
            ],

            [
                'id' => ['from_url','product_remark'],// 产品介绍的产品链接和产品备注的分组更新为1
                'old_group' => 4, //原分组id
                'new_group' => 1 //新分组id
            ]
        ];

        foreach ($fieldIdMap as $item) {
            $id = $item['id'] ?? '';
            $old_group = $item['old_group'] ?? '';
            $new_group = $item['new_group'] ?? '';
            if (empty($id) || empty($old_group) || empty($new_group)) {
                continue;
            }

            if (is_array($id)) {
                $id = array_map(function ($item) {
                    return "'$item'";
                }, $id);
            } else {
                $id = ["'{$id}'"];
            }

            $sql = "update tbl_system_field set `group_id`={$new_group} where `type`=1 and group_id={$old_group} and id in (" . implode(',', $id) . ") ";

            $result = \Yii::app()->db->createCommand($sql)->execute();

            var_dump($result);
        }
    }

    /**
     * 将产品某些字段从不同分组迁移另一个分组（2021-05-14）
     * 需求：https://app.axure.cloud/app/project/2vpeko/preview/daxkzr
     */
    public function actionChangeProductFieldGroup($clientId = 3, $greyNum=null)
    {
        if ($clientId) {
            $clients = $this->getClientList($clientId);
        } else {
            $greyNum = explode(',', $greyNum);
            $clients = $this->getClientList($clientId, false, null, null, 0, 0, $greyNum);
        }

        // 需要迁移字段
        $fieldIdMap = [
            [
                'id' => 'unit',//价格信息分组的计量单位分组更新为1
                'old_group' => 2,//原分组id
                'new_group' => 1 //新分组id
            ],

            [
                'id' => ['from_url','product_remark'],// 产品介绍的产品链接和产品备注的分组更新为1
                'old_group' => 4, //原分组id
                'new_group' => 1 //新分组id
            ]
        ];

        foreach ($fieldIdMap as $item)
        {
            $id = $item['id'] ?? '';
            $old_group = $item['old_group'] ?? '';
            $new_group = $item['new_group'] ?? '';
            if(empty($id) || empty($old_group) || empty($new_group))
            {
                continue;
            }

            if(is_array($id))
            {
                $id = array_map(function ($item) {
                    return "'$item'";
                }, $id);
            }else
            {
                $id = ["'{$id}'"];
            }

            foreach($clients as $client) {
                $clientId = $client['client_id'];
                $db = ProjectActiveRecord::getDbByClientId($clientId);
                try {
                    $sql = "update tbl_field_group set `group_id`={$new_group} where client_id={$clientId} and `type`=1 and group_id={$old_group} and id in (".implode(',', $id).") ";
                    $db->createCommand($sql)->execute();
                    var_dump('group process client_id=' . $clientId);
                    usleep(20000);
                } catch (\Throwable $e) {
                    var_dump("clientId={$clientId}, update group error=" . $e->getMessage());
                }
            }
        }
    }

    /**
     * 分组产品价格的离岸价字段fob整合最小起订量minimum_order_quantity的columns的quantity设置在fob的columns中，统一由fob字段控制，不再独立控制
     * 需求：https://app.axure.cloud/app/project/2vpeko/preview/qulwa0
     */
    public function actionFixProductFieldOfFob($clientId = 3, $greyNum=null)
    {
        //$clientId = 14367;
        // 需要更新的值
        $columns = '["price_currency","price_min","price_max","quantity","fob_type","gradient_price"]';

        if ($clientId) {
            $clients = $this->getClientList($clientId);
        } else {
            $greyNum = explode(',', $greyNum);
            $clients = $this->getClientList($clientId, false, null, null, 0, 0, $greyNum);
        }

        foreach($clients as $client)
        {
            $clientId = $client['client_id'];
            $db = ProjectActiveRecord::getDbByClientId($clientId);

            // 更新
            $sql = "update tbl_custom_field set columns='{$columns}',edit_hint=0 where client_id={$clientId} and `type`=1 and id='fob' ";
            $result = $db->createCommand($sql)->execute();

            // 原独立最小起订量minimum_order_quantity将它设置为删除标志
            //$sql = "update tbl_custom_field set enable_flag=0 where client_id={$clientId} and `type`=1 and id='minimum_order_quantity' ";

            // 完全删除
            $sql = "delete from tbl_custom_field where client_id={$clientId} and `type`=1 and id='minimum_order_quantity' ";

            $db->createCommand($sql)->execute();

            var_dump($clientId);
        }

    }

    public function actionFixFunctionalRelateDisableField($clientId, $dryRun = 1)
    {
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } else {
            $clientIds = Yii::app()->account_base_db->createCommand("SELECT client_id FROM tbl_client WHERE valid_from > '2021-03-18'")->queryColumn();
//            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        foreach ($clientIds as $clientId) {
            echo "start $clientId \n";
            foreach (\common\library\custom_field\CustomFieldService::FUNCTIONAL_DEFAULT_ENABLE as $functionalId => $fieldList) {
                if (PrivilegeService::getInstance($clientId)->hasFunctional($functionalId)) {
                    if (!$dryRun) {
                        ProjectActiveRecord::setConnection(ProjectActiveRecord::getDbByClientId($clientId));
                        foreach ($fieldList as $type => $fieldIds) {
                            foreach ($fieldIds as $fieldId) {
                                \common\library\custom_field\CustomFieldService::enableField($clientId, $type, $fieldId);
                            }
                        }
                        $privilegeField = new \common\library\privilege_v3\PrivilegeField($clientId);
                        $privilegeField->flushCache();
                        ProjectActiveRecord::releaseDbByClientId($clientId);
                    }
                    echo "process $functionalId for $clientId \n";
                } else {
                    echo "skip $functionalId for $clientId \n";
                }
            }
        }
    }

    /*
     * 产品系统字段增加产品规格（执行一次即可）(上线再跑)
     *
     */
    public function actionAddSysFieldForProduct()
    {
        $companySetting = [
            [
                'id'         => 'sku_attributes',
                'name'       => '产品规格',
                'require'    => 0,
                'hint'       => '',
                'edit_hint'  => 0,
                'group_id'   => CustomFieldService::PRODUCT_GROUP_SKU, // 产品规格分组
                'edit_required' => 0,
                'is_exportable' => 0,
                'order' => 1,
                'app_order' => 1,
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可编辑，1可以，0否
                'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
            ],
        ];

        $this->syncSystemFields(Constants::TYPE_PRODUCT, $companySetting);
    }

    /**
     * 将产品规格分组字段同步到自定义字段表
     * @param int $clientId
     * @param null $lastNumber
     */
    public function actionAddFieldForProduct($clientId = 3, $lastNumber = null)
    {
        //$clientId = 14064;
        $system_fields = [
            [
                'id'                  => 'sku_attributes',
                'type'                => Constants::TYPE_PRODUCT,//产品
                'group_id'            => 5, // 信息分组
                'base'                => 1,
                'name'                => '产品规格',
                'field_type'          => CustomFieldService::FIELD_TYPE_TEXT,
                'require'             => 0,
                'edit_required'       => 0,
                'disable_flag'        => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide'           => 0,// 是否可编辑，1可以，0否
                'default'             => '',
                'edit_default'        => 0,
                'hint'                => '',
                'edit_hint'           => 0,
                'is_exportable'       => 0,
                'is_editable'         => 1,
                'is_list'             => 0,
                'columns'             => 'sku_attributes',
                'relation_type'       => 0,
                'relation_field'      => '',
                'relation_field_type' => 0,
                'relation_field_name' => '',
                'export_scenario'     => '',
                'export_group'        => '',
                'readonly'            => 0,
                'order' => 1,
                'app_order' =>1,
                'create_time'         => date('Y-m-d H:i:s'),
                'update_time'         => date('Y-m-d H:i:s')
            ]
        ];

        if (empty($clientId)) {
            //获取所有client 遍历
            $clients = $this->getClientList(0, false, null, null, 0, 0, $lastNumber);
            $count = 0;
            echo '共需处理：', count($clients), PHP_EOL;
            foreach ($clients as $client) {
                echo '开始处理：', $client->client_id . ' ', PHP_EOL;
                //同步规则：system_field 表中存在 ，目标表中不存在则插入
                try {
                    $this->syncSystem2Customer($system_fields, $client->client_id);
                    $this->syncSystem2FieldGroup($system_fields, $client->client_id);
                    $count++;
                } catch (Throwable $e) {
                    LogUtil::error('同步字段出错， client_id: ' . $client->client_id);
                    LogUtil::error($e->getMessage());
                }
            }
            echo '成功处理：', $count, PHP_EOL;
        } else {
            $this->syncSystem2Customer($system_fields, $clientId);
            $this->syncSystem2FieldGroup($system_fields, $clientId);
            echo '成功处理client：', $clientId, PHP_EOL;
        }
    }

    /**
     * 重置产品字段的价格分组信息 cost_with_tax和fob排序
     */
    public function actionResetProductPriceGroupSort($clientId = 3, $greyNum = null)
    {
        if ($clientId) {
            $clients = $this->getClientList($clientId);
        } else {
            $greyNum = explode(',', $greyNum);
            $clients = $this->getClientList($clientId, false, null, null, 0, 0, $greyNum);
        }

        $clientIds = array_column($clients,'client_id');

        foreach($clientIds as $clientId)
        {
            $db = ProjectActiveRecord::getDbByClientId($clientId);

            $sql = "update tbl_custom_field set `order`=15,`app_order`=15 where client_id={$clientId} and `type`=1 and id = 'cost_with_tax' ";

            $db->createCommand($sql)->execute();

            $sql = "update tbl_custom_field set `order`=14,`app_order`=14 where client_id={$clientId} and `type`=1 and id = 'fob' ";

            $db->createCommand($sql)->execute();

            usleep(15000);

            var_dump('current clientId='.$clientId);

        }
    }

    /**
     * 订单的交易分组添加系统字段sku_id(执行一次即可) (上线执行)
     */
    public function actionAddSystemFieldSkuIdToOrder()
    {
        $env = \Yii::app()->params['env'];
        if(in_array($env, ['test']))
        {
            // 排在模型排序之后
            $order = $app_order = 1996252405 - 1;
        }else
        {
            $order = $app_order = 1994091959 - 1;
        }

        $systemFields = [
            [
                'id'         => 'sku_id',
                'name'       => '产品规格',
                'require'    => 0,
                'hint'       => '',
                'edit_hint'  => 0,//是否可编辑字段提示：0-否，1-是
                'group_id'   => CustomFieldService::ORDER_GROUP_PRODUCT, // 商品分组
                'edit_required' => 0,
                'is_exportable' => 1,
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 1,// 是否可隐藏，1可以，0否
                'order' => $order,
                'app_order' => $app_order,
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 1,//是否可编辑：0-否，1-是
                'is_list' => 1,
                'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
            ],
        ];

        $this->syncSystemFields(Constants::TYPE_ORDER, $systemFields);

    }

    /**
     * 将产品规格分组字段同步到自定义字段表
     * @param int $clientId
     * @param null $lastNumber
     */
    public function actionAddFieldForOrder($clientId = 3, $lastNumber = null)
    {
        //$clientId = 14064;
        $system_fields = [
            [
                'id'                  => 'sku_id',
                'type'                => Constants::TYPE_ORDER,//产品
                'group_id'            => CustomFieldService::ORDER_GROUP_PRODUCT, // 交易分组
                'base'                => 1,
                'name'                => '产品规格',
                'field_type'          => CustomFieldService::FIELD_TYPE_SELECT,
                'require'             => 0,
                'edit_required'       => 0,
                'disable_flag'        => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide'           => 1,// 是否可编辑，1可以，0否
                'default'             => '',
                'edit_default'        => 0,
                'hint'                => '',
                'edit_hint'           => 0,
                'is_exportable'       => 1,
                'is_editable'         => 1,
                'is_list'             => 1,
                'columns'             => 'sku_id',
                'relation_type'       => 0,
                'relation_field'      => '',
                'relation_field_type' => 0,
                'relation_field_name' => '',
                'export_scenario'     => '',
                'export_group'        => '',
                'readonly'            => 0,
                'create_time'         => date('Y-m-d H:i:s'),
                'update_time'         => date('Y-m-d H:i:s')
            ]
        ];

        if (empty($clientId)) {
            //获取所有client 遍历
            $clients = $this->getClientList(0, false, null, null, 0, 0, $lastNumber);
            $count = 0;
            echo '共需处理：', count($clients), PHP_EOL;
            foreach ($clients as $client) {
                echo '开始处理：', $client->client_id . ' ', PHP_EOL;
                $clientId = $client->client_id;

                // 字段排在product_model之后
                $sql = "select `order`,`app_order` from tbl_custom_field where client_id={$clientId} and `type`=2 and id='product_model' limit 1 ";
                $row = \ProjectActiveRecord::getDbByClientId($clientId)->createCommand($sql)->queryRow();
                $order = $row['order'] ?? '';
                $app_order = $row['app_order'] ?? '';
                if($order)
                {
                    $order = $order -1;
                }
                if($app_order)
                {
                    $app_order = $app_order -1;
                }

                $system_fields[0]['order'] = $order;
                $system_fields[0]['app_order'] = $app_order;

                //同步规则：system_field 表中存在 ，目标表中不存在则插入
                try {
                    $this->syncSystem2Customer($system_fields, $client->client_id);
                    $this->syncSystem2FieldGroup($system_fields, $client->client_id);
                    $count++;
                } catch (Throwable $e) {
                    LogUtil::error('同步字段出错， client_id: ' . $client->client_id);
                    LogUtil::error($e->getMessage());
                }
            }
            echo '成功处理：', $count, PHP_EOL;
        } else {

            // 字段排在product_model之后
            $sql = "select `order`,`app_order` from tbl_custom_field where client_id={$clientId} and `type`=2 and id='product_model' limit 1 ";
            $row = \ProjectActiveRecord::getDbByClientId($clientId)->createCommand($sql)->queryRow();
            $order = $row['order'] ?? '';
            $app_order = $row['app_order'] ?? '';
            if($order)
            {
                $order = $order -1;
            }
            if($app_order)
            {
                $app_order = $app_order -1;
            }

            $system_fields[0]['order'] = $order;
            $system_fields[0]['app_order'] = $app_order;

            $this->syncSystem2Customer($system_fields, $clientId);
            $this->syncSystem2FieldGroup($system_fields, $clientId);
            echo '成功处理client：', $clientId, PHP_EOL;
        }
    }

    /**
     * 脚本聚合单个client 跑
     * @param int $clientId
     * @param null $lastNumber
     */
    public function actionErpFieldHandle($clientId = 3, $lastNumber = null)
    {
        // 改变字段分组
        $this->actionChangeProductFieldGroup($clientId, $lastNumber);
        sleep(1);
        // fob离岸价字段整合最小起订量等字段
        $this->actionFixProductFieldOfFob($clientId, $lastNumber);
        sleep(1);
        // 产品规格sku_attributes 字段同步到自定义字段表
        $this->actionAddFieldForProduct($clientId, $lastNumber);
        sleep(1);
        // 重置产品字段的价格分组信息 cost_with_tax和fob排序
        $this->actionResetProductPriceGroupSort($clientId, $lastNumber);
        sleep(1);
        // 产品规格sku)id 订单交易分组字段同步到自定义字段表
        $this->actionAddFieldForOrder($clientId, $lastNumber);
    }


    /**
     * 商机、报价单、采购订单添加sku_id系统字段
     */
    public function actionAddSkuIdSystemField() {
        $env = \Yii::app()->params['env'];
        $opportunitySystemFields = [
            [
                'id'         => 'sku_id',
                'name'       => '商机-产品规格',
                'require'    => 0,
                'hint'       => '',
                'edit_hint'  => 1,//是否可编辑字段提示：0-否，1-是
                'group_id'   => CustomFieldService::OPPORTUNITY_GROUP_PRODUCT,
                'edit_required' => 1,
                'is_exportable' => 1,
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 1,// 是否可隐藏，1可以，0否
                'order' => 97,
                'app_order' => 97,
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 1,//是否可编辑：0-否，1-是
                'is_list' => 1,
                'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
            ],
        ];
        $quotationSystemFields = [
            [
                'id'         => 'sku_id',
                'name'       => '产品规格',
                'require'    => 0,
                'hint'       => '',
                'edit_hint'  => 1,//是否可编辑字段提示：0-否，1-是
                'group_id'   => CustomFieldService::QUOTATION_GROUP_PRODUCT,
                'edit_required' => 1,
                'is_exportable' => 1,
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 1,// 是否可隐藏，1可以，0否
                'order' => in_array($env, ['test']) ? 31 : 17,
                'app_order' => in_array($env, ['test']) ? 31 : 17,
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 1,//是否可编辑：0-否，1-是
                'is_list' => 1,
                'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
            ],
        ];
        $purchaseSystemFields = [
            [
                'id'         => 'sku_id',
                'name'       => '产品规格',
                'require'    => 0,
                'hint'       => '',
                'edit_hint'  => 1,//是否可编辑字段提示：0-否，1-是
                'group_id'   => CustomFieldService::PURCHASE_ORDER_GROUP_PRODUCT,
                'edit_required' => 0,
                'is_exportable' => 1,
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 1,// 是否可隐藏，1可以，0否
                'order' => 21,
                'app_order' => 21,
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 1,//是否可编辑：0-否，1-是
                'is_list' => 1,
                'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
            ],
            [
                'id'         => 'invoice_product_id',
                'name'       => '销售订单产品ID',
                'require'    => 0,
                'hint'       => '',
                'edit_hint'  => 0,//是否可编辑字段提示：0-否，1-是
                'group_id'   => CustomFieldService::PURCHASE_ORDER_GROUP_PRODUCT,
                'edit_required' => 0,
                'is_exportable' => 0,
                'is_list' => 1,
                'readonly' => 1, //只读字段
                'disable_flag'  => 1,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可隐藏，1可以，0否
                'order' => 22,
                'app_order' => 22,
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 0,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
            ],
        ];

        $systemFields[Constants::TYPE_OPPORTUNITY] = $opportunitySystemFields;
        $systemFields[Constants::TYPE_QUOTATION] = $quotationSystemFields;
        $systemFields[Constants::TYPE_PURCHASE_ORDER] = $purchaseSystemFields;

        foreach ($systemFields as $type => $systemField) {
            $this->syncSystemFields($type, $systemField);
        }

    }

    /**
     * 商机、报价单、采购订单添加sku_id自定义字段
     * @param int $clientId
     * @param int $grey
     * @param int $greyNum
     * @throws ProcessException
     */
    public function actionAddSkuIdCustomField($clientId = 0, $grey = 0, $greyNum = 0){
        $system_fields = [
            Constants::TYPE_OPPORTUNITY => [
                'id' => 'sku_id',
                'type' => Constants::TYPE_OPPORTUNITY,//商机
                'group_id' => CustomFieldService::OPPORTUNITY_GROUP_PRODUCT,
                'base' => 1,
                'name' => '商机-产品规格',
                'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                'require' => 0,
                'edit_required' => 1,
                'disable_flag' => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 1,// 是否可编辑，1可以，0否
                'default' => '',
                'edit_default' => 0,
                'hint' => '',
                'edit_hint' => 1,
                'is_exportable' => 0,
                'is_editable' => 1,
                'is_list' => 1,
                'columns' => 'sku_id',
                'relation_type' => 0,
                'relation_field' => '',
                'relation_field_type' => 0,
                'relation_field_name' => '',
                'export_scenario' => '',
                'export_group' => '',
                'readonly' => 0,
                'create_time' => date('Y-m-d H:i:s'),
                'update_time' => date('Y-m-d H:i:s')
            ],
            Constants::TYPE_QUOTATION => [
                'id' => 'sku_id',
                'type' => Constants::TYPE_QUOTATION,//报价单
                'group_id' => CustomFieldService::QUOTATION_GROUP_PRODUCT, // 交易分组
                'base' => 1,
                'name' => '产品规格',
                'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                'require' => 0,
                'edit_required' => 1,
                'disable_flag' => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 1,// 是否可编辑，1可以，0否
                'default' => '',
                'edit_default' => 0,
                'hint' => '',
                'edit_hint' => 1,
                'is_exportable' => 0,
                'is_editable' => 1,
                'is_list' => 1,
                'columns' => 'sku_id',
                'relation_type' => 0,
                'relation_field' => '',
                'relation_field_type' => 0,
                'relation_field_name' => '',
                'export_scenario' => '',
                'export_group' => '',
                'readonly' => 0,
                'create_time' => date('Y-m-d H:i:s'),
                'update_time' => date('Y-m-d H:i:s')
            ],
            Constants::TYPE_PURCHASE_ORDER => [
                'id' => 'sku_id',
                'type' => Constants::TYPE_PURCHASE_ORDER,//采购订单
                'group_id' => CustomFieldService::PURCHASE_ORDER_GROUP_PRODUCT, // 交易分组
                'base' => 1,
                'name' => '产品规格',
                'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                'require' => 0,
                'edit_required' => 0,
                'disable_flag' => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 1,// 是否可编辑，1可以，0否
                'default' => '',
                'edit_default' => 0,
                'hint' => '',
                'edit_hint' => 1,
                'is_exportable' => 0,
                'is_editable' => 1,
                'is_list' => 1,
                'columns' => 'sku_id',
                'relation_type' => 0,
                'relation_field' => '',
                'relation_field_type' => 0,
                'relation_field_name' => '',
                'export_scenario' => '',
                'export_group' => '',
                'readonly' => 0,
                'create_time' => date('Y-m-d H:i:s'),
                'update_time' => date('Y-m-d H:i:s')
            ],
            [
                'id'         => 'invoice_product_id',
                'type' => Constants::TYPE_PURCHASE_ORDER,//采购订单
                'group_id'   => CustomFieldService::PURCHASE_ORDER_GROUP_PRODUCT,
                'base' => 1,
                'name'       => '销售订单产品ID',
                'order' => 0,
                'app_order' => 0,
                'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
                'require' => 0,
                'edit_required' => 0,
                'disable_flag' => 1,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可编辑，1可以，0否
                'default' => '',
                'edit_default' => 0,
                'hint' => '',
                'edit_hint' => 0,
                'is_exportable' => 0,
                'is_editable' => 0,
                'is_list' => 1,
                'columns' => 'invoice_product_id',
                'relation_type' => 0,
                'relation_field' => '',
                'relation_field_type' => 0,
                'relation_field_name' => '',
                'export_scenario' => '',
                'export_group' => '',
                'readonly' => 1,
                'create_time' => date('Y-m-d H:i:s'),
                'update_time' => date('Y-m-d H:i:s')
            ],
        ];

        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        $beforeFields = [
            'sku_id' => 'product_model',
        ];

        foreach ($clientIds as $clientId) {
            // 字段排在product_model之后
            echo '---开始处理client_id：', $clientId, PHP_EOL;
            $db = \ProjectActiveRecord::getDbByClientId($clientId);
            if (!$db) {
                echo '--此client无效..'.PHP_EOL;
                continue;
            }

            $this->syncSystem2Customer($system_fields, $clientId);
            //处理字段排序
            echo '--开始处理新字段顺序'.PHP_EOL;
            foreach ($system_fields as $field) {
                $id = $field['id'];
                $type = $field['type'];
                $beforeId = $beforeFields[$id] ?? false;
                if (!$beforeId) {
                    continue;
                }
                $sorter = new \common\library\util\sort\Sorter(new \common\library\util\sort\CustomFieldSortRepository($clientId, $type));
                $sorter->setResort(true);
                $sorter->setId($id, true);
                $sorter->setSortField('order');
                $sorter->after($beforeId);
                $sorter->setSortField('app_order');
                $sorter->after($beforeId);
            }
            $this->syncSystem2FieldGroup($system_fields, $clientId);
            echo '成功处理client_id：', $clientId, PHP_EOL;
        }

    }

    /**
     * 同步报价单、采购订单的sku_id到导出在线字典表
     * @param int $clientId
     * @param int $grey
     * @param int $greyNum
     * @throws ProcessException
     */
    public function actionAddSkuIdExportSetting($clientId = 0, $grey = 0, $greyNum = 0){
        $fields = [
            Constants::TYPE_PURCHASE_ORDER => [
                'sku_id' =>
                    [
                        'export_scenario' => Constants::TYPE_PURCHASE_ORDER,
                        'export_group' => FieldExportService::PURCHASE_ORDER_GROUP_EXCHANGE_PRODUCT
                    ],
            ],
            //商品单页字段
            Constants::TYPE_QUOTATION => [
                'sku_id' => [
                    'export_scenario' => Constants::TYPE_QUOTATION,
                    'export_group' => FieldExportService::QUOTATION_GROUP_EXCHANGE_PRODUCT,
                ]
            ],
            Constants::TYPE_ORDER => [
                'sku_id' => [
                    'export_scenario' => Constants::TYPE_ORDER,
                    'export_group' => FieldExportService::ORDER_GROUP_EXCHANGE_PRODUCT,
                ]
            ],
        ];

        //更新需要导出的系统字段
        $map = [];
        foreach ($fields as $type =>  $field) {
            foreach ($field as $id =>$item) {
                $map[] = ['condition' => ['type' => $type, 'id' => $id],
                    'update' => ['export_scenario'=> (is_array($item['export_scenario']) ? implode(',', $item['export_scenario']) : $item['export_scenario']), 'export_group' => $item['export_group'],'is_exportable' => 1]];
            }
        }
        $admin_db = Yii::app()->db;
        echo "update db v4_admin" . PHP_EOL;
        foreach ($map as $item) {
            $criteria = new CDbCriteria();
            foreach ($item['condition'] as $key => $condition) {
                $criteria->addCondition("$key=:$key");
            }
            $criteria->params += $item['condition'];
            if (isset($item['update']['enable_flag']) && $item['update']['enable_flag'] == 0) {
                $result = $admin_db->getCommandBuilder()->createDeleteCommand('tbl_system_field', $criteria)->execute(); //删除系统字段
            } else {
                $result = $admin_db->getCommandBuilder()->createUpdateCommand('tbl_system_field', $item['update'], $criteria)->execute();
            }
            echo 'result: ' . $result . ', update tbl_system_field: ' . json_encode($item) . PHP_EOL;
        }


        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        $systemFields = [];
        $sql = "type in (".implode(',', array_keys($fields)).") and is_exportable = 1";
        $settings = \common\models\admin\SystemField::model()->findAll($sql);
        foreach ($settings as $setting) {
            $systemFields[] = $setting->getAttributes();
        }

        //todo：灰度手动添加字段
        $systemFields[] = json_decode('{
                                                                "id": "sku_id",
                                                                "type": "2",
                                                                "group_id": "2",
                                                                "base": "1",
                                                                "name": "产品规格",
                                                                "field_type": "3",
                                                                "require": "0",
                                                                "edit_required": "0",
                                                                "disable_flag": "0",
                                                                "edit_hide": "0",
                                                                "default": "",
                                                                "edit_default": "0",
                                                                "hint": "",
                                                                "edit_hint": "1",
                                                                "is_exportable": "1",
                                                                "is_editable": "1",
                                                                "is_list": "1",
                                                                "columns": "sku_id",
                                                                "relation_type": "0",
                                                                "relation_field": "",
                                                                "relation_field_type": "0",
                                                                "relation_field_name": "",
                                                                "export_scenario": "2",
                                                                "export_group": "6",
                                                                "order": "1996252404",
                                                                "app_order": "1996252404",
                                                                "readonly": "0",
                                                                "create_time": "2021-06-09 15:16:37",
                                                                "update_time": "2021-06-09 15:16:37",
                                                                "ext_info": "",
                                                                "unique_check": "0",
                                                                "unique_prevent": "1",
                                                                "unique_message": ""
                                                            }',true);

        $systemFields[] = json_decode('{
                                                                "id": "sku_id",
                                                                "type": "3",
                                                                "group_id": "2",
                                                                "base": "1",
                                                                "name": "产品规格",
                                                                "field_type": "3",
                                                                "require": "0",
                                                                "edit_required": "1",
                                                                "disable_flag": "0",
                                                                "edit_hide": "1",
                                                                "default": "",
                                                                "edit_default": "0",
                                                                "hint": "",
                                                                "edit_hint": "1",
                                                                "is_exportable": "1",
                                                                "is_editable": "1",
                                                                "is_list": "1",
                                                                "columns": "sku_id",
                                                                "relation_type": "0",
                                                                "relation_field": "",
                                                                "relation_field_type": "0",
                                                                "relation_field_name": "",
                                                                "export_scenario": "3",
                                                                "export_group": "6",
                                                                "order": "1854644969",
                                                                "app_order": "1903451416",
                                                                "readonly": "0",
                                                                "create_time": "2021-06-17 14:41:08",
                                                                "update_time": "2021-06-17 14:41:08",
                                                                "ext_info": "",
                                                                "unique_check": "0",
                                                                "unique_prevent": "1",
                                                                "unique_message": ""
                                                            }',true);
        $systemFields[] = json_decode('{
                                                        "id": "sku_id",
                                                        "type": "22",
                                                        "group_id": "2",
                                                        "base": "1",
                                                        "name": "产品规格",
                                                        "field_type": "3",
                                                        "require": "0",
                                                        "edit_required": "1",
                                                        "disable_flag": "0",
                                                        "edit_hide": "1",
                                                        "default": "",
                                                        "edit_default": "0",
                                                        "hint": "",
                                                        "edit_hint": "1",
                                                        "is_exportable": "1",
                                                        "is_editable": "1",
                                                        "is_list": "1",
                                                        "columns": "sku_id",
                                                        "relation_type": "0",
                                                        "relation_field": "",
                                                        "relation_field_type": "0",
                                                        "relation_field_name": "",
                                                        "export_scenario": "22",
                                                        "export_group": "5",
                                                        "order": "21",
                                                        "app_order": "21",
                                                        "readonly": "0",
                                                        "create_time": "2021-06-17 14:41:08",
                                                        "update_time": "2021-06-17 14:41:08",
                                                        "ext_info": "",
                                                        "unique_check": "0",
                                                        "unique_prevent": "1",
                                                        "unique_message": ""
                                                    }',true);

        foreach ($clientIds as $clientId) {
            $db = ProjectActiveRecord::getDbByClientId($clientId);
            if (!$db)
                continue;

            self::info("---开始处理client_id:$clientId");
            try {
                $this->syncSystem2ExportSetting($systemFields,$clientId);
                self::info("完成处理client_id:$clientId");
            } catch (Exception $e)
            {
                self::info($e->getMessage());
            }

        }


    }

    public function actionAmuRunner(){
        $clientIds = "430,3215,3892,4621,5254,5255,8115,8837,13333,14143,15783,15811,17017,18133,18352,18768,19020,23109,23454,24134,24472,25228,25229,25230,25457,25938,26874,27666,29748,30629,31289,38131,41703,43168,43640,45576,48614";
        $this->actionAddSkuIdCustomField($clientIds);
        $this->actionAddSkuIdExportSetting($clientIds);
    }

    public function actionCashCollection($client_id, $grey = 0, $greyNum = null)
    {
        if ($client_id) {
            $clientIds = explode(',', $client_id);
        } elseif ($grey && !is_null($greyNum)) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        foreach ($clientIds as $clientId) {
            try {
                \User::setLoginUserById(PrivilegeService::getInstance($clientId)->getAdminUserId());
                self::info("[$clientId]: doing...");

                //同步字段
                echo '插入新增字段',PHP_EOL;
                //$this->actionSyncByClient($clientId);

                $this->actionInsertCashCollection($clientId);


                //修改amount 属性
                echo '修改amount is_editable属性',PHP_EOL;
                $updateSql = "update  tbl_custom_field set is_editable=0 where  id='amount' and  type=10 and  client_id={$clientId}";
                $db = \ProjectActiveRecord::getDbByClientId($clientId);
                $db->createCommand($updateSql)->execute();

                //修改refer_type 属性
                echo '修改refer_type require edit_required hide edit_hide属性',PHP_EOL;
                $updateSql = "update  tbl_custom_field set `require`=0,`edit_required`=0,`disable_flag`=0,`edit_hide`=0 where  id='refer_type' and  type=10 and  client_id={$clientId}";
                $db = \ProjectActiveRecord::getDbByClientId($clientId);
                $db->createCommand($updateSql)->execute();


                //修改收款银行name
                echo '修改收款银行name',PHP_EOL;
                $updateSql = "update  tbl_custom_field set name='资金账户' where  id='collecting_bank' and  type=10 and  client_id={$clientId}";
                $db = \ProjectActiveRecord::getDbByClientId($clientId);
                $db->createCommand($updateSql)->execute();


                //变更回款单列表字段默认选中
                echo '变更回款单列表字段默认选中',PHP_EOL;
                $sql = "SELECT user_id FROM tbl_user_info WHERE client_id=:client_id AND enable_flag=1";
                $userIds = \Yii::app()->account_base_db->createCommand($sql)->queryColumn([':client_id' => $clientId]);
                foreach ($userIds as  $userId) {

                    $setting = new \common\library\setting\user\UserSetting($clientId, $userId, 'cash.collection.list.field');
                    $data = $setting->getValue();


                    $fixed = [];
                    $noFixed = [];
                    foreach ($data as  $datum) {
                        if($datum['fixed']) {
                            $fixed[] = $datum;
                        }else{
                            $noFixed[] = $datum;
                        }
                    }
                    $value = [
                        [
                            'id' => 'trade_no',
                            'width' => 200,
                            'fixed' => 0
                        ],
                        [
                            'id' => 'real_amount',
                            'width' => 200,
                            'fixed' => 0
                        ],
                        [
                            'id' => 'bank_charge',
                            'width' => 200,
                            'fixed' => 0
                        ]
                    ];
                    $setting->value = array_merge($fixed,$value,$noFixed);
                    $setting->save();
                }


                //修复回款单顺序
                $this->actionFixCashCollectionOrder($clientId);


            } catch (\Exception $e) {
                self::info("error: {$e->getMessage()}");
            }
        }

        self::info("all done!!!");
    }

    public function actionAddLocalProductNoToOrder($clientId = 0, $grey = 0, $greyNum = 0){
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }


        $system_fields = [
            [
                'id' => 'local_product_no',
                'type' => '2',
                'group_id' => '2',
                'base' => '1',
                'name' => '匹配的本地产品',
                'field_type' => '1',
                'require' => '0',
                'edit_required' => '0',
                'disable_flag' => '0',
                'edit_hide' => '0',
                'default' => '',
                'edit_default' => '0',
                'hint' => '',
                'edit_hint' => '1',
                'is_exportable' => '1',
                'is_editable' => '0',
                'is_list' => '1',
                'columns' => 'local_product_no',
                'relation_type' => '0',
                'relation_field' => '',
                'relation_field_type' => '0',
                'relation_field_name' => '',
                'readonly' => '0',
                'ext_info' => '',
            ],
            [
                'id' => 'sku_attributes',
                'type' => '2',
                'group_id' => '2',
                'base' => '1',
                'name' => '平台产品规格详情',
                'field_type' => '0',
                'require' => '1',
                'edit_required' => '0',
                'disable_flag' => '1',
                'edit_hide' => '0',
                'default' => '',
                'edit_default' => '0',
                'hint' => '',
                'edit_hint' => '1',
                'is_exportable' => '0',
                'is_editable' => '0',
                'is_list' => '1',
                'columns' => 'sku_attributes',
                'relation_type' => '0',
                'relation_field' => '',
                'relation_field_type' => '0',
                'relation_field_name' => '',
                'readonly' => '0',
                'ext_info' => '',
            ],
            [
                'id' => 'platform_product_info',
                'type' => '2',
                'group_id' => '2',
                'base' => '1',
                'name' => '平台产品信息',
                'field_type' => '0',
                'require' => '1',
                'edit_required' => '0',
                'disable_flag' => '1',
                'edit_hide' => '0',
                'default' => '0',
                'edit_default' => '0',
                'hint' => '',
                'edit_hint' => '1',
                'is_exportable' => '0',
                'is_editable' => '0',
                'is_list' => '1',
                'columns' => 'platform_product_info',
                'relation_type' => '0',
                'relation_field' => '',
                'relation_field_type' => '0',
                'relation_field_name' => '',
                'readonly' => '0',
                'ext_info' => '',
            ],
        ];
        $beforeFields = [
            'local_product_no' => 'product_name',
            'sku_attributes'=>'local_product_no',
            'platform_product_info' => 'sku_attributes',
        ];

        foreach ($clientIds as $clientId) {
            echo '---开始处理client_id：', $clientId, PHP_EOL;
            $db = \ProjectActiveRecord::getDbByClientId($clientId);
            if (!$db) {
                echo '--此client无效..'.PHP_EOL;
                continue;
            }

            $this->syncSystem2Customer($system_fields, $clientId);
            //处理字段排序
            echo '--开始处理新字段顺序'.PHP_EOL;
            foreach ($system_fields as $k => $field) {
                $id = $field['id'];
                $type = $field['type'];
                $beforeId = $beforeFields[$id] ?? false;
                if (!$beforeId) {
                    continue;
                }
                $sorter = new \common\library\util\sort\Sorter(new \common\library\util\sort\CustomFieldSortRepository($clientId, $type));
                $sorter->setResort(true);
                $sorter->setId($id, true);
                $sorter->setSortField('order');
                $orderNum = $sorter->after($beforeId);
                $sorter->setSortField('app_order');
                $appOrderNum = $sorter->after($beforeId);
                $system_fields[$k]['order'] = $orderNum;
                $system_fields[$k]['app_order'] = $appOrderNum;
            }
            $this->syncSystem2FieldGroup($system_fields, $clientId);
            echo '成功处理client_id：', $clientId, PHP_EOL;
        }
    }

    /* 设置新字段的order值在某字段之前，针对 tbl_custom_field 表；$isBefore = true 表示 $id 在 $compareId之前;
     * 请在插入新字段到tbl_custom_field后使用该方法；
    */
    protected function setNewFieldOrderBeforeOrAfter($id, $compareId, $clientId, $type, $isBefore){
        $sorter = new \common\library\util\sort\Sorter(new \common\library\util\sort\CustomFieldSortRepository($clientId, $type));
        $sorter->setResort(true);
        $sorter->setId($id, true);
        $sorter->setSortField('order');
        $isBefore ? $sorter->before($compareId) : $sorter->after($compareId);
        $sorter->setSortField('app_order');
        $isBefore ? $sorter->before($compareId) : $sorter->after($compareId);
    }

    public function actionAddPlatformSkuIdSystem(){
		$system_fields = [
			[
				'id' => 'local_product_no',
				'type' => '2',
				'group_id' => '2',
				'base' => '1',
				'name' => '匹配的本地产品',
				'field_type' => '1',
				'require' => '0',
				'edit_required' => '0',
				'disable_flag' => '0',
				'edit_hide' => '0',
				'default' => '',
				'edit_default' => '0',
				'hint' => '',
				'edit_hint' => '1',
				'is_exportable' => '1',
				'is_editable' => '0',
				'is_list' => '1',
				'columns' => 'local_product_no',
				'relation_type' => '0',
				'relation_field' => '',
				'relation_field_type' => '0',
				'relation_field_name' => '',
				'readonly' => '0',
				'ext_info' => '',
				'after' => 'name',
			],
            [
                'id' => 'sku_attributes',
                'type' => '2',
                'group_id' => '2',
                'base' => '1',
                'name' => '平台产品规格信息',
                'field_type' => '0',
                'require' => '1',
                'edit_required' => '0',
                'disable_flag' => '1',
                'edit_hide' => '0',
                'default' => '',
                'edit_default' => '0',
                'hint' => '',
                'edit_hint' => '1',
                'is_exportable' => '0',
                'is_editable' => '0',
                'is_list' => '1',
                'columns' => 'sku_attributes',
                'relation_type' => '0',
                'relation_field' => '',
                'relation_field_type' => '0',
                'relation_field_name' => '',
                'readonly' => '0',
                'ext_info' => '',
                'after' => 'name',

            ],
            [
                'id' => 'platform_product_info',
                'type' => '2',
                'group_id' => '2',
                'base' => '1',
                'name' => '平台产品信息',
                'field_type' => '0',
                'require' => '1',
                'edit_required' => '0',
                'disable_flag' => '1',
                'edit_hide' => '0',
                'default' => '0',
                'edit_default' => '0',
                'hint' => '',
                'edit_hint' => '1',
                'is_exportable' => '0',
                'is_editable' => '0',
                'is_list' => '1',
                'columns' => 'platform_product_info',
                'relation_type' => '0',
                'relation_field' => '',
                'relation_field_type' => '0',
                'relation_field_name' => '',
                'readonly' => '0',
                'ext_info' => '',
                'after' => 'name',
            ],
		];
		$this->syncSystemFields(Constants::TYPE_ORDER, $system_fields);

	}

    //更新平台产品系统字段信息
    public function actionSycnPlatformProductSystemField()
    {
        $db = \Yii::app()->db;

        $type = 27; //平台产品

        $date = date('Y-m-d H:i:s');

        //系统表
        $insertSystemSql = "insert into tbl_system_field (`id`,`type`,`group_id`,`base`,`name`,`field_type`,`columns`,`order`,`app_order`,`create_time`,`update_time`) VALUES 
                ('is_match',{$type},1,1,'匹配状态',5,'is_match',1,1,'{$date}','{$date}'),
                ('local_sku',{$type},1,1,'匹配的本地产品',1,'local_sku',2,2,'{$date}','{$date}'),
                ('sync_time',{$type},1,1,'最近同步时间',4,'sync_time',3,3,'{$date}','{$date}'),
                ('third_sku_code',{$type},1,1,'平台SKU编码',1,'third_sku_code',4,4,'{$date}','{$date}'),
                ('attributes_info',{$type},1,1,'平台产品规格',1,'attributes_info',5,5,'{$date}','{$date}'),
                ('store_name',{$type},1,1,'平台店铺',1,'store_name',6,6,'{$date}','{$date}'),
                ('third_product_id',{$type},1,1,'平台产品名称与 ID',1,'third_product_id',7,7,'{$date}','{$date}'),
                ('product_image',{$type},1,1,'产品主图',6,'product_image',8,8,'{$date}','{$date}')
                ON DUPLICATE KEY UPDATE update_time=VALUES(update_time)";

        $db->createCommand($insertSystemSql)->execute();

    }

    //更新平台产品表头字段信息
    public function actionSycnPlatformProductCustomField($client_id,$lastNumber=null)
    {
        if( strpos($client_id, ',') !== false )
        {
            $client_id = explode(',', $client_id);
        }

        $clientList = $this->getClientList($client_id,false, null,null,0,0,$lastNumber);

        $db = \Yii::app()->db;

        $type = 27; //平台产品

        $date = date('Y-m-d H:i:s');

        foreach ($clientList as $client)
        {
            $clientDb = ProjectActiveRecord::getDbByClientId($client['client_id']);

            if (!$clientDb)
                continue;

            $existSql = "select client_id from tbl_custom_field where client_id = :client_id and type = :type and base = :base limit 1";


            $exist = $clientDb->createCommand($existSql)->queryColumn([
                ':client_id' => $client['client_id'],
                ':type' => $type,
                ':base' => 1
            ]);

            if (!empty($exist))
                continue;

            $systemSql = "select * from tbl_system_field where type = :type";

            $systemFields = $db->createCommand($systemSql)->queryAll(true,[':type' => $type]);

            $insertFieldSql = "insert into tbl_custom_field (`enable_flag`,`client_id`,`id`,`type`,`base`,`name`,`group_id`,`field_type`,`ext_info`
,`require`,`edit_required`,`disable_flag`,`edit_hide`,`default`,`edit_default`,`hint`,`edit_hint`,`is_editable`,`is_list`,`columns`,`order`,
                              `app_order`,`readonly`,`unique_check`,`unique_prevent`,`unique_message`,`create_time`,`update_time`) VALUES ";

            $insertGroupSql = "insert into tbl_field_group (`client_id`,`type`,`id`,`group_id`,`create_time`,`update_time`) VALUES ";

            $sqlFieldArr = $sqlGroupArr = [];

            foreach ($systemFields as $field) {

                $columns = json_encode(explode(',', $field['columns']));

                $sqlFieldArr[] = "(1,{$client['client_id']},'{$field['id']}',{$field['type']},{$field['base']},'{$field['name']}','{$field['group_id']}',{$field['field_type']},'{$field['ext_info']}',
                    {$field['require']},{$field['edit_required']},{$field['disable_flag']},{$field['edit_hide']},'{$field['default']}',{$field['edit_default']},'{$field['hint']}',
                    {$field['edit_hint']},{$field['is_editable']},{$field['is_list']},'{$columns}',{$field['order']},{$field['app_order']},{$field['readonly']},{$field['unique_check']},
                    {$field['unique_prevent']},'{$field['unique_message']}','{$date}','{$date}')";

                foreach (explode(',', $field['group_id']) as $groupId) {
                    $sqlGroupArr[] = "({$client['client_id']},{$field['type']},'{$field['id']}',{$groupId},'{$date}','{$date}')";
                }

            }

            $insertFieldSql .= implode(',', $sqlFieldArr);

            $insertGroupSql .= implode(',', $sqlGroupArr);

            $transaction = $clientDb->beginTransaction();

            try {
                $clientDb->createCommand($insertFieldSql)->execute();

                $clientDb->createCommand($insertGroupSql)->execute();

                $transaction->commit();

                self::info("client_id: {$client['client_id']}自定义字段初始化成功");
            } catch (Exception $exception) {
                $transaction->rollback();
                self::info("client_id: {$client['client_id']}自定义字段初始化失败");
                throw $exception;
            }

        }

    }


    public function actionInsertCashCollection($clientId)
    {
        $insertConfig = [
            [
                'id'         => 'real_amount',
                'name'       => '实到账金额',
                'require'    => 1,
                'hint'       => '',
                'edit_hint'  => 1,
                'group_id'   => 1,
                'edit_required' => 0,
                'is_exportable' => 0,
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可编辑，1可以，0否
                'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                'default' => 0,
                'ext_info' => '[2]',
                'type' => '10',
                'base' => '1',
                'edit_default' => '0',
                'is_editable' => '1',
                'is_list' => '0',
                'columns' => 'real_amount',
                'relation_type' => '0',
                'relation_field' => '',
                'relation_field_type' => '0',
                'relation_field_name' => '',
                'readonly' => '0',
            ],
            [
                'id'         => 'bank_charge',
                'name'       => '手续费',
                'require'    => 0,
                'hint'       => '手续费',
                'edit_hint'  => 1,
                'group_id'   => 1,
                'edit_required' => 1,
                'is_exportable' => 0,
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 1,// 是否可编辑，1可以，0否
                'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                'default' => 0,
                'ext_info' => '[2]',
                'type' => '10',
                'base' => '1',
                'edit_default' => '0',
                'is_editable' => '1',
                'is_list' => '0',
                'columns' => 'bank_charge',
                'relation_type' => '0',
                'relation_field' => '',
                'relation_field_type' => '0',
                'relation_field_name' => '',
                'readonly' => '0',
            ],
            [
                'id'         => 'trade_no',
                'name'       => '交易号/票据号',
                'require'    => 0,
                'hint'       => '',
                'edit_hint'  => 1,
                'group_id'   => 1,
                'edit_required' => 1,
                'is_exportable' => 0,
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 1,// 是否可编辑，1可以，0否
                'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
                'default' => '',
                'ext_info' => '',
                'type' => '10',
                'base' => '1',
                'edit_default' => '0',
                'is_editable' => '1',
                'is_list' => '0',
                'columns' => 'trade_no',
                'relation_type' => '0',
                'relation_field' => '',
                'relation_field_type' => '0',
                'relation_field_name' => '',
                'readonly' => '0',
            ],
        ];

        $insertFieldSql = "insert ignore into tbl_custom_field (`enable_flag`,`client_id`,`id`,`type`,`base`,`name`,`group_id`,`field_type`,`ext_info`
,`require`,`edit_required`,`disable_flag`,`edit_hide`,`default`,`edit_default`,`hint`,`edit_hint`,`is_editable`,`is_list`,`columns`,
`readonly`,`create_time`,`update_time`) VALUES ";

        $insertGroupSql = "insert ignore into tbl_field_group (`client_id`,`type`,`id`,`group_id`,`create_time`,`update_time`) VALUES ";

        $sqlFieldArr = $sqlGroupArr = [];
        $date = xm_function_now();
        foreach ($insertConfig as $field) {

            $columns = json_encode(explode(',', $field['columns']));

            $sqlFieldArr[] = "(1,{$clientId},'{$field['id']}',{$field['type']},{$field['base']},'{$field['name']}','{$field['group_id']}',{$field['field_type']},'{$field['ext_info']}',{$field['require']},{$field['edit_required']},{$field['disable_flag']},{$field['edit_hide']},'{$field['default']}',{$field['edit_default']},'{$field['hint']}',{$field['edit_hint']},{$field['is_editable']},{$field['is_list']},'{$columns}',{$field['readonly']},'{$date}','{$date}')";

            foreach (explode(',', $field['group_id']) as $groupId) {
                $sqlGroupArr[] = "({$clientId},{$field['type']},'{$field['id']}',{$groupId},'{$date}','{$date}')";
            }

        }

        $insertFieldSql .= implode(',', $sqlFieldArr);

        $insertGroupSql .= implode(',', $sqlGroupArr);

       // echo $insertFieldSql,PHP_EOL;
       /// echo $insertGroupSql,PHP_EOL;

        $clientDb = ProjectActiveRecord::getDbByClientId($clientId);
        $transaction = $clientDb->beginTransaction();

        try {
            $clientDb->createCommand($insertFieldSql)->execute();

            $clientDb->createCommand($insertGroupSql)->execute();

            $transaction->commit();

            self::info("client_id: {$clientId}自定义字段初始化成功");
        } catch (Exception $exception) {
            $transaction->rollback();
            self::info("client_id: {$clientId}自定义字段初始化失败");
            throw $exception;
        }

    }


    public function actionFixCashCollectionOrder($clientId)
    {
        //需要重排的字段
        $needNewOrder = [
            'trade_no',
            'collection_date',
            'payee',
            'currency',
            'type',
            'collecting_bank',
            'real_amount',
            'bank_charge',
            'amount'
        ];

        //获取当前回款单所有字段
        $db = ProjectActiveRecord::getDbByClientId($clientId);
        $sql = "select  id,`order`,app_order from  tbl_custom_field where client_id={$clientId}  and  type=10 order by `order`,update_time";
        $data = $db->createCommand($sql)->queryAll();

        foreach ($data as $key => &$datum) {
            $datum['order'] =  $key+1;
            $datum['app_order'] =  $key+1;
        }
        unset($datum);

        $order = [];
        $data = array_column($data,null,'id');
        foreach ($needNewOrder as  $item) {
            $order[] = $data[$item]['order'];
        }

        rsort($order);
        foreach ($needNewOrder as  $key=>$datum) {
            $data[$datum]['order'] = $order[$key];
            $data[$datum]['app_order'] = $order[$key];
        }

        $sql4Order = "update tbl_custom_field set `order`= case `id` ";
        $sql4AppOrder = "update tbl_custom_field set `app_order`= case `id` ";
        $whenOrder = '';
        $whenAppOrder = '';
        $idString = [];
        foreach ($data as  $datum) {
            $column = is_numeric($datum['id']) ? $datum['id'] : "'".$datum['id']."'";

            $whenOrder .= " WHEN $column THEN {$datum['order']}";
            $whenAppOrder .= " WHEN $column THEN {$datum['app_order']}";
            $idString[] = "'".$datum['id']."'";
        }

        $sql4Order .= $whenOrder.' END where `id` IN('.implode(',',$idString).") AND client_id={$clientId} AND type=10";
        $sql4AppOrder .= $whenAppOrder.' END where `id` IN('.implode(',',$idString).") AND client_id={$clientId} AND type=10";
        $sql = $sql4Order.';'.$sql4AppOrder;
        //echo  $sql;
        $db->createCommand($sql)->execute();
    }

    /**
     * 采购订单 供应商 库存单据  修改人 修改时间字段 统一
     */
    public function actionOmsUpdateFieldUpdate()
    {
        $updateMap = [
            'update_user' => '修改人',
            'update_time' => '修改时间',
            'modifier' => '修改人',
        ];
        $typeArr = [
            Constants::TYPE_PURCHASE_ORDER,
            Constants::TYPE_SUPPLIER,
            Constants::TYPE_PURCHASE_INBOUND_INVOICE,
            Constants::TYPE_OTHER_INBOUND_INVOICE,
            Constants::TYPE_SALE_OUTBOUND_INVOICE,
            Constants::TYPE_OTHER_OUTBOUND_INVOICE,
            Constants::TYPE_PURCHASE_RETURN_INVOICE,
        ];
        $typeString = implode(',', $typeArr);
        $idString="'" . implode('\',\'', array_keys($updateMap)) . "'";

        //获取相关字段
        $clientSql = "UPDATE tbl_custom_field SET `name`= case `id` ";
        $systemSql = "UPDATE tbl_system_field SET `name`= case `id` ";
        $when = '';

        foreach ($updateMap as $updateKey => $updateValue) {
            $when .= " WHEN '{$updateKey}' THEN '{$updateValue}'";
        }
        $clientSql .= $when . " END where `id` IN($idString) AND type IN({$typeString})";
        $systemSql .= $when . " END where `id` IN($idString) AND type IN({$typeString})";

        //修复admin表
        echo  'fix admin  system ...',PHP_EOL;
        \Yii::app()->getDb()->createCommand($systemSql)->execute();
        echo  'admin fixed ..',PHP_EOL;

        $dbList = $this->dbSetList(1);
        $dbNum = 0;
        $dbCount = count($dbList);
        echo  'to do ',$dbCount,'...',PHP_EOL;
        foreach ($dbList as $dbItem)
        {
            $dbNum++;
            $setId = $dbItem['set_id'];
            echo 'doing  set id ',$setId,PHP_EOL;
            $db = \ProjectActiveRecord::getDbByDbSetId($setId);
            $rows = $db->createCommand($clientSql)->execute();
            echo  'affected rows ',$rows,PHP_EOL;
        }
        echo 'all '.$dbNum.'done ! ';
    }

    /**
     * 更新销售订单-产品信息 【编号】(显示、不可编辑提示) --全量
     */
    public function actionFixOrderProductNoField()
    {
        //修复admin表
        echo  'fix admin  system ...',PHP_EOL;
        $sql = "UPDATE tbl_system_field SET `is_editable`=1 ,`edit_hint`=0 WHERE id='product_no' AND type=2";
        \Yii::app()->getDb()->createCommand($sql)->execute();
        echo  'admin fixed ..',PHP_EOL;

        $dbList = $this->dbSetList(1);

        $dbNum = 0;
        $dbCount = count($dbList);
        echo  'to do ',$dbCount,'...',PHP_EOL;
        foreach ($dbList as $dbItem)
        {
            $dbNum++;
            $setId = $dbItem['set_id'];
            echo 'doing  set id ',$setId,PHP_EOL;
            $db = \ProjectActiveRecord::getDbByDbSetId($setId);
            $sql = "update tbl_custom_field set `is_editable`=1 ,`edit_hint`=0 where id='product_no' and type=2";
            $rows = $db->createCommand($sql)->execute();
            echo  'affected rows ',$rows,PHP_EOL;
        }
        echo 'all '.$dbNum.'done ! ';
    }

    /**
     * 隐藏用户销售订单-费用信息-自定义字段
     */
    public function actionHideOrderFeeField()
    {
        $dbList = $this->dbSetList(1,null);

        $dbCount = count($dbList);
        echo  'to do ',$dbCount,'...',PHP_EOL;
        foreach ($dbList as $dbItem)
        {
            $setId = $dbItem['set_id'];
            echo 'doing  set id ',$setId,PHP_EOL;
            $db = \ProjectActiveRecord::getDbByDbSetId($setId);

            $selectSql = "select id from tbl_custom_field where  type=2 and group_id=3";
            $result = $db->createCommand($selectSql)->queryAll();
            $updateId = [];
            $updateRows = 0;
            foreach ($result as $item) {
                is_numeric($item['id']) && $updateId[] = $item['id'];
            }

            if ($updateId) {
                $updateIds = implode(',', $updateId);
                self::info("set_id：{$setId} ----- updateIds :{$updateIds}");
                $updateSql = "update tbl_custom_field set enable_flag=0 where  type=2 and group_id=3 and id in ({$updateIds})";
                $updateRows = $db->createCommand($updateSql)->execute();
            }

            echo  'affected rows ',$updateRows,PHP_EOL;
        }

        self::info("all done!!!");
    }

    /**
     * 更新外贸文档system field
     * @return void
     * @throws \CDbException
     */
    public function actionSyncTradeDocSystemField()
    {
        $db = \Yii::app()->db;
        $type = Constants::TYPE_TRADE_DOCUMENT;
        $time = date('Y-m-d H:i:s');

        $insertSystemSql = "insert into tbl_system_field (`id`,`type`,`group_id`,`base`,`name`,`field_type`,`columns`,`order`,`app_order`,`create_time`,`update_time`) VALUES 
                ('name',{$type},1,1,'文档名称',1,'name',1,1,'{$time}','{$time}'),
                ('category',{$type},1,1,'文档用途',3,'category',2,2,'{$time}','{$time}'),
                ('owner',{$type},1,1,'文档归属',5,'owner',3,3,'{$time}','{$time}'),
                ('links',{$type},1,1,'应用销售环节',1,'links',4,4,'{$time}','{$time}'),
                ('customer',{$type},1,1,'关联客户',1,'customer',5,5,'{$time}','{$time}'),
                ('status',{$type},1,1,'状态',0,'status',6,6,'{$time}','{$time}'),
                ('is_accessible',{$type},1,1,'链接访问',3,'is_accessible',7,7,'{$time}','{$time}'),
                ('latest_track',{$type},1,1,'最近追踪记录',0,'latest_track',8,8,'{$time}','{$time}'),
                ('create_user',{$type},1,1,'创建人',0,'create_user',9,9,'{$time}','{$time}'),
                ('update_user',{$type},1,1,'更新人',0,'update_user',10,10,'{$time}','{$time}'),
                ('create_time',{$type},1,1,'创建时间',4,'create_time',11,11,'{$time}','{$time}'),
                ('update_time',{$type},1,1,'最近更新时间',4,'update_time',12,12,'{$time}','{$time}'),
                ('first_release_time',{$type},1,1,'首次发布时间',4,'first_release_time',13,13,'{$time}','{$time}'),
                ('latest_release_time',{$type},1,1,'最近发布时间',4,'latest_release_time',14,14,'{$time}','{$time}')
                ON DUPLICATE KEY UPDATE update_time=VALUES(update_time)";

        $db->createCommand($insertSystemSql)->execute();

    }

    /**
     * 根据system field更新custom field
     * @param $type
     * @param $client_id
     * @param $greyNum
     * @return void
     * @throws \CDbException
     * @throws \CException
     * @throws \ProcessException
     */
    public function actionSyncCustomFieldFromSystem($type, $client_id, $greyNum=null)
    {
        if( strpos($client_id, ',') !== false )
        {
            $client_id = explode(',', $client_id);
        }
        $dbSetId = 0;
        if (\Yii::app()->params['env'] == 'test')
            $dbSetId = 3;
        $greyNum = explode(',', $greyNum);
        $clientList = $this->getClientList($client_id,false,null,null, 0, 0, $greyNum, $dbSetId,null);

        $db = \Yii::app()->db;
        $date = date('Y-m-d H:i:s');

        foreach ($clientList as $client) {
            $clientDb = ProjectActiveRecord::getDbByClientId($client['client_id']);

            if (!$clientDb)
                continue;

            $existSql = 'select client_id from tbl_custom_field where client_id=:client_id and type=:type and base=:base limit 1';
            $exist = $clientDb->createCommand($existSql)->queryColumn([
                ':client_id' => $client['client_id'],
                ':type' => $type,
                ':base' => 1
            ]);

            if (!empty($exist))
                continue;

            $systemSql = 'select * from tbl_system_field where type=:type';
            $systemFields = $db->createCommand($systemSql)->queryAll(true,[':type' => $type]);
            $insertFieldSql = 'insert into tbl_custom_field (`enable_flag`,`client_id`,`id`,`type`,`base`,`name`,`group_id`,
                              `field_type`,`ext_info`,`require`,`edit_required`,`disable_flag`,`edit_hide`,`default`,
                              `edit_default`,`hint`,`edit_hint`,`is_editable`,`is_list`,`columns`,`order`,`app_order`,
                              `readonly`,`unique_check`,`unique_prevent`,`unique_message`,`create_time`,`update_time`) VALUES ';

            $insertGroupSql = 'insert into tbl_field_group (`client_id`,`type`,`id`,`group_id`,`create_time`,`update_time`) VALUES ';

            $sqlFieldArr = $sqlGroupArr = [];

            foreach ($systemFields as $field) {

                $columns = json_encode(explode(',', $field['columns']));

                $sqlFieldArr[] = "(1,{$client['client_id']},'{$field['id']}',{$field['type']},{$field['base']},'{$field['name']}','{$field['group_id']}',{$field['field_type']},'{$field['ext_info']}',
                    {$field['require']},{$field['edit_required']},{$field['disable_flag']},{$field['edit_hide']},'{$field['default']}',{$field['edit_default']},'{$field['hint']}',
                    {$field['edit_hint']},{$field['is_editable']},{$field['is_list']},'{$columns}',{$field['order']},{$field['app_order']},{$field['readonly']},{$field['unique_check']},
                    {$field['unique_prevent']},'{$field['unique_message']}','{$date}','{$date}')";

                foreach (explode(',', $field['group_id']) as $groupId) {
                    $sqlGroupArr[] = "({$client['client_id']},{$field['type']},'{$field['id']}',{$groupId},'{$date}','{$date}')";
                }

            }

            $insertFieldSql .= implode(',', $sqlFieldArr);
            $insertGroupSql .= implode(',', $sqlGroupArr);
            $transaction = $clientDb->beginTransaction();

            try {
                $clientDb->createCommand($insertFieldSql)->execute();
                $clientDb->createCommand($insertGroupSql)->execute();
                $transaction->commit();

                self::info("client_id: {$client['client_id']}自定义字段初始化成功");
            } catch (Exception $exception) {
                $transaction->rollback();
                self::info("client_id: {$client['client_id']}自定义字段初始化失败");
                throw $exception;
            }
        }
    }

    public function actionAddProductListFieldUserSetting($clientId, $newSettings){
        $key = 'product.list.field';
        if(!$clientId){
            return;
        }
        $newSettings = array_column($newSettings, null, 'id');
        $sql = "select client_id,user_id,value from tbl_user_setting where `key`='{$key}' and client_id={$clientId} and enable_flag=1";
        $clientDb = ProjectActiveRecord::getDbByClientId($clientId);
        $settings = $clientDb->createCommand($sql)->queryAll();
        $editSql = "update tbl_user_setting set value = case ";
        $cases = [];

        try{
            foreach($settings as $setting){
                if(!$setting['value']){
                    continue;
                }
                $settingVal = json_decode($setting['value'], true);
                if(!$settingVal || !is_array($settingVal)){
                    continue;
                }
                $settingValMap = array_column($settingVal, null, 'id');
                foreach($newSettings as $id => $newSetting){
                    if(!isset($settingValMap[$id])){
                        $settingVal[] = $newSetting;
                    }
                }
                $newSettingStr = json_encode($settingVal);
                $cases[] = "when user_id={$setting['user_id']} then '{$newSettingStr}'";
            }
            if(!$cases){
                return;
            }
            $editSql .= implode(' ', $cases);
            $editSql .= " end where client_id={$clientId} and `key`='{$key}'";

            echo "client：【{$clientId}】产品列表字段用户配置 更新/新增 配置：".json_encode($newSettings)."开始\n";
            $rows = $clientDb->createCommand($editSql)->execute();
            echo "client：【{$clientId}】产品列表字段用户配置更新完成, 影响用户行数{$rows}个\n";
        }catch (\Throwable $e){
            echo "更新/新增 用户配置{$key}失败，报错信息 File:".$e->getFile()." | Line:".$e->getLine()." | Msg:".$e->getMessage().PHP_EOL;
        }
    }
}

