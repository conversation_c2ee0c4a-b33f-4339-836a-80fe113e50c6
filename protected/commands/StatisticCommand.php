<?php
/**
 * Created by <PERSON><PERSON>Storm.
 * User: Administrator
 * Date: 14-12-19
 * Time: 下午2:57
 */
use common\components\MongoActiveRecord;
use common\library\account\service\DbService;
use common\library\ai\classify\ai_field_data\AIFieldData;
use common\library\performance_v2\rule\PerformanceV2Rule;
use common\library\performance_v2\rule\PerformanceV2RuleList;
use common\library\privilege_v2\ClientPermission;
use common\library\util\PgsqlUtil;
use common\library\util\Speed;

class StatisticCommand extends CrontabCommand
{
    /**
     * @deprecated 废弃, 迁至CustomerStatisticsTask
     * 客户统计调度脚本
     * 策略调整直接改这个类
     *
     */
    public function actionCustomerDispatcher($group_total = 0, $group_no = 0)
    {
        $clientList = $this->getClientList();
        $hosts = Yii::app()->account_base_db->createCommand("SELECT group_concat(set_id) as set_id,`host` FROM `tbl_db_set` where `type`=1 group by `host`")->queryAll(true);
        $setIds = [];
        if ($group_total != 0) {
            foreach ($hosts as $key => $host) {
                if ($key % $group_total == $group_no) {
                    $host['set_id'] = explode(',', $host['set_id']);
                    $setIds = array_merge($setIds, $host['set_id']);
                }
            }
            $clientList = array_filter($clientList, function ($client) use ($setIds) {
                return in_array($client->mysql_set_id, $setIds);
            });
        }

        foreach ($clientList as $client) {
            \common\library\CommandRunner::run(
                'statistic',
                'customer',
                [
                    'client_id' => $client->client_id,
                ],
                '/dev/null',
                0
            );
        }
    }

    /**
     * 单个公司的客户统计脚本
     * @param $client_id
     * @throws CDbException
     * @throws ProcessException
     */
    public function actionCustomer($client_id)
    {
        LogUtil::info('client_id' . $client_id . ' begin');

        $client = Client::findByClientId($client_id);
        if($client->mysql_set_id == 0){
            return;
        }

        $db = ProjectActiveRecord::getDbByClientId($client_id);
        $pg = PgActiveRecord::getDbByClientId($client_id);


        $data = array();
        $createCustomerData = array();
        $followCustomerData = array();
        $client_data = array();
        $now = time();
        $date = date('Y-m-d', $now - 86400);
        $now = strtotime($date);

        $dataReader = $pg->createCommand("select * from tbl_company where client_id=$client_id and is_archive=1")->query();
        $total_count = $pg->createCommand("select count(1) from tbl_company where client_id=$client_id and is_archive=1")->queryScalar();

        $client_data = array(
            ClientStatisticsExternalDay::TYPE_CUSTOMER_TOTAL_COUNT => array($total_count),
            ClientStatisticsExternalDay::TYPE_CUSTOMER_GROUP => array(),
            ClientStatisticsExternalDay::TYPE_CUSTOMER_STAR => array(),
            ClientStatisticsExternalDay::TYPE_CUSTOMER_STATUS => array(),
            ClientStatisticsExternalDay::TYPE_CUSTOMER_RECENT_TIME => array(),
            ClientStatisticsExternalDay::TYPE_CUSTOMER_COUNTRY => array(),
            ClientStatisticsExternalDay::TYPE_CUSTOMER_ORIGIN => array(),
        );

        $formatter = new \common\library\customer\CompanyFormatter($client_id);


        foreach ($dataReader as $row) {
            $elem = $formatter->strip($row);

            $star = empty($elem['star']) ? 0 : $elem['star'];
            if (!array_key_exists($star, $client_data[ClientStatisticsExternalDay::TYPE_CUSTOMER_STAR]))
                $client_data[ClientStatisticsExternalDay::TYPE_CUSTOMER_STAR][$star] = 0;
            $client_data[ClientStatisticsExternalDay::TYPE_CUSTOMER_STAR][$star]++;

            $groupId = empty($elem['group_id']) ? 0 : $elem['group_id'];
            if (!array_key_exists($groupId, $client_data[ClientStatisticsExternalDay::TYPE_CUSTOMER_GROUP]))
                $client_data[ClientStatisticsExternalDay::TYPE_CUSTOMER_GROUP][$groupId] = 0;
            $client_data[ClientStatisticsExternalDay::TYPE_CUSTOMER_GROUP][$groupId]++;

            $status = empty($elem['trail_status']) ? 0 : $elem['trail_status'];
            if (!array_key_exists($status, $client_data[ClientStatisticsExternalDay::TYPE_CUSTOMER_STATUS]))
                $client_data[ClientStatisticsExternalDay::TYPE_CUSTOMER_STATUS][$status] = 0;
            $client_data[ClientStatisticsExternalDay::TYPE_CUSTOMER_STATUS][$status]++;

            $recent_time = strtotime($elem['order_time']);

            $recent_time = ($now - $recent_time) / 86400;

            if ($recent_time <= 7) {
                $recent_time = 7;
                if (!array_key_exists($recent_time, $client_data[ClientStatisticsExternalDay::TYPE_CUSTOMER_RECENT_TIME]))
                    $client_data[ClientStatisticsExternalDay::TYPE_CUSTOMER_RECENT_TIME][$recent_time] = 0;
                $client_data[ClientStatisticsExternalDay::TYPE_CUSTOMER_RECENT_TIME][$recent_time]++;
            }
            if ($recent_time <= 30) {
                $recent_time = 30;
                if (!array_key_exists($recent_time, $client_data[ClientStatisticsExternalDay::TYPE_CUSTOMER_RECENT_TIME]))
                    $client_data[ClientStatisticsExternalDay::TYPE_CUSTOMER_RECENT_TIME][$recent_time] = 0;
                $client_data[ClientStatisticsExternalDay::TYPE_CUSTOMER_RECENT_TIME][$recent_time]++;
            }
            if ($recent_time <= 90) {
                $recent_time = 90;
                if (!array_key_exists($recent_time, $client_data[ClientStatisticsExternalDay::TYPE_CUSTOMER_RECENT_TIME]))
                    $client_data[ClientStatisticsExternalDay::TYPE_CUSTOMER_RECENT_TIME][$recent_time] = 0;
                $client_data[ClientStatisticsExternalDay::TYPE_CUSTOMER_RECENT_TIME][$recent_time]++;
            }
            if ($recent_time <= 180) {
                $recent_time = 180;
                if (!array_key_exists($recent_time, $client_data[ClientStatisticsExternalDay::TYPE_CUSTOMER_RECENT_TIME]))
                    $client_data[ClientStatisticsExternalDay::TYPE_CUSTOMER_RECENT_TIME][$recent_time] = 0;
                $client_data[ClientStatisticsExternalDay::TYPE_CUSTOMER_RECENT_TIME][$recent_time]++;
            }
            if ($recent_time > 180) {
                $recent_time = 666666;
                if (!array_key_exists($recent_time, $client_data[ClientStatisticsExternalDay::TYPE_CUSTOMER_RECENT_TIME]))
                    $client_data[ClientStatisticsExternalDay::TYPE_CUSTOMER_RECENT_TIME][$recent_time] = 0;
                $client_data[ClientStatisticsExternalDay::TYPE_CUSTOMER_RECENT_TIME][$recent_time]++;
            }

            $country = empty($elem['country']) ? 0 : $elem['country'];
            if ((is_string($country) || is_integer($country)) && !array_key_exists($country, $client_data[ClientStatisticsExternalDay::TYPE_CUSTOMER_COUNTRY]))
                $client_data[ClientStatisticsExternalDay::TYPE_CUSTOMER_COUNTRY][$country] = 0;
            $client_data[ClientStatisticsExternalDay::TYPE_CUSTOMER_COUNTRY][$country]++;

            $origin = empty($elem['origin']) ? 0 : $elem['origin'];
            if (!array_key_exists($origin, $client_data[ClientStatisticsExternalDay::TYPE_CUSTOMER_ORIGIN]))
                $client_data[ClientStatisticsExternalDay::TYPE_CUSTOMER_ORIGIN][$origin] = 0;
            $client_data[ClientStatisticsExternalDay::TYPE_CUSTOMER_ORIGIN][$origin]++;


///////////////////////////////////////////
            $userList = $elem['user_id'];

            if (!is_array($userList)) {
                $userList = array($userList);
            }
            if (empty($userList)) {
                $userList = array(0);
            }

            foreach ($userList as $userId) {
                if (!array_key_exists($userId, $data))
                    $data[$userId] = array(
                        UserStatisticsExternal::TYPE_CUSTOMER_GROUP => array(),
                        UserStatisticsExternal::TYPE_CUSTOMER_STAR => array(),
                        UserStatisticsExternal::TYPE_CUSTOMER_STATUS => array(),
                        UserStatisticsExternal::TYPE_CUSTOMER_RECENT_TIME => array(),
                        UserStatisticsExternal::TYPE_CUSTOMER_COUNTRY => array(),
                        UserStatisticsExternal::TYPE_CUSTOMER_ORIGIN => array(),
                        UserStatisticsExternal::TYPE_CUSTOMER_SCORE => array(),
                        'total_count' => 0
                    );

                $data[$userId]['total_count']++;

                $star = empty($elem['star']) ? 0 : $elem['star'];
                if (!array_key_exists($star, $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_STAR]))
                    $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_STAR][$star] = 0;
                $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_STAR][$star]++;

                $groupId = empty($elem['group_id']) ? 0 : $elem['group_id'];
                if (!array_key_exists($groupId, $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_GROUP]))
                    $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_GROUP][$groupId] = 0;
                $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_GROUP][$groupId]++;

                $status = empty($elem['trail_status']) ? 0 : $elem['trail_status'];
                if (!array_key_exists($status, $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_STATUS]))
                    $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_STATUS][$status] = 0;
                $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_STATUS][$status]++;

                if ($userId && isset($elem['user_data'][$userId]['order_time']))
                {
                    $time = strtotime($elem['user_data'][$userId]['order_time']);

                    $time = ($now - $time) / 86400;

                    if ($time <= 7) {
                        $time = 7;
                        if (!array_key_exists($time, $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_RECENT_TIME]))
                            $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_RECENT_TIME][$time] = 0;
                        $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_RECENT_TIME][$time]++;
                    }
                    if ($time <= 15) {
                        $time = 15;
                        if (!array_key_exists($time, $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_RECENT_TIME]))
                            $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_RECENT_TIME][$time] = 0;
                        $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_RECENT_TIME][$time]++;
                    }
                    if ($time <= 30) {
                        $time = 30;
                        if (!array_key_exists($time, $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_RECENT_TIME]))
                            $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_RECENT_TIME][$time] = 0;
                        $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_RECENT_TIME][$time]++;
                    }
                    if ($time <= 60) {
                        $time = 60;
                        if (!array_key_exists($time, $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_RECENT_TIME]))
                            $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_RECENT_TIME][$time] = 0;
                        $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_RECENT_TIME][$time]++;
                    }
                    if ($time <= 90) {
                        $time = 90;
                        if (!array_key_exists($time, $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_RECENT_TIME]))
                            $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_RECENT_TIME][$time] = 0;
                        $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_RECENT_TIME][$time]++;
                    }
                }

                $country = empty($elem['country']) ? 0 : $elem['country'];
                if ((is_string($country) || is_integer($country)) && !array_key_exists($country, $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_COUNTRY]))
                    $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_COUNTRY][$country] = 0;
                $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_COUNTRY][$country]++;

                $origin = empty($elem['origin']) ? 0 : $elem['origin'];
                if (!array_key_exists($origin, $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_ORIGIN]))
                    $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_ORIGIN][$origin] = 0;
                $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_ORIGIN][$origin]++;

                //客户评分
                $score = isset($elem['score']['user_total'][$userId]) ? $elem['score']['user_total'][$userId] : (isset($elem['score']['total']) ? $elem['score']['total'] : 0);

                if ($score <= 10) {
                    if (!array_key_exists(1, $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_SCORE]))
                        $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_SCORE][1] = 0;
                    $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_SCORE][1]++;
                }

                if ($score > 10 && $score <= 20) {
                    if (!array_key_exists(2, $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_SCORE]))
                        $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_SCORE][2] = 0;
                    $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_SCORE][2]++;
                }

                if ($score > 20 && $score <= 30) {
                    if (!array_key_exists(3, $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_SCORE]))
                        $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_SCORE][3] = 0;
                    $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_SCORE][3]++;
                }

                if ($score > 30 && $score <= 40) {
                    if (!array_key_exists(4, $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_SCORE]))
                        $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_SCORE][4] = 0;
                    $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_SCORE][4]++;
                }

                if ($score > 40 && $score <= 50) {
                    if (!array_key_exists(5, $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_SCORE]))
                        $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_SCORE][5] = 0;
                    $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_SCORE][5]++;
                }

                if ($score > 50 && $score <= 60) {
                    if (!array_key_exists(6, $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_SCORE]))
                        $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_SCORE][6] = 0;
                    $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_SCORE][6]++;
                }

                if ($score > 60 && $score <= 70) {
                    if (!array_key_exists(7, $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_SCORE]))
                        $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_SCORE][7] = 0;
                    $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_SCORE][7]++;
                }

                if ($score > 70 && $score <= 80) {
                    if (!array_key_exists(8, $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_SCORE]))
                        $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_SCORE][8] = 0;
                    $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_SCORE][8]++;
                }

                if ($score > 80 && $score <= 90) {
                    if (!array_key_exists(9, $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_SCORE]))
                        $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_SCORE][9] = 0;
                    $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_SCORE][9]++;
                }

                if ($score > 90 && $score <= 100) {
                    if (!array_key_exists(10, $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_SCORE]))
                        $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_SCORE][10] = 0;
                    $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_SCORE][10]++;
                }

            }


            $archiveDate = date('Y-m-d', strtotime($elem['archive_time'] ?? ''));

            $yesterDay = date('Y-m-d', strtotime('yesterday'));

            $followDay = date('Y-m-d', strtotime($elem['order_time'] ?? ''));

            foreach ($userList as $userId) {
                if (!array_key_exists($userId, $createCustomerData))
                    $createCustomerData[$userId] = array(
                        UserStatisticsExternal::TYPE_CREATE_CUSTOMER_GROUP => array(),
                        UserStatisticsExternal::TYPE_CREATE_CUSTOMER_STAR => array(),
                        UserStatisticsExternal::TYPE_CREATE_CUSTOMER_STATUS => array(),
                        UserStatisticsExternal::TYPE_CREATE_CUSTOMER_COUNTRY => array(),
                        UserStatisticsExternal::TYPE_CREATE_CUSTOMER_ORIGIN => array(),
                        UserStatisticsExternal::TYPE_CREATE_CUSTOMER_SCORE => array(),
                    );

                if (!isset($createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_GROUP][$archiveDate])) $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_GROUP][$archiveDate] = array();
                if (!isset($createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_STAR][$archiveDate])) $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_STAR][$archiveDate] = array();
                if (!isset($createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_STATUS][$archiveDate])) $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_STATUS][$archiveDate] = array();
                if (!isset($createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_COUNTRY][$archiveDate])) $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_COUNTRY][$archiveDate] = array();
                if (!isset($createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_ORIGIN][$archiveDate])) $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_ORIGIN][$archiveDate] = array();
                if (!isset($createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_SCORE][$archiveDate])) $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_SCORE][$archiveDate] = array();

                $star = empty($elem['star']) ? 0 : $elem['star'];
                if (!array_key_exists($star, $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_STAR][$archiveDate]))
                    $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_STAR][$archiveDate][$star] = 0;
                $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_STAR][$archiveDate][$star]++;

                $groupId = empty($elem['group_id']) ? 0 : $elem['group_id'];
                if (!array_key_exists($groupId, $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_GROUP][$archiveDate]))
                    $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_GROUP][$archiveDate][$groupId] = 0;
                $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_GROUP][$archiveDate][$groupId]++;

                $status = empty($elem['trail_status']) ? 0 : $elem['trail_status'];
                if (!array_key_exists($status, $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_STATUS][$archiveDate]))
                    $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_STATUS][$archiveDate][$status] = 0;
                $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_STATUS][$archiveDate][$status]++;


                $country = empty($elem['country']) ? 0 : $elem['country'];
                if ((is_string($country) || is_integer($country)) && !array_key_exists($country, $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_COUNTRY][$archiveDate]))
                    $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_COUNTRY][$archiveDate][$country] = 0;
                $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_COUNTRY][$archiveDate][$country]++;

                $origin = empty($elem['origin']) ? 0 : $elem['origin'];
                if (!array_key_exists($origin, $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_ORIGIN][$archiveDate]))
                    $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_ORIGIN][$archiveDate][$origin] = 0;
                $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_ORIGIN][$archiveDate][$origin]++;

                $score = isset($elem['score']['user_total'][$userId]) ? $elem['score']['user_total'][$userId] : (isset($elem['score']['total']) ? $elem['score']['total'] : 0);

                if ($score <= 10) {
                    if (!array_key_exists(1, $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_SCORE][$archiveDate]))
                        $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_SCORE][$archiveDate][1] = 0;
                    $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_SCORE][$archiveDate][1]++;
                }

                if ($score > 10 && $score <= 20) {
                    if (!array_key_exists(2, $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_SCORE][$archiveDate]))
                        $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_SCORE][$archiveDate][2] = 0;
                    $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_SCORE][$archiveDate][2]++;
                }

                if ($score > 20 && $score <= 30) {
                    if (!array_key_exists(3, $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_SCORE][$archiveDate]))
                        $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_SCORE][$archiveDate][3] = 0;
                    $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_SCORE][$archiveDate][3]++;
                }

                if ($score > 30 && $score <= 40) {
                    if (!array_key_exists(4, $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_SCORE][$archiveDate]))
                        $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_SCORE][$archiveDate][4] = 0;
                    $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_SCORE][$archiveDate][4]++;
                }

                if ($score > 40 && $score <= 50) {
                    if (!array_key_exists(5, $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_SCORE][$archiveDate]))
                        $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_SCORE][$archiveDate][5] = 0;
                    $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_SCORE][$archiveDate][5]++;
                }

                if ($score > 50 && $score <= 60) {
                    if (!array_key_exists(6, $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_SCORE][$archiveDate]))
                        $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_SCORE][$archiveDate][6] = 0;
                    $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_SCORE][$archiveDate][6]++;
                }

                if ($score > 60 && $score <= 70) {
                    if (!array_key_exists(7, $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_SCORE][$archiveDate]))
                        $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_SCORE][$archiveDate][7] = 0;
                    $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_SCORE][$archiveDate][7]++;
                }

                if ($score > 70 && $score <= 80) {
                    if (!array_key_exists(8, $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_SCORE][$archiveDate]))
                        $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_SCORE][$archiveDate][8] = 0;
                    $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_SCORE][$archiveDate][8]++;
                }

                if ($score > 80 && $score <= 90) {
                    if (!array_key_exists(9, $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_SCORE][$archiveDate]))
                        $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_SCORE][$archiveDate][9] = 0;
                    $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_SCORE][$archiveDate][9]++;
                }

                if ($score > 90 && $score <= 100) {
                    if (!array_key_exists(10, $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_SCORE][$archiveDate]))
                        $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_SCORE][$archiveDate][10] = 0;
                    $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_SCORE][$archiveDate][10]++;
                }


                //目前只更新昨天的跟进客户统计
                if ($followDay == $yesterDay) {
                    if (!isset($followCustomerData[$userId][$yesterDay])) {
                        $followCustomerData[$userId][$yesterDay] = 0;
                    }
                    $followCustomerData[$userId][$yesterDay]++;
                }


            }


        }
        foreach ($client_data AS $type => $typeData) {
            foreach ($typeData AS $value => $count) {
                $value = Util::escapeDoubleQuoteSql($value);
                $sql = "INSERT INTO tbl_client_statistics_external_day (client_id,date,type,value,count)
VALUES ({$client_id},'$date',$type,'{$value}',{$count})
ON DUPLICATE KEY UPDATE count=count+{$count};";
                try {
                    $db->createCommand($sql)->execute();
                } catch (Exception $e) {
                    LogUtil::info($sql);
                    LogUtil::info("1mysql执行出错::" . $e->getMessage());
                }
            }
        }
        foreach ($data as $userId => $userData) {
            $sql = "INSERT INTO tbl_user_statistics_day (user_id,client_id,date,create_time,customer_total_count)
VALUES ({$userId},{$client_id},'$date','$date', {$userData['total_count']})
ON DUPLICATE KEY UPDATE customer_total_count=customer_total_count+{$userData['total_count']};";

            try {
                $db->createCommand($sql)->execute();
            } catch (Exception $e) {
                LogUtil::info($sql);
                LogUtil::info("2mysql执行出错::" . $e->getMessage());
            }

            unset($userData['total_count']);

            foreach ($userData as $type => $typeData) {


                foreach ($typeData as $value => $count) {
                    $value = Util::escapeDoubleQuoteSql($value);
                    $sql = "INSERT INTO tbl_user_statistics_external_day (user_id,client_id,date,type,value,count)
VALUES ({$userId},{$client_id},'$date',$type,'{$value}',{$count})
ON DUPLICATE KEY UPDATE count=count+{$count};";
                    try {
                        $db->createCommand($sql)->execute();
                    } catch (Exception $e) {
                        LogUtil::info($sql);
                        LogUtil::info("3mysql执行出错::" . $e->getMessage());
                    }
                }

            }
        }


        //更新昨天跟进客户数
        $sqlArr = array();
        foreach ($followCustomerData as $userId => $userData) {
            foreach ($userData as $day => $dataCount) {

                $sqlArr[] = "INSERT INTO tbl_user_statistics_day (user_id,client_id,date,create_time,follow_customer_count)
VALUES ({$userId},{$client_id},'$day','$day', {$dataCount})
ON DUPLICATE KEY UPDATE follow_customer_count={$dataCount}";

            }
        }

        if (!empty($sqlArr)) {

            $sqlArr = array_chunk($sqlArr, 1000);
            foreach ($sqlArr as $sqlItem) {
                try {
                    $sql = implode(';', $sqlItem);
                    $db->createCommand($sql)->execute();
                } catch (Exception $e) {
                    LogUtil::info($sql);
                    LogUtil::info("5mysql执行出错::" . $e->getMessage());
                }
            }
        }


        foreach ($createCustomerData as $userId => $userData) {
            foreach ($userData as $type => $typeData) {

                foreach ($typeData as $day => $dataCount) {
                    $sqlArr = array();
                    foreach ($dataCount as $value => $count) {
                        $value = Util::escapeDoubleQuoteSql($value);
                        $sqlArr[] = "INSERT INTO tbl_user_statistics_external_day (user_id,client_id,date,type,value,count)
VALUES ({$userId},{$client_id},'$day',$type,'{$value}',{$count})
ON DUPLICATE KEY UPDATE count={$count}";

                    }

                    $sqlArr = array_chunk($sqlArr, 1000);
                    foreach ($sqlArr as $sqlItem) {
                        try {
                            $sql = implode(';', $sqlItem);
                            $db->createCommand($sql)->execute();
                        } catch (Exception $e) {
                            LogUtil::info($sql);
                            LogUtil::info("4mysql执行出错::" . $e->getMessage());
                        }
                    }

                }

            }
        }


        LogUtil::info('client_id' . $client_id . ' end');
    }

    /**
     * @param $client_id
     * @throws CDbException
     * @throws ProcessException
     */
    public function actionStatisticWorkReportByClientId($client_id)
    {
        $commentFunction = function ($c, $params, $date, $timeType, &$sqlValues) {
            $sql = "
select client_id, array_to_string(user_id, ',', '') as user_id, company_id
from tbl_company
where is_archive=1
      and order_time >= :start_time
      and order_time <= :end_time
      and client_id = :client_id
      and user_id <> '{}'
";

            $values = [];
            $companyList = $c->createCommand($sql)->query($params);
            foreach ($companyList as $company) {
                foreach (explode(',', $company['user_id']) as $userId) {
                    $values[$userId][] = $company;
                }
            }
            foreach ($values as $userId => $value) {
                if (empty($userId)) {
                    continue;
                }

                $companyIds = \common\library\util\PgsqlUtil::formatArray(array_column($value, 'company_id'));
                $sqlValues[] = "({$value[0]['client_id']}, {$userId}, {$date}, '{$timeType}', 'follow_customer_count', '{$companyIds}')";
            }
        };

        $c = \PgActiveRecord::getDbByClientId($client_id);

        list($lastMonthBegin, $lastMonthEnd) = \common\library\statistics\util\DateUtil::between('lastMonth');
        list($lastWeekBegin, $lastWeekEnd) = \common\library\statistics\util\DateUtil::between('lastWeek');
        list($yesterdayBegin, $yesterdayEnd) = \common\library\statistics\util\DateUtil::between('yesterday');

        $isStatisticLastWeek = $lastWeekEnd == $yesterdayEnd;
        $isStatisticLastMonth = $lastMonthEnd == $yesterdayEnd;
//        $isStatisticLastWeek = false;
//        $isStatisticLastMonth = false;
        $isStatisticLastYesterday = true;

        $sqlValues = [];
        if ($isStatisticLastMonth) {
            $params = [
                'start_time' => $lastMonthBegin . ' 00:00:00',
                'end_time' => $lastMonthEnd . ' 23:59:59',
                'client_id' => $client_id
            ];
            list($timeType, $date) = \common\library\statistics\CustomerHelper::timeTypeAndDateSwitch('lastMonth');
            $commentFunction($c, $params, $date, $timeType, $sqlValues);
        }

        if ($isStatisticLastWeek) {
            $params = [
                'start_time' => $lastWeekBegin . ' 00:00:00',
                'end_time' => $lastWeekEnd . ' 23:59:59',
                'client_id' => $client_id
            ];
            list($timeType, $date) = \common\library\statistics\CustomerHelper::timeTypeAndDateSwitch('lastWeek');
            $commentFunction($c,  $params, $date, $timeType, $sqlValues);
        }


        if ($isStatisticLastYesterday) {
            $params = [
                'start_time' => $yesterdayBegin . ' 00:00:00',
                'end_time' => $yesterdayEnd . ' 23:59:59',
                'client_id' => $client_id
            ];
            list($timeType, $date) = \common\library\statistics\CustomerHelper::timeTypeAndDateSwitch('yesterday');
            $commentFunction($c, $params, $date, $timeType, $sqlValues);
        }

        $date = date('Y-m-d H:i:s');
        if (! empty($sqlValues)) {
            $sqlValuesString = implode(',', $sqlValues);

            $insertSql = "
INSERT INTO tbl_statistic_work_report as t1 
(client_id, user_id, date, time_type, key, ids)
VALUES {$sqlValuesString}
ON CONFLICT (client_id, user_id, date, time_type, key) 
DO UPDATE SET ids=t1.ids
";
            try {
                $c->createCommand($insertSql)->execute();
            } catch (\Exception $e) {
                LogUtil::info("*失败 {$client_id}, {$date}" . $e->getMessage());
            }
            LogUtil::info("*完成 {$client_id}");
            echo "*完成 {$client_id}, {$date}\n";
        } else {
            LogUtil::info("*完成 {$client_id}, 没有新增");
            echo "*完成 {$client_id}, 没有新增 {$date}\n";
        }
    }

    /**
     * 统计工作台 昨天 上周 上月 跟进客户数和对应的id
     * @deprecated 废弃, 迁移至statisticWorkReportTask
     */
    public function actionStatisticWorkReport()
    {
        $dbs = self::dbSetList(3);
        foreach ($dbs as $db) {
            $clients = Yii::app()->account_base_db->createCommand("SELECT client_id FROM `tbl_client` where pgsql_set_id={$db['set_id']}")->queryAll(true);
            foreach ($clients as $client) {
                try {
                    $this->actionStatisticWorkReportByClientId($client['client_id']);
                } catch (\Exception $e) {
                    LogUtil::info("*失败 {$client['client_id']}" . $e->getMessage());
                }
            }
        }
    }

    public function actionFixOpportunityStageStatistics()
    {

        $dbs = self::dbSetList(3);
        $limit = 10000;

        foreach ($dbs as $db) {
            $c = PgActiveRecord::getDbByDbSetId($db['set_id']);
            $clients = Yii::app()->account_base_db->createCommand("SELECT client_id,mysql_set_id FROM `tbl_client` where pgsql_set_id={$db['set_id']}")->queryAll(true);
//            $clients = Yii::app()->account_base_db->createCommand("SELECT client_id,mysql_set_id FROM `tbl_client` where pgsql_set_id=10")->queryAll(true);
//            $c = PgActiveRecord::getDbByDbSetId(10);
            $opportunityIdsSql = "
select
 opportunity_id
from tbl_opportunity
where client_id=:client_id
offset :offset
limit :limit
";

            foreach ($clients as $client) {
                if ($client['mysql_set_id'] == 0) {
                    continue;
                }
                $values = [];
                $clientId = $client['client_id'];
                $allStageMap = \common\library\opportunity\stage\Helper::getStageListMap($clientId,[],0,[],"","",true);

                $opportunityIdsParams = [
                    'client_id' => $clientId,
                    'limit' => $limit
                ];

                $i = 0;
                while (true) {
                    $opportunityIdsParams['offset'] = $i * $limit; // todo 循环增加
                    $opportunityIds = $c->createCommand($opportunityIdsSql)->queryAll(true, $opportunityIdsParams);
                    if (empty($opportunityIds)) {
                        break;
                    }

                    $i++;
                    $where = implode(',', array_column($opportunityIds, 'opportunity_id'));

                    $mapSql = "
select *
from (
  select
    *,
    row_number()
    over (
      partition by opportunity_id
      order by create_time
    ) rank
  from (
    select
      opportunity_id,
      create_time,
      json_agg((statistics_id,stage_id) order by statistics_id desc) statistics_id_str
    from tbl_opportunity_stage_statistics
    where opportunity_id in ({$where}) and user_id = 0
    group by
      opportunity_id,
      create_time
    order by
    opportunity_id desc
  ) t1
  order by opportunity_id, create_time desc
) t1r 
left join (
  select
    *,
    row_number()
    over (
      partition by opportunity_id
      order by create_time ) rank
  from tbl_opportunity_history
  where type = 51 
    and opportunity_id in ({$where})
) t2r
  on t1r.opportunity_id = t2r.opportunity_id
    and t1r.rank = t2r.rank
    and t1r.create_time <= t2r.create_time + '1 Seconds'
    and t1r.create_time >= t2r.create_time - interval '1 Seconds'
";
                    $mapResult = $c->createCommand($mapSql)->query();
                    if (! empty($mapResult)) {
                        foreach ($mapResult as $mapItem) {
                            if (! $mapItem['opportunity_id']) {
                                continue;
                            }
                            echo "{$clientId} {$mapItem['opportunity_id']} {$mapItem['rank']}\n";
                            $statisticsArr = json_decode($mapItem['statistics_id_str'], true);
                            $diff = json_encode($mapItem['diff'], true);
                            foreach ($statisticsArr as $key => $statistics) {
                                if ($key == 0) {
                                    $oldStageId = empty($diff['old']) ? 0 : $diff['old'];
                                    $oldStageType = empty($diff['old']) ? 0 : $allStageMap[$diff['old']]['type'];
                                } else {
                                    $oldStageId = $statisticsArr[$key-1]['f2'];
                                    $oldStageType = $allStageMap[$statisticsArr[$key-1]['f2']]['type'];
                                }
                                $userId = $mapItem['update_user'];
                                $stageType = $allStageMap[$statistics['f2']]['type'];
                                $values[] = "({$statistics['f1']}, {$clientId}, {$userId}, {$stageType}, {$oldStageId}, {$oldStageType})";
                            }
                        }
                    }
                }
                if (empty($values)) {
                    echo "client_id: {$clientId} values 为空\n";
                    continue;
                }
                $valuesStr = implode(',', $values);
                $insertSql = "
insert into tbl_opportunity_stage_statistics as t1
(statistics_id,client_id, user_id, stage_type, old_stage_id, old_stage_type)
values {$valuesStr}
on conflict (statistics_id)
  do update set
    user_id=EXCLUDED.user_id,
    stage_type=EXCLUDED.stage_type,
    old_stage_type=EXCLUDED.old_stage_type,
    old_stage_id=EXCLUDED.old_stage_id
";
                $c->createCommand($insertSql)->execute();
            }
        }

    }


    /**
     * @param int $clientId
     * 客户自动化权限公司客户自动建档情况
     * 给运营导
     */
    public function actionCustomerStatistics($clientId = 0)
    {
        $clientList = $this->getClientList($clientId);

        $date = date('Y_m_d_H_i');
        $path = '/tmp/archive_statistics_'.$date.'.csv';
        $fp = fopen($path,'w');
        fputcsv($fp, ['client_id', '版本', '自动建档开关',
            '手动建档数量', '自动建档数量', '建档客户总量',
            '手动建档（已删除）','自动建档（已删除）',
            '手动建档（公海）','自动建档（公海）',
            '手动建档(更新)','自动建档（更新）',
            '5（手动）','5（自动）',//星级
            '4（手动）','4（自动）',
            '3（手动）','3（自动）',
            '2（手动）','2（自动）',
            '1（手动）','1（自动）',
            '0（手动）','0（自动）',
        ]);
        $systemMap = ['dx','crm','crm_plus','crm_with_dx_bundle','crm_smart','crm_pro','crm_lite'];

        foreach ($clientList as $client) {

            $clientId = $client['client_id'];

            if ($client['client_type'] != 1)
            {
                self::info("client_id:$clientId,client_type: {$client['client_type']} != 1 continue!!!!!!!!");
                continue;
            }

            LogUtil::info('client_id' . $clientId . ' begin');

            if (!\common\library\privilege_v3\PrivilegeService::getInstance($clientId)->hasFunctional(\common\library\privilege_v3\PrivilegeConstants::FUNCTIONAL_AI_CLASSIFY_CUSTOMER))
            {
                self::info('missing functional: crm.functional.ai.classify.customer');
                continue;
            }

            $adminUserId = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();
            if (empty($adminUserId))
            {
                self::info("empty admin_user_id,clientId:$clientId");
                continue;
            }

            $systems = PrivilegeClientSystem::getSystems($clientId);
            $systems = array_column($systems, 'system_id');
            $systems = array_intersect($systemMap, $systems);
            $systemString = implode(',', $systems);

            User::setLoginUserById($adminUserId);

            $pg = PgActiveRecord::getDbByClientId($clientId);

            $applySetting = new \common\library\ai\classify\setting\ApplySettings($clientId);
            $customerEnableCreate = $applySetting->customer_enable_create ?? 0;

            $dataReader = $pg->createCommand("select archive_type,is_archive,user_id,order_time,archive_time,star from tbl_company where client_id=$clientId")->query();

            $totalCount = 0;
            $aiArchiveCount = 0;//AI建档总数
            $normalArchiveCount = 0;//手动建档总数
            $aiArchiveDelCount = 0;//AI建档已删除
            $normalArchiveDelCount = 0;//手动建档已删除
            $aiArchivePublicCount = 0;//AI建档--公海
            $normalArchivePublicCount = 0;//手动建档--公海
            $aiArchiveUpdateCount = 0;//AI建档--（更新：order_time>create_time）
            $normalArchiveUpdateCount = 0;//手动建档--（更新：order_time>create_time）

            //星级
            $ai5StarCount = 0;
            $ai4StarCount = 0;
            $ai3StarCount = 0;
            $ai2StarCount = 0;
            $ai1StarCount = 0;
            $ai0StarCount = 0;
            $normal5StarCount = 0;
            $normal4StarCount = 0;
            $normal3StarCount = 0;
            $normal2StarCount = 0;
            $normal1StarCount = 0;
            $normal0StarCount = 0;

            foreach ($dataReader as $row)
            {
                $totalCount++;
                if ($row['archive_type'] == \common\library\customer_v3\company\orm\Company::ARCHIVE_TYPE_AI)
                {
                    $aiArchiveCount++;
                    if (!$row['is_archive'])
                    {
                        $aiArchiveDelCount++;
                        continue;
                    }

                    if ($row['user_id'] == '{}') $aiArchivePublicCount++;
                    if ($row['order_time'] > $row['archive_time']) $aiArchiveUpdateCount++;

                    $starCount = 'ai' . (intval($row['star'])) . 'StarCount';
                    $$starCount++;
                } else {
                    $normalArchiveCount++;
                    if (!$row['is_archive'])
                    {
                        $normalArchiveDelCount++;
                        continue;
                    }

                    if ($row['user_id'] == '{}') $normalArchivePublicCount++;
                    if ($row['order_time'] > $row['archive_time']) $normalArchiveUpdateCount++;

                    $starCount = 'normal' . (intval($row['star'])) . 'StarCount';
                    $$starCount++;
                }
            }

            fputcsv($fp, [$clientId, $systemString, $customerEnableCreate,
                $normalArchiveCount, $aiArchiveCount, $totalCount,
                $normalArchiveDelCount, $aiArchiveDelCount,
                $normalArchivePublicCount, $aiArchivePublicCount,
                $normalArchiveUpdateCount, $aiArchiveUpdateCount,
                $normal5StarCount,$ai5StarCount,
                $normal4StarCount,$ai4StarCount,
                $normal3StarCount,$ai3StarCount,
                $normal2StarCount,$ai2StarCount,
                $normal1StarCount,$ai1StarCount,
                $normal0StarCount,$ai0StarCount,
            ]);

        }

        fclose($fp);

        $content = file_get_contents($path);
        $content = mb_convert_encoding($content, 'GB2312', 'UTF-8');
        file_put_contents($path, $content);

        echo "$path \n";
    }


    /**
     * @param int $clientId
     * 商机自动化权限公司客户自动建档情况
     * 给运营导
     */
    public function actionOpportunityStatistic($clientId = 0)
    {
        LogUtil::info('client_id' . $clientId . ' begin');

        $clientList = $this->getClientList($clientId);

        $date = date('Y_m_d_H_i');
        $path = '/tmp/opportunity_statistics'.$date.'.csv';
        $fp = fopen($path, 'w');
        fputcsv($fp, ['client_id', 'crm_版本', '开启商机创建自动化', '开启商机更新自动化', '商机数', '赢单商机数', '商机数（自动创建）', '赢单商机数（自动创建）']);

        $systemMap = ['dx','crm','crm_plus','crm_with_dx_bundle','crm_smart','crm_pro','crm_lite'];

        foreach ($clientList as $client) {

            $clientId = $client['client_id'];

            if ($client['client_type'] != 1)
            {
                self::info("client_id:$clientId,client_type: {$client['client_type']} != 1 continue!!!!!!!!");
                continue;
            }

            LogUtil::info('client_id' . $clientId . ' begin');

            if (!\common\library\privilege_v3\PrivilegeService::getInstance($clientId)->hasFunctional(
                \common\library\privilege_v3\PrivilegeConstants::FUNCTIONAL_AI_CLASSIFY_OPPORTUNITY))
            {
                self::info('missing functional: crm.functional.ai.classify.opportunity');
                continue;
            }

            $adminUserId = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();
            if (empty($adminUserId)) {
                self::info("empty admin_user_id,clientId:$clientId");
                continue;
            }

            User::setLoginUserById($adminUserId);

            $systems = PrivilegeClientSystem::getSystems($clientId);
            $systems = array_column($systems,'system_id');

            $systems = array_intersect($systemMap, $systems);
            $systemString = implode(',',$systems);

            $applySetting = new \common\library\ai\classify\setting\ApplySettings($clientId);
            $opportunityEnableCreate = $applySetting->opportunity_enable_create ?? 0;
            $opportunityEnableUpdate = $applySetting->opportunity_enable_update ?? 0;

            $pg = PgActiveRecord::getDbByClientId($clientId);
            $dataReader = $pg->createCommand("select stage_type,create_type from tbl_opportunity where client_id=$clientId and enable_flag=1")->query();
            $totalCount = $pg->createCommand("select count(1) from tbl_opportunity where client_id=$clientId and enable_flag=1")->queryScalar();

            //创建
            $aiArchiveCount = 0;

            //赢单
            $stageWinCount = 0;
            $stageWinCountFromAiArchive = 0;

            foreach ($dataReader as $row)
            {
                if ($winStage = ($row['stage_type'] == \common\library\setting\library\stage\Stage::STAGE_WIN_STATUS))
                    $stageWinCount++;

                if ($row['create_type'] == \common\library\opportunity\Opportunity::CREATE_TYPE_SYSTEM)
                {
                    $aiArchiveCount++;
                    $winStage && $stageWinCountFromAiArchive++;
                }

            }
            fputcsv($fp, [$clientId, $systemString, $opportunityEnableCreate, $opportunityEnableUpdate, $totalCount, $stageWinCount, $aiArchiveCount, $stageWinCountFromAiArchive]);

        }

        fclose($fp);

        $content = file_get_contents($path);
        $content = mb_convert_encoding($content, 'GB2312', 'UTF-8');
        file_put_contents($path, $content);

        echo "$path \n";

    }

    /**
     * @param int $clientId
     * 产品自动化相关统计
     * 给产品导
     */
    public function actionClassifyProductStatistic($clientId = 0)
    {

        $checkStart = function ($date)
        {
           return $date > '2019-11-15 00:00:01';
        };


        $clientList = $this->getClientList($clientId);

        $date = date('Y_m_d_H_i');
        $path = '/tmp/classify_product_statistics'.$date.'.csv';
        $fp = fopen($path, 'w');
        fputcsv($fp, ['client_id', 'crm_版本', '开启商机创建自动化', '开启商机更新自动化', '开启商机产品自动化', '自动化创建产品数',
            '商机总数', '商机数（自动创建）', '商机数(手动关闭产品自动化的)', '赢单商机数-11.15至今', '赢单商机数（自动创建）-11.15至今', '赢单商机数（自动更新过产品）-11.15至今']);

        $systemMap = ['dx','crm','crm_plus','crm_with_dx_bundle','crm_smart','crm_pro','crm_lite'];

        foreach ($clientList as $client) {

            $clientId = $client['client_id'];

            if ($client['client_type'] != 1) {
                self::info("client_id:$clientId,client_type: {$client['client_type']} != 1 continue!!!!!!!!");
                continue;
            }

            LogUtil::info('client_id' . $clientId . ' begin');

            if (!\common\library\privilege_v3\PrivilegeService::getInstance($clientId)->hasFunctional(
                \common\library\privilege_v3\PrivilegeConstants::FUNCTIONAL_AI_CLASSIFY_OPPORTUNITY)
            ) {
                self::info('missing functional: crm.functional.ai.classify.opportunity');
                continue;
            }

            if (!\common\library\privilege_v3\PrivilegeService::getInstance($clientId)->hasFunctional(
                \common\library\privilege_v3\PrivilegeConstants::FUNCTIONAL_AI_CLASSIFY_PRODUCT)
            ) {
                self::info('missing functional: crm.functional.ai.classify.product');
                continue;
            }

            $adminUserId = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();
            if (empty($adminUserId)) {
                self::info("empty admin_user_id,clientId:$clientId");
                continue;
            }

            User::setLoginUserById($adminUserId);

            $systems = PrivilegeClientSystem::getSystems($clientId);
            $systems = array_column($systems, 'system_id');
            $systems = array_intersect($systemMap, $systems);
            $systemString = implode(',', $systems);

            $db = PgActiveRecord::getDbByClientId($clientId);

            //商机产品自动化开关
            $settings = new \common\library\ai\classify\setting\Settings($clientId);
            $productEnableCreate = $settings->getApplySettings()->opportunity_product_enable_update ?? 0;
            $opportunityEnableCreate = $settings->getApplySettings()->opportunity_enable_create ?? 0;
            $opportunityEnableUpdate = $settings->getApplySettings()->opportunity_enable_update ?? 0;

            //商机总数
            $opportunityTotal = 0;
            //AI创建商机数
            $opportunityAIArchiveCount = 0;
            //赢单商机数
            $stageWinCount = 0;
            $stageWinCountFromAiArchive = 0;
            $stageWinOpportunityIds = [];

            $dataReader = $db->createCommand("select opportunity_id, stage_type, create_type,succeed_time from tbl_opportunity where client_id=$clientId and enable_flag=1")->query();
            foreach ($dataReader as $row)
            {
                $opportunityTotal ++;

                if (($winStage = ($row['stage_type'] == \common\library\setting\library\stage\Stage::STAGE_WIN_STATUS)) && $checkStart($row['succeed_time']))
                {
                    $stageWinCount++;
                    $stageWinOpportunityIds[] = $row['opportunity_id'];
                }

                if ($row['create_type'] == \common\library\opportunity\Opportunity::CREATE_TYPE_SYSTEM)
                {
                    $opportunityAIArchiveCount++;
                    $checkStart($row['succeed_time']) && $winStage && $stageWinCountFromAiArchive++;
                }

            }

            //自动创建产品总数
            $sql = "select count(1) from tbl_product where client_id={$clientId} and create_type=2";
            $aiCreateProductCount = $db->createCommand($sql)->queryScalar();

            $disableAiProductOpportunityCount = 0;
            $stageWinCountHasUpdateProductCount = 0;
            if ($opportunityTotal > 0)
            {
                //关闭产品自动化商机数
                $sql = "select count(1) from tbl_opportunity_external as OE 
JOIN tbl_opportunity as O 
ON OE.opportunity_id = O.opportunity_id and O.enable_flag = 1 
where OE.client_id={$clientId} and OE.key = 'auto_update_product' and OE.value = '0'";
                $disableAiProductOpportunityCount = $db->createCommand($sql)->queryScalar();

                //有商机产品自动化结果的商机赢单的个数
                if (!empty($stageWinOpportunityIds))
                {
                    $stageWinOpportunityIdsString = implode(',', $stageWinOpportunityIds);
                    $sql = "select count(1) from tbl_opportunity_external where client_id={$clientId} and opportunity_id IN ($stageWinOpportunityIdsString) and key = 'auto_update_product_time' and value != ''";
                    $stageWinCountHasUpdateProductCount = $db->createCommand($sql)->queryScalar();
                }
            }

            fputcsv($fp, [$clientId, $systemString, $opportunityEnableCreate, $opportunityEnableUpdate, $productEnableCreate, $aiCreateProductCount,
                $opportunityTotal, $opportunityAIArchiveCount, $disableAiProductOpportunityCount, $stageWinCount, $stageWinCountFromAiArchive, $stageWinCountHasUpdateProductCount]);
        }

        fclose($fp);

        $content = file_get_contents($path);
        $content = mb_convert_encoding($content, 'GB2312', 'UTF-8');
        file_put_contents($path, $content);

        echo "$path \n";
    }

    public function actionClassifyEnableStatistic($clientId = 0)
    {
        LogUtil::info('client_id' . $clientId . ' begin');

        $clientList = $this->getClientList($clientId);

        $date = date('Y_m_d_H_i');
        $path = '/tmp/email_classify_enable_statistics'.$date.'.csv';
        $fp = fopen($path, 'w');
        fputcsv($fp, ['client_id', 'crm_版本', '是否自动化', '邮件自动化', '客户自动化', '商机自动化', '线索自动化','产品自动化']);

        $systemMap = ['dx','crm','crm_plus','crm_with_dx_bundle','crm_smart','crm_pro','crm_lite'];

        foreach ($clientList as $client) {

            $clientId = $client['client_id'];

            if ($client['client_type'] != 1) {
                self::info("client_id:$clientId,client_type: {$client['client_type']} != 1 continue!!!!!!!!");
                continue;
            }

            LogUtil::info('client_id' . $clientId . ' begin');

            if (!\common\library\privilege_v3\PrivilegeService::getInstance($clientId)->hasFunctional(
                \common\library\privilege_v3\PrivilegeConstants::FUNCTIONAL_AI_CLASSIFY_EMAIL)
            ) {
                self::info('missing functional: crm.functional.ai.classify.email');
                continue;
            }


            $adminUserId = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();
            if (empty($adminUserId)) {
                self::info("empty admin_user_id,clientId:$clientId");
                continue;
            }
            User::setLoginUserById($adminUserId);

            $systems = PrivilegeClientSystem::getSystems($clientId);
            $systems = array_column($systems, 'system_id');
            $systems = array_intersect($systemMap, $systems);
            $systemString = implode(',', $systems);

            //商机产品自动化开关
            $settings = new \common\library\ai\classify\setting\Settings($clientId);
            $enableApply = $settings->isEnableApply();
            $enableMailApply = $settings->isEnableMailApply();
            $enableCustomerApply = $settings->isEnableCustomerApply();
            $enableOpportunityApply = $settings->isEnableOpportunityApply();
            $enableLeadApply = $settings->isEnableLeadApply();
            $enableProductApply = $settings->isEnableProductApply();

            fputcsv($fp, [$clientId, $systemString,$enableApply,$enableMailApply,$enableCustomerApply,$enableOpportunityApply,$enableLeadApply,$enableProductApply]);
        }

        fclose($fp);
        $content = file_get_contents($path);
        $content = mb_convert_encoding($content, 'GB2312', 'UTF-8');
        file_put_contents($path, $content);

        echo "$path \n";
    }

    /**
     * @deprecated
     * 产品需求商机一次性统计
     */
    public function actionVersionOpportunityCount()
    {
        $dbs = \common\library\account\service\DbService::getDbList(DbSet::TYPE_PGSQL);

        $accountDb = Yii::app()->account_base_db;
        $versionSql = "SELECT client_id, system_id FROM tbl_privilege_client_system WHERE enable_flag = 1 AND system_id IN ('crm', 'crm_plus', 'crm_pro', 'crm_smart', 'crm_lite'); ";
        $clientVersion = array_column($accountDb->getCommandBuilder()->createSqlCommand($versionSql)->queryAll(), 'system_id', 'client_id');

        $result = [];
        $topCount = 20;
        $topData = [];
        foreach ($dbs as $dbSet) {
            $db = PgActiveRecord::getDbByDbSet($dbSet);
            if (!$db) {
                continue;
            }
            $sql = "select client_id,
                       count(1) as total,
                       sum(case when stage_type=2 then 1 else 0 end) as succeed,
                       sum(case when stage_type=3 then 1 else 0 end) as fail
                from tbl_opportunity where enable_flag = 1  group by client_id;";
            try {
                $clientData = $db->getCommandBuilder()->createSqlCommand($sql)->queryAll();
            } catch (\Exception $e) {
                $clientData = [];
            }
            foreach ($clientData as $clientDatum) {
                if (count($topData) < $topCount) {
                    $topData[$clientDatum['client_id']] = $clientDatum['total'];
                } else {
                    $minClient = array_search(min($topData), $topData);
                    if ($topData[$minClient] < $clientDatum['total']) {
                        unset($topData[$minClient]);
                        $topData[$clientDatum['client_id']] = $clientDatum['total'];
                    }
                }
                $version = $clientVersion[$clientDatum['client_id']] ?? 'none';
                if (!isset($result[$version])) {
                    $result[$version] = [
                        'version' => $version,
                        'total' => 0,
                        'fail' => 0,
                        'succeed' => 0,
                    ];
                }
                $result[$version]['total'] += $clientDatum['total'];
                $result[$version]['fail'] += $clientDatum['fail'];
                $result[$version]['succeed'] += $clientDatum['succeed'];
            }
        }

        arsort($topData);
        echo json_encode($result, JSON_PRETTY_PRINT);
        echo json_encode($topData, JSON_PRETTY_PRINT);
    }

    /**
     * @deprecated
     * 产品需求商机一次性统计
     */
    public function actionVersionUserOpportunityCount()
    {
        $dbs = \common\library\account\service\DbService::getDbList(DbSet::TYPE_PGSQL);

        $accountDb = Yii::app()->account_base_db;
        $versionSql = "SELECT client_id, system_id FROM tbl_privilege_client_system WHERE enable_flag = 1 AND system_id IN ('crm', 'crm_plus', 'crm_pro', 'crm_smart', 'crm_lite'); ";
        $clientVersion = array_column($accountDb->getCommandBuilder()->createSqlCommand($versionSql)->queryAll(), 'system_id', 'client_id');

        $result = [];
        foreach ($dbs as $dbSet) {
            $db = PgActiveRecord::getDbByDbSet($dbSet);
            if (!$db) {
                continue;
            }
            $sql = "select client_id,
                       create_user,
                       count(1) as total,
                       sum(case when stage_type=2 then 1 else 0 end) as succeed,
                       sum(case when stage_type=3 then 1 else 0 end) as fail
                from tbl_opportunity where enable_flag = 1 group by client_id, create_user;";
            try {
                $clientData = $db->getCommandBuilder()->createSqlCommand($sql)->queryAll();
            } catch (\Exception $e) {
                $clientData = [];
            }
            foreach ($clientData as $clientDatum) {
                $version = $clientVersion[$clientDatum['client_id']] ?? 'other';
                if (!isset($result[$version])) {
                    $result[$version] = [
                        'total' => [
                            50 => 0,
                            20 => 0,
                            10 => 0,
                            0 => 0,
                        ],
                        'succeed' => [
                            2 => 0,
                            0 => 0,
                        ],
                        'fail' => [
                            2 => 0,
                            0 => 0,
                        ],
                    ];
                }
                foreach ($result[$version] as $type => &$countMap) {
                    foreach ($countMap as $count => &$value) {
                        if ($clientDatum[$type] > $count) {
                            $value++;
                            break;
                        }
                    }
                }
            }
        }

        echo json_encode($result, JSON_PRETTY_PRINT);
    }

    /**
     * @deprecated
     * 产品需求商机一次性统计
     */
    public function actionVersionClientOpportunityCount()
    {
        $dbs = \common\library\account\service\DbService::getDbList(DbSet::TYPE_PGSQL);

        $accountDb = Yii::app()->account_base_db;
        $versionSql = "SELECT client_id, system_id FROM tbl_privilege_client_system WHERE enable_flag = 1 AND system_id IN ('crm', 'crm_plus', 'crm_pro', 'crm_smart', 'crm_lite'); ";
        $clientVersion = array_column($accountDb->getCommandBuilder()->createSqlCommand($versionSql)->queryAll(), 'system_id', 'client_id');

        $result = [];
        foreach ($dbs as $dbSet) {
            $db = PgActiveRecord::getDbByDbSet($dbSet);
            if (!$db) {
                continue;
            }
            $sql = "select client_id,
                       count(1) as total,
                       sum(case when stage_type=2 then 1 else 0 end) as succeed,
                       sum(case when stage_type=3 then 1 else 0 end) as fail
                from tbl_opportunity where enable_flag = 1 group by client_id;";
            try {
                $clientData = $db->getCommandBuilder()->createSqlCommand($sql)->queryAll();
            } catch (\Exception $e) {
                $clientData = [];
            }
            foreach ($clientData as $clientDatum) {
                $version = $clientVersion[$clientDatum['client_id']] ?? 'other';
                if (!isset($result[$version])) {
                    $result[$version] = [
                        'total' => [
                            100 => 0,
                            50 => 0,
                            10 => 0,
                            0 => 0,
                        ],
                        'succeed' => [
                            10 => 0,
                            0 => 0,
                        ],
                        'fail' => [
                            10 => 0,
                            0 => 0,
                        ],
                    ];
                }
                foreach ($result[$version] as $type => &$countMap) {
                    foreach ($countMap as $count => &$value) {
                        if ($clientDatum[$type] > $count) {
                            $value++;
                            break;
                        }
                    }
                }
            }
        }

        echo json_encode($result, JSON_PRETTY_PRINT);
    }

    public function actionLeadStatistic($clientId = 0)
    {
        LogUtil::info('client_id' . $clientId . ' begin');

        $clientList = $this->getClientList($clientId);

        $date = date('Y_m_d_H_i');
        $path = '/tmp/lead_company_email_statistics'.$date.'.csv';
        $fp = fopen($path, 'w');
        fputcsv($fp, ['client_id', '来源为询盘的线索数','客户数（来源为询盘的线索邮箱建档）']);


        $num = 1;
        $clientCount = count($clientList);
        foreach ($clientList as $client) {

            $clientId = $client['client_id'];
            echo "Client $clientId: $num/$clientCount \n";
            $num++;

            if ($client['client_type'] != 1) {
                self::info("client_id:$clientId,client_type: {$client['client_type']} != 1 continue!!!!!!!!");
                continue;
            }

            LogUtil::info('client_id' . $clientId . ' begin');

            $adminUserId = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();
            if (empty($adminUserId)) {
                self::info("empty admin_user_id,clientId:$clientId");
                continue;
            }
            User::setLoginUserById($adminUserId);

            $db = PgActiveRecord::getDbByClientId($clientId);

            //平台询盘
            $sql = "select b.lead_id, b.email from tbl_lead as a left join tbl_lead_customer as b on a.lead_id=b.lead_id where a.client_id = $clientId and a.is_archive=1 and a.origin IN (4,5,6,7)";
            $list = $db->createCommand($sql)->queryAll(true);

            if (empty($list))
                continue;

            $leadIds = [];
            $emailMap = [];
            foreach ($list as $item)
            {
                $leadIds[] = $item['lead_id'];
                $emailMap[] = $item['email'];
            }

            $leadIds = array_unique($leadIds);
            $emailMap = array_filter(array_unique($emailMap));

            $emailString = implode("','", $emailMap);
            $sql = "select  DISTINCT(company_id) from tbl_customer where client_id=$clientId and email IN ('$emailString') and is_archive = 1";
            $companyIds = $db->createCommand($sql)->queryColumn();

            fputcsv($fp, [$clientId, count($leadIds), count($companyIds)]);
        }
        fclose($fp);

        $content = file_get_contents($path);
        $content = mb_convert_encoding($content, 'GB2312', 'UTF-8');
        file_put_contents($path, $content);

        echo "$path \n";
    }

    public function actionExportCaptureFeedback()
    {
        LogUtil::info('Export Capture Feedback begin');

        $dbSetList = self::dbSetList(DbSet::TYPE_MYSQL);

        $date = date('Y_m_d_H_i');
        $path = '/tmp/capture_feedback'.$date.'.csv';
        $fp = fopen($path, 'w');
        fputcsv($fp, ['feedback_id', 'biz_id', 'biz_type', 'client_id', 'user_id', 'tag_no', 'data', 'display', 'create_time']);
        $limit = 2000;

        foreach ($dbSetList as $dbSet) {
            $db = \ProjectActiveRecord::getDbByDbSet($dbSet);
            if (!$db) continue;

            $offset = 0;
            while (true) {
                try {
                    $list = $db->createCommand("SELECT * FROM tbl_ai_capture_feedback WHERE 1 limit {$limit} offset {$offset}")->queryAll(true);
                } catch (Exception $e) {
                    break;
                }

                if (empty($list)) break;

                $offset += $limit;

                foreach ($list as $key => $item) {
                    $item['data'] = snappy_uncompress($item['data']);
                    $item['display'] = snappy_uncompress($item['display']);

                    fputcsv($fp, [$item['feedback_id'], $item['biz_id'], $item['biz_type'], $item['client_id'], $item['user_id'], $item['tag_no'], $item['data'] , $item['display'], $item['create_time']]);
                }
            }
        }

        fclose($fp);
        $content = file_get_contents($path);
        $content = mb_convert_encoding($content, 'GB2312', 'UTF-8');
        file_put_contents($path, $content);

        echo "$path \n";
    }

    /**
     * 重点资源统计表xs2支持根据月份查询
     */
    public function actionStatisticWorkReportForXs2()
    {
        ini_set("memory_limit", "1024M");

        // 一开始创建的PRO版本module为4，进行兼容
        $dbs = \common\library\account\service\DbService::getDbList(DbSet::TYPE_PGSQL);

        $dbNum = 1;
        $dbCount = count($dbs);
        foreach ($dbs as $db) {

            echo "{$db['set_id']}: $dbNum/$dbCount \n";

            $clients = Yii::app()->account_base_db->createCommand("SELECT client_id FROM `tbl_client` where pgsql_set_id={$db['set_id']}")->queryAll(true);
            $clientNum = 1;
            $clientCount = count($clients);
            foreach ($clients as $client) {

                echo "db: {$db['set_id']}: $dbNum/$dbCount----client:{$client['client_id']}: $clientNum/$clientCount \n";
                $clientNum++;

                try {
                    LogUtil::info("*开始 {$client['client_id']}");
                    \common\library\server\crontab\task\StatisticWorkTask::xs2ForLastMonth($client['client_id']);
//                    (new \common\library\queue_v2\job\crontab\StatisticsXS2ForLastMonth())->process($client['client_id']);
                } catch (\Exception $e) {
                    LogUtil::info("*失败 {$client['client_id']}" . $e->getMessage());
                } finally {
                    \common\library\account\Client::cleanCacheMap($client['client_id']);
                    PgActiveRecord::releaseDbByClientId($client['client_id']);
                }
            }

            $dbNum++;
        }
    }


	/**
	 * 创建客户数
	 * @param $clientId
	 * @param $userId
	 * @param $startDate
	 * @param $endDate
	 * @throws CDbException
	 * @throws CException
	 * @throws ProcessException
	 */
	public function actionFixErrorCompanyData($clientId, $userId, $startDate, $endDate) {

		//查询满足条件的数据
		$pgDb = PgActiveRecord::getDbByClientId($clientId);

		$sql = "SELECT t.* FROM tbl_company t where client_id ={$clientId} and create_time >= '{$startDate} 00:00:00' and create_time <= '{$endDate} 23:59:59' and create_user={$userId}";

		$data = $pgDb->createCommand($sql)->queryAll();


		//构建每天的数据id以及数量
		$companyDayIdArr = [];
		foreach ($data as $item) {

			$dayDate = date('Ymd', strtotime($item['create_time']));

			$companyDayIdArr[$dayDate][] = $item['company_id'];
		}

		//覆盖更新统计数据表
		$timeType = 'day';

		$key = 'customer_add_count';
		foreach ($companyDayIdArr as $date => $ids) {

			$count = count($ids);

			//插入日增加表s
			$ids = implode(',', $ids);

			$companyAddSql = "INSERT INTO tbl_statistic_work_report as t1 (client_id, user_id, date, time_type, key, ids)
VALUES ($clientId, $userId, '{$date}', '{$timeType}', '{$key}', '{{$ids}}')
ON CONFLICT (client_id, user_id, time_type, key, date) DO UPDATE SET ids=t1.ids||excluded.ids";

			$pgDb->createCommand($companyAddSql)->execute();

			\StatisticsService::companyAdd($clientId, $userId, $count, date('Y-m-d'));
		}

	}


	/**
	 * 按月重跑，有数据的跳过
	 * @param     $clientId
	 * @param int $month     年月，例：202101
	 * @param int $dbSetType 默认3，pg
	 * @throws CDbException
	 */
	public function actionFixXs2ByMonth($clientId, int $month = 0, int $dbSetType = \DbSet::TYPE_PGSQL) {

		$dbSet = DbService::getDbSet($clientId, $dbSetType, false);

		if (!$dbSet || !isset($dbSet['set_id'])) {

			self::info('empty' . json_encode($dbSet));

			return;
		}

		$clients = Client::findAllByDbSetId($dbSetType, $dbSet['set_id']);

		foreach ($clients as $client) {

			self::info('--START--clientId:'.$client->client_id);

			try {

				$this->xs2ByMonth($client->client_id, $month);

			} catch (Exception $exception) {

				self::info('--ERROR--clientId:'.$client->client_id.',exception trace:'.$exception->getTraceAsString());

				continue;
			}

			self::info('--END--clientId:'.$client->client_id);
		}
	}


	/**
	 *
	 * 重点资源统计表xs2支持根据月份查询,每月一号执行计算
	 *
	 * @param          $clientId
	 * @param int|null $month 传要跑的年月
	 * @throws CDbException
	 * @throws CException
	 * @throws ProcessException
	 */
	public function xs2ByMonth($clientId, int $month = null) {

		$db = \PgActiveRecord::getDbByClientId($clientId);

		if (empty($db)) {

			self::info('empty db:'.json_encode($db));
			return;
		}

		$month = $month ?: date('Ym');

		$createTime = date("Y-m-d", strtotime('last day of this month', strtotime($month . '01'))) . ' 23:59:59';

		$timeType = 'month';



		$sql = "SELECT user_id FROM tbl_user_info WHERE client_id=:client_id AND enable_flag=1";

		$userIds = \Yii::app()->account_base_db->createCommand($sql)->queryAll(true, [':client_id' => $clientId]);

		$userIds = implode(',', array_column($userIds, 'user_id'));

		if (empty($userIds)) {

			self::info('userid为空，return;');

			return;
		}


		$query = [
			'private_customer_count' => "select unnest(user_id) as statistic_user_id,company_id as id from tbl_company 
where client_id=:clientId and is_archive=1 and user_id != '{}' AND create_time <= '{$createTime}'",
			'lead_count' => "select unnest(user_id) as statistic_user_id,lead_id as id from tbl_lead 
where client_id=:clientId and is_archive=1 and user_id != '{}' AND create_time <= '{$createTime}'",
			'opportunity_count' => "select unnest(user_id) as statistic_user_id,opportunity_id as id from tbl_opportunity 
where client_id=:clientId and enable_flag=1  and user_id != '{}' AND create_time <= '{$createTime}'",
		];

		foreach ($query as $key => $sql)
		{
			$selectStatistic = 'SELECT count(*)
				FROM tbl_statistic_work_report
				WHERE client_id = :clientId
				  AND user_id in ('.$userIds.')
				  AND time_type = \'' . $timeType . '\'
				  AND key = \'' . $key . '\'
				  AND date = ' . $month;

			$statisticList = $db->createCommand($selectStatistic)->queryScalar([':clientId' => $clientId]);

//			跑过了，跳过
			if ($statisticList) {

				continue;
			}


			$list = $db->createCommand($sql)->query([':clientId' => $clientId]);

			$map = [];
			foreach ($list as $item)
			{
				$map[$item['statistic_user_id']][] = $item['id'];
			}

			$sqlValues = [];

			foreach ($map as $userId => $companyIds)
			{
				$companyIds = PgsqlUtil::formatArray($companyIds);
				$sqlValues[] = "({$clientId}, {$userId}, {$month}, '{$timeType}', '{$key}', '{$companyIds}')";
			}

			if (!empty($sqlValues))
			{
				$sqlValuesString = implode(',', $sqlValues);

				$insertSql = "
INSERT INTO tbl_statistic_work_report as t1 
(client_id, user_id, date, time_type, key, ids)
VALUES {$sqlValuesString}
ON CONFLICT (client_id, user_id, date, time_type, key) 
DO UPDATE SET ids=t1.ids";
				try {
					$db->createCommand($insertSql)->execute();
				} catch (\Exception $e) {
					self::info("*失败 {$clientId}, {$key},{$month}" . $e->getMessage());
				}
				self::info("*完成 {$clientId}, {$key},{$month}");
			}
			else
			{
				self::info("*完成 {$clientId}, {$key},{$month}, 没有新增");
			}
		}
	}


    /**
     * 重点资源统计表xs2支持根据月份查询
     */
    public function actionStatisticWorkReportForXs2BySet($setId, $start = 0, $dryRun = 1)
    {
        $dbs = self::dbSetList(3);
        $dbNum = 1;
        $dbCount = count($dbs);
        foreach ($dbs as $db) {

            echo "{$db['set_id']}: $dbNum/$dbCount \n";

            if ($setId && $setId != $db['set_id']) {
                echo "{$db['set_id']}: skip \n";
                continue;
            }

            if ($db['set_id'] < $start) {
                echo "{$db['set_id']}: skip \n";
                continue;
            }

            $clients = Yii::app()->account_base_db->createCommand("SELECT client_id FROM `tbl_client` where pgsql_set_id={$db['set_id']}")->queryAll(true);
            $clientNum = 1;
            $clientCount = count($clients);
            foreach ($clients as $client) {

                echo "db: {$db['set_id']}: $dbNum/$dbCount----client:{$client['client_id']}: $clientNum/$clientCount \n";
                $clientNum++;

                try {
                    LogUtil::info("*开始 {$client['client_id']}");
                    if (!$dryRun) {
                        \common\library\server\crontab\task\StatisticWorkTask::xs2ForLastMonth($client['client_id']);
//                        (new \common\library\queue_v2\job\crontab\StatisticsXS2ForLastMonth())->process($client['client_id']);
                    }
                } catch (\Exception $e) {
                    LogUtil::info("*失败 {$client['client_id']}" . $e->getMessage());
                }
            }

            $dbNum++;
        }
    }

    public function actionStatisticWorkReportForXs2ByClient($clientId)
    {
        try {
            \common\library\server\crontab\task\StatisticWorkTask::xs2ForLastMonth($clientId);
        } catch (\Exception $e) {
            LogUtil::info("*失败 {$clientId}" . $e->getMessage());
        }
    }

    /**
     * 每周新增建档建议数据 - 发送桌面提醒
     * 每周五中午14:00: 统计上周五-这周五的新增建档建议数量，给用户发送桌面提醒
     */
    public function actionAdviceDesktopNotify($clientId = 0)
    {
        $clientIds = $this->getClientList($clientId, true);
        $stop = date('Y-m-d', strtotime('Friday'));
        $start = date('Y-m-d', strtotime('-1 week Friday'));

        foreach ($clientIds as $clientId) {
            $db = \ProjectActiveRecord::getDbByClientId($clientId->client_id);

            $sql = "SELECT user_id, count(1) as total FROM tbl_ai_customer_advice WHERE client_id=:client_id AND update_time BETWEEN :start AND :stop AND trust_level=:trust_level AND not_accept_type=:not_accept_type AND enable_flag=:enable_flag GROUP BY user_id";
            $result = $db->createCommand($sql)->queryAll(true, [
                ':client_id' => $clientId->client_id,
                ':start' => $start,
                ':stop' => $stop,
                ':trust_level' => 1,
                ':not_accept_type' => 0,
                ':enable_flag' => 1
            ]);

            foreach ($result as $item) {
                if ($item['total'] > 0) {
                    $sql = "SELECT count(1) as total FROM tbl_ai_customer_advice WHERE client_id=:client_id AND user_id=:user_id AND trust_level=:trust_level AND not_accept_type=:not_accept_type AND enable_flag=:enable_flag";
                    $total = $db->createCommand($sql)->queryScalar([
                        ':client_id' => $clientId->client_id,
                        ':user_id' => $item['user_id'],
                        ':trust_level' => 1,
                        ':not_accept_type' => 0,
                        ':enable_flag' => 1
                    ]);

                    //推送桌面提醒
                    \User::setLoginUserById($item['user_id']);
                    \common\library\push\Browser::push(
                        $item['user_id'],
                        \common\library\push\Browser::TYPE_MAIL_AI_EVENTS,
                        [
                            'type' => \common\library\notification\Constant::NOTIFICATION_TYPE_AI_ADVICE,
                            'start_time' => $start,
                            'end_time' => $stop,
                            'last_week' => $item['total'],
                            'total' => $total
                        ]
                    );

                    LogUtil::info("clientId={$clientId->client_id}, userId={$item['user_id']}, start_time={$start}, end_time={$stop}, last_week={$item['total']}, total={$total}");
                }
            }
        }
    }

    /**
     * 失效建档建议统计数据
     *
     * 1、失效建档建议的覆盖度
     *  失效建档建议数量/当前建档建议数量，
     *  有失效建档建议的client数/有建档建议的client数，
     *  有失效建档建议的user数/有建档建议的user数
     *
     * 2、失效建档建议的使用率（不采纳 失效建档建议）
     *  有失效建档建议的client中，有不采纳状态的失效建档建议的client占比，
     *  有失效建档建议的user中，有不采纳状态的失效建档建议的user占比，
     *  不采纳的失效建档建议数量/所有失效建档建议数量
     *  不采纳的有效建档建议数量/所有有效建档建议数量
     *
     * @param int $clientId
     * @throws CDbException
     * @throws CException
     * @throws ProcessException
     */
    public function actionAdviceTrust($clientId = 0)
    {
        $data = [
            'rate' => [
                'total' => [
                    'total' => 0,
                    'invalid' => 0
                ],
                'client' => [
                    'total' => 0,
                    'invalid' => 0
                ],
                'user' => [
                    'total' => 0,
                    'invalid' => 0
                ]
            ],
            'use' => [
                'client' => [
                    'total' => 0,
                    'invalid' => 0
                ],
                'user' => [
                    'total' => 0,
                    'invalid' => 0
                ],
                'total' => [
                    'total' => 0,
                    'invalid' => 0
                ],
                'invalid' => [
                    'total' => 0,
                    'invalid' => 0
                ]
            ]
        ];

        $clientIds = $this->getClientList($clientId, true);
        foreach ($clientIds as $clientId) {
            $db = \ProjectActiveRecord::getDbByClientId($clientId->client_id);

            // 当前建档建议数量
            $sql = "SELECT count(1) as total FROM tbl_ai_customer_advice WHERE client_id={$clientId->client_id} AND enable_flag=1";
            $total = $db->createCommand($sql)->queryScalar();
            $data['rate']['total']['total'] += $total;

            // 失效建档建议数量
            $sql = "SELECT count(1) as total FROM tbl_ai_customer_advice WHERE client_id={$clientId->client_id} AND enable_flag=1 AND trust_level=0";
            $invalid = $db->createCommand($sql)->queryScalar();
            $data['rate']['total']['invalid'] += $invalid;

            // 有建档建议的client数
            if ($total) {
                $data['rate']['client']['total']++;
            }

            // 有失效建档建议的client数
            if ($invalid) {
                $data['rate']['client']['invalid']++;
            }

            $sql = "SELECT count(DISTINCT(user_id)) as total FROM tbl_ai_customer_advice WHERE client_id={$clientId->client_id} AND enable_flag=1";
            $total = $db->createCommand($sql)->queryScalar();

            // 有建档建议的user数
            if ($total) {
                $data['rate']['user']['total'] += $total;
            }

            $sql = "SELECT count(DISTINCT(user_id)) as total FROM tbl_ai_customer_advice WHERE client_id={$clientId->client_id} AND enable_flag=1 AND trust_level=0";
            $invalid = $db->createCommand($sql)->queryScalar();

            // 有失效建档建议的user数
            if ($invalid) {
                $data['rate']['user']['invalid'] += $invalid;
            }


            // 所有失效建档建议数量
            $sql = "SELECT count(1) as total FROM tbl_ai_customer_advice WHERE client_id={$clientId->client_id} AND enable_flag=1 AND trust_level=0";
            $total = $db->createCommand($sql)->queryScalar();
            $data['use']['total']['invalid'] += $total;

            // 不采纳的失效建档建议数量
            $sql = "SELECT count(1) as total FROM tbl_ai_customer_advice WHERE client_id={$clientId->client_id} AND enable_flag=1 AND trust_level=0 AND not_accept_type>0";
            $total = $db->createCommand($sql)->queryScalar();
            $data['use']['invalid']['invalid'] += $total;

            // 有不采纳状态的失效建档建议的client数
            if ($total) {
                $data['use']['client']['invalid']++;
            }

            // 所有有效建档建议数量
            $sql = "SELECT count(1) as total FROM tbl_ai_customer_advice WHERE client_id={$clientId->client_id} AND enable_flag=1 AND trust_level=1";
            $total = $db->createCommand($sql)->queryScalar();
            $data['use']['total']['total'] += $total;

            // 不采纳的有效建档建议数量
            $sql = "SELECT count(1) as total FROM tbl_ai_customer_advice WHERE client_id={$clientId->client_id} AND enable_flag=1 AND trust_level=1 AND not_accept_type>0";
            $total = $db->createCommand($sql)->queryScalar();
            $data['use']['invalid']['total'] += $total;

            // 有不采纳状态的失效建档建议的user数
            $sql = "SELECT count(DISTINCT(user_id)) as total FROM tbl_ai_customer_advice WHERE client_id={$clientId->client_id} AND enable_flag=1 AND trust_level=0 AND not_accept_type>0";
            $total = $db->createCommand($sql)->queryScalar();
            if ($total) {
                $data['use']['user']['invalid'] += $total;
            }
        }

        echo "失效建档建议数量({$data['rate']['total']['invalid']}) / 当前建档建议数量({$data['rate']['total']['total']})\n";
        echo "有失效建档建议的client数({$data['rate']['client']['invalid']}) / 有建档建议的client数({$data['rate']['client']['total']})\n";
        echo "有失效建档建议的user数({$data['rate']['user']['invalid']}) / 有建档建议的user数({$data['rate']['user']['total']})\n";

        echo "有失效建档建议的client({$data['rate']['client']['invalid']})中，有不采纳状态的失效建档建议的client({$data['use']['client']['invalid']})占比({$data['use']['client']['invalid']}/{$data['rate']['client']['invalid']})\n";
        echo "有失效建档建议的user({$data['rate']['user']['invalid']})中，有不采纳状态的失效建档建议的use({$data['use']['user']['invalid']})占比({$data['use']['user']['invalid']}/{$data['rate']['user']['invalid']})\n";
        echo "不采纳的失效建档建议数量({$data['use']['invalid']['invalid']}) / 所有失效建档建议数量({$data['rate']['total']['invalid']})\n";
        echo "不采纳的有效建档建议数量({$data['use']['invalid']['total']}) / 所有有效建档建议数量({$data['use']['total']['total']})\n";
    }


    /**
     * @param $date 指定日期修复 follow_customer_count数据
     * <AUTHOR>
     */
    public function actionFixStatisticWorkReportByDate($date)
    {
        $dbs = self::dbSetList(3);
        foreach ($dbs as $db) {
            $clients = Yii::app()->account_base_db->createCommand("SELECT client_id FROM `tbl_client` where pgsql_set_id={$db['set_id']}")->queryAll(true);
            foreach ($clients as $client) {
                try {
                    $this->actionStatisticWorkReportByClientIdAndDate($client['client_id'], $date);
                } catch (\Exception $e) {
                    LogUtil::info("*失败 {$client['client_id']}" . $e->getMessage());
                }
            }
        }
    }

    public function actionStatisticWorkReportByClientIdAndDate($client_id, $date)
    {
        $commentFunction = function ($c, $params, $date, $timeType, &$sqlValues) {
            $sql = "
select client_id, array_to_string(user_id, ',', '') as user_id, company_id
from tbl_company
where is_archive=1
      and order_time >= :start_time
      and order_time <= :end_time
      and client_id = :client_id
      and user_id <> '{}'
";

            $values = [];
            $companyList = $c->createCommand($sql)->query($params);
            foreach ($companyList as $company) {
                foreach (explode(',', $company['user_id']) as $userId) {
                    $values[$userId][] = $company;
                }
            }
            foreach ($values as $userId => $value) {
                if (empty($userId)) {
                    continue;
                }

                $companyIds = \common\library\util\PgsqlUtil::formatArray(array_column($value, 'company_id'));
                $sqlValues[] = "({$value[0]['client_id']}, {$userId}, {$date}, '{$timeType}', 'follow_customer_count', '{$companyIds}')";
            }
        };

        $c = \PgActiveRecord::getDbByClientId($client_id);

        $sqlValues = [];

        $params = [
            'start_time' => $date . ' 00:00:00',
            'end_time' => $date . ' 23:59:59',
            'client_id' => $client_id
        ];

        $timeType = 'day';
        $date = date('Ymd', strtotime($date));
        $commentFunction($c, $params, $date, $timeType, $sqlValues);


        if (!empty($sqlValues)) {
            $sqlValuesString = implode(',', $sqlValues);

            $insertSql = "
INSERT INTO tbl_statistic_work_report as t1 
(client_id, user_id, date, time_type, key, ids)
VALUES {$sqlValuesString}
ON CONFLICT (client_id, user_id, date, time_type, key) 
DO UPDATE SET ids=t1.ids
";
            try {
                $c->createCommand($insertSql)->execute();
            } catch (\Exception $e) {
                LogUtil::info("*失败 {$client_id}, {$date}" . $e->getMessage());
            }
            LogUtil::info("*完成 {$client_id}");
            echo "*完成 {$client_id}, {$date}\n";
        } else {
            LogUtil::info("*完成 {$client_id}, 没有新增");
            echo "*完成 {$client_id}, 没有新增 {$date}\n";
        }
    }


    public function actionFixTrailRemark($clientId, $userId, $startDate, $endDate)
    {
        $userIds = [$userId];
        $db = ProjectActiveRecord::getDbByClientId($clientId);
        $pgDb = PgActiveRecord::getDbByClientId($clientId);

        $currentList = $db->createCommand("select user_id,customer_remark_count,date from tbl_user_statistics_day where client_id={$clientId} and  user_id in(" . implode(',',
                $userIds) . ") and date >='{$startDate}' and date <='{$endDate}'")->queryAll();
        $currentListMap =  array_reduce($currentList, function ($carry, $item){
            if( !isset($carry[$item['user_id']]))
            {
                $carry[$item['user_id']] =[];
            }
            $carry[$item['user_id']][$item['date']] = $item['customer_remark_count'];
            return $carry;
        },[]);


        $startTime =  strlen($startDate) == 10 ? $startDate . ' 00:00:00' : $startDate;
        $endTime = strlen($endDate) == 10 ? $endDate . ' 23:59:59' : $endDate;


        $trailList = $pgDb->createCommand(
            "select create_user as statistic_user_id, count(1) as dynamic_count, to_char(create_time, 'YYYY-MM-dd') as date  from tbl_dynamic_trail where enable_flag=1 AND client_id={$clientId} AND create_user IN (".implode($userIds).") AND create_time BETWEEN '{$startTime}' AND '{$endTime}' and company_id > 0 and type >=100 and type <200 group by statistic_user_id ,date"
        )->queryAll();

        foreach ($trailList as $item)
        {
            $currentCount  = $currentListMap[$item['statistic_user_id']][$item['date']]??0;
            if( $currentCount < $item['dynamic_count'] )
            {
                $add = $item['dynamic_count'] - $currentCount;
                self::info("{$item['date']} client_id:{$clientId}  user_id:{$item['statistic_user_id']} old:{$currentCount}, new {$item['dynamic_count']} add: {$add}");
                StatisticsService::dynamicRemarkAdd($clientId, $item['statistic_user_id'], $add, $item['date']);
            }
        }

    }

    /**
     * 流失预警（kh19）每天凌晨进行统计
     */
    public function actionCustomerLostWarningStatistic()
    {
        ini_set("memory_limit", "1024M");
        LogUtil::info("lostWarningStatisticBegin", ['end_time' => date('Y-m-d H:i:s')]);

        // 注意，调用该方法才会包括 PRO 客户
        $dbs = \common\library\account\service\DbService::getDbList(DbSet::TYPE_PGSQL);

        // 获取今天时间
        $date = date('Ymd');

        foreach ($dbs as $db)
        {
            // 获取需要运行的 ClientId
            $clientIds = Yii::app()->account_base_db->createCommand("SELECT client_id FROM `tbl_client` where pgsql_set_id = {$db['set_id']}")->queryAll(true);

            foreach ($clientIds as $client)
            {
                try {

                    // 统计客户流失预警内容
                    (new \common\library\server\crontab\task\StatisticWorkTask())->customerLostWarningStatistic($client['client_id'], $date);

                } catch (\Exception $e) {

                    LogUtil::error("lostWarningStatisticError", ['client_id' => $client['client_id'], 'error_msg' => $e->getMessage(), 'error_trace' => $e->getTrace()]);

                } finally {

                    // 运行太多client会内存溢出，注意释放内存
                    \common\library\account\Client::cleanCacheMap($client['client_id']);
                    PgActiveRecord::releaseDbByClientId($client['client_id']);

                }

            }

            LogUtil::info("lostWarningStatisticDbSet", ['set_id' => $db['set_id']]);
        }

        LogUtil::info("lostWarningStatisticEnd", ['end_time' => date('Y-m-d H:i:s')]);
    }

    public function actionCustomerLostWarningStatisticByClientId($clientIds)
    {
        $clientIds = is_array($clientIds) ?$clientIds : explode(',', $clientIds);
        $date = date('Ymd');
        self::info("lostWarningStatisticBegin");
        foreach ($clientIds as $clientId) {
            try {
                // 统计客户流失预警内容
                (new \common\library\server\crontab\task\StatisticWorkTask())->customerLostWarningStatistic($clientId, $date);

            } catch (\Exception $e) {
                self::info("['client_id' => $clientId, 'error_msg' => {$e->getMessage()}, 'error_trace' => {$e->getTrace()}]");

            } finally {
                self::info("client $clientId run lostWarningStatistic finished");
                \common\library\account\Client::cleanCacheMap($clientId);
                PgActiveRecord::releaseDbByClientId($clientId);
            }
        }
        self::info("lostWarningStatisticEnd");
    }

}
