<?php

use common\library\account\Client;
use common\library\custom_field\company_field\duplicate\CompanyMatchProcess;
use common\library\custom_field\company_field\duplicate\ConversionRules;
use common\library\custom_field\CustomFieldService;
use common\library\custom_field\lead_field\duplicate\LeadMatchProcess;
use common\library\customer_v3\customer\CustomerList;
use common\library\customer\field_unique\DuplicateFlagBuilder;
use common\library\customer\Helper;
use common\library\duplicate\DuplicateConstants;
use common\library\email\CommonDomain;
use common\library\lead\Lead;
use common\library\privilege_v3\PrivilegeService;
use common\library\push\Browser;
use common\library\server\duplicate_flag\SyncDuplicateFlagQueueService;
use common\library\swarm\SwarmService;
use common\library\util\PgsqlUtil;

/**
 * Created by PhpStorm.
 * User: beenzhang
 * Date: 2021/7/20
 * Time: 11:56 AM
 */

class FieldDuplicateCommand extends CrontabCommand
{
    public function actionRebuildDuplicateDispatcher($clientId,$greyNum=null)
    {
        if ($clientId)
        {
            $clientIds = explode(',', $clientId);
        } else {
            $dbSetId = 0;
            if (\Yii::app()->params['env'] == 'test')
                $dbSetId = 3;

            $greyNum = explode(',', $greyNum);
            $clientListData = $this->getClientList($clientId,false,null,null, 0, 0, $greyNum, $dbSetId,null);
            $clientIds = array_column($clientListData,'client_id');
        }
        foreach ($clientIds as $clientId)
        {
            $this->actionRebuildDuplicateFlag($clientId);
        }
    }

    public function actionDuplicateQueueStats()
    {
        var_dump(\common\library\server\duplicate_flag\FieldDuplicateConsumer::notifyQueueCount());
    }

    public function actionPushCompanyDuplicateQueue()
    {
        $clientId = 14367;
        $companyIds = 1127642625;
        $companyIds = explode(',',$companyIds);
        $companyIds = array_values(array_filter($companyIds));
        if (empty($companyIds))
            return;

        $userId = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();
        \common\library\server\duplicate_flag\SyncDuplicateFlagQueueService::pushCompanyQueue($userId,$clientId,$companyIds,Constants::SEARCH_INDEX_TYPE_UPDATE);
    }

    public function actionAllRelationConsumer()
    {
        var_dump(\common\library\server\duplicate_flag\FieldDuplicateConsumer::getAllRelationConsumer());
    }

    public function actionRebuildDuplicateFlag($clientId)
    {
        $db = PgActiveRecord::getDbByClientId($clientId);

        \PgActiveRecord::setConnection($db);
        \ProjectActiveRecord::setConnection(\ProjectActiveRecord::getDbByClientId($clientId));

        $client = \common\library\account\Client::getClient($clientId);
        $masterUser = $client->getMasterUser();
        if($masterUser->isEmpty()){
            self::info("masterUser not exists! client_id: $clientId");
            return;
        }
        $userId = $masterUser->getUserId();

        $this->rebuildCompanyDuplicateFlag($clientId, $userId);
        $this->rebuildLeadDuplicateFlag($clientId, $userId);
    }

    protected function rebuildCompanyDuplicateFlag($clientId,$userId)
    {
        $db = PgActiveRecord::getDbByClientId($clientId);

        $maxId = $db->createCommand('select company_id from tbl_company where client_id='.$clientId.' order by company_id desc limit 1')->queryScalar();

        //删除未归档客户
        $sql = 'select company_id from tbl_company where  client_id='.$clientId;
        $currId = 0;
        self::info("begin company delete client_id:$clientId maxId:{$maxId} currId: {$currId}");
        while ( $currId < $maxId )
        {
            $companyIds = $db->createCommand($sql.' and company_id >'.$currId.' and is_archive = 0 order by company_id asc limit 200')->queryColumn();
            if (empty($companyIds))
                break;

            foreach ($companyIds as $companyId)
            {
                $currId = $companyId;
                $sync = new \common\library\custom_field\company_field\duplicate\SyncDuplicate($clientId, $userId);
                $sync->setOpType(\common\library\custom_field\company_field\duplicate\SyncDuplicate::OP_TYPE_DELETE);
                $sync->sync($companyId);
            }
            self::info("delete company duplicate flag clientId:$clientId  currId：$currId count:".count($companyIds));
        }

        $sql = 'select company_id from tbl_company where  client_id='.$clientId;
        $currId = 0;
        self::info("begin company duplicate flag client_id:$clientId maxId:{$maxId} currId: {$currId}");
        while ( $currId < $maxId )
        {
            $companyIds = $db->createCommand($sql . ' and company_id >' . $currId . ' and is_archive =1 order by company_id asc limit 200')->queryColumn();
            if (empty($companyIds))
                break;

            foreach ($companyIds as $companyId)
            {
                $currId = $companyId;
                $sync = new \common\library\custom_field\company_field\duplicate\SyncDuplicate($clientId, $userId);
                $sync->setOpType(\common\library\custom_field\company_field\duplicate\SyncDuplicate::OP_TYPE_EDIT);
                $sync->sync($companyId);
            }

            self::info("company duplicate flag clientId:$clientId  currId：$currId count:".count($companyIds));
        }
    }

    protected function rebuildLeadDuplicateFlag($clientId, $userId)
    {
        $db = PgActiveRecord::getDbByClientId($clientId);

        $maxId = $db->createCommand('select lead_id from tbl_lead where client_id='.$clientId.' order by lead_id desc limit 1')->queryScalar();

        //删除已删除线索
        $sql = 'select lead_id from tbl_lead where  client_id='.$clientId;
        $currId = 0;
        self::info("begin lead delete client_id:$clientId maxId:{$maxId} currId: {$currId}");
        while ( $currId < $maxId )
        {
            $leadIds = $db->createCommand($sql.' and lead_id >'.$currId.' and is_archive IN (0,2) order by lead_id asc limit 200')->queryColumn();
            if (empty($leadIds))
                break;

            foreach ($leadIds as $leadId)
            {
                $currId = $leadId;
                $sync = new \common\library\custom_field\lead_field\duplicate\SyncDuplicate($clientId, $userId);
                $sync->setOpType(\common\library\custom_field\lead_field\duplicate\SyncDuplicate::OP_TYPE_DELETE);
                $sync->sync($leadId);
            }
            self::info("delete lead duplicate flag clientId:$clientId  currId：$currId count:".count($leadIds));
        }

        $sql = 'select lead_id from tbl_lead where  client_id='.$clientId;
        $currId = 0;
        self::info("begin lead client_id:$clientId maxId:{$maxId} currId: {$currId}");
        while ( $currId < $maxId )
        {
            $leadIds = $db->createCommand($sql . ' and lead_id >' . $currId . ' and is_archive =1 order by lead_id asc limit 100')->queryColumn();
            if (empty($leadIds))
                break;

            foreach ($leadIds as $leadId)
            {
                $currId = $leadId;
                $sync = new \common\library\custom_field\lead_field\duplicate\SyncDuplicate($clientId, $userId);
                $sync->setOpType(\common\library\custom_field\lead_field\duplicate\SyncDuplicate::OP_TYPE_EDIT);
                $sync->sync($leadId);
            }
            self::info("lead duplicate flag clientId:$clientId  currId：$currId count:".count($leadIds));
        }
    }

    /**
     * @param int $clientId
     * @throws CDbException
     * @throws CException
     * @throws ProcessException
     * 修正数据：1、字段值从有值到空 2.主页、电话过滤掉的非有效数据  后没有重算对应company本身字段导致flag错误
     * 根据字段判重配置和关联表比对，剔除没有关联数据的字段flag
     */
    public function actionFixNotValidField($clientId)
    {
        $userId = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();
        User::setLoginUserById($userId);

        $rules = \common\library\custom_field\company_field\duplicate\Helper::getUniqueRules($clientId,Constants::TYPE_COMPANY);
        $ruleFields = array_column($rules,'id');
        $rules = \common\library\custom_field\company_field\duplicate\Helper::getUniqueRules($clientId,Constants::TYPE_CUSTOMER);
        $ruleFields = array_merge($ruleFields,array_column($rules,'id'));

        $db = PgActiveRecord::getDbByClientId($clientId);
        $maxId = $db->createCommand('select company_id from tbl_company where client_id='.$clientId.' order by company_id desc limit 1')->queryScalar();

        $sql = 'select company_id,duplicate_flag from tbl_company where  client_id='.$clientId;
        $currId = 0;
        $rebuildFlagNumSql = [];
        self::info("begin company duplicate flag client_id:$clientId maxId:{$maxId} currId: {$currId}");
        while ( $currId < $maxId )
        {
            $companyList = $db->createCommand($sql . ' and company_id >' . $currId . ' and is_archive =1 order by company_id asc limit 200')->queryAll(true);
            if (empty($companyList))
                break;

            foreach ($companyList as $company)
            {
                $currId = $companyId = $company['company_id'];
                $oldDuplicateFlag = $duplicateFlag = $company['duplicate_flag'];

                $relation = new \common\library\custom_field\company_field\duplicate\DuplicateFieldRelationList($clientId);
                $relation->setReferType([4,5]);
                $relation->setCompanyId($companyId);
                $relationList = $relation->find();

                $relationFields = [];
                foreach ($relationList as $item)
                {
                    $fieldId = $item['field_id'];
                    if (strpos($fieldId,'contact:') !== false)
                    {
                        $fieldId = 'contact';
                    } elseif ($fieldId === 'email:domain')
                    {
                        $fieldId = 'email';
                    }
                    $relationFields[] = $fieldId;
                }

                $removeFields = array_diff($ruleFields, $relationFields);

                foreach ($removeFields as $removeField) {
                    $duplicateFlag = \common\library\customer\field_unique\DuplicateFlagBuilder::setValue($duplicateFlag, $removeField,0,Constants::TYPE_COMPANY);
                }

                if (!empty($removeFields) && $oldDuplicateFlag != $duplicateFlag)
                {
                    self::info("company duplicate flag clientId:$clientId  companyId:$companyId old:$oldDuplicateFlag new $duplicateFlag");
                    $rebuildFlagNumSql[] = "update tbl_company set duplicate_flag={$duplicateFlag} where company_id={$companyId} and duplicate_flag != {$duplicateFlag}";
                }
            }

            self::info("company duplicate flag clientId:$clientId  currId：$currId count:".count($companyList));
        }

        $sqlArray = array_chunk($rebuildFlagNumSql,100);
        foreach ($sqlArray as $sqlArr)
        {
            //var_dump($sqlArr);
            $updateSql = implode(';', $sqlArr);
            self::info("company duplicate flag clientId:$clientId sql: $updateSql");
            $db->createCommand($updateSql)->execute();
        }
    }

    /**
     * @param $clientIds
     */
    public function actionFixIntervalDataDispatcher($clientIds, $startTime, $endTime)
    {
        $clientIds = explode(',', $clientIds);
        foreach ($clientIds as $clientId)
        {
            $this->actionFixCompanyIntervalData($clientId, $startTime, $endTime);
            $this->actionFixLeadIntervalData($clientId, $startTime, $endTime);
        }
    }

    public function actionFixCompanyIntervalData($clientId, $startTime, $endTime)
    {
        $userId = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();
        User::setLoginUserById($userId);

        $db = PgActiveRecord::getDbByClientId($clientId);
        $maxId = $db->createCommand("select company_id from tbl_company where client_id={$clientId} and update_time >= '{$startTime}' and update_time <= '{$endTime}' order by company_id desc limit 1")->queryScalar();

        $sql = "select company_id,is_archive,update_time from tbl_company where  client_id={$clientId} 
 and update_time >= '{$startTime}' and update_time <= '{$endTime}'";

        $currId = 0;
        self::info("begin company duplicate flag client_id:$clientId maxId:{$maxId} currId: {$currId}");
        while ($currId < $maxId) {
            $companyList = $db->createCommand($sql . ' and company_id >' . $currId . '  order by company_id asc limit 200')->queryAll(true);
            if (empty($companyList))
                break;

            //var_dump($companyList);

            foreach ($companyList as $company) {
                $currId = $company['company_id'];
                $opType = $company['is_archive'] > 0 ? \common\library\custom_field\company_field\duplicate\SyncDuplicate::OP_TYPE_EDIT : \common\library\custom_field\company_field\duplicate\SyncDuplicate::OP_TYPE_DELETE;

                $sync = new \common\library\custom_field\company_field\duplicate\SyncDuplicate($clientId, $userId);
                $sync->setOpType($opType);
                $sync->sync($currId);
            }

            self::info("company duplicate flag clientId:$clientId  currId：$currId count:" . count($companyList));
        }
    }

    public function actionFixLeadIntervalData($clientId, $startTime, $endTime)
    {
        $userId = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();
        User::setLoginUserById($userId);

        $db = PgActiveRecord::getDbByClientId($clientId);
        $maxId = $db->createCommand("select lead_id from tbl_lead where client_id={$clientId} and update_time >= '{$startTime}' and update_time <= '{$endTime}' order by lead_id desc limit 1")->queryScalar();

        $sql = "select lead_id,is_archive,update_time from tbl_lead where  client_id={$clientId} 
 and update_time >= '{$startTime}' and update_time <= '{$endTime}'";

        $currId = 0;
        self::info("begin lead duplicate flag client_id:$clientId maxId:{$maxId} currId: {$currId}");
        while ($currId < $maxId)
        {
            $leadList = $db->createCommand($sql . ' and lead_id >' . $currId . '  order by lead_id asc limit 200')->queryAll(true);
            if (empty($leadList))
                break;

            foreach ($leadList as $lead) {
                $currId = $lead['lead_id'];
                $opType = $lead['is_archive'] == 1 ? \common\library\custom_field\company_field\duplicate\SyncDuplicate::OP_TYPE_EDIT : \common\library\custom_field\company_field\duplicate\SyncDuplicate::OP_TYPE_DELETE;

                $sync = new \common\library\custom_field\lead_field\duplicate\SyncDuplicate($clientId, $userId);
                $sync->setOpType($opType);
                $sync->sync($currId);
            }

            self::info("lead duplicate flag clientId:$clientId  currId：$currId count:" . count($leadList));
        }
    }

    public function releaseRelation()
    {
        \common\library\server\duplicate_flag\FieldDuplicateConsumer::releaseRelationConsumer();
    }

    public function actionReleaseRelation($ip)
    {
        \common\library\server\duplicate_flag\FieldDuplicateConsumer::releaseRelationConsumer([$ip]);
    }

    public function actionClientQueueLength($clientIds)
    {
        $clientIds = explode(',', $clientIds);
        $clientCountMap = [];
        foreach ($clientIds as $clientId) {
           $clientCountMap[$clientId] = \common\library\server\duplicate_flag\FieldDuplicateConsumer::clientQueueCount($clientId);
        }
        var_dump($clientCountMap);
    }

    public function actionPushNotify($clientIds)
    {
        $clientIds = explode(',',$clientIds);
        if (empty($clientIds))
        {
            return;
        }

        $redis = \RedisService::queue();
        $redis->lpush(SyncDuplicateFlagQueueService::getQueueKey(\Constants::QUEUE_KEY_SYNC_DUPLICATE_NOTIFY), $clientIds);
    }

    public static function actionResetClientData($clientId)
    {
        $clientQueueKey = SyncDuplicateFlagQueueService::getQueueKey(\Constants::QUEUE_KEY_SYNC_DUPLICATE_PREFIX) . $clientId;
        $redis = \RedisService::queuePersistent();
        $redis->del([$clientQueueKey]);
    }


	/**
	 * 修复异常字段配置
	 *
	 * @param string $clientIds
	 * @return void
	 * @throws CDbException
	 * @throws ProcessException
	 */
	public function actionFixRule(string $clientIds = '') {

		if (!$clientIds) {

			$clientIds = [40073,40146,40239,40258,35102,34667,40458,40035,40179,36350,37329,40089,39722,39785,40154,40362,41468,39870,39772,39958,40215,40233,40468,40483,33043,38754,40039,28795,37140,40422,35243,40391,40135,39712,37730,38872,21589,34227,37385,38288,39920,40157,40208,40324,40328,40038,40229,40464,34034,34322,39770,40349,40419,40462,37471,40534,17398,33057,37428,37417,38864,40088,34437,40255,35784,36309,39621,40249,40308,35908,39245,40453,42988,36263,30046,36719,39909,39199,39280,40206,37698,40337,39710,36870,36815,31160,36524,38601,35165,40455,34671,39882,40227,40432,33218,36492,40329,40152,40348,40401,31373,35401,40395,20723,33601,40032,40181,40400,42415,28598,40018,40416,42056,19854,39891,40218,40325,43398,32982,40394,34862,29224,40334,35573,40259,40054,42420,39368,40505,40470,39273,39379,39802,39893,43115,43393,40459,37759,34750,37565,40275,40463,31576,37642,39797,39828,32761,40298,16838,37342,36429,37223,35349,36630,40188,40149,40389,40457,43275,35995,40345,40020,39784,39029,40151,40043,32923,39706,38958,40169,40201,40521,32572,40461,35227,38034,40332,40437,20722,39876,40029,36951,38069,36145,18303,35058,38224,36992,39038,40036,39751,37682,40440,33551,40511,30372,40410,40435,40456,40399,39032,31129,37778,39668,40078,41393,38708,38025,40559,38635,40388,40245,31569,37219,38986,33046,40180,31239,40533,40261,36256,36373,14121,38540,40460,38338,29572,18234,36647,37173,33071,39869,40118,40377,36356,36778,38550,40378,40366,34601,35959,39791,40560,36580,37255,34641];

		}else{

			$clientIds = explode(',', $clientIds);
		}

		foreach ($clientIds as $clientId) {

			$db = \common\models\client\CustomField::getDbByClientId($clientId);

			if (empty($db)) {

				self::info('empty db continue:' . $clientId);
				continue;
			}

			$adminUserId = PrivilegeService::getInstance($clientId)->getAdminUserId();

			if (empty($adminUserId)) {

				self::info('empty adminUserId continue:' . $clientId);
				continue;
			}


			self::info('start:' . $clientId);

			$sql = 'UPDATE tbl_custom_field
					SET unique_check = 1
					WHERE client_id = ' . $clientId . '
					  AND type = 8
					  AND id IN (\'email\', \'tel_list\')';

			try {

				$db->createCommand($sql)->execute();

				$this->actionRebuildDuplicateDispatcher($clientId);

			} catch (Throwable $throwable) {

				self::info($throwable->getMessage());
				self::info($throwable->getTraceAsString())
				;
			}
			self::info('end:' . $clientId);
		}

		self::info('done');
	}


	public function actionRebuildOne($clientId, $companyId = 0, $leadId = 0) {

		try {

			$adminUserId = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();

			User::setLoginUserById($adminUserId);
            if (!empty($companyId)){

                $sync = new \common\library\custom_field\company_field\duplicate\SyncDuplicate($clientId, $adminUserId);

                $sync->setOpType(\common\library\custom_field\company_field\duplicate\SyncDuplicate::OP_TYPE_EDIT);

                $sync->sync($companyId);
            } elseif (!empty($leadId)) {

                $sync = new \common\library\custom_field\lead_field\duplicate\SyncDuplicate($clientId, $adminUserId);

                $sync->setOpType(\common\library\custom_field\lead_field\duplicate\SyncDuplicate::OP_TYPE_EDIT);

                $sync->sync($leadId);
            }

		} catch (Throwable $throwable) {

			self::info($throwable->getMessage());
			self::info($throwable->getTraceAsString());
		}

	}

    public function actionFieldUniqueDetail($clientId, $lead_id) {

        echo $clientId;
        try {

            $adminUserId = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();

            User::setLoginUserById($adminUserId);

            if (!empty($companyId)){


            } elseif (!empty($lead_id)) {

                $user = User::getLoginUser();

                $lead = new Lead($user->getClientId(), $lead_id);
                if (!$lead->isExist()) {

                    echo 'not found';
                }

                $process = new LeadMatchProcess($user->getClientId());
                $rules = $process->matchByLead($lead);
                if (empty($rules)) {

                    echo 'no rules';
                }

                $matchedRules = $rules['matched_rules'];

                echo json_encode(['$matchedRules' => $matchedRules]);

                $leadList = Helper::leadFieldConflictList($user->getUserId(), $matchedRules);

                echo json_encode(['$leadList' => $leadList]);

                //转化成company的规则后执行
                $companyRules = ConversionRules::baseRuleToCompany($rules['base_rules']);
                $customerRules = ConversionRules::leadCustomerRuleToCustomer($rules['customer_rules']);
                $process = new CompanyMatchProcess($user->getClientId());
                $companyMatchedRules = $process->matchByRules($companyRules, $customerRules);
                $companyList = Helper::companyFieldConflictList($user->getUserId(), $companyMatchedRules);

                $accessService = (new \common\library\customer\service\LeadAccessService($user->getClientId(), $user->getUserId()));
                $accessService->setLeadInfoData($user->getClientId(), $lead->user_id, $lead->scope_user_ids);

                echo json_encode([
                    'refer_info' => [
                        'refer_type' => Constants::TYPE_LEAD,
                        'refer_id' => $lead_id,
                        'is_public' => empty($lead->user_id) ? 1 : 0,
                        'user_id' => $lead->user_id,
                        'is_transform' => $accessService->isTransform() ? 1 : 0,
                    ],
                    'lead_list' => $leadList,
                    'company_list' => $companyList,
                ]);
            }

        } catch (Throwable $throwable) {

            self::info($throwable->getMessage());
            self::info($throwable->getTraceAsString());
        }


    }

    public function actionTestDb($clientId)
    {
//        $db = \PgActiveRecord::reuseDbByClientId($clientId);
        $db =\PgActiveRecord::getDbByClientId($clientId);
        var_dump($db);
    }

	/**
	 * @throws CDbException
	 * @throws ProcessException
	 * @throws CException
	 */
	public function actionRebuildDuplicateFlagByFields($client_id, $user_id, $type, $unique_check_field_ids = '', $not_unique_check_field_ids = '')
	{

		//测试案例
		//$not_unique_check_field_ids = ['name'];
		//$unique_check_field_ids = ['homepage','email'];
		//$not_unique_check_field_ids = ['homepage'];

		$clientObject = \common\library\account\Client::getClient($client_id);
		$user = $clientObject->getMasterUser();
		User::setLoginUser($user);

		$redis = \RedisService::cache();
		$redis->set(DuplicateFlagBuilder::REBUILD_DUPLICATE_FLAG_BY_FIELDS[$type] . $client_id, 1, 'Ex', 60 * 60 * 2 );

		$unique_check_field_ids = array_filter(explode(',', $unique_check_field_ids));
		$not_unique_check_field_ids = array_filter(explode(',', $not_unique_check_field_ids));
		$db = PgActiveRecord::getDbByClientId($client_id);

		try {

			//判重有到无
			if (!empty($not_unique_check_field_ids)) {
				$this->updateDuplicateFlagByNotUnique($db, $client_id, $not_unique_check_field_ids, $type, $redis, $user->getUserId());
			}

			//判重无到有
			if (!empty($unique_check_field_ids)) {
				$this->updateDuplicateRelation($db, $client_id, $unique_check_field_ids, $type, $redis, $user->getUserId());
			}
		}catch (Exception $e){
			\common\library\report\error\ErrorReport::phpError(new \CExceptionEvent(null, $e), $e->getTrace());
		} finally {
			$redis->del([DuplicateFlagBuilder::REBUILD_DUPLICATE_FLAG_BY_FIELDS[$type] . $client_id]);
			\common\library\account\Helper::setClientExternalValue($client_id, DuplicateFlagBuilder::DUPLICATE_EXTERNAL_MAP[$type], 0);
			Browser::push($user_id, DuplicateFlagBuilder::DUPLICATE_BROWSER_MAP[$type], 0);
		}

	}
	protected function updateDuplicateFlagByNotUnique($db, $clientId, $fieldIds, $type, $redis, $user_id){

		$indexLead = \common\library\customer\field_unique\DuplicateFlagBuilder::$leadIndexMap;
		$indexCompany = \common\library\customer\field_unique\DuplicateFlagBuilder::$indexMap;

		$tempA = str_pad(0, 32, '0', STR_PAD_LEFT);

		foreach ($fieldIds as $fieldId) {
			if (in_array($fieldId, array_keys($indexLead)) || $fieldId == 'name') {
				if ($fieldId == 'name') {
					$tempA[-$indexLead['company_name']] = '1';
				} else {
					$tempA[-$indexLead[$fieldId]] = '1';
				}
			}
			$tempA[-$indexCompany[$fieldId]] = '1';

		}

		$tempA = base_convert($tempA, 2, 10);


		$tempB = base_convert("ffffffff", 16, 2);

		$tempFieldIds = $fieldIds;
		if ($type == \Constants::TYPE_LEAD) {
			$tempFieldIds = array_merge($fieldIds, ['short_name', 'serial_id', 'fax', 'address']);
		}

		foreach ($tempFieldIds as $fieldId) {
			if (in_array($fieldId, array_keys($indexLead)) || $fieldId == 'name') {
				if ($fieldId == 'name') {
					$tempB[-$indexLead['company_name']] = '0';
				} else {
					$tempB[-$indexLead[$fieldId]] = '0';
				}
			}
			$tempB[-$indexCompany[$fieldId]] = '0';
		}

		$tempB = base_convert($tempB, 2, 10);

		$createTime = date('Y-m-d H:i:s');
		$limit = 2000;

		if ($type == \Constants::TYPE_COMPANY) {


			$sqlCompany = 'select company_id from tbl_company where  client_id=' . $clientId;

			while (true) {

				$companyIds = $db->createCommand($sqlCompany . " and is_archive = 1 and (duplicate_flag & {$tempA}::bigint) > 0 and create_time < '{$createTime}' order by company_id asc limit {$limit}")->queryColumn();

				if (empty($companyIds) || empty($redis->get(DuplicateFlagBuilder::REBUILD_DUPLICATE_FLAG_BY_FIELDS[$type] . $clientId)))
					break;


				$companyIdsArray = array_chunk($companyIds, 200);
				foreach ($companyIdsArray as $idsArr) {
					$sqlTemp = "update tbl_company set duplicate_flag = (duplicate_flag & {$tempB}::bigint) WHERE client_id = " . $clientId . " AND company_id in (" . implode(',', $idsArr) .
						" ) AND is_archive = 1 and duplicate_flag != (duplicate_flag & {$tempB}::bigint) ";
					$count = $db->createCommand($sqlTemp)->execute();
					(new SwarmService($clientId))->setSkipUpdateSearch()->refreshByRefer($idsArr, ['duplicate_flag'], getenv('RUNTIME_ENV') == 'k8s');
					self::info('not_unique_check_field_ids company success count = ' . $count);
				}

			}
		} else {

			$sqlLead = 'select lead_id from tbl_lead where  client_id=' . $clientId;
			while (true) {

				$leadIds = $db->createCommand($sqlLead . " and is_archive != 0 and (duplicate_flag & {$tempA}::bigint) > 0 and create_time < '{$createTime}' order by lead_id asc limit {$limit}")->queryColumn();

				if (empty($leadIds) || empty($redis->get(DuplicateFlagBuilder::REBUILD_DUPLICATE_FLAG_BY_FIELDS[$type] . $clientId)))
					break;


				$leadIdsArray = array_chunk($leadIds, 200);
				foreach ($leadIdsArray as $idsArr) {
					$sqlTemp = "update tbl_lead set duplicate_flag = (duplicate_flag & {$tempB}::bigint) WHERE client_id = " . $clientId . " AND lead_id in (" . implode(',', $idsArr) .
						" ) AND is_archive != 0 and duplicate_flag != (duplicate_flag & {$tempB}::bigint) ";
					$count = $db->createCommand($sqlTemp)->execute();
					self::info('not_unique_check_field_ids lead success count = ' . $count);
				}
			}

		}
	}

	/**
	 * @throws CException
	 * @throws CDbException
	 * @throws ProcessException
	 */
	//先删除tbl_duplicate_field_relation对应字段的数据，再重算tbl_duplicate_field_relation的数据，最后重算表的duplicate_flag值
	protected function updateDuplicateRelation($db, $clientId, $fieldIds, $type, $redis, $user_id, $rebuildFlag = false)
	{

		$TYPE_MAP = [
			\Constants::TYPE_LEAD => \Constants::TYPE_LEAD_CUSTOMER,
			\Constants::TYPE_COMPANY => \Constants::TYPE_CUSTOMER
		];

		$fieldUniqueMap = \common\library\custom_field\CustomFieldService::FIELD_UNIQUE_CHECK_MAP;
		$createTime = date('Y-m-d H:i:s');
		$limit = 500;
		$offset = 0;
		list($ids, $customerIds) = $this->buildIdsAndCustomerIds($fieldIds, $type, $fieldUniqueMap);
		$customerReferType = !empty($customerIds) ? $TYPE_MAP[$type] : 0;
		$tempType = !empty($ids) ? $type : 0;
		$tempType = array_values(array_filter([$tempType, $customerReferType]));
		$deleteSql = $this->buildDeleteDuplicateSql($clientId, $tempType, $fieldIds, $createTime);

		list($fieldASql, $fieldBSql, $customerFieldASql, $customerFieldBSql) = $this->buildSelectFieldSql($ids, $customerIds);

		if(!empty($ids) && !empty($customerIds)) {
			$fieldBSql = '';
		}

		$companyFieldASql = $fieldASql;
		$companyFieldBSql = $fieldBSql;
		if ($type == \Constants::TYPE_LEAD && in_array('company_name', $ids)) {
			$companyFieldASql = str_replace('company_name', 'name', $companyFieldASql);
			$companyFieldBSql = str_replace('company_name', 'name', $companyFieldBSql);
		}

		if (!empty($ids)) {
			$sqlCompany = " select {$companyFieldASql} company_id from tbl_company where  client_id = " . $clientId;
		} else {
			$sqlCustomer = " select {$customerFieldASql} company_id from tbl_customer where  client_id = " . $clientId;
		}

		while (true) {


			$companyData = [];
			$customerList = [];

			if (!empty($ids)) {
				$companyData = !empty($sqlCompany) ? $db->createCommand($sqlCompany . " and is_archive = 1 and create_time < '{$createTime}' {$companyFieldBSql} order by company_id asc limit {$limit} offset {$offset}")->queryAll() : [];
			}else{
				$customerList = !empty($sqlCustomer) ? $db->createCommand($sqlCustomer . " and is_archive = 1 and create_time < '{$createTime}' {$customerFieldBSql} order by company_id asc limit {$limit} offset {$offset}")->queryAll() : [];
			}


			$companyIds = !empty($companyData) ? array_column($companyData, 'company_id') : array_column($customerList, 'company_id');

			if(empty($companyIds) || empty($redis->get(DuplicateFlagBuilder::REBUILD_DUPLICATE_FLAG_BY_FIELDS[$type] . $clientId))){
				break;
			}

			$count = $db->createCommand($deleteSql . " and company_ids && array[" .implode(',', $companyIds) . "]::bigint[] ")->execute();
			self::info("client_id: {$clientId} delete company_ids count = {$count}.");

			if (empty($customerList) && !empty($customerIds)) {
				$customerList = new CustomerList($clientId);
				$customerList->setFields([$customerFieldASql . 'company_id']);
				$customerList->setCompanyId($companyIds);
				$customerList->setIsArchive(1);
				$customerList = $customerList->find();
			}

			$rules = $this->buildBaseRule($companyData, $type, $ids, \Constants::TYPE_COMPANY);
			if (!empty($customerList)) {
				$rules = $this->buildCustomerRules($customerList, $customerReferType, $customerIds, $rules, \Constants::TYPE_CUSTOMER);
			}

			$tempSql = [];

			foreach ($rules as $key => $item) {
				foreach ($item as $k1 => $i1) {
					foreach ($i1 as $k2 => $i2) {
						$tempSql[] = '(' . $clientId . ', ' . $key . ", '" . $k1 . "', '" . $k2 . "', '" . PgsqlUtil::formatArray(array_unique($i2['company_ids'])) . "')";
					}
				}
			}

			$sqlArray = array_chunk($tempSql, 200);
			foreach ($sqlArray as $sqlArr) {
				$sqlTemp = "INSERT INTO tbl_duplicate_field_relation (client_id, refer_type, field_id, md5_value, company_ids) VALUES " . implode(',', $sqlArr) .
					" ON CONFLICT (client_id, refer_type, field_id, md5_value) DO UPDATE
								SET update_time = CURRENT_TIMESTAMP,
								lead_ids = ARRAY[]::bigint[],
								company_ids = (
								SELECT ARRAY_AGG(DISTINCT val)
								FROM (
									SELECT UNNEST(array_cat(tbl_duplicate_field_relation.company_ids, excluded.company_ids)) AS val
								) AS temp
						)";
				$count = $db->createCommand($sqlTemp)->execute();
				self::info('company insert into tbl_duplicate_field_relation success count = ' . $count);

			}
			$offset += $limit;
		}


		if(!empty(array_intersect($fieldIds, array_merge(array_keys(DuplicateFlagBuilder::$leadIndexMap), ['name'])))) {

			$leadCustomerFieldASql = $customerFieldASql;
			$leadCustomerFieldBSql = $customerFieldBSql;
			$leadFieldASql = $fieldASql;
			$leadFieldBSql = $fieldBSql;
			$lIds = $ids;

			if ($type == \Constants::TYPE_COMPANY && !empty(array_intersect($lIds, ['short_name', 'serial_id', 'fax', 'address']))) {
				$lIds = array_diff($ids, ['short_name', 'serial_id', 'fax', 'address']);

				$deleteSql = $this->buildDeleteDuplicateSql($clientId, $tempType, array_diff($fieldIds, ['short_name', 'serial_id', 'fax', 'address']), $createTime);

				list($leadFieldASql, $leadFieldBSql, ,) = $this->buildSelectFieldSql($lIds, []);
			}

			if(!empty($lIds) && !empty($customerIds)) {
				$leadFieldBSql = '';
			}

			$offset = 0;

			if ($type == \Constants::TYPE_COMPANY && in_array('name', $lIds)) {
				$leadFieldASql = str_replace('name', 'company_name', $leadFieldASql);
				$leadFieldBSql = str_replace('name', 'company_name', $leadFieldBSql);
			}


			if (!empty($lIds)) {
				$sqlLead = " select {$leadFieldASql} lead_id from tbl_lead where  client_id = " . $clientId;
			} else {
				$sqlLeadCustomer = " select {$leadCustomerFieldASql} lead_id from tbl_lead_customer where  client_id = " . $clientId;
			}


			while (true) {

				$leadData = $leadCustomerList = [];

				if (!empty($ids)) {
					$leadData = !empty($sqlLead) ? $db->createCommand($sqlLead . " and is_archive = 1 and create_time < '{$createTime}' {$leadFieldBSql} order by lead_id asc limit {$limit} offset {$offset}")->queryAll() : [];
				} else {
					$leadCustomerList = !empty($sqlLeadCustomer) ? $db->createCommand($sqlLeadCustomer . " and is_archive = 1 and create_time < '{$createTime}' {$leadCustomerFieldBSql} order by lead_id asc limit {$limit} offset {$offset}")->queryAll() : [];
				}

				$leadIds = !empty($leadData) ? array_column($leadData, 'lead_id') : array_column($leadCustomerList, 'lead_id');

				if (empty($leadIds) || empty($redis->get(DuplicateFlagBuilder::REBUILD_DUPLICATE_FLAG_BY_FIELDS[$type] . $clientId))) {
					break;
				}

				$count = $db->createCommand($deleteSql . " and lead_ids && array[" . implode(',', $leadIds) . "]::bigint[] ")->execute();
				self::info("client_id: {$clientId} delete lead_ids count = {$count}.");

				if (empty($leadCustomerList) && !empty($customerIds)) {
					$leadCustomerList = new \common\library\lead\LeadCustomerList($clientId);
					$leadCustomerList->setFields([$leadCustomerFieldASql . 'lead_id']);
					$leadCustomerList->setLeadId($leadIds);
					$leadCustomerList->setIsArchive([1]);
					$leadCustomerList = $leadCustomerList->find();
				}


				$rules = [];

				$rules = $this->buildBaseRule($leadData, $type, $lIds, \Constants::TYPE_LEAD);
				if (!empty($leadCustomerList)) {
					$rules = $this->buildCustomerRules($leadCustomerList, $customerReferType, $customerIds, $rules, \Constants::TYPE_LEAD_CUSTOMER);
				}

				$tempSql = [];

				foreach ($rules as $key => $item) {
					foreach ($item as $k1 => $i1) {
						foreach ($i1 as $k2 => $i2) {
							$tempSql[] = '(' . $clientId . ', ' . $key . ", '" . $k1 . "', '" . $k2 . "', '" . PgsqlUtil::formatArray(array_unique($i2['lead_ids'])) . "')";
						}
					}
				}

				$sqlArray = array_chunk($tempSql, 200);
				foreach ($sqlArray as $sqlArr) {
					$sqlTemp = "INSERT INTO tbl_duplicate_field_relation (client_id, refer_type, field_id, md5_value, lead_ids) VALUES " . implode(',', $sqlArr) .
						" ON CONFLICT (client_id, refer_type, field_id, md5_value) DO UPDATE
								SET update_time = CURRENT_TIMESTAMP,
								lead_ids = (
								SELECT ARRAY_AGG(DISTINCT val)
								FROM (
									SELECT UNNEST(array_cat(tbl_duplicate_field_relation.lead_ids, excluded.lead_ids)) AS val
								) AS temp
						)";
					$count = $db->createCommand($sqlTemp)->execute();
					self::info('lead insert into tbl_duplicate_field_relation success count = ' . $count);
				}
				$offset += $limit;
			}
		}

		$sqlArray = $rules = $leadData = $leadCustomerList  = $companyData = $customerList = [];

		if ($type == \Constants::TYPE_COMPANY) {
			$this->updateCompanyDuplicateFlag($db, $clientId, $fieldIds, $redis, $rebuildFlag, $user_id);
		} else {
			$this->updateLeadDuplicateFlag($db, $clientId, $fieldIds, $redis);
		}

	}


	protected function getValue($referType, $fieldId, $data)
	{
		$fieldMap = [
			\Constants::TYPE_COMPANY => [
				'tel' => 'tel_full'
			],
			\Constants::TYPE_CUSTOMER => [
				'tel_list' => 'full_tel_list',
				'email:domain' => 'email'
			],
			\Constants::TYPE_LEAD => [
				'tel' => 'tel_full'
			],
			\Constants::TYPE_LEAD_CUSTOMER => [
				'tel_list' => 'full_tel_list',
				'email:domain' => 'email'
			],
		];

		$relationFieldId = $fieldMap[$referType][$fieldId] ?? $fieldId;
		if (isset($data[$relationFieldId]))
		{
			$value = $data[$relationFieldId]??'';

			if ($relationFieldId == 'full_tel_list')
			{
				$value = is_array($value) ? $value : array_filter(PgsqlUtil::trimArray($value, true));
			}

			if ($relationFieldId == 'contact')
			{
				$value = is_array($value) ? $value : array_filter(is_array($temp = json_decode($value, true)) ? $temp : []);
			}

		} else {
			$value = $data[$fieldId]??'';
		}

		if ($value === '')
			return $value;

		switch ($fieldId)
		{
			case 'homepage':
				$value = \common\library\custom_field\company_field\duplicate\Helper::fillUrl($value);

				//不规范url不处理
				if (filter_var($value, FILTER_VALIDATE_URL) === false)
					return '';

				//包括 http://<EMAIL> 也不处理
				if (str_contains($value, '@'))
					return '';

				$value = parse_url($value)['host']??$value;
				$value = str_replace(['http://','https://','www.','http:','http'], '', $value);
				$value = strtolower($value);

				break;
			case 'contact':

				if (!is_array($value))
					return '';

				$validContact = [];
				foreach ($value as $contact) {

//					脏数据过滤：必须同时有type、value，且值不为空、非数组
					if (empty($contact['type']) || empty($contact['value']) || is_array($contact['type']) || is_array($contact['value'])) {

						continue;
					}

					$validContact[] = $contact;
				}
				$value = $validContact;

				break;

			case 'email':
			case 'email:domain':

				$value = filter_var(strtolower($value), FILTER_VALIDATE_EMAIL) !== false ? $value : '';

				break;

			default:
				if(is_string($value))
					$value = strtolower($value);

				break;
		}

		return $value?:'';
	}


	//变更规则后需要变更duplicateFlag，这个方法可以复用(todo)
	/**
	 * @throws \CException
	 * @throws \CDbException
	 * @throws \ProcessException
	 */
	protected function updateCompanyDuplicateFlag($db, $clientId, $fieldData, $redis = null, $rebuildFlag = false, $user_id = 0)
	{
		$limit = 2000;
		$offset = 0;
		//客户模块才需要判断客户池
		$client = Client::getClient($clientId);
		$detectKey = DuplicateConstants::MODULE_MAP[\Constants::TYPE_COMPANY]['detecting'];
		$ruleTypeKey = DuplicateConstants::MODULE_MAP[\Constants::TYPE_COMPANY]['rule_type'];
		$duplicateRuleType = (($client->getSettingAttributes()[$detectKey] ?? 0) == \common\library\duplicate\DuplicateConstants::DUPLICATE_CONFLICT_DETECTING) ? DuplicateFlagBuilder::DUPLICATE_ALL_RULE_TYPE : ($client->getSettingAttributes()[$ruleTypeKey] ?? 0);
		//$maxId = $db->createCommand('select company_id from tbl_company where client_id=' . $this->clientId . ' order by company_id desc limit 1')->queryScalar();
		$sqlCompany = 'select company_id from tbl_company where  client_id=' . $clientId;
		while (true) {
			$companyIds = $db->createCommand($sqlCompany . " and is_archive = 1 order by company_id asc limit {$limit} offset {$offset}")->queryColumn();
			if (empty($companyIds) || empty($redis->get(DuplicateFlagBuilder::REBUILD_DUPLICATE_FLAG_BY_FIELDS[\Constants::TYPE_COMPANY] . $clientId))) {
				break;
			}

			$companyFlagNumMap = [];
			$fields = [
				\Constants::TYPE_COMPANY => [
					'name',
					'short_name',
					'tel',
					'homepage',
					'serial_id',
					'fax',
					'address',
				],
				\Constants::TYPE_CUSTOMER => [
					'email',
					'contact',
					'tel_list',
				]
			];

			foreach ($fields as $key => $fieldIds) {
				foreach ($fieldIds as $fieldId) {
					if(!in_array($fieldId, $fieldData)){
						continue;
					}
					$fieldFlagMap = [\Constants::TYPE_COMPANY => [], \Constants::TYPE_CUSTOMER => []];

					if (!in_array($fieldId, ['email', 'contact'])) {
						$sql = "select company_ids, lead_ids from tbl_duplicate_field_relation WHERE client_id = {$clientId} and field_id = '{$fieldId}' and refer_type = {$key} and company_ids && array[" . implode(',', $companyIds) . "]::bigint[] ";
					} else {
						$sql = "select company_ids, lead_ids from tbl_duplicate_field_relation WHERE client_id = {$clientId} and field_id like '%{$fieldId}%' and refer_type = {$key} and company_ids && array[" . implode(',', $companyIds) . "]::bigint[] ";
					}

					$relationList = $db->createCommand($sql)->queryAll();

					if (empty($relationList)) {
						continue;
					}


					if ($duplicateRuleType == DuplicateFlagBuilder::DUPLICATE_POOL_ID_RULE_TYPE) {
						$tempCompanyIds = [];

						foreach ($relationList as $relation) {
							$company_ids = PgsqlUtil::trimArray($relation['company_ids']);
							$tempCompanyIds = array_merge($tempCompanyIds, $company_ids);
						}

						$tempCompanyIdsArr = array_chunk($tempCompanyIds, 2000);
						$tempCompanyPoolId = [];
						foreach ($tempCompanyIdsArr as $tempIds) {
							$tempCompanyPoolId = $tempCompanyPoolId + \common\library\customer\pool\Helper::getPoolIdsByCompanyIds($user_id, array_unique($tempIds));
						}
					}

					foreach ($relationList as $relation) {

						$lead_ids = PgsqlUtil::trimArray($relation['lead_ids']);
						$company_ids = PgsqlUtil::trimArray($relation['company_ids']);
						$companyCount = count($company_ids);


						$rebuildIdsFlagMap = array_fill_keys($companyIds, 0);
						foreach ($rebuildIdsFlagMap as $currentId => $flag) {

							if (!isset($fieldFlagMap[$key][$currentId][$fieldId])) {
								$fieldFlagMap[$key][$currentId][$fieldId] = 0;
							}

							if ($fieldFlagMap[$key][$currentId][$fieldId] == DuplicateFlagBuilder::DUPLICATE_ALL) {
								continue;
							}

							if ($key == \Constants::TYPE_COMPANY || $key == \Constants::TYPE_CUSTOMER) {

								if (!in_array($currentId, $company_ids)) {
									continue;
								}

								//客户池判重
								if ($duplicateRuleType == DuplicateFlagBuilder::DUPLICATE_POOL_ID_RULE_TYPE) {
									$companyCount = DuplicateFlagBuilder::formatSameCompanyPoolNum($currentId, $tempCompanyPoolId, $company_ids);
								}

								//枚举值1是线索和客户都有重复，2是线索重复，3是客户重复
								if (!empty($lead_ids) && $companyCount > 1) {
									$fieldFlagMap[$key][$currentId][$fieldId] = DuplicateFlagBuilder::DUPLICATE_ALL;
									continue;
								}

								if (!empty($lead_ids)) {
									if ($fieldFlagMap[$key][$currentId][$fieldId] == DuplicateFlagBuilder::DUPLICATE_COMPANY) {
										$fieldFlagMap[$key][$currentId][$fieldId] = DuplicateFlagBuilder::DUPLICATE_ALL;
									} else {
										$fieldFlagMap[$key][$currentId][$fieldId] = DuplicateFlagBuilder::DUPLICATE_LEAD;
									}
									continue;
								}

								if ($companyCount > 1) {
									if ($fieldFlagMap[$key][$currentId][$fieldId] == DuplicateFlagBuilder::DUPLICATE_LEAD) {
										$fieldFlagMap[$key][$currentId][$fieldId] = DuplicateFlagBuilder::DUPLICATE_ALL;
									} else {
										$fieldFlagMap[$key][$currentId][$fieldId] = DuplicateFlagBuilder::DUPLICATE_COMPANY;
									}
								}

							} else {
								//枚举值1是线索和客户都有重复，2是线索重复，3是客户重复
								if (!in_array($currentId, $lead_ids)) {
									continue;
								}

								if (!empty($company_ids) && count($lead_ids) > 1) {
									$fieldFlagMap[$key][$currentId][$fieldId] = DuplicateFlagBuilder::DUPLICATE_ALL;
									continue;
								}

								if (count($lead_ids) > 1) {
									if ($fieldFlagMap[$key][$currentId][$fieldId] == DuplicateFlagBuilder::DUPLICATE_COMPANY) {
										$fieldFlagMap[$key][$currentId][$fieldId] = DuplicateFlagBuilder::DUPLICATE_ALL;
									} else {
										$fieldFlagMap[$key][$currentId][$fieldId] = DuplicateFlagBuilder::DUPLICATE_LEAD;
									}
									continue;
								}

								if (!empty($company_ids)) {
									if ($fieldFlagMap[$key][$currentId][$fieldId] == DuplicateFlagBuilder::DUPLICATE_LEAD) {
										$fieldFlagMap[$key][$currentId][$fieldId] = DuplicateFlagBuilder::DUPLICATE_ALL;
									} else {
										$fieldFlagMap[$key][$currentId][$fieldId] = DuplicateFlagBuilder::DUPLICATE_COMPANY;
									}
								}
							}
						}
					}

					$arr = array_chunk($fieldFlagMap[$key], 3000, true);
					foreach ($arr as $item) {
						switch ($key) {
							//重算修改字段的影响
							case \Constants::TYPE_COMPANY:
								$this->refreshCompany($companyFlagNumMap, $item, [], $clientId, true);
								break;
							case \Constants::TYPE_CUSTOMER:
								$this->refreshCompany($companyFlagNumMap, [], $item, $clientId, true);
								break;
							default:
								break;
						}
					}
				}
			}

			array_walk($companyFlagNumMap, function (&$value, $key) {

				$value = '(' . $key . ', ' . $value . ')';
			});
			$sqlArray = array_chunk($companyFlagNumMap, 100);
			foreach ($sqlArray as $sqlArr) {
				if ($rebuildFlag) {
					$sqlTemp = "update tbl_company set duplicate_flag = tmp.tmp_duplicate_flag from (VALUES " . implode(',', $sqlArr) .
						") AS tmp(company_id, tmp_duplicate_flag) WHERE client_id = " . $clientId .
						" AND tbl_company.company_id = tmp.company_id";
				} else {
					$sqlTemp = "update tbl_company set duplicate_flag = (tbl_company.duplicate_flag | tmp.tmp_duplicate_flag) from (VALUES " . implode(',', $sqlArr) .
						") AS tmp(company_id, tmp_duplicate_flag) WHERE client_id = " . $clientId .
						" AND tbl_company.company_id = tmp.company_id and tbl_company.duplicate_flag != (tbl_company.duplicate_flag | tmp.tmp_duplicate_flag) ";
				}
				//echo $sqlTemp;
				$count = $db->createCommand($sqlTemp)->execute();
				self::info('company success count = ' . $count);
			}
			$offset += $limit;
		}
	}

	/**
	 * @throws \ProcessException
	 * @throws \CException
	 * @throws \CDbException
	 */
	protected function updateLeadDuplicateFlag($db, $clientId, $fieldData, $redis)
	{
		$fieldData = in_array('name', $fieldData) ? (array_merge($fieldData , ['company_name'])) : $fieldData;
		$limit = 2000;
		$offset = 0;
		//$maxId = $db->createCommand('select lead_id from tbl_lead where client_id=' . $this->clientId . ' order by lead_id desc limit 1')->queryScalar();
		$sqlLead = 'select lead_id from tbl_lead where  client_id=' . $clientId;
		while (true) {
			$leadIds = $db->createCommand($sqlLead . " and is_archive = 1 order by lead_id asc limit {$limit} offset {$offset}")->queryColumn();
			if (empty($leadIds) || empty($redis->get(DuplicateFlagBuilder::REBUILD_DUPLICATE_FLAG_BY_FIELDS[\Constants::TYPE_LEAD] . $clientId)))
				break;

			$leadFlagNumMap = [];
			$fields = [
				\Constants::TYPE_LEAD => [
					'company_name',
					'tel',
					'homepage',
				],
				\Constants::TYPE_LEAD_CUSTOMER => [
					'email',
					'contact',
					'tel_list',
				]
			];

			//$time = '2023-09-30 00:00:00';
			$adminUserId = PrivilegeService::getInstance($clientId)->getAdminUserId();
			foreach ($fields as $key => $fieldIds) {
				foreach ($fieldIds as $fieldId) {
					if (!in_array($fieldId, $fieldData)) {
						continue;
					}
					$fieldFlagMap = [\Constants::TYPE_LEAD => [], \Constants::TYPE_LEAD_CUSTOMER => []];
					if (!in_array($fieldId, ['email', 'contact'])) {
						$sql = "select company_ids, lead_ids from tbl_duplicate_field_relation WHERE client_id = {$clientId} and field_id = '{$fieldId}' and refer_type = {$key} and lead_ids && array[" . implode(',', $leadIds) . "]::bigint[] ";
					} else {
						$sql = "select company_ids, lead_ids from tbl_duplicate_field_relation WHERE client_id = {$clientId} and field_id like '%{$fieldId}%' and refer_type = {$key} and lead_ids && array[" . implode(',', $leadIds) . "]::bigint[] ";
					}
					$relationList = $db->createCommand($sql)->queryAll();

					if (empty($relationList)) {
						continue;
					}


					foreach ($relationList as $relation) {
						$lead_ids = PgsqlUtil::trimArray($relation['lead_ids']);
						$company_ids = PgsqlUtil::trimArray($relation['company_ids']);
						$rebuildIdsFlagMap = array_fill_keys($leadIds, 0);
						foreach ($rebuildIdsFlagMap as $currentId => $flag) {

							if (!isset($fieldFlagMap[$key][$currentId][$fieldId])) {
								$fieldFlagMap[$key][$currentId][$fieldId] = 0;
							}

							if ($fieldFlagMap[$key][$currentId][$fieldId] == DuplicateFlagBuilder::DUPLICATE_ALL) {
								continue;
							}

							if ($key == \Constants::TYPE_COMPANY || $key == \Constants::TYPE_CUSTOMER) {

								if (!in_array($currentId, $company_ids)) {
									continue;
								}

								//枚举值1是线索和客户都有重复，2是线索重复，3是客户重复
								if (!empty($lead_ids) && count($company_ids) > 1) {
									$fieldFlagMap[$key][$currentId][$fieldId] = DuplicateFlagBuilder::DUPLICATE_ALL;
									continue;
								}

								if (!empty($lead_ids)) {
									if ($fieldFlagMap[$key][$currentId][$fieldId] == DuplicateFlagBuilder::DUPLICATE_COMPANY) {
										$fieldFlagMap[$key][$currentId][$fieldId] = DuplicateFlagBuilder::DUPLICATE_ALL;
									} else {
										$fieldFlagMap[$key][$currentId][$fieldId] = DuplicateFlagBuilder::DUPLICATE_LEAD;
									}
									continue;
								}

								if (count($company_ids) > 1) {
									if ($fieldFlagMap[$key][$currentId][$fieldId] == DuplicateFlagBuilder::DUPLICATE_LEAD) {
										$fieldFlagMap[$key][$currentId][$fieldId] = DuplicateFlagBuilder::DUPLICATE_ALL;
									} else {
										$fieldFlagMap[$key][$currentId][$fieldId] = DuplicateFlagBuilder::DUPLICATE_COMPANY;
									}
								}

							} else {
								//枚举值1是线索和客户都有重复，2是线索重复，3是客户重复
								if (!in_array($currentId, $lead_ids)) {
									continue;
								}

								if (!empty($company_ids) && count($lead_ids) > 1) {
									$fieldFlagMap[$key][$currentId][$fieldId] = DuplicateFlagBuilder::DUPLICATE_ALL;
									continue;
								}

								if (count($lead_ids) > 1) {
									if ($fieldFlagMap[$key][$currentId][$fieldId] == DuplicateFlagBuilder::DUPLICATE_COMPANY) {
										$fieldFlagMap[$key][$currentId][$fieldId] = DuplicateFlagBuilder::DUPLICATE_ALL;
									} else {
										$fieldFlagMap[$key][$currentId][$fieldId] = DuplicateFlagBuilder::DUPLICATE_LEAD;
									}
									continue;
								}

								if (!empty($company_ids)) {
									if ($fieldFlagMap[$key][$currentId][$fieldId] == DuplicateFlagBuilder::DUPLICATE_LEAD) {
										$fieldFlagMap[$key][$currentId][$fieldId] = DuplicateFlagBuilder::DUPLICATE_ALL;
									} else {
										$fieldFlagMap[$key][$currentId][$fieldId] = DuplicateFlagBuilder::DUPLICATE_COMPANY;
									}
								}
							}
						}
					}
					$arr = array_chunk($fieldFlagMap[$key], 3000, true);
					foreach ($arr as $item) {
						switch ($key) {
							case \Constants::TYPE_LEAD:
								$this->refreshLead($leadFlagNumMap, $item, [], $adminUserId, true);
								break;
							case \Constants::TYPE_LEAD_CUSTOMER:
								$this->refreshLead($leadFlagNumMap, [], $item, $adminUserId, true);
								break;
							default:
								break;
						}
					}
				}
			}


			array_walk($leadFlagNumMap, function (&$value, $key) {

				$value = '(' . $key . ', ' . $value . ')';
			});

			$sqlArray = array_chunk($leadFlagNumMap, 100);
			foreach ($sqlArray as $sqlArr) {
				$sqlTemp = "update tbl_lead set duplicate_flag = (tbl_lead.duplicate_flag | tmp.tmp_duplicate_flag) from (VALUES " . implode(',', $sqlArr) .
					") AS tmp(lead_id, tmp_duplicate_flag) WHERE client_id = " . $clientId .
					" AND tbl_lead.lead_id = tmp.lead_id and tbl_lead.duplicate_flag != (tbl_lead.duplicate_flag | tmp.tmp_duplicate_flag) ";
				//echo $sqlTemp;
				$count = $db->createCommand($sqlTemp)->execute();
				self::info('lead success count = ' . $count);
			}

			$offset += $limit;
		}
	}


	protected function buildCustomerRules($customerList, $customerReferType, $customerFieldIds, &$matchRules, $reflectCustomerType)
	{
		if (empty($customerList))
			return [];


		foreach ($customerFieldIds as $fieldId)
		{

			foreach ($customerList as $customerData)
			{
				if (($value = $this->getValue($customerReferType, $fieldId, $customerData)) === '')
					continue;

				if ($fieldId == 'email:domain')
				{
					$value = explode('@', $value)[1] ?? '';
					if (!empty($value) && CommonDomain::check($value)) {
						continue;
					}
				}

				switch ($fieldId)
				{
					case 'tel_list':

						foreach ($value as $tel)
						{

							if($reflectCustomerType == \Constants::TYPE_CUSTOMER) {

								$matchRules[$customerReferType][$fieldId][md5($tel)]['company_ids'][] = $customerData['company_id'];
							}else{
								$matchRules[$customerReferType][$fieldId][md5($tel)]['lead_ids'][] = $customerData['lead_id'];
							}

						}
						break;
					case 'contact':

						foreach ($value as $contact)
						{

							//有脏数据
							if (!isset($contact['value']) || is_array($contact['value']) || !isset($contact['type']))
								continue;

							if (($contact['type']) == 'whatsapp') {
								$contact['value'] = preg_replace('/\D/', '', $contact['value']);
								if (empty($contact['value'])) {
									continue;
								}
							}

							if($reflectCustomerType == \Constants::TYPE_CUSTOMER) {
								$matchRules[$customerReferType][$fieldId . ':' . $contact['type']][md5($contact['value'])]['company_ids'][] = $customerData['company_id'];
							}else{
								$matchRules[$customerReferType][$fieldId . ':' . $contact['type']][md5($contact['value'])]['lead_ids'][] = $customerData['lead_id'];
							}

						}
						break;
					default:
						if ($reflectCustomerType == \Constants::TYPE_CUSTOMER) {
							$matchRules[$customerReferType][$fieldId][md5($value)]['company_ids'][] = $customerData['company_id'];
						} else {
							$matchRules[$customerReferType][$fieldId][md5($value)]['lead_ids'][] = $customerData['lead_id'];
						}
						break;
				}
			}

		}

		return $matchRules;
	}


	protected function buildBaseRule($companyList, $referType, $fieldIds, $reflectType)
	{
		if (empty($companyList))
			return [];

		$matchRules = [];
		foreach ($fieldIds as $fieldId)
		{
			foreach ($companyList as $data) {

				if (($value = $this->getValue($referType, $fieldId, $data)) === '') {
					continue;
				}

				if($reflectType == \Constants::TYPE_COMPANY) {
					$matchRules[$referType][$fieldId][md5($value)]['company_ids'][] = $data['company_id'];
				}else{
					$matchRules[$referType][$fieldId][md5($value)]['lead_ids'][] = $data['lead_id'];
				}
			}
		}

		return $matchRules;
	}


	protected function refreshCompany(&$companyFlagNumMap, $companyFlagMap, $customerFlagMap, $clientId, $companyFlag = false)
	{

		foreach ($companyFlagMap as $companyId => $fieldFlags) {
			foreach ($fieldFlags as $fieldId => $flag) {
				$companyFlagNumMap[$companyId] = DuplicateFlagBuilder::setValue($companyFlag ? ($companyFlagNumMap[$companyId] ?? 0) : 0, $fieldId, $flag, \Constants::TYPE_COMPANY);
			}
		}

		foreach ($customerFlagMap as $companyId => $fieldFlags) {
			foreach ($fieldFlags as $fieldId => $flag) {
				$companyFlagNumMap[$companyId] = DuplicateFlagBuilder::setValue($companyFlag ? ($companyFlagNumMap[$companyId] ?? 0) : 0, $fieldId, $flag, \Constants::TYPE_CUSTOMER);
			}
		}


		$idsArr = array_chunk(array_unique(array_keys($companyFlagNumMap)), 100);
		foreach($idsArr as $itemIds) {
			(new SwarmService($clientId))->setSkipUpdateSearch()->refreshByRefer($itemIds, ['duplicate_flag'], getenv('RUNTIME_ENV') == 'k8s');
		}

		return true;
	}

	/**
	 * @throws \CDbException
	 * @throws \ProcessException
	 */
	protected function refreshLead(&$leadFlagNumMap, $leadFlagMap, $leadCustomerFlagMap, $userId, $leadFlag = false)
	{

		foreach ($leadFlagMap as $leadId => $fieldFlags) {
			foreach ($fieldFlags as $fieldId => $flag) {
				$leadFlagNumMap[$leadId] = DuplicateFlagBuilder::setValue($leadFlag ? ($leadFlagNumMap[$leadId] ?? 0) : 0, $fieldId, $flag, \Constants::TYPE_LEAD);
			}
		}

		foreach ($leadCustomerFlagMap as $leadId => $fieldFlags) {
			foreach ($fieldFlags as $fieldId => $flag) {
				$leadFlagNumMap[$leadId] = DuplicateFlagBuilder::setValue($leadFlag ? ($leadFlagNumMap[$leadId] ?? 0) : 0, $fieldId, $flag, \Constants::TYPE_LEAD_CUSTOMER);
			}
		}

		return true;
	}

	protected function buildDeleteDuplicateSql($clientId, $type, $fieldIds, $createTime){

		$deleteSql = " delete from tbl_duplicate_field_relation where client_id = {$clientId} and create_time < '{$createTime}' ";
		$tempSql = count($type) > 1 ? (" AND refer_type in (" . implode(',', $type) . ")") : " AND refer_type = {$type[0]} ";
		$deleteSql .= $tempSql;

		if(in_array(\Constants::TYPE_LEAD, $type) && in_array('name', $fieldIds)){
			$fieldIds[] = 'company_name';
		}

		if(in_array('email', $fieldIds)){
			$fieldIds[] = 'email:domain';
		}

		$fieldIds = array_values($fieldIds);
		if (count($fieldIds) == 1) {
			$deleteSql .= in_array('contact', $fieldIds) ? " and field_id like 'contact%' "  : " and field_id = '{$fieldIds[0]}' " ;
		} else {
			$deleteSql .= !in_array('contact', $fieldIds) ? (" and field_id in ('" . implode("','", $fieldIds) . "' ) ") : (" and ( field_id in ('" . implode("','", $fieldIds) . "' ) or field_id like 'contact%') ");
		}

		return $deleteSql;
	}


	protected function buildIdsAndCustomerIds($fieldIds, $type, $fieldUniqueMap): array
	{

		$ids = $customerIds = [];
		foreach ($fieldIds as $v) {

			if (in_array($v, array_unique(array_merge($fieldUniqueMap[$type] , ['name'])))) {
				if ($type == \Constants::TYPE_LEAD && $v == 'name') {
					$ids[] = 'company_name';
				} else {
					$ids[] = $v;
				}
			} else {
				if (in_array('email', $fieldIds)) {
					$customerIds[] = 'email:domain';
				}
				$customerIds[] = $v;
			}

		}
		return [$ids, $customerIds];
	}


	protected function buildSelectFieldSql($ids, $customerIds){
		$fieldASql = $fieldBSql =  $customerFieldASql = $customerFieldBSql = '';
		foreach ($ids as $key => $id) {
			if ($id == 'tel') {
				$fieldASql .= 'tel_full' . ", ";
			} else {
				$fieldASql .= $id . ", ";
			}
			$fieldBSql  = empty($fieldBSql) ? ((count($ids) == 1) ? " and ( $id != '' ) " : " and ( $id != '' ") : ( ((count($ids) - 1) == $key) ?  $fieldBSql . " or $id != '') " : $fieldBSql . " or $id != '' ");
		}

		$temp = array_diff($customerIds, ['email:domain']);

		foreach ($customerIds as $key => $id) {
			if($id == 'email:domain'){
				continue;
			}
			if ($id == 'tel_list') {
				$customerFieldASql .= 'full_tel_list' . ", ";
			} else {
				$customerFieldASql .= $id . ", ";
			}
			if ($id == 'email') {
				$customerFieldBSql = empty($customerFieldBSql) ? ((count($temp) == 1) ? " and ( $id != '' ) " : " and ( $id != '' ") : ( ((count($customerIds) - 1) == $key) ?  ($customerFieldBSql . " or $id != '' ") : ($customerFieldBSql . " or $id != '' ) "));
			} else {
				$customerFieldBSql = empty($customerFieldBSql) ? ((count($customerIds) == 1) ? " and ( $id != '{}' ) " : " and ( $id != '{}' ") : ( ((count($customerIds) - 1) == $key) ?  ($customerFieldBSql . " or $id != '{}' )") : ($customerFieldBSql . " or $id != '{}'"));
			}
		}
		return [$fieldASql, $fieldBSql, $customerFieldASql, $customerFieldBSql];
	}

	/**
	 * @throws CException
	 * @throws CDbException
	 * @throws ProcessException
	 */
	public function actionRefreshDuplicateFlagRuleChange($client_id, $user_id, $type)
	{

		$clientObject = \common\library\account\Client::getClient($client_id);
		$user = $clientObject->getMasterUser();
		User::setLoginUser($user);

		$db = PgActiveRecord::getDbByClientId($client_id);
		$redis = \RedisService::cache();
		$redis->set(DuplicateFlagBuilder::REBUILD_DUPLICATE_FLAG_BY_FIELDS[$type] . $client_id, 1, 'Ex', 60 * 60 * 2 );
		try {
			switch ($type) {
				case \Constants::TYPE_COMPANY:
					$fieldData = array_keys(DuplicateFlagBuilder::$indexMap);
					$result = \common\library\custom_field\Helper::getFields($client_id, CustomFieldService::DUPLICATE_FIELD_TYPE[$type], $fieldData);
					$result = array_filter($result, function ($v) {
						return !($v['type'] == \Constants::TYPE_CUSTOMER && $v['id'] == 'name');
					});

					$unique_check_field_ids = array_column(array_filter($result, function ($v){
						return $v['unique_check'] == 1;
					}), 'id');
					if(!empty($unique_check_field_ids)) {
						$this->updateDuplicateRelation($db, $client_id, $unique_check_field_ids, $type, $redis, $user->getUserId(), true);
					}
					$not_unique_check_field_ids = array_column(array_filter($result, function ($v){
						return $v['unique_check'] == 0;
					}), 'id');
					if (!empty($not_unique_check_field_ids)) {
						$this->updateDuplicateFlagByNotUnique($db, $client_id, $not_unique_check_field_ids, $type, $redis, $user->getUserId());
					}
					break;
				default:
					break;
			}
		} catch (Exception $e) {
			\common\library\report\error\ErrorReport::phpError(new \CExceptionEvent(null, $e), $e->getTrace());
		} finally {
			$redis->del([DuplicateFlagBuilder::REBUILD_DUPLICATE_FLAG_BY_FIELDS[$type] . $client_id]);
			\common\library\account\Helper::setClientExternalValue($client_id, DuplicateFlagBuilder::DUPLICATE_EXTERNAL_MAP[$type], 0);
			Browser::push($user_id, DuplicateFlagBuilder::DUPLICATE_BROWSER_MAP[$type], 0);
		}

	}


}
