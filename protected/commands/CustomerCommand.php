<?php

/**
 * Created by PhpStorm.
 * User: andy
 * Date: 2015/5/15
 * Time: 20:26
 */

use common\library\alibaba\Constant;
use common\library\alibaba\customer\CustomerSyncHelper;
use common\library\custom_field\CustomFieldService;
use common\library\customer\Helper;
use common\library\customer\orm\CompanyMetadata;
use common\library\customer_v3\company\CompanyService;
use common\library\email_identity\sync\CustomerSync;
use common\library\futong_transfer\FTImportExecutor;
use common\library\history\customer\BatchCompanyBuilder;
use common\library\import\Import;
use common\library\invoice\InvoiceService;
use common\library\object\field\service\ScopeUserFieldService;
use common\library\privilege_v2\ClientPermission;
use common\library\privilege_v2\PrivilegeConstants;
use common\library\privilege_v2\PrivilegePermission;
use common\library\privilege_v3\PrivilegeService;
use common\library\server\es_search\SearchQueueService;
use common\library\setting\item\ItemSettingConstant;
use common\library\setting\user\UserSetting;
use common\library\sns\customer\CustomerContactHelper;
use common\library\swarm\SwarmService;
use common\library\util\PgsqlUtil;
use common\library\util\Speed;
use common\library\util\SqlUtil;
use common\library\workflow\WorkflowConstant;
use common\models\client\CompanyHistoryPg;

class CustomerCommand extends CrontabCommand
{
    const WILLMOVEPUBLICDAY = 7;//将要移入公海的天数
    
    
    public function actionFlush($clientIds, $grey = 1, $greyNum = null) {
       
        if ($clientIds) {
        
            $clientIds = explode(',', $clientIds);
        } elseif ($grey && !is_null($greyNum)) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }
    
        foreach ($clientIds as $clientId) {
        
            $db = ProjectActiveRecord::getDbByClientId($clientId);
        
            if (empty($db)) {
                self::info(("[{$clientId}] empty db continue"));
                continue;
            }

//            $db = PgActiveRecord::getDbByClientId($clientId);
//
//            if (empty($db)) {
//                self::info(("[{$clientId}] empty db continue"));
//                continue;
//            }
        
            $adminUserId = PrivilegeService::getInstance($clientId)->getAdminUserId();
        
            if (empty($adminUserId)) {
            
                self::info(("[{$clientId}][{$adminUserId}] empty adminUserId continue'"));
                continue;
            }
        
            $client = new \common\library\account\Client($clientId);
        
            if ($client->isNew() || $client->mysql_set_id == 0) {
            
                LogUtil::info("clientId:{$clientId},client not exist or mysql_set_id = 0");
                continue;
            }
        
            self::info(("[{$clientId}][{$adminUserId}] start"));
    
            $privilegeField = new \common\library\privilege_v3\PrivilegeField($clientId);
            $privilegeField->flushCache();
        
            self::info(("[{$clientId}][{$adminUserId}] end"));
        }
    
        self::info(("end all"));
    }
    
    public function actionFixSwarmSelectData($clientIds, $grey = 1, $greyNum = null) {
    
        if ($clientIds) {
        
            $clientIds = explode(',', $clientIds);
        } elseif ($grey && !is_null($greyNum)) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }
    
        foreach ($clientIds as $clientId) {
        
            $db = ProjectActiveRecord::getDbByClientId($clientId);
        
            if (empty($db)) {
                self::info(("[{$clientId}] empty db continue"));
                continue;
            }

//            $db = PgActiveRecord::getDbByClientId($clientId);
//
//            if (empty($db)) {
//                self::info(("[{$clientId}] empty db continue"));
//                continue;
//            }
        
            $adminUserId = PrivilegeService::getInstance($clientId)->getAdminUserId();
            
            if (empty($adminUserId)) {
            
                self::info(("[{$clientId}][{$adminUserId}] empty adminUserId continue'"));
                continue;
            }
        
            $client = new \common\library\account\Client($clientId);
        
            if ($client->isNew() || $client->mysql_set_id == 0) {
            
                LogUtil::info("clientId:{$clientId},client not exist or mysql_set_id = 0");
                continue;
            }
        
            self::info(("[{$clientId}][{$adminUserId}] start"));
    
            User::setLoginUserById($adminUserId);
    
            $user = User::getLoginUser();
            
            $selectField = [];

            $filterFieldList =\common\library\setting\library\swarm\SwarmApi::getWrapRelateConfig($user->getClientId(), $user->getUserId());
    
            foreach ($filterFieldList as $filterField) {
    
                foreach ($filterField['config_info'] as $config) {
                    
                    $config['field_type'] == 3 && $selectField[$config['refer_type']][] = $config['id'];
                }
            }
            
    
    
            $sql = 'SELECT t2.item_id, t2.ext_key, t2.ext_value, t1.item_id as swarm_id
                    FROM tbl_item_setting t1
                             INNER JOIN tbl_item_setting_external t2 ON t1.item_id = t2.item_id AND t2.enable_flag = 1 AND t2.item_type = 15
                    WHERE t1.client_id = '.$clientId.'
                      AND t1.item_type = 15
                      AND t1.enable_flag = 1
                      AND ext_value <> \'\'
                      AND ext_key IN (\'criteria_field_desc\', \'filters\')';
    
            $list = $db->createCommand($sql)->queryAll();
    
            $swarmId = [];
            
            foreach ($list ?? [] as $item) {
                
                $update = false;
                
                $item['ext_value'] = json_decode($item['ext_value'] ?? [], true) ?? [];
    
                if (empty($item['ext_value'])) {
                    
                    continue;
                }
    
                foreach ($item['ext_value'] as &$ext) {
    
                    if (empty($ext['field']) || empty($ext['refer_type']) ||  empty($ext['field_type'])) {
    
                        continue;
                    }
                    if (in_array($ext['field'], $selectField[$ext['refer_type']]) && (($ext['field_type'] ?? 0) == 7)) {
    
                        $update = true;
                        
                        $swarmId[] = $item['swarm_id'];
                        
                        $ext['field_type'] = 3;
                        
                        if (is_array($ext['value'])
                            && !empty($ext['value'][0])
                            && in_array($ext['operator'], [WorkflowConstant::FILTER_OPERATOR_NOT_EQUAL, WorkflowConstant::FILTER_OPERATOR_EQUAL])) {
        
                            $ext['value'] = $ext['value'][0];
                        }
                    }
                }
    
                if ($update) {
    
                    $sql = "UPDATE tbl_item_setting_external set ext_value = '" . json_encode( $item['ext_value']) . "' WHERE ext_key = '{$item['ext_key']}' AND client_id = {$clientId} AND item_id = {$item['item_id']}";
    
                    $res = $db->createCommand($sql)->execute();
                }

            }
    
            ItemSettingConstant::refreshAllCache($clientId);
            
            !empty($swarmId) && (new SwarmService($clientId))->refreshByRules($swarmId);
        
            self::info(("[{$clientId}][{$adminUserId}] end"));
        }
    
        self::info(("end all"));
        
    }
    
    
	public function actionFixChunk(int $dryRun = 1) {

		$file = '[{"client_id":"3","company_id":"8730049206"},{"client_id":"3","company_id":"8744587232"},{"client_id":"21","company_id":"*********"},{"client_id":"29","company_id":"*********"},{"client_id":"29","company_id":"*********"},{"client_id":"66","company_id":"4076657145"},{"client_id":"81","company_id":"*********"},{"client_id":"81","company_id":"*********"},{"client_id":"81","company_id":"*********"},{"client_id":"120","company_id":"*********"},{"client_id":"135","company_id":"19373541590"},{"client_id":"193","company_id":"*********"},{"client_id":"270","company_id":"*********"},{"client_id":"270","company_id":"*********"},{"client_id":"279","company_id":"340489114712"},{"client_id":"299","company_id":"*********"},{"client_id":"354","company_id":"*********"},{"client_id":"354","company_id":"*********"},{"client_id":"398","company_id":"*********"},{"client_id":"406","company_id":"*********"},{"client_id":"486","company_id":"*********"},{"client_id":"502","company_id":"*********"},{"client_id":"537","company_id":"10618948206"},{"client_id":"645","company_id":"395342830482"},{"client_id":"645","company_id":"335406283881"},{"client_id":"651","company_id":"8279014513"},{"client_id":"651","company_id":"*********"},{"client_id":"651","company_id":"20750155879"},{"client_id":"651","company_id":"4760473664"},{"client_id":"651","company_id":"18537912122"},{"client_id":"651","company_id":"11068441997"},{"client_id":"651","company_id":"1178096768"},{"client_id":"677","company_id":"19153009124"},{"client_id":"691","company_id":"1719352383"},{"client_id":"789","company_id":"21877727252"},{"client_id":"789","company_id":"1808259532"},{"client_id":"795","company_id":"*********"},{"client_id":"925","company_id":"*********"},{"client_id":"927","company_id":"8991283935"},{"client_id":"981","company_id":"2011347036"},{"client_id":"999","company_id":"*********"},{"client_id":"1034","company_id":"*********"},{"client_id":"1077","company_id":"3633141913"},{"client_id":"1092","company_id":"*********"},{"client_id":"1092","company_id":"14846725040"},{"client_id":"1092","company_id":"*********"},{"client_id":"1155","company_id":"*********"},{"client_id":"1196","company_id":"*********"},{"client_id":"1219","company_id":"*********"},{"client_id":"1243","company_id":"*********"},{"client_id":"1244","company_id":"3392918823"},{"client_id":"1251","company_id":"1314570199378"},{"client_id":"1257","company_id":"4833288837"},{"client_id":"1258","company_id":"*********"},{"client_id":"1265","company_id":"2268165736"},{"client_id":"1265","company_id":"*********"},{"client_id":"1268","company_id":"10828552963"},{"client_id":"1290","company_id":"*********"},{"client_id":"1379","company_id":"*********"},{"client_id":"1409","company_id":"*********"},{"client_id":"1459","company_id":"400201921305"},{"client_id":"1560","company_id":"*********"},{"client_id":"1617","company_id":"*********"},{"client_id":"1662","company_id":"3650147550"},{"client_id":"1662","company_id":"*********"},{"client_id":"1662","company_id":"2576511756969"},{"client_id":"1662","company_id":"2576508457942"},{"client_id":"1662","company_id":"21117363548"},{"client_id":"1662","company_id":"*********"},{"client_id":"1662","company_id":"*********"},{"client_id":"1662","company_id":"21718993435"},{"client_id":"1662","company_id":"20916023736"},{"client_id":"1662","company_id":"2576505093316"},{"client_id":"1662","company_id":"2576510154664"},{"client_id":"1662","company_id":"2576506127371"},{"client_id":"1662","company_id":"20951381013"},{"client_id":"1662","company_id":"21041369793"},{"client_id":"1693","company_id":"*********"},{"client_id":"1693","company_id":"*********"},{"client_id":"1711","company_id":"4846658042"},{"client_id":"1843","company_id":"*********"},{"client_id":"1871","company_id":"7521369302"},{"client_id":"1871","company_id":"7080587821"},{"client_id":"1871","company_id":"*********"},{"client_id":"1878","company_id":"*********"},{"client_id":"1925","company_id":"2954052436"},{"client_id":"1954","company_id":"272372254999"},{"client_id":"1954","company_id":"229032267941"},{"client_id":"1954","company_id":"271376097086"},{"client_id":"1954","company_id":"597991484996"},{"client_id":"1954","company_id":"4420361050676"},{"client_id":"1954","company_id":"487530215358"},{"client_id":"1954","company_id":"*********"},{"client_id":"1954","company_id":"4484403095136"},{"client_id":"1954","company_id":"140235145884"},{"client_id":"1954","company_id":"*********"},{"client_id":"1954","company_id":"209832381690"},{"client_id":"1954","company_id":"353243686695"},{"client_id":"1985","company_id":"5300981362"},{"client_id":"2067","company_id":"*********"},{"client_id":"2074","company_id":"5996973529"},{"client_id":"2074","company_id":"8026780727"},{"client_id":"2085","company_id":"19469375783"},{"client_id":"2147","company_id":"*********"},{"client_id":"2147","company_id":"*********"},{"client_id":"2147","company_id":"*********"},{"client_id":"2147","company_id":"*********"},{"client_id":"2147","company_id":"*********"},{"client_id":"2147","company_id":"*********"},{"client_id":"2147","company_id":"*********"},{"client_id":"2147","company_id":"*********"},{"client_id":"2147","company_id":"*********"},{"client_id":"2147","company_id":"*********"},{"client_id":"2152","company_id":"5074828141"},{"client_id":"2177","company_id":"*********"},{"client_id":"2213","company_id":"2498412728"},{"client_id":"2230","company_id":"*********"},{"client_id":"2315","company_id":"3084095522"},{"client_id":"2315","company_id":"3090130013"},{"client_id":"2319","company_id":"10997412257"},{"client_id":"2418","company_id":"1143960616"},{"client_id":"2447","company_id":"1139092143"},{"client_id":"2478","company_id":"2937319072"},{"client_id":"2530","company_id":"1248965244"},{"client_id":"2530","company_id":"1248974665"},{"client_id":"2530","company_id":"1248936975"},{"client_id":"2530","company_id":"1248946400"},{"client_id":"2530","company_id":"1249050503"},{"client_id":"2530","company_id":"1248792097"},{"client_id":"2530","company_id":"1249102988"},{"client_id":"2530","company_id":"1249010075"},{"client_id":"2530","company_id":"1249023615"},{"client_id":"2530","company_id":"1248843128"},{"client_id":"2530","company_id":"1249032346"},{"client_id":"2570","company_id":"12013496946"},{"client_id":"2570","company_id":"12014006310"},{"client_id":"2582","company_id":"16954134479"},{"client_id":"2620","company_id":"708350185332"},{"client_id":"2737","company_id":"1487350541"},{"client_id":"2781","company_id":"1404986249"},{"client_id":"2781","company_id":"1404985695"},{"client_id":"2781","company_id":"1404985663"},{"client_id":"2781","company_id":"1404986827"},{"client_id":"2801","company_id":"1599043316"},{"client_id":"2852","company_id":"1903718462"},{"client_id":"2852","company_id":"1950165291"},{"client_id":"2852","company_id":"1631729993"},{"client_id":"2875","company_id":"9925174796"},{"client_id":"2875","company_id":"115779417142"},{"client_id":"2910","company_id":"2476116885"},{"client_id":"2975","company_id":"9560845667"},{"client_id":"3007","company_id":"1536258841"},{"client_id":"3007","company_id":"1536267370"},{"client_id":"3007","company_id":"1536270049"},{"client_id":"3093","company_id":"8284943852"},{"client_id":"3140","company_id":"16664862400"},{"client_id":"3140","company_id":"16664653090"},{"client_id":"3140","company_id":"16664591937"},{"client_id":"3178","company_id":"4615677291"},{"client_id":"3207","company_id":"21496267474"},{"client_id":"3207","company_id":"1731106891571"},{"client_id":"3207","company_id":"4409367614"},{"client_id":"3207","company_id":"19289699883"},{"client_id":"3214","company_id":"1294111177302"},{"client_id":"3214","company_id":"2782069811578"},{"client_id":"3214","company_id":"2458368176813"},{"client_id":"3214","company_id":"2607728511936"},{"client_id":"3214","company_id":"3750148553951"},{"client_id":"3214","company_id":"2043785337968"},{"client_id":"3227","company_id":"4232002461"},{"client_id":"3228","company_id":"8410164673"},{"client_id":"3278","company_id":"4734642297"},{"client_id":"3278","company_id":"4640185937"},{"client_id":"3278","company_id":"5056788568"},{"client_id":"3278","company_id":"4978919445"},{"client_id":"3278","company_id":"4599824478"},{"client_id":"3278","company_id":"4596069100"},{"client_id":"3278","company_id":"4653126197"},{"client_id":"3278","company_id":"4657315034"},{"client_id":"3278","company_id":"4659617132"},{"client_id":"3278","company_id":"4982234794"},{"client_id":"3278","company_id":"4645846160"},{"client_id":"3278","company_id":"4649976114"},{"client_id":"3278","company_id":"4580755664"},{"client_id":"3278","company_id":"5074481097"},{"client_id":"3278","company_id":"4593574701"},{"client_id":"3338","company_id":"12231152012"},{"client_id":"3370","company_id":"15774019182"},{"client_id":"3373","company_id":"1742681511"},{"client_id":"3378","company_id":"1821109579"},{"client_id":"3396","company_id":"2192120170"},{"client_id":"3421","company_id":"6383744944"},{"client_id":"3438","company_id":"4210087090"},{"client_id":"3439","company_id":"2154460302"},{"client_id":"3450","company_id":"12120563242"},{"client_id":"3504","company_id":"1823461061"},{"client_id":"3573","company_id":"2505799673"},{"client_id":"3573","company_id":"2505799478"},{"client_id":"3573","company_id":"2505800605"},{"client_id":"3573","company_id":"2505799415"},{"client_id":"3573","company_id":"2505799429"},{"client_id":"3573","company_id":"2494080840"},{"client_id":"3573","company_id":"2505799096"},{"client_id":"3573","company_id":"2505800710"},{"client_id":"3596","company_id":"10403233657"},{"client_id":"3618","company_id":"1860374629"},{"client_id":"3727","company_id":"1940087411"},{"client_id":"3727","company_id":"1940102916"},{"client_id":"3727","company_id":"1940103022"},{"client_id":"3727","company_id":"1940078599"},{"client_id":"3794","company_id":"3587728336"},{"client_id":"3804","company_id":"2210722363"},{"client_id":"3817","company_id":"2045484880"},{"client_id":"3817","company_id":"2045487063"},{"client_id":"3817","company_id":"2045474293"},{"client_id":"3864","company_id":"2053645636"},{"client_id":"3886","company_id":"20295021067"},{"client_id":"3886","company_id":"23277024125"},{"client_id":"3886","company_id":"20546662420"},{"client_id":"3886","company_id":"20700892126"},{"client_id":"3886","company_id":"20866269336"},{"client_id":"3886","company_id":"139504107839"},{"client_id":"3886","company_id":"20845148357"},{"client_id":"3886","company_id":"22715590817"},{"client_id":"3886","company_id":"151951690525"},{"client_id":"3886","company_id":"354271477610"},{"client_id":"3886","company_id":"24776818094"},{"client_id":"3886","company_id":"24630337116"},{"client_id":"3886","company_id":"20746168869"},{"client_id":"3886","company_id":"25563545900"},{"client_id":"3886","company_id":"23575695839"},{"client_id":"3886","company_id":"22416679005"},{"client_id":"3886","company_id":"43634037043"},{"client_id":"3886","company_id":"20245305135"},{"client_id":"3886","company_id":"24021707537"},{"client_id":"3886","company_id":"20431580574"},{"client_id":"3886","company_id":"25431440921"},{"client_id":"3886","company_id":"22407266179"},{"client_id":"3886","company_id":"25409940017"},{"client_id":"3886","company_id":"21829800251"},{"client_id":"3886","company_id":"20745535234"},{"client_id":"3886","company_id":"20433050316"},{"client_id":"3886","company_id":"20719307046"},{"client_id":"3886","company_id":"24407960185"},{"client_id":"3886","company_id":"21154326472"},{"client_id":"3886","company_id":"22651135910"},{"client_id":"3886","company_id":"24204892061"},{"client_id":"3886","company_id":"40268200380"},{"client_id":"3886","company_id":"20489675729"},{"client_id":"3886","company_id":"20793312593"},{"client_id":"3886","company_id":"380545689105"},{"client_id":"3886","company_id":"22075291265"},{"client_id":"3886","company_id":"24205138684"},{"client_id":"3886","company_id":"275743812334"},{"client_id":"3886","company_id":"25171549757"},{"client_id":"3886","company_id":"22911982453"},{"client_id":"3886","company_id":"21029931537"},{"client_id":"3886","company_id":"21980538274"},{"client_id":"3886","company_id":"20276400290"},{"client_id":"3886","company_id":"376871407178"},{"client_id":"3886","company_id":"357547798620"},{"client_id":"3886","company_id":"20741872730"},{"client_id":"3886","company_id":"25612152450"},{"client_id":"3886","company_id":"20793244416"},{"client_id":"3886","company_id":"385335905339"},{"client_id":"3886","company_id":"194371871909"},{"client_id":"3886","company_id":"137435790550"},{"client_id":"3886","company_id":"23691290679"},{"client_id":"3886","company_id":"21870713121"},{"client_id":"3886","company_id":"25468496119"},{"client_id":"3886","company_id":"21422575901"},{"client_id":"3886","company_id":"22857405007"},{"client_id":"3886","company_id":"23441209739"},{"client_id":"3886","company_id":"25481774078"},{"client_id":"3886","company_id":"24873820618"},{"client_id":"3886","company_id":"45471559743"},{"client_id":"3886","company_id":"23821793375"},{"client_id":"3886","company_id":"20996747757"},{"client_id":"3886","company_id":"21594786666"},{"client_id":"3886","company_id":"326524794317"},{"client_id":"3886","company_id":"21445687464"},{"client_id":"3886","company_id":"24966051577"},{"client_id":"3886","company_id":"22266807671"},{"client_id":"3886","company_id":"181711430985"},{"client_id":"3886","company_id":"24268253852"},{"client_id":"3886","company_id":"268754612865"},{"client_id":"3886","company_id":"157997141752"},{"client_id":"3886","company_id":"21030864474"},{"client_id":"3886","company_id":"24833991776"},{"client_id":"3886","company_id":"21405949365"},{"client_id":"3886","company_id":"21346035695"},{"client_id":"3886","company_id":"1697932807617"},{"client_id":"3886","company_id":"235394448531"},{"client_id":"3886","company_id":"414290807353"},{"client_id":"3886","company_id":"22406480574"},{"client_id":"3886","company_id":"20721278295"},{"client_id":"3886","company_id":"24160437558"},{"client_id":"3886","company_id":"25124095981"},{"client_id":"3886","company_id":"24625623187"},{"client_id":"3886","company_id":"27555647477"},{"client_id":"3886","company_id":"20797548734"},{"client_id":"3886","company_id":"24963178756"},{"client_id":"3886","company_id":"94690240139"},{"client_id":"3886","company_id":"194376367867"},{"client_id":"3886","company_id":"357562043123"},{"client_id":"3886","company_id":"22927284759"},{"client_id":"3886","company_id":"238405678400"},{"client_id":"3886","company_id":"24770302072"},{"client_id":"3886","company_id":"21442570625"},{"client_id":"3886","company_id":"22392863699"},{"client_id":"3886","company_id":"20248166784"},{"client_id":"3886","company_id":"20821674442"},{"client_id":"3886","company_id":"20997102322"},{"client_id":"3886","company_id":"354242649060"},{"client_id":"3886","company_id":"354169816024"},{"client_id":"3886","company_id":"238420691308"},{"client_id":"3886","company_id":"20164255474"},{"client_id":"3886","company_id":"24626900582"},{"client_id":"3886","company_id":"134080643645"},{"client_id":"3886","company_id":"7920851019"},{"client_id":"3886","company_id":"20719104270"},{"client_id":"3886","company_id":"20245073221"},{"client_id":"3886","company_id":"121997242373"},{"client_id":"3886","company_id":"20484549208"},{"client_id":"3886","company_id":"30719106260"},{"client_id":"3886","company_id":"352664742789"},{"client_id":"3886","company_id":"357645492816"},{"client_id":"3886","company_id":"20822311840"},{"client_id":"3886","company_id":"20861236324"},{"client_id":"3886","company_id":"20862850684"},{"client_id":"3886","company_id":"24240757225"},{"client_id":"3886","company_id":"20478016014"},{"client_id":"3886","company_id":"20882713377"},{"client_id":"3886","company_id":"275654251346"},{"client_id":"3886","company_id":"384549557864"},{"client_id":"3886","company_id":"20596742352"},{"client_id":"3886","company_id":"20274400917"},{"client_id":"3886","company_id":"23334058609"},{"client_id":"3886","company_id":"21714406845"},{"client_id":"3886","company_id":"30206012491"},{"client_id":"3886","company_id":"21489490322"},{"client_id":"3886","company_id":"305120330453"},{"client_id":"3886","company_id":"20272218652"},{"client_id":"3886","company_id":"20432833204"},{"client_id":"3886","company_id":"20158234950"},{"client_id":"3886","company_id":"23793178233"},{"client_id":"3886","company_id":"20863447439"},{"client_id":"3886","company_id":"43389998530"},{"client_id":"3886","company_id":"21843458618"},{"client_id":"3886","company_id":"24417862894"},{"client_id":"3886","company_id":"20512691977"},{"client_id":"3886","company_id":"20456923889"},{"client_id":"3886","company_id":"23793479910"},{"client_id":"3886","company_id":"27556500451"},{"client_id":"3886","company_id":"20410102332"},{"client_id":"3886","company_id":"16609665525"},{"client_id":"3886","company_id":"22666796324"},{"client_id":"3886","company_id":"21489410461"},{"client_id":"3886","company_id":"21349352973"},{"client_id":"3886","company_id":"20750181672"},{"client_id":"3886","company_id":"20436659650"},{"client_id":"3886","company_id":"22643017843"},{"client_id":"3886","company_id":"178349157759"},{"client_id":"3886","company_id":"198135910871"},{"client_id":"3886","company_id":"20705292466"},{"client_id":"3886","company_id":"20156876583"},{"client_id":"3886","company_id":"21228226126"},{"client_id":"3886","company_id":"22772807359"},{"client_id":"3886","company_id":"21598158157"},{"client_id":"3886","company_id":"24574468710"},{"client_id":"3886","company_id":"22563629245"},{"client_id":"3886","company_id":"275738254163"},{"client_id":"3886","company_id":"24626006954"},{"client_id":"3886","company_id":"21830750721"},{"client_id":"3886","company_id":"20749863861"},{"client_id":"3886","company_id":"21141709658"},{"client_id":"3886","company_id":"20434324609"},{"client_id":"3886","company_id":"24712471664"},{"client_id":"3886","company_id":"238430115290"},{"client_id":"3886","company_id":"36934671884"},{"client_id":"3886","company_id":"142191397318"},{"client_id":"3886","company_id":"20275046348"},{"client_id":"3886","company_id":"20383344865"},{"client_id":"3886","company_id":"357722658198"},{"client_id":"3886","company_id":"20749109830"},{"client_id":"3886","company_id":"24254478690"},{"client_id":"3886","company_id":"217905840820"},{"client_id":"3886","company_id":"23773283294"},{"client_id":"3886","company_id":"30244753961"},{"client_id":"3886","company_id":"20865412065"},{"client_id":"3886","company_id":"21265564182"},{"client_id":"3886","company_id":"373088390712"},{"client_id":"3886","company_id":"17402482633"},{"client_id":"3886","company_id":"63319385280"},{"client_id":"3886","company_id":"22561564604"},{"client_id":"3886","company_id":"411177477359"},{"client_id":"3886","company_id":"126131187548"},{"client_id":"3886","company_id":"20605964196"},{"client_id":"3886","company_id":"20611185383"},{"client_id":"3886","company_id":"20598103335"},{"client_id":"3886","company_id":"271005462655"},{"client_id":"3886","company_id":"24315862045"},{"client_id":"3886","company_id":"24323139777"},{"client_id":"3886","company_id":"40580834223"},{"client_id":"3886","company_id":"149852651275"},{"client_id":"3886","company_id":"23024373578"},{"client_id":"3886","company_id":"24771217318"},{"client_id":"3886","company_id":"21672273803"},{"client_id":"3886","company_id":"23149043868"},{"client_id":"3886","company_id":"24205185164"},{"client_id":"3886","company_id":"24267538061"},{"client_id":"3886","company_id":"128425818848"},{"client_id":"3886","company_id":"20408949306"},{"client_id":"3886","company_id":"22268089449"},{"client_id":"3886","company_id":"20636254640"},{"client_id":"3886","company_id":"29528615466"},{"client_id":"3886","company_id":"66355668192"},{"client_id":"3886","company_id":"20805809663"},{"client_id":"3886","company_id":"21405350130"},{"client_id":"3886","company_id":"77042396450"},{"client_id":"3886","company_id":"22566470651"},{"client_id":"3886","company_id":"22356285681"},{"client_id":"3886","company_id":"20160210068"},{"client_id":"3886","company_id":"129280097405"},{"client_id":"3886","company_id":"23291035173"},{"client_id":"3886","company_id":"22855309400"},{"client_id":"3886","company_id":"20796254026"},{"client_id":"3886","company_id":"24143128208"},{"client_id":"3886","company_id":"24481393452"},{"client_id":"3886","company_id":"21000071672"},{"client_id":"3886","company_id":"25316269423"},{"client_id":"3886","company_id":"20610140699"},{"client_id":"3886","company_id":"26480137272"},{"client_id":"3886","company_id":"357559973916"},{"client_id":"3886","company_id":"22347936398"},{"client_id":"3886","company_id":"25432556930"},{"client_id":"3886","company_id":"23221398197"},{"client_id":"3886","company_id":"20435406064"},{"client_id":"3886","company_id":"23221577759"},{"client_id":"3886","company_id":"3502202304455"},{"client_id":"3886","company_id":"404967230288"},{"client_id":"3886","company_id":"25077228390"},{"client_id":"3886","company_id":"22147649098"},{"client_id":"3886","company_id":"400633316344"},{"client_id":"3886","company_id":"23334136037"},{"client_id":"3886","company_id":"22858029321"},{"client_id":"3886","company_id":"27555279944"},{"client_id":"3886","company_id":"20412430528"},{"client_id":"3886","company_id":"217921653795"},{"client_id":"3886","company_id":"178351815048"},{"client_id":"3886","company_id":"217659656975"},{"client_id":"3886","company_id":"20601224546"},{"client_id":"3886","company_id":"91370106439"},{"client_id":"3886","company_id":"22660599984"},{"client_id":"3886","company_id":"21051099501"},{"client_id":"3886","company_id":"23674140324"},{"client_id":"3886","company_id":"20637144704"},{"client_id":"3886","company_id":"24586339084"},{"client_id":"3886","company_id":"354132919360"},{"client_id":"3886","company_id":"22716979543"},{"client_id":"3886","company_id":"20434077983"},{"client_id":"3886","company_id":"22563186801"},{"client_id":"3886","company_id":"21067587880"},{"client_id":"3886","company_id":"20162091631"},{"client_id":"3886","company_id":"123860526674"},{"client_id":"3886","company_id":"160328277056"},{"client_id":"3886","company_id":"23215763938"},{"client_id":"3886","company_id":"302112894429"},{"client_id":"3886","company_id":"24080972898"},{"client_id":"3886","company_id":"24771976373"},{"client_id":"3886","company_id":"76931242096"},{"client_id":"3886","company_id":"653629623539"},{"client_id":"3886","company_id":"20481989468"},{"client_id":"3886","company_id":"20710052127"},{"client_id":"3886","company_id":"20851904314"},{"client_id":"3886","company_id":"22986939310"},{"client_id":"3886","company_id":"20479539583"},{"client_id":"3886","company_id":"22607576939"},{"client_id":"3886","company_id":"21423428820"},{"client_id":"3886","company_id":"20292736330"},{"client_id":"3886","company_id":"21065677343"},{"client_id":"3886","company_id":"24325863929"},{"client_id":"3886","company_id":"178329996591"},{"client_id":"3886","company_id":"20964676087"},{"client_id":"3886","company_id":"20787597430"},{"client_id":"3886","company_id":"22857512188"},{"client_id":"3886","company_id":"24587340839"},{"client_id":"3886","company_id":"20292798617"},{"client_id":"3886","company_id":"24513923501"},{"client_id":"3886","company_id":"21227970529"},{"client_id":"3886","company_id":"24325253193"},{"client_id":"3886","company_id":"20302078134"},{"client_id":"3886","company_id":"23914015177"},{"client_id":"3886","company_id":"65502653363"},{"client_id":"3886","company_id":"1466215854850"},{"client_id":"3886","company_id":"21542845158"},{"client_id":"3886","company_id":"22328153072"},{"client_id":"3886","company_id":"21885764408"},{"client_id":"3886","company_id":"43598110932"},{"client_id":"3886","company_id":"20731312771"},{"client_id":"3886","company_id":"20293996864"},{"client_id":"3886","company_id":"27555196365"},{"client_id":"3886","company_id":"424934261830"},{"client_id":"3886","company_id":"20245631114"},{"client_id":"3886","company_id":"22973324534"},{"client_id":"3886","company_id":"249708424277"},{"client_id":"3886","company_id":"22044401401"},{"client_id":"3886","company_id":"20961549407"},{"client_id":"3886","company_id":"22716370404"},{"client_id":"3886","company_id":"20158298128"},{"client_id":"3886","company_id":"23773386481"},{"client_id":"3886","company_id":"22875217372"},{"client_id":"3886","company_id":"144926026008"},{"client_id":"3886","company_id":"217931057974"},{"client_id":"3886","company_id":"20997102211"},{"client_id":"3886","company_id":"23829430037"},{"client_id":"3886","company_id":"22911395216"},{"client_id":"3886","company_id":"22418662785"},{"client_id":"3886","company_id":"157661305643"},{"client_id":"3886","company_id":"22220756850"},{"client_id":"3886","company_id":"105941219276"},{"client_id":"3886","company_id":"21139332626"},{"client_id":"3886","company_id":"24204440226"},{"client_id":"3886","company_id":"20442632702"},{"client_id":"3886","company_id":"20380400357"},{"client_id":"3886","company_id":"21134452362"},{"client_id":"3886","company_id":"22028900306"},{"client_id":"3886","company_id":"25351451270"},{"client_id":"3886","company_id":"20607474941"},{"client_id":"3886","company_id":"20483831562"},{"client_id":"3886","company_id":"20601142543"},{"client_id":"3886","company_id":"21036009336"},{"client_id":"3886","company_id":"23821728701"},{"client_id":"3886","company_id":"22327746385"},{"client_id":"3886","company_id":"22716049856"},{"client_id":"3886","company_id":"22337204109"},{"client_id":"3886","company_id":"20787722219"},{"client_id":"3886","company_id":"24142999540"},{"client_id":"3886","company_id":"24417838856"},{"client_id":"3886","company_id":"22154286610"},{"client_id":"3886","company_id":"24010257286"},{"client_id":"3886","company_id":"20805486661"},{"client_id":"3886","company_id":"24586745105"},{"client_id":"3886","company_id":"22029233988"},{"client_id":"3886","company_id":"20509364195"},{"client_id":"3886","company_id":"21346377642"},{"client_id":"3886","company_id":"21845404107"},{"client_id":"3886","company_id":"21344262658"},{"client_id":"3886","company_id":"22972334263"},{"client_id":"3886","company_id":"20300845795"},{"client_id":"3886","company_id":"20634705292"},{"client_id":"3886","company_id":"20409452959"},{"client_id":"3886","company_id":"25171597664"},{"client_id":"3886","company_id":"20436172446"},{"client_id":"3886","company_id":"20864708216"},{"client_id":"3886","company_id":"21347212592"},{"client_id":"3886","company_id":"2023152973753"},{"client_id":"3886","company_id":"225722833587"},{"client_id":"3886","company_id":"24014708830"},{"client_id":"3886","company_id":"36925963221"},{"client_id":"3886","company_id":"21877851755"},{"client_id":"3886","company_id":"20556688653"},{"client_id":"3886","company_id":"246610251568"},{"client_id":"3886","company_id":"20739111504"},{"client_id":"3886","company_id":"22720441140"},{"client_id":"3886","company_id":"24268187006"},{"client_id":"3886","company_id":"21989011853"},{"client_id":"3886","company_id":"20274994763"},{"client_id":"3886","company_id":"211768314256"},{"client_id":"3886","company_id":"24586156934"},{"client_id":"3886","company_id":"22354317163"},{"client_id":"3886","company_id":"25312397364"},{"client_id":"3886","company_id":"380190142741"},{"client_id":"3886","company_id":"21595256639"},{"client_id":"3886","company_id":"25167465874"},{"client_id":"3886","company_id":"380319619688"},{"client_id":"3886","company_id":"305090421398"},{"client_id":"3886","company_id":"21347434710"},{"client_id":"3886","company_id":"21205913969"},{"client_id":"3886","company_id":"23913798410"},{"client_id":"3886","company_id":"30797204213"},{"client_id":"3886","company_id":"23286980981"},{"client_id":"3886","company_id":"425478893757"},{"client_id":"3886","company_id":"179045384613"},{"client_id":"3886","company_id":"22615748825"},{"client_id":"3886","company_id":"20156610871"},{"client_id":"3886","company_id":"22972898060"},{"client_id":"3886","company_id":"25433312257"},{"client_id":"3886","company_id":"21344382737"},{"client_id":"3886","company_id":"3302993413950"},{"client_id":"3886","company_id":"376031548820"},{"client_id":"3886","company_id":"20276519527"},{"client_id":"3886","company_id":"23913891002"},{"client_id":"3886","company_id":"22359571062"},{"client_id":"3886","company_id":"22904204702"},{"client_id":"3886","company_id":"21408530024"},{"client_id":"3886","company_id":"75212675343"},{"client_id":"3886","company_id":"26196786065"},{"client_id":"3886","company_id":"20636145695"},{"client_id":"3886","company_id":"22351987287"},{"client_id":"3886","company_id":"181605177161"},{"client_id":"3886","company_id":"22716560693"},{"client_id":"3886","company_id":"13618683333"},{"client_id":"3886","company_id":"20301728914"},{"client_id":"3886","company_id":"21135195334"},{"client_id":"3886","company_id":"119131327701"},{"client_id":"3886","company_id":"20481789516"},{"client_id":"3886","company_id":"179059281664"},{"client_id":"3886","company_id":"23575365382"},{"client_id":"3886","company_id":"22561054257"},{"client_id":"3886","company_id":"22930910166"},{"client_id":"3886","company_id":"20949168255"},{"client_id":"3886","company_id":"55434046444"},{"client_id":"3886","company_id":"21345604420"},{"client_id":"3886","company_id":"20438485427"},{"client_id":"3886","company_id":"24414323793"},{"client_id":"3886","company_id":"181719287152"},{"client_id":"3886","company_id":"105915112022"},{"client_id":"3886","company_id":"22866557797"},{"client_id":"3886","company_id":"23172903606"},{"client_id":"3886","company_id":"24097897346"},{"client_id":"3886","company_id":"20638442154"},{"client_id":"3886","company_id":"20135490947"},{"client_id":"3886","company_id":"21422382495"},{"client_id":"3886","company_id":"24159819012"},{"client_id":"3886","company_id":"20250075132"},{"client_id":"3886","company_id":"20436372758"},{"client_id":"3886","company_id":"23614222800"},{"client_id":"3886","company_id":"21722923787"},{"client_id":"3886","company_id":"23643893019"},{"client_id":"3886","company_id":"21829504534"},{"client_id":"3886","company_id":"24630763779"},{"client_id":"3886","company_id":"48587803478"},{"client_id":"3886","company_id":"20665929204"},{"client_id":"3886","company_id":"23024510453"},{"client_id":"3886","company_id":"20747431768"},{"client_id":"3886","company_id":"24418112149"},{"client_id":"3886","company_id":"45769226696"},{"client_id":"3886","company_id":"154788406350"},{"client_id":"3886","company_id":"20273055116"},{"client_id":"3886","company_id":"24316183466"},{"client_id":"3886","company_id":"22920267751"},{"client_id":"3886","company_id":"80215527634"},{"client_id":"3886","company_id":"21489991698"},{"client_id":"3886","company_id":"275623215447"},{"client_id":"3886","company_id":"22860886553"},{"client_id":"3886","company_id":"21954129073"},{"client_id":"3886","company_id":"24203828965"},{"client_id":"3886","company_id":"253098334503"},{"client_id":"3886","company_id":"354203270403"},{"client_id":"3886","company_id":"10999717275"},{"client_id":"3886","company_id":"20512755932"},{"client_id":"3886","company_id":"2084155352"},{"client_id":"3886","company_id":"179260261526"},{"client_id":"3886","company_id":"22211165473"},{"client_id":"3886","company_id":"20436276651"},{"client_id":"3886","company_id":"20632641076"},{"client_id":"3886","company_id":"376255383047"},{"client_id":"3886","company_id":"385063072167"},{"client_id":"3886","company_id":"20457014075"},{"client_id":"3886","company_id":"23644143896"},{"client_id":"3886","company_id":"24910463445"},{"client_id":"3886","company_id":"20862625413"},{"client_id":"3886","company_id":"20628099092"},{"client_id":"3886","company_id":"20641484996"},{"client_id":"3886","company_id":"20380373148"},{"client_id":"3886","company_id":"20660655921"},{"client_id":"3886","company_id":"22221400749"},{"client_id":"3886","company_id":"20999318212"},{"client_id":"3886","company_id":"22139799153"},{"client_id":"3886","company_id":"257029588790"},{"client_id":"3886","company_id":"151930211944"},{"client_id":"3886","company_id":"24871404796"},{"client_id":"3886","company_id":"20787998507"},{"client_id":"3886","company_id":"23575471697"},{"client_id":"3886","company_id":"22050884966"},{"client_id":"3886","company_id":"194941456557"},{"client_id":"3886","company_id":"22771717521"},{"client_id":"3886","company_id":"25345647834"},{"client_id":"3886","company_id":"20480621621"},{"client_id":"3886","company_id":"22912739009"},{"client_id":"3886","company_id":"24462184526"},{"client_id":"3886","company_id":"181678774731"},{"client_id":"3886","company_id":"24873154519"},{"client_id":"3886","company_id":"139588258896"},{"client_id":"3886","company_id":"20514291637"},{"client_id":"3886","company_id":"24525150541"},{"client_id":"3886","company_id":"384442455273"},{"client_id":"3886","company_id":"20642986266"},{"client_id":"3886","company_id":"151957056542"},{"client_id":"3886","company_id":"24316279964"},{"client_id":"3886","company_id":"30795344514"},{"client_id":"3886","company_id":"23079326230"},{"client_id":"3886","company_id":"20247804484"},{"client_id":"3886","company_id":"22218039879"},{"client_id":"3886","company_id":"21994462829"},{"client_id":"3886","company_id":"25139674136"},{"client_id":"3886","company_id":"21885988486"},{"client_id":"3886","company_id":"20510963578"},{"client_id":"3886","company_id":"21493667635"},{"client_id":"3886","company_id":"21550138443"},{"client_id":"3886","company_id":"4083455567"},{"client_id":"3886","company_id":"21449873541"},{"client_id":"3886","company_id":"24186815005"},{"client_id":"3886","company_id":"349355190561"},{"client_id":"3886","company_id":"20633036401"},{"client_id":"3886","company_id":"22856932051"},{"client_id":"3886","company_id":"354263082004"},{"client_id":"3886","company_id":"21110997658"},{"client_id":"3886","company_id":"21405609394"},{"client_id":"3886","company_id":"21345325727"},{"client_id":"3886","company_id":"178344733521"},{"client_id":"3886","company_id":"24524737862"},{"client_id":"3886","company_id":"380565790712"},{"client_id":"3886","company_id":"20713888436"},{"client_id":"3886","company_id":"21701389810"},{"client_id":"3886","company_id":"3172036829342"},{"client_id":"3886","company_id":"149390536924"},{"client_id":"3886","company_id":"23678491365"},{"client_id":"3886","company_id":"25397182379"},{"client_id":"3886","company_id":"225740311131"},{"client_id":"3886","company_id":"27557238356"},{"client_id":"3886","company_id":"20292666598"},{"client_id":"3886","company_id":"20512573104"},{"client_id":"3886","company_id":"23766397952"},{"client_id":"3886","company_id":"24204345735"},{"client_id":"3886","company_id":"21446378394"},{"client_id":"3886","company_id":"211952968541"},{"client_id":"3886","company_id":"22145821719"},{"client_id":"3886","company_id":"425520311131"},{"client_id":"3886","company_id":"25453765296"},{"client_id":"3886","company_id":"20295172984"},{"client_id":"3886","company_id":"22338477388"},{"client_id":"3886","company_id":"54876446089"},{"client_id":"3886","company_id":"63590631298"},{"client_id":"3886","company_id":"24626706361"},{"client_id":"3886","company_id":"20300407816"},{"client_id":"3886","company_id":"23339284596"},{"client_id":"3886","company_id":"30794169326"},{"client_id":"3886","company_id":"24873156388"},{"client_id":"3886","company_id":"113318583365"},{"client_id":"3886","company_id":"126124850600"},{"client_id":"3886","company_id":"23619205207"},{"client_id":"3886","company_id":"20299281819"},{"client_id":"3886","company_id":"20637504930"},{"client_id":"3886","company_id":"25242876769"},{"client_id":"3886","company_id":"25612678021"},{"client_id":"3886","company_id":"24834050102"},{"client_id":"3886","company_id":"23074693103"},{"client_id":"3886","company_id":"23028545846"},{"client_id":"3886","company_id":"24568513411"},{"client_id":"3886","company_id":"21421243462"},{"client_id":"3886","company_id":"24408554947"},{"client_id":"3886","company_id":"25432544977"},{"client_id":"3886","company_id":"22927252593"},{"client_id":"3886","company_id":"21065510902"},{"client_id":"3886","company_id":"21111435807"},{"client_id":"3886","company_id":"22857666194"},{"client_id":"3886","company_id":"75183950005"},{"client_id":"3886","company_id":"22564899543"},{"client_id":"3886","company_id":"20292812154"},{"client_id":"3886","company_id":"134306773439"},{"client_id":"3886","company_id":"7201303598"},{"client_id":"3886","company_id":"20608693521"},{"client_id":"3886","company_id":"20556980133"},{"client_id":"3886","company_id":"441168757397"},{"client_id":"3886","company_id":"23939780821"},{"client_id":"3886","company_id":"24913186512"},{"client_id":"3886","company_id":"22416717811"},{"client_id":"3886","company_id":"20825380204"},{"client_id":"3886","company_id":"21840343234"},{"client_id":"3886","company_id":"20787542664"},{"client_id":"3886","company_id":"99501603351"},{"client_id":"3886","company_id":"22466849309"},{"client_id":"3886","company_id":"22467504028"},{"client_id":"3886","company_id":"366874691344"},{"client_id":"3886","company_id":"21738566796"},{"client_id":"3886","company_id":"21076811039"},{"client_id":"3886","company_id":"21885589628"},{"client_id":"3886","company_id":"23441048540"},{"client_id":"3886","company_id":"22648229434"},{"client_id":"3886","company_id":"22972875724"},{"client_id":"3886","company_id":"24581093132"},{"client_id":"3886","company_id":"24833896747"},{"client_id":"3886","company_id":"239014059882"},{"client_id":"3886","company_id":"25431394898"},{"client_id":"3886","company_id":"122042308961"},{"client_id":"3886","company_id":"305892857240"},{"client_id":"3886","company_id":"10914788780"},{"client_id":"3886","company_id":"246409033369"},{"client_id":"3886","company_id":"22928949930"},{"client_id":"3886","company_id":"149367580516"},{"client_id":"3886","company_id":"22973420116"},{"client_id":"3886","company_id":"21947373418"},{"client_id":"3886","company_id":"20805633929"},{"client_id":"3993","company_id":"11515549366"},{"client_id":"3993","company_id":"11010177810"},{"client_id":"4029","company_id":"3105603482"},{"client_id":"4056","company_id":"17260633973"},{"client_id":"4061","company_id":"17622837200"},{"client_id":"4115","company_id":"2202459932"},{"client_id":"4115","company_id":"2202455085"},{"client_id":"4115","company_id":"2202455093"},{"client_id":"4128","company_id":"2215010665"},{"client_id":"4128","company_id":"2215010516"},{"client_id":"4208","company_id":"2260816862"},{"client_id":"4228","company_id":"5485948647"},{"client_id":"4422","company_id":"9471042223"},{"client_id":"4453","company_id":"18125482769"},{"client_id":"4453","company_id":"18125525932"},{"client_id":"4453","company_id":"18125763194"},{"client_id":"4453","company_id":"18107307580"},{"client_id":"4453","company_id":"18125332292"},{"client_id":"4453","company_id":"18126614719"},{"client_id":"4453","company_id":"18125435266"},{"client_id":"4453","company_id":"18107177792"},{"client_id":"4453","company_id":"18107225408"},{"client_id":"4453","company_id":"18126114148"},{"client_id":"4460","company_id":"17542309479"},{"client_id":"4570","company_id":"3526803595"},{"client_id":"4611","company_id":"24481886919"},{"client_id":"4632","company_id":"2729411314"},{"client_id":"4636","company_id":"14105104206"},{"client_id":"4672","company_id":"2751859163"},{"client_id":"4700","company_id":"17791504611"},{"client_id":"4732","company_id":"1199004831104"},{"client_id":"4732","company_id":"1199677260185"},{"client_id":"4858","company_id":"2710758034"},{"client_id":"4864","company_id":"2684041112"},{"client_id":"4864","company_id":"2684041074"},{"client_id":"4864","company_id":"2684040378"},{"client_id":"4864","company_id":"2684042141"},{"client_id":"4864","company_id":"2684043987"},{"client_id":"4864","company_id":"2684043054"},{"client_id":"4867","company_id":"21080320276"},{"client_id":"4867","company_id":"21277313951"},{"client_id":"4936","company_id":"4005954637"},{"client_id":"4957","company_id":"24906120843"},{"client_id":"4957","company_id":"106713389229"},{"client_id":"4957","company_id":"10701609275"},{"client_id":"4957","company_id":"7613818942"},{"client_id":"4957","company_id":"3606890061"},{"client_id":"4957","company_id":"6576239867"},{"client_id":"4957","company_id":"2019191807786"},{"client_id":"4957","company_id":"6540131090"},{"client_id":"4957","company_id":"22534365193"},{"client_id":"4957","company_id":"341831801233"},{"client_id":"4957","company_id":"3105221291"},{"client_id":"4957","company_id":"22664126221"},{"client_id":"4957","company_id":"6539742995"},{"client_id":"4957","company_id":"424996103961"},{"client_id":"4957","company_id":"24909025918"},{"client_id":"4957","company_id":"6540109024"},{"client_id":"4957","company_id":"2759812446"},{"client_id":"4957","company_id":"6540087983"},{"client_id":"4957","company_id":"13612125390"},{"client_id":"4957","company_id":"2759814589"},{"client_id":"4981","company_id":"2741324921"},{"client_id":"5007","company_id":"9298215320"},{"client_id":"5007","company_id":"13171262170"},{"client_id":"5007","company_id":"9300157685"},{"client_id":"5023","company_id":"2770994085"},{"client_id":"5027","company_id":"3862462209"},{"client_id":"5058","company_id":"12120006598"},{"client_id":"5083","company_id":"3622788942"},{"client_id":"5105","company_id":"9182808067"},{"client_id":"5105","company_id":"8529747169"},{"client_id":"5105","company_id":"2840121061"},{"client_id":"5105","company_id":"8546738552"},{"client_id":"5144","company_id":"2884043010"},{"client_id":"5144","company_id":"18195137904"},{"client_id":"5144","company_id":"18131368279"},{"client_id":"5144","company_id":"18514297370"},{"client_id":"5144","company_id":"2884123372"},{"client_id":"5144","company_id":"18195395026"},{"client_id":"5144","company_id":"18195152461"},{"client_id":"5144","company_id":"17925453786"},{"client_id":"5144","company_id":"14625295565"},{"client_id":"5144","company_id":"18131539348"},{"client_id":"5144","company_id":"17925270911"},{"client_id":"5144","company_id":"18118393175"},{"client_id":"5144","company_id":"18131418373"},{"client_id":"5144","company_id":"17943449974"},{"client_id":"5144","company_id":"18312847723"},{"client_id":"5144","company_id":"17925265120"},{"client_id":"5144","company_id":"13671954477"},{"client_id":"5144","company_id":"18131692181"},{"client_id":"5144","company_id":"2884138490"},{"client_id":"5144","company_id":"17925486544"},{"client_id":"5144","company_id":"4684123245"},{"client_id":"5144","company_id":"17925023096"},{"client_id":"5144","company_id":"2885217601"},{"client_id":"5144","company_id":"2878150835"},{"client_id":"5144","company_id":"2888120267"},{"client_id":"5144","company_id":"17925151596"},{"client_id":"5144","company_id":"3138567907"},{"client_id":"5144","company_id":"18131656577"},{"client_id":"5144","company_id":"18353712513"},{"client_id":"5144","company_id":"18312884910"},{"client_id":"5231","company_id":"11407670315"},{"client_id":"5270","company_id":"438879836517"},{"client_id":"5271","company_id":"96512456745"},{"client_id":"5274","company_id":"6931294096"},{"client_id":"5303","company_id":"13567013337"},{"client_id":"5303","company_id":"13567012664"},{"client_id":"5406","company_id":"3079121810"},{"client_id":"5423","company_id":"3294175709"},{"client_id":"5530","company_id":"17890022049"},{"client_id":"5552","company_id":"3517885202"},{"client_id":"5552","company_id":"3535881682"},{"client_id":"5565","company_id":"14450135852"},{"client_id":"5587","company_id":"4684302326"},{"client_id":"5603","company_id":"16493325192"},{"client_id":"5607","company_id":"4742805606"},{"client_id":"5647","company_id":"3147104966"},{"client_id":"5690","company_id":"2950570911765"},{"client_id":"5810","company_id":"8367214366"},{"client_id":"5818","company_id":"18358079219"},{"client_id":"5850","company_id":"12370862093"},{"client_id":"5850","company_id":"12259446881"},{"client_id":"5850","company_id":"12324323259"},{"client_id":"5931","company_id":"1693930040783"},{"client_id":"5941","company_id":"19668805091"},{"client_id":"5941","company_id":"19668775166"},{"client_id":"6005","company_id":"3313463668"},{"client_id":"6238","company_id":"3369633045"},{"client_id":"6347","company_id":"4609238104"},{"client_id":"6594","company_id":"5802362321"},{"client_id":"6764","company_id":"3659469778"},{"client_id":"6856","company_id":"3553962559"},{"client_id":"6856","company_id":"3553875904"},{"client_id":"6856","company_id":"3553965118"},{"client_id":"6856","company_id":"3553888262"},{"client_id":"6856","company_id":"3553959212"},{"client_id":"6856","company_id":"3553958123"},{"client_id":"6856","company_id":"3553875928"},{"client_id":"6879","company_id":"11197666364"},{"client_id":"6917","company_id":"1735867189213"},{"client_id":"7004","company_id":"24202351791"},{"client_id":"7061","company_id":"235881074764"},{"client_id":"7153","company_id":"3820856085"},{"client_id":"7153","company_id":"3758187272"},{"client_id":"7195","company_id":"21483843897"},{"client_id":"7212","company_id":"4637066386"},{"client_id":"7251","company_id":"3912026404"},{"client_id":"7267","company_id":"4602633442"},{"client_id":"7310","company_id":"3903965023"},{"client_id":"7827","company_id":"4024079435"},{"client_id":"7827","company_id":"4024071076"},{"client_id":"7827","company_id":"4023976177"},{"client_id":"7827","company_id":"4023749827"},{"client_id":"7827","company_id":"4023986915"},{"client_id":"7827","company_id":"4023909635"},{"client_id":"7827","company_id":"4036108073"},{"client_id":"7827","company_id":"4023851130"},{"client_id":"7827","company_id":"4023880350"},{"client_id":"7827","company_id":"4023790273"},{"client_id":"7827","company_id":"4024056443"},{"client_id":"7827","company_id":"4023924577"},{"client_id":"7827","company_id":"4024011481"},{"client_id":"7827","company_id":"4024015571"},{"client_id":"7827","company_id":"4024024845"},{"client_id":"7827","company_id":"4023905640"},{"client_id":"7827","company_id":"4024004125"},{"client_id":"7827","company_id":"4024042128"},{"client_id":"7827","company_id":"4024184995"},{"client_id":"7827","company_id":"4023825347"},{"client_id":"7827","company_id":"4023847700"},{"client_id":"7827","company_id":"4023875196"},{"client_id":"7827","company_id":"4023994538"},{"client_id":"7827","company_id":"4024020479"},{"client_id":"7827","company_id":"4023801662"},{"client_id":"7827","company_id":"4024192526"},{"client_id":"7827","company_id":"4036115451"},{"client_id":"7827","company_id":"4024066357"},{"client_id":"7827","company_id":"4023935363"},{"client_id":"7827","company_id":"4024011388"},{"client_id":"7827","company_id":"4024098739"},{"client_id":"7827","company_id":"4024015559"},{"client_id":"7827","company_id":"4024204809"},{"client_id":"7827","company_id":"4024033098"},{"client_id":"7827","company_id":"4024131630"},{"client_id":"7827","company_id":"4024181217"},{"client_id":"7827","company_id":"4036111223"},{"client_id":"7827","company_id":"4023833423"},{"client_id":"7827","company_id":"4023940255"},{"client_id":"7827","company_id":"4024123281"},{"client_id":"7827","company_id":"4023828870"},{"client_id":"7827","company_id":"4024113032"},{"client_id":"7827","company_id":"4023840076"},{"client_id":"7827","company_id":"4024061550"},{"client_id":"7827","company_id":"4023946534"},{"client_id":"7827","company_id":"4023896552"},{"client_id":"7827","company_id":"4023946827"},{"client_id":"7827","company_id":"4023969997"},{"client_id":"7827","company_id":"4023801572"},{"client_id":"7827","company_id":"4023903750"},{"client_id":"7827","company_id":"4023817945"},{"client_id":"7827","company_id":"4023919874"},{"client_id":"7827","company_id":"4023859087"},{"client_id":"7827","company_id":"4023774593"},{"client_id":"7888","company_id":"9582370151"},{"client_id":"7888","company_id":"178295813051"},{"client_id":"7956","company_id":"4042514988"},{"client_id":"8080","company_id":"7501489633"},{"client_id":"8080","company_id":"7501463622"},{"client_id":"8147","company_id":"6249119514"},{"client_id":"8999","company_id":"9229415912"},{"client_id":"9027","company_id":"13945961385"},{"client_id":"9047","company_id":"280170161290"},{"client_id":"9437","company_id":"13082691249"},{"client_id":"9559","company_id":"5517889718"},{"client_id":"9596","company_id":"758772710086"},{"client_id":"9596","company_id":"761181247204"},{"client_id":"9596","company_id":"752229547628"},{"client_id":"9596","company_id":"761525167932"},{"client_id":"9596","company_id":"488365274127"},{"client_id":"9596","company_id":"759332739951"},{"client_id":"9738","company_id":"5234936489"},{"client_id":"9776","company_id":"16846814213"},{"client_id":"9968","company_id":"5655043876"},{"client_id":"10038","company_id":"15730915986"},{"client_id":"10038","company_id":"245837885305"},{"client_id":"10039","company_id":"16572594598"},{"client_id":"10039","company_id":"17583814714"},{"client_id":"10039","company_id":"10692727423"},{"client_id":"10039","company_id":"17591794066"},{"client_id":"10039","company_id":"10323735506"},{"client_id":"10039","company_id":"10300491487"},{"client_id":"10039","company_id":"17618522141"},{"client_id":"10039","company_id":"17693090790"},{"client_id":"10039","company_id":"17652399923"},{"client_id":"10039","company_id":"17670168692"},{"client_id":"10039","company_id":"10849809429"},{"client_id":"10039","company_id":"17588087817"},{"client_id":"10039","company_id":"17469753237"},{"client_id":"10039","company_id":"17617567770"},{"client_id":"10039","company_id":"17592052422"},{"client_id":"10039","company_id":"17461677299"},{"client_id":"10039","company_id":"11135446595"},{"client_id":"10039","company_id":"17605173557"},{"client_id":"10039","company_id":"9556466740"},{"client_id":"10039","company_id":"16715665996"},{"client_id":"10039","company_id":"16874478675"},{"client_id":"10039","company_id":"10699963504"},{"client_id":"10039","company_id":"17583548243"},{"client_id":"10039","company_id":"16689960967"},{"client_id":"10039","company_id":"16884864949"},{"client_id":"10039","company_id":"17482125813"},{"client_id":"10039","company_id":"17633181341"},{"client_id":"10039","company_id":"16816301419"},{"client_id":"10039","company_id":"10564730693"},{"client_id":"10039","company_id":"17619166403"},{"client_id":"10039","company_id":"17469872512"},{"client_id":"10039","company_id":"5162430991"},{"client_id":"10039","company_id":"17601923186"},{"client_id":"10039","company_id":"17448966750"},{"client_id":"10039","company_id":"16670236486"},{"client_id":"10039","company_id":"17619432945"},{"client_id":"10039","company_id":"17432200186"},{"client_id":"10039","company_id":"9595345459"},{"client_id":"10039","company_id":"16876422121"},{"client_id":"10039","company_id":"17592531693"},{"client_id":"10039","company_id":"17482827328"},{"client_id":"10039","company_id":"11302357879"},{"client_id":"10039","company_id":"10849248934"},{"client_id":"10039","company_id":"17590850499"},{"client_id":"10039","company_id":"10802031936"},{"client_id":"10039","company_id":"17501268370"},{"client_id":"10039","company_id":"5162424899"},{"client_id":"10039","company_id":"17450882227"},{"client_id":"10039","company_id":"17671795585"},{"client_id":"10039","company_id":"5162433956"},{"client_id":"10039","company_id":"11167464142"},{"client_id":"10039","company_id":"17691094492"},{"client_id":"10039","company_id":"10564439356"},{"client_id":"10039","company_id":"10995668925"},{"client_id":"10039","company_id":"17460881203"},{"client_id":"10039","company_id":"17613386124"},{"client_id":"10039","company_id":"13996608022"},{"client_id":"10039","company_id":"17582311300"},{"client_id":"10039","company_id":"17619211764"},{"client_id":"10039","company_id":"17630355938"},{"client_id":"10039","company_id":"17461025306"},{"client_id":"10039","company_id":"17481414517"},{"client_id":"10039","company_id":"9523323254"},{"client_id":"10039","company_id":"17618287534"},{"client_id":"10039","company_id":"16693144799"},{"client_id":"10039","company_id":"17617184596"},{"client_id":"10039","company_id":"16688181127"},{"client_id":"11283","company_id":"7721286306"},{"client_id":"11283","company_id":"7721246282"},{"client_id":"12082","company_id":"14211131276"},{"client_id":"12225","company_id":"6832935349"},{"client_id":"12380","company_id":"22900792419"},{"client_id":"12380","company_id":"22900783592"},{"client_id":"12380","company_id":"22900769915"},{"client_id":"12392","company_id":"6723570269"},{"client_id":"12584","company_id":"12448314789"},{"client_id":"12584","company_id":"12476485386"},{"client_id":"12584","company_id":"12476430724"},{"client_id":"12584","company_id":"12476432333"},{"client_id":"12584","company_id":"12511096119"},{"client_id":"12584","company_id":"12476450256"},{"client_id":"12584","company_id":"12513219978"},{"client_id":"12584","company_id":"12476474874"},{"client_id":"12584","company_id":"12448329387"},{"client_id":"12889","company_id":"7832725099"},{"client_id":"13057","company_id":"7341324549"},{"client_id":"13252","company_id":"7688713476"},{"client_id":"13725","company_id":"12313470042"},{"client_id":"13999","company_id":"84334741314"},{"client_id":"13999","company_id":"13408785393"},{"client_id":"13999","company_id":"7639203502"},{"client_id":"14003","company_id":"7731696622"},{"client_id":"14136","company_id":"7786534787"},{"client_id":"14136","company_id":"7851547892"},{"client_id":"14136","company_id":"7787172213"},{"client_id":"14252","company_id":"12490825108"},{"client_id":"14666","company_id":"12513731562"},{"client_id":"15320","company_id":"18833791788"},{"client_id":"15566","company_id":"8823071673"},{"client_id":"15715","company_id":"19746351948"},{"client_id":"15785","company_id":"14331207346"},{"client_id":"15879","company_id":"19259451008"},{"client_id":"16165","company_id":"12845654392"},{"client_id":"16165","company_id":"11676556183"},{"client_id":"16165","company_id":"12784541157"},{"client_id":"16165","company_id":"12380693246"},{"client_id":"16165","company_id":"12945656301"},{"client_id":"16165","company_id":"12379156556"},{"client_id":"16165","company_id":"12324116267"},{"client_id":"16165","company_id":"12379204857"},{"client_id":"16165","company_id":"12703830721"},{"client_id":"16250","company_id":"9899401824"},{"client_id":"16357","company_id":"9638454497"},{"client_id":"16488","company_id":"10737152224"},{"client_id":"16624","company_id":"409821546721"},{"client_id":"16798","company_id":"10336127046"},{"client_id":"16798","company_id":"19123270658"},{"client_id":"16798","company_id":"19123323902"},{"client_id":"16842","company_id":"22981698199"},{"client_id":"17109","company_id":"12414998844"},{"client_id":"17109","company_id":"12415014920"},{"client_id":"17159","company_id":"13292111864"},{"client_id":"17161","company_id":"10238975330"},{"client_id":"17392","company_id":"431639593529"},{"client_id":"17392","company_id":"431642348348"},{"client_id":"17520","company_id":"12673731023"},{"client_id":"17642","company_id":"12952047927"},{"client_id":"17751","company_id":"10127263340"},{"client_id":"17964","company_id":"11481424706"},{"client_id":"18008","company_id":"10999416976"},{"client_id":"18008","company_id":"10999417395"},{"client_id":"18008","company_id":"10999417705"},{"client_id":"18008","company_id":"10999416674"},{"client_id":"18008","company_id":"10998839848"},{"client_id":"18008","company_id":"10999417664"},{"client_id":"18008","company_id":"10998138648"},{"client_id":"18008","company_id":"10999416358"},{"client_id":"18008","company_id":"10999416842"},{"client_id":"18008","company_id":"10999417103"},{"client_id":"18008","company_id":"10999417243"},{"client_id":"18008","company_id":"10998140121"},{"client_id":"18008","company_id":"10999417817"},{"client_id":"18008","company_id":"10998139188"},{"client_id":"18008","company_id":"10999416107"},{"client_id":"18008","company_id":"10998139328"},{"client_id":"18008","company_id":"10998839986"},{"client_id":"18008","company_id":"10999418404"},{"client_id":"18008","company_id":"10999415976"},{"client_id":"18008","company_id":"10998843673"},{"client_id":"18008","company_id":"10999416540"},{"client_id":"18008","company_id":"10999418191"},{"client_id":"18100","company_id":"20670400416"},{"client_id":"18250","company_id":"11724465228"},{"client_id":"18611","company_id":"11584953378"},{"client_id":"18611","company_id":"11583833733"},{"client_id":"18611","company_id":"11585112033"},{"client_id":"18611","company_id":"11585591788"},{"client_id":"18617","company_id":"704455960037"},{"client_id":"19253","company_id":"14744785105"},{"client_id":"19371","company_id":"12677347639"},{"client_id":"19383","company_id":"11163579633"},{"client_id":"19576","company_id":"11293983539"},{"client_id":"19807","company_id":"23147260110"},{"client_id":"19972","company_id":"12155460382"},{"client_id":"19972","company_id":"12155095335"},{"client_id":"20076","company_id":"19029474562"},{"client_id":"20167","company_id":"12105308117"},{"client_id":"20167","company_id":"12125504434"},{"client_id":"20300","company_id":"12032434992"},{"client_id":"20442","company_id":"19901766496"},{"client_id":"20830","company_id":"323271840066"},{"client_id":"20867","company_id":"12696209518"},{"client_id":"21033","company_id":"12851429419"},{"client_id":"21055","company_id":"12556202904"},{"client_id":"21066","company_id":"16949182972"},{"client_id":"21066","company_id":"17066386560"},{"client_id":"21066","company_id":"16854393452"},{"client_id":"21066","company_id":"16819339562"},{"client_id":"21131","company_id":"16380043560"},{"client_id":"21153","company_id":"15785369587"},{"client_id":"21374","company_id":"15039533585"},{"client_id":"21882","company_id":"14161219309"},{"client_id":"21931","company_id":"20181745022"},{"client_id":"22122","company_id":"14633601765"},{"client_id":"22280","company_id":"13422720040"},{"client_id":"22304","company_id":"15427894162"},{"client_id":"22306","company_id":"14630858894"},{"client_id":"22389","company_id":"15860889036"},{"client_id":"22622","company_id":"14578401417"},{"client_id":"22664","company_id":"15043310956"},{"client_id":"22770","company_id":"19574372944"},{"client_id":"22770","company_id":"19575120829"},{"client_id":"22770","company_id":"19574079254"},{"client_id":"22854","company_id":"16563500382"},{"client_id":"22875","company_id":"21663024387"},{"client_id":"23101","company_id":"14994809519"},{"client_id":"23146","company_id":"14939307455"},{"client_id":"23187","company_id":"17449529178"},{"client_id":"23191","company_id":"15927859261"},{"client_id":"23221","company_id":"16983015772"},{"client_id":"23304","company_id":"24649010047"},{"client_id":"23487","company_id":"15243955370"},{"client_id":"23539","company_id":"19128006515"},{"client_id":"23546","company_id":"16063454422"},{"client_id":"23565","company_id":"15238774772"},{"client_id":"23669","company_id":"17770512229"},{"client_id":"23669","company_id":"17770288040"},{"client_id":"23669","company_id":"17770549138"},{"client_id":"23669","company_id":"17770265750"},{"client_id":"23669","company_id":"17770265033"},{"client_id":"23669","company_id":"17770564017"},{"client_id":"23669","company_id":"17773649697"},{"client_id":"23736","company_id":"22271902663"},{"client_id":"23736","company_id":"22270281906"},{"client_id":"23736","company_id":"23278324526"},{"client_id":"23736","company_id":"22272122794"},{"client_id":"23736","company_id":"20996324978"},{"client_id":"23736","company_id":"22271563191"},{"client_id":"23915","company_id":"20823904868"},{"client_id":"23920","company_id":"15924998863"},{"client_id":"23925","company_id":"15190890423"},{"client_id":"23930","company_id":"15275337817"},{"client_id":"23930","company_id":"15279359877"},{"client_id":"23982","company_id":"18498446174"},{"client_id":"23982","company_id":"18498421720"},{"client_id":"24000","company_id":"16458673454"},{"client_id":"24017","company_id":"15931375404"},{"client_id":"24017","company_id":"16096777888"},{"client_id":"24017","company_id":"48490721738"},{"client_id":"24040","company_id":"16282564875"},{"client_id":"24491","company_id":"16139354684"},{"client_id":"24508","company_id":"18951384682"},{"client_id":"24742","company_id":"17465178823"},{"client_id":"24742","company_id":"16701279960"},{"client_id":"24831","company_id":"16306654242"},{"client_id":"24880","company_id":"18306968327"},{"client_id":"24894","company_id":"17190808909"},{"client_id":"24915","company_id":"17000104717"},{"client_id":"24920","company_id":"16942000483"},{"client_id":"24920","company_id":"16937344216"},{"client_id":"24951","company_id":"74984344858"},{"client_id":"24951","company_id":"121402394344"},{"client_id":"24951","company_id":"83145408964"},{"client_id":"24951","company_id":"154638847739"},{"client_id":"24951","company_id":"91898010826"},{"client_id":"24951","company_id":"2406479157177"},{"client_id":"24951","company_id":"158137979937"},{"client_id":"24951","company_id":"58046486517"},{"client_id":"25117","company_id":"18123296450"},{"client_id":"25315","company_id":"18636265534"},{"client_id":"25340","company_id":"19281277323"},{"client_id":"25340","company_id":"19281305079"},{"client_id":"25408","company_id":"19812739875"},{"client_id":"25667","company_id":"19575211081"},{"client_id":"25690","company_id":"239647812101"},{"client_id":"25796","company_id":"17721301149"},{"client_id":"25820","company_id":"17659431565"},{"client_id":"26016","company_id":"18019326428"},{"client_id":"26016","company_id":"18019305830"},{"client_id":"26026","company_id":"18180928323"},{"client_id":"26140","company_id":"18428555379"},{"client_id":"26207","company_id":"18159234480"},{"client_id":"26335","company_id":"19743442400"},{"client_id":"26468","company_id":"4259119509160"},{"client_id":"26562","company_id":"18639635630"},{"client_id":"26563","company_id":"19762158312"},{"client_id":"26719","company_id":"19209204363"},{"client_id":"26785","company_id":"19043498361"},{"client_id":"26808","company_id":"19081205670"},{"client_id":"26808","company_id":"19047859235"},{"client_id":"26808","company_id":"19097836613"},{"client_id":"26978","company_id":"21341740910"},{"client_id":"26978","company_id":"21351846374"},{"client_id":"27104","company_id":"302797343867"},{"client_id":"27104","company_id":"384545123039"},{"client_id":"27202","company_id":"20975651339"},{"client_id":"27202","company_id":"20978426654"},{"client_id":"27359","company_id":"198114311530"},{"client_id":"27359","company_id":"198110757377"},{"client_id":"27359","company_id":"144735460892"},{"client_id":"27359","company_id":"144730953133"},{"client_id":"27359","company_id":"83038622924"},{"client_id":"27359","company_id":"283344513454"},{"client_id":"27359","company_id":"266704879134"},{"client_id":"27359","company_id":"358281337759"},{"client_id":"27359","company_id":"113700458193"},{"client_id":"27359","company_id":"330448254255"},{"client_id":"27359","company_id":"238840123677"},{"client_id":"27359","company_id":"113701511577"},{"client_id":"27359","company_id":"113704539467"},{"client_id":"27359","company_id":"302293230683"},{"client_id":"27359","company_id":"302295177086"},{"client_id":"27359","company_id":"218664144353"},{"client_id":"27359","company_id":"385370835367"},{"client_id":"27359","company_id":"99792019282"},{"client_id":"27359","company_id":"414748290615"},{"client_id":"27434","company_id":"19976812301"},{"client_id":"27434","company_id":"19977034638"},{"client_id":"27507","company_id":"21972914449"},{"client_id":"27587","company_id":"32295436215"},{"client_id":"27595","company_id":"20147076800"},{"client_id":"27675","company_id":"3297320123751"},{"client_id":"27749","company_id":"20245353647"},{"client_id":"27981","company_id":"19811369918"},{"client_id":"28149","company_id":"19994678396"},{"client_id":"28150","company_id":"20428049080"},{"client_id":"28375","company_id":"20384767617"},{"client_id":"28400","company_id":"22000426773"},{"client_id":"28400","company_id":"22005896100"},{"client_id":"28539","company_id":"24773358765"},{"client_id":"28539","company_id":"75224548401"},{"client_id":"28539","company_id":"209204499960"},{"client_id":"28691","company_id":"21209774519"},{"client_id":"28718","company_id":"25461973307"},{"client_id":"28837","company_id":"21031037185"},{"client_id":"28929","company_id":"1247198482463"},{"client_id":"28929","company_id":"2396368246441"},{"client_id":"28967","company_id":"93218696410"},{"client_id":"28987","company_id":"20821473515"},{"client_id":"28987","company_id":"20985203151"},{"client_id":"28987","company_id":"20465198505"},{"client_id":"28987","company_id":"20970469782"},{"client_id":"29040","company_id":"20182057566"},{"client_id":"29072","company_id":"20464006579"},{"client_id":"29116","company_id":"20971268671"},{"client_id":"29137","company_id":"21928439748"},{"client_id":"29270","company_id":"20825150489"},{"client_id":"29270","company_id":"20748693767"},{"client_id":"29274","company_id":"22048693070"},{"client_id":"29274","company_id":"21721661219"},{"client_id":"29363","company_id":"25044283275"},{"client_id":"29540","company_id":"325612932485"},{"client_id":"29540","company_id":"325614998528"},{"client_id":"29540","company_id":"325614434948"},{"client_id":"29585","company_id":"269581724787"},{"client_id":"30076","company_id":"24020592846"},{"client_id":"30328","company_id":"20846833817"},{"client_id":"30346","company_id":"21155589486"},{"client_id":"30346","company_id":"21382559542"},{"client_id":"30346","company_id":"21382269987"},{"client_id":"30422","company_id":"375724081590"},{"client_id":"30442","company_id":"21099882838"},{"client_id":"30442","company_id":"21100256106"},{"client_id":"30442","company_id":"21167654123"},{"client_id":"30442","company_id":"21167667370"},{"client_id":"30442","company_id":"21146790076"},{"client_id":"30442","company_id":"21167043078"},{"client_id":"30442","company_id":"21167695139"},{"client_id":"30442","company_id":"21099492362"},{"client_id":"30442","company_id":"21147780049"},{"client_id":"30442","company_id":"21167338738"},{"client_id":"30442","company_id":"21167734375"},{"client_id":"30442","company_id":"21167283998"},{"client_id":"30442","company_id":"21167704994"},{"client_id":"30442","company_id":"21167940739"},{"client_id":"30442","company_id":"21099098637"},{"client_id":"30442","company_id":"21099778412"},{"client_id":"30442","company_id":"21147743606"},{"client_id":"30442","company_id":"21147127034"},{"client_id":"30442","company_id":"21099021764"},{"client_id":"30442","company_id":"21167883997"},{"client_id":"30442","company_id":"21146752920"},{"client_id":"30442","company_id":"21165492166"},{"client_id":"30442","company_id":"21167720565"},{"client_id":"30442","company_id":"21167315102"},{"client_id":"30442","company_id":"21167593726"},{"client_id":"30442","company_id":"21168666459"},{"client_id":"30442","company_id":"21167531629"},{"client_id":"30442","company_id":"21099609196"},{"client_id":"30442","company_id":"21167682775"},{"client_id":"30442","company_id":"21167547003"},{"client_id":"30442","company_id":"21147018783"},{"client_id":"30442","company_id":"21099747781"},{"client_id":"30442","company_id":"21146654581"},{"client_id":"30442","company_id":"21147077901"},{"client_id":"30442","company_id":"21167303243"},{"client_id":"30442","company_id":"21146600843"},{"client_id":"30442","company_id":"21167867339"},{"client_id":"30442","company_id":"21146963435"},{"client_id":"30442","company_id":"21167822220"},{"client_id":"30442","company_id":"21167792165"},{"client_id":"30442","company_id":"21147229082"},{"client_id":"30442","company_id":"21167855596"},{"client_id":"30442","company_id":"21167903961"},{"client_id":"30442","company_id":"21167373409"},{"client_id":"30442","company_id":"21167843691"},{"client_id":"30442","company_id":"21146868023"},{"client_id":"30442","company_id":"21167030903"},{"client_id":"30442","company_id":"21167745036"},{"client_id":"30442","company_id":"21167634266"},{"client_id":"30442","company_id":"21167775284"},{"client_id":"30442","company_id":"21167559485"},{"client_id":"30442","company_id":"21147041441"},{"client_id":"30442","company_id":"21147298790"},{"client_id":"30482","company_id":"21305888558"},{"client_id":"30517","company_id":"20946552869"},{"client_id":"30658","company_id":"20938119800"},{"client_id":"30701","company_id":"22538494016"},{"client_id":"30701","company_id":"21820292677"},{"client_id":"30864","company_id":"22141826807"},{"client_id":"30964","company_id":"21535134144"},{"client_id":"31059","company_id":"21511253242"},{"client_id":"31141","company_id":"21376269616"},{"client_id":"31288","company_id":"4106280018674"},{"client_id":"31493","company_id":"299275199117"},{"client_id":"31493","company_id":"305540616418"},{"client_id":"31793","company_id":"144916136128"},{"client_id":"31829","company_id":"26495030336"},{"client_id":"31922","company_id":"24629329910"},{"client_id":"32017","company_id":"21900085151"},{"client_id":"32038","company_id":"176636332254"},{"client_id":"32038","company_id":"21982326713"},{"client_id":"32038","company_id":"21941959494"},{"client_id":"32038","company_id":"22227723898"},{"client_id":"32038","company_id":"21982399606"},{"client_id":"32120","company_id":"22830188843"},{"client_id":"32136","company_id":"22161785887"},{"client_id":"32139","company_id":"21930151386"},{"client_id":"32139","company_id":"21930181030"},{"client_id":"32150","company_id":"22537052216"},{"client_id":"32274","company_id":"22059607805"},{"client_id":"32274","company_id":"22059591977"},{"client_id":"32274","company_id":"22059819296"},{"client_id":"32319","company_id":"21644649221"},{"client_id":"32347","company_id":"21836453899"},{"client_id":"32498","company_id":"171265327032"},{"client_id":"32498","company_id":"171265275319"},{"client_id":"32498","company_id":"171265326835"},{"client_id":"32498","company_id":"22274312234"},{"client_id":"32498","company_id":"171265327329"},{"client_id":"32498","company_id":"181128160591"},{"client_id":"32498","company_id":"163651211561"},{"client_id":"32498","company_id":"195275653380"},{"client_id":"32498","company_id":"185412423061"},{"client_id":"32498","company_id":"171265851531"},{"client_id":"32498","company_id":"178236964710"},{"client_id":"32530","company_id":"108239953882"},{"client_id":"32530","company_id":"22363795539"},{"client_id":"32583","company_id":"4004494762703"},{"client_id":"32583","company_id":"3805694794701"},{"client_id":"32583","company_id":"3962824999322"},{"client_id":"32583","company_id":"800460779533"},{"client_id":"32682","company_id":"22433362547"},{"client_id":"32682","company_id":"22435480369"},{"client_id":"32682","company_id":"22385843805"},{"client_id":"32713","company_id":"21909909604"},{"client_id":"32713","company_id":"21913701060"},{"client_id":"32771","company_id":"22362061783"},{"client_id":"33108","company_id":"315109651770"},{"client_id":"33165","company_id":"22022863332"},{"client_id":"33270","company_id":"22086040314"},{"client_id":"33377","company_id":"23282889522"},{"client_id":"33413","company_id":"22363470285"},{"client_id":"33436","company_id":"22314094924"},{"client_id":"33449","company_id":"49176794059"},{"client_id":"33453","company_id":"286047844592"},{"client_id":"33453","company_id":"341657669707"},{"client_id":"33453","company_id":"306123360931"},{"client_id":"33453","company_id":"256199075983"},{"client_id":"33459","company_id":"24411432951"},{"client_id":"33518","company_id":"195880478154"},{"client_id":"33984","company_id":"22540740657"},{"client_id":"34429","company_id":"396379560208"},{"client_id":"34453","company_id":"348641618152"},{"client_id":"34567","company_id":"27714054188"},{"client_id":"34761","company_id":"74789250191"},{"client_id":"34761","company_id":"78094846268"},{"client_id":"34761","company_id":"127020891832"},{"client_id":"34761","company_id":"206511776644"},{"client_id":"35061","company_id":"23159068581"},{"client_id":"35186","company_id":"434667472319"},{"client_id":"35312","company_id":"25189363390"},{"client_id":"35312","company_id":"25634917039"},{"client_id":"35312","company_id":"25052592112"},{"client_id":"35312","company_id":"25189285601"},{"client_id":"35312","company_id":"25189758742"},{"client_id":"35312","company_id":"25057544236"},{"client_id":"35312","company_id":"25085927615"},{"client_id":"35312","company_id":"25086208819"},{"client_id":"35312","company_id":"25052587586"},{"client_id":"35312","company_id":"25189384567"},{"client_id":"35312","company_id":"25189158034"},{"client_id":"35312","company_id":"25189706165"},{"client_id":"35790","company_id":"23687620744"},{"client_id":"37087","company_id":"128447872286"},{"client_id":"37497","company_id":"24024374164"},{"client_id":"37696","company_id":"24484677480"},{"client_id":"37696","company_id":"24484754837"},{"client_id":"38018","company_id":"2782905781980"},{"client_id":"38137","company_id":"24720139618"},{"client_id":"38201","company_id":"24060083879"},{"client_id":"38201","company_id":"1074467329379"},{"client_id":"38201","company_id":"24031410214"},{"client_id":"38214","company_id":"49169151678"},{"client_id":"38352","company_id":"513249661275"},{"client_id":"38754","company_id":"46304234487"},{"client_id":"39210","company_id":"209454350946"},{"client_id":"39356","company_id":"45861619191"},{"client_id":"39356","company_id":"26155601774"},{"client_id":"39356","company_id":"62493496512"},{"client_id":"39356","company_id":"26155599954"},{"client_id":"39356","company_id":"48520215623"},{"client_id":"39356","company_id":"26115268620"},{"client_id":"39356","company_id":"26115268744"},{"client_id":"39356","company_id":"26155601476"},{"client_id":"39356","company_id":"45861619958"},{"client_id":"39356","company_id":"176168792272"},{"client_id":"39356","company_id":"65275735288"},{"client_id":"39356","company_id":"65275773430"},{"client_id":"39356","company_id":"26155601892"},{"client_id":"39356","company_id":"26155616395"},{"client_id":"39356","company_id":"26155600144"},{"client_id":"39435","company_id":"318879604083"},{"client_id":"39504","company_id":"25521442440"},{"client_id":"39629","company_id":"427711982284"},{"client_id":"39663","company_id":"27213365666"},{"client_id":"39940","company_id":"135245661118"},{"client_id":"39940","company_id":"139619123036"},{"client_id":"39940","company_id":"115432602862"},{"client_id":"40560","company_id":"413624991677"},{"client_id":"40746","company_id":"2342594211693"},{"client_id":"41227","company_id":"2718881648976"},{"client_id":"42553","company_id":"160796982567"},{"client_id":"42632","company_id":"195064545662"},{"client_id":"42998","company_id":"189379409443"},{"client_id":"44730","company_id":"305237784156"},{"client_id":"45679","company_id":"430469588066"},{"client_id":"45679","company_id":"430607119002"},{"client_id":"45679","company_id":"341743260144"},{"client_id":"45679","company_id":"518108206750"},{"client_id":"45679","company_id":"430498615915"},{"client_id":"45679","company_id":"444502078975"},{"client_id":"45679","company_id":"430533052239"},{"client_id":"45679","company_id":"430660493038"},{"client_id":"45679","company_id":"415479546202"},{"client_id":"46172","company_id":"231522918767"},{"client_id":"47007","company_id":"254473853716"},{"client_id":"47350","company_id":"2820103320376"},{"client_id":"47726","company_id":"1305458859471"},{"client_id":"47726","company_id":"699950953566"},{"client_id":"48210","company_id":"2417195321099"},{"client_id":"48360","company_id":"395453202725"},{"client_id":"48648","company_id":"3928511001625"},{"client_id":"48748","company_id":"440634319684"},{"client_id":"48748","company_id":"372753740910"},{"client_id":"50190","company_id":"341869864063"},{"client_id":"50226","company_id":"358441787192"},{"client_id":"50226","company_id":"358444826457"},{"client_id":"51426","company_id":"340561162515"},{"client_id":"52875","company_id":"367835147171"},{"client_id":"52875","company_id":"367356400623"},{"client_id":"52875","company_id":"367462700242"},{"client_id":"53063","company_id":"382088345426"},{"client_id":"53278","company_id":"340484969251"},{"client_id":"53707","company_id":"381066874129"},{"client_id":"54809","company_id":"405249733183"},{"client_id":"56366","company_id":"1220692926012"},{"client_id":"56835","company_id":"493687753061"},{"client_id":"56835","company_id":"497292784366"},{"client_id":"56835","company_id":"497291451046"},{"client_id":"59540","company_id":"688496953104"},{"client_id":"60939","company_id":"1017593546435"},{"client_id":"61062","company_id":"3626135620948"},{"client_id":"64519","company_id":"977702474575"},{"client_id":"65480","company_id":"1626492111585"},{"client_id":"65504","company_id":"940806303966"},{"client_id":"68906","company_id":"2123257879608"},{"client_id":"69445","company_id":"1723741734960"},{"client_id":"69687","company_id":"1955674137629"},{"client_id":"69687","company_id":"1894786316758"},{"client_id":"70976","company_id":"2446936924393"},{"client_id":"72783","company_id":"4303361234061"},{"client_id":"72783","company_id":"4070357173699"},{"client_id":"73947","company_id":"2155041457433"},{"client_id":"73947","company_id":"2157187500342"},{"client_id":"73947","company_id":"2163418172092"},{"client_id":"73947","company_id":"2163243892785"},{"client_id":"73947","company_id":"2157201488202"},{"client_id":"73947","company_id":"2155046048893"},{"client_id":"73947","company_id":"2163235483812"},{"client_id":"73947","company_id":"2163392694869"},{"client_id":"73947","company_id":"2157326657794"},{"client_id":"73947","company_id":"2163318486281"},{"client_id":"73947","company_id":"2163265459178"},{"client_id":"73947","company_id":"2157126093680"},{"client_id":"73947","company_id":"2155044576165"},{"client_id":"73947","company_id":"2163249743513"},{"client_id":"73947","company_id":"2155195085578"},{"client_id":"73947","company_id":"1580697163303"},{"client_id":"73947","company_id":"2157140121219"},{"client_id":"73947","company_id":"2155102864118"},{"client_id":"73947","company_id":"2157385513010"},{"client_id":"73947","company_id":"2155192022317"},{"client_id":"73947","company_id":"2157275531374"},{"client_id":"73947","company_id":"2155049634187"},{"client_id":"73947","company_id":"2155044744661"},{"client_id":"73947","company_id":"2163409415553"},{"client_id":"73947","company_id":"2147438539063"},{"client_id":"73947","company_id":"2157288110680"},{"client_id":"73947","company_id":"2163238449575"},{"client_id":"73947","company_id":"2157392585191"},{"client_id":"73947","company_id":"2163269388097"},{"client_id":"73947","company_id":"2157352924087"},{"client_id":"73947","company_id":"2155037448337"},{"client_id":"76796","company_id":"2542992601196"},{"client_id":"78123","company_id":"3017612107383"},{"client_id":"78123","company_id":"2733570008333"},{"client_id":"78123","company_id":"2641543378574"},{"client_id":"80972","company_id":"3679432121075"},{"client_id":"81318","company_id":"3470613497472"},{"client_id":"82910","company_id":"4132026934395"}]';

		$file = json_decode($file, true);

		foreach ($file as $fileItem) {

			try {

				$clientId = $fileItem['client_id'];

				$companyId = $fileItem['company_id'];

				$db = PgActiveRecord::getDbByClientId($clientId);

				if (empty($db)) {

					self::info(('empty db continue:' . $clientId));
					continue;
				}

				$adminUserId = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();

				if (!$adminUserId) {

					self::info(("no admin user, continue:{$clientId}"));
					continue;
				}

				self::info(('[' . $clientId . ']origin company id:' . $companyId));

				User::setLoginUserById($adminUserId);

				$company = new \common\library\customer_v3\company\orm\Company($clientId, $companyId);

				$companyInfo = $company->getAttributes();

				if (!$company->isExist()) {

					self::info(('not exist, continue' . $clientId . ', ' . $companyId));

					continue;
				}


				if (empty($companyInfo['user_id'])) {

					self::info(('empty user_id, continue' . $clientId . ', ' . $companyId));

					continue;
				}

				$sql = 'SELECT company_id, name, short_name, user_id
						FROM tbl_company
						WHERE client_id = ' . $clientId . '
						  AND name LIKE \'' . str_replace("'", "''", $companyInfo['name']) . '-%\'
						  AND short_name LIKE \'' . str_replace("'", "''", $companyInfo['short_name']) . '-%\'
						  AND (user_id @> ARRAY ' . json_encode($companyInfo['user_id']) . '::BIGINT[] IS NOT TRUE
						  OR ARRAY ' . json_encode($companyInfo['user_id']) . '::BIGINT[] @> user_id IS NOT TRUE)
						  and company_id <> ' . $companyId;

				$allCustomerList = $db->createCommand($sql)->queryAll();

				if (empty($allCustomerList)) {

					continue;
				}


				foreach ($allCustomerList as $item) {

					self::info('[added company]' . 'company_id:' . $item['company_id'] . ',name:' . $item['name'] . ',short_name:' . $item['short_name'] . ',user_id:' . $item['user_id']);

					if ($item['user_id'] != '{}') {

						self::info('yuan:' . json_encode($companyInfo['user_id'])
							. ',fen::' . json_encode($item['user_id'])
							. 'add_company_id:' . $item['company_id']
							. 'origin company id:' . $companyId);
					}

					$addedCompany = new \common\library\customer_v3\company\orm\Company($clientId, $item['company_id']);

					if ($dryRun < 1) {

						$addedCompany->setCoverUserFlag(true);

						$old = $addedCompany->getOldAttributes();

						$oldUserId = $old['user_id'];

						$old['user_id'] = [];

						$addedCompany->setOldAttributes($old);

						$addedCompany->addUser($companyInfo['user_id']);

						$addedCompany->setSkipDuplicateCheck(true);

						$addedCompany->setSkipPrivilegeField(true);

						$addedCompany->setCheckQuotaFlag(false);

						$addedCompany->setDuplicateNameRename(true);

						$addedCompany->_skipCheckAcceptChangeGroup = true;

						$addedCompany->setAllowDuplicateName(true);

						$addedCompany->setAllowDuplicateTel(true);

						$addedCompany->save();


						if ($oldUserId != $addedCompany->user_id) {


							$sql = 'UPDATE tbl_customer set user_id = \'' . PgsqlUtil::formatArray($addedCompany->user_id) . '\'
								WHERE client_id = ' . $clientId . '
								  AND company_id = ' . $item['company_id'];

							$result = $db->createCommand($sql)->execute();

						}

						$sql = 'DELETE
								FROM tbl_company_history
								WHERE client_id = ' . $clientId . '
								  AND company_id = ' . $item['company_id'] . '
								  AND type = 18
								  AND update_time > \'' . $companyInfo['create_time'] . '\'::TIMESTAMP WITHOUT TIME ZONE';
						$result = $db->createCommand($sql)->execute();

						(new CustomerSync($clientId))->setFindCompanyId($item['company_id'])->sync();
					}
				}
			} catch (Throwable $throwable) {

				self::info('message: ' . $throwable->getMessage());
				self::info('trace: ' . $throwable->getTraceAsString());
			}
		}

	}


	/**
	 * 删除重复移入公海历史
	 *
	 * @param $clientId
	 * @param $dryRun
	 * @return void
	 * @throws CDbException
	 * @throws ProcessException
	 */
	public function actionFixDiff($clientId = 0, $dryRun = null) {

		if ($clientId) {

			$clientIds = explode(',', $clientId);
		} else {

			$clientIds = array_column($this->getClientList(), 'client_id');
		}

		foreach ($clientIds as $clientId) {

			self::info('clientID: ' . $clientId . ', start');

			$db = PgActiveRecord::getDbByClientId($clientId);

			if (empty($db)) {

				self::info('empty db continue' . $clientId);
				continue;
			}

			try {

				$sql = "SELECT  company_id, MIN(create_time) AS min, MAX(create_time) AS max , (MAX(create_time) - MIN(create_time))
						FROM tbl_company_history
						WHERE client_id = {$clientId} AND type = 17
						GROUP BY company_id , type
						HAVING (MAX(create_time) - MIN(create_time)) < '00:00:11'
						AND COUNT(company_id) > 1
				";

				$result = $db->createCommand($sql)->queryAll();

				self::log('count: ' . count($result) . 'sql: ' . $sql);

				foreach ($result as $item) {

					$infoSql = "SELECT type, company_id, customer_id, update_user, diff, update_type, update_refer, refer_type, refer_id
								FROM tbl_company_history
								WHERE client_id = {$clientId} AND type = 17 AND company_id = {$item['company_id']} AND create_time = '{$item['min']}'
								LIMIT 1";

					$info = $db->createCommand($infoSql)->queryAll();

					$info = $info[0];

					self::log('count: ' . count($info) . 'sql: ' . $infoSql);

					$deleteSql = "DELETE
									FROM tbl_company_history
									WHERE client_id = {$clientId}
									  AND company_id = {$item['company_id']}
									  AND create_time >= '{$item['min']}'
									  AND create_time <= '{$item['max']}'
									  ";

					self::log($deleteSql);

					if (!$dryRun){

						$deleteResult = $db->createCommand($deleteSql)->execute();

						self::info('del result: '.$deleteResult);
					}

					$insert = "INSERT INTO tbl_company_history (client_id, type, company_id, customer_id, update_user, create_time, update_time, diff, update_type, update_refer, refer_type, refer_id)
								VALUES ({$clientId}, {$info['type']}, {$info['company_id']}, {$info['customer_id']}, {$info['update_user']}, '{$item['min']}', '{$item['min']}', '{$info['diff']}' ::JSONB, {$info['update_type']}, {$info['update_refer']}, {$info['refer_type']}, {$info['refer_id']})
					";

					self::log($insert);

					if (!$dryRun){

						$insertResult = $db->createCommand($insert)->execute();

						self::info('add result: '.$insertResult);
					}
				}

				self::info('clientID: ' . $clientId . ', end');

			} catch (Throwable $throwable) {

				self::info('message: ' . $throwable->getMessage());
				self::info('trace: ' . $throwable->getTraceAsString());
			}
		}

	}

	public function actionChunkCustomer($clientId = 0, $dryRun = null) {

		$file = '[{"client_id":21,"company_id":*********},{"client_id":32498,"company_id":171265327032},{"client_id":7827,"company_id":4024079435},{"client_id":7827,"company_id":4024071076},{"client_id":28718,"company_id":25461973307},{"client_id":30442,"company_id":21099882838},{"client_id":23736,"company_id":22271902663},{"client_id":10039,"company_id":16572594598},{"client_id":7827,"company_id":4023976177},{"client_id":30442,"company_id":21100256106},{"client_id":14136,"company_id":7786534787},{"client_id":4957,"company_id":24906120843},{"client_id":4957,"company_id":106713389229},{"client_id":73947,"company_id":2155041457433},{"client_id":30442,"company_id":21167654123},{"client_id":7827,"company_id":4023749827},{"client_id":30517,"company_id":20946552869},{"client_id":16798,"company_id":10336127046},{"client_id":31141,"company_id":21376269616},{"client_id":4115,"company_id":2202459932},{"client_id":30442,"company_id":21167667370},{"client_id":33436,"company_id":22314094924},{"client_id":35061,"company_id":23159068581},{"client_id":38201,"company_id":24060083879},{"client_id":10039,"company_id":17583814714},{"client_id":9437,"company_id":13082691249},{"client_id":39356,"company_id":45861619191},{"client_id":17766,"company_id":11620125072},{"client_id":35312,"company_id":25189363390},{"client_id":10039,"company_id":10692727423},{"client_id":32498,"company_id":171265275319},{"client_id":7827,"company_id":4023986915},{"client_id":30442,"company_id":21146790076},{"client_id":30442,"company_id":21167043078},{"client_id":10039,"company_id":17591794066},{"client_id":6856,"company_id":3553962559},{"client_id":48210,"company_id":2417195321099},{"client_id":32038,"company_id":176636332254},{"client_id":65504,"company_id":940806303966},{"client_id":2074,"company_id":5996973529},{"client_id":35312,"company_id":25634917039},{"client_id":4957,"company_id":10701609275},{"client_id":45679,"company_id":430469588066},{"client_id":28539,"company_id":24773358765},{"client_id":14136,"company_id":7851547892},{"client_id":10038,"company_id":15730915986},{"client_id":66,"company_id":4076657145},{"client_id":56835,"company_id":493687753061},{"client_id":17109,"company_id":12414998844},{"client_id":23669,"company_id":17770512229},{"client_id":24920,"company_id":16942000483},{"client_id":12889,"company_id":7832725099},{"client_id":61062,"company_id":3626135620948},{"client_id":10039,"company_id":10323735506},{"client_id":25340,"company_id":19281277323},{"client_id":22770,"company_id":19574372944},{"client_id":1379,"company_id":*********},{"client_id":27359,"company_id":198114311530},{"client_id":28837,"company_id":21031037185},{"client_id":7827,"company_id":4023909635},{"client_id":10039,"company_id":10300491487},{"client_id":7827,"company_id":4036108073},{"client_id":46172,"company_id":231522918767},{"client_id":48360,"company_id":395453202725},{"client_id":5818,"company_id":18358079219},{"client_id":10039,"company_id":17618522141},{"client_id":32498,"company_id":171265326835},{"client_id":2074,"company_id":8026780727},{"client_id":39356,"company_id":26155601774},{"client_id":39356,"company_id":62493496512},{"client_id":10039,"company_id":17693090790},{"client_id":23669,"company_id":17770288040},{"client_id":28375,"company_id":20384767617},{"client_id":10039,"company_id":17652399923},{"client_id":7827,"company_id":4023851130},{"client_id":11283,"company_id":7721286306},{"client_id":7827,"company_id":4023880350},{"client_id":17161,"company_id":10238975330},{"client_id":6005,"company_id":3313463668},{"client_id":4208,"company_id":2260816862},{"client_id":38201,"company_id":1074467329379},{"client_id":53063,"company_id":382088345426},{"client_id":1662,"company_id":3650147550},{"client_id":73947,"company_id":2157187500342},{"client_id":39356,"company_id":26155599954},{"client_id":30442,"company_id":21167695139},{"client_id":2177,"company_id":*********},{"client_id":73947,"company_id":2163418172092},{"client_id":16165,"company_id":12845654392},{"client_id":24951,"company_id":74984344858},{"client_id":7827,"company_id":4023790273},{"client_id":19383,"company_id":11163579633},{"client_id":69687,"company_id":1955674137629},{"client_id":47007,"company_id":254473853716},{"client_id":31493,"company_id":299275199117},{"client_id":10039,"company_id":17670168692},{"client_id":48748,"company_id":440634319684},{"client_id":40746,"company_id":2342594211693},{"client_id":1662,"company_id":2576511756969},{"client_id":34429,"company_id":396379560208},{"client_id":1662,"company_id":*********},{"client_id":9968,"company_id":5655043876},{"client_id":45679,"company_id":430607119002},{"client_id":30442,"company_id":21099492362},{"client_id":10039,"company_id":17588087817},{"client_id":10039,"company_id":10849809429},{"client_id":27359,"company_id":198110757377},{"client_id":37087,"company_id":128447872286},{"client_id":7827,"company_id":4024056443},{"client_id":24742,"company_id":17465178823},{"client_id":10039,"company_id":17469753237},{"client_id":34761,"company_id":74789250191},{"client_id":52875,"company_id":367835147171},{"client_id":20442,"company_id":19901766496},{"client_id":10039,"company_id":17617567770},{"client_id":10039,"company_id":17592052422},{"client_id":1077,"company_id":3633141913},{"client_id":37696,"company_id":24484677480},{"client_id":34761,"company_id":78094846268},{"client_id":39356,"company_id":48520215623},{"client_id":38754,"company_id":46304234487},{"client_id":73947,"company_id":2163243892785},{"client_id":73947,"company_id":2157201488202},{"client_id":56366,"company_id":1220692926012},{"client_id":16798,"company_id":19123270658},{"client_id":10039,"company_id":17461677299},{"client_id":32150,"company_id":22537052216},{"client_id":73947,"company_id":2155046048893},{"client_id":5105,"company_id":9182808067},{"client_id":7153,"company_id":3820856085},{"client_id":3178,"company_id":4615677291},{"client_id":37497,"company_id":24024374164},{"client_id":22622,"company_id":14578401417},{"client_id":5231,"company_id":11407670315},{"client_id":30442,"company_id":21147780049},{"client_id":14136,"company_id":7787172213},{"client_id":13057,"company_id":7341324549},{"client_id":4957,"company_id":7613818942},{"client_id":81318,"company_id":3470613497472},{"client_id":5810,"company_id":8367214366},{"client_id":7827,"company_id":4023924577},{"client_id":30442,"company_id":21167338738},{"client_id":30442,"company_id":21167734375},{"client_id":10039,"company_id":11135446595},{"client_id":8999,"company_id":9229415912},{"client_id":73947,"company_id":2163235483812},{"client_id":4936,"company_id":4005954637},{"client_id":23669,"company_id":17770549138},{"client_id":17392,"company_id":431639593529},{"client_id":29072,"company_id":20464006579},{"client_id":7195,"company_id":21483843897},{"client_id":33453,"company_id":286047844592},{"client_id":30442,"company_id":21167283998},{"client_id":4957,"company_id":3606890061},{"client_id":21066,"company_id":16949182972},{"client_id":10039,"company_id":17605173557},{"client_id":4957,"company_id":6576239867},{"client_id":29585,"company_id":269581724787},{"client_id":73947,"company_id":2163392694869},{"client_id":23669,"company_id":17770265750},{"client_id":23146,"company_id":14939307455},{"client_id":32274,"company_id":22059607805},{"client_id":39356,"company_id":26115268620},{"client_id":28967,"company_id":93218696410},{"client_id":24017,"company_id":15931375404},{"client_id":1662,"company_id":2576508457942},{"client_id":41227,"company_id":2718881648976},{"client_id":30442,"company_id":21167704994},{"client_id":7827,"company_id":4024011481},{"client_id":5406,"company_id":3079121810},{"client_id":7827,"company_id":4024015571},{"client_id":23669,"company_id":17770265033},{"client_id":10039,"company_id":9556466740},{"client_id":10039,"company_id":16715665996},{"client_id":1662,"company_id":21117363548},{"client_id":64519,"company_id":1937414080164},{"client_id":24951,"company_id":121402394344},{"client_id":82910,"company_id":4132026934395},{"client_id":22664,"company_id":15043310956},{"client_id":30442,"company_id":21167940739},{"client_id":10039,"company_id":16874478675},{"client_id":24951,"company_id":83145408964},{"client_id":26016,"company_id":18019326428},{"client_id":32498,"company_id":22274312234},{"client_id":4957,"company_id":2019191807786},{"client_id":27202,"company_id":20975651339},{"client_id":31288,"company_id":4106280018674},{"client_id":56835,"company_id":497291451046},{"client_id":3007,"company_id":1536258841},{"client_id":5603,"company_id":16493325192},{"client_id":7004,"company_id":24202351791},{"client_id":24951,"company_id":154638847739},{"client_id":30701,"company_id":22538494016},{"client_id":32583,"company_id":4004494762703},{"client_id":16165,"company_id":11676556183},{"client_id":23539,"company_id":19128006515},{"client_id":10039,"company_id":10699963504},{"client_id":24951,"company_id":91898010826},{"client_id":4957,"company_id":6540131090},{"client_id":7956,"company_id":4042514988},{"client_id":10039,"company_id":17583548243},{"client_id":10039,"company_id":16689960967},{"client_id":27359,"company_id":144735460892},{"client_id":32038,"company_id":21982326713},{"client_id":32319,"company_id":21644649221},{"client_id":24920,"company_id":16937344216},{"client_id":73947,"company_id":2157326657794},{"client_id":78123,"company_id":3017612107383},{"client_id":5007,"company_id":9298215320},{"client_id":3140,"company_id":16664862400},{"client_id":29270,"company_id":20825150489},{"client_id":32713,"company_id":21909909604},{"client_id":10039,"company_id":16884864949},{"client_id":30442,"company_id":21099098637},{"client_id":30442,"company_id":21099778412},{"client_id":39435,"company_id":318879604083},{"client_id":27359,"company_id":144730953133},{"client_id":2737,"company_id":1487350541},{"client_id":4957,"company_id":22534365193},{"client_id":53707,"company_id":381066874129},{"client_id":7827,"company_id":4024024845},{"client_id":45679,"company_id":341743260144},{"client_id":5058,"company_id":12120006598},{"client_id":1954,"company_id":4679447155113},{"client_id":30442,"company_id":21147743606},{"client_id":30482,"company_id":21305888558},{"client_id":18611,"company_id":11584953378},{"client_id":33449,"company_id":49176794059},{"client_id":28149,"company_id":19994678396},{"client_id":51426,"company_id":340561162515},{"client_id":73947,"company_id":2163318486281},{"client_id":10039,"company_id":17482125813},{"client_id":6238,"company_id":3369633045},{"client_id":7827,"company_id":4023905640},{"client_id":73947,"company_id":2163265459178},{"client_id":38352,"company_id":513249661275},{"client_id":30442,"company_id":21147127034},{"client_id":30442,"company_id":21099021764},{"client_id":24017,"company_id":16096777888},{"client_id":33453,"company_id":341657669707},{"client_id":32682,"company_id":22433362547},{"client_id":7827,"company_id":4024004125},{"client_id":73947,"company_id":2157126093680},{"client_id":15320,"company_id":18833791788},{"client_id":10039,"company_id":17633181341},{"client_id":10039,"company_id":16816301419},{"client_id":31829,"company_id":26495030336},{"client_id":3007,"company_id":1536267370},{"client_id":39940,"company_id":135245661118},{"client_id":7827,"company_id":4024042128},{"client_id":28539,"company_id":75224548401},{"client_id":3817,"company_id":2045484880},{"client_id":1954,"company_id":4420361050676},{"client_id":32530,"company_id":108239953882},{"client_id":27434,"company_id":19976812301},{"client_id":10039,"company_id":17619166403},{"client_id":16624,"company_id":409821546721},{"client_id":10039,"company_id":10564730693},{"client_id":537,"company_id":10618948206},{"client_id":30442,"company_id":21167883997},{"client_id":30442,"company_id":21146752920},{"client_id":10039,"company_id":17469872512},{"client_id":30442,"company_id":21165492166},{"client_id":28539,"company_id":209204499960},{"client_id":39356,"company_id":26115268744},{"client_id":5587,"company_id":4684302326},{"client_id":23187,"company_id":17449529178},{"client_id":30346,"company_id":21155589486},{"client_id":10039,"company_id":5162430991},{"client_id":4029,"company_id":3105603482},{"client_id":19807,"company_id":23147260110},{"client_id":16488,"company_id":10737152224},{"client_id":34567,"company_id":27714054188},{"client_id":11283,"company_id":7721246282},{"client_id":10039,"company_id":17601923186},{"client_id":4957,"company_id":341831801233},{"client_id":7827,"company_id":4024184995},{"client_id":15715,"company_id":19746351948},{"client_id":12380,"company_id":22900792419},{"client_id":70976,"company_id":2446936924393},{"client_id":32136,"company_id":22161785887},{"client_id":5850,"company_id":12370862093},{"client_id":12225,"company_id":6832935349},{"client_id":26978,"company_id":21341740910},{"client_id":7827,"company_id":4023825347},{"client_id":29363,"company_id":25044283275},{"client_id":30442,"company_id":21167720565},{"client_id":32682,"company_id":22435480369},{"client_id":4061,"company_id":17622837200},{"client_id":23304,"company_id":24649010047},{"client_id":35312,"company_id":25052592112},{"client_id":6856,"company_id":3553875904},{"client_id":2213,"company_id":2498412728},{"client_id":1662,"company_id":*********},{"client_id":32274,"company_id":22059591977},{"client_id":73947,"company_id":2155044576165},{"client_id":3140,"company_id":16664653090},{"client_id":32498,"company_id":171265327329},{"client_id":29540,"company_id":325612932485},{"client_id":23736,"company_id":22270281906},{"client_id":7827,"company_id":4023847700},{"client_id":4957,"company_id":3105221291},{"client_id":32139,"company_id":21930151386},{"client_id":4858,"company_id":2710758034},{"client_id":5552,"company_id":3517885202},{"client_id":13999,"company_id":84334741314},{"client_id":42553,"company_id":160796982567},{"client_id":23221,"company_id":16983015772},{"client_id":1954,"company_id":4420897214159},{"client_id":2315,"company_id":3084095522},{"client_id":39356,"company_id":26155601476},{"client_id":4957,"company_id":22664126221},{"client_id":35312,"company_id":25189285601},{"client_id":9738,"company_id":5234936489},{"client_id":27359,"company_id":83038622924},{"client_id":18100,"company_id":20670400416},{"client_id":20300,"company_id":12032434992},{"client_id":10039,"company_id":17448966750},{"client_id":12584,"company_id":12448314789},{"client_id":27202,"company_id":20978426654},{"client_id":34453,"company_id":348641618152},{"client_id":47726,"company_id":1305458859471},{"client_id":27359,"company_id":283344513454},{"client_id":1985,"company_id":5300981362},{"client_id":27359,"company_id":266704879134},{"client_id":23930,"company_id":15275337817},{"client_id":42998,"company_id":189379409443},{"client_id":24894,"company_id":17190808909},{"client_id":28400,"company_id":22000426773},{"client_id":21066,"company_id":17066386560},{"client_id":32139,"company_id":21930181030},{"client_id":27434,"company_id":19977034638},{"client_id":30346,"company_id":21382559542},{"client_id":38137,"company_id":24720139618},{"client_id":6879,"company_id":11197666364},{"client_id":7827,"company_id":4023875196},{"client_id":18611,"company_id":11585112033},{"client_id":18611,"company_id":11583833733},{"client_id":23669,"company_id":17770564017},{"client_id":10039,"company_id":16670236486},{"client_id":30442,"company_id":21167315102},{"client_id":10039,"company_id":17619432945},{"client_id":21066,"company_id":16854393452},{"client_id":7827,"company_id":4023994538},{"client_id":3817,"company_id":2045487063},{"client_id":16165,"company_id":12784541157},{"client_id":15879,"company_id":19259451008},{"client_id":73947,"company_id":2163249743513},{"client_id":5007,"company_id":13171262170},{"client_id":24508,"company_id":18951384682},{"client_id":72783,"company_id":4303361234061},{"client_id":32583,"company_id":3020015621241},{"client_id":10039,"company_id":17432200186},{"client_id":7827,"company_id":4024020479},{"client_id":73947,"company_id":2155195085578},{"client_id":31059,"company_id":21511253242},{"client_id":1155,"company_id":*********},{"client_id":33453,"company_id":306123360931},{"client_id":9047,"company_id":280170161290},{"client_id":30442,"company_id":21167593726},{"client_id":23736,"company_id":23278324526},{"client_id":10039,"company_id":9595345459},{"client_id":1662,"company_id":*********},{"client_id":34761,"company_id":127020891832},{"client_id":4957,"company_id":6539742995},{"client_id":32713,"company_id":21913701060},{"client_id":9776,"company_id":16846814213},{"client_id":27359,"company_id":358281337759},{"client_id":24017,"company_id":48490721738},{"client_id":5303,"company_id":13567013337},{"client_id":21055,"company_id":12556202904},{"client_id":24040,"company_id":16282564875},{"client_id":19576,"company_id":11293983539},{"client_id":13999,"company_id":13408785393},{"client_id":30328,"company_id":20846833817},{"client_id":4115,"company_id":2202455085},{"client_id":17642,"company_id":12952047927},{"client_id":20867,"company_id":12696209518},{"client_id":27587,"company_id":32295436215},{"client_id":13999,"company_id":7639203502},{"client_id":999,"company_id":*********},{"client_id":7827,"company_id":4023801662},{"client_id":7827,"company_id":4024192526},{"client_id":23487,"company_id":15243955370},{"client_id":24951,"company_id":2406479157177},{"client_id":3140,"company_id":16664591937},{"client_id":32038,"company_id":21941959494},{"client_id":10039,"company_id":16876422121},{"client_id":3093,"company_id":8284943852},{"client_id":30442,"company_id":21168666459},{"client_id":17751,"company_id":10127263340},{"client_id":7827,"company_id":4024066357},{"client_id":7827,"company_id":4036115451},{"client_id":33459,"company_id":24411432951},{"client_id":28400,"company_id":22005896100},{"client_id":23101,"company_id":14994809519},{"client_id":27359,"company_id":113700458193},{"client_id":24831,"company_id":16306654242},{"client_id":23736,"company_id":22272122794},{"client_id":28150,"company_id":20428049080},{"client_id":17392,"company_id":431642348348},{"client_id":789,"company_id":21877727252},{"client_id":48748,"company_id":372753740910},{"client_id":10039,"company_id":17592531693},{"client_id":45679,"company_id":518108206750},{"client_id":24951,"company_id":158137979937},{"client_id":73947,"company_id":1580697163303},{"client_id":78123,"company_id":2733570008333},{"client_id":27359,"company_id":330448254255},{"client_id":26719,"company_id":19209204363},{"client_id":10039,"company_id":17482827328},{"client_id":16165,"company_id":12380693246},{"client_id":34761,"company_id":206511776644},{"client_id":1662,"company_id":21718993435},{"client_id":4957,"company_id":424996103961},{"client_id":5105,"company_id":8529747169},{"client_id":56835,"company_id":498002852613},{"client_id":31922,"company_id":24629329910},{"client_id":7827,"company_id":4023935363},{"client_id":47350,"company_id":2820103320376},{"client_id":13252,"company_id":7688713476},{"client_id":10039,"company_id":11302357879},{"client_id":4228,"company_id":5485948647},{"client_id":29270,"company_id":20748693767},{"client_id":299,"company_id":*********},{"client_id":6764,"company_id":3659469778},{"client_id":7827,"company_id":4024011388},{"client_id":19972,"company_id":12155460382},{"client_id":6917,"company_id":1735867189213},{"client_id":30442,"company_id":21167531629},{"client_id":1954,"company_id":140235145884},{"client_id":27359,"company_id":238840123677},{"client_id":7827,"company_id":4024098739},{"client_id":73947,"company_id":2157140121219},{"client_id":68906,"company_id":2123257879608},{"client_id":30442,"company_id":21099609196},{"client_id":7827,"company_id":4024204809},{"client_id":7827,"company_id":4024015559},{"client_id":73947,"company_id":2155102864118},{"client_id":2315,"company_id":3090130013},{"client_id":30442,"company_id":21167682775},{"client_id":59540,"company_id":688496953104},{"client_id":38214,"company_id":49169151678},{"client_id":4422,"company_id":9471042223},{"client_id":16842,"company_id":22981698199},{"client_id":32530,"company_id":22363795539},{"client_id":12380,"company_id":22900783592},{"client_id":5270,"company_id":438879836517},{"client_id":1257,"company_id":4833288837},{"client_id":4570,"company_id":3526803595},{"client_id":24951,"company_id":58046486517},{"client_id":16165,"company_id":12945656301},{"client_id":28691,"company_id":21209774519},{"client_id":32038,"company_id":22227723898},{"client_id":80972,"company_id":3679432121075},{"client_id":30442,"company_id":21167547003},{"client_id":7827,"company_id":4024033098},{"client_id":5850,"company_id":12259446881},{"client_id":7827,"company_id":4024131630},{"client_id":30442,"company_id":21147018783},{"client_id":45679,"company_id":430498615915},{"client_id":31793,"company_id":144916136128},{"client_id":28929,"company_id":1247198482463},{"client_id":32274,"company_id":22059819296},{"client_id":22306,"company_id":14630858894},{"client_id":45679,"company_id":444502078975},{"client_id":35312,"company_id":25189758742},{"client_id":21931,"company_id":20181745022},{"client_id":4957,"company_id":24909025918},{"client_id":12584,"company_id":12476485386},{"client_id":30442,"company_id":21099747781},{"client_id":73947,"company_id":2157385513010},{"client_id":10039,"company_id":10849248934},{"client_id":7310,"company_id":3903965023},{"client_id":73947,"company_id":2155192022317},{"client_id":7212,"company_id":4637066386},{"client_id":32583,"company_id":3805694794701},{"client_id":45679,"company_id":430533052239},{"client_id":27104,"company_id":302797343867},{"client_id":17964,"company_id":11481424706},{"client_id":7827,"company_id":4024181217},{"client_id":33377,"company_id":23282889522},{"client_id":33413,"company_id":22363470285},{"client_id":30442,"company_id":21146654581},{"client_id":30442,"company_id":21147077901},{"client_id":10039,"company_id":17590850499},{"client_id":5144,"company_id":2884138490},{"client_id":22854,"company_id":16563500382},{"client_id":10039,"company_id":10802031936},{"client_id":73947,"company_id":2157275531374},{"client_id":26026,"company_id":18180928323},{"client_id":21882,"company_id":14161219309},{"client_id":10038,"company_id":245837885305},{"client_id":3794,"company_id":3587728336},{"client_id":33984,"company_id":22540740657},{"client_id":23915,"company_id":20823904868},{"client_id":1662,"company_id":20916023736},{"client_id":30964,"company_id":21535134144},{"client_id":30864,"company_id":22141826807},{"client_id":25340,"company_id":19281305079},{"client_id":7827,"company_id":4036111223},{"client_id":30701,"company_id":21820292677},{"client_id":33165,"company_id":22022863332},{"client_id":73947,"company_id":2155049634187},{"client_id":10039,"company_id":17501268370},{"client_id":42632,"company_id":195064545662},{"client_id":30442,"company_id":21167303243},{"client_id":32498,"company_id":181128160591},{"client_id":45679,"company_id":430660493038},{"client_id":13999,"company_id":18066819806},{"client_id":39356,"company_id":45861619958},{"client_id":20167,"company_id":12105308117},{"client_id":7827,"company_id":4023833423},{"client_id":24742,"company_id":16701279960},{"client_id":10039,"company_id":5162424899},{"client_id":73947,"company_id":2155044744661},{"client_id":73947,"company_id":2163409415553},{"client_id":27359,"company_id":113701511577},{"client_id":44730,"company_id":305237784156},{"client_id":6856,"company_id":3553965118},{"client_id":20167,"company_id":12125504434},{"client_id":73947,"company_id":2147438539063},{"client_id":39629,"company_id":427711982284},{"client_id":10039,"company_id":17450882227},{"client_id":78123,"company_id":2641543378574},{"client_id":7827,"company_id":4023940255},{"client_id":21131,"company_id":16380043560},{"client_id":39356,"company_id":176168792272},{"client_id":39356,"company_id":65275735288},{"client_id":25117,"company_id":18123296450},{"client_id":3993,"company_id":11515549366},{"client_id":6347,"company_id":4609238104},{"client_id":5850,"company_id":12324323259},{"client_id":27749,"company_id":20245353647},{"client_id":27359,"company_id":113704539467},{"client_id":7827,"company_id":4024123281},{"client_id":3817,"company_id":2045474293},{"client_id":73947,"company_id":2157288110680},{"client_id":53278,"company_id":340484969251},{"client_id":23982,"company_id":18498446174},{"client_id":30442,"company_id":21146600843},{"client_id":10039,"company_id":17671795585},{"client_id":4957,"company_id":6540109024},{"client_id":2152,"company_id":5074828141},{"client_id":14666,"company_id":12513731562},{"client_id":981,"company_id":2011347036},{"client_id":60939,"company_id":1017593546435},{"client_id":12380,"company_id":22900769915},{"client_id":10039,"company_id":5162433956},{"client_id":25408,"company_id":19812739875},{"client_id":10039,"company_id":11167464142},{"client_id":30076,"company_id":24020592846},{"client_id":32498,"company_id":163651211561},{"client_id":48648,"company_id":3928511001625},{"client_id":52875,"company_id":367356400623},{"client_id":33270,"company_id":22086040314},{"client_id":30442,"company_id":21167867339},{"client_id":32771,"company_id":22362061783},{"client_id":789,"company_id":1808259532},{"client_id":7827,"company_id":4024113032},{"client_id":7827,"company_id":4023840076},{"client_id":7827,"company_id":4023828870},{"client_id":23546,"company_id":16063454422},{"client_id":4957,"company_id":6540087983},{"client_id":4957,"company_id":2759812446},{"client_id":32583,"company_id":3962824999322},{"client_id":3214,"company_id":3750148553951},{"client_id":16165,"company_id":12379156556},{"client_id":1662,"company_id":2576505093316},{"client_id":39356,"company_id":65275773430},{"client_id":32583,"company_id":2260467360165},{"client_id":19253,"company_id":14744785105},{"client_id":35312,"company_id":25057544236},{"client_id":37696,"company_id":24484754837},{"client_id":9027,"company_id":13945961385},{"client_id":7827,"company_id":4024061550},{"client_id":27981,"company_id":19811369918},{"client_id":10039,"company_id":17691094492},{"client_id":32682,"company_id":22385843805},{"client_id":10039,"company_id":10564439356},{"client_id":1954,"company_id":209832381690},{"client_id":2447,"company_id":1139092143},{"client_id":27104,"company_id":384545123039},{"client_id":35790,"company_id":23687620744},{"client_id":398,"company_id":*********},{"client_id":1662,"company_id":2576510154664},{"client_id":691,"company_id":1719352383},{"client_id":30442,"company_id":21146963435},{"client_id":30442,"company_id":21167822220},{"client_id":30442,"company_id":21167792165},{"client_id":12584,"company_id":12476430724},{"client_id":19371,"company_id":12677347639},{"client_id":39940,"company_id":139619123036},{"client_id":30442,"company_id":21147229082},{"client_id":30442,"company_id":21167855596},{"client_id":5530,"company_id":17890022049},{"client_id":27507,"company_id":21972914449},{"client_id":7827,"company_id":4023946534},{"client_id":4115,"company_id":2202455093},{"client_id":4700,"company_id":17791504611},{"client_id":16165,"company_id":12324116267},{"client_id":40560,"company_id":413624991677},{"client_id":38201,"company_id":24031410214},{"client_id":8147,"company_id":6249119514},{"client_id":23669,"company_id":17773649697},{"client_id":35312,"company_id":25085927615},{"client_id":4957,"company_id":13612125390},{"client_id":19972,"company_id":12155095335},{"client_id":25796,"company_id":17721301149},{"client_id":39356,"company_id":26155601892},{"client_id":33518,"company_id":195880478154},{"client_id":5105,"company_id":2840121061},{"client_id":12584,"company_id":12476432333},{"client_id":10039,"company_id":10995668925},{"client_id":27359,"company_id":302293230683},{"client_id":26207,"company_id":18159234480},{"client_id":7827,"company_id":4023896552},{"client_id":10039,"company_id":17460881203},{"client_id":10039,"company_id":13996608022},{"client_id":10039,"company_id":17613386124},{"client_id":35186,"company_id":434667472319},{"client_id":10039,"company_id":17619211764},{"client_id":10039,"company_id":17582311300},{"client_id":32498,"company_id":195275653380},{"client_id":3207,"company_id":21496267474},{"client_id":39940,"company_id":115432602862},{"client_id":23920,"company_id":15924998863},{"client_id":24491,"company_id":16139354684},{"client_id":10039,"company_id":17630355938},{"client_id":30442,"company_id":21167903961},{"client_id":7827,"company_id":4023946827},{"client_id":38018,"company_id":2782905781980},{"client_id":1662,"company_id":2576506127371},{"client_id":29137,"company_id":21928439748},{"client_id":27359,"company_id":302295177086},{"client_id":76796,"company_id":2542992601196},{"client_id":54809,"company_id":405249733183},{"client_id":28929,"company_id":2396368246441},{"client_id":25315,"company_id":18636265534},{"client_id":5105,"company_id":8546738552},{"client_id":27359,"company_id":218664144353},{"client_id":32038,"company_id":21982399606},{"client_id":39356,"company_id":26155616395},{"client_id":30442,"company_id":21167373409},{"client_id":5007,"company_id":9300157685},{"client_id":69445,"company_id":1723741734960},{"client_id":73947,"company_id":2163238449575},{"client_id":6856,"company_id":3553888262},{"client_id":3007,"company_id":1536270049},{"client_id":3207,"company_id":1731106891571},{"client_id":20830,"company_id":323271840066},{"client_id":30346,"company_id":21382269987},{"client_id":5303,"company_id":13567012664},{"client_id":1662,"company_id":20951381013},{"client_id":29116,"company_id":20971268671},{"client_id":24880,"company_id":18306968327},{"client_id":7827,"company_id":4023801572},{"client_id":7827,"company_id":4023969997},{"client_id":23930,"company_id":15279359877},{"client_id":3207,"company_id":4409367614},{"client_id":4611,"company_id":24481886919},{"client_id":73947,"company_id":2157392585191},{"client_id":30442,"company_id":21167843691},{"client_id":4957,"company_id":2759814589},{"client_id":27359,"company_id":385370835367},{"client_id":50190,"company_id":341869864063},{"client_id":23925,"company_id":15190890423},{"client_id":7827,"company_id":4023903750},{"client_id":35312,"company_id":25086208819},{"client_id":16165,"company_id":12379204857},{"client_id":22389,"company_id":15860889036},{"client_id":10039,"company_id":17461025306},{"client_id":4632,"company_id":2729411314},{"client_id":26978,"company_id":21351846374},{"client_id":16165,"company_id":12703830721},{"client_id":1560,"company_id":*********},{"client_id":29540,"company_id":325614998528},{"client_id":17109,"company_id":12415014920},{"client_id":21066,"company_id":16819339562},{"client_id":23982,"company_id":18498421720},{"client_id":12584,"company_id":12511096119},{"client_id":1662,"company_id":21041369793},{"client_id":39356,"company_id":26155600144},{"client_id":50226,"company_id":358441787192},{"client_id":69687,"company_id":1894786316758},{"client_id":10039,"company_id":17481414517},{"client_id":16250,"company_id":9899401824},{"client_id":73947,"company_id":2163269388097},{"client_id":15566,"company_id":8823071673},{"client_id":35312,"company_id":25052587586},{"client_id":21374,"company_id":15039533585},{"client_id":26468,"company_id":4259119509160},{"client_id":12584,"company_id":12476450256},{"client_id":7827,"company_id":4023817945},{"client_id":32017,"company_id":21900085151},{"client_id":72783,"company_id":4070357173699},{"client_id":10039,"company_id":9523323254},{"client_id":30442,"company_id":21146868023},{"client_id":30442,"company_id":21167030903},{"client_id":6594,"company_id":5802362321},{"client_id":18617,"company_id":704455960037},{"client_id":30442,"company_id":21167745036},{"client_id":12584,"company_id":12513219978},{"client_id":12584,"company_id":12476474874},{"client_id":35312,"company_id":25189384567},{"client_id":10039,"company_id":17618287534},{"client_id":7827,"company_id":4023919874},{"client_id":32498,"company_id":185412423061},{"client_id":6856,"company_id":3553959212},{"client_id":52875,"company_id":367462700242},{"client_id":7827,"company_id":4023859087},{"client_id":45679,"company_id":415479546202},{"client_id":27675,"company_id":3297320123751},{"client_id":12584,"company_id":12448329387},{"client_id":47726,"company_id":699950953566},{"client_id":39504,"company_id":25521442440},{"client_id":35312,"company_id":25189158034},{"client_id":17520,"company_id":12673731023},{"client_id":23191,"company_id":15927859261},{"client_id":26016,"company_id":18019305830},{"client_id":32498,"company_id":171265851531},{"client_id":50226,"company_id":358444826457},{"client_id":73947,"company_id":2157352924087},{"client_id":25667,"company_id":19575211081},{"client_id":22122,"company_id":14633601765},{"client_id":26562,"company_id":18639635630},{"client_id":27359,"company_id":99792019282},{"client_id":27595,"company_id":20147076800},{"client_id":29274,"company_id":22048693070},{"client_id":18611,"company_id":11585591788},{"client_id":30442,"company_id":21167634266},{"client_id":925,"company_id":*********},{"client_id":3207,"company_id":19289699883},{"client_id":1409,"company_id":*********},{"client_id":6856,"company_id":3553958123},{"client_id":3028,"company_id":4264863363},{"client_id":30442,"company_id":21167775284},{"client_id":7827,"company_id":4023774593},{"client_id":23565,"company_id":15238774772},{"client_id":29274,"company_id":21721661219},{"client_id":35312,"company_id":25189706165},{"client_id":18250,"company_id":11724465228},{"client_id":6856,"company_id":3553875928},{"client_id":27359,"company_id":414748290615},{"client_id":29540,"company_id":325614434948},{"client_id":22770,"company_id":19575120829},{"client_id":5552,"company_id":3535881682},{"client_id":3993,"company_id":11010177810},{"client_id":10039,"company_id":16693144799},{"client_id":32498,"company_id":178236964710},{"client_id":30442,"company_id":21167559485},{"client_id":22770,"company_id":19574079254},{"client_id":7153,"company_id":3758187272},{"client_id":23736,"company_id":20996324978},{"client_id":17159,"company_id":13292111864},{"client_id":7267,"company_id":4602633442},{"client_id":10039,"company_id":17617184596},{"client_id":73947,"company_id":2155037448337},{"client_id":30442,"company_id":21147041441},{"client_id":32583,"company_id":800460779533},{"client_id":65480,"company_id":1626492111585},{"client_id":2230,"company_id":*********},{"client_id":30442,"company_id":21147298790},{"client_id":26140,"company_id":18428555379},{"client_id":3864,"company_id":2053645636},{"client_id":23736,"company_id":22271563191},{"client_id":33108,"company_id":315109651770},{"client_id":33453,"company_id":256199075983},{"client_id":39210,"company_id":209454350946},{"client_id":31493,"company_id":305540616418},{"client_id":20076,"company_id":19029474562},{"client_id":795,"company_id":*********},{"client_id":7251,"company_id":3912026404},{"client_id":10039,"company_id":16688181127},{"client_id":24000,"company_id":16458673454},{"client_id":16798,"company_id":19123323902}]';

		$file = json_decode($file, true);

		$len = 200;

//		$file = [
//			[
//				'client_id'  => 1,
//				'company_id' => 38439812,
//			],
//			[
//				'client_id'  => 1,
//				'company_id' => 38440418,
//			],
//		];

		foreach ($file as $fileItem) {

			try {

				$clientId = $fileItem['client_id'];

				$companyId = $fileItem['company_id'];

				$db = PgActiveRecord::getDbByClientId($clientId);

				if (empty($db)) {

					self::info(('empty db continue:' . $clientId));
					continue;
				}

				$adminUserId = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();

				if (!$adminUserId) {

					self::info(("no admin user, continue:{$clientId}"));
					continue;
				}

				self::info(('[' . $clientId . ']origin company id:' . $companyId));

				User::setLoginUserById($adminUserId);

				$company = new \common\library\customer_v3\company\orm\Company($clientId, $companyId);

				$companyInfo = $company->getAttributes();

				if (!$company->isExist()) {

					self::info(('not exist, continue' . $clientId . ', ' . $companyId));

					continue;
				}

				unset($companyInfo['serial_id'], $companyInfo['company_id'], $companyInfo['main_customer_email'], $companyInfo['main_customer']);


				$sql = 'SELECT t5.customer_id, t5.email
							FROM (SELECT DISTINCT ON (t1.customer_id) t1.*, t2.opportunity_id, t3.order_id, t4.quotation_id
							      FROM tbl_customer t1
							               LEFT JOIN tbl_opportunity t2 ON t1.customer_id = ANY (t2.customer_id) AND t2.enable_flag = 1 AND t2.client_id = ' . $clientId . ' AND t2.company_id = ' . $companyId . '
							               LEFT JOIN tbl_order t3 ON t1.customer_id = t3.customer_id AND t3.enable_flag = 1 AND t3.client_id = ' . $clientId . ' AND t3.company_id = ' . $companyId . '
							               LEFT JOIN tbl_quotation t4 ON t1.customer_id = t4.customer_id AND t3.enable_flag = 1 AND t4.client_id = ' . $clientId . ' AND t4.company_id = ' . $companyId . '
							      WHERE t1.client_id = ' . $clientId . '
							        AND t1.company_id = ' . $companyId . '
							        AND t1.is_archive = 1) t5
							ORDER BY t5.main_customer_flag, t5.opportunity_id, t5.order_id, t5.quotation_id, t5.order_rank';

				$allCustomerList = $db->createCommand($sql)->queryAll();

				$chunkList = array_chunk($allCustomerList, $len);

				$chunkCount = count($chunkList);


				for ($i = ($chunkCount - 1); $i > 0; $i--) {


					$addCompanyInfo = $companyInfo;

					$addCompanyInfo['name'] .= ('-' . ($i + 1));

					$addCompanyInfo['short_name'] .= ('-' . ($i + 1));

					$addCompanyInfo['main_customer'] = $chunkList[$i][0]['customer_id'];

					$addCompanyInfo['main_customer_email'] = $chunkList[$i][0]['email'];


					$addCompany = new \common\library\customer_v3\company\orm\Company($clientId);

					$addCompany->setSkipDuplicateCheck(true);

					$addCompany->setSkipPrivilegeField(true);

					$addCompany->setCheckQuotaFlag(false);

					$addCompany->setDuplicateNameRename(true);

					$addCompany->_skipCheckAcceptChangeGroup = true;

					$addCompany->setAllowDuplicateName(true);

					$addCompany->setAllowDuplicateTel(true);


					$addCompany->setAttributes($addCompanyInfo);


//						公私海
					if (!empty($companyInfo['user_id'])) {

						$addCompany->addUser($companyInfo['user_id']);
					}

					$addCompany->setOperatorUserId($adminUserId);


					self::info(' company add pre,company_id:' . $addCompany->company_id);

					if (!$dryRun) {

						$addCompany->save();
					}


					self::info(' company add done,company_id:' . $addCompany->company_id);


					$customerIds = array_column($chunkList[$i], 'customer_id');


					$updateSql = 'UPDATE tbl_customer
									SET company_id = ' . $addCompany->company_id . '
									WHERE client_id = ' . $clientId . '
						             AND  customer_id IN (' . implode(',', $customerIds) . ')
						             AND is_archive = 1';

					if (!$dryRun) {

						$result = $db->createCommand($updateSql)->execute();
					} else {

						self::info('$updateSql: ' . $updateSql);
					}


					$updateSql2 = 'UPDATE tbl_customer
										SET main_customer_flag = 1 , order_rank = 0
										WHERE client_id = ' . $clientId . '
						                 AND   customer_id = ' . $customerIds[0].'
						                 AND is_archive = 1';

					if (!$dryRun) {

						$result = $db->createCommand($updateSql2)->execute();
					} else {

						self::info('$updateSql2: ' . $updateSql2);
					}


					self::info("{$companyId}, $addCompany->company_id, $clientId, " . json_encode($customerIds));

					$this->transferCompanyRelation($companyId, $addCompany->company_id, $clientId, $customerIds, $dryRun);

					\common\library\trail\Helper::resetCompanyLastTrailId($clientId, [$companyId, $addCompany->company_id]);

					\common\library\server\es_search\SearchQueueService::pushCompanyQueue($adminUserId, $clientId, [$addCompany->company_id], Constants::SEARCH_INDEX_TYPE_UPDATE);

					(new CustomerSync($clientId))->setFindCompanyId($addCompany->company_id)->sync();

					(new \common\library\swarm\SwarmService($clientId))->refreshByRefer([$addCompany->company_id], [], false);
				}

				\common\library\server\es_search\SearchQueueService::pushCompanyQueue($adminUserId, $clientId, [$companyId], Constants::SEARCH_INDEX_TYPE_UPDATE);

				(new CustomerSync($clientId))->setFindCompanyId($companyId)->sync();

				(new \common\library\swarm\SwarmService($clientId))->refreshByRefer([$companyId], [], false);

			} catch (Throwable $throwable) {

				self::info('message: ' . $throwable->getMessage());
				self::info('trace: ' . $throwable->getTraceAsString());
			}
		}

	}


	protected function transferCompanyRelation($fromId, $toId, $clientId, $customerId, $dryRun = 1) {

		// sns customer contact
		CustomerContactHelper::changeCompanyIdByCustomerId($clientId, $fromId, $toId, $customerId, $dryRun);


		//迁移阿里客户ID,买家ID、询盘ID、来源店铺
		\common\library\alibaba\trade\Helper::batchChangeCompanyIdByCustomerId($clientId, $fromId, $toId, $customerId, $dryRun);

//		复制一份
		// time line quotation order user schedule company file statistics
		//time line
		\common\library\trail\Helper::batchCopyTrail($fromId, $toId, $clientId, $customerId, $dryRun);

		// CIQ
		\CompanyBindCiq::copyBindInfo($fromId, $toId, $clientId, $dryRun);


		//companyFile
		\CompanyFileService::batchCopyByCompanyId($fromId, $toId, $clientId, $customerId, $dryRun);

		CustomerSyncHelper::batchCopyCompanyId($clientId, $fromId, $toId, $dryRun);
	}




	public function actionRefreshSearchFilter($key = '', $clientIds = '', $startClient = 0, $endClient = 0, $lastNumber = null) {

		$key = UserSetting::CUSTOMER_COMMON_SEARCH_FILTER;
//		$key = UserSetting::CUSTOMER_ADVANCED_SEARCH_FILTER;

		$fieldId = 'order_time';

		$fieldValue = [
			'id'         => 'order_time',
			'type'       => \Constants::TYPE_COMPANY,
			'field_type' => CustomFieldService::FIELD_TYPE_DATE,
			'operator'   => WorkflowConstant::FILTER_OPERATOR_EQUAL];


		self::info('clientIDs:' . $clientIds);

		if ($clientIds) {

			$clientIds = explode(',', $clientIds);

		} else {

			$clientIds = array_column($this->getClientList(0, false, null, null, $startClient, $endClient, $lastNumber), 'client_id');
		}

		foreach ($clientIds as $clientId) {

			self::info('执行clientID：' . $clientId);

			$db = ProjectActiveRecord::getDbByClientId($clientId);

			if (empty($db)) {

				self::info('empty db:'.$clientId);

				continue;
			}


			$sql = "SELECT user_id, value
				FROM tbl_user_setting
				WHERE client_id = {$clientId}
				  AND `key` = '{$key}'
				  AND enable_flag = 1";

			$result = $db->createCommand($sql)->queryAll();

			if (!$result) {

				self::info('empty user setting value:'.$clientId);

				continue;
			}

			foreach ($result as $item) {

				try {

					$value = array_column(json_decode($item['value'], true), null, 'id');

					if ($value[$fieldId] ?? []) {

						self::info("筛选项中已有{$fieldId}: {$clientId}, {$item['user_id']}");

						continue;
					}

					$value[$fieldId] = $fieldValue;


					$updateSql = "UPDATE tbl_user_setting
								SET value = :value
								WHERE user_id ='{$item['user_id']}'
								  AND `key` = '{$key}'";

					$result = $db->createCommand($updateSql)->execute([

						':value'   => json_encode(array_values($value)),
					]);

				} catch (Throwable $throwable) {

					self::info($clientId.$throwable->getMessage() . $throwable->getTraceAsString());

					continue;
				}

			}

			self::info('已执行完：' . $clientId);

		}

	}




	public function actionUpdateOrderRank($clientIds = '', $startClient = 0, $endClient = 0, $lastNumber = null) {

		self::info('clientIDs:' . $clientIds);

		if ($clientIds) {

			$clientIds = explode(',', $clientIds);

		}else{

			$clientIds = array_column($this->getClientList(0, false, null, null, $startClient, $endClient, $lastNumber), 'client_id');
		}


		foreach ($clientIds as $clientId) {

			self::info('执行clientID：' . $clientId);

			$db = PgActiveRecord::getDbByClientId($clientId);

			if (empty($db)) {

				self::info('empty db');

				continue;
			}

			try {

				$sql = 'UPDATE tbl_customer
					SET order_rank = 0
					WHERE client_id = ' . $clientId . '
					  AND is_archive = 1
					  AND main_customer_flag = 1';

				$result = $db->createCommand($sql)->execute();


			} catch (Throwable $throwable) {

				self::info($clientId.$throwable->getMessage() . $throwable->getTraceAsString());

				continue;
			}

			self::info('已执行完：' . $clientId);

		}
	}


	/**
	 * @param $userId
	 * @param $importId
	 * @param $column 公私海标识列，23：X，27：AB
	 * @return void
	 * @throws ProcessException
	 */
	public function actionFTImport($userId, $importId, $column = 23) {


		LogUtil::info('------START BEGIN IMPORT FROM FU TONG [TIME:' . date('Y-m-d H:i:s') . '  USERID:' . $userId . ' IMPORT ID:' . $importId. ' COLUMN:' . $column . ']');

		ini_set("memory_limit", "8096M");

		\User::setLoginUserById($userId);

		$user = \User::getLoginUser();

		$import = new Import($user->getClientId(), $importId);

		$importExecutor = new FTImportExecutor($import);

		$importExecutor->run();

		LogUtil::info('------END [TIME:' . date('Y-m-d H:i:s') . '  USERID:' . $userId . ' IMPORT ID:' . $importId . ']');
	}


	public function actionImport($user_id, $task_id){
        User::setLoginUserById($user_id);
        $import = new \common\library\customer\import\CsvImport($task_id, $user_id);
        $import->import();
    }

    public function actionImportContact($user_id, $task_id)
    {
        $beginTime = microtime(true);
        LogUtil::info('begin: user_id:'.$user_id.' task_id:'.$task_id);

        User::setLoginUserById($user_id);
        $import = new \common\library\contact\import\CsvImport($task_id, $user_id);
        $import->import();

        $endTime = microtime(true);

        LogUtil::info('finish: user_id:'.$user_id.' task_id:'.$task_id .' time:'.($endTime-$beginTime));
    }

    /**
     * 导入线索脚本
     * @param $user_id
     * @param $task_id
     * @return void
     * @throws ProcessException
     */
    public function actionImportLead($user_id, $task_id){
        User::setLoginUserById($user_id);
        $import = new \common\library\lead\import\CsvImport($task_id, $user_id);
        $import->import();
    }

    /** 移入公海刷数脚本
     * @param $clientId
     * @param int $grey
     * @param int $expFlag
     * @throws CDbException
     * @throws ProcessException
     */
    public function actionRefreshMoveToPublic($clientId, $grey = 1,$expFlag = 0)
    {
        if ($clientId) {
            $clientIds = [$clientId];
        } elseif ($grey) {
            $clientIds = \common\library\GreyEnvHelper::getGreyClientIds();
        } elseif ($expFlag) {
            $clientIds = \common\library\account\Client::getExpClientIds();
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        foreach ($clientIds as $clientId) {
            $this->actionMoveToPublic($clientId);
        }
    }



    /**
     * @deprecated  废弃, 迁至CustomerTask
     * @param $client_id
     * @throws CDbException
     * @throws ProcessException
     */
    public function actionMoveToPublic($client_id)
    {
        LogUtil::info("start_clientId:{$client_id}");
        $client = new \common\library\account\Client($client_id);
        if ($client->isNew() || $client->mysql_set_id == 0) {
            LogUtil::info("clientId:{$client_id},client not exist or mysql_set_id = 0");
            return;
        }

        ProjectActiveRecord::setConnection(ProjectActiveRecord::getDbByClientId($client_id));
        $pg = PgActiveRecord::getDbByClientId($client_id);
        $adminUserId = \common\library\privilege_v3\PrivilegeService::getInstance($client_id)->getAdminUserId();
        if ( !$adminUserId )
        {
            LogUtil::info("not set admin user, ignore");
            return;
        }

        $user = User::getUserObject($adminUserId);
        User::setLoginUserById($user->getUserId());
        try
        {
            $groupData = \common\library\group\Helper::getCustomerGroupMoveToPublicSetting($client_id);
        }catch (CDbException $e)
        {
            self::info('db: '.$e->errorInfo);
            throw $e;
        }

        $groupMap = array();
        $startTimeMap = array();
        $moveGroup = array();

        foreach ($groupData as $elem)
        {
            $groupMap[$elem['id']] = intval($elem['public_time']) * 86400;
            $startTimeMap[$elem['id']] = strtotime(date('Y-m-d',strtotime($elem['start_public_time'])));//确保是00:00:00

            if($elem['public_time']){
                $moveGroup[] = $elem['id'];
            }

        }

        if(empty($moveGroup)){
            LogUtil::info("client_id:$client_id not has need to moveGroup");
            return;
        }

        $now = strtotime(date('Y-m-d H:i:s'));

        $noticeArray = array();
        $willPublicArray = array();
        $removeArray = [];
        $removeUserIdList = [];
        $willMoveToPublicCompanyIds = [];

        $list = new \common\library\customer_v3\company\list\CompanyList($user->getUserId());
        $list->setSkipPrivilege(true);
        $list->setUserNum([1,2]);
        $list->setGroupId($moveGroup);
        $list->setFields(['company_id', 'array_to_json(user_id) as user_id', 'order_time', 'group_id','company_hash_id']);
        $result = $list->find();

        $dayX3 = 3;
        $dayX7 = 7;
        $moveToPublicCompanyIds = [];
        foreach ($result as $elem)
        {
            $elem['user_id'] = json_decode($elem['user_id'], true);
            $userList = [];
            $last_owner = 0;
            $oldUserIds = $elem['user_id'];

            $startTime = $startTimeMap[$elem['group_id']];
            $publicTime = $groupMap[$elem['group_id']];
            $sec = strtotime($elem['order_time']);
            $gap = $now - $sec;//未联系时间

            if($startTime > $now)
            {//未到脚本开始时间，仅做检查记录操作

                $the_rest = $gap > $publicTime ? 0 :$publicTime - $gap;//剩余时间

                if ($now + $the_rest < $startTime) {//如果在开始脚本之前就已经超过期限
                    foreach ($elem['user_id'] as $userId) {
                        if ($now + (86400 * $dayX3) > $startTime) {//在3天内到脚本执行时间
                            if (!isset($noticeArray[$userId]['customerCount']))
                                $noticeArray[$userId]['customerCount'] = 0;

                            $noticeArray[$userId]['customerCount']++;
                            $willPublicArray[$dayX3][$userId]['customers'][] = $elem['company_id'];
                        }

                        if ($now + (86400 * $dayX7) >= $startTime) {//在7天内到脚本执行时间
                            $willPublicArray[$dayX7][$userId]['customers'][] = $elem['company_id'];
                            $willMoveToPublicCompanyIds[$userId][] = [
                                'company_id' => $elem['company_id'],
                                'company_hash_id' => $elem['company_hash_id']
                            ];
                        }
                    }
                }
                $userList = $elem['user_id'];
            }
            else
            {
                if ($gap < $groupMap[$elem['group_id']])
                {
                    $userList = $elem['user_id'];

                    $dayX3Time = 86400 * $dayX3;
                    $dayX7Time = 86400 * $dayX7;
                    foreach ($elem['user_id'] as $userId) {
                        if ($gap > $groupMap[$elem['group_id']] - $dayX3Time) {
                            if (!isset($noticeArray[$userId]['customerCount']))
                                $noticeArray[$userId]['customerCount'] = 0;

                            $noticeArray[$userId]['customerCount']++;
                            $willPublicArray[$dayX3][$userId]['customers'][] = $elem['company_id'];
                        }

                        if ($gap > $groupMap[$elem['group_id']] - $dayX7Time) {
                            $willPublicArray[$dayX7][$userId]['customers'][] = $elem['company_id'];
                            $willMoveToPublicCompanyIds[$userId][] = [
                                'company_id' => $elem['company_id'],
                                'company_hash_id' => $elem['company_hash_id']
                            ];
                        }

                    }
                } else {
                    $removeArray[] = $elem['company_id'];
                    $removeUserIdList = $elem['user_id'];
                    $last_owner = array_pop($elem['user_id']);
                    LogUtil::info('clientId:'.$client_id.'delCompany:'.$elem['company_id'].' orderTime:'.$elem['order_time'].' publicDay:'.($groupMap[$elem['group_id']]/86400).' groupId:'.$elem['group_id'].' gap:'.$gap.' groupTime:'.$groupMap[$elem['group_id']]);
                }
            }

            if (empty($userList))
            {

                // 构建scope_user_ids
                $elem['user_id'] = [];
                $scopeUserIds = CompanyService::buildUpdateScopeUsers($client_id, $elem, returnAsPgStr: true) ?? 'scope_user_ids';

                $nowTime = date('Y-m-d H:i:s');
                $pg->createCommand("update tbl_company set user_id='{}', scope_user_ids=$scopeUserIds, last_owner=$last_owner,public_time= '{$nowTime}' where company_id=".$elem['company_id'])->execute();
                $pg->createCommand("update tbl_customer set user_id='{}' where client_id={$client_id} and is_archive=1 and company_id=".$elem['company_id'])->execute();

                \common\library\customer\public_record\Helper::batchSavePublicRecord($client_id,$removeUserIdList,$elem['company_id']);

                if ($last_owner)
                {
                    $companyHistory = new CompanyHistoryPg();
                    $companyHistory->client_id = $client_id;
                    $companyHistory->type = CompanyHistoryPg::TYPE_SYS_MOVE_TO_PUBLIC;
                    $companyHistory->company_id = $elem['company_id'];
                    $companyHistory->customer_id = 0;
                    $companyHistory->create_time = date('Y-m-d H:i:s');
                    $companyHistory->update_time = date('Y-m-d H:i:s');
                    $companyHistory->update_user = 0;
                    $diffData = $companyHistory::diffData(['user_id' => ['old' => $oldUserIds, 'new' => []]], 'modify');
                    $companyHistory->diff = json_encode($diffData);
                    $companyHistory->save();
                }

                $moveToPublicCompanyIds[] = $elem['company_id'];
            }
        }

        foreach ($noticeArray as $userId => $value) {
            try {
                $notification = new \common\library\notification\Notification($client_id, \common\library\notification\Constant::NOTIFICATION_TYPE_CUSTOMER_MOVE_PUBLIC);
                $notification->user_id = $userId;
                $notification->setSourceData($value['customerCount']);
                \common\library\notification\PushHelper::pushNotification($client_id, $userId, $notification);
                
                \common\library\dingding\Helper::pushNotification($notification);
            } catch (Exception $e) {
                LogUtil::info('clientId' . $client_id . 'send_msg_error:' . $e->getMessage());
            }
        }


        foreach($willPublicArray as $dayNumber =>$value)
        {
            foreach ($value as $userId => $item) {
                CustomerPublicRemind::updateDataByPK($userId, $client_id, date('Y-m-d'), $item['customers'], $dayNumber);
            }
        }

        if(!empty($willMoveToPublicCompanyIds)) {

            foreach ($willMoveToPublicCompanyIds as $user_id => $companyData) {
                $this->willMoveToPublicForAIMarketing($client_id,$user_id,$companyData);
            }
        }

        if (!empty($removeArray))
            LogUtil::info('clientId'.$client_id .' remove customer:'.implode(',',$removeArray));

//        ProjectActiveRecord::getDbByClientId($client_id)->setActive(false);
//        Yii::app()->db->setActive(false);
//        Yii::app()->account_base_db->setActive(false);
//        PgActiveRecord::getDbByClientId($client_id)->setActive(false);
//        UserInfo::model()->getDbConnection()->setActive(false);

        if (!empty($moveToPublicCompanyIds)) {
            (new CustomerSync($client_id))->setFindCompanyId($moveToPublicCompanyIds)->sync();

            SearchQueueService::pushCompanyQueue($adminUserId,$client_id,$moveToPublicCompanyIds,Constants::SEARCH_INDEX_TYPE_UPDATE);

            //补上自动移入公海的版本号
            $companyVersion = new \common\library\version\CompanyVersion($client_id, $moveToPublicCompanyIds);
            $companyVersion->setType(\common\library\version\Constant::COMPANY_MODULE_EDIT);
            $companyVersion->add();
        }
    }

    public function actionMoveToPublicDispatcher($client_id='')
    {
        $date = date('Ymd');
        $log = "/tmp/v4_move_to_public_{$date}.log";
        $queue = [];
        $queueKey =  ftok(__FILE__, 'c');
        $clientList = $this->getClientList($client_id);
        foreach ($clientList as $client)
        {
            $queue[] = json_encode([
                'command' => 'customer',
                'action' => 'moveToPublic',
                'log' => $log,
                'params' => [
                    'client_id' => $client['client_id']
                ]
            ]);
        }

        $script = new \common\library\server\script\ScriptProcess(10);
        $script->setQueue($queueKey,  $queue);
        $script->run();

        $result = $script->getResult();
        if( $result['result'] )
        {
            self::info('处理完成');
        }

        foreach ( $result['failList'] as $item )
            self::info("有脚本处理失败: {$item['pid']} - {$item['ret']}  - {$item['msg']}");

        self::info('共耗时:'.$result['runTime']);

//        $yiic = Yii::app()->params['yiic'];
//        $yiic = Yii::getPathOfAlias('application') . "/$yiic";
//        $path = "$yiic customer MoveToPublic --client_id=";
//        $date = date('Ymd');
//        $log = "/tmp/v4_move_to_public_{$date}.log";
//
//        $clientList = $this->getClientList($client_id);
//        foreach ($clientList as $client)
//        {
//            $exec = $path.$client->client_id;
//            $exec = "$exec >> {$log} 2>&1";
//            self::info($exec);
//            exec($exec);
//        }
    }

    public function actionDiscoveryBatchArchive($task_id, $user_id, $public_flag, $group_id = 0, $pool_id = 0)
    {
        LogUtil::info('action Begin');
        User::setLoginUserById($user_id);
        $user = User::getLoginUser();
        $tagName = '来源于小满发现';
        $pool_id = empty($pool_id) ? 0 : $pool_id;

//        $tag = (new \common\library\tag\CompanyTag($user_id))->loadByName($tagName);
//
//        if ($tag->isNew())
//        {
//            $tag->tag_type = \common\library\setting\library\tag\Tag::TYPE_COMPANY;
//            $tag->tag_name = $tagName;
//            $tag->save();
//        }
        $tag = (new \common\library\setting\library\tag\TagApi($user->getClientId(), \Constants::TYPE_COMPANY, $user_id))->findOrCreateByName($tagName);

        $import = new \common\library\customer\import\DiscoveryImport($task_id, $user->getUserId());
        $import->setPoolId($pool_id);
        $import->setGroupId($group_id);
        $import->import($public_flag, false, $pool_id, [$tag->tag_id]);
    }


    public function actionWillMoveToPublic($client_id, $group_id)
    {
        $dayX3 = 3;
        $dayX7 = 7;

        $client = \common\library\account\Client::getClient($client_id);
        if (!$client || $client->mysql_set_id == 0) {
            LogUtil::info("clientId:{$client_id},client not exist or mysql_set_id = 0");
            return;
        }

        $adminUserId = \common\library\privilege_v3\PrivilegeService::getInstance($client->client_id)->getAdminUserId();
        if( $adminUserId )
        {
            $user = User::getUserObject($adminUserId, $client->client_id);
        }else{
            $user = User::getUserObjectByEmail($client->master_account, $client->client_id);
        }


        ProjectActiveRecord::setConnection(ProjectActiveRecord::getDbByClientId($client_id));

//        $group = new \common\library\group\Group($client_id,Constants::TYPE_COMPANY,$group_id);
//        if($group_id>0 && $group->isNew() ){
//            throw new ProcessException('分组id：'.$group_id.' 不存在');
//        }
//        $setting = $group->batchGetExternalKey(['public_time','start_public_time']);
//        if($setting['public_time'] ==0)
//            return;

        $api = new \common\library\setting\library\group\GroupApi($client_id, Constants::TYPE_COMPANY);
        if ($group_id > 0 && !$api->exist($group_id)) {
            throw new ProcessException('分组id：' . $group_id . ' 不存在');
        }
        $setting = $api->getExternalAPI()->kvMap($group_id, [\common\library\setting\library\group\Group::External_KEY_PUBLIC_TIME, \common\library\setting\library\group\Group::External_KEY_START_PUBLIC_TIME])[$group_id] ?? [];
        if (empty($setting['public_time'])) {
            return;
        }

        $now = strtotime(date('Y-m-d'));
        $publicTime = $setting['public_time'] * 86400;
        $startTime =  strtotime(date('Y-m-d',strtotime($setting['start_public_time'])));

        $noticeArray = array();
        $willPublicArray = array();

        $list = new \common\library\customer_v3\company\list\CompanyList($user->getUserId());
        $list->setSkipPrivilege(true);
        $list->setUserNum([1,2]);
        $list->setGroupId($group_id);//该分组和他的子分组
        $list->setFields(['company_id', 'array_to_json(user_id) AS user_id', 'order_time']);
        $result = $list->find();

        foreach ($result as $elem)
        {
            $elem['user_id'] = json_decode($elem['user_id'], true);
            $sec = strtotime($elem['order_time']);
            $gap = $now - $sec;//未联系时间

            if ($startTime > $now) {//未到脚本开始时间
                $the_rest = $gap > $publicTime ? 0 : $publicTime - $gap;//剩余时间
                if ($now + $the_rest < $startTime) {//如果在开始脚本之前就已经超过期限
                    foreach ($elem['user_id'] as $userId) {
                        if ($now + (86400 * $dayX3) >= $startTime) {//在3天内到脚本执行时间
                            if (!isset($noticeArray[$userId]['customerCount']))
                                $noticeArray[$userId]['customerCount'] = 0;

                            $noticeArray[$userId]['customerCount']++;
                            $willPublicArray[$dayX3][$userId]['customers'][] = $elem['company_id'];
                        }

                        if ($now + (86400 * $dayX7) >= $startTime) {//在7天内到脚本执行时间
                            $willPublicArray[$dayX7][$userId]['customers'][] = $elem['company_id'];
                        }
                    }
                }
                continue;
            }

            if ($gap < $publicTime) {
                $dayX3Time = 86400 * $dayX3;
                $dayX7Time = 86400 * $dayX7;
                foreach ($elem['user_id'] as $userId) {
                    if ($gap > $publicTime - $dayX3Time) {
                        if (!isset($noticeArray[$userId]['customerCount']))
                            $noticeArray[$userId]['customerCount'] = 0;

                        $noticeArray[$userId]['customerCount']++;
                        $willPublicArray[$dayX3][$userId]['customers'][] = $elem['company_id'];
                    }

                    if ($gap > $publicTime - $dayX7Time) {
                        $willPublicArray[$dayX7][$userId]['customers'][] = $elem['company_id'];
                    }
                }
            }

        }

        foreach ($noticeArray as $userId => $value)
        {
            $notification = new \common\library\notification\Notification($client_id, \common\library\notification\Constant::NOTIFICATION_TYPE_CUSTOMER_MOVE_PUBLIC);
            $notification->user_id = $userId;
            $notification->setSourceData($value['customerCount']);
            \common\library\notification\PushHelper::pushNotification($client_id, $userId, $notification);
            
            \common\library\dingding\Helper::pushNotification($notification);
        }

        $dayX0Map = [];
        foreach($willPublicArray as $dayNumber =>$value)
        {
            foreach ($value as $userId => $item) {
                CustomerPublicRemind::updateDataByPK($userId, $client_id, date('Y-m-d'), $item['customers'], $dayNumber);
                $dayX0Map[$userId][$dayNumber] = $item['customers'];
            }
        }
//
//        foreach ($dayX0Map as $userId => $dayNumCustomerMap) {
//            CustomerPublicRemind::updateDataByPK($userId, $client_id, date('Y-m-d'), $dayNumCustomerMap, 0);
//        }
    }

    public function actionRemoveDuplicate(){
        $userId = 31005778;
        $userId = 0;
        User::setLoginUserById($userId);
        $user = User::getLoginUser();

        $clientId = $user->getClientId();
        $pgDb = CompanyModel::getDbByClientId($clientId);
        $mysqlDb = UserFeed::getDbByClientId($clientId);

        $map = [];
        $sql = "SELECT name,company_id FROM tbl_company WHERE client_id=$clientId AND user_id @> ARRAY[$userId]::bigint[]";
        $list = $pgDb->createCommand($sql)->queryAll(true);
        foreach ($list as $item) {
            $name = $item['name'];
            $map[$name][] = $item['company_id'];
        }

        $esDeleteCompanyIds = [];
        foreach ($map as $item) {
            if(count($item) <= 1)
                continue;

            $saveCompany = min($item);//默认最小用最小id
            $hasTrailMap = [];
            foreach ($item as $companyId) {
                //暂不考虑company下邮箱不一样
                $trailList = new \common\library\trail\CompanyDynamicList($clientId);
                $trailList->setOperatorUserId($userId);
                $trailList->setCompanyId($companyId);
                if($trailList->count() > 0){
                    $hasTrailMap[$companyId] = $companyId;
                }
            }
            //用有时间线的companyId,有多个则用最小的companyId
            $saveCompany = !empty($hasTrailMap) ? min($hasTrailMap) : $saveCompany;
            $deleteMap = $saveCompany ?  array_diff($item,[$saveCompany]) : $item;

            foreach ($deleteMap as $companyId) {
                $esDeleteCompanyIds[] = $companyId;

                $deleteCompany = "DELETE FROM tbl_company WHERE client_id=$clientId AND company_id=$companyId";
                $row = $pgDb->createCommand($deleteCompany)->execute();

                //customer
                $deleteCustomerSql = "DELETE FROM tbl_customer WHERE client_id=$clientId AND company_id=$companyId";
                $row = $pgDb->createCommand($deleteCustomerSql)->execute();

                //trail
                $trailList = new \common\library\trail\DynamicTrailBatchOperator($userId);
                $trailList->setParams(['client_id' => $clientId, 'company_id' => $companyId]);
                $trailList->deleteMany();

                //feed
                $deleteFeedSql = "DELETE FROM tbl_user_feed where client_id=$clientId AND node_type >=600 AND node_type <= 700 AND refer_id = $companyId";
                $row = $mysqlDb->createCommand($deleteFeedSql)->execute();

                //schedule
                $deleteScheduleSql = "DELETE FROM tbl_user_schedule WHERE client_id={$clientId} AND company_id={$companyId}";
                $row = $pgDb->createCommand($deleteScheduleSql)->execute();

                //file
                $deleteCompanyFileSql = "UPDATE tbl_company_file SET enable_flag=0 WHERE client_id={$clientId} AND company_id={$companyId}";
                $row = $pgDb->createCommand($deleteCompanyFileSql)->execute();
            }
        }

        if (!empty($esDeleteCompanyIds))
        {
            $model = \common\models\search\CompanySearch::model();
            $indexName = $model->index();
            $typeName  = $model->type();

            $bulk = [];
            foreach ($esDeleteCompanyIds as $companyId) {
                $bulk[] = [
                    'delete' => [
                        '_index'   => $indexName,
                        '_type'    => $typeName,
                        '_id'      => $companyId,
                        '_routing' => $clientId,
                    ]
                ];
            }
            $params = [
                'index' =>$indexName,
                'type' => $typeName,
                'body' => $bulk
            ];

            $model->getDbConnection()->bulk($params);

            $model->getDbConnection()->indices()->refresh(['index'=>$indexName]);
        }
    }

    public function actionCompanyCount(){
        $companyCount = 0;
        Speed::log('analyse start-----------');
//        $clientList = $this->getClientList(0, true, 1, 1);
        $condition = "select client_id from tbl_client where mysql_set_id = 5";
        $clientList = \Yii::app()->db->createCommand($condition)->queryAll(true);

        $num = 1;
        $clientCount = count($clientList);
        foreach ($clientList as $client) {
            $clientId = $client['client_id'];

            echo "Client $clientId: $num/$clientCount";
            echo "\n";
            LogUtil::info("Client $clientId: $num/$clientCount");
            $num++;

            $db = \PgActiveRecord::getDbByClientId($clientId);
            if(!$db){
                continue;
            }
            $sql = "select count(1) from tbl_company   where client_id={$clientId} and is_archive=1 and user_id != '{}'";
            $count = $db->createCommand($sql)->queryScalar();
            $companyCount += $count;

        }
        var_dump($companyCount);
    }

    public function actionCompanyAnalyseDispatcher(){
        $condition = 'select DISTINCT (mysql_set_id) from tbl_client where mysql_set_id != 0';
        $dbList = \Yii::app()->db->createCommand($condition)->queryAll(true);
        $dbIds = array_column($dbList, 'mysql_set_id');

        $num = 1;
        $countDbSet = count($dbIds);
        foreach ($dbIds as $mysqlSetId){
            echo "mysqlSetId: $mysqlSetId   $num / $countDbSet \n";
            $num++;
            \common\library\CommandRunner::run(
                'customer',
                'companyAnalyse',
                [
                    'mysqlSetId' => $mysqlSetId,
                ],
                '/dev/null',
                0
            );
        }
    }
    
    /**
     * @depracated
     */
    public function actionCompanyAnalyse($mysqlSetId)
    {
        $companyCount = 0;
        Speed::log('analyse start-----------');
        $condition = "select client_id from tbl_client where mysql_set_id = $mysqlSetId";
        $clientList = \Yii::app()->db->createCommand($condition)->queryAll(true);
//        $clientList = $this->getClientList(3, true, 1, 1);

        $todayTime = time();
        $recentMonth = strtotime('-1month', $todayTime);
        $today = date('Y-m-d', $todayTime);
        $recentMonthDay = date('Y-m-d', $recentMonth);
        $date = date('Ymd', $todayTime);
        $createTime = date('Y-m-d H:i:s');

        $mysqlDb = ProjectActiveRecord::getDbByDbSetId($mysqlSetId);

        $num = 1;
        $clientCount = count($clientList);
        foreach ($clientList as $client) {
            $clientId = $client['client_id'];
            $sqlList = [];

            echo "Client $clientId: $num/$clientCount";
            echo "\n";
            LogUtil::info("Client $clientId: $num/$clientCount");
            $num++;

            $pgDb = CompanyModel::getDbByClientId($clientId);

            //discovery Origin
            $discoveryOriginId = \common\library\setting\library\origin\Origin::SYS_ORIGIN_ID_DISCOVERY;

            //orderMap
            $orderMap = [];

            $orderSql = "SELECT company_id, user_id, count(1) as total FROM tbl_order WHERE client_id=:client_id "
                      . "AND account_date BETWEEN :start AND :stop GROUP BY company_id, user_id";
            $params = [
                ':client_id' => $clientId,
                ':start' => $recentMonthDay . ' 00:00:00',
                ':stop' => $today . ' 23:59:59'
            ];
            $orderData = $pgDb->createCommand($orderSql)->queryAll(true, $params);
            foreach ($orderData as $item) {
                $userId = $item['user_id'];
                $companyId = $item['company_id'];
                $orderMap[$userId][$companyId] = $item['count'];
            }

            $trailMap = [];
            $trailListSql = "SELECT company_id, create_user, count(1) as total FROM tbl_dynamic_trail "
                          . "WHERE client_id=:client_id AND create_time BETWEEN :start_time AND :end_time "
                          . "AND enable_flag=:enable_flag GROUP BY company_id, create_user";
            $params = [
                ':client_id' => $clientId,
                ':start_time' => $recentMonthDay . ' 00:00:00',
                ':end_time' => $today . ' 23:59:59',
                ':enable_flag' => 1
            ];
            $trailData = $pgDb->createCommand($trailListSql)->queryAll(true, $params);
            foreach ($trailData as $item) {
                $userId = $item['create_user'];
                $companyId = $item['company_id'];
                $trailMap[$userId][$companyId] = $item['total'];
            }

            $companyListSql = "select company_id, origin, user_id from tbl_company  where client_id={$clientId} and is_archive=1 and user_id != '{}'";
            $companyList = $pgDb->createCommand($companyListSql)->query();

//            Speed::log('emailListStart');
            $customerListSql = "select email,company_id from tbl_customer where client_id={$clientId} and is_archive= 1";
            $customerList = $pgDb->createCommand($customerListSql)->query();
            $customerMap = [];
            foreach ($customerList as $customer) {
                $companyId = $customer['company_id'];
                $customerMap[$companyId][] = $customer['email'];
            }
//             Speed::log('emailListEnd');

            $dataMap = [];
            foreach ($companyList as $companyItem) {
                $companyCount ++;
                $companyId = $companyItem['company_id'];

                $emailList = $customerMap[$companyId] ?? [];
                if(empty($emailList)){
                    continue;
                }

                $isDiscovery = $companyItem['origin'] && $companyItem['origin'] == $discoveryOriginId ? 1 : 0;

                $userList = \common\library\util\PgsqlUtil::trimArray($companyItem['user_id']);
                foreach ($userList as $userId) {
                    $dataMap[$userId][] = [
                        'company_id' => $companyId,
                        'email_list' => $emailList,
                        'is_discovery' => $isDiscovery,
                        'has_order' => ($orderMap[$userId][$companyId] ?? 0) ? 1 : 0,
                        'trail_count' => $trailMap[$userId][$companyId] ?? 0
                    ];
                }
            }

            foreach ($dataMap as $userId => $data) {
                $data = json_encode($data);
                //array_chunk?
                $sqlList[] = "insert into tbl_company_analyse (client_id, user_id, date, data, create_time) VALUES
($clientId,$userId, $date,'$data','$createTime')
ON DUPLICATE KEY UPDATE data='$data'";
            }
            if(!empty($sqlList)){
                try {
                    $sqlList = implode(';', $sqlList);
                    $mysqlDb->createCommand($sqlList)->execute();
                } catch (Exception $e) {
                    LogUtil::info($e->getMessage());
                }
            }
            Speed::log('analyse end-----------');
        }
        var_dump($companyCount);
    }

    public function actionRemoveMKTrailDispatcher($start = 0, $end = 0){
        $clientList = $this->getClientList(0, false, null, null, $start, $end);
        $num = 1;
        $count = count($clientList);
        foreach ($clientList as $client)
        {
            echo "clientId:{$client['client_id']} $num / $count \n";
            $num++;
            $this->actionDelMkTrail($client['client_id']);
        }
    }

    public function actionDelMkTrail($clientId){
        $userId = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();
        $operator = new \common\library\trail\DynamicTrailBatchOperator($userId);
        $operator->setParams([
            'client_id' => $clientId,
            'node_type' => \common\library\trail\TrailConstants::TYPE_MARKETING_EDM_SEND
        ]);
        $operator->deleteMany();
    }

    public function actionCleanPublic(){
        $client_id = 6971;
        $client_id = 0;
        $adminUserId = \common\library\privilege_v3\PrivilegeService::getInstance($client_id)->getAdminUserId();

        $pgdb = CompanyModel::getDbByClientId($client_id);
        $sql = "select company_id from tbl_company where client_id=$client_id AND is_archive = 1 AND user_id='{}'";
        $companyIds = $pgdb->createCommand($sql)->queryColumn();

        $delCompanySql = "update tbl_company set is_archive=0 where client_id=$client_id AND is_archive = 1 AND user_id='{}'";
        $pgdb->createCommand($delCompanySql)->execute();

        $companyIdsSql = implode(',', $companyIds);
        $sql = "update tbl_customer set is_archive = 0, user_id='{}', company_id=0 where client_id=$client_id and company_id in ($companyIdsSql) and is_archive=1";
        $pgdb->createCommand($sql)->execute();

        //入队列，更新搜索索引
        \common\library\server\es_search\SearchQueueService::pushCompanyQueue($adminUserId, $client_id, $companyIds, \Constants::SEARCH_INDEX_TYPE_DELETE);
    }

    /**
     * @param $clientId
     * 同步客户userId到customer
     */
    public function actionSyncUserByClient($clientId){
        $db = PgActiveRecord::getDbByClientId($clientId);

        $sql = "SELECT company_id FROM tbl_company WHERE client_id = $clientId ORDER BY company_id DESC limit 1";
        $maxCompanyId = $db->createCommand($sql)->queryScalar();
        $currentId = 0;
        self::info("begin sync user client_id:$clientId maxId:{$maxCompanyId} currId: {$currentId}");
        while ($currentId < $maxCompanyId){
            $sourceSql = "select b.customer_id, a.user_id, a.company_id from tbl_company as a inner join tbl_customer as b
on a.client_id=b.client_id and a.company_id = b.company_id and b.is_archive=1
where $currentId < a.company_id and a.client_id=$clientId order by a.company_id asc limit 2000";
            $result = $db->createCommand($sourceSql)->queryAll(true);
            if(empty($result)){
                break;
            }
            $updateSql = [];
            $customerIds = [];
            foreach ($result as $item) {
                $currentId = $item['company_id'];

                $userIds = $item['user_id'];
                $customerId = $item['customer_id'];
                $updateSql[] = "update tbl_customer set user_id='{$userIds}' where customer_id=$customerId";

                $customerIds[] = $customerId;
            }
            $sql = implode(';', $updateSql);
            $db->createCommand($sql)->execute();

            //同步邮箱身份
            (new CustomerSync($clientId))->setFindCustomerId($customerIds)->sync();

            self::info("currId: {$currentId} update count:".count($updateSql));
        }
    }

    /**
     * 该脚本用于【客户版本号模块】初始化
     *
     * @param int $client_from
     * @param int $client_to
     *
     * @throws CDbException
     * @throws ProcessException
     */
    public function actionInitCompanyVersion( int $client_to,int $client_from = 0)
    {
        //遍历指定client
        //遍历client下所有company，会其创建一条company_version 记录
        $client_sql = $client_to ? " and `client_id` <= {$client_to}" : '';
        $clients = Client::model()->findAllBySql("select `client_id` from `tbl_client` where `client_id` >= {$client_from}{$client_sql}");
        $client_ids = array_column($clients, 'client_id');
        foreach ($client_ids as $client_id) {
            try {
                $mysql_db = ProjectActiveRecord::getDbByClientId($client_id);
                $pg_db = PgActiveRecord::getDbByClientId($client_id);
            }catch (Exception $e){
                echo "异常：client({$client_id})连接数据库存在异常" . PHP_EOL;
                echo $e->getMessage() . PHP_EOL;
                echo json_encode($e->getTrace()) . PHP_EOL;
                continue;
            }
            $mysql_db->getCommandBuilder()->createSqlCommand("delete from `tbl_company_version` where client_id={$client_id};")->execute();
            \RedisService::sf()->del(\common\library\version\Constant::COMPANY_VERSION_PREFIX . $client_id);
            $company_total = $pg_db->getCommandBuilder()->createSqlCommand("select count(*) from tbl_company where client_id={$client_id};")->queryScalar();
            echo "共查到client({$client_id})下有{$company_total}个客户" . PHP_EOL;
            $batch_count = 5000;
            for ($i = 0; $i < $company_total; $i += $batch_count) {
                $data = [];
                $criteria = new CDbCriteria([
                    'limit'     => $batch_count,
                    'offset'    => $i,
                    'condition' => "client_id={$client_id}",
                    'order'=>'company_id asc'
                ]);
                $companies = $pg_db->getCommandBuilder()
                    ->createFindCommand('tbl_company', $criteria)
                    ->queryAll();
                foreach ($companies as $company) {
                    $company_version = new \common\library\version\CompanyVersion($client_id, $company['company_id']);
                    $version = [];
                    $version['client_id'] = $client_id;
                    $version['user_id'] = \common\library\util\PgsqlUtil::trimArray($company['user_id'])[0] ?? 0;
                    $version['version'] = $company_version->getVersion();
                    $version['company_id'] = $company['company_id'];
                    $version['type'] = \common\library\version\Constant::COMPANY_MODULE_ADD;
                    $version['create_time'] = date('Y-m-d H:i:s');
                    $data[] = $version;
                }
                $result = $mysql_db->getCommandBuilder()->createMultipleInsertCommand('tbl_company_version', $data)->execute();
                echo "共插入{$result}条版本号记录".PHP_EOL;
            }
        }
    }

    public function actionMovePoolCustomer($clientId, $checkOnly = true)
    {
        $db = PgActiveRecord::getDbByClientId($clientId);
        //检查多个

        $multiPoolCompanyList = $db->getCommandBuilder()->createSqlCommand("select company_id from tbl_company_pool_archive where client_id = {$clientId} and pool_id > 0 group by company_id having count(*) > 1")->queryAll();
        $multiPoolCompanyIds = array_column($multiPoolCompanyList, 'company_id');
        echo "从属多个公海客户数量为: " . count($multiPoolCompanyIds) . "\n";

        if (!$checkOnly) {
            $maxId = $db->getCommandBuilder()->createSqlCommand("select company_id from tbl_company_pool_archive where client_id = {$clientId} and pool_id > 0 and is_archive = 1 order by company_id desc limit 1")->queryScalar();
            $currentId = 0;
            $count = 1000;
            $totalCount = 0;
            while ($currentId < $maxId) {
                $sql = "select company_id, pool_id from tbl_company_pool_archive where client_id = {$clientId} and company_id > {$currentId} and is_archive = 1 order by company_id asc limit {$count} ";
                $companyPoolList = $db->getCommandBuilder()->createSqlCommand($sql)->queryAll();
                if (!count($companyPoolList)) {
                    break;
                }
                $companyPoolMap = array_column($companyPoolList, 'pool_id', 'company_id');
                foreach ($companyPoolMap as $companyId => $poolId) {
                    $db->getCommandBuilder()->createSqlCommand("update tbl_company set pool_id={$poolId} where company_id = {$companyId}")->execute();
                    echo "set company: {$companyId} pool id as {$poolId} \n";
                    $totalCount += 1;
                    $currentId = $companyId;
                }
            }
            echo "共处理 $totalCount 客户";

            //删除权限
            // 权限V3升级，下面的权限更新已经没法使用
//            $userId = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();
//            PrivilegePermission::getInstance($userId)->removeClientPrivilege($clientId, PrivilegeConstants::PRIVILEGE_CUSTOMER_POOL);
//            PrivilegePermission::getInstance($userId)->assignClientPrivilege($clientId, PrivilegeConstants::PRIVILEGE_CRM_CUSTOMER_POOL_SWITCH);
//            PrivilegePermission::getInstance($userId)->assignClientPrivilege($clientId, PrivilegeConstants::PRIVILEGE_CRM_CUSTOMER_POOL_MODULES);
            $db = \PgActiveRecord::getDbByUserId($userId);
            echo "update tbl_custom_field set enable_flag=1 where client_id={$clientId} and type=4 and id='pool_id'";
            $db->createCommand("update tbl_custom_field set enable_flag=1 where client_id={$clientId} and type=4 and id='pool_id'")->execute();
        }
    }

    public function actionFixCustomerSerialIdRedis($clientId, $dryRun = 1)
    {
        $pg = PgActiveRecord::getDbByClientId($clientId);
        $duplicateSerialSql = "
              select substring(serial_id FROM '[0-9]+')::bigint from tbl_company where client_id={$clientId} and serial_id ~ '^[0-9]' order by substring(serial_id FROM '[0-9]+')::bigint desc limit 1;
            ";
        $maxSerialId = $pg->getCommandBuilder()->createSqlCommand($duplicateSerialSql)->queryScalar();
        echo "client: {$clientId} 最大序列为 $maxSerialId \n";
        $redis = Yii::app()->redis;
        $key = IDProducer::getPrefix() . ":$clientId:" . \IDProducer::TYPE_COMPANY;
        $currentSerialId = $redis->executeCommand("get", array($key));
        echo "client: {$clientId} 当前序列为 $currentSerialId \n";
        if (!$dryRun) {
            if ($currentSerialId < $maxSerialId) {
                $redis->executeCommand('set', [$key, $maxSerialId]);
                echo "client: {$clientId} 重置为 $maxSerialId \n";
            }
        }
    }



    public function actionNewFixCustomerSerialId($clientId, $dryRun = 1)
    {
        $clientIds = explode(',', $clientId);

        foreach ($clientIds as $clientId) {
            echo "-开始处理 client_id: {$clientId} \n";
            $pg = PgActiveRecord::getDbByClientId($clientId);
            $duplicateSerialSql = "
              select company_id, serial_id from tbl_company where client_id = {$clientId} and is_archive = 1 and serial_id in (
                    select serial_id from tbl_company  where client_id={$clientId} and is_archive = 1 group by client_id, serial_id having count(*) > 1
                  ) order by order_time desc;
            ";
            $companyList = $pg->getCommandBuilder()->createSqlCommand($duplicateSerialSql)->queryAll();
            if ($companyCount = count($companyList)) {
                echo "处理中 client_id: {$clientId}, 预计数量 {$companyCount} \n";
                $batchUpdate = [];
                $serialIds = [];
                $updateMap = [];
                foreach ($companyList as $company) {
                    if (array_key_exists($company['serial_id'], $serialIds)) {
                        $companyId = $company['company_id'];
                        $serialIds[$company['serial_id']]++;
                        $serialId = $company['serial_id'] . '_' . $serialIds[$company['serial_id']];
                        $updateMap[$companyId] = $serialId;
                        $batchUpdate[] = "UPDATE tbl_company SET serial_id='{$serialId}' where company_id={$companyId}";
                    } else {
                        $updateMap[$company['company_id']] = $company['serial_id'];
                        $serialIds[$company['serial_id']] = 0;
                    }
                }
                if (!$dryRun) {
                    $succeedCount = $pg->getCommandBuilder()->createSqlCommand(implode(';', $batchUpdate))->execute();
                    echo "*完成 client_id: {$clientId}, 更改数量 {$succeedCount} \n";
                    \common\library\server\es_search\SearchQueueService::pushCompanyQueue(0, $clientId,
                        array_column($companyList, 'company_id'), \Constants::SEARCH_INDEX_TYPE_UPDATE);
                }
                echo "client_id: $clientId \n";
                echo json_encode($updateMap, JSON_PRETTY_PRINT) . "\n";
                echo json_encode($serialIds, JSON_PRETTY_PRINT) . "\n";
                \LogUtil::info('client_id:' . $clientId);
                \LogUtil::info(json_encode($updateMap, JSON_PRETTY_PRINT));
                \LogUtil::info(json_encode($serialIds, JSON_PRETTY_PRINT));
            } else {
                echo "^跳过 client_id: {$clientId} \n";
            }
        }
    }

    public function actionFixOpportunitySerialIdRedis($clientId, $dryRun = 1)
    {
        $idType = \IDProducer::TYPE_OPPORTUNITY;
        $tableName = 'tbl_opportunity';
        $pg = PgActiveRecord::getDbByClientId($clientId);
        $duplicateSerialSql = "
              select substring(serial_id FROM '[0-9]+')::bigint from {$tableName} where client_id={$clientId} and serial_id ~ '^[0-9]' order by substring(serial_id FROM '[0-9]+')::bigint desc limit 1;
            ";
        $maxSerialId = $pg->getCommandBuilder()->createSqlCommand($duplicateSerialSql)->queryScalar();
        echo "client: {$clientId} 最大序列为 $maxSerialId \n";
        $redis = Yii::app()->redis;
        $key = IDProducer::getPrefix() . ":$clientId:" . $idType;
        $currentSerialId = $redis->executeCommand("get", array($key));
        echo "client: {$clientId} 当前序列为 $currentSerialId \n";
        if (!$dryRun) {
            if ($currentSerialId < $maxSerialId) {
                $redis->executeCommand('set', [$key, $maxSerialId]);
                echo "client: {$clientId} 重置为 $maxSerialId \n";
            }
        }
    }

    public function actionFixOpportunitySerialId($clientId = 0, $dryRun = 1)
    {
        $idType = \IDProducer::TYPE_OPPORTUNITY;
        $tableName = 'tbl_opportunity';
        $primaryKey = 'opportunity_id';

        $clientList = $this->getClientList($clientId);
        foreach ($clientList as $client) {
            $clientId = $client->client_id;
            echo "-开始处理 client_id: {$clientId} \n";
            $this->actionFixOpportunitySerialIdRedis($clientId, $dryRun);
            $pg = PgActiveRecord::getDbByClientId($clientId);
            $duplicateSerialSql = "
              select {$primaryKey}, serial_id from {$tableName} where client_id = {$clientId} and serial_id in (
                    select serial_id from {$tableName}  where client_id={$clientId} and serial_id ~ '^[0-9]' group by client_id, serial_id having count(*) > 1
                  ) order by {$primaryKey} asc;
            ";
            $opportunityList = $pg->getCommandBuilder()->createSqlCommand($duplicateSerialSql)->queryAll();
            if ($companyCount = count($opportunityList)) {
                echo "处理中 client_id: {$clientId}, 预计数量 {$companyCount} \n";
                $batchUpdate = [];
                $serialIds = [];
                foreach ($opportunityList as $opportunity) {
                    if (in_array($opportunity['serial_id'], $serialIds)) {
                        $opportunityId = $opportunity[$primaryKey];
                        $serialId = \IDProducer::getID($clientId, $idType);
                        $batchUpdate[] = "UPDATE {$tableName} SET serial_id={$serialId} where {$primaryKey}={$opportunityId}";
                    } else {
                        $serialIds[] = $opportunity['serial_id'];
                    }
                }
                if (!$dryRun) {
                    $succeedCount = $pg->getCommandBuilder()->createSqlCommand(implode(';', $batchUpdate))->execute();
                    echo "*完成 client_id: {$clientId}, 更改数量 {$succeedCount} \n";
                }
                \common\library\server\es_search\SearchQueueService::pushCompanyQueue(0, $clientId,
                    array_column($opportunityList, 'company_id'), \Constants::SEARCH_INDEX_TYPE_UPDATE);
            } else {
                echo "^跳过 client_id: {$clientId} \n";
            }
        }
    }

    public function actionFixLeadSerialIdRedis($clientId, $dryRun = 1)
    {
        $idType = \IDProducer::TYPE_LEAD;
        $tableName = 'tbl_lead';
        $pg = PgActiveRecord::getDbByClientId($clientId);
        $duplicateSerialSql = "
              select substring(serial_id FROM '[0-9]+')::bigint from {$tableName} where client_id={$clientId} order by substring(serial_id FROM '[0-9]+')::bigint desc limit 1;
            ";
        $maxSerialId = $pg->getCommandBuilder()->createSqlCommand($duplicateSerialSql)->queryScalar();
        echo "client: {$clientId} 最大序列为 $maxSerialId \n";
        $redis = Yii::app()->redis;
        $key = IDProducer::getPrefix() . ":$clientId:" . $idType;
        $currentSerialId = $redis->executeCommand("get", array($key));
        echo "client: {$clientId} 当前序列为 $currentSerialId \n";
        if (!$dryRun) {
            if ($currentSerialId < $maxSerialId) {
                $redis->executeCommand('set', [$key, $maxSerialId]);
                echo "client: {$clientId} 重置为 $maxSerialId \n";
            }
        }
    }

    public function actionFixLeadSerialId($clientId = 0, $dryRun = 1)
    {
        $idType = \IDProducer::TYPE_LEAD;
        $tableName = 'tbl_lead';
        $primaryKey = 'lead_id';

        $clientList = $this->getClientList($clientId);
        foreach ($clientList as $client) {
            $clientId = $client->client_id;
            echo "-开始处理 client_id: {$clientId} \n";
            $this->actionFixLeadSerialIdRedis($clientId, $dryRun);
            $pg = PgActiveRecord::getDbByClientId($clientId);
            $duplicateSerialSql = "
              select {$primaryKey}, serial_id from {$tableName} where client_id = {$clientId} and serial_id in (
                    select serial_id from {$tableName}  where client_id={$clientId} group by client_id, serial_id having count(*) > 1
                  ) order by {$primaryKey} asc;
            ";
            $opportunityList = $pg->getCommandBuilder()->createSqlCommand($duplicateSerialSql)->queryAll();
            if ($companyCount = count($opportunityList)) {
                echo "处理中 client_id: {$clientId}, 预计数量 {$companyCount} \n";
                $batchUpdate = [];
                $serialIds = [];
                foreach ($opportunityList as $opportunity) {
                    if (in_array($opportunity['serial_id'], $serialIds)) {
                        $opportunityId = $opportunity[$primaryKey];
                        $serialId = \IDProducer::getID($clientId, $idType);
                        $serialId = 'L' . $serialId;
                        $batchUpdate[] = "UPDATE {$tableName} SET serial_id='{$serialId}' where {$primaryKey}={$opportunityId}";
                    } else {
                        $serialIds[] = $opportunity['serial_id'];
                    }
                }
                if (!$dryRun) {
                    $succeedCount = $pg->getCommandBuilder()->createSqlCommand(implode(';', $batchUpdate))->execute();
                    echo "*完成 client_id: {$clientId}, 更改数量 {$succeedCount} \n";
                }
                \common\library\server\es_search\SearchQueueService::pushCompanyQueue(0, $clientId,
                    array_column($opportunityList, 'company_id'), \Constants::SEARCH_INDEX_TYPE_UPDATE);
            } else {
                echo "^跳过 client_id: {$clientId} \n";
            }
        }
    }


    public function actionCheckCustomerSerialId($clientId = 0, $dryRun = 1)
    {
        if ($clientId) {
            $this->fixClientCustomerSerialId($clientId, $dryRun);
        } else {
            $clients = $this->getClientList();
            foreach ($clients as $client) {
                $clientId = $client->client_id;
                $this->fixClientCustomerSerialId($clientId, $dryRun);
            }
        }
    }

    public function fixClientCustomerSerialId($clientId, $dryRun = 1)
    {
        echo "-开始 client_id: {$clientId} \n";
        $sql = "select serial_id, company_id from tbl_company where client_id = {$clientId} and is_archive = 1 group by client_id, serial_id having count(*) > 1";
        $db = PgActiveRecord::getDbByClientId($clientId);
        $list = $db->createCommand($sql)->queryAll(false);
        dd($list);
//
//        if ($count) {
//            echo "| 找到clientId:{$clientId} 重复 {$count} \n";
//            LogUtil::info(json_encode([$clientId => $count]));
//            $this->actionFixCustomerSerialIdRedis($clientId, $dryRun);
//            $this->actionFixCustomerSerialId($clientId, $dryRun);
//        } else {
//            echo "^ 未找到找到clientId:{$clientId} 存在重复 \n";
//        }
    }

    public function actionClientCustomerSerialIdDetail($clientId = 0)
    {
        $fileExt = md5($clientId);
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        $filePath = "/tmp/company_duplicate_serial_id_{$fileExt}.csv";

        if( file_exists($filePath) )
        {
            unlink($filePath);
        }
        $fp = fopen($filePath, 'w');
        fputcsv($fp, ['client_id', 'serial_id', 'company_id']);

        foreach ($clientIds as $clientId) {
            echo "-开始 client_id: {$clientId} \n";
//        $sql = "select serial_id, create_time, update_time from tbl_company where client_id = {$clientId} and serial_id in (
//                  select serial_id from tbl_company where client_id = {$clientId} group by client_id, serial_id having count(*) > 1
//                ) order by serial_id, create_time;";
            $sql = "select client_id,serial_id,array_to_string(array_agg(company_id), ',') as company_id from tbl_company where client_id = {$clientId} and is_archive = 1 group by client_id, serial_id having count(*) > 1";
            $db = PgActiveRecord::getDbByClientId($clientId);
            $result = $db->createCommand($sql)->queryAll();

            if ($count = count($result)) {
                echo "| 找到clientId:{$clientId} 重复 {$count} \n";
                foreach ($result as $line) {
                    fputcsv($fp, $line);
                }
            } else {
                echo "^ 未找到找到clientId:{$clientId} 存在重复 \n";
            }
        }
        fclose($fp);
    }

    /**
     * Mongo 转移到 PG
     *
     * @param int $client_id
     */
    public function actionMigrationCustomerMatchRule($client_id = 0)
    {
        if ($client_id) {
            $this->actionMigrationCustomerMatchRuleForClient($client_id);
        } else {
            $clients = $this->getClientList();
            foreach ($clients as $client) {
                $client_id = $client->client_id;
                $this->actionMigrationCustomerMatchRuleForClient($client_id);
            }
        }
    }

    public function actionMigrationCustomerMatchRuleForClient($client_id)
    {
        $tableName = 'tbl_customer_match_rule';
        $params = array(
            'client_id' => MongoObject::int32Value($client_id),
        );
        $read = new \MongoDB\Driver\ReadPreference(\MongoDB\Driver\ReadPreference::RP_SECONDARY_PREFERRED);
        $options = ['readPreference' => $read];

        $oldClass = CustomerMatchRules::getOldClass();
        $ret = $oldClass::getCollectionFromClientId($client_id)->find($params, $options);
        $succeedCount = 0;
        $failCount = 0;
        $batchData = [];
        $db = PgActiveRecord::getDbByClientId($client_id);
        $deletedCount = $db->createCommand("delete from {$tableName} where client_id=:client_id")->execute([':client_id' => $client_id]);
        foreach ($ret as $rules) {
            $rule = MongoObject::trimBsonDocument($rules);
            $batchData[] = [
                'id'           => CustomerMatchRules::produceAutoIncrementId(),
                'client_id'    => $rule['client_id'],
                'user_id'      => $rule['user_id'],
                'type'         => $rule['type'],
                'create_time'  => $rule['create_time'],
                'update_time'  => $rule['update_time'],
                'country'      => \common\library\util\PgsqlUtil::formatArray(array_values($rule['country'])),
                'biz_type'     => \common\library\util\PgsqlUtil::formatArray(array_values($rule['biz_type'])),
                'scale_id'     => \common\library\util\PgsqlUtil::formatArray(array_values($rule['scale_id'])),
                'category_ids' => json_encode($rule['category_ids'] ?? []),
                'update_user'  => $rule['update_user'],
            ];
        };

        if (count($batchData)) {
            $succeedCount = $db->getCommandBuilder()->createMultipleInsertCommand($tableName, $batchData)->execute();
        } else {
            $succeedCount = 0;
        }

        $failCount = count($batchData) - $succeedCount;

        echo "migrate client: {$client_id} succeed count: {$succeedCount} fail count: {$failCount} \n";
    }

    public function actionRefreshCompanySerialId($clientId, $companyId)
    {
        $db = PgActiveRecord::getDbByClientId($clientId);
        $companyIds = explode(',', $companyId);
        foreach ($companyIds as $companyId) {
            $serialId = \IDProducer::getID($clientId, \IDProducer::TYPE_COMPANY);
            $batchUpdate[] = "UPDATE tbl_company SET serial_id={$serialId} where company_id={$companyId}";
        }
        $succeedCount = $db->getCommandBuilder()->createSqlCommand(implode(';', $batchUpdate))->execute();

        \common\library\server\es_search\SearchQueueService::pushCompanyQueue(0, $clientId, $companyIds, \Constants::SEARCH_INDEX_TYPE_UPDATE);
        echo "*完成 client_id: {$clientId}, 更改数量 {$succeedCount} \n";
    }

    public function actionResetCompanySerialId($clientId, $companyId, $serialId)
    {
        $db = PgActiveRecord::getDbByClientId($clientId);
        $batchUpdate[] = "UPDATE tbl_company SET serial_id={$serialId} where company_id={$companyId}";
        $succeedCount = $db->getCommandBuilder()->createSqlCommand(implode(';', $batchUpdate))->execute();
        \common\library\server\es_search\SearchQueueService::pushCompanyQueue(0, $clientId, [$companyId], \Constants::SEARCH_INDEX_TYPE_UPDATE);
        echo "*完成 client_id: {$clientId}, 更改数量 {$succeedCount} \n";
    }

    /**
     * 永久删除某一个成员所跟进的客户，包括共同跟进
     */
    public function actionRemoveUserCompany(){
        $userId = 0;

        User::setLoginUserById($userId);
        $user = User::getLoginUser();
        if (!$user->hasInfo()){
            return;
        }

        $clientId = $user->getClientId();

        $sql = "select company_id from tbl_company where client_id={$clientId} AND is_archive=1 and user_id @> ARRAY[$userId]::bigint[]";
        $db = PgActiveRecord::getDbByClientId($user->getClientId());
        $companyIds = $db->createCommand($sql)->queryColumn();

        if (empty($companyIds)){
            return;
        }

        $companyIdList = array_chunk($companyIds, 1000);

        foreach ($companyIdList as $ids){
            $companyIdString = implode(',', $ids);

            $updateCompany = "update tbl_company set user_id='{}',is_archive=0 WHERE company_id IN ($companyIdString)
AND client_id = {$clientId} AND is_archive = 1";
//            var_dump($updateCompany);
//        $db->createCommand($updateCompany)->execute();

            $updateCustomer = "update tbl_customer set user_id='{}',is_archive=0 WHERE client_id = {$clientId} AND
company_id IN ($companyIdString) AND is_archive = 1";
//            var_dump($updateCustomer);
//        $db->createCommand($updateCustomer)->execute();
//            die;
        }
    }


    private function willMoveToPublicForAIMarketing(int $client_id,int $user_id,array $company_data)
    {
        //批量操作
        $db = PgActiveRecord::getDbByClientId($client_id);

        if(empty($company_data)) {
            return false;
        }

        $company_data = array_chunk($company_data,1000);
        $type = \common\library\ai\Constant::WILL_MOVE_TO_PUBLIC;
        $createTime = date('Y-m-d H:i:s');
        try{
            foreach ($company_data  as $chunk) {

                $insertValues = [];
                foreach ($chunk as $item) {
                    $companyId = $item['company_id'];
                    $companyHashId = $item['company_hash_id'];
                    $insertValues[] = "({$client_id},{$user_id},{$companyId},'{$companyHashId}','{$createTime}',0,{$type})";

                }
                if(empty($insertValues)) {
                    continue;
                }
                //每一个chunk 结束 尝试入库
                $values = implode(',',$insertValues);

                $sql = "INSERT INTO tbl_ai_activate_company (client_id, user_id, company_id, company_hash_id, create_time,read_flag,type) values {$values} ON CONFLICT (user_id,company_id,type) DO UPDATE SET read_flag=excluded.read_flag, create_time=excluded.create_time";

                $db->createCommand($sql)->execute();
            }

        }catch (\Exception $exception){
            \LogUtil::error($exception->getMessage());
        }

    }

    public function actionFixMultiSelect($clientId)
    {
        $db = PgActiveRecord::getDbByClientId($clientId);
        $filed = 'company_id,external_field_data';
        $sql = 'select ' .$filed.' from tbl_company where client_id='.$clientId;
        $customerSql = 'select company_id,customer_id,external_field_data from tbl_customer where client_id='.$clientId;
        $currId = 0;
        $maxId = $db->createCommand('select company_id from tbl_company where client_id='.$clientId.' order by company_id desc limit 1')->queryScalar();

        $customerExtFieldSetting = Helper::getExternalFieldSetting($clientId, \Constants::TYPE_CUSTOMER);
        $companyExtFieldSetting = Helper::getExternalFieldSetting($clientId, \Constants::TYPE_COMPANY);
        $customerFieldTypeMap = array_column($customerExtFieldSetting, 'field_type', 'id');
        $companyFieldTypeMap = array_column($companyExtFieldSetting, 'field_type', 'id');

        self::info("begin client_id:$clientId maxId:{$maxId} currId: {$currId}");
        while ( $currId < $maxId ) {
            self::info("process client_id:$clientId maxId:{$maxId} currId: {$currId}");
            $companyList = $db->createCommand($sql . ' and company_id >' . $currId . ' and is_archive =1 order by company_id asc limit 1000')->queryAll();
            $companyIds = array_column($companyList,'company_id');
            if (empty($companyIds)) {
                break;
            }

            $bulk = [];
            foreach ($companyList as $item) {
                $needUpdate = false;
                $externalData = json_decode($item['external_field_data'], true);
                foreach ($externalData as $k => $v) {
                    if (($companyFieldTypeMap[$k] ?? null) == \common\library\custom_field\CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT) {
                        if (!is_array($v) && !empty($v)) {
                            $externalData[$k] = json_decode($v, true) ?? explode(',', $v);
                            $needUpdate = true;
                        }
                    }
                }
                if ($needUpdate) {
                    $bulk[] = "update tbl_company set external_field_data = '" . json_encode($externalData) . "' where company_id=" . $item['company_id'];
                }
                $currId = $item['company_id'];
            }
            if (count($bulk)) {
                $db->getCommandBuilder()->createSqlCommand(implode(';', $bulk))->execute();
            }

            $customerList = $db->createCommand($customerSql.' and is_archive =1 and company_id in ('.implode(',',$companyIds).')')->queryAll();
            $bulk = [];
            foreach ($customerList as $customer) {
                $needUpdate = false;
                $externalData = json_decode($customer['external_field_data'], true);
                foreach ($externalData as $k => $v) {
                    if (($customerFieldTypeMap[$k] ?? null) == \common\library\custom_field\CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT) {
                        if (!is_array($v) && !empty($v)) {
                            $externalData[$k] = json_decode($v, true) ?? explode(',', $v);
                            $needUpdate = true;
                        }
                    }
                }
                if ($needUpdate) {
                    $bulk[] = "update tbl_customer set external_field_data = '" . json_encode($externalData) . "' where customer_id=" . $customer['customer_id'];
                }
            }
            if (count($bulk)) {
                $db->getCommandBuilder()->createSqlCommand(implode(';', $bulk))->execute();
            }
        }
    }

    /**
     * @param $userId
     * @param $taskId
     * @param $fieldEditType
     * 将自动化的推荐建档
     */
    public function actionArchiveByAdvice($userId, $taskId, $fieldEditType = 1)
    {
        User::setLoginUserById($userId);
        self::info("process userId:$userId,taskId:$taskId,fieldEditType:$fieldEditType");

        $archiveService = new \common\library\ai\classify\customer\advice\AdviceArchiveService($userId, $taskId);
        $archiveService->setFieldEditType($fieldEditType);
        $archiveService->process();
    }

    /**
     * 处理关闭多公海开关的pool_id字段
     */
    public function actionDisablePoolField()
    {

        $dbSetList = $this->dbSetList(DbSet::TYPE_MYSQL, 2);
        if (\Yii::app()->params['env'] == 'test') {
            $dbSetList = [['set_id' => 3]];
        }

        foreach ($dbSetList as $dbSet) {
            $setId = $dbSet['set_id'];

            $disableClient = [];
            $clientList = $this->getClientList(0, false, null, null, 0, 0, null, $setId);
            foreach ($clientList as $client) {
                $data = \common\library\account\Helper::getClientExternalValue($client->client_id, [\common\library\account\Client::EXTERNAL_KEY_CUSTOMER_POOL_SWITCH]);

                $poolSwitch = $data['customer_pool_switch'] ?? 0;
                if ($poolSwitch) {
                    self::info("clientId:{$client->client_id} continue!");
                    continue;
                }

                $disableClient[] = $client->client_id;
                self::info("clientId:{$client->client_id}");
            }

            if (empty($disableClient))
                continue;

            $db = ProjectActiveRecord::getDbByDbSetId($setId);
            $clientIdString = implode(',', $disableClient);
            $sql = "update tbl_custom_field set enable_flag=0 where client_id IN ($clientIdString) and type=4 and id='pool_id';";
//            var_dump($sql);
            $db->createCommand($sql)->execute();
        }
    }

    /**
     * @return bool
     * 使用移入公海设置的公司列表
     */
    public function actionUsedPublicSetting()
    {
        $dbSetList = $this->dbSetList(DbSet::TYPE_MYSQL, 2);
        if (\Yii::app()->params['env'] == 'test')
        {
            $dbSetList = [['set_id' => 3]];
        }

        $clientList = [];
        foreach ($dbSetList as $dbSet)
        {
            $setId = $dbSet['set_id'];

            $db = ProjectActiveRecord::getDbByDbSetId($setId);

            $sql = " select DISTINCT(client_id) from tbl_customer_group where public_time > 0 and enable_flag = 1";
            $clientIds = $db->createCommand($sql)->queryColumn();
            $clientList = array_merge($clientList, $clientIds);
        }

        if (empty($clientList))
            return false;

        $clientString = implode(',', $clientList);

        $db = Yii::app()->db;
        $sql = "select client_id,name from tbl_client WHERE client_id IN ($clientString)";

        $clientList = $db->createCommand($sql)->queryAll();

        $path = "/tmp/used_public_client.csv";
        $fp = fopen($path,'w');
        foreach ($clientList as $item) {
            fputcsv($fp, [$item['client_id'], $item['name']]);
        }
        fclose($fp);

        $content = file_get_contents($path);
        $content = mb_convert_encoding($content, "GBK", "UTF-8");

        //如果没有转换失败的话
        if ($content != false)
            file_put_contents($path, $content);

        echo $path. "\n";
    }

    /**
     * @param int $clientId
     * 清理客户中已建档邮箱
     */
    public function actionCleanAdvice($clientId = 0)
    {
        $clientList = $this->getClientList($clientId);

        $num = 1;
        $count = count($clientList);
        foreach ($clientList as $client) {
            $clientId = $client['client_id'];

            echo "$clientId $num/$count" . PHP_EOL;
            $num++;

            $adminId = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();
            if (empty($adminId)) {
                self::info("clientId: $clientId,empty admin_user_id,clientId:$clientId");
                continue;
            }

            User::setLoginUserById($adminId);

            $adviceList = new \common\library\ai\classify\customer\advice\CustomerAdviceList($adminId);
            $adviceList->setFields('advice_id, email');
            $list = $adviceList->find();

            $countAdvice = count($list);
            self::info("clientId: $clientId,advice email count:".$countAdvice);

            if (!$countAdvice)
                continue;

            $adviceEmails = array_column($list,'email');
            $emailVsAdviceId = array_column($list, 'advice_id', 'email');

            $customerList = new \common\library\customer_v3\customer\CustomerList($clientId);
            $customerList->setFields('email');
            $customerList->setIsArchive(1);
            $customerList->setEmail($adviceEmails);
            $list = $customerList->find();

            $removeAdviceIds = [];
            foreach ($list as $customer) {
                $email = $customer['email'];
                if (isset($emailVsAdviceId[$email]))
                {
                    $removeAdviceIds[] = $emailVsAdviceId[$email];
                }
            }
            $countReMoveAdvice = count($removeAdviceIds);
            self::info("clientId: $clientId,remove advice email count:".$countReMoveAdvice);

            if (!$countReMoveAdvice)
                continue;

            $adviceOperator = new \common\library\ai\classify\customer\advice\CustomerAdviceBatchOperator($adminId);
            $adviceOperator->setParams(['advice_id'=> $removeAdviceIds]);
            $result = $adviceOperator->delete();

            self::info("clientId: $clientId,  delete result $result");
        }

    }

    /**
     * @param int $clientId
     * @param int $grey
     * 分析商机、订单更新客户相应统计数及交易时间（手动）
     */
    public function actionRefreshDeal($clientId = 0, $grey = 0)
    {
        $clientIds = [];

        if ($clientId) {
            $clientIds = [$clientId];
        } elseif ($grey) {
            $clientIds = \common\library\GreyEnvHelper::getGreyClientIds();
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        $clientCount = count($clientIds);

        echo 'clientCount:'.$clientCount;
        echo "\n";
        LogUtil::info('clientCount:'.$clientCount);

        $num = 1;
        foreach ($clientIds as $clientId) {

            echo "Client $clientId: $num/$clientCount";
            echo "\n";
            LogUtil::info("Client $clientId: $num/$clientCount");
            $num++;

            $adminId = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();
            if (!$adminId)
                continue;

            User::setLoginUserById($adminId);
            $masterUser = User::getLoginUser();
            if ($masterUser->isEmpty())
                continue;


            $db = PgActiveRecord::getDbByClientId($clientId);

            $companyMap = [];

            //opportunity
            $sql = "select company_id,count(1) from tbl_opportunity where client_id=$clientId and enable_flag=1 and stage_type=2 group by company_id ";
            $list = $db->createCommand($sql)->queryAll(true);
            foreach ($list as $item) {
                $companyMap[$item['company_id']]['success_opportunity_count'] = $item['count'];
            }

            //order
            $sql = "select company_id,count(1) from tbl_order where client_id=$clientId and enable_flag=1 and account_flag=1 group by company_id ";
            $list = $db->createCommand($sql)->queryAll(true);
            foreach ($list as $item) {
                $companyMap[$item['company_id']]['performance_order_count'] = $item['count'];
            }

            $companyIds = array_keys($companyMap);
            if (empty($companyIds))
                continue;

            //Setting
            $updateType = CustomerOptionService::getDealSetting($clientId);

            $dealTimeSql = '';
            if ($updateType == Constants::TYPE_OPPORTUNITY)
                $dealTimeSql = "select account_date as deal_time from tbl_opportunity where client_id=$clientId and enable_flag=1 and stage_type=2 and company_id = :company_id order by account_date desc";
            if ($updateType == Constants::TYPE_ORDER)
                $dealTimeSql = "select account_date as deal_time from tbl_order where client_id=$clientId and enable_flag=1 and account_flag=1 and company_id = :company_id order by account_date desc";

            if (empty($dealTimeSql))
                continue;

            foreach ($companyIds as $companyId)
            {
                $params[':company_id'] = $companyId;
                $successTime = $db->createCommand($dealTimeSql)->queryRow(true, $params);
                !empty($successTime) && $companyMap[$companyId]['deal_time'] = $successTime['deal_time'];
            }

            if (empty($companyMap))
                continue;

            $updateSqlArray = [];
            foreach ($companyMap as $companyId => $item) {
                $set = [];
                if (isset($item['success_opportunity_count']))
                    $set[] = " success_opportunity_count = {$item['success_opportunity_count']}";

                if (isset($item['performance_order_count']))
                    $set[] = " performance_order_count = {$item['performance_order_count']}";

                if (isset($item['deal_time']))
                    $set[] = " deal_time = case when deal_time < '{$item['deal_time']}' then '{$item['deal_time']}' else deal_time end";

                if (!empty($set))
                {
                    $set = implode(',', $set);
                    $updateSqlArray[] = "update tbl_company set $set where company_id=$companyId";
                }
            }
            $updateSql = implode('; ', $updateSqlArray);
//            var_dump($companyMap);
            $res = $db->createCommand($updateSql)->execute();

            self::info("clientId:$clientId; res:$res; ");
        }
    }

    public function actionFindDuplicate()
    {
        $userId = 12863126;
        $userId = 0;
        User::setLoginUserById($userId);
        $user = User::getLoginUser();

        $clientId = $user->getClientId();
        $pgDb = CompanyModel::getDbByClientId($clientId);

        $map = [];
        $serialMap = [];
        $sql = "SELECT name,company_id,serial_id FROM tbl_company WHERE client_id=$clientId AND user_id @> ARRAY[$userId]::bigint[]";
        $list = $pgDb->createCommand($sql)->queryAll(true);
        foreach ($list as $item) {
            $name = $item['name'];
            $map[$name][] = $item['company_id'];
            $serialMap[$item['company_id']] = $item['serial_id'];
        }

        $path = 'duplicate_company_'.date('YmdHi').'.csv';

        $fp = fopen($path,'w+');

        fputcsv($fp, ['company_id', 'company_name', 'serial_id']);

        foreach ($map as $name => $item) {
            if (count($item) <= 1)
                continue;

            foreach ($item as $companyId) {
                $serialId = $serialMap[$companyId]??'';
                fputcsv($fp, [$companyId, $name, $serialId]);
            }
            fputcsv($fp, []);
        }

        echo "$path \n";
    }

    public function actionFixMoveToPublicByClient($date = '', $clientId = 0)
    {
        if (!$clientId) {
            $date = $date ?: date('Y-m-d');
            $nextDate =  date('Y-m-d', strtotime($date) + (24 * 3600));
            $sql = "select record_id,result from tbl_crontab_task_record where task_id = 2 and dispatch_type = 'client' and queue_time >= '$date' and queue_time < '$nextDate'";
            $prometheusDb = Yii::app()->prometheus_db;
            $list = $prometheusDb->createCommand($sql)->queryAll();
            foreach ($list as $item) {
                $clientList = [];
                foreach (json_decode($item['result'], true) as $clientId => $result) {
                    if (isset($result['error'])) {
                        $clientList[] =  $clientId;
                    }
                }
                $action = 'fixMoveToPublicByClient';
                $command = realpath($_SERVER['SCRIPT_FILENAME']) ;
                $controller = $this->getName();
                $log = "/tmp/console_{$controller}_{$action}_{$item['record_id']}.log";
                $params = ' --clientId=' . implode(',', $clientList) . " --date=" . $date;
                $shell = "nohup {$command} $controller $action $params >> {$log} 2>&1 & \n";
                echo $shell ."\n";
            }
        } else {
            $clientList = explode(',', $clientId);
            $task = new \common\library\server\crontab\task\CustomerTask();
            foreach ($clientList as $clientId) {
                echo "start for $clientId \n";
                $task->runAction('runMoveToPublic', [$clientId]);
                echo "finish for $clientId \n";
            }
        }
    }

    public function actionInitCustomerOptionService($clientId, $dryRun = 1)
    {
        $admin_user_id = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();
        $version = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getMainSystemId();
        echo "client: $clientId, admin: $admin_user_id, version: $version \n";
        if (!$dryRun) {
            $user = User::getUserObject($admin_user_id);
            User::setLoginUserById($admin_user_id);
            CustomerOptionService::iniClient($user, $version);
        }
    }

    /**
     * 恢复删除分组：需要运维协助将备份数据导入一台机器备用
     */
    public function actionRecoverCompanyGroupId()
    {
        $clientId = 0;//8112
//        $clientId = 8112;

        $mysqlSet = [
//            'set_id' => '112234556678',
//            'type' => DbSet::TYPE_MYSQL,
//            'host' => 'rm-bp1wr0v2xax7j21qh.mysql.rds.aliyuncs.com',
//            'name' => 'v5_client_79',
//            'user' => 'crmphp',
//            'password' => 'ou5pxhJTRxvqWcyr8cYBMXKqGMZ6eg',
//            'port' => '3306',
//            'proxy_host' => '',
//            'proxy_name' => '',
//            'proxy_port' => '0',
        ];
        $pgSet = [
//            'set_id' => '1122345566789',
//            'type' => DbSet::TYPE_PGSQL,
//            'host' => '************',
//            'name' => 'v5_client_79',
//            'user' => 'crmphp',
//            'password' => 'ou5pxhJTRxvqWcyr8cYBMXKqGMZ6eg',
//            'port' => '5432',
//            'proxy_host' => '',
//            'proxy_name' => '',
//            'proxy_port' => '0',
        ];
        $backup_pgDb = PgActiveRecord::getDbByDbSet($pgSet);
        $backup_mysqlDb = ProjectActiveRecord::getDbByDbSet($mysqlSet);

        $pgDb = PgActiveRecord::getDbByClientId($clientId);
        $mysqlDb = ProjectActiveRecord::getDbByClientId($clientId);

        //group 把所有当时存在的客户的跟进人和分组恢复成当时的情况
        $groupType = Constants::TYPE_COMPANY;
        $sql = "select * from tbl_group where client_id=$clientId and type=$groupType";
        $groupList = $backup_mysqlDb->createCommand($sql)->queryAll(true);

        $groupIds = [];
        $insertArray = [];
        foreach ($groupList as $group)
        {
            $groupIds[] = $group['id'];
            $insertArray[] = [
                'id' => $group['id'],
                'type' => $group['type'],
                'client_id' => $group['client_id'],
                'parent_id' => $group['parent_id'],
                'prefix' => "'{$group['prefix']}'",
                'layer' => "{$group['layer']}",
                'name' => "'{$group['name']}'",
                'description' => "'{$group['description']}'",
                'version' => "{$group['version']}",
                'create_time' => "'{$group['create_time']}'",
                'create_user' => "{$group['create_user']}",
                'update_time' => "'{$group['update_time']}'",
                'rank' => $group['rank']
            ];
        }

        if (!empty($insertArray))
        {
            $insert = "insert into tbl_group (id, type,client_id,parent_id,prefix,layer,name,description,version,create_time,create_user,update_time,rank) VALUES ";
            foreach ($insertArray as $row) {
                $rowStr = implode(',', $row);   //字段
                $insert .= "($rowStr),";
            }
            $insert = rtrim($insert, ',');
            $insert .= "ON DUPLICATE KEY UPDATE update_time=VALUES(update_time)";
            $mysqlDb->createCommand($insert)->execute();
        }

        $sql = "select company_id, user_id, group_id from tbl_company where client_id=$clientId and group_id > 0 and is_archive=1";
        $list = $backup_pgDb->createCommand($sql)->query();

        $updateGroupMap = [];
        $updateUserArray = [];
        foreach ($list as $row)
        {
            $updateGroupMap[$row['group_id']][] = $row['company_id'];
            $updateUserArray[] = "update tbl_company set user_id = '{$row['user_id']}' where client_id=$clientId and company_id={$row['company_id']}";
        }

        //GroupId
        foreach ($updateGroupMap as $groupId => $companyIds)
        {
            $companyIdsString = implode(',', $companyIds);
            $sql = "update tbl_company set group_id = {$row['group_id']} where client_id=$clientId and company_id IN ($companyIdsString)";
            $pgDb->createCommand($sql)->execute();
        }

        //userId
        if (!empty($updateUserArray))
        {
            $updateUserArray = array_chunk($updateUserArray, 100);
            foreach ($updateUserArray as $rows)
            {
                $updateUserSql = implode(';', $rows);
                $pgDb->createCommand($updateUserSql)->execute();
            }
        }

    }

    public function actionFixExternalData($clientId, $dryRun=1)
    {
        if ($clientId) {
            $clientIds = [$clientId];
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        foreach ($clientIds as $clientId) {
            $db = PgActiveRecord::getDbByClientId($clientId);
            if (!$db) {
                echo "skip for $clientId \n";
                continue;
            }
            $sql = "select count(1) company_id from tbl_company where client_id = $clientId and external_field_data = '[]'";
            $count = $db->createCommand($sql)->queryScalar();
            echo "client:$clientId count $count \n";
            if (!$dryRun) {
                $sql = "update tbl_company set external_field_data='{}' where client_id = $clientId and external_field_data = '[]'";
                $sql = "update tbl_customer set external_field_data='{}' where client_id = $clientId and external_field_data = '[]'";
                $count = $db->createCommand($sql)->execute();
                echo "client:$clientId count $count updated \n";
            }
        }
    }


    /**
     * @param int $clientId
     * 清理company_id不存在的company、customer数据
     */
    public function actionCleanNotExistsCompany($clientId)
    {
        //clientId 20460 20546
        $pgDb = PgActiveRecord::getDbByClientId($clientId);

        $sql = "SELECT customer.customer_id,customer.company_id FROM tbl_customer AS customer
LEFT OUTER JOIN tbl_company AS company
ON (customer.company_id = company.company_id)
WHERE customer.client_id={$clientId} AND customer.company_id > 0 AND company.company_id IS NULL;
";

        $list = $pgDb->createCommand($sql)->queryAll(true);

        $bulk = [];
        $model = \common\models\search\CompanySearch::model();
        $indexName = $model->index();
        $typeName  = $model->type();

        foreach ($list as $item)
        {
            $companyId = $item['company_id'];

            //customer
            $deleteCustomerSql = "update tbl_customer set is_archive=0 WHERE client_id=$clientId AND company_id=$companyId";
            $row = $pgDb->createCommand($deleteCustomerSql)->execute();

            $bulk[] = [
                'delete' => [
                    '_index'   => $indexName,
                    '_type'    => $typeName,
                    '_id'      => $companyId,
                    '_routing' => $clientId,
                ]
            ];
        }

        //更新搜索引擎
        if (!empty($bulk))
        {
            $params = [
                'index' =>$indexName,
                'type' => $typeName,
                'body' => $bulk
            ];

            $model->getDbConnection()->bulk($params);
            $model->getDbConnection()->indices()->refresh(['index'=>$indexName]);
        }
    }


    /**
     * @param $clientId
     * @param int $grey
     * @param null $greyNum
     * 修复company删除后user_id未清空的问题
     */
    public function actionFixUserId($clientId, $grey = 0, $greyNum = null)
    {
        $clientIds = [];
        if ($clientId) {
            $clientIds = [$clientId];
        } elseif ($grey) {
            if (!is_null($greyNum)) {
                $clientIds = $this->getGreyClientIds($greyNum);
            }
        } else {
            $dbSetId = Yii::app()->params['env'] == 'test' ? 3 : 0;
            $clientIds = array_column($this->getClientList($clientId,false,null,null, 0, 0, null, $dbSetId), 'client_id');
        }

        $num = 1;
        $clientCount = count($clientIds);
        foreach ($clientIds as $clientId)
        {
            echo "Client $clientId: $num/$clientCount";
            echo "\n";
            LogUtil::info("Client $clientId: $num/$clientCount");
            $num++;

            $sql = "UPDATE tbl_company set user_id='{}' where client_id={$clientId} and user_id <>'{}' and is_archive=0";
            $db = PgActiveRecord::getDbByClientId($clientId);
            $db->createCommand($sql)->execute();
        }
    }

    /**
     * 推送当前客户实施状态给ali
     * 1.查询&format数据push到队列
     * 2.oss端起crontab消费推送
     */
    public function actionPushImplementationStatus()
    {
        $opClientId = \common\library\alibaba\Constant::OKKI_OPERATION_DEPARTMENT_CLIENT;//小满客服

        $db = PgActiveRecord::getDbByClientId($opClientId);
        $sql = "SELECT company_id FROM tbl_company WHERE client_id = $opClientId ORDER BY company_id DESC limit 1";
        $maxCompanyId = $db->createCommand($sql)->queryScalar();

        //没有测试环境数据，直接就是推送到线上队列
        $redis = \RedisService::getInstance('redis_queue');
        $queueKey = \common\library\alibaba\push\pushDispatcher::COMPANY_TRAIL_STATUS_QUEUE_LIST_KEY;

        $currentId = 0;
        LogUtil::info("begin sync user client_id:$opClientId maxId:{$maxCompanyId} currId: {$currentId}");
        while ($currentId < $maxCompanyId)
        {
            $sourceSql = "select company_id,trail_status,external_field_data from tbl_company
where $currentId < company_id and client_id=$opClientId order by company_id asc limit 100";
            $result = $db->createCommand($sourceSql)->queryAll(true);

            $data = [];
            foreach ($result as $elem)
            {
                $currentId = $elem['company_id'];

                $externalData = json_decode($elem['external_field_data'], true);
                $osClientId = $externalData['10184058629'] ?? 0;

                if (!$osClientId)
                    continue;

                $statusName = $elem['trail_status'] == 19472242482 ? 'DOING' : ($elem['trail_status'] == 19472246137 ? 'DONE' : 'TODO');

                $data[] = [
                    'okki_client_id' => $osClientId,
                    'implementation_status' => $statusName
                ];
            }

            if (!empty($data))
            {
                $value = json_encode(
                    [
                        'msg_type' => 'ali_implementation_status',
                        'data' => $data
                    ]);
                $res = $redis->lpush($queueKey, [$value]);
                self::info($res);
                self::info("push: $queueKey $value");
            }
        }
    }

    public function actionRetryImportTask($step = 20, $dryRun = 1)
    {
        $setList = \common\library\account\service\DbService::getDbList(DbSet::TYPE_MYSQL);
        $count = 0;
        $dryRunCount = 0;
        foreach ($setList as $set) {
            $db = ProjectActiveRecord::getDbByDbSet($set);
            $sql = "SELECT * FROM tbl_customer_import WHERE `status` = 2 AND create_time >= '2023-07-24 08:00:00' and create_time < '2023-07-26 22:00:00';";
            $list = $db->createCommand($sql)->queryAll();
            foreach ($list as $item) {
                User::setLoginUserById($item['user_id']);
                $taskId = $item['task_id'];
                $userId = $item['user_id'];
                $type = $item['type'];
                $dryRunCount++ ;
                echo "task $taskId type: $type client " . $item['client_id'] . " create time " . $item['create_time'] . "dryRunCount".$dryRunCount."\n";
                if ($dryRun || $count > $step) {
                    continue;
                }
                $model = CustomerImport::model()->findByPk($taskId);
                $model->status = CustomerImport::STATUS_INIT;
                $model->update(['status']);

                switch ($type) {
                    case Constants::TYPE_CUSTOMER:
                        $log = '/tmp/customer_import.log';
                        \common\library\CommandRunner::run(
                            'customer',
                            'import',
                            [
                                'task_id' => $taskId,
                                'user_id' => $userId
                            ],
                            $log
                        );
                        echo " start task $taskId type: $type client " . $item['client_id'] . " create time " . $item['create_time'] . "\n";
                        $count++;
                        break;
                    case Constants::TYPE_LEAD:
                        $log = '/tmp/lead_import.log';
                        \common\library\CommandRunner::run(
                            'customer',
                            'importLead',
                            [
                                'task_id' => $taskId,
                                'user_id' => $userId,
                            ],
                            $log
                        );
                        echo " start task $taskId type: $type client " . $item['client_id'] . " create time " . $item['create_time'] . "\n";
                        $count++;
                        break;
                }
            }
        }
    }

    public function actionRetryExportTask($step = 20, $dryRun = 1)
    {
        $setList = \common\library\account\service\DbService::getDbList(DbSet::TYPE_MYSQL);
        $count = 0;
        foreach ($setList as $set) {
            $db = ProjectActiveRecord::getDbByDbSet($set);
            $sql = "SELECT * FROM tbl_customer_export WHERE `status` = 1 AND create_time >= '2020-09-25 12:00:00' and create_time < '2020-09-26 18;00:00';";
            $list = $db->createCommand($sql)->queryAll();
            foreach ($list as $item) {
                $taskId = $item['task_id'];
                $userId = $item['user_id'];
                $type = $item['type'];
                $params = $item['param'];
                echo "task $taskId type: $type client " . $item['client_id'] . " create time " . $item['create_time'] . " params: $params \n";
                if ($dryRun || $count > $step) {
                    continue;
                }
                switch ($type) {
                    case CustomerExportTask::TYPE_CUSTOMER:
                        $log = '/tmp/customer_export.log';
                        $count++;
                        break;
                    case CustomerExportTask::TYPE_COMPANY_TRAIL:
                        $log = '/tmp/company_trail_export.log';
                        $count++;
                        break;
                    case CustomerExportTask::TYPE_OPPORTUNITY:
                        $log = '/tmp/opportunity_export.log';
                        $count++;
                        break;
                }
                if (empty($log)) {
                    continue;
                }
                \common\library\CommandRunner::run(
                    'export',
                    'export',
                    [
                        'operator_id' => $userId,
                        'task_id' => $taskId,
                        'type' => $type,
                        'params' => "'{$params}'",
                    ],
                    $log
                );
                echo "start task $taskId type: $type client " . $item['client_id'] . " create time " . $item['create_time'] . " params: $params \n";
            }
        }
    }

    public function actionRetryBuildTrail($dryRun = 1, $step = 20)
    {
        $setList = \common\library\account\service\DbService::getDbList(DbSet::TYPE_MYSQL);
        $count = 0;
        foreach ($setList as $set) {
            $db = ProjectActiveRecord::getDbByDbSet($set);
            $sql = "SELECT * FROM tbl_trail_rebuild_task WHERE `status` = 0 AND create_time >= '2020-09-25 12:00:00' and create_time < '2020-09-26 18;00:00';";
            $list = $db->createCommand($sql)->queryAll();
            foreach ($list as $item) {
                $taskId = $item['id'];
                $userId = \common\library\privilege_v3\PrivilegeService::getInstance($item['client_id'])->getAdminUserId();
                echo "task $taskId client " . $item['client_id'] . " user $userId create time " . $item['create_time'] . "\n";
                if ($dryRun || $count > $step) {
                    continue;
                }
                $log = '/tmp/trail_rebuild.log';
                \common\library\CommandRunner::run(
                    'trail',
                    'rebuild',
                    [
                        'task_id' => $taskId,
                        'user_id' => $userId,
                    ],
                    $log
                );
                $count++;
                echo "start task $taskId client " . $item['client_id'] . " create time " . $item['create_time'] . "\n";
            }
        }
    }

    public function actionSyncCustomerUserIdByCompany($clientId, $dryRun = 1)
    {
        $db = PgActiveRecord::getDbByClientId($clientId);
        $sql = "select distinct tbl_company.company_id as company_id, tbl_company.user_id as user_id from tbl_company join tbl_customer on tbl_company.company_id = tbl_customer.company_id where tbl_company.client_id = {$clientId} and tbl_company.user_id != tbl_customer.user_id;";
        $companyList = $db->createCommand($sql)->queryAll();
        $updateList = [];
        foreach ($companyList as $companyInfo) {
            $companyId = $companyInfo['company_id'];
            $userId = $companyInfo['user_id'];
            $updateList[] = "update tbl_customer set user_id='{$userId}' where client_id = $clientId and company_id = $companyId";
        }
        if ($count = count($updateList)) {
            if ($dryRun) {
                echo "update count $count \n";
                var_dump($updateList);
            } else {
                $count = $db->createCommand(implode(';', $updateList))->execute();
            }
        }
    }

    public function actionDeleteCompany($clientId, $all=0,$companyId=0)
    {
        $adminUserId = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();
        User::setLoginUserById($adminUserId);
        $db = PgActiveRecord::getDbByClientId($clientId);
        if( $all )
        {
            $sql  = "select company_id ,user_id from tbl_company where client_id={$clientId} and is_archive=1";
        }else
        {
            $sql  = "select company_id ,user_id from tbl_company where client_id={$clientId} and is_archive=1 and user_id='{}'";
        }

        if( $companyId )
        {
            $sql .= " and company_id={$companyId}";
        }

        $companyList = $db->createCommand($sql)->queryAll();
        $companyChunkArr = array_chunk($companyList ,1000);

        foreach ($companyChunkArr as  $k =>  $companyItemList)
        {
            $delCompanyIds = [];
            $mvCompanyIds = [];
            foreach ($companyItemList as $item )
            {
                if( $item['user_id'] == '{}')
                {
                    $delCompanyIds[] = $item['company_id'];
                }else
                {
                    $mvCompanyIds[] = $item['company_id'];
                }
            }

            if(!empty($mvCompanyIds) )
            {
                $op = new \common\library\customer\CompanyBatchOperator($adminUserId);
                $op->setParams(['company_ids' => $mvCompanyIds,'show_all'=> true]);
                $count = $op->moveToPublic();
            }

            $delCompanyIds = array_merge($delCompanyIds, $mvCompanyIds);
            $op = new \common\library\customer\CompanyBatchOperator($adminUserId);
            $op->setParams(['company_ids' => $delCompanyIds]);
            $count = $op->delete();


            self::info("del k: {$k} ret: {$count} rel:".count($delCompanyIds).' company_ids: '.implode(',', $delCompanyIds));
        }
        self::info(" total : ".count($companyList));


    }

    public function actionFixCustomerEmailIdentity()
    {
        ini_set("memory_limit", "1024M");
        $clientIds = array_column($this->getClientList(0, false, null, 0, 0, 0, [5,8]), 'client_id');
        $greyClientIds = explode(',', '3,18133,18137,18154,18152,6688,6698,18120,18136,19709,18617,38955,7,10,2106,17383,18618,16149,29865');
        $clientIds = array_diff($clientIds, $greyClientIds);
        $total = count($clientIds);
        $i = 1;
        foreach ($clientIds as $clientId) {
            // todo 估算量
            self::info("clientId[ $clientId ] - [$i/$total]");
            try {
                $selectEmailSql = "select email from tbl_email_identity where client_id = $clientId";
                $pgDb = PgActiveRecord::getDbByClientId($clientId);
                PgActiveRecord::setConnection($pgDb);
                $emails = $pgDb->createCommand($selectEmailSql)->queryColumn();
                $count = count($emails);
                self::info("emails总数为[{$count}]\n");

                $emails = array_chunk($emails, 10000);
                foreach ($emails as $emailChunk)
                {
                    !empty($emailChunk) && (new CustomerSync($clientId))->setFindEmail($emailChunk)->sync();
                }
                unset($emails);
            } catch (Exception $e) {
                self::info("clientId = $clientId, 错误信息是: {$e->getMessage()}\n\n");
            }
            $i++;
        }
    }

    // ./yiic-test customer mergeSameEmailCustomerTrail --clientId=14100 --companyId=1115840492
    // 合并客户的重复邮件动态
    public function actionMergeSameEmailCustomerTrail($clientId, $companyId)
    {
        $db = PgActiveRecord::getDbByClientId($clientId);
        if (empty($db)) return;

        $selectTrailListSql = "select trail_id,refer_id,type,customer_id,opportunity_id from tbl_dynamic_trail where enable_flag = 1 and client_id = {$clientId} and company_id = {$companyId} and type in (201,202,301,302) order by create_time desc";
        $trailList = $db->createCommand($selectTrailListSql)->queryAll();

        $map = [];
        foreach ($trailList as $trailItem)
        {
            $referId = $trailItem['refer_id'];
            $type = $trailItem['type'];
            $map[$referId][$type][] = $trailItem;

        }
        $deleteTrailIds = [];
        foreach ($map as $referId => $typeTrailItemList)
        {
            foreach ($typeTrailItemList as $trailItemList)
            {
                $count = count($trailItemList);
                if ($count < 2) continue;

                $referMap = [];
                foreach ($trailItemList as $trailItem)
                {
                    $itemCustomerId = $trailItem['customer_id'];
                    $itemOpportunityId = $trailItem['opportunity_id'];
                    $itemTrailId = $trailItem['trail_id'];

                    $referMap[$itemCustomerId][$itemOpportunityId][] = $itemTrailId;
                }
                foreach ($referMap as $itemCustomerId => &$opportunityTrailList)
                {
                    if (isset($opportunityTrailList[0]) && count($opportunityTrailList) > 1) {
                        $deleteTrailIds = array_merge($deleteTrailIds, $opportunityTrailList[0]);
                        unset($opportunityTrailList[0]);
                    }
                    foreach ($opportunityTrailList as $opportunityId => &$trailList)
                    {
                        unset($trailList[0]);
                        $deleteTrailIds = array_merge($deleteTrailIds, $trailList);
                    }
                }
            }
        }
        if (empty($deleteTrailIds)) return;
        $deleteTrailIdStr = implode(',', $deleteTrailIds);
        $deleteTrailSql = "update tbl_dynamic_trail set enable_flag = 0,delete_flag = 1 where client_id = {$clientId} and trail_id in ($deleteTrailIdStr) and company_id = {$companyId} and type in (201,202,301,302)";
        $db->createCommand($deleteTrailSql)->execute();

	    \common\library\trail\Helper::resetCompanyLastTrailId($clientId, $companyId);

	    \common\library\trail\comment\Helper::refreshCommentByTrail($clientId, $deleteTrailIds);

    }



    public function actionFixRunMoveToPublic($clientId,$startId = 0){

        if ($clientId) {
            $clientIds = [$clientId];
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }
        self::info("start fix");
        foreach ($clientIds as $clientId) {
            if(in_array($clientId,[2,3127])){
                continue;
            }
            if($startId && $startId > $clientId){
                self::info("skip {$clientId}");
                continue;
            }
            self::info("client_id:{$clientId}\r\n");
            \common\library\server\crontab\task\CustomerTask::runMoveToPublic($clientId);
        }
        self::info("finish");
    }

    /**
     * 同步小满客户的历史实施状态，默认全量
     * @param string $client_id 指定特定的客户ID
     * @param int $push_status 是否推送状态给阿里，默认不推送
     * @throws ProcessException
     */
    public function actionSyncImplementationStageToAli($client_id = '') {
        self::pushCompanyStage($client_id);
        self::pushLeadStage($client_id);
        self::pushOpportunityStage($client_id);
    }

    /**
     * 推送线索类阶段时间
     * @param string $client_id
     * @throws ProcessException
     */
    public static function pushLeadStage($client_id = '')
    {
        $opClientId = Constant::OKKI_OPERATION_DEPARTMENT_CLIENT;
        $db = PgActiveRecord::getDbByClientId($opClientId);

        $sql = "SELECT lead_id FROM tbl_lead WHERE client_id = {$opClientId}";
        if (!empty($client_id)) {
            $sql .= " AND external_field_data::jsonb->>'" . Constant::CUSTOMER_CLIENT_FIELD_ID . "' = '{$client_id}'";
        }
        $sql .= " ORDER BY lead_id DESC limit 1";
        $maxId = $db->createCommand($sql)->queryScalar();
        $currentId = 0;
        while ($currentId < $maxId)
        {
            $sourceSql = "SELECT lead_id,company_id,external_field_data FROM tbl_lead";
            $sourceSql .= " WHERE lead_id > $currentId AND client_id=$opClientId";
            if (!empty($client_id)) {
                $sourceSql .= " AND external_field_data::jsonb->>'" . Constant::CUSTOMER_CLIENT_FIELD_ID . "' = '{$client_id}'";
            }
            $sourceSql .= " order by lead_id asc limit 100";
            $result = $db->createCommand($sourceSql)->queryAll(true);

            $ids = array_column($result, 'lead_id');
            $ids = implode(',', $ids);
            $sql = "select lead_id,diff,create_time from tbl_lead_history where client_id = {$opClientId} AND lead_id IN ({$ids})";
            $historyList = $db->createCommand($sql)->queryAll();

            $newHistoryList = [];
            foreach ($historyList as $item) {
                $newHistoryList[$item['lead_id']][] = $item;
            }
            unset($historyList);

            $dataSet = [];
            foreach ($result as $elem)
            {
                $externalData = json_decode($elem['external_field_data'], true);
                $osClientId = $externalData[\common\library\alibaba\Constant::CUSTOMER_CLIENT_FIELD_ID] ?? 0;
                if (!$osClientId && $elem['company_id']) {
                    $fieldId = \common\library\alibaba\Constant::CUSTOMER_CLIENT_FIELD_ID;
                    $sourceSql = "select company_id,external_field_data::jsonb ->> '{$fieldId}' AS okki_client_id from tbl_company where client_id={$opClientId} and company_id = {$elem['company_id']}";
                    $okkiClientIds = \PgActiveRecord::getDbByClientId($opClientId)->createCommand($sourceSql)->queryAll(true);
                    $okkiClientIdMap = array_column($okkiClientIds, 'okki_client_id', 'company_id');
                    $osClientId = $okkiClientIdMap[$elem['company_id']] ?? 0;
                }
                if (!$osClientId) {
                    continue;
                }

                // 线索操作记录查数据
                $time = [];
                $historys = $newHistoryList[$elem['lead_id']];
                foreach ($historys as $history) {
                    $historyDiffs = json_decode($history['diff'], true);
                    foreach ($historyDiffs as $item) {
                        if ($item['id'] == Constant::CUSTOMER_FOLLOWUP_FIELD_ID && isset(Constant::CUSTOMER_IMPLEMENTATION_STATUS_LIST[$item['new']])) {
                            $time[Constant::CUSTOMER_IMPLEMENTATION_STATUS_LIST[$item['new']]][] = $history['create_time'];
                        }

                    }
                }
                if (empty($time)) {
                    continue;
                }
                $statusHistory =  self::processStageTime($time);
                $dataSet[] = [
                    'client_id'             => $opClientId,
                    'okki_client_id'        => $osClientId,
                    'company_id'            => !empty($elem['company_id']) ? $elem['company_id'] : '',
                    'status_history'        => $statusHistory,
                ];
            }

            echo "Get current lead: [" . $currentId. '/'. $maxId . "]data success." . PHP_EOL;
            $currentId = $elem['lead_id'];
            self::pushStageMsg($dataSet);
            usleep(50000);
        }
    }

    /**
     * 推送商机类阶段时间
     * @param string $client_id
     * @throws ProcessException
     */
    public static function pushOpportunityStage($client_id = '')
    {
        $opClientId = Constant::OKKI_OPERATION_DEPARTMENT_CLIENT;
        $db = PgActiveRecord::getDbByClientId($opClientId);

        $sql = "SELECT opportunity_id FROM tbl_opportunity WHERE client_id = {$opClientId}";
        if (!empty($client_id)) {
            $sql .= " AND external_field_data::jsonb->>'" . Constant::OPPORTUNITY_CLIENT_FIELD_ID . "' = '{$client_id}'";
        }
        $sql .= " ORDER BY opportunity_id DESC limit 1";
        $maxId = $db->createCommand($sql)->queryScalar();
        $currentId = 0;
        while ($currentId < $maxId)
        {
            $sourceSql = "SELECT opportunity_id,company_id,external_field_data FROM tbl_opportunity";
            $sourceSql .= " WHERE opportunity_id > $currentId AND client_id=$opClientId";
            if (!empty($client_id)) {
                $sourceSql .= " AND external_field_data::jsonb->>'" . Constant::OPPORTUNITY_CLIENT_FIELD_ID . "' = '{$client_id}'";
            }
            $sourceSql .= " order by opportunity_id asc limit 100";
            $result = $db->createCommand($sourceSql)->queryAll(true);

            $ids = array_column($result, 'opportunity_id');
            $ids = implode(',', $ids);
            $sql = "select opportunity_id,diff,create_time from tbl_opportunity_history where client_id = {$opClientId} AND opportunity_id IN ({$ids})";
            $historyList = $db->createCommand($sql)->queryAll();

            $newHistoryList = [];
            foreach ($historyList as $item) {
                $newHistoryList[$item['opportunity_id']][] = $item;
            }
            unset($historyList);

            $dataSet = [];
            foreach ($result as $elem)
            {
                $externalData = json_decode($elem['external_field_data'], true);
                $osClientId = $externalData[\common\library\alibaba\Constant::OPPORTUNITY_CLIENT_FIELD_ID] ?? 0;
                if (!$osClientId && $elem['company_id']) {
                    $fieldId = \common\library\alibaba\Constant::CUSTOMER_CLIENT_FIELD_ID;
                    $sourceSql = "select company_id,external_field_data::jsonb ->> '{$fieldId}' AS okki_client_id from tbl_company where client_id={$opClientId} and company_id = {$elem['company_id']}";
                    $okkiClientIds = \PgActiveRecord::getDbByClientId($opClientId)->createCommand($sourceSql)->queryAll(true);
                    $okkiClientIdMap = array_column($okkiClientIds, 'okki_client_id', 'company_id');
                    $osClientId = $okkiClientIdMap[$elem['company_id']] ?? 0;
                }
                if (!$osClientId) {
                    continue;
                }

                // 商机操作记录查数据
                $time = [];
                $historys = $newHistoryList[$elem['opportunity_id']];
                foreach ($historys as $history) {
                    $historyDiffs = json_decode($history['diff'], true);
                    foreach ($historyDiffs as $item) {
                        if ($item['id'] == 'stage' && isset(Constant::CUSTOMER_IMPLEMENTATION_STATUS_LIST[$item['new']])) {
                            $time[Constant::CUSTOMER_IMPLEMENTATION_STATUS_LIST[$item['new']]][] = $history['create_time'];
                        }
                    }
                }
                if (empty($time)) {
                    continue;
                }
                $statusHistory =  self::processStageTime($time);
                $dataSet[] = [
                    'client_id'             => $opClientId,
                    'okki_client_id'        => $osClientId,
                    'company_id'            => !empty($elem['company_id']) ? $elem['company_id'] : '',
                    'status_history'        => $statusHistory,
                ];
            }

            echo "Get current opportunity: [" . $currentId. '/'. $maxId . "]data success." . PHP_EOL;
            $currentId = $elem['opportunity_id'];
            self::pushStageMsg($dataSet);
            usleep(50000);
        }
    }


    /**
     * 推送客户类阶段时间
     * @param string $client_id
     * @throws ProcessException
     */
    public static function pushCompanyStage($client_id = '')
    {
        $opClientId = Constant::OKKI_OPERATION_DEPARTMENT_CLIENT;
        $db = PgActiveRecord::getDbByClientId($opClientId);

        $sql = "SELECT company_id FROM tbl_company WHERE client_id = {$opClientId}";
        if (!empty($client_id)) {
            $sql .= " AND external_field_data::jsonb->>'" . Constant::CUSTOMER_CLIENT_FIELD_ID . "' = '{$client_id}'";
        }
        $sql .= " ORDER BY company_id DESC limit 1";
        $maxId = $db->createCommand($sql)->queryScalar();
        $currentId = 0;
        while ($currentId < $maxId)
        {
            $sourceSql = "SELECT company_id,external_field_data FROM tbl_company";
            $sourceSql .= " WHERE company_id > $currentId AND client_id=$opClientId";
            if (!empty($client_id)) {
                $sourceSql .= " AND external_field_data::jsonb->>'" . Constant::CUSTOMER_CLIENT_FIELD_ID . "' = '{$client_id}'";
            }
            $sourceSql .= " order by company_id asc limit 100";
            $result = $db->createCommand($sourceSql)->queryAll(true);

            $ids = array_column($result, 'company_id');
            $ids = implode(',', $ids);
            $sql = "select company_id,diff,create_time from tbl_company_history where client_id = {$opClientId} AND company_id IN ({$ids})";
            $historyList = $db->createCommand($sql)->queryAll();

            $newHistoryList = [];
            foreach ($historyList as $item) {
                $newHistoryList[$item['company_id']][] = $item;
            }
            unset($historyList);

            $dataSet = [];
            foreach ($result as $elem)
            {
                $externalData = json_decode($elem['external_field_data'], true);
                $osClientId = $externalData[\common\library\alibaba\Constant::CUSTOMER_CLIENT_FIELD_ID] ?? 0;
                if (!$osClientId) {
                    continue;
                }

                // 客户操作记录查数据
                $time = [];
                $historys = $newHistoryList[$elem['company_id']];
                foreach ($historys as $history) {
                    $historyDiffs = json_decode($history['diff'], true);
                    foreach ($historyDiffs as $item) {
                        if ($item['id'] == 'trail_status' && in_array(intval($item['new']), [19854895179])) {
                            $time['done'][] = $history['create_time'];
                        }

                        if ($item['id'] == Constant::COMPANY_FOLLOWUP_FIELD_ID && isset(Constant::CUSTOMER_IMPLEMENTATION_STATUS_LIST[$item['new']])) {
                            $time[Constant::CUSTOMER_IMPLEMENTATION_STATUS_LIST[$item['new']]][] = $history['create_time'];
                        }
                    }
                }
                if (empty($time)) {
                    continue;
                }
                $statusHistory =  self::processStageTime($time);
                $dataSet[] = [
                    'client_id'      => $opClientId,
                    'okki_client_id' => $osClientId,
                    'company_id'     => !empty($elem['company_id']) ? $elem['company_id'] : '',
                    'status_history' => $statusHistory,
                ];
            }

            echo "Get current company: [" . $currentId. '/'. $maxId . "]data success." . PHP_EOL;
            $currentId = $elem['company_id'];
            self::pushStageMsg($dataSet);
            usleep(50000);
        }
    }


    public static function processStageTime($time) {
        return [
            'not_followup'     => !empty($time['not_followup']) ? min($time['not_followup']) : '',
            'contact_failure'  => !empty($time['contact_failure']) ? min($time['contact_failure']) : '',
            'not_do'           => !empty($time['not_do']) ? min($time['not_do']) : '',
            'wait_for_confirm' => !empty($time['wait_for_confirm']) ? min($time['wait_for_confirm']) : '',
            'appointment'      => !empty($time['appointment']) ? min($time['appointment']) : '',
            'configure'        => !empty($time['configure']) ? min($time['configure']) : '',
            'training'         => !empty($time['training']) ? min($time['training']) : '',
            'done'             => !empty($time['done']) ? min($time['done']) : '',
        ];
    }

    public static function pushStageMsg($dataSet)
    {
        if (!empty($dataSet))
        {
            $data = [
                'msg_type' => 'ali_implementation_stage',
                'data' => $dataSet,
            ];
            $value = json_encode($data);
            $queueKey = \common\library\alibaba\push\pushDispatcher::COMPANY_TRAIL_STATUS_QUEUE_LIST_KEY;
            $redis = \RedisService::getInstance('redis_queue');
            $res = $redis->lpush($queueKey, [$value]);
        }
    }
    /**
     * @param $clientId
     * @throws CDbException
     * @throws CException
     * @throws ProcessException
     * 修复openApi接口actionPushCompanyAndCustomers bug产生的重复邮箱联系人
     */
    public function actionRemoveDuplicateEmailCustomer($clientId)
    {
        //$clientId = 25938;
        $pgDb = PgActiveRecord::getDbByClientId($clientId);

        $sql = "SELECT company_id,email from tbl_customer WHERE client_id={$clientId} and email != '' and is_archive=1 GROUP BY company_id,email HAVING count(email)>1 order by count(email) desc;";
        $data =  $pgDb->createCommand($sql)->queryAll();

        foreach ($data as $item)
        {
            $companyId = $item['company_id'];
            $email = $item['email'];
            $sql = "select customer_id from tbl_customer where client_id={$clientId} and company_id={$companyId} and email = '{$email}' order by customer_id asc";
            $customerIds = $pgDb->createCommand($sql)->queryColumn();

            if (count($customerIds) <= 1)
                continue;

            self::info('clientId:'.$clientId.'customerId:'.implode(',',$customerIds));

            $firstId = reset($customerIds);

            $update = "update tbl_customer set is_archive=0 where client_id={$clientId} and company_id={$companyId} and email = '{$email}' and customer_id != {$firstId} ";
            $pgDb->createCommand($update)->execute();
        }
    }

    public function actionFixExportTask()
    {
        $setList = \common\library\account\service\DbService::getDbList(DbSet::TYPE_MYSQL);
        $count = 0;
        foreach ($setList as $set) {
            $db = ProjectActiveRecord::getDbByDbSet($set);
            $sql = "SELECT * FROM tbl_customer_export WHERE `status` = 2 AND type = 1 and create_time >= '2021-06-17 12:00:00' and create_time < '2021-06-17 17:00:00'";
            $list = $db->createCommand($sql)->queryAll();
            print_r($list);
            $log = '/tmp/customer_export.log';
            foreach ($list as $item) {
                $taskId = $item['task_id'];
                $userId = $item['user_id'];
                $type = $item['type'];
                $params = $item['param'];
                echo "task $taskId type: $type client " . $item['client_id'] . " create time " . $item['create_time'] . " params: $params \n";
                $count++;
//                \common\library\CommandRunner::run(
//                    'export',
//                    'export',
//                    [
//                        'operator_id' => $userId,
//                        'task_id' => $taskId,
//                        'type' => $type,
//                        'params' => "'{$params}'",
//                    ],
//                    $log
//                );
                echo "start task $taskId type: $type client " . $item['client_id'] . " create time " . $item['create_time'] . " params: $params \n";
            }
        }
    }

    public function actionFixClientExportTask()
    {
        $clientId = 6688;
        $db = ProjectActiveRecord::getDbByClientId($clientId);
        $sql = "SELECT * FROM tbl_customer_export WHERE `status` = 2 AND type = 1 and create_time >= '2021-06-17 12:00:00' and create_time < '2021-06-17 17:00:00'";
        $result = $db->createCommand($sql)->queryAll();
        print_r($result);
        $log = '/tmp/customer_export.log';
        foreach ($result as $item)
        {
            $taskId = $item['task_id'];
            $userId = $item['user_id'];
            $type = $item['type'];
            $params = $item['param'];
            \common\library\CommandRunner::run(
                'export',
                'export',
                [
                    'operator_id' => $userId,
                    'task_id' => $taskId,
                    'type' => $type,
                    'params' => "'{$params}'",
                ],
                $log
            );
        }
    }

    public function actionFixImportTag($clientId = 0, $dryRun = 1)
    {
        if (!$clientId) {
            $clientId = '593,1793,2532,2984,2992,3092,3489,5537,11279,11813,13985,16371,18656,19453,19690,19879,22049,23636,23946,24557,26123,26309,26561,27715,28245,29948,30994,31514,32583,36221,36686,36826,38046,38120,38131,39329,39702,40215,40264,40312,41530,41801,42213,42719,42998,44249,44696,44720,44995,45201,45679,45941,46172,46966,47048,47289,47668,47811,47992,48175,48631,48937,49188,49695,49752,50135,51153,51750,51755,53063,53424,53487,53653,53711,54026,54306,55615,55699,56376,56670,56861,56884,57136,57249,57553';
        }
        $clientIds = explode(',', $clientId);
        foreach ($clientIds as $clientId) {
            $db = ProjectActiveRecord::getDbByClientId($clientId);
            ProjectActiveRecord::setConnection($db);
            if ($clientId % 10 == 1) {
                $timeSql = "AND  update_time >= '2021-08-09 12:00:00'";
            } elseif (in_array($clientId % 10, [2,3])) {
                $timeSql = "AND  update_time >= '2021-08-10 12:00:00'";
            } else {
                $timeSql = "AND  update_time >= '2021-08-11 12:00:00'";
            }

            $sql = "SELECT * FROM tbl_customer_import WHERE client_id = {$clientId}  $timeSql and tag_list != '[]'";
            $importList = $db->createCommand($sql)->queryAll();
            $file = '/tmp/customer_tag_task.csv';
//            $fp = fopen($file, 'w');
//            fputcsv($fp, array_keys(current($importList)));
            foreach ($importList as $importInfo) {
//                $insert = fputcsv($fp, $importInfo);
                echo $importInfo['client_id'] . " time: " . $importInfo['create_time'] . "\n";
                var_dump($importInfo);
            }
//            fclose($fp);

            ProjectActiveRecord::releaseDbByClientId($clientId);
        }
    }

    public function actionCreateExampleData($clientId, $dryRun = 1, $start = 0)
    {
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } else {
//            $clientIds = array_column($this->getClientList(0, true), 'client_id');
            $sql = "SELECT client_id FROM `tbl_client` WHERE `valid_from` >= '2021-08-19' ORDER BY `client_id` DESC LIMIT 0,1000";
            $clientIds = Yii::app()->account_base_db->createCommand($sql)->queryColumn();
        }


        foreach ($clientIds as $clientId) {
            if ($clientId < $start) {
                continue;
            }
            $db = ProjectActiveRecord::getDbByClientId($clientId);
            $pgDb = PgActiveRecord::getDbByClientId($clientId);
            echo "start for $clientId \n";
            $adminUserId = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();
            if (!$adminUserId) {
                echo "empty user for $clientId \n";
                continue;
            }
            \User::setLoginUserById($adminUserId);
            if (!$db || !$pgDb) {
                continue;
            }
            ProjectActiveRecord::setConnection($db);
            PgActiveRecord::setConnection($pgDb);
            $client = \common\library\account\Client::getClient($clientId);
            $userIds = \common\library\account\Helper::getActivationUserIds($clientId);
            if ($dryRun) {
                $needCreate = (new \common\library\example\CompanyExample($clientId))->needCreate();
                if ($needCreate) {
                    echo "create for $clientId \n";
                } else {
                    echo "skip for $clientId \n";
                }
            } else {
                $companyIds = (new \common\library\example\CompanyExample($clientId))->create($userIds);
                if ($companyIds) {
                    echo "create for $clientId \n";
                } else {
                    echo "skip for $clientId \n";
                }
            }
            ProjectActiveRecord::releaseDbByClientId($clientId);
            PgActiveRecord::releaseDbByClientId($clientId);
            \common\library\account\Client::cleanCacheMap($clientId);
        }
    }

    /**
     * @param $clientId
     * @param int $dryRun
     * @throws CDbException
     * @throws CException
     * @throws ProcessException
     * 修复tell_full字段存在非纯数字的情况
     * https://www.tapd.cn/********/bugtrace/bugs/view?bug_id=11********001063570&url_cache_key=url_cache_key_bug_list_23c7ec0cf7e0fe16938ca17a84882964
     */
    public function actionFixTel($clientId,$dryRun=1)
    {
        $db = PgActiveRecord::getDbByClientId($clientId);

        $maxId = $db->createCommand('select company_id from tbl_company where client_id='.$clientId.' order by company_id desc limit 1')->queryScalar();

        $currId = 0;
        $updateSqlArray = [];
        $sql = 'select company_id,tel_full from tbl_company where  client_id='.$clientId;
        self::info("begin company fix tel client_id:$clientId maxId:{$maxId} currId: {$currId}");
        while ( $currId < $maxId )
        {
            $companyList = $db->createCommand($sql . ' and company_id >' . $currId . ' and is_archive =1 order by company_id asc limit 200')->queryAll(true);
            if (empty($companyList))
                break;

            foreach ($companyList as $company)
            {
                $currId = $company['company_id'];
                if (empty($company['tel_full']))
                    continue;

                $tellFull = \Util::numericString($company['tel_full']);
                if ($tellFull == $company['tel_full'])
                    continue;

                $findTel = Util::escapeDoubleQuoteSql($company['tel_full']);
                $updateSqlArray[] = "update tbl_company set tel_full = '{$tellFull}' where company_id={$company['company_id']} and tel_full='{$findTel}'";
            }

            self::info("company fix tell_full clientId:$clientId  currId：$currId count:".count($companyList));
        }

        $sqlArray = array_chunk($updateSqlArray,100);
        foreach ($sqlArray as $sqlArr)
        {
            //var_dump($sqlArr);
            $updateSql = implode(';', $sqlArr);
            self::info("company fix tell_full clientId:$clientId sql: $updateSql");
            if ($dryRun == 0)
            {
                $db->createCommand($updateSql)->execute();
            }
        }
    }

    /**
     * 修复Company 中 name里面混了不可见的&nbsp字符，替换为正常空格
     * https://www.tapd.cn/********/bugtrace/bugs/view?bug_id=11********001065079
     * @param $clientId
     * @param $dryRun
     * @return void
     * @throws ProcessException
     */
    public function actionReplaceSpace($clientId = '', $dryRun=1)
    {
        $userId = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();
        if(empty($userId))
        {
            return;
        }
        User::setLoginUserById($userId);
        $db = PgActiveRecord::getDbByClientId($clientId);

        $maxId = $db->createCommand('select company_id from tbl_company where client_id='.$clientId.' order by company_id desc limit 1')->queryScalar();

        $currId = 0;
        $updateSqlArray = [];
        $sql = 'select company_id,name from tbl_company where  client_id='.$clientId;
        self::info("begin company replace name client_id:$clientId maxId:{$maxId} currId: {$currId}");
        while ( $currId < $maxId )
        {
            $companyList = $db->createCommand($sql . ' and company_id >' . $currId . ' and is_archive =1 order by company_id asc limit 200')->queryAll(true);
            if (empty($companyList))
                break;

            foreach ($companyList as $company)
            {
                $currId = $company['company_id'];
                if (empty($company['name']))
                    continue;

                $companyName = htmlentities($company['name']);
                $pos = strpos($companyName, '&nbsp;');

                if ($pos !== false) {
                    $newCompanyName = str_replace('&nbsp;', ' ', $companyName);
                    $newCompanyName = html_entity_decode($newCompanyName);
                    $newCompanyName = Util::escapeDoubleQuoteSql($newCompanyName);
                    $updateSqlArray[] = "update tbl_company set name = '{$newCompanyName}' where company_id={$company['company_id']}  and name='{$company['name']}'";
                }
            }

            self::info("company replace name clientId:$clientId  currId：$currId count:".count($companyList));
        }

        $sqlArray = array_chunk($updateSqlArray,100);
        foreach ($sqlArray as $sqlArr)
        {
            $updateSql = implode(';', $sqlArr);
            self::info("company replace name clientId:$clientId sql: $updateSql");
            if ($dryRun == 0)
            {
                $db->createCommand($updateSql)->execute();
            }
        }
    }

    public function actionRecoverMoveToPublic($clientId, $date, $dryRun = 1)
    {
        $adminUserId = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();
        \User::setLoginUserById($adminUserId);
        $sql = "select company_id, diff from tbl_company_history where client_id = $clientId and type = " . CompanyHistoryPg::TYPE_SYS_MOVE_TO_PUBLIC . " and company_id in (
                    select company_id from tbl_company where client_id = $clientId and user_id = '{}' and is_archive = 1 and public_time between '{$date} 01:00:00' and '{$date} 06:00:00'
                    ) and update_user = 0 and create_time between '{$date} 01:00:00' and '{$date} 06:00:00';";

        $db = PgActiveRecord::getDbByClientId($clientId);
        $historyList = $db->createCommand($sql)->queryAll();
        $holdMap = [];
        foreach ($historyList as $history) {
            foreach (json_decode($history['diff'], true) as $item) {
                if ($item['id'] == 'user_id' && empty($item['new']) && !empty($item['old'])) {
                    foreach ($item['old'] as $originUserId) {
                        $holdMap[$originUserId][] = $history['company_id'];
                    }
                }
            }
        }

        foreach ($holdMap as $userId => $companyIds) {
            if (!empty($companyIds)) {
                $op = (new \common\library\customer\CompanyBatchOperator($adminUserId));
                $op->setParams(['company_ids' => $companyIds]);
                if (!$dryRun) {
                    $res = $op->hold(0, $userId);
                }
                $this->log("finish", [
                    'userId' => $userId,
                    'count' => count($companyIds),
                ]);
            }
        }

    }

    public function actionRecoverWorkflowMoveToPublic($clientId, $date, $workflowId, $dryRun = 1)
    {
        $adminUserId = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();
        \User::setLoginUserById($adminUserId);
        $sql = "select company_id, diff from tbl_company_history where client_id = $clientId and type = " . CompanyHistoryPg::TYPE_MOVE_TO_PUBLIC . " and company_id in (
                    select company_id from tbl_company where client_id = $clientId and user_id = '{}' and is_archive = 1 and public_time between '{$date} 01:00:00' and '{$date} 06:00:00'
                    ) and update_user = 0 and update_type = 3 and update_refer = {$workflowId} and create_time between '{$date} 01:00:00' and '{$date} 06:00:00';";

        $db = PgActiveRecord::getDbByClientId($clientId);
        $historyList = $db->createCommand($sql)->queryAll();
        $holdMap = [];
        foreach ($historyList as $history) {
            foreach (json_decode($history['diff'], true) as $item) {
                if ($item['id'] == 'user_id' && empty($item['new']) && !empty($item['old'])) {
                    foreach ($item['old'] as $originUserId) {
                        $holdMap[$originUserId][] = $history['company_id'];
                    }
                }
            }
        }

        foreach ($holdMap as $userId => $companyIds) {
            if (!empty($companyIds)) {
                if (!$dryRun) {
                    $op = (new \common\library\customer\CompanyBatchOperator($adminUserId));
                    $chucked = array_chunk($companyIds, 2000);
                    foreach ($chucked as $chuckCompanyIds) {
                        $op->setParams(['company_ids' => $chuckCompanyIds]);
                        $res = $op->hold(0, $userId);
                    }
                }

                $this->log("finish", [
                    'userId' => $userId,
                    'count' => count($companyIds),
                ]);
            }
        }

    }

    public function actionRunCompanyMoveToPublic($clientId, $dryRun = 1)
    {
        $clientIds = explode(',', $clientId);

        foreach ($clientIds as $clientId) {
            $job = new \common\library\queue_v2\job\crontab\CompanyMoveToPublic();
            $job->_debug = $dryRun;
            $res = $job->process($clientId);
            $this->log("move to public for $clientId", [
                'res' => $res
            ]);
        }
    }

    public function actionFixPublicRemind($taskId, $clientId = 0, $dryRun = 1)
    {
        $forClientIds = $clientId ? explode(',', $clientId) : [];
        $db = Yii::app()->prometheus_db;
        $params = $db->createCommand("select dispatch_params from tbl_queue_crontab_task_record_202203 where task_id = {$taskId} and queue_time > '2022-03-17 00:00:00';")->queryColumn();
        foreach ($params as $param) {
            $clientIds = json_decode($param, true)['ids'];
            $firstClient = current($clientIds);
            $firstDb = ProjectActiveRecord::getDbByClientId($firstClient);
            foreach ($clientIds as $clientId) {
                if ($forClientIds && !in_array($clientId, $forClientIds)) {
                    continue;
                }
                if ($clientId == $firstClient) {
                    $this->log("fist is $clientId, continue");
                    continue;
                }
                $clientDb = ProjectActiveRecord::getDbByClientId($clientId);
                ProjectActiveRecord::setConnection($clientDb);

                $userIds = \common\library\account\Helper::getActivationUserIds($clientId);
                $userIdSql = implode(',', $userIds);
                $nowDate = date('Y-m-d');
                $exist = $clientDb->createCommand("select user_id from  tbl_customer_public_remind  where user_id in ({$userIdSql}) and client_id={$clientId} and `date`='{$nowDate}' and day_number = 0 limit 1")->queryScalar();
                if ($exist) {
                    $this->log("skip for $clientId");
                    continue;
                }

                $remindDataList = $firstDb->createCommand("select * from tbl_customer_public_remind where user_id in ({$userIdSql}) and  client_id = {$clientId} and day_number = 0 and `date` = '2022-03-17'")->queryAll();
                if ($remindDataList) {
                    $this->log("data for client $clientId", [
                        'data' => $remindDataList
                    ]);
                    if (!$dryRun) {
                        foreach ($remindDataList as $item) {
                            CustomerPublicRemind::updateDataByPK($item['user_id'], $clientId, $item['date'], json_decode($item['data'], true), 0);
                        }
                        $this->log("insert succeed client $clientId");
                    }
                } else {
                    $this->log("empty remind data, skip for $clientId");
                }
            }
        }
    }

    private function log($string, array $context = [])
    {
        echo $string . " context:" . json_encode($context) . "\n";
        LogUtil::info($string, $context);
    }

    public function actionFixCompanyMoveToPublic($clientId = 28224, $dryRun = 1)
    {
        $moveToPublicCompanyIds = [];
        $removeUserIds = [];
        $ignoreCompanyIds = [];
        $moveToPublicUserCompanyIdsMap = [];
        $noticeUserCompanyCountMap = [];
        $willPublicArray = [];
        $historyMap = [];

        $adminUserId = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();
        $user = User::getUserObject($adminUserId);
        User::setLoginUserById($user->getUserId());

        $sql = "select company_id,diff from (
      select company_id,
             diff,
             type,
             row_number() over (partition by company_id order by create_time desc) as row_number
      from tbl_company_history where client_id = $clientId and company_id in (
          select company_id from tbl_company where client_id = $clientId and public_time > '2022-04-15 00:12:00' and public_time < '2022-04-15 00:13:00'
      ) and type in (18,28,29,30,1) and diff::text like '%user_id%'
      ) as hs where row_number  = 1;";

        $db = PgActiveRecord::getDbByClientId($clientId);
        $historyList = $db->createCommand($sql)->queryAll();
        $companyList = [];
        foreach ($historyList as $history) {
            foreach (json_decode($history['diff'], true) as $historyItem) {
                if ($historyItem['id'] == 'user_id') {
                    $companyList[$history['company_id']] = [
                        'user_id' => $historyItem['new'],
                        'company_id' => $history['company_id'],
                    ];
                    break;
                }
            }
        }

        foreach ($companyList as $elem) {
            $companyId = $elem['company_id'];
            $oldUserIds = $elem['user_id'];

            $removeUserIdList = $elem['user_id'];

            foreach ($removeUserIdList as $userId) {
                $moveToPublicUserCompanyIdsMap[$userId][$companyId] = $companyId;
            }

            $moveToPublicCompanyIds[] = $companyId;
            $removeUserIds = array_merge($oldUserIds, $removeUserIds);

            $historyMap[$companyId] = [[
                'id' => 'user_id',
                'base' => 1,
                'new' => [],
                'old' => $oldUserIds
            ]];
        }

        if (!$dryRun && !empty($moveToPublicCompanyIds)) {
            foreach ($moveToPublicUserCompanyIdsMap as $removeUserId => $removeUserCompanyIds) {
                \common\library\customer\public_record\Helper::batchSavePublicRecord($clientId, $removeUserId, $removeUserCompanyIds);
            }

            $history = new \common\library\history\customer\BatchCompanyBuilder();
            $moveHistoryDataList = [];
            foreach ($historyMap as $companyId => $diff) {
                if (empty($diff)) {
                    continue;
                }
                $moveHistoryDataList[] = [
                    'client_id' => $clientId,
                    'type' => CompanyHistoryPg::TYPE_SYS_MOVE_TO_PUBLIC,
                    'company_id' => $companyId,
                    'customer_id' => 0,
                    'update_user' => 0,
                    'create_time' => "'2022-04-15 00:12:22'",
                    'update_time' => "'2022-04-15 00:12:22'",
                    'diff' => \common\library\util\PgsqlUtil::formatBson($diff),
                    'update_type' => 1,
                    'update_refer' => 0,
                ];
            }
            $history->batchInsert($clientId, $moveHistoryDataList);

            LogUtil::info('clientId:' . $clientId . ' finish move company: ' . implode(',', $moveToPublicCompanyIds));
        }

        if (!$dryRun && !empty($moveToPublicCompanyIds)) {
            (new CustomerSync($clientId))->setFindCompanyId($moveToPublicCompanyIds)->sync();

            SearchQueueService::pushCompanyQueue($adminUserId, $clientId, $moveToPublicCompanyIds, Constants::SEARCH_INDEX_TYPE_UPDATE);

            //补上自动移入公海的版本号
            $companyVersion = new \common\library\version\CompanyVersion($clientId, $moveToPublicCompanyIds);
            $companyVersion->setType(\common\library\version\Constant::COMPANY_MODULE_EDIT);
            $companyVersion->add();
        }

        if (!empty($ignoreCompanyIds)) {
            LogUtil::info('clientId:' . $clientId . ' ignore company:' . implode(',', $ignoreCompanyIds));
        }

        !$dryRun && \common\library\statistics\CompanyHelper::batchIncCompanyKeyCount($clientId, ['move_to_public_company_count', 'auto_move_to_public_company_count'], $moveToPublicUserCompanyIdsMap);

        // 删除相关[跟进客户]任务
//        if (!$dryRun && $moveToPublicCompanyIds) {
//            \common\library\task\Helper::deleteTask($clientId, $moveToPublicCompanyIds, $removeUserIds);
//        }

        $res = compact('moveToPublicCompanyIds', 'noticeUserCompanyCountMap', 'willPublicArray', 'historyMap');
        LogUtil::info('finish move company task', [
            'client_id' => $clientId,
            'res' => $res
        ]);
    }


    public function actionFixCompanyTransformByLead($clientId, $grey = 1, $greyNum = null, $dryRun = 1)
    {
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey && !is_null($greyNum)) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        foreach ($clientIds as $clientId) {
            if (in_array($clientId, [3,7,10,2106,6688])) {
                continue;
            }

            $pgDb = PgActiveRecord::getDbByClientId($clientId);

            if (!$pgDb) {
                $this->log("not db for $clientId, skip");
                continue;
            }

            $this->log("start for $clientId");
            $timeMapField = [
                'edm_time' => 'edm_time',
                'send_mail_time' => 'send_mail_time',
                'receive_mail_time' => 'receive_mail_time',
                'mail_time' => 'mail_time',
                'next_follow_up_time' => 'next_follow_up_time',
            ];
            $sql = "select company_id,main_lead_id,edm_time,send_mail_time,receive_mail_time,mail_time,next_follow_up_time from tbl_company where client_id = {$clientId} and is_archive = 1 and main_lead_id > 0";
            $companyDataList = $pgDb->createCommand($sql)->queryAll();
            $this->log("company for $clientId count:" . count($companyDataList));
            $updateData = [];
            if (count($companyDataList)) {
                $leadIds = implode(',', array_column($companyDataList, 'main_lead_id'));
                $sql = "select lead_id,edm_time,send_mail_time,receive_mail_time,mail_time,next_follow_up_time from tbl_lead where lead_id in ({$leadIds})";
                $leadDataList = $pgDb->createCommand($sql)->queryAll();
                $leadDataList = array_column($leadDataList, null, 'lead_id');
                foreach ($companyDataList as $companyDatum) {
                    if ($leadData = $leadDataList[$companyDatum['main_lead_id']] ?? []) {
                        foreach ($timeMapField as $leadTimeField => $companyTimeField) {
                            if (!empty($leadData[$leadTimeField]) && !in_array($leadData[$leadTimeField], ['1970-01-01', '1970-01-01 00:00:00', '1970-01-01 08:00:00', '1970-01-01 00:00:01']) && $companyDatum[$leadTimeField] < $leadData[$leadTimeField]) {
                                $updateData[$companyDatum['company_id']][] = "{$companyTimeField}='{$leadData[$leadTimeField]}'";
                            }
                        }
                    }
                }
            }

            $updateSql = [];
            foreach ($updateData as $companyId => $updateDatum) {
                $set = implode(',', $updateDatum);
                $updateSql[$companyId] = "update tbl_company set {$set} where company_id = {$companyId}";
            }
            $this->log("update for client: {$clientId}, count:" . count($updateSql), $updateSql);

            if (!$dryRun) {
                $updateSqlFull = implode(';', $updateSql);
                $count  = $pgDb->createCommand($updateSqlFull)->execute();
                $companyIds = array_keys($updateSql);
                (new \common\library\swarm\SwarmService($clientId))->refreshByRefer($companyIds, ['edm_time','send_mail_time','receive_mail_time','mail_time','next_follow_up_time']);
                $this->log("succeed update count: $count for {$clientId}");
            }
        }

    }

    public function actionFixMainCustomer($clientId, $grey = 1, $greyNum = null, $dryRun = 1)
    {
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey && !is_null($greyNum)) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        foreach ($clientIds as $clientId) {
            $db = PgActiveRecord::getDbByClientId($clientId);
            if (!$db) {
                $this->log("skip for $clientId");
                continue;
            }
            $updateList = [];
            $companyIds = $db->createCommand("select company_id from tbl_company where client_id = {$clientId} and is_archive=1 and main_customer = 0 limit 1000000")->queryColumn();
            if (count($companyIds)) {
                $mainCustomerList =  $db->createCommand("select company_id, customer_id from tbl_customer where client_id = {$clientId} and is_archive=1 and main_customer_flag = 1 and company_id in (" . implode(',', $companyIds) . ")")->queryAll();
                $this->log("prepare fix for $clientId, count:" . count($mainCustomerList));
                foreach ($mainCustomerList as $item) {
                    $mainCustomerId = $item['customer_id'];
                    $updateCompanyId = $item['company_id'];
                    $updateList[] = "update tbl_company set main_customer={$mainCustomerId} where company_id = {$updateCompanyId} and main_customer = 0";
                }
                if (!$dryRun && $updateList) {
                    $updated = $db->createCommand(implode(';', $updateList))->execute();
                    $this->log("fix count:" . $updated);
                }
            } else {
                $this->log("no error for $clientId");
            }
        }

    }


    /**
     * 通过聊天记录统计sns每天活跃情况
     *
     * @param int $clientId
     * @param $snsType
     * @param $startDate
     * @param $endDate
     * @param $dryRun
     * @return void
     * @throws ProcessException
     */
    public function actionCustomerContactExport(string $startDate,  string $endDate, int $clientId = 0, string $snsType = 'whatsapp', $dryRun = null): void
    {

        $sns_type_arr = ['facebook', 'facebook_page', 'twitter', 'linkedin', 'whatsapp'];
        if(!in_array($snsType, $sns_type_arr)){
            LogUtil::info("sns_type error");
            return;
        }
        if(empty($startDate) || empty(strtotime($startDate))){
            LogUtil::info("startDate error");
            return;
        }
        if(empty($endDate) || empty(strtotime($endDate))){
            LogUtil::info("endDate error");
            return;
        }
        $startDate = date("Y-m-d",strtotime($startDate)).' 00:00:00';
        $endDate = date("Y-m-d",strtotime($endDate)).' 23:59:59';

        if ($clientId) {

            $clientIds = explode(',', $clientId);
        } else {
            if($snsType == 'facebook_page'){
                $sql = 'select distinct client_id from tbl_facebook_page';

                $list = FacebookPage::model()->getDbConnection()->createCommand($sql)->queryAll();
                $clientIds = array_column($list, 'client_id');
            }else{
                $clientIds = array_column($this->getClientList(), 'client_id');
            }
        }

        $csv_data = [];
        foreach ($clientIds as $clientId) {

            self::info('clientID: ' . $clientId . ', start');

            $db = PgActiveRecord::getDbByClientId($clientId);

            if (empty($db)) {

                self::info('empty db continue' . $clientId);
                continue;
            }

            try {

                $sql = "SELECT DISTINCT user_id FROM tbl_user_customer_contact WHERE client_id={$clientId};";

                $contact = $db->createCommand($sql)->queryAll();

                self::log('contact count: ' . count($contact));

                if(!empty($contact)){
                    foreach ($contact as $item) {

                        $sql = "
SELECT to_char(send_time, 'YYYY-MM-DD') AS day, COUNT(user_contact_id) AS message_count , COUNT(DISTINCT user_contact_id) AS user_count
FROM tbl_user_customer_contact_message
WHERE client_id ={$clientId}
  AND user_contact_id IN (SELECT user_contact_id FROM tbl_user_customer_contact WHERE client_id={$clientId} AND user_id={$item['user_id']} AND sns_type='{$snsType}')
  AND send_time >= '{$startDate}'
  AND send_time <= '{$endDate}'
GROUP BY day
                    ";

                        if (!$dryRun){
                            $list = $db->createCommand($sql)->queryAll();

                            self::log('message count: ' . count($list));

                            if(!empty($list)){
                                foreach ($list as $value){
                                    $data = [];
                                    $data['client_id'] = $clientId;
                                    $data['user_id'] = $item['user_id'];
                                    $data['day'] = $value['day'];
                                    $data['user_count'] = $value['user_count'];
                                    $data['message_count'] = $value['message_count'];

                                    $csv_data[] = $data;
                                }
                            }
                        }else{
                            self::log('message count dryRun sql: ' . $sql);
                        }
                    }
                }

                self::info('clientID: ' . $clientId . ', end');

            } catch (Throwable $throwable) {

                self::info('message: ' . $throwable->getMessage());
                self::info('trace: ' . $throwable->getTraceAsString());
            }
        }

        if(!empty($csv_data)){
            $resultPath = '/tmp/customer_contact_export.csv';
            $fp = fopen($resultPath, 'w');
            fputcsv($fp, ['client_id', 'user_id', '日期', '联系人数', '消息条数']);

            foreach ($csv_data as $csv) {
                self::info('save csv: ' . json_encode($csv));
                fputcsv($fp, [$csv['client_id'], $csv['user_id'], $csv['day'], $csv['user_count'], $csv['message_count']]);
            }
            fclose($fp);
        }

    }

    /**
     * @param $clientId
     * @param $userId
     * @param int $dryRun
     * @throws CDbException
     * @throws CException
     * @throws ProcessException
     * 修复导入客户没选择标签时，清空了用户标签tag->>userId和公司标签client_tag_list的问题
     * https://www.tapd.cn/********/bugtrace/bugs/view/11********001074773
     */
    public function actionFixClientUserTag($clientId = 18668, $userId = 55261447, $dryRun=1)
    {
        User::setLoginUserById($userId);
        $db = PgActiveRecord::getDbByClientId($clientId);

        $recoveryDb = \ProjectActiveRecord::getDbByDbSet([
            'set_id' => 9999999,
            'type' => DbSet::TYPE_PGSQL,
            'host' =>'pc-bp15a5hu63f06w4bq.pg.polardb.rds.aliyuncs.com',
            'name' =>'v5_client',
            'port' => '1921',
            'user' => 'crmphp',
            'password' => 'ou5pxhJTRxvqWcyr8cYBMXKqGMZ6eg',
            'schema_name' => 'v5_client_127'
        ]);

        $maxId = $db->createCommand("select company_id from tbl_company where client_id={$clientId} and is_archive=1 and client_tag_list = '{}' and tag::json->>'{$userId}'='[]' order by company_id desc limit 1")->queryScalar();
        $currId = 0;
        $updateSqlArray = [];
        $historyMap = [];
        $history = new BatchCompanyBuilder();

        $sql = "select company_id from tbl_company where  client_id={$clientId} and client_tag_list = '{}' and tag::json->>'{$userId}'='[]'";
        self::info("begin fix client user tag  client_id:$clientId  user_id:$userId maxId:{$maxId} currId: {$currId}");
        while ( $currId < $maxId )
        {
            $companyList = $db->createCommand($sql . ' and company_id >' . $currId . ' and is_archive =1 order by company_id asc limit 200')->queryAll(true);
            if (empty($companyList))
                break;
            $companyIds = array_column($companyList, 'company_id');

            //到备库db查找数据
            $companyIdsSql = implode(',', $companyIds);
            $recoveryCompanyList = $recoveryDb->createCommand("select company_id,tag->>'{$userId}' as user_tag,client_tag_list from tbl_company where company_id in ({$companyIdsSql})")->queryAll();
            $recoveryCompanyArr = array_column($recoveryCompanyList, null, 'company_id');


            foreach ($companyList as $company)
            {
                $currId = $company['company_id'];
                if (empty($recoveryCompanyArr[$currId]))
                    continue;

                //备库的标签数据
                $userTagIds = $recoveryCompanyArr[$currId]['user_tag'] ? json_decode($recoveryCompanyArr[$currId]['user_tag']) : [];
                $clientTagIds =  \common\library\util\PgsqlUtil::trimArray($recoveryCompanyArr[$currId]['client_tag_list'] ?? '{}') ?? [];


                if (empty($userTagIds) && empty($clientTagIds))
                    continue;

                $updateSqlArray[] = "update tbl_company
                        set tag = tag || JSONB_BUILD_OBJECT(
							               '{$userId}',
							               (ARRAY(SELECT UNNEST(TRANSLATE((NULLIF(tag ->> '{$userId}', '')) :: JSONB::TEXT, '[]', '{}')::BIGINT[]))
							                   ||
							               ARRAY(
							                       SELECT UNNEST(ARRAY [" . implode(', ', $userTagIds) . "] :: BIGINT[])
							                       EXCEPT
							                       SELECT UNNEST(TRANSLATE((NULLIF(tag ->> '{$userId}', '')) :: JSONB::TEXT, '[]', '{}')::BIGINT[])
							                   ))
							           ),
							    client_tag_list = (client_tag_list::BIGINT[])
										           || ARRAY (
										               SELECT UNNEST(ARRAY [" . implode(', ', $clientTagIds) . "] :: BIGINT[])
										               EXCEPT
										               SELECT UNNEST(client_tag_list::BIGINT[])
										               )
                         where company_id={$company['company_id']} and client_tag_list = '{}' and tag::json->>'{$userId}'='[]'";

                if (!empty($clientTagIds)) {
                    $historyMap[$currId] = [[
                        'id'   => 'client_tag_list',
                        'base' => 1,
                        'old'  => [],
                        'new'  => $clientTagIds
                    ]];
                }
                self::info("company fix tag and client_tag_list clientId:$clientId userId:$userId companyId：$currId count:".count($updateSqlArray));
            }
        }

        $sqlArray = array_chunk($updateSqlArray,100);
        foreach ($sqlArray as $sqlArr)
        {
            $updateSql = implode(';', $sqlArr);
            self::info("company fix tag and client_tag_list clientId:$clientId userId:$userId sql: $updateSql".PHP_EOL);
            if ($dryRun == 0)
            {
                $db->createCommand($updateSql)->execute();
            }
        }

        //记录history
        $mapArray = array_chunk($historyMap,100, true);
        foreach ($mapArray as $mapArr)
        {
            if ($dryRun == 0)
            {
                $history->buildByMap($clientId, $userId, CompanyHistoryPg::TYPE_EDIT_COMPANY, $mapArr);
            }
        }
        self::info("company fix tag and client_tag_list completed");
    }

    /**
     * 恢复客户分组 和 阶段 信息
     * tapd:https://www.tapd.cn/********/bugtrace/bugs/view?bug_id=11********001076438
     * @param $clientId
     * @param $dryRun
     * @return void
     * @throws CDbException
     * @throws CException
     * @throws ProcessException
     */
    public function actionFixClientGroupIdAndTrailStatus($clientId = 3291, $dryRun=1)
    {
        $db = PgActiveRecord::getDbByClientId($clientId);

        $recoveryDb = \ProjectActiveRecord::getDbByDbSet([
            'set_id' => 999999,
            'type' => DbSet::TYPE_PGSQL,
            'host' =>'pc-bp1305054bofs1p69.pg.polardb.rds.aliyuncs.com',
            'name' =>'v5_client',
            'port' => '1921',
            'user' => 'crmphp',
            'password' => 'ou5pxhJTRxvqWcyr8cYBMXKqGMZ6eg',
            'schema_name' => 'v5_client_02'
        ]);

        $maxId = $db->createCommand("select company_id from tbl_company where client_id={$clientId} and is_archive=1 and (group_id = 0 or trail_status = 0) order by company_id desc limit 1")->queryScalar();
        $currId = 0;
        $updateSqlArray = [];

        $sql = "select company_id,group_id,trail_status from tbl_company where  client_id={$clientId} and is_archive = 1 and (group_id = 0 or trail_status = 0)";
        self::info("begin fix client user tag  client_id:$clientId  maxId:{$maxId} currId: {$currId}");
        while ( $currId < $maxId )
        {
            $companyList = $db->createCommand($sql . ' and company_id >' . $currId . ' order by company_id asc limit 200')->queryAll(true);
            if (empty($companyList))
                break;
            $companyIds = array_column($companyList, 'company_id');

            //到备库db查找数据
            $companyIdsSql = implode(',', $companyIds);
            $recoveryCompanyList = $recoveryDb->createCommand("select company_id,group_id,trail_status from tbl_company where company_id in ({$companyIdsSql})")->queryAll();
            $recoveryCompanyArr = array_column($recoveryCompanyList, null, 'company_id');


            foreach ($companyList as $company)
            {
                $currId = $company['company_id'];
                if (empty($recoveryCompanyArr[$currId]))
                    continue;

                //主库的分组 和 阶段 数据
                $groupId = $company['group_id'] ?? 0;
                $trailStatus = $company['trail_status'] ?? 0;
                //备库的分组 和 阶段 数据
                $recoverGroupId = $recoveryCompanyArr[$currId]['group_id'] ?? 0;
                $recoverTrailStatus = $recoveryCompanyArr[$currId]['trail_status'] ?? 0;

                if ($groupId == 0 && $trailStatus == 0 && ($recoverGroupId != 0 || $recoverTrailStatus != 0)) {
                    //分组 阶段 都需要更新
                    $updateSqlArray[] = "update tbl_company set group_id = {$recoverGroupId},trail_status = {$recoverTrailStatus}
                        where company_id={$company['company_id']} and group_id = 0 and trail_status = 0";
                } elseif ($groupId == 0 && $recoverGroupId != 0) {
                    //仅更新分组
                    $updateSqlArray[] = "update tbl_company set group_id = {$recoverGroupId}
                        where company_id={$company['company_id']} and group_id = 0";
                } elseif ($trailStatus == 0 && $recoverTrailStatus != 0) {
                    //仅更新阶段
                    $updateSqlArray[] = "update tbl_company set trail_status = {$recoverTrailStatus}
                        where company_id={$company['company_id']} and trail_status = 0";
                } else {
                    continue;
                }
                self::info("company fix group_id and trail_status clientId:$clientId companyId：$currId count:".count($updateSqlArray));
            }
        }

        $sqlArray = array_chunk($updateSqlArray,100);
        foreach ($sqlArray as $sqlArr)
        {
            $updateSql = implode(';', $sqlArr);
            if ($dryRun == 1) {
                //只在模拟执行的时候打印
                self::info("company fix group_id and trail_status clientId:$clientId  sql: $updateSql".PHP_EOL);
            }
            if ($dryRun == 0)
            {
                $db->createCommand($updateSql)->execute();
            }
        }
        self::info("company fix group_id and trail_status completed");

        if ($dryRun == 0)
        {
            //更新客群
            $adminUserId = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();
            if (!$adminUserId) {
                self::info("skip for client:{$clientId}, no admin user");
                return;
            }
            \User::setLoginUserById($adminUserId);
            ItemSettingConstant::refreshAllCache($clientId);

            $service = new SwarmService($clientId);
            $service->refreshAll(false);
            $service->refreshForPublicRuleForClient(false);
            $this->log("refresh all swarm for client:$clientId");
        }
    }

    /**
     * 恢复客户公司字段信息(仅支持int类型字段)
     * @param $clientId
     * @param $dryRun
     * @return void
     * @throws CDbException
     * @throws CException
     * @throws ProcessException
     */
    public function actionRecoverCompanyIntField($recoverField = 'group_id', $clientId = 22714, $dryRun=1)
    {
        $db = PgActiveRecord::getDbByClientId($clientId);

        $recoveryDb = \ProjectActiveRecord::getDbByDbSet([
            'set_id' => 999999,
            'type' => DbSet::TYPE_PGSQL,
            'host' =>'pc-bp1xwr11683i3pe26.pg.polardb.rds.aliyuncs.com',
            'name' =>'v5_client',
            'port' => '1921',
            'user' => 'crmphp',
            'password' => 'ou5pxhJTRxvqWcyr8cYBMXKqGMZ6eg',
            'schema_name' => 'v5_client_582'
        ]);

        //客户误操作删除分组id
        $targetItemArr = [
            976828844748,975600799076,962936771430,929019587937,929010280262,929019938318,906396157241,906395514103,906393994484,906386489088,877104999474,906374836903,906371556664,906369970253,877102803048,877102081302,906354234269,906355997178,906353092571,877101468626,877100628567,906351337290,906348806071,906346710564,906344396500,906342977472,906341898141,877106166080,877105544256
        ];

        $maxId = $db->createCommand("select company_id from tbl_company where client_id={$clientId} and is_archive=1 and {$recoverField} = 0 order by company_id desc limit 1")->queryScalar();
        $currId = 0;

        $sql = "select company_id,{$recoverField} from tbl_company where  client_id={$clientId} and is_archive = 1 and {$recoverField} = 0";
        self::info("begin fix client user tag  client_id:$clientId  maxId:{$maxId} currId: {$currId}");
        $update_flag = false;

        while ( $currId < $maxId )
        {
            $updateSqlArray = [];
            $companyList = $db->createCommand($sql . ' and company_id >' . $currId . ' order by company_id asc limit 200')->queryAll(true);
            if (empty($companyList))
                break;
            $companyIds = array_column($companyList, 'company_id');

            //到备库db查找数据
            $companyIdsSql = implode(',', $companyIds);
            $recoveryCompanyList = $recoveryDb->createCommand("select company_id,{$recoverField} from tbl_company where company_id in ({$companyIdsSql})")->queryAll();
            $recoveryCompanyArr = array_column($recoveryCompanyList, null, 'company_id');


            foreach ($companyList as $company)
            {
                $currId = $company['company_id'];
                if (empty($recoveryCompanyArr[$currId]))
                    continue;

                //主库的字段数据
                $fieldId = $company[$recoverField] ?? 0;
                //备库的字段数据
                $recoverFieldId = $recoveryCompanyArr[$currId][$recoverField] ?? 0;

                if ($fieldId == 0 && $recoverFieldId != 0 && in_array($recoverFieldId, $targetItemArr)) {
                    //更新字段信息
                    $updateSqlArray[] = "update tbl_company set {$recoverField} = {$recoverFieldId} where company_id={$company['company_id']} and {$recoverField} = 0";
                }else {
                    continue;
                }
                self::info("company fix {$recoverField} clientId:$clientId companyId：$currId count:".count($updateSqlArray));
            }

            $updateSql = empty($updateSqlArray) ? '' : implode(';', $updateSqlArray);
            if ($dryRun == 1) {
                //只在模拟执行的时候打印
                self::info("company fix {$recoverField} clientId:$clientId  sql: $updateSql".PHP_EOL);
            }
            if ($dryRun == 0 && !empty($updateSql))
            {
                $db->createCommand($updateSql)->execute();
                $update_flag = true;
            }
        }

        self::info("company fix {$recoverField} completed");

        if ($dryRun == 0 && $update_flag)

        {
            $mysql_db = ProjectActiveRecord::getDbByClientId($clientId);
            ProjectActiveRecord::setConnection($mysql_db);
            //更新分组信息
            $itemIdSql = implode(',',$targetItemArr);
            $updateItemSql = "update tbl_item_setting set enable_flag=1 where item_id in ({$itemIdSql});";

            $mysql_db->createCommand($updateItemSql)->execute();

            //更新客群
            $adminUserId = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();
            if (!$adminUserId) {
                self::info("skip for client:{$clientId}, no admin user");
                return;
            }
            \User::setLoginUserById($adminUserId);
            ItemSettingConstant::refreshAllCache($clientId);

            $service = new SwarmService($clientId);
            $service->refreshAll(false);
            $service->refreshForPublicRuleForClient(false);
            $this->log("refresh all swarm for client:$clientId");
        }
    }

    /**
     * 修复由于1215迭代客户导入优化迭代引入的 联系人转移问题
     * @param $client_id
     * @param $dryRun
     * @return void
     * @throws CDbException
     * @throws CException
     * @throws ProcessException
     */
    public function actionFixCompanyRelateCustomer($client_id, $dryRun = 1)
    {

        //step1:找出新导入的客户
        //step2:找出新客户中转移了旧客户的部分
        //step3:把转移的联系人 返还给 旧客户
        //step4:新建同名的联系人给 新客户
        //step5:修改新客户的操作历史
        if ($client_id) {
            $clientIds = explode(',', $client_id);
        } else {
            //2022-12-06之后 有使用过导出的client
            $clientIds = [83863,33209,49326,32659,33600,85959,82129,19483,23304,83363,55566,83387,28138,60051,80014,86625,41000,74834,4072,78676,2944,57840,60039,41447,73485,85795,63481,62209,69546,55219,20257,70787,67532,79747,51620,41096,76747,75630,80977,88137,4108,83497,48442,40298,78836,83159,75850,86141,87505,69155,75065,79793,28945,19891,78670,62321,3368,83924,52403,79614,70124,30637,1403,5099,78834,18413,56800,79500,55547,42727,51999,78330,69001,80228,39090,77178,67341,3326,47072,73057,43811,62534,68657,88201,333812,48263,60596,57341,54145,59448,38562,88419,55461,47640,75984,79694,68171,46873,31165,27269,42219,33943,83745,61105,78723,82722,49263,81254,9112,13852,71705,39883,22024,55877,63174,84033,75662,55145,47285,33670,85542,71688,23636,3343,44730,28265,81496,30229,61948,70119,84576,31124,40952,74957,10676,63304,88860,20953,83330,72931,9776,42968,70316,1108,47508,77944,43838,37400,49370,66351,13262,86851,57022,82447,51743,33748,84945,16226,44830,71640,72057,84075,58414,47890,333473,86956,65804,45265,83834,40649,67178,82390,79463,86262,72859,20085,80929,76800,55534,78091,57379,81986,36560,26809,59848,49188,85802,33135,86520,36676,79538,82633,88412,4942,69226,35694,21744,78426,37696,78761,80177,49283,82484,3474,64408,86386,82176,32947,66138,82559,43327,35551,75043,66746,75621,82089,27389,84846,86233,71113,65466,62191,50455,88716,67679,75257,3514,19559,72498,58629,83498,81544,85037,74853,70360,37170,29792,41644,58648,50294,88391,86336,51552,36892,37620,35603,87410,72583,79249,81732,9932,33536,32219,38693,87275,87986,53440,67130,63046,88592,85303,58732,32305,29412,86216,40038,83968,85035,44812,26123,27202,86173,58331,74912,35552,30180,75355,17421,40021,54567,79370,52469,81143,60858,77096,52004,84095,3870,63395,38925,86116,75834,65012,55987,3566,80229,57547,55595,70383,88909,85863,74605,27147,13201,35479,62749,30362,42088,31074,73846,33725,25543,8099,7049,55730,69301,79587,11987,36502,67487,81867,67713,86259,82050,5262,83359,40576,59110,69664,76716,42355,48266,69112,40146,80303,86360,53697,59055,69578,28726,59820,64481,62852,83519,81299,85287,65414,11300,76173,86872,66824,81509,76119,23928,44457,87953,40096,77173,81279,84167,76086,87570,78418,64245,80605,19758,86557,88254,11924,81483,59925,88421,82664,319,83171,86440,30900,79320,32403,39108,82636,83353,66615,81708,80959,60635,3274,66061,41771,80940,67254,53276,81799,38203,80739,20523,78888,86535,74533,54190,39679,16367,29191,87997,78936,39136,59274,80096,67245,2322,80376,82983,79341,74580,81777,60672,87830,58022,57218,82603,56901,78160,13877,3095,54204,34663,5170,88234,16054,75812,61522,78600,63445,77704,83013,68496,88580,2662,71345,80972,10299,71741,81371,86820,78831,75687,83263,29245,81844,81301,71939,71749,43391,49386,1512,85052,1867,44692,7321,69576,75322,14499,77546,49935,38093,88148,83903,64801,73981,4659,78329,32556,88659,45463,71197,59258,61179,80746,43691,41337,40121,33327,81368,82556,43643,29079,82131,37994,75503,34548,39474,34197,59832,10077,13344,78123,14434,38992,88363,53456,83107,37113,70699,79784,52255,40312,108,55832,77706,58774,47481,81422,84116,85521,35409,71696,2642,82039,87788,46143,1011,76827,23155,48958,37405,52851,27535,41552,74452,76892,79171,82993,22049,83299,32437,19591,3147,35746,34414,32435,78769,25970,79234,59920,56870,77311,383,77238,49344,36698,67795,82122,88776,56835,79187,64752,2206,79358,82272,14734,72249,83144,73149,19511,440,653,79061,78743,16671,67066,82093,88160,59940,4107,60874,75496,32156,36797,71909,86055,81079,44275,78403,63686,27501,28366,3,44911,84410,6374,77223,51819,89057,79936,88096,38131,78943,31625,82590,19269,81329,65894,88976,84709,45966,27022,59775,27600,53703,72486,16657,62014,72812,55754,87790,33665,81716,78351,83298,73489,69431,4678,10115,65941,88180,84198,68430,82046,75478,69484,29355,87707,84444,74808,55245,46868,1225,30074,63243,67508,81442,5813,78453,87435,9636,86366,79723,60768,70250,28518,84772,33269,2304,9846,83857,30218,48720,80710,65963,79605,79345,3710,30647,88847,22035,5303,80880,28195,62787,88074,31214,87040,31544,49177,41321,68387,80483,82924,31922,26528,82975,30899,75242,44293,1161,30246,4497,37522,76149,73189,42755,2331,1954,50904,88480,80064,32077,60704,78777,4960,88306,81336,520,56367,333388,30654,73006,73970,85517,74849,50854,31261,79930,26497,32178,69002,27499,76062,88223,88837,46621,84831,87836,89091,50170,66657,67687,87662,79712,13699,66440,72719,83702,36723,1670,84073,28216,78949,1980,12369,39329,16084,37204,72738,85464,23297,3022,87119,80949,59744,40290,79469,40331,33531,27577,27675,85368,41729,81693,21505,66921,61755,85172,79230,18617,61347,58893,74417,83020,89120,83208,82767,64760,18618,82702,18420,39015,18303,61246,77841,81444,52437,28351,67578,41698,81773,40262,83769,80250,82197,28808,88632,86459,79030,23576,79303,80189,81897,83491,22825,49025,53583,51777,83435,64142,29599,77221,47184,58692,60359,71359,72924,86939,83210,82170,19219,15377,681,62992,88861,76851,58748,70807,82463,77497,72045,82889,8811,71902,29039,82828,82077,69676,77382,86152,194,76324,41790,72530,5467,74379,48404,33031,42519,55896,5703,7302,61181,82292,86665,651,87983,87181,86959,78291,69498,62185,87368,78372,5034,32038,82204,85799,57745,26969,85255,88839,76904,15515,4184,86242,44174,34402,77521,76120,19179,79832,82032,78252,7769,87903,2189,49613,29062,81233,26218,5717,78814,63720,78824,86127,35170,84008,71823,2274,31728,72600,83887,45053,52005,81951,38939,85950,86484,83509,26884,13508,74423,82041,32663,84222,1878,32028,81658,48307,31026,85458,40535,11528,61741,18351,41155,31450,32307,37438,31402,81372,66805,81138,75986,4054,85318,82139,27620,80684,7788,29591,81109,81888,44385,62790,81095,83142,83213,75179,44652,86079,87345,60333,75946,3205,19576,31829,78416,87276,81878,86677,76717,32498,68549,88553,36311,333356,2001,82438,88824,42612,82419,80992,74106,87259,81238,69746,27405,35324,56893,53459,25929,83033,88012,2327,88494,26473,22908,87723,42019,43099,69704,69167,79239,82207,88768,20016,43896,40837,44221,51219,80284,2595,86066,58290,86883,1603,88694,45289,87518,3058,83986,63892,57201,38478,83836,78608,70431,87347,87596,78467,85507,50448,51570,72124,38952,73128,69585,71857,39203,56155,2545,83486,86894];
        }

        //内部灰度时间
        $beginTime = '2022-12-06';
        foreach ($clientIds as $clientId) {
            $db = PgActiveRecord::getDbByClientId($clientId);
            if (!$db) {
                $this->log("skip for $clientId");
                continue;
            }
            $adminUserId = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();

            if (!$adminUserId) {

                self::info(("no admin user, continue:{$clientId}"));
                continue;
            }
            User::setLoginUserById($adminUserId);

            $recover_flag = false;
            $recover_company_ids = [];
            $companyList = $db->createCommand("select company_id,create_time from tbl_company where client_id = {$clientId} and is_archive=1 and archive_type = 6 and main_customer_email <> '' and create_time > '{$beginTime}'")->queryAll();
            if (empty($companyList)) continue;
            foreach ($companyList as $company)
            {
                $companyId = $company['company_id'];
                $companyCreateTime = $company['create_time'];
                //有问题的联系人 联系人创建时间 早于 客户创建时间
                $customerList =  $db->createCommand("select company_id, customer_id from tbl_customer where client_id = {$clientId} and is_archive=1 and  company_id = {$companyId} and create_time < '{$companyCreateTime}'")->queryAll();
                if (empty($customerList)) continue;
                foreach ($customerList as $customer) {
                    $customerId = $customer['customer_id'];
                    //type 11新建联系人 15恢复联系人 31合并联系人  12删除联系人
                    $hadAddCustomerLog = $db->createCommand("select company_id, customer_id, diff, type from tbl_company_history where client_id = {$clientId} and company_id = {$companyId}  and customer_id = {$customerId} and type in (11,15,31) limit 1")->queryAll();
                    if ($hadAddCustomerLog) continue;
                    //如果当前客户 并没有新建过该联系人,需要进行还原操作 还原给最近一次的新增客户
                    $recoverCompany = $db->createCommand("select company_id from tbl_company_history where client_id = {$clientId} and customer_id = {$customerId} and type in (11,15,31)  order by create_time desc limit 1")->queryScalar();
                    if (empty($recoverCompany)) continue;
                    //确认需要还原联系人的客户 确实缺少了该客户
                    $recoverLog = $db->createCommand("select company_id,type from tbl_company_history where client_id = {$clientId} and customer_id = {$customerId} and company_id = {$recoverCompany} and type in (11,15,31,12)")->queryAll();
                    $addLog = $deleteLog = [];
                    foreach ($recoverLog as $log) {
                        if (in_array($log['type'],[11,15,31])) {
                            $addLog[] = $log;
                        }else{
                            $deleteLog[] = $log;
                        }
                    }
                    if (count($addLog) <= count($deleteLog)) {
                        continue;
                    }
                    self::info("recover Customer clientId:$clientId companyId:$companyId customerId:$customerId recoverCompany: $recoverCompany".PHP_EOL);
                    if (!$dryRun) {
                        try {
                            $recover_flag = true;
                            $recover_company_ids[] = $recoverCompany;
                            //需要进行还原联系人操作
                            $customer = new \common\library\customer_v3\customer\orm\Customer($clientId, $customerId);
                            $company = new \common\library\customer_v3\company\orm\Company($clientId, $companyId);
                            //编辑联系人日志
                            $updateCustomerLog = $db->createCommand("select company_id, customer_id, diff, type, create_time from tbl_company_history where client_id = {$clientId} and company_id = {$companyId}  and customer_id = {$customerId} and type = 2 and create_time >= '{$companyCreateTime}'")->queryAll();
                            foreach ($updateCustomerLog as $i => $row) {
                                $diffs[$i]['diff'] = json_decode($row['diff'], true);
                                foreach($diffs as $diff) {
                                    // 计算需要回滚的字段
                                    $fieldsToRevert = $this->getFieldsToRevert($diff, $customer);
                                    // 回滚联系人改动
                                    $this->doRevertCustomer($customerId, $fieldsToRevert, $db, $clientId);
                                    //删除之前改动日志
                                    $this->clearHistoryLog($row,$clientId,$db);
                                }
                            }

                            // 转移联系人回旧客户
                            $updateSql = "update tbl_customer set company_id = {$recoverCompany} where company_id={$companyId} and customer_id = {$customerId} and is_archive=1";
                            $db->createCommand($updateSql)->execute();
                            //新建该客户联系人
                            $new_customer = new \common\library\customer_v3\customer\orm\Customer($clientId);
                            $attributes = $customer->getAttributes();
                            unset($attributes['customer_id'],$attributes['company_id']);
                            $attributes['create_time'] = $company->create_time ?? '';
                            $attributes['update_time'] = $company->create_time ?? '';
                            $attributes['archive_time'] = $company->archive_time ?? $company->create_time;
                            $new_customer->setAttributes($attributes);
                            $new_customer->setOperatorUserId($company->create_user ?? null);
                            $new_customer->setSkipDuplicateCheck(true);
                            $company->addCustomer($new_customer);
                            $company->save();
                        } catch (Throwable $throwable) {
                            $errmsg = $throwable->getFile().$throwable->getLine().$throwable->getMessage();
                            self::info("recover Customer Error clientId:$clientId companyId:$companyId customerId:$customerId recoverCompany: $recoverCompany errMsg: $errmsg".PHP_EOL);
                            continue;
                        }
                    }
                }
            }
            if (!$dryRun && $recover_flag && $recover_company_ids) {
                //更新客群
                (new \common\library\swarm\SwarmService($clientId))->refreshByRefer($recover_company_ids, [], false);
                //更新索引
                \common\library\server\es_search\SearchQueueService::pushCompanyQueue(0, $clientId, $recover_company_ids, Constants::SEARCH_INDEX_TYPE_UPDATE);
                //更新邮箱归属
                $identitySync = new CustomerSync($clientId);
                $identitySync->setFindCompanyId($recover_company_ids);
                $identitySync->sync();
            }
        }
    }

    protected function doRevertCustomer(int $customerId, array $fieldsToRevert, $db, $clientId)
    {
        $sql = "UPDATE tbl_customer SET customer_id=$customerId ";
        $params = [];
        foreach ($fieldsToRevert as $field => $change) {

            $sql .= ", $field=:$field ";
            if ( in_array($field,['tel_list','contact','image_list','external_field_data']) ) {
                $change['old'] = json_encode($change['old']);
                $change['new'] = json_encode($change['new']);
            }
            $params[":$field"] = $change['old'];
        }

        if (empty($params)) {
            return;
        }

        $sql .= " WHERE client_id=$clientId AND customer_id=$customerId";
        $db->createCommand($sql)->execute($params);
    }

    protected function getFieldsToRevert($diff, $customer)
    {
        $fieldsToRevert = [];

        foreach ($diff['diff'] as $i => $change) {

            $field    = $change['id'] ?? '';
            $oldValue = $change['old'] ?? '';
            $newValue = $change['new'];
            if ($newValue === ($customer->$field ?? NULL)) {
                $fieldsToRevert[$field] = [
                    'old'    => $oldValue,
                    'new'    => $newValue,
                    'change' => $change,
                ];
            }
        }

        return $fieldsToRevert;
    }

    protected function clearHistoryLog($diff,$clientId,$db)
    {
        $sql = "DELETE FROM tbl_company_history
                    WHERE client_id=:client_id AND company_id=:company_id AND type=:type
                    AND create_time=:create_time AND customer_id=:customer_id";
        $db->createCommand($sql)->execute([
            ':client_id'   => $clientId,
            ':company_id'  => $diff['company_id'],
            ':type'        => $diff['type'],
            ':create_time' => $diff['create_time'],
            ':customer_id' => $diff['customer_id']
        ]);
    }

    public function actionFixNextFollowUpTime($clientId, $grey = 1, $greyNum = null, $dryRun = 1)
    {
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey && !is_null($greyNum)) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        foreach($clientIds as $client_id) {
            $db = PgActiveRecord::getDbByClientId($client_id);
            if (!$db) {
                \LogUtil::info("skip for clientId: $client_id, no db");
                return;
            }

            $count = 0;
            foreach (SqlUtil::iterateTableData($db, 'tbl_company', 'company_id', 200, 'next_follow_up_time', "client_id={$clientId} and company_id > 0 and is_archive=1 and next_follow_up_time > '1970-01-01 00:00:00'") as $companyList) {
                if (!$dryRun) {
                    \common\library\schedule\Helper::refreshNextFollowTimeByCompany($client_id, array_column($companyList, 'company_id'));
                }
                $count += count($companyList);
                \LogUtil::info("process for client:{$client_id}, count: $count");
            }
        }
    }

    public function actionRefreshOpportunityNextFollowUpTime($clientId, $grey = 1, $greyNum = null, $dryRun = 1)
    {
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey && !is_null($greyNum)) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        foreach($clientIds as $client_id) {
            $admin = \common\library\privilege_v3\PrivilegeService::getInstance($client_id)->getAdminUserId();
            if (!$admin) {

                self::info(("no admin user, continue:{$client_id}"));
                continue;
            }
            User::setLoginUserById($admin);
            $db = PgActiveRecord::getDbByClientId($client_id);
            if (!$db) {
                \LogUtil::info("skip for clientId: $client_id, no db");
                return;
            }

            $count = 0;
            foreach (SqlUtil::iterateTableData($db, 'tbl_opportunity', 'opportunity_id', 200, 'next_follow_up_time', "client_id={$clientId} and opportunity_id > 0 and enable_flag=1") as $opportunityList) {
                if (!$dryRun) {
                    \common\library\schedule\Helper::refreshNextFollowTimeByOpportunity($client_id, array_column($opportunityList, 'opportunity_id'));
                }
                $count += count($opportunityList);
                \LogUtil::info("process for client:{$client_id}, count: $count");
            }
        }
    }

    /**
     * 清除异常联系人邮箱相关数据
     * 例如：<EMAIL> <EMAIL>
     * 这些系统通知类邮件建为客户联系人后，大量的邮件会导致生成大量的客户邮件动态数据，造成数据库查询压力
     *
     * @param $clientId
     * @param $customerId
     * @param $email
     * @param $dryRun
     * @return void|null
     * @throws ProcessException
     */
    public function actionCleanServiceEmail($clientId = 0, $customerId = 0, $email = '', $dryRun = 1)
    {
        if (empty($customerId) && empty($email)) {
            return null;
        }

        if (!empty($email) && !EmailUtil::isEmail($email)) {
            return null;
        }

        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        foreach($clientIds as $client_id) {
            $db = ProjectActiveRecord::getDbByClientId($client_id);
            $pb = PgActiveRecord::getDbByClientId($client_id);

            if (empty($db) || empty($pb)) {
                continue;
            }

            $relationCount = $customerCount = $trailCount = 0;

            if (!empty($email)) {
                $sql = "SELECT customer_id, company_id, email FROM tbl_customer WHERE client_id={$client_id} AND email='{$email}' AND is_archive=1";
            } else {
                $sql = "SELECT customer_id, company_id, email FROM tbl_customer WHERE client_id={$client_id} AND customer_id={$customerId} AND is_archive=1";
            }

            $customers = $pb->createCommand($sql)->queryAll(true);
            foreach ($customers as $customer) {
                if (empty($customer['email']) || !EmailUtil::isEmail($customer['email'])) {
                    continue;
                }
                $sql = "SELECT email_id FROM tbl_email_id WHERE email='{$customer['email']}'";
                $emailId = \Yii::app()->db->createCommand($sql)->queryScalar();
                if (!empty($emailId)) {
                    if (!$dryRun) {
                        $sql = "UPDATE tbl_email_relation SET customer_id=0 WHERE client_id={$client_id} AND email_id={$emailId} AND customer_id={$customer['customer_id']}";
                        $relationCount += $db->createCommand($sql)->execute();
                    } else {
                        $sql = "SELECT count(1) as total FROM tbl_email_relation WHERE client_id={$client_id} AND email_id={$emailId} AND customer_id={$customer['customer_id']}";
                        $relationCount += $db->createCommand($sql)->queryScalar();
                    }
                }

                if (!$dryRun) {
                    $sql = "UPDATE tbl_dynamic_trail SET enable_flag=0 WHERE client_id={$client_id} AND company_id={$customer['company_id']} AND customer_id && ARRAY[{$customer['customer_id']}]::bigint[] AND type IN (201, 202) AND enable_flag=1";
                    $trailCount += $pb->createCommand($sql)->execute();
                } else {
                    $sql = "SELECT count(1) as total FROM tbl_dynamic_trail WHERE client_id={$client_id} AND company_id={$customer['company_id']} AND customer_id && ARRAY[{$customer['customer_id']}]::bigint[] AND type IN (201, 202) AND enable_flag=1";
                    $trailCount += $pb->createCommand($sql)->queryScalar();
                }
            }

            if (!empty($customers)) {
                $customerIds = array_column($customers, 'customer_id');
                if (!$dryRun) {
                    $sql = "UPDATE tbl_customer SET is_archive=0 WHERE client_id={$client_id} AND customer_id IN (". implode(',', $customerIds) .")";
                    $customerCount = $pb->createCommand($sql)->execute();
                } else {
                    $customerCount = count($customers);
                }
            }

            self::info("clientId={$client_id} relationCount={$relationCount} trailCount={$trailCount} customerCount={$customerCount}");
        }
    }

    /**
     * 修复客户导入引起的额外增加标签问题
     * tapd:https://www.tapd.cn/********/bugtrace/bugs/view/11********001082948
     * @param $clientId
     * @param $dryRun
     * @return void
     * @throws CDbException
     * @throws CException
     * @throws ProcessException
     */
    public function actionFixClientTagList($clientId = 336654,$dryRun=1)
    {
        $adminUserId = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();
        $user = User::getUserObject($adminUserId);
        User::setLoginUserById($user->getUserId());

        $db = PgActiveRecord::getDbByClientId($clientId);

        $recoveryDb = \ProjectActiveRecord::getDbByDbSet([
            'set_id' => 9999999,
            'type' => DbSet::TYPE_PGSQL,
            'host' =>'pc-bp1b1gq80oi6yiy84.pg.polardb.rds.aliyuncs.com',
            'name' =>'v5_client',
            'port' => '1921',
            'user' => 'crmphp',
            'password' => 'ou5pxhJTRxvqWcyr8cYBMXKqGMZ6eg',
            'schema_name' => 'v5_client_996'
        ]);

        $maxId = $db->createCommand("select company_id from tbl_company where client_id={$clientId} and is_archive=1 order by company_id desc limit 1")->queryScalar();
        $currId = 0;
        $updateSqlArray = [];
        $historyMap = [];
        $history = new BatchCompanyBuilder();

        self::info("begin fix client tag list client_id:$clientId maxId:{$maxId} currId: {$currId}");
        while ( $currId < $maxId )
        {
            $companyList = $db->createCommand("select company_id,client_tag_list from tbl_company where  client_id={$clientId} and company_id > {$currId} and is_archive =1 order by company_id asc limit 200")->queryAll(true);
            if (empty($companyList))
                break;
            $companyIds = array_column($companyList, 'company_id');

            //到备库db查找数据
            $companyIdsSql = implode(',', $companyIds);
            $recoveryCompanyList = $recoveryDb->createCommand("select company_id,client_tag_list from tbl_company where company_id in ({$companyIdsSql})")->queryAll();
            $recoveryCompanyArr = array_column($recoveryCompanyList, null, 'company_id');


            foreach ($companyList as $company)
            {
                $currId = $company['company_id'];
                if (empty($recoveryCompanyArr[$currId]))
                    continue;

                //主库的公司标签数据
                $clientTagIds = \common\library\util\PgsqlUtil::trimArray($company['client_tag_list'] ?? '{}') ?? [];
                $clientTagIds = array_unique($clientTagIds);
                //备库的公司标签数据
                $recoverClientTagIds =  \common\library\util\PgsqlUtil::trimArray($recoveryCompanyArr[$currId]['client_tag_list'] ?? '{}') ?? [];
                $recoverClientTagIds = array_unique($recoverClientTagIds);

                //如果$clientTagIds为空表示 客户手动删除了标签
                //如果$recoverClientTagIds为空表示 不用恢复
                //如果$clientTagIds个数只有一个，不用恢复
                if (empty($clientTagIds) || empty($recoverClientTagIds) || count($clientTagIds) < 2)
                    continue;


                $intersect = array_intersect($clientTagIds, $recoverClientTagIds);
                $diff = array_diff($clientTagIds, $recoverClientTagIds);
                //有交集 并且也有不同 才需要恢复
                if (empty($intersect) || empty($diff))
                    continue;

                $updateSqlArray[] = "update tbl_company set client_tag_list = ARRAY [" . implode(', ', $recoverClientTagIds) . "] :: BIGINT[] where client_id ={$clientId} and company_id={$company['company_id']}";

                if (!empty($recoverClientTagIds)) {
                    $historyMap[$currId] = [[
                        'id'   => 'client_tag_list',
                        'base' => 1,
                        'old'  => $clientTagIds,
                        'new'  => $recoverClientTagIds
                    ]];
                }
                self::info("company fix tag and client_tag_list clientId:$clientId companyId：$currId count:".count($updateSqlArray));
            }
        }

        $sqlArray = array_chunk($updateSqlArray,100);
        foreach ($sqlArray as $sqlArr)
        {
            $updateSql = implode(';', $sqlArr);
            self::info("company fix tag and client_tag_list clientId:$clientId sql: $updateSql".PHP_EOL);
            if ($dryRun == 0)
            {
                $db->createCommand($updateSql)->execute();
            }
        }

        //记录history
        $mapArray = array_chunk($historyMap,100, true);
        foreach ($mapArray as $mapArr)
        {
            if ($dryRun == 0)
            {
                $history->buildByMap($clientId, $adminUserId, CompanyHistoryPg::TYPE_EDIT_COMPANY, $mapArr);
            }
        }
        self::info("company fix tag and client_tag_list completed");
    }

    public function actionFixCompanyExternalData($clientId = 81301, $dryRun=1)
    {
        $db = PgActiveRecord::getDbByClientId($clientId);

        $maxId = $db->createCommand("select company_id from tbl_company where client_id={$clientId} and is_archive=1 and (create_time > '2024-06-19' or update_time > '2024-06-19') order by company_id desc limit 1")->queryScalar();
        $currId = 0;
        $updateSqlArray = [];
        $fieldTypeMap = CustomFieldService::getExternalFieldTypeMap($clientId, \CONSTANTS::TYPE_COMPANY);
        //机器翻译 查看Map
        $translateLanguageMap = \common\library\EasyTranslate::getTranslateLanguageMap($clientId, 'en');
        $translateLanguageMap = array_flip($translateLanguageMap);

        $sql = "select company_id,external_field_data from tbl_company where  client_id={$clientId} and is_archive = 1 and (create_time > '2024-06-19' or update_time > '2024-06-19')";
        self::info("begin fix client user tag  client_id:$clientId  maxId:{$maxId} currId: {$currId}");
        while ( $currId < $maxId )
        {
            $companyList = $db->createCommand($sql . ' and company_id >' . $currId . ' order by company_id asc limit 200')->queryAll(true);
            if (empty($companyList))
                break;

            foreach ($companyList as $company)
            {
                $currId = $company['company_id'];
                $external_field_data = json_decode($company['external_field_data'], true);
                $update_flag = false;
                foreach ($external_field_data as $fieldId => $fieldValue) {
                    //有单选/有多选字段
                    //匹配到了英文Map
                    $fieldIdType = $fieldTypeMap[$fieldId] ?? null;
                    if (in_array($fieldIdType, [CustomFieldService::FIELD_TYPE_SELECT, CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT])) {
                        if (is_array($fieldValue)) {
                            foreach ($fieldValue as $k => $option) {
                                if (isset($translateLanguageMap[$option])) {
                                    $update_flag = true;
                                    $fieldValue[$k] = $translateLanguageMap[$option];
                                }
                            }
                            $external_field_data[$fieldId] = $fieldValue;
                        } else {
                            if (isset($translateLanguageMap[$fieldValue])) {
                                $update_flag = true;
                                $external_field_data[$fieldId] = $translateLanguageMap[$fieldValue];
                            }

                        }
                    }
                }

                if ($update_flag) {
                    $external_field_data = json_encode($external_field_data);
                    $updateSqlArray[] = "update tbl_company set external_field_data = '{$external_field_data}' where company_id={$company['company_id']}";
                }
                self::info("company fix external_data clientId:$clientId companyId：$currId count:".count($updateSqlArray));
            }
        }

        $sqlArray = array_chunk($updateSqlArray,100);
        foreach ($sqlArray as $sqlArr)
        {
            $updateSql = implode(';', $sqlArr);
            if ($dryRun == 1) {
                //只在模拟执行的时候打印
                self::info("company fix external_data clientId:$clientId  sql: $updateSql".PHP_EOL);
            }
            if ($dryRun == 0)
            {
                $db->createCommand($updateSql)->execute();
            }
        }
        self::info("company fix external_data completed");

        if ($dryRun == 0 && !empty($sqlArray))
        {
            //更新客群
            $service = new SwarmService($clientId);
            $service->refreshAll(false);
            $service->refreshForPublicRuleForClient(false);
            $this->log("refresh all swarm for client:$clientId");
            //执行完脚本进行es刷新
        }
    }

    public function actionFixCustomerExternalData($clientId = 81301, $dryRun=1)
    {
        $db = PgActiveRecord::getDbByClientId($clientId);

        $maxId = $db->createCommand("select customer_id from tbl_customer where client_id={$clientId} and is_archive=1 and (create_time > '2024-06-19' or update_time > '2024-06-19') order by customer_id desc limit 1")->queryScalar();
        $currId = 0;
        $updateSqlArray = [];
        $fieldTypeMap = CustomFieldService::getExternalFieldTypeMap($clientId, \CONSTANTS::TYPE_CUSTOMER);
        //机器翻译 查看Map
        $translateLanguageMap = \common\library\EasyTranslate::getTranslateLanguageMap($clientId, 'en');
        $translateLanguageMap = array_flip($translateLanguageMap);

        $sql = "select customer_id,external_field_data from tbl_customer where  client_id={$clientId} and is_archive = 1 and (create_time > '2024-06-19' or update_time > '2024-06-19')";
        self::info("begin fix client user tag  client_id:$clientId  maxId:{$maxId} currId: {$currId}");
        while ( $currId < $maxId )
        {
            $customerList = $db->createCommand($sql . ' and customer_id >' . $currId . ' order by customer_id asc limit 200')->queryAll(true);
            if (empty($customerList))
                break;

            foreach ($customerList as $customer)
            {
                $currId = $customer['customer_id'];
                $external_field_data = json_decode($customer['external_field_data'], true);
                $update_flag = false;
                foreach ($external_field_data as $fieldId => $fieldValue) {
                    //有单选/有多选字段
                    //匹配到了英文Map
                    $fieldIdType = $fieldTypeMap[$fieldId] ?? null;
                    if (in_array($fieldIdType, [CustomFieldService::FIELD_TYPE_SELECT, CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT])) {
                        if (is_array($fieldValue)) {
                            foreach ($fieldValue as $k => $option) {
                                if (isset($translateLanguageMap[$option])) {
                                    $update_flag = true;
                                    $fieldValue[$k] = $translateLanguageMap[$option];
                                }
                            }
                            $external_field_data[$fieldId] = $fieldValue;
                        } else {
                            if (isset($translateLanguageMap[$fieldValue])) {
                                $update_flag = true;
                                $external_field_data[$fieldId] = $translateLanguageMap[$fieldValue];
                            }

                        }
                    }

                }

                if ($update_flag) {
                    $external_field_data = json_encode($external_field_data);
                    $updateSqlArray[] = "update tbl_customer set external_field_data = '{$external_field_data}' where customer_id={$customer['customer_id']}";
                }
                self::info("company fix external_data clientId:$clientId companyId：$currId count:".count($updateSqlArray));
            }
        }

        $sqlArray = array_chunk($updateSqlArray,100);
        foreach ($sqlArray as $sqlArr)
        {
            $updateSql = implode(';', $sqlArr);
            if ($dryRun == 1) {
                //只在模拟执行的时候打印
                self::info("company fix external_data clientId:$clientId  sql: $updateSql".PHP_EOL);
            }
            if ($dryRun == 0)
            {
                $db->createCommand($updateSql)->execute();
            }
        }
        self::info("company fix external_data completed");

        if ($dryRun == 0 && !empty($sqlArray))
        {
            //更新客群
            $service = new SwarmService($clientId);
            $service->refreshAll(false);
            $service->refreshForPublicRuleForClient(false);
            $this->log("refresh all swarm for client:$clientId");
            //执行完脚本进行es刷新
        }
    }

    protected function mergeAfterTransferCompanyRelation($clientId, $userId, $fromId, $toId, $groupId)
    {
        // time line quotation order user schedule company file statistics
        //time line
        \common\library\trail\Helper::batchChangeCompanyId($fromId, $toId, $userId);

        //最近动态重算
        \common\library\trail\Helper::resetCompanyLastTrailId($clientId, $toId);

        \common\library\google_ads\lead\SessionLeadHelper::changeCompanyId($clientId, $fromId, $toId);

        // sns customer contact
        CustomerContactHelper::changeCompanyId($clientId, $fromId, $toId);

        // CIQ
        \CompanyBindCiq::deleteByCompanyId($fromId, $clientId);

        //quotation 报价单
        (new InvoiceService($clientId, \Constants::TYPE_QUOTATION))->batchChangeCompanyId($fromId, $toId, $userId, $groupId);

        //order 订单
        (new InvoiceService($clientId, \Constants::TYPE_ORDER))->batchChangeCompanyId($fromId, $toId, $userId, $groupId);

        //user schedule
        \common\library\schedule\Helper::batchChangeCompanyId($fromId, $toId, $userId);

        //companyFile
        \CompanyFileService::batchChangeCompanyId($fromId, $toId, $userId);

        //Opportunity
        \common\library\opportunity\Helper::batchChangeCompanyId($fromId, $toId, $userId);

        //cashCollection
        (new \common\library\cash_collection\CashCollectionBatchOperator($userId))->batchChangeCompanyId($fromId, $toId);

        //迁移阿里客户ID,买家ID、询盘ID、来源店铺
        \common\library\alibaba\trade\Helper::batchChangeCompanyId($clientId, $fromId, $toId);
        CustomerSyncHelper::batchChangeCompanyId($clientId, $fromId, $toId);
    }

    protected function mergeAfterTransferCustomerRelation($clientId, $userId, $companyId, $fromCustomerId, $toCustomerId)
    {
        //time line
        \common\library\trail\Helper::batchChangeCustomerId($companyId, $fromCustomerId, $toCustomerId, $userId);

        // sns customer contact
        CustomerContactHelper::changeCustomerId($clientId, $companyId, $fromCustomerId, $toCustomerId);

        //quotation 报价单
        (new InvoiceService($clientId, \Constants::TYPE_QUOTATION))->batchChangeCustomerId($companyId, $fromCustomerId, $toCustomerId, $userId);

        //order 订单
        (new InvoiceService($clientId, \Constants::TYPE_ORDER))->batchChangeCustomerId($companyId, $fromCustomerId, $toCustomerId, $userId);

        //companyFile
        \CompanyFileService::batchChangeCustomerId($companyId, $fromCustomerId, $toCustomerId, $userId);

        //Opportunity
        \common\library\opportunity\Helper::batchChangeCustomerId($companyId, $fromCustomerId, $toCustomerId, $userId);


        //迁移阿里客户联系人ID, 买家ID、询盘ID、来源店铺
        \common\library\alibaba\trade\Helper::batchChangeCustomerId($clientId, $companyId, $fromCustomerId, $toCustomerId);
        CustomerSyncHelper::batchChangeCustomerId($clientId, $fromCustomerId, $toCustomerId);
    }

    public function actionCompanyMergeAfterTransferData($clientId, $userId, $mainCompanyId, $mergedCompanyIds, $customerTransferMap, $groupId)
    {
        \User::setLoginUserById($userId);

        $mergedCompanyIds = json_decode($mergedCompanyIds, true) ?? [];
        $customerTransferMap = json_decode($customerTransferMap, true) ?? [];

        if (empty($clientId) || empty($userId) || empty($mainCompanyId) || empty($mergedCompanyIds)) {
            return;
        }

        // 移除重复网站动态
        \common\library\trail\Helper::deleteRepeatSiteDynamicOfCompany($clientId, $mainCompanyId, $mergedCompanyIds);

        //迁移客户关联
        foreach ($mergedCompanyIds as $mergedCompanyId) {
            $this->log("transferCompanyRelation: from:{$mergedCompanyId}; to:{$mainCompanyId}");
            $this->mergeAfterTransferCompanyRelation($clientId, $userId, $mergedCompanyId, $mainCompanyId, $groupId);
        }

        //迁移联系人关联
        foreach ($customerTransferMap as $email => $item)
        {
            $toId = $item['to_id'];
            foreach ($item['from_id'] as $fromId)
            {
                $this->log("transferCustomerRelation: companyId:{$mainCompanyId} from:{$fromId}; to:{$toId}");
                $this->mergeAfterTransferCustomerRelation($clientId, $userId, $mainCompanyId, $fromId, $toId);
            }
        }

        $log = '/tmp/performanceV2RefreshPerformanceByReferIds.log';

        \common\library\CommandRunner::run(
            'PerformanceV2',
            'RefreshPerformanceByReferIds',
            [
                'clientId' => $clientId,
                'companyId' => $mainCompanyId,
                'deleteCompanyIds' => implode(",",$mergedCompanyIds)
            ],
            $log,
            0
        );
    }


    /**
     * 修复联系人以及线索联系人 触达次数数据
     * @param $clientId
     * @param $grey
     * @param $greyNum
     * @param $dryRun
     * @return void
     * @throws CDbException
     * @throws ProcessException
     */
    public function actionFixCustomerReachStatus($clientId, $grey = 1, $greyNum = null, $dryRun = 1)
    {
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey && !is_null($greyNum)) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        foreach($clientIds as $client_id) {
            $admin = \common\library\privilege_v3\PrivilegeService::getInstance($client_id)->getAdminUserId();
            if (!$admin) {

                self::info(("no admin user, continue:{$client_id}"));
                continue;
            }
            User::setLoginUserById($admin);
            $db = PgActiveRecord::getDbByClientId($client_id);
            if (!$db) {
                \LogUtil::info("skip for clientId: $client_id, no db");
                return;
            }


            $sql = "UPDATE tbl_customer SET reach_success_count = reach_count WHERE client_id={$client_id} and reach_success_count > reach_count";

            $pg = \PgActiveRecord::getDbByClientId($client_id);
            if (!$dryRun) {
                $pg->getCommandBuilder()->createSqlCommand($sql)->execute();
                \LogUtil::info("process for client:{$client_id},update customer reachStatus");
            } else {
                echo $sql;
                echo PHP_EOL;
            }

            $sql = "UPDATE tbl_lead_customer SET reach_success_count = reach_count WHERE client_id={$client_id} and reach_success_count > reach_count";
            if (!$dryRun) {
                $pg->getCommandBuilder()->createSqlCommand($sql)->execute();
                \LogUtil::info("process for client:{$client_id},update leadCustomer reachStatus");
            } else {
                echo $sql;
            }



            }
        }
}
