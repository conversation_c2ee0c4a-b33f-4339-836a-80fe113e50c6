<?php

use common\library\mail\setting\template\MailSystemTemplate;
use common\library\util\Speed;
use common\library\version\Constant;

/**
 * Created by PhpStorm.
 * User: beenzhang
 * Date: 15/10/14
 * Time: 下午4:44
 */
class MailCommand extends Cron<PERSON>bCommand
{
    private static $notFoundArray = [];

	public function actionFixExposeMail() {

		$arr = [
			[
				'clientId' => 50759,
				'mailId'   => '		
				761250605866,761276583332,777215223257,779299091884,783753274605,785419540051,790974878175,792252699825,794259896694,802833573721,803571242105,810233713946,810755120309,817703153097,819240879068,824364122794,825009775823,831399800799,832691147928 ,867692370018,868173771933,871809752512,872451784641,873645983754,880773405919,882054375840
',
			],
			[
				'clientId' => 1780,
				'mailId'   => '		
754156824604,755213470722,758244301867,793774177866,830056322038
',
			],
			[
				'clientId' => 40143,
				'mailId'   => '		
757802274828,757774362157,758078777100,758129082665,758260046273,758286050057,758340977742,758380169187,758431525609,758477302346,758585493057,758829984676,767258548128,767351455141,767377667085,767416977493,767467884683,767527651072,767541677073,767564269027,767585763223,767618801727,767653347180,767680459598,767720470923,777743667380,777798557925,777830418550,777877274813,785934625888,786032316775,786077515893,786184369111,786222703908,786322180526,786559854248,786625495594,786941649485,787109447590,787191004097,790954687754,790998555438,791156535449,791292249309,791460112334,791513599884,791403296307,791644586484,811003652856,811026083499,811145501878,811450858002,811743167187,811838095214,811958683568,811980082844,812089806174,818391141990,818443937390,818528715192
			',
			],
		];

		foreach ($arr as $item) {

			$where = ' client_id='.$item['clientId'].' and mail_id In ('.$item['mailId'].')';

			LogUtil::info('START----' . $where);

			$db = ProjectActiveRecord::getDbByClientId($item['clientId']);

			$sql = 'SELECT user_id, mail_id, client_id
				FROM tbl_mail
				WHERE ' . $where . '
				    AND folder_id = 11';

			$list = $db->createCommand($sql)->queryAll();

			foreach ($list as $value) {

				try {

					User::setLoginUserById($value['user_id']);

					$sql = 'UPDATE tbl_mail SET folder_id = 2  WHERE mail_id = ' . $value['mail_id'] . ' AND client_id = ' . $value['client_id'];

					$db->createCommand($sql)->execute();

					$mail = new \common\library\mail\Mail($value['mail_id']);

					$version = new \common\library\version\MailVersion($mail->getMailClientId(), $mail->getUserMailId());

					$version->setType(Constant::MAIL_MODULE_ADD);

					$version->setMailId($mail->getMailId());

					$version->add();

				} catch (\Exception $e) {

					\LogUtil::info("CATCH----mail_id {$value['mail_id']} message:{$e->getMessage()}");

					continue;

				}
			}
		}

		\LogUtil::info("END----");
	}


    private static function getNotFoundMailId($mailArray)
    {
        $mailArray = array_unique($mailArray);
        $mailString = implode(',', $mailArray);

        $mailDb = Mail::model()->getDbConnection();
        $sql = "SELECT mail_id FROM tbl_mail WHERE mail_id IN($mailString)";
        $list = $mailDb->createCommand($sql)->queryAll();

        $getMailArray = !empty($list) ? array_column($list, 'mail_id') : [];
        LogUtil::info('countMailArray:' . count($mailArray) . ' countGetMailArray:' . count($getMailArray));

        $diffArray = array_values(array_diff($mailArray, $getMailArray));
        self::$notFoundArray = array_merge(self::$notFoundArray, $diffArray);
    }

    /**
     * @param $userId
     * 清除searchIndex（将已删除的邮件）
     */
    public function actionSyncDelSearchIndex($userId)
    {
        if ($userId <= 0) {
            return;
        }
        User::setLoginUserById($userId);

        $targetFolderId = Mail::FOLDER_DELETE_ID;
        $deleteFlag = Mail::DELETE_FLAG_DELETE;

        $mailDb = Mail::model()->getDbConnection();

        $countSql = "SELECT count(0) FROM tbl_mail WHERE user_id={$userId} AND delete_flag = $deleteFlag AND folder_id=$targetFolderId";
        $totalCount = $mailDb->createCommand($countSql)->queryScalar();
        echo 'total_count:', $totalCount;
        echo "\n";

        $sql = "SELECT mail_id ,user_mail_id FROM tbl_mail WHERE user_id={$userId} AND delete_flag = $deleteFlag AND folder_id=$targetFolderId";
        $list = $mailDb->createCommand($sql)->queryAll(true);

        $map = [];
        foreach ($list as $elem) {
            $map[$elem['user_mail_id']][] = $elem['mail_id'];
        }

        foreach ($map as $userMailId => $idList) {
            LogUtil::info('user_mail_id:' . $userMailId . implode(';', $idList));
            $idList = array_chunk($idList, 50);
            $foreachNum = count($idList);
            $i = 1;
            foreach ($idList as $item) {
                echo $i, '/', $foreachNum;
                echo "\n";
                \common\library\mail\service\SearchEngineService::update($userId, $userMailId, $item);
                $i++;
            }
        }
    }

    /**
     *
     */
    public function actionUserMailAddClientId()
    {
        $userList = UserInfo::model()->findAll();
        foreach ($userList as $userItem) {
            $userId = $userItem['user_id'];
            $clientId = $userItem['client_id'];

            $updateCondition = 'user_id = :user_id';
            $params = [':user_id' => $userId];
            $attributes = ['client_id' => $clientId, 'user_mail_id' => $userId];
            UserMail::model()->updateAll($attributes, $updateCondition, $params);
        }
    }


    public function actionMailTransferTask()
    {


        $taskList = UserMailTransferTask::model()->findAll('status=' . UserMailTransferTask::TASK_STATUS_NO);

        $count = count($taskList);
        $date = date('Y-m-d H:i:s');
        echo "$date 本次共有{$count}个任务\n";
        LogUtil::info("本次共有{$count}个任务\n");

        if (!$count) return;

        foreach ($taskList as $task) {
            $task->status = UserMailTransferTask::TASK_STATUS_ING;
            $task->update(['status']);
        }

        echo "已更改任务状态未正在处理,status:" . UserMailTransferTask::TASK_STATUS_ING . "\n";
        LogUtil::info("已更改任务状态未正在处理,status:" . UserMailTransferTask::TASK_STATUS_ING . "\n");
        foreach ($taskList as $task) {
            try {
                $beginTime = time();
                echo "处理任务task:{$task->task_id}中,client_id:{$task->client_id},old_user_id:{$task->old_user_id},new_user_id:{$task->new_user_id} \n";
                LogUtil::info("处理任务task:{$task->task_id}中,client_id:{$task->client_id},old_user_id:{$task->old_user_id},new_user_id:{$task->new_user_id} \n");
                UserMailService::transfer($task->client_id, $task->old_user_id, $task->new_user_id, $task->user_mail_id);
                $task->status = UserMailTransferTask::TASK_STATUS_FINISH;
                $task->update('status');
                $endTime = time();
                $time = $endTime - $beginTime;
                echo "处理任务task:{$task->task_id} 完成,共耗时:{$time}秒 \n";
                LogUtil::info("处理任务task:{$task->task_id} 完成,共耗时:{$time}秒 \n");


            } catch (Exception $e) {
                $task->status = UserMailTransferTask::TASK_STATUS_EXCEPTION;
                $task->update(['status']);

                $code = $e->getCode();
                $file = $e->getFile();
                $line = $e->getLine();
                $message = $e->getMessage();

                $error = "处理任务task_id:{$task->task_id} 出错! error code:{$code} ,message :{$message} ,file;{$file},line:{$line}  \n";

                echo $error;
                LogUtil::error($error);

            }
        }

    }

    public function actionSetDefaultUserMail($clientId = 0)
    {
        $clientList = [];
        if ($clientId > 0) {
            $clientList[] = array('client_id' => $clientId);
        } else {
            $clientListCondition = 'enable_flag = 1 AND status = 1';
            $clientList = Client::model()->findAll($clientListCondition);
        }
        foreach ($clientList as $client) {
            $clientId = $client['client_id'];
            echo "begin $clientId \n";
            if($client['mysql_set_id'] == 11)
                continue;
            ProjectActiveRecord::setConnection(ProjectActiveRecord::getDbByClientId($clientId));
            //get all setting
            $clientAllSetting = MailSettings::model()->findAll('client_id = :client_id', [':client_id' => $clientId]);
            foreach ($clientAllSetting as $setting) {
                $settingUserId = $setting['user_id'];
                echo "userId: $settingUserId \n";
                $allUserMail = UserMail::findAllByUserId($clientId, $settingUserId);
                foreach ($allUserMail as $oneUserMail) {
                    $userId = $oneUserMail['user_id'];
                    if ($userId == 0) continue;
                    $userMailId = $oneUserMail['user_mail_id'];
                    echo "userMailId: $userMailId \n";
                    MailSettings::model()->updateAll(
                        ['default_user_mail' => $userMailId],
                        'user_id=:user_id AND client_id=:client_id AND default_user_mail=0',
                        [':user_id' => $userId, ':client_id' => $clientId]
                    );
                }
                echo "---------------------- \n";
            }
        }
    }

    /**
     * 删除重复邮件
     *
     * @param $clientId
     * @param $userId
     */
    public function actionDeleteDuplicateMail($clientId, $userId)
    {
        User::setLoginUserById($userId);
        $mailModel = Mail::model();
        $con = ProjectActiveRecord::getDbByClientId($clientId);
        $sql = "select min(mail_id) as mail_id, `subject`, email_size,sender from tbl_mail where client_id= $clientId and user_id=$userId AND mail_type=2 group by email_size,sender, `subject` having count(*) > 1 ";
        echo $sql . "\n";
        $mails = $con->createCommand($sql)->queryAll();
        echo '一共找到' . count($mails) . '重复邮件'."\n";
        foreach ($mails as $repeat_mail) {
            $condition = 'client_id=:client_id and user_id=:user_id and mail_id!=:mail_id and `subject`=:subject and delete_flag=0 and email_size=:email_size and sender=:sender';
            $params = [
                ':client_id' => $clientId,
                ':user_id' => $userId,
                ':mail_id' => $repeat_mail['mail_id'],
                ':subject' => $repeat_mail['subject'],
                ':email_size' => $repeat_mail['email_size'],
                ':sender' => $repeat_mail['sender'],
            ];
            $dataList = $mailModel->findAll($condition, $params);

            echo '找到' . count($dataList) . "封重复的邮件\n";
            LogUtil::info('找到' . count($dataList) . "封重复的邮件\n");

            $mailIds = array_column($dataList, 'mail_id');
            $mailIdCount = count($mailIds);
            for ($i = 0; $i < $mailIdCount; $i += 10000) {
                $ids = array_slice($mailIds, $i, 10000);
                if (count($ids) == 0) {
                    continue;
                }
                EmailUtil::deleteMails($clientId, $userId, $ids);
                foreach (User::getUserObject($userId)->getUserMailIds() as $user_mail_id) {
                    \common\library\mail\service\SearchEngineService::delete($userId,$user_mail_id,$ids);
                }
            }
        }
    }

    public function actionDeleteMailsFromFile($client_id, $user_id, $mail_id_file)
    {
        $mail_ids = trim(file_get_contents($mail_id_file));
        if (strlen($mail_ids) == 0) {
            echo "文件长度为0" . PHP_EOL;
            return -1;
        }
        $this->actionDeleteMails($client_id, $user_id, $mail_ids);
    }

    public function actionDeleteMails($client_id, $user_id, $mail_ids)
    {
        $mail_ids = explode(',', $mail_ids);
        if (count($mail_ids) <= 0) {
            echo "mail_id 为空" . PHP_EOL;
            return -1;
        }
        User::setLoginUserById($user_id);
        EmailUtil::deleteMails($client_id, $user_id, $mail_ids);
    }

    public function actionCleanErrorDraftMail(){
        $db = Yii::app()->db;

        $clientList = Client::model()->findAll('enable_flag=1');

        foreach ($clientList as $client){
            echo "client_id :{$client->client_id} \n";
            if($client->mysql_set_id == 11) continue ;
            $clientDb = ProjectActiveRecord::getDbByClientId($client->client_id);

            $userMailList = UserMail::model()->findAll('client_id='.$client->client_id);
            foreach ($userMailList as $userMail){


                $mailIds = $clientDb->createCommand("  SELECT mail_id  from tbl_mail where user_mail_id={$userMail->user_mail_id} and folder_id=5 and mail_type=0 and update_time >='2016-12-07' ")->queryColumn();
                $count = count($mailIds);
                if($count == 0) continue;
                echo "client_id:{$client->client_id} user_mail_id:{$userMail->user_mail_id} mail count : $count \n";

                if(!empty($mailIds)) {
//                    $mailRet = $clientDb->createCommand('update tbl_mail set folder_id=9 and  delete_flag=2  where  user_mail_id='.$userMail->user_mail_id.' and  mail_type=0 and  mail_id in ('.implode(',',$mailIds).')')->execute();
//
//                    echo "mail ret count : $mailRet \n";
//
//                    $feedRet = $clientDb->createCommand("DELETE  from tbl_user_feed where client_id={$client->client_id} and node_type=203  and refer_id in (".implode(',',$mailIds).')')->execute();
//
//                    echo  "feed ret count : $feedRet \n";
                }
            }
        }
    }


    /**
     * 如果有用户跨页全选误删邮件 用这个恢复 会处理搜索索引 回收箱
     * 改account list和begin end即可
     */
    public function actionRestore()
    {
        $accountList = [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>'
        ];

        $begin = '2017-01-09 12:00:00';
        $end = '2017-01-10 18:30:32';

        foreach ($accountList as $account)
        {
            $account = trim(strtolower($account));
            $user = User::getUserObjectByEmail($account);

            User::setLoginUser($account);

            echo "processing $account\n";

            $userId = $user->getUserId();

            $userMailIds = $user->getUserMailIds();

            $db = ProjectActiveRecord::getDbByUserId($userId);

            ProjectActiveRecord::setConnection($db);

            $referList = $db->createCommand("select refer_id from tbl_recycle where user_id=$userId and type=1 and create_time>='$begin' and create_time<='$end'")->queryColumn();

            $count = count($referList);

            echo "count $count\n";

            $gap = 5000;

            $list = array_chunk($referList, $gap);

            $num = 0;

            foreach ($list as $mailIds)
            {
                $sql = implode(',', $mailIds);
                $db->createCommand("update tbl_mail set folder_id=mail_type,delete_flag=0 where mail_id in ($sql) and folder_id=9 and delete_flag=2")->execute();

                foreach ($userMailIds as $userMailId)
                    \common\library\mail\service\SearchEngineService::update($userId, $userMailId, $mailIds);

                echo "$num/$count\n";

                $num += $gap;
            }

            $db->createCommand("delete from tbl_recycle where user_id=$userId and type=1 and create_time>='$begin' and create_time<='$end'")->execute();
            echo "deleted recycle\n";
        }
    }

    /**
     * @param $userId
     * @throws ProcessException
     * 更新邮件搜索索引
     */
    public function actionUpdateSearchMailIndex($userId){
        $user = User::getUserObject($userId);
        User::setLoginUserById($userId);

        $userId = $user->getUserId();
        $userMailIds = $user->getUserMailIds();

        $db = ProjectActiveRecord::getDbByUserId($userId);
        ProjectActiveRecord::setConnection($db);
        $begin = '1970-01-01 00:00:01';
        $end = '2017-03-06 23:59:59';
        foreach ($userMailIds as $userMailId) {
            $mailIdList = $db->createCommand("select mail_id from tbl_mail where user_id=$userId and user_mail_id = $userMailId and create_time>='$begin' and create_time<='$end'")->queryColumn();

            $count = count($mailIdList);
            echo "userMailId:$userMailId---> mailCount: $count\n";

            $num = 0;
            $gap = 5000;
            $list = array_chunk($mailIdList, $gap);
            foreach ($list as $mailIds) {
                \common\library\mail\service\SearchEngineService::update($userId, $userMailId, $mailIds);
                $num += $gap;
                echo "$num/$count\n";
            }
        }
    }


    public function actionAddMail($userId,$email,$password,$name='' , $smtpName = '' ,$smtpPassword = '',$protocol=''){

        User::setLoginUserById($userId);

        $user  = User::getLoginUser();

//        $password = SecurityUtil::tripleDESEncrypt($password, 'X1a0mAnCrM');

        $server = new UserMailService($userId);

        $params = $server->getEMailParams($email);

        if(!empty($name)){
            $params['receive_server_user_name'] = $name;
            $params['receive_server_pwd_enc'] = $password;
            $params['multi_user_name_flag'] = 1;
        }

        if(!empty($protocol)){
            $params['receiveProtocol'] = $protocol;
        }

        if(!empty($smtpName)){
            $params['smtp_user_name'] = $smtpName;
            $params['smtp_pwd_enc'] = $smtpPassword;
            $params['multi_user_name_flag'] = 2;
        }


        var_dump($params);

        $server->setEmail($email);
        $server->setParams($params);
        $server->setPassword($password);

        $result = $server->canBindEmail();
        if(!$result){
            throw  new RuntimeException($server->getErrorMsg());
        }

        $result = $server->check();
        if(!$result){
            throw  new RuntimeException($server->getErrorMsg());
        }

        //save
        $params = $server->getParams();

        $email_server_type = NULL;
        if($params['receiveProtocol'] == 'pop'){
            $email_server_type = UserMail::TYPE_POP;
        }else if($params['receiveProtocol'] == 'imap'){
            $email_server_type = UserMail::TYPE_IMAP;
        }else if($params['receiveProtocol'] == 'exchange') {
            $email_server_type = UserMail::TYPE_EXCHANGE;
        }else{
            throw new ProcessException("email_server_type error,".$params['receiveProtocol']);
        }

        $now = date('Y-m-d H:i:s');

        $userMailObj = $server->getUserMailObj();
        // 邮箱存在 修改密码
        if(!$userMailObj){
            $userMailObj = UserMail::findPersonalOrMainCommonUserMailByEmail($user->getClientId(),$email);
        }

        if (!$userMailObj)
        {
//            $this->isNew = true;
            $userMailObj = new UserMail();
            //兼容问题,等全量上线时在
            $db = Yii::app()->db;
            $accountMaxId = $db->createCommand('select user_mail_id from tbl_user_mail order by user_mail_id desc limit 1')->queryScalar();

            if($accountMaxId < ********){
                $accountMaxId = ********;
            }else{
                $accountMaxId++;
            }

            $userMailObj->user_mail_id=$accountMaxId;
            $userMailObj->user_id = $userId;
            $userMailObj->email_address = trim(strtolower($email));
            $userMailObj->create_time = $now;
        }else{

            if($userMailObj->transfer_flag == UserMail::MAIL_TRANSFER_FLAG_TRUE){
                throw new RuntimeException('邮箱操作过于频繁，当前操作无法执行，请稍后片刻');
            }

            //协议不能从imap改为pop
            if($userMailObj->email_server_type ==UserMail::TYPE_IMAP && $email_server_type == UserMail::TYPE_POP){
                throw  new RuntimeException('请使用imap协议绑定');
            }

            //协议不能从exchange改为imap、pop
            if($userMailObj->email_server_type == UserMail::TYPE_EXCHANGE && $email_server_type != UserMail::TYPE_EXCHANGE) {
                throw  new RuntimeException('请使用exchange协议绑定', 1410);
            }

            //保存旧的拥有者,只有改变拥有者,才会修改user_id,已删除的除外
//            $server->oldUserId = $userMailObj->user_id;
            $userMailObj->user_id = $userId;
            $userMailObj->enable_flag = 1;

            //更新绑定时间
//            if($this->oldUserId != $this->userId && $this->userId != 0){
//                $userMailObj->create_time = $now;
//            }


        }

        $userMailObj->valid_flag = UserMail::VALID_FLAG_SUCCESS;
        $userMailObj->email_server_type = $email_server_type;
        $userMailObj->receive_server = $params['receiveHost'];
        $userMailObj->receive_port = $params['receivePort'];
        $userMailObj->receive_ssl =  $params['receiveSSL'];
        $userMailObj->send_server = $params['smtpHost'];
        $userMailObj->send_port = $params['smtpPort'];
        $userMailObj->send_ssl = $params['smtpSSL'];
        $userMailObj->setEncodePassword($password);
        $userMailObj->update_time = $now;

        $userMailObj->client_id = $user->getClientId();

        if(isset($params['multi_user_name_flag']) && $params['multi_user_name_flag'] >0 ){
            $multiFlag=$params['multi_user_name_flag'];
            $userMailObj->multi_user_name_flag=$multiFlag;
            if(empty($userMailObj->email_pwd_salt)){
                $userMailObj->email_pwd_salt =SecurityUtil::createRandomSalt();
            }
            $salt= $userMailObj->email_pwd_salt;
            switch ($multiFlag) {

                case UserMail::MULTI_FLAG_SIMPLE_ACCOUNT:

                    $userMailObj->receive_server_user_name= trim($params['receive_server_user_name']);
                    $userMailObj->receive_server_pwd_enc=SecurityUtil::tripleDESEncrypt($params['receive_server_pwd_enc'], $salt);

                    $userMailObj->smtp_user_name=null;
                    $userMailObj->smtp_pwd_enc=null;

                    break;

                case UserMail::MULTI_FLAG_DOUBLE_ACCOUNT:

                    $userMailObj->receive_server_user_name= trim($params['receive_server_user_name']);
                    $userMailObj->receive_server_pwd_enc= SecurityUtil::tripleDESEncrypt($params['receive_server_pwd_enc'], $salt);
                    $userMailObj->smtp_user_name=trim($params['smtp_user_name']);
                    $userMailObj->smtp_pwd_enc=SecurityUtil::tripleDESEncrypt(trim($params['smtp_pwd_enc']), $salt);

                    break;
                default:
                    $userMailObj->multi_user_name_flag = 0;
                    $userMailObj->receive_server_user_name =null;
                    $userMailObj->receive_server_pwd_enc=null;
                    $userMailObj->smtp_user_name=null;
                    $userMailObj->smtp_pwd_enc=null;
                    break;
            }
        }
        else
        {
            $userMailObj->multi_user_name_flag = 0;
            $userMailObj->receive_server_user_name =null;
            $userMailObj->receive_server_pwd_enc=null;
            $userMailObj->smtp_user_name=null;
            $userMailObj->smtp_pwd_enc=null;
        }

        $userMailObj->forbidden_flag = UserMail::FORBIDDEN_FLAG_FALSE;
//        if(SpaceService::checkIsOverLimit($this->clientId)){//云空间超量，不能收发件
//            $userMailObj->forbidden_flag = UserMail::FORBIDDEN_FLAG_TRUE;
//        }

        var_dump($userMailObj->getAttributes());
//        $userMailObj->save();

        //保存一下userMailId
//        $this->userMailId = $userMailObj->user_mail_id;


        return true;
    }



    /**
     * @param $client_id
     */
    public function actionSyncDelSearchIndexByClientId($client_id)
    {
        $db = ProjectActiveRecord::getDbByClientId($client_id);
        if(!$db) {
            throw new RuntimeException('初始化 db connection 失败');
        }
        $targetFolderId = Mail::FOLDER_DELETE_ID;
        $deleteFlag = Mail::DELETE_FLAG_DELETE;


        $countSql = "SELECT count(0) FROM tbl_mail WHERE client_id={$client_id} AND delete_flag = $deleteFlag AND folder_id=$targetFolderId";

        $totalCount = $db->createCommand($countSql)->queryScalar();
        echo 'total_count:', $totalCount,PHP_EOL;

        $sql = "SELECT mail_id ,user_mail_id,user_id FROM tbl_mail WHERE client_id={$client_id} AND delete_flag = $deleteFlag AND folder_id=$targetFolderId";
        $MailList = $db->createCommand($sql)->queryAll(true);

        $map = [];
        foreach ($MailList as $elem) {
            $map[$elem['user_mail_id']]['mail_id'][] = $elem['mail_id'];
            $map[$elem['user_mail_id']]['user_id'] = $elem['user_id'];
        }

        $success = 0;
        foreach ($map as $userMailId => $item) {
            $userId = $item['user_id'];
            $mailIds = $item['mail_id'];
            $mailIds = array_chunk($mailIds, 50);
            $foreachNum = count($mailIds);
            foreach ($mailIds as $item) {
                $str = "user_id => %d \t userMailId=> %d \t 同步删除索引数量=> %d \n";
                $count = count($item);
                echo sprintf($str,$userId, $userMailId,$count);
                $result = \common\library\mail\service\SearchEngineService::update($userId, $userMailId, $item);
                if($result = 1) {
                    $success += $count;
                }
            }
        }


        echo '成功执行：',$success,PHP_EOL;
    }

    public function actionBuildMailIndexForClient($client_id)
    {
        $userList = UserInfo::findAllByClientId($client_id);
        //建索引
        foreach ($userList as $user) {
            $user_id = $user->user_id;
            $user_mail_ids = Yii::app()->db->createCommand("select user_mail_id from tbl_user_mail where client_id=$client_id and user_id=$user_id")->queryColumn();
            foreach ($user_mail_ids as $user_mail_id)
            {

                $api = new \common\library\api\InnerApi('mail_search');

                try
                {
                    $api->call('rebuild_index', ['userId' => $user->user_id,'userMailId' => $user_mail_id]);
                }
                catch (Exception $e)
                {
                    LogUtil::info($e->getMessage());
                }

                try
                {
                    $api->call('rebuild_attachment_index', ['userId' => $user->user_id,'userMailId' => $user_mail_id]);
                }
                catch (Exception $e)
                {
                    LogUtil::info($e->getMessage());
                }
            }
        }
    }

public function actionTransferInboxMsgToMailStatisticsDispatch($beginId=0){

        self::info('maxId:'.ProjectActiveRecord::produceAutoIncrementId());
        $dbList = Yii::app()->db->createCommand('select set_id from tbl_db_set where type=1')->queryColumn();

        foreach ( $dbList as $dbSetId)
        {

//            if($dbSetId <23 ||  $dbSetId >=230)
//                continue;
//
            if($dbSetId <230 ||  $dbSetId >=400)
                continue;

//            if($dbSetId<400)
//                continue;

            $log = '/tmp/transferInboxMsgToMailStatisticsDb'.$dbSetId.'.log';
            \common\library\CommandRunner::run(
                'mail',
                'transferInboxMsgToMailStatisticsDb',
                [
                    'setId' => $dbSetId,
                    'beginId' => $beginId,
                ],
                $log
            );

            $db = ProjectActiveRecord::getDbByDbSetId($dbSetId);
            $maxId = $db->createCommand("select msg_id from tbl_inbox_msg order by msg_id desc limit 1")->queryScalar();
            self::info('dbsetId:'.$dbSetId.'maxid:'.$dbSetId);
            \common\library\CommandRunner::run(
                'mail',
                'transferInboxMsgToMailStatisticsDb',
                [
                    'setId' => $dbSetId,
                    'beginId' => $beginId,
                ],
                $log
            );
        }

    }

    public function actionTransferInboxMsgToMailStatisticsDb($setId,$beginId=0){

        ini_set("memory_limit", "15000M");

        $clientList = Yii::app()->db->createCommand('select client_id from tbl_client where mysql_set_id='.$setId)->queryColumn();
        $db = ProjectActiveRecord::getDbByDbSetId($setId);
        $maxId = $db->createCommand("select msg_id from tbl_inbox_msg order by msg_id desc limit 1")->queryScalar();
        self::info('dbsetId:'.$setId.'maxid:'.$maxId);

//        $db->createCommand('truncate table `tbl_mail_statistics_day`')->execute();
//        self::info('truncate table `tbl_mail_statistics_day` : count=>'.$db);

        foreach ( $clientList as $clientId ){
            self::info('client_id:'.$clientId);
            $this->actionTransferInboxMsgToMailStatistics($clientId,$beginId);

        }
    }


    public function actionTransferInboxMsgToMailStatistics($clientId, $beginId = 0)
    {
        $db = ProjectActiveRecord::getDbByClientId($clientId);

        if ($beginId)
            $extra = " and msg_id > $beginId and msg_id < 4634866768";


        $selectSql  ='SELECT client_id, user_id,email_id,DATE_FORMAT(happened_time,\'%Y%m%d\') AS DAY,msg_type,COUNT(1) FROM tbl_inbox_msg WHERE client_id='.$clientId.$extra.' GROUP BY user_id,email_id,DAY,msg_type; ';
        $reader = $db->createCommand($selectSql)->query();
        $reader->setFetchMode(PDO::FETCH_NUM);

        $map = [];
        $sendSqlPrefix = "INSERT INTO `tbl_mail_statistics_day`(`client_id`,`user_id`,`email`,`date`, `send_mail_count`, `create_time`) VALUES ";
        $sendSqlSuffix = "ON DUPLICATE KEY UPDATE send_mail_count = send_mail_count+values(`send_mail_count`);";
        $receiveSqlPrefix = "INSERT INTO `tbl_mail_statistics_day`(`client_id`,`user_id`,`email`,`date`,`receive_mail_count`, `create_time`) VALUES ";
        $receiveSqlSuffix = "ON DUPLICATE KEY UPDATE receive_mail_count = receive_mail_count+values(`receive_mail_count`);";
        $sendValues = [];
        $receiveValues = [];
        $createTime = date('Y-m-d H:i:s');


        foreach ($reader as $elem)
        {
            if(!isset($map[$elem[2]]) )
            {
                $map[$elem[2]] = UserAccount::getAccountByUserId($elem[2]);;
            }

            $email = Util::escapeDoubleQuoteSql($map[$elem[2]]);

            if(empty($email))
                continue;

            if( $elem[4] ==1)
            {
                $sendValues[] ="({$elem[0]},{$elem[1]}, '{$email}','{$elem[3]}',{$elem[5]},'{$createTime}')";;
            }else
            {
                $receiveValues[] ="({$elem[0]},{$elem[1]}, '{$email}','{$elem[3]}',{$elem[5]},'{$createTime}')";;
            }

            if(count($sendValues) >=2000)
            {
                $sql = $sendSqlPrefix . implode(',', $sendValues) . $sendSqlSuffix;
//                var_dump($sql);
                $rowCount = $db->createCommand($sql)->execute();
//
                self::info("update count:".count($sendValues)." row: $rowCount");
                $sendValues = [];
            }

            if(count($receiveValues) >=2000)
            {
                $sql = $receiveSqlPrefix . implode(',', $receiveValues) . $receiveSqlSuffix;
//                var_dump($sql);
                $rowCount = $db->createCommand($sql)->execute();
//
                self::info("update count:".count($receiveValues)." row: $rowCount");
                $receiveValues = [];
            }

        }


        if(count($sendValues) >0)
        {
            $sql = $sendSqlPrefix . implode(',', $sendValues) . $sendSqlSuffix;
            $rowCount = $db->createCommand($sql)->execute();

            self::info("update count:".count($sendValues)." row: $rowCount");
            $sendValues = [];
        }

        if(count($receiveValues) >0)
        {
            $sql = $receiveSqlPrefix . implode(',', $receiveValues) . $receiveSqlSuffix;
            $rowCount = $db->createCommand($sql)->execute();

            self::info("update count:".count($receiveValues)." row: $rowCount");
            $receiveValues = [];
        }

    }


    public function actionTransferCustomerStatisticsToMailStatisticsDispatch(){

        $dbList = Yii::app()->db->createCommand('select set_id from tbl_db_set where type=1')->queryColumn();

        foreach ( $dbList as $dbSetId)
        {
            if($dbSetId >=100)
                continue;

            $log = '/tmp/transferCustomerStatisticsToMailStatisticsDb'.$dbSetId.'.log';
            \common\library\CommandRunner::run(
                'mail',
                'transferCustomerStatisticsToMailStatisticsDb',
                [
                    'setId' => $dbSetId,
                ],
                $log
            );
        }

    }

    public function actionTransferCustomerStatisticsToMailStatisticsDb($setId){

        ini_set("memory_limit", "15000M");


        $clientList = Yii::app()->db->createCommand('select client_id from tbl_client where mysql_set_id='.$setId)->queryColumn();

        foreach ( $clientList as $clientId ){
            self::info('client_id:'.$clientId);
            $this->actionTransferCustomerStatisticsToMailStatistics($clientId);
        }
    }

    public function actionTransferCustomerStatisticsToMailStatistics($clientId)
    {
        $db = ProjectActiveRecord::getDbByClientId($clientId);
        $reader = $db->createCommand("select client_id, user_id,target_user_id,DATE_FORMAT(date,'%Y%m%d') AS DAY,open_count,create_time from tbl_customer_statistics_day where client_id=$clientId and date>='2018-03-14' and date<='2018-03-28'")->query();
        $reader->setFetchMode(PDO::FETCH_NUM);

        $map = [];
        $sqlPrefix = "INSERT INTO `tbl_mail_statistics_day`(`client_id`,`user_id`,`email`,`date`, `open_count`, `create_time`) VALUES ";
        $sqlSuffix = "ON DUPLICATE KEY UPDATE open_count = values(`open_count`);";
        $values = [];
        foreach ( $reader as $elem )
        {

            if(!isset($map[$elem[2]]) )
            {
                $map[$elem[2]] = UserAccount::getAccountByUserId($elem[2]);;
            }

            $email = Util::escapeDoubleQuoteSql($map[$elem[2]]);

            if(empty($email))
                continue;

            if($elem[4] == 0)
                continue;

            $values[] = "({$elem[0]},{$elem[1]}, '{$email}','{$elem[3]}',{$elem[4]},'{$elem[5]}')";

            if(count($values) >=1000)
            {
                $sql = $sqlPrefix . implode(',', $values) . $sqlSuffix;
//                var_dump($sql);
                $rowCount = $db->createCommand($sql)->execute();

                self::info("update count:".count($values)." row: $rowCount");
                $values = [];
            }
        }

        if(count($values) >0)
        {
            $sql = $sqlPrefix . implode(',', $values) . $sqlSuffix;
            $rowCount = $db->createCommand($sql)->execute();
            self::info("update count:".count($values)." row: $rowCount");
//            var_dump($sql);
            $values = [];
        }

    }

    public function actionTodoRemindDispatcher(){
        $adminDb = Yii::app()->db;
        $sql = 'SELECT set_id FROM tbl_db_set where type=1 AND enable_flag = 1';
        $mysqlDbList = $adminDb->createCommand($sql)->queryColumn();

        $today = date('Ymd');
        $processTime = time();
        foreach ($mysqlDbList as $setId) {
            $log = "/dev/null";
            \common\library\CommandRunner::run(
                'mail',
                'todoRemind',
                [
                    'mysqlDbSet' => $setId,
                    'processTime' => $processTime,
                ],
                $log
            );
        }
    }


    /**
     * @param $mysqlDbSet
     * @param string $processTime
     * @param int $remindType  1:当前这一分钟到提醒时间，2:当前这一分钟到 提醒时间+已过期
     */
    public function actionTodoRemind($mysqlDbSet, $processTime = '', $remindType = 2){
        Speed::log('remindStart');
        $processTime = is_numeric($processTime) ? date('Y-m-d H:i', $processTime) : date('Y-m-d H:i');

        $sql = "select client_id from tbl_client where mysql_set_id = $mysqlDbSet";
        $clientIdList = Yii::app()->db->createCommand($sql)->queryColumn();

        foreach ($clientIdList as $clientId) {
            try {
                $adminId = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();
                if(!$adminId){
                    LogUtil::info("clientId:{$clientId} admin user not exist!");
                    continue;
                }
                User::setLoginUserById($adminId);

                $userList = new \common\library\account\UserList();
                $userList->setClientId($clientId);
                $userList->setEnableFlag(true);
                $userIdList = $userList->findUserId();
                if (empty($userIdList)) {
                    LogUtil::info("client_id:$clientId empty users");
                    continue;
                }

                foreach ($userIdList as $userId) {
                    \common\library\mail\Helper::userTodoRemind($clientId, $userId, $processTime, $remindType);
                }
            } catch (Exception $e) {
                LogUtil::info("clientId:{$clientId}:{$e->getMessage()}");
            }
        }
        Speed::log('remindEnd');
    }

    /**
     * @deprecated  废弃, 迁至MailTask
     * @param int $clientId
     * @param int $startClientId
     * @param int $endClientId
     */
    public function actionClearJunk($clientId = 0, $startClientId = 0, $endClientId = 0)
    {
        $clients = $this->getClientList($clientId, false, null, null, $startClientId, $endClientId);
        foreach ($clients as $client) {
            LogUtil::info("begin:-------client_id:$clientId" . PHP_EOL);
            try {
                $this->clearClientJunk($client);
            } catch (Exception $exception) {
                LogUtil::info("exception:------ client_id" . $clientId . PHP_EOL);
            }
            LogUtil::info("end:-------client_id:$clientId" . PHP_EOL);
            sleep(1);
        }
        echo date('Y-m-d'). "finish!".PHP_EOL;
    }

    private function clearClientJunk($client)
    {
        $clientId = $client['client_id'];
        $now = time();
        $ruleTime = 86400 * 30;
        $referenceTime = date('Y-m-d H:i:s', $now - $ruleTime);
        ProjectActiveRecord::setConnection(ProjectActiveRecord::getDbByClientId($clientId));
        $users = Yii::app()->account_base_db->createCommand("SELECT user_id FROM `tbl_user_info` where client_id={$clientId}")->queryAll(true);
        foreach ($users as $user) {
            $userId = $user['user_id'];
            LogUtil::info("begin:-------user_id: {$userId}");
            try {
                $mailBatchOperator = new \common\library\mail\MailListBatchOperate($userId);
                $mailBatchOperator->setParams([
                    'folder_id'    => [\Mail::FOLDER_JUNK_ID],
                    'type'         => 'junk',
                    'endUpdateTime' => $referenceTime
                ]);
                $affectedRows = $mailBatchOperator->cleanJunkMail();
                LogUtil::info("end:-------user_id: {$userId}: rows: {$affectedRows}" . PHP_EOL);
            } catch (Exception $exception) {
                $message = $exception->getMessage();
                LogUtil::info("fail:-------user_id:{$userId}: {$message}" . PHP_EOL);
            }
        }
    }

    public function actionMailClassify($clientId, $userId, $userMailId, $mailIds)
    {
        LogUtil::info("client_id: $clientId user_id $userId user_mail_id $userMailId mail_ids " . $mailIds);

        $mailIds = explode(',', $mailIds);

        \common\library\ai\classify\Dispatcher::scriptProcess($clientId, $userId, $userMailId, $mailIds);
    }

    public function actionClassifyMailConsumer($workerNum = null, $cpuAffinityFlag = 0)
    {
        //释放本机所有关联
        \common\library\ai\classify\ClassifyMailConsumer::releaseUserMailConsumer();

        if ($workerNum === null)
        {
            $workerNum = 50;
            //非正式和灰度环境
            if (\Yii::app()->params['env'] !== 'production' && \Yii::app()->params['env'] !== 'grey'){
                $workerNum = 10;
            }
        }

        $consumer = new \common\library\ai\classify\ClassifyMailConsumer($workerNum);
        $consumer->setCpuAffinityFlag($cpuAffinityFlag);
        $consumer->start();
    }

    public function actionMailClassifyHistoryDispatch()
    {
        /** @var CDbConnection $db */
        $db = Yii::app()->account_base_db;
        $nowDate = date('Y-m-d H:i:s');
        $privilege = \common\library\privilege_v3\PrivilegeConstants::FUNCTIONAL_AI_CLASSIFY_EMAIL;
        $sql = "select c.client_id, c.pg_set_id from tbl_client_privilege p join tbl_client c on  c.client_id=p.client_id where c.valid_to >= '{$nowDate}' and p.privilege='{$privilege}' and p.enable_flag=1 order by create_time asc";
        $list = $db->createCommand($sql)->queryColumn();
        $listMap = array_reduce($list, function ($carry, $item){
            $carry[$item['pg_set_id']][] = $item['client_id'];
            return $carry;
        }, []);

        $yiic = Yii::getPathOfAlias('application') . "/" . Yii::app()->params['yiic'];
        foreach ( $listMap as $pgSetId => $clientIds)
        {
            $pgDb = PgActiveRecord::getDbByDbSetId($pgSetId);
            $pgSql = "select client_id from tbl_ai_classify_apply_settings where mail_enable_apply=1 and history_flag=0 and client_id in (".implode(',',$clientIds).')';
            $execClientIds = $pgDb->createCommand($pgSql)->queryColumn();
            if( empty($execClientIds) )
                continue;

            $log = "/tmp/mail_classify_history_set_".$pgSql;
            $execClientIds = implode(',', $execClientIds);
            $exec = "nohup $yiic mail mailClassifyHistory  --clientId={$execClientIds} >> $log 2>&1 &";
            LogUtil::info($exec);
            exec($exec);
        }

    }

    public function actionMailClassifyHistory($clientId)
    {
        $clientIds = array_filter(explode(',', $clientId));

        foreach ( $clientIds as $id )
        {
            \common\library\ai\classify\Dispatcher::piMailHistoryScriptProcess($id);
        }

    }

    /**
     * been
     * 清除类型为编辑的邮件版本号（临时帮客户清除，后续需要一个定期脚本处理）
     */
    public function actionCleanEditVersion()
    {
        $clientId = 0;
        $userMailIds = [];

        $db = ProjectActiveRecord::getDbByClientId($clientId);
        foreach ($userMailIds as $userMailId) {
            echo "$userMailId-------start \n";

            $maxIdSql = "select max(version) from tbl_mail_version where user_mail_id=$userMailId and type=2";
            $maxId = $db->createCommand($maxIdSql)->queryScalar();

            $currentId = 0;
            while ($currentId < $maxId){
                $currentId += 10000;

                echo "$currentId/$maxId \n";
                $deleteSql = "delete from tbl_mail_version where user_mail_id=$userMailId and version <= $currentId and type=2";
                $db->createCommand($deleteSql)->execute();
            }
            echo "$userMailId-------end \n";
        }

    }

    public function actionFixVersion($clientId = 0, $startClientId = 0, $endClientId = 0)
    {
        $clients = $this->getClientList($clientId, false, null, null, $startClientId, $endClientId);
        foreach ($clients as $client) {
            echo "begin:-------client_id:$clientId". PHP_EOL;
            try {
                $this->fixClientMailVersion($client);
            } catch (Exception $exception) {
                echo "exception:------ client_id".$exception->getMessage().PHP_EOL;
            }
            echo "end:-------client_id:$clientId". PHP_EOL;
        }
        echo date('Y-m-d'). "finish!".PHP_EOL;
    }

    private function fixClientMailVersion($client)
    {
        $clientId = $client['client_id'];
        ProjectActiveRecord::setConnection(ProjectActiveRecord::getDbByClientId($clientId));
        // 查询该client 下的所有user_mail
        $sql = "select user_mail_id from tbl_user_mail where client_id = {$clientId} and enable_flag = 1";
        $userMailList = Yii::app()->db->createCommand($sql)->queryAll(true);
        if (empty($userMailList))  {
            return ;
        }
        $db = ProjectActiveRecord::getDbByClientId($clientId);

        $userMailIDs = implode(',', array_column($userMailList, 'user_mail_id'));
        $limit = 5000;
        $totalSql = "select count(1) from tbl_mail_version where user_mail_id in ($userMailIDs) and type = 12";
        $total = $db->createCommand($totalSql)->queryScalar();
        if ($total == 0) {
            return ;
        }
        $totalCount = ceil($total / $limit);
        $sql = "select * from tbl_mail_version where user_mail_id in ($userMailIDs) and type = 12 limit {$limit} ";

        for ($i = 0; $i < $totalCount; $i ++) {
            $start = $i * $limit;
            $listSql = $sql. " offset {$start}";
            $data = $db->createCommand($listSql)->queryAll(true);
            foreach ($data as $mailVersion) {
                $mailId = $mailVersion['mail_id'];
                $userMailID= $mailVersion['user_mail_id'];
                $mailSql = "select count(1) from tbl_mail where mail_id = {$mailId} and time_flag = 1";
                $isTimeFlag = $db->createCommand($mailSql)->queryScalar();
                if ($isTimeFlag) {
                    echo "{$mailId} fixed".PHP_EOL;
                    // 删除这个版本号
                    $delSql = "delete from tbl_mail_version where user_mail_id = {$userMailID} and mail_id = {$mailId} and version = 12";
                    $db->createCommand($delSql)->execute();
                    // 版本号为1 +
                    $version = new \common\library\version\MailVersion($clientId, $userMailID);
                    $version->setMailId($mailId);
                    $version->setType(1);
                    $version->add();
                }
            }
        }

    }

    //清理多余的MailVersion
    public function actionCleanExtraMailVersion()
    {
        ini_set('memory_limit', '15000M');
        $clients = $this->getClientList();

        $num = 0;
        $clientCount = count($clients);

        foreach ($clients as $client)
        {
            $num++;

            $clientId = $client['client_id'];
            $clientStartTime = microtime(true);
            //统计Client数据
            $clientDeleteNum = 0;

            try {
                echo "----------------------------\n";
                echo "此时ClientId为{$clientId}\n";

                $userMailListSql = "select user_mail_id from tbl_user_mail where client_id = {$clientId}";
                $userMailList = array_column(Yii::app()->db->createCommand($userMailListSql)->queryAll(true), 'user_mail_id');
                if (empty($userMailList))
                    continue;

                //$userMailList = [1669827];
                $db = ProjectActiveRecord::getDbByClientId($clientId);
                foreach ($userMailList as $userMailId)
                {
                    echo "此时user_mail_id为{$userMailId}\n";

                    $mailVersionMap = [];
                    $latestVersion = 0;
                    $size = 10000;

                    //统计数据变量
                    $userMailIdStartTime = microtime(true);
                    $userMailIdDeleteNum = 0;

                    $deleteVersionIds = [];

                    //统计数据变量
                    do {
                        echo "$clientId $num/$clientCount 此时最新的version为{$latestVersion},size为{$size},user_mail_id:{$userMailId}\n";
                        $versionSql = "select mail_id, type, version from tbl_mail_version where user_mail_id = {$userMailId} and version > $latestVersion order by version asc limit {$size}";
                        $versionData = $db->createCommand($versionSql)->queryAll(false);

                        //统计同一user_mail_id下单次数据
                        $deleteCount = 0;

                        //统计同一user_mail_id下单次数据
                        $deleteItem = [];
                        foreach ($versionData as $item)
                        {
                            $mailId = $item[0];
                            $type = $item[1];
                            $nowVersion = $item[2];

                            //存在重复
                            if (isset($mailVersionMap[$mailId][$type]))
                            {
                                $oldVersionItem = $mailVersionMap[$mailId][$type];
                                $oldVersion = $oldVersionItem[2];
                                if ($nowVersion > $oldVersion)
                                {
                                    $deleteItem[] = $oldVersion;
                                    $deleteCount++;
                                    $userMailIdDeleteNum++;
                                }
                            }

                            $mailVersionMap[$mailId][$type] = $item;
                        }

                        if (empty($deleteItem)) {
                            $latestVersion = end($versionData)[2];
                            continue;
                        }

                        $deleteVersionIds = array_merge($deleteVersionIds,$deleteItem);

                        if (count($deleteVersionIds) >= 5000)
                        {
                            $deleteVersionIds = array_chunk($deleteVersionIds, 1000);
                            foreach ($deleteVersionIds as $deleteVersionId) {
                                $versionStr = implode(',', $deleteVersionId);
                                $deleteSql = "delete from tbl_mail_version where user_mail_id = {$userMailId} and version in ($versionStr)";
                                $db->createCommand($deleteSql)->execute();
                            }
                            $deleteVersionIds = [];
                        }
                        echo "需要处理{$deleteCount}条数据" . "\n";
                        //记录最后一个version
                        $latestVersion = end($versionData)[2];
                    } while (!empty($versionData));

                    if (count($deleteVersionIds) > 0)
                    {
                        $deleteVersionIds = array_chunk($deleteVersionIds, 1000);
                        foreach ($deleteVersionIds as $deleteVersionId) {
                            $versionStr = implode(',', $deleteVersionId);
                            $deleteSql = "delete from tbl_mail_version where user_mail_id = {$userMailId} and version in ($versionStr)";
                            $db->createCommand($deleteSql)->execute();
                        }
                        $deleteVersionIds = [];
                    }

                    $userMailIdEndTime = microtime(true);
                    echo "处理user_mail_id为{$userMailId}的数据总共{$userMailIdDeleteNum}条,耗时" . round($userMailIdEndTime - $userMailIdStartTime, 3) . "秒\n\n";
                    $clientDeleteNum += $userMailIdDeleteNum;
                }

                $clientEndTime = microtime(true);
                echo "处理ClientId为{$clientId}的数据总共{$clientDeleteNum}条,耗时" . round($clientEndTime - $clientStartTime, 3) . "秒\n";
                echo "----------------------------\n\n\n";

            } catch (Exception $e) {

                echo "----------打印错误\n";
                echo $e;
                echo "----------停止打印错误\n\n";

            }
        }
    }

    /**
     * 填充tbl_email_id和tbl_email_relation表的数据
     * @param $dbName
     * @param int $limit
     * @param int $dryRun
     * @param int $concurrent
     * @return array|CActiveRecord[]|\common\library\CommandRunner[]
     */
    public function actionMailStatisticByDb($dbName, $limit=2000, $dryRun = 1, $concurrent = 5)
    {
        $error = 0;
        $command = 'mail';
        $action = 'MailStatisticByDb';
        $total = 0;
        $startTime = microtime(true);

        if (!$dbName) {
            return \common\library\CommandRunner::iterateByDb($command, $action, $concurrent, ['limit' => $limit], true, $dryRun);
        }

        $dbNames = array_filter(explode(',', $dbName));
        foreach ($dbNames as $dbName)
        {
            // 遍历表
            try {
                list($mysqlDb, $pgDb, $currId) = \common\library\CommandRunner::getTaskInfo($command, $action, $dbName);
                //草稿箱和彻底删除邮件,隐藏草稿不计入统计范围
                $extraSql = " and folder_id not in (0,9,11) ";
                $yieldIter = \common\library\util\SqlUtil::queryAll($mysqlDb, 'tbl_mail', 'mail_id','mail_id,client_id,sender,receiver,cc,bcc,receive_time,user_id,mail_type,user_mail_id', $currId, $limit, $extraSql);
                foreach ($yieldIter as $mailList)
                {
                    if ($count = count($mailList)) {
                        LogUtil::info("此次循环执行{$count}次\n");
                        $total+=$count;
                        $runStartTime = microtime(true);
                        $currId = \common\library\command\Helper::recordEmailRelation($mysqlDb, $mailList, $currId);
                        \common\library\CommandRunner::processCommandDbTask($command, $action, $dbName, $currId);
                        $runEndStartTime = microtime(true);
                        LogUtil::info("1000条数据执行了".($runEndStartTime-$runStartTime)."s");
                    }
                }
                LogUtil::info("结束时currId是$currId--总数是$total");
            } catch (Throwable $e) {
                \LogUtil::error($e->getMessage());
                $error = 1;
            }

            \common\library\CommandRunner::finishCommandDbTask($command, $action, $dbName, $error);
        }
        $endTime = microtime(true);
        LogUtil::info("执行了".($endTime-$startTime)."s");
    }


    /**
     * 填充tbl_customer表的数据
     * @param $dbName
     * @param int $limit
     * @param int $dryRun
     * @param int $concurrent
     * @return array|CActiveRecord[]|\common\library\CommandRunner[]
     */
    public function actionUpdateCustomerEmailId($dbName, $limit=2000, $dryRun = 1, $concurrent = 5)
    {
        $command = 'mail';
        $action = 'UpdateCustomerEmailId';
        $error = 0;
        $total = 0;
        $startTime = microtime(true);

        if (!$dbName) {
            return \common\library\CommandRunner::iterateByDb($command, $action, $concurrent, ['limit' => $limit], true, $dryRun);
        }

        $dbNames = array_filter(explode(',', $dbName));
        foreach ($dbNames as $dbName)
        {
            try {
                list($mysqlDb, $pgDb, $currId) = \common\library\CommandRunner::getTaskInfo($command, $action, $dbName);

                $extraSql = " and is_archive = 1 and company_id > 0 ";

                $yieldIter = \common\library\util\SqlUtil::queryAll($pgDb, 'tbl_customer', 'customer_id', 'customer_id,email,client_id', $currId, $limit, $extraSql);
                foreach ($yieldIter as $customerList) {
                    if ($count = count($customerList)) {
                        LogUtil::info("此次循环执行{$count}次\n");
                        $total+=$count;
                        $dbClientId = reset($customerList)['client_id'];
                        try {
                            $currId = \common\library\email\Helper::updateCustomerEmailIdInCommand($dbClientId, $customerList, Constants::TYPE_CUSTOMER, $currId);
                        } catch (Throwable $t) {
                            \LogUtil::error($t->getMessage());
                        }
                        \common\library\CommandRunner::processCommandDbTask($command, $action, $dbName, $currId);
                    }
                }
                LogUtil::info("结束时currId是$currId--总数是$total");
            } catch (Throwable $e) {
                \LogUtil::error($e->getMessage());
                $error = 1;
            }
            \common\library\CommandRunner::finishCommandDbTask($command, $action, $dbName, $error);
        }
        $endTime = microtime(true);
        LogUtil::info("执行了".($endTime-$startTime)."s");
    }

    /**
     * 填充tbl_lead_customer表的数据
     * @param $dbName
     * @param int $limit
     * @param int $dryRun
     * @param int $concurrent
     * @return array|CActiveRecord[]|\common\library\CommandRunner[]
     */
    public function actionUpdateLeadCustomerEmailId($dbName, $limit=2000, $dryRun = 1, $concurrent = 5)
    {

        $command = 'mail';
        $action = 'UpdateLeadCustomerEmailId';
        $error = 0;
        $total = 0;
        $startTime = microtime(true);

        if (!$dbName) {
            return \common\library\CommandRunner::iterateByDb($command, $action, $concurrent, ['limit' => $limit], true, $dryRun);
        }

        $dbNames = array_filter(explode(',', $dbName));
        foreach ($dbNames as $dbName)
        {
            try {
                list($mysqlDb, $pgDb, $currId) = \common\library\CommandRunner::getTaskInfo($command, $action, $dbName);

                $yieldIter = \common\library\util\SqlUtil::queryAll($pgDb, 'tbl_lead_customer', 'customer_id', 'customer_id,email,client_id', $currId, $limit);
                foreach ($yieldIter as $customerList) {
                    if ($count = count($customerList)) {
                        LogUtil::info("此次循环执行{$count}次\n");
                        $total+=$count;
                        $dbClientId = reset($customerList)['client_id'];
                        $currId = \common\library\email\Helper::updateCustomerEmailIdInCommand($dbClientId, $customerList, Constants::TYPE_LEAD_CUSTOMER, $currId);
                        \common\library\CommandRunner::processCommandDbTask($command, $action, $dbName, $currId);
                    }
                }
                LogUtil::info("结束时currId是$currId--总数是$total");
            } catch (Throwable $e) {
                \LogUtil::error($e->getMessage());
                $error = 1;
            }
            \common\library\CommandRunner::finishCommandDbTask($command, $action, $dbName, $error);
        }
        $endTime = microtime(true);
        LogUtil::info("执行了".($endTime-$startTime)."s");
    }

    public function actionUpdateEdmEmailId($dbName, $limit=300, $dryRun = 1, $concurrent = 30)
    {
        $command = 'mail';
        $action = 'UpdateEdmEmailId';
        $error = 0;

        if (!$dbName) {
            return \common\library\CommandRunner::iterateByDb($command, $action, $concurrent, ['limit' => $limit], true, $dryRun);
        }

        $dbNames = array_filter(explode(',', $dbName));

        foreach ($dbNames as $dbName) {
            try {
                list($mysqlDb, $pgDb, $currId) = \common\library\CommandRunner::getTaskInfo($command, $action, $dbName);

                foreach (\common\library\util\SqlUtil::queryAll($mysqlDb, 'tbl_group_mail_task_param', 'task_id',
                    'task_id, send_to_address', $currId, $limit) as $taskList) {
                    if ($count = count($taskList)) {
                        $currId = \common\library\email\Helper::updateEdmEmailId($taskList, $currId);
                        \common\library\CommandRunner::processCommandDbTask($command, $action, $dbName, $currId);
                    }
                }
            } catch (\PDOException $e) {
                \LogUtil::error($e->getMessage());
                $error = 1;
            } catch (\Exception $e) {
                \LogUtil::error($e->getMessage());
                $error = 1;
            }

            \common\library\CommandRunner::finishCommandDbTask($command, $action, $dbName, $error);
        }
    }

    /**
     * 批量硬删除邮件
     * @param $clientId
     * @param $userId
     * @param $mailIds
     * @param $userMailId
     *
     * 1. 删除tbl_mail表记录
     * 2. 删除tbl_dynamic_trail表记录
     * 3. 删除tbl_dynamic_trail_data表记录
     * 4. 更新邮件索引
     */
    public function actionDeleteMailsByMailIds($clientId, $userId, $mailIds, $userMailId)
    {
        $mailIds = explode(',', $mailIds);
        if (empty($mailIds)) throw new RuntimeException("MailIds为空");

        $mailIds = implode(',', $mailIds);
        $db = ProjectActiveRecord::getDbByClientId($clientId);
        $mailListSql = "select * from tbl_mail where client_id = $clientId and user_id = $userId and mail_id in ($mailIds)";
        $mailList = $db->createCommand($mailListSql)->queryAll();

        $pgDb = PgActiveRecord::getDbByClientId($clientId);
        $trailListSql = "select * from tbl_dynamic_trail where client_id = $clientId and refer_id in ($mailIds)";
        $trailList = $pgDb->createCommand($trailListSql)->queryAll();

        self::info("开始删除邮件,总数".count($mailList));
        foreach ($mailList as $item)
        {
            $mailId = $item['mail_id'];
            $subject = $item['subject'];
            $deleteMailSql = "delete from tbl_mail where mail_id = $mailId";
            self::info("删除邮件: mail_id=$mailId  subject=$subject");
            $db->createCommand($deleteMailSql);
        }

        self::info("开始删除动态,总数".count($trailList));
        foreach ($trailList as $item)
        {
            $trailId = $item['trail_id'];
            $referId = $item['refer_id'];
            $deleteTrailSql = "delete from tbl_dynamic_trail where trail_id=$trailId";
            self::info("删除动态: trail_id=$trailId  mail_id=$referId");
            $pgDb->createCommand($deleteTrailSql)->execute();
            echo "删除动态: trail_id=$trailId  mail_id=$referId\n";

            $deleteTrailDataSql = "delete from tbl_dynamic_trail_data where trail_id=$trailId";
            self::info("删除动态内容: trail_id=$trailId  mail_id=$referId");
            $pgDb->createCommand($deleteTrailDataSql)->execute();
            echo "删除动态内容: trail_id=$trailId  mail_id=$referId\n";
        }

        \common\library\mail\service\SearchEngineService::delete($userId, $userMailId, $mailIds);
        self::info("删除完毕");
    }

    public function actionFixMailCallbackService()
    {
        $fp = fopen('callbackSerivice_log', "r");
        while (!feof($fp)) {
            $str = fgets($fp) . "\n";
            preg_match_all("/(?<=actionReceiveMail\().+(?=n#6)/", $str, $ret);
            foreach ($ret[0] as $item) {
                $newItem = explode(',', str_replace(')\\', '', $item));
                $userId = (int)str_replace("'", '', trim($newItem[0]));
                $mailId = (int)str_replace("'", '', trim($newItem[1]));
                $clientId = (int)str_replace("'", '', trim($newItem[3]));
                $replyToEdmMailId = str_replace("'", '', trim($newItem[4]));
                $replyToEdmSysId = str_replace("'", '', trim($newItem[5]));
                $replyToEdmTaskId = str_replace("'", '', trim($newItem[6]));

                LogUtil::info("$userId--$mailId--$clientId--$replyToEdmMailId--$replyToEdmSysId--$replyToEdmTaskId");

                if ($userId) {
                    ProjectActiveRecord::setConnection(ProjectActiveRecord::getDbByUserId($userId));
                } else {
                    ProjectActiveRecord::setConnection(ProjectActiveRecord::getDbByClientId($clientId));
                }

                User::setLoginUserById($userId);
                //
                try {
                    $mail = new \common\library\mail\Mail($mailId);
                    $callbackService = new \common\library\mail\service\CallbackService($mail);
                    $callbackService->setExtendParams([
                        'reply_to_edm_mail_id' => $replyToEdmMailId,
                        'reply_to_edm_sys_id' => $replyToEdmSysId,
                        'reply_to_edm_task_id' => $replyToEdmTaskId
                    ]);

                    //同步邮件，发送邮件，不需要发送撞单
                    if($mail->getMailType() == \Mail::MAIL_TYPE_SEND){
                        $callbackService->setCheckConflict(false);
                    }

                    $callbackService->runFixTasks();
                } catch (Throwable $throwable) {
                    LogUtil::info("Running Wrong $userId--$mailId--$clientId--$replyToEdmMailId--$replyToEdmSysId--$replyToEdmTaskId End");
                    LogUtil::info("Wrong mail_id {$mailId} message:{$throwable->getMessage()}");
                    continue;
                }
            }
        }
    }

    //修复web端发送邮件回调失败的记录
    public function actionFixSendMailCallbackService()
    {
        $fp = fopen('/data/codebase/v4_client/protected/commands/send_mail_callbackService_log', "r");
        while (!feof($fp)) {
            $str =  fgets($fp) . "\n";
            preg_match_all("/(?<=错误mail_id).+(?=error)/", $str, $ret);
            $mailId = (int)$ret[0][0];
            $paramArr = explode('|', $str);
            $clientId = (int)$paramArr[1];
            $userId = (int)$paramArr[2];
            echo "$clientId--$userId--$mailId\n";
            LogUtil::info("$clientId--$userId--$mailId");

            try {
                if ($userId) {
                    ProjectActiveRecord::setConnection(ProjectActiveRecord::getDbByUserId($userId));
                } else {
                    ProjectActiveRecord::setConnection(ProjectActiveRecord::getDbByClientId($clientId));
                }
                User::setLoginUserById($userId);

                $mail = new \common\library\mail\Mail($mailId);
                $callbackService = new \common\library\mail\service\CallbackService($mail);
                $callbackService->runFixTasks();
            } catch (Throwable $throwable) {
                echo "Running Wrong $clientId--$userId--$mailId\n End\n";
                echo "Wrong mail_id {$mailId} message:{$throwable->getMessage()}\n\n";
                LogUtil::info("Running Wrong $clientId--$userId--$mailId\n End");
                LogUtil::info("Wrong mail_id {$mailId} message:{$throwable->getMessage()}\n");
                continue;
            }
        }
    }

    //修复PB接口发送邮件回调失败的记录
    public function actionFixDesktopSendMailCallbackService()
    {
        $fp = fopen('/data/codebase/v4_client/protected/commands/desktop_send_mail_callbackService_log', "r");
        while (!feof($fp)) {
            $str =  fgets($fp) . "\n";
            preg_match_all("/(?<=错误mail_id).+(?=error)/", $str, $ret);
            $mailId = (int)$ret[0][0];
            if (empty($mailId)) continue;
            echo "$mailId\n";

            $selectMailSql = "select client_id,user_id,mail_id from tbl_mail where mail_id = $mailId";
            $dbList = array_filter(\common\library\account\service\DbService::getDbList(DbSet::TYPE_MYSQL), function ($setInfo) {
                return strpos($setInfo['name'], 'v5_client') !== false && strpos($setInfo['name'], 'v5_client_exp') === false;
            });
            try {
                foreach ($dbList as $dbSet) {
                    $db = ProjectActiveRecord::getDbByDbSetId($dbSet['set_id']);
                    $mailInfo = $db->createCommand($selectMailSql)->queryAll()[0] ?? [];
                    if ($mailInfo) {
                        print_r($mailInfo);
                        break;
                    }
                }
                if (empty($mailInfo)) continue;
                $clientId = $mailInfo['client_id'];
                $userId = $mailInfo['user_id'];
                echo "clientId:$clientId -- userId: $userId -- mailId: $mailId\n";
                LogUtil::info("clientId:$clientId -- userId: $userId -- mailId: $mailId");

                if ($userId) {
                    ProjectActiveRecord::setConnection(ProjectActiveRecord::getDbByUserId($userId));
                } else {
                    ProjectActiveRecord::setConnection(ProjectActiveRecord::getDbByClientId($clientId));
                }
                User::setLoginUserById($userId);

                $mail = new \common\library\mail\Mail($mailId);
                $callbackService = new \common\library\mail\service\CallbackService($mail);
                $callbackService->runFixTasks();
            } catch (Throwable $throwable) {
                echo "Wrong mail_id {$mailId} message:{$throwable->getMessage()}\n\n";
                LogUtil::info("Running Wrong $clientId--$userId--$mailId\n End");
                LogUtil::info("Wrong mail_id {$mailId} message:{$throwable->getMessage()}\n");
                continue;
            }
        }
    }

    /**
     * @deprecated
     */
    //修复因为联系人删除逻辑不完善导致标记[包含联系人和其他客户联系人的邮件]为非客户邮件的脏数据
    public function actionFixMailRelationFlag()
    {
        $clientIdList = array_column($this->getClientList(), 'client_id');
        $total = 0;
        foreach ($clientIdList as $clientId) {
            try {
                $clientCount = 0;
                $mysqlDb = ProjectActiveRecord::getDbByClientId($clientId);
                ProjectActiveRecord::setConnection($mysqlDb);
                $pgDb = PgActiveRecord::getDbByClientId($clientId);
                PgActiveRecord::setConnection($pgDb);

                echo "此时ClientId是----$clientId----\n";
                $emailIdSelectSql = "select email_id from tbl_customer where client_id = $clientId and company_id = 0 and is_archive = 0 and update_time > '2019-11-05 00:00:00'";
                $emailIds = array_filter(array_column($pgDb->createCommand($emailIdSelectSql)->queryAll(), 'email_id'));
                if (empty($emailIds)) continue;
                $emailIdWhereSql = " and email_id in (" . implode(',', $emailIds) . ")";
                $relateMailIdSql = "select mail_id from tbl_email_relation where client_id = $clientId " . $emailIdWhereSql;
                $relateMailIds = array_column($mysqlDb->createCommand($relateMailIdSql)->queryAll(), 'mail_id');
                if (empty($relateMailIds)) continue;
                $mailInfosSql = "select mail_id,client_id,sender,receiver,cc,bcc,receive_time,user_id,mail_type from tbl_mail where mail_id in (" . implode(',', $relateMailIds) . ")";
                $mailInfos = array_column($mysqlDb->createCommand($mailInfosSql)->queryAll(), null, 'mail_id');
                $clientCount += count($mailInfos);
                echo "ClientId {$clientId} 涉及 {$clientCount} 封邮件\n";
                $total+=$clientCount;
                $allEmails = [];
                foreach ($mailInfos as $mailId => $mailInfo) {
                    $allEmails = array_merge($allEmails, [
                        trim($mailInfo['receiver']),
                        trim($mailInfo['cc']),
                        trim($mailInfo['bcc']),
                        trim($mailInfo['sender']),
                        trim($mailInfo['reply_to'] ?? ''),
                    ]);
                }
                $allEmails = array_filter(array_unique($allEmails));
                $allEmailsChunk = array_chunk($allEmails, 500);
                $relateEmails = [];
                foreach ($allEmailsChunk as $chunk) {
                    $temp = \common\library\email\Util::findAllEmails($allEmails);
                    $relateEmails = array_merge($relateEmails, $temp);
                }

                $relateEmails = array_unique($relateEmails);
                if (empty($relateEmails)) continue;
                $customerListObj = new \common\library\customer_v3\customer\CustomerList($clientId);
                $customerListObj->setEmail($relateEmails);
                $customerListObj->setFields(['is_archive', 'customer_id', 'company_id', 'name', 'email']);
                $customerMap = array_column($customerListObj->find(), null, 'email');
                if (empty($customerMap)) continue;
                foreach ($mailInfos as $mailId => $mailInfo) {
                    \common\library\email\Helper::updateEmailId($clientId, $mailInfo, $customerMap);
                }
                echo "\n";
            } catch (Throwable $t) {
                echo $t->getMessage()."\n";
            }
        }
        echo "涉及{$total}封邮件\n";
    }

    //获取邮箱的群发单显计数
    public function actionGetExposeInfo($userId, $userMailId, $planSendTime='') {
        $planSendTime = empty($planSendTime) ? date('Y-m-d') : $planSendTime;
        User::setLoginUserById($userId);
        $exposeService = new \common\library\mail\service\ExposeService($userMailId, $userId);
        $exposeService->setPlanDate($planSendTime);
        $key = $exposeService->getExposeTotalTimesCacheKey();
        $result = \Yii::app()->cache->executeCommand('HGETALL', [$key]);
        $count = count($result);
        $allValues = [];
        for($i=0;$i<$count;$i+=2) {
            $allValues[$result[$i]] = $result[$i+1];
        }
        print_r($allValues);
        echo "在{$planSendTime}当天,已使用[" . $exposeService->getExposeTotalTimes() . "]次群发单显次数\n\n";
    }

    //减去某个MailId的群发单显计数
    public function actionRecoverExposeInfo($userId, $userMailId, $planSendTime='', $mailId=0)
    {
        if (empty($mailId)) return;
        User::setLoginUserById($userId);
        $exposeService = new \common\library\mail\service\ExposeService($userMailId);
        $exposeService->setPlanDate($planSendTime);
        $key = $exposeService->getExposeTotalTimesCacheKey();
        $result = \Yii::app()->cache->executeCommand('HGETALL', [$key]);
        $count = count($result);
        $allValues = [];
        for($i=0;$i<$count;$i+=2) {
            $allValues[$result[$i]] = $result[$i+1];
        }
        echo "删除前Hash key是[$key]----values是:\n";
        print_r($allValues);
        $exposeService->recoverTotalTimes($mailId);
        $result = \Yii::app()->cache->executeCommand('HGETALL', [$key]);
        $count = count($result);
        $allValues = [];
        for($i=0;$i<$count;$i+=2) {
            $allValues[$result[$i]] = $result[$i+1];
        }
        echo "删除后Hash key是[$key]----values是:\n";
        print_r($allValues);

    }

    /**
     * @param $dbName
     * @param int $limit
     * @param int $con
     * @return array|CActiveRecord[]
     */
    public function actionFixEmailRelationUserMailId($dbName, $limit = 1000, $con = 15)
    {
        $error = 0;
        $command = 'mail';
        $action = 'FixEmailRelationUserMailId';
        $total = 0;
        $startTime = microtime(true);
        if (!$dbName) {
            return \common\library\CommandRunner::iterateByDb($command, $action, $con, ['limit' => $limit], true);
        }
        $dbNames = array_filter(explode(',', $dbName));
        foreach ($dbNames as $dbName)
        {
            // 遍历表
            try {
                list($mysqlDb, $pgDb, $currId) = \common\library\CommandRunner::getTaskInfo($command, $action, $dbName);
                //草稿箱和彻底删除邮件,隐藏草稿不计入统计范围
                $extraSql = " and folder_id not in (0,11) ";
                $yieldIter = \common\library\util\SqlUtil::queryAll($mysqlDb, 'tbl_mail', 'mail_id','mail_id,client_id,user_mail_id', $currId, $limit, $extraSql);
                foreach ($yieldIter as $mailList)
                {
                    if ($count = count($mailList)) {
                        LogUtil::info("此次循环执行{$count}次\n");
                        $total+=$count;
                        $runStartTime = microtime(true);
                        //修正user_mail_id
                        $unknownMailIds = [];
                        $unknownUserMailIds = [];
                        $userMailIdInfoMap = [];
                        $mailIds = array_column($mailList, 'mail_id');
                        foreach ($mailList as $mailInfo) {
                            $mailId = $mailInfo['mail_id'];
                            $userMailId = $mailInfo['user_mail_id'];
                            $clientId = $mailInfo['client_id'];
                            if ($clientId == 0) {
                                $unknownUserMailIds[] = $userMailId;
                                self::info("mail_id=[{$mailId}----userMailId=[{$userMailId}]----clientId=[$clientId]");
                                $unknownMailIds[] = $mailId;
                            }
                        }
                        if (!empty($unknownUserMailIds)) {
                            $selectClientIdSql = "select client_id,user_mail_id from tbl_user_mail where user_mail_id in (" . implode(',', $unknownUserMailIds) . ")";
                            $userMailIdInfoMap = array_column(Yii::app()->db->createCommand($selectClientIdSql)->queryAll($selectClientIdSql), 'client_id', 'user_mail_id');
                        }

                        $updateUserMailIdSql = '';
                        $updateClientIdSql = '';
                        foreach ($mailList as $mailInfo) {
                            $mailId = $mailInfo['mail_id'];
                            $userMailId = $mailInfo['user_mail_id'];
                            $clientId = $mailInfo['client_id'];
                            if ($clientId == 0) {
                                $clientId = $userMailIdInfoMap[$userMailId] ?? 0;
                                if ($clientId != 0) {
                                    $updateClientIdSql .= "when {$mailId} then {$clientId} ";
                                }
                            }
                            $updateUserMailIdSql .= "when {$mailId} then {$userMailId} ";
                            $currId = $mailId;
                        }
                        $updateEmailRelationSql = "update ignore tbl_email_relation set user_mail_id = case mail_id " . $updateUserMailIdSql . ' end';
                        $updateMailSql = "update tbl_mail set update_time = update_time,client_id = case mail_id ";
                        if ($updateClientIdSql) {
                            $updateEmailRelationSql .= ",client_id = case mail_id " . $updateClientIdSql . "end ";
                            $updateMailSql .= $updateClientIdSql . "end ";
                        }
                        $updateEmailRelationSql .= ' where mail_id in (' . implode(',', $mailIds) . ')';
                        $updateMailSql .= ' where mail_id in (' . implode(',', $unknownMailIds) . ')';


                        if ($updateEmailRelationSql) {
                            if (empty($mysqlDb)) continue;
                            $mysqlDb->getPdoInstance()->exec($updateEmailRelationSql);
                        }

                        if ($updateClientIdSql) {
                            if (empty($mysqlDb)) continue;
                            $mysqlDb->getPdoInstance()->exec($updateMailSql);;
                        }

                        //修正user_mail_id
                        \common\library\CommandRunner::processCommandDbTask($command, $action, $dbName, $currId);
                        $runEndStartTime = microtime(true);
                        LogUtil::info("{$limit}条数据执行了".($runEndStartTime-$runStartTime)."s");
                    }
                }
                LogUtil::info("结束时currId是$currId--总数是$total");
            } catch (Exception $e) {
                \LogUtil::error($e->getMessage());
                $error = 1;
            }

            \common\library\CommandRunner::finishCommandDbTask($command, $action, $dbName, $error);
        }
        $endTime = microtime(true);
        LogUtil::info("执行了".($endTime-$startTime)."s");
    }


    /**
     * @param int $clientId
     * @param int $dbSetId
     * 清理AI自动化打上的个人询盘标签
     */
    public function actionCleanPersonInquiryTag($clientId = 0,$dbSetId = 0)
    {
        ini_set("display_errors", "On");
        ini_set("memory_limit", "15000M");

        if ($clientId)
        {
            $clientList = [$clientId];
        } else {
            $clientList = $this->getClientList($clientId,false,null,null, 0, 0, null, $dbSetId);
            $clientList = array_column($clientList,'client_id');
        }

        $clientCount = count($clientList);

        echo 'clientCount:'.$clientCount;
        echo "\n";
        LogUtil::info('clientCount:'.$clientCount);

        $num = 1;
        foreach ($clientList as $clientId) {

            echo "Client $clientId: $num/$clientCount";
            echo "\n";
            LogUtil::info("Client $clientId: $num/$clientCount");
            $num++;

            $db = ProjectActiveRecord::getDbByClientId($clientId);

            $userListObj = new \common\library\account\UserList();
            $userListObj->setClientId($clientId);
            $userListObj->setFields(['user_id']);
            $userIds = array_column($userListObj->find(), 'user_id');

            if (empty($userIds))
                continue;

            foreach ($userIds as $userId)
            {
                User::setLoginUserById($userId);

                $sql = "SELECT mail_id FROM tbl_ai_mail_classify where user_id=$userId and create_time >= '2019-01-01 00:00:01' and mail_classify_type='personal_inquiry'";
                $mailIdList = $db->createCommand($sql)->queryColumn();

                if (empty($mailIdList))
                    continue;

                $mailIdSql = implode(',', $mailIdList);
                $validMailIds = $db->createCommand("select mail_id from tbl_mail_tag_assoc where mail_id in ($mailIdSql)and user_id= $userId and tag_id=12 and create_time >='2019-11-12 16:31:09'")->queryColumn();

                if (empty($validMailIds))
                    continue;

//                var_dump($mailIdList);
//                var_dump($validMailIds);
//                die;

                $validMailIdsSql = implode(',', $mailIdList);
                $where = " mail_id in ($validMailIdsSql) and user_id= $userId and tag_id=12";
                $db->createCommand("delete from tbl_mail_tag_assoc where $where")->execute();

                $map = \common\library\mail\Helper::aggregateByUserMailId($clientId, $validMailIds);
                foreach ($map as $userMailId => $ids)
                {
                    $version = new \common\library\version\MailVersion($clientId, $userMailId);
                    $version->setType(Constant::MAIL_MODULE_TAG);
                    $version->setMailId($ids);
                    $version->add();
                }
            }

        }
    }

    public function actionFixReplyTrack($clientId=0)
    {
        $clientList = $this->getClientList($clientId, true);

        $total = 0;
        /** @var CDbConnection $accountDb */
        $accountDb = Yii::app()->account_base_db;
        foreach ( $clientList as $client )
        {
            $count= 0;
            $db = ProjectActiveRecord::getDbByClientId($client['client_id']);
            $userIds = $accountDb->createCommand('select user_id from tbl_user_info where client_id='.$client['client_id'])->queryColumn();
            foreach ( $userIds as $userId )
            {
                $sql = "select mail_id,last_view_time,view_count  from tbl_mail_track where client_id={$client['client_id']} and user_id={$userId} and last_view_time='1970-01-01 00:00:01' and view_count=1";
                $list = $db->createCommand($sql)->queryAll();

                if( count($list) )
                {
                    $upSql =  "update tbl_mail_track set last_view_time=create_time where client_id={$client['client_id']} and user_id={$userId} and last_view_time='1970-01-01 00:00:01' and view_count=1";
                    $db->createCommand($upSql)->execute();
                }

                self::info("client_id: {$client['client_id']} user_id: {$userId} count:".count($list));
                $count += count($list);
            }

            $total += $count;
        }

        self::info('total :'.$total);
    }

    public function actionRunFixMailCallbackService()
    {
        $testHost = ['**********'];
        $onlineHost = ['************', '**************'];
        $useHost = $testHost;
        $hostStr = implode(',', array_map(function ($item) {
            return "'" . $item . "'";
        }, $useHost));
        $this->logInfo('hostStr', $hostStr);
        $selectSql = "SELECT c.client_id,c.mysql_set_id FROM tbl_client AS c JOIN tbl_db_set AS d ON c.pgsql_set_id = d.set_id WHERE d.`host` IN ($hostStr)";
        $accountDb = Yii::app()->account_base_db;
        $dbClientInfos = $accountDb->createCommand($selectSql)->queryAll();
        $dbClientMap = [];
        foreach ($dbClientInfos as $item) {
            $dbClientMap[$item['mysql_set_id']][] = $item['client_id'];
        }
        foreach ($dbClientMap as $setId => $clientIdList)
        {
            try {
                $selectSql = "select user_id from tbl_user_info where client_id in (" . implode(',', $clientIdList) . ") and enable_flag = 1";
                $userIds = $accountDb->createCommand($selectSql)->queryColumn();
                $receiveTimeSql = " receive_time >= '2020-02-28 00:27:52' and receive_time <= '2020-02-28 01:34:13'";
                $db = ProjectActiveRecord::getDbByDbSetId($setId);
                if (empty($db)) {
                    echo "DBSetId:{$setId}------Continued\n";
                    continue;
                }
                ProjectActiveRecord::setConnection($db);
                echo "DBSetId:{$setId}------Running\n";
                $selectMailInfoSql = "select mail_id,user_id,client_id from tbl_mail where user_id in (" . implode(',', $userIds) . ") and {$receiveTimeSql}";
                $mailInfos = $db->createCommand($selectMailInfoSql)->queryAll();
                $this->logInfo('mailInfos', $mailInfos);
                foreach ($mailInfos as $item) {
                    try {
                        $userId = $item['user_id'];
                        $mailId = $item['mail_id'];
                        $clientId = $item['client_id'];
                        User::setLoginUserById($userId);
                        $mail = new \common\library\mail\Mail($mailId);
                        echo "Running----Client:[{$clientId}]----UserId:[{$userId}]----MailId:[{$mailId}]\n";
                        $callbackService = new \common\library\mail\service\CallbackService($mail);
                    } catch (Exception $e) {
                        throw new RuntimeException("Error----Client:[{$clientId}]----UserId:[{$userId}]----MailId:[{$mailId}]");
                    }
                }
            } catch (Exception $e) {
                echo "DBSetId:{$setId}------RuntimeExceptionMsg:{$e->getMessage()}\n";
            }
        }
    }

    public function logInfo($key, $info)
    {
        if (is_array($info)) {
            echo "[    $key    ] ---- ";print_r($info);
        } else {
            echo "[    $key    ] ---- ";echo $info;
        }
        echo "\n";
    }

    /**
     * 删除非追踪类型邮件的追踪记录
     * 原因：
     * java收件执行逻辑：分析邮件是否回复某封邮件，是and邮件view_count==0，执行view_count=1、reply_mail_id=xxx;
     * 但没有判断 被回复的邮件类型是否发件  和  发件track_flag==1导致：
     * 1、非从本系统发出和没有勾选追踪的邮件也产生了追踪记录--在新绑邮箱时会产生大量错误数据
     * 2、同步下来的邮件是回复一封收件也产生了追踪记录--收件不应该有追踪记录
     * @param int $clientId
     */
    public function actionCleanErrorTrack($clientId)
    {
        $clientList = $this->getClientList($clientId, true);

        /** @var CDbConnection $accountDb */
        $accountDb = Yii::app()->account_base_db;
        foreach ( $clientList as $client )
        {
            $db = ProjectActiveRecord::getDbByClientId($client['client_id']);

            $userIds = $accountDb->createCommand("select user_id from tbl_user_info where client_id={$client['client_id']}")->queryColumn();
            foreach ( $userIds as $userId )
            {
                //追踪列表中所有trackFlag=0的mailId
                $sql = "select track.mail_id from tbl_mail_track as track 
left join tbl_mail as mail 
on track.mail_id=mail.mail_id 
where track.client_id={$client['client_id']} and track.user_id={$userId} and mail.track_flag=0";
                $mailIds = $db->createCommand($sql)->queryColumn();

                if (empty($mailIds))
                    continue;

                $mailIds = array_chunk($mailIds, 1000);
                foreach ($mailIds as $ids)
                {
                    $idString = implode(',',$ids);
                    self::info("client:{$clientId};userId:{$userId};mailIds:$idString");

                    //tbl_mail_track
                    $deleteTrack = "delete from tbl_mail_track where client_id={$clientId} and mail_id IN ($idString)";
                    $db->createCommand($deleteTrack)->execute();

                    //tbl_mail_track_detail
                    $deleteTrackDetail = "delete from tbl_mail_track_detail where client_id={$clientId} and mail_id IN ($idString)";
                    $db->createCommand($deleteTrackDetail)->execute();
                }
            }
        }
    }

    public function actionGetTrueUnread($userId)
    {
        $user = User::getUserObject($userId);
        $clientId = $user->getClientId();
        try {
            $udpData = \common\library\swoole\client\MailUnreadClient::unread($clientId, $userId);
            print_r($udpData);
        } catch (ProcessException $exception) {
            echo "错误是--".$exception->getMessage().PHP_EOL;
        }
    }

    public function actionGetTrueCompanyUnread($userId)
    {
        $user = User::getUserObject($userId);
        $clientId = $user->getClientId();
        try {
            $udpData = \common\library\swoole\client\MailUnreadClient::companyAggregationUnread($clientId, $userId, 'group_id');
            print_r($udpData);
        } catch (ProcessException $exception) {
            echo "错误是--".$exception->getMessage().PHP_EOL;
        }
    }

    public function actionRebuildUnreadData($userId)
    {
        $user = User::getUserObject($userId);
        $clientId = $user->getClientId();
        $report = new \common\library\report\mail_unread\Report($clientId, $userId);
        $report->rebuild();
        $udpData = \common\library\swoole\client\MailUnreadClient::unread($clientId, $userId);
        print_r($udpData);
    }

    //补充2019-3-1至今的MailFolder的版本号
    public function actionMakeUpMailFolderVersion()
    {
        $selectUserIdSql = "select DISTINCT user_id from tbl_mail_folder where create_time > '2019-03-01 00:00:00'";
        $dbList = array_filter(\common\library\account\service\DbService::getDbList(DbSet::TYPE_MYSQL), function ($setInfo) {
            return strpos($setInfo['name'], 'v5_client') !== false && strpos($setInfo['name'], 'v5_client_exp') === false;
        });
        try {
            foreach ($dbList as $dbSet) {
                echo "db_name : " . $dbSet['name'] . "\n";
                $db = ProjectActiveRecord::getDbByDbSetId($dbSet['set_id']);
                $userIds = $db->createCommand($selectUserIdSql)->queryColumn();
                if (empty($userIds)) continue;
                foreach ($userIds as $userId) {
                    echo "user_id -- {$userId} \n";
                    $version = new \common\library\version\UserModuleVersion($userId);
                    $version->setModule(Constant::USER_MODULE_MAIL_FOLDER);
                    $version->add();
                }
                echo "\n\n";
            }
        } catch (Throwable $throwable) {
            echo "Error Msg: {$throwable->getMessage()}\n";
        }
    }

    public function actionRunFixMailCallbackServiceByClientId()
    {
        $accountDb = Yii::app()->account_base_db;
        $clientIdList = $this->getClientList();
        foreach ($clientIdList as $clientInfo) {
            $clientId = $clientInfo['client_id'];
            try {
                $selectSql = "select user_id from tbl_user_info where client_id = {$clientId} and enable_flag = 1";
                $userIds = $accountDb->createCommand($selectSql)->queryColumn();
                $receiveTimeSql = " receive_time >= '2020-02-28 00:27:52' and receive_time <= '2020-02-28 01:34:13'";
                $db = ProjectActiveRecord::getDbByClientId($clientId);
                if (empty($db)) {
                    echo "clientId:{$clientId}------Continued\n";
                    continue;
                }
                ProjectActiveRecord::setConnection($db);
                echo "clientId:{$clientId}------Running\n";
                $selectMailInfoSql = "select mail_id,user_id,client_id from tbl_mail where user_id in (" . implode(',', $userIds) . ") and {$receiveTimeSql}";
                $mailInfos = $db->createCommand($selectMailInfoSql)->queryAll();
                $this->logInfo('mailInfos', $mailInfos);
                foreach ($mailInfos as $item) {
                    try {
                        $userId = $item['user_id'];
                        $mailId = $item['mail_id'];
                        $clientId = $item['client_id'];
                        User::setLoginUserById($userId);
                        $mail = new \common\library\mail\Mail($mailId);
                        echo "Running----Client:[{$clientId}]----UserId:[{$userId}]----MailId:[{$mailId}]\n";
                        $callbackService = new \common\library\mail\service\CallbackService($mail);
                        $callbackService->runFixTasks();
                    } catch (Exception $e) {
                        throw new RuntimeException("Error----Client:[{$clientId}]----UserId:[{$userId}]----MailId:[{$mailId}]");
                    }
                }
            } catch (Exception $e) {
                echo "DBSetId:{$clientId}------RuntimeExceptionMsg:{$e->getMessage()}\n";
            }
        }
    }

    public function actionStatQueue($userMailId)
    {
        $redis = \RedisService::getInstance('redis_queue');

        $prefix = \Yii::app()->params['classify_mail_consumer_keys']['user_mail_queue_prefix'] ?? '';
        if (!$prefix)
            throw new \ProcessException('无效的邮件队列配置');

        $key = $prefix.$userMailId;
        $data = $redis->lrange($key, 0, -1);
        echo json_encode($data, JSON_PRETTY_PRINT);
    }

    /**
     * @param int $clientId
     * @param int $startClientId
     * @param int $endClientId
     * 邮箱解绑、改绑后清除未完成的待处理邮件
     */
    public function actionCleanInvalidTodo($clientId = 0, $startClientId = 0, $endClientId = 0)
    {
        $clients = $this->getClientList($clientId, false, null, null, $startClientId, $endClientId);
        foreach ($clients as $client)
        {
            $clientId = $client['client_id'];
            LogUtil::info("begin:-------client_id:$clientId" . PHP_EOL);
            ProjectActiveRecord::setConnection(ProjectActiveRecord::getDbByClientId($clientId));

            $users = Yii::app()->account_base_db->createCommand("SELECT user_id FROM `tbl_user_info` where client_id={$clientId}")->queryAll(true);
            foreach ($users as $user)
            {
                $userId = $user['user_id'];
                LogUtil::info("begin:-------user_id: {$userId}");
                try {
                    $mailBatchOperator = new \common\library\mail\MailListBatchOperate($userId);
                    $affectedRows = $mailBatchOperator->cleanInvalidMailTodo();
                    LogUtil::info("end:-------user_id: {$userId}: rows: {$affectedRows}" . PHP_EOL);
                } catch (Exception $exception) {
                    $message = $exception->getMessage();
                    LogUtil::info("fail:-------user_id:{$userId}: {$message}" . PHP_EOL);
                }
            }
        }
    }

    public function actionGetConsumerRelationMap()
    {
        $data = \common\library\ai\classify\ClassifyMailConsumer::getAllRelationConsumer();
        var_dump($data);
    }

    //关闭邮件消费队列并解除本机关联
    public function actionKillMailConsumer()
    {
        $env = \Yii::app()->params['env'] ? \Yii::app()->params['env'] : '';
        $command = "pkill -9 -f 'ClassifyMailConsumer {$env}'";
        exec($command);
        \common\library\ai\classify\ClassifyMailConsumer::releaseUserMailConsumer();
    }

    public function actionMailNotifyQueueCount(){
        $count = \common\library\ai\classify\ClassifyMailConsumer::notifyQueueCount();
        var_dump($count);
    }

    /**
     * 定时同步客服向客户运营的邮件信息需要推给ali
     * @throws CDbException
     * @throws CException
     * @throws ProcessException
     */
    public function actionMailSendInfoSyncToAli()
    {
        // 运营客服的clientId
        $opClientId = \common\library\alibaba\Constant::OKKI_OPERATION_DEPARTMENT_CLIENT;

        $sql = "select user_mail_id from tbl_user_mail where client_id={$opClientId}";

        $userMailIds = \Yii::app()->db->createCommand($sql)->queryColumn();

        $startDate = date('Y-m-d', strtotime('-1day'));
        $endDate = date('Y-m-d');

        foreach ($userMailIds as $userMailId) {
            try {
                $offset = 0;
                $limit = 300;
                $params = [
                    ':user_mail_id' => $userMailId,
                    ':mail_type' => \Mail::MAIL_TYPE_SEND,
                    ':send_status' => \Mail::SEND_STATUS_SEND_SUCCESS,
                    ':start_date' => $startDate,
                    ':end_date' => $endDate
                ];

                $countSql = "select count(1) as `count` from tbl_mail where user_mail_id=:user_mail_id and mail_type=:mail_type and send_status=:send_status and create_time>=:start_date and create_time<=:end_date";
                $total = \ProjectActiveRecord::getDbByClientId($opClientId)->createCommand($countSql)->queryScalar($params);

                do {
                    try {
                        $sql = "select mail_id,subject,create_time from tbl_mail where user_mail_id=:user_mail_id and mail_type=:mail_type and send_status=:send_status and create_time>=:start_date and create_time<=:end_date Limit $offset,$limit ";

                        $list = \ProjectActiveRecord::getDbByClientId($opClientId)->createCommand($sql)->queryAll(true, $params);

                        $offset += $limit;

                        if(empty($list))
                        {
                            break;
                        }

                        $this->pushMailSendInfoToAli($opClientId, $list);

                    }catch (\Exception $e) {}

                }while($offset < $total);
                usleep(100000);
            } catch (\Throwable $e) {
                var_dump($e->getMessage());
            }
        }
    }

    /**
     * 运营客户邮件分析客户的companyId和okkiClientId，组成消息回推阿里端
     * 消息先推os,os关联查询ali_cgs再推ali
     * @param $opClientId
     * @param array $list
     * @param array $trackMapMailIds
     * @return array|void
     * @throws CDbException
     * @throws CException
     * @throws ProcessException
     */
    private function pushMailSendInfoToAli($opClientId, array $list)
    {
        // 非客服运营的clientId不处理
        if($opClientId != \common\library\alibaba\Constant::OKKI_OPERATION_DEPARTMENT_CLIENT)
        {
            return;
        }

        $mailIds = array_column($list,'mail_id');

        if(empty($mailIds))
        {
            return;
        }

        // 通过mailId获取customerId
        $sql = "select mail_id,customer_id from tbl_email_relation where client_id={$opClientId} and mail_id in(" . implode(',', $mailIds) . ")";
        $customerList = \ProjectActiveRecord::getDbByClientId($opClientId)->createCommand($sql)->queryAll();
        $customerIds = array_unique(array_column($customerList, 'customer_id'));

        if(empty($customerIds))
        {
            return;
        }

        // 通过customerId获取companyId
        $sql = "select company_id,customer_id from tbl_customer where client_id={$opClientId} and customer_id in(" . implode(',', $customerIds) . ')';
        $mapCompanyList = \PgActiveRecord::getDbByClientId($opClientId)->createCommand($sql)->queryAll();
        $mapCompanyList = array_column($mapCompanyList, 'company_id', 'customer_id');

        $mailMapCompanyId = $companyIds = [];
        foreach ($customerList as &$item)
        {
            $customerId = $item['customer_id'];
            $mailId = $item['mail_id'];
            if (isset($mapCompanyList[$customerId])) {
                $companyId = $mapCompanyList[$customerId];
                $mailMapCompanyId[$mailId] = $companyId;
                $companyIds[] = $companyId;
            }
        }
        unset($item);

        if(empty($mailMapCompanyId))
        {
            return;
        }

        $mailMapOkkiClientId = [];
        // 通过companyId获取company表的自定义字段定义的okki_client_id
        $fieldId = \common\library\alibaba\Constant::CUSTOMER_CLIENT_FIELD_ID;
        if ($companyIds) {
            $sourceSql = "select company_id,external_field_data::jsonb ->> '{$fieldId}' AS okki_client_id from tbl_company where client_id={$opClientId} and company_id in(" . implode(',', $companyIds) . ")";
            $result = \PgActiveRecord::getDbByClientId($opClientId)->createCommand($sourceSql)->queryAll(true);
            foreach ($mailMapCompanyId as $mailId => $companyId)
            {
                foreach ($result as $elem)
                {
                    if ($elem['company_id'] == $companyId)
                    {
                        $osClientId = $elem['okki_client_id'] ?? 0;
                        if(!$osClientId)
                        {
                            continue;
                        }
                        $mailMapOkkiClientId[$mailId] = $osClientId;
                    }
                }
            }
        }

        $dataSet = [];
        foreach ($list as $k=>$item) {
            $mailId = $item['mail_id'];
            // 没有设置自定义字段okki_client_id的，不需要推送
            if(!isset($mailMapOkkiClientId[$mailId]))
            {
                continue;
            }
            $row['okki_client_id'] = $mailMapOkkiClientId[$mailId];
            $row['email_id'] = $mailId;
            $row['send_time'] = $item['create_time'];
            $row['title'] = $item['subject'];
            $row['create_time'] = $item['create_time'];

            $dataSet[] = $row;
        }

        if($dataSet)
        {
            $data = [
                // 邮箱同步信息
                'msg_type' => 'ali_send_mail',
                'data' => $dataSet,
            ];
            // 打印方便终端查看执行过程
            var_dump('push redis');
            $value = json_encode($data);
            $queueKey = \common\library\alibaba\push\pushDispatcher::COMPANY_TRAIL_STATUS_QUEUE_LIST_KEY;
            $redis = \RedisService::getInstance('redis_queue');
            $res = $redis->lpush($queueKey, [$value]);
        }

        return;
    }

    /**
     * 初始化运营发邮箱同步记录数据，只需跑一次
     */
    public function actionInitMailSendInfoSyncToAli()
    {
        // 运营客服的clientId
        $opClientId = \common\library\alibaba\Constant::OKKI_OPERATION_DEPARTMENT_CLIENT;

        $sql = "select user_mail_id from tbl_user_mail where client_id={$opClientId}";

        $userMailIds = \Yii::app()->db->createCommand($sql)->queryColumn();

        // 产品要求,只推今年数据以后数据
        $startDate = '2021-01-01';
        $endDate = date('Y-m-d');

        foreach ($userMailIds as $userMailId) {
            try {
                $offset = 0;
                $limit = 300;
                $params = [
                    ':user_mail_id' => $userMailId,
                    ':mail_type' => \Mail::MAIL_TYPE_SEND,
                    ':send_status' => \Mail::SEND_STATUS_SEND_SUCCESS,
                    ':start_date' => $startDate,
                    ':end_date' => $endDate
                ];

                $countSql = "select count(1) as `count` from tbl_mail where user_mail_id=:user_mail_id and mail_type=:mail_type and send_status=:send_status and create_time>=:start_date and create_time<=:end_date";
                $total = \ProjectActiveRecord::getDbByClientId($opClientId)->createCommand($countSql)->queryScalar($params);

                do {
                    try {
                        $sql = "select mail_id,subject,create_time from tbl_mail where user_mail_id=:user_mail_id and mail_type=:mail_type and send_status=:send_status and create_time>=:start_date and create_time<=:end_date Limit $offset,$limit ";

                        $list = \ProjectActiveRecord::getDbByClientId($opClientId)->createCommand($sql)->queryAll(true, $params);

                        $offset += $limit;

                        if(empty($list))
                        {
                            break;
                        }

                        $this->pushMailSendInfoToAli($opClientId, $list);

                    }catch (\Exception $e) {}

                    usleep(50000);

                }while($offset < $total);
                usleep(50000);
            } catch (\Throwable $e) {
                var_dump($e->getMessage());
            }
        }
    }


	/**
	 * 关闭邮件模板
	 *
	 * @param $templateId
	 * @throws Exception
	 */
	public function actionDeleteMailTemplate($templateId) {

		$mailSystemTemplate = new MailSystemTemplate($templateId);

		$mailSystemTemplate->enable_flag = 0;

		$mailSystemTemplate->save();
	}

	public function actionFixMailTrack($clientId = 0, $last_number=null) {

        ini_set("memory_limit", "3000M");

        $clientListData = $this->getClientList($clientId,false,null,null, 0, 0, $last_number, 0,null);
        $clientIds = array_column($clientListData, 'client_id');

		LogUtil::info("clientID: ".json_encode($clientIds));

		foreach ($clientIds as $clientId) {

			$execute = true;
			$curId = 0;
			$limitCount = 1000;
			$updateCount = 0;

			$db = ProjectActiveRecord::getDbByClientId($clientId);

			do {
				$selectSql = "SELECT mail_id
							  FROM tbl_mail_track
							  WHERE client_id = {$clientId}
							    AND mail_id > {$curId}
							    AND user_id <> 0
							    AND user_mail_id = 0
							  ORDER BY mail_id
							  LIMIT  {$limitCount};";

                $mailIds = $db->createCommand($selectSql)->queryColumn();

                if( !empty($mailIds) )
                {
                    $mailList = $db->createCommand('select mail_id, user_mail_id from tbl_mail where mail_id in ('.implode(',', $mailIds).')')->queryAll();
                    $mailMap = array_column($mailList, 'user_mail_id', 'mail_id');
                    foreach ($mailIds as $mailId)
                    {
                        $curId = max($curId, $mailId);
                        if( !isset($mailMap[$mailId]) )
                            continue;

                        $ret = $db->getPdoInstance()->exec("update  tbl_mail_track set user_mail_id={$mailMap[$mailId]} where mail_id=$mailId and user_mail_id=0");
                        if($ret)
                            $updateCount++;
                    }

                    self::info("clientId {$clientId}  udpate  count: ".$updateCount);
                }

			} while(!empty($mailIds));

			LogUtil::info("client_id :{$clientId}  count: ".$updateCount);
		}
	}

    /**
     * 重新载入邮件模版高清预览图
     */
    public function actionReloadSystemTemplate(){
        // 获取系统模板列表
        $systemTemplateList = new \common\library\mail\setting\template\MailSystemTemplateList(null);
        $systemTemplateList->setOrder('asc');
        $data = $systemTemplateList->find();
        $unNeed = ['Birthday(1)','新冠肺炎通报2','新冠肺炎通报1'];

        foreach ($data as $item) {
            $systemTemplate = new \common\library\mail\setting\template\MailSystemTemplate($item['template_id']);
            if(in_array($systemTemplate->template_name,$unNeed)){
                $systemTemplate->save();
                continue;
            }
            $systemTemplate->setReload(true);
            $systemTemplate->save();
        }
    }


    /**
     * 修改邮箱邮件附件总大小和邮件大小最大值
     *
     * @param $clientId
     */
    public function actionUpdateMailSize($clientId)
    {
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }
        $userMailObj = new UserMail();
        $errClientIds = [];
        foreach ($clientIds as $clientId) {
            try{
                $updCount = $userMailObj->updateAll(
                    ['attach_total_max_size' => 30, 'mail_max_size'=>30],
                    'client_id=:client_id',
                    [':client_id' => $clientId]
                );
                LogUtil::info("client_id :{$clientId} 修改用户邮箱邮件默认大小条数 count: ".$updCount);
                $updSpecialCount = $userMailObj->updateAll(
                    ['attach_total_max_size' => 50, 'mail_max_size'=>50],
                    "client_id=:client_id and (receive_server like '%qq.com' or receive_server like '%aliyun.com' or receive_server like '%mxhichina.com')",
                    [':client_id' => $clientId]
                );
                LogUtil::info("client_id :{$clientId} 修改阿里qq等用户邮箱邮件默认大小条数 count: ".$updSpecialCount);
            }catch (\Throwable $e) {
                $errClientIds[] = $clientId;
            }
        }
        if (empty($errClientIds)) {
            LogUtil::info("update mail max size and attach total max size successed");
        } else {
            LogUtil::error("update mail max size and attach total max size failed, fail cliendIds include " . json_encode($errClientIds));
        }
    }

    public function actionBackupMailClassifyMessage($dryRun = 1, $delete = 0, $force = 0)
    {
        $redis = \RedisService::queuePersistent(true);
        $notifyQueue = \Yii::app()->params['classify_mail_consumer_keys']['notify_queue'] ?? '';
        $notifyQueueBackup = $notifyQueue . ':backup';
        $userMailQueuePrefix = \Yii::app()->params['classify_mail_consumer_keys']['user_mail_queue_prefix'] ?? '';
        $userMailQueuePrefixBackup = $userMailQueuePrefix . ':backup';

        //debug
//        $notifyQueue .= ':test';
//        $value = json_encode(['client_id' => 1, 'user_id' => 765, 'user_mail_id' => $userMailId = 1]);
//        $redis->lpush($notifyQueue, [$value]);
//        $redis->lpush($userMailQueuePrefix . $userMailId, [10,11,12]);

        $notifyLength = $redis->llen($notifyQueue);

        if ($already = $redis->llen($notifyQueueBackup) && !$delete) {
            if ($force) {
                $redis->del([$notifyQueueBackup]);
                $this->logInfo("clean backup notify", [
                    'key' => $notifyQueueBackup,
                    'count' => $already,
                ]);
            } else {
                $this->logInfo("already backup", []);
                return;
            }
        }

        if (!$notifyLength) {
            return ;
        }

        $notifyQueueDataList = $redis->lrange($notifyQueue, 0, -1);
        if (!$delete) {
            $this->logInfo("get from notify", [
                'key' => $notifyQueue,
                'count' => count($notifyQueueDataList),
            ]);
            if (!$dryRun) {
                $redis->lpush($notifyQueueBackup, $notifyQueueDataList);
            }
        } else {
            $this->logInfo("delete notify key", [
                'key' => $notifyQueue
            ]);
            if (!$dryRun) {
                $redis->del([$notifyQueue]);
            }
        }
        foreach ($notifyQueueDataList as $result) {
            $data = json_decode($result, true);
            $userMailId = $data['user_mail_id'];
            $userMailQueueKey = $userMailQueuePrefix.$userMailId;
            $userMailQueueKeyBackup = $userMailQueuePrefixBackup.$userMailId;
            if (!$delete) {
                $userMailData = $redis->lrange($userMailQueueKey, 0, -1);
                $this->logInfo("get from user mail", [
                    'key' => $userMailQueueKey,
                    'count' => count($userMailData)
                ]);
                if (!$dryRun) {
                    if ($force) {
                        $redis->del([$userMailQueueKeyBackup]);
                        $this->logInfo("clean backup notify", [
                            'key' => $userMailQueueKeyBackup,
                        ]);
                    }
                    if (count($userMailData)) {
                        $redis->lpush($userMailQueueKeyBackup, $userMailData);
                    }
                }
            } else {
                $this->logInfo("delete user mail key", [
                    'key' => $userMailQueueKey
                ]);
                if (!$dryRun) {
                    $redis->del([$userMailQueueKey]);
                }
            }
        }

        $this->logInfo('stat', [
            'notify_key' => $notifyQueue,
            'notify_key_backup' => $notifyQueueBackup,
            'origin_notify' => $redis->llen($notifyQueue),
            'new_notify' => $redis->llen($notifyQueueBackup),
        ]);

    }

    public function actionReleaseConsumer($ip)
    {
        \common\library\ai\classify\ClassifyMailConsumer::releaseUserMailConsumer([$ip]);
    }

    public function actionStatAllConsumer()
    {
        $userMailConsumerKey = \Yii::app()->params['classify_mail_consumer_keys']['user_mail_consumer_key'] ?? '';
        $redis = \RedisService::queuePersistent();
        $allConsumer = $redis->hgetall($userMailConsumerKey);
        var_dump($allConsumer);
    }

    public function actionPushNotifyForUserMailId($ids, $dryRun = 1)
    {
        foreach (explode(',', $ids) as $id) {
            $userMailIds[] = intval($id);
        }

        $sql = "select user_mail_id, client_id, user_id from tbl_user_mail where user_mail_id in (" . implode(',', $userMailIds) . ")";
        $list = Yii::app()->db->createCommand($sql)->queryAll();
        $notifyQueue = \Yii::app()->params['classify_mail_consumer_keys']['notify_queue'] ?? '';
        $redis = \RedisService::getInstance('redis_queue');

        foreach ($list as $item) {
            $clientId = $item['client_id'];
            $userId = $item['user_id'];
            $userMailId = $item['user_mail_id'];
            $value = json_encode(['client_id' => $clientId, 'user_id' => $userId, 'user_mail_id' => $userMailId]);
            if (!$dryRun) {
                $redis->lpush($notifyQueue, [$value]);
                echo "pushed: $notifyQueue $value \n";
            }
            echo "push: $notifyQueue $value \n";
            \LogUtil::info("push: $notifyQueue $value");
        }
    }

    public function actionRefreshMailErrorCodeCache(){

        \common\library\mail\Helper::refreshMailErrorCodeCache();
        self::info('refresh mailErrorCodeCache!!!');
    }

}
