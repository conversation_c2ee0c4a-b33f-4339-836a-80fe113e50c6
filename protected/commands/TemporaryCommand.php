<?php

use common\commands\iteration\bugfix_1013\AddEnableCountCustomField;
use common\components\BaseObject;
use common\components\ElasticSearchActiveRecordV7;
use common\library\account\service\DbService;
use common\library\alibaba\product\AlibabaProductFailureCollector;
use common\library\alibaba\product\AlibabaProductSyncHelper;
use common\library\alibaba\product\AlibabaProductSyncSetting;
use common\library\cash_collection\CashCollection;
use common\library\custom_field\CustomFieldService;
use common\library\custom_field\FieldList;
use common\library\exchange_rate\ExchangeRateService;
use common\library\export_v2\Export;
use common\library\io_handler\customize_handler\CustomizeTask;
use common\library\io_handler\IOTaskPool;
use common\library\object\field\FieldConstant;
use common\library\oms\common\ExcelParseExecutor;
use common\library\oms\order_link\trigger\CashCollectionTrigger;
use common\library\oms\order_profit\Constant as OrderProfitConstant;
use common\library\platform_product\sku\PlatformSkuFilter;
use common\library\privilege_v3\Helper;
use common\library\privilege_v3\PrivilegeCache;
use common\library\privilege_v3\PrivilegeField;
use common\library\privilege_v3\PrivilegeService;
use common\library\product_v2\ProductConstant;
use common\library\product_v2\sku\ProductSkuFilter;
use common\library\queue_v2\job\ExportJob;
use common\library\queue_v2\QueueService;
use common\library\recycle\Recycle;
use common\library\serial\generator\IDProducerGenerator;
use common\library\serial\generator\NumberGenerator;
use common\library\server\refresh_product_inventory\RefreshInventoryQueue;
use common\library\util\excel\ExcelReader;
use common\library\util\PgsqlUtil;
use common\models\base\PrivilegeRoleAccess as PrivilegeRoleAccessModel;
use Elasticsearch\ClientBuilder;
use xiaoman\orm\database\data\DateRange;

class TemporaryCommand extends CrontabCommand
{
    /**
     * 修复某用户销售订单 product_list.external_field_data 字段为数组（部分订单出现product_list格式化后，external_field_data仍为字符串的情况）
     * @param $clientId   当前开始执行的clientId，用于程序意外终止后继续修复
     * @param $orderIds
     * @param $userIds
     * @return void
     */
    public function actionFixOrderProductListExternalField($clientId = null, $grey = 0, $greyNum = null)
    {
        self::info("FixOrderProductListExternalField start" . PHP_EOL);
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey && !is_null($greyNum)) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }
//        $accountBaseDb = Yii::app()->account_base_db;
//        $today = date('Y-m-d 00:00:00');
//        $selectClientsql = "select client_id from tbl_client where client_id >= {$clientId} and valid_to >= '{$today}' order by client_id";
//        $clients = $accountBaseDb->createCommand($selectClientsql)->queryAll();

        $selectSql = "select order_id,name,create_time,update_time,product_list from tbl_order where client_id = :client_id ";
        $editSql = "update tbl_order set product_list = :product_list where client_id = :client_id and order_id = :order_id";
        foreach ($clientIds as $clientId) {
            try {
//                $clientId = $client['client_id'];

                self::info("FixOrderProductListExternalField for client_id:【{$clientId}】 start!" . PHP_EOL);

                $db = PgActiveRecord::getDbByClientId($clientId);
                if (!$db) {
                    continue;
                }

                $rows = 500;
                $offset = 0;
                $params = [":client_id" => $clientId];

                do {
                    $limit = "limit {$rows} offset {$offset} ";
                    $sql = $selectSql . $limit;
                    $orders = $db->createCommand($sql)->queryAll(true, $params);
                    $offset += count($orders);

                    foreach ($orders as $order) {
                        $product_list = json_decode($order['product_list'], true);
                        if (!$product_list) {
                            continue;
                        }

                        $needEdit = false;
                        foreach ($product_list as &$product) {
                            if (empty($product['external_field_data'])) {
                                continue;
                            }
                            if (!is_array($product['external_field_data'])) {
                                $needEdit = true;
                                $product['external_field_data'] = json_decode($product['external_field_data'], true);
                            }
                        }

                        if ($needEdit) {
                            $editParams = [":client_id" => $clientId, ":product_list" => json_encode($product_list), ":order_id" => $order['order_id']];
                            $db->createCommand($editSql)->execute($editParams);
                            self::info("FixOrderProductListExternalField edit product_list.external_field_data with client_id:{$clientId} and order_id:{$order['order_id']}!" . PHP_EOL);
                        }
                    }
                } while (count($orders) >= $rows);
                self::info("FixOrderProductListExternalField for client_id:【{$clientId}】 finished!" . PHP_EOL);
            } catch (\Throwable $e) {
                self::info("FixOrderProductListExternalField for client_id:【{$clientId}】 failed, error on File:" . $e->getFile() . " | Line : " . $e->getLine() . " | Msg : " . $e->getMessage() . PHP_EOL);
                continue;
            }
        }

        self::info("FixOrderProductListExternalField Done!" . PHP_EOL);
    }

    // 产品手风琴迭代 本脚本修正平台产品spu的enable_flag字段，所有平台sku都被移除的spu其enable_flag改为0
    public function actionFixPlatformProductEnableFlag($clientId = null, $grey = 0, $greyNum = null)
    {
        self::info("FixPlatformProductEnableFlag start" . PHP_EOL);
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey && !is_null($greyNum)) {
            $greyNum = explode(",", $greyNum);
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        foreach ($clientIds as $clientId) {
            $updatePlatformProductIds = $this->platformProductEnableFlagUpdate($clientId);
            $rows = count($updatePlatformProductIds);
            self::info("FixPlatformProductEnableFlag clientId: 【{$clientId}】 finished, update platform product rows: {$rows} , platform_product_id :" . implode(',', $updatePlatformProductIds) . PHP_EOL);
        }
        self::info("FixPlatformProductEnableFlag All Finished" . PHP_EOL);
    }

    protected function platformProductEnableFlagUpdate($clientId)
    {
        $spuSql = "select platform_product_id from tbl_platform_product where client_id=:client_id and enable_flag=1";

        $db = \PgActiveRecord::getDbByClientId($clientId);
        $spuData = $db->createCommand($spuSql)->queryAll(true, [':client_id' => $clientId]);

        if (empty($spuData)) {
            self::info("FixPlatformProductEnableFlag clientId: 【{$clientId}】, does not have platform_product" . PHP_EOL);
            return [];
        }
        $platformIdMap = [];
        foreach ($spuData as $spu) {
            $platformIdMap[$spu['platform_product_id']] = true;
        }

        $filter = new PlatformSkuFilter($clientId);
//        $filter->enable_flag = BaseObject::ENABLE_FLAG_TRUE;  // 获取未完全删除的spu
        $filter->select(['platform_product_id', 'enable_flag', 'sku_num' => function () {
            return 'count(1) as sku_num';
        }]);
        $filter->groupBy('platform_product_id');
        $filter->groupBy('enable_flag');
        $filter->order("platform_product_id");
        $data = $filter->rawData();

        $platformProductGroup = [];
        $platformProductUpdate = [];        // 需要变更的平台产品
        foreach ($data as $datum) {
            $platformProductId = $datum['platform_product_id'];
            if (!isset($platformProductGroup[$platformProductId])) {
                $platformProductGroup[$platformProductId] = [];
            }
            if ($datum['enable_flag'] == BaseObject::ENABLE_FLAG_TRUE) {
                $platformProductGroup[$platformProductId]['enable_num'] = $datum['sku_num'];
            } else {
                $platformProductGroup[$platformProductId]['not_enable_num'] = $datum['sku_num'];
            }
        }

        foreach ($platformIdMap as $platformId => $_) {
            if (!isset($platformProductGroup[$platformId]) || empty($platformProductGroup[$platformId]['enable_num'])) {
                $platformProductUpdate[] = $platformId;
            }
        }

        if (!$platformProductUpdate) {
            return [];
        }

        $platformProductUpdateStr = implode(',', $platformProductUpdate);
        $sql = "update tbl_platform_product set enable_flag=0 where client_id={$clientId} and platform_product_id in ({$platformProductUpdateStr})";
        $db->createCommand($sql)->execute();
        return $platformProductUpdate;
    }

    // 产品手风琴迭代新增 enable_sku_count 字段，本脚本用于初始化该字段
    public function actionFixProductEnableSkuNum($clientId = null, $grey = 0, $greyNum = null)
    {
        self::info("FixProductEnableSkuNum start" . PHP_EOL);
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey && !is_null($greyNum)) {
            $greyNum = explode(",", $greyNum);
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        foreach ($clientIds as $clientId) {
            $rows = $this->skuEnableNumUpdate($clientId);
            self::info("FixProductEnableSkuNum clientId: 【{$clientId}】 finished, update product rows: {$rows}" . PHP_EOL);
        }
        self::info("FixProductEnableSkuNum All Finished" . PHP_EOL);
    }

    protected function skuEnableNumUpdate($clientId)
    {
        // 查询所有spu的id
        $allSql = "select product_id from tbl_product where client_id={$clientId} and enable_flag = 1";
        $db = \PgActiveRecord::getDbByClientId($clientId);
        $allProductIds = $db->createCommand($allSql)->queryAll();
        $allProductIds = array_column($allProductIds, 'product_id');
        if (!$allProductIds) {
            self::info("FixProductEnableSkuNum clientId: 【{$clientId}】 does have any product, skip." . PHP_EOL);
            return 0;
        }

        $filter = new ProductSkuFilter($clientId);
        $filter->enable_flag = BaseObject::ENABLE_FLAG_TRUE;
        $filter->disable_flag = BaseObject::ENABLE_FLAG_FALSE;  // 获取未删除的sku数量
        $filter->select(['product_id', 'enable_sku_count' => function () {
            return 'count(1) as enable_sku_count';
        }]);
        $filter->groupBy('product_id');
        $data = $filter->rawData();
        $data = array_column($data, null, 'product_id');
        $dataNum = count($data);
//        $sql = 'update tbl_product set enable_sku_count=:enable_sku_count where client_id=:client_id and product_id=:product_id';

//update tbl_product set description = case
//when product_id=3111963057 then 'aaa'
//when product_id=3113370127 then 'bbb'
//when product_id=3113321063 then 'ccc'
//else 'no description'
//end
//where client_id=14119 and name like 'zbp%';
        $sql = 'update tbl_product set enable_sku_count = case ';

        self::info("FixProductEnableSkuNum clientId: 【{$clientId}】 start, {$dataNum} product's enable_sku_count field will be update " . PHP_EOL);
        $rows = 0;
        $chunkCount = 1000;
        $productIdsChunk = array_chunk($allProductIds, $chunkCount);
        foreach ($productIdsChunk as $productIds) {
            if (!$productIds) {
                continue;
            }
            $chunkSql = $sql;
            foreach ($productIds as $productId) {
                if (!$productId) {
                    continue;
                }
//                $params = [
//                    ":product_id" => $productId,
//                    ":client_id" => $clientId,
//                    ":enable_sku_count" => isset($data[$productId]) ? $data[$productId]['enable_sku_count'] : 0,
//                ];
//                $rows += $db->createCommand($sql)->execute($params);
                $enableSkuCount = isset($data[$productId]) ? $data[$productId]['enable_sku_count'] : 0;
                $chunkSql .= " when product_id={$productId} then {$enableSkuCount} ";
            }
            $productIdsStr = implode(',', $productIds);
            $chunkSql .= " else enable_sku_count end where client_id = {$clientId} and product_id in ({$productIdsStr})";
            $rows += $db->createCommand($chunkSql)->execute();
        }
        return $rows;
    }

    /**
     * 临时脚本，修复所有用户product.list.field自定义配置，去除用户配置的minimum_order_quantity（最小起订量）字段显示，原因是该字段已被合到了fob字段中
     * @param $clientId   当前开始执行的clientId，用于程序意外终止后继续修复
     * @param $grey
     * @param $greyNum
     * @return void
     */
    public function actionFixUserSettingProductListField($clientId = null, $grey = 0, $greyNum = null)
    {
        self::info("FixUserSettingProductListField start" . PHP_EOL);
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey && !is_null($greyNum)) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        foreach ($clientIds as $clientId) {
            $rows = $this->fixProductListField($clientId);
            self::info("FixUserSettingProductListField clientId: 【{$clientId}】 finished, update rows: {$rows}" . PHP_EOL);
        }
        self::info("FixUserSettingProductListField All Finished" . PHP_EOL);
    }

    protected function fixProductListField($clientId)
    {
        $key = "product.list.field";
        $selectSql = "select user_id,value from tbl_user_setting where client_id=:client_id and `key`=:key and enable_flag=1";
        $selectParams = [
            ":client_id" => $clientId,
            ":key" => $key,
        ];
        $mysqlDb = \ProjectActiveRecord::getDbByClientId($clientId);
        if (!$mysqlDb) {
            return 0;
        }
        $settings = $mysqlDb->createCommand($selectSql)->queryAll(true, $selectParams);

        $updateData = [];
        $updateUserIds = [];
        $sql = "update tbl_user_setting set value = case";
        foreach ($settings as $k => $setting) {
            $settingVal = json_decode($setting['value'], true);
            if (!$settingVal) {
                continue;
            }

            $settingValMap = array_column($settingVal, null, "id");
            if (!isset($settingValMap['minimum_order_quantity'])) {
                continue;
            }
            unset($settingValMap['minimum_order_quantity']);
            $settingVal = json_encode(array_values($settingValMap));
            $paramUserIdKey = ":user_id_" . $k;
            $paramSettingValKey = ":setting_val_" . $k;
            $sql .= " when user_id={$paramUserIdKey} then {$paramSettingValKey} ";
            $updateData[$paramUserIdKey] = $setting['user_id'];
            $updateUserIds[] = $setting['user_id'];
            $updateData[$paramSettingValKey] = $settingVal;
        }
        if (count($updateData) == 0) {
            return 0;
        }
        $updateUserIds = implode(',', $updateUserIds);
        $sql .= " else value end where client_id={$clientId} and user_id in ({$updateUserIds}) and `key`=:key";
        $updateData[':key'] = $key;
        $rows = $mysqlDb->createCommand($sql)->execute($updateData);
        self::info("FixUserSettingProductListField clientId: 【{$clientId}】 update userIds: {$updateUserIds}" . PHP_EOL);
        return $rows;
    }

    // 跟单协同迭代：调整所有任务的状态
    public function actionFixTransferInvoiceStatus($clientId = null, $grey = 0, $greyNum = null)
    {
        self::info("actionFixTransferInvoiceStatus start" . PHP_EOL);
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey && !is_null($greyNum)) {
            $greyNum = explode(",", $greyNum);
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        $sql = "update tbl_product_transfer_invoice set status = case when status in (1,2,3) then 2 when status = 4 then 4 else 2 end ";
        foreach ($clientIds as $clientId) {
            $editSql = $sql . " where client_id={$clientId} and type in (38,39,40)";
            $db = \PgActiveRecord::getDbByClientId($clientId);
            $rows = $db->createCommand($editSql)->execute();
            self::info("client 【{$clientId}】 Finished, effect rows: {$rows}" . PHP_EOL);
        }
        self::info("actionFixTransferInvoiceStatus All Finished" . PHP_EOL);
    }

    // 导入任务列表ES数据
    public function actionImportTransferEsDoc($clientId = null, $grey = 0, $greyNum = null)
    {
        self::info("actionImportTransferEsDoc start" . PHP_EOL);
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey && !is_null($greyNum)) {
            $greyNum = explode(",", $greyNum);
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        foreach ($clientIds as $clientId) {
            $this->importTransferEsDoc($clientId);
        }
        self::info("actionImportTransferEsDoc end" . PHP_EOL);
    }

    protected function importTransferEsDoc($clientId)
    {
        $indexName = "transfer_invoice";
        $db = \PgActiveRecord::getDbByClientId($clientId);
        $allOrderTransferSql = "select serial_id,transfer_invoice_id,refer_id,type,tf.create_time,tf.update_time,ord.order_no as refer_invoice_no from tbl_product_transfer_invoice as tf left join tbl_order as ord on tf.refer_id = ord.order_id where tf.client_id={$clientId} and tf.delete_flag = 0 and type in (38,40)";
        $allOrderTransferInfos = $db->createCommand($allOrderTransferSql)->queryAll();

        $allPurchaseOrderTransferSql = "select serial_id,transfer_invoice_id,refer_id,type,tf.create_time,tf.update_time,porder.purchase_order_no as refer_invoice_no from tbl_product_transfer_invoice as tf left join tbl_purchase_order as porder on tf.refer_id = porder.purchase_order_id where tf.client_id={$clientId} and tf.delete_flag = 0 and type =39";
        $allPurchaseOrderTransferInfos = $db->createCommand($allPurchaseOrderTransferSql)->queryAll();

        if (!$allOrderTransferInfos && !$allPurchaseOrderTransferInfos) {
            self::info("importTransferEsDoc clientId: 【{$clientId}】 does not have any transfer invoice, skip." . PHP_EOL);
            return;
        }

        $chunkCount = 1000;
        $orderTransferChunkData = array_chunk($allOrderTransferInfos, $chunkCount);
        $purchaseOrderTransferChunkData = array_chunk($allPurchaseOrderTransferInfos, $chunkCount);

        $esHandler = new \common\library\server\es_search\TransferInvoiceHandler();
        $totalCount = count($allOrderTransferInfos) + count($allPurchaseOrderTransferInfos);
        $failCount = 0;
        foreach ([$orderTransferChunkData, $purchaseOrderTransferChunkData] as $chunkData) {
            foreach ($chunkData as $transferData) {
                $bulk = [];
                foreach ($transferData as $datum) {
                    $body = [
                        'client_id' => $clientId,
                        'create_time' => $datum['create_time'],
                        'update_time' => $datum['update_time'],
                        'transfer_invoice_id' => $datum['transfer_invoice_id'],
                        'refer_invoice_no' => $datum['refer_invoice_no'],
                        'serial_id' => $datum['serial_id']
                    ];
                    $bulk[] = [
                        'index' => [
                            '_index' => $indexName,
                            '_id' => $datum['transfer_invoice_id'],
                            'routing' => $clientId,
                        ]
                    ];
                    $bulk[] = $body;
                }
                $bulkChunkData = [
                    'index' => $indexName,
                    'body' => $bulk,
                    'refresh' => true
                ];
                $ret = $esHandler->handle($bulkChunkData);
                $failCount += $ret['errorCount'] ?? 0;
            }
        }
        self::info("importTransferEsDoc clientId: 【{$clientId}】 import total rows:【{$totalCount}】 | fail rows:【{$failCount}】" . PHP_EOL);
    }

    /**
     * 修复某用户产品属性缺失type字段
     * @param $clientId
     * @param $orderIds
     * @param $userIds
     * @return void
     */
    public function actionFixProductInfoJsonType($clientId = null, $grey = 0, $greyNum = null)
    {
        self::info("FixOrderProductListExternalField start" . PHP_EOL);
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey && !is_null($greyNum)) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        $selectSql = "select product_id,info_json from tbl_product where client_id = :client_id  and enable_flag=1 ";
        $editSql = "update tbl_product set info_json = :info_json where client_id = :client_id and product_id = :product_id ";
        foreach ($clientIds as $clientId) {
            try {
                self::info("actionFixProductInfoJsonType for client_id:【{$clientId}】 start!" . PHP_EOL);

                $db = PgActiveRecord::getDbByClientId($clientId);
                if (!$db) {
                    continue;
                }

                $rows = 500;
                $offset = 0;
                $params = [":client_id" => $clientId];

                do {
                    $limit = "limit {$rows} offset {$offset} ";
                    $sql = $selectSql . $limit;
                    $products = $db->createCommand($sql)->queryAll(true, $params);
                    $offset += count($products);

                    foreach ($products as $product) {
                        $info_json = json_decode($product['info_json'], true);
                        if (!$info_json) {
                            continue;
                        }

                        $needEdit = false;
                        foreach ($info_json as &$datum) {
                            if (!empty($datum['type'])) {
                                continue;
                            }
                            $needEdit = true;
                            $datum['type'] = 'input';
                        }

                        if ($needEdit) {
                            $editParams = [":client_id" => $clientId, ":info_json" => json_encode($info_json), ":product_id" => $product['product_id']];
                            $db->createCommand($editSql)->execute($editParams);
                            self::info("actionFixProductInfoJsonType edit product info_json with client_id:{$clientId} and product_id:{$product['product_id']}!" . PHP_EOL);
                        }
                    }
                } while (count($products) >= $rows);
                self::info("actionFixProductInfoJsonType for client_id:【{$clientId}】 finished!" . PHP_EOL);
            } catch (\Throwable $e) {
                self::info("actionFixProductInfoJsonType for client_id:【{$clientId}】 failed, error on File:" . $e->getFile() . " | Line : " . $e->getLine() . " | Msg : " . $e->getMessage() . PHP_EOL);
                continue;
            }
        }

        self::info("actionFixProductInfoJsonType Done!" . PHP_EOL);
    }

    /**
     * 根据历史记录修复某用户产品自定义字段缺失问题
     * @param $clientId
     * @param $orderIds
     * @param $userIds
     * @return void
     */
    public function actionFixProductExternalField()
    {
        self::info("actionFixProductExternalField start" . PHP_EOL);
        $clientId = 3432;
        $externalFieldId = 1449671216975;
//        $clientId = 14119;
//        $externalFieldId = 1101142917;
        $selectSql = "select product_id,external_field_data from tbl_product where client_id={$clientId} and enable_flag = 1";
        $editSql = "update tbl_product set external_field_data = :external_field_data where client_id = :client_id and product_id = :product_id ";

        $db = PgActiveRecord::getDbByClientId($clientId);
        $products = $db->createCommand($selectSql)->queryAll();
        $productsNeed = [];
        foreach ($products as $product) {
            $externalFieldData = json_decode($product['external_field_data'], true);
            if (empty($externalFieldData[$externalFieldId])) {
                $productsNeed[] = $product;
            }
        }
        unset($products);
        self::info("product without 【4 成本价】 count:" . count($productsNeed) . PHP_EOL);
//        $rows = 0;
        $chunkCount = 100;
        $productChunks = array_chunk($productsNeed, $chunkCount);

        foreach ($productChunks as $productChunk) {
            $productIds = implode(',', array_column($productChunk, 'product_id'));
            if (!$productIds) {
                continue;
            }
            $selectHistorySql = "select product_id, diff from tbl_product_history where client_id={$clientId} and product_id in ({$productIds}) order by product_id,create_time desc";
            $productHistorys = $db->createCommand($selectHistorySql)->queryAll();
            $productHistoryMap = [];
            foreach ($productHistorys as $productHistory) {
                if (!isset($productHistoryMap[$productHistory['product_id']])) {
                    $productHistoryMap[$productHistory['product_id']] = [];
                }
                $diff = json_decode($productHistory['diff'], true);
                $productHistoryMap[$productHistory['product_id']][] = array_column($diff, null, 'id');
            }
            foreach ($productChunk as $product) {
                if (!isset($productHistoryMap[$product['product_id']])) {
                    continue;
                }
                $diffs = $productHistoryMap[$product['product_id']];
                $needEdit = false;
                $externalFieldVal = '';
                foreach ($diffs as $eachDiff) {
                    if (isset($eachDiff[$externalFieldId])) {
                        if (!empty($eachDiff[$externalFieldId]['new'])) {
                            $externalFieldVal = $eachDiff[$externalFieldId]['new'];
                            $needEdit = true;
                        }
                        break;
                    }
                }
                if ($needEdit) {
                    $originExternalFields = json_decode($product['external_field_data'], true);
                    $originExternalFields[$externalFieldId] = $externalFieldVal;
                    $externalFieldsStr = json_encode($originExternalFields);
//                    "update tbl_product set external_field_data = :external_field_data where client_id = :client_id and product_id = :product_id ";
                    $db->createCommand($editSql)->execute([":client_id" => $clientId, ":product_id" => $product['product_id'], ":external_field_data" => $externalFieldsStr]);
                    self::info("update product_id 【{$product['product_id']}】, current external_field_data:{$externalFieldsStr}" . PHP_EOL);
                }
            }
        }

        self::info("actionFixProductExternalField Done!" . PHP_EOL);
    }

    /**
     * 修复用户销售订单缺失的ES数据（修复场景：用户从回收箱恢复销售订单时没有把订单数据写入ES）
     */
    public function actionFixOrderEsData($clientId = null, $grey = 0, $greyNum = null)
    {
        self::info("FixOrderEsData start" . PHP_EOL);
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey && !is_null($greyNum)) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        $chunkCount = 1000;
        foreach ($clientIds as $clientId) {
            $db = PgActiveRecord::getDbByClientId($clientId);

            // 获取用户所有未删除的订单id
            $sql = "select order_id from tbl_order where client_id = {$clientId} and enable_flag = 1 and delete_flag = 0";
            $orderData = $db->createCommand($sql)->queryAll();
            $orderIds = array_column($orderData, 'order_id');

            // 获取ES中已存在的订单id
            $orderNum = count($orderIds);
            $limit = $orderNum > 10000 ? $orderNum : 10000;
            $orderSearcher = new \common\library\invoice\OrderSearchList();
            $orderSearcher->setClientId($clientId);
            $orderSearcher->setLimit($limit);
            $existOrderIds = $orderSearcher->findIds();

            $lostOrderIds = array_diff($orderIds, $existOrderIds);

            self::info("FixOrderEsData client id:【{$clientId}】, lost order num :" . count($lostOrderIds) . PHP_EOL);
            if (count($lostOrderIds) == 0) {
                continue;
            }
            $orderChunkIds = array_chunk(array_values($lostOrderIds), $chunkCount);
            foreach ($orderChunkIds as $batchOrderIds) {
                \common\library\server\es_search\SearchQueueService::pushOrderQueue(0, $clientId, $batchOrderIds, \Constants::ORDER_INDEX_TYPE_CREATE);
            }
            self::info("FixOrderEsData client id:【{$clientId}】end" . PHP_EOL);
        }
        self::info("FixOrderEsData end" . PHP_EOL);
    }

    /**
     * 重置订单的可操作人为操作人+业务归属人
     */
    public function actionFixOrderUserIds($clientId = null, $grey = 0, $greyNum = null)
    {
        self::info("FixOrderUserId start" . PHP_EOL);
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey && !is_null($greyNum)) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        $chunkCount = 1000;
        foreach ($clientIds as $clientId) {
            self::info("FixOrderUserId client id:【{$clientId}】 start " . PHP_EOL);
            $db = \PgActiveRecord::getDbByClientId($clientId);
            if (!$db) {
                continue;
            }
            // 获取用户所有订单
            $sql = "select handler,users,user_id,order_id from tbl_order where client_id = {$clientId}";
            $orderData = $db->createCommand($sql)->queryAll();
            $updateSql = "update tbl_order set user_id = case ";
            $orderChunks = array_chunk($orderData, $chunkCount);
            $editOrderNum = 0;
            foreach ($orderChunks as $orderChunk) {
                $batchUpdateSql = $updateSql;
                $orderIds = [];
                $needEdit = false;
                foreach ($orderChunk as $singleOrder) {
                    $singleOrder['users'] = json_decode($singleOrder['users'], true);
                    $singleOrder['handler'] = array_filter(explode(',', ltrim(rtrim($singleOrder['handler'], '}'), '{')));
                    $singleOrder['user_id'] = array_filter(array_unique(explode(',', ltrim(rtrim($singleOrder['user_id'], '}'), '{'))));
                    $performanceUsers = array_filter(array_column($singleOrder['users'], 'user_id'));
                    $realUserIds = array_unique(array_merge($singleOrder['handler'], $performanceUsers));
                    if (!array_diff($realUserIds, $singleOrder['user_id']) && !array_diff($singleOrder['user_id'], $realUserIds)) {
                        continue;
                    }
                    $realUserIdsStr = implode(',', $realUserIds);
                    $batchUpdateSql .= " when order_id={$singleOrder['order_id']} then '{" . $realUserIdsStr . "}' ";
                    $needEdit = true;
                    $editOrderNum++;
                    $orderIds[] = $singleOrder['order_id'];
                }

                if ($needEdit) {
                    $orderIdsStr = implode(',', $orderIds);
                    $batchUpdateSql .= " else user_id end where client_id={$clientId} and order_id in ({$orderIdsStr})";
                    $db->createCommand($batchUpdateSql)->execute();
                }
            }

            self::info("FixOrderUserId client id:【{$clientId}】, update order num :" . $editOrderNum . PHP_EOL);
        }
        self::info("FixOrderUserId end" . PHP_EOL);
    }

    /**
     * 重置订单的可操作人回滚操作
     */
    public function actionFixOrderUserIdsRollback($clientId = null, $grey = 0, $greyNum = null)
    {
        self::info("FixOrderUserIdsRollback start" . PHP_EOL);
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey && !is_null($greyNum)) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        $chunkCount = 1000;
        foreach ($clientIds as $clientId) {
            $db = \PgActiveRecord::getDbByClientId($clientId);
            if (!$db) {
                continue;
            }
            // 获取用户所有订单
            $sql = "select handler,create_user,users,user_id,order_id from tbl_order where client_id = {$clientId}";
            $orderData = $db->createCommand($sql)->queryAll();
            $updateSql = "update tbl_order set user_id = case ";
            $orderChunks = array_chunk($orderData, $chunkCount);
            $editOrderNum = 0;
            foreach ($orderChunks as $orderChunk) {
                $batchUpdateSql = $updateSql;
                $orderIds = [];
                $needEdit = false;
                foreach ($orderChunk as $singleOrder) {
                    $singleOrder['users'] = json_decode($singleOrder['users'], true);
                    $singleOrder['handler'] = array_filter(explode(',', ltrim(rtrim($singleOrder['handler'], '}'), '{')));
                    $singleOrder['user_id'] = array_filter(array_unique(explode(',', ltrim(rtrim($singleOrder['user_id'], '}'), '{'))));
                    $performanceUsers = array_filter(array_column($singleOrder['users'], 'user_id'));
                    $realUserIds = array_unique(array_merge($singleOrder['handler'], $performanceUsers, [$singleOrder['create_user']]));
                    if (!array_diff($realUserIds, $singleOrder['user_id']) && !array_diff($singleOrder['user_id'], $realUserIds)) {
                        continue;
                    }
                    $realUserIdsStr = implode(',', $realUserIds);
                    $batchUpdateSql .= " when order_id={$singleOrder['order_id']} then '{" . $realUserIdsStr . "}' ";
                    $needEdit = true;
                    $editOrderNum++;
                    $orderIds[] = $singleOrder['order_id'];
                }

                if ($needEdit) {
                    $orderIdsStr = implode(',', $orderIds);
                    $batchUpdateSql .= " else user_id end where client_id={$clientId} and order_id in ({$orderIdsStr})";
                    $db->createCommand($batchUpdateSql)->execute();
                }
            }

            self::info("FixOrderUserIdsRollback client id:【{$clientId}】, update order num :" . $editOrderNum . PHP_EOL);
        }
        self::info("FixOrderUserIdsRollback end" . PHP_EOL);
    }

    public function actionFixOrderCashCollectionStatus($clientId = null, $grey = 0, $greyNum = null)
    {
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey && !is_null($greyNum)) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        foreach ($clientIds as $clientId) {
            $db = \PgActiveRecord::getDbByClientId($clientId);

            // 收集用户金额为0且有关联已生效回款单的订单
//            $sql = "select c.order_id from tbl_cash_collection as c inner join tbl_order as o
//        on o.order_id=c.order_id where c.enable_flag=1 and c.collect_status=1 and o.enable_flag=1 and o.delete_flag=0 and o.client_id={$clientId} and o.amount=0 group by c.order_id";
            $sql = "select order_id from  tbl_order where enable_flag=1 and delete_flag=0 and client_id={$clientId} and amount=0";
            $orderIds = array_filter(array_column($db->createCommand($sql)->queryAll(), 'order_id'));

            if (!$orderIds) {
                self::info("FixOrderCashCollectionStatus clientId : {$clientId} | does not have order to edit" . PHP_EOL);
                return;
            }

            // 更新回款状态
            $orderIdsStr = implode(',', $orderIds);
            $cashCollectionReferType = CashCollection::REFER_TYPE_ORDER;
            $status = CashCollection::COLLECTION_STATUS_FINISH;
            $editCashStatusSql = "update tbl_cash_collection_status set status={$status} where refer_type={$cashCollectionReferType} and amount=0 and client_id={$clientId} and refer_id in ({$orderIdsStr})";
            $db->createCommand($editCashStatusSql)->execute();

            //            "select order_id, amount, amount_rmb, amount_usd from tbl_order where enable_flag=1 and delete_flag=0 and client_id={$clientId} and amount=0";

            // 更新环节状态
            $clientObject = \common\library\account\Client::getClient($clientId);
            $user = $clientObject->getMasterUser();
            \User::setLoginUser($user);
            $trigger = CashCollectionTrigger::make($clientId, $orderIds);
            $trigger->trigger();

            self::info("FixOrderCashCollectionStatus clientId : {$clientId} | update num :" . count($orderIds) . PHP_EOL);
        }
    }

    public function actionImportPlatformProductEsDoc($clientId = null, $grey = 0, $greyNum = null, $skipClientId = 0)
    {
        self::info("ImportPlatformProductEsDoc start" . PHP_EOL);
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey && !is_null($greyNum)) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        foreach ($clientIds as $clientId) {
            if ($clientId < $skipClientId) {
                continue;
            }
            try {
                $indexName = "third_product_sku";
                $db = \PgActiveRecord::getDbByClientId($clientId);
                if (!$db) {
                    self::info("Not valid client: " . $clientId);
                    continue;
                }
                $platformProductSql = "select platform_product_id from tbl_platform_product where client_id={$clientId} and enable_flag=1 order by platform_product_id";
                $platformProductIds = array_column($db->createCommand($platformProductSql)->queryAll(), 'platform_product_id');
                if (!$platformProductIds) {
                    self::info("ImportPlatformProductEsDocTask clientId: 【{$clientId}】 does not have any enable platform product, skip.");
                    continue;
                }

                $chunkCount = 500;
                $curIdx = 0;
                $totalIdCount = count($platformProductIds);
                $platformProductIdRanges = [];
                while ($curIdx < $totalIdCount) {
                    $platformProductIdRanges[] = ['left' => $platformProductIds[$curIdx], 'right' => $platformProductIds[$curIdx + $chunkCount] ?? null];
                    $curIdx += $chunkCount;
                }

                $totalCount = 0;
                $failCount = 0;
                foreach ($platformProductIdRanges as $range) {
                    $skuSql = 'select s.third_sku_code, s.third_product_id, s.third_sku_id, s.platform_product_id, s.platform_sku_id, s.create_time,s.update_time, p.name as third_product_name, p.model as third_product_model from tbl_platform_product_sku s join tbl_platform_product p on s.platform_product_id=p.platform_product_id where s.client_id=:client_id and p.client_id=:client_id2 and s.enable_flag=1 and p.enable_flag=1 ';
                    if (!empty($range['left'])) {
                        $skuSql .= " and p.platform_product_id >= {$range['left']} and s.platform_product_id >= {$range['left']} ";
                    }
                    if (!empty($range['right'])) {
                        $skuSql .= " and p.platform_product_id <= {$range['right']} and s.platform_product_id <= {$range['right']} ";
                    }
                    $platformSkuInfos = $db->createCommand($skuSql)->queryAll(true, [':client_id' => $clientId, ':client_id2' => $clientId]);

                    if (!$platformSkuInfos) {
                        continue;
                    }

                    $chunkCount = 500;
                    $skuChunkData = array_chunk($platformSkuInfos, $chunkCount);
                    $totalCount += count($platformSkuInfos);
                    $esHandler = new \common\library\server\es_search\PlatformProductHandler();
                    foreach ($skuChunkData as $skuDatum) {
                        $bulk = [];
                        foreach ($skuDatum as $datum) {
                            $body = [
                                'client_id' => $clientId,
                                'platform_sku_id' => $datum['platform_sku_id'],
                                'third_sku_code' => $datum['third_sku_code'],
                                'third_product_id' => $datum['third_product_id'],
                                'third_product_name' => $datum['third_product_name'],
                                'third_sku_id' => $datum['third_sku_id'],
                                'platform_product_id' => $datum['platform_product_id'],
                                'third_product_model' => $datum['third_product_model'],
                                'create_time' => $datum['create_time'],
                                'update_time' => $datum['update_time'],
                            ];
                            $bulk[] = [
                                'index' => [
                                    '_index' => $indexName,
                                    '_id' => $datum['platform_sku_id'],
                                    'routing' => $clientId,
                                ]
                            ];
                            $bulk[] = $body;
                        }
                        $bulkChunkData = [
                            'index' => $indexName,
                            'body' => $bulk,
                            'refresh' => false
                        ];
                        $ret = $esHandler->bulk($bulkChunkData);
                        $failCount += $ret['errorCount'] ?? 0;
                        self::info("ImportPlatformProductEsDocTask clientId: 【{$clientId}】 import total rows:【{$totalCount}】, fail rows:【{$failCount}】");
                    }
                }

                self::info("Finally ImportPlatformProductEsDocTask clientId: 【{$clientId}】 import total rows:【{$totalCount}】, fail rows:【{$failCount}】");
            } catch (\Throwable $e) {
                self::info('Error : ' . $e->getMessage() . ' | File : ' . $e->getFile() . ' | Line : ' . $e->getLine() . ' | client_id : ' . $clientId);
            }
        }
    }

    // 增量导入灰度时间后变更的平台产品ES，100%放量后执行
    public function actionImportPlatformProductEsDocAppend($clientId = null, $grey = 0, $greyNum = null, $skipClientId = 0)
    {
        self::info("ImportPlatformProductEsDocAppend start" . PHP_EOL);
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey && !is_null($greyNum)) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        $lastestUpdateTime = '2022-09-21 10:00:00';
        foreach ($clientIds as $clientId) {
            if ($clientId < $skipClientId) {
                continue;
            }
            try {
                $indexName = "third_product_sku";
                $db = \PgActiveRecord::getDbByClientId($clientId);
                if (!$db) {
                    self::info("Not valid client: " . $clientId);
                    continue;
                }
                $platformProductSql = "select platform_product_id from tbl_platform_product where client_id={$clientId} and enable_flag=1 and update_time >= '{$lastestUpdateTime}'";
                $platformProductIds = array_column($db->createCommand($platformProductSql)->queryAll(), 'platform_product_id');
                if (!$platformProductIds) {
                    self::info("ImportPlatformProductEsDocAppendTask clientId: 【{$clientId}】 does not have appended platform product to update, skip.");
                    continue;
                }

                self::info("ImportPlatformProductEsDocAppendTask clientId: 【{$clientId}】 have 【" . count($platformProductIds) . "】 spu platform product to update, start.");

                $chunkCount = 200;
                $platformProductIdChunks = array_chunk($platformProductIds, $chunkCount);

                $totalCount = 0;
                $failCount = 0;
                foreach ($platformProductIdChunks as $platformProductIdChunk) {
                    $platformProductIdStr = implode(',', $platformProductIdChunk);
                    $skuSql = "select s.third_sku_code, s.third_product_id, s.third_sku_id, s.platform_product_id, s.platform_sku_id, s.create_time,s.update_time, p.name as third_product_name, p.model as third_product_model from tbl_platform_product_sku s join tbl_platform_product p on s.platform_product_id=p.platform_product_id where s.client_id=:client_id and p.client_id=:client_id2 and s.enable_flag=1 and p.enable_flag=1 and p.platform_product_id in ({$platformProductIdStr}) and s.platform_product_id in ({$platformProductIdStr})";
                    $platformSkuInfos = $db->createCommand($skuSql)->queryAll(true, [':client_id' => $clientId, ':client_id2' => $clientId]);

                    if (!$platformSkuInfos) {
                        continue;
                    }

                    $chunkCount = 500;
                    $skuChunkData = array_chunk($platformSkuInfos, $chunkCount);
                    $totalCount += count($platformSkuInfos);
                    $esHandler = new \common\library\server\es_search\PlatformProductHandler();
                    foreach ($skuChunkData as $skuDatum) {
                        $bulk = [];
                        foreach ($skuDatum as $datum) {
                            $body = [
                                'client_id' => $clientId,
                                'platform_sku_id' => $datum['platform_sku_id'],
                                'third_sku_code' => $datum['third_sku_code'],
                                'third_product_id' => $datum['third_product_id'],
                                'third_product_name' => $datum['third_product_name'],
                                'third_sku_id' => $datum['third_sku_id'],
                                'platform_product_id' => $datum['platform_product_id'],
                                'third_product_model' => $datum['third_product_model'],
                                'create_time' => $datum['create_time'],
                                'update_time' => $datum['update_time'],
                            ];
                            $bulk[] = [
                                'index' => [
                                    '_index' => $indexName,
                                    '_id' => $datum['platform_sku_id'],
                                    'routing' => $clientId,
                                ]
                            ];
                            $bulk[] = $body;
                        }
                        $bulkChunkData = [
                            'index' => $indexName,
                            'body' => $bulk,
                            'refresh' => false
                        ];
                        $ret = $esHandler->bulk($bulkChunkData);
                        $failCount += $ret['errorCount'] ?? 0;
                        self::info("ImportPlatformProductEsDocAppendTask clientId: 【{$clientId}】 import total rows:【{$totalCount}】, fail rows:【{$failCount}】");
                    }
                }

                self::info("Finally ImportPlatformProductEsDocAppendTask clientId: 【{$clientId}】 import total rows:【{$totalCount}】, fail rows:【{$failCount}】");
            } catch (\Throwable $e) {
                self::info('Error : ' . $e->getMessage() . ' | File : ' . $e->getFile() . ' | Line : ' . $e->getLine() . ' | client_id : ' . $clientId);
            }
        }
    }

    public function actionImportSkuCostTask($clientId = null, $grey = 0, $greyNum = null)
    {
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey && !is_null($greyNum)) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        foreach ($clientIds as $clientId) {
            // 查询按spu定价的产品
            $db = \PgActiveRecord::getDbByClientId($clientId);
            $costType = ProductConstant::COST_TYPE_SPU;
            $selectSql = "select product_id, cost from tbl_product where client_id={$clientId} and enable_flag = 1 and cost_type = {$costType}";
            $productInfos = $db->createCommand($selectSql)->queryAll();

            if (!$productInfos) {
                self::info("用户【{$clientId}】没有任何产品，跳过");
                return;
            }
            $productInfos = array_column($productInfos, 'cost', 'product_id');

            // 分片修改
            $chunkCount = 1000;
            $productChunks = array_chunk($productInfos, $chunkCount, true);
            foreach ($productChunks as $k => $productChunk) {
                $editSql = "update tbl_product_sku set cost = case ";
                $productIds = implode(',', array_keys($productChunk));
                foreach ($productChunk as $productId => $cost) {
                    $editSql .= " when product_id={$productId} then {$cost} ";
                }
                $editSql .= " else cost end where client_id = {$clientId} and product_id in ({$productIds})";
                $rows = $db->createCommand($editSql)->execute();
                self::info("用户【{$clientId}】修改sku行数：" . strval($rows));
            }
            self::info("用户【{$clientId}】修改成本价的产品个数：" . count($productInfos));
        }
    }

    public function actionAddInventoryDownloadPrivilege($clientId = 14119)
    {
        $addRoleName = [];
        $privilegeIds = ['crm.inventory.query.download'];
        $roleService = PrivilegeService::getInstance($clientId)->getUserPrivilege()->getRoles();
        $roleList = $roleService->getAllRoleList();

        // 获取所有角色的库存查看权限
        $roleIds = array_column($roleList, 'role_id');
        $roleIdsStr = implode(',', $roleIds);
        $db = PrivilegeRoleAccessModel::model()->getDbConnection();
        $selectSql = "select role_id, enable_flag from " . PrivilegeRoleAccessModel::model()->tableName() . " where privilege='crm.inventory.query.view' and role_id in ({$roleIdsStr})";
        $viewPrivileges = array_column($db->createCommand($selectSql)->queryAll(), 'enable_flag', 'role_id');
        foreach ($roleList as $roleInfo) {
            if (!empty($viewPrivileges[$roleInfo['role_id']])) {
                $roleService->assignRolePrivilege($roleInfo['role_id'], $privilegeIds, $roleInfo['scope']);
                $addRoleName[] = $roleInfo['role_name'];
            } else {
                $roleService->removeRolePrivilege($roleInfo['role_id'], $privilegeIds);
            }
        }
        Helper::refreshPrivilege($clientId);
        self::info(sprintf("AddInventoryDownloadPrivilege:client_id[%s] role_name[%s]\r\n",
            $clientId, implode(',', $addRoleName)));
    }

    public function actionAddEnableCountCustomField($clientId = '', $grey = 0, $greyNum = null)
    {
        if ($clientId) {
            $clientIds = explode(',', str_replace(' ', '', $clientId));
        } elseif ($grey && !is_null($greyNum)) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        foreach ($clientIds as $clientId) {
            $insertConfig = \common\commands\iteration\bugfix_1013\AddEnableCountCustomField::enableInventoryNumFieldsConfig();

            try {
                // 判断是否存在前置字段
                $clientDb = \ProjectActiveRecord::getDbByClientId($clientId);
                if (!$clientDb) {
                    self::info("Invalid client:" . $clientId . PHP_EOL);
                    continue;
                }
                $selectOrderSql = "select type from tbl_custom_field where client_id={$clientId} and type in (2,22) and id='count'";
                $orderFieldTypes = array_column($clientDb->createCommand($selectOrderSql)->queryAll(), 'type');

                \common\library\custom_field\Helper::addField($clientId, $insertConfig);      // 添加字段
                foreach ($insertConfig as $field) {
                    if (!in_array($field['type'], $orderFieldTypes)) {        // 如果没有count字段则不调整顺序
                        continue;
                    }
                    \common\commands\iteration\bugfix_1013\AddEnableCountCustomField::setNewFieldOrderBeforeOrAfter($field['id'], $field['before'], $clientId, $field['type'], true);
                }
                $clientDb->setActive(false);        // 断开Db连接
            } catch (\Throwable $e) {
                throw new \RuntimeException("Error:" . $e->getMessage() . ", File:" . $e->getFile() . ", Line:" . $e->getLine());
            }
        }
    }

    public function actionAddEnableCountSystemField()
    {
        $fields = AddEnableCountCustomField::enableInventoryNumFieldsConfig();
        foreach ($fields as $field) {
            \common\library\custom_field\Helper::syncSystemFields($field['type'], [$field]);
        }
    }

    public function actionUpdateProductSearchData($clientId = '', $grey = 0, $greyNum = null, $skipClientId = 0, $updateTime = '')
    {
        if ($clientId) {
            $clientIds = explode(',', str_replace(' ', '', $clientId));
        } elseif ($grey && !is_null($greyNum)) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        foreach ($clientIds as $clientId) {
            if ($clientId < $skipClientId) {
                continue;
            }
            try {
                $db = \PgActiveRecord::getDbByClientId($clientId);
                if (!$db) {
                    self::info("Not valid client: " . $clientId);
                    continue;
                }
                if ($updateTime) {
                    $selectIdSql = "select product_id from tbl_product where client_id = {$clientId} and enable_flag=1 and update_time > '{$updateTime}' order by product_id";
                } else {
                    $selectIdSql = "select product_id from tbl_product where client_id = {$clientId} and enable_flag=1 order by product_id";
                }

                $productIds = array_column($db->createCommand($selectIdSql)->queryAll(), 'product_id');
                if (!$productIds) {
                    self::info("UpdateProductSearchData clientId: 【{$clientId}】 does not have any enable product, skip." . PHP_EOL);
                    continue;
                }

                $chunkCount = 500;
                $curIdx = 0;
                $totalIdCount = count($productIds);
                $productIdRanges = [];
                while ($curIdx < $totalIdCount) {
                    $productIdRanges[] = ['left' => $productIds[$curIdx], 'right' => $productIds[$curIdx + $chunkCount] ?? null];
                    $curIdx += $chunkCount;
                }

                self::info("UpdateProductSearchData clientId: 【{$clientId}】, enable product count: {$totalIdCount}" . PHP_EOL);

                $totalSkuCount = 0;
                $spuFailCount = $skuFailCount = 0;
                foreach ($productIdRanges as $range) {
                    $spuSql = "select product_id,product_no,model,name,cn_name,create_time,update_time,external_field_data from tbl_product where client_id=:client_id and enable_flag=1";
                    $skuSql = 'select sku_id, sku_code, product_id,create_time, update_time from tbl_product_sku where client_id=:client_id and enable_flag=1';
                    if (!empty($range['left'])) {
                        $spuSql .= " and product_id >= {$range['left']} ";
                        $skuSql .= " and product_id >= {$range['left']}";
                    }
                    if (!empty($range['right'])) {
                        $spuSql .= " and product_id <= {$range['right']}";
                        $skuSql .= " and product_id <= {$range['right']}";
                    }
                    $spuInfos = $db->createCommand($spuSql)->queryAll(true, [':client_id' => $clientId]);
                    $skuInfos = $db->createCommand($skuSql)->queryAll(true, [':client_id' => $clientId]);
                    $totalSkuCount += count($skuInfos);
                    if (!$spuInfos) {
                        continue;
                    }

                    $spuInfos = array_column($spuInfos, null, 'product_id');
                    $validSkuInfos = [];
                    foreach ($spuInfos as &$spuInfo) {
                        $externalFieldData = is_array($spuInfo['external_field_data']) ? $spuInfo['external_field_data'] : json_decode($spuInfo['external_field_data'], true);
                        $externalField = [];
                        foreach ($externalFieldData as $field => $value) {
                            if (!$field || !$value)
                                continue;

                            $externalField[] = [
                                'field' => (int)$field,
                                'value' => $value,
                            ];
                        }
                        $spuInfo['external_field_data'] = $externalField;
                    }

                    // 组装sku数据
                    foreach ($skuInfos as $skuInfo) {
                        if (!isset($spuInfos[$skuInfo['product_id']])) {
                            continue;
                        }
                        if (!isset($spuInfos[$skuInfo['product_id']]['full_sku_code'])) {
                            $spuInfos[$skuInfo['product_id']]['full_sku_code'] = [];
                        }
                        $spuInfos[$skuInfo['product_id']]['full_sku_code'][] = $skuInfo['sku_code'];
                        $spu = $spuInfos[$skuInfo['product_id']];
                        $validSkuInfos[$skuInfo['sku_id']] = [
                            'sku_code' => $skuInfo['sku_code'],
                            'full_sku_code' => $skuInfo['sku_code'] ?? '',
                            'client_id' => $clientId,
                            'product_id' => $skuInfo['product_id'],
                            'product_name' => $spu['name'],
                            'product_no' => $spu['product_no'],
                            'product_model' => $spu['model'],
                            'create_time' => date('Y-m-d H:i:s', strtotime($skuInfo['create_time'])),
                            'update_time' => date('Y-m-d H:i:s', strtotime($skuInfo['update_time'])),
                            'external_field' => $spu['external_field_data'] ?? [],
                        ];
                    }

                    // 导入spu数据
                    $spubatchFailCount = \common\commands\iteration\bugfix_1013\UpdateProductSearchData::updateSpu($clientId, $spuInfos);
                    $skubatchFailCount = \common\commands\iteration\bugfix_1013\UpdateProductSearchData::updateSku($clientId, $validSkuInfos);
                    $spuFailCount += $spubatchFailCount;
                    $skuFailCount += $skubatchFailCount;
                    self::info("UpdateProductSearchData client_id: {$clientId} | spubatchFailCount: {$spubatchFailCount}, skubatchFailCount:{$skubatchFailCount}, totalCount:{$totalSkuCount}");
                }
                self::info("UpdateProductSearchData Finish, client_id: {$clientId} | spuFailCount: {$spubatchFailCount}, skubatchFailCount:{$skubatchFailCount}");
                \PgActiveRecord::releaseDbByClientId($clientId);
            } catch (\Throwable $e) {
                self::info('Error : ' . $e->getMessage() . ' | File : ' . $e->getFile() . ' | Line : ' . $e->getLine() . ' | client_id : ' . $clientId);
                continue;
            }
        }
    }

    public function actionCreateEsIndex($modelName)
    {
        $class = '\\common\\models\\es_search\\' . $modelName;
        $model = ElasticSearchActiveRecordV7::model($class);
        $model->createIndex();
    }

    public function actionUpdateOutboundInvoiceEsData($clientId = null, $grey = 0, $greyNum = null, $skipClientId = 0, $startTime = null)
    {
        self::info("UpdateOutboundInvoiceEsData start" . PHP_EOL);
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey && !is_null($greyNum)) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        $chunkCount = 500;
        $model = \common\models\es_search\OutboundInvoiceSearch::model();
        $indexName = $model::index();
        foreach ($clientIds as $clientId) {
            if ($clientId < $skipClientId) {
                continue;
            }
            try {
                $userId = PrivilegeService::getInstance($clientId)->getAdminUserId();
                User::setLoginUserById($userId);
                $db = PgActiveRecord::getDbByClientId($clientId);
                $sql = "select outbound_invoice_id from tbl_outbound_invoice where client_id = {$clientId} and delete_flag = 0";
                if (!empty($startTime)) {
                    $sql = "select outbound_invoice_id from tbl_outbound_invoice where client_id = {$clientId} and delete_flag = 0 and update_time>='{$startTime}'";
                }
                $data = $db->createCommand($sql)->queryAll();
                if (!$data) {
                    self::info("UpdateOutboundInvoiceEsData client id:【{$clientId}】has no outbound invoice" . PHP_EOL);
                    continue;
                }
                $primaryIds = array_column($data, 'outbound_invoice_id');

                $chunkIds = array_chunk($primaryIds, $chunkCount);

                $handler = new \common\library\server\es_search\OutboundInvoiceHandler();
                $handler->setNotIndexExternalFieldType();
                foreach ($chunkIds as $batchIds) {
                    $primaryIdsStr = implode(',', $batchIds);
                    $selectBatchSql = "select client_id, outbound_invoice_id, serial_id, type,external_field_data, create_time, update_time from tbl_outbound_invoice where client_id={$clientId} and outbound_invoice_id in ({$primaryIdsStr})";
                    $batchData = $db->createCommand($selectBatchSql)->queryAll();
                    if (!$batchData) {
                        continue;
                    }

                    $bulk = [];
                    foreach ($batchData as $invoice) {
                        $handler->initData([
                            'id' => $invoice['outbound_invoice_id'],
                            'client_id' => $clientId,
                            'user_id' => $userId,
                            'outbound_invoice_id' => $invoice['outbound_invoice_id'],
                            'type' => \Constants::OUTBOUND_INVOICE_INDEX_TYPE_CREATE,
                            'module_type' => $invoice['type']
                        ]);
                        $bulk[] = [
                            'index' => [
                                '_index' => $indexName,
                                '_id' => $invoice['outbound_invoice_id'],
                                'routing' => $invoice['client_id']
                            ]
                        ];

                        $externalField = $handler->formatExternalFields($invoice['external_field_data']);
                        $bulk[] = [
                            'serial_id' => $invoice['serial_id'],
                            'update_time' => $invoice['update_time'],
                            'create_time' => $invoice['create_time'],
                            'client_id' => $invoice['client_id'],
                            'external_field' => $externalField,
                            'type' => $invoice['type']
                        ];
                    }
                    $params = [
                        'index' => $indexName,
                        'body' => $bulk,
                        'refresh' => false
                    ];
                    $ret = $model::getDbConnection()->bulk($params);
                    self::info("clientId:$clientId | batch count:" . count($batchData) . "| error:" . (!empty($ret['errors']) ? 'true' : 'false'));
                }
                self::info("UpdateOutboundInvoiceEsData client id:【{$clientId}】end" . PHP_EOL);
            } catch (\Throwable $e) {
                echo "========UpdateOutboundInvoiceEsData error:$clientId error_code:{$e->getCode()}\r\n";
                self::info("========UpdateOutboundInvoiceEsData error:$clientId  error_code:{$e->getCode()} msg:{$e->getMessage()} line:{$e->getLine()}");
            }
        }
        self::info("UpdateOutboundInvoiceEsData end" . PHP_EOL);
    }

    public function actionUpdateInboundInvoiceEsData($clientId = null, $grey = 0, $greyNum = null, $skipClientId = 0, $startTime = null)
    {
        self::info("UpdateInboundInvoiceEsData start" . PHP_EOL);
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey && !is_null($greyNum)) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        $chunkCount = 500;
        $model = \common\models\es_search\InboundInvoiceSearch::model();
        $indexName = $model::index();
        foreach ($clientIds as $clientId) {
            if ($clientId < $skipClientId) {
                continue;
            }
            try {
                $userId = PrivilegeService::getInstance($clientId)->getAdminUserId();
                User::setLoginUserById($userId);
                $db = PgActiveRecord::getDbByClientId($clientId);
                $sql = "select inbound_invoice_id from tbl_inbound_invoice where client_id = {$clientId} and delete_flag = 0";
                if (!empty($startTime)) {
                    $sql = "select inbound_invoice_id from tbl_inbound_invoice where client_id = {$clientId} and delete_flag = 0 and update_time>='{$startTime}'";
                }
                $data = $db->createCommand($sql)->queryAll();
                if (!$data) {
                    self::info("UpdateInboundInvoiceEsData client id:【{$clientId}】has no inbound invoice" . PHP_EOL);
                    continue;
                }
                $primaryIds = array_column($data, 'inbound_invoice_id');

                $chunkIds = array_chunk($primaryIds, $chunkCount);

                $handler = new \common\library\server\es_search\InboundInvoiceHandler();
                $handler->setNotIndexExternalFieldType();
                foreach ($chunkIds as $batchIds) {
                    $primaryIdsStr = implode(',', $batchIds);
                    $selectBatchSql = "select client_id, inbound_invoice_id, serial_id, type,external_field_data, create_time, update_time from tbl_inbound_invoice where client_id={$clientId} and inbound_invoice_id in ({$primaryIdsStr})";
                    $batchData = $db->createCommand($selectBatchSql)->queryAll();
                    if (!$batchData) {
                        continue;
                    }

                    $bulk = [];
                    foreach ($batchData as $invoice) {
                        $handler->initData([
                            'id' => $invoice['inbound_invoice_id'],
                            'client_id' => $clientId,
                            'user_id' => $userId,
                            'inbound_invoice_id' => $invoice['inbound_invoice_id'],
                            'type' => \Constants::INBOUND_INVOICE_INDEX_TYPE_CREATE,
                            'module_type' => $invoice['type']
                        ]);
                        $bulk[] = [
                            'index' => [
                                '_index' => $indexName,
                                '_id' => $invoice['inbound_invoice_id'],
                                'routing' => $invoice['client_id']
                            ]
                        ];

                        $externalField = $handler->formatExternalFields($invoice['external_field_data']);
                        $bulk[] = [
                            'serial_id' => $invoice['serial_id'],
                            'update_time' => $invoice['update_time'],
                            'create_time' => $invoice['create_time'],
                            'client_id' => $invoice['client_id'],
                            'external_field' => $externalField,
                            'type' => $invoice['type']
                        ];
                    }
                    $params = [
                        'index' => $indexName,
                        'body' => $bulk,
                        'refresh' => false
                    ];
                    $ret = $model::getDbConnection()->bulk($params);
                    self::info("clientId:$clientId | batch count:" . count($batchData) . "| error:" . (!empty($ret['errors']) ? 'true' : 'false'));
                }
                self::info("UpdateinboundInvoiceEsData client id:【{$clientId}】end" . PHP_EOL);
            } catch (\Throwable $e) {
                echo "========UpdateinboundInvoiceEsData error:$clientId error_code:{$e->getCode()}\r\n";
                self::info("========UpdateinboundInvoiceEsData error:$clientId  error_code:{$e->getCode()} msg:{$e->getMessage()} line:{$e->getLine()}");
            }
        }
        self::info("UpdateinboundInvoiceEsData end" . PHP_EOL);
    }

    public function actionUpdatePaymentInvoiceEsData($clientId = null, $grey = 0, $greyNum = null, $skipClientId = 0, $startTime = null)
    {
        self::info("UpdatepaymentInvoiceEsData start" . PHP_EOL);
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey && !is_null($greyNum)) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        $chunkCount = 500;
        $model = \common\models\es_search\PaymentInvoiceSearch::model();
        $indexName = $model::index();
        foreach ($clientIds as $clientId) {
            if ($clientId < $skipClientId) {
                continue;
            }
            try {
                $userId = PrivilegeService::getInstance($clientId)->getAdminUserId();
                User::setLoginUserById($userId);
                $db = PgActiveRecord::getDbByClientId($clientId);
                $sql = "select payment_invoice_id from tbl_payment_invoice where client_id = {$clientId} and enable_flag = 1";
                if (!empty($startTime)) {
                    $sql = "select payment_invoice_id from tbl_payment_invoice where client_id = {$clientId} and enable_flag = 1 and update_time>='{$startTime}'";
                }
                $data = $db->createCommand($sql)->queryAll();
                if (!$data) {
                    self::info("UpdatepaymentInvoiceEsData client id:【{$clientId}】has no payment invoice" . PHP_EOL);
                    continue;
                }
                $primaryIds = array_column($data, 'payment_invoice_id');

                $chunkIds = array_chunk($primaryIds, $chunkCount);

                $handler = new \common\library\server\es_search\PaymentInvoiceHandler();
                $handler->setNotIndexExternalFieldType();
                foreach ($chunkIds as $batchIds) {
                    $primaryIdsStr = implode(',', $batchIds);
                    $selectBatchSql = "select client_id, payment_invoice_id, payment_invoice_no, trade_no, bank_account,bank_name, account_name, external_field_data,create_time, update_time from tbl_payment_invoice where client_id={$clientId} and payment_invoice_id in ({$primaryIdsStr})";
                    $batchData = $db->createCommand($selectBatchSql)->queryAll();
                    if (!$batchData) {
                        continue;
                    }

                    $bulk = [];
                    foreach ($batchData as $invoice) {
                        $handler->initData([
                            'id' => $invoice['payment_invoice_id'],
                            'client_id' => $clientId,
                            'user_id' => $userId,
                            'payment_invoice_id' => $invoice['payment_invoice_id'],
                            'type' => \Constants::PAYMENT_INVOICE_INDEX_TYPE_CREATE,
                        ]);
                        $bulk[] = [
                            'index' => [
                                '_index' => $indexName,
                                '_id' => $invoice['payment_invoice_id'],
                                'routing' => $invoice['client_id']
                            ]
                        ];

                        $externalField = $handler->formatExternalFields($invoice['external_field_data']);
                        $bulk[] = [
                            'payment_invoice_no' => $invoice['payment_invoice_no'],
                            'trade_no' => $invoice['trade_no'],
                            'bank_account' => $invoice['bank_account'],
                            'bank_name' => $invoice['bank_name'],
                            'account_name' => $invoice['account_name'],
                            'update_time' => $invoice['update_time'],
                            'create_time' => $invoice['create_time'],
                            'client_id' => $invoice['client_id'],
                            'external_field' => $externalField
                        ];
                    }
                    $params = [
                        'index' => $indexName,
                        'body' => $bulk,
                        'refresh' => false
                    ];
                    $ret = $model::getDbConnection()->bulk($params);
                    self::info("clientId:$clientId | batch count:" . count($batchData) . "| error:" . (!empty($ret['errors']) ? 'true' : 'false'));
                }
                self::info("UpdatepaymentInvoiceEsData client id:【{$clientId}】end" . PHP_EOL);
            } catch (\Throwable $e) {
                echo "========UpdatepaymentInvoiceEsData error:$clientId error_code:{$e->getCode()}\r\n";
                self::info("========UpdatepaymentInvoiceEsData error:$clientId  error_code:{$e->getCode()} msg:{$e->getMessage()} line:{$e->getLine()}");
            }
        }
        self::info("UpdatepaymentInvoiceEsData end" . PHP_EOL);
    }

    public function actionFixSupplierEsData($clientId = null, $grey = 0, $greyNum = null, $skipClientId = 0, $startTime = null)
    {
        self::info("FixSupplierEsData start" . PHP_EOL);
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey && !is_null($greyNum)) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        $chunkCount = 500;
        $model = \common\models\es_search\SupplierSearch::model();
        $indexName = $model::index();
        foreach ($clientIds as $clientId) {
            if ($clientId < $skipClientId) {
                continue;
            }
            try {
                $db = PgActiveRecord::getDbByClientId($clientId);
                $mysql = ProjectActiveRecord::getDbByClientId($clientId);
                $notIndexFieldTypes = [
                    \common\library\custom_field\CustomFieldService::FIELD_TYPE_IMAGE,
                    \common\library\custom_field\CustomFieldService::FIELD_TYPE_ATTACH
                ];

                $fields = $mysql->createCommand("select id,field_type,relation_field,relation_type,relation_field_type from tbl_custom_field where client_id={$clientId} AND `type`=" . \Constants::TYPE_SUPPLIER . " AND base=0")->queryAll();
                $fields = array_column($fields, null, 'id');

                // 获取用户所有未删除的供应商id
                $sql = "select supplier_id from tbl_supplier where client_id = {$clientId}";
                if (!empty($startTime)) {
                    $sql = "select supplier_id from tbl_supplier where client_id = {$clientId} and update_time>='{$startTime}'";
                }
                $supplierData = $db->createCommand($sql)->queryAll();
                if (!$supplierData) {
                    self::info("FixSupplierEsData client id:【{$clientId}】has no supplier" . PHP_EOL);
                    continue;
                }
                $supplierIds = array_column($supplierData, 'supplier_id');

                $supplierChunkIds = array_chunk($supplierIds, $chunkCount);
                foreach ($supplierChunkIds as $batchSupplierIds) {
//                    \common\library\server\es_search\SearchQueueService::pushSupplierQueue( $clientId,\Constants::SUPPLIER_INDEX_TYPE_CREATE,$batchSupplierIds );
                    $supplierIdsStr = implode(',', $batchSupplierIds);
                    $selectBatchSql = "select client_id,user_ids,supplier_no,supplier_id,name,external_field_data,update_time,create_time from tbl_supplier where client_id={$clientId} and supplier_id in ({$supplierIdsStr})";
                    $batchData = $db->createCommand($selectBatchSql)->queryAll();
                    if (!$batchData) {
                        continue;
                    }

                    $bulk = [];
                    foreach ($batchData as $supplier) {
                        $externalFieldData = is_array($supplier['external_field_data']) ? $supplier['external_field_data'] : (\json_decode($supplier['external_field_data'], true) ?? []);
                        $externalField = $externalFieldNumeric = [];
                        foreach ($externalFieldData as $field => $value) {
                            if (
                                !$field || !$value || !isset($fields[$field]) ||
                                in_array($fields[$field]['field_type'], $notIndexFieldTypes) ||
                                in_array($fields[$field]['relation_field_type'], $notIndexFieldTypes)
                            ) {
                                continue;
                            }

                            $externalField[] = [
                                'field' => (int)$field,
                                'value' => $value,
                            ];

                            if (
                                $fields[$field]['field_type'] == \common\library\custom_field\CustomFieldService::FIELD_TYPE_NUMBER ||
                                $fields[$field]['relation_field_type'] == \common\library\custom_field\CustomFieldService::FIELD_TYPE_NUMBER
                            ) {
                                $externalFieldNumeric[] = [
                                    'field' => (int)$field,
                                    'value' => (double)$value,
                                ];
                            }
                        }
                        $bulk[] = [
                            'index' => [
                                '_index' => $indexName,
                                '_id' => $supplier['supplier_id'],
                                'routing' => $supplier['client_id']
                            ]
                        ];
                        $bulk[] = [
                            'supplier_id' => $supplier['supplier_id'], //供应商id
                            'name' => $supplier['name'], //供应商名称
                            'supplier_no' => $supplier['supplier_no'], //供应商编号
                            'update_time' => $supplier['update_time'],
                            'create_time' => $supplier['create_time'],
                            'client_id' => $supplier['client_id'], //client_id
                            'user_ids' => PgsqlUtil::trimArray($supplier['user_ids']), //user_ids
                            'external_field' => $externalField, //自定义字段
                            'external_field_numeric' => $externalFieldNumeric,
                        ];
                    }
                    $params = [
                        'index' => $indexName,
                        'body' => $bulk,
                        'refresh' => false
                    ];
                    $ret = $model::getDbConnection()->bulk($params);
                    self::info("clientId:$clientId | batch count:" . count($batchData) . "| error:" . (!empty($ret['errors']) ? 'true' : 'false'));
                }
                self::info("FixSupplierEsData client id:【{$clientId}】end" . PHP_EOL);
            } catch (\Throwable $e) {
                echo "========syncSupplierIndexError:$clientId error_code:{$e->getCode()}\r\n";
                self::info("========syncSupplierIndexError:$clientId  error_code:{$e->getCode()} msg:{$e->getMessage()} line:{$e->getLine()}");
            }
        }
        self::info("FixSupplierEsData end" . PHP_EOL);
    }

    public function actionFixProductSkuDefaultImage($clientId = null, $grey = 0, $greyNum = null, $skipClientId = 0)
    {
        self::info("FixProductSkuDefaultImage start" . PHP_EOL);
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey && !is_null($greyNum)) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        foreach ($clientIds as $clientId) {
            if ($clientId < $skipClientId) {
                continue;
            }
            try {
                $db = \PgActiveRecord::getDbByClientId($clientId);

                // 查询用户所有spu有图片但sku无图片的无规格或组合产品
                $sql = "select p.product_id,p.images #>> '{0,id}' as image_file_id from tbl_product p inner join tbl_product_sku sku on p.product_id = sku.product_id where p.client_id={$clientId} and sku.client_id={$clientId} and product_type != 2 and images != '[]' and images != '{}' and p.enable_flag=1 and sku.enable_flag=1 and sku.image_file_id=0";
                $targetProducts = $db->createCommand($sql)->queryAll();

                $chunkNum = 500;
                $chunks = array_chunk($targetProducts, $chunkNum);
                foreach ($chunks as $targetChunkProducts) {
                    $productIds = [];
                    $editSql = "update tbl_product_sku set image_file_id = case";
                    foreach ($targetChunkProducts as $product) {
                        if (empty($product['image_file_id'])) {
                            continue;
                        }
                        $productIds[] = $product['product_id'];
                        $editSql .= " when product_id={$product['product_id']} then {$product['image_file_id']} ";
                    }
                    if (!empty($productIds)) {
                        $productIdsStr = implode(',', $productIds);
                        $editSql .= " else image_file_id end where product_id in ({$productIdsStr}) and image_file_id = 0 and client_id={$clientId}";

                        $db->createCommand($editSql)->execute();
                    }
                    \LogUtil::info("clientId:[{$clientId}], fix product num:[" . count($productIds) . "]");
                }

                \LogUtil::info("clientId:[{$clientId}], fix product finish~");
            } catch (\Throwable $e) {
                self::info("Error:" . $e->getMessage() . " | File:" . $e->getFile() . " | Line:" . $e->getLine());
            }
        }
    }

    public function actionFixPlatformSkuThirdDelete($clientId = null, $grey = 0, $greyNum = null, $skipClientId = 0)
    {
        self::info("FixPlatformSkuThirdDelete start" . PHP_EOL);
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey && !is_null($greyNum)) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        foreach ($clientIds as $clientId) {
            if ($clientId < $skipClientId) {
                continue;
            }

            try {
                $db = \PgActiveRecord::getDbByClientId($clientId);

                // 查询用户疑似删除的spu
                $selectSql = "select platform_product_id from tbl_platform_product where client_id={$clientId} and enable_flag=1 and third_delete=1";
                $thirdDeleteSpu = array_column($db->createCommand($selectSql)->queryAll(), 'platform_product_id');

                if (empty($thirdDeleteSpu)) {
                    self::info("clientId:[{$clientId}], thirdDeleteSpu num:0");
                    continue;
                }
                $chunkNum = 500;
                $chunkSpuIds = array_chunk($thirdDeleteSpu, $chunkNum);
                foreach ($chunkSpuIds as $batchSpuIds) {
                    $spuIdStr = implode(',', $batchSpuIds);
                    $editSql = "update tbl_platform_product_sku set third_delete=1 where client_id={$clientId} and platform_product_id in ({$spuIdStr}) and enable_flag=1 and third_delete != 1";
                    $affectRows = $db->createCommand($editSql)->execute();
                    self::info("clientId:[{$clientId}], fix thirdDeleteSku num: {$affectRows}");
                }

                self::info("clientId:[{$clientId}], FixPlatformSkuThirdDelete finish~");
            } catch (\Throwable $e) {
                self::info("Error:" . $e->getMessage() . " | File:" . $e->getFile() . " | Line:" . $e->getLine());
                continue;
            }
        }
    }

    public function actionFixGenLocalProductTaskStatus($clientId = 0, $importId = 0, $status = 0)
    {
        if (empty($clientId) || empty($importId) || empty($status)) {
            self::info("Param illegal!");
            return;
        }

        if (!isset(\common\library\import\ImportConstants::$statusMap[$status])) {
            self::info("Status illegal!");
            return;
        }
        $editSql = "update tbl_import set status={$status} where client_id={$clientId} and type=27 and import_id={$importId}";
        $db = \PgActiveRecord::getDbByClientId($clientId);
        $affectRows = $db->createCommand($editSql)->execute();
        self::info("affectRows:" . $affectRows);
    }

    // 用户产品(从平台产品生成)sku去重
    public function actionFixProductRepeatSku($clientId = 0)
    {
        if (!$clientId) {
            self::info("client id empty!");
            return;
        }
        try {
            $db = \PgActiveRecord::getDbByClientId($clientId);
            $sql = "select product_id, product_no, name, sku_attributes from tbl_product where client_id={$clientId} and enable_flag=1 and source_type in (1,7) and product_type = 2";
            $products = $db->createCommand($sql)->queryAll();
            $repeatProducts = [];
            foreach ($products as $product) {
                $skuAttributes = json_decode($product['sku_attributes'], true);
                $repeat = false;
                foreach ($skuAttributes as $attrKey => &$skuAttribute) {
                    $attrVals = $skuAttribute['attrs'];
                    $uniqueAttrVals = array_unique($attrVals);
                    if (count($attrVals) != count($uniqueAttrVals)) {
                        $repeat = true;
                        $skuAttribute['attrs'] = $uniqueAttrVals;
                    }
                }
                if ($repeat) {
                    $repeatProducts[$product['product_id']] = $skuAttributes;
                }
            }

            $repeatProductIds = array_keys($repeatProducts);
            if (!$repeatProductIds) {
                self::info("No sku repeat product");
                return;
            }
            $repeatProductIds = implode(',', $repeatProductIds);
            $skuSql = "select sku_id, product_id, attributes from tbl_product_sku where client_id={$clientId} and enable_flag=1 and product_id in ({$repeatProductIds}) order by product_id";
            $skus = $db->createCommand($skuSql)->queryAll();
            $deleteSkuIds = [];
            $repeatAttributes = [];     // 以product_id作为key
            foreach ($skus as $sku) {
                $attributes = json_decode($sku['attributes'], true);
                ksort($attributes);
                $attributesStr = "";
                foreach ($attributes as $attrKey => $attribute) {
                    $attributesStr .= "{$attrKey}:{$attribute['attr']}|";
                }
                $attributesStr = rtrim($attributesStr, '|');
                $key = $sku['product_id'] . '_' . $attributesStr;
                if (!isset($repeatAttributes[$key])) {
                    $repeatAttributes[$key] = false;
                } else {
                    $repeatAttributes[$key] = true;
                    $deleteSkuIds[] = $sku['sku_id'];
                }
            }

            $editSpuSql = "update tbl_product set sku_attributes= case ";
            foreach ($repeatProducts as $productId => $skuAttributes) {
                $skuAttributesStr = json_encode($skuAttributes);
                $editSpuSql .= " when product_id = {$productId} then '{$skuAttributesStr}' ";
            }
            $editSpuSql .= " else sku_attributes end where client_id={$clientId} and product_id in ({$repeatProductIds})";

            $deleteSkuIdsStr = implode(',', $deleteSkuIds);
            $editSkuSql = "update tbl_product_sku set enable_flag=0 where sku_id in ({$deleteSkuIdsStr}) and client_id = {$clientId}";

            self::info('editSpuSql:' . $editSpuSql);
            self::info('editSkuSql:' . $editSkuSql);

            $db->createCommand($editSpuSql)->execute();
            $db->createCommand($editSkuSql)->execute();
        } catch (\Throwable $e) {
            self::info("Error:" . $e->getMessage() . " | File:" . $e->getFile() . " | Line:" . $e->getLine());
        }
    }

    public function actionGetFormulaInfo($clientId = null, $grey = 0, $greyNum = null, $skipClientId = 0)
    {
        self::info("GetFormulaInfo start" . PHP_EOL);
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey && !is_null($greyNum)) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        $fp = "/tmp/formula_zbp.csv";
        if (is_file($fp)) {
            unlink($fp);
        }
        foreach ($clientIds as $clientId) {
            if ($clientId < $skipClientId) {
                continue;
            }

            try {
                $db = \ProjectActiveRecord::getDbByClientId($clientId);
                $sql = "select type, name, ext_info, field_type from tbl_custom_field where client_id={$clientId} and field_type in (11,12) and enable_flag=1";
                $res = $db->createCommand($sql)->queryAll();

                if (count($res) == 0) {
                    self::info("formula field empty 【{$clientId}】");
                    continue;
                }

                $fields = [];
                foreach ($res as &$datum) {
                    $ext_info = json_decode($datum['ext_info'], true);
                    $ext_info = $ext_info['expression'] ?? '';

                    $datum['type'] = $datum['type'] == 2 ? '订单模块' : '商机模块';
                    $datum['field_type'] = $datum['field_type'] == 11 ? '公式' : '汇总';
                    $datum['ext_info'] = $ext_info;
                    preg_match_all('/\{(.*?)\}/', $ext_info, $matchRes);
                    $formulaFields = $matchRes[1] ?? [];
                    foreach ($formulaFields as $k => $v) {
                        $pointPos = strrpos($v, '.');
                        if ($pointPos === false) {
                            continue;
                        }
                        $formulaFields[$k] = substr($v, $pointPos + 1);
                    }
                    $fields = array_merge($fields, $formulaFields);
                    $datum['ext_info_field'] = $formulaFields;
                }
                $fields = array_unique($fields);
                if (empty($fields)) {
                    continue;
                }

                $fieldsBind = [];
                $fieldsParams = [];
                foreach ($fields as $k => $field) {
                    $bind = ":{$field}_{$k}";
                    $fieldsBind[$bind] = $field;
                }
                $fieldsBindKey = array_keys($fieldsBind);
                $fieldsBindStr = implode(',', $fieldsBindKey);
                $fieldSql = "select id,name from tbl_custom_field where client_id={$clientId} and type in (2,9) and id in ({$fieldsBindStr})";
                $fieldRes = $db->createCommand($fieldSql)->queryAll(true, $fieldsBind);
                $fieldRes = array_column($fieldRes, 'name', 'id');

                if ($fieldRes) {
                    foreach ($res as &$datum) {
                        $replacements = [];
                        foreach ($datum['ext_info_field'] as $f) {
                            if (isset($fieldRes[$f])) {
                                $replacements[] = $fieldRes[$f];
                            }
                        }
                        if (count($datum['ext_info_field']) == count($replacements)) {
                            $datum['ext_info'] = str_replace($datum['ext_info_field'], $replacements, $datum['ext_info']);
                        }
                    }
                }

                $cont = "";
                foreach ($res as $datum) {
                    $cont .= "{$clientId},{$datum['name']},{$datum['type']},{$datum['field_type']},{$datum['ext_info']}\n";
                }
                file_put_contents($fp, $cont, FILE_APPEND);
                self::info("【{$clientId}】 finished");
            } catch (\Throwable $e) {
                self::info("Error:" . $e->getMessage() . " | File:" . $e->getFile() . " | Line:" . $e->getLine());
                continue;
            }
        }
    }

    // 修正用户无规格产品 spu 状态正常但 sku 状态为已删除的脏数据
    public function actionFixProductEnableSkuCount($clientId = null, $grey = 0, $greyNum = null, $skipClientId = 0)
    {
        self::info("FixProductEnableSkuCount start" . PHP_EOL);
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey && !is_null($greyNum)) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        foreach ($clientIds as $clientId) {
            if ($clientId < $skipClientId) {
                continue;
            }

            try {
                $db = \PgActiveRecord::getDbByClientId($clientId);

                // 查询用户脏数据
                $selectSql = "select p.product_id,p.product_type,sku.sku_id,p.update_time,sku.update_time as sku_update_time,sku.disable_flag from tbl_product p inner join tbl_product_sku sku on p.product_id = sku.product_id where p.client_id = {$clientId} and sku.client_id = {$clientId} and p.enable_flag=1 and sku.enable_flag=0 and p.product_type in (1,3);";
                $dirtySkus = $db->createCommand($selectSql)->queryAll();

                if (empty($dirtySkus)) {
                    self::info("No dirty sku,client id: {$clientId}");
                    continue;
                }

                self::info("client id:{$clientId}, dirty sku:" . json_encode($dirtySkus));

                $dirtySkuIds = implode(',', array_column($dirtySkus, 'sku_id'));
                $dirtySpuIds = implode(',', array_unique(array_column($dirtySkus, 'product_id')));
                $dirtySpuEnableSkuCount = [];
                foreach ($dirtySkus as $dirtySku) {
                    if (!isset($dirtySpuEnableSkuCount[$dirtySku['product_id']])) {
                        $dirtySpuEnableSkuCount[$dirtySku['product_id']] = 0;
                    }
                    if ($dirtySku['disable_flag'] == 0) {
                        $dirtySpuEnableSkuCount[$dirtySku['product_id']]++;
                    }
                }

                $updateSkuSql = "update tbl_product_sku set enable_flag = 1 where client_id={$clientId} and sku_id in ($dirtySkuIds)";
                $db->createCommand($updateSkuSql)->execute();

                $updateSpuSql = "update tbl_product set enable_sku_count = case ";
                foreach ($dirtySpuEnableSkuCount as $productId => $enableSkuCount) {
                    $updateSpuSql .= " when product_id = {$productId} then {$enableSkuCount} ";
                }
                $updateSpuSql .= " else enable_sku_count end where client_id = {$clientId} and product_id in ($dirtySpuIds)";
                $db->createCommand($updateSpuSql)->execute();

                self::info("clientId:[{$clientId}], FixProductEnableSkuCount finish~");
            } catch (\Throwable $e) {
                self::info("Error:" . $e->getMessage() . " | File:" . $e->getFile() . " | Line:" . $e->getLine());
                continue;
            }
        }
    }

    // debugphp -d 'xdebug.client_port=9004' /data/codebase/bob/php-crm/protected/yiic-test temporary TestAlibabaOrderSync
    public function actionTestAlibabaOrderSync()
    {
        $userId = 11858712;
        \User::setLoginUserById($userId);
        $aliOrderId = '184855282501022011';
        $clientId = User::getLoginUser()->getClientId();
        $userId = User::getLoginUser()->getUserId();
        $storeId = '257429370';

//        $store = new \common\library\alibaba\store\AlibabaStore($clientId, $storeId);
        $store = AlibabaStore::model()->find('store_id=' . $storeId . ' and enable_flag=1');
        $sessionKey = $store->access_token;

        $aliOrder = new \common\library\alibaba\services\AlibabaOrder($sessionKey, $aliOrderId);
        $data = $aliOrder->getInfo([\common\library\alibaba\services\AlibabaOrder::DATA_SELECT_STATUS_ACTION, \common\library\alibaba\services\AlibabaOrder::DATE_SELECT_DRAFT_ROLE]);
        echo json_encode($data);
        $data = $data['value'];
        $alibabaOrderId = $data['trade_id'];
        $processor = new \common\library\alibaba\order\AlibabaOrderSyncProcessor($clientId, $userId, $storeId);
        $processor->setToken($sessionKey);

        $ret = $processor->process($alibabaOrderId, $data);
        echo json_encode($ret);
    }

    public function actionAddOmsV7OrderProductFields($clientId)
    {
        $fields = \common\commands\iteration\oms_v7\OrderProductFieldAppend::getOrderProductAppendFields(true);
        \common\library\custom_field\Helper::addField($clientId, $fields);

        // 调整字段顺序
        $sorter = new \common\library\util\sort\Sorter(new CustomFieldSortRepository($clientId, \Constants::TYPE_ORDER));
        foreach ($fields as $field) {
            $sorter->setId($field['id'], true);
            $sorter->setSortField('order');
            $orderNum = $sorter->after($field['after']);
            $sorter->setSortField('app_order');
            $appOrderNum = $sorter->after($field['after']);
        }
    }

    public function actionAddOmsV7OrderProductSystemFields()
    {
        $settings = OrderProductFieldAppend::getOrderProductAppendFields(false);
        \common\library\custom_field\Helper::syncSystemFields(\Constants::TYPE_ORDER, $settings);
    }

    public function actionAddOmsV7CostInvoiceFields($clientId)
    {
        $fields = \common\commands\iteration\oms_v7\CostInvoiceAddOrderIdField::getAppendFields();
        \common\library\custom_field\Helper::addField($clientId, $fields);

        // 调整字段顺序
        $sorter = new \common\library\util\sort\Sorter(new CustomFieldSortRepository($clientId, \Constants::TYPE_COST_INVOICE));
        $sorter->setResort(true);
        foreach ($fields as $field) {
            $sorter->setId($field['id'], true);
            $sorter->setSortField('order');
            $orderNum = $sorter->after($field['after']);
            $sorter->setSortField('app_order');
            $appOrderNum = $sorter->after($field['after']);
        }
    }

    public function actionAddOmsV7CostInvoiceSystemFields()
    {
        $module = \Constants::TYPE_COST_INVOICE;
        $settings = CostInvoiceAddOrderIdField::getAppendFields();
        \common\library\custom_field\Helper::syncSystemFields($module, $settings);
        \common\commands\iteration\oms_v7\OrderProfitSystemField::resortSystemFields($settings, $module);
    }

    public function actionCostInvoiceRelateOrderId($clientId)
    {
        $updateSql = "update tbl_cost_invoice set order_id = refer_id where client_id={$clientId} and refer_type = 2";
        $db = \PgActiveRecord::getDbByClientId($clientId);
        $editRows = $db->createCommand($updateSql)->execute();

        // 查询关联单据中采购订单关联唯一销售订单的订单号
        $selectSql = "select purchase_order_id,order_id from tbl_purchase_order_product where purchase_order_id in (select refer_id from tbl_cost_invoice where client_id = {$clientId} and refer_type=22 group by refer_id) and client_id={$clientId} and enable_flag = 1 and order_id > 0 group by purchase_order_id,order_id";
        $purchaseOrderInfos = $db->createCommand($selectSql)->queryAll();

        $relation = [];
        foreach ($purchaseOrderInfos as $purchaseOrderInfo) {
            if (!isset($relation[$purchaseOrderInfo['purchase_order_id']])) {
                $relation[$purchaseOrderInfo['purchase_order_id']] = [];
            }
            $relation[$purchaseOrderInfo['purchase_order_id']][] = $purchaseOrderInfo['order_id'];
        }

        $updateSql = 'update tbl_cost_invoice set order_id = case ';
        $targetPurchaseOrderIds = [];
        foreach ($relation as $purchaseOrderId => $orderIds) {
            if (count($orderIds) > 1) {
                continue;
            }
            $targetPurchaseOrderIds[] = $purchaseOrderId;
            $targetOrderId = $orderIds[0] ?? 0;
            $updateSql .= " when refer_id = {$purchaseOrderId} then {$targetOrderId} ";
        }

        if (empty($targetPurchaseOrderIds)) {
            return;
        }

        $targetReferIdStr = implode(',', $targetPurchaseOrderIds);
        $updateSql .= " else order_id end where client_id={$clientId} and refer_type=22 and refer_id in ({$targetReferIdStr}) and order_id = 0";
        $editRows += $db->createCommand($updateSql)->execute();
        self::info("edit cost invoice rows:{$editRows}");
        \LogUtil::info("edit cost invoice rows:{$editRows}");
    }


    public function actionTestXls($filePath){
        $excelReader = new \common\library\util\excel\ExcelReader($filePath);
        $excelReader->setParseImg(true, '/tmp/parse_excel_img');
        $res = $excelReader->getData();
        echo json_encode($res);
    }

    public function actionTestLogInSwoole(){
        $concurrentNum = 1;
        $taskPool = new IOTaskPool($concurrentNum);
        $taskPool->setAsync(true);
        \LogUtil::error('非协程环境环境下使用LogUtil -- 前');
        $task = new CustomizeTask(function(){
            \LogUtil::info('测试swoole环境下使用LogUtil -- 1');
            \LogUtil::info('测试swoole环境下使用LogUtil -- 2');
        });
        $taskPool->addTask($task);
        $taskPool->batchRun();
        \LogUtil::info('非协程环境环境下使用LogUtil -- 后');
    }

    // 修正阿里订单中，阿里产品绑定组合产品时，产品明细表中没有子产品的情况
    public function actionFixOrderAliCombineProduct($clientId=null, $grey = 0, $greyNum = null, $skipClientId = 0)
    {
        self::info("FixProductEnableSkuCount start" . PHP_EOL);
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey && !is_null($greyNum)) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        foreach ($clientIds as $clientId) {
            if ($clientId < $skipClientId) {
                continue;
            }

            try {
                $db = \PgActiveRecord::getDbByClientId($clientId);

                // 查询用户脏数据
                $selectSql = "select refer_id,id,product_name,product_type,combine_record_id,status,user_id,type,product_id from tbl_invoice_product_record where platform_product_id !=0 and product_id in (select product_id from tbl_product where client_id={$clientId} and product_type=3 and enable_flag=1) and client_id={$clientId} and enable_flag=1 and type=2 and product_type=0";
                $dirtyInvoiceProduct = $db->createCommand($selectSql)->queryAll();
                $dirtyInvoiceProduct = array_column($dirtyInvoiceProduct, null, 'id');

                $orderIds = array_unique(array_column($dirtyInvoiceProduct, 'refer_id'));
                if (empty($orderIds)) {
                    self::info("clientId:[{$clientId}], no dirty data~");
                    continue;
                }
                $orderIdStr = implode(',', $orderIds);
                $selectOrderSql = "select order_id, product_list from tbl_order where client_id = {$clientId} and order_id in ($orderIdStr)";
                $orders = $db->createCommand($selectOrderSql)->queryAll();
                $orders = array_column($orders, 'product_list', 'order_id');
                foreach ($orders as $orderId => $productList) {
                    $productList = json_decode($productList, true);
                    $updateInvoiceProduct = false;
                    $updateInvoiceProductSql = "update tbl_invoice_product_record set product_type = 3,combine_product_config = case";
                    $editProductCount = 0;
                    $addSubProductCount = 0;
                    $editUniqueIds = [];
                    foreach ($productList as &$product) {
                        if (isset($dirtyInvoiceProduct[$product['unique_id']])) {
                            $combineProduct = $dirtyInvoiceProduct[$product['unique_id']];
                            $combineProductCfg = (new CombineProductRelationAPI($clientId))->getSubProductInfo($combineProduct['product_id']);
                            if (empty($combineProductCfg)) {
                                continue;
                            }
                            $product['product_type'] = ProductConstant::PRODUCT_TYPE_COMBINE;
                            $combineProductCfg = json_encode($combineProductCfg);
                            $updateInvoiceProductSql .= " when id = {$product['unique_id']} then '{$combineProductCfg}' ";
                            $updateInvoiceProduct = true;
                            $editProductCount++;
                            $editUniqueIds[] = $product['unique_id'];

                            $combineProductConfig = json_decode($combineProductCfg, true);
                            $subProducts = \common\models\client\InvoiceProductRecord::generateSubProductInvoice($clientId, $combineProductConfig, array_merge($product, ['client_id' => $clientId, 'user_id' => $combineProduct['user_id'], 'status' => $combineProduct['status'], 'type' => $combineProduct['type'], 'refer_id' => $combineProduct['refer_id'], 'id' => $combineProduct['id']]));
                            if ($subProducts) {
                                $addSubProductCount += count($subProducts);
                                \common\models\client\InvoiceProductRecord::batchInsert($clientId, $subProducts);
                            }
                        }
                    }

                    $editUniqueIdStr = implode(',', array_filter(array_unique($editUniqueIds)));
                    $updateInvoiceProductSql .= " else combine_product_config end where client_id={$clientId} and refer_id = {$orderId} and id in ({$editUniqueIdStr})";

                    if (!empty(array_filter($editUniqueIds))) {
                        $db->createCommand($updateInvoiceProductSql)->execute();
                    }

                    if ($updateInvoiceProduct) {
                        $updateOrderSql = "update tbl_order set product_list=:product_list where client_id={$clientId} and order_id = :order_id";
                        $db->createCommand($updateOrderSql)->execute([':product_list' => json_encode($productList), ':order_id' => $orderId]);
                    }

                    self::info("clientId:[{$clientId}], order [{$orderId}] fixed with edit combine product count:[{$editProductCount}], add sub product count:[{$addSubProductCount}]");
                }

                self::info("clientId:[{$clientId}], FixOrderAliCombineProduct finish~");
            } catch (\Throwable $e) {
                self::info("Error:" . $e->getMessage() . " | File:" . $e->getFile() . " | Line:" . $e->getLine());
                continue;
            }
        }
    }

    public function actionFixOutboundOriginInfo($clientId)
    {
        $task = new \common\commands\iteration\inventory_pref\FixOutboundOriginInfo();
        $task->execute();
    }

    public function actionInventoryAddGroupField($clientId){
        $sql = "select user_id, client_id, value from tbl_user_setting where client_id={$clientId} and enable_flag=1 and `key`='inventory.report.list.field'";
        $db = \ProjectActiveRecord::getDbByClientId($clientId);
        $userSettings = $db->createCommand($sql)->queryAll();

        if(empty($userSettings)){
            return;
        }

        $groupField = ['id' => 'group', 'width' => 180,"fixed"=>0];
        foreach($userSettings as $setting){
            $fieldPos = [];
            $userId = $setting['user_id'];
            $values = is_array($setting['value']) ? $setting['value'] : json_decode($setting['value'], true);
            foreach($values as $k => $value){
                $fieldPos[$value['id']] = $k;
            }
            if(isset($fieldPos['group'])){
                continue;
            }
            array_splice($values, $fieldPos['name']+1, 0, json_encode($groupField));
            $values[$fieldPos['name']+1] = json_decode($values[$fieldPos['name']+1], true);
            $updateSql = "update tbl_user_setting set `value`=:value where client_id={$clientId} and user_id={$userId} and `key`='inventory.report.list.field'";
            $db->createCommand($updateSql)->execute([':value'=>json_encode($values)]);
            \LogUtil::info("Update inventory.report.list.field user_id:{$userId}, client_id:{$clientId}");
        }
    }

    public function actionTestInvoicePreview(){
        $clientId = 43280;
        $loginUser = 55375314;
        \User::setLoginUserById($loginUser);
        $invoice_id = 2806481430961;
        $template_id = 4961444072466;
        $type = 'excel';
        $name = 'Germax Quotation-Azerbaijan-Rustam-LR-230215';

        // 测试环境
        $loginUser = 11858712;
        \User::setLoginUserById($loginUser);
        $clientId = 14119;
        $invoice_id = 2307565716;
        $template_id = 3250404839;
        $type = 'excel';
        $name = '测试最小起订量【副本】【20230217qiao0014】测试产品排序';
        $export = new \common\library\invoice\export\QuotationExport($clientId,$loginUser,$invoice_id,$template_id);
        $export->setType($type);
        $export->setFileName($name);
        $export->setExportMethod(\common\library\invoice\export\AbstractInvoiceExport::METHOD_PREVIEW, false);
        $export->setModule('crm');
        $start = microtime(true);
        echo json_encode($export->preview());
        echo "\n";
        echo "spending time:".microtime(true) - $start;
    }

    // 移除ERP导入多规格产品的空字符规格
    public function actionFixProductEmptySkuAttr($clientId=null, $grey = 0, $greyNum = null, $skipClientId = 0)
    {
        self::info("actionFixProductEmptySkuAttr start" . PHP_EOL);
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey && !is_null($greyNum)) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        foreach ($clientIds as $clientId) {
            if ($clientId < $skipClientId) {
                continue;
            }

            $sql = "select product_id, sku_attributes from tbl_product where client_id={$clientId} and source_type = 5 and product_type=2 and enable_flag = 1";
            $db = \PgActiveRecord::getDbByClientId($clientId);
            $products = $db->createCommand($sql)->queryAll();

            if(empty($products)){
                self::info("No error product");
                continue;
            }

            $productIds = implode(',', array_column($products, 'product_id'));
            $sql = "select sku_id,attributes from tbl_product_sku where client_id={$clientId} and product_id in ({$productIds}) and enable_flag=1";
            $skus = $db->createCommand($sql)->queryAll();
            foreach($products as $product){
                $skuAttrs = json_decode($product['sku_attributes'], true);
                $valid = true;
                foreach($skuAttrs as $skuAttrKey => $skuAttrVal){
                    if(empty($skuAttrVal['attrs'])){
                        break;
                    }
                    foreach($skuAttrVal['attrs'] as $k => $skuAttrRealVal){
                        if(empty($skuAttrRealVal)){
                            $valid = false;
                            unset($skuAttrVal['attrs'][$k]);
                        }
                    }
                    $skuAttrs[$skuAttrKey]['attrs'] = array_values($skuAttrVal['attrs']);
                }
                if($valid){
                    continue;
                }
                $paramBind = [
                    ':sku_attributes' => json_encode($skuAttrs),
                ];
                $updateSql = "update tbl_product set sku_attributes = :sku_attributes where client_id={$clientId} and product_id = {$product['product_id']}";
                $db->createCommand($updateSql)->execute($paramBind);
            }

            $unValidSkuIds = [];
            foreach($skus as $sku){
                $attrs = json_decode($sku['attributes'], true);
                foreach($attrs as $attrKey=>$attrVal){
                    if(empty($attrVal['attr'])){
                        $unValidSkuIds[] = $sku['sku_id'];
                        break;
                    }
                }
            }

            if(empty($unValidSkuIds)){
                self::info("No error sku");
                continue;
            }

            $unValidSkuIds = implode(',', $unValidSkuIds);
            $updateSkuSql = "update tbl_product_sku set enable_flag = 0 where client_id={$clientId} and sku_id in ({$unValidSkuIds})";
            $db->createCommand($updateSkuSql)->execute();
            self::info("client id: {$clientId} | Error sku:{$unValidSkuIds}");
        }
    }

    //重刷出入库和退货明细的分配和关联关系
    public function actionRefreshInoutboundRelation($clientId)
    {
        // 寻找所有之前修复过的产品的出库单和出库明细
        $skuSql = "select sku_id from tbl_outbound_record where /*status=0 and delete_flag=1 and*/ client_id={$clientId}";
        $db = \PgActiveRecord::getDbByClientId($clientId);
        $wrongSkuIds = array_unique(array_column($db->createCommand($skuSql)->queryAll(), 'sku_id'));       // 可能出错的skuid

        $wrongSkuIdsStr = implode(',', $wrongSkuIds);
        $selectInboundSql = "select warehouse_id,sku_id,inbound_invoice_id, inbound_record_id,inbound_count from tbl_inbound_record where client_id = {$clientId} and sku_id in ({$wrongSkuIdsStr}) and status = 2 and delete_flag=0";     // 已入库的明细
        $selectOutboundSql = "select warehouse_id,sku_id, outbound_record_id,outbound_count,origin_info from tbl_outbound_record where client_id = {$clientId} and sku_id in ({$wrongSkuIdsStr}) and status = 2 and delete_flag=0";      // 已出库的明细
        $selectReturnSql = "select warehouse_id,sku_id, warehouse_return_record_id,return_count,origin_info from tbl_warehouse_return_record where client_id = {$clientId} and sku_id in ({$wrongSkuIdsStr}) and status = 2 and delete_flag=0";      // 已退货的明细
        $inboundRecords = array_column($db->createCommand($selectInboundSql)->queryAll(), null, 'inbound_record_id');
        $outboundRecords = $db->createCommand($selectOutboundSql)->queryAll();
        $returnRecords = $db->createCommand($selectReturnSql)->queryAll();

        $inboundRecordGroup = [];
        foreach ($inboundRecords as $inboundRecord) {
            $key = $inboundRecord['warehouse_id'] . '_' . $inboundRecord['sku_id'];
            if (!isset($inboundRecordGroup[$key])) {
                $inboundRecordGroup[$key] = [];
            }
            $inboundRecord['enable_count'] = $inboundRecord['inbound_count'];
            $inboundRecordGroup[$key][] = $inboundRecord;
        }

        foreach ($outboundRecords as $outboundRecord) {
//            [{"count": "600.0000000000", "inbound_record_id": 3118102402}]
            $key = $outboundRecord['warehouse_id'] . '_' . $outboundRecord['sku_id'];

            $originInfo = [];
            foreach ($inboundRecordGroup[$key] as $k => $inboundRecord) {
                if ($outboundRecord['outbound_count'] == 0) {
                    break;
                }
                if ($inboundRecord['enable_count'] == 0) {
                    continue;
                }
                if ($inboundRecord['enable_count'] >= $outboundRecord['outbound_count']) {
                    $inboundRecord['enable_count'] -= $outboundRecord['outbound_count'];
                    $outboundRecord['outbound_count'] = 0;
                    $originInfo[] = ['count' => $outboundRecord['outbound_count'], 'inbound_record_id' => $inboundRecord['inbound_record_id']];
                } else {
                    $inboundRecord['enable_count'] = 0;
                    $outboundRecord['outbound_count'] -= $inboundRecord['enable_count'];
                    $originInfo[] = ['count' => $inboundRecord['enable_count'], 'inbound_record_id' => $inboundRecord['inbound_record_id']];
                }
                $inboundRecordGroup[$key][$k] = $inboundRecord;
            }

            // 更新出库明细的origin_info
            $originInfo = json_encode($originInfo);
            $updateOutboundSql = "update tbl_outbound_record set origin_info = '{$originInfo}' where client_id = {$clientId} and outbound_record_id ={$outboundRecord['outbound_record_id']} ";
//            $db->createCommand($updateOutboundSql)->execute();
            self::info("updateOutboundSql:" . $updateOutboundSql);
        }

        foreach ($returnRecords as $returnRecord) {
//            [{"count": "600.0000000000", "inbound_record_id": 3118102402}]
            $key = $returnRecord['warehouse_id'] . '_' . $returnRecord['sku_id'];

            $originInfo = [];
            foreach ($inboundRecordGroup[$key] as $k => $inboundRecord) {
                if ($returnRecord['return_count'] == 0) {
                    break;
                }
                if ($inboundRecord['enable_count'] == 0) {
                    continue;
                }
                if ($inboundRecord['enable_count'] >= $returnRecord['return_count']) {
                    $inboundRecord['enable_count'] -= $returnRecord['return_count'];
                    $returnRecord['return_count'] = 0;
                    $originInfo[] = ['count' => $returnRecord['return_count'], 'inbound_record_id' => $inboundRecord['inbound_record_id']];
                } else {
                    $inboundRecord['enable_count'] = 0;
                    $returnRecord['return_count'] -= $inboundRecord['enable_count'];
                    $originInfo[] = ['count' => $inboundRecord['enable_count'], 'inbound_record_id' => $inboundRecord['inbound_record_id']];
                }
                $inboundRecordGroup[$key][$k] = $inboundRecord;
            }

            // 更新origin_info
            $originInfo = json_encode($originInfo);
            $updateReturnSql = "update tbl_warehouse_return_record set origin_info = '{$originInfo}' where client_id = {$clientId} and warehouse_return_record_id ={$returnRecord['warehouse_return_record_id']} ";
//            $db->createCommand($updateReturnSql)->execute();
            self::info("updateReturnSql:" . $updateReturnSql);
        }

        $updateInvoiceIds = [];
        foreach ($inboundRecordGroup as $inboundRecordBatch) {
            $updateInboundSql = "update tbl_inbound_record set enable_count = case ";
            $updateIds = [];
            foreach ($inboundRecordBatch as $inboundRecord) {
                $updateInboundSql .= " when inbound_record_id = {$inboundRecord['inbound_record_id']} then {$inboundRecord['enable_count']} ";
                $updateIds[] = $inboundRecord['inbound_record_id'];
                $updateInvoiceIds[] = $inboundRecord['inbound_invoice_id'];
            }
            $updateIdsStr = implode(',', $updateIds);
            if (!empty($updateIds)) {
                $updateInboundSql .= " else enable_count end where client_id={$clientId} and inbound_record_id in ({$updateIdsStr})";
//                $db->createCommand($updateInboundSql)->execute();
                self::info("updateInboundSql:" . $updateInboundSql);
            }
        }

        $updateInvoiceIds = array_unique($updateInvoiceIds);
        if (!empty($updateInvoiceIds)) {
            $refreshData = [
                'ids' => $updateInvoiceIds,
                'client_id' => $clientId,
                'type' => \common\library\server\refresh_product_inventory\RefreshInventoryQueue::TYPE_INBOUND,
            ];
            self::info("updateInvoiceIds:" . json_encode($updateInvoiceIds));
//            \common\library\server\refresh_product_inventory\RefreshInventoryQueue::refresh($refreshData);
        }

        self::info("actionRefreshInoutboundRelation done");
    }

    public function actionPushInventory($clientId, $type, $invoiceIds){
        $invoiceIds = explode(',', $invoiceIds);
        RefreshInventoryQueue::pushQueue($clientId,$type, $invoiceIds);
    }

    public function actionClearProductQuantity($clientId=null, $grey = 0, $greyNum = null, $skipClientId = 0)
    {
        self::info("actionClearProductQuantity start" . PHP_EOL);
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey && !is_null($greyNum)) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        foreach ($clientIds as $clientId) {
            if ($clientId < $skipClientId) {
                continue;
            }

            $sql = "select product_id, product_no,quantity,source_type,create_time,update_time from tbl_product where quantity !='' and client_id={$clientId} and quantity !~ '^[0-9\.]+$'";
            $db = \PgActiveRecord::getDbByClientId($clientId);
            $products = $db->createCommand($sql)->queryAll();

            if(empty($products)){
                self::info("no product to update" . PHP_EOL);
                continue;
            }

            self::info("product to update: " . json_encode($products));
            $updateProductIds = implode(',', array_column($products, 'product_id'));
            $updateSql = "update tbl_product set quantity='' where client_id={$clientId} and product_id in ({$updateProductIds})";
            self::info("update sql:".$updateSql);
            $rows=0;
//            $rows = $db->createCommand($updateSql)->execute();
            self::info("actionClearProductQuantity end " . $clientId . " | rows:".$rows);
        }

    }

    // 修复公式字段为-1的小数位
    public function actionFixFormulaFieldDecimal($clientId=null, $grey = 0, $greyNum = null, $skipClientId = 0)
    {
        self::info("actionFixFormulaFieldDecimal start" . PHP_EOL);
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey && !is_null($greyNum)) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        foreach ($clientIds as $clientId) {
            if ($clientId < $skipClientId) {
                continue;
            }

            $sql = "select id, ext_info from tbl_custom_field where client_id={$clientId} and field_type in (12,11) and enable_flag=1";
            $db = \ProjectActiveRecord::getDbByClientId($clientId);
            $fields = $db->createCommand($sql)->queryAll();

            if(empty($fields)){
                self::info("no fields to update" . PHP_EOL);
                continue;
            }

            $updateFieldIds = [];
            $paramsBind = [];
            $updateSql = "update tbl_custom_field set ext_info= case ";
            foreach($fields as $k => $field){
                $extInfo = json_decode($field['ext_info'], true);
                if(!$extInfo){
                    continue;
                }
                if(!empty($extInfo['decimal']) && $extInfo['decimal'] < 0){
                    $extInfo['decimal'] = \common\library\custom_field\formula_field\FormulaFieldHandler::FORMULA_DECIMAL_DEFAULT_SIZE;
                    $updateFieldIds[] = $field['id'];
                    $key = ":ext_info_" . $k;
                    $paramsBind[$key] = json_encode($extInfo);
                    $updateSql .= " when id = {$field['id']} then {$key} ";
                }
            }
            if(empty($updateFieldIds)){
                self::info("no fields to update, client_id:" . $clientId);
                continue;
            }

            $updateSql .= " else ext_info end where client_id={$clientId} and id in (".implode(',', $updateFieldIds).") and field_type in (11,12)";
            self::info("update sql:".$updateSql);
            $rows = $db->createCommand($updateSql)->execute($paramsBind);
            self::info("actionFixFormulaFieldDecimal end " . $clientId . " | rows:".$rows);
        }
    }

    // 临时脚本，修复指定用户指定时间的产品
    public function actionRecoverProduct()
    {
        self::info("actionRecoverProduct start" . PHP_EOL);
//        $clientId = 14119;
//        $taskIds = [**********];
        $clientId = 35274;
        $taskIds = [*************,*************,*************,*************,*************,*************,*************,*************];
        $taskIdsStr = implode(',', $taskIds);

        $clientObject = \common\library\account\Client::getClient($clientId);
        $user = $clientObject->getMasterUser();
        \User::setLoginUser($user);

        $productIdsSql = "select distinct product_id from tbl_product_history where product_id in (select product_id from tbl_product where client_id={$clientId} and task_id in ({$taskIdsStr}) and create_time > '2023-02-13 09:00' and create_time < '2023-02-14') and type=4  and create_time > '2023-02-21'";

        $db = \PgActiveRecord::getDbByClientId($clientId);
        $productIds = array_column($db->createCommand($productIdsSql)->queryAll(), 'product_id');

        if(empty($productIds)){
            self::info("no product to recover");
            return;
        }
        self::info("recover product id:".json_encode($productIds));

        $productIdsChunk = array_chunk($productIds, 100);
        $rows = 0;
        foreach($productIdsChunk as $batchProductIds){
//            $rows = common\library\recycle\API::recover($clientId, $user->getUserId(), \common\library\recycle\Recycle::PRODUCT, $batchProductIds);
            self::info("recover rows:".json_encode($rows));
        }
        self::info("actionRecoverProduct finish" . PHP_EOL);
    }

    public function actionTestSyncProductByThirdProductId($clientId, $storeId, $thirdProductIds){
        // 阿里产品id加解密方法：\common\library\alibaba\services\AlibabaProduct::fromDecryptProductId
        $syncSetting = new AlibabaProductSyncSetting($clientId, $storeId);
        $store = $syncSetting->getStore();
        $thirdProductIds = explode(',', $thirdProductIds);
        $cipherIds = [];
        foreach($thirdProductIds as $thirdProductId){
            $aliProduct = \common\library\alibaba\services\AlibabaProduct::fromDecryptProductId($store->access_token, $thirdProductId);
            if($aliProduct){
                $cipherIds[] = $aliProduct->getEncryptProductId();
            }
        }
        $clientObject = \common\library\account\Client::getClient($clientId);
        $user = $clientObject->getMasterUser();
        \User::setLoginUser($user);

        $failureCollector = new AlibabaProductFailureCollector($clientId);
        $processor = new \common\library\alibaba\product\AlibabaProductSyncProcessor($clientId, $store, $failureCollector);
        foreach($cipherIds as $cipherId){
            $processor->processWithLock($cipherId);
        }
    }

    public function actionGetAliGroupsById($clientId, $storeId,$groupId){
        $syncSetting = new AlibabaProductSyncSetting($clientId, $storeId);
        $store = $syncSetting->getStore();
        $groupInfo = \common\library\alibaba\product\AlibabaProductSyncHelper::getGroupsById($store, $groupId);
        echo json_encode($groupInfo);
    }

    public function actionFixRepeatPlatformProduct($clientId=null, $grey = 0, $greyNum = null, $skipClientId = 0)
    {
        self::info("actionFixRepeatPlatformProduct start" . PHP_EOL);
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey && !is_null($greyNum)) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        foreach ($clientIds as $clientId) {
            if ($clientId < $skipClientId) {
                continue;
            }

            try{
                $selectSql = "select third_product_id from tbl_platform_product where client_id = {$clientId} group by third_product_id having count(*)>1";
                $db = \PgActiveRecord::getDbByClientId($clientId);
                $repeatThirdProductIds = array_column($db->createCommand($selectSql)->queryAll(), 'third_product_id');
                if(!$repeatThirdProductIds){
                    self::info("no repeat platform product, client_id:".$clientId);
                    continue;
                }

                $repeatIdsStr = implode(',', $repeatThirdProductIds);

                // 剔除掉关联了订单的平台产品
                $invoiceRecordSql = "select ali_product_id, platform_product_id from tbl_invoice_product_record where client_id={$clientId} and enable_flag=1 and ali_product_id in ({$repeatIdsStr})";
                $excludePlatformProductIds = array_unique(array_column($db->createCommand($invoiceRecordSql)->queryAll(), 'platform_product_id'));

                $selectDetailSql = "select third_product_id, platform_product_id from tbl_platform_product where client_id={$clientId} and third_product_id in ({$repeatIdsStr}) order by enable_flag desc, sync_time desc";
                $repeatProducts = $db->createCommand($selectDetailSql)->queryAll();

                $productKeep = [];
                $platformProductIdToDelete = [];

                foreach($repeatProducts as $repeatProduct){
                    if(in_array($repeatProduct['platform_product_id'], $excludePlatformProductIds)){
                        continue;
                    }
                    if(!isset($productKeep[$repeatProduct['third_product_id']])){       // 某third_product_id下的第一个平台产品保留，其他删除
                        $productKeep[$repeatProduct['third_product_id']] = $repeatProduct['platform_product_id'];
                        continue;
                    }

                    $platformProductIdToDelete[] = $repeatProduct['platform_product_id'];
                }

                $platformProductIdToDelete = array_filter($platformProductIdToDelete);
                $rows = 0;
                if(!empty($platformProductIdToDelete)){
                    $deleteIdsStr = implode(',', $platformProductIdToDelete);

                    // 删除spu表记录
                    $deleteSpuSql = "delete from tbl_platform_product where client_id = {$clientId} and platform_product_id in ({$deleteIdsStr})";
                    self::info($deleteSpuSql);
//                    $rows += $db->createCommand($deleteSpuSql)->execute();

                    // 删除sku表记录
                    $deleteSkuSql = "delete from tbl_platform_product_sku where client_id = {$clientId} and platform_product_id in ({$deleteIdsStr})";
                    self::info($deleteSpuSql);
//                    $rows += $db->createCommand($deleteSkuSql)->execute();

                    // 删除sync表记录
                    $deleteSyncSql = "delete from tbl_platform_product_sync where client_id = {$clientId} and platform_product_id in ({$deleteIdsStr})";
                    self::info($deleteSyncSql);
//                    $rows += $db->createCommand($deleteSyncSql)->execute();
                }
                self::info("Done, delete rows:{$rows}, client_id:".$clientId);
            } catch (\Throwable $e) {
                self::info("clientId:".$clientId." | Error:" . $e->getMessage() . " | File:" . $e->getFile() . " | Line:" . $e->getLine());
                continue;
            }
        }
    }

    public function actionFixOrderProfitConflictModuleType($clientId=null, $grey = 0, $greyNum = null, $skipClientId = 0)
    {
        self::info("actionFixOrderProfitConflictModuleType start" . PHP_EOL);
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey && !is_null($greyNum)) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        $realType = \Constants::TYPE_ORDER_PROFIT;
        $oldType = 48;
        $targetIds = '"'.implode('","', array_column(\common\library\oms\command\OrderProfitFieldSetting::orderProfitListFieldsSetting(),'id')).'"';
        
        foreach ($clientIds as $clientId) {
            if ($clientId < $skipClientId) {
                continue;
            }

            $rows = 0;
            try {
                $updateCustomSql = "update tbl_custom_field set `type`={$realType} where client_id={$clientId} and type={$oldType} and id in ({$targetIds})";
                $updateFieldGroupSql = "update tbl_field_group set `type`={$realType} where client_id={$clientId} and type={$oldType} and id in ({$targetIds})";
                $updateFieldExportSql = "update tbl_field_export_setting set `type`={$realType}, export_type={$realType} where client_id={$clientId} and type={$oldType} and id in ({$targetIds})";
                $db = \ProjectActiveRecord::getDbByClientId($clientId);
                $rows += $db->createCommand($updateCustomSql)->execute();
                $rows += $db->createCommand($updateFieldGroupSql)->execute();
                $rows += $db->createCommand($updateFieldExportSql)->execute();
                self::info("Done, affect rows:{$rows}, client_id:" . $clientId);
            } catch (\Throwable $e) {
                self::info("clientId:" . $clientId . " | Error:" . $e->getMessage() . " | File:" . $e->getFile() . " | Line:" . $e->getLine());
                continue;
            }
        }
    }

    public function actionRollbackOrderProfit($clientId=null, $grey = 0, $greyNum = null, $skipClientId = 0, $flag = 1){    // flag=1是回滚，flag=0是逆向回滚
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey && !is_null($greyNum)) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        foreach ($clientIds as $clientId) {
            if ($clientId < $skipClientId) {
                continue;
            }

            $enableFlag = $requireFlag = $flag == 1 ? 0 : 1;
            $rows = 0;
            try {
                $updateSql = "update tbl_custom_field set `require`={$requireFlag}, enable_flag={$enableFlag} where client_id={$clientId} and ((`type` = 47 and id='order_id') or (`type` = 30 and id='source_type'))";
                $db = \ProjectActiveRecord::getDbByClientId($clientId);
                $rows = $db->createCommand($updateSql)->execute();
                self::info("Done, affect rows:{$rows}, client_id:" . $clientId);
            } catch (\Throwable $e) {
                self::info("clientId:" . $clientId . " | Error:" . $e->getMessage() . " | File:" . $e->getFile() . " | Line:" . $e->getLine());
                continue;
            }
        }
    }


    public function actionTestErpOrderSync($orderNo, $accountId, $platformType)
    {
        // debugphp -d 'xdebug.client_port=9004' /data/codebase/bob/php-crm/protected/yiic-test temporary TestErpOrderSync  --accountId=1657333373021452092 --orderNo=yingchen-********-00002 --platformType=kingdee

        // debugphp -d 'xdebug.client_port=9004' /data/codebase/bob/php-crm/protected/yiic-test temporary TestErpOrderSync  --accountId=**************** --orderNo=CRMPI039230329 --platformType=chanjet

        \common\library\queue_v2\job\ErpSyncOrderJob::run($accountId, $orderNo, $platformType);
//die;
//        $encryptMsg = "mvAQ0gh34CeUYcsCBbh/ztU1pC3TT1Fer6HHzFEkxQ3KfndnbnrCD7dnkTgAijziPGA8okHjJ2BEsLdozsQDejluUG11+a0MlPfDdEeKQhN9VspAkozEGLLIgiL3EMgBdbf/HDbqkPKZfcyhwCecCi5Y81KnXEYCQHyesTOdk+aevJmq4ZnVXnMvEVL02mRuLB+6jGW6GirgD7hdyZs5xwFEHaYAb6Qd3RfPBzPKQeRsIRwNLaWs+258ljA7tIrXdkSxYAd/HsEKMNnGV05oYh80VgnMxRR3nqXJzh++TXnpOgLtb2qNnwlsfLnpKudQaGDLPC3UytcdwKTvhXv0ZOEtf32DlGD6xS8mqXHv3pegVWcyrmC1rC1Lb2PGRVyxwMuR7vfHxybe2cvqNyj6CGwmMyVq3KR9EHPE6sdndlu68r29bKfmRB3DZQSOg2VOqCBPUKTUOiEg25g5mZ9/rKs4fVH8nVDMjmYsZRV45Lj9ByW5tWeeUZ4YKO2fAcVvBmrM18PwRCNZlNfsQFxMi4bW1dv9Cfr6ks3wpcP59YA=";
////        $encryptMsg = "mvAQ0gh34CeUYcsCBbh/zsxeZO9PEN2dPaNLhu9i+A8Vm/A3Ys2s/mG6Idz8SUuNPGA8okHjJ2BEsLdozsQDejluUG11+a0MlPfDdEeKQhN9VspAkozEGLLIgiL3EMgBdbf/HDbqkPKZfcyhwCecCpvSECZZhkaHqdViiUwwmYWevJmq4ZnVXnMvEVL02mRu0zCn2f42ylqoxp0YMK5Fa2NiuXFZ8rD1Zj6aEeR8kdlCKZ5BUKzACu4n4Jwmzc+/pFigEAjtq2kyL1mxhCUai/pJCH7OE7fEXAbLsZ9gPruEwfzF1gByCpDdv6q+WydcHJ6zPlR957F3qthxihHz1H8sKCiCZYGh4xPZ2QcMPMl1Knb7jqZIR268d6xQrxDacHnoZul/5IjQqUofwFzZdsaAd68II0PyTBkyJIvnGVEi328mAnkOnyibEPgDdU1i1gKB58BADgGj/78oa01B1wYlaVEueatF98wP0/WZqjmCjYgDysxTpvSNdF0n38MA5FkAxF2kpdeNIgNQrds8cghvhO2xjL66lyy76Z87Wkc=";
//        $chanjetApiService = new \common\library\erp_service\chanjet\ChanjetApiService();
//        $messageInfo = $chanjetApiService->chanjetDecryptMsg($encryptMsg, $chanjetApiService->getRsa());
//        echo json_encode($messageInfo);
    }

    public function actionFixProductUpdateUser($clientId=null, $grey = 0, $greyNum = null, $skipClientId = 0){
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey && !is_null($greyNum)) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        foreach ($clientIds as $clientId) {
            if ($clientId < $skipClientId) {
                continue;
            }

            try {
                $selectSql = "select product_id, update_user from tbl_product where client_id={$clientId}";
                $selectHistorySql = "select product_id,update_user,create_time from tbl_product_history where client_id={$clientId} and update_user !=0 order by create_time desc";
                $db = \PgActiveRecord::getDbByClientId($clientId);

                $productMap = array_column($db->createCommand($selectSql)->queryAll(), 'update_user', 'product_id');
                $historyMap = $db->createCommand($selectHistorySql)->queryAll();

                $realUpdateUserMap = [];
                $needUpdateUserMap = [];
//                $needUpdate = false;
                foreach($historyMap as $history){
                    if(isset($realUpdateUserMap[$history['product_id']])){
                        continue;
                    }
                    if(!isset($productMap[$history['product_id']])){
                        continue;
                    }
                    $realUpdateUserMap[$history['product_id']] = $history['update_user'];
                    if($history['update_user'] != $productMap[$history['product_id']]){
                        $needUpdateUserMap[$history['product_id']] = $history['update_user'];
                    }
                }
                unset($historyMap, $productMap);

                if(empty($needUpdateUserMap)){
                    self::info("没有需要更改的产品变更者, client_id:".$clientId);
                    continue;
                }

                self::info('client_id:'.$clientId.' | needUpdateUserMap:'.json_encode($needUpdateUserMap));
                $needUpdateUserChunk = array_chunk($needUpdateUserMap, 500, true);
                $rows = 0;
                foreach($needUpdateUserChunk as $batchData){
                    $updateSql = "update tbl_product set update_user=case ";
                    foreach($batchData as $productId => $updateUser){
                        $updateSql .= " when product_id = {$productId} then {$updateUser} ";
                    }
                    $productIds = implode(',', array_keys($batchData));
                    $updateSql .= " else update_user end where client_id = {$clientId} and product_id in ({$productIds})";
                    self::info('client_id:'.$clientId.' | updateSql:'.$updateSql);
//                    $rows += $db->createCommand($updateSql)->execute();
                }

                self::info("Done, affect rows:{$rows}, client_id:" . $clientId);
            } catch (\Throwable $e) {
                self::info("clientId:" . $clientId . " | Error:" . $e->getMessage() . " | File:" . $e->getFile() . " | Line:" . $e->getLine());
                continue;
            }
        }
    }

    // 先执行 debugphp -d 'xdebug.client_port=9004' /data/codebase/bob/php-crm/protected/yiic-test temporary refreshFormula --clientId=14119 --module=2 拿到export_id
    // 再执行 debugphp -d 'xdebug.client_port=9004' /data/codebase/bob/php-crm/protected/yiic-test formulaFields refreshFormulaTask --taskId=刚刚拿到的export_id --userId=xxx 刷新公式字段
    public function actionRefreshFormula($clientId, $module){
        $export = new \common\library\export_v2\Export($clientId);
        $export->scene = \common\library\custom_field\formula_field\FormulaFieldHandler::FORMULA_REFRESH_TASK_SCENE;
        $export->type = $module;
        $export->params = [];
        $export->user_id = PrivilegeService::getInstance($clientId)->getAdminUserId();
        $export->create();

        echo $export->export_id;
    }

    public function actionAddOms0530OrderProductFields($clientId)
    {
        $fields = \common\commands\iteration\oms_v7_4_0\OrderProductFieldCustomField::getOrderProductAppendFields();
        \common\library\custom_field\Helper::addField($clientId, $fields);
    }

    public function actionAddOms0530OrderProductSystemFields()
    {
        $settings = \common\commands\iteration\oms_v7_4_0\OrderProductFieldCustomField::getOrderProductAppendFields();
        \common\library\custom_field\Helper::syncSystemFields(\Constants::TYPE_ORDER, $settings);

    }

    public function actionTestHugeFildDownload($clientId, $taskId){
        $clientObject = \common\library\account\Client::getClient($clientId);
        $user = $clientObject->getMasterUser();
        \User::setLoginUser($user);
        $userId = $user->getUserId();

        $task = new Export($clientId, $taskId);
        $fileId = $task->template_file_id;
        $fileInfo = \UploadFile::findByFileId($fileId);
        $executor = new ExcelParseExecutor($task);
        $job = new ExportJob($userId, $task->export_id, Export::class, $executor);
        QueueService::dispatch($job);
    }

    public function actionDelPrivilegeFieldCache($clientId, $roleId, $functionalId){
        // $functionalId = crm.functional.order; $roleId = 5719
        $pf = new PrivilegeField($clientId);
        $hashKey = $pf->getCacheKey(PrivilegeCache::CACHE_TYPE_OF_ROLE_FIELD);
        $list = $pf->getCache()->hmget($hashKey, $pf->getFunctionRoleCacheKeys($roleId, $functionalId));
//        echo json_encode($list);die;
        $hashFields = array_keys($list);
        $res = $pf->getCache()->hdel($hashKey, $hashFields);
        self::info(json_encode($res));
    }

    // 临时脚本，变更导出中的导出任务状态为导出失败
    public function actionChangeExportStatus2Fail($clientId, $module, $scene){
        if(empty($clientId) || empty($module) || empty($scene)){
            echo "params error";
            return;
        }

        $db = \PgActiveRecord::getDbByClientId($clientId);
        $originStatus = \common\library\export_v2\ExportConstants::EXPORT_STATUS_ORIGIN;
        $newStatus = \common\library\export_v2\ExportConstants::EXPORT_STATUS_FAIL;
        $where = "where client_id={$clientId} and type = {$module} and scene={$scene} and status={$originStatus}";
        $sql = "update tbl_export set status = {$newStatus} {$where}";
        $selectSql = "select client_id,export_id,status,type,scene from tbl_export {$where}";
        echo json_encode($db->createCommand($selectSql)->queryAll());
//        $db->createCommand($sql)->execute();
    }

    public function actionRecoverAllProduct($clientId){
        $db = \ProjectActiveRecord::getDbByClientId($clientId);
        $sql = "select refer_id from tbl_recycle where client_id = {$clientId} and type = 3 group by refer_id";
        $productIds = array_column($db->createCommand($sql)->queryAll(), 'refer_id');
        $chunks = array_chunk($productIds, 100);

        $clientObject = \common\library\account\Client::getClient($clientId);
        $user = $clientObject->getMasterUser();
        User::setLoginUser($user);

        foreach($chunks as $batch){
            \common\library\recycle\API::recover($clientId, $user->getUserId(), Recycle::PRODUCT, $batch);
        }
    }

    public function actionTempFixCustomField(){
        $clientId = 20032;
        $deleteFieldId = *************;
        $restoreFieldId = *************;

//        $clientId = 14119;
//        $restoreFieldId = **********;
//        $deleteFieldId = **********;
        $param = [':ext_info' => json_encode([
            "杭州中织实业有限公司（私账6071）",
            "嘉兴孚步体育用品有限公司",
            "浙江飞乐体育用品有限公司",
            "嘉兴美克斯袜业有限公司",
            "杭州中织实业有限公司"
        ])];
        $restoreSql = "update tbl_custom_field set enable_flag = 1,ext_info = :ext_info where client_id={$clientId} and id='{$restoreFieldId}' and type=22";
        $deleteSql = "update tbl_custom_field set enable_flag=0 where client_id={$clientId} and id='{$deleteFieldId}' and type=22";

        $db = \ProjectActiveRecord::getDbByClientId($clientId);
        $res = $db->createCommand($deleteSql)->execute();
        echo $res;
        $db->createCommand($restoreSql)->execute($param);
        echo $res;
    }

    /** 刷新自增编号，目前只支持订单编号刷新
     * 请保证传入的 $targetNumber 是最大的id
     *    $increaseType: const INCREMENT_ALWAYS = 0;//持续递增
     *    const INCREMENT_RESET_BY_YEAR = 1;//按年递增
     *    const INCREMENT_RESET_BY_MONTH = 2;//按月递增
     *    const INCREMENT_RESET_BY_DAY = 4;//按日递增
     */
    public function actionRefreshIncrNumber($clientId, $module, $increaseType, $targetNumber = 0, $isUpdate=0){
        $clientObject = \common\library\account\Client::getClient($clientId);
        $user = $clientObject->getMasterUser();
        \User::setLoginUser($user);
//        $generate = GenerateService::getInstance($clientId, $module);
//        $redisKey = $generate->getNumberGeneratorKey();
//        var_dump($redisKey);

        $idProducerTypeMap = [
            \Constants::TYPE_PRODUCT => \IDProducer::TYPE_PRODUCT,
            \Constants::TYPE_ORDER => \IDProducer::TYPE_ORDER,
        ];
        if(!isset($idProducerTypeMap[$module])){
            self::info("module type forbidden");
            return;
        }
        $idProducerType = $idProducerTypeMap[$module];

        $numberGenerator = new NumberGenerator($clientId, $module, ['increase_type' => intval($increaseType)]);
        $currentSn = $numberGenerator->getSN();
        self::info("currentSn:".$currentSn);

        if(!$isUpdate){
            return;
        }

//        if($currentSn < $targetNumber){
            $numberGenerator->setSn($targetNumber);
            (new IDProducerGenerator($clientId, $idProducerType))->setId($targetNumber);
            self::info('temporary command refresh Client '.$clientId.'CurrentSn '.$currentSn.' MaxSerialId '.$targetNumber);
//        }else{
//            self::info('temporary command targetNumber less than currentSn, Client '.$clientId.' | CurrentSn '.$currentSn.' | targetId '.$targetNumber);
//        }

    }

    public function actionFixProductEmptyImage($clientId=null, $grey = 0, $greyNum = null, $skipClientId = 0){
            if ($clientId) {
                $clientIds = explode(',', $clientId);
            } elseif ($grey && !is_null($greyNum)) {
                $clientIds = $this->getGreyClientIds($greyNum);
            } else {
                $clientIds = array_column($this->getClientList(), 'client_id');
            }

            foreach ($clientIds as $clientId) {
                if ($clientId < $skipClientId) {
                    continue;
                }

                try {
                    $db = \PgActiveRecord::getDbByClientId($clientId);
                    $sql = "select product_id,images from tbl_product where client_id={$clientId} and enable_flag=1";
                    $products = $db->createCommand($sql)->queryAll();

                    if(empty($products)){
                        continue ;
                    }

                    $updateIds = $params = [];
                    $updateSql = "update tbl_product set images = case ";
                    foreach($products as $k => $product){
                        $images = is_array($product['images']) ? $product['images'] : json_decode($product['images'], true);
                        $update = false;
                        foreach($images as $key => $image){
                            if(empty($image) || empty($image['id']) || empty($image['src'])){
                                unset($images[$key]);
                                $update = true;
                            }
                        }

                        if($update){
                            $updateIds[] = $product['product_id'];
                            $paramKey = ':images_'.$k;
                            $params[$paramKey] = json_encode(array_values($images));
                            $updateSql .= " when product_id = {$product['product_id']} then {$paramKey} ";
                        }

                    }

                    if(empty($updateIds)){
                        self::info("no product images to update clientId:[{$clientId}]");
                        continue;
                    }

                    $productIdStr = implode(',', $updateIds);
                    $updateSql .= " else images end where client_id={$clientId} and product_id in ($productIdStr)";
//                    $updateSql .= " else images end where client_id={$clientId} and product_id=1133867006";
                    $res = 0;
                    $res = $db->createCommand($updateSql)->execute($params);
                    self::info("updateSql:".$updateSql.", images:".json_encode($params));
                    self::info("update product:". $productIdStr . ", res count:".$res);

                } catch (\Throwable $e) {
                    self::info("clientId:" . $clientId . " | Error:" . $e->getMessage() . " | File:" . $e->getFile() . " | Line:" . $e->getLine());
                    continue;
                }
            }
    }

    // 查看订单信息
    public function actionOrderFormulaInfo($clientId, $order_id){
        $clientObject = \common\library\account\Client::getClient($clientId);
        $user = $clientObject->getMasterUser();
        \User::setLoginUser($user);

        $order = new \common\library\invoice\Order($user->getUserId());
        $order->loadById($order_id);

        $order->getFormatter()->setShowListSimplify(true);
        $groups = [1];
        $order->getFormatter()->detailInfoSetting($groups);
        $order->getFormatter()->setShowSystemInfo(0);
        $order->getFormatter()->setShowOperatePrivilege(true);
        $order->getFormatter()->setShowProgressInfo(true);
        $order->getFormatter()->setShowHasReferDownStreamInvoice(true);
        $order->getFormatter()->setShowRelateObjFormulaFields(true);
        $order->getFormatter()->showQuoteFieldFlag(true);

        $data = $order->formatInfo();
        foreach($data['data'][0]['fields'] as $field){
            if($field['field_type'] == 11){
                echo json_encode($field);
            }
        }
    }

    // 查看用户ES数据
    public function actionGetEsData($queryStr){
        // queryStr示例：
        /*
         *
 {
    "index": "product",
    "client": {
        "timeout": 5,
        "connect_timeout": 1
    },
    "body": {
        "_source": false,
        "sort": {
            "_score": {
                "order": "desc"
            },
            "update_time": {
                "order": "desc"
            }
        },
        "query": {
            "bool": {
                "filter": [
                    {
                        "term": {
                            "client_id": "14119"
                        }
                    }
                ],
                "must": [
                    {
                        "match_phrase": {
                            "name.char_split": "hello"
                        }
                    }
                ]
            }
        },
        "size": 9999,
        "from": 0
    },
    "routing": "14119"
}
         */
        $queryParams = json_decode($queryStr, true);
        $params = \Yii::app()->params['elastic_search_v7'];
        $connection = ClientBuilder::fromConfig($params);
        echo json_encode($connection->search($queryParams));
    }

    // 重置Exp环境的product index
    public function actionUpdateProductMapping(){
        $productSearch = new \common\models\es_search\ProductSkuSearch();

        $params = [
            'hosts'=>["es-cn-2r426ze8w000s6gz5.elasticsearch.aliyuncs.com:9200"],
            'BasicAuthentication' => ["elastic","Es2xiaoman"],
            'retries'=>1
        ];
        $params = [
            'hosts'=>["es-cn-7pp2e386b0019e36s.elasticsearch.aliyuncs.com:9200"],
            'BasicAuthentication' => ["elastic","Es2xiaoman-inner"],
            'retries'=>1
        ];
//        $index = 'product_sku_exp';
        $index = 'product_sku';
        $connection = ClientBuilder::fromConfig($params);

        // 设置settings
        $settingParams = [
            'index' => $index,
            'body' => [
                'settings' => $productSearch->setting(),
            ],
        ];

        //不能修改主分片了
        unset($settingParams['body']['settings']['number_of_shards']);
        //备份分片可以修改，但不能通过这种方式修改
        unset($settingParams['body']['settings']['number_of_replicas']);


        $connection->indices()->close(['index' => $index]);
        $ret1 = $connection->indices()->putSettings($settingParams);
        $connection->indices()->open(['index' => $index]);

        self::info('ret1:'.json_encode($ret1));
        echo 'ret1:'.json_encode($ret1).PHP_EOL;

        // 设置mapping
        $mappingParams = [
            'index' => $index,
            'body' => $productSearch->mapping(),
        ];

        $ret2 = $connection->indices()->putMapping($mappingParams);
        self::info('ret2:'.json_encode($ret2));
        echo 'ret2:'.json_encode($ret2).PHP_EOL;
    }

    // 临时脚本，修正clientId=25543订单中的计入业绩时间数据
    public function actionFixOrderStatisticTime(){
        $clientId = 25543;
        $targetFieldId = 6719219040080;
//        $clientId = 14119;
//        $targetFieldId = 3420234292;
        $sql = "SELECT archive_type,source_type,external_field_data,order_id FROM tbl_order WHERE 
external_field_data ->> '{$targetFieldId}' = '无' and enable_flag=1 and client_id={$clientId}";
        $db = \PgActiveRecord::getDbByClientId($clientId);
        $wrongData = $db->createCommand($sql)->queryAll();

        $updateSql = "update tbl_order set external_field_data = case ";
        $updateOrderIds = array_column($wrongData, 'order_id');
        $params = [];
        foreach($wrongData as $k => $datum){
            $externalFieldData = json_decode($datum['external_field_data'], true);
            unset($externalFieldData[$targetFieldId]);
            $key = ":ext_field_".$k;
            $params[$key] = json_encode($externalFieldData);
            $updateSql .= " when order_id = {$datum['order_id']} then {$key} ";
        }

        $orderIdStr = implode(',', $updateOrderIds);
        $result = 0;
        $updateSql .= " else external_field_data end where client_id={$clientId} and order_id in ($orderIdStr)";
        self::info('update sql:'.$updateSql);
//        $result = $db->createCommand($updateSql)->execute($params);
        self::info('update sql:'.json_encode($params));
        self::info('affect rows:'.$result);
    }

    public function actionUpdateProfitFormulaField($client_id){
        $sql = "delete from tbl_custom_field where client_id = {$client_id} and type = 52 and id in ('finish_outbound_date','last_outbound_date','first_outbound_date','defined_1_rmb','defined_2_rmb','defined_3_rmb','addition_cost_amount_rmb')";
        $sql2 = "delete from tbl_field_group where client_id = {$client_id} and type = 52 and id in ('finish_outbound_date','last_outbound_date','first_outbound_date','defined_1_rmb','defined_2_rmb','defined_3_rmb','addition_cost_amount_rmb')";

        $db = \ProjectActiveRecord::getDbByClientId($client_id);
        $deleteRows = 0;
        $deleteRows = $db->createCommand($sql)->execute();
        $deleteRows += $db->createCommand($sql2)->execute();

        self::info("delete rows:".$deleteRows." | client_id:".$client_id);
    }

    public function actionReleaseAlibabaProductSyncMutex($clientId, $storeId){
        $syncSetting = new AlibabaProductSyncSetting($clientId, $storeId);
        $store = $syncSetting->getStore();
        $clientObject = \common\library\account\Client::getClient($clientId);
        $user = $clientObject->getMasterUser();
        \User::setLoginUser($user);


        $failureCollector = new AlibabaProductFailureCollector($clientId);
        $processor = new \common\library\alibaba\product\AlibabaProductSyncProcessor($clientId, $store, $failureCollector);
        $processor->releaseMutex();

        $syncSetting = new AlibabaProductSyncSetting($clientId, $storeId);
        $syncSetting->sync_status = AlibabaProductSyncSetting::SYNC_STATUS_DONE;
        $syncSetting->save();
    }

    // 统计一个月内产品导入失败原因到csv
    public function actionProductImportFailReason($clientId=null, $grey = 0, $greyNum = null, $skipClientId = 0){
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey && !is_null($greyNum)) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        $failStatus = \common\library\import\ImportConstants::IMPORT_STATUS_FAIL;
        $date = date('Y-m-d H:i:s', time() - 6*30*86400);
        $csvPath = '/tmp/zbp_product_import_fail_reason.csv';
        file_put_contents($csvPath, "clientId,失败原因,失败产品数,失败任务数,产品成功率,任务成功率\r\n");
        $allCsvPath = '/tmp/zbp_product_import_all_fail_reason.csv';
        file_put_contents($allCsvPath, "失败原因,失败产品数,失败任务数,涉及客户数,占据比率\r\n");
        $allTaskCount = $allRowCount = $allFailTaskCount = $allFailRowCount = 0;
        $allReasonMap = [];
        foreach ($clientIds as $clientId) {
            if ($clientId < $skipClientId) {
                continue;
            }

            try {
                $db = \PgActiveRecord::getDbByClientId($clientId);
                $sql = "select result_file_id,result_line as  fail_reason,total_count,fail_count from tbl_import where client_id={$clientId} and status = {$failStatus} and type = 1 and enable_flag=1 and create_time >= '{$date}'";
                $countSql = "select count(*) as task_count, sum(total_count) as row_count from tbl_import where client_id={$clientId} and type = 1 and enable_flag=1 and create_time >= '{$date}'";
                $data = $db->createCommand($sql)->queryAll();
                $countData = $db->createCommand($countSql)->queryRow();
                $allRowCount += $countData['row_count'];
                $allTaskCount += $countData['task_count'];
                if(empty($data)){
                    continue ;
                }

                // 查询失败文件
                $fileIds = array_filter(array_column($data, 'result_file_id'));
                $filePathList = [];
                if($fileIds){
                    $clientObject = \common\library\account\Client::getClient($clientId);
                    $user = $clientObject->getMasterUser();
                    \User::setLoginUser($user);
                    $fileInfos = \common\library\file\Helper::fileUrlMap($fileIds);

                    // 下载这些失败文件
                    $taskPool = new IOTaskPool(20);
                    $topDir = '/tmp/zbp';
                    if(!is_dir($topDir)){
                        mkdir($topDir);
                    }
                    $downloadDir = $topDir.'/product_import_fail_reason_'.$clientId;
                    if(!is_dir($downloadDir)){
                        mkdir($downloadDir);
                    }
                    foreach($fileInfos as $fileInfo){
                        $downloadTask = new \common\library\io_handler\downloader\DownloadTask($fileInfo['file_path'], $downloadDir);
                        $downloadTask->setTaskId($fileInfo['file_id']);
                        $taskPool->addTask($downloadTask);
                    }
                    $taskPool->setAsync(false);
                    $taskPool->batchRun();
                    foreach($taskPool->getTasks() as $downloadTask){
                        $filePathList[$downloadTask->getTaskId()] = $downloadTask->getResult();
                    }
                }

                $reasonMap = [];    // 本client相同错误原因的个数，行维度和任务维度
                $totalFailTaskNum = count($data);   // 本client的失败任务数
                $totalFailRowNum = 0;               // 本client的失败行数
                $allFailTaskCount += $totalFailTaskNum;
                $taskRate = $countData['task_count'] == 0 ? 1 : (1 - $totalFailTaskNum / $countData['task_count']);    // 任务成功率
                foreach($data as $datum){
                    $taskRowNum = $datum['total_count'] == 0 ? 1 : $datum['total_count'];
                    $failRowNum = $datum['fail_count'] == 0 ? $taskRowNum : $datum['fail_count'];
                    $failReasons = [];
                    if(isset($filePathList[$datum['result_file_id']])){
                        $fp = $filePathList[$datum['result_file_id']];
                        if(!is_file($fp)){
                            continue;
                        }
                        $fp = fopen($fp, 'r');
                        do{     // 遇到空行则重复查
                            $errorRow = fgetcsv($fp);
                            $failReasons[] = iconv("GB2312//IGNORE", "UTF-8", $errorRow[5] ?? '');
                            $end = feof($fp);
                        }while(!$end);
                        fclose($fp);
                        array_shift($failReasons);
                        $failReasons = array_filter($failReasons);
                        $failRowNum = count($failReasons);
                    }else{
                        $resultLine = is_array($datum['fail_reason']) ? $datum['fail_reason'] : json_decode($datum['fail_reason'], true);
                        $resultLine = $resultLine['error'] ?? '';
                        $failReasons[] = explode(';', $resultLine)[0] ?? '';
                    }

                    $allFailRowCount += $failRowNum;
                    $totalFailRowNum += $failRowNum;

                    $taskReaseMap = [];     // 本task下的map
                    foreach($failReasons as $error){
                        if(empty($error)){
                            continue;
                        }
                        if(!isset($reasonMap[$error])){
                            $reasonMap[$error] = [
                                'row_fail_num' => 0,
                                'task_fail_num' => 0
                            ];
                        }

                        if(!isset($taskReaseMap[$error])){
                            $reasonMap[$error]['task_fail_num']++;
                        }
                        if($datum['fail_count']==0){
                            $reasonMap[$error]['row_fail_num']+=$failRowNum;
                        }else{
                            $reasonMap[$error]['row_fail_num']++;
                        }

                        $taskReaseMap[$error] = true;
                    }
                }
                $rowRate = $countData['row_count'] == 0 ? 1 : (1 - $totalFailRowNum / $countData['row_count']);  // 行成功率
                \PgActiveRecord::releaseDbByClientId($clientId);
                self::info("clientId:".$clientId." done. Task rate:".$taskRate . ". Row Rate:".$rowRate);
            } catch (\Throwable $e) {
                self::info("clientId:" . $clientId . " | Error:" . $e->getMessage() . " | File:" . $e->getFile() . " | Line:" . $e->getLine());
                continue;
            } finally {
                if(isset($downloadDir) && is_dir($downloadDir)){
                    exec("rm -rf ".$downloadDir);
                }
            }

            // 写入csv
            $input = [];
            foreach($reasonMap as $error => $numData){
                $input[] = "{$clientId},".'"'.$error.'",'.$numData['row_fail_num'].','.$numData['task_fail_num'].',,';

                if(!isset($allReasonMap[$error])){
                    $allReasonMap[$error] = [
                        'row_fail_num' => 0,
                        'task_fail_num' => 0,
                        'client_num' => 0
                    ];
                }
                $allReasonMap[$error]['row_fail_num'] += $numData['row_fail_num'];
                $allReasonMap[$error]['task_fail_num'] += $numData['task_fail_num'];
                $allReasonMap[$error]['client_num'] += 1;
            }
            file_put_contents($csvPath, implode("\r\n", $input)."\r\n", FILE_APPEND);
            file_put_contents($csvPath, ' , , , ,'."{$rowRate},{$taskRate}\r\n\r\n",FILE_APPEND);
        }

        $allReasonChunk = array_chunk($allReasonMap, 1000, true);
        $denominator = array_sum(array_column($allReasonMap, 'row_fail_num'));
        unset($allReasonMap);
        $allInput = [];
        foreach($allReasonChunk as $chunk){
            foreach($chunk as $error => $numData){
                $failRate = $numData['row_fail_num'] == 0 ? '-' : round(($numData['row_fail_num'] / $denominator)*100, 2).'%';
                $allInput[] = '"'.$error.'",'.$numData['row_fail_num'].','.$numData['task_fail_num'].','.$numData['client_num'].','. $failRate;
            }
            file_put_contents($allCsvPath, implode("\r\n", $allInput)."\r\n", FILE_APPEND);
        }

        // csv上传阿里云
        $uploadClient = \Yii::app()->params['env'] == 'test' ? 14119 : 14833;
        $clientObject = \common\library\account\Client::getClient($uploadClient);
        $user = $clientObject->getMasterUser();
        \User::setLoginUser($user);
        $upload = \UploadService::uploadRealFile($csvPath, 'zbp_product_import_fail_reason.csv', \UploadService::getFileKey($csvPath));
        $url = $upload->getFileUrl();
        $fileId = $upload->getFileId();
        $upload = \UploadService::uploadRealFile($allCsvPath, 'zbp_product_import_all_fail_reason.csv', \UploadService::getFileKey($allCsvPath));
        $allUrl = $upload->getFileUrl();
        $allFileId = $upload->getFileId();

        $allTaskRate = min(1 - $allFailTaskCount/$allTaskCount, 1);
        $allRowRate = min(1- $allFailRowCount/$allRowCount, 1);
        self::info("Task rate:".$allTaskRate.", row rate:".$allRowRate.', csv url:'.$url. ' , all csv url:'.$allUrl);
        self::info("file_id:".$fileId.", all file_id:".$allFileId);
    }

    // 统计产品导入到csv
    public function actionStatisticProductImport($clientId=null, $grey = 0, $greyNum = null, $skipClientId = 0,$date=null){
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey && !is_null($greyNum)) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        $failStatus = \common\library\import\ImportConstants::IMPORT_STATUS_FAIL;
        $failClientCount = $clientCount = 0;
        $allTaskCount = $failClientTaskCount = 0;
        $allClientFailCount = $failClientFailCount = 0;
        $clientArr = $failClientArr = [];
        foreach ($clientIds as $clientId) {
            if ($clientId < $skipClientId) {
                continue;
            }

            try {
                $db = \PgActiveRecord::getDbByClientId($clientId);
                $countSql = "select count(*) as task_count, sum(case when status=3 then 0 else 1 end)  as fail_task_count from tbl_import where client_id={$clientId} and type = 1 and enable_flag=1";
                if($date){
                    $countSql .= " and create_time >= '{$date}'";
                }
                $countData = $db->createCommand($countSql)->queryRow();
                if(empty($countData) || empty($countData['task_count'])){   // 说明此人不用产品导入
                    continue ;
                }

                $clientCount++;
                $allTaskCount+=$countData['task_count'];
                $allClientFailCount+=$countData['fail_task_count'];
                $clientArr[] = $clientId;
                if(!empty($countData['fail_task_count'])){
                    $failClientCount++;
                    $failClientArr[] = $clientId;
                    $failClientTaskCount+=$countData['task_count'];
                    $failClientFailCount += $countData['fail_task_count'];
                }

                \PgActiveRecord::releaseDbByClientId($clientId);
                self::info("clientId:".$clientId." done. clientCount:{$clientCount}, allTaskCount:{$allTaskCount}, allClientFailCount:{$allClientFailCount}, failClientCount:{$failClientCount}, failClientTaskCount:{$failClientTaskCount}, failClientFailCount:,{$failClientFailCount}");
            } catch (\Throwable $e) {
                self::info("clientId:" . $clientId . " | Error:" . $e->getMessage() . " | File:" . $e->getFile() . " | Line:" . $e->getLine());
                continue;
            }
        }

        $path = '/tmp/zbp_fail_import_statistic.log';
        file_put_contents($path, "All use import client:". implode(",", $clientArr)."\r\nFail import client:".implode(",", $failClientArr));

        // csv上传阿里云
        $uploadClient = \Yii::app()->params['env'] == 'test' ? 14119 : 14833;
        $clientObject = \common\library\account\Client::getClient($uploadClient);
        $user = $clientObject->getMasterUser();
        \User::setLoginUser($user);
        $upload = \UploadService::uploadRealFile($path, 'zbp_fail_import_statistic.log', \UploadService::getFileKey($path));
        $url = $upload->getFileUrl();
        $fileId = $upload->getFileId();

        self::info("clientCount:{$clientCount}, allTaskCount:{$allTaskCount}, allClientFailCount:{$allClientFailCount}, failClientCount:{$failClientCount}, failClientTaskCount:{$failClientTaskCount}, failClientFailCount:,{$failClientFailCount}");
        self::info("path url:".$url." | fileId:".$fileId);
    }

    // 按更新时间重新同步分组为空的阿里产品
    // debugphp -d 'xdebug.client_port=9004' /data/codebase/bob/php-crm/protected/yiic-test temporary SyncEmptyGroupAliProduct --clientId=14367  --startTime=2022-01-01 --endTime=2023-09-01
    public function actionSyncEmptyGroupAliProduct($startTime, $endTime, $clientId=null, $skipClientId = 0){
        $sql = "select store_id,client_id, sync_customer_flag, sync_product_flag from tbl_alibaba_store where enable_flag=1";
        if ($clientId) {
            $sql .= " and client_id in ({$clientId})";
        }

        $storeList = Yii::app()->db->createCommand($sql)->queryAll();
        $clientIds = array_column($storeList, 'client_id');

        // 汇总每个client的storeId
        $storeMap = [];
        foreach($storeList as $store){
            if(!isset($storeMap[$store['client_id']])){
                $storeMap[$store['client_id']] = [];
            }
            $storeMap[$store['client_id']][] = $store['store_id'];
        }

        foreach ($clientIds as $clientId) {
            if ($clientId < $skipClientId) {
                continue;
            }

            try {
                $db = \PgActiveRecord::getDbByClientId($clientId);
                $clientObject = \common\library\account\Client::getClient($clientId);
                $user = $clientObject->getMasterUser();
                \User::setLoginUser($user);

                foreach($storeMap[$clientId] as $storeId){
                    $sql = "select platform_product_id,third_product_id,cipher_product_id from tbl_platform_product where client_id={$clientId} and third_store_id = {$storeId} and enable_flag=1 and sync_time >= '{$startTime}' and sync_time <= '{$endTime}' and groups='{}'";   // 查询指定时间范围内同步的，且groups为{}的平台产品的cipherId

                    $platformProducts = $db->createCommand($sql)->queryAll();
                    if(empty($platformProducts)){
                        continue;
                    }
                    self::info("client id:[{$clientId}], store id:[{$storeId}], empty group platform product count:".count($platformProducts));
                    $syncSetting = new AlibabaProductSyncSetting($clientId, $storeId);
                    $storeObj = $syncSetting->getStore();
                    $rows = 0;
/*
                    foreach($platformProducts as $platformProduct){
                        // 查询阿里接口
                        $cipherId = $platformProduct['cipher_product_id'];
                        $productService = new AlibabaProduct(AlibabaProductSyncHelper::getAccessTokenFromStore($storeObj), $cipherId);
                        $aliProduct = $productService->getInfo();

                        if (empty($aliProduct['product'])) {
                            continue;
                        }
                        $aliProduct = $aliProduct['product'];
                        $aliGroupId = $aliProduct['group_id'] ?? 0;
                        if(!$aliGroupId){
                            self::info("client id:[{$clientId}], third_product_id: [{$platformProduct['third_product_id']}], ali product group id empty,continue");
                            continue;
                        }
                        $groups = AlibabaProductSyncHelper::getGroupsById($storeObj, $aliGroupId);
                        if(empty($groups)){
                            self::info("client id:[{$clientId}], third_product_id: [{$platformProduct['third_product_id']}], ali product group not found, maybe limit");
                            continue;
                        }

                        $key = ':group_'.$platformProduct['platform_product_id'];
                        $param = [];
                        $param[$key] = json_encode($groups);
                        $updateSql = "update tbl_platform_product set groups={$key} where client_id={$clientId} and platform_product_id={$platformProduct['platform_product_id']}";

                        $rows += $db->createCommand($updateSql)->execute($param);
                    }
*/
                    self::info("client id:[{$clientId}], store id:[{$storeId}], update product count:".$rows);
                }

                \PgActiveRecord::releaseDbByClientId($clientId);
            } catch (\Throwable $e) {
                self::info("clientId:" . $clientId . " | Error:" . $e->getMessage() . " | File:" . $e->getFile() . " | Line:" . $e->getLine());
                continue;
            }
        }
    }

    // 按更新时间重新同步分组为空的阿里产品
    // debugphp -d 'xdebug.client_port=9004' /data/codebase/bob/php-crm/protected/yiic-test temporary SyncEmptyGroupAliProduct --clientId=14367  --startTime=2022-01-01 --endTime=2023-09-01
    public function actionSyncEmptyGroupAliProduct2($startTime, $endTime, $clientId=null, $skipClientId = 0){
        $sql = "select store_id,client_id, sync_customer_flag, sync_product_flag from tbl_alibaba_store where enable_flag=1";
        if ($clientId) {
            $sql .= " and client_id in ({$clientId})";
        }

        $storeList = Yii::app()->db->createCommand($sql)->queryAll();
        $clientIds = array_column($storeList, 'client_id');

        // 汇总每个client的storeId
        $storeMap = [];
        foreach($storeList as $store){
            if(!isset($storeMap[$store['client_id']])){
                $storeMap[$store['client_id']] = [];
            }
            $storeMap[$store['client_id']][] = $store['store_id'];
        }

        foreach ($clientIds as $clientId) {
            if ($clientId < $skipClientId) {
                continue;
            }

            try {
                $db = \PgActiveRecord::getDbByClientId($clientId);
                $clientObject = \common\library\account\Client::getClient($clientId);
                $user = $clientObject->getMasterUser();
                \User::setLoginUser($user);

                foreach($storeMap[$clientId] as $storeId){
                    $sql = "select platform_product_id,third_product_id,cipher_product_id from tbl_platform_product where client_id={$clientId} and third_store_id = {$storeId} and enable_flag=1 and sync_time >= '{$startTime}' and sync_time <= '{$endTime}' and groups='{}'";   // 查询指定时间范围内同步的，且groups为{}的平台产品的cipherId

                    $platformProducts = $db->createCommand($sql)->queryAll();
                    if(empty($platformProducts)){
                        continue;
                    }
                    self::info("client id:[{$clientId}], store id:[{$storeId}], empty group platform product count:".count($platformProducts));

                    $localThirdProducts = array_column($platformProducts, null, 'third_product_id');
                    $syncSetting = new AlibabaProductSyncSetting($clientId, $storeId);
                    $storeObj = $syncSetting->getStore();
                    $scope = new \common\library\alibaba\product\scope\AllScope($clientId, $storeObj);
                    $scope->setStartTime($startTime);
                    $scope->setEndTime($endTime);
                    
                    $targetGroupsMap = $productGroupMap = [];
                    $remoteUpdateCount = 0;
                    foreach ($scope->iteratorOfProduct() as $data) {
                        if (empty($data)) {
                            continue;
                        }
                        $remoteUpdateCount += count($data);
                        foreach($data as $datum){
                            if(!isset($localThirdProducts[$datum['id']])){
                                continue;
                            }

                            if(empty($data['group_id'])){   // 说明该产品没有设置分组
                                continue;
                            }
                            $targetGroupsMap[$data['group_id']] = true;
                            $productGroupMap[$data['id']] = $data['group_id'];
                        }
                    }
                    $rows = 0;

                    // 先查询所有的分组信息
                    foreach($targetGroupsMap as $groupId => $_){
                        $targetGroupsMap[$groupId] = AlibabaProductSyncHelper::getGroupsById($storeObj, $groupId);
                    }

                    foreach($productGroupMap as $thirdProductId => $thirdProductGroupId){
                        $groups = $targetGroupsMap[$thirdProductGroupId] ?? null;

                        if(empty($groups)){
                            self::info('client id:[{$clientId}], store id:[{$storeId}], Can not find group info by group id:'.$thirdProductGroupId);
                            continue;
                        }
                        $key = ':group_'.$thirdProductId;
                        $param = [];
                        $param[$key] = json_encode($groups);
                        $updateSql = "update tbl_platform_product set groups={$key} where client_id={$clientId} and third_product_id={$thirdProductId}";

                        $rows += $db->createCommand($updateSql)->execute($param);
                    }

                    self::info("client id:[{$clientId}], store id:[{$storeId}], update product count:".$rows);
                }

                \PgActiveRecord::releaseDbByClientId($clientId);
            } catch (\Throwable $e) {
                self::info("clientId:" . $clientId . " | Error:" . $e->getMessage() . " | File:" . $e->getFile() . " | Line:" . $e->getLine());
                continue;
            }
        }
    }

    // 修复阿里产品同步导致缺失分组的本地产品
    public function actionFixAlibabaSyncLocalProductGroup($clientId=null, $grey = 0, $greyNum = null, $skipClientId = 0){
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey && !is_null($greyNum)) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $sql = "select store_id,client_id, sync_customer_flag, sync_product_flag from tbl_alibaba_store where enable_flag=1";

            $storeList = Yii::app()->db->createCommand($sql)->queryAll();
            $clientIds = array_column($storeList, 'client_id');
        }

        foreach ($clientIds as $clientId) {
            if ($clientId < $skipClientId) {
                continue;
            }

            try {
                $db = \PgActiveRecord::getDbByClientId($clientId);

                // 查询所有没有group的产品对应的阿里产品
                $sql = "select r.product_id,r.platform_product_id,t.groups from tbl_product p inner join tbl_platform_product_relation r on p.product_id = r.product_id inner join tbl_platform_product t on t.platform_product_id = r.platform_product_id where r.client_id={$clientId} and p.client_id={$clientId} and r.enable_flag=1 and p.enable_flag=1 and p.group_id=0 and t.groups != '{}' group by r.product_id, r.platform_product_id,t.groups";
                $data = $db->createCommand($sql)->queryAll();

                if(empty($data)){
                    continue ;
                }

                $platformProductIds = array_column($data, 'platform_product_id');

                // 创建 & 获取分组
                $api = new \common\library\platform_product\PlatformProductAPI($clientId);
                $filter = $api->buildFilter(['platform_product_id' => $platformProductIds]);
                $filter->select(['platform_product_id', 'groups', 'enable_flag']);
                $batch = $filter->find();
                $batch->getFormatter()->displayFields(['platform_product_id', 'groups', 'enable_flag']);
                //$batch->getFormatter()->displayExistSkuAttrTree(true);
                $products = $batch->getAttributes();
                $groupRes = $api->writeGroupsToLocal($products);

                // 根据group名找到group_id
                $groupNamesMap = $updateProductId =[];
                $updateSql = "update tbl_product set group_id = case ";
                foreach($data as $datum){
                    $groupInfos = is_array($datum['groups']) ? $datum['groups'] : json_decode($datum['groups'], true);
                    $groupNameArr = [];
                    foreach($groupInfos as $groupInfo){
                        $groupNameArr[] = preg_replace('/[\<\>]/', '', $groupInfo['group_name'] ?? '');
                    }

                    $key = implode('-', $groupNameArr);
                    if(!isset($groupNamesMap[$key])){
                        $groups = array_column(array_values(\common\library\group\Helper::getGroupByLayerNames($clientId, \Constants::TYPE_PRODUCT, $groupNameArr)), null, 'id');
                        $targetId = 0;
                        $prefix = "0-";
                        if(!empty($groups)){
                            foreach($groupNameArr as $name){
                                foreach($groups as $groupId => $group){
                                    if($name == $group['name'] && $prefix == $group['prefix']){
                                        $prefix .= $groupId . '-';
                                        $targetId = $groupId;
                                    }
                                }
                            }
                            if(empty($groups[$targetId]) || $groups[$targetId]['name'] != $groupNameArr[count($groupNameArr)-1]){
                                $targetId = 0;
                            }
                        }
                        $groupNamesMap[$key] = $targetId;
                    }

                    $targetId = $groupNamesMap[$key];
                    $updateSql .= " when product_id = {$datum['product_id']} then {$targetId} ";
                    $updateProductId[] = $datum['product_id'];
                }

                if(!empty($updateProductId)){
                    $updateProductIdStr = implode(',', $updateProductId);
                    $updateSql .= " end where client_id={$clientId} and product_id in ({$updateProductIdStr}) and group_id = 0 and enable_flag=1";
                    $affectRows = $db->createCommand($updateSql)->execute();
                    self::info("updateSql : ". $updateSql);
                    self::info("clientId:[{$clientId}], affectRows : ". $affectRows);
                }else{
                    self::info("clientId:[{$clientId}], no product group to update.");
                }

                \PgActiveRecord::releaseDbByClientId($clientId);

            } catch (\Throwable $e) {
                self::info("clientId:" . $clientId . " | Error:" . $e->getMessage() . " | File:" . $e->getFile() . " | Line:" . $e->getLine());
                continue;
            }
        }
    }

    // 修复回款单绩效归属人与关联的订单同步 debugphp -d 'xdebug.client_port=9004' /data/codebase/bob/php-crm/protected/yiic-test temporary FixCashCollectionUser --clientId=xxx
    public function actionFixCashCollectionUser($clientId=null, $grey = 0, $greyNum = null, $skipClientId = 0){
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey && !is_null($greyNum)) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        foreach ($clientIds as $clientId) {
            if ($clientId < $skipClientId) {
                continue;
            }

            try {
                $db = \PgActiveRecord::getDbByClientId($clientId);

                // 查询所有没有group的产品对应的阿里产品
                $sql = "select c.cash_collection_id, c.order_id,c.users cash_users,o.users order_users from tbl_cash_collection c left join tbl_order o on c.order_id=o.order_id where o.client_id={$clientId} and c.client_id={$clientId} and c.enable_flag=1 and c.users != o.users";
                $cashInfos = $db->createCommand($sql)->queryAll();

                if(empty($cashInfos)){
                    continue ;
                }

                $updateSql = "update tbl_cash_collection set users = case ";
                $updateCashIds = $params = [];
                foreach($cashInfos as $k => $cashInfo){
                    // 如果回款单的users和订单的users不一致
                    $orderUsers = !is_array($cashInfo['order_users']) ? json_decode($cashInfo['order_users'], true) : $cashInfo['order_users'];
                    $cashUsers = !is_array($cashInfo['cash_users']) ? json_decode($cashInfo['cash_users'], true) : $cashInfo['cash_users'];
                    $orderUsers = array_column($orderUsers, 'user_id');
                    $cashUsers = array_column($cashUsers, 'user_id');
                    sort($orderUsers);
                    sort($cashUsers);
                    if($orderUsers == $cashUsers){
                        continue;
                    }

                    $key = ":users_".$k;
                    $params[$key] = $cashInfo['order_users'];
                    $updateCashIds[] = $cashInfo['cash_collection_id'];
                    $updateSql .= " when cash_collection_id = {$cashInfo['cash_collection_id']} then {$key} ";
                }

                $rows = 0;
                if(!empty($updateCashIds)){
                    $idStr = implode(',', $updateCashIds);
                    $updateSql .= " else users end where client_id={$clientId} and cash_collection_id in ({$idStr})";
                    self::info($updateSql);
                    $rows = $db->createCommand($updateSql)->execute($params);
                }

                \PgActiveRecord::releaseDbByClientId($clientId);
                self::info("clientId: [{$clientId}], update rows:".$rows);
            } catch (\Throwable $e) {
                self::info("clientId:" . $clientId . " | Error:" . $e->getMessage() . " | File:" . $e->getFile() . " | Line:" . $e->getLine());
                continue;
            }
        }
    }

    // 修复阿里订单同步生成回款单的创建人与操作历史一致
    // debugphp -d 'xdebug.client_port=9004' /data/codebase/bob/php-crm/protected/yiic-test temporary FixCashCollectionCreateUser --clientId=14367
    public function actionFixCashCollectionCreateUser($clientId=null, $grey = 0, $greyNum = null, $skipClientId = 0){
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey && !is_null($greyNum)) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        foreach ($clientIds as $clientId) {
            if ($clientId < $skipClientId) {
                continue;
            }

            try {
                $db = \PgActiveRecord::getDbByClientId($clientId);

                // 查询阿里同步生成的回款单
                $sql = "select cash_collection_id, create_user_id from tbl_cash_collection where client_id={$clientId} and enable_flag=1 and create_type=3";
                $cashInfos = $db->createCommand($sql)->queryAll();

                if(empty($cashInfos)){
                    continue ;
                }

//                $cashIds = array_column($cashInfos, 'cash_collection_id');
                $chunkInfos = array_chunk($cashInfos, 5000);
                $rows = 0;
                foreach($chunkInfos as $batchCash){
                    $cashIds = array_column($batchCash, 'cash_collection_id');
                    $cashIdsStr = implode(',', $cashIds);
                    $historySql = "select cash_collection_id,update_user,create_time from tbl_cash_collection_history where client_id={$clientId} and type=1 and cash_collection_id in ({$cashIdsStr}) order by create_time asc";
                    $historyInfos = $db->createCommand($historySql)->queryAll();
                    $batchCash = array_column($batchCash, null, 'cash_collection_id');

                    $updateSql = "update tbl_cash_collection set create_user_id = case ";
                    $updateCashIds = [];
                    foreach($historyInfos as $historyInfo){
                        if(!isset($batchCash[$historyInfo['cash_collection_id']])){
                            continue;
                        }
                        if($historyInfo['update_user'] != $batchCash[$historyInfo['cash_collection_id']]['create_user_id']){
                            $updateSql .= " when cash_collection_id = {$historyInfo['cash_collection_id']} then {$historyInfo['update_user']} ";
                            $updateCashIds[] =$historyInfo['cash_collection_id'];
                        }
                    }
                    if(!empty($updateCashIds)){
                        $updateCashIdsStr = implode(',', $updateCashIds);
                        $updateSql .= " else create_user_id end where client_id={$clientId} and cash_collection_id in ({$updateCashIdsStr}) and create_type=3 and enable_flag=1";
                        self::info('Update Sql:'.$updateSql);
//                        $rows += $db->createCommand($updateSql)->execute();
                    }
                }

                \PgActiveRecord::releaseDbByClientId($clientId);
                self::info("clientId: [{$clientId}], update rows:".$rows);
            } catch (\Throwable $e) {
                self::info("clientId:" . $clientId . " | Error:" . $e->getMessage() . " | File:" . $e->getFile() . " | Line:" . $e->getLine());
                continue;
            }
        }
    }

    //oms模块字段检查及修复
    public function actionRepairCustomField($clientId = 0, $grey = 0, $greyNum = 0)
    {
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey) {
            $greyNum = explode(",", $greyNum);
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        $db = \Yii::app()->db;
        $types = [
            \Constants::TYPE_PRODUCT,
            \Constants::TYPE_ORDER,
            \Constants::TYPE_QUOTATION,
            \Constants::TYPE_CASH_COLLECTION,
            \Constants::TYPE_PURCHASE_ORDER,
            \Constants::TYPE_SUPPLIER,
            \Constants::TYPE_PRODUCT_SKU,
            \Constants::TYPE_PLATFORM_PRODUCT_SKU,
            \Constants::TYPE_PURCHASE_INBOUND_INVOICE,
            \Constants::TYPE_OTHER_INBOUND_INVOICE,
            \Constants::TYPE_SALE_OUTBOUND_INVOICE,
            \Constants::TYPE_OTHER_OUTBOUND_INVOICE,
            \Constants::TYPE_WAREHOUSE,
            \Constants::TYPE_PURCHASE_RETURN_INVOICE,
            \Constants::TYPE_INBOUND_INVOICE,
            \Constants::TYPE_OUTBOUND_INVOICE,
            \Constants::TYPE_RETURN_INVOICE,
            \Constants::TYPE_INITIAL_INVENTORY_INBOUND_INVOICE,
            \Constants::TYPE_PRODUCT_TRANSFER_PURCHASE,
            \Constants::TYPE_PRODUCT_TRANSFER_INBOUND,
            \Constants::TYPE_PRODUCT_TRANSFER_OUTBOUND,
            \Constants::TYPE_PRODUCT_TRANSFER,
            \Constants::TYPE_PRODUCT_TRANSFER_OTHER,
            \Constants::TYPE_PAYMENT_INVOICE,
            \Constants::TYPE_PAYMENT_CAPITAL_ACCOUNT,
            \Constants::TYPE_CASH_COLLECTION_INVOICE,
            \Constants::TYPE_COST_INVOICE,
            \Constants::TYPE_ORDER_PROFIT,
        ];
        $typeStr = implode(',', $types);
        $systemFieldSql = "select * from tbl_system_field WHERE type in ({$typeStr})";
        $systemFieldResult = $db->createCommand($systemFieldSql)->queryAll(true);

        //系统字段信息
        $systemField = [];
        $systemFieldData = [];
        foreach($systemFieldResult as $item){
            $systemField[$item['type']][] = $item['id'];
            $systemFieldData[$item['type']][$item['id']] = $item;
        }

        $data = [];

        foreach ($clientIds as $clientId) {
            echo "开始执行client_id:" . $clientId . PHP_EOL;
            try {
                $adminUserId = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();
                if (!$adminUserId) {
                    \LogUtil::info("clientId:$clientId adminUser not exist!");
                    continue;
                }
                \User::setLoginUserById($adminUserId);

                //用户字段信息
                $customDb = \ProjectActiveRecord::getDbByClientId($clientId);
                $customFieldSql = "select id,type from tbl_custom_field WHERE client_id = {$clientId}  and type in ({$typeStr})";
                $customFieldResult = $customDb->createCommand($customFieldSql)->queryAll(true);
                $customField = [];
                foreach($customFieldResult as $item){
                    $customField[$item['type']][] = $item['id'];
                }

                //对比，系统字段，在tbl_custom_field中不存在的字段
                $diffField = [];
                foreach($systemField as $type => $item){
                    if(!isset($customField[$type])){
                        $diffField[$type] = $item;
                        continue;
                    }
                    $diff = array_diff($item, $customField[$type]);
                    if(!empty($diff)){
                        $diffField[$type] = array_values($diff);
                    }
                }

                if(empty($diffField)){
                    continue;
                }

                //导出遗漏字段，需要导出客户遗漏字段时使用
//                foreach($diffField as $type => $item){
//                    foreach($item as $id){
//                        $data[] = [
//                            'client_id' => $clientId,
//                            'type' => $type,
//                            'field_id' => $id,
//                        ];
//                    }
//                }

                //组装不存在的字段信息
                $missingFields = [];
                foreach($diffField as $type => $item) {
                    foreach($item as $value){
                        $missingFields[] = [
                            'id' => $systemFieldData[$type][$value]['id'],
                            'type' => $systemFieldData[$type][$value]['type'],
                            'group_id' => $systemFieldData[$type][$value]['group_id'],
                            'base' => $systemFieldData[$type][$value]['base'],
                            'name' => $systemFieldData[$type][$value]['name'],
                            'field_type' => $systemFieldData[$type][$value]['field_type'],
                            'require' => $systemFieldData[$type][$value]['require'],
                            'edit_required' => $systemFieldData[$type][$value]['edit_required'],
                            'disable_flag' => $systemFieldData[$type][$value]['disable_flag'],//前端是否隐藏，1隐藏，0不隐藏
                            'edit_hide' => $systemFieldData[$type][$value]['edit_hide'],// 是否可编辑，1可以，0否
                            'order'=> $systemFieldData[$type][$value]['order'],
                            'app_order'=>$systemFieldData[$type][$value]['app_order'],
                            'default' => $systemFieldData[$type][$value]['default'],
                            'edit_default' => $systemFieldData[$type][$value]['edit_default'],
                            'hint' => $systemFieldData[$type][$value]['hint'],
                            'edit_hint' => $systemFieldData[$type][$value]['edit_hint'],
                            'is_exportable' => $systemFieldData[$type][$value]['is_exportable'],
                            'is_editable' => $systemFieldData[$type][$value]['is_editable'],
                            'is_list' => $systemFieldData[$type][$value]['is_list'],
                            'columns' => $systemFieldData[$type][$value]['columns'],
                            'relation_type' => $systemFieldData[$type][$value]['relation_type'],
                            'relation_field' => $systemFieldData[$type][$value]['relation_field'],
                            'relation_field_type' => $systemFieldData[$type][$value]['relation_field_type'],
                            'relation_field_name' => $systemFieldData[$type][$value]['relation_field_name'],
                            'export_scenario' => $systemFieldData[$type][$value]['export_scenario'],
                            'export_group' => $systemFieldData[$type][$value]['export_group'],
                            'readonly' => $systemFieldData[$type][$value]['readonly'],
                            'create_time' => date('Y-m-d H:i:s'),
                            'update_time' => date('Y-m-d H:i:s'),
                        ];
                    }
                }

                //修复
                \common\library\custom_field\Helper::addField($clientId,$missingFields);

            } catch (\Exception $e) {
                throw new \RuntimeException("oms模块字段检查及修复执行失败clientId[" . $clientId . "]:" . $e->getMessage());
            }
            echo "执行成功client_id:" . $clientId . PHP_EOL;
        }

        //需要导出客户遗漏字段时使用
//        $filename = '统计oms模块配置遗漏字段-' . date('YmdHis') . '.xlsx';
//        $headerFieldName = [
//            'client_id' => 'clientId',
//            'type' => 'type',
//            'field_id' => '字段id',
//        ];
//        $file = new common\library\export_v2\ExportFile();
//        $file->setFileHeader($headerFieldName);
//        $file->setFileName($filename);
//        $file->setFileType('excel');
//        $file->setFreezePane('A2');
//        $file->setData($data);
//        $tempFile =  $file->getNewWriter()->writer();
//        echo $tempFile;

    }

    // ./yiic temporary UpdateProductAllDeleteTask
    public function actionUpdateProductAllDeleteTask(){
        $clientId = 3214;
        $importId = 9926495400818;
        $selectSql = "select * from tbl_import where client_id={$clientId} and import_id={$importId}";
        $db = \PgActiveRecord::getDbByClientId($clientId);
        $task = $db->createCommand($selectSql)->queryRow();

        if(empty($task)){
            return;
        }

        $external = json_decode($task['external_field_data'], true);
        $external['min_product_id'] = 3122298467;
        $external['max_product_id'] = 3124471261;
        unset($external['query_params']);
        $external = json_encode($external);
        $sql = "update tbl_import set external_field_data = '{$external}' where client_id={$clientId} and import_id={$importId}";
        self::info($sql);
//        $res = $db->createCommand($sql)->execute();
        self::info('update success');
    }

    //oms设置订单毛利公式的客户数
    public function actionOrderProfitFormula($clientId = 0, $grey = 0, $greyNum = 0)
    {
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey) {
            $greyNum = explode(",", $greyNum);
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }
        $omsClientIds = array_column(\PrivilegeClientModule::getClientByModule(\common\library\privilege_v3\PrivilegeConstants::MODULE_OMS), 'client_id');
        $clientIds = array_intersect($clientIds, $omsClientIds);

        $count = 0;
        $setFormulaClientIds = [];
        $data = [];

        foreach ($clientIds as $clientId) {
            $adminUserId = PrivilegeService::getInstance($clientId)->getAdminUserId();
            if (empty($adminUserId)) {
                continue;
            }
            \User::setLoginUserById($adminUserId);

            $db = \ProjectActiveRecord::getDbByClientId($clientId);

            $sql = "select a.client_id,a.ext_value,a.item_id,b.item_id,b.item_name  from tbl_item_setting_external  as a join tbl_item_setting as b on a.item_id=b.item_id where a.client_id={$clientId} and  a.ext_value != '' and a.module = 52 and a.item_type = 30 and a.ext_key in ('defined_1_rmb','defined_2_rmb','defined_3_rmb')";
            $result = $db->createCommand($sql)->queryAll(true);
            if (!empty($result)) {
                $count += 1;
                $setFormulaClientIds[] = $clientId;
                $formulaMap = [];
                foreach($result as $item){
                    $formula = json_decode($item['ext_value'], true);
                    $formulaMap[$formula['calculate']] = $formula['calculate'];
                    $data[$clientId.'_'.$formula['calculate']] = [
                        'client_id' => $clientId,
                        'name' => $item['item_name'],
                    ];
                }
                $api = new \common\library\oms\order_profit\OrderProfitApi($clientId, $adminUserId);
                $formatFormula = $api->formatFormula($formulaMap);
                foreach($formatFormula as $key => $item){
                    isset($item['format_formula']) &&  $data[$clientId.'_'.$key]['formula'] = $item['format_formula'];
                }

            }
        }
        echo $count . PHP_EOL;
        echo json_encode($setFormulaClientIds);

        $filename = '统计oms用户公式配置-' . date('YmdHis') . '.xlsx';

        $headerFieldName = [
            'client_id' => 'clientId',
            'name' => '公式名称',
            'formula' => '公式',
        ];

        $file = new common\library\export_v2\ExportFile();
        $file->setFileHeader($headerFieldName);
        $file->setFileName($filename);
        $file->setFileType('excel');
        $file->setFreezePane('A2');
        $file->setData($data);
        $tempFile =  $file->getNewWriter()->writer();
        echo $tempFile;

    }

    public function actionGetProductAllDeleteTask($clientId=null, $grey = 0, $greyNum = null){
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey && !is_null($greyNum)) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        foreach ($clientIds as $clientId) {
            try {
                $db = \PgActiveRecord::getDbByClientId($clientId);
                $selectSql = "select * from tbl_import where client_id={$clientId} and type=1 and scene=5 and status=2";
                $tasks = $db->createCommand($selectSql)->queryAll();
                foreach($tasks as $task){
                    self::info("external_field_data:". $task['external_field_data'] . "--client_id={$clientId} --user_id={$task['user_id']} --import_id={$task['import_id']}");
                }
            } catch (\Throwable $e) {
                self::info("clientId:" . $clientId . " | Error:" . $e->getMessage() . " | File:" . $e->getFile() . " | Line:" . $e->getLine());
                continue;
            }
        }
    }


    //修复订单毛利回款金额
    public function actionRefreshCashCollection($clientId = 0, $grey = 0, $greyNum = 0)
    {
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey) {
            $greyNum = explode(",", $greyNum);
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }
        $omsClientIds = array_column(\PrivilegeClientModule::getClientByModule(\common\library\privilege_v3\PrivilegeConstants::MODULE_OMS), 'client_id');
        $clientIds = array_intersect($clientIds, $omsClientIds);

        foreach ($clientIds as $clientId) {
            try{
                echo "执行成功client_id:" . $clientId . PHP_EOL;
                $adminUserId = PrivilegeService::getInstance($clientId)->getAdminUserId();
                if (empty($adminUserId)) {
                    continue;
                }
                \User::setLoginUserById($adminUserId);

                $selectSql = "select order_id from tbl_cash_collection where client_id={$clientId} and update_time >= '2023-08-24 00:00:00' and order_id != 0 and refer_type = " . CashCollection::REFER_TYPE_ORDER;
                $db = \PgActiveRecord::getDbByClientId($clientId);
                $res = $db->createCommand($selectSql)->queryAll();
                $orderIds = array_filter(array_unique(array_column($res, 'order_id')));
                foreach ($orderIds as $orderId) {
                    try {
                        (new \common\library\oms\order_profit\Helper)->referOrderProfitFactor($clientId, $orderId, OrderProfitConstant::FACTOR_TYPE_OF_CASH_COLLECTION_AMOUNT, true);
                    } catch (\Exception $e) {
                        \LogUtil::info("[{$clientId}][{$orderId}]刷新订单利润回款金额错误:" . $e->getMessage());
                    }
                }
            } catch (\Exception $e) {
                throw new \RuntimeException((\Yii::t('invoice', 'Failed to refresh order profit cash collection'))."clientId[" . $clientId . "]:" . $e->getMessage());
            }

        }


    }

    public function actionTestFormula(){
        $clientId = 14119;
        $orderId = **********;
        $clientObject = \common\library\account\Client::getClient($clientId);
        $user = $clientObject->getMasterUser();
        \User::setLoginUser($user);
        $orderList = new \common\library\invoice\OrderList($user->getUserId());
        $orderList->setOrderIds([$orderId]);
        $orderList->setSkipPermissionCheck(true);
        $orderList->getFormatter()->setShowRelateObjFormulaFields(true);
        $res = $orderList->find();
//        var_dump($res);
    }

    public function actionImportDepartmentInfo($clientId = 0, $grey = 0, $greyNum = 0){
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey) {
            $greyNum = explode(",", $greyNum);
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        $csvPath = '/tmp/order_department_info.csv';
        $fp = fopen($csvPath, 'w');
        fputcsv($fp, ['client_id', '部门数等于1的订单数量', '部门数大于1的订单数量']);
        $singleTotalCount = $batchTotalCount = 0;
        foreach ($clientIds as $clientId) {
            try{
                $selectSql = "select sum(case when jsonb_array_length(departments) = 1 then 1 else 0 end) as single_department,sum(case when jsonb_array_length(departments) > 1 then 1 else 0 end)  as batch_department from tbl_order where client_id={$clientId} and ali_order_id=0 and enable_flag=1;";
                $db = \PgActiveRecord::getDbByClientId($clientId);
                $res = $db->createCommand($selectSql)->queryAll();

                if(empty($res)){
                    fputcsv($fp, [$clientId, 0, 0]);
                    continue;
                }

                $singleCount = $res[0]['single_department'] ?? 0;
                $batchCount = $res[0]['batch_department'] ?? 0;
                $singleTotalCount += $singleCount;
                $batchTotalCount += $batchCount;
                fputcsv($fp, [$clientId, $singleCount, $batchCount]);
                self::info("完成写入，client_id [{$clientId}], 部门数等于1的订单数:".$singleCount.", 部门数大于1的订单数:".$batchCount);
            } catch (\Throwable $e) {
                self::info("发送异常，clientId[" . $clientId . "]:" . $e->getMessage());
                continue;
            } finally {
                \PgActiveRecord::releaseDbByClientId($clientId);
            }

        }
        fputcsv($fp, []);
        fputcsv($fp, ['汇总', $singleTotalCount, $batchTotalCount]);

        // csv上传阿里云
        $uploadClient = \Yii::app()->params['env'] == 'test' ? 14119 : 14833;
        $clientObject = \common\library\account\Client::getClient($uploadClient);
        $user = $clientObject->getMasterUser();
        \User::setLoginUser($user);
        $upload = \UploadService::uploadRealFile($csvPath, 'zbp_order_department_info.csv', \UploadService::getFileKey($csvPath));
        $url = $upload->getFileUrl();
        $fileId = $upload->getFileId();

        self::info("部门数等于1的订单数:".$singleTotalCount.", 部门数大于1的订单数:".$batchTotalCount.', csv url:'.$url. ' ,  csv fileId:'.$fileId);
    }

    public function actionTestInitClient($clientId){
        $clientObject = \common\library\account\Client::getClient($clientId);
        $user = $clientObject->getMasterUser();
        \User::setLoginUser($user);
        $admin_user_id = $user->getUserId();
        $config = new \common\library\approval_flow\migrate\Config($admin_user_id);
        $config->init = true;
        $config->initPaymentInvoiceConfig();
        $config->initCashCollectionInvoiceConfig();
        $config->initCostInvoiceConfig();
    }


    public function actionDeleteApprovalBaseConfig($clientId=null, $grey = 0, $greyNum = null){
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey && !is_null($greyNum)) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        foreach ($clientIds as $clientId) {
            try {
                $sql = "select * from tbl_approval_flow_base_config where client_id={$clientId} and refer_type in (44,46,47) and type = 3 and disable_flag = 0 order by create_time asc";

                $db = \ProjectActiveRecord::getDbByClientId($clientId);
                $approvals = $db->createCommand($sql)->queryAll();

                if(empty($approvals)){
                    self::info("no target approvals, client id:".$clientId);
                    \ProjectActiveRecord::releaseDbByClientId($clientId);
                    continue;
                }
                $map = [];
                $rest = [];
                foreach($approvals as $approval){
                    if(!isset($map[$approval['refer_type']])){
                        $map[$approval['refer_type']] = $approval['approval_flow_id'];
                    }else{
                        if(!isset($rest[$approval['refer_type']])){
                            $rest[$approval['refer_type']] = [];
                        }
                        $rest[$approval['refer_type']][] = $approval['approval_flow_id'];
                    }
                }

                if(!empty($rest)){
                    self::info("client_id:{$clientId}, approval need to delete:".json_encode($rest));
                    $delApprovalFlowIds = Util::flattenArray($rest);
                    if(empty($delApprovalFlowIds)){
                        self::info("client_id:{$clientId}, empty delApprovalFlowIds, continue");
                        continue;
                    }
                    $delApprovalFlowIds = implode(',', $delApprovalFlowIds);
                    $updateSql = "update tbl_approval_flow_base_config set disable_flag=1 where client_id={$clientId} and refer_type in (44,46,47) and type = 3 and approval_flow_id in ({$delApprovalFlowIds})";

                    // 还需要查一下这些approval_flow_id有没有涉及的在审批的审批单 todo

                    $effectRows = 0;
//                    $effectRows = $db->createCommand($updateSql)->execute();
                    self::info("effectRows:".$effectRows);
                    self::info($updateSql);
                }else{
                    self::info("client_id:{$clientId}, approval need to delete is empty");
                }
            } catch (\Throwable $e) {
                self::info("clientId:" . $clientId . " | Error:" . $e->getMessage() . " | File:" . $e->getFile() . " | Line:" . $e->getLine());
                continue;
            }
        }
    }

    // 帮client为342435的用户（彩美佳国际贸易（深圳）有限公司）处理导入付款单数据后的上游关联单据数据状态。
    public function actionSyncPaymentUpstreamInvoice($clientId, $fp, $bingoFile='', $startTime='', $endTime=''){    // $bingoFile用来记录已经操作成功的付款单id
        $excelColumn = array_flip([
            'receiver',     // 收款方编号
            'currency',     // 币种
            'exchange_rate',    // 汇率
            'expect_time',  // 预计付款时间
            'refer_invoice_type',     // 关联单据类型
            'refer_invoice_no',  // 关联单据号
            'amount',   // 本次付款金额
            'payment_invoice_no',   // 付款单编号
            'receiver_account_name',        // 收款户名
            'payment_time'  // 实付时间
        ]);

        $excelReader = new ExcelReader($fp);
        $excelData = $excelReader->getData();
        array_shift($excelData);


        // 用户登录
        $clientObject = \common\library\account\Client::getClient($clientId);
        $user = $clientObject->getMasterUser();
        \User::setLoginUser($user);

        $paymentInvoiceNoChunk = array_chunk(array_unique(array_column($excelData, $excelColumn['payment_invoice_no'])), 1000);
        unset($excelData);

        $checkPaymentIds = [];
        if($bingoFile && is_file($bingoFile)){
            $checkPaymentIds = array_flip(array_filter(explode(',', trim(file_get_contents($bingoFile)))));
        }
        $errLogs = [];
        foreach($paymentInvoiceNoChunk as $paymentInvoiceBatch) {
            $paymentInvoiceFilter = new \common\library\oms\payment_invoice\PaymentInvoiceFilter($clientId);
            $paymentInvoiceFilter->enable_flag = \Constants::ENABLE_FLAG_TRUE;
            $paymentInvoiceFilter->payment_invoice_no = new \xiaoman\orm\database\data\In($paymentInvoiceBatch);
            $paymentInvoiceFilter->status = \common\library\oms\common\OmsConstant::PAYMENT_INVOICE_STATUS_FINISH;
            if($startTime && $endTime){
                $paymentInvoiceFilter->create_time = new DateRange($startTime, $endTime);
            }
            $paymentInvoiceFilter->select(['payment_invoice_no', 'payment_invoice_id', 'payment_time', 'trade_no']);
            $paymentInfos = $paymentInvoiceFilter->rawData();

            if(count($paymentInvoiceBatch) != count($paymentInfos)){
                $missPaymentNo = array_diff($paymentInvoiceBatch, array_column($paymentInfos, 'payment_invoice_no'));
                self::info('以下已付款的付款单编号找不到：'. json_encode($missPaymentNo));
            }

            // 变更状态
            foreach($paymentInfos as $paymentInfo){
                if(isset($checkPaymentIds[$paymentInfo['payment_invoice_id']])){
                    self::info("ID为 {$paymentInfo['payment_invoice_id']} 的付款单已变更上游单据状态，略过");
                    continue;
                }
                $paymentInvoice = new \common\library\oms\payment_invoice\PaymentInvoice($clientId, $paymentInfo['payment_invoice_id']);
                if(!$paymentInvoice->isExist()){
                    self::info('该付款单不存在，编号'. $paymentInfo['payment_invoice_no'].'，ID：'.$paymentInfo['payment_invoice_id']);
                    continue;
                }

                $targetCapitalId = 0;

                try{
//                    $paymentInvoice->getOperator()->payment($targetCapitalId, $paymentInfo['payment_time'], $paymentInfo['trade_no'], []);
                    if($bingoFile){
                        self::info("付款单【{$paymentInfo['payment_invoice_no']} | {$paymentInfo['payment_invoice_id']}】更新上游状态成功");
                        self::info("付款单ID完成:{$paymentInfo['payment_invoice_id']}");
                    }
                }catch(\Throwable $t){
                    $msg = "付款单上游单据付款状态变更失败，付款单编号【{$paymentInfo['payment_invoice_no']}】, 付款点ID【{$paymentInfo['payment_invoice_id']}】，原因：".$t->getMessage() . ' | File:'.$t->getFile().' | Line:'.$t->getLine();
                    self::info($msg);
                }
            }
        }
        self::info("操作结束");
    }

    // 帮client为342435的用户（彩美佳国际贸易（深圳）有限公司）导入付款单数据。
    public function actionImportPaymentInvoice($clientId, $fp, $checkStage=0){
        $excelColumn = array_flip([
            'receiver',     // 收款方编号
            'currency',     // 币种
            'exchange_rate',    // 汇率
            'expect_time',  // 预计付款时间
            'refer_invoice_type',     // 关联单据类型
            'refer_invoice_no',  // 关联单据号
            'amount',   // 本次付款金额
            'payment_invoice_no',   // 付款单编号
            'receiver_account_name',        // 收款户名
            'payment_time'  // 实付时间
        ]);

        $referTypeCnMap = [
            '采购单' => \Constants::TYPE_PURCHASE_ORDER,
            '费用单' => \Constants::TYPE_COST_INVOICE,
//            '出运单' => \Constants::TYPE_SHIPPING_INVOICE,
        ];

        $referTypeReceiverTypeMap = [
            \Constants::TYPE_PURCHASE_ORDER => \Constants::TYPE_SUPPLIER,
            \Constants::TYPE_COST_INVOICE => \Constants::TYPE_COMPANY,
//            \Constants::TYPE_SHIPPING_INVOICE => \Constants::TYPE_FORWARDER,
        ];

        $excelReader = new ExcelReader($fp);
        $excelData = $excelReader->getData();
        array_shift($excelData);

        $groupData = [];
        $paymentInvoiceNoList = [];
        $referNoMapList = [];
        $errorMap = []; // key是行数

        // 用户登录
        $clientObject = \common\library\account\Client::getClient($clientId);
        $user = $clientObject->getMasterUser();
        \User::setLoginUser($user);

        $mainCurrency = \common\library\account\Client::getClient($clientId)->getMainCurrency();
        $exchangeRateService = new ExchangeRateService($clientId);

        //第一次遍历做数据收集
        foreach($excelData as $line => $datum){
            $currency = trim($datum[$excelColumn['currency']]);
            if($currency == 'RMB'){
                $datum['currency'] = $currency = 'CNY';
            }

            if ($mainCurrency == ExchangeRateService::USD) {
                $exchangeRateUsd = trim($datum[$excelColumn['exchange_rate']]);
                $exchangeRate = $exchangeRateService->cnyRateForCurrency($currency);
            } else {
                $exchangeRate = trim($datum[$excelColumn['exchange_rate']]);
                $exchangeRateUsd = $exchangeRateService->usdRateForCurrency($currency);
            }

            $referType = trim($referTypeCnMap[$datum[$excelColumn['refer_invoice_type']]]);
            $referNo = trim($datum[$excelColumn['refer_invoice_no']]);
            $paymentInvoiceNo = trim($datum[$excelColumn['payment_invoice_no']]);
            if(!isset($referNoMapList[$referType])){
                $referNoMapList[$referType] = [];
            }
            if(!isset($referNoMapList[$referType][$referNo])){
                $referNoMapList[$referType][$referNo] = [];
            }
            $referNoMapList[$referType][$referNo][$paymentInvoiceNo] = [
                'amount' => trim($datum[$excelColumn['amount']]),
                'payment_amount' => trim($datum[$excelColumn['amount']]),
                'refer_type' => $referType,
                'payment_invoice_no' => $paymentInvoiceNo,     // 该字段不入库，但会作为聚合字段
                'refer_exchange_rate' => $exchangeRate,
                'refer_exchange_rate_usd' => $exchangeRateUsd,
                'refer_currency' => $currency,
                '_line' => $line + 2
            ];

            $receiveReferType = $referTypeReceiverTypeMap[$referType];
            if(!isset($groupData[$paymentInvoiceNo])){
                $groupData[$paymentInvoiceNo] = [
                    'payment_invoice_no' => $paymentInvoiceNo,
//                'receive_refer_id',     // 待填充
//                    'receive_refer_type' => $receiveReferType,    // 待填充
                    'handler' => [
                        $user->getUserId()
                    ],
                    'currency' => $currency,
                    'expect_time' => trim($datum[$excelColumn['expect_time']]),
                    'exchange_rate' => $exchangeRate,
                    'exchange_rate_usd' => $exchangeRateUsd,
                    'amount' => 0,       // 待填充，等于明细的amount求和
                    'payment_time' => trim($datum[$excelColumn['payment_time']]),
                    'record_list' => [],
                    'status' => \common\library\oms\common\OmsConstant::PAYMENT_INVOICE_STATUS_FINISH
                ];

                if(empty($groupData[$paymentInvoiceNo]['payment_time'])){
                    $groupData[$paymentInvoiceNo]['payment_time'] = $groupData[$paymentInvoiceNo]['expect_time'];
                }
                $paymentInvoiceNoList[] = $paymentInvoiceNo;
            }
        }

        // 验证付款单号是否重复(正式跑的时候这一步可以注释掉，确认一遍即可)
        $paymentInvoiceFilter = new \common\library\oms\payment_invoice\PaymentInvoiceFilter($clientId);
        $paymentInvoiceFilter->enable_flag = \Constants::ENABLE_FLAG_TRUE;
        $paymentInvoiceFilter->payment_invoice_no = $paymentInvoiceNoList;
        $paymentInvoiceFilter->select(['payment_invoice_no']);
        $existPaymentInvoiceNo = array_column($paymentInvoiceFilter->rawData(), 'payment_invoice_no');

        if(!empty($existPaymentInvoiceNo)){
            self::info("以下付款单编号已存在：".implode(',', $existPaymentInvoiceNo).'，跳过');
            foreach($existPaymentInvoiceNo as $existNo){
                $errorMap[$existNo] = "付款单【{$existNo}】已存在，跳过";
            }
        }

        // 根据关联单据编号做查询
        $purchaseOrderNo = array_keys($referNoMapList[\Constants::TYPE_PURCHASE_ORDER]);
        $costInvoiceNo = array_keys($referNoMapList[\Constants::TYPE_COST_INVOICE]);
        $payableInfoMap = [];
        $notExistPurchaseOrderNo = $notExistCostInvoiceNo = [];

        if(!empty($purchaseOrderNo)){
            $purchaseOrderFilter = new \common\library\purchase\purchase_order\PurchaseOrderFilter($clientId);
            $purchaseOrderFilter->enable_flag = \Constants::ENABLE_FLAG_TRUE;
            $purchaseOrderFilter->purchase_order_no = $purchaseOrderNo;
            $purchaseOrderFilter->select(['purchase_order_id' => 'refer_id', 'purchase_order_no' => 'record_refer_no', 'supplier_id' => 'receive_refer_id', 'amount']);
            $purchaseInfos = $purchaseOrderFilter->rawData();

            $purchaseOrderIds = array_column($purchaseInfos, 'refer_id');
            $existPurchaseOrderNo = array_column($purchaseInfos, 'record_refer_no');
            $notExistPurchaseOrderNo = array_diff($purchaseOrderNo, $existPurchaseOrderNo);

            if(!empty($purchaseOrderIds)){
                $payableInvoiceFilter = new \common\library\oms\payable_invoice\PayableInvoiceFilter($clientId);
                $payableInvoiceFilter->enable_flag = \Constants::ENABLE_FLAG_TRUE;
                $payableInvoiceFilter->refer_id = $purchaseOrderIds;
                $payableInvoiceFilter->select(['refer_id', 'payable_invoice_id']);
                $payableInfoMap = array_column($payableInvoiceFilter->rawData(), 'payable_invoice_id', 'refer_id');
            }

            foreach($purchaseInfos as $purchaseInfo){
                $records = $referNoMapList[\Constants::TYPE_PURCHASE_ORDER][$purchaseInfo['record_refer_no']];
                foreach($records as $record){
                    $record['refer_id'] = $purchaseInfo['refer_id'];
                    $record['receive_refer_id'] = $purchaseInfo['receive_refer_id'];     // 取付款单的第一个record的receive_refer_id作为付款单的receive_refer_id
//                $record['payment_amount'] = $purchaseInfo['amount'];
                    $record['payable_invoice_id'] = $payableInfoMap[$purchaseInfo['refer_id']] ?? 0;
//                $referNoMapList[\Constants::TYPE_PURCHASE_ORDER][$purchaseInfo['record_refer_no']] = $record;
                    $targetPaymentNo = $record['payment_invoice_no'];
                    if(!empty($groupData[$targetPaymentNo])){
                        $groupData[$targetPaymentNo]['record_list'][] = $record;
                        if(empty($groupData[$targetPaymentNo]['receive_refer_id'])){
                            $groupData[$targetPaymentNo]['receive_refer_id'] = $purchaseInfo['receive_refer_id'];
                            $groupData[$targetPaymentNo]['receive_refer_type'] = \Constants::TYPE_SUPPLIER;
                        }else{
                            // 校验一个付款单下的多个收款人是否相同
                            if($purchaseInfo['receive_refer_id'] != $groupData[$targetPaymentNo]['receive_refer_id']){
                                $errorMap[$groupData[$targetPaymentNo]['payment_invoice_no']] = "付款单【{$groupData[$targetPaymentNo]['payment_invoice_no']}】的明细之间的收款人不相同 | line: ".$record['_line'];
                            }
                        }
                        $groupData[$targetPaymentNo]['amount'] += $record['amount'];
                    }
                }
            }
        }

        if(!empty($costInvoiceNo)){
            $costInvoiceFilter = new \common\library\oms\cost_invoice\CostInvoiceFilter($clientId);
            $costInvoiceFilter->enable_flag = \Constants::ENABLE_FLAG_TRUE;
            $costInvoiceFilter->cost_invoice_no = $costInvoiceNo;
            $costInvoiceFilter->select(['cost_invoice_id' => 'refer_id', 'cost_invoice_no' => 'record_refer_no', 'receive_refer_id', 'amount', 'receive_refer_type']);
            $costInvoiceInfos = $costInvoiceFilter->rawData();

            $costInvoiceIds = array_column($costInvoiceInfos, 'refer_id');
            $existCostInvoiceNo = array_column($costInvoiceInfos, 'record_refer_no');
            $notExistCostInvoiceNo = array_diff($costInvoiceNo, $existCostInvoiceNo);

            if(!empty($costInvoiceIds)){
                $payableInvoiceFilter = new \common\library\oms\payable_invoice\PayableInvoiceFilter($clientId);
                $payableInvoiceFilter->enable_flag = \Constants::ENABLE_FLAG_TRUE;
                $payableInvoiceFilter->refer_id = $costInvoiceIds;
                $payableInvoiceFilter->select(['refer_id', 'payable_invoice_id']);
                $payableInfoMap = array_column($payableInvoiceFilter->rawData(), 'payable_invoice_id', 'refer_id');
            }

            foreach($costInvoiceInfos as $costInvoiceInfo){
                $records = $referNoMapList[\Constants::TYPE_COST_INVOICE][$costInvoiceInfo['record_refer_no']];
                foreach($records as $record){
                    $record['refer_id'] = $costInvoiceInfo['refer_id'];
                    $record['receive_refer_id'] = $costInvoiceInfo['receive_refer_id'];     // 取付款单的第一个record的receive_refer_id作为付款单的receive_refer_id
//                $record['payment_amount'] = $costInvoiceInfo['amount'];
                    $record['payable_invoice_id'] = $payableInfoMap[$costInvoiceInfo['refer_id']] ?? 0;

                    $targetPaymentNo = $record['payment_invoice_no'];
                    if(!empty($groupData[$targetPaymentNo])){
                        $groupData[$targetPaymentNo]['record_list'][] = $record;
                        if(empty($groupData[$targetPaymentNo]['receive_refer_id'])){
                            $groupData[$targetPaymentNo]['receive_refer_id'] = $costInvoiceInfo['receive_refer_id'];
                            $groupData[$targetPaymentNo]['receive_refer_type'] = $costInvoiceInfo['receive_refer_type'];
                        }else{
                            // 校验一个付款单下的多个收款人是否相同
                            if($costInvoiceInfo['receive_refer_id'] != $groupData[$targetPaymentNo]['receive_refer_id']){
                                $errorMap[$groupData[$targetPaymentNo]['payment_invoice_no']] = "付款单【{$groupData[$targetPaymentNo]['payment_invoice_no']}】的明细之间的收款人不相同 | line: ".$record['_line'];
                            }
                        }
                        $groupData[$targetPaymentNo]['amount'] += $record['amount'];
                    }
                }
            }
        }

        // 检验关联单据编号是否存在
        if(!empty($notExistCostInvoiceNo) || !empty($notExistPurchaseOrderNo)){
            $notExistReferInvoiceNo = implode(',', array_merge($notExistCostInvoiceNo, $notExistPurchaseOrderNo));
//            self::info("以下关联单据不存在：".$notExistReferInvoiceNo);

            foreach($notExistCostInvoiceNo as $missInvoiceNo){
                $missReferInvoices = $referNoMapList[\Constants::TYPE_COST_INVOICE][$missInvoiceNo];
                foreach($missReferInvoices as $missReferInvoice){
                    $targetPaymentNo = $missReferInvoice['payment_invoice_no'];
                    $line = $missReferInvoice['_line'];
                    self::info("付款单【{$targetPaymentNo}】无法创建，原因：关联单据【{$missInvoiceNo}】不存在，line: {$line}");
                }
            }

            foreach($notExistPurchaseOrderNo as $missInvoiceNo){
                $missReferInvoices = $referNoMapList[\Constants::TYPE_PURCHASE_ORDER][$missInvoiceNo];
                foreach($missReferInvoices as $missReferInvoice){
                    $targetPaymentNo = $missReferInvoice['payment_invoice_no'];
                    $line = $missReferInvoice['_line'];
                    self::info("付款单【{$targetPaymentNo}】无法创建，原因：关联单据【{$missInvoiceNo}】不存在，line: {$line}");
                }
            }
        }

        unset($referNoMapList);

        // 导入
        self::info('总付款单导入数量：'.count($groupData));
        if(!$checkStage){
            foreach($groupData as $k => $datum){
                if(isset($errorMap[$datum['payment_invoice_no']])){
                    self::info($errorMap[$datum['payment_invoice_no']]);
                    continue;
                }

                if(empty($datum['record_list'])){
                    continue;
                }

                if(empty($datum['payment_time'])){
                    self::info("付款单【{$datum['payment_invoice_no']}】无法创建，原因：实付日期格式错误，line:".implode(',', array_column($datum['record_list'], '_line')));
                    continue;
                }
                // 检查要生成的付款单的明细之间它们的币种、汇率是否相同
//            foreach($datum['record_list'] as $record){
//                if
//            }

                try{
                    $paymentInvoiceField = \common\library\oms\common\OmsField::make($clientId, \Constants::TYPE_PAYMENT_INVOICE);
                    $dataUnpack = $paymentInvoiceField->unpackFormData($datum);
                    $paymentInvoiceField->validateData($dataUnpack, true);
                    $paymentInvoice = new \common\library\oms\payment_invoice\PaymentInvoice($clientId);

                    $paymentInvoice->bindAttrbuties($dataUnpack);
                    $paymentInvoice->createForTransaction();
//                    $paymentInvoice->getOperator()->payment(0, $datum['payment_time'], '', []);
                    self::info('导入成功：'.$datum['payment_invoice_no'].' | 下标：'.$k);
                }catch(\Throwable $e){
                    $err = $e->getMessage()." | File:". $e->getFile()." | Line：".$e->getLine()." | excel line:".implode(',', array_column($datum['record_list'], '_line'))." | datum:".json_encode($datum);
                    $errorMap[$datum['payment_invoice_no']] = $err;
                    self::info($err);
                }
            }
        }else{
            self::info("all err:".json_encode($errorMap));
        }
    }

    // 帮client为342435的用户（彩美佳国际贸易（深圳）有限公司）作废&删除有误的导入付款单
    public function actionDeletePaymentInvoice($clientId, $fp, $startTime, $endTime, $editStartTime='', $editEndTime=''){
        $excelColumn = array_flip([
            'receiver',     // 收款方编号
            'currency',     // 币种
            'exchange_rate',    // 汇率
            'expect_time',  // 预计付款时间
            'refer_invoice_type',     // 关联单据类型
            'refer_invoice_no',  // 关联单据号
            'amount',   // 本次付款金额
            'payment_invoice_no',   // 付款单编号
            'receiver_account_name',        // 收款户名
            'payment_time'  // 实付时间
        ]);

        $excelReader = new ExcelReader($fp);
        $excelData = $excelReader->getData();
        array_shift($excelData);

        // 用户登录
        $clientObject = \common\library\account\Client::getClient($clientId);
        $user = $clientObject->getMasterUser();
        \User::setLoginUser($user);

        $paymentNoAmountMap = [];

        foreach($excelData as $datum){
            $paymentInvoiceNo = trim($datum[$excelColumn['payment_invoice_no']]);
            $amount = trim($datum[$excelColumn['amount']]);
            if(!isset($paymentNoAmountMap[$paymentInvoiceNo])){
                $paymentNoAmountMap[$paymentInvoiceNo] = 0;
            }
            $paymentNoAmountMap[$paymentInvoiceNo] += $amount;
        }

        $allNo = array_keys($paymentNoAmountMap);
        $paymentInvoiceFilter = new \common\library\oms\payment_invoice\PaymentInvoiceFilter($clientId);
        $paymentInvoiceFilter->enable_flag = \Constants::ENABLE_FLAG_TRUE;
        $paymentInvoiceFilter->payment_invoice_no = $allNo;
        $paymentInvoiceFilter->create_time = new DateRange($startTime, $endTime);
        if(!empty($editStartTime) && !empty($editEndTime)){
            $paymentInvoiceFilter->update_time = new DateRange($editStartTime, $editEndTime);
        }
        $paymentInvoiceFilter->select(['payment_invoice_no', 'amount', 'payment_invoice_id']);
        $existPaymentInvoice = array_column($paymentInvoiceFilter->rawData(), null, 'payment_invoice_no');

        $dirtyPaymentMap = [];
        foreach($existPaymentInvoice as $existItem){
//            if(trim(floatval($existItem['amount']), '0') != trim(floatval($paymentNoAmountMap[$existItem['payment_invoice_no']]), 0)){
                $dirtyPaymentMap[$existItem['payment_invoice_no']] = $existItem['payment_invoice_id'];
//            }
        }

        self::info("脏数据：".json_encode($dirtyPaymentMap));

        // 作废和删除
        foreach($dirtyPaymentMap as $targetNo => $paymentInvoiceId){
            /*
            $paymentInvoice = new \common\library\oms\payment_invoice\PaymentInvoice($clientId, $paymentInvoiceId);
            if (!in_array($paymentInvoice->status, [
                \common\library\oms\common\OmsConstant::PAYMENT_INVOICE_STATUS_DRAFT,
                \common\library\oms\common\OmsConstant::PAYMENT_INVOICE_STATUS_DISCARD,
            ])) {
                $paymentInvoice->getOperator()->discard();
            }
            $paymentInvoice->getOperator()->delete();
*/
            self::info("付款单作废和删除成功, 编号：{$targetNo}, id：{$paymentInvoiceId}, 录入错误金额：{$existPaymentInvoice[$targetNo]['amount']}, 实际应录入金额：{$paymentNoAmountMap[$targetNo]}");
        }
    }

    public function actionFixPaymentInvoiceReceiveReferType($clientId, $startTime, $endTime){
        $selectSql = "select payment_invoice_id,payment_invoice_no from tbl_payment_invoice where client_id = {$clientId} and create_time > '{$startTime}' and create_time < '{$endTime}' and receive_refer_type=0";
        $updateSql = "update tbl_payment_invoice set receive_refer_type=23 where client_id = {$clientId} and create_time > '{$startTime}' and create_time < '{$endTime}' and receive_refer_type=0";
        $affectRows = 0;
        $db = \PgActiveRecord::getDbByClientId($clientId);
        $wrongData = $db->createCommand($selectSql)->queryAll();
//        $affectRows = $db->createCommand($updateSql)->execute();
        self::info("wrong data:".json_encode($wrongData));
        self::info('affect rows:'.$affectRows);
    }

    // 临时脚本，不可用作标准化修复逻辑
    public function actionFixEmptyInvoiceProducrtRecord($clientId){
        // 先查出有哪些订单product_list有数据，但在 tbl_invoice_product_record 中没有数据
        $selectSql = "select order_id from tbl_order where enable_flag=1 and client_id={$clientId} and (product_list::text!='[]' and product_list::text!='' and product_list::text!='{}') and order_id not in (select refer_id from tbl_invoice_product_record where client_id={$clientId} and enable_flag=1 and type=2 group by refer_id)";
        $db = \PgActiveRecord::getDbByClientId($clientId);
        $targetOrderIds = array_column($db->createCommand($selectSql)->queryAll(), 'order_id');

        if(empty($targetOrderIds)){
            self::info("no order to fix");
            return false;
        }

        $targetOrderIds = implode(',', $targetOrderIds);
        echo $targetOrderIds;
        $productListSql = "select order_id, product_list,create_user from tbl_order where client_id={$clientId} and order_id in ({$targetOrderIds})";
        $productLists = array_column($db->createCommand($productListSql)->queryAll(), null,'order_id');
//        $targetFields = ["sort","unit","count","sku_id","offer_data","other_cost","product_id","unit_price","cost_amount","description","gross_margin","package_unit","product_name","product_type","cost_with_tax","product_image","product_model","package_volume","product_remark","sku_attributes","product_cn_name","gross_margin_cny","gross_margin_usd","purchase_cost_unit","external_field_data","gross_profit_margin","package_gross_weight","purchase_cost_unit_rmb","purchase_cost_unit_usd","package_volume_subtotal","package_gross_weight_subtotal"];

        /*
        foreach($productLists as $orderId => $productListInfo){
            $userId = $productListInfo['create_user'];
            \User::setLoginUserById($userId);
            $order = new \common\library\invoice\Order($userId, $orderId);
            $productList = json_decode($productListInfo['product_list'], true);
            foreach($productList as &$record){
                unset($record['unique_id']);
            }
            $order->product_list = $productList;
            $order->save();
        }
        */
    }

    // 临时脚本，修复用户导入多规格产品时spu编号和sku编号相同的数据
    public function actionFixProductNo($clientId){
        if(empty($clientId)){
            return false;
        }
        $selectSql = "select spu.product_no,sku.product_id,sku.sku_code,sku_id,sku.product_id from tbl_product spu inner join tbl_product_sku sku on spu.product_id = sku.product_id where  sku.client_id={$clientId} and sku.enable_flag=1 and spu.enable_flag=1 and spu.product_no = sku_code order by sku.sku_id";

        $db = \PgActiveRecord::getDbByClientId($clientId);
        $skuInfos = $db->createCommand($selectSql)->queryAll();

        if(empty($skuInfos)){
            self::info('target sku empty');
            return false;
        }
        $indexMap = []; // key是product_id，value是sku的下标序号
        $skuChunks = array_chunk($skuInfos, 500);
        $updateSql = "update tbl_product_sku set sku_code = case ";
        $affectRows = 0;
        foreach($skuChunks as $skuChunk){
            $batchUpdateSql = $updateSql;
            $updateSkuIds = [];
            foreach($skuChunk as $skuInfo){
                if(!isset($indexMap[$skuInfo['product_id']])){
                    $indexMap[$skuInfo['product_id']] = 0;
                }

                $indexMap[$skuInfo['product_id']]++;
                $skuCode = $skuInfo['product_no'] . "_" . $indexMap[$skuInfo['product_id']];
                $batchUpdateSql .= " when sku_id={$skuInfo['sku_id']} then '{$skuCode}' ";
                $updateSkuIds[] = $skuInfo['sku_id'];
            }

            if(!empty($updateSkuIds)){
                $updateSkuIds = implode(',', $updateSkuIds);
                $batchUpdateSql .= " else sku_code end where client_id={$clientId} and sku_id in ({$updateSkuIds})";
//                $affectRows += $db->createCommand($batchUpdateSql)->execute();
                self::info($batchUpdateSql);
            }
        }

        self::info("affected rows:".$affectRows);
        // 修改ES
//        $this->actionUpdateProductSearchData($clientId);
    }

    public function actionFixTaskData($setId, $rds, $createTime, $dryRun=1)
    {
        $type = DbSet::TYPE_PGSQL;
        /** @var CDbConnection $db */
        $db = Yii::app()->account_base_db;
        $sql = 'select * from tbl_db_set where type=:type and set_id=:set_id';
        $item = $db->createCommand($sql)->queryRow(true ,[
            ':type' => $type,
            ':set_id' => $setId
        ]);

        $oldSchemaName = $newSchemaName = $item['schema_name'];
        $newDbName = $oldDbName = $item['name'];
        $oldPort = $item['port'];
        $newPort  = '5432';
        $oldHost = $item['host'];
        $newHost = $rds;

        $oldDsn = "pgsql:host={$oldHost};dbname={$oldDbName};port={$oldPort}";
        $oldConnection = new DbConnection($oldDsn, $item['user'], $item['password']);
        $oldConnection->charset = 'utf8';

        //pgsql指定schema
        $oldConnection->initSQLs = ["SET search_path TO {$oldSchemaName}"];
        $oldConnection->getSchema()->defaultSchema = $oldSchemaName;
        $sql = "select task_id from tbl_task where create_time > '{$createTime}' and allow_duplicate = 1";
        $oldTaskId = $oldConnection->createCommand($sql)->queryColumn();

        $newDsn = "pgsql:host={$newHost};dbname={$newDbName};port={$newPort}";
        $newConnection = new DbConnection($newDsn, $item['user'], $item['password']);
        $newConnection->charset = 'utf8';

        //pgsql指定schema
        $newConnection->initSQLs = ["SET search_path TO {$newSchemaName}"];
        $newConnection->getSchema()->defaultSchema = $newSchemaName;
        $sql = "select task_id from tbl_task where create_time > '{$createTime}' and allow_duplicate = 1";
        $newTaskId = $newConnection->createCommand($sql)->queryColumn();

        $missingTaskIds = array_diff($oldTaskId, $newTaskId);
        if ($missingTaskIds) {
            echo "missing taskCount:" . count($missingTaskIds) . " detail:" . implode(',', $missingTaskIds) . "\n";

            $missingData = $oldConnection->createCommand("select * from tbl_task where task_id in (" . implode(',', $missingTaskIds) . ")")->queryAll(true);
            [$insertSql, $insertParams] = \common\library\util\SqlBuilder::buildMultiInsert('tbl_task', $missingData, false);
            echo "insert sql [$insertSql], params:" . json_encode($insertParams) . "\n";
            if (!$dryRun) {
                $count = $newConnection->createCommand($insertSql)->execute($insertParams);
                echo "trans count:{$count} \n";
            }
        } else {
            echo "no missing for set {$setId} \n";
        }
    }

    public function actionSearchRepeatUniqueIdOrder($clientId){
        // 先查出有哪些订单product_list有数据，但在 tbl_invoice_product_record 中没有数据
        $selectSql = "select order_id,name,order_no,product_list from tbl_order where enable_flag=1 and client_id={$clientId}";
        $db = \PgActiveRecord::getDbByClientId($clientId);
        $allOrders = array_column($db->createCommand($selectSql)->queryAll(),null, 'order_id');

        if(empty($allOrders)){
            self::info("no order to search");
            return false;
        }

        $repeatUniqueIdOrderIdMap = [];     // key是unique_id，value是order_id
        $resultOrderIds = [];
        foreach($allOrders as $order){
            $productList = json_decode($order['product_list'], true);
            foreach($productList as $record){
                if(empty($record['unique_id'])){
                    continue;
                }

                if(!isset($repeatUniqueIdOrderIdMap[$record['unique_id']])){
                    $repeatUniqueIdOrderIdMap[$record['unique_id']] = [];
                }
                $repeatUniqueIdOrderIdMap[$record['unique_id']][] = $order['order_id'];
            }
        }



        $result = "";
        $repeatMap = [];
        foreach($repeatUniqueIdOrderIdMap as $mapArray){
            if(count($mapArray) > 1){
                foreach($mapArray as $targetOrderId){
                    $order = $allOrders[$targetOrderId];
                    if(isset($repeatMap[$order['order_id']])){
                        continue;
                    }
                    $result .= $order['order_id'] .",".$order['order_no'].",".$order['name']."\r\n";
                    $repeatMap[$order['order_id']] = true;
                }
            }
        }
        echo $result;
    }

    // 临时脚本，帮 client_id=39157 的用户修复出库单的成本单价
    public function actionFixCostUnitPrice(){
        $clientId = 39157;
        $targetOutboundIds = [11822779255798,12218080572349,12187485228035,11755171835152];
        $selectSql = "select outbound_record_id, inventory_cost_unit_price_rmb, inventory_cost_unit_price_usd from tbl_outbound_record where client_id={$clientId} and outbound_invoice_id in (". implode(",", $targetOutboundIds) .") and delete_flag=0";
        $db = \PgActiveRecord::getDbByClientId($clientId);
        $selectRes = $db->createCommand($selectSql)->queryAll();
        self::info("select result:".json_encode($selectRes));

        $updateSql = "update tbl_outbound_record set cost_unit_price_rmb = inventory_cost_unit_price_rmb, cost_unit_price_usd=inventory_cost_unit_price_usd where client_id={$clientId} and outbound_invoice_id in (". implode(",", $targetOutboundIds) .") and delete_flag=0";
        $affectRows = 0;
//        $affectRows = $db->createCommand($updateSql)->execute();
        self::info('affect rows:'.$affectRows);
    }

    // 重算用户的费用单/付款单的付款状态 和 付款金额
    public function actionRefreshPaymentStatus($clientId){
        $clientObject = \common\library\account\Client::getClient($clientId);
        $user = $clientObject->getMasterUser();
        \User::setLoginUser($user);

        // 获取用户所有已付款的付款明细
        $filter = new \common\library\oms\payment_invoice\PaymentInvoiceFilter($clientId);
        $recordFilter = new \common\library\oms\payment_invoice\record\PaymentInvoiceRecordFilter($clientId);
        $filter->enable_flag = \Constants::ENABLE_FLAG_TRUE;
        $recordFilter->enable_flag = \Constants::ENABLE_FLAG_TRUE;
        $recordFilter->select(['payable_invoice_id', 'amount' => function ($column, $table, $quto) {
            return 'sum(tbl_payment_record.amount) as amount';
        }]);
        $filter->status = \common\library\oms\common\OmsConstant::PAYMENT_INVOICE_STATUS_FINISH;
        $joinFilter = $filter->initJoin();
        $joinFilter = $joinFilter->innerJoin($recordFilter)->on('payment_invoice_id', 'payment_invoice_id');
        $joinFilter->joinGroupBy('tbl_payment_record.payable_invoice_id');
        $payableInvoices = array_column($joinFilter->rawData(), null, 'payable_invoice_id');

        // 查询所有应付款单现有脏数据金额(已付款金额)
        $payableFilter = new \common\library\oms\payable_invoice\PayableInvoiceFilter($clientId);
        $payableFilter->enable_flag = \Constants::ENABLE_FLAG_TRUE;
        $payableFilter->select(['payable_invoice_id', 'payment_amount', 'status']);
        $payableDirtyData = array_column($payableFilter->rawData(), null, 'payable_invoice_id');

        self::info("开始刷新应付款单的付款金额");
        $payableInvoiceApi = new \common\library\oms\payable_invoice\PayableInvoiceApi($clientId);
        foreach($payableDirtyData as $dirtyPayableId => $payableDirtyDatum){
            $realAmount = floatval($payableInvoices[$dirtyPayableId]['amount'] ?? 0);
            $dirtyAmount = floatval($payableDirtyDatum['payment_amount']);
            if($realAmount == $dirtyAmount){
                continue;
            }

            $payableInvoice = new \common\library\oms\payable_invoice\PayableInvoice($clientId, $dirtyPayableId);
            if (!$payableInvoice->isExist()) {
                self::info("refreshPayableId:{$dirtyPayableId} not exist");
                continue;
            }

            $status = $payableInvoiceApi->getPayableStatus($payableInvoice, $realAmount);

            self::info("应付款单id：{$dirtyPayableId} 脏数据金额：{$dirtyAmount} 修正后金额：{$realAmount} 修正前状态：{$payableDirtyDatum['status']} 修正后状态：{$status}");

            // $payableInvoice->getOperator()->refreshStatusAndPaymentAmount($status, $realAmount);
        }

/*
        self::info("开始刷新订单毛利");
        $orderProfitFilter = new \common\library\oms\order_profit\OrderProfitFilter($clientId);
        $orderProfitFilter->enable_flag = \Constants::ENABLE_FLAG_TRUE;
        $orderProfitFilter->select(['order_id']);
        $targetOrderIds = array_column($orderProfitFilter->rawData(), 'order_id');
        $helper = new \common\library\oms\order_profit\Helper();
        foreach($targetOrderIds as $targetOrderId){
            $helper->referByOrderId($clientId, $targetOrderId);
        }
*/
        self::info("所有操作结束");
    }


    public function repairCustomer_emptyEmail($adminDb, $pgDb, $mysqlDb, $client_id, $wrongCustomerRecord) {
        // email 为 "" 但 email_id 不为空的情况, 把 tbl_customer.email_id 设置为 0
        $customer_id = $wrongCustomerRecord['customer_id'];
        $sql = "update tbl_customer set email_id=0 where customer_id=$customer_id";
        $count = $pgDb->createCommand($sql)->execute();


        // 旧 email_id 所对应的 tbl_email_relation.customer_id 和 tbl_mail.relate_company_flag 要看情况改动

        // 查出旧 email_id 对应的 email
        $oldEmailId = $wrongCustomerRecord['currentEmailId'];
        $sql = "select email from tbl_email_id where email_id=$oldEmailId;";
        $oldEmailRecords = $adminDb->createCommand($sql)->queryAll();
        if (empty($oldEmailRecords)) {
            return;
        }

        // 查查目前是否有联系人在使用这个 email
        $oldEmail = $oldEmailRecords[0]['email'];
        $sql = "select * from tbl_customer where client_id=$client_id and is_archive=1 and lower(email)=lower('$oldEmail')";
        $customerReords = $pgDb->createCommand($sql)->queryAll();
        if (empty($customerReords)) {
            // 如果旧 email 确认没联系人在用, 那就把
            //    tbl_email_relation.customer_id 置 0
            //    tbl_mail.relate_company_flag 置 0
            $sql = "update tbl_email_relation set customer_id=0 where client_id=$client_id and email_id=$oldEmailId";
            $mysqlDb->createCommand($sql)->execute();

            common\library\email\Helper::updateRelateCompanyFlag($client_id, [$oldEmailId], false);
        } else {
            // 如果旧 email 还有其他联系人在用, 那不要去管, 因为其他联系人会来刷新的
        }
    }

    // 修复: 邮件板块 - 客户邮件 - 客户分组下的公司列表 - 公司是否有往来邮件显示错误问题
    public function actionCompanyCardRepairForClient($client_id, $numPerBatch) {
        // 工单客户
        // 【【工单#20231113000058】客户有往来邮件，但是客户名在列表中颜色是浅色】https://www.tapd.cn/21404721/bugtrace/bugs/view?bug_id=1121404721001093035

        $adminDb = Yii::app()->db;
        $pgDb = PgActiveRecord::getDbByClientId($client_id);
        $mysqlDb = \ProjectActiveRecord::getDbByClientId($client_id);

        $maxCustomerIdInLastBatch = -1;
        $batchIndex = 1;
        while (true) {
            echo "batchIndex: $batchIndex, numPerBatch: $numPerBatch, currentCustomerId: $maxCustomerIdInLastBatch\n";
            $batchIndex += 1;

            // 查一批 customers
            $sql = "select client_id, customer_id, email, email_id, is_archive from tbl_customer where client_id=$client_id and is_archive=1 and customer_id>$maxCustomerIdInLastBatch order by customer_id limit $numPerBatch;";
            $archiveCustomerList = $pgDb->createCommand($sql)->queryAll();
            // 已经遍历完, 结束了
            if (count($archiveCustomerList) === 0) {
                break;
            }

            // 下一批的 customer_id 起点
            $maxCustomerIdInLastBatch = $archiveCustomerList[count($archiveCustomerList)-1]['customer_id'];


            // 根据 email 到 tbl_email_id 查出绝对正确的 email_id
            $tmpEmails = [];
            foreach ($archiveCustomerList as $c) {
                $tmpEmails[] = '"' . $c['email'] . '"';
            }
            $tmpEmailsStr = join(",", $tmpEmails);
            $sql = "select email, email_id from tbl_email_id where email in ($tmpEmailsStr);";
            $emailRecords = $adminDb->createCommand($sql)->queryAll();


            // 建个 email-email_id map, 后面用来查
            $map_email_to_id = [];
            foreach ($emailRecords as $r) {
                $map_email_to_id[$r['email']] = $r['email_id'];
            }
            // 空邮箱的正确 email_id 应该是 0
            $map_email_to_id[""] = 0;


            // 检查 customer 里的 email_id 是否正确, 把不正确的记录下来
            $wrongCustomerRecords = [];
            foreach ($archiveCustomerList as $c) {
                $email = $c['email'];

                // 万一 email 在 tbl_email_id 中不存在, 得判断, 避免脚本终止
                if (!key_exists($email, $map_email_to_id)) {
                    continue;
                }

                $currentEmailId = intval($c['email_id']);
                $realEmailId = intval($map_email_to_id[$email]);

                // email_id 不正确, 记录下来
                if ($currentEmailId !== $realEmailId) {
                    $customer_id = $c['customer_id'];
                    $client_id = $c['client_id'];
                    $wrongCustomerRecords[] = [
                        'client_id' => $client_id,
                        'customer_id' => $customer_id,
                        'email' => $email,
                        'currentEmailId' => $currentEmailId,
                        'realEmailId' => $realEmailId,
                    ];
                }
            }
            // 检查完, 如果没有需要修的
            if (empty($wrongCustomerRecords)) {
                continue;
            }


            // var_dump($wrongCustomerRecords);
            $wrongCount = count($wrongCustomerRecords);
            echo "需要修复 $wrongCount 条数据\n";


            // 有问题的批量 update 修复
            // 修复 tbl_customer.email_id
            // 修复 tbl_email_relation.customer_id
            // 修复 tbl_mail.relate_company_flag
            foreach ($wrongCustomerRecords as $wc) {
                $email = $wc['email'];
                if (!empty($email)) {
                    $client_id = $wc['client_id'];
                    $customer_id = $wc['customer_id'];
                    common\library\email\Helper::updateCustomerEmailRelation($client_id, $customer_id, $email, true);
                } else {
                    $this->repairCustomer_emptyEmail($adminDb, $pgDb, $mysqlDb, $client_id, $wc);
                }
            }


            echo "ok\n";
        }

        echo "over\n";
    }



    public function repairByPgDb($pgDb, $numPerBatch, $debug=false) {
        $adminDb = Yii::app()->db;

        $maxCustomerIdInLastBatch = -1;
        $batchIndex = 1;
        while (true) {
            echo "batchIndex: $batchIndex, numPerBatch: $numPerBatch, currentCustomerId: $maxCustomerIdInLastBatch\n";
            $batchIndex += 1;

            // 查一批 customers
            $sql = "select client_id, customer_id, email, email_id from tbl_customer where is_archive=1 and customer_id>$maxCustomerIdInLastBatch order by customer_id limit $numPerBatch;";
            $archiveCustomerList = $pgDb->createCommand($sql)->queryAll();

            // 已经遍历完, 结束了
            if (count($archiveCustomerList) === 0) {
                break;
            }

            // 下一批的 customer_id 起点
            $maxCustomerIdInLastBatch = $archiveCustomerList[count($archiveCustomerList)-1]['customer_id'];


            // 根据 email 到 tbl_email_id 查出绝对正确的 email_id
            $tmpEmails = [];
            foreach ($archiveCustomerList as $c) {
                $tmpEmails[] = '"' . $c['email'] . '"';
            }
            $tmpEmailsStr = join(",", $tmpEmails);
            $sql = "select email, email_id from tbl_email_id where email in ($tmpEmailsStr);";
            $emailRecords = $adminDb->createCommand($sql)->queryAll();


            // 建个 email-email_id map, 后面用来查
            $map_email_to_id = [];
            foreach ($emailRecords as $r) {
                $map_email_to_id[$r['email']] = $r['email_id'];
            }
            // 空邮箱的正确 email_id 应该是 0
            $map_email_to_id[""] = 0;


            // 检查 customer 里的 email_id 是否正确, 把不正确的记录下来
            $wrongCustomerRecords = [];
            foreach ($archiveCustomerList as $c) {
                $email = $c['email'];

                // 万一 email 在 tbl_email_id 中不存在, 得判断, 避免脚本终止
                if (!key_exists($email, $map_email_to_id)) {
                    continue;
                }

                $currentEmailId = intval($c['email_id']);
                $realEmailId = intval($map_email_to_id[$email]);

                // email_id 不正确, 记录下来
                if ($currentEmailId !== $realEmailId) {
                    $customer_id = $c['customer_id'];
                    $client_id = $c['client_id'];
                    $wrongCustomerRecords[] = [
                        'client_id' => $client_id,
                        'customer_id' => $customer_id,
                        'email' => $email,
                        'currentEmailId' => $currentEmailId,
                        'realEmailId' => $realEmailId,
                    ];
                }
            }
            // 检查完, 如果没有需要修的, 就看下一批 customer
            if (empty($wrongCustomerRecords)) {
                echo "这一批不用修\n";
                continue;
            }

            // var_dump($wrongCustomerRecords);
            // todo 需要修的 customer 打个 log 留存一下
            $wrongCount = count($wrongCustomerRecords);
            echo "需要修复 $wrongCount 条数据\n";

            // 只展示一下有哪些要修, 但不要真的修, 仅供 check 一下
            if ($debug) {
                var_dump($wrongCustomerRecords);
                continue;
            }

            // 有问题的批量 update 修复
            // 修复 tbl_customer.email_id
            // 修复 tbl_email_relation.customer_id
            // 修复 tbl_mail.relate_company_flag
            foreach ($wrongCustomerRecords as $wc) {
                $email = $wc['email'];
                if (!empty($email)) {
                    $client_id = $wc['client_id'];
                    $customer_id = $wc['customer_id'];
                    common\library\email\Helper::updateCustomerEmailRelation($client_id, $customer_id, $email, true);
                } else {
                    $mysqlDb = \ProjectActiveRecord::getDbByClientId($client_id);
                    $this->repairCustomer_emptyEmail($adminDb, $pgDb, $mysqlDb, $client_id, $wc);
                }
            }


            echo "ok\n";
        }
    }

    // ./yiic-test temporary CompanyCardRepairForAll --numPerBatch=2000 --setIdsStr='10,14,15,200024' --debug=0
    // ./yiic-test temporary CompanyCardRepairForAll --numPerBatch=2000 --setIdsStr='103' --debug=0
    // 参数: debug, 传 0 就正常运行, 传 1 就不会修复 只打印需要修复的数据 先供检查
    public function actionCompanyCardRepairForAll($numPerBatch, $setIdsStr, $debug) {
        $pgSetIds = explode(',', $setIdsStr);

        foreach ($pgSetIds as $setId) {
            echo "pg db_set id: $setId\n";
            $pgDb = PgActiveRecord::getDbByDbSetId($setId);
            $this->repairByPgDb($pgDb, $numPerBatch, $debug);
        }

        echo "over\n";
    }


    /*
        ./yiic-test temporary EchoCommandsForRepairingCompanyCardByHost --numPerBatch=2000
    */
    public function actionEchoCommandsForRepairingCompanyCardByHost($numPerBatch) {
        // use ;
        $DbList = \common\library\account\service\DbService::getDbList(DbSet::TYPE_PGSQL);

        $map_host_setIds = [];

        foreach ($DbList as $dBSet) {
            $setId = $dBSet['set_id'];
            $host = $dBSet['host'];
            if (key_exists($host, $map_host_setIds)) {
                $map_host_setIds[$host][] = $setId;
            } else {
                $map_host_setIds[$host] = [$setId];
            }
        }

        foreach ($map_host_setIds as $host => $setIds) {
            $setIdsStr = join(',', $setIds);
            $cmd = "./yiic-test temporary CompanyCardRepairForAll --numPerBatch=$numPerBatch --setIdsStr='$setIdsStr' --debug=0";
            echo $cmd . "\n";
        }
    }



    public function udpateExposeFlagForOneDb($dbMysql, $startMailId=-1) {
        $numPerBatch = 2000;
        $batchIndex = 1;
        $totalUpdateNum = 0;
        $maxIdInLastBatch = $startMailId;
        while (true) {
            // echo "batchIndex: $batchIndex, numPerBatch: $numPerBatch, currentCustomerId: $maxIdInLastBatch\n";

            // 查一批
            $sql = "select mail_id, group_flag from tbl_async_mail_task where mail_id>$maxIdInLastBatch and group_flag in (2, 3, 4) order by mail_id limit $numPerBatch;";
            $taskList = $dbMysql->createCommand($sql)->queryAll();

            // 已经查不到了, 结束了
            if (count($taskList) === 0) {
                echo "该 db 处理完毕, 共处理记录条数: $totalUpdateNum\n";
                break;
            }

            // for next batch
            $batchIndex++;
            $maxIdInLastBatch = $taskList[count($taskList)-1]['mail_id'];
            // echo "查了一批, 当前的 maxId 是: $maxIdInLastBatch\n";

            $exposeSubTaskList = $taskList;

            $countToUpdate = count($exposeSubTaskList);
            // echo "这一批要刷新的子邮件数量: $countToUpdate\n";
            //
            if ($countToUpdate == 0) {
                continue;
            }
            $totalUpdateNum += $countToUpdate;

            // 刷新
            // update tbl_mail set status_bitmap_flag = status_bitmap_flag | 0b00000001 where mail_id in (xxx, xxx, xxx);
            $mailIds = array_column($exposeSubTaskList, 'mail_id');
            $mailIdStr = join(',', $mailIds);
            $sql = "update tbl_mail set status_bitmap_flag = status_bitmap_flag | 0b00000001 where mail_id in ($mailIdStr)";
            $dbMysql->createCommand($sql)->execute();
        }
    }

    // ./yiic-test temporary UpdateExposeFlagForAllDb --timeStamp='2023-11-28 00:00:01' --setIdsStr='3'
    // ./yiic-test temporary UpdateExposeFlagForAllDb --timeStamp='2023-11-28 00:00:01' --setIdsStr='3,9'

    // ./yiic-test temporary UpdateExposeFlagForAllDb --timeStamp='' --setIdsStr='3'
    // ./yiic-test temporary UpdateExposeFlagForAllDb --timeStamp='' --setIdsStr='3,9'
    public function actionUpdateExposeFlagForAllDb($setIdsStr, $timeStamp) {
        $setIds = [];
        if ($setIdsStr == '') {
            $sqlDbList = DbService::getDbList(DbSet::TYPE_MYSQL);
            foreach ($sqlDbList as $dBSet) {
                $setId = $dBSet['set_id'];
                $setIds[] = $setId;
            }
        } else {
            $setIds = explode(',', $setIdsStr);
        }

        foreach ($setIds as $setId) {
            echo "setId: $setId\n";
            $dbMysql = \ProjectActiveRecord::getDbByDbSetId($setId);

            $startMailId = -1;
            if ($timeStamp != '') {
                // 根据 time 确定从哪个 mailId 开始扫表
                // select * from tbl_async_mail_task where create_time > '2023-11-28 00:00:01' order by create_time limit 10;
                $sql = "select * from tbl_async_mail_task where create_time > '$timeStamp' order by create_time limit 10;";
                $taskList = $dbMysql->createCommand($sql)->queryAll();
                if (count($taskList) === 0) {
                    echo "这个 db 从指定时刻起到现在都没有产生过群发子邮件, 无需处理\n";
                    continue;
                }

                $startMailId = $taskList[0]['mail_id'];
            }

            echo "从 mail_id $startMailId 开始刷新\n";
            $this->udpateExposeFlagForOneDb($dbMysql, $startMailId);
        }
    }

    // ./yiic-test temporary  echoHostDbSetRefreshExposeFlag --timeStamp='2023-11-28 00:00:01'
        // ./yiic-test temporary UpdateExposeFlagForAllDb --timeStamp='2023-11-28 00:00:01' --setIdsStr='3'
        // ./yiic-test temporary UpdateExposeFlagForAllDb --timeStamp='2023-11-28 00:00:01' --setIdsStr='16'

    // ./yiic-test temporary  echoHostDbSetRefreshExposeFlag --timeStamp=''
        // ./yiic-test temporary UpdateExposeFlagForAllDb --timeStamp='' --setIdsStr='3'
        // ./yiic-test temporary UpdateExposeFlagForAllDb --timeStamp='' --setIdsStr='16'
    public function actionEchoHostDbSetRefreshExposeFlag($timeStamp) {
        $sqlDbList = DbService::getDbList(DbSet::TYPE_MYSQL);

        $map_host_setIds = [];

        foreach ($sqlDbList as $dBSet) {
            $setId = $dBSet['set_id'];
            $host = $dBSet['host'];
            if (key_exists($host, $map_host_setIds)) {
                $map_host_setIds[$host][] = $setId;
            } else {
                $map_host_setIds[$host] = [$setId];
            }
        }

        foreach ($map_host_setIds as $host => $setIds) {
            $setIdsStr = join(',', $setIds);
            $cmd = "./yiic-test temporary UpdateExposeFlagForAllDb --timeStamp='$timeStamp' --setIdsStr='$setIdsStr'";
            echo $cmd . "\n";
        }
    }


    public function udpateExposeFlagForClients($clientId, $debug) {
        $dbMysql = \ProjectActiveRecord::getDbByClientId($clientId);

        $numPerBatch = 2000;
        $batchIndex = 1;
        $totalUpdateNum = 0;
        $maxIdInLastBatch = -1;
        while (true) {
            // echo "batchIndex: $batchIndex, numPerBatch: $numPerBatch, currentCustomerId: $maxIdInLastBatch\n";

            // 查一批
            $sql = "select mail_id, group_flag from tbl_async_mail_task where client_id=$clientId and mail_id>$maxIdInLastBatch and group_flag in (2, 3, 4) order by mail_id limit $numPerBatch;";
            $exposeSubTaskList = $dbMysql->createCommand($sql)->queryAll();

            // 已经查不到了, 结束了
            if (count($exposeSubTaskList) === 0) {
                echo "client $clientId 处理完毕, 共处理记录条数: $totalUpdateNum\n";
                break;
            }

            // for next batch
            $batchIndex++;
            $maxIdInLastBatch = $exposeSubTaskList[count($exposeSubTaskList)-1]['mail_id'];
            // echo "查了一批, 当前的 maxId 是: $maxIdInLastBatch\n";

            $countToUpdate = count($exposeSubTaskList);
            // echo "这一批要刷新的子邮件数量: $countToUpdate\n";
            //
            if ($countToUpdate == 0) {
                continue;
            }

            // 只展示一下有哪些要改, 但不要真的改, 仅供 check 一下
            if ($debug) {
                var_dump($exposeSubTaskList);
                continue;
            }

            $totalUpdateNum += $countToUpdate;

            // 刷新
            // update tbl_mail set status_bitmap_flag = status_bitmap_flag | 0b00000001 where mail_id in (xxx, xxx, xxx);
            $mailIds = array_column($exposeSubTaskList, 'mail_id');
            $mailIdStr = join(',', $mailIds);
            $sql = "update tbl_mail set status_bitmap_flag = status_bitmap_flag | 0b00000001 where mail_id in ($mailIdStr)";
            $dbMysql->createCommand($sql)->execute();
        }
    }

    // 执行数据变动
    //      ./yiic-test temporary udpateExposeFlagForClients --clientIdsStr='1'
    //      ./yiic-test temporary udpateExposeFlagForClients --clientIdsStr='1,2'
    // 把要变动的打印出来, 但不执行
    //      ./yiic-test temporary udpateExposeFlagForClients --clientIdsStr='1,2' --debug=1
    public function actionUdpateExposeFlagForClients($clientIdsStr, $debug=false) {
        $clientIds = explode(',', $clientIdsStr);

        foreach ($clientIds as $clientId) {
            $this->udpateExposeFlagForClients($clientId, $debug);
        }
    }


    /*
        从头开始跑
            ./yiic-test nanrui SplitItemSettingHistoryForTypeClientSetting

        从 id 3465205537 开始续跑
            ./yiic-test nanrui SplitItemSettingHistoryForTypeClientSetting --startId=3465205537
    */
    public function actionSplitItemSettingHistoryForTypeClientSetting($setIdsStr, $startId=-1) {
        $setIds = [];
        if ($setIdsStr == '') {
            $sqlDbList = DbService::getDbList(DbSet::TYPE_MYSQL);
            foreach ($sqlDbList as $dBSet) {
                $setId = $dBSet['set_id'];
                $setIds[] = $setId;
            }
        } else {
            $setIds = explode(',', $setIdsStr);
        }

        foreach ($setIds as $setId) {
            echo "setId: $setId\n";
            $dbMysql = \ProjectActiveRecord::getDbByDbSetId($setId);

            // 先算个总数, 进度展示需要
            $type = ItemSettingHistoryCompare::SETTING_CRM_CLIENT_SETTING_CHANGE;
            $sql = "select count(*) from tbl_item_setting_history where id>$startId and type=$type and refer_key='' order by id;";
            $countTotalCount = $dbMysql->createCommand($sql)->queryScalar();
            $countTotalCount = intval($countTotalCount);
            if ($countTotalCount <= 0) {
                echo "没有数据需要处理\n";
                return;
            }
            // var_dump($countTotalCount);

            $numPerBatch = 2000;
            $batchIndex = 1;
            $alreadyProcessNum = 0;
            $maxIdInLastBatch = $startId;
            while (true) {
                echo "batchIndex: $batchIndex, numPerBatch: $numPerBatch, currenMaxId: $maxIdInLastBatch\n";

                // 查出一批 type 是 400 的历史记录
                $type = ItemSettingHistoryCompare::SETTING_CRM_CLIENT_SETTING_CHANGE;
                // 因为过程中会一直拆分产生新的 tbl_item_setting_history 记录, 这些新记录不应该被匹配到,
                // 这些记录的 refer_key 都不为空, 所以指定 refer_key=''
                $sql = "select * from tbl_item_setting_history where id>$maxIdInLastBatch and type=$type and refer_key='' order by id limit $numPerBatch;";
                $records = $dbMysql->createCommand($sql)->queryAll();

                // 已经查不到了, 结束了
                if (count($records) === 0) {
                    echo "该 db 处理完毕, 共处理记录条数: $alreadyProcessNum\n";
                    break;
                }

                // for next batch
                $batchIndex++;
                $maxIdInLastBatch = $records[count($records)-1]['id'];

                $countToProcess = count($records);
                echo "这一批要处理的记录数量: $countToProcess\n";

                if ($countToProcess == 0) {
                    continue;
                }
                $alreadyProcessNum += $countToProcess;

                // 遍历每一条记录, 拆分成子记录
                $recordsNew = [];
                foreach ($records as $r) {
                    $diffs = json_decode($r['diff'], true);

                    // 目前 diff 里会同时记录多种设置的 diff, 把多种 diff 拆成多个单条记录
                    // 每条新纪录的 refer_key 设置好, diff 处理好, 其他的任何属性都保留
                    foreach ($diffs as $d) {
                        // 所有属性都先继承原记录
                        $rNew = $r;

                        // diff 需要改
                        $rNew['diff'] = $d;
                        // refer_id 需要记录
                        $id = $rNew['diff']['id'];
                        $rNew['refer_key'] = $id;
                        // diff 里的 id 不需要了, refer_id 已经描述了, 冗余了
                        unset($rNew['diff']['id']);
                        $recordsNew[] = $rNew;
                    }
                    // var_dump($recordsNew);
                }

                // 随便抽两个看看
                // var_dump($recordsNew[7]);
                // var_dump($recordsNew[50]);
                // var_dump(count($recordsNew));

                // test
                // $recordsTest = [$recordsNew[0], $recordsNew[1]];
                // $recordsNew = $recordsTest;

                // 批量把拆分好的子记录插入 db
                $sql = 'INSERT INTO tbl_item_setting_history (id, client_id, user_id, module, refer_id, type, diff, create_time, update_time, refer_key) VALUES ';
                $strs = [];
                foreach ($recordsNew as $r) {
                    $id = \ItemSettingHistory::produceAutoIncrementId();
                    $diffStr = json_encode($r['diff']);
                    $strs[] = "($id, {$r['client_id']}, {$r['user_id']}, {$r['module']}, {$r['refer_id']}, {$r['type']}, '{$diffStr}', '{$r['create_time']}', '{$r['update_time']}', '{$r['refer_key']}')";
                }
                $sql .= join(',', $strs);
                // var_dump($sql);
                $dbMysql->createCommand($sql)->execute();

                var_dump(number_format($alreadyProcessNum / $countTotalCount, 2, '.', ''));
            }

            echo "setId $setId 处理完毕\n";
        }
    }

    /*
        ./yiic-test nanrui EchoCmdSplitItemSettingHistoryForTypeClientSettingForHostDbSet
    */
    public function actionEchoCmdSplitItemSettingHistoryForTypeClientSettingForHostDbSet() {
        $sqlDbList = DbService::getDbList(DbSet::TYPE_MYSQL);

        $map_host_setIds = [];

        foreach ($sqlDbList as $dBSet) {
            $setId = $dBSet['set_id'];
            $host = $dBSet['host'];
            if (key_exists($host, $map_host_setIds)) {
                $map_host_setIds[$host][] = $setId;
            } else {
                $map_host_setIds[$host] = [$setId];
            }
        }
        foreach ($map_host_setIds as $host => $setIds) {
            $setIdsStr = join(',', $setIds);
            $cmd = "./yiic-test nanrui SplitItemSettingHistoryForTypeClientSetting --setIdsStr='$setIdsStr'";
            echo $cmd . "\n";
        }
    }


    // debugphp -d 'xdebug.client_port=9004' /data/codebase/bob/php-crm/protected/yiic-test temporary ProductDataMigrate --clientId=14119
    // debugphp -d 'xdebug.client_port=9004' /data/codebase/bob/php-crm/protected/yiic-test temporary ProductDataMigrate --grey=1 --greyNum=6 --skipClientId=xxx
    public function actionProductDataMigrate($clientId = null, $grey = 0, $greyNum = null, $skipClientId = 0, $startUpdateTime = '', $endUpdateTime = '', $multipleExecute = 1){
        $migrateFields = [
            "price_currency" => 'price_currency',
            "cost_currency" => "cost_currency",
            "price_min" => "price_min",
            "price_max" => "price_max",
            "quantity" =>"quantity",
            "fob_type" => "fob_type",
            "gradient_price" => "gradient_price",
            "count_per_package" => "count_per_package",
            "package_gross_weight" => "package_gross_weight",
            "package_remark" => "package_remark",
            "package_size_length" => "package_size_length",
            "package_size_weight" => "package_size_weight",
            "package_size_height" => "package_size_height",
            "package_volume" => "package_volume",
            "package_unit" => "package_unit",
            "product_net_weight" =>"product_net_weight",
            "product_size_length" => "product_size_length",
            "product_size_height" =>"product_size_height",
            "product_size_weight" => "product_size_weight",
            "product_volume" => "product_volume",
            "sku_external_field_data" => "external_field_data",
            'sku_remark' => 'product_remark',
            'sku_description' => 'description'
        ];

        self::info("ProductDataMigrate start" . PHP_EOL);
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey && !is_null($greyNum)) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        foreach ($clientIds as $clientId) {
            $skuCustomFields = [];
            if ($clientId < $skipClientId) {
                continue;
            }

            try {
                $spuIdSql = "select min(product_id) as min_product_id from tbl_product where client_id={$clientId}";
                $db = \PgActiveRecord::getDbByClientId($clientId);
                $minProductId = $db->createCommand($spuIdSql)->queryScalar();

                if(empty($minProductId)){
                    continue;
                }
                $minProductId = $minProductId - 1;
                $batchNum = 1000;
                $affectSkuRows = 0;
                $productTypeCond = "";
                if(!$multipleExecute){
                    $productTypeCond = " and product_type != 2 ";
                }

                // 查询包装、尺寸和价格group的自定义字段
                $fieldList = new FieldList($clientId);
                $fieldList->setType(\Constants::TYPE_PRODUCT);
                $fieldList->setBase(0);
                $fieldList->setEnableFlag(\Constants::ENABLE_FLAG_TRUE);
                $fieldList->setGroupId([CustomFieldService::PRODUCT_GROUP_PRICE, CustomFieldService::PRODUCT_GROUP_PACKAGE,CustomFieldService::PRODUCT_GROUP_SIZE]);
                $fieldList->setFields(['id']);
                $skuCustomFields = array_flip(array_column($fieldList->find(), 'id'));

                $updateTimeSql = "";
                if($startUpdateTime){
                    $updateTimeSql .= " and update_time >= '{$startUpdateTime}' ";
                }

                if($endUpdateTime){
                    $updateTimeSql .= " and update_time <= '{$endUpdateTime}' ";
                }

                do{
                    $spuSql = "select product_id,product_type,cost_type,price_currency,cost_currency,price_min,price_max,quantity,fob_type,gradient_price,count_per_package,package_gross_weight,package_remark,package_size_length,package_size_weight,package_size_height,package_volume,package_unit,product_net_weight,product_size_length,product_size_height,product_size_weight,product_volume,external_field_data,product_remark,description from tbl_product where client_id={$clientId} and product_id > {$minProductId} {$updateTimeSql} {$productTypeCond} order by product_id limit {$batchNum}";
                    $products = $db->createCommand($spuSql)->queryAll();

                    \LogUtil::info("new2 zbp_log, before migrateSku min_product_id:".$minProductId);
                    $affectSkuRows += $this->migrateSku($clientId, $products, $db, $migrateFields);
                    \LogUtil::info("new2 zbp_log, after migrateSku min_product_id:".$minProductId);
                    if(!empty($products)){
                        $minProductId = $products[count($products)-1]['product_id'];
                    }
                }while(!empty($products));

                \PgActiveRecord::releaseDbByClientId($clientId);
                \User::cleanUserMap();
                \common\library\account\Client::cleanCacheMap();

                $msg = "ProductDataMigrate, client id of {$clientId}, affect sku rows:".$affectSkuRows;
                echo $msg;
                echo PHP_EOL;
                \LogUtil::info($msg);
            } catch (\Throwable $e) {
                $errmsg = "Error:" . $e->getMessage() . " | File:" . $e->getFile() . " | Line:" . $e->getLine();
                echo $errmsg;
                echo PHP_EOL;
                self::info($errmsg);
                continue;
            }
        }
    }

    public function migrateSku($clientId, $products, $db, $migrateFields){
        $affectRows = 0;
        if(empty($products)){
            return 0;
        }

        $batchNum = 50;     // 再分片一下
        $productChunk = array_chunk($products, $batchNum);

        // 变更sku信息
        foreach($productChunk as $batchProduct){
            try{
                $updateSql = "update tbl_product_sku set ";
                $params = [];
                $updateProductId = array_column($batchProduct, 'product_id');

                \LogUtil::info("zbp_log, start iterate migrateFields");
                foreach($migrateFields as $skuField => $spuField){
                    $sqlPart = " {$skuField} = case ";
                    $fieldNeedUpdate = false;
                    foreach($batchProduct as $k => $product){
                        $fieldVal = $product[$spuField];
                        // 对于fob，如果产品类型为多规格且价格类型为按规格定价的话，就略过
                        if($product['product_type'] == ProductConstant::PRODUCT_TYPE_SKU){
                            if(in_array($skuField, ['price_min', 'price_max', 'gradient_price']) && $product['fob_type'] == ProductConstant::FOB_TYPE_SKU){
                                continue;
                            }
                        }

                        // external_field_data数据需要挑出包装、尺寸和价格的自定义字段
                        if($spuField == 'external_field_data' && !empty($skuCustomFields)){
                            $spuCustomFields = json_decode($fieldVal, true);
                            if(is_array($spuCustomFields)){
                                foreach($spuCustomFields as $spuCustomField => $customFieldVal){
                                    if(!isset($skuCustomFields[$spuCustomField])){
                                        unset($spuCustomFields[$spuCustomField]);
                                    }
                                }
                                if(empty($spuCustomFields)){
                                    $fieldVal = '{}';
                                }else{
                                    $fieldVal = json_encode($spuCustomFields);
                                }
                            }else{
                                $fieldVal = '{}';
                            }
                        }

                        $paramKey = ':'.$skuField.'_'.$k;
                        $params[$paramKey] = $fieldVal;
                        $sqlPart .= " when product_id={$product['product_id']} then {$paramKey} ";
                        $fieldNeedUpdate = true;
                    }
                    $sqlPart .= " else {$skuField} end, ";

                    if(!$fieldNeedUpdate){
                        $sqlPart = "";
                    }
                    $updateSql .= $sqlPart;
                }

                $updateSql = rtrim($updateSql, ', ');
                $updateProductIdStr = implode(',', $updateProductId);
                $updateSql .= " where client_id={$clientId} and product_id in ({$updateProductIdStr}) ";   // 考虑到已被删除的产品也要迁移，以免用户从回收箱恢复产品后sku没有数据，这里不加 enable_flag=1

                $affectRows += $db->createCommand($updateSql)->execute($params);

            }catch(\Throwable $e){
                \LogUtil::info("method migrateSku error:". $e->getMessage()." | Line: ".$e->getLine());
            }
        }

        return $affectRows;
    }

    /*
        ./yiic-test temporary FixRelateCompanyFlagForCertainMails
    */
    public function actionFixRelateCompanyFlagForCertainMails() {
        $dbMysql = \ProjectActiveRecord::getDbByClientId(15504);
        $sql = "update tbl_mail set relate_company_flag=1 where mail_id in (5667723935731, 5636423781712, 5563866995209, 5532697858998, 5470289460559, 5151791187487);";
        $dbMysql->createCommand($sql)->execute();
    }

    /**
     * 功能字段设置统计
     * @param $clientId
     * @param $grey
     * @param $greyNum
     * @return void
     * @throws ProcessException
     */
    public function actionFunctionFieldSettingStats($clientId = 0, $grey = 0, $greyNum = 0)
    {
        ini_set("memory_limit", "2048M");
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey) {
            $greyNum = explode(",", $greyNum);
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        $topTenCount = [];
        $topData = [];
        $data = [];

        $functionTypeMap = [
            9=>'关联字段',
            11=>'公式字段',
            12=>'统计字段',
            23=>'引用字段',
        ];

        foreach ($clientIds as $clientId) {
            try {
                echo $clientId . PHP_EOL;
                $adminUserId = PrivilegeService::getInstance($clientId)->getAdminUserId();
                if (empty($adminUserId)) {
                    continue;
                }
                \User::setLoginUserById($adminUserId);

                $db = \ProjectActiveRecord::getDbByClientId($clientId);

                $sql = "select client_id,type,field_type,count(field_type) as count from tbl_custom_field where client_id={$clientId} and enable_flag=1  and type in (2,22)  and field_type in (9,23,11,12) group by type,field_type;";
                $result = $db->createCommand($sql)->queryAll(true);

                if (!empty($result)) {
                    foreach($result as $item){
                        $data[] = [
                            'client_id' => $item['client_id'],
                            'type' => $item['type'],
                            'field_type' => $functionTypeMap[$item['field_type']],
                            'count' => $item['count'],
                        ];


                        if(in_array($item['type'], [2,22])){
                            if(isset($topTenCount[$item['type']][$item['field_type']]) && count($topTenCount[$item['type']][$item['field_type']]) >= 20){
                                $minValue = min($topTenCount[$item['type']][$item['field_type']]);
                                if($item['count'] > $minValue){
                                    foreach($topData[$item['type']][$item['field_type']] as $topKey => $topDatum){
                                        if($topDatum['count'] == $minValue){
                                            unset($topData[$item['type']][$item['field_type']][$topKey]);
                                            continue;
                                        }
                                    }
                                    foreach($topTenCount[$item['type']][$item['field_type']] as $topTwentyKey => $topTwentyValue){
                                        if($topTwentyValue == $minValue){
                                            unset($topTenCount[$item['type']][$item['field_type']][$topTwentyKey]);
                                            continue;
                                        }
                                    }
                                    $topTenCount[$item['type']][$item['field_type']][] = $item['count'];
                                    $topData[$item['type']][$item['field_type']][] = [
                                        'client_id' =>$item['client_id'],
                                        'count' =>$item['count']
                                    ];
                                }
                            }else{
                                $topTenCount[$item['type']][$item['field_type']][] = $item['count'];
                                $topData[$item['type']][$item['field_type']][] = [
                                    'client_id' =>$item['client_id'],
                                    'count' =>$item['count']
                                ];
                            }

                        }
                    }
                }
            } catch (\Throwable $throwable) {
                \LogUtil::info('client_id' . $clientId . 'message' . $throwable->getMessage());
            }
            \common\library\account\Client::cleanCacheMap($clientId);
        }

        foreach($topData as $type => $fieldTypeItem) {
            foreach($fieldTypeItem as $fieldType => $topDatum){
                foreach($topDatum as $item){
                    $data[] = [
                        'client_id' => $item['client_id'],
                        'type' => $type,
                        'field_type' => $functionTypeMap[$fieldType],
                        'count' => $item['count'],
                    ];
                }
            }
        }

        $filename = '功能字段设置统计-' . date('YmdHis') . '.xlsx';

        $headerFieldName = [
            'client_id' => 'clientId',
            'type' => '模块',
            'field_type' => '功能类型',
            'count' => '数量',
        ];

        $file = new common\library\export_v2\ExportFile();
        $file->setFileHeader($headerFieldName);
        $file->setFileName($filename);
        $file->setFileType('excel');
        $file->setFreezePane('A2');
        $file->setData($data);
        $tempFile =  $file->getNewWriter()->writer();
        echo $tempFile;

    }




    /**
     * 功能字段字段类型统计
     * @param $clientId
     * @param $grey
     * @param $greyNum
     * @return void
     * @throws ProcessException
     */
    public function actionFunctionFieldTypeStats($clientId = 0, $grey = 0, $greyNum = 0)
    {
        ini_set("memory_limit", "2048M");
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey) {
            $greyNum = explode(",", $greyNum);
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        $topTenCount = [];
        $topData = [];
        $data = [];

        $functionTypeMap = [
            9=>'关联字段',
            11=>'公式字段',
            12=>'统计字段',
            23=>'引用字段',
        ];

        foreach ($clientIds as $clientId) {
            try {
                echo $clientId;
                $adminUserId = PrivilegeService::getInstance($clientId)->getAdminUserId();
                if (empty($adminUserId)) {
                    continue;
                }
                \User::setLoginUserById($adminUserId);

                $db = \ProjectActiveRecord::getDbByClientId($clientId);

                $sql = "select client_id,type,field_type,relation_field_type,count(relation_field_type) as count from tbl_custom_field where client_id={$clientId} and enable_flag=1  and type in (2,22) and field_type in (9,23,11,12) group by type,field_type,relation_field_type;";
                $result = $db->createCommand($sql)->queryAll(true);

                if (!empty($result)) {
                    foreach($result as $item){
                        $data[] = [
                            'client_id' => $item['client_id'],
                            'type' => $item['type'],
                            'field_type' => $functionTypeMap[$item['field_type']],
                            'relation_field_type' => $item['relation_field_type'],
                            'count' => $item['count'],
                        ];


                        if(isset($topTenCount[$item['type']][$item['field_type']][$item['relation_field_type']]) && count($topTenCount[$item['type']][$item['field_type']][$item['relation_field_type']]) >= 20){
                            $minValue = min($topTenCount[$item['type']][$item['field_type']][$item['relation_field_type']]);
                            if($item['count'] > $minValue){
                                foreach($topData[$item['type']][$item['field_type']][$item['relation_field_type']] as $topKey => $topDatum){
                                    if($topDatum['count'] == $minValue){
                                        unset($topData[$item['type']][$item['field_type']][$item['relation_field_type']][$topKey]);
                                        continue;
                                    }
                                }
                                foreach($topTenCount[$item['type']][$item['field_type']][$item['relation_field_type']] as $topTwentyKey => $topTwentyValue){
                                    if($topTwentyValue == $minValue){
                                        unset($topTenCount[$item['type']][$item['field_type']][$item['relation_field_type']][$topTwentyKey]);
                                        continue;
                                    }
                                }
                                $topTenCount[$item['type']][$item['field_type']][$item['relation_field_type']][] = $item['count'];
                                $topData[$item['type']][$item['field_type']][$item['relation_field_type']][] = [
                                    'client_id' =>$item['client_id'],
                                    'count' =>$item['count']
                                ];
                            }
                        }else{
                            $topTenCount[$item['type']][$item['field_type']][$item['relation_field_type']][] = $item['count'];
                            $topData[$item['type']][$item['field_type']][$item['relation_field_type']][] = [
                                'client_id' =>$item['client_id'],
                                'count' =>$item['count']
                            ];
                        }

                    }
                }
            } catch (\Throwable $throwable) {
                \LogUtil::info('client_id' . $clientId . 'message' . $throwable->getMessage());
            }
            \common\library\account\Client::cleanCacheMap($clientId);
        }

        foreach($topData as $type => $fieldTypeItem) {
            foreach($fieldTypeItem as $fieldType => $fieldTypeDatum){
                foreach($fieldTypeDatum as $relationFieldType => $topDatum){
                    foreach($topDatum as $item){
                        $data[] = [
                            'client_id' => $item['client_id'],
                            'type' => $type,
                            'field_type' => $functionTypeMap[$fieldType],
                            'relation_field_type' => $relationFieldType,
                            'count' => $item['count'],
                        ];
                    }
                }
            }
        }

        $filename = '功能字段设置统计-' . date('YmdHis') . '.xlsx';

        $headerFieldName = [
            'client_id' => 'clientId',
            'type' => '模块',
            'field_type' => '功能类型',
            'relation_field_type' => '关联字段类型',
            'count' => '数量',
        ];

        $file = new common\library\export_v2\ExportFile();
        $file->setFileHeader($headerFieldName);
        $file->setFileName($filename);
        $file->setFileType('excel');
        $file->setFreezePane('A2');
        $file->setData($data);
        $tempFile =  $file->getNewWriter()->writer();
        echo $tempFile;

    }

}