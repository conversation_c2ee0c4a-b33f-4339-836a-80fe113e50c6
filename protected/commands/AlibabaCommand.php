<?php
/**
 * Created by PhpStorm.
 * User: cayley
 * Date: 2020/9/11
 * Time: 7:14 PM
 */

use common\library\account\Client;
use common\library\account\UserList;
use common\library\alibaba\AlibabaService;
use common\library\alibaba\Constant;
use common\library\alibaba\customer\AlibabaCustomerSyncProcessor;
use common\library\alibaba\customer\CustomerSyncHelper;
use common\library\alibaba\oauth\AlibabaAccount;
use common\library\alibaba\oauth\AlibabaAccountService;
use common\library\alibaba\order\AlibabaOrderRelationList;
use common\library\alibaba\order\AlibabaOrderService;
use common\library\alibaba\order\AlibabaOrderSyncHelper;
use common\library\alibaba\order\AlibabaOrderSyncProcessor;
use common\library\alibaba\product\AlibabaProductSyncExecutor;
use common\library\alibaba\product\AlibabaProductSyncSetting;
use common\library\alibaba\product\AlibabaProductSyncTask;
use common\library\alibaba\services\AlibabaOrder;
use common\library\alibaba\services\AlibabaOrderLogUtil;
use common\library\alibaba\services\AlibabaTopClient;
use common\library\alibaba\services\message\DelayMessageService;
use common\library\alibaba\upload\AlibabaUpload;
use common\library\async_task\AsyncTask;
use common\library\cash_collection\CashCollection;
use common\library\alibaba\services\queue\QueueHelper;
use common\library\alibaba\store\AlibabaStore;
use common\library\alibaba\trade\AlibabaChatSummary;
use common\library\customer_v3\company\CompanyService;
use common\library\customer_v3\company\orm\CompanyMetadata;
use common\library\io_handler\customize_handler\CustomizeTask;
use common\library\io_handler\IOTaskPool;
use common\library\notification\PushHelper;
use common\library\object\field\service\ScopeUserFieldService;
use common\library\oms\cash_collection\CashCollectionFilter;
use common\library\oms\common\OmsConstant;
use common\library\oms\order\Order;
use common\library\oms\order_link\trigger\CashCollectionTrigger;
use common\library\privilege_v3\Helper;
use common\library\privilege_v3\PrivilegeConstants;
use common\library\privilege_v3\PrivilegeService;
use common\library\server\es_search\SearchQueueService;
use common\library\util\PgsqlUtil;
use common\library\util\XlsUtil;

class AlibabaCommand extends  CrontabCommand
{
    
    
    public function actionAssignBasic($clientIds) {
        
        if ($clientIds) {
            
            $clientIds = explode(',', $clientIds);
        } else {
            
            $clientIds = array_column($this->getClientList(), 'client_id');
        }
        
        $db = \Yii::app()->db;
        
        $systems = Helper::getConfig();
    
        foreach ($clientIds as $clientId) {
            
            $db = ProjectActiveRecord::getDbByClientId($clientId);
            
            if (empty($db)) {
                self::info(("[{$clientId}] empty db continue"));
                continue;
            }
            
            $db = PgActiveRecord::getDbByClientId($clientId);
            
            if (empty($db)) {
                self::info(("[{$clientId}] empty db continue"));
                continue;
            }
            
            $adminUserId = PrivilegeService::getInstance($clientId)->getAdminUserId();
            
            if (empty($adminUserId)) {
                
                self::info(("[{$clientId}][{$adminUserId}] empty adminUserId continue'"));
                continue;
            }
            
            $client = new \common\library\account\Client($clientId);
            
            if ($client->isNew() || $client->mysql_set_id == 0) {
                
                LogUtil::info("clientId:{$clientId},client not exist or mysql_set_id = 0");
                continue;
            }
            
            self::info(("[{$clientId}][{$adminUserId}] start"));
            
            
            $privilege = PrivilegeService::getInstance($clientId);
            
    
            if (empty($systems[$privilege->getMainSystemId()]['functional'][PrivilegeConstants::FUNCTIONAL_ALI_BASIC]) || $privilege->hasFunctional(PrivilegeConstants::FUNCTIONAL_ALI_BASIC)) {
    
                self::info("clientId:{$clientId},no Privilege or already done,".$privilege->getMainSystemId());
                continue;
            }
            
            $privilege->assignFunction(PrivilegeConstants::FUNCTIONAL_ALI_BASIC);
    
            self::info(("[{$clientId}][{$adminUserId}] end,".(int)$privilege->hasFunctional(PrivilegeConstants::FUNCTIONAL_ALI_BASIC)));
        }
        
        self::info(("all done"));
        
    }
    
    /**
     * 阿里对接消费者服务
     * 日志:
     * console_log_alibaba_alibabaconsumer(总日志)
     * consumer_log_alibabaconsumer_xxxx(具体进程日志, 日志服务器没有consumer_log_前缀)
     */
    public function actionAlibabaConsumer()
    {
        $consumer = new \common\library\alibaba\services\AlibabaConsumer();

        if(\Yii::app()->params['env'] == 'grey')
        {
            if (getenv('CONSUMER_LIMITER') == 'true') {
                $limiter = new \common\library\alibaba\services\queue\ClientLimiter(true);
                if (getenv('CONSUMER_DEMOTION_LIMITER') == 'true') {
                    $decay = intval(getenv('CONSUMER_DEMOTION_LIMITER_DECAY') ?: 5);
                    $maxTokens = intval(getenv('CONSUMER_DEMOTION_LIMITER_MAX_TOKENS') ?: 300);
                    $limiter->addHandler((new \common\library\alibaba\services\queue\DemotionLimiterHandler(true))->setIsGrey(true)->setDecay($decay)->setMaxTokens($maxTokens));
                }
                $consumer->setLimiter($limiter);
            }

            $consumer->startGrey();
        }else
        {
            if (getenv('CONSUMER_LIMITER') == 'true') {
                $limiter = new \common\library\alibaba\services\queue\ClientLimiter(true);
                $limiter->addHandler((new \common\library\alibaba\services\queue\GreyLimiterHandler(true))->setExcludeGrey(true));
                if (getenv('CONSUMER_DEMOTION_LIMITER') == 'true') {
                    $decay = intval(getenv('CONSUMER_DEMOTION_LIMITER_DECAY') ?: 5);
                    $maxTokens = intval(getenv('CONSUMER_DEMOTION_LIMITER_MAX_TOKENS') ?: 300);
                    $limiter->addHandler((new \common\library\alibaba\services\queue\DemotionLimiterHandler(true))->setDecay($decay)->setMaxTokens($maxTokens));
                }
                $consumer->setLimiter($limiter);
            }

            $consumer->start();
        }

    }
    
    public function actionAlibabaOfflineConsumer()
    {
        $consumer = new \common\library\alibaba\services\AlibabaOfflineConsumer();
        
        if(\Yii::app()->params['env'] == 'grey')
        {
            if (getenv('CONSUMER_LIMITER') == 'true') {
                $limiter = new \common\library\alibaba\services\queue\ClientLimiter(true);
                if (getenv('CONSUMER_DEMOTION_LIMITER') == 'true') {
                    $decay = intval(getenv('CONSUMER_DEMOTION_LIMITER_DECAY') ?: 5);
                    $maxTokens = intval(getenv('CONSUMER_DEMOTION_LIMITER_MAX_TOKENS') ?: 300);
                    $limiter->addHandler((new \common\library\alibaba\services\queue\DemotionLimiterHandler(true))->setIsGrey(true)->setDecay($decay)->setMaxTokens($maxTokens));
                }
                $consumer->setLimiter($limiter);
            }
            
            $consumer->startGrey();
        }else
        {
            if (getenv('CONSUMER_LIMITER') == 'true') {
                $limiter = new \common\library\alibaba\services\queue\ClientLimiter(true);
                $limiter->addHandler((new \common\library\alibaba\services\queue\GreyLimiterHandler(true))->setExcludeGrey(true));
                if (getenv('CONSUMER_DEMOTION_LIMITER') == 'true') {
                    $decay = intval(getenv('CONSUMER_DEMOTION_LIMITER_DECAY') ?: 5);
                    $maxTokens = intval(getenv('CONSUMER_DEMOTION_LIMITER_MAX_TOKENS') ?: 300);
                    $limiter->addHandler((new \common\library\alibaba\services\queue\DemotionLimiterHandler(true))->setDecay($decay)->setMaxTokens($maxTokens));
                }
                $consumer->setLimiter($limiter);
            }
            
            $consumer->start();
        }
        
    }
    
    
    public function actionStartForTest()
    {
        putenv("CONSUMER_LIMITER=true");
        putenv("CONSUMER_DEMOTION_LIMITER=true");
        $consumer = new \common\library\alibaba\services\AlibabaConsumer();
        $limiter = new \common\library\alibaba\services\queue\ClientLimiter(true);
        $excludeClientIds = [14367];
        $limiter->addHandler((new \common\library\alibaba\services\queue\GreyLimiterHandler(true))->enableDebug()->setExcludeGrey(true)->setExcludeClientIds($excludeClientIds));
        if (getenv('CONSUMER_DEMOTION_LIMITER') == 'true') {
            $decay = intval(getenv('CONSUMER_DEMOTION_LIMITER_DECAY') ?: 1);
            $maxTokens = intval(getenv('CONSUMER_DEMOTION_LIMITER_MAX_TOKENS') ?: 3);
            $limiter->addHandler((new \common\library\alibaba\services\queue\DemotionLimiterHandler(true))->enableDebug()->setDecay($decay)->setMaxTokens($maxTokens)->setExcludeClientIds($excludeClientIds));
        }
        $consumer->setLimiter($limiter);

        $processConfigs['processAlibabaCustomerPushQueue'] = ['worker_count' => 1,'process' => 'processAlibabaCustomerPushQueue'];
        $processConfigs['processDemotionClientQueue'] = ['worker_count' => 1,'process' => 'processDemotionClientQueue'];
        $consumer->setProcessConfigs($processConfigs);

        $consumer->start();
    }


    public function actionProductQueueCount()
    {
        var_dump(\common\library\alibaba\services\queue\QueueHelper::getProductQueueLength());
    }

    public function actionSetQueueGroup($group)
    {
        $client = \common\library\alibaba\services\AlibabaTopClient::getInstance();
        if( !empty(\common\library\alibaba\services\AlibabaTopClient::TOPIC_GROUP_MAP[$group]) )
        {
           $rsp = $client->routeGroupTmcMessage($group, \common\library\alibaba\services\AlibabaTopClient::TOPIC_GROUP_MAP[$group]);
           var_dump($rsp);
        }
    }

    /**
     * 启动脚本, 用户灰度期间使用
     * 线上环境使用的 , grey=0
     * omg环境使用的 , grey=1
     * @param int $grey
     * @param string $excludeClientIds
     * @param string $includeClientIds
     */
    public function actionAlibabaConsumerForLimit($grey=0, $excludeClientIds='', $includeClientIds='')
    {
        $excludeClientIds = array_values(array_filter(explode(',', $excludeClientIds))); //需要执行的client_id
        $includeClientIds = array_values(array_filter(explode(',', $includeClientIds))); //不需要支持的client_id
        $consumer = new \common\library\alibaba\services\AlibabaConsumer();
        if( !$grey )
        {
            $limiter = new \common\library\alibaba\services\queue\ClientLimiter(true);
            $limiter->addHandler(
                    (new \common\library\alibaba\services\queue\GreyLimiterHandler(true))
                        ->setExcludeGrey(true)
                        ->setExcludeClientIds($excludeClientIds)
                        ->setIncludeClientIds($includeClientIds)
            );
            if (getenv('CONSUMER_DEMOTION_LIMITER') == 'true') {
                $decay = intval(getenv('CONSUMER_DEMOTION_LIMITER_DECAY') ?: 30);
                $maxTokens = intval(getenv('CONSUMER_DEMOTION_LIMITER_MAX_TOKENS') ?: 600);
                $limiter->addHandler((new \common\library\alibaba\services\queue\DemotionLimiterHandler(true))
                    ->setDecay($decay)
                    ->setMaxTokens($maxTokens)
                    ->setExcludeClientIds($excludeClientIds)
                    ->setIncludeClientIds($includeClientIds)
                );
            }
            $consumer->setLimiter($limiter);
        }else
        {
            $processConfigs = $consumer->getProcessConfigs();
            //灰度一般不需要消费阿里的队列
            unset($processConfigs['processAlibabaAuthQueue']);
            unset($processConfigs['processAlibabaChatQueue']);
            unset($processConfigs['processAlibabaOrderQueue']);
            unset($processConfigs['processDelayQueue']);
            unset($processConfigs['processAlibabaCustomerPushQueue']);
            unset($processConfigs['processSyncTaskQueue']);
            $processConfigs['processCustomHandlerQueue'] = ['worker_count' => 40,'process' => 'processCustomHandlerQueue'];
            $consumer->setProcessConfigs($processConfigs);
        }

        $consumer->start();
    }

    public function actionExport()
    {
        try {
            ini_set("memory_limit", "8096M");
            $first_line = [
                'client_id' => 'client_id',
                'user_num' => '绑定了国际站账号user数',
                'company_num' => '成功关联阿里买家的客户数',
                'lead_num' => '成功关联阿里买家线索数',
            ];
            $sql = "select client_id,count(client_id) as total from tbl_alibaba_account where enable_flag = 1 group by client_id order by total desc";
            $alibabaAccountList  = \Yii::app()->db->createCommand($sql)->queryAll();
            $filename =  'AlibabaAccount_'. date("Ymd").'_'.time() . '.csv';
            $path = '/tmp/' . $filename;
            $fp = fopen($path, 'w');
            fputs($fp, (chr(0xEF) . chr(0xBB) . chr(0xBF)));//加上bom头告诉excel软件用utf8格式打开
            fputcsv($fp, $first_line);
            $failCount =0;
            foreach ($alibabaAccountList as $item){
                $clientId = $item['client_id'];
                $userNum = $item['total'];
                $db = ProjectActiveRecord::getDbByClientId($clientId);
                $sql = "select count(distinct(lead_id)) as lead_total, count(distinct(company_id))  as company_total from tbl_alibaba_trade_relation where client_id = {$clientId} and enable_flag = 1";
                $alibabaTrlation = $db->createCommand($sql)->queryRow();
                $row = [
                    'client_id' => $clientId,
                    'user_num' => $userNum,
                    'company_num' => $alibabaTrlation['company_total']??0,
                    'lead_num' => $alibabaTrlation['lead_total']??0,
                ];
                $res = fputcsv($fp, $row);
                if ($res == false) {
                    $failCount++;
                }
            }
            fclose($fp);
            echo "begin upload result \n";
            $msg = sprintf("export@End:path[%s] filename[%s]",$path, $filename);
            \LogUtil::info($msg);
            echo $msg;
        } catch (Exception $e) {
            $endMsg = sprintf("expor@Fail: error_msg[%s]",$e->getMessage());
            \LogUtil::info($endMsg);
            echo "$endMsg";
        }
    }

    /** 体验账号解绑阿里国际站
     * @param $clientId
     * @param $userId
     * @param $sellerAccountId
     */
    public function actionUnbindExpUser($clientId, $userId, $sellerAccountId)
    {
        \LogUtil::info("unbindExpUser clientId:{$clientId} userId:{$userId} sellerAccountId:{$sellerAccountId}");
        $alibabaAccount = new AlibabaAccount($clientId,$userId);
        $alibabaAccount = $alibabaAccount->loadBySellAccountId($sellerAccountId);
        $alibabaAccount->cancelBind();
        \common\library\exp\ExpAlibabaService::deleteAccountCache($sellerAccountId);
    }

    //合同过期的client，解除阿里账号解绑
    public function actionClientExpires($clientId=0)
    {
        $clientList =  $this->getClientList($clientId,false,1);

        foreach ($clientList as $client) {
            $clientObj = (new \common\library\account\Client())->loadByModel($client);
            if(in_array($clientObj->client_id,array(3,7,9,10))) continue;
            try
            {
                //过期时间
                $validToDate = strtotime($client->valid_to);
                $beforeYesterday = 86400*2;
                //client是2天前过期的，需要取消阿里国际站账号绑定
                if(time() > $validToDate  && ((time() - $validToDate) >= $beforeYesterday )){
                    self::info("cancelClientBind:{$clientObj->client_id} \n");
                    \common\library\alibaba\oauth\AlibabaAccountService::cancelClientBind($clientId);
                }
            }catch ( Exception $e)
            {
                LogUtil::error("update clientExpires:{$clientId} error: ".$e->getMessage());
            }


        }

    }

    /**
     * 手动取消阿里账号绑定（用于修复数据）
     * @param $clientId
     * @throws \Exception
     */
    public function actionCancelClientBind($clientId)
    {
        if (!$clientId) {
            throw new Exception('client empty');
        }
        $clientId = explode(',', $clientId);
        $clientList =  $this->getClientList($clientId);

        foreach ($clientList as $client) {
            try {
                if(time() > strtotime($client->valid_to)){
                    \common\library\alibaba\oauth\AlibabaAccountService::cancelClientBind($client->client_id);
                }
            } catch ( Exception $e) {
                LogUtil::error("update clientExpires:{$clientId} error: ".$e->getMessage());
            }
        }
    }

    public function actionCleanCache($clientId)
    {
        //清除店铺管理的缓存
        AlibabaService::getInstance($clientId)->delAlibabaStoreCache();
        self::info("{$clientId} clean store cache ");
        $userIds = array_column(UserInfo::model()->findAll('client_id='.$clientId),'user_id');
        foreach ($userIds as $userId )
        {
            AlibabaService::getInstance($clientId, $userId)->delAlibabaAccountCache();
            self::info("{$clientId} clean userId {$userId} cache ");
        }
    }


    /** 体验账号阿里IM询盘
     * @param $buyer_account_id
     * @param $buyer_email
     * @param $seller_account_id
     * @param $seller_account_email
     * @param $time_stamp
     * @param $business_id_encrypt
     * @param $buyer_account_id_encrypt
     * @param $business_type
     * @param $message_type
     * @param $business_id
     * @param $client_id
     * @param $op_user_id
     * @param $uuid
     * @param $trade_type
     * @throws Exception
     */
    public function actionHandleExpChatMessage($buyer_account_id,$buyer_email, $seller_account_id,$seller_account_email,$time_stamp, $business_id_encrypt,
                                               $buyer_account_id_encrypt, $business_type, $message_type, $business_id,$client_id, $op_user_id, $uuid,$trade_type)
    {
        $params = [

            'client_id' => $client_id,
            'op_user_id' =>  $op_user_id,
            'seller_account_id' => $seller_account_id,//卖家id
            'seller_account_email' => $seller_account_email,//卖email

            'buyer_account_id' => $buyer_account_id,//买家id
            'buyer_email' => $buyer_email,//买家邮箱
            'buyer_account_id_encrypt' => $buyer_account_id_encrypt,

            'time_stamp' => $time_stamp,//时间戳
            'business_id_encrypt' => $business_id_encrypt,//业务加密id
            'business_type' => $business_type,//业务类型
            'message_type' => $message_type,//消息类型
            'business_id' => $business_id,//业务id
            'uuid' => $uuid, //消息id
            'trade_type' => $trade_type,//1收到买家回复信息 2收到卖家回复信息
        ];
        $data = json_encode($params);
        \LogUtil::info("handleExpChatMessage params:{$data}");
        $trackService = new \common\library\alibaba\track\TrackService($params);
        $trackService->apply();
    }


    /** 刷新阿里询盘邮件邮箱
     * @param $clientId
     * @return bool
     * @throws CDbException
     * @throws ProcessException
     */
    public function actionRefreshMailTrade($clientId)
    {
        LogUtil::info(sprintf("refreshMailTradeStart:client_id[%s]", $clientId));
        $db = ProjectActiveRecord::getDbByClientId($clientId);
        $sql = "select *  from tbl_mail_trade where client_id = {$clientId}  and seller_email is null";
        $mailTradeList = $db->createCommand($sql)->queryAll();
        if(!$mailTradeList){
            self::info(sprintf("no need change:client_id[%s]", $clientId));
            return false;
        }
        $mailTradeIdList = array_column($mailTradeList,'mail_id');
        $mailTradeMaps = array_combine($mailTradeIdList,$mailTradeList);
        $mailTradeIdList = array_unique($mailTradeIdList);

        $mailIdStr = join(',',$mailTradeIdList);
        $sql = "select mail_id,receiver From tbl_mail where mail_id in($mailIdStr)";
        $mailList = $db->createCommand($sql)->queryAll();
        if(!$mailList){
            return false;
        }
        $insertValues = [];
        foreach ($mailList as $item){
            if(!isset($mailTradeMaps[$item['mail_id']])){
                continue;
            }
            $mailTrade = $mailTradeMaps[$item['mail_id']];
            $receiverList = $item['receiver'];
            $receiverList = explode(';',$receiverList);
            $receiver = [];
            foreach ($receiverList as $receiverItem){
                $receiverEmail = \EmailUtil::getPreFixAndEmail($receiverItem);
                $receiver[] = $receiverEmail[1];
            }
            $receiverStr = join(';',$receiver);
            $nowTime = date('Y-m-d h:i:s',time());
            $insertValues[] ="({$item['mail_id']},'{$mailTrade['sec_trade_id']}',{$mailTrade['client_id']},{$mailTrade['user_id']},{$mailTrade['user_mail_id']},".
                "{$mailTrade['type']},'{$item['create_time']}','{$nowTime}','{$mailTrade['trade_email']}','{$receiverStr}')";
        }
        if($insertValues){
            $sql = "INSERT IGNORE INTO tbl_mail_trade (mail_id,sec_trade_id,client_id,user_id,user_mail_id,type,create_time,update_time,trade_email,seller_email)
VALUES ".implode(',',$insertValues)." ON DUPLICATE KEY UPDATE `seller_email`=VALUES( `seller_email`)";
            $db = \ProjectActiveRecord::getDbByClientId($clientId);
            $affectedRows =  $db->createCommand($sql)->execute();
            self::info(sprintf("refreshMailTrade:client_id[%s] affectedRows[%s]", $clientId,$affectedRows));
        }

    }


    /** 刷新账号关联的store_id 信息
     * @param $clientId
     * @return bool
     * @throws CDbException
     * @throws ProcessException
     */
    public function actionRefreshStoreId($clientId){

        $db = ProjectActiveRecord::getDbByClientId($clientId);
        $pgDb = PgActiveRecord::getDbByClientId($clientId);

        $sql = "select seller_account_id,store_id from tbl_alibaba_account where client_id = {$clientId} AND store_id != 0 and seller_account_id !=0";
        $accountList = \Yii::app()->db->createCommand($sql)->queryAll();
        foreach ($accountList as $item) {

            $executeTradeCount =  $executeRelationCount = $executeLeadCount =0;
            $sql = "update  tbl_alibaba_trade set store_id = {$item['store_id']} where client_id = {$clientId} and seller_account_id ={$item['seller_account_id']} and store_id = 0";
            $executeTradeCount = $db->createCommand($sql)->execute();

            $sql = "update  tbl_alibaba_trade_relation set store_id = {$item['store_id']} where client_id = {$clientId} and seller_account_id ={$item['seller_account_id']} and store_id = 0";
            $executeRelationCount = $db->createCommand($sql)->execute();

            $sql = "select lead_id from tbl_alibaba_trade_relation where client_id = {$clientId} and seller_account_id = {$item['seller_account_id']} and lead_id !=0 ";
            $leadList = $db->createCommand($sql)->queryAll();
            if($leadList){
                $leadIds = array_column($leadList,'lead_id');
                $leadIdStr = join(',',$leadIds);
                $sql = "update tbl_lead set store_id = {$item['store_id']} WHERE  client_id = {$clientId} AND lead_id in($leadIdStr) and store_id = 0";
                $executeLeadCount = $pgDb->createCommand($sql)->execute();
            }
            if($executeTradeCount || $executeRelationCount || $executeLeadCount){
                self::info(sprintf("refreshStoreId:client_id[%s] seller_account_id[%s] store_id[%s] trade_count[%s]  relation_count[%s] lead_count[%s]",
                    $clientId, $item['seller_account_id'], $item['store_id'], $executeTradeCount, $executeRelationCount,$executeLeadCount));
            }
        }
    }

    public function actionRefreshAccountStatus(){
        $sql = "update tbl_alibaba_account set oauth_flag =1  where enable_flag = 1 and oauth_flag =0 and taobao_user_id = 0";
        $count = \Yii::app()->db->createCommand($sql)->execute();
        self::info("count:{$count}");
    }

    public function actionRefreshExpireTime($clientId){
        $sql = "update   tbl_alibaba_account set expire_time = unix_timestamp(bind_time)+7776000 where  client_id = '{$clientId}' and expire_time = 7776000";
        $count = \Yii::app()->db->createCommand($sql)->execute();
        if($count){
            self::info("refreshExpireTime:client_id:{$clientId} count:{$count}");
        }
        $this->actionCleanAlibabaCache($clientId);
    }


    //清除阿里巴巴绑定账号权限
    public function actionCleanAlibabaCache($clientId, $grey = 1, $expFlag = 0)
    {

        self::info('start');
        if ($clientId) {
            $clientIds = [$clientId];
        } elseif ($grey) {
            $clientIds = \common\library\GreyEnvHelper::getGreyClientIds();
        } elseif ($expFlag) {
            $clientIds = \common\library\account\Client::getExpClientIds();
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }
        $db = UserInfo::model()->getDbConnection();

        foreach ( $clientIds as $clientId )
        {
            $userIds = $db->createCommand('select user_id from tbl_user_info where client_id='.$clientId)->queryColumn();
            $redis = \RedisService::sf();
            $allKeys = [];
            foreach ( $userIds as $userId )
            {
                $redisKey = \common\library\alibaba\Constant::KEY_PREFIX_USER_ALIBABA_LOGIN.':{'.$clientId.'}:{'.$userId.'}';
                $data = $redis->get($redisKey);
                if($data !== null){
                    $allKeys[] = $redisKey;
                }
            }
            if($allKeys){
                $redis->del($allKeys);
                self::info('clean client:'.$clientId.' key count:'.count($allKeys));
            }

            \common\library\alibaba\cache\AlibabaAccountCacheableRepo::instance($clientId)->refreshCache();
        }


        self::info('finish');
    }

    public function actionFixAccountData(){
        LogUtil::info('fixAccountDataStart');
        $path = '/tmp/okki1113.csv';
        if (file_exists($path))
        {
            $data = XlsUtil::getExcelData($path);
            //除去头部字段
            array_shift($data);

            //除去空行
            foreach($data as $key => $item) {
                if(empty(array_filter($item))) {
                    unset($data[$key]);
                }
            }
            $accountList = [];
            foreach ($data as $row) {
                $accountList[] = [
                    'seller_account_id' => $row[0],
                    'seller_nickname' => $row[1],
                    'admin_seller_nickname' => $row[2],
                    'store_id' => $row[3],
                    'store_name' => $row[4]
                ];
            }
            foreach ($accountList as $item){
                if(!$item['seller_account_id']){
                    continue;
                }
                $isAadminFlag = $item['seller_nickname'] == $item['admin_seller_nickname'] ? \common\library\alibaba\Constant::ALI_ADMIN_FLAG:\common\library\alibaba\Constant::ALI_USER_FLAG;
                $sql = "update tbl_alibaba_account set seller_nickname ='{$item['seller_nickname']}',store_id = {$item['store_id']},store_name = '{$item['store_name']}',is_admin = '{$isAadminFlag}' where seller_account_id = {$item['seller_account_id']}";
                $count = \Yii::app()->db->createCommand($sql)->execute();
                if($count){
                    self::info("seller_account_id：{$item['seller_account_id']} count:{$count}");
                }
            }
        }else{
            self::info("no file data");
        }
        LogUtil::info('fixAccountDataFinish');

    }


    //阿里账号过期没有推送解绑
    public function actionUnbindAccountOauth(){

        // 账号绑定时间是以前的2021-1-14以前，并且目前是绑定+授权状态，阿里解绑不会通知，手动给用户解绑
        $sql = "select id,user_id,client_id,seller_account_id
                from tbl_alibaba_account
                where bind_time <'2021-01-14'
                and oauth_flag = 3
                and (unix_timestamp(bind_time)+86400*90) < unix_timestamp(now())
                order by id asc";
        $accountList = \Yii::app()->db->createCommand($sql)->queryAll();

        if(!$accountList){
            die('no account');
        }
        $clientIds = array_column($accountList,'client_id');
        $clientIds = array_unique($clientIds);
        $clientIds = array_values($clientIds);
        $alibabaIds = array_column($accountList,'id');

        $myfile = fopen("/tmp/unbindAccountIdOauth.txt", "w");
        fwrite($myfile, json_encode($alibabaIds));
        fclose($myfile);

        $myfile = fopen("/tmp/unbindAccountOauthData.txt", "w");
        fwrite($myfile, json_encode($accountList));
        fclose($myfile);

        $sql = "update  tbl_alibaba_account  set oauth_flag = 2
                where bind_time <'2021-01-14'
                and oauth_flag = 3
                and (unix_timestamp(bind_time)+86400*90) < unix_timestamp(now())";

        $afferRow = \Yii::app()->db->createCommand($sql)->execute();
        self::info("afferRow:{$afferRow} client_id:".json_encode($clientIds));
        //刷新权限
        foreach ($clientIds as $clientId){
            $this->actionCleanAlibabaCache($clientId);
        }
    }
    //阿里账号过期没有推送解绑
    public function actionUnbindExpireAccount(){

        // 账号绑定时间是以前的2021-1-14以前，并且目前是绑定状态，阿里解绑不会通知，手动给用户解绑
        $sql = "select id,user_id,client_id,seller_account_id
                from tbl_alibaba_account
                where bind_time <'2021-01-14'
                and oauth_flag = 1
                and (unix_timestamp(bind_time)+86400*90) < unix_timestamp(now())
                order by id asc";
        $accountList = \Yii::app()->db->createCommand($sql)->queryAll();

        if(!$accountList){
            die('no account');
        }
        $clientIds = array_column($accountList,'client_id');
        $clientIds = array_unique($clientIds);
        $clientIds = array_values($clientIds);
        $alibabaIds = array_column($accountList,'id');

        $myfile = fopen("/tmp/unbindAccountId.txt", "w");
        fwrite($myfile, json_encode($alibabaIds));
        fclose($myfile);

        $myfile = fopen("/tmp/unbindAccountData.txt", "w");
        fwrite($myfile, json_encode($accountList));
        fclose($myfile);

        $sql = "update  tbl_alibaba_account  set oauth_flag = 0
                where bind_time <'2021-01-14'
                and oauth_flag = 1
                and (unix_timestamp(bind_time)+86400*90) < unix_timestamp(now())";

        $afferRow = \Yii::app()->db->createCommand($sql)->execute();
        self::info("afferRow:{$afferRow} client_id:".json_encode($clientIds));
        //刷新权限
        foreach ($clientIds as $clientId){
            $this->actionCleanAlibabaCache($clientId);
        }
    }

    /** 刷数脚本
     * @param $clientId  单个client_id
     * @param int $grey  按照 main.php 配置的灰度名单
     * @param int $expFlag
     * @param string $module 执行的模块
     * @param int $startId 起始id
     * @param null $lastNumber 尾数执行
     * @throws CDbException
     * @throws Exception
     * @throws ProcessException
     */
    public function actionRefreshData($clientId, $grey = 1, $expFlag = 0,$module = '',$startId = 0,$lastNumber = null){

        if(!$module){
            die('传入需要处理的模型');
        }
        if ($clientId) {
            $clientIds = [$clientId];
        }else if($lastNumber !== null){
            $clientIds = array_column( $this->getClientList($clientId, false, null, null, 0, 0, $lastNumber), 'client_id');
        } elseif ($grey) {
            $clientIds = \common\library\GreyEnvHelper::getGreyClientIds();
        } elseif ($expFlag) {
            $clientIds = \common\library\account\Client::getExpClientIds();
        }else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }
        self::info("start refresh:{$module}");
        foreach ($clientIds as $clientId) {
            if(in_array($clientId,[2,3127])){
                continue;
            }
            if($startId && $startId > $clientId){
                self::info("skip {$clientId}");
                continue;
            }
            $modules = explode(',', $module);
            //AI字段线索自动化新增询盘类型
            if (in_array('lead_trade_behavior', $modules)) {
                $this->actionRefreshLeadTradeBehavior($clientId);
            }
            //刷新联系关系表数据
            if (in_array('chat_summary', $modules)) {
                $this->actionRefreshChatSummary($clientId);
            }
            if (in_array('clean_cache', $modules)) {
                $this->actionCleanAlibabaCache($clientId);
            }

            //重跑订单同步
            if (in_array('order_sync', $modules)) {
                $this->actionRetrySyncOrderStatusTask($clientId);
            }
            //补全回款单跟进人+user_id
            if (in_array('cash_collection', $modules)) {
                $this->actionRefreshCashCollection($clientId);
            }
            //重跑客户通映射关系
            if (in_array('customer_setting', $modules)) {

                \common\library\alibaba\Helper::refreshCustomerSyncSetting($clientId);
            }
        }
        self::info("finish");
    }

    public function actionCancelStoreAuth(){

        $db = \Yii::app()->db;
        $sql = "select store_id,oauth_flag,count(store_id) as total from tbl_alibaba_store  where  oauth_flag = 1 group by store_id,oauth_flag having total >1 order by  total desc";
        $storeList = $db->createCommand($sql)->queryAll();
        if(!$storeList){
            throw new RuntimeException('没有授权都在同一状态的店铺!');
        }

        $updateSqlList = [];
        $clientIds = [];
        foreach ($storeList as $item){
            $sql = "select * from tbl_alibaba_account where oauth_flag = {$item['store_id']} and is_admin  = 1";
            $accountList = $db->createCommand($sql)->queryAll();
            $flag = Constant::AUTH_TYPE_INVALID;
            //主账号不是绑定+授权状态，取消店铺授权
            foreach ($accountList as $accountItem){
                if($accountItem['oauth_flag'] != Constant::OAUTH_FLAG_BIND_AND_AUTH){
                    $updateSqlList[] =  "update tbl_alibaba_store set oauth_flag = {$flag},enable_flag = {$flag} WHERE client_id ={$accountItem['client_id']} and store_id = {$accountItem['store_id']}";;
                    $clientIds[] = $accountItem['client_id'];
                }
            }
        }
        if (!empty($updateSqlList))
        {
            $sql = join(';',$updateSqlList);
            $res = $db->createCommand($sql)->execute();

            $clientIds = array_unique($clientIds);
            foreach ($clientIds as $clientId) {
                $this->resetStoreCache($clientId);
            }

            self::info("cancelStoreAuth:{$sql} res:{$res} ");
        }


    }
    public function actionRefreshLeadTradeBehavior($clientId){
        $adminUserId = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();
        if(!$adminUserId){
            return false;
        }
        User::setLoginUserById($adminUserId);
        $applySettings = new \common\library\ai\classify\setting\ApplySettings($clientId);
        if($applySettings->isNew() ){
            return false;
        }
        $leadTradeBehavior = [];
        if(in_array( \common\library\ai\classify\setting\ApplySettings::ALIBABA_OTHER,$applySettings->lead_trade_behavior)){
            $leadTradeBehavior = [
                \common\library\ai\classify\setting\ApplySettings::ALIBABA_TRADE,
                \common\library\ai\classify\setting\ApplySettings::ALIBABA_EXCHANGE_CARD,
                \common\library\ai\classify\setting\ApplySettings::ALIBABA_OTHER
            ];
        }else{
            $leadTradeBehavior = [
                \common\library\ai\classify\setting\ApplySettings::ALIBABA_TRADE,
                \common\library\ai\classify\setting\ApplySettings::ALIBABA_EXCHANGE_CARD,
            ];
        }
        $applySettings->lead_trade_behavior = $leadTradeBehavior;
        $res = $applySettings->save();
        self::info("[client_id:{$clientId} res:{$res}]");

    }


    //排查阿里信宝订单与crm 订单差异
    public function actionAliDiffOrder($clientId,$storeId)
    {
        $alibabaStore = new \common\library\alibaba\store\AlibabaStore($clientId,$storeId);
        if($alibabaStore->isNew()|| !$alibabaStore->access_token){
            throw  new RuntimeException('店铺授权失效');
        }
        $db = PgActiveRecord::getDbByClientId($clientId);
        $sql = "select ali_order_id From tbl_order where client_id = {$clientId} and ali_store_id = {$storeId}";
        $orders = $db->createCommand($sql)->queryAll();
        $orderIds = array_column($orders,'ali_order_id');

        $beginTime = 0;
        $endTime = time();
        $orderList = new \common\library\alibaba\services\AlibabaOrderList($alibabaStore->access_token);
        $orderList->setCreateDateStart($beginTime);
        $orderList->setCreateDateEnd($endTime);
        $rsp = $orderList->find();
        $totalCount = $rsp['result']['value']['total_count']??0;
        $tradeIds = [];
        $page_size = 100;
        $pageCount = ceil($totalCount/$page_size);
        $pageCount = $pageCount > 0 ? $pageCount : 1;

        for ($i = 0;$i <= $pageCount; $i++) {
            $curPage = $i;
            $orderList = new \common\library\alibaba\services\AlibabaOrderList($alibabaStore->access_token);
            $orderList->setStartPage($curPage);
            $orderList->setPageSize($page_size);
            $orderList->setCreateDateStart($beginTime);
            $orderList->setCreateDateEnd($endTime);
            $rsp = $orderList->find();
            $dataList = $rsp['result']['value']['order_list']['trade_ecology_order']??[];
            foreach ($dataList as $item){
                $tradeIds[] = strval($item['trade_id']);
            }
        }

        $diff = array_diff($orderIds,$tradeIds);
        var_dump($diff);
    }

    public function actionRefreshCashCollectionStat($clientId)
    {
        $adminUserId = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();

        if(!$adminUserId){
            return false;
        }
        User::setLoginUserById($adminUserId);

        $db = PgActiveRecord::getDbByClientId($clientId);
        //阿里订单支付关系表
        $sql = "select order_id,cash_collection_id from tbl_alibaba_order_fund_relation where client_id =  {$clientId}";
        $orderFundRelationList = $db->createCommand($sql)->queryAll();

        if(!$orderFundRelationList){
            return false;
        }
        self::info("[client:{$clientId} start]");

        $cashCollectionIds = array_column($orderFundRelationList,'cash_collection_id');
        $cashCollectionIds = array_unique($cashCollectionIds);
        $cashCollectionIds = array_values($cashCollectionIds);
        self::info("[client:{$clientId}] cash_collect_count:".count($cashCollectionIds));

        $cashCollectionIdChunk = array_chunk($cashCollectionIds,50);

        foreach ($cashCollectionIdChunk as $num => $list){
            \common\library\cash_collection\Helper::refreshCashCollectionStatsByIds($clientId, $list);
            LogUtil::info("[refer client:{$clientId} num:{$num} total:".json_encode($cashCollectionIds));
        }

        self::info("[client:{$clientId} finish]");

    }

    //删除信宝订单重复的回款单
    public function actionDelRepeatCashCollection($clientId){
        $db = PgActiveRecord::getDbByClientId($clientId);
        //阿里订单支付关系表
        $sql = "select order_id,cash_collection_id from tbl_alibaba_order_fund_relation where client_id =  {$clientId}";
        $orderFundRelationList = $db->createCommand($sql)->queryAll();

        if(!$orderFundRelationList){
            return false;
        }
        self::info("[client:{$clientId} start]");

        $orderFundRelationMap = [];
        foreach ($orderFundRelationList as $item){
            $orderFundRelationMap[$item['order_id']][] = $item['cash_collection_id'];
        }
        $sqlList = [];
        $allCashCollectionIds = [];
        foreach ($orderFundRelationMap as $orderId => $cashCollectionId ){
            $sql = 'select cash_collection_id From tbl_cash_collection where client_id = '.$clientId.'  and order_id = '.$orderId. 'and cash_collection_id in ('.implode(',', $cashCollectionId).')';
            $cashCollectionList = $db->createCommand($sql)->queryAll()??0;
            if($cashCollectionList){
                $sqlList[] = 'delete from tbl_cash_collection WHERE client_id = '.$clientId.' AND order_id = '.$orderId.' and cash_collection_id not in ('.implode(',', $cashCollectionId).')';
                $cashCollectionIds =  array_column($cashCollectionList,'cash_collection_id');

                \common\library\cash_collection\Helper::refreshCashCollectionStatsByIds($clientId, $cashCollectionIds);

                self::info("[del order:{$orderId}] cash_collection_id:".json_encode($cashCollectionIds));
            }
        }

        if($sqlList){
            $sql = join(';',$sqlList);
            $deleteCount = count($sqlList);
            $res = $db->createCommand($sql)->execute();
            self::info("[client_id:{$clientId} deleteCount:{$deleteCount} res:{$res}]");
        }
    }


    public function actionDelRepeatOrder($clientId){

        //查询client 下绑定的店铺
        $sql = "select * from tbl_alibaba_store where client_id = {$clientId}";
        $storeList = \Yii::app()->db->createCommand($sql)->queryAll();
        $storeIds = array_column($storeList,'store_id');
        $storeIds = array_unique($storeIds);

        $db = PgActiveRecord::getDbByClientId($clientId);
        $sqlList = [];
        foreach ($storeIds as $storeId){
            $sql = "select ali_order_id,count(ali_order_id) as total From tbl_order
                      where client_id = $clientId
                      and ali_store_id = {$storeId}
                      group by ali_order_id
                      having count(ali_order_id) >1
                      order by total desc";
            $orderList = $db->createCommand($sql)->queryAll()??[];
            if(!$orderList){
                continue;
            }
            $aliOrderId = array_column($orderList,'ali_order_id');
            $sql = 'select order_id,alibaba_trade_id From tbl_alibaba_order_relation where client_id = '.$clientId.'  and store_id = '.$storeId. 'and alibaba_trade_id in ('.implode(',', $aliOrderId).')';
            $relationList = $db->createCommand($sql)->queryAll()??[];
            foreach ($relationList as $item){
                $sqlList[] = "delete from tbl_order WHERE client_id = {$clientId}  AND ali_order_id = '{$item['alibaba_trade_id']}' and ali_store_id = '{$storeId}' AND order_id != {$item['order_id']}";
            }
        }
        if($sqlList){
            $sql = join(';',$sqlList);
            $deleteCount = count($sqlList);
            $res = $db->createCommand($sql)->execute();
            self::info("[client_id:{$clientId} delete:{$deleteCount} res:{$res}]");
        }
    }


    //把询盘关联表数据刷新到聊天消息汇总表
    public function actionRefreshChatSummary($clientId){
        $sql = "select seller_account_id,store_id from tbl_alibaba_account where client_id = {$clientId}  group by seller_account_id";
        $accountList = \Yii::app()->db->createCommand($sql)->queryAll();
        $db = ProjectActiveRecord::getDbByClientId($clientId);

        $list = [];

        foreach ($accountList as $item) {
            $sql = "select buyer_account_id,buyer_email,store_id,seller_account_id,seller_email,count(buyer_account_id) as total
                        from tbl_alibaba_trade
                        where client_id =  {$clientId}
                        and seller_account_id =  {$item['seller_account_id']}
                        and store_id =  {$item['store_id']}
                        group by buyer_account_id";
            $tradeList = $db->createCommand($sql)->queryAll();
            if($tradeList){
                $list = array_merge($list,$tradeList);
            }
        }
        $nowTime = date('Y-m-d H:i:s',time());
        $insertArray = [];
        $updateSqlList = [];

        //聊天汇总把买家跟卖家id 查出来
        $alibabaChatSummaryList = new \common\library\alibaba\trade\AlibabaChatSummaryList($clientId);
        $chatSummaryList = $alibabaChatSummaryList->find();
        $sellerAndBuyerIdMaps = [];
        foreach ($chatSummaryList as $item){
            $key = "{$item['seller_account_id']}_{$item['buyer_account_id']}_{$item['store_id']}";
            $sellerAndBuyerIdMaps[$key] = $item['summary_id'];
        }
        foreach ($list as $item){
            $key = "{$item['seller_account_id']}_{$item['buyer_account_id']}_{$item['store_id']}";
            //如果买家+卖家+店铺，已经存在聊天，则更新
            if(($sellerAndBuyerIdMaps[$key]??[])){
                $updateSqlList[] =  "update tbl_alibaba_chat_summary set count = '{$item['total']}' WHERE summary_id ={$sellerAndBuyerIdMaps[$key]}";;
                continue;
            }
            $insertRow = [];
            $insertRow['summary_id'] = ProjectActiveRecord::produceAutoIncrementId();
            $insertRow['client_id'] = $clientId;
            $insertRow['seller_account_id'] = $item['seller_account_id'];
            $insertRow['seller_email']= "'{$item['seller_email']}'";
            $insertRow['buyer_account_id'] =  $item['buyer_account_id'];
            $insertRow['buyer_email'] =   "'{$item['buyer_email']}'";
            $insertRow['store_id'] =   "'{$item['store_id']}'";
            $insertRow['count'] =  $item['total'];
            $insertRow['enable_flag'] =  1;
            $insertRow['create_time'] =  "'{$nowTime}'";
            $insertRow['update_time'] = "'{$nowTime}'";
            $insertArray[] = $insertRow;
        }

        //重新插入
        if (!empty($insertArray))
        {
            $insert = "insert into tbl_alibaba_chat_summary (summary_id,client_id, seller_account_id,seller_email,buyer_account_id,buyer_email,store_id,count,enable_flag,create_time,update_time) VALUES ";
            foreach ($insertArray as $row){
                $rowStr = implode(',', $row);   //字段
                $insert .= "($rowStr),";
            }
            $insert = rtrim($insert, ',');
            $res = $db->createCommand($insert)->execute();
            $count = count($insertArray);
            self::info("insert summary:client_id:{$clientId} count:$count res:{$res} ");
        }
        if (!empty($updateSqlList))
        {
            $count = count($updateSqlList);
            $sql = join(';',$updateSqlList);
            $res = $db->createCommand($sql)->execute();
            self::info("update summer:client_id:{$clientId} count:$count res:{$res} ");
        }
    }

    //刷新阿里订单支付数据到支付关联表
    public function actionRefreshOrderFundRelation($clientId)
    {
        $adminUserId = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();
        if(!$adminUserId){
            return false;
        }
        $db = PgActiveRecord::getDbByClientId($clientId);
        $sql = "select  alibaba_trade_id,order_id,pay_step from tbl_alibaba_order_relation where client_id = {$clientId} and alibaba_trade_id !=0 and pay_step !='[]'";
        $list = $db->createCommand($sql)->queryAll();
        if(!$list){
            return false;
        }

        $sql = "select alibaba_trade_id,pay_step from tbl_alibaba_order_fund_relation where client_id = {$clientId}";
        $orderFundList = $db->createCommand($sql)->queryAll();
        $aliTradeIdMaps = [];
        foreach ($orderFundList as $item){
            $aliTradeIdMaps[$item['alibaba_trade_id']][] = $item['pay_step'];
        }

        $insertArray = [];
        $now = date('Y-m-d H:i:s',time());
        foreach ($list as $item)
        {
            $payStep = json_decode($item['pay_step'],true)??[];
            foreach ($payStep as $payItem){
                //信宝订单的支付方式已经同步过，不需要再次同步
                if(isset($aliTradeIdMaps[$item['alibaba_trade_id']]) &&  in_array($payItem['pay_step'],$aliTradeIdMaps[$item['alibaba_trade_id']])){
                    continue;
                }
                $insertArray[] = [
                    'relation_id' => ProjectActiveRecord::produceAutoIncrementId(),
                    'client_id' => $clientId,
                    'create_user_id' => $adminUserId,
                    'alibaba_trade_id' => $item['alibaba_trade_id'],
                    'order_id' => $item['order_id'],
                    'pay_step' => "'{$payItem['pay_step']}'",
                    'cash_collection_id' => $payItem['cash_collection_id'],
                    'create_time' => "'{$now}'",
                    'update_time' =>"'{$now}'",
                ];
            }
        }

        if ($insertArray)
        {
            $chunkList = array_chunk($insertArray,50);
            foreach ($chunkList as $num => $chunkItem){
                $insert = "insert into tbl_alibaba_order_fund_relation (relation_id,client_id, create_user_id,alibaba_trade_id,order_id,pay_step,cash_collection_id,create_time,update_time) VALUES ";
                foreach ($chunkItem as $row) {
                    if(!$row){
                        continue;
                    }
                    $rowStr = implode(',', $row);   //字段
                    $insert .= "($rowStr),";
                }
                $insert = rtrim($insert, ',');
                $res = $db->createCommand($insert)->execute();
                self::info("insert order fund:client_id:{$clientId} num:{$num} res:{$res}");
            }
        }
    }

    protected function buildOrderCostList($data){

        $costList = [];
        //运费
        $shipmentFeeAmount = isset($data['shipment_fee']) ? round($data['shipment_fee']['amount'],4):0;
        //物流保险费
        $shipmentInsuranceAmount = isset($data['shipment_insurance_fee'])?round($data['shipment_insurance_fee']['amount'],4):0;

        if($shipmentFeeAmount){
            $shipmentFeeData = [
                'cost' => $shipmentFeeAmount,
                'cost_name' => \common\library\alibaba\Constant::ALIBABA_SHIPMENT_FEE_NAME,
                'percent_type' => 0,
                'percent_amount' => $shipmentFeeAmount,
            ];
            $costList[] = $shipmentFeeData;

        }
        //物流保险费
        if($shipmentInsuranceAmount){
            $shipmentInsuranceData = [
                'cost' => $shipmentInsuranceAmount,
                'cost_name' => \common\library\alibaba\Constant::ALIBABA_SHIPMENT_INSURANCE_FEE_NAME,
                'percent_type' => 0,
                'percent_amount' => $shipmentInsuranceAmount,
            ];
            $costList[] = $shipmentInsuranceData;
        }

        return $costList;
    }


    //同步回款单
    public function actionRefreshOrderCash($clientId)
    {
        $sql = "select store_id,access_token from tbl_alibaba_store where client_id = {$clientId}";
        $storeList = \Yii::app()->db->createCommand($sql)->queryAll();
        $db = PgActiveRecord::getDbByClientId($clientId);

        foreach ($storeList as $storeItem) {

            //需要补全订单附加费用信息的订单
            $sql = "select order_id,ali_order_id,handler,users,exchange_rate,exchange_rate_usd,currency from tbl_order where client_id = {$clientId} and ali_store_id ={$storeItem['store_id']} and enable_flag = 1 ";
            $orderList = $db->createCommand($sql)->queryAll()??[];
            if(!$orderList){
                LogUtil::info("client_id {$clientId}:store_id{$storeItem['store_id']} no order.");
                continue;
            }
            $orderIds = array_column($orderList,'order_id');
            $orderIdMpas = array_combine($orderIds,$orderList);
            $orderIds = array_unique($orderIds);

            $orderIdStr = implode(',',$orderIds);

            //查询是否有回款单
            $sql = "select cash_collection_id,order_id from tbl_cash_collection where client_id = {$clientId} and  order_id in($orderIdStr) and enable_flag = 1";
            $cashCollectionList = $db->createCommand($sql)->queryAll();
            if(!$cashCollectionList){
                LogUtil::info("client_id {$clientId}:store_id{$storeItem['store_id']} no cash collection.");
                continue;
            }
            $successCount = 0;

            self::info("[syncStart:{$clientId} store_id:{$storeItem['store_id']}]");
            foreach ($cashCollectionList as $item){
                $cashCollectionId = $item['cash_collection_id'];
                $orderId = $item['order_id'];

                if(!$cashCollectionId || !$orderId || !($orderIdMpas[$orderId]??[]) ){
                    continue;
                }
                $users = json_decode($orderIdMpas[$orderId]['users'],true)??[];
                $userId = $users[0]['user_id']??0;
                if(!$userId){
                    continue;
                }
                $exchangeRate = $orderIdMpas[$orderId]['exchange_rate'];
                $exchangeRateUsd = $orderIdMpas[$orderId]['exchange_rate_usd'];
                $orderCurrency = $orderIdMpas[$orderId]['currency'];
                User::setLoginUserById($userId);
                $cashCollection = new \common\library\cash_collection\CashCollection($clientId,$cashCollectionId);
                if($cashCollection->isNew()){
                    continue;
                }
                $cashCollection->setUserId($userId);
                //回款单币种跟订单一致，汇率跟订单汇率不一致，同步订单汇率到回款单
                if($orderCurrency == $cashCollection->currency ){
                    if($exchangeRate !=$cashCollection->exchange_rate || $exchangeRateUsd != $cashCollection->exchange_rate_usd) {
                        $cashCollection->exchange_rate = $exchangeRate;
                        $cashCollection->exchange_rate_usd = $exchangeRateUsd;
                        $cashCollection->save();
                    }
                    $successCount++;
                    self::info("[order_id:{$orderId}}] cash_collection:{$cashCollectionId}   successCount:{$successCount}");
                }else{
                    $exchangeRateService = new \common\library\exchange_rate\ExchangeRateService($clientId);
                    $exchangeRate  = $exchangeRateService->cnyRateForCurrency($cashCollection->currency);
                    $exchangeRateUsd  = $exchangeRateService->usdRateForCurrency($cashCollection->currency);
                    $cashCollection->exchange_rate = $exchangeRate;
                    $cashCollection->exchange_rate_usd = $exchangeRateUsd;
                    $cashCollection->save();
                    $successCount++;
                    self::info("[order_id:{$orderId}}] cash_collection:{$cashCollectionId}   successCount:{$successCount}");
                }

            }
            self::info("[syncEnd:{$clientId} store_id:{$storeItem['store_id']}] referCount:{$successCount}");
        }

    }


    public function actionRefreshOrderCost($clientId)
    {
        $sql = "select store_id,access_token from tbl_alibaba_store where client_id = {$clientId} and enable_flag = 1 and access_token != '';";
        $storeList = \Yii::app()->db->createCommand($sql)->queryAll();
        $db = PgActiveRecord::getDbByClientId($clientId);

        foreach ($storeList as $storeItem) {

            //需要补全订单附加费用信息的订单
            $sql = "select count(*) from tbl_order where client_id = {$clientId} and ali_store_id ={$storeItem['store_id']}  and order_id !=0 and (cost_list ='{}' or cost_list ='[]')";
            $orderCount = $db->createCommand($sql)->queryScalar();
            if(!$orderCount){
                LogUtil::info("$clientId:no empty cost order.");
                continue;
            }
            self::info("[syncStart:{$clientId} store_id:{$storeItem['store_id']}] totalCount:{$orderCount}");
            $sql = "select order_id,ali_order_id,cost_list  from tbl_order where client_id = {$clientId} and ali_store_id ={$storeItem['store_id']}  and order_id !=0 and (cost_list ='{}' or cost_list ='[]')";
            $orderList = $db->createCommand($sql)->queryAll();
            $accessToken = $storeItem['access_token'];
            $totalCount = count($orderList);
            $successCount = 0;
            $orderCostList = [];

            foreach ($orderList as $orderItem){
                $orderId = $orderItem['order_id'];
                $aliOrderId = $orderItem['ali_order_id'];
                $aliOrder = new \common\library\alibaba\services\AlibabaOrder($accessToken,$aliOrderId);
                $data = $aliOrder->getInfo([\common\library\alibaba\services\AlibabaOrder::DATA_SELECT_STATUS_ACTION,\common\library\alibaba\services\AlibabaOrder::DATE_SELECT_DRAFT_ROLE]);
                if(empty($data['value']))
                {
                    $error = $aliOrder->getLastError();
                    \LogUtil::error("[client_id {$clientId}] ali_order_id: {$aliOrderId} get info error: ".json_encode($error));
                    continue;
                }
                $costList = $this->buildOrderCostList($data['value']);
                if($costList){
                    $orderCostList[] = ['order_id' => $orderId,'cost_list' => $costList];
                }else{
                    LogUtil::info("[noCostData:{$clientId} order_id:{$orderId} ali_order_id:{$aliOrderId}]");
                }
            }
            if($orderCostList){
                $orderChunkList = array_chunk($orderCostList,50);
                foreach ($orderChunkList as $num => $orderChunkItem){
                    $sqlList = [];
                    foreach ($orderChunkItem as $costItem){
                        $orderId = $costItem['order_id'];
                        $costList = json_encode($costItem['cost_list']??[]);
                        $sqlList[] = "update tbl_order set cost_list = '{$costList}' WHERE order_id ={$orderId}";
                    }
                    $sql = join(';',$sqlList);
                    $count = count($sqlList);
                    $db->createCommand($sql)->execute();
                    $successCount+=$count;
                    self::info("[updateOrderCost:{$clientId} store_id:{$storeItem['store_id']}] num:{$num} count:{$count}");
                }
            }

            self::info("[syncEnd:{$clientId} store_id:{$storeItem['store_id']}] totalCount:{$totalCount} sucsessCount:{$successCount}");
        }

    }

    public function actionRefreshOrderRelation($clientId){

        $sql = "select store_id from tbl_alibaba_store where client_id = {$clientId}";
        $storeList = \Yii::app()->db->createCommand($sql)->queryAll();
        $db = PgActiveRecord::getDbByClientId($clientId);


        foreach ($storeList as $storeItem) {

            $sql = "select relation_id,alibaba_trade_id,order_id  from tbl_alibaba_order_relation where client_id = {$clientId} and store_id = {$storeItem['store_id']}";
            $relationList = $db->createCommand($sql)->queryAll();
            if (!$relationList) {
                LogUtil::info("{$clientId}:{$storeItem['store_id']} empty alibaba relation");
                continue;
            }

            $sql = "select order_id,amount,currency,product_total_amount,fulfillment_channel,shipment_fee_currency,shipment_fee_amount,cost_list from tbl_order where client_id = {$clientId} and ali_store_id ={$storeItem['store_id']} order by order_id asc";
            $list = $db->createCommand($sql)->queryAll();
            $orderIdMaps = array_combine(array_column($list,'order_id'),$list);

            $chunkList = array_chunk($relationList,50);
            foreach ($chunkList as $number => $chunkItem){
                $sqlList = [];
                foreach ($chunkItem as $orderRelationItem){
                    if($orderIdMaps[$orderRelationItem['order_id']]??[]){
                        $orderId = $orderRelationItem['order_id'];
                        $orderInfo = $orderIdMaps[$orderRelationItem['order_id']];
                        $amount = $orderInfo['amount'];
                        $currency = $orderInfo['currency'];
                        $productTotalAmount = $orderInfo['product_total_amount']??0;
                        $productCurrency = "";
                        $shipmentFeeCurrency = "";
                        $shipmentInsuranceCurrency = "";
                        if($productTotalAmount){
                            $productCurrency = $currency;
                        }
                        $fulfillmentChannel = $orderInfo['fulfillment_channel']??"";
                        $shipmentFeeAmount = $orderInfo['shipment_fee_amount']??0;
                        $shipmentFeeCurrency = "";
                        if($shipmentFeeAmount){
                            $shipmentFeeCurrency =  $orderInfo['shipment_fee_currency']??"";
                        }
                        //物流保险费
                        $shipmentInsuranceFee = 0;
                        $shipmentInsuranceCurrency = 0;

                        $costList = json_decode($orderInfo['cost_list'],true)??[];
                        foreach ($costList as $costItem){
                            if($costItem['cost_name'] == \common\library\alibaba\Constant::ALIBABA_SHIPMENT_INSURANCE_FEE_NAME){
                                $shipmentInsuranceFee = $costItem['cost'];
                                $shipmentInsuranceCurrency = $currency;
                            }
                        }
                        $sqlList[] = "update tbl_alibaba_order_relation set amount= {$amount},currency='{$currency}',product_total_amount = {$productTotalAmount},product_currency = '{$productCurrency}'"
                            .",fulfillment_channel = '{$fulfillmentChannel}',shipment_insurance_fee = {$shipmentInsuranceFee},shipment_insurance_currency = '{$shipmentInsuranceCurrency}'"
                            .",shipment_fee_amount = {$shipmentFeeAmount},shipment_fee_currency = '{$shipmentFeeCurrency}'"
                            ."WHERE client_id = {$clientId} AND order_id = {$orderId}";
                    }
                }
                if($sqlList){
                    $sql = join(';',$sqlList);
                    $db->createCommand($sql)->execute();
                }
                self::info("[client_id:{$clientId} store_id:{$storeItem['store_id']}] number:{$number}  count:".count($sqlList));
            }
        }
    }

    //清除阿里账号重复的数据
    public function actionClearRepeatAccount()
    {
        $sql = "select seller_account_id,client_id,count(seller_account_id) as total from tbl_alibaba_account  group by client_id,seller_account_id  having total >1";
        $accountList = \Yii::app()->db->createCommand($sql)->queryAll()??[];
        if(!$accountList){
            die('没有需要删除的数据!');
        }
        $clientIds = array_column($accountList,'client_id');
        $deleteAccountIds = [];
        foreach ($accountList as $accountItem){
            $sql = "select id,seller_account_id,oauth_flag,update_time from tbl_alibaba_account where  client_id ={$accountItem['client_id']} and seller_account_id = {$accountItem['seller_account_id']} order by update_time desc ";
            $sellerAccountList = \Yii::app()->db->createCommand($sql)->queryAll()??[];
            $sellerIds = array_column($sellerAccountList,'id');

            $bindAccountIds = [];
            //获取是否有绑定状态的阿里账号id
            foreach ($sellerAccountList as $item){
                if(in_array($item['oauth_flag'],\common\library\alibaba\Constant::BIND_STATUS_MAP)){
                    $bindAccountIds[] =$item['id'];
                }
            }
            if($bindAccountIds){
                //删除非绑定状态的阿里账号id
                $diffIds = array_diff($sellerIds,$bindAccountIds);
                $deleteAccountIds = array_merge($deleteAccountIds,$diffIds);
            }else{
                //seller_account_id 都没有绑定，保留更新时间最新的id
                $deleteIds = $sellerIds;
                unset($deleteIds[0]);
                $deleteIds = array_values($deleteIds);
                $deleteAccountIds = array_merge($deleteAccountIds,$deleteIds);
            }
        }
        $deleteAccountIds = array_unique($deleteAccountIds);
        $deleteAccountIds = array_values($deleteAccountIds);
        if(!$deleteAccountIds){
            die('没有需要删除的数据!');
        }
        //分批删除
        $deleteChunkList = array_chunk($deleteAccountIds,50);
        foreach ($deleteChunkList as $num => $deleteAccountIds){
            $deleteAccountIdsStr = implode(',', $deleteAccountIds);
            $sql = "delete from tbl_alibaba_account where id in($deleteAccountIdsStr)";
            $res = \Yii::app()->db->createCommand($sql)->execute();
            self::info("clearRepeatAccount num:{$num} res:{$res} account_ids:".json_encode($deleteAccountIds));
        }

        //清除缓存
        foreach ($clientIds as $clientId){
            $this->actionCleanAlibabaCache($clientId);
        }

    }

    public function actionProgressProductTask($clientId){

        $beforeYesterday = date('Y-m-d',(time() - (86400*2)));
        //扫描出执行了2天还没执行完毕的任务
        $sql= "select task_id,user_id,store_id,create_time,update_time from tbl_alibaba_product_sync_task where client_id ={$clientId} and  status =2 and create_time < '{$beforeYesterday}' order by create_time asc";
        $db = ProjectActiveRecord::getDbByClientId($clientId);
        $storeList = $db->createCommand($sql)->queryAll();
        if(!$storeList){
            return false;
        }
        $storeIds = [];
        foreach ($storeList as $item){
            if(!in_array($item['store_id'],$storeIds)){
                self::info("inProgressProductTask client_id:{$clientId} store_id:{$item['store_id']} task_id:{$item['task_id']} create_time:{$item['create_time']} update_time:{$item['update_time']}");
            }
            //$sql = "update tbl_alibaba_product_sync_task set status = 4 where client_id ={$clientId} and store_id = {$item['store_id']} and status = 2";
            //$db->createCommand($sql)->execute();
        }

    }

    public function actionRefreshCashCollectionUsers($clientId)
    {
        $adminUserId = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();
        if(!$adminUserId){
            return false;
        }
        User::setLoginUserById($adminUserId);
        $db = PgActiveRecord::getDbByClientId($clientId);
        $sql = "select order_id,ali_order_id,users from tbl_order where client_id = {$clientId} and ali_order_id !=0 ";
        $orderList = $db->createCommand($sql)->queryAll()??[];
        if(!$orderList){
            return false;
        }
        echo "start refresh:{$clientId}\r\n";
        $orderIds = array_column($orderList,'order_id');
        $orderIdMaps = array_column($orderList,'users','order_id');
        $cashCollectionList =  new \common\library\cash_collection\CashCollectionList($clientId);
        $cashCollectionList->setOrderId($orderIds);
        $cashCollectionList->setSkipPermission(true);
        $cashCollectionList->setViewingUserId($adminUserId);
        $cashCollectionList->setFields(['cash_collection_id','user_id','users','order_id']);
        $list = $cashCollectionList->find();
        if(!$list){
            return false;
        }
        $insertCashCollectionIds = [];
        $insertOrderIds = [];
        foreach ($list as $item){
            $users = json_decode($item['users'],true);
            if(!$users && ($orderIdMaps[$item['order_id']])??0){
                $users = json_decode($orderIdMaps[$item['order_id']],true);
                $userId = $users[0]['user_id'];

                $cashCollection = new \common\library\cash_collection\CashCollection($clientId,$item['cash_collection_id']);
                $cashCollection->setUserId($userId);
                $cashCollection->users = $users;
                $res = $cashCollection->save();
                if($res){
                    $insertCashCollectionIds[] = $item['cash_collection_id'];
                    $insertOrderIds[] = $item['order_id'];
                }
            }
        }
        self::info("refreshCash:client_id:{$clientId} order_ids:".json_encode($insertOrderIds)."cash_ids:".json_encode($insertCashCollectionIds));
    }


    public function actionRefreshCashCollection($clientId)
    {

        $db = PgActiveRecord::getDbByClientId($clientId);
        $sql = "select * from tbl_alibaba_order_relation where client_id = {$clientId} and pay_step !='[]' and pay_step !='{}'";
        $relationList = $db->createCommand($sql)->queryAll();
        $orderIdMaps = [];
        if ($relationList) {
            echo "start refresh:{$clientId}\r\n";
            //获取订单关联信保信息
            foreach ($relationList as $item) {
                $payStepList = json_decode($item['pay_step'], true);
                foreach ($payStepList as $payStepItem) {
                    $orderIdMaps[$item['order_id']][] = $payStepItem['cash_collection_id'];
                }
            }
            if (!$orderIdMaps) {
                return false;
            }

            $orderIds = array_keys($orderIdMaps);
            $orderIdStr = implode(',', $orderIds);
            $sql = "select order_id,handler,company_id,departments From tbl_order where order_id in($orderIdStr)";
            $orderList = $db->createCommand($sql)->queryAll();
            $orderInfoMap = [];
            foreach ($orderList as $item) {
                $handler = \common\library\util\PgsqlUtil::trimArray($item['handler']);
                $departments = json_decode($item['departments'], true) ?? [];
                if (!$handler || !isset($handler[0]) || !$departments) {
                    continue;
                }
                $departmentIds = [];
                foreach ($departments as $departmentItem) {
                    $departmentIds[] = $departmentItem['department_id'];
                }
                $orderInfoMap[$item['order_id']]['company_id'] = $item['company_id'];
                $orderInfoMap[$item['order_id']]['handler'] = [$handler[0]];
                $orderInfoMap[$item['order_id']]['departments'] = $item['departments'];
                $orderInfoMap[$item['order_id']]['department_id'] = $departmentIds;
            }
            $sqlList = [];
            foreach ($orderIdMaps as $orderId => $cashCollectionIds) {
                if (!isset($orderInfoMap[$orderId])) {
                    continue;
                }
                foreach ($cashCollectionIds as $cashCollectionId) {
                    $departments = $orderInfoMap[$orderId]['departments'];
                    $userIds = \common\library\util\PgsqlUtil::formatArray(array_filter($orderInfoMap[$orderId]['handler']));
                    $departmentIds = \common\library\util\PgsqlUtil::formatArray(array_filter($orderInfoMap[$orderId]['department_id']));
                    $companyId = $orderInfoMap[$orderId]['company_id'] ?? 0;
                    $sqlList[] = "update tbl_cash_collection set company_id = $companyId,user_id ='{$userIds}',departments = '{$departments}',department_id = '{$departmentIds}' WHERE client_id = {$clientId} AND cash_collection_id = {$cashCollectionId}";
                }
            }
            if ($sqlList) {
                $chunkList = array_chunk($sqlList, 50);
                foreach ($chunkList as $number => $chunkItem) {
                    $sql = join(';', $chunkItem);
                    $res = $db->createCommand($sql)->execute();
                    self::info("[client_id:{$clientId}] number:{$number} res:{$res} count:" . count($sqlList));
                }
            }
        }
    }


    /** 修复同步订单状态设置为空的订单
     * @param $clientId
     * @return bool
     * @throws CDbException
     * @throws Exception
     * @throws ProcessException
     */
    public function actionRetrySyncOrderStatusTask($clientId){

        $sql = "select store_id from tbl_alibaba_order_sync_setting where client_id = {$clientId} and sync_order_status = '[]'   group by store_id";
        $storeList = \Yii::app()->db->createCommand($sql)->queryAll();
        if(!$storeList){
            return false;
        }
        foreach ($storeList as $item) {
            $sql = "select * from tbl_alibaba_store where client_id = {$clientId} and {$item['store_id']}  and enable_flag = 1 and access_token != ''";
            $storeData = \Yii::app()->db->createCommand($sql)->queryRow();
            if (!$storeData) {
                continue;
            }
            $accessToken = $storeData['access_token'];
            $userId = $storeData['create_user'];
            User::setLoginUserById($userId, $clientId);

            $sql = "select ali_order_id from tbl_order where client_id = {$clientId} and ali_store_id =  '{$item['store_id']}'  and enable_flag = 1";
            $orderList = PgActiveRecord::getDbByClientId($clientId)->createCommand($sql)->queryAll()??[];
            $orderTradeIds = array_column($orderList,'ali_order_id');
            $orderTradeIds = array_unique($orderTradeIds);

            $page = 0;//开始的页码
            $number = 0;
            $totalCount = 0;
            $successCount = 0;
            $listObj = new \common\library\alibaba\services\AlibabaOrderList($accessToken);
            $listObj->setPageSize(50);
            do
            {
                $begin = 0;
                $nowTime = time();
                $listObj->setCreateDateStart($begin);
                $listObj->setCreateDateEnd($nowTime);
                $listObj->setStartPage($page);
                $rsp =  $listObj->find();
                $rsp = is_object($rsp)? \common\library\alibaba\services\AlibabaTopClient::object2array($rsp) :$rsp;
                //订单列表
                $dataList = $rsp['result']['value']['order_list']['trade_ecology_order']??[];
                if( $page==0 )
                {
                    $totalCount = $rsp['result']['value']['total_count']??0;
                }
                $aliOrderListIds = array_column($dataList,'trade_id');
                \LogUtil::info("[client_id {$clientId} store_id {$item['store_id']}] sync_begin:{$begin},page:{$page},totalCount:{$totalCount}");
                foreach ( $aliOrderListIds as $aliOrderId)
                {
                    //没有才新增，如果有跳过不处理
                    if(!$orderTradeIds ||  !in_array($aliOrderId,$orderTradeIds)){
                        $number++;
                        $order = new \common\library\alibaba\services\AlibabaOrder($accessToken,$aliOrderId);
                        $data = $order->getInfo([\common\library\alibaba\services\AlibabaOrder::DATA_SELECT_STATUS_ACTION,\common\library\alibaba\services\AlibabaOrder::DATE_SELECT_DRAFT_ROLE]);
                        if(!isset($data['value'])){
                           continue;
                        }
                        $processor = new \common\library\alibaba\order\AlibabaOrderSyncProcessor($clientId,$userId,$item['store_id']);
                        $processor->setToken($accessToken);
                        $processor->processWithLock($aliOrderId,$data['value']);
                        $result = $processor->getResult();
                        $result['error'] = $processor->getError();
                        self::info("[client_id:{$clientId} aliOrder:{$aliOrderId}] sync order {$result['order_id']} res:".json_encode($result));
                        $successCount++;
                    }
                }
                $page++;
                \LogUtil::info("[client {$clientId}] sync_end:{$begin},page:{$page}, successCount:{$successCount}");

            }while(!empty($dataList));
        }

        self::info("retryTaskFinish");
    }


    public function actionRetryOrderTask($clientId){
        $sql = "select store_id from tbl_alibaba_order_sync_setting where client_id = {$clientId} group by store_id";
        $storeList = \Yii::app()->db->createCommand($sql)->queryAll();
        if(!$storeList){
            return false;
        }
        foreach ($storeList as $item) {
            $sql = "select * from tbl_alibaba_order_sync_task where client_id = {$clientId}  and {$item['store_id']} order by task_id desc limit 1";
            $db = ProjectActiveRecord::getDbByClientId($clientId);
            $taskData = $db->createCommand($sql)->queryRow();
            if (!$taskData) {
                continue;
            }
            $userId = $taskData['user_id'];
            User::setLoginUserById($userId, $clientId);
            $lastySyncTaskId = $taskData['task_id'];
            $task = new \common\library\alibaba\order\AlibabaOrderSyncTask($clientId, $lastySyncTaskId);
            $task->status = 1;
            $task->save();
            self::info(sprintf("retryTaskStart:client_id[%s] task_id[%s] store_id[%s]",
                $clientId, $lastySyncTaskId, $item['store_id']));

            $executor = new \common\library\alibaba\order\AlibabaOrderSyncTaskExecutor($task);
            $executor->setRefreshAllFlag(1);
            $executor->setRetry(1);
            $executor->run();
        }

        self::info("retryTaskFinish");
    }

    /**
     * 排查、执行未完成的订单同步任务
     * @param $clientId
     * @param $startClientId
     * @param $endClientId
     * @param $startTime
     * @param $endTime
     * @param $dryRun
     * @return void
     * @throws \CDbException
     * @throws \CException
     * @throws \ProcessException
     */
    public function actionRetryAllClientOrderTask($clientId = 0, $startClientId = 0, $endClientId = 0, $startTime = '', $endTime = '', $dryRun = 1)
    {
        $startTime = $startTime ?: '1970-01-01 00:00:00';
        $endTime = $endTime ?: date('Y-m-d H:i:s');
        $clientList = $this->getClientList($clientId, true, null, null, $startClientId, $endClientId);

        self::info("dryRun: {$dryRun}");
        $count = 0;
        foreach ($clientList as $client) {
            $clientId = $client['client_id'];
            $db = ProjectActiveRecord::getDbByClientId($clientId);

            self::info("client_id: {$clientId}");
            $sql = "select store_id from tbl_alibaba_order_sync_setting where client_id = {$clientId} group by store_id";
            $storeList = \Yii::app()->db->createCommand($sql)->queryAll();
            if(!$storeList)
                continue;

            foreach ($storeList as $item) {
                $sql = "select * from tbl_alibaba_order_sync_task
                    where client_id = {$clientId} and store_id={$item['store_id']} and create_time>='{$startTime}' and create_time<='{$endTime}'
                    order by task_id desc limit 1";
                $taskData = $db->createCommand($sql)->queryRow();
                if (!$taskData || !in_array($taskData['status'], [1, 2])) {
                    continue;
                }
                ++$count;
                self::info(sprintf("retryTask: client_id[%s] task_id[%s] store_id[%s] create_time[%s] update_time[%s]",
                    $clientId, $taskData['task_id'], $item['store_id'], $taskData['create_time'], $taskData['update_time']));
                if ($dryRun) {
                    continue;
                }

                $userId = $taskData['user_id'];
                User::setLoginUserById($userId, $clientId);
                $lastSyncTaskId = $taskData['task_id'];
                $task = new \common\library\alibaba\order\AlibabaOrderSyncTask($clientId, $lastSyncTaskId);
                $task->status = 1;
                $task->save();

                $executor = new \common\library\alibaba\order\AlibabaOrderSyncTaskExecutor($task);
                $executor->setRefreshAllFlag(1);
                $executor->setRetry(1);
                $executor->run();
            }
        }

        self::info("count: {$count}");
    }

    //重跑客户通映射关系
    public function actionRefreshCustomerSyncSetting($clientId){



    }


    public function actionRefreshStoreFunctional($clientId){

        $sql = "select * from tbl_alibaba_store where client_id = {$clientId} and access_token !=''";
        $storeList = \Yii::app()->db->createCommand($sql)->queryAll();
        foreach ($storeList as $item) {
            $sessionKey = $item['access_token'];
            $services = \common\library\alibaba\services\AlibabaTopClient::getInstance();
            $tradeDraftType = $services->getTradeDraftType($sessionKey);
            if(!$tradeDraftType){
                self::info(sprintf("functionalEmpty:client_id[%s] store_id[%s]",
                    $clientId, $item['store_id']));
                continue;
            }
            $functional = json_encode($tradeDraftType);;
            $alibabaStore = new \common\library\alibaba\store\AlibabaStore($item['client_id'],$item['store_id']);
            $alibabaStore->functional = $functional;
            $res = $alibabaStore->save();
            self::info(sprintf("refreshStoreFunctional:client_id[%s] store_id[%s] functional[%s] res[%s]",
                $clientId, $item['store_id'], $functional, $res));
        }
    }


    /** 修复订单产品表ali product id 为空
     * @param $clientId
     * @return bool
     * @throws CDbException
     * @throws ProcessException
     */
    public function actionRefreshAliProductId($clientId){

        $db = PgActiveRecord::getDbByClientId($clientId);
        $sql = "select distinct(product_id) from tbl_invoice_product_record WHERE client_id = {$clientId} AND type = 2 AND ali_product_id = 0";
        $invoiceProductIds = $db->createCommand($sql)->queryAll();
        if(!$invoiceProductIds){
            return false;
        }
        $invoiceProductIds = array_column($invoiceProductIds,'product_id');
        $invoiceProductIds = array_unique($invoiceProductIds);
        $productIds = implode(',',$invoiceProductIds);

        $sql = "select ali_product_id,product_id from tbl_product WHERE client_id = {$clientId} AND product_id in($productIds) AND ali_product_id !=0";
        $productList = $db->createCommand($sql)->queryAll();
        $chunkList = array_chunk($productList,1);

        foreach ($chunkList as $number => $chunkItem){
            $sqlList = [];
            foreach ($chunkItem as $item){
                $sqlList[] = "update tbl_invoice_product_record set ali_product_id  = {$item['ali_product_id']} WHERE client_id = {$clientId} AND product_id = {$item['product_id']} AND type =2 ";
            }
            $sql = join(';',$sqlList);
            $res = $db->createCommand($sql)->execute();
            self::info("[client_id:{$clientId}] number:{$number} res:{$res} count:".count($sqlList));
        }
    }


    public function actionClearTrail($clientId){

        $db = PgActiveRecord::getDbByClientId($clientId);
        $sql = "select count(*) From tbl_dynamic_trail_data where client_id = {$clientId} and type = 1402 and enable_flag = 1 and data->>'content' like '%<> 的询盘%';";
        $trailCount = $db->createCommand($sql)->queryScalar();
        if($trailCount){
            $sql = "select trail_id From tbl_dynamic_trail_data where client_id = {$clientId} and type = 1402 and enable_flag = 1 and data->>'content' like '%<> 的询盘%';";
            $trailList = $db->createCommand($sql)->queryAll();
            $trailIds = array_column($trailList,'trail_id');
            $trailIds = array_unique($trailIds);
            $trailListStr = implode(',',$trailIds);
            $sql = "update tbl_dynamic_trail set enable_flag = 0 where  client_id = {$clientId} and trail_id in($trailListStr)";
            $trailCount = $db->createCommand($sql)->execute();
            $sql = "update tbl_dynamic_trail_data set enable_flag = 0 where  client_id = {$clientId} and trail_id in($trailListStr)";
            $trailDataCount = $db->createCommand($sql)->execute();
            if($trailCount || $trailDataCount){
                self::info(sprintf("clearTrade:client_id[%s] trail_count[%s]  data_count[%s]",
                    $clientId,$trailCount,$trailDataCount));
            }
        }else{
            echo "skip:{$clientId}\r\n";
        }
    }


    public function actionSyncCustomer($store_id,$task_id,$client_id, $user_id, $retry=0)
    {
        ini_set("memory_limit", "1024M");
        \LogUtil::info("begin SyncCustomer : $store_id,$task_id,$client_id, $user_id, $retry");

        User::setLoginUserById($user_id, $client_id);

        $task = new \common\library\alibaba\customer\AlibabaCustomerSyncTask($client_id, $task_id);
        if( $task->isNew() )
        {
            throw new ProcessException('客户通同步任务不存在:'.$task_id);
        }

        if( $retry )
        {
            $task->status =1;
            $task->save();
        }

        $executor =  new \common\library\alibaba\customer\AlibabaCustomerSyncTaskExecutor($task);
        $executor->run();

        \LogUtil::info("end SyncCustomer : $store_id,$task_id,$client_id, $user_id, $retry");

    }

    // ./yiic-test alibaba syncSingleOrder --clientId=14119 --userId=11858712 --storeId=257429370 --alibabaOrderId=234308510001028893
    public  function actionSyncSingleOrder($clientId, $userId, $storeId=0, $alibabaOrderId=0, $orderId=0)
    {
        User::setLoginUserById($userId, $clientId);
        LogUtil::info("订单同步开始 {$clientId} {$alibabaOrderId}");


        $result =  \common\library\alibaba\order\AlibabaOrderService::syncSingleOrder($clientId, $userId, $storeId,$alibabaOrderId, $orderId);

        LogUtil::error("订单同步完成 {$clientId} {$orderId} result:".json_encode($result));

    }
    /** 同步阿里订单
     * @param $store_id 同步店铺id
     * @param $task_id 同步任务id
     * @param $client_id
     * @param $user_id 同步人id
     * @param int $refresh_all_flag 是否全量同步 0：只同步有更新订单 1:同步指定日期创建的订单
     * @param int $retry 1重置任务状态
     * @throws Exception
     * @throws ProcessException
     */
    public function actionSyncOrder($store_id,$task_id,$client_id, $user_id,$refresh_all_flag =0,$retry=0)
    {
        ini_set("memory_limit", "1024M");
        \LogUtil::info("begin SyncOrder : $store_id,$task_id,$client_id, $user_id");

        User::setLoginUserById($user_id, $client_id);

        $task = new \common\library\alibaba\order\AlibabaOrderSyncTask($client_id, $task_id);
        if( $task->isNew() )
        {
            throw new ProcessException('阿里订单同步任务不存在:'.$task_id);
        }

        if( $retry )
        {
            $task->status =1;
            $task->save();
        }

        $executor =  new \common\library\alibaba\order\AlibabaOrderSyncTaskExecutor($task);
        $executor->setRefreshAllFlag($refresh_all_flag);
        $executor->run();

        \LogUtil::info("finish SyncOrder : $store_id,$task_id,$client_id, $user_id");
    }

    /**
     * 同步外汇明细任务
     * time: 4:58 PM
     * user: huagongzi
     * @param $client_id
     * @param $store_id
     * @param $task_id
     * @param $user_id
     * @param $refresh_all_flag
     * @param $retry
     * @return void
     * @throws ProcessException
     */
    public function actionSyncFundDetail($client_id, $store_id, $user_id, $task_id, $refresh_all_flag = 0, $retry = 0){
        ini_set("memory_limit", "1024M");
        $logInfo = [
            "task_step"         => "start_sync_fund_detail_start",
            "task_name"         => "syncFundDetail",
            "client_id"         => $client_id,
            "store_id"          => $store_id,
            "task_id"           => $task_id,
            "user_id"           => $user_id,
            "refresh_all_flag"  => $refresh_all_flag,
            "retry"             => $retry
        ];
        \LogUtil::info(json_encode($logInfo));

        User::setLoginUserById($user_id, $client_id);

        $taskSetting           = new \common\library\acp\AlibabaSyncTask($client_id, $store_id, $task_id, $user_id);
        $cashCollectionSetting = new \common\library\acp\AlibabaCashCollectionDetailSyncSetting($client_id, $store_id);

        if($taskSetting->isNew() || $cashCollectionSetting->isNew()){
            throw new ProcessException("外汇明细任务或外汇明细开关配置不存在,task_id:{$task_id}");
        }

        if($retry){
            $taskSetting->status = \common\library\acp\Constant::ACP_DATA_SYNC_TASK_FLAG_START;
        }

        $taskSetting->type = \common\library\acp\AlibabaSyncTask::TYPE_ACP_FUND_DETAIL;
        $taskSetting->save();

        $executor = new \common\library\acp\executors\AlibabaSyncFundDetailTaskExecutor($taskSetting);
        $executor->setRefreshAllFlag($refresh_all_flag);
        $executor->setRetry($retry);
        $executor->run();

        $logInfo = [
            "task_step"         => "start_sync_fund_detail_finish",
            "task_name"         => "syncFundDetail",
            "client_id"         => $client_id,
            "store_id"          => $store_id,
            "task_id"           => $task_id,
            "user_id"           => $user_id,
            "refresh_all_flag"  => $refresh_all_flag,
            "retry"             => $retry
        ];

        \LogUtil::info(json_encode($logInfo));

    }

    //主账号授权发送店铺授权通知
    public function actionSendStoreAuthMessage($client_id,$user_id,$store_id)
    {

        \LogUtil::info("sendStoreMessage : $client_id,$user_id,$store_id");
        User::setLoginUserById($user_id);
        try
        {
            $alibabaStore  =new \common\library\alibaba\store\AlibabaStore($client_id,$store_id);
            $alibabaStore->getFormatter();
            $storeData = $alibabaStore->getAttributes();

            $userList = new UserList();
            $userList->setClientId($client_id);
            $userIds =  $userList->findUserId();
            $storeName =  $alibabaStore->store_alias?:$storeData['store_name'];
            if(empty($userIds))
            {
                return false;
            }

            foreach ($userIds as $userId)
            {
                //发送消息通知
                $notification = new \common\library\notification\Notification($client_id, \common\library\notification\Constant::NOTIFICATION_TYPE_ALIBABA_STORE_AUTH);
                $notification->user_id = $userId;
                $notification->create_user_id = $userId;
                $notification->refer_id = 0;
                $notification->setSourceData([
                    'store_id'  => $store_id,
                    'store_name' => $storeName,
                    'bind_time' => $storeData['bind_time'],
                    'expire_day' => $storeData['expire_day']??0
                ]);
                PushHelper::pushNotification($client_id, $userId, $notification);
            }
            \LogUtil::info("end SendStoreMessage : $client_id,$user_id,$store_id,{$storeName},{$storeData['bind_time']},{$storeData['expire_day']}");

            //发送完成消息后同步一次店铺成员
            if( $sessionKey = $alibabaStore->getAccessToken())
            {
                \common\library\alibaba\store\AlibabaStoreAccountSyncHelper::syncAccount($client_id, $store_id,$sessionKey);
            }


        }catch (\Exception $e)
        {
            \LogUtil::error("sendAuthStoreMessage error :".$e->getMessage().$e->getTraceAsString());
        }

    }

    public function actionFixAlibabaBuyerEmail()
    {
       $storeList =  AlibabaStore::model()->findAll('enable_flag=1 and oauth_flag = 3');

       foreach ($storeList as $store)
       {
            $clientId = $store->client_id;
            $adminUserId = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();
            User::setLoginUserById($adminUserId, $clientId);

            self::info("client: {$clientId} store: {$store->store_id} {$store->store_name}");
            $sessionKey = $store->access_token;
            $db = PgActiveRecord::getDbByClientId($clientId);
            $list = $db->createCommand("select  relation_id, buyer_account_id  from tbl_alibaba_customer_relation where  client_id={$clientId}  and buyer_account_id >0 and buyer_email=''")->queryAll();

            foreach ( $list as $item)
            {
                $buyerInfo= \common\library\alibaba\customer\CustomerSyncHelper::getAlibabaBuyerInfo($sessionKey, $item['buyer_account_id']);
                if( empty($buyerInfo['email']))
                    continue;

                $sql ="update  tbl_alibaba_customer_relation set buyer_email='{$buyerInfo['email']}' where relation_id={$item['relation_id']}";
                self::info($sql);

                $db->createCommand($sql)->execute();
            }

       }
    }

    public function actionSetSyncCustomerTaskFail($client_id=0, $dry_run=0)
    {
        $startDate  = date('Y-m-d', strtotime('-1month'));
        $clientList = $this->getClientList($client_id, true);
        $count = 0;

        foreach ($clientList as $client ) {
            $clientId = $client['client_id'];
            $db = ProjectActiveRecord::getDbByClientId($clientId);
            $taskList = $db->createCommand("select * from tbl_alibaba_customer_sync_task where client_id=:client_id and (status=:start_status or status=:running_status) and create_time <= '{$startDate}' and task_type=1")
                ->queryAll(true, [':client_id' => $clientId, ':start_status' => \common\library\alibaba\Constant::SYNC_FLAG_START, ':running_status' => \common\library\alibaba\Constant::SYNC_FLAG_RUNNING]);
            if (empty($taskList)) {
//                self::info("{$clientId} not need retry task ");
                continue;
            }

            foreach ($taskList as $task)
            {
                if( !in_array($task['status'],[\common\library\alibaba\Constant::SYNC_FLAG_RUNNING,\common\library\alibaba\Constant::SYNC_FLAG_START]) )
                    continue;

                self::info("{$task['task_id']} {$task['client_id']} {$task['create_time']} {$task['sync_time']}  {$task['status']} ");
                if( !$dry_run )
                {
                    $db->createCommand("update tbl_alibaba_customer_sync_task set status=:status where task_id=:task_id")
                        ->execute([':status' => \common\library\alibaba\Constant::SYNC_FLAG_ERROR,':task_id' => $task['task_id'] ]);
                }

                $count ++;
            }

            ProjectActiveRecord::releaseDbByClientId($client['client_id']);
            \common\library\account\Client::cleanCacheMap();
            User::cleanUserMap();
        }

        self::info("count: {$count}");

    }

    public function actionRetrySyncCustomerTask($client_id=0, $dry_run=0)
    {
        ini_set("memory_limit", "1024M");

        $startDate  = date('Y-m-d', strtotime('-2month'));
        $clientList = $this->getClientList($client_id, true);
        $retryTasks = [];
        $runTasks = [];
        $allRunCount = 0;

        foreach ($clientList as $client )
        {
            $clientId = $client['client_id'];
            $db = ProjectActiveRecord::getDbByClientId($clientId);
            $taskList = $db->createCommand("select * from tbl_alibaba_customer_sync_task where client_id=:client_id and (status=:start_status or status=:running_status) and create_time >= '{$startDate}' and task_type=1")
                ->queryAll(true,[':client_id'=> $clientId,':start_status' => \common\library\alibaba\Constant::SYNC_FLAG_START, ':running_status' => \common\library\alibaba\Constant::SYNC_FLAG_RUNNING]);
            if( empty($taskList))
            {
//                self::info("{$clientId} not need retry task ");
                continue;
            }

            foreach ($taskList as $task)
            {
                if( !in_array($task['status'],[\common\library\alibaba\Constant::SYNC_FLAG_RUNNING,\common\library\alibaba\Constant::SYNC_FLAG_START]) )
                    continue;

                $allRunCount++;

                $params = [
                    'client_id' => $clientId,
                    'user_id' => $task['user_id'],
                    'store_id' => $task['store_id'],
                    'task_id' => $task['task_id']
                ];

                if( $task['status'] == \common\library\alibaba\Constant::SYNC_FLAG_RUNNING)
                {
                    if( time() < strtotime($task['sync_time']) + 86400*1 )
                    {
                        continue;
                    }

                    if( !$dry_run )
                    {
                        $db->createCommand("update tbl_alibaba_customer_sync_task set status=:status where task_id=:task_id")
                            ->execute([':status' => \common\library\alibaba\Constant::SYNC_FLAG_START,':task_id' => $task['task_id'] ]);
                    }

                    $runTasks[] = $params;
                }else
                {
                    $retryTasks[] = $params;
                }


                $log = '/tmp/alibaba_syncCustomer.log';
                [$exec, $output, $return] =  \common\library\CommandRunner::run(
                    'alibaba',
                    'syncCustomer',
                    $params,
                    $log, 1, $dry_run
                );

                self::info("{$task['task_id']} {$task['client_id']} {$task['create_time']} {$task['sync_time']}  {$task['status']} "
                    . $exec .' '. json_encode($output).' '.json_encode($return) .' '.json_encode($params));
            }

            ProjectActiveRecord::releaseDbByClientId($client['client_id']);
            \common\library\account\Client::cleanCacheMap();
            User::cleanUserMap();
        }

        self::info("retry task count: ".count($retryTasks). ' retry run task count: '.count($runTasks).' all run :'.$allRunCount);
    }

    public function actionRetryFailSyncCustomerTask($client_id=0, $dry_run=0)
    {
        ini_set("memory_limit", "1024M");

        $startDate  = date('Y-m-d', strtotime('-2day'));
        $clientList = $this->getClientList($client_id, true);
        $retryTasks = [];
        $startTasks = [];
        $runTasks = [];
        $allRunCount = 0;

        foreach ($clientList as $client )
        {
            $clientId = $client['client_id'];
            $db = ProjectActiveRecord::getDbByClientId($clientId);
            $statusArr = [
                \common\library\alibaba\Constant::SYNC_FLAG_START,
                \common\library\alibaba\Constant::SYNC_FLAG_RUNNING,
                \common\library\alibaba\Constant::SYNC_FLAG_ERROR,
            ];

            $taskList = $db->createCommand("select * from tbl_alibaba_customer_sync_task where client_id=:client_id and status in (".implode(',', $statusArr).") and create_time >= '{$startDate}' and task_type=1")
                ->queryAll(true,[
                    ':client_id'=> $clientId,
                ]);
            if( empty($taskList))
            {
//                self::info("{$clientId} not need retry task ");
                continue;
            }

            foreach ($taskList as $task)
            {

                if( !in_array($task['status'],$statusArr) )
                    continue;

                $allRunCount++;

                $params = [
                    'client_id' => $clientId,
                    'user_id' => $task['user_id'],
                    'store_id' => $task['store_id'],
                    'task_id' => $task['task_id']
                ];


                switch ($task['status'] )
                {
                    case \common\library\alibaba\Constant::SYNC_FLAG_START:
                        //积压4个小时也是异常
                        if( time() < strtotime($task['sync_time']) + 60*60*4 )
                        {
                            continue 2;
                        }
                        $startTasks[] = $params;
                        break;
                    case \common\library\alibaba\Constant::SYNC_FLAG_RUNNING:
                        //8个小时没执行完成, 也是异常的
                        if( time() < strtotime($task['sync_time']) + 60*60*8 )
                        {
                            continue 2;
                        }
                        $runTasks[] = $params;
                        break;
                    case \common\library\alibaba\Constant::SYNC_FLAG_ERROR:
                        if( strpos($task['fail_reason'], ' Name or service not known') === false)
                            continue 2;
                        $retryTasks[] = $params;
                        break;
                }

                self::info("{$clientId} {$task['task_id']} {$task['create_time']} {$task['status']} {$task['fail_reason']}");

                if( !$dry_run )
                {
                    $db->createCommand("update tbl_alibaba_customer_sync_task set status=:status, fail_reason='' where task_id=:task_id")
                        ->execute([':status' => \common\library\alibaba\Constant::SYNC_FLAG_START,':task_id' => $task['task_id'] ]);
                }

                $log = '/tmp/alibaba_syncCustomer.log';
                [$exec, $output, $return] =  \common\library\CommandRunner::run(
                    'alibaba',
                    'syncCustomer',
                    $params,
                    $log, 1, $dry_run
                );

                self::info("{$task['task_id']} {$task['client_id']} {$task['create_time']} {$task['sync_time']}  {$task['status']} "
                    . $exec .' '. json_encode($output).' '.json_encode($return) .' '.json_encode($params));
            }

            ProjectActiveRecord::releaseDbByClientId($client['client_id']);
            \common\library\account\Client::cleanCacheMap();
            User::cleanUserMap();
        }

        self::info(' start run task count: '.count($startTasks)."retry task count: ".count($retryTasks). ' retry run task count: '.count($runTasks).' all run :'.$allRunCount);
    }

    public function actionUpdateTask($client_id, $last_number=-1)
    {
        $clientIds = explode(',', $client_id);
        if( $last_number >-1)
        {
           $clientIds = array_merge($clientIds,
               array_column($this->getClientList(0,false,null,null,0, 0, $last_number),'client_id'));
        }

        $clientIds = array_values(array_unique(array_filter($clientIds)));

        if( empty($clientIds))
            throw new RuntimeException('没有client');

        foreach ($clientIds as $clientId )
        {
            $db = ProjectActiveRecord::getDbByClientId($clientId);
            $list =  $db->createCommand("select * from tbl_alibaba_customer_sync_task where client_id=:client_id and status=3 and create_time<='2020-12-23 00:00:00'")->queryAll(true, [':client_id'=> $clientId]);

            if( empty($list) )
            {
                continue;
            }

            $adminUser = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();
            User::setLoginUserById($adminUser);
            $pg = PgActiveRecord::getDbByClientId($clientId);

            foreach ($list as $item )
            {
                if( !$item['success_count'])
                    continue;

                if( $item['public_count'] + $item['private_count'] == $item['success_count'])
                {
                    self::info("{$clientId} {$item['task_id']} 已经更新过了,跳过 {$item['success_count']} {$item['public_count']} {$item['private_count']}");
                    continue;
                }
               $companyIds = array_merge(...\common\library\alibaba\customer\CustomerSyncHelper::getSyncTaskCompanyIds($item['task_id']));
               if( empty($companyIds) )
                   continue;

               $companyList = $pg->createCommand('select company_id, user_id from tbl_company where client_id=:client_id and company_id in ('.implode(',', $companyIds).')')->queryAll(true, [':client_id'=> $clientId]);

               $publicCompanyIds = [];
               $privateCompanyIds =[];
               foreach ($companyList as $elem)
               {
                   if( '{}' == $elem['user_id'])
                   {
                       $publicCompanyIds[] = $elem['company_id'];
                   }else
                   {
                       $privateCompanyIds[] = $elem['company_id'];
                   }
               }

               $publicCompanyCount = count($publicCompanyIds);
               $privateCompanyCount = count($privateCompanyIds);

               if( $publicCompanyCount + $privateCompanyCount  !=  $item['success_count'])
               {
                   self::info("{$item['task_id']} 更新完成 {$item['success_count']} ! = {$privateCompanyCount} +$publicCompanyCount");
                   $publicCompanyCount = $item['success_count'] - $privateCompanyCount;
               }

               $sql = "update tbl_alibaba_customer_sync_task set public_count={$publicCompanyCount},private_count={$privateCompanyCount}  where task_id={$item['task_id']}";
               self::info("{$item['client_id']}  {$item['task_id']} 更新完成 {$item['success_count']} {$privateCompanyCount},$publicCompanyCount");
               $db->createCommand($sql)->execute();
//
               \common\library\alibaba\customer\CustomerSyncHelper::saveSyncTaskData($clientId,$item['task_id'], [
                   'public' => $publicCompanyIds,
                   'private' => $privateCompanyIds,
               ]);

                self::info($sql);
            }
        }

    }

    /**
     * 每天定时同步数据任务
     * 由于每天固定时间把所有需要获取平台产品信息的企业一起投入对队列造成获取产品详情接口并发访问过大
     * 暂以分片方案进行：用4进行取模，每次投入client大约在8000-9000左右，数据截止于2025-05-26
     * @param int $client_id
     * php ./yiic-test alibaba pushEverydayTaskQueue --mod=4 --modVal=0
     * php ./yiic-test alibaba pushEverydayTaskQueue --mod=4 --modVal=1
     * php ./yiic-test alibaba pushEverydayTaskQueue --mod=4 --modVal=2
     * php ./yiic-test alibaba pushEverydayTaskQueue --mod=4 --modVal=3
     */
    public function actionPushEverydayTaskQueue($client_id=0, $mod = 0, $modVal = 0, $num = 40, $sleep = 120)
    {
        $sql = "select store_id,client_id, sync_customer_flag, sync_product_flag from tbl_alibaba_store where enable_flag=1";
        if ($client_id > 0)
        {
            $client_id = intval($client_id);
            $sql .= " and client_id={$client_id}";
        } else {
            if ($mod > 0 && $modVal < $mod && $modVal >= 0) {
                $sql .= " and sync_product_flag = 1 and store_id  > 0 and client_id > 0 and mod(client_id, $mod) = $modVal";
            }
        }

        LogUtil::info("actionPushEverydayTaskQueue sql: " . $sql);

        $storeList = Yii::app()->db->createCommand($sql)->queryAll();
        $customerTaskCount = 0;
        $productTaskCount = 0;
        $needToRun = [];
        foreach ($storeList as $item)
        {
            $clientId = $item['client_id'];
            $storeId = $item['store_id'];

            if (!$clientId || !$storeId) {
                LogUtil::info("[pushEverydayTask] client_id或store_id为空, data: " . json_encode($item));
                continue;
            }

            $adminUserId = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();
            if(!$adminUserId)
            {
                continue;
            }

            User::setLoginUserById($adminUserId);

            //改为实时推送了, 不再处理
//            if( $item['sync_customer_flag'])
//            {
//                $store = new \common\library\alibaba\store\AlibabaStore($clientId, $storeId);
//                $customerSyncSetting = $store->getCustomerSyncObj();
//                $alibabaCustomerSync = new \common\library\alibaba\customer\AlibabaCustomerSyncTask($clientId);
//                $nowTime = date('Y-m-d H:i:s',time());
//                $alibabaCustomerSync->client_id = $clientId;
//                $alibabaCustomerSync->user_id = $customerSyncSetting->update_user?:$adminUserId;
//                $alibabaCustomerSync->store_id = $storeId;
//                $alibabaCustomerSync->pool_id = $customerSyncSetting->pool_id;
//                $alibabaCustomerSync->customer_status = $customerSyncSetting->customer_status;
//                $alibabaCustomerSync->status = \common\library\alibaba\Constant::SYNC_FLAG_START;
//                $alibabaCustomerSync->fail_reason = 0;
//                $alibabaCustomerSync->result_line = '';
//                $alibabaCustomerSync->create_time = $nowTime;
//                $alibabaCustomerSync->task_type = \common\library\alibaba\Constant::SYNC_TASK_TYPE_EVERY_DAY;
//                $alibabaCustomerSync->save();
//                $alibabaCustomerSync->pushQueue();
//                $customerTaskCount++;
//            }

            $item['sync_product_flag'] && $needToRun[] = ['client_id' => $clientId, 'admin_user_id' => $adminUserId, 'store_id' => $storeId];
//            if( $item['sync_product_flag'] )
//            {
//                \common\library\alibaba\services\queue\QueueHelper::pushProductSyncHandle($clientId, $adminUserId, $storeId, \common\library\alibaba\product\AlibabaProductSyncTask::TASK_TYPE_SYSTEM);
//                $productTaskCount++;
//            }
        }
        $total  = count($needToRun);
        $uniqid = uniqid();
        LogUtil::info("actionPushEverydayTaskQueue_start", [
            'uniqid' => $uniqid, 'total'  => $total,
            'modVal' => $modVal, 'num' => $num, 'sleep' => $sleep,
        ]);
        $needToRuns = array_chunk($needToRun, $num);
        foreach ($needToRuns as $itemList) {
            foreach ($itemList as $value) {
                \common\library\alibaba\services\queue\QueueHelper::pushProductSyncHandle($value['client_id'], $value['admin_user_id'], $value['store_id'], \common\library\alibaba\product\AlibabaProductSyncTask::TASK_TYPE_SYSTEM);
            }
            $sleep && sleep($sleep);
        }
        LogUtil::info("actionPushEverydayTaskQueue_end", [
            'uniqid' => $uniqid,
            'total'  => $total,
        ]);
        self::info('共处理店铺：'. count($storeList)." 同步客户任务数: {$customerTaskCount}  同步产品任务数: {$productTaskCount}");
    }

    /**
     * 为用户手动触发全量同步
     * @param $clientId
     * @param $storeId
     * @param $taskType
     * @return void
     */
    public function actionSyncAllProduct($clientId, $storeId)
    {
        if (!$clientId || !$storeId) {
            return;
        }

        $adminUserId = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();
        if(!$adminUserId) {
            return;
        }

        QueueHelper::pushProductSyncHandle($clientId, $adminUserId, $storeId, AlibabaProductSyncTask::TASK_TYPE_SYSTEM, 0);
    }

    public function actionSyncProduct($clientId, $userId = 0, $storeId = 0, $taskType = 1, $checkLastSyncTime = 1)
    {
        ini_set("memory_limit", "1024M");
        LogUtil::info("begin Sync: clientId:$clientId,userId:$userId,storeId:$storeId,taskType:$taskType,checkLastSyncTime:$checkLastSyncTime");

        //开始处理同步任务后, 就解除锁定, 防止任务重复处理
        QueueHelper::unlockProductSyncTask($clientId, $storeId);

        if (empty($userId))
            $userId = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();

        User::setLoginUserById($userId);

        $storeIds = [$storeId];
        if ($storeId == 0)
        {
            $alibabaStoreList = new \common\library\alibaba\store\AlibabaStoreList($clientId);
            $alibabaStoreList->setFields(['store_id']);
            $alibabaStoreList->setEnableFlag(1);
            $alibabaStoreList->setSyncProductFlag(1);
            $list = $alibabaStoreList->find();
            $storeIds = array_column($list, 'store_id');
        }

        $storeIds = array_filter($storeIds);
        if (empty($storeIds))
        {
            LogUtil::info("empty store ids clientId:{$clientId}");
            return false;
        }

        foreach ($storeIds as $storeId)
        {
            //保证执行上锁
            $lock = QueueHelper::lockProductSyncTaskSync($clientId, $storeId);
            if (!$lock) {
                LogUtil::info("sync task repeat clientId:{$clientId} storeId:{$storeId}");
                return false;
            }

            $task = new \common\library\alibaba\product\AlibabaProductSyncTask($clientId);
            $task->task_type = $taskType;
            $task->store_id = $storeId;
            $task->user_id = $userId;
            $task->create_time = $task->update_time = date('Y-m-d H:i:s');
            $task->save();

            $dispatcher = new \common\library\alibaba\product\AlibabaProductSyncDispatcher($clientId, $userId);
            $dispatcher->runByStoreTask($task, $checkLastSyncTime);

            QueueHelper::unlockProductSyncTaskSync($clientId, $storeId);
        }
    }

    /**
     * 同步指定ID的平台产品
     * @param $clientId
     * @param $userId
     * @param $platformSkuIds
     * @param int $taskType
     * @throws \ProcessException
     */
    public function actionSyncProductByIds($clientId, $userId, $platformSkuIds, int $taskType = 1)
    {
        LogUtil::info("begin Sync: clientId:$clientId,platformSkuIds:$platformSkuIds");

        if (!$platformSkuIds) {
            return;
        }

        if (empty($userId)) {
            $userId = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();
        }
        User::setLoginUserById($userId);

        $task = new \common\library\alibaba\product\AlibabaProductSyncTask($clientId);
        $task->store_id = 0;
        $task->user_id = $userId;
        $task->status = Constant::SYNC_FLAG_START;
        $task->platform_sku_ids = $platformSkuIds;
        $task->task_type = $taskType;
        $task->create_time = $task->update_time = date('Y-m-d H:i:s');
        $task->save();


        $dispatcher = new \common\library\alibaba\product\AlibabaProductSyncDispatcher($clientId, $userId);
        $dispatcher->runByIdsTask($task);
    }

    public function actionFixRunAllProduct($client_id,$store_id){

        $alibabaStore = new \common\library\alibaba\store\AlibabaStore($client_id,$store_id);
        if($alibabaStore->isNew() ||$alibabaStore->enable_flag !=1 || !$alibabaStore->access_token){
            die('授权无效!');
        }
        $token = $alibabaStore->access_token;
        $currentPage = 1;//页码
        $startTime = '';//产品更新最早时间
        $endTime = '';//产品更新最晚时间
        $pageSize = 30;//页码
        $totalCount = 0;//返回总数
        $pageCount = 0;
        $maxStartPageSize = 166;//需要重新计算的页码，超过5000个（166页）
        $totalCurrentPage = 1; //总页数
        $lastUpdateTimeTo = '';//商品最晚更新时间
        self::info("store_id: {$alibabaStore->store_id} token:{$alibabaStore->access_token}");
        $lastProductIds = [];

        do {
            $productList = new \common\library\alibaba\services\AlibabaProductList($token);

            if($currentPage % $maxStartPageSize == 1 && $lastUpdateTimeTo){
                $endTime = $lastUpdateTimeTo;
                $currentPage = 1;
                self::info("store_id: {$alibabaStore->store_id} lastUpdateTimeTo:{$lastUpdateTimeTo}");
            }
            $productList->setCurrentPage($currentPage);
            $productList->setPageSize($pageSize);
            if ($startTime)
                $productList->setModifiedFrom($startTime);
            if ($endTime)
                $productList->setModifiedTo($endTime);

            $rsp = $productList->find();

            if (!isset($rsp['total_item']) || $rsp['total_item'] <= 0) {
                self::info("no_data".json_encode($rsp));
                return false;
            }
            if ($currentPage === 1)
            {
                $totalCount = $rsp['total_item'];
                $pageCount = ceil($totalCount/ $pageSize);
                self::info("total:{$totalCount} currentPage{$currentPage}");
            }

            \LogUtil::info("TotalCurrentPage:{$totalCurrentPage} CurrentPage:{$currentPage},pageCount: {$pageCount}, PageSize:{$pageSize}, total:{$rsp['total_item']}");

            $data = $rsp['products']['alibaba_product_brief_response'] ?? [];
            if (empty($data))
            {
                self::info("no_product:".json_encode($data));
            }

            $aliProductIds = array_column($data,'product_id');
            foreach ($data as $item){
                if($lastUpdateTimeTo && $lastProductIds && in_array($item['product_id'],$lastProductIds)){
                    self::info("skip:{$item['product_id']}");
                    continue;
                }
            }

            $productCount = count($aliProductIds);
            self::info("sync begin:TotalCurrentPage:{$totalCurrentPage} currentPage:{$currentPage}, totalCount:{$totalCount} product:{$productCount}");
            if($currentPage % $maxStartPageSize === 0){
                $lastData = array_pop($data);
                $lastUpdateTimeTo = $lastData['gmt_modified'];//最晚更新时间
            }
            $currentPage++;
            $totalCurrentPage++;

            $lastProductIds = $aliProductIds;

        } while ($currentPage <= $pageCount);

        echo "finish\r\n";
    }

    public function actionParseProductUrlDispatcher($clientId = 0, $lastNumber = null)
    {
        $clientList = $this->getClientList($clientId, false, null, null, 0, 0, $lastNumber);

        $num = 1;
        $clientCount = count($clientList);
        foreach ($clientList as $client)
        {
            echo "{$client['client_id']} $num/$clientCount".PHP_EOL;
            $num++;

            if (Yii::app()->params['env'] == 'test' && $client['mysql_set_id'] != 3)
                continue;

            $this->actionParseProductUrl($client['client_id']);
        }
        echo 'done!'.PHP_EOL;
    }

    public function actionParseProductUrl($clientId)
    {
        $sql = "select product_id, from_url from tbl_product where client_id = :clientId and source_type = :sourceType
and from_url != '' and ali_product_id = 0 and ali_store_id = 0";
        $params = [
            ':clientId' => $clientId,
            ':sourceType' => \common\models\client\ClientProduct::SOURCE_TYPE_ALIBABA
        ];
        $db = PgActiveRecord::getDbByClientId($clientId);
        $list = $db->createCommand($sql)->queryAll(true, $params);

        $updateSql = [];
        foreach ($list as $item)
        {
            preg_match('/product\/(\d+)\-\d+/i',$item['from_url'], $match);

            if (!empty($aliProductId = $match[1]))
            {
                $updateSql[] = "update tbl_product set ali_product_id=$aliProductId where product_id = {$item['product_id']}";
            }
        }

        $updateSql = array_chunk($updateSql, 100);
        foreach ($updateSql as $sql) {
            $sql = implode(';', $sql);
            $db->createCommand($sql)->execute();
        }
    }


    public function actionUpdateCategoryAttr($id = 0, $isSingle = false){
        $allSql = 'select id, ali_cat_id from v4_admin.tbl_category where is_leaf = 1';
        if ($isSingle) {
            $allSql = $allSql . ' and id = ' . $id;
        } else {
            $allSql = $allSql . ' and id >= ' . $id . ' order by id asc ';
        }
        $categories = \Yii::app()->db->createCommand($allSql)->queryAll(true);
        $categories = array_column($categories, null, 'id');

        $num = 0;
        foreach ($categories as $id => $cat) {
            $num++;
            echo date('Y-m-d H:i:s').' 第'.$num.'条，正在更新id：'.$id.' 的品类属性json数据...'."\n";
            $systemAttrJson = \common\library\alibaba\category\AlibabaCategoryHelper::updateCategoryAttr($cat);

            if (!$systemAttrJson) {
                echo '该品类没有找到阿里对对应数据，跳过。'."\n";
                continue;
            }

            echo '更新完成，正在保存...' . "\n";
            $inUpSql = "insert into v4_admin.tbl_alibaba_category_attr set id=:id, bind_attr_json='[]', system_attr_json=:system_attr_json ON DUPLICATE KEY UPDATE system_attr_json=:system_attr_json";
            \Yii::app()->db->createCommand($inUpSql)->execute([':id' => $id, ':system_attr_json' => $systemAttrJson]);
            echo '本条更新完成' . "\n";
        }

        echo "本次类目属性更新完成。\n";
    }

    public function actionOutputDataJson(){

        $json =  \common\library\alibaba\category\AlibabaCategoryHelper::getDataJson();

        fwrite(fopen('data.json', 'w+'), $json);
    }

    public function actionUpdateCategory(){
        echo date('Y-m-d H:i:s').' 开始更新类目表...'."\n";
        \common\library\alibaba\category\AlibabaCategoryHelper::updateCategory();
        echo date('Y-m-d H:i:s').' 完成类目更新'."\n";
        \common\library\alibaba\category\AlibabaCategoryHelper::updateCategoryPrefix();
        echo date('Y-m-d H:i:s').' 完成rootid和prefix_id更新, 全部操作已完成。'."\n";
    }

    public function actionFixStatusAction($clientId)
    {
        $db = PgActiveRecord::getDbByClientId($clientId);
//        $userId = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();
//        var_dump($userId);
        User::setLoginUserById('11858592');
        $list = $db->createCommand('select order_id , ali_order_id, ali_store_id from tbl_order where client_id=:client_id and ali_store_id >0')->queryAll(true, [':client_id' => $clientId]);
        $map = [];
        foreach ($list as $item )
        {
            if( isset($map[$item['ali_store_id']]))
            {
                $sessionKey = $map[$item['ali_store_id']];
            }else
            {

                $sessionKey =  AlibabaStore::model()->getDbConnection()->createCommand("select * from tbl_alibaba_store where store_id={$item['ali_store_id']} and enable_flag=1 ")->queryRow()['access_token'];
                $map[$item['ali_store_id']] = $sessionKey;
            }

            if( empty($sessionKey))
            {
                self::info('error sessionKey '.$item['ali_store_id']);
                continue;
            }

            $aliOrder = new \common\library\alibaba\services\AlibabaOrder($sessionKey, $item['ali_order_id']);
            $result = $aliOrder->getStatusAction()['actions']['actions']??[];
            $statusActionList = array_column($result,null,'name');
            if( isset($statusActionList['view_payment_link']))
            {
                $statusActionList['view_payment_link']['time'] = time();
            }
            $statusActions = json_encode($statusActionList);
            var_dump($statusActions);

            $db->createCommand("update tbl_order set status_action='{$statusActions}' where order_id={$item['order_id']}")->execute();

        }

    }

    public function actionStoreAccount($client_id = 0, $lastNumber = null)
    {
        $clientList = $this->getClientList($client_id, false, null, null, 0, 0, $lastNumber);
        foreach ($clientList as $client)
        {
            $db = AlibabaStore::model()->getDbConnection();
            $list = $db->createCommand('select store_id , client_id, access_token ,enable_flag from tbl_alibaba_store where client_id=:client_id')->queryAll(true,[':client_id'=> $client['client_id']]);
            foreach ($list as $item)
            {
                $clientId = $item['client_id'];
                $storeId = $item['store_id'];
                $sessionKey = $item['access_token'];

                if(!$item['enable_flag'])
                {
                    $sessionKey = $db->createCommand("select  access_token from tbl_alibaba_store  where store_id={$storeId} and enable_flag=1")->queryScalar();
                }

                if( empty($sessionKey) )
                {
                    self::info("店铺授权已取消 ,跳过 {$clientId}  {$storeId}  {$sessionKey}");
                    continue;
                }

                $ret = \common\library\alibaba\store\AlibabaStoreAccountSyncHelper::syncAccount($clientId, $storeId, $sessionKey);
                if( !$ret )
                {
                    self::info("店铺账号同步失败 ,跳过 {$clientId}  {$storeId}  {$sessionKey}");
                }

                $accountList = \common\library\alibaba\store\AlibabaStoreAccountSyncHelper::getAccountList($clientId, $storeId, null);
                $map = array_column($accountList,null, 'seller_email');

                $clientIds = [];
                $memberList = $db->createCommand("select id, store_id,client_id,seller_account_id,seller_email from tbl_alibaba_store_members where client_id={$clientId} and store_id={$storeId} and seller_account_id=0 and enable_flag=1")->queryAll();
                foreach ($memberList as $elem)
                {
                    $isLower = false;
                    $lower = strtolower($elem['seller_email']);
                    if( isset($map[$elem['seller_email']]))
                    {
                        $info = $map[$elem['seller_email']];
                        $isLower = false;
                    }else
                    {
                        $info = $map[$lower]??[];
                        if(!empty($info))
                        {
                            $isLower = true;
                        }
                    }

                    if( empty($info))
                    {
                        self::info("店铺同步账号完成  {$clientId}  {$storeId} {$elem['seller_email']} is empty!");
                        //todo 需要做修正处理
                        continue;
                    }

                    if( $isLower)
                    {
                        $db->createCommand("update tbl_alibaba_store_members set seller_account_id=:seller_account_id,seller_email='{$lower}' where id=:id")->execute([':seller_account_id'=> $info['seller_account_id'],':id'=> $elem['id']]);
                    }else
                    {
                        $db->createCommand("update tbl_alibaba_store_members set seller_account_id=:seller_account_id where id=:id")->execute([':seller_account_id'=> $info['seller_account_id'],':id'=> $elem['id']]);
                    }


                }

                $this->resetStoreMemberCache($clientId);

                self::info("店铺同步账号完成  {$clientId}  {$storeId}  {$sessionKey}");
            }
        }

    }


    public function actionFixCustomerCountry($client_id=0)
    {
        $clientList = $this->getClientList($client_id);
        foreach ($clientList as $item )
        {
            $clientId = $item['client_id'];
            $db = ProjectActiveRecord::getDbByClientId($clientId);
            $pgDb = PgActiveRecord::getDbByClientId($clientId);

            $taskList = $db->createCommand('select company_ids,public_company_ids from tbl_alibaba_customer_sync_data where client_id='.$clientId)->queryAll();
            $companyIds = [];
            foreach ($taskList as $elem )
            {
                if( $elem['company_ids'] )
                {
                    $privateCompanyJson = snappy_uncompress($elem['company_ids'] );
                    $privateCompanyIds = json_decode($privateCompanyJson, true)?:[];
                    $companyIds = array_merge($companyIds, $privateCompanyIds);
                }

                if( $elem['public_company_ids'])
                {
                    $publicCompanyJson = snappy_uncompress($elem['public_company_ids'] );
                    $publicCompanyIds = json_decode($publicCompanyJson, true)?:[];
                    $companyIds = array_merge($companyIds, $publicCompanyIds);
                }

                $companyIds = array_values(array_unique($companyIds));
            }

            if( empty($companyIds))
            {
                self::info("empty {$clientId}");
                continue;
            }

           $companyList =  $pgDb->createCommand("select company_id, country from tbl_company where client_id={$clientId} and company_id  in (".implode(',', $companyIds).") and country in ('ALA','BLM','FX','FX','GGY','JEY','KS','MAF','MNE','SGS','SRB','TLS','TP','UK','ZR')")->queryAll();

            foreach ($companyList as $value)
            {
                $country = \common\library\alibaba\services\DataHelper::convertCountry($value['country']);
                if(empty($country))
                    continue;

                $sql = "update tbl_company set country='{$country}' where company_id={$value['company_id']}";
                self::info("{$value['company_id']} {$value['country']} {$country} {$sql}");
                $pgDb->createCommand($sql)->execute();
            }
        }
    }

    public function actionCompanyAliStoreIdTransferByClientId($clientId)
    {
        echo "clientId:{$clientId} start\n";
        $pgDb = PgActiveRecord::getDbByClientId($clientId);
        $db = ProjectActiveRecord::getDbByClientId($clientId);
        if (! $pgDb) {
            return;
        }

        $sql = "SELECT company_id, array_to_string(array_agg(store_id), ',') as ali_store_id FROM tbl_alibaba_company_relation where client_id={$clientId} and company_id<>0 and store_id<>0 group by company_id";
        $list = $pgDb->createCommand($sql)->queryAll();

        $sql = "SELECT company_id, GROUP_CONCAT(store_id) as ali_store_id
from tbl_alibaba_trade_relation
where client_id={$clientId}
  and company_id<>0
  and enable_flag = 1
  and store_id <> 0
group by company_id";
        $tradeList = $db->createCommand($sql)->queryAll();
        $tradeMap = array_column($tradeList, null, 'company_id');

        if (empty($list) && empty($tradeList)) {
            return;
        }

        foreach ($list as $key => $item) {
            if (isset($tradeMap[$item['company_id']])) {
                $list[$key]['ali_store_id'] = $tradeMap[$item['company_id']]['ali_store_id'] . ',' . $item['ali_store_id'];
                unset($tradeMap[$item['company_id']]);
            }
        }

        $list = array_merge($list, array_values($tradeMap));
        $sqlItem = [];
        foreach ($list as $item) {
            $sqlItem[] .= "({$item['company_id']}, {$clientId}, '{{$item['ali_store_id']}}', '', '')";
        }

        $sqlItemStr = implode(',', $sqlItem);
        $insertSql = "insert into tbl_company(company_id, client_id, ali_store_id,  name, short_name) values {$sqlItemStr} ON CONFLICT (company_id) DO UPDATE
              SET ali_store_id = EXCLUDED.ali_store_id";
        LogUtil::info("clientId:{$clientId} sql:{$insertSql}");
        echo "clientId:{$clientId} success\n";
        $pgDb->createCommand($insertSql)->execute();
    }

    public function actionCompanyAliStoreIdTransfer()
    {
        $clients = $this->getClientList();
        foreach ($clients as $client) {
            if (! in_array($client['client_id'], [3,7,10,2106, 6688, 6698, 16149,17383,18120,18133,18152,18154,18617,18618,19709,38955,39839])) {
                continue;
            }
            $this->actionCompanyAliStoreIdTransferByClientId($client['client_id']);
        }
    }

    public function actionNewInitCustomerSyncFlag($client_id = 0, $lastNumber = null)
    {

        $clientList = $this->getClientList($client_id, false, null, null, 0, 0, $lastNumber);
        foreach ($clientList as $client)
        {
           $db =  AlibabaStore::model()->getDbConnection();
           $list =  $db->createCommand('select store_id , sync_customer_flag from tbl_alibaba_store  where client_id=:client_id and sync_customer_flag=1')->queryAll(true, [':client_id'=> $client['client_id']]);
           $storeIds = array_column($list,'store_id');
           if( empty($storeIds))
               continue;

           $db->createCommand('update tbl_alibaba_store set sync_customer_flag=0 where client_id=:client_id and store_id in ('.implode(',', $storeIds).')')->execute([':client_id'=> $client['client_id']]);
           $db->createCommand('update tbl_alibaba_customer_sync_setting set enable_flag=0 where client_id=:client_id and store_id in ('.implode(',', $storeIds).')')->execute([':client_id'=> $client['client_id']]);

           $this->resetStoreCache($client['client_id']);
           self::info("client_id: {$client['client_id']} store_ids: ".implode(',', $storeIds));
        }
    }

    public function actionFixAlibabaCompanyRelation($client_id = 0, $lastNumber = null)
    {
        $clientList = $this->getClientList($client_id, false, null, null, 0, 0, $lastNumber);
        foreach ($clientList as $client)
        {
            $db =  PgActiveRecord::getDbByClientId($client['client_id']);
            try
            {
                $sql = 'update tbl_alibaba_company_relation  set sync_status = CASE WHEN company_id>0 THEN 2 ELSE 3 END  where client_id=1 and sync_status=0;';
                $ret = $db->createCommand($sql)->execute([':client_id'=> $client['client_id']]);
                self::info("fix sync_status   client_id: {$client['client_id']} count: {$ret}");

                $sql = 'select r.relation_id,r.alibaba_company_id,r.company_id  from tbl_alibaba_company_relation  r join tbl_company c on c.company_id = r.company_id where r.client_id=:client_id and r.company_id >0 and c.is_archive=0';
                $companyRelationList = $db->createCommand($sql)->queryAll(true,[':client_id'=> $client['client_id']]);
                if( !empty($companyRelationList))
                {
                    $fromCompanyIds = array_column($companyRelationList,'company_id');
                    $fromCompanyIds = array_filter(array_unique($fromCompanyIds));
                    if( !empty($fromCompanyIds) )
                    {
                        $sql = "UPDATE tbl_alibaba_company_relation SET company_id=0"
                            . "WHERE client_id=:client_id AND company_id in  (".implode(',',$fromCompanyIds).')';
                        $params = [
                            ':client_id' => $client['client_id'],
                        ];
                        $unbindCompanyCount = $db->createCommand($sql)->execute($params);

                        self::info("fix  tbl_alibaba_company_relation  client_id: {$client['client_id']} count: {$unbindCompanyCount}");

                    }
                }

                $sql = 'select r.relation_id,r.alibaba_customer_id,r.customer_id from tbl_alibaba_customer_relation  r join tbl_customer c on c.customer_id = r.customer_id where r.client_id=:client_id and r.customer_id >0 and c.is_archive=0';
                $customerRelationList = $db->createCommand($sql)->queryAll(true,[':client_id'=> $client['client_id']]);
                if( !empty($customerRelationList))
                {
                    $fromCustomerIds = array_column($customerRelationList,'customer_id');
                    $fromCustomerIds = array_filter(array_unique($fromCustomerIds));
                    if( !empty($fromCustomerIds) )
                    {
                        $sql = "UPDATE tbl_alibaba_customer_relation SET customer_id=0"
                            . "  WHERE client_id=:client_id AND  customer_id in  (".implode(',',$fromCustomerIds).')';
                        $params = [
                            ':client_id' => $client['client_id'],
                        ];
                        $unbindCustomerCount = $db->createCommand($sql)->execute($params);

                        var_dump($sql);
                        self::info("fix  tbl_alibaba_customer_relation  client_id: {$client['client_id']} count: {$unbindCustomerCount}");
                    }

                }

            }catch (\Exception $e)
            {
                self::info("client_id: {$client['client_id']} error:".$e->getMessage());
                if( Yii::app()->params['env'] != 'test')
                {
                    throw  $e;
                }
            }

        }
    }

    public function actionRecoverDelete($client_id)
    {
        $db =  ProjectActiveRecord::getDbByClientId($client_id);
        $adminUserId = \common\library\privilege_v3\PrivilegeService::getInstance($client_id)->getAdminUserId();
        User::setLoginUserById($adminUserId);


        $pgDb = PgActiveRecord::getDbByClientId($client_id);
        $deleteStartTime = $pgDb->createCommand("select create_time from tbl_alibaba_company_relation where client_id={$client_id} order by create_time asc limit 1")->queryScalar();
        $syncStart = '2021-03-02 00:00:00';
        $syncEnd = '2021-03-04 00:00:00';
        $sql = "SELECT refer_id,type FROM `tbl_recycle` WHERE `client_id` = {$client_id} AND `type` = '2' and `create_time` <='{$syncEnd}' and create_time>='{$deleteStartTime}'";
        $list = $db->createCommand($sql)->queryAll();

        $delCompanyIds = array_column($list, 'refer_id');
//        $delCompanyIds = $pgDb->createCommand('select company_id from tbl_company where company_id in ('.implode(',', array_column($list, 'refer_id')).') and is_archive=0')->queryColumn();


        $selectSql = "select c.company_id, c.name ,r.alibaba_company_id, r.alibaba_company_name from  tbl_company c join  tbl_alibaba_company_relation r on c.company_id=r.company_id".
            " where r.sync_time >='{$syncStart}' and r.sync_time <='{$syncEnd}' and r.client_id={$client_id} and r.company_id>0 and r.is_add=1 and c.is_archive=1 and c.ali_store_id !='{}'";
        $syncList = $pgDb->createCommand($selectSql)->queryAll();
        $syncCompanyIds = array_column($syncList,'company_id');


        $delCompanyInfoList = $pgDb->createCommand('select name,company_id from tbl_company where company_id in ('.implode(',', $delCompanyIds).')')->queryAll();
        $delCompanyNameMap =[];
        foreach ($delCompanyInfoList as $item)
        {
            $pos = strpos($item['name'], '_重命名_20210303');
            if( $pos )
            {
                $name = substr($item['name'],0, $pos);
                $delCompanyNameMap[$name] = $item['company_id'];
            }
        }
        $i =0;
        $needDeleteCompanyIds =[];
        foreach ($syncList as $item )
        {
            $oldCompanyId = $delCompanyNameMap[$item['alibaba_company_name']]??0;
            if( $oldCompanyId )
            {
                $i ++;
                self::info("{$i} del company_id: {$item['company_id']} old_company_id: {$oldCompanyId}  {$item['alibaba_company_id']} {$item['alibaba_company_name']}");
                $needDeleteCompanyIds[] = $item['company_id'];
            }
        }

        self::info('del company_ids:'.implode(',',$needDeleteCompanyIds). ' count'.count($needDeleteCompanyIds));

        $delCompanyList = $pgDb->createCommand('select name,user_id,company_id from tbl_company where company_id in ('.implode(',', $needDeleteCompanyIds).')')->queryAll();

        $companyChunkArr = array_chunk($delCompanyList ,1000);
        $total =0;
        foreach ($companyChunkArr as  $k =>  $companyItemList)
        {
            $delCompanyIds = [];
            $mvCompanyIds = [];
            foreach ($companyItemList as $item )
            {
                if( $item['user_id'] == '{}')
                {
                    $delCompanyIds[] = $item['company_id'];
                }else
                {
                    $mvCompanyIds[] = $item['company_id'];
                }
            }

            if(!empty($mvCompanyIds) )
            {
                $op = new \common\library\customer\CompanyBatchOperator($adminUserId);
                $op->setParams(['company_ids' => $mvCompanyIds,'show_all'=> true]);
                $count = $op->moveToPublic();
            }

            $delCompanyIds = array_merge($delCompanyIds, $mvCompanyIds);
            $op = new \common\library\customer\CompanyBatchOperator($adminUserId);
            $op->setParams(['company_ids' => $delCompanyIds]);
            //下次不再同步了
            $count = $op->delete('', 0);
            $total += $count;
            self::info("del k: {$k} ret: {$count} rel:".count($delCompanyIds).' company_ids: '.implode(',', $delCompanyIds));
        }

        self::info('finish :'.$total.'  '.count($delCompanyList));

    }


    public function actionFixCompanyNote($client_id, $company_id=0)
    {
        $db =  ProjectActiveRecord::getDbByClientId($client_id);
        $pgDb = PgActiveRecord::getDbByClientId($client_id);
        $sql = "select company_id from tbl_alibaba_company_relation where client_id={$client_id} and company_id>0 ";
        if( $company_id )
        {
            $sql .=  ' and company_id='.$company_id;
        }

        $companyList = $pgDb->createCommand($sql)->queryAll();
        foreach ($companyList as $item )
        {
            $sql = "select * from (
  SELECT trail_id,create_time,
  count(*) OVER(PARTITION BY create_time ) AS Row
  FROM tbl_dynamic_trail
	where client_id={$client_id} and company_id={$item['company_id']} and type=101 and enable_flag=1
) dups
where
dups.Row > 1";
            $list = $pgDb->createCommand($sql)->queryAll();
            $ids = array_column($list, 'trail_id');
            if( empty($ids))
                continue;

            $sql = "select trail_id,data from tbl_dynamic_trail_data  where client_id={$client_id} and trail_id in  (".implode(',', $ids).')';
            $dataList = $pgDb->createCommand($sql)->queryAll();
            $dataMap = array_column($dataList, 'data', 'trail_id');
            $sql = "select trail_id,alibaba_note_id from tbl_alibaba_customer_note_relation where client_id={$client_id} and trail_id in  (".implode(',', $ids).')';
            $relationMap = array_column($db->createCommand($sql)->queryAll(),'alibaba_note_id', 'trail_id');
            $retainMap =[];
            $delMap =[];
            foreach ($list as $elem)
            {
                $data = json_decode($dataMap[$elem['trail_id']], true);
                if( !isset($data['note_code']) )
                    continue;

                if( !isset($retainMap[$elem['create_time']]) )
                {
                    $retainMap[$elem['create_time']] = $elem;
                }else
                {
                    if( isset($relationMap[$elem['trail_id']]))
                    {
                        self::info("已存在关联表 ".$elem['trail_id']);
                        if( $retainMap[$elem['create_time']]['trail_id'] != $elem['trail_id'])
                        {
                            if( isset($delMap[$elem['create_time']]))
                            {
                                $delMap[$elem['create_time']][] = $retainMap[$elem['create_time']]['trail_id'];
                            }else
                            {
                                $delMap[$elem['create_time']] = [$retainMap[$elem['create_time']]['trail_id']];
                            }

                            $retainMap[$elem['create_time']] = $elem;

                        }
                        continue;
                    }

                    if( isset($delMap[$elem['create_time']]))
                    {
                        $delMap[$elem['create_time']][] = $elem['trail_id'];
                    }else
                    {
                        $delMap[$elem['create_time']] = [$elem['trail_id']];
                    }
                }
            }

            foreach ($delMap as $time  => $itemList)
            {
                if( !isset($retainMap[$time]))
                {
                    self::info('至少要保留一个 '.$time);
                    continue;
                }

                self::info("{$time}  retain: {$retainMap[$time]['trail_id']} delete {$item['company_id']}  trail_id".implode(',', $itemList));
                $delSql1 = 'update tbl_dynamic_trail set enable_flag =0  where trail_id in ('.implode(',', $itemList).')';
                $delSql2 = 'UPDATE tbl_follow_up SET enable_flag=0  WHERE trail_id in ('.implode(',', $itemList).')';
                self::info($delSql1);
                $pgDb->createCommand($delSql1)->execute();
                self::info($delSql2);
                $pgDb->createCommand($delSql2)->execute();

                \common\library\server\es_search\SearchQueueService::pushFollowUpQueueByTrailId(0, $client_id , $itemList, Constants::SEARCH_INDEX_TYPE_DELETE);
            }

        }
    }

    public function actionUpdateCompanyUser($client_id, $seller_account_id)
    {
        $adminUserId = \common\library\privilege_v3\PrivilegeService::getInstance($client_id)->getAdminUserId();
        User::setLoginUserById($adminUserId);

        /** @var CDbConnection $adminDb */
        $adminDb = Yii::app()->db;
        $toUserId = $adminDb->createCommand("select user_id from tbl_alibaba_store_members where client_id={$client_id} and seller_account_id={$seller_account_id} and enable_flag=1")->queryScalar();
        if( empty($toUserId) )
        {
            self::info('没有绑定用户');
            return false;
        }

        $db = ProjectActiveRecord::getDbByClientId($client_id);
        $pgDb = PgActiveRecord::getDbByClientId($client_id);
        $relationListObj = new \common\library\alibaba\customer\AlibabaCompanyRelationList($client_id);
        $relationListObj->setOwnerAccountId($seller_account_id);
        $relationListObj->setIsAdd(1);
        $relationListObj->setFields(['alibaba_company_id', 'company_id', 'store_id']);
        $relationListObj->find();
        $relationList = $relationListObj->find();
        $relationCompanyIds = array_column($relationList, 'company_id');
        $relationCompanyIds = array_filter($relationCompanyIds);
        if (empty($relationCompanyIds)) {
            self::info('没有同步记录');
            return false;
        }

        $companyListObj = new \common\library\customer_v3\company\list\CompanyList($adminUserId);
        $companyListObj->setSkipPrivilege(true);
        $companyListObj->setCompanyIds($relationCompanyIds);
        $companyListObj->setFields(['company_id', 'user_id']);
        $companyList  = $companyListObj->find();
        if( empty($companyList) )
        {
            self::info('没有同步记录客户');
            return false;
        }

        $companyMap = array_column($companyList, null, 'company_id');
        $updateCount = 0;
        $noAdminCount = 0;
        $inUserCount = 0;
        $companyUserIds = [];
        foreach ($relationCompanyIds as $companyId)
        {
            if( !isset($companyMap[$companyId]))
            {
                continue;
            }

            $info = $companyMap[$companyId];
            $userIds = \common\library\util\PgsqlUtil::trimArray($info['user_id']);
            if( in_array($toUserId, $userIds))
            {
                $inUserCount++;
                continue;
            }

            if( !in_array($adminUserId, $userIds))
            {
                $noAdminCount++;
                continue;
            }


            $data = [
                'client_id' => $client_id,
               'company_id' => $companyId,
               'admin_user_id' => $adminUserId,
               'old_user_id' => $info['user_id'],
               'new_user_id' => $toUserId,
            ];

            // 构建scope_user_ids
            $info['user_id'] = [$toUserId];
            $scopeUserIds = CompanyService::buildUpdateScopeUsers($client_id, $info, returnAsPgStr: true) ?? 'scope_user_ids';

            self::info("data:".json_encode($data));
            $companySql = "update tbl_company set user_id='{ $toUserId }', scope_user_ids=$scopeUserIds WHERE client_id=$client_id and company_id={$companyId} and is_archive=1";
            $customerSql = "update tbl_customer set user_id='{ $toUserId }' where client_id=$client_id and company_id={$companyId} and is_archive=1";
            self::info($companySql);
            self::info($customerSql);
            $pgDb->createCommand($companySql)->execute();
            $pgDb->createCommand($customerSql)->execute();

            $updateCount++;
        }

        self::info("finish client_id:  {$client_id}  seller_account_id: {$seller_account_id} sync_count:".count($relationCompanyIds).' company_count:'.count($companyList). " update_count: {$updateCount} noAdminCount: {$noAdminCount} inUserCount:{$inUserCount}");
    }

    //主账号每天定时检查aliStore是否即将过期,提前7天通知
    public function actionSendAliStoreTips($clientId = '')
    {
        \LogUtil::info("Start SendAliStoreTips");

        if($clientId)

        {
            $clientIds = [$clientId];
        }else
        {
            $nowTime = time();
            $lastTime = time() + 8*24*3600;
            $sql = "select client_id from tbl_alibaba_store where expire_time>{$nowTime} and expire_time <{$lastTime} and enable_flag = 1 and client_id > 0  group by client_id";
            $clientIds = \Yii::app()->db->createCommand($sql)->queryColumn();
        }
        \LogUtil::info("ali store expire in 7 days, client_id: ".implode(",", $clientIds));

        //$clientIds = [14292];
        try
        {
            foreach($clientIds as $clientId)
            {
                //已过期client跳过
                if (\common\library\account\Client::getClient($clientId)->getExpiredDays() > 0) {
                    LogUtil::info("clientId:{$clientId} is expire!");
                    continue;
                }

                $alibabaStore = new \common\library\alibaba\store\AlibabaStoreList($clientId);
                $alibabaStore->getFormatter()->listInfoSetting();
                $storeList = $alibabaStore->find();
                $processStoreLists = [];
                $processUserMap = [];
                $storeIds = [];
                $sendEmailStoreMap = [];
                $autoRenewStoreIds = [];
                foreach($storeList as $item)
                {
                    $time = $item['expire_time'] - time();
                    $oauthFlag = $item['oauth_flag'];
                    $enableFlag = $item['enable_flag'];
                    $userId = $item['create_user'];
                    $expireDay = floor($time/86400);
                    $itemValue = [
                        'store_id' => $item['store_id'],
                        'store_name' => $item['store_name'],
                        'seller_account_info' => $item['seller_account_info'],
                        'expire_time' => date('Y-m-d H:i:s', $item['expire_time']),
                        'expire_day' => $expireDay
                    ];
                    \LogUtil::info("[SendAliStoreTips - itemValue]client_id {$clientId} itemValue: ".json_encode($itemValue));
                    // 将在7天内过期店铺
                    if($expireDay <= 7 && ($oauthFlag == Constant::STORE_OAUTH_FLAG_AUTH || $enableFlag == Constant::AUTH_TYPE_ENABLE)) {
                        // 在当天内 确实过期
                        if($expireDay == 0 && $item['expire_time'] - time() < 0)
                        {
                            continue;
                        }

                        $processStoreLists[$item['store_id']] = $itemValue;
                        $processUserMap[$userId][$item['store_id']] = $itemValue;
                        $storeIds[] = $item['store_id'];

                        //7天这个节点发一次邮件
                        $expireDay == 7 && $sendEmailStoreMap[] = $itemValue;
                    }

                    if ($time < 2*24*3600 && ($enableFlag == Constant::STORE_TYPE_ENABLE && $oauthFlag == Constant::STORE_OAUTH_FLAG_AUTH)) {
                        $autoRenewStoreIds[] = $item['store_id'];
                    }
                    \LogUtil::info("[SendAliStoreTips - autoRenewStoreIds]client_id {$clientId} autoRenewStoreIds: ".json_encode($autoRenewStoreIds));
                }

                $storeIds = array_unique($storeIds);

                if($storeIds)
                {
                    \LogUtil::info("client_id: {$clientId} expire store ids: ".implode(",", $storeIds));
                    // 子账号关联的okki账号
                    $alibabaAccountList = new \common\library\alibaba\oauth\AlibabaAccountList($clientId);
                    $alibabaAccountList->setStoreId($storeIds);
                    $alibabaAccountList->setFields(['store_id','user_id']);
                    $alibabaAccountList->setOauthFlag([1,3]);
                    $accountList = $alibabaAccountList->find();
                    if($accountList)
                    {
                        foreach($accountList as $row)
                        {
                            $storeId = $row['store_id'];
                            $userId = $row['user_id'];
                            if(isset($processStoreLists[$storeId]))
                            {
                                $processUserMap[$userId][$storeId] = $processStoreLists[$storeId];

                            }
                        }
                    }
                }

                \LogUtil::info("client_id {$clientId} store expire in 7 days, ".json_encode($processUserMap));
                // 7天即将到期通知
                foreach($processUserMap as $userId => $storeList)
                {
                    User::setLoginUserById($userId);
                    $type = \common\library\notification\Constant::NOTIFICATION_TYPE_ALIBABA_STORE_EXPIRED_PROCESS;
                    $notification = new \common\library\notification\Notification($clientId, $type);
                    $notification->user_id = $userId;
                    $notification->create_user_id = $userId;
                    $notification->refer_id = 0;
                    $notification->browserData = array_values($storeList);
                    $notification->setSourceData([
                        'store_list'  => array_values($storeList)
                    ]);

                    PushHelper::pushNotification($clientId, $userId, $notification);

                }

                $adminInfo = PrivilegeService::getInstance($clientId)->getAdminInfo();

                if(empty($adminInfo)){
                    continue;
                }

                //发邮件
                foreach ($sendEmailStoreMap as $store) {
                    try {
                        \SystemNotifyMailUtil::sendAlibabaStoreToExpireNotify($adminInfo['email'], $store['store_name'] ?? '', $store['seller_account_info']['seller_nickname'] ?? '', $store['seller_account_info']['seller_email'] ?? '', $store['expire_time'] ?? '');
                    } catch (\Exception $exception) {
                        \LogUtil::info("[SendAliStoreTips - sendAlibabaStoreToExpireNotify] client_id {$clientId} sendAlibabaStoreToExpireNotify fail");
                        \LogUtil::info("[SendAliStoreTips - sendAlibabaStoreToExpireNotify] error msg :" . $exception->getMessage());
                        \LogUtil::info("[SendAliStoreTips - sendAlibabaStoreToExpireNotify] error :" . $exception->getTraceAsString());
                    }
                }

                usleep(20000);

                if (empty($autoRenewStoreIds)) {
                    continue;
                }

                $aliClient = \common\library\alibaba\services\AlibabaTopClient::getInstance();
                $storeTable = \AlibabaStore::model()->tableName();
                $sqlPrefix = "UPDATE {$storeTable} SET expire_time=CASE id ";
                $sqlArr = [];

                $renewStoreList = new \common\library\alibaba\store\AlibabaStoreList($clientId);
                $renewStoreList->setStoreIds($autoRenewStoreIds);
                $renewStoreList->setFields(['id', 'store_id', 'access_token']);
                $renewStoreList = $renewStoreList->find();
                $sqlSuffix = " END WHERE client_id={$clientId} AND id in (".implode(",",  array_column($renewStoreList, 'id')).")";
                $successStoreIds = [];
                foreach ($renewStoreList as $item) {
                    $renewStoreAccessToken = $item['access_token'];
                    $renewId = $item['id'];
                    $renewStoreId = $item['store_id'];
                    $expireTime = $aliClient->extendAuthTime($renewStoreAccessToken);
                    if ($expireTime) {
                        $sqlArr[] = " WHEN {$renewId} THEN {$expireTime} ";
                        $successStoreIds[] = $renewStoreId;
                    }
                }
                if (!empty($sqlArr)) {
                    $db = \Yii::app()->db;
                    $sql = $sqlPrefix.implode(" ", $sqlArr).$sqlSuffix;
                    $db->createCommand($sql)->execute();

                    \common\library\alibaba\cache\AlibabaStoreCacheableRepo::instance($clientId)->refreshCache();
                    \LogUtil::info("client_id {$clientId} auto extend store expire time for store_id: ".implode(",", $autoRenewStoreIds));

                    $alibabaAccountList = new \common\library\alibaba\oauth\AlibabaAccountList($clientId);
                    $alibabaAccountList->setStoreId($successStoreIds);
                    $alibabaAccountList->setFields(['id']);
                    $refreshTokenAccountIds = $alibabaAccountList->find();
                    $refreshTokenAccountIds = array_column($refreshTokenAccountIds, 'id');
                    $alibabaAccountService = new \common\library\alibaba\oauth\AlibabaAccountService($clientId, $adminInfo['user_id']);
                    try {
                        foreach ($refreshTokenAccountIds as $id) {
                            $alibabaAccountService->refreshToken($id);
                            \LogUtil::info("[SendAliStoreTips - refreshToken]client_id {$clientId} refreshToken id: {$id}");
                        }
                    } catch (\Exception $exception) {
                        \LogUtil::info("[SendAliStoreTips - refreshToken]client_id {$clientId} extend store success, refresh account token and expire time fail");
                        \LogUtil::info("[SendAliStoreTips - refreshToken] error msg :".$exception->getMessage());
                        \LogUtil::info("[SendAliStoreTips - refreshToken] error :".$exception->getTraceAsString());
                    }
                }
            }

        }catch (\Exception $e)
        {
            var_dump($e->getMessage());
            \LogUtil::error("SendAliStoreTips error :".$e->getMessage().$e->getTraceAsString());
        }

    }

    public function actionExtendAliStoreTime($clientIds = '') {
        \LogUtil::info("Start extent expired store auth time");

        if (!empty($clientIds)) {
            $clientIds = explode(",", $clientIds);
        } else {
            $nowTime = time();
            $lastTime = time() + 2*24*3600;
            $sql = "select client_id from tbl_alibaba_store where expire_time>{$nowTime} and expire_time <{$lastTime} and enable_flag = 1 and client_id > 0  group by client_id";
            $clientIds = \Yii::app()->db->createCommand($sql)->queryColumn();
        }

        foreach ($clientIds as $clientId) {
            try {
                //已过期client跳过
                if (\common\library\account\Client::getClient($clientId)->getExpiredDays() > 0) {
                    LogUtil::info("clientId:{$clientId} is expire!");
                    continue;
                }

                $alibabaStore = new \common\library\alibaba\store\AlibabaStoreList($clientId);
                $alibabaStore->getFormatter()->listInfoSetting();
                $storeList = $alibabaStore->find();
                $autoRenewStoreIds = [];
                foreach ($storeList as $item) {
                    $time = $item['expire_time'] - time();
                    $oauthFlag = $item['oauth_flag'];
                    $enableFlag = $item['enable_flag'];

                    if ($time < 2*24*3600 && ($enableFlag == Constant::STORE_TYPE_ENABLE && $oauthFlag == Constant::STORE_OAUTH_FLAG_AUTH)) {
                        $autoRenewStoreIds[] = $item['store_id'];
                    }
                }
                echo "client {$clientId} extend store auth time，store_id: ".implode(",", $autoRenewStoreIds)."\n";

                $aliClient = \common\library\alibaba\services\AlibabaTopClient::getInstance();
                $storeTable = \AlibabaStore::model()->tableName();
                $sqlPrefix = "UPDATE {$storeTable} SET expire_time=CASE id ";
                $sqlArr = [];

                $renewStoreList = new \common\library\alibaba\store\AlibabaStoreList($clientId);
                $renewStoreList->setStoreIds($autoRenewStoreIds);
                $renewStoreList->setFields(['id', 'access_token']);
                $renewStoreList = array_column($renewStoreList->find(),  'access_token', 'id');
                $sqlSuffix = " END WHERE client_id={$clientId} AND id in (".implode(",",  array_keys($renewStoreList)).")";
                foreach ($renewStoreList as $renewStoreId => $renewStoreAccessToken) {
                    $expireTime = $aliClient->extendAuthTime($renewStoreAccessToken);
                    if ($expireTime) {
                        $sqlArr[] = " WHEN {$renewStoreId} THEN {$expireTime} ";
                    }
                }
                if (!empty($sqlArr)) {
                    $db = \Yii::app()->db;
                    $sql = $sqlPrefix.implode(" ", $sqlArr).$sqlSuffix;
                    $db->createCommand($sql)->execute();

                    \common\library\alibaba\cache\AlibabaStoreCacheableRepo::instance($clientId)->refreshCache();
                }
            } catch (\Exception $exception) {
                var_dump($exception->getMessage());
                \LogUtil::error(" extent expired store auth time error :".$exception->getMessage().$exception->getTraceAsString());
            }
        }


    }

    /**
     * 失效触发通知
     * @param $clientId
     * @param $storeId
     * @throws ProcessException
     */
    public function actionSendExpireNotification($clientId, int $storeId)
    {
        try{
            $storeMapUserId = \common\library\alibaba\store\AlibabaStoreService::getStoreLinkOkkiAccount($clientId, [$storeId]);
            $aliStore = new \common\library\alibaba\store\AlibabaStore($clientId, $storeId);
            $aliStore->getFormatter()->detailInfoSetting();
            $data = $aliStore->getAttributes();

            // 非失效状态，不需要发通知
            if($data['enable_flag'] == 1)
            {
                \LogUtil::info("SendExpireNotification info clientId={$clientId},storeId={$storeId}目前不是失效状态");
                return;
            }
            if(isset($storeMapUserId[$storeId]))
            {
                $userIds = $storeMapUserId[$storeId];
                $userIds[] = $aliStore->create_user;
                $userIds = array_unique($userIds);
                $itemValue = [
                    'store_id' => $data['store_id'],
                    'store_name' => $data['store_name'],
                    'store_alias' => $data['store_alias'],
                    'seller_account_info' => $data['seller_account_info'],
                    'expire_time' => date('Y-m-d H:i:s', $data['expire_time']),
                    'unbind_time' => $data['unbind_time']
                ];
                foreach($userIds as $userId)
                {
                    User::setLoginUserById($userId);
                    $type = \common\library\notification\Constant::NOTIFICATION_TYPE_ALIBABA_STORE_EXPIRED;
                    $notification = new \common\library\notification\Notification($clientId, $type);
                    $notification->user_id = $userId;
                    $notification->create_user_id = $userId;
                    $notification->refer_id = 0;
                    $notification->browserData = $itemValue;
                    $notification->setSourceData($itemValue);

                    PushHelper::pushNotification($clientId, $userId, $notification);

                }

                $adminInfo = PrivilegeService::getInstance($clientId)->getAdminInfo();

                if(empty($adminInfo)){
                    return;
                }

                //发邮件
                $expireTime = date('Y-m-d H:i:s');
                \SystemNotifyMailUtil::sendAlibabaStoreExpiredNotify($adminInfo['email'], $itemValue['store_name']??'', $itemValue['seller_account_info']['seller_nickname']??'', $itemValue['seller_account_info']['seller_email']??'', $expireTime);

            }
        }catch (\Throwable $e)
        {
            \LogUtil::info("SendExpireNotification error clientId={$clientId},storeId={$storeId},error=".$e->getMessage());
        }

    }

    /**
     * 修复oauth_flag标志数据
     * @param int $client_id
     * @param null $lastNumber
     * @throws CDbException
     * @throws CException
     */
    public function actionFixOath($client_id=0, $lastNumber=null)
    {
        $clientList = $this->getClientList($client_id, false, null, null, 0, 0, $lastNumber);
        foreach ($clientList as $client)
        {
            $db =  AlibabaStore::model()->getDbConnection();
            $list =  $db->createCommand('select store_id,enable_flag,bind_time,oauth_flag,expire_time from tbl_alibaba_store  where client_id=:client_id and oauth_flag=0')->queryAll(true, [':client_id'=> $client['client_id']]);
            foreach ($list as $item )
            {
                $expireTime = $item['expire_time'];
                $enableFlag = $item['enable_flag'];
                $oauthFlag = 0;
                /**
                 * 授权有效的，对应oauth_flag=1
                 */
                if($enableFlag == Constant::STORE_TYPE_ENABLE)
                {
                    $oauthFlag = Constant::STORE_OAUTH_FLAG_AUTH;
                } else if($enableFlag == Constant::STORE_TYPE_DELETE && time() >= $expireTime)
                {
                    // 已正常已过期的
                    $oauthFlag = Constant::STORE_OAUTH_FLAG_EXPIRE;
                }else if($enableFlag == Constant::STORE_TYPE_DELETE && time() < $expireTime)
                {
                    // 未到期，但授权状态为0的，那么人为手动取消授权的
                    $oauthFlag = Constant::STORE_OAUTH_FLAG_CANCEL;
                }

                $sql = "update tbl_alibaba_store set oauth_flag={$oauthFlag} where store_id={$item['store_id']}";
                self::info($sql);
                $db->createCommand($sql)->execute();
            }
            $this->resetStoreCache($client['client_id']);
            usleep(30 * 1000);
        }
    }

    /**
     * 修正：阿里店铺授权时并发调用导致主账号授权过期时间与店铺授权过期时间不一致
     * @see https://www.tapd.cn/21404721/bugtrace/bugs/view/1121404721001069132
     */
    public function actionFixStoreAuthExpireTime(int $client_id, int $store_id)
    {
        $db = Yii::app()->db; /* @var CDbConnection $db */

        // 查询店铺的授权过期时间
        $sql = "select id,expire_time from tbl_alibaba_store where client_id=$client_id and store_id=$store_id and enable_flag=1";
        $storeInfo = $db->createCommand($sql)->queryRow();

        // 查询主账号的过期时间
        $sql = "select id,expire_time from tbl_alibaba_account where client_id=$client_id and store_id=$store_id and is_admin=1 and enable_flag=1";
        $accountInfo = $db->createCommand($sql)->queryRow();

        if (empty($storeInfo) || empty($accountInfo)) {
            throw new ProcessException("店铺信息不存在");
        }
        LogUtil::info("店铺=$store_id 店铺到期时间 {$storeInfo['expire_time']} 主账号={$accountInfo['id']} 到期时间 {$accountInfo['expire_time']}");

        if ($accountInfo['expire_time'] > $storeInfo['expire_time'])
        {
            LogUtil::info("修改店铺=$store_id 到期时间为 {$accountInfo['expire_time']}");
            $sql = "update tbl_alibaba_store set expire_time={$accountInfo['expire_time']} where id={$storeInfo['id']} limit 1";
            $db->createCommand($sql)->execute();
        }

        $this->resetStoreCache($client_id);
    }

    //同步差异产品
    public function actionSyncDiffProduct($store_id,$user_id,$client_id = 0)
    {

        LogUtil::info("syncDiffProductStart:clientId:{$client_id} user_id:{$user_id} store_id:{$store_id}");
        User::setLoginUserById($user_id);
        $user = User::getLoginUser();
        if(!$client_id){
            $clientId = $user->getClientId();
        }else{
            $clientId = $client_id;
        }
        $userId = $user->getUserId();
        $alibabaStore = new \common\library\alibaba\store\AlibabaStore($clientId,$store_id);
        if($alibabaStore->isNew()|| !$alibabaStore->access_token || !$alibabaStore->oauth_flag){
            throw  new RuntimeException('店铺授权失效');
        }
        $crmProductList = new \common\library\product\ProductList($clientId);
        $crmProductList->setSourceTypes([1]);
        $crmProductList->setAliStoreIds([$store_id]);
        $crmProductList->setFields(['product_id','ali_product_id']);
        $list = $crmProductList ->find();
        $crmAliProductId = array_column($list,'ali_product_id');

        $token = $alibabaStore->access_token;
        $currentPage = 1;//页码
        $pageSize = 30;//页码
        $totalCount = 0;//返回总数
        $pageCount = 0;
        $aliProductIdMap = [];
        $allAliProductIds = [];
        $allAliProductMaps = [];
        $diffProductList = [];
        do {
            $productList = new \common\library\alibaba\services\AlibabaProductList($token);
            $productList->setCurrentPage($currentPage);
            $productList->setPageSize($pageSize);
            $rsp = $productList->find();

            if (!isset($rsp['total_item']) || $rsp['total_item'] <= 0) {
                die('no_data');
            }
            if ($currentPage === 1)
            {
                $totalCount = $rsp['total_item'];
                $pageCount = ceil($totalCount/ $pageSize);
            }
            $data = $rsp['products']['alibaba_product_brief_response'] ?? [];
            if (empty($data))
            {
                die("no_product");
            }
            foreach ($data as $item){
                $allAliProductIds[] = $item['id'];
                $allAliProductMaps[$item['id']] = $item['product_id'];
            }
            $currentPage++;

        } while ($currentPage <= $pageCount);

        $diffProductList = array_diff($allAliProductIds,$crmAliProductId);
        $diffProductList = array_unique($diffProductList);
        $diffProductList = array_values($diffProductList);
        $fileName = "/tmp/diff_product_{$store_id}.txt";
        $myfile = fopen($fileName, "w");
        fwrite($myfile, json_encode($diffProductList));
        fclose($myfile);

        echo "diffProductCount".count($diffProductList)."\r\n";
        var_dump($diffProductList);

        if(!$diffProductList){
            throw  new RuntimeException('没有差异产品!');
        }
        foreach ($diffProductList as $id){
            $aliProductId = $allAliProductMaps[$id];
            $syncExecutor = new \common\library\alibaba\product\AlibabaProductSyncExecutor($clientId, $alibabaStore);
            $crmProductId = $syncExecutor->saveByAliProductId($aliProductId);
            $msg = "ali_product_id:{$id} crm_product_id:{$crmProductId}\r\n";
            echo $msg;
            LogUtil::info($msg);
        }


    }

    public function actionSyncCustomerNote($clientId, $companyId=0)
    {
        $db = PgActiveRecord::getDbByClientId($clientId);
        $adminUserId = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();
        User::setLoginUserById($adminUserId);
        $listObj = new \common\library\alibaba\customer\AlibabaCompanyRelationList($clientId);
        $listObj->setIsSync(true);
        $listObj->setFields(['relation_id','alibaba_company_id','company_id', 'store_id']);
        if( $companyId)
        {
            $listObj->setCompanyId($companyId);
        }

        $relationList = $listObj->find();
        if( empty($relationList ))
        {
            self::info("client_id: {$clientId} relation list empty!");
        }
        $storeTokenMap =[];
        $processorMap =[];
        foreach ($relationList as $item )
        {
            $storeId = $item['store_id'];
            if( !isset($storeTokenMap[$storeId]))
            {
                $store = new \common\library\alibaba\store\AlibabaStore($clientId, $storeId);
                $storeTokenMap[$storeId] = $store->getAccessToken();
            }

            $token = $storeTokenMap[$storeId];
            if( empty($token))
            {
                self::info("client_id: {$clientId}  id: {$item['relation_id']} company_id {$item['company_id']} token empty fail! continue" );
                continue;
            }

            $companyRelation = new \common\library\alibaba\customer\AlibabaCompanyRelation($clientId, $item['relation_id']);
            if( $companyRelation->isNew() || !$companyRelation->company_id)
            {
                self::info("client_id: {$clientId}  id: {$item['relation_id']} company_id {$item['company_id']} relation empty fail! continue" );
                continue;
            }

            if( !isset($processorMap[$storeId]))
            {
                $processor =  new \common\library\alibaba\customer\AlibabaCustomerSyncProcessor($clientId, $adminUserId, $storeId);
                $processor->setSessionKey($token);
                $processor->initDefaultSetting();
                $processorMap[$storeId] = $processor;
            }

            $processor = $processorMap[$storeId];
//            $forceSyncNoteFlag = $companyRelation->sync_note_flag == Constant::SYNC_STATUS_FAIL;
            $forceSyncNoteFlag = true;
            $maxNoteId = $processor->processNote($companyRelation->company_id, $companyRelation->alibaba_company_id, [], $forceSyncNoteFlag , $companyRelation->sync_max_note_id);
            $companyRelation->sync_note_flag = $maxNoteId === false? Constant::SYNC_STATUS_FAIL: Constant::SYNC_STATUS_FINISH;
            if( $maxNoteId && $maxNoteId > $companyRelation->sync_max_note_id )
            {
                $companyRelation->sync_max_note_id = $maxNoteId;
            }
            $companyRelation->save();
            self::info("client_id: {$clientId}  id: {$item['relation_id']}  company_id: {$companyRelation->company_id} alibaba_company_id {$companyRelation->alibaba_company_id} maxId: {$maxNoteId} ");

        }


    }

    /**
     * 每天定期同步7天内保护期内的客户
     */
    public function actionSyncInProtectingCustomer()
    {
        $dbSetList = \common\library\account\service\DbService::getDbList(DbSet::TYPE_PGSQL);
        $startDate= date('Y-m-d', strtotime('-7day'));
        $endDate = date('Y-m-d',strtotime('+1day'));

        foreach($dbSetList as $dbSet) {
            try {
                $db = PgActiveRecord::getDbByDbSetId($dbSet['set_id']);
                $sql = "select client_id, store_id,alibaba_company_id from tbl_alibaba_company_relation where create_time>:startDate and create_time<=:endDate and is_protecting=:is_protecting";
                $params = [
                    ':is_protecting' => 1,
                    ':startDate' => $startDate,
                    ':endDate' => $endDate
                ];

                $result = $db->createCommand($sql)->queryAll(true, $params);
                $syncStoreMap = [];
                foreach ($result as $item) {
                    try {
                        $client_id = $item['client_id'];
                        $store_id = $item['store_id'];
                        $user_id = \common\library\privilege_v3\PrivilegeService::getInstance($client_id)->getAdminUserId();
                        User::setLoginUserById($user_id);
                        $alibaba_company_id = $item['alibaba_company_id'];
                        if (!isset($syncStoreMap[$store_id])) {
                            $alibabaStore = new AlibabaStore($client_id, $item['store_id']);
                            $sessionKey = $alibabaStore->getAccessToken();
                            if (!$sessionKey || !$alibabaStore->sync_customer_flag) {
                                $syncStoreMap[$store_id] = false;
                                \LogUtil::info("[actionSyncInProtectingCustomer] clientId={$client_id} continue  storeId={$item['store_id']},alibabaCompanyId={$alibaba_company_id} ");
                                continue;
                            }

                            $syncStoreMap[$store_id] = true;
                        } elseif (!$syncStoreMap[$store_id]) {
                            \LogUtil::info("[actionSyncInProtectingCustomer] clientId={$client_id} continue  storeId={$item['store_id']},alibabaCompanyId={$alibaba_company_id} ");
                            continue;
                        }
                        \common\library\alibaba\customer\AlibabaCustomerService::syncSingleCustomer(
                            $client_id,
                            $user_id,
                            0,
                            $alibaba_company_id,
                            0,
                            0,
                            Constant::SYNC_TYPE_RESYNC
                        );

                    } catch (Exception $e) {
                        \LogUtil::info("syncSingleCustomer error clientId={$client_id},storeId={$item['store_id']},error=" . $e->getMessage());
                    }

                }

            } catch (\Throwable $e) {
                \LogUtil::info("SyncInProtectingCustomer error msg=" . $e->getMessage());
            }

            \User::cleanUserMap();
            Client::cleanCacheMap();
        }

    }

    public function actionCleanSyncCustomerError()
    {
        $db  = AlibabaStore::model()->getDbConnection();
        $storeList = $db->createCommand('select store_id, client_id from tbl_alibaba_store where enable_flag=1 and sync_customer_flag=0')->queryAll();
        foreach ($storeList as $store)
        {
            $clientId = $store['client_id'];
            $storeId = $store['store_id'];

            $mysqlDb = ProjectActiveRecord::getDbByClientId($clientId);
            $existTask = $mysqlDb->createCommand("select * from tbl_alibaba_customer_sync_task where client_id={$clientId} and  store_id={$storeId} and create_time >='2021-08-10 10:00:00' and task_type=1")->queryRow();
            if( !empty($existTask))
            {
                self::info("{$clientId} {$storeId} 存在任务,跳过");
                continue;
            }

            $pgDb = PgActiveRecord::getDbByClientId($clientId);
            $alibabaCompanyIds = $pgDb->createCommand("select refer_id from tbl_alibaba_sync_history where client_id={$clientId} and store_id={$storeId} and create_time >='2021-08-10 10:00:00' and create_time <='2021-08-11 11:30:00' and type=3")->queryColumn();
            if( empty($alibabaCompanyIds))
            {
                self::info("{$clientId} {$storeId} 没有进行过推送同步,跳过");
                continue;
            }

            $companyIds = $pgDb->createCommand("select company_id from tbl_alibaba_company_relation where client_id={$clientId} and store_id={$storeId} and create_time >='2021-08-10 10:00:00' and  create_time <='2021-08-11 11:30:00' and is_add=1 and alibaba_company_id in (".implode(',', $alibabaCompanyIds).")")->queryColumn();

            if( empty($companyIds) )
            {
                self::info("{$clientId} {$storeId} 没有进行过同步客户,跳过");
                continue;
            }

            $delCompanyList = $pgDb->createCommand("select company_id, user_id from tbl_company where client_id={$clientId} and is_archive =1  and company_id in (".implode(',', $companyIds).')')->queryAll();

            if( empty($delCompanyList) )
            {
                self::info("{$clientId} {$storeId} 没有需求删除客户,跳过");
            }

            self::info("{$clientId} {$storeId} 删除的客户id: ".json_encode($delCompanyList));

            $adminUserId = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();
            if( !$adminUserId )
            {
                continue;
            }

            User::setLoginUserById($adminUserId);

            $delCompanyIds = [];
            $mvCompanyIds = [];
            foreach ($delCompanyList as $item )
            {
                if( $item['user_id'] == '{}')
                {
                    $delCompanyIds[] = $item['company_id'];
                }else
                {
                    $mvCompanyIds[] = $item['company_id'];
                }
            }

            if(!empty($mvCompanyIds) )
            {
                $op = new \common\library\customer\CompanyBatchOperator($adminUserId);
                $op->setParams(['company_ids' => $mvCompanyIds,'show_all'=> true]);
                $count = $op->moveToPublic();
            }

            $delCompanyIds = array_merge($delCompanyIds, $mvCompanyIds);

            if( empty($delCompanyIds) )
            {
                continue;
            }

            $op = new \common\library\customer\CompanyBatchOperator($adminUserId);
            $op->setParams(['company_ids' => $delCompanyIds]);
            //下次不再同步了
            $count = $op->delete();

        }

    }

    /**
     * 处理历史多店铺同步配置
     */
    public function actionHandleMultiStoreSetting()
    {
        $sql = 'select distinct client_id from tbl_alibaba_store order by id';

        $offset = 0;
        $pageSize = 1000;
        $db = Yii::app()->db;
        do {
            $query = "{$sql} limit {$offset}, {$pageSize}";
            $clientIds = $db->createCommand($query)->queryAll();
            $clientIds = array_column($clientIds, 'client_id');

            $now = date('Y-m-d H:i:s');
            foreach ($clientIds as $clientId) {
                if (!$clientId) continue;

                LogUtil::info("[start] client_id: {$clientId}");

                $itemId = ProjectActiveRecord::produceAutoIncrementId();
                $query = "insert into tbl_item_setting (item_id, client_id, item_name, attributes, description, module, item_type, create_time, update_time) values ({$itemId}, {$clientId}, 'customer_multi_store_handle', 'mutex', '', 5, 11, '{$now}', '{$now}')";
                $clientDB = ProjectActiveRecord::getDbByClientId($clientId);
                $clientDB->createCommand($query)->execute();

                LogUtil::info("[success] client_id: {$clientId}, setting_id: {$itemId}");
            }

            $offset += $pageSize;

        } while ($clientIds);

        echo 'success', PHP_EOL;

    }

    public function actionSyncInProtectingCustomerByClient($clientId)
    {
        $task = new \common\library\server\crontab\task\AlibabaCustomerTask();
        $task->runAction('runSyncInProtecting', [$clientId]);
    }


    /**
     * 处理历史线索关联ali_company_id
     */
    public function actionHandleTradeRelation(string $client_ids = null, int $last_num = null)
    {
        if ($client_ids) {
            $client_ids = explode(',', $client_ids);
        }
        $iterator = $this->iteratorOfAllClients($client_ids, $last_num);
        foreach ($iterator as $item) {
            /** @var $item \Client */
            LogUtil::info("[HandleTradeRelation] client_id: {$item->client_id}");

            $conn = ProjectActiveRecord::getDbByClientId($item->client_id);

            $list = new \common\library\alibaba\oauth\AlibabaAccountList($item->client_id);
            $list->setOauthFlag([1,3]);
            $list->setFields(['store_id', 'access_token']);

            $sql = "select store_id, access_token from tbl_alibaba_account
                    where oauth_flag in (3) and client_id={$item->client_id} and is_admin=1";
            $accounts = Yii::app()->db->createCommand($sql)->queryAll();
            $accessTokens = array_column($accounts, 'access_token', 'store_id');

            foreach ($this->iteratorOfTradeRelations($item->client_id) as $tradeRelations) {

                if (!$tradeRelations) {
                    continue;
                }

                // 已存在
                $relations = [];
                $buyerAccountIds = array_unique(array_column($tradeRelations, 'buyer_account_id'));
                if ($buyerAccountIds) {
                    $buyerAccountIds = implode(',', $buyerAccountIds);
                    $sql = "select co.alibaba_company_id,co.store_id,co.buyer_account_id,cu.alibaba_customer_id,co.detail_url,co.owner_email,co.owner_account_id,co.alibaba_company_name from tbl_alibaba_company_relation co
                            left join tbl_alibaba_customer_relation cu on co.client_id=cu.client_id and co.alibaba_company_id=cu.alibaba_company_id
                            where co.client_id={$item->client_id} and co.buyer_account_id in ($buyerAccountIds) and co.company_id<>0";

                    $relations = PgActiveRecord::getDbByClientId($item->client_id)->createCommand($sql)->queryAll();
                    $relations = ArrayUtil::keyBy($relations, function ($value) {
                        return "{$value['store_id']}_{$value['buyer_account_id']}";
                    });
                }

                $buyerMembers = []; // 缓存
                $now = date('Y-m-d H:i:s');
                foreach ($tradeRelations as $tradeRelation) {

                    if (!isset($accessTokens[$tradeRelation['store_id']])) {
                        LogUtil::info("No access token. client_id: {$item->client_id}, store_id: {$tradeRelation['store_id']}");
                        continue;
                    }

                    $key = "{$tradeRelation['store_id']}_{$tradeRelation['buyer_account_id']}";
                    if (empty($buyerMembers[$key])) {
                        if (!empty($relations[$key])) {
                            $buyerMembers[$key] = [
                                'alibaba_company_id'  => $relations[$key]['alibaba_company_id'] ?? 0,
                                'alibaba_customer_id' => $relations[$key]['alibaba_customer_id'] ?? 0,
                                'detail_url' => $relations[$key]['detail_url'] ?? '',
                                'owner_email' => $relations[$key]['owner_email'] ?? '',
                                'owner_account_id' => $relations[$key]['owner_account_id'] ?? 0,
                                'alibaba_company_name' => $relations[$key]['alibaba_company_name'] ?? '',
                            ];
                        } else {
                            $buyerMemberInfo = CustomerSyncHelper::getAlibabaBuyerInfo($accessTokens[$tradeRelation['store_id']], $tradeRelation['buyer_account_id']);
                            if (!$buyerMemberInfo) {
                                \LogUtil::info("buyerMemberInfo empty, clientId: {$item->client_id}, buyerAccountId: {$tradeRelation['buyer_account_id']}, accessToken: {$accessTokens[$tradeRelation['store_id']]}");
                                continue;
                            }

                            $buyerMembers[$key] = [
                                'alibaba_company_id' => $buyerMemberInfo['alibaba_company_id'] ?? 0,
                                'alibaba_customer_id' => $buyerMemberInfo['alibaba_customer_id'] ?? 0,
                                'detail_url' => $buyerMemberInfo['detail_url'] ?? '',
                                'owner_email' => $buyerMemberInfo['owner_email'] ?? '',
                                'owner_account_id' => $buyerMemberInfo['owner_account_id'] ?? 0,
                                'alibaba_company_name' => $buyerMemberInfo['company_name'] ?? '',
                            ];
                        }

                    }

                    $buyerMemberInfo = $buyerMembers[$key];

                    $sql = 'update tbl_alibaba_trade_relation
                            set ali_company_id=:ali_company_id, ali_customer_id=:ali_customer_id, ali_company_detail_url=:detail_url, owner_account_id=:owner_account_id, owner_email=:owner_email, ali_company_name=:ali_company_name, update_time=:now
                            where relation_id=:where_relation_id';
                    $params = [
                        ':ali_company_id' => $buyerMemberInfo['alibaba_company_id'] ?? 0,
                        ':ali_customer_id' => $buyerMemberInfo['alibaba_customer_id'] ?? 0,
                        ':ali_company_name' => $buyerMemberInfo['alibaba_company_name'] ?? '',
                        ':detail_url' => $buyerMemberInfo['detail_url'] ?? '',
                        ':owner_account_id' => $buyerMemberInfo['owner_account_id'] ?? 0,
                        ':owner_email' => $buyerMemberInfo['owner_email'] ?? '',
                        ':where_relation_id' => $tradeRelation['relation_id'],
                        ':now' => $now,
                    ];

                    $result = $conn->createCommand($sql)->execute($params);

                    $msg = "success. relation_id: {$tradeRelation['relation_id']}, ali_company_id: {$params[':ali_company_id']}, ali_customer_id: {$params[':ali_customer_id']}, result: {$result}";
                    echo $msg, PHP_EOL;
                    LogUtil::info($msg);

                }

            }

            $conn->setActive(false);
        }

        echo 'success', PHP_EOL;
    }

    /**
     * 所有clientId迭代器
     * @param int|null $onesDigit
     * @return \Generator
     */
    private function iteratorOfAllClients(array $clientIds = null, int $onesDigit = null)
    {
        $sql = '1=1';
        if ($clientIds !== null) {
            yield from $this->getClientList($clientIds);
            return;
        }

        if ($onesDigit !== null) {
            $sql = "client_id like '%{$onesDigit}'";
        }
        $sql .= ' order by client_id desc';

        $maxClient = Client::model()->find($sql);

        if (!$maxClient) {
            return;
        }
        $maxClientId = $maxClient->client_id;

        $startClientId = 1;
        while ($startClientId <= $maxClientId) {
            $endClientId = $startClientId + 2000;

            $clients = $this->getClientList(0, false, null, null, $startClientId, $endClientId, $onesDigit);
            yield from $clients;

            if ($clients && end($clients)->client_id > $endClientId) {
                $endClientId = end($clients)->client_id;
            }
            $startClientId = $endClientId + 1;
        }
    }

    /**
     * TradeRelation迭代器
     * @param $clientId
     * @param int $limit
     * @return \Generator
     * @throws \CDbException
     * @throws \CException
     * @throws \ProcessException
     */
    private function iteratorOfTradeRelations($clientId, $limit = 1000)
    {
        $conn = ProjectActiveRecord::getDbByClientId($clientId);
        if (!$conn) {
            return;
        }

        $sql = 'select relation_id,buyer_account_id,store_id,seller_account_id from tbl_alibaba_trade_relation
                where client_id=%d and relation_id>%d and store_id!=0 and buyer_account_id!=0 and ali_company_id=0
                order by relation_id limit %d';

        $lastId = 0;
        do {

            $query = sprintf($sql, $clientId, $lastId, $limit);
            $data = $conn->createCommand($query)->queryAll();

            yield $data;

            if (!$data) {
                break;
            }

            $lastId = end($data)['relation_id'];

        } while ($data);

    }

    public function actionCleanRecycle()
    {
        $path = '/root/log_cleansynccustomererror';
        $fd = fopen($path, 'r');

        $clientIds = [
            14561,
32897,
16119,
52720,
28185,
46705,
1204,
22551,
51374,
31165,
8112,
            34663,
55846,
36809,
2443,
31165,
5406,
34525,
54253,
32897,
6361,
            50170,
            31165,
        ];

        $clientMap = [];
        while ($row =  fgets($fd)){
            if( strpos($row, '删除的客户id')  ==false)
            {
                continue;
            }

            $pos = strpos($row, '|||') +3;
            $str = substr($row, $pos);
            $arr = explode(' ', $str);
            list($clientId, $storeId, ,$companyIdStr) =  $arr;

            if( in_array($clientId, $clientIds) )
            {
                continue;
            }

            $companyIds = json_decode($companyIdStr, true);

            if( empty($companyIds))
                continue;

            if( is_array($companyIds[0]))
            {
                $companyIds = array_column($companyIds,'company_id');
            }

            $companyIds = array_filter($companyIds);

            if( empty($companyIds))
                continue;


            $pg = PgActiveRecord::getDbByClientId($clientId);
            $delCompanyIds = $pg->createCommand("select company_id from tbl_company where client_id={$clientId} and is_archive =0  and company_id in (".implode(',', $companyIds).')')->queryColumn();
            if( empty($delCompanyIds) )
                continue;

            $db = ProjectActiveRecord::getDbByClientId($clientId);
           $list =  $db->createCommand("select refer_id,create_time  from tbl_recycle where  client_id={$clientId} and create_time>='2021-08-11 18:10:00' and create_time<='2021-08-11 18:59:00' and type=2 and delete_flag=1 and refer_id in (".implode(',', $delCompanyIds).')')->queryAll();
//           var_dump($clientId, $list); die;
           $referIds = array_filter(array_column($list, 'refer_id'));

           if( empty($referIds))
               continue;

           $sql = "update tbl_recycle set delete_flag=2 where  client_id={$clientId} and create_time>='2021-08-11 18:10:00' and create_time<='2021-08-11 19:00:00' and type=2 and delete_flag=1 and refer_id in (".implode(',', $referIds).')';
           $r = $db->createCommand($sql)->execute();
            self::info("$clientId $storeId 清理回收箱companyIds: ".json_encode($referIds));

            if( isset($clientMap[$clientId]))
                $clientMap[$clientId] = array_values(array_unique(array_merge($clientMap[$clientId], $referIds)));
            else
            {
                $clientMap[$clientId] = $referIds;
            }
            //清理系统消息
            $delMsgIds =[];
            $userIds = array_column(UserInfo::findAllByClientId($clientId), 'user_id');
            $list = $db->createCommand("select notification_id,data, type,refer_id,read_flag   from tbl_notification where client_id={$clientId} and create_time>='2021-08-11 18:10:00' and create_time<='2021-08-11 18:50:00' and type=308 and enable_flag=1 and user_id in (".implode(',', $userIds).')')->queryAll();
            foreach ($list as $item)
            {
                $companyObjList = json_decode($item['data'], true)['company_data'];
                $msgCompanyIds = array_column($companyObjList, 'company_id');
                if( array_intersect($msgCompanyIds, $delCompanyIds) )
                {
                    $delMsgIds[] = $item['notification_id'];
                }
            }

            if( !empty($delMsgIds))
            {
                            $r = $db->createCommand("update  tbl_notification set enable_flag=0  where notification_id in (".implode(',', $delMsgIds).") and enable_flag=1")->execute();
                self::info("$clientId $storeId 清理通知: ".json_encode($delMsgIds));
            }


        }

        self::info("client count: ".count($clientMap));

        $resultPath = '/tmp/清理涉及的client-明细.csv';
        @unlink($resultPath);
        $fa = fopen($resultPath, 'w');
        fputcsv($fa, ['client_id', 'count', 'company_id']);
        foreach ($clientMap as $key => $companyIds)
        {
            fputcsv($fa, [$key, count($companyIds), implode('|',$companyIds)]);
        }
    }

    public function actionUpdateSyncOwnerChangeSetting($clientId=0)
    {
        $clientList = $this->getClientList($clientId);
        $total =0;
        /** @var \CDbConnection $db */
        $db = Yii::app()->db;
        foreach ($clientList as $client)
        {
            $sql = "select sync_customer_flag,store_id,enable_flag from tbl_alibaba_store where client_id={$client['client_id']}";
            $list = $db->createCommand($sql)->queryAll();
            if(empty($list) || count($list) >1)
                continue;

            $storeId = $list[0]['store_id'];
            $enableFlag = $list[0]['enable_flag'];
            if( !$enableFlag)
                continue;

            $updateSql = "update  tbl_alibaba_customer_sync_setting set sync_owner_change=1 where client_id={$client['client_id']} and  store_id=$storeId";
            $ret = $db->createCommand($updateSql)->execute();
            if($ret)
                $total++;
            self::info("update tbl_alibaba_customer_sync_setting {$client['client_id']} {$storeId}");
        }

        self::info('total:'.$total);
    }


    //生成阿里消息日志表
    public function actionCreateMessageTable($year)
    {
        if(empty($year) || $year < date('Y')){
            throw new RuntimeException('请输入正确年份!');
        }

        common\library\alibaba\services\TableHelper::createMessageTable($year);

    }

    //阿里消息日志表添加消息时间字段（仅限2021、2022年需要执行该脚本）
    public function actionMessageTableAddMessageTime($year)
    {
        $yearArr = ['2021', '2022'];
        if(empty($year) || $year < date('Y') || !in_array($year,$yearArr)){
            throw new RuntimeException('请输入正确年份!');
        }

        common\library\alibaba\services\TableHelper::addMessageTime($year);

    }

    //阿里消息日志表更新消息时间
    public function actionMessageTableUpdateMessageTime($year)
    {
        if(empty($year) || $year < date('Y')){
            throw new RuntimeException('请输入正确年份!');
        }

        common\library\alibaba\services\TableHelper::updateMessageTime($year);

    }


    /**
     * 处理线上的产品同步配置
     * is_overrider => 1
     */
    public function actionHandleOldProductSetting($clientId = null, $storeIds = null)
    {

        $storeIds && $storeIds = explode(',', $storeIds);

        $settingList = new AlibabaProductSyncSettingModel();

        $sql = "select setting_id from {$settingList->tableName()} where";
        $where = ' is_override=0';
        $params = [];

        if ($clientId) {
            $where .= ' and client_id=:client_id';
            $params[':client_id'] = $clientId;

            if ($storeIds) {
                $storeIds = implode(',', $storeIds);
                $where .= " and store_id in ({$storeIds})";
            }
        }

        $limit = 10;
        $lastId = 0;
        $count = 0;

        do {
            $raw = "{$sql}{$where} and setting_id>{$lastId} order by setting_id limit {$limit}";

            $data = $settingList->dbConnection->createCommand($raw)->queryAll(true, $params);


            if (!$data) {
                break;
            }

            $settingIds = array_column($data, 'setting_id');
            LogUtil::info('setting_ids: ' . implode(',', $settingIds));
            $criteria = new CDbCriteria();
            $criteria->addInCondition('setting_id', $settingIds);
            $criteria->addCondition('is_override=0');

            $count += $settingList->updateAll(['is_override' => 1], $criteria);

            $lastId = end($data)['setting_id'];

        } while ($data);

        var_dump($count);
    }

    public function actionSyncAliOwner($clientId, $storeId)
    {

        $userId = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();
        User::setLoginUserById($userId);

        $alibabaStore = new \common\library\alibaba\store\AlibabaStore($clientId, $storeId);
        if( $alibabaStore->isNew() )
        {
            throw new RuntimeException('商店不存在');
        }

        $untreatedTask = (new \common\library\alibaba\customer\AlibabaCustomerSyncTask($clientId))->loadByUntreated($storeId, Constant::SYNC_TASK_TYPE_ALL);
        if( !$untreatedTask->isNew() )
        {
            throw new RuntimeException('有进行中的任务未完成');
        }

        $customerSyncSetting = $alibabaStore->getCustomerSyncObj();
        $customerStatus =  json_decode($customerSyncSetting->customer_status)?:[];
        if( empty($customerStatus))
        {
            throw new RuntimeException('请先完映射关系设置');
        }

        //多店铺情况需要验证
        if(count(AlibabaService::getInstance($clientId)->getStoreList()) > 1){
            if (!$alibabaStore->getSyncSettingObj()->getSetting(0, SyncSettingConstant::CUSTOMER_MULTI_STORE_KEY)) {
                throw new RuntimeException('请先完成多店铺同步设置');
            }
        }

        $alibabaCustomerSync = new \common\library\alibaba\customer\AlibabaCustomerSyncTask($clientId);
        $alibabaCustomerSync->task_type = Constant::SYNC_TASK_TYPE_ALL;
        $nowTime = date('Y-m-d H:i:s',time());
        $alibabaCustomerSync->client_id = $clientId;
        $alibabaCustomerSync->user_id = $userId;
        $alibabaCustomerSync->store_id = $storeId;
        $alibabaCustomerSync->pool_id = $customerSyncSetting->pool_id;
        $alibabaCustomerSync->customer_status = json_encode($customerStatus);
        $alibabaCustomerSync->status = Constant::SYNC_FLAG_START;
        $alibabaCustomerSync->fail_reason = 0;
        $alibabaCustomerSync->result_line = '';
        $alibabaCustomerSync->create_time = $nowTime;
        $alibabaCustomerSync->archive_time_start = $customerSyncSetting->archive_time_start;
        $alibabaCustomerSync->archive_time_end = $customerSyncSetting->archive_time_end;

        $alibabaCustomerSync->update_type = Constant::UPDATE_TASK_TYPE_APPEND;
        $alibabaCustomerSync->update_owner_flag = Constant::UPDATE_ENABLE_FLAG;
        $alibabaCustomerSync->update_status_flag = Constant::UPDATE_DISABLE_FLAG;
        $result = $alibabaCustomerSync->save();
        $alibabaCustomerSync->run();
        self::info("SyncAliOwner:clientId:{$clientId},storeId: {$storeId} 同步成功 ");

    }


    public function actionRepairAlibabaChat($client_id, $seller_account_id, $buyer_account_id,$store_id,$time )
    {
        $userId = \common\library\privilege_v3\PrivilegeService::getInstance($client_id)->getAdminUserId();
        User::setLoginUserById($userId);

        $alibabaChatSummary = new AlibabaChatSummary($client_id);
        $alibabaChatSummary->loadBySellerAccountAndBuyerAccount($seller_account_id,$buyer_account_id,$store_id);

        $alibabaAccount = new AlibabaAccount();
        $alibabaAccount->loadBySellAccountId($seller_account_id);

        if($alibabaChatSummary->isNew()){
            $alibabaChatSummary->client_id = $client_id;
            $alibabaChatSummary->summary_id = \ProjectActiveRecord::produceAutoIncrementId($client_id);
            $alibabaChatSummary->seller_account_id = $seller_account_id;
            $alibabaChatSummary->seller_email = $alibabaAccount->seller_email;
            $alibabaChatSummary->buyer_account_id = $buyer_account_id;
            $alibabaChatSummary->store_id = $alibabaAccount->store_id;
            $alibabaChatSummary->count = 1;
            $alibabaChatSummary->recent_time = $time;
            $alibabaChatSummary->create_time = $time;
            $alibabaChatSummary->update_time = $time;
            $alibabaChatSummary->last_trade_type = 1;
            $alibabaChatSummary->receive_count = 1;
            $alibabaChatSummary->last_receive_time = $time;
            $alibabaChatSummary->save();

            self::info("RepairAlibabaChat:clientId:{$client_id},seller_account_id: {$seller_account_id},
            buyer_account_id: {$buyer_account_id},summary_id: {$alibabaChatSummary->summary_id} 添加成功 ");

        }

    }

    /**
     * 修复店铺同步客户的状态
     * @param $dryRun
     * @return void
     * @throws CDbException
     * @throws CException
     */
    public function actionFixStoreStatus($dryRun = 0)
    {
        $sql = 'select a.id,a.client_id,a.store_id,a.sync_customer_flag,b.setting_id,b.enable_flag from tbl_alibaba_store a
                join tbl_alibaba_customer_sync_setting b on a.client_id=b.client_id and a.store_id=b.store_id
                where a.sync_customer_flag!=b.enable_flag and a.store_id!=0 and a.id>%d order by a.id limit 200';
        $db = AlibabaStore::model()->getDbConnection();

        $lastId = 0;
        $count = 0;
        $clientIds = [];
        while (true) {
            $data = $db->createCommand(sprintf($sql, $lastId))->queryAll();
            if (!$data) {
                break;
            }

            foreach ($data as $value) {
                if(!$dryRun){
                    $db->createCommand("update tbl_alibaba_store set sync_customer_flag={$value['enable_flag']} where id={$value['id']}")->execute();
                    $clientIds[] = $value['client_id'];
                }
                self::info("client_id: {$value['client_id']}, store_id: {$value['store_id']}, id: {$value['id']}, setting_id: {$value['setting_id']}, old: {$value['sync_customer_flag']}, new: {$value['enable_flag']}");
            }

            $count += count($data);
            $lastId = end($data)['id'];
        }
        $clientIds = array_unique($clientIds);
        foreach ($clientIds as $clientId) {
            $this->resetStoreCache($clientId);
        }
        self::info("总数: {$count}");
    }
    /**
     * 更新alibaba订单同步配置-订单状态映射
     * @param int $client_id
     * @param null $lastNumber
     */
    public function actionUpdateAlibabaOrderSyncSetting($client_id = 0, $lastNumber=null)
    {
        if( strpos($client_id, ',') !== false )
        {
            $client_id = explode(',', $client_id);
        }

        $clientList = $this->getClientList($client_id,false, null,null,0,0,$lastNumber);


        //获取映射分类
        $map = \common\library\invoice\Helper::aliOrderStatusMap();

        foreach ($clientList as $client)
        {
            $db = Yii::app()->db;

            $list =  $db->createCommand('select setting_id,store_id,order_status_map,sync_order_status from tbl_alibaba_order_sync_setting  where client_id=:client_id')->queryAll(true, [':client_id'=> $client['client_id']]);

            foreach ($list as $item )
            {
                //以前没有设置过的或者已经设置新的映射关系的跳过
                if(!empty($item['order_status_map']) || empty(json_decode($item['sync_order_status'],true))){
                    continue;
                }

                $orderStatusMap = [];

                foreach ($map as $val) {
                    if (!in_array($val['category'], json_decode($item['sync_order_status'], true))) {
                        continue;
                    }
                    $orderStatusMap[] = [
                        'ali_order_status' => $val['ali_order_status'],
                        'sale_order_status' => ''
                    ];

                }
                if (empty($orderStatusMap)) {
                    continue;
                }

                $orderStatusMap = json_encode($orderStatusMap);

                try {

                    $sql = "update tbl_alibaba_order_sync_setting set order_status_map='{$orderStatusMap}' where setting_id={$item['setting_id']}";

                    $db->createCommand($sql)->execute();

                    self::info("client_id:{$client['client_id']},store_id:{$item['store_id']}"."更新成功");

                } catch (Exception $e) {
                    $endMsg = sprintf("UpdateAlibabaOrderSyncSetting: error_msg[%s]",$e->getMessage());
                    \LogUtil::info($endMsg);
                    self::info($endMsg);
                    continue;
                }

            }
        }

    }


    public function actionResyncOrderProduct($clientId, $storeId, $orderId)
    {
        $adminUserId = PrivilegeService::getInstance($clientId)->getAdminUserId();
        User::setLoginUserById($adminUserId);

        // 1. Clean old products of order
        $order = new \common\library\invoice\Order($adminUserId, $orderId);
        if ($order->isNew() || !$order->ali_order_id) {
            throw new RuntimeException('订单不存在或不是阿里订单');
        }
        $order->product_list = [];
        $order->save();

        // 2. sync products of products
        $result = \common\library\alibaba\order\AlibabaOrderService::syncSingleOrder($clientId, $adminUserId, $storeId, $order->ali_order_id, $orderId);
        var_dump($result);
    }

    /**
     * 将 关闭同步的客户 重置为 开启同步，并触发同步
     * @param $clientId
     * @param $storeId
     * @return void
     * @throws \CDbException
     * @throws \ProcessException|\CException
     */
    public function actionResyncCustomer($clientId, $storeId)
    {
        if (!$clientId || !$storeId) {
            return;
        }

        $storeIds = explode(',', $storeId);
        $db = PgActiveRecord::getDbByClientId($clientId);
        $user_id = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();
        User::setLoginUserById($user_id);
        foreach ($storeIds as $storeId) {

            $sqlTmp = "select relation_id, company_id, alibaba_company_id from tbl_alibaba_company_relation
                    where client_id={$clientId} and store_id={$storeId} and relation_id>%s and sync_switch_flag=0
                    order by relation_id limit 500";

            $updateSqlTmp = "update tbl_alibaba_company_relation set sync_switch_flag=1 where relation_id in (%s) and sync_switch_flag=0";

            $lastId = 0;
            while (true) {
                $sql = sprintf($sqlTmp, $lastId);
                $data = $db->createCommand($sql)->queryAll();
                if (!$data) {
                    break;
                }

                // 重置客户同步标志位
                $relationIds = array_column($data, 'relation_id');

                $updateSql = sprintf($updateSqlTmp, implode(',', $relationIds));
                $count = $db->createCommand($updateSql)->execute();
                self::info("set sync_switch_flag, count: {$count}");

                // 重新同步客户
                foreach ($data as $relation) {
                    \common\library\alibaba\customer\AlibabaCustomerService::syncSingleCustomer($clientId, $user_id, 1, $relation['alibaba_company_id']);
                    self::info("pushed to queue, aliCompanyId: {$relation['alibaba_company_id']}");
                }

                $lastId = end($data)['relation_id'];
                self::info("lastId: {$lastId}");
            }
        }

    }

    /**
     * 修复阿里客户联系人邮箱
     * @param int $client_id
     * @param int $store_id
     */
    public function actionUpdateAlibabaCustomerRelation($client_id ,$store_id)
    {
        if (!$client_id || !$store_id) {
            return;
        }
        $user_id = \common\library\privilege_v3\PrivilegeService::getInstance($client_id)->getAdminUserId();
        User::setLoginUserById($user_id);

        $store = new \common\library\alibaba\store\AlibabaStore($client_id, $store_id);
        $sessionKey = $store->getAccessToken();

        $db = PgActiveRecord::getDbByClientId($client_id);

        $sqlTmp = "select relation_id, alibaba_company_id from tbl_alibaba_customer_relation
                where client_id={$client_id} and store_id={$store_id} and relation_id>%s and buyer_email=''
                order by relation_id limit 500";

        $lastId = 0;
        $count = 0;
        while (true) {
            $sql = sprintf($sqlTmp, $lastId);
            $list = $db->createCommand($sql)->queryAll();

            if (!$list) {
                break;
            }

            $aliCompanyIds = array_unique(array_column($list, 'alibaba_company_id'));
            foreach ($aliCompanyIds as $aliCompanyId) {
                $alibabaCompany = new \common\library\alibaba\services\AlibabaCompany($sessionKey, $aliCompanyId);
                $data = $alibabaCompany->getInfo();

                foreach ($data['contact_open_co_list']['contact_open_co'] ?? [] as $datum) {
                    $customerInfo = CustomerSyncHelper::parserCustomerInfo($datum);
                    $alibabaCustomerId = $customerInfo['alibaba_customer_id'];
                    $alibabaCustomerEmail = $customerInfo['email'];
                    if(empty($alibabaCustomerEmail)){
                        continue;
                    }

                    $customerRelation = (new \common\library\alibaba\customer\AlibabaCustomerRelation($client_id))->loadByAlibabaCustomerId($alibabaCustomerId);
                    if ($customerRelation->isNew()) {
                        continue;
                    }

                    if ($customerRelation->buyer_email) {
                        continue;
                    }

                    $customerRelation->buyer_email = $alibabaCustomerEmail;
                    $customerRelation->save();
                    $count++;

                    self::info("update aliCompanyId: {$aliCompanyId}, alibabaCustomerId:{$alibabaCustomerId}  buyer_email:  {$alibabaCustomerEmail}");

                }
            }

            $lastId = end($list)['relation_id'];
            self::info("lastId: {$lastId}");
        }
        self::info("totalCount: {$count}");

    }

    //阿里巴巴消息表修改字段
    public function actionUpdateAlibabaMessageTable($year)
    {
        $db = \Yii::app()->alibaba_message_db;

        foreach (['01','02','03','04','06','07','08','09','10','11','12'] as $month) {

            $date = $year.$month;

            $sql = "select * from information_schema.tables where table_schema='alibaba_message' and table_name ='tbl_chat_message_{$date}';";

            if (!empty($db->createCommand($sql)->queryRow())) {
                $chatSql = "ALTER TABLE `tbl_chat_message_{$date}`
MODIFY COLUMN `message_data` varchar(2048) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '初始参数' AFTER `message_time`,
MODIFY COLUMN `handler_params` varchar(2048) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '消息传入参数' AFTER `message_data`;";

                $db->createCommand($chatSql)->execute();
            }

            $sql = "select * from information_schema.tables where table_schema='alibaba_message' and table_name ='tbl_customer_message_{$date}';";

            if (!empty($db->createCommand($sql)->queryRow())) {
                $customerSql = "ALTER TABLE `tbl_customer_message_{$date}`
MODIFY COLUMN `message_data` varchar(2048) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '初始参数' AFTER `message_time`;";

                $db->createCommand($customerSql)->execute();
            }

            $sql = "select * from information_schema.tables where table_schema='alibaba_message' and table_name ='tbl_order_notify_{$date}';";

            if (!empty($db->createCommand($sql)->queryRow())) {
                $orderSql = "ALTER TABLE `tbl_order_notify_{$date}`
MODIFY COLUMN `message_data` varchar(2048) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '初始参数' AFTER `message_time`;";

                $db->createCommand($orderSql)->execute();
            }

            $sql = "select * from information_schema.tables where table_schema='alibaba_message' and table_name ='tbl_delay_message_{$date}';";

            if (!empty($db->createCommand($sql)->queryRow())) {
                $delaySql = "ALTER TABLE `tbl_delay_message_{$date}`
MODIFY COLUMN `message_data` varchar(2048) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '延时消息内容' AFTER `seller_account_id`;";

                $db->createCommand($delaySql)->execute();
            }

        }

        $sql = "select * from information_schema.tables where table_schema='alibaba_message' and table_name ='tbl_unbind_cancel_auth_{$year}';";

        if (!empty($db->createCommand($sql)->queryRow())) {
            $unbindSql = "ALTER TABLE `tbl_unbind_cancel_auth_{$year}`
MODIFY COLUMN `message_data` varchar(2048) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '初始参数' AFTER `message_time`;";
            $db->createCommand($unbindSql)->execute();

        }


    }

    /**
     * 生成TM国际站缓存（国际站曾经有购买过小满就设置缓存）
     */
    public function actionSetClientAccountCache()
    {

        $sql = 'select distinct(seller_account_id) as seller_account_id from tbl_alibaba_account where 1=1';
        $accountList = \Yii::app()->db->createCommand($sql)->queryAll();

        $alibabaAccount = new AlibabaAccount();

        foreach ($accountList as $account) {

            try {
                $sellerAccountId = (int)$account['seller_account_id'];
                if (!$sellerAccountId) {
                    continue;
                }

                $alibabaAccount->setClientAccountCache($sellerAccountId);

                self::info("SetClientAccountCache: seller_account_id:{$sellerAccountId}");
            } catch (\Throwable $t) {
                $log = sprintf('seller_account_id=%s, %s', $sellerAccountId, $t->getMessage());
                LogUtil::error($log);
                echo $log  . "\n";
            }

        }

    }

    //清除阿里巴巴国际站缓存
    public function actionCleanAlibabaAccountCache($clientId = 0, $grey = 1, $greyNum = null)
    {
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey && !is_null($greyNum)) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        foreach ($clientIds as $clientId) {

            try {
                $userIds = array_column(UserInfo::model()->findAll('client_id='.$clientId),'user_id');
                foreach ($userIds as $userId )
                {
                    AlibabaService::getInstance($clientId, $userId)->delAlibabaAccountCache();
                    self::info("{$clientId} clean userId {$userId} cache ");
                }

                self::info("cleanAlibabaAccountCache:client_id:$clientId complete！\n");
            } catch (\Throwable $t) {
                $log = sprintf('client_id=%s, %s', $clientId, $t->getMessage());
                LogUtil::error($log);
                echo $log  . "\n";
            }

        }

    }

    //小满侧开启销售助手
    public function actionOpenAssistantFlag($clientId = 0, $grey = 1, $greyNum = null)
    {
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey && !is_null($greyNum)) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        foreach ($clientIds as $clientId) {

            try {
                $admin = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();
                User::setLoginUserById($admin);

                $sql = "select seller_account_id from tbl_alibaba_account where client_id = {$clientId}";
                $accountList = \Yii::app()->db->createCommand($sql)->queryAll();

                foreach ($accountList as $item){
                    $alibabaAccount = new \common\library\alibaba\oauth\AlibabaAccount($clientId);
                    $alibabaAccount->loadBySellAccountId($item['seller_account_id']);

                    $alibabaAccount->tmOpen(true);
                    self::info("OpenAssistant:client_id:$clientId, sellerAccountId:{$item['seller_account_id']} complete！\n");

                }

                self::info("OpenAssistant:client_id:$clientId complete！\n");
            } catch (\Throwable $t) {
                $log = sprintf('client_id=%s, %s', $clientId, $t->getMessage());
                LogUtil::error($log);
                echo $log  . "\n";
            }

        }

    }

    /**
     * 异步同步客户小记
     * @param $clientId
     * @param $storeId
     * @param $companyId
     * @param $aliCompanyId
     * @param $customerIds
     * @param $userId
     * @return void
     * @throws \ProcessException
     */
    public function actionSyncNoteByAliCompany($clientId, $storeId, $companyId, $aliCompanyId, $customerIds, $userId=0)
    {
        if (!$userId) {
            $userId = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();
        }
        User::setLoginUserById($userId);

        $customerIds = explode(',', $customerIds);
        $alibabaCustomerSyncProcessor = new AlibabaCustomerSyncProcessor($clientId, $userId, $storeId);
        $alibabaCustomerSyncProcessor->initDefaultSetting();
        $alibabaCustomerSyncProcessor->processNote($companyId, $aliCompanyId, $customerIds);
    }

    //重新同步alibaba客户信息
    public function actionSyncCustomerMessage($clientId = 0, $grey = 1, $greyNum = null)
    {
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey && !is_null($greyNum)) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }
        $db = \Yii::app()->alibaba_message_db;

        foreach ($clientIds as $clientId) {

            try {
                $pgDb = PgActiveRecord::getDbByClientId($clientId);

                $sql = "select store_id from tbl_alibaba_store where client_id ={$clientId} and enable_flag=1 and oauth_flag=1";

                $storeList = \Yii::app()->db->createCommand($sql)->queryAll();

                $storeIds = array_column($storeList,'store_id');

                foreach($storeIds as $storeId){
                    $sql = "select message_id,message_data from tbl_customer_message_202211 where client_id =:client_id and  store_id=:store_id and message_time between :b_message_time and :e_message_time and message_id>%d and type=:type limit 200";

                    $lastId = 0;
                    $count = 0;
                    while (true) {

                        $params = [
                            'store_id' => $storeId,
                            ':client_id' => $clientId,
                            ':type' => 2,
                            'b_message_time' => '2022-11-11 00:00:00',
                            'e_message_time' => '2022-11-11 11:00:00',
                        ];
                        $data = $db->createCommand(sprintf($sql, $lastId))->queryAll(true, $params);

                        if (!$data) {
                            break;
                        }
                        $customerIds = [];

                        foreach ($data as &$item) {

                            if(empty($item['message_data'])){
                                continue;
                            }

                            $message = json_decode($item['message_data'], true);

                            if(empty($message['content'])){
                                continue;
                            }

                            $content = json_decode($message['content'], true);

                            if(empty($content['customer_id'])){
                                continue;
                            }
                            $customerIds[] = $content['customer_id'];

                            $item['customer_id'] = $content['customer_id'];

                        }

                        $sql = "select alibaba_company_id from tbl_alibaba_company_relation where client_id={$clientId} and alibaba_company_id in (".implode(',', $customerIds).")";

                        $relation = $pgDb->createCommand($sql)->queryAll();

                        $aliCompanyIds = array_column($relation,'alibaba_company_id');

                        $updateIds = array_diff($customerIds, $aliCompanyIds);

                        foreach ($data as $item) {
                            if(empty($item['customer_id'])){
                                continue;
                            }
                            if(in_array($item['customer_id'], $updateIds)){

                                \common\library\alibaba\services\queue\QueueHelper::pushCustomHandle(\common\library\alibaba\services\queue\QueueHelper::GREY_CLIENT_CUSTOM_HANDLER_QUEUE_KEY, $item['message_data']);

                                LogUtil::info("[SyncCustomerMessage] client_id:{$clientId}, message_data:{$item['message_data']}");
                            }

                        }

                        $count += count($data);
                        $lastId = end($data)['message_id'];
                    }

                }


                self::info("SyncCustomerMessage:client_id:$clientId complete！\n");
            } catch (\Throwable $t) {
                $log = sprintf('SyncCustomerMessage client_id=%s, %s', $clientId, $t->getMessage());
                LogUtil::error($log);
                echo $log  . "\n";
            }

        }

    }

    public function resetAccountCache($clientId)
    {
        \common\library\alibaba\cache\AlibabaAccountCacheableRepo::instance($clientId)->refreshCache();
    }

    public function resetStoreCache($clientId)
    {
        \common\library\alibaba\cache\AlibabaStoreCacheableRepo::instance($clientId)->refreshCache();
    }

    public function resetStoreAccountCache($clientId)
    {
        \common\library\alibaba\cache\AlibabaStoreAccountCacheableRepo::instance($clientId)->refreshCache();
    }

    public function resetStoreMemberCache($clientId)
    {
        \common\library\alibaba\cache\AlibabaStoreMemberCacheableRepo::instance($clientId)->refreshCache();
    }
    //清除阿里店铺重复的数据
    public function actionClearRepeatStore($id)
    {
        $db =\Yii::app()->db;
        $sql = "select client_id, store_id from tbl_alibaba_store  where id = :id";
        $store = $db->createCommand($sql)->queryRow(true, [':id' => $id]);

        if(empty($store)){
            self::info('数据不存在');
            return false;
        }

        $sql = "select count(*) from tbl_alibaba_store  where client_id = :client_id and store_id = :store_id";
        $count = $db->createCommand($sql)->queryScalar([':client_id' => $store['client_id'] , ':store_id' => $store['store_id']]);

        if($count < 2){
            self::info('没有重复数据');
            return false;
        }

        $sql = "delete from tbl_alibaba_store where id=:id";
        $db->createCommand($sql)->execute([':id'=>$id]);
        \LogUtil::info("clearRepeatAccount client_id:{$store['client_id']} store_id:{$store['store_id']} id:{$id}");
        self::info("clearRepeatAccount client_id:{$store['client_id']} store_id:{$store['store_id']} id:{$id}");

        //清除店铺管理的缓存
        AlibabaService::getInstance($store['client_id'])->delAlibabaStoreCache();

        $this->resetStoreCache($store['client_id']);
    }


    //存量的已过期国际站店铺发送邮件通知及弹窗
    public function actionSendEmailForInvalidStore($clientId = 0, $grey = 1, $greyNum = null)
    {
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey && !is_null($greyNum)) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        foreach ($clientIds as $clientId) {

            try {
                //已过期client跳过
                if (\common\library\account\Client::getClient($clientId)->getExpiredDays() > 0) {
                    continue;
                }

                $adminInfo = PrivilegeService::getInstance($clientId)->getAdminInfo();

                if(empty($adminInfo)){
                    continue;
                }

                $storeList = new \common\library\alibaba\store\AlibabaStoreList($clientId);
                $storeList->setEnableFlag(0);
                $storeList->getFormatter()->listInfoSetting();
                $list = $storeList->find();

                foreach ($list as $item) {
                    \SystemNotifyMailUtil::sendAlibabaStoreExpiredNotify($adminInfo['email'], $item['store_name'], $item['seller_account_info']['seller_nickname']??'', $item['seller_account_info']['seller_email']??'', $item['update_time']);

                    //失效加入缓存
                    AlibabaService::getInstance($clientId)->setInvalidAlibabaStoreCache($item['store_id']);

                    self::info("SendEmailForInvalidStore:client_id:{$clientId} store_id {$item['store_id']} \n");
                    LogUtil::info("SendEmailForInvalidStore:client_id:{$clientId} store_id {$item['store_id']} \n");
                }


            } catch (\Throwable $t) {
                $log = sprintf('client_id=%s, %s', $clientId, $t->getMessage());
                LogUtil::error($log);
                echo $log  . "\n";
            }

        }

    }

    //修复访客营销配置为空数据
    public function actionRepairEmptyVisitorMarketingSetting($clientId = 0, $grey = 1, $greyNum = null)
    {
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey && !is_null($greyNum)) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }
        $visitorMarketingSetting = \common\library\alibaba\visitor_marketing\VisitorMarketingSettingHelper::getInitVisitorMarketingSetting();

        foreach ($clientIds as $clientId) {

            try {
                $alibabaStoreList = new \common\library\alibaba\store\AlibabaStoreList($clientId);
                $alibabaStoreList->setFields(['store_id','enable_flag']);
                $list = $alibabaStoreList->find();

                foreach($list as $item){
                    $alibabaStore = new \common\library\alibaba\store\AlibabaStore($clientId, $item['store_id']);
                    $visitorMarketingObj = $alibabaStore->getVisitorMarketingObj();
                    if($visitorMarketingObj->isNew())
                    {
                        continue;
                    }
                    if(!empty($visitorMarketingObj->open_period)){
                        continue;
                    }

                    $alibabaStore->setVisitorMarketingSetting($item['enable_flag'], $visitorMarketingSetting);
                    $alibabaStore->save();

                    \LogUtil::info('RepairEmptyVisitorMarketingSetting:clientId:'.$clientId.'storeId:'.$item['store_id']);
                }

            } catch (\Throwable $t) {
                $log = sprintf('client_id=%s, %s', $clientId, $t->getMessage());
                LogUtil::error($log);
                echo $log  . "\n";
            }

        }

    }
    public function actionSyncAlibabaOrder1()
    {
        $client_id = 14367;
        $store_id  = *********;
        $order_id  = 129466178501023482;
        $user_id   = 11859133;

        $client_id = 351352;
        $store_id  = *********;
        $order_id  = 211402219501022956;
        $user_id   = 249521333;

        $alibabaStore = new \common\library\alibaba\store\AlibabaStore($client_id, $store_id);
        if ($alibabaStore->enable_flag != 1 || !$alibabaStore->access_token) {
            throw new RuntimeException('店铺没有授权');
        }
        User::setLoginUserById($user_id);

        $aliOrder = new AlibabaOrder($alibabaStore->access_token, $order_id);
        $data = $aliOrder->getInfo([AlibabaOrder::DATA_SELECT_STATUS_ACTION,AlibabaOrder::DATE_SELECT_DRAFT_ROLE]);

        $processor = new \common\library\alibaba\order\AlibabaOrderSyncProcessor($client_id, $user_id, $store_id);
        $res = $processor->processWithLock($order_id, $data['value']);
        var_dump($res);
    }

    public function actionDelFundAmountNotValidCashCollection($clientId){
        $adminUserId = PrivilegeService::getInstance($clientId)->getAdminUserId();
        User::setLoginUserById($adminUserId);
        $db = PgActiveRecord::getDbByClientId($clientId);
        $orderFundRelationSql = "select order_id,cash_collection_id from tbl_alibaba_order_fund_relation where client_id={$clientId} and receive_amount=0 and pay_step ='REFUND'";
        $orderFundRelations = $db->createCommand($orderFundRelationSql)->queryAll();
        if (empty($orderFundRelations)){
            return;
        }

        foreach ($orderFundRelations as $orderFundRelation){
            $cashCollectId = $orderFundRelation['cash_collection_id'];
            $order_id = $orderFundRelation['order_id'];
            //删除es
            SearchQueueService::pushCashCollectionQueue($adminUserId, $clientId, [$cashCollectId], \Constants::CASH_COLLECTION_INDEX_TYPE_DELETE);
            //删除回款单
            $cashCollectionDeleteSql = "delete from tbl_cash_collection where client_id={$clientId} and cash_collection_id ={$cashCollectId}";
            $deleteResult = $db->createCommand($cashCollectionDeleteSql)->execute();

            $oldCashCollectionRecycleDeleteSql = "delete from tbl_recycle where client_id={$clientId} and refer_id ={$cashCollectId} and type=11";
            \ProjectActiveRecord::getDbByClientId($clientId)->createCommand($oldCashCollectionRecycleDeleteSql)->execute();

            //删除关系记录
            $deleteAlibabaOrderFundSql = "delete from tbl_alibaba_order_fund_relation where client_id={$clientId} and cash_collection_id={$cashCollectId} and order_id={$order_id}";
            $deleteResult = $db->createCommand($deleteAlibabaOrderFundSql)->execute();

            \common\library\performance_v2\record\PerformanceRecorder::cleanByRefer($clientId, \Constants::TYPE_CASH_COLLECTION, [$cashCollectId]);
            \LogUtil::info("delFundCashCollect clientId:$clientId $cashCollectId");

        }

        $order_ids = array_column($orderFundRelations,'order_id');
        $orderSql = "select refer_id,status from tbl_cash_collection_status where client_id = {$clientId} and refer_id IN (". implode(',', $order_ids) .")";
        $orderStatusInfo = $db->createCommand($orderSql)->queryAll();
        $orderStatusMap = array_column($orderStatusInfo, null, 'refer_id');

        $cashCollectionFilter = new CashCollectionFilter($clientId);
        $cashCollectionFilter->order_id = $order_ids;
        $cashCollectionFilter->enable_flag = \Constants::ENABLE_FLAG_TRUE;
        $cashCollectionFilter->collect_status = CashCollection::IS_COLLECTED;
        $cashCollectionFilter->refer_type = CashCollection::REFER_TYPE_ORDER;
        $cashCollectionFilter->select(['getDate' => function () {
            return "max(collection_date),min(collection_date)";
        }, 'order_id']);
        $cashCollectionFilter->groupBy('order_id');
        $cashCollectionList = $cashCollectionFilter->rawData();
        $cashCollectionMap = array_column($cashCollectionList, null, 'order_id');

        //删除后维护订单回款
        foreach ($orderFundRelations as $orderFundRelation){
            $order_id = $orderFundRelation['order_id'];
            if ($orderStatusMap[$order_id]['status'] ?? 0) {
                $finish_collection_date = '1970-01-01';
                $first_collection_date = $cashCollectionMap[$order_id]['min'] ?? '1970-01-01';
                $last_collection_date = $cashCollectionMap[$order_id]['max'] ?? '1970-01-01';
                if ($orderStatusMap[$order_id]['status'] == CashCollection::COLLECTION_STATUS_FINISH) {
                    $finish_collection_date = $last_collection_date;
                }
                $orderModel = \common\models\client\Order::findById($order_id);
                $orderModel->first_collection_date = $first_collection_date;
                $orderModel->last_collection_date = $last_collection_date;
                $orderModel->finish_collection_date = $finish_collection_date;
                $orderModel->update();
            }

        }
        // 回款时间维护后增加绩效埋点，重跑下
        \common\library\performance_v2\Helper::runPerformance($clientId, \Constants::TYPE_ORDER, $order_ids);
        \LogUtil::info("delFund clientId:$clientId end!");
    }

    //访客营销配置-接待时间配置
    public function actionUpdateOpenPeriodSetting($clientId = 0, $grey = 1, $greyNum = null)
    {
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey && !is_null($greyNum)) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }
        $visitorMarketingSetting = \common\library\alibaba\visitor_marketing\VisitorMarketingSettingHelper::getInitVisitorMarketingSetting();

        foreach ($clientIds as $clientId) {

            try {
                $alibabaStoreList = new \common\library\alibaba\store\AlibabaStoreList($clientId);
                $alibabaStoreList->setFields(['store_id']);
                $list = $alibabaStoreList->find();

                foreach($list as $item){
                    $alibabaStore = new \common\library\alibaba\store\AlibabaStore($clientId, $item['store_id']);
                    $visitorMarketingObj = $alibabaStore->getVisitorMarketingObj();
                    if($visitorMarketingObj->isNew())
                    {
                        continue;
                    }

                    $alibabaStore->setVisitorMarketingSetting($alibabaStore->visitor_marketing_flag, $visitorMarketingSetting);
                    $alibabaStore->save();

                    \LogUtil::info("UpdateOpenPeriodSetting clientId:{$clientId}, storeId:{$item['store_id']}, visitorMarketingSetting:".json_encode($visitorMarketingSetting)  );
                }

            } catch (\Throwable $t) {
                $log = sprintf('client_id=%s, %s', $clientId, $t->getMessage());
                LogUtil::error($log);
                echo $log  . "\n";
            }

        }

    }

    //更新访客营销时间配置
    public function actionUpdateVisitorMarketingTime($clientId = 0, $grey = 1, $greyNum = null)
    {
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey && !is_null($greyNum)) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        foreach ($clientIds as $clientId) {

            try {

                if(!PrivilegeService::getInstance($clientId)->hasFunctional([PrivilegeConstants::FUNCTIONAL_VISITOR_MARKETING])){
                    continue;
                }

                $alibabaStoreList = new \common\library\alibaba\store\AlibabaStoreList($clientId);
                $alibabaStoreList->setFields(['store_id']);
                $list = $alibabaStoreList->find();

                foreach($list as $item){
                    $setting = new \common\library\alibaba\visitor_marketing\AlibabaVisitorMarketingSetting($clientId, $item['store_id']);
                    if($setting->isNew())
                    {
                        continue;
                    }
                    //处理过的跳过
                    if($setting->first_open_time != '1970-01-01 00:00:01'){
                        continue;
                    }

                    //默认开关修改时间节点 2023-05-18 18:00:00
                    $nodeTime = '2023-05-18 18:00:00';
                    if (strtotime($setting->create_time) < strtotime($nodeTime)) {

                        if($setting->enable_flag){
                            $setting->first_open_time = $setting->create_time;
                            $setting->latest_open_time = $setting->create_time;
                        }else{
                            $setting->first_open_time = $setting->create_time;
                            $setting->latest_open_time = $setting->create_time;
                            $setting->latest_close_user = $setting->update_user;
                            $setting->latest_close_time = $setting->update_time;
                        }

                    } else {
                        if($setting->enable_flag){
                            $setting->first_open_user = $setting->update_user;
                            $setting->first_open_time = $setting->update_time;
                            $setting->latest_open_user = $setting->update_user;
                            $setting->latest_open_time = $setting->update_time;
                        }
                    }

                    $setting->save();

                    \LogUtil::info("UpdateVisitorMarketingTime clientId:{$clientId}, storeId:{$item['store_id']},first_open_user:{$setting->first_open_user}, first_open_time: {$setting->first_open_time},latest_open_user:{$setting->latest_open_user} , latest_open_time:{$setting->latest_open_time}");
                }

            } catch (\Throwable $t) {
                $log = sprintf('client_id=%s, %s', $clientId, $t->getMessage());
                LogUtil::error($log);
                echo $log  . "\n";
            }

        }

    }
    public function actionFixCashCollectionCreateType($clientId){
        $adminUserId = PrivilegeService::getInstance($clientId)->getAdminUserId();
        User::setLoginUserById($adminUserId);
        $db = PgActiveRecord::getDbByClientId($clientId);
        $orderFundRelationSql = "select order_id,cash_collection_id from tbl_alibaba_order_fund_relation where client_id={$clientId} and pay_step ='REFUND'";
        $orderFundRelations = $db->createCommand($orderFundRelationSql)->queryAll();
        if (empty($orderFundRelations)){
            return;
        }
        $cashCollectionIdList = array_column($orderFundRelations,'cash_collection_id');
        $cashCollectionIds = array_chunk($cashCollectionIdList, 200);
        foreach ($cashCollectionIds as $chunk) {
            $sql = "update tbl_cash_collection set create_type = 3 where cash_collection_id in (" . implode(',',$chunk) . ") and client_id = $clientId";
            $result = $db->createCommand($sql)->execute();
        }

    }

    public function actionFixOrderLinkStatus($clientId)
    {
        $db = PgActiveRecord::getDbByClientId($clientId);
        $orderFundRelationSql = "select order_id,cash_collection_id from tbl_alibaba_order_fund_relation where client_id={$clientId}";
        $orderFundRelations = $db->createCommand($orderFundRelationSql)->queryAll();
        if (empty($orderFundRelations)) {
            return;
        }
        $orderIdList = array_column($orderFundRelations, 'order_id');
        $orderIdList = array_unique($orderIdList);
        $orderIds = array_chunk($orderIdList, 50);
        foreach ($orderIds as $chunk) {
            $linkTrigger = CashCollectionTrigger::make($clientId, $chunk);
            $linkTrigger->addOrderIds($chunk);
            $linkTrigger->trigger();
        }
    }

    public function actionEchoAllDemotionDurationInfo($clientId=0)
    {
        $limiterHandler = (new \common\library\alibaba\services\queue\DemotionLimiterHandler(true));
        $limiterHandler->setIsGrey(\Yii::app()->params['env'] == 'grey');
        if ($clientId) {
            echo "detail: " . json_encode($limiterHandler->getDurationInfo($clientId)) . "\n";
            echo "matched clientIds:" . implode(',', $limiterHandler->getMatchedClientIds()) . "\n";
        } else {
            $limiterHandler->echoAllDurationInfo();
        }
    }


    public function actionEchoDemotionMatchedList()
    {
        $limiterHandler = (new \common\library\alibaba\services\queue\DemotionLimiterHandler(true));
        $limiterHandler->setIsGrey(\Yii::app()->params['env'] == 'grey');
        echo "matched clientIds:" . implode(',', $limiterHandler->getMatchedClientIds()) . "\n";
    }

    public function actionQueueStats()
    {
        $stats = QueueHelper::getQueueStat();
        var_dump($stats);
    }
    /***
     * @param $clientId
     * @param $storeId
     * @param $aliOrderId
     * @param $syncConfig
     * @param $isWriteBack
     * @return void
     * $syncConfig 信保订单同步配置细化 指定同步信保订单产品 客户通 订单状态 订单基础信息 回款单等 未指定就全部同步
     * $isWriteBack 存在依赖关系字段 是否回写 执行后置操作 例如 同步信保订单产品后是否同步订单表ProductList 同步关系 客户通同步会写company表等
     * 订单状态同步需要回写
     */

    // ./yiic-test alibaba SyncAlibabaOrder --clientId=14119 --storeId=********* --aliOrderId=201355895001022011
    public function actionSyncAlibabaOrder($clientId = 14367, $storeId = *********, $aliOrderId = 175908502501028893, $syncConfig = [], $isWriteBack = 1)
    {
        $clientObject = \common\library\account\Client::getClient($clientId);
        $user = $clientObject->getMasterUser();
        \User::setLoginUser($user);
        $clientId = User::getLoginUser()->getClientId();
        $userId = User::getLoginUser()->getUserId();

        $store = \AlibabaStore::model()->find('store_id=' . $storeId . ' and enable_flag=1');

        $sessionKey = $store->access_token;
        if (empty($sessionKey)) {
            echo "授权失败";
            return;
        }
        $aliOrder = new AlibabaOrder($sessionKey, $aliOrderId);
        $data = $aliOrder->getInfo([AlibabaOrder::DATA_SELECT_STATUS_ACTION, \common\library\alibaba\services\AlibabaOrder::DATE_SELECT_DRAFT_ROLE]);
        if (empty($data['value'])) {
            $error = $aliOrder->getLastError();
            \LogUtil::error("[client_id {$clientId}] ali_order_id: {$aliOrderId} get info error: " . json_encode($error));
            echo "请求请求体为空";
            return;
        }
        $data = $data['value'];
        $alibabaOrderId = $data['trade_id'];
        $data['executeSyncAliOrderConfig'] = is_array($syncConfig ?? []) ? $syncConfig ?? [] : json_decode($syncConfig);
        $data['isWriteBack'] = $isWriteBack;
        $processor = new AlibabaOrderSyncProcessor($clientId, $userId, $storeId);
        $processor->setToken($sessionKey);

        $processor->processWithLock($alibabaOrderId, $data);
    }

    /***
     * @param $clientId
     * @param $order_id
     * @param $opUserId
     * @return void
     * @throws ProcessException
     * 异步下载图片
     */
    public function actionDownloadOrderProductImg($clientId = 1, $order_id = 3115324411, $opUserId = 0)
    {
        AlibabaOrderLogUtil::info("[下载图片 $clientId $order_id $opUserId]");
        $adminUserId = PrivilegeService::getInstance($clientId)->getAdminUserId();
        User::setLoginUserById($adminUserId);
        if (!$opUserId) {
            $opUserId = $adminUserId;
        }

        $orderModel = \common\models\client\Order::findById($order_id);
        if (!$orderModel) {
            throw new \RuntimeException('订单不存在');
        }
        $ali_store_id = $orderModel->ali_store_id;
        $ali_order_id = $orderModel->ali_order_id;
        AlibabaOrderLogUtil::setPrefix("AlibabaOrder$ali_order_id");
        $alibabaStore = new common\library\alibaba\store\AlibabaStore($clientId, $orderModel->ali_store_id);
        //异步下载图片下 无视开关
//        if($alibabaStore->isNew() || !$alibabaStore->sync_order_flag)
//        {
//            throw new \RuntimeException('开关关闭');
//        }
        $sessionKey = $alibabaStore->getAccessToken();

        //授权失效也不处理同步了
        if (empty($sessionKey)) {
            AlibabaOrderLogUtil::info("[授权失败 图片无法同步]:");
        }

        try {
            $aliOrder = new AlibabaOrder($sessionKey, $orderModel->ali_order_id);
            $data = $aliOrder->getInfo([AlibabaOrder::DATA_SELECT_STATUS_ACTION, AlibabaOrder::DATE_SELECT_DRAFT_ROLE]);
            if (empty($data['value'])) {
                AlibabaOrderLogUtil::info("[阿里订单消息体为空]:");
            }
            $orderProductList = json_decode($orderModel->product_list, true);
            $aliOrderProductList = $data['value']['order_products']['trade_ecology_order_product'];
            $aliOrderImgMap = [];
            foreach ($aliOrderProductList as $aliOrder) {
                $aliOrderImgMap[$aliOrder['product_id']] = $aliOrder['product_image'];
            }
            if (empty($aliOrderImgMap) || empty($orderProductList)) {
                throw new \RuntimeException('该订单没有需要下载的图片');
            }
            //协程串行上传 图片 上传图片有判重
            $concurrentNum = 20;
            $taskPool = new IOTaskPool($concurrentNum, $orderModel->ali_order_id);
            $taskPool->setAsync(false);     // 可自由切换串行或并发下载
            foreach ($aliOrderImgMap as $uniqueId => $imageUrl) {
                $task = new CustomizeTask(function () use ($clientId, $imageUrl, $ali_store_id) {
                    $upload = new AlibabaUpload($clientId, $ali_store_id);
                    $obj = $upload->upload($imageUrl);
                    if (!$obj) {
                        return false;
                    }
                    return $obj->getFileUrl();
                });
                $task->setTaskId($uniqueId);
                $taskPool->addTask($task);
            }
            $taskPool->batchRun();
            foreach ($taskPool->getTasks() as $task) {
                $uniqueId = $task->getTaskId();
                $aliOrderImgMap[$uniqueId] = $task->getResult();
            }
            $syncSetting = new AlibabaProductSyncSetting($clientId, $orderModel->ali_store_id);
            if ($syncSetting->isNew()) {
                $syncSetting->save();
            }
            $store = $syncSetting->getStore();
            $syncExecutor = new AlibabaProductSyncExecutor($clientId, $store);
            $syncExecutor->setOpUser($opUserId);
            $syncExecutor->setCheckLastSyncTime(false);
            $products = $syncExecutor->runByOrderProduct($aliOrderProductList);
            $productImgMap = [];
            foreach ($products as $key => $value) {
                if (!isset($aliOrderImgMap[$key])) {
                    continue;
                }
                $productImgMap[$value['third_product_id']] = $aliOrderImgMap[$key];
            }
            foreach ($orderProductList as &$orderProduct) {
                if ($orderProduct['product_image'] || !isset($orderProduct['ali_product_id'])) {
                    continue;
                }
                $orderProduct['product_image'] = $productImgMap[$orderProduct['ali_product_id']];
            }
            $orderModel->product_list = json_encode($orderProductList);
            $orderModel->update();
        } catch (Exception $e) {
            LogUtil::error("update order img error :{$order_id} error: " . $e->getMessage());
        }

    }

    //推送队列消费
    public function actionTryConsumerMsg()
    {
        $msg =
            [
                'content' => [
                    "trade_id"=> '175933979001028893',
                    "status" => "unpay"
                ],
                'id' => '46450153968460675',
                'pub_app_key' => '12497914',
                'pub_time' => "2023-05-26 16:53:47",
                'topic' => 'icbu_trade_OrderNotify',
                'user_nick' => 'cn1524857901snge',
                'executeSyncAliOrderConfig' => [1, 2, 3, 4],
                '__process_until' => time() + 1,
                'handler' => 'OrderNotifyHandler',
                'max_retry_count' => 1,   //表示失败要重试的次数
                'sync_type' => Constant::SYNC_TYPE_ORDER,
                'event' => Constant::OKKI_SYNC_ORDER,
                'isDelayMsg' => true,
                'client_id' => 14367,
                'user_id' => **********,
                'store_id' => *********,
                'params' => json_encode([
                    'client_id' => 14367,
                    'user_id' => ********,
                    'store_id' => *********,
                    'buyer_account_id' => ***********,
                    'seller_account_id' => *********
                ])
            ];
        $delayMessageService = new DelayMessageService(14367, ********, *********);
        $delayMessageService->pushQueue(Constant::DELAY_MESSAGE_TYPE_ORDER_RETRY, $msg, 10);
        echo "推送成功";
//        $data = $msg;
//        $params = empty($data['params']) ? [] : (is_array($data['params']) ? $data['params'] : json_decode($data['params'], true));
//        $userId = empty($params['user_id']) ? $params['op_user_id'] : $params['user_id'];
//        $delayMessageService = new DelayMessageService($params['client_id'], $userId, $params['store_id'] ?? 0);
//        $messageId = $msg['message_id'] ?? 0;
//        $suffix = $msg['suffix'] ?? date('Ym');
//        $delayMessageService->getDelayMessage()->setSuffix($suffix);
//        $handler = QueueHelper::createHandler($msg['handler']);
//        $handler->initData($msg);
//        $limiter = new \common\library\alibaba\services\queue\ClientLimiter(true);
//        $limiter->setExcludeGrey(true);
//        $handler->setLimiter($limiter);
//        $handler->run();
    }

    //同步任务

    /**
     * @throws ProcessException
     */
    public function actionSyncOrderTask()
    {
        $store_id = 251272336;
        $task_id = 3267438020;
        $client_id = 1;
        $user_id = 765;
        $refresh_all_flag = 1;
        $retry = 0;
        ini_set("memory_limit", "1024M");
        \LogUtil::info("begin SyncOrder : $store_id,$task_id,$client_id, $user_id");

        User::setLoginUserById($user_id, $client_id);

        $task = new \common\library\alibaba\order\AlibabaOrderSyncTask($client_id, $task_id);
        if ($task->isNew()) {
            throw new ProcessException('阿里订单同步任务不存在:' . $task_id);
        }

        if ($retry) {
            $task->status = 1;
            $task->save();
        }

        $executor = new \common\library\alibaba\order\AlibabaOrderSyncTaskExecutor($task);
        $executor->setRefreshAllFlag($refresh_all_flag);
        $executor->run();
    }

    public function actionDelOrder()
    {
        $clientId = 14367;
        $db = \PgActiveRecord::getDbByClientId($clientId);
        $sql = "select a.order_id from tbl_alibaba_order_relation as a
left join tbl_order as b
on a.order_id=b.order_id
where b.order_id is null and a.client_id = $clientId;";
        $res = $db->createCommand($sql)->queryAll();
        $order_ids = array_column($res, 'order_id');
        $order_array = array_filter($order_ids, function ($value) {
            return $value !== 0;
        });
        $orderIdChunk = array_chunk($order_array, 200);

        foreach ($orderIdChunk as $chunk) {
            $sql = "delete from  tbl_alibaba_order_relation  where order_id in (" . implode(',', $chunk) . ") and client_id = $clientId";
            $result = $db->createCommand($sql)->execute();
            var_dump($sql);
        }
    }

    //更新线索和客户潜客运营来源标识
    public function actionUpdateVisitorMarketingSource($clientId = 0, $grey = 1, $greyNum = null)
    {
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey && !is_null($greyNum)) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        foreach ($clientIds as $clientId) {

            try {

                $adminUserId = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();
                if(!$adminUserId){
                    return false;
                }
                \User::setLoginUserById($adminUserId);

                if(!PrivilegeService::getInstance($clientId)->hasFunctional([PrivilegeConstants::FUNCTIONAL_VISITOR_MARKETING])){
                    continue;
                }

                $db = \ProjectActiveRecord::getDbByClientId($clientId);

                $alibabaStoreList = new \common\library\alibaba\store\AlibabaStoreList($clientId);
                $alibabaStoreList->setFields(['store_id']);
                $storeList = $alibabaStoreList->find();

                $count = 0;
                foreach($storeList as $store){
                    $sql = "select relation_id,buyer_account_id,lead_id,company_id from tbl_alibaba_trade_relation where client_id =:client_id and store_id=:store_id and visitor_scene IN('visiter_markting_shopDetail','visiter_markting_productDetail','visiter_marktig_live') and relation_id>%d order by relation_id limit 500";

                    $lastId = 0;
                    while (true) {
                        $data = $db->createCommand(sprintf($sql, $lastId))->queryAll(true, [':client_id' => $clientId, ':store_id' => $store['store_id']]);

                        if (empty($data)) {
                            break;
                        }

                        foreach ($data as $item) {

                            //线索
                            if(!empty($item['lead_id'])){
                                $lead = new \common\library\lead\Lead($clientId, $item['lead_id']);
                                if(!$lead->isNew()){
                                    $originList = $lead->origin_list;
                                    array_push($originList, \common\library\setting\library\origin\Origin::SYS_ORIGIN_SYSTEM_MARKETING);
                                    $lead->origin_list = array_unique($originList);
                                    $lead->save();
                                    self::info("[UpdateVisitorMarketingSource] client_id: {$clientId}, store_id: {$store['store_id']}, buyer_account_id: {$item['buyer_account_id']}, lead_id: {$item['lead_id']}, origin_list: ".json_encode($lead->origin_list));
                                    \LogUtil::info("[UpdateVisitorMarketingSource] client_id: {$clientId}, store_id: {$store['store_id']}, buyer_account_id: {$item['buyer_account_id']}, lead_id: {$item['lead_id']}, origin_list: ".json_encode($lead->origin_list));
                                }
                            }

                            //客户
                            if(!empty($item['company_id'])){
                                $company = new \common\library\customer_v3\company\orm\Company($clientId, $item['company_id']);

                                if(!$company->isNew()){
                                    $originList = $company->origin_list;
                                    array_push($originList, \common\library\setting\library\origin\Origin::SYS_ORIGIN_SYSTEM_MARKETING);
                                    $company->origin_list = array_unique($originList);
                                    $company->save();
                                    self::info("[UpdateVisitorMarketingSource] client_id: {$clientId}, store_id: {$store['store_id']}, buyer_account_id: {$item['buyer_account_id']}, company_id: {$item['company_id']}, origin_list: ".json_encode($company->origin_list));
                                    \LogUtil::info("[UpdateVisitorMarketingSource] client_id: {$clientId}, store_id: {$store['store_id']}, buyer_account_id: {$item['buyer_account_id']}, company_id: {$item['company_id']}, origin_list: ".json_encode($company->origin_list));
                                }
                            }
                        }

                        $count += count($data);
                        $lastId = end($data)['relation_id'];
                    }

                }
                self::info("[UpdateVisitorMarketingSource] clientId:{$clientId} 总数: {$count}");
                \LogUtil::info("[UpdateVisitorMarketingSource] clientId:{$clientId} 总数: {$count}");

            } catch (\Throwable $t) {
                $log = sprintf('[UpdateVisitorMarketingSource] client_id=%s, %s', $clientId, $t->getMessage());
                \LogUtil::error($log);

            }

        }

    }


    //导出没有绑定授权正常的国际站店铺的客户Client列表
    public function actionDownloadUnbindClient($clientId = 0, $grey = 1, $greyNum = null)
    {
        set_time_limit(0);
        ini_set('memory_limit', '2048M');

        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey && !is_null($greyNum)) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        $fileName = '国际站数据_' . time() . '.csv';
        $path = '/tmp/' . $fileName;
        $fp = fopen($path, 'w');
        fputcsv($fp, ['ClientID', '小满里公司名称', '产品版本', 'CGS在约情况']);

        $db = \Yii::app()->db;
        foreach ($clientIds as $clientId) {

            try {
                echo 'clientId: '.$clientId." begin \n";
                $adminUserId = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();
                if(!$adminUserId){
                    continue;
                }

                $client = \common\library\account\Client::getClient($clientId);

                if($client->getExpiredDays() > 0){
                    continue;
                }

                $mainSystemId = PrivilegeService::getInstance($clientId)->getMainSystemId();

                if(!in_array($mainSystemId, [\common\library\privilege_v3\PrivilegeConstants::CRM_SMART_SYSTEM_ID, \common\library\privilege_v3\PrivilegeConstants::CRM_PRO_SYSTEM_ID, \common\library\privilege_v3\PrivilegeConstants::OKKI_PRO_SYSTEM_ID])){
                    continue;
                }

                $sql = "select client_id from tbl_alibaba_store where client_id = :client_id and enable_flag = :enable_flag limit 1";

                $row = $db->createCommand($sql)->queryRow(true, [
                    ':client_id' => $clientId,
                    ':enable_flag' => 1,
                ]);

                if($row){
                    continue;
                }

                $sql = "select client_id from tbl_client_contract_relation where client_id=:client_id limit 1";

                $cgs = \Yii::app()->account_base_read_db->createCommand($sql)->queryScalar([':client_id' => $clientId]);

                $cgsStatus = $cgs ? 'CGS在约' : '未知';

                fputcsv($fp, [
                    $clientId,
                    $client->full_name,
                    $mainSystemId,
                    $cgsStatus
                ]);
                echo 'clientId: '.$clientId." end \n";

            } catch (\Throwable $t) {
                var_dump($t);die;
                $log = sprintf('[DownloadUnbindClient] client_id=%s, %s', $clientId, $t->getMessage());
                \LogUtil::error($log);
                continue;
            }

        }

        fclose($fp);
        echo  $path."\n";
    }

    //小满Client断约, 关闭潜客运营功能(处理历史数据)
    public function actionExpireClientCloseVisitorMarketing()
    {
        $today = date('Y-m-d 00:00:00');
        $begin = date('Y-m-d 00:00:00', strtotime('-300 days'));    // 潜客运营上线时间
        $sql   = "select client_id, valid_to from tbl_client where valid_to > '$begin' and valid_to < '$today'";
        $rows  = Yii::app()->account_base_db->createCommand($sql)->queryAll();
        foreach ($rows as $row) {
            $client = Client::getClient($row['client_id']);
            $daysExpired = $client->getExpiredDays();

            if($daysExpired < 0){
                continue;
            }
            \common\library\alibaba\store\AlibabaStoreService::closeVisitorMarketing($row['client_id']);

            var_dump($row['client_id']);
            \LogUtil::info("[ExpireClientCloseVisitorMarketing] clientId:.{$row['client_id']}");

        }
    }


    //刷新阿里店铺账号token、过期时间
    public function actionRefreshStoreToken($clientId = 0, $grey = 1, $greyNum = null)
    {
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey && !is_null($greyNum)) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        foreach ($clientIds as $clientId) {

            try {
                $admin = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();

                if(!$admin){
                    continue;
                }

                User::setLoginUserById($admin);

                $storeList = new \common\library\alibaba\store\AlibabaStoreList($clientId);
                $storeList->setEnableFlag(1);
                $storeList = $storeList->find();

                $storeIds = array_column($storeList, 'store_id');

                $alibabaAccountList = new \common\library\alibaba\oauth\AlibabaAccountList($clientId);
                $alibabaAccountList->setStoreId($storeIds);
                $alibabaAccountList->setFields(['id']);
                $refreshTokenAccountIds = $alibabaAccountList->find();
                $refreshTokenAccountIds = array_column($refreshTokenAccountIds , 'id');

                $alibabaAccountService = new \common\library\alibaba\oauth\AlibabaAccountService($clientId, $admin);
                try {
                    foreach ($refreshTokenAccountIds as $id) {
                        $alibabaAccountService->refreshToken($id);
                        self::info("RefreshStoreToken, client_id {$clientId} refreshToken id: {$id}");
                    }
                } catch (\Exception $exception) {
                    self::info($exception->getMessage());
                }

                self::info("RefreshStoreToken:client_id:$clientId complete！\n");

            } catch (\Throwable $t) {
                $log = sprintf('client_id=%s, %s', $clientId, $t->getMessage());
                LogUtil::error($log);
                echo $log  . "\n";
                continue;
            } finally {
                // 释放内存
                User::cleanUserMap();
                \common\library\account\Client::cleanCacheMap($clientId);
                ProjectActiveRecord::releaseDbByClientId($clientId);

            }

        }

    }


    /***
     * 转信保订单异步逻辑
     * 1. 校验
     * 2. 转信保订单
     * 3. 保存失败原因
     * @param $client_id
     * @param $task_id
     * @param $user_id
     * @return void
     * @throws ProcessException
     * @throws \xiaoman\orm\exception\OrmException
     */
    public function actionTransferAliOrder($client_id, $task_id, $user_id)
    {

        User::setLoginUserById($user_id);

        $task = new AsyncTask($client_id, $task_id);
        if ($task->isNew()) {
            return;
        }
        $task->getOperator()->running();
        $aliOrder = $task->ext_info['ali_order_params'];
        $aliOrderProduct = $task->ext_info['ali_order_product_params'];
        $order_id = $task->refer_id;

        $order = new Order($client_id, $order_id);

        $aliRes = AlibabaOrderSyncHelper::createTransferAliOrder($order_id, $aliOrder, $aliOrderProduct);

        $task->getOperator()->success(['task_result' => json_encode($aliRes)]);

        if ($order->isNew()) {
            return;
        }

        if (empty($ali_store_id)){
            return ;
        }

        //任务结束 尝试同步订单信息
        $list = new AlibabaOrderRelationList($client_id);
        $list->setAlibabaMasterOrderId($order_id);
        $list->setOrderId(0);
        $list->setStoreId($order->ali_store_id);
        $list->setCreateType(OmsConstant::ALIBABA_CREATE_TYPE_TRANSFER_SUB_ALI_ORDER);
        $relationList = $list->find();

        foreach ($relationList as $relation) {
            AlibabaOrderService::syncSingleOrder($client_id, $user_id, $order->ali_store_id, $relation['alibaba_trade_id']);
        }
    }


    /***
     * 异步下载国际站订单产品图片，回写成file_id
     * @param $client_id
     * @param $order_id
     * @return void
     * @throws ProcessException
     */
    public function actionSyncAliProductImg($client_id, $order_id)
    {
        $adminUserId = \common\library\privilege_v3\PrivilegeService::getInstance($client_id)->getAdminUserId();

        if (!$adminUserId) {
            return;
        }
        User::setLoginUserById($adminUserId);

        AlibabaOrderSyncHelper::syncAliProductImg($order_id);
    }


    /***
     * 同步国际站订单信息
     * @param $client_id
     * @param $store_id
     * @param $order_id
     * @return void
     * @throws ProcessException
     */
    public function actionSyncAliOrderRelationInfo($client_id, $order_id)
    {
        $adminUserId = \common\library\privilege_v3\PrivilegeService::getInstance($client_id)->getAdminUserId();

        if (!$adminUserId) {
            return;
        }
        User::setLoginUserById($adminUserId);

        $order = new Order($client_id,$order_id);

        if ($order->isNew()) {
            return;
        }

        $store_id = $order->ali_store_id;

        $list = new AlibabaOrderRelationList($client_id);
        $list->setAlibabaMasterOrderId($order_id);
        $list->setOrderId(0);
        $list->setStoreId($store_id);
        $list->setCreateType(OmsConstant::ALIBABA_CREATE_TYPE_TRANSFER_SUB_ALI_ORDER);
        $relationList = $list->find();

        foreach ($relationList as $relation) {
            if (empty($relation['alibaba_trade_id'])){
                continue;
            }
            AlibabaOrderService::syncSingleOrder($client_id, $adminUserId, $store_id, $relation['alibaba_trade_id']);
        }

    }


    // 删除并发产生的脏数据 （临时用）
    public function actionClearDirtyData($client_id, $store_id, $user_id)
    {

        User::setLoginUserById($user_id);

        $db =\Yii::app()->db;

        $sql = "delete from tbl_alibaba_store where client_id = :client_id and store_id = :store_id";
        $db->createCommand($sql)->execute([':client_id'=>$client_id, ':store_id'=>$store_id]);


        $sql = "delete from tbl_alibaba_account where client_id = :client_id and store_id = :store_id";
        $db->createCommand($sql)->execute([':client_id'=>$client_id, ':store_id'=>$store_id]);

        \LogUtil::info("clearRepeatAccount client_id:{$client_id} store_id:{$store_id} ");
        self::info("clearRepeatAccount client_id:{$client_id} store_id:{$store_id} ");

        //清除店铺管理的缓存
        AlibabaService::getInstance($client_id, $user_id)->delAlibabaStoreCache();
        AlibabaService::getInstance($client_id, $user_id)->delAlibabaAccountCache();

        $this->resetStoreCache($client_id);
    }



   //php yiic-test Alibaba FixInvalidToken --client_id=0 --step=other --start=0 --end=10
    public function actionFixInvalidToken($client_id, $step, $start, $end)
    {
        if(!in_array($step, ['expire_client', 'recent_no_login_client','other'])) {
            throw new \InvalidArgumentException("Invalid step: $step");
        }
        echo "Start-FixInvalidToken-Params client_id:{$client_id} step:{$step} start:{$start} end:{$end}\n";
        $alibabaAccount = new AlibabaAccount();
        if($client_id) {
            $accounts = $alibabaAccount->getModelClass()::model()->findAll("client_id = :clientId and enable_flag = 1 and oauth_flag = 3",
            [':clientId' => $client_id]);
        } else {
            $condition = "enable_flag = 1 and oauth_flag = 3 and id > $start and id <= $end";
            echo "Start-Condition: $condition\n";
            $accounts = $alibabaAccount->getModelClass()::model()->findAll($condition);
            $num = count($accounts);
            echo "Total accounts found: $num\n";
        }
        foreach ($accounts as $account) {
            echo "Start-FixInvalidToken id:{$account->id} client_id:{$account->client_id} user_id:{$account->user_id} store_id:{$account->store_id} seller_account_id:{$account->seller_account_id}  oauth_flag:{$account->oauth_flag}  token:{$account->access_token}\n";
            if ($step == 'expire_client' && $this->isExpire($account) ) {
                $this->deal($account);
            } else if($step == 'recent_no_login_client'  && $this->noRecentLogin($account)) {
                $this->deal($account);
            } else if($step == 'other') {
                $this->deal($account);
            }
        }
    }


    private function isExpire($account) {
        if(\common\library\privilege_v3\PrivilegeService::getInstance($account->client_id)->isExpire()) {
            return true;
        } else {
            echo "isNotExpire id:{$account->id} client_id:{$account->client_id}\n";
            return false;
        }
    }
    private function noRecentLogin($account) {
        $userId = $account->user_id;
        if(empty($userId)) {
            echo "noRecentLogin id:{$account->id} user_id {$userId} is empty\n";
            return false;
        }
        $user = \User::getUserObject($userId);
        $tableName = \LoginEvent::model()->tableName();
        $sql = "SELECT * FROM `{$tableName}` WHERE `account` = :account ORDER BY `login_time` DESC LIMIT 1,1";
        $params[':account'] = $user->getEmail();
        $lastLoginEvent = \LoginEvent::model()->dbConnection->createCommand($sql)->queryAll(true, $params);
        if(isset($lastLoginEvent[0]['login_time'])
            && strtotime($lastLoginEvent[0]['login_time']) < strtotime('-1 months')){
            return true;
        }
        echo "isRecentLogin id:{$account->id} user_id:{$userId} lastLoginTime:{$lastLoginEvent[0]['login_time']}\n";
        return false;
    }


    private function deal($account) {
        sleep(1);
        if ($this->validAccessToken($account)) {
            echo "Skip-Unbind id:{$account->id} client_id:{$account->client_id} user_id:{$account->user_id} store_id:{$account->store_id} seller_account_id:{$account->seller_account_id} is_admin:{$account->is_admin} oauth_flag:{$account->oauth_flag} user_id:{$account->user_id} token:{$account->access_token}\n";
            return;
        }
        echo "Start-Unbind id:{$account->id} client_id:{$account->client_id} user_id:{$account->user_id} store_id:{$account->store_id} seller_account_id:{$account->seller_account_id} is_admin:{$account->is_admin} oauth_flag:{$account->oauth_flag} user_id:{$account->user_id} token:{$account->access_token}\n";
        User::setLoginUserById($account->user_id);
        AlibabaAccountService::accessTokenInvalid($account->access_token);
        echo "End-Unbind id:{$account->id} client_id:{$account->client_id} user_id:{$account->user_id} store_id:{$account->store_id} seller_account_id:{$account->seller_account_id} is_admin:{$account->is_admin} oauth_flag:{$account->oauth_flag} user_id:{$account->user_id} token:{$account->access_token}\n";
    }


    private function validAccessToken($account): bool
    {
        if (empty($account->access_token)) {
            return false;
        }
        $top = AlibabaTopClient::getInstance();
        $rsp = $top->getCustomer($account->access_token, "1");
        $data = AlibabaTopClient::object2array($rsp);
        echo "validAccessToken client_id:{$account->client_id} user_id:{$account->user_id} store_id:{$account->store_id} seller_account_id:{$account->seller_account_id} oauth_flag:{$account->oauth_flag} token:{$account->access_token}, rsp:".json_encode($data)."\n";
        if(($data['code'] ?? '')=== 'IllegalAccessToken'){
            return false;
        }
        return true;
    }
}
