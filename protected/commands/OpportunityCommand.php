<?php

use common\library\custom_field\CustomFieldService;
use common\library\opportunity\OpportunityBatchOperator;
use common\library\opportunity\OpportunityFormatter;
use common\library\opportunity\sales_flow\OpportunitySalesFlowService;
use common\library\privilege_v3\PrivilegeConstants;
use common\library\privilege_v3\PrivilegeService;
use common\library\setting\library\flow\FlowApi;
use common\library\setting\library\link\Link;
use common\library\setting\library\link\LinkApi;
use common\library\setting\library\stage\StageApi;
use common\library\util\SqlUtil;

/**
 * Created by PhpStorm.
 * User: troy.li
 * Date: 2018-09-04
 * Time: 11:33 AM
 */

class OpportunityCommand extends CrontabCommand
{

    /**
     * 导入商机脚本
     * @param $user_id
     * @param $task_id
     * @return void
     * @throws ProcessException
     */
    public function actionImport($user_id, $task_id){
        User::setLoginUserById($user_id);
        $import = new \common\library\opportunity\import\CsvImport($task_id, $user_id);
        $import->import();
    }

    /**
     * 修复没有主销售流程问题，商机销售阶段为0的问题
     * @param $clientId
     * @param int $grey
     * @param int $expFlag
     * @param int $startId
     * @throws CDbException
     * @throws ProcessException
     */
    public function actionFixOpportunityStage($clientId, $grey = 1,$expFlag = 0,$startId = 0){

        if ($clientId) {
            $clientIds = [$clientId];
        } elseif ($grey) {
            $clientIds = \common\library\GreyEnvHelper::getGreyClientIds();
        } elseif($expFlag){
            $clientIds= \common\library\account\Client::getExpClientIds();
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        foreach ($clientIds as $clientId) {

            if($startId && $clientId < $startId){
                continue;
            }

            //判断有没有设置主销售流程
//            $opportunitySalesFlow = new \common\library\opportunity\sales_flow\OpportunitySalesFlowList($clientId);
//            $opportunitySalesFlow->setFields(['flow_id']);
//            $opportunitySalesFlow->setMainFlag(\common\library\opportunity\sales_flow\OpportunitySalesFlow::MAIN_FLOW_FLAG);
//            $mainSaleFlow = $opportunitySalesFlow->findOne();

            $api = new FlowApi($clientId);
            $mainSaleFlow = $api->getMainFlow();

            $db = PgActiveRecord::getDbByClientId($clientId);
            $mainSaleFlowId = 0;
            //没有主销售流程，把第一个流程默认设置为主销售流程
            if(!$mainSaleFlow){
                $api = new FlowApi($clientId);
                $api->getParams()->setWithDisabled();
                $saleFlow = $api->firstOne();
//                $opportunitySalesFlow = new \common\library\opportunity\sales_flow\OpportunitySalesFlowList($clientId);
//                $opportunitySalesFlow->setFields(['flow_id']);
//                $saleFlow = $opportunitySalesFlow->findOne();
                if(!$saleFlow){
                    continue;
                }
                $mainSaleFlowId = $saleFlow['flow_id'];

                $afforRow = $api->updateMainFlag($mainSaleFlowId) ? 1 : 0;

//                $sql = "UPDATE tbl_opportunity_sales_flow SET main_flag = 1 , disable_flag = 0 WHERE client_id = {$clientId} AND flow_id = {$mainSaleFlowId}";
//                $afforRow = $db->createCommand($sql)->execute();
                $msg = "FixMainSaleFLow:client_id:$clientId main_sale_flow:$mainSaleFlowId res:$afforRow\r\n";
                echo $msg."\n";
                LogUtil::info($msg);
            }else{
                $mainSaleFlowId = $mainSaleFlow['flow_id'];
            }
            //没有设置商机，跳过
            if(!$mainSaleFlowId){
                continue;
            }
            //判断是否有stage = 0 的商机
            $sql = "SELECT opportunity_id,flow_id,create_user FROM tbl_opportunity WHERE client_id = {$clientId} AND stage = 0";
            $list = $db->createCommand($sql)->queryAll();

            //修复商机销售阶段为空
            if($list){

                //主销售流程的第一个阶段
//                $stage =new \common\library\opportunity\stage\OpportunityStageList($clientId,1);
//                $stage->setFlowId($mainSaleFlowId);
//                $firstStage = $stage->findOne();

                $stageApi = new StageApi($clientId);
                $stageApi->setFlowId($mainSaleFlowId);
                $firstStage = $stageApi->firstOne();

                foreach ($list as $item){
                    if(!$item['opportunity_id']){
                        continue;
                    }
                    $flowId = $mainSaleFlowId; //主销售流程
                    $stageId = $firstStage['stage_id'];//主销售流程第一个销售阶段

                    if($item['flow_id'] != $mainSaleFlowId){
                        //判断对应销售流程的第一个id
//                        $stageData =new \common\library\opportunity\stage\OpportunityStageList($clientId,1);
//                        $stageData->setFlowId($item['flow_id']);
//                        $toStageData = $stageData->findOne();
                        $stageApi = new StageApi($clientId);
                        $stageApi->setFlowId($item['flow_id']);
                        $toStageData = $stageApi->firstOne();
                        if($toStageData){
                            $flowId = $item['flow_id'];
                            $stageId= $toStageData['stage_id'];
                        }
                    }

                    $sql = "UPDATE tbl_opportunity SET flow_id = {$flowId}, stage = {$stageId}  WHERE client_id = {$clientId} AND opportunity_id = {$item['opportunity_id']}";
                    $afforRow = $db->createCommand($sql)->execute();

                    //插入统计
                    \common\library\opportunity\statistics\OpportunityStageStatisticsService::saveStatistics(
                        $clientId,
                        $item['create_user'],
                        $item['opportunity_id'],
                        $stageId,
                        0,
                        $flowId
                    );

                    $msg = "FixStageId:client_id:$clientId opportunity_id:{$item['opportunity_id']} flow_id:{$flowId} stage_id:{$stageId} res:{$afforRow}\r\n";
                    echo $msg."\n";
                    LogUtil::info($msg);
                }
            }
        }

    }

    
    public function actionRemoveSerialIdPrefix()
    {
        $clients = $this->getClientList();
        foreach ($clients as $client) {
            $clientId = $client->client_id;
            $db = PgActiveRecord::getDbByClientId($clientId);
            $sql = "select opportunity_id, serial_id from tbl_opportunity";
            $res = $db->createCommand($sql)->queryAll();
            $batch = [];
            foreach ($res as $opportunity) {
                $newSerialId = OpportunityFormatter::trimSerialId($opportunity['serial_id']);
                if ($newSerialId != $opportunity['serial_id']) {
                    $updateSql = "update tbl_opportunity set serial_id={$newSerialId} where opportunity_id={$opportunity['opportunity_id']}";
                    $batch[] = $updateSql;
                }
            }
            if ($count = count($batch)) {
                $batchSql = implode(';', $batch);
                $db->createCommand($batchSql)->execute();
                echo "clientId: {$clientId} 更新 {$count} \n";
            }
        }

    }

    public function actionExport()
    {
        $clients = $this->getClientList();
        echo "client_id,公司名称,CRM超级管理员账号,创建成功商机数", PHP_EOL;

        foreach ($clients as $client) {
            $clientId = $client->client_id;
            $db = PgActiveRecord::getDbByClientId($clientId);
            $sql = "SELECT count(1) as total FROM tbl_opportunity WHERE client_id=:client_id AND enable_flag=:enable_flag";
            $result = $db->createCommand($sql)->queryRow(true, [':client_id' => $clientId, ':enable_flag' => 1]);

            try {
                $admin = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();
                $admin = UserAccount::findByUserId($admin);
                $email = $admin['account'];
            } catch (Exception $e) {
                $email = $client->email;
            }

            if ($result['total'] > 0) {
                echo "{$clientId},{$client->full_name},{$email},{$result['total']}", PHP_EOL;
            }
        }
    }

    public function actionFixOpportunityUserFeed()
    {
        $clients = $this->getClientList();
        $trailTypes = [
            \common\library\trail\TrailConstants::TYPE_OPPORTUNITY_ADD,
            \common\library\trail\TrailConstants::TYPE_OPPORTUNITY_DEL,
            \common\library\trail\TrailConstants::TYPE_OPPORTUNITY_STAGE,
            \common\library\trail\TrailConstants::TYPE_OPPORTUNITY_TRAIL_STOP
        ];

        foreach ($clients as $client) {
            $clientId = $client->client_id;
            $pgsqlDb = PgActiveRecord::getDbByClientId($clientId);
            $sql = "SELECT a.*, b.data FROM tbl_dynamic_trail as a LEFT JOIN tbl_dynamic_trail_data as b ON a.trail_id=b.trail_id "
                 . "WHERE a.client_id=:client_id AND a.opportunity_id > 0 AND a.enable_flag=:enable_flag "
                 . "AND a.type IN (". implode(',', $trailTypes) .")";
            $params = [
                ':client_id' => $clientId,
                ':enable_flag' => 1
            ];
            $result = $pgsqlDb->createCommand($sql)->queryAll(true, $params);
            $mysqlDb = ProjectActiveRecord::getDbByClientId($clientId);

            if (count($result) > 0) {
                $sql = "UPDATE tbl_user_feed SET delete_flag=:delete_flag WHERE client_id=:client_id "
                     . "AND node_type IN (". implode(',', $trailTypes) .") AND refer_id=0 AND delete_flag=:e_delete_flag";
                $params = [
                    ':delete_flag' => 1,
                    ':client_id' => $clientId,
                    ':e_delete_flag' => 0
                ];
                $total = $mysqlDb->createCommand($sql)->execute($params);
                echo "DELETE client_id={$clientId}, Total={$total}", PHP_EOL;
            }

            foreach ($result as $item) {
                $sql = "SELECT feed_id FROM tbl_user_feed WHERE client_id=:client_id AND user_id=:user_id "
                     . "AND node_type=:node_type AND refer_id=:refer_id AND create_time=:create_time";
                $params = [
                    ':client_id' => $item['client_id'],
                    ':user_id' => $item['create_user'],
                    ':node_type' => $item['type'],
                    ':refer_id' => $item['opportunity_id'],
                    ':create_time' => $item['create_time']
                ];
                $feed = $mysqlDb->createCommand($sql)->queryScalar($params);
                if (intval($feed) === 0) {
                    User::setLoginUserById($item['create_user']);
                    $feed = new \Feed();
                    $feed->setClientId($item['client_id']);
                    $feed->setUserId($item['create_user']);
                    $feed->setCreateUser($item['create_user']);
                    $feed->setNodeType($item['type']);
                    $feed->setReferId($item['opportunity_id']);
                    $feed->setCreateTime($item['create_time']);

                    if (empty($item['data'])) {
                        $feed->setData([]);
                    } else {
                        $feed->setData(json_decode($item['data'], true));
                    }

                    $feed->save();

                    echo "INSERT client_id={$item['client_id']}, user_id={$item['create_user']}, type={$item['type']}, refer_id={$item['opportunity_id']}", PHP_EOL;
                } else {
                    $message = 'no data';
                    if (!empty($item['data'])) {
                        User::setLoginUserById($item['create_user']);
                        $data = UserFeedData::model()->find(['feed_id' => $feed]);
                        if (empty($data)) {
                            $data = new UserFeedData();
                            $data->feed_id = $feed;
                            $data->data = json_decode($item['data'], true);
                            $data->client_id = $item['client_id'];
                            $data->user_id = $item['create_user'];
                            $ret = $data->save();

                            $message = 'update data';
                        } else {
                            $message = 'exist data';
                        }
                    }

                    echo "EXIST {$message} client_id={$item['client_id']}, user_id={$item['create_user']}, type={$item['type']}, refer_id={$item['opportunity_id']}", PHP_EOL;
                }
            }
        }
    }


    public function actionUpdateAutoSettings()
    {
        /**
         * @var  CDbConnection $db
         */
        $db = Yii::app()->account_base_db;
        $privilege = \common\library\privilege_v3\PrivilegeConstants::FUNCTIONAL_AI_CLASSIFY_OPPORTUNITY;
        //没索引, 但可以接受, 数据量不多
        $clientIds = $db->createCommand("select client_id from tbl_client_privilege where privilege='{$privilege}' and enable_flag=1")->queryColumn();
        LogUtil::info("具有{$privilege}功能的企业共有" . count($clientIds) . ' date:' . date('Y-m-d H:i:s'));
        foreach ( $clientIds as $clientId )
        {

            $adminUserId = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();
            if( !$adminUserId )
            {
                LogUtil::info('未发现adminUser, continue clientId:' . $clientId);
                continue;
            }

            $db = \PgActiveRecord::getDbByClientId($clientId);

            if (empty($db)) {
                LogUtil::info('未发现db, continue clientId:' . $clientId);
                continue;
            }

            try {
                User::setLoginUserById($adminUserId);
                $settings = new \common\library\ai\classify\setting\ApplySettings($clientId);
                $oldAmount = $settings->auto_amount;
                $oldCurrency = $settings->auto_currency;

                $settings->updateAutoAmount();

                LogUtil::info("auto update clientId:{$clientId} amount : {$oldAmount} => {$settings->auto_amount}  currency : {$oldCurrency} => {$settings->currency}");
            } catch (\InnerApiException $e) {

            }
        }

        LogUtil::info("执行完成 date:" . date('Y-m-d H:i:s'));


    }


    public function actionEmailApply($client_id, $email_id, $user_id = 0, $queue = false)
    {
        $client_id = trim($client_id);
        $email_id = trim($email_id);
        $user_id = trim($user_id);
        $queue = trim($queue);

        $builder = new \common\library\ai\classify\opportunity\builder\EmailBuilder($client_id, $email_id, $user_id);
        //邮箱未收件完成的, 无需处理
        $email = \UserMail::findById($email_id);
        if( !$email['fully_received_flag'] )
        {
            //todo 重新入队等待
            if( $queue )
            {

            }

            self::info("邮件未收件完毕, 不执行. client: $client_id, email_id:$email_id, user_id:{$user_id} queue:$queue ");
            return false;
        }

        $builder->build();

        self::info("任务结束执行: client: $client_id, email_id:$email_id, user_id:{$user_id} queue:$queue end:" . date('Y-m-d H:i:s'));

    }

    public function actionCompanyApply($client_id, $company_id, $user_id)
    {
        $client_id = trim($client_id);
        $company_id = trim($company_id);
        $user_id = trim($user_id);

        self::info("任务开始执行任务开始执行: client: $client_id, company_id:$company_id, user_id:{$user_id} begin:" . date('Y-m-d H:i:s'));

        User::setLoginUserById($user_id);
        $company = new \common\library\customer_v3\company\orm\Company($client_id, $company_id);
        //执行任务的时候, 该客户被删除了, 就跳过
        if( !$company->isExist() )
        {
            self::info("任务异常: 改用户已经被删除  client: $client_id, company_id:$company_id ,user_id:{$user_id} ");
            return false;
        }


        $builder = new \common\library\ai\classify\opportunity\builder\CompanyBuilder($client_id, $company_id, $user_id);
        $builder->build();

        self::info("任务开始执行任务结束执行: client: $client_id, company_id:$company_id, user_id:{$user_id} end:" . date('Y-m-d H:i:s'));
    }

    public function actionSimulationClient()
    {
        $clientId = 1;
        $emailId = 30000023;//<EMAIL>

        $where = "client_id={$clientId} and enable_flag=1";
        $where .= " and user_mail_id={$emailId}";
        $list = UserMail::model()->findAll($where);

        foreach ($list as $email) {
            $this->actionSimulationEmail($email->user_mail_id);
        }

    }


    public function actionSimulationEmail($emailId)
    {
        $email = new \common\library\email\Email($emailId);
        if (!$email->user_id) {
            self::info("邮箱: {$email->email_address} 已被解绑不执行");
            return;
        }

        if (!$email->fully_received_flag) {
            self::info("邮箱: {$email->email_address} 未收完邮件 , 跳过");
            return;
        }

        $userId = $email->user_id;

        User::setLoginUserById($userId);
        $user = User::getLoginUser();
        $clientId = $user->getClientId();
        $userId = $user->getUserId();

        $listObj = new \common\library\mail\MailList($clientId, $userId);
        $listObj->setUserMailId($emailId);

        //过滤已删除,草稿,隐藏草稿,java, 永久删除
        $listObj->setNotInFolderIds([
            \Mail::FOLDER_DRAFT_ID,
            \Mail::FOLDER_TRASH_ID,
            \Mail::FOLDER_DELETE_ID,
            \Mail::FOLDER_IMPORT_ID,
            \Mail::FOLDER_HIDDEN_DRAFT_ID,
        ]);
        $listObj->setFields('client_id,user_id,mail_id, user_mail_id');
        $listObj->setOrderBy('receive_time');
        $listObj->setOrder('asc');
        $list = $listObj->find();
        $count = count($list);
        self::info("client_id: {$clientId} email: {$email->email_address} count:{$count}");

        $classify = new \common\library\ai\classify\mail\Classify($clientId, $userId);
        $opportunityApply = new \common\library\ai\classify\opportunity\simulation\OpportunityApply($clientId, $userId);

        foreach ($list as $i => $item) {
//            if( $i <2000)
//                continue;
            self::info("begin mail_id:{$item['mail_id']}     {$i}/{$count}");
            $time1 = microtime(true);
            $applyRule = $classify->process($item['user_mail_id'], $item['mail_id']);
            self::info('callback:' . json_encode($applyRule));
            $opportunityApply->apply($classify, $applyRule);
            $time2 = microtime(true);
            self::info("end mail_id:{$item['mail_id']} time:" . (round(($time2 - $time1) / 1000, 3)) . 's');

        }

    }

    public function actionUpdateOpportunitySwitch($clientId = 0, $last = null)
    {

        $clientList = $this->getClientList($clientId, false, null, null, 0, 0, $last);

        foreach ($clientList as $item) {

            $sql = 'select count(1) from tbl_opportunity where client_id=' . $item['client_id'] . ' and enable_flag=1';
            $db = PgActiveRecord::getDbByClientId($item['client_id']);
            $count = $db->createCommand($sql)->queryScalar();

            $client = new \common\library\account\Client($item['client_id']);
            if (!$client->hasPrivilege(\common\library\privilege_v3\PrivilegeConstants::FUNCTIONAL_OPPORTUNITY)) {
                self::info(" client: {$item['client_id']}  无 crm.functional.opportunity continue  ");
                continue;
            }


            if (1 || $count) {
                self::info(" client: {$item['client_id']}  count : {$count} opportunity_switch on  ");
                $client->setExtentAttributes([\common\library\account\Client::EXTERNAL_KEY_OPPORTUNITY_SWITCH => 1]);
            } else {
                self::info(" client: {$item['client_id']}  count : {$count} opportunity_switch off  ");
                $client->setExtentAttributes([\common\library\account\Client::EXTERNAL_KEY_OPPORTUNITY_SWITCH => 0]);
            }

            $client->saveExtentAttributes();

        }
    }


    public function actionUpdateListField($clientId = 0, $last = null)
    {
        $clientList = self::getClientList($clientId, false, null, null, 0, 0,
            $last);

        /**
         * @var CDbConnection $db
         */
        $db = Yii::app()->db;
        foreach ($clientList as $item) {
            self::info('exec client_id:' . $item['client_id']);
            $key = \common\library\account\external\UserInfoExternal::EXTERNAL_KEY_OPPORTUNITY_LIST_SORT;
            $sql = "select * from tbl_user_info_external where client_id={$item['client_id']} and `key`='{$key}' ";
            $list = $db->createCommand($sql)->queryAll();

            $hasFuncational = \common\library\privilege_v3\PrivilegeService::getInstance($item['client_id'])
                ->hasFunctional([\common\library\privilege_v3\PrivilegeConstants::FUNCTIONAL_AI_CLASSIFY_OPPORTUNITY]);

            $cache = false;
            foreach ($list as $elem) {
                $value = json_decode($elem['value']);

                if ((!$hasFuncational || in_array('stage_stay_time', $value)) && in_array('create_type', $value)) {
                    continue;
                }

                if (!in_array('create_type', $value) && $hasFuncational) {
                    $value[] = 'create_type';
                }

                if (!in_array('stage_stay_time', $value)) {
                    $value[] = 'stage_stay_time';
                }


                $value = json_encode($value);
                $upSql = "update tbl_user_info_external set `value`='{$value}' where user_id={$elem['user_id']} and `key`='{$key}' ";
                $db->createCommand($upSql)->execute();

                $cache = true;
                self::info('update user: ' . $elem['user_id'] . ' value' . $value);
            }

            if (1 || $cache) {
                \common\library\CommandRunner::run(
                    'cache',
                    'cleanUserInfo',
                    [
                        'clientId' => $item['client_id'],
                    ],
                    '/dev/null',
                    0
                );
            }
        }
    }


    public function actionAddFunctional($client_id)
    {
        $clientIdIds = [$client_id];
//        $clientIdIds = [15087,13902,14375,14619,14014,14062,13761,14492,14450,14041,13660,3980,14056];
//        $clientIdIds = [11740,13647,14455,14438];
        foreach ($clientIdIds as $clientId) {
            if (!\common\library\privilege_v3\PrivilegeService::getInstance($clientId)->hasFunctional([\common\library\privilege_v3\PrivilegeConstants::FUNCTIONAL_AI])) {
                continue;
            }

            $admin = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();
            User::setLoginUserById($admin);

            \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getClientPrivilege()->assignFunctional(\common\library\privilege_v3\PrivilegeConstants::FUNCTIONAL_AI_CLASSIFY_CUSTOMER);
            $setting = new \common\library\ai\classify\setting\ApplySettings($clientId);
            $setting->mail_enable_apply = 1;
            $setting->customer_enable_create = 1;
            \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getClientPrivilege()->assignFunctional(\common\library\privilege_v3\PrivilegeConstants::FUNCTIONAL_AI_CLASSIFY_OPPORTUNITY);
            $setting->opportunity_enable_create = 1;
            $setting->opportunity_enable_update = 1;

//            var_dump($setting->getAttributes());die;
            $setting->save();
        }
    }

    public function actionFixSetting($client_id)
    {
        $clientList = $this->getClientList($client_id, false, null, null, 0, 0);
//        $clientIdIds = [15087,13902,14375,14619,14014,14062,13761,14492,14450,14041,13660,3980,14056];
//        $clientIdIds = [11740,13647,14455,14438];
        foreach ($clientList as $client) {
            $clientId = $client['client_id'];
            $admin = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();
            if (empty($admin))
                continue;

            User::setLoginUserById($admin);

            $setting = new \common\library\ai\classify\setting\ApplySettings($clientId);
            if ($setting->isNew()) {
                self::info('new setting continue ' . $clientId);
                continue;
            }

            $setting->initStage();
            var_dump($setting->getAttributes());
            $setting->save();
        }
    }


    public function actionOpportunityList()
    {


        $clientIdIds = [15087, 13902, 14375, 14619, 14014, 14062, 13761, 14492, 14450, 14041, 13660, 3980, 14056];
//        $clientIdIds = [11740,13647,14455,14438];
        foreach ($clientIdIds as $clientId) {

            $sql = 'select count(1) as count, client_id,create_type  from tbl_opportunity where client_id=' . $clientId . ' and enable_flag=1  group by client_id, create_type';
            $db = PgActiveRecord::getDbByClientId($clientId);
            $list = $db->createCommand($sql)->queryAll();

            foreach ($list as $item) {
                if ($item['create_type'] != 2)
                    continue;

                echo "{$item['client_id']} {$item['count']} \n";
            }
        }
    }


    public function actionClientApplyProcess($clientId)
    {
        ini_set("display_errors", "On");
        ini_set("memory_limit", "15000M");
        \common\library\ai\classify\Dispatcher::clientScriptProcess($clientId);
    }

    public function actionFixAutoAmount()
    {
        $clientIdIds = [15087, 13902, 14375, 14619, 14014, 14062, 13761, 14492, 14450, 14041, 13660, 3980, 14056];
//        $clientIdIds =['14375'];
        foreach ($clientIdIds as $clientId) {
            $admin = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();
            if (empty($admin))
                continue;

            User::setLoginUserById($admin);
            $setting = new \common\library\ai\classify\setting\ApplySettings($clientId);
            $db = ProjectActiveRecord::getDbByClientId($clientId);
            $defaultAmount = $setting->auto_amount;

            $list = $db->createCommand("select * from tbl_ai_mail_classify where client_id={$clientId} and mail_tag_id=15 and file_extract_call=1")->queryAll();

            foreach ($list as $item) {
                $opportunityData = $item['opportunity_data'] ? json_decode(snappy_uncompress($item['opportunity_data']), true) : '';
                if (empty($opportunityData))
                    continue;

                $updateOpportunityIds = [];
                foreach ($opportunityData as $result) {
                    $updateOpportunityIds = array_merge(array_column($result['update_opportunity'], 'opportunity_id'), $updateOpportunityIds);
                }

                $updateOpportunityIds = array_unique($updateOpportunityIds);

                if (empty($updateOpportunityIds))
                    continue;

                $fileIds = $db->createCommand('select file_id from  tbl_mail_attach  where mail_id=' . $item['mail_id'])->queryColumn();
                if (empty($fileIds))
                    continue;

                $userId = \common\library\email\Email::getEmailObject($item['user_mail_id'])->user_id;
                User::setLoginUserById($userId);
                $api = new \common\library\api\InnerApi('file_classify');
                $api->setTimeout(5);


                $params = [
                    'mail_id' => $item['mail_id'], 'file_id' => implode(',', $fileIds),
                    'client_id' => $clientId, 'user_id' => $userId
                ];
                try {
                    $data = $api->call('extract', $params);
                } catch (\Exception $e) {
                    self::info($e->getMessage() . ' params:' . json_encode($params));
                    die;
                }

                $log = \AiMailClassify::model()->find('client_id=:client_id and user_id=:user_id and user_mail_id=:user_mail_id and mail_id=:mail_id',
                    [':client_id' => $clientId, ':user_id' => $userId, ':user_mail_id' => $item['user_mail_id'], ':mail_id' => $item['mail_id']]);

                $invoiceExtraData = $data['data'];
                $log->file_extract_call = 1;
                $log->file_extract_data = snappy_compress(json_encode($invoiceExtraData));


                $result = array_filter($invoiceExtraData['structInfo'] ?? [], function ($elem) {
                    return !empty($elem['data']);
                });

                $result = array_values($result);
                $invoiceInfo = $result[0]['data'] ?? [];
                $piNumber = $invoiceInfo['piNumber'] ?? '';
                $amount = $invoiceInfo['total'] ?? 0;
                $currency = $invoiceInfo['currencyType'] ?? '';
                $currency = strtoupper($currency);

                self::info(json_encode(compact('invoiceInfo', 'piNumber', 'amount', 'currency')));

                $mail = new \common\library\mail\Mail($item['mail_id'], $userId);

                foreach ($updateOpportunityIds as $id) {
                    try {
                        $opportunity = new \common\library\opportunity\Opportunity($clientId, $id);
                        if ($opportunity->isNew()) {
                            self::info('商机id 已被删除' . $id);
                            continue;
                        }
                    } catch (Exception $e) {
                        self::info('商机id 已被删除' . $id);
                        continue;
                    }


                    // 商机更新时间 和 动态更新时间 应该取邮件时间
                    $opportunity->setEditInfo(\common\library\ai\classify\ai_field_data\OpportunityAIFieldData::FIELD_EDIT_TYPE_BY_AI, $mail->receive_time);
                    $canEditFields = $opportunity->getCanAIEditField();

                    //不开启自动化更新就不处理
                    if (!$opportunity->extendAttributes()->auto_update)
                        continue;

                    $opportunity->setUserId($userId);

                    //目前ai可编辑字段仅处理 amount
                    if ($amount && $amount != $defaultAmount && in_array('amount', $canEditFields) && $opportunity->amount != $amount) {
                        $opportunity->amount = $amount;
                        $opportunity->currency = $currency;

                        self::info('更新金额' . $amount . 'currency: ' . $currency . ' id:' . $id);
                    }

                    if ($piNumber && !$opportunity->extendAttributes()->pi_number) {
                        $opportunity->extendAttributes()->pi_number = $piNumber;
                    }

                    $opportunity->save();

                }

                $log->save();

            }
        }
    }

    /** 更新商机销售阶段对应的销售流程
     * @param $clientId
     * @param int $grey
     * @param int $expFlag
     * @param int $startId
     * @throws ProcessException
     */
    public function actionRefreshOpportunityStage($clientId, $grey = 1,$expFlag = 0,$startId = 0)
    {

        if ($clientId) {
            $clientIds = [$clientId];
        } elseif ($grey) {
            $clientIds = \common\library\GreyEnvHelper::getGreyClientIds();
        } elseif($expFlag){
            $clientIds= \common\library\account\Client::getExpClientIds();
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        foreach ($clientIds as $clientId) {


            if($startId && $clientId < $startId){
                continue;
            }
            $systemId = "";
            $systemIds = array_column(PrivilegeClientSystem::getSystems($clientId), 'system_id');

            //获取对应的版本配置的权限 crm pro > crm plus > crm_with_dx_bundle > crm > crm smart > crm lite > crm lite_2021 > dx
            if(in_array(\common\library\privilege_v3\PrivilegeConstants::CRM_PRO_SYSTEM_ID,$systemIds)) {
                $systemId = \common\library\privilege_v3\PrivilegeConstants::CRM_PRO_SYSTEM_ID;
            } else if(in_array(\common\library\privilege_v3\PrivilegeConstants::CRM_PLUS_SYSTEM_ID,$systemIds)) {
                $systemId = \common\library\privilege_v3\PrivilegeConstants::CRM_PLUS_SYSTEM_ID;
            } else if(in_array(\common\library\privilege_v3\PrivilegeConstants::CRM_WITH_DX_SYSTEM_ID,$systemIds)) {
                $systemId = \common\library\privilege_v3\PrivilegeConstants::CRM_WITH_DX_SYSTEM_ID;
            } else if (in_array(\common\library\privilege_v3\PrivilegeConstants::CRM_SYSTEM_ID, $systemIds)) {
                $systemId = \common\library\privilege_v3\PrivilegeConstants::CRM_SYSTEM_ID;
            } else if (in_array(\common\library\privilege_v3\PrivilegeConstants::OKKI_PRO_SYSTEM_ID, $systemIds)) {
                $systemId = \common\library\privilege_v3\PrivilegeConstants::OKKI_PRO_SYSTEM_ID;
            } else if (in_array(\common\library\privilege_v3\PrivilegeConstants::CRM_SMART_SYSTEM_ID, $systemIds)) {
                $systemId = \common\library\privilege_v3\PrivilegeConstants::CRM_SMART_SYSTEM_ID;
            } else if (in_array(\common\library\privilege_v3\PrivilegeConstants::CRM_LITE_SYSTEM_ID, $systemIds)) {
                $systemId = \common\library\privilege_v3\PrivilegeConstants::CRM_LITE_SYSTEM_ID;
            } else if (in_array(\common\library\privilege_v3\PrivilegeConstants::CRM_LITE_2021_ID, $systemIds)) {
                $systemId = \common\library\privilege_v3\PrivilegeConstants::CRM_LITE_2021_ID;
            } else if (in_array(\common\library\privilege_v3\PrivilegeConstants::CRM_LITE_2023_ID, $systemIds)) {
                $systemId = \common\library\privilege_v3\PrivilegeConstants::CRM_LITE_2023_ID;
            } else if (in_array(\common\library\privilege_v3\PrivilegeConstants::DX_SYSTEM_ID, $systemIds)) {
                $systemId = \common\library\privilege_v3\PrivilegeConstants::DX_SYSTEM_ID;
            } else {
                $msg = sprintf("refreshOpportunityStageEmpty:client_id[%s] system_id[%s]",
                    $clientId, $systemId);
                \LogUtil::info($msg);
                continue;
            }

            $adminUserId = PrivilegeService::getInstance($clientId)->getAdminUserId();
            if(!$adminUserId){
                $msg = "refreshOpportunityStage:clientId:[$clientId] no system user!\r\n";
                \LogUtil::info($msg);
                echo $msg;
                continue;
            }
            \User::setLoginUserById($adminUserId);
            //初始化商机的销售阶段
            $opportunityStageService = new \common\library\opportunity\stage\OpportunityStageService($clientId);
            $opportunityStageService->systemInitOpportunitySalesFlowAndStage($systemId);
        }

    }

    public function actionCleanAIApply($client_id, $company_id = 0)
    {

        ini_set("display_errors", "On");
        ini_set("memory_limit", "15000M");
        $db = \PgActiveRecord::getDbByClientId($client_id);
        $clientDb = ProjectActiveRecord::getDbByClientId($client_id);

        $where = " client_id={$client_id} ";
        if ($company_id)
            $where .= ' and company_id=' . $company_id;

        $total = $db->createCommand("select count(1) as count  from tbl_ai_opportunity_apply_record  where $where")->queryScalar();
        //自动化创建的商机
        $aiOpIds = $db->createCommand("select opportunity_id from tbl_opportunity  where client_id={$client_id} and create_type=2")->queryColumn();
        echo 'client_id ' . $client_id . 'company_id ' . $company_id . 'total: ' . $total . 'create count' . count($aiOpIds) . "\n";
        $i = 0;
        do {
            $sql = "select * from tbl_ai_opportunity_apply_record  where $where limit 2000";

            $list = $db->createCommand($sql)->queryAll();
            if (empty($list)) {
                self::info(" client_id : $client_id 没有可执行记录");
                break;
            }

            $deleteOpIds = [];
            $mailIds = [];
            $companyIds = [];

            foreach ($list as $item) {
                ++$i;

                if ($i % 1000 == 0) {
                    echo "client_id:{$client_id}   processing $i / $total  \n";
                }

                $mailIds[] = $item['mail_id'];
                $companyIds[] = $item['company_id'];
                $createOpIds = \common\library\util\PgsqlUtil::trimArray($item['create_opportunity']);
                $updateOpIds = \common\library\util\PgsqlUtil::trimArray($item['update_opportunity']);
                $tempIds = array_values(array_unique(array_merge($createOpIds, $updateOpIds)));
                $deleteOpIds = array_merge($deleteOpIds, $tempIds);

                LogUtil::info('record:' . json_encode($item));
            }


            if (!empty($deleteOpIds)) {
                $deleteOpIds = array_values(array_unique($deleteOpIds));
                $deleteOpIds = array_intersect($deleteOpIds, $aiOpIds);
            }

            $mailIds = array_values(array_unique($mailIds));
            $mailIdStr = implode(',', $mailIds);

            $companyIds = array_values(array_unique($companyIds));
            $companyIdStr = implode(',', $companyIds);

            $result = [
                'tbl_dynamic_trail.mail' => 0,
                'tbl_dynamic_trail.opportunity' => 0,
                'tbl_dynamic_trail_data.opportunity' => 0,
                'tbl_opportunity_external' => 0,
                'tbl_opportunity_stage_statistics' => 0,
                'tbl_opportunity_history' => 0,
                'tbl_opportunity' => 0,
                'tbl_second_privilege_role_user' => 0,
                'tbl_ai_opportunity_apply_record' => 0,
            ];

            // 清理客户相关-动态
            $ret = $db->createCommand("delete from tbl_dynamic_trail where client_id={$client_id} and  company_id in ({$companyIdStr}) and refer_id in ({$mailIdStr}) and (type=201 or type=202)")->execute();
            $result['tbl_dynamic_trail.mail'] += $ret;

            // 清理商机相关
            if (!empty($deleteOpIds)) {

                $opportunityIdStr = implode(',', $deleteOpIds);
                // 清理商机动态
                $trailIds = $db->createCommand("select  trail_id from tbl_dynamic_trail where client_id={$client_id} and  opportunity_id in ($opportunityIdStr)")->queryColumn();
                if (!empty($trailIds)) {
                    $ret = $db->createCommand("delete from tbl_dynamic_trail where client_id={$client_id} and  opportunity_id in ($opportunityIdStr)")->execute();
                    $result['tbl_dynamic_trail.opportunity'] += $ret;
                    $ret = $db->createCommand("delete from tbl_dynamic_trail_data where client_id={$client_id} and  trail_id in (" . implode(',', $trailIds) . ")")->execute();
                    $result['tbl_dynamic_trail_data.opportunity'] += $ret;
                }

                // 商机资料
                $ret = $db->createCommand("delete from tbl_opportunity_external where client_id={$client_id} and opportunity_id in ($opportunityIdStr)")->execute();
                $result['tbl_opportunity_external'] += $ret;
                // 商机统计
                $ret = $db->createCommand("delete from tbl_opportunity_stage_statistics where client_id={$client_id} and opportunity_id in ({$opportunityIdStr})")->execute();
                $result['tbl_opportunity_stage_statistics'] += $ret;
                // 商机历史
                $ret = $db->createCommand("delete from tbl_opportunity_history where client_id={$client_id} and  opportunity_id in($opportunityIdStr)")->execute();
                $result['tbl_opportunity_history'] += $ret;
                // 商机
                $ret = $db->createCommand("delete from tbl_opportunity where client_id={$client_id} and  opportunity_id in($opportunityIdStr)")->execute();
                $result['tbl_opportunity'] += $ret;
                // 权限
                $module = \common\library\privilege_v3\second_privilege\SecondPrivilegePermission::MODULE_OPPORTUNITY;
                $ret = $clientDb->createCommand("delete from tbl_second_privilege_role_user where client_id={$client_id} and  refer_id in($opportunityIdStr) and module_id='{$module}'")->execute();
                $result['tbl_second_privilege_role_user'] += $ret;
            }

            //删除记录流水
            $ret = $db->createCommand("delete from tbl_ai_opportunity_apply_record where client_id={$client_id} and  mail_id in ({$mailIdStr}) and company_id in ({$companyIdStr})")->execute();
            $result['tbl_ai_opportunity_apply_record'] += $ret;

            self::info(json_encode($result));

        } while (!empty($list));

    }

    /**
     * 版本拆分
     * 对CRM、PLUS-1711版本client跑脚本，去掉系统默认的其他销售阶段，保留的销售阶段默认配置与pro相同
    CRM、PLUS-1711版本client中，若没有使用商机模块，将商机改为和SMART版本一致；
    若已使用商机模块，保留现有商机能力
    同时符合以下条件的client的商机特性降级为与SMART版本一致
    ①2019年4月1日前激活的client
    ②商机创建数为0
     */
    public function actionVersionControl(){
        $sql = "select client_id from tbl_client WHERE version IN (1711,2) and (activated_time < '2019-04-01 00:00:01' or isnull(activated_time))";
        $clientIdList = Yii::app()->account_base_db->createCommand($sql)->queryColumn();

        $stageType = \common\library\setting\library\stage\Stage::STAGE_ON_GOING_STATUS;
        $removeNames = "'开发','洽谈','询盘','样品'";

        foreach ($clientIdList as $clientId) {
            try {
                $pgDb = PgActiveRecord::getDbByClientId($clientId);

                if (!$pgDb){
                    self::info("fail!: [$clientId] db null continue");
                    continue;
                }

                $sql = "select count(1) from tbl_opportunity where client_id = $clientId";
                $count = $pgDb->createCommand($sql)->queryScalar();
                if ($count > 0) {
                    continue;
                }

                //去掉系统默认的其他销售阶段
//            $sql = "select count(1) from tbl_opportunity_stage WHERE client_id=$clientId and type=$stageType and name IN ($removeNames)";
//            $result = $pgDb->createCommand($sql)->queryScalar();
                $sql = "update tbl_opportunity_stage set enable_flag=0 WHERE client_id=$clientId and type=$stageType and name IN ($removeNames)";
                $result = $pgDb->createCommand($sql)->execute();
                self::info("clientId:$clientId,count:$result");
            } catch (Exception $e){
                self::info("fail!: [$clientId] {$e->getMessage()}");
            }
        }
    }

    /**
     * bill 长期需要使用到的商机数据统计脚本
     * @throws CDbException
     * @throws ProcessException
     */
    public function actionBillOpportunity()
    {
        $clients = Yii::app()->account_base_db->createCommand("SELECT `client_id`,`name` FROM `tbl_client`")->queryAll(true);
        $resultPath = '/tmp/bill_opportunity.csv';
        $fp = fopen($resultPath, 'w');
        fputcsv($fp, array('client_id', '主账号', '激活账号数', '商机功能开关', '创建商机数'));
        foreach ($clients as $client) {
            $c = PgActiveRecord::getDbByClientId($client['client_id']);
            if (! $c) {
                continue;
            }
            $opportunityCount = $c->createCommand("select count(1) from tbl_opportunity where client_id={$client['client_id']}")->queryScalar();
            $userId = PrivilegeService::getInstance($client['client_id']);
            $userId = $userId->getAdminUserId();
            $superAdmin = User::getUserObject($userId);
            $clientSwitch = \common\library\account\Client::getClient($client['client_id']);
            $rs = $clientSwitch->getExtentAttributes(['opportunity_switch']);
            fputcsv($fp, array(
                $client['client_id'],
                $superAdmin->getEmail(),
                $rs['opportunity_switch'],
                \common\library\account\Client::getClient($client['client_id'])->getUsedUserNum(),
                $opportunityCount
            ));
            echo $client['client_id'] . "\n";
        }
        fclose($fp);
    }

    /**
     * @param $clientId
     * 将商机的产品自动更新关闭（1.有产品 2.未设置过开关）
     */
    public function actionProductSwitch($clientId = 0)
    {
        $clientList = $this->getClientList($clientId);

        $num = 1;
        $clientCount = count($clientList);
        foreach ($clientList as $client)
        {
            //2019-11-08 11:39:37 smart1.4上线时间（灰度）
            $clientId = $client['client_id'];

            echo "Client $clientId: $num/$clientCount";
            echo "\n";
            LogUtil::info("Client $clientId: $num/$clientCount");
            $num++;

            $db = PgActiveRecord::getDbByClientId($clientId);

            //有产品商机id
            $sql = "select distinct(b.opportunity_id) from tbl_opportunity as a JOIN tbl_opportunity_product as b 
on a.opportunity_id=b.opportunity_id 
where a.client_id=$clientId and a.product_edit_time <= '2019-11-08 11:39:37' and product_template = '{}'";
            $opportunityIds = $db->createCommand($sql)->queryColumn();

            if (empty($opportunityIds))
            {
                self::info("clientId:{$clientId}: empty(opportunityIds)");
                continue;
            }

            $idString = implode(',', $opportunityIds);
            //有设置过开关不处理
            $sql = "select opportunity_id from tbl_opportunity_external where opportunity_id IN ($idString) and key = 'auto_update_product'";
            $hasSetSwitchIds = $db->createCommand($sql)->queryColumn();

            $unSetSwitchIds = array_diff($opportunityIds, $hasSetSwitchIds);
            if (empty($unSetSwitchId))
            {
                self::info("clientId:{$clientId}: empty(unSetSwitchId)");
                continue;
            }

            self::info('unSetSwitchIds:' . implode(',', $unSetSwitchIds));
            $values = [];
            $date = date('Y-m-d H:i:s');
            foreach ($unSetSwitchIds as $unSetSwitchId) {
                $values[] = "($unSetSwitchId,'auto_update_product','0',$clientId,'$date','$date')";
            }
            $valuesString = implode(',', $values);
            $sql = "insert into tbl_opportunity_external (opportunity_id,key,value,client_id,create_time,update_time) VALUES {$valuesString}
 ON CONFLICT (opportunity_id, key) DO UPDATE SET value=EXCLUDED.value, update_time=EXCLUDED.update_time";
            $db->createCommand($sql)->execute();
        }
    }

    public function actionInitMarkTime()
    {
        $clients = $this->getClientList();
        foreach ($clients as $client) {
            $clientId = $client->client_id;
            $sql = "select * from tbl_user_info where client_id={$clientId}";
            $users = \Yii::app()->account_base_db->createCommand($sql)->query();
            if (empty($users)) {
                continue;
            }
            $insertSql = [];
            foreach ($users as $user) {
                $time = date('Y-m-d H:m:s');
                $userId = $user['user_id'];
                $value = 0;

                $insertSql[] = "({$clientId}, {$userId}, {$value}, '{$time}')";

            }
            $sql = "INSERT INTO tbl_opportunity_mark as t1 (client_id, user_id, opportunity_id, update_time ) VALUES "
                . implode(',', $insertSql)
                . "ON CONFLICT (user_id,opportunity_id) DO UPDATE set update_time=EXCLUDED.update_time";

            $sqlCustomer = "INSERT INTO tbl_company_mark as t1 (client_id, user_id, opportunity_id, update_time ) VALUES "
                . implode(',', $insertSql)
                . "ON CONFLICT (user_id,opportunity_id) DO UPDATE set update_time=EXCLUDED.update_time";
            $count = \common\models\client\OpportunityMark::getDbByClientId($clientId)->createCommand($sql)->execute();
            $count1 = \common\models\client\CompanyMark::getDbByClientId($clientId)->createCommand($sql)->execute();

            echo "client_id:{$clientId} opportunity count:{$count} company count:{$count}\n";
            return $count;
        }

    }

    //刷新商机产品company
    public function actionReferOpportunityProduct($clientId, $grey = 1,$expFlag = 0,$startId = 0){

        if ($clientId) {
            $clientIds = [$clientId];
        } elseif ($grey) {
            $clientIds = \common\library\GreyEnvHelper::getGreyClientIds();
        } elseif($expFlag){
            $clientIds= \common\library\account\Client::getExpClientIds();
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        foreach ($clientIds as $clientId) {

            if($clientId == 2){
                continue;
            }
            if($startId && $clientId < $startId){
                continue;
            }
            $db = PgActiveRecord::getDbByClientId($clientId);
            $sql = "SELECT op.opportunity_id, o.company_id
                        FROM tbl_opportunity_product op
                            LEFT JOIN tbl_opportunity o ON o.opportunity_id = op.opportunity_id
                        WHERE op.client_id = {$clientId}
                            AND op.company_id = 0
                        GROUP BY op.opportunity_id, o.company_id";
            $list = $db->createCommand($sql)->queryAll();
            if(!$list){
                $msg = "no opportunity product:client_id:$clientId\r\n";
                self::info($msg);
                continue;
            }
            $count = 0;
            foreach ($list as $item){
                $sql = "update tbl_opportunity_product set company_id = {$item['company_id']} where client_id = {$clientId} and opportunity_id = {$item['opportunity_id']} and company_id = 0";
                $db->createCommand($sql)->execute();
                $count++;
            }
            $msg = "referOpportunityProduct:client_id:$clientId count:$count\r\n";
            self::info($msg);
        }
    }




    public function actionUpdateStageEditTime($clientId = 0, $lastNumber = 0){

	    $clientIds = array_column($this->getClientList($clientId, false, null, null, 0, 0, $lastNumber), 'client_id');

	    LogUtil::info('----------------actionUpdateStageEditTime----------------START----------------clientIds: '.json_encode($clientIds));

	    $total = 0;

	    foreach ($clientIds as $clientId) {

		    $updateCount = 0;

		    $db = PgActiveRecord::getDbByClientId($clientId);

		    $maxOpportunityId = 0;

		    do{

			    $selectSql = 'SELECT opportunity_id, max(update_time)
							FROM tbl_opportunity_stage_statistics
							WHERE client_id = '.$clientId.'
							  AND opportunity_id > '.$maxOpportunityId.'
							  AND stage_id != old_stage_id
							GROUP BY opportunity_id
							ORDER BY opportunity_id
							LIMIT 1000';

			    $list = $db->createCommand($selectSql)->queryAll();

			    $opportunityIds = array_column($list, 'opportunity_id');

			    $count = count($opportunityIds);

			    if ($count == 0) {

				    break;
			    }

			    $maxOpportunityId = max($opportunityIds);




			    $str = ' ( VALUES ';

			    foreach ($list as $value) {

				    $str .= ' (' . implode(', \'', $value) . '\'::TIMESTAMP), ';
			    }

			    $str = rtrim($str, ', ');

			    $str .= ' ) ';




			    $updateSql = 'UPDATE tbl_opportunity
								SET stage_edit_time = arr.max
								FROM '.$str.' AS arr (opportunity_id, max)
								WHERE tbl_opportunity.opportunity_id = arr.opportunity_id
								  AND stage_edit_time = \'1970-01-01 00:00:00\'';

			    $updateCount += $db->createCommand($updateSql)->execute();

		    }while ($count >= 1000);

            PgActiveRecord::releaseDbByClientId($clientId);

            LogUtil::info('clientId:' . $clientId . '------' . 'opportunityIds:' . json_encode($opportunityIds) . '------' . 'updateCount:' . $updateCount);

            $total += $updateCount;
        }

        LogUtil::info('----------------actionUpdateStageEditTime----------------END----------------updateCount: ' . $total);
    }

    public function actionAddDefaultSalesGuide($clientId = 0)
    {
        $admin = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();
        User::setLoginUserById($admin);

        $opportunityStageService = new \common\library\opportunity\stage\OpportunityStageService($clientId);
        $opportunityStageService->systemInitOpportunitySalesFlowAndStage();

        $salesFlowService = new OpportunitySalesFlowService($clientId);
        $defaultSalesFlow = $salesFlowService->getDefaultSalesFlow();
        $flowApi = new FlowApi($clientId);
        $flowApi->setDefaultFlow($defaultSalesFlow['flow_id']);
    }

    // tms功能初始化
    public function actionInitSalesGuide($clientId = 0, $grey = 0, $greyNum = 0)
    {
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }
        $total = 0;

        foreach ($clientIds as $clientId) {
            $admin = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();
            User::setLoginUserById($admin);

            // 配置成单指南总开关
            $client = \common\library\account\Client::getClient($clientId);
            $client->setExtentAttributes([
                \common\library\account\Client::EXTERNAL_KEY_SALES_GUIDE_SWITCH => '1',
            ]);
            $client->saveExtentAttributes();

            //新增外贸销售标准流程
            $systemId = PrivilegeService::getInstance($clientId)->getMainSystemId();

            //获取外贸销售标准流程
            $salesFlowService = new \common\library\opportunity\sales_flow\OpportunitySalesFlowService($clientId);
            $standardSalesFlow = $salesFlowService->getDefaultSalesFlow();

            //老客户情况lite_2021版本删除原有默认流程，只保留外贸销售标准流程
            if (in_array($systemId, PrivilegeConstants::CRM_LITE_IDS)) {

                if (empty($standardSalesFlow)) {
                    $api = new FlowApi($clientId);
                    $mainSaleFlow = $api->getMainFlow();

                    if (!empty($mainSaleFlow)) {
                        $api->updateMainFlag($mainSaleFlow['flow_id'], 0);
                        $api->setOpUser($admin);
                        $api->delete($mainSaleFlow['flow_id']);
                    }
                }
            }

            if (empty($standardSalesFlow)) {
                $opportunityStageService = new \common\library\opportunity\stage\OpportunityStageService($clientId);
                $opportunityStageService->systemInitOpportunitySalesFlowAndStage();
            }

            $privilegeRole = new common\library\privilege_v3\PrivilegeRole($clientId);

            // 给所有角色开启“公司话术”、“公司文档”的“查看”权限
            foreach ($privilegeRole->getAllRoleList() as $role) {
                $privilegeRole->assignPrivilege($role['role_id'], PrivilegeConstants::PRIVILEGE_SCOPE_OWNER,[PrivilegeConstants::PRIVILEGE_CRM_SPEECHCRAFT_VIEW,PrivilegeConstants::PRIVILEGE_CRM_DOCUMENT_VIEW,PrivilegeConstants::PRIVILEGE_SETTING_SALES_GUIDE]);
            }

            $total++;

            self::info("InitSalesGuide:client_id:$clientId complete！\n");

        }
        self::info("InitSalesGuide complete :total:$total");

    }

    /**
     * 重新初始化默认权限
     * 文档域名设置”都归入到“销售助手设置”页面下，直接合并到“销售助手设置”的权限即可   将销售助手设置设置为默认权限
     * @param int $clientId
     * @param int $grey
     * @param int $greyNum
     */
    public function actionInitSalesGuideLite($clientId = 0, $grey = 0, $greyNum = 0){
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }
        $total = 0;

        foreach ($clientIds as $clientId) {
            $privilegeRole = new common\library\privilege_v3\PrivilegeRole($clientId);

            // 给所有角色开启“公司话术”、“公司文档”的“查看”权限
            foreach ($privilegeRole->getAllRoleList() as $role) {
                $privilegeRole->assignPrivilege($role['role_id'], PrivilegeConstants::PRIVILEGE_SCOPE_OWNER,[PrivilegeConstants::PRIVILEGE_CRM_SPEECHCRAFT_VIEW,PrivilegeConstants::PRIVILEGE_CRM_DOCUMENT_VIEW,PrivilegeConstants::PRIVILEGE_SETTING_SALES_GUIDE]);
            }

            $total++;

            self::info("InitSalesGuideLite:client_id:$clientId complete！\n");

        }
        self::info("InitSalesGuideLite complete :total:$total");

    }

    //外贸销售流程初始化(只进行一次)
    public function actionInitStandardFlow($clientId, $grey = 1, $greyNum = null)
    {
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey && !is_null($greyNum)) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }
        $startDate = date('Y-m-d H:i:s', strtotime('-1 month'));
        $endDate = date('Y-m-d H:i:s');

        foreach ($clientIds as $clientId) {
            $adminUserId = PrivilegeService::getInstance($clientId)->getAdminUserId();
            User::setLoginUserById($adminUserId);
            //有商机-近1个月有手动创建的商机 (此情况不进行初始化)
            $opportunity = \common\library\opportunity\Helper::checkHasCreateByUser($clientId,$startDate, $endDate);

            if($opportunity){
                $client = \common\library\account\Client::getClient($clientId);
                $beforeUpdateExternalInfo = $client->getExtentAttributes();
                $client->setExtentAttributes([\common\library\account\Client::EXTERNAL_KEY_INIT_SALES_FLOW  =>'0']);
                $client->saveExtentAttributes();
                if (count($client->getExtentAttributes() ?: []) <= 10) {
                    \LogUtil::info("error_update_client_external_info", [
                        'after_ext_info' => $client->getExtentAttributes(),
                        'before_ext_info' => $beforeUpdateExternalInfo,
                    ]);
                    $client->deleteCache();
                    $client->getExtentAttributes();
                }
            } else {
                $salesFlowService = new OpportunitySalesFlowService($clientId);

                $defaultSalesFlow = $salesFlowService->getDefaultSalesFlow();

                if (!empty($defaultSalesFlow)) {
                    $api = new FlowApi($clientId);

                    $api->setDefaultFlow($defaultSalesFlow['flow_id']);
                }
            }
            self::info("InitStandardFlow:client_id:$clientId complete！\n");
        }

    }


    //商机流程自动映射销售环节
    public function actionAddOpportunityLink($clientId, $grey = 1, $greyNum = null)
    {
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey && !is_null($greyNum)) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        $rank = ['询盘','询价','洽谈','回信','需求','报价','方案','样品','寄样','PI','赢单','订单','输单'];

        foreach ($clientIds as $clientId) {
            $adminUserId = PrivilegeService::getInstance($clientId)->getAdminUserId();
            User::setLoginUserById($adminUserId);

            //获取外贸销售标准流程
            $salesFlowService = new \common\library\opportunity\sales_flow\OpportunitySalesFlowService($clientId);
            $defaultSalesFlow = $salesFlowService->getDefaultSalesFlow();

            if (empty($defaultSalesFlow)) {
                self::info("外贸标准销售流程未初始化. client: $clientId");
                continue;
            }
            //获取阶段
            $stageApi = new StageApi($clientId);
            $stageApi->setFlowId($defaultSalesFlow['flow_id']);
            $defaultStageList = $stageApi->listAll();

            $stageNameMap = array_column($defaultStageList,'stage_id','name');

            $stageIds = array_column($defaultStageList,'stage_id');

            //获取系统环节
            $linkApi = new LinkApi($clientId);
            $linkApi->setStageId($stageIds);
            $linkApi->setType(Link::LINK_TYPE_SYSTEM);

            $linkList = $linkApi->listAll();
            $stageLinkMap = [];
            foreach ($linkList as $link) {
                $stageLinkMap[$link['stage_id']][] = $link['link_id'];
            }

            //获取商机流程
            $flowApi = new FlowApi($clientId);
            $flowApi->setWithDisabled(1);
            $data = $flowApi->flowList();

            foreach($data as $item) {
                if ($item['name'] == $defaultSalesFlow['name']) {
                    continue;
                }

                $stageApi->setFlowId($item['flow_id']);
                $stageList = $stageApi->listAll();

                $newStageList = array_column($stageList, null, 'name');

                //重新排序
                $rankStageList = array_values(array_replace(array_intersect_key(array_flip($rank), $newStageList),$newStageList));

                $completeStage = [];
                $useLinkIds = [];
                foreach($rankStageList as $stage){
                    //询盘
                    if (in_array($stage['name'],['询盘','询价','洽谈','回信','需求','报价']) && !in_array($stage['name'],$completeStage)) {
                        $linkIds = $stageLinkMap[$stageNameMap['询盘']] ?? [];
                        if (!array_intersect($useLinkIds, $linkIds)) {
                            $useLinkIds = array_merge($useLinkIds, $linkIds);
                            $stageApi->edit($stage['stage_id'],['success_rate' => $stage['success_rate'],'link_ids' => json_encode($linkIds)]);
                            $completeStage[] = $stage['name'];

                            $linkApi->updateRelateStage($stage['stage_id'], $linkIds);
                        }

                    }

                    //报价
                    if (in_array($stage['name'],['报价','方案']) && !in_array($stage['name'],$completeStage)) {
                        $linkIds = $stageLinkMap[$stageNameMap['报价']] ?? [];
                        if (!array_intersect($useLinkIds, $linkIds)) {
                            $useLinkIds = array_merge($useLinkIds, $linkIds);
                            $stageApi->edit($stage['stage_id'],['success_rate' => $stage['success_rate'],'link_ids' => json_encode($linkIds)]);
                            $completeStage[] = $stage['name'];

                            $linkApi->updateRelateStage($stage['stage_id'], $linkIds);
                        }
                    }

                    //样品
                    if (in_array($stage['name'],['样品','寄样']) && !in_array($stage['name'],$completeStage)) {
                        $linkIds = $stageLinkMap[$stageNameMap['样品']] ?? [];
                        if (!array_intersect($useLinkIds, $linkIds)) {
                            $useLinkIds = array_merge($useLinkIds, $linkIds);
                            $stageApi->edit($stage['stage_id'],['success_rate' => $stage['success_rate'],'link_ids' => json_encode($linkIds)]);
                            $completeStage[] = $stage['name'];

                            $linkApi->updateRelateStage($stage['stage_id'], $linkIds);
                        }
                    }
                    //订单
                    if (in_array($stage['name'],['PI','订单']) && !in_array($stage['name'],$completeStage)) {
                        $linkIds = $stageLinkMap[$stageNameMap['订单']] ?? [];
                        if (!array_intersect($useLinkIds, $linkIds)) {
                            $useLinkIds = array_merge($useLinkIds, $linkIds);
                            $stageApi->edit($stage['stage_id'],['success_rate' => $stage['success_rate'],'link_ids' => json_encode($linkIds)]);
                            $completeStage[] = $stage['name'];

                            $linkApi->updateRelateStage($stage['stage_id'], $linkIds);
                        }
                    }
                    //赢单
                    if (in_array($stage['name'],['赢单','订单']) && !in_array($stage['name'],$completeStage)) {
                        $linkIds = $stageLinkMap[$stageNameMap['赢单']] ?? [];
                        if (!array_intersect($useLinkIds, $linkIds)) {
                            $useLinkIds = array_merge($useLinkIds, $linkIds);
                            $stageApi->edit($stage['stage_id'],['success_rate' => $stage['success_rate'],'link_ids' => json_encode($linkIds)]);
                            $completeStage[] = $stage['name'];

                            $linkApi->updateRelateStage($stage['stage_id'], $linkIds);
                        }
                    }
                    //输单
                    if (in_array($stage['name'],['输单']) && !in_array($stage['name'],$completeStage)) {
                        $linkIds = $stageLinkMap[$stageNameMap['输单']] ?? [];
                        if (!array_intersect($useLinkIds, $linkIds)) {
                            $useLinkIds = array_merge($useLinkIds, $linkIds);
                            $stageApi->edit($stage['stage_id'],['success_rate' => $stage['success_rate'],'link_ids' => json_encode($linkIds)]);
                            $completeStage[] = $stage['name'];

                            $linkApi->updateRelateStage($stage['stage_id'], $linkIds);
                        }
                    }

                }

            }
            self::info("AddOpportunityLink:client_id:$clientId complete！\n");
        }


    }

    //初始化话术建议
    public function actionSysInitTmsTips($client_id, $flow_id, $user_id)
    {

        $processor = new common\library\tms_tips\TmsTipsInit($client_id, $flow_id, $user_id);
        $processor->run();

    }

    // 更新重名阶段名称
    public function actionUpdateDuplicateStage($clientId = 0, $tryRun = 0){
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        foreach ($clientIds as $clientId) {

            try {
                $admin = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();
                User::setLoginUserById($admin);

                //获取商机流程
                $flowApi = new \common\library\setting\library\flow\FlowApi($clientId);
                $flowApi->getParams()->setWithDisabled(1);
                $data = $flowApi->listAll();

                $api = new StageApi($clientId);

                foreach ($data as $flow) {

                    $api->setFlowId($flow['flow_id']);
                    $stageList = $api->listAll();

                    $stageName = array_column($stageList, 'name');

                    $repeatName = array_diff_assoc($stageName, array_unique($stageName));

                    if (empty($repeatName)) {
                        continue;
                    }

                    var_dump($repeatName);

                    $api->setFlowId($flow['flow_id']);

                    $repeatStage = $api->getByNames($repeatName);

                    $nameArr = [];
                    foreach ($repeatStage as $item) {

                        $count = count(array_keys($nameArr, $item['name'])) + 1;

                        if (!$tryRun) {
                            $api->edit($item['stage_id'], [
                                'field_type'    => $item['type'],
                                'name'          => $item['name']."($count)",
                                'success_rate'  => $item['success_rate'],
                                'tip'           => $item['tip'],
                                'link_ids'      => json_encode($item['link_ids']),
                            ]);
                        }

                        $nameArr[] = $item['name'];

                    }

                    self::info("UpdateDuplicateStage:client_id:$clientId ,flow_id:{$flow['flow_id']} , stage: ".json_encode($repeatStage)."！\n");
                    \LogUtil::info("UpdateDuplicateStage:client_id:$clientId ,flow_id:{$flow['flow_id']} , stage: ".json_encode($repeatStage)."！\n");

                }
            } catch (\Throwable $t) {
                $log = sprintf('client_id=%s, %s', $clientId, $t->getMessage());
                LogUtil::error($log);
                echo $log  . "\n";
            }


        }

    }


    //修复tms权限问题
    public function actionClearTmsFunctions($clientId, $grey = 1, $greyNum = null)
    {
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey && !is_null($greyNum)) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        foreach ($clientIds as $clientId) {

            $client = \common\library\account\Client::getClient($clientId);

            $salesGuideSwitch = $client->getExtentAttributes([\common\library\account\Client::EXTERNAL_KEY_SALES_GUIDE_SWITCH])[\common\library\account\Client::EXTERNAL_KEY_SALES_GUIDE_SWITCH] ?? 0;

            if(!$salesGuideSwitch) {
                $privilegeService = PrivilegeService::getInstance($clientId);
                $privilegeService->removeFunction([
                    \common\library\privilege_v3\PrivilegeConstants::FUNCTIONAL_SPEECHCRAFT,
                    \common\library\privilege_v3\PrivilegeConstants::FUNCTIONAL_DOCUMENT,
                    \common\library\privilege_v3\PrivilegeConstants::FUNCTIONAL_SUGGESTION,
                ]);

                $roleService = PrivilegeService::getInstance($clientId)->getUserPrivilege()->getRoles();
                $roleList = $roleService->getAllRoleList(true, false);

                foreach ($roleList as $roleInfo) {
                    $roleService->removeRolePrivilege($roleInfo['role_id'], [PrivilegeConstants::PRIVILEGE_SETTING_SALES_GUIDE]);
                }

                self::info("ClearTmsFunctions:client_id:$clientId complete！\n");
            }

        }


    }


    //更新环节关联流程-阶段数据
    public function actionUpdateRelatedStage($clientId = 0, $grey = 1, $greyNum = null)
    {
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey && !is_null($greyNum)) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        foreach ($clientIds as $clientId) {

            try {
                $admin = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();
                User::setLoginUserById($admin);

                $client = \common\library\account\Client::getClient($clientId);

                if(!$client->getExtentAttribute(\common\library\account\Client::EXTERNAL_KEY_SALES_GUIDE_SWITCH)) {
                    continue;
                }
                //获取商机流程
                $flowApi = new FlowApi($clientId);
                $flowApi->setWithDisabled(1);
                $flowList = $flowApi->flowList();

                $linkApi = new LinkApi($clientId);
                foreach ($flowList as $flow) {
                    foreach ($flow['stage_info'] as $stage) {

                        $linkApi->updateRelateStage($stage['stage_id'], $stage['link_ids']);

                    }

                }

                self::info("UpdateRelatedStage:client_id:$clientId complete！\n");
            } catch (\Throwable $t) {
                $log = sprintf('client_id=%s, %s', $clientId, $t->getMessage());
                LogUtil::error($log);
                echo $log  . "\n";
            }

        }

    }

    //prometheus开通TMS
    public function actionOpenTms($clientId)
    {
        $this->actionInitSalesGuide($clientId);

        $this->actionInitStandardFlow($clientId);

        $this->actionAddOpportunityLink($clientId);

        $privilege = new PrivilegeCommand($this->getName(), $this->getCommandRunner());

        $privilege->actionRefreshPrivilege($clientId);

    }

    //赋权限初始化TMS
    public function actionInitTms($clientId)
    {
        $this->actionInitSalesGuide($clientId);

        $this->actionInitStandardFlow($clientId);

        $this->actionAddOpportunityLink($clientId);

    }


    //按售卖版本开通TMS（crm_smart、crm_pro、crm_lite），非TMS版本内的已经开通的需要添加使用权限
    public function actionRefreshTmsByVersion($clientId = 0, $grey = 1, $greyNum = null, $force=0)
    {
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey && !is_null($greyNum)) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        foreach ($clientIds as $clientId) {

            try {
                $admin = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();
                User::setLoginUserById($admin);

                $client = \common\library\account\Client::getClient($clientId);

                $privilegeService = PrivilegeService::getInstance($clientId);
                $systemId = $privilegeService->getMainSystemId();

                $switch = $client->getExtentAttribute(\common\library\account\Client::EXTERNAL_KEY_SALES_GUIDE_SWITCH);

                //在TMS售卖版本中的
                if(in_array($systemId,PrivilegeConstants::TMS_ALLOW_SYSTEMS)){

                    if(!$switch || $force) {
                        //先移除权限重新添加
                        $privilegeService->removeFunction([
                            PrivilegeConstants::FUNCTIONAL_SPEECHCRAFT,
                            PrivilegeConstants::FUNCTIONAL_DOCUMENT,
                            PrivilegeConstants::FUNCTIONAL_SUGGESTION,
                        ]);

                        $privilegeService->assignFunction([
                            PrivilegeConstants::FUNCTIONAL_SPEECHCRAFT,
                            PrivilegeConstants::FUNCTIONAL_DOCUMENT,
                            PrivilegeConstants::FUNCTIONAL_SUGGESTION,
                        ]);

                    }

                } else {
                    //没有开通TMS且不在TMS售卖版本内的需要移除权限
                    if(!$switch) {

                        $privilegeService->removeFunction([
                            PrivilegeConstants::FUNCTIONAL_SPEECHCRAFT,
                            PrivilegeConstants::FUNCTIONAL_DOCUMENT,
                            PrivilegeConstants::FUNCTIONAL_SUGGESTION,
                        ]);

                    } else {
                        //不在TMS售卖版本内但开通了TMS的为其添加试用权限
                        $promotion = new \common\library\promotion\Promotion($clientId);
                        $promotion->module = PrivilegeConstants::MODULE_TMS;
                        $promotion->getOperator()->openTms();

                    }
                }

                self::info("RefreshTmsByVersion:client_id:$clientId complete！\n");
            } catch (\Throwable $t) {
                $log = sprintf('client_id=%s, %s', $clientId, $t->getMessage());
                LogUtil::error($log);
                echo $log  . "\n";
            }

        }

    }

    //修复历史阶段被初始化到主流程下问题
    public function actionRepairHistoryStage($clientId, $flowId, $stageIds)
    {
        if(empty($stageIds)){
            return false;
        }

        $db = ProjectActiveRecord::getDbByClientId($clientId);

        $sql = "UPDATE tbl_item_setting SET relate_id =:relate_id WHERE client_id =:client_id and item_id in($stageIds) and item_type = :item_type and module=:module";
        $params = [
            ':client_id' => $clientId,
            ':relate_id' => $flowId,
            ':item_type' => \common\library\setting\item\ItemSettingConstant::ITEM_TYPE_STAGE,
            ':module' => \Constants::TYPE_OPPORTUNITY
        ];

        $db->createCommand($sql)->execute($params);

        self::info("RepairHistoryStage:client_id:$clientId ,flow_id:{$flowId} , stage: {$stageIds}\n");
        \LogUtil::info("RepairHistoryStage:client_id:$clientId ,flow_id:{$flowId} , stage: {$stageIds}\n");

    }


    /**
     * 修复商机阶段状态数据错误问题
     * https://www.tapd.cn/21404721/bugtrace/bugs/view/1121404721001081902
     * @return void
     */
    public function actionFixStageType($dryRun = 1)
    {
        $clientIds = [14833, 8149, 70754, 45962, 83044, 2377]; //生产环境
        //$clientIds = [1,14350];
        foreach ($clientIds as $clientId) {
            $admin = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();
            User::setLoginUserById($admin);
            $db = PgActiveRecord::getDbByClientId($clientId);
            if (!$db) {
                \LogUtil::info("skip for clientId: $clientId, no db");
                return;
            }

            $count = 0;
            $api = new StageApi($clientId);
            $stageList = $api->listAll();
            $stageMap = array_column($stageList, 'type', 'stage_id');
            //1.遍历客户的所有商机
            //2。找出商机中商机阶段是进行中，但是stage_type确实输单类型的
            // 更新stage_type
            foreach (\common\library\util\SqlUtil::iterateTableData($db, 'tbl_opportunity', 'opportunity_id', 200, 'stage,stage_type,trail_active_flag', "client_id={$clientId} and opportunity_id > 0 and enable_flag=1") as $opportunityList)
            {
                $updateValues = [];
                $updateStatisticsValues = [];
                $deleteStatisticsVaules = [];
                foreach ($opportunityList as $opportunity)
                {
                    $type = $stageMap[$opportunity['stage']] ?? null;
                    if (!is_null($type) && $type != $opportunity['stage_type'] && $opportunity['stage_type'] == 3) {
                        $sql = "SELECT statistics_id,opportunity_id,stage_id,stage_type,old_stage_id,old_stage_type,fail_flag FROM tbl_opportunity_stage_statistics where opportunity_id = {$opportunity['opportunity_id']} and client_id = {$clientId} order by statistics_id desc limit 2";
                        $statisticsLog = $db->getCommandBuilder()->createSqlCommand($sql)->queryAll();
                        if (count($statisticsLog) == 2) {
                            if ($statisticsLog[0]['old_stage_id'] == $opportunity['stage'] && $statisticsLog[0]['stage_type'] == 3 && $statisticsLog[1]['stage_id'] == $opportunity['stage'] && $statisticsLog[1]['stage_type'] == 1 && $statisticsLog[1]['fail_flag'] == 1) {
                                $trail_active_flag = 1;
                                $fail_flag = 0;
                                $updateValues[] = '(' . implode(',', [$opportunity['opportunity_id'], $type, $trail_active_flag]) . ')';
                                $deleteStatisticsVaules[] = $statisticsLog[0]['statistics_id'];
                                $updateStatisticsValues[] = '(' . implode(',', [$statisticsLog[1]['statistics_id'], $fail_flag, "'1970-01-01 00:00:00'"]) . ')';
                            }
                        }
                    }
                }
                if (!empty($updateValues)) {
                    $values = implode(',', $updateValues);
                     $sql = "UPDATE tbl_opportunity as t SET stage_type = new_table.stage_type,trail_active_flag = new_table.trail_active_flag FROM (Values {$values}) AS new_table(opportunity_id,stage_type,trail_active_flag)
                WHERE t.opportunity_id=new_table.opportunity_id";
                    $statisticsValues = implode(',', $updateStatisticsValues);
                    $statisticsSql = "UPDATE tbl_opportunity_stage_statistics as t SET fail_flag = new_table.fail_flag,end_time = new_table.end_time::timestamp FROM (Values {$statisticsValues}) AS new_table(statistics_id,fail_flag,end_time)
                WHERE t.statistics_id=new_table.statistics_id";
                    $deleteIds = implode(',',$deleteStatisticsVaules);
                    $deleteSql = "DELETE FROM tbl_opportunity_stage_statistics WHERE  statistics_id in ({$deleteIds})";
                    if (!$dryRun) {
                        $db->getCommandBuilder()->createSqlCommand($sql)->execute();
                        $db->getCommandBuilder()->createSqlCommand($statisticsSql)->execute();
                        $db->getCommandBuilder()->createSqlCommand($deleteSql)->execute();
                    } else {
                        var_dump($sql,$statisticsSql,$deleteSql);
                        \LogUtil::info($sql.PHP_EOL.$statisticsSql.PHP_EOL.$deleteSql);
                    }
                }
                $count += count($opportunityList);
                \LogUtil::info("process for client:{$clientId}, count: $count");
            }
        }
    }

    public function actionDeleteOpportunity($clientId, $all=0, $opportunity=0)
    {
        $adminUserId = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();
        User::setLoginUserById($adminUserId);
        $db = PgActiveRecord::getDbByClientId($clientId);
        $sql  = "select opportunity_id from tbl_opportunity where client_id={$clientId} and enable_flag=1";

        if ($opportunity) {
            $sql .= " and opportunity_id={$opportunity}";
        }

        $opportunityList = $db->createCommand($sql)->queryAll();
        $opportunityChunkArr = array_chunk($opportunityList ,1000);

        foreach ($opportunityChunkArr as  $k =>  $opportunityItemList) {
            $delOpportunityIds = array_column($opportunityItemList, 'opportunity_id');
            $op = new \common\library\opportunity\OpportunityBatchOperator($adminUserId);
            $op->setParams(['opportunity_ids' => $delOpportunityIds, 'show_all' => true]);
            $count = $op->delete();

            self::info("del k: {$k} ret: {$count} rel:".count($delOpportunityIds).' opportunity_ids: '.implode(',', $delOpportunityIds));
        }
        self::info(" total : ".count($opportunityList));
    }

    /**
     * 修复商机附件编辑后格式问题
     * https://www.tapd.cn/21404721/bugtrace/bugs/view/1121404721001088926
     * @return void
     */
    public function actionFixFileList($clientId = 0, $grey = 1, $greyNum = null, $dryRun = 1)
    {
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey && !is_null($greyNum)) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        $i = 1;
        $total = count($clientIds);
        foreach ($clientIds as $client_id) {
            \LogUtil::info("clientId[$client_id] 进度 $i / $total");
            $admin = \common\library\privilege_v3\PrivilegeService::getInstance($client_id)->getAdminUserId();
            if (empty($admin)) {
                \LogUtil::info(("[{$client_id}][{$admin}] empty adminUserId continue'"));
                continue;
            }
            User::setLoginUserById($admin);

            $db = \PgActiveRecord::getDbByClientId($client_id);

            if (empty($db)) {
                \LogUtil::info(("[{$client_id}] empty db continue"));
                continue;
            }

            foreach (SqlUtil::iterateTableData($db, 'tbl_opportunity', 'opportunity_id', 1000, 'file_list', "client_id={$client_id} and enable_flag = 1") as $opportunityList)
            {
                $updateValues = [];
                foreach ($opportunityList as $opportunity)
                {
                    $update_flag = false;
                    if($opportunity['file_list'] != '[]' && $opportunity['file_list'] != '{}') {
                        $file_list = [];
                        $opportunity['file_list'] = json_decode($opportunity['file_list'], true) ?? [];
                        foreach ($opportunity['file_list'] as $item) {
                            if (is_string($item) && !in_array($item,[0,'default'])) {
                                $update_flag = true;
                                $file_id = $item;
                                $file = \UploadFile::findByFileId($file_id);
                                if (!$file) {
                                    continue;
                                }
                                $file_list[] = [
                                    'user_id'     => $file['user_id'],
                                    'file_id'     => $file_id,
                                    'create_time' => date('Y-m-d H:i:s'),
                                    'refer_type'  => \common\library\opportunity\Opportunity::FILE_LIST_REFER_TYPE_DEFAULT,
                                    'refer_id'    => 0
                                ];
                            } else {
                                $file_list[] = $item;
                            }
                        }

                        if ($update_flag) {
                            $update_file_list = json_encode($file_list);
                            $update_file_list = "'".$update_file_list."'";
                            $updateValues[] = '(' . implode(',', [$opportunity['opportunity_id'], $update_file_list]) . ')';
                        }
                    }
                }

                $updateArray = array_chunk($updateValues,500);
                foreach ($updateArray as $updateArr)
                {
                    $values = implode(',', $updateArr);
                    $updateSql = "UPDATE tbl_opportunity as t SET file_list = new_table.file_list::jsonb FROM (Values {$values}) AS new_table(opportunity_id,file_list)
                            WHERE t.opportunity_id=new_table.opportunity_id";
                    if (!$dryRun) {
                        $db->createCommand($updateSql)->execute();
                    } else {
                        \LogUtil::info(($updateSql));
                    }

                }
            }
            \LogUtil::info(("[{$client_id}] fix Opportunity FileList Done"));
            $i++;
        }
    }

    /**
     * 修复商机自定义字段格式问题
     * https://www.tapd.cn/21404721/bugtrace/bugs/view/1121404721001089872
     * @param $clientId
     * @param $grey
     * @param $greyNum
     * @param $dryRun
     * @return void
     * @throws CDbException
     * @throws ProcessException
     */
    public function actionFixExternalFieldData($clientId = 0, $grey = 1, $greyNum = null, $dryRun = 1)
    {
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey && !is_null($greyNum)) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        $i = 1;
        $total = count($clientIds);
        foreach ($clientIds as $client_id) {
            echo("clientId[$client_id] 进度 $i / $total".PHP_EOL);
            $admin = \common\library\privilege_v3\PrivilegeService::getInstance($client_id)->getAdminUserId();
            if (empty($admin)) {
                \LogUtil::info(("[{$client_id}][{$admin}] empty adminUserId continue'"));
                continue;
            }

            $db = \PgActiveRecord::getDbByClientId($client_id);

            if (empty($db)) {
                \LogUtil::info(("[{$client_id}] empty db continue"));
                continue;
            }

            foreach (SqlUtil::iterateTableData($db, 'tbl_opportunity', 'opportunity_id', 1000, 'external_field_data', "client_id={$client_id} and enable_flag = 1 and jsonb_typeof(external_field_data)='array'") as $opportunityList)
            {
                $updateValues = [];
                foreach ($opportunityList as $opportunity)
                {
                    if($opportunity['external_field_data'] != '[]') {
                        $new_external_field_dta = [];
                        $external_field_dta = json_decode($opportunity['external_field_data'],true);
                        if (!is_array($external_field_dta))
                            continue;

                        foreach($external_field_dta as $value) {
                            foreach ($value as $key => $item) {
                                if (!isset($new_external_field_dta[$key])) {
                                    $new_external_field_dta[$key] = $item;
                                }
                            }
                        }
                        $new_external_field_dta =empty($new_external_field_dta) ? '{}' : json_encode($new_external_field_dta);
                        $updateValues[] = '(' . implode(',', [$opportunity['opportunity_id'], "'$new_external_field_dta'"]) . ')';
                    } else {
                        $updateValues[] = '(' . implode(',', [$opportunity['opportunity_id'], "'{}'"]) . ')';
                    }
                }

                $updateArray = array_chunk($updateValues,500);
                foreach ($updateArray as $updateArr)
                {
                    $values = implode(',', $updateArr);
                    $updateSql = "UPDATE tbl_opportunity as t SET external_field_data = new_table.external_field_data::jsonb FROM (Values {$values}) AS new_table(opportunity_id,external_field_data)
                            WHERE t.opportunity_id=new_table.opportunity_id";
                    if (!$dryRun) {
                        $db->createCommand($updateSql)->execute();
                    } else {
                        echo(($updateSql));
                    }
                }
            }
            echo(("[{$client_id}] fix Opportunity ExternalFieldData Done"));
            $i++;
        }
    }


    public function actionUpdateLastTrailTime($client_id, $dryRun = 1)
    {
        $admin = \common\library\privilege_v3\PrivilegeService::getInstance($client_id)->getAdminUserId();
        if (empty($admin)) {
            \LogUtil::info(("[{$client_id}][{$admin}] empty adminUserId continue'"));
            return false;
        }
        User::setLoginUserById($admin);

        $db = PgActiveRecord::getDbByClientId($client_id);
        if (empty($db)) {
            \LogUtil::info(("[{$client_id}] empty db continue'"));
            return false;
        }

        $opportunityList = new \common\library\opportunity\OpportunityList($client_id);
        $opportunityList->setViewingUserId($admin);
        $opportunityList->setUserId($admin);
        $opportunityList->setShowAll(1);
        $opportunityList->setSkipPermissionCheck(1);
        $opportunityList->getFormatter()->setSpecifyFields([
            'opportunity_id',
            'trail_time',
            'update_time',
            'order_time'
            ]);
        $opportunityList->getFormatter()->setShowLastTrail(true);
        $list = $opportunityList->find();
        $updateSqlArray = [];
        foreach ($list as $opportunityInfo)
        {
            if (empty($opportunityInfo['last_trail']) || empty($opportunityInfo['last_trail']['create_time'])) {
                continue;
            }

            if (strtotime($opportunityInfo['trail_time']) < strtotime($opportunityInfo['last_trail']['create_time'])) {
                $time = $opportunityInfo['last_trail']['create_time'];
                $set = "trail_time='{$time}', order_time=(CASE WHEN order_time < '{$time}'::timestamp THEN '{$time}' ELSE order_time END), update_time=(CASE WHEN update_time < '{$time}'::timestamp THEN '{$time}' ELSE update_time END)";
                $sql = "update tbl_opportunity set {$set} where opportunity_id = {$opportunityInfo['opportunity_id']}";
                $updateSqlArray[$opportunityInfo['opportunity_id']] = $sql;
            }
        }

        $sqlArray = array_chunk($updateSqlArray, 100, true);

        foreach ($sqlArray as $sqlArr)
        {
            $updateSql = implode(';', $sqlArr);
            $updateOpportunityIds = array_keys($sqlArr);
            self::info("opportunity fix last_trail_time clientId:$client_id sql: $updateSql".PHP_EOL);
            if ($dryRun == 0)
            {
                $db->createCommand($updateSql)->execute();
                //商机绩效计算
                $opportunity = new OpportunityBatchOperator($admin);
                $opportunity->statistics($client_id,$updateOpportunityIds);
            }
        }

    }

    //删除重复的输单阶段
    public function actionDeleteDuplicateFailStage($client_id = 0, $tryRun = 0){
        if ($client_id) {
            $clientIds = explode(',', $client_id);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        foreach ($clientIds as $clientId) {

            try {
                $admin = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();
                if (empty($admin)) {
                    \LogUtil::info(("[{$clientId}][{$admin}] empty adminUserId continue'"));
                    continue;
                }

                User::setLoginUserById($admin);

                $db = PgActiveRecord::getDbByClientId($clientId);
                if (empty($db)) {
                    \LogUtil::info(("[{$clientId}] empty db continue'"));
                    continue;
                }


                //获取商机流程
                $flowApi = new \common\library\setting\library\flow\FlowApi($clientId);
                $data = $flowApi->listAll();

                $api = new StageApi($clientId);

                foreach ($data as $flow) {

                    $api->setFlowId($flow['flow_id']);
                    $api->setStageType(\common\library\setting\library\stage\Stage::STAGE_FAIL_STATUS);
                    $stageList = $api->listAll();

                    if (count($stageList) < 2) {
                        continue;
                    }

                    $reserveFailStage = [];
                    $deleteFailStage = [];

                    foreach ($stageList as $item) {
                        //判断是否有引用数据
                        $checker = new \common\library\checker\ItemSettingChecker($clientId, \Constants::TYPE_OPPORTUNITY, \common\library\setting\item\ItemSettingConstant::ITEM_TYPE_STAGE);
                        $checkResult = $checker->checkData([$item['stage_id']]);
                        if (isset($checkResult[0]['count']) && $checkResult[0]['count'] > 0) {
                            $reserveFailStage[] = $item['stage_id'];
                        } else {
                            $deleteFailStage[] = $item['stage_id'];
                        }
                    }

                    if (!empty($reserveFailStage) && !empty($deleteFailStage)) {
                        //删除没有引用的多余输单阶段
                        if ($tryRun) {
                            $api->delete($deleteFailStage);
                        }
                    }

                    self::info("DeleteDuplicateStage:client_id:$clientId ,flow_id:{$flow['flow_id']} , delete stage: ".json_encode($deleteFailStage)."！\n");
                    \LogUtil::info("DeleteDuplicateStage:client_id:$clientId ,flow_id:{$flow['flow_id']} , delete stage: ".json_encode($deleteFailStage)."！\n");

                }
            } catch (\Throwable $t) {
                $log = sprintf('DeleteDuplicateStage:client_id=%s, %s', $clientId, $t->getMessage());
                LogUtil::error($log);
                echo $log  . "\n";
            }


        }

    }

    /**
     * 处理商机部门异常
     * https://www.tapd.cn/21404721/bugtrace/bugs/view/1121404721001094283
     * @param $client_id
     * @param $dryRun
     * @return void
     */
    public function actionRefreshDepartment($client_id = 0, $dryRun = 0){
        if ($client_id) {
            $clientIds = explode(',', $client_id);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        foreach ($clientIds as $clientId) {

            try {
                $admin = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();
                if (empty($admin)) {
                    \LogUtil::info(("[{$clientId}][{$admin}] empty adminUserId continue'"));
                    continue;
                }

                User::setLoginUserById($admin);

                $db = PgActiveRecord::getDbByClientId($clientId);
                if (empty($db)) {
                    \LogUtil::info(("[{$clientId}] empty db continue'"));
                    continue;
                }

                //处理内灰开始12-01 至 12-08晚上的问题数据
                //获取商机
                $opportunityList = new \common\library\opportunity\OpportunityList($clientId);
                $opportunityList->setSkipPermissionCheck(true);
                $opportunityList->setDepartment(0);
                $opportunityList->setCreateStartDate('2023-12-01');
                $opportunityList->setCreateEndDate('2023-12-08');
                $opportunityList->getFormatter()->setSpecifyFields(['opportunity_id','main_user','department']);
                $list = $opportunityList->find();

                $updateValues = [];
                foreach ($list as $opportunity) {
                    //重刷部门字段
                    if (empty($opportunity['main_user'])) {
                        continue;
                    }

                    $userDepartments = \common\library\performance\Helper::getUserPerformanceDepartment($clientId, $opportunity['main_user']);
                    $department = $userDepartments[$opportunity['main_user']] ?? 0;
                    if (!empty($department)) {
                        $updateValues[$opportunity['opportunity_id']] = '(' . implode(',', [$opportunity['opportunity_id'], $department]) . ')';
                    }

                }

                $updateArray = array_chunk($updateValues,500, true);
                foreach ($updateArray as $updateArr)
                {
                    $values = implode(',', $updateArr);
                    $updateOpportunityIds = array_keys($updateArr);
                    $updateSql = "UPDATE tbl_opportunity as t SET department = new_table.department FROM (Values {$values}) AS new_table(opportunity_id,department)
                            WHERE t.opportunity_id=new_table.opportunity_id";
                    if (!$dryRun) {
                        $db->createCommand($updateSql)->execute();
                        //商机绩效计算
                        $opportunity = new OpportunityBatchOperator($admin);
                        $opportunity->statistics($client_id,$updateOpportunityIds);
                    } else {
                        echo(($updateSql));
                    }
                }

            } catch (\Throwable $t) {
                $log = sprintf('RefreshDepartment:client_id=%s, %s', $clientId, $t->getMessage());
                LogUtil::error($log);
                echo $log  . "\n";
            }


        }

    }

    /**
     * 修复商机金额异常数据
     */
    public function actionRefreshAmount($client_id = 0, $dryRun = 0){
        if ($client_id) {
            $clientIds = explode(',', $client_id);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        foreach ($clientIds as $clientId) {

            try {
                $admin = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();
                if (empty($admin)) {
                    \LogUtil::info(("[{$clientId}][{$admin}] empty adminUserId continue'"));
                    continue;
                }

                User::setLoginUserById($admin);

                $db = PgActiveRecord::getDbByClientId($clientId);
                if (empty($db)) {
                    \LogUtil::info(("[{$clientId}] empty db continue'"));
                    continue;
                }

                $sql = "select opportunity_id, amount, amount_rmb, amount_usd, currency, exchange_rate, exchange_rate_usd
from tbl_opportunity
where client_id = {$clientId}
  and enable_flag = 1
  and auto_summary_flag = 1
  and amount_rmb = amount_usd
  and currency ='USD'
  and amount > 0
";
                //获取问题商机
                $opportunityData = $db->createCommand($sql)->queryAll();

                if (empty($opportunityData)) {
                    continue;
                }

                $updateValues = [];
                foreach ($opportunityData as $opportunity) {
                    //重刷金额
                    $amount = $opportunity['amount'];
                    $exchange_rate = $opportunity['exchange_rate'];
                    $amount_rmb = round(floatval($amount) * floatval($exchange_rate) / 100, 2);
                    if (!empty($amount_rmb)) {
                        $updateValues[$opportunity['opportunity_id']] = '(' . implode(',', [$opportunity['opportunity_id'], $amount_rmb]) . ')';
                    }

                }

                $updateArray = array_chunk($updateValues,500, true);
                foreach ($updateArray as $updateArr)
                {
                    $values = implode(',', $updateArr);
                    $updateOpportunityIds = array_keys($updateArr);
                    $updateSql = "UPDATE tbl_opportunity as t SET amount_rmb = new_table.amount_rmb FROM (Values {$values}) AS new_table(opportunity_id,amount_rmb)
                            WHERE t.opportunity_id=new_table.opportunity_id";
                    if (!$dryRun) {
                        $db->createCommand($updateSql)->execute();
                        //商机绩效计算
                        $opportunity = new OpportunityBatchOperator($admin);
                        $opportunity->statistics($client_id,$updateOpportunityIds);
                    } else {
                        echo(($updateSql));
                    }
                }

            } catch (\Throwable $t) {
                $log = sprintf('RefreshAmount:client_id=%s, %s', $clientId, $t->getMessage());
                LogUtil::error($log);
                echo $log  . "\n";
            }


        }

    }

    public function actionFixOpportunityExternalData($clientId = 81301, $dryRun=1)
    {
        $db = PgActiveRecord::getDbByClientId($clientId);

        $maxId = $db->createCommand("select opportunity_id from tbl_opportunity where client_id={$clientId} and enable_flag=1 and (create_time > '2024-06-19' or update_time > '2024-06-19') order by opportunity_id desc limit 1")->queryScalar();
        $currId = 0;
        $updateSqlArray = [];
        $fieldTypeMap = CustomFieldService::getExternalFieldTypeMap($clientId, \CONSTANTS::TYPE_OPPORTUNITY);
        //机器翻译 查看Map
        $translateLanguageMap = \common\library\EasyTranslate::getTranslateLanguageMap($clientId, 'en');
        $translateLanguageMap = array_flip($translateLanguageMap);

        $sql = "select opportunity_id,external_field_data from tbl_opportunity where  client_id={$clientId} and enable_flag = 1 and (create_time > '2024-06-19' or update_time > '2024-06-19')";
        self::info("begin fix client user tag  client_id:$clientId  maxId:{$maxId} currId: {$currId}");
        while ( $currId < $maxId )
        {
            $opportunityList = $db->createCommand($sql . ' and opportunity_id >' . $currId . ' order by opportunity_id asc limit 200')->queryAll(true);
            if (empty($opportunityList))
                break;

            foreach ($opportunityList as $opportunity)
            {
                $currId = $opportunity['opportunity_id'];
                $external_field_data = json_decode($opportunity['external_field_data'], true);
                $update_flag = false;
                foreach ($external_field_data as $fieldId => $fieldValue) {
                    //有单选/有多选字段
                    //匹配到了英文Map
                    $fieldIdType = $fieldTypeMap[$fieldId] ?? null;
                    if (in_array($fieldIdType, [CustomFieldService::FIELD_TYPE_SELECT, CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT])) {
                        if (is_array($fieldValue)) {
                            foreach ($fieldValue as $k => $option) {
                                if (isset($translateLanguageMap[$option])) {
                                    $update_flag = true;
                                    $fieldValue[$k] = $translateLanguageMap[$option];
                                }
                            }
                            $external_field_data[$fieldId] = $fieldValue;
                        } else {
                            if (isset($translateLanguageMap[$fieldValue])) {
                                $update_flag = true;
                                $external_field_data[$fieldId] = $translateLanguageMap[$fieldValue];
                            }

                        }
                    }

                }

                if ($update_flag) {
                    $external_field_data = json_encode($external_field_data);
                    $updateSqlArray[] = "update tbl_opportunity set external_field_data = '{$external_field_data}' where opportunity_id={$opportunity['opportunity_id']}";
                }

                self::info("company fix external_data clientId:$clientId companyId：$currId count:".count($updateSqlArray));
            }
        }

        $sqlArray = array_chunk($updateSqlArray,100);
        foreach ($sqlArray as $sqlArr)
        {
            $updateSql = implode(';', $sqlArr);
            if ($dryRun == 1) {
                //只在模拟执行的时候打印
                self::info("company fix external_data clientId:$clientId  sql: $updateSql".PHP_EOL);
            }
            if ($dryRun == 0)
            {
                $db->createCommand($updateSql)->execute();
            }
        }
        self::info("opportunity fix external_data completed");
        //执行完脚本进行es刷新
    }
}
