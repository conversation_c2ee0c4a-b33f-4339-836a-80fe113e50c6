<?php
use common\library\privilege_v3\PrivilegeConstants;
use protobuf\Common\DeviceType;
use protobuf\Common\PBCommand;
use protobuf\Common\PBConstants;
use protobuf\Common\PBErrorCode;
use protobuf\Common\PBHeader;
use protobuf\Common\PBHeader\PBRspStatus;
use protobuf\Common\PBPackage;
use protobuf\CRMCommon\PBApplicationOptionReq;
use protobuf\CRMCommon\PBApplicationOptionRsp;
use protobuf\CRMCommon\PBClientSettingRsp;
use protobuf\CRMCommon\PBDeleteUserSettingReq;
use protobuf\CRMCommon\PBDepartmentDetailTreeRsp;
use protobuf\CRMCommon\PBDesktopInitRsp;
use protobuf\CRMCommon\PBDiskFileListReq;
use protobuf\CRMCommon\PBDiskFileListRsp;
use protobuf\CRMCommon\PBDisplayUserInfoRsp;
use protobuf\CRMCommon\PBNoticeListRsp;
use protobuf\CRMCommon\PBNoticeReadReq;
use protobuf\CRMCommon\PBPermissionTreeReq;
use protobuf\CRMCommon\PBPermissionUserListReq;
use protobuf\CRMCommon\PBPermissionUserListRsp;
use protobuf\CRMCommon\PBPinListReq;
use protobuf\CRMCommon\PBPinListRsp;
use protobuf\CRMCommon\PBPinType;
use protobuf\CRMCommon\PBSaveUserSettingReq;
use protobuf\CRMCommon\PBUserMailInfo;
use protobuf\CRMCommon\PBUserSetting;
use protobuf\CRMCommon\PBUserSettingRsp;
use protobuf\CRMCommon\PBUserTokenRsp;
use protobuf\Customer\PBBusinessCardOperateLogReq;
use protobuf\Customer\PBBusinessCardOperateLogRsp;
use protobuf\Customer\PBBusinessCardTransferReq;
use protobuf\Customer\PBCompanyBaseInfoReq;
use protobuf\Customer\PBCompanyBaseInfoRsp;
use protobuf\Customer\PBCompanyCompareReq;
use protobuf\Customer\PBCompanyCompareRsp;
use protobuf\Customer\PBCompanyDetailInfoReq;
use protobuf\Customer\PBCompanyDetailInfoRsp;
use protobuf\Customer\PBCompanyListByFilterReq;
use protobuf\Customer\PBCompanyListHeaderRep;
use protobuf\Customer\PBCompanyListHeaderReq;
use protobuf\Customer\PBCompanyListReq;
use protobuf\Customer\PBCompanyListRsp;
use protobuf\Customer\PBCompanyTagRsp;
use protobuf\Customer\PBCompanyTrailStatusRsp;
use protobuf\Customer\PBCustomerAddTagReq;
use protobuf\Customer\PBCustomerHoldReq;
use protobuf\Customer\PBCustomerPoolListRsp;
use protobuf\Customer\PBCustomerRemarkReq;
use protobuf\Customer\PBCustomerRemoveTagReq;
use protobuf\Customer\PBCustomerResetTagReq;
use protobuf\Customer\PBCustomerSearchReq;
use protobuf\Customer\PBCustomerSetGroupReq;
use protobuf\Customer\PBCustomerSetPoolReq;
use protobuf\Customer\PBCustomerSetStarReq;
use protobuf\Customer\PBCustomerSetStatusReq;
use protobuf\Customer\PBCustomerSetTagReq;
use protobuf\Customer\PBCustomerSetTagRsp;
use protobuf\Customer\PBDynamicQuickTextListRsp;
use protobuf\Customer\PBEditCompanyByFieldReq;
use protobuf\Customer\PBEditCompanyByFieldsReq;
use protobuf\Customer\PBFormFieldListReq;
use protobuf\Customer\PBFormFieldListRsp;
use protobuf\Customer\PBSubmitCompanyReq;
use protobuf\Customer\PBSubmitCompanyRsp;
use protobuf\Customer\PBSubmitCustomerInfo;
use protobuf\Customer\PBTrailListReq;
use protobuf\Customer\PBTrailListRsp;
use protobuf\Customer\PBTrailParamRsp;
use protobuf\Customer\PBTrailStatReq;
use protobuf\Customer\PBTrailStatRsp;
use protobuf\Email\PBEmailsMatchReq;
use protobuf\Email\PBEmailsMatchRsp;
use protobuf\Email\PBUploadFileRenameReq;
use protobuf\Email\PBUploadFileRenameRsp;
use protobuf\HttpPush\PBHttpPushReq;
use protobuf\HttpPush\PBHttpPushReq\PBIdInfo;
use protobuf\HttpPush\PBHttpPushType;
use protobuf\MailSetting\PBBindUserMailReq;
use protobuf\MailSetting\PBBindUserMailRsp;
use protobuf\MailSetting\PBDeleteMailTagReq;
use protobuf\MailSetting\PBEditMailFolderReq;
use protobuf\MailSetting\PBEditMailFolderRsp;
use protobuf\MailSetting\PBEditMailRuleReq;
use protobuf\MailSetting\PBEditMailRuleRsp;
use protobuf\MailSetting\PBEditMailSignatureReq;
use protobuf\MailSetting\PBEditMailSignatureRsp;
use protobuf\MailSetting\PBEditMailSignatureSettingReq;
use protobuf\MailSetting\PBEnableMailRuleReq;
use protobuf\MailSetting\PBMailBlackListReq;
use protobuf\MailSetting\PBMailBlackListRsp;
use protobuf\MailSetting\PBMailBlackSaveReq;
use protobuf\MailSetting\PBMailDetectBindReq;
use protobuf\MailSetting\PBMailDetectBindRsp;
use protobuf\MailSetting\PBMailExposeStatReq;
use protobuf\MailSetting\PBMailExposeStatRsp;
use protobuf\MailSetting\PBMailFolderInfo;
use protobuf\MailSetting\PBMailFolderListRsp;
use protobuf\MailSetting\PBMailRule;
use protobuf\MailSetting\PBMailRuleFilter;
use protobuf\MailSetting\PBMailRuleFilterType;
use protobuf\MailSetting\PBMailRuleListRsp;
use protobuf\MailSetting\PBMailRuleOperation;
use protobuf\MailSetting\PBMailSignature;
use protobuf\MailSetting\PBMailSignatureListRsp;
use protobuf\MailSetting\PBMailSignatureSetting;
use protobuf\MailSetting\PBMailSignatureSettingRsp;
use protobuf\MailSetting\PBMailTagListRsp;
use protobuf\MailSetting\PBMailTemplateReq;
use protobuf\MailSetting\PBMailTemplateRsp;
use protobuf\MailSetting\PBMailTextDeleteReq;
use protobuf\MailSetting\PBMailTextReq;
use protobuf\MailSetting\PBMailTextRsp;
use protobuf\MailSetting\PBUnbindUserMailReq;
use protobuf\MailSetting\PBUserMailBindInfo;
use protobuf\MailSetting\PBUserMailBindInfoReq;
use protobuf\MailSetting\PBUserMailBindInfoRsp;
use protobuf\MailSetting\PBUserMailDetailListRsp;
use protobuf\MailSetting\PBUserMailListReq;
use protobuf\MailSetting\PBUserMailListRsp;
use protobuf\MailSync\PBBatchEditMailReq;
use protobuf\MailSync\PBEmailIdentityReq;
use protobuf\MailSync\PBEmailIdentityRsp;
use protobuf\MailSync\PBMailAttachmentInfo;
use protobuf\MailSync\PBMailAttachmentReq;
use protobuf\MailSync\PBMailAttachmentRsp;
use protobuf\MailSync\PBMailBaseInfo;
use protobuf\MailSync\PBMailCardReq;
use protobuf\MailSync\PBMailCardRsp;
use protobuf\MailSync\PBMailCompareReq;
use protobuf\MailSync\PBMailCompareRsp;
use protobuf\MailSync\PBMailCompensateReq;
use protobuf\MailSync\PBMailCompensateRsp;
use protobuf\MailSync\PBMailContentReq;
use protobuf\MailSync\PBMailContentRsp;
use protobuf\MailSync\PBMailDistributeReq;
use protobuf\MailSync\PBMailDistributeRsp;
use protobuf\MailSync\PBMailDraftInfo;
use protobuf\MailSync\PBMailExtraDataReq;
use protobuf\MailSync\PBMailExtraDataRsp;
use protobuf\MailSync\PBMailExtraStatusInfo;
use protobuf\MailSync\PBMailInfo;
use protobuf\MailSync\PBMailInfoBit;
use protobuf\MailSync\PBMailSearchListReq;
use protobuf\MailSync\PBMailSearchListRsp;
use protobuf\MailSync\PBMailSession;
use protobuf\MailSync\PBMailSetTodoReq;
use protobuf\MailSync\PBMailStatusInfo;
use protobuf\MailSync\PBMailSyncReq;
use protobuf\MailSync\PBMailSyncRsp;
use protobuf\MailSync\PBMailTagInfo;
use protobuf\MailSync\PBMailTodoDetail;
use protobuf\MailSync\PBMailTodoSetCompletedReq;
use protobuf\MailSync\PBMailTodoSyncRsp;
use protobuf\MailSync\PBMailVersion;
use protobuf\MailSync\PBSendMailReq;
use protobuf\MailSync\PBSubordinateMailReq;
use protobuf\MailSync\PBSubordinateMailRsp;
use protobuf\MailSync\PBWriteMailDraftRsp;
use protobuf\MailSync\PBWriteMailForwardAsAttachmentReq;
use protobuf\MailSync\PBWriteMailForwardAsAttachmentRsp;
use protobuf\MailSync\PBWriteMailForwardRsp;
use protobuf\OkkiAi\PBAiAgentAssetAnalysisReq;
use protobuf\OkkiAi\PBAiAgentAssetAnalysisRsp;
use protobuf\OkkiAi\PBaramFieldValueArr;
use protobuf\OkkiAi\PBParamFieldValueObj;
use protobuf\Opportunity\PBSubmitOpportunityReq;
use protobuf\Opportunity\PBSubmitOpportunityRsp;
use protobuf\PushData\PBPushCommand;
use protobuf\PushData\PBPushData;
use protobuf\PushData\PBPushDatum;
use protobuf\PushData\PBPushMailVersion;
use protobuf\PushData\PBPushUserSettingVersion;
use protobuf\TodoFeed\PBUnReadCardListReq;
use protobuf\CRMCommon\PBUploadFileExtRsp;
use protobuf\Report\PBDigitalReportListRsp;
use protobuf\Approval\PBApprovalFormListReq;
use protobuf\Approval\PBApprovalFormListRsp;
use protobuf\WhatsappCloud\PBGetAllSameClientUserRsp;
use protobuf\WorkJournal\PBWorkJournalSubmitStatisticsReq;
use protobuf\WorkJournal\PBWorkJournalSubmitStatisticsRsp;
use protobuf\WorkJournal\PBWorkJournalTemplateListReq;
use protobuf\WorkJournal\PBWorkJournalTemplateListRsp;

/**
 * Created by PhpStorm.
 * User: andy
 * Date: 2017/12/20
 * Time: 11:45
 */

class StormFuryExampleCommand extends CrontabCommand
{
    const TEST_DEV = [
        'wuyiwai' => 'wuyiwai.php.dev.xiaoman.cn',
        'nuxse' => 'http://nuxse.php.dev.xiaoman.cn/',
        'k2' => 'http://k2.dev.xiaoman.cn/',
        'test' => 'http://k.dev.xiaoman.cn',
        'seven' => 'https://sevenshi.php.dev.xiaoman.cn/',
        'bing' => 'http://bing.k.xiaoman.cn',
        'amu' => 'http://amu.php.dev.xiaoman.cn',
        'test-app' => 'http://test.app.xiaoman.cn',
        'test-desktop' => 'http://desktop.dev.xiaoman.cn/',
        'crm-desktop' => 'https://crm-desktop-api.xiaoman.cn',
        'omgk' => 'https://omgk.xiaoman.cn',
        'omgm' => 'https://omgm.xiaoman.cn',
        'app-dev' => 'https://app.dev.xiaoman.cn',
        'june' => 'http://june.php.dev.xiaoman.cn',
        'karonyang' => 'https://karonyang.php.dev.xiaoman.cn/',
        'yuanyi' => 'https://one.php.dev.xiaoman.cn/',
        'raylei' => 'https://rayboy.php.dev.xiaoman.cn',
        'bob' => 'https://bob.php.dev.xiaoman.cn',
        'fulili' => 'https://fulili.php.dev.xiaoman.cn',
        'bogiang' => 'https://bogiang.php.dev.xiaoman.cn',
        'steven' => 'https://stevencrm.php.dev.xiaoman.cn',
        'onegong' => 'https://one.php.dev.xiaoman.cn',
        'workjournal' => 'https://app-work-report.feature.dev.xiaoman.cn',
        'tonyfeng' => 'tony.php.dev.xiaoman.cn',
        'rayleitest' => 'https://okki-ai-generate-data.feature.dev.xiaoman.cn',
        'tyson' => 'https://tyson24.php.dev.xiaoman.cn',
        'milktea' => 'https://milktea.php.dev.xiaoman.cn',
        'nanrui' => 'https://nanrui.php.dev.xiaoman.cn',
        '34' => 'https://leads-iteration-3-4-1075059.story.dev.xiaoman.cn',
        'wuliwei' => 'https://wuliwei.php.dev.xiaoman.cn',
        'ai-mail'=>'https://app-ai-mail.feature.dev.xiaoman.cn',
        'gooray'=>'https://gooray.php.dev.xiaoman.cn',
        'app-ai-report-v2' => 'https://app-ai-report-v2.feature.dev.xiaoman.cn',
        'xyy' => 'https://xyy.php.dev.xiaoman.cn/',
        '1093893' => 'https://okkiai-iteration-4-3-1093893.story.dev.xiaoman.cn',
        '5543' => 'https://okkiai-iteration-3-6-1085543.story.dev.xiaoman.cn',
        'online' => 'https://crm.xiaoman.cn',
        'tina' => 'https://tina.php.dev.xiaoman.cn',
        'lzy' => 'https://lzy.php.dev.xiaoman.cn',
        'jasper' => 'https://jasper.php.dev.xiaoman.cn',
    ];

    private $userInfo = [];


//    private $userAccount = '<EMAIL>';
//    private $userAccount = '<EMAIL>';
    private $userAccount = '<EMAIL>';
    private $clientType = 'mail';

	public function actionSubmitCompany()
	{
        $this->setUserAccount('<EMAIL>');

//        $param = '{"company_id":**********,"archiveFlag":1,"company":{"homepage":"http://www.baidu.cm","name":"Test-ym-dump-address2021110201","pool_id":"**********","fax":"78978978945645645645645645645645645646","timezone":"8","address":"九若路与Z123交叉口 中国四川省阿坝藏族羌族自治州九寨沟县九若路与Z123交叉口"},"customers":[]}';
        $param = '{"archiveFlag":true,"company":{"**********":"\"\"","**********":"\"\"","**********":"123","tel":"{\"tel\":\"25555\",\"tel_area_code\":\"93\"}","remark":"\"\"","scale_id":"\"\"","**********":"\"\"","**********":"\"\"","**********":"\"\"","cus_tag":"[]","pool_id":"**********","**********":"\"\"","name":"\"拔得动\"","homepage":"\"yyyyy\"","serial_id":"\"\"","**********":"\"\"","image_list":"\"\"","trail_status":"\"\"","category_ids":"[[3]]","**********":"\"\"","**********":"\"\"","**********":"\"\"","**********":"\"\"","**********":"\"\"","annual_procurement":"\"\"","group_id":"\"\"","2777664001":"\"\"","**********":"\"\"","**********":"\"\"","intention_level":"\"\"","origin":"0","short_name":"\"\"","timezone":"\"\"","fax":"\"\"","country":"\"\"","biz_type":"\"\"","address":"\"\"","**********":"\"\"","star":"\"\""},"customers":[{"info":{"email":"\"\"","**********":"\"\"","**********":"\"\"","customer_id":"0","**********":"\"\"","remark":"\"\"","**********":"\"\"","tel_list":"\"\"","name":"\"呃呃呃\"","**********":"\"\"","**********":"\"\"","image_list":"\"\"","**********":"\"\"","post":"\"\"","contact":"\"\"","gender":"\"\"","**********":"\"\"","post_grade":"\"\"","main_customer_flag":"1","birth":"\"\""}}]}';

        $param = '{
  "archiveFlag": 1,
  "buyerAccountId": *********,
  "company": {
    "**********": "\"\"",
    "**********": "\"\"",
    "**********": "\"\"",
    "**********": "\"\"",
    "**********": "\"\"",
    "**********": "\"\"",
    "**********": "\"\"",
    "**********": "\"\"",
    "**********": "\"\"",
    "**********": "\"\"",
    "address": "\"\"",
    "annual_procurement": "\"0\"",
    "biz_type": "\"渠道商\"",
    "category_ids": "[]",
    "company_id": "null",
    "country": "{\"country\":\"CN\",\"province\":\"\",\"city\":\"\"}",
    "cus_tag": "[\"**********\",\"**********\"]",
    "fax": "\"\"",
    "group_id": "null",
    "homepage": "\"http://beta.alibaba.net\"",
    "image_list": "[]",
    "intention_level": "\"0\"",
    "name": "\"sichuanjiudacossmp13any1122\"",
    "origin_list": "[\"404\"]",
    "pool_id": "\"**********\"",
    "remark": "\"\"",
    "scale_id": "null",
    "short_name": "\"\"",
    "star": 5,
    "tel": "[]",
    "timezone": "\"8\"",
    "trail_status": "\"\""
  },
  "customers": [
    {
      "info": {
        "**********": "",
        "**********": "",
        "**********": "",
        "**********": "",
        "**********": "",
        "**********": "",
        "**********": "",
        "**********": "",
        "**********": "[]",
        "**********": "",
        "birth": "",
        "contact": "[]",
        "email": "<EMAIL>",
        "gender": 0,
        "image_list": "[\"**********\"]",
        "main_customer_flag": 1,
        "name": "Sharon xiaoman",
        "post": "",
        "post_grade": 0,
        "remark": "",
        "tel_list": "[]"
      }
    }
  ],
  "leadId": **********,
  "sellerAccountId": *********
}';

        $param = json_decode($param, true);

        $req = new PBSubmitCompanyReq();

        $req->setLeadId($param['leadId'] ?? 0);
        $req->setArchiveFlag($param['archiveFlag']);

        $req->setCompany($param['company']);

        $customers = [];

        foreach ($param['customers'] as $item) {

            $item = $item['info'];

            array_walk($item, function (&$value) {

                $value = is_array($value) ? json_encode($value) : $value;
            });

            $customer = new PBSubmitCustomerInfo();

            $customer->setInfo($item);

            $customers[] = $customer;
        }

        $req->setCustomers($customers);

        $a = json_decode($req->serializeToJsonString(), true);

        $url = self::TEST_DEV['gooray'] . '/api/stormsFury/CustomerV2Write/SubmitCompany';
        $url =  'localhost:8888/api/stormsFury/CustomerV2Write/SubmitCompany';

		$rsp = new PBSubmitCompanyRsp();
		$this->process($url, $req, $rsp, false, false);
		echo $rsp->serializeToJsonString();
	}

    private function setUserLoginInfo()
    {
        //User::setLoginUserById(765);

        $userData = \common\library\account\service\LoginService::produceSkey($this->userAccount, $this->clientType, 300)['cookie'];
        $userData = array_column($userData, 'value', 'key');
        $this->userInfo = [
            "session_key" => $userData['pskey'],
            "user_id" => $userData['userId'],
            "client_id" => $userData['clientId'],
        ];
    }

    public function actionLastContact()
    {
        $req = new \protobuf\CRMCommon\PBMailContactReq();
        $url = self::TEST_DEV['seven'] . '/api/stormsFury/contactRead/LastContactList';
        $rsp = new \protobuf\CRMCommon\PBMailContactRsq();
        $this->process($url, $req, $rsp);
        echo $rsp->serializeToJsonString();
    }


    private function setUserAccount($account)
    {
        $this->userAccount = $account;
    }

    /**
     * @param $rsp
     * @param $needDecode
     */
    public function logInfo($rsp, $needDecode = true, $printJson=false)
    {
        /**
         * @var \Google\Protobuf\Internal\Message $rsp
         */
        $rsp = $rsp->serializeToJsonString();
        $rsp = $needDecode ? json_decode($rsp) : $rsp;
        if (is_string($rsp) || is_numeric($rsp)) {
            echo $rsp;
        } else {
            if($printJson){
                echo json_encode($rsp);
            }else{
                print_r($rsp);
            }
        }
    }
	public function actionRename(){

		$url = self::TEST_DEV['test-desktop'] . '/api/stormsFury/upload/rename';

		$req = new PBUploadFileRenameReq();

		$req->setFileId(**********);
		$req->setFileName('test1.pdf');

		$rsp = new PBUploadFileRenameRsp();

		$this->process($url, $req, $rsp, false);
		$this->logInfo($req);
		$this->logInfo($rsp);
    }

    public function actionMailExposeStat(){


	    $url = self::TEST_DEV['seven'] . '/api/stormsFury/MailSettingRead/MailExposeStat';

	    $this->setUserAccount('<EMAIL>');

	    $req = new PBMailExposeStatReq();

	    $req->setUserMailIds([********, ********]);

	    $rsp = new PBMailExposeStatRsp();

	    $this->process($url, $req, $rsp, false);
	    $this->logInfo($req);
	    $this->logInfo($rsp);exit;
    }

    public function actionSendTimeSuggest()
    {
        $req = new \protobuf\Email\PBSendTimeSuggestReq();
        $emails = ['<EMAIL>'];
        $req->setEmailList($emails);
        $url = self::TEST_DEV['wuyiwai'] . '/api/stormsFury/mailRead/sendTimeSuggest';
        $rsp = new \protobuf\Email\PBSendTimeSuggestRsp();
        $this->process($url, $req, $rsp, false);
        $this->logInfo($req);
        $this->logInfo($rsp);

    }

    public function actionLargeAttachList()
    {
        $req =new \protobuf\Email\PBLargeAttachListReq();
        $req->setPage(1);
        $req->setPageSize(20);
        $url = self::TEST_DEV['yoyo'] . "/api/stormsFury/mailRead/largeAttachList";
        $rsp = new \protobuf\Email\PBLargeAttachListRsp();
        $this->process($url, $req, $rsp, false);
        echo $rsp->serializeToJsonString()."\n";

    }



    public function actionCreateLargeAttach()
    {
        $req =new \protobuf\Email\PBCreateLargeAttachReq();
        $field_id = '1101015178';
        $req->setFileId($field_id);

        $url = self::TEST_DEV['yoyo'] . "/api/stormsFury/mailWrite/createLargeAttach";
        $rsp = new \protobuf\Email\PBCreateLargeAttachRsp();
        $this->process($url, $req, $rsp, false);
        echo $rsp->serializeToJsonString()."\n";
    }
    public function actionRemoveLargeAttach()
    {
        $req =new \protobuf\Email\PBRemoveLargeAttachReq();
        $field_ids = ['38030307'];
        $mail_id = 1101019469;

        $req->setFileIds($field_ids);
        $req->setMailId($mail_id);

        $url = self::TEST_DEV['yoyo'] . "/api/stormsFury/mailWrite/removeLargeAttach";
        $rsp = new \protobuf\Email\PBRemoveLargeAttachRsp();
        $this->process($url, $req, $rsp, false);
        echo $rsp->serializeToJsonString()."\n";
    }

    public function actionUseLargeAttach()
    {
        $req =new \protobuf\Email\PBUseLargeAttachReq();
        $field_ids = ['1100883139'];
        $req->setFileIds($field_ids);
        $req->setMailId('1100890741');
        $url = self::TEST_DEV['yoyo'] . "/api/stormsFury/mailWrite/useLargeAttach";
        $rsp = new \protobuf\Email\PBUseLargeAttachRsp();
        $this->process($url, $req, $rsp, false);
        echo $rsp->serializeToJsonString()."\n";
    }



    public function actionPushData()
    {
        $value = '87091faaaceeb73363b3441f29f9450f';
        $bytes = str_repeat(chr(0), \Google\Protobuf\Internal\CodedOutputStream::MAX_VARINT64_BYTES);
        $size = \Google\Protobuf\Internal\CodedOutputStream::writeVarintToArray($value, $bytes, true);
        $req = new PBHttpPushReq();
        $req->setType(PBHttpPushType::HTTP_PUSH_USER);
        $req->setAppKey($value);
        $userIds = $req->getUserIds();
        $idInfo = new PBIdInfo();
        $idInfo->setId(765);
        $userIds[] = $idInfo;
        $req->setUserIds($userIds);

        $data = new PBPushData();
        $list = $data->getList();


        $datum = new PBPushDatum();
        $datum->setCommand(PBPushCommand::PUSH_USER_SETTING);

        $ver = new PBPushUserSettingVersion();
        $ver->setUserId(765);
        $ver->setVersion(765);
        $ver->setModule(765);

        $datum->setDatum($ver->serializeToString());

        $list[] = $datum;

        $datum = new PBPushDatum();
        $datum->setCommand(PBPushCommand::PUSH_MAIL);

        $ver = new PBPushMailVersion();
        $ver->setUserMailId(765);
        $ver->setVersion(1);

        $datum->setDatum($ver->serializeToString());

        $list[] = $datum;

        $data->setList($list);

        $req->setContent($data->serializeToString());

        $url = 'http://10.0.5.162:9557/pushMessage';

        $rspStatus = new PBRspStatus();
        $rspStatus->setErrorCode(0);
        $rspStatus->setErrorMsg("");

        $header = new PBHeader();
        $header->setSequence(1);
        $header->setVersion(PBConstants::PROTO_VERSION);
        $header->setMagicNumber(PBConstants::MAGIC_NUMBER);
        $header->setCommand(PBCommand::CRM_HTTP_REQUEST);
        $header->setIsResponse(0);
        $header->setSessionKey('f7c351e21d8f92ea424d022efab99f0805272cc3e92360a87e8532ab90aa0708');
        $header->setClientId(1);
        $header->setUserId(765);
        $header->setDeviceType(DeviceType::WINDOWS);
        $header->setDeviceId('andytest');
        $header->setClientVersion('123');
        $header->setResponseStatus($rspStatus);

        $package = new PBPackage();
        $package->setHeader($header);
        if ($req !== null) {
            $package->setBody($req->serializeToString());
        }

        LogUtil::info('body json: ');
        LogUtil::info($req->serializeToJsonString());
        LogUtil::info('command json: ');
        LogUtil::info($data->serializeToJsonString());
        LogUtil::info('package: ');
        LogUtil::info(bin2hex($package->serializeToString()));
        LogUtil::info('body: ');
        LogUtil::info(bin2hex($req->serializeToString()));

        $data = $package->serializeToString();

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, ["content-type:application/x-protobuf"]);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        $ret = curl_exec($ch);

        if ($ret === false) {
            $error = curl_error($ch);
            $errorCode = curl_errno($ch);
            $error .= ' error_no:' . $errorCode;

            echo "error: ";
            var_dump($error);
            var_dump($errorCode);
        }
        curl_close($ch);
        var_dump($ret);

    }

    public function actionGetVersion()
    {
//        $url = self::TEST_DEV['test'] . "/api/stormsFury/interface/UserVersionInfo";
        $url = "http://kcallback.xiaoman.cn/stormsFury/interface/userVersionInfo";
        $rsp = new PBPushData();
        $this->process($url, null, $rsp);

        /**
         * @var $list PBPushDatum[]
         */
        $list = $rsp->getList();

        foreach ($list as $datum) {

            printf("command %s \n", $datum->serializeToJsonString());
        }
    }

    protected function process(
        $url,
        $req,
        \Google\Protobuf\Internal\Message &$rsp = null,
        bool $echoDirect = false,
        $getFullRsp = false,
        $xdebugSession = ''
    )
    {
        print_r([
            "url" => $url
        ]);
        $rspStatus = new PBRspStatus();
        $rspStatus->setErrorCode(0);
        $rspStatus->setErrorMsg('');

        //设置用户登录key
        $this->setUserLoginInfo();

        $header = new PBHeader();
        $header->setSequence(1);
        $header->setVersion(PBConstants::PROTO_VERSION);
        $header->setMagicNumber(PBConstants::MAGIC_NUMBER);
        $header->setCommand(PBCommand::CRM_HTTP_REQUEST);
        $header->setIsResponse(0);
        $header->setSessionKey($this->userInfo['session_key']);
        $header->setClientId($this->userInfo['client_id']);
        $header->setUserId($this->userInfo['user_id']);
        $header->setDeviceType(DeviceType::MAC);
        $header->setDeviceId('andytest');
        $header->setClientVersion('6.10.5');
        $header->setResponseStatus($rspStatus);
        // $header->setLanguage('en');

        $package = new PBPackage();
        $package->setHeader($header);
        if ($req !== null) {
            $package->setBody($req->serializeToString());
        }

        $data = $package->serializeToString();

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            "content-type:application/x-protobuf",
            "x-xiaoman-app-version:6.10.5"
        ]);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);

        if (!empty($xdebugSession)) {
            curl_setopt($ch, CURLOPT_COOKIE, "XDEBUG_SESSION=${xdebugSession}; ");
        }


        $ret = curl_exec($ch);
        if ($ret === false) {
            $error = curl_error($ch);
            $errorCode = curl_errno($ch);
            $error .= ' error_no:' . $errorCode;

            echo "error: ";
            var_dump($error);
            var_dump($errorCode);
        }
        curl_close($ch);
        if ($echoDirect) {
            echo "开始打印信息: " . PHP_EOL;
            var_dump($ret);
            echo "结束打印信息: " . PHP_EOL;
            die();
        }
        $result = new PBPackage();
        @$result->mergeFromString($ret);

        $rspHeader = $result->getHeader();
        $rspStatus = $rspHeader->getResponseStatus();

        if ($rspStatus->getErrorCode() !== PBErrorCode::OK && !$getFullRsp) {
            printf("error! code: %s msg: %s status: %s\n", $rspStatus->getErrorCode(), $rspStatus->getErrorMsg(), $rspStatus->getStatusCode());
            die();
        }

        if($getFullRsp){
            $package = new PBPackage();
            $package->mergeFromString($ret);
            return $package;
        }else{
            $body = $result->getBody();
            if (!empty($body)) {
                $rsp->mergeFromString($body);
            }
        }
    }

    public function actionException()
    {
        $url = self::TEST_DEV['test'] . "/api/stormsFury/mailRead/exception";
        $url = "/api/stormsFury/mailRead/exception";
        $rsp = new PBUserMailListRsp();
        $this->process($url, null, $rsp);

    }

    public function actionUserInfo()
    {
        $url = self::TEST_DEV['yoyo'] . "/api/stormsFury/userRead/info";
        $rsp = new PBDisplayUserInfoRsp();
        $this->process($url, null, $rsp,false);
        echo $rsp->serializeToJsonString() . "\n";
    }

    public function actionDepartmentTree()
    {
        $url = self::TEST_DEV['test'] . "/api/stormsFury/userRead/departmentDetailTree";
        $rsp = new PBDepartmentDetailTreeRsp();
        $this->process($url, null, $rsp, true);

        print_r(json_decode($rsp->serializeToJsonString(), true));
    }


    public function actionTree()
    {
        $url = self::TEST_DEV['yoyo'] . "/api/stormsFury/departmentRead/tree";

        $req = new \protobuf\CRMCommon\PBDepartmentTreeReq();
        $req->setShowMember(true);
        $req->setIncludeUndistributedNode(true);
        $req->setClientFlag(true);

        $rsp = new \protobuf\CRMCommon\PBDepartmentTreeRsp();
        $this->process($url, null, $rsp,false);
        echo $rsp->serializeToJsonString() . "\n";

    }


    public function actionGetDraft()
    {
        $req = new PBMailCompareReq();
        $versionList = $req->getVersionList();
        $version = new PBMailVersion();
        $version->setUserMailId(30000047);
        $version->setVersion(4210);
        $versionList[] = $version;
        $req->setVersionList($versionList);

        $rsp = new PBMailCompareRsp();
        $url = self::TEST_DEV['test'] . "/api/stormsFury/mailRead/SyncInfo";
        $this->process($url, $req, $rsp);

        /**
         * @var $sessionList PBMailSession[]
         */
        $sessionList = $rsp->getSession();
        foreach ($sessionList as $session) {
            printf("user_mail_id %s session_id %s size %s version %s expired_time %s\n", $session->getUserMailId(), $session->getSessionId(),
                $session->getSize(), $session->getVersion(), $session->getExpiredTime());

            if (!$session->getSize()) {
                continue;
            }

            $url = self::TEST_DEV['test'] . "/api/stormsFury/mailRead/sync";

            $req = new PBMailSyncReq();
            $req->setUserMailId($session->getUserMailId());
            $req->setSessionId($session->getSessionId());
            $req->setOffset(0);
            $req->setSize(1000);

            $rsp = new PBMailSyncRsp();

            $this->process($url, $req, $rsp, true);

            /**
             * @var $draftList PBMailDraftInfo[]
             */
            $draftList = $rsp->getDraftList();

            foreach ($draftList as $draft) {
                $base = $draft->getBaseInfo();
                $status = $draft->getStatusInfo();
                $content = $draft->getContent();

                printf("draft mail_id %s subject %s sender %s receiver %s email_size %s folder_id %s content %s\n",
                    $base->getMailId(), $base->getSubject(), $base->getSender(), $base->getReceiver(), $base->getEmailSize(),
                    $status->getFolderId(), $content ? substr(snappy_uncompress($content->getPlainText()), 0, 50) : '脏数据');
            }
        }
    }

    public function actionGetMail()
    {

	    $this->setUserAccount('<EMAIL>');

        $url = self::TEST_DEV['test'] . "/api/stormsFury/mailSettingRead/userMailList";
        $rsp = new PBUserMailListRsp();

        $this->process($url, null, $rsp);

        /**
         * @var $userMailList PBUserMailInfo[]
         */
        $userMailList = $rsp->getUserMailList();

        $req = new PBMailCompareReq();
        $versionList = $req->getVersionList();
        foreach ($userMailList as $userMailInfo) {
            printf("user_mail_id %s email %s\n", $userMailInfo->getUserMailId(), $userMailInfo->getEmail());

            $version = new PBMailVersion();
            $version->setUserMailId($userMailInfo->getUserMailId());
            $version->setVersion(860000);
            $versionList[] = $version;
        }
        $req->setVersionList($versionList);
        $rsp = new PBMailCompareRsp();
        $url = self::TEST_DEV['nuxse'] . "/api/stormsFury/mailRead/SyncInfo";
        $this->process($url, $req, $rsp);

        /**
         * @var $sessionList PBMailSession[]
         */
        $sessionList = $rsp->getSession();
        foreach ($sessionList as $session) {
            printf("user_mail_id %s session_id %s size %s version %s expired_time %s\n", $session->getUserMailId(), $session->getSessionId(),
                $session->getSize(), $session->getVersion(), $session->getExpiredTime());

            if (!$session->getSize()) {
                continue;
            }

            $url = self::TEST_DEV['nuxse'] . "/api/stormsFury/mailRead/sync";

            $req = new PBMailSyncReq();
            $req->setUserMailId($session->getUserMailId());
            $req->setSessionId($session->getSessionId());
            $req->setOffset(0);
            $req->setSize(1000);

            $rsp = new PBMailSyncRsp();
            $this->process($url, $req, $rsp);

            printf("\nbase info\n");
            /**
             * @var $addList PBMailInfo[]
             */
            $addList = $rsp->getAddList();
            foreach ($addList as $add) {
                /**
                 * @var $base PBMailBaseInfo
                 */
                $base = $add->getBaseInfo();
                /**
                 * @var $status PBMailStatusInfo
                 */
                $status = $add->getStatusInfo();

                printf("mail_id %s subject %s sender %s receiver %s email_size %s folder_id %s root_mail_id %s source_mail_id %s\n",
                    $base->getMailId(), $base->getSubject(), $base->getSender(), $base->getReceiver(), $base->getEmailSize(),
                    $status->getFolderId(),$base->getRootMailId(),$base->getSourceMailId());
            }
            printf("\nstatus info\n");
            /**
             * @var $editList PBMailStatusInfo[]
             */
            $editList = $rsp->getEditList();

            foreach ($editList as $statusInfo) {
                printf("mail_id %s  folder_id %s\n", $statusInfo->getMailId(), $statusInfo->getFolderId());
            }

            printf("\ntag info\n");
            /**
             * @var $tagList PBMailTagInfo[]
             */
            $tagList = $rsp->getTagList();

            foreach ($tagList as $tagInfo) {
                printf("mail_id %s tag count %s\n", $tagInfo->getMailId(), count($tagInfo->getTagList()));
            }

            /**
             * @var $draftList PBMailDraftInfo[]
             */
            $draftList = $rsp->getDraftList();

            foreach ($draftList as $draft) {
                $base = $draft->getBaseInfo();
                $status = $draft->getStatusInfo();
                $content = $draft->getContent();

                printf("draft mail_id %s subject %s sender %s receiver %s email_size %s folder_id %s content %s\n",
                    $base->getMailId(), $base->getSubject(), $base->getSender(), $base->getReceiver(), $base->getEmailSize(),
                    $status->getFolderId(), $content ? substr(snappy_uncompress($content->getPlainText()), 0, 50) : '脏数据');
            }

            /**
             * @var $extraStatusList PBMailExtraStatusInfo[]
             */
            $extraStatusList = $rsp->getExtraStatusList();
            foreach ($extraStatusList as $extraStatusInfo) {
                printf("extraStatus  mail_id %s risk_flag %s receive_origin_sender %s \n",
                    $extraStatusInfo->getMailId(),$extraStatusInfo->getRiskFlag(),$extraStatusInfo->getReceiveOriginSender());
                print_r(iterator_to_array($extraStatusInfo->getRiskReasons()));

            }
        }
    }

    public function actionUserMailList()
    {
//        $this->setUserAccount('<EMAIL>');
        $this->setUserAccount('<EMAIL>');

        $req = new PBUserMailListReq();
//        $req->setUserId(********);
        $req->setUserId(765);
        $req->setManageableUser(0);
        $url = self::TEST_DEV['yuanyi'] . "/api/stormsFury/mailSettingRead/userMailList";
        $rsp = new PBUserMailListRsp();
        $this->process($url, null, $rsp);
        $this->logInfo($req, true);
        $this->logInfo($rsp, true);
    }

    public function actionUserMailList2(){
        $this->setUserAccount('<EMAIL>');

        $req = new PBUserMailListReq();
//        $req->setUserId(1);
        $req->setManageableUser(0);
        $rsp = new PBUserMailListRsp();

        $url = self::TEST_DEV['app-dev'] . "/api/stormsFury/mailSettingRead/userMailList";
        echo $url;
        $this->process($url, $req, $rsp);

        echo $rsp->serializeToJsonString();
    }


    public function actionSignList()
    {
        $url = self::TEST_DEV['test'] . "/api/stormsFury/mailSettingRead/signatureList";
        $rsp = new PBMailSignatureListRsp();

        $this->process($url, null, $rsp);

        /**
         * @var $list PBMailSignature[]
         */
        $list = $rsp->getList();

        foreach ($list as $elem) {
            printf("name %s content %s\n", $elem->getName(), $elem->getContent());
        }
    }

    public function actionFolderList()
    {
        $rsp = new PBMailFolderListRsp();

        $url = self::TEST_DEV['test'] . "/api/stormsFury/mailSettingRead/folderList";
        $this->process($url, null, $rsp);

        /**
         * @var $list  PBMailFolderInfo[]
         */
        $list = $rsp->getFolderList();

        foreach ($list as $elem) {
            printf("folder_id %s name %s\n", $elem->getFolderId(), $elem->getName());
        }
    }

    public function actionFolderList2(){
        $req = new \protobuf\MailSetting\PBMailFolderListReq();
        $req->setUserId(11859021);

        $rsp = new PBMailFolderListRsp();
        $url = self::TEST_DEV['amu'] . "/api/stormsFury/mailSettingRead/folderList";
        $this->process($url, $req, $rsp);

        /**
         * @var $list  PBMailFolderInfo[]
         */
        $list = $rsp->getFolderList();

        foreach ($list as $elem) {
            printf("folder_id %s name %s\n", $elem->getFolderId(), $elem->getName());
        }
    }

    public function actionSignatureSetting()
    {
        $rsp = new PBMailSignatureSettingRsp();

        $url = self::TEST_DEV['test'] . "/api/stormsFury/mailSettingRead/SignatureSetting";
        $this->process($url, null, $rsp);

        /**
         * @var $list PBMailSignatureSetting[]
         */
        $list = $rsp->getList();

        foreach ($list as $elem) {
            printf("user_mail_id %s default_sign_id %s reply_sign_id %s\n",
                $elem->getUserMailId(), $elem->getDefaultSignature(), $elem->getReplyForwardSignature());
        }
    }

    public function actionTagList()
    {
        $rsp = new PBMailTagListRsp();
        $url = self::TEST_DEV['wuyiwai'] . "/api/stormsFury/mailSettingRead/tagList";
        $this->process($url, null, $rsp, false);
        $this->logInfo($rsp);
    }

    public function actionMailPinList()
    {
        $req = new PBPinListReq();
        $req->setType(PBPinType::PIN_MAIL);

        $rsp = new PBPinListRsp();

        $url = self::TEST_DEV['test'] . "/api/stormsFury/userRead/pinList";
        $this->process($url, $req, $rsp);

        $pinData = $rsp->getData();

        $pinData = iterator_to_array($pinData->getReferIds());

        printf("count %s\n", count($pinData));
    }

    public function actionCompensate()
    {
        $this->setUserAccount('<EMAIL>');
        $req = new PBMailCompensateReq();
        $req->setMailIds([**********]);
        $flag = 0;
        $flag |= (1 << PBMailInfoBit::MAIL_TODO_DETAIL_BIT);
        $req->setFlag($flag);

        $rsp = new PBMailCompensateRsp();
        $url = self::TEST_DEV['seven'] . "/api/stormsFury/mailRead/compensate";
        $this->process($url, $req, $rsp, false);
        $this->logInfo($req);
        $this->logInfo($rsp);
    }


    public function actionMailContent()
    {
        $req = new PBMailContentReq();
        $mailIds = $req->getMailIdList();
        $mailIds[] = **********;
        $req->setMailIdList($mailIds);

        $rsp = new PBMailContentRsp();
        $url = self::TEST_DEV['wuyiwai'] . "/api/stormsFury/mailRead/content";
        $this->process($url, $req, $rsp);
        $contentList = $rsp->getContentList();
        foreach ($contentList as $item) {
            echo "----" . PHP_EOL;
            echo "[mailId] => {$item->getMailId()}" . PHP_EOL;
            echo "[content] => {$item->getContent()}" . PHP_EOL;
            echo "[plainText] => {$item->getPlainText()}" . PHP_EOL;
            echo "----" . PHP_EOL;
        }
    }

    public function actionMailAttachment()
    {
        $req = new PBMailAttachmentReq();
        $mailIds = $req->getMailIdList();
        $mailIds = [40267361,40265062,40265052,40248478];
        $req->setMailIdList($mailIds);

        $rsp = new PBMailAttachmentRsp();

        $url = self::TEST_DEV['yoyo'] . "/api/stormsFury/mailRead/attachmentList";
        $this->process($url, $req, $rsp,false);

        /**
         * @var $list PBMailAttachmentInfo[]
         */
        $list = $rsp->getAttchmentList();

        foreach ($list as $elem) {
            printf("mail_id %s\n", $elem->getMailId());

//            $aList = $elem->getAttachmentList();
//            foreach ($aList as $file) {
//                printf("attachment filename %s size %s\n", $file->getFileName(), $file->getFileSize());
//            }
//            $iList = $elem->getInlineImageList();
//            foreach ($iList as $file) {
//                printf("inline image filename %s size %s\n", $file->getFileName(), $file->getFileSize());
//            }
        }
    }

    public function actionRuleList()
    {
        $rsp = new PBMailRuleListRsp();

        $url = self::TEST_DEV['june'] . "/api/stormsFury/mailSettingRead/ruleList";
        $this->process($url, null, $rsp, false);
        $this->logInfo($rsp);die;
        /**
         * @var $list PBMailRule[]
         */
        $list = $rsp->getList();

        foreach ($list as $elem) {
            $userMail = $elem->getApplyUserMailId() ? $elem->getApplyUserMailId() : '全部';
            $continue = $elem->getContinueApply() ? '继续执行' : '不继续执行';
            $enable = $elem->getEnableFlag() ? '启用' : '禁用';
            printf("rule_id %s user_mail %s %s %s\n", $elem->getRuleId(), $userMail, $continue, $enable);

            /**
             * @var $filters PBMailRuleFilter[]
             */
            $filters = $elem->getFilters();

            foreach ($filters as $filter) {
                $str = '';
                switch ($filter->getType()) {
                    case PBMailRuleFilterType::MAIL_RULE_FILTER_SENDER:
                        $str = '发件人';
                        break;
                    case PBMailRuleFilterType::MAIL_RULE_FILTER_SENDER_DOMAIN:
                        $str = '发件域';
                        break;
                    case PBMailRuleFilterType::MAIL_RULE_FILTER_RECEIVER:
                        $str = '收件人';
                        break;
                    case PBMailRuleFilterType::MAIL_RULE_FILTER_CC:
                        $str = '抄送人';
                        break;
                    case PBMailRuleFilterType::MAIL_RULE_FILTER_SUBJECT:
                        $str = '主题';
                        break;
                }
                $compare = '';
                switch ($filter->getCompareType()) {
                    case 0:
                        $compare = '不包含';
                        break;
                    case 1:
                        $compare = '包含';
                        break;
                    case 2:
                        $compare = '属于私海';
                        break;
                    case 3:
                        $compare = '属于公海';
                        break;
                    case 4:
                        $compare = '等于';
                        break;
                    case 5:
                        $compare = '不等于';
                        break;
                }
                printf("filter %s %s %s\n", $str, $compare, $filter->getValue());
            }
        }
    }

    public function actionSubordinateList()
    {
        $this->setUserAccount('<EMAIL>');
        $req = new PBMailSearchListReq();
        $req->setMailDetailFlag(1);
        $req->setUserId(51060);
        $req->setLimit(20);


        $rsp = new PBMailSearchListRsp();
        $url = self::TEST_DEV['jasper'] . "/api/stormsFury/mailRead/subordinateList";
        $this->process($url, $req, $rsp, false);
        $this->logInfo($rsp);
    }

    public function actionMailSearchList()
    {
        $this->setUserAccount('<EMAIL>');
        $req = new PBMailSearchListReq();
        $req->setIncludeFolderIds([**********]);
        $req->setUserId(********);
        $req->setLimit(20);
        $rsp = new PBMailSearchListRsp();

        $url = self::TEST_DEV['wuyiwai'] . "/api/stormsFury/mailRead/searchList";
        $this->process($url, $req, $rsp, true);
        $this->logInfo($rsp);
    }

    public function actionWriteSignature()
    {
        $info = new PBMailSignature();
        $info->setName('andy test ' . date('Y-m-d H:i:s'));
        $info->setContent('testing signature from andy @' . date("Y-m-d H:i:s"));

        $req = new PBEditMailSignatureReq();
        $req->setSignature($info);

        $rsp = new PBEditMailSignatureRsp();

        $url = self::TEST_DEV['test'] . "/api/stormsFury/mailSettingWrite/signature";
        $this->process($url, $req, $rsp);

        $signature = $rsp->getSignature();

        printf("json %s id %s\n", $signature->serializeToJsonString(), $signature->getSignId());

        $setting = new PBMailSignatureSetting();
        $setting->setUserMailId(765);
        $setting->setDefaultSignature($signature->getSignId());
        $setting->setReplyForwardSignature($signature->getSignId());

        $req = new PBEditMailSignatureSettingReq();
        $req->setSetting($setting);

        $url = self::TEST_DEV['test'] . "/api/stormsFury/mailSettingWrite/signatureSetting";
        $this->process($url, $req);
    }

    public function actionBindUserMail()
    {
        $email = '<EMAIL>';
        $password = base64_encode('ruby2016mcm');
        $bind = new PBUserMailBindInfo();
        $bind->setEmail($email);
        $bind->setPassword($password);

        $req = new PBBindUserMailReq();
        $req->setData($bind);

        $rsp = new PBBindUserMailRsp();

        $url = self::TEST_DEV['test'] . "/api/stormsFury/mailSettingWrite/bindUserMail";
        $this->process($url, $req, $rsp);

        $bind = $rsp->getData();

        printf("bind info: %s\n", $bind->serializeToJsonString());
    }

    public function actionUnbindUserMail()
    {
        $req = new PBUnbindUserMailReq();
        $req->setUserMailId(********);
        $url = self::TEST_DEV['wuyiwai'] . "/api/stormsFury/mailSettingWrite/unbindUserMail";
        $this->process($url, $req);
    }

    public function actionBatchEdit()
    {
        $this->setUserAccount('<EMAIL>');
        $req = new PBBatchEditMailReq();
        $req->setMailIds([**********]);
        $req->setCommand('read');
        $url = self::TEST_DEV['wuyiwai'] . "/api/stormsFury/mailWrite/batchEdit";
        $rsp = new \protobuf\MailSync\PBBatchEditMailRsp();
        $this->process($url, $req, $rsp, false);
        $this->logInfo($req);
        $this->logInfo($rsp);
    }

    public function actionMailDraft()
    {
        $rsp = new PBWriteMailDraftRsp();

        $url = self::TEST_DEV['test'] . "/api/stormsFury/mailWrite/draft";
        $this->process($url, null, $rsp);

        printf("mail id: %s\n", $rsp->getMailId());
    }

    public function actionDeleteTag()
    {
        $req = new PBDeleteMailTagReq();
        $req->setTagId(********);

        $rsp = new PBWriteMailForwardRsp();

        $url = self::TEST_DEV['test'] . '/api/stormsFury/mailSettingWrite/deleteTag';
        $this->process($url, $req);
    }

    public function actionForwardAsAttach()
    {
        $req = new PBWriteMailForwardAsAttachmentReq();
        $req->setMailId(32541337);

        $rsp = new PBWriteMailForwardAsAttachmentRsp();

        $url = self::TEST_DEV['test'] . '/api/stormsFury/mailWrite/forwardAsAttachment';
        $this->process($url, $req, $rsp);

        echo $rsp->serializeToJsonString();
    }

    public function actionUserToken()
    {
        $rsp = new PBUserTokenRsp();
        $url = self::TEST_DEV['test'] . '/api/stormsFury/userWrite/token';
        $this->process($url, null, $rsp);
        echo $rsp->serializeToJsonString();
    }

    public function actionCompanyCard()
    {
        $req = new \protobuf\Customer\PBCompanyCardReq();
        $req->setCompanyId(1103966438);
        $req->setDirectOwner(1);

        $rsp = new \protobuf\Customer\PBCompanyCardRsp();

        $url = self::TEST_DEV['yoyo'] . '/api/stormsFury/customerRead/companyCard';
        $this->process($url, $req, $rsp);

        echo $rsp->serializeToJsonString() . "\n";
    }

    public function actionCompanyTrailStatus()
    {
        $rsp = new PBCompanyTrailStatusRsp();
        $url = self::TEST_DEV['test'] . '/api/stormsFury/customerSettingRead/trailStatusList';
        $this->process($url, null, $rsp);

        echo $rsp->serializeToJsonString() . "\n";
    }

    public function actionPrivilege()
    {
        // pro: client_id=14059  <EMAIL>(usrid=********)
        // lite：client_id: 14049  <EMAIL>(usrid=********)
        $this->setUserAccount('<EMAIL>');
        $rsp = new PBDesktopInitRsp();
        $url = self::TEST_DEV['wuyiwai'] . '/api/stormsFury/userRead/privilege';
        $this->process($url, null, $rsp);
        $this->logInfo($rsp);
    }

    public function actionEmailBasicInfo()
    {
        $req = new PBEmailsMatchReq();
        $req->setEmails('<EMAIL>;<EMAIL>;<EMAIL>');
        $req->setCheckBlack(true);
        $rsp = new PBEmailsMatchRsp();
        $url = self::TEST_DEV['karonyang'] . '/api/stormsFury/mailRead/emailBasicInfo';
        $this->process($url, $req, $rsp);
        $this->logInfo($rsp);
    }

    //客户跟进
    public function actionRemark()
    {
        $req = new PBCustomerRemarkReq();

        $req->setCompanyId(**********);
        $req->setCustomerId(**********);
        $req->setContent('测试PB的快速跟进');
        $req->setRemarkType(101);
        $req->setRemarkTime(date("Y-m-d H:i:s"));
        $req->setFileIds([**********]);

        $url = self::TEST_DEV['wuyiwai'] . '/api/stormsFury/customerWrite/remark';
        $this->process($url, $req);
    }

    //客户添加标签
    public function actionAddTag()
    {
        $req = new PBCustomerAddTagReq();

        $companyIds = $req->getCompanyIds();
        $companyIds[] = 31873292;
        $req->setCompanyIds($companyIds);

        $tagIds = $req->getTagIds();
        $tagIds[] = 37521713;
        $req->setTagIds($tagIds);

        $url = 'http://wuyiwai.k.xiaoman.cn/api/stormsFury/customerWrite/addTag';
        $this->process($url, $req);

    }

    //客户移除标签
    //todo 需要更改tag类型，不支持数组
    public function actionRemoveTag()
    {
        $req = new PBCustomerRemoveTagReq();

        $companyIds = $req->getCompanyIds();
        $companyIds[] = 31873292;
        $req->setCompanyIds($companyIds);

        $req->setTagId(37521713);

        $url = 'http://wuyiwai.k.xiaoman.cn/api/stormsFury/customerWrite/removeTag';
        $this->process($url, $req);
    }

    //客户设置状态
    public function actionSetStatus()
    {
        $req = new PBCustomerSetStatusReq();
        $req->setCompanyId(31873292);
        $req->setStatus(35096399);

        $url = 'http://wuyiwai.k.xiaoman.cn/api/stormsFury/customerWrite/setStatus';
        $this->process($url, $req);
    }

    //客户设置星标
    public function actionSetStar()
    {
        $req = new PBCustomerSetStarReq();

        $req->setCompanyId(31873292);
        $req->setStar(5);

        $url = 'http://wuyiwai.k.xiaoman.cn/api/stormsFury/customerWrite/setStar';
        $this->process($url, $req);
    }

    //客户设置分组
    public function actionSetGroup()
    {
        $req = new PBCustomerSetGroupReq();

        $companyIds = $req->getCompanyIds();
        $companyIds[] = 31873292;
        $req->setCompanyIds($companyIds);

        $req->setGroupId(32343929);

        $url = 'http://wuyiwai.k.xiaoman.cn/api/stormsFury/customerWrite/setGroup';
        $this->process($url, $req);
    }

    //客户设置公海分组
    public function actionSetPool()
    {
        $req = new PBCustomerSetPoolReq()
        ;

        $companyIds = $req->getCompanyIds();
        $companyIds[] = 31873292;
        $req->setCompanyIds($companyIds);

        $req->setPoolId(0);

        $url = self::TEST_DEV['test'].'/api/stormsFury/customerWrite/setPool';
        $this->process($url, $req);
    }

    //客户基本资料
    public function actionCompanyBaseInfo()
    {
        $req = new PBCompanyBaseInfoReq();
        $req->setCompanyId(31873278);

        $url = 'http://wuyiwai.k.xiaoman.cn/api/stormsFury/customerRead/companyBaseInfo';
        $rsp = new PBCompanyBaseInfoRsp();
        $this->process($url, $req, $rsp);

        echo $rsp->serializeToJsonString() . "\n";
    }

    //客户动态参数
    public function actionTrailParam()
    {
        $req = new PBCompanyBaseInfoReq();
        $req->setCompanyId(31873278);

        $url = self::TEST_DEV['yoyo'] . '/api/stormsFury/customerRead/trailParam';
        $rsp = new PBTrailParamRsp();
        $this->process($url, $req, $rsp, false);

        echo $rsp->serializeToJsonString() . "\n";
    }

    // 用户列表接口
    public function actionUserInfoList()
    {
        $rsp = new \protobuf\CRMCommon\PBUserInfoListRsp();
        $url = self::TEST_DEV['yoyo'] . '/api/stormsFury/userRead/userList';
        $this->process($url, null, $rsp, false);

        echo $rsp->serializeToJsonString() . "\n";
    }

    //客户动态统计
    public function actionTrailStat()
    {
        $req = new PBTrailStatReq();
        $req->setCompanyId(**********);

        $url = self::TEST_DEV['yoyo'] . '/api/stormsFury/customerRead/trailStat';
        $rsp = new PBTrailStatRsp();
        $this->process($url, $req, $rsp, false);

        echo $rsp->serializeToJsonString() . "\n";
    }

    //客户动态列表
    public function actionTrailList()
    {
        $this->setUserAccount('<EMAIL>');
        $req = new PBTrailListReq();
        $req->setCompanyId(**********);
        $req->setOffset(0);
        $req->setLimit(10);

        $url = self::TEST_DEV['wuyiwai'] . '/api/stormsFury/customerRead/trailList';
        $rsp = new PBTrailListRsp();
        $this->process($url, $req, $rsp, false);
        $this->logInfo($req);
        $this->logInfo($rsp);
    }

    public function actionPoolList()
    {
//        $pool = new \common\library\customer\pool\Pool(1);
//        $pool->client_id = 1;
//        $pool->addVersion();
        $rsp = new PBCustomerPoolListRsp();
        $url = self::TEST_DEV['test'] . '/api/stormsFury/customerSettingRead/poolList';
        $this->process($url, null, $rsp, false);

        echo $rsp->serializeToJsonString() . "\n";
    }

    //设置，编辑标签
    public function actionSetTag()
    {
        $req = new PBCustomerSetTagReq();
        $req->setTagColor('#61BC81');
//        $req->setTagId(********);
        $req->setTagName('PB第二个标签');

        $url = 'http://wuyiwai.k.xiaoman.cn/api/stormsFury/customerSettingWrite/setTag';
        $rsp = new PBCustomerSetTagRsp();
        $this->process($url, $req, $rsp);
        echo $rsp->serializeToJsonString() . "\n";
    }

    //公司详细资料
    public function actionCompanyDetailInfo()
    {
        $this->setUserAccount("<EMAIL>");
        $req = new PBCompanyDetailInfoReq();
        $req->setCompanyId(**********);

        $rsp = new PBCompanyDetailInfoRsp();
        $url = self::TEST_DEV['wuyiwai'] . '/api/stormsFury/customerRead/companyDetailInfo';
        $this->process($url, $req, $rsp, false);
        $this->logInfo($req);
        $this->logInfo($rsp);
    }



    //客户列表头部
    public function actionHeaderFieldList()
    {
        $req = new PBCompanyListHeaderReq();
//        $req->setKey('company.public.list.field');
        $rsp = new PBCompanyListHeaderRep();
        $url = self::TEST_DEV['yoyo'] . '/api/stormsFury/customerSettingRead/headerFieldList';
        $this->process($url, $req, $rsp, false);
        echo $rsp->serializeToJsonString() . "\n";
    }

    public function actionEditCompanyByField()
    {
        $req = new PBEditCompanyByFieldReq();
        $req->setCompanyId('**********');
        $req->setFieldKey('address');
        $req->setFieldValue('sz');
        //$req->setFieldKey('38789122');
        //$req->setFieldValue('["banana", "peach", "apple"]');
        //$req->setFieldValue('["apple"]');

        //$url = self::TEST_DEV['yoyo'] . '/api/stormsFury/customerWrite/editCompanyByField';
        //$this->process($url, $req);

        $rsp = new PBCompanyListRsp();
        $url = self::TEST_DEV['yoyo'].'/api/stormsFury/customerWrite/editCompanyByField';
        $this->process($url, $req, $rsp, false);
        echo $rsp->serializeToJsonString() . "\n";
    }

    public function actionEditCompanyByFields()
    {
        $this->setUserAccount("<EMAIL>");
        $req = new PBEditCompanyByFieldsReq();
        $req->setCompanyId(**********);
        $req->setType(Constants::TYPE_COMPANY);
        $fields = $req->getFields();
        $fieldItem = new PBEditCompanyByFieldReq();
        $fieldItem->setCompanyId(**********);
        $fieldItem->setFieldKey('tel');
        $telInfo = [
            'tel_area_code' => '086',
            'tel' => '***********'
        ];
        $fieldItem->setFieldValue(json_encode($telInfo));

        $fieldItem = new PBEditCompanyByFieldReq();
        $fieldItem->setCompanyId(**********);
        $fieldItem->setFieldKey('image_list');
        $imageList = [**********, **********, **********, **********, **********];
        $fieldItem->setFieldValue(json_encode($imageList));
        $fields[] = $fieldItem;

        $req->setFields($fields);

        $rsp = new PBCompanyListRsp();
        $url = self::TEST_DEV['wuyiwai'] . '/api/stormsFury/customerWrite/editCompanyByFields';
        $this->process($url, $req, $rsp, false);
        echo $rsp->serializeToJsonString() . "\n";
    }

    public function actionTipsList()
    {
        $req = new \protobuf\Tips\PBTipsListReq();

        //构造请求参数
        //$req->setScene('company');
        $req->setScene('company');
        $req->setId(1100446349);
        $req->setPageNo(1);
        $req->setPageSize(2);
        $rsp = new \protobuf\Tips\PBTipsListRsp();
        $url = self::TEST_DEV['yoyo'].'/api/stormsFury/tipsRead/tipsList';
        $this->process($url, $req, $rsp, false);

        echo $rsp->serializeToJsonString() . "\n";
    }

    public function actionDisinterest()
    {
        $req = new \protobuf\Tips\PBTipsDisinterestReq();

        //构造请求参数
        $req->setFeedId('1100536015');
        $req->setCompanyHashId('00eff839e74001f0348ccb0924801a00');
        $rsp = new \protobuf\Tips\PBTipsListRsp();
        $url = self::TEST_DEV['yoyo'].'/api/stormsFury/tipsWrite/disinterest';
        $this->process($url, $req, $rsp, false);

        echo $rsp->serializeToJsonString() . "\n";
    }



    public function actionTipsCompanyInfo()
    {
        $req = new \protobuf\Tips\PBTipsCompanyInfoReq();
        //构造请求参数
        $req->setScene('company');
        $req->setId(1105182083);
        $rsp = new \protobuf\Tips\PBTipsCompanyInfoRsp();
        $url = self::TEST_DEV['yoyo'].'/api/stormsFury/tipsRead/companyInfo';
        $this->process($url, $req, $rsp, false);

        echo $rsp->serializeToJsonString() . "\n";
    }

    public function actionExchangeBizEntityId()
    {
        $req = new \protobuf\Tips\PBTipsExchangeBizEntityReq();
        //构造请求参数
        $req->setScene('mail');
        $req->setId(**********);
        $rsp = new \protobuf\Tips\PBTipsExchangeBizEntityRsp();
        $url = self::TEST_DEV['yoyo'].'/api/stormsFury/tipsRead/exchangeBizEntityId';
        $this->process($url, $req, $rsp, false);

        echo $rsp->serializeToJsonString() . "\n";
    }


    public function actionCompanyList()
    {
        $this->setUserAccount('<EMAIL>');
        $req = new PBCompanyListReq();

        //构造请求参数
        $req->setOffset(0);
        $req->setLimit(50);
        $req->setUserNum([0]);
        $req->setSortType('desc');
        $req->setSortField('order_time');
        $req->setShowFieldKey('company.private.list.field');
        $req->setOffset(0);
        $req->setLimit(20);
        $req->setSearchModel(0);
        $req->setTagMatchMode(1);
        $req->setShowAll(1);
        $rsp = new PBCompanyListRsp();
       //$url = self::TEST_DEV['test-desktop'].'/api/stormsFury/customerRead/companyList';
        $url = self::TEST_DEV['seven'].'/api/stormsFury/customerRead/companyList';
        $this->process($url, $req, $rsp, false);
        echo $rsp->serializeToJsonString() . "\n";
    }

    public function actionCompanySearchList()
    {
        $req = new PBCompanyListReq();

        //构造请求参数
        $req->setUserNum([1,2]);
        $req->setSortType('desc');
        $req->setSortField('order_time');
    //    $req->setShowFieldKey('company.private.list.field');proto
//        $req->setUserId([11858646]);
        $req->setOffset(0);
        $req->setLimit(20);
        $req->setKeyword('1cc');
        $req->setTagMatchMode(1);
        $req->setSearchModel(2);
        $req->setShowAll(1);

//        $searchExternalFieldInfo = new \protobuf\Customer\PBSearchExternalFieldInfo();
//        $searchExternalFieldInfo->setFieldId(********);
//        $searchExternalFieldInfo->setKeyword('["琵琶","古琴-xxx"]');
//       // $searchExternalFieldInfo->setMatchType('match');
//        $companyField = $req->getCompanyField();
//        $companyField[] = $searchExternalFieldInfo;
//        $req->setCompanyField($companyField);


        $rsp = new PBCompanyListRsp();
        $url = self::TEST_DEV['yoyo'].'/api/stormsFury/customerRead/companyList';
        $this->process($url, $req, $rsp, false);

        echo $rsp->serializeToJsonString() . "\n";
    }

    public function actionCompanyFilterList()
    {
        $req = new \protobuf\Customer\PBCompanyFilterListReq();

        //构造请求参数
        $req->setPublicFlag(0);

        $rsp = new \protobuf\Customer\PBCompanyFilterListRsp();
        $url = self::TEST_DEV['yoyo'].'/api/stormsFury/customerRead/companyFilterList';
        $this->process($url, $req, $rsp, false);

        echo $rsp->serializeToJsonString() . "\n";
    }

    public function actionFieldList()
    {
        $this->setUserAccount("<EMAIL>");
        $req = new \protobuf\CRMCommon\PBFieldListReq();
        //构造请求参数
        $req->setBase(-1);
        $req->setType(5);
//        $req->setShowHide(0);

        $rsp = new \protobuf\CRMCommon\PBFieldListRsp();
        $url = self::TEST_DEV['wuyiwai'].'/api/stormsFury/fieldSettingRead/fieldList';

        $this->process($url, $req, $rsp, false);
        $this->logInfo($req);
        $this->logInfo($rsp);
    }

    public function actionSetExternalValue()
    {
        $req = new \protobuf\CRMCommon\PBSetExternalValueReq();
        //构造请求参数
        $value = [********,**********,**********];
        $req->setKey('company_search_filed');
        $req->setValue($value);
        $url = self::TEST_DEV['yoyo'].'/api/stormsFury/userWrite/setExternalValue';
        $this->process($url, $req);
    }


    public function actionCustomerList()
    {
        $this->setUserAccount("<EMAIL>");

        $req = new PBCustomerSearchReq();
        $req->setOffset(0);
        $req->setLimit(10);
        $req->setKeyword('yoyo');
        //$req->setCompareDayOp(-1);

     //   $companyFieldList = $req->getCompanyField();
      //  $companyField = new \protobuf\Customer\PBSearchExternalFieldInfo();
//        $companyField->setFieldId('**********');
//        $companyField->setKeyword('["武汉音乐学院"]');
//        $companyField->setMatchType('term'); // field_type = 1/2(单行和多行文本) 需要传match_type
  //      $companyFieldList[] = $companyField;
//        $companyField = new \protobuf\Customer\PBSearchExternalFieldInfo();
//        $companyField->setFieldId('**********');
//        $companyField->setKeyword('["中央音乐学院"]');
//        $companyFieldList[] = $companyField;
 //       $req->setCompanyField($companyFieldList);

//        $customerFieldList = $req->getCustomerField();
//        $customerField = new \protobuf\Customer\PBSearchExternalFieldInfo();
//        $customerField->setFieldId('1116147620');
//        $customerField->setKeyword('["3"]');
//        $customerFieldList[] = $customerField;
//        $req->setCustomerField($customerFieldList);

        $rsp = new PBCompanyListRsp();
        $url = self::TEST_DEV['yoyo'].'/api/stormsFury/customerRead/customerList';
        $this->process($url, $req, $rsp, false);

        $this->logInfo($req);
        $this->logInfo($rsp);
    }

    public function actionClientSetting()
    {
        $version = new \common\library\version\ClientModuleVersion(1);
        $version->setModule(\common\library\version\Constant::CLIENT_MODULE_SETTING);
        $version->add();

        $url = self::TEST_DEV['test'] . '/api/stormsFury/clientRead/setting';
        $rsp = new PBClientSettingRsp();
        $this->process($url, null, $rsp, true );

        echo  $rsp->serializeToJsonString(). "\n";
    }

    //客户标签列表
    public function actionCustomerTagList()
    {
        $rsp = new PBCompanyTagRsp();
        $url = self::TEST_DEV['steven'] . '/api/stormsFury/customerSettingRead/tagList';
        $this->process($url, null, $rsp);
        $list = $rsp->getTagList();
        foreach ($list as $item) {
            printf("tag_name %s tag_id %s\n", $item->getName(), $item->getTagId());
        }
    }

    //客户设置标签
    public function actionSetTags()
    {
        $this->setUserAccount('<EMAIL>');
        $req = new \protobuf\Customer\PBCustomerSetTagsReq();

        $companyIds[] = **********;
        $req->setCompanyIds($companyIds);

        $tagIds[] = **********;
        $tagIds[] = **********;
        $req->setAddTagIds($tagIds);

        $deleteTagIds[] = **********;
        $req->setDeleteTagIds($deleteTagIds);

        $url = self::TEST_DEV['steven'] . '/api/stormsFury/customerWrite/SetTags';
        $this->process($url, $req);

    }

    //背调信息
    public function actionClientBackground()
    {
        $this->setUserAccount('<EMAIL>');
        $req = new \protobuf\Discovery\PBBackgroundReq();
        $req->setType(3);
        $req->setRelateId(18);

        $url = self::TEST_DEV['steven'] . "/api/stormsFury/DiscoveryRead/ClientBackground";
        $rsp = new \protobuf\Discovery\PBBackgroundRsp();
        $this->process($url, $req, $rsp, false);
        $this->logInfo($rsp);
    }


    public function actionPublicReasonList()
    {
        $this->setUserAccount('<EMAIL>');

        $url = self::TEST_DEV['steven'] . '/api/stormsFury/customerSettingRead/publicReasonList';
        $rsp = new \protobuf\Customer\PBCustomerPublicReasonListRsp();
        $this->process($url, null, $rsp,true);
        $this->logInfo($rsp);

    }


    public function actionResetTag()
    {
        $this->setUserAccount('<EMAIL>');
        $req = new PBCustomerResetTagReq();
        $req->setTagIds([**********,  **********]);
        $req->setCompanyId(**********);
        $req->setCompanyIds([**********, **********]);

        $url = self::TEST_DEV['steven'] . '/api/stormsFury/customerWrite/resetTag';
        $this->process($url, $req);
    }

    public function actionSyncTodo()
    {
        $url = self::TEST_DEV['test'] . '/api/stormsFury/mailRead/syncTodo';
        $rsp = new PBMailTodoSyncRsp();
        $this->process($url, null, $rsp);

        /**
         * @var $data PBMailTodoDetail[]
         */
        $data = $rsp->getTodoList();
        foreach ($data as $item) {
            var_dump(
                $item->getUserId(),
                $item->getMailId(),
                $item->getCompletedFlag(),
                $item->getRemindFlag(),
                $item->getProcessTime()
            );
        }
    }

    public function actionSetTodo(){
        $time = time();
        $req = new PBMailSetTodoReq();
        $req->setMailIds([37849884]);
        $req->setProcessTime($time);
        var_dump($time);

        $url = self::TEST_DEV['test'] . '/api/stormsFury/mailWrite/setTodo';
        $this->process($url, $req);

    }

    public function actionSetTodoCompleted(){
        $request = new PBMailTodoSetCompletedReq();
        $request->setMailIds([********]);

        $url = self::TEST_DEV['test'] . '/api/stormsFury/mailWrite/setCompleted';
        $this->process($url, $request);
    }

    public function actionEmailIdentity()
    {
        $this->setUserAccount('<EMAIL>');
        $request = new PBEmailIdentityReq();
        $request->setMailIds([**********]);
        $request->setDetailFalg(true);

        $url = self::TEST_DEV['tonyfeng']. '/api/stormsFury/mailRead/emailIdentity';
        $response = new PBEmailIdentityRsp();
        $this->process($url, $request, $response, true);
        $this->logInfo($request);
        $this->logInfo($response);
    }

    public function actionMailCard()
    {
        $this->setUserAccount('<EMAIL>');
        $req = new PBMailCardReq();
        $req->setEmail("<EMAIL>");

        $url = self::TEST_DEV['wuyiwai'] . '/api/stormsFury/mailRead/mailCard';
        $rsp = new PBMailCardRsp();
        $this->process($url, $req, $rsp, false);
        $this->logInfo($req);
        $this->logInfo($rsp);
    }

    //邮件模板
    public function actionTemplateList()
    {
        $this->setUserAccount('<EMAIL>');
        $req = new PBWorkJournalTemplateListReq();
        $req->setFilterCommitUserSetting(true);
//        $req->setType(1);
//        $req->setGroupId(0);
//        $req->setNoContent(true);

        $rsp = new PBWorkJournalTemplateListRsp();
        $url = self::TEST_DEV['xyy'].'/api/stormsFury/workJournalRead/templateList';
        $this->process($url, $req, $rsp, false, null, 'yingyingxian');
        $this->logInfo($rsp, false, true);
    }

    //移入私海
    public function actionHold()
    {
        $req = new PBCustomerHoldReq();
        $req->setGroupId(********);
        $req->setCompanyIds([********]);

        $url = self::TEST_DEV['wuyiwai']."/api/stormsFury/customerWrite/hold";
        $this->process($url, $req);
    }

    public function actionUserMailDetailList()
    {
        $this->setUserAccount('<EMAIL>');
        $rsp = new PBUserMailDetailListRsp();
        $url = self::TEST_DEV['wuyiwai'].'/api/stormsFury/mailSettingRead/userMailDetailList';
        $this->process($url, null, $rsp, false);
        $this->logInfo($rsp);
    }

    public function actionAddVersion()
    {
        $clientId = 3439;
        $userMailId = ********;
        $mailId = **********;
        $version = new \common\library\version\MailVersion($clientId, $userMailId);
        $version->setMailId($mailId);
        $version->setType(\common\library\version\Constant::MAIL_MODULE_BOUNCE);
        $version->add();
    }

    public function actionCompanySyncInfo()
    {
        $req = new PBCompanyCompareReq();
        $req->setVersion(0);
        $rsp = new PBCompanyCompareRsp();
        $url = self::TEST_DEV['wuyiwai'].'/api/stormsFury/companyRead/syncInfo';
        $this->process($url, $req, $rsp, false);
        echo $rsp->serializeToJsonString() . "\n";
    }

    public function actionCompanySync()
    {
        $req = new \protobuf\Customer\PBCompanySyncReq();
        $req->setSessionId('crm:sync:company:version1_********_company_compare_0_0');
        $req->setOffset(0);
        $req->setSize(10);
        $rsp = new \protobuf\Customer\PBCompanySyncRsp();
        $url = self::TEST_DEV['wuyiwai'].'/api/stormsFury/companyRead/sync';
        $this->process($url, $req, $rsp, false);
        echo $rsp->serializeToJsonString() . "\n";
    }


    //保存快速文本
    public function actionMailTextSave()
    {
        $req = new PBMailTextReq();
        $req->setName('测试快速文本名称');
        $req->setContent("这是快速文本内容");
        $rsp = new PBMailTextRsp();
        $url = self::TEST_DEV['wuyiwai'].'/api/stormsFury/mailSettingWrite/mailText';
        $this->process($url, $req, $rsp, false);
        echo $rsp->serializeToJsonString() . "\n";
    }

    //删除快速文本
    public function actionMailTextDelete()
    {
        $req = new PBMailTextDeleteReq();
        $req->setTextId(38844126);
        $url = self::TEST_DEV['wuyiwai'].'/api/stormsFury/mailSettingWrite/mailTextDelete';
        $this->process($url, $req);
    }

    //批量添加黑名单
    public function actionMailBlackSave()
    {
        $req = new PBMailBlackSaveReq();
        $req->setEmails(['<EMAIL>', '<EMAIL>']);
        $url = self::TEST_DEV['wuyiwai'] . '/api/stormsFury/mailSettingWrite/mailBlackSave';
        $this->process($url, $req);
    }

    public function actionApplicationOption()
    {
        $req = new PBApplicationOptionReq();
        $req->setKey('app.welcome');
        $url = self::TEST_DEV['wuyiwai'] . '/api/stormsFury/systemRead/applicationOption';
        $rsp = new PBApplicationOptionRsp();
        $this->process($url, $req ,$rsp, false);
        echo $rsp->serializeToJsonString()."\n";
    }

    //编辑收件规则
    public function actionMailRule()
    {
        $req = new PBEditMailRuleReq();

        $mailRule = new PBMailRule();
        $mailRule->setHistoryFolderId(-1);
        $mailRule->setName('PB测试收件规则123');
        $mailRule->setType(2);
        $mailRule->setFilterType(1);
        $mailRule->setContinueApply(false);
        $mailRule->setEnableFlag(1);
        $mailRule->setHistoryFlag(1);

        $filter = new PBMailRuleFilter();
        $filter->setType(0);
        $filter->setCompareType(1);
        $filter->setValue('<EMAIL>');
        $filters = $mailRule->getFilters();
        $filters[] = $filter;
        $mailRule->setFilters($filters);

        $operation = new PBMailRuleOperation();
        $operation->setType(7);
        $operation->setValue(2);
        $operation->setExtra('{"day":2,"time":"09:00:00"}');
        $operations = $mailRule->getOperations();
        $operations[] = $operation;
        $mailRule->setOperations($operations);
        $req->setRule($mailRule);
        $req->setMergeRuleIds([**********, **********]);

        $rsp = new PBEditMailRuleRsp();
        $url = self::TEST_DEV['june'] . '/api/stormsFury/mailSettingWrite/mailRule';
        $this->process($url, $req ,$rsp, false);
        $this->logInfo($rsp);
        echo $rsp->serializeToJsonString()."\n";
    }

    public function actionGeneralSetting()
    {
        $this->setUserAccount('<EMAIL>');

        $rsp = new \protobuf\MailSetting\PBMailGeneralSettingRsp();
        $url = self::TEST_DEV['seven'] . '/api/stormsFury/mailSettingRead/generalSetting';
        $this->process($url, null, $rsp, false);
        echo $rsp->serializeToJsonString()."\n";
    }

    //绑定邮箱
    public function actionBindInfo()
    {
        echo "开始执行\n";
        $startTime = microtime(true);
        $req = new PBUserMailBindInfoReq();
        $req->setUserMailId(********);
        $req->setCheckFlag(1);
        $rsp = new PBUserMailBindInfoRsp();
        $url = self::TEST_DEV['wuyiwai'] . '/api/stormsFury/mailSettingRead/bindInfo';
        $this->process($url, $req, $rsp, false);
        echo $rsp->serializeToJsonString()."\n";
        $endTime = microtime(true);
        echo "执行了".round($endTime-$startTime, 3)."秒";
    }

    //文档树
    public function actionTreeList()
    {
        $req = new PBDiskFileListReq();
        $req->setType(2);//1自己 2 共享
        $req->setFolderId(0);
        $rsp = new PBDiskFileListRsp();
        $url = self::TEST_DEV['wuyiwai'] . '/api/stormsFury/diskRead/treeList';
        $this->process($url, $req, $rsp, false);
        echo $rsp->serializeToJsonString()."\n";
    }

    //启用邮件规则
    public function actionEnableMailRule()
    {
        $req = new PBEnableMailRuleReq();
        $req->setRuleId(********);
        $req->setFlag(1);
        $url = self::TEST_DEV['wuyiwai'] . '/api/stormsFury/mailSettingWrite/enableMailRule';
        $this->process($url, $req);
    }

    //发邮件
    public function actionSendMail()
    {
        $this->setUserAccount('<EMAIL>');
        $req = new PBSendMailReq();
        $pbBaseInfo = new PBMailBaseInfo();
        $pbBaseInfo->setMailId(**********);
        $pbBaseInfo->setUserMailId(********);
        $pbBaseInfo->setUserId(765);
        $pbBaseInfo->setExposeFlag(0);
        $pbBaseInfo->setReceiver('onegong<<EMAIL>>');
        $pbBaseInfo->setSubject('onegong测试邮件111');
        $req->setContent("<div><div><p>onegong测试邮件111</p></div></div>");
        $req->setAttachmentIds(['**********','**********']);    //邮件附件
        $pbBaseInfo->setTrackFlag(1);
//        $pbMailExposeInfo = new \protobuf\MailSync\PBMailExposeInfo();
//        $pbMailExposeInfo->setUserMailIds(['********']);
//        $pbMailExposeInfo->setSignMode(2);
//        $pbMailExposeInfo->setMaxWaitTime(67);
//        $pbMailExposeInfo->setSubject(['onegong测试邮件']);
//        $pbMailExposeInfo->setSender('jolie');
//        $pbBaseInfo->setMailExposeInfo($pbMailExposeInfo);
        $req->setBaseInfo($pbBaseInfo);
        $url = self::TEST_DEV['yuanyi'] . '/api/stormsFury/mailWrite/send';
        $this->process($url, $req,$rsp,true);
        $this->logInfo($req);
        $this->logInfo($rsp);
    }

    public function actionCancelTiming()
    {
        $req = new \protobuf\MailSync\PBCancelTimingMailReq();
        $req->setMailId(**********);
        $url = self::TEST_DEV['wuyiwai'] . '/api/stormsFury/mailWrite/cancelTiming';
        $this->process($url, $req);
    }

    public function actionSaveMailDraft()
    {
        $this->setUserAccount('<EMAIL>');
        $req = new \protobuf\MailSync\PBSaveMailDraftReq();
        $pbBaseInfo = new PBMailBaseInfo();
        $pbBaseInfo->setMailId(**********);
        $pbBaseInfo->setUserMailId(********);
        $pbBaseInfo->setReceiver('<EMAIL>');
        $pbBaseInfo->setSubjectRemark('这是主题备注');
        $req->setBaseInfo($pbBaseInfo);
        $url = self::TEST_DEV['wuyiwai'] . '/api/stormsFury/mailWrite/save';
        $rsp = new PBWriteMailDraftRsp();
        $this->process($url, $req, $rsp, false);
        $this->logInfo($req);
    }

    //获取群发单显信息
    public function actionExposeStat()
    {
        $req = new PBMailExposeStatReq();
        $req->setUserMailIds([********]);
        $req->setPlanSendTime(**********);
        $rsp = new PBMailExposeStatRsp();
        $url = self::TEST_DEV['wuyiwai'] . '/api/stormsFury/mailSettingRead/mailExposeStat';
        $this->process($url, $req, $rsp, false);
        print_r(json_decode($rsp->serializeToJsonString()));
    }

    //获取下属邮件
    public function actionSubordinatesMailSearchList()
    {

        $req = new PBMailSearchListReq();
        //$emailIds = ["<EMAIL>","<EMAIL>", "<EMAIL>","<EMAIL>","<EMAIL>"];
        //$req->setCustomerEmails($emailIds);
        $req->setIncludeFolderIds([5]);
        $req->setUserId(11858593);
        $req->setUserMailId(30000189);
        $req->setLimit(20);
        $req->setMailDetailFlag(true);
        /*
        $req->setUserId(-1);
        $req->setIncludeFolderIds([5]);
        $req->setMailDetailFlag(true);
        $req->setMailType(0);
        $req->setOffset(0);
        $req->setLimit(20);
        */

        /*

        {"includeFolderIds":[5],"limit":100,"mailDetailFlag":true,"mailType":0,"offset":0,"userId":-1}}

         * */
        $rsp = new PBMailSearchListRsp();

        $url = self::TEST_DEV['yoyo'] . "/api/stormsFury/mailRead/searchList";
        $this->process($url, $req, $rsp,true);
        echo $rsp->serializeToJsonString()."\n";
        die();
        $mailIds = $rsp->getMailIds();
        echo $rsp->serializeToJsonString()."\n";
        printf("count %s\n", count(iterator_to_array($mailIds)));

        //$data = $rsp->getMailInfo();
      //  echo $rsp->serializeToJsonString()."\n";


    }

    //分发记录
    public function actionDistributeDetail()
    {
        $req = new PBMailDistributeReq();
        $req->setMailId(40451075);
        $url = self::TEST_DEV['wuyiwai'] . '/api/stormsFury/mailRead/mailDistributeDetail';
        $rsp = new PBMailDistributeRsp();
        $this->process($url, $req, $rsp, false);
        echo $rsp->serializeToJsonString()."\n";
    }

    //邮箱/域名黑名单列表
    public function actionMailBlackList()
    {
        $req = new PBMailBlackListReq();
        $req->setType(1);
        $url = self::TEST_DEV['wuyiwai'] . '/api/stormsFury/mailSettingRead/mailBlackList';
        $rsp = new PBMailBlackListRsp();
        $this->process($url, $req, $rsp, false);
        echo $rsp->serializeToJsonString()."\n";
    }

    public function actionPermissionUserList()
    {
        $req = new PBPermissionUserListReq();
        //$map = ['crm.company.private.view','crm.mail.view'];
        $map = ['crm.mail.view'];
        $req->setPermission($map);
        $url = self::TEST_DEV['yoyo'] . '/api/stormsFury/departmentRead/permissionUserList';
        $rsp = new PBPermissionUserListRsp();
        $this->process($url, $req, $rsp, false);
        echo $rsp->serializeToJsonString()."\n";
    }


    public function actionPermissionTree()
    {
        $req = new PBPermissionTreeReq();

        $map = ['crm.company.private.view'];
        $req->setPermission($map);
        $req->setShowMember(1);
        $url = self::TEST_DEV['yoyo'] . '/api/stormsFury/departmentRead/permissionTree';

        $rsp = new PBPermissionTreeRsp();

        $this->process($url, $req, $rsp, false);
        echo $rsp->serializeToJsonString()."\n";
    }

    public function actionListUserSetting()
    {
        $req = new PBUserSetting();
        $key = 'desktop.mail.list.field';
        $req->setKey($key);

        $url = self::TEST_DEV['wuyiwai'] . '/api/stormsFury/userRead/setting';
        $rsp = new PBUserSettingRsp();
        $this->process($url, $req, $rsp, false);
        $this->logInfo($req);
        $this->logInfo($rsp);
    }

    public function actionSaveUserSetting()
    {
        $this->setUserAccount('<EMAIL>');
        $req = new PBSaveUserSettingReq ();
        $key = 'desktop.mail.list.field';

        $value = [
            'read_status' => 1,
            'avatar' => 1,
            'nickname' => 1,
            'email_address' => 1,
            'company_name' => 1,
            'mail_time' => 1,
            'mail_title' => 1,
            'summary' => 1,
            'other_icon' => 1,
            'mail_tag' => 1,
        ];
        $value = json_encode($value);
        $pbUserSetting = new PBUserSetting();
        $pbUserSetting->setKey($key);
        $pbUserSetting->setValue($value);
        $req->setSetting($pbUserSetting);

        $url = self::TEST_DEV['wuyiwai'] . '/api/stormsFury/userWrite/saveUserSetting';
        $this->process($url, $req);
        $this->logInfo($req);
    }


    public function actionDeleteUserFilter()
    {
        $req = new PBDeleteUserSettingReq ();
        $key = 'customer.private.filter';
        $value = 'aaa6';
        $req->setKey($key);
        $req->setValue($value);

        $url = self::TEST_DEV['yoyo'] . '/api/stormsFury/userWrite/DeleteUserFilter';
        $this->process($url, $req);
    }

    //标记已读
    public function actionNoticeRead()
    {
        $req = new PBNoticeReadReq();
        $url = self::TEST_DEV['wuyiwai'] . '/api/stormsFury/noticeWrite/read';
        $this->process($url, $req);
    }

    public function actionNoticeList()
    {
        $rsp = new PBNoticeListRsp();
        $url = self::TEST_DEV['wuyiwai'] . '/api/stormsFury/noticeRead/list';
        $this->process($url, null, $rsp);
        echo $rsp->serializeToJsonString();
    }

    public function actionCompanyListByFilter()
    {
        $this->setUserAccount('<EMAIL>');
        $req = new PBCompanyListByFilterReq();
        $req->setFilterId('1星--2020年添加PPE客户');
        $req->setFilterKey('customer.private.filter');
        $req->setLimit(50);
        $req->setOffset(0);
        $url = self::TEST_DEV['omgk'] . '/api/stormsFury/customerRead/companyListByFilter';
        $rsp = new PBCompanyListRsp();
        $this->process($url, $req, $rsp, false);
        $this->logInfo($req);
        $this->logInfo($rsp);
    }

    public function actionDeleteList()
    {
        $req = new \protobuf\CRMCommon\PBDiskDeleteListReq();
        $req->setFolderIds([********]);
        $url = self::TEST_DEV['wuyiwai'] . '/api/stormsFury/diskWrite/deleteList';
        $this->process($url, $req);
    }

    //标签排序:type-[1:mail 3:company 4:lead]
    public function actionArrangeTagOrder()
    {
        $req = new \protobuf\CRMCommon\PBArrangeTagOrderReq();
        $req->setType(1);
        $req->setTagIds([11,12,13,14,15,1,2,********,32318919]);
        $url = self::TEST_DEV['wuyiwai'] . '/api/stormsFury/generalTagWrite/arrangeTagOrder';
        $this->process($url, $req);
    }

    //设置邮箱常规项
    public function actionMailSettingSave()
    {
        $req = new \protobuf\MailSetting\PBMailSettingSaveReq();
        $setting = new \protobuf\MailSetting\PBMailGeneralSetting();
        $setting->setDefaultUserMailId(30000132);
        $setting->setFont('楷体');
        $setting->setFontColor('#E43E3E');
        $setting->setFontSize('10px');
        $setting->setPopupReminder(true);
        $req->setSetting($setting);
        $url = self::TEST_DEV['wuyiwai'] . '/api/stormsFury/mailSettingWrite/mailSettingSave';
        $this->process($url, $req);
    }

    public function actionRecentSelectList()
    {
        $req = new \protobuf\CRMCommon\PBRecentSelectListReq();
        $req->setType([13,14]);
        $req->setShowType(1);
        $req->setLimit(20);
        $url = self::TEST_DEV['wuyiwai'] . '/api/stormsFury/userRead/recentSelectList';
        $rsp = new \protobuf\CRMCommon\PBRecentSelectListRsp();
        $this->process($url, $req, $rsp);
        echo $rsp->serializeToJsonString();
    }

    public function actionArrangeMailFolderOrder()
    {
        $req = new \protobuf\MailSetting\PBArrangeMailFolderOrderReq();
        $req->setFolderIds([
            32349447,
            38732694,
            40451910,
            1100048584,
            1100048726,
            1100048729,
            1100048731,
            40451761,
            1100048732,
            1100048794,
            1100288186,
            1100288187,
            1100288643
        ]);
        $url = self::TEST_DEV['wuyiwai'] . '/api/stormsFury/mailSettingWrite/arrangeMailFolderOrder';
        $this->process($url, $req);
    }

    //保存文件夹属性
    public function actionSaveMailFolderAttr()
    {
        $req = new PBEditMailFolderReq();
        $req->setFolderId(1100528012);
        $req->setParentId(1100528006);
        $url = self::TEST_DEV['wuyiwai'] . '/api/stormsFury/mailSettingWrite/folder';
        $rsp = new PBEditMailFolderRsp();
        $this->process($url, $req, $rsp);
        echo $rsp->serializeToJsonString()."\n";
    }

    //获取翻译选项
    public function actionMailLanguage()
    {
        $req = new \protobuf\MailSync\PBMailLanguageReq();
        $req->setMailId(**********);
        $url = self::TEST_DEV['wuyiwai'] . '/api/stormsFury/mailRead/mailLanguage';
        $rsp = new \protobuf\MailSync\PBMailLanguageRsp();
        $this->process($url, $req, $rsp);
        echo $rsp->serializeToJsonString() . "\n";
    }

    //邮件翻译
    public function actionMailTranslate()
    {
        $this->setUserAccount('<EMAIL>');
        $req = new \protobuf\MailSync\PBMailTranslateReq();
        $req->setMailId(**********);
        $req->setSource('fr');
        $req->setTarget('en');
        $url = self::TEST_DEV['wuyiwai'] . '/api/stormsFury/mailRead/mailTranslate';
        $rsp = new \protobuf\MailSync\PBMailTranslateRsp();
        $this->process($url, $req, $rsp, false);
        echo $rsp->serializeToJsonString() . "\n";
    }

    public function actionMailTranslateOption()
    {
        $rsp = new \protobuf\MailSetting\PBMailTranslateOptionsRsp();
        $url = self::TEST_DEV['wuyiwai'] . '/api/stormsFury/mailSettingRead/mailTranslateOption';
        $this->process($url, null, $rsp, false);
        echo $rsp->serializeToJsonString() . "\n";
    }

    public function actionMailTemplateView()
    {
        $req = new \protobuf\MailSetting\PBMailTemplateViewReq();
        $req->setTemplateId(90);
        $req->setSystemFlag(true);
        $url = self::TEST_DEV['wuyiwai'] . '/api/stormsFury/mailSettingRead/mailTemplateView';
        $rsp = new \protobuf\MailSetting\PBMailTemplateViewRsp();
        $this->process($url, $req, $rsp, false);
        echo $rsp->serializeToJsonString() . "\n";
    }

    public function actionMailList()
    {
        $this->setUserAccount('<EMAIL>');
        $req = new \protobuf\MailSync\PBMailListReq();
        $req->setType(1);
        $req->setFolderId(1);
//        $req->setCompanyFlag(1);
        $req->setOffset(0);
        $req->setLimit(2);
        $rsp = new \protobuf\MailSync\PBMailListRsp();
        $url = self::TEST_DEV['seven'] . '/api/stormsFury/mailRead/mailList';
        $this->process($url, $req, $rsp, false);
        echo $rsp->serializeToJsonString() . "\n";
    }

    public function actionExportMail()
    {
        $req = new \protobuf\MailSync\PBExportMailReq();
        $req->setMailIds([**********, **********]);
        $req->setSaveFile(true);
        $rsp = new \protobuf\MailSync\PBExportMailRsp();
        $url = self::TEST_DEV['wuyiwai'] . '/api/stormsFury/mailWrite/exportMail';
        $this->process($url, $req, $rsp, false);
        echo $rsp->serializeToJsonString() . "\n";
    }

    public function actionGetContentsAndPlainText()
    {
        //使用这个账号进行测试
//        private $userAccount = '<EMAIL>';
        $request = new \protobuf\MailSync\PBMailContentReq();
        $request->setMailIdList([**********, **********, **********, **********, **********, **********, **********, **********, **********, **********, **********, **********, **********, **********, **********, **********, **********, **********, **********, **********, **********, **********, **********, **********, **********, **********, **********, **********, **********, **********, **********, **********, **********, **********, **********, **********, **********, **********, **********, **********, **********, **********, **********, **********, **********, **********, **********, **********, **********, **********]);
        $rsp = new \protobuf\MailSync\PBMailContentRsp();
        $url = self::TEST_DEV['seven'] . '/api/stormsFury/mailRead/content';
        $this->process($url, $request, $rsp, true);
        echo $rsp->serializeToJsonString() . "\n";
    }

    public function actionEmailSuggestion()
    {
        $req = new \protobuf\Email\PBEmailMatchReq();
        $req->setKeyword('test');
        $rsp = new \protobuf\Email\PBEmailMatchRsp();
        $url = self::TEST_DEV['yoyo'] . '/api/stormsFury/mailRead/emailSuggestion';
        $this->process($url, $req, $rsp, false);
        echo $rsp->serializeToJsonString() . "\n";
    }

    //客户邮件
    //获取客户邮件列表筛选分组项
    public function actionCustomerMailFilterList()
    {
        $rsp = new \protobuf\MailSync\PBCustomerMailFilterListRsp();
        $url = self::TEST_DEV['wuyiwai'] . '/api/stormsFury/customerMailRead/customerMailFilterList';
        $this->process($url, null, $rsp, false);
        $this->logInfo($rsp);
    }

    //获取客户分组列表筛选项
    public function actionMailCompanyGroupList()
    {
        $this->setUserAccount('<EMAIL>');
        $req = new \protobuf\MailSync\PBMailCompanyGroupListReq();
        $req->setType('pool_id');
        $rsp = new \protobuf\MailSync\PBMailCompanyGroupListRsp();
        $url = self::TEST_DEV['wuyiwai'] . '/api/stormsFury/customerMailRead/mailCompanyGroupList';
        $this->process($url, $req, $rsp, false);
        $this->logInfo($req);
        $this->logInfo($rsp);
    }

    //获取某个分组下的客户列表
    public function actionMailCompanyList()
    {
        $this->setUserAccount('<EMAIL>');
        $req = new \protobuf\MailSync\PBMailCompanyListReq();
        $req->setType('star');
        $req->setParentId(0);
        $rsp = new \protobuf\MailSync\PBMailCompanyListRsp();
        $url = self::TEST_DEV['wuyiwai'] . '/api/stormsFury/customerMailRead/mailCompanyList';
        $this->process($url, $req, $rsp, false);
        $this->logInfo($req);
        $this->logInfo($rsp);
    }

    //获取关心客户列表: 关注客户 和 七天内有收件往来客户
    public function actionConcernCompanyList()
    {
        $rsp = new \protobuf\MailSync\PBMailConcernCompanyListRsp();
        $url = self::TEST_DEV['wuyiwai'] . '/api/stormsFury/customerMailRead/concernCompanyList';
        $this->process($url, null, $rsp, false);
        $this->logInfo($rsp);
    }

    //按分组类型聚合分组未读数
    public function actionAggregationUnread()
    {
        $req = new \protobuf\MailSync\PBAggregationUnreadReq();
        $req->setType('group_id');
        $rsp = new \protobuf\MailSync\PBAggregationUnreadRsp();
        $url = self::TEST_DEV['wuyiwai'] . '/api/stormsFury/customerMailRead/aggregationUnread';
        $this->process($url, $req, $rsp, false);
        $this->logInfo($req);
        $this->logInfo($rsp);
    }

    //查询具体客户的未读数
    public function actionCustomerUnread()
    {
        $req = new \protobuf\MailSync\PBCustomerUnreadReq();
        $req->setCompanyIds([********, ********]);
        $rsp = new \protobuf\MailSync\PBCustomerUnreadRsp();
        $url = self::TEST_DEV['wuyiwai'] . '/api/stormsFury/customerMailRead/customerUnread';
        $this->process($url, $req, $rsp, false);
        $this->logInfo($req);
        $this->logInfo($rsp);
    }

    public function actionExposeMailProgressList()
    {
        $this->setUserAccount('<EMAIL>');
        $req = new \protobuf\MailSync\PBExposeMailProgressListReq();
        $rsp = new \protobuf\MailSync\PBExposeMailProgressListRsp();
        $url = self::TEST_DEV['omgk'] . '/api/stormsFury/mailRead/exposeMailProgressList';
        $this->process($url, $req, $rsp, true);
        $this->logInfo($req);
        $this->logInfo($rsp);
    }

    //停止发送群发单显
    public function actionStopExposeMailTask()
    {
        $this->setUserAccount('<EMAIL>');
        $req = new \protobuf\MailSync\PBStopExposeMailTaskReq();
        $req->setMailIds([**********]);
        $url = self::TEST_DEV['wuyiwai'] . '/api/stormsFury/mailWrite/stopExposeMailTask';
        $this->process($url, $req);
        $this->logInfo($req);
    }

    //获取邮箱的群发单显数量信息
    public function actionExposeCountInfo()
    {
        $req = new \protobuf\MailSync\PBExposeCountInfoReq();
        $req->setUserMailIds([********, ********]);
        $rsp = new \protobuf\MailSync\PBExposeCountInfoRsp();
        $url = self::TEST_DEV['wuyiwai'] . '/api/stormsFury/mailRead/exposeCountInfo';
        $this->process($url, $req, $rsp, false);
        $this->logInfo($req);
        $this->logInfo($rsp);
    }

    //获取联系人列表
    public function actionContactList()
    {
        $req = new \protobuf\Customer\PBContactListReq();
        $req->setCompanyIds([********, ********]);
        $rsp = new \protobuf\Customer\PBContactListRsp();
        $url = self::TEST_DEV['wuyiwai'] . '/api/stormsFury/customerRead/contactList';
        $this->process($url, $req, $rsp, false);
        $this->logInfo($req);
        $this->logInfo($rsp);
    }

    //客户状态列表
    public function actionStatusList()
    {
        $this->setUserAccount('<EMAIL>');
        $rsp = new \protobuf\Customer\PBCustomerStatusListRsp();
        $url = self::TEST_DEV['wuyiwai'] . '/api/stormsFury/customerRead/statusList';
        $this->process($url, null, $rsp, false);
        $this->logInfo($rsp);
    }

    public function actionArchiveMail()
    {
        $this->setUserAccount('<EMAIL>');
        $req = new \protobuf\Customer\PBArchiveMailReq();
        $req->setCompanyId(********);
        $req->setMailId(**********);
        $url = self::TEST_DEV['wuyiwai'] . '/api/stormsFury/customerWrite/archiveMail';
        $this->process($url, $req);
        $this->logInfo($req);
    }

    //邮件追踪列表
    public function actionTrackList()
    {
        $this->setUserAccount('<EMAIL>');
        $req = new \protobuf\MailSync\PBMailTrackListReq();
        $req->setType(0);
        $req->setReplyFlag(2);
        $url = self::TEST_DEV['wuyiwai'] . '/api/stormsFury/traceRead/mailTrackList';
        $rsp = new \protobuf\MailSync\PBMailTrackListRsp();
        $this->process($url, $req, $rsp, false);
        $this->logInfo($req);
        $this->logInfo($rsp);
    }

    //获取邮箱身份以及所属客户信息
    public function actionEmailIdentityAndMailCard()
    {
        $this->setUserAccount('<EMAIL>');
        $req = new \protobuf\MailSync\PBEmailIdentityReq();
        $req->setEmails([
            '<EMAIL>',
            '<EMAIL>',
            ]);
        $req->setDetailFalg(true);
        $rsp = new \protobuf\MailSync\PBEmailIdentityRsp();
        $url = self::TEST_DEV['wuyiwai'] . '/api/stormsFury/mailRead/emailIdentity';
        $this->process($url, $req, $rsp, false);
        $this->logInfo($req);
        $this->logInfo($rsp);
    }

    public function actionSaveSchedule()
    {
        $this->setUserAccount('<EMAIL>');
        $req = new \protobuf\Schedule\PBScheduleCreateReq();

        $remindType = new \protobuf\Schedule\PBRemindTimeType();
        $remindType->setType("-1day");
        $remindTypeArr[] = $remindType;
        //自定义
        $remindType->setType('ext');
        $remindType->setTime('2020-06-03 10:27:07');
        $remindTypeArr[] = $remindType;
//        $req->setTitle("command创建的日程-sevenshi");
        $req->setTitle("图片command创建的重复日程-sevenshi");
        $req->setColor(1);
        $req->setStartTime('2020-06-01');
        $req->setEndTime('2020-06-05');
//        $req->setRepeatType(0);
        $req->setRepeatType(1);
        $req->setRepeatEnd('2020-06-03 10:26:47');
        $req->setFullDayFlag(true);
//        $req->setRemark("command测试的备注-sevenshi");
        $req->setRemark("图片command测试的重复日程备注-sevenshi");
        $req->setFullDayFlag(1);
        $req->setRemindTime($remindTypeArr);
        $req->setParticipantUserId([
            46900
        ]);
        $req->setReferType(1);
        $req->setReferId(********);
        $req->setRepeatChangeType(0);
        $req->setAttachList([
            **********
        ]);
        $req->setImageList([
            **********
        ]);

        $url = self::TEST_DEV['seven'] . '/api/stormsFury/userScheduleWrite/saveSchedule';
        $this->process($url, $req);

    }

    // 根据referId获取审批单信息
    public function actionApplyFormInfoByReferId()
    {
        $this->setUserAccount('<EMAIL>');
        $req = new \protobuf\Approval\PBApplyFormInfoByReferIdReq();
        $req->setReferId(**********);
        $rsp = new \protobuf\Approval\PBApplyFormInfoByReferIdRsp();
        $url = self::TEST_DEV['wuyiwai'] . '/api/stormsFury/approvalReadV2/applyFormInfoByReferId';
        $this->process($url, $req, $rsp, false);
        $this->logInfo($req);
        $this->logInfo($rsp);
    }

    // 设置自定义审批人
    public function actionCustomApprovers()
    {
        $this->setUserAccount('<EMAIL>');
        $req = new \protobuf\Approval\PBCustomApproversReq();
        $req->setApplyFormId(**********);
        $req->setApprovers([765]);
        $req->setAttachIds([**********]);
        $url = self::TEST_DEV['wuyiwai'] . '/api/stormsFury/approvalWriteV2/customApprovers';
        $this->process($url, $req);
        $this->logInfo($req);
    }

    // 自定义审批配置列表
    public function actionCustomConfigList()
    {
        $this->setUserAccount('<EMAIL>');
        $rsp = new \protobuf\Approval\PBCustomConfigListRsp();
        $url = self::TEST_DEV['wuyiwai'] . '/api/stormsFury/approvalReadV2/customConfigList';
        $this->process($url, null, $rsp);
        $this->logInfo($rsp);
    }

    // 提交审批
    public function actionCustomApplyForm()
    {
        $this->setUserAccount('<EMAIL>');
        $req = new \protobuf\Approval\PBCustomApplyFormReq();
        $req->setApprovalFlowId(**********);
        $req->setAttachIds([**********]);
        $req->setApprovers([3,10]);
        $req->setReferId(**********);
        $req->setReferType(\protobuf\Approval\PBApprovalReferType::ENTITY_TYPE_MAIL);
        $req->setSubject('测试pb提交审批2');
        $req->setContent('111');
        $url = self::TEST_DEV['wuyiwai'] . '/api/stormsFury/approvalWriteV2/customApplyForm';
        $this->process($url, $req);
        $this->logInfo($req);
    }

    // 根据email删除邮箱联想的结果
    public function actionDeleteMatchEmailSuggestion()
    {
        $this->setUserAccount('<EMAIL>');
        $req = new \protobuf\MailSync\PBDeleteMatchEmailSuggestionReq();
        $req->setEmail('<EMAIL>');
        $url = self::TEST_DEV['wuyiwai'] . '/api/stormsFury/mailWrite/deleteMatchEmailSuggestion';
        $this->process($url, $req);
        $this->logInfo($req);
    }

    public function actionEditMailSubjectRemark()
    {
        $this->setUserAccount('<EMAIL>');
        $req = new \protobuf\MailSync\PBEditMailSubjectRemarkReq();
        $req->setMailId(**********);
        $req->setSubjectRemark('邮件标题备注1');
        $url = self::TEST_DEV['wuyiwai'] . '/api/stormsFury/mailWrite/editMailSubjectRemark';
        $this->process($url, $req);
        $this->logInfo($req);
    }


    public function actionTestDeleteContact()
    {
        $this->setUserAccount('<EMAIL>');
        $req = new \protobuf\Customer\PBDeleteContactReq();
        $req->setCustomerIds([
            **********
        ]);
        $url = self::TEST_DEV['seven'] . '/api/stormsFury/customerWrite/deleteContact';
        $rsp = new PBCompanyListRsp();
        $this->process($url, $req,$rsp,true);
        $rsp->serializeToJsonString();
    }

    public function actionSaveCustomer()
    {
        $this->setUserAccount("<EMAIL>");
        $req = new \protobuf\Customer\PBSaveCustomerReq();
        $req->setCompanyId(**********);
        $req->setCustomerId(**********);

        $fields = $req->getFields();

        $fieldItem = new \protobuf\Customer\PBCustomerField();
        $fieldItem->setFieldKey('image_list');
        $fieldItem->setFieldValue(json_encode([**********]));

        $fields[] = $fieldItem;

        $fieldItem = new \protobuf\Customer\PBCustomerField();
        $fieldItem->setFieldKey('email');
        $fieldItem->setFieldValue('<EMAIL>');

        $fields[] = $fieldItem;

        $req->setFields($fields);

        $rsp = new PBCompanyListRsp();
        $url = self::TEST_DEV['wuyiwai'].'/api/stormsFury/customerWrite/saveCustomer';
        $this->process($url, $req, $rsp, false);
        echo $rsp->serializeToJsonString() . "\n";
    }

    public function actionProductList()
    {
        $this->setUserAccount('<EMAIL>');
        $req = new \protobuf\Product\PBProductListReq();
        $req->setCreateUserId([]);
        $req->setSelectModel(true);
        $req->setGroupId(-1);
        $req->setPage(1);
        $req->setPageSize(20);
        $req->setDisableFlag(-1);
        $req->setProductModel("");
        $req->setProductName("");
        $req->setSourceType(0);
        $req->setUpdateTimeEnd("");
        $req->setUpdateTimeStart("");

        $rsp = new \protobuf\Product\PBProductListRsp();
        $url = self::TEST_DEV['seven'] . '/api/stormsFury/productRead/list';
        $this->process($url, $req, $rsp,false);
        $this->logInfo($rsp);
    }

    public function actionSpecifyLangProductList ()
    {
        $this->setUserAccount('<EMAIL>');
        $req = new \protobuf\Product\PBSpecifyLangProductListReq();
        $req->setProductIds([**********,**********]);
        $req->setSpecifyLang('en');

        $rsp = new \protobuf\Product\PBSpecifyLangProductListRsp();
        $url = self::TEST_DEV['seven'] . '/api/stormsFury/productRead/SpecifyLangProductList';
        $this->process($url, $req, $rsp,false);
        $this->logInfo($rsp);
    }

    public function actionProductGroupList()
    {
        $rsp = new \protobuf\Product\PBProductGroupRsp();
        $url = self::TEST_DEV['seven'] . '/api/stormsFury/productRead/groupList';
        $this->process($url, null, $rsp, false);
        $this->logInfo($rsp);
    }

    public function actionGetMainSystemId()
    {
        $this->setUserAccount('<EMAIL>');
        $rsp = new \protobuf\CRMCommon\PBGetMainSystemIdRsp();
        $url = self::TEST_DEV['wuyiwai'] . '/api/stormsFury/userRead/getMainSystemId';
        $this->process($url, null, $rsp, false);
        $this->logInfo($rsp);
    }

    public function actionTranslateContent()
    {
        $this->setUserAccount('<EMAIL>');
        $req = new \protobuf\MailSync\PBTranslateContentReq();
        $req->setContent("space");
        $req->setSource("en");
        $req->setTarget("zh");
        $rsp = new \protobuf\MailSync\PBTranslateContentRsp();
        $url = self::TEST_DEV['seven'] . '/api/stormsFury/mailRead/translateContent';
        $this->process($url, $req, $rsp, true);
        $this->logInfo($req);
        $this->logInfo($rsp);
    }

    // ./yiic-test stormfuryexample leadSyncInfo
    public function actionLeadSyncInfo()
    {
        $this->setUserAccount('<EMAIL>');
        $req = new \protobuf\Lead\PBLeadCompareReq();
        $req->setVersion(0);
        $rsp = new \protobuf\Lead\PBLeadCompareRsp();
        $url = self::TEST_DEV['wuyiwai'].'/api/stormsFury/leadRead/syncInfo';
        $this->process($url, $req, $rsp, false);
        $this->logInfo($req);
        $this->logInfo($rsp);
    }

    // ./yiic-test stormfuryexample leadSync
    public function actionLeadSync()
    {
        $this->setUserAccount('<EMAIL>');
        $req = new \protobuf\Lead\PBLeadSyncReq();
        $req->setSessionId('crm:sync:lead:version1_46900_0_0');
        $req->setOffset(0);
        $req->setSize(50);
        $rsp = new \protobuf\Lead\PBLeadSyncRsp();
        $url = self::TEST_DEV['wuyiwai'].'/api/stormsFury/leadRead/sync';
        $this->process($url, $req, $rsp, false);
        $this->logInfo($req);
        $this->logInfo($rsp);
    }

    public function actionGetPermissionScopeUser()
    {
        $this->setUserAccount('<EMAIL>');
        $req = new \protobuf\CRMCommon\PBGetPermissionScopeUserReq();
        $req->setPermission(PrivilegeConstants::PRIVILEGE_CRM_LEAD_VIEW);
        $rsp = new \protobuf\CRMCommon\PBGetPermissionScopeUserRsp();
        $url = self::TEST_DEV['wuyiwai'].'/api/stormsFury/userRead/getPermissionScopeUser';
        $this->process($url, $req, $rsp, false);
        $this->logInfo($req);
        $this->logInfo($rsp);
    }

    public function actionUnreadStatistics()
    {
        $this->setUserAccount('<EMAIL>');
        $rsp = new \protobuf\MailSync\PBUnreadStatisticsRsp();
        $url = self::TEST_DEV['wuyiwai'] . '/api/stormsFury/mailRead/unreadStatistics';
        $this->process($url, null, $rsp, false);
        $this->logInfo($rsp);
    }

    public function actionSyncUserMail()
    {
        $this->setUserAccount('<EMAIL>');
        $req = new \protobuf\MailSync\PBUserMailCompareReq();
        $rsp = new \protobuf\MailSync\PBUserMailCompareRsp();
        $url = self::TEST_DEV['wuyiwai'] . '/api/stormsFury/mailRead/syncUserMailInfo';
        $this->process($url, $req, $rsp, false);
        $this->logInfo($req);
        $this->logInfo($rsp);
    }

    /*
        ./yiic-test stormfuryexample SyncInfo
    */
    public function actionSyncInfo()
    {
        $this->setUserAccount('<EMAIL>');
        $req = new PBMailCompareReq();
        $version = new PBMailVersion();
//        $version->setUserMailId(********);
//        $version->setVersion(4768);
        $versionList = $req->getVersionList();
        $versionList[] = $version;
        $req->setVersionList($versionList);

        $rsp = new PBMailCompareRsp();
        $url = self::TEST_DEV['nanrui'].'/api/stormsFury/mailRead/syncInfo';
        $this->process($url, $req, $rsp, false);
        $this->logInfo($req);
        $this->logInfo($rsp);
    }

    /*
        ./yiic-test stormfuryexample Sync
    */
    public function actionSync()
    {
        $this->setUserAccount('<EMAIL>');
        $req = new PBMailSyncReq();
        $req->setSessionId('crm:sync:mail:version1_51106_0_6319_0');

        $req->setOffset(0);
        $req->setSize(100);

        $rsp = new PBMailSyncRsp();
        $url = self::TEST_DEV['seven'].'/api/stormsFury/mailRead/sync';
        $this->process($url, $req, $rsp, true);
        $this->logInfo($req);
        $this->logInfo($rsp);
    }

    /*
       ./yiic-test stormfuryexample SyncMailRemark
    */
    public function actionSyncMailRemark()
    {
        $this->setUserAccount('<EMAIL>');
        $req = new PBMailSyncReq();
        // 这个来自于前面的 actionSyncInfo()
        $req->setSessionId('crm:sync:mail:version1_46900_0_0_0');

        $req->setOffset(500);
        $req->setSize(********);

        $rsp = new PBMailSyncRsp();
        $url = self::TEST_DEV['nanrui'].'/api/stormsFury/mailRead/sync';
        $this->process($url, $req, $rsp, false);

        $a = $rsp->getAddList();
        if (!empty($a)) {
            var_dump(count($a));
            // $mailInfo = $a[0];
            foreach ($a as $mailInfo) {
                // var_dump($mailInfo->getBaseInfo()->getMailId());
                $delete_flag = $mailInfo->getStatusInfo()->getDeleteFlag();
                $baseInfo = $mailInfo->getBaseInfo();
                if ($delete_flag == 0) {
                    // echo "ok\n";
                    $mailRemark = $baseInfo->getMailRemark();
                    if ($mailRemark != "") {
                        var_dump($baseInfo->getMailId());
                        var_dump($mailRemark);
                    }
                }
            }
        }
    }

    public function actionReply()
    {
        $this->setUserAccount('<EMAIL>');
        $req = new \protobuf\MailSync\PBWriteMailReplyReq();
        $req->setMailId(**********);
        $req->setReplyWithAttachment(false);

        $rsp = new \protobuf\MailSync\PBWriteMailReplyRsp();
        $url = self::TEST_DEV['karonyang'] . '/api/stormsFury/mailWrite/reply';
        $this->process($url, $req, $rsp);
        $this->logInfo($req);
        $this->logInfo($rsp);
    }

    public function actionMailDetectBindInfo() {
        //$this->setUserAccount('<EMAIL>');
        $req = new PBMailDetectBindReq();

        $req->setEmail("<EMAIL>");
        //$req->setEmail('<EMAIL>');

        $url = self::TEST_DEV['crm-desktop'] . "/api/stormsFury/mailSettingRead/mailDetectBindInfo";

        $url = self::TEST_DEV['omgm'] . "/api/stormsFury/mailSettingRead/mailDetectBindInfo";

        try {
            $rsp = new PBMailDetectBindRsp();
        }catch (\Throwable $e) {
            //var_dump($e->getMessage());
        }

       // var_dump($rsp);

        $this->process($url, $req,$rsp, false);

        $this->logInfo($rsp);
    }

    // 邮件行为鉴权
    public function actionCheckMailAccess()
    {
        $this->setUserAccount('<EMAIL>');
        $req = new \protobuf\MailSync\PBCheckMailAccessReq();
        $req->setMailId(**********);
        $url = self::TEST_DEV['wuyiwai'] . "/api/stormsFury/mailRead/checkMailAccess";
        $this->process($url, $req);
        $this->logInfo($req);
    }

    // 获取客户/线索/商机搜索自定义字段设置
    public function actionSearchSetting()
    {
        $this->setUserAccount('<EMAIL>');
        $req = new \protobuf\CRMCommon\PBSearchSettingReq();
        $req->setType(Constants::TYPE_LEAD);
        $url = self::TEST_DEV['wuyiwai'] . "/api/stormsFury/customerRead/searchSetting";
        $rsp = new \protobuf\CRMCommon\PBSearchSettingRsp();
        $this->process($url, $req, $rsp, false);
        $this->logInfo($req);
        $this->logInfo($rsp);
    }

    public function actionLeadCustomerList()
    {
        $this->setUserAccount('<EMAIL>');
        $req = new \protobuf\Lead\PBLeadCustomerSearchReq();
        $req->setAiTagMatchMode(1);
        $req->setLimit(20);
        $req->setPin(false);
        $req->setOffset(0);
//        $req->setStatusId(1);
//        $req->setAiStatusId(1);
//        $req->setCompareDay(20);
//        $req->setCompareDayOp(1);

        $url = self::TEST_DEV['omgk'].'/api/stormsFury/leadRead/customerList';
        $this->process($url, $req, $rsp, false);
        $this->logInfo($req);
        $this->logInfo($rsp);
    }

    public function actionTodoFeedCard()
    {
        $this->setUserAccount('<EMAIL>');
        $req = new PBUnReadCardListReq();
        $rsp = new \protobuf\TodoFeed\PBUnReadCardListRsp();
        $url = self::TEST_DEV['gooray'].'/stormsFury/todoFeedRead/getUnReadCardList';
        $this->process($url, $req, $rsp, false, false, 'gooray');
        $this->logInfo($rsp);
    }

    // ./yiic-test stormFuryExample todoFeedList
    public function actionTodoFeedList(){
        $this->setUserAccount('<EMAIL>');
        $req = new \protobuf\TodoFeed\PBUnReadFeedListReq();
        $req->setFeedType(1001);
        $req->setObjectType(10);
        $rsp = new \protobuf\TodoFeed\PBUnReadFeedListRsp();
        $url = self::TEST_DEV['xyy'].'/stormsFury/todoFeedRead/feedList';
        $this->process($url, $req, $rsp, false, null, 'yingyingxian');
        $this->logInfo($rsp, false, true);
    }

    public function actionUpdateFeed(){
        $this->setUserAccount('<EMAIL>');
        $req = new \protobuf\TodoFeed\PBUpdateFeedReq();
        $req->setFeedType(*************);
        $req->setObjectType(1);
        $req->setFeedIds([**********]);
        $req->setMailIds([**********]);
        $url = self::TEST_DEV['app-dev'].'/stormsFury/TodoFeedWrite/UpdateFeed';
        $this->process($url, $req);
    }

    public function actionMailReceive()
    {
        $this->setUserAccount('<EMAIL>');

        $req = new \protobuf\MailSetting\PBMailReceiveReq();
        $req->setUserMailIds([2676385, ********, ********, ********]);
        $url = self::TEST_DEV['omgk'] . "/api/stormsFury/mailSettingWrite/receive";
        $rsp = new \protobuf\MailSetting\PBMailReceiveRsp();
        $this->process($url, $req, $rsp, false);
        $this->logInfo($req);
        $this->logInfo($rsp);
    }

    public function actionWorkReportTypeList()
    {
        $this->setUserAccount('<EMAIL>');
        $url = self::TEST_DEV['karonyang'] . "/api/stormsFury/WorkReportRead/TypeList";
        $rsp = new \protobuf\Report\PBReportTypeListRsp();
        $this->process($url, null, $rsp,false);
        $this->logInfo($rsp);
    }

    public function actionWorkReportList()
    {
        $this->setUserAccount('<EMAIL>');

        $req = new \protobuf\Report\PBReportListReq();
        $req->setType(\protobuf\Report\PBReportType::REPORT_TYPE_WEEK);
        $req->setCurPage(1);
        $req->setPageSize(20);

        $url = self::TEST_DEV['karonyang'] . "/api/stormsFury/WorkReportRead/ReportList";
        $rsp = new \protobuf\Report\PBReportListRsp();
        $this->process($url, $req, $rsp,false);
        $this->logInfo($rsp);
    }


    public function actionFullFieldList()
    {
        $this->setUserAccount('<EMAIL>');
        $req = new \protobuf\CRMCommon\PBFullFieldListReq();
        $req->setType(4);
        $url = self::TEST_DEV['june'] . '/api/stormsFury/fieldSettingRead/fullFieldList';
        $rsp = new \protobuf\CRMCommon\PBFullFieldListRsp();
        $this->process($url, $req, $rsp, false);
        $this->logInfo($req);
    }


    public function actionProductFilterList()
    {
        $this->setUserAccount('<EMAIL>');
        $req = new \protobuf\Product\PBProductFilterListRsp();
        $url = self::TEST_DEV['amu'] . "/stormsFury/ProductRead/ProductFilterList";
        $this->process($url, $req, $rsp, false);
        $this->logInfo($rsp);
    }

    public function actionReportDetail()
    {
        $this->setUserAccount('<EMAIL>');
//        $this->setUserAccount('<EMAIL>');
        $req = new \protobuf\Report\PBReportDetailReq();
        $req->setReportId(**********);
//        $req->setUserId(26953);
//        $req->setDepartmentId(8521);
        $url = self::TEST_DEV['karonyang'] . "/api/stormsFury/WorkReportRead/ReportDetail";
//        $url = "https://app-daily-report.bugfix.dev.xiaoman.cn/api/stormsFury/WorkReportRead/ReportDetail";

        $rsp = new \protobuf\Report\PBReportDetailRsp();
        $this->process($url, $req, $rsp, false);
//        $this->process($url, $req, $rsp, false);
        $this->logInfo($rsp);
    }


    public function actionReportDetail1()
    {
        $this->setUserAccount('<EMAIL>');
//        $this->setUserAccount('<EMAIL>');

        $req = new \protobuf\Report\PBReportDetailReq();
        $req->setReportId(**********);
//        $req->setUserId(26953);
//        $req->setDepartmentId(8521);
        $url = self::TEST_DEV['karonyang'] . "/api/stormsFury/WorkReportRead/ReportDetail";

        $rsp = new \protobuf\Report\PBReportDetailRsp();
        $this->process($url, $req, $rsp, true);
//        $this->process($url, $req, $rsp, );
        $this->logInfo($rsp);
    }


    public function actionUploadFileExt()
    {
        $this->setUserAccount('<EMAIL>');
        $rsp = new PBUploadFileExtRsp();
        $url = self::TEST_DEV['june'].'/stormsFury/upload/uploadFileExt';
        $this->process($url, null, $rsp, true);
        $this->logInfo($rsp);
    }

    public function actionDigitalReportList($echoDirect=true)
    {
        $this->setUserAccount('<EMAIL>');

        $rsp = new PBDigitalReportListRsp();
        $url = self::TEST_DEV['seven'].'/stormsFury/reportRead/DigitalReportList';
        $this->process($url, null, $rsp, $echoDirect);
        $this->logInfo($rsp);
    }


    public function actionConversationMailList()
    {
        $this->setUserAccount('<EMAIL>');
        $req = new \protobuf\MailSync\PBConversationMailListReq();
        $req->setUserId(********);
        $req->setConversationId(1139953);
        $req->setUserId(********);
        $req->setMailDetailFlag(true);
        $url = self::TEST_DEV['wuyiwai'] . "/stormsFury/mailRead/conversationMailList";
        $rsp = new \protobuf\MailSync\PBConversationMailListRsp();
        $this->process($url, $req, $rsp, false);
        $this->logInfo($req);
        $this->logInfo($rsp);
    }

    public function actionConversationStatistic()
    {
        $this->setUserAccount('<EMAIL>');
        $req = new \protobuf\MailSync\PBConversationStatisticReq();
        $req->setConversationIds([**********]);
        $url = self::TEST_DEV['nuxse'] . "/stormsFury/mailRead/conversationStatistic";
        $rsp = new \protobuf\MailSync\PBConversationStatisticRsp();
        $this->process($url, $req, $rsp, false);
        $this->logInfo($req);
        $this->logInfo($rsp);
    }
    public function actionBindMailAlias()
    {
        $this->setUserAccount('<EMAIL>');
        $req = new \protobuf\MailSetting\PBBindMailAliasReq();
        $req->setUserMailId(********);
        $req->setMailAlias('<EMAIL>');
        $url = self::TEST_DEV['karonyang'] . "/api/stormsFury/mailSettingWrite/bindMailAlias";
        $this->process($url, $req, $rsp, true );
        $this->logInfo($rsp);
    }
    public function actionUnbindUserMailAlias()
    {
        $this->setUserAccount('<EMAIL>');
        $req = new \protobuf\MailSetting\PBUnbindUserMailAliasReq();
        $req->setUserMailId(********);
        $req->setAliasId(38);
        $url = self::TEST_DEV['karonyang'] . "/api/stormsFury/mailSettingWrite/unbindUserMailAlias";
        $this->process($url, $req, $rsp, true );
        $this->logInfo($rsp);
    }

    public function actionMailLastTranslateOption()
    {
        $this->setUserAccount('<EMAIL>');
        $rsp = new \protobuf\MailSync\PBMailLastTranslateOptionRsp();
        $url = self::TEST_DEV['wuyiwai'] . "/stormsFury/mailRead/mailLastTranslateOption";
        $this->process($url, null, $rsp, false);
        $this->logInfo($rsp);
    }

    // ./yiic-test stormfuryexample mailFontSettingList
    public function actionMailFontSettingList()
    {
        $this->setUserAccount('<EMAIL>');
        $rsp = new \protobuf\MailSetting\PBMailFontSettingListRsp();
        $url = self::TEST_DEV['wuyiwai'] . "/stormsFury/mailSettingRead/mailFontSettingList";
        $this->process($url, null, $rsp);
        $this->logInfo($rsp);
    }

    public function actionMailExtraData()
    {
        $this->setUserAccount('<EMAIL>');

        $req = new PBMailExtraDataReq();
        $req->setMailIds([**********]);
       // $req->setFields(['can_unlock','receive_read_receipt','large_attach_list','view_count']);
        $req->setFields(['can_unlock','view_count']);
        $url = self::TEST_DEV['k2'].'/stormsFury/mailRead/extraData';
        $rsp = new PBMailExtraDataRsp();
        $this->process($url, $req, $rsp);
        $this->logInfo($req);
        $this->logInfo($rsp);
    }

    public function actionMailInfo()
    {
        $this->setUserAccount('<EMAIL>');

        $req = new PBSubordinateMailReq();
        $req->setMailId(**********);
        $url = self::TEST_DEV['seven'].'/stormsFury/mailRead/subordinateMailInfo';
        $rsp = new PBSubordinateMailRsp();
        $req->setSkipViewPrivilege(true);
        $this->process($url, $req, $rsp,false);
        $baseInfo = $rsp->getBaseInfo();
        $this->logInfo($req);
        $this->logInfo($rsp);
        printf('expose_flag %s alibaba_info',$baseInfo->getExposeFlag());
        var_dump($rsp->getExtraData()->getAlibabaInfo());
        var_dump($baseInfo->getMailExposeInfo()->getSender());
        var_dump($baseInfo->getMailExposeInfo()->getSignMode());
        var_dump(iterator_to_array($baseInfo->getMailExposeInfo()->getSubject()));

    }

    public function actionKanBanDetail()
    {
        $this->setUserAccount('<EMAIL>');

        $req = new \protobuf\Report\PBKanBanDetailReq();
        $rsp = new \protobuf\Report\PBKanBanDetailRsp();
        $req->setReportKey('active_trend');
        $params = new \protobuf\Report\PBKanBanParams();

        $repeatParamArr = [];
        $dateParam = new \protobuf\Report\PBKanBanDateParam();
        $dateParam->setStartDate('2022-06-08');
        $dateParam->setEndDate('2022-08-14');
        $params->setDate($dateParam);
        $repeatParamArr[] = $params;


        $params = new \protobuf\Report\PBKanBanParams();
        $companyRangeparams = new \protobuf\Report\PBKanBanCompanyRangeParam();
        $companyRangeparams->setCompanyRange(0);
        $params->setCompanyRange($companyRangeparams);
        $repeatParamArr[] = $params;

        $req->setParams($repeatParamArr);

        $url = self::TEST_DEV['seven'].'/stormsFury/reportRead/KanBanDetail';
        $this->process($url, $req, $rsp,false);
        $this->logInfo($rsp);

    }

    public function actionKanBanDetailForPk()
    {
        $this->setUserAccount('<EMAIL>');

        $req = new \protobuf\Report\PBKanBanDetailReq();
        $rsp = new \protobuf\Report\PBKanBanDetailRsp();
        $req->setReportKey('load_pk_refer');
        $params = new \protobuf\Report\PBKanBanParams();

        $repeatParamArr = [];
        $dateParam = new \protobuf\Report\PBKanBanDateParam();
        $dateParam->setStartDate('2022-01-01');
        $dateParam->setEndDate('2022-09-30');
        $params->setDate($dateParam);
        $repeatParamArr[] = $params;

        $params = new \protobuf\Report\PBKanBanParams();
        $pkFieldParams = new \protobuf\Report\PBKanBanPKFieldParam();
        $pkFieldParams->setField(\protobuf\Report\PBPkField::COMPANY_ADD_COUNT);
        $params->setPkField($pkFieldParams);

        $repeatParamArr[] = $params;

        $req->setParams($repeatParamArr);

        $url = self::TEST_DEV['seven'].'/stormsFury/reportRead/KanBanDetailForLoadPk';
        $this->process($url, $req, $rsp,false);
        $this->logInfo($rsp);

    }

    public function actionPerformanceComponent()
    {
        $this->setUserAccount('<EMAIL>');

        $req = new \protobuf\Report\PBPerformanceComponentReq();
        $req->setRuleId(**********);
        //$req->setDepartmentId(28);
        $req->setStartDate('2023-02-01');
        $req->setEndDate('2023-02-28');

        $url = self::TEST_DEV['karonyang'].'/stormsFury/performanceV2Read/performanceComponent';
        $rsp = new \protobuf\Report\PBSummatItem();
        $this->process($url, $req, $rsp);
        $this->logInfo($req);
        $this->logInfo($rsp);
    }

    public function actionKanBanPerformanceProgress()
    {
        $this->setUserAccount('<EMAIL>');

        $req = new \protobuf\Report\PBKanBanDetailReq();
        $rsp = new \protobuf\Report\PBKanBanDetailRsp();
        $req->setReportKey('performance_progress');
        $params = new \protobuf\Report\PBKanBanParams();

        $repeatParamArr = [];
        $depAndUserParam = new \protobuf\Report\PBKanBanDepAndUserParam();
//        $depAndUserParam->setDepId(8479);
//        $depAndUserParam->setUserId(0);
//        $params->setVisibleId($depAndUserParam);
        $repeatParamArr[] = $params;

        $params = new \protobuf\Report\PBKanBanParams();
        $dateParam = new \protobuf\Report\PBKanBanDateParam();
        $dateParam->setStartDate('2023-02-01');
        $dateParam->setEndDate('2023-02-28');
        $params->setDate($dateParam);
        $repeatParamArr[] = $params;


        $params = new \protobuf\Report\PBKanBanParams();
        $pageParam = new \protobuf\Report\PBKanBanPageParam();
        $pageParam->setCurPage(1);
        $pageParam->setPageSize(20);
        $params->setPage($pageParam);
        $repeatParamArr[] = $params;


        $params = new \protobuf\Report\PBKanBanParams();
        $params->setPerformanceType(2);


        $repeatParamArr[] = $params;

        $req->setParams($repeatParamArr);

        $url = self::TEST_DEV['raylei'].'/api/stormsFury/reportRead/KanBanDetail';
        $this->process($url, $req, $rsp,false);
        $this->logInfo($rsp);
    }

    public function actionKanBanOrderAmountTrend()
    {
        $this->setUserAccount('<EMAIL>');

        $req = new \protobuf\Report\PBKanBanDetailReq();
        $rsp = new \protobuf\Report\PBKanBanDetailRsp();
        $req->setReportKey('order_amount_trend');
        $params = new \protobuf\Report\PBKanBanParams();

        $repeatParamArr = [];
        $dateParam = new \protobuf\Report\PBKanBanDateParam();
        $dateParam->setStartDate('2022-07-11');
        $dateParam->setEndDate('2022-08-31');
        $params->setDate($dateParam);
        $repeatParamArr[] = $params;

//        $params = new \protobuf\Report\PBKanBanParams();
//        $depAndUserParam = new \protobuf\Report\PBKanBanDepAndUserParam();
//        $depAndUserParam->setDepId(0);
//        $depAndUserParam->setUserId(0);
//        $params->setVisibleId($depAndUserParam);
//        $repeatParamArr[] = $params;
//
//        $params = new \protobuf\Report\PBKanBanParams();
//        $orderSearchParams = new \protobuf\Report\PBKanBanOrderSearchParam();
//        $orderSearchParams->setOrderTypes([
//            \protobuf\Invoices\PBOrderType::TYPE_CRM_ORDER,
//            \protobuf\Invoices\PBOrderType::TYPE_ALI_ORDER,
//        ]);
//        $orderSearchParams->setAliStatusIds([
//          1,2,3,5,6,7
//        ]);
//        //$orderSearchParams->setOrderStatus();
//        $orderSearchParams->setUserTypes([0]);
//        $params->setOrderSearch($orderSearchParams);
//        $repeatParamArr[] = $params;
        $req->setParams($repeatParamArr);

        $url = self::TEST_DEV['karonyang'].'/stormsFury/reportRead/KanBanDetail';
        $this->process($url, $req, $rsp);
        //$this->logInfo($req);
        $this->logInfo($rsp);
    }

    public function actionOrderList()
    {
        $this->setUserAccount('<EMAIL>');

        $req = new \protobuf\Invoices\PBOrderSearchReq();
        $rsp = new \protobuf\Invoices\PBOrderListRsp();
//        $req->setUserId(*********);
//        $req->setDepId(20);
//        $req->setUserTypes([
//            \protobuf\Invoices\PBOrderUserType::TYPE_CREATE_USER,
//            \protobuf\Invoices\PBOrderUserType::TYPE_PERFORMANCE_USER,
//            \protobuf\Invoices\PBOrderUserType::TYPE_CREATE_USER,
//            ]);
//        $req->setOrderTypes([
//            \protobuf\Invoices\PBOrderType::TYPE_ALI_ORDER,
//            \protobuf\Invoices\PBOrderType::TYPE_CRM_ORDER,
//        ]);
//        $req->setAliStatusIds([ //1,2,3,5,6,7,8
//            1,2,3,5,6,7,8
//        ]);
//        $req->setOrderStatus([********,**********,**********,**********,********,**********,********,**********,********,**********,********,**********,********,********,********,********,********,**********,**********,**********,**********]);
        $req->setDealType(2);
//        $req->setOrderPurchaseType(2);
//        $req->setCurPage(1);
        $req->setPageSize(100);
        $req->setBeginDate('2022-01-01');
        $req->setEndDate('2022-11-30');
        $req->setSortField('order_id');
        $req->setSortType('desc');

        $url = self::TEST_DEV['bob'].'/stormsFury/OrderRead/orderList';
        $this->process($url, $req, $rsp);
//        $this->logInfo($req);
        $this->logInfo($rsp, false, true);
    }

    public function actionApprovalReferTypeList()
    {
        $this->setUserAccount('<EMAIL>');

//        $req = new \protobuf\Approval\PBApprovalDiffReq();
        $rsp = new \protobuf\Approval\PBApprovalReferTypeListRsp();

        $url = self::TEST_DEV['bob'].'/stormsFury/approvalReadV2/ReferTypeList';
        $this->process($url,null, $rsp  );
//        $this->logInfo($req);
        $this->logInfo($rsp, false, true);
    }


    public function actionRayleiTest()
        {
            $this->setUserAccount('<EMAIL>');
            $req = new \protobuf\Invoices\PBCashCollectionDetailReq();
            $rsp = new \protobuf\Invoices\PBCashCollectionDetailRsp();
            $req->setCashCollectionId(**********);
            $url = self::TEST_DEV['karonyang'].'/api/stormsFury/CashCollectionRead/Detail';
            $this->process($url, $req, $rsp);
            $this->logInfo($req);
            $this->logInfo($rsp);

    }


    public function actionFollowUpCompanyList()
    {
        $this->setUserAccount('<EMAIL>');
        $req = new \protobuf\Customer\PBImportantCompanyReq();
        $req->setSwarmId(1);
        $req->setStartDate('2022-09-01');
        $req->setEndDate('2022-09-08');
        $req->setUserId([

        ]);

        $rsp = new \protobuf\Customer\PBImportantCompanyRsp();
        $url = self::TEST_DEV['seven'].'/api/stormsFury/CustomerRead/followUpCompanyList';
        $this->process($url, $req, $rsp,false );
        $this->logInfo($req);
        $this->logInfo($rsp);

    }

    public function actionKanbanDetailZbp()
    {
        $this->setUserAccount('<EMAIL>');

        $req = new \protobuf\Report\PBKanBanDetailReq();
        $rsp = new \protobuf\Report\PBKanBanDetailRsp();
        $req->setReportKey('order_product_model_rank');
        $params = new \protobuf\Report\PBKanBanParams();

        $repeatParamArr = [];
        $dateParam = new \protobuf\Report\PBKanBanDateParam();
        $dateParam->setStartDate('2021-03-18');
        $dateParam->setEndDate('2022-10-17');
        $params->setDate($dateParam);
        $repeatParamArr[] = $params;

        $params = new \protobuf\Report\PBKanBanParams();
        $pageParam = new \protobuf\Report\PBKanBanPageParam();
        $pageParam->setPageSize(100);
        $params->setPage($pageParam);
        $repeatParamArr[] = $params;

        $params = new \protobuf\Report\PBKanBanParams();
        $orderParams = new \protobuf\Report\PBKanBanOrderSearchParam();
//        $orderParams->setDealType(2);
//        $orderParams->setOrderPurchaseType(2);
//        $orderParams->setArchiveTypes([1,2]);
//        $orderParams->setOrderTypes([1,2]);
        $orderParams->setAliStatusIds([7]);
        $orderParams->setOrderStatus([**********]);
        $params->setOrderSearch($orderParams);
        $repeatParamArr[] = $params;

        $req->setParams($repeatParamArr);

        $url = self::TEST_DEV['bob'].'/stormsFury/reportRead/KanBanDetail';
        $this->process($url, $req, $rsp);
        $this->logInfo($rsp);
    }

    /** 测试看板聚合后根据orderConfig排序的用例
     * 预期能根据sum-invoice_product.amount（金额）降序排序
     */
    public function actionTestOrderConfig()
    {
        $this->setUserAccount('<EMAIL>');

        $req = new \protobuf\Report\PBKanBanDetailReq();
        $rsp = new \protobuf\Report\PBKanBanDetailRsp();
        $req->setReportKey('order_product_model_rank');
        $params = new \protobuf\Report\PBKanBanParams();

        $repeatParamArr = [];
        $dateParam = new \protobuf\Report\PBKanBanDateParam();
        $dateParam->setStartDate('2021-03-18');
        $dateParam->setEndDate('2022-10-17');
        $params->setDate($dateParam);
        $repeatParamArr[] = $params;

        $req->setParams($repeatParamArr);

        $url = self::TEST_DEV['bob'].'/stormsFury/reportRead/KanBanDetail';
        $this->process($url, $req, $rsp);
        $this->logInfo($rsp);
    }

    public function actionRemoveTrail()
    {
        $this->setUserAccount('<EMAIL>');
        $req = new \protobuf\Customer\PBCompanyTrailDetailReq();
        $req->setTrailId(**********);
        $url = self::TEST_DEV['fulili'].'/api/stormsFury/CustomerWrite/removeTrail';
        $this->process($url, $req);
        $this->logInfo($req);

    }

    public function actionPerformanceOrderStatistic()
    {
        $this->setUserAccount('<EMAIL>');

        $req = new \protobuf\Report\PBPerformanceOrderStatisticReq();
        $rsp = new \protobuf\Report\PBPerformanceOrderStatisticRsp();

        $req->setStartDate('2022-11-01');
        $req->setEndDate('2022-11-30');
//        $req->setUserId([********,30393,46900]);
        $req->setDepId([20]);
        $req->setPurchaseType([0,1,2]);

        $url = self::TEST_DEV['bob'].'/stormsFury/reportRead/performanceOrderGeneral';
        $this->process($url, $req, $rsp);
        $this->logInfo($rsp);
    }

    public function actionOrderProductDetail()
    {
        $this->setUserAccount('<EMAIL>');

        $req = new \protobuf\Report\PBKanBanOrderProductReq();
        $rsp = new \protobuf\Report\PBKanBanOrderProductRsp();

        $req->setStartDate('2022-04-14');
        $req->setEndDate('2022-04-14');
//        $req->setUserId([********]);
//        $req->setDepId([8538,8535]);
        $orderSearch = new \protobuf\Report\PBKanBanOrderSearchParam();
//        $orderSearch->setAliStatusIds([1]);
        $orderSearch->setDealType(1);
//        $orderSearch->setOrderPurchaseType(2);
//        $orderSearch->setOrderTypes([2]);
//        $orderSearch->setUserTypes([0,1,2]);
        $req->setOrderSearch($orderSearch);
        $req->setSortField('amount');
        $req->setReportKey('order_product_group_rank');
        $req->setRefreshFlag(1);

        $url = self::TEST_DEV['bob'].'/stormsFury/reportRead/orderProductDetail';
        $fullRsp = $this->process($url, $req, $rsp);
        $this->logInfo($rsp, true);
        if($fullRsp){
            $this->logInfo($fullRsp, true, true);
        }
    }

    public function actionKanBanOrderCompanyAnalysis()
    {
        $this->setUserAccount('<EMAIL>');
        $req = new \protobuf\Report\PBKanBanOrderCompanyAnalysisReq();
//        $req->setUserIds([765,46900,********]);
        $req->setDepId(20);
        $req->setStartDate('2022-10-01');
        $req->setEndDate('2022-10-31');
        $req->setView(1);
        $req->setOrderDealType(2);
        $req->setRegionalScope(3);
        $req->setSortField('amount');
        $req->setSortType(1);
        $companyParam = new \protobuf\Report\PBKanBanCompanySearchParam();
//        $companyParam->setOwnerType(1);
//        $companyParam->setArchiveTimeStart('2022-01-01');
//        $companyParam->setArchiveTimeEnd('2022-12-31');
        $req->setCompanySearch($companyParam);
        $orderParam = new \protobuf\Report\PBKanBanOrderSearchParam();
//        $orderParam->setUserTypes([1,2]);
//        $orderParam->setDealType(2);
//        $orderParam->setOrderPurchaseType(0);
//        $orderParam->setDealType(2);
//        $orderParam->setOrderStatus([1,2,3,5,6]);
        $req->setOrderSearch($orderParam);

        $url = self::TEST_DEV['karonyang'].'/api/stormsFury/reportRead/kanBanOrderCompanyAnalysis';
        $rsp = new \protobuf\Report\PBKanBanOrderCompanyAnalysisRsp();
       // $this->logInfo($req);
        $this->logInfo($req);
        $this->process($url, $req,$rsp);
        $this->logInfo($rsp);
    }

    public function actionKanBanCompanyActiveTrend()
    {
        $this->setUserAccount('<EMAIL>');
        $req = new \protobuf\Report\PBKanBanCompanyActiveTrendReq();
        $req->setUserIds([765, 46900, ********]);
        $req->setStartDate('2022-01-01');
        $req->setEndDate('2022-09-31');
//        $req->setOrderDealType(2);
        $rsp = new \protobuf\Report\PBKanBanCompanyActiveTrendRsq();
        $url = self::TEST_DEV['karonyang'].'/api/stormsFury/reportRead/companyActiveTrend';
        $this->logInfo($req);
        $this->process($url, $req,$rsp);

        $this->logInfo($rsp);
    }

    public function actionKanBanOrderAmountTrendV2()
    {
        $this->setUserAccount('<EMAIL>');
        $req = new \protobuf\Report\PBKanBanOrderAmountTrendReq();
        //$req->setUserIds([765, 46900, ********]);
        $req->setStartDate('2022-09-01');
        $req->setEndDate('2022-12-31');
        $req->setDepId(6);
        $orderParam = new \protobuf\Report\PBKanBanOrderSearchParam();
//        $orderParam->setOrderPurchaseType(2);
//        $orderParam->setUserTypes([1,2]);
//        $orderParam->setOrderStatus([1,2,3,5,6]);
        $orderParam->setArchiveTypes([1,2,3,4,5,6]);
        $req->setOrderSearch($orderParam);
        $rsp = new \protobuf\Report\PBKanBanOrderAmountTrendRsq();
        $url = self::TEST_DEV['karonyang'].'/api/stormsFury/reportRead/orderAmountTrend';
        $this->logInfo($req);
        $this->process($url, $req,$rsp);

        $this->logInfo($rsp);
    }

    public function actionOrderUserDetail()
    {
        $this->setUserAccount('<EMAIL>');

        $req = new \protobuf\Report\PBKanBanOrderUserReq();
        $rsp = new \protobuf\Report\PBKanBanOrderUserRsp();

        $req->setStartDate('2021-01-01');
        $req->setEndDate('2021-12-31');
//        $req->setUserId([30393,51106,71475,********,3,10,********,********,********,********,********,********,********,********,********,********,*********,*********,*********,*********,*********,*********,46900,********,********,********,*********,765,********,********,********,********,*********,*********,*********,*********,14,483,484,********,********,********,********,********,********,30421,********,********,********,********,********,*********,*********,*********,********,********,********,********,********,********,********,********,********,********,*********,*********,*********,*********,*********,51060,1,*********,*********,********,********,*********,********,377,482,734,51026,51068,51305,********,********,********,********,********,********,********,11858549,11858629,11858630,11858643,11858693,11858694,11858695,11858696,11858697,11858698,11858704,11859068,********,11863955,11863993,11864011,11864012,11864022,11864030,11864038,249503788,249503792,249514713,249514716,249514717,249514718,249518336,249518347,249518348,249518391,249518545,249518653,249518665,249518666,249518674,249518676,249518687,249518702,249518743,249518757,249519295,249519296,249519411]);
        $req->setDepId([8574]);
        $orderSearch = new \protobuf\Report\PBKanBanOrderSearchParam();
        $orderSearch->setDealType(2);
//        $orderSearch->setOrderPurchaseType(2);
        $req->setOrderSearch($orderSearch);
        $req->setSortField('amount,complete_ratio');
        $req->setRefreshFlag(1);

        $url = self::TEST_DEV['bob'].'/stormsFury/reportRead/orderUserDetail';
        $this->process($url, $req, $rsp);
        $this->logInfo($rsp);
    }
    public function actionGetAppComponentDefaultSetting()
    {
        $this->setUserAccount('<EMAIL>');
        $req = new \protobuf\CRMCommon\PBGetUserSettingAppComponentReq();
        $req->setKey('app.home.page.component.setting');
        //$req->setShowAll(1);
        $rsp = new \protobuf\CRMCommon\PBGetUserSettingAppComponentRsp();
        $url = self::TEST_DEV['karonyang'].'/stormsFury/userRead/GetUserSettingAppComponent';
        // $this->logInfo($req);
        $this->process($url, $req,$rsp);

        $this->logInfo($rsp);
    }


    public function actionMailConversationList()
    {
        $this->setUserAccount('<EMAIL>');

        $req = new \protobuf\MailSync\PBBaseMailListReq();
        $rsp = new \protobuf\MailSync\PBBaseMailListRsp();

        $anchorInfo =  new \protobuf\MailSync\PBAnchorInfo();
//        $anchorInfo->setAnchor(**********);
        $anchorInfo->setSize(20);
//        $anchorInfo->setConversationId(9038);
        //往上拉
        $anchorInfo->setDirection(0);
        $req->setAnchorInfo($anchorInfo);
        $req->setFolderIds([Mail::FOLDER_INBOX_ID]);
//        $req->setUserMailId(********); //某个邮箱
        $req->setUrgentFlag(-1);
        $req->setAttachFlag(1);
        //tagIds tagAllFlag 怎么联动
//        $req->setTagIds(["**********",
//            "**********"]);
//        $req->setTagAllFlag(0);

//        $req->setStartDate(**********);
        $req->setPinFlag(-1);
        $req->setTodoCompletedFlag(-1);
        $req->setReadFlag(-1);
//        $req->setAttachFlag(-1);
        $req->setRelateCompanyFlag(-1);
        $req->setTrackType(-1);
        $req->setUrgentFlag(-1);


        $url = self::TEST_DEV['seven'] . "/stormsFury/mailRead/MailConversationList";
//        $this->process($url, $req, $rsp,false);
        $this->process($url, $req, $rsp,true);
        $this->logInfo($rsp);
    }


    public function actionNewMailList()
    {
        $this->setUserAccount('<EMAIL>');

        $echo = false;

        $req = new \protobuf\MailSync\PBBaseMailListReq();
        $rsp = new \protobuf\MailSync\PBBaseMailListRsp();

        $anchorInfo =  new \protobuf\MailSync\PBAnchorInfo();
        $anchorInfo->setAnchor(strtotime('2024-03-21 09:02:03'));
        $anchorInfo->setSize(1);
        $anchorInfo->setMailId(**********);
        //往上拉
        $anchorInfo->setDirection(1);
        $req->setAnchorInfo($anchorInfo);
        $req->setFolderIds([Mail::FOLDER_INBOX_ID]);
//        $req->setUserMailId(********); //某个邮箱
        $req->setUrgentFlag(-1);
        $req->setAttachFlag(-1);
        //tagIds tagAllFlag 怎么联动
//        $req->setTagIds(["**********",
//            "**********"]);
//        $req->setTagAllFlag(0);

        $req->setPinFlag(-1);
        $req->setTodoCompletedFlag(-1);
        $req->setReadFlag(-1);
        $req->setRelateCompanyFlag(-1);
        $req->setTrackType(-1);
        $req->setUrgentFlag(-1);
//        $req->setStartDate(**********);

        $url = self::TEST_DEV['seven'] . "/stormsFury/mailRead/NewMailList";
        $this->process($url, $req, $rsp,$echo);
        $this->logInfo($rsp);
    }


    public function actionMailTodoList()
    {
        $this->setUserAccount('<EMAIL>');


        $req = new \protobuf\MailSync\PBBaseMailListReq();
        $rsp = new \protobuf\MailSync\PBBaseMailListRsp();

        $anchorInfo =  new \protobuf\MailSync\PBAnchorInfo();
        $anchorInfo->setAnchor(**********);
        $anchorInfo->setSize(44);
        $anchorInfo->setMailId(**********);
        $anchorInfo->setDirection(1);
        $req->setAnchorInfo($anchorInfo);

        $req->setTodoCompletedFlag(0);

        $url = self::TEST_DEV['seven'] . "/stormsFury/mailRead/MailTodoList";
        $this->process($url, $req, $rsp,false);
        $this->logInfo($rsp);
    }
    // 单据附件列表 ./yiic-test StormFuryExample InvoiceAttachmentList
    public function actionInvoiceAttachmentList()
    {
        $req =new \protobuf\Invoices\PBInvoiceAttachmentsListReq();
        $req->setType(47);
        $req->setId(**********);
        $url = self::TEST_DEV['bogiang'] . "/api/stormsFury/invoiceRead/attachmentsList";
        $rsp = new \protobuf\Invoices\PBInvoiceAttachmentsListRsp();
        $this->process($url, $req, $rsp, false);
        $this->logInfo($rsp);
    }

    // 单据新增附件 ./yiic-test StormFuryExample InvoiceAddAttachment
    public function actionInvoiceAddAttachment()
    {
        // 单据新增附件 ./yiic-test StormFuryExample InvoiceAddAttachment
        $req =new \protobuf\Invoices\PBInvoiceAddAttachmentsReq();
        $fileIds=[3214856839];// [3214856839];
        $req->setType(47);
        $req->setId(**********);
        $req->setFileIds($fileIds);
        $url = self::TEST_DEV['bogiang'] . "/api/stormsFury/invoiceWrite/addAttachments";
        $rsp = new \protobuf\Invoices\PBInvoiceAddAttachmentsReq();
        $this->process($url, $req, $rsp, true);
        $this->logInfo($rsp);
    }

    // 单据提交审批 ./yiic-test StormFuryExample InvoiceApplyApproval
    public function actionInvoiceApplyApproval()
    {
        $req =new \protobuf\Invoices\PBInvoiceApplyApprovalReq();
        $req->setType(44);
        $req->setId(3180440319);
        $url = self::TEST_DEV['bogiang'] . "/api/stormsFury/invoiceWrite/applyApproval";
        $rsp = new \protobuf\Invoices\PBInvoiceApplyApprovalRsp();
        $this->process($url, $req, $rsp, false);
        $this->logInfo($rsp);
    }

    // 费用单详情 ./yiic-test StormFuryExample CostInvoiceInfo
    public function actionCostInvoiceInfo()
    {
        \user::setLoginUserById('11858713');
        $req =new \protobuf\Invoices\PBCostInvoiceInfoReq();
        $req->setCostInvoiceId(5180375012);
        $url = self::TEST_DEV['karonyang'] . "/api/stormsFury/costInvoiceRead/info";
        $rsp = new \protobuf\Invoices\PBCostInvoiceInfoRsp();
        $this->process($url, $req, $rsp, true);
        $this->logInfo($rsp);
    }

    // 回款登记详情 ./yiic-test StormFuryExample CashCollectionInvoiceInfo
    public function actionCashCollectionInvoiceInfo()
    {
        $req =new \protobuf\Invoices\PBCashCollectionInvoiceInfoReq();
        $req->setCashCollectionInvoiceId(**********);
        $url = self::TEST_DEV['bogiang'] . "/api/stormsFury/cashCollectionInvoiceRead/info";
        $rsp = new \protobuf\Invoices\PBCashCollectionInvoiceInfoRsp();
        $this->process($url, $req, $rsp, false);
        $this->logInfo($rsp);
    }

    // 付款单详情 ./yiic-test StormFuryExample PaymentInvoiceInfo
    public function actionPaymentInvoiceInfo()
    {
        $this->setUserAccount('<EMAIL>');
        $req = new \protobuf\Invoices\PBPaymentInvoiceInfoReq();
        $req->setPaymentInvoiceId(**********);
        $url = self::TEST_DEV['bob'] . "/stormsFury/paymentInvoiceRead/info";
        $rsp = new \protobuf\Invoices\PBPaymentInvoiceInfoRsp();
        $this->process($url, $req, $rsp, false, false, 'bob');
        $this->logInfo($rsp);
    }

    // 采购订单详情 ./yiic-test StormFuryExample PurchaseOrderInfo
    public function actionPurchaseOrderInfo()
    {
        $this->setUserAccount('<EMAIL>');

        $req = new \protobuf\Invoices\PBPurchaseOrderInfoReq();
        $req->setPurchaseOrderId(**********);
        $url = self::TEST_DEV['gooray'] . "/api/stormsFury/purchaseOrderRead/info";
        $rsp = new \protobuf\Invoices\PBPurchaseOrderInfoRsp();
        $this->process($url, $req, $rsp, false, false, 'gooray');
        $this->logInfo($rsp);
    }

    // 采购订单产品详情 ./yiic-test StormFuryExample PurchaseOrderProductInfo
    public function actionPurchaseOrderProductInfo()
    {
        $this->setUserAccount('<EMAIL>');

        $req = new \protobuf\Invoices\PBPurchaseOrderProductInfoReq();
        $req->setPurchaseOrderProductId(**********);
//        $param = new \protobuf\CRMCommon\PBApprovalDiffReferParam();
//        $param->setApplyFormId('**********');
//        $productParam = new \protobuf\CRMCommon\PBApprovalDiffProductParam();
//        $productParam->setUniqueId('27f04827009c054e80ceeae669a54264');
//        $param->setProductParam($productParam);
//        $req->setApprovalReferParam($param);
        $url = self::TEST_DEV['gooray'] . "/api/stormsFury/purchaseOrderRead/purchaseOrderProductInfo";
        $rsp = new \protobuf\Invoices\PBPurchaseOrderProductInfoRsp();
        $this->process($url, $req, $rsp, false, false, 'gooray');
        $this->logInfo($rsp);
    }

    //名片列表
    public function actionBusinessCardList()
    {
        $this->setUserAccount('<EMAIL>');
        $req = new \protobuf\Customer\PBBusinessCardListReq();
//        $req->setArchiveType(1);
        $req->setPageSize(20);
        $req->setPageNo(1);
        $req->setUserId(********);
        // $req->setDraftStatus(1);
        $url = self::TEST_DEV['tina'] . "/api/stormsFury/customerRead/businessCardList";
        $rsp = new \protobuf\Customer\PBBusinessCardListRsp();
        $this->process($url, $req, $rsp, false, false, 'tina');
        $this->logInfo($rsp);
    }

    //名片转移
    public function actionBusinessCardTransfer()
    {
        $this->setUserAccount('<EMAIL>');
        $url = self::TEST_DEV['tyson'] . "/api/stormsFury/customerWrite/businessCardTransfer";
        $req = new PBBusinessCardTransferReq();
        $req->setBelongerUserId(51060);
        $req->setBusinessCardId([
            **********,
            **********,
            **********
        ]);

        $rsp = new PBBusinessCardOperateLogRsp();
        $this->process($url, $req, $rsp, false);
        $this->logInfo($rsp);
    }

    //名片操作历史
    public function actionBusinessCardOperateLog()
    {
        $this->setUserAccount('<EMAIL>');
        $url = self::TEST_DEV['tyson'] . "/api/stormsFury/customerRead/businessCardOperateLog";
        $req = new PBBusinessCardOperateLogReq();
        $req->setBusinessCardId(**********);
        $rsp = new PBBusinessCardOperateLogRsp();
        $this->process($url, $req, $rsp, false);
        $this->logInfo($rsp);
    }


    //名片表单
    public function actionBusinessCardForm()
    {
        $business_card_id = **********;
        $this->setUserAccount('<EMAIL>');
        $req = new \protobuf\Customer\PBBusinessCardDetailReq();
        $req->setBusinessCardId($business_card_id);
        $url = self::TEST_DEV['tina'] . "/api/stormsFury/customerRead/businessCardForm";
        $rsp = new \protobuf\Customer\PBBusinessCardRsp();
        $this->process($url, $req, $rsp, false, false, 'tina');
        $this->logInfo($rsp);
    }

    //名片保存
    public function actionSaveBusinessCard()
    {
        $this->setUserAccount('<EMAIL>');

        $req = new \protobuf\Customer\PBSaveBusinessCardReq();
        $req->setEnableFlag(true);
        $companyCard = new \protobuf\Customer\PBCompanyCardInfo();
        $companyCard->setName('YIGIT HAN OZDEMIR1');
        $companyCard->setAddress('SATIg TEMSiLCiSi');
        $telInfo = new \protobuf\Customer\PBTelInfo();
        $telInfo->setTel('***********');
        $telInfo->setAreaCode('355');
        $companyCard->setTel($telInfo);
        $companyCard->setFax('**********');
        $companyCard->setHomepage('favorite.com1');
        $companyCard->setRemark('大型交流现场');
        $origin = new \protobuf\Customer\PBCustomerOriginInfo();
        $origin->setOriginId(**********);
        $origin->setOriginName('展会');
        $companyCard->setOrigin($origin);

        $customerCard = new \protobuf\Customer\PBCustomerCardInfo();
        $customerCard->setName('0528第一次测试');
        $telInfos = [];
        $telInfo = new \protobuf\Customer\PBTelInfo();
        $telInfo->setTel('*********');
        $telInfo->setAreaCode('355');
        $telInfos[] = $telInfo;
        $customerCard->setTelList($telInfos);
        $contact = new \protobuf\Customer\PBSocialContact();
        $contact->setType('facebook');
        $contact->setValue('Fb123456');
        $customerCard->setContact([$contact]);
        $customerCard->setPost('攻城狮');
        $customerCard->setEmail(['<EMAIL>']);
        $req->setCustomerCard($customerCard);
        // $image = new \protobuf\Customer\PBImageInfo();
        // $image->setId(**********);
        // $req->setImage($image);
        $req->setCompanyCard($companyCard);

        $rsp = new \protobuf\Customer\PBBusinessCardRsp();
        $url = self::TEST_DEV['tina'] . '/stormsFury/customerWrite/saveBusinessCard';

        $this->process($url, $req, $rsp, false, false, 'tina');
        $this->logInfo($rsp);
    }


    public function actionDeleteBusinessCard()
    {
        $this->setUserAccount('<EMAIL>');

        $req = new \protobuf\Customer\PBSaveBusinessCardReq();

        $req->setEnableFlag(false);
        $req->setBusinessCardId(**********);

        $rsp = new \protobuf\Customer\PBBusinessCardRsp();

        $url = self::TEST_DEV['gooray'] . '/stormsFury/customerWrite/saveBusinessCard';

        $this->process($url, $req, $rsp, false, false, 'gooray');
        $this->logInfo($rsp);
    }

    //名片保存
    public function actionBusinessCardByFileId()
    {
        $business_card_id = 1;
        $this->setUserAccount('<EMAIL>');
        $req = new \protobuf\Customer\PBBusinessCardByFileIdReq();
        $req->setVcard('BEGIN:VCARD
VERSION:2.1
N;CHARSET=utf-8:黄;岳;;;
FN;CHARSET=utf-8:黄岳
TITLE;CHARSET=utf-8:高级研究员
ORG;CHARSET=utf-8:上海市涌东新区电子商务行业协会;复旦大学电子商务研究中心
TEL;CELL;CHARSET=utf-8:***********
TEL;CELL;CHARSET=utf-8:***********
TEL;WORK;CHARSET=utf-8:***********
TEL;WORK;CHARSET=utf-8:********
TEL;FAX;CHARSET=utf-8:***********
EMAIL;WORK;CHARSET=utf-8:<EMAIL>
URL;HOMEPAGE;CHARSET=utf-8:PH.Ilt.,t.P.,4&SgAWMSM&
ADR;WORK;CHARSET=utf-8:;;浦东新区上丰路977号 B 区7楼701 8室;上海市;;;中国
ADR;WORK;CHARSET=utf-8:;;国顺路670号李达三楼61 2室;上海市;;;中国
X-IS-ANGLE;CHARSET=utf-8:90
END:VCARD
');
        $req->setFileId(**********);
        $req->getFileUrl('http://bcr2.intsig.net/BCRService/BCR_VCF2');
        $url = self::TEST_DEV['tina'] . "/api/stormsFury/customerWrite/businessCardByFileId";
        $rsp = new \protobuf\Customer\PBBusinessCardRsp();
        $this->process($url, $req, $rsp, false, false, 'tina');
        $this->logInfo($rsp);
    }



    //速记列表
    public function actionStenographyList()
    {
        $this->setUserAccount('<EMAIL>');
        $req = new \protobuf\Customer\PBStenographyListReq();
        $req->setPageSize(10);
        $req->setPageNo(0);
        $req->setBusinessCardId(912);
        $url = self::TEST_DEV['fulili'] . "/api/stormsFury/customerRead/stenographyList";
        $rsp = new \protobuf\Customer\PBStenographyListRsp();
        $this->process($url, $req, $rsp);
        $this->logInfo($rsp);
    }

    //速记表单
    public function actionStenographyForm()
    {
        $stenography_id = **********;
        $this->setUserAccount('<EMAIL>');
        $req = new \protobuf\Customer\PBStenographyDetailReq();
        $rsp = new \protobuf\Customer\PBStenographyDetailRsp();
        $req->setStenographyId($stenography_id);
        $url = self::TEST_DEV['fulili'] . "/api/stormsFury/customerRead/stenographyForm";
        $this->process($url, $req, $rsp);
        $this->logInfo($rsp);
    }


    //速记保存
    public function actionSaveStenography()
    {
        $stenography_id = **********;
        $this->setUserAccount('<EMAIL>');
        $req = new \protobuf\Customer\PBSaveStenographyReq();
        $req->setEnableFlag(true);
        $req->setStenographyId($stenography_id);
        $req->setContent('test');
        $req->setBusinessCardId(1);
        $fileList = new \protobuf\CRMCommon\PBFileList();
        $fileInfo = new \protobuf\CRMCommon\PBFileInfo();
        $fileInfo->setFileId(**********);
        $images[] = $fileInfo;
        $fileInfo = new \protobuf\CRMCommon\PBFileInfo();
        $fileInfo->setFileId(**********);
        $images[] = $fileInfo;
        $fileList->setFileList($images);
        $req->setImages($fileList);
        $url = self::TEST_DEV['fulili'] . "/api/stormsFury/customerWrite/SaveStenography";
        $this->process($url, $req);
    }



    //名片建档表单
    public function actionFormFieldList()
    {
        $this->setUserAccount('<EMAIL>');

        $req = new PBFormFieldListReq();
        $rsp = new PBFormFieldListRsp();

        $req->setBusinessCardId(**********);
        $req->setArchiveFlag(1);

        $url = self::TEST_DEV['gooray'] . "/api/stormsFury/customerV2Read/formFieldList";

        $this->process($url, $req, $rsp, false, false, 'gooray');
        $this->logInfo($rsp);
    }

    public function actionVideoList()
    {
        $this->setUserAccount('<EMAIL>');

        $req = new protobuf\Activity\PBVideoListReq();
        $rsp = new protobuf\Activity\PBVideoListRsp();

        $req->setPageSize(10);
        $req->setCurPage(1);

        $url = self::TEST_DEV['milktea'] . "/api/stormsFury/ActivityRead/VideoList";

        $this->process($url, $req, $rsp);
        $this->logInfo($rsp);
    }


    //名片批量保存
    public function actionBatchSaveBusinessCard()
    {
        $business_card_id = 1;
        $this->setUserAccount('<EMAIL>');
        $req = new \protobuf\Customer\PBBatchSaveBusinessCardReq();
        $vcardInfo = new \protobuf\Customer\PBVcardInfo();
        $vcardInfo->setFileId(**********);
        $vcardInfo->setFileUrl('https://v4client.oss-cn-hangzhou.aliyuncs.com/other/img/********/UE1oNGtkdFhxODBoU0hGZlpEQnU5QmhqZE5LUnNwM21iUS9VWUgvR1J4dz0=.jpeg');
        $vcardInfo->setVcard("BEGIN:VCARD
VERSION:2.1
N;CHARSET=utf-8:HOK;AO UC;;;
FN;CHARSET=utf-8:AO UC HOK
TEL;CELL;CHARSET=utf-8:+***********
TEL;CELL;CHARSET=utf-8:+***********
TEL;WORK;CHARSET=utf-8:+*********
TEL;WORK;CHARSET=utf-8:********
TEL;WORK;CHARSET=utf-8:+************
TEL;WORK;CHARSET=utf-8:+***********
ADR;WORK;CHARSET=utf-8:;;162132, Bonoroncwefl Oonacrb r. Caooi\Yn-Fmmnn 1;;;;
X-IS-ANGLE;CHARSET=utf-8:0
END:VCARD");
        $vcardList[] = $vcardInfo;
        $vcardInfo = new \protobuf\Customer\PBVcardInfo();
        $vcardInfo->setFileId(**********);
        $vcardInfo->setFileUrl('https://v4client.oss-cn-hangzhou.aliyuncs.com/other/img/********/aXhmVk90OXM1RXgwTWxuc1cxblhrSy9nUG9RRHQ5bHRvdUZtRUxjWEtxaz0=.jpeg');
        $vcardInfo->setVcard("BEGIN:VCARD
VERSION:2.1
N;CHARSET=utf-8:;;;;
ORG;CHARSET=utf-8:YANTARI;
TEL;WORK;CHARSET=utf-8:+***********
TEL;WORK;CHARSET=utf-8:+393355652993
EMAIL;WORK;CHARSET=utf-8:<EMAIL>
URL;HOMEPAGE;CHARSET=utf-8:www.yantartgom
ADR;WORK;CHARSET=utf-8:;;Strada del Francese 10)56 T;ORINO;Lombardy;;Italy
ADR;WORK;CHARSET=utf-8:;;Roberto Bacino 660;;;;
X-IS-ANGLE;CHARSET=utf-8:0
END:VCARD");
        $vcardList[] = $vcardInfo;
        $req->setVcardList($vcardList);
        $url = self::TEST_DEV['gooray'] . "/api/stormsFury/customerWrite/batchSaveBusinessCard";
        $rsp = new \protobuf\Customer\PBBatchSaveBusinessCardRsp();
        $this->process($url, $req, $rsp, false, false, 'gooray');
        $this->logInfo($rsp);

    }

    // tab布局列表 ./yiic-test StormFuryExample PageLayoutList
    public function actionPageLayoutList()
    {
        $this->setUserAccount('<EMAIL>');
        $req = new \protobuf\CRMCommon\PBPageLayoutReq();
        $req->setMenuId('order');
        $req->setPageId(1);
        $allTabListUrl = self::TEST_DEV['onegong'] . "/api/stormsFury/pageLayoutRead/allTabList";
        $allTabListRsp = new \protobuf\CRMCommon\PBPageLayoutRsp();
        $this->process($allTabListUrl, $req, $allTabListRsp, true);
        $this->logInfo($allTabListRsp);

        $tabListUrl = self::TEST_DEV['onegong'] . "/api/stormsFury/pageLayoutRead/tabList";
        $tabListRsp = new \protobuf\CRMCommon\PBPageLayoutRsp();
        $this->process($tabListUrl, $req, $tabListRsp, true);
        $this->logInfo($tabListRsp);

        $userTabListUrl = self::TEST_DEV['onegong'] . "/api/stormsFury/pageLayoutRead/userTabList";
        $userTabListRsp = new \protobuf\CRMCommon\PBPageLayoutUserRsp();
        $this->process($userTabListUrl, $req, $userTabListRsp, true);
        $this->logInfo($userTabListRsp);
    }

    public function actionBusinessFieldUniqueDetail()
    {
        $this->setUserAccount('<EMAIL>');
        $req = new \protobuf\Customer\PBBusinessCardDetailReq();
        $req->setBusinessCardId(1);
        $url = self::TEST_DEV['fulili'] . "/api/stormsFury/customerRead/businessFieldUniqueDetail";
        $rsp = new \protobuf\Customer\PBCheckBusinessFieldRsp();
        $this->process($url, $req, $rsp);
        $this->logInfo($rsp);
    }


    public function actionStatisticUnreadWorkJournalRecord()
    {
        $this->setUserAccount('<EMAIL>');
        $url = self::TEST_DEV['karonyang'] . "/api/stormsFury/workJournalRead/statisticUnreadWorkJournalRecord";
        $rsp = new \protobuf\WorkJournal\PBStatisticUnreadWorkJournalRecordRsp();
        $this->process($url, null, $rsp, false);
        $this->logInfo($rsp);
    }

    public function actionWorkJournalDetail($journalId)
    {
        $req = new \protobuf\WorkJournal\PBWorkJournalDetailReq();
        $req->setJournalId($journalId);
        $this->setUserAccount('<EMAIL>');
        $url = self::TEST_DEV['karonyang'] . "/api/stormsFury/workJournalRead/workJournalDetail";
        $rsp = new \protobuf\WorkJournal\PBWorkJournalDetailRsp();
        $this->process($url, $req, $rsp, false);
        $this->logInfo($rsp);
    }

    public function actionWorkJournalTemplateList($filterCommitUserSetting)
    {
        $req = new \protobuf\WorkJournal\PBWorkJournalTemplateListReq();
//        $req->setFilterCommitUserSetting($filterCommitUserSetting);
        $req->setCurPage(1);
        $req->setPageSize(20);
        $this->setUserAccount('<EMAIL>');
        $url = self::TEST_DEV['karonyang'] . "/api/stormsFury/workJournalRead/templateList";
        $rsp = new \protobuf\WorkJournal\PBWorkJournalTemplateListRsp();
        $this->process($url, $req, $rsp, false);
        $this->logInfo($rsp);
    }

    public function actionWorkJournalTemplateDetail($templateId)
    {
        $req = new \protobuf\WorkJournal\PBWorkJournalTemplateDetailReq();
        $req->setTemplateId($templateId);
        $this->setUserAccount('<EMAIL>');
        $url = self::TEST_DEV['karonyang'] . "/api/stormsFury/workJournalRead/workJournalTemplateDetail";
        $rsp = new \protobuf\WorkJournal\PBWorkJournalTemplate();
        $this->process($url, $req, $rsp, false);
        $this->logInfo($rsp);
    }

    public function actionSaveWorkJournal($journalId, $templateId, $startTime, $endTime, $receiverUserIds, $performanceListData, $fieldListData, $fieldFieldInfoList)
    {
        $pbWorkJournal = new \protobuf\WorkJournal\PBWorkJournal();
        $pbWorkJournal->setJournalId($journalId);
        $pbWorkJournal->setTemplateId($templateId);
        $pbWorkJournal->setStartTime($startTime);
        $pbWorkJournal->setCycleEndTime($endTime);
        $pbWorkJournal->setReceiveUserId(\common\library\util\PgsqlUtil::trimArray($receiverUserIds));
        $pbWorkJournal->setPerformanceListData(json_decode($performanceListData, true));
        $fieldListData = json_decode($fieldListData, true);
        $fieldFieldInfoList = json_decode($fieldFieldInfoList, true);
        list($pbFieldInfoList, $pbFieldListData) = \common\library\custom_field\Helper::formatFieldInfoToProtobuf($fieldFieldInfoList, $fieldListData);
        $pbWorkJournal->setFieldListValue($pbFieldListData);
        $req = new \protobuf\WorkJournal\PBSaveWorkJournalReq();
        $req->setWorkJournal($pbWorkJournal);
        $this->setUserAccount('<EMAIL>');
        $url = self::TEST_DEV['karonyang'] . "/api/stormsFury/workJournalWrite/saveWorkJournal";
        $rsp = new \protobuf\WorkJournal\PBSaveWorkJournalRsp();
        $this->process($url, $req, $rsp);
        $this->logInfo($rsp);
    }

    public function actionDeleteWorkJournal($journalId)
    {
        $req = new \protobuf\WorkJournal\PBDeleteWorkJournalReq();
        $req->setJournalId($journalId);
        $this->setUserAccount('<EMAIL>');
        $url = self::TEST_DEV['karonyang'] . "/api/stormsFury/workJournalWrite/deleteWorkJournal";
        $this->process($url, $req, $rsp, false);
        $this->logInfo($rsp);
    }

    // ./yiic-test stormFuryExample recordList
    public function actionRecordList()
    {
        $this->setUserAccount('<EMAIL>');

        $req = new protobuf\WorkJournal\PBWorkJournalRecordListReq();
        $rsp = new protobuf\WorkJournal\PBWorkJournalRecordListRsp();

        $req->setUserIds([********]);
        $req->setRecordType(2);
        $req->setPageSize(100);
        $req->setCurPage(1);
//        $req->setReadFlag(0);

        $url = self::TEST_DEV['xyy'] . "/api/stormsFury/WorkJournalRead/WorkJournalRecordList";

        $this->process($url, $req, $rsp,true);
        $this->logInfo($rsp);
    }


    public function actionCompanyFollowUp()
    {
        $this->setUserAccount('<EMAIL>');
        $req = new \protobuf\WorkJournal\PBGetWorkJournalFollowupCompanyReq();
        $rsp = new \protobuf\WorkJournal\PBGetWorkJournalFollowupCompanyRsp();
        $startDate = strtotime("2023-06-06");
        $endDate = strtotime("2023-06-06");
        $url = self::TEST_DEV['raylei'] . "/api/stormsFury/WorkJournalRead/GetCompanyFollowUp";
        $req->setUserId(********);
        $req->setPageSize(20);
        $req->setStartTime($startDate);
        $req->setEndTime($endDate);
        $this->process($url, $req, $rsp);
        $this->logInfo($rsp);
    }

    public function actionCheckCanCommitWorkJournal($startTime, $endTime, $templateId)
    {
        $req = new \protobuf\WorkJournal\PBCheckCanCommitWorkJournalReq();
        $req->setTemplateId($templateId);
        $req->setStartTime($startTime);
        $req->setEndTime($endTime);
        $this->setUserAccount('<EMAIL>');
        $url = self::TEST_DEV['karonyang'] . "/stormsFury/workJournalRead/checkCanCommitWorkJournal";
        $rsp = new PBRspStatus();
        $this->logInfo($req,1,1);
        $this->process($url, $req, $rsp, false);
        $this->logInfo($rsp,1,1);
    }


    public function actionContrastGoalCompletionList()
    {
        $this->setUserAccount('<EMAIL>');
        $req = new \protobuf\Performance\PBContrastGoalCompletionListReq();
        $rsp = new \protobuf\Performance\PBContrastGoalCompletionListRsp();

        $url = self::TEST_DEV['workjournal'] . "/stormsFury/PerformanceV2Read/ContrastGoalCompletionList";
        var_dump($url);

        $req->setRuleId(0);
        $req->setTimeType(0);
        $req->setStartDate(*************);
        $req->setEndDate(*************);
        $req->setScope(2);
        $req->setOwnerId(51060);
        $req->setTargetType(1);
        $this->process($url, $req, $rsp);
        $this->logInfo($rsp);

    }

	public function actionDynamicQuickTextList()
	{
		$this->setUserAccount('<EMAIL>');
		$rsp=new PBDynamicQuickTextListRsp();
		$url = self::TEST_DEV['fulili'].'/api/stormsFury/CustomerRead/DynamicQuickTextList';
		$this->process($url, null, $rsp);
		$this->logInfo($rsp);

    }

    public function actionIdentify()
    {
        $req = new \protobuf\Customer\PBContactIdentifyReq();
        $rsp = new \protobuf\Customer\PBContactIdentifyRsp();

        $url = self::TEST_DEV['june'] . "/stormsFury/CustomerContactRead/Identify";

        $req->setPlatform(3);
        $req->setIdentifier(*********);
        $req->setDirectOwner(1);
        $req->setSellerAccountId(*********);
        $req->setUserId(********);
        $this->httpProcess($url, $req, $rsp, true);
        $this->logInfo($rsp);

    }

    public function actionTmLogin()
    {
        $req = new \protobuf\SalesGuide\PBTmLoginReq();
        $rsp = new \protobuf\SalesGuide\PBTmLoginRsp();

        $req->setToken('ea998b11e1b50bee9dc27feb435ab6c90b2b2457d5673d82d6f54d751f509ce2');
        $req->setOrigin('app');

        $url = self::TEST_DEV['june'] . "/stormsFury/userRead/tmLogin";
        $this->httpProcess($url, $req, $rsp, true);
        $this->logInfo($rsp);

    }


    public function actionFormFieldListV2()
    {
        $req = new PBFormFieldListReq();
        $rsp = new PBFormFieldListRsp();

        $req->setCompanyId('**********');

        $url = self::TEST_DEV['june'] . "/stormsFury/customerV2Read/formFieldList";
        $this->httpProcess($url, $req, $rsp, true);
        $this->logInfo($rsp);

    }

    public function actionSuggestionList()
    {
        $req = new \protobuf\SalesGuide\PBSuggestionListReq();
        $rsp = new \protobuf\SalesGuide\PBSuggestionListRsp();

        $req->setLinkId(**********);

        $url = self::TEST_DEV['june'] . "/stormsFury/SalesGuideRead/Suggestionlist";
        $this->httpProcess($url, $req, $rsp, true);
        $this->logInfo($rsp);

    }

    public function actionImSendMessage()
    {
        $req = new \protobuf\Alibaba\PBImSendMessageReq();

        $req->setSenderAccountId(*********);
        $req->setReceiverAccountId(*********);
        $req->setContent('a bc');
        $req->setMessageType(63);
        $req->setScene(63);
        $req->setExtParam('{}');
        $req->setClientInfo('web_seller_pwa');
        $req->setEntranceSource('okki');

        $url = self::TEST_DEV['june'] . "/stormsFury/AlibabaWrite/ImSendMessage";
        $this->httpProcess($url, $req, $rsp, true);
        $this->logInfo($rsp);

    }


    protected function httpProcess(
        $url,
        $req,
        \Google\Protobuf\Internal\Message &$rsp = null,
        bool $echoDirect = false
    )
    {
        $data = $req->serializeToString();

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, ["Content-type:application/x-protobuf","x-xiaoman-proto-version:2","x-xiaoman-proto:application/x-protobuf"]);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);

        $ret = curl_exec($ch);
        if ($ret === false) {
            $error = curl_error($ch);
            $errorCode = curl_errno($ch);
            $error .= ' error_no:' . $errorCode;

            echo "error: ";
            var_dump($error);
            var_dump($errorCode);
        }
        curl_close($ch);

        if ($echoDirect) {
            echo "开始打印信息: " . PHP_EOL;
            var_dump($ret);
            echo "结束打印信息: " . PHP_EOL;
            die();
        }

    }

    public function actionAIAgentList()
    {
        $this->setUserAccount('<EMAIL>');
        $rsp = new \protobuf\OkkiAi\PBAgentListRsp();
        $url = self::TEST_DEV['xyy'] . "/stormsFury/AiAgentRead/AiAgentList";
        $this->process($url, null, $rsp, false);
        $this->logInfo($rsp, true, true);

    }

    public function actionOpenConversation()
    {
        $this->setUserAccount('<EMAIL>');
        $rsp = new \protobuf\OkkiAi\PBOpenConversationRsp();
        $req = new \protobuf\OkkiAi\PBOpenConversationReq();

//        $req->setAgentId(AiAgent::AI_AGENT_SCENE_TYPE_MAIL_WRITE);
//        $url = self::TEST_DEV['wuliwei'] . "/stormsFury/AiAgentReadService/openConversation";
//        $this->process($url, $req, $rsp, false);
//        $this->logInfo($rsp);
//        $rsp = new \protobuf\OkkiAi\PBOpenConversationRsp();
//        $req->setAgentId(AiAgent::AI_AGENT_SCENE_TYPE_MAIL_POLISH);
//        $url = self::TEST_DEV['wuliwei'] . "/stormsFury/AiAgentReadService/openConversation";
//        $this->process($url, $req, $rsp, false);
//        $this->logInfo($rsp);

//        $rsp = new \protobuf\OkkiAi\PBOpenConversationRsp();
//        $req->setAgentId(AiAgent::AI_AGENT_SCENE_TYPE_DATA_ANALYSIS);
//        $url = self::TEST_DEV['wuliwei'] . "/stormsFury/AiAgentReadService/openConversation";

        $req->setAgentId(7);
        $conversationParams = new \protobuf\OkkiAi\PBOpenConversationParams();
        $dataParams = new \protobuf\OkkiAi\PBOpenConversationDataDistributionParams();
        $dataParams->setCompanyId(3580221716);
        $params = new \protobuf\OkkiAi\PBOpenConversationParamsParams();
        $params->setDataDistributionParams($dataParams);
        $conversationParams->setParams($params);
        $req->setConversationParams($conversationParams);
        $url = self::TEST_DEV['xyy'] . "/stormsFury/aiAgentRead/openConversation";
        $this->process($url, $req, $rsp, false, false, "yingyingxian");
        $a = $rsp->serializeToJsonString();
        $this->logInfo($rsp, true,true);
//        $url =  "127.0.0.1:8888/api/stormsFury/AiAgentRead/openConversation";
//
//        $this->process($url, $req, $rsp, false, false);
//        $this->logInfo($rsp);
    }

    public function actionOpenConversation2()
    {
        $this->setUserAccount('<EMAIL>');
        $rsp = new \protobuf\OkkiAi\PBOpenConversationRsp();
        $req = new \protobuf\OkkiAi\PBOpenConversationReq();

        $req->setAgentId(9);
        $url = self::TEST_DEV['adamzj'] . "/stormsFury/aiAgentRead/openConversation";
        $this->process($url, $req, $rsp, true, true, "adamzj");
        $this->logInfo($rsp, true,true);
    }

    public function actionAiAgentTipsWordList() {
        $this->setUserAccount('<EMAIL>');
        $rsp = new \protobuf\OkkiAi\PBAiAgentTipsWordListRsp();
        $req = new \protobuf\OkkiAi\PBAiAgentTipsWordListReq();
        $req->setAgentId(1);
        $url = self::TEST_DEV['ai-mail'] . "/stormsFury/aiAgentRead/AiAgentTipsWordList";
        $this->process($url, $req, $rsp, false);
        $this->logInfo($rsp);

        $req->setAgentId(2);
        $url = self::TEST_DEV['ai-mail'] . "/stormsFury/aiAgentRead/AiAgentTipsWordList";
        $this->process($url, $req, $rsp, false);
        $this->logInfo($rsp);
    }

    public function actionGetKCoinInfo() {
        $this->setUserAccount('<EMAIL>');

        $rsp = new \protobuf\OkkiAi\PBGetKCoinInfoRsp();
        $url = self::TEST_DEV['raylei'] . "/api/stormsFury/aiagentRead/GetKCoinInfo";
        $this->process($url,null, $rsp, false);
        $this->logInfo($rsp);
    }

    public function actiongetKCoinInfoDesc() {
        $this->setUserAccount('<EMAIL>');
        $rsp = new \protobuf\OkkiAi\PBKCoinInfoDescRsp();
        $url = self::TEST_DEV['wuliwei'] . "/stormsFury/AiAgentReadService/GetKCoinInfoDesc";
        $this->process($url,null, $rsp, false);
        $this->logInfo($rsp);

    }

    public function actionAiAgentChatCompletions()
    {
        $this->setUserAccount('<EMAIL>');
        $req = new \protobuf\OkkiAi\PBAiAgentChatCompletionsReq();
        $rsp = new \protobuf\OkkiAi\PBAiAgentChatCompletionsRsp();
        $req->setConversationId(**************);
        $textQuestion = '这个月赢单的金额有多少？';
        $detailQuestion = '本月创建的客户有哪些？';
        //$chartQuestion = '成交金额最高的top10客户是谁';
        $hightLightQuestion = '近1年，每个月商机总金额是多少？';
        $dateQuestion = '过去3个月中，每个员工每天创建了多少个客户？';
        $presetQuestion = '近3个月，每个月商机总金额是多少？';
        $params = new \protobuf\OkkiAi\PBAiAgentChatCompletionsReqParams();
        $mailSummaryParams = new \protobuf\OkkiAi\PBAiAgentMailReplyReqParams();
        $mailSummaryParams->setMailId(**********);
        $params->setMailReplyParams($mailSummaryParams);
        $req->setSceneType(4);
        $req->setParams($params);
//        $req->setQuestion("你是谁");
//        $req->setQuestion("今年新建客户的国家地区分布占比是怎样的？");
//        $url = "127.0.0.1:8888/api/stormsFury/AiAgentReadService/AiAgentChatCompletions";
//        $url = self::TEST_DEV['raylei'] . "/stormsFury/AiAgentRead/AiAgentChatCompletions";
        $url = "127.0.0.1:8888/api/stormsFury/AiAgentRead/AiAgentChatCompletions";
//        $url = self::TEST_DEV['app-ai-report-v2'] . "/stormsFury/AiAgentRead/AiAgentChatCompletions";

        $this->process($url,$req, $rsp, true);
        $this->logInfo($rsp);
    }

    public function actionAiAgentChatCompletions2()
    {
        $this->setUserAccount('<EMAIL>');
        $req = new \protobuf\OkkiAi\PBAiAgentChatCompletionsReq();
        $rsp = new \protobuf\OkkiAi\PBAiAgentChatCompletionsRsp();
        $req->setConversationId(**********);
        $params = new \protobuf\OkkiAi\PBAiAgentChatCompletionsReqParams();
        $chatReplyParams = new \protobuf\OkkiAi\PBAiAgentChatReplyReqParams();
        $chatReplyParams->setChannelType(\protobuf\OkkiAi\PBChannelType::CHANNEL_TYPE_TM);
        $chatReplyParams->setSellerAccountId(*********);
        $chatReplyParams->setBuyerAccountId(*********);
        $chatReplyParams->setStrategyId(2002);
        $params->setChatReplyParams($chatReplyParams);
        $req->setSceneType(\protobuf\OkkiAi\PBAgentSceneType::SCENE_TYPE_CHAT_REPLY);
        $req->setParams($params);
        $req->setQuestion("坚持不改变价格，价格是好产品的唯一缺点，如果客户尝试购买就会发现它们物超所值");
        $url = self::TEST_DEV['adamzj'] . "/stormsFury/aiAgentRead/aiAgentChatCompletions";
        $this->process($url, $req, $rsp, true, true, "adamzj");
        $this->logInfo($rsp, true, true);
    }

    // ./yiic-test stormFuryExample getHistory
    public function actionGetHistory()
    {
        $url =  self::TEST_DEV['xyy'] . '/api/stormsFury/aiAgentRead/conversationHistory';
        $this->setUserAccount( '<EMAIL>');
        $req = new \protobuf\OkkiAi\PBConversationHistoryReq();
        $req->setAgentId(7);
        $req->setConversationId(**********);
//        $req->setHistoryId(**********);
//        $req->setHistoryId(**********);

        $rsp = new \protobuf\OkkiAi\PBConversationHistoryRsp();
        $this->process($url,$req, $rsp, false, null, "yingyingxian");
        $a = $rsp->serializeToJsonString();
        $this->logInfo($rsp, true, true);
//        $res = $rsp->getList();
//        print_r(json_decode($rsp->serializeToJsonString(), true));

    }

    // 报表生成专用
    // ./yiic-test stormFuryExample chatGenerateData
    public function actionChatGenerateData()
    {
        $this->setUserAccount('<EMAIL>');
        $req = new \protobuf\OkkiAi\PBAiAgentChatCompletionsReq();
        $rsp = new \protobuf\OkkiAi\PBAiAgentChatCompletionsRsp();
        $req->setConversationId(**********);
//        $textQuestion = '这个月赢单的金额有多少？';
//        $orderQuestion = '这个月的销售订单总金额是多少？';
//        $oppornity = '本年的赢单金额有多少？';
//        $detailQuestion = '本月创建的客户有哪些？';
//        //$chartQuestion = '成交金额最高的top10客户是谁';
//        $presetQuestion = '这周跟进了多少客户？';
        $presetQuestion2 = '订单分析';
        $req->setQuestion($presetQuestion2);
        $params = new \protobuf\OkkiAi\PBAiAgentChatCompletionsReqParams();
        $dataGenerate = new \protobuf\OkkiAi\PBAiAgentDataDistributionChatReqParams();
        $dataGenerate->setCompanyId(0);
        $params->setDataDistributionParams($dataGenerate);
        $req->setParams($params);
        $req->setSceneType(\protobuf\OkkiAi\PBAgentSceneType::SCENE_TYPE_GENERATION_DATA);
//        $req->setQuestion("你是谁");
//        $req->setQuestion("今年新建客户的国家地区分布占比是怎样的？");
        $url = self::TEST_DEV['xyy'] . "stormsFury/AiAgentRead/AiAgentChatCompletions";
//        $url = self::TEST_DEV['app-ai-report-v2'] . "/stormsFury/AiAgentRead/AiAgentChatCompletions";

        $this->process($url,$req, $rsp, true, null, "yingyingxian");
        $this->logInfo($rsp, true, true);
    }

    // ./yiic-test stormFuryExample aiAgentFeedBack
    public function actionAiAgentFeedBack()
    {
        $this->setUserAccount('<EMAIL>');
        $req = new \protobuf\OkkiAi\PBFeedbackReq();
        $req->setFavorite(1);
        $req->setRecordId([**********,**********,**********,**********]);
//        $req->setQuestion("这个月创建了多少个客户？");
//        $req->setQuestion("你是谁");
        $url = self::TEST_DEV['1086078'] . "/api/stormsFury/AiAgentWrite/feedback";
        $url = self::TEST_DEV['xyy'] . "/api/stormsFury/AiAgentWrite/feedback";

        $this->process($url,$req, $rsp, true, null, 'yingyingxian');
        $this->logInfo($rsp, true, true);
    }


    public function actionConversationHistory()
    {
        $this->setUserAccount('<EMAIL>');
        $req = new \protobuf\OkkiAi\PBConversationHistoryReq();
        $rsp = new \protobuf\OkkiAi\PBConversationHistoryRsp();
        $req->setAgentId(7);
        $req->setConversationId(**********);
        $req->setHistoryId(**********);
//        $req->setQuestion("你是谁");
//        $req->setQuestion("今年新建客户的国家地区分布占比是怎样的？");
        $url = self::TEST_DEV['xyy'] . "/stormsFury/aiAgentRead/ConversationHistory";
        $this->process($url, $req, $rsp, false, false, "xyy");

        $this->logInfo($rsp, true, true);
    }

    public function actionConversationList()
    {
        $this->setUserAccount('<EMAIL>');

        $req = new \protobuf\MailSync\PBBaseMailListReq();
        $rsp = new \protobuf\MailSync\PBBaseMailListRsp();

        $anchorInfo =  new \protobuf\MailSync\PBAnchorInfo();
        $anchorInfo->setSize(50);
        $anchorInfo->setAnchor(0);
        $anchorInfo->setConversationId(0);

        //往下拉
        //        $anchorInfo->setDirection(0);
        //往上拉
        $anchorInfo->setDirection(0);

        $req->setAnchorInfo($anchorInfo);

        //收件箱
        $req->setFolderIds([Mail::FOLDER_INBOX_ID]);

        //走es往下拉
        $req->setRelateCompanyFlag(-1);

//        $req->setUserMailId(********); //某个邮箱
        //未读
        $req->setReadFlag(-1);
        $req->setTrackType(-1);

        $req->setUrgentFlag(-1);
        $req->setAttachFlag(1);
        $req->setPinFlag(-1);
        //测试todo_flag
        $req->setTodoCompletedFlag(-1);
        $req->setUrgentFlag(-1);

//        $req->setAttachFlag(1);
//        $req->setTrackType(1);

        //tagIds tagAllFlag 怎么联动
//        $req->setTagIds(["**********",
//            "**********"]);
//        $req->setTagAllFlag(0);


        $url = self::TEST_DEV['seven'] . "/stormsFury/mailRead/ConversationList";
//        $this->process($url, $req, $rsp,false);
        $this->process($url, $req, $rsp,false);
        $this->logInfo($rsp);

    }

    public function actionWriteMailConfig()
    {
        $this->setUserAccount('<EMAIL>');
        $rsp = new \protobuf\OkkiAi\PBWriteMailConfigRsp();
        // stormsFury/aiAgentRead/WriteMailConfig
        $url = self::TEST_DEV['ai-mail'] . "/stormsFury/aiAgentRead/WriteMailConfig";
        print($url);
        $this->process($url, null, $rsp, false);
        $this->logInfo($rsp);

    }

    public function actionTranslateMailText() {
        $this->setUserAccount('<EMAIL>');
        $rsp = new \protobuf\OkkiAi\PBTranslateMailTextRsp();
        $text = [
            "哇哈哈",
            "蒙牛牛"
        ];

        $req = new \protobuf\OkkiAi\PBTranslateMailTextReq();
        $req->setMailText($text);
        $url = self::TEST_DEV['wuliwei'] . "/stormsFury/aiAgentRead/TranslateMailText";
        print($url);
        $this->process($url, $req, $rsp, false, false, "wuliwei");
        $this->logInfo($rsp);
    }

    public function actionOpenConversationAndCompletion(){
        // OpenConversation逻辑
        $this->setUserAccount('<EMAIL>');
        $OpenConversationRsp = new \protobuf\OkkiAi\PBOpenConversationRsp();
        $req = new \protobuf\OkkiAi\PBOpenConversationReq();
        $req->setAgentId(AiAgent::AI_AGENT_SCENE_TYPE_MAIL_WRITE);
        $url = self::TEST_DEV['wuliwei'] . "/stormsFury/AiAgentReadService/openConversation";
        $this->process($url, $req, $OpenConversationRsp, false);
        $this->logInfo($OpenConversationRsp);
        $historyId = $OpenConversationRsp->getPresetMessageList()->getHistoryId();

        $this->setUserAccount('<EMAIL>');
        $CompletionReq = new \protobuf\OkkiAi\PBAiAgentChatCompletionsReq();
        $CompletionReq->setSceneType(1);
        $CompletionReq->setFromHistoryId($historyId);
        $rsp = new \protobuf\OkkiAi\PBAiAgentChatCompletionsRsp();

//        $req->setConversationId($OpenConversationRsp->getConversationId());
//        $req->setQuestion("这个月创建了多少个客户？");
//        $req->setQuestion("你是谁");
//        $req->setQuestion("今年新建客户的国家地区分布占比是怎样的？");

        $mailParams = new \protobuf\OkkiAi\PBAiAgentMailWriteChatReqParams();
        $mailParams->setLanguage("俄语");
        $mailParams->setLanguageEn("ru");
        $mailParams->setCompanyName("AIlab");
        $mailParams->setProductName("AI产品");
        $mailParams->setExtraDesc("111我这个消息是新的");



        $params = new \protobuf\OkkiAi\PBAiAgentChatCompletionsReqParams();

        $params->setMailWriteParams($mailParams);
        $CompletionReq->setParams($params);
        $CompletionReq->setConversationId($OpenConversationRsp->getConversationId());
        $url = self::TEST_DEV['wuliwei'] . "/stormsFury/AiAgentReadService/AiAgentChatCompletions";


        $this->process($url,$CompletionReq, $rsp, false);
        $this->logInfo($rsp);
    }

    public function actionApprovalList()
    {
        $this->setUserAccount('<EMAIL>');
        $req = new protobuf\Approval\PBApprovalFormListReq();
        $rsp = new protobuf\Approval\PBApprovalFormListRsp();
        $req->setReferType(\protobuf\Approval\PBApprovalReferType::ENTITY_TYPE_SHIPPING_INVOICE);
        $req->setType(3);
        $req->setPageNo(1);
        $req->setPageSize(20);
        $url = self::TEST_DEV['gooray'] . "/api/stormsFury/approvalReadV2/approvalFormSearch";
        $this->process($url,$req, $rsp, false, false, 'gooray');
        $this->logInfo($rsp);
    }





    public function actionJsConfig()
    {
//        $this->setUserAccount(' <EMAIL>');
        $this->setUserAccount('<EMAIL>');
        $req = new \protobuf\Wecom\PBJsConfigReq();

        $req->setUrl("https://leads-iteration-1-1-409.story.dev.xiaoman.cn");

        $url = self::TEST_DEV['steven'] . "/api/stormsFury/WecomRead/JsConfig";
        $rsp = new \protobuf\Wecom\PBJsConfigRsp();
        $this->process($url, $req, $rsp, false);
        $this->logInfo($rsp);
    }


    public function actionWecomInfo()
    {
        $this->setUserAccount(' <EMAIL>');
//        $this->setUserAccount('<EMAIL>');

        $url = self::TEST_DEV['steven'] . "/api/stormsFury/WecomRead/WecomInfo";  // DiscoveryRead
        $rsp = new \protobuf\Wecom\PBWecomInfoRsp();
        $this->process($url, '', $rsp, false);
//        $this->logInfo($rsp);
    }


    public function actionGenerateLoginUrl()
    {
        $this->setUserAccount('<EMAIL>');

        $req = new \protobuf\Wecom\PBGenerateLoginUrlReq();

        $url = self::TEST_DEV['steven'] . "/api/stormsFury/WecomRead/GenerateLoginUrl";  // DiscoveryRead
        $origin = \common\library\account\service\WeComService::ORIGIN_WECOM_APP;
        $code = "ptoxafgOUBuSiY151Lznk4CthNpNz1qq2wL6WOIMznU";

        $service = new \common\library\account\service\WeComService();
        $service->setOrigin($origin);
        $service->setCode($code);
        $result = $service->generateLoginUrl();

        $rsp = new \protobuf\Wecom\PBGenerateLoginUrlRsp();
        $rsp->setUrl($result['url'] ?? '');
        $rsp->setExpire($result['expire'] ?? 0);

        $this->process($url, $req, $rsp, false);
//        $this->logInfo($rsp);
    }

    function actionDiscountList()
    {
        $this->setUserAccount('<EMAIL>');
        $rsp = new \protobuf\OkkiAi\PBAiAgentDiscountRsp();
        $req = new \protobuf\OkkiAi\PBAiAgentDiscountReq();
        $req->setSceneTypeList([\protobuf\OkkiAi\PBAgentSceneType::SCENE_TYPE_DATA_ANALYSIS,
            \protobuf\OkkiAi\PBAgentSceneType::SCENE_TYPE_GENERATION_DATA]);

        $url = self::TEST_DEV['wuliwei'] . "/stormsFury/AiAgentReadService/DiscountList";

        $this->process($url, $req, $rsp, false, false, "wuliwei");
        $this->logInfo($rsp);
    }

    public function actionAvailableUserList()
    {

        $this->setUserAccount('<EMAIL>');
        $req = new \protobuf\Customer\PBAvailableUserListReq();
        $req->setCompanyIds([**********]);
        $rsp = new \protobuf\CRMCommon\PBUserInfoListRsp();
        $url = self::TEST_DEV['gooray'] . '/stormsFury/customerRead/availableUserList';

        $this->process($url, $req, $rsp);
        $this->logInfo($rsp);
    }

    /**
        ./yiic-test stormfuryexample MailTrackListOpenAnchor
     */
    public function actionMailTrackListOpenAnchor()
    {
        $this->setUserAccount('<EMAIL>');
        $req = new \protobuf\MailSync\PBBaseMailListReq();
        $anchorInfo = new \protobuf\MailSync\PBAnchorInfo();
        $anchorInfo->setSize(20);
        $anchorInfo->setAnchor(**********);
        $anchorInfo->setMailId(**********);
        $req->setAnchorInfo($anchorInfo);
        $rsp = new \protobuf\MailSync\PBMailSearchListRsp();
        $url = self::TEST_DEV['nanrui'] . '/stormsFury/traceRead/mailTrackListOpenAnchor';

        $this->process($url, $req, $rsp);
        $this->logInfo($req);
        $this->logInfo($rsp);
    }

    /**
        ./yiic-test stormfuryexample MailTrackListUnopenAnchor
    */
    public function actionMailTrackListUnopenAnchor()
    {
        $this->setUserAccount('<EMAIL>');
        $req = new \protobuf\MailSync\PBBaseMailListReq();
        $anchorInfo = new \protobuf\MailSync\PBAnchorInfo();
        $anchorInfo->setSize(20);
        $anchorInfo->setAnchor(**********);
        $anchorInfo->setMailId(**********);
        $req->setAnchorInfo($anchorInfo);
        $rsp = new \protobuf\MailSync\PBMailSearchListRsp();
        $url = self::TEST_DEV['nanrui'] . '/stormsFury/traceRead/mailTrackListUnopenAnchor';

        $this->process($url, $req, $rsp);
        $this->logInfo($req);
        $this->logInfo($rsp);
    }

    public function actionOriginList()
    {
        $this->setUserAccount('<EMAIL>');
        $rsp = new \protobuf\Customer\PBCustomerOriginListRsp();
        $url = self::TEST_DEV['gooray'] . '/stormsFury/customerRead/originList';

        $this->process($url, null, $rsp, false, false, 'gooray');
        $this->logInfo($rsp);
    }

    public function actionProductInfo()
    {
        $this->setUserAccount('<EMAIL>');

        $req = new \protobuf\Product\PBProductProfileInfoReq();
        $req->setProductId(**********);
        $rsp = new \protobuf\Product\PBProductProfileInfoRsp();
        $url = self::TEST_DEV['gooray'] . '/stormsFury/productRead/productProfileInfo';

        $this->process($url, $req, $rsp, false, false, 'gooray');
        $this->logInfo($rsp);
    }

    public function actionSubProductList()
    {
        $this->setUserAccount('<EMAIL>');

        $req = new \protobuf\Product\PBSubProductListReq();
        $req->setProductId(**********);
//        $req->setPageSize(2);
        $rsp = new \protobuf\Product\PBSubProductListRsp();
        $url = self::TEST_DEV['gooray'] . '/stormsFury/productRead/subProductList';

        $this->process($url, $req, $rsp, false, false, 'gooray');
        $this->logInfo($rsp);
    }

    // ./yiic-test stormFuryExample specificationList
    public function actionSpecificationList()
    {
        $this->setUserAccount('<EMAIL>');

        $req = new \protobuf\Product\PBProductSpecificationListReq();
        $req->setProductId(**********);
//        $req->setPageSize(2);
//        $req->setCurPage(1);
//        $headers = [];
//        $header = new \protobuf\Product\PBSkuTreeAttribute();
//        $item = new \protobuf\CRMCommon\PBFieldItem();
//        $item->setId(**********);
//        $header->setNode($item);
//        $childes = [];
//        $child = new \protobuf\Product\PBSkuTreeAttribute();
//        $item = new \protobuf\CRMCommon\PBFieldItem();
//        $item->setId(**********);
//        $child->setNode($item);
//        $childes[] = $child;
////        $child = new \protobuf\Product\PBSkuTreeAttribute();
////        $item = new \protobuf\CRMCommon\PBFieldItem();
////        $item->setId(**********);
////        $child->setNode($item);
////        $childes[] = $child;
//        $header->setChild($childes);
//        $headers[] = $header;
//        $req->setAttributeNodes($headers);
        $rsp = new \protobuf\Product\PBProductSpecificationListRsp();
        $url = self::TEST_DEV['condy'] . '/stormsFury/productRead/productSpecificationList';

        $this->process($url, $req, $rsp, false, false, 'gooray');
        $this->logInfo($rsp);
    }


    public function actionUserTabEnumList()
    {
        $this->setUserAccount('<EMAIL>');
        $req = new \protobuf\CRMCommon\PBPageLayoutEnumReq();
        $req->setType(1);
        $req->setReferId(**********);
        $rsp = new \protobuf\CRMCommon\PBPageLayoutEnumRsp();
        $url = self::TEST_DEV['gooray'] . '/stormsFury/pageLayoutRead/userTabEnumList';

        $this->process($url, $req, $rsp, false, false, 'gooray');
        $this->logInfo($rsp);
    }

    public function actionUnProcess()
    {
        $this->setUserAccount('<EMAIL>');

        $req = new \protobuf\CRMCommon\PBUserUnProcessReq();
        $req->setProcessType([\protobuf\CRMCommon\PBUserUnProcessType::UN_READ_FEED_COUNT]);
        $rsp = new \protobuf\CRMCommon\PBUserUnProcessRsp();
        $url = self::TEST_DEV['gooray'] . '/stormsFury/userRead/UnProcess';

        $this->process($url, $req, $rsp, false, false, 'gooray');
        $this->logInfo($rsp);
    }

    public function actionRemarkTypeList()
    {
        $this->setUserAccount('<EMAIL>');

        $url = self::TEST_DEV['gooray'] . '/stormsFury/customerRead/remarkTypeList';

        $rsp = new \protobuf\Customer\PBCustomerRemarkTypeListRsp();

        $this->process($url, null, $rsp, false, false, 'gooray');
        $this->logInfo($rsp);
    }


    // ./yiic-test stormfuryExample customerInfoList
    public function actionCustomerInfoList()
    {
        $this->setUserAccount('<EMAIL>');

        $req = new \protobuf\Customer\PBCustomerInfoListReq();
        $req->setCompanyId(**********);
        $url = self::TEST_DEV['gooray'] . '/stormsFury/customerRead/customerInfoList';

        $rsp = new \protobuf\Customer\PBCustomerInfoListRsp();

        $this->process($url, $req, $rsp, false, false, 'gooray');
        $this->logInfo($rsp);
    }

    public function actionQuickTemplateList()
    {
        $this->setUserAccount('<EMAIL>');

        $req = new \protobuf\Customer\PBQuickTemplateListReq();
        $req->setCurPage(1);
        $req->setPageSize(20);
        $rsp = new \protobuf\Customer\PBQuickTemplateListRsp();
        $url = self::TEST_DEV['gooray'] . '/stormsFury/customerSettingRead/quickTemplateList';

        $this->process($url, $req, $rsp, false, false, 'gooray');
        $this->logInfo($rsp);
    }

    // ./yiic-test stormFuryExample getTaskProgress
    public function actionGetTaskProgress()
    {
        $this->setUserAccount('<EMAIL>');
        $req = new \protobuf\OkkiAi\PBAiAgentGetTaskProgressReq();
        $req->setScene(6);
        $rsp = new \protobuf\OkkiAi\PBAiAgentGetTaskProgressRsp();
        $url = self::TEST_DEV['xyy'] . '/stormsFury/aiAgentRead/getTaskProgress';
        $this->process($url, $req, $rsp, false, false, 'yingyingxian');
        $this->logInfo($rsp);
    }

    // ./yiic-test stormFuryExample assetAnalysisList
    public function actionAssetAnalysisList()
    {
        $this->setUserAccount('<EMAIL>');
        $req = new \protobuf\OkkiAi\PBAnalysisListReq();
        $rsp = new \protobuf\OkkiAi\PBAnalysisListRsp();
        $req->setScene(6);
        $req->setTaskId(**********);

        $url =  'localhost:8888/api/stormsFury/aiAgentRead/assetAnalysisList';
        $this->process($url, $req, $rsp);
         $test = $rsp->serializeToJsonString();
        $this->logInfo($rsp, true, true);

    }

    // ./yiic-test stormFuryExample assetAnalysis
    public function actionAssetAnalysis()
    {
        $this->setUserAccount('<EMAIL>');
        $req = new PBAiAgentAssetAnalysisReq();
        $rsp = new PBAiAgentAssetAnalysisRsp();

        $echoParamsJson = '{"echo_params":[{"key":" common.visible","label":" 查看范围","value":" 所有范围"}]}';
        $paramsJson = '{"params":[{"type":" date","field":" common.date","value":{"end":" 2024-06-31","start":" 2024-01-01"}},{"type":"select_visible_user_id","field":"common.visible","value":["*********","*********","********","********","*********","********","*********","********","********","*********","*********","*********","*********","*********","*********","*********","********","*********","*********","*********","*********","*********","*********","*********","*********","*********","*********"]},{"type":"date","field":"common.date","value":{"end":"2024-06-26","start":"2023-12-26"}}]}';

        $echoParams = json_decode($echoParamsJson, true);
        $params = json_decode($paramsJson, true);

        $echoParamsList = $paramsList = [];
        $echoParamsListPB = new \protobuf\OkkiAi\PBEchoParamsList();
        foreach ($echoParams['echo_params'] as $item) {
            $echoParamsPB = new \protobuf\OkkiAi\PBEchoParamItem();
            $echoParamsPB->setParamItem($item);
            $echoParamsList[] = $echoParamsPB;
        }
        $echoParamsListPB->setParam($echoParamsList);


        $paramsListPB = new \protobuf\OkkiAi\PBParamsList();
        foreach ($params['params'] as $item) {
            $paramPB = new \protobuf\OkkiAi\PBParamItem();
            $paramPB->setType($item['type']);
            $paramPB->setField($item['field']);

//            $value = new \protobuf\OkkiAi\PBParamFieldValue();
            $keys = array_keys($item['value']);
            if (is_string($keys[0])) {
//                $obj = new PBParamFieldValueObj();
//                $obj->setValue($item['value']);
//                $value->setValueObj($obj);
//                $value->setValueObj($item['value']);
                $paramPB->setValueObj($item['value']);
            }else {
//                $arr = new PBaramFieldValueArr();
//                $arr->setValue($item['value']);
//                $value->setValueArr($arr);
//                $value->setValueArr($item['value']);
                $paramPB->setValueArr($item['value']);
            }
//            $paramPB->setValue($value);
            $paramsList[] = $paramPB;
        }
        $paramsListPB->setParam($paramsList);

        $req->setScene(6);
        $req->setParam($paramsList);
        $req->setEchoParams($echoParamsList);

//        $url = 'https://okkiai-iteration-3-6-1086078.story.dev.xiaoman.cn/stormsFury/aiAgentWrite/assetAnalysis';
//        $this->process($url, $req, $rsp, false, false);

        $url = self::TEST_DEV['1086078'] . '/stormsFury/aiAgentWrite/assetAnalysis';
        $this->process($url, $req, $rsp, false, false, 'yingyingxian');
        $this->logInfo($rsp, true, true);
    }

    public function actionSubordinateMailList()
    {
        $this->setUserAccount('<EMAIL>');

        $req = new PBMailSearchListReq();
        $req->setLimit(20);
        $req->setOffset(1);
        $req->setMailDetailFlag(true);
        $req->setGroupByUser(true);
        $req->setUserId(-1);

        $rsp = new PBMailSearchListRsp();
        $url = self::TEST_DEV['seven'] ."/api/stormsFury/mailRead/SubordinateMailList";
        $this->process($url, $req, $rsp,false);
        $this->logInfo($rsp);
    }

    // ./yiic-test stormFuryExample CompanyFileList
    public function actionCompanyFileList()
    {
        $this->setUserAccount('<EMAIL>');

        $req = new \protobuf\CRMCommon\PBReferFileListReq();
        $req->setReferType(\protobuf\CRMCommon\PBType::TYPE_COMPANY);
        $req->setReferId(**********);

        $rsp = new \protobuf\CRMCommon\PBReferFileListRsp();
        $url = self::TEST_DEV['gooray'] ."/stormsFury/customerRead/referFileList";
        $this->process($url, $req, $rsp,false, false, 'gooray');
        $this->logInfo($rsp);
    }

    // ./yiic-test stormFuryExample unbindCheck
    public function actionUnbindCheck()
    {
        $this->setUserAccount('<EMAIL>');

        $req = new \protobuf\DingDing\PBDingTalkUnBindCheckReq();
        $rsp = new \protobuf\DingDing\PBDingTalkUnBindCheckRsp();
        $url = self::TEST_DEV['gooray'] ."/stormsFury/dingDingRead/unbindCheck";
        $this->process($url, $req, $rsp,false);
        $this->logInfo($rsp);
    }

    public function actionApprovalDiffer()
    {
        $this->setUserAccount('<EMAIL>');

        $req = new \protobuf\Approval\PBApprovalDiffReq();
        $req->setApplyFormId(**********);
        $rsp = new \protobuf\Approval\PBApprovalDiffRsp();

        $url = self::TEST_DEV['tina'] ."/stormsFury/approvalReadV2/approvalDiffer";
        $this->process($url, $req, $rsp,false, false,'tina');
        $this->logInfo($rsp);
    }

    // ./yiic-test stormFuryExample opportunityFormFieldList
    public function actionOpportunityFormFieldList()
    {
        $this->setUserAccount('<EMAIL>');
        //$this->setUserAccount('<EMAIL>');
        $req = new \protobuf\Opportunity\PBOpportunityFormFieldListReq();
        $req->setOpportunityId(**********);
        $req->setScene(1);
        $rsp = new \protobuf\Opportunity\PBOpportunityFormFieldListRsp();
        $url = self::TEST_DEV['karonyang'] . "/api/stormsFury/OpportunityV2Read/FormFieldList";
        $this->process($url,$req, $rsp, false, false,'karonyang');
        $this->logInfo($rsp);
    }

    // ./yiic-test stormFuryExample submitOpportunity
    public function actionSubmitOpportunity()
    {
        $this->setUserAccount('<EMAIL>');
        $data = '{"opportunity":{"**********":"小满科技","**********":"2024-08-05","**********":123,"**********":"","company_id":**********,"name":"测试05-lxy","flow_id":"**********","main_lead_id":"","currency":"USD","exchange_rate":"123","customer_id":"","stage":"**********","main_user":"********","account_date":"2024-09-04","handler":"","origin_list":"14"},"product_list":[]}';
        $data = json_decode($data, true);
        $req = new PBSubmitOpportunityReq();
        $req->setData($data['opportunity']??[]);
        $req->setDepartment(8613);
        $rsp = new PBSubmitOpportunityRsp();
        $url = self::TEST_DEV['milktea'] . "/api/stormsFury/OpportunityV2Write/SubmitOpportunity";
        $this->process($url,$req, $rsp, false, false,'milktea');
        $this->logInfo($rsp);
    }

    // ./yiic-test stormFuryExample getOriginLeads
    public function actionGetOriginLeads()
    {
        $this->setUserAccount('<EMAIL>');
        $req = new \protobuf\Customer\PBOriginLeadsReq();
        $req->setCompanyId(**********);
        $rsp = new \protobuf\Customer\PBOriginLeadsRsp();
        $url = self::TEST_DEV['milktea'] . "/api/stormsFury/CustomerRead/getOriginLeads";
        $this->process($url,$req, $rsp, false, false,'milktea');
        $this->logInfo($rsp);
    }

    // ./yiic-test stormFuryExample secondRolesList
    public function actionSecondRolesList()
    {
        $this->setUserAccount('<EMAIL>');
        $req = new \protobuf\CRMCommon\PBSecondRolesListReq();
        $req->setModuleId('opportunity');
        $rsp = new \protobuf\CRMCommon\PBSecondRolesListRsp();
        $url = self::TEST_DEV['milktea'] . "/api/stormsFury/PrivilegeRead/secondRolesList";
        $this->process($url,$req, $rsp, false, false,'milktea');
        $this->logInfo($rsp);
    }

    // ./yiic-test stormFuryExample deleteOpportunity
    public function actionDeleteOpportunity()
    {
        $this->setUserAccount('<EMAIL>');
        $req = new \protobuf\Opportunity\PBDeleteOpportunityReq();
        $opportunity_id = [**********,**********];
        $req->setOpportunityId($opportunity_id);
        $rsp = new \protobuf\Opportunity\PBDeleteOpportunityRsp();
        $url = self::TEST_DEV['milktea'] . "/api/stormsFury/OpportunityV2Write/deleteOpportunity";
        $this->process($url,$req, $rsp, false, false,'milktea');
        $this->logInfo($rsp);
    }

    // ./yiic-test stormFuryExample getAuthScope
    public function actionGetAuthScope()
    {
        $rsp = new \protobuf\DingDing\PBDingTalkAuthScopeRsp();
        $url = self::TEST_DEV['gooray'] . "/api/stormsFury/dingDingRead/getAuthScope";
        $this->process($url, null, $rsp, false);
        $this->logInfo($rsp);
    }

    // ./yiic-test stormFuryExample SubmitStatistics
    public function actionSubmitStatistics()
    {
        $this->setUserAccount('<EMAIL>');
        $req = new PBWorkJournalSubmitStatisticsReq();
        $rsp = new PBWorkJournalSubmitStatisticsRsp();
        $req->setTemplateId(**********);
        $req->setStartTime(strtotime('2024-09-23') * 1000);
        $req->setEndTime(strtotime('2024-09-29') * 1000);
        $url = self::TEST_DEV['xyy'] . "/api/stormsFury/WorkJournalRead/SubmitStatistics";
        $this->process($url,$req, $rsp, false, false,'yingyingxian');
        $this->logInfo($rsp, printJson: true);
    }


    public function actionCompanyField()
    {
        $this->setUserAccount('<EMAIL>');

        $req = new \protobuf\Customer\PBCompanyFieldListReq();
        $req->setCompanyId(**********);

        $rsp = new \protobuf\Customer\PBCompanyFieldListRsp();
        $url = self::TEST_DEV['tina'] ."/api/stormsFury/customerRead/companyField";
        $this->process($url, $req, $rsp,false, false,'tina');
        $this->logInfo($rsp);
    }

    public function actionAppCompanyList()
    {
        $this->setUserAccount('<EMAIL>');

        $req = new \protobuf\services\customer\AppCompanyListReq();
        $req->setSortType(\protobuf\Report\PBSortType::SORT_DESC);
        $req->setSortField('order_time');
        $req->setPageSize(20);
        $req->setPageNo(1);
        $req->setShowAll(\protobuf\Customer\PBShowAll::SHOW_ALL);
        $req->setCompanyType(\protobuf\Customer\PBCompanyOwnerType::TYPE_PUBLIC_COMPANY);
        // 构建filters
        $filtersValue = ['swarm_id'=>**********];
        $filters = [];
        foreach ($filtersValue as $k => $v) {
            $filter = new \protobuf\Customer\PBFilterFieldInfo();
            $filter->setFieldId($k);

            $value = new \Google\Protobuf\Value();
            $value->mergeFromJsonString(json_encode($v));
            $filter->setValue($value);
            $filters[] = $filter;
        }
        $req->setFilters($filters);

        $rsp = new \protobuf\services\customer\AppCompanyListRsp();
        $url = self::TEST_DEV['tina'] ."/api/stormsFury/customerRead/appCompanyList";
        $this->process($url, $req, $rsp,false, false,'tina');
        $this->logInfo($rsp);
    }

    public function actionCustomerSwarmList()
    {
        $this->setUserAccount('<EMAIL>');

        $req = new \protobuf\Customer\PBCustomerSwarmReadListReq();
        $req->setUserId(********);
        $params = new \protobuf\Customer\PBSwarmReadParams();
        $params->setShowAll(0);
        $req->setParams($params);
        // $req->setSwarmType(\protobuf\Customer\PBSwarmType::TYPE_PUBLIC);

        $rsp = new \protobuf\Customer\PBCustomerSwarmReadListRsp();
        $url = self::TEST_DEV['tina'] ."/api/stormsFury/CustomerSwarmRead/List";
        $this->process($url, $req, $rsp,false, false,'tina');
        $this->logInfo($rsp);
    }

    // ./yiic-test stormfuryexample getallsameclientuser
    public function actionGetAllSameClientUser()
    {
        $url = self::TEST_DEV['lzy'] . "/api/stormsFury/WhatsappCloudRead/getAllSameClientUser";
        $this->setUserAccount("<EMAIL>");
        $rsp = new PBGetAllSameClientUserRsp();
        $this->process($url, null, $rsp);
        $this->logInfo($rsp);
    }

    public function actionTestSkuInfo(){
        $this->setUserAccount('<EMAIL>');

        $req = new \protobuf\Product\PBProductSkuInfoReq();
        $req->setSkuId(**********);
//        $sku_id = **********;
        $rsp = new \protobuf\Product\PBProductSkuInfoRsp();
        $url = self::TEST_DEV['condy'] ."/api/stormsFury/productRead/ProductSkuInfo";
        $this->process($url, $req, $rsp,false, false);
        $this->logInfo($rsp);
        print_r(json_decode($rsp->serializeToJsonString(), true));

    }

    public function actionTestTry()
    {
        $this->setUserAccount('<EMAIL>');

        $req = new \protobuf\Product\PBProductSkuUpdateDisableReq();
        $req->setSkuIds([**********]);
        $req->setDisableFlag(true);
//        $sku_id = **********;
        $rsp = new \protobuf\Product\PBProductSkuUpdateDisableRsp();
        $url = self::TEST_DEV['condy'] ."/api/stormsFury/productWrite/UpdateSkuDisableFlag";
        $this->process($url, $req, $rsp,false, false);
        $this->logInfo($rsp);
        print_r(json_decode($rsp->serializeToJsonString(), true));
    }


    /**
     * ./yiic-test stormfuryexample atAvailableUserList
     */
    public function actionAtAvailableUserList()
    {
        $url = self::TEST_DEV['gooray'] . "/api/stormsFury/atRead/availableUserList";
        $this->setUserAccount('<EMAIL>');
        $req = new \protobuf\At\PBAtAvailableUserListReq();
        $req->setObjType(4);
        $req->setObjId(**********);
        $rsp = new \protobuf\At\PBAtAvailableUserListRsp();
        $this->process($url, $req, $rsp, false, false, 'gooray');
        $this->logInfo($rsp);
    }

    /**
     * ./yiic-test stormfuryexample trailCommentInfo
     */
    public function actionTrailCommentInfo()
    {
        $url = self::TEST_DEV['gooray'] . "/api/stormsFury/customerRead/trailCommentInfo";
        $this->setUserAccount('<EMAIL>');
        $req = new \protobuf\Customer\PBTrailCommentReq();
        $req->setCommentId(**********);
        $rsp = new \protobuf\Customer\PBTrailCommentRsp();
        $this->process($url, $req, $rsp);
        $this->logInfo($rsp);
    }


    /**
     * ./yiic-test stormfuryexample atDefaultContent
     */
    public function actionAtDefaultContent()
    {
        $url = self::TEST_DEV['gooray'] . "/api/stormsFury/atRead/defaultContent";
        $this->setUserAccount('<EMAIL>');
        $req = new \protobuf\At\PBAtDefaultContentReq();
        $commentContent = new \protobuf\At\PBAtCommentContentParams();
        $commentContent->setCommentId(**********);
        $req->setCommentContent($commentContent);
        $rsp = new \protobuf\At\PBAtDefaultContentRsp();
        $this->process($url, $req, $rsp, false, false, 'gooray');
        $this->logInfo($rsp);
    }

    /**
     * ./yiic-test stormfuryexample addTrailComment
     */
    public function actionAddTrailComment()
    {
        $url = self::TEST_DEV['gooray'] . "/api/stormsFury/customerWrite/addTrailComment";

        $this->setUserAccount('<EMAIL>');
        $req = new \protobuf\Customer\PBTrailCommentReq();
        $req->setAtUserIds([*********,*********,*********]);
        $req->setTrailId(**********);
        $req->setContent('2222 @0822_empty  @*********  @0125empty_gg 2222');
        $rsp = new \protobuf\Customer\PBTrailCommentRsp();
        $this->process($url, $req, $rsp, false, false, 'gooray');
        $this->logInfo($rsp);
    }

    public function actionQuestionRecommend()
    {
        $this->setUserAccount('<EMAIL>');
        $req = new \protobuf\OkkiAi\PBQuestionRecommendReq();
        $req->setRecordId(**********);
        $rsp = new \protobuf\OkkiAi\PBQuestionRecommendRsp();
        $url =  "localhost:8888/api/stormsFury/aiAgentRead/questionRecommend";
        $this->process($url, $req, $rsp,false, false);
        $this->logInfo($rsp);
    }


    // ./yiic-test stormfuryexample businessCardRecognize
    public function actionBusinessCardRecognize()
    {
        $this->setUserAccount('<EMAIL>');

        $req = new \protobuf\services\customer\BusinessCardRecognizeReq();
        $file_data = file_get_contents("https://v4client.oss-cn-hangzhou.aliyuncs.com/other/img/********/b73d008df7ecc322a6a31b30da1acb990db8eb570f00c41af74793fc3f9999d8.jpg");
        $req->setData($file_data);

        $rsp = new \protobuf\Customer\PBBusinessCardRsp();
        $url = self::TEST_DEV['tina'] ."/api/stormsFury/customerRead/businessCardRecognize";
        $this->process($url, $req, $rsp,false, false,'tina');
        $this->logInfo($rsp);
    }

    // ./yiic-test stormfuryexample batchSaveBusinessCardDraft
    public function actionBatchSaveBusinessCardDraft()
    {
        $this->setUserAccount('<EMAIL>');

        $req = new \protobuf\services\customer\BatchSaveBusinessCardDraftReq();
        // $req->setBusinessCardIds([**********]);
        $req->setSaveType(\protobuf\Customer\PBBusinessCardSaveType::TYPE_SAVE_ALL);
        $url = self::TEST_DEV['tina'] ."/api/stormsFury/customerWrite/batchSaveBusinessCardDraft";
        $this->process($url, $req, $rsp,false, false,'tina');
    }

    // ./yiic-test stormfuryexample batchDeleteBusinessCardDraft
    public function actionBatchDeleteBusinessCardDraft()
    {
        $this->setUserAccount('<EMAIL>');

        $req = new \protobuf\services\customer\BatchDeleteBusinessCardDraftReq();
        $req->setBusinessCardIds([**********]);
        $url = self::TEST_DEV['tina'] ."/api/stormsFury/customerWrite/batchDeleteBusinessCardDraft";
        $this->process($url, $req, $rsp,false, false,'tina');
    }

    // ./yiic-test stormfuryexample batchVcardToDraft
    public function actionBatchVcardToDraft()
    {
        $this->setUserAccount('<EMAIL>');

        $req = new \protobuf\services\customer\BatchVcardToDraftReq();
        $vcard = new \protobuf\Customer\PBVcardInfo();
        $vcard->setVcard("BEGIN:VCARD\r\nVERSION:2.1\r\nNICKNAME;CHARSET=utf-8:Karen Nie\r\nTEL;CELL:+86***********\r\nTEL;CELL:***********\r\nTEL;WORK:+************\r\nTEL;WORK:**********\r\nTEL;WORK;FAX:+************\r\nX-IS-IM;OTHER:********\r\nEMAIL;WORK:<EMAIL>\r\nURL;WORK:www.twusa.cn\r\nADR;WORK;CHARSET=utf-8:;;Suite 1002, Bulldtng C, Chamtime Plaza. 2889 Jlnke Road, Pudong;Shanghai;;;China\r\nADR;WORK;CHARSET=utf-8:;;\346\265\246\344\270\234\346\226\260\345\214\272\345\274\240\346\261\237\351\253\230\347\247\221\351\207\221\347\247\221\350\267\2572889\345\217\267 \351\225\277\346\263\260\345\271\277\345\234\272\345\206\231\345\255\227\346\245\274 C \345\272\247 1002 \345\256\244;\344\270\212\346\265\267\345\270\202;;;\344\270\255\345\233\275\r\nN;CHARSET=utf-8:\350\201\202;\344\275\263\347\220\252;;;\r\nFN;CHARSET=utf-8:\350\201\202 \344\275\263\347\220\252\r\nORG;WORK;CHARSET=utf-8:\347\276\216\344\273\223\344\272\222\350\201\224\357\274\210\344\270\212\346\265\267\357\274\211\344\276\233\345\272\224\351\223\276\347\256\241\347\220\206\346\234\211\351\231\220\345\205\254\345\217\270;;\346\213\233\345\225\206\346\200\273\347\233\221\r\nTITLE;CHARSET=utf-8:\346\213\233\345\225\206\346\200\273\347\233\221\r\nORG;WORK;CHARSET=utf-8:TWUSA Supply Chain Management. LTD;;Bu* ln\302\267 m Development Dlnac\303\257or\r\nTITLE;CHARSET=utf-8:Bu* ln\302\267 m Development Dlnac\303\257or\r\nX-IS-ANGLE:0\r\nAUTHOR:IntSig_Android\r\nEND:VCARD\r\n");
        $vcard->setFileId(5113706885);
        $vcard->setFileUrl("https://v4client.oss-cn-hangzhou.aliyuncs.com/other/img/********/488dd2afb5661cf14c314309d7df03b2b31da887cf688018027c10d1564a926c.jpg");
        $req->setVcardList([$vcard]);
        $url = self::TEST_DEV['tina'] ."/api/stormsFury/customerWrite/batchVcardToDraft";
        $this->process($url, $req, $rsp,false, false,'tina');
    }
    /**
     * ./yiic-test stormfuryexample settings
     */
    public function actionSettings()
    {
        $url = self::TEST_DEV['tina'] . "/api/stormsFury/userRead/settings";
        $this->setUserAccount('<EMAIL>');

        $req = new \protobuf\services\user\SettingsReq();
        $req->setKey(['notification.app.no.disturb.setting', 'approvalflow.app.waitapproval.repeat.notify', 'notification.app.alibaba.setting', 'notification.app.push.setting', 'app.push']);
        $rsp = new \protobuf\services\user\SettingsRsp();
        $this->process($url, $req, $rsp, false, false, 'tina');
        $this->logInfo($rsp);
    }


    // ./yiic-test stormfuryexample businessCardAppendByFileId
    public function actionBusinessCardAppendByFileId()
    {
        $this->setUserAccount('<EMAIL>');

        // 识别正面
        $req = new \protobuf\services\customer\BusinessCardRecognizeReq();
        $file_data = file_get_contents("https://v4client.oss-cn-hangzhou.aliyuncs.com/other/img/********/b73d008df7ecc322a6a31b30da1acb990db8eb570f00c41af74793fc3f9999d8.jpg");
        $req->setData($file_data);

        $rsp = new \protobuf\Customer\PBBusinessCardRsp();
        $url = self::TEST_DEV['tina'] ."/api/stormsFury/customerRead/businessCardRecognize";
        $this->process($url, $req, $rsp,false, false,'tina');
        $this->logInfo($rsp);

        // 识别反面
        $req = new \protobuf\services\customer\BusinessCardAppendByFileIdReq();
        $req->setVcard('BEGIN:VCARD
VERSION:2.1
N;CHARSET=utf-8:黄;岳;;;
FN;CHARSET=utf-8:黄岳
TITLE;CHARSET=utf-8:高级研究员
ORG;CHARSET=utf-8:上海市涌东新区电子商务行业协会;复旦大学电子商务研究中心
TEL;CELL;CHARSET=utf-8:***********
TEL;CELL;CHARSET=utf-8:***********
TEL;WORK;CHARSET=utf-8:***********
TEL;WORK;CHARSET=utf-8:********
TEL;FAX;CHARSET=utf-8:***********
EMAIL;WORK;CHARSET=utf-8:<EMAIL>
URL;HOMEPAGE;CHARSET=utf-8:PH.Ilt.,t.P.,4&SgAWMSM&
ADR;WORK;CHARSET=utf-8:;;浦东新区上丰路977号 B 区7楼701 8室;上海市;;;中国
ADR;WORK;CHARSET=utf-8:;;国顺路670号李达三楼61 2室;上海市;;;中国
X-IS-ANGLE;CHARSET=utf-8:90
END:VCARD
');
        $req->setFileId(**********);
        $req->setFileUrl('http://bcr2.intsig.net/BCRService/BCR_VCF2');
        $req->setBusinessCardId($rsp->getBusinessCardId());
        $req->setCustomerFields($rsp->getCustomerFields());
        $req->setCompanyFields($rsp->getCompanyFields());
        $url = self::TEST_DEV['tina'] . "/api/stormsFury/customerWrite/businessCardAppendByFileId";
        $rsp = new \protobuf\Customer\PBBusinessCardRsp();
        $this->process($url, $req, $rsp, false, false, 'tina');
        $this->logInfo($rsp);
    }

    // ./yiic-test stormfuryexample businessCardAppendRecognize
    public function actionBusinessCardAppendRecognize()
    {
        $this->setUserAccount('<EMAIL>');

        // 识别正面
        $req = new \protobuf\services\customer\BusinessCardRecognizeReq();
        $file_data = file_get_contents("https://v4client.oss-cn-hangzhou-internal.aliyuncs.com/other/img/********/T0ZCMWNaTGx1aGpsbzgyTWgxUDN3ZWV4L09LQ1JIMkl5NVIzc1FhdWxvbz0=.jpg");
        $req->setData($file_data);

        $rsp = new \protobuf\Customer\PBBusinessCardRsp();
        $url = self::TEST_DEV['tina'] ."/api/stormsFury/customerRead/businessCardRecognize";
        $this->process($url, $req, $rsp,false, false,'tina');
        $this->logInfo($rsp);

        // 识别反面
        $req = new \protobuf\services\customer\BusinessCardAppendRecognizeReq();
        $file_data = file_get_contents("https://v4client.oss-cn-hangzhou.aliyuncs.com/other/img/********/YVkyMXNwN2ZwNGZ6K0xRc09PT3BZREV4RXo3YmhyTHg3ZlIwa3c1cGF6UT0=.jpg");
        $req->setData($file_data);
        $req->setBusinessCardId($rsp->getBusinessCardId());
        $req->setCustomerFields($rsp->getCustomerFields());
        $req->setCompanyFields($rsp->getCompanyFields());
        $url = self::TEST_DEV['tina'] . "/api/stormsFury/customerRead/businessCardAppendRecognize";
        $rsp = new \protobuf\Customer\PBBusinessCardRsp();
        $this->process($url, $req, $rsp, false, false, 'tina');
        $this->logInfo($rsp);
    }

    // ./yiic-test stormfuryexample businessCardResetBackImage
    public function actionBusinessCardResetBackImage() {
        $this->setUserAccount('<EMAIL>');

        // 重置正面
        $req = new \protobuf\services\customer\BusinessCardResetBackImageReq();
        $req->setBusinessCardId(**********);
        $customerFields = new \protobuf\Customer\PBCustomerFields();
        $customerFieldItem = new \protobuf\CRMCommon\PBFieldItem();
        $customerFieldItem->setId('name');
        $customerFieldItem->setValue('客户-测试正面回滚');
        $customerFields->setField([$customerFieldItem]);
        $req->setCustomerFields($customerFields);
        $companyFields = new \protobuf\Customer\PBCompanyFields();
        $companyFieldItem = new \protobuf\CRMCommon\PBFieldItem();
        $companyFieldItem->setId('name');
        $companyFieldItem->setValue('公司-测试正面回滚');
        $companyFields->setField([$companyFieldItem]);
        $req->setCompanyFields($companyFields);
        $url = self::TEST_DEV['tina'] . "/api/stormsFury/customerWrite/businessCardResetBackImage";
        $rsp = new \protobuf\Customer\PBBusinessCardRsp();
        $this->process($url, $req, $rsp, false, false, 'tina');
        $this->logInfo($rsp);
    }
}



