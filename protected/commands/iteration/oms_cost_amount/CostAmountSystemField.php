<?php

namespace common\commands\iteration\oms_cost_amount;

use common\library\object\field\field_type\FieldTypeFactory;
use common\library\object\field\updator\calculator\FieldCalculateTask;
use common\modules\prometheus\library\script\dispatcher\task\DispatchTypeClientTrait;
use common\modules\prometheus\library\script\dispatcher\task\DispatchTypeCustomTrait;
use common\modules\prometheus\library\script\dispatcher\task\IterationScriptTask;
use common\modules\prometheus\library\script\dispatcher\task\RepeatableExecuteTrait;

// debugphp -d 'xdebug.client_port=9004' /data/codebase/bob/php-crm/protected/yiic-test iterationScript developRun --folder=oms_cost_amount --taskClass=CostAmountSystemField
class CostAmountSystemField extends IterationScriptTask
{
    use DispatchTypeCustomTrait;
    use RepeatableExecuteTrait;

    // 更改 system_field表 的 金额小计 字段为公式字段
    public function execute()
    {
        $db = \Yii::app()->db;
        $extInfo = json_encode([
            "expression" => "{count}*{unit_price}+{other_cost}",
            "empty" => 0,
            "remark" => "",
            "decimal" => 4,
            "decimal_logic" => "floor"
        ]);

        $updateSql = 'update tbl_system_field set ext_info=:ext_info, field_type=11 where type=2 and id="cost_amount"';
        $result = $db->createCommand($updateSql)->execute([':ext_info'=>$extInfo]);
        echo $result;
        echo PHP_EOL;
    }
}