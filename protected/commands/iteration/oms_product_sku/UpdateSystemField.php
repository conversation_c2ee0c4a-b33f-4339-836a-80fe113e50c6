<?php

namespace common\commands\iteration\oms_product_sku;

use common\library\custom_field\Helper;
use common\library\oms\command\ProductFieldSetting;
use common\modules\prometheus\library\script\dispatcher\task\DispatchTypeClientTrait;
use common\modules\prometheus\library\script\dispatcher\task\DispatchTypeCustomTrait;
use common\modules\prometheus\library\script\dispatcher\task\IterationScriptTask;
use common\modules\prometheus\library\script\dispatcher\task\RepeatableExecuteTrait;

// debugphp -d 'xdebug.client_port=9004' /data/codebase/bob/php-crm/protected/yiic-test iterationScript developRun --folder=oms_product_sku --taskClass=UpdateSystemField
class UpdateSystemField extends IterationScriptTask
{
    use DispatchTypeCustomTrait;
    use RepeatableExecuteTrait;
    use ProductFieldSetting;

    public function execute()
    {
        $params = [
            ':fob_column'=>'["price_currency","fob_price","price_min","price_max","quantity","fob_type","gradient_price"]',
//            ':cost_column'=>'["cost_currency","cost","associate_cost_flag"]'
        ];

//        $sql = "update tbl_system_field set `columns` = case when id = 'fob' then :fob_column when id='cost_with_tax' then :cost_column else `columns` end where type=1 and id in ('fob', 'cost_with_tax')";
        $sql = "update tbl_system_field set `columns` = :fob_column where type=1 and id in ('fob')";
        $db = \Yii::app()->db;
        $affectRows = $db->createCommand($sql)->execute($params);
        \LogUtil::log('info', "UpdateSystemField update columns affectRows:" . $affectRows);
        $system_fields = UpdateCustomField::getUpdateFields();
        $system_fields = array_merge(self::productCartonFieldsSetting(),$system_fields);
        Helper::syncSystemFields(\Constants::TYPE_PRODUCT, $system_fields);

        // 强制修改部分字段的group
        $fieldGroupSql = "update tbl_system_field set group_id=0 where id in ('create_time','update_time','create_user','update_user','source_type','ali_store_id','product_type') and type=1";
        $rows = $db->createCommand($fieldGroupSql)->execute();
        echo $rows;

        // 变更字段顺序
        $module = \Constants::TYPE_PRODUCT;
        $sorter = new \common\library\util\sort\Sorter(new \common\library\util\sort\SystemFieldSortRepository($module,
            'order'));
        $sorter->setResort(true);
        $orderSettings = self::fieldOrderSetting();
        foreach($orderSettings as $after=>$before){
            $sorter->setId($after, true);
            $sorter->setSortField('order');
            $orderNum = $sorter->after($before);
            $sorter->setSortField('app_order');
            $appOrderNum = $sorter->after($before);
        }
    }

    public static function fieldOrderSetting(){
        return [
            "name"=> "product_no",
            "cn_name"=> "name",
            "images"=> "cn_name",
            "model"=> "images",
            "sku_attributes"=> "model",
            "group_id"=> "sku_attributes",
            "fob"=> "group_id",
            "cost_with_tax"=> "fob",
            "minimum_order_quantity"=> "cost_with_tax",
            "unit"=> "minimum_order_quantity",
            "description"=> "unit",
            "package_size"=> "description",
            "package_volume"=> "package_size",
            "package_gross_weight"=> "package_volume",
            "count_per_package"=> "package_gross_weight",
            "package_remark"=> "count_per_package",
            "product_size"=> "package_remark",
            "product_volume"=> "product_size",
            "category_ids"=> "product_volume",
            "hs_code"=> "category_ids",
            "info_json"=> "hs_code",
            "product_remark"=> "info_json",
            "from_url"=> "product_remark",
            "source_type"=> "ali_store_id",
            "create_user"=> "update_time",
            "create_time"=> "tax_refund_rate",
            "update_user"=> "vat_rate",
            "update_time"=> "create_time",
            "combine_info"=> "update_time",
            "create_type"=> "combine_info",
            "customs_cn_name"=> "create_type",
            "customs_name"=> "customs_cn_name",
            "cus_tag"=> "customs_name",
            "package_unit"=> "cus_tag",
            "product_net_weight"=> "package_unit",
            "tax_refund_rate"=> "product_net_weight",
            "vat_rate"=> "create_user",
            "product_type"=> "update_user",
            "ali_store_id"=> "product_type"
        ];
    }
}