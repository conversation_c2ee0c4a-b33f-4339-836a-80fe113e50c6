<?php

use common\library\account\service\DbService;
use common\library\account\UserList;
use common\library\ai_agent\agent\AiAgentList;
use common\library\ai_agent\AiAgentConstants;
use common\library\ai_agent\AiAgentFactory;
use common\library\ai_agent\api\AIClient;
use common\library\object\object_define\Constant as ObjConstant;
use common\library\ai_agent\DataAnalysisAiAgent;
use common\library\ai_agent\utils\IncompleteJsonFixer;
use common\library\exchange_rate\ExchangeRateService;
use common\library\oms\order_profit\OrderProfitFactorTriggerTrait;
use common\library\contact\ContactList;
use common\library\customer\Company;
use common\library\customer\CompanyList;
use common\library\edm\auto\JobRunner;
use common\library\email_identity\sync\ContactSync;
use common\library\email_identity\sync\CustomerSync;
use common\library\email_identity\sync\LeadSync;
use common\library\invoice\Order;
use common\library\invoice\status\InvoiceStatusService;
use common\library\mail\MailList;
use common\library\mail\service\CallbackService;
use common\library\privilege_v3\PrivilegeConstants;
use common\library\privilege_v3\PrivilegeService;
use common\library\purchase\purchase_order\PurchaseOrder;
use common\library\purchase\purchase_order\PurchaseOrderFilter;
use common\library\purchase\purchase_order_product\PurchaseOrderProductAPI;
use common\library\report\mail_unread\Report;
use common\library\server\es_search\OrderHandler;
use common\library\swoole\client\MailUnreadClient;
use common\library\todo\Feed;
use common\library\todo\TodoConstant;
use common\library\trail\events\EdmEvents;
use common\library\trail\TrailConstants;
use common\library\version\Constant;
use common\library\version\Helper;
use common\library\version\SystemModuleVersion;
use common\library\version\UserModuleVersion;
use common\library\workflow\trigger\RuleTrigger;
use common\library\workflow\WorkflowConstant;
use common\models\client\PerformanceV2Rule;
use common\library\api\InnerApi;
use common\library\ai\translate\TranslateService;
use common\library\push\AppPush;
use common\library\ai_agent\MailReplyAiAgent;
use \commom\library\ai_service\AiQualityCheckChatJourney;
use xiaoman\orm\database\data\In;

class WuliweiCommand extends \CrontabCommand
{


    private $clientId = 333608;
    private $viewUserId = *********;
    public function actionTestHolo()
    {


        $today = strtotime('today');
        $todayMidnight = strtotime('midnight', $today);

        $startDate = strtotime('-3 days', $todayMidnight);
        $endDate = $todayMidnight;

        $ret = $this->getErrorStatData($startDate, $endDate);
        \Util::batchLogInfo([
            "ret"=>$ret
        ]);
    }

    use OrderProfitFactorTriggerTrait;

    function getErrorStatData($startDateTime, $endDateTime)
    {
        $pgDB = DataWorkActiveRecord::getDbByClientId(9650);

        $sql = 'SELECT 
  error_code,
  DATE(create_time) AS time ,
  COUNT(*) AS value
FROM tbl_ai_service_process_record
WHERE  error_code > 0 and create_time > :create_time
GROUP BY time, error_code LIMIT 100';
        $startDate = date('Y-m-d', $startDateTime);
        $errorDataByDays = $pgDB->createCommand($sql)->queryAll(true, [":create_time"=>$startDate]);
//        \util::batchLogInfo(
//            [
//                "res"=>$errorDataByDays,
//            ]
//        );
        $errorCodeToDay = [];

        foreach ($errorDataByDays as $errorDataByDay)
        {
            $errorCode = $errorDataByDay['error_code'];
            $date = $errorDataByDay['time'];
            $value = $errorDataByDay['value'];
            $errorCodeToDay[$errorCode][$date] = $value;
        }

        \Util::batchLogInfo([
            "errorCodeToDay"=>$errorCodeToDay
        ]);

        $result = [];
        $currentDateTime = $startDateTime;
        while ($currentDateTime <= $endDateTime)
        {
            $date = date('Y-m-d', $currentDateTime);
            $result['date'][] = $date;
            $currentDateTime = strtotime('+1 day', $currentDateTime);
        }

        foreach ($errorCodeToDay as $errorCode => $dataByDay)
        {

            $currentDateTime = $startDateTime;
            while ($currentDateTime <= $endDateTime)
            {
                $date = date('Y-m-d', $currentDateTime);

                if (key_exists($date, $dataByDay)) {
                    $result[$errorCode][] = $dataByDay[$date];
                } else {
                    $result[$errorCode][] = 0;
                }
                $currentDateTime = strtotime('+1 day', $currentDateTime);
            }

        }

        return $result;
    }

    public function actionTestSls()
    {
        $today = strtotime('today');
        $todayMidnight = strtotime('midnight', $today);

        $startDate = strtotime ('-15 days', $todayMidnight);
        $endDate = $todayMidnight;

        $res = $this->getUserAndClientCountByDateRange($startDate, $endDate);
        \Util::batchLogInfo([
            "res"=>$res
        ]);
    }

    function getCacheKey($date)
    {
        $cacheKey = 'AIUserCount';
        return $cacheKey . $date;
    }
    function getUserAndClientCountByDateRange($startDate, $endDate)
    {
        $query = "action: aiagentchatcompletions | select  substr(_time_, 1, 10) as date, count(DISTINCT clientId) as clientCount, count(DISTINCT userId) as userCount from log group by date order by date";
        $cachePrefix = "UserAndClient";
        $logData =  $this->getSlsDataByDateRangeWithCache($startDate, $endDate, $query, $cachePrefix);
        $data = array_column($logData,'data');
        return [
            "date" => array_column($logData,'date'),
            "clientCount"=>array_column($data, "clientCount"),
            "userCount"=>array_column($data, "userCount"),
        ];
    }


    function getSlsDataByDateRangeWithCache($startDate, $endDate, $query, $cachePrefix)
    {
        $resultList = [];
        $currentDate = $startDate;

        $redis = \RedisService::cache();
        while ($currentDate <= $endDate)
        {
            $date = date('Y-m-d', $currentDate);

            $redisKey = $cachePrefix . $date;

            \Util::batchLogInfo([
                "current"=>date('Y-m-d H:i:s', $currentDate)
            ]);
//            $redis->del([$redisKey]);
            $valueFromRedis = $redis->get($redisKey);

            if (!empty($valueFromRedis))
            {
                \Util::batchLogInfo([
                    "hit cache"
                ]);
                $value = json_decode($valueFromRedis, true);
                $resultList[] = $value;
            }
            else
            {
                \Util::batchLogInfo([
                    "no hit cache"
                ]);

                $res = $this->getSlsCountByDate($date, $query);
                $resJson = json_encode($res);
                $redis->set($redisKey, $resJson);
                $resultList[] = $res;
            }
            $currentDate = strtotime('+1 day', $currentDate);
        }

        return $resultList;
    }

    function actionTestRedis()
    {
        $redis = \RedisService::cache();
        $cacheKey = 'AIUserCount';
        $tempCacheResult = $redis->zrange($cacheKey, 0, -1, 'WITHSCORES');
        foreach($tempCacheResult as $key=>$value)
        {
            \Util::batchLogInfo([
                "cache" => date('Y-m-d H:i:s', $value)
            ]);
            $cacheResult[$value] = $key;
        }
    }

    function getSlsCountByDate($date, $query)
    {
        $projects = \Yii::app()->params['aliyun_log']['projects'];
        $slsProject = $projects[0]['project_name'] ?? '';

        $s = new \common\library\log\SlsLog($slsProject, 'crm-service');

        $clientIdsStr = $this->generateClientStr();

        $dayBeginAndEnd = $this->getStartAndEndOfDay($date);

        $startTimestamp = strtotime($dayBeginAndEnd['startOfDay']);
        $endTimestamp = strtotime($dayBeginAndEnd['endOfDay']);

        $logs = $s->getLogs($startTimestamp, $endTimestamp, $query);


        foreach ($logs as $log) {
//            \Util::batchLogInfo([$log]);
            foreach ($log as $item) {
//                \Util::batchLogInfo(["k"=>"v", $item]);
                if ($item['date'] == $date) {
                    return [
                        'date'=>$date,
                        'data'=>$item
                    ];
                }
            }
        }
        return [
            'date'=>$date,
            'data'=>[]
        ];
    }

    function getStartAndEndOfDay($date) {
        $startOfDay = date('Y-m-d 00:00:00', strtotime($date));
        $endOfDay = date('Y-m-d 23:59:59', strtotime($date));

        return array(
            'startOfDay' => $startOfDay,
            'endOfDay' => $endOfDay
        );
    }


    function generateClientStr()
    {
        return "";
        $clientIds = $this->getInternalClientIds();
        \Util::batchLogInfo([
            'length clientIds' => count($clientIds)
        ]);

        $str = $this->convertIntArrayToString($clientIds);

        return $str;
    }

    /*
     * 目前线上client内部有2640个，外部108690个。所以用not in 内部的方式比较合适
     * */
    function getInternalClientIds()
    {
        $sql = "select client_id from tbl_client where client_type = :client_type";
        $db = \Yii::app()->account_base_db;
        $params = [":client_type" => \common\library\account\Client::CLIENT_TYPE_INTERNAL];
        $clientIds = $db->createCommand($sql)->queryColumn($params);

        return $clientIds;
    }

    function getClientIds()
    {
        $sql = "select client_id from tbl_client";
        $db = \Yii::app()->account_base_db;

        $clientIds = $db->createCommand($sql)->queryColumn();

        return $clientIds;
    }

    function actionTestFilterUser() {

    }

    function convertIntArrayToString($array) {
        if (empty($array)) {
            return "";
        }
        $stringArray = array_map(function($number) {
            return "not clientId: $number";
        }, $array);

        $result = implode(' ', $stringArray);

        return $result;
    }

    function actionTestWebsocket()
    {
        $rsp = new protobuf\OkkiAi\PBAiAgentChatCompletionsWebSocketRsp();
        $context = new protobuf\OkkiAi\PBAiMessageContext();
        $context->setContent("");

        $historyId = **********;
        $conversationId = **********;
        $rsp->setContext($context);
        $rsp->setStatus(0);
        $rsp->setHistoryId($historyId);
        $rsp->setConversationId($conversationId);

        $api = new InnerApi('push_proxy');
        $api->call('push', [
            'to' => [
                'clientId' => 9650,
                'userIds' => [11858405],
            ],
            'message' => [
                'event' => 'ai_stream',

                'pb_base64_data' => base64_encode($rsp->serializeToString())
            ],
            'subscribe' => ['scene' => 'okki_ai', 'channel' => 'ALL']
        ]);

        for ($i = 0; $i < 10; $i++) {
            sleep(1);
            $rsp->setStatus(1);
            $context = new protobuf\OkkiAi\PBAiMessageContext();
            $context->setContent("{$i}");
            $rsp->setContext($context);
            $api->call('push', [
                'to' => [
                    'clientId' => 9650,
                    'userIds' => [11858405],
                ],
                'message' => [
                    'event' => 'ai_stream',
                    'data' => [
                        'record_id' => 1008611,
                        'history_id' => 13800,
                        'status' => -1,
                        'content' => [
                            'text' => 'whatever'
                        ],
                    ],
                    'pb_base64_data' => base64_encode($rsp->serializeToString())
                ],
                'subscribe' => ['scene' => 'okki_ai', 'channel' => 'ALL']
            ]);
        }
        $rsp->setStatus(-1);

        $api->call('push', [
            'to' => [
                'clientId' => 6534,
                'userIds' => [55321916],
            ],
            'message' => [
                'event' => 'ai_stream',
                'data' => [
                    'record_id' => 1008611,
                    'history_id' => 13800,
                    'status' => -1,
                    'content' => [
                        'text' => 'whatever'
                    ],
                ],
                'pb_base64_data' => base64_encode($rsp->serializeToString())
            ],
            'subscribe' => ['scene' => 'okki_ai', 'channel' => 'ALL']
        ]);
    }

    function actionPushApp() {
        $clientId = 1;
        $userIds = [1];
        $event = "ai_stream";
        $scene = 'okki_ai';
        $historyId = **********;
        $conversationId = **********;
        $rsp = new protobuf\OkkiAi\PBAiAgentChatCompletionsWebSocketRsp();
        $context = new protobuf\OkkiAi\PBAiMessageContext();
        $context->setContent("");
        $rsp->setContext($context);
        $rsp->setStatus(0);
        $rsp->setHistoryId($historyId);
        $rsp->setConversationId($conversationId);
        AppPush::push($clientId, $userIds, $event, $scene, $rsp);
    }

    function actionTranslate() {
        $target = 'ru';
        $results = (new TranslateService())
            ->setSource('auto')
            ->setTarget($target)
            ->setContentList(["你好"])
            ->translate();
        \Util::batchLogInfo(["result" => $results]);
    }

    function actionIncompleteJson() {
        // 示例用法
        $incompleteJson = '{"k1":[123,2,1], "k2": {"kk2": ['; // 不完整的 JSON

        $completeJson = '{
    "总结分析": "根据邮件内容，这是一封订阅推送邮件，提供了客户流失预警的快照报表。报表显示采购金额下降客户数为0，采购频次下降客户数为2，疑似流失客户数为0。邮件结尾为版权信息。",
    "回复策略": [
        "策略一：感谢您订阅推送邮件并提供了客户流失预警的快照报表。我们注意到采购频次下降的客户数有2个，我们将立即与这些客户联系，了解他们的需求和关注点，并提供相应的解决方案。",
        "策略二：非常感谢您关注我们的业务，并提供了客户流失预警的快照报表。我们会仔细分析报表中的数据，并与您共同探讨如何提高采购频次和避免客户流失的策略。期待与您进一步合作。",
        "策略三：非常感谢您订阅推送邮件并提供了客户流失预警的快照报表。我们注意到采购金额下降的客户数为0，这是一个积极的信号。我们将继续努力与这些客户保持良好的合作关系，并提供更好的产品和服务。"
    ],
    "回复语言": "中文",
    "回复风格": "专业"
}';


        $fixer = new IncompleteJsonFixer($incompleteJson);
        $fixedJson = $fixer->fixIncompleteJson();

        echo "Fixed JSON: " . $fixedJson;
        $lastFixedJson = ''; // 保存上一次的返回结果
        for ($length = 1; $length <= strlen($completeJson); $length++) {
            $subString = substr($completeJson, 0, $length);
            $fixer = new IncompleteJsonFixer($subString);
            $fixedJson = $fixer->fixIncompleteJson();
            $isValidJson = json_decode($fixedJson) !== null;
            // 如果修复后的 JSON 无效，则使用上一次的返回结果
            if (!$isValidJson) {
                $fixedJson = $lastFixedJson;
            } else {
                $lastFixedJson = $fixedJson; // 保存当前修复后的 JSON 作为上一次的返回结果
            }
            // 输出修复后的 JSON
            // echo "Fixed JSON: $fixedJson\n";
            $res = json_decode($fixedJson, true);
//            $jsonStr = json_encode($res,JSON_UNESCAPED_UNICODE);
            $jsonStr = json_encode($res,JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES | JSON_PRETTY_PRINT);

            print_r([
                "sub" => $subString,
                "fixJson" => $fixedJson,
                "jsonStr" => $jsonStr
            ]);


        }
    }

    // ./yiic-test Wuliwei FixUserStat --clientId=533 --userId=18265503
    public function actionFixUserStat($clientId = null, $userId = null, $dryRun = true)
    {
        if (!$userId) $userId = 18265503;
        if (!$clientId) $clientId = 533;

        $db = \ProjectActiveRecord::getDbByClientId($clientId);

        $userMailList = new UserMailList($clientId);
        $userMailList->setUserId($userId);
        $list = $userMailList->find();
        $userMailIds = array_column($list,'user_mail_id');

        $userMailIdsStr = implode(',', $userMailIds);
        $mailCountFromTblMailSQL = "SELECT DATE(receive_time) AS day, COUNT(*) as send_mail_count FROM tbl_mail WHERE user_mail_id in ({$userMailIdsStr}) and client_id={$clientId} AND receive_time >= '2024-04-01 00:00:00' AND receive_time <'2024-05-01 00:00:00' AND mail_type =2 group by day order by day   limit 100 offset 0;";
        var_dump($mailCountFromTblMailSQL);
        $mailStat = $db->createCommand($mailCountFromTblMailSQL)->queryAll();
        var_dump($mailStat);

        foreach ($mailStat as $mailStatByDay) {
            $date = $mailStatByDay['day'];
            $sendMailCount = $mailStatByDay['send_mail_count'];
            $updateSql = "update tbl_user_statistics_day set send_mail_count = {$sendMailCount} where client_id = {$clientId} and user_id in ({$userId}) and date = '{$date}'";
            $affectRow = 0;
            if (!$dryRun) {
                $affectRow = $db->createCommand($updateSql)->execute();
                print("excute " . $updateSql);
            }
            print_r([$updateSql, $affectRow]);
        }

    }

    function  actionGetCompanyById() {
        User::setLoginUserById(11858405);
        $company_id = 3197658876;
        $clientId = 9650;
        $company = new \common\library\customer\Company($clientId, $company_id);
        $data = $company->getAttributes();
        \Util::batchLogInfo(["data" => $data['name']]);
    }

    function actionGetRegex()
    {
        $QueryList = [
            '客户jolie0206客户1的今年累计成交金额是多少？',
            '客户jolie0206客户1的平均订单金额是多少？',
            '客户jolie0206客户1的今年赢单金额是多少？',
            '客户jolie0206客户1的平均赢单商机金额是多少？'
        ];

        foreach ($QueryList as $query) {
            print_r(["value" => $this->matchRegex($query)]);
        }
    }

    function matchRegex($query) {
        $regexMatches = [
            "/客户(.+?)的今年累计成交金额是多少？/" => "今年累计成交金额",
            "/客户(.+?)的平均订单金额是多少？/" => "平均订单金额",
            "/客户(.+?)的今年赢单金额是多少？/" => "今年赢单金额",
            "/客户(.+?)的平均赢单商机金额是多少？/" => "平均赢单商机金额"
        ];

        foreach ($regexMatches as $expression => $value) {
            if (preg_match($expression, $query, $matches)) {
                return $value;
            }
        }

        return null;
    }

    public function actionBuildRsp()
    {
        $cases = [
            '{
    "role": 1,
    "conversation_id": 3513605590,
    "message_type": 2,
    "record_id": 3514630009,
    "status": 0,
    "history_id": 3514630011,
    "context": {
        "title": [

        ],
        "subtitle": [

        ],
        "content": "",
        "header_left": [

        ],
        "header_right": [

        ],
        "footer_left": [
            {
                "type": "button",
                "event": "okki_ai_gpt_copy",
                "color": "",
                "icon": "copy",
                "text": "",
                "tips": "",
                "params": {
                    "content": ""
                },
                "history": true
            }
        ],
        "footer_right": [
            {
                "type": "text",
                "icon": "",
                "text": "内容由AI生成",
                "class": "text-medium-6 text-xs !leading-5"
            },
            {
                "type": "feedback",
                "event": "okki_ai_feedback",
                "icon": "feedback",
                "text": "",
                "params": {
                    "record_id": 3514630009
                }
            }
        ],
        "extra": [

        ]
    }
}',
'{
    "role": 1,
    "conversation_id": 3513605590,
    "message_type": 7,
    "record_id": 3514630018,
    "status": 0,
    "history_id": 3514630020,
    "context": {
        "title": {
            "type": "text",
            "icon": "",
            "text": "本周每个员工的新建客户数",
            "class": ""
        },
        "subtitle": {
            "type": "text",
            "icon": "",
            "text": "提问时间：2024-04-25 19:47",
            "class": ""
        },
        "content": [
            {
                "员工": "pro-jolieTest",
                "新建客户数": 1,
                "primary_key": 3514566708,
                "report_item_unique_key": "ddafbd5bc8ba98a5c870f4d585103c25",
                "refer_list": "companyList"
            }
        ],
        "header_left": [

        ],
        "header_right": [
            {
                "type": "select",
                "value": "bar",
                "params": [
                    {
                        "label": "饼图",
                        "value": "pie"
                    },
                    {
                        "label": "条形图",
                        "value": "horizontal-bar"
                    },
                    {
                        "label": "折线图",
                        "value": "line"
                    },
                    {
                        "label": "柱状图",
                        "value": "bar"
                    },
                    {
                        "label": "表格",
                        "value": "table"
                    }
                ]
            }
        ],
        "footer_left": [
            {
                "type": "button",
                "event": "okki_ai_download_echarts_excel",
                "color": "",
                "icon": "Download",
                "text": "",
                "tips": "下载Excel",
                "params": {
                    "content": ""
                },
                "history": true
            },
            {
                "type": "button",
                "event": "okki_ai_download_echarts_image",
                "color": "var(--okki-medium-8)",
                "icon": "Picture",
                "text": "下载图片",
                "tips": "",
                "params": {
                    "content": "本周每个员工的新建客户数"
                },
                "history": true
            }
        ],
        "footer_right": [
            {
                "type": "text",
                "icon": "",
                "text": "内容由AI生成",
                "class": "text-medium-6 text-xs"
            },
            {
                "type": "feedback",
                "event": "feedback",
                "icon": "feedback",
                "text": "",
                "params": {
                    "record_id": 3514630018
                }
            }
        ],
        "config": {
            "code": 0,
            "desc": "本周每个员工的新建客户数",
            "chatFlag": true,
            "charType": [
                {
                    "label": "饼图",
                    "value": "pie"
                },
                {
                    "label": "条形图",
                    "value": "horizontal-bar"
                },
                {
                    "label": "折线图",
                    "value": "line"
                },
                {
                    "label": "柱状图",
                    "value": "bar"
                },
                {
                    "label": "表格",
                    "value": "table"
                }
            ],
            "XAxis": [
                {
                    "field": "员工",
                    "name": "员工",
                    "currency": "",
                    "high_light": false
                }
            ],
            "YAxis": [
                {
                    "field": "新建客户数",
                    "name": "Total",
                    "currency": "",
                    "high_light": true
                }
            ],
            "series": [

            ]
        }
    }
}
'
        ];

        foreach ($cases as $case) {
            $data = json_decode($case, true);
            $rsp = \common\library\ai_agent\Helper::buildPBAiAgentChatCompletionsWebSocketRsp($data);
            print_r([
                "rsp" => $rsp->serializeToJsonString()
            ]);
        }

    }

    public function actionTest()
    {
//        $string = '{"DSL":{"desc":"查询今年和去年每个国家的新建客户数","from":{"tables":[{"name":"tbl_company"}]},"fields":[{"field":"tbl_company.country","alias":"国家"},{"field":"tbl_company.create_time","function":"TO_CHAR","params":["YYYY"],"alias":"时间"},{"field":"tbl_company.company_id","function":"count","distinct":true,"alias":"新建客户数"}],"where":[{"operator":">=","field":"tbl_company.create_time","value":"2022-01-01 00:00:00"},{"operator":"<=","field":"tbl_company.create_time","value":"2023-12-30 10:12:00"}],"group_by":["tbl_company.country","时间"],"order_by":{"国家":"asc","时间":"asc"},"limit":1000}}';
//
//        $dsl = json_decode($string, true);
//        $dsl = $dsl['DSL'];
        $companyId = 1;
        $clientId = 9650;
        $endOrderStatuNames = [1234];
        $filterStatusNames = ['已作废'];
        $invoiceStatusService = new InvoiceStatusService($clientId, \Constants::TYPE_ORDER);
        $endOrderStatus = $invoiceStatusService->filterEndOrderStatus($clientId, $filterStatusNames);
        $endOrderStatuNames = ['已作废'];
        $dsl =  [
            'desc' => '平均订单金额',
            'from' => [
                'tables' => [['name' => 'tbl_order']],
            ],
            'fields' => [
                [
                    'function' => 'avg',
                    'field' => 'tbl_order.amount',
                ],
            ],
            'where' => [
                [
                    "operator" => "=",
                    "field" => "tbl_order.company_id",
                    "value" => $companyId,
                ],
                [
                    "operator" => '=',
                    'field' => 'tbl_order.status',
                    "value" => $endOrderStatuNames
                ]
            ],
        ];

        \User::setLoginUserById(11858405);

        $sql = (new \common\library\ai_agent\utils\DslSqlBuilder('9650', '11858405', $dsl))->buildSql();
        $generateAiAgent = new \common\library\ai_agent\GenerateDataAiAgent('9650', '11858405');

        // 分析dsl
        $analysisInfo = $generateAiAgent->analysisDsl($dsl);
        if (($analysisInfo['code'] ?? -1) != 0) {
            throw new AiAgentException("analysis dsl error !", ($analysisInfo['code'] ?? -1));
        }

        $result = $generateAiAgent->execDslSql('9650', '11858405', $dsl, $sql, $analysisInfo);
        var_dump($result);
    }

    public function actionReport($clientId=0, $userId=0)
    {
        if (empty($clientId)) {
            $clientId = 9650;
        }

        if (empty($userId)) {
            $userId = 11858405;
        }

        $key = 'xs5';
        $paramsJson = '[
    "{\"field\":\"common.visible\",\"type\":\"select_visible_user_id\",\"value\":\"\"}",
    "{\"field\":\"common.date\",\"type\":\"date\",\"value\":{\"start\":\"2024-03-01\",\"end\":\"2024-04-28\"}}"
]';
        $params = json_decode($paramsJson);
        print_r($params);
//
//        $params = [
//            [
//                'field' => 'common.visible',
//                'type' => 'select_visible_user_id',
//                'value' => ''
//            ],
//            [
//                'field' => 'common.date',
//                'type' => 'date',
//                'value' => '{"start":"2024-04-01","end":"2024-04-28"}'
//            ]
//        ];

        $extendParams = [];
        $refresh = 1;
        $show_detail = 0;
        $show_data = 1;
        User::setLoginUserById($userId);
        $start = microtime(true);
        $report = new \common\library\statistics\render\report\Report($clientId, $key, $params, $extendParams);
        $report->setViewUserId($userId);
        $report->setForceRefresh($refresh);
        $report->setUniqueKey('');
        $report->setShowDetail($show_detail);
        $report->setShowData($show_data);
        $data = $report->format();
        $end = microtime(true);
        $executionTime = $end - $start;
        echo "执行花费的时间： " . $executionTime . " 秒";
        print_r([
            'data' => count($data)
        ]);

    }

    public function actionOpenCustomerSwitch($switch = 'off')
    {
        $redis = \RedisService::getInstance('redis');
        $redisKey = 'ai-report-switch';
        $status = $redis->get($redisKey);

        $redis->set($redisKey, $switch);
    }

    public function actionCheckCustomerSwitch()
    {
        $res = \common\library\ai_agent\Helper::isOpenAiReportSwitch();
        print_r([
            'res' => $res
        ]);
    }
    public function actionTestModel() {

        $list = new \common\library\ai_service\AiQualityCheckCompanyList(\User::getLoginUser());
        $res = $list->find();
        \Util::batchLogInfo(['data' => $res]);
        $count = $list->count();
        \Util::batchLogInfo(['data' => $count]);
    }
    public function actionGetConversationHistory() {
        User::setLoginUserById(*********);
        $pageSize = 100;
        $clientId = $this->clientId;
        $messageListObj = new \common\library\sns\customer\UserCustomerContactMessageList($this->clientId);
        $messageListObj->setLimit($pageSize);
        // todo: userContactId怎么看
        $userContactId = **********;
        $scene = 'drawer';

        $messageListObj->setUserContactId($userContactId);
        $messageListObj->getFormatter()->setShowSnsInfo(true);

        if (!empty($start_time)) {
            $messageListObj->setStartTime($start_time);
        }

        if (!empty($end_time)) {
            $messageListObj->setEndTime($end_time);
        }

        if ($scene == 'drawer' && empty($scroll_id)) {
            $order = ['asc','desc'];
            $messageListObj->setOrderBy(['send_time','id']);
            $messageListObj->setOrder($order);
        }

        $messageList = $messageListObj->find();

        $formattedMessageList = $this->getFormattedMessages($messageList);
        print($formattedMessageList);

    }
    function getFormattedMessages($messageList) {
        $formattedMessages = '';

        foreach ($messageList as $message) {
            // 根据send_type决定是Customer还是Salesman
            $senderType = $message['send_type'] == 1 ? 'Customer' : 'Salesman';

            // 组装输出的字符串
            $formattedMessages .= $message['send_time'] . ' ' . $senderType . ':  ' . $message['body'] . PHP_EOL;
        }

        return $formattedMessages;
    }

    public function actionCompanyQualityCheck() {
        $userId = *********;
        User::setLoginUserById(*********);
        $aiAgent = AiAgentFactory::createAgent(AiAgent::AI_AGENT_SCENE_TYPE_COMPANY_QUALITY_CHECK, $this->clientId, $userId);
        $question = '';
        $processRecord = $aiAgent->saveProcessRecord($question);

        $context = [
            'question_history_id' => \PgActiveRecord::produceAutoIncrementId(),
            'answer_history_id' => \PgActiveRecord::produceAutoIncrementId(),
            'record_id' => $processRecord->record_id
        ];

        $aiAgent->setContext($context);


        $params = [
            'params' => [
                'user_contact_id' => **********,
                'company_id' => **********,
            ]
        ];

        $aiAgent->process($params);

    }

    public function actionFeed() {
        $userId = *********;
        $clientId = 333383;
        User::setLoginUserById($userId);


    }

    public function actionTestDW()
    {
        $clientId = 333383;
        $this->clientId=333383;
        $userIds = \common\library\account\Helper::getUserIds($clientId);


        foreach ($userIds as $userId) {
            \User::setLoginUserById($userId);
            $user = \User::getLoginUser();
            $client_id = $user->getClientId();
            //有客户查看权限 & 角色可见范围大于本人
            $privilegeService = PrivilegeService::getInstance($client_id, $user->getUserId());
            if($privilegeService->getPrivilegeScope(PrivilegeConstants::PRIVILEGE_CRM_COMPANY_VIEW) <= PrivilegeConstants::PRIVILEGE_SCOPE_OWNER){
                print("角色范围小于本人");
                continue;
            }

            $feedPdo = new \common\library\todo\Feed(11, 1102);
            $feedPdo->setClientId($clientId);
            $feedPdo->setUserId($userId);
            $visibleUserIds = \common\library\ai_agent\company_quality_check\Helper::getCompanyVisibleUserIds($this->clientId, $this->viewUserId, []);
            $visibleUserIds[] = 0;
            $visibleUserIdsStr = implode(',', $visibleUserIds);
            $limit = 10;

            $companyAlias = 'B';
            $companyList = new CompanyList($this->viewUserId);
            $companyList->setAlias($companyAlias);
            $visibleUserIds = \common\library\ai_agent\company_quality_check\Helper::getCompanyVisibleUserIds($this->clientId, $this->viewUserId, $this->followUserId ?? []);
            $companyList->setUserId($visibleUserIds);
            list($companyWhere, $companyParams) = $companyList->buildParams();

            print_r(['companyWhere' => $companyWhere, 'companyParams' => $companyParams]);

            $statisticsTokenSql = "select company_id, order_time, mail_time, greatest_time, user_id,
       CASE WHEN ongoing_opportunity_count > 0 AND greatest_time > current_timestamp - interval '30 days' THEN 'L00_有进行中的商机且近30天有联系'
            WHEN ongoing_opportunity_count > 0 AND greatest_time > current_timestamp - interval '180 days' THEN 'L01_有进行中的商机且近180天有联系'
            WHEN performance_order_count > 0 AND greatest_time > current_timestamp - interval '30 days' THEN 'L02_有历史成单且近30天有联系'
            WHEN performance_order_count > 0 AND greatest_time > current_timestamp - interval '180 days'  THEN 'L03_有历史赢单且近180天有联系'
            WHEN success_opportunity_count > 0 AND greatest_time > current_timestamp - interval '30 days' THEN 'L04_有历史商机赢单且近30天有联系'
            WHEN success_opportunity_count > 0 AND greatest_time > current_timestamp - interval '180 days' THEN 'L05_有历史商机赢单且近180天有联系'
            WHEN greatest_time > current_timestamp - interval '30 days' AND array_length(user_id, 1)  > 0 THEN 'L06_最近30天有联系且在私海'
            WHEN greatest_time > current_timestamp - interval '180 days' AND array_length(user_id, 1) > 0 THEN 'L07_最近180天有联系且在私海'
            WHEN greatest_time > current_timestamp - interval '180 days' AND array_length(user_id, 1) = 0 THEN 'L08_其他近180天有联系且不在私海中'
            ELSE '其他'
           END AS buyer_type,
       transaction_order_amount + success_opportunity_amount_cny + 100 * ongoing_opportunity_count + COALESCE(array_length(user_id, 1), 0) * 10 - EXTRACT(DAY FROM AGE(NOW(), greatest_time)) / 30 AS rank_score

from (select *, GREATEST(order_time, mail_time) as greatest_time

      from tbl_company
      where client_id={$clientId} and user_id && ARRAY[$visibleUserIdsStr]::bigint[]
     ) as company
order by buyer_type, rank_score desc
limit {$limit}";
            try {
                print($statisticsTokenSql);
                $dataWorkDb = DataWorkActiveRecord::getDbByClientId(1);
                $companyList = $dataWorkDb->createCommand($statisticsTokenSql)->queryAll();
                \Util::batchLogInfo([
                    '$dataWorkClientSceneCostInfo' => $companyList
                ]);
            } catch (Exception $e) {
                self::info("clientId[$clientId] 统计token异常 异常信息是[{$e->getMessage()}]");
            }
            $companyIdList = array_column($companyList, 'company_id');
            print_r($companyIdList);
            $res = $feedPdo->pushFeed($companyIdList);
            var_dump($res);
        }
    }

    public function actionExSetting($clientId)
    {
        $client = \common\library\account\Client::getClient($clientId);
        $client->setExtentAttributes([  \common\library\account\Client::EXTERNAL_KEY_AI_COMPANY_QC => '1']);
        $client->saveExtentAttributes();
    }

    public function actionClearFeed() {
        $userId = *********;
        User::setLoginUserById($userId);


        $feedPdo = new \common\library\todo\Feed(\common\library\todo\TodoConstant::OBJECT_TYPE_AI_COMPANY_INSIGHTS, \common\library\todo\TodoConstant::TODO_TYPE_COMPANY_INSIGHTS_DYNAMIC);

        $feedIds = [**********];

        $feedPdo->updateFeedStatusByFeedId(TodoConstant::FEED_STATUS_I_KNOW,$feedIds);
    }

    public function actionClearQC($clientIds=''){
        if (empty($clientIds)) {
            $clientIds = [
                33205,
                72718,
                76012,
                83025];
        } else {
            $clientIds = [$clientIds];
        }

        var_dump($clientIds);

        foreach ($clientIds as $clientId) {
            $privilegeService = PrivilegeService::getInstance($clientId);
            $adminUserId = $privilegeService->getAdminUserId();


            $userIds = \common\library\account\Helper::getUserIds($clientId);
            $userIds = [$adminUserId];
            foreach($userIds as $userIdItem) {
                User::setLoginUserById($userIdItem);

//                $feedPdo = new \common\library\todo\Feed(\common\library\todo\TodoConstant::OBJECT_TYPE_AI_COMPANY_INSIGHTS, \common\library\todo\TodoConstant::TODO_TYPE_COMPANY_INSIGHTS_DYNAMIC);
//                $feedListData = $feedPdo->readFeed(100, 0);
//                $feedList = $feedListData['feed_list'] ?? [];
//                $feedIds = array_column($feedList, 'feed_id');
//
//                print_r([\common\library\todo\TodoConstant::TODO_TYPE_COMPANY_INSIGHTS_DYNAMIC, $feedIds]);
//                $feedPdo->updateFeedStatusByFeedId(TodoConstant::FEED_STATUS_DELETE,$feedIds);

                $feedPdo = new \common\library\todo\Feed(\common\library\todo\TodoConstant::OBJECT_TYPE_AI_COMPANY_INSIGHTS, \common\library\todo\TodoConstant::TODO_TYPE_COMPANY_INSIGHTS_RECOMMEND);
                $feedListData = $feedPdo->readFeed(100, 0);
                $feedList = $feedListData['feed_list'] ?? [];

                $feedIds = array_column($feedList, 'feed_id');

                print_r([\common\library\todo\TodoConstant::TODO_TYPE_COMPANY_INSIGHTS_RECOMMEND, $feedIds]);
                $feedPdo->updateFeedStatusByFeedId(TodoConstant::FEED_STATUS_DELETE,$feedIds);

            }
            User::setLoginUserById($adminUserId);

            $pg = \PgActiveRecord::getDbByClientId($clientId);
            $deleteQcJourneySql = "delete from tbl_ai_quality_check_chat_journey where client_id={$clientId};";

            $deleteQcCompanySql = "delete from tbl_ai_quality_check_company where client_id={$clientId};";


            // 删qccompany
            $pg->createCommand($deleteQcCompanySql)->execute();
            // 删journey
            $pg->createCommand($deleteQcJourneySql)->execute();
        }

    }

    function actionWorkRule() {
        $userId = 11858713;
        $ruleId = 3610798288;

        User::setLoginUserById($userId);
        $user = User::getLoginUser();
        $clientId = $user->getClientId();

        $rule = new \common\library\workflow\WorkflowRule($clientId, $ruleId);
        $trigger = new RuleTrigger($rule->client_id, $rule->getAttributes());
        $trigger->run(0, 1000, false);
    }

    function actionAliOrder() {
        $clientId = 14367;
        $userId = 11859131;
        $storeId = *********;
        $alibabaOrderId =229120335501028893;
        $orderId = 3255516843;
        $params = [
            'clientId' => $clientId,
            'userId' => $userId,
            'storeId' => $storeId,
            'alibabaOrderId' => $alibabaOrderId,
            'orderId' => $orderId,
        ];

        User::setLoginUserById($userId, $clientId);
        $this->info("订单同步开始 {$clientId} {$alibabaOrderId}");


        $result =  \common\library\alibaba\order\AlibabaOrderService::syncSingleOrder($clientId, $userId, $storeId,$alibabaOrderId, $orderId);

        $this->info("订单同步完成 {$clientId} {$orderId} result:".json_encode($result));


    }

    function actionOrderProfit()
    {
        $clientId = 14367;
        $orderId = 1634794965;
        $jobType = \common\library\oms\order_profit\Constant::FACTOR_TYPE_OF_CASH_COLLECTION_AMOUNT;
        $this->referOrderProfitFactor($clientId, $orderId, $jobType, true);
    }

    //  ./yiic-test Wuliwei productRecord --all=1
    function actionProductRecord($debug=1)
    {
        if ($debug) {
            $clientIds = ['14119'];
        } else {
            $clientList = $this->getClientList();
            $clientIds = empty($clientList) ? [] : array_keys(ArrayUtil::index($clientList, 'client_id'));
        }

        $minId = 0;

        foreach ($clientIds as $clientId) {
            $pgDB = \PgActiveRecord::getDbByClientId($clientId);
            // tbl_invoice_product_record


            // 捞出来所有订单
            $orderSql = "select tbl_order.order_id, 
       tbl_order.exchange_rate, 
       tbl_order.exchange_rate_usd, 
       tbl_invoice_product_record.gross_margin,
       tbl_invoice_product_record.id
from tbl_order 
         join tbl_invoice_product_record on tbl_order.order_id = tbl_invoice_product_record.refer_id
         where tbl_order.client_id=:client_id 
           and tbl_order.enable_flag=1  
           and tbl_invoice_product_record.id > :min_id
           and tbl_invoice_product_record.gross_margin >  9
         and tbl_invoice_product_record.gross_margin_cny = 0
         and tbl_invoice_product_record.gross_margin_usd = 0
         order by tbl_invoice_product_record.id";

            $orderSql .= " limit 5000";

            while (true) {
                $orderParams = [
                    ':client_id' => $clientId,
                    ':min_id' => $minId,
                ];

                $orderJoinRecords = $pgDB->createCommand($orderSql)->queryAll(true, $orderParams);
                if (count($orderJoinRecords)) {
                    $minId = $orderJoinRecords[count($orderJoinRecords) - 1]['id'];
                    if (empty($minId)) {
                        break;
                    }
                } else {
                    break;
                }


                $orderJoinRecordsChunks = array_chunk($orderJoinRecords, 200);
                foreach ($orderJoinRecordsChunks as $joinRecordsChunk) {
                    $updateSql =  "update tbl_invoice_product_record set gross_margin_cny =case id ";
                    foreach ($joinRecordsChunk as $joinRecord) {
                        $grossMarginCny =  round($joinRecord['gross_margin'] * ($joinRecord['exchange_rate'] / 100), 4);
                        $updateSql .=sprintf("when %d then %f ", $joinRecord['id'], $grossMarginCny);
                    }
                    $updateSql .= 'end, ';

                    $updateSql .= ' gross_margin_usd = case id ';
                    foreach ($joinRecordsChunk as $joinRecord) {
                        $grossMarginUsd =  round($joinRecord['gross_margin'] * ($joinRecord['exchange_rate_usd'] / 100), 4);
                        $updateSql .= sprintf("when %d then %f ", $joinRecord['id'], $grossMarginUsd);
                    }
                    $updateSql .= 'end ';

                    $updateSql .= "where client_id=$clientId and id in (" . implode(', ', array_column($joinRecordsChunk, 'id')) . ")";

                    echo $updateSql . PHP_EOL;

                    $pgDB->createCommand($updateSql)->execute();
                }
            }
        }
    }

    function actionCn() {
        $tr = [

            "关联采购入库单ID" => "Linked purchase receipt ID",
            "出入库明细-仓库" => "Inventory detail - warehouse",
            "产品是否停售" => "Product discontinued",

            // 采购入库
            "关联采购单id" => "Linked purchase order ID",
            "采购订单明细id" => "Purchase order detail ID",
//    "出入库明细-仓库" => "Inventory detail - warehouse",
            "产品流转ID" => "Product circulation ID",
            "产品流转明细ID" => "Product circulation detail ID",
//    "产品是否停售" => "Product discontinued",

            // 供应商产品
            "起订量" => "Minimum order quantity",

            // 供应商
            "主资金账户标识"  => "Main fund account identifier",
            // 销售出库
            "出库来源" => "Outbound source",
            "关联出运单" => "Linked shipment order",
            "出运数量" => "Shipment quantity",
            "成本单价" => "Cost unit price",
            "关联出库单" => "Linked outbound order",

            // 销售订单
            "综合到账金额（CNY）" => "Total received amount (CNY)",
            "综合到账金额（USD）" => "Total received amount (USD)",
            "订单来源类型" => "Order source type",
            "交易服务费币种" => "Transaction fee currency",
            "交易服务费金额" => "Transaction fee amount",
        ];

        foreach ($tr as $k => $v) {
            print("\"${k}\"=>\"${k}\"," . "\n");
        }
    }
    function actionDiff() {
        $enArray = include("messages/en/privilege.php");
        $twArray = include("messages/zh-TW/privilege.php");
        $cnArray = include("messages/zh-CN/privilege.php");

        // 查找在$enArray中但不在$twArray中的键
        $keys_not_in_twArray = array_diff(array_keys($enArray), array_keys($twArray));

        $keys_not_in_cnArray = array_diff(array_keys($enArray), array_keys($cnArray));
        echo "缺失繁体翻译\n";
        foreach ($keys_not_in_twArray as $key) {
//            echo "Key '$key' is in enArray but not in twArray.\n";
            echo '"' . $key . '"' . "\n";
        }

        echo "\n\n\n\n缺失CN->CN\n";
        foreach ($keys_not_in_cnArray as $k => $v) {
            print("\"${v}\"=>\"${v}\"," . "\n");
        }
    }
    function actionGetField() {
        $field = \common\library\object\field\Helper::getTransferFieldConfig(\Constants::TYPE_SHIPPING_PRODUCT);
        print_r(['field' => $field['field']]);
        $fieldList =  array_keys($field['field']);
        print_r(['field' => $fieldList]);
        $fieldList = array_map(function ($field) {
            return "'" . $field . "'";
        }, $fieldList);
        $sql = implode(",", $fieldList);
        echo $sql;
        echo count($fieldList);
    }

    function actionGetFieldFromSQL() {
        $createRecordSql = "create table tbl_shipping_record
(
    shipping_record_id    bigint                                        not null
        primary key,
    shipping_invoice_id   bigint                                        not null,
    order_id              bigint                                        not null,
    client_id             bigint                                        not null,
    invoice_product_id    bigint                                        not null,
    package_unit          varchar(64)     default ''::character varying not null,
    count_per_package     numeric(11, 4)  default 0                     not null,
    package_remark        text            default ''::text              not null,
    shipping_count        numeric(20, 4)  default 0                     not null,
    product_net_weight    numeric(11, 4)  default 0                     not null,
    package_gross_weight  numeric(11, 4)  default 0                     not null,
    package_volume        varchar(64)     default ''::character varying not null,
    package_size_length   numeric(11, 4)  default 0                     not null,
    package_size_weight   numeric(11, 4)  default 0                     not null,
    package_size_height   numeric(11, 4)  default 0                     not null,
    product_size_length   numeric(11, 4)  default 0                     not null,
    product_size_weight   numeric(11, 4)  default 0                     not null,
    product_size_height   numeric(11, 4)  default 0                     not null,
    carton_size_length    numeric(11, 4)  default 0                     not null,
    carton_size_weight    numeric(11, 4)  default 0                     not null,
    carton_size_height    numeric(11, 4)  default 0                     not null,
    description           text            default ''::text              not null,
    remark                text            default ''::text              not null,
    count_per_carton      numeric(11, 4)  default 0                     not null,
    container_type        varchar(128)    default ''::character varying not null,
    container_no          varchar(256)    default ''::character varying not null,
    container_seal_number varchar(256)    default ''::character varying not null,
    hs_code               varchar(64)     default ''::character varying not null,
    customs_name          varchar(256)    default ''::character varying not null,
    customs_cn_name       varchar(256)    default ''::character varying not null,
    external_field_data   jsonb           default '{}'::jsonb           not null,
    enable_flag           smallint        default 1                     not null,
    create_time           timestamp(0)    default now()                 not null,
    update_time           timestamp(0)    default now()                 not null,
    product_id            bigint,
    sku_id                bigint,
    product_type          smallint        default 0                     not null,
    combine_record_id     bigint          default 0                     not null,
    sort                  bigint          default 0                     not null,
    package_type          varchar(256)    default ''::character varying not null,
    carton_gross_weight   numeric(20, 4)  default 0                     not null,
    carton_volume         numeric(20, 4)  default 0                     not null,
    product_volume        numeric(20, 4)  default 0                     not null,
    is_master_product     smallint        default 0                     not null,
    master_id             bigint          default 0                     not null,
    carton_net_weight     numeric(20, 4)  default 0                     not null,
    product_image         jsonb           default '{}'::jsonb           not null,
    product_name          varchar(255)    default ''::character varying not null,
    product_cn_name       varchar(255)    default ''::character varying not null,
    product_model         varchar(255)    default ''::character varying not null,
    unit                  varchar(64)     default ''::character varying not null,
    sale_price            numeric(30, 10) default 0                     not null,
    carton_count          numeric(20, 4)  default 0                     not null
);";
        $recordFields = $this->parseTableFields($createRecordSql);
        print_r($recordFields);
    }

    function parseTableFields($createTableSql) {
        $fields = [];

        // 使用正则表达式匹配字段定义
        preg_match_all('/\s+(\w+)\s+(?:bigint|varchar|integer|timestamp|smallint|text|jsonb|numeric|date)\s*/', $createTableSql, $matches);

        if (!empty($matches[1])) {
            foreach ($matches[1] as $field) {
                $fields[] = $field;
            }
        }

        return $fields;
    }
    public function actionTest5()
    {
        $db = \Yii::app()->prometheus_db;
        $report_field = [
            'client_id',
            'cash_collection_id',
        ];
        $report_field = json_encode($report_field);
        $sql = "update tbl_replication_listener set report_field ='{$report_field}' where `table` = 'tbl_cash_collection'";
        $db->createCommand($sql)->execute();

        $report_field = [
            'client_id',
            'outbound_record_id',
           'type'
        ];
        $report_field = json_encode($report_field);
        $sql = "update tbl_replication_listener set report_field ='{$report_field}' where `table` = 'tbl_outbound_record'";
        $db->createCommand($sql)->execute();
    }

    public function actionTest6(){
        $messageJob = json_decode('[
  {
    "messageId": "1736260499334-0",
    "msgType": "INSERT",
    "clientId": "333863",
    "tableName": "tbl_cash_collection",
    "oldValues": {
      "client_id": null,
      "cash_collection_id": null
    },
    "newValues": {
      "client_id": "333863",
      "cash_collection_id": "5156965329"
    },
    "timeStamp": 1736260499334354329,
    "timeFormat": "2025-01-07 22:34:59"
  }
]', true);
        $message = new \common\library\server\binlog_to_biz\MessageJob($messageJob);
        $message->handle();
    }
    public function actionTest7() {
        $clientId = 351383;
        \common\library\object\field\template\AddCalculateFieldTask::addFields($clientId);
    }


    public function actionCheck()
    {
        $columns = [
            'cash_collection_id' => [
                'type' => 'int',
                'name' => 'cash_collection_id',
                'nullable' => '0',
                'php_type' => 'int',
                'filter' => [
                    'enable' => true,
                    'batch' => true
                ]
            ],
            'create_time' => [
                'type' => 'dateTime',
                'name' => 'create_time',
                'nullable' => '0',
                'php_type' => 'string',
                'filter' => [
                    'enable' => true,
                    'batch' => true
                ]
            ],
            'update_time' => [
                'type' => 'dateTime',
                'name' => 'update_time',
                'nullable' => '0',
                'php_type' => 'string',
                'filter' => [
                    'enable' => true,
                    'batch' => true
                ]
            ],
            'collection_date' => [
                'type' => 'date',
                'name' => 'collection_date',
                'nullable' => '0',
                'php_type' => 'date',
                'filter' => [
                    'enable' => true,
                    'batch' => true
                ]
            ],
            'amount' => [
                'type' => 'float',
                'name' => 'amount',
                'nullable' => '0',
                'php_type' => 'float',
                'filter' => [
                    'enable' => true,
                    'batch' => true
                ]
            ],
            'currency' => [
                'type' => 'string',
                'name' => 'currency',
                'nullable' => '0',
                'php_type' => 'string',
                'filter' => [
                    'enable' => true,
                    'batch' => true
                ]
            ],
            'exchange_rate' => [
                'type' => 'float',
                'name' => 'exchange_rate',
                'nullable' => '0',
                'php_type' => 'float',
                'filter' => [
                    'enable' => true,
                    'batch' => true
                ]
            ],
            'finance_verify_name' => [
                'type' => 'string',
                'name' => 'finance_verify_name',
                'nullable' => '1',
                'php_type' => 'string',
                'filter' => [
                    'enable' => true,
                    'batch' => true
                ]
            ],
            'comment' => [
                'type' => 'string',
                'name' => 'comment',
                'nullable' => '1',
                'php_type' => 'string',
                'filter' => [
                    'enable' => true,
                    'batch' => true
                ]
            ],
            'order_id' => [
                'type' => 'int',
                'name' => 'order_id',
                'nullable' => '1',
                'php_type' => 'int',
                'filter' => [
                    'enable' => true,
                    'batch' => true
                ]
            ],
            'user_id' => [
                'type' => 'array',
                'name' => 'user_id',
                'nullable' => '1',
                'php_type' => 'array',
                'filter' => [
                    'enable' => true,
                    'batch' => true
                ]
            ],
            'client_id' => [
                'type' => 'int',
                'name' => 'client_id',
                'nullable' => '1',
                'php_type' => 'int',
                'filter' => [
                    'enable' => true,
                    'batch' => true
                ]
            ],
            'op_user_id' => [
                'type' => 'int',
                'name' => 'op_user_id',
                'nullable' => '0',
                'php_type' => 'int',
                'filter' => [
                    'enable' => true,
                    'batch' => true
                ]
            ],
            'type' => [
                'type' => 'string',
                'name' => 'type',
                'nullable' => '0',
                'php_type' => 'string',
                'filter' => [
                    'enable' => true,
                    'batch' => true
                ]
            ],
            'cash_collection_no' => [
                'type' => 'string',
                'name' => 'cash_collection_no',
                'nullable' => '0',
                'php_type' => 'string',
                'filter' => [
                    'enable' => true,
                    'batch' => true
                ]
            ],
            'amount_rmb' => [
                'type' => 'float',
                'name' => 'amount_rmb',
                'nullable' => '0',
                'php_type' => 'float',
                'filter' => [
                    'enable' => true,
                    'batch' => true
                ]
            ],
            'amount_usd' => [
                'type' => 'float',
                'name' => 'amount_usd',
                'nullable' => '0',
                'php_type' => 'float',
                'filter' => [
                    'enable' => true,
                    'batch' => true
                ]
            ],
            'exchange_rate_usd' => [
                'type' => 'float',
                'name' => 'exchange_rate_usd',
                'nullable' => '0',
                'php_type' => 'float',
                'filter' => [
                    'enable' => true,
                    'batch' => true
                ]
            ],
            'company_id' => [
                'type' => 'int',
                'name' => 'company_id',
                'nullable' => '0',
                'php_type' => 'int',
                'filter' => [
                    'enable' => true,
                    'batch' => true
                ]
            ],
            'create_user_id' => [
                'type' => 'int',
                'name' => 'create_user_id',
                'nullable' => '0',
                'php_type' => 'int',
                'filter' => [
                    'enable' => true,
                    'batch' => true
                ]
            ],
            'file_list' => [
                'type' => 'jsonb',
                'name' => 'file_list',
                'nullable' => '0',
                'php_type' => 'array',
                'filter' => [
                    'enable' => true,
                    'batch' => true
                ]
            ],
            'opportunity_id' => [
                'type' => 'int',
                'name' => 'opportunity_id',
                'nullable' => '0',
                'php_type' => 'int',
                'filter' => [
                    'enable' => true,
                    'batch' => true
                ]
            ],
            'enable_flag' => [
                'type' => 'int',
                'name' => 'enable_flag',
                'nullable' => '0',
                'php_type' => 'int',
                'filter' => [
                    'enable' => true,
                    'batch' => true
                ]
            ],
            'payee' => [
                'type' => 'string',
                'name' => 'payee',
                'nullable' => '0',
                'php_type' => 'string',
                'filter' => [
                    'enable' => true,
                    'batch' => true
                ]
            ],
            'department_id' => [
                'type' => 'array',
                'name' => 'department_id',
                'nullable' => '0',
                'php_type' => 'array',
                'filter' => [
                    'enable' => true,
                    'batch' => true
                ]
            ],
            'departments' => [
                'type' => 'jsonb',
                'name' => 'departments',
                'nullable' => '0',
                'php_type' => 'array',
                'filter' => [
                    'enable' => true,
                    'batch' => true
                ]
            ],
            'users' => [
                'type' => 'jsonb',
                'name' => 'users',
                'nullable' => '0',
                'php_type' => 'array',
                'filter' => [
                    'enable' => true,
                    'batch' => true
                ]
            ],
            'refer_type' => [
                'type' => 'int',
                'name' => 'refer_type',
                'nullable' => '0',
                'php_type' => 'int',
                'filter' => [
                    'enable' => true,
                    'batch' => true
                ]
            ],
            'finance_verify_date' => [
                'type' => 'date',
                'name' => 'finance_verify_date',
                'nullable' => '1',
                'php_type' => 'date',
                'filter' => [
                    'enable' => true,
                    'batch' => true
                ]
            ],
            'external_field_data' => [
                'type' => 'jsonb',
                'name' => 'external_field_data',
                'nullable' => '0',
                'php_type' => 'array',
                'filter' => [
                    'enable' => true,
                    'batch' => true
                ]
            ],
            'collecting_bank' => [
                'type' => 'string',
                'name' => 'collecting_bank',
                'nullable' => '0',
                'php_type' => 'string',
                'filter' => [
                    'enable' => true,
                    'batch' => true
                ]
            ],
            'collect_status' => [
                'type' => 'int',
                'name' => 'collect_status',
                'nullable' => '0',
                'php_type' => 'int',
                'filter' => [
                    'enable' => true,
                    'batch' => true
                ]
            ],
            'source_lead_id' => [
                'type' => 'int',
                'name' => 'source_lead_id',
                'nullable' => '0',
                'php_type' => 'int',
                'filter' => [
                    'enable' => true,
                    'batch' => true
                ]
            ],
            'real_amount' => [
                'type' => 'float',
                'name' => 'real_amount',
                'nullable' => '0',
                'php_type' => 'float',
                'filter' => [
                    'enable' => true,
                    'batch' => true
                ]
            ],
            'real_amount_rmb' => [
                'type' => 'float',
                'name' => 'real_amount_rmb',
                'nullable' => '0',
                'php_type' => 'float',
                'filter' => [
                    'enable' => true,
                    'batch' => true
                ]
            ],
            'real_amount_usd' => [
                'type' => 'float',
                'name' => 'real_amount_usd',
                'nullable' => '0',
                'php_type' => 'float',
                'filter' => [
                    'enable' => true,
                    'batch' => true
                ]
            ],
            'bank_charge' => [
                'type' => 'float',
                'name' => 'bank_charge',
                'nullable' => '0',
                'php_type' => 'float',
                'filter' => [
                    'enable' => true,
                    'batch' => true
                ]
            ],
            'bank_charge_rmb' => [
                'type' => 'float',
                'name' => 'bank_charge_rmb',
                'nullable' => '0',
                'php_type' => 'float',
                'filter' => [
                    'enable' => true,
                    'batch' => true
                ]
            ],
            'bank_charge_usd' => [
                'type' => 'float',
                'name' => 'bank_charge_usd',
                'nullable' => '0',
                'php_type' => 'float',
                'filter' => [
                    'enable' => true,
                    'batch' => true
                ]
            ],
            'trade_no' => [
                'type' => 'string',
                'name' => 'trade_no',
                'nullable' => '0',
                'php_type' => 'string',
                'filter' => [
                    'enable' => true,
                    'batch' => true
                ]
            ],
            'capital_account_id' => [
                'type' => 'int',
                'name' => 'capital_account_id',
                'nullable' => '0',
                'php_type' => 'int',
                'filter' => [
                    'enable' => true,
                    'batch' => true
                ]
            ],
            'create_type' => [
                'type' => 'int',
                'name' => 'create_type',
                'nullable' => '0',
                'php_type' => 'int',
                'filter' => [
                    'enable' => true,
                    'batch' => true
                ]
            ],
            'store_id' => [
                'type' => 'int',
                'name' => 'store_id',
                'nullable' => '0',
                'php_type' => 'int',
                'filter' => [
                    'enable' => true,
                    'batch' => true
                ]
            ],
            'acp_sync_order_record_id' => [
                'type' => 'int',
                'name' => 'acp_sync_order_record_id',
                'nullable' => '0',
                'php_type' => 'int',
                'filter' => [
                    'enable' => true,
                    'batch' => true
                ]
            ],
            'cash_collection_invoice_id' => [
                'type' => 'int',
                'name' => 'cash_collection_invoice_id',
                'nullable' => '0',
                'php_type' => 'int',
                'filter' => [
                    'enable' => true,
                    'batch' => true
                ]
            ],
            'cash_collection_invoice_amount' => [
                'type' => 'float',
                'name' => 'cash_collection_invoice_amount',
                'nullable' => '0',
                'php_type' => 'float',
                'filter' => [
                    'enable' => true,
                    'batch' => true
                ]
            ],
            'cash_collection_invoice_amount_usd' => [
                'type' => 'float',
                'name' => 'cash_collection_invoice_amount_usd',
                'nullable' => '0',
                'php_type' => 'float',
                'filter' => [
                    'enable' => true,
                    'batch' => true
                ]
            ],
            'cash_collection_invoice_amount_rmb' => [
                'type' => 'float',
                'name' => 'cash_collection_invoice_amount_rmb',
                'nullable' => '0',
                'php_type' => 'float',
                'filter' => [
                    'enable' => true,
                    'batch' => true
                ]
            ],
            'cash_collection_invoice_bank_charge' => [
                'type' => 'float',
                'name' => 'cash_collection_invoice_bank_charge',
                'nullable' => '0',
                'php_type' => 'float',
                'filter' => [
                    'enable' => true,
                    'batch' => true
                ]
            ],
            'cash_collection_invoice_bank_charge_usd' => [
                'type' => 'float',
                'name' => 'cash_collection_invoice_bank_charge_usd',
                'nullable' => '0',
                'php_type' => 'float',
                'filter' => [
                    'enable' => true,
                    'batch' => true
                ]
            ],
            'cash_collection_invoice_bank_charge_rmb' => [
                'type' => 'float',
                'name' => 'cash_collection_invoice_bank_charge_rmb',
                'nullable' => '0',
                'php_type' => 'float',
                'filter' => [
                    'enable' => true,
                    'batch' => true
                ]
            ],
            'cash_collection_invoice_exchange_rate' => [
                'type' => 'float',
                'name' => 'cash_collection_invoice_exchange_rate',
                'nullable' => '0',
                'php_type' => 'float',
                'filter' => [
                    'enable' => true,
                    'batch' => true
                ]
            ],
            'cash_collection_invoice_currency' => [
                'type' => 'string',
                'name' => 'cash_collection_invoice_currency',
                'nullable' => '0',
                'php_type' => 'string',
                'filter' => [
                    'enable' => true,
                    'batch' => true
                ]
            ],
            'cash_collection_invoice_exchange_rate_usd' => [
                'type' => 'float',
                'name' => 'cash_collection_invoice_exchange_rate_usd',
                'nullable' => '0',
                'php_type' => 'float',
                'filter' => [
                    'enable' => true,
                    'batch' => true
                ]
            ],
            'allocate_time' => [
                'type' => 'dateTime',
                'name' => 'allocate_time',
                'nullable' => '0',
                'php_type' => 'string',
                'filter' => [
                    'enable' => true,
                    'batch' => true
                ]
            ],
            'collect_flag' => [
                'type' => 'int',
                'name' => 'collect_flag',
                'nullable' => '0',
                'php_type' => 'int',
                'filter' => [
                    'enable' => true,
                    'batch' => true
                ]
            ],
            'shipping_invoice_id' => [
                'type' => 'int',
                'name' => 'shipping_invoice_id',
                'nullable' => '0',
                'php_type' => 'int',
                'filter' => [
                    'enable' => true,
                    'batch' => true
                ]
            ],
            'allocated_status' => [
                'type' => 'int',
                'name' => 'allocated_status',
                'nullable' => '0',
                'php_type' => 'int',
                'filter' => [
                    'enable' => true,
                    'batch' => true
                ]
            ],
            'scope_user_ids' => [
                'type' => 'array',
                'name' => 'scope_user_ids',
                'nullable' => '1',
                'php_type' => 'array',
                'filter' => [
                    'enable' => true,
                    'batch' => true
                ]
            ]
        ];

        $fieldOnDB = [
            'cash_collection_id',
            'cash_collection_no',
            'client_id',
            'user_id',
            'users',
            'type',
            'refer_type',
            'order_id',
            'opportunity_id',
            'company_id',
            'department_id',
            'departments',
            'payee',
            'amount',
            'amount_rmb',
            'amount_usd',
            'currency',
            'exchange_rate',
            'exchange_rate_usd',
            'collection_date',
            'finance_verify_name',
            'finance_verify_date',
            'comment',
            'file_list',
            'op_user_id',
            'enable_flag',
            'create_user_id',
            'create_time',
            'update_time',
            'external_field_data',
            'collecting_bank',
            'collect_status',
            'source_lead_id',
            'real_amount',
            'real_amount_rmb',
            'real_amount_usd',
            'bank_charge',
            'bank_charge_rmb',
            'bank_charge_usd',
            'trade_no',
            'capital_account_id',
            'create_type',
            'store_id',
            'acp_sync_order_record_id',
            'cash_collection_invoice_id',
            'cash_collection_invoice_currency',
            'cash_collection_invoice_exchange_rate',
            'cash_collection_invoice_exchange_rate_usd',
            'cash_collection_invoice_amount',
            'cash_collection_invoice_amount_usd',
            'cash_collection_invoice_amount_rmb',
            'cash_collection_invoice_bank_charge',
            'cash_collection_invoice_bank_charge_usd',
            'cash_collection_invoice_bank_charge_rmb',
            'allocate_time',
        ];

        $keys = array_keys($columns);
        foreach ($keys as $key) {

            if (!in_array($key, $fieldOnDB)) {
                echo $key . PHP_EOL;
            }
        }
    }

    public function actionPurchaseProduct() {
        $clientId=333863;
        $privilegeService = PrivilegeService::getInstance($clientId);
        $adminUserId = $privilegeService->getAdminUserId();
        User::setLoginUserById($adminUserId);

        $api = new PurchaseOrderProductAPI($clientId);

       // print_r(['fieldMap' => $api->setObjectFieldMap()]);
        $params = [
            "page_no" => "1",
            "page_size" => "20",
            "sort_field" => "",
            "sort_type" => "",
            "scene" => "list",
            "handler" => [],
            "skip_permission_check" => 0,
//            "debug" => 1,
            "query_filters" => ["**********" => ["value" => ["1", "1000"]]],
        ];
        print_r($params);
        $data = $api->productList($adminUserId, $params);
        print_r(['data' => $data]);
    }


    public function actionSynOrder($orderId, $clientId, $userId)
    {

        $type = \Constants::ORDER_INDEX_TYPE_UPDATE;
        $data = [
            'id' => $orderId,
            'client_id' => $clientId,
            'user_id' => $userId,
            'order_id' => $orderId,
            'type' => $type,
            'handler' => OrderHandler::class,
            'timestamp' => time(),
        ];
        $ret = \common\library\server\Helper::handleSearchV7($data);
    }

    public function actionRefreshInventory() {
        /*
         *   array(3) {
    ["ids"]=>
    array(1) {
      [0]=>
      int(5146333400)
    }
    ["client_id"]=>
    string(5) "14119"
    ["type"]=>
    string(8) "outbound"
  }
         * */
        $ids = [5181436673, 5181436674];
        $clientId = 14119;
        $type = \common\library\server\refresh_product_inventory\RefreshInventoryQueue::TYPE_INBOUND_RECORD;
        $data = [
            'ids' => $ids,
            'client_id' => $clientId,
            'type' => $type,
        ];

        \common\library\server\refresh_product_inventory\RefreshInventoryQueue::refresh($data);
    }

    public function actionRate() {
        $this->clientId=14119;
        $this->setLoginByClientId($this->clientId);
        $rateService = new \common\library\exchange_rate\ExchangeRateService();
        $rateService->setMainCurrencyByClientId($this->clientId);
        $rate = $rateService->usdRateForCurrency('CNY');

    }

    public function setLoginByClientId($clientId)
    {
        $privilegeService = PrivilegeService::getInstance($clientId);
        $adminUserId = $privilegeService->getAdminUserId();
        User::setLoginUserById($adminUserId);
    }


    public function actionSearchOrder() {
        $clientId=14119;
        $api = new \common\library\oms\order\OrderApi($clientId);
        $ret = $api->purchaseProductReferOrder(0, '1');
        var_dump($ret);
    }

    public function actionPurchase() {
        $this->clientId=14119;
        $orderIdList = [
            5215743477,
            3562957014,
        ];
        $orderPartialPurchaseMap = [];
        $purchaseOrderFilter = new PurchaseOrderFilter($this->clientId);
        $purchaseOrderFilter->select([
            'refer_order_id',
            'order_count' => function ()  {
                // return "sum({$purchaseProductAlias}.unit_price * {$purchaseProductAlias}.count * {$purchaseAlias}.exchange_rate /100) as total_unit_price_cny";
                return "count(*) as order_count";
            },
        ]);
        $purchaseOrderFilter->enable_flag = \Constants::ENABLE_FLAG_TRUE;
//        $purchaseOrderFilter->purchase_type = PurchaseOrder::PURCHASE_TYPE_PARTIAL;
        $purchaseOrderFilter->refer_order_id = new In($orderIdList);
        $purchaseOrderFilter->groupBy("refer_order_id");


        $purchaseOrderProductList = $purchaseOrderFilter->rawData();
        print_r(['rawdata' => $purchaseOrderProductList]);
        $orderIdToCount = array_column($purchaseOrderProductList, 'order_count', 'refer_order_id');
        print_r([$orderIdToCount]);
    }

    public function actionFixCustomField() {
        $clientId = 14119;
        $pgDb = \PgActiveRecord::getDbByClientId($clientId);
        $sql = "select field, field_name from tbl_field where client_id=14119 and object_name in ('objQuotation', 'objQuotationProduct')";

        $fieldList = $pgDb->createCommand($sql)->queryAll();

        $idToName = array_column($fieldList, 'field_name', 'field', );

        print_r($idToName);


        $mysqlDb = \ProjectActiveRecord::getDbByClientId($clientId);

        $customFieldSql = "select * from tbl_custom_field where client_id = 14119 and type=3 ";
        $customFieldList = $mysqlDb->createCommand($customFieldSql)->queryAll();
//        print_r($customFieldList);


        $v4db = \Yii::app()->db;

        $systemFieldSql = "select id, name from tbl_system_field where type=3";
        $systemField = $v4db->createCommand($systemFieldSql)->queryAll();
        $systemIdToName = array_column($systemField, 'name', 'id');

        foreach ($systemIdToName as $id => $name) {
            if (empty($idToName[$id])) {
                $idToName[$id] = $name;
            }
        }

        foreach ($customFieldList as $customField) {
            $name = $idToName[$customField['id']] ?? '';
            if (empty($name)) {
                print_r([
                    'not found',
                    $customField['id']
                ]);
                continue;
            }
            $id = $customField['id'];
            $updateSql = "update tbl_custom_field set name='$name' where client_id = 14119 and type=3  and id='{$id}'";
            print_r([
                'updateSql' => $updateSql
            ]);

            $mysqlDb->createCommand($updateSql)->execute();
        }
    }
}
