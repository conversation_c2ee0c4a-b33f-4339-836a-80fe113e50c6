<?php
/**
 * Created by PhpStorm.
 * User: shisiying
 * Date: 2019-10-17
 * Time: 17:29
 */

use common\components\BaseObject;
use common\library\ai\service\EventsReport;
use common\library\auto_market\handler\MarketingAutomationHandler;
use common\library\auto_market\handler\MarketingAutomationHandlerApi;
use common\library\auto_market\handler\MarketingAutomationHandlerFilter;
use common\library\custom_field\field_item\field_item_list\CustomerStatusList;
use common\library\mail\setting\rule\MailRuleList;
//use common\library\custom_field\field_item\field_item_list\CustomerStatusList;
//use common\library\custom_field\field_item\field_item_list\OriginList;
//use common\library\custom_field\field_item\FieldItemSetting;
use common\library\performance_v2\PerformanceV2Constant;
use common\library\performance_v2\record\PerformanceRecorder;
use common\library\performance_v2\refer\AbstractPerformanceRefer;
use common\library\serial\SerialRule;
use common\library\setting\user\process\UserGuide;
use common\library\setting\user\UserSetting;
use \common\library\mail\setting\rule\MailRuleOperator;
use common\library\statistics\CompanyHelper;
use common\models\client\CompanyHistoryPg;
use common\models\client\OpportunityProduct;
use common\library\account\Client;
use common\library\alimail\AliMailOrganizationObj;

class SevenCommand extends CrontabCommand
{
    protected $leadCount = 0;
    protected $opportunityCount = 0;
    protected $followCount = 0;
    protected $companyCount = 0;

    const FIX_TYPE_INT_OVER_FLOW = 1;

    public function actionSynLeadHistoryData($clientId)
    {
        $adminUserId = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();
        if ($adminUserId) {
            \User::setLoginUserById($adminUserId);
        } else {
            return;
        }

        $db = PgActiveRecord::getDbByClientId($clientId);
        $offset = 0;

        $leadHistoryData = function () use ($db, &$offset, $clientId) {
            $perPage = 1600;
            while (true) {
                $sql = "select * from tbl_lead where client_id = {$clientId} and is_archive=1 limit {$perPage} OFFSET {$offset} ";
                $leads = $db->createCommand($sql)->queryAll();
                $leadIds = array_column($leads, 'lead_id');
                if ($count = count($leadIds)) {
                    $offset += $count;
                } else {
                    break;
                }
                yield $leads;
            }
        };

        foreach ($leadHistoryData() as $leads) {

            $eventReport = new EventsReport(0, 0, 0);

            foreach ($leads as $lead) {

                $userId = \common\library\util\PgsqlUtil::trimArray($lead['user_id'])[0] ?? 0;
                $eventReport->setUserId($userId);
                $eventReport->setClientId($clientId);
                $eventReport->setEventTime($lead['create_time'] ?? null);
                $eventReport->setReferType(EventsReport::REFER_TYPE_LEAD);
                $eventReport->setEvent(EventsReport::EVENT_CREATE);
                $eventReport->setReferId($lead['lead_id'] ?? 0);
                $originId = $lead['origin'] ?? 0;

                $originApi = new \common\library\setting\library\origin\OriginApi($clientId, \Constants::TYPE_COMPANY);
                $originName = $originApi->getOneNameById($originId);
//                if ($originApi->isSystemSetting($originId)) {
//                    $originName = $originApi->getExtraDataMap()[$originId] ?? '';
//                } else {
//                    $origin = new FieldItemSetting($clientId, \common\library\custom_field\field_item\\common\library\setting\item\ItemSettingConstant::MIGRATION_ITEM_TYPE_OF_ORIGIN);
//                    $origin->loadById($originId);
//                    $originName = $origin->item_name ?? '';
//                }


                $extraData = [
                    'create_type' => EventsReport::CREATE_TYPE_MANUAL,
                    'email' => $lead['main_customer_email'] ?? '',
                    'origin' => $originId,
                    'origin_name' => $originName,
                    'group_id' => $lead['group_id'] ?? 0,
                    'company_name' => $lead['company_name'] ?? '',
                    'homepage' => $lead['homepage'] ?? '',
                    'country' => $lead['country'] ?? '',
                    'scale_id' => $lead['scale_id'] ?? 0,
                ];

                switch ($originId) {
                    case \common\library\setting\library\origin\Origin::SYS_ORIGIN_ID_MARKETING:
                    case \common\library\setting\library\origin\Origin::SYS_ORIGIN_ALIBABA :
                    case \common\library\setting\library\origin\Origin::SYS_ORIGIN_GLOBAL_SOURCES:
                    case \common\library\setting\library\origin\Origin::SYS_ORIGIN_GLOBAL_MARKET:
                    case \common\library\setting\library\origin\Origin::SYS_ORIGIN_MADE_IN_CHINA:
                        $extraData['create_type'] = EventsReport::CREATE_TYPE_AUTO;
                        break;

                    case \common\library\setting\library\origin\Origin::SYS_ORIGIN_ID_DISCOVERY:
                    case \common\library\setting\library\origin\Origin::SYS_ORIGIN_ID_AI_RECOMMEND:
                        $extraData['company_hash_id'] = $lead['company_hash_id'] ?? 0;
                        if ($originId == \common\library\setting\library\origin\Origin::SYS_ORIGIN_ID_AI_RECOMMEND) {
                            $extraData['pin'] = 1;
                        }
                        break;
                    default:
                        break;
                }

                $eventReport->setExtraInfo($extraData);
                $eventReport->addBodyItem();

                $this->leadCount++;
            }

            $ret = $eventReport->report();
            if ($ret) {
                self::info("client:" . $clientId . "成功同步线索" . count($leads) . "条");
            }
        }
    }

    public function actionSynOpportunityData($clientId, $fixType = '')
    {
        $db = PgActiveRecord::getDbByClientId($clientId);
        $offset = 0;

        $opportunityData = function () use ($db, &$offset, $clientId) {
            $perPage = 1600;
            while (true) {
                $sql = "select * from tbl_opportunity where client_id = {$clientId} and enable_flag=1 limit {$perPage} OFFSET {$offset} ";
                $opportunitys = $db->createCommand($sql)->queryAll();
                $opportunityIds = array_column($opportunitys, 'opportunity_id');
                if ($count = count($opportunityIds)) {
                    $offset += $count;
                } else {
                    break;
                }
                yield $opportunitys;
            }
        };


        foreach ($opportunityData() as $opportunitys) {

            $eventReport = new EventsReport(0, 0, 0);
            foreach ($opportunitys as $opportunity) {

                //重传stage超过int最大值的数据
                if ($fixType==self::FIX_TYPE_INT_OVER_FLOW && $opportunity['stage'] < 2147483647) {
                    continue;
                }

                $user_ids = \common\library\util\PgsqlUtil::trimArray($opportunity['user_id']);

                //兼容为空的情况
                if (empty($user_ids)) {
                    $user_ids = [0];
                }

                //拆分商机上的user_id
                foreach ($user_ids as $user_id) {
                    $eventReport->setUserId($user_id);
                    $eventReport->setClientId($clientId);
                    $eventReport->setEventTime($opportunity['create_time'] ?? null);
                    $eventReport->setReferType(EventsReport::REFER_TYPE_OPPORTUNITY_STAGE);
                    $eventReport->setEvent(EventsReport::EVENT_CREATE);
                    $eventReport->setReferId($opportunity['opportunity_id'] ?? 0);
                    $eventReport->setExtraInfo([
                        'origin' => $opportunity['origin'] ?? 0,
                        'stage' => $opportunity['stage'] ?? 0,
                        'weight' => (double)($opportunity['amount_usd'] ?? 0)
                    ]);
                    $eventReport->addBodyItem();
                    $this->opportunityCount++;
                }
            }
            $ret = $eventReport->report();
            if ($ret) {
                self::info("client:" . $clientId . "成功同步商机" . $eventReport->getMessageBodysCounts() . "条");
            }
        }
    }

    public function actionSynDxHistorySData($clientId)
    {
        $db = ProjectActiveRecord::getDbByClientId($clientId);
        $offset = 0;

        $dxFollowHistoryData = function () use ($db, &$offset, $clientId) {
            $perPage = 1600;
            while (true) {
                $sql = "select * from tbl_discovery_follow where client_id = {$clientId} and status=1 limit {$perPage} OFFSET {$offset} ";
                $follows = $db->createCommand($sql)->queryAll();
                $followIds = array_column($follows, 'follow_id');
                if ($count = count($followIds)) {
                    $offset += $count;
                } else {
                    break;
                }
                yield $follows;
            }
        };


        foreach ($dxFollowHistoryData() as $follows) {
            $eventReport = new EventsReport(0, 0, 0);
            foreach ($follows as $follow) {

                $eventReport->setReferType(EventsReport::REFER_TYPE_MAIL_DX_FOLLOW);
                $eventReport->setUserId($follow['user_id']);
                $eventReport->setClientId($clientId);
                $eventReport->setEventTime($follow['create_time'] ?? null);
                $eventReport->setReferId($follow['follow_id'] ?? 0);
                $eventReport->setEvent(EventsReport::EVENT_CREATE);
                $eventReport->setExtraInfo([
                    'company_hash_id' => $follow['company_hash_id'] ?? '',
                    'create_type' => EventsReport::CREATE_TYPE_MANUAL,
                    'group_id' => $follow['group_id']
                ]);
                $eventReport->addBodyItem();
                $this->followCount++;

            }
            $ret = $eventReport->report();
            if ($ret) {
                self::info("client:" . $clientId . "成功同步DX关注" . count($follows) . "条");
            }
        }
    }

    public function actionSynCustomerData($clientId)
    {

        $adminUserId = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();
        if ($adminUserId) {
            \User::setLoginUserById($adminUserId);
        } else {
            return;
        }

        $db = PgActiveRecord::getDbByClientId($clientId);
        $offset = 0;

        $companyData = function () use ($db, &$offset, $clientId) {
            $perPage = 1600;
            while (true) {
                $sql = "select * from tbl_company where client_id = {$clientId} and is_archive=1 limit {$perPage} OFFSET {$offset} ";
                $companys = $db->createCommand($sql)->queryAll();
                $companyIds = array_column($companys, 'company_id');
                if ($count = count($companyIds)) {
                    $offset += $count;
                } else {
                    break;
                }
                yield $companys;
            }
        };

        foreach ($companyData() as $companys) {

            $eventReport = new EventsReport(0, 0, 0);

            foreach ($companys as $company) {

                $user_ids = \common\library\util\PgsqlUtil::trimArray($company['user_id']);

                //兼容为空的情况
                if (empty($user_ids)) {
                    $user_ids = [0];
                }
                foreach ($user_ids as $user_id) {
                    $eventReport->setUserId($user_id);
                    $eventReport->setClientId($clientId);
                    $eventReport->setEventTime($company['create_time'] ?? null);
                    $eventReport->setReferType(EventsReport::REFER_TYPE_ARCHIVE);
                    $eventReport->setEvent(EventsReport::EVENT_CREATE);
                    $eventReport->setReferId($company['company_id'] ?? 0);
                    $originId = $company['origin'] ?? 0;

//                    if (isset(OriginList::SYS_ORIGIN_MAP[$originId])) {
//                        $originName = OriginList::SYS_ORIGIN_MAP[$originId]['item_name'] ?? '';
//                    } else {
//                        $origin = new FieldItemSetting($clientId, \common\library\custom_field\field_item\\common\library\setting\item\ItemSettingConstant::MIGRATION_ITEM_TYPE_OF_ORIGIN);
//                        $origin->loadById($originId);
//                        $originName = $origin->item_name ?? '';
//                    }
                    $originApi = new \common\library\setting\library\origin\OriginApi($clientId, \Constants::TYPE_COMPANY);
                    $originName = $originApi->getOneNameById($originId);

                    $extraData = [
                        'create_type' => EventsReport::CREATE_TYPE_MANUAL,
                        'email' => $company['main_customer_email'] ?? '',
                        'origin' => $originId,
                        'origin_name' => $originName,
                        'group_id' => $company['group_id'] ?? 0,
                        'company_name' => $company['name'] ?? '',
                        'homepage' => $company['homepage'] ?? '',
                        'country' => $company['country'] ?? '',
                        'scale_id' => $company['scale_id'] ?? 0,
                        'company_hash_id' => $company['company_hash_id'] ?? ''
                    ];

                    if ($company['archive_type'] == \common\library\customer_v3\company\orm\Company::ARCHIVE_TYPE_AI) {
                        $extraData['create_type'] = EventsReport::CREATE_TYPE_AUTO;
                    }

                    $eventReport->setExtraInfo($extraData);
                    $eventReport->addBodyItem();
                    $this->companyCount++;
                }
            }

            $ret = $eventReport->report();
            if ($ret) {
                self::info("client:" . $clientId . "成功同步客户建档" . count($companys) . "条");
            }
        }

    }


    public function actionRun()
    {
        $clientList = self::getClientList();
        $num = 0;
        $count = count($clientList);
        foreach ($clientList as $client) {
            $num++;
            self::info("clientId:{$client['client_id']} $num / $count \n");
            $this->actionSynLeadHistoryData($client['client_id']);
            $this->actionSynDxHistorySData($client['client_id']);
            $this->actionSynOpportunityData($client['client_id']);
        }

        self::info("共同步了" . $num . "个client");
        self::info("共同步了" . $this->leadCount . "条线索数据");
        self::info("共同步了" . $this->followCount . "条Dx关注数据");
        self::info("共同步了" . $this->opportunityCount . "条商机数据");
    }

    public function actionRunOpportuinty()
    {
        $clientList = self::getClientList();
        $num = 0;
        $count = count($clientList);
        foreach ($clientList as $client) {
            $num++;
            self::info("clientId:{$client['client_id']} $num / $count \n");
//            $this->actionSynOpportunityData($client['client_id']);
            //重传大于int最大值的stage
            $this->actionSynOpportunityData($client['client_id'], self::FIX_TYPE_INT_OVER_FLOW);
        }

        self::info("共同步了" . $num . "个client");
        self::info("共同步了" . $this->opportunityCount . "条商机数据");

    }

    public function actionRunCompany()
    {
        $clientList = self::getClientList();
        $num = 0;
        $count = count($clientList);
        foreach ($clientList as $client) {
            $num++;
            self::info("clientId:{$client['client_id']} $num / $count \n");
            $this->actionSynCustomerData($client['client_id']);
        }

        self::info("共同步了" . $num . "个client");
        self::info("共同步了" . $this->companyCount . "条客户数据");

    }

    /**
     * 修复群发单显邮件数据统计发送数不一致
     * @param $clientId
     * @param $starDate
     * @param $endDate
     * <AUTHOR>
     * @throws ProcessException
     */
    public function actionFixMailSendCount($clientId=0,$starDate='2019-12-01',$endDate='2019-12-31',$userId=0)
    {

        $db = ProjectActiveRecord::getDbByClientId($clientId);

        if ($userId!=0) {
            //查询当前client下的所有user_id
            $userList = [$userId];
        } else {
            $userList = UserInfo::model()->getDbConnection()->createCommand("select user_id from tbl_user_info where client_id={$clientId}")->queryColumn();
        }

        $userSendMailCounts = [];
        $createTime = date("Y-m-d H:i:s");
        //对每个user_id进行操作
        foreach ($userList as $userId) {
            $sql = "SELECT user_id, cast(receive_time AS date) as receive_time,count(*) as send_mail_count FROM tbl_mail WHERE user_id={$userId} and receive_time between '{$starDate} 00:00:00' and '{$endDate} 23:59:59' and mail_type=2 and folder_id!=0 and folder_id!=11 GROUP BY cast(receive_time AS date)";
            $data = $db->createCommand($sql)->queryAll();
            if (!empty($data)) {
                $userSendMailCounts[$userId] = $data;
            }
        }

        //重新更新每个user_id的send_mail_count
        foreach ($userSendMailCounts as $user => $sendMailCount) {
            foreach ($sendMailCount as $dateCount) {
                $updateSql = "INSERT INTO `tbl_user_statistics_day` (`client_id`, `user_id`, `date`,`send_mail_count`,`create_time`) VALUES ({$clientId}, {$user}, '{$dateCount['receive_time']}',{$dateCount['send_mail_count']},'{$createTime}')  ON DUPLICATE KEY UPDATE send_mail_count={$dateCount['send_mail_count']}";
                self::info($updateSql);
                $db->createCommand($updateSql)->execute();
            }
        }

    }

    public function actionSynOldSerialRuleForClients($clientId = 0, $lastNumber = null)
    {
        $clientList = [];

        //灰度指定client
        if ($clientId!=0 && is_string($clientId)) {
            $clientIdList = explode(',',$clientId);
        } else {
            //为0全量
            $clientList = self::getClientList($clientId, false, null, null, 0, 0, $lastNumber);
            $clientIdList = array_column($clientList, 'client_id', '');
        }

        foreach ($clientIdList as $clientId) {
            try{
                $this->synSerialRule($clientId);
            }catch (Throwable $throwable) {
                self::info($throwable->getMessage());
            }
        }
    }

    public function synSerialRule($clientId)
    {
        $db = ProjectActiveRecord::getDbByClientId($clientId);
        $sql = "SELECT * FROM tbl_serial_number_rule where client_id=$clientId";
        $result = $db->createCommand($sql)->queryAll(true);

        $sqlArr = [];
        $dateArr = [
            1 => 'yyyy',
            2 => 'yyMM',
            3 => 'yyyyMM',
            4 => 'yyMMdd',
            5 => 'yyyyMMdd'
        ];

        foreach ($result as $item) {

            $middle = (int)$item['middle'];
            $middle_str = isset($dateArr[$middle]) ? "{{" . $dateArr[(int)$item['middle']] . "}}":'';
            $customRule = [
                'prefix' => [
                    'rule_type' => 1,
                    'rule_value' => $item['prefix'] . $middle_str,
                ],
                'middle' => [
                    'rule_type' => 2,
                    'rule_value' => [
                        'number' => $item['suffix'],
                        'increase_type' => $item['suffix_rule']
                    ],
                ],
                'suffix' => [
                    'rule_type' => 1,
                    'rule_value' => '',
                ]
            ];

            $sqlArr[] = $this->buildBatchSaveSql($item['client_id'],$item['type'],$customRule);
        }

        if (empty($sqlArr)) {
            return;
        }

        $insertSql = "insert into tbl_serial_rule (client_id,module_id,custom_rule,enable_flag) values ";
        $insertSql = $insertSql . implode(',', $sqlArr);
        $insertSql .= " ON DUPLICATE KEY UPDATE custom_rule = values(custom_rule)";
        return $db->createCommand($insertSql)->execute();

    }

    private function buildBatchSaveSql($client_id, $module_id, array $customRule)
    {
        $customRule = json_encode($customRule,JSON_UNESCAPED_UNICODE);
        $sql = "('{$client_id}','{$module_id}','{$customRule}',1)";
        return $sql;
    }

    public function actionSetCompanyDefaultIncreaseNum($clientId = 0, $lastNumber = null)
    {
        $redis = \RedisService::getInstance('redis');
        $type = Constants::TYPE_COMPANY;
        $yearExpireAt = strtotime(date('Y-01-01 00:00:00',strtotime('+1 year')));
        $monthExpireAt = strtotime(date('Y-m-01 00:00:00', strtotime('first day of next month')));
        $dayExpireAt = strtotime(date('Y-m-d 00:00:00', strtotime('+1 day')));
        $clientList = [];

        //灰度指定client
        if ($clientId!=0 && is_string($clientId)) {
            $clientIdList = explode(',',$clientId);
        } else {
            //为0全量
            $clientList = self::getClientList($clientId, false, null, null, 0, 0, $lastNumber);
            $clientIdList = array_column($clientList, 'client_id', '');
        }


        foreach ($clientIdList as $clientId) {
            try{
                $this->setDafault($clientId, $type, $redis, $yearExpireAt, $monthExpireAt, $dayExpireAt);
            }catch (Throwable $throwable) {
                self::info($throwable->getMessage());
            }
        }
    }

    private function setDafault($clientId, $type, $redis, $yearExpireAt, $monthExpireAt, $dayExpireAt)
    {
        echo 'client:' . $clientId . ":begin!";

        $keyMap = [
            'all' => "serial_number:client:{$clientId}:type:{$type}:increment_always",
            'year' => "serial_number:client:{$clientId}:type:{$type}:increment_by_year",
            'month' => "serial_number:client:{$clientId}:type:{$type}:increment_by_month",
            'day' => "serial_number:client:{$clientId}:type:{$type}:increment_by_day",
        ];
        $db = \PgActiveRecord::getDbByClientId($clientId);
        $sql = "select company_id,archive_time  from tbl_company where client_id = {$clientId} and is_archive=1 ";

        //查询该client_id。总的客户数
        $data = $db->createCommand($sql)->queryAll();

        $allCount = count($data);
        echo PHP_EOL;
        echo "allcount:";
        echo $allCount;
        echo PHP_EOL;
        $redis->set($keyMap['all'], $allCount);


        $yearArr = [];
        $monthArr = [];
        $dayArr = [];

        foreach ($data as $item) {
            if ($item['archive_time'] >= '2020-01-01 00:00:00' && $item['archive_time'] <= '2020-12-31 23:59:59') {
                $yearArr[] = $item['company_id'];
            }

            if ($item['archive_time'] >= '2020-03-01 00:00:00' && $item['archive_time'] <= '2020-03-31 23:59:59') {
                $monthArr[] = $item['company_id'];
            }

            if ($item['archive_time'] >= date('Y-m-d') . ' 00:00:00' && $item['archive_time'] <= date('Y-m-d') . ' 23:59:59') {
                $dayArr[] = $item['company_id'];
            }
        }

        $yearCount = count($yearArr);
        $monthCount = count($monthArr);
        $dayCount = count($dayArr);

        //查询该client_id。今年的客户数
        $redis->set($keyMap['year'], $yearCount);
        $redis->expireat($keyMap['year'], $yearExpireAt);

        echo "yearCount:";
        echo $yearCount;
        echo PHP_EOL;

        //查询该client_id。这个月的客户数
        $redis->set($keyMap['month'], $monthCount);
        $redis->expireat($keyMap['month'], $monthExpireAt);

        echo "monthCount:";
        echo $monthCount;
        echo PHP_EOL;

        //查询该client_id。今天的客户数
        $redis->set($keyMap['day'], $dayCount);
        $redis->expireat($keyMap['day'], $dayExpireAt);
        echo "dayCount:";
        echo $dayCount;
        echo PHP_EOL;

        echo 'client:' . $clientId . "done!";
        echo PHP_EOL;
    }

    public function actionAddProductExportPrivilege($clientId = 0, $grey = 1, $expFlag = 0,$lastNumber=null)
    {
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey) {
            $clientIds = \common\library\GreyEnvHelper::getGreyClientIds();
        } elseif ($expFlag) {
            $clientIds = \common\library\account\Client::getExpClientIds();
        } else {
            $clientIds = array_column($this->getClientList($clientId, false, null, null, 0, 0, $lastNumber), 'client_id');
        }

        foreach ($clientIds as $clientId) {

            $roleService = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getUserPrivilege()->getRoles();

            //系统管理员
            $roleInfo = $roleService->getRoleInfoByName('系统管理员');
            if ($roleInfo) {
                $roleService->assignPrivilege($roleInfo['role_id'], $roleInfo['scope'], [
                    \common\library\privilege_v3\PrivilegeConstants::PRIVILEGE_CRM_PRODUCT_EXPORT,
                ]);
            }
            //产品管理管理员
            $roleInfo = $roleService->getRoleInfoByName('产品管理管理员');
            if ($roleInfo) {
                $roleService->assignPrivilege($roleInfo['role_id'], $roleInfo['scope'], [
                    \common\library\privilege_v3\PrivilegeConstants::PRIVILEGE_CRM_PRODUCT_EXPORT,
                ]);
            }
        }
    }

    public function actionSamplingExportOrderAndQuotation()
    {
        $count = 0;
        //读取文件获取数据
        $url = "https://jinyuncrmdevelop.oss-cn-hangzhou.aliyuncs.com/query-sparksql-4253.csv";
        //下载文件到tmp，
        $contents =file_get_contents($url);
        file_put_contents('/tmp/query-sparksql-4253.csv',$contents);
        self::info("文件下载成功");
        setlocale(LC_ALL,array('zh_CN.gbk','zh_CN.gb2312','zh_CN.gb18030'));
        $resultArray = array();
        if (($handle = fopen('/tmp/query-sparksql-4253.csv', "r")) !== FALSE)
        {
            while (! feof($handle)){
                $data = fgetcsv($handle);
                if($data!== FALSE)
                    $resultArray[]=$data;
            }
            fclose($handle);
        }

        $exportResultArray = [];
        foreach ($resultArray as $item) {

            if ($count >5) {
                break;
            }
            $count ++;

            if (empty($item[0]) || empty($item[1]) || empty($item[2]) || empty($item[12])){
                self::info("数据行为空");
                continue;
            }



            try {
                User::setLoginUserById($item[2]);
            } catch (Exception $exception) {
                self::info("用户【".$item[2]."】登陆不成功");
                continue;
            }

            $exportExcelUrl = '';
            $exportPdfUrl ='';

            self::info("开始用户【".$item[2]."】导出文件");

            if ($item[4] == Constants::TYPE_ORDER) {
                //导出excel
                try{
                    $export = new \common\library\invoice\export\OrderExport($item[1],$item[2],$item[3],$item[12]);
                    $export->setType('excel');
                    $export->setExportMethod(\common\library\invoice\export\AbstractInvoiceExport::METHOD_PREVIEW);
                    $export->setModule('crm');
                    $exportExcelUrl = $export->preview()['export_url'] ?? '';
                } catch (Exception $exception) {
                    $exportExcelUrl = '导出失败';
                }

                self::info("订单excel导出结果：".$exportExcelUrl);

                //导出pdf
                try{
                    $export = new \common\library\invoice\export\OrderExport($item[1],$item[2],$item[3],$item[12]);
                    $export->setType('pdf');
                    $export->setExportMethod(\common\library\invoice\export\AbstractInvoiceExport::METHOD_PREVIEW);
                    $export->setModule('crm');
                    $exportPdfUrl = $export->preview()['export_url'] ?? '';
                } catch (Exception $e) {
                    $exportPdfUrl = '导出失败';
                }

                self::info("订单pdf导出结果：".$exportPdfUrl);

            } elseif($item[4] == Constants::TYPE_QUOTATION) {

                //导出excel
                try{
                    $export = new \common\library\invoice\export\QuotationExport($item[1],$item[2],$item[3],$item[12]);
                    $export->setType('excel');
                    $export->setExportMethod(\common\library\invoice\export\AbstractInvoiceExport::METHOD_PREVIEW);
                    $export->setModule('crm');
                    $exportExcelUrl = $export->preview()['export_url'] ?? '';
                }catch (Exception $e) {
                    $exportExcelUrl = '导出失败';
                }

                self::info("报价单excel导出结果：".$exportExcelUrl);

                //导出pdf
                try {
                    $export = new \common\library\invoice\export\QuotationExport($item[1],$item[2],$item[3],$item[12]);
                    $export->setType('pdf');
                    $export->setExportMethod(\common\library\invoice\export\AbstractInvoiceExport::METHOD_PREVIEW);
                    $export->setModule('crm');
                    $exportPdfUrl = $export->preview()['export_url'] ?? '';
                } catch (Exception $e) {
                    $exportPdfUrl = '导出失败';
                }

                self::info("报价单pdf导出结果：".$exportPdfUrl);
            }

            $exportResultArray[]= [
                'export_id' => $item[0], //export_id
                'client_id' => $item[1], //client_id
                'user_id' => $item[2], //user_id
                'template_id' => $item[12], //template_id
                'new_export_excel_url' => $exportExcelUrl, //new_export_excel_url
                'new_export_pdf_url' => $exportPdfUrl, //new_export_pdf_url
            ];

        }

        self::info("csv数据获取成功");
        self::info("开始上传结果文件");
        //遍历数据 导出文件，记录文件，记录异常
        $fileName = "SamplingExportOrderAndQuotation.csv";
        $save_file_path = "/tmp/".$fileName;
        $exportFd = fopen($save_file_path, 'w');

        //生成文件保存好生成的链接
        foreach ($exportResultArray as $item) {
            fputcsv($exportFd,array_values($item));
        }

        fclose($exportFd);

        $content = file_get_contents($save_file_path);
        $content = mb_convert_encoding($content, "GBK", "UTF-8");
        file_put_contents($save_file_path, $content);


        $fileKey = \UploadService::getFileKey($fileName);
        $upload = UploadService::uploadRealFile($save_file_path, $fileName, $fileKey);
        $file = $upload->getFileObject();
        self::info($file->getFileUrl()) ;
        unlink($save_file_path);
        self::info("完成任务！done");

    }

    /**
     * <AUTHOR>
     * 迁移旧版新手任务数据
     */

    public function actionMigrateOldUserGuideV2ToNewVersion($clientId = 0, $lastNumber = null, $start = 0)
    {
        $clientIdList = [];

        //灰度指定client
        if ($clientId != 0 && is_string($clientId)) {
            $clientIdList = explode(',', $clientId);
        } else {
            //为0全量
            $clientList = self::getClientList($clientId, false, null, null, $start, 0, $lastNumber);
            $clientIdList = array_column($clientList, 'client_id', '');
        }

        foreach ($clientIdList as $clientId) {
            //所有client下的所有用户
            $db = \ProjectActiveRecord::getDbByClientId($clientId);
            $sql = "SELECT user_id FROM tbl_user_info WHERE client_id=:client_id AND enable_flag=1";
            $userIds = \Yii::app()->account_base_db->createCommand($sql)->queryAll(true, [':client_id' => $clientId]);

            //登陆superadmin
            $adminUserId = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();
            if ($adminUserId) {
                \User::setLoginUserById($adminUserId);
            } else {
                continue;
            }

            //先查询一些全局的任务完成状态
            //查询是否授权店铺
            $store = AlibabaStore::model()->find("client_id = {$clientId}");

            //查询是否授权设置账号对应关系
            $member = AlibabaStoreMembers::model()->find("client_id = {$clientId}");

            //查询是否有同步了阿里订单以及商品信息数据
            $aliRelateOrder = \common\models\client\AlibabaOrderRelation::model()->find("client_id = {$clientId}");

            //查询是否有同步了阿里客户通的数据
            $aliRelateCompany = AlibabaCustomerNoteRelationModel::model()->find("client_id = {$clientId}");

            //查询是否有whatsapp账号数据
            $contactMessage = UserCustomerContactMessage::model()->find("client_id = {$clientId}");

            $clientTask = [
                UserGuide::AUTH_ALI_ICBU_STORE => $store,
                UserGuide::ALI_ICBU_ACCOUNT_MAP_SETTING => $member,
                UserGuide::SYNC_ALI_ORDER_AND_PRODUCTS => $aliRelateOrder,
                UserGuide::SYNCHRONIZE_ALI_CUSTOMERS => $aliRelateCompany,
                UserGuide::START_WHATSAPP_CHAT_SYNC_ASSISTANT => $contactMessage,
            ];

            if (!empty($userIds)) {
                foreach ($userIds as $userId) {
                    $this->actionMigrateUserGuideV2ByUserId($clientId, $userId['user_id'],$clientTask);
                    echo "clientId:{$clientId} userId:{$userId['user_id']} complete\n";
                }
            }
        }
    }

    public function actionMigrateUserGuideV2ByUserId($clientId, $userId,$clientTask = [])
    {
        User::setLoginUserById($userId);
        // 查询原有的数据
        $key = \common\library\setting\user\UserSetting::USER_GUIDE_V2;
        $setting = new \common\library\setting\user\UserSetting($clientId, $userId, $key);
        $taskList = $setting->getValue();


        if (!is_array($taskList)) {
            $taskList = [];
        }

        $result = [];

        //老数据补偿措施
        if (empty($taskList)) {

            $group = new \common\library\group\migration\GroupList($clientId, \Constants::TYPE_COMPANY);
            $group->setIncludeSystemGroup(false);
            $group->setLimit(1);
            if (!empty($group->find())) {
                $taskList[\common\library\setting\user\process\UserGuide::CUSTOMER_GROUP] = UserGuide::USER_GUIDE_TASK_STATUS_FINISH;
            }

            $list = new CustomerImportList($userId);
            $list->setType(Constants::TYPE_CUSTOMER);
            $list->setLimit(1);
            if (!empty($list->find())) {
                $taskList[\common\library\setting\user\process\UserGuide::CUSTOMER_IMPORT] = UserGuide::USER_GUIDE_TASK_STATUS_FINISH;
            }
        }


        //先初始化
        foreach (UserGuide::GUIDE_DEFAULT_SETTING_TASK_LIST as $guideKey => $guideStatus) {
            $result[$guideKey] = $taskList[$guideKey] ?? UserGuide::USER_GUIDE_TASK_STATUS_UN_FINISH;
        }

        //判断是否已经绑定了邮箱

        $userMail = UserMail::model()->find("client_id = {$clientId} and user_id = {$userId} and enable_flag = 1");

        //判断是否从浏览器登陆过
        $browser = LoginEventExternal::model()->find("client_id = {$clientId} and user_id = {$userId} and `key` = 'browser' and value like 'Chrome%'");

        //判断是否从app登陆过
        $app = LoginEventExternal::model()->find("client_id = {$clientId} and user_id = {$userId} and `key` = 'platform' and ( value like 'Android%' or value like 'iOS%')");

        //查询是否有阿里国际站账号
        $aliAccount = AlibabaAccount::model()->find("client_id = {$clientId} and user_id = {$userId}");

        //查询是否已经填了企业和客户信息
        $userProperties = DiscoveryUserProperties::model()->find('client_id=:client_id AND user_id=:user_id', [
            ':client_id' => $clientId,
            ':user_id' => $userId
        ]);

        //查询是否来源为小满发现的线索
        $leadFromXm = Lead::model()->find("client_id = {$clientId} and create_user_id =$userId and company_hash_id != ''");

        //查询是否有线索转化而来的客户
        $companyFromLead = Lead::model()->find("client_id = {$clientId} and user_id && ARRAY[$userId]::bigint[] and company_id!=0 and is_archive = 1");

        //查询是否有营销邮件数据
        $edm = GroupMailTask::model()->find("client_id = {$clientId} and user_id ={$userId}");

        //下载谷歌浏览器
        $result[UserGuide::DOWNLOAD_CHROME] = empty($browser) ?  UserGuide::USER_GUIDE_TASK_STATUS_UN_FINISH : UserGuide::USER_GUIDE_TASK_STATUS_FINISH;
        //授权店铺
        $result[UserGuide::AUTH_ALI_ICBU_STORE] = empty($clientTask[UserGuide::AUTH_ALI_ICBU_STORE]) ? UserGuide::USER_GUIDE_TASK_STATUS_UN_FINISH : UserGuide::USER_GUIDE_TASK_STATUS_FINISH;
        //设置账号对应关系
        $result[UserGuide::ALI_ICBU_ACCOUNT_MAP_SETTING] = empty($clientTask[UserGuide::ALI_ICBU_ACCOUNT_MAP_SETTING]) ? UserGuide::USER_GUIDE_TASK_STATUS_UN_FINISH : UserGuide::USER_GUIDE_TASK_STATUS_FINISH;
        //同步阿里客户通客户
        $result[UserGuide::SYNCHRONIZE_ALI_CUSTOMERS] = empty($clientTask[UserGuide::SYNCHRONIZE_ALI_CUSTOMERS]) ? UserGuide::USER_GUIDE_TASK_STATUS_UN_FINISH : UserGuide::USER_GUIDE_TASK_STATUS_FINISH;
        //同步阿里订单与商品信息
        $result[UserGuide::SYNC_ALI_ORDER_AND_PRODUCTS] = empty($clientTask[UserGuide::SYNC_ALI_ORDER_AND_PRODUCTS]) ? UserGuide::USER_GUIDE_TASK_STATUS_UN_FINISH : UserGuide::USER_GUIDE_TASK_STATUS_FINISH;
        //绑定邮箱
        $result[UserGuide::BIND_MAIL_MANAGE_GUIDE] = empty($userMail) ? UserGuide::USER_GUIDE_TASK_STATUS_UN_FINISH : UserGuide::USER_GUIDE_TASK_STATUS_FINISH;
        //绑定阿里国际站账号
        $result[UserGuide::BIND_ALI_ICBU_ACCOUNT] = empty($aliAccount) ? UserGuide::USER_GUIDE_TASK_STATUS_UN_FINISH : UserGuide::USER_GUIDE_TASK_STATUS_FINISH;
        //开启whatsApp聊天同步助手
        $result[UserGuide::START_WHATSAPP_CHAT_SYNC_ASSISTANT] = empty($clientTask[UserGuide::START_WHATSAPP_CHAT_SYNC_ASSISTANT]) ? UserGuide::USER_GUIDE_TASK_STATUS_UN_FINISH : UserGuide::USER_GUIDE_TASK_STATUS_FINISH;
        //填写企业和客户信息
        $result[UserGuide::FILL_IN_CORPORATE_AND_CUSTOMER_INFO] = empty($userProperties) ? UserGuide::USER_GUIDE_TASK_STATUS_UN_FINISH : UserGuide::USER_GUIDE_TASK_STATUS_FINISH;
        //小满发现建档为线索
        $result[UserGuide::ARCHIVE_LEAD_FROM_XIAOMAN_DISCOVERY] = empty($leadFromXm) ? UserGuide::USER_GUIDE_TASK_STATUS_UN_FINISH : UserGuide::USER_GUIDE_TASK_STATUS_FINISH;
        //线索转客户
        $result[UserGuide::ARCHIVE_COMPANY_FROM_LEAD] = empty($companyFromLead) ? UserGuide::USER_GUIDE_TASK_STATUS_UN_FINISH : UserGuide::USER_GUIDE_TASK_STATUS_FINISH;
        //发送营销邮件
        $result[UserGuide::SEND_EDM] = empty($edm) ? UserGuide::USER_GUIDE_TASK_STATUS_UN_FINISH : UserGuide::USER_GUIDE_TASK_STATUS_FINISH;
        //app下载提示
        $result[UserGuide::DOWNLOAD_GUIDE] = empty($app) ? UserGuide::USER_GUIDE_TASK_STATUS_UN_FINISH : UserGuide::USER_GUIDE_TASK_STATUS_FINISH;


        $newSetting = new \common\library\setting\user\UserSetting($clientId, $userId, \common\library\setting\user\UserSetting::USER_GUIDE_V3);
        //更新最新的数据
        $newSetting->value = $result;
        $newSetting->save();

    }

    public function actionResetUserGuideV2ByUserId($clientId,$userId)
    {

        if (empty($clientId) || empty($userId)) {
            return;
        }

        User::setLoginUserById($userId);
        //全部的任务数据
        $allTaskList = $this->getTaskList($clientId,$userId);

        self::info("全部的任务数据");
        self::info(json_encode($allTaskList));
        if (empty($allTaskList)) {
            return;
        }

        // 查询原有的数据
        $key = \common\library\setting\user\UserSetting::USER_GUIDE_V4;
        $setting = new \common\library\setting\user\UserSetting($clientId, $userId, $key);

        $taskList = $setting->getValue();
        self::info("更新之前的数据");
        self::info(json_encode($taskList));
        if (empty($taskList)) {
            return;
        }


        foreach ($allTaskList as $key => $taskValue) {
            $taskList[$key] = UserGuide::USER_GUIDE_TASK_STATUS_FINISH;
        }

        self::info("更新之后的数据");
        self::info(json_encode($taskList));
        //更新最新的数据

        if (empty($taskList)) {
            return;
        }

        $setting->value = $taskList;
        $setting->save();
    }

    public function actionResetUserGuideV2ByClientId($clientId)
    {
        //所有client下的所有用户
        $sql = "SELECT user_id FROM tbl_user_info WHERE client_id=:client_id AND enable_flag=1";
        $userIds = \Yii::app()->account_base_db->createCommand($sql)->queryAll(true, [':client_id' => $clientId]);

        if (!empty($userIds)) {
            foreach ($userIds as $userId) {
                $this->actionResetUserGuideV2ByUserId($clientId,$userId['user_id']);
                echo "clientId:{$clientId} userId:{$userId['user_id']} complete\n";
            }
        }
    }

    //默认越南盾系统汇率表
    function actionMigrateVndAndIdrCurrency()
    {
        $exchanges = [
            [
                'name' => 'VND',
                'cn_name' => '越南盾',
                'update_time' => date('Y-m-d H:i:s'),
                'bank_conversion_pri' =>  0,
                'f_buy_pri' =>  0,
                'f_sell_pri' =>  0,
                'm_buy_pri' =>  0,
                'm_sell_pri' =>  0,
                'type' => 'CNY'
            ],
            [
                'name' => 'VND',
                'cn_name' => '越南盾',
                'update_time' => date('Y-m-d H:i:s'),
                'bank_conversion_pri' =>  0,
                'f_buy_pri' =>   0,
                'f_sell_pri' =>  0,
                'm_buy_pri' =>  0,
                'm_sell_pri' =>  0,
                'type' => 'USD'
            ],
            [
                'name' => 'IDR',
                'cn_name' => '印尼盾',
                'update_time' => date('Y-m-d H:i:s'),
                'bank_conversion_pri' =>  0,
                'f_buy_pri' =>  0,
                'f_sell_pri' =>  0,
                'm_buy_pri' =>  0,
                'm_sell_pri' =>  0,
                'type' => 'CNY'
            ],
            [
                'name' => 'IDR',
                'cn_name' => '印尼盾',
                'update_time' => date('Y-m-d H:i:s'),
                'bank_conversion_pri' =>  0,
                'f_buy_pri' =>   0,
                'f_sell_pri' =>  0,
                'm_buy_pri' =>  0,
                'm_sell_pri' =>  0,
                'type' => 'USD'
            ],
            [
                'name' => 'BRL',
                'cn_name' => '巴西雷亚尔',
                'update_time' => date('Y-m-d H:i:s'),
                'bank_conversion_pri' =>  0,
                'f_buy_pri' =>  0,
                'f_sell_pri' =>  0,
                'm_buy_pri' =>  0,
                'm_sell_pri' =>  0,
                'type' => 'CNY'
            ],
            [
                'name' => 'BRL',
                'cn_name' => '巴西雷亚尔',
                'update_time' => date('Y-m-d H:i:s'),
                'bank_conversion_pri' =>  0,
                'f_buy_pri' =>  0,
                'f_sell_pri' =>  0,
                'm_buy_pri' =>  0,
                'm_sell_pri' =>  0,
                'type' => 'USD'
            ],
        ];

        //插入系统表数据
        foreach ($exchanges as $item) {
            $values[] = "('" . implode("','", $item) . "')";
        }
        $sql = "REPLACE INTO " . ExchangeRate::model()->tableName()
            . "( `name`,`cn_name`,`update_time`,`bank_conversion_pri`,`f_buy_pri`,`f_sell_pri`,`m_buy_pri`,`m_sell_pri`,`type`) VALUES "
            . implode(',', $values);
        return ExchangeRate::model()->getCommandBuilder()->createSqlCommand($sql)->execute();
    }

    //越南盾初始值

    function actionSetCustomVndAndIdrValueForClient($clientId,$lastNumber =null)
    {
        $clientList = [];

        //灰度指定client
        if ($clientId!=0 && is_string($clientId)) {
            $clientIdList = explode(',',$clientId);
        } else {
            //为0全量
            $clientList = self::getClientList($clientId, false, null, null, 0, 0, $lastNumber);
            $clientIdList = array_column($clientList, 'client_id', '');
        }

        foreach ($clientIdList as $clientId) {
            CustomerOptionService::initCurrencyForVndAndIdr($clientId);
            self::info($clientId." 已完成");
        }
    }

    //初始化企业和个人邮件模版分类
    public function actionInitMailTemplateDefaultGroup($clientIds = 0, $lastNumber = null, $start = 0)
    {
        $clientIdList = [];
        $type = Constants::TYPE_MAIL_TEMPLATE;

        //灰度指定client
        if ($clientIds != 0 && is_string($clientIds)) {
            $clientIdList = explode(',', $clientIds);
        } else {
            //为0全量
            $clientList = self::getClientList($clientIds, false, null, null, $start, 0, $lastNumber);
            $clientIdList = array_column($clientList, 'client_id', '');
        }



        foreach ($clientIdList as $clientId) {

            $adminUserId = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();

            if (!$adminUserId) {
                continue;
            }


            //删除所有已存在的模版数据
            $type = Constants::TYPE_MAIL_TEMPLATE;
            $db = \ProjectActiveRecord::getDbByClientId($clientId);
            if ($db==null) {
                continue;
            }



            $deleteSql = "delete from tbl_group where client_id=$clientId and `type` = $type";
            $affectRows =  $db->createCommand($deleteSql)->execute();

            if ($affectRows) {
                self::info("client_id:".$clientId."删除了".$affectRows."条数据");
            }

            //所有client下的所有用户
            $sql = "SELECT user_id FROM tbl_user_info WHERE client_id=:client_id AND enable_flag=1";
            $userIds = \Yii::app()->account_base_db->createCommand($sql)->queryAll(true, [':client_id' => $clientId]);

            $groupCounts = count($userIds)*3+3;
            $endIncrementGroupId = \ProjectActiveRecord::produceAutoIncrementId($groupCounts);
            $startIncrementGroupId = $endIncrementGroupId - $groupCounts + 1;

            $sqlArr = [];
            $nowTime = date('Y-m-d H:i:s');

            $sqlArr[] = "({$startIncrementGroupId},'节日模版',{$type},$clientId,0,'{$nowTime}',$adminUserId,$adminUserId,1)";
            $startIncrementGroupId++;
            $sqlArr[] = "({$startIncrementGroupId},'新年模版',{$type},$clientId,0,'{$nowTime}',$adminUserId,$adminUserId,2)";
            $startIncrementGroupId++;
            $sqlArr[] = "({$startIncrementGroupId},'其他模版',{$type},$clientId,0,'{$nowTime}',$adminUserId,$adminUserId,3)";
            $startIncrementGroupId++;

            foreach ($userIds as $userId) {
                $sqlArr[] = "({$startIncrementGroupId},'节日模版',{$type},$clientId,{$userId['user_id']},'{$nowTime}',$adminUserId,$adminUserId,1)";
                $startIncrementGroupId++;
                $sqlArr[] = "({$startIncrementGroupId},'新年模版',{$type},$clientId,{$userId['user_id']},'{$nowTime}',$adminUserId,$adminUserId,2)";
                $startIncrementGroupId++;
                $sqlArr[] = "({$startIncrementGroupId},'其他模版',{$type},$clientId,{$userId['user_id']},'{$nowTime}',$adminUserId,$adminUserId,3)";
                $startIncrementGroupId++;
            }

            $this->batchSaveDefaultGroup($clientId,$sqlArr,$db);
        }

    }

    private function batchSaveDefaultGroup($clientId, array $sqlArr,$db)
    {
        $sqlField = 'insert into tbl_group(id,`name`,`type`,client_id,user_id,create_time,create_user,update_user,`rank`) values ';
        if(!empty($sqlArr)){
            $sql = $sqlField.implode(',',$sqlArr);
            try {
                $affectRows =  $db->createCommand($sql)->execute();
            } catch (Exception $e) {
                self::info($e->getMessage());
                return;
            }
            self::info('clientId:'.$clientId.'已更新'.$affectRows."行数据");
        }
    }


    //初始化实施kpi考核
    public function actionUserGuideV2($clientId,$lastNumber =null)
    {
        $clientIdList = [];

        //灰度指定client
        if ($clientId!=0 && is_string($clientId)) {
            $clientIdList = explode(',',$clientId);
        } else {
            //为0全量
            $clientList = self::getClientList($clientId, false, null, null, 0, 0, $lastNumber);
            $clientIdList = array_column($clientList, 'client_id', '');
        }

        foreach ($clientIdList as $client) {
            $this->actionUserGuideV2ByClientId($client);
        }
    }

    public function actionUserGuideV2ByClientId($clientId)
    {
        $sql = "SELECT user_id FROM tbl_user_info WHERE client_id=:client_id AND enable_flag=1";
        $userIds = \Yii::app()->account_base_db->createCommand($sql)->queryAll(true, [':client_id' => $clientId]);
        if (! empty($userIds)) {
            foreach ($userIds as $userId) {
                $this->actionUserGuideV2ByUserId($clientId, $userId['user_id']);
                echo "clientId:{$clientId} userId:{$userId['user_id']} complete\n";
            }
        }
    }

    public function actionUserGuideV2ByUserId($clientId, $userId)
    {
        User::setLoginUserById($userId);
        $values = [];
        foreach (\common\library\setting\user\process\UserGuide::OPERATIONAL_IMPLEMENTATION_KPI_LIST as $key => $value) {
            $values[$key] = 1;
        }

        //客户分组
//        $group = new \common\library\group\GroupList($clientId, \Constants::TYPE_COMPANY);
//        $group->setIncludeSystemGroup(false);
//        $group->setLimit(1);
//        $data = $group->find();
        $data = (new \common\library\setting\library\group\GroupApi($clientId, \Constants::TYPE_COMPANY))->firstOne();
        if (empty($data)) {
            $values[\common\library\setting\user\process\UserGuide::CUSTOMER_GROUP] = 0;
        }

        //客户状态
//        $listObj = new CustomerStatusList($clientId);
//        $listObj->setUserId($userId);
//        $listObj->setLimit(1);
//        $data = $listObj->find();
        $data = (new \common\library\setting\library\status\StatusApi($clientId, \Constants::TYPE_COMPANY))->firstOne();
        if (empty($data)) {
            $values[\common\library\setting\user\process\UserGuide::CUSTOMER_STATUS] = 0;
        }

        //公海管理
//        $poolList = new \common\library\customer\pool\PoolList($clientId);
//        $poolList->setUserId($userId);
//        $poolList->setLimit(1);
//        $data = $listObj->find();
        $data = (new \common\library\setting\library\pool\PoolApi($clientId))->list(\common\library\customer\pool\Helper::getUserPoolIds($clientId, $userId, true));

        //跟进规则
        $client = \common\library\account\Client::getClient($clientId);
        $attributes = $client->getExtentAttributes();
        if (!isset($attributes['refer_send_mail'])) {
            $values[\common\library\setting\user\process\UserGuide::CUSTOMER_FOLLOW] = 0;
        }

        //设置客户字段
        $type = \Constants::TYPE_COMPANY;
        $fields = \common\models\client\CustomField::model()->find("client_id = $clientId and type = {$type} and base=0");
        if (empty($fields)) {
            $values[\common\library\setting\user\process\UserGuide::CUSTOMER_FIELDS] = 0;
        }

        //客户来源
//        $listObj = new OriginList($clientId);
//        $listObj->setUserId($userId);
//        $listObj->setLimit(1);
//        $data = $listObj->find();
        $data = (new \common\library\setting\library\pool\PoolApi($clientId))->list(\common\library\customer\pool\Helper::getUserPoolIds($clientId, $userId, true));
        if (empty($data)) {
            $values[\common\library\setting\user\process\UserGuide::CUSTOMER_ORIGINS] = 0;
        }

        //操作产品同步
        $total =  ProductCrawlTask::findTotalByClientId($clientId);
        if (empty($total)) {
            $values[\common\library\setting\user\process\UserGuide::PRODUCT_IMPORT] = 0;
        }

        //开启安全登陆
        $user = new \common\library\account\UserInfo(User::getLoginUser()->getUserId());
        if ($user->safe_login!=1) {
            $values[\common\library\setting\user\process\UserGuide::SAFE_LOGIN] = 0;
        }

        //开启微信绑定
        $account = (new \common\library\account\Account())->loadById($userId);
        if (!$account->wx_openid) {
            $values[\common\library\setting\user\process\UserGuide::WECHAT_LOGIN] = 0;
        }

        $userSetting = new UserSetting($clientId, $userId, UserSetting::OPERATING_KPI_USER_GUIDE);
        $userSetting->value = $values;
        $userSetting->save();

    }

    public function fixAppLoginData($clientId = 0, $start = 0, $lastNumber = null)
    {
        $clientIdList = [];

        //灰度指定client
        if ($clientId != 0 && is_string($clientId)) {
            $clientIdList = explode(',', $clientId);
        } else {
            //为0全量
            $clientList = self::getClientList($clientId, false, null, null, $start, 0, $lastNumber);
            $clientIdList = array_column($clientList, 'client_id', '');
        }

        foreach ($clientIdList as $clientId) {
            //所有client下的所有用户
            $db = \ProjectActiveRecord::getDbByClientId($clientId);
            $sql = "SELECT user_id FROM tbl_user_info WHERE client_id=:client_id AND enable_flag=1";
            $userIds = \Yii::app()->account_base_db->createCommand($sql)->queryAll(true, [':client_id' => $clientId]);

            //登陆superadmin
            $adminUserId = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();
            if ($adminUserId) {
                \User::setLoginUserById($adminUserId);
            } else {
                continue;
            }
        }

        if (!empty($userIds)) {
            foreach ($userIds as $userId) {

                $key = \common\library\setting\user\UserSetting::USER_GUIDE_V3;
                $setting = new \common\library\setting\user\UserSetting($clientId, $userId['user_id'], $key);
                $taskList = $setting->getValue();

                //不是跳过 就校正app下载数据
                if (isset($taskList[UserGuide::DOWNLOAD_GUIDE]) && $taskList[UserGuide::DOWNLOAD_GUIDE]!=2 ) {
                    $app = LoginEventExternal::model()->find("client_id = {$clientId} and user_id = {$userId} and `key` = 'platform' and (value like 'Android%' or value like 'iOS%')");

                    echo "clientId:{$clientId} userId:{$userId['user_id']} complete\n";
                    $taskList[UserGuide::DOWNLOAD_GUIDE] = empty($app) ? UserGuide::USER_GUIDE_TASK_STATUS_UN_FINISH : UserGuide::USER_GUIDE_TASK_STATUS_FINISH;

                    //更新最新的数据
                    $setting->value = $taskList;
                    $setting->save();
                }
            }
        }
    }

    //修复客户编号特殊字符数据
    public function actionFixSpecialCharacterOfTheCompanySerialId($clientId)
    {
        $db = PgActiveRecord::getDbByClientId($clientId);
        $updateSql = "update tbl_company set  serial_id = replace(serial_id,chr(8),'') where client_id={$clientId} and  serial_id like '%wm%' ";
        $db->createCommand($updateSql)->execute();
    }

    public function actionSetNoviceStatus($clientId = 0, $start = 0, $lastNumber = null)
    {


        $clientIdList = [];

        //灰度指定client
        if ($clientId != 0 && is_string($clientId)) {
            $clientIdList = explode(',', $clientId);
        } else {
            //为0全量
            $clientList = self::getClientList($clientId, false, null, null, $start, 0, $lastNumber);
            $clientIdList = array_column($clientList, 'client_id', '');
        }

        foreach ($clientIdList as $clientId) {

            self::info("开始更新clientId:".$clientId);
            //所有client下的所有用户
            $db = \ProjectActiveRecord::getDbByClientId($clientId);
            $sql = "SELECT user_id FROM tbl_user_info WHERE client_id=:client_id AND enable_flag=1";
            $userIds = \Yii::app()->account_base_db->createCommand($sql)->queryAll(true, [':client_id' => $clientId]);

            //登陆superadmin
            $adminUserId = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();
            if ($adminUserId) {
                \User::setLoginUserById($adminUserId);
            } else {
                continue;
            }

            if (!empty($userIds)) {
                foreach ($userIds as $userId) {

                    try{
                        $key = \common\library\setting\user\UserSetting::USER_GUIDE_V3;
                        $setting = new \common\library\setting\user\UserSetting($clientId, $userId['user_id'], $key);
                        $taskList = $setting->getValue();

                        //如果为不展示状态，则将状态置为1
                        if (isset($taskList[UserGuide::SHOW_NOVICE_ENTRY_PORTAL]) && $taskList[UserGuide::SHOW_NOVICE_ENTRY_PORTAL]!=1 ) {

                            $taskList[UserGuide::SHOW_NOVICE_ENTRY_PORTAL] = 1;
                            //更新最新的数据
                            $setting->value = $taskList;
                            $setting->save();
                            self::info("userID:".$userId['user_id']."SHOW_NOVICE_ENTRY_PORTAL已更新");
                        }
                    } catch (Throwable $e){
                        self::info($e->getMessage());
                    }
                }
            }
        }
    }


    //收件规则数据的转换
    //脚本不可重入，需要过滤已经跑过脚本，这里引入redis set记录已经跑过的clientID
    public function actionConvertHistoryMailRule($clientIds = 0, $start = 0, $lastNumber = null,$filterRun = true)
    {
        $clientIdList = [];
        $greyClientIds = [];
        $key = "grey:convert:history:mailRule";
        $redis = \RedisService::getInstance('redis');

        //灰度指定client
        if ($clientIds != 0 && is_string($clientIds)) {
            $clientIdList = explode(',', $clientIds);
        } else {
            //为0全量
            $clientList = self::getClientList($clientIds, false, null, null, $start, 0, $lastNumber);
            $clientIdList = array_column($clientList, 'client_id', '');
        }

        if ($filterRun) {
            $greyClientIds = $redis->smembers($key);
            self::info("已灰度名单：");
            self::info(implode(',',$greyClientIds));
        }

        foreach ($clientIdList as $clientId) {

            if ($filterRun && in_array($clientId, $greyClientIds, true)) {
                self::info("client_id:".$clientId."已存在,跳过");
                continue;
            }

            //所有client下的所有用户
            $sql = "SELECT user_id FROM tbl_user_info WHERE client_id=:client_id AND enable_flag=1";
            $userIds = \Yii::app()->account_base_db->createCommand($sql)->queryAll(true, [':client_id' => $clientId]);

            if (!empty($userIds)) {
                foreach ($userIds as $userId) {
                    try{
                        $this->actionProcessOldrule($clientId, $userId['user_id']);
                    } catch (Throwable $e){
                        self::info($e->getMessage());
                    }
                }
            }

            if ($filterRun) {
                $redis->sadd($key,[$clientId]);
            }
        }

    }

    public function actionProcessOldrule($clientId,$userId)
    {
        self::info("开始更新【clientId】:【userId】 ".$clientId.":".$userId);

        User::setLoginUserById($userId);
        $listObj = new MailRuleList($userId);
        $listObj->setType(MailRule::TYPE_RECEIVE);
        $listObj->setClientId($clientId);
        $listObj->setEnableFlag(1);
        $listObj->setLimit(500);
        $listObj->setOffset(0);
        $listObj->setOrderBy(['mail_rule_type desc','order_num asc', 'rule_id desc']);
        $list = $listObj->find();

        $updateRules = [];
        $replaceFilterTypeMap = [
            MailRuleOperator::RULE_FILTER_TYPE_CONTAIN => MailRuleOperator::RULE_FILTER_TYPE_EQUAL,
            MailRuleOperator::RULE_FILTER_TYPE_NOT_CONTAIN => MailRuleOperator::RULE_FILTER_TYPE_NOT_EQUAL
        ];
        foreach ($list as $rule) {

            $filterSetting = is_array($rule['filter_settings'])?$rule['filter_settings']: json_decode($rule['filter_settings'], true);
            if (empty($filterSetting))
                continue;

//            echo "更新之前的filter_setting数据:".json_encode($filterSetting).PHP_EOL;
            $isUpdated = false;
            foreach ($filterSetting as &$filter) {

                $filterKey = $filter['key'];

                //发件域->发件人
                if ($filterKey == 'filter_sender_domain') {
                    $filter['key'] = 'filter_sender';
                    $isUpdated = true;
                    continue;
                }

                //发件人/收件人/抄送人 的 包含/不包含->等于/不等于
                if (in_array($filterKey,['filter_sender','filter_receiver','filter_cc']) && isset($replaceFilterTypeMap[$filter['type']])) {
                    $filter['type'] = $replaceFilterTypeMap[$filter['type']];
                    $isUpdated = true;
                }
            }

            if ($isUpdated) {
                $updateRules[$rule['rule_id']] = json_encode($filterSetting,JSON_UNESCAPED_UNICODE);
//                echo "更新之后的filter_setting数据:".json_encode($filterSetting,JSON_UNESCAPED_UNICODE).PHP_EOL;
            }
        }

        if (!empty($updateRules)) {

            $ids = implode(',', array_keys($updateRules));
            $sql = "UPDATE tbl_mail_rule SET filter_settings = CASE rule_id ";
            foreach ($updateRules as $ruleId => $newFilterSetting) {
                $sql .= sprintf("WHEN %d THEN %s ", $ruleId, "'".$newFilterSetting."'");
            }
            $sql .= "END WHERE rule_id IN ($ids)";
//            echo $sql;
            $db = ProjectActiveRecord::getDbByClientId($clientId);
            $db->createCommand($sql)->execute();

            //支持桌面端更新收发件规则
            $userModuleVersion = new \common\library\version\UserModuleVersion($userId);
            $userModuleVersion->setModule(\common\library\version\Constant::USER_MODULE_MAIL_RULE);
            $userModuleVersion->add();

            //删除缓存，让收发件规则加载最新代码
            \common\library\mail\setting\rule\MailRuleOperator::delRuleCache($clientId, $userId);
        }



    }



    public function actionMailRule(): void
    {
        User::setLoginUserById(********);
        \common\library\mail\setting\rule\MailRuleOperator::delRuleCache( 1,********);
        $mail = new \common\library\mail\Mail(1129031551,********);
        $ruleService = new \common\library\mail\setting\rule\MailRuleOperator($mail);
        $ruleService->matchAndExecute();
    }

    public function actionRessetOrderTaskStatus($clientId,$userId)
    {

        self::info("开始clientId:userId{$clientId}:{$userId}重置订单导出任务状态为导出中的任务为失败");
        $coll = CustomerExport::getDbByUserId($userId);
        $type =  CustomerExportTask::TYPE_ORDER;
        $status = CustomerExport::STATUS_INIT;
        $failStatus = CustomerExport::STATUS_EXPORT_FAIL;

        $sql = "update tbl_customer_export set status = {$failStatus} where  user_id=:u and type = {$type} and status = {$status}";
        $params[':u'] = $userId;

        $ret = $coll->createCommand($sql)->execute($params);

        self::info("结束重置任务，一共更新了{$ret}条数据");

    }

    /**
     * @param $clientId
     * @param $companyIds
     * 修复客户的计入业绩订单数据
     */
    public function actionFixRefreshStaticCount($clientId,$companyIds)
    {
        self::info("开始clientId:".$clientId.":companyIds为:".$companyIds."的计入业绩订单修复");
        $companyIds = explode(',',$companyIds);
        \common\library\customer\Helper::refreshStaticCount($clientId, $companyIds, [Constants::TYPE_ORDER]);
        self::info("clientId:".$clientId.":companyIds为:".implode(',',$companyIds)."的计入业绩订单修复"."结束");
    }


    public function actionMigrateUserGuideV3ToUserGuideV4($clientIds = 0, $lastNumber = null, $start = 0)
    {

        $clientIdList = [];
        //灰度指定client
        if ($clientIds != 0 && is_string($clientIds)) {
            $clientIdList = explode(',', $clientIds);
        } else {
            //为0全量
            $clientList = self::getClientList($clientIds, false, null, null, $start, 0, $lastNumber);
            $clientIdList = array_column($clientList, 'client_id', '');
        }

        foreach ($clientIdList as $clientId) {

            //所有client下的所有用户
            $db = \ProjectActiveRecord::getDbByClientId($clientId);
            $sql = "SELECT user_id FROM tbl_user_info WHERE client_id=:client_id AND enable_flag=1";
            $userIds = \Yii::app()->account_base_db->createCommand($sql)->queryAll(true, [':client_id' => $clientId]);

            $userIds = array_column($userIds,'user_id');

            if (!empty($userIds)) {
                foreach ($userIds as $userId) {
                    try {

                        //1.迁移v3数据到v4
                        User::setLoginUserById($userId);
                        // 查询原有的数据
                        $key = \common\library\setting\user\UserSetting::USER_GUIDE_V3;
                        $setting = new \common\library\setting\user\UserSetting($clientId, $userId, $key);
                        $taskList = $setting->getValue();

                        if (!is_array($taskList)) {
                            $taskList = [];
                        }

                        //没有数据补偿措施
                        if (empty($taskList)) {
                            foreach (UserGuide::GUIDE_DEFAULT_SETTING_TASK_LIST as $guideKey => $guideStatus) {
                                $taskList[$guideKey] = $guideStatus;
                            }
                        }


                        $newSetting = new \common\library\setting\user\UserSetting($clientId, $userId, \common\library\setting\user\UserSetting::USER_GUIDE_V4);
                        $newSetting->value = $taskList;
                        $newSetting->save();
                        echo "clientId:{$clientId} userId:{$userId} migrate task complete\n";

                        //2. 将老用户的分型提醒弹窗全部置为已完成
                        //完成任务分型弹窗
                        $dialogKey = \common\library\setting\user\UserSetting::USER_GUIDE_TIPS_STATUS;
                        $dialogSetting = new \common\library\setting\user\UserSetting($clientId, $userId, $dialogKey);
                        $dialogSetting->value = UserGuide::USER_GUIDE_TASK_STATUS_FINISH;
                        $dialogSetting->save();
                        self::info("userID:".$userId."USER_GUIDE_TIPS_STATUS已更新");

                    }catch(Throwable $e) {
                        self::info("userID:".$userId.$e->getMessage());
                    }

                }
            }
        }
    }


    /**
     * @param int $clientIds
     * @param null $lastNumber
     * @param int $start
     * @throws ProcessException
     * 去除用户订阅下线订阅报表的数据
     */
    public function actionRemoveOldReportKeyForReportSubscribe($clientIds = 0, $lastNumber = null, $start = 0)
    {
        //构建废弃报表map
        $offlineReportKeys = [
            'yjts1',
            'yjts2',
            'yjts3',
            'yjts4',
            'yjts5',
            'yjts6',
            'yj1',
            'yj2',
            'yj3',
            'yj4',
            'yj5',
            'yj6',
            'xs10',
            'xs3',
            'dd2'
        ];

        //灰度指定client
        if ($clientIds != 0 && is_string($clientIds)) {
            $clientIdList = explode(',', $clientIds);
        } else {
            //为0全量
            $clientList = self::getClientList($clientIds, false, null, null, $start, 0, $lastNumber);
            $clientIdList = array_column($clientList, 'client_id', '');
        }

        $reportKeyStr = "'" .implode("','", $offlineReportKeys  ) . "'";

        $accountBaseDb = \Yii::app()->account_base_db;
        $updateTime = date('Y-m-d H:i:s');
        foreach ($clientIdList as $clientId) {

            //所有client下的所有用户
            $sql = "SELECT user_id FROM tbl_user_info WHERE client_id=:client_id";
            $userIds = $accountBaseDb->createCommand($sql)->queryColumn([':client_id' => $clientId]);
            if (empty($userIds)) {
                continue;
            }

            $userIdStr= implode(',',$userIds);

            $db = PgActiveRecord::getDbByClientId($clientId);

            //寻找出废弃报表
            //select * from tbl_report_subscribe where user_id in(1,3,10,14,377,482,483,484,734,765,30393,30421,46900,51026,51060,51068,51106,51305,71475,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,11863701,11863707,11863759,11863786,11863794,11863822,11863955,11863993,11863996,11863999,11864011,11864012,11864013,11864014,11864022,11864030,11864032,11864038,11864046,249503788,249503790,249503792,249503807,249503809,249503810,249503811,249503812) and report_key in ('yjts1','yjts2','yjts3','yjts4','yjts5','yjts6','yj1','yj2','yj3','yj4','yj5','yj6','xs10','xs3','dd2')
//            $querySql = "select * from tbl_report_subscribe where user_id in({$userIdStr}) and report_key in ({$reportKeyStr}) and enable_flag=1 ";
//            $data = $db->createCommand($querySql)->queryAll();

            //按照client纬度将废弃报表数据删除
            //update tbl_report_subscribe set enable_flag = 0,update_time='2021-12-14 11:26:51' where user_id in(1,3,10,14,377,482,483,484,734,765,30393,30421,46900,51026,51060,51068,51106,51305,71475,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,11863701,11863707,11863759,11863786,11863794,11863822,11863955,11863993,11863996,11863999,11864011,11864012,11864013,11864014,11864022,11864030,11864032,11864038,11864046,249503788,249503790,249503792,249503807,249503809,249503810,249503811,249503812) and report_key in ('yjts1','yjts2','yjts3','yjts4','yjts5','yjts6','yj1','yj2','yj3','yj4','yj5','yj6','xs10','xs3','dd2') and enable_flag=1
            $updateSql = "update tbl_report_subscribe set enable_flag = 0,update_time='{$updateTime}' where user_id in({$userIdStr}) and report_key in ({$reportKeyStr}) and enable_flag=1";
            $count = $db->createCommand($updateSql)->execute([]);

            self::info("ClientId[$clientId] userId[{$userIdStr}] 更新了[{$count}]条废弃报表数据");
            PgActiveRecord::releaseDbByClientId($clientId);
            \common\library\account\Client::cleanCacheMap();
            User::cleanUserMap();
        }

    }

    //给用户初始化倒计时组件
    public function actionFixTeamWallSetting($clientIds = 0, $lastNumber = null, $start = 0)
    {

        if ($clientIds != 0 && is_string($clientIds)) {
            $clientIdList = explode(',', $clientIds);
        } else {
            //为0全量
            $clientList = self::getClientList($clientIds, true, null, null, $start, 0, $lastNumber);
            $clientIdList = array_column($clientList, 'client_id', '');
        }

        foreach ($clientIdList as $clientId) {

            $admin_user_id =  common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();
            if (empty($admin_user_id)) {
                continue;
            }
            User::setLoginUserById($admin_user_id);

            $client = \common\library\account\Client::getClient($clientId);
            $clientSetting = $client->getSettingAttributes([\common\library\account\Client::SETTING_KEY_PDCA_TEAM_WALL_SETTING]);
            $teamWallSetting = $clientSetting[\common\library\account\Client::SETTING_KEY_PDCA_TEAM_WALL_SETTING];

            if (empty($teamWallSetting)) {
                self::info($clientId."团队墙没有配置");
                self::info($clientId."开始初始化团队墙");
                common\library\performance_v2\Helper::initClientTeamWallSetting($clientId);
                self::info($clientId."完成团队墙初始化");
                continue;
            }

            //判断有没有倒计时组件
            $hasTimeWidget = false;
            foreach ($teamWallSetting as $item) {
                if ($item['type']==PerformanceV2Constant::TEAM_WALL_SETTING_TYPE_TIME_WIDGET) {
                    $hasTimeWidget = true;
                    break;
                }
            }

            if ($hasTimeWidget) {
                self::info($clientId."已经有倒计时组件了");
                continue;
            }

            $timeWidget = [
                "type" => PerformanceV2Constant::TEAM_WALL_SETTING_TYPE_TIME_WIDGET,
                "name" => \Yii::t("performanceV2", "倒计时"),
                "id" => PerformanceV2Constant::TEAM_WALL_SETTING_TYPE_TIME_WIDGET . "-*************-1",
                "count_down_type" => 1,
                "enable_flag" => 1,
                "layout" => [
                    "x" => 0,
                    "y" => 0,
                    "w" => 1,
                    "h" => 1,
                    "i" => PerformanceV2Constant::TEAM_WALL_SETTING_TYPE_TIME_WIDGET . "-*************-1"
                ]
            ];

            $teamWallSetting = array_merge([$timeWidget],$teamWallSetting);
            $client->setSettingAttributes([\common\library\account\Client::SETTING_KEY_PDCA_TEAM_WALL_SETTING => $teamWallSetting]);
            $client->saveSettingAttributes();

            self::info($clientId."已更新倒计时组件");
            self::info(json_encode($teamWallSetting));

        }
    }

    // 将main_user加到user_id中去
    public function actionFixOpportunity($clientId = 33662)
    {
        $opportunityIdsSql ="(*************,*************,*************,*************,*************,*************,*************,*************,*************,*************,*************,*************,*************,*************,*************,*************,*************,*************,*************,*************,*************,*************,*************,*************,*************,*************,*************,*************,*************,*************,*************,*************,*************,*************,*************,************,*************,*************,*************,*************,*************,*************,*************,********91056,1284047604522,1130068815963,1130080884818,1130098003434,1330734486141,1284073134699,1284129667744,1130018543941,1276455001233,1276475161286,1255929004184,1314241304776,1289119615868,1261535989180,1294012616077,1247493524533)";
        $pgDb = PgActiveRecord::getDbByClientId($clientId);
        $opportunityProductSql = "update tbl_opportunity set user_id=array_append(user_id, main_user) where client_id={$clientId} opportunity_id in $opportunityIdsSql and  not ARRAY[main_user]::bigint[] <@ user_id";
        $affectRows = $pgDb->createCommand($opportunityProductSql)->execute();
        self::info("更新sql语句".$opportunityProductSql);
        self::info("影响了".$affectRows."行数据");
    }

    /**
     * @param $clientId
     * @param $value
     * 重置自增值
     */
    public function actionResetSerialRule($clientId = 71303)
    {

        //将用户的的所有客户数据的serial_id 设置为空
        $pgDb = PgActiveRecord::getDbByClientId($clientId);
        $updateSql = "update tbl_company set serial_id=0 where  client_id=$clientId";
        $affectRow = $pgDb->createCommand($updateSql)->execute();
        self::info("存在客户编号清空，影响客户数据量为:".$affectRow." 操作sql:为".$updateSql);

        $redis = \RedisService::getInstance('redis');
        $type = Constants::TYPE_COMPANY;
        $yearExpireAt = strtotime(date('Y-01-01 00:00:00',strtotime('+1 year')));
        $monthExpireAt = strtotime(date('Y-m-01 00:00:00', strtotime('first day of next month')));
        $dayExpireAt = strtotime(date('Y-m-d 00:00:00', strtotime('+1 day')));

        $keyMap = [
            'all' => "serial_number:client:{$clientId}:type:{$type}:increment_always",
            'year' => "serial_number:client:{$clientId}:type:{$type}:increment_by_year",
            'month' => "serial_number:client:{$clientId}:type:{$type}:increment_by_month",
            'day' => "serial_number:client:{$clientId}:type:{$type}:increment_by_day",
        ];


        $allCount = 0;
        $yearCount =0;
        $monthCount = 0;
        $dayCount = 0;


        $redis->set($keyMap['all'], $allCount);
        self::info("allcount:".$keyMap['all'].' value:'.$allCount);

        //查询该client_id。今年的客户数
        $redis->set($keyMap['year'], $yearCount);
        $redis->expireat($keyMap['year'], $yearExpireAt);
        self::info("yearCount:".$keyMap['year'].' value:'.$yearCount);


        //查询该client_id。这个月的客户数
        $redis->set($keyMap['month'], $monthCount);
        $redis->expireat($keyMap['month'], $monthExpireAt);
        self::info("monthCount:".$keyMap['month'].' value:'.$monthCount);


        //查询该client_id。今天的客户数
        $redis->set($keyMap['day'], $dayCount);
        $redis->expireat($keyMap['day'], $dayExpireAt);
        self::info("dayCount:".$keyMap['day'].' value:'.$dayCount);

        self::info("clientId:".$clientId."自定义编号规则重置完毕");

    }

    /**
     * @param $clientId
     * @param $startDate
     * @param $endDate
     * 修复公海领取客户数据
     */
    public function actionFixPublicCompanyDataCount($clientId,$startDate='2022-01-01',$endDate = '2022-01-06')
    {

        //找出满足时间的移入公海数据
        $pgDb = PgActiveRecord::getDbByClientId($clientId);
        $table = 'tbl_company_history';
        $type = [
            CompanyHistoryPg::TYPE_FOLLOW_FROM_PUBLIC,
            CompanyHistoryPg::TYPE_ALLOCATION,
        ];

        $startDateTime = $startDate." 00:00:00";
        $endDateTime = $endDate." 23:59:59";

        //查询这个时间段创建的客户数据
        $countryCompanySql = "select company_id from tbl_company   where client_id=$clientId and archive_time >= '2021-01-01 00:00:00'";
        $companyData = $pgDb->createCommand($countryCompanySql)->queryAll();

        if (empty($companyData)) {
            self::info("没有用户数据");
            return;
        }

        $companyIds = implode(',',array_column($companyData,'company_id'));
        $types = implode(',',$type);
        $sql ="select create_time,company_id,update_user,diff,type from {$table} where client_id=$clientId and company_id in ($companyIds) and type in ({$types}) and create_time between '{$startDateTime}' and '{$endDateTime}'";
        $publicCompanyData = $pgDb->createCommand($sql)->queryAll();

        //按照时间日期分组
        $typeGroupByDatePublicCompanyData = [];
        foreach ($publicCompanyData as $companyDatum) {
            $diffData =  current(json_decode($companyDatum['diff'],true));

            if (empty($diffData)) continue;
            $userIds = $diffData['new']??[];
            if (empty($userIds)) continue;

            $date = date('Ymd',strtotime($companyDatum['create_time']));
            $type = $companyDatum['type'];

            foreach ($userIds as $userId) {
                $typeGroupByDatePublicCompanyData[$type][$date][$userId][] = $companyDatum['company_id'];
            }
        }

        foreach ($typeGroupByDatePublicCompanyData as $type =>  $groupByDatePublicCompanyData) {

            foreach ($groupByDatePublicCompanyData as $date => $companyDatum) {
                foreach ($companyDatum as $userId => $companyIds) {
                    //按照user_id纬度更新统计数据
                    CompanyHelper::IncCompanyKeyCount($clientId, $userId, 'public_company_receive_count', $companyIds,$date);
                    if($type == CompanyHistoryPg::TYPE_FOLLOW_FROM_PUBLIC) {
                        CompanyHelper::IncCompanyKeyCount($clientId, $userId, 'public_company_allocate_receive_count', $companyIds,$date);
                    } else {
                        CompanyHelper::IncCompanyKeyCount($clientId, $userId, 'public_company_assigned_receive_count', $companyIds,$date);
                    }
                }
            }
        }


        self::info("完成公海领取客户数据的更新，sql为：".$sql);

    }

    public function actionResetPerformanceCount($clientIds = 0, $lastNumber = null, $start = 0,$resetFlag = 0)
    {
        $clientIdList = $this->getClientIdList($clientIds, $lastNumber, $start);

        if (empty($clientIdList)) {
            echo "没有clientId数据";
            return;
        }

        foreach ($clientIdList as $clientId) {

            $adminId = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();
            if (!$adminId)
                continue;

            User::setLoginUserById($adminId);
            $masterUser = User::getLoginUser();
            if ($masterUser->isEmpty())
                continue;


            $db = PgActiveRecord::getDbByClientId($clientId);

            //先查后更新 更新所有的订单的account_flag=0

            $maxOrderId = $db->createCommand('select order_id from tbl_order where client_id='.$clientId.' order by order_id desc limit 1')->queryScalar();
            self::info('select order_id from tbl_order where client_id='.$clientId.' order by order_id desc limit 1');

            $currOrderId = 0;
            $filed = 'order_id';
            $sql = 'select ' .$filed.' from tbl_order where  client_id='.$clientId;

            while ( $currOrderId < $maxOrderId ) {
                $orderList = $db->createCommand($sql.' and order_id >'.$currOrderId.' order by order_id asc limit 10000')->queryColumn();
                self::info($sql.' and order_id >'.$currOrderId.' order by order_id asc limit 10000');
                if (empty($orderList)) {
                    break;
                }

                $currOrderId =end($orderList);
                $orderIdsStr = implode(',',$orderList);

                if ($currOrderId!=0) {
                    $resetOrderSql ="update tbl_order set account_flag=0 where  order_id in ({$orderIdsStr})";
                    self::info($resetOrderSql);
                    $res = $db->createCommand($resetOrderSql)->execute();
                } else {
                    break;
                }
            }

            //先查后更新 重置所有所有company的performance_order_count
            $maxId = $db->createCommand('select company_id from tbl_company where client_id='.$clientId.' order by company_id desc limit 1')->queryScalar();
            self::info('select company_id from tbl_company where client_id='.$clientId.' order by company_id desc limit 1');

            $currId = 0;
            $filed = 'company_id';
            $sql = 'select ' .$filed.' from tbl_company where  client_id='.$clientId;

            while ( $currId < $maxId ) {
                $list = $db->createCommand($sql.' and company_id >'.$currId.' order by company_id asc limit 10000')->queryColumn();
                self::info($sql.' and company_id >'.$currId.' order by company_id asc limit 10000');
                if (empty($list)) {
                    break;
                }
                $currId =end($list);
                $companyIdStr = implode(',',$list);

                if ($currId!=0) {
                    $resetCompanySql ="update tbl_company set  performance_order_count = 0 where  company_id in ({$companyIdStr})";
                    self::info($resetCompanySql);
                    $res = $db->createCommand($resetCompanySql)->execute();
                } else {
                    break;
                }
            }

            //查询业绩表里面的数据
            //找出预设规则为订单金额的rule_id
            $sql = "select rule_id,time_field from tbl_performance_v2_rule where client_id={$clientId} and enable_flag=1  and type=" . \common\library\performance_v2\PerformanceV2Constant::RULE_TYPE_ORDER_AMOUNT;
            $ruleId = $db->createCommand($sql)->queryScalar();
            self::info($sql);

            if ($ruleId) {
                //成交客户数
                $recordType = \common\library\performance_v2\PerformanceV2Constant::RECORD_TYPE_USER;
                $dealCompanyCountTotalSql = "select array_to_string(array_agg(distinct refer_id),',') as order_ids,company_id,count(distinct refer_id) as company_count, sum(indicator_value) as amount ,max(account_date) as max_account_date from tbl_performance_v2_record WHERE client_id = {$clientId} and rule_id={$ruleId} and record_type ={$recordType}  group by company_id";
                self::info($dealCompanyCountTotalSql);

                $list = $db->createCommand($dealCompanyCountTotalSql)->queryAll();
            } else {
                self::info("跳过client_id:".$clientId."没有绩效规则");
                continue;
            }

            if (empty($list)) {
                self::info("跳过client_id:".$clientId."没有绩效规则数据");
                continue;
            }

            $orderIds = [];
            foreach ($list as  $item) {


                $companyId = $item['company_id'] ?? 0;
                $count = $item['company_count'] ?? 0;
                $orderIds = array_merge($orderIds,explode(',',$item['order_ids']));

                if (empty($companyId))  {
                    continue;
                }

                $set =[];

                $set[] = " performance_order_count = '{$count}'";
                if (!empty($set))
                {
                    $set = implode(',', $set);
                    $updateSqlArray[] = "update tbl_company set $set where company_id=$companyId";
                }
            }

            //更新满足条件的订单的account_flag=1
            $orderIdStrChunk = array_chunk($orderIds,1000);
            foreach ($orderIdStrChunk as $OrderItem) {
                $orderIdStr = implode(',',$OrderItem);
                $updateAccountFlagSql = "update tbl_order set account_flag=1 where  order_id in ($orderIdStr)";
                $res = $db->createCommand($updateAccountFlagSql)->execute();
                self::info($updateAccountFlagSql);
            }

            if (empty($updateSqlArray)) {
                continue;
            }

            $updateSqlArrayChunk = array_chunk($updateSqlArray,1000);
            foreach ($updateSqlArrayChunk as $item) {
                $updateSql = implode('; ', $item);
                self::info($updateSql);
                $res = $db->createCommand($updateSql)->execute();
            }

            self::info("clientId:$clientId; res:$res; ");
        }

    }

    private function getClientIdList($clientIds = 0, $lastNumber = null, $start = 0)
    {
        $clientIdList = [];
        //灰度指定client
        if ($clientIds != 0 && is_string($clientIds)) {
            $clientIdList = explode(',', $clientIds);
        } else {
            //为0全量
            $clientList = self::getClientList($clientIds, false, null, null, $start, 0, $lastNumber);
            $clientIdList = array_column($clientList, 'client_id', '');
        }

        return $clientIdList;
    }


    /**
     * @param $clientId
     * @param $referId
     * @param $ruleId
     * 测试refer
     */
    public function actionTestRunPerformanceRuleWithReferId($clientId, $orderId, $ruleId)
    {
        $adminUserId = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();
        \User::setLoginUserById($adminUserId);

        $orderObject = new \common\library\invoice\Order($adminUserId, $orderId);
        $orderPerformance = new common\library\performance_v2\refer\OrderPerformance($orderObject);
        $performanceRecorder = new PerformanceRecorder($orderPerformance);
        $performanceRecorder->testRunRule($ruleId);
    }


    public function actionCheckUserGuideV4ByUserId($clientId, $userId)
    {
        User::setLoginUserById($userId);
        $result = [];

        //判断是否已经绑定了邮箱
        $userMail = UserMail::model()->find("client_id = {$clientId} and user_id = {$userId} and enable_flag = 1");
        //判断是否从浏览器登陆过
        $browser = LoginEventExternal::model()->find("client_id = {$clientId} and user_id = {$userId} and `key` = 'browser' and value like 'Chrome%'");
        //判断是否从app登陆过
        $app = LoginEventExternal::model()->find("client_id = {$clientId} and user_id = {$userId} and `key` = 'platform' and ( value like 'Android%' or value like 'iOS%')");
        //查询是否有阿里国际站账号
        $aliAccount = AlibabaAccount::model()->find("client_id = {$clientId} and user_id = {$userId}");
        //查询是否已经填了企业和客户信息
        $userProperties = DiscoveryUserProperties::model()->find('client_id=:client_id AND user_id=:user_id', [
            ':client_id' => $clientId,
            ':user_id' => $userId
        ]);
        //查询是否来源为小满发现的线索
        $leadFromXm = Lead::model()->find("client_id = {$clientId} and create_user_id =$userId and company_hash_id != ''");
        //查询是否有线索转化而来的客户
        $companyFromLead = Lead::model()->find("client_id = {$clientId} and user_id && ARRAY[$userId]::bigint[] and company_id!=0 and is_archive = 1");
        //查询是否有营销邮件数据
        $edm = GroupMailTask::model()->find("client_id = {$clientId} and user_id ={$userId}");
        //下载谷歌浏览器
        $result[UserGuide::DOWNLOAD_CHROME] = empty($browser) ?  UserGuide::USER_GUIDE_TASK_STATUS_UN_FINISH : UserGuide::USER_GUIDE_TASK_STATUS_FINISH;
        //授权店铺
        $result[UserGuide::AUTH_ALI_ICBU_STORE] = empty($clientTask[UserGuide::AUTH_ALI_ICBU_STORE]) ? UserGuide::USER_GUIDE_TASK_STATUS_UN_FINISH : UserGuide::USER_GUIDE_TASK_STATUS_FINISH;
        //设置账号对应关系
        $result[UserGuide::ALI_ICBU_ACCOUNT_MAP_SETTING] = empty($clientTask[UserGuide::ALI_ICBU_ACCOUNT_MAP_SETTING]) ? UserGuide::USER_GUIDE_TASK_STATUS_UN_FINISH : UserGuide::USER_GUIDE_TASK_STATUS_FINISH;
        //同步阿里客户通客户
        $result[UserGuide::SYNCHRONIZE_ALI_CUSTOMERS] = empty($clientTask[UserGuide::SYNCHRONIZE_ALI_CUSTOMERS]) ? UserGuide::USER_GUIDE_TASK_STATUS_UN_FINISH : UserGuide::USER_GUIDE_TASK_STATUS_FINISH;
        //同步阿里订单与商品信息
        $result[UserGuide::SYNC_ALI_ORDER_AND_PRODUCTS] = empty($clientTask[UserGuide::SYNC_ALI_ORDER_AND_PRODUCTS]) ? UserGuide::USER_GUIDE_TASK_STATUS_UN_FINISH : UserGuide::USER_GUIDE_TASK_STATUS_FINISH;
        //绑定邮箱
        $result[UserGuide::BIND_MAIL_MANAGE_GUIDE] = empty($userMail) ? UserGuide::USER_GUIDE_TASK_STATUS_UN_FINISH : UserGuide::USER_GUIDE_TASK_STATUS_FINISH;
        //绑定阿里国际站账号
        $result[UserGuide::BIND_ALI_ICBU_ACCOUNT] = empty($aliAccount) ? UserGuide::USER_GUIDE_TASK_STATUS_UN_FINISH : UserGuide::USER_GUIDE_TASK_STATUS_FINISH;
        //开启whatsApp聊天同步助手
        $result[UserGuide::START_WHATSAPP_CHAT_SYNC_ASSISTANT] = empty($clientTask[UserGuide::START_WHATSAPP_CHAT_SYNC_ASSISTANT]) ? UserGuide::USER_GUIDE_TASK_STATUS_UN_FINISH : UserGuide::USER_GUIDE_TASK_STATUS_FINISH;
        //填写企业和客户信息
        $result[UserGuide::FILL_IN_CORPORATE_AND_CUSTOMER_INFO] = empty($userProperties) ? UserGuide::USER_GUIDE_TASK_STATUS_UN_FINISH : UserGuide::USER_GUIDE_TASK_STATUS_FINISH;
        //小满发现建档为线索
        $result[UserGuide::ARCHIVE_LEAD_FROM_XIAOMAN_DISCOVERY] = empty($leadFromXm) ? UserGuide::USER_GUIDE_TASK_STATUS_UN_FINISH : UserGuide::USER_GUIDE_TASK_STATUS_FINISH;
        //线索转客户
        $result[UserGuide::ARCHIVE_COMPANY_FROM_LEAD] = empty($companyFromLead) ? UserGuide::USER_GUIDE_TASK_STATUS_UN_FINISH : UserGuide::USER_GUIDE_TASK_STATUS_FINISH;
        //发送营销邮件
        $result[UserGuide::SEND_EDM] = empty($edm) ? UserGuide::USER_GUIDE_TASK_STATUS_UN_FINISH : UserGuide::USER_GUIDE_TASK_STATUS_FINISH;
        //app下载提示
        $result[UserGuide::DOWNLOAD_GUIDE] = empty($app) ? UserGuide::USER_GUIDE_TASK_STATUS_UN_FINISH : UserGuide::USER_GUIDE_TASK_STATUS_FINISH;
        print_r($result);

    }

    public function actionSendWorkReportNotify($type = 'daily', $date = '2022-04-03')
    {
        User::setLoginUserById(46900);

        $user = \User::getLoginUser();
        $clientId = $user->getClientId();
        $userId = $user->getUserId();

        if (!in_array($type, ['week', 'daily'])) {
            return $this->fail(-1,'type 参数值不正确，请输入 daily 或者 week');
        }

        if (!empty($date) && date('Y-m-d', strtotime($date)) != $date ) {
            return $this->fail(-1,'date 参数值不正确，请输入某个日期，例如：2021-06-01');
        }

        $userSetting = new UserSetting($clientId, $userId, UserSetting::NOTIFICATION_PIN_SETTING);
        $mailSetting = $userSetting->getValue();
        $notifyType = $type == 'daily' ? \common\library\notification\Constant::NOTIFICATION_TYPE_WORK_REPORT_DAILY : \common\library\notification\Constant::NOTIFICATION_TYPE_WORK_REPORT_WEEK;
        if (!isset($mailSetting[$notifyType]) || empty($mailSetting[$notifyType])) {
            echo '消息通知选项关闭中，无法发送';
            return;
        }

        $key = $type == 'daily' ? 'daily_report' : 'week_report';
        $data = \common\library\work_report\ReportService::builder($clientId, $userId, $type, $date, true);
        $report = \common\library\work_report\ReportService::getWorkReport($clientId, $userId, $key, $data['end']);
        if ($report) {
            $notification = new \common\library\notification\Notification($clientId, $notifyType);
            $notification->user_id = $userId;
            $notification->create_user_id = 0;
            $notification->setSourceData([
                'report_id' => $report['report_id'],
                'title' => $report['title'],
                'name' => $report['name'],
                'desc' => $report['desc']
            ]);


//            \common\library\notification\PushHelper::pushNotification($clientId, $userId, $notification);


            $title = "";
            $desc = "";

            //·取数优先级：新建客户数>新建跟进数>邮件发送数>营销发送数
            //·昨日新建客户数n家/新建跟进数n条/邮件发送数n封/营销发送数n封，环比提升75%/下降65%；
            //若四组数据都为0，则不推送；

            if ($type == 'daily') {

                $reportXs1 = $report['reportObject']['work']['xs1']??[];

                if (!empty($reportXs1)) {

                    $reportKeys = [
                        'company_add_count' => '新建客户数',
                        'customer_remark_count' => '新建跟进数',
                        'mail_send_count' => '邮件发送数',
                        'edm_finish_count' => '营销发送数'
                    ];

                    $unitMap = [
                        'company_add_count' => '家',
                        'customer_remark_count' => '条',
                        'mail_send_count' => '封',
                        'edm_finish_count' => '封'
                    ];

                    foreach ($reportKeys as $reportKey => $titleName) {

                        $trend_ratio = $reportXs1[$reportKey]['trend_ratio'] ??'';
                        if(isset($reportXs1[$reportKey]['value'])
                            && $reportXs1[$reportKey]['value']>0) {

                            $desc = "昨日".$titleName.$reportXs1[$reportKey]['value'].($unitMap[$reportKey] ?? '条');
                            if(isset($reportXs1[$reportKey]['trend_ratio'])
                                && $reportXs1[$reportKey]['trend_ratio']!='-') {
                                $desc .='，环比'.$reportXs1[$reportKey]['trend_ratio']>0?'提升':'下降'.$reportXs1[$reportKey]['value'].'%';
                            }

                            $title = "昨日工作日报已更新";

                            if ($trend_ratio =='-') {
                                $desc .= '，实现了0的突破';
                            }

                            break;
                        }
                    }
                }
            }


            if ($type == 'week') {

                //标题：上周工作周报已更新
                //内容：
                //取数优先级：按顺序取第一个绩效不为0的数据；若绩效为0，则新建客户数>新建跟进数>邮件发送数>营销发送数；
                //其中，绩效优先取金额的字段，再取其他非金额的字段；
                //内容：
                //·绩效金额不为0：上周#绩效名称#为#币种##金额#，环比上升了/下降了7%了；
                //·绩效金额为0，绩效有非零指标：上周#绩效名称#为#数值#，环比上升了/下降了7%了；
                //·绩效都为0，关键指标不为0：上周新建客户数n家/新建跟进数n条/邮件发送数n封/营销发送数n封，环比提升75%/下降65%；
                //都为0：不推送消息
                //
                //若环比因为分母为0时，则环比提升75%/下降65%改为：实现了0的突破
                $reportPerformance = $report['reportObject']['performance']??[];
                $hasPerformance = false;
                foreach ($reportPerformance as $performance) {

                    //本周完成金额
                    $week_finish_amount = $performance['week_finish_amount']['value'] ?? null;
                    //环比趋势
                    $trend_ratio = $performance['trend_ratio'] ?? null;

                    if (!is_null($week_finish_amount)) {

                        //绩效金额不为0
                        if ($week_finish_amount!=0) {

                            $desc = "上周" . str_replace('本周', '', ($performance['name'] ?? '')) . "为" . $week_finish_amount;

                            if (!is_null($trend_ratio) && $trend_ratio !='-') {
                                $desc .='，环比'.($trend_ratio>0?'提升':'下降').$trend_ratio.'%';
                            }


                            if ($trend_ratio =='-') {
                                $desc .= '，实现了0的突破';
                            }

                            $hasPerformance = true;
                            $title = "上周工作周报已更新";
                            break;
                            // 绩效金额为0
                        }
                    }
                }

                //绩效金额都为0的情况
                if (!$hasPerformance) {

                    $reportXs1 = $report['reportObject']['work']['xs1']??[];

                    $reportKeys = [
                        'company_add_count' => '新建客户数',
                        'customer_remark_count' => '新建跟进数',
                        'mail_send_count' => '邮件发送数',
                        'edm_finish_count' => '营销发送数'
                    ];

                    $unitMap = [
                        'company_add_count' => '家',
                        'customer_remark_count' => '条',
                        'mail_send_count' => '封',
                        'edm_finish_count' => '封'
                    ];

                    foreach ($reportKeys as $reportKey => $titleName) {

                        if(isset($reportXs1[$reportKey]['value'])
                            && $reportXs1[$reportKey]['value']>0) {

                            $desc = "昨日".$titleName.$reportXs1[$reportKey]['value'].($unitMap[$reportKey] ?? '条');
                            if(isset($reportXs1[$reportKey]['trend_ratio'])
                                && $reportXs1[$reportKey]['trend_ratio']!='-') {
                                $desc .='，环比'.($reportXs1[$reportKey]['trend_ratio']>0?'提升':'下降').$reportXs1[$reportKey]['trend_ratio'].'%';
                            }

                            $title = "上周工作周报已更新";
                            break;
                        }
                    }
                }
            }


            $reportType = \protobuf\PushData\PBWebReportType::TYPE_DAILY;
            if ($type == 'week') {
                $reportType = \protobuf\PushData\PBWebReportType::TYPE_WEEK;
            }


            if (!empty($title) && !empty($desc)) {
                \common\library\notification\PushHelper::pushDesktopWorkReport($clientId, $userId, $report['report_id'],$title, $desc,$reportType);
            }

            echo '消息通知已发送成功';
        }
    }

    /**
     * @param $clientId
     * @param $value
     * 重置自增值
     */
    public function actionFixInCreatementNum()
    {
        //配置
        $clientId = 1;
//        $clientId = 61179;
        $redis = \RedisService::getInstance('redis');
        $type = Constants::TYPE_COMPANY;

//        $adminUserId = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();
//        User::setLoginUserById($adminUserId);

        // 获取有问题的自增编号配置
        $keyMap = [
            'all' => "serial_number:client:{$clientId}:type:{$type}:increment_always",
            'year' => "serial_number:client:{$clientId}:type:{$type}:increment_by_year",
            'month' => "serial_number:client:{$clientId}:type:{$type}:increment_by_month",
            'day' => "serial_number:client:{$clientId}:type:{$type}:increment_by_day",
        ];

        echo "自定义规则的自增值:".PHP_EOL;
        echo "all:".$redis->get($keyMap['all']).PHP_EOL;
        echo "year:".$redis->get($keyMap['year']).PHP_EOL;
        echo "month:".$redis->get($keyMap['month']).PHP_EOL;
        echo "day:".$redis->get($keyMap['day']).PHP_EOL;
        echo "id_prefix:$clientId:5:".$redis->get(":$clientId:5").PHP_EOL;

//        $generator = new \common\library\serial\generator\IDProducerGenerator($clientId, \IDProducer::TYPE_COMPANY);
//        echo "自增的数字:".$generator->generate().PHP_EOL;

        //todo获取有问题的编号规则
//        $rule = new SerialRule($clientId, \Constants::TYPE_COMPANY);
//        echo "客户编号规则：".json_encode($rule->getSnRule());

        //修复编号
        $ruleAllNum = $redis->get($keyMap['all']);
        $idIncratementNum = $redis->get(":$clientId:5");

        if ($ruleAllNum > $idIncratementNum) {
            $redis->set(":$clientId:5", $ruleAllNum);
            echo "id_prefix:$clientId:5:".$redis->get(":$clientId:5").PHP_EOL;
        } else if($ruleAllNum < $idIncratementNum) {
            $redis->set($keyMap['all'], $ruleAllNum);
            echo "all:".$redis->get($keyMap['all']).PHP_EOL;
        } else {
            echo "不需要更新";
        }

    }


    //修复客户编号自增不同步的bug
    /*
     * case by case 有用户反馈先重置自增id
     **/
    public function actionFixCustomerSerialId($clientIds = 0, $lastNumber = null, $start = 0, $customeValue =null)
    {
        $clientIdList = $this->getClientIdList($clientIds, $lastNumber, $start);

        if (empty($clientIdList)) {
            echo "没有clientId数据";
            return;
        }

        $redis = \RedisService::getInstance('redis');
        $type = Constants::TYPE_COMPANY;

        foreach ($clientIdList as $clientId) {

            //输出用户配置
            $adminUserId = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();
            User::setLoginUserById($adminUserId);
            $rule = new SerialRule($clientId, \Constants::TYPE_COMPANY);
            echo "自定义规则的配置:".PHP_EOL;
            var_dump($rule->getSnRule());

            // 获取有问题的自增编号配置
            $keyMap = [
                'all' => "serial_number:client:{$clientId}:type:{$type}:increment_always",
            ];

            $ruleAllNum = $redis->get($keyMap['all']);
            $idIncratementNum = $redis->get(":$clientId:5");

            echo "自定义规则的自增值:".PHP_EOL;
            echo "all:".$ruleAllNum.PHP_EOL;
            echo "id_prefix:$clientId:5:".$idIncratementNum.PHP_EOL;


            //设置自定义值
            if (!is_null($customeValue)) {
                echo "customeValue为：".$customeValue.PHP_EOL;
                $lastTotal = null;
                //先查询客户最大数量
                $db = \PgActiveRecord::getDbByClientId($clientId);
                $sql = "select count(*)  from tbl_company where client_id = {$clientId} ";
                //查询该client_id。总的客户数
                $total = $db->createCommand($sql)->queryScalar();
                echo "total为：".$total.PHP_EOL;

                //对比customervalue
                //哪个大取值
                if ($customeValue > $total) {
                    echo "customeValue比客户总数大".PHP_EOL;
                    $lastTotal = $customeValue;
                } else if($customeValue < $total) {
                    echo "客户总数比customeValue大".PHP_EOL;
                    $lastTotal = $total;
                }
                echo "lastTotal为：".$lastTotal.PHP_EOL;

                //如果自定义值或者客户总数比目前自增的数要大就更新
                if (!is_null($lastTotal) && ($lastTotal>$ruleAllNum || $lastTotal>$idIncratementNum)) {
                    echo "更新了all和id_perfix值".PHP_EOL;
                    $redis->set($keyMap['all'], $lastTotal);
                    $redis->set(":$clientId:5", $lastTotal);
                }
                echo "更新后的值：".PHP_EOL;
                echo "all:".$redis->get($keyMap['all']).PHP_EOL;
                echo "id_prefix:$clientId:5:".$redis->get(":$clientId:5").PHP_EOL;
            } else {
                if ($ruleAllNum > $idIncratementNum) {
                    $redis->set(":$clientId:5", $ruleAllNum);
                    echo "自定义规则持续递增比较大，将id自增设置为自定义规则持续递增的值".PHP_EOL;
                    echo "id_prefix:$clientId:5:".$redis->get(":$clientId:5").PHP_EOL;
                } else if($ruleAllNum < $idIncratementNum) {
                    echo "id自增比较大，将自定义规则持续递增设置为id自增的值".PHP_EOL;
                    $redis->set($keyMap['all'], $ruleAllNum);
                    echo "all:".$redis->get($keyMap['all']).PHP_EOL;
                } else {
                    echo "两个值一样大，不需要更新值";
                }
            }
        }
    }

    public function getTaskList($clientId,$userId)
    {
        $setting = new \common\library\setting\user\UserSetting($clientId, $userId, \common\library\setting\user\UserSetting::USER_GUIDE_V4);
        $data = $setting->getValue();

        $result = [];
        $user = User::getLoginUser();
        $adminType = $user->getAdminType();

        $taskList = [];


        //根据分型来返回对应的任务列表
        $setting = new \common\library\setting\user\UserSetting($clientId, $userId, \common\library\setting\user\UserSetting::USER_GUIDE_TYPING);
        $typingData = $setting->getValue();

        $typingData = json_decode($typingData,true);

        $totalNum = $typingData['total_number'] ?? 3;
        $needFeature = $typingData['need_feature'] ?? 1;

        $isAdmin = $adminType == \common\library\account\UserInfo::ADMIN_TYPE_SUPER ? true : false;
        $client = new Client($clientId);
        $attr = $client->getExtentAttributes([Client::EXTERNAL_KEY_COMPANY_SWARM]);
        $hasCompanySwarm = $attr[Client::EXTERNAL_KEY_COMPANY_SWARM] ?? 0;

        //主账户需要主账户任务
        if ($isAdmin) {
            $taskList = array_merge(UserGuide::GUIDE_ADMIN_SETTING_TASK_LIST,$taskList);
            //公司只有一个人需要去除这些任务
            if ($totalNum == UserGuide::TYPING_TOTAL_NUM_1) {
                $taskList = array_diff_key($taskList,UserGuide::GUIDE_ADMIN_SETTING_EXCLUDE_TASK_LIST);
            }
            //公司四个人以上
            else if ($totalNum == UserGuide::TYPING_TOTAL_NUM_3) {
                $taskList = array_merge($taskList,[UserGuide::SET_UP_TASK => UserGuide::USER_GUIDE_TASK_STATUS_UN_FINISH]);
            }
            if(!$hasCompanySwarm){
                unset($taskList[UserGuide::CUSTOMER_SEPARATE_SWARM_SETTING]);
            }else{
                //切换新旧版判断
                $setting = new \common\library\setting\user\UserSetting($clientId, $userId, \common\library\setting\user\UserSetting::CUSTOMER_SWARM_SWITCH);
                $customerSwarmSwitch = $setting->getValue() ?? 0;
                if(!$customerSwarmSwitch) {
                    unset($taskList[UserGuide::CUSTOMER_SEPARATE_SWARM_SETTING]);
                }
            }
        }

        switch ($needFeature) {
            //全部
            case UserGuide::TYPING_NEED_FEATURE_ALL:
                $taskList = array_merge($taskList, UserGuide::GUIDE_MANAGE_CUSTOMER_TASK_LIST, UserGuide::GUIDE_DEVELOP_CUSTOMER_TASK_LIST);
                break;
            //管理客户
            case UserGuide::TYPING_NEED_FEATURE_MANAGE_CUSTOMER:
                $taskList = array_merge(UserGuide::GUIDE_MANAGE_CUSTOMER_TASK_LIST,$taskList);
                break;
            //开发客户
            case UserGuide::TYPING_NEED_FEATURE_DEVELOP_CUSTOMER:
                $taskList = array_merge(UserGuide::GUIDE_DEVELOP_CUSTOMER_TASK_LIST,$taskList);
                break;
            default:
                break;
        }


        foreach ($taskList as $key => $status) {
            if ($key === UserGuide::SET_UP_TASK) {
                if (!\common\library\task\Helper::hasWorkBenchV2Functional($clientId)) {
                    unset($taskList[UserGuide::SET_UP_TASK]);
                }
                continue;
            }

            //校验权限
            if (array_key_exists($key,UserGuide::USER_GUIDE_TASK_PRIVILEGES)) {
                if(!\common\library\privilege_v3\Helper::hasFunctional($clientId,UserGuide::USER_GUIDE_TASK_PRIVILEGES[$key]['functional'])) {
                    unset($taskList[$key]);
                }
                continue;
            }


        }

        //新手任务 ，若是新用户，则默认初始化值
        foreach ($taskList as $key => $item) {
            $result['task_list'][$key] = $data[$key] ?? $item;
        }

        return $result['task_list'];
    }


    /**
     * @param int $clientIds
     * @param null $lastNumber
     * @param int $start
     * @param int $resetFlag
     * 处理因为邮件回调延时没有完成任务的数据
     */
    public function actionFixMailTask($clientIds = 0, $lastNumber = null, $start = 0)
    {
        $clientIdList = $this->getClientIdList($clientIds, $lastNumber, $start);

        if (empty($clientIdList)) {
            echo "没有clientId数据";
            return;
        }

        $startCreateTime = date('Y-m-01 00:00:00');
        $endCreateTime = date('Y-m-t 23:59:59');

        foreach ($clientIdList as $clientId) {

            $pgDb = PgActiveRecord::getDbByClientId($clientId);
            //查询未完成的邮件任务 type=2为回复邮件任务 status=1为未完成任务 当月月数据
            $taskSql = "select task_id,refer_id,rule_id from tbl_task where client_id=$clientId and type=2 and status=1  and create_time between '$startCreateTime' and '$endCreateTime' ";
//            $taskSql = "select task_id, refer_id,rule_id from tbl_task where client_id=$clientId and type=2 and status=2   and create_time between '$startCreateTime' and '$endCreateTime' ";

            $tasks = $pgDb->createCommand($taskSql)->queryAll();
            $referIds = implode(',', array_column($tasks, 'refer_id'));
            if (empty($referIds)) {
                self::info("clientId:".$clientId." 没有未完成的数据");
                continue;
            }

            $mySqlDb = ProjectActiveRecord::getDbByClientId($clientId);
            //找出该更封邮件的收到时间跟回复回复时间
            $mailSql = "select mail_id,reply_to_mail_id,receive_time as create_time from tbl_mail where mail_id in ($referIds) and reply_flag=1";
            $mailData = $mySqlDb->createCommand($mailSql)->queryAll();

            $mailIds = array_column($mailData, 'mail_id');
            $mailReplyMailMap = array_column($mailData, null, 'mail_id');

            $replyMailIdStr= implode(',',array_column($mailData, 'reply_to_mail_id'));

            if (empty($replyMailIdStr)) {
                self::info("clientId:".$clientId." 没有回复邮件id的数据");
                continue;
            }

            //回复邮件的时间
            $replyMailSql = "select mail_id, receive_time as create_time from tbl_mail where mail_id in ($replyMailIdStr)";
            $replyMailData = $mySqlDb->createCommand($replyMailSql)->queryAll();
            $replyMailMap = array_column($replyMailData, null, 'mail_id');

            if (empty($replyMailMap)) {
                self::info("clientId:".$clientId." replyMailMap为空");
                continue;
            }

            $ruleIdStr = implode(',', array_column($tasks, 'rule_id'));
            if (empty($ruleIdStr)) {
                self::info("clientId:".$clientId." ruleIdStr为空");
                continue;
            }
            $ruleSql = "select handlers,rule_id from tbl_workflow_rule where rule_id in ($ruleIdStr) ";
            $rules = $pgDb->createCommand($ruleSql)->queryAll();
            $ruleMap = [];

            if (empty($rules)) {
                self::info("clientId:".$clientId." rules为空");
                continue;
            }

            foreach($rules as $rule) {
                $handler = json_decode($rule['handlers'],true);
                $handler = current($handler);
                if (empty($handler)) {
                    self::info("clientId:".$clientId." handler为空");
                    continue;
                }

                $ruleMap[$rule['rule_id']] = [
                    'timeLimit' => $handler['config']['time_limit']??0
                ];
            }

            $updateTaskSqlArr = [];
            foreach ($tasks as $task) {

                $mailId = $task['refer_id'] ?? '';
                $taskId = $task['task_id'] ?? '';
                $ruleId = $task['rule_id'] ?? '';

                if (empty($mailId) && empty($taskId) && empty($ruleId)) {
                    self::info("clientId:".$clientId." mailId taskId ruleId为空");
                    continue;
                }

                //是否已经被回复
                if (!in_array($mailId, $mailIds)) {
                    continue;
                }

                //邮件的创建时间
                $createTime = $mailReplyMailMap[$mailId]['create_time'] ?? '';
                //邮件的被回复时间
                $finishTime = $replyMailMap[$mailReplyMailMap[$mailId]['reply_to_mail_id'] ?? '']['create_time'] ?? '';

                //截止时间
                $endTime = date('Y-m-d H:i:s', (strtotime($createTime) + $ruleMap[$ruleId]['timeLimit']??0));

                if (!empty($createTime) && !empty($finishTime) && !empty($endTime)) {
                    //更新该task状态以及处理时间
                    $updateTaskSqlArr[] = "update tbl_task set status =2,allow_duplicate=1,create_time='$createTime',finish_time = '$finishTime',update_time = '$finishTime',end_time='$endTime'  where client_id = {$clientId}  and task_id =$taskId";
                }

            }

            if (empty($updateTaskSqlArr)) {
                continue;
            }

            $updateSqlArrayChunk = array_chunk($updateTaskSqlArr, 100);
            foreach ($updateSqlArrayChunk as $item) {
                $updateSql = implode('; ', $item);
                $res = $pgDb->createCommand($updateSql)->execute();
            }
        }
    }

    public function actionFixCutomerGroup($clientId, $companyId, $field, $time)
    {
        if (empty($field) || empty($time) || empty($clientId) || empty($companyId) ) {
            self::info("field或者time不能为空");
            return;
        }

        $pgDb = PgActiveRecord::getDbByClientId($clientId);
        $updateSql = "update tbl_company set {$field} = '{$time}' where client_id={$clientId} and company_id={$companyId} ";
        $res = $pgDb->createCommand($updateSql)->execute();
        (new  \common\library\swarm\SwarmService($clientId))->refreshByRefer([$companyId], [
            $field
        ]);

        self::info("client_id:".$clientId."更新完毕"." res:".$res." sql:为".$updateSql);
    }

    /**
     * @param $clientId
     * @throws CDbException
     * @throws ProcessException
     * 修复收发件时间一样的数据
     */
    public function actionFixCutomerGroupByTrail($clientId)
    {

        $pgDb = PgActiveRecord::getDbByClientId($clientId);

        //找出收发件相同的邮件
        $sql = "select company_id from tbl_company where client_id = $clientId and is_archive = 1 and send_mail_time > '1970-01-01 00:00:00' and receive_mail_time > '1970-01-01 00:00:00';";

        $companyIds = $pgDb->createCommand($sql)->queryColumn();
        if (empty($companyIds)) {
            self::info("不存在收发件时间一样的数据");
            return;
        }

        $companyIdStr = implode(',',$companyIds);
        //找出这些company_id的最新的收件时间
        $trailSql = "SELECT max(A.create_time) as receive_mail_time,A.company_id FROM tbl_dynamic_trail as A WHERE A.enable_flag=1 AND A.client_id=$clientId AND A.company_id in ($companyIdStr) AND A.type=202  group by company_id";
        $data = $pgDb->createCommand($trailSql)->queryAll();

        $updateArr = [];
        $hasReceiveCompanyIds = [];
        if(!empty($data)) {
            foreach ($data as $item) {
                $companyId = $item['company_id']?? '';
                $time = $item['receive_mail_time'] ?? '';

                if (empty($companyId) || empty($time)) {
                    self::info("company_id或者time没有时间");
                    continue;
                }
                $hasReceiveCompanyIds[] = $companyId;
                $updateSqlArray[] = "update tbl_company set receive_mail_time = '{$time}' where client_id={$clientId} and company_id={$companyId} ";
            }
        }


        //剩下没有收件时间的
        foreach ($companyIds as $resItem) {

            if(in_array($resItem,$hasReceiveCompanyIds)) {
                continue;
            }

            $updateSqlArray[] = "update tbl_company set  receive_mail_time = '1970-01-01 00:00:00' where client_id={$clientId} and company_id={$resItem} ";
        }

        $updateSqlArrayChunk = array_chunk($updateSqlArray, 100);
        foreach ($updateSqlArrayChunk as $item) {
            $updateSql = implode('; ', $item);
            self::info($updateSql);
            $res = $pgDb->createCommand($updateSql)->execute();
        }

        (new  \common\library\swarm\SwarmService($clientId))->refreshByRefer($companyIds, [
            'receive_mail_time',
        ]);

        self::info("client_id:".$clientId."更新完毕"." sql:为".implode('#',$updateSqlArray));
    }

    /**
     *
     * 给授权client增加管理视角功能
     */
    public function actionbakArangeMailListClietnSettingSwitch($clientIds = 0, $lastNumber = null, $start = 0, $flag = 1)
    {

        $ignoreClientId = "74602,74603,74604,74613,74614,74615,74616,74617,74618,74619,74620,74621,74622,74626,74627,74628,74629,74630,74632,74633,74634,74635,74636,74637,74638,74639,74640,74641,74643,74644,74645,74646,74647,74648,74649,74650,74651,74652,74657,74658,74659,74660,74661,74662,74663,74664,74665,74666,74667,74668,74669,74670,74671,74672,74673,74674,74675,74676,74677,74678,74679,74680,74681,74682,74683,74684,74685,74686,74687,74688,74689,74690,74691,74692,74693,74694,74695,74696,74697,74698,74699,74700,74701,74702,74703,74704,74705,74706,74707,74708,74709,74710,74711,74712,74713,74714,74715,74716,74718,74719,74720,74721,74722,74723,74724,74725,74726,74727,74728,74729,74730,74731,74732,74733,74734,74735,74736,75048,75049,75050,75309,77738,77739,77740,77741,77742,77743,77744,77745,77746,77747,77869,77870,77871,77872,77873,77875,77876,77877,77878,77879,77880,77881,77882,77883,77884,80528,80529,80530,80531,80532,80533,80534,80535,80536,80537,80546,80547,80548,80549,80550,80551,80552,80553,80554,80555,80556,80557,80558,80559,80560,80568,80569,80570,80571,80572,80573,80574,80575,80576,80577,80578,80579,80580,80581,80582,80584,80585,80586,80587,80588,80589,80590,80591,80592,80593,80594,80596,80597,80598,80599,80606,80607,80608,80609,80610,80611,80612,80613,80614,80615,80616,80617,80618,80619,80620,80634,80635,80636,80637,80638,80639,80640,80641,80642,80643,80644,80645,80646,80647,80648,80658,80659,80660,80661,80662,80663,80664,80665,80666,80667,80668,80669,80670,80671,80672,81283,81284,81285,81286,81287,81288,81289,81290,81291,81292,81313,81314,81315,81558,81559,81560,81561,81562,81563,81564,81565,81566,81567,81572,81573,81574,81575,81576,81577,81578,81579,81580,81581,81584,81585,81586,81587,81588,81589,81590,81591,81592,81593";

        $ignoreClientIdArr = explode(',',$ignoreClientId);

        $clientIdList = $this->getClientIdList($clientIds, $lastNumber, $start);

        if (empty($clientIdList)) {
            echo "没有clientId数据";
            return;
        }

        foreach ($clientIdList as $clientId) {

            if(in_array($clientId,$ignoreClientIdArr)) {
                continue;
            }

            // 配置系统配置
            $client = \common\library\account\Client::getClient($clientId);
            $client->setExtentAttributes([
                \common\library\account\Client::EXTERNAL_KEY_MANAGE_VIEW_SWITCH => $flag,
            ]);
            $client->saveExtentAttributes();

            if($flag) {
                self::info("client_id:".$clientId."管理视角开关已开启");
            } else {
                self::info("client_id:".$clientId."管理视角开关已关闭");
            }
        }

    }
    public function actionGetDbInfoByClientId($clientId)
    {
        if (empty($clientId)) {
            return "client id不能为空";
        }

        $client = \common\library\account\Client::getClient($clientId);
        if ($client->isNew()) {
            // eid 查询
            if (is_numeric($clientId)){
                $clientObj = \Client::findByEid($clientId);
                if (empty($clientObj)) {
                    throw new RuntimeException('client not found');
                }
                $client = \common\library\account\Client::getClient($clientObj->client_id);
            }else{
                return "client not found";
            }
        }

        $mysql_set_id = $client->mysql_set_id;
        $pgsql_set_id = $client->pgsql_set_id;

        if( !$mysql_set_id )
        {
            return  [];
        }

        $mysql_info = Yii::app()->account_base_db->createCommand("SELECT * FROM tbl_db_set WHERE set_id=$mysql_set_id")->queryRow();

        $mysql_cmd = sprintf("mysql -h%s  -P%s -u%s -p%s -D%s",
            $mysql_info['host'],
            $mysql_info['port'],
            $mysql_info['user'],
            $mysql_info['password'],
            $mysql_info['name']
        );
        $db = [
            'mysql' => [
                'type' => 'mysql',
                'host' => $mysql_info['host'],
                'port' => $mysql_info['port'],
                'name' => $mysql_info['name'],
                'schema_name' => $mysql_info['schema_name']??$mysql_info['name'],
                'cmd' => $mysql_cmd
            ],
        ];
        if ($pgsql_set_id) {
            $pgsql_info = Yii::app()->account_base_db->createCommand("SELECT * FROM tbl_db_set WHERE set_id=$pgsql_set_id")->queryRow();
            $pgsql_cmd = sprintf("psql -h %s  -p %s  \"dbname=%s user=%s password=%s\"",
                $pgsql_info['host'],
                $pgsql_info['port'],
                $pgsql_info['name'],
                $pgsql_info['user'],
                $pgsql_info['password']
            );
            $db['pgsql'] = [
                'type' => 'pgsql',
                'host' => $pgsql_info['host'],
                'port' => $pgsql_info['port'],
                'name' => $pgsql_info['name'],
                'schema_name' => $pgsql_info['schema_name']??'public',
                'cmd' => $pgsql_cmd
            ];

        }
        print_r($db);
    }

    public function actionUpdateOutLookMailDialogueStatus()
    {
        //查询v4_admin有outlook mail的邮箱 //todo 有性能问题
        $sql = "select user_id,user_mail_id,client_id,email_address
from tbl_user_mail
where   send_server in ('smtp.outlook.com','smtp.office365.com','snt-m.hotmail.com','s.outlook.com','outlook.office365.com','smtp.live.com','smtp-mail.outlook.com','eas.outlook.com','smtp.outlook.office365.com')
and  ( forbidden_flag = 0  and user_id != 0 and oauth_flag=0)";

        $list = Yii::app()->db->createCommand($sql)->queryAll();

        $res = [];
        foreach ($list as $item) {
            $res[$item['client_id']][$item['user_id']][] = $item['email_address'];
        }

        $insertField = [
            'user_id',
            'client_id',
            '`key`',
            'value',
            'create_time'
        ];
        $time = date("Y-m-d H:i:s");


        foreach($res as $client => $userItme) {
            $db = \ProjectActiveRecord::getDbByClientId($client);
            $insertSql = "replace into tbl_user_setting (".implode(',',$insertField).") values ";
            $tempValue = [];

            foreach ($userItme as $userId => $value) {
                $insertValues = [
                    $userId,
                    $client,
                    "'out.look.mail.rebind.tip'",
                    "'".json_encode([
                        're_bind_flag' => 1,//默认为0 不需要弹窗 1为需要弹窗
                        'bind_list' => $value
                    ])."'",
                    "'". $time."'"
                ];

                $tempValue[] = '('. implode(',',$insertValues).')';
            }

            $insertSql .= implode(',',$tempValue);
            $db->createCommand($insertSql)->execute();
            self::info("client_id:".$client."执行完成，sql:".$insertSql);
        }

    }

    /**
     *
     * 给授权client增加邮件同步改造功能
     */
    public function actionArangeMailListClietnSettingSwitch($clientIds = 0, $lastNumber = null, $start = 0)
    {

        $clientIdList = $this->getClientIdList($clientIds, $lastNumber, $start);

        if (empty($clientIdList)) {
            echo "没有clientId数据";
            return;
        }

        foreach ($clientIdList as $clientId) {
            // 配置系统配置
            $client = \common\library\account\Client::getClient($clientId);
            $client->setExtentAttributes([
                \common\library\account\Client::EXTERNAL_KEY_MOBILE_MAIL_LIST_SWITCH => '1',
            ]);
            $client->saveExtentAttributes();
            self::info("client_id:".$clientId."邮件同步开关已开启");

        }

    }

    public function actionAiClietnSettingSwitch($clientIds = 0, $lastNumber = null, $start = 0, $openFlag = 1, $model = 'azure-openai-gpt-3')
    {

        $clientIdList = $this->getClientIdList($clientIds, $lastNumber, $start);

        if (empty($clientIdList)) {
            echo "没有clientId数据";
            return;
        }

        foreach ($clientIdList as $clientId) {
            // 配置系统配置
            $client = \common\library\account\Client::getClient($clientId);
            $client->setExtentAttributes([
                'ai_write_switch' => $openFlag,
                'ai_write_model' => $model,
            ]);
            $client->saveExtentAttributes();
            if ($openFlag) {
                self::info("client_id:" . $clientId . " AI开关已开启");
                self::info("client_id:" . $clientId . " AI服务为$model");
            } else {
                self::info("client_id:" . $clientId . "AI开关已关闭");
                self::info("client_id:" . $clientId . " AI服务为$model");
            }

        }

    }

    public function actionAiSubjectClietnSettingSwitch($clientIds = 0, $lastNumber = null, $start = 0, $openFlag = 1)
    {

        $clientIdList = $this->getClientIdList($clientIds, $lastNumber, $start);

        if (empty($clientIdList)) {
            echo "没有clientId数据";
            return;
        }

        foreach ($clientIdList as $clientId) {
            // 配置系统配置
            $client = \common\library\account\Client::getClient($clientId);
            $client->setExtentAttributes([
                'ai_subject_switch' => $openFlag,
            ]);
            $client->saveExtentAttributes();
            if ($openFlag) {
                self::info("client_id:" . $clientId . " AI-ai_subject_switch开关已开启");
            } else {
                self::info("client_id:" . $clientId . "AI-ai_subject_switch 开关已关闭");
            }

        }

    }

    public function actionAiReplyClietnSettingSwitch($clientIds = 0, $lastNumber = null, $start = 0, $openFlag = 1)
    {

        $clientIdList = $this->getClientIdList($clientIds, $lastNumber, $start);

        if (empty($clientIdList)) {
            echo "没有clientId数据";
            return;
        }

        foreach ($clientIdList as $clientId) {
            // 配置系统配置
            $client = \common\library\account\Client::getClient($clientId);
            $client->setExtentAttributes([
                'ai_reply_switch' => $openFlag,
            ]);
            $client->saveExtentAttributes();
            if ($openFlag) {
                self::info("client_id:" . $clientId . " AI-ai_reply_switch开关已开启");
            } else {
                self::info("client_id:" . $clientId . "AI-ai_reply_switch开关已关闭");
            }

        }

    }

    public function actionMailSearchV2ClietnSettingSwitch($clientIds = 0, $lastNumber = null, $start = 0, $openFlag = 1)
    {

        $clientIdList = $this->getClientIdList($clientIds, $lastNumber, $start);

        if (empty($clientIdList)) {
            echo "没有clientId数据";
            return;
        }

        foreach ($clientIdList as $clientId) {
            // 配置系统配置
            $client = \common\library\account\Client::getClient($clientId);
            $client->setExtentAttributes([
                'mail_search_v2_switch' => $openFlag,
            ]);
            $client->saveExtentAttributes();
            if ($openFlag) {
                self::info("client_id:" . $clientId . " 邮件搜索V2开关已开启");
            } else {
                self::info("client_id:" . $clientId . "邮件搜索V2开关已关闭");
            }

        }

    }

    //clientIds从数仓取出，有利于更快的跑历史数据
    public function actionAddConferenceMailVersion($clientIds = 0, $lastNumber = null, $start = 0, $userMailId = 0, $dryRun = 1)
    {
        self::info("当前为:".($dryRun ? "测试模式" : "正式模式"));
        $clientIdList = $this->getClientIdList($clientIds, $lastNumber, $start);

        if (empty($clientIdList)) {
            echo "没有clientId数据";
            return;
        }

        foreach ($clientIdList as $clientId) {

            $db = ProjectActiveRecord::getDbByClientId($clientId);

            if (empty($db)) {
                self::info("client_id[$clientId] -- 无数据库");
                continue;
            }

            $userMailIds = [];
            if ($userMailId==0) {
                $userMailsql = "SELECT user_mail_id FROM tbl_user_mail WHERE client_id=:client_id AND user_id!=0 AND enable_flag=1";
                $userMailIds = \Yii::app()->db->createCommand($userMailsql)->queryColumn([':client_id' => $clientId]);
            } else {
                $userMailIds[] = $userMailId;
            }

            foreach ($userMailIds as $userMailId) {

                $sql = "SELECT mail_id FROM tbl_mail_conference WHERE client_id={$clientId} AND user_mail_id={$userMailId}" ;
                $conferenceMailIds = $db->createCommand($sql)->queryColumn();

                if (empty($conferenceMailIds)) continue;
                if (!$dryRun) {
                    $mailVersion = new \common\library\version\MailVersion($clientId, $userMailId);
                    $mailVersion->setMailId($conferenceMailIds);
                    $mailVersion->setType(\common\library\version\Constant::MAIL_MODULE_ADD);
                    $mailVersion->add();
                    self::info("Run：userMailId[$userMailId] --count[".count($conferenceMailIds)."] -- conferenceMailIds [" . implode(',', $conferenceMailIds) . "]");
                } else {
                    self::info("dryRun：userMailId[$userMailId] --count[".count($conferenceMailIds)."] -- conferenceMailIds [" . implode(',', $conferenceMailIds) . "]");
                }
            }
        }
    }


    //更新tbl_mail_conversation_folder 的user_mail_id
    public function actionMigateClientConversationFolder()
    {
        //查询tbl_mail_conversation_folder，遍历每一行数据
        $db = ProjectActiveRecord::getDbByClientId(1);
        $clientids=  "9604,9645,9646,9648,9650,9652,14057,14058,14059,14064,14082";
        //去tbl_mail_conversation找到对应的conversation_id，更新tbl_mail_conversation_folder.user_mail_id
        $maxConverSationId = $db->createCommand("select mail_conversation_id from tbl_mail_conversation where client_id in($clientids)"."order by mail_conversation_id desc limit 1")->queryScalar();
        $currConverSationId = 1146978;
        $filed = 'client_id,mail_conversation_id,user_mail_id';
        $sql = 'select ' . $filed . " from tbl_mail_conversation where  client_id in($clientids) ";

        while ($currConverSationId < $maxConverSationId) {
            self::info("当前会话Id：{$currConverSationId}");
            $conversationList = $db->createCommand($sql . ' and mail_conversation_id >' . $currConverSationId . ' order by mail_conversation_id asc limit 10000')->queryAll();

            if (empty($conversationList)) {
                break;
            }

            $currConverSationId = end($conversationList)['mail_conversation_id']??0;
            if ($currConverSationId != 0) {

                $resetOrderSql = "update tbl_mail_conversation_folder set user_mail_id= case";

                foreach ($conversationList as $item) {
                    $resetOrderSql .= " when client_id= {$item['client_id']}  and conversation_id = {$item['mail_conversation_id']} then {$item['user_mail_id']} ";
                }

                $realClientIds = implode(',',array_unique(array_column($conversationList,'client_id')));
                $resetOrderSql .= " end where client_id in($realClientIds) and conversation_id in (".implode(',',array_column($conversationList,'mail_conversation_id')).")";

                $res = $db->createCommand($resetOrderSql)->execute();
               self::info("更新了{$res}条数据");
            } else {
                break;
            }
        }
    }

    //更新tbl_mail_conversation_email 的user_mail_id
    public function actionMigateClientConversationEmail()
    {
        //查询tbl_mail_conversation_folder，遍历每一行数据
        $db = ProjectActiveRecord::getDbByClientId(1);
        $clientids=  "1,9604,9645,9646,9648,9650,9652,14057,14058,14059,14064,14082";
        //去tbl_mail_conversation找到对应的conversation_id，更新tbl_mail_conversation_folder.user_mail_id
        $maxConverSationId = $db->createCommand("select mail_conversation_id from tbl_mail_conversation where client_id in($clientids)"."order by mail_conversation_id desc limit 1")->queryScalar();
        $currConverSationId = 996545;
        $filed = 'client_id,mail_conversation_id,user_mail_id';
        $sql = 'select ' . $filed . " from tbl_mail_conversation where  client_id in($clientids) ";

        while ($currConverSationId < $maxConverSationId) {
            self::info("当前会话Id：{$currConverSationId}");
            $conversationList = $db->createCommand($sql . ' and mail_conversation_id >' . $currConverSationId . ' order by mail_conversation_id asc limit 10000')->queryAll();

            if (empty($conversationList)) {
                break;
            }

            $currConverSationId = end($conversationList)['mail_conversation_id']??0;
            if ($currConverSationId != 0) {

                $resetOrderSql = "update tbl_mail_conversation_email set user_mail_id= case";

                foreach ($conversationList as $item) {
                    $resetOrderSql .= " when client_id= {$item['client_id']}  and conversation_id = {$item['mail_conversation_id']} then {$item['user_mail_id']} ";
                }

                $realClientIds = implode(',',array_unique(array_column($conversationList,'client_id')));
                $resetOrderSql .= " end where client_id in($realClientIds) and conversation_id in (".implode(',',array_column($conversationList,'mail_conversation_id')).")";

                $res = $db->createCommand($resetOrderSql)->execute();
                self::info("更新了{$res}条数据");
            } else {
                break;
            }
        }
    }

    //更新tbl_mail_conversation_tag 的user_mail_id
    public function actionMigateClientConversationTag()
    {
        //查询tbl_mail_conversation_folder，遍历每一行数据
        $db = ProjectActiveRecord::getDbByClientId(1);
        $clientids=  "1,9604,9645,9646,9648,9650,9652,14057,14058,14059,14064,14082";
        //去tbl_mail_conversation找到对应的conversation_id，更新tbl_mail_conversation_folder.user_mail_id
        $maxConverSationId = $db->createCommand("select mail_conversation_id from tbl_mail_conversation where client_id in($clientids)"."order by mail_conversation_id desc limit 1")->queryScalar();
        $currConverSationId = 0;
        $filed = 'client_id,mail_conversation_id,user_mail_id';
        $sql = 'select ' . $filed . " from tbl_mail_conversation where  client_id in($clientids) ";

        while ($currConverSationId < $maxConverSationId) {
            self::info("当前会话Id：{$currConverSationId}");
            $conversationList = $db->createCommand($sql . ' and mail_conversation_id >' . $currConverSationId . ' order by mail_conversation_id asc limit 10000')->queryAll();

            if (empty($conversationList)) {
                break;
            }

            $currConverSationId = end($conversationList)['mail_conversation_id']??0;
            if ($currConverSationId != 0) {

                $resetOrderSql = "update tbl_mail_conversation_tag set user_mail_id= case";

                foreach ($conversationList as $item) {
                    $resetOrderSql .= " when client_id= {$item['client_id']}  and conversation_id = {$item['mail_conversation_id']} then {$item['user_mail_id']} ";
                }

                $realClientIds = implode(',',array_unique(array_column($conversationList,'client_id')));
                $resetOrderSql .= " end where client_id in($realClientIds) and conversation_id in (".implode(',',array_column($conversationList,'mail_conversation_id')).")";

                $res = $db->createCommand($resetOrderSql)->execute();
                self::info("更新了{$res}条数据");
            } else {
                break;
            }
        }
    }





    //迁移数据 tbl_mail_conversation folder_ids
    public function actionFormatConverstionFolderIds()
    {
        //查询这三张表的数据聚合到tbl_conversation表
    //查询tbl_mail_conversation_folder，遍历每一行数据
    $db = ProjectActiveRecord::getDbByClientId(1);
//    $clientids=  "9604,9645,9646,9648,9650,9652,14057,14058,14059,14064,14082";
    $clientids=  "1";
    //去tbl_mail_conversation找到对应的conversation_id，更新tbl_mail_conversation_folder.user_mail_id
    $maxConverSationId = $db->createCommand("select mail_conversation_id from tbl_mail_conversation where client_id in($clientids)"."order by mail_conversation_id desc limit 1")->queryScalar();
    $currConverSationId = 0;
    $filed = 'client_id,mail_conversation_id,user_mail_id';
    $sql = 'select ' . $filed . " from tbl_mail_conversation where  client_id in($clientids) ";

    while ($currConverSationId < $maxConverSationId) {
        self::info("当前会话Id：{$currConverSationId}");
        $conversationList = $db->createCommand($sql . ' and mail_conversation_id >' . $currConverSationId . ' order by mail_conversation_id asc limit 10000')->queryAll();

        if (empty($conversationList)) {
            break;
        }

        $currConverSationId = end($conversationList)['mail_conversation_id']??0;
        if ($currConverSationId != 0) {

            $clientids = implode(',',array_unique(array_column($conversationList,'client_id')));
            $conversationIds = implode(',',array_unique(array_column($conversationList,'mail_conversation_id')));
            $userMailIds = implode(',',array_unique(array_column($conversationList,'user_mail_id')));


            //查询当前会话的folder_id
            //找出当前会话id的所在文件夹
            $folderList = $db->createCommand("select conversation_id,GROUP_CONCAT(folder_id) as folder_ids from tbl_mail_conversation_folder where 
                client_id in($clientids) and user_mail_id in ($userMailIds) and conversation_id in ($conversationIds) group by conversation_id")->queryAll();


            if (empty($folderList)) {
                continue;
            }


            $setFolderSql = '';
            foreach ($folderList as $item) {
                $folders = "'".$item['folder_ids']."'";
                $setFolderSql .= " when mail_conversation_id = {$item['conversation_id']} then {$folders} ";
            }

            //查询当前会话的email_id

            //查询当前回话的标签ID

            //更新数据回会话表数据
            $updateMailConversationSql = "update tbl_mail_conversation set folder_ids = case {$setFolderSql} ";
            $updateMailConversationSql .= " end where  mail_conversation_id in (".implode(',',array_column($folderList,'conversation_id')).")";
            $res = $db->createCommand($updateMailConversationSql)->execute();
            self::info("更新了{$res}条数据");
        } else {
            break;
        }
    }
    }



    //迁移数据 tbl_mail_conversation email_ids
    public function actionFormatConverstionEmailIds()
    {
        //查询这三张表的数据聚合到tbl_conversation表
        //查询tbl_mail_conversation_folder，遍历每一行数据
        $db = ProjectActiveRecord::getDbByClientId(1);
//    $clientids=  "9604,9645,9646,9648,9650,9652,14057,14058,14059,14064,14082";
        $clientids=  "1";
        //去tbl_mail_conversation找到对应的conversation_id，更新tbl_mail_conversation_folder.user_mail_id
        $maxConverSationId = $db->createCommand("select mail_conversation_id from tbl_mail_conversation where client_id in($clientids)"."order by mail_conversation_id desc limit 1")->queryScalar();
        $currConverSationId = 0;
        $filed = 'client_id,mail_conversation_id,user_mail_id';
        $sql = 'select ' . $filed . " from tbl_mail_conversation where  client_id in($clientids) ";

        while ($currConverSationId < $maxConverSationId) {
            self::info("当前会话Id：{$currConverSationId}");
            $conversationList = $db->createCommand($sql . ' and mail_conversation_id >' . $currConverSationId . ' order by mail_conversation_id asc limit 10000')->queryAll();

            if (empty($conversationList)) {
                break;
            }

            $currConverSationId = end($conversationList)['mail_conversation_id']??0;
            if ($currConverSationId != 0) {

                $clientids = implode(',',array_unique(array_column($conversationList,'client_id')));
                $conversationIds = implode(',',array_unique(array_column($conversationList,'mail_conversation_id')));
                $userMailIds = implode(',',array_unique(array_column($conversationList,'user_mail_id')));


                //查询当前会话的email_id

                $folderList = $db->createCommand("select conversation_id,GROUP_CONCAT(email_id) as email_ids from tbl_mail_conversation_email where 
                client_id in($clientids) and user_mail_id in ($userMailIds) and conversation_id in ($conversationIds) group by conversation_id")->queryAll();


                if (empty($folderList)) {
                    continue;
                }


                $setFolderSql = '';
                foreach ($folderList as $item) {
                    $folders = "'".$item['email_ids']."'";
                    $setFolderSql .= " when mail_conversation_id = {$item['conversation_id']} then {$folders} ";
                }


                //更新数据回会话表数据
                $updateMailConversationSql = "update tbl_mail_conversation set email_ids = case {$setFolderSql} ";
                $updateMailConversationSql .= " end where  mail_conversation_id in (".implode(',',array_column($folderList,'conversation_id')).")";
                $res = $db->createCommand($updateMailConversationSql)->execute();
                self::info("更新了{$res}条数据");
            } else {
                break;
            }
        }
    }



    //迁移数据 tbl_mail_conversation tag_ids
    public function actionFormatConverstionTagIds()
    {
        //查询这三张表的数据聚合到tbl_conversation表
        //查询tbl_mail_conversation_folder，遍历每一行数据
        $db = ProjectActiveRecord::getDbByClientId(1);
//    $clientids=  "9604,9645,9646,9648,9650,9652,14057,14058,14059,14064,14082";
        $clientids=  "1";
        //去tbl_mail_conversation找到对应的conversation_id，更新tbl_mail_conversation_folder.user_mail_id
        $maxConverSationId = $db->createCommand("select mail_conversation_id from tbl_mail_conversation where client_id in($clientids)"."order by mail_conversation_id desc limit 1")->queryScalar();
        $currConverSationId = 0;
        $filed = 'client_id,mail_conversation_id,user_mail_id';
        $sql = 'select ' . $filed . " from tbl_mail_conversation where  client_id in($clientids) ";

        while ($currConverSationId < $maxConverSationId) {
            self::info("当前会话Id：{$currConverSationId}");
            $conversationList = $db->createCommand($sql . ' and mail_conversation_id >' . $currConverSationId . ' order by mail_conversation_id asc limit 10000')->queryAll();

            if (empty($conversationList)) {
                break;
            }

            $currConverSationId = end($conversationList)['mail_conversation_id']??0;
            if ($currConverSationId != 0) {

                $clientids = implode(',',array_unique(array_column($conversationList,'client_id')));
                $conversationIds = implode(',',array_unique(array_column($conversationList,'mail_conversation_id')));
                $userMailIds = implode(',',array_unique(array_column($conversationList,'user_mail_id')));


                //查询当前会话的tag

                $folderList = $db->createCommand("select conversation_id,GROUP_CONCAT(tag_id) as tag_ids from tbl_mail_conversation_tag where 
                client_id in($clientids) and user_mail_id in ($userMailIds) and conversation_id in ($conversationIds) group by conversation_id")->queryAll();


                if (empty($folderList)) {
                    continue;
                }


                $setFolderSql = '';
                foreach ($folderList as $item) {
                    $folders = "'".$item['tag_ids']."'";
                    $setFolderSql .= " when mail_conversation_id = {$item['conversation_id']} then {$folders} ";
                }


                //更新数据回会话表数据
                $updateMailConversationSql = "update tbl_mail_conversation set tag_ids = case {$setFolderSql} ";
                $updateMailConversationSql .= " end where  mail_conversation_id in (".implode(',',array_column($folderList,'conversation_id')).")";
                $res = $db->createCommand($updateMailConversationSql)->execute();
                self::info("更新了{$res}条数据");
            } else {
                break;
            }
        }
    }

    //迁移数据 tbl_mail_conversation relate_company_flags
    public function actionFormatConverstionRelateCompanyFlags()
    {
        //遍历会话
        //遍历会话下的子邮件
        //聚合子邮件的relate_company_flags
        //更新会话表

        $db = ProjectActiveRecord::getDbByClientId(1);
//        $clientids=  "1,9604,9645,9646,9648,9650,9652,14057,14058,14059,14064,14082";
        $clientids=  "1";
        //去tbl_mail_conversation找到对应的conversation_id，更新tbl_mail_conversation_folder.user_mail_id
        $maxConverSationId = $db->createCommand("select mail_conversation_id from tbl_mail_conversation where client_id in($clientids)"."order by mail_conversation_id desc limit 1")->queryScalar();
        $currConverSationId = 0;
        $filed = 'client_id,mail_conversation_id,user_mail_id';
        $sql = 'select ' . $filed . " from tbl_mail_conversation where  client_id in($clientids) ";

        while ($currConverSationId < $maxConverSationId) {
            self::info("当前会话Id：{$currConverSationId}");
            $conversationList = $db->createCommand($sql . ' and mail_conversation_id >' . $currConverSationId . ' order by mail_conversation_id asc limit 10000')->queryAll();

            if (empty($conversationList)) {
                break;
            }

            $currConverSationId = end($conversationList)['mail_conversation_id']??0;
            if ($currConverSationId != 0) {

                $conversationIds = implode(',',array_unique(array_column($conversationList,'mail_conversation_id')));

                //遍历会话下的子邮件
                $relateCompanyFlagList = $db->createCommand("select conversation_id,GROUP_CONCAT(relate_company_flag) as relate_company_flags from tbl_mail where 
                conversation_id in ($conversationIds) group by conversation_id")->queryAll();

                if (empty($relateCompanyFlagList)) {
                    continue;
                }

                $setFolderSql = '';
                foreach ($relateCompanyFlagList as $item) {
                    //过滤
                    $valueArr = array_filter(array_unique(explode(',',$item['relate_company_flags'])), function($value) {
                        return $value !== "";
                    });
                    $types = implode(',',$valueArr);
                    $folders = "'".$types."'";
                    $setFolderSql .= " when mail_conversation_id = {$item['conversation_id']} then {$folders} ";
                }


                //更新数据回会话表数据
                $updateMailConversationSql = "update tbl_mail_conversation set relate_company_types = case {$setFolderSql} ";
                $updateMailConversationSql .= " end where  mail_conversation_id in (".implode(',',array_column($relateCompanyFlagList,'conversation_id')).")";

                $res = $db->createCommand($updateMailConversationSql)->execute();
                self::info("更新了{$res}条数据");
            } else {
                break;
            }
        }
    }

    //迁移会话属性表relate_company_flag到第三张表
    public function actionMigrateRecompanyFlagToTable()
    {
        //todo 查询client_id为1的conversation

        //遍历会话
        //遍历会话下的子邮件
        //聚合子邮件的relate_company_flags
        //更新会话表

        $db = ProjectActiveRecord::getDbByClientId(1);
        $clientids=  "1,9604,9645,9646,9648,9650,9652,14057,14058,14059,14064,14082";
        //去tbl_mail_conversation找到对应的conversation_id，更新tbl_mail_conversation_folder.user_mail_id
        $maxConverSationId = $db->createCommand("select mail_conversation_id from tbl_mail_conversation where client_id in($clientids)"."order by mail_conversation_id desc limit 1")->queryScalar();
        $currConverSationId = 0;
        $filed = 'client_id,mail_conversation_id,user_mail_id';
        $sql = 'select ' . $filed . " from tbl_mail_conversation where  client_id in($clientids) ";

        while ($currConverSationId < $maxConverSationId) {
            self::info("当前会话Id：{$currConverSationId}");
            $conversationList = $db->createCommand($sql . ' and mail_conversation_id >' . $currConverSationId . ' order by mail_conversation_id asc limit 10000')->queryAll();

            if (empty($conversationList)) {
                break;
            }

            $currConverSationId = end($conversationList)['mail_conversation_id']??0;
            if ($currConverSationId != 0) {

                $conversationIds = implode(',',array_unique(array_column($conversationList,'mail_conversation_id')));

                //遍历会话下的子邮件
                $relateCompanyFlagList = $db->createCommand("select conversation_id,user_mail_id,client_id,GROUP_CONCAT(relate_company_flag) as relate_company_flags from tbl_mail where 
                conversation_id in ($conversationIds) group by conversation_id")->queryAll();

                if (empty($relateCompanyFlagList)) {
                    continue;
                }

                $insertSql = [];
                $setFolderSql = '';
                foreach ($relateCompanyFlagList as $item) {
                    //过滤
                    $types= array_unique(explode(',',$item['relate_company_flags']));
                    foreach ($types as $type) {
                        if ($type=="") {
                            continue;
                        }
                        //生成插入语句
                        $elem = [];
                        $elem[] = $item['conversation_id'];
                        $elem[] = $type?:0;
                        $elem[] = 0;
                        $elem[] = $item['client_id'];
                        $elem[] = $item['user_mail_id'];
                        $insertSql[] = '(' . implode(',', $elem) . ')';
                    }
                }

                if (empty($insertSql)) {
                    continue;
                }


                //插入tbl_mail_conversation_relate_company_flag表
                $insertSql = "insert into tbl_mail_conversation_identity(conversation_id,relate_company_flag,
    mail_count,client_id,user_mail_id) values "
                . implode(',', $insertSql)
                . "ON DUPLICATE KEY UPDATE relate_company_flag=VALUES(relate_company_flag)";

                $res = $db->createCommand($insertSql)->execute();
                self::info("更新了{$res}条数据");
            } else {
                break;
            }
        }
    }

    //将邮件表folder_id=5 and delete_flag=0的数据修复delete_flag=1
    public function actionUpdateDeleteFlagOfTrashMail($clientIds = 0, $lastNumber = null, $start = 0, $pageSize = 1000)
    {
        $clientIdList = $this->getClientIdList($clientIds, $lastNumber, $start);

        if (empty($clientIdList)) {
            echo "没有clientId数据";
            return;
        }

        foreach ($clientIdList as $clientId) {

            //查询用户id
            $sql = "SELECT user_id FROM tbl_user_info WHERE client_id=:client_id AND enable_flag=1";
            $userIds = \Yii::app()->account_base_db->createCommand($sql)->queryAll(true, [':client_id' => $clientId]);

            $userIds = array_column($userIds, 'user_id');
            if (empty($userIds)) {
                self::info("clientId:{$clientId} userId:为空------End\n");
                continue;
            }
            $userIdStr = implode(',', $userIds);

            //查询满足条件的邮件，批量更新delete_flag

            $querySql = "select min(mail_id) as min_mail_id,max(mail_id) as max_mail_id from tbl_mail where user_id in ($userIdStr) and folder_id=5 and delete_flag=0 and receive_time > '2021-01-01 00:00:00'";

            $db = ProjectActiveRecord::getDbByClientId($clientId);
            if (empty($db)) {
                self::info("client_id[$clientId] -- 无数据库");
                continue;
            }

            try {
                $total = $db->createCommand($querySql)->queryRow();
            } catch (\Throwable $e) {
                continue;
            }

            $minMailId = $total['min_mail_id'] ?? 0;
            $maxMailId = $total['max_mail_id'] ?? 0;

            if ($minMailId == 0 || $maxMailId == 0) {
                self::info("client_id:{$clientId} 没有数据需要更新了");
                continue;
            }

            $currMailId = $minMailId;

            while ($currMailId < $maxMailId) {

                $sql = "select  mail_id from tbl_mail  where user_id in ({$userIdStr})  and 
                folder_id=5 and delete_flag=0 and mail_id>={$currMailId} and mail_id<={$maxMailId}  
                order by  mail_id asc limit {$pageSize}";

                try {
                    $mailIds = $db->createCommand($sql)->queryColumn();
                } catch (\Throwable $e) {
                    self::info("client_id[$clientId] -- 执行失败,msg:".$e->getMessage());
                    //一般不会有异常，有循环调用的风险,先跳出循环
                    break;
                }

                if (empty($mailIds)) {
                    self::info("client_id:{$clientId} mailIds为空 没有数据需要更新了");
                    break;
                }

                $currMailId = end($mailIds);
                $mailIdsStr = implode(',', $mailIds);

                //更新数据，批量更新delete_flag
                $updateSql = "update tbl_mail set delete_flag=1 where mail_id in ($mailIdsStr) and folder_id=5 and delete_flag=0";
                try {
                    $res = $db->createCommand($updateSql)->execute();
                } catch (\Exception $e) {
                    self::info("client_id[$clientId] -- 执行失败".$e->getMessage());
                    break;
                }

                self::info("client_id:" . $clientId . "更新了{$res}条数据");
            }

        }

    }


    /**
     * @return void
     * 修复这段时间的邮件回调任务数据 11:16:00 ~ 11:25:00
     */
    public function actionRunFixMailCallbackService20240307($clientIds = 0, $lastNumber = null, $start = 0)
    {

        $clientIdList = $this->getClientIdList($clientIds, $lastNumber, $start);

        if (empty($clientIdList)) {
            echo "没有clientId数据";
            return;
        }

        foreach ($clientIdList as $clientId) {

            //查询用户
            $sql = "SELECT user_id FROM tbl_user_info WHERE client_id=:client_id AND enable_flag=1";
            $userIds = \Yii::app()->account_base_db->createCommand($sql)->queryColumn([':client_id' => $clientId]);

            if (empty($userIds)) {
                self::info("client_id:{$clientId} userId:为空------End\n");
                continue;
            }

            $userIdsStr = implode(',', $userIds);
            //查询这些用户在这段时间的收件
            $sql = "SELECT mail_id,user_id,client_id FROM tbl_mail WHERE user_id IN ($userIdsStr)
                AND receive_time >= '2024-03-07 11:16:00' AND receive_time <= '2024-03-07 11:25:00'";
            $db = ProjectActiveRecord::getDbByClientId($clientId);
            $mailInfos = $db->createCommand($sql)->queryAll();

            if (empty($mailInfos)) {
                self::info("client_id:{$clientId} mailInfos为空------End\n");
                continue;
            }

            //给这些邮件执行回调
            foreach ($mailInfos as $item) {

                $userId = $item['user_id'];
                $mailId = $item['mail_id'];
                $clientId = $item['client_id'];
                User::setLoginUserById($userId);

                $mail = new \common\library\mail\Mail($mailId);
                echo "Running----Client:[{$clientId}]----UserId:[{$userId}]----MailId:[{$mailId}]\n";
                $callbackService = new \common\library\mail\service\CallbackService($mail);
                $callbackService->runFixTasks240307();
            }

            //打印client 执行了多少回调
            self::info( "Client:[{$clientId}]----RunCount:[" . count($mailInfos) . "]");
        }
    }

    //app会话功能放量开关
    public function actionInquiryDataSwitch($clientIds = 0, $lastNumber = null, $start = 0, $openFlag = 1)
    {

        $clientIdList = $this->getClientIdList($clientIds, $lastNumber, $start);

        if (empty($clientIdList)) {
            echo "没有clientId数据";
            return;
        }

        foreach ($clientIdList as $clientId) {
            // 配置系统配置
            $client = \common\library\account\Client::getClient($clientId);
            $client->setExtentAttributes([
                'inquiry_data_switch' => $openFlag,
            ]);

            $client->saveExtentAttributes();
            if ($openFlag) {
                self::info("client_id:" . $clientId . " 询盘库放量开关已开启");
            } else {
                self::info("client_id:" . $clientId . "询盘库放量开关已关闭");
            }
        }
    }


    public function actionQuery()
    {
        $mailAliasObj = UserMailAlias::findByMailAlias($mailAlias, $this->clientId);
        if ($mailAliasObj && $mailAliasObj->user_mail_id != $this->userMailObj->user_mail_id) {
            throw new RuntimeException(\Yii::t('mail', 'Binding failed! The mailbox has been bound or registered'), 1406);
        }
    }

    //
    public function actionMigrateGmailUserMail($env = 'test')
    {

        $userMailId = [];
        if ($env = 'test') {
            $userMailId = [********,********,30000743,30000972,30001078];
        } else {
            $userMailId = [
                48556931,48517886,48568363,48546689,48552822,48540809,48403935,48519287,48518776,48408418,48403571,48498243,48479253,48476936,48476960,48465315,48512839,48512806,48512808,48506346,48288748,48518789,48518575,48518578,48518576,48403561,48465045,48512570,48512754,48357287,48380684,48331555,48194501,48208008,48344368,48488600,48510426,48512802,48226170,48482480,48357435,48497217,48272207,48468736,48497910,48370642,48518592,48510434,48454070,48339973,48497971,48494651,48460064,48497311,48343356,48368146,48388825,48461125,48438111,48522968,48340354,48439704,48287222,48517120,48483993,48472782,48432383,48497548,48432391,48528957,48525298,48340868,48535741,48525527,48445409,48529801,48466078,48526776,48526777,48443206,48535113,48537800,48512805,48527060,48526778,48512229,48553507,48533923,48469757,48257896,48553508,48528868,48575906,48568749,48568817,48440742,48535747,48557569,48571335,48569829,48557867,48538296,48535742,48549151,48548356,48557502,48556190,48555836,48450422,48553504,48557866,48556084,48417992,48568828,48541755,48557255,43022003,48552984,48564722,48566925,48540924,48555848,7278463,48416474,48557083,48566966,48560015,7278466,48564503,48557011,48418524,48546419,48510667,48557509,48566929,48555319,48559558,48554662,48191556,7278465,48257897,48554554,48576462,48190760,48336227,48554845,48561892,48568836,48573227,48416477,48430197,48540919,48554535,48370637,48416475,48197854,48191557,48550395,48522964,48498476,48416470,48580528,48580418,48537673,48581461,48578830,48579875,48573225,48580063,48579871,48557685,48564482,48537672,48571678,48562506,48444199,48413856,48566930,48582495,48483947,48571327,48513358,48413859,48494364,48568981,48512827,48370174,48558063,48568002,48569402,48575823,48548601,48574368,48279781,48221778,48543608,48573198,48573245,48573264,48573259,48540923,48559271,48571386,48572511,48460851,48453552,48498037,48453559,48340876,48250656,48493936,48439311,48367662,48571325,48438168,48304232,48463923,48383405,48412092,48445762,48362165,48341874,48387774,48360040,48363801,48342475,48342432,48342452,48343586,48342349,48342476,48477932,48477864,48477748,48474690,48288737,48476265,48304806,48253211,48304800,48304803,48413850,48362162,48477930,48304810,48253215,48253209,48484678
            ];
        }

        $gmail = new \common\library\mail\GoogleEmailChecker();
        $gmail->saveGoogleEmailToCacheUserMail($userMailId);
    }


    /**
     * 验证置顶邮件状态
     */
    public function actionCheckTop($userId=55301374)
    {
        //两个问题

        //1.邮件置顶取消了为啥缓存key还有，导致走了指定列表
        //2.子邮件没有置顶了，会话数据还有置顶标记，为什么

        //查看缓存key状态
        $redis = RedisService::cache();
        $topFlag = $redis->get(Pin::TYPE_MAIL_TOP_CACHE_KEY . $userId);
        var_dump($topFlag);




        //查看哪一封子邮件取消置顶，但是没有让邮件数据置顶
    }


    public function actionTestData()
    {
        $db = ProjectActiveRecord::getDbByClientId(1);
        $sql = "SELECT * FROM `tbl_ai_customer_advice` `t` WHERE client_id=1  and email='<EMAIL>' and advice_id=1102261278 LIMIT 1";
        $res = $db->createCommand($sql)->queryRow();
        var_dump($res);
    }
    public function actionTrackAiContentByHandlerId($userId=********,$handlerId=4301322396)
    {
        User::setLoginUserById($userId);
        //找到计划，输出ai_write_flag，以及sendtence
        $user = User::getLoginUser();
        $clientId = $user->getClientId();

        $handler = new MarketingAutomationHandler($clientId, $handlerId);
        //根据processList找到对应的task_id或者mail_id，根据contact_type来判断
        $contactType = $handler->touch_type;
        $processList = $handler->process_list;
        var_dump($contactType);
        var_dump($processList);
        //根据task_id或者mail_id找到对应的生成的多内容

        //输出最终发出去的邮件分发规则
    }


    public function actionRunTask()
    {
        \common\library\server\crontab\task\MarketingAutomationScheduledTask::runPlanDelayTrigger(1);
    }

    //校验生成的多内容是不是相等的，有没有替换成功
    public function actionCheckMulTiContent($userId = 249520518, $mailId = 0,$handlerId = 4306257219,$planId = 0)
    {
        User::setLoginUserById($userId);
        $clientId = User::getLoginUser()->getClientId();

        $_handlerFilter = new MarketingAutomationHandlerFilter($clientId);
        $_handlerFilter->user_id =$userId;
        $_handlerFilter->is_used = 1;
        $_handlerFilter->enable_flag = 1;
        $_handlerFilter->ai_write_flag = 1;
        if (!empty($mailId)) {
            $_handlerFilter->rawWhere(" AND process_list::bigint[] && ARRAY[" . $mailId . "]::bigint[] ");
        }else if (!empty($handlerId)) {
            $_handlerFilter->rawWhere(" AND handler_id = " . $handlerId);
        }else if(!empty($planId)) {
            $_handlerFilter->rawWhere(" AND plan_id = " . $planId);
        }
        $result = $_handlerFilter->rawData();

        if (empty($result)) {
            self::info("没有找到对应的handler");
            return;
        }
        $sentence = $result[0]['sentence']??[];
        $touchType = $result[0]['touch_type']??0;
        $mailId = $result[0]['process_list'][0]??0;

        if (empty($sentence)) {
            self::info("没有找到对应的sentence");
            return;
        }

        if (empty($mailId)) {
            self::info("没有找到对应的mailId");
            return;
        }

        self::info("前端选取的sentence:" . $sentence);
        self::info("触发渠道: " . ($touchType == 1?'edm':'expose'));
        self::info("任务id或主邮件id: " . $mailId);
        //查询分发规则

        //edm的从edmtask获取
        if($touchType ==1) {
            $task = new \common\library\edm\EdmTask($clientId, $mailId);
            $extra = $task->getParamExtra();
            if (!empty($extra)) {
                self::info("extra:" . json_encode($extra));
            } else{
                self::info("extra为空");
            }
        } else if ($touchType == 2) {
            $sql = "select extra from tbl_mail_expose where client_id=$clientId and user_id=$userId and mail_id!=$mailId and main_mail_id=$mailId";
            $db = ProjectActiveRecord::getDbByClientId($clientId);
            $extra = $db->createCommand($sql)->queryColumn();
            if (!empty($extra)) {
                foreach ($extra as  $item)
                {
                    $item = json_decode($item,true);
                    $config = $item['multi_content_subjects']??[];
                    self::info("分发规则:" . json_encode($config));
                }
            } else{
                self::info("extra为空");
            }
        }

        //expose从tbl_mail_expose获取
        //获取多正文的内容
        $contenList = MultiMailContent::model()->findAll('client_id=:client_id AND user_id=:user_id AND mail_id=:mail_id',
            [
                ':client_id' => $clientId,
                ':user_id' => $userId,
                ':mail_id' => $mailId,
            ]);

        if (empty($contenList)) {
            self::info("没有找到对应的多内容");
            return;
        } else {
            self::info("找到了".count($contenList)."个多内容");
        }

        //md5 content，判断是否content有相等的，并且找出相等的content_id
        $contentMd5List = [];
        $contentIdList = [];
        foreach ($contenList as $content) {
            $contentMd5 = md5($content->content);
            if (isset($contentMd5List[$contentMd5])) {
                $contentIdList[] = $content->content_id;
            } else {
                $contentMd5List[$contentMd5] = $content->content_id;
            }
        }

        if (empty($contentIdList)) {
            self::info("没有重复的content");
            return;
        }

        self::info("重复的contentIdList:" . json_encode($contentIdList));
    }

    /**
     * @return void
     * 修复这段时间的邮件回调任务数据 10:35 ~ 11.20
     */
    public function actionRunFixMailCallbackService20241128($clientIds = 0, $lastNumber = null, $start = 0,$startReceiveTime = '2024-11-28 10:35:00',$endReceiveTime ='2024-11-28 11:20:00')
    {
        //查询影响到的

        if (empty($clientIds)) {
            //生产
            $hostDbSetData = "select group_concat(set_id) as set_ids from tbl_db_set where host='pgm-bp1uk2s0i188kw8n.pg.rds.aliyuncs.com'";
            $accountDb = \Yii::app()->account_base_db;
            $dbSetIds = $accountDb->createCommand($hostDbSetData)->queryRow();
            $dbSetIds = $dbSetIds['set_ids']??'';
            if (empty($dbSetIds)) {
                self::info("dbSetIds is empty------Stop");
                return false;
            }
            //获取clientId
            $condition = " enable_flag = 1 AND pgsql_set_id in($dbSetIds) AND client_id not in (
                SELECT client_id FROM tbl_privilege_client_system
                WHERE system_id = 'crm_ames_basic' AND enable_flag = 1
            )";
            $clientList = \Client::model()->findAll($condition);
            $clientIdList = array_column($clientList, 'client_id');
            if (empty($clientIdList)) {
                echo "没有clientId数据";
                return;
            }
        } else {
            $clientIdList = $this->getClientIdList($clientIds, $lastNumber, $start);
        }


        foreach ($clientIdList as $clientId) {

            //查询用户
            $sql = "SELECT user_id FROM tbl_user_info WHERE client_id=:client_id AND enable_flag=1";
            $userIds = \Yii::app()->account_base_db->createCommand($sql)->queryColumn([':client_id' => $clientId]);

            if (empty($userIds)) {
                self::info("client_id:{$clientId} userId:为空------End\n");
                continue;
            }

            $userIdsStr = implode(',', $userIds);
            //查询这些用户在这段时间的收发件，过滤草稿以及彻底删除的邮件
            $sql = "SELECT mail_id,user_id,client_id FROM tbl_mail WHERE user_id IN ($userIdsStr)
                AND receive_time >= '$startReceiveTime' AND receive_time <= '$endReceiveTime' and folder_id not in (0,9,11) and delete_flag=0";;
            $db = ProjectActiveRecord::getDbByClientId($clientId);
            $mailInfos = $db->createCommand($sql)->queryAll();

            if (empty($mailInfos)) {
                self::info("client_id:{$clientId} mailInfos为空------End\n");
                continue;
            }

            //给这些邮件执行回调
            foreach ($mailInfos as $item) {

                $userId = $item['user_id'];
                $mailId = $item['mail_id'];
                $clientId = $item['client_id'];
                User::setLoginUserById($userId);

                $mail = new \common\library\mail\Mail($mailId);
                echo "Running----Client:[{$clientId}]----UserId:[{$userId}]----MailId:[{$mailId}]\n";
                $callbackService = new \common\library\mail\service\CallbackService($mail);
                $callbackService->runFixTasks241128();
            }

            //打印client 执行了多少回调
            self::info( "Client:[{$clientId}]----RunCount:[" . count($mailInfos) . "]");
        }
    }

    /**
     * @return void
     * @throws \AlibabaCloud\Client\Exception\ClientException
     * @throws \AlibabaCloud\Client\Exception\ServerException
     * 查询aliyunmail创建成功的实例
     * 注意时间是utc 东八区要+8来看
     * startCreateTime 2024-12-12
     */
    public function actionInstanceList($startCreateTime, $endCreateTime, $page = 1, $pageSize = 20)
    {
        $res = common\library\alimail\AliyunAlimailApi::client()->queryAvailableInstances('', '', [],$pageSize,$page,$startCreateTime,$endCreateTime);
        var_dump($res);
    }

    //  ["Status"]=>
    //        string(6) "Normal"
    //        ["SubscriptionType"]=>
    //        string(12) "Subscription"
    //        ["OwnerId"]=>
    //        int(1145897595410559)
    //        ["EndTime"]=>
    //        string(20) "2025-12-12T16:00:00Z"
    //        ["InstanceID"]=>
    //        string(41) "alimailhz059bff965ad146f4adb0a1173d92f24c"
    //        ["ProductCode"]=>
    //        string(7) "alimail"
    //        ["CreateTime"]=>
    //        string(20) "2024-12-12T02:51:10Z"
    //        ["ProductType"]=>
    //        string(22) "alimail_okki_public_cn"
    //        ["RenewalDurationUnit"]=>
    //        string(1) "M"
    //        ["Seller"]=>
    //        string(5) "26842"
    //        ["SubStatus"]=>
    //        string(6) "Normal"
    //        ["RenewStatus"]=>
    //        string(13) "ManualRenewal"

    public function actionFixInitAliMail($client_id=393796, $order_time = 12, $instanceId='alimailhz059bff965ad146f4adb0a1173d92f24c', $orderId='242512117230281')
    {

        if (empty($client_id) || empty($order_time) || empty($instanceId) || empty($orderId)) {
            return $this->fail(ErrorCode::CODE_FAIL, '参数错误');
        }

        $newOrderTime = 0;
        if ($order_time < 12) {
            return $this->fail(ErrorCode::CODE_FAIL, "时长不能小于 12 月");
        }
        else if ($order_time >= 12 && $order_time < 24) {
            $newOrderTime = 12;
        }
        else if ($order_time >= 24) {
            $newOrderTime = 24;
        }

        $client = \common\library\account\Client::getClient($client_id);

        // 生成虚假域名
        $prefix = $client->client_type != 1 ? 'dev' : 'prod';
        $domain = 'fake' . $prefix . substr(md5($client_id),0,46) . '.com';
        LogUtil::info("random domain:" . $domain);
        $accountNum = 5;

        // 创建以后信息保存到 db
        $startTime = date('Y-m-d H:i:s');
        $endTime = date('Y-m-d H:i:s', strtotime("+$newOrderTime months"));

        $o = new AliMailOrganizationObj($client_id);

        //存在则不更新了
        if (!$o->isNew()) {
            return $this->fail(ErrorCode::CODE_FAIL, '组织已存在');
        }

        $o->client_id = $client_id;
        $o->instance_id = $instanceId;
        $o->domain = $domain;
        $o->order_id = $orderId;
        $o->order_time = $newOrderTime;
        $o->purchase_time = $order_time;
        $o->account_num = $accountNum;
        $o->detect_status = AliMailOrganization::DETECT_STATUS_ALI_MAIL_INITIALING;
        $o->start_time = $startTime;
        $o->end_time = $endTime;
        $o->create_time = date('Y-m-d H:i:s');
        $res = $o->save();
        if (!$res) {
            LogUtil::error("AliMailOrganization db 创建失败, client: $client_id, instanceId: $instanceId, orderId: $orderId");
            return $this->fail(ErrorCode::CODE_FAIL, '组织创建失败 1');
        }

        $client = \common\library\account\Client::getClient($client_id);
        $client->setExtentAttributes([\common\library\account\Client::EXTERNAL_KEY_ALI_MAIL_FLAG => AliMailOrganizationObj::ALI_MAIL_FLAG_DEFAULT_INITIALIZED_HIDDEN]);
        $client->saveExtentAttributes();

        try {
            $innerApi = new \common\library\api\InnerApi('oss_api');
            $innerApi->setAccessLog(true);
            $response = $innerApi->call('notifyAlimailRegister', ['client_id' => $client_id,]);
        } catch (\Exception $e) {
            \LogUtil::error("OS内部服务异常 interface[notifyAlimailRegister] clientId[{$client_id}] msg[{$e->getMessage()} trace[{$e->getTraceAsString()}]");
        }

        //打印成功日志
        LogUtil::info("AliMailOrganization db 创建成功, client: $client_id, instanceId: $instanceId, orderId: $orderId");
    }



}

