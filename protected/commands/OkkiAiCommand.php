<?php


use common\library\ai_agent\Helper;
use common\library\ai_agent\jobs\AiAsyncJob;
use common\library\ai_agent\vector\CommonVectorService;
use common\library\ai_agent\vector\EmbeddingService;
use common\library\async_task\AsyncTask;
use common\library\privilege_v3\PrivilegeConstants;
use common\library\privilege_v3\PrivilegeService;
use common\library\queue_v2\QueueService;
use common\library\report\dingtalk\DingTalkRobot;
use common\library\todo\Feed;
use common\library\todo\TodoConstant;
use common\models\vector\CommonVector;

class OkkiAiCommand extends CrontabCommand
{
    const LAST_DAY_OF_THIS_MONTH = 32;
    const LAST_DAY = [
        28 => [28,29,30,31, self::LAST_DAY_OF_THIS_MONTH],
        29 => [29,30,31, self::LAST_DAY_OF_THIS_MONTH],
        30 => [30,31, self::LAST_DAY_OF_THIS_MONTH],
        31 => [31, self::LAST_DAY_OF_THIS_MONTH]
    ];

    public function actionRunJob($params): void
    {
        LogUtil::info("OkkiAI_RunJob", ['params' => $params]);
        if (is_string($params)) {
            $params = json_decode($params, true);
        }
        $job = new AiAsyncJob($params);
        $job->handle();
    }

    // ./yiic-test okkiAi aiAnalysisSubscription --configId=3627049624 --dryRun=0
    public function actionAiAnalysisSubscription($configId = '', $dryRun = 1, $clientIds = '')
    {
        ini_set("memory_limit", "1500M");
        $totalMinute = 60; // 需要在50分钟内执行完成
        $processLimit = 30; // 进程数上限
        $elementsPerMinute = 0.6; //每分钟进程能处理多少任务
        // 获取符合条件的订阅信息
        // 月+今天几号+小时
        $clientIds = $clientIds ? [$clientIds] : array_column($this->getClientList(0, true, true), 'client_id');
        $week = date('N') ;
        $day = date('j');
        $lastDay = date('t');
        $hour = date('G');
        if ($lastDay == $day) {
            $day = implode(',', self::LAST_DAY[$lastDay]);
        }

        // 订阅本身的数量很少，是可以直接全部放在数组里做处理的
        $result = [];
        foreach ($clientIds as $clientId)
        {
            LogUtil::info("Begin_AiAnalysisSubscription", ['client_id' => $clientId]);
            try {
                $selectWeekSql = "time_type = 1 AND {$week} = ANY(receive_day) and receive_time = {$hour}";
                $selectMonthSql = "time_type = 2 and (ARRAY[{$day}]::smallint[] && receive_day) and receive_time = {$hour}";

                if ($configId) {
                    $selectSql = "select * from tbl_ai_subscription where config_id = {$configId}";
                } else {
                    $selectSql = "select * from tbl_ai_subscription where client_id = $clientId and enable_flag = 1 and subscribe_type in (1,2) and (($selectWeekSql) or ($selectMonthSql))";
                }
                $pg = PgActiveRecord::getDbByClientId($clientId);

                if (empty($pg)) continue;

                $subscriptionList = $pg->createCommand($selectSql)->queryAll();
                if (!$dryRun) {
                    foreach ($subscriptionList as $subscription) {
                        try {
                            $userId = $subscription['user_id'];
                            $subscribeType = intval($subscription['subscribe_type']);
                            $scene = match ($subscribeType) {
                                \common\library\statistics\subscribe\AiSubscribeList::AI_SUBSCRIBE_TYPE_ASSET_ANALYSIS => \common\library\async_task\AsyncTaskConstant::SCENE_AI_SYSTEM_ASSET_ANALYSIS,
                                \common\library\statistics\subscribe\AiSubscribeList::AI_SUBSCRIBE_TYPE_TEAM_ANALYSIS => \common\library\async_task\AsyncTaskConstant::SCENE_AI_SYSTEM_TEAM_ANALYSIS
                            };

                            $timeRange = $subscription['time_range'];
                            $params = json_decode($subscription['params'], true);
                            $params = $this->buildParams($params, $timeRange);
                            $result[] = [
                                'clientId' => $clientId,
                                'userId' => $userId,
                                'params' => $params['params'],
                                'echo_params' => $params['echo_params'],
                                'config_id' => $subscription['config_id'],
                                'scene' => $scene
                            ];
                        } catch (Exception $e) {
                            LogUtil::error("AiAnalysisSubscription_Error_UserId:{$userId}" . $e->getMessage());
                            $this->cleanUpForClient($clientId);
                            continue;
                        }
                    }
                }
                // 释放内存
                $this->cleanUpForClient($clientId);
            } catch (Exception $exception) {
                LogUtil::error("AiAnalysisSubscription_Error_ClientId:{$clientId}" . $exception->getMessage(), ['client_id' => $clientId]);
                $this->cleanUpForClient($clientId);
                continue;
            } finally {
                $this->cleanUpForClient($clientId);
            }
            LogUtil::info("finish_AiAnalysisSubscription", ['client_id' => $clientId]);
        }

        if (empty($result)) {
            $now = date("Y-m-d H:i");
            self::info("aiAnalysisSubscription 没有需要执行的订阅任务",[
                'time' => $now
            ]);
            return ;
        }
        $maxElementsPerProcess = $elementsPerMinute * $totalMinute;
        $maxTotalElements = $processLimit * $maxElementsPerProcess;
        $resultCount = count($result);
        if ($resultCount > $maxTotalElements) {
            $params = [
                'taskCount' => $resultCount,
                'maxElementsPerProcess' => $maxElementsPerProcess,
                'maxTotalElements' => $maxTotalElements,
                'totalMinute' => $totalMinute,
                'processLimit' => $processLimit,
                'elementsPerMinute' => $elementsPerMinute
            ];
            $this->sendSubscriptWarning($params);
        }

        foreach ($this->chunkTasks($result, $totalMinute, $processLimit, $elementsPerMinute) as $task) {
            \common\library\CommandRunner::run('OkkiAi', 'handleAiAnalysisSubscriptionTask', [
                'subscriptionList' => "'" . json_encode($task, JSON_UNESCAPED_UNICODE) . "'"
            ]);
            self::info("客户分析订阅任务投递完成", [
                'info' => $task
            ]);
        }


    }

    public function buildParams(array $allParams, $timeRange)
    {
        $timeRangeList = [
            7 => 'P7D',
            30 => 'P30D',
            60 => 'P60D',
            90 => 'P90D',
            180 => 'P180D',
            365 => 'P365D'
        ];

        if (empty($timeRange)) return $allParams;

        // 处理时间范围参数
        $end = new \DateTime();
        $endDate = $end->format('Y-m-d');
        $start = $end->sub(new DateInterval($timeRangeList[$timeRange]));
        $startDate = $start->format('Y-m-d');

        $params = array_column($allParams['params'], null, 'field');
        $echoParams = array_column($allParams['echo_params'], null, 'key');
        $timeRangeParams['type'] = 'date';
        $timeRangeParams['field'] = 'common.date';
        $timeRangeParams['value']['start'] = $startDate;
        $timeRangeParams['value']['end'] = $endDate;
        $echoTimeRangeParams['key'] = 'common.date';
        $echoTimeRangeParams['label'] = '时间区间';
        $echoTimeRangeParams['value'] = $startDate . '~' . $endDate;
        $params['common.date'] = $timeRangeParams;
        $echoParams['common.date'] = $echoTimeRangeParams;

        $allParams['params'] = array_values($params);
        $allParams['echo_params'] = array_values($echoParams);
        return $allParams;
    }


    /** 定时任务
     * ai异常率告警
     * @return void
     */
    public function actionAiAbnormalRateNotify()
    {
        try {
            $failRateThreshold = 0.06;
            $growthRateThreshold = 0.1;
            $currentTotal = 0;
            $currentTotalFailCount = 0;

            $endDate = date("Y-m-d 23:59:59", strtotime("-1 day"));
            $startDate = date("Y-m-d 00:00:00", strtotime("-2 day"));

            $startDateFormat = date("Y-m-d", strtotime("-2 day"));
            $endDateFormat = date("Y-m-d", strtotime("-1 day"));

            $loginClientId = \Yii::app()->params['env'] == 'test' ? 9650 : 6534;

            $dataWorkDb = DataWorkActiveRecord::getDbByClientId($loginClientId);

            $clientWhere = \Yii::app()->params['env'] == 'test'  ? "client_id in (9650,333383,1) " : <<<ClientWhere
client_id in
      (71, 181, 285, 323, 674, 756, 1034, 1064, 1130, 1141, 1182, 1838, 1913, 1972, 1984, 2327, 2419, 2501, 3402, 3764,
       3871, 3946, 4204, 4216, 4300, 4611, 4621, 4699, 4743, 4862, 5020, 5041, 5117, 5300, 5731, 5959, 6848, 7126, 7379,
       7708, 7950, 8914, 9550, 9732, 9901, 10516, 10628, 11727, 11813, 12665, 12872, 13878, 14474, 14716, 15174, 15405,
       15738, 16987, 16993, 17113, 17372, 17586, 18079, 18297, 18778, 18907, 19328, 19469, 19730, 19750, 19902, 20262,
       21327, 22334, 22482, 22599, 22830, 23660, 23791, 23841, 24596, 25240, 26302, 26608, 27037, 27222, 27359, 27377,
       27866, 28337, 28339, 28564, 28575, 28635, 28998, 29068, 29094, 29205, 29208  , 29518, 29573, 29640, 29851, 29883,
       30085, 30504, 30653, 31010, 31141, 31235, 31386, 31478, 31637, 31668, 31865, 31999, 32066, 32095, 32142, 32197,
       32252, 32315, 32393, 32542, 32564, 32648, 32811, 33125, 33203, 33205, 33209, 33227, 33338, 33554, 33662, 33736,
       33859, 34173, 34224, 34386, 34627, 34646, 34789, 34929, 34999, 35193, 35237, 35309, 35528, 35586, 35708, 35721,
       35815, 35824, 35958, 36105, 36148, 36204, 36223, 36241, 36242, 36262, 36401, 36412, 36513, 36644, 36691, 36728,
       36836, 36913, 36945, 37029, 37494, 37515, 37599, 37651, 37688, 37719, 37994, 38128, 38691, 38746, 39261, 40083,
       40136, 40320, 41308, 41446, 41755, 41931, 41999, 42284, 42354, 42387, 42612, 42867, 43116, 43602, 43670, 44393,
       44598, 44841, 45018, 45062, 45858, 45880, 46097, 46103, 46240, 46451, 46503, 46614, 47266, 47280, 47300, 47425,
       47722, 47879, 48043, 48279, 48427, 49206, 49395, 49667, 49686, 50108, 50460, 51028, 51255, 51369, 51657, 51958,
       51969, 52015, 52124, 52257, 53180, 53271, 54237, 54369, 55581, 56648, 57449, 58575, 58690, 58731, 58912, 58923,
       59047, 59219, 59382, 59435, 59487, 59932, 60288, 61225, 61279, 61637, 61883, 62109, 62379, 62707, 62813, 62852,
       63208, 63509, 63521, 63586, 63814, 63938, 63967, 63980, 64035, 64051, 64052, 64805, 65530, 65654, 66297, 66432,
       66609, 66673, 67074, 67468, 67950, 68158, 68355, 68443, 68493, 69015, 69437, 69544, 69721, 69872, 70026, 70337,
       70374, 70442, 70538, 70645, 70754, 70858, 71274, 71488, 71496, 71692, 72051, 72107, 72336, 72486, 72718, 73012,
       73035, 73175, 73387, 73910, 73947, 74184, 74241, 74309, 74373, 74502, 74589, 74784, 74853, 75155, 75298, 75425,
       75527, 75547, 75709, 75822, 76012, 76042, 76595, 76708, 77013, 77070, 77094, 77275, 77478, 77690, 77942, 78347,
       78395, 78573, 78739, 78821, 78971, 79135, 79247, 79303, 79550, 79639, 79689, 79759, 79805, 79876, 79986, 80354,
       80657, 80700, 80725, 80962, 81135, 81361, 81442, 81659, 81736, 81862, 81875, 82088, 82174, 82207, 82442, 82549,
       83025, 83034, 83149, 83381, 83495, 83543, 83624, 83860, 83928, 84337, 84633, 84846, 84909, 85529, 85675, 85677,
       86297, 86419, 86694, 86919, 86962, 87097, 87173, 87280, 87537, 87661, 87823, 88066, 88570, 88645, 88744, 88784,
       88928, 89013, 89054, 89071, 89091, 333352, 333385, 333540, 333682, 334073, 334176, 334315, 334412, 334475,
       335260, 335496, 335522, 336056, 336216, 336326, 336340, 336856, 336926, 337032, 337058, 337087, 337185, 337391,
       337412, 337984, 338030, 338117, 338485, 338712, 339018, 339106, 339628, 339737, 339754, 339803, 339923, 339947,
       340166, 340299, 340392, 340469, 340484, 340566, 340737, 340741, 340777, 340828, 341009, 341274, 341283, 341470,
       341742, 342034, 342144, 342147, 342182, 342188, 342594, 343163, 343334, 343576, 344249, 344491, 344495, 345086,
       345535, 345556, 345876, 345891, 345969, 346418, 346779, 346999, 347422, 347614, 347863, 348065, 348606, 349183,
       349271, 349296, 349388, 349526, 349813, 349934, 350190, 350242, 350293, 350424, 350435, 350639, 350726, 350788,
       350799, 350916, 350932, 350983, 351011, 351047, 351068, 351069, 351073, 351145, 351199, 351216, 351268, 351275,
       351279, 351390, 351447, 351449, 351479, 351501, 351523, 351552, 351562, 351563, 351641, 351649, 351658, 351663,
       351702, 351703, 351720, 351729, 351735, 351737, 351766, 351810, 351827, 351835, 351871, 351883, 351916, 351930,
       351949, 351963, 351980, 351993, 352013, 352025, 352030, 352033, 352041, 352046, 352057, 352068, 352075, 352101,
       352103, 352118, 352138, 352145, 352146, 352155, 352169, 352180, 352181, 352182, 352193, 352284, 352313, 352371,
       352392, 352414, 352441, 352469, 352480, 352527, 352587, 352626, 352672, 352678, 352728, 352757, 352761, 352773,
       352785, 352797, 352799, 352814, 352926, 352936, 352981, 352987, 352989, 353000, 353001, 353016, 353029, 353042,
       353056, 353062, 353075, 353108, 353115, 353155, 353167, 353168, 353193, 353211, 353246, 353263, 353279, 353287,
       353293, 353294, 353321, 353347, 353364, 353371, 353388, 353411, 353414, 353446, 353451, 353455, 353474, 353487,
       353501, 353505, 353521, 353550, 353560, 353563, 353605, 353616, 353623, 353648, 353708, 353719, 353739, 353782,
       353825, 353831, 353842, 353882, 353887, 353923, 353933, 353934, 353946, 353947, 354031, 354036, 354065, 354088,
       354104, 354112, 354118, 354139, 354157, 354184, 354216, 354231, 354277, 354284, 354299, 354307, 354312, 354318,
       354326, 354370, 354395, 354396, 354403, 354407, 354485, 354487, 354500, 354549, 354605, 354619, 354646, 354647,
       354655, 354669, 354675, 354699, 354725, 354730, 354735, 354781, 354785, 354797, 354844, 354851, 354935, 354951,
       354968, 354972, 354983, 354995, 355015, 355064, 355129, 355153, 355196, 355221, 355246, 355296, 355326, 355333,
       355362, 355394, 355398, 355399, 355431, 355440, 355469, 355481, 355507, 355551, 355564, 355584, 355611, 355620,
       355669, 355678, 355727, 355733, 355815, 355909, 355927, 355969, 355991, 356047, 356057, 356067, 356116, 356185,
       356186, 356197, 356328, 356368, 356415, 356426, 356477, 356482, 356501, 356549, 356606, 356622, 356628, 356647,
       356725, 356773, 356781, 356783, 356952, 356955, 356991, 357033, 357045, 357046, 357106, 357217, 357221, 357341,
       357355, 357451, 357491, 357501, 357513, 357515, 357522, 357526, 357609, 357614, 357627, 357648, 357659, 357676,
       357708, 357901, 357935, 358000, 358062, 358078, 358114, 358143, 358147, 358466, 358580, 358599, 358753)
ClientWhere;

            $where = "  create_time >= '{$startDate}' and create_time <= '{$endDate}' and  {$clientWhere} ";

            $totalSql = "
select count(1), scene_type, TO_CHAR(create_time::timestamp with time zone::date, 'YYYY-MM-DD') AS stat_date
from tbl_ai_service_process_record
WHERE {$where}
and scene_type in (1,2,7,8,9,10,11,12,13,14,15,18)
group by scene_type, stat_date
";

            $totalList = $dataWorkDb->createCommand($totalSql)->queryAll();

            $sceneTypeToDateCountMap = [];

            foreach ($totalList as $item)
            {

                $statDate = $item['stat_date'];
                $sceneType = $item['scene_type'];
                $count = $item['count'];

                $sceneTypeToDateCountMap[$sceneType][$statDate] = $count;


                // 统计一下昨天失败的总数
                if ($statDate == $endDateFormat) {
                    $currentTotal += $count;
                }
            }

            $failSql = <<<SQL
select count(1),scene_type,stat_date from (
SELECT TO_CHAR(create_time::timestamp with time zone::date, 'YYYY-MM-DD') AS stat_date,
       record_id,
       scene_type                                  ,
       CASE
           WHEN scene_type = 1 THEN '写信'
           WHEN scene_type = 2 THEN '润色'
           WHEN scene_type = 6 THEN 'insights'
           WHEN scene_type = 7 THEN '数据分析'
           WHEN scene_type = 8 THEN '新建客户'
           WHEN scene_type = 9 THEN '商机跟进'
           WHEN scene_type = 10 THEN '客户跟进'
           WHEN scene_type = 11 THEN '报表生成'
           WHEN scene_type = 12 THEN '沟通质检'
           WHEN scene_type = 14 THEN '辅助回复'
           WHEN scene_type = 15 THEN '沟通建议'
           WHEN scene_type = 18 THEN '客户资产分析'
           ELSE CAST(scene_type AS TEXT) -- 使用 CAST 确保返回的是文本类型
           END                                     AS scene_type_text,
       client_id,
       create_user                                 AS user_id,
       status,
       error_code,
       error_msg,
       CASE
           WHEN status = 1 AND error_code = 0 THEN '成功'
           WHEN status = 1 AND error_code != 0 THEN '成功-ERROR'
           WHEN status = 2 AND error_code = 40004 THEN '失败-风控异常'
           WHEN status = 2 AND error_code = 40001 THEN '失败-GPT异常'
           WHEN status = 2 AND error_code IN (40011, 40012, 40013, 40014, 40015, 40016) THEN '失败-解析SQL异常'
           WHEN status = 2 AND error_code = 40041 AND create_time::timestamp with time zone::date >= '2023-12-20'
               THEN '失败-超时'
           WHEN status = 2 AND error_code = 40041 AND create_time::timestamp with time zone::date < '2023-12-20'
               THEN '成功'
           WHEN status = 2 THEN '失败-其他异常'
           WHEN status = 3 THEN '失败-风控异常' -- 不应该归类为技术异常
           WHEN status = 0 THEN '失败-初始化' -- 需要具体排插
           ELSE '未知'
           END                                     AS record_status_text,
       create_time
FROM tbl_ai_service_process_record
WHERE {$where}

    and  scene_type in (1,2,7,8,9,10,11,12,13,14,15,18)
  AND scene_type NOT IN (6)                                 -- 使用 NOT IN 替代注释掉的 AND scene_type != 6
  AND NOT ( status = 1 AND error_code = 0 AND request_data = '{}')) as tbl_record_origin
                                     where record_status_text not in ('成功', '成功-ERROR','失败-风控异常')
 group by scene_type,stat_date;

SQL;
            $failList = $dataWorkDb->createCommand($failSql)->queryAll();


            $failRateMap = [];


            foreach ($failList as $item)
            {
                $statDate = $item['stat_date'];
                $sceneType = $item['scene_type'];
                $count = $item['count'];

                if (!isset($sceneTypeToDateCountMap[$sceneType][$statDate])) {
                    $sceneTypeToDateCountMap[$sceneType][$statDate] = 0;
                }


                $totalCount = $sceneTypeToDateCountMap[$sceneType][$statDate];

                $failRate = $totalCount == 0 ? $count * 100 : round($count / $totalCount * 100, 2);
                $failRateMap[$sceneType][$statDate] = [
                    'failRate' => $failRate,
                    'failCount' => $count
                ];
            }

            // 统计
            $warningMap = [];

            foreach ($sceneTypeToDateCountMap as $sceneType => $dateInfo)
            {
                $failRateArr = $failRateMap[$sceneType] ?? [];

                $previousFailInfo = $failRateArr[$startDateFormat] ?? [];
                $currentFailInfo = $failRateArr[$endDateFormat] ?? [];

                $previousFailRate = $previousFailInfo['failRate'] ?? 0;
                $currentFailRate = $currentFailInfo['failRate'] ?? 0;

                $previousFailCount = $previousFailInfo['failCount'] ?? 0;
                $currentFailCount = $currentFailInfo['failCount'] ?? 0;
                $currentTotalFailCount += $currentFailCount;

                $previousTotalCount = $dateInfo[$startDateFormat] ?? 0;
                $currentTotalCount = $dateInfo[$endDateFormat] ?? 0;


                if ($previousFailRate == $currentFailRate) continue;

                $growthRate = $previousFailRate == 0 ? $currentFailRate * 100
                    : (($currentFailRate - $previousFailRate) / $previousFailRate) * 100;

                if ($growthRate >= $growthRateThreshold || $currentFailRate >= $failRateThreshold)
                {
                    $warningMap[$sceneType] = [
                        'date' => $endDateFormat,
                        'sceneType' => $sceneType,
                        'failRate' => $currentFailRate,
                        'growthRate' => $growthRate,
                        'preFailRate' => $previousFailRate,
                        'previousFailCount' => $previousFailCount,
                        'currentFailCount' => $currentFailCount,
                        'previousTotalCount' => $previousTotalCount,
                        'currentTotalCount' => $currentTotalCount
                    ];
                }
            }

            if (empty(array_keys($warningMap))) {
                self::info(__FUNCTION__ . "没有需要告警的数据");
                return;
            }
            $warningSceneTypesStr = implode(",", array_keys($warningMap));

            $recordSql = <<<RECORDSQL

select record_id, scene_type from (
SELECT TO_CHAR(create_time::timestamp with time zone::date, 'YYYY-MM-DD') AS stat_date,
       record_id,
       scene_type                                  ,
       status,
       error_code,
       CASE
           WHEN status = 1 AND error_code = 0 THEN '成功'
           WHEN status = 1 AND error_code != 0 THEN '成功-ERROR'
           WHEN status = 2 AND error_code = 40004 THEN '失败-风控异常'
           WHEN status = 2 AND error_code = 40001 THEN '失败-GPT异常'
           WHEN status = 2 AND error_code IN (40011, 40012, 40013, 40014, 40015, 40016) THEN '失败-解析SQL异常'
           WHEN status = 2 AND error_code = 40041 AND create_time::timestamp with time zone::date >= '2023-12-20'
               THEN '失败-超时'
           WHEN status = 2 AND error_code = 40041 AND create_time::timestamp with time zone::date < '2023-12-20'
               THEN '成功'
           WHEN status = 2 THEN '失败-其他异常'
           WHEN status = 3 THEN '失败-风控异常' -- 不应该归类为技术异常
           WHEN status = 0 THEN '失败-初始化' -- 需要具体排插
           ELSE '未知'
           END                                     AS record_status_text,
       create_time
FROM tbl_ai_service_process_record
WHERE {$where}

  AND scene_type IN ({$warningSceneTypesStr}) -- 排除了 13，如果需要包含 13，请将其添加回列表中
  AND NOT ( status = 1 AND error_code = 0 AND request_data = '{}') ) as tbl_record_origin
                                     where record_status_text not in ('成功', '成功-ERROR','失败-风控异常');

RECORDSQL;
            $recordList = $dataWorkDb->createCommand($recordSql)->queryAll();

            foreach ($recordList as $item)
            {
                $itemSceneType = $item['scene_type'];
                $recordId = $item['record_id'];
                if (!isset($warningMap[$itemSceneType]['recordIds'])) {
                    $warningMap[$itemSceneType]['recordIds'] = [];
                }
                $warningMap[$itemSceneType]['recordIds'][] = $recordId;
            }

            $totalInfo['totalCount'] = $currentTotal;
            $totalInfo['totalFailCount'] = $currentTotalFailCount;
            // 告警
            self::sendDingTalkWarningMessage($warningMap, $totalInfo);
        } catch (\Throwable $exception) {
            self::info($exception->getMessage());
            return;
        }
    }

    private static function sendDingTalkWarningMessage($messageInfo, $totalInfo = [])
    {
        $redis = \RedisService::sf();
        $endDateFormat = date("Y-m-d", strtotime("-1 day"));
        $totalCount = $totalInfo['totalCount'] ?? 0;
        $totalFailCount = $totalInfo['totalFailCount'] ?? 0;
        $totalFailRate = $totalFailCount == 0 ? 0 : round(($totalFailCount / $totalCount) * 100, 2);

        $title = "# 技术异常告警 \n
## {$endDateFormat} \n
### 请求总数: {$totalCount} \n
### 失败总数: {$totalFailCount} \n
### 总失败率: {$totalFailRate}% \n
---\n";
        $markdownStr = $title;
//        $table = "| 场景 | 昨日失败率 | 当日失败率 | 增长率 | recordIds |
//| -------- | ---------- | ---------- | ------ | ---- | \n";

//        $markdownStr .= $table;

        $dingTalk = new DingTalkRobot(null, 'SEC96bd1e2bad12b9506dbd37b0e42014150ea315c379577c824ba71ef7eee4553b');
        $dingTalk->setWebhook('https://oapi.dingtalk.com/robot/send?access_token=e8d9d2574d6bc0e82b9cf37ac654f4e775a50edb5b1d87b45a3aadb62a7df19d');

        foreach ($messageInfo as $sceneType => $info) {

            $agentModel = AiAgent::findBySceneType($sceneType);
            $agentName = $agentModel->agent_name;
            $recordIds = $info['recordIds'] ?? [];
            $failRate = round($info['failRate'], 2);
            $growthRate = round($info['growthRate'], 2);
            $recordIdStr = implode(",", $recordIds);
            $preFailRate = round($info['preFailRate'], 2);
            $currentFailCount = $info['currentFailCount'] ?? 0;
            $previousFailCount = $info['previousFailCount'] ?? 0;

            $previousTotalCount = $info['previousTotalCount'] ?? 0;
            $currentTotalCount = $info['currentTotalCount'] ?? 0;

            $cacheKey = md5(\common\library\ai_service\AiServiceConstant::BAD_CASE_RECORD_LIST_CACHE_PRE ."{$sceneType}_{$endDateFormat}");

            $redis->setex($cacheKey, 1209600 , $recordIdStr);

            $baseUrl = \Yii::app()->params['env'] == 'test' ? "https://rayboy.php.dev.xiaoman.cn" : "https://prometheus.xiaoman.cn/";

            $url = "{$baseUrl}/prometheus?#/prometheus/aiService/processBadCase?record_key={$cacheKey}";



//            $markdownStr .= "| $agentName | {$failRate} % | {$growthRate} % | {$preFailRate} %  | {$recordIdStr} |
//";

            $markdownStr .= <<<HTML

### {$agentName}
失败率: 昨日:{$preFailRate}%  今日:{$failRate}% \n
失败数: 昨日:{$previousFailCount} / {$previousTotalCount} 今日: {$currentFailCount} / {$currentTotalCount} \n
recordIds统计: \n 
```
{$recordIdStr}
```
\n 
<a href="$url" target="_blank" style="font-size: 10px;">查看详情</a> \n
---\n

HTML;

        }


        $dingTalk->markdown($title, $markdownStr);

    }


    public function actionBuildTaskByClientId()
    {
        $json = <<<JSON
{"181":["3416267","3997752","9500774","16241534","17457635","47739583","47739584","47739585","55495576","56039814"],"628":["6028884","6028887","6028895","11878081","11878134","32448596","37187134","55282899","56036081"],"1064":["881271","19570438","55144367","55941437","56266862","56292247","56318232","56327661"],"1157":["71656","109132","4821423","8472739","8472740","8472741","8472742","8472743","8472744","8472745","39560219","55226029","55234313","55258680","55300805","55919257","55947967","56176460","56178691"],"1838":["1385350","11982762","11982787","12104865","44341614","55207534","55297305","55366173","55750860","55753961","55798252","56159869","56164976","56167794","56331998","56511500"],"2327":["206126","1971583","7249346","12720090","15549695","15549697","15549698","15549699","15549918","15549920","15549921","15549922","27167054","50073630","51352844","55288579","55288586","55288588","55374858","55949721","56001868","56039316","56164155","56169686","56174187","56191674"],"2501":["13253923","16424750","16425921","47517447","47523251","47523252","49251672","56324232"],"3764":["3822046","4125844","8951998","14713690","17049236","22398255","24900806","24944483","44791805","55761354"],"4204":["22966710","27947452","31186076","55254676","55255462","55269850","55269851","55529712","56160141","56167957"],"4317":["28392274","28847449","28847455","28847459","28853329","56183258","56183259","56183260","56183262","56183460","56183462","56514466"],"4333":["28763236","28786385","28786387","55270424","55295103"],"4611":["50017127","50017129","52409028","55222587","55271650","55274477","55379375"],"4621":["3226966","31473907","31473908","31473909","31473911","31473912","31473913","31473914","55170559","55221612","55223361","55223362","55264171","55269782","55290998","55307526","55307527","55307528","56038725","56324822","56505334"],"4648":["14170829","14383530","18602462","23773092","27003970","31633901","31714605","42079642","55249175","55276361","55276427","55276429","55937996","55938067","56033328","56158034","56158035","56288163","56516499"],"4699":["5341926","31917572","35541819","55491734"],"4862":["27665898","33102443","55253831"],"5041":["28435238","34765311"],"5117":["32210351","35773456","35788085","35788086","35788087","55254503","55270391","55270574","55270577","55298350","55298351"],"7126":["27222929","30846824","30875598","33903320","44191363","44191364","55285542"],"7708":["40302714","55793073","56165662","56165878","56323785","56325381","56325528","56326409","56505752"],"7797":["45592754","55145242","55254716","55274504","55353532","55544263","56501130"],"9901":["52249194","53438059","53438060","53439676","53442939","55166315","55218389","55280192","55304859","55766577"],"10628":["6438034","54912204","55145152","55247957","55247958","55247964","56169459","56185701"],"12665":["55176833","55220191","55220338","55220340","55256236","55289404","55289406","55290426","55290427","56166098","56166099","56166100","56166101","56167269","56167272", "56167275","56167276","56167320","56188374","56498569","56499362","56499363"],"13878":["55220814","55221090","55221091","55505428","56488576"],"15460":["55240136","55240137","55249292","55294828","55297024","55319162","55541431"],"16859":["55251841","55252951","55256208","56163362","56172407"],"16993":["55252411","55265791","55265817","55268933","55346437","55568401","56187220","56504856"],"17111":["55252933","55256337","55262047"],"17372":["55254297","55257943","56329821"],"17586":["55255369","55256889","55256890","55256892","55256909","55257574"],"19416":["55296008","55311899","55546068","55546069","55546070","55787830","55943702","55944889"],"19469":["55263612","55274039","55274040","55293066","56170366","56184623","56326708","56326709"],"19902":["55267149","55267150","55267153","55267157","55267178","55267195","55500127","55542313","55769727","55769781"],"20262":["55267310","55303381","55303382"],"21327":["55271888","55272316","55272318","55272322","55272324","55274821","55300266","55315390","55315392","55315393","55315394","55341858","55378177","56185654","56185655","56185907","56185908","56186697"],"22482":["55309133","55309811","56504836","56504857"],"22599":["55277317","55277550","55277551","55277552","55277554","55277558","55277559","55277560","55277561","55292156","55297212","55298604","55364366","55364367","55364368","55364369","56188545","56192495","56311887","56313155","56510614","56510615"],"23006":["55279364","55279654","55279655","55279656","55279657","55279661","55279662","55279664","55296329","55305448","55317410","55320472","55368166","55507754","55545221","55545223","56168590","56302080","56335132","56335270","56509609","56509921"],"23841":["55283372","55364971","55364972","55364973","55364974","55366619","55366627","55366628","55366631","56321071"],"25240":["55288693","55289943","55289944","55289946","55289948","56297155","56297156","56297157","56297341","56301013"],"27037":["55299055","55299277","55299292","55299296","55323376","55425970","55540687","55540688","55540689","55540690","55909228"],"27359":["55300846","55302796","56181999","56182001","56186510","56274465","56291371","56295014","56497659","56501559"],"28339":["55425299","55425430","55425431","55425433","55425435","56509790","56514223"],"29094":["56330677","56330746","56330747","56330748","56508826"],"29312":["55314539","55315320","55315321","55315322","55315323","55510163","55543942","55543943","55745967","55920079","55920081","56156575","56156576","56175896","56175913","56514926"],"29518":["56507399","56507440","56507441"],"29883":["55309393","55309424","55309425","55347301","56036486","56036493","56164397","56171596","56192896","56500812"],"31235":["55330813","55331697","55331701","55331702","55785447","56488254"],"31386":["55317141","55318313","55318314","55318315","55431281","55761234","55788290","55788291","55788294","55791148"],"31668":["55317417","55317422","55318363","55374902","55761951","56329720","56505581","56510156"],"32315":["55325648","55325742","55325743","55325744","55770011","55940573","55940574","55940575","55940576","55940577","56159040","56192584","56266836"],"32564":["55320208","55320318","55320322","55320324","55320325","55320328","55320329","55320332","55320333","55320387","55320393","55369356","55423764","55425000","55506431","55539258","55548273","55567870","55936588"],"33209":["55325096","55325097","55325099","55325101","55325103","55325143","55325144","55325145","55325146","55325155","55325235","55409960","55768499","55936513","55936531","56080621","56266337","56266340","56266343","56266347","56266349","56289984","56289986","56292710","56300382","56300383","56300392"],"33338":["55323637","55324365","55324367","55324368","55324369","55324381","55324382","55324383","55324384","55324385"],"33554":["55325047","55325484","55325486","55325487","55326172","55326225","55418227","55430196","55769607","56331106"],"34173":["55325192","55545369","55788606","55950601","56172273","56284982","56325799"],"34386":["55328806","55329315","55330102","55531284","56162516","56162517","56162519","56162536","56263190"],"34627":["55346424","55756577","55920512","56328429","56328435","56328437","56328442","56329152"],"34646":["55337629","55949114","56312740","56312785","56314455"],"34929":["55327731","55331294","55331456","55331457","55331458","55336864","55380261","55756605","55756615","55756621","55756772","55756778"],"35311":["55328622","55329225","55329226","55341952","55364387","55539363","55762682","55811844"],"35721":["55372596","55374710","55374713","55374714","55374911"],"35824":["55331452","55331658","55331659","55331660","55331661","55331662","55540925","55547412","55547636","55549458","55549462","55648970","55649471","55649972","55754854","55907289","55938238","56079893","56157018","56195917"],"36105":["55761981","55762008","55762009","55762010","55762011","55762012","55762013","55763416","55792311"],"36204":["55335215","55369095","55503367","56162954","56163483","56292428","56298631"],"36241":["56488493","56488662","56488663","56488664","56488665","56488666","56488667"],"36262":["55333886","55334465","55526041","55526044","56159935","56174230","56331066","56331071"],"36401":["55431729"],"36412":["55345917","55345923","55345925","55345927","55500295","55513123","55524345","56172621","56192374"],"36459":["56170620","56170644","56170645","56170646","56170647","56170648","56170649","56170650","56170651","56174349"],"36728":["55333186","55333381","55337341","55337344","55337345","56496948","56496949","56496950","56496951","56496952"],"36836":["55413638","55528935","55757674","55757675","55797132","56080267","56159130","56159135","56159139","56159141","56507120","56507121","56507175","56507826"],"37515":["55334340","55337748","55337749","55337750","55337751","55337752","55337753","55337754","55539619","55547628","55547629","55791036","56312068","56332660","56335663","56516396"],"37651":["55345295","55345328","56156096"],"37688":["55341938","55341961","55341962","55341963","56042177","56042178","56042463","56169790","56318460","56318462","56507614","56514919"],"38746":["55341933","55342214","55342223","56037134","56044814","56044815","56161494","56318721","56497421","56497426"],"40320":["56325879","56325888","56325889","56326075"],"40787":["55375225","55415913","55427991","55437179","56513472","56513473","56513474","56513475","56513476"],"40829":["55362526","55363474","55363475","55363476","55363477","55363478","55442758","55442759","55547768","56157336","56511440"],"41308":["55364893","55364908","55496846","55510204","55795565","56515440"],"41446":["55365500","55366324","55366325","55499625","55499626","55502969","55539100","55949853","56190305","56335458"],"41755":["56321568","56321604","56321606","56321718","56321719","56321720","56321721","56321722","56321723","56321724","56321725","56321726","56321727","56321728","56321729","56321730","56321731","56321732","56321733","56321734"],"42085":["55368014","55368026","55368628","55408948","56193527"],"42867":["55380872","56325991","56507147","56507186","56514551"],"43116":["55370769","55370972","55370975","55523845","56187308","56300095","56301590","56497128"],"43602":["55372690","56323487","56323488"],"43670":["55380622","55380623","55380694","55408090","55408101","55408102","55408110","55408128","55408129","55412236"],"44213":["55379515","55419405","55420712","55420801","55526837","56032166","56032171","56170582","56170606","56184073","56496264","56496272"],"44598":["55376055","55407208","55407209","55425911","55770004","56331706","56331711","56331713","56335258","56505557"],"44841":["55381901","55426526","55492736","55902588","55902589","56178580","56178581","56189347","56287102","56503571","56509579"],"45018":["55430694","55491991","55491993","55491994","56169759","56231236","56247653","56288641"],"45304":["55377560","55380805","55946702","56037621","56040532","56488808","56488809","56497458","56514780"],"45858":["55405183","56328402","56328432","56329219","56329220","56329221","56329223"],"46097":["55404294","55407665","55407666","55407667","55407668","55426225","55528349","55529139","55762587","55762588","55763805"],"47266":["55383148","55383154","55383155","56038617","56153137","56153340","56502638","56505205","56516427"],"47280":["55383150","55411748","55411749","55411750","55418237","55426438","55426439","55431348","55527382","56113641"],"47300":["55383089","55404562","55404563","55404565","55939515","56154795","56157505"],"47722":["55407154","55407156","56032393","56160063","56258536"],"49206":["55408347","55408363","56293650","56319368"],"49667":["55411902","55411942","55411943","55511277","55531540","55797866","55950381","56080115","56151254","56161103","56257817","56294713","56500202"],"49686":["55410644","55413395","55413396","55413398","55413399","55413586","55413683","55423639","55425275","55426143","55945675","55951157","56037343","56178013","56182010","56186198","56186199","56186200","56186201","56187334","56187616","56187851","56194437","56507710","56512332"],"51969":["55414203","55414305","55414306","55414307","55414308","56161024","56322286","56328911","56328912"],"52015":["55438435","55442691","55442692","55442693","55442694","55442695","55442696","55442697","55442698","55442699","55442700","55442709","55487759","55487788","55505936","55534257","55768788"],"52224":["55428401","55428402","55428403","55428404","55436103","55495237","55526107","55751358","55752527","55756066","55903240","56033318","56161241","56176159"],"52257":["55420329","56033061","56033062","56033063","56033064","56033111","56158930","56164453","56335062","56500379","56503413"],"54237":["55423098","55423099","55423100","55423101","55423102","55423103","55423104","55792874","56194291","56335946"],"54369":["55417577","55422575","55788790","55948517","55950911","56184932","56184933"],"55581":["55432350","55436836","55441515","55763625","56079516","56186546","56335615","56335727","56508724"],"58575":["55442345","55442524","55442526","55442527","55442528","55442529","55442530","55442533","55442534","55442535","55523878"],"58690":["55441458","55441470","55441471","55441472","55441473","55441474","55441475","56191604"],"58912":["55442424","55442426","55442431","55442439","55442501","56188935","56188936","56188943","56189399","56191194"],"58923":["55487464","55487473","55487474","55487475","56321406","56321408","56321409"],"59047":["55489624","55489630","55504440","55526643","55752335","55801153","55904043","55904748"],"60288":["55937254","55937297","56172781","56298391","56321748","56329025"],"61279":["55492214","55492251","55492252","55492253","55492254","55492255"],"61637":["55492719","55492960","55492961","55492962","55492963","55492964","55492965","55492966","55494032","55510890","55511118","55754518","55788569","56176097","56178520","56263528","56293407","56302782"],"61883":["55539504","55541083","55752321","55907979","55920094","56034192","56038714","56258315","56333807"],"62405":["55505191","55512808"],"63105":["55497832","55497851","55497975","55547204","55549167"],"63208":["55497662","55497663","55525734","55746261","56285666","56321022","56496066","56514645"],"63938":["55497967","55500339","55500610","56033273","56300711","56312554"],"67468":["55513047","55544863","56319204","56319205","56508068","56508100"],"67635":["55746189","55747143","55747144","56512630"],"67950":["55523715","55524695","55524696","55524698","55525361","55755399","55759097","55762772","55762773","55762820","56498307"],"68355":["55531972","55531987","55532227","55533776","55545725"],"68443":["56328643","56328986","56329890","56329891","56329892","56329893","56329894","56332767","56333474"],"68493":["55527175","55527186","55796353","56038748","56038749","56044292","56158944","56158945","56289479"],"70026":["55529547","55529632","55529634","55529635","55529636","55529637","55529638","55529639","55951122","56034476","56034477","56513337"],"70337":["55530540","56321510"],"70538":["55531868","55792425","55903719","55950539","56151225"],"70645":["55794826","55794861","55794862","56156202","56157940","56170500","56173880","56263995","56266302","56322164","56504016"],"70760":["55548518","55548888","56510304"],"70858":["55531571","55755061","55755062","55755063","55755064","55755074","55769174","55769197","55769204","55796362","56333258"],"71274":["55543714","55543794","55543795","55543796","55543797","55543899","55757842","55946456","55946461"],"71488":["55539259","55539277","55539278","55539279","55787191","55800191","55800192","55940326","55941454","56159870","56160584","56161666","56170610","56173221","56190505","56295614","56299263","56506492"],"73035":["55540594","55548964","55548968","55548970","55548971","55548972","55549232","56156061","56313444","56504605"],"73387":["55545603","55548272","55568642"],"73789":["55544261","55544467","55544487","55544489","56046113","56159837","56159838"],"73910":["55762982","56038815","56038816","56038818","56330571","56330577"],"73947":["55545149","55545189","55545191","55546217","55546218","55546220","55747264","55751469","55752451","56157793","56165430","56166411","56514417"],"74373":["55567115","55753906","55754825"],"74853":["55798199","56034148","56170576","56268693","56294425"],"75155":["55754824","55755103","56500229"],"75822":["55760958","55788295","55903864","56188066","56326017"],"76042":["55761101","55761114","55761115","55761116","55761117","55761845","55761847","56191467","56504851"],"76263":["55759878","55760357","55760358","55760359","56294202","56508640","56508937","56509417"],"76595":["55762592","55766986","55937844","55937846","56166544","56191428","56191429","56507575","56507576","56508932","56508943","56508944","56508945","56508964","56508965","56509263","56509264","56509265","56509266","56509367","56511274"],"77275":["55766426","55766431","55766433","55766434","55766435","55766440","56044889"],"77690":["55785823","55785840","55785841","56080683","56169083","56172114"],"78347":["55789465","55945850","56048010","56078723","56168582","56333282","56333283","56488245","56507657"],"78395":["55793173","55795258","55795259","55795260","55795355","55920410","55947310","56257363","56300502","56300503","56330803","56501566","56511086"],"78821":["55902930","55902993","55902994","55902995","55902996","55902997","55902998","55903002","55903003"],"78988":["56510187","56510517"],"79135":["55907573","55907588","55907589","56039936","56079514","56286334","56292618","56312413","56508182"],"79639":["55799237","55902768","56078890"],"79759":["55797684","55801180","56036284","56330562"],"79986":["55937442","56039912","56043705","56335388","56335461","56501438","56501439","56501442","56501443","56502963"],"81345":["55940253","55940254","55946689","56079540","56160661","56162652","56274301","56274554","56509018","56509020"],"81375":["55946504","55947008","55947009","55949920","55950763","56153009","56184261"],"83624":["55943787","55943971","55943972","56032902","56079283","56168014","56169082","56295339","56295347","56311936","56503170","56503213"],"83860":["56037569","56040038","56040041","56040042","56166458","56498928","56501890","56505647","56510116"],"84846":["55950355","55950356","55950357","55950358","55951346"],"84909":["56496130","56496397","56496398"],"85677":["56032185","56032212","56032213","56032214","56032215","56032344","56035461","56152792","56161468","56291701"],"85968":["56178667","56178669"],"86419":["56080411","56146323","56146324","56155149","56182808","56183430","56314979","56323299","56510910"],"87280":["56039154","56039163","56039173","56039186","56039199","56039201","56039205","56311426"],"88570":["56046741","56047274","56047275","56047276","56078868","56080507","56166959","56285660"],"89013":["56323509","56323516","56323525","56331168"],"89054":["56286258","56286268","56286269","56314379","56508551"],"89071":["56039960","56040642","56040710","56041016","56155327","56288252"],"89091":["56039922","56040812","56040819","56040821","56040822","56040829","56042118","56488409"],"334315":["56326351","56326364","56326365","56326366","56326367","56326368","56326369","56326370","56326371","56326372","56326373","56326374","56326375","56326376","56326377","56326378","56326379","56326380","56326381","56326382","56326383","56326384","56326385","56326386","56326389","56326390","56326392","56326393","56326394","56328083"],"334323":["56325359"],"336056":["56158949","56159015","56159031","56169142","56498708","56498709","56498714","56508808"],"336216":["56155265","56155302","56155317","56155318","56155319","56155320","56155321","56155360","56155391","56171084","56312106"],"336340":["56156920","56160108","56160110","56160111","56163250"],"337087":["56194341","56194910","56266230","56266231","56330582"],"337091":["56159127","56186360","56187385","56187387","56187747","56192889","56511189","56511954"],"337391":["56160694","56161015","56161038","56291110","56324220","56324326","56324564","56326023","56326024"],"337984":["56186267","56297735","56501428"],"338030":["56162862","56325929","56326127"],"339018":["56298480","56314857","56314916","56314919","56314930","56318196","56488954","56499206"],"339106":["56175280","56187212","56192978","56195770","56295324","56489385","56496187"],"339803":["56174060","56174109","56174110","56382650","56382792","56497518","56497519","56497520"],"339947":["56175488","56176316","56176317","56176626","56176627","56185359","56189561","56231525","56288856","56294314","56294315","56488931","56488933","56497547","56509404"],"340166":["56178261","56181480","56181481","56504021","56508157","56508161","56509135"],"340245":["56514011","56514013"],"340299":["56178216","56178421","56178422","56178423","56178425","56178427","56178432","56178445","56297909","56500859","56500861","56509171"],"340491":["56193869","56193871","56193872","56193873","56193875","56194030","56194135","56195405","56263013","56327399"],"340777":["56191347","56191361","56191362","56194845","56231416"],"341274":["56184994","56185140","56247208"],"341283":["56185106","56186868","56186869","56186870","56186871","56186872","56194001","56194002","56194003","56194004","56194321","56195995","56290643","56330706","56330707","56330708","56515811"],"342034":["56299603","56300039","56300040","56300041","56300042","56300043","56300044","56300045","56300046","56300047","56300048","56300049","56300052","56300672","56329340"],"342144":["56296292","56296585","56321695","56333414","56502974","56503065","56503157"],"343334":["56266166","56266475","56266476","56266477","56266478","56266481","56266482","56266483","56266484"],"344491":["56318885","56319222","56319225","56319226","56335164"],"344495":["56263889","56274385","56274386","56289886","56289960","56301481","56329330"],"345535":["56301430"],"346999":["56325444"],"347470":["56293093","56293616","56293617","56293618"],"349183":["56325333","56325357","56328800","56329341","56500846"],"349296":["56497240","56498004","56498005","56498006"],"349934":["56322059","56322091","56322092","56322093","56322096","56322097","56322108","56322109","56327949","56327951","56501897","56510299","56514724"],"350424":["56313335"],"350726":["56319606"],"350788":["56322920","56323139","56333459"],"350916":["56325375","56330283","56330285","56333650"],"350985":["56312923"],"350992":["56313527","56313541","56313542","56313543"],"350994":["56329052"],"351011":["56333036"],"351047":["56319123","56319128","56319129","56319130","56319131"],"351199":["56314874","56314881","56314882"],"351275":["56318168","56318175","56318177","56318182","56318218","56320122","56320123"],"351310":["56326101","56510426","56510708","56510709","56510710"],"351390":["56318096","56318104","56318106","56318107","56333531","56335565","56500254","56502170"],"351447":["56323735","56333511","56334982","56496149","56499388","56499389"],"351449":["56322905","56322997","56322998","56322999","56323000","56323001","56323002","56323003","56323004","56323005","56323006","56323007","56323008","56323009","56323010","56323011","56323012","56323013","56323014","56323015","56328043","56328244","56330915","56333234","56333237","56333331","56333348","56333349","56382658","56382733","56500145","56513883"],"351501":["56319021","56319416","56319417","56334712"],"351641":["56329570","56330631","56330946","56333548","56497674"],"351649":["56332191","56382761","56498052"],"351658":["56382954","56382958"],"351663":["56318217","56328169","56511123","56511124","56512468"],"351681":["56510830","56511608"],"351703":["56502997","56503011","56503017","56503018","56503022"],"351729":["56318095","56318120"],"351737":["56335074","56335286","56335287","56335288","56335289","56335292"],"351871":["56330122","56334969","56334970","56334971"],"351916":["56499008","56503020","56506702"],"351949":["56319071","56319079","56319080","56325308","56488483","56499720"],"352033":["56319463"],"352046":["56332898","56332909","56332910","56335868","56500602"],"352057":["56318374"],"352068":["56321827","56329840","56329841","56329842","56329843","56329859"],"352101":["56327941","56327966","56327981","56327982","56327983"],"352103":["56319587","56319592","56319754","56319789","56319797","56321776","56334474","56507840"],"352138":["56326225","56326250","56326254","56326255","56326269","56326270","56326275","56332088","56332089","56332090","56332095","56503071","56503072","56503073","56506811","56510583"],"352169":["56382727"],"352180":["56326606","56326621","56329714","56334433","56497981","56508605","56508607"],"352181":["56326036","56326098","56326467","56326468","56326469","56506393","56506394"],"352193":["56333512","56333523"],"352284":["56325641","56325646","56325647","56325648","56329353","56329483","56508482"],"352313":["56319501","56321156","56321157","56321158","56321165"],"352392":["56329815","56329839","56497858","56497859"],"352441":["56330288","56330335","56330347","56330348","56330361","56330362","56330366","56498320","56498321","56498322"],"352527":["56322742","56322774","56322775","56322776","56322777","56322778","56322779","56322780","56322781","56322782","56322783","56322784","56323914","56324159","56334152","56334153","56334154","56334155","56335528"],"352587":["56332963","56513367","56513370"],"352640":["56323750"],"352722":["56513127","56514443"],"352757":["56322206","56496195","56507956","56508820"],"352785":["56322067","56510232","56510234"],"352797":["56323132","56326864","56326865","56328394","56328395","56328397","56328399","56328411","56499917","56510196","56512543"],"352799":["56332703","56333769"],"352814":["56324385"],"352936":["56332901","56335011","56335012","56335013","56335014","56335015","56335016","56382925"],"352981":["56329345","56329347","56488416","56510385"],"353029":["56326435","56326446","56326447","56326448","56326449","56326450","56326451","56326452","56513174"],"353056":["56324910","56382947","56382948","56382956","56383005","56403653","56488178","56488179","56488180"],"353075":["56322839","56322846","56322848","56323512","56323651"],"353167":["56331805","56331812","56331814","56331815","56331817","56331818","56331819","56331820","56513786","56513791"],"353211":["56330436","56501746","56515161"],"353279":["56323413","56323416","56323417","56382712"],"353294":["56328243","56328516","56328517","56328518","56328519","56328529","56331469","56331470","56331471"],"353388":["56331399","56331411","56331412","56331413","56331414","56331415","56331416","56331417","56331418","56331419"],"353446":["56506300","56506358","56507624","56507628","56507634"],"353474":["56329634","56329639","56329657","56331324","56331325","56331327","56331328","56331329","56331462","56497944"],"353487":["56324270","56324595","56324596","56324653","56328070","56498372"],"353501":["56329525","56329534","56329539"],"353505":["56324300","56324523","56324524","56324952","56324953","56324954","56324955","56324956","56324957","56324961","56516454"],"353521":["56324825","56325975","56325977","56325978","56325981","56326104","56332324","56498986"],"353563":["56326391","56326727","56382842"],"353648":["56324486","56324700","56324701","56324702","56324705","56506040","56509415","56514893"],"353782":["56330227"],"353825":["56329684","56329694","56329695","56329696","56497848","56497850","56499588"],"353842":["56333651"],"353882":["56328823","56488399","56504959","56504960","56504961","56504962"],"353887":["56330583","56330608","56330609","56330610","56330611","56330612","56330613"],"353946":["56325475","56325485","56334011","56511583","56512455","56514366","56514691","56515238","56515239"],"354031":["56327031","56329164","56329167","56329168"],"354091":["56512792","56515191","56515192","56515193","56515194","56515195"],"354104":["56325870","56326402"],"354112":["56331295"],"354139":["56489387","56489397","56489398","56489400","56489403","56498629","56505186","56515262"],"354184":["56328933","56328934","56328935","56328936","56328937","56328938","56328939","56328940","56328942","56333484","56504334"],"354299":["56328984","56329673","56329675"],"354318":["56330404","56510726"],"354396":["56335585","56504085","56504086","56504099"],"354403":["56326778","56332155","56332160","56334904","56513026","56514753"],"354575":["56331855","56511287","56511288","56511289","56511290","56511291","56514822"],"354605":["56327581","56333144","56333151"],"354669":["56328631","56328652","56328923","56328924","56328928","56497038","56497039","56499984","56501258"],"354725":["56328868","56332450","56332964","56488388","56510849","56514556","56514706"],"354735":["56333757","56334351","56334848"],"354785":["56330655","56330667","56330668","56330669","56330670","56330671","56330776","56330892","56330895","56331154","56335894"],"354797":["56329129","56329266","56329267","56329268","56329971","56329972","56329974","56329975","56497384"],"354844":["56328973","56329080","56333019","56333020"],"354851":["56330406","56330413","56507057","56515270","56515271","56515272"],"354935":["56489083"],"354951":["56334048","56335053","56335054","56335055","56335606","56335607","56499310"],"354972":["56327454","56327456","56327457","56327458","56327459","56327460","56327462","56510190","56510192"],"355064":["56329530","56329540","56333399","56513067"],"355129":["56330951"],"355153":["56498753","56498939"],"355326":["56332503","56496191","56503264"],"355440":["56331802","56331891","56331892","56331893","56331894","56331895","56331896","56331897","56331898","56331899","56332109"],"355507":["56489120"],"355551":["56332717"],"355598":["56332047","56335207"],"355620":["56333333","56333368","56333369","56333370","56333371","56333372","56333373","56333386","56333421","56333422","56333423","56333424","56333425","56333448","56333449","56333450","56333451","56333452","56333659","56333660","56333661","56506779","56506780","56510284","56514829"],"355669":["56500429"],"355774":["56514142"],"355825":["56510206","56510215","56510245"],"355871":["56512746","56512785","56512786"],"355909":["56335072","56335078","56335079","56335081","56335082","56335083","56507213"],"356008":["56334896","56335335"],"356047":["56335206","56335291","56516031"],"356057":["56507292","56507328"],"356197":["56488195","56488260","56488261","56488264"],"356328":["56488456","56488466","56488478","56488482","56500023","56504137","56508900","56514069"],"356477":["56497086","56497713","56497714","56497715","56497716","56497718","56497719","56497721","56498373","56515012"],"356574":["56512816","56512828","56513578","56513579","56513580"],"356606":["56497119","56497139","56498099","56498102","56502259","56507315"],"356952":["56500991","56501009","56501794","56501795","56501796"],"356955":["56506681","56506693"],"356991":["56500584","56501986","56503281","56503815","56510220","56510222"],"357045":["56501151","56501220","56503751","56506426"],"357184":["56509745","56509750","56509751","56509752","56509753","56510110","56511809"],"357221":["56503993","56514847"],"357247":["56511492"],"357341":["56502332","56502338"],"357513":["56505129"],"357522":["56503077","56503087","56503088","56503089","56513930"],"357614":["56506163","56506402","56506403","56506404","56506405"],"357627":["56504618","56506212","56506213","56506214","56506217","56506227"],"357708":["56506419","56507714","56507715","56507716","56510804","56512849","56512850","56512851"],"358062":["56507325","56508239","56508240","56508241","56508242","56508243","56508244","56508245","56508246"],"358078":["56507143","56507221","56507223","56507224","56513212","56513233"],"358114":["56506048","56507492","56507493","56507494","56514964"],"358143":["56509485","56509491","56509493","56509494","56509524"],"358270":["56515300","56515957","56515958"],"358314":["56512806","56512829","56512830","56512831","56512832","56512833","56512834","56512835","56512836","56512837","56512838","56512839","56512840","56512841","56513048"],"358458":["56514490","56514507"],"358466":["56508075","56508140","56508141","56508142","56508143","56508144","56508145","56508146","56508149","56508151","56509287"],"358495":["56511198","56511206","56511419","56511456","56511466","56511468","56511484","56513971"],"358499":["56513425","56513431","56513435"],"358669":["56511021","56512010","56512011","56512012","56512013","56512014","56512015","56512016","56512017","56512019","56512024","56512035"],"358821":["56509767","56510935","56510936","56510937"],"358984":["56511721","56511742","56511743","56511745","56511927","56512614"],"359111":["56513796","56514364","56514496","56514497"],"359142":["56511688","56515227","56515231","56515232","56515233"],"359327":["56515179","56515922","56515924"],"359388":["56514692","56514713","56514714","56514715","56514716"],"359524":["56515087","56515113","56515114","56515116","56515117","56515324"],"359568":["56514267","56514281","56514282","56514283","56514284","56514285","56514286","56514287","56514288","56514289","56514290","56514291"],"323":["3902394","4195872","4360378","4673457","4693582","4693584","4693593","4693615","4693680","4693686","4693687","4693688","4695253","55946373","55946376"],"549":["476184","49608351","51835817"],"674":["6301195","6730249","6732525","6732554","15489152","26206160","26206161","55221004","55535144","56291383"],"954":["477751","477873","479827","7653268","22390931","55300466","55311449","55314027","55438204","55758822","56514858","56514859","56514860"],"1130":["1321118","8248548","8248738","8248893","8248951","8248991","8249079","8249251","9431469","25572203","56501572"],"1141":["3977223","8420979","31481667","42572828","49745650","55247344","55270374","55420095","55941345","56293709","56329329","56329331","56511740"],"1182":["5008236","19423116","19423348","23185485","55254374","55292070","55301858","56505079","56506249"],"1684":["830373","55292889","55292891","55292893","56044337","56163863","56163864","56166310","56167297","56172510","56331870"],"2953":["1967475","25581860","26340223","55220975","55281596","55374024"],"3058":["9670617","12477745","19861259","20016190","20016192","20016194","20016195","20016704","36895320","37055082","55270108","55942944","55943461"],"3402":["7651431","22251843","55254783","55940450","55940451","55940452","56164711","56165630","56165631","56274087","56503615"],"3946":["20293079","26051113","26114115","26114124","26114132","26118866","26119057","55427855"],"4216":["19118255","27824044","55283420","55283421","55283422","55301474","55375331","55431469","55765441","56300119","56300120","56300121","56507436"],"4300":["19408469","28602209","28602210","28752885","30370709","30370710","36332174","55494177","55754671","55787891","55799088","55799089","55799090","55799091","55799796","55939482","55944415","56164027"],"4743":["32094721","32119430","32119431","32119432","32119437","32119438","51631479","54752546","55254661","55257816","55270330","55282222","55288377","55299346","55346657","55383251","55493141","55503379","56036141","56036171","56164123","56187196","56195632"],"5300":["20818145","55210929","55758186","56033525","56182324"],"5731":["39063715","50084406","55408755","55408774","55427137","55753992","55939641","56162189","56508050"],"6848":["7056862","43742990","53277137","55273200","55764309","55800325","55800332","55842004","55949462","56161475","56285820"],"8914":["49168984","55264281","55265682","55266334","55275251","55286795","55295213","55295252","55295525","55295526","55295593","55295641","55295643","55295758","55299262","55801749","55801752","55801760","56159661","56174317","56189179","56191515","56266632","56266633","56289928","56508315","56513343"],"9732":["3805445","8776035","42070909","52965473","52965474","52965475","55284598","55751669"],"11727":["55164328","55172176","55172177","55172178","55172179","55172180","55172181","55172183","55172184","55172185","55258854","55423994","55789791","55789801","56151745","56151746","56258498","56258499","56290976","56319497"],"12872":["55185069","55199096","55199097","55769191","56334567","56334592","56335143","56505216"],"14474":["55223471","55315496","55315497","55315498","55318954","55323745","55490443","56032756","56035391"],"14716":["55226833","55235106","55235108","55235109","55235110","55235113","55273092","55343450","55406743","56382965","56382966","56382969","56382972","56382973","56488186","56501568","56508492"],"15405":["55253383","55253818","55253819","55253820","55253821","55253822","55314389","55314390","55314391","55340200","55376518","55417156","55488435","55488436","55509003","55746918","55763035","55767252","55786668","55936824","55936904","55950940","55950958","56169219","56247197","56290668","56328168","56503390","56505428"],"15738":["55246010","55467273","56172474"],"17025":["55252534","55275254","55275255","55275256","55275257","55275259","56035703","56186014","56258432"],"17093":["55252818","55253170","55253171","55787094"],"17113":["55252937","55255521","55255522","55255523","55255524","55255525","55255526","55255527","55492598","55501646","55505454"],"17662":["55255664","55256641","55256674","55334927","56258325"],"18079":["56501005","56502644","56511776"],"18297":["55258517","55258766","55258767","55258768","55258769","55259168","55270728","55273491","55297758","55298421","55298460"],"18778":["55260434","55263438","55263442","55263443","55263447","55263448","55263449","55263450","55263554","55263555","55914499","55914500","56175588","56175589","56504171"],"18907":["55261087","55266770","55266772","55266773","55266774","55266776","55266777","55266778"],"22334":["55276190","55276300","55276301","55276302","55276303","55276598","55369009","56190759","56190760","56193288","56327938"],"23791":["55498718","55498719","55785658","55785659","55785660","55785661","55785663","56333437"],"24596":["55286200","55286438","55293576","56274642","56289944","56289945","56314386","56327001"],"26302":["55294714","55295272","55295273","55295274","55295275","55295276","55295278","55295279","55295447","55792289","56328530","56328802","56509255","56509259"],"27222":["55300237","55300757","55300973","55300974","55300975","55300976","55300982","55442883","56168174","56168182","56168195","56168196","56168205","56168217","56325340","56325342","56325350","56325406","56501010"],"28564":["55304611","55305241","55305272","55305273","55305274","55334805","55339065","55374076","55418775","55509239","55797501","56322200"],"28575":["55305334","55305487","55305488","55305489","55504318","55769521","55786477","56296742"],"28635":["55306306","55306755","55306757","55306758","55306759","55306760","55306772","55306773","55337419","55416014"],"29068":["55306244","55306369","55310034","55310056","56162437","56176463"],"29205":["55306383","55306974","55306975","55306987","55505766","55507550","55544635","55567927","55746419","56299347","56332906","56506343","56506344"],"29640":["55309909","56078687","56327296","56327307","56334646","56334647","56334648","56334649","56334650","56334651","56334652","56334653","56334654","56334655","56334656","56334657","56334658","56488328","56488455"],"30652":["55312141","55312886","55799571","55904641","56078770","56156276"],"31010":["55313829","55314416","55338190","55421099","55421100","56497212","56497217","56497218","56497220"],"31478":["55315648","55334621","55334622","55334624","55334625","56268900","56291651","56318524","56323604","56514680"],"31637":["55317706","55317709","55317714","55317715","55317716","55318557","55318991","55319861","56158988","56327958","56501392","56512922"],"31865":["55334430","55532975","55548752","55939804"],"32142":["55318518","55318552","55347212","55412662","55438392","55759832","56038518","56038529","56187484"],"32197":["55318385","55903720","55938624","55938625","55938666","56264870","56264871","56264872","56264875","56488395","56488398"],"32252":["55318560","55948071","56046645","56048231","56289467","56289520","56324016","56332040","56497950"],"32393":["55510943","55757887","55757899","55767367","55767368","55937536","56335653","56335654","56500934","56510242"],"32542":["55428325","55428344","55428611","56157749","56506464","56506494"],"32648":["55324209","55324240","55324241","55324242","55324243","55324247","56508177","56508178","56508180"],"33098":["55343147","55343165","55347729","55347758","55411339","55411344","55419078","56299511"],"33125":["55328190","55328716","55362566","56166785","56301752","56301753","56301754","56301755","56505045","56512862"],"33203":["55322208","55322442","55322443","55322444","55323885","55488857","55488858","56146235","56189657"],"33859":["55324803","55324804","55324813","55324815","55324816","55324817","55324818","55324819","55324821","55567862","56191440","56194026","56335922","56382804","56382822","56505401"],"34224":["55345075","55345187","55345188","55345189","55345190","55345191","55345192","55345196","55345198","55345199","55345200","55345204","55345206","55381003","55426078","55525997","56034486","56034487","56034567","56321665","56508064"],"34681":["55326452","55326455","55365678","55366102","55406257","55797525","55947331","56146942"],"34789":["55326815","55326821","55326822","55326823","56335336","56502634"],"34999":["55328872","55331530","55331532","55497625","55538680"],"35309":["55328656","55329989","55329990","55329991","55329992","55340649","55343024","55382446","55382447","55382449","55762817","55763529","56153257","56153259","56155377"],"35396":["55329036","55407416","55407417","55944625","56152888","56323157","56511400","56513983","56513984","56513985"],"35586":["55332634","55334506","55334507","55346533","56488626","56496287","56496291","56496459","56506972","56512680"],"35815":["55332860","55333267","55404319","55756372","55766404","56300467","56500625"],"36148":["55339608","55339897","55366122","55371303","55754516","56194428"],"36242":["55333673","55334738","55334806","55336179","55525833"],"36513":["55340722","55341158","55341159","55365240","55796718","55920698","55946712","56079881"],"36913":["55335905","55343341","55343349","55343350","55343351","55343352","55343353","55345900","55345901","55382156","55510997","56326550"],"36945":["55337218","55343167","55362790","56330242","56330243","56330244"],"37029":["55332716","55346138","55346352","55346368","55346372","55346631","55346635","55346637","55422830","55424498","55785984"],"39473":["55353695","55361484","55372536","55372537","55372538","55372539","55372540","55372541","55413948","55491312"],"41931":["55367328","55367477","55367732","55368917","55371080","56328628","56328750","56328751"],"42354":["55375824","55378632","55509368","55509371","55509373","55797452","56313029","56329372"],"42387":["55368142","55792404","56315006","56315007"],"45062":["55381208","55411857","55411858","55411899"],"45880":["55382873","55794021","55904336","55941908","56038437","56038443","56039208","56042986","56174794","56288230","56288243","56300744","56500996"],"45962":["55380983","55381583","55381584","55414328","55540810","55540812","55914481","55943113","55943114","56040944","56040945","56172310","56173022","56190661","56285567","56291207","56294920","56299170","56300675","56313093","56321798","56333946","56335347","56502002","56502003","56506425","56507324","56510467"],"46240":["55405860","55418094","55436354","56504482"],"46966":["55383304","55420258","55420285","55421964","55421965","55421966","55421967","55421968","55542051","55770222","56172304","56287388"],"50460":["55410635","55410739","55410744","55410745","55410746","55410747","55411289","56040849","56188538"],"51028":["55412507","55412861","55412868","55436584","55436745","55441371","56039398","56080036"],"51255":["55506000","56326172","56328193"],"51369":["55412625","55413360","55413625","55528080","56191094","56293241","56293242","56293243","56312130","56496886"],"53271":["55415714","55415726","55495795"],"56734":["55488123"],"59382":["55490154","55490256"],"59435":["55768113","55785346","55785370","55785371","55785374","55937387","55937400","55937412"],"61822":["55505163","55506145","55506148","55951077","55951078","55951081","56190358","56247755","56318632","56318644"],"62379":["55497105","55497107","55497109","55497110","55497112","55499954","55508355","56319575","56500987","56503057"],"62813":["55496412","56323668","56323669","56324339"],"63111":["55503929","55505293","55795171","55798398","56045654","56184095","56184147"],"63586":["55498055","55498301","55498453","55498454","55498647","55498944","55499321","55499353","55499354","55759303","55761740","55761751"],"63814":["56322471"],"64051":["55498206","56507582","56513219"],"64805":["55528915","55786331","55786335","55786591","56079940","56079941","56079942","56497635"],"65530":["55503413","55504796","55504798","55546145","56041831","56146516","56146517","56162035","56162037","56162038","56284897","56284898","56284899","56284900","56332625"],"65654":["55510531","55510554","55510555","56497741","56497742","56497743","56497785","56497786"],"66432":["55544262","55544339","55544340","55544341","55544342","55544344","55544349","55544351","55765439","56159076","56322288","56322455","56322456"],"67074":["55509883","55510296","56489237","56489359"],"69437":["56325914","56507747","56507748","56507750"],"69872":["55750822","55752364","55752365","55752366","55752367"],"70442":["55533707","55903807","55936313","55943201","56039081","56186440","56292606"],"71496":["55539493","55539499","56157657","56157674","56296218"],"71692":["55541941","55542187","55542188","55542189","55542190","55542191","55542192","55542193","55542194","56181308","56331538","56331595","56331604"],"72107":["55541410","55541894","55541895","56258429","56258434","56333940","56333942"],"72718":["55539990","55543344","55543346","55543347","55543348","55543350","55543353","55543400","56504529","56507667","56508483"],"74184":["55786411","55786419","55786420","56325256"],"74309":["55548760","55785909","55787646","56043114","56045905","56266384","56266385","56266386","56266387","56266388"],"74502":["55745904","56507859","56507860","56507861","56507862","56507863","56510447","56510449","56514239"],"74784":["56080790","56080793"],"75298":["55753611","55753941","55753942","55753943","55753944","55789824","55795282","56035522","56036627","56328412","56509885"],"75425":["55759279","55759420","55759421","55759422","56165609","56329682"],"75527":["55757659","55758978","55758979","55758980","55758981","55758982","55758983","55758984","55758985","55758986","55758987","55758988","55761052","55761193","55789870","55793620","55793732","56509542"],"75547":["56035661","56035675","56297646","56297647"],"75709":["55756093","55756354"],"76708":["55761741","55761772","55761773","55761774","55761775","55761833","55763198","55768039","56033919","56043832","56157836","56511501","56511502"],"77070":["55785778","55785802","55786948","55786949","55904803","56323761","56323762","56323830","56324119","56503180","56503181"],"78573":["55794315","55797505","55797506","55797507","55797570","55797571","55797572","55799631"],"78622":["55904518","55907165","55907166","55938977","56510271","56510272"],"78739":["56321299","56322509","56324295","56514957","56514958","56514963"],"78971":["55793484","55795712","55795713","56045715"],"79530":["55904323","55940688","55941850","55941851","55941852","55941853","55941854","55941855","55941856","56298400","56513529","56513531"],"80354":["55903439","55920200","56192607","56192608","56329983","56497469","56497470","56514014","56514956"],"80725":["56041000","56041501","56041502","56172235","56508421","56508422","56508423"],"81442":["55936749","55938150","55938151","55938152","55938153","55938154","55947693","55947694","55947695","55947810","55947811","56504871","56505001"],"82174":["55938974","55939073","55939074","55939075","55939313","55946971","56034957","56035479","56172056","56172057","56178683","56178697","56266201","56332915","56332946","56505119"],"82549":["56302495","56302497","56323228","56329231","56332631"],"83025":["55942998","55943130","55943164","55943165","55943167","55943169","55943171","55943172","55943173","55943174","55943175","55943176","56161465"],"83148":["56511091","56511101","56511115","56511116","56511117","56511536"],"83495":["55945628","55948614","55948616","55948618","55948622","55948624","55948883","56160379","56516091","56516096"],"83928":["55946426","55946434","55946437","56162996","56162997","56162998","56162999","56163000","56167123","56190492"],"84633":["55532109","56159049","56160174","56329873"],"85529":["56033938","56328919","56328920","56328921","56330023"],"85675":["56033419","56324415","56328307"],"86012":["56156743","56156749","56156750"],"86919":["56039930","56039943","56039947","56039948","56039949","56039950","56311708","56311709","56311710","56311711","56311712","56321979"],"86962":["56322372","56330954","56330956","56333790"],"87097":["56035822","56035939","56035943","56035945","56035947","56045950","56047948"],"87173":["56258239","56262324","56284955","56287911"],"87537":["56041182","56159426","56263703","56312792","56327513","56334670","56334823","56497158","56497176","56513523"],"87661":["56037659","56037705","56037706","56037707","56037708","56037709","56037710","56037711","56037712","56297046","56515309"],"88645":["56042768","56042775","56042776","56042778","56043477","56043478","56043479","56043520","56231280"],"88784":["56048263","56327179","56327181","56327190","56500695"],"333352":["56154808","56154864","56154865","56154867","56488383","56488387","56489044"],"333540":["56047098","56047127","56047130","56294294","56324963","56324970","56331825","56331833","56331849"],"333682":["56163467","56163647","56163648","56332739"],"334412":["56045662","56079994","56333215"],"335260":["56080843","56080859","56151744","56164207","56164211","56334105","56496381","56514463","56514464"],"335496":["56155278","56184731","56184739","56184740","56184741","56184742","56285859","56285862"],"336326":["56167978","56189997","56312334","56312335","56323131","56323133","56514154","56515926"],"336856":["56159371","56503379","56503383"],"337032":["56158813","56499463","56503144"],"337110":["56162711","56164446","56164447","56170212","56170213","56170214"],"337185":["56159562","56159791","56159792","56159793","56159794","56159808","56159809","56159810","56159811","56174433"],"337412":["56164394","56164707","56164713","56164714","56164715","56164716","56295879","56323409","56334795"],"337985":["56163460","56164304","56164305","56164306","56164321","56164322","56311751","56505454","56505881","56505892","56513596"],"338117":["56163842","56163843","56166001","56166011","56166012"],"339737":["56176147","56176176","56312708"],"339754":["56186520","56189460","56189461","56189462","56510040","56511429"],"340484":["56181929","56183367"],"340701":["56183721","56183782","56188738","56188934","56257613","56321207","56321352"],"340737":["56194789","56194790","56194791","56195039","56262394","56325995","56496997"],"340741":["56195271","56314988","56314989","56324369","56505264"],"341470":["56190715","56193815","56193816","56193817","56193818","56193819","56193820","56193821","56193822","56193823"],"342147":["56314885","56321554","56321555","56321556"],"342182":["56302764","56302797","56302799","56302811"],"342188":["56333099","56333128","56333165","56333166"],"343163":["56192528","56192554","56192555","56192557","56192558","56192560","56192571","56192572","56192573","56192582"],"343433":["56263287","56288025","56504400"],"343576":["56324259","56501593"],"343833":["56515947","56515949","56515950"],"344249":["56497653","56498193","56498196","56499127"],"345086":["56319178","56319184","56326907","56326908","56329980","56499493","56501071","56515336"],"345252":["56286229","56286247","56286248","56286249","56286250","56286251","56286252","56286253","56514917"],"345439":["56512824","56512843","56512844"],"345556":["56323242","56323331","56323333","56323334","56324044","56324205","56504007"],"345891":["56287924","56288132","56288133","56288321","56288322","56290024","56290025","56290030","56300208","56318647","56323991","56505539"],"346418":["56318474","56319170","56319171","56323150","56489306"],"346779":["56290286","56298093","56496902"],"347863":["56314762","56314769","56314770","56314771","56314772","56314773","56333056","56334045","56335136"],"348065":["56296473","56300596","56300608","56300610","56302171","56498315"],"348606":["56311459","56311838","56318274","56318275"],"348641":["56295907","56295921","56295922","56295923","56295924","56295925","56295926","56295927","56313674","56329992","56499929","56499935","56499936","56499937","56499938","56503229","56503377","56505245","56507583","56509414","56515058"],"349271":["56333969","56508273"],"349388":["56323829","56323858","56323859","56323860","56323861","56323862","56323863","56323864","56323865","56323866"],"349526":["56488877","56488886","56488887","56488888","56496158","56500472"],"350190":["56318500","56318516","56318517"],"350242":["56324203","56324857","56324858","56324886","56325366","56501995"],"350435":["56313127"],"350639":["56319805","56321281","56321282","56321283"],"350799":["56331848","56509315","56509316","56509317","56509318","56509319","56509320","56509321","56509322","56509323","56509324","56509325","56509329","56509330","56509331","56509332","56513568","56515609","56515610","56515611"],"350932":["56313244"],"351068":["56326535","56326544","56326545","56507151"],"351069":["56323110","56323737","56382725","56500006"],"351073":["56335594","56335747","56335810","56335814"],"351145":["56318406","56322213","56322216","56497972","56497973","56497974","56497983","56504566"],"351216":["56313616","56506978"],"351268":["56321028","56321061","56321062","56321063","56321064","56321065","56321066","56321067","56321068"],"351307":["56313920","56313933","56313934"],"351393":["56516063","56516474","56516475"],"351479":["56334914","56334994","56334995","56508222","56508223","56509434"],"351562":["56329723","56329748","56329749","56329753","56335526","56501991","56501997","56503288"],"351563":["56334133","56334136","56334137","56515586","56515587"],"351702":["56321855","56321897","56321898","56321899","56321900","56321901","56321902","56321903","56321940","56329814","56513978"],"351720":["56322725","56322941","56322942","56322943","56322946"],"351735":["56318454","56318466","56318477","56318653","56318654","56330346","56333806"],"351766":["56314861","56498885"],"351827":["56321027","56321036","56321037","56321038","56321039"],"351835":["56314868","56314883"],"351883":["56318215","56318249","56318251","56318252","56318256","56318257","56319373","56319374","56330441","56330442","56330639"],"351980":["56318329","56318333"],"351993":["56488806","56500481"],"352041":["56333098"],"352146":["56333705","56333792","56333793","56333826"],"352182":["56503834","56503840","56503841","56509931"],"352469":["56323187","56326960"],"352480":["56488679","56488691","56488692","56507342","56507343","56507344","56507345"],"352626":["56322988","56324457","56324458","56325832"],"352678":["56503106","56503761","56503762","56503763","56511447"],"352728":["56331102"],"352773":["56323607","56323617","56323618","56323619","56323620","56323621","56323622","56323623","56323624","56323625","56323626","56323627","56323628","56323629","56323630","56323631","56323632","56323633","56323634"],"352926":["56322378","56322431"],"352987":["56326817","56327905","56327906","56327907","56332306","56502225","56515414"],"352989":["56334486","56505891"],"353000":["56330226"],"353016":["56331010","56331320","56332527","56332530","56332532","56503703"],"353042":["56498334","56498353","56503937","56515511"],"353062":["56328045"],"353115":["56323490","56323653","56323654","56323655","56323656","56325568","56325571","56325572","56330091","56332795"],"353155":["56323810","56324648","56324649"],"353168":["56323036","56333951","56333954","56333957","56333963","56503845","56508642","56508643"],"353193":["56323300","56323324","56323325","56501694","56508262"],"353246":["56499145","56499233","56500200"],"353263":["56332023"],"353275":["56327431","56382837","56382838","56501900"],"353287":["56501675","56505918","56505919","56506837"],"353321":["56333506"],"353347":["56334129","56334301","56334665","56502667"],"353364":["56324882","56327257","56327258","56327259","56327260","56327261","56327262","56327263","56327264","56327265","56327266","56514151","56514453"],"353411":["56335601","56497885"],"353414":["56324769","56324776","56330123","56330125","56510963","56510965","56510966"],"353451":["56324291","56327977","56327978","56327979"],"353455":["56324236","56326037","56326038","56326039","56326046","56326521","56326522","56326523","56326524","56326525","56326526","56326527","56326528","56497420"],"353550":["56335900"],"353605":["56330324","56332621","56332622","56332623","56503760","56504890","56504892","56504896","56509827"],"353616":["56327565","56328895","56328896","56328897","56328898","56328899","56328905","56328906","56328907"],"353623":["56331788","56501617","56501618","56503246"],"353708":["56325868","56329141"],"353719":["56331184","56331240","56331271","56331272","56331273","56332496"],"353831":["56335737","56382674","56511562","56511563"],"353923":["56332065","56332374","56332600","56334475"],"353934":["56334929","56335266"],"353947":["56335589","56335859","56335860","56335861","56508512"],"354036":["56325614","56325628","56325629","56326271"],"354088":["56501273","56501313","56501391","56501956","56501957","56508129"],"354118":["56326530","56334284","56334285","56504379","56512783","56514241"],"354231":["56329513"],"354277":["56327916","56328474","56328475","56328476","56328662","56328663","56328664","56328665","56328958","56328959","56382841"],"354305":["56515142","56515146"],"354312":["56327962","56327987","56328002","56497183","56504163"],"354326":["56327601","56328246","56328247"],"354370":["56502277","56502285"],"354407":["56327642","56505538"],"354485":["56326910","56327094","56327097"],"354500":["56326716","56329544"],"354549":["56509096"],"354619":["56328642","56329802","56329803","56329804","56329805","56329806","56329807","56329808","56329809","56333173","56333174","56333175","56333176","56333177","56333178","56333179","56333180","56333181","56333182","56333183","56333184","56333185","56333186","56333187","56333188","56333189","56510530"],"354646":["56327210"],"354655":["56328422","56500631","56500632","56500633"],"354675":["56327369","56327371","56327372"],"354699":["56327109","56330465","56330466","56330467","56330468","56330470","56330471","56330472","56330473","56330474","56330475","56330476","56330477","56330478","56509666","56512593","56513242","56513245","56513246","56513281"],"354781":["56329161","56500457","56500458","56500459","56500460","56500461"],"354983":["56329084","56332923","56332924"],"355015":["56328873","56328876","56328878"],"355196":["56329659","56331492","56331550"],"355197":["56511804","56511811","56511814"],"355221":["56505123","56505590","56505591","56510926"],"355246":["56495960","56496305","56501697"],"355296":["56330378"],"355333":["56328082"],"355362":["56329305","56329993","56329994","56329995","56329996","56329997","56329998","56329999","56330000","56330001"],"355469":["56331716","56331852"],"355564":["56332626","56332639","56332640","56332691"],"355584":["56332904"],"355611":["56332948","56332993","56332994","56332995","56505436"],"355727":["56333562","56333795","56333796","56333797","56333798","56333799","56333800","56333801","56333802"],"355733":["56504050"],"355815":["56334518","56334753"],"355927":["56503794","56503823","56503830"],"355969":["56488241","56488275","56488276"],"355991":["56505529"],"356067":["56500674","56500686","56500688"],"356116":["56382654","56488238","56488278","56488351","56488352","56489207","56497243","56499814","56506396","56506398"],"356349":["56496818","56510676","56510677"],"356368":["56499858","56499866","56515827"],"356415":["56497353","56497389","56497473","56502636","56511028"],"356482":["56503722","56503727","56503729","56503740"],"356501":["56496299","56497365","56497366","56497367","56514300"],"356549":["56496460","56499963"],"356622":["56496464","56496466","56496467","56496468","56496469","56496470","56496471","56496472","56496473","56501712"],"356773":["56507262","56507267"],"357046":["56500184","56500231","56500234","56501619"],"357082":["56511736"],"357106":["56501757","56501859","56503391","56503392","56503393","56503394","56503395","56503396","56503397"],"357355":["56504922","56504930","56504931","56504932"],"357491":["56503135","56503142"],"357515":["56505627","56505867","56505868","56505870","56505889","56505945","56506036","56506039","56506044","56506075"],"357609":["56504544"],"357648":["56507015","56507044","56507045","56507046","56507047","56507048","56507049","56507050","56507051","56507052"],"357659":["56504315"],"357676":["56504696","56504705","56504706","56504707","56504708","56504709","56504710","56504711"],"357901":["56507806","56507812"],"357916":["56514044"],"358000":["56508010","56508382","56508383","56508980","56509943"],"358147":["56506146","56508431","56510473"],"358177":["56514915","56515787","56515792","56515793","56515794"],"358283":["56509641","56509647"],"358556":["56510287","56510669","56510997","56512739","56516339"],"358580":["56509023","56509061","56509070","56509071","56509072","56509073"],"358632":["56508867","56514991","56514992"],"358653":["56510255","56510260","56510261","56510262"],"358788":["56514683","56515188"],"358890":["56511327","56511469","56511470","56511559","56514833"],"358993":["56514195","56514199","56514200","56514201","56514202"],"359143":["56511818","56511835","56511836","56511837","56511838","56511897"],"359144":["56511820","56511821","56511822","56511823","56511824","56511825","56511826","56511827","56511828"],"359232":["56512763","56513305","56513311"],"359291":["56514939","56514959","56514960","56514961","56514962"],"359342":["56514552","56514599","56514600","56514601","56514602","56514603"],"359498":["56514006","56514346","56514581"]}
JSON;

        $clientToUserMap = json_decode($json,true);


        $params = [
            [
                'field' =>  'common.date',
                'type' => 'date',
                'value' => [
                    'start' => '2023-11-01',
                    'end' => '2024-04-30'
                ]
            ]
        ];

        $echoParams = [
            [
                'key' => 'common.visible',
                'label' => '查看范围',
                'value' => ''
            ],
            [
                'key' => 'common.date',
                'label' => '时间区间',
                'value' => '2023-11-01 ~ 2024-04-30'
            ]
        ];

        foreach ($clientToUserMap as $clientId => $userIds)
        {
            foreach ($userIds as $userId)
            {
                User::setLoginUserById($userId);
                $extInfo = [
                    'params' => $params,
                    'echo_params' => $echoParams,
                    'analysis_number' => \common\library\ai_agent\Helper::getAnalysisNumber($clientId),
                ];

                $asyncTask = new AsyncTask($clientId);
                $asyncTask->getOperator()->create($clientId, Constants::TYPE_OKKI_AI, \common\library\async_task\AsyncTaskConstant::SCENE_AI_ASSET_ANALYSIS_OPERATION, 0, extInfo: $extInfo);

                $assetAnalysisJob = new \common\library\ai_agent\jobs\AssetAnalysisJob($clientId, $userId, $asyncTask->task_id);
                $assetAnalysisJob->setParams($params);
                $assetAnalysisJob->handle();
                self::info("buildAssetAnalysis success: clientId:[{$clientId}] userId:[$userId]");
            }

        }
    }

    public function actionSendAssetAnalysisBusinessMail()
    {
        $json = <<<JSON
{"181":["3416267","3997752","9500774","16241534","17457635","47739583","47739584","47739585","55495576","56039814"],"628":["6028884","6028887","11878081","11878134","32448596","37187134","55282899","56036081"],"1064":["881271","19570438","55144367","55941437","56266862","56292247","56318232","56327661"],"1157":["71656","109132","4821423","8472740","8472741","8472742","8472743","8472744","39560219","55234313","55258680","55300805","55919257","55947967","56176460","56178691"],"1838":["1385350","11982762","11982787","12104865","44341614","55207534","55297305","55366173","55750860","55753961","55798252","56159869","56164976","56167794","56331998","56511500"],"2327":["206126","1971583","7249346","12720090","15549695","15549697","15549698","15549699","15549918","15549920","15549921","15549922","27167054","50073630","55288579","55288586","55288588","55374858","55949721","56001868","56164155","56169686","56174187"],"2501":["13253923","16424750","16425921","47517447","47523251","47523252","49251672","56324232"],"3764":["3822046","4125844","8951998","14713690","17049236","22398255","24900806","24944483","44791805","55761354"],"4204":["22966710","27947452","31186076","55254676","55255462","55269850","55269851","55529712","56160141","56167957"],"4317":["28392274","28847449","28847455","28847459","28853329","56183258","56183259","56183260","56183262","56183460","56183462","56514466"],"4333":["28763236","28786385","28786387","55270424","55295103"],"4611":["50017127","50017129","52409028","55222587","55271650","55274477","55379375"],"4621":["3226966","31473907","31473908","31473909","31473911","31473912","31473913","31473914","55170559","55223361","55223362","55264171","55269782","55290998","55307526","55307527","55307528","56038725","56324822","56505334"],"4648":["14170829","14383530","18602462","23773092","27003970","31633901","31714605","42079642","55249175","55276361","55276427","55276429","55937996","55938067","56033328","56158034","56158035","56288163","56516499"],"4699":["5341926","31917572","35541819","55491734"],"4862":["27665898","33102443","55253831"],"5041":["34765311"],"5117":["32210351","35773456","35788085","35788086","35788087","55254503","55270391","55270577","55298350","55298351"],"7126":["27222929","30846824","30875598","33903320","44191363","44191364","55285542"],"7708":["40302714","55793073","56165662","56165878","56323785","56325381","56325528","56326409","56505752"],"7797":["45592754","55145242","55254716","55274504","55353532","55544263","56501130"],"9901":["52249194","53438059","53438060","53439676","53442939","55166315","55218389","55280192","55304859"],"10628":["6438034","54912204","55145152","55247957","55247958","55247964","56169459","56185701"],"12665":["55176833","55220191","55220338","55220340","55256236","55289404","55289406","55290426","55290427","56166098","56166099","56166100","56166101","56167269","56167272","56167275","56167276","56167320","56188374","56498569","56499362","56499363"],"13878":["55220814","55221090","55221091","55505428","56488576"],"15460":["55240136","55240137","55249292","55294828","55297024","55319162","55541431"],"16859":["55251841","55252951","55256208","56163362","56172407"],"16993":["55252411","55265817","55268933","56187220","56504856"],"17111":["55252933","55256337","55262047"],"17372":["55254297","55257943","56329821"],"17586":["55255369","55256889","55256890","55256892","55256909","55257574"],"19416":["55296008","55311899","55546068","55546069","55546070","55787830","55943702","55944889"],"19469":["55263612","55274039","55274040","55293066","56170366","56184623","56326708","56326709"],"19902":["55267149","55267150","55267153","55267157","55267178","55267195","55500127","55542313","55769727","55769781"],"20262":["55267310","55303381","55303382"],"21327":["55271888","55272316","55272318","55272322","55272324","55274821","55300266","55315390","55315392","55315393","55315394","55341858","55378177","56185654","56185655","56185907","56185908","56186697"],"22482":["55309133","55309811","56504836","56504857"],"22599":["55277317","55277550","55277551","55277552","55277554","55277558","55277559","55277560","55277561","55292156","55297212","55298604","55364366","55364367","55364368","55364369","56188545","56192495","56311887","56313155","56510614","56510615"],"23006":["55279364","55279654","55279655","55279656","55279657","55279661","55279662","55279664","55296329","55305448","55317410","55320472","55368166","55507754","55545221","55545223","56168590","56302080","56335132","56335270","56509609","56509921"],"23841":["55283372","55364971","55364972","55364973","55364974","55366619","55366627","55366628","55366631","56321071"],"25240":["55288693","55289943","55289944","55289946","55289948","56297155","56297157","56297341","56301013"],"27037":["55299055","55299277","55299296","55323376","55425970","55540688","55540689","55540690"],"27359":["55300846","55302796","56181999","56182001","56186510","56291371","56295014","56497659"],"28339":["55425299","55425430","55425431","55425433","55425435","56509790","56514223"],"29094":["56330677","56330746","56330747","56330748","56508826"],"29312":["55314539","55315320","55315321","55315322","55315323","55510163","55543942","55543943","55745967","55920079","55920081","56156575","56156576","56175896","56175913","56514926"],"29518":["56507399","56507440","56507441"],"29883":["55309393","55309424","55309425","55347301","56036486","56036493","56164397","56171596","56192896","56500812"],"31235":["55330813","55331697","55331701","55331702","55785447"],"31386":["55317141","55318314","55318315","55761234","55788290","55788291","55788294","55791148"],"31668":["55317417","55317422","55318363","55374902","55761951","56329720","56505581","56510156"],"32315":["55325648","55325742","55325743","55325744","55770011","55940573","55940574","55940575","55940576","55940577","56159040","56192584","56266836"],"32564":["55320208","55320318","55320322","55320324","55320325","55320328","55320329","55320332","55320333","55320387","55320393","55369356","55423764","55425000","55506431","55539258","55548273","55567870","55936588"],"33209":["55325096","55325097","55325099","55325101","55325143","55325144","55325145","55325146","55325155","55325235","56080621","56266347","56292710","56300383","56300392"],"33338":["55323637","55324365","55324367","55324369","55324381","55324382","55324383","55324384","55324385"],"33554":["55325047","55325486","55325487","55326172","55418227","55430196","55769607","56331106"],"34173":["55788606","56172273","56284982","56325799"],"34386":["55328806","55329315","55330102","55531284","56162516","56162517","56162519","56162536","56263190"],"34627":["55346424","55756577","55920512","56328429","56328435","56328437","56328442","56329152"],"34646":["55337629","55949114","56312740","56312785"],"34929":["55327731","55331294","55331456","55331457","55331458","55336864","55380261","55756605","55756615","55756621","55756772","55756778"],"35311":["55328622","55329225","55329226","55341952","55364387","55539363","55762682","55811844"],"35721":["55372596","55374710","55374713","55374714","55374911"],"35824":["55331452","55331658","55331661","55331662","55549458","55549462","55649471","55649972","55754854","55907289","55938238","56079893","56157018","56195917"],"36105":["55761981","55762008","55762009","55762010","55762011","55762012","55762013","55763416","55792311"],"36204":["55335215","55369095","55503367","56162954","56163483","56292428","56298631"],"36241":["56488493","56488662","56488663","56488664","56488665","56488666","56488667"],"36262":["55333886","55334465","55526041","55526044","56174230","56331066","56331071"],"36401":["55431729"],"36412":["55345917","55345923","55345925","55345927","55513123","55524345","56192374"],"36459":["56170620","56170644","56170645","56170646","56170647","56170649","56170650","56170651","56174349"],"36728":["55333186","55333381","55337341","55337344","55337345","56496948","56496949","56496950","56496951","56496952"],"36836":["55413638","55528935","55757674","55757675","55797132","56080267","56159130","56159135","56159139","56159141","56507120","56507121","56507175","56507826"],"37515":["55334340","55337748","55337749","55337750","55337751","55337752","55337753","55337754","55539619","55547628","55547629","55791036","56312068","56332660","56335663","56516396"],"37651":["55345295","55345328","56156096"],"37688":["55341938","55341961","55341962","55341963","56042177","56169790","56318460","56318462","56507614","56514919"],"38746":["55341933","55342214","55342223","56037134","56044814","56044815","56161494","56318721","56497421","56497426"],"40320":["56325879","56325888","56325889","56326075"],"40787":["55375225","56513472","56513473","56513474","56513475","56513476"],"40829":["55362526","55363474","55363475","55363476","55363477","55363478","55442758","55442759","55547768","56157336","56511440"],"41308":["55364893","55364908","55510204","55795565","56515440"],"41446":["55365500","55366324","55366325","55499625","55499626","55502969","55539100","55949853","56190305","56335458"],"41755":["56321568","56321604","56321606","56321718","56321719","56321721","56321722","56321723","56321724","56321725","56321727","56321728","56321730","56321731"],"42085":["55368014","55368026","55368628","55408948","56193527"],"42867":["55380872","56325991","56507147","56507186","56514551"],"43116":["55370769","55370972","55370975","55523845","56497128"],"43602":["55372690","56323487","56323488"],"43670":["55380622","55380623","55380694","55408090","55408101","55408102","55408110","55408128","55408129","55412236"],"44213":["55379515","55419405","55420712","55420801","55526837","56032166","56032171","56170582","56170606","56184073","56496264","56496272"],"44598":["55376055","55407208","55407209","55425911","55770004","56331706","56331711","56331713","56335258","56505557"],"44841":["55381901","55426526","55492736","55902588","55902589","56178580","56178581","56189347","56287102","56503571","56509579"],"45018":["55430694","55491991","55491993","55491994","56169759","56231236","56247653","56288641"],"45304":["55377560","55380805","55946702","56037621","56040532","56488808","56488809","56497458","56514780"],"45858":["55405183","56328402","56328432","56329219","56329220","56329221","56329223"],"46097":["55404294","55407665","55407666","55407667","55407668","55426225","55529139","55762587","55762588","55763805"],"47266":["55383148","55383154","55383155","56038617","56153137","56153340","56502638","56505205","56516427"],"47280":["55383150","55411748","55411749","55411750","55418237","55426438","55426439","55431348","55527382","56113641"],"47300":["55383089","55404562","55404563","55404565","55939515","56154795","56157505"],"47722":["55407154","55407156","56032393","56160063","56258536"],"49206":["55408363","56293650","56319368"],"49667":["55411902","55411942","55411943","55511277","55531540","55797866","55950381","56080115","56151254","56161103","56257817","56500202"],"49686":["55410644","55413395","55413396","55413398","55413399","55413586","55413683","55423639","55425275","55426143","55951157","56037343","56178013","56182010","56186198","56186200","56186201","56187334","56187851","56194437","56507710","56512332"],"51969":["55414203","55414305","55414306","55414307","55414308","56161024","56322286","56328912"],"52015":["55438435","55442691","55442692","55442693","55442694","55442698","55442699","55442700","55442709","55505936"],"52224":["55428401","55428402","55428403","55428404","55436103","55495237","55526107","55751358","55752527","55756066","55903240","56033318","56161241","56176159"],"52257":["55420329","56033061","56033063","56033064","56033111","56158930","56164453","56335062","56500379","56503413"],"54237":["55423098","55423099","55423100","55423101","55423102","55423103","55423104","55792874","56194291","56335946"],"54369":["55417577","55422575","55788790","55948517","55950911","56184932","56184933"],"55581":["55432350","55436836","55441515","55763625","56079516","56186546","56335615","56335727"],"58575":["55442345","55442524","55442527","55442528","55442529","55442530","55442534","55442535","55523878"],"58690":["55441458","55441470","55441471","55441472","55441473","55441474","55441475","56191604"],"58912":["55442424","55442426","55442431","55442439","55442501","56188935","56188936","56188943","56189399","56191194"],"58923":["55487464","55487473","55487474","55487475","56321406","56321408","56321409"],"59047":["55489624","55489630","55504440","55526643","55752335","55801153","55904043","55904748"],"60288":["55937254","55937297","56172781","56298391","56321748"],"61279":["55492214","55492251","55492252","55492253","55492254","55492255"],"61637":["55492719","55492960","55492961","55492962","55492963","55492964","55492965","55492966","55494032","55510890","55511118","55754518","55788569","56176097","56178520","56263528","56293407","56302782"],"61883":["55539504","55541083","55752321","55907979","55920094","56034192","56038714","56258315","56333807"],"62405":["55505191","55512808"],"63105":["55497832","55497851","55497975","55547204","55549167"],"63208":["55497662","55497663","55525734","55746261","56285666","56321022","56514645"],"63938":["55497967","55500339","55500610","56033273","56300711","56312554"],"67468":["55513047","55544863","56319204","56319205","56508068","56508100"],"67635":["55746189","55747143","55747144","56512630"],"67950":["55523715","55524695","55524696","55524698","55525361","55755399","55759097","55762772","55762773","55762820","56498307"],"68355":["55532227","55533776","55545725"],"68443":["56328643","56328986","56329890","56329891","56329892","56329893","56329894","56332767","56333474"],"68493":["55527175","55527186","55796353","56038748","56038749","56044292","56158944","56158945","56289479"],"70026":["55529547","55529632","55529634","55529635","55529636","55529637","55529638","55529639","55951122","56034476","56034477","56513337"],"70337":["55530540","56321510"],"70538":["55531868","55792425","55903719","55950539","56151225"],"70645":["55794826","55794861","55794862","56156202","56157940","56170500","56173880","56263995","56266302","56322164","56504016"],"70760":["55548518","55548888","56510304"],"70858":["55531571","55755061","55755062","55755063","55755064","55755074","55769174","55769197","55769204"],"71274":["55543794","55543796","55543797","55543899","55757842","55946456","55946461"],"71488":["55539259","55539277","55539278","55539279","55787191","55800191","55800192","55940326","55941454","56159870","56160584","56161666","56170610","56173221","56190505","56295614","56299263","56506492"],"73035":["55540594","55548964","55548968","55548970","55548971","55548972","55549232","56156061","56313444","56504605"],"73387":["55545603","55548272","55568642"],"73789":["55544261","55544467","55544487","55544489","56046113","56159837","56159838"],"73910":["55762982","56038815","56038816","56038818","56330571","56330577"],"73947":["55545149","55545189","55545191","55546217","55546218","55747264","55751469","55752451","56157793","56165430","56166411","56514417"],"74853":["55798199","56034148","56170576","56268693","56294425"],"75155":["55754824","55755103","56500229"],"75822":["55760958","55788295","55903864","56188066","56326017"],"76042":["55761101","55761114","55761115","55761116","55761117","55761845","55761847","56191467","56504851"],"76263":["55759878","55760357","55760358","55760359","56294202","56508640","56508937","56509417"],"76595":["55762592","55766986","55937844","55937846","56166544","56191428","56191429","56507575","56507576","56508932","56508943","56508944","56508945","56508964","56508965","56509263","56509264","56509265","56509266","56509367","56511274"],"77275":["55766426","55766431","55766433","55766434","55766435","55766440","56044889"],"77690":["55785823","55785840","55785841","56080683","56169083","56172114"],"78347":["55789465","55945850","56048010","56078723","56168582","56333282","56333283","56488245","56507657"],"78395":["55793173","55795258","55795259","55795260","55795355","55920410","55947310","56257363","56300502","56330803","56501566","56511086"],"78821":["55902930","55902993","55902994","55902995","55902996","55902997","55902998","55903002","55903003"],"78988":["56510187","56510517"],"79135":["55907573","55907588","55907589","56039936","56079514","56286334","56292618","56312413"],"79639":["55799237","55902768","56078890"],"79759":["55797684","55801180","56036284","56330562"],"79986":["55937442","56039912","56043705","56335388","56335461","56501438","56501439","56501442","56501443","56502963"],"81345":["55940253","55940254","55946689","56079540","56160661","56162652","56274301","56274554","56509018","56509020"],"81375":["55946504","55947008","55947009","55949920","55950763","56153009","56184261"],"83624":["55943787","55943971","55943972","56032902","56079283","56168014","56169082","56295339","56295347","56311936","56503170","56503213"],"83860":["56037569","56040038","56040041","56040042","56498928","56501890","56505647"],"84846":["55950355","55950356","55950357","55950358","55951346"],"84909":["56496130","56496397","56496398"],"85677":["56032185","56032212","56032213","56032214","56032215","56032344","56035461","56152792","56161468","56291701"],"85968":["56178667","56178669"],"86419":["56080411","56146323","56146324","56155149","56182808","56183430","56314979","56323299","56510910"],"87280":["56039163","56039173","56039186","56039199","56039201","56039205","56311426"],"88570":["56046741","56047275","56078868","56080507","56285660"],"89013":["56323509","56323516","56323525","56331168"],"89054":["56286258","56286268","56286269","56314379","56508551"],"89071":["56039960","56040642","56040710","56041016","56155327"],"89091":["56039922","56040812","56040819","56040821","56040822","56040829","56042118","56488409"],"334315":["56326351","56326364","56326365","56326366","56326367","56326368","56326369","56326370","56326371","56326372","56326373","56326374","56326375","56326376","56326377","56326378","56326379","56326380","56326381","56326382","56326383","56326384","56326385","56326386","56326389","56326390","56326392","56326393","56326394","56328083"],"334323":["56325359"],"336056":["56158949","56159015","56159031","56498708","56498709","56498714","56508808"],"336216":["56155265","56155302","56155317","56155318","56155319","56155320","56155321","56155391","56171084"],"336340":["56156920","56160108","56160110","56160111","56163250"],"337087":["56194341","56194910","56266230","56266231","56330582"],"337091":["56159127","56186360","56187385","56187387","56187747","56192889","56511189","56511954"],"337391":["56160694","56161015","56161038","56291110","56324220","56324326","56324564","56326023","56326024"],"337984":["56186267","56297735","56501428"],"338030":["56162862","56325929","56326127"],"339018":["56298480","56314930"],"339106":["56175280","56187212","56192978","56195770","56295324","56489385","56496187"],"339803":["56174060","56174109","56174110","56382650","56382792","56497518","56497519","56497520"],"339947":["56175488","56176316","56176317","56176626","56176627","56189561","56231525","56288856","56294314","56488931","56488933","56497547","56509404"],"340166":["56178261","56181480","56181481","56504021","56508157","56508161","56509135"],"340245":["56514011","56514013"],"340299":["56178216","56178421","56178422","56178423","56178425","56178432","56297909","56500859","56500861","56509171"],"340491":["56193869","56193871","56193872","56193873","56193875","56194030","56194135","56195405","56263013","56327399"],"340777":["56191347","56191361","56191362","56194845","56231416"],"341274":["56184994","56185140","56247208"],"341283":["56185106","56186868","56186869","56186870","56186871","56186872","56194001","56194002","56194003","56194004","56194321","56195995","56290643","56330706","56330707","56330708","56515811"],"342034":["56299603","56300039","56300040","56300041","56300042","56300043","56300044","56300045","56300046","56300047","56300048","56300049","56300052","56300672","56329340"],"342144":["56296292","56296585","56321695","56333414","56502974","56503065","56503157"],"343334":["56266166","56266475","56266476","56266477","56266478","56266481","56266482","56266483","56266484"],"344491":["56318885","56319222","56319225","56319226","56335164"],"344495":["56263889","56274385","56274386","56289886","56289960","56301481","56329330"],"345535":["56301430"],"346999":["56325444"],"347470":["56293616","56293617","56293618"],"349183":["56325333","56325357","56328800","56329341","56500846"],"349296":["56497240","56498004","56498005","56498006"],"349934":["56322059","56322091","56322092","56322093","56322096","56322097","56322108","56322109","56327949","56327951","56501897","56510299","56514724"],"350424":["56313335"],"350726":["56319606"],"350788":["56322920","56323139","56333459"],"350916":["56325375","56330283","56330285","56333650"],"350985":["56312923"],"350992":["56313527","56313541","56313542"],"350994":["56329052"],"351011":["56333036"],"351047":["56319123","56319128","56319129","56319130","56319131"],"351199":["56314874","56314881","56314882"],"351275":["56318168","56318175","56318177","56318182","56318218","56320122","56320123"],"351310":["56326101","56510426","56510708","56510709","56510710"],"351390":["56318096","56318104","56318106","56318107","56333531","56500254","56502170"],"351447":["56323735","56333511","56334982","56496149","56499388","56499389"],"351449":["56322905","56322997","56322998","56322999","56323000","56323001","56323002","56323003","56323004","56323005","56323006","56323007","56323008","56323009","56323010","56323011","56323012","56323013","56323014","56328043","56328244","56330915","56333234","56333237","56333331","56333348","56333349","56382658","56382733","56500145","56513883"],"351501":["56319021","56319416","56319417","56334712"],"351641":["56329570","56330631","56330946","56333548","56497674"],"351649":["56332191","56382761","56498052"],"351658":["56382954","56382958"],"351663":["56318217","56328169","56511123","56511124","56512468"],"351681":["56510830","56511608"],"351703":["56502997","56503011","56503017","56503018","56503022"],"351729":["56318095","56318120"],"351737":["56335074","56335286","56335287","56335288","56335289","56335292"],"351871":["56330122","56334969","56334970","56334971"],"351916":["56499008","56503020","56506702"],"351949":["56319071","56319080","56325308","56488483","56499720"],"352033":["56319463"],"352046":["56332898","56332909","56332910","56335868","56500602"],"352057":["56318374"],"352068":["56321827","56329840","56329841","56329842","56329843","56329859"],"352101":["56327941","56327966","56327981","56327982","56327983"],"352103":["56319587","56319592","56319754","56319789","56319797","56321776","56334474","56507840"],"352138":["56326225","56326269","56326275","56332089","56332095","56503072","56503073","56506811","56510583"],"352169":["56382727"],"352180":["56326606","56329714","56334433","56497981","56508605","56508607"],"352181":["56326036","56326098","56326467","56326468","56326469","56506393","56506394"],"352193":["56333512","56333523"],"352284":["56325641","56325646","56325647","56325648","56329353","56329483","56508482"],"352313":["56319501","56321156","56321157","56321158","56321165"],"352392":["56329815","56329839","56497858","56497859"],"352441":["56330288","56330335","56330347","56330348","56330361","56330362","56330366","56498320","56498321","56498322"],"352527":["56322742","56322774","56322775","56322776","56322777","56322778","56322779","56322783","56322784","56323914","56324159","56334155","56335528"],"352587":["56332963","56513367","56513370"],"352640":["56323750"],"352722":["56513127","56514443"],"352757":["56322206","56496195","56507956","56508820"],"352785":["56322067","56510232","56510234"],"352797":["56323132","56326864","56326865","56328394","56328395","56328397","56328399","56328411","56499917","56510196"],"352799":["56332703","56333769"],"352814":["56324385"],"352936":["56332901","56335011","56335012","56335013","56335014","56335015","56335016","56382925"],"352981":["56329345","56329347","56488416","56510385"],"353029":["56326435","56326446","56326447","56326448","56326449","56326450","56326451","56326452","56513174"],"353056":["56324910","56382947","56382948","56382956","56383005","56403653","56488178","56488179","56488180"],"353075":["56322839","56322846","56322848","56323512","56323651"],"353167":["56331805","56331812","56331814","56331815","56331817","56331818","56331819","56331820","56513786","56513791"],"353211":["56330436","56501746","56515161"],"353279":["56323413","56323416","56323417","56382712"],"353294":["56328243","56328516","56328517","56328518","56328519","56328529","56331469","56331470","56331471"],"353388":["56331399","56331411","56331412","56331413","56331414","56331415","56331416","56331417","56331418","56331419"],"353446":["56506300","56506358","56507624","56507628","56507634"],"353474":["56329634","56329639","56329657","56331324","56331325","56331327","56331328","56331329","56331462","56497944"],"353487":["56324270","56324595","56324596","56324653","56328070","56498372"],"353501":["56329525","56329534","56329539"],"353505":["56324300","56324523","56324524","56324952","56324953","56324954","56324955","56324956","56324957","56324961","56516454"],"353521":["56324825","56325975","56325977","56325978","56325981","56326104","56332324","56498986"],"353563":["56326391","56326727","56382842"],"353648":["56324486","56324700","56324701","56324702","56324705","56506040","56509415","56514893"],"353782":["56330227"],"353825":["56329684","56329694","56329695","56329696","56497848","56497850","56499588"],"353842":["56333651"],"353882":["56328823","56488399","56504960","56504961","56504962"],"353887":["56330583","56330608","56330609","56330610","56330611","56330612","56330613"],"353946":["56325475","56334011","56511583","56512455","56514366","56514691","56515238"],"354031":["56327031","56329164","56329167","56329168"],"354091":["56512792","56515191","56515192","56515193","56515194","56515195"],"354104":["56325870","56326402"],"354112":["56331295"],"354139":["56489387","56489397","56489398","56489400","56489403","56498629","56505186","56515262"],"354184":["56328933","56328934","56328935","56328936","56328937","56328938","56328939","56328940","56328942","56333484","56504334"],"354299":["56328984","56329673","56329675"],"354318":["56330404","56510726"],"354396":["56335585","56504085","56504086","56504099"],"354403":["56326778","56332155","56332160","56334904","56513026","56514753"],"354575":["56331855","56511287","56511288","56511289","56511290","56511291","56514822"],"354605":["56327581","56333144","56333151"],"354669":["56328631","56328652","56328923","56328924","56328928","56497038","56497039","56499984","56501258"],"354725":["56328868","56332450","56514556","56514706"],"354735":["56333757","56334351","56334848"],"354785":["56330655","56330667","56330668","56330670","56330671","56330776","56330892","56330895","56331154","56335894"],"354797":["56329129","56329266","56329268","56329971","56329972","56329974","56329975"],"354844":["56328973","56329080","56333019","56333020"],"354851":["56330406","56330413","56507057","56515270","56515271","56515272"],"354935":["56489083"],"354951":["56334048","56335053","56335054","56335055","56335606","56335607","56499310"],"354972":["56327454","56327456","56327457","56327458","56327459","56327460","56510190","56510192"],"355064":["56329530","56329540","56333399","56513067"],"355129":["56330951"],"355153":["56498753","56498939"],"355326":["56332503","56496191","56503264"],"355440":["56331802","56331891","56331892","56331893","56331894","56331895","56331896","56331897","56331898","56331899","56332109"],"355507":["56489120"],"355551":["56332717"],"355620":["56333333","56333368","56333369","56333370","56333371","56333372","56333373","56333386","56333421","56333422","56333423","56333424","56333425","56333448","56333449","56333450","56333451","56333452","56333659","56333660","56333661","56506779","56506780","56510284","56514829"],"355669":["56500429"],"355774":["56514142"],"355825":["56510206","56510215","56510245"],"355871":["56512746","56512785","56512786"],"355909":["56335072","56335078","56335079","56335081","56335082","56335083","56507213"],"356047":["56335206","56335291","56516031"],"356057":["56507292","56507328"],"356197":["56488195","56488260","56488261","56488264"],"356328":["56488456","56488466","56488478","56488482","56500023","56504137","56508900","56514069"],"356477":["56497086","56497713","56497714","56497715","56497716","56497718","56497719","56497721","56498373","56515012"],"356574":["56512816","56512828","56513578","56513579","56513580"],"356606":["56497119","56497139","56498099","56498102","56502259","56507315"],"356952":["56500991","56501009","56501794","56501795","56501796"],"356955":["56506681","56506693"],"356991":["56500584","56501986","56503281","56503815","56510220","56510222"],"357045":["56501151","56501220","56503751","56506426"],"357184":["56509745","56509750","56509751","56509752","56509753","56510110","56511809"],"357221":["56503993"],"357247":["56511492"],"357341":["56502332","56502338"],"357513":["56505129"],"357522":["56503077","56503087","56503088","56503089","56513930"],"357614":["56506163","56506402","56506403","56506404","56506405"],"357627":["56504618","56506212","56506213","56506214","56506217","56506227"],"357708":["56506419","56507714","56507715","56507716","56510804","56512849","56512850","56512851"],"358062":["56507325","56508239","56508240","56508241","56508242","56508243","56508245","56508246"],"358078":["56507143","56507221","56507223","56507224","56513212","56513233"],"358114":["56506048","56507492","56507493","56507494"],"358143":["56509485","56509491","56509493","56509494","56509524"],"358270":["56515300","56515957","56515958"],"358314":["56512806","56512829","56512830","56512831","56512832","56512833","56512834","56512835","56512836","56512837","56512838","56512839","56512840","56512841","56513048"],"358458":["56514490","56514507"],"358466":["56508075","56508140","56508141","56508142","56508143","56508144","56508145","56508146","56508149","56508151","56509287"],"358495":["56511198","56511206","56511419","56511456","56511466","56511468","56511484","56513971"],"358499":["56513425","56513431","56513435"],"358669":["56511021","56512010","56512011","56512012","56512013","56512014","56512015","56512016","56512017","56512019","56512024","56512035"],"358821":["56509767","56510935","56510936","56510937"],"358984":["56511721","56511742","56511743","56511745"],"359111":["56513796","56514364","56514496","56514497"],"359142":["56511688","56515227","56515231","56515232","56515233"],"359327":["56515179","56515922","56515924"],"359388":["56514692","56514713","56514714","56514715","56514716"],"359524":["56515087","56515113","56515114","56515116","56515117","56515324"],"359568":["56514281","56514282","56514283","56514284","56514286","56514287","56514288","56514289","56514290","56514291"],"323":["3902394","4195872","4360378","4673457","4693582","4693584","4693593","4693615","4693680","4693686","4693687","4693688","4695253","55946373","55946376"],"549":["476184","49608351"],"674":["6301195","6730249","6732525","6732554","15489152","26206160","26206161","55221004","55535144"],"954":["477751","479827","55311449","55314027","55438204","55758822","56514859","56514860"],"1130":["1321118","8248548","8248738","8248893","8248951","8248991","8249079","8249251","9431469","25572203","56501572"],"1141":["3977223","8420979","31481667","42572828","49745650","55247344","55270374","55420095","55941345","56293709","56329329","56329331","56511740"],"1182":["5008236","19423116","19423348","23185485","55254374","55292070","55301858","56505079","56506249"],"1684":["830373","55292889","55292891","56163863","56163864","56166310","56167297","56172510","56331870"],"2953":["1967475","25581860","26340223","55220975","55281596","55374024"],"3058":["9670617","12477745","19861259","20016190","20016192","20016195","37055082","55270108","55942944","55943461"],"3402":["7651431","22251843","55254783","55940450","55940451","56164711","56165630","56165631","56274087","56503615"],"3946":["20293079","26051113","26114115","26114124","26114132","26118866","26119057","55427855"],"4216":["19118255","27824044","55283420","55283421","55283422","55301474","55375331","55431469","55765441","56300119","56300120","56300121","56507436"],"4300":["19408469","28602209","28602210","28752885","30370709","30370710","36332174","55494177","55754671","55787891","55799088","55799089","55799090","55799091","55799796","55939482","55944415","56164027"],"4743":["32094721","32119430","32119431","32119432","32119437","32119438","51631479","54752546","55254661","55257816","55270330","55282222","55288377","55299346","55346657","55383251","55493141","55503379","56036141","56036171","56164123","56187196","56195632"],"5300":["20818145","55210929","55758186","56033525","56182324"],"5731":["39063715","50084406","55408755","55408774","55427137","55753992","56162189","56508050"],"6848":["7056862","43742990","53277137","55273200","55800325","55800332","55842004","55949462","56161475","56285820"],"8914":["49168984","55264281","55265682","55266334","55275251","55286795","55295213","55295252","55295525","55295526","55295593","55295641","55295643","55295758","55299262","55801749","55801752","55801760","56159661","56189179","56191515","56266632","56289928","56508315","56513343"],"9732":["3805445","8776035","42070909","52965473","52965474","52965475","55284598","55751669"],"11727":["55164328","55172176","55172177","55172178","55172179","55172180","55172181","55172183","55172184","55172185","55258854","55423994","55789791","55789801","56151745","56151746","56258498","56258499","56290976","56319497"],"12872":["55185069","55199096","55199097","55769191","56334567","56334592","56335143","56505216"],"14474":["55223471","55315496","55315497","55315498","55318954","55323745","55490443","56032756","56035391"],"14716":["55226833","55235106","55235108","55235109","55235110","55235113","55273092","55343450","55406743","56382965","56382966","56382969","56382972","56382973","56488186","56501568","56508492"],"15405":["55253383","55253818","55253819","55253820","55253821","55253822","55314389","55314390","55314391","55340200","55376518","55417156","55488435","55488436","55509003","55746918","55763035","55786668","55936824","55936904","55950940","55950958","56169219","56247197","56290668","56328168","56503390","56505428"],"15738":["55246010","55467273","56172474"],"17025":["55252534","55275254","55275255","55275256","55275257","55275259","56035703","56186014","56258432"],"17093":["55252818","55253170","55253171"],"17113":["55252937","55255521","55255522","55255523","55255524","55255525","55255526","55255527","55492598","55505454"],"17662":["55255664","55256641","55256674","55334927","56258325"],"18079":["56501005","56502644","56511776"],"18297":["55258517","55258766","55258767","55258769","55259168","55270728","55273491","55298421","55298460"],"18778":["55260434","55263438","55263442","55263443","55263447","55263448","55263449","55263450","55263554","55263555","55914499","55914500","56175588","56175589","56504171"],"18907":["55261087","55266770","55266772","55266773","55266774","55266776","55266777","55266778"],"22334":["55276190","55276300","55276301","55276302","55276598","55369009","56190759","56190760","56193288","56327938"],"23791":["55498718","55498719","55785658","55785660","55785661","55785663","56333437"],"24596":["55286200","55286438","55293576","56274642","56289944","56289945","56314386","56327001"],"26302":["55294714","55295272","55295273","55295274","55295275","55295276","55295278","55295279","55295447","55792289","56328530","56328802","56509255","56509259"],"27222":["55300237","55300757","55300973","55300974","55300975","55300976","55300982","55442883","56168174","56168182","56168195","56168196","56168205","56168217","56325340","56325342","56325350","56325406","56501010"],"28564":["55304611","55305241","55305272","55305273","55305274","55334805","55339065","55374076","55509239","56322200"],"28575":["55305334","55305487","55305488","55305489"],"28635":["55306306","55306755","55306757","55306758","55306759","55306760","55306772","55306773","55337419","55416014"],"29068":["55306244","55306369","55310034","55310056","56162437","56176463"],"29205":["55306383","55306974","55306975","55505766","55507550","55544635","55567927","55746419","56332906","56506343","56506344"],"29640":["55309909","56078687","56327296","56327307","56334646","56334647","56334648","56334649","56334650","56334651","56334652","56334653","56334654","56334655","56334656","56334657","56334658","56488328","56488455"],"30652":["55312141","55799571","55904641","56078770","56156276"],"31010":["55313829","55314416","55338190","55421099","55421100","56497212","56497217","56497218","56497220"],"31478":["55315648","55334621","55334622","55334624","55334625","56268900","56291651","56318524","56323604","56514680"],"31637":["55317706","55317709","55317714","55317715","55317716","55318557","56158988","56501392","56512922"],"31865":["55532975","55548752"],"32142":["55318518","55318552","55347212","55412662","55438392","55759832","56038518","56187484"],"32197":["55318385","55903720","55938624","55938625","55938666","56264870","56264871","56264872","56264875","56488395","56488398"],"32252":["55318560","55948071","56046645","56048231","56289467","56289520","56324016","56332040","56497950"],"32393":["55510943","55757887","55757899","55767367","55767368","55937536","56335653","56335654","56500934","56510242"],"32542":["55428325","56157749","56506464","56506494"],"32648":["55324209","55324240","55324243","55324247","56508177","56508178","56508180"],"33098":["55343147","55343165","55347729","55347758","55411339","55411344","55419078","56299511"],"33125":["55328190","55328716","55362566","56166785","56301752","56301753","56301754","56301755","56505045","56512862"],"33203":["55322208","55322442","55322443","55322444","55488857","55488858","56146235","56189657"],"33859":["55324803","55324804","55324813","55324815","55324816","55324817","55324818","55324819","55324821","55567862","56191440","56194026","56335922","56382804","56505401"],"34224":["55345075","55345187","55345188","55345189","55345190","55345191","55345192","55345196","55345198","55345199","55345200","55345204","55345206","55381003","55426078","55525997","56034486","56034487","56321665","56508064"],"34681":["55326452","55326455","55365678","55366102","55406257","55797525","55947331","56146942"],"34789":["55326815","55326821","55326822","55326823","56335336","56502634"],"34999":["55328872","55331530","55331532","55497625","55538680"],"35309":["55328656","55329989","55329990","55329991","55329992","55340649","55343024","55382446","55382447","55382449","55762817","55763529","56153257","56153259","56155377"],"35396":["55329036","55407416","55407417","55944625","56152888","56323157","56511400","56513983","56513984","56513985"],"35586":["55332634","55334506","55334507","55346533","56488626","56496287","56496291","56496459","56506972","56512680"],"35815":["55332860","55404319","55756372","55766404","56300467","56500625"],"36148":["55339608","55339897","55366122","55754516","56194428"],"36242":["55333673","55334738","55334806","55336179"],"36513":["55340722","55341158","55341159","55365240","55796718","55920698","55946712","56079881"],"36913":["55335905","55343341","55343349","55343350","55343351","55343352","55343353","55345900","55345901","55382156","55510997","56326550"],"36945":["55337218","55343167","55362790","56330242","56330243","56330244"],"37029":["55332716","55346352","55346368","55346372","55422830","55785984"],"39473":["55353695","55372538","55413948"],"41931":["55367328","55367477","55367732","55368917","55371080","56328628","56328750","56328751"],"42354":["55375824","55378632","55509368","55509371","55509373","55797452","56313029","56329372"],"42387":["55368142","55792404","56315006","56315007"],"45062":["55381208","55411857","55411858","55411899"],"45880":["55382873","55794021","55904336","55941908","56038437","56038443","56039208","56042986","56174794","56288230","56288243","56300744","56500996"],"45962":["55380983","55381583","55414328","55540810","55914481","55943113","55943114","56040944","56040945","56173022","56321798","56333946","56335347","56502002","56502003","56507324","56510467"],"46240":["55405860","55418094","55436354","56504482"],"46966":["55383304","55420258","55420285","55421964","55421965","55421966","55421967","55421968","55542051","55770222","56172304","56287388"],"50460":["55410635","55410739","55410744","55410745","55410746","55410747","55411289","56040849","56188538"],"51028":["55412507","55412861","55412868","55436584","55436745","55441371","56039398","56080036"],"51255":["55506000","56326172","56328193"],"51369":["55412625","55413360","55413625","55528080","56191094","56293241","56293242","56293243","56312130","56496886"],"53271":["55415714","55415726","55495795"],"56734":["55488123"],"59382":["55490154","55490256"],"59435":["55768113","55785346","55785370","55785371","55785374","55937412"],"61822":["55505163","55506145","55506148","55951077","55951078","55951081","56190358","56247755","56318632","56318644"],"62379":["55497105","55497107","55497109","55497110","55497112","55499954","56319575","56500987","56503057"],"62813":["55496412","56323668","56323669","56324339"],"63111":["55503929","55795171","55798398","56045654","56184095","56184147"],"63586":["55498055","55498301","55498453","55498454","55498647","55499321","55499353","55499354","55759303","55761751"],"63814":["56322471"],"64051":["55498206","56507582","56513219"],"64805":["55786331","55786335","55786591","56079940","56079941","56079942","56497635"],"65530":["55503413","55504796","55504798","55546145","56041831","56146516","56146517","56162035","56162037","56162038","56284897","56284898","56284899","56284900","56332625"],"65654":["55510531","55510554","55510555","56497741","56497742","56497743","56497785","56497786"],"66432":["55544262","55544339","55544340","55544341","55544342","55544344","55544349","55544351","56159076","56322288","56322455","56322456"],"67074":["55509883","55510296","56489237","56489359"],"69437":["56325914","56507747","56507748","56507750"],"69872":["55750822","55752364","55752365","55752366","55752367"],"70442":["55533707","55903807","55936313","55943201","56186440","56292606"],"71496":["55539493","55539499","56157657","56157674","56296218"],"71692":["55541941","55542187","55542188","55542189","55542190","55542191","55542192","55542193","55542194","56181308","56331538","56331595","56331604"],"72107":["55541410","55541894","55541895","56258429","56258434","56333940","56333942"],"72718":["55539990","55543344","55543346","55543347","55543348","55543350","55543353","55543400","56504529","56507667","56508483"],"74184":["55786411","55786419","55786420","56325256"],"74309":["55548760","55785909","55787646","56043114","56045905","56266384","56266385"],"74502":["55745904","56507859","56507860","56507861","56507862","56507863","56510447","56510449","56514239"],"74784":["56080790","56080793"],"75298":["55753611","55753941","55753942","55753943","55753944","55795282","56035522","56036627","56328412","56509885"],"75425":["55759279","55759420","55759421","55759422","56165609","56329682"],"75527":["55757659","55758978","55758979","55758980","55758981","55758982","55758983","55758984","55758985","55758986","55758987","55758988","55761052","55761193","55789870","55793620","55793732","56509542"],"75547":["56035661","56035675","56297647"],"75709":["55756093","55756354"],"76708":["55761741","55761772","55761773","55761774","55761775","55761833","55763198","55768039","56033919","56043832","56157836","56511501","56511502"],"77070":["55785778","55785802","55786949","56323830","56324119","56503180","56503181"],"78573":["55794315","55797505","55797506","55797507","55797570","55797571","55797572","55799631"],"78622":["55904518","55907165","55907166","55938977","56510271","56510272"],"78739":["56322509","56514957","56514958","56514963"],"78971":["55793484","55795712","55795713","56045715"],"79530":["55904323","55940688","55941850","55941851","55941852","55941853","55941854","55941855","55941856","56298400","56513529","56513531"],"80354":["55903439","55920200","56192607","56497469","56497470","56514014","56514956"],"80725":["56041000","56041501","56041502","56172235","56508421","56508422","56508423"],"81442":["55936749","55938150","55938151","55938152","55938153","55938154","55947693","55947694","55947695","55947810","55947811","56504871","56505001"],"82174":["55938974","55939073","55939074","55939075","55939313","55946971","56034957","56035479","56172056","56178683","56178697","56266201","56332915","56505119"],"82549":["56302495","56302497","56323228","56329231","56332631"],"83025":["55942998","55943130","55943164","55943165","55943167","55943169","55943171","55943172","55943173","55943174","55943175","55943176","56161465"],"83148":["56511091","56511101","56511115","56511116","56511117","56511536"],"83495":["55945628","55948614","55948616","55948618","55948622","55948624","55948883","56160379","56516091","56516096"],"83928":["55946426","55946434","55946437","56162997","56162998","56162999","56167123"],"84633":["55532109","56159049","56160174","56329873"],"85529":["56033938","56328919","56328920","56328921","56330023"],"85675":["56033419","56324415","56328307"],"86012":["56156743","56156749","56156750"],"86919":["56039930","56039943","56039947","56039949","56039950","56311708","56311709","56311710","56311711","56311712","56321979"],"86962":["56322372","56330954","56330956","56333790"],"87097":["56035822","56035939","56035943","56035945","56035947","56045950","56047948"],"87173":["56258239","56262324","56284955","56287911"],"87537":["56041182","56159426","56263703","56312792","56327513","56334670","56334823","56497158","56497176","56513523"],"87661":["56037659","56037705","56037706","56037707","56037708","56037709","56037710","56037711","56037712","56297046","56515309"],"88645":["56042768","56042775","56042776","56042778","56043477","56043478","56043479","56043520","56231280"],"88784":["56048263","56327179","56327181","56327190","56500695"],"333352":["56154808","56154865","56154867","56488383","56488387","56489044"],"333540":["56047098","56047127","56047130","56294294","56324963","56324970","56331825","56331833","56331849"],"333682":["56163467","56163647","56163648","56332739"],"334412":["56045662","56079994","56333215"],"335260":["56080843","56080859","56151744","56164207","56164211","56334105","56496381","56514463","56514464"],"335496":["56155278","56184731","56184739","56184740","56184741","56184742","56285859","56285862"],"336326":["56167978","56189997","56312334","56312335","56323131","56323133","56514154","56515926"],"336856":["56159371","56503379","56503383"],"337032":["56158813","56499463","56503144"],"337110":["56162711","56164446"],"337185":["56159562","56159791","56159792","56159793","56159794","56159808","56159809","56159810","56159811","56174433"],"337412":["56164394","56164707","56164713","56164714","56164715","56164716","56295879","56323409","56334795"],"337985":["56163460","56164304","56164305","56164306","56164321","56164322","56311751","56505454","56505881","56505892","56513596"],"338117":["56163842","56163843","56166001","56166011","56166012"],"339737":["56176147","56176176","56312708"],"339754":["56189460","56189461","56189462","56510040"],"340484":["56181929","56183367"],"340701":["56183721","56183782","56188738","56188934","56257613","56321207","56321352"],"340737":["56194789","56194790","56194791","56195039","56262394","56325995","56496997"],"340741":["56195271","56314989","56324369","56505264"],"341470":["56190715","56193815","56193816","56193817","56193818","56193819","56193820","56193821","56193822","56193823"],"342147":["56314885","56321554","56321555","56321556"],"342182":["56302764","56302797","56302799","56302811"],"342188":["56333099","56333128","56333165","56333166"],"343163":["56192528","56192554","56192555","56192557","56192558","56192560","56192571","56192572","56192573","56192582"],"343433":["56263287","56288025","56504400"],"343576":["56324259","56501593"],"343833":["56515947","56515949","56515950"],"344249":["56497653","56498193","56498196","56499127"],"345086":["56319178","56319184","56326907","56326908","56329980","56499493","56501071","56515336"],"345252":["56286229","56286247","56286248","56286249","56286250","56286251","56286252","56286253","56514917"],"345439":["56512824","56512843","56512844"],"345556":["56323242","56323331","56323333","56323334","56324044","56324205","56504007"],"345891":["56287924","56288132","56288133","56288321","56288322","56290024","56290025","56290030","56300208","56318647","56323991","56505539"],"346418":["56318474","56319170","56319171","56323150","56489306"],"346779":["56290286","56298093","56496902"],"347863":["56314762","56314769","56314770","56314771","56314772","56314773","56333056","56334045","56335136"],"348065":["56296473","56300608","56300610","56302171","56498315"],"348606":["56311459","56311838","56318274","56318275"],"348641":["56295907","56295921","56295922","56295923","56295924","56295925","56295926","56295927","56313674","56329992","56499929","56499935","56499937","56499938","56503229","56503377","56505245","56507583","56509414","56515058"],"349271":["56333969","56508273"],"349388":["56323829","56323858","56323859","56323860","56323861","56323862","56323863","56323864","56323865","56323866"],"349526":["56488877","56488886","56488887","56488888","56496158","56500472"],"350190":["56318500","56318516","56318517"],"350242":["56324203","56324857","56324858","56324886","56325366","56501995"],"350435":["56313127"],"350639":["56319805","56321281","56321282","56321283"],"350799":["56331848","56509315","56509316","56509317","56509318","56509319","56509320","56509321","56509322","56509323","56509324","56509325","56509329","56509330","56509331","56513568","56515609","56515610","56515611"],"350932":["56313244"],"351068":["56326535","56326544","56326545","56507151"],"351069":["56323110","56323737"],"351073":["56335594","56335747","56335810","56335814"],"351145":["56318406","56322213","56322216","56497972","56497973","56497974","56497983","56504566"],"351216":["56313616","56506978"],"351268":["56321028","56321061","56321062","56321063","56321064","56321065","56321066","56321067","56321068"],"351307":["56313920","56313933","56313934"],"351393":["56516063","56516474","56516475"],"351479":["56334914","56334994","56334995","56508222","56508223","56509434"],"351562":["56329723","56329748","56329749","56329753","56335526","56501991","56501997"],"351563":["56334133","56334136","56334137","56515586","56515587"],"351702":["56321855","56321897","56321898","56321899","56321900","56321901","56321902","56321940","56329814"],"351720":["56322725","56322941","56322942","56322943","56322946"],"351735":["56318454","56318466","56318477","56318653","56318654","56330346","56333806"],"351766":["56314861","56498885"],"351827":["56321027","56321036","56321037","56321038","56321039"],"351835":["56314868","56314883"],"351883":["56318215","56318249","56318251","56318252","56318256","56318257","56319373","56319374","56330441","56330442","56330639"],"351980":["56318329","56318333"],"351993":["56488806","56500481"],"352041":["56333098"],"352146":["56333705","56333792","56333793","56333826"],"352182":["56503834","56503840","56503841","56509931"],"352469":["56323187","56326960"],"352480":["56488679","56488691","56488692","56507342","56507343","56507344","56507345"],"352626":["56322988","56324457","56324458","56325832"],"352678":["56503106","56503761","56503762","56503763","56511447"],"352728":["56331102"],"352773":["56323607","56323617","56323618","56323619","56323620","56323621","56323622","56323623","56323624","56323625","56323626","56323627","56323628","56323629","56323630","56323631","56323632","56323633","56323634"],"352926":["56322378","56322431"],"352987":["56326817","56327905","56327906","56327907","56332306","56502225","56515414"],"352989":["56334486","56505891"],"353000":["56330226"],"353016":["56331010","56331320","56332527","56332530","56332532","56503703"],"353042":["56498334","56498353","56503937","56515511"],"353062":["56328045"],"353115":["56323490","56323653","56323654","56323655","56323656","56325568","56325571","56325572","56330091","56332795"],"353155":["56323810","56324648","56324649"],"353168":["56323036","56333951","56333954","56333957","56333963","56503845","56508642","56508643"],"353193":["56323300","56323324","56323325","56501694","56508262"],"353246":["56499145","56499233","56500200"],"353263":["56332023"],"353275":["56327431","56382837","56382838","56501900"],"353287":["56501675","56505918","56505919","56506837"],"353321":["56333506"],"353347":["56334129","56334301","56334665","56502667"],"353364":["56327257","56327258","56327259","56327260","56327261","56327262","56327263","56327264","56327265","56327266","56514151","56514453"],"353411":["56335601","56497885"],"353414":["56324769","56324776","56330123","56330125","56510963","56510965","56510966"],"353451":["56324291","56327977","56327978","56327979"],"353455":["56324236","56326038","56326046","56326521","56326522","56326523","56326524","56326525","56326526","56326527","56326528","56497420"],"353550":["56335900"],"353605":["56330324","56332621","56332622","56332623","56503760","56504890","56504892","56504896","56509827"],"353616":["56327565","56328895","56328896","56328897","56328898","56328899","56328905","56328906","56328907"],"353623":["56331788","56501617","56501618"],"353708":["56325868","56329141"],"353719":["56331184","56331240","56331271","56331272","56331273","56332496"],"353831":["56335737","56382674","56511562","56511563"],"353923":["56332065","56332374","56332600","56334475"],"353934":["56334929","56335266"],"353947":["56335589","56335859","56335860","56335861","56508512"],"354036":["56325614","56325628","56325629","56326271"],"354088":["56501273","56501313","56501391","56501956","56501957","56508129"],"354118":["56326530","56334284","56334285","56504379","56512783","56514241"],"354231":["56329513"],"354277":["56327916","56328474","56328475","56328662","56328663","56328664","56328665","56328958","56328959","56382841"],"354305":["56515142","56515146"],"354312":["56327962","56327987","56328002","56497183","56504163"],"354326":["56327601","56328246","56328247"],"354370":["56502277","56502285"],"354407":["56327642","56505538"],"354485":["56326910","56327094","56327097"],"354500":["56326716","56329544"],"354549":["56509096"],"354619":["56328642","56329802","56329803","56329804","56329805","56329806","56329807","56329808","56329809","56333173","56333174","56333175","56333176","56333177","56333178","56333179","56333180","56333181","56333182","56333183","56333184","56333185","56333186","56333187","56333188","56333189","56510530"],"354646":["56327210"],"354655":["56328422","56500631","56500632","56500633"],"354675":["56327369","56327371","56327372"],"354699":["56327109","56330466","56330467","56330468","56330470","56330471","56330472","56330473","56330474","56330475","56330476","56330477","56330478","56509666","56512593","56513242","56513245","56513246","56513281"],"354781":["56329161","56500457","56500458","56500459","56500460","56500461"],"354983":["56329084","56332923","56332924"],"355015":["56328873","56328876","56328878"],"355196":["56329659","56331492","56331550"],"355197":["56511804","56511811","56511814"],"355221":["56505590","56505591","56510926"],"355246":["56495960","56496305","56501697"],"355296":["56330378"],"355333":["56328082"],"355362":["56329305","56329993","56329994","56329995","56329996","56329997","56329998","56329999","56330000","56330001"],"355469":["56331716","56331852"],"355564":["56332626","56332639","56332640","56332691"],"355584":["56332904"],"355611":["56332948","56332993","56332994","56332995","56505436"],"355727":["56333562","56333795","56333796","56333797","56333798","56333799","56333801","56333802"],"355733":["56504050"],"355815":["56334518","56334753"],"355927":["56503794","56503823","56503830"],"355969":["56488241","56488275","56488276"],"355991":["56505529"],"356067":["56500674","56500686","56500688"],"356116":["56382654","56488238","56488278","56488351","56488352","56489207","56497243","56499814","56506396","56506398"],"356349":["56496818","56510676","56510677"],"356368":["56499858","56499866","56515827"],"356415":["56497353","56497389","56497473","56502636","56511028"],"356482":["56503722","56503727","56503729","56503740"],"356501":["56496299","56497365","56497366","56514300"],"356549":["56496460","56499963"],"356622":["56496464","56496466","56496467","56496468","56496469","56496470","56496471","56496472","56496473","56501712"],"356773":["56507262","56507267"],"357046":["56500184","56500231","56500234","56501619"],"357082":["56511736"],"357106":["56501757","56501859","56503391","56503392","56503393","56503394","56503395","56503396","56503397"],"357355":["56504922","56504930","56504931","56504932"],"357491":["56503135","56503142"],"357515":["56505627","56505867","56505868","56505870","56505889","56505945","56506036","56506039","56506044","56506075"],"357609":["56504544"],"357648":["56507015","56507044","56507045","56507046","56507047","56507048","56507049","56507050","56507051","56507052"],"357659":["56504315"],"357676":["56504696","56504705","56504706","56504707","56504708","56504709","56504710","56504711"],"357901":["56507806","56507812"],"357916":["56514044"],"358000":["56508010","56508382","56508383","56508980","56509943"],"358147":["56506146","56508431","56510473"],"358177":["56514915","56515787","56515792","56515793","56515794"],"358283":["56509641","56509647"],"358556":["56510287","56510669","56510997","56512739","56516339"],"358580":["56509023","56509061","56509070","56509071","56509072","56509073"],"358632":["56508867","56514992"],"358653":["56510255","56510260","56510261","56510262"],"358788":["56514683","56515188"],"358890":["56511327","56511469","56511470","56511559","56514833"],"358993":["56514195","56514199","56514200","56514201","56514202"],"359143":["56511818","56511835","56511836","56511837","56511838","56511897"],"359144":["56511820","56511821","56511822","56511823","56511824","56511825","56511826","56511827","56511828"],"359232":["56512763","56513305","56513311"],"359291":["56514939","56514959","56514960","56514961","56514962"],"359342":["56514552","56514599","56514600","56514601","56514602"],"359498":["56514346"]}
JSON;
        $clientToUserMap = json_decode($json, true);

        foreach ($clientToUserMap as $clientId => $userIds)
        {

            foreach ($userIds as $userId)
            {
                $user = new User($userId,$clientId);
                $email = $user->getEmail();

                $templateData = [
                    'emailTitle' => '「AI客户分析」竟能分析出这些，快来免费体验～',
                    'status' => 4,
                ];

                Helper::sendEmail($email, $templateData, 'subscribe_statistic_report', 'get_asset_analysis_subscribe_template',1);
                self::info("client:{$clientId} user_id[{$userId}] 成功发送运营邮件");


            }

        }

    }


    /**
     * @param $tasks // 需要调度的任务列表
     * @param $totalMinutes // 需要在多少分钟内完成
     * @param $elementsPerMinute // 一个进程大概每分钟能消费多少任务 （仅仅用作提醒）
     * @param $processLimit // 进程数上限
     * @return array
     */
    public function chunkTasks($tasks, $totalMinutes, $processLimit, $elementsPerMinute)
    {

        if ($elementsPerMinute <= 0) {
            throw new \RuntimeException("请评估每个任务大概需要消耗的时长");
        }

        $maxElementsPerProcess = $elementsPerMinute * $totalMinutes;

        // 计算总任务量
        $totalTasks = count($tasks);

        // 计算需要的进程数
        $requiredProcesses = ceil($totalTasks / $maxElementsPerProcess);
        // 确保进程数不超过上限
        $processCount = min($requiredProcesses, $processLimit);


        return array_chunk($tasks, ceil($totalTasks / $processCount));
    }


    public function actionHandleAiAnalysisSubscriptionTask($subscriptionList)
    {
        $subscriptionList = json_decode($subscriptionList, true);
        $clientToSubscriptionMap = [];
        foreach ($subscriptionList as $subscriptionInfo)
        {
            $clientId = $subscriptionInfo['clientId'];
            $clientToSubscriptionMap[$clientId][] = $subscriptionInfo;
        }

        foreach ($clientToSubscriptionMap as $clientId => $list)
        {
            foreach ($list as $subscriptionInfo)
            {
                $userId = $subscriptionInfo['userId'];
                $params = $subscriptionInfo['params'] ?? [];
                $echoParams = $subscriptionInfo['echo_params'] ?? [];
                $configId = $subscriptionInfo['config_id'];
                $scene = $subscriptionInfo['scene'];

                User::setLoginUserById($userId);
                $service = new \common\library\ai\service\AiAgentWriteService();
                try {
                    $service->analysis($params, $echoParams, $configId, $scene,0);
                } catch (\Throwable $exception) {
                    self::info("客户资产分析订阅运行失败",[
                        'configId' => $configId,
                        'scene' => $scene,
                        'errMsg' => $exception->getMessage()
                    ]);
                    continue;
                }

                self::info("客户资产分析订阅运行完成",$subscriptionInfo);
            }
            $this->cleanUpForClient($clientId);

        }

    }


    public function sendSubscriptWarning($params)
    {


        $taskCount = $params['taskCount'];
        $maxTotalElements = $params['maxTotalElements'];
        $totalMinute = $params['totalMinute'];
        $processLimit = $params['processLimit'];
        $elementsPerMinute = $params['elementsPerMinute'];

        $dingTalk = new DingTalkRobot(null,'SEC7eafef52aa991ac8b69fbb688604583724f4d2008a12e67a0f4191e76b40a2f3');
        $dingTalk->setWebhook('https://oapi.dingtalk.com/robot/send?access_token=28285b936349c49235f74f9651d33a96a2476ea1e89d4858e244de4fac8c1b48');

        $missTask = max($taskCount - $maxTotalElements, 0);
        $title = "客户分析订阅任务执行超时预警";
        $markdownStr = $title . "
===\n";
        $markdownStr .= "## 辅助信息 
执行时间上限: $totalMinute \n
需要执行任务总数: {$taskCount} \n
当前启动进程总数: {$processLimit} \n
每分钟处理任务数: {$elementsPerMinute} \n 
一小时内能够处理的任务限制: {$maxTotalElements}  \n
可能执行超过时间限制的task总数: {$missTask}  \n

";
        $markdownStr .= "\n--- \n\n";

        $dingTalk->markdown($title,$markdownStr);

    }


    public function actionDispatchCustomerSalesMonitor()
    {
        ini_set("memory_limit", "1024M");
        $clientIds = array_column($this->getClientList(0, true, true, null, 0, 0), 'client_id');
//        $clientIds = [333383];
        $pushStartDate = date('Y-m-d 00:00:00', strtotime('-1 year', time()));
        $pushEndDate = date('Y-m-d 23:59:59', time());

        foreach ($clientIds as $clientId)
        {
            if (!\common\library\ai_agent\Helper::hasInsightPermission($clientId)) {
                continue;
            }
            $db = PgActiveRecord::getDbByClientId($clientId);

            $adminUser = PrivilegeService::getInstance($clientId)->getAdminUserId();
            User::setLoginUserById($adminUser);

            // 查询上周成交订单金额有变化的客户
            $startDate = date('Y-m-d H:i:s', strtotime('last week monday'));
            $endDate = date('Y-m-d H:i:s', strtotime('last week sunday 23:59:59'));

            $sql = "select company_id,user_id,serial_id from tbl_company where  user_id != '{}' and is_archive=1 and performance_order_count > 3 and latest_transaction_order_time > '{$startDate}' and latest_transaction_order_time <= '{$endDate}' and client_id = {$clientId}";
            $list = $db->createCommand($sql)->queryAll();

            if (empty($list)) continue;

            $companyInfoMap = array_column($list, null, 'company_id');

            $pushUserMap = [];
            // 构造参数
            foreach ($companyInfoMap as $companyId => $companyInfo)
            {
                $userIds = \common\library\util\PgsqlUtil::trimArray($companyInfo['user_id']);
                $serialId = $companyInfo['serial_id'] ?? '';
                if (empty($serialId)) continue;

                foreach ($userIds as $userId)
                {
                    $pushUserMap[$userId][] = [
                        'params' => [
                            [
                                'field' => 'common.select_cycle',
                                'value' => 'month'
                            ],
                            [
                                'field' => 'common.data_source',
                                'value' => 'order'
                            ],
                            [
                                'field' => 'company.serial_id',
                                'value' => $serialId,
                                'label' => '客户编号'
                            ],
                            [
                                'field' => 'order.performance_date',
                                'type' => 'date',
                                'value' => [
                                    'start' => $pushStartDate,
                                    'end' => $pushEndDate
                                ]
                            ]
                        ],
                        'company_id' => $companyId
                    ];
                }

            }

            // 执行推送
            foreach ($pushUserMap as $userId => $pushInfo)
            {
                $privilegeService = PrivilegeService::getInstance($clientId,$userId);
                //没有订单查看权限不推送
                if (!$privilegeService->hasPrivilege(PrivilegeConstants::PRIVILEGE_CRM_ORDER_VIEW)) continue;

                self::info("DispatchCustomerSalesMonitorSuccess",[
                    'userId' => $userId,
                    'paramsList' => $pushInfo
                ]);

                \common\library\CommandRunner::run('OkkiAi', 'ProcessCustomerSalesMonitor', [
                    'userId' => $userId,
                    'paramsList' => "'" . json_encode($pushInfo, JSON_UNESCAPED_UNICODE) . "'"
                ]);
            }
            self::info("DispatchCustomerSalesMonitorClientSuccess",[
                'clientId' => $clientId,
            ]);
            // 清理缓存
            $this->cleanUpForClient($clientId);
        }
    }





    public function actionProcessCustomerSalesMonitor($userId, $paramsList)
    {
        // todo 两张报表 暂时不用设置内存
        $startTime = date('Y-m-d H:i:s', time());
        self::info("ProcessCustomerSalesMonitorBegin",[
            'time' => $startTime
        ]);
        $paramsList = json_decode($paramsList, true);
        User::setLoginUserById($userId);
        $user = User::getLoginUser();
        $agent = new \common\library\ai_agent\ReportInterpretAiAgent($user->getClientId(), $user->getUserId());
        $agent->setDruRun(0);
        $agent->customerSalesMonitor($paramsList);

        $endTime = date('Y-m-d H:i:s', time());
        self::info("ProcessCustomerSalesMonitorEnd", [
                'time' => $endTime
            ]
        );
    }

    public function actionTestChannel()
    {
        self::info("processing...",[
            'time' => date("Y-m-d H:i:s")
        ]);
        sleep(10);
        self::info("sleep over...");
    }


    public function actionDispatchTestChannel()
    {
        for ($i = 0; $i < 100; $i++) {
            \common\library\CommandRunner::run('OkkiAi', 'TestChannel', [
                'i' => $i
            ]);
        }
    }

    /**
     * 调整向量化方法重新刷新向量库向量值
     * ./yiic-test okkiAi refreshCommonVector --doc_ids=1100
     * @return void
     */
    public function actionRefreshCommonVector($dryRun = 1, $doc_ids = '', $model = EmbeddingService::MODEL_TYPE_BGE_M3)
    {
        if ($doc_ids) {
            $docIds = is_array($doc_ids) ? $doc_ids : explode(',', $doc_ids);
        }else {
            // 获取数据助理的向量库docIds
            $commonVectorDocumentListPdo = new \common\library\ai_agent\vector\CommonVectorDocumentList();
            $commonVectorDocumentListPdo->setOrderBy('create_time');
            $commonVectorDocumentListPdo->setOrder('desc');
            $commonVectorDocumentList = $commonVectorDocumentListPdo->find();
            // 数据助理的文档类型都是csv，且已检查其他非数据助理文档文档类型不是csv
            $commonVectorDocumentList = array_filter($commonVectorDocumentList, function ($item) {
                return $item['doc_type'] == 2;
            });
            $docIds = array_column($commonVectorDocumentList, 'doc_id');
        }
        // 获取文档text，进行向量化
        $commonVectorDetailListPdo = new \common\library\ai_agent\vector\CommonVectorDetailList();
        $commonVectorDetailListPdo->setDocId($docIds);
        $commonVectorDetailListPdo->setFields(['doc_id', 'detail_id', 'text']);
        $commonVectorDetailList = $commonVectorDetailListPdo->find();
        $chunk = array_chunk($commonVectorDetailList, 200);
        var_dump("count chunk:". count($chunk));
        foreach ($chunk as $index => $detailList) {
            $updatedVectorList = [];
            foreach ($detailList as $detail) {
                $docId = $detail['doc_id'];
                $detailId = $detail['detail_id'];
                $text = $detail['text'];

                // 生成向量，更新tbl_vector
                $embeddingService = new EmbeddingService();
                $embeddingService->setTrace('CRM', 'CommonVectorService');
                $vector = $embeddingService->embed($text, $model);
                $vector = json_encode($vector);
                $updatedVectorList[] = " WHEN detail_id = {$detailId} AND doc_id = {$docId} THEN '{$vector}' ";
            }

            $caseWhenSql = implode(' ', $updatedVectorList);
            $docIdsStr = implode(',', $docIds);
            $detailIdsStr = implode(',', array_column($detailList, 'detail_id'));

            $updateSql = "UPDATE tbl_vector SET model_type = {$model}, vector = CASE {$caseWhenSql} ELSE vector END  where doc_id IN ({$docIdsStr}) AND detail_id IN ({$detailIdsStr}) ";

            if (!$dryRun) {
                $db = CommonVector::model()->getDbConnection();
                $rows = $db->createCommand($updateSql)->execute();
                self::info('RefreshCommonVector', ['detailIds' => $detailIdsStr, 'affectRow' => $rows, 'needUpdatedRow' => count($detailList)]);
                $db->setActive(false);
            }
            var_dump("finish chunk {$index}");
            sleep(60); //解决ai-connector限流
        }
        echo "finish";
    }

    public function actionCheckVectorAfterRefresh($docId, $text, $limit=3, $model = EmbeddingService::MODEL_TYPE_BGE_M3)
    {
        $start = microtime(true);
        $service = new CommonVectorService();
        $commonVectorInfos = $service->search($text, [$docId], $limit,  $model);
        $end = microtime(true);
        $costTime = $end - $start;
        var_dump($commonVectorInfos);
        var_dump("cost_time:{$costTime}");
    }
}