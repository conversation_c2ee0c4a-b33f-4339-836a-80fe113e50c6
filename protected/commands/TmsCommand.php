<?php

use common\library\facebook\page\FacebookService;
use common\library\import\Import;
use common\library\privilege_v3\PrivilegeConstants;
use common\library\privilege_v3\PrivilegeService;
use common\library\speechcraft\SpeechcraftImportExecutor;
use common\library\trade_document\page\PageAPI;
use common\library\trade_document\page\user\Page;
use xiaoman\orm\exception\OrmException;
use xiaoman\orm\exception\QueryException;

class TmsCommand extends CrontabCommand
{
    /**
     * @var CDbConnection
     */
    protected $clientDb;

    /**
     * 生成pdf（全部client）
     * @param string $startTime 只生成指定日期之后发布的文档
     * @throws CDbException
     * @throws ProcessException
     */
    public function actionGeneratePdf(string $startTime = '')
    {
        $clients = $this->getClientList(0, true, 1); // 全量
        $clientIds = array_column($clients, 'client_id', '');
        unset($clients);

        $startTime = date('Y-m-d H:i:s', strtotime($startTime));
        foreach ($clientIds as $client_id) {
            LogUtil::info("begin client: $client_id");
            $this->clientDb  = ProjectActiveRecord::getDbByClientId($client_id);
            try {
                $this->actionGeneratePdfForClient($client_id, $startTime);
            } catch (Throwable $exception) {
                LogUtil::error("Error: client_id=$client_id, ". $exception->getMessage());
                continue;
            }
        }
    }

    /**
     * 生成pdf（单个client）
     * @param int $client_id
     * @param $startTime
     * @throws ProcessException
     * @throws OrmException
     */
    public function actionGeneratePdfForClient(int $client_id, $startTime)
    {
        $adminUserId = PrivilegeService::getInstance($client_id)->getAdminUserId();
        if (!$adminUserId) {
            return;
        }
        User::setLoginUserById($adminUserId);
        $params = [
            'update_time_s' => $startTime,
            'is_accessible' => 1
        ];
        $pageApi = new PageAPI($client_id, $adminUserId);
        $pages = $pageApi->pageList($params);
        foreach ($pages['list'] as $page) {
            /**
             * 只需要处理在线文档，PDF、图片、PPT等类型不需要处理
             */
            if ($page['is_released'] && ((int)$page['kind']) === \common\library\trade_document\Constants::PAGE_KIND_ONLINE_DOC) {
                LogUtil::info("add to queue client: $client_id, page_id: {$page['page_id']}");
                $page = new Page($client_id, $page['page_id']);
                $pageApi->genPdfAsync($page);
            }
        }
    }

    /**
     * 修复PDF类型文档的下载地址（全部client）
     * @param string $startTime 只生成指定日期之后发布的文档
     * @throws CDbException
     * @throws ProcessException
     */
    public function actionFixPdfUrl(string $startTime = ''): void
    {
        $clients = $this->getClientList(0, true, 1); // 全量
        $clientIds = array_column($clients, 'client_id', '');
        unset($clients);

        $startTime = date('Y-m-d H:i:s', strtotime($startTime));
        foreach ($clientIds as $client_id) {
            LogUtil::info("begin client: $client_id");
            $this->clientDb  = ProjectActiveRecord::getDbByClientId($client_id);
            try {
                $this->actionFixPdfUrlForClient($client_id, $startTime);
            } catch (Throwable $exception) {
                LogUtil::error("Error: client_id=$client_id, ". $exception->getMessage());
                continue;
            }
        }
    }

    /**
     * 修复PDF类型文档的下载地址（单个client）
     * @param int $client_id
     * @param $startTime
     * @return void
     * @throws OrmException
     * @throws ProcessException
     * @throws QueryException
     */
    public function actionFixPdfUrlForClient(int $client_id, $startTime): void
    {
        $adminUserId = PrivilegeService::getInstance($client_id)->getAdminUserId();
        if (!$adminUserId) {
            return;
        }
        User::setLoginUserById($adminUserId);
        $params = [
            'create_time_s' => $startTime,
            'is_accessible' => 1
        ];
        $pageApi = new PageAPI($client_id, $adminUserId);
        $pages = $pageApi->pageList($params);
        foreach ($pages['list'] as $page) {
            if (((int)$page['kind']) !== \common\library\trade_document\Constants::PAGE_KIND_PDF) {
                continue;
            }
            $sourcePdfUrl = $page['latest_version_info']['modules']['file_url'] ?? null;
            if (empty($sourcePdfUrl)) {
                continue;
            }
            if ($page['pdf_file_url'] !== $sourcePdfUrl) {
                // 需要修正
                $pageObj = new Page($client_id, $page['page_id']);
                $pageObj->pdf_file_url = $sourcePdfUrl;
                $pageObj->pdf_file_id =  $page['latest_version_info']['modules']['file_id'] ?? 0;
                \LogUtil::info("document reset pdf url page_id: {$page['page_id']}, from {$page['pdf_file_url']} to url: {$pageObj->pdf_file_url}");
                $pageObj->update(['pdf_file_id', 'pdf_file_url']);
            }
        }
    }

    /**
     * @throws CDbException
     */
    public function actionUpdateDocReleasedStatus($clientId, $grey = 1, $greyNum = null)
    {
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey && !is_null($greyNum)) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        $clientIds = array_values($clientIds);
        foreach ($clientIds as $clientId) {
            $sql = <<<SQL
update tbl_cms_site_page set latest_is_released = IFNULL(JSON_EXTRACT(data, '$.latest_is_released'), 0)
where client_id = :client_id and JSON_VALID(data) and JSON_TYPE(data) = "OBJECT";
SQL;

            ProjectActiveRecord::getDbByClientId($clientId)->getCommandBuilder()
                ->createSqlCommand($sql, [':client_id' => $clientId])->execute();
        }
    }

    /**
     * 批量导入话术
     */
    public function actionImportSpeechcraft(int $user_id, int $import_id): void
    {
        $beginTime = microtime(true);
        self::info('begin: user_id:'.$user_id.' import_id:'.$import_id);

        \User::setLoginUserById($user_id);
        $user = \User::getLoginUser();

        $import = new Import($user->getClientId(), $import_id);

        $importExecutor = new SpeechcraftImportExecutor($import);
        $importExecutor->run();

        $endTime = microtime(true);

        self::info('finish: user_id:' . $user_id . ' import_id:' . $import_id . ' time:' . ($endTime - $beginTime));
    }


    /**
     * @throws CDbException
     */
    public function actionInsertNewTradeModule()
    {
        $sql = <<<SQL
    INSERT INTO tbl_trade_module (module_key, categories, data, name, remark, preview_image, enable_flag) VALUES ('universal_cover', '["5"]', '{
    "key": "universal_cover",
    "name": "通用封面",
    "type": "container",
    "children": [
        {
            "type": "component",
            "field": "title",
            "label": "标题",
            "componentType": "rich-text"
        },
        {
            "type": "component",
            "field": "subtitle",
            "label": "二级标题",
            "componentType": "rich-text"
        },
        {
            "type": "component",
            "field": "layout",
            "label": "排版",
            "rules": {
                "layout": "required|in:0,1"
            },
            "attributes": {
                "options": [
                    {
                        "icon": "layout-up",
                        "label": "上图下文",
                        "value": 0
                    },
                    {
                        "icon": "layout-down",
                        "label": "上文下图",
                        "value": 1
                    }
                ]
            },
            "componentType": "radio-icon"
        },
        {
            "type": "component",
            "field": "date",
            "label": "日期",
            "componentType": "rich-text"
        },
        {
            "type": "component",
            "field": "company_name",
            "label": "公司名称",
            "componentType": "rich-text"
        },
        {
            "type": "component",
            "field": "logo",
            "label": "公司LOGO",
            "attributes": {
                "width": 200,
                "height": 200
            },
            "componentType": "image"
        },
        {
            "type": "component",
            "field": "background",
            "label": "背景图",
            "attributes": {
                "width": 1200,
                "height": 1200
            },
            "componentType": "image"
        }
    ],
    "default_data": {
        "layout": 0,
        "date": "<span><b>Document Time</b></span>",
        "logo": {
            "id": 0,
            "url": "https://jinyuncrmdevelop.oss-cn-hangzhou.aliyuncs.com/pictures/Vector_20220614105140_cdk9o.png"
        },
        "title": "<span><b>Document Title</b></span>",
        "subtitle": "<span>Subtitle</span>",
        "background": {
            "id": 0,
            "url": "https://jinyuncrmdevelop.oss-cn-hangzhou.aliyuncs.com/pictures/封面图_20220624134807_0l57d.png"
        },
        "company_name": "<span><b>Company Name</b></span>"
    },
    "containerType": "module"
}', '通用封面', '通用的文档封面，图文单独显示，信息更聚焦', 'https://jinyuncrmdevelop.oss-cn-hangzhou.aliyuncs.com/pictures/封面图_20220624134807_0l57d.png', 1);
SQL;

        $ret = TradeModule::model()->getDbConnection()->createCommand($sql)->execute();
        $ret && LogUtil::info("insert trade module success");
    }

    //更新文档关联环节
    public function actionRefreshDocumentLinks($clientId = 0, $grey = 1, $greyNum = null)
    {
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey && !is_null($greyNum)) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        $samples = SystemConfig::config(SystemConfig::TMS_SAMPLE_DOCUMENTS);
        $samples = json_decode($samples ?: '[]', true);

        foreach ($samples as $key => $sample) {
            $sysPage = new \common\library\trade_document\page\system\TradeTemplate();
            $sysPage->loadById($sample['id']);
            if ($sysPage->isNew() || !$sysPage->isEnable()) {
                continue;
            }
            $sysPage->getFormatter();
            $attributes = $sysPage->getAttributes();
            $samples[$key]['link_ids'] = $attributes['link_ids'] ?? [];
        }

        foreach ($clientIds as $clientId) {

            try {
                $admin = PrivilegeService::getInstance($clientId)->getAdminUserId();
                User::setLoginUserById($admin);

                $client = \common\library\account\Client::getClient($clientId);

                if(!$client->getExtentAttribute(\common\library\account\Client::EXTERNAL_KEY_SALES_GUIDE_SWITCH)){
                    continue;
                }

                $db = \ProjectActiveRecord::getDbByClientId($clientId);

                foreach ($samples as $sample) {

                    $sql = "select page_id,template_page_id,name from tbl_cms_site_page where client_id=:client_id and template_page_id =:template_page_id and name=:name and link_ids='';";

                    $row = $db->createCommand($sql)->queryRow(true, [':client_id'=>$clientId,':template_page_id'=>$sample['id'],':name'=>$sample['name']]);

                    if(empty($row)){
                        continue;
                    }

                    // 继承系统模板所选的环节，但是需要将系统的环节ID转为客户的环节ID
                    $api = new PageAPI($clientId, $admin);
                    $linkIds = $api->systemLinkIdsToClientLinkIds($sample['link_ids'] ?? []);

                    $page = new \common\library\trade_document\page\user\Page($clientId, $row['page_id']);
                    $page->link_ids = $linkIds;
                    $page->update();

                    self::info("RefreshDocumentLinks: page_id:{$row['page_id']}, linkIds:".json_encode($linkIds));
                    LogUtil::info("RefreshDocumentLinks: page_id:{$row['page_id']}, linkIds:".json_encode($linkIds));
                }

                self::info("RefreshDocumentLinks:client_id:$clientId complete！\n");
            } catch (\Throwable $t) {
                $log = sprintf('client_id=%s, %s', $clientId, $t->getMessage());
                LogUtil::error($log);
                echo $log . "\n";
            }

        }

    }

    /***
     * 临时脚本，数据修复
     * tapd: https://www.tapd.cn/21404721/bugtrace/bugs/view?bug_id=1121404721001070441
     *
     * @param mixed $clientId
     * @param mixed $grey
     * @param $greyNum
     * @return void
     * @throws CDbException
     * @throws CException
     */
    public function actionFixScheduleTitle($clientId = 0, $grey = 1, $greyNum = null, $test = true)
    {
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey && !is_null($greyNum)) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        $scheduleModel = ScheduleModel::model();
        $companyModel = CompanyModel::model();
        $scheduleRepeatModel = ScheduleRepeatModel::model();
        foreach ($clientIds as $clientId) {
            $admin = PrivilegeService::getInstance($clientId)->getAdminUserId();
            if( empty($admin))
                continue;

            User::setLoginUserById($admin);

            $command = $scheduleModel->getDbConnection()->createCommand();
            $command->setText("select * from tbl_schedule where client_id = {$clientId} and refer_type = 4 and title = '跟进客户：undefined'");
            $scheduleList = $command->queryAll();

            $command = $scheduleRepeatModel->getDbConnection()->createCommand();
            $command->setText("select * from tbl_schedule_repeat where client_id = {$clientId} and refer_type = 4 and title = '跟进客户：undefined'");
            $scheduleRepeatList = $command->queryAll();

            $companyIds = array_merge(array_column($scheduleList, 'refer_id'), array_column($scheduleRepeatList, 'refer_id'));
            $companyIds = array_unique($companyIds);
            if (empty($companyIds)) {
                continue;
            }

            $command = $companyModel->getDbConnection()->createCommand();
            $command->setText("select * from tbl_company where client_id = ${clientId} and company_id in (" . implode(',', $companyIds) . ')');
            $companyList = $command->queryAll();
            $companyList = array_column($companyList, null, 'company_id');

            foreach ($scheduleList as $schedule) {
                if (!isset($companyList[$schedule['refer_id']])) {
                    continue;
                }
                $command = $scheduleModel->getDbConnection()->createCommand();
                $command->setText("update tbl_schedule set title = :title where schedule_id = {$schedule['schedule_id']}");

                if ($test) {
                    echo "跟进客户：{$companyList[$schedule['refer_id']]['name']}" . "\n";
                    echo $command->getText() . "\n";
                } else {
                    $command->bindValue(':title', "跟进客户：{$companyList[$schedule['refer_id']]['name']}");
                    $command->execute();
                }
            }

            foreach ($scheduleRepeatList as $scheduleRepeat) {
                if (!isset($companyList[$scheduleRepeat['refer_id']])) {
                    continue;
                }
                $command = $scheduleRepeatModel->getDbConnection()->createCommand();
                $command->setText("update tbl_schedule_repeat set title = :title where repeat_id = {$scheduleRepeat['repeat_id']}");

                if ($test) {
                    echo "跟进客户：{$companyList[$scheduleRepeat['refer_id']]['name']}";
                    echo $command->getText() . "\n";
                } else {
                    $command->bindValue(':title', "跟进客户：{$companyList[$scheduleRepeat['refer_id']]['name']}");
                    $command->execute();
                }
            }

            \common\library\account\Client::cleanCacheMap();
            PgActiveRecord::releaseDbByClientId($clientId);
            ProjectActiveRecord::releaseDbByClientId($clientId);
            User::cleanUserMap();
        }
    }

    /**
     * 新增权限 - 社媒管理-分配
     * @param int $clientId
     * @param int $grey
     * @param int $greyNum
     */
    public function actionInitAssignPermission($clientId = 0, $grey = 0, $greyNum = 0)
    {
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }
        $total = 0;

        foreach ($clientIds as $clientId) {
            $privilegeService = PrivilegeService::getInstance($clientId);

            $admin = PrivilegeService::getInstance($clientId)->getAdminUserId();
            User::setLoginUserById($admin);

            $privilegeService->getUserPrivilege()->getRoles()->assignPrivilege(
                $privilegeService->getRoleIdByName(PrivilegeConstants::ROLE_NAME_OF_SYSTEM_ADMIN),
                PrivilegeConstants::PRIVILEGE_SCOPE_OWNER,
                [PrivilegeConstants::PRIVILEGE_CRM_SOCIALMEDIA_ASSIGN],
            );
            $total++;
            self::info("InitAssignPermission:client_id:$clientId complete！\n");
        }
        self::info("InitAssignPermission complete :total:$total");
    }

    public function actionInitFacebookPageAssignUsers($clientId = 0, $grey = 0, $greyNum = 0, $inCrontab = 0)
    {
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $sql = 'select distinct client_id from tbl_facebook_page';
            !empty($inCrontab) && $sql .= " where create_time > '" . date('Y-m-d H:i:s', strtotime('-10 minute')) . "'";

            $list = FacebookPage::model()->getDbConnection()->createCommand($sql)->queryAll();
            $clientIds = array_column($list, 'client_id');
        }
        $total = 0;

        foreach ($clientIds as $clientId) {
            $privilegeService = PrivilegeService::getInstance($clientId);

            $admin = PrivilegeService::getInstance($clientId)->getAdminUserId();
            User::setLoginUserById($admin);

            $userIds = $privilegeService->getUserIdsByPrivileges(PrivilegeConstants::PRIVILEGE_CRM_SOCIALMEDIA_VIEW);
            if (!empty($userIds)) {
                $sql = 'select page_id from tbl_facebook_page where client_id = :client_id';
                $pages = FacebookPage::model()->getDbConnection()->createCommand($sql)->queryAll(true, [':client_id' => $clientId]);
                if (empty($pages)) {
                    continue;
                }

                $facebookService = FacebookService::getInstance($clientId);
                $pageUsers = $facebookService->getPageAssignUserIds($clientId, array_column($pages, 'page_id'));
                foreach ($pages as $page) {
                    if (!empty($pageUsers[$page['page_id']])) {
                        continue;
                    }
                    $facebookService->assignPage($page['page_id'], $userIds);
                }
            }
        }
        self::info("actionInitFacebookPageAssignUsers complete :total:$total");
    }

    //修复话术建议为空
    public function actionRefreshTmsData($clientId = 0, $grey = 1, $greyNum = null, $tryRun=0)
    {
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey && !is_null($greyNum)) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        $samples = SystemConfig::config(SystemConfig::TMS_SAMPLE_DOCUMENTS);
        $samples = json_decode($samples ?: '[]', true);

        foreach ($samples as $key => $sample) {
            $sysPage = new \common\library\trade_document\page\system\TradeTemplate();
            $sysPage->loadById($sample['id']);
            if ($sysPage->isNew() || !$sysPage->isEnable()) {
                continue;
            }
            $sysPage->getFormatter();
            $attributes = $sysPage->getAttributes();
            $samples[$key]['link_ids'] = $attributes['link_ids'] ?? [];
        }

        foreach ($clientIds as $clientId) {

            try {
                $admin = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();
                User::setLoginUserById($admin);

                $client = \common\library\account\Client::getClient($clientId);

                $privilegeService = PrivilegeService::getInstance($clientId);
                $systemId = $privilegeService->getMainSystemId();

                $switch = $client->getExtentAttribute(\common\library\account\Client::EXTERNAL_KEY_SALES_GUIDE_SWITCH);

                //在TMS售卖版本中的
                if(!$switch || !in_array($systemId,PrivilegeConstants::TMS_ALLOW_SYSTEMS)){
                    continue;
                }
                $pgDb = \PgActiveRecord::getDbByClientId($clientId);

                $tipSql = "select tips_id from tbl_tms_tips where client_id = {$clientId} and system_tips_id !=0 limit 1";

                $row = $pgDb->createCommand($tipSql)->queryRow();

                if (!empty($row)) {
                    continue;
                }

                if(!$tryRun){
                    //先移除权限重新添加
                    $privilegeService->removeFunction([
                        PrivilegeConstants::FUNCTIONAL_SPEECHCRAFT,
                        PrivilegeConstants::FUNCTIONAL_DOCUMENT,
                        PrivilegeConstants::FUNCTIONAL_SUGGESTION,
                    ]);

                    $privilegeService->assignFunction([
                        PrivilegeConstants::FUNCTIONAL_SPEECHCRAFT,
                        PrivilegeConstants::FUNCTIONAL_DOCUMENT,
                        PrivilegeConstants::FUNCTIONAL_SUGGESTION,
                    ]);

                    //更新文档关联环节
                    $db = \ProjectActiveRecord::getDbByClientId($clientId);
                    foreach ($samples as $sample) {

                        $sql = "select page_id,template_page_id,name from tbl_cms_site_page where client_id=:client_id and template_page_id =:template_page_id and name=:name and link_ids='';";

                        $row = $db->createCommand($sql)->queryRow(true, [':client_id'=>$clientId,':template_page_id'=>$sample['id'],':name'=>$sample['name']]);

                        if(empty($row)){
                            continue;
                        }

                        // 继承系统模板所选的环节，但是需要将系统的环节ID转为客户的环节ID
                        $api = new PageAPI($clientId, $admin);
                        $linkIds = $api->systemLinkIdsToClientLinkIds($sample['link_ids'] ?? []);

                        $page = new \common\library\trade_document\page\user\Page($clientId, $row['page_id']);
                        $page->link_ids = $linkIds;
                        $page->update();

                        self::info("actionRefreshTmsData:client_id:$clientId page_id:{$row['page_id']}, linkIds:".json_encode($linkIds));
                        LogUtil::info("actionRefreshTmsData:client_id:$clientId page_id:{$row['page_id']}, linkIds:".json_encode($linkIds));
                    }

                }

                self::info("actionRefreshTmsData:client_id:$clientId complete！\n");
            } catch (\Throwable $t) {
                $log = sprintf('RefreshTmsData client_id=%s, %s', $clientId, $t->getMessage());
                LogUtil::error($log);
                echo $log  . "\n";
            }

        }

    }

    //修复因没有编辑权限上传文档后没有自动发布的PDF、图片、视频
    public function actionDocumentPublish($clientId = 0, $grey = 1, $greyNum = null, $dryRun = 0)
    {
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        foreach ($clientIds as $clientId) {
            $admin = PrivilegeService::getInstance($clientId)->getAdminUserId();
            if(!$admin){
                continue;
            }
            User::setLoginUserById($admin);

            $db = \ProjectActiveRecord::getDbByClientId($clientId);

            $sql = "select page_id from tbl_cms_site_page where client_id=:client_id and kind IN(2,6,8) and owner = :owner and latest_is_released=:latest_is_released and latest_version_id=:latest_version_id and first_version_id=:first_version_id and enable_flag=:enable_flag and page_id>%d order by page_id limit 200";

            $lastId = 0;
            $count = 0;
            while (true) {
                $data = $db->createCommand(sprintf($sql, $lastId))->queryAll(true, [':client_id'=>$clientId,':owner'=> 0 ,':latest_is_released'=> 0,':latest_version_id'=> 0,':first_version_id'=> 0,':enable_flag'=>1 ]);
                if (!$data) {
                    break;
                }

                foreach ($data as $item) {
                    $page = new Page($clientId, $item['page_id']);
                    $api = new PageAPI($clientId, $admin);
                    !$dryRun && $api->publish($page,true);
                    self::info("DocumentPublish:client_id:{$clientId} page_id:{$item['page_id']}！\n");
                    \LogUtil::info("DocumentPublish:client_id:{$clientId} page_id:{$item['page_id']}！\n");
                }

                $count += count($data);
                $lastId = end($data)['page_id'];

            }

            self::info("DocumentPublish:client_id:{$clientId} total:{$count} complete！\n");
            \LogUtil::info("DocumentPublish:client_id:{$clientId} total:{$count} complete！\n");

        }
    }


}
