<?php

use common\library\account\Client;
use common\library\cash_collection\CashCollection;
use common\library\cash_collection\CashCollectionBatchOperator;
use common\library\cash_collection\CashCollectionList;
use common\library\customer\CompanyBatchOperator;
use common\library\invoice\batch\OrderBatchOperator;
use common\library\invoice\OrderList;
use common\library\invoice\status\InvoiceStatusService;
use common\library\opportunity\OpportunityList;
use common\library\performance_v2\filter\RuleConfig;
use common\library\performance_v2\Helper;
use common\library\performance_v2\PerformanceV2Constant;
use common\library\performance_v2\record\PerformanceV2RecalculateTask;
use common\library\performance_v2\record\PerformanceV2RecordList;
use common\library\performance_v2\rule\PerformanceV2Rule;
use common\library\performance_v2\team_wall\KeyBehaviorList;
use common\library\privilege_v3\PrivilegeService;
use common\library\util\PgsqlUtil;
use common\library\workflow\filter\WorkflowFilterRunner;

class PerformanceV2Command extends CrontabCommand
{

    // ./yiic performanceV2 checkRuleName
    public function actionCheckRuleName()
    {
//        $db = PgActiveRecord::getDbByClientId(22197);
//        $sql = "update tbl_performance_v2_rule set name = '成交订单金额' where client_id = 22197 and rule_id = 22305404681";
//        $db->createCommand($sql)->execute();
//        exit;
        $clientIds = array_column($this->getClientList(0, true, true, null, 0, 0, null, 0, 0, 0, true), 'client_id');
        self::info("actionRepairMailReplyRule count:" . count($clientIds));
        $needDealRules = [];
        foreach ($clientIds as $clientId) {
            $db = PgActiveRecord::getDbByClientId($clientId);
            if (empty($db)) continue;
            try {
                $sql = "select * from tbl_performance_v2_rule where client_id = {$clientId} and enable_flag=1
                                      and name like '成交订单金额%' and refer_type = 2 and type = 2 order by rule_id asc";
                $ruleList = $db->createCommand($sql)->queryAll();
                foreach ($ruleList as $rule) {
                    if ($rule['name'] != '成交订单金额') {
                        $needDealRules[$clientId][$rule['rule_id']] = $rule['name'];
                    }
                }
            } catch (\Throwable $t) {
            } finally {
                \common\library\account\Client::cleanCacheMap($clientId);
                PgActiveRecord::releaseDbByClientId($clientId);
            }
        }
        echo json_encode($needDealRules);
    }

    // ./yiic-omg performanceV2 findProductField
    public function actionFindProductField()
    {
        \User::setLoginUserById(765);
        $clientIds = array_column($this->getClientList(0, true, true, null, 0, 0, null, 0, 0, 0, true), 'client_id');
        self::info("actionFindProductField count:" . count($clientIds));
        $fields1 = ['cost_with_tax','fob', 'package_unit', 'package_size','package_volume','package_gross_weight','count_per_package','package_remark','product_size','product_volume','product_net_weight'];
        $fields2 = ['cost','fob_price','fob_type','sku_external_field_data','product_volume', 'product_size_height','product_size_weight','product_size_length','product_net_weight','package_size_height','package_size_length','package_size_weight','package_unit','gradient_price','sku_remark','sku_description','count_per_package','package_remark','package_volume','cost_currency','package_gross_weight','price_max','price_min','price_currency','quantity'];
        $fields  = array_merge($fields1, $fields2);
        $checkFields = [];
        foreach ($fields as $field) {
            $checkFields[] = 'product.'.$field;
        }
        $whichUsers = [];
        $total = count($clientIds);
        foreach ($clientIds as $i => $clientId) {
            self::info("total:".$total.'/'.($i+1).PHP_EOL);
            $clientUserIds = \common\library\account\Helper::getUserIds($clientId);
            if (empty($clientUserIds)) {
                continue;
            }
            try {
                $db = \ProjectActiveRecord::getDbByClientId($clientId);
                $db->setActive(true);
                $userStr = implode(',', $clientUserIds);
                $sql  = "select user_id,`key`,`value` from tbl_user_setting where user_id in ($userStr) and enable_flag = 1 and `key` like 'statistic.analysis.setting%'";
                $list = $db->createCommand($sql)->queryAll();
                foreach ($list as $item) {
                    $settings = json_decode($item['value'], true);
                    foreach ($settings as $settingKey => $setting) {
                        if (isset($setting['field'])) {
                            if (in_array($setting['field'], $checkFields) && !isset($whichUsers[$clientId][$item['user_id']][$setting['field']])) {
                                $whichUsers[$clientId][$item['user_id']][$setting['field']] = 1;
                            }
                        } else {
                            if (in_array($settingKey, $checkFields) && !isset($whichUsers[$clientId][$item['user_id']][$settingKey])) {
                                $whichUsers[$clientId][$item['user_id']][$settingKey] = 1;
                            }
                        }
                    }
                }
            } catch (\Throwable $t) {

            } finally {
                \common\library\account\Client::cleanCacheMap($clientId);
                if (!empty($db)) {
                    \ProjectActiveRecord::setConnection(null);
                    $db->setActive(false);
                }
            }
        }
        var_dump($whichUsers);
        print_r(array_keys($whichUsers));
        self::info("end");
    }

    // 7月1到8月24因为开启规则没有重跑规则问题，全量修复 静态字段 绩效目标
    // php ./yiic-test performanceV2 fixSaveRuleProblem --run_num=2 --run_mod=0
    public function actionFixSaveRuleProblem($run_num = 5, $run_mod = 0)
    {
        if (!is_numeric($run_num) || $run_num <= 0) {
            return;
        }
        if (!($run_mod >= 0 && $run_mod <= ($run_num - 1))) {
            return;
        }
        $clientIds = [
            47508,18617,77301,3474,340526,47959,82812,1092,37005,19567,88725,55328,48655,48415,338731,18303,35477,338419,27611,6534,340126,340030,340545,78945,80708,46785,50517,15294,78403,2118,6033,39190,4107,58912,69044,22830,627,86173,30900,54095,341756,42991,78365,58036,83125,340596,4492,336497,41799,14474,342736,64714,343432,32254,80746,79582,68203,19328,87661,48520,82371,49907,33209,4828,340300,341665,339037,57242,12117,340602,343438,344226,70374,72701,339200,1631,340538,31165,19192,60051,341442,340856,21578,37572,339387,341750,40967,31428,18618,22381,32984,26479,40034,31729,73977,62609,40564,342058,12128,14989,337434,62707,61179,75337,76409,341311,342791,52065,49810,343175,35904,60839,26790,338447,19542,60932,339633,30653,334701,5813,54807,341505,337985,35027,3049,56658,338963,334334,335395,69010,61184,50198,46283,26123,29284,67643,82343,4960,45258,32085,87148,36000,88847,53815,33371,22513,77241,6237,30733,53952,6955,87814,27170,47308,6612,33204,33670,10188,342041,81979,340848,81862,36157,46128,50755,72146,36597,40720,83057,35843,78327,75321,16317,22035,50340,338712,68968,81747,64864,76926,55595,54092,337836,58325,18339,50285,32539,30492,33253,76308,73576,26608,27350,336727,88359,341005,27614,37080,81107,82753,80303,3438,336272,85677,23513,341316,341566,21041,52346,26991,83284,53180,336625,341443,85471,334002,52419,44213,84137,5454,57685,54354,53397,338114,36032,340491,341850,18526,82932,339332,43215,39630,30517,83839,71279,342925,19572,88566,79432,45065,342433,25905,22024,10843,29703,67280,60334,599,82329,82906,343756,3710,344740,48427,40312,79207,13815,8679,681,341149,37204,19453,23427,25360,46568,27524,64408,2395,27499,32231,341007,33410,50492,340414,72973,56835,73740,337438,4498,82242,36836,31769,341546,54951,19506,343381,2884,45970,343852,81305,47640,35461,70771,66409,338810,38790,78347,69002,10509,341461,8149,40272,48043,2189,46673,336654,67147,52924,79451,58160,42952,84216,1972,75266,66870,48862,29976,85736,33008,75923,61182,343890,342788,82007,51613,78163,69101,82212,77130,340562,8834,49302,334299,80176,341615,36076,84273,59681,86087,84590,36545,35528,2327,335732,77126,71372,85833,58123,39056,15555,344040,42545,63720,61170,73867,31337,342921,341215,339956,32008,1034,336808,342486,53709,86375,1094,38697,336924,18668,36698,37084,14833,342712,51090,36297,22836,22197,80980,28411,337539,68082,62116,27124,82345,21388,14126,37967,10844,343451,6088,87436,338401,42825,30362,3192,31241,78644,37729,79825,59169,61180,343503,81713,341272,83298,60359,340166,51787,68246,340103,25683,79504,341006,61873,25200,74527,342493,339876,78543,43062,341235,343314,1160,66546,8220,16244,75867,78312,62814,338549,56181,334217,28458,1878,66496,75302,19730,59204,80311,87857,342265,17278,52912,82564,70027,8790,78773,32644,49131,336378,5405,64084,31623,336784,342191,81005,44208,28570,339499,50934,41229,70442,33622,34502,80867,18622,52448,74241,64444,342213,337167,44938,17125,69498,70894,83444,4868,48611,27209,25543,32890,71755,75877,343579,81866,35341,61181,2246,49209,88603,340096,28711,20485,3010,39261,21084,337701,51013,334603,13070,338763,342867,44061,71932,75651,78819,335062,42805,341968,35517,33947,31604,33241,64245,57296,340299,76240,34802,6170,1306,70013,4141,335311,23926,28621,82608,30643,342480,67156,58471,40039,70051,334374,87177,31597,25824,53556,27873,523,4939,336280,86348,62716,88915,87617,46615,333521,3326,58358,73387,76804,26884,7195,31585,338765,48414,4832,64816,73723,80710,33001,68884,28794,38702,73677,342812,344427,342822,83836,340863,55766,341328,57114,335165,8632,68727,36144,19901,19758,46879,14260,11723,3214,34466,341594,6322,341551,343528,70792,52891,36762,344197,344077,35825,35929,64000,30873,333396,16120,81571,37370,341647,344316,78229,82206,342139,39672,55429,6623,51406,334438,334073,32156,26603,15664,81301,72439,46746,17168,26409,23356,68736,7579,338876,63342,27670,70384,69508,48383,19744,23774,18137,28407,84797,29721,334475,12225,83498,47107,15377,28599,27663,73812,41029,33063,64423,59776,28564,76790,42362,83465,31254,23722,340609,31764,81954,23486,72149,36730,46597,72718,12137,77165,3248,36319,60832,78839,86966,44657,81140,61603,340678,54175,342126,63980,17050,67648,32815,12546,50381,77850,54304,82680,44174,87829,66582,63327,49354,4184,79915,73031,56364,83819,21672,31237,69497,31016,342803,30062,30494,36166,18351,88374,4485,67035,57425,42534,87532,342460,334379,29435,34582,342577,62236,35790,73344,41426,344323,337120,61177,82145,343045,31752,30381,69401,343410,340502,72920,86710,343370,58668,3045,20294,4769,49303,61185,337614,38145,85950,75636,42186,10263,342874,20315,40329,343208,32197,70978,35678,7986,342240,338299,74589,33284,20972,42250,334279,54264,334892,79538,87435,83925,24034,46107,4282,79457,49320,340129,84746,3,45812,38462,56331,84772,82747,43080,88850,334048,83797,19383,59047,30210,51855,340963,40209,81790,28185,338394,342113,41552,76280,72371,37107,76699,27153,86484,342860,341503,85799,30628,87250,40899,336161,31765,342403,71832,341514,78996,59440,334463,60127,18773,85174,22649,32738,10908,36512,339942,69384,7261,64420,32906,51109,60044,38381,340883,34657,87475,342751,340639,7373,47300,334275,340428,38126,88299,49206,311,342384,342317,339770,31808,75167,341263,46705,338034,84221,21840,18465,344456,586,78402,27606,75313,27026,37608,340625,82174,34471,48143,23304,340321,14492,30406,342118,5034,42284,16251,60350,7386,22020,39572,37994,341747,87823,343657,339656,77749,24018,33036,83755,11987,25418,335355,7317,343098,339959,38069,339148,23116,62312,340430,339646,47109,28338,47266,334392,50170,344029,32949,47281,342938,null,33542,74750,340032,63648,79203,79872,81163,84360,14770,32878,33713,42814,88778,342140,32847,338340,47507,83086,58332,83261,336923,339885,88969,340431,340704,83606,3322,13008,342489,27715,5062,78338,37449,341818,42088,68948,66058,343838,65114,341374,84997,48570,341233,8963,70690,83025,343066,35103,335770,50327,342274,335760,33585,81203,30444,33529,23869,56305,341403,35607,73886,40030,61523,14604,61633,42575,342279,37978,82836,73013,33574,58372,32252,70854,37329
        ];
        // $clientIds = [1, 9650];
        foreach ($clientIds as $clientId) {
            $mod = $clientId % $run_num;
            if ($run_num == 1 || $mod == $run_mod) {
                self::info($clientId . '|start' . PHP_EOL);
                $db = PgActiveRecord::getDbByClientId($clientId);
                if (empty($db)) continue;
                try {
                    $sql = "select * from tbl_performance_v2_rule where client_id = {$clientId} and enable_flag=1 and update_time >= '2023-07-01 00:00:00' and enable_flag=1 and delete_flag=0 order by rule_id asc";
                    $ruleList = $db->createCommand($sql)->queryAll();
                    foreach ($ruleList as $rule) {
                        // 只修复静态字段
                        if (!\common\library\performance_v2\rule\PerformanceV2Rule::checkIsDynamicTimeByReferTypeAndTimeField($rule['refer_type'], $rule['time_field'])) {
                            try {
                                self::info($rule['client_id'] . '|' . $rule['rule_id'] . '|start' . PHP_EOL);
                                // $this->actionRecordPerformanceByRuleId($rule['client_id'], $rule['rule_id'], [], true);
                            } catch (\Throwable $t) {
                                self::info($rule['client_id'] . '|' . $rule['rule_id'] . '|' . $t->getTraceAsString() . PHP_EOL);
                            }
                            self::info($rule['client_id'] . '|' . $rule['rule_id'] . '|end' . PHP_EOL);
                        }
                    }
                } catch (\Throwable $t) {
                    \common\library\account\Client::cleanCacheMap($clientId);
                    PgActiveRecord::releaseDbByClientId($clientId);
                }
            }
            self::info($clientId . '|end' . PHP_EOL);
        }
    }

    // php ./yiic performanceV2 FixPerformanceDataByClient --clientIdsStr=
    // type = 1 动态， type = 2 静态
    public function actionFixPerformanceDataByClient($clientIdsStr = '')
    {
        if (empty($clientIdsStr)) return;

        $clientIds = explode(',', $clientIdsStr);
        self::info("本次执行的clientIds是[$clientIdsStr]");
//        $clientIds = [36079,1972,35372,18768,3892,11813,44938,31399,61633,22036,78312,234,1793,11459,37177,4246,51999,77478,13699,88024,341847,37206,6623,32002,85809,31791,63495,64914,77161,2443,7549,4698,78433,25418,26208,59766,51078,338837,37225,21875,342980,37784,28547,55930,34893,17855,41021,52674,335433,30629,2829,300,32419,24837,17383,338594,10155,20972,77271,336573,6653,337949,45817,59772,22836,31704,50654,20043,75146,88254,70690,16,25457,13096,77229,34593,81178,28738,29033,341436,86375,31201,40078,336882,80867,20927,336021,14126,70894,84894,35568,2679,43787,9543,60127,37947,24559,340222,337434,6252,86240,30340,34802,18526,42861,337200,30137,4193,82984,19209,35093,39382,78573,34468,76240,54306,42575,31012,2873,29878,13254,18617,28834,4846,45941,22604,78930,62895,10509,33600,34189,31793,18687,71088,27097,27157,343805,342629,338583,26604,4830,11335,5321,340663,3,83985,5255,56098,578,57153,2199,12885,32847,4229,59486,40680,81167,30873,47508,35541,79025,64408,344456,30706,25345,24686,33177,78615,339617,4832,86820,9488,82107,339613,7,41937,72484,8837,23565,60871,4355,49227,31197,79418,4687,85655,336278,26561,1133,9534,70670,338053,13286,79966,14499,14836,13866,341778,29976,50826,343223,80176,26884,54092,39733,87683,83604,3248,46981,12344,342126,80329,78314,60809,75735,10272,799,5263,3045,39306,25824,64585,76308,5310,81986,34627,294,12933,78844,60738,35471,57202,30164,17628,339336,50453,36857,340066,54264,72491,23933,59194,61239,41787,71278,55473,1985,78898,52448,8929,11220,22002,51057,51055,341252,37525,62132,87408,55595,77094,23513,72840,70645,4047,1144,68203,55608,1954,47510,25791,85917,21994,37759,29829,26513,39865,50755,27764,338299,339970,83938,47001,69453,31777,45603,12225,52155,7741,72304,77373,26981,80412,79498,335158,4646,81966,337066,87836,74133,339631,4138,763,336771,338084,59320,21815,11437,62240,337675,71303,335230,26890,57134,24051,22138,63095,77850,27020,340669,15621,342580,333374,88198,29208,64939,341677,34680,23928,36545,64864,67527,33670,333521,342736];
        foreach ($clientIds as $clientId) {
            $db = PgActiveRecord::getDbByClientId($clientId);
            if (empty($db)) continue;
            try {
                $sql = "select * from tbl_performance_v2_rule where client_id = {$clientId} and enable_flag=1 order by rule_id asc";
                $ruleList = $db->createCommand($sql)->queryAll();
                foreach ($ruleList as $rule) {
                    try {
                        $this->actionRecordPerformanceByRuleId($rule['client_id'], $rule['rule_id'], [], true);
                    } catch (\Throwable $t) {
                        self::info($rule['client_id'] . '|' . $rule['rule_id'] . '|' . $t->getTraceAsString() . PHP_EOL);
                    }
                    self::info($rule['client_id'] . '|' . $rule['rule_id'] . '|end' . PHP_EOL);
                }
            } catch (\Throwable $t) {
            } finally {
                \common\library\account\Client::cleanCacheMap($clientId);
                PgActiveRecord::releaseDbByClientId($clientId);
            }
        }
    }

    //  重跑对方回复数，我方回复数规则
    // ./yiic performanceV2 repairMailReplyRule
    public function actionRepairMailReplyRule()
    {
        $clientIds = array_column($this->getClientList(0, true, true, null, 0, 0, null, 0, 0, 0, true), 'client_id');
        self::info("actionRepairMailReplyRule count:" . count($clientIds));
        // $clientIds = [81345]; // 线上问题工单企业
        foreach ($clientIds as $clientId) {
            $db = PgActiveRecord::getDbByClientId($clientId);
            if (empty($db))continue;
            $sql = "select rule_id from tbl_performance_v2_rule where client_id = {$clientId} and refer_type = 6 and target_field in ('reply_from_mail_count', 'reply_to_mail_count') and enable_flag = 1";
            $ruleIds = $db->createCommand($sql)->queryColumn();
            if (empty($ruleIds)) {
                continue;
            }
            self::info("actionRepairMailReplyRule start_client_id:" . $clientId);
            foreach ($ruleIds as $ruleId) {
                try {
                    $this->actionRecordPerformanceByRuleId($clientId, $ruleId, [], true);
                    self::info("actionRepairMailReplyRule 跑成功:" . $clientId . "|||||{$ruleId}");
                } catch (\Throwable $t) {
                    self::info("actionRepairMailReplyRule 跑失败:" . $clientId . "|||||{$ruleId}|||||" . $t->getTraceAsString());
                }
            }
            self::info("actionRepairMailReplyRule end_client_id:" . $clientId);
        }
    }

    // ./yiic performanceV2 repairRule
    public function actionRepairRule()
    {
//        $filters = '[{"unit": "", "field": "collect_status", "value": 1, "operator": "=", "filter_no": 1, "field_type": "3", "refer_type": "10"}, {"unit": "", "field": "create_type", "value": 1, "operator": "=", "filter_no": 2, "field_type": "3", "refer_type": "10"}]';
//        $ruleId = 7844980252866;

        $clientId = 28956;
        $pgDb = \PgActiveRecord::getDbByClientId($clientId);
        \PgActiveRecord::setConnection($pgDb);
        $filters = '[{"unit": "", "field": "collect_status", "value": 1, "operator": "=", "filter_no": 1, "field_type": "3", "refer_type": "10"}, {"unit": "", "field": "create_type", "value": [1, 2], "operator": "in", "filter_no": 2, "field_type": "3", "refer_type": "10"}]';
        $ruleId = 7844980252866;
        $sql = "update tbl_performance_v2_rule set filters='{$filters}' where client_id = {$clientId} and rule_id = {$ruleId}";
        $pgDb->createCommand($sql)->execute();
    }

    // 这里新建client时候会用来初始化，请勿修改
    public function actionPerformanceInitByClientId($clientId)
    {
        common\library\performance_v2\Helper::initClientPrepareRuleByClient($clientId);
        common\library\performance_v2\Helper::initClientTeamWallSetting($clientId);
    }

    // 修改原来邮件打开次数统计绩效规则，并重跑数据
    // ./yiic-test performanceV2 fixEdmViewUcount
    public function actionFixEdmViewUcount()
    {
        $clientIds = array_column($this->getClientList(0, false, null, null, 0, 0), 'client_id');
        //
        // $clientIds = [19506]; // 线上问题工单企业
        foreach ($clientIds as $clientId) {
            $db = PgActiveRecord::getDbByClientId($clientId);
            if (empty($db))continue;

            $sql = "select * from tbl_performance_v2_rule where client_id = {$clientId} and refer_type = 21 and target_field = 'view_count'";
            $ruleInfos = $db->createCommand($sql)->queryAll();
            if (empty($ruleInfos)) {
                continue;
            }
            echo "start_client_id:" . $clientId . PHP_EOL;
            // 已经删除，禁用的修复字段，启用的修复字段并重跑规则
            $updateFieldRuleIds = [];
            $runPerformanceRuleIds = [];
            foreach ($ruleInfos as $ruleInfo) {
                $updateFieldRuleIds[] = $ruleInfo['rule_id'];
                // 启用的
                if ($ruleInfo['enable_flag']) {
                    $runPerformanceRuleIds[] = $ruleInfo['rule_id'];
                }
            }
            if (!empty($updateFieldRuleIds)) {
                $updateRuleIdStr = implode(',', $updateFieldRuleIds);
                $updateSql = "update tbl_performance_v2_rule set target_field='view_ucount' where client_id={$clientId} and refer_type = 21 and target_field = 'view_count' and rule_id in ({$updateRuleIdStr})";
                $db->createCommand($updateSql)->execute();
            }
            if (!empty($runPerformanceRuleIds)) {
                foreach ($runPerformanceRuleIds as $ruleId) {
                    [$exec, $output, $return] = \common\library\CommandRunner::run(
                        'PerformanceV2',
                        'recordPerformanceByRuleId',
                        [
                            'clientId' => $clientId,
                            'ruleId' => $ruleId,
                        ]
                    );
                    echo $clientId . '|' . $ruleId . '|result:' . (($return == 0) ? '入队成功' : '入队失败').PHP_EOL;
                }
            }
            echo "end_client_id:" . $clientId . PHP_EOL;
        }
    }


    // ./yiic performanceV2 runDefaultOrderPerformanceRule
    public function actionRunDefaultOrderPerformanceRule()
    {
        // $clientIds = array_column($this->getClientList(), 'client_id');
        $clientIds = [
            336746,344864,344783,345721,338996,79369,345595,345035,58442,344885,341163,345751,345337,345134,345380,344897,345461,343521,343939,344068,345234,343670,334623,345411,342047,344763,88574,70442,86173,35422,69318,1094,1109,4905,4317,16317,9845,10263,17125,17421,22338,78839,79190,9044,14833,30562,19540,18773,21084,18303,8571,8603,8595,8573,8557,8558,8602,8574,8607,8583,8609,8559,8600,8592,8585,8577,8568,4,8564,8580,8575,84102,8579,8591,8569,8582,18234,19854,3805,8576,8593,8587,8605,8604,8588,8565,8578,8596,8599,8566,8590,8594,8606,8589,8608,8584,8598,8572,8597,8586,16838,8567,8570,8610,20722,8601,20723,8581,17398,67874,26608,26603,26790,26884,27873,29703,30098,29165,29393,63085,32614,30873,28411,58372,31029,1932,30644,62609,52346,14474,31388,32539,336340,48043,55429,36444,37967,34452,55401,29851,35528,31478,31597,17050,37146,45830,48415,46913,30464,70119,42922,33371,39306,42528,48655,36698,42575,55249,33008,80850,80980,42179,42236,69041,54234,42534,3248,3326,3539,3474,3322,3156,3359,3192,87567,344230,344136,344174,89035,339652,344225,344254,344116,342845,344076,340461,344217,344029,334382,340012,344265,333729,344258,336543,342404,344272,341613,342446,344223,344112,333830,341948,337483,337310,344200,339606,339506,344013,340747,344170,336837,83383,341478,343147,344038,344256,341289,342045,342029,344176,344177,342177,87391,343381,342173,87434,87596,87436,84757,344543,345169,345482,345581,345588,83423,345547,345462,345371,344531,345136,345118,344503,344003,344477,345589,343978,345208,344899,345313,342134,345671,345367,345604,344133,345288,345049,345696,85693,343236,345660,339137,345697,345465,340994,84888,345477,345657,345081,345453,339602,345111,344639,344481,344913,344893,345372,345451,54951,342621,344051,343847,343338,344310,344236,344132,334820,343471,61749,341266,344364,342624,343720,343431,341966,344414,344420,344422,344423,344425,344426,344427,344428,344140,343456,343674,344192,344277,334189,344163,343813,337773,344331,344326,340377,334644,340955,344226,344413,344454,344241,343717,344318,343637,344339,342462,343207,64245,339242,341909,339320,84358,339470,81229,341566,85807,343005,343076,342722,342515,341971,343204,342767,343110,341952,342027,342340,341226,341842,334427,337870,342778,343163,339190,342469,338914,335947,339784,333948,343191,342470,343213,340590,343167,343137,342765,342412,343289,341792,339007,87407,340265,340737,340076,343101,343206,341545,342812,72701,343548,341888,343199,343329,343469,341756,343361,343486,343026,343507,342557,343545,343441,343417,337869,341276,342607,341870,89123,342157,42163,340307,342479,343451,343473,343519,343312,342654,343544,343460,339972,337461,343413,333567,342485,336923,339938,341810,341947,343257,343554,87286,343557,343514,42440,343580,342126,336692,342360,342464,341447,342440,341832,339682,339226,339899,340390,341451,337307,342426,342347,341970,342657,342494,342298,341838,342519,342802,340394,342775,342293,342795,342397,60125,342748,342678,340725,341974,54790,341101,339700,342946,342947,342405,341193,342936,342948,342391,341735,342606,342940,342080,337398,341424,335391,75167,342712,53952,66958,338578,333486,344228,344333,342531,81394,344407,344457,343020,338305,340045,344581,338374,342094,344432,337402,340395,341784,343282,340707,341910,344316,341527,344556,344355,344208,344452,344614,338053,344343,343836,88886,336638,339515,340813,344461,341703,341036,344640,344550,342117,343831,87858,344027,344608,77097,48740,343391,341958,340959,340112,344603,84746,340232,340226,66496,48570,85805,338079,344918,341604,344533,338862,341849,344957,344216,341617,344237,343475,338072,344902,82921,344030,344968,342536,344220,344778,339432,343077,341353,341941,341718,344061,343759,341912,344204,344662,344991,342422,343328,344767,342463,344421,344636,342669,339368,345007,341673,337270,344910,344856,343675,344083,344475,344912,344793,343724,341644,341840,338065,336865,84557,340093,336171,342433,79603,343251,342076,342250,343438,343407,339888,76186,343414,343457,342910,339101,340208,343305,83959,342830,343408,343210,342113,343342,87943,342688,343022,339417,340741,343523,339563,343619,340712,79638,340601,343529,340191,342192,343622,343621,342416,343590,342414,88577,342677,343601,342582,334267,67530,24034,341311,341852,341945,88678,83125,83776,341218,82410,62934,72973,70013,68246,60932,66444,53556,334002,78342,78365,338394,337660,32317,339739,342242,340063,343219,341015,340768,340717,340438,338085,343240,341357,342788,343318,343304,342971,343293,343303,342625,79200,342618,342239,343275,342373,336410,343239,341239,338015,343229,341499,342559,343263,343202,85404,341659,341800,87219,333829,336682,338948,343299,81759,340904,341878,88152,334126,342516,342737,342073,343208,64864,52912,78773,36597,84216,82329,83163,334905,340543,57488,337163,88151,53398,342595,339548,342764,342269,341491,341284,341058,342563,342808,40910,342841,341844,342599,342814,337908,342611,338825,342807,342839,342459,341663,342891,338922,338719,342881,341024,342258,342203,342407,341864,342679,337675,336727,342880,342766,342803,342421,341697,342108,342355,342554,342378,341847,340748,342580,341986,341946,342627,74241,64238,335311,335151,344689,85059,337717,343990,344619,344087,344231,340155,341648,342017,344595,344353,344622,344293,340202,337463,344563,344470,344705,340976,344698,343330,339280,344621,344547,344512,344354,337252,344508,344641,337119,344302,343237,344483,54136,344342,344785,69842,340054,86300,344790,87169,339653,344710,344404,344702,341133,56964,81163,344741,344400,339161,339975,344836,343615,87549,342431,339735,53788,344673,342230,344866,333481,344731,343659,341003,342001,335752,340081,344626,342120,46804,340316,344644,344085,344429,344876,342448,341646,344670,341181,338235,344672,341004,344417,343037,343576,343753,344625,342555,344891,344349,344916,344813,343437,344451,344099,344719,341450,48379,77478,85471,339502,340257,340733,341714,341868,338220,342321,79665,339922,342075,335472,341384,341454,341189,340260,342016,342553,338109,342581,342590,342345,342374,341831,342128,342254,340729,342456,342623,342248,342626,342525,342641,341306,342232,342447,336159,342534,342481,342465,341472,334556,342603,342304,340502,342480,340433,342103,342614,64124,344585,344578,342701,343107,334626,71047,340999,345237,82962,345254,344858,345250,345153,340838,339016,343320,38793,88479,345057,335560,343750,342104,343001,345190,341471,341227,344999,338166,343825,344150,344357,344266,342449,341009,343663,343901,343870,344590,345374,345342,342727,343398,343919,345320,345196,336508,344969,337167,341551,344701,341901,342818,343898,344462,344584,343811,343512,333415,77607,343334,344800,344775,342705,344572,341050,343793,340806,344724,344577,344348,338661,342215,344279,344300,343291,342301,344649,341176,338679,344645,343497,343758,344576,344784,340592,341196,344756,342263,344034,344283,344795,344212,344541,344740,344562,83416,344384,334892,31226,70027,336924,339942,57795,60334,67147,61435,63702,63100,65114,83422,83057,340299,339547,339945,340997,69112,336378,335433,72408,71659,68943,340650,82118,78070,79837,340032,340108,72920,52065,54369,81931,81747,81862,70383,65241,83444,73134,84137,4141,88847,87676,84360,82204,340236,340548,342640,340228,343364,338150,342330,340416,341507,338105,340469,340946,341135,341229,335731,83719,340435,69617,342542,338995,340763,342163,341820,342763,342706,343384,342942,340201,341213,340500,337477,340361,340173,343366,341921,340679,343390,342500,339894,69469,341688,341047,342780,340836,343397,84331,343298,75448,343387,343190,85563,71728,340784,336474,344246,342167,342843,344074,344617,335559,343757,344629,335613,344392,344495,342849,342966,344602,344582,344187,342924,344674,343537,335755,344360,344589,343884,344385,343848,344579,344434,344704,343256,344706,343705,337469,344436,344397,344557,344308,337409,344368,344375,344713,344513,339840,55151,341446,344332,344182,344117,344002,55560,344215,344466,343685,343815,342165,27791,344011,344337,66605,344040,343382,342659,342783,344278,83158,340344,344505,344458,340147,340567,344497,57834,343598,344514,343433,338121,344232,342430,341460,341342,344453,344362,344344,343623,344537,342009,340034,344558,343995,344071,342338,342653,344456,83498,72371,72146,341376,338418,338267,76483,342822,343658,339905,343855,340270,343405,340485,343613,343751,340389,339911,341865,343683,343672,343860,341516,343845,333888,339634,341423,343661,343723,341164,343904,343574,343835,340444,343301,343677,343701,45318,338463,343803,81505,339760,340735,343827,333440,342848,342649,343383,338187,340704,340639,343646,341652,339725,336300,337845,343217,343371,343526,341605,335730,339035,341242,343586,88098,49404,342829,341494,343662,341503,343404,336836,340862,340294,342852,88735,68699,341523,339824,337054,340494,343428,343461,343300,69227,343560,342513,343611,343377,343310,343506,343644,343678,339500,343499,334967,343538,340523,343575,342577,341294,343432,334985,334279,68727,50517,58147,344025,342619,343246,343673,343722,339572,337050,343997,341500,88766,343911,341339,343841,344035,343858,339877,344100,344162,342119,344009,343258,343641,342919,343638,338364,342229,343604,343800,341005,83874,340428,343874,342643,84199,341522,337515,343917,343948,343871,340745,337793,343721,342876,344005,343961,343921,343996,336726,344148,344019,88725,334080,87814,84482,72370,73378,74133,79948,342191,342130,342401,341728,342369,334268,341365,341861,341670,57005,342354,341980,342386,336960,339730,342484,341950,342190,341689,342384,340925,334979,340223,339393,340526,334614,342175,342231,342176,342387,341651,342366,89055,341798,340796,342138,339064,342541,341091,342307,342267,342118,341747,81623,82846,83761,336756,77126,77635,78732,67527,76144,335395,82087,340596,340421,82174,339026,334334,64084,69267,70374,7195,6623,4769,4743,341271,73576,66409,341419,341443,340459,15377,4577,5813,14989,15294,8220,7660,8887,8679,12380,10,15395,11574,12225,23052,21459,52783,20485,20043,20554,20294,25200,49131,18526,66475,69527,26143,26123,29707,26479,27202,27606,28846,28711,58668,60038,29369,30362,71869,30243,28655,87250,86966,30244,28484,49302,32198,32231,31747,31769,27835,81571,81107,333396,32700,32738,33338,71372,34299,60201,55846,37099,32984,33026,37978,337492,36545,38464,33361,76790,78573,38707,36166,50381,77975,41960,49907,43907,18438,18618,18617,18465,41881,46879,86484,51109,84273,337241,334938,82371,70231,343752,342413,343427,340698,83785,342901,343639,338213,343797,336007,342270,343632,342732,342069,341216,343805,340453,340818,338459,338981,341367,342288,341579,341496,342588,342125,77770,341203,343627,337159,336069,339018,343754,341432,343681,339487,343840,343403,341230,343756,341439,342922,341201,343718,343702,343558,343496,343561,343370,345426,345238,345241,340170,344574,345417,80656,345412,345323,340900,344898,345121,4639,345194,345554,340501,345459,345391,345468,345522,344387,344455,344717,345222,345523,345242,344154,344691,345512,345363,342863,345559,345292,345558,345024,345093,86908,345143,345236,344950,345476,345189,344820,345138,340061,344879,68642,345273,345444,343957,338964,343810,343837,343964,343703,340714,340121,343023,342003,341736,343650,343863,343866,343969,343906,343826,19123,343474,339579,343668,343749,341358,343195,342012,343929,337967,343481,341569,343839,343986,339411,87974,343651,343828,336918,342474,343872,343992,342856,344104,339985,343868,336104,343822,343998,343547,343949,343880,87701,85664,339646,75313,81335,343462,343686,342521,341898,342131,343935,343933,343834,343951,343113,343531,339307,342532,342287,343585,343653,337493,343913,337877,343528,340077,342489,343907,340105,342356,340290,341363,342170,342586,342337,341046,343882,341220,343972,334410,342279,340031,343696,338544,342569,343928,340926,341811,342058,343876,340431,341298,343821,343838,341509,341788,51532,335123,340495,342857,343109,342997,340984,334562,333353,342501,336977,85927,343179,341963,340751,343008,342980,336961,343209,339485,333955,342079,340372,341422,343212,340080,341123,89024,334394,47334,343221,343222,337002,343226,88202,343228,343230,343224,338218,80040,337096,339940,343175,342921,333802,341875,342317,337120,16481,66263,342824,342227,342939,87308,342388,342864,342660,342540,342285,341121,342493,340570,342362,342343,342274,55102,341884,340111,342796,340610,342550,342594,342717,342228,339143,341634,82334,341403,342955,342265,342517,342616,342361,340275,83494,341853,342561,342443,341234,58597,342565,342246,340781,340575,41978,341529,342718,342043,342959,341374,72439,339633,339843,337938,342904,342771,340728,342584,342132,342799,341126,342139,339895,340146,342749,342821,340968,342862,336702,339559,341786,341413,342438,85087,342213,338408,342178,336204,340178,342667,342937,342200,338859,342357,78914,342871,342969,342568,70356,342133,342644,342968,342978,341833,342646,339686,342898,342665,342874,342791,342736,340134,341292,82295,342773,334151,342403,339607,342696,343223,340720,343192,343393,337711,343410,340406,340841,343203,343430,343315,342742,338655,343388,343271,340788,340648,343372,339861,339270,339554,342628,342789,342832,342585,87101,338437,343274,343348,338970,54822,337800,341535,343425,341925,342428,83614,342837,343253,342562,340491,343379,337438,342370,73402,337320,343579,339530,342693,69825,337783,342908,342935,342506,343700,339887,343698,342048,342838,87940,333773,341588,343532,342893,343588,343603,339096,343620,343657,342700,83931,342784,339158,343292,48490,343568,343715,341583,343503,338687,336972,342819,341827,82530,340556,339045,341331,343294,341922,338727,342435,342505,68968,69054,58036,339690,339740,88743,87605,86212,341904,340533,342418,342633,339272,339389,341996,341791,341401,342675,342214,342116,342567,342256,340075,342322,342478,342604,334300,57890,342639,342372,28759,342544,63281,342451,342310,342389,341576,85072,342303,342615,342744,78433,342724,337634,342776,342399,341514,50626,338838,341021,342756,73292,341678,342486,340250,341615,342327,340963,62716,82753,83154,333963,341283,341647,340489,58902,64000,50340,79207,339387,339956,83082,82005,82658,78479,78347,78403,54807,66546,66028,10051,72162,337045,81301,345016,345507,345584,345499,345504,345101,345083,345483,88798,344160,345503,343495,345497,343231,342710,345570,345683,345747,345334,345180,345098,87571,340774,76430,337205,75649,345009,343094,344509,344016,344583,344904,85844,343820,341911,344652,344518,340811,334909,339188,344106,343914,344880,345084,344863,345000,344501,345151,340308,337690,344248,341928,344823,344214,343180,345177,345105,343584,342275,334305,343967,340819,344816,83990,343846,343070,343096,345167,342634,345205,344439,343940,343399,344727,345077,342635,344942,344050,87228,50074,343908,333463,338466,344861,344180,344650,344233,345023,339930,343245,344751,344972,344465,344542,334442,344782,336380,344712,57166,340861,344781,345043,344168,345091,345014,345090,343424,343429,344181,343155,344873,344728,344161,341528,83203,342377,345125,344925,344322,336609,344809,345123,344995,342575,345073,336019,334217,71149,339448,15338,16120,75266,344142,344313,344203,340894,80927,340750,341392,340413,341543,342549,339779,344321,343945,344275,344131,342074,344243,343963,340694,343856,344070,341782,339236,344033,83541,82911,342672,341999,344257,344145,342495,343261,343220,62767,343706,344281,341764,343852,344358,343636,343862,343905,344172,344370,344171,344372,344323,341383,343524,336337,57134,33179,342281,88688,336374,10371,342596,335169,339027,342410,342220,343098,80806,343103,342846,342973,335400,340434,342429,342171,341572,341417,341683,342264,337029,342030,342794,341672,343120,342787,79274,342897,342556,340436,87131,341393,343013,343074,342720,342752,75110,340525,339385,341305,342751,342053,343085,340545,68950,343164,341567,84604,84590,339484,340078,342548,336411,340660,335280,342698,342994,342070,342038,48148,340597,79830,342136,88848,342201,336615,342867,52119,339498,342041,343034,340089,342996,343035,343036,340445,343039,343040,343041,34997,343042,343043,343044,343045,343046,343047,343048,343049,343050,343051,343052,343053,343054,343055,343056,343057,343058,343059,342508,342977,342739,342970,342986,342987,342988,342989,342990,342991,342365,342887,334976,339556,342453,342831,336926,62709,342100,88501,84353,342895,341627,342529,342460,342689,340616,51205,341633,342847,342707,339445,337995,338735,333781,342965,342860,343009,342135,76836,343011,338715,342212,342592,342721,342694,340839,342785,342938,342691,88603,46946,343337,343450,342613,341666,343493,342681,342313,340001,339319,343100,343409,335554,342376,338780,76659,343396,340165,337289,343073,339765,343465,343360,343406,342151,342954,343444,343422,342499,342981,342923,342514,342477,343436,343504,342311,342411,79505,342007,343141,340842,342348,343543,343522,335471,341481,342153,342656,341805,343484,83588,75923,338583,338876,341862,344096,341505,68158,344190,87392,343974,343340,343968,344055,29067,344081,344183,344202,344006,340144,340046,341872,343552,343823,88446,341356,88358,341098,344166,343794,341585,339770,340418,343325,343038,341573,340756,344221,344146,341995,343314,343142,342834,344197,336734,343942,340364,343941,344077,344242,340510,87173,344043,340625,340566,341552,341319,72472,342140,339583,69884,340030,343060,338677,343061,343062,343063,343064,343065,343066,343067,343068,343069,338382,339073,342884,342992,341470,340645,342524,338189,342953,88711,81543,341650,341967,87546,340070,341362,343072,341458,341819,337894,342636,341389,342925,342866,341568,342761,341665,342078,342629,340777,342174,342957,340503,79498,83419,341677,72560,342057,342566,81681,80867,340958,72149,42545,47280,54175,82345,58692,85833,61170,338810,58160,87857,80940,76883,60891,79451,78338,341426,341224,340126,47308,340538,341316,341818,339754,341750,31016,341744,341233,340096,3886,6322,340414,4107,4248,8694,5405,19506,15433,21641,20174,19901,82088,6534,24837,27814,27153,17015,51816,26991,23926,26989,12146,29039,29853,29721,75337,59964,49183,30444,26282,31808,32890,69862,33115,61181,61179,61184,36470,49372,37762,36512,36000,33169,35477,35577,38178,35341,57786,11431,12137,11504,11813,11810,33670,47523,45702,45065,57425,47300,36157,37005,46746,46568,44657,41330,47617,80303,80176,43637,42952,41853,41799,43116,335770,343772,343777,343773,343728,343760,343729,343774,343730,343731,343732,343733,343761,343775,343734,343762,343735,343778,343736,343738,343763,343739,343740,343764,343741,343742,343743,343744,343765,343745,343746,343747,343766,343767,343776,343768,343769,343770,343771,343779,343780,343781,343782,343783,343784,343785,343786,343787,343788,343789,343790,343791,343792,343887,343888,343889,343890,343891,343892,343893,343894,343895,343896,343897,344088,344089,344090,344091,344092,344093,344094,344284,344285,344286,344287,344288,344289,344290,344291,344292,344708,344760,344965,344966,345062,345063,345064,345065,345066,345067,345068,345069,345070,345071,345072,345129,345130,343737,345606,345607,345608,345609,345610,345612,345613,345614,345615,345616,345617,4955,4960,16580,10676,8986,18622,19179,14056,10843,13300,22830,22030,35946,50198,19087,21815,19758,18725,25418,23928,18297,18339,58332,81140,80746,29321,33519,34471,32252,31764,33542,29583,81361,79501,30517,30381,38145,33036,32471,333924,30940,49796,37107,35372,36032,36950,36762,36979,37329,59204,61633,38546,39190,34356,68203,67280,78402,36499,49206,79158,80428,35103,47879,63427,63342,41439,42814,46191,599,3013,394,523,3049,324,42088,4485,6214,12546,5155,5193,5980,5959,4828,24223,13070,25543,25501,17278,22035,25387,27801,27853,74448,79249,28109,29228,28564,30108,87005,30492,30494,30643,73947,36628,31752,60060,33906,33209,33960,33958,57726,35294,34708,34680,35825,33947,52492,44938,49344,37729,54304,59681,46705,33644,39572,33574,33284,1611,2102,2244,1595,41227,41229,40312,41043,39884,40352,50755,1491,824,1388,1486,1306
        ];
        $clientCount = count($clientIds);
        $i = 1;
        foreach ($clientIds as $clientId)
        {
            try {
                self::info("进度: [$i / $clientCount]");
                $selectRuleSql = "select * from tbl_performance_v2_rule where client_id = $clientId and refer_type = 2 and type = 2 and enable_flag = 1 and delete_flag = 0";
                $db = PgActiveRecord::getDbByClientId($clientId);
                $ruleInfo = $db->createCommand($selectRuleSql)->queryRow();
                if (empty($ruleInfo)) {
                    $i++;
                    continue;
                }
                $ruleInfo['filters'] = json_decode($ruleInfo['filters'], true);
                $this->actionRecordPerformanceByRuleInfo($ruleInfo);
                $i++;
            } catch (Exception $e) {
                self::info("执行异常,msg -- {$e->getMessage()}");
                $i++;
            }
        }
    }

    public function actionTestRule()
    {
        $userId = 11858405;
        $ruleId = 3477355087;
        User::setLoginUserById($userId);
        $user = User::getLoginUser();
        $clientId = $user->getClientId();

        $this->actionRecordPerformanceByRuleId($clientId, $ruleId);
    }

    // ./yiic-test performanceV2  RecordPerformanceByRuleId --clientId=9650 --ruleId=3377753578
    public function actionRecordPerformanceByRuleId($clientId, $ruleId, $ruleInfo = [], $needDeleteHistoryRecord = null)
    {
        $iterStartTime = microtime(true);
        if (empty($clientId) || empty($ruleId)) return;
        PgActiveRecord::setConnection(PgActiveRecord::getDbByClientId($clientId));
        ProjectActiveRecord::setConnection(ProjectActiveRecord::getDbByClientId($clientId));
        if (empty($ruleInfo)) {
            $ruleInfo = (\common\models\client\PerformanceV2Rule::model()->findByPk($ruleId))->getAttributes();
        }
        $performanceV2RuleFormatter = new \common\library\performance_v2\rule\PerformanceV2RuleFormatter($clientId);
        $ruleInfo = $performanceV2RuleFormatter->strip($ruleInfo);

        $ruleList = ($ruleInfo['refer_type'] == Constants::TYPE_MAIL) ? Helper::splitPerformanceRule($ruleInfo) : [$ruleInfo];
        self::info(sprintf("clientId[{$ruleInfo['client_id']}] ruleId[{$ruleInfo['rule_id']}] ruleName[%s] referType[{$ruleInfo['refer_type']}] 拆分[%s]个子规则", str_replace('%', '%%', $ruleInfo['name']), count($ruleList)));

        foreach ($ruleList as $index => $ruleItem)
        {
            if (in_array($ruleItem['time_field'], ['latest_success_opportunity_time','every_transaction_order_time','deal_time'])) $needDeleteHistoryRecord =  true;
            if (($ruleInfo['refer_type'] == Constants::TYPE_MAIL)) {
                $needDeleteHistoryRecord = ($index == 0);
            }
            $this->actionRecordPerformanceByRuleInfo($ruleItem, $needDeleteHistoryRecord);
        }
        $iterEndTime = microtime(true);
        self::info(sprintf("clientId[$clientId] 执行 ruleId[{$ruleInfo['rule_id']}] [%s]秒", ($iterEndTime-$iterStartTime)));

    }

    public function actionRecordPerformanceByTaskId($clientId = 1, $taskId = 3217655009, $needDeleteHistoryRecord = null)
    {
        $iterStartTime = microtime(true);
        PgActiveRecord::setConnection(PgActiveRecord::getDbByClientId($clientId));
        ProjectActiveRecord::setConnection(ProjectActiveRecord::getDbByClientId($clientId));
        PerformanceV2RecalculateTask::start($taskId);
        $taskInfo = (PerformanceV2Task::model()->findByPk($taskId))->getAttributes();
        $taskRuleId = $taskInfo['rule_id'];

        // 组装规则信息
        $ruleInfo = (new PerformanceV2Rule($clientId, $taskRuleId))->getAttributes();
        $ruleList = ($ruleInfo['refer_type'] == Constants::TYPE_MAIL) ? Helper::splitPerformanceRule($ruleInfo) : [$ruleInfo];
        self::info(sprintf("clientId[{$ruleInfo['client_id']}] ruleId[{$ruleInfo['rule_id']}] ruleName[%s] referType[{$ruleInfo['refer_type']}] 拆分[%s]个子规则", str_replace('%', '%%', $ruleInfo['name']), count($ruleList)));
        try {
            foreach ($ruleList as $index => $ruleItem)
            {
                if (($ruleInfo['refer_type'] == Constants::TYPE_MAIL)) {
                    $needDeleteHistoryRecord = ($index == 0);
                }
                $succeed = $this->actionRecordPerformanceByRuleInfo($ruleItem, $needDeleteHistoryRecord);
            }
            PerformanceV2RecalculateTask::finish($taskId,$succeed ?? false);
        }catch (Exception $exception){
            self::info("绩效重算失败 失败原因是:" . $exception->getMessage());
            PerformanceV2RecalculateTask::finish($taskId,false);
        }

    }

    //todo 如果是成交订单金额，需要重新维护account_flag以及更新客群的时间统计字段
    public function actionRecordPerformanceByRuleInfo($ruleInfo,  $needDeleteHistoryRecord = null)
    {
        $clientId = $ruleInfo['client_id'];
        $referType = $ruleInfo['refer_type'];
        $ruleId = $ruleInfo['rule_id'];
        $timeField = $ruleInfo['time_field'];

        //重置客群的计算时间
        if ($ruleInfo['type'] == 2 && $ruleInfo['refer_type'] == Constants::TYPE_ORDER) {
            Helper::resetCompanyFieldWithTransactionOrder($clientId, $ruleId);
        }

        // 这部分逻辑是为了计算是否需要删除历史数据
        /**
         * 会显式设置needDelete 的场景有以下几点
         * 如果以后有增加请在这里补充
         * 1, 邮件拆分子规则的情况，第一个为true 后面都是false
//         * 2 静态时间转换为动态时间的情况，需要删除历史绩效记录  --raylei: 2023-07-04以后就没有这种场景了
         */
        //  $needDeleteHistoryRecord = ($needDeleteHistoryRecord === null) ?  true : $needDeleteHistoryRecord
        $needDeleteHistoryRecord = $needDeleteHistoryRecord === null || $needDeleteHistoryRecord;
        // 动态时间不清理数据
        if ($needDeleteHistoryRecord)
        {
            LogUtil::info("DeletePerformanceRecord clientId:$clientId  ruleId:{$ruleId} ");
            // 清理旧数据
            $recordList = new PerformanceV2RecordList($clientId);
            $recordList->setReferType($referType);
            $recordList->setRuleId($ruleId);
            $recordList->delete();
        }

        // 超级管理员
        $adminUserId = PrivilegeService::getInstance($clientId)->getAdminUserId();
        if (!$adminUserId) {
            LogUtil::info("clientId:$clientId adminUser not exist!");
            return;
        }
        User::setLoginUserById($adminUserId);
        $ruleConfig = new RuleConfig($clientId, $referType, [], $ruleInfo);
        $filterRunner = new WorkflowFilterRunner($ruleConfig);
        $filterRunner->setFilters($ruleInfo['filters'], $ruleInfo['criteria']);
        // 处理绩效计算过程中用到的ExtraFilters - begin
        $targetFieldFilters = $ruleConfig->getExtraFiltersByTargetField();
        $filterRunner->addExtraWorkflowFilters($targetFieldFilters['filters'], $targetFieldFilters['criteria'], $targetFieldFilters['operator']);
        // 处理绩效计算过程中用到的ExtraFilters - end
        $filterRunner->setFields('*');
        $succeed = true;
        $specialKeys = PerformanceV2Constant::REFER_SPECIAL_KEYS_MAP[$referType] ?? [];

        self::info("ClientId[{$clientId}] RuleId[$ruleId] -- Begin -- ");
        $model = PerformanceV2Record::model();
        $dataMap = [];
        $mainCurrency = Client::getClient($clientId)->getMainCurrency();
        // 特殊字段在这里加
        $extraData = [
            'mainCurrency' => $mainCurrency,
            'specialKeys' => $specialKeys
        ];

        foreach ($filterRunner->iterate(1000) as $items)
        {
            $items = Helper::processReferListByRuleInfo($items,$ruleInfo);
            if (empty($items)) continue;

            //匹配的销售订单
            $matchOrderIds = [];
            self::info("count[  " . count($items) . "  ]");
            try {
                // 获取业绩计算结果
                list($userPerformanceRecords, $departmentPerformanceRecords) = Helper::getBatchRecordPerformances($referType, $items, $ruleInfo, $extraData);
                $allPerformanceRecords = array_merge($userPerformanceRecords, $departmentPerformanceRecords);
                $values = [];
                $count = count($allPerformanceRecords);
                // 获取record_id
                $recordId = PgActiveRecord::produceAutoIncrementId($count);
                foreach ($allPerformanceRecords as $item) {
                    if ($item['account_date'] == '1970-01-01 00:00:00' || $item['account_date'] == '1970-01-01') continue;
                    $userRecord = [];
                    $userRecord['record_id'] = $recordId;
                    $userRecord['client_id'] = $item['client_id'];
                    $userRecord['refer_id'] = $item['refer_id'];
                    $userRecord['owner_id'] = $item['owner_id'] ?? 0;
                    $userRecord['rule_id'] = $item['rule_id'];
                    $userRecord['refer_type'] = $item['refer_type'] ?? 0;
                    $userRecord['record_type'] = $item['record_type'] ?? 0;
                    $userRecord['indicator_type'] = \Util::escapeDoubleQuoteSql($item['indicator_type']);
                    $userRecord['indicator_value'] = $item['indicator_value'] ?? 0;
                    $userRecord['account_date'] = \Util::escapeDoubleQuoteSql($item['account_date']);
                    $userRecord['create_time'] = \Util::escapeDoubleQuoteSql($item['create_time']);
                    $userRecord['update_time'] = \Util::escapeDoubleQuoteSql($item['update_time']);
                    $userRecord['company_id'] = $item['company_id'] ?? 0;
                    $values[] = "({$userRecord['record_id']},{$userRecord['client_id']}, {$userRecord['refer_id']}, {$userRecord['owner_id']}, {$userRecord['rule_id']}, {$userRecord['refer_type']}, {$userRecord['record_type']}, '{$userRecord['indicator_type']}', {$userRecord['indicator_value']}, '{$userRecord['account_date']}', '{$userRecord['create_time']}', '{$userRecord['update_time']}', {$userRecord['company_id']})";
                    $recordId --;

                    //满足预设的成交订单金额绩效规则
                    //满足条件更新客群的数据

                    //成交订单金额规则的判断条件
                    $defaultOrderRuleFlag = $item['record_type'] == PerformanceV2Constant::RECORD_TYPE_USER && $ruleInfo['type'] == 2
                        && $ruleInfo['refer_type'] == \Constants::TYPE_ORDER && !empty($item['company_id']);

                    if ($defaultOrderRuleFlag) {
                        $matchOrderIds[] = $item['refer_id'];
                    }

                }
                // 业绩数据入库
                if (!empty($values)) {
                    // 批量入库 若存在则更新value
                    $commonInsertPreSql = "insert into tbl_performance_v2_record (record_id,client_id, refer_id, owner_id, rule_id, refer_type, record_type, indicator_type, indicator_value, account_date, create_time, update_time, company_id) VALUES ";
                    $commonInsertEndSql = " ON CONFLICT (client_id, rule_id, owner_id, refer_id,account_date ) DO UPDATE SET indicator_type = EXCLUDED.indicator_type, account_date = EXCLUDED.account_date, update_time = EXCLUDED.update_time, indicator_value = EXCLUDED.indicator_value, owner_id = EXCLUDED.owner_id, company_id = EXCLUDED.company_id";
                    $insertSql = $commonInsertPreSql . implode(",", $values) . $commonInsertEndSql;
                    $model->getDbConnection()->getPdoInstance()
                        ->prepare($insertSql)->execute();
                }

            } catch (Exception $e) {
                //                print_r($e->getTraceAsString());
                self::info("错误信息是: [{$e->getMessage()}]");
//                \Util::batchLogInfo([
//                    'e' => $e::class,
//                ]);
                \common\library\report\error\ErrorReport::phpError(new \CExceptionEvent(null, $e), $e->getTrace());
                $succeed = false;
            }

            // 处理跟订单业绩相关数据
            if ($ruleInfo['type'] == 2 && $ruleInfo['refer_type'] == Constants::TYPE_ORDER)
            {
                foreach ($items as $item)
                {
                    $companyId = $item['company_id'];
                    if (empty($companyId)) continue;

                    if (!isset($dataMap[$companyId])) $dataMap[$companyId] = $item['account_date'];
                    $dataMap[$companyId] < $item['account_date'] && $dataMap[$companyId] = $item['account_date'];
                }
                // 更改order的account_flag
                $orderIds = array_column($items, 'order_id') ?? [];
                $orderBatchOperator = new OrderBatchOperator($adminUserId);
                if (!empty($matchOrderIds)) {
                    $orderBatchOperator->setParams([
                        'order_ids' => $matchOrderIds,
                    ]);
                    $accountFlag = $ruleInfo['enable_flag'] ? true : false;
                    $orderBatchOperator->changeAccountFlag($accountFlag);
                }
                $notMatchOrderIds = array_diff($orderIds, $matchOrderIds);
                if(!empty($notMatchOrderIds)){
                    $orderBatchOperator->setParams([
                        'order_ids' => $notMatchOrderIds
                    ]);
                    $orderBatchOperator->changeAccountFlag(false);
                }
            }
        }
        self::info("ClientId[{$clientId}] RuleId[$ruleId] -- End -- \n");

        if ($ruleInfo['type'] == 2 && $ruleInfo['refer_type'] == Constants::TYPE_ORDER && !empty($dataMap))
        {
            //预设成交订单规则 变更需要重新计算company_id的成交日期时间
            if ($ruleInfo['enable_flag']==1) {
                Helper::refreshCompanyFieldWithTransactionOrder($clientId,$ruleId);
                //重新计算client订单的首单、重算首次成交订单金额
                \common\library\performance_v2\Helper::refreshOrderFirstFlag($clientId, $ruleInfo['rule_id']);
            }
        }

        return $succeed;
    }

    public function actionCreatePerformanceTask(int $type = 1, string $greyClientIdsStr = '', string $greyNumber = '')
    {
        $commonTaskColumns = ['id', 'client_id', 'update_user', 'rule_id', 'refer_type', 'target_field', 'status', 'create_time', 'update_time'];
        $taskColumnsSql = " (" . implode(',', $commonTaskColumns) . ") " ;

        switch ($type)
        {
            case 1: //灰度clientIds
                self::info("灰度clientIds为{$greyClientIdsStr}的clientId");
                $greyClientIds = explode(',', $greyClientIdsStr);
                break;
            case 2: //灰度尾号
                self::info("灰度尾号为{$greyNumber}的clientId");
                $greyNumber = explode(',', $greyNumber);
                $greyClientIds = array_column($this->getClientList(0, false, null, null, 0, 0, $greyNumber), 'client_id');
                break;
            case 3: //全量
                self::info("全量client");
                $greyClientIds = array_column($this->getClientList(), 'client_id');
                break;
            default:
                $greyClientIds = [];
                break;
        }

        foreach ($greyClientIds as $clientId)
        {
            try {
                self::info("ClientId[{$clientId}] Begin");
                $pgDb = PgActiveRecord::getDbByClientId($clientId);
                if (empty($pgDb)) continue;
                PgActiveRecord::setConnection($pgDb);

                // 逻辑部分
                $sql = "select * from tbl_performance_v2_rule where TYPE != 1 and client_id = {$clientId}";
                $ruleList = $pgDb->createCommand($sql)->queryAll();

                $ruleIds = implode(',', array_column($ruleList, 'rule_id'));
                if (empty($ruleIds)) continue;
                $deleteTaskSql = "delete from tbl_performance_v2_task where client_id = {$clientId} and rule_id in ($ruleIds)";
                $deleteRows = $pgDb->createCommand($deleteTaskSql)->execute();
                self::info("删除task的sql为[  {$deleteTaskSql}  ] -- 删除了[  {$deleteRows}  ]行");

                $presetTasks = [];
                $date = date('Y-m-d H:i:s');
                foreach ($ruleList as $ruleInfo)
                {
                    $initTaskInfo = [
                        ProjectActiveRecord::produceAutoIncrementId(),
                        $clientId,
                        $ruleInfo['update_user'],
                        $ruleInfo['rule_id'],
                        $ruleInfo['refer_type'],
                        empty($ruleInfo['target_field']) ? "''" : "'{$ruleInfo['target_field']}'",
                        PerformanceV2Constant::TASK_STATUS_INIT,
                        "'{$date}'",
                        "'{$date}'",
                    ];
                    $presetTasks[] = '(' . implode(',', $initTaskInfo) . ')';
                }
                $valuesSql = implode(',', $presetTasks);
                $insertTaskSql = "insert into tbl_performance_v2_task {$taskColumnsSql} values {$valuesSql}";
                $pgDb->createCommand($insertTaskSql)->execute();
                // 逻辑部分
            } catch (Throwable $throwable) {
                self::info("Error Msg[{$throwable->getMessage()}]");
            }
            self::info("ClientId[{$clientId}] END\n");
        }
    }

    public function actionCreatePresetPerformance(int $type = 1, string $greyClientIdsStr = '', string $greyNumber = '')
    {
        switch ($type)
        {
            case 1: //灰度clientIds
                self::info("灰度clientIds为{$greyClientIdsStr}的clientId");
                $greyClientIds = explode(',', $greyClientIdsStr);
                break;
            case 2: //灰度尾号
                self::info("灰度尾号为{$greyNumber}的clientId");
                $greyNumber = explode(',', $greyNumber);
                $greyClientIds = array_column($this->getClientList(0, false, null, null, 0, 0, $greyNumber), 'client_id');
                break;
            case 3: //全量
                self::info("全量client");
                $greyClientIds = array_column($this->getClientList(), 'client_id');
                break;
            default:
                $greyClientIds = [];
                break;
        }

        foreach ($greyClientIds as $clientId)
        {
            try {
                $pgDb = PgActiveRecord::getDbByClientId($clientId);
                PgActiveRecord::setConnection($pgDb);
                $sql = "select t.id from tbl_performance_v2_task as t join tbl_performance_v2_rule as r on t.rule_id = r.rule_id where r.type != 1 and r.client_id = {$clientId}";
                $taskList = $pgDb->createCommand($sql)->queryColumn();
                foreach ($taskList as $taskId)
                {
                    $this->actionRecordPerformanceByTaskId($clientId, $taskId);
                }
            } catch (Throwable $throwable) {
                self::info("Error Msg[{$throwable->getMessage()}]");
            }
        }
    }

    public function getBatchRecordPerformances($referTYpe, $referInfoList, $rule, $extraData = [],$traceId = '')
    {
        $userPerformanceRecords = [];
        $departmentPerformanceRecords = [];
        $relateInfo = [];
        if (empty($referInfoList)) return [$userPerformanceRecords, $departmentPerformanceRecords];

        $specialKeys = $extraData['specialKeys'] ?? [];
        $specialReferIds = (isset($specialKeys['refer_id_key']) && $specialKeys['refer_id_key'] != '') ? array_column($referInfoList,$specialKeys['refer_id_key']) : 0;
        $extraData['mainCurrency'] = empty($extraData['mainCurrency']) ? $extraData['mainCurrency'] :  Client::getClient($rule['client_id'])->getMainCurrency();


        // 初始化公共参数
        $clientId = $rule['client_id'];
        $targetField = $rule['target_field']; // 考核指标字段

        $performanceField = $rule['performance_field']; // 被考核角色字段
        // 转换performanceField => e.g 邮件中的sender和receiver同时取user_id
        $performanceField = Helper::transferPerformanceField($referTYpe,$performanceField);

        $timeField = $rule['time_field']; // 计入时间依据字段
        $formula = json_decode($rule['formula'] ?? '{}',true) ?? [];
        $now = date('Y-m-d H:i:s');

        $adminUserId = PrivilegeService::getInstance($clientId)->getAdminUserId();
        if (!$adminUserId) {
            \LogUtil::info("clientId:$clientId adminUser not exist!");
            return [$userPerformanceRecords, $departmentPerformanceRecords];
        }

        if (!empty($formula))
        {
            $relateInfo = Helper::getFormulaRelateInfoByReferId($clientId,$adminUserId,$formula,$specialReferIds,$referTYpe);
        }
        // 有些考核指标需要关联其他对象  e.g:  order => cash_collection
        $specialInfo = Helper::getRelateInfoMapByTargetField($clientId,$referTYpe,$targetField,$specialReferIds,$formula);

        $allReferUserIds = array_unique(Helper::getAllUserIdsByReferInfoListAndTargetField($referInfoList,$referTYpe,$performanceField));

        $userToDepartmentListMap = Helper::batchGetUserToDepartmentList($clientId, $allReferUserIds);

        foreach ($referInfoList as $referInfo) {

            if (!\common\library\performance_v2\Helper::isNeedRecordPerformance($referTYpe, $referInfo, $rule['rule_id'])) continue;

            $externalFieldData = Helper::getExternalFieldDataByReferInfo($referInfo,$referTYpe);

            $specialReferId = (isset($specialKeys['refer_id_key']) && $specialKeys['refer_id_key'] != '') ? $referInfo[$specialKeys['refer_id_key']] : 0;
            $referInfo['specialInfo'] = $specialInfo[$specialReferId] ?? [];

            $exchangeRateSetting = [
                'amount_rmb' => 'exchange_rate',
                'amount_usd' => 'exchange_rate_usd',
            ];
            $exchangeRate = (int)(isset($exchangeRateSetting[$targetField]) ? $referInfo[$exchangeRateSetting[$targetField]] : 100);

            if (empty($specialReferId)) {
                self::info($traceId." specialReferId empty ruleId[{$rule['rule_id']}]  --  referInfo[" . json_encode($referInfo). "]");
                continue;
            }

            $performanceRates = \common\library\performance_v2\Helper::getReferPerformanceRates($referTYpe, $referInfo, $performanceField, $userToDepartmentListMap);
            $userRates = $performanceRates['users'];
            $departmentRates = $performanceRates['departments'];
            $accountDate = '';
            if (in_array($referTYpe, [\Constants::TYPE_OPPORTUNITY, \Constants::TYPE_ORDER, \Constants::TYPE_QUOTATION, \Constants::TYPE_CASH_COLLECTION,\Constants::TYPE_LEAD,\Constants::TYPE_COMPANY])) {
                $accountDate = (isset($externalFieldData[$timeField]) && !empty($externalFieldData[$timeField])) ? $externalFieldData[$timeField] : '';
            }

            $timeField = Helper::transferTimeField($referTYpe,$timeField);



            if (empty($accountDate)) {
                // 自定义字段可能有没有值的时候
                $accountDate = $referInfo[$timeField] ?? '1970-01-01 00:00:00';
            }

            if (empty($accountDate)) {
                continue;
            }

            // 特殊字段在这里加
            $extraData['exchangeRate'] = $exchangeRate;

            // 公共数据部分
            $record = [
                'client_id' => $rule['client_id'],
                'refer_type' => $referTYpe,
                'refer_id' => $specialReferId,
                'indicator_type' => $rule['target_field'],
                'account_date' => $accountDate,
                'create_time' => $now,
                'update_time' => $now,
                'rule_id' => $rule['rule_id'],
                'indicator_value' => 0,
                'owner_id' => 0,
                'company_id' => $referInfo['company_id'] ?? 0,
            ];

            self::info($traceId." ruleId[{$rule['rule_id']}]  --  refer_id[" . $specialReferId . "]");
            foreach ($userRates as $belongsTo => $rate)
            {
                if (empty($belongsTo)) continue;
                $userRecord = Helper::getPerformanceRecordMapByRecordType($record, PerformanceV2Constant::RECORD_TYPE_USER, $referInfo, $relateInfo, $rule, $belongsTo, $rate, $extraData);
                $userPerformanceRecords[] = $userRecord;
            }

            foreach ($departmentRates as $belongsTo => $rate)
            {
                $belongsTo = (int)$belongsTo;
                $departmentRecord = Helper::getPerformanceRecordMapByRecordType($record, PerformanceV2Constant::RECORD_TYPE_DEPARTMENT, $referInfo, $relateInfo, $rule, $belongsTo, $rate, $extraData);
                $departmentPerformanceRecords[] = $departmentRecord;
            }
        }

        return [$userPerformanceRecords, $departmentPerformanceRecords];
    }

    // 初始化Client业绩规则,业绩task,业绩数据
    public function actionInitClientPerformanceData(int $type = 1, string $greyClientIdsStr = '', string $greyNumber = '0')
    {
        switch ($type)
        {
            case 1: //灰度clientIds
                self::info("灰度clientIds为{$greyClientIdsStr}的clientId");
                $greyClientIds = explode(',', $greyClientIdsStr);
                break;
            case 2: //灰度尾号
                self::info("灰度尾号为{$greyNumber}的clientId");
                $greyNumberArr = explode(',', $greyNumber);
                $greyClientIds = array_column($this->getClientList(0, false, null, null, 0, 0, $greyNumberArr), 'client_id');
                break;
            case 3: //全量
                self::info("全量client");
                $greyClientIds = array_column($this->getClientList(), 'client_id');
                break;
            default:
                $greyClientIds = [];
                break;
        }

        $filterClientIds = [7,10,2106,17383,18618,16149,4210,15220,3648,23306,15290,7398,11991,3378,10779,20539,22280,22461,22577,22752,22807,23547,26193,26204,26335,26556,27026,27577,27598,27611,28261,32194,32435,32757,32017,32414,32399,32338,32681,29527,32512];
        $filterClientIds = array_merge($filterClientIds, array_column($this->getClientList(0, false, null, null, 0, 0, [9]), 'client_id'));
        $greyClientIds = array_values(array_diff($greyClientIds, $filterClientIds));
        $greyClientIdsStr = implode(',', $greyClientIds);
        foreach ($greyClientIds as $clientId)
        {
            try {
                \common\library\performance_v2\Helper::initClientPrepareRuleByClient($clientId);
                \common\library\performance_v2\Helper::initClientTeamWallSetting($clientId);
                $this->actionMigrateGoal($clientId);
            } catch (Throwable $throwable) {
                self::info("初始化业绩规则错误信息是 -- " . $throwable->getMessage());
                continue;
            }
        }

        try {
            $this->actionCreatePerformanceTask($type, $greyClientIdsStr, $greyNumber);
            $this->actionCreatePresetPerformance($type, $greyClientIdsStr, $greyNumber);
        } catch (Throwable $throwable) {
            self::info("初始化业绩数据错误信息是 -- " . $throwable->getMessage());
        }
    }

    /**
     * 迁移绩效目标
     * @param $clientId
     */
    public function actionMigrateGoal($clientId)
    {
        $db = PgActiveRecord::getDbByClientId($clientId);
        $myDb = ProjectActiveRecord::getDbByClientId($clientId);
        PgActiveRecord::setConnection($db);
        ProjectActiveRecord::setConnection($myDb);
        $setting = (new \common\library\performance\PerformanceSetting($clientId))->getSetting(true);
        $sql = "select * from tbl_performance_v2_rule where client_id={$clientId} and type<>" . PerformanceV2Constant::RULE_TYPE_COMMON;
        $ruleMap = array_column($db->createCommand($sql)->queryAll(), null, 'type');

        Switch ($setting['type']) {
            case \Constants::TYPE_ORDER:
                $ruleId = $ruleMap[PerformanceV2Constant::RULE_TYPE_ORDER_AMOUNT]['rule_id'];
                break;
            case Constants::TYPE_OPPORTUNITY:
                $ruleId = $ruleMap[PerformanceV2Constant::RULE_TYPE_OPPORTUNITY_WIN]['rule_id'];
                break;
            case Constants::TYPE_CASH_COLLECTION:
                $ruleId = $ruleMap[PerformanceV2Constant::RULE_TYPE_CASH_COLLECTION_AMOUNT]['rule_id'];
                break;
            default:
                throw new \RuntimeException('Migrate goal fault clientId:' . $clientId);
        }
        $sql = "select * from tbl_user_performance_goals where client_id={$clientId} and cycle={$setting['cycle']} ";
        $oldUserGoals = $db->createCommand($sql)->query();
        $sql= "select * from tbl_department_performance_goals where client_id={$clientId} and cycle={$setting['cycle']} ";
        $oldDepGoals = $db->createCommand($sql)->query();

        $data = [];
        $index = [];
        foreach ($oldUserGoals as $goal) {
            if (! in_array($goal['cycle'], [1,2,3])) {
                continue;
            }
            $amount = round($goal['amount'] / $goal['cycle'], 0);
            for ($i=1; $i <= $goal['cycle']; $i++) {
                $indexKey = $clientId . ":" . $goal['user_id'] . ":" . $ruleId . ":" . $goal['year'] . ":" . PerformanceV2Constant::RECORD_TYPE_USER . ":" . ($goal['cycle'] * ($goal['cycle_num'] - 1) + $i);
                if (isset($index[$indexKey]) && $index[$indexKey]) {
                    continue;
                }
                $index[$indexKey] = true;

                $item = [
                    $clientId,
                    $goal['user_id'],
                    $ruleId,
                    $goal['year'],
                    PerformanceV2Constant::RECORD_TYPE_USER,
                    $goal['cycle'] * ($goal['cycle_num'] - 1) + $i,
                    $amount
                ];
                $itemStr = "(" . implode(',', $item) . ")";
                $data[] = $itemStr;
            }
        }

        foreach ($oldDepGoals as $goal) {
            if (! in_array($goal['cycle'], [1,2,3])) {
                continue;
            }
            $amount = round($goal['amount'] / $goal['cycle'], 0);
            for ($i=1; $i <= $goal['cycle']; $i++) {
                $indexKey = $clientId . ":" . $goal['department_id'] . ":" . $ruleId . ":" . $goal['year'] . ":" . PerformanceV2Constant::RECORD_TYPE_DEPARTMENT . ":" . ($goal['cycle'] * ($goal['cycle_num'] - 1) + $i);
                if (isset($index[$indexKey]) && $index[$indexKey]) {
                    continue;
                }
                $index[$indexKey] = true;
                $item = [
                    $clientId,
                    $goal['department_id'],
                    $ruleId,
                    $goal['year'],
                    PerformanceV2Constant::RECORD_TYPE_DEPARTMENT,
                    $goal['cycle'] * ($goal['cycle_num'] - 1) + $i,
                    $amount
                ];
                $itemStr = "(" . implode(',', $item) . ")";
                $data[] = $itemStr;
            }
        }
        if (empty($data)) {
            echo "client:{$clientId} Migrate Goal null !\n";
            return;
        }
        $valuesStart = implode(',', $data);
        $insertSql = "insert into tbl_performance_v2_goals (client_id, refer_id, performance_rule_id, year, scope, month, amount) values " . $valuesStart . " on conflict(client_id, refer_id, performance_rule_id, year, scope, month) do update set amount=EXCLUDED.amount";

        $db->createCommand($insertSql)->execute();

        echo "client:{$clientId} Migrate Goal success !\n";
    }

    public function actionReRunPerformance($userId, $ruleId)
    {
        User::setLoginUserById($userId);
        $user = User::getLoginUser();
        $clientId = $user->getClientId();

        $taskList = PerformanceV2Task::model()->findAll("client_id = {$clientId} and rule_id = {$ruleId}");
        if (empty($taskList)) return;
        $taskId = $taskList[0]['id'];

        $this->actionRecordPerformanceByTaskId($clientId, $taskId);
    }

    public function actionFixPerformanceData($clientIds = 0, $start = 0, $lastNumber = null)
    {
        ini_set("memory_limit", "15000M");

        $clientIdList = [];
        //灰度指定client
        if ($clientIds != 0 && is_string($clientIds)) {
            $clientIdList = explode(',', $clientIds);
        } else {
            //为0全量
            $clientList = self::getClientList($clientIds, false, null, null, $start, 0, $lastNumber);
            $clientIdList = array_column($clientList, 'client_id', '');
        }

        foreach ($clientIdList as $clientId) {

            self::info("开始clientId：".$clientId."数据统计");
            //所有client下的所有规则id
            $adminUserId = PrivilegeService::getInstance($clientId)->getAdminUserId();
            User::setLoginUserById($adminUserId);

            $list = new \common\library\performance_v2\rule\PerformanceV2RuleList($clientId, $adminUserId);
            $list->setFields('rule_id');
            $list->setEnableFlag(1);
            $ruleList = $list->find();

            if (empty($ruleList)) {
                continue;
            }

            foreach ($ruleList as $rule) {
                try{
                    $this->actionRecordPerformanceByRuleId($clientId,$rule['rule_id']);
                } catch (Throwable $e){
                    self::info($e->getMessage());
                }
            }
        }
    }

    // 修复订单默认业绩脚本
    public function actionFixOrderAccountFlag()
    {
        $clientIds = array_column($this->getClientList(), 'client_id');
        foreach ($clientIds as $clientId)
        {
            $db = PgActiveRecord::getDbByClientId($clientId);
            $defaultRule = $db->createCommand("SELECT * FROM tbl_performance_v2_rule WHERE client_id={$clientId} AND refer_type=2 AND enable_flag=1 AND delete_flag=0 AND TYPE=2;")->queryRow();
            if (empty($defaultRule)) {
                continue;
            }
            $this->actionRecordPerformanceByRuleInfo($defaultRule);
        }
    }

    //修复成交订单金额绩效规则
    //https://www.tapd.cn/********/prong/stories/view/11********001026457?url_cache_key=dff088bca8f7d3144d4d5f643bac227b&action_entry_type=stories
    public function actionFixOrderAmountRule($execute = 0)
    {
        $clientIds = array_column($this->getClientList(), 'client_id');
        $clientIds = [73366];
        $processedClientIds = [];
        $num = 1;
        $clientCount = count($clientIds);
        foreach ($clientIds as $clientId)
        {
            echo "$clientId $num/$clientCount \n";
            $num++;

            $db = PgActiveRecord::getDbByClientId($clientId);
            PgActiveRecord::setConnection($db);

            $sql = "select * from tbl_performance_v2_rule where client_id={$clientId} and type=" . PerformanceV2Constant::RULE_TYPE_ORDER_AMOUNT;
            $ruleList = $db->createCommand($sql)->queryAll();
            if (empty($ruleList))
                continue;

            $statusService = new InvoiceStatusService($clientId, \Constants::TYPE_ORDER);
            $orderEndStatus = $statusService->endStatus();
            $saveStatus = [];
            foreach ($orderEndStatus as $status)
            {
                if ($status['name'] === '已作废' || $status['name'] === '售后' || $status['name'] === '交易取消')
                    continue;

                $saveStatus[] = $status['id'];
            }
            if (empty($saveStatus))
                continue;
            print_r($saveStatus);
            $sqlArray = [];
            foreach ($ruleList as $rule)
            {
                $beforeFilters = $rule['filters'];
                $filters = json_decode($rule['filters'],true);

                $needUpdate = false;
                foreach ($filters as &$filter)
                {
                    if ($filter['field'] != 'status'
                        || $filter['refer_type'] != Constants::TYPE_ORDER
                        || !empty($filter['value'])
                    )
                        continue;

                    $filter['value'] = $saveStatus;
                    $needUpdate = true;
                }

                if (!$needUpdate)
                    continue;

                $filters = json_encode($filters);

                $sqlArray[] = "update tbl_performance_v2_rule set filters='{$filters}' where client_id={$clientId} and rule_id={$rule['rule_id']}";
            }

            if (!empty($sqlArray))
            {
                $sqlString = implode(';',$sqlArray);
                LogUtil::info("clientId:{$clientId} beforeFilters:".$beforeFilters);
                LogUtil::info("clientId:{$clientId} executeSql:".$sqlString);
                $processedClientIds[] = $clientId;
                if ($execute)
                    $db->createCommand($sqlString)->execute();
            }
        }
        LogUtil::info("executeFlag:{$execute} processed ClientIds:".json_encode($processedClientIds));
    }


    /**
     * 业绩重算
     * @param $clientId
     * @param $referIds
     * @param $traceId
     * @param $isThrow
     * @return void
     */
    public function actionRefreshPerformanceByReferIds($clientId, $companyId, $deleteCompanyIds = '')
    {
        if (empty($clientId) || empty($companyId)) {

            self::info('param wrong');

            return;
        }
        $deleteCompanyIds = explode(',',$deleteCompanyIds);
        if (!empty($deleteCompanyIds)) {
            try {
                $recordList = new PerformanceV2RecordList($clientId);
                $recordList->setSkipOwnerCheck(true);
                $recordList->setCompanyId($deleteCompanyIds);
                $recordList->delete();
            } catch (\Throwable $exception) {
                $errMessage = $exception->getMessage();
                \LogUtil::error(__FUNCTION__ . "_error messages[{$errMessage}] companyIds", ['companyIds' => $deleteCompanyIds]);
            }
        }


        $sql = 'SELECT order_id
				FROM tbl_order
				WHERE client_id = '.$clientId.'
				  AND company_id = '.$companyId.'
				  AND enable_flag = 1';

        $db = PgActiveRecord::getDbByClientId($clientId);

        $orderList = $db->createCommand($sql)->queryAll();


        $sql = 'SELECT opportunity_id
				FROM tbl_opportunity
				WHERE client_id = '.$clientId.'
				  AND company_id = '.$companyId.'
				  AND enable_flag = 1';

        $db = PgActiveRecord::getDbByClientId($clientId);

        $opportunityList = $db->createCommand($sql)->queryAll();


        $sql = 'SELECT cash_collection_id
				FROM tbl_cash_collection
				WHERE client_id = '.$clientId.'
				  AND company_id = '.$companyId.'
				  AND enable_flag = 1';

        $db = PgActiveRecord::getDbByClientId($clientId);

        $collectionList = $db->createCommand($sql)->queryAll();


        $referMap = array_filter([

            Constants::TYPE_ORDER           => array_column($orderList, 'order_id'),
            Constants::TYPE_OPPORTUNITY     => array_column($opportunityList, 'opportunity_id'),
            Constants::TYPE_CASH_COLLECTION => array_column($collectionList, 'cash_collection_id'),
        ]);

        self::info('refer map: ' . json_encode($referMap));

        $client = new \common\library\account\Client($clientId);

        $adminUser = $client->getMasterUser();

        if (!$adminUser) {

            self::info('no admin user');

            return ;
        }

        User::setLoginUser($adminUser);


        foreach ($referMap as $type => $referIds) {

            if (empty($referIds)) {

                continue;
            }

            $list = \common\models\client\PerformanceV2Rule::model()->findAllByAttributes([

                'client_id'   => $clientId,
                'refer_type'  => $type,
                'enable_flag' => 1,
            ]);

            self::info('PerformanceV2Rule: '.json_encode($list));

            $list = array_filter($list);

            foreach ($list as $ruleInfo) {

                $this->actionRecordPerformanceByRuleInfoAndReferIds($ruleInfo->getAttributes(), $referIds, $companyId);
            }
        }

    }

    //根据rule_id和refer_id来记录业绩 仅供埋点使用，不应该用在补偿场景
    public function actionRecordPerformanceByRuleInfoAndReferIds($ruleInfo, $referIds, $orderCompanyId)
    {
        if (empty($ruleInfo) || empty($referIds)) {
            return;
        }

        if (!is_array($ruleInfo)) {
            $ruleInfo = json_decode($ruleInfo,true);
        }

        if (!is_array($referIds)) {
            $referIds = json_decode($referIds,true);
        }

        $clientId = $ruleInfo['client_id'];
        $referType = $ruleInfo['refer_type'];
        $ruleId = $ruleInfo['rule_id'];

        //重置客群的计算时间
        if ($ruleInfo['type'] == 2 && $ruleInfo['refer_type'] == Constants::TYPE_ORDER) {

            Helper::resetCompanyFieldWithTransactionOrder($clientId, $ruleId, $referIds);
        }

        LogUtil::info(" 清除referIds旧数据");
        LogUtil::info("开始计算关联对象referIds为:".implode(',',$referIds)."的绩效数据"."个数为:".count($referIds));

        // 清理旧数据
        $recordList = new PerformanceV2RecordList($clientId);
        $recordList->setReferType($referType);
        $recordList->setRuleId($ruleId);
        $recordList->setReferId($referIds);
        $recordList->delete();

        // 超级管理员
        $adminUserId = PrivilegeService::getInstance($clientId)->getAdminUserId();
        if (!$adminUserId) {
            LogUtil::info(" clientId:$clientId adminUser not exist!");
            return false;
        }
        User::setLoginUserById($adminUserId);

        $filters = is_string($ruleInfo['filters']) ? (json_decode($ruleInfo['filters'], true) ?? []) : $ruleInfo['filters'];

        $ruleConfig = new RuleConfig($clientId, $referType, [], $ruleInfo);
        $filterRunner = new WorkflowFilterRunner($ruleConfig);
        $filterRunner->setFilters($filters, $ruleInfo['criteria']);
        $filterRunner->setReferIds($referIds);
        $filterRunner->setFields('*');
        $succeed = true;
        $specialKeys = PerformanceV2Constant::REFER_SPECIAL_KEYS_MAP[$referType] ?? [];

        self::info(" ClientId[{$clientId}] RuleId[$ruleId] -- Begin -- ");
        $model = PerformanceV2Record::model();
        $dataMap = [];
        $mainCurrency = Client::getClient($clientId)->getMainCurrency();

        // 特殊字段在这里加
        $extraData = [
            'mainCurrency' => $mainCurrency,
            'specialKeys' => $specialKeys
        ];

        foreach ($filterRunner->iterate(1000) as $items)
        {
            self::info("count[  " . count($items) . "  ]");
            try {
                // 获取业绩计算结果
                list($userPerformanceRecords, $departmentPerformanceRecords) = Helper::getBatchRecordPerformances($referType, $items, $ruleInfo, $extraData);

                $allPerformanceRecords = array_merge($userPerformanceRecords, $departmentPerformanceRecords);
                if (empty($allPerformanceRecords)) continue;

                $recordId = PgActiveRecord::produceAutoIncrementId(count($allPerformanceRecords));

                foreach ($userPerformanceRecords as $key => $value)
                {
                    $userPerformanceRecords[$key]['record_id'] = $recordId;
                    $recordId --;
                }

                foreach ($departmentPerformanceRecords as $key => $value)
                {
                    $departmentPerformanceRecords[$key]['record_id'] = $recordId;
                    $recordId --;
                }


                // 业绩数据入库
                if (!empty($userPerformanceRecords)) {
                    $model->getDbConnection()->getCommandBuilder()->createMultipleInsertCommand($model->tableName(), $userPerformanceRecords)->execute();
                }

                if (!empty($departmentPerformanceRecords)) {
                    $model->getDbConnection()->getCommandBuilder()->createMultipleInsertCommand($model->tableName(), $departmentPerformanceRecords)->execute();
                }
            } catch (Exception $e) {
                self::info("错误信息是: [{$e->getMessage()}]");
                $succeed = false;
            }

            // 处理跟订单业绩相关数据
            if ($ruleInfo['type'] == 2 && $ruleInfo['refer_type'] == Constants::TYPE_ORDER) {
                $orderIds = array_column($items, 'order_id') ?? [];
                foreach ($items as $item) {
                    $companyId = $item['company_id'];
                    if (empty($companyId)) continue;

                    if (!isset($dataMap[$companyId])) $dataMap[$companyId] = $item['account_date'];
                    $dataMap[$companyId] < $item['account_date'] && $dataMap[$companyId] = $item['account_date'];
                }
                // 更改order的account_flag
                if (!empty($orderIds)) {
                    $orderBatchOperator = new OrderBatchOperator($adminUserId);
                    $orderBatchOperator->setParams([
                        'order_ids' => $orderIds,
                    ]);
                    $accountFlag = $ruleInfo['enable_flag'] ? true : false;
                    $orderBatchOperator->changeAccountFlag($accountFlag);
                }
            }
        }

        self::info( "ClientId[{$clientId}] RuleId[$ruleId] -- End -- \n");

        if ($ruleInfo['type'] == 2 && $ruleInfo['refer_type'] == Constants::TYPE_ORDER && !empty($dataMap)) {
            //预设成交订单规则 变更需要重新计算company_id的成交日期时间
            if ($ruleInfo['enable_flag'] == 1) {

                Helper::refreshCompanyFieldWithTransactionOrder($clientId, $ruleId, [$orderCompanyId]);

            }
        }

        return $succeed;
    }


    /**
     * 开启关闭了的预设成交订单金额绩效规则
     */
    public function actionResetPerformanceV2Order($clientIds = 0, $lastNumber = null, $start = 0)
    {

        if ($clientIds != 0 && is_string($clientIds)) {
            $clientIdList = explode(',', $clientIds);
        } else {
            //为0全量
            $clientList = self::getClientList($clientIds, true, null, null, $start, 0, $lastNumber);
            $clientIdList = array_column($clientList, 'client_id', '');
        }

        $amountTargetField = [
            'amount_rmb',
            'amount_usd',
            'product_total_amount_rmb',
            'product_total_amount_usd'
        ];

        foreach ($clientIdList as $clientId) {

            $db = PgActiveRecord::getDbByClientId($clientId);
            //查询业绩表里面的数据
            //找出预设规则为订单金额的rule_id
            $sql = "select * from tbl_performance_v2_rule where client_id={$clientId}  and type=" . \common\library\performance_v2\PerformanceV2Constant::RULE_TYPE_ORDER_AMOUNT;
            $rule = $db->createCommand($sql)->queryRow();

            if (empty($rule)) {
                continue;
            }

            $targetField = $rule['target_field'] ?? '';
            $ruleId = $rule['rule_id'] ?? '';
            $name = $rule['name'] ?? '';


            if ($ruleId) {

                $runRuleId = [];

                //配置了金额的
                if (in_array($targetField,$amountTargetField)) {

                    if (!$rule['enable_flag']) {
                        //开启rule_id
                        $updateSql = "update tbl_performance_v2_rule set enable_flag = 1 where client_id ={$clientId} and rule_id={$rule['rule_id']}";
                        $db->createCommand($updateSql)->execute();
                        self::info("clientId:".$clientId." 重新开启该绩效");
                        $runRuleId[] = $ruleId;
                        continue;
                    }

                    //没有配置金额的
                } else {


                    //判断名字是否更改
                    if ($name != "成交订单金额") {

                        //新建一份该配置的绩效规则，跟随状态
                        $newRuleId = ProjectActiveRecord::produceAutoIncrementId();
                        $newParam = [
                            $newRuleId,
                            "'".$rule['name']."'",
                            "'".$rule['description']."'",
                            $clientId,
                            $rule['refer_type'],
                            "'" .$rule['filters'] . "'",
                            "'".$rule['time_field']."'",
                            "'".$rule['target_field']."'",
                            "'".$rule['calculate_rule']."'",
                            "'".$rule['performance_field']."'",
                            "'".$rule['criteria_type']."'",
                            "'".$rule['criteria']."'",
                            PerformanceV2Constant::RULE_TYPE_COMMON,
                            $rule['enable_flag'],
                        ];
                        $insertArr[] = "(" . implode(',', $newParam). ")";
                    }

                    //reset回成交订单金额的配置
                    $mainCurrency = Client::getClient($clientId)->getMainCurrency();

                    $statusService = new InvoiceStatusService($clientId, \Constants::TYPE_ORDER);
                    $orderEndStatus = $statusService->endStatus();
                    $saveStatus = [];
                    foreach ($orderEndStatus as $status)
                    {
                        if ($status['name'] === '已作废' || $status['name'] === '售后' || $status['name'] === '交易取消')
                            continue;

                        $saveStatus[] = $status['id'];
                    }

                    $filters = [[
                        'value' => $saveStatus,
                        'filter_no' => 1,
                        'refer_type' => 2,
                        'field' => 'status',
                        'field_type' => 3,
                        'operator' => 'in',
                        'date_type' => '',
                        'unit' => '',
                    ]];
                    $param = [
                        $rule['rule_id'],
                        "'成交订单金额'",
                        "'考核订单的订单金额，仅指定订单状态的订单会被计入绩效。'",
                        $clientId,
                        Constants::TYPE_ORDER,
                        "'" . json_encode($filters) . "'",
                        "'account_date'",
                        $mainCurrency == 'CNY' ? "'amount_rmb'" : "'amount_usd'",
                        "'sum'",
                        "'users'",
                        1,
                        "'(1)'",
                        PerformanceV2Constant::RULE_TYPE_ORDER_AMOUNT,
                        PerformanceV2Constant::ENABLE_FLAG_TRUE
                    ];
                    $insertArr[] = "(" . implode(',', $param). ")";
                    $sql = "insert into tbl_performance_v2_rule (rule_id, name, description, client_id, refer_type, filters, time_field, target_field, calculate_rule, performance_field, criteria_type, criteria, type, enable_flag) values " . implode(',', $insertArr) . " on conflict(rule_id) do update set name=EXCLUDED.name, description=EXCLUDED.description, refer_type=EXCLUDED.refer_type, filters=EXCLUDED.filters, time_field=EXCLUDED.time_field, target_field=EXCLUDED.target_field, calculate_rule=EXCLUDED.calculate_rule, performance_field=EXCLUDED.performance_field, criteria_type=EXCLUDED.criteria_type, criteria=EXCLUDED.criteria, type=EXCLUDED.type, enable_flag=EXCLUDED.enable_flag ";

                    $db->createCommand($sql)->execute();

                    $runRuleId[] = $ruleId;

                    if (isset($newRuleId) && !empty($newRuleId)) {
                        $runRuleId[] = $newRuleId;
                    }

                    self::info("clientId:".$clientId." 新建新的绩效并重置开启该绩效");
                }

                if (!empty($runRuleId)) {
                    //执行rule的数据
                    foreach ($runRuleId as $ruleId) {
                        $this->actionRecordPerformanceByRuleId($clientId,$ruleId);
                    }
                } else {
                    self::info("clientId:".$clientId." 没有需要处理的数据");
                }
            }
        }
    }

    public function actionResetRuleTaskStatus($clientId, $ruleId)
    {
//        $clientId = 23272;
//        $ruleId = 22376957456;
        //重置ruletask status=1，并且时间是在当前时间之前的任务
        $db = PgActiveRecord::getDbByClientId($clientId);
        $beforeDateTime = date("Y-m-d 00:00:00", time());
        $updateSql = "update tbl_performance_v2_task set  status =" . PerformanceV2Constant::TASK_STATUS_FAILED . " where client_id=$clientId and rule_id=$ruleId and status=" . PerformanceV2Constant::TASK_STATUS_START . " and create_time < '$beforeDateTime'";
        $db->createCommand($updateSql)->execute();
    }

    //修复有两个以上的成交订单金额绩效数据
    public function actionResetOrderPerformanceRule($clientIds = 0, $lastNumber = null, $start = 0)
    {
        if ($clientIds != 0 && is_string($clientIds)) {
            $clientIdList = explode(',', $clientIds);
        } else {
            //为0全量
            $clientList = self::getClientList($clientIds, true, null, null, $start, 0, $lastNumber);
            $clientIdList = array_column($clientList, 'client_id', '');
        }


        foreach ($clientIdList as $clientId) {
            $db = PgActiveRecord::getDbByClientId($clientId);

            if(empty($db)) {
                continue;
            }

            //查询业绩表里面的数据
            //找出预设规则为订单金额的rule_id
            $sql = "select * from tbl_performance_v2_rule where client_id={$clientId}  and type=" . \common\library\performance_v2\PerformanceV2Constant::RULE_TYPE_ORDER_AMOUNT;
            $rules = $db->createCommand($sql)->queryAll(true);

            if (empty($rules)) {
                self::info("没有成交订单金额绩效规则");
                continue;
            }

            if (count($rules)<2) {
                self::info("client_id:".$clientId." 无需修改");
            }

            $needUpdateRuleIds = [];
            foreach ($rules as $rule) {

                if ($rule['name'] != "成交订单金额" || $rule['refer_type'] !=\Constants::TYPE_ORDER) {
                    $needUpdateRuleIds[] = $rule['rule_id'];
                    //todo 打印出来
                    print_r($rule);
                }

            }

            if (empty($needUpdateRuleIds)) {
                self::info("没有需要更新的绩效规则");
                continue;
            }

            $ruleIdStr = implode(",",$needUpdateRuleIds);
            //更新成交订单金额的type为1
            $updateSql = "update tbl_performance_v2_rule set type=".\common\library\performance_v2\PerformanceV2Constant::RULE_TYPE_COMMON ." where client_id=$clientId and rule_id in ($ruleIdStr)";
            self::info("更新sql:".$updateSql);
            $db->createCommand($updateSql)->execute();
            self::info("client_id:".$clientId."更新数据完毕");
        }


    }

    public function actionImportGoals(int $user_id, int $import_id):void
    {
        $beginTime = microtime(true);
        self::info('begin: user_id:'.$user_id.' import_id:'.$import_id);

        \User::setLoginUserById($user_id);
        $user = \User::getLoginUser();

        $import = new \common\library\import\Import($user->getClientId(), $import_id);

        $importExecutor = new \common\library\performance_v2\goal\PerformanceV2GoalImportExecutor($import);
        $importExecutor->run();

        $endTime = microtime(true);

        self::info('finish: user_id:' . $user_id . ' import_id:' . $import_id . ' time:' . ($endTime - $beginTime));
    }

    public function actionSetSystemPredeterminedRules($clientIds = '')
    {
        $whiteList = [9650];
        $clientIds = explode(',', $clientIds);
        foreach ($clientIds as $clientId)
        {
            if (!in_array($clientId,$whiteList)) continue;
            $ruleIds = Helper::SetSystemPredeterminedRules($clientId);

            foreach ($ruleIds as $ruleId)
            {
                $this->actionRecordPerformanceByRuleId($clientId,$ruleId);
            }

            $ruleStr = implode(",",$ruleIds);
            self::info("SetSystemPredeterminedRules clientId:{$clientId} complete!");
            self::info("SetSystemPredeterminedRules ruleIds:{$ruleStr} complete!");
        }

    }

    // 根据clientId重跑绩效
    public function actionFixPerformanceRecordByClientIds($clientIds)
    {
        $clientIds = explode(',', $clientIds);
        foreach ($clientIds as $clientId)
        {
            self::info("当前是client [$clientId]");
            $db = PgActiveRecord::getDbByClientId($clientId);
            $ruleIds = $db->createCommand("select rule_id from tbl_performance_v2_rule where client_id = {$clientId} and enable_flag = 1")->queryColumn();
            foreach ($ruleIds as $ruleId)
            {
                $this->actionRecordPerformanceByRuleId($clientId, $ruleId);
            }
        }
    }

    /**
     * 自动化测试绩效模块
     * @param int $clientId
     * @param int $type  1 -> 结果目标 2->动态时间 3->动态时间公式字段
     * @return void
     */
    public function actionTestPerformanceV2Record($clientId = 9650,$type = 1)
    {
        $ruleInfoArr = [
            // 结果
            1 => [
                [
                    'rule_id' => PgActiveRecord::produceAutoIncrementId(1),
                    'client_id' => $clientId,
                    'refer_type' => \Constants::TYPE_ORDER,
                    'filters' => [],
                    'enable_flag' => 1,
                    'time_field' => 'create_time',
                    'target_field' => '',
                    'calculate_rule' => 'count',
                    'performance_field' => 'create_user',
                    'criteria_type' => 0,
                    'criteria' => 0,
                    'performance_type' => 1,
                    'type' => 1,
                    'create_time' => "2021-03-23 10:17:27",
                    'update_time' => "2021-03-23 10:17:27"
                ],
                // 回款单
                [
                    'rule_id' => PgActiveRecord::produceAutoIncrementId(1),
                    'client_id' => $clientId,
                    'refer_type' => \Constants::TYPE_CASH_COLLECTION,
                    'filters' => [],
                    'enable_flag' => 1,
                    'time_field' => 'create_time',
                    'target_field' => '',
                    'calculate_rule' => 'count',
                    'performance_field' => 'create_user_id',
                    'criteria_type' => 0,
                    'criteria' => 0,
                    'performance_type' => 1,
                    'type' => 1,
                    'create_time' => "2021-03-23 10:17:27",
                    'update_time' => "2021-03-23 10:17:27"
                ],
                // 报价单
                [
                    'rule_id' => PgActiveRecord::produceAutoIncrementId(1),
                    'refer_type' => \Constants::TYPE_QUOTATION,
                    'client_id' => $clientId,
                    'filters' => '[{"unit": "", "field": "amount_rmb", "value": 10, "operator": ">", "filter_no": 1, "field_type": "5", "refer_type": "3"}]',
                    'enable_flag' => 1,
                    'time_field' => 'quotation_date',
                    'target_field' => 'amount_rmb',
                    'calculate_rule' => 'sum',
                    'performance_field' => 'create_user',
                    'criteria_type' => 1,
                    'criteria' => "(1)",
                    'performance_type' => 1,
                    'type' => 1,
                    'create_time' => "2021-03-23 10:17:27",
                    'update_time' => "2021-03-23 10:17:27"
                ],
                // 产品
                [
                    'rule_id' => PgActiveRecord::produceAutoIncrementId(1),
                    'client_id' => $clientId,
                    'refer_type' => \Constants::TYPE_PRODUCT,
                    'filters' => '{}',
                    'enable_flag' => 1,
                    'time_field' => 'create_time',
                    'target_field' => '',
                    'calculate_rule' => 'count',
                    'performance_field' => 'create_user',
                    'criteria_type' => 1,
                    'criteria' => "(1)",
                    'performance_type' => 1,
                    'type' => 1,
                    'create_time' => "2021-03-23 10:17:27",
                    'update_time' => "2021-03-23 10:17:27"
                ],
                // 商机
                [
                    'rule_id' => PgActiveRecord::produceAutoIncrementId(1),
                    'client_id' => $clientId,
                    'refer_type' => \Constants::TYPE_OPPORTUNITY,
                    'filters' => '[{"unit": "", "field": "currency", "value": "USD", "operator": "=", "filter_no": 1, "field_type": 3, "refer_type": "9"}]',
                    'enable_flag' => 1,
                    'time_field' => 'account_date',
                    'target_field' => 'amount_usd',
                    'calculate_rule' => 'sum',
                    'performance_field' => 'main_user',
                    'criteria_type' => 1,
                    'criteria' => "(1)",
                    'performance_type' => 1,
                    'type' => 1,
                    'create_time' => "2021-03-23 10:17:27",
                    'update_time' => "2021-03-23 10:17:27"
                ],
                // 客户
                [
                    'rule_id' => PgActiveRecord::produceAutoIncrementId(1),
                    'client_id' => $clientId,
                    'refer_type' => \Constants::TYPE_COMPANY,
                    'filters' => '[{"unit": "", "field": "transaction_order_amount", "value": 10, "operator": ">", "filter_no": 1, "field_type": 5, "refer_type": "4"}]',
                    'enable_flag' => 1,
                    'time_field' => 'archive_time',
                    'target_field' => '',
                    'calculate_rule' => 'count',
                    'performance_field' => 'create_user',
                    'criteria_type' => 1,
                    'criteria' => "(1)",
                    'performance_type' => 1,
                    'type' => 1,
                    'create_time' => "2021-03-23 10:17:27",
                    'update_time' => "2021-03-23 10:17:27"
                ],
                // 线索
                [
                    'rule_id' => PgActiveRecord::produceAutoIncrementId(1),
                    'client_id' => $clientId,
                    'refer_type' => \Constants::TYPE_LEAD,
                    'filters' => '[{"unit": "", "field": "order_time", "value": -3, "operator": "later", "filter_no": 1, "field_type": 10, "refer_type": "7"}]',
                    'enable_flag' => 1,
                    'time_field' => 'create_time',
                    'target_field' => '',
                    'calculate_rule' => 'count',
                    'performance_field' => 'create_user',
                    'criteria_type' => 1,
                    'criteria' => "(1)",
                    'performance_type' => 1,
                    'type' => 1,
                    'create_time' => "2021-03-23 10:17:27",
                    'update_time' => "2021-03-23 10:17:27"
                ],
                // 营销邮件
                [
                    'rule_id' => PgActiveRecord::produceAutoIncrementId(1),
                    'client_id' => $clientId,
                    'refer_type' => \Constants::TYPE_EDM,
                    'filters' => '{}',
                    'enable_flag' => 1,
                    'time_field' => 'create_time',
                    'target_field' => 'send_to_count',
                    'calculate_rule' => 'count',
                    'performance_field' => 'user_id',
                    'criteria_type' => 1,
                    'criteria' => "(1)",
                    'performance_type' => 1,
                    'type' => 1,
                    'create_time' => "2021-03-23 10:17:27",
                    'update_time' => "2021-03-23 10:17:27"
                ],
            ],
            // 动态时间
            2 => [
                // 订单
                [
                    'rule_id' => PgActiveRecord::produceAutoIncrementId(1),
                    'client_id' => $clientId,
                    'refer_type' => \Constants::TYPE_ORDER,
                    'filters' => '[{"unit": "USD", "field": "amount", "value": 100, "operator": ">", "filter_no": 1, "unit_type": "currency", "field_type": "5", "refer_type": "2"}]',
                    'enable_flag' => 1,
                    'time_field' => 'update_time',
                    'target_field' => '',
                    'calculate_rule' => 'count',
                    'performance_field' => 'create_user',
                    'criteria_type' => 1,
                    'criteria' => "(1)",
                    'performance_type' => 1,
                    'type' => 1,
                    'create_time' => "2021-03-23 10:17:27",
                    'update_time' => "2021-03-23 10:17:27"
                ],
                // 商机
                [
                    'rule_id' => PgActiveRecord::produceAutoIncrementId(1),
                    'client_id' => $clientId,
                    'refer_type' => \Constants::TYPE_OPPORTUNITY,
                    'filters' => '{}',
                    'enable_flag' => 1,
                    'time_field' => 'stage_edit_time',
                    'target_field' => '',
                    'calculate_rule' => 'count',
                    'performance_field' => 'main_user',
                    'criteria_type' => 1,
                    'criteria' => "(1)",
                    'performance_type' => 1,
                    'type' => 1,
                    'create_time' => "2021-03-23 10:17:27",
                    'update_time' => "2021-03-23 10:17:27"
                ],
                // 客户
                [
                    'rule_id' => PgActiveRecord::produceAutoIncrementId(1),
                    'client_id' => $clientId,
                    'refer_type' => \Constants::TYPE_COMPANY,
                    'filters' => '[{"unit": "", "field":"ongoing_opportunity_count", "value": 1, "operator": ">", "filter_no": 1, "field_type": 5, "refer_type": "4"}]',
                    'enable_flag' => 1,
                    'time_field' => 'edit_time',
                    'target_field' => '',
                    'calculate_rule' => 'count',
                    'performance_field' => 'last_owner',
                    'criteria_type' => 1,
                    'criteria' => "(1)",
                    'performance_type' => 1,
                    'type' => 1,
                    'create_time' => "2021-03-23 10:17:27",
                    'update_time' => "2021-03-23 10:17:27"
                ],
                // 线索
                [
                    'rule_id' => PgActiveRecord::produceAutoIncrementId(1),
                    'client_id' => $clientId,
                    'refer_type' => \Constants::TYPE_COMPANY,
                    'filters' => '[{"unit": "", "field": "origin", "value": "10", "operator": "=", "filter_no": 1, "field_type": "3", "refer_type": "7"}]',
                    'enable_flag' => 1,
                    'time_field' => 'order_time',
                    'target_field' => '',
                    'calculate_rule' => 'count',
                    'performance_field' => 'create_user',
                    'criteria_type' => 1,
                    'criteria' => "(1)",
                    'performance_type' => 1,
                    'type' => 1,
                    'create_time' => "2021-03-23 10:17:27",
                    'update_time' => "2021-03-23 10:17:27"
                ],
            ],
            // 过程 公式字段
            3 => [
                // 订单 -> 关联回款单字段
                [
                    'rule_id' => PgActiveRecord::produceAutoIncrementId(1),
                    'client_id' => $clientId,
                    'refer_type' => \Constants::TYPE_ORDER,
                    'filters' => '[{"unit": "", "field": "product_total_amount_usd", "value": 100, "operator": ">", "filter_no": 1, "field_type": "5", "refer_type": "2"}]',
                    'enable_flag' => 1,
                    'time_field' => 'create_time',
                    'target_field' => '',
                    'calculate_rule' => 'sum',
                    'performance_field' => 'create_user',
                    'criteria_type' => 1,
                    'criteria' => "(1)",
                    'performance_type' => 1,
                    'type' => 1,
                    'create_time' => "2021-03-23 10:17:27",
                    'update_time' => "2021-03-23 10:17:27",
                    'formula' => '{"name": "test", "calculate": "{2.product_total_count}+{2.collect_amount}+{9.amount_usd}", "field_type": ["2", "9"], "calculate_fields": ["2.product_total_count", "2.collect_amount", "9.amount_usd"]}'
                ],
                [
                    'rule_id' => PgActiveRecord::produceAutoIncrementId(1),
                    'client_id' => $clientId,
                    'refer_type' => \Constants::TYPE_CASH_COLLECTION,
                    'filters' => '{}',
                    'enable_flag' => 1,
                    'time_field' => 'collection_date',
                    'target_field' => '',
                    'calculate_rule' => 'sum',
                    'performance_field' => 'users',
                    'criteria_type' => 0,
                    'criteria' => "",
                    'performance_type' => 1,
                    'type' => 1,
                    'create_time' => "2021-03-23 10:17:27",
                    'update_time' => "2021-03-23 10:17:27",
                    'formula' => '{"name": "hshshs", "calculate": "{2.amount_usd}+{9.amount_usd}+{10.real_amount_usd}", "field_type": ["2", "9", "10"], "calculate_fields": ["2.amount_usd", "9.amount_usd", "10.real_amount_usd"]}'
                ],

            ]
        ];
        $ruleIds = [];

        foreach ($ruleInfoArr[$type] as $ruleInfo) {
            $ruleIds[] = $ruleInfo['rule_id'];
            $performanceV2RuleFormatter = new \common\library\performance_v2\rule\PerformanceV2RuleFormatter($clientId);
            $ruleInfo = $performanceV2RuleFormatter->strip($ruleInfo);
            $this->actionRecordPerformanceByRuleInfo($ruleInfo);
        }


        foreach ($ruleIds as $ruleId){
            $performanceRecordList = new PerformanceV2RecordList($clientId);
            $performanceRecordList->setRuleId($ruleId);
            $performanceRecordList->delete();
        }
    }

    /**
     * 切换币种时 重算对应cost_list脚本绩效脚本
     * @param $clientId
     * @return void
     * @throws ProcessException
     */
    public function actionReRunPerformanceAssociatedMainCurrency($clientId)
    {
        $db = PgActiveRecord::getDbByClientId($clientId);

        $sql = "select distinct rule_id from tbl_performance_v2_rule where client_id = {$clientId} and (target_field like 'cost_list%' or target_field in ('addition_cost_amount','cost_with_tax_total') or formula::json #>>'{calculate_fields}' LIKE 'cost_list%'  or formula::json #>>'{calculate_fields}' LIKE '%addition_cost_amount%' or formula::json #>>'{calculate_fields}' LIKE '%cost_with_tax_total%' ) and delete_flag = 0";

        $ruleIds = $db->createCommand($sql)->queryColumn();

        foreach ($ruleIds as $ruleId)
        {
            $this->actionRecordPerformanceByRuleId($clientId,$ruleId);
        }

        \LogUtil::info('-----ReRunPerformanceAssociatedMainCurrency-----complete! ',[
            'clientId' => $clientId,
            'ruleId' => $ruleIds
        ]);


    }

    public function actionResetDefaultRule($clientIds = '')
    {
        if (empty($clientIds)) return;
        $clientIdArr = explode(',',$clientIds);
        foreach ($clientIdArr as $clientId)
        {
            $db = PgActiveRecord::getDbByClientId($clientId);
            \common\library\performance_v2\Helper::initClientPrepareRuleByClient($clientId);
            $sql = "select rule_id from tbl_performance_v2_rule where client_id = {$clientId} and type <> 1";
            $ruleIds = $db->createCommand($sql)->queryColumn();
            foreach ($ruleIds as $ruleId)
            {
                $this->actionRecordPerformanceByRuleId($clientId, $ruleId);
            }

            Util::logInfo('----ResetDefaultRule-----complete!',[
                'clientId' => $clientId,
                'ruleIds' => $ruleIds
            ]);

        }
    }

    // 这里用于异步执行
    public  function actionResetCompanyFieldWithTransactionOrder($clientId, $ruleId, $orderId = [])
    {
        Util::logInfo('------异步执行重制客户时间-----begin',[
            'clientId' => $clientId,
            'ruleId' => $ruleId,
        ]);

        $adminUserId = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();
        User::setLoginUserById($adminUserId);

        Helper::resetCompanyFieldWithTransactionOrder($clientId,$ruleId,$orderId);
    }

    // ./yiic-omg performanceV2 runSystemPredeterminedRules --type=1 --greyClientIdsStr='37434,52819,56574,81465,3468,29824,52261,36713,86820,23883,33127,70547,44249,16189,83032,35208,22558,2639,36880,81465,59702,33734,51613,41661,28182,28426,29158,15220,44249,70547,53169,77241,33668,66647,83032,52261,16189,5234,56106,67066,9550'
    // ./yiic-test performanceV2 runSystemPredeterminedRules --type=1 --greyClientIdsStr='9650' --sync=0
    // ./yiic-omg performanceV2 runSystemPredeterminedRules --type=1 --greyClientIdsStr='37434,52819,56574,81465,3468,29824,52261,36713,86820,23883,33127,70547,44249,16189,83032,35208,22558,2639,36880,81465,59702,33734,51613,41661,28182,28426,29158,15220,44249,70547,53169,77241,33668,66647,83032,52261,16189,5234,56106,67066,9550' --sync=1
    // ./yiic-omg performanceV2 runSystemPredeterminedRules --type=2 --greyNumber='11' --excludeClientIdsStr='37434,52819,56574,81465,3468,29824,52261,36713,86820,23883,33127,70547,44249,16189,83032,35208,22558,2639,36880,81465,59702,33734,51613,41661,28182,28426,29158,15220,44249,70547,53169,77241,33668,66647,83032,52261,16189,5234,56106,67066,9550' --sync=1
    public function actionRunSystemPredeterminedRules(string $clientIdsStr='', string $greyNumStr='', $sync=0, $excludeClientIdsStr='')
    {
        $clientIds = explode(',', $clientIdsStr);
        $greyNumList = explode(',', $greyNumStr);
        $greyClientIds = [];
        foreach ($greyNumList as $greyNum)
        {
            $itemGreyClientIds = array_column($this->getClientList(0, true, true, null, 0, 0, $greyNum), 'client_id');
            $greyClientIds = array_merge($greyClientIds, $itemGreyClientIds);
        }
        $needToSelectClientIds = array_values(array_unique(array_filter(array_merge($greyClientIds, $clientIds))));

//        $typeCondition = implode(',', [\common\library\performance_v2\PerformanceV2Constant::RULE_TYPE_COMPANY_COUNT, \common\library\performance_v2\PerformanceV2Constant::RULE_TYPE_FOLLOW_UP_COUNT]);
        if (!empty($excludeClientIds)) {
            $excludeClientIds = explode(',', $excludeClientIdsStr);
            $needToSelectClientIds = array_diff($needToSelectClientIds, $excludeClientIds);
        }
        if (empty($needToSelectClientIds)) return;
        self::info("执行的clientID是 [ " . json_encode($needToSelectClientIds) . " ]");

        $typeCondition = implode(',', [\common\library\performance_v2\PerformanceV2Constant::RULE_TYPE_COMPANY_COUNT]);
        foreach ($needToSelectClientIds as $clientId)
        {
            $db = \PgActiveRecord::getDbByClientId($clientId);
            // 邮件规则不重跑数据
            $sql = "select rule_id from tbl_performance_v2_rule where client_id = {$clientId} and type in ($typeCondition) and enable_flag = 1";
            $ruleIds = $db->createCommand($sql)->queryColumn();
            self::info(sprintf("clientId[$clientId] -- ruleIds[%s] -- 备注[%s]", json_encode($ruleIds), empty($ruleIds) ? '无预设规则' : ''));
            if (empty($ruleIds)) continue;

            if ($sync) {
                foreach ($ruleIds as $ruleId) {
                    self::info("clientId[$clientId] 同步执行绩效计算[$ruleId]");
                    $this->actionRecordPerformanceByRuleId($clientId, $ruleId);
                }
            } else {
                foreach ($ruleIds as $ruleId) {
                    self::info("clientId[$clientId] 投递任务[$ruleId]");
                    \common\library\CommandRunner::run(
                        'performanceV2',
                        'recordPerformanceByRuleId',
                        [
                            'clientId' => $clientId,
                            'ruleId' => $ruleId,
                        ]
                    );
                }
            }
        }
    }

    // 查询默认绩效规则的数量
    // ./yiic-test performanceV2 getPerformanceRecordCount --clientIdsStr='9650'
    // ./yiic-omg performanceV2 getPerformanceRecordCount --clientIdsStr='37434,52819,56574,81465,3468,29824,52261,36713,86820,23883,33127,70547,44249,16189,83032,35208,22558,2639,36880,81465,59702,33734,51613,41661,28182,28426,29158,15220,44249,70547,53169,77241,33668,66647,83032,52261,16189,5234,56106,67066,9550' --greyNumStr='11'
    public function actionGetPerformanceRecordCount(string $clientIdsStr='', string $greyNumStr='', $all=0)
    {
        if ($all) {
            $needToSelectClientIds = array_column($this->getClientList(0, true, true), 'client_id');
        } else {
            $clientIds = explode(',', $clientIdsStr);
            $greyNumList = explode(',', $greyNumStr);
            $greyClientIds = [];
            foreach ($greyNumList as $greyNum)
            {
                $itemGreyClientIds = array_column($this->getClientList(0, true, true, null, 0, 0, $greyNum), 'client_id');
                $greyClientIds = array_merge($greyClientIds, $itemGreyClientIds);
            }
            $needToSelectClientIds = array_values(array_unique(array_filter(array_merge($greyClientIds, $clientIds))));
        }

        $totalMap = [];
        $referClientTotalMap = [];
        foreach ($needToSelectClientIds as $clientId)
        {
            $db = \PgActiveRecord::getDbByClientId($clientId);
            if (empty($db)) continue;

            $typeCondition = implode(',', [PerformanceV2Constant::RULE_TYPE_COMPANY_COUNT, PerformanceV2Constant::RULE_TYPE_FOLLOW_UP_COUNT]);
            $selectRuleSql = "select rule_id,refer_type from tbl_performance_v2_rule where client_id = {$clientId} and type in ($typeCondition) and enable_flag = 1";
            $ruleList = array_column($db->createCommand($selectRuleSql)->queryAll(), 'refer_type', 'rule_id');
            foreach ($ruleList as $itemRuleId => $itemRuleReferType)
            {
                $countSql = "select count(1) as total from tbl_performance_v2_record where client_id = {$clientId} and rule_id={$itemRuleId}";
                $total = $db->createCommand($countSql)->queryScalar();
                if ($total > 2000) {
                    $totalMap[$itemRuleReferType][$clientId][$itemRuleId] = $total;
                    $referClientTotalMap[$itemRuleReferType][$clientId] = $total;
                }
            }
        }
        foreach ($referClientTotalMap as $referType => &$list)
        {
            arsort($list);
        }
        \Util::batchLogInfo([
            'referClientTotalMap' => $referClientTotalMap,
            'totalMap' => $totalMap
        ]);
        self::info(sprintf("排行[%s] 排行详细[%s]", json_encode($referClientTotalMap), json_encode($totalMap)));
    }

    /**
     *
     * ./yiic-omg performancev2 FixCountPerformance --type=2 --greyNumber=111
     *
     * 修复count类型的绩效数据
     * @return void
     */
    public function actionFixCountPerformance(int $type = 1, string $greyClientIdsStr = '', int $greyNumber = 0)
    {
        $greyClientIds = [];
        switch ($type) {
            case 1: //灰度clientIds
                self::info("灰度clientIds为{$greyClientIdsStr}的clientId");
                $greyClientIds = explode(',', $greyClientIdsStr);
                break;
            case 2: //灰度尾号
                var_dump("灰度尾号为{$greyNumber}的clientId");
                self::info("灰度尾号为{$greyNumber}的clientId");
                $greyClientIds = array_column($this->getClientList(0, false, null, null, 0, 0, $greyNumber), 'client_id');
                break;
            case 3: //全量
                self::info("全量client");
                $greyClientIds = array_column($this->getClientList(), 'client_id');
                break;
            default:
                break;
        }

        $ignoreType = implode(",",[\Constants::TYPE_MAIL,\Constants::TYPE_FOLLOWUP]);

        if (empty($greyClientIds)) return;

        foreach ($greyClientIds as $clientId)
        {
            $db = PgActiveRecord::getDbByClientId($clientId);
            $db->setActive(true);

            $sql = "select * from tbl_performance_v2_rule where client_id = {$clientId} and enable_flag = 1 and delete_flag = 0 
                                        and calculate_rule = 'count' and refer_type not in ({$ignoreType})";

            $ruleInfos = $db->createCommand($sql)->queryAll();
            $ruleIds = array_column($ruleInfos,'rule_id');
            if (empty($ruleInfos))
            {
                $db->setActive(false);
                continue;
            }
            // 重算绩效
            foreach ($ruleInfos as $ruleInfo)
            {
                $performanceV2RuleFormatter = new \common\library\performance_v2\rule\PerformanceV2RuleFormatter($clientId);
                $ruleInfo = $performanceV2RuleFormatter->strip($ruleInfo);
                $this->actionRecordPerformanceByRuleInfo($ruleInfo);
            }
            $ruleIdsStr = implode(",",$ruleIds);
            \LogUtil::info("FixCountPerformance --clientId[$clientId] --ruleIds=[$ruleIdsStr]");
            $db->setActive(false);
        }
        $clientIds = implode(",",$greyClientIds);
        \LogUtil::info("FixCountPerformance complete! --clientIds[$clientIds]");
    }

    // 全量修复绩效
    public function actionFixPerformance(string $clientIdsStr='', string $greyNumStr='', $sync=0, $excludeClientIdsStr='')
    {
        $clientIds = array_filter(explode(',', $clientIdsStr));
        $greyNumList = array_filter(explode(',', $greyNumStr));
        $greyClientIds = [];

        foreach ($greyNumList as $greyNum)
        {
            $itemGreyClientIds = array_column($this->getClientList(0, true, true, null, 0, 0, $greyNum), 'client_id');
            $greyClientIds = array_merge($greyClientIds, $itemGreyClientIds);
        }
        $needToSelectClientIds = array_values(array_unique(array_filter(array_merge($greyClientIds, $clientIds))));
        if (!empty($excludeClientIds)) {
            $excludeClientIds = explode(',', $excludeClientIdsStr);
            $needToSelectClientIds = array_diff($needToSelectClientIds, $excludeClientIds);
        }
        if (empty($needToSelectClientIds)) return;
        self::info("执行的clientID是 [ " . json_encode($needToSelectClientIds) . " ]");

        foreach ($needToSelectClientIds as $clientId)
        {
            try {
                $db = \PgActiveRecord::getDbByClientId($clientId);
                if (empty($db)) continue;
                $db->setActive(true);
                $sql = "select rule_id from tbl_performance_v2_rule where client_id = {$clientId} and refer_type = 6 and enable_flag = 1";
                $ruleIds = $db->createCommand($sql)->queryColumn();
                if (empty($ruleIds)) {
                    $db->setActive(false);
                    continue;
                }
                self::info(sprintf("clientId[$clientId] ruleIds[%s]", json_encode($ruleIds)));
                foreach ($ruleIds as $ruleId) {
                    self::info("clientId[$clientId] 同步执行绩效计算[$ruleId]");
                    $this->actionRecordPerformanceByRuleId($clientId, $ruleId);
                }
                $db->setActive(false);
            } catch (Exception $e) {
                self::info("clientId[$clientId] 执行异常 信息是[{$e->getMessage()}] trace[{$e->getTraceAsString()}]");
            }
        }
    }

    public function actionRunPresetPerformanceRule()
    {
        $clientIds = array_column($this->getClientList(0, true, true, null, 0, 0), 'client_id');
//        $clientIds = [14058];
        foreach ($clientIds as $clientId) {
            $privilegeService = PrivilegeService::getInstance($clientId);
            if (!in_array($privilegeService->getMainSystemId(), [
                \common\library\privilege_v3\PrivilegeConstants::CRM_PLUS_SYSTEM_ID,
                \common\library\privilege_v3\PrivilegeConstants::CRM_LITE_SYSTEM_ID,
                \common\library\privilege_v3\PrivilegeConstants::CRM_SYSTEM_ID
            ])) {
                continue;
            }
            $db = PgActiveRecord::getDbByClientId($clientId);
            $typeArr = [PerformanceV2Constant::RULE_TYPE_COMPANY_COUNT,PerformanceV2Constant::RULE_TYPE_SEND_MAIL_COUNT];
            $typeStr = implode(',',$typeArr);
            $sql = "select rule_id from tbl_performance_v2_rule where client_id={$clientId} and type in ({$typeStr})";
            $ruleIds = $db->createCommand($sql)->queryColumn();
            foreach ($ruleIds as $ruleId)
            {
                $this->actionRecordPerformanceByRuleId($clientId, $ruleId);
            }

            self::info(sprintf("runPresetPerformanceRule --ruleId[%s]",$typeStr));
        }
    }

    public function actionPresetSystemRule0506($type = 6)
    {
        $ignoreClientIdMap = [
            PerformanceV2Constant::RULE_TYPE_COMPANY_COUNT => [
                1209, 6698, 9596, 38130, 700, 777, 11229, 108, 1984, 2349, 37001, 42242, 3525, 2847, 2228, 5039, 26980, 16717, 36599, 493, 2089, 2601, 10241, 41500, 21745, 31399, 3092, 615, 4512, 34996, 4038, 2595, 21066, 33646, 30455,
                37722, 37039, 38895, 43336, 2582, 24397, 4355, 32911, 30099, 5101, 27677, 4240, 38858, 28549, 42303, 3095, 2740,
                18493, 64, 37539, 1611, 30578, 34847, 29001, 30148, 27795, 21742, 35555, 112, 23432, 37181, 42037, 39710,
                42960, 39734, 31752, 32953, 3451, 3922, 31756, 36343, 37641, 43325, 1950, 40977, 27068, 31890, 43187, 774, 37220,
                4330, 43088, 39261, 35921, 39334, 789, 518, 38214, 1711, 28460, 40325, 23608, 36275, 41615, 34426, 28668, 42952,
                1710, 26, 13475, 41452, 7285, 5606, 28695, 30784, 36417, 21641, 42034, 42519, 27555, 24557, 29961, 42632, 1648,
                7023, 36466, 1011, 3071, 311, 330, 10435, 27778, 31518, 41797, 21183, 8030, 2992, 37035, 45, 5480, 42430, 29392,
                5688, 7186, 33882, 35231, 41492, 30420, 4678, 13, 4951, 731, 270, 30937, 30635, 34330, 25970, 38257, 35671, 921,
                23760, 2813, 34687, 38326, 27202, 30418, 37451, 12689, 29603, 27750, 36203, 22132, 1104, 279, 5225, 29672, 43296,
                36246, 3699, 7118, 2452, 2809, 30696, 3259, 4024, 4077, 4873, 451, 613, 28469, 14484, 37993, 12656, 3208, 124, 38880,
                1290, 2389, 836, 652, 14576, 32456, 39795, 5083, 40181, 3391, 30440, 2804, 40550, 23104, 28206, 1146, 28985, 40958,
                1780, 33377, 31209, 27924, 29589, 39000, 39134, 5070, 1258, 42047, 1370, 41639, 4108, 38606, 598, 34762, 36809, 34999,
                31905, 31607, 3485, 911, 36373, 414, 10002, 8106, 1772, 36940, 29253, 31992, 28987, 25570, 27580, 20795, 860, 30409,
                1685, 8648, 38351, 2567, 31367, 34076, 4445, 2662, 30871, 4659, 36979, 37231, 34789, 28734, 3864, 26607, 35614,
                36543, 18538, 41011, 28852, 29573, 30348, 20670, 42422, 39209, 591, 537, 18145, 42830, 5172, 36539, 1127, 37633,
                28748, 9061, 33211, 26072, 9354, 40697, 33729, 37661, 41444, 2904, 35406, 359, 23455, 7540, 11810, 35168, 39131,
                39633, 40666, 3111, 32660, 29368, 37, 2113, 23528, 5316, 463, 37192, 5148, 33711, 2468, 16323, 35638, 2590, 963,
                22504, 41291, 34457, 31769, 39651, 29395, 43275, 10, 16671, 30495, 30134, 42340, 1239, 38879, 9905, 41786,
                37140, 3227, 19758, 34050, 39368, 39534, 1265, 41351, 36133, 29295, 2303, 37578, 41464, 37123, 32854, 33070, 31996, 2170, 104, 30119, 4256, 40712, 28519, 1679, 28170, 27996, 1840, 889, 2791, 41600, 30964, 2960, 2233, 28259, 841, 41538, 30609, 40702, 2487, 3007, 43298, 26294, 32471, 36870, 36334, 32191, 8221, 27926, 5162, 33960, 30039, 32235, 38491, 39118, 32635, 585, 6797, 29282, 40155, 17029, 1390, 593, 15079, 34673, 35349, 42613, 33013, 4497, 2671, 797, 38949, 32940, 42380, 3020, 28082, 29040, 5817, 999, 35634, 4548, 29228, 3831, 41288, 3748, 33987, 32319, 43121, 946, 41090, 39293, 440, 29434, 42179, 2394, 40380, 2222, 33909, 40235, 39616, 1836, 30244, 28242, 41854, 573, 2244, 27775, 944, 4317, 34375, 41316, 1458, 7715, 38742, 37550, 30628, 28491, 37894, 3188, 817, 8643, 36868, 3539, 30754, 21084, 39051, 21262, 3427, 37061, 2066, 28958, 39888, 14231, 2295, 39137, 40095, 6726, 21138, 36301, 34469, 40165, 29133, 4940, 41104, 1476, 39021, 39281, 9037, 4764, 30196, 36925, 39391, 30988, 31166, 29062, 8977, 41765, 37053, 29380, 30919, 3368, 20486, 30416, 24306, 35819, 38126, 34394, 42623, 3421, 34959, 14326, 41578, 19775, 30448, 29442, 3926, 2827, 28191, 30761, 10664, 37128, 5112, 1443, 25934, 19440, 38593, 32716, 23912, 36299, 30593, 1297, 34734, 2987, 39125, 36760, 32284, 38374, 1553, 319, 38405, 26697, 25667, 34950, 29639, 1873, 21268, 29483, 28895, 41200, 40029, 21967, 38703, 34299, 41308, 40954, 28964, 38831, 30428, 33958, 1131, 2852, 36901, 2131, 38924, 33190, 28561, 27418, 40394, 33060, 33135, 436, 7783, 29081, 37003, 1921, 5239, 20554, 33079, 7661, 29833, 29321, 12891, 15182, 39739, 36627, 37729, 28249, 36771, 36319, 32316, 3865, 8988, 35096, 14438, 34117, 25299, 32474, 39154, 467, 30389, 1560, 29763, 644, 32003, 40440, 28135, 37440, 42049, 36995, 17443, 32742, 31728, 43283, 33262, 10638, 30074, 36857, 27371, 29148, 19371, 26671, 36639, 8887, 2564, 26468, 3226, 31604, 33081, 40818, 4657, 39215, 1763, 37700, 25362, 2381, 31369, 22763, 30442, 34818, 1938, 39161, 34859, 36237, 32518, 36431, 31373, 6815, 39096, 31200, 5587, 41715, 42238, 26303, 40657, 39496, 668, 1362, 2830, 31214, 2630, 5149, 36521, 32334, 2857, 2169, 35949, 37409, 42903, 30122, 5337, 28140, 25405, 24508, 27671, 34945, 5717, 4453, 1154, 3027, 29008, 23736, 25402,
                42063, 30234, 31445, 649, 5952, 32539, 40602, 4027, 34220, 2891, 38269, 41258, 37744, 11293, 3218, 36207, 31068, 30990,
                32591, 730, 25537, 36481, 4295, 2820, 30166, 41767, 15325, 30249, 29640, 39306, 31761, 42755, 31073, 2760, 6988, 26328, 23354, 42875, 41569, 43247, 39371, 28028, 39139, 41110, 32271, 17093, 36381, 2532, 17809, 43293, 10647, 40952, 34461, 41634, 21161, 36225, 28243, 42805, 40378, 41135, 43010, 2331, 38389, 36501, 39519, 41084, 1135, 35242, 520, 41931, 3085, 35190, 4550, 29917, 4784, 41764, 7187, 28703, 2798, 34695, 3131, 41368, 6099, 37383, 4553, 5319, 6736, 28792, 30292, 36934, 29484, 28218, 808, 13971, 16963, 29393, 2032, 818, 1175, 3202, 32436, 39884, 32067, 36515, 41294, 11838, 2577, 3206, 29127, 40191, 30262, 28742, 37907, 36305, 33921, 28063, 23161, 37762, 350, 3941, 3390, 11628, 33144, 38240, 1949, 5720, 39249, 234, 29427, 6018, 42082, 28988, 28111, 3379, 32909, 753, 41810, 28181, 725, 26502, 29191, 39869, 41577, 31170, 39375, 1867, 40967, 631, 33466, 457, 4856, 36034, 5964, 37943, 25528, 357, 923, 39488, 4096, 9684, 8045, 39257, 38233, 740, 33768, 28150, 36512, 38263, 43322, 29436, 33872, 41531, 31420, 36259, 35122, 409, 38522, 35884, 6601, 39116, 7708, 40146, 30426, 2782, 40536, 14260, 29574, 42566, 40635, 3679, 37944, 2395, 41552, 715, 36242, 36311, 36276, 2770, 30550, 515, 30735, 34285, 32356, 38986, 31537, 25215, 39297, 13261, 38825, 40765, 42107, 34927, 27888, 28738, 34889, 30730, 2835, 30445, 38995, 32654, 41561, 30928, 41873, 39176, 32320, 2064, 25631, 38860, 37885, 39101, 32572, 39392, 8811, 5250, 29468, 29822, 38855, 30931, 38458, 27687, 32713, 37177, 4217, 41033, 30364, 29491, 25679, 29769, 2875, 36360, 31243, 28914, 28013, 41896, 34989, 9557, 30275, 41711, 28791, 43026, 30647, 39666, 38925, 2794, 30192, 27843, 40389, 41517, 29164, 4456, 28850, 40610, 34292, 29898, 998, 2484, 2741, 35972, 29854, 38225, 29742, 2534, 40041, 5544, 1620, 2133, 33932, 33117, 3191, 33708, 27507, 38694, 32259, 1412, 40537, 27851, 2910, 34808, 5171, 38047, 30830, 40588, 39865, 41605, 7867, 34580, 42147, 28624, 28146, 41338, 29399, 35513, 41053, 9712, 32727, 38621, 38811, 5620, 24478, 35827, 41832, 25316, 2805, 27481, 20213, 34313, 37692, 30187, 42577, 2677, 35552, 40229, 3197, 37205, 37851, 1330, 14970, 3696, 17139, 7952, 30559, 26012, 25494, 39727, 2350, 2319, 32888, 34636, 42459, 27870, 9629, 34749, 41412, 7068, 22283, 42301, 2402, 35153, 2272, 27272, 27529, 40419, 31136, 27267, 26310, 28962, 858, 950, 2953, 40584, 1603, 27273, 39807, 14383, 4363, 41936, 1430, 20016, 5310, 2648, 29543, 5698, 37454, 17161, 39631, 28275, 28374, 31964, 40509, 29122, 42648, 43108, 29204, 25514, 3108, 2964, 28726, 34714, 31754, 24975, 37027, 42849, 32744, 26372, 42335, 37294, 6843, 30117, 40871, 4691, 1194, 29254, 33447, 36572, 3213, 28048, 30235, 28916, 40595, 37054, 20858, 43250, 38812, 41922, 42968, 37134, 2745, 22779, 30282, 320, 33647, 5040, 30851, 31481, 41029, 41043, 33910, 37285, 42189, 24664, 37657, 34099, 726, 34119, 43145, 2286, 37418, 7990, 34359, 6694, 30795, 32923, 10961, 4962, 35923, 42227, 21723, 3342, 42745, 40266, 36048, 29740, 31227, 30314, 2701, 881, 40420, 2232, 9308, 33316, 32788, 2712, 10790, 37468, 40953, 24627, 39665, 33935, 38961, 27435, 36916, 41214, 6969, 22264, 722, 3138, 34309, 41082, 39649, 42689, 26612, 3632, 34369, 27916, 25872, 25678, 41902, 17470, 38227, 34058, 26232, 43351, 709, 2833, 34124, 28109, 27297, 3800, 32892, 36902, 27779, 2362, 27906, 42573, 41804, 4259, 27889, 38485, 42433, 38634, 35865, 533, 2935, 2346, 1701, 24315, 38576, 6413, 42041, 25618, 28740, 26650, 23707, 37549, 33399, 41817, 40992, 7889, 38467, 24023, 31306, 344, 4148, 30088, 3561, 29261, 20043, 2922, 33634, 10990, 30164, 29435, 36371, 2834, 27827, 34249, 32960, 34200, 25889, 28119, 9446, 39876, 40362, 33429, 2399, 3070, 39033, 851, 372, 40852, 6010, 31424, 802, 116, 37503, 5414, 1237, 42727, 31957, 39207, 42215, 16367, 34542, 41349, 39218, 34162, 3219, 42256, 528, 37373, 24536, 37226, 284, 4757, 8476, 31389, 41999, 3225, 30212, 34303, 39386, 43126, 30728, 22475, 41139, 41479, 3115, 21819, 34240, 29275, 30046, 5649, 35857, 43346, 27320, 30065, 1152, 41188, 37572, 33609, 31034, 5963, 4090, 34784, 5723, 1499, 6556, 28301, 2205, 19538, 33571, 755, 38731, 29166, 42394, 37400, 8964, 41387, 34135, 38238, 2294, 35957, 28349, 32033, 23300, 41876, 20991, 41352, 40043, 40270, 39094, 5207, 36844, 26586, 37203, 20888, 34548, 30385, 2538, 30492, 7270, 960, 24552, 29311, 10780, 27925, 38094, 28761, 10074, 36327, 9044, 2772, 6664, 7743, 40122, 34769, 40950, 30411, 39224, 1088, 2717, 30521, 35815, 38293, 41734, 27028, 31789, 21055, 4624, 40388, 15324, 32283, 37202, 1843, 37481, 41549, 4855, 30230, 6889, 925, 17421, 34488, 39175, 15240, 31008, 32736, 26224, 38163, 39178, 17737, 27714, 22517, 6078, 957, 31723, 2407, 779, 3207, 30468, 650, 42723, 3343, 36444, 37093, 38325, 43073, 34380, 2549, 427, 2731, 32695, 37404, 35354, 31673, 567, 24051, 28043, 32217, 29493, 7582, 43164, 35379, 2708, 39712, 41693, 5129, 32758, 40643, 1516, 9961, 3172, 34398, 29103, 37206, 506, 22918, 33870, 30243, 10075, 42840, 30954, 41565, 40691, 21756, 1134, 31541, 35919, 27520, 33326, 3128, 31902, 35342, 39451, 2338, 2823, 42806, 2047, 37662, 4498, 30597, 866, 28975, 29200, 32574, 40300, 35246, 171, 37442, 1273, 1642, 1706, 37642, 31024, 1133, 4438, 24628, 31443, 21589, 39728, 2388, 35606, 37062, 32777, 9793, 40152, 28250, 4641, 32548, 32409, 23470, 32072, 2030, 3097, 28641, 40, 39356, 29403, 30459, 25780, 27844, 34774, 26855, 3153, 21271, 2280, 43273, 41541, 29881, 30982, 3609, 34156, 29679, 40848, 28010, 41208, 11265, 35102, 20862, 40338, 27206, 41385, 1076, 32236, 2291, 43120, 21578, 7108, 19433, 21206, 42738, 36063, 36249, 38723, 31156, 33945, 11358, 30147, 2260, 30490, 41934, 4957, 31201, 9694, 31449, 21511, 8044, 43157, 4904, 5605, 35740, 34977, 32786, 4554, 37779, 40222, 29366, 1108, 31021, 40215, 3205, 40248, 42709, 28297, 29092, 30913, 34534, 3087, 36951, 26016, 18907, 32105, 4744, 34973, 33368, 3019, 28330, 36468, 37564, 41647, 40011, 40695, 36042, 2359, 26777, 39943, 41178, 27949, 28992, 43112, 30197, 32405, 22229, 42890, 32984, 38108, 37901, 30058, 43306, 38334, 303, 4953, 35302, 1304, 28736, 31439, 18172, 3338, 2600, 40860, 5297, 2104, 35813, 28346, 43227, 39032, 26188, 43244, 36711, 29076, 38381, 29059, 3691, 27914, 3320, 4147, 30889, 1980, 28456, 39201, 4036, 33741, 40634, 31535, 29125, 42025, 40426, 21719, 33906, 412, 39696, 39254, 791, 20928, 32098, 1863, 3166, 564, 5128, 3366, 1132, 5695, 3872, 37716, 40629, 6272, 12687, 42772, 42801, 27293, 28870, 2783, 1260, 22303, 27752, 38220, 20566, 36547, 35922, 32772, 40700, 39899, 29598, 35946, 36871, 16786, 23918, 36426, 40342, 12523, 35761, 13477, 2062, 37574, 16637, 6180, 37040, 4967, 30381, 42495, 3220, 21582, 24847, 28945, 33011, 39486, 23957, 41954, 28461, 32341, 41964, 39854, 33456, 33142, 2329, 3682, 19305, 41665, 43069, 24881, 37306, 40071, 42355, 33274, 36695, 37271, 30655, 35438, 31700, 39379, 1303, 34806, 32969, 332, 19914, 37636, 30584, 40701, 35759, 34452, 40405, 390, 2829, 3244, 28721, 42426, 36156, 11984, 38526, 30674, 30090, 6348, 29405, 890, 29829, 19080, 4619, 22195, 3771, 7663, 2656, 39159, 40817, 6795, 11723, 41325, 27572, 34862, 42407, 39320, 3430, 37942, 39643, 26722, 7888, 39474, 32313, 5339, 35006, 15892, 34938, 28204, 42457, 28105, 4741, 40127, 2524, 5012, 317, 39719, 25779, 38834, 31438, 38945, 43204, 41374, 428, 20440, 5174, 22972, 26706, 1546, 35806, 22528, 42796, 41447, 788, 7338, 1724, 35101, 34006, 1109, 32028, 21211, 40334, 7302, 43327, 2941, 4613, 28404, 33303, 5974, 19840, 39910, 35021, 2699, 30245, 39341, 29948, 40564, 27646, 30006, 768, 30321, 11369, 38081, 34527, 41314
            ],
            PerformanceV2Constant::RULE_TYPE_SEND_MAIL_COUNT => [
                2805, 28748, 789, 1780, 14401, 2847, 3699, 2303, 4330, 8030, 2131, 4108, 19775, 3391, 13261, 2751, 2992, 4355, 817, 2791, 7023, 3451, 39572, 528, 440, 5316, 18493, 5129, 2295, 43259, 41029, 33377, 2809, 10002, 1290, 372, 2662, 2770, 4217, 520, 26502, 5310, 2616, 108, 3095,
                9596, 5039, 25796, 4256, 4445, 2590, 26, 13475, 740, 2222, 7118, 2606, 585, 26199, 1553, 323, 517, 31166, 26311, 17930, 950, 39371, 841, 4904, 5236, 4951, 28150, 37692, 6556, 11293, 29679, 2338, 2567, 2671, 8648, 39205, 37744, 20862, 3864, 9684, 33303, 7338, 1867, 7186, 30234, 4497, 7187, 8643, 17131, 353, 15325, 27320, 2875, 1375, 29282, 8811, 25537, 2331, 2910, 13029, 5964, 6969, 7888, 1710, 5698, 3366, 13836, 16367, 32969, 2397, 28734, 41468, 34099, 3071, 35406, 2395, 1921, 3087, 37651, 34818, 515, 104, 3259, 8221, 39356, 38485, 38094, 3111, 279, 2062, 4899, 34789, 2974, 2707, 30655, 20554, 2807, 31073, 35740, 5162, 38047, 1131, 17161, 29822, 35396, 2310, 2989, 27202, 2402, 4757, 38995, 2601, 1011, 32020, 4467, 9905, 43069, 3561, 12891, 11100, 722, 3131, 3063, 5688, 2477, 1611, 26158, 39207, 3227, 21719, 2595, 4856, 330, 533, 3299, 797, 30321, 31673, 10961, 923, 37737, 2484, 23707, 33771, 35101, 41178, 33711, 27267, 32854, 2349, 2104, 42727, 1152, 4548, 1443, 777, 36868, 27032, 3092, 2644, 3225, 27231, 5040, 1134, 8988, 19080, 40848, 40697, 15124, 40041, 14484, 19569, 34973, 10780, 7863, 37212, 26430, 34285, 38723, 2350, 4957, 1154, 42238, 36515, 30196, 5514, 963, 38879, 36979, 4453, 42265, 3800, 3226, 33399, 39224, 31481, 40245, 25872, 38958, 29850, 15079, 10241, 5573, 700, 34426, 2359, 4553, 1239, 42525, 34389, 6078, 22307, 774, 715, 493, 7990, 32236, 28792, 4065, 598, 21161, 28109, 4024, 511, 2714, 30492, 26072, 40095, 5974, 28247, 27775, 36925, 16963, 30166, 5952, 31201, 30462, 41735, 38731, 2564, 30459, 6840, 7117, 25667, 11723, 709, 28988, 38603, 40, 31152, 24397, 3696, 2860, 5695, 319, 32892, 34714, 10074, 30861, 21967, 22475, 608, 29368, 7540, 731, 27914, 2744, 4595, 40388, 38925, 39218, 19305, 5345, 2798, 22918, 3632, 41084, 3520, 1698, 1362, 29092, 270, 37885, 34005, 40643, 3219, 41548, 3198, 29949, 31034, 37203, 29029, 4976, 19440, 16323, 29457, 2975, 41139, 18145, 2657, 8476, 19748, 818, 39386, 25678, 1198, 29246, 14979, 4097, 726, 36902, 42890, 36783, 24536, 21672, 3539, 2637, 26294, 2623, 2987, 30490, 768, 23104, 836, 33870, 41031, 3006, 5239, 39797, 4317, 368, 31024, 1303, 40701, 37366, 34375, 5606, 35104, 25920, 5016, 7947, 2051, 2985, 33460, 29191, 1258, 5717, 29948, 3218, 38042, 3343, 11838, 26119, 3244, 30937, 4550, 34734, 811, 15219, 17417, 27906, 866, 35634, 3150, 2772, 9793, 860, 41931, 29491, 22044, 518, 29703, 3865, 40172, 9308, 17111, 23662, 2319, 390, 32313, 1551, 26328, 12889, 593, 4255, 41325, 41090, 25970, 26697, 41968, 2260, 4363, 4740, 42539, 10664, 357, 42573, 13783, 11229, 40967, 6018, 28742, 3390, 4038, 2089, 27778, 22763, 2922, 23760, 29200, 33447, 15014, 27098, 35669, 30964, 7661, 2731, 31306, 37779, 1287, 2307, 2745, 21578, 34749, 38108, 20486, 16671, 28160, 5339, 13660, 41605, 8106, 28275, 3206, 19916, 36114, 13824, 28082, 20016, 39175, 23957, 13831, 320, 884, 2740, 27435, 2782, 42914, 4554, 451, 35174, 6180, 4940, 9061, 40852, 1763, 3191, 2614, 26279, 720, 33548, 2712, 2677, 3129, 3320, 3251, 2346, 5326, 5004, 31905, 3763, 626, 28478, 7900, 668, 37617, 38956, 40952, 2083, 37302, 37572, 34784, 38081, 414, 14231, 1475, 33500, 22517, 17662, 627, 22424, 30420, 802, 36995, 28461, 37001, 38240, 2953, 688, 21066, 2852, 2857, 1146, 29262, 4240, 21742, 35884, 39134, 171, 21271, 32235, 27481, 564, 734, 34006, 2320, 31984, 2246, 27572, 3188, 4619, 26200, 30249, 40380, 2935, 3185, 30353, 33960, 43298, 3172, 3748, 3922, 2263, 38576, 36934, 22504, 21268, 42919, 32700, 27827, 42544, 24023, 37539, 28987, 42191, 30389, 3342, 29917, 4386, 2934, 30881, 6847, 30628, 2827, 37062, 39673, 1642, 24306, 34124, 27938, 10647, 27106, 30243, 35438, 38621, 30597, 10790, 7889, 2394, 27926, 18401, 18172, 1260, 39651, 358, 29337, 30445, 18518, 29898, 39154, 24975, 4624, 40243, 30119, 2834, 30521, 2933, 5544, 39029, 29393, 831, 31756, 1701, 2285, 27966, 37574, 29984, 28546, 32450, 38163, 31029, 14690, 38484, 28003, 29412, 16816, 38225, 33933, 7302, 15504, 3070, 32271, 38811, 29122, 28311, 40550, 689, 28243, 30851, 40536, 34847, 24628, 3208, 28740, 28349, 23470, 43015, 34249, 37061, 9932, 43187, 39051, 42065, 1648, 20858, 37330, 4641, 3097, 38945, 29186, 8887, 24464, 33634, 34050, 36417, 43174, 332, 25770, 40143, 41219, 7952, 427, 1691, 6335, 3525, 28729, 13971, 39436, 33647, 35702, 35923, 356, 37381, 36225, 213, 38052, 40814, 21243, 560, 960, 36276, 23736, 3027, 1635, 35715, 7270, 35022, 2900, 28705, 29769, 1109, 35972, 40215, 25869, 41110, 779, 25889, 26892, 35354, 500, 32320, 36042, 41422, 27418, 21055, 2487, 28135, 2823, 17470, 39534, 32727, 40564, 26915, 25405, 32259, 38593, 457, 42633, 881, 6795, 26468, 506, 13526, 32909, 28852, 38439, 3368, 2425, 21731, 14930, 29591, 30252, 25128, 32931, 36627, 14563, 39794, 20492, 29961, 7108, 25999, 2381, 9961, 32999, 38325, 9446, 463, 3631, 9037, 3423, 35214, 40146, 3379, 29435, 10281, 29137, 23486, 32109, 3118, 2897, 14736, 25809, 25494, 32768, 41607, 19433, 28001, 1840, 2995, 25934, 27446, 32984, 39474, 2943, 6601, 116, 43108, 15324, 28897, 27669, 31176, 1132, 27273, 311, 37894, 6843, 36650, 23300, 10667, 43227, 12656, 20528, 24437, 42303, 20156, 24051, 32940, 22132, 32852, 33425, 39662, 31125, 2517, 27618, 29064, 37192, 29573, 34646, 35008, 36503, 2581, 12336, 23455, 4755, 27128, 14251, 24664, 1938, 29483, 40362, 375, 26310, 33601, 30086, 30314, 35342, 4096, 3261, 5207, 22303, 32777, 4675, 2858, 28806, 31068, 33969, 35638, 21244, 11369, 1738, 2244, 8085, 7715, 2887, 15664, 34061, 853, 30468, 6010, 29640, 22528, 25570, 21511, 34394, 428, 21481, 36204, 36901, 20543, 39430, 23810, 36916, 33135, 37945, 39734, 1273, 41693, 2814, 38526, 17392, 43296, 24552, 31367, 494, 2960, 41767, 14875, 41764, 2802, 42047, 13784, 30134, 38214, 4681, 3569, 32098, 34449, 39710, 1984, 38128, 42712, 32923, 3421, 32758, 27795, 7027, 973, 4855, 9514, 20670, 30635, 12687, 27925, 8044, 30848, 34348, 27671, 29382, 34039, 37400, 7783, 34135, 35349, 3228, 37716, 32724, 7663, 35922, 3685, 40692, 12146, 41730, 35946, 2779, 17139, 20452, 38196, 39000, 4384, 29574, 29164, 43157, 27601, 36844, 25797, 33906, 730, 34162, 32888, 2047, 29484, 42772, 631, 7379, 34253, 5414, 29399, 32742, 9354, 38685, 28541, 39738, 2066, 11984, 3682, 29943, 615, 2966, 7314, 28992, 30416, 42560, 13250, 30418, 33515, 39032, 2407, 21213, 42865, 33144, 26940, 36512, 32695, 37636, 40636, 31449, 35949, 3941, 34313, 27580, 31209, 36319, 36501, 2577, 28793, 2409, 32125, 25402, 2333, 41529, 37053, 32541, 23879, 37404, 37722, 43327, 29254, 26650, 32911, 5112, 2433, 30411, 32393, 38986, 2545, 4744, 1668, 21745, 32028, 34306, 42884, 43038, 28259, 29233, 5149, 30716, 2681, 29366, 38920, 2804, 26372, 31369, 5963, 650, 33824, 2030, 40222, 5101, 21646, 32788, 20795, 39666, 39261, 11358, 13477, 1476, 3506, 39486, 2584, 9182, 26732, 33081, 34927, 10638, 36340, 42566, 30637, 32772, 2241, 3338, 4967, 6413, 31846, 42952, 3220, 11553, 29493, 10990, 43204, 7614, 36461, 39392, 21756, 38742
            ],
            PerformanceV2Constant::RULE_TYPE_FOLLOW_UP_COUNT => [
                39631, 30851, 4657, 2847, 1362, 28792, 28738, 38405, 29008, 2595, 64, 2701, 33882, 41325, 41569, 37226, 28206, 40697, 16963, 21719, 29679, 37993, 33646, 32445, 4453, 36381, 112, 40380, 36599, 43088, 32003, 30468, 23528, 39356, 31756, 38593, 37039, 33548, 37943, 28806, 5101, 2922, 30196, 3864, 7783, 27795, 451, 29253, 37617, 42303, 4951, 41552, 26, 35122, 1134, 4467, 37447, 37366, 41764, 35740, 11293, 841, 4967, 923, 30134, 16323, 32341, 30492, 881, 27926, 3027, 41422, 38214, 29851, 27202, 715, 20554, 42171, 29574, 36901, 30871, 2809, 41053, 37474, 42952, 2671, 3569, 2131, 37539, 650, 43108, 19538, 30006, 31890, 34389, 36042, 42727, 30597, 34784, 9596, 26072, 42037, 32539, 42322, 42147, 2791, 35246, 19080, 26372, 36014, 4038, 43298, 9629, 39719, 33377, 39134, 31905, 38526, 27363, 37373, 40388, 3131, 33906
            ]

        ];
        $clientIds = array_column($this->getClientList(0, true, true, null, 0, 0), 'client_id');
//        $clientIds = [9650];
        $listCount = count($clientIds);
        $i = 0;
        $param = [];
        foreach ($clientIds as $clientId) {
            $i++;
            $privilegeService = PrivilegeService::getInstance($clientId);

            if (!in_array($privilegeService->getMainSystemId(), [
                \common\library\privilege_v3\PrivilegeConstants::CRM_PLUS_SYSTEM_ID,
                \common\library\privilege_v3\PrivilegeConstants::CRM_LITE_SYSTEM_ID,
                \common\library\privilege_v3\PrivilegeConstants::CRM_SYSTEM_ID
            ])) {
                continue;
            }

            if (in_array($clientId, $ignoreClientIdMap[$type])) continue;
            $db = PgActiveRecord::getDbByClientId($clientId);

            $sql = "select * from tbl_performance_v2_rule where client_id={$clientId} and type = $type";
            $ruleMap = array_column($db->createCommand($sql)->queryAll(), null, 'type');

            switch ($type) {
                case PerformanceV2Constant::RULE_TYPE_COMPANY_COUNT:
                    $typeArr = [PerformanceV2Constant::RULE_TYPE_COMPANY_COUNT];

                    // --------新建客户数
                    $param = [
                        $ruleMap[PerformanceV2Constant::RULE_TYPE_COMPANY_COUNT]['rule_id'] ?? \ProjectActiveRecord::produceAutoIncrementId(),
                        "'新建客户数'",
                        "'当期新建的客户数'",
                        $clientId,
                        \Constants::TYPE_COMPANY,
                        "'{}'",
                        "'archive_time'",
                        "''",
                        "'count'",
                        "'create_user'",
                        PerformanceV2Constant::CRITERIA_TYPE_NULL,
                        "''",
                        PerformanceV2Constant::RULE_TYPE_COMPANY_COUNT,
                        PerformanceV2Constant::ENABLE_FLAG_TRUE,
                        \common\models\client\PerformanceV2Rule::PERFORMANCE_TYPE_PROCESS,
                        "'{}'",
                    ];
                    break;
                case PerformanceV2Constant::RULE_TYPE_SEND_MAIL_COUNT:
                    $typeArr = [PerformanceV2Constant::RULE_TYPE_SEND_MAIL_COUNT];

                    // --------发送邮件数
                    $param = [
                        $ruleMap[PerformanceV2Constant::RULE_TYPE_SEND_MAIL_COUNT]['rule_id'] ?? \ProjectActiveRecord::produceAutoIncrementId(),
                        "'发送邮件数'",
                        "'当前发送的总邮件数'",
                        $clientId,
                        \Constants::TYPE_MAIL,
                        "'{}'",
                        "'send_time'",
                        "'send_mail_count'",
                        "'count'",
                        "'sender'",
                        PerformanceV2Constant::CRITERIA_TYPE_NULL,
                        "''",
                        PerformanceV2Constant::RULE_TYPE_SEND_MAIL_COUNT,
                        PerformanceV2Constant::ENABLE_FLAG_TRUE,
                        \common\models\client\PerformanceV2Rule::PERFORMANCE_TYPE_PROCESS,
                        "'{}'"
                    ];

                    break;
                case PerformanceV2Constant::RULE_TYPE_FOLLOW_UP_COUNT:
                    $typeArr = [PerformanceV2Constant::RULE_TYPE_FOLLOW_UP_COUNT];

                    // --------添加跟进数
                    $param = [
                        $ruleMap[PerformanceV2Constant::RULE_TYPE_FOLLOW_UP_COUNT]['rule_id'] ?? \ProjectActiveRecord::produceAutoIncrementId(),
                        "'添加跟进数'",
                        "'当期新建的「线索、客户、商机」的跟进数'",
                        $clientId,
                        \Constants::TYPE_FOLLOWUP,
                        "'{}'",
                        "'create_time'",
                        "''",
                        "'count'",
                        "'create_user'",
                        PerformanceV2Constant::CRITERIA_TYPE_NULL,
                        "''",
                        PerformanceV2Constant::RULE_TYPE_FOLLOW_UP_COUNT,
                        PerformanceV2Constant::ENABLE_FLAG_TRUE,
                        \common\models\client\PerformanceV2Rule::PERFORMANCE_TYPE_PROCESS,
                        "'{}'"
                    ];

                    break;
                default:
                    break;
            }

            $ruleId = $param[0];
            $insertArr[] = "(" . implode(',', $param) . ")";
            $sql = "insert into tbl_performance_v2_rule (rule_id, name, description, client_id, refer_type, filters, time_field, target_field, calculate_rule, performance_field, criteria_type, criteria, type, enable_flag,performance_type,formula) values " . implode(',', $insertArr) . " on conflict(rule_id) do update set name=EXCLUDED.name, description=EXCLUDED.description, refer_type=EXCLUDED.refer_type, filters=EXCLUDED.filters, time_field=EXCLUDED.time_field, target_field=EXCLUDED.target_field, calculate_rule=EXCLUDED.calculate_rule, performance_field=EXCLUDED.performance_field, criteria_type=EXCLUDED.criteria_type, criteria=EXCLUDED.criteria, type=EXCLUDED.type, enable_flag=EXCLUDED.enable_flag ";

            $db->createCommand($sql)->execute();

            // 开启发送邮件白名单
            if ($type == PerformanceV2Constant::RULE_TYPE_SEND_MAIL_COUNT) {
                Helper::setPerformanceSwitchCacheByReferType($clientId, \Constants::TYPE_MAIL, 1, \Mail::MAIL_TYPE_SEND);
            }

            $this->actionRecordPerformanceByRuleId($clientId, $ruleId);

            self::info(sprintf("[$clientId]完成初始化 进度[%f%%] 规则Id[{$ruleId}] [type_$type]", ($i / $listCount) * 100));
        }
    }


    public function actionFixLatestSuccessOpportunityTimePerformance()
    {
        $clientIds = array_column($this->getClientList(0, true, true, null, 0, 0), 'client_id');
//        $clientIds = [9650];
        $now = date('Y-m-d H:i:s');
        foreach ($clientIds as $clientId)
        {

            $db = PgActiveRecord::getDbByClientId($clientId);
            if (empty($db))continue;
            $sql = "select rule_id,performance_field,target_field from tbl_performance_v2_rule where client_id = {$clientId} and delete_flag = 0 and time_field = 'latest_success_opportunity_time'";
            $ruleInfos = $db->createCommand($sql)->queryAll();

            if (empty($ruleInfos)) continue;

            foreach ($ruleInfos as $ruleInfo)
            {
                $ruleId = $ruleInfo['rule_id'];
                $performanceField = $ruleInfo['performance_field'];
                $targetField = $ruleInfo['target_field'];

                // 从record表找对应记录了绩效的company_id
                $sql = "select company_id,owner_id,account_date,indicator_value,rule_id from tbl_performance_v2_record where client_id = {$clientId} and rule_id = {$ruleId} ";
                $recordInfo = $db->createCommand($sql)->queryAll();
                $companyIds = array_unique(array_column($recordInfo,'company_id'));

                if (empty($companyIds)) continue;

                $companyIdStr = implode(",",$companyIds);

                // 获取客户信息
                $sql = "select {$performanceField},company_id from tbl_company where company_id in ($companyIdStr) ";
                $companyToUserIdMap = array_column($db->createCommand($sql)->queryAll(),null,'company_id');

                // 获取userId数组
                if ($performanceField == 'user_id') {
                    $companyToUserIdMap = array_map(function ($v) {
                        $v['user_id'] = PgsqlUtil::trimArray($v['user_id']);
                        return $v;
                    },$companyToUserIdMap);
                    $userIds = array_column($companyToUserIdMap,$performanceField);

                    $userIds = array_reduce($userIds, function($carry, $item) {
                        return array_unique(array_merge($carry, $item));
                    }, []);
                } else {
                    $userIds = array_unique(array_column($companyToUserIdMap,$performanceField));
                }


                // 获取对应部门信息
                $userToDepartmentListMap = Helper::batchGetUserToDepartmentList($clientId, $userIds);

                // 从商机表中查找对应的赢单状态 且关联到对应company 中的数据,取最近的account_date
                $sql = "select  company_id,account_date from tbl_opportunity where client_id = {$clientId} and company_id in ($companyIdStr) and enable_flag = 1 and stage_type = 2 group by company_id,account_date";

                $opportunityInfos = $db->createCommand($sql)->queryAll();

                $records  = [];
                // 组装map
                foreach ($opportunityInfos as $info)
                {
                    $companyId = $info['company_id'];
                    $accountDate = $info['account_date'];
                    $userIds = $companyToUserIdMap[$companyId][$performanceField];

                    $userIds = is_array($userIds) ? $userIds : [$userIds];

                    $isInsertDepartmentId = [];
                    foreach ($userIds as $userId)
                    {
                        $userRecord = [
                            'client_id' => $clientId,
                            'refer_id' => $companyId,
                            'owner_id' => $userId,
                            'rule_id' => $ruleId,
                            'refer_type' => \Constants::TYPE_COMPANY,
                            'record_type' => PerformanceV2Constant::RECORD_TYPE_USER,
                            'indicator_type' => $targetField,
                            'account_date' => $accountDate,
                            'create_time' => $now,
                            'update_time' => $now,
                            'indicator_value' => 1,
                            'company_id' => $companyId,
                        ];
                        $records[] = $userRecord;
                        $departmentIds = $userToDepartmentListMap[$userId] ?? [];

                        foreach ($departmentIds as $departmentId)
                        {
                            if (in_array($departmentId,$isInsertDepartmentId)) continue;
                            $departmentRecord = [
                                'client_id' => $clientId,
                                'refer_id' => $companyId,
                                'owner_id' => $departmentId,
                                'rule_id' => $ruleId,
                                'refer_type' => \Constants::TYPE_COMPANY,
                                'record_type' => PerformanceV2Constant::RECORD_TYPE_DEPARTMENT,
                                'indicator_type' => $targetField,
                                'account_date' => $accountDate,
                                'create_time' =>  \Util::escapeDoubleQuoteSql($now),
                                'update_time' => \Util::escapeDoubleQuoteSql($now),
                                'indicator_value' => 1,
                                'company_id' => $companyId,
                            ];
                            $records[] = $departmentRecord;
                            // 记录已经写入过的departmentId 批量构建相同的departmentId会导致数据库写入不成功
                            $isInsertDepartmentId[] = $departmentId;
                        }
                    }
                }


                $recordList = new PerformanceV2RecordList($clientId);
                $recordList->setReferId($companyIds);
                $recordList->setRuleId($ruleId);
                $recordList->setReferType(\Constants::TYPE_COMPANY);
                $recordList->delete();
                // 防止删除没删完就insert失败
                sleep(0.1);

                if (!empty($records)) {
                    $recordId = PgActiveRecord::produceAutoIncrementId(count($records));
                    $insertValues = [];

                    foreach ($records as $value) {
                        $insertValues[] = "({$recordId}, {$value['client_id']}, {$value['refer_id']}, {$value['owner_id']}, {$value['rule_id']}, {$value['refer_type']}, {$value['record_type']}, '{$value['indicator_type']}', {$value['indicator_value']}, '{$value['account_date']}', '{$value['create_time']}', '{$value['update_time']}', {$value['company_id']})";
                        $recordId --;
                    }

                    $commonInsertPreSql = "insert into tbl_performance_v2_record (record_id,client_id, refer_id, owner_id, rule_id, refer_type, record_type, indicator_type, indicator_value, account_date, create_time, update_time, company_id) VALUES ";
                    $commonInsertEndSql = " ON CONFLICT (client_id, rule_id, owner_id, refer_id,account_date) DO UPDATE SET indicator_type = EXCLUDED.indicator_type, account_date = EXCLUDED.account_date, update_time = EXCLUDED.update_time, indicator_value = EXCLUDED.indicator_value, owner_id = EXCLUDED.owner_id, company_id = EXCLUDED.company_id";
                    $insertSql = $commonInsertPreSql . implode(",", $insertValues) . $commonInsertEndSql;
                    $affectRows = $db->getPdoInstance()->prepare($insertSql)->execute();
                }
            }
        }
    }


    // 修复由filter问题导致的绩效记录错误数据 需要自己改一下里面的SQL
    public function actionFixPerformanceRecordFilter(int $dryRun = 1)
    {
        $clientIds = array_column($this->getClientList(0, true, true, null, 0, 0), 'client_id');
        //        $clientIds = [9650];
//        $clientIds = [18133];
        $count = 0;
        foreach ($clientIds as $clientId) {
            $db = PgActiveRecord::getDbByClientId($clientId);
            if (empty($db)) continue;
            $sql = "select rule_id from tbl_performance_v2_rule where client_id = {$clientId} and filters @> '[{\"refer_type\": \"9\", \"field\": \"origin_list\"}]' and delete_flag = 0";
            $ruleIds = $db->createCommand($sql)->queryColumn();
            if (empty($ruleIds)) continue;

            $count += count($ruleIds);
            self::info("client_id={$clientId}, rule_id=" . implode(',', $ruleIds));
            if (!$dryRun) {
                foreach ($ruleIds as $ruleId) {
                    $this->actionRecordPerformanceByRuleId($clientId, $ruleId);
                }
            }
        }
        self::info("actionPerformanceRuleComplete!, count={$count}");

    }



    public function actionFixCollectAmountFormulaPerformance(int $dryRun = 1)
    {
        $clientIds = array_column($this->getClientList(0, true, true, null, 0, 0), 'client_id');
        //        $clientIds = [9650];
        $clientIds = [18133];
        $count = 0;
        foreach ($clientIds as $clientId)
        {
            $db = PgActiveRecord::getDbByClientId($clientId);
            if (empty($db))continue;
            $sql = "SELECT rule_id FROM tbl_performance_v2_rule WHERE  client_id = {$clientId} and refer_type = 2 and delete_flag = 0 and formula->>'calculate_fields' LIKE '%2.collect_amount%'";
            $ruleIds = $db->createCommand($sql)->queryColumn();

            if (empty($ruleIds)) continue;

            self::info("client_id={$clientId}, rule_id=" . implode(',', $ruleIds));
            $count += count($ruleIds);

            if (!$dryRun) {
                foreach ($ruleIds as $ruleId) {
                    $this->actionRecordPerformanceByRuleId($clientId,$ruleId);
                }
            }
        }
        self::info("actionFixCollectAmountFormulaPerformance, count={$count}");
    }


    public function actionFixPerformanceTimeField(int $dryRun = 1)
    {
        $clientIds = array_column($this->getClientList(0, false, null, null, 0, 0), 'client_id');
                $clientIds = [9650];
//        $clientIds = [18133];
        $count = 0;
        foreach ($clientIds as $clientId)
        {
            $db = PgActiveRecord::getDbByClientId($clientId);
            if (empty($db))continue;
            $sql = "select rule_id from tbl_performance_v2_rule where client_id = {$clientId} and time_field = 'first_collection_date' and type = 2 limit 1";

            $ruleIds = $db->createCommand($sql)->queryColumn();
            if (empty($ruleIds)) continue;

            self::info("client_id={$clientId},rule_ids=" . implode(',', $ruleIds));

            $count += count($ruleIds);
            if (!$dryRun) {
                $ruleId = $ruleIds[0];
                $this->actionRecordPerformanceByRuleId($clientId,$ruleId);
            }
        }
        self::info("actionFixPerformanceTimeField, count={$count}");
    }

    // 将对应用户已删除的订单部门信息迁移至正常部门的脚本
    // 工单 【【工单-**************】用户希望开发能帮忙修复数据】https://www.tapd.cn/********/bugtrace/bugs/view?bug_id=11********001084208
    public function actionFixOrderDepartmentInfo($clientId = 9650)
    {
        $adminUserId = PrivilegeService::getInstance($clientId)->getAdminUserId();
        $mysqlDb = Yii::app()->account_base_db;
        $pgDb = PgActiveRecord::getDbByClientId($clientId);
        $departmentSql = "select id from tbl_department where client_id = {$clientId} and enable_flag = 0 ";
        $departmentIds = $mysqlDb->createCommand($departmentSql)->queryColumn();

        $lenDepartmentIds = count($departmentIds);
        if (empty($departmentIds)) return;


        $orderSql = "select * from tbl_order where client_id = {$clientId}  ";

        foreach ($departmentIds as $index => $departmentId)
        {
            if ($index == 0) {
                $orderSql .= " AND ( departments @> '[{\"department_id\": \"{$departmentId}\"}]' ";
            } elseif ($index == $lenDepartmentIds - 1) {
                $orderSql .= " OR departments @> '[{\"department_id\": \"{$departmentId}\"}]' )";
            } else {
                $orderSql .= " OR departments @> '[{\"department_id\": \"{$departmentId}\"}]' ";
            }
        }

        $orderListData = $pgDb->createCommand($orderSql)->queryAll();
        if (empty($orderListData)) return;

        $userIdToOrderInfoMap = [];
        $logArr = [];

        foreach ($orderListData as $datum)
        {
            $orderId = $datum['order_id'];
            $userArr = json_decode($datum['users'],true);
            $logArr[$orderId][] = json_decode($datum['departments']);
            foreach ($userArr as $user)
            {
                $userId = $user['user_id'];
                $userIdToOrderInfoMap[$userId][] = $orderId;
            }
        }

        self::info(sprintf("fix OrderArr ---[info] --- \n %s ",json_encode($logArr)));

        $userIds = array_keys($userIdToOrderInfoMap);
        $userToDepartmentMap = Helper::batchGetUserToDepartmentList($clientId, $userIds);

        foreach ($userIdToOrderInfoMap as $userId => $orderIds)
        {
            $department = $userToDepartmentMap[$userId][0];
            $updateSql = sprintf("UPDATE tbl_order set departments = JSONB_SET (departments,'{0, department_id}','\"{$department}\"') where client_id = {$clientId} and order_id in (%s)",implode(",",$orderIds));
            $affectRow = $pgDb->createCommand($updateSql)->execute();
            self::info("userId[{$userId}]affect rows {$affectRow}");
        }

        $performanceRuleList = new \common\library\performance_v2\rule\PerformanceV2RuleList($clientId,$adminUserId);
        $performanceRuleList->setReferType(\Constants::TYPE_ORDER);
        $performanceRuleList->setEnableFlag(PerformanceV2Constant::ENABLE_FLAG_TRUE);
        $performanceRuleList->setFields('rule_id');

        $ruleIds = array_column($performanceRuleList->find(),'rule_id');
        // 重算绩效
        foreach ($ruleIds as $ruleId)
        {
            $this->actionRecordPerformanceByRuleId($clientId,$ruleId);
        }
    }


    public function actionFixStaticTimePerformance()
    {
        // 修复
        $json = '{"34897": [1763413348698], "49114": [6845525595987], "7886": [22305344193, 22305344194], "82337": [7227289371044, 6662259735411, 7220732043052], "336161": [7833706951102], "83666": [6246274632788, 4059494564131], "16993": [22376934762], "25938": [22305425036], "25023": [7835208230383], "6534": [5872416686906], "76549": [2350178874065, 7835416464716], "4050": [7835600627437], "29550": [3495208751038], "28956": [22305438578, 7814339873832, 22305438579, 22305438582, 7796093427490, 7796121737795, 7924367611038, 7924600813264, 7928368214913, 7943237766997, 7844980252866, 7950077053060], "53815": [2135837359498, 7688683453890, 7386667830704, 7386627336574], "84945": [6865568075547], "31993": [7836369118520, 7836411106074, 7836477675312, 22446683603, 7836672113598, 7836712897652, 7836819252264, 7837079622949, 7870120173932, 7870050242178], "80428": [3220417065311], "18617": [2240755073128, 7836377852088], "81600": [3569885434806], "33670": [7810212091188], "88024": [7811034428339, 7811000542063, 7865126279535], "15555": [7836368648891, 7837031495397], "61492": [783144543473, 7836936813254], "340562": [7836200692230], "336946": [6373228387380], "85886": [4602081285565], "5330": [6238127341922], "33253": [7824639935104, 7824614054641, 7838309506369, 7692424703041, 7838346228146, 7824527109751], "35757": [6716864553378], "33227": [6238116387710], "29751": [22376996277], "30751": [7719024189869], "338752": [6866555754174], "34189": [7839665456637], "77130": [2302983406379, 2302983406383, 2302983406387], "46568": [6548701044340], "31906": [22305454738, 7841132611601], "63416": [1073402278889, 1073402278891], "69105": [7841796787956], "336536": [6867743167889], "34436": [6040953043816, 99439125488], "340108": [7835288417221, 7666320894579], "58160": [591387569804, 7843231414454, 7842346270718], "1491": [22376843793, 22376843791], "63495": [811043216887], "35069": [474848372398, 7842537054834, 7567554305651], "340304": [7810166918622], "28532": [22376980660], "36934": [23263554646, 7292645158949], "74457": [7844791407122, 1866298445862, 1866298445871], "5193": [22376886004], "57669": [6238102485072, 7698969204671], "30406": [7846653642967], "59486": [7847464939681], "49796": [341472838818], "54304": [6871326837863, 7873103182031], "61791": [4143483017758], "53816": [7851087660546, 351518511342], "37783": [3546957598533, 3546940656008, 7856981533443, 5758754338255, 7862835352812], "41854": [90532412308], "35193": [7858518447255], "49487": [6262956991524, 7860464366228], "29583": [22376995482], "336398": [6239375128167], "73036": [1458484032268], "88637": [5513835411852], "52257": [7861577844866], "340003": [7636366160980], "84014": [7861464041470], "33319": [7639092833715, 6238124420468, 22249106606, 6238124420470], "38697": [7835857972861], "53298": [7862693279262], "333899": [7840493748321], "337348": [6493648117958], "84797": [7864323725760], "335286": [5772644904583], "71408": [1752278127327], "26991": [7019300345266, 7019454077999], "50864": [7019660078897, 7019678494163], "70894": [6212329370761, 6588823257605, 7866195796577], "72859": [1415392439307], "31618": [5607803472346, 1145293287855], "40546": [55142341219], "13254": [7328273530659, 7872468544963, 7872546296791, 7872499904624, 7872431366613], "78959": [4697126011895], "88374": [7871390914540], "19506": [22305392631, 22305392632, 1889541332922], "14126": [7291225680606, 7290988048942, 22305367428, 7288234561924], "59989": [646985838477], "45065": [6664144186358, 234549559991, 6664491306022], "84273": [4649331146241], "15174": [22305372981], "86125": [5736850822767, 7667742363063, 5736850822774, 7731350666503, 6056008390983, 5736850822776], "53180": [353470492645], "48655": [338763389772, 338763389768], "35883": [23054804505, 23054804504, 23054804506], "18137": [21503033339], "339583": [7811230228872], "3192": [22376862196], "338248": [7843360461770, 7843360461772], "78996": [2980564785496], "339175": [7917560694470], "340629": [7916656038695], "29976": [7693188336721], "34147": [22793340140], "337916": [7917245871404], "31141": [7917282276870, 22377011399], "45358": [194426443252], "64084": [1011550823788, 1373220981106, 1373257428397, 1120715576821, 1120688311637, 1417227963917, 1011563191351], "88769": [5630959489102, 7926021549902, 5630959489112], "35586": [7919235542585], "81681": [4884231882253], "69527": [1205484145891], "13008": [6348742840165], "340497": [7922820133326], "38939": [24816013712], "78342": [2732104002723], "338099": [6571565697356], "337291": [7924778862086], "66319": [1150391216475, 1150391216478, 1150391216481], "340071": [7925945831553], "26302": [6171637397505], "42623": [164366857558], "70709": [1577425110446, 1577425110452], "72114": [1451996168121], "64422": [1060102842371], "81203": [3445975564639], "69054": [1276485232614], "28484": [22305435366], "62993": [7641609404966], "80746": [7625498131941, 6238122709656], "7317": [22305341159], "3312": [22376863780, 7940661172158, 7942425749953], "31428": [7925600349038], "68246": [1416288402178, 1416288402181], "37967": [7861724754519], "31769": [22249104922], "34534": [22774411579], "72351": [2646534804442], "38300": [7943048548625], "23841": [60281367293], "26036": [22305425533, 7944507229458, 6238125165722], "61883": [1404164782089], "336594": [7943864428546], "340396": [7866547670637], "21578": [22305401412, 7945367397131], "27715": [4492762512926, 888110365370], "22060": [22376953022], "8005": [7946689221469], "340465": [7947439836796], "35270": [6238106412648, 7948940559394], "37050": [7948498105546, 7949543076413], "1972": [7948441418855, 7948523368092, 22376848544], "87910": [4855511657033], "45817": [247361395929], "340496": [7950020564867], "334438": [5717465171183], "35237": [23407792481], "628": [7952946125468], "12225": [7697749462106], "35227": [36325436565]}';
        $fixInfo = json_decode($json,true);
        $count = 0;
        foreach ($fixInfo as $clientId => $ruleIds)
        {
            $count  += count($ruleIds);
            $db = PgActiveRecord::getDbByClientId($clientId);
            if (empty($db))continue;

            foreach ($ruleIds as $ruleId)
            {
                $this->actionRecordPerformanceByRuleId($clientId,$ruleId,[],true);
            }
        }
        self::info(sprintf("affectMap[%s] count[$count] \n",$json));
    }

    // 修复过程目标的静态时间的数据
    // 工单：【工单-20230601000067】不符合绩效规则的商机，被计入了绩效 https://www.tapd.cn/********/s/2397821
    // ./yiic-test performanceV2 fixDynamicPerformance --clientId=9650 --ruleId=3373385872
    public function actionFixDynamicPerformance(int $clientId, int $ruleId, $dryRun = true)
    {
        $client = new Client($clientId);
        if ($client->isNew()) {
            return;
        }
        $masterUser = $client->getMasterUser();
        \User::setLoginUserById($masterUser->getUserId());
        $performanceRule = new PerformanceV2Rule($clientId, $ruleId);
        if (!\common\library\performance_v2\rule\PerformanceV2Rule::checkIsDynamicTimeByReferTypeAndTimeField($performanceRule->refer_type, $performanceRule->time_field)){
            self::info("client_id={$clientId},rule_id={$ruleId},仅动态时间的目标规则支持执行该脚本");
            return ;
        }

        $pgDb = PgActiveRecord::getDbByClientId($clientId);
        $currId = 0;
        $limit = 1000;

        $referType = $performanceRule->refer_type;
        $referPrimaryKey = \common\library\workflow\WorkflowConstant::RELATE_PRIMARY_KEY[$referType];
        $ruleInfo = $performanceRule->getAttributes();
        $filters = is_string($ruleInfo['filters']) ? (json_decode($ruleInfo['filters'], true) ?? []) : $ruleInfo['filters'];

        $ruleConfig = new RuleConfig($clientId, $referType, [], $ruleInfo);
        $filterRunner = new WorkflowFilterRunner($ruleConfig);
        $filterRunner->setFilters($filters, $ruleInfo['criteria']);
        $filterRunner->setFields($referPrimaryKey);

        /*
         * 前提：由于静态时间的过程目标是无法修改的，所以filters是固定的
         * 该脚本是对历史filters运行错误导致的脏数据进行删除，若后续前提发生改动就不要使用该脚本
         */
        foreach (\common\library\util\SqlUtil::queryAll($pgDb, 'tbl_performance_v2_record', 'record_id', '*', $currId, $limit, " and client_id={$clientId} and  rule_id={$ruleId} and refer_type={$referType}") as $recordList)
        {
            $referIds = array_unique(array_column($recordList, 'refer_id'));

            $filterRunner->setReferIds($referIds);
            $enableRecordReferIds = [];

            foreach ($filterRunner->iterate($limit) as $item) {
                $enableRecordReferIds = array_column($item, $referPrimaryKey);
            }

            $needDeleteRecordReferIds = array_diff($referIds, $enableRecordReferIds);
            self::info(sprintf("client_id={$clientId},rule_id={$ruleId},dryRun={$dryRun},需要删除的refer_id=[%s]", implode(',',$needDeleteRecordReferIds)));

            $needDeleteRecordList = [];
            $needDeleteRecordIds = [];
            foreach ($recordList as $record)
            {
                if (in_array($record['refer_id'], $needDeleteRecordReferIds)) {
                    $needDeleteRecordList[] = $record;
                    $needDeleteRecordIds[] = $record['record_id'];
                }
            }
            self::info(sprintf("client_id={$clientId},rule_id={$ruleId},dryRun={$dryRun},需要删除的record_id=[%s]", implode(',', $needDeleteRecordIds)));
            if (!$dryRun) {
                $deleteRecordSql = "delete from tbl_performance_v2_record where client_id={$clientId} and record_id in (" . implode(',', $needDeleteRecordIds) . ')';
                $rows = $pgDb->createCommand($deleteRecordSql)->execute();
                self::info(sprintf("client_id={$clientId},rule_id={$ruleId},dryRun={$dryRun},删除的rows={$rows},删除的记录为recordList=[%s]", json_encode($needDeleteRecordList)));
            }
        }
    }

    public function actionFixWorkJournalPerformance($dryRun = 1)
    {
        $clientIds = array_column($this->getClientList(0, true, true, null, 0, 0), 'client_id');
        //        $clientIds = [9650,333383];
        $i = 1;
        $clientCount = count($clientIds);

        $countMap = [];
        foreach ($clientIds as $clientId) {
            self::info("进度: [$i / $clientCount]");
            $db = PgActiveRecord::getDbByClientId($clientId);
            if (empty($db)) continue;
            $sql = "select rule_id from tbl_performance_v2_rule where client_id = {$clientId} and refer_type = 53 and delete_flag = 0 and enable_flag = 1";
            $ruleIds = $db->createCommand($sql)->queryColumn();

            if (empty($ruleIds)) continue;

            if (!$dryRun) {
                foreach ($ruleIds as $ruleId) {
                    $this->actionRecordPerformanceByRuleId($clientId, $ruleId);
                }
            } else {
                $workJournalList = new \common\library\work_journal\WorkJournalList($clientId);
                $workJournalList->setSkipPermissionCheck(true);
                $workJournalCount = $workJournalList->count();
                // 查询下有多少规则和工作报告
                $countMap[$clientId] = [
                    'rule_count' => count($ruleIds),
                    'work_journal_count' => $workJournalCount,
                ];
            }
            $i++;
        }

        if (!empty($countMap)) {
            // 自定义比较函数
            function compareData($a, $b)
            {
                if ($a["rule_count"] == $b["rule_count"]) {
                    return $b["work_journal_count"] - $a["work_journal_count"];
                }
                return $b["rule_count"] - $a["rule_count"];
            }

            // 使用 usort() 函数进行排序 根据规则-workJournal进行排序
            uasort($countMap, "compareData");
            self::info(json_encode($countMap));
        }
    }


    public function actionFixPerformanceFilter($clientId,$rule_id)
    {
        $statusService = new InvoiceStatusService($clientId, \Constants::TYPE_ORDER);
        $orderEndStatus = $statusService->endStatus();
        $saveStatus = [];
        foreach ($orderEndStatus as $status)
        {
            if ($status['name'] === '已作废' || $status['name'] === '售后' || $status['name'] === '交易取消')
                continue;

            $saveStatus[] = $status['id'];
        }
        $filters = [[
            'value' => $saveStatus,
            'filter_no' => 1,
            'refer_type' => 2,
            'field' => 'status',
            'field_type' => 3,
            'operator' => 'in',
            'date_type' => '',
            'unit' => '',
        ]];
        $updateFilter = "'" . json_encode($filters) . "'";
        $db = PgActiveRecord::getDbByClientId($clientId);
        $updateSql = "update tbl_performance_v2_rule set filters = {$updateFilter} where rule_id = {$rule_id} and client_id = {$clientId} and type = 2 ";
        $rows  = $db->createCommand($updateSql)->execute();
        $this->actionRecordPerformanceByRuleId($clientId, $rule_id);
        self::info("{$clientId} fix filters done! rows[{$rows}]");


    }

    // ./yiic-test performanceV2 fixPerformanceRuleName --tryRun 1
    public function actionFixPerformanceRuleName($tryRun = 0)
    {
        $clientIds = array_column($this->getClientList(0, true, true), 'client_id');
        $handleTaskIds = $handleClientIds = [];

        foreach ($clientIds as $clientId) {
            $ruleIds = $taskIds = [];
            $pgdb = PgActiveRecord::getDbByClientId($clientId);
            if (empty($pgdb)) continue;
            self::info($clientId);

            $select = "select rule_id from tbl_performance_v2_rule where client_id = {$clientId} and name like '%\%%'";
            try {
                $ruleIds = $pgdb->createCommand($select)->queryColumn();
            }catch (Exception $e) {
                self::info("ClientId:{$clientId} msg:[{$e->getMessage()}]");
            }
            if (empty($ruleIds)) continue;

            $selectTask = sprintf("select id from tbl_performance_v2_task where client_id = {$clientId} and rule_id in (%s) and status = 1", implode(',', $ruleIds));
            try {
                $taskIds = $pgdb->createCommand($selectTask)->queryColumn();
            }catch (Exception $e) {
                self::info( self::info("ClientId:{$clientId} msg:[{$e->getMessage()}]"));
            }
            if (empty($taskIds)) continue;

            $handleTaskIds = array_merge($handleTaskIds, $taskIds);
            $handleClientIds = array_merge($handleClientIds, [$clientId]);

            if ($tryRun) {
                foreach ($taskIds as $taskId) {
                    $this->actionRecordPerformanceByTaskId($clientId, $taskId);
                }
            }
        }
        self::info(sprintf("all fix, clientIds:[%s]\ntaskIds:[%s]", implode(',',$handleClientIds), implode(',',$handleTaskIds)));
    }

}
