<?php

use common\components\CacheObject;
use common\library\account\Client;
use common\library\account\external\UserInfoExternal;
use common\library\account\Helper;
use common\library\account\service\DbService;
use common\library\ai_agent\AiAgentConstants;
use common\library\ai_agent\parser\SQLParserService;
use common\library\ai_agent\ReportInterpretAiAgent;
use common\library\ai_agent\sql_handle\SqlDispatcher;
use common\library\ai_agent\trace\AiTracer;
use common\library\ai_agent\vector\EmbeddingService;
use common\library\ai_service\AiAgentConversationHistoryList;
use common\library\performance_v2\PerformanceV2Constant;
use common\library\privilege_v3\PrivilegeConstants;
use common\library\privilege_v3\PrivilegeService;
use common\library\queue_v2\job\MailReceiveTask;
use common\library\todo\TodoConstant;
use common\library\util\PgsqlUtil;
use common\library\util\XlsUtil;
use PHPSQLParser\PHPSQLCreator;

class XyyCommand extends \CrontabCommand
{

    //  ./yiic-test xyy fixTeamWallSetting
    public function actionFixTeamWallSetting($clientIds = 0, $dryRun = 1)
    {

        $handleClients = [];
        // 全量找出缺少倒计时组件client（不管client是否过期）
        $clientIds = $clientIds ? (is_string($clientIds) ? explode(',', $clientIds) : $clientIds) : array_column(self::getClientList(0), 'client_id');
        foreach ($clientIds as $clientId) {
            try {
//                if (in_array($clientId, [14115])) continue;
                $admin_user_id =  common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();
                if (empty($admin_user_id)) {
                    continue;
                }
                User::setLoginUserById($admin_user_id);

                if (!$client = Client::getClient($clientId)) continue;
                if (!$pgdb = PgActiveRecord::getDbByClientId($clientId)) continue;
                if (!$mysqldb = ProjectActiveRecord::getDbByClientId($clientId)) continue;
                $pgdb->setActive(false);
                $mysqldb->setActive(false);

                $teamWallSetting = $client->getSettingAttributes([\common\library\account\Client::SETTING_KEY_PDCA_TEAM_WALL_SETTING]);
                // 没有团队墙配置，初始化
                $privilegeService = PrivilegeService::getInstance($clientId);
                if (!$privilegeService->hasFunctional(PrivilegeConstants::FUNCTIONAL_PERFORMANCE_TEAM_WALL)) {
                    continue ;
                }
                if (empty($teamWallSetting[Client::SETTING_KEY_PDCA_TEAM_WALL_SETTING])) {
                    $handleClients = array_merge($handleClients, [$clientId]);

                    if (!$dryRun) {
                        self::info($clientId . "团队墙没有配置");
                        self::info($clientId . "开始初始化团队墙");
                        common\library\performance_v2\Helper::initClientTeamWallSetting($clientId);
                        self::info($clientId . "完成团队墙初始化");
                        continue;
                    }
                } else {
                    $types = array_column($teamWallSetting[Client::SETTING_KEY_PDCA_TEAM_WALL_SETTING], 'type');
                    // 有key但是没有倒计时键值，需要被修复
                    if (! in_array(PerformanceV2Constant::TEAM_WALL_SETTING_TYPE_TIME_WIDGET, $types)) {
                        $handleClients = array_merge($handleClients, [$clientId]);
                        if (!$dryRun) {
                            $timeWidget = [
                                "type" => PerformanceV2Constant::TEAM_WALL_SETTING_TYPE_TIME_WIDGET,
                                "name" => \Yii::t("performanceV2", "倒计时"),
                                "id" => PerformanceV2Constant::TEAM_WALL_SETTING_TYPE_TIME_WIDGET . "-*************-1",
                                "count_down_type" => 1,
                                "enable_flag" => 1,
                                "layout" => [
                                    "x" => 0,
                                    "y" => 0,
                                    "w" => 1,
                                    "h" => 1,
                                    "i" => PerformanceV2Constant::TEAM_WALL_SETTING_TYPE_TIME_WIDGET . "-*************-1"
                                ]
                            ];

                            $teamWallSetting = array_merge([$timeWidget], $teamWallSetting);
                            $client->setSettingAttributes([\common\library\account\Client::SETTING_KEY_PDCA_TEAM_WALL_SETTING => $teamWallSetting]);
                            $client->saveSettingAttributes();
                            self::info($clientId . "已更新倒计时组件");
                            self::info(json_encode($teamWallSetting));
                        }
                    }


                }
            }catch (Exception $e) {
                self::info("client【{$clientId}】error：" . $e->getMessage() . $e->getFile() . $e->getLine());
                continue;
            }

        }
        self::info(sprintf("共有%s客户数据需要被修复，clientId：[%s]", count($handleClients), implode(',',$handleClients)));
    }

    public function actionTest()
    {
        $string = '
{"DSL":{"desc":"查询上个月每个员工创建的客户数量","from":{"tables":[{"name":"tbl_company"}]},"fields":[{"field":"tbl_company.create_user","alias":"员工"},{"field":"tbl_company.company_id","function":"count","alias":"创建客户数量"}],"where":[{"operator":">=","field":"tbl_company.create_time","value":"2023-12-01 00:00:00"},{"operator":"<","field":"tbl_company.create_time","value":"2024-01-01 00:00:00"}],"group_by":["tbl_company.create_user"],"limit":1000}}
';

        $dsl = json_decode($string, true);
        $dsl = $dsl['DSL'];
        $dsl = json_encode($dsl);

        \User::setLoginUserById(11858405);

        $sql = (new \common\library\ai_agent\utils\DslSqlBuilder('9650', '11858405', $dsl))->buildSql();
        $generateAiAgent = new \common\library\ai_agent\GenerateDataAiAgent('9650', '11858405');

        // 分析dsl
        $analysisInfo = $generateAiAgent->analysisDsl($dsl);
        if (($analysisInfo['code'] ?? -1) != 0) {
            throw new AiAgentException("analysis dsl error !", ($analysisInfo['code'] ?? -1));
        }
        print_r($sql . "\n");
        $result = $generateAiAgent->execDslSql('9650', '11858405', $dsl, $sql, $analysisInfo);
        var_dump($result);
    }

    // ./yiic xyy updateData  --clientId=9650 --userId=11858405
    public function actionUpdateData($clientId = null, $userId = null)
    {
        if (!$userId) $userId = 55939848;
        if (!$clientId) $clientId = 31402;
        if (is_array($userId)) $userId = implode(',', $userId);
        $updateSql = "update tbl_user_statistics_day set send_mail_count = 53 where client_id = {$clientId} and user_id in({$userId}) and `date` >= '2024-04-01 00:00:00' and `date` <= '2024-04-01 23:59:59'";
        $affectRow = \ProjectActiveRecord::getDbByClientId($clientId)->createCommand($updateSql)->execute();
        print_r("affectRow:" . $affectRow);
    }

    //  ./yiic-test xyy fixDailyWorkReport
    public function actionFixDailyWorkReport()
    {
//        $clientId=9650;
//        $date = '2023-06-04';
        $clientId=355747;
        $date = '2024-04-07';
        $task = new \common\library\server\crontab\task\WorkReportTask();
        $task->runAction('runBuildWorkReport',['clientId' => $clientId, 'type' => 'daily', 'date' => $date]);
    }

    // ./yiic-test xyy fixWeekWorkReport
    public function actionFixWeekWorkReport()
    {
//        $clientId=9650;
//        $date = '2021-06-20';
        $clientId=355747;
        $date = '2024-04-07';
        $task = new \common\library\server\crontab\task\WorkReportTask();
        $task->runAction('runBuildWorkReport',['clientId' => $clientId, 'type' => 'week', 'date' => $date]);
    }

    public function actionTestMail()
    {
        $userId = 249519509;
        $mailId = 3514571277;
        User::setLoginUserById($userId);
        $mail = new \common\library\mail\Mail($mailId, $userId);
       $service = new \common\library\mail\service\CallbackService($mail);
//       $service->applyTaskRule();

    }

    public function actionGetWorkJournalTemplateCsv($clientIds = '')
    {
        $resultPath = '/tmp/work_journal_template.csv';
        $fp = fopen($resultPath, 'w');
        fputcsv($fp, ['client_id', 'template_id', 'name', 'field_name', 'type']);
        $fieldList = $ruleList = $result = [];
        $clientIds = $clientIds ? explode(',', $clientIds) : array_column($this->getClientList(0, true, true), 'client_id');
        foreach ($clientIds as $clientId) {
            try {
                echo "client $clientId start\n";
                $dbPg = PgActiveRecord::getDbByClientId($clientId);
                $dbMysql = ProjectActiveRecord::getDbByClientId($clientId);
                // 获取提交过工作报告的报告模版ID
                $getTemplateIdSql = "select template_id from tbl_work_journal_record where enable_flag = 1 and record_type = 2 and client_id = {$clientId}";
                $templateIds = $dbPg->createCommand($getTemplateIdSql)->queryColumn();
                if (empty($templateIds)) continue;
                // 根据工作报告获取模版内容，包含client_id,template_id,自定义字段，考核指标名称
                $getTemplateSql = "select client_id, template_id, name, performance_rule_info, field_list from tbl_work_journal_template where template_id in (".implode(',', $templateIds).") and enable_flag = 1 and client_id = {$clientId}";
                $templates = $dbPg->createCommand($getTemplateSql)->queryAll();

                foreach (array_column($templates, 'field_list') as $field) {
                    $fieldList = array_merge($fieldList, PgsqlUtil::trimArray($field, true));
                }

                foreach (array_column($templates, 'performance_rule_info') as $ruleInfo) {
                    $ruleList = array_merge($ruleList, array_column(json_decode($ruleInfo ?? '{}', true), 'rule_id'));
                }

                // 获取自定义字段名称
                $getFieldSql = "select name as field_name, 1 as type, id from tbl_custom_field where id in (".implode(',', $fieldList).") and type = 53 and disable_flag = 0 and enable_flag = 1 and client_id = {$clientId}";
                $fields = array_column($dbMysql->createCommand($getFieldSql)->queryAll(), null, 'id');
                // 获取其他绩效指标名称
                $getPerformanceSql = "select name as field_name, 2 as type, rule_id from tbl_performance_v2_rule where rule_id in (".implode(',', $ruleList).") and enable_flag = 1 and client_id = {$clientId}";
                $performances = array_column($dbPg->createCommand($getPerformanceSql)->queryAll(), null, 'rule_id');
                // 写入文件
                foreach ($templates as $template) {
                    $temp['client_id'] = $template['client_id'];
                    $temp['template_id'] = $template['template_id'];
                    $temp['name'] = $template['name'];

                    foreach (PgsqlUtil::trimArray($template['field_list']) ?? [] as $fieldId) {
                       if (empty($fields[$fieldId])) continue;
                        $temp_field = $temp;
                        $temp_field['field_name'] = explode('.', $fields[$fieldId]['field_name'])[1] ?? '';
                        $temp_field['type'] = $fields[$fieldId]['type'];
                        fputcsv($fp, $temp_field);
                    }

                    foreach (array_column(json_decode($template['performance_rule_info'], true), 'rule_id') ?? [] as $ruleId) {
                        if (empty($performances[$ruleId])) continue;
                        $temp_rule = $temp;
                        $temp_rule['field_name'] = $performances[$ruleId]['field_name'];
                        $temp_rule['type'] = $performances[$ruleId]['type'];
                        fputcsv($fp, $temp_rule);
                    }
                }
                echo "client $clientId done\n";
            }catch (Throwable $throwable) {
                echo "错误是: [ {$throwable->getMessage()} ]\n";
            }
        }
        fclose($fp);
        echo "END\n";

    }

    // ./yiic-test xyy GetAnalysisSubscriptionInfo
    public function actionGetAnalysisSubscriptionInfo()
    {
        $resultPath = '/tmp/analysis_subscription_info.csv';
        $fp = fopen($resultPath, 'w');
        fputcsv($fp, ['client_id', 'user_id', 'create_time']);

        $setList = DbService::getDbList(DbSet::TYPE_PGSQL);
        foreach ($setList as $set) {
            try {
                $set_id = $set['set_id'];
                $db = PgActiveRecord::getDbByDbSetId($set_id);
                if (empty($db)) continue;
                $selectSql = "select client_id, user_id, create_time from tbl_ai_subscription where enable_flag = 1";
                $res = $db->createCommand($selectSql)->queryAll();
                foreach ($res ?? [] as $key => $item) {
                    fputcsv($fp, $item);
                }
            }catch (Throwable $throwable) {
                echo "error:{$throwable->getMessage()}\n}";
                continue;
            }
        }
        fclose($fp);
        echo "finished\n";
    }

    public function actionPush2Web()
    {
        $clientId = 9650;
        $userId =11858405 ;
//        $title = 'AI客户分析失败';
//        $desc = '由于你的k币不足，本次报告分析失败';
        $title = 'AI客户分析已完成';
        $desc = "AI客户分析已完成，请查阅（本次分析消耗5个K币）";
        $is_admin = true;
//        $type = \common\library\ai_agent\AssetAnalysisAiAgent::NOTICE_TYPE_FAIL_LESS_KCOIN;
        $type = \common\library\ai_agent\AssetAnalysisAiAgent::NOTICE_TYPE_SUCCESS;
        $processedDetailMap = ['client_id' => $clientId, 'title' => $title, 'desc' => $desc, 'status' => $type, 'is_admin' => $is_admin];
        \common\library\push\Browser::push($userId, \common\library\push\Browser::TYPE_AI_CUSTOMER_ASSET_ANALYSIS, $processedDetailMap);


    }

    public function actionFreshCache($clientId = null, $userId = null)
    {
        $clientId = $clientId;
        $userId = $userId;
        User::setLoginUserById($userId);
        $userInfo = new \common\library\account\UserInfo($userId,$clientId);
        $userInfo->setExtentAttributes(
            [UserInfoExternal::EXTERNAL_KEY_FIRST_CUSTOMER_ASSET_ANALYSIS => 1]
        );
        $userInfo->saveExtentAttributes();
        echo $userId . "finish \n";
    }

    /**
     * 代办历史记录更新成未读状态
     * @param $clientIds
     * @param $userId
     * @return void
     * @throws ProcessException
     * ./yiic-test xyy refreshFeed
     */
    public function actionRefreshFeed($clientIds = null)
    {
        $clientIds = $clientIds ? explode(',', $clientIds) : array_column($this->getClientList(0, true, true), 'client_id');
        foreach ($clientIds as $clientId) {
            try {
                $userIds = Helper::getUserIds($clientId);
                foreach ($userIds as $userId) {
                    User::setLoginUserById($userId);
                    $feedObj = new \common\library\todo\Feed(TodoConstant::OBJECT_TYPE_STATISTIC, TodoConstant::TODO_TYPE_STATISTIC_INSIGHT);
                    $feedObj->setShowReadSubFeed(true);
                    $feedObj->setShowAll(true);
                    $data = $feedObj->readFeed(20);
                    foreach ($data['feed_list'] ?? [] as $feed) {
                        if ($feed['status'] == TodoConstant::FEED_STATUS_I_KNOW) {
                            $feedObj->updateFeedStatusByFeedId(TodoConstant::FEED_STATUS_UN_REREAD, [$feed['feed_id']]);
                            echo "clientId:{$clientId},userId:{$userId},feed:{$feed['title']} update status success\n";
                        }
                    }
                    $this->cleanUpForClient($clientId);
                }
                echo "finished\n";
            } catch (Exception $exception) {
                echo "error: {$exception->getMessage()}\n";
            }

        }
    }

    // ./yiic-test xyy generateDataSlsLog
    public function actionGenerateDataSlsLog()
    {
        $projects = \Yii::app()->params['aliyun_log']['projects'];
        $slsProject = $projects[0]['project_name'] ?? '';

        $startTime = strtotime("2024-02-01 00:00:00");
        $endTime = strtotime("2024-10-00 00:00:00");
        $query = "* and message: OKKiAI_timeLog and api_end and start and env: prod and 11 ";
//        $query = "* and message: OKKiAI_timeLog and api_end and start and env: dev and 11 ";
        $logList = [];
        $offset = 0;

        $s = new \common\library\log\SlsLog($slsProject, 'crm-service');

        // 循环拉取所有log
        do {
            $logs = $s->getLogs($startTime, $endTime, $query, '', 100, $offset);
            $logList = array_merge($logList, $logs['list']);
            $offset = count($logList) - 1;
        } while (count($logs['list']) == 100);

        if (empty($logList)) {
            self::info("empty logs\n");
            return true;
        }

        \Util::batchLogInfo([
            'logs' => $logs
        ]);

        $ids = \Client:: getAllInternalClientIds();
        $res[] = ['clientId', 'userId', 'account', '总耗时', 'gpt请求耗时', 'sql查询耗时', '请求时间', 'traceId'];
        foreach ($logList as $log) {

            $clientId = $log['clientId'];
            $userId = $log['userId'];
            $gptEnd = $log['gpt_end'] ?? 0;

            if ($log['sceneType'] != 11) continue; //过滤非报表生成aigent数据
            if (!empty($gptEnd) &&$gptEnd == 0) continue; // 过滤预置问题
            if (in_array($clientId, $ids)) continue;  // 过滤内部账号

            $account = $log['account'];
            $start = $log['start'];
            $end = $log['api_end'];
            $totalCost = $end - $start;
            $gptStart = $log['gpt_begin'] ?? 0;
            $gptCost = $gptEnd - $gptStart;
            $sqlCost = $log['exec_sql_end'] ?? 0;
            $traceId = $log['traceId'] ?? '';
            $dateTime = date('Y-m-d H:i:s', strtotime($log['datetime']));
            $res[] = [$clientId, $userId, $account, $totalCost, $gptCost, $sqlCost, $dateTime, $traceId];
        }


        // 创建新的 Spreadsheet 对象
        $spreadsheet = new PhpOffice\PhpSpreadsheet\Spreadsheet();

        // 获取当前活动的工作表
        $sheet = $spreadsheet->getActiveSheet();
        // 将数据填充到工作表
        foreach ($res as $rowIndex => $row) {
            foreach ($row as $columnIndex => $value) {
                // PHPExcel 库中的列是从 1 开始的，而行是从 1 开始的
                $sheet->setCellValueByColumnAndRow($columnIndex + 1, $rowIndex + 1, $value);
            }
        }
        // 创建一个写入器来保存 Spreadsheet 对象到文件
        $writer = new PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);
        $writer->save('/tmp/generateDataSlsLog.xlsx');
        self::info("finish\n");
    }

    // ./yiic-test xyy getCanTeamAnalysisUser
    public function actionGetCanTeamAnalysisUser1()
    {
        $clientList = $this->getClientList(0, true, true);
        $canTeamAnalysisUser = [];

        $resultPath = '/tmp/can_team_analysis_user.csv';
        $fp = fopen($resultPath, 'w');
        fputcsv($fp, ['client','user_id']);

        foreach ($clientList as $client) {

            //过滤内部账号
            if ($client->client_type == \Client::CLIENT_TYPE_TEST) continue;

            $clientId = $client['client_id'];
            $privilegeService = \common\library\privilege_v3\PrivilegeService::getInstance($clientId);
            $adminUserId = $privilegeService->getAdminUserId();


            if (!$this->checkPrivilege($clientId, $adminUserId)) {
                continue;
            }else {
                $userIds = Helper::getUserIds($clientId);
                fputcsv($fp, [$clientId,$adminUserId]);
                foreach ($userIds as $userId) {
                    if (!$this->checkPrivilege($clientId, $userId))
                        continue;
                    fputcsv($fp, [$clientId, $userId]);
                }
            }
            print_r("$clientId finished\n");
            $this->cleanUpForClient($clientId);
        }
        fclose($fp);
        print_r("finished\n");
    }

    public function checkPrivilege($clientId = null, $userId = null)
    {


        $privilegeService = \common\library\privilege_v3\PrivilegeService::getInstance($clientId, $userId);

        $systemId = $privilegeService->getMainSystemId();

        if (!in_array($systemId, [PrivilegeConstants::CRM_SMART_SYSTEM_ID,PrivilegeConstants::CRM_PRO_SYSTEM_ID,PrivilegeConstants::OKKI_PRO_SYSTEM_ID])) return false;

        $hasStatisticFunctional = $privilegeService->hasFunctional(PrivilegeConstants::FUNCTIONAL_STATISTIC);
        $hasStatisticPrivilege = $privilegeService->hasPrivilege(PrivilegeConstants::PRIVILEGE_CRM_STATISTIC_VIEW);

        $customerPrivilege = $privilegeService->getPrivilegeScope(PrivilegeConstants::PRIVILEGE_CRM_COMPANY_VIEW) > PrivilegeConstants::PRIVILEGE_SCOPE_OWNER;

        $okkiAiPrivilege = $privilegeService->hasFunctional([PrivilegeConstants::FUNCTIONAL_OKKI_AI]);

        if (!$hasStatisticFunctional || !$hasStatisticPrivilege || !$customerPrivilege || !$okkiAiPrivilege) return false;

        return true;
    }

    public function actionSqlParser()
    {
        $sql = "
       SELECT COUNT(*) AS 新建客户数 FROM tbl_company WHERE date_trunc('year', create_time) = date_trunc('year', current_date);
        ";
        User::setLoginUserById(11858405);
        $agent = new \common\library\ai_agent\GenerateDataSqlAiAgent(9650, 11858405);
        $agent->loadTablePrompt();
        $sql = $agent->addSkipSql( $agent->handleCustomField($agent->translation($sql)));
        $sqlparserService = new \common\library\ai_agent\parser\SQLParserService($sql);
        $fields =$agent->buildQueryField($sql);
        var_dump($sqlparserService->parsed);
    }

    // ./yiic-test xyy generateDataMessageType
    public function actionGenerateDataMessageType()
    {
        User::setLoginUserById(11858405);
        $resultPath = '/tmp/全量无跳转sql.xlsx';
        $list = XlsUtil::getExcelData($resultPath);
        $sqlList = [];
        foreach ($list as $index => $item) {
            if ($index == 0) continue;
            $sqlList[$index][$list[0][0]] = $item[0];
            $sqlList[$index][$list[0][1]] = $item[1];
            $sqlList[$index][$list[0][2]] = $item[2];
            $sqlList[$index][$list[0][3]] = $item[3];
            $sqlList[$index][$list[0][4]] = $item[4];
            $sqlList[$index][$list[0][5]] = $item[5];
        }
        $data = [];
        $data[] = ['recordId','问题', '问题类型','origin_sql', 'sql', 'time', '是否有主键查询','是否可执行', '是否可构建图表','错误信息'];
        $agent = new \common\library\ai_agent\GenerateDataSqlAiAgent(9650, 11858405);
        $pg = PgActiveRecord::getDbByClientId(9650);
        foreach ($sqlList as $index => $item) {
            $answer = json_decode($item['answer'], true) ? json_decode($item['answer'], true)['PostgreSQL'] : $item['answer'];
            try {
                $skipSql = '';
                // PGSQL高级SQL语法转换
                $translationSql = $agent->translation($answer);

                // 处理SQL，新增权限字段等
                $sql = $agent->handleSql($translationSql);

                // 处理自定义字段
                $sql = $agent->handleCustomField($sql);

                // 添加查询主键sql
                $skipSql = $agent->addSkipSql($translationSql);

                $canBuildConfig = !empty($agent->buildChartConfig($skipSql));
                $skipSql = $agent->handleCustomField($agent->handleSql($skipSql));

                $haveSkip = intval(str_contains($skipSql, 'primary_key'));
                $err = '';
                $questionType = $agent->processor->questionType;
                $agent->newBuildData($skipSql);
                $canExec = 1;

                $data[] = [$item['record_id'],$item['question'],$questionType, $answer, $skipSql ?? '', $item['create_time'], $haveSkip, $canExec, $canBuildConfig,$err];

            }catch (Throwable $throwable) {
                $haveSkip = $canExec = 0;
                $err = $throwable->getMessage();
                $file = $throwable->getFile();
                $line = $throwable->getLine();

                $data[] = [$item['record_id'],$item['question'], $questionType ?? 0,$answer, $skipSql ?? '', $item['create_time'], $haveSkip, $canExec, $canBuildConfig ?? 0,$err, $file, $line];
                continue;
            }
        }
        // 创建新的 Spreadsheet 对象
        $spreadsheet = new PhpOffice\PhpSpreadsheet\Spreadsheet();

        // 获取当前活动的工作表
        $sheet = $spreadsheet->getActiveSheet();
        // 将数据填充到工作表
        foreach ($data as $rowIndex => $row) {
            foreach ($row as $columnIndex => $value) {
                // PHPExcel 库中的列是从 1 开始的，而行是从 1 开始的
                $sheet->setCellValueByColumnAndRow($columnIndex + 1, $rowIndex + 1, $value);
            }
        }
        // 创建一个写入器来保存 Spreadsheet 对象到文件
        $writer = new PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);
        $writer->save('/tmp/全量无跳转sql-fix-2.xlsx');
        self::info("finish\n");
    }

    public function actionSqlTree()
    {
        User::setLoginUserById(11858405);
        $sql1 = "
WITH
    first_order AS (SELECT company_id, MIN(create_time) AS first_order_time
                    FROM dwd_ai_tbl_order
                    WHERE transaction_order_time > '2000-01-01 00:00:00'
                    GROUP BY company_id),
     second_order AS (SELECT o.company_id, MIN(o.create_time) AS second_order_time
                      FROM dwd_ai_tbl_order o
                      JOIN first_order f ON o.company_id = f.company_id
                      WHERE o.create_time > f.first_order_time
                      AND o.transaction_order_time > '2000-01-01 00:00:00'
                      GROUP BY o.company_id),
     order_cycle AS (SELECT s.company_id,
                     EXTRACT(EPOCH FROM (s.second_order_time - f.first_order_time)) / 86400 AS order_cycle_days
                     FROM first_order f
                     JOIN second_order s ON f.company_id = s.company_id)      
        ";

        $sql2 = "
        WITH
         dwd_ai_tbl_order AS (SELECT *
                         FROM dwd_ai_tbl_order
                         WHERE dwd_ai_tbl_order.client_id = 3
                         AND dwd_ai_tbl_order.enable_flag = 1)
        ";

        $withParsed1 = (new SQLParserService($sql2))->parsed;
        $withParsed2 = (new SQLParserService($sql1))->parsed;
        $withParsed['WITH'] = array_merge($withParsed1['WITH'], $withParsed2['WITH']);
        $creator = new PHPSQLCreator($withParsed);
        return $creator->created;
        $service = new \common\library\ai_agent\parser\SQLParserService($sql);
        $parsed = $service->parsed;
        $tables = [];
        foreach ($parsed['WITH'] as $item) {
            $subTree = $item['sub_tree'];
            if ($subTree[2]['expr_type'] == 'bracket_expression') {
                $sqlParsed = $subTree[2]['sub_tree'];
                $sql = $service->subSqlCreate($sqlParsed);
                $subService = new SQLParserService($sql);
                $tables = array_merge($tables, $subService->tables());
            }

        }


        $tables = array_column($tables, null, 'table');

        $agent = new \common\library\ai_agent\GenerateDataSqlAiAgent(9650, 11858405);
        $agent->dispatcher = (new common\library\ai_agent\sql_handle\SqlDispatcher($sql))->getDispatcher();
        $agent->processor =  (new common\library\ai_agent\sql_handle\SqlDispatcher($sql))->getDispatcher()->getProcessor();
        $agent->setQuestion('11');
        $agent->processor->setQuestion($agent->question)->checkAiSwarm();
        $skipSql = $agent->addSkipSql($agent->translation($sql));
        $skipSql = $agent->handleCustomField($agent->handleSql($skipSql));
        $data = $agent->data = $agent->newBuildData($skipSql);
        var_dump($data);
        var_dump($agent->buildChartConfig($skipSql));
        var_dump($skipSql);

    }

    public function actionTestAiSwarm()
    {
        $recordId = 4314285133;
        User::setLoginUserById(11858405);
        $api = new \common\library\setting\library\aiSwarm\AiSwarmApi(9650, 11858405);
        $data = $api->handleByRecordId($recordId, '');
        $aiSwarmId = $api->create($data, 11858405);
        var_dump($aiSwarmId);
    }

    public function actionTestWorkflow()
    {
        User::setLoginUserById(11858405);
        $mail = new \common\library\mail\Mail(3263978835);
        $service = new \common\library\mail\service\CallbackService($mail);
//        $service->applyTaskRule();
    }

    // ./yiic-omg xyy batchAddPreQuestionId
    public function actionBatchAddPreQuestionId()
    {
        $redis = \RedisService::getInstance('redis');
        $list = \common\library\ai_agent\Helper::getPresetQuestionList();
        foreach ($list as $index => $item) {
            if (!empty($item['questionId'])) continue;
            $desc = $item['desc'];
            $list[$index]['questionId'] = ProjectActiveRecord::produceAutoIncrementId();
            $redis->hset(\common\library\ai_agent\AiAgentConstants::CACHE_GENERATE_DATA_PRESET_QUESTION, $desc, json_encode($list[$index]));
        }
        self::info('finish');
    }


    public function actionExportPreQuestionImportDev()
    {
        $res[] = ['desc', 'sql', 'tag', 'showTag'];
        if (\Yii::app()->params['env'] != 'test') {
            User::setLoginUserById(55321916);
            $presetQuestionList = \common\library\ai_agent\Helper::getPresetQuestionList();

            foreach ($presetQuestionList as $index => $item) {
                $res[] = [$item['desc'], $item['sql'], $item['tag'], json_encode($item['showTag'])];
            }

            // 创建新的 Spreadsheet 对象
            $spreadsheet = new PhpOffice\PhpSpreadsheet\Spreadsheet();

            // 获取当前活动的工作表
            $sheet = $spreadsheet->getActiveSheet();
            // 将数据填充到工作表
            foreach ($res as $rowIndex => $row) {
                foreach ($row as $columnIndex => $value) {
                    // PHPExcel 库中的列是从 1 开始的，而行是从 1 开始的
                    $sheet->setCellValueByColumnAndRow($columnIndex + 1, $rowIndex + 1, $value);
                }
            }
            // 创建一个写入器来保存 Spreadsheet 对象到文件
            $writer = new PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);
            $writer->save('/tmp/preQuestionList.xlsx');

        }else {
            User::setLoginUserById(11858405);
            $resultPath = '/tmp/preQuestionList.xlsx';
            $list = XlsUtil::getExcelData($resultPath);
            foreach ($list as $index => $row) {
                if ($index == 0) continue;
                $presetQuestionList[$index][$list[0][0]] = $row[0];
                $presetQuestionList[$index][$list[0][1]] = $row[1];
                $presetQuestionList[$index][$list[0][2]] = $row[2];
                $presetQuestionList[$index][$list[0][3]] = $row[3];
            }
            $redis = \RedisService::getInstance('redis');
            foreach ($presetQuestionList as $presetQuestion) {
                $desc = $presetQuestion['desc'];
                $content['sql'] = $presetQuestion['sql'];
                $content['greyClientIds'] = null;
                $content['clickCount'] = 0;
                $content['ownerType'] = \common\library\ai_agent\GenerateDataSqlAiAgent::PRESET_QUESTION_OWNER_TYPE_ALL;
                $content['tag'] = $presetQuestion['tag'];
                $content['showTag'] = json_decode($presetQuestion['showTag'], true);
                $content['questionId'] = ProjectActiveRecord::produceAutoIncrementId();

                $redis->hset(\common\library\ai_agent\AiAgentConstants::CACHE_GENERATE_DATA_PRESET_QUESTION, $desc, json_encode($content));
                $redis->lpush(\common\library\ai_agent\AiAgentConstants::CACHE_GENERATE_DATA_PRESET_ORDER, [$desc]);
            }
        }
    }

    public function actionTest1()
    {
        $pgDB = DataWorkActiveRecord::getDbByClientId(9650);
        $sql = "select record_id, context->> 'timeLog' as time_log, create_time, client_id from tbl_ai_service_process_record where scene_type = 11 and context->>'timeLog' is not null and create_time between '2024-08-01' and '2024-11-01'  order by create_time asc";
        $recordList = $pgDB->createCommand($sql)->queryAll();
        $recordListIndexByClientId = [];
        foreach ($recordList as $record) {
            $recordListIndexByClientId[$record['client_id']][] = $record;
        }

        foreach ($recordListIndexByClientId as $clientId => $records) {
            $traceInfoList = [];
            foreach ($records as $record) {
                $timeLog = json_decode($record['time_log'], true);
                $timeInfo = [];
                $traceInfo = $traceLog = [];
                $apiBegin = $timeLog['start'];
                $apiEnd = $timeLog['api_end'] ?? 0;
                $sseBegin = $timeLog['sse_begin'] ?? 0;

                $traceLog['trace_id'] = md5($record['record_id']);
                $traceLog['client_id'] = $record['client_id'];
                $traceLog['create_time'] = $record['create_time'];
                $traceLog['update_time'] = $record['create_time'];
                $traceLog['record_id'] = $record['record_id'];

                foreach ($timeLog as $key => $value) {

                    if ((str_contains($key, '_begin') && !str_contains($key, 'gpt')) || str_contains($key, '_begin_')) {
                        $arr = explode('_begin', $key);
                        $name = $arr[0];
                        $type = 'begin';
                        $index = !empty($arr[1]) ? explode('_', $arr[1])[1] : 0;
                        if (!empty($timeInfo[$name]['end'][$index]) ) {
                            if ( $name != 'exec_sql') {
                                $timeInfo[$name]['cost'][$index] = $timeInfo[$name]['end'][$index] - $value;
                            }else {
                                $timeInfo[$name]['cost'][$index] = $timeInfo[$name]['end'][$index] > 1000 ? $timeInfo[$name]['end'][$index] - $value : $timeInfo[$name]['end'][$index];
                                $timeInfo[$name]['end'][$index] < 1000 &&  $timeInfo[$name]['end'][$index] = $value + $timeInfo[$name]['cost'][$index];
                            }
                        }
                        $timeInfo[$name][$type][$index] = $value;
                    }

                    if ((str_contains($key, '_end') && !str_contains($key, 'gpt')) || str_contains($key, '_end_')) {
                        $arr = explode('_end', $key);
                        $name = $arr[0];
                        $type = 'end';
                        $index = !empty($arr[1]) ? explode('_', $arr[1])[1] : 0;

                        if (!empty($timeInfo[$name]['begin'][$index])) {
                            if ( $name != 'exec_sql') {
                                $timeInfo[$name]['cost'][$index] = $value - $timeInfo[$name]['begin'][$index];
                            }else {
                                $timeInfo[$name]['cost'][$index] = $value;
                                $timeInfo[$name]['end'][$index] < 1000 && $value = $timeInfo[$name]['begin'][$index] + $value;
                            }
                        }
                            $timeInfo[$name][$type][$index] = $value;

                    }
                }

                if ($apiEnd) {
                    $traceLog['key'] = 'ai_agent_read_chat_completion';
                    $traceInfo['logKeyName'] = '接口请求';
                    $traceInfo['costTime'] = $apiEnd - $apiBegin;
                    $traceInfo['startTime'] = $apiBegin;
                    $traceInfo['endTime'] = $apiEnd;
                    $traceLog['info'] = $traceInfo;
                    $traceInfoList[] = new AiTracer($traceLog);

                    if ($sseBegin) {
                        $traceLog['key'] = 'sse_response';
                        $traceInfo['logKeyName'] = '流式输出';
                        $traceInfo['costTime'] = $apiEnd - $sseBegin;
                        $traceInfo['startTime'] = $sseBegin;
                        $traceInfo['endTime'] = $apiEnd;
                        $traceLog['info'] = $traceInfo;
                        $traceInfoList[] = new AiTracer($traceLog);
                    }
                }

                foreach ($timeInfo as $key => $info) {

                    if (empty($info['cost'])) continue;

                    foreach ($info['cost'] as $index => $value) {
                        $traceInfo['startTime'] = $info['begin'][$index];
                        $traceInfo['endTime'] = $info['end'][$index];
                        switch ($key) {
                            // 风控
                            case 'check_risk':
                                $traceLog['key'] = 'check_risk_info';
                                $traceInfo['logKeyName'] = '处理风控';
                                $traceInfo['costTime'] = $value;
                                $traceLog['info'] = $traceInfo;
                                $traceInfoList[] = new AiTracer($traceLog);
                                break;
                            // 查询数仓
                            case 'exec_sql':
                                $traceLog['key'] = 'generate_data_query_sql';
                                $traceInfo['logKeyName'] = '数仓查询';
                                $traceInfo['costTime'] = $value;
                                $traceLog['info'] = $traceInfo;
                                $traceInfoList[] = new AiTracer($traceLog);
                                break;
                            // sql矫正
                            case 'fix_sql':
                                $traceLog['key'] = 'generate_data_fix_sql';
                                $traceInfo['logKeyName'] = 'sql矫正';
                                $traceInfo['costTime'] = $value;
                                $traceLog['info'] = $traceInfo;
                                $traceInfoList[] = new AiTracer($traceLog);
                                break;
                            case 'gpt':
                                $traceLog['key'] = 'generate_data_gpt_request_' . $index;
                                $traceInfo['logKeyName'] = '大模型请求';
                                $traceInfo['costTime'] = $value;
                                $traceLog['info'] = $traceInfo;
                                $traceInfoList[] = new AiTracer($traceLog);
                                break;
                        }
                    }
                }
            }
            $traceOperator = new \common\library\ai_agent\trace\AiTraceOperator($clientId);
            $traceOperator->batchAddLogs($traceInfoList);
            self::info("$clientId finish\n");
        }
    }

    // debugxyy xyy importData2TraceLogTable
    public function actionImportData2TraceLogTable()
    {
        $pgDB = DataWorkActiveRecord::getDbByClientId(9650);
        $sql = "select record_id, context->> 'timeLog' as time_log, create_time, client_id from tbl_ai_service_process_record where scene_type = 11 and context->>'timeLog' is not null and create_time between '2024-08-01' and '2024-11-01'  order by create_time asc";
        $recordList = $pgDB->createCommand($sql)->queryAll();
        $recordListIndexByClientId = [];
        foreach ($recordList as $record) {
            $recordListIndexByClientId[$record['client_id']][] = $record;
        }

        foreach ($recordListIndexByClientId as $clientId => $records) {
            $traceInfoList = [];
            foreach ($records as $record) {
                $timeLog = json_decode($record['time_log'], true);
                $timeInfo = [];
                $traceInfo = $traceLog = [];
                $apiBegin = $timeLog['start'];
                $apiEnd = $timeLog['api_end'] ?? 0;
                $sseBegin = $timeLog['sse_begin'] ?? 0;

                $traceLog['trace_id'] = md5($record['record_id']);
                $traceLog['client_id'] = $record['client_id'];
                $traceLog['create_time'] = $record['create_time'];
                $traceLog['update_time'] = $record['create_time'];
                $traceLog['record_id'] = $record['record_id'];

                foreach ($timeLog as $key => $value) {

                    if ((str_contains($key, '_begin') && !str_contains($key, 'gpt')) || str_contains($key, '_begin_')) {
                        $arr = explode('_begin', $key);
                        $name = $arr[0];
                        $type = 'begin';
                        $index = !empty($arr[1]) ? explode('_', $arr[1])[1] : 0;
                        if (!empty($timeInfo[$name]['end'][$index]) ) {
                            if ( $name != 'exec_sql') {
                                $timeInfo[$name]['cost'][$index] = $timeInfo[$name]['end'][$index] - $value;
                            }else {
                                $timeInfo[$name]['cost'][$index] = $timeInfo[$name]['end'][$index] > 1000 ? $timeInfo[$name]['end'][$index] - $value : $timeInfo[$name]['end'][$index];
                                $timeInfo[$name]['end'][$index] < 1000 &&  $timeInfo[$name]['end'][$index] = $value + $timeInfo[$name]['cost'][$index];
                            }
                        }
                        $timeInfo[$name][$type][$index] = $value;
                    }

                    if ((str_contains($key, '_end') && !str_contains($key, 'gpt')) || str_contains($key, '_end_')) {
                        $arr = explode('_end', $key);
                        $name = $arr[0];
                        $type = 'end';
                        $index = !empty($arr[1]) ? explode('_', $arr[1])[1] : 0;

                        if (!empty($timeInfo[$name]['begin'][$index])) {
                            if ( $name != 'exec_sql') {
                                $timeInfo[$name]['cost'][$index] = $value - $timeInfo[$name]['begin'][$index];
                            }else {
                                $timeInfo[$name]['cost'][$index] = $value;
                                $timeInfo[$name]['end'][$index] < 1000 && $value = $timeInfo[$name]['begin'][$index] + $value;
                            }
                        }
                        $timeInfo[$name][$type][$index] = $value;

                    }
                }

                if ($apiEnd) {
                    $traceLog['key'] = 'ai_agent_read_chat_completion';
                    $traceInfo['logKeyName'] = '接口请求';
                    $traceInfo['costTime'] = $apiEnd - $apiBegin;
                    $traceInfo['startTime'] = $apiBegin;
                    $traceInfo['endTime'] = $apiEnd;
                    $traceLog['info'] = $traceInfo;
                    $traceInfoList[] = new AiTracer($traceLog);

                    if ($sseBegin) {
                        $traceLog['key'] = 'sse_response';
                        $traceInfo['logKeyName'] = '流式输出';
                        $traceInfo['costTime'] = $apiEnd - $sseBegin;
                        $traceInfo['startTime'] = $sseBegin;
                        $traceInfo['endTime'] = $apiEnd;
                        $traceLog['info'] = $traceInfo;
                        $traceInfoList[] = new AiTracer($traceLog);
                    }
                }

                foreach ($timeInfo as $key => $info) {

                    if (empty($info['cost'])) continue;

                    foreach ($info['cost'] as $index => $value) {
                        $traceInfo['startTime'] = $info['begin'][$index];
                        $traceInfo['endTime'] = $info['end'][$index];
                        switch ($key) {
                            // 风控
                            case 'check_risk':
                                $traceLog['key'] = 'check_risk_info';
                                $traceInfo['logKeyName'] = '处理风控';
                                $traceInfo['costTime'] = $value;
                                $traceLog['info'] = $traceInfo;
                                $traceInfoList[] = new AiTracer($traceLog);
                                break;
                            // 查询数仓
                            case 'exec_sql':
                                $traceLog['key'] = 'generate_data_query_sql';
                                $traceInfo['logKeyName'] = '数仓查询';
                                $traceInfo['costTime'] = $value;
                                $traceLog['info'] = $traceInfo;
                                $traceInfoList[] = new AiTracer($traceLog);
                                break;
                            // sql矫正
                            case 'fix_sql':
                                $traceLog['key'] = 'generate_data_fix_sql';
                                $traceInfo['logKeyName'] = 'sql矫正';
                                $traceInfo['costTime'] = $value;
                                $traceLog['info'] = $traceInfo;
                                $traceInfoList[] = new AiTracer($traceLog);
                                break;
                            case 'gpt':
                                $traceLog['key'] = 'generate_data_gpt_request_' . $index;
                                $traceInfo['logKeyName'] = '大模型请求';
                                $traceInfo['costTime'] = $value;
                                $traceLog['info'] = $traceInfo;
                                $traceInfoList[] = new AiTracer($traceLog);
                                break;
                        }
                    }
                }
            }
            $traceOperator = new \common\library\ai_agent\trace\AiTraceOperator($clientId);
            $traceOperator->batchAddLogs($traceInfoList);
            self::info("$clientId finish\n");
        }
    }


    public function actionCheckDealSqlValue()
    {
        $originSql = "SELECT tbl_order.order_name AS 订单名称 FROM tbl_order WHERE   create_time between '1' and '5' and nums > 2 and less <1  and tbl_order.user_id = '{}' LIMIT 1000;";
        $originSql = "select * from tbl_company where create_user_name = 'pro-jolieTEst' and create_user_name = now('ProJJJJ') and create_user_name in (select user_name from tbl_user where user_id = '{}') ) ";



        var_dump("before {$originSql}");
        $sqlParse = new SQLParserService($originSql);
        $info = $sqlParse->parsed;
        $dealSqlInfo = $sqlParse->checkWhereValue($info,'user_id', \common\library\workflow\WorkflowConstant::FILTER_OPERATOR_EQUAL, '{}');
        var_dump($dealSqlInfo);

    }

    public function actionFixAiSwarmSql($userId, $aiSwarmId, $id = 0, $dryRun = 1)
    {
        User::setLoginUserById($userId);
        $user = User::getUserObject($userId);
        $clientId = $user->getClientId();

        $selectSql = "select ext_value from tbl_item_setting_external where item_id = {$aiSwarmId} and item_type = 35 and ext_key = 'filters'";
        $mysqldb =  ProjectActiveRecord::getDbByClientId($clientId);
        $data = $mysqldb->createCommand($selectSql)->queryColumn();

        if (empty($data)) {
            echo "empty setting\n";
            return 'failed';
        }

        $extValue = json_decode($data[0], true);
        $id = $id ?: $extValue['id'];

        $recordSql = "select context->> 'exec_sql' as sql from tbl_ai_service_process_record where record_id = {$id}";
        $pgdb = PgActiveRecord::getDbByClientId($clientId);
        $sql = $pgdb->createCommand($recordSql)->queryAll();

        if (empty($sql)) {
            echo "empty Record\n";
            return 'failed';
        }

        $sql = $sql[0]['sql'];
        $extValue['sql'] = $sql;

        var_dump($extValue);

        if (!$dryRun) {
            $extValue = json_encode($extValue, JSON_UNESCAPED_UNICODE);
            $extValue = addslashes($extValue);
            $updateSql = "update tbl_item_setting_external set ext_value = '{$extValue}' where item_id = {$aiSwarmId} and item_type=35 and ext_key = 'filters'";
            $row = $mysqldb->createCommand($updateSql)->execute();
            self::info("$userId have fixed aiSwarm, affect row {$row}");
        }

        echo "finished\n";
    }

    // ./yiic-test xyy fixExpTeamAnalysisData
    public function actionFixExpTeamAnalysisData($dryRun = 1)
    {
        $setNosSql = "SELECT set_no from tbl_exp_client where enable_flag = 1 and locked = 1 order by set_no asc limit 100000";
        $setNos = \Yii::app()->db->createCommand($setNosSql)->queryColumn();
        // 团队分析错当客户分析执行的task_id，把数据都删除掉；
        $sql = "select distinct t.task_id from tbl_async_task t inner join tbl_ai_analysis_record r on r.task_id = t.task_id where t.scene in (6,601) and r.scene_type != 22";
        foreach ($setNos as $setNo)
        {
            try {
                $clientDbName = "v5_client_exp_online_{$setNo}";

                $v5ClientExpOnlineSet = \common\library\account\service\DbService::getDbByTypeAndName(1, $clientDbName);
                $pgDb = \PgActiveRecord::getDbByDbSetId($v5ClientExpOnlineSet['set_id']);
                $taskIds = array_column($pgDb->createCommand($sql)->queryAll(), 'task_id');
                self::info('FixExpTeamAnalysisData', ['setId' => $v5ClientExpOnlineSet['set_id'],'deletedTaskId' => $taskIds]);
                $sql = "delete from tbl_ai_analysis_record where task_id in ({$sql})";
                echo "$sql\n";
                if (!$dryRun) {
                    $sql = "delete from tbl_ai_analysis_record where task_id in ({$sql})";
                    $count = \Yii::app()->db->createCommand($sql)->execute();
                    self::info("FixExpTeamAnalysisData, affect row {$count}");
                }
                isset($pgDb) && $pgDb->setActive(false);
            }catch (Throwable $throwable) {
                self::error($throwable->getMessage());
                isset($pgDb) && $pgDb->setActive(false);
                continue;
            }

        }
    }

    public function actionTestReportInterpret()
    {
        User::setLoginUserById(*********);
        $params = '[{"field":"common.select_cycle","value":"month"},{"field":"common.data_source","value":"opportunity"},{"field":"company.serial_id","value":"123105","label":"客户编号"},{"field":"common.date","type":"date","value":{"start":"2023-12-31","end":"2024-12-31"}}]';
        $params = json_decode($params, true);
        $agent = new ReportInterpretAiAgent(351474, *********);
        $agent->setCompanyId(5150234217);
        $taskId = $agent->createTask($params);
        var_dump($taskId);
        $agent->setTaskId($taskId);
        $job = $agent->getJob();
        $job->setParams($params);
        $job->handle();
    }

    // ./yiic-test xyy setExpPresetQuestionList
    public function actionSetExpPresetQuestionList()
    {
        $onlineRedis = \RedisService::getInstance('redis');
        $expRedis = \RedisService::getInstance('redis_exp_debug');
        $presetQuestionList = $onlineRedis->hgetall(\common\library\ai_agent\AiAgentConstants::CACHE_GENERATE_DATA_PRESET_QUESTION);
        $presetQuestionOrder = $onlineRedis->lrange(\common\library\ai_agent\AiAgentConstants::CACHE_GENERATE_DATA_PRESET_ORDER, 0, -1);

        foreach ($presetQuestionOrder as $order => $question)
        {
            if (isset($presetQuestionList[$question]))
            {
                $expRedis->hset(\common\library\ai_agent\AiAgentConstants::CACHE_GENERATE_DATA_PRESET_QUESTION, $question, $presetQuestionList[$question]);
                $expRedis->lpush(\common\library\ai_agent\AiAgentConstants::CACHE_GENERATE_DATA_PRESET_ORDER, [$question]);
                var_dump( $onlineRedis->hgetall(\common\library\ai_agent\AiAgentConstants::CACHE_GENERATE_DATA_PRESET_QUESTION));
            }
        }
    }

    // ./yiic-test xyy fixExpTeamAnalysisData
    public function actionFixExpTeamAnalysisData1($dryRun = 1)
    {
        $setNosSql = "SELECT set_no from tbl_exp_client where enable_flag = 1 and locked = 1 order by set_no asc limit 100000";
        $setNos = \Yii::app()->db->createCommand($setNosSql)->queryColumn();
        // 团队分析错当客户分析执行的task_id，把数据都删除掉；
        $sql = "select distinct t.task_id from tbl_async_task t inner join tbl_ai_analysis_record r on r.task_id = t.task_id where t.scene in (6,601) and r.scene_type != 22";
        foreach ($setNos as $setNo)
        {
            try {
                $clientDbName = "v5_client_exp_online_{$setNo}";

                $v5ClientExpOnlineSet = \common\library\account\service\DbService::getDbByTypeAndName(1, $clientDbName);
                $pgDb = \PgActiveRecord::getDbByDbSetId($v5ClientExpOnlineSet['set_id']);
                $taskIds = array_column($pgDb->createCommand($sql)->queryAll(), 'task_id');
                self::info('FixExpTeamAnalysisData', ['setId' => $v5ClientExpOnlineSet['set_id'],'deletedTaskId' => $taskIds]);
                $sql = "delete from tbl_ai_analysis_record where task_id in ({$sql})";
                echo "$sql\n";
                if (!$dryRun) {
                    $sql = "delete from tbl_ai_analysis_record where task_id in ({$sql})";
                    $count = \Yii::app()->db->createCommand($sql)->execute();
                    self::info("FixExpTeamAnalysisData, affect row {$count}");
                }
                isset($pgDb) && $pgDb->setActive(false);
            }catch (Throwable $throwable) {
                self::error($throwable->getMessage());
                isset($pgDb) && $pgDb->setActive(false);
                continue;
            }

        }
    }

    /**
     * ./yiic-test xyy switchDocument
     * 切换向量库文档
     * @return void
     */
    public function actionSwitchDocument($versionId = '', $map = '')
    {
        if (!empty($map)) {
            $map = json_decode($map, true);
        }
        $docMap = [
            '1110' => '1115',
            '1108' => '1116',
            '1114' => '1117',
            '1111' => '1118',
            '1102' => '1119',
            '3570976417' => '1120',
            '1101' => '1121',
            '1100' => '1122',
            '1109' => '1123'
        ];

        $docMap = array_merge($map, $docMap);

        $aiAgentVersionListPdo = new \common\library\ai_agent\version\AiAgentVersionList();
        $aiAgentVersionListPdo->setAgentId(7);
        $aiAgentVersionListPdo->setVersion($versionId);
        $aiAgentVersionListPdo->setOrderBy('update_time');
        $aiAgentVersionListPdo->setOrder('desc');
        $aiAgentVersionList = $aiAgentVersionListPdo->find();
        foreach ($aiAgentVersionList as $index => $aiAgentVersion) {
            $versionId = $aiAgentVersion['version_id'];
            $switchMap = [];
            $promptConfig = json_decode($aiAgentVersion['prompt_config'], true);

            if (empty($promptConfig)) continue;
            $saveFlag = false;
            foreach ($promptConfig as $key => $item) {
                if (!empty($item['docIds'])) {
                    foreach ($item['docIds'] as $k => $id) {
                        if (!empty($docMap[$id])) {
                            $promptConfig[$key]['docIds'][$k] = $docMap[$id];
                            $saveFlag = true;
                            $switchMap[$id] = $docMap[$id];
                        }
                    }
                }
            }
            if ($saveFlag ?? false) {
                $aiAgentVersionPdo = new \common\library\ai_agent\version\AiAgentVersion($versionId);
                $aiAgentVersionPdo->agent_id = $aiAgentVersion['agent_id'];
                $aiAgentVersionPdo->model = $aiAgentVersion['model'];
                $aiAgentVersionPdo->user_prompt = $aiAgentVersion['user_prompt'];
                $aiAgentVersionPdo->system_prompt = $aiAgentVersion['system_prompt'];
                $aiAgentVersionPdo->prompt_config = json_encode($promptConfig);
                $aiAgentVersionPdo->update_time = date('Y-m-d H:i:s');
                $aiAgentVersionPdo->save();
                self::info('SwitchDocument', ['versionId' => $versionId, 'switch' => $switchMap]);
            }
        }
        echo "finish";
    }

    /**
     * 导入向量库
     * ./yiic-test xyy importVector
     * @return void
     * @throws CDbException
     */
    public function actionImportVector()
    {
        $docMap = [
//            '1115' => [
//                'url' => 'https://jinyuncrmdevelop.oss-cn-hangzhou.aliyuncs.com/other/doc/11858405/0286fa3b0ef4aeb729d690e8526dd62315e76145cffaf102bd28f5d5b8320191.csv',
//                'name' => '报表生成数据表向量集合-1226-bge-m3',
//            ],
//            '1116' => [
//                'url' => 'https://jinyuncrmdevelop.oss-cn-hangzhou.aliyuncs.com/other/doc/11858405/c1d769b91d042c12680045886362460a802b12e6b5138126ded1db450f5b2bc0.csv',
//                'name' => '报表生成问题推荐向量集合-1226-bge-m3',
//            ],
//            '1117' => [
//                'url' => 'https://jinyuncrmdevelop.oss-cn-hangzhou.aliyuncs.com/other/doc/11858405/ffd0c291585078b62c1cbe3e692531f6a4dbd54b85616593a02c7307edb12cd9.csv',
//                'name' => '报表生成直出sql示例数据-1226-bge-m3'
//            ],
//            '1118' => [
//                'url' => 'https://jinyuncrmdevelop.oss-cn-hangzhou.aliyuncs.com/other/doc/11858405/3579484a0b15fd3a9f438c82f732ea5aaf866900fd25cd7dc967fa92cc714cc5.csv',
//                'name' => '报表生成业务知识理解-1226-bge-m3'
//            ],
//            '1119' => [
//                'url' => 'https://jinyuncrmdevelop.oss-cn-hangzhou.aliyuncs.com/others/33775efa1ee50818cb3b72691d0ab5aaa622069d03b4fe8f8843644a5f085b91.csv',
//                'name' => '报表生成直出SQL示例数据-bge-m3'
//            ],
//            '1120' => [
//                'url' => 'https://v4client.oss-cn-hangzhou.aliyuncs.com/other/doc/249518442/05da8ddcacd4315c0fe1892757d85d9036822931d708dca8c9870ca78503170b.csv',
//                'name' => '报表生成业务知识理解-bge-m3',
//            ],
//            '1121' => [
//                'url' => 'https://jinyuncrmdevelop.oss-cn-hangzhou.aliyuncs.com/others/aa20f45035ea42822251589dc4ac7615fa30b53b26c85c24f4cfff744e957056.csv',
//                'name' => '报表生成问题识别数据-bge-m3'
//            ],
//            '1122' => [
//                'url' => 'https://jinyuncrmdevelop.oss-cn-hangzhou.aliyuncs.com/others/f0cdca4a9b4e112f0554c7722bedfeaf92fe1a07bf6fe8fa91f9744196071701.csv',
//                'name' => '报表生成DSL示例数据-bge-m3',
//            ],
            '1124' => [
                'url' => 'https://jinyuncrmdevelop.oss-cn-hangzhou-internal.aliyuncs.com/other/doc/249521496/6a391c2154f319f2a5896c68d1d32e6a23e451c0b2bf2e7011df75b3b96790c7.csv',
                'name' => '复杂问题业务知识数据集-20250207-bge-m3'
            ],
            '1127' => [
                'url' => 'https://jinyuncrmdevelop.oss-cn-hangzhou-internal.aliyuncs.com/other/doc/11858405/69776e48da531eaab7d1bfa880b2770c00cd5094872e6bd120b8107d956c7949.csv',
                'name' => '推荐系统报表向量数据库-20250221-bge-m3'
            ]
        ];

        foreach ($docMap as $docId => $doc) {
            $service = new \common\library\ai_agent\vector\CommonVectorService();
            $service->deleteDocumentVectors($docId);
            $params = new \common\library\ai_agent\vector\ImportVectorParams();
            $params->docName = $doc['name'];
            $params->reader = new \common\library\ai_agent\document\reader\CsvReader($doc['url']);
            $params->splitter = new \common\library\ai_agent\document\splitter\SimpleSplitter();
            $params->docId = $docId;
            $params->modelType = EmbeddingService::MODEL_TYPE_BGE_M3;
            $service->import($params);
            var_dump("import finish: $docId");
        }
        echo "finish all\n";
    }


    public function actionTestNewAgent()
    {
        User::setLoginUserById(11858405);
        $agent = new \common\library\ai_agent\ReportGenerateAiAgent(9650, 11858405);
        $presetQuestionList = \common\library\ai_agent\Helper::getPresetQuestionList();
        $result = [];
        $presetQuestionList = [
            "不同员工的复购客户占比（基于销售订单）" => ['covertName' => "不同员工的复购客户占比（基于销售订单）"],
            "去年和今年都有赢单商机的客户" => ['covertName' => "去年和今年都有赢单商机的客户"],
            "去年和今年都有成交订单的客户" => ['covertName' => "去年和今年都有成交订单的客户"],
        ];
        foreach ($presetQuestionList as $item) {
            try {
                $question = $item['covertName'];
                $processRecord = $agent->saveProcessRecord($question);
                $context = [
                    'question_history_id' => empty($retryRecordId) ? \PgActiveRecord::produceAutoIncrementId() : 0,
                    'answer_history_id' =>  \PgActiveRecord::produceAutoIncrementId(),
                    'record_id' => $processRecord->record_id,
                    'retry_record_id' => $retryRecordId ?? 0
                ];
                $agent->setContext($context);
                $agent->reponseRecordId();
                $agent->setQuestion('客户金额');
                $agent->process();
                $errorCode = 0;
                $errorMessage = 'success';
            }catch (Throwable $throwable) {
                $errorCode = $throwable->getCode();
                $errorMessage = $throwable->getMessage();
            } finally {
                $result[$question]['code'] = $errorCode;
                $result[$question]['msg'] = $errorMessage;
            }
        }

        var_dump($result);

    }

    public function actionPrompt()
    {
        $prompt = <<<PROMPT
# Role:

You are an experienced foreign trade salesperson, proficient in Japan's B2B export scenarios. You excel in email analysis and developing diverse sales strategies, capable of quickly identifying customer needs. You are familiar with product pricing, payment methods, and logistics arrangements. With excellent communication and data analysis skills, you are dedicated to building strong customer relationships and enhancing sales performance.

# Task:

1. Based on the provided email communication records, write a comprehensive summary and analysis in Japanese to summarize the sales communication with the customer, helping the manager quickly understand the sales progress.
2. Develop three different effective response strategies based on the summary and analysis to promote sales progress.

# Requirements:

## The summary should include:

- Briefly describe the email correspondence in chronological order, ensuring logical coherence.
- Clearly define the relationships and roles of all parties involved (such as salesperson, suppliers, and customers) without using specific names.
- Describe the customer's current stage and the latest progress in the business process.
- Include the customer's explicit requests, areas of interest, as well as implicit needs or expectations.
- Identify obstacles affecting relationships and business progress, detailing the customer's challenges or concerns.
- Outline the customer's decision-making process, potential influencing factors, and possible objections.
- Cover key details of foreign trade transactions, including products, specifications, quotations, payment methods, and logistics arrangements.
- Accurately capture customer needs and the latest sales progress to guide the next steps, highlighting the most urgent issues and follow-up items.
- Base the summary on the content of the email correspondence log, without fabricating or making things up.

## General Requirements for Strategies

- Each strategy should have a concise name and a detailed description
- Develop comprehensive and thorough strategies, avoiding multiple strategies that can be merged into one
- Strategies should address identified customer concerns and needs, respond to their intentions, solve problems, and aim to facilitate deal closure and relationship development
- Consider different strategies for various customer concern scenarios
- Strategies should take into account the customer's decision-making process and potential objections

# Workflow:

1. **Summary Analysis Stage**:
   
   - Ensure the recorded content is accurate and complete, covering all communication points.
2. **Strategy Development Stage**:
   
   - Re-engage the customer with fresh insights or new developments in the industry.
   - Craft personalized follow-up messages that resonate with the customer's past interactions.
   - Develop multi-tiered follow-up strategies to maintain engagement at various levels.
3. Determine the appropriate language for the salesperson to reply to the client based on historical communications.The output language should use language codes (e.g., "en" for English, "zh" for Simplified Chinese).

# Output Format

Output the content in JSON format in Japanese, structured as follows. Ensure the JSON is parseable and does not contain any additional information:

```json
{
    "summaryAnalysis": "ここに日本語で分析の要約を入力してください（最大160文字）",
    "tradeStage": "貿易段階の名称",
    "replyLanguage": "言語名（例：en, es, zh, zh-twなど）",
    "replyStrategy": [
        {
            "strategyName": "戦略1の名称（日本語）",
            "strategyContent": "戦略1の内容（日本語）"
        },
        {
            "strategyName": "戦略2の名称（日本語）",
            "strategyContent": "戦略2の内容（日本語）"
        },
        {
            "strategyName": "戦略3の名称（日本語）",
            "strategyContent": "戦略3の内容（日本語）"
        }
    ]
}
```
PROMPT;

        var_dump(json_encode(['salesmanReplyStrategyPrompt' => $prompt]));
    }



}