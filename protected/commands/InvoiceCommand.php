<?php
/**
 * 订单|报价单 Command 模块
 * 【报价单】 用actionExport
 * 【销售订单】 用actionRunExport
 * Created by PhpStorm.
 * User: Tony
 * Date: 17/12/18
 * Time: 上午10:45
 */

use common\library\custom_field\CustomFieldFormatter;
use common\library\custom_field\CustomFieldService;
use common\library\custom_field\FieldList;
use common\library\customer_v3\company\list\CompanyList;
use common\library\customer_v3\customer\CustomerList;
use common\library\invoice\OrderList;
use common\library\invoice\QuotationList;
use common\library\invoice\status\InvoiceStatusService;
use common\library\setting\library\fund\relation\CostItemInvoiceRelationApi;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

class InvoiceCommand extends CrontabCommand
{
    // 特殊处理的field
    private $special = ['company_id','opportunity_id', 'status', 'handler', 'customer_id', 'users', 'create_user', 'departments'];

    private $invoice_type_map = [
        CustomerExportTask::TYPE_ORDER => '订单',
        CustomerExportTask::TYPE_QUOTATION => '报价单'
    ];

    private $task_type_map = [
        CustomerExportTask::TYPE_CONCISE => '简洁版',
        CustomerExportTask::TYPE_DETAILED => '详细版'
    ];

    /**
     * 订单|报价单导出任务
     *
     * @param $operator_id
     * @param $task_id
     * @param $task_type
     * @param $invoice_type
     * @param $params
     * @see QuotationReadController::actionExport() 导出下属报价单
     *
     * @deprecated 待迁移到 QuotationListExport::class
     * @TODO 部分字段类型不支持，导出脚本需要迁移到 QuotationListExport
     * @see QuotationListExport::class
     */
    public function actionExport($operator_id, $task_id, $task_type, $invoice_type, $params)
    {
        // 初始化环境参数
        ini_set("memory_limit", "8096M");
        parse_str($params, $params);

        // 初始化查询器参数
        $user_id = $params['user_id'] ?? 0;
        $user_type = $params['user_type'] ?? [];
        $keyword = $params['keyword'] ?? '';
        $order_no_keyword = $params['order_no_keyword'] ?? '';
        $approval_status = $params['approval_status'] ?? '';
        $status = $params['status'] ?? [];
        $company_keyword = $params['company_keyword'] ?? '';
        $customer_keyword = $params['customer_keyword'] ?? '';
        $serial_keyword = $params['serial_keyword'] ?? '';
        $start_date = $params['start_date'] ?? '';
        $end_date = $params['end_date'] ?? '';
        $start_create_time = $params['start_create_time'] ?? '';
        $end_create_time = $params['end_create_time'] ?? '';
        $settle_start = $params['settle_start'] ?? '';
        $settle_end = $params['settle_end'] ?? '';
        $invoice_type = $invoice_type ?? CustomerExportTask::TYPE_ORDER;
        $task_type = $task_type ?? CustomerExportTask::TYPE_CONCISE;
        $create_user = $params['create_user'] ?? -1;
        $quotation_no_keyword = $params['quotation_no_keyword'] ?? '';

        // 查询订单列表
        User::setLoginUserById($operator_id);
        $user = User::getLoginUser();
        if ($invoice_type == CustomerExportTask::TYPE_ORDER) {
            $listObj = new OrderList($user->getUserId());
            $listObj->setUserType($user_type);
            $listObj->setOrderNoKeyWord(trim($order_no_keyword));
            $listObj->setSettleStart($settle_start);
            $listObj->setSettleEnd($settle_end);

            if ($user_id > 0) {
                $listObj->setUserId($user_id);
            }
            $listObj->setShowAll(true);
        } else {
            $listObj = new QuotationList($user->getUserId());
            $listObj->setCreateUser($create_user);
            $listObj->setQuotationNoKeyWord(trim($quotation_no_keyword));
            $listObj->setShowAll(true);
            if( $create_user === 0) {
                $listObj->setShowAllCreateUser(true);
            }
            //设置导出格式数据
            $listObj->setShowExportFormat(true);
        }

        $listObj->setClientId($user->getClientId());
        $listObj->setKeyword(trim($keyword));
        $listObj->setCompanyKeyword(trim($company_keyword));
        $listObj->setCustomerKeyword(trim($customer_keyword));
        $listObj->setSerialIdKeyword(trim($serial_keyword));
        $listObj->setStartUpdateDate($start_date);
        $listObj->setEndUpdateDate($end_date);
        $listObj->setApprovalStatus($approval_status);
        $listObj->setStatus($status);
        $listObj->setStartCreateTime($start_create_time);
        $listObj->setEndCreateTime($end_create_time);
        $listObj->setOrderBy('update_time');
        $listObj->setOrder('desc');
        $total_count = $listObj->count();
        $list = $listObj->find();

        // 初始化任务
        $task = new CustomerExportTask($task_id);
        LogUtil::info("task_id:{$task_id}  find_total_count:{$total_count}");

        // 查询单据字段列表
        $service = new FieldList($user->getClientId());
        $service->setType($invoice_type);
        $service->setReadonly(1);
        $service->setDisableFlag(0);
        $service->setEnableFlag(1);
        $service->setOrderBy(FieldList::ORDER_PC);
        $service->setFormatterType(CustomFieldFormatter::TYPE_GROUP_INFO);
        $fields = $service->find();

        // 特殊字段数据要记录下来，后面会作处理
        $special_fields = [];
        foreach ($list as $key => $val) {
            foreach ($this->special as $special) {
                if (isset($val[$special]) && !empty($val[$special])) {
                    if (is_array($val[$special])) {
                        if ($special === 'users') {
                            $val[$special] = array_column($val[$special], 'user_id');
                        } elseif ($special === 'departments') {
                            $val[$special] = array_column($val[$special], 'department_id');
                        }

                        foreach ($val[$special] as $v) {
                            $special_fields[$special][] = $v;
                        }
                    } else {
                        $special_fields[$special][] = $val[$special];
                    }
                }
            }
        }


//        echo json_encode($special_fields);die;
        // 查询特殊字段的数据，并写入列表
        $special_values = $this->specialField($invoice_type, $special_fields, $user->getUserId(), $user->getClientId());
        foreach ($list as $key => $val) {
            foreach ($this->special as $special) {
                if (isset($val[$special]) && !empty($val[$special])) {
                    if (is_array($val[$special])) {
                        if ($special === 'users') {
                            foreach ($val[$special] as $k => $v) {
                                $special_fields[$special][] = $v['user_id'];
                                $list[$key][$special][$k]['user_id'] = isset($special_values[$special][$v['user_id']]) ? $special_values[$special][$v['user_id']]['value'] : '';
                            }
                        } elseif ($special === 'departments') {
                            foreach ($val[$special] as $k => $v) {
                                $special_fields[$special][] = $v['department_id'];
                                $list[$key][$special][$k]['department_id'] = isset($special_values[$special][$v['department_id']]) ? $special_values[$special][$v['department_id']]['value'] : '';
                            }
                        } else {
                            foreach ($val[$special] as $k => $v) {
                                $special_fields[$special][] = $v;
                                $list[$key][$special][$k] = isset($special_values[$special][$v]) ? $special_values[$special][$v]['value'] : '';
                            }
                        }
                    } else {
                        $special_fields[$special][] = $val[$special];
                        $list[$key][$special] = isset($special_values[$special][$val[$special]]) ? $special_values[$special][$val[$special]]['value'] : '';

                        // 客户编号特殊处理
                        if ($special === 'company_id') {
                            $list[$key]['company'] = $special_values[$special][$val[$special]]['serial_id'] ?? '';
                        }
                    }
                }elseif(in_array($special, ['opportunity_id','company_id','customer_id'])){ // 商机、客户和联系人从0转为空
                    $list[$key][$special] = '';
                }
            }
        }

        //费用提取到list中，方便下载
        foreach ($list as $key => $item) {
            foreach ($item as $field => $value) {
                if ($field=='cost_list') {
                    //统计相同费用的金额
                    $costSumMap = [];
                    $costItemRelationIds = array_unique(array_column($value, 'cost_item_relation_id'));
                    if ($costItemRelationIds) {
                        $costSumMap = array_combine($costItemRelationIds, array_fill(0, count($costItemRelationIds), 0));
                        foreach ($value as $costItem) {
                            if (!empty($costItem['cost_item_relation_id'] ?? 0)) {
                                $costSumMap[$costItem['cost_item_relation_id']] += $costItem['cost'] ?? 0;
                            }
                        }
                    }
                    foreach ($value as $k => $v) {
                        if (isset($v['cost_item_relation_id']) && !empty($v['cost_item_relation_id'])) {
                            $list[$key][$v['cost_item_relation_id']] = $costSumMap[$v['cost_item_relation_id']] ?? 0;
                        }
                    }
                }
            }
        }

        // 把交易产品组排在最后面
        $product_fields = [];
        foreach ($fields as $group_key => $group_value) {
            if (in_array($group_value['name'], ['报价产品', '交易产品'])) {
                unset($fields[$group_key]);
                $product_fields = $group_value;
            }
        }
        $fields[] = $product_fields;

        // 初始化 Excel 表格列键值 MAP 表
        $fields_map = [];
        $index = 0;
        foreach ($fields as $key => $val) {
            if (isset($val['fields'])) {
                foreach ($val['fields'] as $k => $v) {
                    if (!isset($fields_map[$v['id']])) {

                        // 包装毛重和包装体积字段名称加单位
                        if (in_array($v['id'], ['package_volume', 'package_volume_amount', 'package_volume_subtotal'])) {
                            $v['name'] .= '(m³)';
                        } elseif (in_array($v['id'], ['package_gross_weight', 'package_gross_weight_amount', 'package_gross_weight_subtotal'])) {
                            $v['name'] .= '(kg)';
                        }

                        // 附加费用字段汇总
                        if (in_array($v['id'], ['cost_name', 'cost'])) {
                            if (isset($fields_map['cost_list'])) {
                                continue;
                            } else {
                                $v['id'] = 'cost_list';
                                $v['name'] = '附加费用详情';
                            }
                        }

                        // 产品型号、名称、占产品总金额百分比、报价日期等字段不显示
                        if (in_array($v['id'], ['product_id', 'product_image', 'percent_of_total_amount', 'quotation_date'])) {
                            continue;
                        }

                        // 附件、图片类型字段不显示
                        if (in_array($v['field_type'], [CustomFieldService::FIELD_TYPE_IMAGE, CustomFieldService::FIELD_TYPE_ATTACH])) {
                            continue;
                        }

                        // 关联产品字段的附件、图片类型字段不显示
                        if (in_array($v['relation_field_type'], [CustomFieldService::FIELD_TYPE_IMAGE, CustomFieldService::FIELD_TYPE_ATTACH])) {
                            continue;
                        }

                        // 联系人邮箱ID错误修正，历史遗留问题
                        if ($v['id'] == 'customer_emial') {
                            $v['id'] = 'customer_email';
                        }

                        //附加费用(款项)字段插入到addition_cost_amount之前
                        if ($v['id']=='addition_cost_amount') {
                            $costItemFieldList = $this->setCostItemFieldName();
                            if ($costItemFieldList) {
                                foreach ($costItemFieldList as $item) {
                                    $fields_map[$item['relation_id']] = [
                                        'i' => $index,
                                        'index' => $this->IntToChr($index),
                                        'id' => $item['relation_id'],
                                        'name' => $item['item_name'],
                                        'columns' => json_encode([$item['relation_id']]),
                                        'group_id' => $v['group_id'],
                                        'is_list' => 0,
                                        'relation_field' => ''
                                    ];

                                    $index++;
                                }
                            }
                        }

                        // 产品编号特殊处理
                        if ($v['id'] == 'product_no') {
                            $fields_map['product_number'] = [
                                'i' => $index,
                                'index' => $this->IntToChr($index),
                                'id' => 'product_number',
                                'name' => '序号',
                                'columns' => $v['columns'],
                                'group_id' => $v['group_id'],
                                'is_list' => $v['is_list'],
                                'relation_field' => $v['relation_field']
                            ];

                            $index++;
                        }

                        $fields_map[$v['id']] = [
                            'i' => $index,
                            'index' => $this->IntToChr($index),
                            'id' => $v['id'],
                            'name' => $v['name'],
                            'columns' => $v['columns'],
                            'group_id' => $v['group_id'],
                            'is_list' => $v['is_list'],
                            'relation_field' => $v['relation_field']
                        ];

                        $index++;

                        // 客户编号特殊处理
                        if ($v['id'] == 'company_id') {
                            $fields_map['company'] = [
                                'i' => $index,
                                'index' => $this->IntToChr($index),
                                'id' => 'company',
                                'name' => '客户编号',
                                'columns' => $v['columns'],
                                'group_id' => $v['group_id'],
                                'is_list' => $v['is_list'],
                                'relation_field' => $v['relation_field']
                            ];

                            $index++;
                        }


                    }
                }
            }
        }

        // 导出 Excel 文件
        list($file_path, $file_name) = $this->createExcel($user->getUserId(), $fields_map, $list, $invoice_type, $task_type);

        $task->begin();
        echo "begin upload result \n";

        // 上传结果
        $upload = UploadService::uploadRealFile($file_path, $file_name, UploadService::getFileKey($file_path));
        $task->finish($upload->getFileId(), $total_count, 0);

        echo $file_path, PHP_EOL, 'finish';
    }

    /**
     * 生成 Excel 列标
     *
     * @param $index
     * @param int $start
     * @return string
     */
    private function IntToChr($index, $start = 65)
    {
        $str = '';
        if (floor($index / 26) > 0) {
            $str .= $this->IntToChr(floor($index / 26)-1);
        }
        return $str . chr($index % 26 + $start);
    }

    /**
     * 格式化字段数据
     *
     * @param $field_id
     * @param $value
     * @return false|string
     */
    private function formatValue($field_id, $value)
    {
        if (in_array($field_id, ['create_time', 'update_time', 'account_date'])) {
            $format_value = date('Y-m-d', strtotime($value));
            $value = $format_value == '1970-01-01' ? '' : $format_value;
        }

        switch ($field_id) {
            case 'create_time':
            case 'update_time':
            case 'account_date':
                $format_value = date('Y-m-d', strtotime($value));
                $value = $format_value == '1970-01-01' ? '' : $format_value;
                break;

            case 'exchange_rate':
                $value = sprintf('%.2f', round($value / 100, 2));
                break;
            case 'gross_profit_margin':
                if(!empty($value)){
                    $value = sprintf('%.2f', round($value, 2)).'%';
                }
                break;
            default:
                $value = Util::unEmoji($value);
                break;
        }

        if ($value === null) {
            return '';
        }
        $value = ' ' . Util::replaceBrToLinebreak($value);

        return $value;
    }

    /**
     * 获得特殊字段的数据值
     *
     * @param $invoice_type
     * @param $field
     * @param $user_id
     * @param $client_id
     * @return mixed
     */
    private function specialField($invoice_type, $field, $user_id, $client_id)
    {
        foreach ($field as $key => $item) {
            $item = array_unique($item);
            $value = [];
            switch ($key) {

                // 用户数据
                case 'create_user':
                case 'users':
                case 'handler':
                    $item =array_filter($item, function ($id) {
                       return $id != '{}';
                    });
                    $list = \UserInfo::findAllByIds($item);
                    $list = array_map(function($elem) {
                        return ['id'=>$elem['user_id'],'value'=>$elem['nickname']];
                    }, $list);
                    $value = array_combine(array_column($list,'id'), $list);
                    break;

                // 客户公司数据
                case 'company_id':
                    $list = new CompanyList($user_id);
                    $list->setSkipPrivilege(true);
                    $list->setCompanyIds($item);
                    $list->setFields(['company_id', 'name', 'is_archive', 'serial_id']);
                    $list = $list->find();

                    foreach ($list as $elem){
                        $value[$elem['company_id']] = [
                            'id' => $elem['company_id'],
                            'value' => $elem['name'],
                            'serial_id' => $elem['serial_id']
                        ];
                    }
                    break;

                // 商机字段
                case 'opportunity_id':
                    $list = new \common\library\opportunity\OpportunityList($client_id);
                    $list->setViewingUserId($user_id);
                    $list->setOpportunityIds(array_values($item));
                    $list->setFields(['opportunity_id', 'name']);
                    $list->setSkipPermissionCheck(true);
                    $list = $list->find();

                    foreach ($list as $elem){
                        $value[$elem['opportunity_id']] = [
                            'id' => $elem['opportunity_id'],
                            'value' => $elem['name'],
                        ];
                    }
                    break;

                // 单据状态查询
                case 'status':
                    $list = new InvoiceStatusService($client_id, $invoice_type);
                    $list = $list->baseInfoList(false, $item,false);
                    $list = array_map(function($elem) {
                        return ['id' => $elem['id'], 'value' => $elem['name']];
                    }, $list);
                    $value = array_combine(array_column($list,'id'), $list);
                    break;


                // 客户数据查询
                case 'customer_id':
                    $list = new CustomerList($client_id);
                    $list->setCustomerId($item);
                    $list->setFields(['customer_id', 'name', 'email']);
                    $list = $list->find();

                    foreach ($list as $elem){
                        if (empty($elem['name'])) $elem['name'] = $elem['email'];
                        $value[$elem['customer_id']] = ['id' => $elem['customer_id'], 'value' => $elem['name']];
                    }
                    break;
                case 'departments':
                    $departmentService = new \common\library\department\DepartmentService($client_id);
                    $list = $departmentService->batchGetDepartmentListForIds($item);
                    $list = array_map(function($elem) {
                        return ['id'=>$elem['id'],'value'=>$elem['name']];
                    }, $list);
                    $value = array_combine(array_column($list,'id'), $list);
                    break;
            }

            $field[$key] = $value;
        }


        return $field;
    }

    /**
     * 获取产品类型
     *
     * @param $category_ids
     * @return string
     */
    private function getCategory($category_ids) {
        $name = '';
        $list = Category::findAllByIds($category_ids);
        foreach ($list as $item) {
            $name = "{$item->cn_name}（{$item->en_name}）";
        }

        return $name;
    }

    /**
     * 创建 Excel 表格
     *
     * @param $user_id
     * @param $columns
     * @param $data
     * @param $invoice_type 1=订单，2=报价单
     * @param $task_type 1=简洁版，2=详细版
     * @throws \PhpOffice\PhpSpreadsheet\Exception
     * @throws \PhpOffice\PhpSpreadsheet\Writer\Exception
     */
    private function createExcel($user_id, $columns, $data, $invoice_type, $task_type)
    {
        $excel = new Spreadsheet();
        $excel->setActiveSheetIndex(0);
        $excel->getDefaultStyle()->getFont()->setName('宋体');

        // 设置表头列值
        $row = 1;
        $column = 0;
        foreach ($columns as $key => $val) {

            // 简洁版不需要提供产品列表
            if ($task_type == CustomerExportTasK::TYPE_CONCISE && $val['is_list'] == 1 && $val['group_id'] == CustomFieldService::ORDER_GROUP_PRODUCT) {
                continue;
            }

            $columns[$key]['index'] = $val['index'] = $this->IntToChr($column);
            $excel->getActiveSheet()->getColumnDimension($val['index'])->setWidth(20);
            $excel->getActiveSheet()->setCellValue("{$val['index']}{$row}", $val['name']);
            $column++;
        }

        // 设置每列的值
        foreach ($data as $elem) {
            $row++;

            // 自定义字段放入当前数组处理
            if (isset($elem['external_field_data']) && is_array($elem['external_field_data'])) {
                $elem += $elem['external_field_data'];
            }

            // 单据字段数值写入 Excel 当前列
            foreach ($elem as $id => $field) {
                if (isset($columns[$id])) {

                    // 简洁版不需要提供产品列表
                    if ($task_type == CustomerExportTasK::TYPE_CONCISE && $columns[$id]['is_list'] ==1 && $columns[$id]['group_id'] == CustomFieldService::ORDER_GROUP_PRODUCT) {
                        continue;
                    }

                    $cel = $columns[$id]['index'] . $row;
                    if (is_array($field)) {
                        switch ($id) {
                            case 'users':
                                $excel->getActiveSheet()->setCellValue($cel, implode(';', array_map(function($val) {
                                    return "{$val['user_id']} ({$val['rate']}%)";
                                }, $field)));
                                break;
                            case 'departments':
                                $excel->getActiveSheet()->setCellValue($cel, implode(';', array_map(function($val) {
                                    return "{$val['department_id']} ({$val['rate']}%)";
                                }, $field)));
                                break;
                            case 'cost_list':
                                $excel->getActiveSheet()->setCellValue($cel, implode(';',array_filter(array_map(function ($val) {
                                    if (isset($val['cost_item_relation_id']) && !empty($val['cost_item_relation_id'])) {
                                        return '';
                                    }
                                    $val['cost'] = number_format($val['cost'] ?: 0, 2);
                                    return "{$val['cost_name']}: {$val['cost']}";
                                }, $field))));
                                break;

                            default:
                                $excel->getActiveSheet()->setCellValue($cel, implode(';', $field));
                                break;
                        }
                    } else {
                        $excel->getActiveSheet()->setCellValue($cel, $this->formatValue($id, $field));
                    }
                }
            }

            // 产品列表，详细版需要提供
            $product_total = isset($elem['product_list']) ? count($elem['product_list']) : 0;
            if ($task_type == CustomerExportTask::TYPE_DETAILED && $product_total > 0) {
                foreach ($elem['product_list'] as $j => $product) {
                    // 从第二件产品起，需要重复设置单据相关字段值
                    foreach ($elem as $id => $field) {
                        if (isset($columns[$id])) {
                            $cel = $columns[$id]['index'] . $row;
                            if (is_array($field)) {
                                switch ($id) {
                                    case 'users':
                                        $excel->getActiveSheet()->setCellValue($cel, implode(';', array_map(function($val) {
                                            return "{$val['user_id']} ({$val['rate']}%)";
                                        }, $field)));
                                        break;
                                    case 'departments':
                                        $excel->getActiveSheet()->setCellValue($cel, implode(';', array_map(function($val) {
                                            return "{$val['department_id']} ({$val['rate']}%)";
                                        }, $field)));
                                        break;
                                    case 'cost_list':
                                        $excel->getActiveSheet()->setCellValue($cel, implode(';',array_filter(array_map(function ($val) {
                                            if (isset($val['cost_item_relation_id']) && !empty($val['cost_item_relation_id'])) {
                                                return '';
                                            }
                                            $val['cost'] = number_format($val['cost'] ?: 0, 2);
                                            return "{$val['cost_name']}: {$val['cost']}";
                                        }, $field))));
                                        break;

                                    default:
                                        $excel->getActiveSheet()->setCellValue($cel, implode(';', $field));
                                        break;
                                }
                            } else {
                                $excel->getActiveSheet()->setCellValue($cel, $this->formatValue($id, $field));
                            }
                        }
                    }

                    // 设置产品列表相关字段值
                    // 自定义字段放入当前数组处理
                    if (isset($product['external_field_data']) && is_array($product['external_field_data'])) {
                        $product += $product['external_field_data'];
                    }

                    foreach ($product as $id => $field) {
                        $id = str_replace(['[', ']', '"', "'"], '', $id);       // 临时逻辑，针对custom_field字段column格式错误的字段特殊处理，如["xxx"]处理成 xxx
                        if (isset($columns[$id])) {
                            $cel = $columns[$id]['index'] . $row;
                            if (is_array($field)) {
                                $field = $columns[$id]['relation_field'] === 'category_ids' ? [$this->getCategory($field)] : $field;
                                if (count($field) !== count($field, COUNT_RECURSIVE)) { // 处理多维数组
                                    array_walk($field, function (&$item) {
                                        /**
                                         * @TODO 部分字段类型不支持，导出脚本需要迁移到 QuotationListExport
                                         * @see QuotationListExport::class
                                         */
                                        if (!is_string(($item['name'] ?? '')) || !is_string($item['value'] ?? '')) {
                                            $item = '';
                                            return;
                                        }
                                        $item = ($item['name'] ?? '') . ': ' . ($item['value'] ?? '--');
                                    });
                                }
                                $excel->getActiveSheet()->setCellValue($cel, implode(';', $field));
                            } else {
                                // 产品编号特殊处理
                                if ($id === 'product_no') {
                                    $excel->getActiveSheet()->setCellValue($columns['product_number']['index'] . $row, $this->formatValue('product_number', $j + 1));
                                }

                                $field = $columns[$id]['relation_field'] === 'group_id' ? \common\library\group\Helper::getGroupName(Constants::TYPE_PRODUCT, $field) : $field;
                                $excel->getActiveSheet()->setCellValue($cel, $this->formatValue($id, $field));
                            }
                        }
                    }

                    // 产品列表，换行
                    if ($j < $product_total - 1) {
                        $row++;
                    }
                }
            }
        }

        $file_path = '/tmp/invoice_list_export_' . $user_id . '_' . time() . '.xls';
        $file_name = $this->task_type_map[$task_type] . $this->invoice_type_map[$invoice_type];
        $file_name .= date('Ymd') . '.xlsx';
        $xlsWriter = new Xlsx($excel);
        $xlsWriter->save($file_path);

        return [$file_path, $file_name];
    }

    /**
     * @param $filePath
     * <AUTHOR>
     * @date 2020-04-17 15:26
     * 删除单据导出产生的临时文件
     */
    public function actionCleanTmpFile($filePath) {

        $filePath = explode(',',$filePath);
        foreach ($filePath as $path) {
            unlink($path);
        }
    }

    /**
     * @see OrderReadController::actionExportByFilter() 导出订单
     */
    public function actionRunExport($userId, $taskId, $referType, $showProduct, $queryParams, $fields)
    {

        LogUtil::info(sprintf("runExportStrat@start:user_id[%s] taskId[%s] referType[%s]", $userId, $taskId, $referType));

	    ini_set("memory_limit", "8096M");

	    parse_str($queryParams, $queryParams);
        parse_str($fields, $fields);

        User::setLoginUserById($userId);
        $user = User::getLoginUser();

        $invoiceFields = $fields['invoice_fields']??[];
        $productFields = $fields['product_fields']??[];

        $showProduct = $showProduct ? 1 : 0;
        $productFields = $showProduct ? $productFields : [];

        //todo 目前还是获取tbl_order 表中的 product_list 进行订单产品的数据渲染 ； 这里后续需要处理为获取tbl_invoice_record进行处理
        $export = ($referType == CustomerExportTask::TYPE_ORDER)
            ? new \common\library\invoice\export_from_filter\OrderListExport($user->getClientId(), $taskId)
            : new \common\library\invoice\export_from_filter\QuotationListExport($user->getClientId(), $taskId);
        $export->setOperatorUserId($userId);
        $export->setQueryParams($queryParams);
        $export->setInvoiceFields($invoiceFields);
        $export->setShowProduct($showProduct ? 1 : 0);
        $export->setProductFields($productFields);
        $export->run();
        LogUtil::info(sprintf("runExportFinish@start:user_id[%s] taskId[%s]", $userId, $taskId));
    }

    //crm订单产品总金额刷数
    public function actionRefreshOrderProductAmount($clientId, $grey = 1, $expFlag = 0)
    {
        self::info('start');
        if ($clientId) {
            $clientIds = [$clientId];
        } elseif ($grey) {
            $clientIds = \common\library\GreyEnvHelper::getGreyClientIds();
        } elseif ($expFlag) {
            $clientIds = \common\library\account\Client::getExpClientIds();
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        foreach ( $clientIds as $clientId ) {
            if(in_array($clientId,[2,7])){
                continue;
            }
            $db = PgActiveRecord::getDbByClientId($clientId);
            $type = \common\library\invoice\Order::TYPE_CRM_ORDER;
            $sql = "select order_id,product_total_amount,exchange_rate,exchange_rate_usd from tbl_order where client_id = {$clientId} and source_type = {$type} and product_total_amount >0 ORDER  by order_id asc";
            $list = $db->createCommand($sql)->queryAll();
            if(!$list){
                continue;
            }
            $chunkList = array_chunk($list,50);
            $db = PgActiveRecord::getDbByClientId($clientId);
            foreach ($chunkList as $number => $chunkItem){
                $sqlList = [];
                foreach ($chunkItem as $item){
                    $productTotalAmountRmb = round($item['product_total_amount'] * ($item['exchange_rate'] / 100), 5);
                    $productTotalAmountUsd = round($item['product_total_amount'] * $item['exchange_rate_usd'] / 100, 5);
                    $sqlList[] = "update tbl_order set product_total_amount_usd  = {$productTotalAmountUsd},product_total_amount_rmb = {$productTotalAmountRmb} WHERE client_id = {$clientId} AND order_id = {$item['order_id']}";
                }
                $sql = join(';',$sqlList);
                $res = $db->createCommand($sql)->execute();
                self::info("[client_id:{$clientId}] number:{$number} res:{$res} count:".count($sqlList));
            }
        }
        self::info('finish');
    }

    protected function setCostItemFieldName()
    {
        $costItemInvoiceRelationList = CostItemInvoiceRelationApi::getCostItemRelationByInvoice(\Constants::TYPE_QUOTATION);
        $costItemRelationMap = array_column($costItemInvoiceRelationList, null, 'relation_id');

        return $costItemRelationMap;
    }
}