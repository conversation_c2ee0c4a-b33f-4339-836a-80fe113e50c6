<?php


use common\library\alibaba\order\AlibabaOrderSyncHelper;
use common\library\alibaba\services\AlibabaOrder;
use common\library\alibaba\services\AlibabaProductGroup;
use common\library\alibaba\store\AlibabaStore;
use common\library\APIConstant;
use common\library\async_task\AsyncTaskFilter;
use common\library\custom_field\CustomFieldFormatter;
use common\library\custom_field\FieldList;
use common\library\history\invoice\OrderBuilder;
use common\library\oms\common\OmsConstant;
use common\library\oms\inquiry_feedback\InquiryFeedBack;
use common\library\oms\inquiry_feedback\InquiryFeedBackFilter;
use common\library\oms\warehouse_return_invoice\purchase\PurchaseReturnApi;
use common\library\oms\warehouse_return_invoice\purchase\PurchaseReturnInvoice;
use common\library\util\Arr;
use common\library\util\PgsqlUtil;
use common\library\validation\Validator;

class CondyCommand extends CrontabCommand
{


    public function actionTest123()
    {
        \User::setLoginUserById(11864037);

        $clientId = 14367;
        $adminUserId = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();
        if (!$adminUserId) {
            LogUtil::info("clientId:$clientId adminUser not exist!");
            return;
        }

        User::setLoginUserById($adminUserId);
        $data = "[{\"id\":1,\"name\":\"基本信息\",\"can_add_custom_field\":true,\"is_hide\":false,\"fields\":{\"1101060387\":[],\"1101060390\":\"多行文本默认值\",\"1101060392\":\"100\",\"1101060395\":\"\",\"1101171288\":0,\"1105112312\":\"\",\"1105112317\":\"\",\"1105748394\":\"\",\"1106123057\":253,\"1106123086\":22,\"1106223473\":-1253,\"1106223774\":99.9639,\"3118802410\":\"\",\"3160063335\":[],\"3163525048\":1,\"3163668035\":\"\",\"3163734779\":0,\"3180373004\":[],\"3442113999\":\"1000\",\"3447636020\":\"\",\"3449238368\":[[\"1\",\"101\",\"10101\"]],\"3449777714\":[33],\"3449777784\":1,\"3449778077\":[3570168583],\"3449778080\":{\"tel_area_code\":355,\"tel\":\"110110110110\"},\"3449778082\":\"低纬度\",\"3449778089\":\"https://3838haiduwhd.com\",\"3477298105\":\"2024-09-25\",\"3477358481\":\"\",\"3493003405\":\"\",\"3493493627\":\"互联网\",\"3493493660\":\"中国\",\"3493493670\":\"东八区：北京，香港，台湾\",\"3493493681\":\"2022-10-12 14:56:49\",\"3493493702\":\"VIP客户\",\"3493493745\":\"客户3-edit\",\"3493493806\":\"\",\"3510397161\":\"\",\"3515009901\":\"<EMAIL>\",\"3515009907\":\"dev\",\"3515270709\":\"\",\"3572376163\":111,\"3572379995\":111,\"**********\":\"\",\"**********\":\"4星\",\"**********\":\"\",\"**********\":100,\"**********\":\"\",\"**********\":\"\",\"**********\":\"\",\"**********\":\"三星的公司\",\"**********\":\"\",\"**********\":\"\",\"**********\":\"\",\"**********\":\"\",\"**********\":\"\",\"**********\":\"\",\"**********\":\"\",\"**********\":\"73\",\"**********\":\"73\",\"**********\":\"\",\"handler\":[\"********\"],\"order_no\":\"\",\"name\":\"\",\"exchange_rate\":100,\"status\":\"0\",\"exchange_rate_usd\":14.2857,\"account_date\":\"2024-09-14\",\"currency\":\"CNY\",\"users\":[{\"user_id\":\"********\",\"rate\":100}],\"departments\":[{\"rate\":100,\"department_id\":\"144\"}],\"company_id\":**********,\"opportunity_id\":\"\",\"customer_id\":**********,\"customer_name\":\"dev\",\"customer_phone\":\"213 *********\",\"company_address\":\"低纬度\",\"price_contract\":\"DEQ\",\"bank_info\":\"测试银行信息默认值\",\"receive_remittance_way\":\"PayPal\",\"tax_refund_type\":\"2\",\"shipment_deadline_remark\":\"预付款7天之后\",\"remark\":\"\",\"company_fax\":\"哈哈哈dw ae\",\"company_name\":\"三星的公司\",\"company_phone\":\"***************\",\"customer_address\":\"mm\",\"customer_email\":\"<EMAIL>\",\"insurance_remark\":\"\",\"more_or_less\":\"\",\"marked\":\"\",\"order_contract\":\"\",\"package_remark\":\"\",\"price_contract_remark\":\"\",\"receive_remittance_remark\":\"\",\"shipment_deadline\":\"2024-09-27\",\"shipment_port\":\"\",\"target_port\":\"\",\"transport_mode\":\"陆运\",\"country\":\"CN\",\"refer_quotation_no\":\"\",\"service_fee_amount\":1},\"list\":[]},{\"id\":2,\"name\":\"交易产品\",\"can_add_custom_field\":true,\"is_hide\":false,\"fields\":{\"package_volume_amount\":\"361.0541\",\"package_gross_weight_amount\":\"1454.42\",\"product_total_count\":\"22\",\"amount_en_upper\":\"\",\"product_total_amount_en_upper\":\"\",\"parts_total_count\":\"0\",\"product_total_count_no_parts\":\"22\"},\"list\":[{\"1102748156\":242,\"1106224145\":-87,\"3161863808\":\"\",\"3162345910\":\"\",\"3162359671\":\"\",\"3162469705\":\"100\",\"3163352408\":22,\"3163727403\":0,\"3410827449\":{\"product_size_length\":\"111\",\"product_size_weight\":\"222\",\"product_size_height\":\"333\"},\"3410827451\":\"32.2\",\"3410827454\":\"8.205786\",\"3410827459\":\"将快乐赶的好远@‘'“、@#¥？》《><:、｜|\",\"3413022306\":\"323\",\"3442113436\":\"0\",\"3447805973\":\"fefv将快乐赶的好远@‘'“、@#¥？》《><:、｜|\",\"3448032730\":\"\",\"3448032732\":\"\",\"3449279514\":[{\"bucket\":\"v4client\",\"status\":1,\"file_id\":\"3584165484\",\"user_id\":********,\"file_key\":\"other/doc/********/20e22962135273db5119daeb75bd74808a214ab6c64fe88306d05d50a6b47a99.xlsx\",\"file_name\":\"简洁版订单20240716 (2).xlsx\",\"file_path\":\"https://v4client.oss-cn-hangzhou.aliyuncs.com/other/doc/********/20e22962135273db5119daeb75bd74808a214ab6c64fe88306d05d50a6b47a99.xlsx\",\"file_size\":\"8651\",\"file_preview_url\":\"/api/documentpreview/office?key=other%252Fdoc%252F********%252F20e22962135273db5119daeb75bd74808a214ab6c64fe88306d05d50a6b47a99.xlsx&name=%25E7%25AE%2580%25E6%25B4%2581%25E7%2589%2588%25E8%25AE%25A2%25E5%258D%259520240716%2520%25282%2529.xlsx&sign=242302ae53eabe9d28bcf2b135885193&bucket=v4client\",\"mime_type\":\"\",\"create_user\":\"********\",\"create_time\":\"2024-07-17 10:30:45\"}],\"3449443402\":3454325013,\"3449443407\":\"Bale\",\"3449443409\":[{\"bucket\":\"v4client\",\"status\":1,\"file_id\":\"3584165538\",\"user_id\":********,\"file_key\":\"invoice-file/img/249520527/37a36aa206b71d6c7177f419e5e98a7f05830d2f9906c02d89f6166dc346c9d8.jpeg\",\"file_name\":\"3副本.jpeg\",\"file_path\":\"https://v4client.oss-cn-hangzhou.aliyuncs.com/invoice-file/img/249520527/37a36aa206b71d6c7177f419e5e98a7f05830d2f9906c02d89f6166dc346c9d8.jpeg\",\"file_size\":\"136828\",\"file_preview_url\":\"https://v4client.oss-cn-hangzhou.aliyuncs.com/invoice-file%2Fimg%2F249520527%2F37a36aa206b71d6c7177f419e5e98a7f05830d2f9906c02d89f6166dc346c9d8.jpeg?response-content-disposition=inline%3B%20filename%3D3%25E5%2589%25AF%25E6%259C%25AC.jpeg%3B%20filename%2A%3Dutf-8%27%273%25E5%2589%25AF%25E6%259C%25AC.jpeg&response-content-type=image%2Fjpeg&OSSAccessKeyId=LTAI5tABwJoaZbSyGLBru6CC&Signature=1QwvqsQ3W9b2EeGSrcTNp8SzNYU%3D&Expires=1727156533\",\"mime_type\":\"\",\"create_user\":\"********\",\"create_time\":\"2024-07-17 10:32:12\"}],\"3449443411\":\"\",\"3449443414\":[4,415,100000674],\"3449443418\":[{\"name\":\"Fiber Type\",\"type\":\"select\",\"value\":\"362133\",\"options\":[{\"label\":\"Other\",\"value\":\"4\"},{\"label\":\"Staple\",\"value\":\"362133\"},{\"label\":\"Top\",\"value\":\"362134\"},{\"label\":\"Tow\",\"value\":\"362135\"}],\"format\":\"Staple\"},{\"name\":\"Pattern\",\"type\":\"select\",\"value\":\"362147\",\"options\":[{\"label\":\"Dyed\",\"value\":\"580\"},{\"label\":\"Raw\",\"value\":\"362147\"},{\"label\":\"Other\",\"value\":\"4\"},{\"label\":\"Bleached\",\"value\":\"93\"}],\"format\":\"Raw\"}],\"3450286195\":\"dwhed\",\"3450308119\":\"jierf3\",\"3450308121\":\"将快乐赶的好远@‘'“、@#¥？》《><:、｜|\",\"3450308176\":\"2024-07-17\",\"3450308178\":\"324\",\"3450308180\":[{\"bucket\":\"v4client\",\"status\":1,\"file_id\":\"3584165580\",\"user_id\":********,\"file_key\":\"invoice-file/img/249520527/37a36aa206b71d6c7177f419e5e98a7f05830d2f9906c02d89f6166dc346c9d8.jpeg\",\"file_name\":\"3副本.jpeg\",\"file_path\":\"https://v4client.oss-cn-hangzhou.aliyuncs.com/invoice-file/img/249520527/37a36aa206b71d6c7177f419e5e98a7f05830d2f9906c02d89f6166dc346c9d8.jpeg\",\"file_size\":\"136828\",\"file_preview_url\":\"https://v4client.oss-cn-hangzhou.aliyuncs.com/invoice-file%2Fimg%2F249520527%2F37a36aa206b71d6c7177f419e5e98a7f05830d2f9906c02d89f6166dc346c9d8.jpeg?response-content-disposition=inline%3B%20filename%3D3%25E5%2589%25AF%25E6%259C%25AC.jpeg%3B%20filename%2A%3Dutf-8%27%273%25E5%2589%25AF%25E6%259C%25AC.jpeg&response-content-type=image%2Fjpeg&OSSAccessKeyId=LTAI5tABwJoaZbSyGLBru6CC&Signature=1QwvqsQ3W9b2EeGSrcTNp8SzNYU%3D&Expires=1727156533\",\"mime_type\":\"\",\"create_user\":\"********\",\"create_time\":\"2024-07-17 10:33:26\"}],\"3450308183\":\"拿铁\",\"3451819597\":\"\",\"3451819793\":\"\",\"3454274429\":\"https://v4client.oss-cn-hangzhou.aliyuncs.com/invoice-file/img/********/5728768aa00d40e075350870fbc91a1c58c863edb55cfba9980b0a4ddeb916a4.png\",\"3457250300\":0,\"3457250309\":314.2854,\"3457280622\":[\"禁止倒立\"],\"3457977854\":\"将快乐赶的好远@‘'“、@#¥？》《><:、｜|\",\"3457977856\":\"简单爱\",\"3457977858\":\"甜甜的\",\"3457977890\":[\"小时光\",\"旧时光\"],\"3457977893\":\"2024-07-18\",\"3457977895\":\"将快乐赶的好远@‘'“、@#¥？》《><:、｜|\",\"3457977898\":\"将快乐赶的好远@‘'“、@#¥？》《><:、｜|\\n将快乐赶的好远@‘'“、@#¥？》《><:、｜|\",\"3457977903\":\"将快乐赶的好远@‘'“、@#¥？》《><:、｜|\",\"3457977910\":[{\"bucket\":\"v4client\",\"status\":1,\"file_id\":\"3584165446\",\"user_id\":********,\"file_key\":\"invoice-file/img/249520527/37a36aa206b71d6c7177f419e5e98a7f05830d2f9906c02d89f6166dc346c9d8.jpeg\",\"file_name\":\"3副本.jpeg\",\"file_path\":\"https://v4client.oss-cn-hangzhou.aliyuncs.com/invoice-file/img/249520527/37a36aa206b71d6c7177f419e5e98a7f05830d2f9906c02d89f6166dc346c9d8.jpeg\",\"file_size\":\"136828\",\"file_preview_url\":\"https://v4client.oss-cn-hangzhou.aliyuncs.com/invoice-file%2Fimg%2F249520527%2F37a36aa206b71d6c7177f419e5e98a7f05830d2f9906c02d89f6166dc346c9d8.jpeg?response-content-disposition=inline%3B%20filename%3D3%25E5%2589%25AF%25E6%259C%25AC.jpeg%3B%20filename%2A%3Dutf-8%27%273%25E5%2589%25AF%25E6%259C%25AC.jpeg&response-content-type=image%2Fjpeg&OSSAccessKeyId=LTAI5tABwJoaZbSyGLBru6CC&Signature=1QwvqsQ3W9b2EeGSrcTNp8SzNYU%3D&Expires=1727156533\",\"mime_type\":\"\",\"create_user\":\"********\",\"create_time\":\"2024-07-17 10:29:37\"}],\"3457977914\":[{\"bucket\":\"v4client\",\"status\":1,\"file_id\":\"3584165447\",\"user_id\":********,\"file_key\":\"other/doc/********/549c1daccd59d6fc4a99813753c91518f62593d14cd58b68019878b9058f43bc.xlsx\",\"file_name\":\"简洁版订单20240716 (3).xlsx\",\"file_path\":\"https://v4client.oss-cn-hangzhou.aliyuncs.com/other/doc/********/549c1daccd59d6fc4a99813753c91518f62593d14cd58b68019878b9058f43bc.xlsx\",\"file_size\":\"8509\",\"file_preview_url\":\"/api/documentpreview/office?key=other%252Fdoc%252F********%252F549c1daccd59d6fc4a99813753c91518f62593d14cd58b68019878b9058f43bc.xlsx&name=%25E7%25AE%2580%25E6%25B4%2581%25E7%2589%2588%25E8%25AE%25A2%25E5%258D%259520240716%2520%25283%2529.xlsx&sign=1e55c8ec599227d5aee51dab0f8a718f&bucket=v4client\",\"mime_type\":\"\",\"create_user\":\"********\",\"create_time\":\"2024-07-17 10:29:38\"}],\"3457977917\":[],\"3457977920\":[{\"bucket\":\"v4client\",\"status\":1,\"file_id\":\"3584165448\",\"user_id\":********,\"file_key\":\"other/doc/249521382/5cadff429c95166aaa97ab5b192cf37f45f96fbbeceeda1beaf1bf2da55d4b43.xls\",\"file_name\":\"副本.xls\",\"file_path\":\"https://v4client.oss-cn-hangzhou.aliyuncs.com/other/doc/249521382/5cadff429c95166aaa97ab5b192cf37f45f96fbbeceeda1beaf1bf2da55d4b43.xls\",\"file_size\":\"307200\",\"file_preview_url\":\"/api/documentpreview/office?key=other%252Fdoc%252F249521382%252F5cadff429c95166aaa97ab5b192cf37f45f96fbbeceeda1beaf1bf2da55d4b43.xls&name=%25E5%2589%25AF%25E6%259C%25AC.xls&sign=f581e389813beacb0a07b901dda98228&bucket=v4client\",\"mime_type\":\"\",\"create_user\":\"********\",\"create_time\":\"2024-07-17 10:29:44\"}],\"3457977923\":\"32\",\"3457977925\":[\"多选2\",\"多选1\"],\"3457977964\":\"3\",\"3457977972\":3.32,\"3457977974\":[{\"bucket\":\"v4client\",\"status\":1,\"file_id\":\"3584165482\",\"user_id\":********,\"file_key\":\"invoice-file/img/249520527/37a36aa206b71d6c7177f419e5e98a7f05830d2f9906c02d89f6166dc346c9d8.jpeg\",\"file_name\":\"3副本.jpeg\",\"file_path\":\"https://v4client.oss-cn-hangzhou.aliyuncs.com/invoice-file/img/249520527/37a36aa206b71d6c7177f419e5e98a7f05830d2f9906c02d89f6166dc346c9d8.jpeg\",\"file_size\":\"136828\",\"file_preview_url\":\"https://v4client.oss-cn-hangzhou.aliyuncs.com/invoice-file%2Fimg%2F249520527%2F37a36aa206b71d6c7177f419e5e98a7f05830d2f9906c02d89f6166dc346c9d8.jpeg?response-content-disposition=inline%3B%20filename%3D3%25E5%2589%25AF%25E6%259C%25AC.jpeg%3B%20filename%2A%3Dutf-8%27%273%25E5%2589%25AF%25E6%259C%25AC.jpeg&response-content-type=image%2Fjpeg&OSSAccessKeyId=LTAI5tABwJoaZbSyGLBru6CC&Signature=1QwvqsQ3W9b2EeGSrcTNp8SzNYU%3D&Expires=1727156533\",\"mime_type\":\"\",\"create_user\":\"********\",\"create_time\":\"2024-07-17 10:30:43\"}],\"3457977978\":\"将快乐赶的好远@‘'“、@#¥？》《><:、｜|\",\"3457977980\":3.3232,\"3457977982\":\"将快乐赶的好远@‘'“、@#¥？》《><:、｜|\",\"3457977989\":{\"package_size_length\":\"222\",\"package_size_weight\":\"333\",\"package_size_height\":\"444\"},\"3457977993\":\"木质架子\",\"3457977996\":[\"2.3434\"],\"3457978000\":{\"carton_size_length\":\"33\",\"carton_size_weight\":\"333\",\"carton_size_height\":\"333\"},\"3457978004\":\"3.659337\",\"3457978009\":\"3.23\",\"3457978011\":\"3.3\",\"3457978200\":\"3\",\"3457978202\":\"发么人非\",\"3457978205\":\"非么人\",\"3457978216\":\"add\",\"3457978221\":[\"2\"],\"3457978223\":[{\"bucket\":\"v4client\",\"status\":1,\"file_id\":\"3584165581\",\"user_id\":********,\"file_key\":\"other/doc/********/2aaee28352643f75ad08f0ce833ceb2dedcb56069a3cb8f496e725a3f547d846.xlsx\",\"file_name\":\"详细版订单20240711 (2).xlsx\",\"file_path\":\"https://v4client.oss-cn-hangzhou.aliyuncs.com/other/doc/********/2aaee28352643f75ad08f0ce833ceb2dedcb56069a3cb8f496e725a3f547d846.xlsx\",\"file_size\":\"6636\",\"file_preview_url\":\"/api/documentpreview/office?key=other%252Fdoc%252F********%252F2aaee28352643f75ad08f0ce833ceb2dedcb56069a3cb8f496e725a3f547d846.xlsx&name=%25E8%25AF%25A6%25E7%25BB%2586%25E7%2589%2588%25E8%25AE%25A2%25E5%258D%259520240711%2520%25282%2529.xlsx&sign=b1ad7b46dc06d39a191c118dd33daa70&bucket=v4client\",\"mime_type\":\"\",\"create_user\":\"********\",\"create_time\":\"2024-07-17 10:33:29\"}],\"3457978232\":\"将快乐赶的好远@‘'“、@#¥？》《><:、｜|\",\"3460471814\":[{\"bucket\":\"v4client\",\"status\":1,\"file_id\":\"3584165551\",\"user_id\":********,\"file_key\":\"other/doc/********/20e22962135273db5119daeb75bd74808a214ab6c64fe88306d05d50a6b47a99.xlsx\",\"file_name\":\"简洁版订单20240716 (2).xlsx\",\"file_path\":\"https://v4client.oss-cn-hangzhou.aliyuncs.com/other/doc/********/20e22962135273db5119daeb75bd74808a214ab6c64fe88306d05d50a6b47a99.xlsx\",\"file_size\":\"8651\",\"file_preview_url\":\"/api/documentpreview/office?key=other%252Fdoc%252F********%252F20e22962135273db5119daeb75bd74808a214ab6c64fe88306d05d50a6b47a99.xlsx&name=%25E7%25AE%2580%25E6%25B4%2581%25E7%2589%2588%25E8%25AE%25A2%25E5%258D%259520240716%2520%25282%2529.xlsx&sign=242302ae53eabe9d28bcf2b135885193&bucket=v4client\",\"mime_type\":\"\",\"create_user\":\"********\",\"create_time\":\"2024-07-17 10:32:47\"}],\"3483655566\":0,\"3493010523\":\"222\",\"3498496734\":257.5455,\"3498506009\":303,\"3499341251\":\"\",\"3503170506\":\"\",\"3513072744\":\"\",\"3560103051\":[],\"3560103055\":\"\",\"3572250727\":\"\",\"3600402212\":\"\",\"3604003275\":708.4,\"3611306067\":[\"萝卜兔子\",\"价格多选2\"],\"3611308697\":\"\",\"3611308915\":\"\",\"product_id\":3564779431,\"offer_data\":\"\",\"sku_attributes\":[],\"platform_product_info\":\"0\",\"unique_id\":\"\",\"task_outbound_count\":\"\",\"task_purchase_count\":\"\",\"combine_product_id\":\"\",\"combine_product_no\":\"\",\"is_sub_product\":\"\",\"product_disable_flag\":\"\",\"product_type\":1,\"to_outbound_count\":\"\",\"to_purchase_count\":\"\",\"purchase_cost_unit_rmb\":\"\",\"product_enable_flag\":\"\",\"todo_shipping_count\":\"\",\"product_no\":\"&*(9)))\",\"product_image\":\"https://v4client.oss-cn-hangzhou.aliyuncs.com/invoice-file/oth/********/ce0d0a1e934d2404bbd5b299abdfedac4c87be5013b22e092a6228407c6a1643.PNG\",\"product_name\":\"测试订单产品导入新逻辑\",\"product_cn_name\":\"dwhed\",\"sku_id\":3564779432,\"product_model\":\"jierf3\",\"unit_price\":11,\"count\":22,\"cost_amount\":253,\"gross_margin\":\"-73\",\"gross_profit_margin\":\"-663.6364\",\"cost_with_tax\":\"84.00\",\"package_volume\":\"32.8231\",\"unit\":\"Ampere\",\"product_remark\":\"将快乐赶的好远@‘'“、@#¥？》《><:、｜|\",\"description\":\"将快乐赶的好远@‘'“、@#¥？》《><:、｜|\",\"product_package_remark\":\"将快乐赶的好远@‘'“、@#¥？》《><:、｜|\",\"other_cost\":\"11\",\"vat_rate\":\"44\",\"tax_refund_rate\":\"33\",\"count_per_package\":\"2\",\"package_gross_weight\":\"132.22\",\"package_count\":\"11\",\"package_gross_weight_subtotal\":\"1454.42\",\"enable_count\":23,\"package_volume_subtotal\":\"361.0541\",\"package_unit\":\"Bale\",\"hs_code\":\"323\",\"is_master_product\":0,\"master_id\":\"\",\"ratio\":\"\",\"master_group_id\":0,\"product_images\":[{\"file_id\":\"3580873707\",\"file_name\":\"https___v4client.oss-cn-hangzhou.aliyuncs.com_invoice-file_oth_55503922_411bc42c6b43cc8e28c6c10f7884292859a9932e13ae0579c421f9c854cd68ea.PNG\",\"file_size\":\"49093\",\"mime_type\":\"\",\"file_key\":\"invoice-file/oth/********/ce0d0a1e934d2404bbd5b299abdfedac4c87be5013b22e092a6228407c6a1643.PNG\",\"file_path\":\"https://v4client.oss-cn-hangzhou.aliyuncs.com/invoice-file/oth/********/ce0d0a1e934d2404bbd5b299abdfedac4c87be5013b22e092a6228407c6a1643.PNG\",\"file_preview_url\":\"https://v4client.oss-cn-hangzhou.aliyuncs.com/invoice-file%2Foth%2F********%2Fce0d0a1e934d2404bbd5b299abdfedac4c87be5013b22e092a6228407c6a1643.PNG?response-content-disposition=inline%3B%20filename%3Dhttps___v4client.oss-cn-hangzhou.aliyuncs.com_invoice-file_oth_55503922_411bc42c6b43cc8e28c6c10f7884292859a9932e13ae0579c421f9c854cd68ea.PNG%3B%20filename%2A%3Dutf-8%27%27https___v4client.oss-cn-hangzhou.aliyuncs.com_invoice-file_oth_55503922_411bc42c6b43cc8e28c6c10f7884292859a9932e13ae0579c421f9c854cd68ea.PNG&response-content-type=image%2Fpng&OSSAccessKeyId=LTAI5tABwJoaZbSyGLBru6CC&Signature=mUxEphvwSFUxQ5%2B%2BV3hfI9fnsT8%3D&Expires=1727156534\",\"create_user\":\"********\",\"create_time\":\"2024-07-08 14:45:15\"}]}]},{\"id\":3,\"name\":\"费用信息\",\"can_add_custom_field\":true,\"is_hide\":false,\"fields\":{\"1106224147\":253,\"3163597936\":36.1428,\"3163748900\":0,\"3444776085\":289.1428,\"3446559740\":0,\"3449804921\":36.1428,\"3455463523\":253,\"3455474923\":253,\"3462478592\":254,\"3512904308\":1,\"product_total_amount\":\"253\",\"product_total_amount_rmb\":\"\",\"product_total_amount_usd\":\"\",\"addition_cost_amount\":\"0\",\"amount\":\"253\",\"amount_rmb\":\"\",\"amount_usd\":\"\",\"cash_collection_info.collect_amount_rmb\":\"\",\"cash_collection_info.collect_amount_usd\":\"\",\"cash_collection_percentage\":\"\",\"cash_collection_info.not_collect_amount_rmb\":\"\",\"cash_collection_info.not_collect_amount_usd\":\"\",\"cost_with_tax_total\":\"1848\",\"order_gross_margin\":\"-1606\",\"service_fee_currency\":\"\",\"service_fee_amount_rmb\":\"\",\"service_fee_amount_usd\":\"\"},\"list\":[]},{\"id\":6,\"name\":\"银行信息\",\"can_add_custom_field\":false,\"is_hide\":false,\"fields\":{\"capital_account_id\":\"\",\"capital_name\":\"\",\"capital_bank\":\"\",\"bank_account\":\"\",\"capital_account_address\":\"\",\"capital_account_remark\":\"\"},\"list\":[]}]";
        $data = json_decode($data, true);
        $attributes = [];
        foreach ($data as $group) {
            if ($group['id'] == \common\library\custom_field\CustomFieldService::ORDER_GROUP_PRODUCT) {
                $productList = $group['list'];
            } elseif ($group['id'] == \common\library\custom_field\CustomFieldService::ORDER_GROUP_FEE) {
                foreach ($group['list'] as &$item) {
                    if (isset($item['percent_of_total_amount'])) {
                        foreach ($item['percent_of_total_amount'] as $costKey => $value) {
                            $item[$costKey] = $value;
                        }
                        unset($item['percent_of_total_amount']);
                    }
                }
                $costList = $group['list'];
            }
            foreach ($group['fields'] as $field => $value) {
                $attributes[$field] = $value;
            }
        }

        AlibabaOrderSyncHelper::createSubAliOrder($result['order_id'] ?? 444,$data, [
            123=>[
                444
            ]
        ]);

    }

    public function actionTryCreateOrder()
    {
        $client_id = 14367;
        $store_id = 244059865;
        $adminUserId = \common\library\privilege_v3\PrivilegeService::getInstance($client_id)->getAdminUserId();
        if (!$adminUserId) {
            LogUtil::info("clientId:$client_id adminUser not exist!");
            return;
        }

        User::setLoginUserById($adminUserId);

        $alibabaStore = new AlibabaStore($client_id, $store_id);

        $sessionKey = $alibabaStore->getAccessToken();

        $order_params = [
            'logistics_detail' => [
                "shipment_address" => [
                    "snapshot_id" => '0002175395197003',
                ],
                "shipment_method" => '海运(sea)',
                "trade_term" => 'CPT',
//                    "carrier" =>  '',
//                    "carrier_code" =>  '',
                "shipment_date" => [
                    "duration" => 10,
                    "date" => [
                        "format_date" => '2024-10-11',
                    ],
                    "type" => 'absolute'
                ],
            ],

            "fulfillment_channel" => 'TAD',

            "payment_detail" => [
                "shipment_fee" => 20,
                "shipment_insurance_fee" => 80,
                "initial_amount" => 100,
            ],
            "target_participant" => [
                "buyer_email" => '<EMAIL>'
            ],
            "remark" => '我是备注1',
            "currency" => 'CNY',
            "scene" => OmsConstant::SELLER_OPEN_CREATE,
            "trade_assurance_type" => OmsConstant::TRADE_ASSURANCE_TYPE_TA_PLUS,
            "product_list" => [
                [
                    'product_id' => '1600955996818',
                    'name' => '测试产品id',
                    'sku_id' => '104650308781',
                    'unit' => '个',
                    'quantity' => '123',
                    'unit_price' => '456',
//                    'product_image' => 'H944aa0aa7c19401cb8390abdd987c51aK.jpg'
                ]
            ]
        ];

        try {
            (new Validator($order_params, [
                'logistics_detail.shipment_address' => 'required|array',
                'logistics_detail.shipment_address.snapshot_id' => 'required|string',
                'logistics_detail.shipment_method' => 'required|in:' . implode(',', OmsConstant::SHIPMENT_METHOD_MAP),
                'logistics_detail.trade_term' => 'required|in:' . implode(',', OmsConstant::TRADE_TERM_MAP),
                'logistics_detail.shipment_date' => 'required|array',
                'logistics_detail.shipment_date.duration' => 'numeric',
                'logistics_detail.shipment_date.date.format_date' => 'string',
                'logistics_detail.shipment_date.type' => 'required|string|in:' . implode(',', OmsConstant::SHIPPING_METHOD_TYPE_MAP),
                'fulfillment_channel' => 'required|string|in:' . implode(',', OmsConstant::FULFILLMENT_CHANNEL_MAP),
                'payment_detail' => 'required|array',
                'payment_detail.shipment_fee' => 'numeric',
                'payment_detail.shipment_insurance_fee' => 'numeric',
                'payment_detail.initial_amount' => 'required|numeric',
                'target_participant' => 'required|array',
                'target_participant.buyer_email' => 'required|string',
                'remark' => 'string',
                'currency' => 'required|string',
                'scene' => 'string',
                'trade_assurance_type' => 'string',
                'product_list' => 'required|array',
                'product_list.product_id' => 'required|integer',
                'product_list.name' => 'required|string',
                'product_list.sku_id' => 'required|integer',
                'product_list.unit' => 'required|string',
                'product_list.quantity' => 'required|numeric',
                'product_list.unit_price' => 'required|numeric',
            ]))->validate();
        } catch (\Throwable $exception) {
            var_dump($exception->getMessage());
        }

        $orderService = new AlibabaOrder($sessionKey, null);
        $data = $orderService->createOrder($order_params);
        var_dump($data);
    }

    public function actionSaveAddress(){
        $client_id = 14367;
        $store_id = 244059865;
        $adminUserId = \common\library\privilege_v3\PrivilegeService::getInstance($client_id)->getAdminUserId();
        if (!$adminUserId) {
            LogUtil::info("clientId:$client_id adminUser not exist!");
            return;
        }

        User::setLoginUserById($adminUserId);

        $user = User::getLoginUser();
        $clientId = $user->getClientId();
        $alibabaStore = new \common\library\alibaba\store\AlibabaStore($clientId, $store_id);
        if($alibabaStore->enable_flag != 1 || !$alibabaStore->access_token){
            throw new RuntimeException('店铺没有授权');
        }

        $email = "<EMAIL>";
        $address = [
            'address' => "创建地址",
            'address2' => "创建地址",
            'city' => [
                'name' =>  'Guangzhou',
                'code' => '440100'
            ],
            'country' => [
                'name' => 'China',
                'code' => 'CN'
            ],
            'province' => [
                'name' => 'Guangdong Province',
                'code' => '440000',
            ],
            'zip' =>510095
        ];
        $contact = [
            'phone_code' => '+86',
            'mobile_no' => '13539419698',
        ];

        $shipping = new \common\library\alibaba\services\AlibabaShipping($alibabaStore->access_token);
        $data = $shipping->saveAddress($email, $address, $contact, "jack ma");
        var_dump($data);
    }


    public function actionFindAddress()
    {
        $client_id = 14367;
        $store_id = 244059865;
        $adminUserId = \common\library\privilege_v3\PrivilegeService::getInstance($client_id)->getAdminUserId();
        if (!$adminUserId) {
            LogUtil::info("clientId:$client_id adminUser not exist!");
            return;
        }
//        $type = 'countryList';
//        $type = 'countryIso';
        $type = 'provinceId';
        User::setLoginUserById($adminUserId);

//        $param = 'CN';
        $param = '440000';
        $user = User::getLoginUser();
        $clientId = $user->getClientId();
        $alibabaStore = new \common\library\alibaba\store\AlibabaStore($clientId, $store_id);
        if($alibabaStore->enable_flag != 1 || !$alibabaStore->access_token){
            throw new RuntimeException('店铺没有授权');
        }

        $shipping = new \common\library\alibaba\services\AlibabaShipping($alibabaStore->access_token);
        $data = $shipping->getCountryAndRegions($type, $param);
        var_dump($data);
    }

    public function actionTest(){
        $client_id = 14119;
        $adminUserId = \common\library\privilege_v3\PrivilegeService::getInstance($client_id)->getAdminUserId();
        if (!$adminUserId) {
            LogUtil::info("clientId:$client_id adminUser not exist!");
            return;
        }
        User::setLoginUserById($adminUserId);
        $order_id = 4287992868;

        $filter = new AsyncTaskFilter($client_id);
        $filter->refer_id = 4289937861;
        $filter->order('create_time','asc');
        $filter->limit(1);
        $info = $filter->rawData()[0] ?? [];
        $task_result = !empty($info['task_result']) ? json_decode($info['task_result'], true) : ['msg' => ''];

        $content = "";
        foreach ($task_result as $index => $task_result_entry) {
            if (!empty($task_result_entry['ali_order_id'])) {
                $content .= \Yii::t('history', '信保订单创建成功，信保订单号为', ['{index}' => $index, '{prefix}' => $task_result_entry['ali_order_id']]);
            } else {
                $content .= \Yii::t('history', '信保订单创建失败，失败原因为', ['{index}' => $index, '{prefix}' => $task_result_entry['msg'] ?? '']);
            }
            if (isset($task_result[(++$index)])) {
                $content .= "\n";
            }
        }

        $buildType = \common\models\client\OrderHistory::TYPE_SYNC_ALI_ALIBABA_ORDER_TASK_RESULT;
        $orderBuilder = new OrderBuilder();
        $orderBuilder->build($client_id, $adminUserId, $buildType, ['refer_id' => $order_id, 'data' => [
        ]], [
            [
                'id' => 'ali_order_reason',
                'new' => $content
                ,
                'old' => ""
            ]
        ]);

    }

    public function actionRefreshUsers()
    {
        $handler = [444];
        $ids = [4247149887];
        $client_id = 334135;
        $db = \PgActiveRecord::getDbByClientId(334135);
        $idStr = implode(',', $ids);

        $feedbackFilter = new InquiryFeedBackFilter($client_id);
        $feedbackFilter->inquiry_collaboration_id = 4247149887;
        $batchFeedback = $feedbackFilter->find();
        $batchFeedback->getOperator()->batchRefreshUsers($handler);
    }


    public function actionSort(){
        $products = [
            ["product_id" => "38e27152d5dfbb6eb9d32f8d02bd218", "sort" => 1],
            ["product_id" => "286f9884d46b80dce7cbf2b2886135eb", "sort" => 2],
            ["product_id" => "95c993a13f624f05c4c3c8dadd73728d", "sort" => 3],
            ["product_id" => '38e27152d5dfbb6eb9d32f8d02bd218', "sort" => 4],
            ["product_id" => '95c993a13f624f05c4c3c8dadd73728d', "sort" => 5],
            ["product_id" => "95c993a13f624f05c4c3c8dadd73728d", "sort" => 6],
        ];

        $groupedProducts = [];
        foreach ($products as $product) {
            $productId = $product['product_id'];
            if (!isset($groupedProducts[$productId])) {
                $groupedProducts[$productId] = [];
            }
            $groupedProducts[$productId][] = $product;
        }

        // 按照每个组内最小sort值进行排序
        uasort($groupedProducts, function ($a, $b) {
            $minSortA = min(array_column($a, 'sort'));
            $minSortB = min(array_column($b, 'sort'));
            return $minSortA - $minSortB;
        });

        // 在每个组内按照sort值越小越前的顺序排列
        foreach ($groupedProducts as &$group) {
            usort($group, function ($a, $b) {
                return $a['sort'] - $b['sort'];
            });
        }
        $sortedProducts = [];
        unset($group);
        foreach ($groupedProducts as $group) {
            foreach ($group as $product) {
                $sortedProducts[] = $product;
            }
        }

        print_r($sortedProducts);
    }

    public function actionAbcd()
    {
        $client_id = 14119;
        $adminUserId = \common\library\privilege_v3\PrivilegeService::getInstance($client_id)->getAdminUserId();
        if (!$adminUserId) {
            LogUtil::info("clientId:$client_id adminUser not exist!");
            return;
        }
        User::setLoginUserById($adminUserId);

        $user = User::getLoginUser();
        $client_id = $user->getClientId();
        $user_id = $user->getUserId();
        $req = new \protobuf\Product\PBProductSkuInfoReq();
        $sku_id = 3474877945;

        if(empty($sku_id)) {
            throw new \RuntimeException(\Yii::t('invoice', '产品不存在或已删除'));
        }

        $sku = new \common\library\product_v2\sku\ProductSku($client_id, $sku_id);

        if ($sku->isNew()){
            throw new \RuntimeException(\Yii::t('invoice', '产品不存在或已删除'));
        }
        $product_id = $sku->product_id;

        $fieldGroups = [
            \common\library\custom_field\CustomFieldService::PRODUCT_GROUP_BASIC,
            \common\library\custom_field\CustomFieldService::PRODUCT_GROUP_PRICE,
            \common\library\custom_field\CustomFieldService::PRODUCT_GROUP_SIZE,
            \common\library\custom_field\CustomFieldService::PRODUCT_GROUP_PACKAGE,
            \common\library\custom_field\CustomFieldService::PRODUCT_GROUP_CARTON,
            \common\library\custom_field\CustomFieldService::PRODUCT_GROUP_CUSTOM,
            \common\library\custom_field\CustomFieldService::PRODUCT_GROUP_DESCRIBE,
            \common\library\custom_field\CustomFieldService::PRODUCT_GROUP_CHARACTER,
            \common\library\custom_field\CustomFieldService::GROUP_SYSTEM_INFO,
        ];

        $productApi = new \common\library\product_v2\ProductAPI($client_id, $user_id);
        $data =  $productApi->productInfo($product_id, $fieldGroups, null, APIConstant::SCENE_APP_PB);
        $service = new \common\library\product_v2\service\BuildPBProductService($client_id);

        $data['sku_id'] = $sku_id;
        $service->setBaseData($data);
        $data['data'] = $service->buildFormatSkuInfo($sku_id, $data['data']);
        $service->buildSkuInfo($data['data'] ?? []);

    }
    public function actionTestFields($client_id = 334135, $user_id = 249520890)
    {
        User::setLoginUserById($user_id);
        $service = new FieldList($client_id);
        $service->setType(Constants::TYPE_PURCHASE_RETURN_INVOICE);
        $service->setEnableFlag(Constants::ENABLE_FLAG_TRUE);
        $service->setFormatterType(CustomFieldFormatter::TYPE_COLUMN_MAP2);
        $list = $service->find();
        var_dump($list);
    }

    public function actionTestList($client_id = 334135, $user_id = 249520890){
        User::setLoginUserById($user_id);

        $removed = 0;
        $time_type = 1;
        $start_time = '';
        $end_time = '';
        $params = [
            'scene' => APIConstant::SCENE_OPEN_API,
            'serial_id' => $serial_id ?? '',
            'page_no' => $page ?? 1,
            'page_size' => $page_size ?? 30,
            'delete_flag' => $removed ? \Constants::DELETE_FLAG_LINK : \Constants::DELETE_FLAG_FALSE,
            'status' => empty($status) ? null : $status,
        ];

        if ($time_type == 1) {
            $params['update_time_start'] = $start_time;
            $params['update_time_end'] = $end_time;
        } else if ($time_type == 2) {
            $params['create_time_start'] = $start_time;
            $params['create_time_end'] = $end_time;
        }

        $api = new PurchaseInboundApi();
        $res = $api->webList($params);

        var_dump($res);
    }

    public function actionTestInfo(){
        $user_id = 249520890;
        User::setLoginUserById($user_id);

        $user = \User::getLoginUser();

        if (!empty($warehouse_return_invoice_no)) {
            $purchaseReturn = new PurchaseInboundInvoice($user->getClientId());
            $purchaseReturn->loadByNo(\Constants::TYPE_PURCHASE_RETURN_INVOICE, $warehouse_return_invoice_no);
        } else {
            $purchaseReturn = new PurchaseInboundInvoice($user->getClientId(),3514901840);
        }

        $purchaseReturn->canRead();
        $purchaseReturn->getFormatter()->openApiInfoSetting();
        $data = $purchaseReturn->getAttributes();

        var_dump($data);
    }

    public function actionPush($client_id = 334135, $user_id = 249520890, $data = '')
    {
        $data = '{"serial_id":"","currency":"USD","exchange_rate":"731.86","exchange_rate_usd":"100.00000","warehouse_invoice_time":"2025-01-09","supplier_id":5137995577,"remark":"退货备注","warehouse_id":3485846285,"record_list":[{"product_image":[{"file_id":"5158306878","file_name":"A39A8D84-9A1A-4E0D-A605-16CE0E0CF19D.png","file_size":"117777","mime_type":"image/png","file_key":"invoice-file/img/11858712/bf7b7ee7337537903b6170339de166d9c8ddf1451b96b4ade3bcf906dc40a20d.png","file_path":"https://v4client.oss-cn-hangzhou.aliyuncs.com/invoice-file/img/11858712/bf7b7ee7337537903b6170339de166d9c8ddf1451b96b4ade3bcf906dc40a20d.png","file_preview_url":"https://v4client.oss-cn-hangzhou.aliyuncs.com/invoice-file%2Fimg%2F11858712%2Fbf7b7ee7337537903b6170339de166d9c8ddf1451b96b4ade3bcf906dc40a20d.png?response-content-disposition=inline%3B%20filename%3DA39A8D84-9A1A-4E0D-A605-16CE0E0CF19D.png%3B%20filename%2A%3Dutf-8%27%27A39A8D84-9A1A-4E0D-A605-16CE0E0CF19D.png&response-content-type=image%2Fpng&OSSAccessKeyId=LTAI5tABwJoaZbSyGLBru6CC&Signature=sxInLPgKNfBCAl3Vry28pkFKKrQ%3D&Expires=1737253789","create_user":"249520890","create_time":"2025-01-09 10:27:17"}],"return_price":2,"return_count":2,"product_id":5158306884,"sku_id":5158306885,"inbound_record_id":5158307130,"inbound_invoice_id":5158307129}],"product_total_count":2,"attachment":[],"discount_rate":10,"handler":[249520890]}';
        LogUtil::info("push client_id={$client_id} user_id={$user_id} data={$data}");
        User::setLoginUserById($user_id);
        $data = json_decode($data, true);

        $warehouse_return_invoice_id = $data['warehouse_return_invoice_id'] ?? 0;
        $serial_id = $data['serial_id'] ?? '';
        $operatorUserId = $data['user_id'] ?? $user_id;


        // 尝试下是否有单据编号的销售出库单
        if (!empty($serial_id)) {
            $purchaseReturn = new PurchaseReturnInvoice($client_id);
            $purchaseReturn->loadByNo(\Constants::TYPE_SALE_OUTBOUND_INVOICE, $serial_id);
            $warehouse_return_invoice_id = $purchaseReturn->warehouse_return_invoice_id;
        }

        // 新建采购订单初始化处理
        $isNew = false;
        if (empty($warehouse_return_invoice_id)) {
            $isNew = true;
            $purchaseReturn = $purchaseReturn ?? new PurchaseReturnInvoice($client_id);
        } else if (empty($purchaseReturn)) {
            $purchaseReturn = new PurchaseReturnInvoice($client_id, $warehouse_return_invoice_id);
        }

        //validate
        $validator = new \common\modules\resource\library\purchase_return\Validator($data, $isNew);
        $validator->validate();

        $param = new \common\modules\resource\library\purchase_return\Param($client_id, $data);
        $param->setIsNew($isNew);
        $param->setOldPurchaseReturnInvoice($purchaseReturn);
        $data = $param->init();

        try {
            $fieldConfig = OmsField::make($client_id, \Constants::TYPE_SALE_OUTBOUND_INVOICE);
            $fieldConfig->validateData($data, true, ['product_no', 'product_unit', 'handler', 'sku_code']);
        } catch (\RuntimeException $e) {
            throw new \RuntimeException('Input data validate error: ' . $e->getMessage(), 404);
        }

        try {
            $outWarehouse = isset($data['status']) && $data['status'] == \common\library\oms\common\OmsConstant::INBOUND_INVOICE_STATUS_FINISH;
            unset($data['status']);

            $purchaseReturn->setDomainHandler(\User::getUserObject($operatorUserId));
            $purchaseReturn->bindAttrbuties($data);
            if ($isNew) {
                $purchaseReturn->createForTransaction();
            } else {
                $purchaseReturn->checkNoLogic();
                $purchaseReturn->getOperator()->edit($data);
            }

            if ($outWarehouse) {
                $purchaseReturn->canReturn();
                $purchaseReturn->getOperator()->toReturn();
            }
        } catch (\RuntimeException $e) {
            throw new \RuntimeException('Operation Failed. ' . $e->getMessage(), 404);
        }

        var_dump([
            'warehouse_return_invoice_id' => intval($purchaseReturn->warehouse_return_invoice_id),
            'serial_id' => $purchaseReturn->serial_id,
        ]);
    }

    public function actionTestRemove($client_id = 334135, $user_id = 249520890, $inbound_invoice_id = 5158307129, $serial_id = '')
    {
        User::setLoginUserById($user_id);
        if (!empty($serial_id)) {
            $purchaseInbound = new PurchaseInboundInvoice($client_id);
            $purchaseInbound->loadByNo(\Constants::TYPE_PURCHASE_INBOUND_INVOICE, $serial_id);
            $inbound_invoice_id = $purchaseInbound->inbound_invoice_id;
        }

        if (empty($inbound_invoice_id)) {
            $purchaseInbound = $purchaseInbound ?? new PurchaseInboundInvoice($client_id);
        } else if (empty($purchaseInbound)) {
            $purchaseInbound = new PurchaseInboundInvoice($client_id, $inbound_invoice_id);
        }
        try {
            $purchaseInbound->canDel();
            $result = $purchaseInbound->getOperator()->delete();
        } catch (\RuntimeException $e) {
            throw new \RuntimeException($e->getMessage(), 404);
        }
        return ([
            'inbound_invoice_id' => $purchaseInbound->inbound_invoice_id
        ]);
    }

    public function actionupload1()
    {
        \User::setLoginUserById(11864037);

        $clientId = 14367;
        $fileurl = 'https://car2.autoimg.cn/cardfs/product/g32/M0B/6C/B5/autohomecar__ChxkPWZhPOGAAJgPADSGcSjZNYg857.jpg';
        $http = new HTTPClient();
        $urlInfo  = parse_url($fileurl);
        $proxyIp = $this->getProxy($urlInfo['host'] ?? '');
        !empty($proxyIp) && $http->setProxyIp($proxyIp);
        $file = $http->get($fileurl);

        $func = function () use ($http, &$isStatus200) {
            $httpStatusText = $http->getResponseHeader()[0] ?? '';
            if (str_contains($httpStatusText, '200')) {
                $isStatus200 = true;
            }
        };
        $func->call($http);

        if ($file === false || !$isStatus200) {
            var_dump("不对");
            return false;
        }
        $filearr = [];
        $filearr = parse_url($fileurl);
        $filearr = pathinfo($filearr['path']);
        $fileExt = \common\library\oms\common\Helper::getImageExtensionByUrl($fileurl);
        if (!$fileExt) {
            var_dump($fileExt);
            return false;
        }
        $fn = 'condy' . '_' . time() . '_' . rand(1, 999) . '.' . $fileExt;
        $tmpFileName = 'product_import_' . $fn;
        $path = '/tmp/' . $tmpFileName;

        file_put_contents($path, $file);   // 下载远程图片到本地
        $fileKey = \UploadService::getFileKey($path);
        $upload = \UploadService::uploadRealFile($path, $fn, $fileKey);
        $singleProductImage = [
            'id' => $upload->getFileId(),
            'src' => $upload->getFileUrl(),
        ];
        var_dump($singleProductImage);

    }

    protected function getProxy($fileHost)
    {
        $env = \Yii::app()->params['env'] ?? 'dev';
        // 本地开发环境走不了代理
        if ($env == 'dev') {
            return '';
        }
        $proxy = \Yii::app()->params['resource_download']['proxy_ip'] ?? '';
        if (empty($fileHost)) {
            return $proxy;
        }
        $proxyWhiteList = \Yii::app()->params['resource_download']['white_list'] ?? [];
        foreach ($proxyWhiteList as $whiteHost) {
            if (stripos($fileHost, $whiteHost) !== false) {
                $proxy = '';
                break;
            }
        }
        return $proxy;
    }

}