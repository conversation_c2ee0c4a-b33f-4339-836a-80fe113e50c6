<?php


class SupplierCommand extends \CrontabCommand
{

    public function actionAddCustomizeDeliveryDate($clientId)
    {
        $db = \PgActiveRecord::getDbByClientId($clientId);
        $selectSql = "select  * from  tbl_supplier where client_id = $clientId ";
        $supplierList = $db->createCommand($selectSql)->queryAll();
        foreach ($supplierList as $supplierInfo) {
            if (!$supplierInfo['supplier_id']) {
                continue;
            }
            $supplier_id = $supplierInfo['supplier_id'];
            $delivery_date = $supplierInfo['delivery_date'];
            switch ($delivery_date) {
                case 1:
                    $delivery_date = '1-2天';
                    break;
                case 2:
                    $delivery_date = '3-5天';
                    break;
                case 3:
                    $delivery_date = '6-10天';
                    break;
                case 4:
                    $delivery_date = '11-15天';
                    break;
                case 5:
                    $delivery_date = '>15天';//待定
                    break;
                default:
                    $delivery_date = '';
                    break;
            }
            if ($delivery_date == '') {
                continue;
            }
            $updateSkuSql = "update tbl_supplier set custom_delivery_date = '$delivery_date' where client_id={$clientId} and supplier_id = {$supplier_id} ";
            $db->createCommand($updateSkuSql)->execute();
        }
    }

    public function actionUpdateSupplierProductGradientPrice($clientId = 333319)
    {
        $db = \PgActiveRecord::getDbByClientId($clientId);
        $selectSql = "select relation_id,supplier_price,gradient_price from  tbl_supplier_product where client_id = $clientId ";
        $supplierProductList = $db->createCommand($selectSql)->queryAll();
        foreach ($supplierProductList as $supplierProductInfo) {
            $relationId = $supplierProductInfo['relation_id'];
            $supplierPrice = $supplierProductInfo['supplier_price'];
            //一级阶梯定价，且起订量默认 ≥1+最新报价
            if (floatval($supplierPrice)) {
                $gradientPrice = json_encode([["price" => floatval($supplierPrice), "quantity" => 1]]);
            } else {
                $gradientPrice = json_encode([["price" => '', "quantity" => '']]);
            }
            $updateSql = "update tbl_supplier_product set gradient_price = '{$gradientPrice}' where client_id={$clientId} and relation_id = {$relationId} ";
            $db->createCommand($updateSql)->execute();
        }
    }

}