<?php


use common\library\account\Client;
use common\library\approval_flow\Constants;
use common\library\cash_collection\CashCollection;
use common\library\exchange_rate\ExchangeRateService;
use common\library\oms\cash_collection_invoice\CashCollectionInvoiceApi;
use common\library\oms\common\TransferTriggerFactory;
use common\library\oms\product_transfer\computation\InboundComputation;
use common\library\oms\product_transfer\computation\OutboundComputation;
use common\library\oms\product_transfer\computation\PurchaseComputation;
use common\library\oms\product_transfer\inbound\record\BatchInboundProductTransferRecord;
use common\library\oms\product_transfer\inbound\relation\BatchInboundProductTransferRelation;
use common\library\oms\product_transfer\outbound\record\BatchOutboundProductTransferRecord;
use common\library\oms\product_transfer\outbound\relation\BatchOutboundProductTransferRelation;
use common\library\oms\product_transfer\purchase\record\BatchPurchaseProductTransferRecord;
use common\library\oms\product_transfer\purchase\relation\BatchPurchaseProductTransferRelation;
use common\library\purchase\purchase_order\PurchaseOrder;
use common\library\server\es_search\SearchQueueService;
use common\library\util\Arr;
use common\modules\prometheus\library\auto_test\sls_log_active_api\statistics\PHPExternalSlsLogActiveTask;
use common\modules\prometheus\library\auto_test\ui_test\cases\UITestCases;
use common\modules\prometheus\library\code_gate\code_report\ReportBuilderFactory;
use common\modules\prometheus\library\code_gate\gate_rule\GateRule;
use common\modules\prometheus\library\gitlab\GitlabDevopsClient;

class ZhengbogiangCommand extends \CrontabCommand
{


    public function actionAddConfigDescribe()
    {
        //{"host": "https://prometheus.xiaoman.cn", "script_ks_cluster": "host"}
        //{"host": "http://prometheus.okki.ai", "script_ks_cluster": "k8s-aws-sgp"}
        $prometheusService = [
            1 => [
                'host' => 'https://devops-iteration-5-7-1111291.story.beta.xiaoman.cn',
                'script_ks_cluster' => 'host'
            ],
            2 => [
                'host' => 'https://prometheus.okki.ai',
                'script_ks_cluster' => 'k8s-aws-sgp'
            ]
        ];
        foreach ($prometheusService as $id => $item) {
            $config = json_encode($item);
            $sql = "update tbl_config_describe set prometheus_service='{$config}' where id={$id}";
            \Yii::app()->prometheus_db->createCommand($sql)->execute();
        }
    }

    public function actionAddGateRule()
    {

//        $insertData = [
//            'project_id' => 186,
//            'type' => '201',
//            'module' => '',
//            'name' => '测试规则',
//            'description' => '测试规则',
//            'criteria' => '(1)',
//            'criteria_type' => 1,
//            'filters' => [
//                ['filter_no' => 1, 'field_type' => 5, 'field' => 'pass_rate', 'value' => 90, 'operator' => '>'],
//            ],
//            'handlers' => [
//                ['type' => "allow_release", 'config' => []]
//            ]
//        ];
//        $insertData = [
//            'project_id' => 186,
//            'type' => '101',
//            'module' => '',
//            'name' => '代码扫描规则',
//            'description' => '代码扫描规则',
//            'criteria' => '(1)',
//            'criteria_type' => 1,
//            'filters' => [
//                ['filter_no' => 1, 'field_type' => 5, 'field' => 'danger_count', 'value' => 1, 'operator' => '>'],
//            ],
//            'handlers' => [
//                ['type' => "refuse_release", 'config' => []]
//            ]
//        ];
        $insertData = [
            'project_id' => 186,
            'type' => '301',
            'module' => '',
            'name' => 'php-代码覆盖率规则',
            'description' => 'php-代码覆盖率规则',
            'criteria' => '(1)',
            'criteria_type' => 1,
            'filters' => [
                ['filter_no' => 1, 'field_type' => 5, 'field' => 'incr_statement_coverage_rate', 'value' => 10, 'operator' => '>'],
            ],
            'handlers' => [
                ['type' => "refuse_release", 'config' => []]
            ]
        ];

        $ruleModel = new GateRule();
        $ruleModel->setAttributes($insertData);
        $ruleModel->save();

    }

    public function actionTestRunGate()
    {
        try {

            $projectId = 186;
            $type=301;
            $module = '';
            $branches = ['feature/branch-name'];
            $obj = new \common\modules\prometheus\library\code_gate\gate_rule\execute\GateRunner($type, $projectId, $module, $branches);
            $obj->run();
        } catch (Throwable $throwable) {
            echo "error:".$throwable->getMessage().PHP_EOL;
            var_dump($throwable->getLine(),$throwable->getFile(),$throwable->getTraceAsString());
        }

    }
    public function actionTestApifoxReport()
    {
        //测试数据插入
        $projectId=186;
        $branch = 'master_1';
        $module = '';
        $reportGateData = [
            'commit_id' => '81d90ca3',
            'url' => 'http://www.baidu.com2',
            'total' => 101,
            'passed' => 91,
            'failed' => 10,
            'unknown' => 0,
        ];
        $builder = ReportBuilderFactory::createBuilder(201,$projectId,$branch,$module);
        $builder->setSourceData($reportGateData);
        $builder->build();
//        GateReportConsumer::pushQueue(GateConstant::AUTO_TEST_SUB_TYPE_APIFOX, $projectId, $branch, $module, $reportGateData);
        echo "success".PHP_EOL;
    }

    public function actionOOOO()
    {

        //门禁规则设置
        //auto_test:  pass_rate>90 通过率
        //code_scan(区分前后端):  bug_count<1 \ danger_count<1
        //code_coverage:  bug_count<1 \ danger_count<1


        $apifoxDb = \Yii::app()->apifox_db;
        $sql = "create unique index http_apis_project_id_path_method on http_apis (project_id, path(256), method);";
//        $sql = "drop index http_apis_project_id_path_method on http_apis;";
//        $sql = "update  http_apis set deleted_at=now() where  project_id=345199 and isnull(deleted_at) and id in (select entity_id from project_branch_resource_references where project_branch_id in (select id from project_branches where project_id=345199 and is_main=0) and entity_type='HTTP_API' and isnull(deleted_at))";

//        $sql = "update http_apis set deleted_at=now() where isnull(deleted_at) and id=3470968";
//        $sql = "delete  from  http_apis  where !isnull(deleted_at)";
//        $sql = "delete  from  http_apis  where id in(3481270)";

        $count = $apifoxDb->createCommand($sql)->execute();
        echo "success:".$count.PHP_EOL;
    }

    public function actionFixApifoxInheritPreProcessors()
    {
//        $sql = "select * from http_api_cases where test_suite_id=349292 and isnull(deleted_at)";
//        $sql = "select * from http_api_cases where project_id=345091 and isnull(deleted_at) and !isnull(inherit_pre_processors_snapshot) and inherit_pre_processors_snapshot!='{}' and inherit_pre_processors_snapshot!='[]' and inherit_pre_processors_snapshot!='null'";
//        $sql = "select * from http_api_cases where id=3476540 project_id=345091 and  isnull(deleted_at) and !isnull(inherit_pre_processors_snapshot) and inherit_pre_processors_snapshot!='{}' and inherit_pre_processors_snapshot!='[]' and inherit_pre_processors_snapshot!='null'";
        $sql = "select * from http_api_cases where  project_id=345091 and   isnull(deleted_at) and !isnull(inherit_pre_processors_snapshot) and inherit_pre_processors_snapshot!='{}' and inherit_pre_processors_snapshot!='[]' and inherit_pre_processors_snapshot!='null'";
        $apifoxDb = \Yii::app()->apifox_db;
        $data = $apifoxDb->createCommand($sql)->queryAll();

//        $allScriptSql = "";

        foreach ($data as $item) {
            $inheritPreProcessors = json_decode($item['inherit_pre_processors_snapshot'],true);
            if (!is_array($inheritPreProcessors)) {
                echo "!!!!!!!!!!!inherit_pre_processors_snapshot 解析后 不是数组 id:{$item['id']} name:{$item['name']}".PHP_EOL;
                continue;
            }
//            if (count($inheritPreProcessors) >1) {
//                echo "!!!!!!!脚本数量超过1 跳过执行 id:{$item['id']} name:{$item['name']} count:".count($inheritPreProcessors).PHP_EOL;
//                continue;
//            }
//
//            continue;

            $showFlag = $updateFlag= 0;
            foreach ($inheritPreProcessors as &$processItem) {
                if ($processItem['type'] == 'commonScript' && $processItem['executionTiming']!='presend' && $processItem['targetType'] == "projectSetting") {
                    $processItem['executionTiming'] = 'presend';
                    $updateFlag = 1;
                }
//                if($processItem['type'] == 'commonScript' && $processItem['executionTiming']!='presend' && $processItem['targetType'] != "projectSetting") {
////                    $showFlag = 1 ;
////                    echo "!!!!!!!!!!!type:{$processItem['type']} executionTiming:{$processItem['executionTiming']} targetType:{$processItem['targetType']} id:{$item['id']} name:{$item['name']}".PHP_EOL;
////                    die;
//                }
            }
            unset($processItem);
            if ($updateFlag) {


                self::info("***********修复记录 id:{$item['id']} name:{$item['name']} 旧数据：【【{$item['inherit_pre_processors_snapshot']}】】");
                $newInheritPreProcessors = addslashes(json_encode($inheritPreProcessors));
                $updateSql = "update http_api_cases set inherit_pre_processors_snapshot='{$newInheritPreProcessors}' where id={$item['id']}";
                echo "%%%%%%%%%%%%%%%修复的数据 id:{$item['id']} name:{$item['name']} ,修复数据sql【【{$updateSql}】】".PHP_EOL;
                $apifoxDb->createCommand($updateSql)->execute();
            }

        }
    }
    
    public function actionUiTest()
    {
        $listObj = new \common\modules\prometheus\library\auto_test\sls_log_active_api\SlsLogActiveFeList();
        $listObj->setMatchFeatureFlag(0);
        $listObj->setLimit(10);
        $listObj->setOffset(0);

        $listObj->setFields('path,params,last_request_time');
        $list = $listObj->find();
        $count = $listObj->count();
        var_dump($count);
        die;
//        $qwe = new FeSlsLogActiveTask();
//        $ee = $qwe->buildFeatureList();
//        var_dump($ee);die;


        $data = '[{"case_data":{"name":"页面巡检 建档建议-OKKI 客户管理","content":["check_page"],"case_type":2,"business":"CRM","module":"商机","owner":"kelly","Status":1},"feature_data":[{"name":"建档建议-OKKI 客户管理","content":"/crm/customer/personal","param":"?company_id=*********","business":"CRM","module":"客户","status":1,"type":1,"page":"/crm/customer/personal?company_id=*********","last_exist_time":"2024-08-15T02:53:33.877575243Z"}]}]';
        $data = json_decode($data, true);

        foreach ($data as $item) {
            $caseModel = new UITestCases();
            $caseModel->create($item);
        }
    }

    // ./yiic-test zhengbogiang dupData >> /tmp/apifoxDupData
    public function actionDupData()
    {
        $sql ="
        select b.name,pb.name as branchName,a.* from (
select project_branch_id,ha.project_id,method,path,ha.deleted_at,count(1) as num from http_apis as ha
left join project_branch_resource_references as pbrr
on ha.id = pbrr.entity_id
where isnull(ha.deleted_at) 
group by project_branch_id,ha.project_id,method,path,ha.deleted_at order by num desc
) as a 
left join projects as b
on a.project_id = b.id
left join project_branches as pb
on a.project_branch_id = pb.id
having a.num >1
";

        $apiFoxDb = \Yii::app()->apifox_db;
        $data = $apiFoxDb->createCommand($sql)->queryAll();

//        $testCaseSql = "select id,name,steps from api_test_cases where project_id in (select id from projects where team_id=72) and isnull(deleted_at)";
//        $testCaseData = $apiFoxDb->createCommand($testCaseSql)->queryAll();

        //根据重复的数据，找出path、project_id 数据的接口 ；第二步在进行测试场景的查询，找出这些接口的测试场景
        foreach ($data as $item) {
            $project_id = $item['project_id'];
            $path = $item['path'];
            $sql = "select * from http_apis where project_id={$project_id} and path='{$path}' and isnull(deleted_at)";
            $apiData = $apiFoxDb->createCommand($sql)->queryAll();
//            $apiIds = array_column($apiData, 'id');
            foreach ($apiData as $api) {
                $apiId = $api['id'];
                $testCaseSql = "select * from api_test_cases where project_id={$project_id} and steps like '%\"bindId\":{$apiId}%'";
                $testCaseData = $apiFoxDb->createCommand($testCaseSql)->queryAll();

                $qqcount = count($testCaseData);
                echo "project_name:{$item['name']} branch_name:{$item['branchName']} path:{$path} api_id:{$apiId} 接口名：【{$api['name']}】 ***个数：$qqcount".PHP_EOL;
                foreach ($testCaseData as $testCase) {
                    echo "|-- testCase_id:{$testCase['id']}  testCase_name: ***** {$testCase['name']} *****".PHP_EOL;
                }

                $apiCaseSql = "select * from http_api_cases where project_id={$project_id} and api_detail_id = {$apiId} and test_suite_id=0 and isnull(deleted_at)";
                $apiCaseData = $apiFoxDb->createCommand($apiCaseSql)->queryAll();
                if (!empty($apiCaseData)) {
                    foreach ($apiCaseData as $apiCase) {
                        $apiCaseId = $apiCase['id'];
                        $testCaseSql = "select * from api_test_cases where project_id={$project_id} and steps like '%\"bindId\":{$apiCaseId}%'";
                        $testCaseData = $apiFoxDb->createCommand($testCaseSql)->queryAll();
                        foreach ($testCaseData as $testCase) {
                            echo "|---- 引用用例的 testCase_id:{$testCase['id']}  testCase_name: ***** {$testCase['name']} *****".PHP_EOL;
                        }
                    }
                }
            }
        }
    }

    public function actionDeleteProjcetApi()
    {
        //删除已删除的项目下的api
        $sql = "select * from http_apis where project_id in (select id from projects where !isnull(deleted_at))";
        $apifoxDb = \Yii::app()->apifox_db;
        $data = $apifoxDb->createCommand($sql)->queryAll();
        $ids = array_column($data, 'id');
        $idsStr = implode(',', $ids);
        $deleteSql = "update http_apis set deleted_at=now() where id in ({$idsStr})";
        $apifoxDb->createCommand($deleteSql)->execute();
    }

    public function actionDeleteBranchApi()
    {
        //非主分支的 接口
        $sql = "select entity_id from project_branch_resource_references where project_branch_id in  (select id from project_branches where is_main!=1)  and entity_type='HTTP_API'";

        $apifoxDb = \Yii::app()->apifox_db;
        $notMainBranchApiIds = array_column($apifoxDb->createCommand($sql)->queryAll(),'entity_id');

        $updateSql = "update http_apis set deleted_at=now() where id in (".implode(',',$notMainBranchApiIds).") and isnull(deleted_at)";
//        echo $updateSql.PHP_EOL;
        $apifoxDb->createCommand($updateSql)->execute();
    }

    //修改apifox的api 的 delete_at ，将重复的delete_at，修改掉，时间+1s
    public function actionFixDupDeleteAt()
    {
        $apifoxDb = \Yii::app()->apifox_db;
        $dupSql = "select project_id, path, method, deleted_at ,count(1) as num ,group_concat(id) as api_ids from http_apis where !isnull(deleted_at) group by project_id, path, method, deleted_at having num>1 order by num desc";

        $dupData = $apifoxDb->createCommand($dupSql)->queryAll();

        foreach ($dupData as $item) {
            $apiIds = explode(',',$item['api_ids']);
            $i=1;
            foreach ($apiIds as $apiId) {
                if ($i == 1) {
                    $i++;
                    continue;
                }
                $updateSql = "update http_apis set deleted_at=DATE_ADD(deleted_at,INTERVAL {$i} SECOND) where id={$apiId}";
                echo $updateSql.PHP_EOL;
                $apifoxDb->createCommand($updateSql)->execute();
                $i++;
            }
//            die;
        }
    }




    public function actionTestWrite()
    {
        $newPreProcessors = '';
        $apifoxDb = \Yii::app()->apifox_db;
        $id = 3489163;

        $updateSql = "update http_api_cases set pre_processors=:pre_processors where id={$id}";
        echo $updateSql.PHP_EOL;
        $apifoxDb->createCommand($updateSql)->execute([':pre_processors' => $newPreProcessors]);
        echo "success id:{$id}".PHP_EOL;
        echo PHP_EOL;die;
    }


    public function actionFix2()
    {


        $sql = "select id,name,test_suite_id,project_id, pre_processors from http_api_cases where  pre_processors like '\"[{%'";
//        $sql = "select id, pre_processors from http_api_cases where  id=3492311";
//        $sql = "select id, pre_processors from http_api_cases where  id=3500347";
        $apifoxDb = \Yii::app()->apifox_db;

        $data = $apifoxDb->createCommand($sql)->queryAll();
        foreach ($data as $item) {
//            if ($item['id'] == 3489163) {
//                continue;
//            }
//            $count = count(explode('},{',$item['pre_processors']));
//            if ($count >2) {
//                echo "*****************id:{$item['id']} name:[{$item['name']}] count:{$count}".PHP_EOL;
//                echo $item['pre_processors'].PHP_EOL;
//                continue;
//                die;
//            }
            $pre_processors = substr($item['pre_processors'], 1, strlen($item['pre_processors'])-2);
            var_dump($pre_processors);
            $start = strpos($pre_processors, 'data\":') + 9;
            $pre = substr($pre_processors, 0, $start);
            $pre_processors = substr($pre_processors, $start, strlen($pre_processors));
            $end = strpos($pre_processors, '\",\"defaultEnable');
            $center = substr($pre_processors, 0, $end);
            $post = substr($pre_processors, $end, strlen($pre_processors));

            var_dump($pre);
            var_dump($center);
            var_dump($post);

            $pre = str_replace('\"', '"', $pre);
            $post = str_replace('\"', '"', $post);

//            $newPreProcessors = $pre . $center . $post;


//            echo "最新数据：".$newPreProcessors.PHP_EOL;
            $jsonString = trim($item['pre_processors'],'"');

            $jsonString = str_replace('\{\"', '{"', $jsonString);
            $jsonString = str_replace('\":\"', '":"', $jsonString);
            $jsonString = str_replace('\",\"', '","', $jsonString);
            $jsonString = str_replace('\":', '":', $jsonString);
            $jsonString = str_replace(',\"', ',"', $jsonString);
            $jsonString = str_replace('\"}', '"}', $jsonString);
            $jsonString = str_replace('{\"', '{"', $jsonString);
            $newPreProcessors = $jsonString;
            print_r( $jsonString);

            //判断是否json格式的数据
            $updateFlag = 1;

            if (json_decode($newPreProcessors) == null) {
                $updateFlag = 0;

                echo "pre_processors:".$newPreProcessors.PHP_EOL;
                echo "!!!!!!!!!!!!!!!!!!!!!!!!!json格式错误".PHP_EOL;
                echo "id:{$item['id']} name:[{$item['name']}]".PHP_EOL;
            }


            $updateSql = "update http_api_cases set pre_processors=:pre_processors where id={$item['id']}";
            echo $updateSql.PHP_EOL;
            $updateFlag  && $apifoxDb->createCommand($updateSql)->execute([':pre_processors' => $newPreProcessors]);
            echo "success id:{$item['id']} name:[{$item['name']}]".PHP_EOL;

            $caseSql = "select * from api_test_cases where id={$item['test_suite_id']}";
            $caseData = $apifoxDb->createCommand($caseSql)->queryRow();
            echo "caseName:".$caseData['name'].PHP_EOL;
            $projectSql ="select name from projects where id={$item['project_id']}";
            $projectData = $apifoxDb->createCommand($projectSql)->queryRow();
            echo "projectName:".$projectData['name'].PHP_EOL;

            echo PHP_EOL;die;
        }

    }

    // ./yiic-test zhengbogiang FixApifoxPreProcessors  修复apifox的pre_processors
    public function actionFixApifoxPreProcessors()
    {
        $apifoxDb = \Yii::app()->apifox_db;

        $projectSql = "select id,name from projects where team_id=72";
        $projectMap = array_column($apifoxDb->createCommand($projectSql)->queryAll(true),'name', 'id');

        $projectIds = implode(',',array_keys($projectMap));
        $sql = "select id,name,project_id,pre_processors from http_api_cases where  project_id in ($projectIds) and pre_processors!='[]'";
        $data = $apifoxDb->createCommand($sql)->queryAll();

//        echo count($data).PHP_EOL;die;
        $i=0;
        foreach ($data as $item) {
            // 修复pre_processors ,提取出renderType=dynamicValueDivider 的数据，放在最前面 ;
            $preProcessors = json_decode($item['pre_processors'], true);
            $dynamicValueDivider = [];
            $other = [];
            $hasDynamicValueDivider = false;
            if (empty($preProcessors)) {
                continue;
            }
            foreach ($preProcessors as $preProcessor) {
                if (isset($preProcessor['renderType']) && $preProcessor['renderType'] == 'dynamicValueDivider') {
                    $dynamicValueDivider[] = $preProcessor;
                    $hasDynamicValueDivider = true;
                } else {
                    $other[] = $preProcessor;
                }
            }

            $newPreProcessors = array_merge($dynamicValueDivider, $other);
            //如果只有一个dynamicValueDivider 则不处理
            if ($hasDynamicValueDivider && count($newPreProcessors) == 1) {
                continue;
            }
            //没有dynamicValueDivider 也不出里
            if (!$hasDynamicValueDivider) {
                continue;
            }

            $projectName = $projectMap[$item['project_id']] ?? '';
            self::info("api_id:【{$item['id']}】 ;api_name:【{$item['name']}】;project_name【{$projectName}】 ;pre_processors:【".json_encode($preProcessors)."】");
//            echo "api_id:【{$item['id']}】 ;api_name:【{$item['name']}】;project_name【{$projectName}】 ;pre_processors:【" . json_encode($preProcessors) . "】";
            $newPreProcessors = json_encode($newPreProcessors);
            $safe_string = str_replace("'", "\'", $newPreProcessors);
            $sql = "update http_api_cases set pre_processors='{$safe_string}' where id={$item['id']}";
//            echo $sql.PHP_EOL;die;
            $apifoxDb->createCommand($sql)->execute();
//            echo "success id:{$item['id']}".PHP_EOL;
            $i++;
        }
        echo "success count:{$i}".PHP_EOL;
    }

    
    //获取gitlab的job信息
    public function actionGitJob()
    {
        $gitlabClient = new GitlabDevopsClient();
        $i = 1;
        $returnData = [];
//        $qwe = $gitlabClient->getProjectJobs(186, $i, 1);
//        echo \GuzzleHttp\json_encode($qwe);die;

        $stage = 'auto-test';
        $startTime = strtotime(date('Y-m-d 00:00:00', strtotime('-1 day')));
        $endTime = strtotime(date('Y-m-d 23:59:59', strtotime('-1 day')));

        echo "startTime:".$startTime.PHP_EOL;
        echo "endTime:".$endTime.PHP_EOL;

        while ($branches = $gitlabClient->getProjectJobs(186, $i, 100)) {

            foreach ($branches as $branch) {
                $createdAt = strtotime($branch['created_at']);
                if ($createdAt >= $startTime && $createdAt <= $endTime) {
                    $returnData[] = $branch;
                }
                if ($createdAt < $startTime) {
                    echo "break 1";
                    break 2;
                }
            }
            $i++;
            echo "allcount : ".count($returnData)." i:{$i}".PHP_EOL;

        }

        $total = count($returnData);
        echo "total:{$total}".PHP_EOL;
        $autoTestDev = 0;
        $autoTestBeta = 0;
        $autoTestJobs = [
            'beta' => [],
            'dev' => [],
        ];
        foreach ($returnData as $item) {
            if ($item['stage'] == $stage) {
                //如果ref包含hotfix\master\release，则算入beta环境
                if (preg_match('/hotfix|master|release/i', $item['ref'])) {
                    $autoTestBeta++;
                    $autoTestJobs['beta'][] =$item['web_url'] ;
                } else {
                    $autoTestDev++;
                    $autoTestJobs['dev'][] =$item['web_url'] ;
                }
            }
        }

        echo "autoTestDev:{$autoTestDev}".PHP_EOL;
        echo "autoTestBeta:{$autoTestBeta}".PHP_EOL;
        echo "autoTestJobs:".json_encode($autoTestJobs).PHP_EOL;
    }

    // ./yiic-test zhengbogiang ParseContent 解析文本获取对应的gitlab-runner信息
    public function actionParseContent()
    {
        $cookie = "preferred_language=en; sidebar_collapsed=false; visitor_id=97671856-5a82-468c-a616-c67e89835ad5; _gitlab_session=c663a7e5f51f92a6b5c20d2db6f7b773; event_filter=all; diff_view=parallel; collapsed_gutter=false";
        $urls = ["https://gitlab.xiaoman.cc/php/PHP-CRM/-/jobs/2397461/raw",
            "https://gitlab.xiaoman.cc/php/PHP-CRM/-/jobs/2397460/raw",
            "https://gitlab.xiaoman.cc/php/PHP-CRM/-/jobs/2397352/raw",];
        $gitlabRunner = [];
        foreach ($urls as $url) {

            //请求链接，获取内容 读取出来的是html文件，需要解析出来
            //Waiting for pod default/runner-eqcqanjd-project-186-concurrent-6ggf75 to be running, status is Pending
            //提取runner-eqcqanjd-project-186-concurrent-6ggf75

            $htmlContent = $this->fetchUrlContent($url,$cookie);
//            echo $htmlContent;die;
            // 使用正则表达式提取目标字符串
            $pattern = '/runner-eqcqanjd-project-186-concurrent-\w+/';
            preg_match($pattern, $htmlContent, $matches);
            // 检查是否找到了匹配的字符串
            if (!empty($matches)) {
                $extractedString = $matches[0];
                $gitlabRunner[] = $extractedString;
                echo "提取的字符串: " . $extractedString.PHP_EOL;
            } else {
                echo "未找到匹配的字符串";
            }
        }
        echo \GuzzleHttp\json_encode($gitlabRunner);die;
    }
    // 使用 cURL 请求 URL
    function fetchUrlContent($url,$cookie) {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            'Cookie: ' . $cookie,
        ));
        $output = curl_exec($ch);
        curl_close($ch);
        return $output;
    }

    //修复活跃接口表的有效状态  tbl_sls_log_active_api的enable_flag 已完成
    public function actionFixEnableFlag()
    {
        $db = Yii::app()->prometheus_db;
        $sql = "update tbl_sls_log_active_api set enable_flag=0 where 1=1";
        $db->createCommand($sql)->execute();

        $dataSql = "select * from tbl_sls_log_active_api";
        $dataList = $db->createCommand($dataSql)->queryAll();
        $qwe = new PHPExternalSlsLogActiveTask();
        foreach ($dataList as &$data) {
            $data['url']=$data['path'];
            $data['http_host']=$data['host'];
            $data['enable_flag']=$qwe->checkUrlEnable($data);
            echo "{$data['application']} ---- {$data['path']} ---- enable_flag:{$data['enable_flag']}".PHP_EOL;
            $updateSql  = "update tbl_sls_log_active_api set enable_flag={$data['enable_flag']} where path='{$data['path']}' and application='{$data['application']}' ";
            $db->createCommand($updateSql)->execute();
        }
        unset($data);
        echo "success";
    }

    //扫描php代码接口  ./yiic-test zhengbogiang FindPhpPathAndParam
    public function actionFindPhpPathAndParam()
    {

        $app2pathMap = [
//            ['route_prefix' => '/controllers', 'path_prefix' => '/api/'],
//            ['route_prefix' => '/modules/ames/controllers', 'path_prefix' => '/ames/'],
//            ['route_prefix' => '/modules/app/controllers', 'path_prefix' => '/app/'],
//            ['route_prefix' => '/modules/assistant/controllers', 'path_prefix' => '/assistant/'],
//            ['route_prefix' => '/modules/external/controllers', 'path_prefix' => '/external/'],
//            ['route_prefix' => '/modules/lighthouse/controllers', 'path_prefix' => '/lighthouse/'],
//            ['route_prefix' => '/modules/marketing/controllers', 'path_prefix' => '/marketing/'],
            ['route_prefix' => '/modules/prometheus/controllers', 'path_prefix' => '/prometheus/'],
//            ['route_prefix' => '/modules/resource/controllers', 'path_prefix' => '/resource/'],
//            ['route_prefix' => '/modules/shops/controllers', 'path_prefix' => '/shops/'],--
//            ['route'=>'/modules/storms_fury/controllers' ,'path_prefix'=> '/stormsFury/'],--
//            ['route_prefix' => '/modules/tapd_plugin/controllers', 'path_prefix' => '/tapdPlugin/'],
//            ['route_prefix' => '/modules/ticket_assistant/controllers', 'path_prefix' => '/ticketAssistant/'],
        ];

        //查找 protected/controllers 的文件
        //当前执行目录 currPath
        $count = count($app2pathMap);
        // 创建 Swoole Table
        $table = new Swoole\Table(10240*$count);
        $table->column('path', Swoole\Table::TYPE_STRING, 256);
        $table->column('route', Swoole\Table::TYPE_STRING, 256);
        $table->column('controller', Swoole\Table::TYPE_STRING, 128);
        $table->column('params_count', Swoole\Table::TYPE_INT, 8);
        $table->column('params', Swoole\Table::TYPE_STRING, 1024);
        $table->create();

        $processes = [];
        for ($i = 0; $i < count($app2pathMap); $i++) {
            $handleData = $app2pathMap[$i]??[];
            $processes[$i] = new Swoole\Process(function ($worker) use ($table,$i,$handleData) {
                // 在这里执行你的任务 例如，处理数据库查询，文件操作等
                try {
                    $this->module2path($table,$i, $handleData);
                    echo "Worker #$i finished\n";
                } catch (\Swoole\Exception $exception) {
                    var_dump($exception->getMessage(),$exception->getTraceAsString());
                }
            });
            $processes[$i]->start();
        }

        // 等待所有子进程退出并读取管道数据
        foreach ($processes as $process) {
            Swoole\Process::wait();
        }
        //获取table的数据，然后进行批量插入到 tbl_code_api 表中
        $insert = [];
        $date = xm_function_date();
        foreach ($table as $key => $value) {
            $path = $value['path'];
            $params_count = $value['params_count'];
            $controller = $value['controller'];
            $route = $value['route'];
            $params = $value['params'];
            $insert[] = ['date' => $date, 'type' => 'php', 'path' => $path,  'route' => $route,'controller' => $controller, 'params_count' => $params_count, 'params' => $params];
        }
//        echo count($insert);die;
        $codeApi = new \common\modules\prometheus\library\auto_test\code_api\CodeApi();
        $codeApi->create($insert);
        echo "success";
    }

    public function module2path($table,$i, $handleData)
    {
        echo "module2path workerId:{$i} start\n";
        $currPath = getcwd();
        $routePrefix = $handleData['route_prefix'] ?? '';
        $pathPrefix = $handleData['path_prefix'] ?? '';
        $directory = $currPath.$routePrefix;
        $files = scandir($directory);
//var_dump($files);die;
//var_dump($currPath);die;
        $autoloadPath = $currPath.'/vendor/autoload.php';
        require $autoloadPath;
        //查看是否有components 文件，如果有，将它引入
        $componentsPath = $directory.'/../components';
        $componentsFiles = scandir($componentsPath);
//        var_dump($componentsFiles);die;
        foreach ($componentsFiles as $file) {
            if (is_file($componentsPath . '/' . $file) && pathinfo($file, PATHINFO_EXTENSION) === 'php') {
                include $componentsPath . '/' . $file;
            }
        }

        foreach ($files as $file) {
            if (is_file($directory . '/' . $file) && pathinfo($file, PATHINFO_EXTENSION) === 'php') {
                try {
                    // 寻找类名为 xxxxController 的文件
                    $filePath = $directory . '/' . $file;
                    $fileRoute = $routePrefix.'/'.$file;
                    $className = pathinfo($file, PATHINFO_FILENAME);
//                    echo $filePath.PHP_EOL;
//                    continue;
                    include $filePath;

                    // 使用反射获取类中的方法
                    $reflectionClass = new ReflectionClass($className);
//                    var_dump($reflectionClass);die;
                    $methods = $reflectionClass->getMethods(ReflectionMethod::IS_PUBLIC);
                    foreach ($methods as $method) {
                        if (strpos($method->name, 'action') === 0) {
                            if ($method->name == 'actions') {
                                continue;
                            }
                            // 拼接成 /api/OrderRead/List
                            //计算字符串长度计算
                            $controller = substr( $className,0,-strlen('Controller'));
                            $apiPath = $pathPrefix . ucfirst($controller) . '/' . substr($method->name, 6);
                            $md5 = md5($apiPath);
                            // 获取方法参数个数 | 参数值
                            $paramCount = $method->getNumberOfParameters();
                            $params = $method->getParameters();
                            $paramsArr = [];
                            foreach ($params as $param) {
                                $paramsArr[$param->name] = $param->isDefaultValueAvailable() ? $param->getDefaultValue() : null;
                            }
                            echo "API Path: " . $apiPath .'___';echo "Number of parameters: " . $paramCount . PHP_EOL;
                            $table->set($md5, ['path' => $apiPath, 'route' => $fileRoute, 'controller' => $className, 'params_count' => $paramCount,'params' => json_encode($paramsArr)]);
                        }
                    }

                } catch (Throwable $throwable) {
                    var_dump($throwable->getMessage(),$throwable->getTraceAsString());
                }
            }
        }
    }

    public function actionTestApi()
    {
        $apifoxDB = \Yii::app()->apifox_db;
        $pDB = \Yii::app()->prometheus_db;

        $codeApiSql = "select * from tbl_code_api where path like '/api/%' ";
//        $codeApiSql = "select * from tbl_code_api ";
        $codeApiList = $pDB->createCommand($codeApiSql)->queryAll();

        $apifoxApiSql = "select path,project_id from http_apis where project_id in (select id from projects where team_id=72) and status='released' and deleted_at is null";
        $apifoxApiList = $apifoxDB->createCommand($apifoxApiSql)->queryAll();


        $path2projectMap = [];
        foreach ($codeApiList as $codeItem) {
            $lowerPath = strtolower($codeItem['path']);
            $path2projectMap[$lowerPath] = [];
            foreach ($apifoxApiList as $apifoxItem) {
                if ($lowerPath == strtolower($apifoxItem['path'])) {
                    !in_array($apifoxItem['project_id'],$path2projectMap[$lowerPath]) && $path2projectMap[$lowerPath][] = $apifoxItem['project_id'];
                }
            }
        }

        //获取/ 前2的数据，进行重新填充
        // /api/customerread/addresslist  => /api/customerread
        $xxMap = [];
        foreach ($path2projectMap as $path => $projectIds) {
            $pathArr = explode('/', $path);
            $pathArr = array_slice(array_filter(array_values($pathArr)),0, -1);
            $newPath = '/'.implode('/', $pathArr);
            $xxMap[$newPath] = array_merge($xxMap[$newPath] ?? [], $projectIds);
        }
        array_walk($xxMap, function (&$value) {
            $value = array_values(array_unique($value));
        });

        // 去掉read、write 例如: /api/aimarketingread , /api/aimarketingwrite => /api/aimarketing
        $noRWMap = [];
        foreach ($xxMap as $path => $projectIds) {
            $noRWPath = preg_replace('/(read|write)$/i', '', $path);
            $noRWMap[$noRWPath] = array_merge($noRWMap[$noRWPath] ?? [], $projectIds);
        }
        array_walk($noRWMap, function (&$value) {
            $value = array_values(array_unique($value));
        });
        echo \GuzzleHttp\json_encode($noRWMap);die;
    }




    // ./yiic-test zhengbogiang FixSlsLogActive
    public function actionFixSlsLogActive()
    {

        $db = Yii::app()->prometheus_db;
        //删除tbl_sls_log_active_api的数据
        $deleteSlsSql = "delete from tbl_sls_log_active_api where 1=1";
        $db->createCommand($deleteSlsSql)->execute();

        $slsDateSql = "select * from tbl_sls_log_active_api_date";
        $slsDateList = $db->createCommand($slsDateSql)->queryAll();

        $slsDateListByDate = ArrayUtil::groupBy($slsDateList, 'date');

        $slsLogActiveApiModel = new \common\modules\prometheus\library\auto_test\sls_log_active_api\SlsLogActiveApi();
//        $i=0;
        foreach ($slsDateListByDate as $date => $slsDateList) {
//            if ($date!='2024-08-07') continue;
//            if ($i == 2) {
//                break;
//            }
            $count = count($slsDateList);
            echo "date:{$date},count:{$count}".PHP_EOL;
            //插入tbl_sls_log_active_api 数据,这里需要改造一下insertTableFiledRule ，直接去改代码,不用的时候记得关闭，避免执行时候数据报错
            $slsLogActiveApiModel->create($slsDateList);

continue;
            //插入完成后，需要重新统计tbl_apifox_api_project_statistics_deta的have_apifox_count、no_have_apifox_count、active_api_count
            $slslogStatSql = "select
    apifox_project_name,
    count(1) as active_api_count,
    sum(IF(apifox_api_count>0,1,0)) as have_apifox_count,
    sum(IF(apifox_api_count=0,1,0)) as no_have_apifox_count
from tbl_sls_log_active_api where enable_flag=1 group by apifox_project_name";

            $slslogStatMap = array_column($db->createCommand($slslogStatSql)->queryAll(true),null,'apifox_project_name');
            foreach ($slslogStatMap as $projectName => $slslogStat) {
                if (empty($projectName))
                    continue;
                $updateSql = "update tbl_apifox_api_project_statistics_date set active_api_count={$slslogStat['active_api_count']},have_apifox_count={$slslogStat['have_apifox_count']},no_have_apifox_count={$slslogStat['no_have_apifox_count']} where project_name='{$projectName}' and date='{$date}'";
                $db->createCommand($updateSql)->execute();

                //$date 当天的中心统计
                $centerSql = "select center,sum(active_api_count) as active_api_count,sum(have_apifox_count) as have_apifox_count,sum(no_have_apifox_count) as no_have_apifox_count from tbl_apifox_api_project_statistics_date where center_flag=0  and date='{$date}' group by center";
                $centerList = $db->createCommand($centerSql)->queryAll();
                foreach ($centerList as $centerItem) {
                    $updateCenterSql = "update tbl_apifox_api_project_statistics_date set active_api_count={$centerItem['active_api_count']},have_apifox_count={$centerItem['have_apifox_count']},no_have_apifox_count={$centerItem['no_have_apifox_count']} where center='{$centerItem['center']}' and center_flag=1 and date='{$date}'";
                    $db->createCommand($updateCenterSql)->execute();
                }
            }

            /**
             * 影响到tbl_apifox_api_statistics中的request_flag字段
             * request_flag字段影响到api_project_statistics的request_count、not_request_count、active_cover_api_count、active_not_cover_api_count、active_api_params_count、active_api_actual_case_params_count
             * api_project_statistics的 `request_count` 影响到：request_rate
             * api_project_statistics的 `not_request_count` 影响到：无
             * api_project_statistics的 `active_cover_api_count` 影响到：无
             * api_project_statistics的 `active_not_cover_api_count` 影响到：无
             * api_project_statistics的 `active_api_params_count` 影响到：无
             * api_project_statistics的 `active_api_actual_case_params_count` 影响到：无
             */

            //插入数据后的全量数据slsLog
            $slsNowSql = "select apifox_project_id,path from tbl_sls_log_active_api where enable_flag=1";
            $slsNowList = $db->createCommand($slsNowSql)->queryAll();

            $apifoxDateSql = "select api_id,date,path,project_id from tbl_apifox_api_statistics_date where date='{$date}'";
            $apifoxDateList = $db->createCommand($apifoxDateSql)->queryAll();
            $enableApiId = [];
            $disableApiId = [];
            foreach ($apifoxDateList as $apifoxDateItem) {
                $request_flag = 0;
                foreach ($slsNowList as $slsNowItem) {
                    if ( ($apifoxDateItem['project_id'] == $slsNowItem['apifox_project_id'])
                        && (strtolower($apifoxDateItem['path']) == strtolower($slsNowItem['path']))) {
                        $request_flag=1;
                        continue;
                    }
                }
                if ($request_flag) {
                    $enableApiId[] = $apifoxDateItem['api_id'];
                }else{
                    $disableApiId[] = $apifoxDateItem['api_id'];
                }
            }
            //更新tbl_apifox_api_statistics_date表中的request_flag字段
            if (!empty($enableApiId)) {
                $enableApiIdStr = implode(',', $enableApiId);
                $enableSql = "update tbl_apifox_api_statistics_date set request_flag=1 where api_id in ({$enableApiIdStr}) and `date`='{$date}'";
                $db->createCommand($enableSql)->execute();
            }

            //$disableApiId 不用更新，默认是0
            if (!empty($disableApiId)) {
                $disableApiIdStr = implode(',', $disableApiId);
                $disableSql = "update tbl_apifox_api_statistics_date set request_flag=0 where api_id in ({$disableApiIdStr}) and `date`='{$date}'";
                $db->createCommand($disableSql)->execute();
            }

            //重新计算tbl_apifox_api_project_statistics_date表中的request_count,not_request_count,active_cover_api_count,active_not_cover_api_count,active_api_params_count,active_api_actual_case_params_count
            $statApiDateSql = "select project_id,
       count(1) as api_count,
       sum(IF(request_flag=1, 1, 0)) as request_count,
       sum(IF(request_flag=0, 1, 0)) as not_request_count,
       sum(IF(related_suite_case_count>0 && request_flag=1, 1, 0)) as active_cover_api_count,
       sum(IF(related_suite_case_count=0 && request_flag=1, 1, 0)) as active_not_cover_api_count,
       sum(IF(request_flag=1, params_count, 0)) as active_api_params_count,
       sum(IF(request_flag=1, strict_cover_params_count, 0))  as active_api_actual_case_params_count
from tbl_apifox_api_statistics_date 
where date ='{$date}'
group by project_id";
            $statApiDateList = $db->createCommand($statApiDateSql)->queryAll();
            foreach ($statApiDateList as $statApiDateItem) {
                $request_rate = $statApiDateItem['api_count'] == 0 ? 0 : round(($statApiDateItem['request_count'] / $statApiDateItem['api_count']) * 100, 2);
                $updateApiDateSql = "update tbl_apifox_api_project_statistics_date set request_count={$statApiDateItem['request_count']},
not_request_count={$statApiDateItem['not_request_count']},active_cover_api_count={$statApiDateItem['active_cover_api_count']},
active_not_cover_api_count={$statApiDateItem['active_not_cover_api_count']},active_api_params_count={$statApiDateItem['active_api_params_count']},
active_api_actual_case_params_count={$statApiDateItem['active_api_actual_case_params_count']},request_rate={$request_rate} 
where project_id={$statApiDateItem['project_id']} and center_flag=0 and date='{$date}' ";
                $db->createCommand($updateApiDateSql)->execute();
            }
            //中心统计
            $centerSql = "select center,
sum(api_count) as api_count,
sum(request_count) as request_count,sum(not_request_count) as not_request_count,
sum(active_cover_api_count) as active_cover_api_count,sum(active_not_cover_api_count) as active_not_cover_api_count,
sum(active_api_params_count) as active_api_params_count,sum(active_api_actual_case_params_count) as active_api_actual_case_params_count 
from tbl_apifox_api_project_statistics_date where center_flag=0 and date='{$date}' group by center";
            $centerList = $db->createCommand($centerSql)->queryAll();
            foreach ($centerList as $centerItem) {
                $request_rate = $centerItem['api_count'] == 0 ? 0 : round(($centerItem['request_count'] / ($centerItem['api_count'])) * 100, 2);
                $updateCenterSql = "update tbl_apifox_api_project_statistics_date set request_count={$centerItem['request_count']},
not_request_count={$centerItem['not_request_count']},active_cover_api_count={$centerItem['active_cover_api_count']},
active_not_cover_api_count={$centerItem['active_not_cover_api_count']},active_api_params_count={$centerItem['active_api_params_count']},
active_api_actual_case_params_count={$centerItem['active_api_actual_case_params_count']},request_rate={$request_rate}
where center='{$centerItem['center']}' and center_flag=1 and date='{$date}' ";
                $db->createCommand($updateCenterSql)->execute();
            }

        }

        echo "success".PHP_EOL;
    }


    // ./yiic-test zhengbogiang FixCenterData
    public function actionFixCenterData()
    {
        //修复0802后的中心数据
        $db = Yii::app()->prometheus_db;
        $sql = "select date,center,sum(api_count) as api_count,sum(case_count) as case_count,
                sum(related_suite_case_count) as related_suite_case_count,
                sum(cover_api_count) as cover_api_count,
                sum(not_cover_api_count) as not_cover_api_count,
                sum(params_count) as params_count,
                sum(cover_params_count) as cover_params_count,
                sum(strict_cover_params_count) as strict_cover_params_count,
                sum(cover_values_params_count) as cover_values_params_count,
                sum(strict_cover_values_params_count) as strict_cover_values_params_count,
                sum(combination_count) as combination_count,
                sum(current_combination_count) as current_combination_count,
                sum(request_count) as request_count,
                sum(not_request_count) as not_request_count,
                sum(request_match_count) as request_match_count,
                sum(not_request_match_count) as not_request_match_count,
                sum(active_api_have_apifox_count) as active_api_have_apifox_count,
                sum(active_api_no_have_apifox_count) as active_api_no_have_apifox_count,
                sum(active_cover_api_count) as active_cover_api_count,
                sum(active_not_cover_api_count) as active_not_cover_api_count,
                sum(active_api_params_count) as active_api_params_count,
                sum(actual_cover_params_count) as actual_cover_params_count,
                sum(actual_use_apifox_params_count) as actual_use_apifox_params_count,
                sum(active_api_actual_case_params_count) as active_api_actual_case_params_count
from tbl_apifox_api_project_statistics_date where center_flag=0  and date in ('2025-04-16','2025-04-15')  group by center,date";
        $centerDateList = $db->createCommand($sql)->queryAll();

//        $updateCenterArr = ['other','平台研发中心'];

        foreach ($centerDateList as $item) {
            $center = $item['center'];
//            if (!in_array($center, $updateCenterArr)) {
//                continue;
//            }
            echo $center.$item['date'].PHP_EOL;

            $cover_params_rate = $item['params_count'] == 0 ? 0 : round(($item['cover_params_count'] / $item['params_count']) * 100, 2);
$strict_cover_params_rate = $item['params_count'] == 0 ? 0 : round(($item['strict_cover_params_count'] / $item['params_count']) * 100, 2);
$cover_values_params_rate = $item['params_count'] == 0 ? 0 : round(($item['cover_values_params_count'] / $item['params_count']) * 100, 2);
$strict_cover_values_params_rate = $item['params_count'] == 0 ? 0 : round(($item['strict_cover_values_params_count'] / $item['params_count']) * 100, 2);
$combination_coverage_rate = $item['combination_count'] == 0 ? 0 : round(($item['current_combination_count'] / $item['combination_count']) * 100, 2);
$request_rate = $item['api_count'] == 0 ? 0 : round(($item['request_count'] / $item['api_count']) * 100, 2);

            $updateSql = "update tbl_apifox_api_project_statistics_date set 
                                                  api_count={$item['api_count']},
case_count={$item['case_count']},
related_suite_case_count={$item['related_suite_case_count']},
cover_api_count={$item['cover_api_count']},
not_cover_api_count={$item['not_cover_api_count']},
params_count={$item['params_count']},
cover_params_count={$item['cover_params_count']},
strict_cover_params_count={$item['strict_cover_params_count']},
cover_values_params_count={$item['cover_values_params_count']},
strict_cover_values_params_count={$item['strict_cover_values_params_count']},
combination_count={$item['combination_count']},
current_combination_count={$item['current_combination_count']},
request_count={$item['request_count']},
not_request_count={$item['not_request_count']},
request_match_count={$item['request_match_count']},
not_request_match_count={$item['not_request_match_count']},
active_api_have_apifox_count={$item['active_api_have_apifox_count']},
active_api_no_have_apifox_count={$item['active_api_no_have_apifox_count']},
active_cover_api_count={$item['active_cover_api_count']},
active_not_cover_api_count={$item['active_not_cover_api_count']},
active_api_params_count={$item['active_api_params_count']},
actual_cover_params_count={$item['actual_cover_params_count']},
actual_use_apifox_params_count={$item['actual_use_apifox_params_count']},
active_api_actual_case_params_count={$item['active_api_actual_case_params_count']},
cover_params_rate={$cover_params_rate},
strict_cover_params_rate={$strict_cover_params_rate},
cover_values_params_rate={$cover_values_params_rate},
strict_cover_values_params_rate={$strict_cover_values_params_rate},
combination_coverage_rate={$combination_coverage_rate},
request_rate={$request_rate}
                where center='{$center}' and center_flag=1 and date='{$item['date']}'";

//            echo $updateSql.PHP_EOL;
            $db->createCommand($updateSql)->execute();
        }

    }

    // 统计组装使用的企业数据情况
    // ./yiic-omg zhengbogiang getTotal --clientId=36262
    public function actionGetTotal($clientId = '')
    {
        if (!empty($clientId)) {
            $clientIds = explode(',', $clientId);
            $clients = [];
            foreach ($clientIds as $clientId) {
                $clients[] = ['client_id' => $clientId, 'client_type' => 1];
            }
        } else {
            $clients = array_column($this->getClientList(0, true, true, null, 0, 0), null, 'client_id');
        }
        $result = [];
        $result['productClient'] = $result['partListClient'] = $result['orderClient'] = $result['purchaseClient'] = [];
        foreach ($clients as $clientInfo) {
            try {
                $clientId   = $clientInfo['client_id'];
                $clientType = $clientInfo['client_type'];
                if ($clientType == 1) {
                    //echo $clientId.PHP_EOL;
                } else {
                    //echo 'NOT'. $clientId.PHP_EOL;
                    continue;
                }
                $db = PgActiveRecord::getDbByClientId($clientId);
                // 1、设置可为配件的产品的client
                $productSql = "select count(*) from tbl_product where client_id={$clientId} and enable_flag=1 and is_parts=1 limit 1";
                $productClient = $db->createCommand($productSql)->queryScalar();
                // 2、设置配件清单的client
                $partListSql = "select count(*) from tbl_product_parts where client_id={$clientId} and enable_flag=1 limit 1";
                $partListClient = $db->createCommand($partListSql)->queryScalar();
                // 3、销售订单产品含有配件的client
                $orderSql = "select count(*) from tbl_invoice_product_record where client_id={$clientId} and type = 2 and enable_flag=1 and master_id > 0 limit 1";
                $orderClient = $db->createCommand($orderSql)->queryScalar();

                $orderDetailSql = "select refer_id,product_id,sku_id from tbl_invoice_product_record where client_id={$clientId} and type = 2 and enable_flag=1 and master_id > 0";
                $orderProducts = $db->createCommand($orderDetailSql)->queryAll();
                $purchaseClient = 0;
                foreach ($orderProducts as $orderProduct) {
                    $productId = $orderProduct['product_id'] ?? '';
                    $skuId     = $orderProduct['sku_id'] ?? '';
                    $orderId   = $orderProduct['refer_id'] ?? '';
                    if (empty($productId) || empty($skuId) || empty($orderId)) {
                        continue;
                    }
                    // 4、采购订单产品含有配件的client
                    $purchaseSql = "select count(*) from tbl_purchase_order_product where client_id={$clientId} and enable_flag=1 and order_id =$orderId and product_id=$productId and sku_id=$skuId";
                    $purchaseClient = $db->createCommand($purchaseSql)->queryScalar();
                    if (!empty($purchaseClient)) {
                        break;
                    }
                }
                // 4、采购订单产品含有配件的client
//                $purchaseSql = "select count(*) from tbl_purchase_order_product where client_id={$clientId} and enable_flag=1 and master_id > 0 limit 1";
//                $purchaseClient = $db->createCommand($purchaseSql)->queryScalar();

                if ($productClient > 0) {
                    $result['productClient'][] = $clientId;
                }
                if ($partListClient > 0) {
                    $result['partListClient'][] = $clientId;
                }
                if ($orderClient > 0) {
                    $result['orderClient'][] = $clientId;
                }
                if ($purchaseClient > 0) {
                    $result['purchaseClient'][] = $clientId;
                }
            } catch (\Throwable $t) {}
        }

        echo 'productClient:'.count($result['productClient']).PHP_EOL;
        echo 'partListClient:'.count($result['partListClient']).PHP_EOL;
        echo 'orderClient:'.count($result['orderClient']).PHP_EOL;
        echo 'purchaseClient:'.count($result['purchaseClient']).PHP_EOL;
        echo 'productClient:'.json_encode($result['productClient']).PHP_EOL;
        echo 'partListClient:'.json_encode($result['partListClient']).PHP_EOL;
        echo 'orderClient:'.json_encode($result['orderClient']).PHP_EOL;
        echo 'purchaseClient:'.json_encode($result['purchaseClient']).PHP_EOL;
    }

    public function actionOrderInfo0112()
    { //  nohup ./yiic-omg zhengbogiang OrderInfo0112 >> /tmp/OrderInfo0112_2 2>&1 &   tail -f /tmp/OrderInfo0112_2
//        $errData ='{"759":[15361882949687]}';
//        $errData = json_decode($errData, true);
        $errData = [];
        foreach ($errData as $clientId => $orderIds) {
            //登录用户
            $userId = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();
            User::setLoginUserById($userId,$clientId);
            $db = \PgActiveRecord::getDbByClientId($clientId);
            $orderIdStr = implode(',', $orderIds);
            $sql = "select * from tbl_order where client_id={$clientId} and order_id in ($orderIdStr) and exchange_rate_usd=0 and archive_type not in (4,8)";
            $orderErrorList = $db->createCommand($sql)->queryAll();
            foreach ($orderErrorList as $orderErrorItem) {

                $currency = $orderErrorItem['currency'];
                $exchangeRate = $orderErrorItem['exchange_rate'];
                if ($exchangeRate<10 && $currency=='USD') {
                    $exchangeRate = (new ExchangeRateService($clientId))->cnyRateForCurrency($currency);
                }
                $exchangeRateUsd = (new ExchangeRateService($clientId))->usdRateForCurrency($currency);


                $orderUpdateParams = [
                    ':exchange_rate' => $exchangeRate,
                    ':exchange_rate_usd' => $exchangeRateUsd,
                    ':amount_rmb' => round($orderErrorItem['amount'] * ($exchangeRate / 100), 5),
                    ':amount_usd' => round($orderErrorItem['amount'] * $exchangeRateUsd / 100, 5),
                    ':product_total_amount_rmb' => round($orderErrorItem['product_total_amount'] * ($exchangeRate / 100), 5),
                    ':product_total_amount_usd' => round($orderErrorItem['product_total_amount'] * $exchangeRateUsd / 100, 5),
                    ':order_gross_margin_cny' => round($orderErrorItem['order_gross_margin'] * ($exchangeRate / 100), 5),
                    ':order_gross_margin_usd' => round($orderErrorItem['order_gross_margin'] * $exchangeRateUsd / 100, 5),
                    ':client_id' => $clientId,
                    ':order_id' => $orderErrorItem['order_id']
                ];

                $productList = json_decode($orderErrorItem['product_list'], true);
                $setProductListStr = '';
                if (!empty($productList)) {
                    //空不用修产品数据
                    foreach ($productList as &$productItem) {
                        $productItem['gross_margin_cny'] =round($productItem['gross_margin'] * ($exchangeRate / 100), 5);
                        $productItem['gross_margin_usd'] =round($productItem['gross_margin'] * $exchangeRateUsd / 100, 5);
                    }
                    unset($productItem);

                    $product_list =\common\library\util\PgsqlUtil::formatBson($productList);
                    $setProductListStr = "product_list={$product_list}, ";
                }

                $orderUpdateSql = "update tbl_order set exchange_rate=:exchange_rate,exchange_rate_usd=:exchange_rate_usd,
                     {$setProductListStr} amount_rmb=:amount_rmb,amount_usd=:amount_usd,
                     product_total_amount_rmb=:product_total_amount_rmb,product_total_amount_usd=:product_total_amount_usd,
                     order_gross_margin_cny=:order_gross_margin_cny,order_gross_margin_usd=:order_gross_margin_usd
                     where client_id=:client_id and order_id=:order_id";
                $orderUpdateResult = $db->createCommand($orderUpdateSql)->execute($orderUpdateParams);

                $recordField = ['currency','exchange_rate', 'exchange_rate_usd', 'amount_rmb', 'amount_usd', 'product_total_amount_rmb',
                    'product_total_amount_usd', 'order_gross_margin_cny', 'order_gross_margin_usd', 'client_id', 'order_id'];
                if ($orderUpdateResult) {
                    $logOldInfo = array_intersect_key($orderErrorItem, array_flip($recordField));
                    echo "client_id:{$clientId} order_id:{$orderErrorItem['order_id']} oldOrderDataJson:" . json_encode($logOldInfo) . PHP_EOL;
                    echo "client_id:{$clientId} order_id:{$orderErrorItem['order_id']} updateParams:" . json_encode($orderUpdateParams) . PHP_EOL;
                }


                $invoiceSql = "select * from tbl_invoice_product_record where refer_id={$orderErrorItem['order_id']}";
                $invoiceList = $db->createCommand($invoiceSql)->queryAll();
                $fixInvoiceCount = count($invoiceList);
                echo "需要修复tbl_invoice_product_record数据量：{$fixInvoiceCount}" . PHP_EOL;
                foreach ($invoiceList as $invoiceItem) {
                    $updateInvoiceParams = [
                        ':amount_rmb' => round($invoiceItem['amount'] * ($exchangeRate / 100), 5),
                        ':amount_usd' => round($invoiceItem['amount'] * $exchangeRateUsd / 100, 5),
                        ':invoice_amount_rmb' => round($invoiceItem['invoice_amount'] * ($exchangeRate / 100), 5),
                        ':invoice_amount_usd' => round($invoiceItem['invoice_amount'] * $exchangeRateUsd / 100, 5),
                        ':gross_margin_cny' => round($invoiceItem['gross_margin'] * ($exchangeRate / 100), 5),
                        ':gross_margin_usd' => round($invoiceItem['gross_margin'] * $exchangeRateUsd / 100, 5),
                        ':client_id' => $clientId,
                        ':id' => $invoiceItem['id'],
                    ];

                    $setPurchaseCostUnitStr = '';
                    if (!empty($invoiceItem['purchase_cost_unit'])) {
                        $setPurchaseCostUnitStr = "purchase_cost_unit_rmb=:purchase_cost_unit_rmb,purchase_cost_unit_usd=:purchase_cost_unit_usd,";
                        $updateInvoiceParams[':purchase_cost_unit_rmb'] =round($invoiceItem['purchase_cost_unit'] * ($exchangeRate / 100), 5);
                        $updateInvoiceParams[':purchase_cost_unit_usd'] =round($invoiceItem['purchase_cost_unit'] * $exchangeRateUsd / 100, 5);
                    }

                    $productUpdateSql = "update tbl_invoice_product_record set amount_rmb=:amount_rmb,amount_usd=:amount_usd,
                                        {$setPurchaseCostUnitStr} invoice_amount_rmb=:invoice_amount_rmb,invoice_amount_usd=:invoice_amount_usd,
                                        gross_margin_cny=:gross_margin_cny,gross_margin_usd=:gross_margin_usd
                                        where client_id=:client_id and id=:id";
                    $productUpdateResult = $db->createCommand($productUpdateSql)->execute($updateInvoiceParams);

                    $recordField = ['amount_rmb', 'amount_usd', 'invoice_amount_rmb', 'invoice_amount_usd', 'gross_margin_cny',
                        'gross_margin_usd', 'purchase_cost_unit_rmb', 'purchase_cost_unit_usd', 'client_id', 'id'];
                    if ($productUpdateResult) {
                        $logOldInfo = array_intersect_key($invoiceItem, array_flip($recordField));
                        echo "client_id:{$clientId} invoice_product_id:{$invoiceItem['id']} oldInvoiceProductDataJson:" . json_encode($logOldInfo) . PHP_EOL;
                        echo "client_id:{$clientId} invoice_product_id:{$invoiceItem['id']} updateProductParams:" . json_encode($updateInvoiceParams) . PHP_EOL;
                    }

                }

                echo "-----------刷新回款单状态-------------".PHP_EOL;
                \common\library\cash_collection\Helper::updateStatsAmountByRefer($clientId, CashCollection::REFER_TYPE_ORDER, $orderErrorItem['order_id'], [
                    'amount' => $orderErrorItem['amount'],
                    'amount_rmb' => $orderUpdateParams[':amount_rmb'],
                    'amount_usd' => $orderUpdateParams[':amount_usd'],
                ]);
            }
            User::resetLoginUser();
            \PgActiveRecord::releaseDbByClientId($clientId);

        }
        echo "success" . PHP_EOL;
    }


// nohup ./yiic-omg zhengbogiang OrderExchangeRateUsd >> /tmp/OrderExchangeRateUsd5 2>&1 &   tail -f /tmp/OrderExchangeRateUsd5

    public function actionOrderExchangeRateUsd()
    {
        $sql = "select client_id,order_id from tbl_order where exchange_rate_usd=0 and enable_flag=1 and create_time>'2024-01-12 09:30:00'";
        $dbList = $this->dbSetList(DbSet::TYPE_PGSQL);
        $count = 0;
        foreach ($dbList as $dbItem)
        {
            $setId = $dbItem['set_id'];
            echo '-----------doing  set id ',$setId,PHP_EOL;
            $db = \PgActiveRecord::getDbByDbSetId($setId);
            $orderList = $db->createCommand($sql)->queryAll();
            foreach ($orderList as $item) {
                echo "client_id:{$item['client_id']},-------order_id:{$item['order_id']}" . PHP_EOL;
            }
            $count += count($orderList);
            PgActiveRecord::releaseDbBySetId($setId);
        }
        echo "success,count:{$count}";
    }

    public function actionProductStats()
    {
        // 获取oms的client_id;
        $omsClientSql = "select distinct client_id from tbl_client_privilege where privilege='crm.functional.purchase' and enable_flag=1";
        $accountDb = Yii::app()->account_base_db;
        $omsClient = $accountDb->createCommand($omsClientSql)->queryColumn();


        $statsBase = [
            'client' => [
                'spu' => [
                    'manual' => [],
                    'import' => [],
                    'other' => [],
                    'edit_manual' => [],
                ],
                'sku' => [
                    'manual' => [],
                    'import' => [],
                    'other' => [],
                    'edit_manual' => [],
                ],
                'combine' => [
                    'manual' => [],
                    'import' => [],
                    'other' => [],
                    'edit_manual' => [],
                ]
            ],
            'spu' => [
                'spu' => [
                    'manual' => 0,
                    'import' => 0,
                    'other' => 0,
                ],
                'sku' => [
                    'manual' => 0,
                    'import' => 0,
                    'other' => 0,
                ],
                'combine' => [
                    'manual' => 0,
                    'import' => 0,
                    'other' => 0,
                ]
            ],
            'sku'=>[
                'sku'=>[
                    'manual' => 0,
                    'import' => 0,
                    'other' => 0,
                ],
            ]
        ];

        // 获取当前日期
        $currentDate = new DateTime();
        // 获取当前周的开始时间（周一）和结束时间（周日）
        $startOfCurrentWeek = clone $currentDate;
        $startOfCurrentWeek->modify('last monday');
        $endOfCurrentWeek = clone $currentDate;
        $endOfCurrentWeek->modify('this sunday');

        $earlyDay = '';
        // 计算前四周每周的开始时间和结束时间
        for ($i = 1; $i <= 4; $i++) {
            // 计算本周的开始时间
            $startOfWeek = clone $startOfCurrentWeek;
            $startOfWeek->modify("-{$i} week");

            // 计算本周的结束时间
            $endOfWeek = clone $endOfCurrentWeek;
            $endOfWeek->modify("-{$i} week");

            $startOfWeekStr = $startOfWeek->format('Y-m-d');
            $endOfWeekStr = $endOfWeek->format('Y-m-d');
            // 输出本周的开始时间和结束时间
            $dateMap[$startOfWeekStr] = ['min' => $startOfWeekStr, 'max' => $endOfWeekStr];

            if ($i == 4) {
                $earlyDay = $startOfWeekStr;
            }
        }

        $dataStats = array_combine(array_keys($dateMap), array_fill(0, count($dateMap), $statsBase));
        $dataStatsByOms = array_combine(array_keys($dateMap), array_fill(0, count($dateMap), $statsBase));

        $clientIds = array_column($this->getClientList(), 'client_id');
        foreach ($clientIds as $clientId) {

            $pgdb = \PgActiveRecord::getDbByClientId($clientId);
            if (!$pgdb) {
                echo "--此client【{$clientId}】无效..".PHP_EOL;
                continue;
            }

            //计算时间
            $beginTime = microtime(true);
            //先不考虑sku的统计
            $sql = "select client_id,product_type,source_type,create_time from tbl_product where enable_flag=1 and client_id={$clientId} and product_type IN (1, 2, 3) and create_time >='{$earlyDay}'";
            $productList = $pgdb->createCommand($sql)->queryAll();
            foreach ($productList as $productItem) {
                $clientId = $productItem['client_id'];
                $productType = $productItem['product_type'];
                $sourceType = $productItem['source_type'];
                $createTime = $productItem['create_time'];

                $dateKey = '';
                foreach ($dateMap as $dateKeyItem => $dateItem) {
                    if ($createTime >= $dateItem['min'] && $createTime <= $dateItem['max']) {
                        $dateKey = $dateKeyItem;
                        break;
                    }
                }

                if (empty($dateKey)) {
                    continue;
                }

                $productTypeMap = [
                    1 => 'spu',
                    2 => 'sku',
                    3 => 'combine',
                ];

                $sourceTypeMap = [
                    0 => 'manual',
                    4 => 'import',
                ];

                $dataStats[$dateKey]['client'][$productTypeMap[$productType]][$sourceTypeMap[$sourceType] ?? 'other'][] = $clientId;
                $dataStats[$dateKey]['spu'][$productTypeMap[$productType]][$sourceTypeMap[$sourceType] ?? 'other']++;
                if (in_array($clientId, $omsClient)) {
                    $dataStatsByOms[$dateKey]['client'][$productTypeMap[$productType]][$sourceTypeMap[$sourceType] ?? 'other'][] = $clientId;
                    $dataStatsByOms[$dateKey]['spu'][$productTypeMap[$productType]][$sourceTypeMap[$sourceType] ?? 'other']++;
                }
            }
            $endTime = microtime(true);
            echo "计算产品统计时间：" . ($endTime - $beginTime) . PHP_EOL;

            $beginTime = microtime(true);
            //手动创建的产品被编辑过
            $editSql = "select a.product_id,a.client_id,a.create_time,b.product_type from tbl_product_history as a left join tbl_product as b on a.product_id=b.product_id where a.client_id={$clientId} and b.client_id={$clientId} and a.create_time >='{$earlyDay}' and b.product_type IN (1, 2, 3) and b.source_type=0 and a.type=2 and b.enable_flag=1";
            $editProductList = $pgdb->createCommand($editSql)->queryAll();
            foreach ($editProductList as $editProductItem) {
                $clientId = $editProductItem['client_id'];
                $productType = $editProductItem['product_type'];
                $createTime = $editProductItem['create_time'];

                $dateKey = '';
                foreach ($dateMap as $dateKeyItem => $dateItem) {
                    if ($createTime >= $dateItem['min'] && $createTime <= $dateItem['max']) {
                        $dateKey = $dateKeyItem;
                        break;
                    }
                }

                if (empty($dateKey)) {
                    continue;
                }

                $productTypeMap = [
                    1 => 'spu',
                    2 => 'sku',
                    3 => 'combine',
                ];

                $dataStats[$dateKey]['client'][$productTypeMap[$productType]]['edit_manual'][] = $clientId;
                if (in_array($clientId, $omsClient)) {
                    $dataStatsByOms[$dateKey]['client'][$productTypeMap[$productType]]['edit_manual'][] = $clientId;
                }
            }
            $endTime = microtime(true);
            echo "计算产品编辑统计时间：" . ($endTime - $beginTime) . PHP_EOL;


            $beginTime = microtime(true);
            $skuSql = "select b.client_id,b.source_type,b.create_time,b.product_type from tbl_product_sku as a left join tbl_product as b on a.product_id=b.product_id where  a.client_id={$clientId} and b.client_id={$clientId} and a.enable_flag=1 and b.enable_flag=1 and b.create_time >='{$earlyDay}' and b.product_type=2 ";
            $skuList = $pgdb->createCommand($skuSql)->queryAll();
            foreach ($skuList as $skuItem) {
                $clientId = $skuItem['client_id'];
                $productType = $skuItem['product_type'];
                $sourceType = $skuItem['source_type'];
                $createTime = $skuItem['create_time'];

                $dateKey = '';
                foreach ($dateMap as $dateKeyItem => $dateItem) {
                    if ($createTime >= $dateItem['min'] && $createTime <= $dateItem['max']) {
                        $dateKey = $dateKeyItem;
                        break;
                    }
                }

                if (empty($dateKey)) {
                    continue;
                }

                $productTypeMap = [
                    1 => 'spu',
                    2 => 'sku',
                    3 => 'combine',
                ];

                $sourceTypeMap = [
                    0 => 'manual',
                    4 => 'import',
                ];

                $dataStats[$dateKey]['sku'][$productTypeMap[$productType]][$sourceTypeMap[$sourceType] ?? 'other']++;
                if (in_array($clientId, $omsClient)) {
                    $dataStatsByOms[$dateKey]['sku'][$productTypeMap[$productType]][$sourceTypeMap[$sourceType] ?? 'other']++;
                }
            }
            $endTime = microtime(true);
            echo "计算sku统计时间：" . ($endTime - $beginTime) . PHP_EOL;


            PgActiveRecord::releaseDbByClientId($clientId);
        }
        //将$dataStats、$dataStatsByOms统计中的数组，统计成数字
        foreach ($dataStats as $dateKey => $dataStatsItem) {
            foreach ($dataStatsItem['client'] as $productType => $productTypeItem) {
                foreach ($productTypeItem as $sourceType => $sourceTypeItem) {
                    $dataStats[$dateKey]['client'][$productType][$sourceType] = count(array_unique($sourceTypeItem));
                }
            }
        }
        foreach ($dataStatsByOms as $dateKey => $dataItem) {
            foreach ($dataItem['client'] as $productType => $productTypeItem) {
                foreach ($productTypeItem as $sourceType => $sourceTypeItem) {
                    $dataStatsByOms[$dateKey]['client'][$productType][$sourceType] = count(array_unique($sourceTypeItem));
                }
            }
        }

        echo \GuzzleHttp\json_encode(['dataStats' => $dataStats, 'dataStatsByOms' => $dataStatsByOms]);
        die;
    }

    /**
     * 修复订单币种对应的汇率错误
     * tail -f /tmp/FixOrderCurrency
     * nohup ./yiic-omg zhengbogiang FixOrderCurrency >> /tmp/FixOrderCurrency 2>&1 &
     */
    public function actionFixOrderCurrency()
    {
        $clientIds = [523, 780, 3438, 3276, 5093, 7104, 12117, 18925, 6231, 20607, 25412, 25501, 27638, 33542, 33511, 34584, 51326, 50383, 58692, 58756, 63042, 30562, 66156, 74883, 78560, 88366, 339296, 337120];
        foreach ($clientIds as $clientId) {
            $db = \PgActiveRecord::getDbByClientId($clientId);
            $userId = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();
            User::setLoginUserById($userId, $clientId);

            $orderErrorSql = "select * from tbl_order where client_id={$clientId} and  ((exchange_rate!=100 and currency='CNY') or (exchange_rate_usd!=100 and currency='USD')) and create_time>'2023-09-01 00:00:00' and archive_type not in (4,8)";
            $orderErrorList = $db->createCommand($orderErrorSql)->queryAll();
            foreach ($orderErrorList as $orderErrorItem) {

                $currency = $orderErrorItem['currency'];
                $exchangeRate = $orderErrorItem['exchange_rate'];
                $exchangeRateUsd = $orderErrorItem['exchange_rate_usd'];
                if ($currency == 'CNY') {
                    $exchangeRate = 100;
                    if ($exchangeRateUsd == 100) {
                        $exchangeRateUsd = (new ExchangeRateService($clientId))->usdRateForCurrency($currency);
                    }
                } elseif ($currency == 'USD') {
                    $exchangeRateUsd = 100;
                    if ($exchangeRate == 100) {
                        $exchangeRate = (new ExchangeRateService($clientId))->cnyRateForCurrency($currency);
                    }
                }

                $orderUpdateParams = [
                    ':exchange_rate' => $exchangeRate,
                    ':exchange_rate_usd' => $exchangeRateUsd,
                    ':amount_rmb' => round($orderErrorItem['amount'] * ($exchangeRate / 100), 5),
                    ':amount_usd' => round($orderErrorItem['amount'] * $exchangeRateUsd / 100, 5),
                    ':product_total_amount_rmb' => round($orderErrorItem['product_total_amount'] * ($exchangeRate / 100), 5),
                    ':product_total_amount_usd' => round($orderErrorItem['product_total_amount'] * $exchangeRateUsd / 100, 5),
                    ':order_gross_margin_cny' => round($orderErrorItem['order_gross_margin'] * ($exchangeRate / 100), 5),
                    ':order_gross_margin_usd' => round($orderErrorItem['order_gross_margin'] * $exchangeRateUsd / 100, 5),
                    ':client_id' => $clientId,
                    ':order_id' => $orderErrorItem['order_id']
                ];

                $productList = json_decode($orderErrorItem['product_list'], true);
                $setProductListStr = '';
                if (!empty($productList)) {
                    //空不用修产品数据
                    foreach ($productList as &$productItem) {
                        $productItem['gross_margin_cny'] =round($productItem['gross_margin'] * ($exchangeRate / 100), 5);
                        $productItem['gross_margin_usd'] =round($productItem['gross_margin'] * $exchangeRateUsd / 100, 5);
                    }
                    unset($productItem);

                    $product_list =\common\library\util\PgsqlUtil::formatBson($productList);
                    $setProductListStr = "product_list={$product_list}, ";
                }

                $orderUpdateSql = "update tbl_order set exchange_rate=:exchange_rate,exchange_rate_usd=:exchange_rate_usd,
                     {$setProductListStr} amount_rmb=:amount_rmb,amount_usd=:amount_usd,
                     product_total_amount_rmb=:product_total_amount_rmb,product_total_amount_usd=:product_total_amount_usd,
                     order_gross_margin_cny=:order_gross_margin_cny,order_gross_margin_usd=:order_gross_margin_usd
                     where client_id=:client_id and order_id=:order_id";
                $orderUpdateResult = $db->createCommand($orderUpdateSql)->execute($orderUpdateParams);

                $recordField = ['currency','exchange_rate', 'exchange_rate_usd', 'amount_rmb', 'amount_usd', 'product_total_amount_rmb',
                    'product_total_amount_usd', 'order_gross_margin_cny', 'order_gross_margin_usd', 'client_id', 'order_id'];
                if ($orderUpdateResult) {
                    $logOldInfo = array_intersect_key($orderErrorItem, array_flip($recordField));
                    echo "client_id:{$clientId} order_id:{$orderErrorItem['order_id']} oldOrderDataJson:" . json_encode($logOldInfo) . PHP_EOL;
                    echo "client_id:{$clientId} order_id:{$orderErrorItem['order_id']} updateParams:" . json_encode($orderUpdateParams) . PHP_EOL;
                }


                $invoiceSql = "select * from tbl_invoice_product_record where refer_id={$orderErrorItem['order_id']}";
                $invoiceList = $db->createCommand($invoiceSql)->queryAll();
                $fixInvoiceCount = count($invoiceList);
                echo "需要修复tbl_invoice_product_record数据量：{$fixInvoiceCount}" . PHP_EOL;
                foreach ($invoiceList as $invoiceItem) {
                    $updateInvoiceParams = [
                        ':amount_rmb' => round($invoiceItem['amount'] * ($exchangeRate / 100), 5),
                        ':amount_usd' => round($invoiceItem['amount'] * $exchangeRateUsd / 100, 5),
                        ':invoice_amount_rmb' => round($invoiceItem['invoice_amount'] * ($exchangeRate / 100), 5),
                        ':invoice_amount_usd' => round($invoiceItem['invoice_amount'] * $exchangeRateUsd / 100, 5),
                        ':gross_margin_cny' => round($invoiceItem['gross_margin'] * ($exchangeRate / 100), 5),
                        ':gross_margin_usd' => round($invoiceItem['gross_margin'] * $exchangeRateUsd / 100, 5),
                        ':client_id' => $clientId,
                        ':id' => $invoiceItem['id'],
                    ];

                    $setPurchaseCostUnitStr = '';
                    if (!empty($invoiceItem['purchase_cost_unit'])) {
                        $setPurchaseCostUnitStr = "purchase_cost_unit_rmb=:purchase_cost_unit_rmb,purchase_cost_unit_usd=:purchase_cost_unit_usd,";
                        $updateInvoiceParams[':purchase_cost_unit_rmb'] =round($invoiceItem['purchase_cost_unit'] * ($exchangeRate / 100), 5);
                        $updateInvoiceParams[':purchase_cost_unit_usd'] =round($invoiceItem['purchase_cost_unit'] * $exchangeRateUsd / 100, 5);
                    }

                    $productUpdateSql = "update tbl_invoice_product_record set amount_rmb=:amount_rmb,amount_usd=:amount_usd,
                                        {$setPurchaseCostUnitStr} invoice_amount_rmb=:invoice_amount_rmb,invoice_amount_usd=:invoice_amount_usd,
                                        gross_margin_cny=:gross_margin_cny,gross_margin_usd=:gross_margin_usd
                                        where client_id=:client_id and id=:id";
                    $productUpdateResult = $db->createCommand($productUpdateSql)->execute($updateInvoiceParams);

                    $recordField = ['amount_rmb', 'amount_usd', 'invoice_amount_rmb', 'invoice_amount_usd', 'gross_margin_cny',
                        'gross_margin_usd', 'purchase_cost_unit_rmb', 'purchase_cost_unit_usd', 'client_id', 'id'];
                    if ($productUpdateResult) {
                        $logOldInfo = array_intersect_key($invoiceItem, array_flip($recordField));
                        echo "client_id:{$clientId} invoice_product_id:{$invoiceItem['id']} oldInvoiceProductDataJson:" . json_encode($logOldInfo) . PHP_EOL;
                        echo "client_id:{$clientId} invoice_product_id:{$invoiceItem['id']} updateProductParams:" . json_encode($updateInvoiceParams) . PHP_EOL;
                    }

                }

                echo "-----------刷新回款单状态-------------".PHP_EOL;
                \common\library\cash_collection\Helper::updateStatsAmountByRefer($clientId, CashCollection::REFER_TYPE_ORDER, $orderErrorItem['order_id'], [
                    'amount' => $orderErrorItem['amount'],
                    'amount_rmb' => $orderUpdateParams[':amount_rmb'],
                    'amount_usd' => $orderUpdateParams[':amount_usd'],
                ]);
            }

            User::resetLoginUser();
            \PgActiveRecord::releaseDbByClientId($clientId);
        }
        echo "success";
    }
    
    /**
     * 寻找灰度后创建币种、汇率有误的订单
     * tail -f /tmp/FindOrderRateError
     * nohup ./yiic-omg zhengbogiang FindOrderRateError >> /tmp/FindOrderRateError 2>&1 &
     */
    public function actionFindOrderRateError()
    {
        $sql="select archive_type,order_id,client_id,currency,exchange_rate,exchange_rate_usd,amount,amount_rmb,amount_usd,product_total_amount,product_total_amount_rmb,product_total_amount_usd from tbl_order where ((exchange_rate!=100 and currency='CNY') or (exchange_rate_usd!=100 and currency='USD')) and create_time>'2023-09-01 00:00:00'";
        $dbList = $this->dbSetList(DbSet::TYPE_PGSQL);
        foreach ($dbList as $dbItem)
        {
            $setId = $dbItem['set_id'];
            echo '-----------doing  set id ',$setId,PHP_EOL;
            $db = \PgActiveRecord::getDbByDbSetId($setId);
            $orderList = $db->createCommand($sql)->queryAll();
            foreach ($orderList as $item) {
                echo "dataJson:".json_encode($item, JSON_UNESCAPED_UNICODE).PHP_EOL;
            }
            PgActiveRecord::releaseDbBySetId($setId);
        }
        echo "success";
    }

    /**
     * 修复采购订单币种空、汇率有问题的数据
     * tail -f /tmp/FixPurchaseOrderCurrency
     * nohup ./yiic-omg zhengbogiang FixPurchaseOrderCurrency >> /tmp/FixPurchaseOrderCurrency 2>&1 &
     */
    public function actionFixPurchaseOrderCurrency()
    {
        //修复采购订单、
        //关联的付款单、应付款单数据
        $clientIds = [6216,20032,48043,27747,82923,1972,36157,18617,49621,32527,18297,780,88773,340042,11813,87857,81866,
            54951,60492,39771,4317,64691,32142,77241,339296,43116,62416,29443,33248,9832,83082,86852,67527,334002,47266,341850,31357,343062];

        foreach ($clientIds as $clientId) {
            echo "开始修复client:{$clientId}的错误数据" . PHP_EOL;
            $userId = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();
            User::setLoginUserById($userId, $clientId);
            $db = \PgActiveRecord::getDbByClientId($clientId);
            $sql = "select * from tbl_purchase_order where (exchange_rate_usd=0 or exchange_rate=0) and client_id={$clientId}  and create_time>'2023-08-29 10:00:00'";
            $purchaseErrorList = $db->createCommand($sql)->queryAll();


            foreach ($purchaseErrorList as $purchaseErrorItem) {
                $currency = $purchaseErrorItem['currency'];
                if (empty($currency)) {
                    $currency = 'CNY';
                }
                $exchangeRate = $purchaseErrorItem['exchange_rate'];
                $exchangeRateUsd = $purchaseErrorItem['exchange_rate_usd'];
                //将exchange_rate\exchange_rate_usd填上，然后进行amount_rmb\product_total_amount_rmb\payment_amount_rmb —usd 计算
                if (empty(floatval($exchangeRate))) {
                    $exchangeRate = (new ExchangeRateService($clientId))->cnyRateForCurrency($currency);
                }
                if (empty(floatval($exchangeRateUsd))) {
                    $exchangeRateUsd = (new ExchangeRateService($clientId))->usdRateForCurrency($currency);
                }

                if (empty(floatval($exchangeRate)) || empty(floatval($exchangeRateUsd))) {
                    echo "clientId:" . $clientId . "exchangeRate：" . $exchangeRate . "  exchangeRateUsd：" . $exchangeRateUsd . PHP_EOL;
                    continue;
                }

                //判断是否在审批中，是的话跳过，且做记录
                if (\common\library\approval_flow\Helper::getApprovingApplyFormId($clientId, $purchaseErrorItem['purchase_order_id'], Constants::ENTITY_TYPE_PURCHASE_ORDER)) {
                    echo "clientId:" . $clientId . "---" . $purchaseErrorItem['purchase_order_id'] . "正在审批,无法修改" . PHP_EOL;
                    continue;
                }

                $purchaseUpdateParams = [
                    ':currency' => $currency,
                    ':exchange_rate' => round($exchangeRate, 5),
                    ':exchange_rate_usd' => round($exchangeRateUsd, 5),
                    ':amount_rmb' => round($purchaseErrorItem['amount'] * ($exchangeRate / 100), 2),
                    ':amount_usd' => round($purchaseErrorItem['amount'] * $exchangeRateUsd / 100, 2),
                    ':product_total_amount_rmb' => round($purchaseErrorItem['product_total_amount'] * ($exchangeRate / 100), 2),
                    ':product_total_amount_usd' => round($purchaseErrorItem['product_total_amount'] * $exchangeRateUsd / 100, 2),
                    ':payment_amount_rmb' => round($purchaseErrorItem['payment_amount'] * ($exchangeRate / 100), 2),
                    ':payment_amount_usd' => round($purchaseErrorItem['payment_amount'] * $exchangeRateUsd / 100, 2),
                    ':purchase_order_id' => $purchaseErrorItem['purchase_order_id'],
                    ':client_id' => $clientId,
                ];

                $purchaseUpdateSql = "update tbl_purchase_order set currency=:currency,exchange_rate=:exchange_rate,
                              exchange_rate_usd=:exchange_rate_usd,amount_rmb=:amount_rmb,amount_usd=:amount_usd,
                              product_total_amount_rmb=:product_total_amount_rmb,product_total_amount_usd=:product_total_amount_usd,
                              payment_amount_rmb=:payment_amount_rmb,payment_amount_usd=:payment_amount_usd 
                              where purchase_order_id=:purchase_order_id and client_id=:client_id";
                $updatePurchaseResult = $db->createCommand($purchaseUpdateSql)->execute($purchaseUpdateParams);
                if ($updatePurchaseResult) {
                    echo "oldData:purchase_order_id:". $purchaseErrorItem['purchase_order_id']."OldData:".json_encode($purchaseErrorItem,JSON_UNESCAPED_UNICODE).PHP_EOL;
                    echo "clientId:" . $clientId . "%%%updatePurchaseSuccess_purchase_order_id:" . $purchaseErrorItem['purchase_order_id'] . "updateJson:" . json_encode($purchaseUpdateParams, JSON_UNESCAPED_UNICODE) . PHP_EOL;
                }

                //对应应付款单
                $payableSql = "select * from tbl_payable_invoice where client_id={$clientId} and refer_type=22 and refer_id={$purchaseErrorItem['purchase_order_id']}";
                $payableInfo = $db->createCommand($payableSql)->queryRow();
                $payableUpdateParams = [
                    ':currency' => $currency,
                    ':exchange_rate' => round($exchangeRate,5),
                    ':exchange_rate_usd' => round($exchangeRateUsd,5),
                    ':amount_rmb' => round($payableInfo['amount'] * ($exchangeRate / 100), 2),
                    ':amount_usd' => round($payableInfo['amount'] * $exchangeRateUsd / 100, 2),
                    ':payment_amount_rmb' => round($payableInfo['payment_amount'] * ($exchangeRate / 100), 2),
                    ':payment_amount_usd' => round($payableInfo['payment_amount'] * $exchangeRateUsd / 100, 2),
                    ':payable_invoice_id' => $payableInfo['payable_invoice_id'],
                    ':client_id' => $clientId,
                ];

                $payableUpdateSql = "update tbl_payable_invoice set currency=:currency,exchange_rate=:exchange_rate,
                               exchange_rate_usd=:exchange_rate_usd,amount_rmb=:amount_rmb,amount_usd=:amount_usd,
                               payment_amount_rmb=:payment_amount_rmb,payment_amount_usd=:payment_amount_usd
                              where payable_invoice_id=:payable_invoice_id and client_id=:client_id";
                $updatePayableResult = $db->createCommand($payableUpdateSql)->execute($payableUpdateParams);
                if ($updatePayableResult) {
                    echo "oldData:payable_invoice_id:". $payableInfo['payable_invoice_id']."OldData:".json_encode($payableInfo,JSON_UNESCAPED_UNICODE).PHP_EOL;
                    echo "clientId:" . $clientId . "^^^updatePayableSuccess_payable_invoice_id:" . $payableInfo['payable_invoice_id'] . "updateJson:" . json_encode($payableUpdateParams, JSON_UNESCAPED_UNICODE) . PHP_EOL;
                }

                //如果有付款金额，去找对应的付款单
                if ($payableInfo['payment_amount'] > 0) {
                    $paymentSql = "select a.* from tbl_payment_invoice as a inner join tbl_payment_record as b 
                                    on a.payment_invoice_id=b.payment_invoice_id and b.payable_invoice_id={$payableInfo['payable_invoice_id']}   
                                    and a.client_id={$clientId}";

                    $paymentList = $db->createCommand($paymentSql)->queryAll();
                    foreach ($paymentList as $paymentItem) {
                        $paymentCurrency = $paymentItem['currency'];
                        $paymentRate = $paymentItem['exchange_rate'];
                        $paymentRateUsd = $paymentItem['exchange_rate_usd'];
                        if (empty(floatval($paymentRate))) {
                            $paymentRate = (new ExchangeRateService($clientId))->cnyRateForCurrency($paymentCurrency);
                        }
                        if (empty(floatval($paymentRateUsd))) {
                            $paymentRateUsd = (new ExchangeRateService($clientId))->usdRateForCurrency($paymentCurrency);
                        }

                        $paymentUpdateParams = [
                            ':currency' => $currency,
                            ':exchange_rate' => round($paymentRate,5),
                            ':exchange_rate_usd' => round($paymentRateUsd,5),
                            ':amount_rmb' => round($paymentItem['amount'] * ($paymentRate / 100), 2),
                            ':amount_usd' => round($paymentItem['amount'] * $paymentRateUsd / 100, 2),
                            ':payment_invoice_id' => $paymentItem['payment_invoice_id'],
                            ':client_id' => $clientId,
                        ];

                        $paymentUpdateSql = "update tbl_payment_invoice set currency=:currency,exchange_rate=:exchange_rate,
                               exchange_rate_usd=:exchange_rate_usd,amount_rmb=:amount_rmb,amount_usd=:amount_usd
                              where payment_invoice_id=:payment_invoice_id and client_id=:client_id";
                        $updatePaymentResult = $db->createCommand($paymentUpdateSql)->execute($paymentUpdateParams);
                        if ($updatePaymentResult) {
                            echo "oldData:payment_invoice_id:" . $paymentItem['payment_invoice_id'] . "OldData:" . json_encode($paymentItem, JSON_UNESCAPED_UNICODE) . PHP_EOL;
                            echo "clientId:" . $clientId . "&&&updatePaymentSuccess_payment_invoice_id:" . $paymentItem['payment_invoice_id']  . "updateJson:" . json_encode($paymentUpdateParams, JSON_UNESCAPED_UNICODE) . PHP_EOL;
                        }
                    }
                }
            }

            User::resetLoginUser();
            PgActiveRecord::releaseDbByClientId($clientId);
        }
        echo "success";
    }
    
    //tail -f /tmp/RecomputeTransferData_client
    // nohup ./yiic-omg zhengbogiang recomputeTransferData >> /tmp/RecomputeTransferData_client 2>&1 &
    public function actionRecomputeTransferData()
    {

        //23928 该客户暂不修复，之前他的采购单状态设置有问题，他对应的采购任务单的任务进度一直都是错的，这个需要单独修复，跟客户协调
        $clientIds = [48043, 18617, 61179, 61181, 61184, 61185, 23412, 14833, 3192];

        foreach ($clientIds as $clientId) {
            $pgDb = \PgActiveRecord::getDbByClientId($clientId);

            $lastTransferProcessSql = "SELECT dynamic_id FROM (
    SELECT dynamic_id, ROW_NUMBER() OVER (PARTITION BY transfer_invoice_id ORDER BY create_time DESC) AS row_num
  FROM tbl_transfer_invoice_dynamic where client_id={$clientId} and type=5 and create_time>'2023-06-14 17:00:00') sub WHERE row_num = 1";

            $lastTransferProcessData = $pgDb->createCommand($lastTransferProcessSql)->queryAll();
            if (empty($lastTransferProcessData)) {
                echo "clientId:{$clientId},无数据，跳过！" . PHP_EOL;
                continue;
            }

            $dynamicIds = implode(',', array_column($lastTransferProcessData, 'dynamic_id'));


            //灰度期间有变更过的transfer_invoice_id sql  通过dynamic_id 获取最新数据
            $changeProcessSql = "select * from tbl_transfer_invoice_dynamic where client_id={$clientId} and dynamic_id in ({$dynamicIds}) and type=5 and create_time>'2023-06-14 17:00:00' and create_time<'2023-06-21 12:00:00'";
            $changeProcessData =$pgDb->createCommand($changeProcessSql)->queryAll();
            $changeProcessMap = array_column($changeProcessData, null, 'transfer_invoice_id');

            //修复过进度的transfer_invoice_id sql
            $changeProcessSystemSql = "select * from tbl_transfer_invoice_dynamic where client_id={$clientId} and type=11";
            $changeProcessSystemData =$pgDb->createCommand($changeProcessSystemSql)->queryAll();

            $changeProcessTransferInvoiceId = array_column($changeProcessData, 'transfer_invoice_id');
            $changeProcessSystemTransferInvoiceId = array_column($changeProcessSystemData, 'transfer_invoice_id');

            $oldChangeProcessTransferInvoiceId = array_diff($changeProcessTransferInvoiceId, $changeProcessSystemTransferInvoiceId);
            if (empty($oldChangeProcessTransferInvoiceId)) {
                echo "clientId:{$clientId},无数据，跳过！" . PHP_EOL;
                continue;

            }

            $transferIds = implode(',', $oldChangeProcessTransferInvoiceId);
            $transferSql = "select * from tbl_product_transfer_invoice where client_id={$clientId} and status!=1 and type in (38,39,40) and delete_flag=0 and transfer_invoice_id in ({$transferIds})";
            $transferList = $pgDb->createCommand($transferSql)->queryAll();
            if (empty($transferList)) {
                echo "clientId:{$clientId},无数据，跳过！" . PHP_EOL;
                continue;
            }

            foreach ($transferList as $transferItem) {
                //登陆用户
                User::setLoginUserById($transferItem['create_user']);
                if ($transferItem['product_total_count'] == 0) {
                    echo "transfer_invoice_id:{$transferItem['transfer_invoice_id']},product_total_count是0，跳过！" . PHP_EOL;
                    continue;
                }

                $lastChangeProcessData = json_decode($changeProcessMap[$transferItem['transfer_invoice_id']]['data'] ?? '{}', true);
                if (empty($lastChangeProcessData)) {
                    continue;
                }
                $lastChangeProcessMap = array_column($lastChangeProcessData, 'value', 'id');


                switch ($transferItem['type']) {
                    case Constants::TYPE_PRODUCT_TRANSFER_PURCHASE:
                        $transferComputation = new PurchaseComputation($clientId);
                        break;
                    case Constants::TYPE_PRODUCT_TRANSFER_INBOUND:
                        $transferComputation = new InboundComputation($clientId);
                        break;
                    case Constants::TYPE_PRODUCT_TRANSFER_OUTBOUND:
                        $transferComputation = new OutboundComputation($clientId);
                        break;
                }
                $transferComputation->setReferType($transferItem['type']);
                $transferComputation->setReferId([$transferItem['transfer_invoice_id']]);//单据id 接受数组
                $statusMap = $transferComputation->getStatusMap();

                $computationProcess = $statusMap[$transferItem['transfer_invoice_id']]['task_process'] ?? 0;
                $lastChangeProcess = $lastChangeProcessMap['new_progress'] ?? 0;

                if ($computationProcess != $lastChangeProcess) {
                    //需要重算的数据
                    echo "### {$transferItem['client_id']} ### {$transferItem['type']} ### {$transferItem['transfer_invoice_id']} ### {$computationProcess} ### {$lastChangeProcess} ".PHP_EOL;

                    //开始重算
                    echo "开始重算：".PHP_EOL;
                    $trigger = TransferTriggerFactory::make($transferItem['type'],$clientId);
                    $trigger->setSystemFixDataFlag(true);
                    $trigger->setTransferInvoiceIds([$transferItem['transfer_invoice_id']]);
                    $trigger->trigger();
                }
            }

        }

    }

    //tail -f /tmp/NewProductTransfer_06201500
    // nohup ./yiic-test zhengbogiang NewProductTransfer >> /tmp/NewProductTransfer_06201500 2>&1 &
    public function actionNewProductTransfer()
    {
        $sql = "select a.* from tbl_product_transfer_invoice as a
    left join tbl_product_transfer_record as b
on a.transfer_invoice_id=b.transfer_invoice_id
where a.status!=1 and a.type!=43 and a.delete_flag=0 and b.transfer_invoice_id is null";

        $dbList = $this->dbSetList(DbSet::TYPE_PGSQL);

        foreach ($dbList as $dbItem) {
            $setId = $dbItem['set_id'];
            if (($setId > 10000 && $setId < 10005)  || $setId==103) {
                continue;
            }

            $pgdb = \PgActiveRecord::getDbByDbSetId($setId);
            $list = $pgdb->createCommand($sql)->queryAll();
            //set_id纬度， 切割成client_id纬度 后进行操作
            $clientIds = Arr::uniqueFilterValues(array_column($list, 'client_id'));
            $productTransferDataByClient = array_combine($clientIds, array_fill(0, count($clientIds), []));
            foreach ($list as $item) {
                $productTransferDataByClient[$item['client_id']][] = $item;
            }


            foreach ($productTransferDataByClient as $clientId => $batchProductTransferData) {

                //测试
                if ($clientId != 14119) {
                    continue;
                }

                foreach ($batchProductTransferData as $productTransferDatum) {
                    if (empty($productTransferDatum['refer_id']) || empty($productTransferDatum['transfer_invoice_id'])) {
                        continue;
                    }

//                    if ($productTransferDatum['transfer_invoice_id'] != 3376326826) {
//                        continue;
//                    }

                    $productTransferRecodeSql = "select * from tbl_product_transfer_record where transfer_invoice_id = {$productTransferDatum['transfer_invoice_id']} and client_id={$clientId} and type={$productTransferDatum['type']}";
                    $productTransferRecodeData = $pgdb->createCommand($productTransferRecodeSql)->queryAll();
                    if ($productTransferRecodeData) {
                        echo "xxxxxxxxx不要插入哦哦哦哦哦哦" . PHP_EOL;
                        //避免两次插入
                        continue;
                    }

                    //登陆用户
                    User::setLoginUserById($productTransferDatum['create_user']);

                    if ($productTransferDatum['refer_type'] == \Constants::TYPE_ORDER) {
                        //获取订单产品的数据 //如果上游单是销售单 && 不能是组合产品
                        $invoiceProductSql = "select * from tbl_invoice_product_record where client_id={$clientId} and type=2 
                        and refer_id={$productTransferDatum['refer_id']} and product_type!=3 and enable_flag=1 order by id asc";
                        $invoiceProductList = $pgdb->createCommand($invoiceProductSql)->queryAll();
//                    echo \GuzzleHttp\json_encode($invoiceProductList);die;

                        $productList = [];
                        foreach ($invoiceProductList as $item) {
                            $productList[] = [
                                'transfer_invoice_record_id' => intval(\ProjectActiveRecord::produceAutoIncrementId()),
                                'transfer_invoice_id' => $productTransferDatum['transfer_invoice_id'], //单据id
                                'type' => $productTransferDatum['type'],    //单据类型
                                'refer_id' => $productTransferDatum['refer_id'],  //单据的关联id
                                'refer_type' => $productTransferDatum['refer_type'],
                                'sku_id' => $item['sku_id'],    //订单产品skuid
                                'product_id' => $item['product_id'],    //订单产品product_id
                                'remark' => $item['product_remark'],    //订单产品 product_remark
                                'description' => $item['description'],  //订单产品 描述
                                'expect_time' => $productTransferDatum['expect_time'],  //单据的期望时间
                                'reach_count' => $item['count'],    //订单产品的数量 count
                                'sub_refer_id' => $item['id'],  //订单产品id
                                'create_user' => $productTransferDatum['create_user'],
                                'update_user' => $productTransferDatum['create_user'],
                                'create_time' => xm_function_now(),
                                'update_time' => xm_function_now(),
                                'delete_flag' => \Constants::DELETE_FLAG_FALSE,
                            ];
                        }

                        if (!empty($productList)) {
                            if ($productTransferDatum['type'] == \Constants::TYPE_PRODUCT_TRANSFER_PURCHASE) {
                                $batchProductTransferRecord = new  BatchPurchaseProductTransferRecord($clientId);
                                $batchProductTransferRecord->getOperator()->create($productList);

                            } elseif ($productTransferDatum['type'] == \Constants::TYPE_PRODUCT_TRANSFER_OUTBOUND) {
                                $batchProductTransferRecord = new  BatchOutboundProductTransferRecord($clientId);
                                $batchProductTransferRecord->getOperator()->create($productList);
                            }
                        }
                    }
                    elseif ($productTransferDatum['refer_type'] == \Constants::TYPE_PURCHASE_ORDER) {
                        //获取采购订单产品的数据
                        $purchaseProductSql = "select * from tbl_purchase_order_product where client_id={$clientId} 
                        and purchase_order_id={$productTransferDatum['refer_id']} and enable_flag=1 order by purchase_order_product_id asc";
                        $purchaseProductList = $pgdb->createCommand($purchaseProductSql)->queryAll();

                        $productList = [];
                        foreach ($purchaseProductList as $item) {
                            $productList[] = [
                                'transfer_invoice_record_id' => intval(\ProjectActiveRecord::produceAutoIncrementId()),
                                'transfer_invoice_id' => $productTransferDatum['transfer_invoice_id'], //单据id
                                'type' => $productTransferDatum['type'],    //单据类型
                                'refer_id' => $productTransferDatum['refer_id'],  //单据的关联id
                                'refer_type' => $productTransferDatum['refer_type'],
                                'sku_id' => $item['sku_id'],    //订单产品skuid
                                'product_id' => $item['product_id'],    //订单产品product_id
                                'remark' => $item['product_remark'],    //订单产品 product_remark
//                                'description' => $item['description'],  //订单产品 描述 --采购订单产品无描述
                                'expect_time' => $productTransferDatum['expect_time'],  //单据的期望时间
                                'reach_count' => $item['count'],    //订单产品的数量 count
                                'sub_refer_id' => $item['purchase_order_product_id'],  //订单产品id
                                'create_user' => $productTransferDatum['create_user'],
                                'update_user' => $productTransferDatum['create_user'],
                                'create_time' => xm_function_now(),
                                'update_time' => xm_function_now(),
                                'delete_flag' => \Constants::DELETE_FLAG_FALSE,
                            ];
                        }

                        if (!empty($productList)) {
                            $batchProductTransferRecord = new  BatchInboundProductTransferRecord($clientId);
                            $batchProductTransferRecord->getOperator()->create($productList);
                        }

                    }


                    if ($productList) {
                        $productListStr = json_encode($productList, JSON_UNESCAPED_UNICODE);
                        self::info("transfer_invoice_id:{$productTransferDatum['transfer_invoice_id']} productListStr:{$productListStr}");
                    }

                    //单据状态修改 将 未受理(5)、已受理(6)、已拒绝(7) 改为 处理中(2) 不区分任何任务
                    if (in_array($productTransferDatum['status'], [5, 6, 7])) {
                        $updateStatusSql = "update tbl_product_transfer_invoice set status=2 where client_id={$clientId} and transfer_invoice_id={$productTransferDatum['transfer_invoice_id']} and delete_flag=0";
                        //执行sql，记录日志
                        $updateStatusResult = $pgdb->createCommand($updateStatusSql)->execute();
                        if ($updateStatusResult) {
                            self::info("transfer_invoice_id:{$productTransferDatum['transfer_invoice_id']} updateSql:{$updateStatusSql}");
                        }
                    }


                    $hasRelationDataFlag = 0;
                    //缺少relation的数据恢复   必须是新任务单据  、判断是采购任务、还是出库任务
                    if ($productTransferDatum['type'] == \Constants::TYPE_PRODUCT_TRANSFER_PURCHASE) {
                        $relationSql = "select * from tbl_product_transfer_record as tptr inner join tbl_purchase_order_product as tpop on tptr.sub_refer_id=tpop.invoice_product_id
where tptr.transfer_invoice_id={$productTransferDatum['transfer_invoice_id']} and tptr.client_id={$clientId} and tptr.type=38 and tptr.delete_flag=0 and tpop.enable_flag=1 order by tptr.transfer_invoice_id asc";
                        $relationData =$pgdb->createCommand($relationSql)->queryAll();
                        if ($relationData) {

                            $hasRelationDataFlag = 1;
                            $transferInvoiceRelationMap = [];
                            foreach ($relationData as $relationDatum) {
                                if (!empty($relationDatum['transfer_invoice_id']) && $relationDatum['transfer_invoice_record_id'])
                                {
                                    $transferInvoiceRelationMap[] = [
                                        'transfer_invoice_id' => $relationDatum['transfer_invoice_id'],
                                        'transfer_invoice_record_id' => $relationDatum['transfer_invoice_record_id'],
                                        'refer_id' => $relationDatum['purchase_order_id'],
                                        'sub_refer_id' => $relationDatum['purchase_order_product_id'],
                                        'enable_flag' => 1
                                    ];
                                }
                            }
                            $transferInvoiceRelation = new BatchPurchaseProductTransferRelation($clientId);
                            $transferInvoiceRelation->getOperator()->setRelation($transferInvoiceRelationMap);
                        }
                    }
                    elseif ($productTransferDatum['type'] == \Constants::TYPE_PRODUCT_TRANSFER_OUTBOUND) {
                        $relationSql = "select * from tbl_product_transfer_record as tptr inner join tbl_outbound_record as tpop on tptr.sub_refer_id=tpop.sub_refer_id
where tptr.transfer_invoice_id={$productTransferDatum['transfer_invoice_id']} and tptr.client_id={$clientId} and tptr.type=40 and tptr.delete_flag=0 and tpop.delete_flag=0 order by tptr.transfer_invoice_id asc";
                        $relationData =$pgdb->createCommand($relationSql)->queryAll();
                        if ($relationData) {
                            $hasRelationDataFlag = 1;
                            $transferInvoiceRelationMap = [];
                            foreach ($relationData as $relationDatum) {
                                if (!empty($relationDatum['transfer_invoice_id']) && $relationDatum['transfer_invoice_record_id'])
                                {
                                    $transferInvoiceRelationMap[] = [
                                        'transfer_invoice_id' => $relationDatum['transfer_invoice_id'],
                                        'transfer_invoice_record_id' => $relationDatum['transfer_invoice_record_id'],
                                        'refer_id' => $relationDatum['outbound_invoice_id'],
                                        'sub_refer_id' => $relationDatum['outbound_record_id'],
                                        'type' => 40,
                                        'enable_flag' => 1
                                    ];
                                }
                            }
                            $transferInvoiceRelation = new BatchOutboundProductTransferRelation($clientId);
                            $transferInvoiceRelation->getOperator()->setRelation($transferInvoiceRelationMap);
                        }
                    }
                    elseif ($productTransferDatum['type'] == \Constants::TYPE_PRODUCT_TRANSFER_INBOUND) {
                        $relationSql = "select * from tbl_product_transfer_record as tptr inner join tbl_inbound_record as tpop on tptr.sub_refer_id=tpop.sub_refer_id
where tptr.transfer_invoice_id={$productTransferDatum['transfer_invoice_id']} and tptr.client_id={$clientId} and tptr.type=39 and tptr.delete_flag=0 and tpop.delete_flag=0 order by tptr.transfer_invoice_id asc";
                        $relationData =$pgdb->createCommand($relationSql)->queryAll();
                        if ($relationData) {
                            $hasRelationDataFlag = 1;
                            $transferInvoiceRelationMap = [];
                            foreach ($relationData as $relationDatum) {
                                if (!empty($relationDatum['transfer_invoice_id']) && $relationDatum['transfer_invoice_record_id'])
                                {
                                    $transferInvoiceRelationMap[] = [
                                        'transfer_invoice_id' => $relationDatum['transfer_invoice_id'],
                                        'transfer_invoice_record_id' => $relationDatum['transfer_invoice_record_id'],
                                        'refer_id' => $relationDatum['inbound_invoice_id'],
                                        'sub_refer_id' => $relationDatum['inbound_record_id'],
                                        'type' => 39,
                                        'enable_flag' => 1
                                    ];
                                }
                            }
                            $transferInvoiceRelation = new BatchInboundProductTransferRelation($clientId);
                            $transferInvoiceRelation->getOperator()->setRelation($transferInvoiceRelationMap);
                        }
                    }

                    //保存relation记录
                    if ($hasRelationDataFlag) {
                        $transferInvoiceRelationMapStr = json_encode($transferInvoiceRelationMap, true);
                        self::info("transfer_invoice_id:{$productTransferDatum['transfer_invoice_id']} transferInvoiceRelationMapStr:" . $transferInvoiceRelationMapStr);
                    }


                    //单据状态重刷 task_process !=0 的进行重刷 也判断采购任务、还是出库任务
                    if ($productTransferDatum['task_process'] != 0) {
                        self::info("transfer_invoice_id:{$productTransferDatum['transfer_invoice_id']} old_task_process:" . $productTransferDatum['task_process']);
                        $trigger = TransferTriggerFactory::make($productTransferDatum['type'],$clientId);
                        $trigger->setSystemFixDataFlag(true);
                        $trigger->setTransferInvoiceIds([$productTransferDatum['transfer_invoice_id']]);
                        $trigger->trigger();
                    }

                }
            }

        }

        echo "success".PHP_EOL;
    }


    public function actionPurchaseTrigger()
    {
        User::setLoginUserById(11858712);
        $trigger = TransferTriggerFactory::make(40,14119);
        $trigger->setSystemFixDataFlag(true);
        $trigger->setTransferInvoiceIds([3393018227]);
        $trigger->trigger();
    }

    public function actionFixPurchaseProductTransfer()
    {
        $client_id = 43062;
        $sql = "select * from tbl_product_transfer_relation where  client_id={$client_id} and  type=38 and enable_flag=2 and update_time='2023-05-24 14:23:27'";
//        $client_id =14787;
//        $sql = "select * from tbl_product_transfer_relation where  client_id={$client_id} and  type=38 and enable_flag=2 and update_time='2023-06-12 23:12:27'";

        $pgsql = \PgActiveRecord::getDbByClientId($client_id);
        $relationData = $pgsql->createCommand($sql)->queryAll();

        $purchaseOrderProductIds = Arr::uniqueFilterValues(array_column($relationData, 'sub_refer_id'));
        $transferInvoiceIds = Arr::uniqueFilterValues(array_column($relationData, 'transfer_invoice_id'));

        //有效采购订单产品数据
        $purchaseOrderProductIdStr = implode(',', $purchaseOrderProductIds);
        $purchaseOrderProductEnableSql = "select * from tbl_purchase_order_product where client_id={$client_id} and purchase_order_product_id in ({$purchaseOrderProductIdStr}) and enable_flag=1";
        $purchaseOrderProductData = $pgsql->createCommand($purchaseOrderProductEnableSql)->queryAll();

        //有效单据任务明细数据
        $transferInvoiceIdStr = implode(',', $transferInvoiceIds);
        $transferInvoiceEnableSql = "select * from tbl_product_transfer_invoice where type=38 and client_id={$client_id} and transfer_invoice_id in ({$transferInvoiceIdStr}) and delete_flag=0";
        $transferInvoiceData = $pgsql->createCommand($transferInvoiceEnableSql)->queryAll();

        $enablePurchaseOrderProductId = Arr::uniqueFilterValues(array_column($purchaseOrderProductData, 'purchase_order_product_id'));
        $enableTransferInvoiceId = Arr::uniqueFilterValues(array_column($transferInvoiceData, 'transfer_invoice_id'));

        //需恢复的relation数据
        $recoverRelationId = [];

        foreach ($relationData as $datum) {
            if (in_array($datum['sub_refer_id'], $enablePurchaseOrderProductId)) {
                $recoverRelationId[] = $datum['relation_id'];

//                if (in_array($datum['transfer_invoice_id'], $enableTransferInvoiceId)) {
//                    //需要恢复的采购任务id
//                    $recoverTransferInvoiceId[] = $datum['transfer_invoice_id'];
//                }
            }
        }


        if (empty($recoverRelationId)) {
            echo "无恢复的relation数据" . PHP_EOL;
            die();
        }

        $recoverRelationIdStr = implode(',', $recoverRelationId);
        $updateRelationSql = "update tbl_product_transfer_relation set enable_flag=1 where client_id={$client_id} and type=38 and enable_flag=2 and update_time='2023-05-24 14:23:27' and relation_id in ($recoverRelationIdStr)";
        echo "updateRelationSql: {$updateRelationSql}" . PHP_EOL;
        $updateRelationCount = $pgsql->createCommand($updateRelationSql)->execute();
        echo "updateRelationCount: {$updateRelationCount}" . PHP_EOL;

        //恢复采购任务进度 ，分情况，如果是已完成，直接将 task_process =100 ，处理中的看是否有动态，通过动态获取  经查询无该数据，直接修改 status=4的数据 task_process=100
        //select * from tbl_transfer_invoice_dynamic where client_id=43062 and transfer_invoice_type=38 and type=5
        // and transfer_invoice_id in (select transfer_invoice_id from tbl_product_transfer_invoice where client_id=43062 and type=38 and delete_flag=0 and task_process=0 and status=2)

        $recoverTransferInvoiceId = [];
        foreach ($transferInvoiceData as $datum) {
            if ($datum['status'] == 4 && $datum['task_process'] == 0) {
                $recoverTransferInvoiceId[] = $datum['transfer_invoice_id'];
            }
        }

        if (empty($recoverTransferInvoiceId)) {
            echo "无恢复的TransferInvoice数据" . PHP_EOL;
            die();
        }

        $recoverTransferInvoiceIdStr = implode(',', $recoverTransferInvoiceId);
        $updateTransferInvoiceSql = "update tbl_product_transfer_invoice set task_process=100 where client_id={$client_id} and type=38 and delete_flag=0 and status=4 and task_process=0 and transfer_invoice_id in ($recoverTransferInvoiceIdStr)";
        echo "updateTransferInvoiceSql: {$updateTransferInvoiceSql}" . PHP_EOL;
        $updateTransferInvoiceCount = $pgsql->createCommand($updateTransferInvoiceSql)->execute();
        echo "updateTransferInvoiceCount: {$updateTransferInvoiceCount}" . PHP_EOL;

    }

    public function actionRefreshPrivilege()
    {
        $clientId = 18137;
        $userId = 55257804;

        \common\library\privilege_v3\PrivilegeService::getInstance($clientId)
            ->initClient(
                \common\library\privilege_v3\PrivilegeConstants::OKKI_PRO_SYSTEM_ID,
                $userId, true, true
            );
        $systemIds = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getSystemIds();

        if (in_array(\common\library\privilege_v3\PrivilegeConstants::OKKI_PRO_SYSTEM_ID, $systemIds)) {
            echo "版本是okki_pro" . PHP_EOL;
        } else {
            echo "版本不是okki_pro" . PHP_EOL;
        }


        $client = Client::getClient($clientId);
        $version = $client->getExtentAttributes([Client::EXTERNAL_KEY_CRM_VERSION])[Client::EXTERNAL_KEY_CRM_VERSION];
        if ($version == 20) {
            echo "版本号是20" . PHP_EOL;
        } else {
            echo "版本号不是20" . PHP_EOL;
        }

    }

    // nohup ./yiic zhengbogiang PurchaseOrder2OrderNum >> tail -f /tmp/PurchaseOrder2OrderNum 2>&1 &
    public function actionPurchaseOrder2OrderNum()
    {

        $sql = "select  a.client_id,a.purchase_order_id,a.create_type,count(b.order_id) as order_num
from tbl_purchase_order as a
left join tbl_purchase_order_product as b
on a.purchase_order_id = b.purchase_order_id
and  a.enable_flag=1 and b.enable_flag=1 and a.client_id=10
group by a.purchase_order_id";

        $dbList = $this->dbSetList(DbSet::TYPE_PGSQL);
        foreach ($dbList as $dbItem) {
            $setId = $dbItem['set_id'];
            if ($setId > 10000 && $setId < 10005) {
                continue;
            }

            $pgdb = \PgActiveRecord::getDbByDbSetId($setId);
            $list = $pgdb->createCommand($sql)->queryAll();
            foreach ($list as $item) {
                echo "{$item['client_id']} ### {$item['purchase_order_id']} ### {$item['create_type']} ### {$item['order_num']}" . PHP_EOL;
            }

        }

        echo "success".PHP_EOL;
    }


    //tail -f /tmp/DelAlibabaOrderFundRelationAndSyncOrder
    // nohup ./yiic zhengbogiang DelAlibabaOrderFundRelationAndSyncOrder >> /tmp/DelAlibabaOrderFundRelationAndSyncOrder 2>&1 &
    public function actionDelAlibabaOrderFundRelationAndSyncOrder()
    {

        /**
         * 1、获取 存在 `tbl_alibaba_order_fund_relation` && 不存在 `tbl_cash_collection` 的 cash_collection_id
         * 2、删除对应的`tbl_alibaba_order_fund_relation`的信息
         * 3、记录删除的order_id，进行重新同步 ，同步脚本如下，userId操作人的ID（用主账号ID）、storeId、alibabaOrderId 可以在tbl_alibaba_order_relation表中获得
         * ./yiic  alibaba syncSingleOrder --clientId=33344 --userId=55419548 --storeId=236805276 --alibabaOrderId=164537791001024584 --orderId=5531097197480
         */

        $dbList = $this->dbSetList(DbSet::TYPE_PGSQL);
        foreach ($dbList as $dbItem)
        {
            $setId = $dbItem['set_id'];
            if ($setId > 10000 && $setId < 10005) {
                continue;
            }
            echo '-----------doing  set id ',$setId,PHP_EOL;
            //db纬度
            $orderFundRelationSql = "select client_id,order_id,alibaba_trade_id,cash_collection_id from tbl_alibaba_order_fund_relation";
            $cashCollectionSql = "select cash_collection_id from tbl_cash_collection";

            $pgdb = \PgActiveRecord::getDbByDbSetId($setId);
            $orderFundRelationList = $pgdb->createCommand($orderFundRelationSql)->queryAll();
            $cashCollectionList = $pgdb->createCommand($cashCollectionSql)->queryAll();

            $orderFundRelationCashCollectionId = array_filter(array_unique(array_column($orderFundRelationList, 'cash_collection_id')));
            $cashCollectionId = array_filter(array_unique(array_column($cashCollectionList, 'cash_collection_id')));

            //需要删除的 cash_collection_id
            $diffCashCollection = array_diff($orderFundRelationCashCollectionId, $cashCollectionId);

            if (empty($diffCashCollection)) {
                echo "set_id  没有差异数据，跳过".PHP_EOL;
                continue;
            }

            $needHandleCashCollection = [];
            foreach ($orderFundRelationList as $item) {
                if (in_array($item['cash_collection_id'],$diffCashCollection)) {
                    $needHandleCashCollection[$item['client_id']][] = $item;
                }
            }

            foreach ($needHandleCashCollection as $clientId => $delArr) {
                $userId = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();
                User::setLoginUserById($userId, $clientId);

                $orderIds = array_filter(array_unique(array_column($delArr, 'order_id')));
                if (empty($orderIds)) {
                    echo "clientId :{$clientId} 的删除orderId是空".PHP_EOL;
                    continue;
                }
                $orderIdStr = implode(',', $orderIds);
                $alibabaOrderRelationSql = "select * from tbl_alibaba_order_relation where client_id={$clientId} and order_id in ({$orderIdStr})";
                $alibabaOrderRelationList = $pgdb->createCommand($alibabaOrderRelationSql)->queryAll();
                $alibabaOrderRelationList = array_column($alibabaOrderRelationList, null, 'order_id');

                foreach ($delArr as $item) {
                    $storeId = $alibabaOrderRelationList[$item['order_id']]['store_id'] ?? 0;
                    $alibabaOrderId = $alibabaOrderRelationList[$item['order_id']]['alibaba_trade_id'] ?? 0;
                    if (empty($storeId) || empty($alibabaOrderId)) {
                        echo "无storeId || alibabaOrderId ,client_id:{$clientId} - order_id:{$item['order_id']} - cash_collection_id:{$item['cash_collection_id']}" . PHP_EOL;
                        continue;
                    }

                    $deleteOrderFundRelationSql = "delete from tbl_alibaba_order_fund_relation where client_id={$clientId} and cash_collection_id={$item['cash_collection_id']}";
                    $deleteResult = $pgdb->createCommand($deleteOrderFundRelationSql)->execute();
                    if ($deleteResult) {
                        echo "clientId:{$clientId} 删除tbl_alibaba_order_fund_relation-cash_collection_id:{$item['cash_collection_id']} 成功 count :{$deleteResult}" . PHP_EOL;

                        try {
                            $result =  \common\library\alibaba\order\AlibabaOrderService::syncSingleOrder($clientId, $userId, $storeId,$alibabaOrderId, $item['order_id']);
                        } catch (Exception $exception) {

                            $syncData = json_encode([
                                'clientId' => $clientId,
                                'userId' => $userId,
                                'storeId' => $storeId,
                                'alibabaOrderId' => $alibabaOrderId,
                                'orderId' => $item['order_id'],
                            ]);
                            echo "同步订单出错～～{$exception->getMessage()} ,同步信息:{$syncData}".PHP_EOL;
                        }
                        echo "同步订单成功～～" . PHP_EOL;
                    }
                }

            }

        }
        echo "success".PHP_EOL;die;
    }

    public function actionCloseRequireAndHide()
    {
        $qwe = [
            \Constants::TYPE_SALE_OUTBOUND_INVOICE => ['company_id', 'currency', 'exchange_rate', 'product_total_amount', 'product_unit', 'sale_price', 'product_amount'],
            \Constants::TYPE_PURCHASE_INBOUND_INVOICE => ['supplier_id', 'currency', 'exchange_rate', 'product_total_amount', 'product_unit', 'purchase_price', 'product_amount'],
        ];
        $clientId = 14787;
        $db = \ProjectActiveRecord::getDbByClientId($clientId);
        foreach ($qwe as $type => $idArr) {
            $idStr = implode("','", $idArr);
            $sql = "update tbl_custom_field SET edit_required = 1, edit_hide = 1 where client_id = {$clientId} and type={$type} and id in ('{$idStr}')";
            $wwww = $db->createCommand($sql)->execute();
            echo "count:{$wwww}" . PHP_EOL;
        }
    }

    public function actionRefreshCashCollectionInvoiceStatus($clientId,$userId,$cashCollectionInvoiceId)
    {
        $result = (new CashCollectionInvoiceApi)->refreshAllocateStatus($clientId, $userId, $cashCollectionInvoiceId);
        if ($result == false) {
            echo "RefreshCashCollectionInvoiceStatus fail" . PHP_EOL;
        }
        echo "success" . PHP_EOL;
    }

    public function actionFixOrderStatus()
    {
        $clientId = 43391;
        $db = \ProjectActiveRecord::getDbByClientId($clientId);
        $update = "update tbl_item_setting set field_type=34 where client_id=43391 and item_id=3626813028950";
        $updateResult = $db->createCommand($update)->execute();
        var_dump($updateResult);
    }

    //修复产品库存
    public function actionFixProductInventory()
    {
        $clientId= 62116;
        $pgdb = \PgActiveRecord::getDbByClientId($clientId);

        $sql = "select * from tbl_outbound_record where client_id={$clientId} and outbound_invoice_id=5388272960883 and status=2 and delete_flag=0";
        $outboundList = $pgdb->createCommand($sql)->queryAll();
        if (empty($outboundList)) {
            throw new \RuntimeException("无修复数据");
        }

        $updateCount = 0;
        foreach ($outboundList as $item) {

//            [{"count": "70500.0000000000", "inbound_record_id": 5191404576459}]
            $origin_info = json_decode($item['origin_info'], true);
            if (empty($origin_info)) {
                echo "outbound_record_id:{$item['outbound_record_id']}  没有origin_info数据";
                echo PHP_EOL;
                continue;
            }

            foreach ($origin_info as $origin_item) {
                $updateSql = "update tbl_inbound_record set enable_count=enable_count+ {$origin_item['count']} where  client_id={$clientId} and inbound_record_id={$origin_item['inbound_record_id']}";
                echo $updateSql;
                echo PHP_EOL;
                $updateResult = $pgdb->createCommand($updateSql)->execute();
                if ($updateResult) {
                    echo "inbound_record_id:{$origin_item['inbound_record_id']}  可用数据+{$origin_item['count']}";
                    echo PHP_EOL;
                }
            }
            $updateCount++;
        }

        if (count($outboundList) == $updateCount) {
            $updateOutboundRecodeSql = "update tbl_outbound_record set status=0 , delete_flag=1 where  client_id={$clientId} and outbound_invoice_id = 5388272960883 and status=2 and delete_flag=0";
            $updateOutboundRecodeResult = $pgdb->createCommand($updateOutboundRecodeSql)->execute();

            $updateOutboundInvoiceSql = "update tbl_outbound_invoice set status=0 , delete_flag=1 where  client_id={$clientId} and outbound_invoice_id = 5388272960883 and status=2 and delete_flag=0";
            $updateOutboundInvoiceResult = $pgdb->createCommand($updateOutboundInvoiceSql)->execute();

            var_dump($updateOutboundRecodeResult,$updateOutboundInvoiceResult);
            echo PHP_EOL;

        } else {
            echo "刷新条数不足！！！";
            echo PHP_EOL;
        }
        echo "success";
        echo PHP_EOL;
    }

    //采购订单的入库状态 历史数据处理 -- 后期上线移到上线脚本
    public function actionPurchaseOrderInboundStatus()
    {
        $clientId= 14787;
        $pgdb = \PgActiveRecord::getDbByClientId($clientId);

        $sql = "select purchase_order_id from tbl_purchase_order where client_id={$clientId} and inbound_status=0";
        $purchaseOrderList = $pgdb->createCommand($sql)->queryAll();
        if (empty($purchaseOrderList)) {
            throw new \RuntimeException("无修复数据");
        }

        $adminId = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();
        User::setLoginUserById($adminId);

        foreach ($purchaseOrderList as $item) {
            $purchaseOrder = new PurchaseOrder($clientId,$item['purchase_order_id']);
            $purchaseOrder->refreshInboundStatus();
        }
        echo "success";
        echo PHP_EOL;
    }


    /**
     * 查询出导入成功的任务
     * nohup ./yiic  zhengbogiang OrderImportSuccess >> /tmp/orderImportSuccess.log 2>&1 &
     */
    public function actionOrderImportSuccess($skipSetId=0)
    {
        $echoArr = [];
        $dbList = $this->dbSetList(3);
        foreach ($dbList as $dbItem)
        {
            $setId = $dbItem['set_id'];
            if ($setId <= $skipSetId) {
                continue;
            }
            if ($setId > 10000 && $setId < 10005) {
                continue;
            }
            echo 'doing  set id ',$setId,PHP_EOL;
            $db = \PgActiveRecord::getDbByDbSetId($setId);
            $sql = "select * from tbl_import where type=2 and status=3";
            $list = $db->createCommand($sql)->queryAll();

            foreach ($list as $item) {
                $echoArr[$item['client_id']][] = $item['import_id'];
                echo "import_id:{$item['import_id']}~~~~~~~client_id:{$item['client_id']}" . PHP_EOL;
            }
            \PgActiveRecord::releaseDbBySetId($setId);
        }

        echo \GuzzleHttp\json_encode($echoArr);die;
    }

    //获取查询出导入成功的任务 的数据后处理
    public function actionFixOrderAmount($skipSetId=0)
    {
        $dbList = $this->dbSetList(DbSet::TYPE_PGSQL);
        foreach ($dbList as $dbItem)
        {
            $setId = $dbItem['set_id'];
            if ($setId <= $skipSetId) {
                continue;
            }
            if ($setId > 10000 && $setId < 10005) {
                continue;
            }
            echo 'doing  set id ',$setId,PHP_EOL;
            $db = \PgActiveRecord::getDbByDbSetId($setId);
            $sql = "select * from tbl_import where type=2 and status=3 and create_time<='2022-12-08 23:59:59'";
            $list = $db->createCommand($sql)->queryAll();

            $fixClientId = array_filter(array_unique(array_column($list, 'client_id')));
            if (empty($fixClientId)) {
                continue;
            }

            $fixImport = [];//['clientId' => ['import_id1', 'import_id2']]
            $fixImport = array_combine($fixClientId, array_fill(0, count($fixClientId), []));
            foreach ($list as $importItem) {
                $fixImport[$importItem['client_id']][] = $importItem['import_id'];
            }
            if (empty($fixImport)) {
                continue;
            }
            $this->FixImportDataOrderAmountError($fixImport);

            \PgActiveRecord::releaseDbBySetId($setId);
        }
    }

    protected function FixImportDataOrderAmountError($fixImport)
    {
        foreach ($fixImport as $clientId => $importIds) {
            $pgdb = \PgActiveRecord::getDbByClientId($clientId);
            $adminUserId = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();
            User::setLoginUserById($adminUserId);

            foreach ($importIds as $importId) {
                //搜索出orderId
                $selectOrderIdSql = "select biz_id from tbl_import_history where client_id={$clientId} and import_id={$importId} and biz_type=2";
                $orderIdList = $pgdb->createCommand($selectOrderIdSql)->queryAll();
                $orderIdArr = array_column($orderIdList, 'biz_id');
                if (empty($orderIdArr)) {
                    //没有导入数据
                    echo "clientId:{$clientId}~~~import_id:{$importId} 无订单数据，无需修复".PHP_EOL;
                    continue;
                }
                $orderIdStr = implode(',', $orderIdArr);
                $orderListSql = "select exchange_rate,exchange_rate_usd,amount,product_total_amount,amount_rmb,amount_usd,
                                    product_total_amount_rmb,product_total_amount_usd,client_id,order_id
                                    from tbl_order 
                                    where client_id={$clientId} and archive_type=8 and order_id in ({$orderIdStr})";
                $orderList = $pgdb->createCommand($orderListSql)->queryAll();
                foreach ($orderList as $orderItem) {
                    $rateRmb = $orderItem['exchange_rate'];
                    $rateUsd = $orderItem['exchange_rate_usd'];
                    $orderAmount = $orderItem['amount'];
                    $productTotalAmount = $orderItem['product_total_amount'];
                    $amountRMB = round($orderAmount * ($rateRmb / 100), 5);
                    $amountUSD = round($orderAmount * $rateUsd / 100, 5);
                    $productTotalAmountRmb = round($productTotalAmount * ($rateRmb / 100), 5);
                    $productTotalAmountUsd = round($productTotalAmount * $rateUsd / 100, 5);

                    //计算后金额一致不需要修改
                    if ($amountRMB == $orderItem['amount_rmb'] && $amountUSD == $orderItem['amount_usd']) {
                        continue;
                    }

                    //更新订单金额
                    $updateOrderSql = "update tbl_order set amount_rmb=:amount_rmb ,amount_usd=:amount_usd 
                            , product_total_amount_rmb=:product_total_amount_rmb ,product_total_amount_usd=:product_total_amount_usd
                            where client_id=:client_id and archive_type=8 and order_id=:order_id";
                    $updateOrderResult = $pgdb->createCommand($updateOrderSql)->execute([
                        ':amount_rmb' => $amountRMB,
                        ':amount_usd' => $amountUSD,
                        ':product_total_amount_rmb' => $productTotalAmountRmb,
                        ':product_total_amount_usd' => $productTotalAmountUsd,
                        ':client_id' => $clientId,
                        ':order_id' => $orderItem['order_id'],
                    ]);
                    if ($updateOrderResult) {
                        \common\library\cash_collection\Helper::updateStatsAmountByRefer($clientId, 1, $orderItem['order_id'], [
                            'amount' => $orderAmount,
                            'amount_rmb' => $amountRMB,
                            'amount_usd' => $amountUSD,
                        ]);
                        echo "clientId:{$clientId}~~~order_id:{$orderItem['order_id']} 订单数据修复成功 :-D".PHP_EOL;
                    }else {
                        echo "clientId:{$clientId}~~~order_id:{$orderItem['order_id']} 订单数据修复失败 T^T".PHP_EOL;
                    }

                    //订单产品
                    $invoiceProductSql = "select * from tbl_invoice_product_record where client_id={$clientId} and refer_id={$orderItem['order_id']}";
                    $invoiceProductList = $pgdb->createCommand($invoiceProductSql)->queryAll();
                    foreach ($invoiceProductList as $invoiceProduct) {
                        $invoiceProductAmount = $invoiceProduct['amount'];
                        $productAmountRMB = round($invoiceProductAmount * ($rateRmb / 100), 5);
                        $productAmountUSD = round($invoiceProductAmount * $rateUsd / 100, 5);

                        //更新订单产品 金额
                        $updateSql = "update tbl_invoice_product_record set invoice_amount_rmb=:invoice_amount_rmb,invoice_amount_usd=:invoice_amount_usd,amount_rmb=:amount_rmb,amount_usd=:amount_usd 
                                        where client_id=:client_id and id=:id and refer_id=:refer_id";
                        $updateResult = $pgdb->createCommand($updateSql)->execute([
                            ':invoice_amount_rmb' => $amountRMB,
                            ':invoice_amount_usd' => $amountUSD,
                            ':amount_rmb' => $productAmountRMB,
                            ':amount_usd' => $productAmountUSD,
                            ':client_id' => $clientId,
                            ':id' => $invoiceProduct['id'],
                            ':refer_id' => $orderItem['order_id'],
                        ]);
                        if ($updateResult) {
                            echo "clientId:{$clientId}~~~id:{$invoiceProduct['id']} 订单产品数据修复成功 (*^__^*)".PHP_EOL;
                        }else {
                            echo "clientId:{$clientId}~~~id:{$invoiceProduct['id']} 订单产品数据修复失败 (〒︿〒)".PHP_EOL;
                        }
                    }

                }

            }

            \PgActiveRecord::releaseDbByClientId($clientId);
        }
    }

    //删除用户信息
    public function actionDeleteData()
    {
        $clientId = 32254;
        $pgsqlDb = \PgActiveRecord::getDbByClientId($clientId);
        $mysqlDb = \ProjectActiveRecord::getDbByClientId($clientId);

        $rw = 'read';
        //出入库
        // 入库
        $inboundInvoiceSql = "from tbl_inbound_invoice where client_id={$clientId}";
        $this->executeSql($pgsqlDb,'tbl_inbound_invoice',$inboundInvoiceSql,$rw);
        $inboundRecordSql = "from tbl_inbound_record where client_id={$clientId}";
        $this->executeSql($pgsqlDb,'tbl_inbound_record',$inboundRecordSql,$rw);


        // 出库
        $outboundInvoiceSql = "from tbl_outbound_invoice where client_id={$clientId}";
        $this->executeSql($pgsqlDb,'tbl_outbound_invoice',$outboundInvoiceSql,$rw);

        $outboundRecordSql = "from tbl_outbound_record where client_id={$clientId}";
        $this->executeSql($pgsqlDb,'tbl_outbound_record',$outboundRecordSql,$rw);

        // 退货
        $warehouseReturnInvoiceSql = "from tbl_warehouse_return_invoice where client_id={$clientId}";
        $this->executeSql($pgsqlDb,'tbl_warehouse_return_invoice',$warehouseReturnInvoiceSql,$rw);

        $warehouseReturnRecordSql = "from tbl_warehouse_return_record where client_id={$clientId}";
        $this->executeSql($pgsqlDb,'tbl_warehouse_return_record',$warehouseReturnRecordSql,$rw);


        //库存
        $productInventorySql = "from tbl_product_inventory where client_id={$clientId}";
        $this->executeSql($pgsqlDb,'tbl_product_inventory',$productInventorySql,$rw);

        //产品
        $productSql = "from tbl_product where client_id={$clientId}";
        $this->executeSql($pgsqlDb,'tbl_product',$productSql,$rw);

//        $fileIds = [];
//        foreach ($productList as $productItem) {
//            $images = json_decode($productItem['images'], true);
//            if ($images) {
//                $fileIds = array_merge($fileIds, array_column($images, 'file_id'));
//            }
//        }
//
//        //拿出images 删除图片
//        if ($fileIds) {
//            //mysql tbl_upload_file
//            $fileIdStr = implode(',', $fileIds);
//            $fileSql = "from tbl_upload_file where client_id={$clientId} and file_id in ({$fileIdStr})";
//        }


        $productSkuSql = "from tbl_product_sku where client_id={$clientId}";
        $this->executeSql($pgsqlDb,'tbl_product_sku',$productSkuSql,$rw);

        $productHistorySql = "from tbl_product_history where client_id={$clientId}";
        $this->executeSql($pgsqlDb,'tbl_product_history',$productHistorySql,$rw);

        $productPreviewFileSql = "from tbl_product_preview_file where client_id={$clientId}";
        $this->executeSql($pgsqlDb,'tbl_product_preview_file',$productPreviewFileSql,$rw);

        //mysql 回收箱 delete_flag=2
        $recycleSql = "from tbl_recycle where client_id={$clientId} and type in (3,4)";
        $this->executeSql($mysqlDb,'tbl_recycle',$recycleSql,$rw);


        //供应商
        $supplierSql = "from tbl_supplier where client_id={$clientId}";
        $this->executeSql($pgsqlDb,'tbl_supplier',$supplierSql,$rw);

        $supplierContactSql = "from tbl_supplier_contact where client_id={$clientId}";
        $this->executeSql($pgsqlDb,'tbl_supplier_contact',$supplierContactSql,$rw);

        $supplierProductSql = "from tbl_supplier_product where client_id={$clientId}";
        $this->executeSql($pgsqlDb,'tbl_supplier_product',$supplierProductSql,$rw);

        $supplierHistorySql = "from tbl_supplier_history where client_id={$clientId}";
        $this->executeSql($pgsqlDb,'tbl_supplier_history',$supplierHistorySql,$rw);


        //采购订单
        $purchaseOrderSql = "from tbl_purchase_order where client_id={$clientId}";
        $this->executeSql($pgsqlDb,'tbl_purchase_order',$purchaseOrderSql,$rw);

        $purchaseOrderProductSql = "from tbl_purchase_order_product where client_id={$clientId}";
        $this->executeSql($pgsqlDb,'tbl_purchase_order_product',$purchaseOrderProductSql,$rw);

        $purchaseOrderHistorySql = "from tbl_purchase_order_history where client_id={$clientId}";
        $this->executeSql($pgsqlDb,'tbl_purchase_order_history',$purchaseOrderHistorySql,$rw);



        //销售订单
        $orderSql = "from tbl_order where client_id={$clientId}";
        $this->executeSql($pgsqlDb,'tbl_order',$orderSql,$rw);

        $invoiceProductRecordSql = "from tbl_invoice_product_record where client_id={$clientId} and type=2";
        $this->executeSql($pgsqlDb,'tbl_invoice_product_record',$invoiceProductRecordSql,$rw);

        $cashCollectionStatusSql = "from tbl_cash_collection_status where client_id={$clientId} and refer_type=1";
        $this->executeSql($pgsqlDb,'tbl_cash_collection_status',$cashCollectionStatusSql,$rw);

        $orderHistorySql = "from tbl_order_history where client_id={$clientId}";
        $this->executeSql($pgsqlDb,'tbl_order_history',$orderHistorySql,$rw);

        //阿里信保订单关系数据、信保订单的回款单关系表
        $alibabaOrderRelationSql = "from tbl_alibaba_order_relation where client_id={$clientId}";
        $this->executeSql($pgsqlDb,'tbl_alibaba_order_relation',$alibabaOrderRelationSql,$rw);

        $alibabaOrderFundRelationSql = "from tbl_alibaba_order_fund_relation where client_id={$clientId}";
        $this->executeSql($pgsqlDb,'tbl_alibaba_order_fund_relation',$alibabaOrderFundRelationSql,$rw);


        //mysql
        $notificationSql = "from tbl_notification where client_id={$clientId} and type in (501,502,503)";
        $this->executeSql($mysqlDb,'tbl_notification',$notificationSql,$rw);

    }

    // 删除订单、回款单相关信息
    // php ./yiic-test zhengbogiang orderAndCostDeleteData
    // ./yiic-omg zhengbogiang orderAndCostDeleteData
    public function actionOrderAndCostDeleteData()
    {
        $clientId = 5614;
        $userId   = 29372540;
        $clientId = 0;
        $pgsqlDb = \PgActiveRecord::getDbByClientId($clientId);
        $mysqlDb = \ProjectActiveRecord::getDbByClientId($clientId);
        $rw = 'read';

        // 审批相关 tbl_refer_lock mysql
        $referLockSql = "from tbl_refer_lock where client_id={$clientId} and lock_flag = 1 and refer_type in (2,10)";
        $this->executeSql($mysqlDb,'tbl_refer_lock', $referLockSql, $rw);
        $approvalNodeSql = "from tbl_approval_flow_approval_node where apply_form_id in (select apply_form_id from tbl_approval_flow_apply_form where client_id={$clientId} and refer_type in (2, 10))";
        $this->executeSql($mysqlDb,'tbl_approval_flow_approval_node', $approvalNodeSql, $rw);
        $applyFormSql = "from tbl_approval_flow_apply_form where client_id={$clientId} and refer_type in (2, 10)";
        $this->executeSql($mysqlDb,'tbl_approval_flow_apply_form', $applyFormSql, $rw);
        $approvalFormSql = "from tbl_approval_flow_approval_form where client_id={$clientId} and refer_type in (2, 10)";
        $this->executeSql($mysqlDb,'tbl_approval_flow_approval_form', $approvalFormSql, $rw);


        //回款单 关联订单的
        $cashCollectionHistorySql = "from tbl_cash_collection_history where client_id={$clientId} and cash_collection_id in (select cash_collection_id from tbl_cash_collection where client_id={$clientId} and refer_type=1)";
        $this->executeSql($pgsqlDb,'tbl_cash_collection_history', $cashCollectionHistorySql, $rw);
        $cashCollectionSql = "from tbl_cash_collection where client_id={$clientId} and refer_type=1";

        $enableCashCollectionIdSql = 'select cash_collection_id ' . $cashCollectionSql .' and enable_flag=1';
        $cashCollectionIds = $pgsqlDb->createCommand($enableCashCollectionIdSql)->queryColumn();

        $cashCollectionIds = array_chunk($cashCollectionIds, 500);
        foreach ($cashCollectionIds as $cashCollectionIdList) {
            if ($rw == 'delete' && $cashCollectionIdList) {
                self::info('pushCashCollectionQueue 数据为:' . count($cashCollectionIdList));
                SearchQueueService::pushCashCollectionQueue($userId, $clientId, $cashCollectionIdList, \Constants::CASH_COLLECTION_INDEX_TYPE_DELETE);
            }
        }
        $this->executeSql($pgsqlDb,'tbl_cash_collection', $cashCollectionSql, $rw);


        //销售订单
        $orderSql = "from tbl_order where client_id={$clientId}";
        $enableOrderIdSql = 'select order_id ' . $orderSql . ' and enable_flag=1';
        $orderIds = $pgsqlDb->createCommand($enableOrderIdSql)->queryColumn();
        $orderIds = array_chunk($orderIds, 500);
        foreach ($orderIds as $orderIdList) {
            if ($rw == 'delete' && $orderIdList) {
                self::info('pushOrderQueue 数据为:' . count($orderIdList));
                \common\library\server\es_search\SearchQueueService::pushOrderQueue($userId, $clientId, $orderIdList, \Constants::ORDER_INDEX_TYPE_DELETE);
            }
        }

        $this->executeSql($pgsqlDb,'tbl_order',$orderSql,$rw);

        $invoiceProductRecordSql = "from tbl_invoice_product_record where client_id={$clientId} and type=2";
        $this->executeSql($pgsqlDb,'tbl_invoice_product_record',$invoiceProductRecordSql,$rw);

        $cashCollectionStatusSql = "from tbl_cash_collection_status where client_id={$clientId} and refer_type=1";
        $this->executeSql($pgsqlDb,'tbl_cash_collection_status',$cashCollectionStatusSql,$rw);

        $orderHistorySql = "from tbl_order_history where client_id={$clientId}";
        $this->executeSql($pgsqlDb,'tbl_order_history',$orderHistorySql,$rw);

        //阿里信保订单关系数据、信保订单的回款单关系表
        $alibabaOrderRelationSql = "from tbl_alibaba_order_relation where client_id={$clientId}";
        $this->executeSql($pgsqlDb,'tbl_alibaba_order_relation',$alibabaOrderRelationSql,$rw);

        $alibabaOrderFundRelationSql = "from tbl_alibaba_order_fund_relation where client_id={$clientId}";
        $this->executeSql($pgsqlDb,'tbl_alibaba_order_fund_relation',$alibabaOrderFundRelationSql,$rw);

        //消息通知mysql
        $notificationSql = "from tbl_notification where client_id={$clientId} and type in (501,502,503,511,512,513)";
        $this->executeSql($mysqlDb,'tbl_notification',$notificationSql,$rw);

        //绩效数据
        $performanceSql = "from tbl_performance_v2_record where client_id={$clientId} and refer_type in (2, 10)";
        $this->executeSql($pgsqlDb,'tbl_performance_v2_record',$performanceSql,$rw);
    }


    protected function executeSql($db,string $tbl_name,string $sql,string $rw='read')
    {
        $readSql   = "select count(*) ".$sql;
        $deleteSql = "delete " . $sql;
        $count = $db->createCommand($readSql)->queryScalar();
        self::info($tbl_name . '数据为:' . $count);

        //echo $readSql.PHP_EOL;
        echo $deleteSql.PHP_EOL;

        if ($rw == 'delete' ){
            //读取到数据才进行删除
            if ($count) {
                $deleteSql = "delete " . $sql;
                $deleteCount = $db->createCommand($deleteSql)->execute();
                self::info($tbl_name . '删除数据量为:' . $deleteCount);
            }
        }

    }

    public function actionPurchaseCountOrderCount()
    {
        $dbList = $this->dbSetList(3);

        $dbNum = 0;
        $dbCount = count($dbList);
        echo  'to do ',$dbCount,'...',PHP_EOL;
        foreach ($dbList as $dbItem)
        {
            $echoMap = [];
            $dbNum++;
            $setId = $dbItem['set_id'];
            if ($setId <= 9102) {
                continue;
            }
            if ($setId > 10000 && $setId < 10005) {
                continue;
            }
            echo 'doing  set id ',$setId,PHP_EOL;
            $db = \PgActiveRecord::getDbByDbSetId($setId);
            $sql = "select a.client_id,a.transfer_invoice_id from tbl_product_transfer_record as a
left join tbl_invoice_product_record as b
on a.sub_refer_id=b.id
where a.type=38 and a.delete_flag=0 and a.refer_type=2 and a.reach_count>0
and  a.reach_count::numeric!=b.count::numeric
group by a.client_id,a.transfer_invoice_id";
            $list = $db->createCommand($sql)->queryAll();
            $clientIds = array_column($list, 'client_id');
            $echoMap = array_combine($clientIds, array_fill(0, count($clientIds), []));
            foreach ($list as $item) {
                $echoMap[$item['client_id']][] = $item['transfer_invoice_id'];
            }

            foreach ($echoMap as $echoClientID => $echoItem) {
                echo "clientId:{$echoClientID}~~~;transfer_invoice_count: " . count($echoItem) . "~~~;transfer_invoice_ids:" . json_encode($echoItem) . PHP_EOL;
            }

            \PgActiveRecord::releaseDbBySetId($setId);
        }
    }
}
