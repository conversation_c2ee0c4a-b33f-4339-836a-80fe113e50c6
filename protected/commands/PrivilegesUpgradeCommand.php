<?php
/**
 * Created by PhpStorm.
 * User: Tony
 * Date: 18/4/9
 * Time: 下午2:08
 */

use common\library\account\Client;
use common\library\privilege_v2\PrivilegeFormatter;
use common\models\base\PrivilegeRole as PrivilegeRoleModel;
use common\models\base\PrivilegeRoleAccess as PrivilegeRoleAccessModel;
use common\library\privilege_v2\PrivilegeConstants;
use common\library\privilege_v2\PrivilegePermission;
use common\library\privilege_v2\PrivilegeCache;
use common\library\privilege_v2\ClientPermission;
use common\library\privilege_v2\PrivilegeRole;

class PrivilegesUpgradeCommand extends CrontabCommand
{
    use PrivilegeFormatter;
    private $limit = 100;

    public function actionRun()
    {
        $this->InitRoles();
        $this->systemPrivilege();
        $this->clientPrivilege();
        $this->userPrivilege();
    }

    private function systemPrivilege()
    {
        $db = \Yii::app()->db;
        PrivilegeClientSystem::setConnection($db);
        $redis = \common\library\privilege_v2\CacheObject::getCache();
        $today = date('Y-m-d');
        $criteria = new CDbCriteria;
        $criteria->condition = "create_time >= :today OR update_time >= :today";
        $criteria->params = [':today' => $today];

        $total = PrivilegeClientSystem::model()->count($criteria);
        echo 'systemPrivilege, ', $total, PHP_EOL;

        $command = \Yii::app()->account_base_db->createCommand();

        $max = ceil($total / $this->limit);
        for ($i = 0; $i <= $max; $i++) {
            echo 'offset = ', $i * $this->limit, PHP_EOL;
            $criteria = new CDbCriteria;
            $criteria->condition = "create_time >= :today OR update_time >= :today";
            $criteria->params = [':today' => $today];
            $criteria->limit = $this->limit;
            $criteria->offset = $i * $this->limit;
            $list = PrivilegeClientSystem::model()->findAll($criteria);
            $list = $this->formatter($list);

            foreach ($list as $item) {
                try {
                    $command->insert('tbl_privilege_client_system', [
                        'client_id' => $item['client_id'],
                        'enable_flag' => $item['enable_flag'],
                        'system_id' => $item['system_id'],
                        'create_time' => $item['create_time'],
                        'is_paid' => $item['is_paid'],
                        'update_time' => $item['update_time']
                    ]);
                } catch (Exception $e) {
                    $command->update('tbl_privilege_client_system', [
                        'enable_flag' => $item['enable_flag'],
                        'is_paid' => $item['is_paid'],
                        'update_time' => $item['update_time']
                    ], 'client_id=:client_id and system_id=:system_id', [
                        ':client_id' => $item['client_id'],
                        ':system_id' => $item['system_id']
                    ]);
                }

                $redis->hdel("crm:table:privilege_client:system:{{$item['client_id']}}", [$item['client_id']]);
            }
        }
    }

    private function clientPrivilege()
    {
        $db = \Yii::app()->db;
        ClientPrivilegeModel::setConnection($db);
        $today = date('Y-m-d');
        $criteria = new CDbCriteria;
        $criteria->condition = "create_time >= :today OR update_time >= :today";
        $criteria->params = [':today' => $today];
        $total = ClientPrivilegeModel::model()->count($criteria);
        echo 'clientPrivilege, ', $total, PHP_EOL;

        $command = \Yii::app()->account_base_db->createCommand();

        $max = ceil($total / $this->limit);
        for ($i = 0; $i <= $max; $i++) {
            echo 'offset = ', $i * $this->limit, PHP_EOL;
            $criteria = new CDbCriteria;
            $criteria->condition = "create_time >= :today OR update_time >= :today";
            $criteria->params = [':today' => $today];
            $criteria->limit = $this->limit;
            $criteria->offset = $i * $this->limit;

            $list = ClientPrivilegeModel::model()->findAll($criteria);
            $list = $this->formatter($list);
            foreach ($list as $item) {
                if (empty($item['privilege'])) {
                    continue;
                }

                try {
                    $command->insert('tbl_client_privilege', [
                        'client_id' => $item['client_id'],
                        'privilege' => $item['privilege'],
                        'system_id' => $item['system_id'],
                        'enable_flag' => $item['enable_flag'],
                        'create_time' => $item['create_time'],
                        'update_time' => $item['update_time'],
                        'update_user' => $item['update_user']
                    ]);
                } catch (Exception $e) {
                    $command->update('tbl_client_privilege', [
                        'enable_flag' => $item['enable_flag'],
                        'update_time' => $item['update_time'],
                        'update_user' => $item['update_user']
                    ], 'client_id=:client_id and privilege=:privilege and system_id=:system_id', [
                        ':client_id' => $item['client_id'],
                        ':privilege' => $item['privilege'],
                        ':system_id' => $item['system_id']
                    ]);
                }

                PrivilegeCache::hDel("crm:table:privilege_client:client:{{$item['client_id']}}", $item['client_id']);

            }
        }

    }

    private function userPrivilege()
    {
        $db = \Yii::app()->db;
        UserPrivilege::setConnection($db);
        $today = date('Y-m-d');
        $criteria = new CDbCriteria;
        $criteria->condition = "create_time >= :today OR update_time >= :today";
        $criteria->params = [':today' => $today];
        $total = UserPrivilege::model()->count($criteria);
        echo 'userPrivilege,', $total, PHP_EOL;

        $command = \Yii::app()->account_base_db->createCommand();
        $except = [
            'mk.base',
            'mk.edm',
            'mk.page',
            'mk.sns',
            PrivilegeConstants::PRIVILEGE_CRM_ADMIN,
            PrivilegeConstants::PRIVILEGE_CRM_USER,
            PrivilegeConstants::PRIVILEGE_DX_ADMIN,
            PrivilegeConstants::PRIVILEGE_DX_USER,
            PrivilegeConstants::PRIVILEGE_MK_ADMIN,
            PrivilegeConstants::PRIVILEGE_MK_USER,
        ];

        $special = [
            PrivilegeConstants::PRIVILEGE_CRM_ADMIN,
            PrivilegeConstants::PRIVILEGE_CRM_USER,
            PrivilegeConstants::PRIVILEGE_DX_ADMIN,
            PrivilegeConstants::PRIVILEGE_DX_USER,
            PrivilegeConstants::PRIVILEGE_MK_ADMIN,
            PrivilegeConstants::PRIVILEGE_MK_USER,
        ];

        $roleList = [
            PrivilegeConstants::PRIVILEGE_CRM_ADMIN => PrivilegeConstants::ROLE_CRM_ADMIN,
            PrivilegeConstants::PRIVILEGE_CRM_USER => PrivilegeConstants::ROLE_CRM_USER,
            PrivilegeConstants::PRIVILEGE_DX_ADMIN => PrivilegeConstants::ROLE_DX_ADMIN,
            PrivilegeConstants::PRIVILEGE_DX_USER => PrivilegeConstants::ROLE_DX_USER,
            PrivilegeConstants::PRIVILEGE_MK_ADMIN => PrivilegeConstants::ROLE_MK_ADMIN,
            PrivilegeConstants::PRIVILEGE_MK_USER => PrivilegeConstants::ROLE_MK_USER,
            PrivilegeConstants::PRIVILEGE_LAB_MAIL_CLASSIFY => PrivilegeConstants::ROLE_LAB_MAIL_CLASSIFY,
            PrivilegeConstants::PRIVILEGE_LAB_MAIL_READ => PrivilegeConstants::ROLE_LAB_MAIL_VIEW,
            PrivilegeConstants::PRIVILEGE_LAB_SENTIMENT => PrivilegeConstants::ROLE_LAB_MAIL_SENTIMENT,
            PrivilegeConstants::PRIVILEGE_ACCOUNT_DISABLED => PrivilegeConstants::ROLE_ACCOUNT_DISABLED,
        ];

        $max = ceil($total / $this->limit);
        for ($i = 0; $i <= $max; $i++) {
            echo 'offset = ', $i * $this->limit, PHP_EOL;
            $criteria = new CDbCriteria;
            $criteria->condition = "create_time >= :today OR update_time >= :today";
            $criteria->params = [':today' => $today];
            $criteria->limit = $this->limit;
            $criteria->offset = $i * $this->limit;

            $list = UserPrivilege::model()->findAll($criteria);
            $list = $this->formatter($list);

            foreach ($list as $item) {
                if (empty($item['privilege'])) {
                    continue;
                }

                try {
                    if (isset($roleList[$item['privilege']])) {
                        if ($item['privilege'] == PrivilegeConstants::PRIVILEGE_CRM_USER && $item['enable_flag'] == 2) {
                            PrivilegePermission::getInstance($item['user_id'])->assignRoleUser(
                                $roleList[PrivilegeConstants::PRIVILEGE_ACCOUNT_DISABLED],
                                [$item['user_id']],
                                false
                            );
                        } elseif ($item['enable_flag'] == 1) {
                            PrivilegePermission::getInstance($item['user_id'])->assignRoleUser(
                                $roleList[$item['privilege']],
                                [$item['user_id']],
                                false
                            );
                        } elseif ($item['enable_flag'] == 0) {
                            PrivilegePermission::getInstance($item['user_id'])->removeRoleUser(
                                $roleList[$item['privilege']],
                                [$item['user_id']],
                                false
                            );
                        }

                        if (in_array($item['privilege'], $special)) {
                            try {
                                $command->insert('tbl_user_privilege', [
                                    'client_id' => $item['client_id'],
                                    'user_id' => $item['user_id'],
                                    'privilege' => $item['privilege'],
                                    'enable_flag' => $item['enable_flag'],
                                    'create_time' => $item['create_time'],
                                    'create_user' => $item['create_user'],
                                    'update_time' => $item['update_time']
                                ]);
                            } catch (Exception $e) {
                                $command->update('tbl_user_privilege', [
                                    'enable_flag' => $item['enable_flag'],
                                    'update_time' => $item['update_time'],
                                ], 'client_id=:client_id and user_id=:user_id and privilege=:privilege', [
                                    ':client_id' => $item['client_id'],
                                    ':user_id' => $item['user_id'],
                                    ':privilege' => $item['privilege']
                                ]);
                            }
                        }

                    } else {

                        if (in_array($item['privilege'], $except)) {
                            continue;
                        }

                        try {
                            $command->insert('tbl_user_privilege', [
                                'client_id' => $item['client_id'],
                                'user_id' => $item['user_id'],
                                'privilege' => $item['privilege'],
                                'enable_flag' => $item['enable_flag'],
                                'create_time' => $item['create_time'],
                                'create_user' => $item['create_user'],
                                'update_time' => $item['update_time']
                            ]);
                        } catch (Exception $e) {
                            $command->update('tbl_user_privilege', [
                                'enable_flag' => $item['enable_flag'],
                                'update_time' => $item['update_time'],
                            ], 'client_id=:client_id and user_id=:user_id and privilege=:privilege', [
                                ':client_id' => $item['client_id'],
                                ':user_id' => $item['user_id'],
                                ':privilege' => $item['privilege']
                            ]);
                        }

                    }

                    PrivilegeCache::hDel("crm:table:privilege_user:enabled:{{$item['client_id']}}", $item['user_id']);
                } catch (Exception $e) {

                }
            }
        }
    }

    private function initRoles()
    {
        $roles = [
            [
                'role_id' => PrivilegeConstants::ROLE_CRM_ADMIN,
                'role_name' => '超级管理员',
                'description' => '超级管理员可以查看系统所有数据，协助完成所有设置',
                'system_id' => 'crm',
                'privilege_id' => 'crm.admin',
                'disable_flag' => PrivilegeConstants::ENABLE_FLAG_FALSE,
                'scope' => PrivilegeConstants::PRIVILEGE_SCOPE_DIRECTOR
            ],
            [
                'role_id' => PrivilegeConstants::ROLE_CRM_USER,
                'role_name' => '系统用户',
                'description' => '系统基础访问者，可以查看个人数据，完成本职工作',
                'system_id' => 'crm',
                'privilege_id' => 'crm.user',
                'disable_flag' => PrivilegeConstants::ENABLE_FLAG_FALSE,
                'scope' => PrivilegeConstants::PRIVILEGE_SCOPE_OWNER
            ],
            [
                'role_id' => PrivilegeConstants::ROLE_DX_ADMIN,
                'role_name' => '小满发现管理员',
                'description' => '管理员可以查看系统所有数据，协助完成所有设置',
                'system_id' => 'dx',
                'privilege_id' => 'dx.admin',
                'disable_flag' => PrivilegeConstants::ENABLE_FLAG_FALSE,
                'scope' => PrivilegeConstants::PRIVILEGE_SCOPE_DIRECTOR
            ],
            [
                'role_id' => PrivilegeConstants::ROLE_DX_USER,
                'role_name' => '小满发现用户',
                'description' => '系统基础访问者',
                'system_id' => 'dx',
                'privilege_id' => 'dx.user',
                'disable_flag' => PrivilegeConstants::ENABLE_FLAG_FALSE,
                'scope' => PrivilegeConstants::PRIVILEGE_SCOPE_OWNER
            ],
            [
                'role_id' => PrivilegeConstants::ROLE_MK_ADMIN,
                'role_name' => '小满营销管理员',
                'description' => '管理员可以查看系统所有数据，协助完成所有设置',
                'system_id' => 'mk',
                'privilege_id' => 'mk.admin',
                'disable_flag' => PrivilegeConstants::ENABLE_FLAG_FALSE,
                'scope' => PrivilegeConstants::PRIVILEGE_SCOPE_DIRECTOR
            ],
            [
                'role_id' => PrivilegeConstants::ROLE_MK_USER,
                'role_name' => '小满营销用户',
                'description' => '系统基础访问者',
                'system_id' => 'mk',
                'privilege_id' => 'mk.user',
                'disable_flag' => PrivilegeConstants::ENABLE_FLAG_FALSE,
                'scope' => PrivilegeConstants::PRIVILEGE_SCOPE_OWNER
            ],
            [
                'role_id' => PrivilegeConstants::ROLE_LAB_MAIL_CLASSIFY,
                'role_name' => '智能邮件分类',
                'description' => 'X-Lab 智能邮件分类使用权限角色',
                'system_id' => 'crm',
                'privilege_id' => 'crm.lab.mail.classify',
                'disable_flag' => PrivilegeConstants::ENABLE_FLAG_TRUE,
                'scope' => PrivilegeConstants::PRIVILEGE_SCOPE_OWNER
            ],
            [
                'role_id' => PrivilegeConstants::ROLE_LAB_MAIL_VIEW,
                'role_name' => '智能邮件阅读',
                'description' => 'X-Lab 智能邮件阅读使用权限角色',
                'system_id' => 'crm',
                'privilege_id' => 'crm.lab.mail.read',
                'disable_flag' => PrivilegeConstants::ENABLE_FLAG_TRUE,
                'scope' => PrivilegeConstants::PRIVILEGE_SCOPE_OWNER
            ],
            [
                'role_id' => PrivilegeConstants::ROLE_LAB_MAIL_SENTIMENT,
                'role_name' => '智能邮件情绪应用',
                'description' => 'X-Lab 智能邮件情绪应用使用权限角色',
                'system_id' => 'crm',
                'privilege_id' => 'crm.lab.mail.sentiment',
                'disable_flag' => PrivilegeConstants::ENABLE_FLAG_TRUE,
                'scope' => PrivilegeConstants::PRIVILEGE_SCOPE_OWNER
            ],
            [
                'role_id' => PrivilegeConstants::ROLE_ACCOUNT_DISABLED,
                'role_name' => '账号禁用',
                'description' => '设置用户账号禁用状态',
                'system_id' => 'crm',
                'privilege_id' => 'crm.account.disabled',
                'disable_flag' => PrivilegeConstants::ENABLE_FLAG_TRUE,
                'scope' => PrivilegeConstants::PRIVILEGE_SCOPE_OWNER
            ],
            [
                'role_id' => 101,
                'role_name' => '测试专用角色',
                'description' => '测试专用角色',
                'system_id' => 'crm',
                'privilege_id' => 'crm.tony.test.case1',
                'disable_flag' => PrivilegeConstants::ENABLE_FLAG_TRUE,
                'scope' => PrivilegeConstants::PRIVILEGE_SCOPE_OWNER
            ]
        ];

        echo 'initRoles, ', count($roles), PHP_EOL;

        $i = 1;
        foreach ($roles as $item) {
            $object = PrivilegeRoleModel::model()->find('role_id=:role_id', [':role_id' => $item['role_id']]);

            if ($object) {
                $object->role_name = $item['role_name'];
                $object->description = $item['description'];
                $object->system_id = $item['system_id'];
                $object->disable_flag = $item['disable_flag'];
                $object->enable_flag = PrivilegeConstants::ENABLE_FLAG_TRUE;
                $object->client_id = 0;
                $object->base = 1;
                $object->update_time = date('Y-m-d H:i:s');
            } else {
                $object = new PrivilegeRoleModel();
                $object->role_id = $item['role_id'];
                $object->role_name = $item['role_name'];
                $object->description = $item['description'];
                $object->system_id = $item['system_id'];
                $object->disable_flag = $item['disable_flag'];
                $object->enable_flag = PrivilegeConstants::ENABLE_FLAG_TRUE;
                $object->create_time = date('Y-m-d H:i:s');
                $object->client_id = 0;
                $object->base = 1;
                $object->list_order = $i;
            }

            if ($object->save() && in_array($item['role_id'], [101, 7, 8, 9])) {
                $model = PrivilegeRoleAccessModel::model()->find('role_id=:role_id and privilege=:privilege', [
                    ':role_id' => $item['role_id'],
                    ':privilege' => $item['privilege_id']
                ]);

                if ($model) {
                    $model->enable_flag = PrivilegeConstants::ENABLE_FLAG_TRUE;
                    $model->scope = $item['scope'];
                    $model->update_time = date('Y-m-d H:i:s');
                } else {
                    $model = new PrivilegeRoleAccessModel();
                    $model->role_id = $item['role_id'];
                    $model->privilege = $item['privilege_id'];
                    $model->scope = $item['scope'];
                    $model->enable_flag = PrivilegeConstants::ENABLE_FLAG_TRUE;
                    $model->create_time = date('Y-m-d H:i:s');
                }

                $model->save();
            }

            $i++;
        }
    }

    public function actionFixUser()
    {
        $db = \Yii::app()->db;

        $command = \Yii::app()->account_base_db->createCommand();

        $clientIds = [55,147,372,439,633,759,791,804,839,840,853,909,1022,1071,1095,1228,1251,1330,1470,1490,1565,1701,1942,1954,1970,2200,2272,2398,2492,2526,2536,2541,2586,2786,2813,2992,3033,3122,3300,3432,3433,3646,3763,3790,3922,3955,4136,4168,4286,4390,4666,5005,5149,5396,5476,5519,5705,6517,6582,7226,7504,7588,7830,7960,8003,8243,8568,8574,8791,9984,9985,10026,10728,10863,10994];

        foreach ($clientIds as $client_id) {

            $userIds = \common\library\privilege_v2\ClientPermission::getInstance($client_id)->getClientUserIdsByVersion('crm_plus');

            foreach ($userIds as $user_id) {
                try {
                    $permission = PrivilegePermission::getInstance($user_id);
                } catch (Exception $e) {
                    continue;
                }

                if (
                    !(
                        $permission->checkRolesAuth([PrivilegeConstants::PRIVILEGE_CRM_ADMIN, PrivilegeConstants::PRIVILEGE_CRM_USER])
                        &&
                        $permission->checkRolesAuth([PrivilegeConstants::PRIVILEGE_DX_ADMIN, PrivilegeConstants::PRIVILEGE_DX_USER])
                        &&
                        $permission->checkRolesAuth([PrivilegeConstants::PRIVILEGE_MK_ADMIN, PrivilegeConstants::PRIVILEGE_MK_USER])
                    )
                ) {
                    echo "client_id = {$client_id}, user_id = {$user_id}";

                    if (! $permission->checkRolesAuth([PrivilegeConstants::PRIVILEGE_CRM_ADMIN, PrivilegeConstants::PRIVILEGE_CRM_USER])) {
                        $permission->assignRoleUser(PrivilegeConstants::ROLE_CRM_USER, [$user_id]);
                        echo ', crm_user';
                    }

                    if (! $permission->checkRolesAuth([PrivilegeConstants::PRIVILEGE_DX_ADMIN, PrivilegeConstants::PRIVILEGE_DX_USER])) {
                        $permission->assignRoleUser(PrivilegeConstants::ROLE_DX_USER, [$user_id]);
                        echo ', dx_user';
                    }

                    if (! $permission->checkRolesAuth([PrivilegeConstants::PRIVILEGE_MK_ADMIN, PrivilegeConstants::PRIVILEGE_MK_USER])) {
                        $permission->assignRoleUser(PrivilegeConstants::ROLE_MK_USER, [$user_id]);
                        echo ', mk_user';
                    }

                    echo PHP_EOL;
                }
            }
        }
    }

    public function actionTransferPrivilege($clientId){
        \ClientPrivilegeModel::setConnection(Yii::app()->account_base_db);
        $client = Client::getClient($clientId);
        $masterUser = $client->getMasterUser();

        if($masterUser->isEmpty()){
            self::info("masterUser not exists! client_id: $clientId");
            return;
        }

        $privilegePermission = PrivilegePermission::getInstance($masterUser->getUserId());

        //未拥有crm权限忽略
        $clientSystems = $privilegePermission->getClientSystems();
        $clientSystems = array_column($clientSystems,'system_id');
        if(!in_array(PrivilegeConstants::V5CLIENT_SYSTEM_ID, $clientSystems)){
            self::info("system crm not exists! client_id: $clientId");
            return;
        }

        //删除旧版edm权限
        $privilegePermission->removeClientPrivilege($clientId, PrivilegeConstants::PRIVILEGE_EDM_OLD);

        //删除crm.functional.edm.crm
        $clientPrivileges = $privilegePermission->getClientAccessPrivileges();
        if(!empty($clientPrivileges) && !in_array('crm.functional.edm.crm', $clientPrivileges)){
            self::info("privilege edm exists! client_id: $clientId");
            return;
        }

        $privilegePermission->removeClientPrivilege($clientId, 'crm.functional.edm.crm');
    }

    public function actionTransferPrivilegeDispatcher($lastNumber = null){
        $clientList = $this->getGreyClientIds($lastNumber);
        $num = 1;
        $clientCount = count($clientList);
        foreach ($clientList as $clientId) {
            echo "$clientId $num/$clientCount \n";
            $num++;
            try{
                $this->actionTransferPrivilege($clientId);
            }catch (Exception $exception){
                echo $exception->getMessage();
                continue;
            }
        }
    }

    /**
     * 旧权限系统相关表同步，过渡期
     *
     * @param string $method
     * @param CActiveRecord $model
     * @param array $where
     * @param array $update
     * @return bool
     */
    private function permissionOldSyncUpdate(string $method, \CActiveRecord $model, array $where, array $update)
    {
        $whereParams = $params = [];

        $privilegeFormatter = $this->getFlipConfigList();

        foreach ($where as $key => $value) {
            if (in_array($key, ['privilege_id', 'privilege'])) {
                $where[$key] = $privilegeFormatter[$value] ?? $value;
            }
        }

        foreach ($update as $key => $value) {
            if (in_array($key, ['privilege_id', 'privilege'])) {
                $update[$key] = $privilegeFormatter[$value] ?? $value;
            }
        }

        foreach ($where as $key => $value) {
            $whereParams[] = "{$key}=:{$key}";
        }

        foreach ($where as $key => $value) {
            $params[":{$key}"] = $value;
        }

        $object = $model->find(implode(' and ', $whereParams), $params);

        if ($object) {
            if ($method == 'add') {
                foreach ($update as $key => $value) {
                    $object->$key = $value;
                }
                $object->enable_flag = PrivilegeConstants::ENABLE_FLAG_TRUE;
            } elseif ($method == 'remove') {
                $object->enable_flag = PrivilegeConstants::ENABLE_FLAG_FALSE;
            }

            $object->update_time = date('Y-m-d H:i:s');
        } elseif ($method == 'add') {
            $class = "\\" . get_class($model);
            $object = new $class();
            foreach ($update as $key => $value) {
                $object->$key = $value;
            }
            $object->enable_flag = PrivilegeConstants::ENABLE_FLAG_TRUE;
            $object->create_time = date('Y-m-d H:i:s');
        }

        if ($object) {
            return $object->save();
        }

        return false;
    }

    /**
     * 给有效期内的所有客户公司账号，创建部门管理员角色
     */
    public function actionDepartmentManagerRun()
    {
        ini_set("memory_limit", "8096M");
        $now  = date('Y-m-d H:i:s');
        $condition = " enable_flag = 1 AND valid_to >'$now'";
        $clientList = \Client::model()->findAll($condition);

        $roles = [
            [
                'role_name' => '部门管理员',
                'description' => '部门管理员可以查看部门所有数据，协助完成所有设置',
                'system_id' => 'crm',
                'disable_flag' => PrivilegeConstants::ENABLE_FLAG_FALSE,
                'scope' => PrivilegeConstants::PRIVILEGE_SCOPE_MANAGER,
                'list_order' => 1
            ],
            [
                'role_name' => '普通用户',
                'description' => '普通用户可以查看个人的数据',
                'system_id' => 'crm',
                'disable_flag' => PrivilegeConstants::ENABLE_FLAG_FALSE,
                'scope' => PrivilegeConstants::PRIVILEGE_SCOPE_OWNER,
                'list_order' => 2
            ],
        ];

        $privileges = [
            PrivilegeConstants::PRIVILEGE_CRM_LEAD_VIEW,
            PrivilegeConstants::PRIVILEGE_CRM_LEAD_CREATE,
            PrivilegeConstants::PRIVILEGE_CRM_LEAD_REMOVE,
            PrivilegeConstants::PRIVILEGE_CRM_LEAD_EDIT,
            PrivilegeConstants::PRIVILEGE_CRM_LEAD_TRANSFER,
            PrivilegeConstants::PRIVILEGE_CRM_LEAD_REPORT,
            PrivilegeConstants::PRIVILEGE_CRM_LEAD_MOVE_POOL,
            PrivilegeConstants::PRIVILEGE_CRM_LEAD_POOL_VIEW,
            PrivilegeConstants::PRIVILEGE_CRM_LEAD_POOL_CREATE,
            PrivilegeConstants::PRIVILEGE_CRM_LEAD_POOL_REMOVE,
            PrivilegeConstants::PRIVILEGE_CRM_LEAD_POOL_EDIT,
            PrivilegeConstants::PRIVILEGE_CRM_LEAD_POOL_TRANSFER,
            PrivilegeConstants::PRIVILEGE_CRM_OPPORTUNITY_VIEW,
            PrivilegeConstants::PRIVILEGE_CRM_OPPORTUNITY_CREATE,
            PrivilegeConstants::PRIVILEGE_CRM_OPPORTUNITY_REMOVE,
            PrivilegeConstants::PRIVILEGE_CRM_OPPORTUNITY_EDIT,
            PrivilegeConstants::PRIVILEGE_CRM_OPPORTUNITY_EDIT,
            PrivilegeConstants::PRIVILEGE_CRM_OPPORTUNITY_TRANSFER,
            PrivilegeConstants::PRIVILEGE_CRM_OPPORTUNITY_MEMBER_MANAGE,
            PrivilegeConstants::PRIVILEGE_CRM_OPPORTUNITY_STATISTIC,
            PrivilegeConstants::PRIVILEGE_CRM_OPPORTUNITY_TRAIL_CREATE,
        ];

        foreach ($clientList as $item) {
            try {
                $admin_user_id = \common\library\privilege_v3\PrivilegeService::getInstance($item['client_id'])->getAdminUserId();
                $roleModel = new PrivilegeRole(User::getUserObject($admin_user_id));

                foreach ($roles as $role) {
                    if (! $role_id = $roleModel->isExistsRole(\Yii::t('privilege', $role['role_name']))) {
                        $object = new PrivilegeRoleModel();
                        $object->role_name = $role['role_name'];
                        $object->description = $role['description'];
                        $object->system_id = $role['system_id'];
                        $object->disable_flag = $role['disable_flag'];
                        $object->enable_flag = PrivilegeConstants::ENABLE_FLAG_TRUE;
                        $object->create_time = date('Y-m-d H:i:s');
                        $object->client_id = $item['client_id'];
                        $object->base = PrivilegeConstants::ROLE_BASE_USER;
                        $object->list_order = $role['list_order'];
                        $object->save();

                        $role_id = $object->role_id;
                    }

                    if (!empty($role_id)) {
                        foreach ($privileges as $privilege) {
                            $model = PrivilegeRoleAccessModel::model()->find(
                                'role_id=:role_id and privilege=:privilege', [
                                ':role_id' => $role_id,
                                ':privilege' => $privilege
                            ]);

                            if ($model) {
                                $model->enable_flag = PrivilegeConstants::ENABLE_FLAG_TRUE;
                                $model->scope = $role['scope'];
                                $model->update_time = date('Y-m-d H:i:s');
                            } else {
                                $model = new PrivilegeRoleAccessModel();
                                $model->role_id = $role_id;
                                $model->privilege = $privilege;
                                $model->scope = $role['scope'];
                                $model->enable_flag = PrivilegeConstants::ENABLE_FLAG_TRUE;
                                $model->create_time = date('Y-m-d H:i:s');
                            }

                            $model->save();
                        }

                        self::info("client_id = {$item['client_id']}, role_id={$role_id} created success.");
                    }
                }

            } catch (Exception $e) {
                self::info("client_id = {$item['client_id']}, user_id={$admin_user_id} error.");
                self::info($e->getMessage());
            }
        }
    }

    /**
     * 给有效期内的所有客户公司账号，符合部门管理员要求的所有用户账号绑定部门管理员角色权限
     */
    public function actionDepartmentManagerAssignUserRun()
    {
        ini_set("memory_limit", "8096M");
        $now = date('Y-m-d H:i:s');
        $condition = " enable_flag = 1 AND valid_to >'$now'";
        $clientList = \Client::model()->findAll($condition);

        $roles = [
            [
                'role_name' => '部门管理员',
                'description' => '部门管理员可以查看部门所有数据，协助完成所有设置',
                'system_id' => 'crm',
                'disable_flag' => PrivilegeConstants::ENABLE_FLAG_FALSE,
                'scope' => PrivilegeConstants::PRIVILEGE_SCOPE_MANAGER,
                'list_order' => 1
            ],
            [
                'role_name' => '普通用户',
                'description' => '普通用户可以查看个人的数据',
                'system_id' => 'crm',
                'disable_flag' => PrivilegeConstants::ENABLE_FLAG_FALSE,
                'scope' => PrivilegeConstants::PRIVILEGE_SCOPE_OWNER,
                'list_order' => 2
            ],
        ];

        foreach ($clientList as $item) {
            try {
                $admin_user_id = \common\library\privilege_v3\PrivilegeService::getInstance($item['client_id'])->getAdminUserId();
                $roleModel = new PrivilegeRole(User::getUserObject($admin_user_id));

                foreach ($roles as $role) {
                    if ($role_id = $roleModel->isExistsRole(\Yii::t('privilege', $role['role_name']))) {
                        if ($role['role_name'] === '部门管理员') {
                            $this->DepartmentManagerAssignUser($role_id, $item['client_id']);
                        } else {
                            $this->NormalAssignUser($role_id, $item['client_id']);
                        }
                    } else {
                        self::info("client_id = {$item['client_id']}, role_id={$role_id} "
                            ."can't find {$role['role_name']}.");
                    }
                }


            } catch (Exception $e) {
                self::info("client_id = {$item['client_id']}, user_id={$admin_user_id} error.");
                self::info($e->getMessage());
            }
        }
    }

    /**
     * 绑定部门管理员角色权限
     *
     * @param $role_id
     * @param $client_id
     */
    private function DepartmentManagerAssignUser($role_id, $client_id)
    {
        $list = new \common\library\account\UserList();
        $list->setClientId($client_id);
        $list->setEnableFlag(1);
        $userList = $list->find();

        $department = new \common\library\department\DepartmentMember($client_id);

        foreach ($userList as $item) {
            if ($this->isDepartmentManager($department->getUserDepartment($item['user_id']))) {
                self::info("client_id={$client_id}, role_id={$role_id}, user_id={$item['user_id']} is Manager.");
                PrivilegePermission::getInstance($item['user_id'])->assignRoleUser(
                    $role_id,
                    [$item['user_id']],
                    false
                );
            }
        }
    }

    /**
     * 绑定普通用户角色权限
     *
     * @param $role_id
     * @param $client_id
     */
    public function NormalAssignUser($role_id, $client_id)
    {
        $list = new \common\library\account\UserList();
        $list->setClientId($client_id);
        $list->setEnableFlag(1);
        $userList = $list->find();

        foreach ($userList as $item) {
            self::info("client_id={$client_id}, role_id={$role_id}, user_id={$item['user_id']} assign user.");
            PrivilegePermission::getInstance($item['user_id'])->assignRoleUser(
                $role_id,
                [$item['user_id']],
                false
            );
        }
    }

    /**
     * 是否符合部门管理员的条件（所属部门都必须是管理员）
     *
     * @param $list
     * @return bool
     */
    private function isDepartmentManager($list)
    {
        $department = [];
        foreach ($list as $item) {
            if (!isset($department[$item['department_id']])) {
                $department[$item['department_id']] = $item['user_type'];
            } else {
                $department[$item['department_id']] = $item['user_type'] == 1 ? $item['user_type']
                    : $department[$item['department_id']];
            }
        }

        return count($department) === array_sum($department) && count($department);
    }


    public function actionAIPrivilegeDispatcher($lastNumber = null){
        $clientList = $this->getGreyClientIds($lastNumber);
        $num = 1;
        $clientCount = count($clientList);
        foreach ($clientList as $clientId) {
            echo "$clientId $num/$clientCount \n";
            $num++;
            try{
                $this->actionAIPrivilege($clientId);
            }catch (Exception $exception){
                echo $exception->getMessage();
                continue;
            }
        }
    }

    public function actionAIPrivilege($clientId){
        \ClientPrivilegeModel::setConnection(Yii::app()->account_base_db);
        $client = Client::getClient($clientId);

        $masterUser = $client->getMasterUser();

        if($masterUser->isEmpty()){
            self::info("masterUser not exists! client_id: $clientId");
            return;
        }

        $privilegePermission = PrivilegePermission::getInstance($masterUser->getUserId());

        $version = $client->getExtentAttributes([Client::EXTERNAL_KEY_CRM_VERSION])['crm_version_id']??0;
        if($version != 9){
            $systems = $privilegePermission->getClientSystems();
            $systemIds = array_column($systems, 'system_id');
            if(!in_array('prod.crm_plus', $systemIds)){
                self::info("clientId version:$version continue!");
                return;
            }
        }

        $clientSystems = $privilegePermission->getClientSystems();
        $clientSystems = array_column($clientSystems, 'system_id');
        if(empty($clientSystems)){
            return;
        }

        $privilegePermission->assignClientPrivilege($clientId, PrivilegeConstants::PRIVILEGE_AI);
        $privilegePermission->assignClientPrivilege($clientId, PrivilegeConstants::PRIVILEGE_CRM_LEAD_MODULES);
        $privilegePermission->assignClientPrivilege($clientId, PrivilegeConstants::PRIVILEGE_CRM_LEAD_SWITCH);
    }

    public function actionFixRoles()
    {
        $db = \Yii::app()->account_base_db;
        $sql = "select client_id, role_name, create_time, count(1) as total, group_concat(role_id) as roleids "
             . "from tbl_privilege_role where create_time>='2018-11-01' and create_time<='2018-12-01' and "
             . "role_name='普通用户' and enable_flag=1 group by client_id, role_name having total > 1";
        $list = $db->createCommand($sql)->queryAll(true);

        $totalNumber = 0;

        foreach ($list as $item) {
            $clientId = $item['client_id'];
            $roleIds = array_reverse(explode(',', $item['roleids']));
            $upList = [];

            foreach ($roleIds as $roleId) {
                $sql = "SELECT count(1) FROM tbl_privilege_role_user WHERE client_id=:client_id AND role_id=:role_id AND enable_flag=1";
                $params = [':client_id' => $clientId, ':role_id' => $roleId];
                $total = $db->createCommand($sql)->queryScalar($params);

                echo "clientId = {$clientId}, roleId = {$roleId}, total = {$total}", PHP_EOL;

                $upList[$roleId] = $total;
                $totalNumber++;
            }

            $number = count($roleIds) - 1;
            foreach ($upList as $roleId => $total) {
                if ($total == 0 && $number > 0) {
                    $admin_user_id = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();
                    $roleModel = new PrivilegeRole(User::getUserObject($admin_user_id));

                    if ($roleModel) {
//                        $roleModel->remove($roleId);
                        echo "remove clientId = {$clientId}, roleId = {$roleId}, total = {$total}", PHP_EOL;
                        $number--;
                    }

                }
            }

        }

        echo "TotalNumber = {$totalNumber}", PHP_EOL;

    }
}