<?php


namespace tests\functional\product;


use common\library\alibaba\services\queue\QueueHelper;
use common\library\custom_field\CustomFieldService;
use common\library\import\Import;
use common\library\custom_field\FieldExportService;
use common\library\custom_field\FieldList;
use common\library\custom_field\Helper;
use common\library\product_v2\formatter\ProductField;
use common\library\product_v2\parts\ProductPartsAPI;
use common\library\product_v2\Product;
use common\library\recycle\Recycle;
use common\library\util\sort\CustomFieldSortRepository;
use common\library\util\sort\Sorter;
use common\models\client\ProductHistory;

class ProductWriteTest extends \WebFunctionalTestCase
{

    public function testImport1()
    {
        ini_set('memory_limit', '2046M');
        \User::setLoginUserById(11858714);
        $user = \User::getLoginUser();

        $import = new Import($user->getClientId(), 3513682672);
        $importExecutor = new \common\library\product_v2\import\SkuProductImportExecutor($import);
        $importExecutor->run();
    }
    public function testProductParts()
    {
        \User::setLoginUserById(11858712);
        $user = \User::getLoginUser();
        $api = new ProductPartsAPI($user->getClientId(), $user->getUserId());
        $api->closeEnableFlage([
            2,3
        ]);
    }

    public function testDelete1()
    {
        \User::setLoginUserById(11858712);
        $params = [
            'product_ids' => [3462839415, 3462841137]
        ];
        $this->callAction('delete', $params);
        $this->responseOk();
    }

    public function testBatchEdit()
    {
        \User::setLoginUserById(11858712);
        $this->callAction('BatchEdit', [
            'product_id' => [3469447395, 3468443360],
            'data' => json_encode([
                'is_parts' => 0,
                'product_remark' => rand(100, 100000),
            ]),
        ]);
        $this->responseOk();
    }

    public function testCreateV2()
    {
        \User::setLoginUserById(11858714);
        $clientId = \User::getLoginUser()->getClientId();
        $params = [
            'product_id' => '',
            'platform_sku_id' => '',
            'file_ids' => '[]',
            'supplier_product_list' => '[]',
            'data' => '{"32381882":"","37533537":"","37859878":"","38190177":"","38450075":"","39496298":"","1100021250":"","1100200852":"","1100237250":"","1100237303":"","1100237329":"","1100243173":"好","1100375155":"这个是默认值","**********":"","1100384190":"","**********":[],"1100537214":"","**********":"","1101009627":122.33,"1101058789":"2","1102557269":"","1103182093":"","1103182094":"","1103935296":"11","1103935322":"1","1105787763":333333333333333,"1463356179":"","1480093341":"","1480108834":"","1480109339":"","3117596653":"","3136790871":"<img src=\"https://k.dev.xiaoman.cn/api/customerWrite/remark?company_id=3136618293&amp;remark_type=101&amp;content=csrf注入的11\">","3265534451":"","3265534453":"","3265534525":"","3265534531":"","product_no":"skyhe20231121002","cn_name":"skyhe20231121002","images":[],"model":"","group_id":"","unit":"","description":"","from_url":"","product_remark":"","name":"skyhe20231121002","supplier_product_list":[],"cost_with_tax":{"cost_currency":"CNY","cost":100},"fob":{"fob_type":4,"quantity":10,"gradient_price":[{"price":"","quantity":""}],"price_currency":"CNY","fob_price":150},"customs_cn_name":"","customs_name":"","package_size":{"package_size_length":"","package_size_weight":"","package_size_height":""},"package_volume":"","package_gross_weight":"","count_per_package":"","package_remark":"","package_unit":"","category_ids":"","vat_rate":"","tax_refund_rate":"","hs_code":"","info_json":[]}'
        ];

        $this->callAction('CreateV2', $params);
        $this->responseOk();
    }

    public function testMultiMatch()
    {
        \User::setLoginUserById(11858714);
        $user = \User::getLoginUser();
        $taskApi = new \common\library\platform_product\task\PlatformProductTaskApi($user->getClientId(), 11858714);
        $taskApi->execByTaskId(3458254358);

        $params = [
'match_scope'=> 1,
'match_pattern'=> 1,
'match_config'=> '[]',
'platform_sku_ids'=> [3426391397,3426391400,3426391402,3426391404,3426391405,3426391407,3426391410,3426391411,3426391415],
        ];
        $this->callAction('MultiMatch', $params, 'ThirdProductWrite');
        $this->responseOk(null, self::$responseCode);
    }

    public function testCopy()
    {
        $this->loginAsQiao();
        $this->loginAsPro();
        $params = [
            'product_id' => 1105182618
        ];
        $this->callAction('copy', $params);
        $this->responseOk();
    }

    public function testDelete()
    {
        $this->loginUser('<EMAIL>');
        $params = [
            'product_ids' => [1120646349,]
        ];
        $this->callAction('delete', $params);
        $this->responseOk();

//        $this->seeInDatabase(\Recycle::class, [
//            'type'     => Recycle::PRODUCT,
//            'refer_id' => current($params['product_ids']),
//        ]);
//
//        $this->seeInDatabase(ProductHistory::class, [
//            'product_id' => current($params['product_ids']),
//            'type'       => ProductHistory::TYPE_DELETE,
//        ]);
    }

    public function testRecover()
    {
        $this->loginAsPro();
        $productId = 1104087287;
        $params = [
            'refer_id' => [$productId],
            'type'     => Recycle::PRODUCT,
        ];
        $this->callAction('recover', $params, 'recycleWrite');
        $this->responseOk();

        $this->notSeeInDatabase(\Recycle::class, [
            'type'     => Recycle::PRODUCT,
            'refer_id' => $productId,
        ]);

        $this->seeInDatabase(ProductHistory::class, [
            'product_id' => $productId,
            'type'       => ProductHistory::TYPE_RECOVER,
        ]);
    }

    public function testCreate()
    {
//        $this->loginAsWeason3();
//        $product = new Product(1, 3160003987);
//        serialize($product);
//        return;

        $this->loginUser('<EMAIL>');
        $this->loginAsWeason3();
//        $data = '[{"id":1,"name":"基本信息","can_add_custom_field":true,"is_hide":false,"fields":[{"client_id":"14119","id":"product_no","type":"1","base":"1","name":"产品编号","field_type":"1","ext_info":[],"require":"0","edit_required":"0","disable_flag":"0","edit_hide":"0","default":"","edit_default":"0","hint":"","edit_hint":"1","is_editable":1,"is_list":"0","columns":["product_no"],"relation_type":"0","relation_field":"","relation_field_type":"0","relation_field_name":"","order":"12","app_order":"21","enable_flag":"1","readonly":"0","create_time":"2019-11-15 11:52:05","update_time":"2019-11-15 11:52:05","unique_check":"0","unique_prevent":"1","unique_message":"","group_id":"1","relation":0,"value":"","edit_unique":"0","edit_unique_check":"1","edit_unique_prevent":"1","edit_unique_message":"1"},{"client_id":"14119","id":"name","type":"1","base":"1","name":"产品名称","field_type":"1","ext_info":[],"require":"1","edit_required":"0","disable_flag":"0","edit_hide":"0","default":"","edit_default":"0","hint":"","edit_hint":"1","is_editable":1,"is_list":"0","columns":["name"],"relation_type":"0","relation_field":"","relation_field_type":"0","relation_field_name":"","order":"11","app_order":"20","enable_flag":"1","readonly":"0","create_time":"2019-11-15 11:52:05","update_time":"2019-11-15 11:52:05","unique_check":"0","unique_prevent":"1","unique_message":"","group_id":"1","relation":0,"value":"柠檬精","edit_unique":"0","edit_unique_check":"1","edit_unique_prevent":"1","edit_unique_message":"1"},{"client_id":"14119","id":"model","type":"1","base":"1","name":"产品型号","field_type":"1","ext_info":[],"require":"0","edit_required":"1","disable_flag":"0","edit_hide":"1","default":"","edit_default":"0","hint":"","edit_hint":"1","is_editable":1,"is_list":"0","columns":["model"],"relation_type":"0","relation_field":"","relation_field_type":"0","relation_field_name":"","order":"10","app_order":"19","enable_flag":"1","readonly":"0","create_time":"2019-11-15 11:52:05","update_time":"2019-11-15 11:52:05","unique_check":"0","unique_prevent":"1","unique_message":"","group_id":"1","relation":0,"value":"lemo","edit_unique":"0","edit_unique_check":"1","edit_unique_prevent":"1","edit_unique_message":"1"},{"client_id":"14119","id":"group_id","type":"1","base":"1","name":"产品分组","field_type":"3","ext_info":[],"require":"0","edit_required":"1","disable_flag":"0","edit_hide":"0","default":"","edit_default":"1","hint":"","edit_hint":"1","is_editable":1,"is_list":"0","columns":["group_id"],"relation_type":"0","relation_field":"","relation_field_type":"0","relation_field_name":"","order":"9","app_order":"18","enable_flag":"1","readonly":"0","create_time":"2019-11-15 11:52:05","update_time":"2019-11-15 11:52:05","unique_check":"0","unique_prevent":"1","unique_message":"","group_id":"1","relation":0,"value":1103944016,"edit_unique":"0","edit_unique_check":"1","edit_unique_prevent":"1","edit_unique_message":"1"},{"client_id":"14119","id":"description","type":"1","base":"1","name":"产品描述","field_type":"2","ext_info":[],"require":"0","edit_required":"1","disable_flag":"0","edit_hide":"1","default":"","edit_default":"0","hint":"","edit_hint":"1","is_editable":1,"is_list":"0","columns":["description"],"relation_type":"0","relation_field":"","relation_field_type":"0","relation_field_name":"","order":"8","app_order":"17","enable_flag":"1","readonly":"0","create_time":"2019-11-15 11:52:05","update_time":"2020-05-12 16:53:39","unique_check":"0","unique_prevent":"1","unique_message":"","group_id":"1","relation":0,"value":"小小的柠檬精，你酸了吗","edit_unique":"0","edit_unique_check":"1","edit_unique_prevent":"1","edit_unique_message":"1"},{"client_id":"14119","id":"images","type":"1","base":"1","name":"产品图片","field_type":"6","ext_info":[],"require":"0","edit_required":"1","disable_flag":"0","edit_hide":"1","default":"","edit_default":"0","hint":"","edit_hint":"1","is_editable":1,"is_list":"0","columns":["images"],"relation_type":"0","relation_field":"","relation_field_type":"0","relation_field_name":"","order":"7","app_order":"16","enable_flag":"1","readonly":"0","create_time":"2019-11-15 11:52:05","update_time":"2019-11-15 11:52:05","unique_check":"0","unique_prevent":"1","unique_message":"","group_id":"1","relation":0,"value":[{"progress":null,"id":"1104353615","src":"https://v4client.oss-cn-hangzhou.aliyuncs.com/mail-attach/img/11858712/fb0e31ffe5ebb2e51b0eb8b7226933ca1a95d05a5b710bb0fdf7936c8bbf6b7f.jpg"},{"progress":null,"id":"1104353614","src":"https://v4client.oss-cn-hangzhou.aliyuncs.com/mail-attach/img/11858712/5b1bb9d52102be317ea003a99dc29b7d0f3e7af48221a8569566ab7c4f8e022f.jpg"},{"progress":null,"id":"1104353616","src":"https://v4client.oss-cn-hangzhou.aliyuncs.com/product-file/img/11858712/06bb5468157366f0b09b8a177c66a22a7a8fce85332f9fce5a5360bb6e3ef75f.jpg"},{"progress":null,"id":"1104353617","src":"https://v4client.oss-cn-hangzhou.aliyuncs.com/product-file/img/11858712/c75f60f1a4fda85cfd595e267f8d1a422b9a8bcd1b9dd4fce2ba998f60e512c3.jpg"},{"progress":null,"id":"1104353618","src":"https://v4client.oss-cn-hangzhou.aliyuncs.com/mail-attach/img/11858712/93a971ff44620773547c7fab3c4b7ce33d82842857d48d6b5c2ee41b7883943d.jpg"}],"edit_unique":"0","edit_unique_check":"1","edit_unique_prevent":"1","edit_unique_message":"1"},{"client_id":"14119","id":"1101142917","type":"1","base":"0","name":"产品下拉单选","field_type":"3","ext_info":["简单爱","迷迭香","乌克丽丽"],"require":"0","edit_required":"1","disable_flag":"0","edit_hide":"1","default":"","edit_default":"1","hint":"","edit_hint":"1","is_editable":1,"is_list":"0","columns":["1101142917"],"relation_type":"0","relation_field":"","relation_field_type":"0","relation_field_name":"","order":"6","app_order":"0","enable_flag":"1","readonly":"0","create_time":"2019-12-09 14:49:14","update_time":"2019-12-09 14:49:14","unique_check":"0","unique_prevent":"1","unique_message":"","group_id":"1","relation":0,"value":"简单爱","edit_unique":"0","edit_unique_check":"1","edit_unique_prevent":"1","edit_unique_message":"1"},{"client_id":"14119","id":"1101142900","type":"1","base":"0","name":"没有默认值只有提示","field_type":"5","ext_info":[0],"require":"0","edit_required":"1","disable_flag":"0","edit_hide":"1","default":"","edit_default":"1","hint":"产品数值提示：100","edit_hint":"1","is_editable":1,"is_list":"0","columns":["1101142900"],"relation_type":"0","relation_field":"","relation_field_type":"0","relation_field_name":"","order":"5","app_order":"0","enable_flag":"1","readonly":"0","create_time":"2019-12-09 14:47:34","update_time":"2019-12-09 14:47:34","unique_check":"0","unique_prevent":"1","unique_message":"","group_id":"1","relation":0,"value":"3000","edit_unique":"0","edit_unique_check":"1","edit_unique_prevent":"1","edit_unique_message":"1"},{"client_id":"14119","id":"1101142920","type":"1","base":"0","name":"下拉单选没有默认值","field_type":"3","ext_info":["可爱女人","甜甜的"],"require":"0","edit_required":"1","disable_flag":"0","edit_hide":"1","default":"可爱女人","edit_default":"1","hint":"","edit_hint":"1","is_editable":1,"is_list":"0","columns":["1101142920"],"relation_type":"0","relation_field":"","relation_field_type":"0","relation_field_name":"","order":"4","app_order":"0","enable_flag":"1","readonly":"0","create_time":"2019-12-09 14:49:47","update_time":"2019-12-09 14:49:47","unique_check":"0","unique_prevent":"1","unique_message":"","group_id":"1","relation":0,"value":"可爱女人","edit_unique":"0","edit_unique_check":"1","edit_unique_prevent":"1","edit_unique_message":"1"},{"client_id":"14119","id":"1101142899","type":"1","base":"0","name":"产品数值","field_type":"5","ext_info":[0],"require":"0","edit_required":"1","disable_flag":"0","edit_hide":"1","default":"123","edit_default":"1","hint":"23","edit_hint":"1","is_editable":1,"is_list":"0","columns":["1101142899"],"relation_type":"0","relation_field":"","relation_field_type":"0","relation_field_name":"","order":"3","app_order":"0","enable_flag":"1","readonly":"0","create_time":"2019-12-09 14:47:11","update_time":"2019-12-09 14:47:11","unique_check":"0","unique_prevent":"1","unique_message":"","group_id":"1","relation":0,"value":"123","edit_unique":"0","edit_unique_check":"1","edit_unique_prevent":"1","edit_unique_message":"1"},{"client_id":"14119","id":"1103943568","type":"1","base":"0","name":"下拉多选","field_type":"7","ext_info":["小时光","旧时光","皮皮"],"require":"0","edit_required":"1","disable_flag":"0","edit_hide":"1","default":"","edit_default":"1","hint":"","edit_hint":"1","is_editable":1,"is_list":"0","columns":["1103943568"],"relation_type":"0","relation_field":"","relation_field_type":"0","relation_field_name":"","order":"2","app_order":"0","enable_flag":"1","readonly":"0","create_time":"2020-05-13 16:58:39","update_time":"2020-05-13 16:58:39","unique_check":"0","unique_prevent":"1","unique_message":"","group_id":"1","relation":0,"value":["小时光","旧时光"],"edit_unique":"0","edit_unique_check":"1","edit_unique_prevent":"1","edit_unique_message":"1"},{"client_id":"14119","id":"1103943594","type":"1","base":"0","name":"自定义日期-时间","field_type":"4","ext_info":[],"require":"0","edit_required":"1","disable_flag":"0","edit_hide":"1","default":"","edit_default":"1","hint":"","edit_hint":"1","is_editable":1,"is_list":"0","columns":["1103943594"],"relation_type":"0","relation_field":"","relation_field_type":"0","relation_field_name":"","order":"1","app_order":"0","enable_flag":"1","readonly":"0","create_time":"2020-05-13 16:58:55","update_time":"2020-05-13 16:58:55","unique_check":"0","unique_prevent":"1","unique_message":"","group_id":"1","relation":0,"value":"2020-06-04","edit_unique":"0","edit_unique_check":"1","edit_unique_prevent":"1","edit_unique_message":"1"},{"client_id":"14119","id":"1104004375","type":"1","base":"0","name":"基本信息的多行文本","field_type":"2","ext_info":[],"require":"0","edit_required":"1","disable_flag":"0","edit_hide":"1","default":"1、粉色的\n2、水蜜桃","edit_default":"1","hint":"多行文本","edit_hint":"1","is_editable":1,"is_list":"0","columns":["1104004375"],"relation_type":"0","relation_field":"","relation_field_type":"0","relation_field_name":"","order":"0","app_order":"0","enable_flag":"1","readonly":"0","create_time":"2020-05-18 16:26:12","update_time":"2020-05-18 16:26:12","unique_check":"0","unique_prevent":"1","unique_message":"","group_id":"1","relation":0,"value":"1、粉色的\n2、水蜜桃","edit_unique":"0","edit_unique_check":"1","edit_unique_prevent":"1","edit_unique_message":"1"},{"client_id":"14119","id":"1104029332","type":"1","base":"0","name":"111","field_type":"7","ext_info":[],"require":"0","edit_required":"1","disable_flag":"0","edit_hide":"1","default":"","edit_default":"1","hint":"","edit_hint":"1","is_editable":1,"is_list":"0","columns":["1104029332"],"relation_type":"0","relation_field":"","relation_field_type":"0","relation_field_name":"","order":"0","app_order":"0","enable_flag":"1","readonly":"0","create_time":"2020-05-22 13:33:38","update_time":"2020-05-22 13:33:38","unique_check":"0","unique_prevent":"1","unique_message":"","group_id":"1","relation":0,"value":"","edit_unique":"0","edit_unique_check":"1","edit_unique_prevent":"1","edit_unique_message":"1"},{"client_id":"14119","id":"1104235403","type":"1","base":"0","name":"报价日期","field_type":"4","ext_info":[],"require":"0","edit_required":"1","disable_flag":"0","edit_hide":"1","default":"","edit_default":"1","hint":"","edit_hint":"1","is_editable":1,"is_list":"0","columns":["1104235403"],"relation_type":"0","relation_field":"","relation_field_type":"0","relation_field_name":"","order":"0","app_order":"0","enable_flag":"1","readonly":"0","create_time":"2020-06-01 16:51:17","update_time":"2020-06-01 16:51:17","unique_check":"0","unique_prevent":"1","unique_message":"","group_id":"1","relation":0,"value":"2020-06-04","edit_unique":"0","edit_unique_check":"1","edit_unique_prevent":"1","edit_unique_message":"1"}],"list":[]},{"id":2,"name":"价格信息","can_add_custom_field":true,"is_hide":false,"fields":[{"client_id":"14119","id":"fob","type":"1","base":"1","name":"离岸价","field_type":"0","ext_info":[],"require":"0","edit_required":"1","disable_flag":"0","edit_hide":"1","default":"","edit_default":"0","hint":"","edit_hint":"1","is_editable":1,"is_list":"0","columns":["price_currency","price_min","price_max"],"relation_type":"0","relation_field":"","relation_field_type":"0","relation_field_name":"","order":"5","app_order":"15","enable_flag":"1","readonly":"0","create_time":"2019-11-15 11:52:05","update_time":"2020-05-12 16:53:39","unique_check":"0","unique_prevent":"1","unique_message":"","group_id":"2","relation":0,"value":{"price_currency":"USD","price_min":"45","price_max":"78"},"edit_unique":"0","edit_unique_check":"1","edit_unique_prevent":"1","edit_unique_message":"1"},{"client_id":"14119","id":"cost_with_tax","type":"1","base":"1","name":"含税成本价","field_type":"0","ext_info":[],"require":"0","edit_required":"1","disable_flag":"0","edit_hide":"1","default":"","edit_default":"0","hint":"","edit_hint":"1","is_editable":1,"is_list":"0","columns":["cost_currency","cost"],"relation_type":"0","relation_field":"","relation_field_type":"0","relation_field_name":"","order":"4","app_order":"14","enable_flag":"1","readonly":"0","create_time":"2019-11-15 11:52:05","update_time":"2020-05-12 16:53:39","unique_check":"0","unique_prevent":"1","unique_message":"","group_id":"2","relation":0,"value":{"cost_currency":"USD","cost":"30"},"edit_unique":"0","edit_unique_check":"1","edit_unique_prevent":"1","edit_unique_message":"1"},{"client_id":"14119","id":"unit","type":"1","base":"1","name":"计量单位","field_type":"3","ext_info":[],"require":"0","edit_required":"1","disable_flag":"0","edit_hide":"1","default":"","edit_default":"0","hint":"","edit_hint":"1","is_editable":1,"is_list":"0","columns":["unit"],"relation_type":"0","relation_field":"","relation_field_type":"0","relation_field_name":"","order":"3","app_order":"13","enable_flag":"1","readonly":"0","create_time":"2020-04-17 14:12:33","update_time":"2020-04-17 14:12:33","unique_check":"0","unique_prevent":"1","unique_message":"","group_id":"2","relation":0,"value":"Bag","edit_unique":"0","edit_unique_check":"1","edit_unique_prevent":"1","edit_unique_message":"1"},{"client_id":"14119","id":"minimum_order_quantity","type":"1","base":"1","name":"最小起订量","field_type":"0","ext_info":[],"require":"0","edit_required":"1","disable_flag":"0","edit_hide":"1","default":"","edit_default":"0","hint":"","edit_hint":"1","is_editable":1,"is_list":"0","columns":["quantity"],"relation_type":"0","relation_field":"","relation_field_type":"0","relation_field_name":"","order":"1","app_order":"12","enable_flag":"1","readonly":"0","create_time":"2019-11-15 11:52:05","update_time":"2020-05-12 16:53:39","unique_check":"0","unique_prevent":"1","unique_message":"","group_id":"2","relation":0,"value":"40000000","edit_unique":"0","edit_unique_check":"1","edit_unique_prevent":"1","edit_unique_message":"1"},{"client_id":"14119","id":"1104013620","type":"1","base":"0","name":"每公斤单价","field_type":"3","ext_info":["20元","30元","40元"],"require":"0","edit_required":"1","disable_flag":"0","edit_hide":"1","default":"20元","edit_default":"1","hint":"","edit_hint":"1","is_editable":1,"is_list":"0","columns":["1104013620"],"relation_type":"0","relation_field":"","relation_field_type":"0","relation_field_name":"","order":"0","app_order":"0","enable_flag":"1","readonly":"0","create_time":"2020-05-20 14:59:48","update_time":"2020-05-20 14:59:48","unique_check":"0","unique_prevent":"1","unique_message":"","group_id":"2","relation":0,"value":"30元","edit_unique":"0","edit_unique_check":"1","edit_unique_prevent":"1","edit_unique_message":"1"},{"client_id":"14119","id":"1104013622","type":"1","base":"0","name":"阶梯价格测试","field_type":"2","ext_info":["1、10-40"],"require":"0","edit_required":"1","disable_flag":"0","edit_hide":"1","default":"1、10-20\n2、20-40\n3、40-80","edit_default":"1","hint":"","edit_hint":"1","is_editable":1,"is_list":"0","columns":["1104013622"],"relation_type":"0","relation_field":"","relation_field_type":"0","relation_field_name":"","order":"0","app_order":"0","enable_flag":"1","readonly":"0","create_time":"2020-05-20 15:00:26","update_time":"2020-05-20 15:00:26","unique_check":"0","unique_prevent":"1","unique_message":"","group_id":"2","relation":0,"value":"1、10-20\n2、20-40\n3、40-80","edit_unique":"0","edit_unique_check":"1","edit_unique_prevent":"1","edit_unique_message":"1"}],"list":[]},{"id":3,"name":"包装信息","can_add_custom_field":true,"is_hide":false,"fields":[{"client_id":"14119","id":"package_size","type":"1","base":"1","name":"包装尺寸","field_type":"0","ext_info":[],"require":"0","edit_required":"1","disable_flag":"0","edit_hide":"0","default":"","edit_default":"0","hint":"","edit_hint":"0","is_editable":1,"is_list":"0","columns":["package_size_length","package_size_weight","package_size_height"],"relation_type":"0","relation_field":"","relation_field_type":"0","relation_field_name":"","order":"7","app_order":"11","enable_flag":"1","readonly":"0","create_time":"2020-04-17 14:12:33","update_time":"2020-04-17 14:12:33","unique_check":"0","unique_prevent":"1","unique_message":"","group_id":"3","relation":0,"value":{"package_size_length":"1","package_size_weight":"1","package_size_height":"1"},"edit_unique":"0","edit_unique_check":"1","edit_unique_prevent":"1","edit_unique_message":"1"},{"client_id":"14119","id":"package_volume","type":"1","base":"1","name":"包装体积","field_type":"5","ext_info":[2],"require":"0","edit_required":"1","disable_flag":"0","edit_hide":"1","default":"","edit_default":"0","hint":"","edit_hint":"1","is_editable":1,"is_list":"0","columns":["package_volume"],"relation_type":"0","relation_field":"","relation_field_type":"0","relation_field_name":"","order":"6","app_order":"10","enable_flag":"1","readonly":"0","create_time":"2019-11-15 11:52:05","update_time":"2020-05-12 16:53:39","unique_check":"0","unique_prevent":"1","unique_message":"","group_id":"3","relation":0,"value":"","edit_unique":"0","edit_unique_check":"1","edit_unique_prevent":"1","edit_unique_message":"1"},{"client_id":"14119","id":"package_gross_weight","type":"1","base":"1","name":"包装毛重","field_type":"5","ext_info":[2],"require":"0","edit_required":"1","disable_flag":"0","edit_hide":"1","default":"","edit_default":"0","hint":"","edit_hint":"1","is_editable":1,"is_list":"0","columns":["package_gross_weight"],"relation_type":"0","relation_field":"","relation_field_type":"0","relation_field_name":"","order":"5","app_order":"9","enable_flag":"1","readonly":"0","create_time":"2019-11-15 11:52:05","update_time":"2020-05-12 16:53:40","unique_check":"0","unique_prevent":"1","unique_message":"","group_id":"3","relation":0,"value":"","edit_unique":"0","edit_unique_check":"1","edit_unique_prevent":"1","edit_unique_message":"1"},{"client_id":"14119","id":"count_per_package","type":"1","base":"1","name":"每包装产品数","field_type":"5","ext_info":[],"require":"0","edit_required":"1","disable_flag":"0","edit_hide":"1","default":"","edit_default":"0","hint":"","edit_hint":"1","is_editable":1,"is_list":"0","columns":["count_per_package"],"relation_type":"0","relation_field":"","relation_field_type":"0","relation_field_name":"","order":"3","app_order":"8","enable_flag":"1","readonly":"0","create_time":"2019-11-15 11:52:05","update_time":"2020-05-12 16:53:40","unique_check":"0","unique_prevent":"1","unique_message":"","group_id":"3","relation":0,"value":"5600","edit_unique":"0","edit_unique_check":"1","edit_unique_prevent":"1","edit_unique_message":"1"},{"client_id":"14119","id":"package_remark","type":"1","base":"1","name":"包装说明","field_type":"2","ext_info":[],"require":"0","edit_required":"1","disable_flag":"0","edit_hide":"1","default":"","edit_default":"0","hint":"","edit_hint":"1","is_editable":1,"is_list":"0","columns":["package_remark"],"relation_type":"0","relation_field":"","relation_field_type":"0","relation_field_name":"","order":"2","app_order":"7","enable_flag":"1","readonly":"0","create_time":"2019-11-15 11:52:05","update_time":"2020-05-12 16:53:40","unique_check":"0","unique_prevent":"1","unique_message":"","group_id":"3","relation":0,"value":"","edit_unique":"0","edit_unique_check":"1","edit_unique_prevent":"1","edit_unique_message":"1"},{"client_id":"14119","id":"1103943821","type":"1","base":"0","name":"包装材质","field_type":"3","ext_info":["木质架子","纸箱子","蛇皮袋","塑料袋"],"require":"0","edit_required":"1","disable_flag":"0","edit_hide":"1","default":"","edit_default":"1","hint":"","edit_hint":"1","is_editable":1,"is_list":"0","columns":["1103943821"],"relation_type":"0","relation_field":"","relation_field_type":"0","relation_field_name":"","order":"1","app_order":"0","enable_flag":"1","readonly":"0","create_time":"2020-05-13 17:15:20","update_time":"2020-05-13 17:15:20","unique_check":"0","unique_prevent":"1","unique_message":"","group_id":"3","relation":0,"value":"木质架子","edit_unique":"0","edit_unique_check":"1","edit_unique_prevent":"1","edit_unique_message":"1"},{"client_id":"14119","id":"1104004379","type":"1","base":"0","name":"包装需要的人力","field_type":"3","ext_info":["1个人","2个人","3个人"],"require":"0","edit_required":"1","disable_flag":"0","edit_hide":"1","default":"","edit_default":"1","hint":"","edit_hint":"1","is_editable":1,"is_list":"0","columns":["1104004379"],"relation_type":"0","relation_field":"","relation_field_type":"0","relation_field_name":"","order":"0","app_order":"0","enable_flag":"1","readonly":"0","create_time":"2020-05-18 16:26:41","update_time":"2020-05-18 16:26:41","unique_check":"0","unique_prevent":"1","unique_message":"","group_id":"3","relation":0,"value":"1个人","edit_unique":"0","edit_unique_check":"1","edit_unique_prevent":"1","edit_unique_message":"1"}],"list":[]},{"id":4,"name":"产品介绍","can_add_custom_field":true,"is_hide":false,"fields":[{"client_id":"14119","id":"category_ids","type":"1","base":"1","name":"产品类目","field_type":"0","ext_info":[],"require":"0","edit_required":"1","disable_flag":"0","edit_hide":"1","default":"","edit_default":"0","hint":"","edit_hint":"1","is_editable":1,"is_list":"0","columns":["category_ids"],"relation_type":"0","relation_field":"","relation_field_type":"0","relation_field_name":"","order":"5","app_order":"5","enable_flag":"1","readonly":"0","create_time":"2019-11-15 11:52:05","update_time":"2020-05-12 16:53:40","unique_check":"0","unique_prevent":"1","unique_message":"","group_id":"4","relation":0,"value":["1","103","10309"],"edit_unique":"0","edit_unique_check":"1","edit_unique_prevent":"1","edit_unique_message":"1"},{"client_id":"14119","id":"hs_code","type":"1","base":"1","name":"海关编码","field_type":"1","ext_info":[],"require":"0","edit_required":"1","disable_flag":"0","edit_hide":"1","default":"","edit_default":"0","hint":"","edit_hint":"1","is_editable":1,"is_list":"0","columns":["hs_code"],"relation_type":"0","relation_field":"","relation_field_type":"0","relation_field_name":"","order":"4","app_order":"4","enable_flag":"1","readonly":"0","create_time":"2019-11-15 11:52:05","update_time":"2020-05-12 16:53:40","unique_check":"0","unique_prevent":"1","unique_message":"","group_id":"4","relation":0,"value":"0023910293side","edit_unique":"0","edit_unique_check":"1","edit_unique_prevent":"1","edit_unique_message":"1"},{"client_id":"14119","id":"info_json","type":"1","base":"1","name":"产品属性","field_type":"0","ext_info":[],"require":"0","edit_required":"0","disable_flag":"0","edit_hide":"0","default":"","edit_default":"0","hint":"","edit_hint":"1","is_editable":1,"is_list":"0","columns":["info_json"],"relation_type":"0","relation_field":"","relation_field_type":"0","relation_field_name":"","order":"3","app_order":"3","enable_flag":"1","readonly":"0","create_time":"2019-11-15 11:52:05","update_time":"2020-05-12 16:53:40","unique_check":"0","unique_prevent":"1","unique_message":"","group_id":"4","relation":0,"value":[{"name":"Model Number","type":"input","is_editable":0,"value":"w3"},{"name":"Brand Name","type":"input","is_editable":0,"value":"lemo"},{"name":"Place of Origin","value":"AX","type":"country"},{"name":"Weight (kg)","type":"input","is_editable":0,"value":"34"},{"name":"Certification","type":"input","is_editable":0},{"name":"Grade","type":"input","is_editable":0,"value":"grade"},{"name":"Size (cm)","type":"input","is_editable":0,"value":"33"},{"name":"Maturity","type":"input","is_editable":0},{"name":"Cultivation Type","type":"select","value":"350688","options":[{"label":"Other","value":"4"},{"label":"Common","value":"350687"},{"label":"GMO","value":"350688"},{"label":"Organic","value":"4466"}]},{"name":"Shape","type":"select","value":"4","options":[{"label":"Other","value":"4"},{"label":"Ball","value":"701"},{"label":"Oval","value":"351094"}]},{"name":"Color","type":"select","value":"366","options":[{"label":"Other","value":"4"},{"label":"Green","value":"175"},{"label":"Red","value":"10"},{"label":"Yellow","value":"366"}]},{"name":"Variety","type":"select","value":"100010356","options":[{"label":"Other","value":"4"},{"label":"Hayward","value":"100010356"},{"label":"Qinmei","value":"100010357"}]},{"name":"Style","type":"input","is_editable":0,"value":"freestyle"},{"name":"Type","type":"input","is_editable":0}],"edit_unique":"0","edit_unique_check":"1","edit_unique_prevent":"1","edit_unique_message":"1"},{"client_id":"14119","id":"from_url","type":"1","base":"1","name":"产品链接","field_type":"1","ext_info":[],"require":"0","edit_required":"0","disable_flag":"0","edit_hide":"0","default":"","edit_default":"0","hint":"","edit_hint":"1","is_editable":1,"is_list":"0","columns":["from_url"],"relation_type":"0","relation_field":"","relation_field_type":"0","relation_field_name":"","order":"2","app_order":"2","enable_flag":"1","readonly":"0","create_time":"2019-11-15 11:52:05","update_time":"2020-05-12 16:53:40","unique_check":"0","unique_prevent":"1","unique_message":"","group_id":"4","relation":0,"value":"www.lemo.com","edit_unique":"0","edit_unique_check":"1","edit_unique_prevent":"1","edit_unique_message":"1"},{"client_id":"14119","id":"product_remark","type":"1","base":"1","name":"产品备注","field_type":"2","ext_info":[],"require":"0","edit_required":"1","disable_flag":"0","edit_hide":"1","default":"","edit_default":"1","hint":"","edit_hint":"1","is_editable":1,"is_list":"0","columns":["product_remark"],"relation_type":"0","relation_field":"","relation_field_type":"0","relation_field_name":"","order":"1","app_order":"1","enable_flag":"1","readonly":"0","create_time":"2019-11-15 11:52:05","update_time":"2020-05-12 16:53:40","unique_check":"0","unique_prevent":"1","unique_message":"","group_id":"4","relation":0,"value":"","edit_unique":"0","edit_unique_check":"1","edit_unique_prevent":"1","edit_unique_message":"1"},{"client_id":"14119","id":"1103943822","type":"1","base":"0","name":"产品颜色","field_type":"1","ext_info":[],"require":"0","edit_required":"1","disable_flag":"0","edit_hide":"1","default":"","edit_default":"1","hint":"","edit_hint":"1","is_editable":1,"is_list":"0","columns":["1103943822"],"relation_type":"0","relation_field":"","relation_field_type":"0","relation_field_name":"","order":"0","app_order":"0","enable_flag":"1","readonly":"0","create_time":"2020-05-13 17:15:49","update_time":"2020-05-13 17:15:49","unique_check":"0","unique_prevent":"1","unique_message":"","group_id":"4","relation":0,"value":"yellow","edit_unique":"0","edit_unique_check":"1","edit_unique_prevent":"1","edit_unique_message":"1"},{"client_id":"14119","id":"1104004385","type":"1","base":"0","name":"产品注意事项","field_type":"7","ext_info":["禁止倒立","易燃","易爆","易碎"],"require":"0","edit_required":"1","disable_flag":"0","edit_hide":"1","default":"","edit_default":"1","hint":"","edit_hint":"1","is_editable":1,"is_list":"0","columns":["1104004385"],"relation_type":"0","relation_field":"","relation_field_type":"0","relation_field_name":"","order":"0","app_order":"0","enable_flag":"1","readonly":"0","create_time":"2020-05-18 16:27:30","update_time":"2020-05-18 16:27:30","unique_check":"0","unique_prevent":"1","unique_message":"","group_id":"4","relation":0,"value":["禁止倒立","易燃","易爆","易碎"],"edit_unique":"0","edit_unique_check":"1","edit_unique_prevent":"1","edit_unique_message":"1"}],"list":[]}]';
        $data = '{"1118951180":["B"],"cost_with_tax":{"cost_currency":"","cost":""},"fob":{"price_currency":"EUR","price_min":11,"price_max":11,"quantity":11,"fob_type":1,"gradient_price":[{"quantity":"","price":""}]},"package_size":{"package_size_length":"","package_size_weight":"","package_size_height":""},"images":[{"progress":null,"id":"1120502341","src":"https://v4client.oss-cn-hangzhou.aliyuncs.com/mail-attach/img/765/a596b97f4503aedb83a0515ae2a626da6ba665deed5ff14deade9ebe2972bc04.JPG"}],"info_json":[{"name":"Model Number","type":"input","is_editable":0},{"name":"Brand Name","type":"input","is_editable":0},{"name":"Place of Origin","value":"","type":"country"},{"name":"Supply Type","type":"select","value":"","options":[{"label":"In-Stock Items","value":"*********"},{"label":"OEM Service","value":"*********"},{"label":"ODM","value":*********}]},{"name":"Material","type":"checkbox","value":[6444210,8083462],"options":[{"label":"Polyamide","value":46713448},{"label":"Silk","value":3356855},{"label":"Other","value":4},{"label":"Modal","value":3489232},{"label":"ACETATE","value":17851002},{"label":"Genuine Leather","value":3242875},{"label":"Milk Fiber","value":1878303647},{"label":"Acrylic","value":4410093},{"label":"Spandex","value":29971367},{"label":"Fur","value":11646718},{"label":"LYCRA","value":3705463},{"label":"Linen","value":7580284},{"label":"Nylon","value":6444210},{"label":"Cotton","value":3703721},{"label":"Latex","value":12040230},{"label":"Faux Leather","value":187600186},{"label":"Ramie","value":121184716},{"label":"Viscose","value":121441610},{"label":"Polyester","value":7719852},{"label":"Rayon","value":37135247},{"label":"Pu","value":3323086},{"label":"Microfiber","value":8083462},{"label":"Faux Fur","value":48854935},{"label":"Bamboo Fiber","value":93434062}]},{"name":"Color","type":"checkbox","value":[],"options":[{"label":"Dark Gray","value":324542552},{"label":"Yellow","value":3354417},{"label":"White","value":3331185},{"label":"Beige","value":5715155},{"label":"Other","value":4},{"label":" Ivory","value":4173850},{"label":"Khaki","value":4384391},{"label":"Green","value":216016296},{"label":"Blue","value":123085},{"label":"Red","value":3331260},{"label":"Rose Madder","value":314492112},{"label":"Black","value":3327837},{"label":"Pink","value":3328925},{"label":"Light Blue","value":3670345},{"label":"GRAY","value":5412614},{"label":"NAVY","value":4371350},{"label":"Dark blue","value":6899263},{"label":"Silver","value":3460547},{"label":"Brown","value":3399503},{"label":"Purple","value":3851110},{"label":"Gold","value":3331116},{"label":"Orange","value":3558409}]},{"name":"Size","type":"checkbox","value":[],"options":[{"label":"Other","value":4},{"label":"5 XL","value":92869724},{"label":"4 XL","value":104559763},{"label":"Free","value":4348586},{"label":"xs","value":28313},{"label":"S","value":28314},{"label":"M","value":28315},{"label":"L","value":28316},{"label":"XXS","value":28381},{"label":"XL","value":28317},{"label":"XXL","value":28318},{"label":"XXXL","value":28319}]},{"name":"7 days sample order lead time","type":"select","value":"","options":[{"label":"Support","value":6212099},{"label":"Not Support","value":1878262937}]},{"name":"Ethnic Region","type":"input","is_editable":0}],"sku_attributes":[{"item_id":"1119281154","value":[{"item_id":"1119281155"},{"item_id":"1120475509"}]}],"name":"测试多规格","model":"111","group_id":1105655957,"description":"1111","unit":"Barrel","package_volume":11,"package_unit":"Barrel","category_ids":["3","307","30799"]}';
        $skuList = '[{"fob_price":"","disable_flag":0,"sku_code":"1","attributes":[{"item_id":"1119281154","value":{"item_id":"1119281155"}}],"image_file_id":"1120502341"},{"fob_price":"","disable_flag":0,"sku_code":"2","attributes":[{"item_id":"1119281154","value":{"item_id":"1120475509"}}],"image_file_id":"1120502341"}]';
        $params = [
            'data' => $data,
//            'sku_list' => $skuList
        ];

        $params = [
            'data' => '{"1100021250":"sde111","1100243173":"好","1100375155":"这个是默认值","1100375290":"大幅度","**********":[],"1101009627":"122.33","1101058789":"2","1102557269":[],"1103182094":[],"1103935296":"11","1103935322":"1","1105787763":"333333333333333","1463356179":[],"1480108834":" 中华人    民共和 国呢  ","1480109339":"字段 2","3117596653":[],"3136790871":"<img src=\"https://k.dev.xiaoman.cn/api/customerWrite/remark?company_id=3136618293&amp;remark_type=101&amp;content=csrf注入的11\">","cost_with_tax":{"cost_currency":"USD","cost":"","associate_cost_flag":"","cost_type":1},"fob":{"price_currency":"","price_min":"","price_max":"","quantity":"","fob_type":1,"gradient_price":[{"quantity":"","price":""}]},"package_size":{"package_size_length":"","package_size_weight":"","package_size_height":""},"images":[],"info_json":[],"name":"123112","attachment_list":[],"supplier_product_list":[],"sku_attributes":null}',
        ];
        $this->callAction('create', $params);

        $this->responseOk();
    }

    public function testEdit()
    {
        $this->loginUser('<EMAIL>');
        $productId = 1120502584;
        $data = '{"1118951180":["B"],"cost_with_tax":{"cost_currency":"","cost":""},"fob":{"price_currency":"EUR","price_min":11,"price_max":11,"quantity":11,"fob_type":1,"gradient_price":[{"quantity":"","price":""}]},"package_size":{"package_size_length":"","package_size_weight":"","package_size_height":""},"images":[{"progress":null,"id":"1120502341","src":"https://v4client.oss-cn-hangzhou.aliyuncs.com/mail-attach/img/765/a596b97f4503aedb83a0515ae2a626da6ba665deed5ff14deade9ebe2972bc04.JPG"}],"info_json":[{"name":"Model Number","type":"input","is_editable":0},{"name":"Brand Name","type":"input","is_editable":0},{"name":"Place of Origin","value":"","type":"country"},{"name":"Supply Type","type":"select","value":"","options":[{"label":"In-Stock Items","value":"*********"},{"label":"OEM Service","value":"*********"},{"label":"ODM","value":*********}]},{"name":"Material","type":"checkbox","value":[6444210,8083462],"options":[{"label":"Polyamide","value":46713448},{"label":"Silk","value":3356855},{"label":"Other","value":4},{"label":"Modal","value":3489232},{"label":"ACETATE","value":17851002},{"label":"Genuine Leather","value":3242875},{"label":"Milk Fiber","value":1878303647},{"label":"Acrylic","value":4410093},{"label":"Spandex","value":29971367},{"label":"Fur","value":11646718},{"label":"LYCRA","value":3705463},{"label":"Linen","value":7580284},{"label":"Nylon","value":6444210},{"label":"Cotton","value":3703721},{"label":"Latex","value":12040230},{"label":"Faux Leather","value":187600186},{"label":"Ramie","value":121184716},{"label":"Viscose","value":121441610},{"label":"Polyester","value":7719852},{"label":"Rayon","value":37135247},{"label":"Pu","value":3323086},{"label":"Microfiber","value":8083462},{"label":"Faux Fur","value":48854935},{"label":"Bamboo Fiber","value":93434062}]},{"name":"Color","type":"checkbox","value":[],"options":[{"label":"Dark Gray","value":324542552},{"label":"Yellow","value":3354417},{"label":"White","value":3331185},{"label":"Beige","value":5715155},{"label":"Other","value":4},{"label":" Ivory","value":4173850},{"label":"Khaki","value":4384391},{"label":"Green","value":216016296},{"label":"Blue","value":123085},{"label":"Red","value":3331260},{"label":"Rose Madder","value":314492112},{"label":"Black","value":3327837},{"label":"Pink","value":3328925},{"label":"Light Blue","value":3670345},{"label":"GRAY","value":5412614},{"label":"NAVY","value":4371350},{"label":"Dark blue","value":6899263},{"label":"Silver","value":3460547},{"label":"Brown","value":3399503},{"label":"Purple","value":3851110},{"label":"Gold","value":3331116},{"label":"Orange","value":3558409}]},{"name":"Size","type":"checkbox","value":[],"options":[{"label":"Other","value":4},{"label":"5 XL","value":92869724},{"label":"4 XL","value":104559763},{"label":"Free","value":4348586},{"label":"xs","value":28313},{"label":"S","value":28314},{"label":"M","value":28315},{"label":"L","value":28316},{"label":"XXS","value":28381},{"label":"XL","value":28317},{"label":"XXL","value":28318},{"label":"XXXL","value":28319}]},{"name":"7 days sample order lead time","type":"select","value":"","options":[{"label":"Support","value":6212099},{"label":"Not Support","value":1878262937}]},{"name":"Ethnic Region","type":"input","is_editable":0}],"sku_attributes":[{"item_id":"1119281154","value":[{"item_id":"1119281155"},{"item_id":"1120475509"}]}],"name":"测试多规格","model":"111","group_id":1105655957,"description":"1111","unit":"Barrel","package_volume":11,"package_unit":"Barrel","category_ids":["3","307","30799"]}';
        $skuList = '[{"fob_price":"","disable_flag":0,"sku_code":"1","attributes":[{"item_id":"1119281154","value":{"item_id":"1119281155"}}],"image_file_id":"1120502341"},{"fob_price":"","disable_flag":0,"sku_code":"2","attributes":[{"item_id":"1119281154","value":{"item_id":"1120475509"}}],"image_file_id":"1120502341"}]';
        $params = [
            'data' => $data,
            'sku_list' => $skuList,
            'product_id' => $productId,
        ];
        $this->callAction('edit', $params);
        $this->responseOk();
    }

    public function testDebugEdit()
    {
        $this->loginUser('<EMAIL>');
        $params = [
            'data' => '[{"id":1,"name":"基本信息","fields":[{"id":"product_no","name":"产品编号","base":"1","require":"0","group_id":"1","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","columns":["product_no"],"value":"0169720","format":"0169720"},{"id":"name","name":"产品名称","base":"1","require":"1","group_id":"1","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","columns":["name"],"value":"<EMAIL>","format":"<EMAIL>"},{"id":"39496298","name":"产品规格","base":"0","require":"0","group_id":"1","hint":"tet","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","columns":["39496298"],"value":"","format":""},{"id":"images","name":"产品图片","base":"1","require":"0","group_id":"1","hint":"","field_type":"6","ext_info":[],"default":"","disable_flag":"0","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","columns":["images"],"value":[],"format":[]},{"id":"1102557269","name":"下拉单选字段为64个字符","base":"0","require":"0","group_id":"1","hint":"","field_type":"7","ext_info":["1反反复复付付付反反复复付付付反反复复付付付反反复复付付付反反复复付付付反反复反反复复付付付反反复复付付付反反复复付付付反反反复","2反反复复付付付反反复复付付付反反复复付付付反反复复付付付反反复复付付付反反复反反复复付付付反反复复付付付反反复复付付付反反反复"],"default":"","disable_flag":"0","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","columns":["1102557269"],"value":"","format":""},{"id":"37859878","name":"产品自定义字段111","base":"0","require":"0","group_id":"1","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","columns":["37859878"],"value":"","format":""},{"id":"model","name":"产品型号","base":"1","require":"0","group_id":"1","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","columns":["model"],"value":"","format":""},{"id":"1100200852","name":"测试隐藏","base":"0","require":"0","group_id":"1","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","columns":["1100200852"],"value":"","format":""},{"id":"1100237250","name":"产品颜色","base":"0","require":"0","group_id":"1","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","columns":["1100237250"],"value":"","format":""},{"id":"group_id","name":"产品分组","base":"1","require":"0","group_id":"1","hint":"","field_type":"3","ext_info":[],"default":"","disable_flag":"0","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","columns":["group_id"],"value":"1127490128","format":"无"},{"id":"1100237303","name":"产品规则形状","base":"0","require":"0","group_id":"1","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","columns":["1100237303"],"value":"","format":""},{"id":"description","name":"产品描述","base":"1","require":"0","group_id":"1","hint":"","field_type":"2","ext_info":[],"default":"","disable_flag":"0","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","columns":["description"],"value":"","format":""},{"id":"1100243173","name":"字段测试","base":"0","require":"0","group_id":"1","hint":"","field_type":"1","ext_info":[],"default":"好","disable_flag":"0","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","columns":["1100243173"],"value":"好","format":"好"},{"id":"1100384190","name":"测试新增的多行文本","base":"0","require":"0","group_id":"1","hint":"","field_type":"2","ext_info":[],"default":"","disable_flag":"0","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","columns":["1100384190"],"value":"","format":""},{"id":"1100537214","name":"测试时间","base":"0","require":"0","group_id":"1","hint":"","field_type":"4","ext_info":[],"default":"","disable_flag":"0","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","columns":["1100537214"],"value":"","format":""},{"id":"1100375155","name":"文本框类型的产品字段","base":"0","require":"0","group_id":"1","hint":"123222123","field_type":"1","ext_info":[],"default":"这个是默认值","disable_flag":"0","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","columns":["1100375155"],"value":"这个是默认值","format":"这个是默认值"},{"id":"1101009627","name":"数字类型","base":"0","require":"0","group_id":"1","hint":"","field_type":"5","ext_info":[2],"default":"122.33","disable_flag":"0","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","columns":["1101009627"],"value":"122.33","format":"122.33"},{"id":"1101058789","name":"下拉","base":"0","require":"0","group_id":"1","hint":"","field_type":"3","ext_info":["1","2","3","4","5"],"default":"2","disable_flag":"0","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","columns":["1101058789"],"value":"2","format":"2"},{"id":"37533537","name":"产品测试","base":"0","require":"0","group_id":"1","hint":"we","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","columns":["37533537"],"value":"","format":""},{"id":"1103935296","name":"c1","base":"0","require":"0","group_id":"1","hint":"11","field_type":"1","ext_info":[],"default":"11","disable_flag":"0","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","columns":["1103935296"],"value":"11","format":"11"},{"id":"1103182093","name":"周五1","base":"0","require":"0","group_id":"1","hint":"","field_type":"3","ext_info":["1","2","3"],"default":"","disable_flag":"0","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","columns":["1103182093"],"value":"","format":""},{"id":"1103935322","name":"w w w","base":"0","require":"0","group_id":"1","hint":"","field_type":"3","ext_info":["1","2"],"default":"1","disable_flag":"0","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","columns":["1103935322"],"value":"1","format":"1"},{"id":"1103182094","name":"周五2","base":"0","require":"0","group_id":"1","hint":"","field_type":"7","ext_info":["1","2","3"],"default":"","disable_flag":"0","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","columns":["1103182094"],"value":"","format":""},{"id":"1105787763","name":"33333","base":"0","require":"1","group_id":"1","hint":"33333333333","field_type":"5","ext_info":[5],"default":"333333333333333","disable_flag":"0","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","columns":["1105787763"],"value":"333333333333333","format":"333333333333333"}]},{"id":2,"name":"价格信息","fields":[{"id":"fob","name":"离岸价","base":"1","require":"0","group_id":"2","hint":"","field_type":"0","ext_info":[],"default":"","disable_flag":"0","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","columns":["price_currency","price_min","price_max"],"value":{"price_currency":"","price_min":"0.0000","price_max":"0.0000"},"format":{"price_currency":"","price_min":"0.0000","price_max":"0.0000"}},{"id":"cost_with_tax","name":"含税成本价","base":"1","require":"0","group_id":"2","hint":"","field_type":"0","ext_info":[],"default":"","disable_flag":"0","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","columns":["cost_currency","cost"],"value":{"cost_currency":"","cost":"0.0000"},"format":{"cost_currency":"","cost":"0.0000"}},{"id":"unit","name":"计量单位","base":"1","require":"0","group_id":"2","hint":"","field_type":"3","ext_info":[],"default":"","disable_flag":"0","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","columns":["unit"],"value":"","format":""},{"id":"minimum_order_quantity","name":"最小起订量","base":"1","require":"0","group_id":"2","hint":"","field_type":"0","ext_info":[],"default":"","disable_flag":"0","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","columns":["quantity"],"value":"","format":""}]},{"id":3,"name":"包装信息","fields":[{"id":"32381882","name":"价格信息-单价","base":"0","require":"0","group_id":"3","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","columns":["32381882"],"value":"","format":""},{"id":"38190177","name":"产品价格自定义字段1221","base":"0","require":"0","group_id":"3","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","columns":["38190177"],"value":"","format":""},{"id":"38450075","name":"问问","base":"0","require":"0","group_id":"3","hint":"都是初三的","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","columns":["38450075"],"value":"","format":""},{"id":"1100021250","name":"sd","base":"0","require":"0","group_id":"3","hint":"ds11111111","field_type":"1","ext_info":[],"default":"sde111","disable_flag":"0","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","columns":["1100021250"],"value":"sde111","format":"sde111"},{"id":"package_size","name":"包装尺寸","base":"1","require":"0","group_id":"3","hint":"","field_type":"0","ext_info":[],"default":"","disable_flag":"0","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","columns":["package_size_length","package_size_weight","package_size_height"],"value":{"package_size_length":"0.00","package_size_weight":"0.00","package_size_height":"0.00"},"format":{"package_size_length":"0.00","package_size_weight":"0.00","package_size_height":"0.00"}},{"id":"package_volume","name":"包装体积","base":"1","require":"0","group_id":"3","hint":"","field_type":"5","ext_info":[2],"default":"","disable_flag":"0","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","columns":["package_volume"],"value":"","format":""},{"id":"1100237329","name":"最大优惠价格","base":"0","require":"0","group_id":"3","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","columns":["1100237329"],"value":"","format":""},{"id":"package_gross_weight","name":"包装毛重","base":"1","require":"0","group_id":"3","hint":"","field_type":"5","ext_info":[2],"default":"","disable_flag":"0","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","columns":["package_gross_weight"],"value":"","format":""},{"id":"count_per_package","name":"每包装产品数","base":"1","require":"0","group_id":"3","hint":"","field_type":"5","ext_info":[],"default":"","disable_flag":"0","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","columns":["count_per_package"],"value":"","format":""},{"id":"**********","name":"测试字段","base":"0","require":"0","group_id":"3","hint":"","field_type":"7","ext_info":["sdf","dsfsdf","fsdf"],"default":"","disable_flag":"0","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","columns":["**********"],"value":"","format":""},{"id":"package_remark","name":"包装说明","base":"1","require":"0","group_id":"3","hint":"","field_type":"2","ext_info":[],"default":"","disable_flag":"0","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","columns":["package_remark"],"value":"","format":""},{"id":"**********","name":"ccc","base":"0","require":"0","group_id":"3","hint":"IMEE GROUP LTD EURO Dollar Payment, the remittance instruction route:  *57A: Account with institutio","field_type":"2","ext_info":[],"default":"","disable_flag":"0","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","columns":["**********"],"value":"","format":""},{"id":"**********","name":"下拉单选的文本框类型","base":"0","require":"0","group_id":"3","hint":"","field_type":"3","ext_info":["11","2234","32341","535","55","5","666"],"default":"","disable_flag":"0","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","columns":["**********"],"value":"","format":""}]},{"id":4,"name":"更多信息","fields":[{"id":"category_ids","name":"产品类目","base":"1","require":"1","group_id":"4","hint":"","field_type":"0","ext_info":[],"default":"","disable_flag":"0","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","columns":["category_ids"],"value":[5,540],"format":[5,540]},{"id":"hs_code","name":"海关编码","base":"1","require":"0","group_id":"4","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","columns":["hs_code"],"value":"","format":""},{"id":"info_json","name":"产品属性","base":"1","require":"0","group_id":"4","hint":"","field_type":"0","ext_info":[],"default":"","disable_flag":"0","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","columns":["info_json"],"value":[{"name":"Safety Standards","type":"select","value":""},{"name":"Breaking Capacity","type":"select","value":""},{"name":"Usage","type":"select","value":""},{"name":"Model Number","type":"input","is_editable":0,"value":""},{"name":"Brand Name","type":"input","is_editable":0,"value":""},{"name":"Place of Origin","type":"country","value":""}],"format":[{"name":"Safety Standards","type":"select","value":"","options":[{"label":"Other","value":"4"},{"label":"CSA","value":"156"},{"label":"IEC","value":"157"}]},{"name":"Breaking Capacity","type":"select","value":"","options":[{"label":"Other","value":"4"},{"label":"High","value":"154"},{"label":"Low","value":"155"}]},{"name":"Usage","type":"select","value":"","options":[{"label":"Other","value":"4"},{"label":"Automotive","value":"158"},{"label":"High Voltage","value":"149"},{"label":"Low Voltage","value":"159"},{"label":"Plug","value":"160"},{"label":"Resettable","value":"161"},{"label":"Thermal","value":"162"}]},{"name":"Model Number","type":"input","is_editable":0,"value":""},{"name":"Brand Name","type":"input","is_editable":0,"value":""},{"name":"Place of Origin","type":"country","value":""}]},{"id":"from_url","name":"产品链接","base":"1","require":"0","group_id":"4","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","columns":["from_url"],"value":"","format":""},{"id":"product_remark","name":"产品备注","base":"1","require":"0","group_id":"4","hint":"","field_type":"2","ext_info":[],"default":"","disable_flag":"0","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","columns":["product_remark"],"value":"","format":""}]},{"id":5,"name":"产品规格","fields":[{"id":"38190265","name":"描述自定义","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","columns":["38190265"],"value":"","format":""},{"id":"1100375290","name":"多行文本框","base":"0","require":"1","group_id":"5","hint":"产品多行文本框","field_type":"2","ext_info":[],"default":"大幅度","disable_flag":"0","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","columns":["1100375290"],"value":"","format":""},{"id":"sku_attributes","name":"产品规格","base":"1","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","columns":["sku_attributes"],"value":[],"format":[]},{"id":"1104255363","name":" 报价日期","base":"0","require":"0","group_id":"5","hint":"","field_type":"4","ext_info":[],"default":"","disable_flag":"0","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","columns":["1104255363"],"value":"","format":""}]},{"id":0,"name":"系统信息","fields":[{"id":"create_time","name":"创建时间","base":"1","require":"0","group_id":"0","hint":"","field_type":"1","ext_info":"","default":"","disable_flag":"0","is_editable":"1","unique_check":"0","unique_prevent":"","unique_message":"","columns":["create_time"],"value":"2021-07-15 14:19:26","format":"2021-07-15 14:19:26"},{"id":"create_user","name":"创建人","base":"1","require":"0","group_id":"0","hint":"","field_type":"1","ext_info":"","default":"","disable_flag":"0","is_editable":"1","unique_check":"0","unique_prevent":"","unique_message":"","columns":["create_user"],"value":11863794,"format":{"nickname":"熊一","email":"<EMAIL>","avatar":"","user_id":"11863794"}},{"id":"source_type","name":"创建方式","base":"1","require":"0","group_id":"0","hint":"","field_type":"1","ext_info":"","default":"","disable_flag":"0","is_editable":"1","unique_check":"0","unique_prevent":"","unique_message":"","columns":["source_type"],"value":0,"format":"手动创建"},{"id":"update_user","name":"修改人","base":"1","require":"0","group_id":"0","hint":"","field_type":"1","ext_info":"","default":"","disable_flag":"0","is_editable":"1","unique_check":"0","unique_prevent":"","unique_message":"","columns":["update_user"],"value":0,"format":{"nickname":null,"email":null,"avatar":null,"user_id":null}},{"id":"update_time","name":"修改时间","base":"1","require":"0","group_id":"0","hint":"","field_type":"1","ext_info":"","default":"","disable_flag":"0","is_editable":"1","unique_check":"0","unique_prevent":"","unique_message":"","columns":["update_time"],"value":"2021-07-15 14:19:26","format":"2021-07-15 14:19:26"},{"id":"product_type","name":"产品类型","base":"1","require":"0","group_id":"0","hint":"","field_type":"1","ext_info":"","default":"","disable_flag":"0","is_editable":"1","unique_check":"0","unique_prevent":"","unique_message":"","columns":["product_type"],"value":1,"format":"普通产品"},{"id":"ali_product_id","name":"阿里商品ID","base":"1","require":"0","group_id":"0","hint":"","field_type":"1","ext_info":"","default":"","disable_flag":"0","is_editable":"1","unique_check":"0","unique_prevent":"","unique_message":"","columns":["ali_product_id"],"value":0,"format":""},{"id":"ali_store_id","name":"来源店铺","base":"1","require":"0","group_id":"0","hint":"","field_type":"1","ext_info":"","default":"","disable_flag":"0","is_editable":"1","unique_check":"0","unique_prevent":"","unique_message":"","columns":["ali_store_id"],"value":0,"format":""}]}]',
            'product_id' => 1127486249
        ];
        $this->callAction('edit', $params);
        $this->responseOk();
    }

    public function testSubmitAliSync()
    {
        $this->loginUser('<EMAIL>');
        $clientId = \User::getLoginUser()->getClientId();
//        QueueHelper::unlockProductSyncTask($clientId, 248100056);
        $params = [
            'store_id' => ['248100056']
        ];

        $this->callAction('submitAliSync', $params);
        $this->responseOk();
    }

    public function testMoveProductGroup()
    {
//        $this->loginUser('<EMAIL>');
        $this->loginUser('<EMAIL>');
        $params = [
            'product_ids' => [1126661431,],
//            'group_id' => 1105655957,
            'group_id' => rand(0,1),
        ];
        $this->callAction('MoveProductGroup', $params);
        $this->responseOk();
    }

    public function testImport()
    {
        $this->loginUser('<EMAIL>');
        $params = [
            'file_id' => 1124821856,
            'replace_flag' => 0,
            'external_field_data' => [
                [
                    'base' =>1,
                    'source_id' => '产品编号',
                    'target_id' => 'product_no'
                ],
                [
                    'base' =>1,
                    'source_id' => '产品型号',
                    'target_id' => 'model'
                ],
                [
                    'base' =>1,
                    'source_id' => '产品名称必填',
                    'target_id' => 'product_no'
                ]
            ]
        ];
        $this->callAction('Import', $params);
        $this->responseOk();
    }
}
