<?php

namespace tests\functional\ai_sdr;

use common\library\ai_sdr\AISdrService;
use common\library\ai_sdr\Constant;
use common\library\ai_sdr\SdrDetailExecutor;
use common\library\ai_sdr\task\AiSdrTask;
use common\library\ai_sdr\task\AiSdrTaskFilter;
use common\library\ai_sdr\task_detail\AiSdrTaskDetail;
use common\library\ai_sdr\task_detail\AiSdrTaskDetailFilter;

/**
 * AI SDR 基础功能测试
 * 
 * 测试AI SDR的基础功能，避免复杂的数据库操作
 */
class AiSdrBasicFunctionalTest extends \WebFunctionalTestCase
{
    protected $testClientId = 333392;
    protected $testUserId = 249519530;
    
    protected function setUp(): void
    {
        parent::setUp();
        $this->loginAsQqlite_01();
    }
    
    /**
     * 测试AISdrService实例化
     */
    public function testAISdrServiceInstantiation_WithValidParameters_CreatesInstance()
    {
        // Act
        $service = new AISdrService($this->testClientId, $this->testUserId);
        
        // Assert
        $this->assertInstanceOf(AISdrService::class, $service, 'Should create AISdrService instance');
        $this->assertTrue(true, 'AISdrService instantiation works correctly');
    }
    
    /**
     * 测试获取任务列表
     */
    public function testGetTaskList_WithValidService_ReturnsArray()
    {
        try {
            // Arrange
            $service = new AISdrService($this->testClientId, $this->testUserId);
            
            // Act
            $tasks = $service->getTaskList();
            
            // Assert
            $this->assertIsArray($tasks, 'Task list should be an array');
            
            // 如果有任务，验证结构
            if (!empty($tasks)) {
                foreach ($tasks as $source => $task) {
                    $this->assertNotEquals(Constant::TASK_SOURCE_SYSTEM, $source, 'Should not include system tasks');
                    if (is_array($task)) {
                        $this->assertArrayHasKey('task_id', $task, 'Task should have task_id');
                    }
                }
            }
            
            $this->assertTrue(true, 'Get task list works correctly');
        } catch (\Exception $e) {
            $this->markTestSkipped('Get task list failed: ' . $e->getMessage());
        }
    }
    
    /**
     * 测试任务过滤器
     */
    public function testTaskFilter_WithBasicConditions_WorksCorrectly()
    {
        try {
            // Arrange
            $filter = new AiSdrTaskFilter($this->testClientId);
            
            // Act
            $filter->task_status = Constant::AI_SDR_TASK_STATUS_PROCESSING;
            $filter->limit(5);
            $filter->order('create_time', 'desc');
            
            $tasks = $filter->find();
            $count = $filter->count();
            
            // Assert
            $this->assertNotNull($tasks, 'Filter should return results');
            $this->assertIsInt($count, 'Count should return integer');
            $this->assertGreaterThanOrEqual(0, $count, 'Count should be non-negative');
            
            $this->assertTrue(true, 'Task filter works correctly');
        } catch (\Exception $e) {
            $this->markTestSkipped('Task filter test failed: ' . $e->getMessage());
        }
    }
    
    /**
     * 测试任务详情过滤器
     */
    public function testTaskDetailFilter_WithBasicConditions_WorksCorrectly()
    {
        try {
            // Arrange
            $filter = new AiSdrTaskDetailFilter($this->testClientId);
            
            // Act
            $filter->status = Constant::DETAIL_STATUS_ADD;
            $filter->enable_flag = 1;
            $filter->limit(10);
            
            $details = $filter->find();
            $count = $filter->count();
            
            // Assert
            $this->assertNotNull($details, 'Filter should return results');
            $this->assertIsInt($count, 'Count should return integer');
            $this->assertGreaterThanOrEqual(0, $count, 'Count should be non-negative');
            
            $this->assertTrue(true, 'Task detail filter works correctly');
        } catch (\Exception $e) {
            $this->markTestSkipped('Task detail filter test failed: ' . $e->getMessage());
        }
    }
    
    /**
     * 测试SdrDetailExecutor实例化
     */
    public function testSdrDetailExecutorInstantiation_WithValidParameters_CreatesInstance()
    {
        // Act
        $executor = new SdrDetailExecutor($this->testClientId, $this->testUserId);
        
        // Assert
        $this->assertInstanceOf(SdrDetailExecutor::class, $executor, 'Should create SdrDetailExecutor instance');
        $this->assertTrue(true, 'SdrDetailExecutor instantiation works correctly');
    }
    
    /**
     * 测试常量定义
     */
    public function testConstants_AreDefinedAndConsistent()
    {
        // 测试任务状态常量
        $this->assertEquals(0, Constant::AI_SDR_TASK_STATUS_DRAFT);
        $this->assertEquals(1, Constant::AI_SDR_TASK_STATUS_PROCESSING);
        $this->assertEquals(2, Constant::AI_SDR_TASK_STATUS_PAUSED);
        $this->assertEquals(3, Constant::AI_SDR_TASK_STATUS_FINISHED);
        
        // 测试详情状态常量
        $this->assertEquals(0, Constant::DETAIL_STATUS_ADD);
        $this->assertEquals(1, Constant::DETAIL_STATUS_LABEL);
        $this->assertEquals(2, Constant::DETAIL_STATUS_BACKGROUND_CHECKING);
        $this->assertEquals(4, Constant::DETAIL_STATUS_VALIDATE_CONTACTS);
        $this->assertEquals(5, Constant::DETAIL_STATUS_CREATE_MARKETING_PLAN);
        $this->assertEquals(6, Constant::DETAIL_STATUS_EXECUTE_MARKETING_PLAN);
        $this->assertEquals(-1, Constant::DETAIL_STATUS_ERROR);
        
        // 测试质量常量
        $this->assertEquals(0, Constant::LEAD_QUALITY_UNKNOWN);
        $this->assertEquals(1, Constant::LEAD_QUALITY_LOW);
        $this->assertEquals(2, Constant::LEAD_QUALITY_MEDIUM);
        $this->assertEquals(3, Constant::LEAD_QUALITY_HIGH);
        
        $this->assertTrue(true, 'Constants are defined and consistent');
    }
    
    /**
     * 测试Redis缓存键格式
     */
    public function testRedisCacheKeyFormats_AreCorrect()
    {
        $clientId = $this->testClientId;
        $taskId = 12345;
        
        // 测试任务缓存键格式
        $taskKey = sprintf(Constant::REDIS_CACHE_TASK_KEY, $clientId, $taskId);
        $this->assertIsString($taskKey, 'Task cache key should be string');
        $this->assertStringContainsString((string)$clientId, $taskKey, 'Task cache key should contain client ID');
        $this->assertStringContainsString((string)$taskId, $taskKey, 'Task cache key should contain task ID');
        
        // 测试每日限制缓存键格式
        $limitKey = sprintf(Constant::TASK_DAILY_LIMIT_CACHE_KEY, $clientId, $taskId);
        $this->assertIsString($limitKey, 'Limit cache key should be string');
        $this->assertStringContainsString((string)$clientId, $limitKey, 'Limit cache key should contain client ID');
        
        $this->assertTrue(true, 'Redis cache key formats are correct');
    }
    
    /**
     * 测试数据模型实例化
     */
    public function testDataModelInstantiation_WithValidParameters_CreatesInstances()
    {
        // 测试AiSdrTask实例化
        $task = new AiSdrTask($this->testClientId);
        $this->assertInstanceOf(AiSdrTask::class, $task, 'Should create AiSdrTask instance');
        $this->assertTrue($task->isNew(), 'New task should be marked as new');
        
        // 测试AiSdrTaskDetail实例化
        $detail = new AiSdrTaskDetail($this->testClientId);
        $this->assertInstanceOf(AiSdrTaskDetail::class, $detail, 'Should create AiSdrTaskDetail instance');
        $this->assertTrue($detail->isNew(), 'New detail should be marked as new');
        
        $this->assertTrue(true, 'Data model instantiation works correctly');
    }
    
    /**
     * 测试状态转换逻辑验证
     */
    public function testStatusTransitionLogic_IsConsistent()
    {
        // 测试状态值的逻辑性
        $this->assertLessThan(Constant::DETAIL_STATUS_LABEL, Constant::DETAIL_STATUS_ADD, 'ADD should be less than LABEL');
        $this->assertLessThan(Constant::DETAIL_STATUS_BACKGROUND_CHECKING, Constant::DETAIL_STATUS_LABEL, 'LABEL should be less than BACKGROUND_CHECKING');
        $this->assertLessThan(Constant::DETAIL_STATUS_VALIDATE_CONTACTS, Constant::DETAIL_STATUS_BACKGROUND_CHECKING, 'BACKGROUND_CHECKING should be less than VALIDATE_CONTACTS');
        
        // 测试阶段值的逻辑性
        $this->assertLessThan(Constant::AI_SDR_STAGE_REACHABLE, Constant::AI_SDR_STAGE_DIG, 'DIG should be less than REACHABLE');
        $this->assertLessThan(Constant::AI_SDR_STAGE_MARKETING, Constant::AI_SDR_STAGE_REACHABLE, 'REACHABLE should be less than MARKETING');
        $this->assertLessThan(Constant::AI_SDR_STAGE_EFFECTIVE, Constant::AI_SDR_STAGE_MARKETING, 'MARKETING should be less than EFFECTIVE');
        
        $this->assertTrue(true, 'Status transition logic is consistent');
    }
    
    /**
     * 测试限制常量的合理性
     */
    public function testLimitConstants_AreReasonable()
    {
        // 测试每日限制
        $this->assertGreaterThan(0, Constant::DAILY_LIMIT, 'Daily limit should be positive');
        $this->assertLessThan(1000, Constant::DAILY_LIMIT, 'Daily limit should be reasonable');
        
        // 测试潜在客户数量限制
        $this->assertGreaterThan(0, Constant::AI_SDR_POTENTIAL_CUSTOMER_COUNT_LIMIT, 'Customer count limit should be positive');
        $this->assertGreaterThan(Constant::DAILY_LIMIT, Constant::AI_SDR_POTENTIAL_CUSTOMER_COUNT_LIMIT, 'Customer count limit should be greater than daily limit');
        
        // 测试无限制标识
        $this->assertEquals(-1, Constant::TOTAL_LIMIT_UNLIMITED, 'Unlimited should be -1');
        
        $this->assertTrue(true, 'Limit constants are reasonable');
    }
    
    /**
     * 测试记录类型常量的完整性
     */
    public function testRecordTypeConstants_AreComplete()
    {
        $recordTypes = [
            Constant::RECORD_TYPE_ADD_LEAD,
            Constant::RECORD_TYPE_BACKGROUND_CHECK,
            Constant::RECORD_TYPE_ANALYZE_QUALITY,
            Constant::RECORD_TYPE_CHECK_CONTACTS,
            Constant::RECORD_TYPE_CREATE_MARKET_PLAN,
            Constant::RECORD_TYPE_EXECUTE_MARKET_PLAN
        ];
        
        // 验证所有记录类型都是非负整数
        foreach ($recordTypes as $type) {
            $this->assertIsInt($type, "Record type {$type} should be integer");
            $this->assertGreaterThanOrEqual(0, $type, "Record type {$type} should be non-negative");
        }
        
        // 验证记录类型值的唯一性
        $this->assertEquals(count($recordTypes), count(array_unique($recordTypes)), 'Record type values should be unique');
        
        $this->assertTrue(true, 'Record type constants are complete');
    }
    
    /**
     * 测试工作流配置文件存在性
     */
    public function testWorkflowConfigFile_Exists()
    {
        $workflowPath = dirname(__DIR__, 3) . '/library/ai_sdr/config/workflow.yaml';
        
        if (file_exists($workflowPath)) {
            $this->assertFileExists($workflowPath, 'Workflow configuration file should exist');
            
            $content = file_get_contents($workflowPath);
            $this->assertNotEmpty($content, 'Workflow file should not be empty');
            
            // 检查基本的YAML结构
            $this->assertStringContainsString('type:', $content, 'Workflow should have type definition');
            $this->assertStringContainsString('places:', $content, 'Workflow should have places definition');
            $this->assertStringContainsString('transitions:', $content, 'Workflow should have transitions definition');
            
            $this->assertTrue(true, 'Workflow configuration file exists and has basic structure');
        } else {
            $this->markTestSkipped('Workflow configuration file not found');
        }
    }
}
