<?php

namespace tests\functional\ai_sdr;

use common\library\ai_sdr\Constant;
use common\library\ai_sdr\SdrDetailExecutor;
use common\library\ai_sdr\SdrLeadDetail;
use common\library\ai_agent\SdrLeadQualityAnalysisAgent;
use common\library\ai_agent\SdrEdmWriteAgent;
use common\library\AiBackgroundService;
use tests\unit\ai_sdr\MockServices;
use tests\unit\ai_sdr\AiSdrTestDataFactory;

/**
 * AI服务集成测试
 * 
 * 测试AI SDR与各种AI服务的集成
 */
class AiServiceIntegrationTest extends \WebFunctionalTestCase
{
    protected $testClientId = 333392;
    protected $testUserId = 249519530;
    
    protected function setUp(): void
    {
        parent::setUp();
        $this->loginAsQqlite_01();
    }
    
    /**
     * 测试质量分析AI服务集成
     */
    public function testQualityAnalysisIntegration_WithValidInput_ReturnsExpectedResult()
    {
        try {
            // 创建Mock AI服务
            $mockAiServices = MockServices::createMockAiServices();
            
            // 设置预期的质量分析结果
            $expectedResult = AiSdrTestDataFactory::createQualityAnalysisResultData([
                'answer' => [
                    'lead_quality' => Constant::LEAD_QUALITY_HIGH,
                    'confidence' => 0.9,
                    'reason' => ['Excellent product match', 'High purchase potential']
                ]
            ]);
            
            $mockAiServices->setQualityAnalysisResult($expectedResult);
            
            // 执行质量分析
            $result = $mockAiServices->getQualityAnalysisResult();
            
            // 验证结果
            $this->assertArrayHasKey('answer', $result, 'Result should contain answer');
            $this->assertArrayHasKey('usage', $result, 'Result should contain usage info');
            $this->assertArrayHasKey('model', $result, 'Result should contain model info');
            
            $answer = $result['answer'];
            $this->assertEquals(Constant::LEAD_QUALITY_HIGH, $answer['lead_quality'], 'Quality should be HIGH');
            $this->assertEquals(0.9, $answer['confidence'], 'Confidence should be 0.9');
            $this->assertIsArray($answer['reason'], 'Reason should be array');
            
            $this->assertTrue($mockAiServices->wasQualityAnalysisCalled(), 'Quality analysis should be called');
            
            $this->assertTrue(true, 'Quality analysis integration works correctly');
        } catch (\Exception $e) {
            $this->markTestSkipped('Quality analysis integration test failed: ' . $e->getMessage());
        }
    }
    
    /**
     * 测试背景调研AI服务集成
     */
    public function testBackgroundCheckIntegration_WithValidInput_CreatesBackgroundTask()
    {
        try {
            // 创建Mock AI服务
            $mockAiServices = MockServices::createMockAiServices();
            
            // 设置预期的背景调研结果
            $expectedResult = AiSdrTestDataFactory::createBackgroundCheckResultData([
                'task_id' => 54321,
                'status' => 'completed',
                'report' => [
                    'company_name' => 'Integration Test Company',
                    'country' => 'US',
                    'homepage' => 'https://integrationtest.com',
                    'employees_min' => 100,
                    'employees_max' => 500
                ]
            ]);
            
            $mockAiServices->setBackgroundCheckResult($expectedResult);
            
            // 执行背景调研
            $result = $mockAiServices->getBackgroundCheckResult();
            
            // 验证结果
            $this->assertArrayHasKey('task_id', $result, 'Result should contain task_id');
            $this->assertArrayHasKey('status', $result, 'Result should contain status');
            $this->assertArrayHasKey('report', $result, 'Result should contain report');
            
            $this->assertEquals(54321, $result['task_id'], 'Task ID should match');
            $this->assertEquals('completed', $result['status'], 'Status should be completed');
            
            $report = $result['report'];
            $this->assertArrayHasKey('company_name', $report, 'Report should contain company name');
            $this->assertArrayHasKey('country', $report, 'Report should contain country');
            $this->assertArrayHasKey('homepage', $report, 'Report should contain homepage');
            
            $this->assertTrue($mockAiServices->wasBackgroundCheckCalled(), 'Background check should be called');
            
            $this->assertTrue(true, 'Background check integration works correctly');
        } catch (\Exception $e) {
            $this->markTestSkipped('Background check integration test failed: ' . $e->getMessage());
        }
    }
    
    /**
     * 测试营销内容生成AI服务集成
     */
    public function testMarketingContentIntegration_WithValidInput_GeneratesContent()
    {
        try {
            // 创建Mock AI服务
            $mockAiServices = MockServices::createMockAiServices();
            
            // 设置预期的营销内容结果
            $expectedResult = AiSdrTestDataFactory::createMarketingContentResultData([
                'subject' => 'Partnership Opportunity with Your Company',
                'content' => 'Dear Sir/Madam, We would like to explore potential partnership opportunities...',
                'round' => 1,
                'tone' => 'professional',
                'language' => 'en'
            ]);
            
            $mockAiServices->setMarketingContentResult($expectedResult);
            
            // 执行营销内容生成
            $result = $mockAiServices->getMarketingContentResult();
            
            // 验证结果
            $this->assertIsArray($result, 'Result should be array');
            $this->assertNotEmpty($result, 'Result should not be empty');
            
            $firstEmail = $result[0];
            $this->assertArrayHasKey('subject', $firstEmail, 'Email should have subject');
            $this->assertArrayHasKey('content', $firstEmail, 'Email should have content');
            $this->assertArrayHasKey('round', $firstEmail, 'Email should have round');
            $this->assertArrayHasKey('tone', $firstEmail, 'Email should have tone');
            $this->assertArrayHasKey('language', $firstEmail, 'Email should have language');
            
            $this->assertNotEmpty($firstEmail['subject'], 'Subject should not be empty');
            $this->assertNotEmpty($firstEmail['content'], 'Content should not be empty');
            $this->assertEquals(1, $firstEmail['round'], 'Round should be 1');
            
            $this->assertTrue($mockAiServices->wasMarketingContentCalled(), 'Marketing content generation should be called');
            
            $this->assertTrue(true, 'Marketing content integration works correctly');
        } catch (\Exception $e) {
            $this->markTestSkipped('Marketing content integration test failed: ' . $e->getMessage());
        }
    }
    
    /**
     * 测试AI服务失败处理
     */
    public function testAiServiceFailure_WithTimeout_HandlesGracefully()
    {
        try {
            // 创建Mock AI服务
            $mockAiServices = MockServices::createMockAiServices();
            
            // 设置AI服务抛出异常
            $exception = new \Exception('AI service timeout');
            $mockAiServices->setQualityAnalysisException($exception);
            
            // 尝试调用AI服务
            try {
                $result = $mockAiServices->getQualityAnalysisResult();
                $this->fail('Should have thrown exception');
            } catch (\Exception $e) {
                $this->assertEquals('AI service timeout', $e->getMessage(), 'Exception message should match');
            }
            
            $this->assertTrue($mockAiServices->wasQualityAnalysisCalled(), 'Service should have been called');
            
            $this->assertTrue(true, 'AI service failure handling works correctly');
        } catch (\Exception $e) {
            $this->markTestSkipped('AI service failure test failed: ' . $e->getMessage());
        }
    }
    
    /**
     * 测试多轮AI服务调用
     */
    public function testMultipleAiServiceCalls_WithDifferentInputs_ReturnsConsistentResults()
    {
        try {
            // 创建Mock AI服务
            $mockAiServices = MockServices::createMockAiServices();
            
            // 设置多个质量分析结果
            $results = [
                ['answer' => ['lead_quality' => Constant::LEAD_QUALITY_HIGH]],
                ['answer' => ['lead_quality' => Constant::LEAD_QUALITY_MEDIUM]],
                ['answer' => ['lead_quality' => Constant::LEAD_QUALITY_LOW]]
            ];
            
            $mockAiServices->setQualityAnalysisResults($results);
            
            // 执行多次调用
            $result1 = $mockAiServices->getQualityAnalysisResult();
            $result2 = $mockAiServices->getQualityAnalysisResult();
            $result3 = $mockAiServices->getQualityAnalysisResult();
            
            // 验证结果
            $this->assertEquals(Constant::LEAD_QUALITY_HIGH, $result1['answer']['lead_quality']);
            $this->assertEquals(Constant::LEAD_QUALITY_MEDIUM, $result2['answer']['lead_quality']);
            $this->assertEquals(Constant::LEAD_QUALITY_LOW, $result3['answer']['lead_quality']);
            
            $this->assertTrue($mockAiServices->wasQualityAnalysisCalled(), 'Service should have been called');
            
            $this->assertTrue(true, 'Multiple AI service calls work correctly');
        } catch (\Exception $e) {
            $this->markTestSkipped('Multiple AI service calls test failed: ' . $e->getMessage());
        }
    }
    
    /**
     * 测试AI服务与状态机集成
     */
    public function testAiServiceWithStateMachine_ProcessesCorrectly()
    {
        try {
            // 创建测试数据
            $detailData = AiSdrTestDataFactory::createTaskDetailData(12345, [
                'status' => Constant::DETAIL_STATUS_ADD,
                'lead_quality' => Constant::LEAD_QUALITY_UNKNOWN
            ]);
            
            $sdrDetail = new SdrLeadDetail($this->testClientId);
            $sdrDetail->initFromSingle($detailData);
            
            // 创建状态机执行器
            $executor = new SdrDetailExecutor($this->testClientId, $this->testUserId);
            
            // 设置客户档案
            $clientProfile = AiSdrTestDataFactory::createClientProfileData();
            $executor->setClientProfile($clientProfile);
            
            // 验证执行器创建成功
            $this->assertInstanceOf(SdrDetailExecutor::class, $executor, 'Executor should be created');
            
            // 验证详情数据
            $this->assertEquals(12345, $sdrDetail->task_id, 'Task ID should match');
            $this->assertEquals(Constant::DETAIL_STATUS_ADD, $sdrDetail->status, 'Status should be ADD');
            
            $this->assertTrue(true, 'AI service with state machine integration works correctly');
        } catch (\Exception $e) {
            $this->markTestSkipped('AI service with state machine test failed: ' . $e->getMessage());
        }
    }
    
    /**
     * 测试推荐API集成
     */
    public function testRecommendApiIntegration_WithValidDomains_ReturnsCompanyProfiles()
    {
        try {
            // 创建Mock推荐API
            $mockRecommendApi = MockServices::createMockRecommendApi();
            
            // 设置测试域名和响应
            $testDomains = ['example.com', 'testcompany.com'];
            $expectedResponse = AiSdrTestDataFactory::createRecommendApiResponseData([
                'example.com' => [
                    'company_name' => 'Example Corp',
                    'main_products' => ['Software', 'Services'],
                    'company_type' => ['Technology'],
                    'country' => 'US'
                ]
            ]);
            
            $mockRecommendApi->setResponse($testDomains, $expectedResponse['example.com']);
            
            // 调用推荐API
            $result = $mockRecommendApi->getCompanyProfileByDomains($testDomains);
            
            // 验证结果
            $this->assertIsArray($result, 'Result should be array');
            $this->assertArrayHasKey('example.com', $result, 'Result should contain example.com');
            
            $companyProfile = $result['example.com'];
            $this->assertArrayHasKey('company_name', $companyProfile, 'Profile should have company name');
            $this->assertArrayHasKey('main_products', $companyProfile, 'Profile should have main products');
            $this->assertArrayHasKey('company_type', $companyProfile, 'Profile should have company type');
            
            $this->assertTrue($mockRecommendApi->wasMethodCalled('getCompanyProfileByDomains'), 'API method should be called');
            
            $this->assertTrue(true, 'Recommend API integration works correctly');
        } catch (\Exception $e) {
            $this->markTestSkipped('Recommend API integration test failed: ' . $e->getMessage());
        }
    }
    
    /**
     * 测试线索自动归档集成
     */
    public function testLeadAutoArchiveIntegration_WithValidDomains_CreatesLeads()
    {
        try {
            // 创建Mock线索自动归档
            $mockLeadAutoArchive = MockServices::createMockLeadAutoArchive();
            
            // 设置测试域名
            $testDomains = ['newlead.com'];
            $leadData = AiSdrTestDataFactory::createLeadData([
                'website' => 'https://newlead.com',
                'company_name' => 'New Lead Company'
            ]);
            
            $mockLeadAutoArchive->setLeadForDomain('newlead.com', (object) $leadData);
            
            // 调用线索归档
            $result = $mockLeadAutoArchive->archiveByBatchDomain($testDomains, true);
            
            // 验证结果
            $this->assertIsArray($result, 'Result should be array');
            $this->assertArrayHasKey('newlead.com', $result, 'Result should contain newlead.com');
            
            $lead = $result['newlead.com'];
            $this->assertIsObject($lead, 'Lead should be object');
            $this->assertEquals('https://newlead.com', $lead->website, 'Website should match');
            $this->assertEquals('New Lead Company', $lead->company_name, 'Company name should match');
            
            $callLog = $mockLeadAutoArchive->getCallLog();
            $this->assertNotEmpty($callLog, 'Call log should not be empty');
            
            $this->assertTrue(true, 'Lead auto archive integration works correctly');
        } catch (\Exception $e) {
            $this->markTestSkipped('Lead auto archive integration test failed: ' . $e->getMessage());
        }
    }
    
    /**
     * 测试完整的AI服务工作流
     */
    public function testCompleteAiWorkflow_WithAllServices_ProcessesSuccessfully()
    {
        try {
            // 创建所有Mock服务
            $mockAiServices = MockServices::createMockAiServices();
            $mockRecommendApi = MockServices::createMockRecommendApi();
            $mockLeadAutoArchive = MockServices::createMockLeadAutoArchive();
            
            // 设置工作流数据
            $domain = 'workflow-test.com';
            
            // 1. 推荐API获取公司信息
            $companyProfile = AiSdrTestDataFactory::createBuyerProfileData([
                'company_name' => 'Workflow Test Company'
            ]);
            $mockRecommendApi->setResponse([$domain], $companyProfile);
            
            // 2. 线索自动归档
            $leadData = AiSdrTestDataFactory::createLeadData([
                'website' => "https://{$domain}",
                'company_name' => $companyProfile['company_name']
            ]);
            $mockLeadAutoArchive->setLeadForDomain($domain, (object) $leadData);
            
            // 3. AI质量分析
            $qualityResult = AiSdrTestDataFactory::createQualityAnalysisResultData([
                'answer' => ['lead_quality' => Constant::LEAD_QUALITY_HIGH]
            ]);
            $mockAiServices->setQualityAnalysisResult($qualityResult);
            
            // 4. 背景调研
            $backgroundResult = AiSdrTestDataFactory::createBackgroundCheckResultData();
            $mockAiServices->setBackgroundCheckResult($backgroundResult);
            
            // 5. 营销内容生成
            $marketingResult = AiSdrTestDataFactory::createMarketingContentResultData();
            $mockAiServices->setMarketingContentResult($marketingResult);
            
            // 执行完整工作流
            $step1 = $mockRecommendApi->getCompanyProfileByDomains([$domain]);
            $step2 = $mockLeadAutoArchive->archiveByBatchDomain([$domain], true);
            $step3 = $mockAiServices->getQualityAnalysisResult();
            $step4 = $mockAiServices->getBackgroundCheckResult();
            $step5 = $mockAiServices->getMarketingContentResult();
            
            // 验证每个步骤
            $this->assertArrayHasKey($domain, $step1, 'Step 1: Company profile should be retrieved');
            $this->assertArrayHasKey($domain, $step2, 'Step 2: Lead should be created');
            $this->assertEquals(Constant::LEAD_QUALITY_HIGH, $step3['answer']['lead_quality'], 'Step 3: Quality should be analyzed');
            $this->assertArrayHasKey('task_id', $step4, 'Step 4: Background check should be initiated');
            $this->assertNotEmpty($step5, 'Step 5: Marketing content should be generated');
            
            // 验证所有服务都被调用
            $this->assertTrue($mockRecommendApi->wasMethodCalled('getCompanyProfileByDomains'), 'Recommend API should be called');
            $this->assertTrue($mockAiServices->wasQualityAnalysisCalled(), 'Quality analysis should be called');
            $this->assertTrue($mockAiServices->wasBackgroundCheckCalled(), 'Background check should be called');
            $this->assertTrue($mockAiServices->wasMarketingContentCalled(), 'Marketing content should be called');
            
            $this->assertTrue(true, 'Complete AI workflow works correctly');
        } catch (\Exception $e) {
            $this->markTestSkipped('Complete AI workflow test failed: ' . $e->getMessage());
        }
    }
}
