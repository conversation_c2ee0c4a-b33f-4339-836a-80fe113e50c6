<?php

namespace tests\functional\ai_sdr;

use common\library\ai_sdr\Constant;
use common\library\ai_sdr\AISdrService;
use common\library\ai_sdr\task\AiSdrTask;
use common\library\ai_sdr\task_detail\AiSdrTaskDetail;
use common\library\ai_sdr\task_record\AiSdrTaskRecord;
use tests\DatabaseTransactions;

/**
 * 真实数据库操作测试
 * 
 * 测试AI SDR模块的真实数据库CRUD操作
 */
class RealDatabaseOperationTest extends \WebFunctionalTestCase
{
    use DatabaseTransactions;
    
    protected $testClientId = 333392;
    protected $testUserId = 249519530;
    
    protected function setUp(): void
    {
        parent::setUp();
        $this->loginAsQqlite_01();
    }
    
    /**
     * 测试任务创建和读取
     */
    public function testTaskCreateAndRead_WithValidData_WorksCorrectly()
    {
        try {
            // 创建任务
            $task = new AiSdrTask($this->testClientId);
            $task->client_id = $this->testClientId;
            $task->user_id = $this->testUserId;
            $task->source = Constant::TASK_SOURCE_AI_SDR;
            $task->current_stage = Constant::AI_SDR_STAGE_DIG;
            $task->end_stage = Constant::AI_SDR_STAGE_MARKETING;
            $task->task_status = Constant::AI_SDR_TASK_STATUS_PROCESSING;
            $task->email = 'test_real_db_' . time() . '@example.com';
            $task->tags = [1, 2, 3]; // bigint数组，使用整数值
            $task->enable_flag = 1;
            $task->stat_total = 0;
            
            // 保存任务
            $success = $task->create();
            $this->assertNotFalse($success, 'Task creation should succeed');
            $this->assertNotNull($task->task_id, 'Task ID should be generated');
            
            // 读取任务验证
            $taskId = $task->task_id;
            $loadedTask = new AiSdrTask($this->testClientId, $taskId);
            
            $this->assertFalse($loadedTask->isNew(), 'Loaded task should exist');
            $this->assertEquals($this->testClientId, $loadedTask->client_id);
            $this->assertEquals($this->testUserId, $loadedTask->user_id);
            $this->assertEquals(Constant::TASK_SOURCE_AI_SDR, $loadedTask->source);
            $this->assertEquals(Constant::AI_SDR_STAGE_DIG, $loadedTask->current_stage);
            $this->assertEquals(Constant::AI_SDR_STAGE_MARKETING, $loadedTask->end_stage);
            $this->assertEquals(Constant::AI_SDR_TASK_STATUS_PROCESSING, $loadedTask->task_status);
            $this->assertIsArray($loadedTask->tags, 'Tags should be array');
            $this->assertContains(1, $loadedTask->tags, 'Tags should contain 1');
            $this->assertContains(2, $loadedTask->tags, 'Tags should contain 2');
            $this->assertContains(3, $loadedTask->tags, 'Tags should contain 3');
            
            $this->assertTrue(true, 'Task create and read works correctly');
        } catch (\Exception $e) {
            $this->markTestSkipped('Task create and read failed: ' . $e->getMessage());
        }
    }
    
    /**
     * 测试任务更新
     */
    public function testTaskUpdate_WithValidData_UpdatesCorrectly()
    {
        try {
            // 创建任务
            $task = $this->createRealTask();
            $originalTaskId = $task->task_id;
            $originalStatTotal = $task->stat_total;
            
            // 更新任务
            $task->task_status = Constant::AI_SDR_TASK_STATUS_PAUSED;
            $task->current_stage = Constant::AI_SDR_STAGE_REACHABLE;
            $task->stat_total = $originalStatTotal + 10;
            $task->tags = [1, 2, 3, 4]; // 更新bigint数组
            
            $success = $task->update();
            $this->assertNotFalse($success, 'Task update should succeed');
            
            // 重新加载验证
            $updatedTask = new AiSdrTask($this->testClientId, $originalTaskId);
            $this->assertEquals(Constant::AI_SDR_TASK_STATUS_PAUSED, $updatedTask->task_status);
            $this->assertEquals(Constant::AI_SDR_STAGE_REACHABLE, $updatedTask->current_stage);
            $this->assertEquals($originalStatTotal + 10, $updatedTask->stat_total);
            $this->assertIsArray($updatedTask->tags, 'Updated tags should be array');
            $this->assertContains(4, $updatedTask->tags, 'Tags should contain 4');
            $this->assertCount(4, $updatedTask->tags, 'Tags should have 4 elements');
            
            $this->assertTrue(true, 'Task update works correctly');
        } catch (\Exception $e) {
            $this->markTestSkipped('Task update failed: ' . $e->getMessage());
        }
    }
    
    /**
     * 测试任务详情创建和读取
     */
    public function testTaskDetailCreateAndRead_WithValidData_WorksCorrectly()
    {
        try {
            // 创建任务
            $task = $this->createRealTask();
            
            // 创建任务详情
            $detail = new AiSdrTaskDetail($this->testClientId);
            $detail->task_id = $task->task_id;
            $detail->lead_id = rand(1000000, 9999999);
            $detail->user_id = $this->testUserId;
            $detail->source = Constant::TASK_SOURCE_AI_SDR;
            $detail->stage = Constant::AI_SDR_STAGE_DIG;
            $detail->status = Constant::DETAIL_STATUS_ADD;
            $detail->lead_quality = Constant::LEAD_QUALITY_UNKNOWN;
            $detail->product_ids = ['Product A', 'Product B']; // 数组字段
            $detail->company_types = ['Manufacturer', 'Exporter']; // 数组字段
            $detail->public_homepage = ['https://example.com']; // 数组字段
            $detail->enable_flag = 1;
            // 添加必需字段
            $detail->usage_record_id = rand(1000, 9999);
            $detail->company_id = rand(1000, 9999);
            $detail->delivery_status = 0;
            
            // 保存详情
            $success = $detail->create();
            $this->assertNotFalse($success, 'Detail creation should succeed');
            $this->assertNotNull($detail->id, 'Detail ID should be generated');
            
            // 读取详情验证
            $detailId = $detail->id;
            $loadedDetail = new AiSdrTaskDetail($this->testClientId, $detailId);
            
            $this->assertFalse($loadedDetail->isNew(), 'Loaded detail should exist');
            $this->assertEquals($task->task_id, $loadedDetail->task_id);
            $this->assertEquals($this->testUserId, $loadedDetail->user_id);
            $this->assertEquals(Constant::AI_SDR_STAGE_DIG, $loadedDetail->stage);
            $this->assertEquals(Constant::DETAIL_STATUS_ADD, $loadedDetail->status);
            $this->assertEquals(Constant::LEAD_QUALITY_UNKNOWN, $loadedDetail->lead_quality);
            
            // 验证数组字段
            $this->assertIsArray($loadedDetail->product_ids, 'Product IDs should be array');
            $this->assertContains('Product A', $loadedDetail->product_ids, 'Should contain Product A');
            $this->assertIsArray($loadedDetail->company_types, 'Company types should be array');
            $this->assertContains('Manufacturer', $loadedDetail->company_types, 'Should contain Manufacturer');
            $this->assertIsArray($loadedDetail->public_homepage, 'Public homepage should be array');
            $this->assertContains('https://example.com', $loadedDetail->public_homepage, 'Should contain homepage');
            
            $this->assertTrue(true, 'Task detail create and read works correctly');
        } catch (\Exception $e) {
            $this->markTestSkipped('Task detail create and read failed: ' . $e->getMessage());
        }
    }
    
    /**
     * 测试任务详情状态更新
     */
    public function testTaskDetailStatusUpdate_WithValidTransition_UpdatesCorrectly()
    {
        try {
            // 创建任务和详情
            $task = $this->createRealTask();
            $detail = $this->createRealTaskDetail($task->task_id);
            
            $originalStatus = $detail->status;
            $originalStage = $detail->stage;
            
            // 更新状态
            $detail->status = Constant::DETAIL_STATUS_LABEL;
            $detail->stage = Constant::AI_SDR_STAGE_REACHABLE;
            $detail->lead_quality = Constant::LEAD_QUALITY_MEDIUM;
            $detail->stage_reachable_time = date('Y-m-d H:i:s'); // 更新阶段时间
            
            $success = $detail->update();
            $this->assertNotFalse($success, 'Detail status update should succeed');
            
            // 重新加载验证
            $updatedDetail = new AiSdrTaskDetail($this->testClientId, $detail->id);
            $this->assertEquals(Constant::DETAIL_STATUS_LABEL, $updatedDetail->status);
            $this->assertEquals(Constant::AI_SDR_STAGE_REACHABLE, $updatedDetail->stage);
            $this->assertEquals(Constant::LEAD_QUALITY_MEDIUM, $updatedDetail->lead_quality);
            $this->assertNotEquals('1970-01-01 00:00:01', $updatedDetail->stage_reachable_time);
            
            $this->assertTrue(true, 'Task detail status update works correctly');
        } catch (\Exception $e) {
            $this->markTestSkipped('Task detail status update failed: ' . $e->getMessage());
        }
    }
    
    /**
     * 测试任务记录创建和读取
     */
    public function testTaskRecordCreateAndRead_WithValidData_WorksCorrectly()
    {
        try {
            // 创建任务和详情
            $task = $this->createRealTask();
            $detail = $this->createRealTaskDetail($task->task_id);
            
            // 创建任务记录
            $record = new AiSdrTaskRecord($this->testClientId);
            $record->task_id = $task->task_id;
            $record->detail_id = $detail->id;
            $record->lead_id = $detail->lead_id;
            $record->type = Constant::RECORD_TYPE_ADD_LEAD;
            $record->data = [ // JSON字段
                'action' => 'create_lead',
                'result' => 'success',
                'details' => [
                    'company_name' => 'Test Company',
                    'contact_email' => '<EMAIL>'
                ],
                'timestamp' => time()
            ];
            $record->estimate_time = date('Y-m-d H:i:s');
            // 添加必需字段
            $record->client_id = $this->testClientId;
            $record->refer_id = $detail->id;
            $record->refer_type = 1; // 假设1表示detail类型
            $record->enable_flag = 1;
            $record->create_time = date('Y-m-d H:i:s');
            $record->update_time = date('Y-m-d H:i:s');
            
            // 保存记录
            $success = $record->create();
            $this->assertNotFalse($success, 'Record creation should succeed');
            $this->assertNotNull($record->record_id, 'Record ID should be generated');
            
            // 读取记录验证
            $recordId = $record->record_id;
            $loadedRecord = new AiSdrTaskRecord($task->task_id, $recordId);
            
            $this->assertFalse($loadedRecord->isNew(), 'Loaded record should exist');
            $this->assertEquals($task->task_id, $loadedRecord->task_id);
            $this->assertEquals($detail->id, $loadedRecord->detail_id);
            $this->assertEquals($detail->lead_id, $loadedRecord->lead_id);
            $this->assertEquals(Constant::RECORD_TYPE_ADD_LEAD, $loadedRecord->type);
            
            // 验证JSON字段
            $this->assertIsArray($loadedRecord->data, 'Data should be array');
            $this->assertEquals('create_lead', $loadedRecord->data['action']);
            $this->assertEquals('success', $loadedRecord->data['result']);
            $this->assertIsArray($loadedRecord->data['details'], 'Details should be array');
            $this->assertEquals('Test Company', $loadedRecord->data['details']['company_name']);
            
            $this->assertTrue(true, 'Task record create and read works correctly');
        } catch (\Exception $e) {
            $this->markTestSkipped('Task record create and read failed: ' . $e->getMessage());
        }
    }
    
    /**
     * 测试统计数据更新
     */
    public function testStatisticUpdate_WithRealData_UpdatesCorrectly()
    {
        try {
            // 创建任务
            $task = $this->createRealTask();
            $originalStatTotal = $task->stat_total;
            
            // 使用AISdrService更新统计
            $increment = 5;
            AISdrService::updateStatTotal($this->testClientId, $task->task_id, $increment);
            
            // 重新加载验证
            $updatedTask = new AiSdrTask($this->testClientId, $task->task_id);
            $this->assertEquals($originalStatTotal + $increment, $updatedTask->stat_total);
            
            $this->assertTrue(true, 'Statistic update works correctly');
        } catch (\Exception $e) {
            $this->markTestSkipped('Statistic update failed: ' . $e->getMessage());
        }
    }
    
    /**
     * 创建真实的测试任务
     */
    private function createRealTask(): AiSdrTask
    {
        $task = new AiSdrTask($this->testClientId);
        $task->client_id = $this->testClientId;
        $task->user_id = $this->testUserId;
        $task->source = Constant::TASK_SOURCE_AI_SDR;
        $task->current_stage = Constant::AI_SDR_STAGE_DIG;
        $task->end_stage = Constant::AI_SDR_STAGE_MARKETING;
        $task->task_status = Constant::AI_SDR_TASK_STATUS_PROCESSING;
        $task->email = 'test_real_' . time() . '@example.com';
        $task->tags = [1, 2]; // bigint数组
        $task->enable_flag = 1;
        $task->stat_total = 0;
        
        $success = $task->create();
        if ($success === false) {
            throw new \RuntimeException('Failed to create real test task');
        }
        
        return $task;
    }
    
    /**
     * 创建真实的测试任务详情
     */
    private function createRealTaskDetail(int $taskId): AiSdrTaskDetail
    {
        $detail = new AiSdrTaskDetail($this->testClientId);
        $detail->task_id = $taskId;
        $detail->lead_id = rand(1000000, 9999999);
        $detail->user_id = $this->testUserId;
        $detail->source = Constant::TASK_SOURCE_AI_SDR;
        $detail->stage = Constant::AI_SDR_STAGE_DIG;
        $detail->status = Constant::DETAIL_STATUS_ADD;
        $detail->lead_quality = Constant::LEAD_QUALITY_UNKNOWN;
        $detail->product_ids = ['Product A'];
        $detail->company_types = ['Manufacturer'];
        $detail->public_homepage = ['https://example.com'];
        $detail->enable_flag = 1;
        // 添加必需字段
        $detail->usage_record_id = rand(1000, 9999);
        $detail->company_id = rand(1000, 9999);
        $detail->delivery_status = 0;
        
        $success = $detail->create();
        if ($success === false) {
            throw new \RuntimeException('Failed to create real test task detail');
        }
        
        return $detail;
    }
}
