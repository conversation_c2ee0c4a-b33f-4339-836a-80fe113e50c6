<?php
/**
 *
 * Author: ruisenlin
 * Date: 2022/6/7
 */


namespace common\tests\functional\tapd;

use common\modules\prometheus\library\devops\checklist\TapdShiftOut;
use common\modules\prometheus\library\release\IterationList;
use common\modules\prometheus\library\tapd\tapd_service_relation\TapdServiceRelation;
use common\modules\prometheus\library\tapd\tapd_service_relation\TapdServiceRelationList;
use common\modules\prometheus\library\tapd\tapd_story\CustomField;
use common\modules\prometheus\library\tapd\tapd_story\Stories;
use common\modules\prometheus\library\tapd\TapdApi;
use common\modules\prometheus\library\tapd\TapdClient;
use common\modules\prometheus\library\tapd\TapdConstant;
use common\modules\prometheus\library\webhook\executor\dingtalk\JoinDingtalkGroupTask;
use common\modules\prometheus\library\webhook\executor\tapd\bugs\BugsUpdateExecutor;
use common\modules\prometheus\library\webhook\executor\tapd\bugs\execute_task\SyncTapdSobotTask;
use common\modules\prometheus\library\webhook\executor\tapd\bugs\TapdBugExecutor;
use common\modules\prometheus\library\webhook\executor\tapd\comments\execute_task\CirculationCommentsTask;
use common\modules\prometheus\library\webhook\executor\tapd\release\BulidPackageTag;
use common\modules\prometheus\library\webhook\factory\handle\TapdHandlerFactory;
use common\modules\prometheus\library\webhook\Processor;
use http\Exception\RuntimeException;

class Tapd extends \WebFunctionalTestCase
{

    public function testAction()
    {
        preg_match('/[\x{4e00}-\x{9fa5}]+/u', '叶秋琳（java开发)', $matches);
        preg_match('/[\x{4e00}-\x{9fa5}]+/u', '方建坤(支撑）', $matches);
//        preg_match_all('/[\x{4e00}-\x{9fa5}]+/u', '曹绚', $matches);
        var_dump($matches);
    }


    public function testGetCustomField($workspaceId)
    {
        $tapdClient = TapdClient::getInstance();

        $params = [
            'workspace_id' => $workspaceId
        ];
        $custom_fields_settings = $tapdClient->getBugsCustom_fields_settings($params);


        var_export($custom_fields_settings);

    }

    public static function customFieldProvider()
    {
        return [
            [********],
            [20612851],
            [20036251]
        ];
    }


    public function testGetTestCaseById()
    {
        $tapdClient = TapdClient::getInstance();

        $params = [
            'workspace_id' => 20036251,
            'id' => 1120036251001025746
        ];

        $tcases = $tapdClient->getTcases($params);


        var_export($tcases);
    }



    public function testGetBugsById($workspaceId, $bugId)
    {
        $tapdClient = TapdClient::getInstance();

        $params = [
            'workspace_id' => $workspaceId,
            'id' => $bugId
        ];

        $bug = $tapdClient->getBugs($params);

        var_export($bug);

    }

    public static function bugsProvider()
    {

        return [
            [********, 11********001071950]
        ];

    }


    public function testGetRoles($workspaceId)
    {

        $tapdClient = TapdClient::getInstance();

        $params = [
            'workspace_id' => $workspaceId
        ];

        $roles = $tapdClient->getRoles($params);

        var_export($roles);

    }

    public static function workspaceIdProvider()
    {
        return [
            [********]
        ];
    }


    public function testGetUsers($workspaceId)
    {

        $tapdClient = TapdClient::getInstance();

        $users = $tapdClient->getUserListByWorkspaceId($workspaceId);


        var_export($users);

    }

    public function testGetFieldsInfo($workspaceId)
    {
        $tapdClient = TapdClient::getInstance();

        $params = [
            'workspace_id' => $workspaceId
        ];

        $result = $tapdClient->getStoriesGet_fields_info($params);

        var_export($result);

    }


    public function testGetBugsByIterationId($bugfixIterationId)
    {
        $tapdClient = TapdClient::getInstance();

        $str = addslashes(implode('|', TapdConstant::BUGFIX_BUG_STATUS));

        var_export($str);
        $bugs = $tapdClient->getBugs([
            'workspace_id' => TapdConstant::WORKSPACE_ID[TapdConstant::WORKSPACE_LME],
            'iteration_id' => $bugfixIterationId,
            'status' => "=resolved",
            'limit' => 100,
            'fields' => 'id,current_owner,status'
        ]);


        var_export($bugs);
    }


    public static function iterationIdProvider()
    {
        return [
            ['11********001000848']
        ];

    }


    public function testSyncRoleUsers()
    {
        $api = new TapdApi();

        $api->syncRoleUsers();
    }


    public function testSyncTapdUsers()
    {
        $api = new TapdApi();
        $api->syncUserRole();
    }


    public function syncUserProvider()
    {
        return [
            [********, 8]
        ];
    }


    public function testGetTapdAccountList()
    {
        $api = new TapdApi();

        $tapdAccountList = $api->getTapdAccountList('', 2, 20);


        var_export($tapdAccountList);
    }


    public function testSyncTapdUser()
    {
        $api = new TapdApi();
        $api->syncTapdUser();
    }

    public function testGetStoriesTemplateList()
    {
        $tapdClient = TapdClient::getInstance();

        $storiesTemplate_list = $tapdClient->getStoriesTemplate_list(['workspace_id' => ********, 'workitem_type_id' => 11********001000037]);

        var_export($storiesTemplate_list);
    }

    public function testGetStoriesTemplate()
    {
        $tapdClient = TapdClient::getInstance();

        $storiesTemplate_list = $tapdClient->getTemplate(['workspace_id' => ********, 'id' => '11********001000384']);

        var_export($storiesTemplate_list);
    }


    public function testGetStories()
    {
        $tapdClient = TapdClient::getInstance();
        $stories = $tapdClient->getStories(['id' => 11********001047189, 'workspace_id' => ********]);

        var_export($stories);
    }

    public function testGetWorkItemTypes()
    {
        $tapdClient = TapdClient::getInstance();
        $workItemTypes = $tapdClient->getworkitem_types(['workspace_id' => ********]);

        var_export($workItemTypes);
    }


    public function testGetBugsTemplateList()
    {
        $tapdClient = TapdClient::getInstance();

        $bugsTemplate_list = $tapdClient->getBugsTemplate_list(['workspace_id' => ********]);

        var_export($bugsTemplate_list);
    }


    public function testGetStoriesGet_fields_info()
    {
        $tapdClient = TapdClient::getInstance();

        $fieldsInfo = $tapdClient->getStoriesGet_fields_info(['workspace_id' => 30044898]);

        var_export($fieldsInfo);
    }

    public function testCreateStory()
    {
        $tapdClient = TapdClient::getInstance();

        $templateStory = $tapdClient->getStories(['id' => TapdConstant::TEMPLATE_STORY_ID, 'workspace_id' => TapdConstant::WORKSPACE_ID[TapdConstant::WORKSPACE_LME]])[0]['Story'];

        $description = str_replace(TapdConstant::TEMPLATE_PLACEHOLDER, '这是测试内容', $templateStory['description']);
        $sotriesParams = [
            'name' => '这是一个测试标题',
            'workspace_id' => TapdConstant::WORKSPACE_ID[TapdConstant::WORKSPACE_LME],
            'description' => $description,
        ];


        $tapdClient->postStories($sotriesParams);
    }


    public function testLoadStoryById()
    {
        $story = new Stories(11********001034094);
        var_export($story->getAttributes());
    }


    public function testGetStoriesCount()
    {
        $tapdClient = TapdClient::getInstance();
        $storiesCount = $tapdClient->getStoriesCount(['workspace_id' => TapdConstant::WORKSPACE_ID[TapdConstant::WORKSPACE_LME], 'custom_field_11' => '20220919000019']);
        var_export($storiesCount);
    }



    public function testTapdHandle()
    {
        $param = <<< JSON
{
    "event": "story::update",
    "event_from": "web",
    "referer": "https:\/\/www.tapd.cn\/********\/prong\/stories\/view\/11********001039457",
    "workspace_id": "********",
    "current_user": "Ruisenlin",
    "id": "11********001039457",
    "old_id": "11********001039457",
    "old_secret_root_id": "0",
    "old_sort": "103901500000",
    "old_workitem_type_id": "11********001000037",
    "old_name": "kk测试test",
    "old_description": "<h3 data-lines=\"1\" data-sign=\"81f0a2dbea0b2930af944038c0986f48\" id=\"%E8%83%8C%E6%99%AF\"><span style=\"color: #3f4a56;\"><a href=\"#%E8%83%8C%E6%99%AF\" class=\"anchor\" style=\"color: #3f4a56;\" rel=\"noopener\" target=\"_blank\"><\/a><\/span>背景<\/h3><p data-lines=\"2\" data-type=\"p\" data-sign=\"b65db927ae98120dd432b05c696f15202\"><p>test<\/p><\/p><h3 data-lines=\"2\" data-sign=\"d7c8ad1cf43ab6f6bfc7bfdf904defaa\" id=\"%E5%8F%AF%E8%A1%8C%E6%80%A7%E5%88%86%E6%9E%90\"><a href=\"#%E5%8F%AF%E8%A1%8C%E6%80%A7%E5%88%86%E6%9E%90\" class=\"anchor\" rel=\"noopener\" target=\"_blank\"><\/a>可行性分析<\/h3><h4 data-lines=\"2\" data-sign=\"ee22c2b1a55321b57153b639d6c7a0cc\" id=\"%E7%94%A8%E6%88%B7%E8%B0%83%E7%A0%94\"><a href=\"#%E7%94%A8%E6%88%B7%E8%B0%83%E7%A0%94\" class=\"anchor\" rel=\"noopener\" target=\"_blank\"><\/a>用户调研<\/h4><p data-lines=\"2\" data-type=\"p\" data-sign=\"6eb8822fb3dbd5831140faa0ccca1a6f2\"><br  \/><\/p><h3 data-lines=\"2\" data-sign=\"e9e8ca2c97aa30632c80d3b15b069eab\" id=\"%E6%B6%89%E4%BC%97\"><a href=\"#%E6%B6%89%E4%BC%97\" class=\"anchor\" rel=\"noopener\" target=\"_blank\"><\/a>涉众<\/h3><p data-lines=\"2\" data-type=\"p\" data-sign=\"f193bfbd0cae141c2439c9228729dc512\">可能涉及的用户，如果有则需明确说明，如试用用户，正式用户等。<\/p><div data-sign=\"7d0ecc46fe4eaef0e47665ca50b9679d\" data-lines=\"1\" class=\"cherry-table-container simple-table\"><div><div class=\"scroll_table\"><table class=\"cherry-table\"><thead><tr><th align=\"left\" style=\"width: 66.0469px; text-align: center;\">CRM<\/th><th align=\"left\" style=\"width: 57.9219px; text-align: center;\">Plus<\/th><th align=\"left\" style=\"width: 41.7031px; text-align: center;\">DX<\/th><th align=\"left\" style=\"width: 50.8281px; text-align: center;\">Lite<\/th><th align=\"left\" style=\"width: 77.1875px; text-align: center;\">Smart<\/th><th align=\"left\" style=\"width: 51.9062px; text-align: center;\">Pro<\/th><\/tr><\/thead><tbody><tr style=\"word-break: break-word;\"><td data-cell-id=\"7643-1628359000116-cell-1-0\" style=\"word-break: break-all; margin: 0px; padding: 5px; height: 28px; white-space: normal; border-width: 1px; border-style: solid; overflow: inherit; vertical-align: middle; text-align: center; color: #393939; width: 66.0469px; border-bottom-color: initial !important;\"><span style=\"word-break: break-word; line-height: inherit;\">√<\/span><\/td><td data-cell-id=\"7643-1628359000116-cell-1-1\" style=\"word-break: break-all; margin: 0px; padding: 5px; height: 28px; white-space: normal; border-width: 1px; border-style: solid; overflow: inherit; vertical-align: middle; text-align: center; color: #393939; width: 57.9219px; border-bottom-color: initial !important;\"><span style=\"word-break: break-word; line-height: inherit;\">√<\/span><\/td><td data-cell-id=\"7643-1628359000116-cell-1-2\" style=\"word-break: break-all; margin: 0px; padding: 5px; height: 28px; white-space: normal; border-width: 1px; border-style: solid; overflow: inherit; vertical-align: middle; text-align: center; color: #393939; width: 41.7031px; border-bottom-color: initial !important;\"><span style=\"word-break: break-word; line-height: inherit;\">√<\/span><\/td><td data-cell-id=\"7643-1628359000116-cell-1-3\" style=\"word-break: break-all; margin: 0px; padding: 5px; height: 28px; white-space: normal; border-width: 1px; border-style: solid; overflow: inherit; vertical-align: middle; text-align: center; color: #393939; width: 50.8281px; border-bottom-color: initial !important;\"><span style=\"word-break: break-word; line-height: inherit;\">√<\/span><\/td><td data-cell-id=\"7643-1628359000116-cell-1-4\" style=\"word-break: break-all; margin: 0px; padding: 5px; height: 28px; white-space: normal; border-width: 1px; border-style: solid; overflow: inherit; vertical-align: middle; text-align: center; color: #393939; width: 77.1875px; border-bottom-color: initial !important;\"><span style=\"word-break: break-word; line-height: inherit;\">√<\/span><\/td><td data-cell-id=\"7643-1628359000116-cell-1-5\" style=\"word-break: break-all; margin: 0px; padding: 5px; height: 28px; white-space: normal; border-width: 1px; border-style: solid; overflow: inherit; vertical-align: middle; text-align: center; color: #393939; width: 51.9062px; border-bottom-color: initial !important;\"><span style=\"word-break: break-word; line-height: inherit;\">√<\/span><\/td><\/tr><\/tbody><\/table><\/div><\/div><\/div><div data-sign=\"a865f45c80072a1984298ec28ca732e1\" data-lines=\"1\" class=\"cherry-table-container simple-table\"><br  \/><\/div><h4 data-lines=\"1\" data-sign=\"98da56e051e1c7a1bc5a5a23ac79476d\" id=\"%E5%AF%B9%E5%85%B6%E4%BB%96%E4%BA%A7%E5%93%81%E7%9A%84%E5%BD%B1%E5%93%8D\"><a href=\"#%E5%AF%B9%E5%85%B6%E4%BB%96%E4%BA%A7%E5%93%81%E7%9A%84%E5%BD%B1%E5%93%8D\" class=\"anchor\" rel=\"noopener\" target=\"_blank\"><\/a>对其他产品的影响<\/h4><p data-lines=\"2\" data-type=\"p\" data-sign=\"516639e4408cd359467a9b25731b28a52\">是否对其他产品、功能有影响，例如对移动端（存在新老版本兼容性问题），桌面端，其他功能模块的影响<\/p><div data-sign=\"929335bc8612c20e9f1ec3dfd0e1c41d\" data-lines=\"1\" class=\"cherry-table-container simple-table\"><div><div class=\"scroll_table\"><table class=\"cherry-table\"><tbody><tr><td>Web<\/td><td>移动端<\/td><td>桌面端<\/td><\/tr><tr><td><\/td><td><\/td><td><\/td><\/tr><\/tbody><\/table><\/div><\/div><\/div><h3 data-lines=\"1\" data-sign=\"2f4fa0af2a7bae11b3b42e782f98e51c\" id=\"%E8%AF%A6%E7%BB%86%E5%8A%9F%E8%83%BD\"><a href=\"#%E8%AF%A6%E7%BB%86%E5%8A%9F%E8%83%BD\" class=\"anchor\" rel=\"noopener\" target=\"_blank\"><\/a>详细功能<\/h3><h4 data-lines=\"2\" data-sign=\"370be40882fa3f186123ae3f1ba67200\" id=\"%E9%9C%80%E6%B1%82%E5%86%85%E5%AE%B9\"><a href=\"#%E9%9C%80%E6%B1%82%E5%86%85%E5%AE%B9\" class=\"anchor\" rel=\"noopener\" target=\"_blank\"><\/a>需求内容<\/h4><p data-lines=\"2\" data-type=\"p\" data-sign=\"19e06836f53bb06e0ffaeb4fc58f27962\">详细具体需求内容。包括如下内容：<\/p><div data-sign=\"3bb5f59c07dfb398fc622dbb2f2dbb81\" data-lines=\"1\" class=\"cherry-table-container simple-table\"><div><div class=\"scroll_table\"><table class=\"cherry-table\"><tbody><tr><td>所需模块<\/td><td>需求内容<\/td><td>优先级<\/td><td>原型图<\/td><td>需求详细描述<\/td><\/tr><tr><td><\/td><td><\/td><td><\/td><td><\/td><td><\/td><\/tr><\/tbody><\/table><\/div><\/div><\/div><h4 data-lines=\"1\" data-sign=\"46e4ed3679d31b7e2b5b4c9c63b2dec3\" id=\"%E4%BA%A4%E4%BA%92%E5%9C%B0%E5%9D%80\"><a href=\"#%E4%BA%A4%E4%BA%92%E5%9C%B0%E5%9D%80\" class=\"anchor\" rel=\"noopener\" target=\"_blank\"><\/a>交互地址<\/h4><p data-lines=\"2\" data-type=\"p\" data-sign=\"17e1f69ae2b845a9dabac384ed543df12\">交互页面地址<\/p><h4 data-lines=\"2\" data-sign=\"333d2d4b0a9f0e3989dedbbfc3a792de\" id=\"%E7%BB%9F%E8%AE%A1%E9%9C%80%E6%B1%82\"><a href=\"#%E7%BB%9F%E8%AE%A1%E9%9C%80%E6%B1%82\" class=\"anchor\" rel=\"noopener\" target=\"_blank\"><\/a>统计需求<\/h4><p data-lines=\"2\" data-type=\"p\" data-sign=\"cf30ee87831231458410fcdf1179a3dc2\">具体统计需求<\/p>",
    "old_markdown_description": "",
    "old_description_type": "1",
    "old_creator": "智齿工单",
    "old_created": "2023-01-10 13:47:58",
    "old_modified": "2023-04-12 10:34:26",
    "old_parent_id": "0",
    "old_children_id": "||11********001044989",
    "old_ancestor_id": "11********001039457",
    "old_path": "11********001039457:",
    "old_level": "0",
    "old_workspace_id": "********",
    "old_status": "status_9",
    "old_flows": "",
    "old_priority": "2",
    "old_owner": "Ruisenlin;",
    "old_participator": ";无;Ruisenlin",
    "old_cc": "",
    "old_begin": "",
    "old_due": "",
    "old_source": "用户反馈",
    "old_workitem_id": "",
    "old_iteration_id": "11********001000629",
    "old_issue_id": "",
    "old_support_id": "",
    "old_support_forum_id": "",
    "old_module": "",
    "old_completed": "",
    "old_templated_id": "11********001000409",
    "old_delay_count": "",
    "old_type": "",
    "old_status_append": "",
    "old_business_value": "",
    "old_tech_risk": "",
    "old_size": "",
    "old_import_flag": "0",
    "old_effort": "",
    "old_effort_completed": "0",
    "old_exceed": "0",
    "old_remain": "0",
    "old_progress": "0",
    "old_release_id": "0",
    "old_feature": "",
    "old_entity_type": "Story",
    "old_custom_field_one": "",
    "old_custom_field_two": "",
    "old_custom_field_three": "",
    "old_custom_field_four": "",
    "old_custom_field_five": "",
    "old_custom_field_six": "",
    "old_custom_field_seven": "",
    "old_custom_field_eight": "",
    "old_attachment_count": "0",
    "old_has_attachment": "0",
    "old_developer": "Ruisenlin",
    "old_bug_id": "",
    "old_test_focus": "",
    "old_category_id": "-1",
    "old_version": "",
    "old_confidential": "N",
    "old_created_from": "api",
    "old_follower": "",
    "old_sync_type": "",
    "old_predecessor_count": "0",
    "old_is_archived": "0",
    "old_modifier": "",
    "old_progress_manual": "0",
    "old_successor_count": "0",
    "old_label": "",
    "old_custom_field_9": "",
    "old_custom_field_10": "",
    "old_custom_field_11": "20220915000070",
    "old_custom_field_12": "2022-12-30",
    "old_custom_field_13": "",
    "old_custom_field_14": "",
    "old_custom_field_15": "",
    "old_custom_field_16": "",
    "old_custom_field_17": "",
    "old_custom_field_18": "",
    "old_custom_field_19": "",
    "old_custom_field_20": "",
    "old_custom_field_21": "",
    "old_custom_field_22": "",
    "old_custom_field_23": "",
    "old_custom_field_24": "",
    "old_custom_field_25": "",
    "old_custom_field_26": "",
    "old_custom_field_27": "",
    "old_custom_field_28": "",
    "old_custom_field_29": "",
    "old_custom_field_30": "",
    "old_custom_field_31": "",
    "old_custom_field_32": "",
    "old_custom_field_33": "",
    "old_custom_field_34": "",
    "old_custom_field_35": "",
    "old_custom_field_36": "",
    "old_custom_field_37": "",
    "old_custom_field_38": "",
    "old_custom_field_39": "",
    "old_custom_field_40": "",
    "old_custom_field_41": "",
    "old_custom_field_42": "",
    "old_custom_field_43": "",
    "old_custom_field_44": "",
    "old_custom_field_45": "",
    "old_custom_field_46": "",
    "old_custom_field_47": "",
    "old_custom_field_48": "",
    "old_custom_field_49": "",
    "old_custom_field_50": "",
    "old_custom_field_51": "",
    "old_custom_field_52": "",
    "old_custom_field_53": "",
    "old_custom_field_54": "",
    "old_custom_field_55": "",
    "old_custom_field_56": "",
    "old_custom_field_57": "",
    "old_custom_field_58": "",
    "old_custom_field_59": "",
    "old_custom_field_60": "",
    "old_custom_field_61": "",
    "old_custom_field_62": "",
    "old_custom_field_63": "",
    "old_custom_field_64": "",
    "old_custom_field_65": "",
    "old_custom_field_66": "",
    "old_custom_field_67": "",
    "old_custom_field_68": "",
    "old_custom_field_69": "",
    "old_custom_field_70": "",
    "old_custom_field_71": "",
    "old_custom_field_72": "",
    "old_custom_field_73": "",
    "old_custom_field_74": "",
    "old_custom_field_75": "",
    "old_custom_field_76": "",
    "old_custom_field_77": "",
    "old_custom_field_78": "",
    "old_custom_field_79": "",
    "old_custom_field_80": "",
    "old_custom_field_81": "",
    "old_custom_field_82": "",
    "old_custom_field_83": "",
    "old_custom_field_84": "",
    "old_custom_field_85": "",
    "old_custom_field_86": "",
    "old_custom_field_87": "",
    "old_custom_field_88": "",
    "old_custom_field_89": "",
    "old_custom_field_90": "",
    "old_custom_field_91": "",
    "old_custom_field_92": "",
    "old_custom_field_93": "",
    "old_custom_field_94": "",
    "old_custom_field_95": "",
    "old_custom_field_96": "",
    "old_custom_field_97": "",
    "old_custom_field_98": "",
    "old_custom_field_99": "",
    "old_custom_field_100": "",
    "old_custom_field_101": "",
    "old_custom_field_102": "",
    "old_custom_field_103": "",
    "old_custom_field_104": "",
    "old_custom_field_105": "",
    "old_custom_field_106": "",
    "old_custom_field_107": "",
    "old_custom_field_108": "",
    "old_custom_field_109": "",
    "old_custom_field_110": "",
    "old_custom_field_111": "",
    "old_custom_field_112": "",
    "old_custom_field_113": "",
    "old_custom_field_114": "",
    "old_custom_field_115": "",
    "old_custom_field_116": "",
    "old_custom_field_117": "",
    "old_custom_field_118": "",
    "old_custom_field_119": "",
    "old_custom_field_120": "",
    "old_custom_field_121": "",
    "old_custom_field_122": "",
    "old_custom_field_123": "",
    "old_custom_field_124": "",
    "old_custom_field_125": "",
    "old_custom_field_126": "",
    "old_custom_field_127": "",
    "old_custom_field_128": "",
    "old_custom_field_129": "",
    "old_custom_field_130": "",
    "old_custom_field_131": "",
    "old_custom_field_132": "",
    "old_custom_field_133": "",
    "old_custom_field_134": "",
    "old_custom_field_135": "",
    "old_custom_field_136": "",
    "old_custom_field_137": "",
    "old_custom_field_138": "",
    "old_custom_field_139": "",
    "old_custom_field_140": "",
    "old_custom_field_141": "",
    "old_custom_field_142": "",
    "old_custom_field_143": "",
    "old_custom_field_144": "",
    "old_custom_field_145": "",
    "old_custom_field_146": "",
    "old_custom_field_147": "",
    "old_custom_field_148": "",
    "old_custom_field_149": "",
    "old_custom_field_150": "",
    "old_custom_field_151": "",
    "old_custom_field_152": "",
    "old_custom_field_153": "",
    "old_custom_field_154": "",
    "old_custom_field_155": "",
    "old_custom_field_156": "",
    "old_custom_field_157": "",
    "old_custom_field_158": "",
    "old_custom_field_159": "",
    "old_custom_field_160": "",
    "old_custom_field_161": "",
    "old_custom_field_162": "",
    "old_custom_field_163": "",
    "old_custom_field_164": "",
    "old_custom_field_165": "",
    "old_custom_field_166": "",
    "old_custom_field_167": "",
    "old_custom_field_168": "",
    "old_custom_field_169": "",
    "old_custom_field_170": "",
    "old_custom_field_171": "",
    "old_custom_field_172": "",
    "old_custom_field_173": "",
    "old_custom_field_174": "",
    "old_custom_field_175": "",
    "old_custom_field_176": "",
    "old_custom_field_177": "",
    "old_custom_field_178": "",
    "old_custom_field_179": "",
    "old_custom_field_180": "",
    "old_custom_field_181": "",
    "old_custom_field_182": "",
    "old_custom_field_183": "",
    "old_custom_field_184": "",
    "old_custom_field_185": "",
    "old_custom_field_186": "",
    "old_custom_field_187": "",
    "old_custom_field_188": "",
    "old_custom_field_189": "",
    "old_custom_field_190": "",
    "old_custom_field_191": "",
    "old_custom_field_192": "",
    "old_custom_field_193": "",
    "old_custom_field_194": "",
    "old_custom_field_195": "",
    "old_custom_field_196": "",
    "old_custom_field_197": "",
    "old_custom_field_198": "",
    "old_custom_field_199": "",
    "old_custom_field_200": "",
    "old_custom_plan_field_1": "0",
    "old_custom_plan_field_2": "0",
    "old_custom_plan_field_3": "0",
    "old_custom_plan_field_4": "0",
    "old_custom_plan_field_5": "0",
    "old_custom_plan_field_6": "0",
    "old_custom_plan_field_7": "0",
    "old_custom_plan_field_8": "0",
    "old_custom_plan_field_9": "0",
    "old_custom_plan_field_10": "0",
    "change_fields": "iteration_id,modified",
    "secret": "",
    "rio_token": "",
    "queue_id": "170972118",
    "event_id": "111901704",
    "created": "2023-05-08 16:31:33"
}
JSON;


        $processor = new Processor(new TapdHandlerFactory());
        $processor->setJsonData($param);
        $processor->process();
    }

    public function testBugsGetFieldInfo()
    {
        $client = TapdClient::getInstance();
        $bugsGet_fields_info = $client->getBugsGet_fields_info(['workspace_id' => ********]);
        var_export($bugsGet_fields_info);
    }

    public function testIterationGetFieldInfo()
    {
        $client = TapdClient::getInstance();
        $iterationGet_fields_info = $client->getIterationsCustom_fields_settings(['workspace_id' => ********]);
        var_export($iterationGet_fields_info);
    }

    public function testIterationInfo()
    {
        $client = TapdClient::getInstance();
        $iteration = $client->getIterations(['id' => 11********001001154, 'workspace_id' => ********]);
        var_export($iteration);
    }

    public static function tapdProvider()
    {

        $bugCreate = <<< JSON
{
    "event": "bug::create",
    "event_from": "web",
    "referer": "https:\/\/www.tapd.cn\/********\/bugtrace\/bugs\/add?template_id=11********001003474",
    "workspace_id": "********",
    "current_user": "Ruisenlin",
    "id": "11********001090777",
    "secret": "",
    "rio_token": "",
    "devproxy_host": "http:\/\/hook-proxifer.tapd.woa.com",
    "queue_id": "196424799",
    "event_id": "126738487",
    "created": "2023-09-08 16:25:49"
}
JSON;
        $bugUpdate = <<< JSON
{
    "event": "bug::update",
    "event_from": "web",
    "referer": "https:\/\/www.tapd.cn\/********\/bugtrace\/bugs\/view?bug_id=11********001099238",
    "workspace_id": "********",
    "current_user": "Ruisenlin",
    "id": "11********001099238",
    "old_id": "11********001099238",
    "old_sid": "0",
    "old_project_id": "********",
    "old_parent_id": "",
    "old_title": "【工单#20240416000025】",
    "old_description": "<div data-inline-code-theme=\"red\" data-code-block-theme=\"default\"><h5 data-lines=\"1\" data-sign=\"f6e43c42078473eb1319d8bb58f015a4\" id=\"%E3%80%90%E7%8E%AF%E5%A2%83%E8%B4%A6%E5%8F%B7%E5%AF%86%E7%A0%81%E3%80%91%EF%BC%9A\" class=\"cherry-highlight-line\"><a href=\"#%E3%80%90%E7%8E%AF%E5%A2%83%E8%B4%A6%E5%8F%B7%E5%AF%86%E7%A0%81%E3%80%91%EF%BC%9A\" class=\"anchor\" target=\"_blank\"><\/a>【<strong>环境&amp;账号密码<\/strong>】：<\/h5><blockquote data-lines=\"4\" data-sign=\"18b77c604c528450b4168446e90c55b0_4\">环境：<a title=\"https:\/\/crm.xiaoman.cn\" href=\"https:\/\/crm.xiaoman.cn\" target=\"_blank\">https:\/\/crm.xiaoman.cn<\/a><br  \/>客户账号： <br  \/>clientID= <br  \/>userID=<\/blockquote><h5 data-lines=\"1\" data-sign=\"5a6aa35a82da69ddb34dd525dc6bd0b2\" id=\"%E3%80%90%E8%87%AA%E6%B5%8B%E6%83%85%E5%86%B5%E3%80%91%EF%BC%9A\"><a href=\"#%E3%80%90%E8%87%AA%E6%B5%8B%E6%83%85%E5%86%B5%E3%80%91%EF%BC%9A\" class=\"anchor\" target=\"_blank\"><\/a>【<strong>自测情况<\/strong>】：<\/h5><blockquote data-lines=\"1\" data-sign=\"cd9aea1b471a5f3fe6a7a5f6824c41cf_1\">客户号有此问题； 测试号自测正常，未复现<\/blockquote><h5 data-lines=\"2\" data-sign=\"9b6d029b7b4521f8768c0d06f5a00093\" id=\"%E3%80%90%E5%A4%8D%E7%8E%B0%E6%AD%A5%E9%AA%A4%E5%8F%8A%E9%97%AE%E9%A2%98%E7%8E%B0%E8%B1%A1%E3%80%91%EF%BC%9A\"><a href=\"#%E3%80%90%E5%A4%8D%E7%8E%B0%E6%AD%A5%E9%AA%A4%E5%8F%8A%E9%97%AE%E9%A2%98%E7%8E%B0%E8%B1%A1%E3%80%91%EF%BC%9A\" class=\"anchor\" target=\"_blank\"><\/a>【<strong>复现步骤及问题现象<\/strong>】：<\/h5><blockquote data-lines=\"1\" data-sign=\"a0e889e2f6f4f2dfd236cc27b199a34d_1\">待描述<\/blockquote><h5 data-lines=\"2\" data-sign=\"f6d93bde3691d3ab74f597ed282ffe07\" id=\"%E3%80%90%E9%A2%84%E6%9C%9F%E7%BB%93%E6%9E%9C%E3%80%91%EF%BC%9A\"><a href=\"#%E3%80%90%E9%A2%84%E6%9C%9F%E7%BB%93%E6%9E%9C%E3%80%91%EF%BC%9A\" class=\"anchor\" target=\"_blank\"><\/a>【<strong>预期结果<\/strong>】：<\/h5><blockquote data-lines=\"1\" data-sign=\"a0e889e2f6f4f2dfd236cc27b199a34d_1\">待描述<\/blockquote><h5 data-lines=\"2\" data-sign=\"b1b067fb3134c0fcc2eb4b6447681bea\" id=\"%E3%80%90%E5%A4%8D%E7%8E%B0%E9%93%BE%E6%8E%A5%E6%95%B0%E6%8D%AE%E6%8E%A5%E5%8F%A3%E5%8F%82%E6%95%B0%E3%80%91%EF%BC%9A\"><a href=\"#%E3%80%90%E5%A4%8D%E7%8E%B0%E9%93%BE%E6%8E%A5%E6%95%B0%E6%8D%AE%E6%8E%A5%E5%8F%A3%E5%8F%82%E6%95%B0%E3%80%91%EF%BC%9A\" class=\"anchor\" target=\"_blank\"><\/a>【<strong>复现链接|数据|接口参数<\/strong>】：<\/h5><blockquote data-lines=\"1\" data-sign=\"4089808eb3b2612cf515590b277bdbe1_1\">待粘贴<\/blockquote><h5 data-lines=\"2\" data-sign=\"1933422b3e78a99b8f84c0e421ab1e92\" id=\"%E3%80%90%E7%9B%B8%E5%85%B3%E6%88%AA%E5%9B%BE%E3%80%91%EF%BC%9A\"><a href=\"#%E3%80%90%E7%9B%B8%E5%85%B3%E6%88%AA%E5%9B%BE%E3%80%91%EF%BC%9A\" class=\"anchor\" target=\"_blank\"><\/a>【<strong>相关截图<\/strong>】：<\/h5><blockquote data-lines=\"1\" data-sign=\"8f287d9b603c65e13bc701de81d5cf2f_1\">无<\/blockquote><h5 data-lines=\"2\" data-sign=\"000b590bc758a01a9e6a5baf85d5eb42\" id=\"%E3%80%90%E9%A2%84%E6%9C%9F%E4%BB%A3%E7%A0%81%E6%8F%90%E4%BA%A4%E5%88%86%E6%94%AF%E3%80%91%EF%BC%9A\"><a href=\"#%E3%80%90%E9%A2%84%E6%9C%9F%E4%BB%A3%E7%A0%81%E6%8F%90%E4%BA%A4%E5%88%86%E6%94%AF%E3%80%91%EF%BC%9A\" class=\"anchor\" target=\"_blank\"><\/a>【<strong>预期代码提交分支<\/strong>】：<\/h5><blockquote data-lines=\"7\" data-sign=\"f23d0a5641368606fb43a79d7cd68c8f_7\">1、建议提交至：最近bugfix， 并将此缺陷移至相应周bugfix里跟进；<br  \/>2、开发第一时间给出原因和预计解决时间在评论中，带“智齿：”字眼会自动同步至智齿工单系统<br  \/>评论格式：<br  \/>智齿：是否bug： ，原因：xxxxx，影响范围：xxxx，预计解决方案：xxx，预计上线时间：xxx<br  \/>3、开发写好2后，再次回复：转回工单组（此步骤是让智齿工单自动流转）<br  \/>4、工单缺陷流程：待处理➔工单已定位待解决 or 工单数据修复➔已解决-➔工单已验证；<br  \/>工单定位后标记：工单已定位待解决 or 工单数据修复，修复后标记：已解决<\/blockquote><\/div>",
    "old_description_type": "2",
    "old_module": "企业中心",
    "old_milestone": "",
    "old_reporter": "Ruisenlin",
    "old_deadline": "",
    "old_created": "2024-04-16 11:52:39",
    "old_resolved": "2024-04-16 12:05:09",
    "old_closed": "",
    "old_modified": "2024-04-16 12:05:09",
    "old_lastmodify": "Ruisenlin",
    "old_auditer": "",
    "old_de": "",
    "old_te": "",
    "old_confirmer": "",
    "old_current_owner": "Ruisenlin;",
    "old_participator": "Ruisenlin",
    "old_closer": "",
    "old_status": "resolved",
    "old_resolution": "fixed",
    "old_priority": "low",
    "old_severity": "",
    "old_platform": "",
    "old_os": "",
    "old_testmode": "工单反馈",
    "old_testtype": "",
    "old_testphase": "",
    "old_source": "",
    "old_frequency": "",
    "old_cc": "",
    "old_estimate": "",
    "old_flows": "in_progress|resolved",
    "old_version_report": "",
    "old_version_test": "",
    "old_version_fix": "",
    "old_version_close": "",
    "old_delayed": "0",
    "old_custom_field_one": "20240416000025",
    "old_custom_field_two": "2024-04-16 11:51:44",
    "old_custom_field_three": "web端",
    "old_custom_field_four": "",
    "old_custom_field_five": "",
    "old_regression_number": "0",
    "old_issue_id": "",
    "old_support_id": "",
    "old_support_forum_id": "",
    "old_created_from": "",
    "old_baseline_find": "",
    "old_baseline_join": "",
    "old_baseline_close": "",
    "old_ticket_id": "",
    "old_story_id": "",
    "old_baseline_test": "",
    "old_originphase": "",
    "old_sourcephase": "",
    "old_bugtype": "",
    "old_feature": "",
    "old_in_progress_time": "2024-04-16 11:52:39",
    "old_verify_time": "",
    "old_reject_time": "",
    "old_reopen_time": "",
    "old_audit_time": "",
    "old_suspend_time": "",
    "old_assigned_time": "",
    "old_iteration_id": "11********001000629",
    "old_custom_field_6": "",
    "old_custom_field_7": "",
    "old_custom_field_8": "",
    "old_custom_field_9": "是",
    "old_custom_field_10": "",
    "old_custom_field_11": "",
    "old_custom_field_12": "",
    "old_custom_field_13": "",
    "old_custom_field_14": "",
    "old_custom_field_15": "",
    "old_custom_field_16": "",
    "old_custom_field_17": "",
    "old_custom_field_18": "",
    "old_custom_field_19": "",
    "old_custom_field_20": "",
    "old_custom_field_21": "",
    "old_custom_field_22": "",
    "old_custom_field_23": "",
    "old_custom_field_24": "",
    "old_custom_field_25": "",
    "old_custom_field_26": "",
    "old_custom_field_27": "",
    "old_custom_field_28": "",
    "old_custom_field_29": "",
    "old_custom_field_30": "",
    "old_custom_field_31": "",
    "old_custom_field_32": "",
    "old_custom_field_33": "",
    "old_custom_field_34": "",
    "old_custom_field_35": "",
    "old_custom_field_36": "",
    "old_custom_field_37": "",
    "old_custom_field_38": "",
    "old_custom_field_39": "",
    "old_custom_field_40": "",
    "old_custom_field_41": "",
    "old_custom_field_42": "",
    "old_custom_field_43": "",
    "old_custom_field_44": "",
    "old_custom_field_45": "",
    "old_custom_field_46": "",
    "old_custom_field_47": "",
    "old_custom_field_48": "",
    "old_custom_field_49": "",
    "old_custom_field_50": "",
    "old_custom_field_51": "",
    "old_custom_field_52": "",
    "old_custom_field_53": "",
    "old_custom_field_54": "",
    "old_custom_field_55": "",
    "old_custom_field_56": "",
    "old_custom_field_57": "",
    "old_custom_field_58": "",
    "old_custom_field_59": "",
    "old_custom_field_60": "",
    "old_custom_field_61": "",
    "old_custom_field_62": "",
    "old_custom_field_63": "",
    "old_custom_field_64": "",
    "old_custom_field_65": "",
    "old_custom_field_66": "",
    "old_custom_field_67": "",
    "old_custom_field_68": "",
    "old_custom_field_69": "",
    "old_custom_field_70": "",
    "old_custom_field_71": "",
    "old_custom_field_72": "",
    "old_custom_field_73": "",
    "old_custom_field_74": "",
    "old_custom_field_75": "",
    "old_custom_field_76": "",
    "old_custom_field_77": "",
    "old_custom_field_78": "",
    "old_custom_field_79": "",
    "old_custom_field_80": "",
    "old_custom_field_81": "",
    "old_custom_field_82": "",
    "old_custom_field_83": "",
    "old_custom_field_84": "",
    "old_custom_field_85": "",
    "old_custom_field_86": "",
    "old_custom_field_87": "",
    "old_custom_field_88": "",
    "old_custom_field_89": "",
    "old_custom_field_90": "",
    "old_custom_field_91": "",
    "old_custom_field_92": "",
    "old_custom_field_93": "",
    "old_custom_field_94": "",
    "old_custom_field_95": "",
    "old_custom_field_96": "",
    "old_custom_field_97": "",
    "old_custom_field_98": "",
    "old_custom_field_99": "",
    "old_custom_field_100": "",
    "old_custom_plan_field_1": "0",
    "old_custom_plan_field_2": "0",
    "old_custom_plan_field_3": "0",
    "old_custom_plan_field_4": "0",
    "old_custom_plan_field_5": "0",
    "old_custom_plan_field_6": "0",
    "old_custom_plan_field_7": "0",
    "old_custom_plan_field_8": "0",
    "old_custom_plan_field_9": "0",
    "old_custom_plan_field_10": "0",
    "old_template_id": "11********001003474",
    "old_begin": "",
    "old_due": "",
    "old_release_id": "",
    "old_fixer": "Ruisenlin",
    "old_follower": "",
    "old_sync_type": "",
    "old_label": "",
    "old_effort": "",
    "old_effort_completed": "0",
    "old_exceed": "0",
    "old_remain": "0",
    "old_progress": "0",
    "old_size": "",
    "new_closed": "2024-04-16 12:05:16",
    "new_closer": "Ruisenlin",
    "new_status": "closed",
    "new_flows": "in_progress|resolved|closed",
    "new_verify_time": "2024-04-16 12:05:16",
    "new_modified": "2024-04-16 12:05:16",
    "change_fields": "closed,closer,status,flows,verify_time,modified",
    "secret": "",
    "rio_token": "",
    "devproxy_host": "http:\/\/websocket-proxy",
    "queue_id": "243805737",
    "event_id": "150894616",
    "created": "2024-04-16 12:05:16"
}
JSON;
        $commentAdd = <<< JSON
{
    "event": "bug_comment::add",
    "event_from": "web",
    "referer": "https:\/\/www.tapd.cn\/********\/bugtrace\/bugs\/view?bug_id=11********001099238",
    "workspace_id": "********",
    "current_user": "Ruisenlin",
    "id": "11********001113091",
    "description":"<p>智齿：测试<\/p>",
    "author": "Ruisenlin",
    "entity_id": "11********001099238",
    "secret": "",
    "rio_token": "",
    "devproxy_host": "http:\/\/websocket-proxy",
    "queue_id": "243797868",
    "event_id": "150891197",
    "created": "2024-04-16 11:52:57"
}
JSON;
        $commentUpdate = <<< JSON
{
    "event": "bug_comment::update",
    "event_from": "web",
    "referer": "https:\/\/www.tapd.cn\/********\/comments\/edit_workitem_comment\/11********001103636?keepThis=true&TB_iframe=true&height=550&width=720&entity_type=bug&rand=1694162678028",
    "workspace_id": "********",
    "current_user": "Ruisenlin",
    "id": "11********001103636",
    "description":"<p><span style=\"color: #3f4a56;\">智齿：<\/span><br><\/p><p><span style=\"color: #3f4a56;\">转回工单组<\/span><\/p><p><span style=\"color: #3f4a56;\">修改<\/span><\/p>",
    "author": "Ruisenlin",
    "entity_id": "11********001090777",
    "secret": "",
    "rio_token": "",
    "devproxy_host": "http:\/\/hook-proxifer.tapd.woa.com",
    "queue_id": "196433426",
    "event_id": "126743002",
    "created": "2023-09-08 16:44:50"
}
JSON;
        $iterationMove = <<< JSON
{
    "event": "story::update",
    "event_from": "web",
    "referer": "https:\/\/www.tapd.cn\/********\/prong\/stories\/view\/11********001039457",
    "workspace_id": "********",
    "current_user": "Ruisenlin",
    "id": "11********001039457",
    "old_id": "11********001039457",
    "old_secret_root_id": "0",
    "old_sort": "103901500000",
    "old_workitem_type_id": "11********001000037",
    "old_name": "kk测试test",
    "old_description": "<h3 data-lines=\"1\" data-sign=\"81f0a2dbea0b2930af944038c0986f48\" id=\"%E8%83%8C%E6%99%AF\"><span style=\"color: #3f4a56;\"><a href=\"#%E8%83%8C%E6%99%AF\" class=\"anchor\" style=\"color: #3f4a56;\" rel=\"noopener\" target=\"_blank\"><\/a><\/span>背景<\/h3><p data-lines=\"2\" data-type=\"p\" data-sign=\"b65db927ae98120dd432b05c696f15202\"><p>test<\/p><\/p><h3 data-lines=\"2\" data-sign=\"d7c8ad1cf43ab6f6bfc7bfdf904defaa\" id=\"%E5%8F%AF%E8%A1%8C%E6%80%A7%E5%88%86%E6%9E%90\"><a href=\"#%E5%8F%AF%E8%A1%8C%E6%80%A7%E5%88%86%E6%9E%90\" class=\"anchor\" rel=\"noopener\" target=\"_blank\"><\/a>可行性分析<\/h3><h4 data-lines=\"2\" data-sign=\"ee22c2b1a55321b57153b639d6c7a0cc\" id=\"%E7%94%A8%E6%88%B7%E8%B0%83%E7%A0%94\"><a href=\"#%E7%94%A8%E6%88%B7%E8%B0%83%E7%A0%94\" class=\"anchor\" rel=\"noopener\" target=\"_blank\"><\/a>用户调研<\/h4><p data-lines=\"2\" data-type=\"p\" data-sign=\"6eb8822fb3dbd5831140faa0ccca1a6f2\"><br  \/><\/p><h3 data-lines=\"2\" data-sign=\"e9e8ca2c97aa30632c80d3b15b069eab\" id=\"%E6%B6%89%E4%BC%97\"><a href=\"#%E6%B6%89%E4%BC%97\" class=\"anchor\" rel=\"noopener\" target=\"_blank\"><\/a>涉众<\/h3><p data-lines=\"2\" data-type=\"p\" data-sign=\"f193bfbd0cae141c2439c9228729dc512\">可能涉及的用户，如果有则需明确说明，如试用用户，正式用户等。<\/p><div data-sign=\"7d0ecc46fe4eaef0e47665ca50b9679d\" data-lines=\"1\" class=\"cherry-table-container simple-table\"><div><div class=\"scroll_table\"><table class=\"cherry-table\"><thead><tr><th align=\"left\" style=\"width: 66.0469px; text-align: center;\">CRM<\/th><th align=\"left\" style=\"width: 57.9219px; text-align: center;\">Plus<\/th><th align=\"left\" style=\"width: 41.7031px; text-align: center;\">DX<\/th><th align=\"left\" style=\"width: 50.8281px; text-align: center;\">Lite<\/th><th align=\"left\" style=\"width: 77.1875px; text-align: center;\">Smart<\/th><th align=\"left\" style=\"width: 51.9062px; text-align: center;\">Pro<\/th><\/tr><\/thead><tbody><tr style=\"word-break: break-word;\"><td data-cell-id=\"7643-1628359000116-cell-1-0\" style=\"word-break: break-all; margin: 0px; padding: 5px; height: 28px; white-space: normal; border-width: 1px; border-style: solid; overflow: inherit; vertical-align: middle; text-align: center; color: #393939; width: 66.0469px; border-bottom-color: initial !important;\"><span style=\"word-break: break-word; line-height: inherit;\">√<\/span><\/td><td data-cell-id=\"7643-1628359000116-cell-1-1\" style=\"word-break: break-all; margin: 0px; padding: 5px; height: 28px; white-space: normal; border-width: 1px; border-style: solid; overflow: inherit; vertical-align: middle; text-align: center; color: #393939; width: 57.9219px; border-bottom-color: initial !important;\"><span style=\"word-break: break-word; line-height: inherit;\">√<\/span><\/td><td data-cell-id=\"7643-1628359000116-cell-1-2\" style=\"word-break: break-all; margin: 0px; padding: 5px; height: 28px; white-space: normal; border-width: 1px; border-style: solid; overflow: inherit; vertical-align: middle; text-align: center; color: #393939; width: 41.7031px; border-bottom-color: initial !important;\"><span style=\"word-break: break-word; line-height: inherit;\">√<\/span><\/td><td data-cell-id=\"7643-1628359000116-cell-1-3\" style=\"word-break: break-all; margin: 0px; padding: 5px; height: 28px; white-space: normal; border-width: 1px; border-style: solid; overflow: inherit; vertical-align: middle; text-align: center; color: #393939; width: 50.8281px; border-bottom-color: initial !important;\"><span style=\"word-break: break-word; line-height: inherit;\">√<\/span><\/td><td data-cell-id=\"7643-1628359000116-cell-1-4\" style=\"word-break: break-all; margin: 0px; padding: 5px; height: 28px; white-space: normal; border-width: 1px; border-style: solid; overflow: inherit; vertical-align: middle; text-align: center; color: #393939; width: 77.1875px; border-bottom-color: initial !important;\"><span style=\"word-break: break-word; line-height: inherit;\">√<\/span><\/td><td data-cell-id=\"7643-1628359000116-cell-1-5\" style=\"word-break: break-all; margin: 0px; padding: 5px; height: 28px; white-space: normal; border-width: 1px; border-style: solid; overflow: inherit; vertical-align: middle; text-align: center; color: #393939; width: 51.9062px; border-bottom-color: initial !important;\"><span style=\"word-break: break-word; line-height: inherit;\">√<\/span><\/td><\/tr><\/tbody><\/table><\/div><\/div><\/div><div data-sign=\"a865f45c80072a1984298ec28ca732e1\" data-lines=\"1\" class=\"cherry-table-container simple-table\"><br  \/><\/div><h4 data-lines=\"1\" data-sign=\"98da56e051e1c7a1bc5a5a23ac79476d\" id=\"%E5%AF%B9%E5%85%B6%E4%BB%96%E4%BA%A7%E5%93%81%E7%9A%84%E5%BD%B1%E5%93%8D\"><a href=\"#%E5%AF%B9%E5%85%B6%E4%BB%96%E4%BA%A7%E5%93%81%E7%9A%84%E5%BD%B1%E5%93%8D\" class=\"anchor\" rel=\"noopener\" target=\"_blank\"><\/a>对其他产品的影响<\/h4><p data-lines=\"2\" data-type=\"p\" data-sign=\"516639e4408cd359467a9b25731b28a52\">是否对其他产品、功能有影响，例如对移动端（存在新老版本兼容性问题），桌面端，其他功能模块的影响<\/p><div data-sign=\"929335bc8612c20e9f1ec3dfd0e1c41d\" data-lines=\"1\" class=\"cherry-table-container simple-table\"><div><div class=\"scroll_table\"><table class=\"cherry-table\"><tbody><tr><td>Web<\/td><td>移动端<\/td><td>桌面端<\/td><\/tr><tr><td><\/td><td><\/td><td><\/td><\/tr><\/tbody><\/table><\/div><\/div><\/div><h3 data-lines=\"1\" data-sign=\"2f4fa0af2a7bae11b3b42e782f98e51c\" id=\"%E8%AF%A6%E7%BB%86%E5%8A%9F%E8%83%BD\"><a href=\"#%E8%AF%A6%E7%BB%86%E5%8A%9F%E8%83%BD\" class=\"anchor\" rel=\"noopener\" target=\"_blank\"><\/a>详细功能<\/h3><h4 data-lines=\"2\" data-sign=\"370be40882fa3f186123ae3f1ba67200\" id=\"%E9%9C%80%E6%B1%82%E5%86%85%E5%AE%B9\"><a href=\"#%E9%9C%80%E6%B1%82%E5%86%85%E5%AE%B9\" class=\"anchor\" rel=\"noopener\" target=\"_blank\"><\/a>需求内容<\/h4><p data-lines=\"2\" data-type=\"p\" data-sign=\"19e06836f53bb06e0ffaeb4fc58f27962\">详细具体需求内容。包括如下内容：<\/p><div data-sign=\"3bb5f59c07dfb398fc622dbb2f2dbb81\" data-lines=\"1\" class=\"cherry-table-container simple-table\"><div><div class=\"scroll_table\"><table class=\"cherry-table\"><tbody><tr><td>所需模块<\/td><td>需求内容<\/td><td>优先级<\/td><td>原型图<\/td><td>需求详细描述<\/td><\/tr><tr><td><\/td><td><\/td><td><\/td><td><\/td><td><\/td><\/tr><\/tbody><\/table><\/div><\/div><\/div><h4 data-lines=\"1\" data-sign=\"46e4ed3679d31b7e2b5b4c9c63b2dec3\" id=\"%E4%BA%A4%E4%BA%92%E5%9C%B0%E5%9D%80\"><a href=\"#%E4%BA%A4%E4%BA%92%E5%9C%B0%E5%9D%80\" class=\"anchor\" rel=\"noopener\" target=\"_blank\"><\/a>交互地址<\/h4><p data-lines=\"2\" data-type=\"p\" data-sign=\"17e1f69ae2b845a9dabac384ed543df12\">交互页面地址<\/p><h4 data-lines=\"2\" data-sign=\"333d2d4b0a9f0e3989dedbbfc3a792de\" id=\"%E7%BB%9F%E8%AE%A1%E9%9C%80%E6%B1%82\"><a href=\"#%E7%BB%9F%E8%AE%A1%E9%9C%80%E6%B1%82\" class=\"anchor\" rel=\"noopener\" target=\"_blank\"><\/a>统计需求<\/h4><p data-lines=\"2\" data-type=\"p\" data-sign=\"cf30ee87831231458410fcdf1179a3dc2\">具体统计需求<\/p>",
    "old_markdown_description": "",
    "old_description_type": "1",
    "old_creator": "智齿工单",
    "old_created": "2023-01-10 13:47:58",
    "old_modified": "2023-09-14 14:36:07",
    "old_parent_id": "0",
    "old_children_id": "||11********001044989",
    "old_ancestor_id": "11********001039457",
    "old_path": "11********001039457:",
    "old_level": "0",
    "old_workspace_id": "********",
    "old_status": "status_2",
    "old_flows": "",
    "old_priority": "2",
    "old_owner": "Ruisenlin;",
    "old_participator": ";无;Ruisenlin",
    "old_cc": "",
    "old_begin": "",
    "old_due": "",
    "old_source": "用户反馈",
    "old_workitem_id": "",
    "old_iteration_id": "0",
    "old_issue_id": "",
    "old_support_id": "",
    "old_support_forum_id": "",
    "old_module": "",
    "old_completed": "",
    "old_templated_id": "11********001000409",
    "old_delay_count": "",
    "old_type": "",
    "old_status_append": "",
    "old_business_value": "",
    "old_tech_risk": "",
    "old_size": "",
    "old_import_flag": "0",
    "old_effort": "",
    "old_effort_completed": "0",
    "old_exceed": "0",
    "old_remain": "0",
    "old_progress": "0",
    "old_release_id": "0",
    "old_feature": "",
    "old_entity_type": "Story",
    "old_custom_field_one": "",
    "old_custom_field_two": "",
    "old_custom_field_three": "",
    "old_custom_field_four": "",
    "old_custom_field_five": "",
    "old_custom_field_six": "",
    "old_custom_field_seven": "",
    "old_custom_field_eight": "",
    "old_attachment_count": "0",
    "old_has_attachment": "0",
    "old_developer": "Ruisenlin",
    "old_bug_id": "",
    "old_test_focus": "",
    "old_category_id": "-1",
    "old_version": "",
    "old_confidential": "N",
    "old_created_from": "api",
    "old_follower": "",
    "old_sync_type": "",
    "old_predecessor_count": "0",
    "old_is_archived": "0",
    "old_modifier": "",
    "old_progress_manual": "0",
    "old_successor_count": "0",
    "old_label": "",
    "old_step": "",
    "old_custom_field_9": "",
    "old_custom_field_10": "",
    "old_custom_field_11": "20230908000083",
    "old_custom_field_12": "2022-12-29",
    "old_custom_field_13": "",
    "old_custom_field_14": "",
    "old_custom_field_15": "",
    "old_custom_field_16": "",
    "old_custom_field_17": "",
    "old_custom_field_18": "",
    "old_custom_field_19": "",
    "old_custom_field_20": "",
    "old_custom_field_21": "",
    "old_custom_field_22": "",
    "old_custom_field_23": "",
    "old_custom_field_24": "",
    "old_custom_field_25": "",
    "old_custom_field_26": "",
    "old_custom_field_27": "",
    "old_custom_field_28": "",
    "old_custom_field_29": "",
    "old_custom_field_30": "",
    "old_custom_field_31": "",
    "old_custom_field_32": "",
    "old_custom_field_33": "",
    "old_custom_field_34": "",
    "old_custom_field_35": "",
    "old_custom_field_36": "",
    "old_custom_field_37": "",
    "old_custom_field_38": "",
    "old_custom_field_39": "",
    "old_custom_field_40": "",
    "old_custom_field_41": "",
    "old_custom_field_42": "",
    "old_custom_field_43": "",
    "old_custom_field_44": "",
    "old_custom_field_45": "",
    "old_custom_field_46": "",
    "old_custom_field_47": "",
    "old_custom_field_48": "",
    "old_custom_field_49": "",
    "old_custom_field_50": "",
    "old_custom_field_51": "",
    "old_custom_field_52": "",
    "old_custom_field_53": "",
    "old_custom_field_54": "",
    "old_custom_field_55": "",
    "old_custom_field_56": "",
    "old_custom_field_57": "",
    "old_custom_field_58": "",
    "old_custom_field_59": "",
    "old_custom_field_60": "",
    "old_custom_field_61": "",
    "old_custom_field_62": "",
    "old_custom_field_63": "",
    "old_custom_field_64": "",
    "old_custom_field_65": "",
    "old_custom_field_66": "",
    "old_custom_field_67": "",
    "old_custom_field_68": "",
    "old_custom_field_69": "",
    "old_custom_field_70": "",
    "old_custom_field_71": "",
    "old_custom_field_72": "",
    "old_custom_field_73": "",
    "old_custom_field_74": "",
    "old_custom_field_75": "",
    "old_custom_field_76": "",
    "old_custom_field_77": "",
    "old_custom_field_78": "",
    "old_custom_field_79": "",
    "old_custom_field_80": "",
    "old_custom_field_81": "",
    "old_custom_field_82": "",
    "old_custom_field_83": "",
    "old_custom_field_84": "",
    "old_custom_field_85": "",
    "old_custom_field_86": "",
    "old_custom_field_87": "",
    "old_custom_field_88": "",
    "old_custom_field_89": "",
    "old_custom_field_90": "",
    "old_custom_field_91": "",
    "old_custom_field_92": "",
    "old_custom_field_93": "",
    "old_custom_field_94": "",
    "old_custom_field_95": "",
    "old_custom_field_96": "",
    "old_custom_field_97": "",
    "old_custom_field_98": "",
    "old_custom_field_99": "",
    "old_custom_field_100": "",
    "old_custom_field_101": "",
    "old_custom_field_102": "",
    "old_custom_field_103": "",
    "old_custom_field_104": "",
    "old_custom_field_105": "",
    "old_custom_field_106": "",
    "old_custom_field_107": "",
    "old_custom_field_108": "",
    "old_custom_field_109": "",
    "old_custom_field_110": "",
    "old_custom_field_111": "",
    "old_custom_field_112": "",
    "old_custom_field_113": "",
    "old_custom_field_114": "",
    "old_custom_field_115": "",
    "old_custom_field_116": "",
    "old_custom_field_117": "",
    "old_custom_field_118": "",
    "old_custom_field_119": "",
    "old_custom_field_120": "",
    "old_custom_field_121": "",
    "old_custom_field_122": "",
    "old_custom_field_123": "",
    "old_custom_field_124": "",
    "old_custom_field_125": "",
    "old_custom_field_126": "",
    "old_custom_field_127": "",
    "old_custom_field_128": "",
    "old_custom_field_129": "",
    "old_custom_field_130": "",
    "old_custom_field_131": "",
    "old_custom_field_132": "",
    "old_custom_field_133": "",
    "old_custom_field_134": "",
    "old_custom_field_135": "",
    "old_custom_field_136": "",
    "old_custom_field_137": "",
    "old_custom_field_138": "",
    "old_custom_field_139": "",
    "old_custom_field_140": "",
    "old_custom_field_141": "",
    "old_custom_field_142": "",
    "old_custom_field_143": "",
    "old_custom_field_144": "",
    "old_custom_field_145": "",
    "old_custom_field_146": "",
    "old_custom_field_147": "",
    "old_custom_field_148": "",
    "old_custom_field_149": "",
    "old_custom_field_150": "",
    "old_custom_field_151": "",
    "old_custom_field_152": "",
    "old_custom_field_153": "",
    "old_custom_field_154": "",
    "old_custom_field_155": "",
    "old_custom_field_156": "",
    "old_custom_field_157": "",
    "old_custom_field_158": "",
    "old_custom_field_159": "",
    "old_custom_field_160": "",
    "old_custom_field_161": "",
    "old_custom_field_162": "",
    "old_custom_field_163": "",
    "old_custom_field_164": "",
    "old_custom_field_165": "",
    "old_custom_field_166": "",
    "old_custom_field_167": "",
    "old_custom_field_168": "",
    "old_custom_field_169": "",
    "old_custom_field_170": "",
    "old_custom_field_171": "",
    "old_custom_field_172": "",
    "old_custom_field_173": "",
    "old_custom_field_174": "",
    "old_custom_field_175": "",
    "old_custom_field_176": "",
    "old_custom_field_177": "",
    "old_custom_field_178": "",
    "old_custom_field_179": "",
    "old_custom_field_180": "",
    "old_custom_field_181": "",
    "old_custom_field_182": "",
    "old_custom_field_183": "",
    "old_custom_field_184": "",
    "old_custom_field_185": "",
    "old_custom_field_186": "",
    "old_custom_field_187": "",
    "old_custom_field_188": "",
    "old_custom_field_189": "",
    "old_custom_field_190": "",
    "old_custom_field_191": "",
    "old_custom_field_192": "",
    "old_custom_field_193": "",
    "old_custom_field_194": "",
    "old_custom_field_195": "",
    "old_custom_field_196": "",
    "old_custom_field_197": "",
    "old_custom_field_198": "",
    "old_custom_field_199": "",
    "old_custom_field_200": "",
    "old_custom_plan_field_1": "0",
    "old_custom_plan_field_2": "0",
    "old_custom_plan_field_3": "0",
    "old_custom_plan_field_4": "0",
    "old_custom_plan_field_5": "0",
    "old_custom_plan_field_6": "0",
    "old_custom_plan_field_7": "0",
    "old_custom_plan_field_8": "0",
    "old_custom_plan_field_9": "0",
    "old_custom_plan_field_10": "0",
    "change_fields": "iteration_id,modified",
    "secret": "",
    "rio_token": "",
    "devproxy_host": "http:\/\/hook-proxifer.tapd.woa.com",
    "queue_id": "197632486",
    "event_id": "127409886",
    "created": "2023-09-14 18:18:35"
}
JSON;

        $rejectBug = <<< JSON
{
    "event": "bug::update",
    "event_from": "web",
    "referer": "https:\/\/www.tapd.cn\/********\/bugtrace\/bugs\/bug_workflow\/11********001090777\/timeline?width=627&height=auto&iframe=true&trigger_dom_id=bug-status-11********001090777&rand=1694939184496",
    "workspace_id": "********",
    "current_user": "Ruisenlin",
    "id": "11********001090777",
    "old_id": "11********001090777",
    "old_sid": "0",
    "old_project_id": "********",
    "old_parent_id": "",
    "old_title":"【工单#20230908000083】0908测试",
    "old_description_type": "2",
    "old_module": "企业管理",
    "old_milestone": "",
    "old_reporter": "Ruisenlin",
    "old_deadline": "2023-10-12",
    "old_created": "2023-09-08 16:25:49",
    "old_resolved": "",
    "old_closed": "",
    "old_modified": "2023-09-17 16:26:23",
    "old_lastmodify": "Ruisenlin",
    "old_auditer": "",
    "old_de": "Ruisenlin",
    "old_te": "",
    "old_confirmer": "",
    "old_current_owner": "Ruisenlin;",
    "old_participator": "Ruisenlin",
    "old_closer": "Ruisenlin",
    "old_status": "acknowledged",
    "old_resolution": "",
    "old_priority": "medium",
    "old_severity": "",
    "old_platform": "",
    "old_os": "",
    "old_testmode": "常规功能测试",
    "old_testtype": "",
    "old_testphase": "",
    "old_source": "",
    "old_frequency": "",
    "old_cc": "",
    "old_estimate": "",
    "old_flows": "in_progress|acknowledged|rejected|reopened|new|acknowledged",
    "old_version_report": "",
    "old_version_test": "",
    "old_version_fix": "",
    "old_version_close": "",
    "old_delayed": "0",
    "old_custom_field_one": "20230917000001",
    "old_custom_field_two": "2023-09-08 16:09:07",
    "old_custom_field_three": "web端",
    "old_custom_field_four": "",
    "old_custom_field_five": "",
    "old_regression_number": "0",
    "old_issue_id": "",
    "old_support_id": "",
    "old_support_forum_id": "",
    "old_created_from": "",
    "old_baseline_find": "",
    "old_baseline_join": "",
    "old_baseline_close": "",
    "old_ticket_id": "",
    "old_story_id": "",
    "old_baseline_test": "",
    "old_originphase": "",
    "old_sourcephase": "",
    "old_bugtype": "",
    "old_feature": "",
    "old_in_progress_time": "",
    "old_verify_time": "",
    "old_reject_time": "",
    "old_reopen_time": "2023-09-17 16:26:01",
    "old_audit_time": "",
    "old_suspend_time": "",
    "old_assigned_time": "",
    "old_iteration_id": "0",
    "old_custom_field_6": "",
    "old_custom_field_7": "2023-09-17 16:26",
    "old_custom_field_8": "2023-09-17 16:26",
    "old_custom_field_9": "否",
    "old_custom_field_10": "",
    "old_custom_field_11": "",
    "old_custom_field_12": "",
    "old_custom_field_13": "",
    "old_custom_field_14": "",
    "old_custom_field_15": "",
    "old_custom_field_16": "",
    "old_custom_field_17": "",
    "old_custom_field_18": "",
    "old_custom_field_19": "",
    "old_custom_field_20": "",
    "old_custom_field_21": "",
    "old_custom_field_22": "",
    "old_custom_field_23": "",
    "old_custom_field_24": "",
    "old_custom_field_25": "",
    "old_custom_field_26": "",
    "old_custom_field_27": "",
    "old_custom_field_28": "",
    "old_custom_field_29": "",
    "old_custom_field_30": "",
    "old_custom_field_31": "",
    "old_custom_field_32": "",
    "old_custom_field_33": "",
    "old_custom_field_34": "",
    "old_custom_field_35": "",
    "old_custom_field_36": "",
    "old_custom_field_37": "",
    "old_custom_field_38": "",
    "old_custom_field_39": "",
    "old_custom_field_40": "",
    "old_custom_field_41": "",
    "old_custom_field_42": "",
    "old_custom_field_43": "",
    "old_custom_field_44": "",
    "old_custom_field_45": "",
    "old_custom_field_46": "",
    "old_custom_field_47": "",
    "old_custom_field_48": "",
    "old_custom_field_49": "",
    "old_custom_field_50": "",
    "old_custom_field_51": "",
    "old_custom_field_52": "",
    "old_custom_field_53": "",
    "old_custom_field_54": "",
    "old_custom_field_55": "",
    "old_custom_field_56": "",
    "old_custom_field_57": "",
    "old_custom_field_58": "",
    "old_custom_field_59": "",
    "old_custom_field_60": "",
    "old_custom_field_61": "",
    "old_custom_field_62": "",
    "old_custom_field_63": "",
    "old_custom_field_64": "",
    "old_custom_field_65": "",
    "old_custom_field_66": "",
    "old_custom_field_67": "",
    "old_custom_field_68": "",
    "old_custom_field_69": "",
    "old_custom_field_70": "",
    "old_custom_field_71": "",
    "old_custom_field_72": "",
    "old_custom_field_73": "",
    "old_custom_field_74": "",
    "old_custom_field_75": "",
    "old_custom_field_76": "",
    "old_custom_field_77": "",
    "old_custom_field_78": "",
    "old_custom_field_79": "",
    "old_custom_field_80": "",
    "old_custom_field_81": "",
    "old_custom_field_82": "",
    "old_custom_field_83": "",
    "old_custom_field_84": "",
    "old_custom_field_85": "",
    "old_custom_field_86": "",
    "old_custom_field_87": "",
    "old_custom_field_88": "",
    "old_custom_field_89": "",
    "old_custom_field_90": "",
    "old_custom_field_91": "",
    "old_custom_field_92": "",
    "old_custom_field_93": "",
    "old_custom_field_94": "",
    "old_custom_field_95": "",
    "old_custom_field_96": "",
    "old_custom_field_97": "",
    "old_custom_field_98": "",
    "old_custom_field_99": "",
    "old_custom_field_100": "",
    "old_custom_plan_field_1": "0",
    "old_custom_plan_field_2": "0",
    "old_custom_plan_field_3": "0",
    "old_custom_plan_field_4": "0",
    "old_custom_plan_field_5": "0",
    "old_custom_plan_field_6": "0",
    "old_custom_plan_field_7": "0",
    "old_custom_plan_field_8": "0",
    "old_custom_plan_field_9": "0",
    "old_custom_plan_field_10": "0",
    "old_template_id": "11********001003474",
    "old_begin": "",
    "old_due": "",
    "old_release_id": "",
    "old_fixer": "",
    "old_follower": "",
    "old_sync_type": "",
    "old_label": "",
    "old_effort": "",
    "old_effort_completed": "0",
    "old_exceed": "0",
    "old_remain": "0",
    "old_progress": "0",
    "old_size": "",
    "change_fields": "closed,status,flows,reject_time,modified",
    "secret": "",
    "rio_token": "",
    "devproxy_host": "http:\/\/hook-proxifer.tapd.woa.com",
    "queue_id": "197980328",
    "event_id": "127612814",
    "created": "2023-09-17 16:26:27"
}
JSON;


        $updateStoryReleaseId = <<< JSON
{
    "event": "bug::update",
    "event_from": "auto_task",
    "referer": "",
    "workspace_id": "********",
    "current_user": "yangyang",
    "id": "11********001100727",
    "old_id": "11********001100727",
    "old_sid": "0",
    "old_project_id": "********",
    "old_parent_id": "",
    "old_title": "【工单#00000000000】测试发布计划自动集成",
    "old_description": "<p><span style",
    "old_markdown_description": "",
    "old_description_type": "1",
    "old_module": "",
    "old_milestone": "",
    "old_reporter": "yangyang",
    "old_deadline": "",
    "old_created": "2024-05-28 14:58:12",
    "old_resolved": "2024-05-28 15:01:49",
    "old_closed": "2024-05-28 16:03:18",
    "old_modified": "2024-05-28 16:03:18",
    "old_lastmodify": "yangyang",
    "old_auditer": "",
    "old_de": "",
    "old_te": "",
    "old_confirmer": "",
    "old_current_owner": "yangyang;",
    "old_participator": "yangyang",
    "old_closer": "yangyang",
    "old_status": "feedback",
    "old_resolution": "transferred to story",
    "old_priority": "",
    "old_severity": "",
    "old_platform": "",
    "old_os": "",
    "old_testmode": "",
    "old_testtype": "",
    "old_testphase": "",
    "old_source": "",
    "old_frequency": "",
    "old_cc": "",
    "old_estimate": "",
    "old_flows": "in_progress|resolved|feedback",
    "old_version_report": "",
    "old_version_test": "",
    "old_version_fix": "",
    "old_version_close": "",
    "old_delayed": "0",
    "old_custom_field_one": "",
    "old_custom_field_two": "",
    "old_custom_field_three": "",
    "old_custom_field_four": "",
    "old_custom_field_five": "",
    "old_regression_number": "0",
    "old_issue_id": "",
    "old_support_id": "",
    "old_support_forum_id": "",
    "old_created_from": "",
    "old_baseline_find": "",
    "old_baseline_join": "",
    "old_baseline_close": "",
    "old_ticket_id": "",
    "old_story_id": "11********001081006",
    "old_baseline_test": "",
    "old_originphase": "",
    "old_sourcephase": "",
    "old_bugtype": "",
    "old_feature": "",
    "old_in_progress_time": "2024-05-28 14:58:12",
    "old_verify_time": "",
    "old_reject_time": "",
    "old_reopen_time": "",
    "old_audit_time": "",
    "old_suspend_time": "",
    "old_assigned_time": "",
    "old_iteration_id": "0",
    "old_custom_field_6": "工程建设组",
    "old_custom_field_7": "",
    "old_custom_field_8": "",
    "old_custom_field_9": "",
    "old_custom_field_10": "",
    "old_custom_field_11": "",
    "old_custom_field_12": "",
    "old_custom_field_13": "",
    "old_custom_field_14": "",
    "old_custom_field_15": "",
    "old_custom_field_16": "",
    "old_custom_field_17": "",
    "old_custom_field_18": "",
    "old_custom_field_19": "",
    "old_custom_field_20": "",
    "old_custom_field_21": "",
    "old_custom_field_22": "",
    "old_custom_field_23": "",
    "old_custom_field_24": "",
    "old_custom_field_25": "",
    "old_custom_field_26": "",
    "old_custom_field_27": "",
    "old_custom_field_28": "",
    "old_custom_field_29": "",
    "old_custom_field_30": "",
    "old_custom_field_31": "",
    "old_custom_field_32": "",
    "old_custom_field_33": "",
    "old_custom_field_34": "",
    "old_custom_field_35": "",
    "old_custom_field_36": "",
    "old_custom_field_37": "",
    "old_custom_field_38": "",
    "old_custom_field_39": "",
    "old_custom_field_40": "",
    "old_custom_field_41": "",
    "old_custom_field_42": "",
    "old_custom_field_43": "",
    "old_custom_field_44": "",
    "old_custom_field_45": "",
    "old_custom_field_46": "",
    "old_custom_field_47": "",
    "old_custom_field_48": "",
    "old_custom_field_49": "",
    "old_custom_field_50": "",
    "old_custom_field_51": "",
    "old_custom_field_52": "",
    "old_custom_field_53": "",
    "old_custom_field_54": "",
    "old_custom_field_55": "",
    "old_custom_field_56": "",
    "old_custom_field_57": "",
    "old_custom_field_58": "",
    "old_custom_field_59": "",
    "old_custom_field_60": "",
    "old_custom_field_61": "",
    "old_custom_field_62": "",
    "old_custom_field_63": "",
    "old_custom_field_64": "",
    "old_custom_field_65": "",
    "old_custom_field_66": "",
    "old_custom_field_67": "",
    "old_custom_field_68": "",
    "old_custom_field_69": "",
    "old_custom_field_70": "",
    "old_custom_field_71": "",
    "old_custom_field_72": "",
    "old_custom_field_73": "",
    "old_custom_field_74": "",
    "old_custom_field_75": "",
    "old_custom_field_76": "",
    "old_custom_field_77": "",
    "old_custom_field_78": "",
    "old_custom_field_79": "",
    "old_custom_field_80": "",
    "old_custom_field_81": "",
    "old_custom_field_82": "",
    "old_custom_field_83": "",
    "old_custom_field_84": "",
    "old_custom_field_85": "",
    "old_custom_field_86": "",
    "old_custom_field_87": "",
    "old_custom_field_88": "",
    "old_custom_field_89": "",
    "old_custom_field_90": "",
    "old_custom_field_91": "",
    "old_custom_field_92": "",
    "old_custom_field_93": "",
    "old_custom_field_94": "",
    "old_custom_field_95": "",
    "old_custom_field_96": "",
    "old_custom_field_97": "",
    "old_custom_field_98": "",
    "old_custom_field_99": "",
    "old_custom_field_100": "",
    "old_custom_plan_field_1": "0",
    "old_custom_plan_field_2": "0",
    "old_custom_plan_field_3": "0",
    "old_custom_plan_field_4": "0",
    "old_custom_plan_field_5": "0",
    "old_custom_plan_field_6": "0",
    "old_custom_plan_field_7": "0",
    "old_custom_plan_field_8": "0",
    "old_custom_plan_field_9": "0",
    "old_custom_plan_field_10": "0",
    "old_template_id": "11********001005063",
    "old_begin": "",
    "old_due": "",
    "old_release_id": "",
    "old_fixer": "yangyang",
    "old_follower": "",
    "old_sync_type": "",
    "old_label": "",
    "old_effort": "",
    "old_effort_completed": "0",
    "old_exceed": "0",
    "old_remain": "0",
    "old_progress": "0",
    "old_size": "",
    "new_de": "yangyang;",
    "new_custom_field_9": "否",
    "new_release_id": "11********001000106",
    "new_modified": "2024-05-28 16:03:19",
    "change_fields": "release_id,de,custom_field_9,modified",
    "secret": "",
    "rio_token": "",
    "devproxy_host": "http://websocket-proxy",
    "queue_id": "254265358",
    "event_id": "156098711",
    "created": "2024-05-28 16:03:19"
}
JSON;

        $updateBugReleaseId = <<< JSON
{
    "event": "bug::update",
    "event_from": "web",
    "referer": "https://www.tapd.cn/********/releases/planning_bug/11********001000106",
    "workspace_id": "********",
    "current_user": "yangyang",
    "id": "11********001100727",
    "old_id": "11********001100727",
    "old_sid": "0",
    "old_project_id": "********",
    "old_parent_id": "",
    "old_title": "【工单#00000000000】测试发布计划自动集成",
    "old_description": "<p><span style=",
    "old_markdown_description": "",
    "old_description_type": "1",
    "old_module": "",
    "old_milestone": "",
    "old_reporter": "yangyang",
    "old_deadline": "",
    "old_created": "2024-05-28 14:58:12",
    "old_resolved": "2024-05-28 15:01:49",
    "old_closed": "",
    "old_modified": "2024-05-28 15:02:11",
    "old_lastmodify": "yangyang",
    "old_auditer": "",
    "old_de": "",
    "old_te": "",
    "old_confirmer": "",
    "old_current_owner": "yangyang;",
    "old_participator": "",
    "old_closer": "",
    "old_status": "resolved",
    "old_resolution": "fixed",
    "old_priority": "",
    "old_severity": "",
    "old_platform": "",
    "old_os": "",
    "old_testmode": "",
    "old_testtype": "",
    "old_testphase": "",
    "old_source": "",
    "old_frequency": "",
    "old_cc": "",
    "old_estimate": "",
    "old_flows": "in_progress|resolved",
    "old_version_report": "",
    "old_version_test": "",
    "old_version_fix": "",
    "old_version_close": "",
    "old_delayed": "0",
    "old_custom_field_one": "",
    "old_custom_field_two": "",
    "old_custom_field_three": "",
    "old_custom_field_four": "",
    "old_custom_field_five": "",
    "old_regression_number": "0",
    "old_issue_id": "",
    "old_support_id": "",
    "old_support_forum_id": "",
    "old_created_from": "",
    "old_baseline_find": "",
    "old_baseline_join": "",
    "old_baseline_close": "",
    "old_ticket_id": "",
    "old_story_id": "",
    "old_baseline_test": "",
    "old_originphase": "",
    "old_sourcephase": "",
    "old_bugtype": "",
    "old_feature": "",
    "old_in_progress_time": "2024-05-28 14:58:12",
    "old_verify_time": "",
    "old_reject_time": "",
    "old_reopen_time": "",
    "old_audit_time": "",
    "old_suspend_time": "",
    "old_assigned_time": "",
    "old_iteration_id": "0",
    "old_custom_field_6": "工程建设组",
    "old_custom_field_7": "",
    "old_custom_field_8": "",
    "old_custom_field_9": "",
    "old_custom_field_10": "",
    "old_custom_field_11": "",
    "old_custom_field_12": "",
    "old_custom_field_13": "",
    "old_custom_field_14": "",
    "old_custom_field_15": "",
    "old_custom_field_16": "",
    "old_custom_field_17": "",
    "old_custom_field_18": "",
    "old_custom_field_19": "",
    "old_custom_field_20": "",
    "old_custom_field_21": "",
    "old_custom_field_22": "",
    "old_custom_field_23": "",
    "old_custom_field_24": "",
    "old_custom_field_25": "",
    "old_custom_field_26": "",
    "old_custom_field_27": "",
    "old_custom_field_28": "",
    "old_custom_field_29": "",
    "old_custom_field_30": "",
    "old_custom_field_31": "",
    "old_custom_field_32": "",
    "old_custom_field_33": "",
    "old_custom_field_34": "",
    "old_custom_field_35": "",
    "old_custom_field_36": "",
    "old_custom_field_37": "",
    "old_custom_field_38": "",
    "old_custom_field_39": "",
    "old_custom_field_40": "",
    "old_custom_field_41": "",
    "old_custom_field_42": "",
    "old_custom_field_43": "",
    "old_custom_field_44": "",
    "old_custom_field_45": "",
    "old_custom_field_46": "",
    "old_custom_field_47": "",
    "old_custom_field_48": "",
    "old_custom_field_49": "",
    "old_custom_field_50": "",
    "old_custom_field_51": "",
    "old_custom_field_52": "",
    "old_custom_field_53": "",
    "old_custom_field_54": "",
    "old_custom_field_55": "",
    "old_custom_field_56": "",
    "old_custom_field_57": "",
    "old_custom_field_58": "",
    "old_custom_field_59": "",
    "old_custom_field_60": "",
    "old_custom_field_61": "",
    "old_custom_field_62": "",
    "old_custom_field_63": "",
    "old_custom_field_64": "",
    "old_custom_field_65": "",
    "old_custom_field_66": "",
    "old_custom_field_67": "",
    "old_custom_field_68": "",
    "old_custom_field_69": "",
    "old_custom_field_70": "",
    "old_custom_field_71": "",
    "old_custom_field_72": "",
    "old_custom_field_73": "",
    "old_custom_field_74": "",
    "old_custom_field_75": "",
    "old_custom_field_76": "",
    "old_custom_field_77": "",
    "old_custom_field_78": "",
    "old_custom_field_79": "",
    "old_custom_field_80": "",
    "old_custom_field_81": "",
    "old_custom_field_82": "",
    "old_custom_field_83": "",
    "old_custom_field_84": "",
    "old_custom_field_85": "",
    "old_custom_field_86": "",
    "old_custom_field_87": "",
    "old_custom_field_88": "",
    "old_custom_field_89": "",
    "old_custom_field_90": "",
    "old_custom_field_91": "",
    "old_custom_field_92": "",
    "old_custom_field_93": "",
    "old_custom_field_94": "",
    "old_custom_field_95": "",
    "old_custom_field_96": "",
    "old_custom_field_97": "",
    "old_custom_field_98": "",
    "old_custom_field_99": "",
    "old_custom_field_100": "",
    "old_custom_plan_field_1": "0",
    "old_custom_plan_field_2": "0",
    "old_custom_plan_field_3": "0",
    "old_custom_plan_field_4": "0",
    "old_custom_plan_field_5": "0",
    "old_custom_plan_field_6": "0",
    "old_custom_plan_field_7": "0",
    "old_custom_plan_field_8": "0",
    "old_custom_plan_field_9": "0",
    "old_custom_plan_field_10": "0",
    "old_template_id": "11********001005063",
    "old_begin": "",
    "old_due": "",
    "old_release_id": "",
    "old_fixer": "yangyang",
    "old_follower": "",
    "old_sync_type": "",
    "old_label": "",
    "old_effort": "",
    "old_effort_completed": "0",
    "old_exceed": "0",
    "old_remain": "0",
    "old_progress": "0",
    "old_size": "",
    "new_release_id": "11********001000106",
    "new_modified": "2024-05-28 15:04:14",
    "change_fields": "release_id,modified",
    "secret": "",
    "rio_token": "",
    "devproxy_host": "http://websocket-proxy",
    "queue_id": "254223749",
    "event_id": "156078399",
    "created": "2024-05-28 15:04:14"
}
JSON;

        $removeBugReleaseId = <<< JSON
{
    "event": "bug::update",
    "event_from": "auto_task",
    "referer": "",
    "workspace_id": "********",
    "current_user": "yangyang",
    "id": "11********001100727",
    "old_id": "11********001100727",
    "old_sid": "0",
    "old_project_id": "********",
    "old_parent_id": "",
    "old_title": "【工单#00000000000】测试发布计划自动集成",
    "old_description": "<p><span style=",
    "old_markdown_description": "",
    "old_description_type": "1",
    "old_module": "",
    "old_milestone": "",
    "old_reporter": "yangyang",
    "old_deadline": "",
    "old_created": "2024-05-28 14:58:12",
    "old_resolved": "2024-05-28 15:01:49",
    "old_closed": "2024-05-28 16:03:18",
    "old_modified": "2024-05-28 16:03:18",
    "old_lastmodify": "yangyang",
    "old_auditer": "",
    "old_de": "",
    "old_te": "",
    "old_confirmer": "",
    "old_current_owner": "yangyang;",
    "old_participator": "yangyang",
    "old_closer": "yangyang",
    "old_status": "feedback",
    "old_resolution": "transferred to story",
    "old_priority": "",
    "old_severity": "",
    "old_platform": "",
    "old_os": "",
    "old_testmode": "",
    "old_testtype": "",
    "old_testphase": "",
    "old_source": "",
    "old_frequency": "",
    "old_cc": "",
    "old_estimate": "",
    "old_flows": "in_progress|resolved|feedback",
    "old_version_report": "",
    "old_version_test": "",
    "old_version_fix": "",
    "old_version_close": "",
    "old_delayed": "0",
    "old_custom_field_one": "",
    "old_custom_field_two": "",
    "old_custom_field_three": "",
    "old_custom_field_four": "",
    "old_custom_field_five": "",
    "old_regression_number": "0",
    "old_issue_id": "",
    "old_support_id": "",
    "old_support_forum_id": "",
    "old_created_from": "",
    "old_baseline_find": "",
    "old_baseline_join": "",
    "old_baseline_close": "",
    "old_ticket_id": "",
    "old_story_id": "11********001081006",
    "old_baseline_test": "",
    "old_originphase": "",
    "old_sourcephase": "",
    "old_bugtype": "",
    "old_feature": "",
    "old_in_progress_time": "2024-05-28 14:58:12",
    "old_verify_time": "",
    "old_reject_time": "",
    "old_reopen_time": "",
    "old_audit_time": "",
    "old_suspend_time": "",
    "old_assigned_time": "",
    "old_iteration_id": "0",
    "old_custom_field_6": "工程建设组",
    "old_custom_field_7": "",
    "old_custom_field_8": "",
    "old_custom_field_9": "",
    "old_custom_field_10": "",
    "old_custom_field_11": "",
    "old_custom_field_12": "",
    "old_custom_field_13": "",
    "old_custom_field_14": "",
    "old_custom_field_15": "",
    "old_custom_field_16": "",
    "old_custom_field_17": "",
    "old_custom_field_18": "",
    "old_custom_field_19": "",
    "old_custom_field_20": "",
    "old_custom_field_21": "",
    "old_custom_field_22": "",
    "old_custom_field_23": "",
    "old_custom_field_24": "",
    "old_custom_field_25": "",
    "old_custom_field_26": "",
    "old_custom_field_27": "",
    "old_custom_field_28": "",
    "old_custom_field_29": "",
    "old_custom_field_30": "",
    "old_custom_field_31": "",
    "old_custom_field_32": "",
    "old_custom_field_33": "",
    "old_custom_field_34": "",
    "old_custom_field_35": "",
    "old_custom_field_36": "",
    "old_custom_field_37": "",
    "old_custom_field_38": "",
    "old_custom_field_39": "",
    "old_custom_field_40": "",
    "old_custom_field_41": "",
    "old_custom_field_42": "",
    "old_custom_field_43": "",
    "old_custom_field_44": "",
    "old_custom_field_45": "",
    "old_custom_field_46": "",
    "old_custom_field_47": "",
    "old_custom_field_48": "",
    "old_custom_field_49": "",
    "old_custom_field_50": "",
    "old_custom_field_51": "",
    "old_custom_field_52": "",
    "old_custom_field_53": "",
    "old_custom_field_54": "",
    "old_custom_field_55": "",
    "old_custom_field_56": "",
    "old_custom_field_57": "",
    "old_custom_field_58": "",
    "old_custom_field_59": "",
    "old_custom_field_60": "",
    "old_custom_field_61": "",
    "old_custom_field_62": "",
    "old_custom_field_63": "",
    "old_custom_field_64": "",
    "old_custom_field_65": "",
    "old_custom_field_66": "",
    "old_custom_field_67": "",
    "old_custom_field_68": "",
    "old_custom_field_69": "",
    "old_custom_field_70": "",
    "old_custom_field_71": "",
    "old_custom_field_72": "",
    "old_custom_field_73": "",
    "old_custom_field_74": "",
    "old_custom_field_75": "",
    "old_custom_field_76": "",
    "old_custom_field_77": "",
    "old_custom_field_78": "",
    "old_custom_field_79": "",
    "old_custom_field_80": "",
    "old_custom_field_81": "",
    "old_custom_field_82": "",
    "old_custom_field_83": "",
    "old_custom_field_84": "",
    "old_custom_field_85": "",
    "old_custom_field_86": "",
    "old_custom_field_87": "",
    "old_custom_field_88": "",
    "old_custom_field_89": "",
    "old_custom_field_90": "",
    "old_custom_field_91": "",
    "old_custom_field_92": "",
    "old_custom_field_93": "",
    "old_custom_field_94": "",
    "old_custom_field_95": "",
    "old_custom_field_96": "",
    "old_custom_field_97": "",
    "old_custom_field_98": "",
    "old_custom_field_99": "",
    "old_custom_field_100": "",
    "old_custom_plan_field_1": "0",
    "old_custom_plan_field_2": "0",
    "old_custom_plan_field_3": "0",
    "old_custom_plan_field_4": "0",
    "old_custom_plan_field_5": "0",
    "old_custom_plan_field_6": "0",
    "old_custom_plan_field_7": "0",
    "old_custom_plan_field_8": "0",
    "old_custom_plan_field_9": "0",
    "old_custom_plan_field_10": "0",
    "old_template_id": "11********001005063",
    "old_begin": "",
    "old_due": "",
    "old_fixer": "yangyang",
    "old_follower": "",
    "old_sync_type": "",
    "old_label": "",
    "old_effort": "",
    "old_effort_completed": "0",
    "old_exceed": "0",
    "old_remain": "0",
    "old_progress": "0",
    "old_size": "",
    "new_de": "yangyang;",
    "new_custom_field_9": "否",
    "old_release_id": "11********001000106",
    "new_release_id": "0",
    "new_modified": "2024-05-28 16:03:19",
    "change_fields": "de,custom_field_9,release_id,modified",
    "secret": "",
    "rio_token": "",
    "devproxy_host": "http://websocket-proxy",
    "queue_id": "254265358",
    "event_id": "156098711",
    "created": "2024-05-28 16:03:19"
}
JSON;

        return [
//            [$bugCreate],
//            [$bugUpdate],
//            [$commentAdd],
//            [$commentUpdate],
//            [$iterationMove],
//            [$rejectBug],
            [$updateStoryReleaseId],
            [$updateBugReleaseId],
            [$removeBugReleaseId],
        ];
    }

    /**
     * @dataProvider tapdProvider
     */
    public function testTapd($webhookData)
    {
        $factory = new TapdHandlerFactory();
        $handler = $factory->createHandler();
        $handler->setData(json_decode($webhookData, true));
        $handler->handle();
    }


    public function testTapdShiftOut()
    {
        $data = [
            'release_id' => 1,
            'tapd_id' => 102,
            'tapd_type' => 'story',
            'user' => 'bogiangzheng',
        ];

        $tapdShiftOut = new TapdShiftOut();
        $tapdShiftOut->buildData($data);
        $token = $tapdShiftOut->getToken();
        var_dump($token);
    }

    public function testTapdShiftOutCheck()
    {
//        $token = '4b670907b9a215a4bde1da138f444220e1efb691616b7226d6e8a21e6d179cca';
        $token = '20539115f4881d03ebdbe05a043ee53f90b3256789db1e2cabd6a892c50e4c7d';
        $tapdShiftOut = new TapdShiftOut();
        $tapdShiftOut->check($token);
        echo 123;
    }

    public function testCirculationCommentsTask()
    {
        $event = '{"event":"bug_comment::add","event_from":"web","referer":"https://www.tapd.cn/47237250/bugtrace/bugs/view/1147237250001108395?only_content=true","workspace_id":"47237250","current_user":"been","id":"1147237250001126096","title":" 在状态 [待处理] 添加","description":"<p>[转回工单组] 测试</p>","author":"been","entity_id":"1147237250001108395","secret":"","rio_token":"","devproxy_host":"http://websocket-proxy","queue_id":"308925063","event_id":"179636387","created":"2024-11-28 10:05:15"}';
        $event = json_decode($event, true);
        $id = $event['entity_id'];
        $workspaceId = $event['workspace_id'];

        // 获取tapd缺陷详细数据
        $tapdClient = TapdClient::getInstance();
        $result = $tapdClient->getBugs(['workspace_id' => $workspaceId, 'id' => $id]);

        $bugInfo = $result[0]['Bug'];

        $task = new CirculationCommentsTask($event, $bugInfo);
        $task->run();
    }

    public function testBugUpdateTask()
    {
        $event = '{"event":"bug::update","event_from":"web","referer":"https:\/\/www.tapd.cn\/47237250\/bugtrace\/bugs\/view\/1147237250001108395?only_content=true","workspace_id":"47237250","current_user":"been","id":"1147237250001108395","old_id":"1147237250001108395","old_sid":"0","old_project_id":"47237250","old_parent_id":"","old_title":"\u3010\u5de5\u5355#\u3011\u6d4b\u8bd5tapd\u63d2\u4ef6\u63a5\u53e3\u60c5\u51b5","old_description":"<div data-inline-code-theme=\"red\" data-code-block-theme=\"default\"><h5 data-lines=\"1\" data-sign=\"f6e43c42078473eb1319d8bb58f015a4\" id=\"%E3%80%90%E7%8E%AF%E5%A2%83%E8%B4%A6%E5%8F%B7%E5%AF%86%E7%A0%81%E3%80%91%EF%BC%9A\" class=\"cherry-highlight-line\"><a href=\"#%E3%80%90%E7%8E%AF%E5%A2%83%E8%B4%A6%E5%8F%B7%E5%AF%86%E7%A0%81%E3%80%91%EF%BC%9A\" class=\"anchor\" target=\"_blank\"><\/a>\u3010<strong>\u73af\u5883&amp;\u8d26\u53f7\u5bc6\u7801<\/strong>\u3011\uff1a<\/h5><blockquote data-lines=\"4\" data-sign=\"69a74ce5a7c6b4d6dffff578cf3ec0ff_4\"><p data-sign=\"8c48d7df0d2532bb8458674ba7cc4e174\" data-type=\"p\" data-lines=\"4\">\u73af\u5883\uff1a<a href=\"https:\/\/crm.xiaoman.cn\" title=\"https:\/\/crm.xiaoman.cn\" target=\"_blank\">https:\/\/crm.xiaoman.cn<\/a><br  \/>\u5ba2\u6237\u8d26\u53f7\uff1a <br  \/>clientID= <br  \/>userID=<\/p><\/blockquote><h5 data-lines=\"1\" data-sign=\"5a6aa35a82da69ddb34dd525dc6bd0b2\" id=\"%E3%80%90%E8%87%AA%E6%B5%8B%E6%83%85%E5%86%B5%E3%80%91%EF%BC%9A\"><a href=\"#%E3%80%90%E8%87%AA%E6%B5%8B%E6%83%85%E5%86%B5%E3%80%91%EF%BC%9A\" class=\"anchor\" target=\"_blank\"><\/a>\u3010<strong>\u81ea\u6d4b\u60c5\u51b5<\/strong>\u3011\uff1a<\/h5><blockquote data-lines=\"1\" data-sign=\"2a55758a4f22df60c36210438c6359c0_1\"><p data-sign=\"4b4fb0c8341f87a726362c697cf52e451\" data-type=\"p\" data-lines=\"1\">\u5ba2\u6237\u53f7\u6709\u6b64\u95ee\u9898\uff1b \u6d4b\u8bd5\u53f7\u81ea\u6d4b\u6b63\u5e38\uff0c\u672a\u590d\u73b0<\/p><\/blockquote><h5 data-lines=\"2\" data-sign=\"9b6d029b7b4521f8768c0d06f5a00093\" id=\"%E3%80%90%E5%A4%8D%E7%8E%B0%E6%AD%A5%E9%AA%A4%E5%8F%8A%E9%97%AE%E9%A2%98%E7%8E%B0%E8%B1%A1%E3%80%91%EF%BC%9A\"><a href=\"#%E3%80%90%E5%A4%8D%E7%8E%B0%E6%AD%A5%E9%AA%A4%E5%8F%8A%E9%97%AE%E9%A2%98%E7%8E%B0%E8%B1%A1%E3%80%91%EF%BC%9A\" class=\"anchor\" target=\"_blank\"><\/a>\u3010<strong>\u590d\u73b0\u6b65\u9aa4\u53ca\u95ee\u9898\u73b0\u8c61<\/strong>\u3011\uff1a<\/h5><blockquote data-lines=\"1\" data-sign=\"a3856d4446ced2111fc3c7f1ddf19f6c_1\"><p data-sign=\"531058b41b7e924fdb704e82832bb6511\" data-type=\"p\" data-lines=\"1\">\u5f85\u63cf\u8ff0<\/p><\/blockquote><h5 data-lines=\"2\" data-sign=\"f6d93bde3691d3ab74f597ed282ffe07\" id=\"%E3%80%90%E9%A2%84%E6%9C%9F%E7%BB%93%E6%9E%9C%E3%80%91%EF%BC%9A\"><a href=\"#%E3%80%90%E9%A2%84%E6%9C%9F%E7%BB%93%E6%9E%9C%E3%80%91%EF%BC%9A\" class=\"anchor\" target=\"_blank\"><\/a>\u3010<strong>\u9884\u671f\u7ed3\u679c<\/strong>\u3011\uff1a<\/h5><blockquote data-lines=\"1\" data-sign=\"a3856d4446ced2111fc3c7f1ddf19f6c_1\"><p data-sign=\"531058b41b7e924fdb704e82832bb6511\" data-type=\"p\" data-lines=\"1\">\u5f85\u63cf\u8ff0<\/p><\/blockquote><h5 data-lines=\"2\" data-sign=\"b1b067fb3134c0fcc2eb4b6447681bea\" id=\"%E3%80%90%E5%A4%8D%E7%8E%B0%E9%93%BE%E6%8E%A5%E6%95%B0%E6%8D%AE%E6%8E%A5%E5%8F%A3%E5%8F%82%E6%95%B0%E3%80%91%EF%BC%9A\"><a href=\"#%E3%80%90%E5%A4%8D%E7%8E%B0%E9%93%BE%E6%8E%A5%E6%95%B0%E6%8D%AE%E6%8E%A5%E5%8F%A3%E5%8F%82%E6%95%B0%E3%80%91%EF%BC%9A\" class=\"anchor\" target=\"_blank\"><\/a>\u3010<strong>\u590d\u73b0\u94fe\u63a5|\u6570\u636e|\u63a5\u53e3\u53c2\u6570<\/strong>\u3011\uff1a<\/h5><blockquote data-lines=\"1\" data-sign=\"72265e0d2d1fcd5c4df6f8b17fa22e79_1\"><p data-sign=\"4ef795d3124b587d0d27cef020f8bd701\" data-type=\"p\" data-lines=\"1\">\u5f85\u7c98\u8d34<\/p><\/blockquote><h5 data-lines=\"2\" data-sign=\"1933422b3e78a99b8f84c0e421ab1e92\" id=\"%E3%80%90%E7%9B%B8%E5%85%B3%E6%88%AA%E5%9B%BE%E3%80%91%EF%BC%9A\"><a href=\"#%E3%80%90%E7%9B%B8%E5%85%B3%E6%88%AA%E5%9B%BE%E3%80%91%EF%BC%9A\" class=\"anchor\" target=\"_blank\"><\/a>\u3010<strong>\u76f8\u5173\u622a\u56fe<\/strong>\u3011\uff1a<\/h5><blockquote data-lines=\"1\" data-sign=\"110b25ac6045896e47afe9b32ec84517_1\"><p data-sign=\"d81bb206a889656035b929cd8bb1ef101\" data-type=\"p\" data-lines=\"1\">\u65e0<\/p><\/blockquote><h5 data-lines=\"2\" data-sign=\"000b590bc758a01a9e6a5baf85d5eb42\" id=\"%E3%80%90%E9%A2%84%E6%9C%9F%E4%BB%A3%E7%A0%81%E6%8F%90%E4%BA%A4%E5%88%86%E6%94%AF%E3%80%91%EF%BC%9A\"><a href=\"#%E3%80%90%E9%A2%84%E6%9C%9F%E4%BB%A3%E7%A0%81%E6%8F%90%E4%BA%A4%E5%88%86%E6%94%AF%E3%80%91%EF%BC%9A\" class=\"anchor\" target=\"_blank\"><\/a>\u3010<strong>\u9884\u671f\u4ee3\u7801\u63d0\u4ea4\u5206\u652f<\/strong>\u3011\uff1a<\/h5><blockquote data-lines=\"7\" data-sign=\"8dacd0da593818cdf1c3e557b225f0ff_7\"><p data-sign=\"43b591da2b291242b94dce002927ffc57\" data-type=\"p\" data-lines=\"7\">1\u3001\u5efa\u8bae\u63d0\u4ea4\u81f3\uff1a\u6700\u8fd1bugfix\uff0c \u5e76\u5c06\u6b64\u7f3a\u9677\u79fb\u81f3\u76f8\u5e94\u5468bugfix\u91cc\u8ddf\u8fdb\uff1b<br  \/> 2\u3001\u5f00\u53d1\u7b2c\u4e00\u65f6\u95f4\u7ed9\u51fa\u539f\u56e0\u548c\u9884\u8ba1\u89e3\u51b3\u65f6\u95f4\u5728\u8bc4\u8bba\u4e2d\uff0c\u5e26\u201c\u667a\u9f7f\uff1a\u201d\u5b57\u773c\u4f1a\u81ea\u52a8\u540c\u6b65\u81f3\u667a\u9f7f\u5de5\u5355\u7cfb\u7edf<br  \/>    \u8bc4\u8bba\u683c\u5f0f\uff1a<br  \/> \t\u667a\u9f7f\uff1a\u662f\u5426bug\uff1a \uff0c\u539f\u56e0\uff1axxxxx\uff0c\u5f71\u54cd\u8303\u56f4\uff1axxxx\uff0c\u9884\u8ba1\u89e3\u51b3\u65b9\u6848\uff1axxx\uff0c\u9884\u8ba1\u4e0a\u7ebf\u65f6\u95f4\uff1axxx<br  \/>3\u3001\u5f00\u53d1\u5199\u597d2\u540e\uff0c\u518d\u6b21\u56de\u590d\uff1a\u8f6c\u56de\u5de5\u5355\u7ec4\uff08\u6b64\u6b65\u9aa4\u662f\u8ba9\u667a\u9f7f\u5de5\u5355\u81ea\u52a8\u6d41\u8f6c\uff09<br  \/>4\u3001\u5de5\u5355\u7f3a\u9677\u6d41\u7a0b\uff1a\u5f85\u5904\u7406\u2794\u5de5\u5355\u5df2\u5b9a\u4f4d\u5f85\u89e3\u51b3 or \u5de5\u5355\u6570\u636e\u4fee\u590d\u2794\u5df2\u89e3\u51b3-\u2794\u5de5\u5355\u5df2\u9a8c\u8bc1\uff1b<br  \/>\t\u5de5\u5355\u5b9a\u4f4d\u540e\u6807\u8bb0\uff1a\u5de5\u5355\u5df2\u5b9a\u4f4d\u5f85\u89e3\u51b3 or \u5de5\u5355\u6570\u636e\u4fee\u590d\uff0c\u4fee\u590d\u540e\u6807\u8bb0\uff1a\u5df2\u89e3\u51b3<\/p><\/blockquote><\/div>","old_markdown_description":"#####\u3010**\u73af\u5883&\u8d26\u53f7\u5bc6\u7801**\u3011\uff1a\r\n>\u73af\u5883\uff1ahttps:\/\/crm.xiaoman.cn\r\n\u5ba2\u6237\u8d26\u53f7\uff1a \r\nclientID= \r\nuserID=\r\n#####\u3010**\u81ea\u6d4b\u60c5\u51b5**\u3011\uff1a\r\n>\u5ba2\u6237\u53f7\u6709\u6b64\u95ee\u9898\uff1b \u6d4b\u8bd5\u53f7\u81ea\u6d4b\u6b63\u5e38\uff0c\u672a\u590d\u73b0\r\n\r\n#####\u3010**\u590d\u73b0\u6b65\u9aa4\u53ca\u95ee\u9898\u73b0\u8c61**\u3011\uff1a\r\n>\u5f85\u63cf\u8ff0\r\n\r\n#####\u3010**\u9884\u671f\u7ed3\u679c**\u3011\uff1a\r\n>\u5f85\u63cf\u8ff0\r\n\r\n#####\u3010**\u590d\u73b0\u94fe\u63a5|\u6570\u636e|\u63a5\u53e3\u53c2\u6570**\u3011\uff1a\r\n>\u5f85\u7c98\u8d34\r\n\r\n#####\u3010**\u76f8\u5173\u622a\u56fe**\u3011\uff1a\r\n>\u65e0\r\n\r\n#####\u3010**\u9884\u671f\u4ee3\u7801\u63d0\u4ea4\u5206\u652f**\u3011\uff1a\r\n>1\u3001\u5efa\u8bae\u63d0\u4ea4\u81f3\uff1a\u6700\u8fd1bugfix\uff0c \u5e76\u5c06\u6b64\u7f3a\u9677\u79fb\u81f3\u76f8\u5e94\u5468bugfix\u91cc\u8ddf\u8fdb\uff1b\r\n 2\u3001\u5f00\u53d1\u7b2c\u4e00\u65f6\u95f4\u7ed9\u51fa\u539f\u56e0\u548c\u9884\u8ba1\u89e3\u51b3\u65f6\u95f4\u5728\u8bc4\u8bba\u4e2d\uff0c\u5e26\u201c\u667a\u9f7f\uff1a\u201d\u5b57\u773c\u4f1a\u81ea\u52a8\u540c\u6b65\u81f3\u667a\u9f7f\u5de5\u5355\u7cfb\u7edf\r\n    \u8bc4\u8bba\u683c\u5f0f\uff1a\r\n \t\u667a\u9f7f\uff1a\u662f\u5426bug\uff1a \uff0c\u539f\u56e0\uff1axxxxx\uff0c\u5f71\u54cd\u8303\u56f4\uff1axxxx\uff0c\u9884\u8ba1\u89e3\u51b3\u65b9\u6848\uff1axxx\uff0c\u9884\u8ba1\u4e0a\u7ebf\u65f6\u95f4\uff1axxx\r\n3\u3001\u5f00\u53d1\u5199\u597d2\u540e\uff0c\u518d\u6b21\u56de\u590d\uff1a\u8f6c\u56de\u5de5\u5355\u7ec4\uff08\u6b64\u6b65\u9aa4\u662f\u8ba9\u667a\u9f7f\u5de5\u5355\u81ea\u52a8\u6d41\u8f6c\uff09\r\n4\u3001\u5de5\u5355\u7f3a\u9677\u6d41\u7a0b\uff1a\u5f85\u5904\u7406\u2794\u5de5\u5355\u5df2\u5b9a\u4f4d\u5f85\u89e3\u51b3 or \u5de5\u5355\u6570\u636e\u4fee\u590d\u2794\u5df2\u89e3\u51b3-\u2794\u5de5\u5355\u5df2\u9a8c\u8bc1\uff1b\r\n\t\u5de5\u5355\u5b9a\u4f4d\u540e\u6807\u8bb0\uff1a\u5de5\u5355\u5df2\u5b9a\u4f4d\u5f85\u89e3\u51b3 or \u5de5\u5355\u6570\u636e\u4fee\u590d\uff0c\u4fee\u590d\u540e\u6807\u8bb0\uff1a\u5df2\u89e3\u51b3","old_description_type":"2","old_module":"\u6a21\u57571","old_milestone":"","old_reporter":"calvin.wy","old_deadline":"","old_created":"2024-11-27 16:22:27","old_resolved":"","old_closed":"","old_modified":"2024-11-28 13:35:49","old_lastmodify":"been","old_auditer":"","old_de":"","old_te":"","old_confirmer":"","old_current_owner":"calvin.wy;","old_participator":"","old_closer":"","old_status":"in_progress","old_resolution":"","old_priority":"medium","old_severity":"","old_platform":"","old_os":"","old_testmode":"\u5e38\u89c4\u529f\u80fd\u6d4b\u8bd5","old_testtype":"","old_testphase":"","old_source":"","old_frequency":"","old_cc":"","old_estimate":"","old_flows":"in_progress","old_version_report":"","old_version_test":"","old_version_fix":"","old_version_close":"","old_delayed":"0","old_custom_field_one":"\u5426","old_custom_field_two":"","old_custom_field_three":"","old_custom_field_four":"","old_custom_field_five":"20241113000125","old_regression_number":"0","old_issue_id":"","old_support_id":"","old_support_forum_id":"","old_created_from":"","old_baseline_find":"","old_baseline_join":"","old_baseline_close":"","old_ticket_id":"","old_story_id":"","old_baseline_test":"","old_originphase":"","old_sourcephase":"","old_bugtype":"","old_feature":"","old_in_progress_time":"2024-11-27 16:22:27","old_verify_time":"","old_reject_time":"","old_reopen_time":"","old_audit_time":"","old_suspend_time":"","old_assigned_time":"","old_iteration_id":"1147237250001001755","old_custom_field_6":"","old_custom_field_7":"2024-11-29 10:10","old_custom_field_8":"","old_custom_field_9":"","old_custom_field_10":"","old_custom_field_11":"","old_custom_field_12":"","old_custom_field_13":"\u670d\u52a1\u7aef","old_custom_field_14":"","old_custom_field_15":"","old_custom_field_16":"","old_custom_field_17":"","old_custom_field_18":"","old_custom_field_19":"","old_custom_field_20":"","old_custom_field_21":"","old_custom_field_22":"","old_custom_field_23":"","old_custom_field_24":"","old_custom_field_25":"","old_custom_field_26":"","old_custom_field_27":"","old_custom_field_28":"","old_custom_field_29":"","old_custom_field_30":"","old_custom_field_31":"","old_custom_field_32":"","old_custom_field_33":"","old_custom_field_34":"","old_custom_field_35":"","old_custom_field_36":"","old_custom_field_37":"","old_custom_field_38":"","old_custom_field_39":"","old_custom_field_40":"","old_custom_field_41":"","old_custom_field_42":"","old_custom_field_43":"","old_custom_field_44":"","old_custom_field_45":"","old_custom_field_46":"","old_custom_field_47":"","old_custom_field_48":"","old_custom_field_49":"","old_custom_field_50":"","old_custom_field_51":"","old_custom_field_52":"","old_custom_field_53":"","old_custom_field_54":"","old_custom_field_55":"","old_custom_field_56":"","old_custom_field_57":"","old_custom_field_58":"","old_custom_field_59":"","old_custom_field_60":"","old_custom_field_61":"","old_custom_field_62":"","old_custom_field_63":"","old_custom_field_64":"","old_custom_field_65":"","old_custom_field_66":"","old_custom_field_67":"","old_custom_field_68":"","old_custom_field_69":"","old_custom_field_70":"","old_custom_field_71":"","old_custom_field_72":"","old_custom_field_73":"","old_custom_field_74":"","old_custom_field_75":"","old_custom_field_76":"","old_custom_field_77":"","old_custom_field_78":"","old_custom_field_79":"","old_custom_field_80":"","old_custom_field_81":"","old_custom_field_82":"","old_custom_field_83":"","old_custom_field_84":"","old_custom_field_85":"","old_custom_field_86":"","old_custom_field_87":"","old_custom_field_88":"","old_custom_field_89":"","old_custom_field_90":"","old_custom_field_91":"","old_custom_field_92":"","old_custom_field_93":"","old_custom_field_94":"","old_custom_field_95":"","old_custom_field_96":"","old_custom_field_97":"","old_custom_field_98":"","old_custom_field_99":"","old_custom_field_100":"","old_custom_plan_field_1":"0","old_custom_plan_field_2":"0","old_custom_plan_field_3":"0","old_custom_plan_field_4":"0","old_custom_plan_field_5":"0","old_custom_plan_field_6":"0","old_custom_plan_field_7":"0","old_custom_plan_field_8":"0","old_custom_plan_field_9":"0","old_custom_plan_field_10":"0","old_template_id":"1147237250001004271","old_begin":"","old_due":"","old_release_id":"","old_fixer":"","old_follower":"","old_sync_type":"","old_label":"","old_effort":"","old_effort_completed":"0","old_exceed":"0","old_remain":"0","old_progress":"0","old_size":"","old_custom_field_101":"","old_custom_field_102":"","old_custom_field_103":"","old_custom_field_104":"","old_custom_field_105":"","old_custom_field_106":"","old_custom_field_107":"","old_custom_field_108":"","old_custom_field_109":"","old_custom_field_110":"","old_custom_field_111":"","old_custom_field_112":"","old_custom_field_113":"","old_custom_field_114":"","old_custom_field_115":"","old_custom_field_116":"","old_custom_field_117":"","old_custom_field_118":"","old_custom_field_119":"","old_custom_field_120":"","old_custom_field_121":"","old_custom_field_122":"","old_custom_field_123":"","old_custom_field_124":"","old_custom_field_125":"","old_custom_field_126":"","old_custom_field_127":"","old_custom_field_128":"","old_custom_field_129":"","old_custom_field_130":"","old_custom_field_131":"","old_custom_field_132":"","old_custom_field_133":"","old_custom_field_134":"","old_custom_field_135":"","old_custom_field_136":"","old_custom_field_137":"","old_custom_field_138":"","old_custom_field_139":"","old_custom_field_140":"","old_custom_field_141":"","old_custom_field_142":"","old_custom_field_143":"","old_custom_field_144":"","old_custom_field_145":"","old_custom_field_146":"","old_custom_field_147":"","old_custom_field_148":"","old _custom_field_149":"","old_custom_field_150":"","old_custom_field_151":"","old_custom_field_152":"","old_custom_field_153":"","old_custom_field_154":"","old_custom_field_155":"","old_custom_field_156":"","old_custom_field_157":"","old_custom_field_158":"","old_custom_field_159":"","old_custom_field_160":"","old_custom_field_161":"","old_custom_field_162":"","old_custom_field_163":"","old_custom_field_164":"","old_custom_field_165":"","old_custom_field_166":"","old_custom_field_167":"","old_custom_field_168":"","old_custom_field_169":"","old_custom_field_170":"","old_custom_field_171":"","old_custom_field_172":"","old_custom_field_173":"","old_custom_field_174":"","old_custom_field_175":"","old_custom_field_176":"","old_custom_field_177":"","old_custom_field_178":"","old_custom_field_179":"","old_custom_field_180":"","old_custom_field_181":"","old_custom_field_182":"","old_custom_field_183":"","old_custom_field_184":"","old_custom_field_185":"","old_custom_field_186":"","old_custom_field_187":"","old_custom_field_188":"","old_custom_field_189":"","old_custom_field_190":"","old_custom_field_191":"","old_custom_field_192":"","old_custom_field_193":"","old_custom_field_194":"","old_custom_field_195":"","old_custom_field_196":"","old_custom_field_197":"","old_custom_field_198":"","old_custom_field_199":"","old_custom_field_200":"","new_description":"<div data-inline-code-theme=\"red\" data-code-block-theme=\"default\"><\/div>","new_markdown_description":"","new_modified":"2024-11-28 13:48:36","change_fields":"description,markdown_description,modified","secret":"","rio_token":"","devproxy_host":"http:\/\/websocket-proxy","queue_id":"309041794","event_id":"179686139","created":"2024-11-28 13:48:36"}';

        $event = json_decode($event, true);
        $id = $event['id'];
        $workspaceId = $event['workspace_id'];

        // 获取tapd缺陷详细数据
        $tapdClient = TapdClient::getInstance();
        $result = $tapdClient->getBugs(['workspace_id' => $workspaceId, 'id' => $id]);

        $bugInfo = $result[0]['Bug'];

        $task = new SyncTapdSobotTask($event, $bugInfo);;
        $task->run();
    }

    public function testBugUpdateStatusSyncCommantToSobot()
    {
        $event = ['event' => "bug::update", 'id' => "11********001109105", 'change_fields' => "status,deadline", 'workspace_id' => "********"];

        // 获取tapd缺陷详细数据
        $tapdClient = TapdClient::getInstance();
        $bugInfo = $tapdClient->getBugs(['workspace_id' => ********, 'id' => 11********001109105])[0]['Bug'];
        //  bug已验证，评论智齿：工单已经验证通过，待上线
        $verified = array_merge($bugInfo , ['status' => TapdConstant::BUG_STATUS_VERIFIED]);
        $task = new SyncTapdSobotTask($event, $verified);
        $task->run();
        // bug已关闭，评论智齿：工单已上线
        $closed = array_merge($bugInfo , ['status' => TapdConstant::BUG_STATUS_CLOSE]);
        $task = new SyncTapdSobotTask($event, $closed);
        $task->run();
    }

    public function testBuildPackageTag()
    {

        $storyData =    [
            "event"=> "story::update",
            "event_from"=> "web",
            "workspace_id"=> "********",
            "package_tag"=> "HarmonyOS",
            "id"=> "1111111111111111111",
            "old_release_id"=> "",
            "new_release_id"=> "11********001000170",
            "change_fields"=> "release_id"
        ];
        $bugData = [
            "event"=> "bug::update",
            "event_from"=> "web",
            "workspace_id"=> "********",
            "package_tag"=> "Android",
            "id"=> "2222222222222222222",
            "old_release_id"=> "",
            "new_release_id"=> "11********001000170",
            "change_fields"=> "release_id"
        ];

        //上车
        $storyInfo = [
            'workspace_id' => "********",
            'release_id' => "11********001000170",
        ];
        $bugInfo = [
            'workspace_id' => "********",
            'release_id' => "11********001000170",
        ];

        $db = \Yii::app()->prometheus_db;

        $updateStory = 'update tbl_tapd_stories set package_tag = "HarmonyOS" , release_id = 11********001000170 where stories_id = 1111111111111111111';
        $db->createCommand($updateStory)->execute();
        $class = new  BulidPackageTag($storyData,$storyInfo);
        $class->run();
        $this->printPackageTag($storyData['new_release_id']);//初次计算预计值：HarmonyOS (现在执行预计值：HarmonyOS,Android)

        $updateBug = 'update tbl_tapd_ticket_bugs set package_tag = "Android" , release_id = 11********001000170 where bug_id = 2222222222222222222';
        $db->createCommand($updateBug)->execute();
        $class = new  BulidPackageTag($bugData,$bugInfo);
        $class->run();
        $this->printPackageTag($storyData['new_release_id']); //预计值：HarmonyOS,Android

        // story下车
        $storyInfo = [
            'workspace_id' => "********",
            'release_id' => "",
        ];
        $storyData['new_release_id'] = '';
        $storyData['old_release_id'] = 11********001000170;
        $updateStory = 'update tbl_tapd_stories set release_id = "" where stories_id = 1111111111111111111';
        $db->createCommand($updateStory)->execute();
        $class = new  BulidPackageTag($storyData,$storyInfo);
        $class->run();
        $this->printPackageTag($storyData['old_release_id']);//预计值：Android

        // bug车上变更tag
        $bugInfo = [
            'workspace_id' => "********",
            'release_id' => "11********001000170",
        ];
        $storyData['package_tag'] = 'iOS';
        $storyData['change_fields'] = 'package_tag';
        $updateStory = 'update tbl_tapd_ticket_bugs set package_tag = "iOS" where bug_id = 2222222222222222222';
        $db->createCommand($updateStory)->execute();
        $class = new  BulidPackageTag($storyData,$bugInfo);
        $class->run();
        $this->printPackageTag($bugInfo['release_id']);//预计值：iOS
    }

    public function printPackageTag($releaseId)
    {
        $tapdService = new TapdServiceRelationList();
        $tapdService->setFields('prometheus_iteration_id');
        $tapdService->setReferType(TapdServiceRelation::REFER_TYPE_PROMETHEUS_ITERATION);
        $tapdService->setTapdId($releaseId);
        $iterationId = $tapdService->findOne()['prometheus_iteration_id'] ?? [];
        $iteration = new IterationList();
        $iteration->setFields('package_tag');
        $iteration->setIterationIds([$iterationId]);
        var_dump($iteration->findOne()['package_tag']);
    }

}
