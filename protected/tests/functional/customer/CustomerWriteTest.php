<?php
/**
 * Created by Php<PERSON>torm.
 * User: cayley
 * Date: 2018/12/6
 * Time: 下午3:15
 */
namespace tests\functional\customer;

use common\library\customer\service\AccessService;
use User;

class CustomerWriteTest extends \WebFunctionalTestCase
{


	public function testHold() {

		$this->loginUser(11858712);

		$param = '
company_ids%5B0%5D=1178481279&company_ids%5B1%5D=1178481015&company_ids%5B2%5D=1178481070&company_ids%5B3%5D=1178481081&company_ids%5B4%5D=1178481257&company_ids%5B5%5D=1178481268&company_ids%5B6%5D=1178481092&company_ids%5B7%5D=1178481103&company_ids%5B8%5D=1178481114&company_ids%5B9%5D=1178481125&company_ids%5B10%5D=1178481136&company_ids%5B11%5D=1178481147&company_ids%5B12%5D=1178481158&company_ids%5B13%5D=1178481169&company_ids%5B14%5D=1178481180&company_ids%5B15%5D=1178481191&company_ids%5B16%5D=1178481202&company_ids%5B17%5D=**********&company_ids%5B18%5D=1*********&company_ids%5B19%5D=**********&group_id=-1&manageable=0&signature=TEVQcElNSlh0cE5rc1JvVUFJQWY0VDBFR0VIbDB3aEFEZXNsUFNISmFDL0x4dytlRE9tc0Z0T0RLak10bUFnejAwMFVrUm9uOUMrZytuRW03YzBOUVZEMk5WWVVLaEdC
		';

		$this->callAction('hold', $param);

	}

	public function testFTImport() {

		$this->loginAsYves00004();

		$param = [
			'file_ids' => [
				'customer_info'   => '**********',
				'customer_linker' => '**********',
				'Dict_CustType'   => '**********',
				'email_smtp'      => '**********'],

			'account_map' => [
				['ft' => '5', 'user_id' => '0'],
				['ft' => '3', 'user_id' => '********'],
				['ft' => '4', 'user_id' => '********'],
			],
		];

		$this->callAction('FTImport', $param);
//		$this->callAction('FTImport', 'file_ids=%7B%22Dict_CustType%22:%************%22,%22email_smtp%22:%************%22,%22customer_info%22:%************%22,%22customer_linker%22:%************%22%7D&account_map[]=%7B%22ft%22:%225%22,%22user_id%22:%22********%22%7D&account_map[]=%7B%22ft%22:%223%22,%22user_id%22:%**********%22%7D&account_map[]=%7B%22ft%22:%224%22,%22user_id%22:%**********%22%7D&account_map[]=%7B%22ft%22:%226%22,%22user_id%22:%**********%22%7D&account_map[]=%7B%22ft%22:%227%22,%22user_id%22:%**********%22%7D&account_map[]=%7B%22ft%22:%228%22,%22user_id%22:%**********%22%7D&account_map[]=%7B%22ft%22:%2210%22,%22user_id%22:%22********%22%7D&account_map[]=%7B%22ft%22:%2212%22,%22user_id%22:%**********%22%7D&account_map[]=%7B%22ft%22:%2214%22,%22user_id%22:%**********%22%7D&account_map[]=%7B%22ft%22:%2221%22,%22user_id%22:%**********%22%7D&account_map[]=%7B%22ft%22:%2215%22,%22user_id%22:%**********%22%7D&account_map[]=%7B%22ft%22:%2223%22,%22user_id%22:%**********%22%7D&account_map[]=%7B%22ft%22:%2231%22,%22user_id%22:%**********%22%7D&account_map[]=%7B%22ft%22:%2222%22,%22user_id%22:%22********%22%7D');
	}


    public function testSaveExtendInfo()
    {
        $this->callAction('saveExtendInfo', [
                'company_id' =>********,
                'data' => json_encode(['opportunity_auto'=>1])
            ]
        );
        $this->responseOk();
    }

    public function testFollowProduct()
    {
        $this->loginUser('<EMAIL>');
        $params = [
            'company_id' => '**********',
            'type' => 'sku',
            'ids' => [**********,'**********','**********'],
        ];

//        $params = [
//            'company_id' => '**********',
//            'type' => 'product',
//            'ids' => [**********,'**********','**********'],
//        ]
//
//        $params = [
//            'company_id' => '**********',
//            'type' => 'group',
//            'ids' => [**********,'**********','**********'],
//        ];
//
//        $params = [
//            'company_id' => '**********',
//            'type' => 'category',
//            'ids' => [20906,2],
//        ];
        $this->callAction('FollowProduct',$params);
        $this->responseOk();
    }

    public function testCancelFollowProduct()
    {
        $this->loginUser('<EMAIL>');
        $params = [
            'company_id' => '**********',
            'type' => 'sku',
            'id' => '**********',
        ];

//        $params = [
//            'company_id' => '**********',
//            'type' => 'product',
//            'id' => '**********',
//        ];

        $this->callAction('CancelFollowProduct',$params);
        $this->responseOk();
    }

    public function testShare()
    {
//        sevenshi-php.dev.xiaoman.cn/api/CustomerWrite/share?company_ids[]=1107108299&user_list[]=11858687&user_list[]=11858630
        $this->loginByEmail('<EMAIL>');
        $this->callAction('share', [
            'company_ids' => [
                1107108299
            ],
            'user_list' => [
                11858687,
                11858630
            ]
        ],'CustomerWrite'
        );
        $this->responseOk();
    }

    public function testTransfer()
    {
        $this->loginByEmail('<EMAIL>');

        $this->callAction('transfer', [
                'company_ids' => [
                    1107108299
                ],
                'user_list' => [
                    11858687,
                    11858630
                ]
            ]
        );
        $this->responseOk();
    }

    public function testRemoveProductGroupId()
    {
        $this->loginUser(46900);
        $params = [
            'product_group_id' => 3137211610,
            'delete_all_flag' => true,
        ];
        $this->callAction('RemoveProductGroupId',$params);
        $this->responseOk();
    }

    public function testAddProductGroupIds()
    {
        $this->loginUser(46900);
        $params = [
            'product_group_ids' => [
                38151470,
                1104306880
            ],
            'company_ids' => [
                3111996546,
                3112042419
            ],
        ];
        $this->callAction('AddProductGroupIds',$params);
        $this->responseOk();
    }

    /**
     *
     */
    public function testRemoveBlacklist()
    {
        $this->loginUser('46900');
        $req = [
            'ids' => ['=1) OR client_id=(14700']
        ];
        $this->callaction('RemoveBlacklist', $req );
        $this->responseOk();
    }
    /**
     *
     */
    public function testMarkRead()
    {
        $this->loginUser('46900');
        $req = [
            'company_id' => '(select max(company_id) from tbl_company)'
        ];
        $this->callaction('MarkRead', $req );
        $this->responseOk();
    }

    /**
     *
     */
    public function testMessageMarkRead()
    {
        $this->loginUser('46900');
        $req = [
//            'company_id' => '(select max(company_id) from tbl_company)'
            'company_id' => 1
        ];
        $this->callaction('MessageMarkRead', $req );
        $this->responseOk();
    }

    public function testEditTrail()
    {
        $this->loginUser(11864032);
        $params = [
            'trail_id' => '3118803599',
            'content' => '这是一条kevin创建的测试动态',
            'customer_id' => 0,
            'file_ids' => [
                3118803597
            ]
        ];
        $this->callAction('EditTrail',$params);
        $this->responseOk();
    }

	public function testUpdateCustomerContact(){
		$this->loginUser(11859131);
		$user = User::getLoginUser();
		$clientId = $user->getClientId();
		$sns_type = 'whatsapp';
		$customer_name = 'test';
		$sns_id = '86123456789201';
		$customer_id = 3560513581;
		if ($customer_id) {
			try {
				$customer = new \common\library\customer_v3\customer\orm\Customer($user->getClientId(), $customer_id);
				$company = new \common\library\customer_v3\company\orm\Company($user->getClientId(), $customer->company_id);
				$userIds = $company->user_id;
				$cardType = (new AccessService($user->getClientId(), $user->getUserId()))
					->setCompanyInfoData($user->getClientId(), $userIds, $company->pool_id ?? 0, $company->scope_user_ids)
					->cardType(true);
				$hasPermission = !in_array($cardType, [\common\library\email_identity\EmailIdentity::CARD_TYPE_CAN_NOT_MANAGE_COMPANY, \common\library\email_identity\EmailIdentity::CARD_TYPE_CAN_NOT_MANAGE_PUBLIC_COMPANY]);
				if ($hasPermission && ($sns_type == 'whatsapp')) {
					$customer->name = $customer_name ?: $customer->name;
					$customer->setSkipDuplicateCheck(false);
					$customer->setPoolId($company->pool_id);
					$contactList = is_array($customer->contact) ? $customer->contact : json_decode($customer->contact, true);
					$updateFlag = true;
					foreach ($contactList as $contact) {
						if (($sns_type == $contact['type'] && $contact['value'] == $sns_id) || empty($sns_id)) {
							$updateFlag = false;
							break;
						}
					}
					if ($updateFlag) {
						$contactList[] = ['type' => $sns_type, 'value' => $sns_id];
						$customer->contact = $contactList;
					}
					$customer->save();
				}

			} catch (\Exception $e) {
				LogUtil::warning("client_id = {$clientId}, customer = {$customer_id} " . $e->getMessage());
			}
		}

	}

    public function testCustomerRemark()
    {
        \User::setLoginUserByAccount('<EMAIL>');
        $params = 'company_id=**********&customer_id=**********&remark=test1';
        $rsp = $this->callAction('customerRemark', $params);
        $this->responseOk();
    }
}
