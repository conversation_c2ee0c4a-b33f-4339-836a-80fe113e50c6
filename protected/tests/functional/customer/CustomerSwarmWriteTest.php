<?php

namespace common\tests\functional\customer;

use common\library\field\Constant;
use common\library\setting\item\Api;
use common\library\setting\item\ItemSettingConstant;
use common\library\setting\library\swarm\Swarm;
use common\library\setting\library\swarm\SwarmApi;
use common\library\setting\library\swarm\SwarmMetadata;
use common\library\swarm\SwarmService;
use common\library\swarm\trigger\RuleTrigger;
use common\library\workflow\filter\WorkflowCriteriaBuilder;
use common\library\workflow\WorkflowConstant;
use Constants;

class  CustomerSwarmWriteTest extends \WebFunctionalTestCase {




	public function testCreateTemplate() {

//		$value = WorkflowCriteriaBuilder::getFieldValueName(self::$clientId, Constants::TYPE_COMPANY, 'archive_type', Constant::FIELD_TYPE_SELECT, 46900, 4, '', [static::class,
//		                                                                                                                                                 'getRuleFilterFieldsConfig']);

//		$name = WorkflowCriteriaBuilder::getFieldName(self::$clientId, 4, 'performance_order_count', [SwarmApi::class, 'getRuleFilterFieldsConfig']);
//

//		$api = Api::swarm(self::$clientId);
//
//		$api->initSwarmTemplate();
	}


	public function testCreateInit() {

		$this->loginUser(46900);

		$api = Api::swarm(self::$clientId);

		$api->createInit();

		$fieldList = $api->getFieldNameMap('trail_status');
		$fieldList = $api->getFieldNameMap('country');
	}

	public function testSetFieldList() {

		$this->loginUser(46900);

		$field = [
			[
				'field' => 'name',
				'fixed' => 1,
			],
			[
				'field' => 'tag',
			],
			[
				'field' => 'order_time',
				'order' => 'desc',

			],

		];

		$param = [

			'swarm_id' => 1738667780,
			'value'    => json_encode($field),
		];

		$param = [
			'swarm_ids' => [1,2,3],
			'value' => '[{"field":"name","fixed":1},{"field":"tag"},{"field":"customer.name"},{"field":"last_trail"},{"field":"country"},{"field":"ali_store_id"},{"field":"short_name"},{"field":"score"},{"field":"success_opportunity_count"},{"field":"duplicate_flag"}]',

		];

        $this->loginUser('<EMAIL>');
        $param = 'swarm_ids%5B0%5D=1&swarm_type=15&value=%5B%7B%22field%22%3A%22name%22%2C%22fixed%22%3A1%2C%22object_name%22%3A%22objCompany%22%7D%2C%7B%22field%22%3A%22name%22%2C%22object_name%22%3A%22objCustomer%22%7D%2C%7B%22field%22%3A%22order_time%22%2C%22object_name%22%3A%22objCompany%22%7D%2C%7B%22field%22%3A%22score%22%2C%22object_name%22%3A%22objCompany%22%7D%2C%7B%22field%22%3A%22timezone%22%2C%22object_name%22%3A%22objCompany%22%7D%5D';

		$this->callAction('SetFieldList', $param);

		$this->responseOk();
	}
    public function testEdit() {

        $this->loginClient(14100);
//        $this->loginUser(46900);
//        $this->loginAsRuby();
//		$data = Api::swarm(self::$clientId)->find(1566102576);
//		$data = Api::swarm(self::$clientId, self::$userId)->oneTree(1725541408);
//
        $param = [
            'dry_run' => 1,
//            'data' => '{"name":"numeric","type":0,"criteria_type":1,"criteria":"(1 AND 2 AND 3 AND 4 AND 5 AND 6 AND 7 AND 8)","filters":[{"field_no":"b79351c0","field":"performance_order_count","field_type":5,"operator":"=","value":1,"unit":"次","value_type":null,"refer_type":4,"name":"成交订单数","filter_no":1},{"field_no":"beda05fa","field":"performance_order_count","field_type":5,"operator":">=","value":1,"unit":"次","value_type":null,"refer_type":4,"name":"成交订单数","filter_no":2},{"field_no":"a8d2c77a","field":"performance_order_count","field_type":5,"operator":"range","value":[1,2],"unit":"次","value_type":null,"refer_type":4,"name":"成交订单数","filter_no":3},{"field_no":"7ee33df7","field":"country","field_type":3,"operator":"in","value":["CN","TW","HK","JP","KP","KR","MO","MN","BD","BT","IO","LK","IN","MV","NP","PK","BN","MM","KH","ID","LA","MY","PH","TL","SG","VN","TH","KZ","KG","TJ","TM","UZ","AF","AZ","BH","AM","CY","GE","PS","IR","IQ","IL","JO","KW","LB","OM","QA","SA","SY","AE","TR","YE"],"unit":"","value_type":null,"refer_type":4,"name":"国家地区","filter_no":4},{"field_no":"631b34da","field":"trail_status","field_type":3,"operator":"in","value":["1118019101"],"unit":"","value_type":null,"refer_type":4,"name":"客户阶段","filter_no":5},{"field_no":"3caa80b5","field":"performance_order_count","field_type":5,"operator":"range","value":[0.001,999999999999999],"unit":"次","value_type":null,"refer_type":4,"name":"成交订单数","filter_no":6},{"field_no":"64482447","field":"1100750875","field_type":5,"operator":"=","value":0.123456789999,"unit":"","value_type":null,"refer_type":4,"name":"公司自定义字段－数值","filter_no":7},{"field_no":"2c8b6208","field":"1100634517","field_type":7,"operator":"=","value":["金光瑶"],"unit":"","value_type":null,"refer_type":5,"name":"联系自定义字段－下拉多选","filter_no":8},{"field_no":"2656e0d2","field":"success_opportunity_amount_cny","field_type":5,"operator":"range","value":[0.123156468465,999999999999999],"unit":"","value_type":null,"refer_type":4,"name":"赢单商机金额(CNY)","filter_no":9}],"second_filter_type":1,"second_filters":[],"scope":null,"criteria_visual":null,"swarm_id":"3483724194","user_id":0,"iterate_field":null,"node":null}',
//            'data' => '{"name":"numeric","type":0,"criteria_type":1,"criteria":"(1 AND 2 AND 3 AND 4 AND 5 AND 6 AND 7 AND 8)","filters":[{"field_no":"b79351c0","field":"performance_order_count","field_type":5,"operator":"=","value":1,"unit":"次","value_type":null,"refer_type":4,"name":"成交订单数","filter_no":1},{"field_no":"beda05fa","field":"performance_order_count","field_type":5,"operator":">=","value":1,"unit":"次","value_type":null,"refer_type":4,"name":"成交订单数","filter_no":2},{"field_no":"a8d2c77a","field":"performance_order_count","field_type":5,"operator":"range","value":[1,2],"unit":"次","value_type":null,"refer_type":4,"name":"成交订单数","filter_no":3},{"field_no":"7ee33df7","field":"country","field_type":3,"operator":"in","value":["CN","TW","HK","JP","KP","KR","MO","MN","BD","BT","IO","LK","IN","MV","NP","PK","BN","MM","KH","ID","LA","MY","PH","TL","SG","VN","TH","KZ","KG","TJ","TM","UZ","AF","AZ","BH","AM","CY","GE","PS","IR","IQ","IL","JO","KW","LB","OM","QA","SA","SY","AE","TR","YE"],"unit":"","value_type":null,"refer_type":4,"name":"国家地区","filter_no":4},{"field_no":"631b34da","field":"trail_status","field_type":3,"operator":"in","value":["1118019101"],"unit":"","value_type":null,"refer_type":4,"name":"客户阶段","filter_no":5},{"field_no":"3caa80b5","field":"performance_order_count","field_type":5,"operator":"range","value":[0,2147483647],"unit":"次","value_type":null,"refer_type":4,"name":"成交订单数","filter_no":6},{"field_no":"64482447","field":"1100750875","field_type":5,"operator":"=","value":0.123456789999,"unit":"","value_type":null,"refer_type":4,"name":"公司自定义字段－数值","filter_no":7},{"field_no":"2c8b6208","field":"1100634517","field_type":7,"operator":"=","value":["金光瑶"],"unit":"","value_type":null,"refer_type":5,"name":"联系自定义字段－下拉多选","filter_no":8},{"field_no":"2656e0d2","field":"success_opportunity_amount_cny","field_type":5,"operator":"range","value":[0.123156468465,999999999999999],"unit":"","value_type":null,"refer_type":4,"name":"赢单商机金额(CNY)","filter_no":9}],"second_filter_type":1,"second_filters":[],"scope":null,"criteria_visual":null,"swarm_id":"3483724194","user_id":0,"iterate_field":null,"node":null}',
            
            'data' => '{"name":"date","type":0,"criteria_type":1,"criteria":"(1 AND 2 AND 3 AND 4 AND 5)","filters":[{"field_no":"3ccd7c95","field":"archive_time","field_type":10,"operator":"=","value":["2024-02-20","2024-02-20"],"unit":"","value_type":"range","refer_type":4,"name":"创建时间","filter_no":1},{"field_no":"611514b5","field":"archive_time","field_type":10,"operator":"<>","value":"today","unit":"","value_type":"dynamic_range","refer_type":4,"name":"创建时间","filter_no":2},{"field_no":"5ec8a44a","field":"archive_time","field_type":10,"operator":"earlier","value":-1,"unit":"天","value_type":"before_current","refer_type":4,"name":"创建时间","filter_no":3},{"field_no":"d3b79941","field":"archive_time","field_type":10,"operator":"later","value":"2024-02-22","unit":"","value_type":"specific","refer_type":4,"name":"创建时间","filter_no":4},{"field_no":"cd385b0b","field":"archive_time","field_type":10,"operator":"later","value":"edit_time","unit":"","value_type":"field","refer_type":4,"name":"创建时间","filter_no":5}],"second_filter_type":1,"second_filters":[],"scope":null,"criteria_visual":null,"swarm_id":"3483471992","user_id":0,"iterate_field":null,"node":null}',
            
            
            
            
        ];

        $this->callAction('edit', $param);
        $this->responseOk();
    }

    public function testCreate() {
//        $this->loginUser('46900');
//        $this->loginAsWeason3();
//
        $this->loginClient(14100);
        
        $param = [
//            'swarm_type' => ItemSettingConstant::ITEM_TYPE_SWARM,
            'swarm_type' => ItemSettingConstant::ITEM_TYPE_SWARM,
            'dry_run' => 0,
//            'data' => '{"name":"45345345","type":1,"criteria_type":1,"criteria":"","filters":[],"has_second_filter":true,"second_filter_type":1,"second_filter_field":"3264103474","second_filters":[],"user_id":46900,"iterate_module":"","iterate_field":"3264103474","node":[{"name":"L1+","iterate_value":"L1+"},{"name":"L3","iterate_value":"L3"},{"name":"L4","iterate_value":"L4"}]}',
//            'data' => '{"name":"ssssssssss","type":0,"criteria_type":1,"criteria":"","filters":[{"field_no":1,"field":"assess","field_type":5,"operator":"range","value":[0,6],"unit":"","value_type":null,"refer_type":4,"name":"评分","filter_no":1}],"has_second_filter":false,"second_filter_type":1,"second_filters":[],"user_id":0,"iterate_field":null,"node":null}',
//            'data' => '{"name":"ss4","type":0,"criteria_type":1,"criteria":"","filters":[{"field_no":2,"field":"assess","field_type":5,"operator":"range","value":[4,5],"unit":"","value_type":null,"refer_type":4,"name":"评分","filter_no":1}],"has_second_filter":false,"second_filter_type":1,"second_filters":[],"user_id":0,"iterate_field":null,"node":null}',
//            'data' => '{"name":"rrrrrrr","type":1,"criteria_type":1,"criteria":"","filters":[],"has_second_filter":true,"second_filter_type":1,"second_filter_field":"cus_tag","second_filters":[],"scope":null,"criteria_visual":null,"user_id":11864032,"iterate_module":"","iterate_field":"cus_tag","node":[{"name":"www2","iterate_value":"3427306775"},{"name":"wewfrefertret","iterate_value":"3427265045"},{"name":"77777","iterate_value":"3427127381"},{"name":"3333339999","iterate_value":"3427055035"},{"name":"33300000","iterate_value":"3427037633"},{"name":"我是weason2的个人标签","iterate_value":"3427037626"},{"name":"7","iterate_value":"3426277641"},{"name":"asdf99999","iterate_value":"3426274307"},{"name":"我看看","iterate_value":"3426264537"},{"name":"apple","iterate_value":"3426262647"},{"name":"666","iterate_value":"3426236760"},{"name":"123333","iterate_value":"3426222632"},{"name":"888","iterate_value":"3426221665"},{"name":"333","iterate_value":"3426214865"},{"name":"xxxxxxxx","iterate_value":"3426203407"},{"name":"个人标签test","iterate_value":"3425973313"},{"name":"nanrui-test","iterate_value":"3424285594"},{"name":"nanrui-1234","iterate_value":"3424272841"},{"name":"nanrui-test1","iterate_value":"3424268409"},{"name":"123nanrui","iterate_value":"3424261819"},{"name":"三","iterate_value":"3409441390"},{"name":"测试新建标签","iterate_value":"3409040942"},{"name":"敌我i从5","iterate_value":"3407110316"},{"name":"xindedwf","iterate_value":"3407109538"},{"name":"小黑哈哈lo","iterate_value":"3406945812"},{"name":"小子","iterate_value":"3406945826"},{"name":"小黑哈哈","iterate_value":"3406945804"},{"name":"www1","iterate_value":"3406945796"},{"name":"1221223","iterate_value":"3406945776"},{"name":"sdcs","iterate_value":"3406945772"},{"name":"lifu傻逼","iterate_value":"3406945767"},{"name":"小黑测试","iterate_value":"3406945762"},{"name":"测试测试","iterate_value":"3406945504"},{"name":"测试","iterate_value":"3406945498"},{"name":"ces","iterate_value":"3406945496"},{"name":"8888","iterate_value":"3406945475"},{"name":"让天然空调热哦","iterate_value":"3406309534"},{"name":"我今晚i大家微风","iterate_value":"3406309532"},{"name":"速度速度但是","iterate_value":"3405209549"},{"name":"来蹭饭的标签2334","iterate_value":"3405001758"},{"name":"哈哈哈哈哈哈哈","iterate_value":"3404975734"},{"name":"来蹭饭的标签3","iterate_value":"3404709143"},{"name":"我的个人标签啊哈哈哈","iterate_value":"3404683117"},{"name":"哈哈哈，商机标签1","iterate_value":"3402281429"},{"name":"哈哈哈哈大厦是的","iterate_value":"3401929689"},{"name":"哈哈哈商机新建","iterate_value":"3393734885"},{"name":"速度速度","iterate_value":"3392892381"},{"name":"2222","iterate_value":"3392509954"},{"name":"21312312312321","iterate_value":"3392509946"},{"name":"222233333","iterate_value":"3392509943"},{"name":"吃的是草的是","iterate_value":"3392342219"},{"name":"weason3专属","iterate_value":"3373241925"},{"name":"geren","iterate_value":"3300579063"},{"name":"热热饭","iterate_value":"3377545712"},{"name":"tests1","iterate_value":"3371906004"},{"name":"1","iterate_value":"3370326282"},{"name":"baidu1112","iterate_value":"3363805860"},{"name":"测试公司标签13112","iterate_value":"3180376225"},{"name":"我是一个小小小小小人小人小人小人小人","iterate_value":"3162790861"},{"name":"777","iterate_value":"3268035633"},{"name":"111测216","iterate_value":"3179928058"},{"name":"ceshi","iterate_value":"3162790639"},{"name":"测是hi hi hi44433","iterate_value":"3162358543"},{"name":"dqwdqdwq","iterate_value":"3162215121"},{"name":"uuu","iterate_value":"3160282674"},{"name":"rf","iterate_value":"3160282616"},{"name":"ty","iterate_value":"3160282600"},{"name":"uu","iterate_value":"3160281825"},{"name":"kxmms","iterate_value":"3160281816"},{"name":"wqqw","iterate_value":"3160281805"},{"name":"sdds","iterate_value":"3160281801"},{"name":"jjjj","iterate_value":"3160281797"},{"name":"jk","iterate_value":"3160281776"},{"name":"hhh","iterate_value":"3160281763"},{"name":"呵呵","iterate_value":"3160251301"},{"name":"测试121321","iterate_value":"3160251264"},{"name":"tets","iterate_value":"3160020890"},{"name":"22222","iterate_value":"3160020884"},{"name":"xinjian","iterate_value":"3159994016"},{"name":"小黑","iterate_value":"3159753010"},{"name":"test","iterate_value":"3159670569"},{"name":"公司标签test","iterate_value":"3145424912"},{"name":"个人标签","iterate_value":"3145406706"},{"name":"🌧️","iterate_value":"3145405252"},{"name":"嘻嘻","iterate_value":"3145405245"},{"name":"吼吼吼","iterate_value":"3145405236"},{"name":"hhha","iterate_value":"3145354556"},{"name":"xiao","iterate_value":"3145262928"},{"name":"建文我老弟","iterate_value":"3145262924"},{"name":"xiaohutyu","iterate_value":"3145262887"},{"name":"xiaoheiya","iterate_value":"3145262287"},{"name":"lala","iterate_value":"3145262250"},{"name":"是","iterate_value":"3145189105"},{"name":"我de","iterate_value":"3145136138"},{"name":"huwei dont give bug","iterate_value":"3137336387"},{"name":"建文叫我改bug","iterate_value":"3137335770"},{"name":"建文傻子","iterate_value":"3137335768"},{"name":"jianwenqusi","iterate_value":"3137335754"},{"name":"xiaohei87878","iterate_value":"3137335738"},{"name":"lalalal","iterate_value":"3137335071"},{"name":"test11111","iterate_value":"3137319603"},{"name":"死海","iterate_value":"3137318469"},{"name":"我问","iterate_value":"3137318457"},{"name":"我是在公海新建的第二个标签","iterate_value":"3137318422"},{"name":"公海新建个人标签","iterate_value":"3137318345"},{"name":"b","iterate_value":"3137314149"},{"name":"weas","iterate_value":"3137314145"},{"name":"weason","iterate_value":"3137314143"},{"name":"a","iterate_value":"3137314141"},{"name":"我是weason3的个人标签","iterate_value":"3137313786"},{"name":"2345678","iterate_value":"3137295843"},{"name":"测序","iterate_value":"3137295552"},{"name":"询盘未回复","iterate_value":"3136856118"},{"name":"个人标签2","iterate_value":"3136790824"},{"name":"112333","iterate_value":"3136790821"},{"name":"elma","iterate_value":"3136785775"},{"name":"小糊涂呀","iterate_value":"3136710968"},{"name":"aa","iterate_value":"3136692307"},{"name":"测是打算发时代风帆大厦","iterate_value":"3118925121"},{"name":"sqwefwefqfsdvcsdsdsdsdsdfsdsdfsdfdffddf","iterate_value":"3118894941"},{"name":"我 v","iterate_value":"3118858868"},{"name":"2221212","iterate_value":"3118759663"},{"name":"大厦大厦大厦大厦大厦大大厦大厦大厦","iterate_value":"3118759662"},{"name":"测飒飒大厦大厦大厦大厦大厦大厦大厦大厦大","iterate_value":"3118759658"},{"name":"ceshi标签按换行","iterate_value":"3118226885"},{"name":"测试很长的标签看看效果有什么问题","iterate_value":"3118226626"},{"name":"测试0701","iterate_value":"3117486038"},{"name":"30","iterate_value":"3114612942"},{"name":"44","iterate_value":"3114612988"}]}',
//            'data' => '{"name":"234234242","type":0,"criteria_type":1,"criteria":"","filters":[],"has_second_filter":true,"second_filter_type":1,"second_filter_field":"origin_list","second_filters":[],"scope":{},"criteria_visual":null,"user_id":0,"iterate_module":"","iterate_field":"origin_list","node":[{"name":"Facebook Lead表单","iterate_value":"15"},{"name":"系统推荐","iterate_value":"3"},{"name":"OKKI SHOPS","iterate_value":"16"},{"name":"潜客运营","iterate_value":"17"},{"name":"广告投放","iterate_value":"1100214039"},{"name":"互联网","iterate_value":"1100214037"},{"name":"海关数据","iterate_value":"1100214040"},{"name":"合作交换","iterate_value":"1100214041"},{"name":"熟人介绍","iterate_value":"1100214038"},{"name":"环球资源","iterate_value":"1100214043"},{"name":"其他","iterate_value":"1100214044"},{"name":"小满发现","iterate_value":"1"},{"name":"小满营销","iterate_value":"2"},{"name":"test-998","iterate_value":"3372255513"},{"name":"？？？？","iterate_value":"3373393122"},{"name":"阿里巴巴（B2B平台）","iterate_value":"4"},{"name":"来源测试","iterate_value":"3374305384"},{"name":"环球资源（B2B平台）","iterate_value":"5"},{"name":"i am stronger","iterate_value":"3374434078"},{"name":"环球市场（B2B平台）","iterate_value":"6"},{"name":"工作流来源（勿删除）","iterate_value":"3375392845"},{"name":"中国制造（B2B平台）","iterate_value":"7"},{"name":"小满助手","iterate_value":"8"},{"name":"审批流来源","iterate_value":"3375394651"},{"name":"官网询盘","iterate_value":"9"},{"name":"test9966","iterate_value":"3457339892"},{"name":"社交平台","iterate_value":"10"},{"name":"OKKI Leads","iterate_value":"14"},{"name":"123","iterate_value":"3457339910"},{"name":"电商平台","iterate_value":"11"},{"name":"FB公共主页","iterate_value":"12"},{"name":"ERP同步","iterate_value":"13"}]}',
            'data' => '{"user_id":11858673,"name":"阿里询盘测试客群","type":1,"criteria":"","criteria_type":1,"has_second_filter":false,"second_filter_type":1,"second_filters":[],"iterate_field":null,"node":null,"scene":"filter","filters":[{"field_no":"a3c8e9aa","field":"origin_list","field_type":"7","operator":"in","value":["406"],"unit":"","value_type":"","date_type":null,"refer_type":4,"name":"客户来源","filter_no":1}]}',




        ];

        $param['data'] = '{"user_id":11858673,"name":"阿里询盘测试客群","type":1,"criteria":"","criteria_type":1,"has_second_filter":false,"second_filter_type":1,"second_filters":[],"iterate_field":null,"node":null,"scene":"filter","filters":[{"object_name":"objCompany","field_no":"a3c8e9aa","field":"origin_list","field_type":"7","operator":"in","value":["406"],"unit":"","value_type":"","date_type":null,"refer_type":4,"name":"客户来源","filter_no":1}]}';

        $param['data'] = '{"user_id":11858673,"name":"阿里询盘测试客群","type":1,"criteria":"","criteria_type":1,"has_second_filter":false,"second_filter_type":1,"second_filters":[],"iterate_field":null,"node":null,"scene":"filter","filters":[{"object_name":"objCustomer","field_no":"a3c8e9aa","field":"suspected_invalid_email_flag","field_type":"13","operator":"=","value":1,"unit":"","value_type":"","date_type":null,"refer_type":5,"name":"是否疑似失效邮箱","filter_no":1}]}';


        $this->callAction('create', $param);

        $this->responseOk();


    }

	public function testDelete() {

//		$this->loginUser(46900);
//        $this->loginAsQiao();
        
        
        $this->loginClient(1);
//        $this->loginAsWeason3();
//        $clientId = static::$clientId;
		$param = [
			'swarm_id' => $itemId = 3406112983,
		];
        \ProjectActiveRecord::getDbByClientId(static::$clientId)->createCommand("update tbl_item_setting set enable_flag = 1 where item_id = {$itemId}")->execute();

		$this->callAction('delete', $param);

//        $count = \ProjectActiveRecord::getDbByClientId(static::$clientId)->createCommand("select count(1) from tbl_item_setting where enable_flag = 1 and parent_id = {$itemId} and client_id = {$clientId}")->queryScalar();
//        dd($count);


		$this->responseOk();
	}

	public function testSort() {

		$this->loginUser(46900);

//		$params = [
//
//			'swarm_ids' => [3,2,1],
//		];

		$params = 'swarm_ids%5B0%5D=1&swarm_ids%5B1%5D=1960754291&swarm_ids%5B2%5D=3&swarm_ids%5B3%5D=1971173263&swarm_ids%5B4%5D=2258769450&swarm_ids%5B5%5D=2258769547&swarm_ids%5B6%5D=2585566235&swarm_ids%5B7%5D=2914464793&swarm_ids%5B8%5D=2987728314&swarm_ids%5B9%5D=3100150158';

        $this->loginUser(46900);

		$this->callAction('sort', $params);

		$this->responseOk();
	}


	public function testDebugCreateSwarm() {


//		$this->loginAsRuby();
//        $this->loginAsQiao();
//        $param = [
//            'data' => '{"name":"联系人昵称","type":0,"criteria_type":1,"criteria":"()","filters":[{"field_no":0,"field":"name","field_type":1,"operator":"is_null","value":null,"unit":"","value_type":null,"refer_type":5,"name":"昵称","rule_type":1}],"second_filter_type":1,"second_filters":[],"iterate_field":"","iterate_value":"","swarm_id":"1887827766","user_id":0,"prefix":"0-","layer":"1"}',
//            'dry_run' => 1,
//        ];
//        $this->callAction('create', $param);
//        $this->responseOk();
//        return;

//        $this->loginUser(46900);
//        $this->enableProfile();
        $data = '{
    "name": "42个字段条件-不为空-公司",
    "type": 0,
    "criteria_type": 3,
    "criteria": "34",
    "filters": [
        {
            "field_no": 0,
            "field": "name",
            "field_type": 1,
            "operator": "not_null",
            "value": null,
            "unit": "",
            "value_type": null,
            "refer_type": 4,
            "name": "公司名称",
            "filter_no": 1
        },
        {
            "field_no": 1,
            "field": "short_name",
            "field_type": 1,
            "operator": "not_null",
            "value": null,
            "unit": "",
            "value_type": null,
            "refer_type": 4,
            "name": "简称",
            "filter_no": 2
        },
        {
            "field_no": 2,
            "field": "serial_id",
            "field_type": 1,
            "operator": "not_null",
            "value": null,
            "unit": "",
            "value_type": null,
            "refer_type": 4,
            "name": "客户编号",
            "filter_no": 3
        },
        {
            "field_no": 3,
            "field": "country",
            "field_type": 3,
            "operator": "not_null",
            "value": [],
            "unit": "",
            "value_type": null,
            "refer_type": 4,
            "name": "国家地区",
            "filter_no": 4
        },
        {
            "field_no": 4,
            "field": "timezone",
            "field_type": 3,
            "operator": "not_null",
            "value": [],
            "unit": "",
            "value_type": null,
            "refer_type": 4,
            "name": "时区",
            "filter_no": 5
        },
        {
            "field_no": 5,
            "field": "trail_status",
            "field_type": 3,
            "operator": "not_null",
            "value": [],
            "unit": "",
            "value_type": null,
            "refer_type": 4,
            "name": "客户阶段",
            "filter_no": 6
        },
        {
            "field_no": 6,
            "field": "origin",
            "field_type": 3,
            "operator": "not_null",
            "value": [],
            "unit": "",
            "value_type": null,
            "refer_type": 4,
            "name": "客户来源",
            "filter_no": 7
        },
        {
            "field_no": 7,
            "field": "group_id",
            "field_type": 3,
            "operator": "not_null",
            "value": [],
            "unit": "",
            "value_type": null,
            "refer_type": 4,
            "name": "分组",
            "filter_no": 8
        },
        {
            "field_no": 8,
            "field": "star",
            "field_type": 3,
            "operator": "not_null",
            "value": [],
            "unit": "",
            "value_type": null,
            "refer_type": 4,
            "name": "客户星级",
            "filter_no": 9
        },
        {
            "field_no": 9,
            "field": "biz_type",
            "field_type": 3,
            "operator": "not_null",
            "value": [],
            "unit": "",
            "value_type": null,
            "refer_type": 4,
            "name": "客户类型",
            "filter_no": 10
        },
        {
            "field_no": 10,
            "field": "intention_level",
            "field_type": 3,
            "operator": "not_null",
            "value": [],
            "unit": "",
            "value_type": null,
            "refer_type": 4,
            "name": "采购意向",
            "filter_no": 11
        },
        {
            "field_no": 11,
            "field": "annual_procurement",
            "field_type": 3,
            "operator": "not_null",
            "value": [],
            "unit": "",
            "value_type": null,
            "refer_type": 4,
            "name": "年采购额",
            "filter_no": 12
        },
        {
            "field_no": 12,
            "field": "scale_id",
            "field_type": 3,
            "operator": "not_null",
            "value": [],
            "unit": "",
            "value_type": null,
            "refer_type": 4,
            "name": "规模",
            "filter_no": 13
        },
        {
            "field_no": 13,
            "field": "scale_id",
            "field_type": 3,
            "operator": "not_null",
            "value": [],
            "unit": "",
            "value_type": null,
            "refer_type": 4,
            "name": "规模",
            "filter_no": 14
        },
        {
            "field_no": 14,
            "field": "homepage",
            "field_type": 1,
            "operator": "not_null",
            "value": null,
            "unit": "",
            "value_type": null,
            "refer_type": 4,
            "name": "公司网址",
            "filter_no": 15
        },
        {
            "field_no": 15,
            "field": "fax",
            "field_type": 1,
            "operator": "not_null",
            "value": null,
            "unit": "",
            "value_type": null,
            "refer_type": 4,
            "name": "传真",
            "filter_no": 16
        },
        {
            "field_no": 16,
            "field": "tel",
            "field_type": 1,
            "operator": "not_null",
            "value": null,
            "unit": "",
            "value_type": null,
            "refer_type": 4,
            "name": "座机",
            "filter_no": 17
        },
        {
            "field_no": 17,
            "field": "address",
            "field_type": 1,
            "operator": "not_null",
            "value": null,
            "unit": "",
            "value_type": null,
            "refer_type": 4,
            "name": "详细地址",
            "filter_no": 18
        },
        {
            "field_no": 18,
            "field": "remark",
            "field_type": 2,
            "operator": "not_null",
            "value": null,
            "unit": "",
            "value_type": null,
            "refer_type": 4,
            "name": "公司备注",
            "filter_no": 19
        },
        {
            "field_no": 19,
            "field": "pin_flag",
            "field_type": 13,
            "operator": "=",
            "value": "0",
            "unit": "",
            "value_type": null,
            "refer_type": 0,
            "name": "是否关注",
            "filter_no": 20
        },
        {
            "field_no": 20,
            "field": "performance_order_count",
            "field_type": 5,
            "operator": "not_null",
            "value": null,
            "unit": "",
            "value_type": null,
            "refer_type": 4,
            "name": "成交订单数",
            "filter_no": 21
        },
        {
            "field_no": 21,
            "field": "transaction_order_amount",
            "field_type": 5,
            "operator": "not_null",
            "value": null,
            "unit": "",
            "value_type": null,
            "refer_type": 4,
            "name": "成交订单金额",
            "filter_no": 22
        },
        {
            "field_no": 22,
            "field": "transaction_order_amount_avg",
            "field_type": 5,
            "operator": "not_null",
            "value": null,
            "unit": "",
            "value_type": null,
            "refer_type": 4,
            "name": "成交订单均价",
            "filter_no": 23
        },
        {
            "field_no": 23,
            "field": "success_opportunity_count",
            "field_type": 5,
            "operator": "not_null",
            "value": null,
            "unit": "",
            "value_type": null,
            "refer_type": 4,
            "name": "赢单商机数",
            "filter_no": 24
        },
        {
            "field_no": 24,
            "field": "ongoing_opportunity_count",
            "field_type": 5,
            "operator": "not_null",
            "value": null,
            "unit": "",
            "value_type": null,
            "refer_type": 4,
            "name": "进行中的商机数",
            "filter_no": 25
        },
        {
            "field_no": 25,
            "field": "success_opportunity_amount_cny",
            "field_type": 5,
            "operator": "not_null",
            "value": null,
            "unit": "",
            "value_type": null,
            "refer_type": 4,
            "name": "赢单商机金额(CNY)",
            "filter_no": 26
        },
        {
            "field_no": 26,
            "field": "success_opportunity_amount_usd",
            "field_type": 5,
            "operator": "not_null",
            "value": null,
            "unit": "",
            "value_type": null,
            "refer_type": 4,
            "name": "赢单商机金额(USD)",
            "filter_no": 27
        },
        {
            "field_no": 27,
            "field": "success_opportunity_amount_avg_cny",
            "field_type": 5,
            "operator": "not_null",
            "value": null,
            "unit": "",
            "value_type": null,
            "refer_type": 4,
            "name": "赢单商机均价(CNY)",
            "filter_no": 28
        },
        {
            "field_no": 28,
            "field": "success_opportunity_amount_avg_usd",
            "field_type": 5,
            "operator": "not_null",
            "value": null,
            "unit": "",
            "value_type": null,
            "refer_type": 4,
            "name": "赢单商机均价(USD)",
            "filter_no": 29
        },
        {
            "field_no": 29,
            "field": "user_id",
            "field_type": 7,
            "operator": "not_null",
            "value": [],
            "unit": "",
            "value_type": null,
            "refer_type": 4,
            "name": "跟进人",
            "filter_no": 30
        },
        {
            "field_no": 30,
            "field": "last_owner",
            "field_type": 3,
            "operator": "not_null",
            "value": [],
            "unit": "",
            "value_type": null,
            "refer_type": 4,
            "name": "原跟进人",
            "filter_no": 31
        },
        {
            "field_no": 31,
            "field": "create_user",
            "field_type": 3,
            "operator": "not_null",
            "value": [],
            "unit": "",
            "value_type": null,
            "refer_type": 4,
            "name": "创建人",
            "filter_no": 32
        },
        {
            "field_no": 32,
            "field": "last_edit_user",
            "field_type": 3,
            "operator": "not_null",
            "value": [],
            "unit": "",
            "value_type": null,
            "refer_type": 4,
            "name": "最近修改人",
            "filter_no": 33
        },
        {
            "field_no": 33,
            "field": "users",
            "field_type": 5,
            "operator": "range",
            "value": [1,2],
            "unit": "",
            "value_type": null,
            "refer_type": 4,
            "name": "跟进人数",
            "filter_no": 34
        },
        {
            "field_no": 34,
            "field": "release_count",
            "field_type": 5,
            "operator": "not_null",
            "value": null,
            "unit": "",
            "value_type": null,
            "refer_type": 4,
            "name": "进入公海次数",
            "filter_no": 35
        },
        {
            "field_no": 35,
            "field": "ali_store_id",
            "field_type": 7,
            "operator": "not_null",
            "value": [],
            "unit": "",
            "value_type": null,
            "refer_type": 4,
            "name": "国际站店铺",
            "filter_no": 36
        },
        {
            "field_no": 36,
            "field": "archive_type",
            "field_type": 3,
            "operator": "not_null",
            "value": [],
            "unit": "",
            "value_type": null,
            "refer_type": 4,
            "name": "创建方式",
            "filter_no": 37
        },
        {
            "field_no": 37,
            "field": "2607437917",
            "field_type": 2,
            "operator": "not_null",
            "value": null,
            "unit": "",
            "value_type": null,
            "refer_type": 4,
            "name": "comp002多行文本框TU吖",
            "filter_no": 38
        },
        {
            "field_no": 38,
            "field": "2607438067",
            "field_type": 7,
            "operator": "not_null",
            "value": [],
            "unit": "",
            "value_type": null,
            "refer_type": 4,
            "name": "comp004下拉多选TU吖",
            "filter_no": 39
        },
        {
            "field_no": 39,
            "field": "2607437990",
            "field_type": 3,
            "operator": "not_null",
            "value": [],
            "unit": "",
            "value_type": null,
            "refer_type": 4,
            "name": "comp003下拉单选TU吖",
            "filter_no": 40
        },
        {
            "field_no": 40,
            "field": "2607438161",
            "field_type": 5,
            "operator": "not_null",
            "value": null,
            "unit": "",
            "value_type": null,
            "refer_type": 4,
            "name": "comp006数值TU吖",
            "filter_no": 41
        },
        {
            "field_no": 41,
            "field": "2607437880",
            "field_type": 1,
            "operator": "not_null",
            "value": null,
            "unit": "",
            "value_type": null,
            "refer_type": 4,
            "name": "comp001单行文本框TU吖",
            "filter_no": 42
        }
    ],
    "has_second_filter": false,
    "second_filter_type": 1,
    "second_filters": [],
    "user_id": 0,
    "iterate_field": null,
    "node": null
}';
        $this->loginUser(765);
        $this->loginUser('<EMAIL>');
		$param = [
//			'data'    => '{"name":"hello","type":1,"criteria_type":1,"criteria":"","filters":[{"field_no":16,"field":"gender","field_type":3,"operator":"is_null","value":[],"unit":"","value_type":null,"date_type":null,"refer_type":5,"name":"性别","rule_type":2,"filter_no":1}],"has_second_filter":false,"second_filter_type":2,"second_filters":[{"iterate_field":"","iterate_value":"","criteria":"","criteria_type":1,"filters":[],"criteria_desc":"","order_rank":"1","display_flag":"","swarm_id":"1844145394","user_id":"11858712","name":"进入公海次数","create_user":"11858712","create_time":"2022-01-15 16:34:59","update_time":"2022-01-15 16:34:59","system_flag":"0","parent_id":"1830950838","prefix":"0-1830950838-","layer":"2","iterate_type":"0","template_id":"0","customer_count":509}],"iterate_field":"","iterate_value":"","swarm_id":"1830950838","user_id":11858712,"prefix":"0-","layer":"1","node":[{"iterate_field":"","iterate_value":"","criteria":"","criteria_type":1,"filters":[],"criteria_desc":"","order_rank":"1","display_flag":"","swarm_id":"1844145394","user_id":"11858712","name":"进入公海次数","create_user":"11858712","create_time":"2022-01-15 16:34:59","update_time":"2022-01-15 16:34:59","system_flag":"0","parent_id":"1830950838","prefix":"0-1830950838-","layer":"2","iterate_type":"0","template_id":"0","customer_count":509}]}',
            'data' => $data,
			'dry_run' => 1,
		];

		$this->callAction('create', $param);

		$this->responseOk();

	}

    public function testDebugCreateSwarmByCompare()
    {
        $this->loginAsQiao();
        $data = '{"name":"小乔今年创建的客户","type":0,"criteria_type":3,"criteria":"(1 and 2 and 3) or (1 and 2 and 4)","filters":[{"field_no":1,"field":"archive_time","field_type":10,"operator":"later","value":"2022-01-01","unit":"","value_type":"specific","refer_type":4,"name":"创建时间","rule_type":7,"filter_no":1},{"field_no":2,"field":"user_id","field_type":7,"operator":"in","value":["11858712"],"unit":"","value_type":null,"refer_type":4,"name":"跟进人","rule_type":4,"filter_no":2},{"field_no":3,"field":"trail_status","field_type":3,"operator":"in","value":["1101043657","1101043656"],"unit":"","value_type":null,"refer_type":4,"name":"客户阶段","rule_type":2,"filter_no":3},{"field_no":4,"field":"intention_level","field_type":3,"operator":"in","value":[0],"unit":"","value_type":null,"refer_type":4,"name":"采购意向","rule_type":2,"filter_no":4},{"field_no":2,"field":"last_owner","field_type":3,"operator":"not_null","value":[],"unit":"","value_type":null,"refer_type":4,"name":"原跟进人","rule_type":4,"filter_no":5},{"field_no":3,"field":"create_user","field_type":3,"operator":"not_null","value":[],"unit":"","value_type":null,"refer_type":4,"name":"创建人","rule_type":4,"filter_no":6},{"field_no":4,"field":"last_edit_user","field_type":3,"operator":"in","value":["11858712"],"unit":"","value_type":null,"refer_type":4,"name":"最近修改人","rule_type":4,"filter_no":7}],"has_second_filter":true,"second_filter_type":2,"second_filters":[{"iterate_field":"","iterate_value":"","criteria":"(1)","criteria_type":1,"filters":[{"field_no":2,"field":"1101101841","field_type":10,"operator":"<>","value":["2022-01-18","2022-01-21"],"unit":"","value_type":"range","date_type":1,"refer_type":4,"name":"自定义日期-时间","rule_type":7,"filter_no":1}],"criteria_desc":"「自定义日期-时间」不等于2022-01-18,2022-01-21之间","order_rank":"1","display_flag":"","swarm_id":"1887821500","user_id":"0","name":"二级客群","create_user":"11858712","create_time":"2022-01-18 14:03:32","update_time":"2022-01-18 14:03:32","system_flag":"0","parent_id":"1791115012","prefix":"0-1791115012-","layer":"2","iterate_type":"0","template_id":"0","customer_count":0}],"iterate_field":"","swarm_id":"1791115012","user_id":0,"prefix":"0-","layer":"1","node":[{"iterate_field":"","iterate_value":"","criteria":"(1)","criteria_type":1,"filters":[{"field_no":2,"field":"1101101841","field_type":10,"operator":"<>","value":["2022-01-18","2022-01-21"],"unit":"","value_type":"range","refer_type":4,"name":"自定义日期-时间","rule_type":7,"filter_no":1}],"criteria_desc":"「自定义日期-时间」不等于2022-01-18,2022-01-21之间","order_rank":"1","display_flag":"","swarm_id":"1887821500","user_id":"0","name":"二级客群","create_user":"11858712","create_time":"2022-01-18 14:03:32","update_time":"2022-01-18 14:03:32","system_flag":"0","parent_id":"1791115012","prefix":"0-1791115012-","layer":"2","iterate_type":"0","template_id":"0","customer_count":0}]}';
        $ruleInfo = json_decode($data, true);

        $trigger = new RuleTrigger(static::$clientId, $ruleInfo);
        $trigger->useCompareFilter();
        $res = $trigger->run($dryRun = 1, 2000);
        dd($res);
    }

    public function testDebugEditSwarm()
    {
//        $this->loginAsQiao();
//        $this->loginUser('<EMAIL>');
//        $this->loginClient(14119);
//        $companyIds = [33227713,1158022101,3112286902,3136791522,3159761496,3159954770,3160003754,3160407399,3160442271,3082109805,3082346325,3114978793,3117941792,3160658460,3160658480,3160892158,3162919468,3162984600,3163420374,3163621515,3163621725,3163730133,3161667114,3161927878,3162193106,3118225967,3118822547,3159613114,3159616057,3159616411,3159616420,3159616715,3159650063,3159921165,3159942235,3159970323,3159976007,3159979181,3159980721,3159992764,3160024859,3160024876,3160026877,3160033133,3160038455,3160039353,3160041008,3160041599,3160042072,3160042521,3160043068,3160043831,3160140128,3160144325,3160144470,3160146581,3160148650,3160376969,3160377506,3160381686,3160381949,3160382272,3160382533,3160383141,3160384093,3160385185,3160385471,3160391078,3160393352,3160393687,3160394004,3160394350,3160395709,3160396862,3160397597,3160400077,3160401094,3160401123,3160401511,3160401526,3160402043,3160402057,3160402574,3160402610,3160403171,3160403200,3160403723,3160403737,3160405185,*********7,3160405879,3160405885,3160406695,3160406705,3160407081,3160407123,3160411297,3160411336,3160412092,3160412099,3160412283,3160412290,3160413115,3160413121,3160413484,3160413522,3160413530,3160414675,3160414682,3160415042,3160415052,3160415065,3160415218,3160415226,3160415883,3160416127,3160416167,3160416558,3160416583,3160416941,3160416974,3160418443,3160420289,3160420296,3160420652,3160420663,3160421174,3160421180,3160421930,3160421945,3160422571,3160422578,3160423206,3160423233,3160423810,3160423816,3160424029,3160424065,3160425748,3160425763,3160426379,3160426393,3160426485,3160426522,3160428435,3160428469,3160429153,3160429180,3160429352,3160429388,3160430539,3160430545,3160431046,3160431066,3160431798,3160431849,3160432456,3160432482,3160433367,3160433383,3160434036,3160434060,3160434753,3160434772,3160435422,3160435437,3160435759,3160435781,3160436776,3160436800,3160436867,3160436897,3160438091,3160438097,3160440167,3160440182,3160441338,3160441351,3160442177,3160442194,3160442346,3160442377,3160443357,3160443363,3160444186,3160444226,3160444834,3160444858,3160445109,3160445127,3160446289,3160446298,3160447343,3160447351,3160447974,3160448002,3160449612,3160449615,3160449896,3160449928,3160450796,3160450819,3160451585,3160451594,3160452260,3160452272,3160452875,3160452896,3160453637,3160453657,3160454772,3160454782,3160455537,3160455564,3160456269,3160456295,3160457608,3160457617,3160459122,3160459145,3160459544,3160459548,3160460277,3160460283,3160462005,3160462013,3160462787,3160462803,3160463476,3160463491,3160464196,3160464204,3160465845,3160465850,3071490745,3113339479,3113728597,3113743286,3113786832,3114645343,3114645352,3114714712,3114750467,3115270202,3117372306,3117372317,3160466329,3160466335,3160467049,3160467068,3160467715,3160467730,3160467853,3160467863,3160468618,3160468624,3160468916,3160468935,3160470107,3160470119,3160470739,3160470750,3160471372,3160471382,3160473822,3160473840,3160474112,3160474117,3160474374,3160474918,3160475664,3160475925,3160475934,3160477829,3160477851,3160478531,3160478557,3160479060,3160479069,3160479491,3160479509,3160480350,3160480372,3160480482,3160480508,3160483209,3160484261,3160484268,3160484703,3160484711,3160484999,3160579234,3160644054,3160644840,3160658309,3160658379,3160658433,3160658449,3160675497,3160676045,3160676110,3160676889,3160681189,3160681190,3160681191,3160681195,3160681224,3160681226,3160681228,3160681229,3160681238,3160681261,3160681262,3160681264,3160681273,3160681274,3160681285,3160681289,3160681291,3160681292,3160681293,3160681294,3160681302,3160681717,3160681726,3160681728,3160681742,3160681743,3160681744,3160681746,3160681755,3160681756,3160681758,3160681775,3160681776,3160681777,3160681778,3160681779,3160681781,3160681783,3160681793,3160681796,3160681871,3160681879,3160681881,3160681899,3160681911,3160681912,3160681913,3160681914,3160681919,3160681926,3160681927,3160681938,3160681942,3160681954,3160681963,3160681965,3160681966,3160681969,3160681970,3160681994,3160682388,3160682403,3160682406,3160682409,3160682413,3160682417,3160682427,3160682429,3160682430,3160682432,3160682433,3160682436,3160682437,3160682449,3160682453,3160682454,3160682455,3160682456,3160682460,3160682724,3160682725,3160682730,3160682751,3160682761,3160682772,3160682792,3160682801,3160682802,3160682828,3160683416,3160683420,3160683423,3160683455,3160683464,3160683475,3160683479,3160683492,3160683515,3160683540,3160683987,3160683989,3160684003,3160684019,3160684020,3160684048,3160684059,3160684061,3160684075,3160684084,3160684751,3160684772,3160684804,3160684843,3160684849,3160684864,3160684878,3160684882,3160684886,3160684894,3160685505,3160685542,3160685556,3160685564,3160685567,3160685568,3160685588,3160685600,3160685624,3160685629,3160685711,3160685713,3160685734,3160685767,3160685775,3160685776,3160685796,3160685807,3160685830,3160685836,3160688178,3160688187,3160688259,3160688262,3160688266,3160688267,3160688293,3160688294,3160688296,3160688298,3160688312,3160688315,3160688316,3160688319,3160688325,3160688343,3160688381,3160688382,3160688384,3160688387,3160689243,3160689277,3160689291,3160689292,3160689317,3160689324,3160689339,3160689342,3160689346,3160689369,3160689378,3160689384,3160689396,3160689400,3160689408,3160689414,3160689417,3160689424,3160689428,3160689429,3160689434,3160689435,3160689443,3160705737,3160705741,3160705753,3160705756,3160705763,3160705768,3160705778,3160705794,3160705803,3160705818,3160706541,3160706547,3160706559,3160706562,3160706565,3160706568,3160706571,3160706574,3160706577,3160706580,3160706583,3160706586,3160706591,3160706592,3160706597,3160706599,3160706602,3160706605,3160706654,3160706659,3160707154,3160707179,3160707191,3160707192,3160707203,3160707207,3160707233,3160707273,3160707317,3160707333,3160707850,3160707859,3160707878,3160707901,3160707906,3160707907,3160707925,3160708005,3160708016,3160708044,3160708594,3160708604,3160708608,3160708612,3160708613,3160708616,3160708622,3160708626,3160708627,3160708631,3160708634,3160708643,3160708650,3160708659,3160708661,3160708667,3160708668,3160708676,3160708677,3160708680,3160709794,3160709797,3160709830,3160709843,3160709844,3160709848,3160710122,3160710151,3160710157,3160710218,3160710236,3160710262,3160710318,3160710327,3160710329,3160710409,3160710415,3160710425,3160710485,3160710502,3160710507,3160710526,3160710534,3160710550,3160710551,3160710560,3160710562,3160710569,3160710570,3160710580,3160710588,3160710606,3160710609,3160710616,3160711355,3160711362,3160711395,3160711396,3160711418,3160711435,3160711442,3160711449,3160711451,3160711478,3160712251,3160712259,3160712271,3160712294,3160712296,3160712329,3160712346,3160712362,3160712402,3160712404,3160713241,3160713245,3160713246,3160713267,3160713269,3160713279,3160713284,3160713288,3160713289,3160713304,3160713305,3160713306,3160713307,3160713315,3160713316,3160713317,3160713326,3160713327,3160713328,3160713489,3160713492,3160713520,3160713540,3160713545,3160713574,3160713578,3160713618,3160713637,3160713662,3160714379,3160714395,3160714397,3160714409,3160714411,3160714429,3160714430,3160714435,3160714440,3160714441,3160714448,3160714450,3160714455,3160714456,3160714462,3160714467,3160714468,3160714474,3160714477,3160714507,3160715214,3160715215,3160715222,3160715228,3160715248,3160715250,3160715251,3160715253,3160715254,3160715256,3160715261,3160715265,3160715278,3160715282,3160715283,3160715284,3160715286,3160715290,3160715291,3160715653,3160715662,3160715666,3160715669,3160715670,3160715678,3160715679,3160715685,3160715686,3160715687,3160715696,3160715698,3160715706,3160715708,3160715710,3160715719,3160715730,3160715734,3160715744,3160715753,3160717126,3160717164,3160717167,3160717170,3160717185,3160717187,3160717191,3160717208,3160717215,3160717224,3160721462,3160721485,3160721490,3160721496,3160721499,3160721522,3160721531,3160721538,3160721545,3160721546,3160721549,3160721551,3160721562,3160721583,3160721585,3160721591,3160721604,3160721605,3160721608,3160721759,3160721762,3160721765,3160721779,3160721780,3160721794,3160721795,3160721796,3160721799,3160721800,3160721802,3160721805,3160721813,3160721815,3160721816,3160721822,3160721824,3160721826,3160721829,3160722196,3160722201,3160722206,3160722214,3160722231,3160722269,3160722282,3160722314,3160722345,3160722370,3160722911,3160722921,3160722925,3160722935,3160722948,3160722949,3160722953,3160722961,3160722966,3160722969,3160722977,3160722981,3160722982,3160722986,3160722989,3160722990,3160722995,3160723003,3160723004,3160723316,3160723322,3160723323,3160723340,3160723341,3160723357,3160723365,3160723371,3160723374,3160723375,3160723376,3160723381,3160723385,3160723390,3160723400,3160723409,3160723410,3160723412,3160723413,3160723499,3160723514,3160723516,3160723517,3160723518,3160723530,3160723531,3160723536,3160723539,3160723544,3160723545,3160723546,3160723551,3160723556,3160723557,3160723558,3160723568,3160723569,3160723573,3160723689,3160724012,3160724016,3160724020,3160724024,3160724040,3160724047,3160724059,3160724060,3160724061,3160724072,3160724073,3160724074,3160724075,3160724079,3160724088,3160724090,3160724093,3160724102,3160724103,3160724663,3160724664,3160724668,3160724675,3160724681,3160724685,3160724686,3160724687,3160724688,3160724699,3160724702,3160724703,3160724705,3160724706,3160724720,3160724721,3160724725,3160724726,3160724727,3160724734,3160725343,3160725345,3160725373,3160725393,3160725399,3160725414,3160725445,3160725478,3160725482,3160725521,3160726100,3160726103,3160726114,3160726128,3160726131,3160726136,3160726143,3160726155,3160726157,3160726203,3160727050,3160727056,3160727091,3160727096,3160727107,3160727123,3160727139,3160727163,3160727191,3160727194,3160729673,3160729698,3160729723,3160729778,3160729784,3160729785,3160729802,3160729816,3160729854,3160729862,3160731300,3160731305,3160731356,3160731380,3160731387,3160731401,3160731427,3160731439,3160731454,3160731477,3160732294,3160732305,3160732314,3160732333,3160732335,3160732338,3160732353,3160732359,3160732388,3160732389,3160732887,3160732899,3160732906,3160732916,3160732923,3160732936,3160732944,3160732946,3160732947,3160732961,3160734065,3160734091,3160734092,3160734101,3160734102,3160734103,3160734104,3160734105,3160734115,3160736644,3160736677,3160736678,3160736696,3160736700,3160736714,3160736735,3160736736,3160736754,3160736787,3160737459,3160737460,3160737492,3160737567,3160737572,3160737576,3160737600,3160737621,3160737679,3160737700,3160737765,3160737786,3160737800,3160737835,3160737839,3160737855,3160737894,3160737899,3160737956,3160738041,3160837401,3160837402,3160837414,3160837425,3160837429,3160837434,3160837454,3160837463,3160837469,3160837473,3160838765,3160838772,3160838779,3160838787,3160838794,3160838804,3160838813,3160838820,3160838828,3160838833,3160840288,3160840291,3160840292,3160840297,3160840300,3160840307,3160840308,3160840309,3160840311,3160840317,3160840320,3160840323,3160840325,3160840331,3160840332,3160840334,3160840335,3160840340,3160840345,3160840587,3160840597,3160840620,3160840688,3160840723,3160840725,3160840755,3160840782,3160840803,3160840808,3160840820,3160840822,3160840849,3160840901,3160840903,3160840913,3160840916,3160840951,3160841112,3160841213,3160844980,3160844983,3160844987,3160844991,3160844999,3160845005,3160845009,3160845020,3160845025,3160845031,3160845834,3160845836,3160845840,3160845841,3160845845,3160845848,3160845851,3160845854,3160845857,3160845860,3160845863,3160845867,3160845869,3160845872,3160845875,3160845880,3160845883,3160845889,3160845891,3160847615,3160847620,3160847656,3160847684,3160847713,3160847722,3160847746,3160847792,3160847793,3160847830,3160849068,3160849077,3160849092,3160849134,3160849141,3160849161,3160849164,3160849179,3160849195,3160849222,3160849822,3160849827,3160849830,3160849832,3160849838,3160849842,3160849844,3160849845,3160849846,3160849848,3160849851,3160849855,3160849860,3160849866,3160849867,3160849869,3160849870,3160849872,3160849875,3160850059,3160850070,3160850072,3160850073,3160850075,3160850090,3160850091,3160850094,3160850097,3160850104,3160850117,3160850118,3160850119,3160850121,3160850129,3160850136,3160850142,3160850144,3160850145,3160850152,3160850456,3160850464,3160850488,3160850530,3160850546,3160850581,3160864991,3160864997,3160865003,3160865053,3160865689,3160865699,3160865717,3160865744,3160865763,3160865772,3160865773,3160865789,3160865794,3160865808,3160865955,3160865968,3160865977,3160866009,3160866031,3160866037,3160866067,3160866117,3160866133,3160866134,3160866961,3160866972,3160866995,3160867013,3160867027,3160867029,3160867050,3160867059,3160867065,3160867088,3160868002,3160868003,3160868005,3160868007,3160868018,3160868024,3160868025,3160868030,3160868033,3160868047,3160868048,3160868050,3160868051,3160868052,3160868064,3160868065,3160868067,3160868074,3160868075,3160868076,3160868519,3160868528,3160868539,3160868563,3160868579,3160868590,3160868605,3160868624,3160868625,3160868657,3160869326,3160869331,3160869335,3160869347,3160869351,3160869367,3160869369,3160869376,3160869385,3160869398,3160869593,3160869617,3160869638,3160869674,3160869701,3160869741,3160869774,3160869779,3160869833,3160869852,3160870680,3160870689,3160870690,3160870691,3160870703,3160870704,3160870712,3160870713,3160870717,3160870718,3160870720,3160870728,3160870729,3160870736,3160870740,3160870744,3160870745,3160870763,3160871346,3160871349,3160871356,3160871370,3160871381,3160871386,3160871390,3160871395,3160871399,3160871421,3160872112,3160872114,3160872138,3160872147,3160872159,3160872169,3160872192,3160872195,3160872205,3160872223,3160872880,3160872896,3160872905,3160872940,3160872950,3160872953,3160872982,3160872986,3160873011,3160873022,3160873669,3160873670,3160873680,3160873697,3160873699,3160873706,3160873730,3160873736,3160873743,3160873745,3160874555,3160874570,3160874603,3160874606,3160874612,3160874617,3160874627,3160874635,3160874657,3160874658,3160875324,3160875353,3160875393,3160875428,3160875429,3160875434,3160875435,3160875454,3160875469,3160875493,3160876137,3160876158,3160876164,3160876198,3160876226,3160876238,3160876254,3160876263,3160876264,3160876305,3160876900,3160876913,3160876935,3160876946,3160876955,3160876962,3160876970,3160876989,3160877005,3160877033,3160877285,3160877308,3160877329,3160877380,3160877402,3160877406,3160877407,3160877439,3160877461,3160877474,3160878335,3160878340,3160878342,3160878346,3160878348,3160878349,3160878353,3160878362,3160878365,3160878368,3160878371,3160878372,3160878380,3160878390,3160878391,3160878392,3160878396,3160878397,3160878799,3160878804,3160878816,3160878830,3160878841,3160878850,3160878851,3160878865,3160878907,3160878913,3160879798,3160879802,3160879833,3160879848,3160879856,3160879864,3160879867,3160879872,3160879888,3160879902,3160880504,3160880530,3160880531,3160880551,3160880552,3160880559,3160880575,3160880602,3160880612,3160880629,3160881409,3160881417,3160881447,3160881450,3160881457,3160881465,3160881469,3160881471,3160881478,3160881484,3160881485,3160881499,3160881500,3160881501,3160881503,3160881505,3160881515,3160881517,3160882028,3160882049,3160882066,3160882085,3160882088,3160882090,3160882095,3160882096,3160882106,3160882113,3160882119,3160882121,3160882122,3160882123,3160882124,3160882133,3160882143,3160882648,3160882662,3160882684,3160882730,3160882731,3160882732,3160882758,3160882762,3160882772,3160882812,3160883468,3160883477,3160883490,3160883525,3160883530,3160883531,3160883544,3160883568,3160883577,3160883598,3160884513,3160884521,3160884537,3160884559,3160884577,3160884578,3160884579,3160884588,3160884595,3160884604,3160884940,3160884944,3160884960,3160884961,3160884973,3160884986,3160884987,3160884997,3160885001,3160885017,3160886081,3160886083,3160886098,3160886109,3160886124,3160886135,3160886148,3160886155,3160886167,3160886168,3160886917,3160886923,3160886958,3160886993,3160886994,3160887019,3160887020,3160887038,3160887051,3160887083,3160887859,3160887874,3160887890,3160887903,3160887915,3160887917,3160887921,3160887937,3160887953,3160887958,3160888629,3160888630,3160888653,3160888710,3160888714,3160888736,3160888742,3160888754,3160888772,3160888773,3160889748,3160889761,3160889794,3160889878,3160889889,3160889900,3160889918,3160889932,3160889938,3160889966,3160895385,3160895386,3160895422,3160895458,3160895465,3160895474,3160895489,3160895499,3160895500,3160895506,3160896035,3160896039,3160896097,3160896124,3160896137,3160896155,3160896156,3160896157,3160896200,3160896203,3160896439,3160896467,3160896516,3160896525,3160896585,3160896590,3160896605,3160896639,3160896661,3160896696,3160900730,3160900733,3160900773,3160900778,3160900827,3160900831,3160900840,3160900859,3160900891,3160900895,3160901476,3160901488,3160901504,3160901505,3160901506,3160901507,3160901517,3160901532,3160901533,3160901535,3160901536,3160901544,3160901565,3160901570,3160901574,3160901575,3160901576,3160901593,3160901598,3160901609,3160903743,3160903758,3160903770,3160903790,3160903800,3160903802,3160903839,3160903840,3160903864,3160903865,3160905964,3160905971,3160905978,3160905985,3160905991,3160905995,3160905996,3160906003,3160906008,3160906016,3160906018,3160906021,3160906029,3160906032,3160906039,3160906040,3160906043,3160906045,3160906049,3160906057,3160907168,3160907176,3160907187,3160907203,3160907219,3160907234,3160907236,3160907272,3160907284,3160907325,3160908011,3160908014,3160908051,3160908059,3160908077,3160908081,3160908106,3160908116,3160908129,3160908153,3160908718,3160908732,3160908733,3160908742,3160908745,3160908747,3160908762,3160908763,3160908765,3160908768,3160908769,3160908781,3160908790,3160908794,3160908795,3160908797,3160908798,3160908802,3160908803,3160908829,3160924047,3160924056,3160924091,3160924113,3160924123,3160924125,3160924136,3160924140,3160924152,3160924170,3160924876,3160924881,3160924893,3160924895,3160924927,3160924940,3160924946,3160924972,3160925007,3160925037,3160925711,3160925721,3160925730,3160925735,3160925767,3160925768,3160925794,3160925804,3160925806,3160925812,3160926569,3160926579,3160926604,3160926628,3160926632,3160926645,3160926647,3160926655,3160926670,3160926673,3160927603,3160927621,3160927636,3160927639,3160927649,3160927670,3160927672,3160927694,3160927706,3160927715,3160928187,3160928199,3160928209,3160928211,3160928246,3160928247,3160928249,3160928271,3160928297,3160928299,3160929147,3160929152,3160929197,3160929218,3160929221,3160929232,3160929242,3160929243,3160929262,3160929269,3160930246,3160930252,3160930273,3160930274,3160930276,3160930298,3160930300,3160930305,3160930306,3160930308,3160930310,3160930323,3160930328,3160930332,3160930339,3160930340,3160930341,3160930355,3160931198,3160931200,3160931207,3160931208,3160931223,3160931224,3160931233,3160931246,3160931247,3160931273,3160931283,3160931288,3160931289,3160931296,3160931297,3160931299,3160931307,3160931309,3160931310,3160931345,3161360358,3161360359,3161360376,3161360377,3161360387,3161360393,3161360415,3161360419,3161360420,3161360423,3161360430,3161360441,3161360446,3161360447,3161360455,3161360456,3161360465,3161360470,3161360473,3161360475,3161361182,3161361202,3161361213,3161361241,3161361265,3161361278,3161361294,3161361297,3161361327,3161361332,3161362005,3161362221,3161362222,3161362243,3161362293,3161362294,3161362344,3161362349,3161362367,3161362368,3161362414,3161363018,3161363027,3161363053,3161363067,3161363079,3161363081,3161363092,3161363113,3161363120,3161363154,3161364150,3161364151,3161364185,3161364232,3161364249,3161364267,3161364291,3161364299,3161364321,3161364354,3161365393,3161365399,3161365438,3161365461,3161365507,3161365510,3161365519,3161365551,3161365583,3161365587,3161367040,3161367046,3161367050,3161367056,3161367063,3161367066,3161367070,3161367078,3161367082,3161367085,3161367087,3161367095,3161367098,3161367109,3161367110,3161367111,3161367113,3161367119,3161367121,3161367132,3161370223,3161370231,3161370248,3161370264,3161370271,3161370276,3161370284,3161370295,3161370314,3161370330,3161372883,3161372892,3161372920,3161372973,3161372992,3161373026,3161373038,3161373078,3161373128,3161373136,3161373776,3161373780,3161373804,3161373823,3161373824,3161373846,3161373865,3161373902,3161373903,3161373948,3161375608,3161375625,3161375646,3161375674,3161375689,3161375692,3161375705,3161375708,3161375721,3161375737,3161376653,3161376658,3161376677,3161376695,3161376704,3161376733,3161376742,3161376755,3161376765,3161376777,3161377758,3161377762,3161377775,3161377904,3161377908,3161377914,3161377928,3161377932,3161377934,3161377937,3161377939,3161377941,3161377943,3161377953,3161377955,3161377961,3161377969,3161377970,3161377975,3161377976,3161377989,3161377990,3161378666,3161378677,3161378691,3161378723,3161378734,3161378739,3161378749,3161378759,3161378768,3161378785,3161461982,3161461988,3161461996,3161462023,3161462033,3161462066,3161462067,3161462095,3161462105,3161462109,3161462707,3161462711,3161462751,3161462799,3161462820,3161462834,3161462860,3161462861,3161462892,3161462914,3161465913,3161465917,3161465925,3161465936,3161465943,3161465946,3161465961,3161465973,3161465989,3161466009,3161466220,3161466234,3161466251,3161466277,3161466289,3161466314,3161466330,3161466391,3161466406,3161466483,3161467617,3161467618,3161467626,3161467648,3161467652,3161467661,3161467698,3161467712,3161467714,3161467754,3161468731,3161468736,3161468779,3161468787,3161468789,3161468819,3161468825,3161468852,3161468856,3161468873,3161469399,3161469433,3161469474,3161469499,3161469507,3161469550,3161469554,3161469572,3161469604,3161469608,3161469610,3161469617,3161469670,3161469689,3161469693,3161469716,3161469731,3161469754,3161469769,3161469775,3161469785,3161469804,3161469807,3161471134,3161471141,3161471172,3161471187,3161471198,3161471212,3161471226,3161471229,3161471238,3161471249,3161471902,3161471904,3161471911,3161471914,3161471917,3161471924,3161471926,3161471928,3161471931,3161471936,3161471939,3161471940,3161471943,3161471947,3161471949,3161471953,3161471954,3161471961,3161471962,3161471968,3161472365,3161472463,3161472467,3161472472,3161472480,3161472483,3161472486,3161472487,3161472488,3161472496,3161472499,3161472501,3161472502,3161472503,3161472512,3161472515,3161472516,3161472518,3161472519,3161472546,3161472547,3161473102,3161473107,3161473130,3161473144,3161473160,3161473163,3161473181,3161473198,3161473205,3161473235,3161473845,3161473851,3161473861,3161473901,3161473934,3161473947,3161473962,3161473983,3161474000,3161474042,3161474877,3161474883,3161474884,3161474890,3161474896,3161474903,3161474905,3161474906,3161474907,3161474908,3161474911,3161474912,3161474923,3161474924,3161474927,3161474931,3161474932,3161474933,3161474934,3161475252,3161475264,3161475275,3161475297,3161475309,3161475315,3161475325,3161475343,3161475361,3161475368,3161475518,3161475546,3161475571,3161475605,3161475639,3161475650,3161475657,3161475683,3161475704,3161475764,3161476930,3161476931,3161476956,3161476972,3161476992,3161476993,3161477015,3161477023,3161477037,3161477057,3161477766,3161477770,3161477786,3161477806,3161477812,3161477818,3161477821,3161477832,3161477842,3161477854,3161478370,3161478375,3161478403,3161478440,3161478441,3161478480,3161478484,3161478504,3161478547,3161478556,3161480111,3161480115,3161480136,3161480159,3161480160,3161480183,3161480192,3161480207,3161480228,3161480253,3161480768,3161480780,3161480806,3161480831,3161480849,3161480850,3161480890,3161480898,3161480910,3161480948,3161483707,3161483710,3161483728,3161483729,3161483732,3161483733,3161483743,3161483747,3161483751,3161483760,3161483771,3161483780,3161483783,3161483790,3161483801,3161483802,3161483814,3161483817,3161483818,3161483825,3161484572,3161484586,3161484610,3161484638,3161484646,3161484663,3161484675,3161484695,3161484704,3161484722,3161485368,3161485375,3161485380,3161485390,3161485394,3161485401,3161485406,3161485418,3161485419,3161485442,3161487001,3161487008,3161487029,3161487072,3161487090,3161487101,3161487115,3161487140,3161487141,3161487189,3161488045,3161488046,3161488076,3161488096,3161488112,3161488142,3161488149,3161488151,3161488163,3161488182,3161488675,3161488680,3161488699,3161488737,3161488756,3161488766,3161488770,3161488790,3161488845,3161488846,3161489510,3161489512,3161489521,3161489532,3161489544,3161489550,3161489559,3161489571,3161489576,3161489608,3161490648,3161490661,3161490676,3161490693,3161490705,3161490722,3161490723,3161490728,3161490753,3161490766,3161491444,3161491445,3161491450,3161491457,3161491458,3161491473,3161491477,3161491481,3161491482,3161491484,3161491494,3161491496,3161491497,3161491500,3161491502,3161491510,3161491511,3161491515,3161491519,3161491521,3161493725,3161493732,3161493740,3161493741,3161493752,3161493755,3161493804,3161493807,3161493808,3161493816,3161493818,3161493824,3161493827,3161493830,3161493842,3161493845,3161493849,3161493854,3161493857,3161493860,3161494334,3161494336,3161494356,3161494506,3161494508,3161494512,3161494543,3161494604,3161494606,3161494612,3161533254,3161533278,3161534179,3161534192,3161534217,3161534229,3161534297,3161534933,3161534941,3161534973,3161563434,3161563442,3161563447,3161563460,3161563484,3161563498,3161563807,3161563811,3161563812,3161563826,3161563827,3161563833,3161563837,3161563839,3161563854,3161563855,3161563859,3161563860,3161563868,3161563871,3161579176,3161579179,3161579248,3161579819,3161579820,3161579828,3161579829,3161579840,3161579869,3161579879,3161579921,3161580097,3161580098,3161580099,3161580107,3161580119,3161580120,3161607352,3161607361,3161608241,3161608248,3161608254,3161608256,3161608404,3161609010,3161609015,3161609030,3161617279,3161617280,3161617286,3161617289,3161617290,3161617297,3161617317,3161617319,3161617322,3161617324,3161617326,3161617328,3161617330,3161617335,3161617341,3161617343,3161617345,3161617347,3161617348,3161619320,3161619322,3161619521,3161619525,3161619533,3161619539,3161619544,3161619577,3161619580,3161619586,3161620409,3161620418,3161620522,3161620532,3161620537,3161620542,3161620555,3161620660,3161620665,3161620674,3161621221,3161621222,3161621327,3161621331,3161621332,3161621346,3161621376,3161621479,3161621480,3161621496,3161621801,3161621802,3161621955,3161621960,3161621964,3161621985,3161622017,3161622089,3161622110,3161622123,3161623629,3161623635,3161623662,3161623684,3161623731,3161623732,3161623733,3161623736,3161623742,3161623749,3161623759,3161623778,3161623840,3161623846,3161623850,3161623855,3161623860,3161623862,3161623876,3161624235,3161624248,3161624325,3161624326,3161624332,3161624352,3161624380,3161624429,3161624453,3161624477,3161625244,3161625254,3161625311,3161625464,3161625480,3161625536,3161625680,3161625688,3161625729,3161625820,3161625856,3161625872,3161625903,3161625921,3161625937,3161625974,3161626006,3161626089,3161626129,3161626149,3161627120,3161627129,3161627135,3161627137,3161627156,3161627174,3161627203,3161627214,3161627219,3161627223,3161627232,3161627266,3161627301,3161627322,3161627355,3161627960,3161627990,3161628071,3161628073,3161628075,3161628076,3161628079,3161628081,3161628083,3161628085,3161628626,3161628656,3161628692,3161628693,3161628882,3161628954,3161628970,3161628972,3161628979,3161628980,3161628990,3161629001,3161629011,3161629048,3161629126,3161629127,3161629133,3161629207,3161629210,3161629214,3161629220,3161629234,3161629254,3161629274,3161629275,3161629324,3161629969,3161629977,3161630099,3161630114,3161630116,3161630129,3161630154,3161630182,3161630183,3161630203,3161631261,3161631265,3161631329,3161631333,3161631341,3161631345,3161631368,3161631435,3161631436,3161631458,3161632230,3161632234,3161632578,3161632596,3161632603,3161632633,3161632656,3161632664,3161632723,3161632784,3161632805,3161632819,3161632849,3161632857,3161632859,3161632860,3161632862,3161632865,3161632870,3161632918,3161632971,3161633039,3161633040,3161633044,3161633046,3161633058,3161633081,3161633382,3161633386,3161633387,3161633389,3161633390,3161633402,3161633413,3161633414,3161633416,3161633418,3161633419,3161633420,3161633421,3161633433,3161633435,3161633436,3161633438,3161633439,3161633443,3161633586,3161633587,3161633828,3161633838,3161633854,3161633856,3161633869,3161633873,3161633878,3161633882,3161633886,3161633948,3161633984,3161634017,3161634022,3161634031,3161634032,3161634033,3161634038,3161634043,3161634047,3161634052,3161634090,3161634092,3161634146,3161634147,3161634148,3161634150,3161634151,3161634163,3161634962,3161634967,3161635016,3161635022,3161635027,3161635032,3161635069,3161635104,3161635112,3161635145,3161650615,3161650620,3161650704,3161650715,3161650723,3161650724,3161650745,3161650773,3161650782,3161650812,3161651476,3161651485,3161651564,3161651571,3161651572,3161651577,3161651633,3161651641,3161651659,3161651666,3162692308,3162819356,3162856024,3162875565,3162919308,3163161248,3163161252,3163161256,3163161260,3163161264,3163161268,3163161272,3163161276,3163161280,3163161284,3163161288,3163161292,3163161296,3163161300,3163161304,3163161308,3163161312,3163161316,3163161320,3163161324,3163161328,3163161332,3163161336,3163161340,3163161344,3163161348,3163161352,3163161356,3163161360,3163415832,3163561393,3163670738,3163784267,3163898942,3163997789,3164027475,3161652240,3161652249,3161652254,3161652255,3161652257,3161652260,3161652294,3161652296,3161652301,3161652308,3161652320,3161652321,3161652323,3161652328,3161652330,3161652332,3161652334,3161652337,3161652490,3161652496,3161652497,3161652504,3161652538,3161652540,3161652544,3161652548,3161652551,3161652556,3161652566,3161652585,3161652587,3161652588,3161652594,3161652607,3161652608,3161652623,3161652638,3161652651,3161653176,3161653178,3161653186,3161653264,3161653276,3161653284,3161653326,3161653329,3161653344,3161653375,3161653989,3161653997,3161653999,3161654137,3161654140,3161654148,3161654178,3161654198,3161654222,3161654229,3161654502,3161654512,3161654528,3161654530,3161654534,3161654603,3161654635,3161654642,3161654651,3161654652,3161654659,3161654660,3161654668,3161654727,3161654772,3161654774,3161654776,3161654789,3161655007,3161655009,3161655158,3161655223,3161655224,3161655328,3161655357,3161655378,3161655392,3161655457,3161655535,3161655564,3161655574,3161655640,3161655641,3161655663,3161655734,3161655835,3161655857,3161655903,3161657854,3161657855,3161657907,3161657913,3161657923,3161657935,3161657962,3161657963,3161657989,3161658005,3161658837,3161658838,3161658840,3161658843,3161658871,3161658872,3161658888,3161658889,3161658895,3161658899,3161658913,3161658916,3161658934,3161658940,3161658956,3161658961,3161658971,3161658975,3161658989,3161659020,3161660133,3161660135,3161660141,3161660143,3161660146,3161660151,3161660154,3161660156,3161660157,3161660166,3161660168,3161660171,3161660174,3161660195,3161660209,3161660233,3161660234,3161660235,3161660248,3161660249,3161660696,3161660700,3161660703,3161660745,3161660762,3161660766,3161660815,3161660839,3161660859,3161660864,3161661409,3161661412,3161661446,3161661456,3161661469,3161661488,3161661497,3161661520,3161661521,3161661568,3161662108,3161662112,3161662187,3161662201,3161662208,3161662244,3161662270,3161662278,3161662328,3161662331,3161663418,3161663423,3161663498,3161663540,3161663553,3161663565,3161663574,3161663610,3161663631,3161663659,3161664371,3161664379,3161664434,3161664438,3161664462,3161664474,3161664497,3161664551,3161664588,3161664605,3161665406,3161665407,3161665424,3161665430,3161665433,3161665439,3161665449,3161665486,3161665491,3161665511,3161666162,3161666179,3161666211,3161666245,3161666254,3161666268,3161666282,3161666298,3161666311,3161666322,3161666901,3161666902,3161666909,3161666966,3161666992,3161666994,3161667050,3161667077,3161667093,3161667714,3161667719,3161667743,3161667791,3161667813,3161667828,3161667863,3161667889,3161667890,3161667927,3161701184,3161701186,3161701297,3161701362,3161701377,3161701389,3161701449,3161701450,3161701487,3161701533,3161701883,3161701908,3161702074,3161702123,3161702131,3161702145,3161702203,3161702301,3161702312,3161702393,3161762532,3161762546,3161762569,3161762580,3161762610,3161762640,3161762654,3161762677,3161762697,3161762740,3161762748,3161762749,3161762765,3161762807,3161762819,3161762846,3161762870,3161762873,3161766616,3161766635,3161766636,3161766639,3161766659,3161766667,3161766673,3161766676,3161766688,3161766691,3161766692,3161766714,3161766720,3161766723,3161766733,3161766735,3161766736,3161766749,3161766756,3161766765,3161767028,3161767033,3161767050,3161767067,3161767079,3161767091,3161767111,3161767135,3161767142,3161767161,3161767525,3161767533,3161767605,3161767625,3161767638,3161767670,3161767675,3161767692,3161767706,3161767719,3161768122,3161768123,3161768188,3161768218,3161768222,3161768243,3161768245,3161768255,3161768268,3161768316,3161768654,3161768662,3161768713,3161768718,3161768732,3161768743,3161768768,3161768774,3161768783,3161768790,3161769189,3161769197,3161769231,3161769255,3161769263,3161769276,3161769295,3161769310,3161769315,3161769339,3161769743,3161769746,3161769747,3161769748,3161769759,3161769778,3161769786,3161769788,3161769789,3161769790,3161769791,3161769798,3161769804,3161769825,3161769850,3161769851,3161769852,3161769857,3161769858,3161769865,3161770225,3161770228,3161770232,3161770233,3161770251,3161770275,3161770276,3161770277,3161770278,3161770279,3161770306,3161770308,3161770317,3161770320,3161770321,3161770322,3161770324,3161770337,3161770340,3161770588,3161770593,3161770595,3161770597,3161770610,3161770613,3161770632,3161770633,3161770637,3161770638,3161770641,3161770649,3161770654,3161770660,3161770677,3161770681,3161770682,3161770685,3161770707,3161770711,3161770846,3161770859,3161770861,3161770868,3161770898,3161770930,3161770965,3161770968,3161770977,3161770978,3161770983,3161770990,3161770998,3161771015,3161771018,3161771022,3161771023,3161771024,3161771031,3161771038,3161771291,3161771299,3161771357,3161771368,3161771370,3161771386,3161771400,3161771411,3161771412,3161771423,3161876871,1126690412,1128020458,1158021925,1634798945,1106165134,3163620308,3178573537,3159752745,32327372,32330186,32340751,32510313,31917124,1105011356,3159612795,35047975,36834810,3118225876,3118225999,3118226024,3118226030,3118226049,3118226061,3118226094,3118226154,3118226160,3118226177,3118226183,3118226194,3118226218,3118226240,3118446306,3119201167,3145376154,3159612548,3159612571,3159612602,3159612608,3159612614,3159612631,3159612656,3159612668,3159612687,3159612707,3159612719,3159612733,3159612745,3159612751,3159612764,3159612776,3159612785,3159612804,3159612813,3159612827,3159612836,3159612845,3159612854,3159612872,3159612891,3159612897,3159612903,3159612909,3159612915,3159612943,3159612957,3159612970,3159612976,3159612987,3159612993,3159612999,3159613005,3159613011,3159613018,3159613031,3159613038,3159613049,3159616319,3159616328,3159616334,3159616345,3159616358,3159616371,3159616402,3159616460,3159677908,3159921322,3159954257,3159992838,3160000200,3160119439,3160119500,3160161933,3160406565,3160437993,2954097314,3114650147,3117679909,3118119242,3118119244,3118119246,3118119248,3118119252,3118119254,3118119257,3118119259,3118119261,3118119265,3118119267,3118119270,3118119272,3118119274,3118119278,3118119280,3118119282,3118119284,3118119286,3118119288,3118119309,3118119311,3118119332,3118119353,3118119355,3118119372,3118119389,3118119410,3118119432,3118119439,3118119441,3118119463,3118119467,3118119471,3118119482,3118119504,3118119515,3118119518,3118119524,3118119526,3118119528,3118119530,3118119532,3118119534,3118119536,3118119538,3118119540,3118119542,3118119544,3118119546,3118119548,3118119550,3118119554,3118119556,3118119559,3118119561,3118119563,3118119565,3118119567,3118119569,3118119573,3118119575,3118119577,3118119579,3118119581,3118119587,3118119589,3118119591,3118119598,3118119602,3118119604,3118119609,3118119611,3118119613,3118119615,3118119617,3118119619,3118119622,3118119624,3118119626,3118119628,3118119630,3118119632,3118119634,3118119636,3118119638,3118119640,3118119643,3118119645,3118119647,3118119649,3118119651,3118119653,3118119657,3118119659,3118119661,3118119663,3118119665,3118119669,3118119671,3118119673,3118119675,3118119677,3118119680,3118119682,3118119684,3118119686,3118119688,3118119690,3118119692,3118119694,3118119696,3118119698,3118119700,3118119704,3118119706,3118119708,3118119711,3118119713,3118119715,3118119718,3118119720,3118119728,3118119730,3118119732,3118119734,3118119738,3118119750,3118119752,3118119754,3118119756,3118119762,3118119766,3118119772,3118119781,3118119783,3118119785,3118119792,3118119800,3118119810,3118119814,3118119828,3118119830,3118119832,3118119834,3118119840,3118119842,3118119855,3118119857,3118119861,3118119867,3118119869,3118119873,3118119875,3118119879,3118119881,3118119883,3118119885,3118119893,3118119897,3118119901,3118119903,3118119905,3118119907,3118119909,3118119911,3118119917,3118119919,3118119927,3118119931,3118119933,3118119935,3118119937,3118119943,3118119946,3118119948,3118119951,3118119953,3118119955,3118119958,3118119960,3118119964,3118119966,3118119968,3118119970,3118119972,3118119974,3118119978,3118119980,3118119982,3118119984,3118119986,3118119988,3118119990,3118119992,3118119994,3118119996,3118120000,3118120007,3118120010,3118120015,3118120018,3118120023,3118120025,3118120027,3118120029,3118120031,3118120033,3118120035,3118120041,3118120044,3118120047,3118120049,3118120056,3118120060,3118120066,3118120068,3118120070,3118120074,3118120076,3118120078,3118120080,3118120082,3118120086,3118120088,3118120090,3118120098,3118120100,3118120102,3118120104,3118120108,3118120112,3118120115,3118120117,3118120119,3118120121,3118120123,3118120125,3118120127,3118120129,3118120131,3118120133,3118120135,3118120137,3118120139,3118120143,3118120145,3118120147,3118120149,3118120157,3118120159,3118120161,3118120163,3118120165,3118120167,3118120169,3118120171,3118120173,3118120175,3118120177,3118120179,3118120181,3118120185,3118120187,3118120189,3118120191,3118120196,3118120198,3118120201,3118120224,3118120226,3118120228,3118120230,3118120232,3118120234,3118120236,3118120238,3118120240,3118120242,3118120244,3118120246,3118120248,3118120252,3118120254,3118120256,3118120258,3118120260,3118120262,3118120264,3118120268,3118120270,3118120272,3118120275,3118120279,3118120282,3118120290,3118120293,3118120295,3118120298,3118120302,3118120306,3118120312,3118120323,3118120327,3118120330,3118120335,3118120337,3118120343,3118120345,3118120347,3118120349,3118120351,3118120355,3118120357,3118120359,3118120361,3118120364,3118120366,3118120370,3118120372,3118120374,3118120378,3118120380,3118120382,3118120384,3118120389,3118120391,3118120398,3118120400,3118120402,3118120404,3118120406,3118120408,3118120410,3118120412,3118120415,3118120417,3118120421,3118120423,3118120425,3118120427,3118120429,3118120431,3118120433,3118120435,3118120437,3118120439,3118120442,3118120444,3118120446,3118120448,3118120452,3118120454,3118120456,3118120461,3118120464,3118120466,3118120468,3118120470,3118120472,3118120475,3118120477,3118120479,3118120481,3118120483,3118120486,3118120488,3118120490,3118120492,3118120494,3118120496,3118120498,3118120500,3118120502,3118120504,3118120506,3118120508,3118120510,3118120512,3118120514,3118120517,3118120519,3118120521,3118120523,3118120525,3118120527,3118120529,3118120531,3118120533,3118120535,3118120537,3118120539,3118120542,3118120544,3118120546,3118120548,3118120552,3118120554,3118120556,3118120558,3118120560,3118120562,3118120564,3118120566,3118120568,3118120574,3118120576,3118120578,3118120580,3118120582,3118120584,3118120586,3118120588,3118120590,3118120592,3118120594,3118120596,3118120598,3118120600,3118120602,3118120604,3118120606,3118120608,3118120610,3118120612,3118120614,3118120616,3118120618,3118120620,3118120622,3118120625,3118120627,3118120632,3118120634,3118120636,3118120638,3160580917,3160581388,3160582167,3160582782,3160582890,3160582942,3160583055,3160583274,3160583900,3160584317,3160587451,3160643209,3160649342,3160685388,3160735828,3160744827,3160744931,3160752929,3160757535,3160757541,3160757546,3160757551,3160760330,3160784227,3160785135,3160838712,3160838997,3160867233,3160867698,3160894553,3161344649,3161491376,3161491939,3162405371,3162514486,3162557578,3162585687,3162585802,3162645639,3162646075,3162646086,3162646103,3162646107,3162646118,3162646160,3162646166,3162646314,3162649044,3162741568,3162907691,3162919471,3162919498,3162942367,3162979027,3163020520,3163134553,3163157398,3163160029,3163759169,3163829051,3163829500,3163833426,3163836262,3163894053,3163894884,3161676959,3161688504,3161799737,3161919002,3161919005,3161924535,3161936094,3161936098,3161939359,3161940379,3161943932,3162220366,1126310652,1126350982,1126352687,1126352945,1126353058,1126807465,1132393154,1158021260,1158021272,1158021284,1158021296,1158021308,1158021495,1158021575,1158021609,1158021703,1158021715,1158021763,1158021775,1158021787,1158021829,1158021853,1158021865,1158021889,1158021913,1158021961,1158021973,1158021985,1158021997,1158022009,1158022137,1158022161,1158022173,1158022298,1161939820,1777958538,1110105155,1110105168,1115757806,1115757819,1115758106,1115758136,1115758238,40454129,1100400916,1100742572,38440418,3160867100,3162646058,1126354476,3160930689,3160383072,3162697842,21794443,32332206,1158022263,3118225984,3118226006,3118226012,3118226166,3118226200,3118226381,3159612701,3159612930,3159616364,3159616380,3159616510,3159616615,3159616674,3159616760,3159616777,3160030114,3160030171,3160030185,3160030205,3160059806,3160062583,3160377033,3160377043,3160377047,3160377059,3160377063,3160377072,3160377078,3160377196,3160377204,3160377215,3160377251,3160377253,3160377254,3160377255,3160377325,3160377328,3160377340,3160377349,3160377360,3160377361,3160377373,3160377489,3160377490,3160377502,3160380314,3160380325,3160380336,3160380400,3160380401,3160380413,3160381677,3160381678,3160381679,3160381680,3160381740,3160381753,3160381758,3160381765,3160381772,3160381786,3160381798,3160381909,3160381911,3160381914,3160381954,3160381955,3160381959,3160381960,3160382032,3160382037,3160382074,3160382079,3160382089,3160382146,3160382209,3160382212,3160382225,3160382263,3160382264,3160382265,3160382266,3160382332,3160382344,3160382347,3160382357,3160382368,3160382369,3160382380,3160382503,3160382506,3160382517,3160382568,3160382569,3160382570,3160382572,3160382637,3160382640,3160382643,3160382658,3160382662,3160382670,3160382679,3160382790,3160382797,3160382811,3160383105,3160383107,3160383133,3160383359,3160383372,3160383381,3160383393,3160383406,3160383416,3160383427,3160383600,3160383616,3160383628,3160384239,3160384242,3160384257,3160384262,3160384288,3160384291,3160384293,3160384295,3160384301,3160384308,3160384320,3160384453,3160384454,3160384469,3160385134,3160385139,3160385140,3160385141,3160385238,3160385249,3160385253,3160385263,3160385279,3160385286,3160385298,3160385408,3160385419,3160385422,3160385544,3160385549,3160385556,3160385561,3160385641,3160385645,3160385657,3160385667,3160385674,3160385683,3160385689,3160385794,3160385802,3160391066,3160391067,3160391068,3160391134,3160391150,3160391151,3160391159,3160391171,3160391175,3160391184,3160391302,3160391316,3160391319,3160392775,3160393332,3160393339,3160393340,3160393341,3160393421,3160393429,3160393440,3160393454,3160393462,3160393463,3160393477,3160393608,3160393610,3160393617,3160393673,3160393674,3160393676,3160393677,3160393765,3160393774,3160393775,3160393790,3160393800,3160393815,3160393825,3160393932,3160393943,3160393946,3160394027,3160394028,3160394029,3160394034,3160394083,3160394095,3160394096,3160394108,3160394119,3160394127,3160394136,3160394277,3160394281,3160394284,3160394310,3160394314,3160394315,3160394316,3160394409,3160394414,3160394422,3160394430,3160394441,3160394442,3160394454,3160394567,3160394574,3160394577,3160395515,3160395568,3160395569,3160395570,3160395571,3160395644,3160395661,3160395663,3160395669,3160395683,3160395692,3160395710,3160395826,3160395832,3160395843,3160396001,3160396636,3160397043,3160397300,3160397865,3160400167,3160400188,3160400204,3160400352,3160400353,3160400363,3160400370,3160400389,3160400397,3160400408,3160401018,3160401051,3160401193,3160401215,3160401225,3160401346,3160401383,3160401405,3160401418,3160401424,3160401438,3160401448,3160401562,3160401565,3160401644,3160401677,3160401685,3160401820,3160401834,3160401858,3160401866,3160401877,3160401907,3160401920,3160401990,3160401991,3160402151,3160402180,3160402194,3160402358,3160402390,3160402398,3160402410,3160402429,3160402437,3160402450,3160402515,3160402519,3160402674,3160402698,3160402708,3160402835,3160402849,3160402877,3160402893,3160402904,3160402931,3160402945,3160403105,3160403113,3160403161,3160403312,3160403323,3160403337,3160403522,3160403557,3160403563,3160403572,3160403587,3160403598,3160403620,3160403626,3160403641,3160403765,3160403768,3160403856,3160403881,3160403893,3160404020,3160404058,3160404073,3160404083,3160404092,3160404107,3160404118,3160404139,3160404158,3160404246,3160404986,3160405027,3160405055,3160405765,3160405821,3160406221,3160406225,3160406278,3160406667,3160406670,3160406673,3160406675,3160406772,3160406777,3160406786,3160406801,3160406807,3160406810,3160406817,3160406928,3160406944,3160406947,3160407863,3160411259,3160411292,3160411456,3160411649,3160411838,3160411994,3160411998,3160411999,3160412000,3160412133,3160412154,3160412473,3160412664,3160412679,3160413218,3160413229,3160413244,3160413358,3160413366,3160413378,3160413380,3160413405,3160413411,3160413412,3160413458,3160413469,3160413470,3160413471,3160413675,3160413694,3160413709,3160413721,3160413727,3160413738,3160413742,3160413833,3160413839,3160413840,3160414705,3160414706,3160414710,3160414711,3160414759,3160414765,3160414768,3160414800,3160414805,3160414911,3160414919,3160414939,3160414942,3160414946,3160415612,3160415678,3160416149,3160416263,3160416277,3160416287,3160416385,3160416406,3160416417,3160416424,3160416442,3160416452,3160416463,3160416512,3160416514,3160416517,3160416519,3160416662,3160416677,3160416679,3160416684,3160416698,3160416708,3160416717,3160416823,3160416831,3160416842,3160417343,3160417463,3160418544,3160418594,3160418651,3160420252,3160420256,3160420260,3160420261,3160420364,3160420365,3160420377,3160420389,3160420401,3160420402,3160420409,3160420541,3160420548,3160420558,3160420614,3160420615,3160420628,3160420629,3160420747,3160420760,3160420782,3160420797,3160420831,3160420846,3160420855,3160420920,3160420923,3160420926,3160421118,3160421606,3160421855,3160422033,3160422047,3160422242,3160422279,3160422516,3160422517,3160422701,3160422896,3160422997,3160423395,3160423637,3160423663,3160423733,3160425843,3160425859,3160425955,3160426850,3160427032,3160427199,3160427221,3160429314,3160429316,3160429349,3160429502,3160430055,3160430637,3160430656,3160430664,3160430665,3160430675,3160430684,3160430700,3160430816,3160430824,3160430835,3160431607,3160432071,3160432110,3160432505,3160432521,3160432604,3160433270,3160434164,3160434282,3160434632,3160434850,3160435111,3160436318,3160436459,3160436704,3160436706,3160437241,3160437350,3160437362,3160437388,3160438239,3160438614,3160442260,3160442633,3160442636,3160442664,3160442675,3160442688,3160442700,3160442723,3160442972,3160442977,3160443033,3160444431,3160445061,3160445073,3160445077,3160445081,3160445471,3160445476,3160445491,3160445514,3160445522,3160445528,3160445548,3160445594,3160445725,3160445784,3160445788,3160445801,3160446424,3160447435,3160448372,3160449024,3160449040,3160449259,3160450703,3160451913,3160452560,3160452759,3160453049,3160453932,3160453938,3160454048,3160454115,3160454557,3160454559,3160454891,3160455022,3160455114,3160455117,3160455159,3160455170,3160455266,3160455278,3160455317,3160455318,3160455370,3160455377,3160455850,3160456478,3160456547,3160456636,3160456656,3160456673,3160456712,3160456728,3160456731,3160457940,3160459599,3160459602,3160459603,3160459605,3160459722,3160459740,3160459742,3160459778,3160459799,3160459824,3160459856,3160459969,3160460037,3160460047,3160460052,3160460193,3160460194,3160460196,3160460197,3160460341,3160460350,3160460358,3160460365,3160460381,3160460384,3160460399,3160460518,3160460528,3160460537,3160462448,3160463051,3160463126,3160463611,3160463622,3160465054,3160465071,3160465315,3160465828,3160465829,3160465830,3160465838,3160465937,3160465938,3160465945,3160465957,3160465968,3160465972,3071499186,3100150414,3114319245,3114319456,3115290899,3117688080,3160465984,3160466133,3160466142,3160466153,3160466515,3160468692,3160468693,3160468703,3160468704,3160468713,3160468716,3160468726,3160468855,3160468860,3160468866,3160468978,3160468982,3160468988,3160468989,3160469058,3160469068,3160469077,3160469091,3160469099,3160469105,3160469108,3160469230,3160469240,3160469251,3160469374,3160470212,3160470841,3160470995,3160471148,3160471225,3160471413,3160471527,3160471529,3160471530,3160471543,3160471614,3160471627,3160471630,3160471640,3160471652,3160471656,3160471668,3160471800,3160471832,3160473371,3160473729,3160474076,3160474077,3160474100,3160474101,3160474472,3160474473,3160474487,3160474495,3160474507,3160474519,3160474526,3160474650,3160474660,3160474669,3160475345,3160476004,3160477914,3160477928,3160477938,3160477946,3160477950,3160477952,3160477966,3160478084,3160478087,3160478095,3160478157,3160478176,3160478178,3160478189,3160478253,3160478256,3160478262,3160478275,3160478285,3160478287,3160478307,3160478424,3160478432,3160478441,3160478615,3160478621,3160478847,3160478856,3160479143,3160479160,3160479162,3160479170,3160479184,3160479187,3160479201,3160479319,3160479324,3160479330,3160479776,3160479815,3160479827,3160479847,3160479858,3160479861,3160480306,3160480307,3160480309,3160480325,3160480457,3160480463,3160480488,3160483264,3160484165,3160484168,3160484172,3160484184,3160484340,3160484347,3160484350,3160484361,3160484366,3160484369,3160484397,3160484494,3160484495,3160484505,3160484628,3160502185,3160551757,3160587646,3160656964,3160656966,3160659072,3160659075,3160660112,3160660116,3160661049,3160661050,3160661388,3160661396,3160662216,3160662222,3160663108,3160663111,3160664471,3160664475,3160664543,3160664547,3160667949,3160667950,3160668226,3160668235,3160669010,3160669014,3160670141,3160670144,3160670523,3160670526,3160671413,3160671416,3160671658,3160671661,3160671789,3160671798,3160672329,3160672341,3160672911,3160672914,3160673535,3160673537,3160674242,3160674245,3160674693,3160674694,3160675490,3160675493,3160675621,3160675764,3160676211,3160676222,3160676236,3160676520,3160676537,3160676609,3160676637,3160676656,3160676928,3160676933,3160677187,3160677326,3160681187,3160681192,3160681415,3160681416,3160681426,3160681435,3160681449,3160681459,3160681469,3160681585,3160681590,3160681599,3160682056,3160682060,3160682125,3160682126,3160682132,3160682147,3160682154,3160682163,3160682168,3160682315,3160682316,3160682337,3160682495,3160682496,3160682936,3160683617,3160683623,3160683718,3160683821,3160684181,3160684184,3160684645,3160684721,3160684725,3160684970,3160684995,3160685565,3160685572,3160685820,3160685821,3160686381,3160686385,3160688275,3160688289,3160688469,3160688481,3160688487,3160688490,3160688507,3160688512,3160688524,3160688642,3160688649,3160688665,3160689219,3160689259,3160689718,3160690454,3160690486,3160705831,3160705903,3160706496,3160706685,3160706749,3160706757,3160706768,3160706788,3160706789,3160706802,3160706803,3160706937,3160706940,3160706946,3160707420,3160707881,3160707903,3160708728,3160708811,3160708873,3160708886,3160708887,3160708900,3160708911,3160708913,3160708923,3160709049,3160709051,3160709083,3160709718,3160709724,3160709932,3160709935,3160710226,3160710242,3160710270,3160710282,3160710720,3160710721,3160710741,3160710745,3160710753,3160710768,3160710769,3160710784,3160710794,3160710807,3160710813,3160710819,3160710830,3160710838,3160711047,3160711059,3160711074,3160711077,3160711090,3160711093,3160711256,3160711261,3160711591,3160711768,3160712460,3160712461,3160712560,3160712583,3160712727,3160712799,3160712879,3160713181,3160713187,3160713481,3160713490,3160713792,3160713808,3160713846,3160713993,3160714848,3160714849,3160714869,3160714885,3160714898,3160714903,3160714913,3160714931,3160714950,3160715072,3160715075,3160715080,3160715130,3160715133,3160715813,3160715814,3160715968,3160715972,3160715985,3160715990,3160716005,3160716013,3160716026,3160716135,3160716143,3160716153,3160716977,3160716980,3160717331,3160717396,3160717518,3160721494,3160721505,3160721862,3160721868,3160722462,3160722467,3160722686,3160722749,3160722758,3160722869,3160722872,3160723321,3160723327,3160723596,3160723599,3160723656,3160723671,3160723888,3160724077,3160724078,3160724390,3160724778,3160724784,3160724791,3160724792,3160724793,3160724794,3160724795,3160724796,3160724801,3160724831,3160724962,3160724981,3160724995,3160724998,3160725005,3160725006,3160725019,3160725131,3160725138,3160725141,3160725229,3160725231,3160725598,3160725625,3160725776,3160725831,3160726008,3160726012,3160726327,3160726333,3160726950,3160727263,3160727276,3160727498,3160727708,3160727711,3160728540,3160728543,3160729702,3160729911,3160729919,3160729940,3160729941,3160731235,3160731259,3160731261,3160731539,3160731542,3160731635,3160732135,3160732136,3160732188,3160732436,3160732448,3160732522,3160732556,3160732696,3160732707,3160732709,3160732714,3160732729,3160732732,3160732736,3160732805,3160732806,3160732987,3160733069,3160733090,3160733225,3160733798,3160733805,3160734061,3160734068,3160734519,3160734531,3160735327,3160735329,3160735860,3160735865,3160736489,3160736507,3160736511,3160736562,3160736803,3160736854,3160736855,3160737466,3160737582,3160737707,3160737730,3160737844,3160737847,3160737976,3160738007,3160738112,3160738141,3160738158,3160738161,3160738369,3160738420,3160738470,3160738492,3160738571,3160738581,3160738600,3160738989,3160738995,3160739070,3160739077,3160739157,3160739723,3160739726,3160739928,3160739929,3160739935,3160739947,3160739950,3160739971,3160740096,3160740112,3160740116,3160740238,3160740240,3160740266,3160740274,3160740296,3160740302,3160740310,3160740313,3160740321,3160740527,3160740535,3160740846,3160740850,3160740957,3160741087,3160741094,3160741101,3160741102,3160741139,3160741484,3160741556,3160741724,3160741725,3160741756,3160741765,3160741811,3160742277,3160742334,3160742338,3160742593,3160742596,3160742604,3160742739,3160742743,3160742748,3160742751,3160742752,3160742753,3160742758,3160742833,3160742846,3160742855,3160742863,3160742868,3160742871,3160742878,3160742948,3160742953,3160743053,3160743058,3160743059,3160743154,3160743155,3160743156,3160743158,3160743159,3160743160,3160743161,3160743182,3160743270,3160743271,3160743282,3160743297,3160743303,3160743307,3160743319,3160743434,3160743435,3160743454,3160743618,3160743644,3160743666,3160743696,3160743736,3160743739,3160744321,3160744325,3160744575,3160744578,3160744842,3160744880,3160744941,3160745010,3160745011,3160745025,3160745165,3160745171,3160745725,3160745823,3160745871,3160746121,3160746270,3160746287,3160746532,3160746559,3160746703,3160746733,3160746779,3160746790,3160746833,3160746932,3160747070,3160747071,3160747072,3160747073,3160747076,3160747082,3160747090,3160747140,3160747141,3160747143,3160747157,3160747174,3160747185,3160747186,3160747196,3160747199,3160747357,3160747369,3160747374,3160747639,3160747642,3160747688,3160747773,3160747775,3160748696,3160748698,3160748752,3160748753,3160748942,3160749582,3160749586,3160749608,3160749643,3160749644,3160749951,3160749956,3160750038,3160750330,3160750387,3160750390,3160750970,3160751050,3160751052,3160751056,3160751060,3160751063,3160751065,3160751066,3160751073,3160751076,3160751082,3160751163,3160751175,3160751180,3160751189,3160751208,3160751211,3160751216,3160751363,3160751372,3160751380,3160751512,3160751515,3160752379,3160752421,3160752424,3160752535,3160752540,3160752551,3160752555,3160752559,3160752570,3160752578,3160752695,3160752701,3160752702,3160752762,3160752763,3160752764,3160752765,3160752769,3160752772,3160752777,3160752778,3160755563,3160755569,3160755693,3160755694,3160755919,3160756035,3160756038,3160756066,3160756170,3160756171,3160756220,3160756413,3160756417,3160756532,3160756625,3160756626,3160756697,3160759000,3160759001,3160759003,3160759016,3160759040,3160759117,3160759118,3160759136,3160759168,3160759182,3160759185,3160759193,3160759196,3160759203,3160759296,3160759299,3160759312,3160759337,3160759366,3160759371,3160759376,3160759377,3160759380,3160759393,3160759394,3160759498,3160759509,3160759510,3160759518,3160759529,3160759530,3160759605,3160759610,3160759612,3160760355,3160761058,3160761061,3160761089,3160761111,3160761113,3160761115,3160761225,3160761226,3160761235,3160761238,3160761275,3160761278,3160761295,3160761424,3160761425,3160761448,3160761449,3160761460,3160761471,3160761476,3160761491,3160761621,3160761627,3160761628,3160763149,3160763199,3160763225,3160763286,3160763288,3160763289,3160763299,3160763300,3160763364,3160763404,3160763408,3160763493,3160763496,3160763549,3160763700,3160763702,3160763738,3160763810,3160763828,3160764099,3160764114,3160764150,3160764167,3160764235,3160764244,3160764256,3160764431,3160764449,3160764464,3160764648,3160764661,3160764666,3160764670,3160764673,3160764677,3160764678,3160764679,3160764680,3160764687,3160764700,3160764817,3160764839,3160764845,3160764847,3160764860,3160764865,3160764877,3160764997,3160765000,3160765005,3160765150,3160765155,3160765314,3160765317,3160765423,3160765424,3160765479,3160765574,3160765759,3160765776,3160765814,3160765826,3160765829,3160765848,3160765852,3160765873,3160765875,3160765882,3160780375,3160780378,3160780426,3160780432,3160780446,3160780618,3160782687,3160782792,3160782793,3160782807,3160782815,3160782828,3160782832,3160782836,3160782961,3160782967,3160782996,3160783090,3160783091,3160783170,3160783197,3160783199,3160783203,3160783214,3160783216,3160783221,3160783223,3160783229,3160783230,3160783231,3160783330,3160783348,3160783356,3160783363,3160783369,3160783379,3160783382,3160783491,3160783499,3160783507,3160783850,3160783858,3160783859,3160783860,3160783863,3160783865,3160783866,3160783872,3160783944,3160783959,3160783973,3160783976,3160783982,3160783990,3160783999,3160784119,3160784120,3160784134,3160784136,3160784198,3160784201,3160785255,3160785259,3160785285,3160785289,3160785325,3160785639,3160785923,3160785924,3160785982,3160786066,3160786067,3160786240,3160786243,3160787103,3160787121,3160787550,3160787553,3160802734,3160802735,3160802783,3160802788,3160802789,3160802793,3160802794,3160802795,3160802799,3160802807,3160802818,3160802904,3160802918,3160802928,3160802947,3160802948,3160802952,3160802957,3160803087,3160803098,3160803109,3160832536,3160832539,3160832542,3160832543,3160832548,3160832549,3160832556,3160832557,3160832582,3160832587,3160832927,3160832929,3160832980,3160832981,3160833048,3160833456,3160833658,3160833719,3160833741,3160833829,3160833862,3160833956,3160834080,3160834147,3160834156,3160834408,3160835479,3160835585,3160835592,3160835608,3160835612,3160835630,3160837323,3160837515,3160837517,3160838815,3160838938,3160838955,3160840661,3160840939,3160841131,3160841177,3160841453,3160841457,3160841640,3160841665,3160841668,3160845086,3160845097,3160845107,3160845126,3160846152,3160847694,3160847695,3160847704,3160847924,3160847934,3160847975,3160848435,3160849082,3160849087,3160849106,3160849279,3160849280,3160849437,3160849888,3160849891,3160850005,3160850214,3160850219,3160850222,3160850223,3160850225,3160850226,3160850242,3160850246,3160850258,3160850264,3160850266,3160850276,3160850297,3160850305,3160850310,3160850318,3160850324,3160850501,3160850503,3160850539,3160850556,3160864992,3160865014,3160865106,3160865107,3160865156,3160865179,3160865647,3160865651,3160865672,3160865867,3160865868,3160865885,3160865974,3160866025,3160866073,3160866094,3160866095,3160866305,3160866324,3160866803,3160866951,3160866974,3160867141,3160867232,3160867362,3160867896,3160867928,3160867930,3160868129,3160868155,3160868157,3160868158,3160868160,3160868161,3160868173,3160868177,3160868184,3160868208,3160868215,3160868222,3160868225,3160868240,3160868247,3160868352,3160868355,3160868360,3160868397,3160868400,3160868421,3160868658,3160868726,3160868738,3160868832,3160868863,3160868948,3160869002,3160869050,3160869451,3160869452,3160869455,3160869462,3160869489,3160869587,3160869588,3160869618,3160869783,3160869929,3160869949,3160870091,3160870337,3160870397,3160870422,3160870624,3160870654,3160870722,3160870725,3160870863,3160870867,3160871297,3160871300,3160871503,3160871519,3160871523,3160871610,3160871643,3160871769,3160871826,3160871833,3160871883,3160871887,3160872096,3160872099,3160872190,3160872325,3160872343,3160872449,3160872680,3160872683,3160872784,3160872870,3160872872,3160872926,3160873082,3160873092,3160873236,3160873425,3160873492,3160873555,3160873722,3160873728,3160873774,3160873859,3160873861,3160873996,3160874365,3160874430,3160874506,3160874507,3160874728,3160874730,3160874860,3160874948,3160874958,3160875034,3160875092,3160875224,3160875231,3160875391,3160875392,3160875495,3160875594,3160875626,3160875628,3160876182,3160876360,3160876368,3160876619,3160876653,3160876692,3160876694,3160876697,3160876787,3160877004,3160877026,3160877094,3160877105,3160877151,3160877340,3160877556,3160877574,3160877577,3160877599,3160877733,3160877898,3160878007,3160878107,3160878296,3160878298,3160878754,3160878758,3160878900,3160878980,3160878983,3160879001,3160879104,3160879634,3160879635,3160879702,3160880003,3160880006,3160880539,3160880543,3160880704,3160880706,3160880802,3160880844,3160880919,3160881095,3160881419,3160881442,3160881453,3160881622,3160881629,3160881632,3160881642,3160881645,3160881646,3160881651,3160881659,3160881700,3160881709,3160881722,3160881740,3160881743,3160881749,3160881756,3160881872,3160881875,3160881882,3160881975,3160881977,3160882035,3160882189,3160882191,3160882192,3160882197,3160882199,3160882207,3160882208,3160882213,3160882294,3160882306,3160882317,3160882328,3160882329,3160882334,3160882335,3160882452,3160882461,3160882462,3160882534,3160882538,3160883198,3160883372,3160883376,3160883407,3160883677,3160883678,3160883731,3160883735,3160884371,3160884373,3160884407,3160884667,3160884692,3160884696,3160884697,3160884702,3160884703,3160884706,3160884707,3160884720,3160884721,3160884723,3160884724,3160884729,3160884741,3160884747,3160884872,3160884880,3160884881,3160885050,3160885055,3160885169,3160885173,3160885178,3160885357,3160886216,3160886228,3160886239,3160886240,3160886275,3160886439,3160886490,3160886871,3160886873,3160887049,3160887193,3160887196,3160887215,3160887281,3160887481,3160887756,3160887758,3160887798,3160888021,3160888023,3160888042,3160888125,3160888276,3160888568,3160888623,3160888626,3160888806,3160888880,3160888886,3160889660,3160889661,3160889743,3160889745,3160889778,3160890007,3160890018,3160890104,3160890291,3160895341,3160895421,3160895424,3160895560,3160895581,3160895761,3160895762,3160895796,3160895872,3160896181,3160896282,3160896292,3160896320,3160896412,3160896450,3160896865,3160896890,3160897093,3160897143,3160897152,3160900675,3160900676,3160900811,3160900984,3160900995,3160901064,3160901082,3160901642,3160901643,3160901651,3160901652,3160901653,3160901658,3160901659,3160901661,3160901677,3160901681,3160901734,3160901808,3160901828,3160901833,3160901843,3160901856,3160901857,3160901867,3160901990,3160901995,3160902000,3160903715,3160903716,3160903856,3160903858,3160903925,3160903954,3160903984,3160904199,3160904215,3160904402,3160904421,3160904585,3160904699,3160905888,3160905925,3160905926,3160906103,3160906107,3160906108,3160906111,3160906112,3160906117,3160906120,3160906121,3160906291,3160906309,3160906317,3160906318,3160906330,3160906334,3160906340,3160906533,3160906564,3160906568,3160906569,3160907038,3160907197,3160907200,3160907270,3160907379,3160907394,3160907413,3160907525,3160907719,3160907811,3160907968,3160907972,3160908508,3160908509,3160908624,3160908683,3160908876,3160908879,3160908881,3160908884,3160908895,3160908897,3160908901,3160908904,3160908905,3160908936,3160909004,3160909026,3160909033,3160909051,3160909052,3160909076,3160909080,3160909232,3160909241,3160909242,3160923923,3160923925,3160923967,3160924255,3160924261,3160924268,3160924826,3160924827,3160924971,3160925119,3160925132,3160925135,3160925157,3160925171,3160925410,3160925413,3160925647,3160925783,3160925787,3160925863,3160925882,3160925969,3160925986,3160926125,3160926328,3160926428,3160926436,3160926449,3160926721,3160926767,3160926781,3160927072,3160927515,3160927576,3160927578,3160927793,3160927801,3160927865,3160927931,3160928011,3160928018,3160928267,3160928269,3160928306,3160928408,3160928434,3160928450,3160928658,3160928794,3160928866,3160928922,3160929021,3160929022,3160929071,3160929359,3160929373,3160929380,3160929637,3160929640,3160929675,3160929690,3160930011,3160930014,3160930180,3160930183,3160930277,3160930435,3160930442,3160930443,3160930444,3160930445,3160930447,3160930448,3160930465,3160930482,3160930527,3160930543,3160930555,3160930556,3160930563,3160930565,3160930660,3160930664,3160930665,3160931043,3160931045,3160931251,3160931390,3160931392,3160931394,3160931395,3160931407,3160931410,3160931411,3160931412,3160931430,3160931433,3160931526,3160931527,3160931563,3160931564,3160931572,3160931575,3160931582,3160931704,3160931714,3160931725,3161345185,3161345187,3161360345,3161360352,3161360382,3161360545,3161360547,3161360550,3161360551,3161360556,3161360561,3161360562,3161360564,3161360652,3161360656,3161360659,3161360672,3161360676,3161360681,3161360688,3161360819,3161360820,3161360828,3161361362,3161361367,3161361431,3161361437,3161361598,3161361689,3161362228,3161362231,3161362292,3161362449,3161362459,3161362606,3161362853,3161363038,3161363048,3161363499,3161363591,3161363613,3161364064,3161364066,3161364407,3161364425,3161364432,3161364497,3161364728,3161364805,3161365403,3161365568,3161365571,3161365664,3161365676,3161365736,3161365780,3161366125,3161367015,3161367021,3161367207,3161367218,3161367224,3161367238,3161367243,3161367257,3161367265,3161367267,3161367275,3161367444,3161367517,3161367526,3161367532,3161367541,3161367547,3161367561,3161367685,3161367696,3161367709,3161370377,3161370388,3161370395,3161370421,3161370431,3161370861,3161370993,3161372936,3161373027,3161373028,3161373202,3161373222,3161373226,3161373571,3161373625,3161373631,3161373969,3161374024,3161374039,3161375641,3161375791,3161375808,3161375925,3161375959,3161375989,3161375992,3161376524,3161376527,3161376563,3161376850,3161376855,3161377851,3161377854,3161378618,3161378832,3161378833,3161378836,3161378841,3161379014,3161462053,3161462086,3161462087,3161462226,3161462227,3161462303,3161462525,3161462610,3161462615,3161462723,3161463010,3161463026,3161463610,3161463615,3161466042,3161466049,3161466092,3161466282,3161466290,3161466305,3161466306,3161466372,3161466585,3161466605,3161466683,3161466734,3161467699,3161467705,3161467809,3161467810,3161467888,3161468048,3161468572,3161468576,3161468626,3161468877,3161468941,3161468948,3161469176,3161469184,3161469226,3161469230,3161469236,3161469311,3161469831,3161469896,3161469899,3161469906,3161469910,3161469926,3161469940,3161469943,3161469945,3161469947,3161469953,3161469959,3161469967,3161469977,3161469978,3161469983,3161469987,3161469990,3161470011,3161470025,3161470038,3161470053,3161470077,3161470078,3161470080,3161470086,3161470096,3161470105,3161470111,3161470131,3161470132,3161470138,3161470157,3161470165,3161470405,3161470417,3161470440,3161470441,3161470451,3161470465,3161470760,3161470816,3161470876,3161470973,3161470988,3161470990,3161471011,3161471309,3161471339,3161471345,3161471421,3161471422,3161471423,3161471424,3161471438,3161471708,3161471795,3161471797,3161472014,3161472022,3161472023,3161472024,3161472025,3161472030,3161472035,3161472038,3161472180,3161472185,3161472190,3161472197,3161472208,3161472209,3161472217,3161472343,3161472355,3161472360,3161472389,3161472392,3161472593,3161472597,3161472601,3161472602,3161472603,3161472611,3161472612,3161472613,3161472629,3161472735,3161472744,3161472762,3161472771,3161472774,3161472785,3161472786,3161472911,3161472912,3161472929,3161472969,3161472970,3161472972,3161473302,3161473321,3161473884,3161473959,3161473960,3161474111,3161474120,3161474686,3161474688,3161474771,3161474775,3161474839,3161474842,3161475009,3161475012,3161475125,3161475126,3161475158,3161475398,3161475472,3161475506,3161475539,3161475547,3161475709,3161475749,3161475944,3161475946,3161475965,3161476722,3161476725,3161476776,3161477112,3161477130,3161477578,3161477581,3161477711,3161478229,3161478379,3161478381,3161478673,3161479700,3161479701,3161479755,3161480311,3161480321,3161480880,3161480881,3161481489,3161483745,3161483761,3161483773,3161483905,3161483910,3161483911,3161483913,3161483924,3161483925,3161483933,3161483934,3161484004,3161484034,3161484140,3161484357,3161484484,3161484668,3161484678,3161484816,3161484880,3161484900,3161484901,3161484973,3161485130,3161485282,3161485481,3161485484,3161485641,3161485642,3161485670,3161485780,3161485884,3161485980,3161486147,3161487014,3161487015,3161487041,3161487255,3161487260,3161487379,3161487466,3161487579,3161488004,3161488011,3161488123,3161488193,3161488288,3161488414,3161488427,3161488487,3161488815,3161488816,3161488942,3161489003,3161489194,3161489256,3161489432,3161489435,3161489600,3161489685,3161489703,3161489811,3161489878,3161489909,3161489943,3161489974,3161490547,3161490549,3161490567,3161490822,3161490846,3161490860,3161491088,3161491213,3161491403,3161491404,3161491557,3161491612,3161491621,3161491630,3161491631,3161491634,3161491639,3161491649,3161491650,3161491728,3161491743,3161491763,3161491771,3161491772,3161491781,3161491785,3161491908,3161491914,3161491930,3161493453,3161493455,3161493496,3161493763,3161493772,3161493777,3161493778,3161493806,3161493821,3161493833,3161493835,3161493846,3161493866,3161493915,3161493918,3161493920,3161493921,3161493932,3161493934,3161493936,3161493938,3161494324,3161494399,3161494405,3161494481,3161494491,3161494500,3161494571,3161494573,3161494581,3161494616,3161494640,3161494651,3161494678,3161494680,3161494681,3161494685,3161494687,3161494689,3161494691,3161494693,3161533940,3161534075,3161534203,3161534950,3161535097,3161535220,3161536321,3161536322,3161536550,3161536558,3161555195,3161563509,3161563530,3161563535,3161563582,3161563585,3161563586,3161563614,3161563843,3161563845,3161563847,3161563851,3161563881,3161563887,3161565873,3161565877,3161565894,3161565896,3161566458,3161566463,3161566465,3161566466,3161570412,3161579146,3161579159,3161579331,3161579675,3161579690,3161579748,3161579785,3161580172,3161580175,3161580212,3161580213,3161580317,3161580348,3161581835,3161581858,3161581924,3161581927,3161582018,3161582058,3161582064,3161582066,3161607167,3161607311,3161607319,3161607462,3161609368,3161609970,3161609979,3161610134,3161617270,3161617275,3161617339,3161619307,3161619313,3161619343,3161619401,3161619568,3161619721,3161619743,3161619745,3161619752,3161619753,3161619756,3161619758,3161619760,3161619761,3161620331,3161620343,3161620416,3161620496,3161620502,3161620854,3161620858,3161621135,3161621145,3161621224,3161621497,3161621500,3161621504,3161621506,3161621556,3161621559,3161621566,3161621572,3161621574,3161621575,3161621578,3161621580,3161621582,3161621583,3161621609,3161621862,3161621868,3161621914,3161621963,3161622298,3161622303,3161622376,3161622479,3161622526,3161622530,3161622543,3161623357,3161623360,3161623402,3161623483,3161623512,3161623636,3161623748,3161623753,3161623766,3161623770,3161623889,3161623894,3161623940,3161623942,3161623945,3161623962,3161623972,3161623973,3161623979,3161623981,3161623983,3161623984,3161623986,3161623987,3161624232,3161624233,3161624304,3161624613,3161624617,3161624936,3161624938,3161625155,3161625164,3161625513,3161625672,3161625673,3161625726,3161626458,3161626499,3161626544,3161626604,3161626721,3161626725,3161626728,3161626731,3161626807,3161626849,3161626851,3161627030,3161627032,3161627047,3161627229,3161627838,3161627839,3161627841,3161627852,3161627854,3161627962,3161627963,3161627986,3161628217,3161628219,3161628666,3161628673,3161628808,3161628824,3161628855,3161628856,3161628863,3161628864,3161628865,3161628866,3161628956,3161628957,3161629003,3161629241,3161629319,3161629383,3161629403,3161629412,3161629427,3161629457,3161629510,3161629536,3161629557,3161629572,3161629577,3161629583,3161629584,3161629591,3161629593,3161629595,3161629596,3161629975,3161630044,3161630046,3161630139,3161630392,3161630412,3161630878,3161630880,3161631071,3161631083,3161631084,3161631618,3161631619,3161631637,3161631846,3161631849,3161632002,3161632004,3161632005,3161632006,3161632163,3161632327,3161632364,3161632606,3161632607,3161632654,3161632661,3161632686,3161632696,3161632697,3161632749,3161632785,3161632791,3161632801,3161632926,3161632949,3161633080,3161633082,3161633092,3161633106,3161633113,3161633119,3161633146,3161633154,3161633155,3161633161,3161633169,3161633171,3161633180,3161633189,3161633193,3161633196,3161633199,3161633200,3161633204,3161633208,3161633215,3161633217,3161633219,3161633220,3161633223,3161633225,3161633375,3161633376,3161633831,3161633833,3161633890,3161633895,3161633913,3161633990,3161634110,3161634123,3161634138,3161634140,3161634312,3161634329,3161634443,3161634552,3161634554,3161634563,3161634564,3161634581,3161634582,3161634600,3161634601,3161634602,3161634603,3161634607,3161634809,3161634810,3161634811,3161634812,3161634813,3161634818,3161634983,3161635008,3161635010,3161635175,3161635255,3161635271,3161635385,3161635749,3161635750,3161650587,3161650611,3161650658,3161650924,3161650928,3161650973,3161651330,3161651334,3161651528,3161651656,3162562858,3162598279,3162600116,3162646093,3162646324,3162646328,3162693704,3162752747,3162800014,3163526738,3163699633,3163798837,3163898998,3163917805,3161651782,3161651803,3161652188,3161652191,3161652375,3161652380,3161652488,3161652666,3161652673,3161652699,3161652709,3161652750,3161652752,3161652766,3161652767,3161652768,3161652776,3161652784,3161652789,3161652790,3161652794,3161652808,3161652883,3161652884,3161652887,3161653202,3161653206,3161653252,3161653473,3161653502,3161653512,3161653534,3161653535,3161653838,3161653946,3161653957,3161654006,3161654372,3161654481,3161654486,3161654602,3161654628,3161654630,3161654818,3161654987,3161655005,3161655006,3161655016,3161655071,3161655074,3161655121,3161655163,3161655280,3161655282,3161655287,3161655292,3161655359,3161655412,3161655470,3161655472,3161655475,3161655515,3161655516,3161655523,3161655982,3161656022,3161656070,3161656080,3161656181,3161656204,3161656257,3161656295,3161656494,3161657886,3161657928,3161657932,3161658102,3161658106,3161658111,3161658728,3161658758,3161658761,3161659056,3161659077,3161659116,3161659121,3161659140,3161659141,3161659144,3161659149,3161659150,3161659162,3161659164,3161659173,3161659184,3161659191,3161659193,3161659312,3161659313,3161659325,3161660245,3161660362,3161660368,3161660370,3161660386,3161660387,3161660396,3161660398,3161660405,3161660406,3161660407,3161660408,3161660420,3161660430,3161660444,3161660456,3161660552,3161660560,3161660565,3161660586,3161660588,3161660634,3161660636,3161660757,3161660959,3161660970,3161660976,3161661008,3161661109,3161661201,3161661218,3161661227,3161661237,3161661320,3161661329,3161661330,3161661567,3161661652,3161661653,3161662168,3161662250,3161662262,3161662437,3161662453,3161663404,3161663406,3161663467,3161663729,3161664301,3161664459,3161664479,3161664604,3161664710,3161664730,3161665045,3161665290,3161665305,3161665309,3161665633,3161665639,3161665889,3161665892,3161666065,3161666066,3161666191,3161666388,3161666441,3161666451,3161666665,3161666678,3161666712,3161666800,3161666821,3161666957,3161667133,3161667135,3161667245,3161667257,3161667781,3161667838,3161667839,3161668064,3161668068,3161700969,3161700970,3161701217,3161701452,3161701472,3161701478,3161701849,3161701861,3161701950,3161702002,3161702029,3161702033,3161702392,3161702400,3161702415,3161702433,3161702474,3161702509,3161702641,3161702747,3161702756,3161702758,3161702809,3161702825,3161703095,3161703124,3161703176,3161762438,3161762442,3161762714,3161763033,3161763045,3161763081,3161763095,3161763131,3161763135,3161763138,3161763165,3161763166,3161763171,3161763184,3161763214,3161763230,3161763232,3161763264,3161763604,3161763650,3161763698,3161766480,3161766569,3161766570,3161766731,3161766747,3161766748,3161766761,3161766790,3161766791,3161766816,3161766825,3161766826,3161766827,3161766829,3161766842,3161766843,3161766850,3161766857,3161766877,3161766885,3161766887,3161766913,3161767182,3161767183,3161767190,3161767191,3161767240,3161767241,3161767242,3161767246,3161767249,3161767260,3161767263,3161767270,3161767276,3161767277,3161767299,3161767307,3161767338,3161767354,3161767360,3161767362,3161767474,3161767476,3161767618,3161767737,3161767740,3161767743,3161767792,3161767796,3161767812,3161767841,3161767933,3161767935,3161767938,3161768017,3161768019,3161768160,3161768331,3161768335,3161768340,3161768352,3161768381,3161768386,3161768387,3161768389,3161768393,3161768399,3161768400,3161768405,3161768419,3161768424,3161768427,3161768459,3161768467,3161768469,3161768519,3161768521,3161768673,3161768746,3161768747,3161768754,3161768843,3161768874,3161768882,3161768904,3161768932,3161768933,3161768939,3161769088,3161769093,3161769128,3161769314,3161769316,3161769318,3161769338,3161769391,3161769400,3161769411,3161769414,3161769520,3161769525,3161769533,3161769646,3161769700,3161769703,3161769769,3161769770,3161769773,3161769774,3161769811,3161769817,3161769935,3161769936,3161769958,3161769960,3161769983,3161769984,3161769988,3161769993,3161769995,3161769997,3161769999,3161770000,3161770107,3161770121,3161770220,3161770222,3161770227,3161770230,3161770281,3161770290,3161770323,3161770334,3161770338,3161770395,3161770400,3161770406,3161770407,3161770410,3161770411,3161770412,3161770414,3161770493,3161770494,3161770516,3161770618,3161770628,3161770631,3161770667,3161770683,3161770723,3161770725,3161770726,3161770736,3161770752,3161770766,3161770772,3161770774,3161770776,3161770778,3161770780,3161770781,3161770793,3161770944,3161770946,3161770964,3161771005,3161771006,3161771007,3161771008,3161771043,3161771044,3161771093,3161771107,3161771108,3161771111,3161771133,3161771134,3161771142,3161771144,3161771145,3161771148,3161771150,3161771151,3161771202,3161771204,3161771305,3161771367,3161771373,3161771378,3161771379,3161771503,3161771521,3161771531,3161771545,3161771558,3161771565,3161771619,3161771680,3161771681,3161771799,3161771800,3161771847,3161771849,3161771920,3161771922,3161774062,3161774064,3161775172,3161775173,3161775234,3161775236,3161775288,3161775289,3161775401,3161775402,3161775745,3161775749,3161775823,3161775824,3161775868,3161775869,3161776000,3161776001,3161776004,3161776008,3161776190,3161776191,3161776244,3161776250,3161776695,3161776696,3161777074,3161777075,3161777126,3161777127,3161777216,3161777218,3161777262,3161777263,3161777358,3161777359,3161777412,3161777413,3161792182,3161792183,3161792229,3161792232,3161792354,3161792355,3161792750,3161792752,3161794006,3161794009,3161794063,3161794065,3161794915,3161794916,3161795020,3161795022,3161795119,3161795121,3161795163,3161795165,3161799785,3161799787,3161800664,3161800665,3161800869,3161800870,3161800913,3161800914,3161800960,3161800962,3161801096,3161801098,3161801753,3161801755,3161801808,3161801809,3161802134,3161802391,3161802393,3161802451,3161802452,3161802497,3161802498,3161803337,3161803339,3161803441,3161803443,3161803520,3161803521,3161804460,3161804461,3161804485,3161804486,3161804500,3161804503,3161804532,3161804533,3161805507,3161805508,3161808035,3161808036,3161808081,3161808083,3161808985,3161808987,3161809212,3161809216,3161824603,3161824604,3161824757,3161824759,3161825797,3161825798,3161827660,3161827661,3161828952,3161828953,3161829219,3161829225,3161829533,3161829537,3161829898,3161829900,3161830601,3161830603,3161831018,3161831020,3161831370,3161831371,3161871442,3161871443,3161872174,3161872182,3161872335,3161872342,3161872892,3161872894,3161872929,3161872933,3161874306,3161874311,3161874788,3161874792,3161875164,3161875165,3161875653,3161875654,3161876145,3161876146,3161876501,3161876502,3161876967,3161876968,3161877803,3161877806,3161878368,3161878369,3161879580,3161879581,3161879700,3161879701,3161880099,3161880102,3161880507,3161881080,3161881085,3161881236,3161881247,3161881654,3161881658,3161883351,3161883353,3161883841,3161884031,3161884654,3161884732,3161885163,3161885166,3161899673,3161899676,3161900019,3161900020,3161900774,3161900776,3161901203,3161901206,3161902171,3161902173,3161902844,3161902845,3161903309,3161903313,3161904755,3161904760,3161905990,3161905992,3161906129,3161906130,3161906529,3161906535,3161907010,3161907011,3161908665,3161908668,3161909163,3161909177,3161910480,3161910481,3161911046,3161911047,3161911502,3161911512,3161913082,3161913083,3161913500,3161913501,3161913891,3161913897,3161914462,3161914477,3161916570,3161916705,3161916707,3161917154,3161917158,3161917738,3161917742,3161918609,3161918616,3161919024,3161919026,3161920003,3161920004,3161921555,3161921558,3161922425,3161922426,3161922936,3161922937,3161923416,3161923417,3161923960,3161923961,3161924461,3161924463,3161924534,3161925074,3161925075,3161926655,3161926656,3161927789,3161927804,3161928335,3161928338,3161928708,3161928709,3161930232,3161930233,3161931632,3161931638,3161931648,3161931690,3161931995,3161931998,3161932407,3161932414,3161932981,3161932984,3161933447,3161933448,3161934285,3161934286,3161934693,3161934695,3161934809,3161934810,3161934947,3161934949,3161935104,3161935105,3161936776,3161936783,3161936960,3161937257,3161937259,3161937715,3161937716,3161938110,3161938114,3161938724,3161938725,3161939505,3161939506,3161939796,3161939800,3161940380,3161943573,3161943575,3161945338,3161945339,3161946548,3161946551,3161947671,3161947683,3161947686,3161947706,3161948681,3161948683,3161949047,3161949214,3161949215,3161949665,3161949666,3161950815,3161950817,3161951094,3161951098,3161951249,3161951263,3161952029,3161952045,3161952559,3161952561,3161953288,3161953290,3161954737,3161954749,3161957805,3161957818,3161958347,3161958350,3161958953,3161958955,3162175653,3162175656,3162175840,3162175844,3162176305,3162176307,3162176392,3162176393,3162176416,3162176417,3162177410,3162177417,3162177796,3162177799,3162178379,3162178380,3162179289,3162179291,3162180604,3162180608,3162182599,3162182602,3162189301,3162189302,3162190035,3162190037,3162190668,3162190670,3162191371,3162191372,3162193104,3162209005,3162209006,3162209538,3162209545,3162211595,3162211608,3162214157,3162214159,3162214631,3162216248,3162216251,3162216545,3162216556,3162216963,3162216964,3162217571,3162217573,3162218089,3162218097,3162218757,3162218765,3162219221,3162219231,3162220256,1118175932,1120725375,1158021123,1158021664,1158021750,1158021841,1158022113,1158022240,1158022278,1158022375,1620822590,1634798956,1106053043,1113710026,38786844,1134388682,3160731051,3160835305,3118519787,3161657423,3117991856];
//        $existCompanyIds = \PgActiveRecord::getDbByClientId(1)->createCommand("select company_id from tbl_company where client_id=1  and user_id && ARRAY[11864032,1,3,10,14,37,377,482,483,484,734,765,30393,30421,46900,51026,51053,51054,51060,51068,51106,51182,51305,51863,71433,71475,72135,72278,11850755,11850833,11850882,11850887,11851120,11851127,11851806,11851845,11851925,11851926,11852193,11852355,11852396,11852447,11852554,11852571,11855392,11855791,11856186,11856745,11857371,11857375,11857742,11857799,11857805,11857806,11858010,11858048,11858078,11858289,11858314,11858374,11858380,11858381,11858398,11858408,11858410,11858412,11858414,11858416,11858450,11858460,11858462,11858463,11858464,11858466,11858469,11858482,11858507,11858508,11858509,11858519,11858528,11858549,11858572,11858629,11858630,11858643,11858687,11858689,11858691,11858693,11858694,11858695,11858696,11858697,11858698,11858701,11858704,11858710,11858730,11858792,11858793,11858794,11858795,11858796,11858819,11858821,11859007,11859068,11859096,11859100,11859141,11863701,11863707,11863759,11863786,11863794,11863822,11863955,11863993,11863999,11864011,11864012,11864013,11864014,11864022,11864030,11864038,11864046,249503788,249503790,249503792,249503807,249503809,249503810,249503811,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********]::bigint[] AND is_archive=1
// and ( client_id=1 AND is_archive=1 AND (( company_id in (select company_id from tbl_customer where  client_id=1 AND is_archive=1 AND ( email=''  ))
//                                               OR company_id in (select company_id from tbl_customer where  client_id=1 AND is_archive=1 AND ( tel_list='{}'  ))
//                                               OR company_id in (select company_id from tbl_customer where  client_id=1 AND is_archive=1 AND ( name=''  )) ) ));
//")->queryColumn();
//        dd(array_values(array_diff($existCompanyIds, $companyIds)));

        $this->loginAsWeason3();
        $this->callAction('edit', [
//            'data' => '{"name":"联系人联系方式为空客群","type":1,"criteria_type":2,"criteria":"(1 OR 2 OR 3)","filters":[{"field_no":1,"field":"email","field_type":1,"operator":"is_null","value":null,"unit":"","value_type":null,"refer_type":5,"name":"邮箱","filter_no":1},{"field_no":2,"field":"tel_list","field_type":1,"operator":"is_null","value":null,"unit":"","value_type":null,"refer_type":5,"name":"联系电话","filter_no":2},{"field_no":3,"field":"name","field_type":1,"operator":"is_null","value":null,"unit":"","value_type":null,"refer_type":5,"name":"昵称","filter_no":3}],"second_filter_type":1,"second_filters":[],"iterate_field":null,"iterate_value":"","iterate_module":"","iterate_relate":"","criteria_field_desc":[{"field_no":1,"field":"email","field_type":1,"operator":"is_null","value":null,"unit":"","value_type":null,"refer_type":5,"name":"邮箱","filter_no":1,"field_name":"联系人邮箱","operator_name":"为空","value_name":null},{"field_no":2,"field":"tel_list","field_type":1,"operator":"is_null","value":null,"unit":"","value_type":null,"refer_type":5,"name":"联系电话","filter_no":2,"field_name":"联系人联系电话","operator_name":"为空","value_name":null},{"field_no":3,"field":"name","field_type":1,"operator":"is_null","value":null,"unit":"","value_type":null,"refer_type":5,"name":"昵称","filter_no":3,"field_name":"联系人昵称","operator_name":"为空","value_name":null}],"swarm_id":"3163210709","user_id":11864032,"prefix":"0-","layer":"1","relate_id":"0","field_type":"0","public_rule_flag":0,"iterate_field_desc":"","calculating_flag":"0","node":null}',
            'data' => '{"name":"1212","type":0,"criteria_type":1,"criteria":"","filters":[{"field_no":0,"field":"archive_time","field_type":10,"operator":"earlier","value":1,"unit":"天","value_type":"before_current","refer_type":4,"name":"创建时间","filter_no":1}],"second_filter_type":1,"second_filters":[],"scope":null,"criteria_visual":null,"swarm_id":"3369314745","user_id":0,"iterate_field":null,"node":null}',
            'dry_run' => 0,
        ]);
        $this->responseOk();
    }

    public function testTempSwarmCompanyList()
    {
        $this->loginAsQiao();
        $this->callAction('companyList', [
            'temp_swarm_id' => '141199f4cdefbad64d6dec1a5471125a72a00',
        ], 'customerRead');
        $this->responseOk();
    }


    public function testDebugBatchCreateSwarm() {

//		$this->loginAsRuby();
//        $this->loginAsQiao();
//        $param = [
//            'data' => '{"name":"联系人昵称","type":0,"criteria_type":1,"criteria":"()","filters":[{"field_no":0,"field":"name","field_type":1,"operator":"is_null","value":null,"unit":"","value_type":null,"refer_type":5,"name":"昵称","rule_type":1}],"second_filter_type":1,"second_filters":[],"iterate_field":"","iterate_value":"","swarm_id":"1887827766","user_id":0,"prefix":"0-","layer":"1"}',
//            'dry_run' => 1,
//        ];
//        $this->callAction('create', $param);
//        $this->responseOk();
//        return;

//        $this->loginUser(46900);
//        $this->enableProfile();
        $this->loginUser(11856593);
        $this->loginAsQiao();
        $this->loginUser('<EMAIL>');
        $dataList = '[{"name":"多选1111","type":0,"criteria_type":1,"criteria":"","filters":[],"has_second_filter":true,"second_filter_type":1,"second_filter_field":"2607415264","second_filters":[],"user_id":0,"iterate_field":"2607415264","node":[{"name":"a","iterate_value":"a"},{"name":"b","iterate_value":"b"},{"name":"c","iterate_value":"c"}]},{"name":"多选","type":0,"criteria_type":1,"criteria":"","filters":[],"has_second_filter":true,"second_filter_type":1,"second_filter_field":"2607415264","second_filters":[],"user_id":0,"iterate_field":"2607415264","node":[{"name":"a","iterate_value":"a"},{"name":"b","iterate_value":"b"},{"name":"c","iterate_value":"c"}]}]';
        $param = [
//			'data'    => '{"name":"hello","type":1,"criteria_type":1,"criteria":"","filters":[{"field_no":16,"field":"gender","field_type":3,"operator":"is_null","value":[],"unit":"","value_type":null,"date_type":null,"refer_type":5,"name":"性别","rule_type":2,"filter_no":1}],"has_second_filter":false,"second_filter_type":2,"second_filters":[{"iterate_field":"","iterate_value":"","criteria":"","criteria_type":1,"filters":[],"criteria_desc":"","order_rank":"1","display_flag":"","swarm_id":"1844145394","user_id":"11858712","name":"进入公海次数","create_user":"11858712","create_time":"2022-01-15 16:34:59","update_time":"2022-01-15 16:34:59","system_flag":"0","parent_id":"1830950838","prefix":"0-1830950838-","layer":"2","iterate_type":"0","template_id":"0","customer_count":509}],"iterate_field":"","iterate_value":"","swarm_id":"1830950838","user_id":11858712,"prefix":"0-","layer":"1","node":[{"iterate_field":"","iterate_value":"","criteria":"","criteria_type":1,"filters":[],"criteria_desc":"","order_rank":"1","display_flag":"","swarm_id":"1844145394","user_id":"11858712","name":"进入公海次数","create_user":"11858712","create_time":"2022-01-15 16:34:59","update_time":"2022-01-15 16:34:59","system_flag":"0","parent_id":"1830950838","prefix":"0-1830950838-","layer":"2","iterate_type":"0","template_id":"0","customer_count":509}]}',
            'data_list' => $dataList,
        ];

        $this->callAction('batchCreate', $param);

        $this->responseOk();

    }



}
