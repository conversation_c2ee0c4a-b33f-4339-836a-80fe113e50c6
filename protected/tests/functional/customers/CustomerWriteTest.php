<?php

namespace tests\functional\customer;

use common\library\customer_v3\company\orm\Company;
use common\library\customer\Helper;
use common\library\setting\item\Api;
use CompanyModel;
use User;


/**
 * Created by PhpStorm.
 * User: troy.li
 * Date: 2017-12-22
 * Time: 10:53 AM
 */

class CustomerWriteTest extends \WebFunctionalTestCase
{

    public static function setUpBeforeClass():void
    {
        parent::setUpBeforeClass();
        self::$name = 'CustomerWrite';

    }


    public function testSetTags() {

        $this->loginUser(46900);

        $params = [
            'company_ids'    => [3383927848],
            'add_tag_ids'    => [3394367455],
            'delete_tag_ids' => [3180376225],
        ];

        $this->callAction('setTags', $params);

        $this->responseOk();
    }


    public function testSetAssess() {

        $this->loginClient(1);

        $assessList = (Api::assessDimension(self::$clientId, \Constants::TYPE_COMPANY))->listAll();

        array_walk($assessList, function (&$item) {

            $item['dimension_value'] = rand(0,4);
        });

//        unset($assessList[0]);
//        $assessList[1]['dimension_id'] = $assessList[0]['dimension_id'];

//        $assessList = array_merge($assessList, $assessList, $assessList);

        $dataList = json_encode($assessList);

        $param = [

            'company_ids' => [3403879671],
            'data_list'   => $dataList,
            'type'        => 2,
            'remark'      => '',
        ];

        $this->callAction('SetAssess', $param);

        $this->responseOk();
    }


    protected function originData()
    {
        return [
            [
                "origin_id"   => "8",
                "origin_name" => "合作交换"
            ],
            [
                "origin_id"   => "9",
                "origin_name" => "自主开发123123"
            ],
            [
                "origin_id"   => "10",
                "origin_name" => "其他"
            ],
            [
                "origin_id"   => "296439",
                "origin_name" => "展会"
            ],
            [
                "origin_id"   => "21733740",
                "origin_name" => "自主开发"
            ],
            [
                "origin_id"   => "21752933",
                "origin_name" => "enen"
            ],
            [
                "origin_id"   => "32256961",
                "origin_name" => "小满发现"
            ],
            [
                "origin_id"   => "32322715",
                "origin_name" => "你听得到"
            ],
            [
                "origin_id"   => "32433683",
                "origin_name" => "听说"
            ],
            [
                "origin_id"   => "32444243",
                "origin_name" => "dev"
            ],
            [
                "origin_id"   => "4",
                "origin_name" => "互联网"
            ],
            [
                "origin_id"   => "5",
                "origin_name" => "熟人介绍"
            ],
            [
                "origin_id"   => "6",
                "origin_name" => "广告投放"
            ],
            [
                "origin_id"   => "7",
                "origin_name" => "海关数据"
            ]
        ];
    }

    public function testSetOriginOrder()
    {
        $params = [
            'origin_ids' => implode(',', array_column($this->originData(), 'origin_id')),
        ];

        $result = $this->callAction('SetOriginOrder', $params);

        $this->responseOk($result);
        $this->echoResult($result);
    }

    public function testEditCompany()
    {
        $this->loginAsQiao();
        $companyId = 1645922235;
        $data = json_decode('
        {"company":[{"group_id":1,"name":"基本信息","fields":[{"id":"name","name":"公司名称","base":"1","require":"1","group_id":"1","hint":"请输入公司名称","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"1","unique_prevent":"1","unique_message":"","value":"黑不溜秋1","format":"黑不溜秋1"},{"id":"short_name","name":"简称","base":"1","require":"0","group_id":"1","hint":"简称提示","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"1","unique_prevent":"1","unique_message":"已有相同的公司简称","value":"","format":""},{"id":"serial_id","name":"客户编号","base":"1","require":"0","group_id":"1","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"1","unique_prevent":"1","unique_message":"","value":"9679","format":"9679"},{"id":"origin","name":"客户来源","base":"1","require":"0","group_id":"1","hint":"客户来源提示","field_type":"3","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"1101043666","format":"0"}]},{"group_id":2,"name":"特征信息","fields":[{"id":"1514096184","name":"锁定期","base":"0","require":"0","group_id":"2","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"","format":""},{"id":"intention_level","name":"采购意向","base":"1","require":"0","group_id":"2","hint":"","field_type":"3","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":0,"format":"未知"},{"id":"annual_procurement","name":"年采购额","base":"1","require":"0","group_id":"2","hint":"","field_type":"3","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":0,"format":"无采购额"},{"id":"biz_type","name":"客户类型","base":"1","require":"0","group_id":"2","hint":"客户类型提示","field_type":"3","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"","format":""},{"id":"category_ids","name":"主营产品","base":"1","require":"0","group_id":"2","hint":"主营产品提示","field_type":"7","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":[],"format":"[]"},{"id":"country","name":"国家地区","base":"1","require":"0","group_id":"2","hint":"国家地区提示","field_type":"3","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"","format":""},{"id":"province","name":"省份","base":"1","require":"0","group_id":"2","hint":"身份提示","field_type":"3","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"","format":""},{"id":"city","name":"城市","base":"1","require":"0","group_id":"2","hint":"城市提示","field_type":"3","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"","format":""},{"id":"scale_id","name":"规模","base":"1","require":"0","group_id":"2","hint":"规模提示","field_type":"3","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":0,"format":"0"},{"id":"1101101841","name":"自定义日期-时间","base":"0","require":"0","group_id":"2","hint":"自定义时间提示","field_type":"10","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"","format":""},{"id":"timezone","name":"时区","base":"1","require":"0","group_id":"2","hint":"时区提示","field_type":"3","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"","format":""}]},{"group_id":3,"name":"管理信息","fields":[{"id":"star","name":"客户星级","base":"1","require":"1","group_id":"3","hint":"客户星级提示","field_type":"5","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":0,"format":"0"},{"id":"group_id","name":"分组","base":"1","require":"0","group_id":"3","hint":"分组提示","field_type":"3","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":0,"format":"0"},{"id":"cus_tag","name":"客户标签","base":"1","require":"0","group_id":"3","hint":"客户标签提示","field_type":"7","ext_info":[],"default":"[]","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":[],"format":"[]"},{"id":"trail_status","name":"客户阶段","base":"1","require":"0","group_id":"3","hint":"客户状态提示","field_type":"3","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":0,"format":"0"},{"id":"pool_id","name":"公海分组","base":"1","require":"1","group_id":"3","hint":"公海分组提示","field_type":"3","ext_info":[""],"default":"0","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":0,"format":"公共公海分组"}]},{"group_id":4,"name":"联系信息","fields":[{"id":"lonlat","name":"经维度坐标","base":"1","require":"0","group_id":"4","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","value":{"longitude":"0.000000","latitude":"0.000000"},"format":"{\"longitude\":\"0.000000\",\"latitude\":\"0.000000\"}"},{"id":"tel","name":"座机","base":"1","require":"0","group_id":"4","hint":"座机提示","field_type":"1","ext_info":[],"default":"{\"tel_area_code\":\"\",\"tel\":\"\"}","disable_flag":"0","is_editable":1,"unique_check":"1","unique_prevent":"1","unique_message":"","value":{"tel_area_code":"","tel":""},"format":"{\"tel_area_code\":\"\",\"tel\":\"\"}"},{"id":"homepage","name":"公司网址","base":"1","require":"0","group_id":"4","hint":"主页提示","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"1","unique_prevent":"1","unique_message":"","value":"","format":""},{"id":"address","name":"联系地址","base":"1","require":"0","group_id":"4","hint":"联系地址提示","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"1","unique_prevent":"2","unique_message":"联系地址已存在","value":"","format":""},{"id":"fax","name":"传真","base":"1","require":"0","group_id":"4","hint":"传真提示","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"1","unique_prevent":"2","unique_message":"已有相同的传真","value":"","format":""}]},{"group_id":5,"name":"其他信息","fields":[{"id":"image_list","name":"图片","base":"1","require":"0","group_id":"5","hint":" ","field_type":"6","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":[],"format":"[]"},{"id":"remark","name":"备注","base":"1","require":"0","group_id":"5","hint":"","field_type":"2","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"","format":""},{"id":"1102352738","name":"客户下拉多选","base":"0","require":"0","group_id":"5","hint":"","field_type":"7","ext_info":["周黑鸭"],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"","format":""},{"id":"1102603643","name":"客户-支付信息","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"","format":""},{"id":"1105128163","name":"客户自定义下拉单选","base":"0","require":"0","group_id":"5","hint":"","field_type":"3","ext_info":["客户1","客户2","客户3"],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"","format":""},{"id":"1105162749","name":"客户地址","base":"0","require":"0","group_id":"5","hint":"","field_type":"2","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"1","unique_prevent":"1","unique_message":"客户地址已存在哟～～～","value":"","format":""},{"id":"1101101837","name":"自定义下拉单选","base":"0","require":"0","group_id":"5","hint":"","field_type":"3","ext_info":["客户1","彩虹","不柒"],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"","format":""},{"id":"1101101838","name":"自定义日期","base":"0","require":"0","group_id":"5","hint":"","field_type":"4","ext_info":[],"default":"2020-03-12","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"","format":""},{"id":"1101101839","name":"自定义数值","base":"0","require":"0","group_id":"5","hint":"请输入数值","field_type":"5","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"","format":""},{"id":"1105128171","name":"客户自定义日期","base":"0","require":"0","group_id":"5","hint":"","field_type":"4","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"","format":""},{"id":"1101101840","name":"自定义下拉多选","base":"0","require":"0","group_id":"5","hint":"请选择下拉多选","field_type":"7","ext_info":["客户多选2","客户多选3"],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"","format":""},{"id":"next_follow_up_time","name":"下次跟进时间","base":"1","require":"0","group_id":"5","hint":"","field_type":"10","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"","format":""}]}],"customers":[{"customer_id":0,"company_id":0,"email":"","name":"","main_customer_flag":0,"fields":[{"id":"main_customer_flag","name":"主要联系人","base":"1","require":"0","hint":"","field_type":"3","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":1,"group_id":0},{"id":"name","name":"昵称","base":"1","require":"0","hint":"","field_type":"1","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"当时的","group_id":0},{"id":"email","name":"邮箱","base":"1","require":"0","hint":"","field_type":"1","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"1","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"remark","name":"备注","base":"1","require":"0","hint":"","field_type":"2","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"post_grade","name":"职级","base":"1","require":"0","hint":"","field_type":"3","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"birth","name":"生日","base":"1","require":"0","hint":"","field_type":"4","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"post","name":"职位","base":"1","require":"0","hint":"","field_type":"1","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"tel_list","name":"联系电话","base":"1","require":"0","hint":"联系电话提示","field_type":"1","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"1","unique_prevent":"2","unique_message":"","value":[],"group_id":0},{"id":"contact","name":"社交平台","base":"1","require":"0","hint":"","field_type":"2","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"1","unique_prevent":"2","unique_message":"已存在相同的社交平台","value":[],"group_id":0},{"id":"image_list","name":"图片","base":"1","require":"0","hint":" ","field_type":"6","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":[],"group_id":0},{"id":"gender","name":"性别","base":"1","require":"0","hint":"","field_type":"3","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"1105746753","name":"旺旺号","base":"0","require":"0","hint":"","field_type":"1","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"1","unique_prevent":"1","unique_message":"存在重复的旺旺号","value":"","group_id":0},{"id":"1102611672","name":"自定义联系人下拉多选","base":"0","require":"0","hint":"","field_type":"7","ext_info":["小美","小梅","小妹"],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"1102611671","name":"自定义联系人下拉单选","base":"0","require":"0","hint":"","field_type":"3","ext_info":["剪刀","菜刀","尖刀"],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"1102611669","name":"自定义联系人文本","base":"0","require":"0","hint":"","field_type":"1","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"1","unique_prevent":"2","unique_message":"联系人相同啦","value":"","group_id":0},{"id":"1102611670","name":"自定义联系多行文本","base":"0","require":"0","hint":"","field_type":"2","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"1102611675","name":"自定义联系人数值","base":"0","require":"0","hint":"","field_type":"5","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0}]}]}
       ', true);
        $params = [
            'company_id' => $companyId,
            'data'       => json_encode($data),
        ];

        $this->enableProfile();
        $saveCompany = $this->callAction('saveCompanyData', $params);
        $this->responseOk();
    }


	public function testSaveCompanyData() {

		$this->loginUser('46900');
        
        $data = '{"company":[{"group_id":1,"name":"基本信息","fields":[{"id":"short_name","name":"简称","base":"1","require":"0","group_id":"1","hint":"我是简称提示语","field_type":"1","ext_info":[""],"default":"","disable_flag":"0","is_editable":1,"unique_check":"1","unique_prevent":"2","unique_message":"简称不允许重复","type":"4","value":"","format":""},{"id":"serial_id","name":"客户编号","base":"1","require":"0","group_id":"1","hint":"这是客户编号哦","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":0,"unique_check":"1","unique_prevent":"1","unique_message":"","type":"4","value":"西西西西20230612西西2906701233","format":"西西西西20230612西西2906701233"},{"id":"name","name":"公司名称","base":"1","require":"1","group_id":"1","hint":"我是公司名称提示语","field_type":"1","ext_info":[""],"default":"","disable_flag":"0","is_editable":1,"unique_check":"1","unique_prevent":"1","unique_message":"公司名称","type":"4","value":"上海星谷信息科技有限公司","format":"上海星谷信息科技有限公司"},{"id":"1887817526","name":"ccccc","base":"0","require":"0","group_id":"1","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"1","unique_prevent":"2","unique_message":"hahaha","type":"4","value":"","format":""},{"id":"origin","name":"客户来源","base":"1","require":"1","group_id":"1","hint":"123124","field_type":"7","ext_info":[""],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":4,"format":"4"}]},{"group_id":2,"name":"特征信息","fields":[{"id":"timezone","name":"时区","base":"1","require":"0","group_id":"2","hint":"","field_type":"3","ext_info":[""],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"city","name":"城市","base":"1","require":"0","group_id":"2","hint":"","field_type":"3","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"country","name":"国家地区","base":"1","require":"0","group_id":"2","hint":"","field_type":"3","ext_info":[""],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"province","name":"省份","base":"1","require":"0","group_id":"2","hint":"","field_type":"3","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"category_ids","name":"主营产品","base":"1","require":"0","group_id":"2","hint":"","field_type":"7","ext_info":[""],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":[],"format":"[]"},{"id":"3043781064","name":"测试公司-tv-多行文本","base":"0","require":"0","group_id":"2","hint":"请输入","field_type":"2","ext_info":[],"default":"123","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":123,"format":"123"},{"id":"scale_id","name":"规模","base":"1","require":"0","group_id":"2","hint":"","field_type":"3","ext_info":[""],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":2,"format":"2"},{"id":"intention_level","name":"采购意向","base":"1","require":"0","group_id":"2","hint":"","field_type":"3","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":0,"format":"未知"},{"id":"annual_procurement","name":"年采购额","base":"1","require":"0","group_id":"2","hint":"","field_type":"3","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":0,"format":"无采购额"},{"id":"biz_type","name":"客户类型","base":"1","require":"0","group_id":"2","hint":"","field_type":"3","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"1119201714","name":"公司信息-客户管理自定义","base":"0","require":"0","group_id":"2","hint":"这是自定义哦~","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"1","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3266094846","name":"客户自定义字段","base":"0","require":"0","group_id":"2","hint":"2222","field_type":"2","ext_info":[],"default":"22","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":22,"format":"22"}]},{"group_id":3,"name":"管理信息","fields":[{"id":"group_id","name":"分组","base":"1","require":"0","group_id":"3","hint":"21212","field_type":"3","ext_info":[""],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":0,"format":"0"},{"id":"product_group_ids","name":"产品分组","base":"1","require":"0","group_id":"3","hint":"","field_type":"7","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":[],"format":"[]"},{"id":"trail_status","name":"客户阶段","base":"1","require":"0","group_id":"3","hint":"我是提示语122","field_type":"3","ext_info":[""],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":0,"format":"0"},{"id":"cus_tag","name":"客户标签","base":"1","require":"0","group_id":"3","hint":"我是客户标签提示语","field_type":"7","ext_info":[""],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":[],"format":"[]"},{"id":"pool_id","name":"公海分组","base":"1","require":"1","group_id":"3","hint":"","field_type":"3","ext_info":[""],"default":"0","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":38066098,"format":"ALI"},{"id":"star","name":"客户星级","base":"1","require":"0","group_id":"3","hint":"","field_type":"5","ext_info":[""],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":0,"format":"0"}]},{"group_id":4,"name":"联系信息","fields":[{"id":"lonlat","name":"经维度坐标","base":"1","require":"0","group_id":"4","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":{"longitude":"0.000000","latitude":"0.000000"},"format":"{\"longitude\":\"0.000000\",\"latitude\":\"0.000000\"}"},{"id":"1106146700","name":"sharon测试备注","base":"0","require":"0","group_id":"4","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"1","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"tel","name":"座机","base":"1","require":"0","group_id":"4","hint":"我是座机呀","field_type":"1","ext_info":[""],"default":"","disable_flag":"0","is_editable":1,"unique_check":"1","unique_prevent":"2","unique_message":"","type":"4","value":{"tel_area_code":"213","tel":"02160314188"},"format":"{\"tel_area_code\":\"213\",\"tel\":\"02160314188\"}"},{"id":"fax","name":"传真","base":"1","require":"0","group_id":"4","hint":"","field_type":"1","ext_info":[""],"default":"","disable_flag":"0","is_editable":1,"unique_check":"1","unique_prevent":"2","unique_message":"传真不允许重复","type":"4","value":"738838","format":"738838"},{"id":"address","name":"详细地址","base":"1","require":"0","group_id":"4","hint":"000","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"1","unique_prevent":"2","unique_message":"","type":"4","value":"中国上海市浦东新区浦建路145号强生大厦24楼345356346","format":"中国上海市浦东新区浦建路145号强生大厦24楼345356346"},{"id":"homepage","name":"公司网址","base":"1","require":"0","group_id":"4","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"1","unique_prevent":"1","unique_message":"","type":"4","value":"www.91syun.com45645456","format":"www.91syun.com45645456"},{"id":"3215316242","name":"导出日期字段测试","base":"0","require":"0","group_id":"4","hint":"","field_type":"4","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""}]},{"group_id":5,"name":"其他信息","fields":[{"id":"1118472445","name":"字段名称 - os001","base":"0","require":"0","group_id":"5","hint":"","field_type":"3","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"1106335125","name":"自定义字段-数值","base":"0","require":"0","group_id":"5","hint":"","field_type":"5","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"1118472443","name":"os - 2","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"1119201713","name":"公司信息--线索自定义字段","base":"0","require":"0","group_id":"5","hint":"","field_type":"3","ext_info":["1"],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"1127474852","name":"自定义字段-日期","base":"0","require":"0","group_id":"5","hint":"","field_type":"4","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"1139914768","name":"多选客户字段","base":"0","require":"0","group_id":"5","hint":"","field_type":"7","ext_info":["a","b","v","c","q","g","hj"],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"1118472446","name":"os - 字段 - 001 - 0","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"image_list","name":"公司logo","base":"1","require":"0","group_id":"5","hint":" ","field_type":"6","ext_info":[""],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":[],"format":"[]"},{"id":"3025700946","name":"客户老编号","base":"0","require":"0","group_id":"5","hint":"我是提示语","field_type":"1","ext_info":[],"default":"我是默认值","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"我是默认值","format":"我是默认值"},{"id":"3136809397","name":"多行文本test","base":"0","require":"0","group_id":"5","hint":"","field_type":"2","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3082106510","name":"测试公司-tv-文本","base":"0","require":"0","group_id":"5","hint":"这个文本要写了啊","field_type":"1","ext_info":[],"default":"我是必填文本默认值","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"这个重复了","type":"4","value":"我是必填文本默认值","format":"我是必填文本默认值"},{"id":"3082106514","name":"测试公司-tv-日期","base":"0","require":"0","group_id":"5","hint":"这个日期要写啊","field_type":"4","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3082106516","name":"测试公司-tv-数值","base":"0","require":"0","group_id":"5","hint":"这个数值要填写啊","field_type":"5","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3082106518","name":"测试公司-tv-日期时间","base":"0","require":"0","group_id":"5","hint":"这个时间要填啊","field_type":"10","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3082106517","name":"测试公司-tv-下拉多选","base":"0","require":"0","group_id":"5","hint":"这个多选要选啊","field_type":"7","ext_info":["去","我","呃","人","他"],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3113323380","name":"产品组单选","base":"0","require":"0","group_id":"5","hint":"","field_type":"3","ext_info":["单选2","单选1"],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3117881352","name":"询盘产品-测试工单","base":"0","require":"0","group_id":"5","hint":"","field_type":"7","ext_info":["洗衣片","吸色片","留香珠"],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3118670319","name":"xcvjkla","base":"0","require":"0","group_id":"5","hint":"","field_type":"2","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3136619212","name":"rayleitest","base":"0","require":"0","group_id":"5","hint":"","field_type":"10","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3136619217","name":"rayleitest2","base":"0","require":"0","group_id":"5","hint":"","field_type":"10","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3136693579","name":"test","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3136693584","name":"test1","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3236815431","name":"信息状态","base":"0","require":"0","group_id":"5","hint":"","field_type":"3","ext_info":["冲突","不冲突"],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3137336748","name":"新老客户类型","base":"0","require":"0","group_id":"5","hint":"","field_type":"7","ext_info":["新客户","老客户"],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3137337033","name":"单行文本测试","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3137337034","name":"多行文本测试","base":"0","require":"0","group_id":"5","hint":"","field_type":"2","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3145424470","name":"zxz定义多选","base":"0","require":"0","group_id":"5","hint":"","field_type":"7","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3145424869","name":"多行选择2","base":"0","require":"0","group_id":"5","hint":"","field_type":"7","ext_info":["阿萨德尬","3条图文"],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"remark","name":"公司备注","base":"1","require":"0","group_id":"5","hint":"啦啦啦","field_type":"2","ext_info":[],"default":"龙琴","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"test","format":"test"},{"id":"**********","name":"ollar Payment, the remittance instruction route:  *5","base":"0","require":"0","group_id":"5","hint":"","field_type":"7","ext_info":["IMEE GROUP LTD EURO Dollar Payment, the remittance instruction route:  *57A: Account with institutio","2"],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"**********","name":"321698","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"**********","name":"线索&客户-公司7-数值","base":"0","require":"0","group_id":"5","hint":"","field_type":"5","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"**********","name":"3333","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"**********","name":"1117test","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"1106325564","name":"线索&客户-公司6-日期+时间","base":"0","require":"0","group_id":"5","hint":"","field_type":"10","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3162339311","name":"孚盟客户编码","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"1106325536","name":"线索&客户-公司2-多行文本","base":"0","require":"0","group_id":"5","hint":"","field_type":"2","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"1102723524","name":"sharon测试iOS多行文本默认文案","base":"0","require":"0","group_id":"5","hint":"","field_type":"2","ext_info":[],"default":"iOS","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"iOS","format":"iOS"},{"id":"1106325563","name":"线索&客户-公司5-日期","base":"0","require":"0","group_id":"5","hint":"","field_type":"4","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"1106325554","name":"线索&客户-公司4-下拉多选","base":"0","require":"0","group_id":"5","hint":"","field_type":"7","ext_info":["1234567890","abcdefghijklmn","你是我的小丫小苹果"],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3043780965","name":"下拉多选","base":"0","require":"0","group_id":"5","hint":"","field_type":"7","ext_info":["test","苹果","雪梨","kevinAdd","yang","huangzhengbang"],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3082158045","name":"工单自定义多选字段","base":"0","require":"0","group_id":"5","hint":"","field_type":"7","ext_info":["测试1","测试2","测试3"],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"**********","name":"lora客户公司－多行文本","base":"0","require":"0","group_id":"5","hint":"","field_type":"2","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3082301486","name":"自定义日期","base":"0","require":"0","group_id":"5","hint":"","field_type":"4","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"1107572141","name":"线索自定义筛选字段","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3113338550","name":"产品文本","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"********","name":"sharon自定义字段下拉","base":"0","require":"0","group_id":"5","hint":"下啦提示","field_type":"3","ext_info":["琵琶","古琴-xxx"],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3113338556","name":"产品多行文本","base":"0","require":"0","group_id":"5","hint":"","field_type":"2","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"1100149746","name":"lora测试有数据的字段","base":"0","require":"0","group_id":"5","hint":"","field_type":"2","ext_info":[],"default":"有数据字段的一个默认值","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"2","unique_message":"lora测试有数据的字段内容重复啦～","type":"4","value":"","format":""},{"id":"3113338557","name":"产品日期","base":"0","require":"0","group_id":"5","hint":"","field_type":"4","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"1100286110","name":"OS - 重要测试字段，千万别动","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3113338576","name":"产品数值","base":"0","require":"0","group_id":"5","hint":"","field_type":"5","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"1101160789","name":"lora客户公司－数值","base":"0","require":"0","group_id":"5","hint":"","field_type":"5","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3113338577","name":"产品日期时间","base":"0","require":"0","group_id":"5","hint":"","field_type":"10","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3115017206","name":"测试字段","base":"0","require":"0","group_id":"5","hint":"","field_type":"3","ext_info":["专业进口商","专业进口商2","专业进口商3"],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3118759324","name":"1111","base":"0","require":"0","group_id":"5","hint":"","field_type":"3","ext_info":["bbb","aaa"],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"1118059887","name":"poi","base":"0","require":"0","group_id":"5","hint":"","field_type":"7","ext_info":["123","234"],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3118893863","name":"第一周","base":"0","require":"0","group_id":"5","hint":"","field_type":"2","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"1118426017","name":"单选字段","base":"0","require":"0","group_id":"5","hint":"","field_type":"3","ext_info":["单选1","单选2","单选3"],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3145130520","name":"zxz自定义公司信息","base":"0","require":"0","group_id":"5","hint":"","field_type":"3","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"1118472442","name":"新增一个字段 - os","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3159905480","name":"test124","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3159969328","name":"test客户-公司信息","base":"0","require":"0","group_id":"5","hint":"","field_type":"5","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"1118262834","name":"young-2333333","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3160001274","name":"test1014","base":"0","require":"0","group_id":"5","hint":"","field_type":"4","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"1118263027","name":"122","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3160007696","name":"33333","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"1118263031","name":"ProductList","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3160007723","name":"123123","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"1118263048","name":"333","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3160658226","name":"testfu","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3162556534","name":"客户分组","base":"0","require":"0","group_id":"5","hint":"","field_type":"3","ext_info":["分组1","分组2"],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"1118312205","name":"20210413-1","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3162558308","name":"产品分组-测试呀","base":"0","require":"0","group_id":"5","hint":"","field_type":"3","ext_info":["1111","2222","3333","goler"],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3162944320","name":"test2","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"1129001406","name":"年采购额1","base":"0","require":"0","group_id":"5","hint":"","field_type":"5","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3163468310","name":"123321","base":"0","require":"0","group_id":"5","hint":"","field_type":"7","ext_info":["123","321","111"],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3178748579","name":"test12","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"1971174486","name":"我是数字","base":"0","require":"0","group_id":"5","hint":"","field_type":"5","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3179264311","name":"test11","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"2408213670","name":"富通客户代码","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3179535831","name":"测试1234","base":"0","require":"0","group_id":"5","hint":"","field_type":"5","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3214850962","name":"tset22","base":"0","require":"0","group_id":"5","hint":"323","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3261059607","name":"测试bug公司信息","base":"0","require":"0","group_id":"5","hint":"","field_type":"3","ext_info":["测试一下"],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3262787186","name":"xiala123","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3262787206","name":"casdsadas","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3262788462","name":"xialatar1312","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3262797346","name":"发大水发大水","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"1111328455","name":"janetest1","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3262797350","name":"fasdfasd","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"1111328400","name":"janetest","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3262797372","name":"safasfasf","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3262799111","name":"asdfasdfasd","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3264103474","name":"test999999","base":"0","require":"0","group_id":"5","hint":"","field_type":"3","ext_info":["L1+","L3","L4"],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3265423862","name":"ceshi最后","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3265549956","name":"43212","base":"0","require":"0","group_id":"5","hint":"","field_type":"5","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3265565121","name":"客户管理公司刷新","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3265576728","name":"文本2","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3265577136","name":"文本6","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3265577221","name":"啊撒大声地","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3265577459","name":"sscCC","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3265991429","name":"不归谷火锅","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3266766831","name":"数字类型111","base":"0","require":"0","group_id":"5","hint":"","field_type":"5","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3266766881","name":"数值类111","base":"0","require":"0","group_id":"5","hint":"","field_type":"5","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3307965519","name":"test客户管理","base":"0","require":"0","group_id":"5","hint":"","field_type":"7","ext_info":["2","3","1"],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3309347019","name":"test线索客户","base":"0","require":"0","group_id":"5","hint":"","field_type":"3","ext_info":["111","222","333"],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3328689040","name":"小黑专用","base":"0","require":"0","group_id":"5","hint":"","field_type":"3","ext_info":["1","3"],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3337449046","name":"客户走查日期字段","base":"0","require":"0","group_id":"5","hint":"","field_type":"4","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"1106325537","name":"线索&客户-公司3-下拉单选","base":"0","require":"0","group_id":"5","hint":"","field_type":"3","ext_info":["23人","卫栖梧若"],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3262787165","name":"xiala","base":"0","require":"0","group_id":"5","hint":"","field_type":"2","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3262797282","name":"懂法守法","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3262797399","name":"dasfadsfadfaa","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3262848629","name":"xass","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3262848638","name":"jjjj","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3262873770","name":"dafafasd","base":"0","require":"0","group_id":"5","hint":"","field_type":"3","ext_info":["fhdfsdfsd","asdfadfdsa","asdfadsf"],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3262876014","name":"asdasd","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3262919215","name":"xialaceshi","base":"0","require":"0","group_id":"5","hint":"","field_type":"3","ext_info":["123","321"],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3262919271","name":"啊你为呀","base":"0","require":"0","group_id":"5","hint":"","field_type":"3","ext_info":["割发代首感受到","大沙发斯蒂芬"],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3262919286","name":"fadsfasdf","base":"0","require":"0","group_id":"5","hint":"","field_type":"3","ext_info":["asda","gafadsf","fdsgdsg"],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3264020859","name":"asdasd123","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3265564719","name":"刷新一下","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3266309331","name":"规划局国际化国际化","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3266328243","name":"amumu","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3266330401","name":"xdxddxd","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3266330543","name":"sdds","base":"0","require":"0","group_id":"5","hint":"","field_type":"10","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3266330788","name":"dfasfadsfas","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3266330791","name":"123214","base":"0","require":"0","group_id":"5","hint":"","field_type":"10","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3266330801","name":"fasdfdsadfasdf","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3266333403","name":"ds","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3266333453","name":"asdasdasd","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3266333614","name":"dsafasdfas","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3266333626","name":"sdfasdfasdf","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3266333658","name":"asdfasdfasf","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3266333673","name":"asdfasdfas","base":"0","require":"0","group_id":"5","hint":"","field_type":"2","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3266333739","name":"法撒旦发多少","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3266333748","name":"打算发多少","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3266421750","name":"1212","base":"0","require":"0","group_id":"5","hint":"","field_type":"2","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3266422273","name":"2323232","base":"0","require":"0","group_id":"5","hint":"","field_type":"4","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3266422444","name":"121232323","base":"0","require":"0","group_id":"5","hint":"","field_type":"4","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3266426846","name":"121222","base":"0","require":"0","group_id":"5","hint":"","field_type":"5","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3266426850","name":"frf","base":"0","require":"0","group_id":"5","hint":"","field_type":"7","ext_info":["2121"],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3266469371","name":"121","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3266469386","name":"1212erer","base":"0","require":"0","group_id":"5","hint":"","field_type":"2","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3266469446","name":"123vadsa","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3266469447","name":"f34qer","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3307965351","name":"test线索","base":"0","require":"0","group_id":"5","hint":"","field_type":"7","ext_info":["1","2","4"],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3371846369","name":"反而分","base":"0","require":"0","group_id":"5","hint":"","field_type":"10","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3375189528","name":"日期","base":"0","require":"0","group_id":"5","hint":"","field_type":"4","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3375191313","name":"下拉多选1","base":"0","require":"0","group_id":"5","hint":"","field_type":"7","ext_info":["1","2","3"],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3378197135","name":"客群多选","base":"0","require":"0","group_id":"5","hint":"","field_type":"7","ext_info":["多选1","多选2","多选3"],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3378289020","name":"客户分组自定义","base":"0","require":"0","group_id":"5","hint":"","field_type":"3","ext_info":["A","B","C"],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3378946287","name":"客户阶段","base":"0","require":"0","group_id":"5","hint":"","field_type":"3","ext_info":["1","2"],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3378961051","name":"unittest1686275917","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""}]}],"customers":[]}';

		$param = [
			'company_id'   => '3382895431',
//			'lead_id'      => '3236822289',
			'data'         => $data,
			'archive_flag' => 1,
			//			'company_hash_id' => '6473332c9c93e529fcfd493a44f336d0',
		];

		$this->callAction('SaveCompanyData', $param);
	}

    public function testSaveCompany()
    {


	    $data = '
{"company":[{"group_id":1,"name":"基本信息","fields":[{"id":"short_name","name":"简称","base":"1","require":"0","group_id":"1","hint":"我是简称提示语","field_type":"1","ext_info":[""],"default":"","disable_flag":"0","is_editable":1,"unique_check":"1","unique_prevent":"2","unique_message":"简称不允许重复","type":"4","value":"","format":""},{"id":"serial_id","name":"客户编号","base":"1","require":"0","group_id":"1","hint":"这是客户编号哦","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":0,"unique_check":"1","unique_prevent":"1","unique_message":"","type":"4","value":"西西西西20230612西西2906701233","format":"西西西西20230612西西2906701233"},{"id":"name","name":"公司名称","base":"1","require":"1","group_id":"1","hint":"我是公司名称提示语","field_type":"1","ext_info":[""],"default":"","disable_flag":"0","is_editable":1,"unique_check":"1","unique_prevent":"1","unique_message":"公司名称","type":"4","value":"上海星谷信息科技有限公司","format":"上海星谷信息科技有限公司"},{"id":"1887817526","name":"ccccc","base":"0","require":"0","group_id":"1","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"1","unique_prevent":"2","unique_message":"hahaha","type":"4","value":"","format":""},{"id":"origin","name":"客户来源","base":"1","require":"1","group_id":"1","hint":"123124","field_type":"7","ext_info":[""],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"4","format":"4"}]},{"group_id":2,"name":"特征信息","fields":[{"id":"timezone","name":"时区","base":"1","require":"0","group_id":"2","hint":"","field_type":"3","ext_info":[""],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"city","name":"城市","base":"1","require":"0","group_id":"2","hint":"","field_type":"3","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"country","name":"国家地区","base":"1","require":"0","group_id":"2","hint":"","field_type":"3","ext_info":[""],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"province","name":"省份","base":"1","require":"0","group_id":"2","hint":"","field_type":"3","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"category_ids","name":"主营产品","base":"1","require":"0","group_id":"2","hint":"","field_type":"7","ext_info":[""],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":[],"format":"[]"},{"id":"3043781064","name":"测试公司-tv-多行文本","base":"0","require":"0","group_id":"2","hint":"请输入","field_type":"2","ext_info":[],"default":"123","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":123,"format":"123"},{"id":"scale_id","name":"规模","base":"1","require":"0","group_id":"2","hint":"","field_type":"3","ext_info":[""],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":2,"format":"2"},{"id":"intention_level","name":"采购意向","base":"1","require":"0","group_id":"2","hint":"","field_type":"3","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":0,"format":"未知"},{"id":"annual_procurement","name":"年采购额","base":"1","require":"0","group_id":"2","hint":"","field_type":"3","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":0,"format":"无采购额"},{"id":"biz_type","name":"客户类型","base":"1","require":"0","group_id":"2","hint":"","field_type":"3","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"1119201714","name":"公司信息-客户管理自定义","base":"0","require":"0","group_id":"2","hint":"这是自定义哦~","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"1","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3266094846","name":"客户自定义字段","base":"0","require":"0","group_id":"2","hint":"2222","field_type":"2","ext_info":[],"default":"22","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":22,"format":"22"}]},{"group_id":3,"name":"管理信息","fields":[{"id":"group_id","name":"分组","base":"1","require":"0","group_id":"3","hint":"21212","field_type":"3","ext_info":[""],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":0,"format":"0"},{"id":"product_group_ids","name":"产品分组","base":"1","require":"0","group_id":"3","hint":"","field_type":"7","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":[],"format":"[]"},{"id":"trail_status","name":"客户阶段","base":"1","require":"0","group_id":"3","hint":"我是提示语122","field_type":"3","ext_info":[""],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":0,"format":"0"},{"id":"cus_tag","name":"客户标签","base":"1","require":"0","group_id":"3","hint":"我是客户标签提示语","field_type":"7","ext_info":[""],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":[],"format":"[]"},{"id":"pool_id","name":"公海分组","base":"1","require":"1","group_id":"3","hint":"","field_type":"3","ext_info":[""],"default":"0","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":38066098,"format":"ALI"},{"id":"star","name":"客户星级","base":"1","require":"0","group_id":"3","hint":"","field_type":"5","ext_info":[""],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":0,"format":"0"}]},{"group_id":4,"name":"联系信息","fields":[{"id":"lonlat","name":"经维度坐标","base":"1","require":"0","group_id":"4","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":{"longitude":"0.000000","latitude":"0.000000"},"format":"{\"longitude\":\"0.000000\",\"latitude\":\"0.000000\"}"},{"id":"1106146700","name":"sharon测试备注","base":"0","require":"0","group_id":"4","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"1","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"tel","name":"座机","base":"1","require":"0","group_id":"4","hint":"我是座机呀","field_type":"1","ext_info":[""],"default":"","disable_flag":"0","is_editable":1,"unique_check":"1","unique_prevent":"2","unique_message":"","type":"4","value":{"tel_area_code":"213","tel":"02160314188"},"format":"{\"tel_area_code\":\"213\",\"tel\":\"02160314188\"}"},{"id":"fax","name":"传真","base":"1","require":"0","group_id":"4","hint":"","field_type":"1","ext_info":[""],"default":"","disable_flag":"0","is_editable":1,"unique_check":"1","unique_prevent":"2","unique_message":"传真不允许重复","type":"4","value":"738838","format":"738838"},{"id":"address","name":"详细地址","base":"1","require":"0","group_id":"4","hint":"000","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"1","unique_prevent":"2","unique_message":"","type":"4","value":"中国上海市浦东新区浦建路145号强生大厦24楼345356346","format":"中国上海市浦东新区浦建路145号强生大厦24楼345356346"},{"id":"homepage","name":"公司网址","base":"1","require":"0","group_id":"4","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"1","unique_prevent":"1","unique_message":"","type":"4","value":"www.91syun.com45645456","format":"www.91syun.com45645456"},{"id":"3215316242","name":"导出日期字段测试","base":"0","require":"0","group_id":"4","hint":"","field_type":"4","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""}]},{"group_id":5,"name":"其他信息","fields":[{"id":"1118472445","name":"字段名称 - os001","base":"0","require":"0","group_id":"5","hint":"","field_type":"3","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"1106335125","name":"自定义字段-数值","base":"0","require":"0","group_id":"5","hint":"","field_type":"5","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"1118472443","name":"os - 2","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"1119201713","name":"公司信息--线索自定义字段","base":"0","require":"0","group_id":"5","hint":"","field_type":"3","ext_info":["1"],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"1127474852","name":"自定义字段-日期","base":"0","require":"0","group_id":"5","hint":"","field_type":"4","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"1139914768","name":"多选客户字段","base":"0","require":"0","group_id":"5","hint":"","field_type":"7","ext_info":["a","b","v","c","q","g","hj"],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"1118472446","name":"os - 字段 - 001 - 0","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"image_list","name":"公司logo","base":"1","require":"0","group_id":"5","hint":" ","field_type":"6","ext_info":[""],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":[],"format":"[]"},{"id":"3025700946","name":"客户老编号","base":"0","require":"0","group_id":"5","hint":"我是提示语","field_type":"1","ext_info":[],"default":"我是默认值","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"我是默认值","format":"我是默认值"},{"id":"3136809397","name":"多行文本test","base":"0","require":"0","group_id":"5","hint":"","field_type":"2","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3082106510","name":"测试公司-tv-文本","base":"0","require":"0","group_id":"5","hint":"这个文本要写了啊","field_type":"1","ext_info":[],"default":"我是必填文本默认值","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"这个重复了","type":"4","value":"我是必填文本默认值","format":"我是必填文本默认值"},{"id":"3082106514","name":"测试公司-tv-日期","base":"0","require":"0","group_id":"5","hint":"这个日期要写啊","field_type":"4","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3082106516","name":"测试公司-tv-数值","base":"0","require":"0","group_id":"5","hint":"这个数值要填写啊","field_type":"5","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3082106518","name":"测试公司-tv-日期时间","base":"0","require":"0","group_id":"5","hint":"这个时间要填啊","field_type":"10","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3082106517","name":"测试公司-tv-下拉多选","base":"0","require":"0","group_id":"5","hint":"这个多选要选啊","field_type":"7","ext_info":["去","我","呃","人","他"],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3113323380","name":"产品组单选","base":"0","require":"0","group_id":"5","hint":"","field_type":"3","ext_info":["单选2","单选1"],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3117881352","name":"询盘产品-测试工单","base":"0","require":"0","group_id":"5","hint":"","field_type":"7","ext_info":["洗衣片","吸色片","留香珠"],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3118670319","name":"xcvjkla","base":"0","require":"0","group_id":"5","hint":"","field_type":"2","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3136619212","name":"rayleitest","base":"0","require":"0","group_id":"5","hint":"","field_type":"10","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3136619217","name":"rayleitest2","base":"0","require":"0","group_id":"5","hint":"","field_type":"10","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3136693579","name":"test","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3136693584","name":"test1","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3236815431","name":"信息状态","base":"0","require":"0","group_id":"5","hint":"","field_type":"3","ext_info":["冲突","不冲突"],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3137336748","name":"新老客户类型","base":"0","require":"0","group_id":"5","hint":"","field_type":"7","ext_info":["新客户","老客户"],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3137337033","name":"单行文本测试","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3137337034","name":"多行文本测试","base":"0","require":"0","group_id":"5","hint":"","field_type":"2","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3145424470","name":"zxz定义多选","base":"0","require":"0","group_id":"5","hint":"","field_type":"7","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3145424869","name":"多行选择2","base":"0","require":"0","group_id":"5","hint":"","field_type":"7","ext_info":["阿萨德尬","3条图文"],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"remark","name":"公司备注","base":"1","require":"0","group_id":"5","hint":"啦啦啦","field_type":"2","ext_info":[],"default":"龙琴","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"test","format":"test"},{"id":"**********","name":"ollar Payment, the remittance instruction route:  *5","base":"0","require":"0","group_id":"5","hint":"","field_type":"7","ext_info":["IMEE GROUP LTD EURO Dollar Payment, the remittance instruction route:  *57A: Account with institutio","2"],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"**********","name":"321698","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"**********","name":"线索&客户-公司7-数值","base":"0","require":"0","group_id":"5","hint":"","field_type":"5","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"**********","name":"3333","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"**********","name":"1117test","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"1106325564","name":"线索&客户-公司6-日期+时间","base":"0","require":"0","group_id":"5","hint":"","field_type":"10","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3162339311","name":"孚盟客户编码","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"1106325536","name":"线索&客户-公司2-多行文本","base":"0","require":"0","group_id":"5","hint":"","field_type":"2","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"1102723524","name":"sharon测试iOS多行文本默认文案","base":"0","require":"0","group_id":"5","hint":"","field_type":"2","ext_info":[],"default":"iOS","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"iOS","format":"iOS"},{"id":"1106325563","name":"线索&客户-公司5-日期","base":"0","require":"0","group_id":"5","hint":"","field_type":"4","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"1106325554","name":"线索&客户-公司4-下拉多选","base":"0","require":"0","group_id":"5","hint":"","field_type":"7","ext_info":["1234567890","abcdefghijklmn","你是我的小丫小苹果"],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3043780965","name":"下拉多选","base":"0","require":"0","group_id":"5","hint":"","field_type":"7","ext_info":["test","苹果","雪梨","kevinAdd","yang","huangzhengbang"],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3082158045","name":"工单自定义多选字段","base":"0","require":"0","group_id":"5","hint":"","field_type":"7","ext_info":["测试1","测试2","测试3"],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"**********","name":"lora客户公司－多行文本","base":"0","require":"0","group_id":"5","hint":"","field_type":"2","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3082301486","name":"自定义日期","base":"0","require":"0","group_id":"5","hint":"","field_type":"4","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"1107572141","name":"线索自定义筛选字段","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3113338550","name":"产品文本","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"********","name":"sharon自定义字段下拉","base":"0","require":"0","group_id":"5","hint":"下啦提示","field_type":"3","ext_info":["琵琶","古琴-xxx"],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3113338556","name":"产品多行文本","base":"0","require":"0","group_id":"5","hint":"","field_type":"2","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"1100149746","name":"lora测试有数据的字段","base":"0","require":"0","group_id":"5","hint":"","field_type":"2","ext_info":[],"default":"有数据字段的一个默认值","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"2","unique_message":"lora测试有数据的字段内容重复啦～","type":"4","value":"","format":""},{"id":"3113338557","name":"产品日期","base":"0","require":"0","group_id":"5","hint":"","field_type":"4","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"1100286110","name":"OS - 重要测试字段，千万别动","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3113338576","name":"产品数值","base":"0","require":"0","group_id":"5","hint":"","field_type":"5","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"1101160789","name":"lora客户公司－数值","base":"0","require":"0","group_id":"5","hint":"","field_type":"5","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3113338577","name":"产品日期时间","base":"0","require":"0","group_id":"5","hint":"","field_type":"10","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3115017206","name":"测试字段","base":"0","require":"0","group_id":"5","hint":"","field_type":"3","ext_info":["专业进口商","专业进口商2","专业进口商3"],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3118759324","name":"1111","base":"0","require":"0","group_id":"5","hint":"","field_type":"3","ext_info":["bbb","aaa"],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"1118059887","name":"poi","base":"0","require":"0","group_id":"5","hint":"","field_type":"7","ext_info":["123","234"],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3118893863","name":"第一周","base":"0","require":"0","group_id":"5","hint":"","field_type":"2","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"1118426017","name":"单选字段","base":"0","require":"0","group_id":"5","hint":"","field_type":"3","ext_info":["单选1","单选2","单选3"],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3145130520","name":"zxz自定义公司信息","base":"0","require":"0","group_id":"5","hint":"","field_type":"3","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"1118472442","name":"新增一个字段 - os","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3159905480","name":"test124","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3159969328","name":"test客户-公司信息","base":"0","require":"0","group_id":"5","hint":"","field_type":"5","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"1118262834","name":"young-2333333","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3160001274","name":"test1014","base":"0","require":"0","group_id":"5","hint":"","field_type":"4","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"1118263027","name":"122","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3160007696","name":"33333","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"1118263031","name":"ProductList","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3160007723","name":"123123","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"1118263048","name":"333","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3160658226","name":"testfu","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3162556534","name":"客户分组","base":"0","require":"0","group_id":"5","hint":"","field_type":"3","ext_info":["分组1","分组2"],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"1118312205","name":"20210413-1","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3162558308","name":"产品分组-测试呀","base":"0","require":"0","group_id":"5","hint":"","field_type":"3","ext_info":["1111","2222","3333","goler"],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3162944320","name":"test2","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"1129001406","name":"年采购额1","base":"0","require":"0","group_id":"5","hint":"","field_type":"5","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3163468310","name":"123321","base":"0","require":"0","group_id":"5","hint":"","field_type":"7","ext_info":["123","321","111"],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3178748579","name":"test12","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"1971174486","name":"我是数字","base":"0","require":"0","group_id":"5","hint":"","field_type":"5","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3179264311","name":"test11","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"2408213670","name":"富通客户代码","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3179535831","name":"测试1234","base":"0","require":"0","group_id":"5","hint":"","field_type":"5","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3214850962","name":"tset22","base":"0","require":"0","group_id":"5","hint":"323","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3261059607","name":"测试bug公司信息","base":"0","require":"0","group_id":"5","hint":"","field_type":"3","ext_info":["测试一下"],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3262787186","name":"xiala123","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3262787206","name":"casdsadas","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3262788462","name":"xialatar1312","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3262797346","name":"发大水发大水","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"1111328455","name":"janetest1","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3262797350","name":"fasdfasd","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"1111328400","name":"janetest","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3262797372","name":"safasfasf","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3262799111","name":"asdfasdfasd","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3264103474","name":"test999999","base":"0","require":"0","group_id":"5","hint":"","field_type":"3","ext_info":["L1+","L3","L4"],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3265423862","name":"ceshi最后","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3265549956","name":"43212","base":"0","require":"0","group_id":"5","hint":"","field_type":"5","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3265565121","name":"客户管理公司刷新","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3265576728","name":"文本2","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3265577136","name":"文本6","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3265577221","name":"啊撒大声地","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3265577459","name":"sscCC","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3265991429","name":"不归谷火锅","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3266766831","name":"数字类型111","base":"0","require":"0","group_id":"5","hint":"","field_type":"5","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3266766881","name":"数值类111","base":"0","require":"0","group_id":"5","hint":"","field_type":"5","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3307965519","name":"test客户管理","base":"0","require":"0","group_id":"5","hint":"","field_type":"7","ext_info":["2","3","1"],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3309347019","name":"test线索客户","base":"0","require":"0","group_id":"5","hint":"","field_type":"3","ext_info":["111","222","333"],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3328689040","name":"小黑专用","base":"0","require":"0","group_id":"5","hint":"","field_type":"3","ext_info":["1","3"],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3337449046","name":"客户走查日期字段","base":"0","require":"0","group_id":"5","hint":"","field_type":"4","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"1106325537","name":"线索&客户-公司3-下拉单选","base":"0","require":"0","group_id":"5","hint":"","field_type":"3","ext_info":["23人","卫栖梧若"],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3262787165","name":"xiala","base":"0","require":"0","group_id":"5","hint":"","field_type":"2","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3262797282","name":"懂法守法","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3262797399","name":"dasfadsfadfaa","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3262848629","name":"xass","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3262848638","name":"jjjj","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3262873770","name":"dafafasd","base":"0","require":"0","group_id":"5","hint":"","field_type":"3","ext_info":["fhdfsdfsd","asdfadfdsa","asdfadsf"],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3262876014","name":"asdasd","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3262919215","name":"xialaceshi","base":"0","require":"0","group_id":"5","hint":"","field_type":"3","ext_info":["123","321"],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3262919271","name":"啊你为呀","base":"0","require":"0","group_id":"5","hint":"","field_type":"3","ext_info":["割发代首感受到","大沙发斯蒂芬"],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3262919286","name":"fadsfasdf","base":"0","require":"0","group_id":"5","hint":"","field_type":"3","ext_info":["asda","gafadsf","fdsgdsg"],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3264020859","name":"asdasd123","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3265564719","name":"刷新一下","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3266309331","name":"规划局国际化国际化","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3266328243","name":"amumu","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3266330401","name":"xdxddxd","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3266330543","name":"sdds","base":"0","require":"0","group_id":"5","hint":"","field_type":"10","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3266330788","name":"dfasfadsfas","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3266330791","name":"123214","base":"0","require":"0","group_id":"5","hint":"","field_type":"10","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3266330801","name":"fasdfdsadfasdf","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3266333403","name":"ds","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3266333453","name":"asdasdasd","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3266333614","name":"dsafasdfas","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3266333626","name":"sdfasdfasdf","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3266333658","name":"asdfasdfasf","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3266333673","name":"asdfasdfas","base":"0","require":"0","group_id":"5","hint":"","field_type":"2","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3266333739","name":"法撒旦发多少","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3266333748","name":"打算发多少","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3266421750","name":"1212","base":"0","require":"0","group_id":"5","hint":"","field_type":"2","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3266422273","name":"2323232","base":"0","require":"0","group_id":"5","hint":"","field_type":"4","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3266422444","name":"121232323","base":"0","require":"0","group_id":"5","hint":"","field_type":"4","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3266426846","name":"121222","base":"0","require":"0","group_id":"5","hint":"","field_type":"5","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3266426850","name":"frf","base":"0","require":"0","group_id":"5","hint":"","field_type":"7","ext_info":["2121"],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3266469371","name":"121","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3266469386","name":"1212erer","base":"0","require":"0","group_id":"5","hint":"","field_type":"2","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3266469446","name":"123vadsa","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3266469447","name":"f34qer","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3307965351","name":"test线索","base":"0","require":"0","group_id":"5","hint":"","field_type":"7","ext_info":["1","2","4"],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3371846369","name":"反而分","base":"0","require":"0","group_id":"5","hint":"","field_type":"10","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3375189528","name":"日期","base":"0","require":"0","group_id":"5","hint":"","field_type":"4","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3375191313","name":"下拉多选1","base":"0","require":"0","group_id":"5","hint":"","field_type":"7","ext_info":["1","2","3"],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3378197135","name":"客群多选","base":"0","require":"0","group_id":"5","hint":"","field_type":"7","ext_info":["多选1","多选2","多选3"],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3378289020","name":"客户分组自定义","base":"0","require":"0","group_id":"5","hint":"","field_type":"3","ext_info":["A","B","C"],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3378946287","name":"客户阶段","base":"0","require":"0","group_id":"5","hint":"","field_type":"3","ext_info":["1","2"],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"3378961051","name":"unittest1686275917","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""}]}],"customers":[]}
	    ';

	    $params = [
//		    'company_id'   => ,
		    'data'         => $data,
		    'check_public' => 1,
		    'archive_flag' => 1,
	    ];

		$this->callAction('saveCompanyData', $params);

	    $this->responseOk();
		exit;


//        static::loginUser(11850882);
//        static::loginUser(11855392);
//        $this->loginAsJocelyn();
//        $company = CompanyModel::model()->findByAttributes(['client_id' => static::$clientId, 'user_id' => '{' . static::$userId . '}', 'is_archive' => 1]);
//        $companyId = $company->company_id;
//        $company = new \common\library\customer_v3\company\orm\Company(static::$clientId, $companyId);
//        $company->getFormatter()->detailInfoSetting();
//        $companyData = $company->getAttributes();
//        $data = [
//            'company'   => $companyData['company'],
//            'customers' => $companyData['customers'],
//        ];

        $this->loginAsLora();
        $companyId = 0;
        $this->loginAsQiao();
        $data = json_decode('
        {"company":[{"group_id":1,"name":"基本信息","fields":[{"id":"name","name":"公司名称","base":"1","require":"1","group_id":"1","hint":"请输入公司名称","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"1","unique_prevent":"1","unique_message":"","value":"kequn导入客户157","format":"kequn导入客户157"},{"id":"short_name","name":"简称","base":"1","require":"0","group_id":"1","hint":"简称提示","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"1","unique_prevent":"1","unique_message":"已有相同的公司简称","value":"","format":""},{"id":"serial_id","name":"客户编号","base":"1","require":"0","group_id":"1","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"1","unique_prevent":"1","unique_message":"","value":"9867","format":"9867"},{"id":"origin","name":"客户来源","base":"1","require":"0","group_id":"1","hint":"客户来源提示","field_type":"3","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":1101043664,"format":"1101043664"}]},{"group_id":2,"name":"特征信息","fields":[{"id":"1514096184","name":"锁定期","base":"0","require":"0","group_id":"2","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"180","format":"180"},{"id":"intention_level","name":"采购意向","base":"1","require":"0","group_id":"2","hint":"","field_type":"3","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":0,"format":"未知"},{"id":"annual_procurement","name":"年采购额","base":"1","require":"0","group_id":"2","hint":"","field_type":"3","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":6,"format":"5万～10万美元"},{"id":"biz_type","name":"客户类型","base":"1","require":"0","group_id":"2","hint":"客户类型提示","field_type":"3","ext_info":[{"zh-CN":"原材料供应商","zh-TW":"原材料供應商","en":"Raw Material Supplier","value":"原材料供应商","name":"原材料供应商","label":"原材料供应商"},{"zh-CN":"生产商","zh-TW":"生產商","en":"Producers","value":"生产商","name":"生产商","label":"生产商"},{"zh-CN":"加盟商","zh-TW":"加盟商","en":"Franchisee","value":"加盟商","name":"加盟商","label":"加盟商"},{"zh-CN":"渠道商","zh-TW":"渠道商","en":"Distributors","value":"渠道商","name":"渠道商","label":"渠道商"},{"zh-CN":"贸易商","zh-TW":"貿易商","en":"Trader","value":"贸易商","name":"贸易商","label":"贸易商"},{"zh-CN":"代理商","zh-TW":"代理商","en":"Agent","value":"代理商","name":"代理商","label":"代理商"},{"zh-CN":"批发商","zh-TW":"批發商","en":"Wholesaler","value":"批发商","name":"批发商","label":"批发商"},{"zh-CN":"分销商","zh-TW":"分銷商","en":"Distributor","value":"分销商","name":"分销商","label":"分销商"},{"zh-CN":"代销商","zh-TW":"代銷商","en":"Affiliate","value":"代销商","name":"代销商","label":"代销商"},{"zh-CN":"零售商","zh-TW":"零售商","en":"Retailer","value":"零售商","name":"零售商","label":"零售商"},{"zh-CN":"采购办事处","zh-TW":"採購辦事處","en":"Purchasing Office","value":"采购办事处","name":"采购办事处","label":"采购办事处"},{"zh-CN":"采购咨询公司","zh-TW":"採購諮詢公司","en":"Purchasing Consulting Company","value":"采购咨询公司","name":"采购咨询公司","label":"采购咨询公司"},{"zh-CN":"出口商","zh-TW":"出口商","en":"Exporter","value":"出口商","name":"出口商","label":"出口商"},{"zh-CN":"进口商","zh-TW":"進口商","en":"Importer","value":"进口商","name":"进口商","label":"进口商"},{"zh-CN":"个人消费者","zh-TW":"個人消費者","en":"Personal Consumer","value":"个人消费者","name":"个人消费者","label":"个人消费者"},{"zh-CN":"机构/团体消费者","zh-TW":"機構/團體消費者","en":"Institution/group consumer","value":"机构/团体消费者","name":"机构/团体消费者","label":"机构/团体消费者"},{"zh-CN":"工程商","zh-TW":"工程商","en":"Engineering","value":"工程商","name":"工程商","label":"工程商"},{"zh-CN":"其他","zh-TW":"其他","en":"Other","value":"其他","name":"其他","label":"其他"}],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"贸易商","format":"贸易商"},{"id":"category_ids","name":"主营产品","base":"1","require":"0","group_id":"2","hint":"主营产品提示","field_type":"7","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":[],"format":"[]"},{"id":"country","name":"国家地区","base":"1","require":"0","group_id":"2","hint":"国家地区提示","field_type":"3","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"","format":"","countryInfo":{"country":"","province":"","city":""}},{"id":"province","name":"省份","base":"1","require":"0","group_id":"2","hint":"身份提示","field_type":"3","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"","format":""},{"id":"city","name":"城市","base":"1","require":"0","group_id":"2","hint":"城市提示","field_type":"3","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"","format":""},{"id":"scale_id","name":"规模","base":"1","require":"0","group_id":"2","hint":"规模提示","field_type":"3","ext_info":[{"value":1,"name":"少于59人","en_name":"Less than 59 employees","label":"少于59人"},{"value":2,"name":"60-149人","en_name":"60-149 employees","label":"60-149人"},{"value":3,"name":"150-499人","en_name":"150-499 employees","label":"150-499人"},{"value":4,"name":"500-999人","en_name":"500-999 employees","label":"500-999人"},{"value":5,"name":"1000-4999人","en_name":"1000-4999 employees","label":"1000-4999人"},{"value":6,"name":"5000人以上","en_name":"More than 5000 employees","label":"5000人以上"}],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":0,"format":"0"},{"id":"1101101841","name":"自定义日期-时间","base":"0","require":"0","group_id":"2","hint":"自定义时间提示","field_type":"10","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"2022-01-27 15:11:15","format":"2022-01-27 15:11:15"},{"id":"timezone","name":"时区","base":"1","require":"0","group_id":"2","hint":"时区提示","field_type":"3","ext_info":[{"label":"零时区：伦敦","value":"0"},{"label":"东一区：罗马，巴黎","value":"1"},{"label":"东二区：雅典，以色列","value":"2"},{"label":"东三区：莫斯科，科威特","value":"3"},{"label":"东四区：喀布尔","value":"4"},{"label":"东五区：伊斯兰堡，卡拉奇","value":"5"},{"label":"东六区：阿拉木图，科伦坡","value":"6"},{"label":"东七区：曼谷，雅加达","value":"7"},{"label":"东八区：北京，香港，台湾","value":"8"},{"label":"东九区：东京","value":"9"},{"label":"东十区：悉尼","value":"10"},{"label":"东十一区：霍尼亚拉，马加丹","value":"11"},{"label":"东西十二区: 奥克兰","value":"12"},{"label":"西十一区：帕果帕果，阿洛菲","value":"-11"},{"label":"西十区：夏威夷","value":"-10"},{"label":"西九区：阿拉斯加","value":"-9"},{"label":"西八区：洛杉矶，旧金山","value":"-8"},{"label":"西七区：盐湖城、丹佛、凤凰城","value":"-7"},{"label":"西六区：芝加哥，休斯顿，亚特兰大","value":"-6"},{"label":"西五区：纽约，华盛顿，波士顿","value":"-5"},{"label":"西四区：加拿大，加拉加斯","value":"-4"},{"label":"西三区：巴西利亚","value":"-3"},{"label":"西二区：协调世界时","value":"-2"},{"label":"西一区：佛得角群岛","value":"-1"}],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"","format":""}]},{"group_id":3,"name":"管理信息","fields":[{"id":"star","name":"客户星级","base":"1","require":"1","group_id":"3","hint":"客户星级提示","field_type":"5","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":2,"format":"2"},{"id":"group_id","name":"分组","base":"1","require":"0","group_id":"3","hint":"分组提示","field_type":"3","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":1102214917,"format":"1102214917"},{"id":"cus_tag","name":"客户标签","base":"1","require":"0","group_id":"3","hint":"客户标签提示","field_type":"7","ext_info":[],"default":"[]","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":[],"format":"[]"},{"id":"trail_status","name":"客户阶段","base":"1","require":"0","group_id":"3","hint":"客户状态提示","field_type":"3","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"1101043656","format":"1101043656"},{"id":"pool_id","name":"公海分组","base":"1","require":"1","group_id":"3","hint":"公海分组提示","field_type":"3","ext_info":[""],"default":"0","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":1101108488,"format":"欧洲地区客户"}]},{"group_id":4,"name":"联系信息","fields":[{"id":"lonlat","name":"经维度坐标","base":"1","require":"0","group_id":"4","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","value":{"longitude":"0.000000","latitude":"0.000000"},"format":"{\"longitude\":\"0.000000\",\"latitude\":\"0.000000\"}"},{"id":"tel","name":"座机","base":"1","require":"0","group_id":"4","hint":"座机提示","field_type":"1","ext_info":[],"default":"{\"tel_area_code\":\"\",\"tel\":\"\"}","disable_flag":"0","is_editable":1,"unique_check":"1","unique_prevent":"1","unique_message":"","value":{"tel_area_code":"","tel":"18872930150"},"format":"{\"tel_area_code\":\"\",\"tel\":\"18872930150\"}"},{"id":"homepage","name":"公司网址","base":"1","require":"0","group_id":"4","hint":"主页提示","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"1","unique_prevent":"1","unique_message":"","value":"www.baidu157.com","format":"www.baidu157.com"},{"id":"address","name":"联系地址","base":"1","require":"0","group_id":"4","hint":"联系地址提示","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"1","unique_prevent":"2","unique_message":"联系地址已存在","value":"福田","format":"福田"},{"id":"fax","name":"传真","base":"1","require":"0","group_id":"4","hint":"传真提示","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"1","unique_prevent":"2","unique_message":"已有相同的传真","value":"************","format":"************"}]},{"group_id":5,"name":"其他信息","fields":[{"id":"image_list","name":"图片","base":"1","require":"0","group_id":"5","hint":" ","field_type":"6","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":[],"format":"[]"},{"id":"remark","name":"备注","base":"1","require":"0","group_id":"5","hint":"","field_type":"2","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"工作流备注","format":"工作流备注"},{"id":"1102352738","name":"客户下拉多选","base":"0","require":"0","group_id":"5","hint":"","field_type":"7","ext_info":["周黑鸭"],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":["周黑鸭"],"format":"[\"周黑鸭\"]"},{"id":"1102603643","name":"客户-支付信息","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"","format":""},{"id":"1105128163","name":"客户自定义下拉单选","base":"0","require":"0","group_id":"5","hint":"","field_type":"3","ext_info":["客户1","客户2","客户3"],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"客户2","format":"客户2"},{"id":"1105162749","name":"客户地址","base":"0","require":"0","group_id":"5","hint":"","field_type":"2","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"1","unique_prevent":"1","unique_message":"客户地址已存在哟～～～","value":"","format":""},{"id":"1101101837","name":"自定义下拉单选","base":"0","require":"0","group_id":"5","hint":"","field_type":"3","ext_info":["客户1","彩虹","不柒"],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"彩虹","format":"彩虹"},{"id":"1101101838","name":"自定义日期","base":"0","require":"0","group_id":"5","hint":"","field_type":"4","ext_info":[],"default":"2020-03-12","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"2022-01-12","format":"2022-01-12"},{"id":"1101101839","name":"自定义数值","base":"0","require":"0","group_id":"5","hint":"请输入数值","field_type":"5","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":300,"format":"300"},{"id":"1105128171","name":"客户自定义日期","base":"0","require":"0","group_id":"5","hint":"","field_type":"4","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"","format":""},{"id":"1101101840","name":"自定义下拉多选","base":"0","require":"0","group_id":"5","hint":"请选择下拉多选","field_type":"7","ext_info":["客户多选2","客户多选3"],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":["客户多选2","客户多选3"],"format":"[\"客户多选2\",\"客户多选3\"]"},{"id":"next_follow_up_time","name":"下次跟进时间","base":"1","require":"0","group_id":"5","hint":"","field_type":"10","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"2022-01-08 15:10:47","format":"2022-01-08 15:10:47"}]}],"customers":[{"customer_id":1711250750,"company_id":1711250751,"name":"","email":"<EMAIL>","main_customer_flag":1,"fields":[{"id":"main_customer_flag","name":"主要联系人","base":"1","require":"0","hint":"","field_type":"3","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":1,"group_id":0,"field_customer_id":1711250750,"format":"1"},{"id":"name","name":"昵称","base":"1","require":"0","hint":"","field_type":"1","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0,"field_customer_id":1711250750,"format":""},{"id":"email","name":"邮箱","base":"1","require":"0","hint":"","field_type":"1","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"1","unique_prevent":"1","unique_message":"","value":"<EMAIL>","group_id":0,"field_customer_id":1711250750,"format":"<EMAIL>"},{"id":"remark","name":"备注","base":"1","require":"0","hint":"","field_type":"2","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0,"field_customer_id":1711250750,"format":""},{"id":"post_grade","name":"职级","base":"1","require":"0","hint":"","field_type":"3","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":0,"group_id":0,"field_customer_id":1711250750,"format":"0"},{"id":"birth","name":"生日","base":"1","require":"0","hint":"","field_type":"4","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0,"field_customer_id":1711250750,"format":""},{"id":"post","name":"职位","base":"1","require":"0","hint":"","field_type":"1","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0,"field_customer_id":1711250750,"format":""},{"id":"tel_list","name":"联系电话","base":"1","require":"0","hint":"联系电话提示","field_type":"1","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"1","unique_prevent":"2","unique_message":"","value":[],"group_id":0,"field_customer_id":1711250750,"format":"[]"},{"id":"contact","name":"社交平台","base":"1","require":"0","hint":"","field_type":"2","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"1","unique_prevent":"2","unique_message":"已存在相同的社交平台","value":[],"group_id":0,"field_customer_id":1711250750,"format":"[]"},{"id":"image_list","name":"图片","base":"1","require":"0","hint":" ","field_type":"6","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":[],"group_id":0,"field_customer_id":1711250750,"format":"[]"},{"id":"gender","name":"性别","base":"1","require":"0","hint":"","field_type":"3","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":0,"group_id":0,"field_customer_id":1711250750,"format":"0"},{"id":"1105746753","name":"旺旺号","base":"0","require":"0","hint":"","field_type":"1","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"1","unique_prevent":"1","unique_message":"存在重复的旺旺号","value":"","group_id":0,"field_customer_id":1711250750,"format":""},{"id":"1102611672","name":"自定义联系人下拉多选","base":"0","require":"0","hint":"","field_type":"7","ext_info":["小美","小梅","小妹"],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0,"field_customer_id":1711250750,"format":""},{"id":"1102611671","name":"自定义联系人下拉单选","base":"0","require":"0","hint":"","field_type":"3","ext_info":["剪刀","菜刀","尖刀"],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0,"field_customer_id":1711250750,"format":""},{"id":"1102611669","name":"自定义联系人文本","base":"0","require":"0","hint":"","field_type":"1","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"1","unique_prevent":"2","unique_message":"联系人相同啦","value":"","group_id":0,"field_customer_id":1711250750,"format":""},{"id":"1102611670","name":"自定义联系多行文本","base":"0","require":"0","hint":"","field_type":"2","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0,"field_customer_id":1711250750,"format":""},{"id":"1102611675","name":"自定义联系人数值","base":"0","require":"0","hint":"","field_type":"5","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0,"field_customer_id":1711250750,"format":""}]}]}
       ', true);

        array_walk($data['company'], function (&$groupFields) {
            array_walk($groupFields['fields'], function (&$field) {
                if ($field['id'] == 'name') {
                    $field['value'] = $field['value'] . time();
                }
                if ($field['id'] == 'short_name') {
                    $field['value'] = 'Unique Company Name'. time();
                }
                if ($field['id'] == 'address') {
                    $field['value'] = $this->faker()->streetName;
                }
                if ($field['id'] == 'homepage') {
                    $field['value'] = 'www.' . time() . '.com';
                }
                if ($field['id'] == 'remark') {
                    $field['value'] = $this->faker()->streetName;
                }
                if ($field['id'] == 'tel') {
                    $field['value']['tel_area_code'] = $this->faker()->numberBetween(1, 300);
                    $field['value']['tel'] = $this->faker()->numberBetween(10000000, 99999999);
                }
            });
        });

//        $sql = "update tbl_custom_field set unique_check = :unique_check, unique_prevent=1 where client_id=:client_id and id=:field_id and type=:type";
//        $sqlParams = [
//            ':unique_check' => 1,
//            ':type' => Constants::TYPE_COMPANY,
//            ':client_id' => static::$clientId,
//            ':field_id' => 'contact',
//        ];
//        ProjectActiveRecord::getDbByClientId(static::$clientId)->createCommand($sql)->execute($sqlParams);
        $data = json_decode('{"company":[{"group_id":1,"name":"基本信息","fields":[{"id":"serial_id","name":"客户编号","base":"1","require":"0","group_id":"1","hint":"","field_type":"5","ext_info":[],"default":"","disable_flag":"0","is_editable":0,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"142","format":"142"},{"id":"name","name":"公司名称","base":"1","require":"1","group_id":"1","hint":"请输入公司名称","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"客户编号只读哟","format":"客户编号只读哟"},{"id":"short_name","name":"简称","base":"1","require":"0","group_id":"1","hint":"简称提示","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"","format":""},{"id":"origin","name":"客户来源","base":"1","require":"1","group_id":"1","hint":"客户来源提示","field_type":"3","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":1101043664,"format":"1101043664"}]},{"group_id":2,"name":"特征信息","fields":[{"id":"biz_type","name":"客户类型","base":"1","require":"0","group_id":"2","hint":"客户类型提示","field_type":"3","ext_info":[{"zh-CN":"原材料供应商","zh-TW":"原材料供應商","en":"Raw Material Supplier","value":"原材料供应商","name":"原材料供应商","label":"原材料供应商"},{"zh-CN":"生产商","zh-TW":"生產商","en":"Producers","value":"生产商","name":"生产商","label":"生产商"},{"zh-CN":"加盟商","zh-TW":"加盟商","en":"Franchisee","value":"加盟商","name":"加盟商","label":"加盟商"},{"zh-CN":"渠道商","zh-TW":"渠道商","en":"Distributors","value":"渠道商","name":"渠道商","label":"渠道商"},{"zh-CN":"贸易商","zh-TW":"貿易商","en":"Trader","value":"贸易商","name":"贸易商","label":"贸易商"},{"zh-CN":"代理商","zh-TW":"代理商","en":"Agent","value":"代理商","name":"代理商","label":"代理商"},{"zh-CN":"批发商","zh-TW":"批發商","en":"Wholesaler","value":"批发商","name":"批发商","label":"批发商"},{"zh-CN":"分销商","zh-TW":"分銷商","en":"Distributor","value":"分销商","name":"分销商","label":"分销商"},{"zh-CN":"代销商","zh-TW":"代銷商","en":"Affiliate","value":"代销商","name":"代销商","label":"代销商"},{"zh-CN":"零售商","zh-TW":"零售商","en":"Retailer","value":"零售商","name":"零售商","label":"零售商"},{"zh-CN":"采购办事处","zh-TW":"採購辦事處","en":"Purchasing Office","value":"采购办事处","name":"采购办事处","label":"采购办事处"},{"zh-CN":"采购咨询公司","zh-TW":"採購諮詢公司","en":"Purchasing Consulting Company","value":"采购咨询公司","name":"采购咨询公司","label":"采购咨询公司"},{"zh-CN":"出口商","zh-TW":"出口商","en":"Exporter","value":"出口商","name":"出口商","label":"出口商"},{"zh-CN":"进口商","zh-TW":"進口商","en":"Importer","value":"进口商","name":"进口商","label":"进口商"},{"zh-CN":"个人消费者","zh-TW":"個人消費者","en":"Personal Consumer","value":"个人消费者","name":"个人消费者","label":"个人消费者"},{"zh-CN":"机构/团体消费者","zh-TW":"機構/團體消費者","en":"Institution/group consumer","value":"机构/团体消费者","name":"机构/团体消费者","label":"机构/团体消费者"},{"zh-CN":"工程商","zh-TW":"工程商","en":"Engineering","value":"工程商","name":"工程商","label":"工程商"},{"zh-CN":"其他","zh-TW":"其他","en":"Other","value":"其他","name":"其他","label":"其他"}],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"","format":""},{"id":"category_ids","name":"主营产品","base":"1","require":"0","group_id":"2","hint":"主营产品提示","field_type":"7","ext_info":[],"default":"[]","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":[],"format":"[]"},{"id":"country","name":"国家地区","base":"1","require":"0","group_id":"2","hint":"国家地区提示","field_type":"3","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"","format":"","countryInfo":{"country":"","province":"","city":""}},{"id":"province","name":"省份","base":"1","require":"0","group_id":"2","hint":"身份提示","field_type":"3","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"","format":""},{"id":"timezone","name":"时区","base":"1","require":"0","group_id":"2","hint":"时区提示","field_type":"3","ext_info":[{"label":"零时区：伦敦","value":"0"},{"label":"东一区：罗马，巴黎","value":"1"},{"label":"东二区：雅典，以色列","value":"2"},{"label":"东三区：莫斯科，科威特","value":"3"},{"label":"东四区：喀布尔","value":"4"},{"label":"东五区：伊斯兰堡，卡拉奇","value":"5"},{"label":"东六区：阿拉木图，科伦坡","value":"6"},{"label":"东七区：曼谷，雅加达","value":"7"},{"label":"东八区：北京，香港，台湾","value":"8"},{"label":"东九区：东京","value":"9"},{"label":"东十区：悉尼","value":"10"},{"label":"东十一区：霍尼亚拉，马加丹","value":"11"},{"label":"东西十二区: 奥克兰","value":"12"},{"label":"西十一区：帕果帕果，阿洛菲","value":"-11"},{"label":"西十区：夏威夷","value":"-10"},{"label":"西九区：阿拉斯加","value":"-9"},{"label":"西八区：洛杉矶，旧金山","value":"-8"},{"label":"西七区：盐湖城、丹佛、凤凰城","value":"-7"},{"label":"西六区：芝加哥，休斯顿，亚特兰大","value":"-6"},{"label":"西五区：纽约，华盛顿，波士顿","value":"-5"},{"label":"西四区：加拿大，加拉加斯","value":"-4"},{"label":"西三区：巴西利亚","value":"-3"},{"label":"西二区：协调世界时","value":"-2"},{"label":"西一区：佛得角群岛","value":"-1"}],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"","format":""},{"id":"city","name":"城市","base":"1","require":"0","group_id":"2","hint":"城市提示","field_type":"3","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"","format":""},{"id":"scale_id","name":"规模","base":"1","require":"0","group_id":"2","hint":"规模提示","field_type":"3","ext_info":[{"value":1,"name":"少于59人","en_name":"Less than 59 employees","label":"少于59人"},{"value":2,"name":"60-149人","en_name":"60-149 employees","label":"60-149人"},{"value":3,"name":"150-499人","en_name":"150-499 employees","label":"150-499人"},{"value":4,"name":"500-999人","en_name":"500-999 employees","label":"500-999人"},{"value":5,"name":"1000-4999人","en_name":"1000-4999 employees","label":"1000-4999人"},{"value":6,"name":"5000人以上","en_name":"More than 5000 employees","label":"5000人以上"}],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":0,"format":"0"},{"id":"1101101841","name":"自定义日期-时间","base":"0","require":"0","group_id":"2","hint":"自定义时间提示","field_type":"10","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"","format":""},{"id":"1101197498","name":"俄文","base":"0","require":"0","group_id":"2","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"","format":""}]},{"group_id":3,"name":"管理信息","fields":[{"id":"star","name":"客户星级","base":"1","require":"0","group_id":"3","hint":"客户星级提示","field_type":"5","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":0,"format":"0"},{"id":"group_id","name":"分组","base":"1","require":"0","group_id":"3","hint":"分组提示","field_type":"3","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":0,"format":"0"},{"id":"pool_id","name":"公海分组","base":"1","require":"1","group_id":"3","hint":"公海分组提示","field_type":"3","ext_info":[""],"default":"0","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":0,"format":"公共公海分组"},{"id":"cus_tag","name":"客户标签","base":"1","require":"0","group_id":"3","hint":"客户标签提示","field_type":"7","ext_info":[],"default":"[]","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":[],"format":"[]"},{"id":"trail_status","name":"客户状态","base":"1","require":"0","group_id":"3","hint":"客户状态提示","field_type":"3","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":0,"format":"0"}]},{"group_id":4,"name":"联系信息","fields":[{"id":"fax","name":"传真","base":"1","require":"0","group_id":"4","hint":"传真提示","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"","format":""},{"id":"tel","name":"座机","base":"1","require":"0","group_id":"4","hint":"座机提示","field_type":"1","ext_info":[],"default":"{\"tel_area_code\":\"\",\"tel\":\"\"}","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":{"tel_area_code":"","tel":""},"format":"{\"tel_area_code\":\"\",\"tel\":\"\"}"},{"id":"homepage","name":"主页","base":"1","require":"0","group_id":"4","hint":"主页提示","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"","format":""},{"id":"address","name":"联系地址","base":"1","require":"0","group_id":"4","hint":"联系地址提示","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"1","unique_prevent":"1","unique_message":"联系地址已存在","value":"","format":""},{"id":"lonlat","name":"经维度坐标","base":"1","require":"0","group_id":"4","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":{"longitude":"0.000000","latitude":"0.000000"},"format":"{\"longitude\":\"0.000000\",\"latitude\":\"0.000000\"}"}]},{"group_id":5,"name":"其他信息","fields":[{"id":"1101101835","name":"自定义文本","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"提子","format":""},{"id":"1101101836","name":"自定义多行文本","base":"0","require":"0","group_id":"5","hint":"","field_type":"2","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"","format":""},{"id":"1101101837","name":"自定义下拉单选","base":"0","require":"0","group_id":"5","hint":"","field_type":"3","ext_info":["客户1","客户3"],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"","format":""},{"id":"1101101838","name":"自定义日期","base":"0","require":"0","group_id":"5","hint":"","field_type":"4","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"","format":""},{"id":"1101101840","name":"自定义下拉多选","base":"0","require":"0","group_id":"5","hint":"","field_type":"7","ext_info":["客户多选2","客户多选3"],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"","format":""},{"id":"1101101839","name":"自定义数值","base":"0","require":"0","group_id":"5","hint":"","field_type":"5","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"","format":""},{"id":"image_list","name":"图片","base":"1","require":"0","group_id":"5","hint":" ","field_type":"6","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":[],"format":"[]"},{"id":"remark","name":"备注","base":"1","require":"0","group_id":"5","hint":"","field_type":"2","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"","format":""},{"id":"1102352738","name":"下拉多选","base":"0","require":"0","group_id":"5","hint":"","field_type":"7","ext_info":["咸水鸭","姜母鸭","周黑鸭"],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"","format":""}]}],"customers":[{"customer_id":1102358948,"company_id":1102358949,"name":"dudu","email":"<EMAIL>","main_customer_flag":1,"fields":[{"id":"main_customer_flag","name":"主要联系人","base":"1","require":"0","hint":"","field_type":"3","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":1,"group_id":0,"field_customer_id":1102358948,"format":"1"},{"id":"name","name":"昵称","base":"1","require":"0","hint":"","field_type":"1","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"dudu","group_id":0,"field_customer_id":1102358948,"format":"dudu"},{"id":"email","name":"邮箱","base":"1","require":"0","hint":"","field_type":"1","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"1","unique_prevent":"1","unique_message":"","value":"<EMAIL>","group_id":0,"field_customer_id":1102358948,"format":"<EMAIL>"},{"id":"remark","name":"备注","base":"1","require":"0","hint":"","field_type":"2","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0,"field_customer_id":1102358948,"format":""},{"id":"1101101835","name":"qqq","base":"0","require":"0","hint":"","field_type":"1","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"提子","group_id":0,"field_customer_id":1102358948,"format":""},{"id":"post_grade","name":"职级","base":"1","require":"0","hint":"","field_type":"3","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":0,"group_id":0,"field_customer_id":1102358948,"format":"0"},{"id":"birth","name":"生日","base":"1","require":"0","hint":"","field_type":"4","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0,"field_customer_id":1102358948,"format":""},{"id":"post","name":"职位","base":"1","require":"0","hint":"","field_type":"1","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0,"field_customer_id":1102358948,"format":""},{"id":"tel_list","name":"联系电话","base":"1","require":"0","hint":"联系电话提示","field_type":"1","ext_info":[],"disable_flag":"0","is_editable":1,"default":"[]","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":[],"group_id":0,"field_customer_id":1102358948,"format":"[]"},{"id":"contact","name":"社交平台","base":"1","require":"0","hint":"","field_type":"2","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":[],"group_id":0,"field_customer_id":1102358948,"format":"[]"},{"id":"image_list","name":"图片","base":"1","require":"0","hint":" ","field_type":"6","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":[],"group_id":0,"field_customer_id":1102358948,"format":"[]"},{"id":"gender","name":"性别","base":"1","require":"0","hint":"","field_type":"3","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":0,"group_id":0,"field_customer_id":1102358948,"format":"0"},{"id":"1102250609","name":"测试字段","base":"0","require":"0","hint":"","field_type":"7","ext_info":["123","211","2121"],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0,"field_customer_id":1102358948,"format":""}]}]}', true);

        array_walk($data['customers'], function (&$groupFields) {
            $groupFields['customer_id'] = 0;
            $groupFields['company_id'] = 0;
            array_walk($groupFields['fields'], function (&$field) {
                if ($field['id'] == 'email') {
                    $field['value'] = $this->faker()->email;
                }
                if ($field['id'] == 'tel_list') {
                    $field['value'] = [
                        $this->faker()->numberBetween(1, 100),
                        $this->faker()->numberBetween(*********, *********),
                    ];
                }
                if ($field['id'] == 'contact') {
                    $field['value'] = [
                        [
                            'type' => 'facebook',
                            'value' => '123' . time(),
                        ]
                    ];
                }
            });
        });
        array_walk($data['company'], function (&$groupFields) {
            array_walk($groupFields['fields'], function (&$field) {
                if ($field['id'] == 'serial_id') {
                    $field['value'] = '';
                }
                if ($field['id'] == 'name') {
                    $field['value'] = $field['value'] . time();
                }
                if ($field['id'] == 'short_name') {
                    $field['value'] = 'Unique Company Name'. time();
                }
                if ($field['id'] == 'address') {
                    $field['value'] = $this->faker()->streetName;
                }
                if ($field['id'] == 'homepage') {
                    $field['value'] = 'www.' . time() . '.com';
                }
                if ($field['id'] == 'remark') {
                    $field['value'] = $this->faker()->streetName;
                }
                if ($field['id'] == 'tel') {
                    $field['value']['tel_area_code'] = $this->faker()->numberBetween(1, 300);
                    $field['value']['tel'] = $this->faker()->numberBetween(10000000, 99999999);
                }
                if ($field['id'] == 'category_ids') {
                    $field['value'] = [[]];
                }
            });
        });
        $companyId = 0;

        $this->loginUser('<EMAIL>');

//        $data = json_decode('{"company":[{"group_id":1,"name":"基本信息","fields":[{"id":"serial_id","name":"客户编号","base":"1","require":"0","group_id":"1","hint":"","field_type":"5","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"00129"},{"id":"name","name":"公司名称","base":"1","require":"1","group_id":"1","hint":"请输入公司名称","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","value":"xhl的公司测试"},{"id":"short_name","name":"简称","base":"1","require":"0","group_id":"1","hint":"简称提示","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"origin","name":"客户来源","base":"1","require":"1","group_id":"1","hint":"客户来源提示","field_type":"3","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"1101043661"}]},{"group_id":2,"name":"特征信息","fields":[{"id":"biz_type","name":"客户类型","base":"1","require":"0","group_id":"2","hint":"客户类型提示","field_type":"3","ext_info":[{"zh-CN":"原材料供应商","zh-TW":"原材料供應商","en":"Raw Material Supplier","value":"原材料供应商","name":"原材料供应商","label":"原材料供应商"},{"zh-CN":"生产商","zh-TW":"生產商","en":"Producers","value":"生产商","name":"生产商","label":"生产商"},{"zh-CN":"加盟商","zh-TW":"加盟商","en":"Franchisee","value":"加盟商","name":"加盟商","label":"加盟商"},{"zh-CN":"渠道商","zh-TW":"渠道商","en":"Distributors","value":"渠道商","name":"渠道商","label":"渠道商"},{"zh-CN":"贸易商","zh-TW":"貿易商","en":"Trader","value":"贸易商","name":"贸易商","label":"贸易商"},{"zh-CN":"代理商","zh-TW":"代理商","en":"Agent","value":"代理商","name":"代理商","label":"代理商"},{"zh-CN":"批发商","zh-TW":"批發商","en":"Wholesaler","value":"批发商","name":"批发商","label":"批发商"},{"zh-CN":"分销商","zh-TW":"分銷商","en":"Distributor","value":"分销商","name":"分销商","label":"分销商"},{"zh-CN":"代销商","zh-TW":"代銷商","en":"Affiliate","value":"代销商","name":"代销商","label":"代销商"},{"zh-CN":"零售商","zh-TW":"零售商","en":"Retailer","value":"零售商","name":"零售商","label":"零售商"},{"zh-CN":"采购办事处","zh-TW":"採購辦事處","en":"Purchasing Office","value":"采购办事处","name":"采购办事处","label":"采购办事处"},{"zh-CN":"采购咨询公司","zh-TW":"採購諮詢公司","en":"Purchasing Consulting Company","value":"采购咨询公司","name":"采购咨询公司","label":"采购咨询公司"},{"zh-CN":"出口商","zh-TW":"出口商","en":"Exporter","value":"出口商","name":"出口商","label":"出口商"},{"zh-CN":"进口商","zh-TW":"進口商","en":"Importer","value":"进口商","name":"进口商","label":"进口商"},{"zh-CN":"个人消费者","zh-TW":"個人消費者","en":"Personal Consumer","value":"个人消费者","name":"个人消费者","label":"个人消费者"},{"zh-CN":"机构/团体消费者","zh-TW":"機構/團體消費者","en":"Institution/group consumer","value":"机构/团体消费者","name":"机构/团体消费者","label":"机构/团体消费者"},{"zh-CN":"工程商","zh-TW":"工程商","en":"Engineering","value":"工程商","name":"工程商","label":"工程商"},{"zh-CN":"其他","zh-TW":"其他","en":"Other","value":"其他","name":"其他","label":"其他"}],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"category_ids","name":"主营产品","base":"1","require":"0","group_id":"2","hint":"主营产品提示","field_type":"7","ext_info":[],"default":"[]","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":[]},{"id":"country","name":"国家地区","base":"1","require":"0","group_id":"2","hint":"国家地区提示","field_type":"3","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"","countryInfo":{"country":"","province":"","city":""}},{"id":"province","name":"省份","base":"1","require":"0","group_id":"2","hint":"身份提示","field_type":"3","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"timezone","name":"时区","base":"1","require":"0","group_id":"2","hint":"时区提示","field_type":"3","ext_info":[{"label":"零时区：伦敦","value":"0"},{"label":"东一区：罗马，巴黎","value":"1"},{"label":"东二区：雅典，以色列","value":"2"},{"label":"东三区：莫斯科，科威特","value":"3"},{"label":"东四区：喀布尔","value":"4"},{"label":"东五区：伊斯兰堡，卡拉奇","value":"5"},{"label":"东六区：阿拉木图，科伦坡","value":"6"},{"label":"东七区：曼谷，雅加达","value":"7"},{"label":"东八区：北京，香港，台湾","value":"8"},{"label":"东九区：东京","value":"9"},{"label":"东十区：悉尼","value":"10"},{"label":"东十一区：霍尼亚拉，马加丹","value":"11"},{"label":"东西十二区: 奥克兰","value":"12"},{"label":"西十一区：帕果帕果，阿洛菲","value":"-11"},{"label":"西十区：夏威夷","value":"-10"},{"label":"西九区：阿拉斯加","value":"-9"},{"label":"西八区：洛杉矶，旧金山","value":"-8"},{"label":"西七区：盐湖城、丹佛、凤凰城","value":"-7"},{"label":"西六区：芝加哥，休斯顿，亚特兰大","value":"-6"},{"label":"西五区：纽约，华盛顿，波士顿","value":"-5"},{"label":"西四区：加拿大，加拉加斯","value":"-4"},{"label":"西三区：巴西利亚","value":"-3"},{"label":"西二区：协调世界时","value":"-2"},{"label":"西一区：佛得角群岛","value":"-1"}],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"city","name":"城市","base":"1","require":"0","group_id":"2","hint":"城市提示","field_type":"3","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"scale_id","name":"规模","base":"1","require":"0","group_id":"2","hint":"规模提示","field_type":"3","ext_info":[{"value":1,"name":"少于59人","en_name":"Less than 59 employees","label":"少于59人"},{"value":2,"name":"60-149人","en_name":"60-149 employees","label":"60-149人"},{"value":3,"name":"150-499人","en_name":"150-499 employees","label":"150-499人"},{"value":4,"name":"500-999人","en_name":"500-999 employees","label":"500-999人"},{"value":5,"name":"1000-4999人","en_name":"1000-4999 employees","label":"1000-4999人"},{"value":6,"name":"5000人以上","en_name":"More than 5000 employees","label":"5000人以上"}],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"1101101841","name":"自定义日期-时间","base":"0","require":"0","group_id":"2","hint":"自定义时间提示","field_type":"10","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"1101197498","name":"俄文","base":"0","require":"0","group_id":"2","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":""}]},{"group_id":3,"name":"管理信息","fields":[{"id":"star","name":"客户星级","base":"1","require":"0","group_id":"3","hint":"客户星级提示","field_type":"5","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":0},{"id":"group_id","name":"分组","base":"1","require":"0","group_id":"3","hint":"分组提示","field_type":"3","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"pool_id","name":"公海分组","base":"1","require":"1","group_id":"3","hint":"公海分组提示","field_type":"3","ext_info":[""],"default":"0","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":0},{"id":"cus_tag","name":"客户标签","base":"1","require":"0","group_id":"3","hint":"客户标签提示","field_type":"7","ext_info":[],"default":"[]","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"[]"},{"id":"trail_status","name":"客户状态","base":"1","require":"0","group_id":"3","hint":"客户状态提示","field_type":"3","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":""}]},{"group_id":4,"name":"联系信息","fields":[{"id":"fax","name":"传真","base":"1","require":"0","group_id":"4","hint":"传真提示","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"tel","name":"座机","base":"1","require":"0","group_id":"4","hint":"座机提示","field_type":"1","ext_info":[],"default":"{\"tel_area_code\":\"\",\"tel\":\"\"}","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"{\"tel_area_code\":\"\",\"tel\":\"\"}"},{"id":"homepage","name":"主页","base":"1","require":"0","group_id":"4","hint":"主页提示","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"address","name":"联系地址","base":"1","require":"0","group_id":"4","hint":"联系地址提示","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"1","unique_prevent":"1","unique_message":"联系地址已存在","value":""},{"id":"lonlat","name":"经维度坐标","base":"1","require":"0","group_id":"4","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":{"longitude":0,"latitude":0}}]},{"group_id":5,"name":"其他信息","fields":[{"id":"1101101835","name":"自定义文本","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"1101101836","name":"自定义多行文本","base":"0","require":"0","group_id":"5","hint":"","field_type":"2","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"1101101837","name":"自定义下拉单选","base":"0","require":"0","group_id":"5","hint":"","field_type":"3","ext_info":["客户1","客户3"],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"1101101838","name":"自定义日期","base":"0","require":"0","group_id":"5","hint":"","field_type":"4","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"1101101840","name":"自定义下拉多选","base":"0","require":"0","group_id":"5","hint":"","field_type":"7","ext_info":["客户多选2","客户多选3"],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"1101101839","name":"自定义数值","base":"0","require":"0","group_id":"5","hint":"","field_type":"5","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"image_list","name":"图片","base":"1","require":"0","group_id":"5","hint":" ","field_type":"6","ext_info":[],"default":"","disable_flag":"0","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","value":[]},{"id":"remark","name":"备注","base":"1","require":"0","group_id":"5","hint":"","field_type":"2","ext_info":[],"default":"","disable_flag":"0","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"1102352738","name":"下拉多选","base":"0","require":"0","group_id":"5","hint":"","field_type":"7","ext_info":["咸水鸭","姜母鸭","周黑鸭"],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":""}]}],"customers":[{"customer_id":0,"company_id":0,"email":"","name":"","main_customer_flag":1,"fields":[{"id":"main_customer_flag","name":"主要联系人","base":"1","require":"0","hint":"","field_type":"3","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":1,"group_id":0},{"id":"name","name":"昵称","base":"1","require":"0","hint":"","field_type":"1","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"小妮子","group_id":0},{"id":"email","name":"邮箱","base":"1","require":"0","hint":"","field_type":"1","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"1","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"remark","name":"备注","base":"1","require":"0","hint":"","field_type":"2","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"1102250660","name":"qqq","base":"0","require":"0","hint":"","field_type":"5","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"post_grade","name":"职级","base":"1","require":"0","hint":"","field_type":"3","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"birth","name":"生日","base":"1","require":"0","hint":"","field_type":"4","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"post","name":"职位","base":"1","require":"0","hint":"","field_type":"1","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"tel_list","name":"联系电话","base":"1","require":"0","hint":"联系电话提示","field_type":"1","ext_info":[],"disable_flag":"0","is_editable":1,"default":"[]","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":[],"group_id":0},{"id":"contact","name":"社交平台","base":"1","require":"0","hint":"","field_type":"2","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":[],"group_id":0},{"id":"image_list","name":"图片","base":"1","require":"0","hint":" ","field_type":"6","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":[],"group_id":0},{"id":"gender","name":"性别","base":"1","require":"0","hint":"","field_type":"3","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"1102250609","name":"测试字段","base":"0","require":"0","hint":"","field_type":"7","ext_info":["123","211","2121"],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0}]}]}');
//        $this->loginUser('<EMAIL>');
//        (new \common\library\privilege_v3\PrivilegeField(static::$clientId))->flushCache();
//        $companyId = 1102358949;
        $params = [
            'company_id'   => $companyId,
            'data'         => json_encode($data),
            'check_public' => 1,
            'archive_flag' => 1,
        ];
//        $this->enableProfile();
        $saveCompany = $this->callAction('saveCompanyData', $params);
        $this->responseOk();
        $companyId = $this->actionResult['data']['company_id'];

        if ($companyId){
            $op = new \common\library\customer\CompanyBatchOperator(static::$userId);
            $op->setParams(['company_ids' => [$companyId]]);
            $count = $op->delete();
        }
    }

    public function testDebugCompanySave()
    {
        $data = json_decode('
        {"company":[{"group_id":1,"name":"基本信息","fields":[{"id":"name","name":"公司名称","base":"1","require":"1","group_id":"1","hint":"我是公司名称提示语","field_type":"1","ext_info":[""],"default":"","disable_flag":"0","is_editable":1,"unique_check":"1","unique_prevent":"1","unique_message":"","value":"Zcasda01"},{"id":"serial_id","name":"客户编号","base":"1","require":"0","group_id":"1","hint":"这是客户编号哦","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":0,"unique_check":"1","unique_prevent":"1","unique_message":"","value":""},{"id":"origin","name":"客户来源","base":"1","require":"0","group_id":"1","hint":"","field_type":"3","ext_info":[""],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"short_name","name":"简称","base":"1","require":"0","group_id":"1","hint":"我是简称提示语","field_type":"1","ext_info":[""],"default":"","disable_flag":"0","is_editable":1,"unique_check":"1","unique_prevent":"2","unique_message":"简称不允许重复","value":""},{"id":"1887817526","name":"ccccc","base":"0","require":"0","group_id":"1","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"1","unique_prevent":"2","unique_message":"","value":""},{"id":"1108018263","name":"lvy新增1","base":"0","require":"0","group_id":"1","hint":"","field_type":"5","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","value":""}]},{"group_id":2,"name":"特征信息","fields":[{"id":"timezone","name":"时区","base":"1","require":"0","group_id":"2","hint":"","field_type":"3","ext_info":[{"label":"零时区：伦敦","value":"0"},{"label":"东一区：罗马，巴黎","value":"1"},{"label":"东二区：雅典，以色列","value":"2"},{"label":"东三区：莫斯科，科威特","value":"3"},{"label":"东四区：喀布尔","value":"4"},{"label":"东五区：伊斯兰堡，卡拉奇","value":"5"},{"label":"东六区：阿拉木图，科伦坡","value":"6"},{"label":"东七区：曼谷，雅加达","value":"7"},{"label":"东八区：北京，香港，台湾","value":"8"},{"label":"东九区：东京","value":"9"},{"label":"东十区：悉尼","value":"10"},{"label":"东十一区：霍尼亚拉，马加丹","value":"11"},{"label":"东西十二区: 奥克兰","value":"12"},{"label":"西十一区：帕果帕果，阿洛菲","value":"-11"},{"label":"西十区：夏威夷","value":"-10"},{"label":"西九区：阿拉斯加","value":"-9"},{"label":"西八区：洛杉矶，旧金山","value":"-8"},{"label":"西七区：盐湖城、丹佛、凤凰城","value":"-7"},{"label":"西六区：芝加哥，休斯顿，亚特兰大","value":"-6"},{"label":"西五区：纽约，华盛顿，波士顿","value":"-5"},{"label":"西四区：加拿大，加拉加斯","value":"-4"},{"label":"西三区：巴西利亚","value":"-3"},{"label":"西二区：协调世界时","value":"-2"},{"label":"西一区：佛得角群岛","value":"-1"}],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"province","name":"省份","base":"1","require":"0","group_id":"2","hint":"","field_type":"3","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"country","name":"国家地区","base":"1","require":"0","group_id":"2","hint":"","field_type":"3","ext_info":[""],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"","countryInfo":{"country":"","province":"","city":""}},{"id":"category_ids","name":"主营产品","base":"1","require":"0","group_id":"2","hint":"","field_type":"7","ext_info":[""],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":[]},{"id":"city","name":"城市","base":"1","require":"0","group_id":"2","hint":"","field_type":"3","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"scale_id","name":"规模","base":"1","require":"0","group_id":"2","hint":"","field_type":"3","ext_info":[{"value":1,"name":"少于59人","en_name":"Less than 59 employees","label":"少于59人"},{"value":2,"name":"60-149人","en_name":"60-149 employees","label":"60-149人"},{"value":3,"name":"150-499人","en_name":"150-499 employees","label":"150-499人"},{"value":4,"name":"500-999人","en_name":"500-999 employees","label":"500-999人"},{"value":5,"name":"1000-4999人","en_name":"1000-4999 employees","label":"1000-4999人"},{"value":6,"name":"5000人以上","en_name":"More than 5000 employees","label":"5000人以上"}],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"intention_level","name":"采购意向","base":"1","require":"0","group_id":"2","hint":"","field_type":"3","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"biz_type","name":"客户类型","base":"1","require":"0","group_id":"2","hint":"","field_type":"3","ext_info":[{"zh-CN":"原材料供应商","zh-TW":"原材料供應商","en":"Raw Material Supplier","value":"原材料供应商","name":"原材料供应商","label":"原材料供应商"},{"zh-CN":"生产商","zh-TW":"生產商","en":"Producers","value":"生产商","name":"生产商","label":"生产商"},{"zh-CN":"加盟商","zh-TW":"加盟商","en":"Franchisee","value":"加盟商","name":"加盟商","label":"加盟商"},{"zh-CN":"渠道商","zh-TW":"渠道商","en":"Distributors","value":"渠道商","name":"渠道商","label":"渠道商"},{"zh-CN":"贸易商","zh-TW":"貿易商","en":"Trader","value":"贸易商","name":"贸易商","label":"贸易商"},{"zh-CN":"代理商","zh-TW":"代理商","en":"Agent","value":"代理商","name":"代理商","label":"代理商"},{"zh-CN":"批发商","zh-TW":"批發商","en":"Wholesaler","value":"批发商","name":"批发商","label":"批发商"},{"zh-CN":"分销商","zh-TW":"分銷商","en":"Distributor","value":"分销商","name":"分销商","label":"分销商"},{"zh-CN":"代销商","zh-TW":"代銷商","en":"Affiliate","value":"代销商","name":"代销商","label":"代销商"},{"zh-CN":"零售商","zh-TW":"零售商","en":"Retailer","value":"零售商","name":"零售商","label":"零售商"},{"zh-CN":"采购办事处","zh-TW":"採購辦事處","en":"Purchasing Office","value":"采购办事处","name":"采购办事处","label":"采购办事处"},{"zh-CN":"采购咨询公司","zh-TW":"採購諮詢公司","en":"Purchasing Consulting Company","value":"采购咨询公司","name":"采购咨询公司","label":"采购咨询公司"},{"zh-CN":"出口商","zh-TW":"出口商","en":"Exporter","value":"出口商","name":"出口商","label":"出口商"},{"zh-CN":"进口商","zh-TW":"進口商","en":"Importer","value":"进口商","name":"进口商","label":"进口商"},{"zh-CN":"个人消费者","zh-TW":"個人消費者","en":"Personal Consumer","value":"个人消费者","name":"个人消费者","label":"个人消费者"},{"zh-CN":"机构/团体消费者","zh-TW":"機構/團體消費者","en":"Institution/group consumer","value":"机构/团体消费者","name":"机构/团体消费者","label":"机构/团体消费者"},{"zh-CN":"工程商","zh-TW":"工程商","en":"Engineering","value":"工程商","name":"工程商","label":"工程商"},{"zh-CN":"其他","zh-TW":"其他","en":"Other","value":"其他","name":"其他","label":"其他"}],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"annual_procurement","name":"年采购额","base":"1","require":"0","group_id":"2","hint":"","field_type":"3","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"3043781064","name":"测试公司-tv-多行文本","base":"0","require":"0","group_id":"2","hint":"请输入","field_type":"2","ext_info":[],"default":"123","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"123"},{"id":"1108018301","name":"lvy新增文本","base":"0","require":"0","group_id":"2","hint":"","field_type":"2","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"1119201714","name":"公司信息-客户管理自定义","base":"0","require":"0","group_id":"2","hint":"这是自定义哦~","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"1","unique_prevent":"1","unique_message":"","value":""}]},{"group_id":3,"name":"管理信息","fields":[{"id":"group_id","name":"分组","base":"1","require":"0","group_id":"3","hint":"21212","field_type":"3","ext_info":[""],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"cus_tag","name":"客户标签","base":"1","require":"0","group_id":"3","hint":"我是客户标签提示语","field_type":"7","ext_info":[""],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":[]},{"id":"trail_status","name":"客户阶段","base":"1","require":"1","group_id":"3","hint":"我是提示语122","field_type":"3","ext_info":[""],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"17096"},{"id":"pool_id","name":"公海分组","base":"1","require":"1","group_id":"3","hint":"我是公海分组字段提","field_type":"3","ext_info":[""],"default":"0","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":0},{"id":"star","name":"客户星级","base":"1","require":"0","group_id":"3","hint":"","field_type":"5","ext_info":[""],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"0"},{"id":"product_group_ids","name":"产品分组","base":"1","require":"0","group_id":"3","hint":"测试一下文案","field_type":"7","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":""}]},{"group_id":4,"name":"联系信息","fields":[{"id":"homepage","name":"公司网址","base":"1","require":"0","group_id":"4","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"1","unique_prevent":"2","unique_message":"","value":""},{"id":"tel","name":"座机","base":"1","require":"0","group_id":"4","hint":"我是座机呀","field_type":"1","ext_info":[""],"default":"","disable_flag":"0","is_editable":1,"unique_check":"1","unique_prevent":"2","unique_message":"","value":{"tel":"","tel_area_code":""}},{"id":"address","name":"详细地址","base":"1","require":"0","group_id":"4","hint":"000","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"1","unique_prevent":"2","unique_message":"","value":""},{"id":"fax","name":"传真","base":"1","require":"0","group_id":"4","hint":"","field_type":"1","ext_info":[""],"default":"","disable_flag":"0","is_editable":1,"unique_check":"1","unique_prevent":"1","unique_message":"传真不允许重复","value":""},{"id":"1118472444","name":"CCT2-Effect","base":"0","require":"0","group_id":"4","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"1106146700","name":"sharon测试备注","base":"0","require":"0","group_id":"4","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"1","unique_prevent":"1","unique_message":"","value":""},{"id":"lonlat","name":"经维度坐标","base":"1","require":"0","group_id":"4","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","value":{"longitude":0,"latitude":0}},{"id":"1108043070","name":"lvy新增日期","base":"0","require":"0","group_id":"4","hint":"","field_type":"4","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","value":""}]},{"group_id":5,"name":"其他信息","fields":[{"id":"3082106510","name":"测试公司-tv-文本","base":"0","require":"0","group_id":"5","hint":"这个文本要写啊","field_type":"1","ext_info":[],"default":"我是必填文本默认值","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"这个重复了","value":"我是必填文本默认值"},{"id":"remark","name":"公司备注","base":"1","require":"0","group_id":"5","hint":"啦啦啦","field_type":"2","ext_info":[],"default":"龙琴","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"龙琴"},{"id":"image_list","name":"公司logo","base":"1","require":"0","group_id":"5","hint":" ","field_type":"6","ext_info":[""],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":[]},{"id":"next_follow_up_time","name":"下次跟进时间","base":"1","require":"0","group_id":"5","hint":"","field_type":"10","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"3082106512","name":"测试公司-tv-下拉单选","base":"0","require":"0","group_id":"5","hint":"这个单选要选啊","field_type":"3","ext_info":["1","2","3","4","5"],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"3025700946","name":"客户老编号","base":"0","require":"0","group_id":"5","hint":"我是提示语","field_type":"1","ext_info":[],"default":"我是默认值","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"我是默认值"},{"id":"3043780965","name":"下拉多选","base":"0","require":"0","group_id":"5","hint":"","field_type":"7","ext_info":["test","苹果","雪梨"],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"3082106517","name":"测试公司-tv-下拉多选","base":"0","require":"0","group_id":"5","hint":"这个多选要选啊","field_type":"7","ext_info":["去","我","呃","人","他"],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"3082106516","name":"测试公司-tv-数值","base":"0","require":"0","group_id":"5","hint":"这个数值要填写啊","field_type":"5","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"3082106514","name":"测试公司-tv-日期","base":"0","require":"0","group_id":"5","hint":"这个日期要写啊","field_type":"4","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"3082106518","name":"测试公司-tv-日期时间","base":"0","require":"0","group_id":"5","hint":"这个时间要填啊","field_type":"10","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"1111328455","name":"janetest1","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"1111328400","name":"janetest","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"1101160789","name":"lora客户公司－数值","base":"0","require":"0","group_id":"5","hint":"","field_type":"5","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"1100286110","name":"OS - 重要测试字段，千万别动","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"1100149746","name":"lora测试有数据的字段","base":"0","require":"0","group_id":"5","hint":"","field_type":"2","ext_info":[],"default":"有数据字段的一个默认值","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"2","unique_message":"lora测试有数据的字段内容重复啦～","value":"有数据字段的一个默认值"},{"id":"**********","name":"ollar Payment, the remittance instruction route:  *5","base":"0","require":"0","group_id":"5","hint":"","field_type":"7","ext_info":["IMEE GROUP LTD EURO Dollar Payment, the remittance instruction route:  *57A: Account with institutio"],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"**********","name":"lora客户公司－多行文本","base":"0","require":"0","group_id":"5","hint":"","field_type":"2","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"********","name":"sharon自定义字段下拉","base":"0","require":"0","group_id":"5","hint":"下啦提示","field_type":"3","ext_info":["琵琶","古琴-xxx"],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"**********","name":"lora客户公司－文本","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"1","unique_prevent":"1","unique_message":"1243434","value":""},{"id":"**********","name":"线索&客户-公司1-文本 - xiugai - dd","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"1118262834","name":"young-2333333","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"1118263027","name":"122","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"1118263031","name":"ProductList","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"1118263048","name":"333","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"1118263055","name":"dd","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"1118059887","name":"poi","base":"0","require":"0","group_id":"5","hint":"","field_type":"7","ext_info":["123","234"],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"1118312205","name":"20210413-1","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"1102723524","name":"sharon测试iOS多行文本默认文案","base":"0","require":"0","group_id":"5","hint":"","field_type":"2","ext_info":[],"default":"iOS","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","value":"iOS"},{"id":"**********","name":"线索&客户-公司7-数值","base":"0","require":"0","group_id":"5","hint":"","field_type":"5","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"1107572141","name":"线索自定义筛选字段","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"1106335125","name":"自定义字段-数值","base":"0","require":"0","group_id":"5","hint":"","field_type":"5","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"1118426017","name":"单选字段","base":"0","require":"0","group_id":"5","hint":"","field_type":"3","ext_info":["单选1","单选2","单选3"],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"1971174486","name":"我是数字","base":"0","require":"0","group_id":"5","hint":"","field_type":"5","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"1118472442","name":"新增一个字段 - os","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"1118439606","name":"新建一个","base":"0","require":"0","group_id":"5","hint":"","field_type":"2","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"1106325536","name":"线索&客户-公司2-多行文本","base":"0","require":"0","group_id":"5","hint":"","field_type":"2","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"1118472445","name":"字段名称 - os001","base":"0","require":"0","group_id":"5","hint":"","field_type":"3","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"1106325537","name":"线索&客户-公司3-下拉单选","base":"0","require":"0","group_id":"5","hint":"","field_type":"3","ext_info":["23人","卫栖梧若","接口环境净空法师刘丽说离开时"],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"1118472446","name":"os - 字段 - 001 - 0","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"1118472443","name":"os - 2","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"2178741392","name":"1","base":"0","require":"0","group_id":"5","hint":"","field_type":"4","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"1106325564","name":"线索&客户-公司6-日期+时间","base":"0","require":"0","group_id":"5","hint":"","field_type":"10","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"2408213670","name":"富通客户代码","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"1129001406","name":"年采购额1","base":"0","require":"0","group_id":"5","hint":"","field_type":"5","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"2433403748","name":"11111","base":"0","require":"0","group_id":"5","hint":"","field_type":"2","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"2","unique_message":"","value":""},{"id":"1139914768","name":"多选客户字段","base":"0","require":"0","group_id":"5","hint":"","field_type":"7","ext_info":["a","b","v","c","q","g","hj"],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"1106325554","name":"线索&客户-公司4-下拉多选","base":"0","require":"0","group_id":"5","hint":"","field_type":"7","ext_info":["1234567890","abcdefghijklmn","你是我的小丫小苹果","怎么爱你都不限购"],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"1106325563","name":"线索&客户-公司5-日期","base":"0","require":"0","group_id":"5","hint":"","field_type":"4","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"1127474852","name":"自定义字段-日期","base":"0","require":"0","group_id":"5","hint":"","field_type":"4","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"1119201713","name":"公司信息--线索自定义字段","base":"0","require":"0","group_id":"5","hint":"","field_type":"3","ext_info":["1","2"],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"2408213661","name":"ces 11","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"3082158045","name":"工单自定义多选字段","base":"0","require":"0","group_id":"5","hint":"","field_type":"7","ext_info":["测试1","测试2","测试3"],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"3082301486","name":"自定义日期","base":"0","require":"0","group_id":"5","hint":"","field_type":"4","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"3113323369","name":"产品分组多选","base":"0","require":"0","group_id":"5","hint":"","field_type":"7","ext_info":["产品1","产品2","产品3"],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"3113323380","name":"产品组单选","base":"0","require":"0","group_id":"5","hint":"","field_type":"3","ext_info":["单选1","单选2"],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"3113338550","name":"产品文本","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"3113338556","name":"产品多行文本","base":"0","require":"0","group_id":"5","hint":"","field_type":"2","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"3113338557","name":"产品日期","base":"0","require":"0","group_id":"5","hint":"","field_type":"4","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"3113338576","name":"产品数值","base":"0","require":"0","group_id":"5","hint":"","field_type":"5","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"3113338577","name":"产品日期时间","base":"0","require":"0","group_id":"5","hint":"","field_type":"10","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"3115017206","name":"测试字段","base":"0","require":"0","group_id":"5","hint":"","field_type":"3","ext_info":["专业进口商","专业进口商2","专业进口商3"],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"3115243335","name":"ttttt","base":"0","require":"0","group_id":"5","hint":"","field_type":"3","ext_info":["aaa1","Aaa","AAA"],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"3117456124","name":"2","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"3117881352","name":"询盘产品-测试工单","base":"0","require":"1","group_id":"5","hint":"","field_type":"7","ext_info":["洗衣片","吸色片","留香珠"],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":["吸色片"]}]}],"customers":[{"customer_id":0,"company_id":0,"email":"","name":"","main_customer_flag":1,"fields":[{"id":"main_customer_flag","name":"主要联系人","base":"1","require":"0","hint":"","field_type":"3","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":1,"group_id":0},{"id":"contact","name":"社交平台","base":"1","require":"0","hint":"","field_type":"2","ext_info":[""],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"1","unique_prevent":"2","unique_message":"","value":[],"group_id":0},{"id":"tel_list","name":"联系电话","base":"1","require":"0","hint":"","field_type":"1","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"1","unique_prevent":"2","unique_message":"","value":[],"group_id":0},{"id":"birth","name":"生日","base":"1","require":"0","hint":"","field_type":"4","ext_info":[""],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"name","name":"昵称","base":"1","require":"0","hint":"","field_type":"1","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"asad","group_id":0},{"id":"email","name":"邮箱","base":"1","require":"0","hint":"","field_type":"1","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"1","unique_prevent":"2","unique_message":"","value":"","group_id":0},{"id":"post_grade","name":"职级","base":"1","require":"0","hint":"","field_type":"3","ext_info":[""],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"post","name":"职位","base":"1","require":"0","hint":"","field_type":"1","ext_info":[""],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"1127474215","name":"职位信息","base":"0","require":"0","hint":"","field_type":"1","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"gender","name":"性别","base":"1","require":"0","hint":"","field_type":"3","ext_info":[""],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"image_list","name":"头像/名片","base":"1","require":"0","hint":" ","field_type":"6","ext_info":[""],"disable_flag":"1","is_editable":"1","default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":[],"group_id":0},{"id":"remark","name":"联系人备注","base":"1","require":"0","hint":"","field_type":"2","ext_info":[""],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"3082106530","name":"测试联系人-tv-文本","base":"0","require":"0","hint":"这是联系人必填提示","field_type":"1","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"3082106531","name":"测试联系人-tv-多行文本","base":"0","require":"0","hint":"","field_type":"2","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"？？？？","value":"","group_id":0},{"id":"3082106535","name":"测试联系人-tv-单选","base":"0","require":"0","hint":"这是单选提示啊","field_type":"3","ext_info":["12","13","14","15","16"],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"3082106536","name":"测试联系人-tv-日期","base":"0","require":"0","hint":"","field_type":"4","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"3082106537","name":"测试联系人-tv-数值","base":"0","require":"0","hint":"","field_type":"5","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"3082106539","name":"测试联系人-tv-下拉多选","base":"0","require":"0","hint":"","field_type":"7","ext_info":["12","23","34","45","56"],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"3082106540","name":"测试联系人-tv-日期时间","base":"0","require":"0","hint":"","field_type":"10","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"1119201734","name":"自定义联系字段~","base":"0","require":"0","hint":"我添加的~","field_type":"5","ext_info":[],"disable_flag":"1","is_editable":"1","default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"1297594717","name":"*********","base":"0","require":"0","hint":"","field_type":"4","ext_info":[],"disable_flag":"1","is_editable":"1","default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"1*********","name":"+++++","base":"0","require":"0","hint":"","field_type":"1","ext_info":[],"disable_flag":"1","is_editable":"1","default":"","type":"5","unique_check":"1","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"3117455615","name":"kaia","base":"0","require":"0","hint":"","field_type":"1","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0}]}]}
        ', true);
        $companyId = 1132170448;
        $companyId = 0;
//        $mail_id = 1102570029;
        $archive_flag = 1;

//        array_walk($data['company'], function (&$groupFields) {
//            array_walk($groupFields['fields'], function (&$field) {
//                if ($field['id'] == 'name') {
//                    $field['value'] = time() . $field['value'];
//                }
//                if ($field['id'] == 'remark') {
//                    $field['value'] = $this->faker()->streetName;
//                }
//            });
//        });
//
//        array_walk($data['customers'], function (&$groupFields) {
//            array_walk($groupFields['fields'], function (&$field) {
//                if ($field['id'] == 'name') {
//                    $field['value'] = 'unittest_' . time();
//                }
////                if ($field['id'] == 'tel_list') {
////                    foreach ($field['value'] as &$tel) {
////                        $tel[1] = rand(1, 99999) . $tel[1];
////                    }
////                }
//            });
//        });

//    $this->enableProfile();
//        $this->loginAsXpro();
        $this->loginAsKk();
        $companyId = 0;
        $params = [
//            'data'         => json_encode($data),
            'data' => '{"company": [{"group_id": 1, "name": "基本信息", "fields": [{"id": "serial_id", "name": "客户编号", "base": "1", "require": "0", "group_id": "1", "hint": "", "field_type": "1", "ext_info": [], "default": "", "disable_flag": "0", "is_editable": 1, "unique_check": "1", "unique_prevent": "1", "unique_message": "", "value": ""}, {"id": "name", "name": "公司名称", "base": "1", "require": "1", "group_id": "1", "hint": "", "field_type": "1", "ext_info": [], "default": "", "disable_flag": "0", "is_editable": 1, "unique_check": "1", "unique_prevent": "1", "unique_message": "", "value": "AutoApi_3ddbd2240bf"}, {"id": "origin", "name": "客户来源", "base": "1", "require": "0", "group_id": "1", "hint": "", "field_type": "3", "ext_info": [], "default": "", "disable_flag": "0", "is_editable": 1, "unique_check": "0", "unique_prevent": "1", "unique_message": "", "value": ""}, {"id": "short_name", "name": "简称", "base": "1", "require": "0", "group_id": "1", "hint": "", "field_type": "1", "ext_info": [], "default": "", "disable_flag": "0", "is_editable": 1, "unique_check": "0", "unique_prevent": "1", "unique_message": "", "value": ""}]}, {"group_id": 2, "name": "特征信息", "fields": [{"id": "category_ids", "name": "主营产品", "base": "1", "require": "0", "group_id": "2", "hint": "", "field_type": "7", "ext_info": [], "default": "", "disable_flag": "0", "is_editable": 1, "unique_check": "0", "unique_prevent": "1", "unique_message": "", "value": []}, {"id": "biz_type", "name": "客户类型", "base": "1", "require": "0", "group_id": "2", "hint": "", "field_type": "3", "ext_info": [{"zh-CN": "原材料供应商", "zh-TW": "原材料供應商", "en": "Raw Material Supplier", "value": "原材料供应商", "name": "原材料供应商", "lead": "原材料供应商"}, {"zh-CN": "生产商", "zh-TW": "生產商", "en": "Producers", "value": "生产商", "name": "生产商", "lead": "生产商"}, {"zh-CN": "加盟商", "zh-TW": "加盟商", "en": "Franchisee", "value": "加盟商", "name": "加盟商", "lead": "加盟商"}, {"zh-CN": "渠道商", "zh-TW": "渠道商", "en": "Distributors", "value": "渠道商", "name": "渠道商", "lead": "渠道商"}, {"zh-CN": "贸易商", "zh-TW": "貿易商", "en": "Trader", "value": "贸易商", "name": "贸易商", "lead": "贸易商"}, {"zh-CN": "代理商", "zh-TW": "代理商", "en": "Agent", "value": "代理商", "name": "代理商", "lead": "代理商"}, {"zh-CN": "批发商", "zh-TW": "批發商", "en": "Wholesaler", "value": "批发商", "name": "批发商", "lead": "批发商"}, {"zh-CN": "分销商", "zh-TW": "分銷商", "en": "Distributor", "value": "分销商", "name": "分销商", "lead": "分销商"}, {"zh-CN": "代销商", "zh-TW": "代銷商", "en": "Affiliate", "value": "代销商", "name": "代销商", "lead": "代销商"}, {"zh-CN": "零售商", "zh-TW": "零售商", "en": "Retailer", "value": "零售商", "name": "零售商", "lead": "零售商"}, {"zh-CN": "采购办事处", "zh-TW": "採購辦事處", "en": "Purchasing Office", "value": "采购办事处", "name": "采购办事处", "lead": "采购办事处"}, {"zh-CN": "采购咨询公司", "zh-TW": "採購諮詢公司", "en": "Purchasing Consulting Company", "value": "采购咨询公司", "name": "采购咨询公司", "lead": "采购咨询公司"}, {"zh-CN": "出口商", "zh-TW": "出口商", "en": "Exporter", "value": "出口商", "name": "出口商", "lead": "出口商"}, {"zh-CN": "进口商", "zh-TW": "進口商", "en": "Importer", "value": "进口商", "name": "进口商", "lead": "进口商"}, {"zh-CN": "个人消费者", "zh-TW": "個人消費者", "en": "Personal Consumer", "value": "个人消费者", "name": "个人消费者", "lead": "个人消费者"}, {"zh-CN": "机构/团体消费者", "zh-TW": "機構/團體消費者", "en": "Institution/group consumer", "value": "机构/团体消费者", "name": "机构/团体消费者", "lead": "机构/团体消费者"}, {"zh-CN": "工程商", "zh-TW": "工程商", "en": "Engineering", "value": "工程商", "name": "工程商", "lead": "工程商"}, {"zh-CN": "其他", "zh-TW": "其他", "en": "Other", "value": "其他", "name": "其他", "lead": "其他"}], "default": "", "disable_flag": "0", "is_editable": 1, "unique_check": "0", "unique_prevent": "1", "unique_message": "", "value": ""}, {"id": "country", "name": "国家地区", "base": "1", "require": "0", "group_id": "2", "hint": "", "field_type": "3", "ext_info": [], "default": "", "disable_flag": "0", "is_editable": 1, "unique_check": "0", "unique_prevent": "1", "unique_message": "", "value": "", "countryInfo": {"country": "", "province": "", "city": ""}}, {"id": "intention_level", "name": "采购意向", "base": "1", "require": "0", "group_id": "2", "hint": "", "field_type": "3", "ext_info": [], "default": "", "disable_flag": "0", "is_editable": 1, "unique_check": "0", "unique_prevent": "1", "unique_message": "", "value": ""}, {"id": "annual_procurement", "name": "年采购额", "base": "1", "require": "0", "group_id": "2", "hint": "", "field_type": "3", "ext_info": [], "default": "", "disable_flag": "0", "is_editable": 1, "unique_check": "0", "unique_prevent": "1", "unique_message": "", "value": ""}, {"id": "province", "name": "省份", "base": "1", "require": "0", "group_id": "2", "hint": "", "field_type": "3", "ext_info": [], "default": "", "disable_flag": "0", "is_editable": 1, "unique_check": "0", "unique_prevent": "1", "unique_message": "", "value": ""}, {"id": "timezone", "name": "时区", "base": "1", "require": "0", "group_id": "2", "hint": "", "field_type": "3", "ext_info": [{"label": "零时区：伦敦", "value": "0"}, {"label": "东一区：罗马，巴黎", "value": "1"}, {"label": "东二区：雅典，以色列", "value": "2"}, {"label": "东三区：莫斯科，科威特", "value": "3"}, {"label": "东四区：喀布尔", "value": "4"}, {"label": "东五区：伊斯兰堡，卡拉奇", "value": "5"}, {"label": "东六区：阿拉木图，科伦坡", "value": "6"}, {"label": "东七区：曼谷，雅加达", "value": "7"}, {"label": "东八区：北京，香港，台湾", "value": "8"}, {"label": "东九区：东京", "value": "9"}, {"label": "东十区：悉尼", "value": "10"}, {"label": "东十一区：霍尼亚拉，马加丹", "value": "11"}, {"label": "东西十二区: 奥克兰", "value": "12"}, {"label": "西十一区：帕果帕果，阿洛菲", "value": "-11"}, {"label": "西十区：夏威夷", "value": "-10"}, {"label": "西九区：阿拉斯加", "value": "-9"}, {"label": "西八区：洛杉矶，旧金山", "value": "-8"}, {"label": "西七区：盐湖城、丹佛、凤凰城", "value": "-7"}, {"label": "西六区：芝加哥，休斯顿，亚特兰大", "value": "-6"}, {"label": "西五区：纽约，华盛顿，波士顿", "value": "-5"}, {"label": "西四区：加拿大，加拉加斯", "value": "-4"}, {"label": "西三区：巴西利亚", "value": "-3"}, {"label": "西二区：协调世界时", "value": "-2"}, {"label": "西一区：佛得角群岛", "value": "-1"}], "default": "", "disable_flag": "0", "is_editable": 1, "unique_check": "0", "unique_prevent": "1", "unique_message": "", "value": ""}, {"id": "city", "name": "城市", "base": "1", "require": "0", "group_id": "2", "hint": "", "field_type": "3", "ext_info": [], "default": "", "disable_flag": "0", "is_editable": 1, "unique_check": "0", "unique_prevent": "1", "unique_message": "", "value": ""}, {"id": "scale_id", "name": "规模", "base": "1", "require": "0", "group_id": "2", "hint": "", "field_type": "3", "ext_info": [{"value": 1, "name": "少于59人", "en_name": "Less than 59 employees", "label": "少于59人"}, {"value": 2, "name": "60-149人", "en_name": "60-149 employees", "label": "60-149人"}, {"value": 3, "name": "150-499人", "en_name": "150-499 employees", "label": "150-499人"}, {"value": 4, "name": "500-999人", "en_name": "500-999 employees", "label": "500-999人"}, {"value": 5, "name": "1000-4999人", "en_name": "1000-4999 employees", "label": "1000-4999人"}, {"value": 6, "name": "5000人以上", "en_name": "More than 5000 employees", "label": "5000人以上"}], "default": "", "disable_flag": "0", "is_editable": 1, "unique_check": "0", "unique_prevent": "1", "unique_message": "", "value": ""}]}, {"group_id": 3, "name": "管理信息", "fields": [{"id": "star", "name": "客户星级", "base": "1", "require": "0", "group_id": "3", "hint": "", "field_type": "5", "ext_info": [], "default": "", "disable_flag": "0", "is_editable": 1, "unique_check": "0", "unique_prevent": "1", "unique_message": "", "value": "218689544565"}, {"id": "group_id", "name": "分组", "base": "1", "require": "0", "group_id": "3", "hint": "", "field_type": "3", "ext_info": [], "default": "", "disable_flag": "0", "is_editable": 1, "unique_check": "0", "unique_prevent": "1", "unique_message": "", "value": ""}, {"id": "pool_id", "name": "公海分组", "base": "1", "require": "1", "group_id": "3", "hint": "", "field_type": "3", "ext_info": [], "default": "0", "disable_flag": "0", "is_editable": 1, "unique_check": "0", "unique_prevent": "1", "unique_message": "", "value": 0}, {"id": "cus_tag", "name": "客户标签", "base": "1", "require": "0", "group_id": "3", "hint": "", "field_type": "7", "ext_info": [], "default": "", "disable_flag": "0", "is_editable": 1, "unique_check": "0", "unique_prevent": "1", "unique_message": "", "value": []}, {"id": "product_group_ids", "name": "产品分组", "base": "1", "require": "0", "group_id": "3", "hint": "", "field_type": "7", "ext_info": [], "default": "", "disable_flag": "0", "is_editable": 1, "unique_check": "0", "unique_prevent": "1", "unique_message": "", "value": ""}, {"id": "trail_status", "name": "客户阶段", "base": "1", "require": "0", "group_id": "3", "hint": "", "field_type": "3", "ext_info": [], "default": "", "disable_flag": "0", "is_editable": 1, "unique_check": "0", "unique_prevent": "1", "unique_message": "", "value": ""}]}, {"group_id": 4, "name": "联系信息", "fields": [{"id": "homepage", "name": "公司网址", "base": "1", "require": "0", "group_id": "4", "hint": "", "field_type": "1", "ext_info": [], "default": "", "disable_flag": "0", "is_editable": 1, "unique_check": "1", "unique_prevent": "1", "unique_message": "", "value": ""}, {"id": "fax", "name": "传真", "base": "1", "require": "0", "group_id": "4", "hint": "", "field_type": "1", "ext_info": [], "default": "", "disable_flag": "0", "is_editable": 1, "unique_check": "1", "unique_prevent": "1", "unique_message": "", "value": ""}, {"id": "tel", "name": "座机", "base": "1", "require": "0", "group_id": "4", "hint": "", "field_type": "1", "ext_info": [], "default": "", "disable_flag": "0", "is_editable": 1, "unique_check": "1", "unique_prevent": "1", "unique_message": "", "value": {"tel_area_code": "", "tel": ""}}, {"id": "address", "name": "详细地址", "base": "1", "require": "0", "group_id": "4", "hint": "", "field_type": "1", "ext_info": [], "default": "", "disable_flag": "0", "is_editable": 1, "unique_check": "0", "unique_prevent": "1", "unique_message": "", "value": ""}, {"id": "lonlat", "name": "经维度坐标", "base": "1", "require": "0", "group_id": "4", "hint": "", "field_type": "1", "ext_info": [], "default": "", "disable_flag": "1", "is_editable": "1", "unique_check": "0", "unique_prevent": "1", "unique_message": "", "value": {"longitude": 0, "latitude": 0}}]}, {"group_id": 5, "name": "其他信息", "fields": [{"id": "415564998548", "name": "公海分组", "base": "0", "require": "0", "group_id": "5", "hint": "", "field_type": "3", "ext_info": ["公海公共分组"], "default": "", "disable_flag": "0", "is_editable": 1, "unique_check": "0", "unique_prevent": "1", "unique_message": "", "value": ""}, {"id": "remark", "name": "公司备注", "base": "1", "require": "0", "group_id": "5", "hint": "", "field_type": "2", "ext_info": [], "default": "", "disable_flag": "0", "is_editable": 1, "unique_check": "0", "unique_prevent": "1", "unique_message": "", "value": ""}, {"id": "image_list", "name": "公司logo", "base": "1", "require": "0", "group_id": "5", "hint": "", "field_type": "6", "ext_info": [], "default": "", "disable_flag": "0", "is_editable": 1, "unique_check": "0", "unique_prevent": "1", "unique_message": "", "value": []}, {"id": "415235694607", "name": "锪唿苏骇绋痨夼胭茸卦", "base": "0", "require": "0", "group_id": "5", "hint": "", "field_type": "1", "ext_info": [], "default": "", "disable_flag": "0", "is_editable": 1, "unique_check": "0", "unique_prevent": "1", "unique_message": "", "value": ""}, {"id": "415241505119", "name": "珉悫绀妆蛾圃茌京矗薷", "base": "0", "require": "0", "group_id": "5", "hint": "", "field_type": "1", "ext_info": [], "default": "", "disable_flag": "0", "is_editable": 1, "unique_check": "0", "unique_prevent": "1", "unique_message": "", "value": ""}, {"id": "4558350545349", "name": "自定义1", "base": "0", "require": "0", "group_id": "5", "hint": "", "field_type": "1", "ext_info": [], "default": "", "disable_flag": "0", "is_editable": 1, "unique_check": "0", "unique_prevent": "1", "unique_message": "", "value": ""}]}], "customers": [{"customer_id": 0, "company_id": 0, "email": "", "name": "", "main_customer_flag": 0, "fields": [{"id": "name", "name": "昵称", "base": "1", "require": "1", "hint": "", "field_type": "1", "ext_info": [], "disable_flag": "0", "is_editable": 1, "default": "", "type": "5", "unique_check": "0", "unique_prevent": "1", "unique_message": "", "value": "AutoApi_3ddbd2240bf", "group_id": 0}, {"id": "email", "name": "邮箱", "base": "1", "require": "0", "hint": "", "field_type": "1", "ext_info": [], "disable_flag": "0", "is_editable": 1, "default": "", "type": "5", "unique_check": "1", "unique_prevent": "1", "unique_message": "", "value": "<EMAIL>", "group_id": 0}, {"id": "post_grade", "name": "职级", "base": "1", "require": "0", "hint": "", "field_type": "3", "ext_info": [], "disable_flag": "0", "is_editable": 1, "default": "", "type": "5", "unique_check": "0", "unique_prevent": "1", "unique_message": "", "value": "", "group_id": 0}, {"id": "post", "name": "职位", "base": "1", "require": "0", "hint": "", "field_type": "1", "ext_info": [], "disable_flag": "0", "is_editable": 1, "default": "", "type": "5", "unique_check": "0", "unique_prevent": "1", "unique_message": "", "value": "", "group_id": 0}, {"id": "tel_list", "name": "联系电话", "base": "1", "require": "0", "hint": "", "field_type": "1", "ext_info": [], "disable_flag": "0", "is_editable": 1, "default": "", "type": "5", "unique_check": "1", "unique_prevent": "1", "unique_message": "", "value": [], "group_id": 0}, {"id": "contact", "name": "社交平台", "base": "1", "require": "0", "hint": "", "field_type": "2", "ext_info": [], "disable_flag": "0", "is_editable": 1, "default": "", "type": "5", "unique_check": "1", "unique_prevent": "1", "unique_message": "", "value": [], "group_id": 0}, {"id": "birth", "name": "生日", "base": "1", "require": "0", "hint": "", "field_type": "4", "ext_info": [], "disable_flag": "0", "is_editable": 1, "default": "", "type": "5", "unique_check": "0", "unique_prevent": "1", "unique_message": "", "value": "", "group_id": 0}, {"id": "main_customer_flag", "name": "主要联系人", "base": "1", "require": "0", "hint": "", "field_type": "3", "ext_info": [], "disable_flag": "0", "is_editable": 1, "default": "", "type": "5", "unique_check": "0", "unique_prevent": "1", "unique_message": "", "value": 1, "group_id": 0}, {"id": "gender", "name": "性别", "base": "1", "require": "0", "hint": "", "field_type": "3", "ext_info": [], "disable_flag": "0", "is_editable": 1, "default": "", "type": "5", "unique_check": "0", "unique_prevent": "1", "unique_message": "", "value": "", "group_id": 0}, {"id": "image_list", "name": "头像/名片", "base": "1", "require": "0", "hint": "", "field_type": "6", "ext_info": [], "disable_flag": "0", "is_editable": 1, "default": "", "type": "5", "unique_check": "0", "unique_prevent": "1", "unique_message": "", "value": [], "group_id": 0}, {"id": "remark", "name": "联系人备注", "base": "1", "require": "0", "hint": "", "field_type": "2", "ext_info": [], "disable_flag": "0", "is_editable": 1, "default": "", "type": "5", "unique_check": "0", "unique_prevent": "1", "unique_message": "", "value": "", "group_id": 0}]}]}',
            'company_id'   => $companyId,
//            'discovery_company_id' => 'ebeefd31740dd72002a3e5e32417f86c',
            'lead_id'      => 0,
            'archive_flag' => $archive_flag,
            'check_public' =>1,
//            'mail_id' => $mail_id,
//            'discovery_company_id' => '514543b32ab2ea6b',
        ];

        $this->callAction('saveCompanyData', $params);
        $this->responseOk();
    }

    public function testArchiveLeadToCompany()
    {
//        $lead = $this->lead;

        static::loginUser(11857352);
        $leadId = 32897216;
        Lead::model()->updateByPk($leadId, ['is_archive' => 1]);
        foreach (LeadCustomer::model()->findAllByAttributes(['lead_id' => $leadId]) as $leadCustomer) {
            $leadCustomer->is_archive = 1;
            $leadCustomer->save();
        }
//        $companyId = 32693721;
        $companyId = 0;

//        $companyId = 32621413;
//        $leadId = $lead->lead_id;
        $params = [
            'lead_id'    => $leadId,
            'company_id' => $companyId,
        ];
        $data = $this->callAction('archive', $params, 'LeadRead');
        $data = $data['data'];
        $data['company'][0]['fields'][0]['value'] = $this->faker()->company;

        array_walk($data['company'], function (&$groupField) {
            array_walk($groupField['fields'], function (&$field) {
                if ($field['id'] == 'pool_id') {
                    $field['value'] = 0;
                }
            });
        });
        array_walk($data['customers'][0]['fields'], function (&$field) {
            if ($field['id'] == 'main_customer_flag') {
                $field['value'] = 1;
            }
        });
//        $company = new \common\library\customer_v3\company\orm\Company(static::$clientId, $companyId);
//        $company->getFormatter()->detailInfoSetting();
//        $companyData = $company->getAttributes();
//        $data = [
//            'company'   => $companyData['company'],
//            'customers' => $companyData['customers'],
//        ];
//        $data['customers'][0]['fields'][1]['value'] = $this->faker()->email;
        $params = [
            'company_id' => $companyId,
            'data'       => json_encode($data),
            'lead_id'    => $leadId
        ];

        $saveCompany = $this->callAction('saveCompanyData', $params);
        $this->responseOk();
//
//        $this->seeInDatabase(Lead::class, [
//            'client_id'  => static::$clientId,
//            'company_id' => $companyId,
//            'lead_id'    => $leadId,
//        ]);
//
//        foreach ($data['customers'] as $leadCustomer) {
//            $this->seeInDatabase(LeadCustomer::class, [
//                'client_id'           => static::$clientId,
//                'lead_id'             => $leadId,
//                'company_id'          => $companyId,
//                'company_customer_id' => $leadCustomer['customer_id'],
//                'customer_id'         => $leadCustomer['source_customer_id'],
//            ]);
//        }

    }

    public function testSaveCustomer()
    {
//        static::loginUser(765);
//        $companyId = 21774532;
//        $customerId = 32952117;
//        $customer = new \common\library\customer_v3\customer\orm\Customer(static::$clientId, $customerId);
//        $customer->getFormatter()->detailInfoSetting();
//        $data = $customer->getAttributes();
//        array_walk($data['fields'], function (&$field) {
//            if ($field['id'] == 'main_customer_flag') {
//                $field['value'] = 1;
//            }
//        });
//        $data['main_customer_flag'] = 1;
//
//        $data = json_decode('{"customer_id":0,"company_id":1100242952,"email":"","name":"","main_customer_flag":0,"fields":[{"id":"email","name":"邮箱","base":"1","require":"0","hint":"","field_type":"1","ext_info":[],"disable_flag":"0","is_editable":"1","default":"","type":"5","unique_check":"1","unique_prevent":"1","unique_message":"邮箱已存在","value":"<EMAIL>","group_id":0,"field_customer_id":0,"format":"<EMAIL>"},{"id":"name","name":"昵称","base":"1","require":"0","hint":"","field_type":"1","ext_info":[""],"disable_flag":"0","is_editable":"1","default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"test33","group_id":0,"field_customer_id":0,"format":"test33"},{"id":"post_grade","name":"职级","base":"1","require":"0","hint":"","field_type":"3","ext_info":[""],"disable_flag":"0","is_editable":"1","default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0,"field_customer_id":0,"format":""},{"id":"post","name":"职位","base":"1","require":"0","hint":"","field_type":"1","ext_info":[],"disable_flag":"0","is_editable":"1","default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0,"field_customer_id":0,"format":""},{"id":"birth","name":"生日","base":"1","require":"0","hint":"","field_type":"4","ext_info":[],"disable_flag":"0","is_editable":"1","default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0,"field_customer_id":0,"format":""},{"id":"main_customer_flag","name":"主要联系人","base":"1","require":"0","hint":"","field_type":"3","ext_info":[],"disable_flag":"0","is_editable":"1","default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0,"field_customer_id":0,"format":""},{"id":"gender","name":"性别","base":"1","require":"0","hint":"","field_type":"3","ext_info":[],"disable_flag":"0","is_editable":"1","default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0,"field_customer_id":0,"format":""},{"id":"tel_list","name":"联系电话","base":"1","require":"0","hint":"","field_type":"1","ext_info":[""],"disable_flag":"0","is_editable":"1","default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":[["","(86) 15817487446"]],"group_id":0,"field_customer_id":0,"format":"[[\"\",\"(86) 15817487446\"]]"},{"id":"contact","name":"社交平台","base":"1","require":"0","hint":"","field_type":"2","ext_info":[""],"disable_flag":"0","is_editable":"1","default":"","type":"5","unique_check":"1","unique_prevent":"1","unique_message":"社交平台不允许重复","value":[],"group_id":0,"field_customer_id":0,"format":""},{"id":"remark","name":"备注","base":"1","require":"0","hint":"","field_type":"2","ext_info":[""],"disable_flag":"0","is_editable":"1","default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0,"field_customer_id":0,"format":""},{"id":"image_list","name":"图片","base":"1","require":"0","hint":" ","field_type":"6","ext_info":[],"disable_flag":"0","is_editable":"1","default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":[],"group_id":0,"field_customer_id":0,"format":"[]"},{"id":"40444319","name":"sharon下拉多选","base":"0","require":"0","hint":"","field_type":"7","ext_info":["礼品","礼物","礼品/礼物/圣诞老人"],"disable_flag":"0","is_editable":"1","default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0,"field_customer_id":0,"format":""},{"id":"1100149382","name":"lora测试联系人信息－多行","base":"0","require":"0","hint":"","field_type":"2","ext_info":[],"disable_flag":"0","is_editable":"1","default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0,"field_customer_id":0,"format":""},{"id":"38722965","name":"下拉多选-联系","base":"0","require":"0","hint":"","field_type":"7","ext_info":["Who","AM","I","？"],"disable_flag":"0","is_editable":"1","default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0,"field_customer_id":0,"format":""},{"id":"1100784721","name":"1029联系人下拉多选","base":"0","require":"0","hint":"","field_type":"7","ext_info":["东","南","西","北"],"disable_flag":"0","is_editable":"1","default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0,"field_customer_id":0,"format":""},{"id":"1100784727","name":"1029联系人下拉单选","base":"0","require":"0","hint":"","field_type":"3","ext_info":["肯德基","金拱门","必胜客","就巴黎圣母院修复开展合作，中国专家将参与巴黎圣母院修复工作。  根据这份文件，中法双方将在2020年确定巴黎圣母院保护修复合作的主题、模式及中方专家人选，尽早选派中国专家与法国团队共同参与现场修复工作"],"disable_flag":"0","is_editable":"1","default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0,"field_customer_id":0,"format":""},{"id":"1100784729","name":"1029联系人时间","base":"0","require":"0","hint":"","field_type":"4","ext_info":[],"disable_flag":"0","is_editable":"1","default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0,"field_customer_id":0,"format":""},{"id":"1100784733","name":"1029联系人日期和时间","base":"0","require":"0","hint":"","field_type":"10","ext_info":[],"disable_flag":"0","is_editable":"1","default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0,"field_customer_id":0,"format":""},{"id":"1100784740","name":"1029联系人文本","base":"0","require":"0","hint":"","field_type":"1","ext_info":[],"disable_flag":"0","is_editable":"1","default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0,"field_customer_id":0,"format":""},{"id":"1100784742","name":"1029联系人多行文本","base":"0","require":"0","hint":"","field_type":"2","ext_info":[],"disable_flag":"0","is_editable":"1","default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0,"field_customer_id":0,"format":""},{"id":"1101118040","name":"1202数值-联系人","base":"0","require":"0","hint":"","field_type":"5","ext_info":[],"disable_flag":"0","is_editable":"1","default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0,"field_customer_id":0,"format":""},{"id":"37847161","name":"lora日期测试","base":"0","require":"0","hint":"ee","field_type":"4","ext_info":[],"disable_flag":"0","is_editable":"1","default":"2018-12-07","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0,"field_customer_id":0,"format":""},{"id":"37847164","name":"lora日期－时间","base":"0","require":"0","hint":"","field_type":"10","ext_info":[],"disable_flag":"0","is_editable":"1","default":"2019-04-02 10:45:56","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0,"field_customer_id":0,"format":""},{"id":"37847168","name":"lora下拉单选","base":"0","require":"0","hint":"下拉提示","field_type":"3","ext_info":["lora1","lora2"],"disable_flag":"0","is_editable":"1","default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0,"field_customer_id":0,"format":""},{"id":"1101160773","name":"lora客户联系－文本","base":"0","require":"0","hint":"","field_type":"1","ext_info":[],"disable_flag":"0","is_editable":"1","default":"","type":"5","unique_check":"1","unique_prevent":"1","unique_message":"啊啊啊啊，测试文本","value":"","group_id":0,"field_customer_id":0,"format":""},{"id":"1101160774","name":"lora客户联系－多行文本","base":"0","require":"0","hint":"","field_type":"2","ext_info":[],"disable_flag":"0","is_editable":"1","default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0,"field_customer_id":0,"format":""},{"id":"1101160786","name":"lora客户联系－下拉多选","base":"0","require":"0","hint":"","field_type":"7","ext_info":["lora1","lora2","lora3"],"disable_flag":"0","is_editable":"1","default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0,"field_customer_id":0,"format":""},{"id":"1101160787","name":"lora客户联系－数值","base":"0","require":"0","hint":"","field_type":"5","ext_info":[],"disable_flag":"0","is_editable":"1","default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0,"field_customer_id":0,"format":""},{"id":"37847156","name":"lora测试自定义","base":"0","require":"0","hint":"","field_type":"1","ext_info":[],"disable_flag":"1","is_editable":"1","default":"11111","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0,"field_customer_id":0,"format":""},{"id":"38698472","name":"lora－日期","base":"0","require":"0","hint":"","field_type":"4","ext_info":[],"disable_flag":"1","is_editable":"1","default":"2019-04-02","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0,"field_customer_id":0,"format":""},{"id":"39496171","name":"xhl测试多选","base":"0","require":"0","hint":"","field_type":"7","ext_info":["多选1号","多选2号","多选3号","多选4号"],"disable_flag":"1","is_editable":"1","default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0,"field_customer_id":0,"format":""}]}');
//        $companyId = 1100242952;
//        $customerId = 0;
//        $this->loginAsJolie();
//
//        $companyId = 'a';
//        $params = [
//            'company_id'  => $companyId,
//            'data'        => json_encode($data),
//            'customer_id' => $customerId,
//        ];


	    $this->loginAsRuby();

	    $params = 'customer_id=0&company_id=3081976771&data=%7B%22customer_id%22%3A0%2C%22company_id%22%3A3081976771%2C%22email%22%3A%22%22%2C%22name%22%3A%22%22%2C%22main_customer_flag%22%3A1%2C%22fields%22%3A%5B%7B%22id%22%3A%22main_customer_flag%22%2C%22name%22%3A%22%E4%B8%BB%E8%A6%81%E8%81%94%E7%B3%BB%E4%BA%BA%22%2C%22base%22%3A%221%22%2C%22require%22%3A%220%22%2C%22hint%22%3A%22%22%2C%22field_type%22%3A%223%22%2C%22ext_info%22%3A%5B%5D%2C%22disable_flag%22%3A%220%22%2C%22is_editable%22%3A1%2C%22default%22%3A%22%22%2C%22type%22%3A%225%22%2C%22unique_check%22%3A%220%22%2C%22unique_prevent%22%3A%221%22%2C%22unique_message%22%3A%22%22%2C%22value%22%3Atrue%2C%22group_id%22%3A0%7D%2C%7B%22id%22%3A%22contact%22%2C%22name%22%3A%22%E7%A4%BE%E4%BA%A4%E5%B9%B3%E5%8F%B0%22%2C%22base%22%3A%221%22%2C%22require%22%3A%220%22%2C%22hint%22%3A%22%22%2C%22field_type%22%3A%222%22%2C%22ext_info%22%3A%5B%22%22%5D%2C%22disable_flag%22%3A%220%22%2C%22is_editable%22%3A1%2C%22default%22%3A%22%22%2C%22type%22%3A%225%22%2C%22unique_check%22%3A%221%22%2C%22unique_prevent%22%3A%222%22%2C%22unique_message%22%3A%22%22%2C%22value%22%3A%5B%5D%2C%22group_id%22%3A0%7D%2C%7B%22id%22%3A%22tel_list%22%2C%22name%22%3A%22%E8%81%94%E7%B3%BB%E7%94%B5%E8%AF%9D%22%2C%22base%22%3A%221%22%2C%22require%22%3A%220%22%2C%22hint%22%3A%22%22%2C%22field_type%22%3A%221%22%2C%22ext_info%22%3A%5B%5D%2C%22disable_flag%22%3A%220%22%2C%22is_editable%22%3A1%2C%22default%22%3A%22%22%2C%22type%22%3A%225%22%2C%22unique_check%22%3A%221%22%2C%22unique_prevent%22%3A%222%22%2C%22unique_message%22%3A%22%22%2C%22value%22%3A%5B%5D%2C%22group_id%22%3A0%7D%2C%7B%22id%22%3A%22birth%22%2C%22name%22%3A%22%E7%94%9F%E6%97%A5%22%2C%22base%22%3A%221%22%2C%22require%22%3A%220%22%2C%22hint%22%3A%22%22%2C%22field_type%22%3A%224%22%2C%22ext_info%22%3A%5B%22%22%5D%2C%22disable_flag%22%3A%220%22%2C%22is_editable%22%3A1%2C%22default%22%3A%22%22%2C%22type%22%3A%225%22%2C%22unique_check%22%3A%220%22%2C%22unique_prevent%22%3A%221%22%2C%22unique_message%22%3A%22%22%2C%22value%22%3A%22%22%2C%22group_id%22%3A0%7D%2C%7B%22id%22%3A%22name%22%2C%22name%22%3A%22%E6%98%B5%E7%A7%B0%22%2C%22base%22%3A%221%22%2C%22require%22%3A%220%22%2C%22hint%22%3A%22%22%2C%22field_type%22%3A%221%22%2C%22ext_info%22%3A%5B%5D%2C%22disable_flag%22%3A%220%22%2C%22is_editable%22%3A1%2C%22default%22%3A%22%22%2C%22type%22%3A%225%22%2C%22unique_check%22%3A%220%22%2C%22unique_prevent%22%3A%221%22%2C%22unique_message%22%3A%22%22%2C%22value%22%3A%223333%22%2C%22group_id%22%3A0%7D%2C%7B%22id%22%3A%22email%22%2C%22name%22%3A%22%E9%82%AE%E7%AE%B1%22%2C%22base%22%3A%221%22%2C%22require%22%3A%220%22%2C%22hint%22%3A%22%22%2C%22field_type%22%3A%221%22%2C%22ext_info%22%3A%5B%5D%2C%22disable_flag%22%3A%220%22%2C%22is_editable%22%3A1%2C%22default%22%3A%22%22%2C%22type%22%3A%225%22%2C%22unique_check%22%3A%221%22%2C%22unique_prevent%22%3A%222%22%2C%22unique_message%22%3A%22%22%2C%22value%22%3A%22%22%2C%22group_id%22%3A0%7D%2C%7B%22id%22%3A%22post_grade%22%2C%22name%22%3A%22%E8%81%8C%E7%BA%A7%22%2C%22base%22%3A%221%22%2C%22require%22%3A%220%22%2C%22hint%22%3A%22%22%2C%22field_type%22%3A%223%22%2C%22ext_info%22%3A%5B%22%22%5D%2C%22disable_flag%22%3A%220%22%2C%22is_editable%22%3A1%2C%22default%22%3A%22%22%2C%22type%22%3A%225%22%2C%22unique_check%22%3A%220%22%2C%22unique_prevent%22%3A%221%22%2C%22unique_message%22%3A%22%22%2C%22value%22%3A%22%22%2C%22group_id%22%3A0%7D%2C%7B%22id%22%3A%22post%22%2C%22name%22%3A%22%E8%81%8C%E4%BD%8D%22%2C%22base%22%3A%221%22%2C%22require%22%3A%220%22%2C%22hint%22%3A%22%22%2C%22field_type%22%3A%221%22%2C%22ext_info%22%3A%5B%22%22%5D%2C%22disable_flag%22%3A%220%22%2C%22is_editable%22%3A1%2C%22default%22%3A%22%22%2C%22type%22%3A%225%22%2C%22unique_check%22%3A%220%22%2C%22unique_prevent%22%3A%221%22%2C%22unique_message%22%3A%22%22%2C%22value%22%3A%22%22%2C%22group_id%22%3A0%7D%2C%7B%22id%22%3A%221127474215%22%2C%22name%22%3A%22%E8%81%8C%E4%BD%8D%E4%BF%A1%E6%81%AF%22%2C%22base%22%3A%220%22%2C%22require%22%3A%220%22%2C%22hint%22%3A%22%22%2C%22field_type%22%3A%221%22%2C%22ext_info%22%3A%5B%5D%2C%22disable_flag%22%3A%220%22%2C%22is_editable%22%3A1%2C%22default%22%3A%22%22%2C%22type%22%3A%225%22%2C%22unique_check%22%3A%220%22%2C%22unique_prevent%22%3A%221%22%2C%22unique_message%22%3A%22%22%2C%22value%22%3A%22%22%2C%22group_id%22%3A0%7D%2C%7B%22id%22%3A%22gender%22%2C%22name%22%3A%22%E6%80%A7%E5%88%AB%22%2C%22base%22%3A%221%22%2C%22require%22%3A%220%22%2C%22hint%22%3A%22%22%2C%22field_type%22%3A%223%22%2C%22ext_info%22%3A%5B%22%22%5D%2C%22disable_flag%22%3A%220%22%2C%22is_editable%22%3A1%2C%22default%22%3A%22%22%2C%22type%22%3A%225%22%2C%22unique_check%22%3A%220%22%2C%22unique_prevent%22%3A%221%22%2C%22unique_message%22%3A%22%22%2C%22value%22%3A%22%22%2C%22group_id%22%3A0%7D%2C%7B%22id%22%3A%22remark%22%2C%22name%22%3A%22%E8%81%94%E7%B3%BB%E4%BA%BA%E5%A4%87%E6%B3%A8%22%2C%22base%22%3A%221%22%2C%22require%22%3A%220%22%2C%22hint%22%3A%22%22%2C%22field_type%22%3A%222%22%2C%22ext_info%22%3A%5B%22%22%5D%2C%22disable_flag%22%3A%220%22%2C%22is_editable%22%3A1%2C%22default%22%3A%22%22%2C%22type%22%3A%225%22%2C%22unique_check%22%3A%220%22%2C%22unique_prevent%22%3A%221%22%2C%22unique_message%22%3A%22%22%2C%22value%22%3A%22%22%2C%22group_id%22%3A0%7D%2C%7B%22id%22%3A%223082106530%22%2C%22name%22%3A%22%E6%B5%8B%E8%AF%95%E8%81%94%E7%B3%BB%E4%BA%BA-tv-%E6%96%87%E6%9C%AC%22%2C%22base%22%3A%220%22%2C%22require%22%3A%220%22%2C%22hint%22%3A%22%E8%BF%99%E6%98%AF%E8%81%94%E7%B3%BB%E4%BA%BA%E5%BF%85%E5%A1%AB%E6%8F%90%E7%A4%BA%22%2C%22field_type%22%3A%221%22%2C%22ext_info%22%3A%5B%5D%2C%22disable_flag%22%3A%220%22%2C%22is_editable%22%3A1%2C%22default%22%3A%22%22%2C%22type%22%3A%225%22%2C%22unique_check%22%3A%220%22%2C%22unique_prevent%22%3A%221%22%2C%22unique_message%22%3A%22%22%2C%22value%22%3A%22%22%2C%22group_id%22%3A0%7D%2C%7B%22id%22%3A%223082106531%22%2C%22name%22%3A%22%E6%B5%8B%E8%AF%95%E8%81%94%E7%B3%BB%E4%BA%BA-tv-%E5%A4%9A%E8%A1%8C%E6%96%87%E6%9C%AC%22%2C%22base%22%3A%220%22%2C%22require%22%3A%220%22%2C%22hint%22%3A%22%22%2C%22field_type%22%3A%222%22%2C%22ext_info%22%3A%5B%5D%2C%22disable_flag%22%3A%220%22%2C%22is_editable%22%3A1%2C%22default%22%3A%22%22%2C%22type%22%3A%225%22%2C%22unique_check%22%3A%220%22%2C%22unique_prevent%22%3A%221%22%2C%22unique_message%22%3A%22%EF%BC%9F%EF%BC%9F%EF%BC%9F%EF%BC%9F%22%2C%22value%22%3A%22%22%2C%22group_id%22%3A0%7D%2C%7B%22id%22%3A%223082106535%22%2C%22name%22%3A%22%E6%B5%8B%E8%AF%95%E8%81%94%E7%B3%BB%E4%BA%BA-tv-%E5%8D%95%E9%80%89%22%2C%22base%22%3A%220%22%2C%22require%22%3A%220%22%2C%22hint%22%3A%22%E8%BF%99%E6%98%AF%E5%8D%95%E9%80%89%E6%8F%90%E7%A4%BA%E5%95%8A%22%2C%22field_type%22%3A%223%22%2C%22ext_info%22%3A%5B%2212%22%2C%2213%22%2C%2214%22%2C%2215%22%2C%2216%22%5D%2C%22disable_flag%22%3A%220%22%2C%22is_editable%22%3A1%2C%22default%22%3A%22%22%2C%22type%22%3A%225%22%2C%22unique_check%22%3A%220%22%2C%22unique_prevent%22%3A%221%22%2C%22unique_message%22%3A%22%22%2C%22value%22%3A%22%22%2C%22group_id%22%3A0%7D%2C%7B%22id%22%3A%223082106536%22%2C%22name%22%3A%22%E6%B5%8B%E8%AF%95%E8%81%94%E7%B3%BB%E4%BA%BA-tv-%E6%97%A5%E6%9C%9F%22%2C%22base%22%3A%220%22%2C%22require%22%3A%220%22%2C%22hint%22%3A%22%22%2C%22field_type%22%3A%224%22%2C%22ext_info%22%3A%5B%5D%2C%22disable_flag%22%3A%220%22%2C%22is_editable%22%3A1%2C%22default%22%3A%22%22%2C%22type%22%3A%225%22%2C%22unique_check%22%3A%220%22%2C%22unique_prevent%22%3A%221%22%2C%22unique_message%22%3A%22%22%2C%22value%22%3A%22%22%2C%22group_id%22%3A0%7D%2C%7B%22id%22%3A%223082106537%22%2C%22name%22%3A%22%E6%B5%8B%E8%AF%95%E8%81%94%E7%B3%BB%E4%BA%BA-tv-%E6%95%B0%E5%80%BC%22%2C%22base%22%3A%220%22%2C%22require%22%3A%220%22%2C%22hint%22%3A%22%22%2C%22field_type%22%3A%225%22%2C%22ext_info%22%3A%5B%5D%2C%22disable_flag%22%3A%220%22%2C%22is_editable%22%3A1%2C%22default%22%3A%22%22%2C%22type%22%3A%225%22%2C%22unique_check%22%3A%220%22%2C%22unique_prevent%22%3A%221%22%2C%22unique_message%22%3A%22%22%2C%22value%22%3A%22%22%2C%22group_id%22%3A0%7D%2C%7B%22id%22%3A%223082106539%22%2C%22name%22%3A%22%E6%B5%8B%E8%AF%95%E8%81%94%E7%B3%BB%E4%BA%BA-tv-%E4%B8%8B%E6%8B%89%E5%A4%9A%E9%80%89%22%2C%22base%22%3A%220%22%2C%22require%22%3A%220%22%2C%22hint%22%3A%22%22%2C%22field_type%22%3A%227%22%2C%22ext_info%22%3A%5B%2212%22%2C%2223%22%2C%2234%22%2C%2245%22%2C%2256%22%5D%2C%22disable_flag%22%3A%220%22%2C%22is_editable%22%3A1%2C%22default%22%3A%22%22%2C%22type%22%3A%225%22%2C%22unique_check%22%3A%220%22%2C%22unique_prevent%22%3A%221%22%2C%22unique_message%22%3A%22%22%2C%22value%22%3A%22%22%2C%22group_id%22%3A0%7D%2C%7B%22id%22%3A%223082106540%22%2C%22name%22%3A%22%E6%B5%8B%E8%AF%95%E8%81%94%E7%B3%BB%E4%BA%BA-tv-%E6%97%A5%E6%9C%9F%E6%97%B6%E9%97%B4%22%2C%22base%22%3A%220%22%2C%22require%22%3A%220%22%2C%22hint%22%3A%22%22%2C%22field_type%22%3A%2210%22%2C%22ext_info%22%3A%5B%5D%2C%22disable_flag%22%3A%220%22%2C%22is_editable%22%3A1%2C%22default%22%3A%22%22%2C%22type%22%3A%225%22%2C%22unique_check%22%3A%220%22%2C%22unique_prevent%22%3A%221%22%2C%22unique_message%22%3A%22%22%2C%22value%22%3A%22%22%2C%22group_id%22%3A0%7D%2C%7B%22id%22%3A%223117455615%22%2C%22name%22%3A%22kaia%22%2C%22base%22%3A%220%22%2C%22require%22%3A%220%22%2C%22hint%22%3A%22%22%2C%22field_type%22%3A%221%22%2C%22ext_info%22%3A%5B%5D%2C%22disable_flag%22%3A%220%22%2C%22is_editable%22%3A1%2C%22default%22%3A%22%22%2C%22type%22%3A%225%22%2C%22unique_check%22%3A%220%22%2C%22unique_prevent%22%3A%221%22%2C%22unique_message%22%3A%22%22%2C%22value%22%3A%22%22%2C%22group_id%22%3A0%7D%2C%7B%22id%22%3A%22image_list%22%2C%22name%22%3A%22%E5%A4%B4%E5%83%8F%2F%E5%90%8D%E7%89%87%22%2C%22base%22%3A%221%22%2C%22require%22%3A%220%22%2C%22hint%22%3A%22%20%22%2C%22field_type%22%3A%226%22%2C%22ext_info%22%3A%5B%22%22%5D%2C%22disable_flag%22%3A%220%22%2C%22is_editable%22%3A1%2C%22default%22%3A%22%22%2C%22type%22%3A%225%22%2C%22unique_check%22%3A%220%22%2C%22unique_prevent%22%3A%221%22%2C%22unique_message%22%3A%22%22%2C%22value%22%3A%5B%5D%2C%22group_id%22%3A0%7D%2C%7B%22id%22%3A%221119201734%22%2C%22name%22%3A%22%E8%87%AA%E5%AE%9A%E4%B9%89%E8%81%94%E7%B3%BB%E5%AD%97%E6%AE%B5~%22%2C%22base%22%3A%220%22%2C%22require%22%3A%220%22%2C%22hint%22%3A%22%E6%88%91%E6%B7%BB%E5%8A%A0%E7%9A%84~%22%2C%22field_type%22%3A%225%22%2C%22ext_info%22%3A%5B%5D%2C%22disable_flag%22%3A%220%22%2C%22is_editable%22%3A1%2C%22default%22%3A%22%22%2C%22type%22%3A%225%22%2C%22unique_check%22%3A%220%22%2C%22unique_prevent%22%3A%221%22%2C%22unique_message%22%3A%22%22%2C%22value%22%3A%22%22%2C%22group_id%22%3A0%7D%2C%7B%22id%22%3A%221*********%22%2C%22name%22%3A%22%2B%2B%2B%2B%2B%22%2C%22base%22%3A%220%22%2C%22require%22%3A%220%22%2C%22hint%22%3A%22%22%2C%22field_type%22%3A%221%22%2C%22ext_info%22%3A%5B%5D%2C%22disable_flag%22%3A%220%22%2C%22is_editable%22%3A1%2C%22default%22%3A%22%22%2C%22type%22%3A%225%22%2C%22unique_check%22%3A%221%22%2C%22unique_prevent%22%3A%221%22%2C%22unique_message%22%3A%22%22%2C%22value%22%3A%22%22%2C%22group_id%22%3A0%7D%2C%7B%22id%22%3A%221297594717%22%2C%22name%22%3A%22%2A%2A%2A%2A%2A%2A%2A%2A%2A%22%2C%22base%22%3A%220%22%2C%22require%22%3A%220%22%2C%22hint%22%3A%22%22%2C%22field_type%22%3A%224%22%2C%22ext_info%22%3A%5B%5D%2C%22disable_flag%22%3A%220%22%2C%22is_editable%22%3A1%2C%22default%22%3A%22%22%2C%22type%22%3A%225%22%2C%22unique_check%22%3A%220%22%2C%22unique_prevent%22%3A%221%22%2C%22unique_message%22%3A%22%22%2C%22value%22%3A%22%22%2C%22group_id%22%3A0%7D%5D%7D&check_public=1';

        $this->loginUser('<EMAIL>');
        $this->loginClient(351352);
        $params = [
            'company_id' => 4304119678,
            'data' => '{"customer_id":0,"company_id":4304119678,"email":"","name":"","main_customer_flag":0,"fields":[{"id":"email","name":"邮箱","base":"1","require":"0","hint":"","field_type":"1","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"1","unique_prevent":"1","unique_message":"邮箱已存在","value":"<EMAIL>","group_id":0},{"id":"name","name":"昵称","base":"1","require":"0","hint":"","field_type":"1","ext_info":[""],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"<EMAIL>","group_id":0},{"id":"post_grade","name":"职级","base":"1","require":"0","hint":"","field_type":"3","ext_info":[""],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"post","name":"职位","base":"1","require":"0","hint":"","field_type":"1","ext_info":[""],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"birth","name":"生日","base":"1","require":"0","hint":"","field_type":"4","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"main_customer_flag","name":"主要联系人","base":"1","require":"0","hint":"","field_type":"3","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"gender","name":"性别","base":"1","require":"0","hint":"","field_type":"3","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"tel_list","name":"联系电话","base":"1","require":"0","hint":"","field_type":"1","ext_info":[""],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":[],"group_id":0},{"id":"contact","name":"社交平台","base":"1","require":"0","hint":"","field_type":"2","ext_info":[""],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"1","unique_prevent":"1","unique_message":"社交平台不允许重复","value":[],"group_id":0},{"id":"remark","name":"备注","base":"1","require":"0","hint":"","field_type":"2","ext_info":[""],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"image_list","name":"图片","base":"1","require":"0","hint":" ","field_type":"6","ext_info":[""],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":[],"group_id":0},{"id":"40444319","name":"sharon下拉多选","base":"0","require":"0","hint":"","field_type":"7","ext_info":["礼品","礼物","礼品/礼物/圣诞老人"],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"1100149382","name":"lora测试联系人信息－多行","base":"0","require":"0","hint":"","field_type":"2","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"38722965","name":"下拉多选-联系","base":"0","require":"0","hint":"","field_type":"7","ext_info":["Who","AM","I","？"],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"1100784721","name":"1029联系人下拉多选","base":"0","require":"0","hint":"","field_type":"7","ext_info":["东","南","西","北"],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"1100784727","name":"1029联系人下拉单选","base":"0","require":"0","hint":"","field_type":"3","ext_info":["肯德基","金拱门","必胜客","就巴黎圣母院修复开展合作，中国专家将参与巴黎圣母院修复工作。 根据这份文件，中法双方将在2020年确定巴黎圣母院保护修复合作的主题、模式及中方专家人选，尽早选派中国专家与法国团队共同参与现场修复工作"],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"1100784729","name":"1029联系人时间","base":"0","require":"0","hint":"","field_type":"4","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"1100784733","name":"1029联系人日期和时间","base":"0","require":"0","hint":"","field_type":"10","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"1100784740","name":"1029联系人文本","base":"0","require":"0","hint":"","field_type":"1","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"1100784742","name":"1029联系人多行文本","base":"0","require":"0","hint":"","field_type":"2","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"1101118040","name":"1202数值-联系人","base":"0","require":"0","hint":"","field_type":"5","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"37847161","name":"lora日期测试","base":"0","require":"0","hint":"ee","field_type":"4","ext_info":[],"disable_flag":"0","is_editable":1,"default":"2018-12-07","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"2018-12-07","group_id":0},{"id":"37847164","name":"lora日期－时间","base":"0","require":"0","hint":"","field_type":"10","ext_info":[],"disable_flag":"0","is_editable":1,"default":"2019-04-02 10:45:56","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"2019-04-02 10:45:56","group_id":0},{"id":"37847168","name":"lora下拉单选","base":"0","require":"0","hint":"下拉提示","field_type":"3","ext_info":["lora1","lora2"],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"1101160773","name":"lora客户联系－文本","base":"0","require":"0","hint":"","field_type":"1","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"1","unique_prevent":"1","unique_message":"啊啊啊啊，测试文本","value":"","group_id":0},{"id":"1101160774","name":"lora客户联系－多行文本","base":"0","require":"0","hint":"","field_type":"2","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"1101160786","name":"lora客户联系－下拉多选","base":"0","require":"0","hint":"","field_type":"7","ext_info":["lora1","lora2","lora3"],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"1101160787","name":"lora客户联系－数值","base":"0","require":"0","hint":"","field_type":"5","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"1102561875","name":"客户联系－下拉单选","base":"0","require":"0","hint":"","field_type":"3","ext_info":["1","2"],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"1102647385","name":"wr自定义数值-联系","base":"0","require":"0","hint":"","field_type":"5","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"1103042023","name":"BINGTEST","base":"0","require":"0","hint":"","field_type":"7","ext_info":["112","222","2223","333"],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"1103950086","name":"ruby-多行文本框","base":"0","require":"0","hint":"","field_type":"2","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"38698472","name":"lora－日期","base":"0","require":"1","hint":"","field_type":"4","ext_info":[],"disable_flag":"0","is_editable":1,"default":"2019-04-02","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"2019-04-02","group_id":0}]}',
            
        ];
        $this->callAction('SaveCustomer', $params);
        $this->responseOk();
//
//        $this->seeInDatabase(CustomerModel::class, ['customer_id' => $customerId, 'main_customer_flag' => 1]);
//        $this->seeInDatabase(CompanyModel::class, ['company_id' => $companyId, 'main_customer' => $customerId]);
    }

    public function testDebugSaveCustomer()
    {
        $this->loginAsRuby();
        $data = '{"customer_id":0,"company_id":1105036664,"email":"","name":"","main_customer_flag":0,"fields":[{"id":"email","name":"邮箱","base":"1","require":"0","hint":"","field_type":"1","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"1","unique_prevent":"1","unique_message":"邮箱已存在","value":"<EMAIL>","group_id":0},{"id":"name","name":"昵称","base":"1","require":"0","hint":"","field_type":"1","ext_info":[""],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"<EMAIL>","group_id":0},{"id":"post_grade","name":"职级","base":"1","require":"0","hint":"","field_type":"3","ext_info":[""],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"post","name":"职位","base":"1","require":"0","hint":"","field_type":"1","ext_info":[""],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"birth","name":"生日","base":"1","require":"0","hint":"","field_type":"4","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"main_customer_flag","name":"主要联系人","base":"1","require":"0","hint":"","field_type":"3","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"gender","name":"性别","base":"1","require":"0","hint":"","field_type":"3","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"tel_list","name":"联系电话","base":"1","require":"0","hint":"","field_type":"1","ext_info":[""],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":[],"group_id":0},{"id":"contact","name":"社交平台","base":"1","require":"0","hint":"","field_type":"2","ext_info":[""],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"1","unique_prevent":"1","unique_message":"社交平台不允许重复","value":[],"group_id":0},{"id":"remark","name":"备注","base":"1","require":"0","hint":"","field_type":"2","ext_info":[""],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"image_list","name":"图片","base":"1","require":"0","hint":" ","field_type":"6","ext_info":[""],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":[],"group_id":0},{"id":"40444319","name":"sharon下拉多选","base":"0","require":"0","hint":"","field_type":"7","ext_info":["礼品","礼物","礼品/礼物/圣诞老人"],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"1100149382","name":"lora测试联系人信息－多行","base":"0","require":"0","hint":"","field_type":"2","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"38722965","name":"下拉多选-联系","base":"0","require":"0","hint":"","field_type":"7","ext_info":["Who","AM","I","？"],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"1100784721","name":"1029联系人下拉多选","base":"0","require":"0","hint":"","field_type":"7","ext_info":["东","南","西","北"],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"1100784727","name":"1029联系人下拉单选","base":"0","require":"0","hint":"","field_type":"3","ext_info":["肯德基","金拱门","必胜客","就巴黎圣母院修复开展合作，中国专家将参与巴黎圣母院修复工作。  根据这份文件，中法双方将在2020年确定巴黎圣母院保护修复合作的主题、模式及中方专家人选，尽早选派中国专家与法国团队共同参与现场修复工作"],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"1100784729","name":"1029联系人时间","base":"0","require":"0","hint":"","field_type":"4","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"1100784733","name":"1029联系人日期和时间","base":"0","require":"0","hint":"","field_type":"10","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"1100784740","name":"1029联系人文本","base":"0","require":"0","hint":"","field_type":"1","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"1100784742","name":"1029联系人多行文本","base":"0","require":"0","hint":"","field_type":"2","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"1101118040","name":"1202数值-联系人","base":"0","require":"0","hint":"","field_type":"5","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"37847161","name":"lora日期测试","base":"0","require":"0","hint":"ee","field_type":"4","ext_info":[],"disable_flag":"0","is_editable":1,"default":"2018-12-07","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"2018-12-07","group_id":0},{"id":"37847164","name":"lora日期－时间","base":"0","require":"0","hint":"","field_type":"10","ext_info":[],"disable_flag":"0","is_editable":1,"default":"2019-04-02 10:45:56","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"2019-04-02 10:45:56","group_id":0},{"id":"37847168","name":"lora下拉单选","base":"0","require":"0","hint":"下拉提示","field_type":"3","ext_info":["lora1","lora2"],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"1101160773","name":"lora客户联系－文本","base":"0","require":"0","hint":"","field_type":"1","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"1","unique_prevent":"1","unique_message":"啊啊啊啊，测试文本","value":"","group_id":0},{"id":"1101160774","name":"lora客户联系－多行文本","base":"0","require":"0","hint":"","field_type":"2","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"1101160786","name":"lora客户联系－下拉多选","base":"0","require":"0","hint":"","field_type":"7","ext_info":["lora1","lora2","lora3"],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"1101160787","name":"lora客户联系－数值","base":"0","require":"0","hint":"","field_type":"5","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"1102561875","name":"客户联系－下拉单选","base":"0","require":"0","hint":"","field_type":"3","ext_info":["1","2"],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"1102647385","name":"wr自定义数值-联系","base":"0","require":"0","hint":"","field_type":"5","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"1103042023","name":"BINGTEST","base":"0","require":"0","hint":"","field_type":"7","ext_info":["112","222","2223","333"],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"1103950086","name":"ruby-多行文本框","base":"0","require":"0","hint":"","field_type":"2","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"38698472","name":"lora－日期","base":"0","require":"1","hint":"","field_type":"4","ext_info":[],"disable_flag":"0","is_editable":1,"default":"2019-04-02","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"2019-04-02","group_id":0}]}';
        $params = [
            'data' => $data,
            'customer_id' => 0,
            'company_id' => 1105036664,
        ];
        $this->callAction('saveCustomer', $params);
        $this->responseOk();
    }

    public function testSetPool()
    {
//        $pools = \common\library\customer\pool\Helper::getUserPoolNameMap(static::$clientId, static::$userId);
//        $pool_id = current(array_keys($pools));
//        $company = CompanyModel::model()->findByAttributes([
//            'client_id'  => static::$clientId,
//            'user_id'    => '{}',
//            'is_archive' => 1
//        ]);
//
////        $company = CompanyModel::model()->findByAttributes(['client_id' => static::$clientId, 'user_id' => '{}', 'is_archive' => 1]);
//        $company_id = $company->company_id;
//        $company->pool_id = 0;
//        $company->save();
//        $this->echo($pool_id);
//        $this->echo($company_id);

//        $pool_not_exist = -1;
//        $params = [
//            'company_ids' => [$company->company_id],
//            'pool_id' => $pool_not_exist,
//        ];
//
//        $this->expectExceptionMessage('没有该公海分组权限');
//        $this->callAction('setPool', $params);

        $this->loginAsWeason3();
        $this->callAction('setPool', [
            'company_ids' => ['1133123765'],
//            'pool_id' => 0,
            'pool_id' => 3458246414,
        ]);
        $this->responseOk();
    }


    public function testDebugSetGroup()
    {
        $this->loginAsQiao();
        $this->callAction('setGroup', [
            'company_ids' => [2213534762],
            'group_id' => 1161833757,
//            'group_id' => 1132376090,
        ]);
        $this->responseOk();
    }

    public function testImport()
    {
        $this->loginAsKk();
//        $fileName = 'lead-import-2.xls';
        $fileName = 'H 客户-6A (2).xlsx';
        $fileFullPath = '/tmp/' . $fileName;
        $fileKey = $fileName . time();
        $upload = UploadService::uploadRealFile($fileFullPath, $fileName, $fileKey);
        $file = $upload->getFileObject();

        $fileId = $file->file_id;
        $this->assertNotNull($fileId);
        $this->echo($fileId, true, 'file id: ');
//        static::loginUser(11855392);
//        static::loginUser(765);
//        $fileId = 32924747;
        $params = [
            'file_id' => $fileId,
            'public_flag' => 0,
            'replace_flag' => CustomerImport::FLAG_OF_IGNORE,
            'ignore_exists' => 0,
        ];
        $this->echo($params);
//        $this->expectExceptionMessage('相同文件导入任务已存在');
        $result = $this->callAction('import', $params);
        $this->responseOk();

        $this->seeInDatabase(CustomerImport::class, ['type' => Constants::TYPE_COMPANY, 'task_id' => $this->actionResult['data']['task_id'], 'replace_flag' => $params['replace_flag']]);
    }

    public function testUploadFile()
    {
        $fileName = 'benchmark-B2B打开过的邮箱 - 1500.xlsx';
        $fileFullPath = dirname(__FILE__) . '/files/' . $fileName;
        $fileKey = time() . md5($fileName) . '.xls';
        $upload = UploadService::uploadRealFile($fileFullPath, $fileName, $fileKey);
        $file = $upload->getFileObject();
        dd($file->getFileUrl());
    }

    public function testTransfer()
    {
//        $company = CompanyModel::model()->findByPk(33880503);
//        $company->user_id = '{11850882}';
//        $company->save();
//        static::loginUser(11850882);
        $this->loginAsLora();
        $params = [
            'company_ids' => [37427738],
            'user_list'   => [11858380],
        ];
        $this->callAction('transfer', $params);
        $this->responseOk();
    }

    public function testTransferAll()
    {
//        static::loginUser(1);
//        $this->loginAsPro();
//        $companyId = TestData::getOnePrivateCompany()['company_id'];

        $this->loginUser('<EMAIL>');
        $companyId = 38866259;
        $params = [
            'company_ids' => [$companyId],
            'user_list'   => [10],
        ];
        $this->echoParams($params);
        $this->callAction('transferAll', $params);
        $this->echoResult();
    }

    public function testHold()
    {
        $this->loginUser('<EMAIL>');

        $this->loginAsRuby();
        $company = $this->findPublic();
//        $group = GroupModel::model()->findByAttributes([
//            'client_id' => static::$clientId,
//            'type'      => Constants::TYPE_COMPANY
//        ]);
        $params = [
            'company_ids' => [$company->company_id],
            'group_id'    => 0,
        ];


        $this->echoParams($params);
        $this->callAction('hold', $params);
        $this->responseOk();

//        $this->callAction('CompanyDiffList', ['company_id' => $company->company_id], 'CustomerRead');
//        $this->responseOk();
    }

    public function testShare()
    {
        $this->loginAsPro();
        $this->callAction('share', [
            'company_ids' => [1100629168],
            'user_list' => [11858581,2],
        ]);
        $this->responseOk();
    }

    protected function findPublic()
    {
        return CompanyModel::model()->findByAttributes([
            'client_id'  => static::$clientId,
            'user_id'    => '{}',
            'is_archive' => 1,
            'pool_id'    => 0
        ]);
    }

	public function testRemark() {

		$this->loginUser(46900);

		$params = [
			'company_id'  => '1000000937',
			'content'     => 1233,
			'remark_type' => '101',
			'remark_time' => date('Y-m-d H:i:s'),
			//            'customer_id' => '',
		];

		$this->callAction('remark', $params);
		$this->responseOk();
	}

	public function testRemoveTrail(){

		$this->loginUser(46900);

		$param = [
			'trail_id' => 3160710028,

		];

		$this->callAction('removeTrail', $param);

	}


    public function testMergeCompany()
    {
//        $mainCompanyId = 34045834;
//        $otherCompanyId = 34045829;

	    $this->loginUser(46900);
    
        $this->loginClient(351352);
    
        $param = [
            'config' => '{ "company": [{"group_id":1,"name":"基本信息","fields":[{"id":"serial_id","name":"客户编号","base":"1","require":"0","group_id":"1","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"2","unique_message":"","type":"4","value":"paastest0120241107766","format":"paastest0120241107766"},{"id":"name","name":"公司名称","base":"1","require":"1","group_id":"1","hint":"字段提示","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"1","unique_prevent":"1","unique_message":"","type":"4","value":"随机条件测试公司63906","format":"随机条件测试公司63906"},{"id":"short_name","name":"简称","base":"1","require":"0","group_id":"1","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"2","unique_message":"","type":"4","value":"","format":""},{"id":"origin_list","name":"客户来源","base":"1","require":"0","group_id":"1","hint":"","field_type":"7","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"2","unique_message":"","type":"4","value":[],"format":"[]"}]},{"group_id":2,"name":"特征信息","fields":[{"id":"timezone","name":"时区","base":"1","require":"0","group_id":"2","hint":"","field_type":"3","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"2","unique_message":"","type":"4","value":"","format":""},{"id":"province","name":"省份","base":"1","require":"0","group_id":"2","hint":"","field_type":"3","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"city","name":"城市","base":"1","require":"0","group_id":"2","hint":"","field_type":"3","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"category_ids","name":"主营产品","base":"1","require":"0","group_id":"2","hint":"","field_type":"7","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"2","unique_message":"","type":"4","value":[],"format":"[]"},{"id":"biz_type","name":"客户类型","base":"1","require":"0","group_id":"2","hint":"","field_type":"3","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"2","unique_message":"","type":"4","value":"","format":""},{"id":"intention_level","name":"采购意向","base":"1","require":"0","group_id":"2","hint":"","field_type":"3","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"2","unique_message":"","type":"4","value":0,"format":"未知"},{"id":"annual_procurement","name":"年采购额","base":"1","require":"0","group_id":"2","hint":"","field_type":"3","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"2","unique_message":"","type":"4","value":0,"format":"无采购额"},{"id":"scale_id","name":"规模","base":"1","require":"0","group_id":"2","hint":"","field_type":"3","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"2","unique_message":"","type":"4","value":0,"format":"0"},{"id":"country","name":"国家地区","base":"1","require":"0","group_id":"2","hint":"","field_type":"3","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"2","unique_message":"","type":"4","value":"","format":""}]},{"group_id":3,"name":"管理信息","fields":[{"id":"cus_tag","name":"客户标签","base":"1","require":"0","group_id":"3","hint":"","field_type":"7","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":[],"format":"[]"},{"id":"trail_status","name":"客户阶段","base":"1","require":"0","group_id":"3","hint":"","field_type":"3","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":0,"format":"0"},{"id":"star","name":"客户星级","base":"1","require":"0","group_id":"3","hint":"","field_type":"5","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"2","unique_message":"","type":"4","value":0,"format":"0"},{"id":"group_id","name":"客户分组","base":"1","require":"0","group_id":"3","hint":"字段提示","field_type":"3","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":0,"format":"0"},{"id":"pool_id","name":"公海分组","base":"1","require":"1","group_id":"3","hint":"字段提示","field_type":"3","ext_info":[""],"default":"0","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":0,"format":"公共公海分组"},{"id":"product_group_ids","name":"产品分组","base":"1","require":"0","group_id":"3","hint":"","field_type":"7","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"2","unique_message":"","type":"4","value":[],"format":"[]"}]},{"group_id":4,"name":"联系信息","fields":[{"id":"homepage","name":"公司网址","base":"1","require":"0","group_id":"4","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"2","unique_message":"","type":"4","value":"","format":""},{"id":"fax","name":"传真","base":"1","require":"0","group_id":"4","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"2","unique_message":"","type":"4","value":"","format":""},{"id":"tel","name":"座机","base":"1","require":"0","group_id":"4","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"2","unique_message":"","type":"4","value":{"tel_area_code":"","tel":""},"format":"{\"tel_area_code\":\"\",\"tel\":\"\"}"},{"id":"address","name":"详细地址","base":"1","require":"0","group_id":"4","hint":"","field_type":"2","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"2","unique_message":"","type":"4","value":"","format":""}]},{"group_id":5,"name":"其他信息","fields":[{"id":"remark","name":"公司备注","base":"1","require":"0","group_id":"5","hint":"","field_type":"2","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"image_list","name":"公司logo","base":"1","require":"0","group_id":"5","hint":" ","field_type":"6","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":[],"format":"[]"},{"id":"4247082120","name":"company-自定义单行文本","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"1","unique_prevent":"2","unique_message":"","type":"4","value":"","format":""},{"id":"4247082124","name":"company-自定义多行文本","base":"0","require":"0","group_id":"5","hint":"","field_type":"2","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"1","unique_prevent":"2","unique_message":"","type":"4","value":"","format":""},{"id":"4247082128","name":"company-自定义单选","base":"0","require":"0","group_id":"5","hint":"","field_type":"3","ext_info":["选项一","选项二","选项三"],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"4247082132","name":"company-自定义日期","base":"0","require":"0","group_id":"5","hint":"","field_type":"4","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"4247082136","name":"company-自定义数值","base":"0","require":"0","group_id":"5","hint":"","field_type":"5","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"4247082140","name":"company-自定义图片","base":"0","require":"0","group_id":"5","hint":"","field_type":"6","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"4247082144","name":"company-自定义多选","base":"0","require":"0","group_id":"5","hint":"","field_type":"7","ext_info":["选项一","选项二","选项三"],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""},{"id":"4247082149","name":"company-自定义日期时间","base":"0","require":"0","group_id":"5","hint":"","field_type":"10","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","type":"4","value":"","format":""}]}], "main_company_id": 4305537094, "another_company_id": [4305539963], "customers": [] }
',
        
        ];

        $this->callAction('mergeCompany', $param);

        $this->responseOk();
    }

    public function testRemove()
    {
        static::loginUser(11855392);
        $params = [
            'company_ids' => [
                32320104,
                21797527,
                33228556,
                31905151,
                31862461,
                31862467,
                31859522,
                31860683,
                31860975,
                21838238,
                21840466,
                21840535,
                21840462,
                21840433,
                31865990,
            ],
        ];
        $this->callAction('remove', $params);
        $this->responseOk();
    }

    public function testCustomerPool()
    {
        $this->loginAsJocelyn();
        dd(\common\library\customer\pool\Helper::userInPool(static::$clientId, static::$userId, 0));
    }

    public function testRemoveCompanyUser()
    {
        $this->loginUser('<EMAIL>');
        $this->callAction('removeCompanyUser', 'company_id=3082309543&user_id=11864032');
    }


    public function testInsertData()
    {
        $clientId = 1;
        $db = PgActiveRecord::getDbByClientId($clientId);
        $dateTime = date('Y-m-d H:i:s');
        $itemData = function ($userId = 0) use ($dateTime, $clientId) {
            return [
                'name'                         => rand() * 10000000,
                'short_name'                   => '',
                'group_id'                     => [0, 1, 2][array_rand([0, 1, 2])],
                'origin'                       => [0, 1, 2][array_rand([0, 1, 2])],
                'address'                      => '',
                'main_customer'                => 0,
                'main_customer_email'          => '',
                'last_owner'                   => 0,
                'homepage'                     => '',
                'remark'                       => '',
                'biz_type'                     => '',
                'star'                         => [0, 1, 2][array_rand([0, 1, 2])],
                'score'                        => '{}',
                'trail_status'                 => [0, 1, 2][array_rand([0, 1, 2])],
                'fax'                          => '',
                'tel'                          => '',
                'tel_full'                     => '',
                'tel_area_code'                => '',
                'country'                      => '',
                'province'                     => '',
                'city'                         => '',
                'serial_id'                    => '',
                'scale_id'                     => '',
                'category_ids'                 => '{}',
                'tag'                          => '{}',
                'user_data'                    => '{}',
                'external_field_data'          => '{}',
                'company_id'                   => PgActiveRecord::produceAutoIncrementId(),
                'client_id'                    => $clientId,
                'user_id'                      => $userId ? '{' . $userId . '}' : '{}',
                'is_archive'                   => 1,
                'pool_id'                      => [0, 1, 2][array_rand([0, 1, 2])],
                'create_time'                  => $dateTime,
                'update_time'                  => $dateTime,
                'archive_time'                 => $dateTime,
                'order_time'                   => $dateTime,
                'edm_time'                     => $dateTime,
                'send_mail_time'               => $dateTime,
                'receive_mail_time'            => $dateTime,
                'mail_time'                    => $dateTime,
                'meeting_time'                 => $dateTime,
                'public_time'                  => $dateTime,
                'follow_up_time'               => $dateTime,
                'public_time2'                 => $dateTime,
                'image_list'                   => '{}',
                'follow_product_list'          => '{}',
                'follow_product_group_list'    => '{}',
                'follow_product_category_list' => '{}',
                'client_tag_list'              => '{}',
            ];
        };
        for ($p = 0; $p < 100; $p++) {
            $data = [];
            for ($i = 0; $i < 1000; $i++) {
                $data[] = $itemData(0);
            }
            $count = $db->getCommandBuilder()->createMultipleInsertCommand('tbl_company_test', $data)->execute();
            echo "insert $count";
        }

    }

    public function testAddTag()
    {

//        dd(\MailTagAssoc::getMailTagList(1128856766));
//        $this->loginAsLora();
	    $this->loginUser(46900);
//        $params = [
//            'company_ids' => [3115195418],
//            'tag_ids'     => [1315443873,1119126143]
//        ];

	    $params = 'company_ids%5B0%5D=21764047&company_ids%5B1%5D=3118519787&company_ids%5B2%5D=21766555&company_ids%5B3%5D=21766532&company_ids%5B4%5D=21767458&company_ids%5B5%5D=21764043&company_ids%5B6%5D=3118862680&company_ids%5B7%5D=21794443&company_ids%5B8%5D=21774532&company_ids%5B9%5D=21796689&company_ids%5B10%5D=3118226006&company_ids%5B11%5D=3137263315&company_ids%5B12%5D=3137263291&company_ids%5B13%5D=3137263268&company_ids%5B14%5D=3137193837&company_ids%5B15%5D=3137195743&company_ids%5B16%5D=3115195418&company_ids%5B17%5D=3115336395&company_ids%5B18%5D=3136996066&company_ids%5B19%5D=3136997084&company_ids%5B20%5D=3136806581&company_ids%5B21%5D=3136785838&company_ids%5B22%5D=3136601649&company_ids%5B23%5D=3136601422&company_ids%5B24%5D=3136601399&company_ids%5B25%5D=3136601392&company_ids%5B26%5D=3136571358&company_ids%5B27%5D=3136586222&company_ids%5B28%5D=3136601138&company_ids%5B29%5D=1100172745&company_ids%5B30%5D=3136693195&company_ids%5B31%5D=3136838997&company_ids%5B32%5D=3136694141&company_ids%5B33%5D=3136694171&company_ids%5B34%5D=3136791522&company_ids%5B35%5D=32258844&company_ids%5B36%5D=3112436498&company_ids%5B37%5D=3136807660&company_ids%5B38%5D=2258898726&company_ids%5B39%5D=3136694195&company_ids%5B40%5D=1633147518&company_ids%5B41%5D=3136784814&company_ids%5B42%5D=3136770378&company_ids%5B43%5D=3136712953&company_ids%5B44%5D=3136712820&company_ids%5B45%5D=3136712789&company_ids%5B46%5D=3118974359&company_ids%5B47%5D=3136634492&company_ids%5B48%5D=3082347853&company_ids%5B49%5D=1107179586&company_ids%5B50%5D=1107108781&company_ids%5B51%5D=21753310&company_ids%5B52%5D=3136785751&company_ids%5B53%5D=3136712708&company_ids%5B54%5D=3136710786&company_ids%5B55%5D=3118894702&company_ids%5B56%5D=3118891451&company_ids%5B57%5D=3118891476&company_ids%5B58%5D=3114424665&company_ids%5B59%5D=3119276037&company_ids%5B60%5D=3136627236&company_ids%5B61%5D=3136601512&company_ids%5B62%5D=3136602024&company_ids%5B63%5D=3136618544&company_ids%5B64%5D=1125589284&company_ids%5B65%5D=3136618539&company_ids%5B66%5D=3136618897&company_ids%5B67%5D=3118359601&company_ids%5B68%5D=3119323796&company_ids%5B69%5D=3118438873&company_ids%5B70%5D=3119323678&company_ids%5B71%5D=3119323673&company_ids%5B72%5D=38442449&company_ids%5B73%5D=3119025079&company_ids%5B74%5D=3119276033&company_ids%5B75%5D=3119201181&company_ids%5B76%5D=3119201167&company_ids%5B77%5D=3119201160&company_ids%5B78%5D=3119201056&company_ids%5B79%5D=3119201011&company_ids%5B80%5D=1697951950&company_ids%5B81%5D=3119057153&company_ids%5B82%5D=3117451196&company_ids%5B83%5D=3119025975&company_ids%5B84%5D=3119025928&company_ids%5B85%5D=3119024637&company_ids%5B86%5D=3119007907&company_ids%5B87%5D=3118862733&company_ids%5B88%5D=1126355200&company_ids%5B89%5D=3118977948&company_ids%5B90%5D=3118975064&company_ids%5B91%5D=3118975040&company_ids%5B92%5D=3118975028&company_ids%5B93%5D=3118975010&company_ids%5B94%5D=3114650135&company_ids%5B95%5D=3118940253&company_ids%5B96%5D=3118940493&company_ids%5B97%5D=3118940478&company_ids%5B98%5D=3118940472&company_ids%5B99%5D=3118940248&tag_ids%5B0%5D=3137295544';

        $this->callAction('addTag', $params);
        $this->responseOk();
    }
	public function testRemoveTags()
    {

//        dd(\MailTagAssoc::getMailTagList(1128856766));
//        $this->loginAsLora();
	    $this->loginUser(46900);

        $params = [
            'company_ids' => [40447757],
            'tag_ids'     => [3136949603]
        ];

		$params = 'company_ids%5B0%5D=21764047&company_ids%5B1%5D=3118519787&company_ids%5B2%5D=21766555&company_ids%5B3%5D=21766532&company_ids%5B4%5D=21767458&company_ids%5B5%5D=21764043&company_ids%5B6%5D=3118862680&company_ids%5B7%5D=21794443&company_ids%5B8%5D=21774532&company_ids%5B9%5D=21796689&company_ids%5B10%5D=3118226006&company_ids%5B11%5D=3137263315&company_ids%5B12%5D=3137263291&company_ids%5B13%5D=3137263268&company_ids%5B14%5D=3137193837&company_ids%5B15%5D=3137195743&company_ids%5B16%5D=3115195418&company_ids%5B17%5D=3115336395&company_ids%5B18%5D=3136996066&company_ids%5B19%5D=3136997084&company_ids%5B20%5D=3136806581&company_ids%5B21%5D=3136785838&company_ids%5B22%5D=3136601649&company_ids%5B23%5D=3136601422&company_ids%5B24%5D=3136601399&company_ids%5B25%5D=3136601392&company_ids%5B26%5D=3136571358&company_ids%5B27%5D=3136586222&company_ids%5B28%5D=3136601138&company_ids%5B29%5D=1100172745&company_ids%5B30%5D=3136693195&company_ids%5B31%5D=3136838997&company_ids%5B32%5D=3136694141&company_ids%5B33%5D=3136694171&company_ids%5B34%5D=3136791522&company_ids%5B35%5D=32258844&company_ids%5B36%5D=3112436498&company_ids%5B37%5D=3136807660&company_ids%5B38%5D=2258898726&company_ids%5B39%5D=3136694195&company_ids%5B40%5D=1633147518&company_ids%5B41%5D=3136784814&company_ids%5B42%5D=3136770378&company_ids%5B43%5D=3136712953&company_ids%5B44%5D=3136712820&company_ids%5B45%5D=3136712789&company_ids%5B46%5D=3118974359&company_ids%5B47%5D=3136634492&company_ids%5B48%5D=3082347853&company_ids%5B49%5D=1107179586&company_ids%5B50%5D=1107108781&company_ids%5B51%5D=21753310&company_ids%5B52%5D=3136785751&company_ids%5B53%5D=3136712708&company_ids%5B54%5D=3136710786&company_ids%5B55%5D=3118894702&company_ids%5B56%5D=3118891451&company_ids%5B57%5D=3118891476&company_ids%5B58%5D=3114424665&company_ids%5B59%5D=3119276037&company_ids%5B60%5D=3136627236&company_ids%5B61%5D=3136601512&company_ids%5B62%5D=3136602024&company_ids%5B63%5D=3136618544&company_ids%5B64%5D=1125589284&company_ids%5B65%5D=3136618539&company_ids%5B66%5D=3136618897&company_ids%5B67%5D=3118359601&company_ids%5B68%5D=3119323796&company_ids%5B69%5D=3118438873&company_ids%5B70%5D=3119323678&company_ids%5B71%5D=3119323673&company_ids%5B72%5D=38442449&company_ids%5B73%5D=3119025079&company_ids%5B74%5D=3119276033&company_ids%5B75%5D=3119201181&company_ids%5B76%5D=3119201167&company_ids%5B77%5D=3119201160&company_ids%5B78%5D=3119201056&company_ids%5B79%5D=3119201011&company_ids%5B80%5D=1697951950&company_ids%5B81%5D=3119057153&company_ids%5B82%5D=3117451196&company_ids%5B83%5D=3119025975&company_ids%5B84%5D=3119025928&company_ids%5B85%5D=3119024637&company_ids%5B86%5D=3119007907&company_ids%5B87%5D=3118862733&company_ids%5B88%5D=1126355200&company_ids%5B89%5D=3118977948&company_ids%5B90%5D=3118975064&company_ids%5B91%5D=3118975040&company_ids%5B92%5D=3118975028&company_ids%5B93%5D=3118975010&company_ids%5B94%5D=3114650135&company_ids%5B95%5D=3118940253&company_ids%5B96%5D=3118940493&company_ids%5B97%5D=3118940478&company_ids%5B98%5D=3118940472&company_ids%5B99%5D=3118940248&tag_ids%5B0%5D=3137248891';


        $this->callAction('RemoveTags', $params);
        $this->responseOk();
    }

    public function testStar()
    {
        $this->loginAsLora();
        $this->loginAsQiao();
//        $company_id = TestData::getOnePrivateCompany()['company_id'];
        $company_id = 1684770684;
        $params = [
            'company_id' => $company_id,
            'star'       => 5,
        ];

        $this->callAction('star', $params);
        $this->responseOk();
    }


    public function testDelete()
    {
        CompanyModel::model()->updateByPk(32327466, [
            'is_archive' => 1
        ]);
        $this->loginAsRuby();
        $this->callAction('delete', [
            'company_ids' => [32327466],
        ]);
        $this->responseOk();
    }

    public function testSaveByAdvice()
    {
        $this->loginAsQiao();
        $params = [
            'advice_ids' => [1102586536]
        ];

        $this->callAction('saveByAdvice', $params);
        $this->responseOk();

    }

    public function testCheckCustomerNo()
    {
        $this->callAction('CheckCustomerNo', [
            'serial_id' => 'KP011',
            'company_id' => '',
        ]);
        $this->responseOk();
    }

    public function testCustomerContactMarkRead()
    {
        $this->loginUser('11857375');
        $this->callAction('messageMarkRead' , [
            'company_id' => 0
        ]);
        $this->responseOk();
    }

    public function testAddBlackList()
    {
        $this->loginUser('<EMAIL>');
        $this->callAction('AddBlacklist', [
            'mail_name' => $this->faker()->email,
        ]);
        $this->responseOk();
    }

    public function testExampleCompany()
    {
        $data = '{"company":[{"group_id":1,"name":"基本信息","fields":[{"id":"name","name":"公司名称","base":"1","require":"1","group_id":"1","hint":"请输入公司名称","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"1","unique_prevent":"1","unique_message":"","value":"（示例）ARDMORE HOME DESIGN, INC."},{"id":"short_name","name":"简称","base":"1","require":"0","group_id":"1","hint":"简称提示","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"1","unique_prevent":"1","unique_message":"已有相同的公司简称","value":"ARDMORE"},{"id":"serial_id","name":"客户编号","base":"1","require":"0","group_id":"1","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"1","unique_prevent":"1","unique_message":"","value":"示例01"},{"id":"origin","name":"客户来源","base":"1","require":"0","group_id":"1","hint":"客户来源提示","field_type":"3","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"1"}]},{"group_id":2,"name":"特征信息","fields":[{"id":"intention_level","name":"采购意向","base":"1","require":"0","group_id":"2","hint":"","field_type":"3","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":2},{"id":"annual_procurement","name":"年采购额","base":"1","require":"0","group_id":"2","hint":"","field_type":"3","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":7},{"id":"biz_type","name":"客户类型","base":"1","require":"0","group_id":"2","hint":"客户类型提示","field_type":"3","ext_info":[{"zh-CN":"原材料供应商","zh-TW":"原材料供應商","en":"Raw Material Supplier","value":"原材料供应商","name":"原材料供应商","label":"原材料供应商"},{"zh-CN":"生产商","zh-TW":"生產商","en":"Producers","value":"生产商","name":"生产商","label":"生产商"},{"zh-CN":"加盟商","zh-TW":"加盟商","en":"Franchisee","value":"加盟商","name":"加盟商","label":"加盟商"},{"zh-CN":"渠道商","zh-TW":"渠道商","en":"Distributors","value":"渠道商","name":"渠道商","label":"渠道商"},{"zh-CN":"贸易商","zh-TW":"貿易商","en":"Trader","value":"贸易商","name":"贸易商","label":"贸易商"},{"zh-CN":"代理商","zh-TW":"代理商","en":"Agent","value":"代理商","name":"代理商","label":"代理商"},{"zh-CN":"批发商","zh-TW":"批發商","en":"Wholesaler","value":"批发商","name":"批发商","label":"批发商"},{"zh-CN":"分销商","zh-TW":"分銷商","en":"Distributor","value":"分销商","name":"分销商","label":"分销商"},{"zh-CN":"代销商","zh-TW":"代銷商","en":"Affiliate","value":"代销商","name":"代销商","label":"代销商"},{"zh-CN":"零售商","zh-TW":"零售商","en":"Retailer","value":"零售商","name":"零售商","label":"零售商"},{"zh-CN":"采购办事处","zh-TW":"採購辦事處","en":"Purchasing Office","value":"采购办事处","name":"采购办事处","label":"采购办事处"},{"zh-CN":"采购咨询公司","zh-TW":"採購諮詢公司","en":"Purchasing Consulting Company","value":"采购咨询公司","name":"采购咨询公司","label":"采购咨询公司"},{"zh-CN":"出口商","zh-TW":"出口商","en":"Exporter","value":"出口商","name":"出口商","label":"出口商"},{"zh-CN":"进口商","zh-TW":"進口商","en":"Importer","value":"进口商","name":"进口商","label":"进口商"},{"zh-CN":"个人消费者","zh-TW":"個人消費者","en":"Personal Consumer","value":"个人消费者","name":"个人消费者","label":"个人消费者"},{"zh-CN":"机构/团体消费者","zh-TW":"機構/團體消費者","en":"Institution/group consumer","value":"机构/团体消费者","name":"机构/团体消费者","label":"机构/团体消费者"},{"zh-CN":"工程商","zh-TW":"工程商","en":"Engineering","value":"工程商","name":"工程商","label":"工程商"},{"zh-CN":"其他","zh-TW":"其他","en":"Other","value":"其他","name":"其他","label":"其他"}],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"category_ids","name":"主营产品","base":"1","require":"0","group_id":"2","hint":"主营产品提示","field_type":"7","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":[["15"]]},{"id":"country","name":"国家地区","base":"1","require":"0","group_id":"2","hint":"国家地区提示","field_type":"3","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"US","countryInfo":{"country":"US","province":"","city":""}},{"id":"province","name":"省份","base":"1","require":"0","group_id":"2","hint":"身份提示","field_type":"3","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"timezone","name":"时区","base":"1","require":"0","group_id":"2","hint":"时区提示","field_type":"3","ext_info":[{"label":"零时区：伦敦","value":"0"},{"label":"东一区：罗马，巴黎","value":"1"},{"label":"东二区：雅典，以色列","value":"2"},{"label":"东三区：莫斯科，科威特","value":"3"},{"label":"东四区：喀布尔","value":"4"},{"label":"东五区：伊斯兰堡，卡拉奇","value":"5"},{"label":"东六区：阿拉木图，科伦坡","value":"6"},{"label":"东七区：曼谷，雅加达","value":"7"},{"label":"东八区：北京，香港，台湾","value":"8"},{"label":"东九区：东京","value":"9"},{"label":"东十区：悉尼","value":"10"},{"label":"东十一区：霍尼亚拉，马加丹","value":"11"},{"label":"东西十二区: 奥克兰","value":"12"},{"label":"西十一区：帕果帕果，阿洛菲","value":"-11"},{"label":"西十区：夏威夷","value":"-10"},{"label":"西九区：阿拉斯加","value":"-9"},{"label":"西八区：洛杉矶，旧金山","value":"-8"},{"label":"西七区：盐湖城、丹佛、凤凰城","value":"-7"},{"label":"西六区：芝加哥，休斯顿，亚特兰大","value":"-6"},{"label":"西五区：纽约，华盛顿，波士顿","value":"-5"},{"label":"西四区：加拿大，加拉加斯","value":"-4"},{"label":"西三区：巴西利亚","value":"-3"},{"label":"西二区：协调世界时","value":"-2"},{"label":"西一区：佛得角群岛","value":"-1"}],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"city","name":"城市","base":"1","require":"0","group_id":"2","hint":"城市提示","field_type":"3","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"scale_id","name":"规模","base":"1","require":"0","group_id":"2","hint":"规模提示","field_type":"3","ext_info":[{"value":1,"name":"少于59人","en_name":"Less than 59 employees","label":"少于59人"},{"value":2,"name":"60-149人","en_name":"60-149 employees","label":"60-149人"},{"value":3,"name":"150-499人","en_name":"150-499 employees","label":"150-499人"},{"value":4,"name":"500-999人","en_name":"500-999 employees","label":"500-999人"},{"value":5,"name":"1000-4999人","en_name":"1000-4999 employees","label":"1000-4999人"},{"value":6,"name":"5000人以上","en_name":"More than 5000 employees","label":"5000人以上"}],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":1},{"id":"1101101841","name":"自定义日期-时间","base":"0","require":"0","group_id":"2","hint":"自定义时间提示","field_type":"10","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":""}]},{"group_id":3,"name":"管理信息","fields":[{"id":"star","name":"客户星级","base":"1","require":"1","group_id":"3","hint":"客户星级提示","field_type":"5","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":4},{"id":"group_id","name":"分组","base":"1","require":"0","group_id":"3","hint":"分组提示","field_type":"3","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":0},{"id":"cus_tag","name":"客户标签","base":"1","require":"0","group_id":"3","hint":"客户标签提示","field_type":"7","ext_info":[],"default":"[]","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"[]"},{"id":"trail_status","name":"客户状态","base":"1","require":"0","group_id":"3","hint":"客户状态提示","field_type":"3","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"0"},{"id":"pool_id","name":"公海分组","base":"1","require":"1","group_id":"3","hint":"公海分组提示","field_type":"3","ext_info":[""],"default":"0","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":0}]},{"group_id":4,"name":"联系信息","fields":[{"id":"lonlat","name":"经维度坐标","base":"1","require":"0","group_id":"4","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","value":{"longitude":0,"latitude":0}},{"id":"tel","name":"座机","base":"1","require":"0","group_id":"4","hint":"座机提示","field_type":"1","ext_info":[],"default":"{\"tel_area_code\":\"\",\"tel\":\"\"}","disable_flag":"0","is_editable":1,"unique_check":"1","unique_prevent":"1","unique_message":"","value":{"tel":"************","tel_area_code":1}},{"id":"homepage","name":"主页","base":"1","require":"0","group_id":"4","hint":"主页提示","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"1","unique_prevent":"1","unique_message":"","value":"http://www.madegoods.com"},{"id":"address","name":"联系地址","base":"1","require":"0","group_id":"4","hint":"联系地址提示","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"1","unique_prevent":"2","unique_message":"联系地址已存在","value":"768 Turnbull Canyon Rd,City Of Industry,CA,91745,United States of America"},{"id":"fax","name":"传真","base":"1","require":"0","group_id":"4","hint":"传真提示","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"1","unique_prevent":"2","unique_message":"已有相同的传真","value":""}]},{"group_id":5,"name":"其他信息","fields":[{"id":"image_list","name":"图片","base":"1","require":"0","group_id":"5","hint":" ","field_type":"6","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":[]},{"id":"remark","name":"备注","base":"1","require":"0","group_id":"5","hint":"","field_type":"2","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"Home furnishings"},{"id":"1102352738","name":"客户下拉多选","base":"0","require":"0","group_id":"5","hint":"","field_type":"7","ext_info":["周黑鸭"],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"1102603643","name":"客户-支付信息","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"1105128163","name":"客户自定义下拉单选","base":"0","require":"0","group_id":"5","hint":"","field_type":"3","ext_info":["客户1","客户2","客户3"],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"1105162749","name":"客户地址","base":"0","require":"0","group_id":"5","hint":"","field_type":"2","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"1","unique_prevent":"1","unique_message":"客户地址已存在哟～～～","value":""},{"id":"1101101837","name":"自定义下拉单选","base":"0","require":"0","group_id":"5","hint":"","field_type":"3","ext_info":["客户1","彩虹","不柒"],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"1101101838","name":"自定义日期","base":"0","require":"0","group_id":"5","hint":"","field_type":"4","ext_info":[],"default":"2020-03-12","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"2020-03-12"},{"id":"1101101839","name":"自定义数值","base":"0","require":"0","group_id":"5","hint":"请输入数值","field_type":"5","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"1105128171","name":"客户自定义日期","base":"0","require":"0","group_id":"5","hint":"","field_type":"4","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"1101101840","name":"自定义下拉多选","base":"0","require":"0","group_id":"5","hint":"请选择下拉多选","field_type":"7","ext_info":["客户多选2","客户多选3"],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"next_follow_up_time","name":"下次跟进时间","base":"1","require":"0","group_id":"5","hint":"","field_type":"10","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":""}]}],"customers":[{"customer_id":0,"company_id":0,"email":"","name":"","main_customer_flag":1,"fields":[{"id":"main_customer_flag","name":"主要联系人","base":"1","require":"0","hint":"","field_type":"3","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":1,"group_id":0},{"id":"name","name":"昵称","base":"1","require":"0","hint":"","field_type":"1","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"Alan","group_id":0},{"id":"email","name":"邮箱","base":"1","require":"0","hint":"","field_type":"1","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"1","unique_prevent":"2","unique_message":"","value":"<EMAIL>","group_id":0},{"id":"remark","name":"备注","base":"1","require":"0","hint":"","field_type":"2","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"post_grade","name":"职级","base":"1","require":"0","hint":"","field_type":"3","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":3,"group_id":0},{"id":"birth","name":"生日","base":"1","require":"0","hint":"","field_type":"4","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"1980-11-07","group_id":0},{"id":"post","name":"职位","base":"1","require":"0","hint":"","field_type":"1","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"CEO","group_id":0},{"id":"tel_list","name":"联系电话","base":"1","require":"0","hint":"联系电话提示","field_type":"1","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"1","unique_prevent":"1","unique_message":"","value":[],"group_id":0},{"id":"contact","name":"社交平台","base":"1","require":"0","hint":"","field_type":"2","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"1","unique_prevent":"1","unique_message":"已存在相同的社交平台","value":[{"type":"facebook","value":"https://www.facebook.com/madegoodshome"},{"type":"twitter","value":"https://twitter.com/MadeGoods"},{"type":"instagram","value":"https://www.instagram.com/madegoods/"}],"group_id":0},{"id":"image_list","name":"图片","base":"1","require":"0","hint":" ","field_type":"6","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":[],"group_id":0},{"id":"gender","name":"性别","base":"1","require":"0","hint":"","field_type":"3","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":1,"group_id":0},{"id":"1105746753","name":"旺旺号","base":"0","require":"0","hint":"","field_type":"1","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"1","unique_prevent":"1","unique_message":"存在重复的旺旺号","value":"","group_id":0},{"id":"1102611672","name":"自定义联系人下拉多选","base":"0","require":"0","hint":"","field_type":"7","ext_info":["小美","小梅","小妹"],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"1102611671","name":"自定义联系人下拉单选","base":"0","require":"0","hint":"","field_type":"3","ext_info":["剪刀","菜刀","尖刀"],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"1102611669","name":"自定义联系人文本","base":"0","require":"0","hint":"","field_type":"1","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"1","unique_prevent":"2","unique_message":"联系人相同啦","value":"","group_id":0},{"id":"1102611670","name":"自定义联系多行文本","base":"0","require":"0","hint":"","field_type":"2","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"1102611675","name":"自定义联系人数值","base":"0","require":"0","hint":"","field_type":"5","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0}]},{"customer_id":0,"company_id":0,"email":"","name":"","main_customer_flag":0,"fields":[{"id":"main_customer_flag","name":"主要联系人","base":"1","require":"0","hint":"","field_type":"3","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"name","name":"昵称","base":"1","require":"0","hint":"","field_type":"1","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"Alice","group_id":0},{"id":"email","name":"邮箱","base":"1","require":"0","hint":"","field_type":"1","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"1","unique_prevent":"2","unique_message":"","value":"<EMAIL>","group_id":0},{"id":"remark","name":"备注","base":"1","require":"0","hint":"","field_type":"2","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"post_grade","name":"职级","base":"1","require":"0","hint":"","field_type":"3","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":2,"group_id":0},{"id":"birth","name":"生日","base":"1","require":"0","hint":"","field_type":"4","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"1981-04-01","group_id":0},{"id":"post","name":"职位","base":"1","require":"0","hint":"","field_type":"1","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"Manager","group_id":0},{"id":"tel_list","name":"联系电话","base":"1","require":"0","hint":"联系电话提示","field_type":"1","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"1","unique_prevent":"1","unique_message":"","value":[],"group_id":0},{"id":"contact","name":"社交平台","base":"1","require":"0","hint":"","field_type":"2","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"1","unique_prevent":"1","unique_message":"已存在相同的社交平台","value":[],"group_id":0},{"id":"image_list","name":"图片","base":"1","require":"0","hint":" ","field_type":"6","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":[],"group_id":0},{"id":"gender","name":"性别","base":"1","require":"0","hint":"","field_type":"3","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":2,"group_id":0},{"id":"1105746753","name":"旺旺号","base":"0","require":"0","hint":"","field_type":"1","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"1","unique_prevent":"1","unique_message":"存在重复的旺旺号","value":"","group_id":0},{"id":"1102611672","name":"自定义联系人下拉多选","base":"0","require":"0","hint":"","field_type":"7","ext_info":["小美","小梅","小妹"],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"1102611671","name":"自定义联系人下拉单选","base":"0","require":"0","hint":"","field_type":"3","ext_info":["剪刀","菜刀","尖刀"],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"1102611669","name":"自定义联系人文本","base":"0","require":"0","hint":"","field_type":"1","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"1","unique_prevent":"2","unique_message":"联系人相同啦","value":"","group_id":0},{"id":"1102611670","name":"自定义联系多行文本","base":"0","require":"0","hint":"","field_type":"2","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"1102611675","name":"自定义联系人数值","base":"0","require":"0","hint":"","field_type":"5","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0}]},{"customer_id":0,"company_id":0,"email":"","name":"","main_customer_flag":0,"fields":[{"id":"main_customer_flag","name":"主要联系人","base":"1","require":"0","hint":"","field_type":"3","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"name","name":"昵称","base":"1","require":"0","hint":"","field_type":"1","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"Mike","group_id":0},{"id":"email","name":"邮箱","base":"1","require":"0","hint":"","field_type":"1","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"1","unique_prevent":"2","unique_message":"","value":"<EMAIL>","group_id":0},{"id":"remark","name":"备注","base":"1","require":"0","hint":"","field_type":"2","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"post_grade","name":"职级","base":"1","require":"0","hint":"","field_type":"3","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":1,"group_id":0},{"id":"birth","name":"生日","base":"1","require":"0","hint":"","field_type":"4","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"1982-08-23","group_id":0},{"id":"post","name":"职位","base":"1","require":"0","hint":"","field_type":"1","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"Purchaser","group_id":0},{"id":"tel_list","name":"联系电话","base":"1","require":"0","hint":"联系电话提示","field_type":"1","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"1","unique_prevent":"1","unique_message":"","value":[],"group_id":0},{"id":"contact","name":"社交平台","base":"1","require":"0","hint":"","field_type":"2","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"1","unique_prevent":"1","unique_message":"已存在相同的社交平台","value":[],"group_id":0},{"id":"image_list","name":"图片","base":"1","require":"0","hint":" ","field_type":"6","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":[],"group_id":0},{"id":"gender","name":"性别","base":"1","require":"0","hint":"","field_type":"3","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":1,"group_id":0},{"id":"1105746753","name":"旺旺号","base":"0","require":"0","hint":"","field_type":"1","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"1","unique_prevent":"1","unique_message":"存在重复的旺旺号","value":"","group_id":0},{"id":"1102611672","name":"自定义联系人下拉多选","base":"0","require":"0","hint":"","field_type":"7","ext_info":["小美","小梅","小妹"],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"1102611671","name":"自定义联系人下拉单选","base":"0","require":"0","hint":"","field_type":"3","ext_info":["剪刀","菜刀","尖刀"],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"1102611669","name":"自定义联系人文本","base":"0","require":"0","hint":"","field_type":"1","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"1","unique_prevent":"2","unique_message":"联系人相同啦","value":"","group_id":0},{"id":"1102611670","name":"自定义联系多行文本","base":"0","require":"0","hint":"","field_type":"2","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"1102611675","name":"自定义联系人数值","base":"0","require":"0","hint":"","field_type":"5","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0}]}]}';
        $this->callAction('saveCompanyData', [
            'data' => $data,
            'check_public' => 1,
            'archive_flag' => 1,
            'company_hash_id' => 'f7d9a571c56f9279',
        ]);
        $this->responseOk();
    }

    public function testCompanyAddUser()
    {
        $this->loginAsXpro();
        $company = new Company(static::$clientId, 1101152602);
        $company->addUser(static::$userId);
        $company->save();
    }


    public function testSetGroup()
    {
        $this->loginUser('<EMAIL>');
        $this->loginAsKk();
        $this->callAction('setGroup', [
            'company_ids' => ['3118103867'],
//            'group_id' => 0,
            'group_id' => 2282718655,
        ]);
        $this->responseOk();
    }

    public function testCustomerSendMailTime()
    {
        $this->loginUser('<EMAIL>');
        $companyId = 2819441172;
        $customerId= 2819441171;
        Helper::updateSendMailTime(
            static::$clientId,
            static::$userId,
            $companyId,
            $customerId,
            '2022-03-28'
        );
    }


    public function testDebugCompanySaveByLead()
    {
        $data = json_decode('
        {"company":[{"group_id":1,"name":"基本信息","fields":[{"id":"name","name":"公司名称","base":"1","require":"1","group_id":"1","hint":"请输入公司名称","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"1","unique_prevent":"1","unique_message":"","value":"xiao_man_buyer yandex.ru.company1","format":"xiao_man_buyer yandex.ru.company"},{"id":"serial_id","name":"客户编号","base":"1","require":"0","group_id":"1","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"1","unique_prevent":"1","unique_message":"","value":"","format":""},{"id":"origin","name":"客户来源","base":"1","require":"0","group_id":"1","hint":"客户来源提示","field_type":"3","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":4,"format":"4"},{"id":"short_name","name":"简称","base":"1","require":"0","group_id":"1","hint":"简称提示","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"1","unique_prevent":"1","unique_message":"已有相同的公司简称","value":"","format":""}]},{"group_id":2,"name":"特征信息","fields":[{"id":"1514096184","name":"锁定期","base":"0","require":"0","group_id":"2","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"","format":""},{"id":"intention_level","name":"采购意向","base":"1","require":"0","group_id":"2","hint":"","field_type":"3","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"","format":"未知"},{"id":"annual_procurement","name":"年采购额","base":"1","require":"0","group_id":"2","hint":"","field_type":"3","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"","format":"无采购额"},{"id":"biz_type","name":"客户类型","base":"1","require":"0","group_id":"2","hint":"客户类型提示","field_type":"3","ext_info":[{"zh-CN":"原材料供应商","zh-TW":"原材料供應商","en":"Raw Material Supplier","value":"原材料供应商","name":"原材料供应商","label":"原材料供应商"},{"zh-CN":"生产商","zh-TW":"生產商","en":"Producers","value":"生产商","name":"生产商","label":"生产商"},{"zh-CN":"加盟商","zh-TW":"加盟商","en":"Franchisee","value":"加盟商","name":"加盟商","label":"加盟商"},{"zh-CN":"渠道商","zh-TW":"渠道商","en":"Distributors","value":"渠道商","name":"渠道商","label":"渠道商"},{"zh-CN":"贸易商","zh-TW":"貿易商","en":"Trader","value":"贸易商","name":"贸易商","label":"贸易商"},{"zh-CN":"代理商","zh-TW":"代理商","en":"Agent","value":"代理商","name":"代理商","label":"代理商"},{"zh-CN":"批发商","zh-TW":"批發商","en":"Wholesaler","value":"批发商","name":"批发商","label":"批发商"},{"zh-CN":"分销商","zh-TW":"分銷商","en":"Distributor","value":"分销商","name":"分销商","label":"分销商"},{"zh-CN":"代销商","zh-TW":"代銷商","en":"Affiliate","value":"代销商","name":"代销商","label":"代销商"},{"zh-CN":"零售商","zh-TW":"零售商","en":"Retailer","value":"零售商","name":"零售商","label":"零售商"},{"zh-CN":"采购办事处","zh-TW":"採購辦事處","en":"Purchasing Office","value":"采购办事处","name":"采购办事处","label":"采购办事处"},{"zh-CN":"采购咨询公司","zh-TW":"採購諮詢公司","en":"Purchasing Consulting Company","value":"采购咨询公司","name":"采购咨询公司","label":"采购咨询公司"},{"zh-CN":"出口商","zh-TW":"出口商","en":"Exporter","value":"出口商","name":"出口商","label":"出口商"},{"zh-CN":"进口商","zh-TW":"進口商","en":"Importer","value":"进口商","name":"进口商","label":"进口商"},{"zh-CN":"个人消费者","zh-TW":"個人消費者","en":"Personal Consumer","value":"个人消费者","name":"个人消费者","label":"个人消费者"},{"zh-CN":"机构/团体消费者","zh-TW":"機構/團體消費者","en":"Institution/group consumer","value":"机构/团体消费者","name":"机构/团体消费者","label":"机构/团体消费者"},{"zh-CN":"工程商","zh-TW":"工程商","en":"Engineering","value":"工程商","name":"工程商","label":"工程商"},{"zh-CN":"其他","zh-TW":"其他","en":"Other","value":"其他","name":"其他","label":"其他"}],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"","format":""},{"id":"category_ids","name":"主营产品","base":"1","require":"0","group_id":"2","hint":"主营产品提示","field_type":"7","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":[],"format":"[]"},{"id":"country","name":"国家地区","base":"1","require":"0","group_id":"2","hint":"国家地区提示","field_type":"3","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"SZ","format":"SZ","countryInfo":{"country":"SZ","province":"","city":""}},{"id":"province","name":"省份","base":"1","require":"0","group_id":"2","hint":"身份提示","field_type":"3","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"","format":"r334r"},{"id":"timezone","name":"时区","base":"1","require":"0","group_id":"2","hint":"时区提示","field_type":"3","ext_info":[{"label":"零时区：伦敦","value":"0"},{"label":"东一区：罗马，巴黎","value":"1"},{"label":"东二区：雅典，以色列","value":"2"},{"label":"东三区：莫斯科，科威特","value":"3"},{"label":"东四区：喀布尔","value":"4"},{"label":"东五区：伊斯兰堡，卡拉奇","value":"5"},{"label":"东六区：阿拉木图，科伦坡","value":"6"},{"label":"东七区：曼谷，雅加达","value":"7"},{"label":"东八区：北京，香港，台湾","value":"8"},{"label":"东九区：东京","value":"9"},{"label":"东十区：悉尼","value":"10"},{"label":"东十一区：霍尼亚拉，马加丹","value":"11"},{"label":"东西十二区: 奥克兰","value":"12"},{"label":"西十一区：帕果帕果，阿洛菲","value":"-11"},{"label":"西十区：夏威夷","value":"-10"},{"label":"西九区：阿拉斯加","value":"-9"},{"label":"西八区：洛杉矶，旧金山","value":"-8"},{"label":"西七区：盐湖城、丹佛、凤凰城","value":"-7"},{"label":"西六区：芝加哥，休斯顿，亚特兰大","value":"-6"},{"label":"西五区：纽约，华盛顿，波士顿","value":"-5"},{"label":"西四区：加拿大，加拉加斯","value":"-4"},{"label":"西三区：巴西利亚","value":"-3"},{"label":"西二区：协调世界时","value":"-2"},{"label":"西一区：佛得角群岛","value":"-1"}],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":2,"format":""},{"id":"city","name":"城市","base":"1","require":"0","group_id":"2","hint":"城市提示","field_type":"3","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"","format":"43t34t"},{"id":"1101101841","name":"自定义日期-时间","base":"0","require":"0","group_id":"2","hint":"自定义时间提示","field_type":"10","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"","format":""},{"id":"scale_id","name":"规模","base":"1","require":"0","group_id":"2","hint":"规模提示","field_type":"3","ext_info":[{"value":1,"name":"少于59人","en_name":"Less than 59 employees","label":"少于59人"},{"value":2,"name":"60-149人","en_name":"60-149 employees","label":"60-149人"},{"value":3,"name":"150-499人","en_name":"150-499 employees","label":"150-499人"},{"value":4,"name":"500-999人","en_name":"500-999 employees","label":"500-999人"},{"value":5,"name":"1000-4999人","en_name":"1000-4999 employees","label":"1000-4999人"},{"value":6,"name":"5000人以上","en_name":"More than 5000 employees","label":"5000人以上"}],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":0,"format":"0"}]},{"group_id":3,"name":"管理信息","fields":[{"id":"star","name":"客户星级","base":"1","require":"0","group_id":"3","hint":"客户星级提示","field_type":"5","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"0","format":"0"},{"id":"group_id","name":"分组","base":"1","require":"0","group_id":"3","hint":"分组提示","field_type":"3","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":0,"format":"0"},{"id":"cus_tag","name":"客户标签","base":"1","require":"0","group_id":"3","hint":"客户标签提示","field_type":"7","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":[],"format":"[]"},{"id":"trail_status","name":"客户阶段","base":"1","require":"0","group_id":"3","hint":"客户状态提示","field_type":"3","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"0","format":"0"},{"id":"pool_id","name":"公海分组","base":"1","require":"1","group_id":"3","hint":"公海分组提示","field_type":"3","ext_info":[""],"default":"0","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":0,"format":""}]},{"group_id":4,"name":"联系信息","fields":[{"id":"tel","name":"座机","base":"1","require":"0","group_id":"4","hint":"座机提示","field_type":"1","ext_info":[],"default":"{\"tel_area_code\":\"\",\"tel\":\"\"}","disable_flag":"0","is_editable":1,"unique_check":"1","unique_prevent":"1","unique_message":"","value":{"tel":"","tel_area_code":""},"format":"{\"tel_area_code\":\"\",\"tel\":\"\"}"},{"id":"address","name":"详细地址","base":"1","require":"0","group_id":"4","hint":"联系地址提示","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"1","unique_prevent":"2","unique_message":"联系地址已存在","value":"r334r 43t34t  utu Yutyu Ttyu 34r34","format":"r334r 43t34t  utu Yutyu Ttyu 34r34"},{"id":"fax","name":"传真","base":"1","require":"0","group_id":"4","hint":"传真提示","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"1","unique_prevent":"2","unique_message":"已有相同的传真","value":"25-4156-3464564","format":"25-4156-3464564"},{"id":"homepage","name":"公司网址","base":"1","require":"0","group_id":"4","hint":"主页提示","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"1","unique_prevent":"1","unique_message":"","value":"https://india.ivanayvonnes1.com","format":"https://india.ivanayvonnes.com"},{"id":"lonlat","name":"经维度坐标","base":"1","require":"0","group_id":"4","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","value":{"longitude":0,"latitude":0},"format":"{\"longitude\":0,\"latitude\":0}"}]},{"group_id":5,"name":"其他信息","fields":[{"id":"remark","name":"公司备注","base":"1","require":"0","group_id":"5","hint":"","field_type":"2","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"","format":""},{"id":"1102352738","name":"客户下拉多选","base":"0","require":"0","group_id":"5","hint":"","field_type":"7","ext_info":["周黑鸭","煌上煌","麻辣烫"],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"","format":""},{"id":"image_list","name":"公司logo","base":"1","require":"0","group_id":"5","hint":" ","field_type":"6","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":[],"format":"[]"},{"id":"1102603643","name":"客户-支付信息","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"2","unique_message":"","value":"","format":""},{"id":"1105128163","name":"客户自定义下拉单选","base":"0","require":"0","group_id":"5","hint":"","field_type":"3","ext_info":["客户1","客户2","客户3"],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"","format":""},{"id":"1105162749","name":"客户地址","base":"0","require":"0","group_id":"5","hint":"","field_type":"2","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"1","unique_prevent":"1","unique_message":"客户地址已存在哟～～～","value":"","format":""},{"id":"1101101837","name":"自定义下拉单选","base":"0","require":"0","group_id":"5","hint":"","field_type":"3","ext_info":["客户1","彩虹","不柒"],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"","format":""},{"id":"1101101838","name":"自定义日期","base":"0","require":"0","group_id":"5","hint":"","field_type":"4","ext_info":[],"default":"2020-03-12","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"2020-03-12","format":""},{"id":"1101101839","name":"自定义数值","base":"0","require":"0","group_id":"5","hint":"请输入数值","field_type":"5","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"","format":""},{"id":"1105128171","name":"客户自定义日期","base":"0","require":"0","group_id":"5","hint":"","field_type":"4","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"","format":""},{"id":"1101101840","name":"自定义下拉多选","base":"0","require":"0","group_id":"5","hint":"请选择下拉多选","field_type":"7","ext_info":["客户多选2","客户多选3","服务型（无库存）"],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"","format":""},{"id":"next_follow_up_time","name":"下次跟进时间","base":"1","require":"0","group_id":"5","hint":"","field_type":"10","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"","format":""},{"id":"2607416847","name":"Tony测试字段1","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"","format":""},{"id":"2607416851","name":"Tony测试字段2","base":"0","require":"0","group_id":"5","hint":"","field_type":"5","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"","format":""},{"id":"2717392482","name":"zzz下拉单","base":"0","require":"0","group_id":"5","hint":"","field_type":"3","ext_info":["zq1","z2","z3"],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"","format":""},{"id":"2717396393","name":"15555t2","base":"0","require":"0","group_id":"5","hint":"","field_type":"3","ext_info":["西联汇款","西联汇款2","西联汇款3","西联汇款4","西联汇款66"],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"","format":""},{"id":"2717396378","name":"15terst","base":"0","require":"0","group_id":"5","hint":"","field_type":"3","ext_info":["信保订单","Paypal","支付宝","T/T 美元账户","西联汇款"],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","format":""},{"id":"3082019289","name":"客户-日期时间字段","base":"0","require":"0","group_id":"5","hint":"","field_type":"10","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"","format":""},{"id":"3082362156","name":"xiaohei","base":"0","require":"0","group_id":"5","hint":"","field_type":"3","ext_info":["1","2"],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"","format":""}]}],"customers":[{"customer_id":0,"client_id":14119,"user_id":[11858711],"lead_id":1195569776,"company_id":0,"company_customer_id":0,"name":"Ruyande Yvmi","email":"<EMAIL>","main_customer_flag":1,"post_grade":0,"post":"Buyer manager","tel_list":[["","8615827426855"],["1","2319498464153"]],"full_tel_list":["8615827426855","12319498464153"],"is_archive":1,"birth":"","gender":1,"contact":[{"type":"facebook","value":"https://www.facebook.com/melscience/"},{"type":"linkedin","value":"https://www.linkedin.com/in/aldona-skiauteryt%C4%97-%E6%B0%B4%E7%AB%B9-b10a131b1/"}],"group_id":0,"tag":[],"user_data":[],"image_list":[1120510823],"external_field_data":{"1102611669":"","1102611670":"","1102611671":"","1102611672":"","1102611675":""},"remark":null,"create_time":"2021-11-17 14:25:28","update_time":"2021-11-17 14:25:28","archive_time":"2021-11-17 14:25:28","order_time":"2021-11-17 14:25:28","edm_time":"1970-01-01 00:00:00","send_mail_time":"1970-01-01 00:00:00","receive_mail_time":"1970-01-01 00:00:00","mail_time":"1970-01-01 00:00:00","email_id":102045,"source_customer_id":1195569775,"fields":[{"id":"main_customer_flag","name":"主要联系人","base":"1","require":"0","hint":"","field_type":"3","ext_info":[],"disable_flag":"0","is_editable":"1","default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":1,"group_id":0,"field_customer_id":0,"format":"1"},{"id":"name","name":"昵称","base":"1","require":"0","hint":"","field_type":"1","ext_info":[],"disable_flag":"0","is_editable":"1","default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"Ruyande Yvmi","group_id":0,"field_customer_id":0,"format":"Ruyande Yvmi"},{"id":"email","name":"邮箱","base":"1","require":"0","hint":"","field_type":"1","ext_info":[],"disable_flag":"0","is_editable":"1","default":"","type":"5","unique_check":"1","unique_prevent":"2","unique_message":"","value":"ivanayvonnes@yandex.ru111012","group_id":0,"field_customer_id":0,"format":"<EMAIL>"},{"id":"remark","name":"联系人备注","base":"1","require":"0","hint":"","field_type":"2","ext_info":[],"disable_flag":"0","is_editable":"1","default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0,"field_customer_id":0,"format":""},{"id":"post_grade","name":"职级","base":"1","require":"0","hint":"","field_type":"3","ext_info":[],"disable_flag":"0","is_editable":"1","default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":0,"group_id":0,"field_customer_id":0,"format":"0"},{"id":"birth","name":"生日","base":"1","require":"0","hint":"","field_type":"4","ext_info":[],"disable_flag":"0","is_editable":"1","default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0,"field_customer_id":0,"format":""},{"id":"post","name":"职位","base":"1","require":"0","hint":"","field_type":"1","ext_info":[],"disable_flag":"0","is_editable":"1","default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"Buyer manager","group_id":0,"field_customer_id":0,"format":"Buyer manager"},{"id":"contact","name":"社交平台","base":"1","require":"0","hint":"sdsdsd","field_type":"2","ext_info":[],"disable_flag":"0","is_editable":"1","default":"","type":"5","unique_check":"1","unique_prevent":"2","unique_message":"已存在相同的社交平台","value":[{"type":"facebook","value":"https://www.facebook.com/melscience/"},{"type":"linkedin","value":"https://www.linkedin.com/in/aldona-skiauteryt%C4%97-%E6%B0%B4%E7%AB%B9-b10a131b1/"}],"group_id":0,"field_customer_id":0,"format":"[{\"type\":\"facebook\",\"value\":\"https:\\/\\/www.facebook.com\\/melscience\\/\"},{\"type\":\"linkedin\",\"value\":\"https:\\/\\/www.linkedin.com\\/in\\/aldona-skiauteryt%C4%97-%E6%B0%B4%E7%AB%B9-b10a131b1\\/\"}]"},{"id":"tel_list","name":"联系电话","base":"1","require":"0","hint":"联系电话提示","field_type":"1","ext_info":[],"disable_flag":"0","is_editable":"1","default":"","type":"5","unique_check":"1","unique_prevent":"2","unique_message":"","value":[["","8615827426855"],["1","2319498464153"]],"group_id":0,"field_customer_id":0,"format":"[[\"\",\"8615827426855\"],[\"1\",\"2319498464153\"]]"},{"id":"image_list","name":"头像/名片","base":"1","require":"0","hint":" ","field_type":"6","ext_info":[],"disable_flag":"0","is_editable":"1","default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":[1120510823],"group_id":0,"field_customer_id":0,"format":"[{\"file_id\":1120510823,\"file_name\":\"H0b945712b2ce4f8c8e42a074722040621.jpg\",\"file_size\":\"15828\",\"file_path\":\"https:\\/\\/v4client.oss-cn-hangzhou.aliyuncs.com\\/other\\/img\\/11858592\\/2ae4eb4de427f988b7b90df0ce39481d74d61e0a7a456e8f738a1d3b375f1463.jpg\",\"file_preview_url\":\"https:\\/\\/v4client.oss-cn-hangzhou.aliyuncs.com\\/other%2Fimg%2F11858592%2F2ae4eb4de427f988b7b90df0ce39481d74d61e0a7a456e8f738a1d3b375f1463.jpg?response-content-disposition=inline%3Bfilename%3DH0b945712b2ce4f8c8e42a074722040621.jpg%3Bfilename%2A%3Dutf-8%27%27H0b945712b2ce4f8c8e42a074722040621.jpg&response-content-type=image%2Fjpeg&OSSAccessKeyId=&Signature=xB3eT28dQRCx3jIatP28Cr%2BVTBQ%3D&Expires=1656219219\"}]"},{"id":"gender","name":"性别","base":"1","require":"0","hint":"","field_type":"3","ext_info":[],"disable_flag":"0","is_editable":"1","default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":1,"group_id":0,"field_customer_id":0,"format":"1"},{"id":"1105746753","name":"旺旺号","base":"0","require":"0","hint":"","field_type":"1","ext_info":[],"disable_flag":"0","is_editable":"1","default":"","type":"5","unique_check":"1","unique_prevent":"1","unique_message":"存在重复的旺旺号","value":"","group_id":0,"field_customer_id":0,"format":""},{"id":"1102611672","name":"自定义联系人下拉多选","base":"0","require":"0","hint":"","field_type":"7","ext_info":["小美","小梅","小妹"],"disable_flag":"0","is_editable":"1","default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0,"field_customer_id":0,"format":""},{"id":"1102611671","name":"自定义联系人下拉单选","base":"0","require":"0","hint":"","field_type":"3","ext_info":["剪刀","菜刀","尖刀"],"disable_flag":"0","is_editable":"1","default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0,"field_customer_id":0,"format":""},{"id":"1102611669","name":"自定义联系人文本","base":"0","require":"0","hint":"","field_type":"1","ext_info":[],"disable_flag":"0","is_editable":"1","default":"","type":"5","unique_check":"1","unique_prevent":"2","unique_message":"联系人相同啦","value":"","group_id":0,"field_customer_id":0,"format":""},{"id":"1102611670","name":"自定义联系多行文本","base":"0","require":"0","hint":"","field_type":"2","ext_info":[],"disable_flag":"0","is_editable":"1","default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0,"field_customer_id":0,"format":""},{"id":"1102611675","name":"自定义联系人数值","base":"0","require":"0","hint":"","field_type":"5","ext_info":[],"disable_flag":"0","is_editable":"1","default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0,"field_customer_id":0,"format":""},{"id":"3114951639","name":"小黑呀","base":"0","require":"0","hint":"","field_type":"4","ext_info":[],"disable_flag":"0","is_editable":"1","default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0,"field_customer_id":0,"format":""}]}]}
        ', true);
        $companyId = 0;
        $leadId = 1195569776;
//        $mail_id = 1102570029;
        $archive_flag = 1;
//    $this->enableProfile();
        $this->loginAsXpro();

        $params = [
            'data'         => json_encode($data),
            'company_id'   => $companyId,
//            'discovery_company_id' => 'ebeefd31740dd72002a3e5e32417f86c',
            'lead_id'      => $leadId,
            'archive_flag' => $archive_flag,
            'check_public' =>1,
//            'mail_id' => $mail_id,
//            'discovery_company_id' => '514543b32ab2ea6b',
        ];

        $this->callAction('saveCompanyData', $params);
        $this->responseOk();
    }

	public function testAddTrailComment() {

//		$this->loginUser(46900);
		$this->loginAsRuby();

		$param = [

			'trail_id' => 1104368875,
			'content' => 'xxx'
		];

		$this->callAction('AddTrailComment', $param);

		$this->responseOk();


//		$sql = 'SELECT DISTINCT ON (type) trail_id,
//				                          type,
//				                          create_user
//				FROM tbl_dynamic_trail
//				WHERE client_id = 1
//				  AND company_id = 3118519787
//				  AND enable_flag = 1;
//				  ';
//		$list = \DynamicTrail::getDbByClientId(1)->createCommand($sql)->queryAll();
//
//		foreach ($list as $item) {
//
//			$user = User::getLoginUser();
//
//			$comment = new \common\library\trail\comment\DynamicTrailComment($user->getClientId());
//
//			$comment->setTrailId($item['trail_id']);
//
//			$comment->setContent('zzz');
//
//			$comment->save();
//		}


	}

	public function testEditTrailComment() {

		$this->loginUser(46900);

		$param = [

			'comment_id' => 3160161946,
			'content' => '?????'
		];

		$this->callAction('EditTrailComment', $param);

		$this->responseOk();
	}

	public function testDeleteTrailComment() {

		$this->loginUser(46900);

		$param = [

			'comment_id' => 3160161939,
		];

		$this->callAction('DeleteTrailComment', $param);

		$this->responseOk();
	}

    public function testMoveToPublic() {

        $this->loginClient(1);

        $param = [
            'company_ids'      => [3179587426],
            'public_reason_id' => 3262744571,
            'pool_id'          => 0,
            'change_pool_flag' => 1,
        ];

        $this->callAction('MoveToPublic', $param);

        $this->responseOk();
    }

    public function testArchiveMail()
    {
        $this->loginAsWeason3();
        $this->callAction('archiveMail', 'company_id=3159980721&mail_id=3264796348');
        $this->responseOk();
    }

    public function testForbiddenCustomer()
    {
        $this->loginAsWeason3();
        $params = [
          'customer_id' => [3136637947,3446017799]
        ];
        $this->callAction('ForbiddenCustomer', $params);
        $this->responseOk();
    }

    public function testCancelForbiddenCustomer()
    {
        $this->loginAsWeason3();
        $params = [
            'customer_id' => [3136637947,3446017799]
        ];
        $this->callAction('CancelForbiddenCustomer', $params);
        $this->responseOk();
    }

    public function testCreateQuickPlanByCompany() {
        static::loginUser(*********);

        $company_ids = [3564760090,3564777341,3454960913];
        $this->callAction("createQuickPlanByCompany", $company_ids);
        $this->responseOk();
    }
}
