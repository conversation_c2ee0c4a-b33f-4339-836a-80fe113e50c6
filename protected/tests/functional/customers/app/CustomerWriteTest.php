<?php

use common\library\customer_v3\company\orm\Company;
use common\library\privilege_v3\Helper;
use common\library\privilege_v3\PrivilegeConstants;

/**
 * Created by PhpStorm.
 * User: troy.li
 * Date: 2017-12-22
 * Time: 10:53 AM
 */

class CustomerWriteTest extends AppFunctionalTestCase
{

    protected function originData()
    {
        return [
            [
                "origin_id"   => "8",
                "origin_name" => "合作交换"
            ],
            [
                "origin_id"   => "9",
                "origin_name" => "自主开发123123"
            ],
            [
                "origin_id"   => "10",
                "origin_name" => "其他"
            ],
            [
                "origin_id"   => "296439",
                "origin_name" => "展会"
            ],
            [
                "origin_id"   => "21733740",
                "origin_name" => "自主开发"
            ],
            [
                "origin_id"   => "21752933",
                "origin_name" => "enen"
            ],
            [
                "origin_id"   => "32256961",
                "origin_name" => "小满发现"
            ],
            [
                "origin_id"   => "32322715",
                "origin_name" => "你听得到"
            ],
            [
                "origin_id"   => "32433683",
                "origin_name" => "听说"
            ],
            [
                "origin_id"   => "32444243",
                "origin_name" => "dev"
            ],
            [
                "origin_id"   => "4",
                "origin_name" => "互联网"
            ],
            [
                "origin_id"   => "5",
                "origin_name" => "熟人介绍"
            ],
            [
                "origin_id"   => "6",
                "origin_name" => "广告投放"
            ],
            [
                "origin_id"   => "7",
                "origin_name" => "海关数据"
            ]
        ];
    }

    public function testSetOriginOrder()
    {
        $params = [
            'origin_ids' => implode(',', array_column($this->originData(), 'origin_id')),
        ];

        $result = $this->callAction('SetOriginOrder', $params);

        $this->responseOk($result);
        $this->echoResult($result);
    }

    public function testSaveCompany()
    {
//
//        $customer = (new \common\library\customer_v3\customer\orm\Customer(1))->loadByTel(93123456);
//        dd($customer->isExist());
//

        $data = '{"company":[{"group_id":1,"name":"基本信息","fields":[{"id":"name","name":"公司名称","base":"1","require":"1","group_id":"1","hint":"请输入公司名称","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"1","unique_prevent":"1","unique_message":"","value":"dsdvvv","format":"dsdvvv"},{"id":"short_name","name":"简称","base":"1","require":"0","group_id":"1","hint":"简称提示","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"1","unique_prevent":"1","unique_message":"已有相同的公司简称","value":"","format":""},{"id":"serial_id","name":"客户编号","base":"1","require":"0","group_id":"1","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"1","unique_prevent":"1","unique_message":"","value":"9688","format":"9688"},{"id":"origin","name":"客户来源","base":"1","require":"0","group_id":"1","hint":"客户来源提示","field_type":"3","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":0,"format":"0"}]},{"group_id":2,"name":"特征信息","fields":[{"id":"1514096184","name":"锁定期","base":"0","require":"0","group_id":"2","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"","format":""},{"id":"intention_level","name":"采购意向","base":"1","require":"0","group_id":"2","hint":"","field_type":"3","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":1,"format":"低"},{"id":"annual_procurement","name":"年采购额","base":"1","require":"0","group_id":"2","hint":"","field_type":"3","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":1,"format":"0~1千美元"},{"id":"biz_type","name":"客户类型","base":"1","require":"0","group_id":"2","hint":"客户类型提示","field_type":"3","ext_info":[{"zh-CN":"原材料供应商","zh-TW":"原材料供應商","en":"Raw Material Supplier","value":"原材料供应商","name":"原材料供应商","label":"原材料供应商"},{"zh-CN":"生产商","zh-TW":"生產商","en":"Producers","value":"生产商","name":"生产商","label":"生产商"},{"zh-CN":"加盟商","zh-TW":"加盟商","en":"Franchisee","value":"加盟商","name":"加盟商","label":"加盟商"},{"zh-CN":"渠道商","zh-TW":"渠道商","en":"Distributors","value":"渠道商","name":"渠道商","label":"渠道商"},{"zh-CN":"贸易商","zh-TW":"貿易商","en":"Trader","value":"贸易商","name":"贸易商","label":"贸易商"},{"zh-CN":"代理商","zh-TW":"代理商","en":"Agent","value":"代理商","name":"代理商","label":"代理商"},{"zh-CN":"批发商","zh-TW":"批發商","en":"Wholesaler","value":"批发商","name":"批发商","label":"批发商"},{"zh-CN":"分销商","zh-TW":"分銷商","en":"Distributor","value":"分销商","name":"分销商","label":"分销商"},{"zh-CN":"代销商","zh-TW":"代銷商","en":"Affiliate","value":"代销商","name":"代销商","label":"代销商"},{"zh-CN":"零售商","zh-TW":"零售商","en":"Retailer","value":"零售商","name":"零售商","label":"零售商"},{"zh-CN":"采购办事处","zh-TW":"採購辦事處","en":"Purchasing Office","value":"采购办事处","name":"采购办事处","label":"采购办事处"},{"zh-CN":"采购咨询公司","zh-TW":"採購諮詢公司","en":"Purchasing Consulting Company","value":"采购咨询公司","name":"采购咨询公司","label":"采购咨询公司"},{"zh-CN":"出口商","zh-TW":"出口商","en":"Exporter","value":"出口商","name":"出口商","label":"出口商"},{"zh-CN":"进口商","zh-TW":"進口商","en":"Importer","value":"进口商","name":"进口商","label":"进口商"},{"zh-CN":"个人消费者","zh-TW":"個人消費者","en":"Personal Consumer","value":"个人消费者","name":"个人消费者","label":"个人消费者"},{"zh-CN":"机构/团体消费者","zh-TW":"機構/團體消費者","en":"Institution/group consumer","value":"机构/团体消费者","name":"机构/团体消费者","label":"机构/团体消费者"},{"zh-CN":"工程商","zh-TW":"工程商","en":"Engineering","value":"工程商","name":"工程商","label":"工程商"},{"zh-CN":"其他","zh-TW":"其他","en":"Other","value":"其他","name":"其他","label":"其他"}],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"","format":""},{"id":"category_ids","name":"主营产品","base":"1","require":"0","group_id":"2","hint":"主营产品提示","field_type":"7","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":[],"format":"[]"},{"id":"country","name":"国家地区","base":"1","require":"0","group_id":"2","hint":"国家地区提示","field_type":"3","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"","format":"","countryInfo":{"country":"","province":"","city":""}},{"id":"province","name":"省份","base":"1","require":"0","group_id":"2","hint":"身份提示","field_type":"3","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"","format":""},{"id":"city","name":"城市","base":"1","require":"0","group_id":"2","hint":"城市提示","field_type":"3","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"","format":""},{"id":"scale_id","name":"规模","base":"1","require":"0","group_id":"2","hint":"规模提示","field_type":"3","ext_info":[{"value":1,"name":"少于59人","en_name":"Less than 59 employees","label":"少于59人"},{"value":2,"name":"60-149人","en_name":"60-149 employees","label":"60-149人"},{"value":3,"name":"150-499人","en_name":"150-499 employees","label":"150-499人"},{"value":4,"name":"500-999人","en_name":"500-999 employees","label":"500-999人"},{"value":5,"name":"1000-4999人","en_name":"1000-4999 employees","label":"1000-4999人"},{"value":6,"name":"5000人以上","en_name":"More than 5000 employees","label":"5000人以上"}],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":0,"format":"0"},{"id":"1101101841","name":"自定义日期-时间","base":"0","require":"0","group_id":"2","hint":"自定义时间提示","field_type":"10","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"","format":""},{"id":"timezone","name":"时区","base":"1","require":"0","group_id":"2","hint":"时区提示","field_type":"3","ext_info":[{"label":"零时区：伦敦","value":"0"},{"label":"东一区：罗马，巴黎","value":"1"},{"label":"东二区：雅典，以色列","value":"2"},{"label":"东三区：莫斯科，科威特","value":"3"},{"label":"东四区：喀布尔","value":"4"},{"label":"东五区：伊斯兰堡，卡拉奇","value":"5"},{"label":"东六区：阿拉木图，科伦坡","value":"6"},{"label":"东七区：曼谷，雅加达","value":"7"},{"label":"东八区：北京，香港，台湾","value":"8"},{"label":"东九区：东京","value":"9"},{"label":"东十区：悉尼","value":"10"},{"label":"东十一区：霍尼亚拉，马加丹","value":"11"},{"label":"东西十二区: 奥克兰","value":"12"},{"label":"西十一区：帕果帕果，阿洛菲","value":"-11"},{"label":"西十区：夏威夷","value":"-10"},{"label":"西九区：阿拉斯加","value":"-9"},{"label":"西八区：洛杉矶，旧金山","value":"-8"},{"label":"西七区：盐湖城、丹佛、凤凰城","value":"-7"},{"label":"西六区：芝加哥，休斯顿，亚特兰大","value":"-6"},{"label":"西五区：纽约，华盛顿，波士顿","value":"-5"},{"label":"西四区：加拿大，加拉加斯","value":"-4"},{"label":"西三区：巴西利亚","value":"-3"},{"label":"西二区：协调世界时","value":"-2"},{"label":"西一区：佛得角群岛","value":"-1"}],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"","format":""}]},{"group_id":3,"name":"管理信息","fields":[{"id":"star","name":"客户星级","base":"1","require":"1","group_id":"3","hint":"客户星级提示","field_type":"5","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":5,"format":"5"},{"id":"group_id","name":"分组","base":"1","require":"0","group_id":"3","hint":"分组提示","field_type":"3","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":1104306089,"format":"1104306089"},{"id":"cus_tag","name":"客户标签","base":"1","require":"0","group_id":"3","hint":"客户标签提示","field_type":"7","ext_info":[],"default":"[]","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":[],"format":"[]"},{"id":"trail_status","name":"客户状态","base":"1","require":"0","group_id":"3","hint":"客户状态提示","field_type":"3","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"1105890198","format":"1105890198"},{"id":"pool_id","name":"公海分组","base":"1","require":"1","group_id":"3","hint":"公海分组提示","field_type":"3","ext_info":[""],"default":"0","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":1105689531,"format":"真实客户数据"}]},{"group_id":4,"name":"联系信息","fields":[{"id":"lonlat","name":"经维度坐标","base":"1","require":"0","group_id":"4","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","value":{"longitude":"0.000000","latitude":"0.000000"},"format":"{\"longitude\":\"0.000000\",\"latitude\":\"0.000000\"}"},{"id":"tel","name":"座机","base":"1","require":"0","group_id":"4","hint":"座机提示","field_type":"1","ext_info":[],"default":"{\"tel_area_code\":\"\",\"tel\":\"\"}","disable_flag":"0","is_editable":1,"unique_check":"1","unique_prevent":"1","unique_message":"","value":{"tel_area_code":"","tel":""},"format":"{\"tel_area_code\":\"\",\"tel\":\"\"}"},{"id":"homepage","name":"主页","base":"1","require":"0","group_id":"4","hint":"主页提示","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"1","unique_prevent":"1","unique_message":"","value":"","format":""},{"id":"address","name":"联系地址","base":"1","require":"0","group_id":"4","hint":"联系地址提示","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"1","unique_prevent":"2","unique_message":"联系地址已存在","value":"","format":""},{"id":"fax","name":"传真","base":"1","require":"0","group_id":"4","hint":"传真提示","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"1","unique_prevent":"2","unique_message":"已有相同的传真","value":"","format":""}]},{"group_id":5,"name":"其他信息","fields":[{"id":"image_list","name":"图片","base":"1","require":"0","group_id":"5","hint":" ","field_type":"6","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":[],"format":"[]"},{"id":"remark","name":"备注","base":"1","require":"0","group_id":"5","hint":"","field_type":"2","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"","format":""},{"id":"1102352738","name":"客户下拉多选","base":"0","require":"0","group_id":"5","hint":"","field_type":"7","ext_info":["周黑鸭","煌上煌","麻辣烫"],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"","format":""},{"id":"1102603643","name":"客户-支付信息","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"dsdds","format":"dsdds"},{"id":"1105128163","name":"客户自定义下拉单选","base":"0","require":"0","group_id":"5","hint":"","field_type":"3","ext_info":["客户1","客户2","客户3"],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"","format":""},{"id":"1105162749","name":"客户地址","base":"0","require":"0","group_id":"5","hint":"","field_type":"2","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"1","unique_prevent":"1","unique_message":"客户地址已存在哟～～～","value":"","format":""},{"id":"1101101837","name":"自定义下拉单选","base":"0","require":"0","group_id":"5","hint":"","field_type":"3","ext_info":["客户1","彩虹","不柒"],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"","format":""},{"id":"1101101838","name":"自定义日期","base":"0","require":"0","group_id":"5","hint":"","field_type":"4","ext_info":[],"default":"2020-03-12","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"","format":""},{"id":"1101101839","name":"自定义数值","base":"0","require":"0","group_id":"5","hint":"请输入数值","field_type":"5","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"","format":""},{"id":"1105128171","name":"客户自定义日期","base":"0","require":"0","group_id":"5","hint":"","field_type":"4","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"","format":""},{"id":"1101101840","name":"自定义下拉多选","base":"0","require":"0","group_id":"5","hint":"请选择下拉多选","field_type":"7","ext_info":["客户多选2","客户多选3"],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"","format":""},{"id":"next_follow_up_time","name":"下次跟进时间","base":"1","require":"0","group_id":"5","hint":"","field_type":"10","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"","format":""}]}],"customers":[{"customer_id":1697933318,"company_id":1697933319,"name":"dd","email":"<EMAIL>","main_customer_flag":1,"fields":[{"id":"main_customer_flag","name":"主要联系人","base":"1","require":"0","hint":"","field_type":"3","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":1,"group_id":0,"field_customer_id":1697933318,"format":"1"},{"id":"name","name":"昵称","base":"1","require":"0","hint":"","field_type":"1","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"dd","group_id":0,"field_customer_id":1697933318,"format":"dd"},{"id":"email","name":"邮箱","base":"1","require":"0","hint":"","field_type":"1","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"1","unique_prevent":"1","unique_message":"","value":"<EMAIL>","group_id":0,"field_customer_id":1697933318,"format":"<EMAIL>"},{"id":"remark","name":"备注","base":"1","require":"0","hint":"","field_type":"2","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0,"field_customer_id":1697933318,"format":""},{"id":"post_grade","name":"职级","base":"1","require":"0","hint":"","field_type":"3","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":0,"group_id":0,"field_customer_id":1697933318,"format":"0"},{"id":"birth","name":"生日","base":"1","require":"0","hint":"","field_type":"4","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0,"field_customer_id":1697933318,"format":""},{"id":"post","name":"职位","base":"1","require":"0","hint":"","field_type":"1","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0,"field_customer_id":1697933318,"format":""},{"id":"tel_list","name":"联系电话","base":"1","require":"0","hint":"联系电话提示","field_type":"1","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"1","unique_prevent":"2","unique_message":"","value":[],"group_id":0,"field_customer_id":1697933318,"format":"[]"},{"id":"contact","name":"社交平台","base":"1","require":"0","hint":"","field_type":"2","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"1","unique_prevent":"2","unique_message":"已存在相同的社交平台","value":[],"group_id":0,"field_customer_id":1697933318,"format":"[]"},{"id":"image_list","name":"图片","base":"1","require":"0","hint":" ","field_type":"6","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":[],"group_id":0,"field_customer_id":1697933318,"format":"[]"},{"id":"gender","name":"性别","base":"1","require":"0","hint":"","field_type":"3","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":0,"group_id":0,"field_customer_id":1697933318,"format":"0"},{"id":"1105746753","name":"旺旺号","base":"0","require":"0","hint":"","field_type":"1","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"1","unique_prevent":"1","unique_message":"存在重复的旺旺号","value":"","group_id":0,"field_customer_id":1697933318,"format":""},{"id":"1102611672","name":"自定义联系人下拉多选","base":"0","require":"0","hint":"","field_type":"7","ext_info":["小美","小梅","小妹"],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0,"field_customer_id":1697933318,"format":""},{"id":"1102611671","name":"自定义联系人下拉单选","base":"0","require":"0","hint":"","field_type":"3","ext_info":["剪刀","菜刀","尖刀"],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0,"field_customer_id":1697933318,"format":""},{"id":"1102611669","name":"自定义联系人文本","base":"0","require":"0","hint":"","field_type":"1","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"1","unique_prevent":"2","unique_message":"联系人相同啦","value":"","group_id":0,"field_customer_id":1697933318,"format":""},{"id":"1102611670","name":"自定义联系多行文本","base":"0","require":"0","hint":"","field_type":"2","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0,"field_customer_id":1697933318,"format":""},{"id":"1102611675","name":"自定义联系人数值","base":"0","require":"0","hint":"","field_type":"5","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0,"field_customer_id":1697933318,"format":""}]}]}';
        $data = json_decode(urldecode($data), true);
        $companyId = 1697933319;

        $this->loginUser(46900);
        $params = [
            'company_id' => $companyId,
            'data' => json_encode($data),
        ];

//        dd(json_decode($params['data'], true, JSON_UNESCAPED_UNICODE));

        $this->callAction('saveCompany', $params);
        $this->responseOk();
    }

    public function testSaveCompanyAndCustomer()
    {

	    //$this->loginUser(46900);
        $this->loginAsWeason3();

        $companyId = 0;

	    $data = '{"company":[{"group_id":1,"name":"基本信息","value":"0621b","fields":[{"id":"name","name":"公司名称","value":"0621c","base":"1","require":"1","group_id":"1","hint":"","field_type":"1","ext_info":[""],"default":"","disable_flag":"0","is_editable":1,"unique_check":"1","unique_prevent":"1","unique_message":""},{"id":"serial_id","name":"客户编号","base":"1","require":"0","group_id":"1","hint":"这是客户编号哦","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"1","unique_prevent":"1","unique_message":"","value":""},{"id":"origin","name":"客户来源","base":"1","require":"0","group_id":"1","hint":"","field_type":"3","ext_info":[""],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":406},{"id":"2509481603","name":"下拉单选-zhengyiming-公司信息","base":"0","require":"0","group_id":"1","hint":"","field_type":"3","ext_info":["a","b","c"],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"2509481605","name":"日期-zhengyiming-公司信息","base":"0","require":"0","group_id":"1","hint":"","field_type":"4","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"2509481606","name":"数值-zhengyiming-公司信息","base":"0","require":"0","group_id":"1","hint":"","field_type":"5","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"2509481610","name":"下拉多选-zhengyiming-公司信息","base":"0","require":"0","group_id":"1","hint":"","field_type":"7","ext_info":["a","b","c"],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"2509481611","name":"日期-时间-zhengyiming-公司信息","base":"0","require":"0","group_id":"1","hint":"","field_type":"10","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"short_name","name":"简称","base":"1","require":"0","group_id":"1","hint":"","field_type":"1","ext_info":[""],"default":"","disable_flag":"0","is_editable":1,"unique_check":"1","unique_prevent":"2","unique_message":"简称不允许重复","value":""},{"id":"1887817526","name":"ccccc","base":"0","require":"0","group_id":"1","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"1","unique_prevent":"2","unique_message":"","value":""},{"id":"1108018263","name":"lvy新增1","base":"0","require":"0","group_id":"1","hint":"","field_type":"5","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"2509481589","name":"文本-zhengyiming-公司信息","base":"0","require":"0","group_id":"1","hint":"please input chrome","field_type":"1","ext_info":[],"default":"chrome","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"字段重复111","value":"chrome"}]},{"group_id":2,"name":"特征信息","fields":[{"id":"province","name":"省份","base":"1","require":"0","group_id":"2","hint":"","field_type":"3","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"country","name":"国家地区","base":"1","require":"0","group_id":"2","hint":"","field_type":"3","ext_info":[""],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"","countryInfo":{"country":"","province":"","city":""}},{"id":"timezone","name":"时区","base":"1","require":"0","group_id":"2","hint":"","field_type":"3","ext_info":[{"label":"零时区：伦敦","value":"0"},{"label":"东一区：罗马，巴黎","value":"1"},{"label":"东二区：雅典，以色列","value":"2"},{"label":"东三区：莫斯科，科威特","value":"3"},{"label":"东四区：喀布尔","value":"4"},{"label":"东五区：伊斯兰堡，卡拉奇","value":"5"},{"label":"东六区：阿拉木图，科伦坡","value":"6"},{"label":"东七区：曼谷，雅加达","value":"7"},{"label":"东八区：北京，香港，台湾","value":"8"},{"label":"东九区：东京","value":"9"},{"label":"东十区：悉尼","value":"10"},{"label":"东十一区：霍尼亚拉，马加丹","value":"11"},{"label":"东西十二区: 奥克兰","value":"12"},{"label":"西十一区：帕果帕果，阿洛菲","value":"-11"},{"label":"西十区：夏威夷","value":"-10"},{"label":"西九区：阿拉斯加","value":"-9"},{"label":"西八区：洛杉矶，旧金山","value":"-8"},{"label":"西七区：盐湖城、丹佛、凤凰城","value":"-7"},{"label":"西六区：芝加哥，休斯顿，亚特兰大","value":"-6"},{"label":"西五区：纽约，华盛顿，波士顿","value":"-5"},{"label":"西四区：加拿大，加拉加斯","value":"-4"},{"label":"西三区：巴西利亚","value":"-3"},{"label":"西二区：协调世界时","value":"-2"},{"label":"西一区：佛得角群岛","value":"-1"}],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"city","name":"城市","base":"1","require":"0","group_id":"2","hint":"","field_type":"3","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"category_ids","name":"主营产品","base":"1","require":"0","group_id":"2","hint":"","field_type":"7","ext_info":[""],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":[]},{"id":"scale_id","name":"规模","base":"1","require":"0","group_id":"2","hint":"","field_type":"3","ext_info":[{"value":1,"name":"少于59人","en_name":"Less than 59 employees","label":"少于59人"},{"value":2,"name":"60-149人","en_name":"60-149 employees","label":"60-149人"},{"value":3,"name":"150-499人","en_name":"150-499 employees","label":"150-499人"},{"value":4,"name":"500-999人","en_name":"500-999 employees","label":"500-999人"},{"value":5,"name":"1000-4999人","en_name":"1000-4999 employees","label":"1000-4999人"},{"value":6,"name":"5000人以上","en_name":"More than 5000 employees","label":"5000人以上"}],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"intention_level","name":"采购意向","base":"1","require":"0","group_id":"2","hint":"","field_type":"3","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"biz_type","name":"客户类型","base":"1","require":"0","group_id":"2","hint":"","field_type":"3","ext_info":[{"zh-CN":"原材料供应商","zh-TW":"原材料供應商","en":"Raw Material Supplier","value":"原材料供应商","name":"原材料供应商","label":"原材料供应商"},{"zh-CN":"生产商","zh-TW":"生產商","en":"Producers","value":"生产商","name":"生产商","label":"生产商"},{"zh-CN":"加盟商","zh-TW":"加盟商","en":"Franchisee","value":"加盟商","name":"加盟商","label":"加盟商"},{"zh-CN":"渠道商","zh-TW":"渠道商","en":"Distributors","value":"渠道商","name":"渠道商","label":"渠道商"},{"zh-CN":"贸易商","zh-TW":"貿易商","en":"Trader","value":"贸易商","name":"贸易商","label":"贸易商"},{"zh-CN":"代理商","zh-TW":"代理商","en":"Agent","value":"代理商","name":"代理商","label":"代理商"},{"zh-CN":"批发商","zh-TW":"批發商","en":"Wholesaler","value":"批发商","name":"批发商","label":"批发商"},{"zh-CN":"分销商","zh-TW":"分銷商","en":"Distributor","value":"分销商","name":"分销商","label":"分销商"},{"zh-CN":"代销商","zh-TW":"代銷商","en":"Affiliate","value":"代销商","name":"代销商","label":"代销商"},{"zh-CN":"零售商","zh-TW":"零售商","en":"Retailer","value":"零售商","name":"零售商","label":"零售商"},{"zh-CN":"采购办事处","zh-TW":"採購辦事處","en":"Purchasing Office","value":"采购办事处","name":"采购办事处","label":"采购办事处"},{"zh-CN":"采购咨询公司","zh-TW":"採購諮詢公司","en":"Purchasing Consulting Company","value":"采购咨询公司","name":"采购咨询公司","label":"采购咨询公司"},{"zh-CN":"出口商","zh-TW":"出口商","en":"Exporter","value":"出口商","name":"出口商","label":"出口商"},{"zh-CN":"进口商","zh-TW":"進口商","en":"Importer","value":"进口商","name":"进口商","label":"进口商"},{"zh-CN":"个人消费者","zh-TW":"個人消費者","en":"Personal Consumer","value":"个人消费者","name":"个人消费者","label":"个人消费者"},{"zh-CN":"机构/团体消费者","zh-TW":"機構/團體消費者","en":"Institution/group consumer","value":"机构/团体消费者","name":"机构/团体消费者","label":"机构/团体消费者"},{"zh-CN":"工程商","zh-TW":"工程商","en":"Engineering","value":"工程商","name":"工程商","label":"工程商"},{"zh-CN":"其他","zh-TW":"其他","en":"Other","value":"其他","name":"其他","label":"其他"}],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"annual_procurement","name":"年采购额","base":"1","require":"0","group_id":"2","hint":"","field_type":"3","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"1108018301","name":"lvy新增文本","base":"0","require":"0","group_id":"2","hint":"","field_type":"2","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"1119201714","name":"公司信息-客户管理自定义","base":"0","require":"0","group_id":"2","hint":"这是自定义哦~","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"1","unique_prevent":"1","unique_message":"","value":""},{"id":"2509481602","name":"多行文本-zhengyiming-公司信息","base":"0","require":"0","group_id":"2","hint":"请输入内容","field_type":"2","ext_info":[],"default":"ym公司信息","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"此处显示一条友好提示","value":"ym公司信息"},{"id":"3043781064","name":"测试-tv-多行文本","base":"0","require":"0","group_id":"2","hint":"请输入","field_type":"2","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"1","unique_prevent":"2","unique_message":"重复了","value":""}]},{"group_id":3,"name":"管理信息","fields":[{"id":"group_id","name":"分组","base":"1","require":"0","group_id":"3","hint":"","field_type":"3","ext_info":[""],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"cus_tag","name":"客户标签","base":"1","require":"1","group_id":"3","hint":"","field_type":"7","ext_info":[""],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":["1129031336"]},{"id":"trail_status","name":"客户阶段","base":"1","require":"0","group_id":"3","hint":"","field_type":"3","ext_info":[""],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"pool_id","name":"公海分组","base":"1","require":"1","group_id":"3","hint":"我是公海分组字段提示，其实么用","field_type":"3","ext_info":[""],"default":"0","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":0},{"id":"star","name":"客户星级","base":"1","require":"0","group_id":"3","hint":"","field_type":"5","ext_info":[""],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"0"}]},{"group_id":4,"name":"联系信息","fields":[{"id":"homepage","name":"公司网址","base":"1","require":"1","group_id":"4","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"1","unique_prevent":"1","unique_message":"","value":"1231310621a"},{"id":"fax","name":"传真","base":"1","require":"0","group_id":"4","hint":"","field_type":"1","ext_info":[""],"default":"","disable_flag":"0","is_editable":1,"unique_check":"1","unique_prevent":"2","unique_message":"传真不允许重复","value":""},{"id":"tel","name":"座机","base":"1","require":"0","group_id":"4","hint":"","field_type":"1","ext_info":[""],"default":"","disable_flag":"0","is_editable":1,"unique_check":"1","unique_prevent":"1","unique_message":"","value":{"tel":"","tel_area_code":""}},{"id":"1118472444","name":"CCT2-Effect","base":"0","require":"0","group_id":"4","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"address","name":"详细地址","base":"1","require":"0","group_id":"4","hint":"000","field_type":"1","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"1","unique_prevent":"2","unique_message":"","value":""},{"id":"1106146700","name":"sharon测试备注","base":"0","require":"0","group_id":"4","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"1","unique_prevent":"1","unique_message":"","value":""},{"id":"lonlat","name":"经维度坐标","base":"1","require":"0","group_id":"4","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","value":{"longitude":0,"latitude":0}},{"id":"1108043070","name":"lvy新增日期","base":"0","require":"0","group_id":"4","hint":"","field_type":"4","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","value":""}]},{"group_id":5,"name":"其他信息","fields":[{"id":"1111328455","name":"janetest1","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"1111328400","name":"janetest","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"1101160789","name":"lora客户公司－数值","base":"0","require":"0","group_id":"5","hint":"","field_type":"5","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"1100286110","name":"OS - 重要测试字段，千万别动","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"1100149746","name":"lora测试有数据的字段","base":"0","require":"0","group_id":"5","hint":"","field_type":"2","ext_info":[],"default":"有数据字段的一个默认值","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"2","unique_message":"lora测试有数据的字段内容重复啦～","value":"有数据字段的一个默认值"},{"id":"**********","name":"ollar Payment, the remittance instruction route:  *5","base":"0","require":"0","group_id":"5","hint":"","field_type":"7","ext_info":["IMEE GROUP LTD EURO Dollar Payment, the remittance instruction route:  *57A: Account with institutio"],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"**********","name":"lora客户公司－多行文本","base":"0","require":"0","group_id":"5","hint":"","field_type":"2","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"********","name":"sharon自定义字段下拉","base":"0","require":"0","group_id":"5","hint":"下啦提示","field_type":"3","ext_info":["琵琶","古琴-xxx"],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"**********","name":"lora客户公司－文本","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"1","unique_prevent":"1","unique_message":"1243434","value":""},{"id":"**********","name":"线索&客户-公司1-文本 - xiugai - dd","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"1118262834","name":"young-2333333","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"1118263027","name":"122","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"1118263031","name":"ProductList","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"1118263048","name":"333","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"1118263055","name":"dd","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"1118059887","name":"poi","base":"0","require":"0","group_id":"5","hint":"","field_type":"7","ext_info":["123","234"],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"1118312205","name":"20210413-1","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"remark","name":"公司备注","base":"1","require":"0","group_id":"5","hint":"啦啦啦","field_type":"2","ext_info":[],"default":"龙琴","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"龙琴"},{"id":"1102723524","name":"sharon测试iOS多行文本默认文案","base":"0","require":"0","group_id":"5","hint":"","field_type":"2","ext_info":[],"default":"iOS","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","value":"iOS"},{"id":"image_list","name":"公司logo","base":"1","require":"0","group_id":"5","hint":" ","field_type":"6","ext_info":[""],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":[]},{"id":"1106325565","name":"线索&客户-公司7-数值","base":"0","require":"0","group_id":"5","hint":"","field_type":"5","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"1107572141","name":"线索自定义筛选字段","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"next_follow_up_time","name":"下次跟进时间","base":"1","require":"0","group_id":"5","hint":"","field_type":"10","ext_info":[],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"1106335125","name":"自定义字段-数值","base":"0","require":"0","group_id":"5","hint":"","field_type":"5","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"1118426017","name":"单选字段","base":"0","require":"0","group_id":"5","hint":"","field_type":"3","ext_info":["单选1","单选2","单选3"],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"1971174486","name":"我是数字","base":"0","require":"0","group_id":"5","hint":"","field_type":"5","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"1118472442","name":"新增一个字段 - os","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"1118439606","name":"新建一个","base":"0","require":"0","group_id":"5","hint":"","field_type":"2","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"1106325536","name":"线索&客户-公司2-多行文本","base":"0","require":"0","group_id":"5","hint":"","field_type":"2","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"1118472445","name":"字段名称 - os001","base":"0","require":"0","group_id":"5","hint":"","field_type":"3","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"1106325537","name":"线索&客户-公司3-下拉单选","base":"0","require":"0","group_id":"5","hint":"","field_type":"3","ext_info":["23人","卫栖梧若","接口环境净空法师刘丽说离开时"],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"1118472446","name":"os - 字段 - 001 - 0","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"1118472443","name":"os - 2","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"2178741392","name":"1","base":"0","require":"0","group_id":"5","hint":"","field_type":"4","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"1106325564","name":"线索&客户-公司6-日期+时间","base":"0","require":"0","group_id":"5","hint":"","field_type":"10","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"2408213670","name":"富通客户代码","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"1129001406","name":"年采购额1","base":"0","require":"0","group_id":"5","hint":"","field_type":"5","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"2433403748","name":"11111","base":"0","require":"0","group_id":"5","hint":"","field_type":"2","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"2","unique_message":"","value":""},{"id":"1139914768","name":"多选客户字段","base":"0","require":"0","group_id":"5","hint":"","field_type":"7","ext_info":["a","b","v","c","q","g","hj"],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"1106325554","name":"线索&客户-公司4-下拉多选","base":"0","require":"0","group_id":"5","hint":"","field_type":"7","ext_info":["1234567890","abcdefghijklmn","你是我的小丫小苹果","怎么爱你都不限购"],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"1106325563","name":"线索&客户-公司5-日期","base":"0","require":"0","group_id":"5","hint":"","field_type":"4","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"1127474852","name":"自定义字段-日期","base":"0","require":"0","group_id":"5","hint":"","field_type":"4","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"1119201713","name":"公司信息--线索自定义字段","base":"0","require":"0","group_id":"5","hint":"","field_type":"3","ext_info":["1","2"],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"2408213661","name":"ces 11","base":"0","require":"0","group_id":"5","hint":"","field_type":"1","ext_info":[],"default":"","disable_flag":"1","is_editable":"1","unique_check":"0","unique_prevent":"1","unique_message":"","value":""},{"id":"3025700946","name":"客户老编号","base":"0","require":"1","group_id":"5","hint":"1111232","field_type":"1","ext_info":[],"default":"rq","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":"rq"},{"id":"3043780965","name":"下拉多选","base":"0","require":"0","group_id":"5","hint":"","field_type":"7","ext_info":["test","苹果","雪梨"],"default":"","disable_flag":"0","is_editable":1,"unique_check":"0","unique_prevent":"1","unique_message":"","value":""}]}],"customers":[{"customer_id":0,"company_id":0,"email":"","name":"","main_customer_flag":1,"fields":[{"id":"contact","name":"社交平台","base":"1","require":"0","hint":"","field_type":"2","ext_info":[""],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"1","unique_prevent":"2","unique_message":"","value":[],"group_id":0},{"id":"tel_list","name":"联系电话","base":"1","require":"0","hint":"","field_type":"1","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"1","unique_prevent":"1","unique_message":"","value":[],"group_id":0},{"id":"birth","name":"生日","base":"1","require":"0","hint":"","field_type":"4","ext_info":[""],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"name","name":"昵称","base":"1","require":"0","hint":"","field_type":"1","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"1231231","group_id":0},{"id":"email","name":"邮箱","base":"1","require":"0","hint":"","field_type":"1","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"1","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"post_grade","name":"职级","base":"1","require":"0","hint":"","field_type":"3","ext_info":[""],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"1119201734","name":"自定义联系字段~","base":"0","require":"0","hint":"我添加的~","field_type":"5","ext_info":[],"disable_flag":"1","is_editable":"1","default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"main_customer_flag","name":"主要联系人","base":"1","require":"0","hint":"","field_type":"3","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":1,"group_id":0},{"id":"post","name":"职位","base":"1","require":"0","hint":"","field_type":"1","ext_info":[""],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"2509481695","name":"日期-zhengyiming-联系信息","base":"0","require":"0","hint":"","field_type":"4","ext_info":[],"disable_flag":"1","is_editable":"1","default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"1127474215","name":"职位信息","base":"0","require":"0","hint":"","field_type":"1","ext_info":[],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"1297594717","name":"*********","base":"0","require":"0","hint":"","field_type":"4","ext_info":[],"disable_flag":"1","is_editable":"1","default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"gender","name":"性别","base":"1","require":"0","hint":"","field_type":"3","ext_info":[""],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"2509481699","name":"下拉多选-zhengyiming-联系信息","base":"0","require":"0","hint":"","field_type":"7","ext_info":["a","b","c"],"disable_flag":"1","is_editable":"1","default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"image_list","name":"头像/名片","base":"1","require":"0","hint":" ","field_type":"6","ext_info":[""],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":[],"group_id":0},{"id":"1315971754","name":"+++++","base":"0","require":"0","hint":"","field_type":"1","ext_info":[],"disable_flag":"1","is_editable":"1","default":"","type":"5","unique_check":"1","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"remark","name":"联系人备注","base":"1","require":"0","hint":"","field_type":"2","ext_info":[""],"disable_flag":"0","is_editable":1,"default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"2509481698","name":"数值-zhengyiming-联系信息","base":"0","require":"0","hint":"","field_type":"5","ext_info":[],"disable_flag":"1","is_editable":"1","default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"2509481694","name":"下拉单选-zhengyiming-联系信息","base":"0","require":"0","hint":"","field_type":"3","ext_info":["a","b","c"],"disable_flag":"1","is_editable":"1","default":"","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"","group_id":0},{"id":"2509481700","name":"日期-时间-zhengyiming-联系信息","base":"0","require":"0","hint":"sdfaf","field_type":"10","ext_info":[],"disable_flag":"0","is_editable":1,"default":"2022-04-01 10:55:00","type":"5","unique_check":"0","unique_prevent":"1","unique_message":"","value":"2022-04-01 10:55:00","group_id":0},{"id":"2509481689","name":"文本-zhengyiming-联系信息","base":"0","require":"0","hint":"asdfa","field_type":"1","ext_info":[],"disable_flag":"1","is_editable":"1","default":"ddd","type":"5","unique_check":"1","unique_prevent":"2","unique_message":"","value":"ddd","group_id":0},{"id":"2509481693","name":"多行文本-zhengyiming-联系信息","base":"0","require":"0","hint":"12312345","field_type":"2","ext_info":[],"disable_flag":"0","is_editable":1,"default":"test","type":"5","unique_check":"1","unique_prevent":"1","unique_message":"","value":"testdfdsfdsfsdfsdf","group_id":0}]}]}';
        $params = [
            'company_id' => $companyId,
            'data'       => $data,
//            'lead_id' => $leadId,
        ];

        $saveCompany = $this->callAction('SaveCompanyAndCustomer', $params);
        $this->responseOk();
    }

    public function testArchiveLeadToCompany()
    {
//        $lead = $this->lead;

        static::loginUser(11857806);
        $leadId = 34900605;
        Lead::model()->updateByPk($leadId, ['is_archive' => 1]);
        foreach (LeadCustomer::model()->findAllByAttributes(['lead_id' => $leadId]) as $leadCustomer) {
            $leadCustomer->is_archive = 1;
            $leadCustomer->save();
        }
//        $companyId = 32693721;
        $companyId = 0;

//        $companyId = 32621413;
//        $leadId = $lead->lead_id;
        $params = [
            'lead_id'    => $leadId,
            'company_id' => $companyId,
        ];
        $data = $this->callAction('archive', $params, 'LeadRead');
        $data = $data['data'];
        $data['company'][0]['fields'][0]['value'] = $this->faker()->company;
        array_walk($data['customers'][0]['fields'], function (&$field) {
            if ($field['id'] == 'main_customer_flag') {
                $field['value'] = 1;
            }
            if ($field['id'] == 'email') {
                $field['value'] = $this->faker()->email;
            }
        });
//        $company = new \common\library\customer_v3\company\orm\Company(static::$clientId, $companyId);
//        $company->getFormatter()->detailInfoSetting();
//        $companyData = $company->getAttributes();
//        $data = [
//            'company'   => $companyData['company'],
//            'customers' => $companyData['customers'],
//        ];
//        $data['customers'][0]['fields'][1]['value'] = $this->faker()->email;
        $params = [
            'company_id' => $companyId,
            'data'       => json_encode($data),
            'lead_id'    => $leadId
        ];

        $saveCompany = $this->callAction('SaveCompanyAndCustomer', $params);
        $this->responseOk();

        $this->seeInDatabase(Lead::class, [
            'client_id'  => static::$clientId,
            'company_id' => $companyId,
            'lead_id'    => $leadId,
        ]);

        foreach ($data['customers'] as $leadCustomer) {
            $this->seeInDatabase(LeadCustomer::class, [
                'client_id'           => static::$clientId,
                'lead_id'             => $leadId,
                'company_id'          => $companyId,
                'company_customer_id' => $leadCustomer['customer_id'],
                'customer_id'         => $leadCustomer['source_customer_id'],
            ]);
        }

    }

    public function testSaveCustomer()
    {
//        static::loginUser(765);
//        $companyId = 21774532;
        $customerId = 21743785;
        $customer = new \common\library\customer_v3\customer\orm\Customer(static::$clientId, $customerId);
        $companyId = $customer->company_id;
        $customer->getFormatter()->detailInfoSetting();
//        $data = $customer->getAttributes();
//        array_walk($data['fields'], function (&$field) {
//            if ($field['id'] == 'main_customer_flag') {
//                $field['value'] = 1;
//            }
//        });
//        $data['main_customer_flag'] = 1;

        $data = json_decode('{
  "customer_id": "94235339",
  "company_id": "*********",
  "email": "<EMAIL>",
  "name": "",
  "main_customer_flag": 0,
  "fields": [
    {
      "id": "name",
      "name": "昵称",
      "base": "1",
      "require": "0",
      "group_id": "0",
      "hint": "",
      "field_type": "1",
      "default": "",
      "disable_flag": "0",
      "is_editable": "1",
      "value": "",
      "format": "",
      "ext_info": []
    },
    {
      "id": "email",
      "name": "邮箱",
      "base": "1",
      "require": "1",
      "group_id": "0",
      "hint": "",
      "field_type": "1",
      "default": "",
      "disable_flag": "0",
      "is_editable": "1",
      "value": "<EMAIL>",
      "format": "<EMAIL>",
      "ext_info": []
    },
    {
      "id": "post_grade",
      "name": "职级",
      "base": "1",
      "require": "0",
      "group_id": "0",
      "hint": "",
      "field_type": "3",
      "default": "",
      "disable_flag": "0",
      "is_editable": "1",
      "value": 0.0,
      "format": "0",
      "ext_info": []
    },
    {
      "id": "post",
      "name": "职位",
      "base": "1",
      "require": "0",
      "group_id": "0",
      "hint": "",
      "field_type": "1",
      "default": "",
      "disable_flag": "0",
      "is_editable": "1",
      "value": "",
      "format": "",
      "ext_info": []
    },
    {
      "id": "tel_list",
      "name": "联系电话",
      "base": "1",
      "require": "0",
      "group_id": "0",
      "hint": "",
      "field_type": "1",
      "default": "",
      "disable_flag": "0",
      "is_editable": "1",
      "value": [],
      "format": "[]",
      "ext_info": []
    },
    {
      "id": "contact",
      "name": "社交平台",
      "base": "1",
      "require": "0",
      "group_id": "0",
      "hint": "",
      "field_type": "2",
      "default": "",
      "disable_flag": "0",
      "is_editable": "1",
      "value": [],
      "format": "[]",
      "ext_info": []
    },
    {
      "id": "birth",
      "name": "生日",
      "base": "1",
      "require": "0",
      "group_id": "0",
      "hint": "",
      "field_type": "4",
      "default": "",
      "disable_flag": "0",
      "is_editable": "1",
      "value": "",
      "format": "",
      "ext_info": []
    },
    {
      "id": "main_customer_flag",
      "name": "主要联系人",
      "base": "1",
      "require": "0",
      "group_id": "0",
      "hint": "",
      "field_type": "3",
      "default": "",
      "disable_flag": "0",
      "is_editable": "1",
      "value": 0,
      "format": "0",
      "ext_info": []
    },
    {
      "id": "gender",
      "name": "性别",
      "base": "1",
      "require": "0",
      "group_id": "0",
      "hint": "",
      "field_type": "3",
      "default": "",
      "disable_flag": "0",
      "is_editable": "1",
      "value": 1.0,
      "format": "1",
      "ext_info": []
    },
    {
      "id": "remark",
      "name": "备注",
      "base": "1",
      "require": "0",
      "group_id": "0",
      "hint": "",
      "field_type": "2",
      "default": "",
      "disable_flag": "0",
      "is_editable": "1",
      "value": "",
      "format": "",
      "ext_info": []
    },
    {
      "id": "image_list",
      "name": "图片",
      "base": "1",
      "require": "0",
      "group_id": "0",
      "hint": " ",
      "field_type": "6",
      "default": "",
      "disable_flag": "0",
      "is_editable": "1",
      "value": [],
      "format": "[]",
      "ext_info": []
    },
    {
      "id": "4089570792",
      "name": "im上",
      "base": "0",
      "require": "0",
      "group_id": "0",
      "hint": "",
      "field_type": "3",
      "default": "",
      "disable_flag": "1",
      "is_editable": "1",
      "value": "",
      "format": "",
      "ext_info": [
        "1",
        "2",
        "3"
      ]
    },
    {
      "id": "6355349057",
      "name": "lora2",
      "base": "0",
      "require": "0",
      "group_id": "0",
      "hint": "",
      "field_type": "3",
      "default": "",
      "disable_flag": "1",
      "is_editable": "1",
      "value": "",
      "format": "",
      "ext_info": [
        "1",
        "2"
      ]
    },
    {
      "id": "6711821122",
      "name": "wr日期-联系1-必填提示",
      "base": "0",
      "require": "0",
      "group_id": "0",
      "hint": "",
      "field_type": "4",
      "default": "",
      "disable_flag": "0",
      "is_editable": "1",
      "value": "",
      "format": "",
      "ext_info": []
    },
    {
      "id": "6711829875",
      "name": "wr文本-联系-默认",
      "base": "0",
      "require": "0",
      "group_id": "0",
      "hint": "",
      "field_type": "1",
      "default": "wr文本-联系-默认值",
      "disable_flag": "0",
      "is_editable": "1",
      "value": "wr文本-联系-默认值",
      "format": "",
      "ext_info": []
    },
    {
      "id": "6711833442",
      "name": "wr多行文本-联系",
      "base": "0",
      "require": "0",
      "group_id": "0",
      "hint": "",
      "field_type": "2",
      "default": "",
      "disable_flag": "0",
      "is_editable": "1",
      "value": "",
      "format": "",
      "ext_info": []
    },
    {
      "id": "6711845073",
      "name": "wr下拉单选-联系",
      "base": "0",
      "require": "0",
      "group_id": "0",
      "hint": "",
      "field_type": "3",
      "default": "",
      "disable_flag": "0",
      "is_editable": "1",
      "value": "",
      "format": "",
      "ext_info": [
        "正式环境",
        "dev环境",
        "Test环境"
      ]
    },
    {
      "id": "6711849176",
      "name": "wr日期-联系-默认0903",
      "base": "0",
      "require": "0",
      "group_id": "0",
      "hint": "",
      "field_type": "4",
      "default": "2018-09-03",
      "disable_flag": "0",
      "is_editable": "1",
      "value": "2018-09-03",
      "format": "",
      "ext_info": []
    },
    {
      "id": "6711854367",
      "name": "wr日期+时间-联系",
      "base": "0",
      "require": "0",
      "group_id": "0",
      "hint": "",
      "field_type": "10",
      "default": "",
      "disable_flag": "0",
      "is_editable": "1",
      "value": "",
      "format": "",
      "ext_info": []
    },
    {
      "id": "6971571231",
      "name": "测试隐藏字段",
      "base": "0",
      "require": "0",
      "group_id": "0",
      "hint": "",
      "field_type": "10",
      "default": "",
      "disable_flag": "1",
      "is_editable": "1",
      "value": "",
      "format": "",
      "ext_info": []
    }
  ]
}', true);

        $params = [
            'company_id'  => $companyId,
            'data'        => json_encode($data),
            'customer_id' => $customerId,
        ];

        $this->callAction('SaveCustomer', $params);

        $this->responseOk();

        $this->seeInDatabase(CustomerModel::class, ['customer_id' => $customerId, 'main_customer_flag' => 1]);
        $this->seeInDatabase(CompanyModel::class, ['company_id' => $companyId, 'main_customer' => $customerId]);
    }

    public function testSetPool()
    {
        $pools = \common\library\customer\pool\Helper::getUserPoolNameMap(static::$clientId, static::$userId);
        $pool_id = current(array_keys($pools));
        $company = CompanyModel::model()->findByAttributes([
            'client_id'  => static::$clientId,
            'user_id'    => '{}',
            'is_archive' => 1
        ]);

//        $company = CompanyModel::model()->findByAttributes(['client_id' => static::$clientId, 'user_id' => '{}', 'is_archive' => 1]);
        $company_id = $company->company_id;
        $company->pool_id = 0;
        $company->save();
        $this->echo($pool_id);
        $this->echo($company_id);

//        $pool_not_exist = -1;
//        $params = [
//            'company_ids' => [$company->company_id],
//            'pool_id' => $pool_not_exist,
//        ];
//
//        $this->expectExceptionMessage('没有该公海分组权限');
//        $this->callAction('setPool', $params);

        $params = [
            'company_ids' => [$company_id],
            'pool_id'     => $pool_id,
        ];
        $this->echo($params);

        $this->callAction('setPool', $params);
        $this->responseOk();
        $this->seeInDatabase(CompanyModel::class,
            ['client_id' => static::$clientId, 'company_id' => $company_id, 'pool_id' => $pool_id]);

        $this->callAction('CompanyDiffList', ['company_id' => $company_id], 'CustomerRead');
        $this->responseOk();
    }

    public function testDebugSetGroup()
    {
        static::loginUser('<EMAIL>');
        $companyId = 33888306;
        $groupId = '';
        $params = [
            'company_ids' => [$companyId],
            'group_id'    => $groupId,
        ];
        $this->echo($params);

        \Yii::t('privilege', 'Missing permission');

        $this->callAction('setGroup', $params);
        $this->responseOk();
    }

    public function testImport()
    {
        $this->loginUser('<EMAIL>');
        $clientId = static::$clientId;
        $groupList = \common\library\group\Helper::getGroupTreeName($clientId, Constants::TYPE_COMPANY);
//        $tree = \common\library\group\Helper::getGroupTree($clientId, Constants::TYPE_COMPANY, $groupIds = [], true);
        $groupMap = array_flip(array_map(function ($groupNames) {
            return implode('>', $groupNames);
        }, $groupList));
        dd($groupMap);
    }

    public function testUploadFile()
    {
        $fileName = '导入客户模板(世邦).xls';
        $fileFullPath = dirname(__FILE__) . '/files/' . $fileName;
        $fileKey = time() . md5($fileName) . '.xls';
        $upload = UploadService::uploadRealFile($fileFullPath, $fileName, $fileKey);
        $file = $upload->getFileObject();
        dd($file->getFileUrl());
    }

    public function testTransfer()
    {
        static::loginUser(11850882);
        $companyId = 38021081;
        $userId = 72135;
        $company = CompanyModel::model()->findByPk($companyId);
        $company->user_id = '{765}';
        $company->save();
//        $this->enableProfile();

        $params = [
            'company_ids' => [$companyId],
            'user_list'   => [$userId],
        ];
        $this->callAction('transfer', $params);
        $this->responseOk();

        $this->assertContains($userId, (new Company(static::$clientId, $companyId))->user_id);


        static::loginUser(11850882);
        $companyId = 38021081;
        $userId = 72135;
        $company = CompanyModel::model()->findByPk($companyId);
        $company->user_id = '{' . $userId . '}';
        $company->save();
//        $this->enableProfile();

        $params = [
            'company_ids' => [$companyId],
            'user_list'   => [$userId],
        ];
        $this->callAction('transfer', $params, 'SalesCustomerWrite');
        $this->responseOk();

        $this->assertContains($userId, (new Company(static::$clientId, $companyId))->user_id);
        $this->assertNotContains(static::$userId, (new Company(static::$clientId, $companyId))->user_id);
    }

    public function testHold()
    {
        $this->loginAsQiao();
        $company = $this->findPublic();
//        $group = GroupModel::model()->findByAttributes([
//            'client_id' => static::$clientId,
//            'type'      => Constants::TYPE_COMPANY
//        ]);
        $params = [
            'company_ids' => [$company->company_id],
            'group_id'    => -1,
        ];
        $this->echoParams($params);
        $this->callAction('hold', $params);
        $this->responseOk();

//        $this->callAction('CompanyDiffList', ['company_id' => $company->company_id], 'CustomerRead');
//        $this->responseOk();
    }

    protected function findPublic()
    {
        return CompanyModel::model()->findByAttributes([
            'client_id'  => static::$clientId,
            'user_id'    => '{}',
            'is_archive' => 1,
            'pool_id'    => 0
        ]);
    }

    public function testRemark()
    {
        static::loginUser(11855392);
        $params = [
            'customer_id' => 32369476,
            'company_id'  => 32369477,
            'content'     => $this->faker()->sentence,
        ];

        $this->callAction('remark', $params);
        $this->responseOk();
    }

    public function testMergeCompany()
    {
        $mainCompanyId = 34045834;
        $otherCompanyId = 34045829;
        $params = [
            'config' => [
                ''
            ]
        ];

    }

    public function testRemove()
    {
        static::loginUser(11855392);
        $params = [
            'company_ids' => [
                32320104,
                21797527,
                33228556,
                31905151,
                31862461,
                31862467,
                31859522,
                31860683,
                31860975,
                21838238,
                21840466,
                21840535,
                21840462,
                21840433,
                31865990,
            ],
        ];
        $this->callAction('remove', $params);
        $this->responseOk();
    }

    public function testAddTag()
    {
        static::loginUser(765);
        $this->loginAsRuby();
        $params = [
            'company_ids' => [3160245818],
            'tag_ids' => [3160251301, 3160020890, 3160020884],
        ];

        $this->callAction('addTag', $params);
        $this->responseOk();
    }

    public function testShare()
    {
        static::loginUser(11850882);
        $companyId = 38021081;
        $userId = 72135;
        $company = CompanyModel::model()->findByPk($companyId);
        $company->user_id = '{765}';
        $company->save();
        $this->enableProfile();

        $params = [
            'company_ids' => [$companyId],
            'user_list'   => [$userId],
        ];
        $this->callAction('share', $params, 'SalesCustomerWrite');
        $this->responseOk();

        $this->assertContains($userId, (new Company(static::$clientId, $companyId))->user_id);
    }

    /**
     *
     */
    public function testCustomerArchive()
    {
        $this->loginUser('46900');

        $req = [
          'mail_id' => '1100625488',
          'force_archive' => 1
        ];
        $this->callaction('customerArchive', $req, null );
        $this->responseOk();
    }
}
