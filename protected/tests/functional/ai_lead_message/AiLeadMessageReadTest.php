<?php


use common\library\ai_lead_message\AiLeadMessageService;

class AiLeadMessageReadTest extends InternalFunctionalTestCase
{
	public function testList(){
		$clientId = 14598;
		//登陆超管
		$adminUserId = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();
		if ($adminUserId) {
			\User::setLoginUserById($adminUserId);
		} else {
			throw new RuntimeException("client_id不正确，无法获取超管账号");
		}
		$formattedDateTime = (new DateTime())->sub(new DateInterval('PT10M'))->format('Y-m-d H:i:s');
		$list = AiLeadMessageService::transferAiLeadMessageList($clientId, $formattedDateTime);
		var_dump($list);
	}

}