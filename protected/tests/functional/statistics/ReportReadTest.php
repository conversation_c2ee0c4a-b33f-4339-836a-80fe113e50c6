<?php
/**
 * Created by PhpStorm.
 * User: cayley
 * Date: 2019/3/28
 * Time: 上午9:53
 */

namespace tests\funtional\statistics;

use common\library\account\Client;

class ReportReadTest extends \WebFunctionalTestCase
{

    public function testCustomDetail()
    {
//        $this->loginUser('<EMAIL>');
//        $this->loginUser('<EMAIL>');
        $this->loginUser('<EMAIL>');
//        $this->loginUser('<EMAIL>');
        $params = [

//            'key' =>'**********',
            'key' =>'dd4',
            'refresh' => 1,
            'params' => [
                '{"field":"order.account_date","type":"date","value":{"start":"2020-03-17","end":"2020-03-17"}}'
            ],
        ];

        $this->callAction('detail', $params);
        $this->responseOk();
    }


    public function testReportList()
    {
        $params = [
            'type' => \common\library\statistics\Constant::REPORT_TYPE_AGGREGATE
        ];
        $this->loginUser('<EMAIL>');
        $this->callAction('reportList', $params);
        $this->responseOk();
    }


    public function testCustomReportList()
    {
        $params = [
        ];
        $this->loginUser('<EMAIL>');
        $this->callAction('CustomReportList', $params);
        $this->responseOk();
    }


    public function testReportConfig()
    {
        $params = [
            'report_key' => '**********'
        ];
        $this->loginUser('<EMAIL>');
        $this->callAction('reportConfig', $params);
        $this->responseOk();
    }


    public function testReportObjects()
    {
        $params = [
        ];
//        $this->loginUser('<EMAIL>');
        $this->loginUser('<EMAIL>');
        $this->callAction('reportObjects', $params);
        $this->responseOk();
    }

    public function testReportObjectInfo()
    {
        $params = [
//            'report_object_key' => 'quotation'
//            'report_object_key' => 'opportunity'
//            'report_object_key' => 'lead'
//            'report_object_key' => 'cash_collection'
//            'report_object_key' => 'company'
//            'report_object_key' => 'product'
            'report_object_key' => 'order'
        ];
        $this->loginUser('<EMAIL>');
//        $this->loginUser('<EMAIL>');
//        $this->loginUser('<EMAIL>');
        $this->callAction('ReportObjectInfo', $params);
        $this->responseOk();
    }


    public function testDashboardList()
    {

//        $this->loginUser('<EMAIL>');
//        $setting = new \common\library\setting\user\UserSetting(User::getLoginUser()->getClientId(), User::getLoginUser()->getUserId(), \common\library\setting\user\UserSetting::USER_LANGUAGE);
//        $setting->value = 'en';
//        $setting->save();

//        $this->loginUser('<EMAIL>');
        $this->loginUser('<EMAIL>');
        $params = [
            'type' => \common\library\statistics\Constant::DASHBOARD_TYPE_CUSTOM
        ];
        $this->callAction('dashboardList', $params);
        $this->responseOk();

    }

    public function testDashboardInfo()
    {
//        $setting = new \common\library\setting\user\UserSetting(User::getLoginUser()->getClientId(), User::getLoginUser()->getUserId(), \common\library\setting\user\UserSetting::USER_LANGUAGE);
//        $setting->value = 'en';
//        $setting->save();

//        $this->loginAsRuby();
        $this->loginUser('<EMAIL>');
        $params = [
//            'key' =>  'customer',//\common\library\statistics\data\DashboardData::DASHBOARD_CLIENT_KEY
            'key' => '1100741018'
        ];
        $this->callAction('dashboardInfo', $params);
        $this->responseOk();
    }


    public function testDetail()
    {

//        $this->loginUser('<EMAIL>');
//        $this->loginUser('<EMAIL>');

        $params = [
//            'key' => 'kh1',
//            'key' => 'kh2',
//            'key' => 'kh3',
//            'key' => 'kh4',
//            'key' => 'kh5',
//            'key' => 'kh6',
//            'key' => 'kh7',
//            'key' => 'kh8',
//            'key' => 'kh9',
//            'key' => 'sj1',
//            'key' => 'sj2',
//            'key' => 'sj3',
//            'key' => 'sj4',
//            'key' => 'sj5',
//            'key' => 'sj6',
//            'key' => 'sj7',
//            'key' => 'sj8',
//            'key' => 'sj9',
//            'key' => 'sj10',
//            'key' => 'sj11',
//            'key' => 'sj12',
//            'key' => 'sj13',
//            'key' => 'dd1',
//            'key' => 'dd2',
//            'key' => 'dd3',
//            'key' => 'dd4',
//            'key' => 'yx1',
//            'key' => 'yx2',
//            'key' => 'cp1',
//            'key' => 'cp2',
//            'key' => 'cp3',
//            'key' => 'cp4',
//            'key' => 'cp5',
//            'key' => 'cp6',
//            'key' => 'cp7',
//            'key' => 'cp8',
//            'key' => 'cp9',
//            'key' => 'cp10',
//            'key' => 'yj1',
//            'key' => 'yj2',
//            'key' => 'yj3',
//            'key' => 'yj4',
//            'key' => 'yj5',
//            'key' => 'yj6',
//            'key' => 'yj7',
//            'key' => 'yj8',
//            'key' => 'yj9',
//            'key' => 'cpts1',
//            'key' => 'cpts2',
//            'key' => 'cpts3',
//            'key' => 'yjts1',
//            'key' => 'yjts2',
//            'key' => 'yjts3',
//            'key' => 'yjts4',
//            'key' => 'yjts5',
//            'key' => 'yjts6',
//            'key' => 'khts1',
//            'key' => 'yjts2',
//            'key' => 'yjts2',
//            'key' => 'sjts1',
            'key'    => 'sjts2',
//            'key' => 'sjts3',
//            'key' => 'xs1',
//            'key' => 'xs4',
//            'key' => 'xs6',
//            'key' => 'yjwcu',
//            'key' => '1107882974',
//            'key' => 'task1',
//            'params' => [
//                json_encode([
//                    "field" => "common.task_rule_id",
//                    'multiple' => 1,
//                    "value" =>  [
//                        1119223791,
//                        1119246314
//                    ]
//                ])
//            ],
            'refresh' => 1,
        ];

	    //$this->loginUser('11855533');
//	    $this->callAction('detail', 'key=xs1&refresh=1');
//	    $this->callAction('detail', 'key=report1&refresh=1');
//	    $this->callAction('detail', 'key=xswork&refresh=1&params[]=%7B%22field%22:%22common.visible%22,%22type%22:%22select_visible_user_id%22,%22value%22:[]%7D&params[]=%7B%22field%22:%22common.date%22,%22type%22:%22date%22,%22value%22:%7B%22start%22:%222022-01-01%22,%22end%22:%222022-12-31%22%7D%7D&refresh=1&unique_key=&show_detail=0');
//	    $this->callAction('detail', 'key=xs5&refresh=1');
//	    $this->callAction('detail', 'key=xswork&refresh=1');
//	    $this->callAction('detail', 'key=report1&params[]=%7B%22field%22:%22common.visible%22,%22type%22:%22select_visible_user_id%22,%22value%22:[%221%22,%2210%22,%2211850882%22,%2211851127%22,%2211851806%22,%2211852396%22,%2211852554%22,%2211852571%22,%2211855392%22,%2211857375%22,%2211857742%22,%2211857799%22,%2211857805%22,%2211857806%22,%2211858314%22,%2211858373%22,%2211858380%22,%2211858381%22,%22********%22,%2211858416%22,%2211858450%22,%2211858460%22,%2211858462%22,%2211858463%22,%2211858466%22,%2211858469%22,%2211858519%22,%2211858528%22,%2211858549%22,%2211858572%22,%2211858629%22,%2211858630%22,%2211858643%22,%2211858687%22,%2211858689%22,%2211858691%22,%2211858693%22,%2211858694%22,%2211858695%22,%2211858696%22,%2211858697%22,%2211858698%22,%2211858701%22,%2211858704%22,%2211858710%22,%2211858730%22,%2211858792%22,%2211858793%22,%2211858794%22,%2211858795%22,%2211858796%22,%2211858819%22,%2211858821%22,%2211859007%22,%2211859068%22,%2211859096%22,%2211859100%22,%2211859140%22,%2211859141%22,%2211863701%22,%2211863707%22,%2211863759%22,%2211863786%22,%2211863794%22,%2211863822%22,%2214%22,%223%22,%2230393%22,%2230421%22,%22377%22,%2246900%22,%22482%22,%22483%22,%22484%22,%2251026%22,%2251060%22,%2251068%22,%2251106%22,%2251305%22,%2271475%22,%22734%22,%22765%22]%7D&params[]=%7B%22field%22:%22common.date%22,%22type%22:%22date%22,%22value%22:%7B%22start%22:%222021-09-01%22,%22end%22:%222021-09-16%22%7D%7D&refresh=0&unique_key=&show_detail=0');

        //$this->loginAsQiao();
        $this->loginAsXpro();
//        $this->callAction('detail','key=yjxqu&params%5B0%5D%5Bfield%5D=common.account_date&params%5B0%5D%5Btype%5D=date&params%5B0%5D%5Bvalue%5D%5Bstart%5D=2024-12-23&params%5B0%5D%5Bvalue%5D%5Bend%5D=2024-12-29&params%5B1%5D%5Bfield%5D=common.rule_id&params%5B1%5D%5Btype%5D=performance_rule_id&params%5B1%5D%5Bvalue%5D=**********&refresh=0&unique_key=&show_detail=0&show_data=1');
        $this->callAction('detail', 'key=xs5&params[0][field]=common.visible&params[0][type]=select_visible_user_id&params[0][value]=&params[1][field]=common.date&params[1][type]=date&params[1][value][start]=2025-03-01&params[1][value][end]=2025-04-30&user_id=&refresh=0&unique_key=&show_detail=0&show_data=1');
        $this->responseOk();
    }

    public function testYjDetail()
    {
        $this->loginAsPro();
        $params = [
            'key' => 'yjwcu',
            'params' => [
            json_encode([
            "field" => "common.rule_id",
            "value" =>  [
                **********,
            ]
        ])
    ],
            'refresh' => 1,
        ];

        $this->callAction('detail', $params);
        $this->responseOk();


    }



    public function testProduct()
    {
        $params = [
            'product_id' =>1
        ];
        $this->callAction('product', $params);
        $this->responseOk();
    }


    public function testReportYj6()
    {

        $this->callAction('detail', [
            'key' => 'yj6',
            'refresh' => 1
        ]);
        $this->responseOk();
    }

    public function testReportYJTS1()
    {
//        $this->loginUser('<EMAIL>');

        $this->loginAsJocelyn();

        $this->callAction('detail', [
            'key' => 'yjts1',
            'params' => [
                '{"field":"user_performance.account_date","type":"date","value":{"start":"","end":""}}',
            ]
        ]);
        $this->responseOk();
    }

    public function testReportYJ1()
    {
//        $this->loginUser('<EMAIL>');

        $this->loginAsJocelyn();

        $this->callAction('detail', [
            'key' => 'yj1',
            'refresh' => 1,
        ]);
        $this->responseOk();
    }

    public function testReportYJTS2()
    {
//        $this->loginUser('<EMAIL>');
//        $this->loginAsLora();
        $this->loginAsJocelyn();
        $this->loginUser('<EMAIL>');

        $this->loginAsRuby();
        $this->callAction('detail', [
                "key"    => "yjts2",
                "params" => [
                    '{"field":"department_performance.account_date","type":"date","value":{"start":"2019-09-01","end":"2019-09-30"}}',
                    '{"field":"common.visible","type":"select_visible_department_id","value":"1"}',
//                    '{"field":"common.visible","type":"select_visible_department_id","value":"8390"}',
                ]
            ]
        );
        $this->responseOk();
    }

    public function testReportYJTS3()
    {
        $this->loginAsJocelyn();

        $this->callAction('detail', [
            'key' => 'yjts3',
            'params' => [
                '{"field":"user_performance.account_date","type":"date","value":{"start":"2019-01-01","end":"2019-02-28"}}',
            ]
        ]);
        $this->responseOk();
    }

    public function testReportYJTS4()
    {
        $this->loginUser('<EMAIL>');
        $this->loginAsJocelyn();
        $this->callAction('detail', [
            'key' => 'yjts4',
            'params' => [
                '{"field":"common.visible","type":"select_visible_department_id","value":"83912"}',
                '{"field":"department_performance.account_date","type":"date","value":{"start":"2019-01-01","end":"2019-06-25"}}',
            ]
        ]);
        $this->responseOk();
    }

    public function testReportYJTS5()
    {
        $this->loginAsJocelyn();
        $this->loginUser('<EMAIL>');
        $this->callAction('detail', [
            'key' => 'yjts5',
            'params' => [
                '{"field":"user_performance.account_date","type":"date","value":{"start":"2019-01-01","end":"2019-08-30"}}',
                '{"field":"common.page","type":"int","value":{"page":1,"page_size":50}}',
//                '{"field":"user_performance.account_date","type":"date","value":{"start":"2019-01-01","end":"2019-06-24"}}',
//                '{"field":"common.page","type":"int","value":{"page":"1","page_size":"2"}}'
            ]
        ]);
        $this->responseOk();
    }

    public function testReportYJTS6()
    {
        $this->loginAsJocelyn();
        $this->loginUser('<EMAIL>');
        $this->callAction('detail', [
            'key' => 'yjts6',
            'params' => [
                '{"field":"department_performance.account_date","type":"date","value":{"start":"2019-01-01","end":"2019-08-22"}}',
                '{"field":"common.visible","type":"select_visible_department_id","value":["8391","8393","8390","8392"]}',
//                '{"field":"common.page","type":"int","value":{"page":"1","page_size":"1000"}}',
            ]
        ]);
        $this->responseOk();
    }

    public function testReportXS1()
    {
        $this->loginAsWeason3();
        $this->callAction('detail', [
            'key' => 'xs1',
            'refresh' => 1,
        ]);
        $this->responseOk();
    }


    public function testReportXS2()
    {
        $this->callAction('detail', [
            'key' => 'xs2',
        ]);
        $this->responseOk();
    }

    public function testReportXS3()
    {
        $this->callAction('detail', [
            'key' => 'xs3',
//            'user_id' => [********]
        ]);
        $this->responseOk();
    }

    public function testReportXS5()
    {
        $this->callAction('detail', [
            'key' => 'xs5',
        ]);
        $this->responseOk();
    }

    public function testReportXS6()
    {
        $this->callAction('detail', [
            'key' => 'xs6',
        ]);
        $this->responseOk();
    }

    public function testReportXS7()
    {
        $this->callAction('detail', [
            'key' => 'xs7',
        ]);
        $this->responseOk();
    }


    public function testReportXS10()
    {
        $this->callAction('detail', [
            'key' => 'xs10',
        ]);
        $this->responseOk();
    }


    public function testReportXS11()
    {
        $this->loginUser('<EMAIL>');
        $this->callAction('detail', [
            'key' => 'xs11',
            'refresh' => 1,
        ]);
        $this->responseOk();
    }

    public function testReportXS12()
    {
        $this->loginAsRuby();
        $this->callAction('detail', [
            'key' => 'xs12',
            'refresh' => 1,
        ]);
        $this->responseOk();
    }

    public function testReportXS13()
    {
        $this->loginAsRuby();
        $this->callAction('detail', [
            'key' => 'xs13',
            'refresh' => 1,
        ]);
        $this->responseOk();
    }


    public function testReportSJ13()
    {
        $this->loginUser('<EMAIL>');
        $this->callAction('detail', [
            'key' => 'sj13',
        ]);
        $this->responseOk();
    }


    public function testReportSJ14()
    {
        $this->loginUser('<EMAIL>');
        $this->callAction('detail', [
            'key' => 'sj14',
        ]);
        $this->responseOk();
    }


    public function testReportCP1()
    {
        $this->loginClient(14059);
        $this->callAction('detail', [
            'key' => 'cp1',
            'refresh' => 1,
        ]);
        $this->responseOk();
    }

    public function testReportCP4()
    {
        $this->loginUser('<EMAIL>');
//        $this->loginAsKk();
//        dd(Client::getClient(static::$clientId)->getExtentAttributes([Client::EXTERNAL_KEY_GROUP_DISABLED]));
        $this->callAction('detail', [
            'key' => 'cp4',
        ]);
        $this->responseOk();
    }

    public function testReportCP9()
    {

        $this->callAction('detail', [
            'key' => 'cp9',
        ]);
        $this->responseOk();
    }

    public function testReportCP10()
    {

        $this->loginUser('<EMAIL>');
        $this->loginUser('<EMAIL>');
        $this->callAction('detail', [
            'key' => 'cp10',
            'refresh' => 1,
            'params' => [
//                '{"field":"invoice_product.product_id","type":"select_product","value":[35070427]}',
//                '{"field":"company.group_id","type":"select_company_group","value":33927654}'
            ]
        ]);
        $this->responseOk();
    }


    public function testReportCP14()
    {

        $this->loginUser('<EMAIL>');
        $this->callAction('detail', [
            'key' => 'cp14',
        ]);
        $this->responseOk();
    }




    public function testReportCP15()
    {

        $this->callAction('detail', [
            'key' => 'cp15',
        ]);
        $this->responseOk();
    }


    public function testReportCP16()
    {

        $this->callAction('detail', [
            'key' => 'cp16',
        ]);
        $this->responseOk();
    }


    public function testReportCP17()
    {

        $setting = new \common\library\setting\user\UserSetting(User::getLoginUser()->getClientId(), User::getLoginUser()->getUserId(), \common\library\setting\user\UserSetting::USER_LANGUAGE);
        $setting->value = 'zh-TW';
        $setting->save();

        $this->callAction('detail', [
            'key' => 'cp17',
        ]);
        $this->responseOk();
    }


    public function testReportCP18()
    {

        $this->callAction('detail', [
            'key' => 'cp18',
        ]);
        $this->responseOk();
    }


    public function testReportCP19()
    {

        $this->callAction('detail', [
            'key' => 'cp19',
        ]);
        $this->responseOk();
    }

    public function testReportCP20()
    {
        $this->callAction('detail', [
            'key' => 'cp20',
        ]);
        $this->responseOk();
    }

    public function testReportCP21()
    {

        $this->callAction('detail', [
            'key' => 'cp21',
        ]);
        $this->responseOk();
    }


    public function testReportCP22()
    {

        $this->callAction('detail', [
            'key' => 'cp22',
        ]);
        $this->responseOk();
    }


    public function testReportCPTS1()
    {

        $this->callAction('detail', [
            'key' => 'cpts1',
        ]);
        $this->responseOk();
    }

    public function testReportCPTS2()
    {

        $this->callAction('detail', [
            'key' => 'cpts2',
        ]);
        $this->responseOk();
    }

    public function testReportCPTS3()
    {

        $this->callAction('detail', [
            'key' => 'cpts3',
        ]);
        $this->responseOk();
    }


    public function testReportCPTS4()
    {

        $this->callAction('detail', [
            'key' => 'cpts4',
            'refresh' => 1,
        ]);
        $this->responseOk();
    }

    public function testReportCPTS5()
    {
        $this->loginUser('<EMAIL>');
        $this->callAction('detail', [
            'key' => 'cpts5',
        ]);
        $this->responseOk();
    }

    public function testReportCPTS6()
    {

        $this->callAction('detail', [
            'key' => 'cpts6',
        ]);
        $this->responseOk();
    }

    public function testReportCPCJDD()
    {
//        $this->loginAsLora();
        $this->loginUser('<EMAIL>');
        $this->loginAsXpro();

        $this->loginAsJocelyn();
        $this->callAction('detail', [
            'key' => 'cpcjdd',
            'params' => [
                '{"field":"product.product_id","value":1103127839, "type": "init"}',
//                '{"field":"order.status","comment":"订单状态","type":"3","multiple":1,"value":["31875672"],"field_type":7,"options":[],"label":""}',
//                '{"field":"company.company_id","value":1103960379}',
                '{"field":"common.page","type":"int","value":{"page":1,"page_size":10}}',
//                '{"field":"common.visible","type":"select_visible_department_id","value":["8391","8393","8390","8392"]}',
            ],
            'show_detail' => 1,
            'refresh' => 1,
        ]);
        $this->responseOk();
    }


    public function testReportCpxscjdd()
    {
        $this->loginAsRuby();
        $this->callAction('detail', [
            'key' => 'cpxscjdd',
            'params' => [
//                '{"field":"product.product_id","value":1103177401}',
//                '{"field":"company.company_id","value":38868293}',
//                '{"field":"common.page","type":"int","value":{"page":1,"page_size":10}}',
                '{"field":"common.visible","type":"select_visible_department_id","value":["8391","8393","8390","8392"]}',
            ],
            'show_detail' => 1,
            'refresh' => 1,
        ]);
        $this->responseOk();
    }



    public function testReportCpxscjbjd()
    {
        $this->loginAsRuby();
        $this->callAction('detail', [
            'key' => 'cpxscjbjd',
            'params' => [
//                '{"field":"product.product_id","value":1103177401}',
//                '{"field":"company.company_id","value":38868293}',
//                '{"field":"common.page","type":"int","value":{"page":1,"page_size":10}}',
//                '{"field":"common.visible","type":"select_visible_department_id","value":["8391","8393","8390","8392"]}',
            ],
            'show_detail' => 1,
            'refresh' => 1,
        ]);
        $this->responseOk();
    }


    public function testReportCpxscjsj()
    {
        $this->loginAsQiao();
        $this->callAction('detail', [
            'key' => 'cpxscjsj',
            'params' => [
//                '{"field":"product.product_id","value":1103177401}',
//                '{"field":"company.company_id","value":38868293}',
//                '{"field":"common.page","type":"int","value":{"page":1,"page_size":10}}',
//                '{"field":"common.visible","type":"select_visible_department_id","value":["8391","8393","8390","8392"]}',
            ],
            'show_detail' => 1,
            'refresh' => 1,
        ]);
        $this->responseOk();
    }

    public function testReportCPCJBJD()
    {
        $this->loginAsRuby();
        $this->loginAsPro();
        $this->callAction('detail', [
            'key' => 'cpcjbjd',
            'params' => [
                '{"field":"product.product_id","value":1103671497}',
//                '{"field":"company.company_id","value":38868293}',
                '{"field":"common.page","type":"int","value":{"page":1,"page_size":10}}',
//                '{"field":"common.visible","type":"select_visible_department_id","value":["8391","8393","8390","8392"]}',
            ],
            'show_detail' => 1,
            'refresh' => 1,
        ]);
        $this->responseOk();
    }

    public function testReportCPCJSJ()
    {
        $this->loginAsQiao();
        $this->loginAsPro();
        $this->callAction('detail', [
            'key' => 'cpcjsj',
            'params' => [
                '{"field":"product.product_id","value":1103671497}',
//                '{"field":"company.company_id","value":38868293}',
                '{"field":"opportunity.create_time","type":"date","value":{}}',
                '{"field":"common.page","type":"int","value":{"page":1,"page_size":10}}',
//                '{"field":"common.visible","type":"select_visible_department_id","value":["8391","8393","8390","8392"]}',
            ],
            'show_detail' => 1,
            'refresh' => 1,
        ]);
        $this->responseOk();
    }

    public function testReportKH1()
    {

        $this->callAction('detail', [
            'key' => 'kh1',
        ]);
        $this->responseOk();
    }


    public function testReportKH9()
    {

        $this->callAction('detail', [
            'key' => 'kh9',
        ]);
        $this->responseOk();
    }


    public function testReportKHTS1()
    {

        $this->loginAsJocelyn();
        $this->callAction('detail', [
            'key' => 'khts1',
            'params' => [
//                '{"field":"company.select_date","type":"date","value":{"start":"2018-06-29","end":"2019-05-02"}}'
            ]
        ]);
        $this->responseOk();
    }


    public function testReportDD1()
    {

        $this->callAction('detail', [
            'key' => 'dd1',
        ]);
        $this->responseOk();
    }


    public function testReportDD2()
    {

        $this->callAction('detail', [
            'key' => 'dd2',
        ]);
        $this->responseOk();
    }


    public function testReportDD3()
    {

        $this->callAction('detail', [
            'key' => 'dd3',
        ]);
        $this->responseOk();
    }



    public function testReportDD4()
    {
        $this->loginAsJocelyn();
        $this->callAction('detail', [
            'key' => 'dd4',
        ]);
        $this->responseOk();
    }


    public function testReportSJ1()
    {

        $this->callAction('detail', [
            'key' => 'sj1',
        ]);
        $this->responseOk();
    }




    public function testReportSJ2()
    {
        $this->loginUser('<EMAIL>');
        $this->callAction('detail', [
            'key' => 'sj2',
            'refresh' => 1
        ]);
        $this->responseOk();
    }


    public function testReportSJ15()
    {
        $this->loginUser('<EMAIL>');

        $this->callAction('detail', [
            'key' => 'sj15',
            'refresh' => 1
        ]);
        $this->responseOk();
    }

    public function testReportSJ5()
    {

        $this->callAction('detail', [
            'key' => 'sj5',
        ]);
        $this->responseOk();
    }

    public function testReportSJTS1()
    {

        $this->callAction('detail', [
            'key' => 'sjts1',
            'refresh' => 1
        ]);

        $this->responseOk();
    }

    public function testReportSJTS2()
    {

        $this->callAction('detail', [
            'key' => 'sjts2',
            'refresh' => 1
        ]);
        $this->responseOk();
    }

    public function testReportSJTS3()
    {


        $setting = new \common\library\setting\user\UserSetting(User::getLoginUser()->getClientId(), User::getLoginUser()->getUserId(), \common\library\setting\user\UserSetting::USER_LANGUAGE);
        $setting->value = 'en';
        $setting->save();
        $this->callAction('detail', [
            'key' => 'sjts3',
            'refresh' => 1
        ]);
        $this->responseOk();
    }


    public function testReportYX1()
    {

        $this->loginAsJocelyn();
        $this->callAction('detail', [
            'key' => 'yx1',
            'refresh' => 1
        ]);
        $this->responseOk();
    }

    public function testReportYX2()
    {


        $this->callAction('detail', [
            'key' => 'yx2',
            'refresh' => 1,
//            'user_id' => [765]
        ]);
        $this->responseOk();
    }


    public function testReportXSWork()
    {

	    $this->loginUser('46900');

        $this->callAction('detail', [
            'key' => 'xswork',
//            'key' => 'report1',
            'refresh' => 1,
'params' => [
	'{"field":"common.date","type":"date","value":{"start":"2021-01-01","end":"2021-12-31"}}',

]
//            'user_id' => [765]
        ]);
        $this->responseOk();
    }


    public function testSystem()
    {
        ini_set("memory_limit", "1500M");
        $reportKeys = [];
        $configPath = \Yii::getPathOfAlias("application.library.statistics.config.chart");
        foreach (scandir($configPath) as $file)
        {
            if( $file == '.' || $file =='..' || strpos($file,'yaml') === false )
                continue;

            $arr = explode('.', $file);

            if( empty($arr) )
                continue;

            list($fileName, $fileExt) = $arr;
            if( $fileExt == 'yaml')
                $reportKeys[] = $fileName;
        }

        foreach ( $reportKeys as $reportKey )
        {
            $beginTime = microtime(true);
            $this->echo($reportKey);
            $this->callAction('detail', [
                'key' => $reportKey,
                'refresh' => 1,
            ]);

            $endTime = microtime(true);
            $total = rand(($endTime - $beginTime) * 1000,2);

            \LogUtil::info("[{$reportKey}]:一共耗时".$total.'ms');

            $this->responseOk();
        }
    }

    public function testReportOro()
    {
        $this->loginUser(11859131);
        $this->callAction('detail', [
            'key' => 'cpxscjdd',
            'refresh' => 1,
            'params' => [
                '{"field":"common.visible","value":["11859131"]}',
                '{"field":"product.product_id","value":""}',
                '{"field":"common.page","value":{"page":1,"pages_size":20}}',
                '{"field":"common.page","value":{"page":1,"pages_size":20}}',
                '{"field":"order.company_id","value":0}',
                '{"field":"order.create_time","type":"date","value":{"start":"2022-01-01","end":"2022-01-12"}}',
            ],
            'show_detail' =>1

        ]);
        $this->responseOk();
    }

    public function testReportDd134()
    {
        $this->loginByEmail('<EMAIL>');
        $this->callAction('detail', [
            'key' => 'dd1',
            'refresh' => 1,
            'params' => [
                '{"field":"common.visible","type":"select_visible_user_id","value":[]}',
                '{"field":"order.user_type","type":"user_type","value":["1","2"]}',
                '{"field":"order.create_time","type":"date","value":{"start":"2022-01-01","end":"2022-01-20"}}',
            ],
            'show_detail' =>1
        ]);
        $this->responseOk();
    }

    public function testDashboardList1()
    {
        $this->loginByEmail('<EMAIL>');
        $params = [
            'type' => \common\library\statistics\Constant::DASHBOARD_TYPE_SCENE
        ];
        $this->callAction('dashboardList', $params);
        $this->responseOk();
    }

    public function testDetail3()
    {
        $this->loginByEmail('<EMAIL>');
        $rsp = $this->callAction('detail',
        'key=**********&params%5B0%5D%5Bfield%5D=common.visible&params%5B0%5D%5Btype%5D=select_visible_user_id&params%5B0%5D%5Bvalue%5D=&params%5B1%5D%5Bfield%5D=order.account_date&params%5B1%5D%5Btype%5D=date&params%5B1%5D%5Bvalue%5D%5Bstart%5D=2025-04-01&params%5B1%5D%5Bvalue%5D%5Bend%5D=2025-04-30&refresh=1&unique_key=&show_detail=0&show_data=1',
        'reportRead'
        );
        dd($rsp);
    }

}

