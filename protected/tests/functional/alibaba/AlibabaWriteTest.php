<?php
/**
 * Created by PhpStorm.
 * User: cayley
 * Date: 2020/11/6
 * Time: 6:27 PM
 */
namespace tests\functional\alibaba;

use common\library\alibaba\Constant;

class AlibabaWriteTest extends \WebFunctionalTestCase
{


    //https://crm-iteration-3-4-1080643.story.dev.xiaoman.cn/api/alibabaWrite/userAuthOauth?code=0_awfdPbbeJwLxjhoyUDdRGJW767653&state=UUpodGNjdDFPbVg0b2RzSjRrcWF1d1hVS09QbE0rblQ

    public function testUserAuthOauth()
    {
        $s = '351383|249521383';
        $state = \SecurityUtil::base64Encrypt($s, Constant::TRIPLE_DES_KEY);
        // UWltcHZicFRmcDZoS0xtZTl3aUJ5YUVyQmV1WHkwUE4
        $s = \SecurityUtil::base64Dncrypt($state, Constant::TRIPLE_DES_KEY);
        $this->callAction('UserAuthOauth', [
            'code'  => '0_awfdPbbeJwLxjhoyUDdRGJW767653',
            'state' => $state,//可以用这个计算登录态
        ], 'AlibabaWrite');
        $this->responseOk();
    }

    public function testBatchSyncAlibabaOrderSwitch()
    {
        \User::setLoginUserById(11858713);
        $this->callAction('BatchSyncAlibabaOrderSwitch', [
            'order_ids' => [3554693419],
            'type' => [4,1,2,3],
        ], 'AlibabaWrite');
        $this->responseOk();
    }

    public function testSyncAlibabaCustomer()
    {
        $this->loginClient(14367);
        $params = [
            'store_id'  => '*********',
            'task_type' => 5,
        ];
        $this->callAction('SyncAlibabaCustomer', $params);
        $this->responseOk();
    }


    public function testSaveIntention()
    {
        $this->loginUser('<EMAIL>');
        $params = [
//            'order_id' =>   '**********',
//            'order_id' =>   '**********',
//            'order_id' =>   '**********',
//            'seller_account_id' =>   '*********',
        'mail_id' => **********,
//        'order' => 0,
            'seller_account_id' =>   '*********',

//            'order_id' =>   '**********',
//            'seller_account_id' =>   '*********',
        ];
        $this->callAction('saveIntention', $params);
        $this->responseOk();
    }


    public function testSetStoreAlias()
    {
//        $this->loginUser('<EMAIL>');
        $this->loginUser('<EMAIL>');
        $params = [
            'store_id' =>   '*********',
//            'store_id' =>   '*********',
             'store_alias' => 'testcompany22'
        ];
        $this->callAction('setStoreAlias', $params);
        $this->responseOk();
    }

    public function testProduceDefaultAlias()
    {
        $this->loginUser('<EMAIL>');
        $clientId = User::getLoginUser()->getClientId();
        $store = new \common\library\alibaba\store\AlibabaStore($clientId,********* );
        $alias =  $store->produceDefaultAlias();

        $newStore = new \common\library\alibaba\store\AlibabaStore($clientId);
        $newStore->store_name = $store->store_name;
        $newStore->store_id =1;
        $alias =  $newStore->produceDefaultAlias();
        dd($alias);
    }

    public function testSetOrderSync()
    {
        $this->loginUser('<EMAIL>');
        $params = [
            'store_id' => '*********',
            'sync_order_flag' => 1,
            'sync_order_extent' => '1',
//            'sync_date_after' => null,
            'is_build_customer' => '1',
            'order_status_map' => json_encode([
                [
                    'ali_order_status' => 'undeliver',
                    'sale_order_status' => "1100013562"
                ],
                [
                    'ali_order_status' => 'delivering',
                    'sale_order_status' => "1100013564"
                ],
            ]),

        ];
        $this->callAction('setOrderSync', $params);
        $this->responseOk();
    }

    public function testSetProductSync()
    {
        $this->loginUser('<EMAIL>');
        $params = [
            'store_id' => '*********',
            'sync_product_flag' => '1',
            'sync_product_extent' => '1',
            'is_override' => '1',
        ];

        $this->callAction('setProductSync', $params);
        $this->responseOk();
    }


    public function testSaveStoreMemberSettings()
    {
        $data = [
            [
                'user_id' => ********,
                'seller_account_id' => *********
            ],
            [
                'user_id' => ********,
                'seller_account_id' => *********
            ],
            [
                'user_id' => ********,
                'seller_account_id' => *********
            ],
            [
                'user_id' => *********,
                'seller_account_id' => *********
            ],
            [
//                'user_id' => ********,
                'seller_account_id' => *********
            ],
        ];

        $this->loginUser('<EMAIL>');
        $params = [
            'store_id' => '*********',
            'data' => json_encode($data)
        ];

        $this->callAction('saveStoreMemberSettings', $params);
        $this->responseOk();
    }

    public function testSyncAlibabaSingleCustomer()
    {
        $this->loginUser(********);
        $params = [
            'store_id' => *********,
            'alibaba_company_id' => **********,
            'company_id' => **********,
            'update_type' => 1,
            'update_owner_flag' => 1,
            'update_status_flag' => 0,
            'move_public_flag' => 1
        ];

        $this->callAction('syncAlibabaSingleCustomer', $params);
        $this->responseOk();

        //var_dump(SecurityUtil::tripleDESEncrypt('http://test.k8.xiaoman.cn/api/test/index','OKKI_Img123'));

    }


    public function testLogisticsRecommend()
    {
        $this->loginUser('<EMAIL>');
        $params = [
            'order_id' =>   '**********',
            'origin_zip_code' => '12121',
            'destination_zip_code' => '32323',
            'package_list' => json_encode([
                [
                    'quantity' =>1,
                    'length' =>1,
                    'width' =>2,
                    'weight' => 3,
                    'height' =>4,
                ],
                [
                    'quantity' =>1,
                    'length' =>1,
                    'width' =>2,
                    'weight' => 3,
                    'height' =>4,
                ]

            ]),
            'package_category' => ['liquid','powder','battery'],
        ];
        $this->callAction('LogisticsRecommend', $params);
        $this->responseOk();
    }

    public function testSetCustomerSync()
    {
        $this->loginUser('<EMAIL>');
        $params = [
            'store_id' =>   '*********',
            'sync_customer_flag' =>1
        ];
        $this->callAction('setCustomerSync', $params);
        $this->responseOk();
    }

    public function testSetCustomerSyncOwnerChange()
    {
        $this->loginUser('<EMAIL>');
        $params = [
            'store_id' =>   '*********',
            'sync_owner_change' =>1
        ];
        $this->callAction('setCustomerSyncOwnerChange', $params);
        $this->responseOk();
    }

    public function testSetCustomerSyncRename()
    {
        $this->loginAsWeason3();
        $params = [
            'store_id' => '*********',
            'company_rename_flag' => 1
        ];
        $this->callAction('SetCustomerSyncRename', $params);
        $this->responseOk();
    }

    public function testSetAssistantOpenFlag()
    {
        $this->loginUser('<EMAIL>');
        $params = [
            'seller_account_id' =>   '*********',
            'flag' =>1
        ];
        $this->callAction('SetAssistantOpenFlag', $params);
        $this->responseOk();
    }

    public function testSetVisitorMarketingSetting()
    {
        $this->loginUser('<EMAIL>');
        $params = [
            'store_id' => '*********',
            'visitor_marketing_flag' => 1,
            'data' => json_encode([
                'open_period' => [
                    'type' => 2,
                    'weekday' =>[
                        [
                            'begin_time' => '08:00',
                            'end_time' => '12:00',
                        ],
                        [
                            'begin_time' => '14:00',
                            'end_time' => '18:00',
                        ],
                    ],
                    'weekend' =>[
                        'open_flag' => 1,
                        'time_interval' => [
                            [
                                'begin_time' => '08:00',
                                'end_time' => '12:00',
                            ],
                            [
                                'begin_time' => '14:00',
                                'end_time' => '18:00',
                            ],
                        ]
                    ],

                ],
                'buyer_group_info' => [
                    "area_list" => [
                        "CN",
                        "USA",
                    ],
                    "include_registered_flag" => 1
                ],
                'welcome_message' => [
                    'homepage' => [
                        'open_flag' => 1,
                        'data' => [
                            [
                                'speechcraft' => "Hi! We are professional supplier for many years",
                                'system_flag' => 0

                            ],
                            [
                                'speechcraft' => "Welcome to our store. Click \"CHAT\"",
                                'system_flag' => 0

                            ],
                        ],
                    ],
                    'product_detail' => [
                        'open_flag' => 1,
                        'data' => [
                            [
                                'speechcraft' => "We have chargers with the functions you need- OCPP charger.",
                                'system_flag' => 0

                            ],
                            [
                                'speechcraft' => "Dear xxx,\n\nThank you for your reply! \n\nBelow are the purchas",
                                'system_flag' => 0

                            ],
                            [
                                'speechcraft' => "Hi! We are professional supplier for many years",
                                'system_flag' => 0

                            ],
                        ],
                    ],
                ]
            ]),

        ];
        $this->callAction('SetVisitorMarketingSetting', $params);
        $this->responseOk();
    }

    public function testCustomerSyncMultiStoreFieldTactic()
    {
        $this->loginUser(*********);
        $params = [
            'tactic' =>   'crm',
        ];
        $this->callAction('CustomerSyncMultiStoreFieldTactic', $params);
        $this->responseOk();
    }

    public function testCustomerSyncMultiStoreFieldSetting()
    {
        $this->loginUser(*********);
        $params = [
            'setting' =>   '{"company.name":1,"company.homepage":1,"company.fax":1,"company.address":0,"customer.name":0,"customer.email":0,"customer.tel_list":0,"customer.contact":0,"customer.post":0,"customer.gender":0,"customer.image_list":0,"customer.remark":0,"company.trail_status":0,"company.intention_level":0,"company.origin_list":0,"company.annual_procurement":0,"company.remark":0}',
        ];
        $this->callAction('CustomerSyncMultiStoreFieldSetting', $params);
        $this->responseOk();
    }

    public function testChangeVisitorMarketingSetting()
    {
        $params = [
            'company_id' => ********* ,
            'account_id' => *********,
            'operator'  => 0,
        ];

        $this->callAction('ChangeVisitorMarketingSetting',$params);
        $this->responseOk();
    }
}
