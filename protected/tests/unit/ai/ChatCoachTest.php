<?php

namespace common\tests\unit\ai;

use common\library\ai_agent\NewChatReplyAiAgent;
use FunctionalTestCase;

class ChatCoachTest extends FunctionalTestCase
{
    public function setUp(): void
    {
        parent::setUp();
        \User::setLoginUserById(249519509);
    }
    public function testParseChatCoachResponse()
    {
        $response = <<<TEXT
沟通分析
从销售人员和客户的上次对话可以看出，客户询问了关于样品的订单信息，销售人员也提供了产品的价格、图片和详细信息。然而，在销售人员后续的跟进中，并没有收到客户的任何反馈。这可能意味着交易最终没有成功。可能的原因包括客户对价格或产品特性不满意、客户找到了其他供应商、或者客户的需求发生了变化。由于已经过去了60天，客户的商业活动或需求可能已经发生了变化。

回复策略
1. 销售人员可以首先问候客户，并表达对客户近况的关心，例如：“您好，Anastasia，很长时间没有联系了，希望您一切都好。最近您的业务进展如何？有什么可以帮助您的吗？”这样可以重新建立起友好的沟通氛围。
2. 销售人员可以提供一些最新的产品信息或者促销活动，以此吸引客户的兴趣，例如：“顺便提一下，我们最近推出了一些新款的背包，或许您会感兴趣。如果您需要最新的产品目录或样品，我很乐意为您提供。”
3. 销售人员可以借助即将到来的节日或特殊时期，向客户发送节日的问候，并顺势提及产品，例如：“随着春季的到来，我们有一系列适合春季市场的新产品。不知道您是否有更新的产品需求？期待您的回复，并祝您春季愉快！”这样可以在不显得过于推销的情况下，自然地引导话题到业务上来。
TEXT;
        
        $agent = new NewChatReplyAiAgent(333383, 249519509);

        $result = $agent->tryParseChatCoachReply($response);

        $this->assertEquals('从销售人员和客户的上次对话可以看出，客户询问了关于样品的订单信息，销售人员也提供了产品的价格、图片和详细信息。然而，在销售人员后续的跟进中，并没有收到客户的任何反馈。这可能意味着交易最终没有成功。可能的原因包括客户对价格或产品特性不满意、客户找到了其他供应商、或者客户的需求发生了变化。由于已经过去了60天，客户的商业活动或需求可能已经发生了变化。', $result['沟通分析']);
        $this->assertIsArray($result['回复策略']);
        $this->assertCount(3, $result['回复策略']);
        $this->assertEquals('销售人员可以首先问候客户，并表达对客户近况的关心，例如：“您好，Anastasia，很长时间没有联系了，希望您一切都好。最近您的业务进展如何？有什么可以帮助您的吗？”这样可以重新建立起友好的沟通氛围。', $result['回复策略'][0]);
        $this->assertEquals('销售人员可以提供一些最新的产品信息或者促销活动，以此吸引客户的兴趣，例如：“顺便提一下，我们最近推出了一些新款的背包，或许您会感兴趣。如果您需要最新的产品目录或样品，我很乐意为您提供。”', $result['回复策略'][1]);
        $this->assertEquals('销售人员可以借助即将到来的节日或特殊时期，向客户发送节日的问候，并顺势提及产品，例如：“随着春季的到来，我们有一系列适合春季市场的新产品。不知道您是否有更新的产品需求？期待您的回复，并祝您春季愉快！”这样可以在不显得过于推销的情况下，自然地引导话题到业务上来。', $result['回复策略'][2]);
    }

    /**
     * 测试多出一些文字的情况
     * @return void
     */
    public function testParseChatCoachResponse_Additional()
    {
        $response = <<<TEXT
沟通分析
从对话可以看出，这位客户在之前的对话中没有回应，可能是因为以下几个原因：
1. 语言障碍：销售人员在之前的对话中提到只会说英语，而客户使用的是意大利语，这可能导致了沟通的困难。
2. 付费问题：客户在对话中多次提到免费回答次数已用完，并推荐销售人员购买付费套餐。这可能意味着客户对于继续使用服务有犹豫或者不满意。
3. 客户推荐：客户在对话中提到了邀请销售人员使用他的链接以获得额外的回答次数。这可能是客户试图帮助销售人员解决付费问题的方式。

回复策略
销售人员可以采取以下策略来重新吸引客户的参与：
1. 解决语言障碍：销售人员可以尝试使用在线翻译工具或者寻找会意大利语的同事来帮助与客户进行沟通。这样可以消除语言障碍，增加客户的参与度。
2. 解决付费问题：销售人员可以向客户解释付费套餐的优势和价值，并提供一些优惠或者折扣来吸引客户购买。同时，销售人员也可以询问客户对于服务的不满意之处，并提供解决方案来改善客户的体验。
3. 感谢客户推荐：销售人员可以向客户表达感谢，并说明已经使用了他的链接。销售人员可以提供一些额外的回答次数作为回报，并邀请客户继续参与对话。这样可以增加客户的忠诚度和参与度。
TEXT;
        
        $agent = new NewChatReplyAiAgent(333383, 249519509);
        $result = $agent->tryParseChatCoachReply($response);
        $this->assertEquals("从对话可以看出，这位客户在之前的对话中没有回应，可能是因为以下几个原因：
1. 语言障碍：销售人员在之前的对话中提到只会说英语，而客户使用的是意大利语，这可能导致了沟通的困难。
2. 付费问题：客户在对话中多次提到免费回答次数已用完，并推荐销售人员购买付费套餐。这可能意味着客户对于继续使用服务有犹豫或者不满意。
3. 客户推荐：客户在对话中提到了邀请销售人员使用他的链接以获得额外的回答次数。这可能是客户试图帮助销售人员解决付费问题的方式。", $result['沟通分析']);
        $this->assertIsArray($result['回复策略']);
        $this->assertCount(3, $result['回复策略']);
        $this->assertEquals('解决语言障碍：销售人员可以尝试使用在线翻译工具或者寻找会意大利语的同事来帮助与客户进行沟通。这样可以消除语言障碍，增加客户的参与度。', $result['回复策略'][0]);
        $this->assertEquals('解决付费问题：销售人员可以向客户解释付费套餐的优势和价值，并提供一些优惠或者折扣来吸引客户购买。同时，销售人员也可以询问客户对于服务的不满意之处，并提供解决方案来改善客户的体验。', $result['回复策略'][1]);
        $this->assertEquals('感谢客户推荐：销售人员可以向客户表达感谢，并说明已经使用了他的链接。销售人员可以提供一些额外的回答次数作为回报，并邀请客户继续参与对话。这样可以增加客户的忠诚度和参与度。', $result['回复策略'][2]);
    }

    /**
     * 测试gpt返回内容中，前后都有多余文字的情况
     * @return void
     */
    public function testParseChatCoachResponse_Additional2()
    {
        $response = <<<TEXT
沟通分析
从对话可以看出，这位客户在之前的对话中没有回应，可能有以下几个原因：
1. 客户可能对销售人员的提问或建议不感兴趣，因此选择不回应。
2. 客户可能对销售人员的提问或建议不满意，因此选择不回应。
3. 客户可能对销售人员的语言能力不满意，因此选择不回应。

回复策略
销售人员可以采取以下策略来重新吸引客户的注意：
1. 重新引起客户的兴趣：销售人员可以尝试提供更具吸引力的信息或建议，以重新引起客户的兴趣。例如，提供独特的旅行目的地、特别的旅行体验或有趣的旅行故事，以吸引客户的关注。
2. 解决客户的不满意：销售人员可以主动询问客户对之前提供的信息或建议是否满意，并提供更好的解决方案。例如，如果客户对之前提供的旅行目的地不满意，销售人员可以提供其他更适合客户需求的目的地。
3. 提供多语言支持：销售人员可以向客户保证提供多语言支持，以解决客户对语言能力的不满意。销售人员可以表示自己可以用英语进行沟通，并提供帮助和解答客户的问题。

销售人员可以根据具体情况选择适合的回复策略，以重新吸引客户的关注和参与。
TEXT;
        
        $agent = new NewChatReplyAiAgent(333383, 249519509);
        $result = $agent->tryParseChatCoachReply($response);

        $this->assertEquals(
            "从对话可以看出，这位客户在之前的对话中没有回应，可能有以下几个原因：
1. 客户可能对销售人员的提问或建议不感兴趣，因此选择不回应。
2. 客户可能对销售人员的提问或建议不满意，因此选择不回应。
3. 客户可能对销售人员的语言能力不满意，因此选择不回应。",
            $result['沟通分析']);
        $this->assertIsArray($result['回复策略']);
        $this->assertCount(3, $result['回复策略']);
        $this->assertEquals('重新引起客户的兴趣：销售人员可以尝试提供更具吸引力的信息或建议，以重新引起客户的兴趣。例如，提供独特的旅行目的地、特别的旅行体验或有趣的旅行故事，以吸引客户的关注。', $result['回复策略'][0]);
        $this->assertEquals('解决客户的不满意：销售人员可以主动询问客户对之前提供的信息或建议是否满意，并提供更好的解决方案。例如，如果客户对之前提供的旅行目的地不满意，销售人员可以提供其他更适合客户需求的目的地。', $result['回复策略'][1]);
        $this->assertEquals('提供多语言支持：销售人员可以向客户保证提供多语言支持，以解决客户对语言能力的不满意。销售人员可以表示自己可以用英语进行沟通，并提供帮助和解答客户的问题。', $result['回复策略'][2]);
    }

    public function testParseChatCoachResponse_Additional3()
    {
        $response = <<<TEXT
沟通分析
从对话可以看出，客户目前的主要需求集中在以下几点：
1. 客户对设计图的准确性和颜色标注（如橙色应为红色）非常关注，说明客户对产品的细节要求较高。
2. 客户已经明确表示需要重新检查报价和运输时间，表明价格和交付时间是客户决策的重要因素。
3. 客户目前处于审阅阶段（\"let me review\"），但尚未给出进一步反馈，可能需要更多时间或信息来做出决定。

回复策略
1. **主动跟进，提供支持**
销售人员可以在适当的时间主动联系客户，询问其是否需要进一步的帮助或解释。例如，可以说：“您好，我想确认您是否有任何问题需要我解答，或者是否需要进一步调整设计图？我随时为您提供支持。” 这样可以表现出对客户需求的关注，同时避免过于催促。

2. **强调专业性，建立信任**
销售人员可以通过解释设计图的专业性和细节处理（如边缘留空的原因）来进一步建立信任。例如，可以补充说明：“我们在设计时特别考虑了季节性变化对地板的影响，这样可以确保长期使用的稳定性。如果您有其他特殊需求，也可以告诉我。” 通过展示专业知识，增强客户对产品和服务的信心。

3. **提供报价和交付时间的透明性**
销售人员可以主动准备好更新后的报价和交付时间，并告知客户已做好准备。例如，可以说：“您好，我已经准备好更新后的报价和交付时间，随时可以为您提供。如果您需要进一步调整或讨论，请随时联系我。” 这样可以让客户感到销售人员的高效和可靠，同时推动客户尽快进入下一步决策阶段。
TEXT;
        
        $agent = new NewChatReplyAiAgent(333383, 249519509);
        $result = $agent->tryParseChatCoachReply($response);

        $this->assertEquals(
            '从对话可以看出，客户目前的主要需求集中在以下几点：
1. 客户对设计图的准确性和颜色标注（如橙色应为红色）非常关注，说明客户对产品的细节要求较高。
2. 客户已经明确表示需要重新检查报价和运输时间，表明价格和交付时间是客户决策的重要因素。
3. 客户目前处于审阅阶段（\"let me review\"），但尚未给出进一步反馈，可能需要更多时间或信息来做出决定。',
            $result['沟通分析']);
        $this->assertIsArray($result['回复策略']);
        $this->assertCount(3, $result['回复策略']);
        $this->assertEquals('**主动跟进，提供支持** 销售人员可以在适当的时间主动联系客户，询问其是否需要进一步的帮助或解释。例如，可以说：“您好，我想确认您是否有任何问题需要我解答，或者是否需要进一步调整设计图？我随时为您提供支持。” 这样可以表现出对客户需求的关注，同时避免过于催促。', $result['回复策略'][0]);
        $this->assertEquals('**强调专业性，建立信任** 销售人员可以通过解释设计图的专业性和细节处理（如边缘留空的原因）来进一步建立信任。例如，可以补充说明：“我们在设计时特别考虑了季节性变化对地板的影响，这样可以确保长期使用的稳定性。如果您有其他特殊需求，也可以告诉我。” 通过展示专业知识，增强客户对产品和服务的信心。', $result['回复策略'][1]);
        $this->assertEquals('**提供报价和交付时间的透明性** 销售人员可以主动准备好更新后的报价和交付时间，并告知客户已做好准备。例如，可以说：“您好，我已经准备好更新后的报价和交付时间，随时可以为您提供。如果您需要进一步调整或讨论，请随时联系我。” 这样可以让客户感到销售人员的高效和可靠，同时推动客户尽快进入下一步决策阶段。', $result['回复策略'][2]);
    }

    public function testMarkdown(): void
    {
        $response = <<<TEXT
**CommunicationAnalysis**  
From the conversation, it can be seen that the overseas customer has shown interest in the factory's main products, specifically asking about spring washers. However, despite the salesperson providing detailed responses, including product availability and a request for payment and shipping details, the customer has not yet provided further feedback or confirmed the transaction. The customer's lack of immediate response could indicate hesitation, a need for more information, or time constraints. Building trust and addressing any potential concerns are key to moving the conversation forward.

**ResponseStrategy**  
1. **Reassure and Provide Additional Information:**  
   Sales personnel can follow up politely by reassuring the customer about the product's quality and availability. For example:  
   "Hello Eric, I wanted to follow up to ensure you have all the information you need about the spring washers. If you have any questions or need further details, I’m here to assist you. Let me know how I can help!"  
   This approach shows attentiveness and a willingness to support the customer without being overly pushy.

2. **Highlight Urgency and Benefits:**  
   Sales personnel can emphasize the benefits of acting promptly, such as stock availability or special pricing. For example:  
   "Hi Eric, just a quick note to let you know that the spring washers are ready in stock, and we can process your order immediately. I’d love to help you secure them at the current price. Let me know if you’d like me to proceed with the payment link."  
   This strategy creates a sense of urgency while reinforcing the salesperson’s eagerness to assist.

3. **Build Trust Through Personalization:**  
   Sales personnel can personalize the message to make the customer feel valued and build trust. For example:  
   "Hello Eric, I hope you’re doing well. I noticed you were interested in the spring washers. If there’s anything specific you’re looking for or any concerns you have, feel free to share them with me. I’d be happy to work with you to ensure everything meets your expectations."  
   This approach fosters a more personal connection and encourages the customer to share any hesitations or additional requirements.
TEXT;
        
        $agent = new NewChatReplyAiAgent(333383, 249519509);
        $result = $agent->tryParseChatCoachReply($response, ['CommunicationAnalysis', 'ResponseStrategy']);
        $this->assertEquals("From the conversation, it can be seen that the overseas customer has shown interest in the factory's main products, specifically asking about spring washers. However, despite the salesperson providing detailed responses, including product availability and a request for payment and shipping details, the customer has not yet provided further feedback or confirmed the transaction. The customer's lack of immediate response could indicate hesitation, a need for more information, or time constraints. Building trust and addressing any potential concerns are key to moving the conversation forward.", $result['CommunicationAnalysis']);
        $this->assertIsArray($result['ResponseStrategy']);
        $this->assertCount(3, $result['ResponseStrategy']);
        $this->assertEquals(<<<TEXT
**Reassure and Provide Additional Information:**      Sales personnel can follow up politely by reassuring the customer about the product's quality and availability. For example:      "Hello Eric, I wanted to follow up to ensure you have all the information you need about the spring washers. If you have any questions or need further details, I’m here to assist you. Let me know how I can help!"      This approach shows attentiveness and a willingness to support the customer without being overly pushy.
TEXT
            , $result['ResponseStrategy'][0]);
        $this->assertEquals(<<<TEXT
**Highlight Urgency and Benefits:**      Sales personnel can emphasize the benefits of acting promptly, such as stock availability or special pricing. For example:      "Hi Eric, just a quick note to let you know that the spring washers are ready in stock, and we can process your order immediately. I’d love to help you secure them at the current price. Let me know if you’d like me to proceed with the payment link."      This strategy creates a sense of urgency while reinforcing the salesperson’s eagerness to assist.
TEXT
            , $result['ResponseStrategy'][1]);
        $this->assertEquals(<<<TEXT
**Build Trust Through Personalization:**      Sales personnel can personalize the message to make the customer feel valued and build trust. For example:      "Hello Eric, I hope you’re doing well. I noticed you were interested in the spring washers. If there’s anything specific you’re looking for or any concerns you have, feel free to share them with me. I’d be happy to work with you to ensure everything meets your expectations."      This approach fosters a more personal connection and encourages the customer to share any hesitations or additional requirements.
TEXT
            , $result['ResponseStrategy'][2]);
    }

    public function testMarkdown_OnlyPart1(): void
    {
        $response = <<<TEXT
**CommunicationAnalysis**  
From the conversation, it can be seen that the overseas customer has shown interest in the factory's main products, specifically asking about spring washers. However, despite the salesperson providing detailed responses, including product availability and a request for payment and shipping details, the customer has not yet provided further feedback or confirmed the transaction. The customer's lack of immediate response could indicate hesitation, a need for more information, or time constraints. Building trust and addressing any potential concerns are key to moving the conversation forward.

**ResponseStrategy**
TEXT;
        $agent = new NewChatReplyAiAgent(333383, 249519509);
        $result = $agent->tryParseChatCoachReply($response, ['CommunicationAnalysis', 'ResponseStrategy']);
        $this->assertEquals("From the conversation, it can be seen that the overseas customer has shown interest in the factory's main products, specifically asking about spring washers. However, despite the salesperson providing detailed responses, including product availability and a request for payment and shipping details, the customer has not yet provided further feedback or confirmed the transaction. The customer's lack of immediate response could indicate hesitation, a need for more information, or time constraints. Building trust and addressing any potential concerns are key to moving the conversation forward.", $result['CommunicationAnalysis']);
        $this->assertIsArray($result['ResponseStrategy']);
        $this->assertCount(0, $result['ResponseStrategy']);
    }

    public function testMarkdown_OnlyPart2(): void
    {
        $response = <<<TEXT
**ResponseStrategy**
1. test1
2. test2
3. test3
TEXT;
        $agent = new NewChatReplyAiAgent(333383, 249519509);
        $result = $agent->tryParseChatCoachReply($response, ['CommunicationAnalysis', 'ResponseStrategy']);
        $this->assertEquals("", $result['CommunicationAnalysis']);
        $this->assertEquals(['test1', 'test2', 'test3'], $result['ResponseStrategy']);
    }

    public function testMarkdown_OnlyHeader(): void
    {
        $response = <<<TEXT
**ResponseStrategy**
TEXT;
        $agent = new NewChatReplyAiAgent(333383, 249519509);
        $result = $agent->tryParseChatCoachReply($response, ['CommunicationAnalysis', 'ResponseStrategy']);
        $this->assertEquals("", $result['CommunicationAnalysis']);
        $this->assertEquals([], $result['ResponseStrategy']);
    }

    public function testMarkdown_OnlyHeader2(): void
    {
        $response = <<<TEXT
**Response
TEXT;
        $agent = new NewChatReplyAiAgent(333383, 249519509);
        $result = $agent->tryParseChatCoachReply($response, ['CommunicationAnalysis', 'ResponseStrategy']);
        $this->assertEquals("", $result['CommunicationAnalysis']);
        $this->assertEquals([], $result['ResponseStrategy']);
    }

    public function testMarkdown_h1(): void
    {
        $response = <<<TEXT
# CommunicationAnalysis
From the last conversation between the salesperson and the customer, it can be seen that the interaction did not result in a successful transaction. The conversation primarily revolved around technical issues related to message notifications, as indicated by the customer's repeated inquiries about receiving message alerts. The salesperson responded by sharing multiple links, presumably to address the issue, but there is no indication that these efforts led to a resolution or further business engagement. The lack of follow-up or confirmation of a transaction suggests that the customer's concerns were not adequately resolved, leading to a breakdown in communication.

# ResponseStrategy
1. Sales personnel can reach out to the customer with a friendly message, inquiring about their well-being and expressing a genuine interest in resolving any past issues. They can say, "Hello [Customer's Name], I hope this message finds you well. I wanted to check in and see if there are any lingering issues we can help resolve. Your satisfaction is important to us."

2. Sales personnel can re-establish contact by acknowledging the past communication gap and offering assistance with any current business needs. They might say, "Hi [Customer's Name], it's been a while since we last connected. I apologize for any inconvenience you experienced before. Please let me know how we can assist you with your current business requirements."

3. Sales personnel can use the opportunity of a recent holiday or upcoming event to reconnect with the customer. They could write, "Dear [Customer's Name], Happy New Year! As we start 2025, I wanted to reach out and see how your business is doing. If there's anything we can do to support your goals this year, please feel free to reach out." 
TEXT;
        $agent = new NewChatReplyAiAgent(333383, 249519509);
        $result = $agent->tryParseChatCoachReply($response, ['CommunicationAnalysis', 'ResponseStrategy']);
        $this->assertEquals("From the last conversation between the salesperson and the customer, it can be seen that the interaction did not result in a successful transaction. The conversation primarily revolved around technical issues related to message notifications, as indicated by the customer's repeated inquiries about receiving message alerts. The salesperson responded by sharing multiple links, presumably to address the issue, but there is no indication that these efforts led to a resolution or further business engagement. The lack of follow-up or confirmation of a transaction suggests that the customer's concerns were not adequately resolved, leading to a breakdown in communication.", $result['CommunicationAnalysis']);
        $this->assertEquals(
            [
                "Sales personnel can reach out to the customer with a friendly message, inquiring about their well-being and expressing a genuine interest in resolving any past issues. They can say, \"Hello [Customer's Name], I hope this message finds you well. I wanted to check in and see if there are any lingering issues we can help resolve. Your satisfaction is important to us.\"",
                "Sales personnel can re-establish contact by acknowledging the past communication gap and offering assistance with any current business needs. They might say, \"Hi [Customer's Name], it's been a while since we last connected. I apologize for any inconvenience you experienced before. Please let me know how we can assist you with your current business requirements.\"",
                "Sales personnel can use the opportunity of a recent holiday or upcoming event to reconnect with the customer. They could write, \"Dear [Customer's Name], Happy New Year! As we start 2025, I wanted to reach out and see how your business is doing. If there's anything we can do to support your goals this year, please feel free to reach out.\""
            ],
            $result['ResponseStrategy']);
    }
}