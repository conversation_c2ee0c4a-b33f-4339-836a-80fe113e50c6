<?php

use common\library\custom_field\CustomFieldService;
use common\library\opportunity\OpportunityList;
use common\library\workflow\WorkflowConstant;

class OpportunityReadTest extends \WebFunctionalTestCase
{
    public function testList()
    {
//        $this->loginAsWeason3();
        $this->loginUser(********);
//        $userId = \User::getLoginUser()->getUserId();
//
//        $params = [
//            'stage_type' => [1,2],
//            'account_start_date' => date('Y-m-01') . ' 00:00:00', 'account_end_date' => date('Y-m-t') . ' 23:59:59'
//        ];
//
//        $params = ['filters' => [['field' => 'cus_tag', 'operator' => 'in', 'field_type' => 7, 'refer_type' => 9, 'value' => [**********]]]];
//
//        //        $params = 'disable_flag=0&curPage=1&pageSize=20&show_all=1&filters%5B0%5D%5Bfield_no%5D=6&filters%5B0%5D%5Bname%5D=%E5%95%86%E6%9C%BA%E6%9D%A5%E6%BA%90&filters%5B0%5D%5Bfield%5D=origin_list&filters%5B0%5D%5Bvalue%5D%5B0%5D=9&filters%5B0%5D%5Boperator%5D=in&filters%5B0%5D%5Bfield_type%5D=7&filters%5B0%5D%5Blast_select%5D=0&filters%5B0%5D%5Brefer_type%5D=9&filters%5B0%5D%5Bvalue_type%5D=&filters%5B0%5D%5Brelation_origin_field%5D=&filters%5B0%5D%5Brelation_origin_type%5D=0&filters%5B0%5D%5Brelation_field_type%5D=7&filter_type=opportunity.common.search.filter&criteria_type=1';
//
//        $params = 'disable_flag=0&curPage=1&pageSize=100&filters%5B0%5D%5Bfield%5D=stage_type&filters%5B0%5D%5Bname%5D=%E5%95%86%E6%9C%BA%E7%8A%B6%E6%80%81&filters%5B0%5D%5Bfield_type%5D=3&filters%5B0%5D%5Boperator%5D=%3D&filters%5B0%5D%5Bvalue%5D%5B0%5D=1&filters%5B0%5D%5Bvalue%5D%5B1%5D=2&filters%5B1%5D%5Bfield%5D=account_date&filters%5B1%5D%5Bname%5D=%E7%BB%93%E5%8D%95%E6%97%A5%E6%9C%9F&filters%5B1%5D%5Bfield_type%5D=4&filters%5B1%5D%5Boperator%5D=%3D&filters%5B1%5D%5Bvalue%5D%5B0%5D=2023-07-01%2000%3A00%3A00&filters%5B1%5D%5Bvalue%5D%5B1%5D=2023-07-31%2023%3A59%3A59&filter_type=opportunity.advanced.search.filter&criteria_type=1';
//
        $params = [
            'user_id'             => ["46900"],
            'main_user'           => 46900,
            'user_type'           => ["1", "2"],
            'show_all'            => 1,
            'check_company_exits' => 1,
            'sort_type'           => 'asc',
            'sort_field'          => 'name',
            'trail_active_flag'   => 1,
            'pageSize'            => -1,
            'customer_id'         => ["************"],
        ];

        $params = ['disable_flag' => 0, 'curPage' => 1,  'pageSize' => 20, 'show_all' => 1];
        
        $res = $this->callAction('list', $params, 'opportunityRead');
        $this->responseOk();
    }

    public function testListFilter()
    {
        $this->loginAsQiao();
        $userId = \User::getLoginUser()->getUserId();

        $params = [
            'show_all' => 0,
            'user_type' => [1],
            'filters' => [
                [
                    'field' => 'stage_type',
                    'name' => '商机状态',
                    'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                    'operator' => WorkflowConstant::FILTER_OPERATOR_EQUAL,
                    'value' => [1,2]
                ],
                [
                    'field' => 'account_date',
                    'name' => '结单日期',
                    'field_type' => CustomFieldService::FIELD_TYPE_DATE,
                    'operator' => WorkflowConstant::FILTER_OPERATOR_EQUAL,
                    'value' => [
                        date('Y-m-01') . ' 00:00:00',
                        date('Y-m-t') . ' 23:59:59'
                    ]
                ],
            ],
        ];
        $res = $this->callAction('list', $params, 'opportunityRead');
        $this->responseOk();
    }

    public function testInfo()
    {
        $this->loginAsWeason3();
        $params = [
            'opportunity_id' => **********
        ];

        $res = $this->callAction('info', $params, 'opportunityRead');
        $this->responseOk();
    }
}