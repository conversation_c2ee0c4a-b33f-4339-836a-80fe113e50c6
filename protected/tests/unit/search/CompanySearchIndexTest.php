<?php

namespace tests\unit\search;

use common\components\ElasticSearchActiveRecordV7;
use common\library\search\field\ExternalField;
use common\library\search\field\NestedField;
use common\library\search\metadata\CompanyMetadata;
use common\library\search\SearchIndexBuilder;

class CompanySearchIndexTest extends \FunctionalTestCase
{

    public function testSearchIndexBuilderByMetadata()
    {
        $builder = new SearchIndexBuilder(CompanyMetadata::class);
        [$analysis, $properties] = $builder->build();

        $this->echo($analysis);
        $this->echo($properties);
    }

    public function getMetadataIndex($metadata = CompanyMetadata::class)
    {
        $model = new class extends \common\components\ElasticSearchActiveRecordV7 {
            protected $analysis;
            protected $properties;
            protected $dynamicTemplates;
            public static $metadata;

            public function __construct()
            {
                parent::__construct();
                list($this->analysis, $this->properties, $this->dynamicTemplates) = (new SearchIndexBuilder(CompanyMetadata::class))->build();
            }

            public static function index()
            {
                return 'unittest_' . static::$metadata::index() .'_index';
            }

            public static function model($className = __CLASS__)
            {
                return parent::model($className);
            }

            public function mapping()
            {
                return [
                    '_source' => [
                        'enabled' => true
                    ],
                    '_routing' => [
                        'required' => true,
                    ],
                    'properties' => $this->properties,
                    'dynamic_templates' => $this->dynamicTemplates,
                ];
            }

            public function setting()
            {
                return [
                    'number_of_shards' => 1,
                    'number_of_replicas' => 0,
                    'analysis' => $this->analysis,
                ];
            }
        };

        $model::$metadata = $metadata;

        return $model;
    }

    public function testDeleteIndex()
    {
        $model = $this->getMetadataIndex();
        $index = $model->index();
        $db = ElasticSearchActiveRecordV7::model()->getDbConnection();
        try {
            $db->indices()->delete(['index' => $index]);
        } catch (\Throwable $exception) {

        }
    }

    /**
     * @test
     */
    public function it_can_create_an_index_by_model()
    {
        $error = false;
        $this->testDeleteIndex();
        $model = $this->getMetadataIndex();
        try {
            $res = $model->createIndex();
        } catch (\Throwable $exception) {
            $this->echo($exception->getMessage());
            $error = true;
        }
        $this->echo($model->setting(), true, 'setting' );
        $this->echo($model->mapping(), true, 'mapping');
        $this->assertFalse($error);
    }


    /**
     * @test
     */
    public function it_can_update_an_index_setting_and_mapping()
    {
        $model = $this->getMetadataIndex();
        $res = $model->updateIndexSettings();
        $res = $model->updateMapping();
        $this->assertTrue(true);
    }

    public function testHomepageKeyword()
    {
        $pattern = '/([+\-&|!(){}\[\]\^"\*\?:;~\/\\\])/';
        $replace = '\\\$1';
        $value = 'www.asda.p0YilekED153702.com/asdjlkad/asd?asdkj=1';
        $keyword = preg_replace($pattern, $replace, $value);
        dd($keyword);
    }

    /**
     * @test
     */
    public function it_can_analysis_an_field()
    {


        $params = [
//            "text" => "<EMAIL>",
//            "text" => "<EMAIL>",
//            'text' => '我爱深圳天啊安阿斯达门广场',
//            'text' => 'www.p0YilekED153702.com.cn',
//            'text' => 'www.asda.p0YilekED153702.com/asdjlkad/asd?asdkj=1',
//            'text' => 'rubyYa---*********---202003130000002303',
//            'text' => 'baidu.com',
                'text' => '1 8002752232',
//            "field" => "customer_list.email",
            "field" => "customer_list.tel.keyword",
//            "field" => "name",
//            "field" => "serial_id",
//            "field" => "homepage",

//            "field" => "customer_list.email.domain",
        ];

//        preg_match("/(https?:\/\/)?(www\.)?([^\/]+)(.*)?/", $params['text'], $res);
//        dd($res);

        $res = $this->analyzer($params);
        dd($res);
    }


    /**
     * @test
     */
    public function it_can_insert_some_data_into_index()
    {
        $model = $this->getMetadataIndex();
        $model->setRouting(1);
        $res = $model->bulk([
            1 => [
                'name' => 'asdajkl1lkjaw0912',
                'homepage' => 'www.asda.baidu.com/asdjlkad/asd?asdkj=1',
                'customer_list' => [
                    'email' => '<EMAIL>'
                ],
                'serial_id' => 'rubyYa---*********---202003130000002303',
                'tel' => '************/**** ********'
            ],
            2 => [
                'name' => '我爱深圳天安门广场',
                'homepage' => 'www.aksdj.com',
                'customer_list' => [
                    'email' => '<EMAIL>'
                ],
            ],
            3 => [
                'name' => '我爱深圳天啊安阿斯达门广场',
                'homepage' => 'www.baidu.com',
                'customer_list' => [
                    'email' => '<EMAIL>'
                ],
            ],
        ]);
        $this->seeJsonStructure([
            'took',
            'items' => [
                [
                    'index' => [
                        'status' => 200
                    ]
                ]
            ]
        ], $res);
    }


    /**
     * @test
     */
    public function it_can_query_one_email()
    {
        $model = $this->getMetadataIndex();
        $model->setRouting(1);
        $res = $model->search([
//            'explain' => true,
            "query" => [
//                "ids" => ["values"  => ['1', '2']],
//                "bool" => [
//                    "should" => [
//                        [
//                            "multi_match" => [
//                                "query" => "sda",
//                                "fields" => [
//                                    "email.split",
//                                ],
//                                "type" => "phrase",
//                                "slop" => 0,
//                                "max_expansions" => 1,
//                                "operator" => 'and',
//                            ]
//                        ],
//                    ]
//                ],
                "bool" => [
                    "must" => [
                        [
//                            'match' => [
//                                'homepage' =>  "baidu.com",
//                            ],
//                            'match' => [
//                                'serial_id' =>  "*********---202003130000002303",
//                            ],
                            'match' => [
                                'tel' =>  "************/**** ********",
                            ],
                        ],
//                        [
//                            "multi_match" => [
////                                "query" => "asdajkl1lkjaw0912",
////                                "query" => "天安门",
//                                "query" => "baidu.com",
//                                "fields" => [
////                                    "name.icu_token",
////                                    "email.keyword",
//                                    'homepage^4'
//                                ],
////                                "type" => "best_fields",
////                                "type" => "phrase",
////                                "slop" => 0,
////                                "max_expansions" => 1,
//                                "operator" => 'and',
//                                'minimum_should_match' => '100%'
//                            ],
//                        ],
//                        [
//                            'nested' => [
//                                'path' => 'customer_list',
//                                'query' => [
//                                    'match' => [
//                                        'customer_list.email' => '<EMAIL>',
//                                    ]
//                                ]
//                            ],
//
//                        ],
                    ]
                ],
            ],
        ]);
        $this->echo($res);
    }


    protected function analyzer($params)
    {
        $model = $this->getMetadataIndex();
        $index = $model->index();

        $host = implode(':', \Yii::app()->params['elastic_search_v7']['BasicAuthentication']) . '@' . current(\Yii::app()->params['elastic_search_v7']['hosts']);
        $uri = "/$index/_analyze?pretty";
        $data = json_encode($params);

        return $this->requestUrl($host, $uri, $data);
    }


}