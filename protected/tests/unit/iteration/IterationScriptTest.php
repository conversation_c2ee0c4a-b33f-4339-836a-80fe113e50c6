<?php

namespace common\tests\unit\iteration_script;

use common\commands\iteration\demo\DemoClientTask;
use common\commands\iteration\erp_v6_2\refreshInventoryTask;
use common\library\api\InnerApi;
use common\library\mail\setting\black\Help;
use common\library\report\gitlab\GitlabClient;
use common\modules\prometheus\library\kube_sphere\KsOpenApiClient;
use common\modules\prometheus\library\kube_sphere\YamlHelper;
use common\modules\prometheus\library\report\StatisticRepository;
use common\modules\prometheus\library\script\Constants;
use common\modules\prometheus\library\script\dispatcher\BatchDispatcher;
use common\modules\prometheus\library\script\dispatcher\Helper;
use common\modules\prometheus\library\script\dispatcher\PlanDispatcher;
use common\modules\prometheus\library\script\dispatcher\task\IterationScriptTask;
use common\modules\prometheus\library\script\execution_batch\BatchExecuteStat;
use common\modules\prometheus\library\script\execution_batch\ExecutionBatch;
use common\modules\prometheus\library\script\execution_batch\ExecutionBatchList;
use common\modules\prometheus\library\script\execution_batch\ExecutionBatchModel;
use common\modules\prometheus\library\script\execution_batch\ExecutionBatchRecord;
use common\modules\prometheus\library\script\execution_batch\ExecutionBatchRecordList;
use common\modules\prometheus\library\script\execution_batch\ExecutionBatchRecordModel;
use common\modules\prometheus\library\script\Helper as HelperAlias;
use common\modules\prometheus\library\script\ScriptRelation;
use common\modules\prometheus\library\script\ScriptRelationModel;
use common\modules\prometheus\library\script\plan\ScriptPlan;
use common\modules\prometheus\library\script\plan\ScriptPlanModel;
use common\modules\prometheus\library\script\plan\PlanExecuteStat;
use common\modules\prometheus\library\script\task\ScriptTask;

class IterationScriptTest extends \FunctionalTestCase
{
    public function testCreateIteration()
    {
        $formatData = [
            'iteration_name' => 'iteration'.xm_function_now().microtime(true),
            'path' => 'erp_v6_2',
            'description' => '',
            'project_manager' => 'been',
            'official_launch_time' => xm_function_date(),
        ];
        $iteration = new ScriptRelation();
        $iteration->iteration_name = $formatData['iteration_name'];
        $iteration->path = $formatData['path'];
        $iteration->description = $formatData['description'];

        $iteration->create_user = '<EMAIL>';
        $iteration->create_time = date('Y-m-d H:i:s');
        $iteration->enable_flag = \Constants::ENABLE_FLAG_TRUE;

        $iteration->project_manager = $formatData['project_manager'];
        $iteration->official_launch_time = $formatData['official_launch_time'];

        $iteration->save();

        var_dump($iteration->getAttributes());
    }

    public function testCreatePlan()
    {
        $plan = new ScriptPlan(**********);
        $plan->setAccountEmail('<EMAIL>');
        $plan->script_relation_id = 18;
        $plan->description = '';
        $plan->principal = '<EMAIL>';
        $plan->dispatch_type = 'client';
        $plan->dispatch_rules = '{"client_id": [14367,1]}';
        $ret = $plan->save();

        $this->echo($plan->getAttributes());
        $this->assertTrue($ret);
    }

    public function testCreateTask()
    {
        $formatData = [
            'iteration_id' => 18,
            'task_name' => '刷新库存',
            'task_class' => 'refreshInventoryTask',
            'description' => '刷新库存',
            'principal' => '<EMAIL>',
            'repeatable_flag' => 1,
            'dispatch_type' => 'client',
        ];

        $iteration = new ScriptRelation($formatData['iteration_id']);

//        $taskId = **********;
        $taskId = 0;
        $task = new ScriptTask($taskId);
        $task->script_relation_id = $formatData['iteration_id'];
        $task->task_name = $formatData['task_name'];
        $task->task_class = $formatData['task_class'];
        $task->description = $formatData['description'];
        $task->principal = $formatData['principal'];
        $task->repeatable_flag = $formatData['repeatable_flag'];
        $task->dispatch_type = $formatData['dispatch_type'];
        $task->create_time = date('Y-m-d H:i:s');
        $task->enable_flag = \Constants::ENABLE_FLAG_TRUE;
        $task->path = $iteration->path;
        $task->save();

        var_dump($task->getAttributes());
    }


    public function testExecutePlan()
    {
        $planIds = [
            1634513517,
//            1634513518,
//            1634513519,
//            1634513520
        ];
        $test_run = 0;
        foreach ($planIds as $planId)
        {
            $planDispatcher = new PlanDispatcher($planId);
            $planDispatcher->setOperatorEmail('<EMAIL>');
            $planDispatcher->dispatch();
        }
    }

    public function testValidDispatch()
    {
        $planId = **********;
        $plan = new ScriptPlan($planId);
        $plan->dispatch_type = 'client';
        $plan->dispatch_rules = '{"client_id": [14367,1]}';
        $ret = $plan->validDispatch();
        $this->assertTrue($ret);

        $plan->dispatch_type = 'mysql';
        $plan->dispatch_rules = '{"mysql_set_id": [14367,1]}';
        $ret = $plan->validDispatch();
        $this->assertTrue($ret);

        $plan->dispatch_type = 'pg';
        $plan->dispatch_rules = '{"pg_set_id": [14367,1]}';
        $ret = $plan->validDispatch();
        $this->assertTrue($ret);

        $plan->dispatch_type = 'pg';
        $plan->dispatch_rules = '{"pg_set_id": [14367,1],"all_db":[]}';
        $ret = $plan->validDispatch();
        $this->assertTrue($ret);
    }

    public function testFormatDispatchRule()
    {
        $plan = new ScriptPlan();
        $dispatchRules = '{"client_id": [14367,1],"client_tail_num":[]}';
        $ret = $plan->formatDispatchRule($dispatchRules);
        $this->echo($dispatchRules);
        $this->echo($ret);
        $this->assertNotEmpty($ret);

        $dispatchRules = '{"client_tail_num":[]}';
        $ret = $plan->formatDispatchRule($dispatchRules);
        $this->echo($dispatchRules);
        $this->echo($ret);
        $this->assertEmpty($ret);

        $dispatchRules = '{}';
        $ret = $plan->formatDispatchRule($dispatchRules);
        $this->echo($dispatchRules);
        $this->echo($ret);
        $this->assertEmpty($ret);
    }

    public function testTask()
    {
        $recordList = [
            [
                'record_id' => 0,
                'client_id' => 14367,
                'mysql_set_id' => 3,
                'pgsql_set_id' => 10,
            ]
        ];
        $task = new refreshInventoryTask('production');
        $task->setRecordList($recordList);
        $task->run();
    }

    public function testAnalyzePlanStatus()
    {
        $statusMap = [
//            Constants::BATCH_STATUS_ONGOING,
            Constants::BATCH_STATUS_SUCCESS,
            Constants::BATCH_STATUS_FAIL,
            Constants::BATCH_STATUS_SUCCESS_PARTIAL_FAIL,
        ];

        if (!empty(array_intersect([Constants::BATCH_STATUS_NONE, Constants::BATCH_STATUS_ONGOING],$statusMap)))
        {
            var_dump('存在未开始、进行中的batch：结束');
            return true;
        }

        //3,4,5,3+4,3+5,4+5,3+4+5
        $sum = array_sum($statusMap);
        switch ($sum)
        {
            case 3:
                $status = Constants::PLAN_STATUS_SUCCESS;
                break;
            case 4:
                $status = Constants::BATCH_STATUS_FAIL;
                break;
            case 5:
            case 7:
            case 8:
            case 9:
            case 12:
                $status = Constants::BATCH_STATUS_SUCCESS_PARTIAL_FAIL;
                break;
            default:
                $status = false;
        }

        var_dump($status);
    }


    public function testCreateAndExecute()
    {
        $formatData = [
            'iteration_name' => 'iteration'.xm_function_now().microtime(true),
            'path' => 'erp_v6',
            'description' => '',
            'project_manager' => 'been',
            'official_launch_time' => xm_function_date(),
        ];
        $iteration = new ScriptRelation();
        $iteration->iteration_name = $formatData['iteration_name'];
        $iteration->path = $formatData['path'];
        $iteration->description = $formatData['description'];

        $iteration->create_user = '<EMAIL>';
        $iteration->create_time = date('Y-m-d H:i:s');
        $iteration->enable_flag = \Constants::ENABLE_FLAG_TRUE;

        $iteration->project_manager = $formatData['project_manager'];
        $iteration->official_launch_time = $formatData['official_launch_time'];

        $iteration->save();

        $this->echo($iteration->getAttributes());

        $formatDataMap = [
            [
                'iteration_id' => $iteration->relation_id,
                'task_name' => '刷新库存',
                'task_class' => 'DemoTask',
                'description' => '刷新库存',
                'principal' => '<EMAIL>',
                'repeatable_flag' => 1,
                'dispatch_type' => 'client',
            ],
            [
                'iteration_id' => $iteration->relation_id,
                'task_name' => '刷新库存',
                'task_class' => 'DemoMysqlTask',
                'description' => '刷新库存',
                'principal' => '<EMAIL>',
                'repeatable_flag' => 1,
                'dispatch_type' => 'mysql',
            ],
            [
                'iteration_id' => $iteration->relation_id,
                'task_name' => '刷新库存',
                'task_class' => 'DemoPgsqlTask',
                'description' => '刷新库存',
                'principal' => '<EMAIL>',
                'repeatable_flag' => 1,
                'dispatch_type' => 'pg',
            ],
            [
                'iteration_id' => $iteration->relation_id,
                'task_name' => '刷新库存',
                'task_class' => 'DemoAllTask',
                'description' => '刷新库存',
                'principal' => '<EMAIL>',
                'repeatable_flag' => 1,
                'dispatch_type' => 'all',
            ],
        ];


        foreach ($formatDataMap as $formatData)
        {
            $task = new ScriptTask();
            $task->script_relation_id = $formatData['iteration_id'];
            $task->task_name = $formatData['task_name'];
            $task->task_class = $formatData['task_class'];
            $task->description = $formatData['description'];
            $task->principal = $formatData['principal'];
            $task->repeatable_flag = $formatData['repeatable_flag'];
            $task->dispatch_type = $formatData['dispatch_type'];
            $task->create_time = date('Y-m-d H:i:s');
            $task->enable_flag = \Constants::ENABLE_FLAG_TRUE;
            $task->path = $iteration->path;
            $task->save();
        }

        $planList = [
            [
                'dispatch_type' => 'client',
                'dispatch_rules' => '{"client_id":[14367,1]}',
            ],
            [
                'dispatch_type' => 'mysql',
                'dispatch_rules' => '{"mysql_set_id":[3]}',
            ],
            [
                'dispatch_type' => 'pg',
                'dispatch_rules' => '{"pg_set_id":[10]}',
            ],
            [
                'dispatch_type' => 'all',
                'dispatch_rules' => '{}',
            ],
        ];
        foreach ($planList as $dispatch)
        {
            $plan = new ScriptPlan();
            $plan->setAccountEmail('<EMAIL>');
            $plan->script_relation_id = $iteration->relation_id;
            $plan->description = '';
            $plan->principal = '<EMAIL>';
            $plan->dispatch_type = $dispatch['dispatch_type'];
            $plan->dispatch_rules = $dispatch['dispatch_rules'];
            $ret = $plan->save();
        }

    }

    public function testDemoTask()
    {
        $recordList = [
            [
                'record_id' => 0,
                'client_id' => 14367,
                'mysql_set_id' => 3,
                'pgsql_set_id' => 10,
            ]
        ];

        $task = new DemoClientTask('dev');
        $task->setRecordList($recordList);
        $task->run();
    }

    public function testReRun() {
        $dispatch = new BatchDispatcher(**********);
        $dispatch->reRunFail(true);
        $dispatch->dispatch();
    }

    public function testPlanExecuteDispatcher() {
        $dispatcher = new PlanDispatcher(**********);
        $dispatcher->dispatch();
    }

    public function testBatchDispatcher() {
        $plan = new ScriptPlan(**********);
        $batchList = new ExecutionBatchList();
        $batchList->setPlanIds($plan->plan_id);
        $batchList->setExecuteStatus(Constants::BATCH_STATUS_NONE);
        $batchList = $batchList->find();
        $dispatcher = new BatchDispatcher($batchList[0]['batch_id']);
        $dispatcher->dispatch();
    }

    public function testReportStatistics() {
        $repo = new StatisticRepository();
//        $planStat = new PlanExecuteStat(1, [1, 2]);
//        $repo->setStat($planStat);
        $planStat = $repo->getStat(PlanExecuteStat::class, **********);
        var_dump($planStat);
    }

    public function testRedis() {
        $redis = \RedisService::queuePersistent();
        var_dump($redis->llen());
    }

    public function testPlanExecuteStat() {
        $flag = get_class_methods(RecordExecuteStat::class);
        var_dump($flag);
    }

    public function testInterruptDispatcher() {
        $repo = new StatisticRepository();
        $planId = **********;
        $plan = new ScriptPlan($planId);
        $batch = [];
        foreach ($plan->last_execute_batch as $batchId) {
            $batchStat = $repo->getStat(BatchExecuteStat::class, $batchId);
            $batch[] = $batchStat;
        }

        var_dump($batch);
    }

    public function testCleanUpInterruptData() {
        $planId = 1634513542;
        $redis = \RedisService::cache();
        $redis->del("plan_{$planId}_interrupt");

        $repo = new StatisticRepository();
        $repo->delStat(PlanExecuteStat::class, $planId);
        $plan = new ScriptPlan($planId);
        foreach ($plan->last_execute_batch as $batchId) {
            $repo->delStat(BatchExecuteStat::class, $batchId);
        }

        $lastExecuteBatch = implode(",", $plan->last_execute_batch);
        $sql = <<<SQL
UPDATE prometheus.tbl_iteration_plan t SET t.execute_status = 1, t.last_execute_batch = 'null' WHERE t.plan_id = {$planId};
delete from tbl_iteration_execution_batch_record where batch_id in ({$lastExecuteBatch});
delete from tbl_iteration_execution_batch where plan_id = $planId;
SQL;
        \Yii::app()->prometheus_db->createCommand($sql)->execute();
    }

    public function testExecuteBatch()
    {
        $batchId = 1634626690;
        $dispatcher = new BatchDispatcher($batchId);
        $dispatcher->dispatch();
    }

//    public function testPushQueue() {
//        $batchId = 1634626003;
//        $batch = new ExecutionBatch($batchId);
//
//        $recordList = new ExecutionBatchRecordList();
//        $recordList->setFields(['record_id','dispatch_refer_id']);
//        $recordList->setBatchIds($batchId);
//        $recordList->setExecuteStatus([Constants::RECORD_STATUS_NONE]);
//        $recordList = $recordList->find();
//
//        $dispatch = new BatchDispatcher($batchId);
//        $recordSetList = $dispatch->aggregateRecord($recordList);
//
//        $redis = \RedisService::queuePersistent(true);
//        $batchQueue = "iteration:script:batch".\Yii::app()->params['env'];
//        $task = new ScriptTask($batch->task_id);
//        $class = Constants::ITERATION_SCRIPT_ROOT_PATH.$task->path.'\\'.$task->task_class;
//        foreach ($recordSetList as $recordList)
//        {
//            $data = [
//                'class' => $class,
//                'record_list' => $recordList
//            ];
//            $this->log('push queue:'.var_export($data,true));
//            //push
//            $redis->rpush($batchQueue, [json_encode($data, true)]);
//        }
//
//        $result = $redis->brpop([$batchQueue], 0);
//        $data = json_decode($result[1], true);
//        var_dump($data);
//    }

    public function testIterationBranch() {
        $iterationId = 75;
        var_dump(ScriptRelationModel::getGitlabBranch($iterationId));
    }

    public function testGitlabClient() {
        $gitlab = new GitlabClient();
        $id = 186;
        $branches = $gitlab->getBranch($id, 'es-migration');
        var_dump($branches);
    }

    public function testGetGitlabUser() {
        $gitlab = new GitlabClient();
        var_dump($gitlab->getMergeRequest(GitlabClient::PID_PHP_CRM, 32766));
    }

    public function testGetGitlabFile() {
        $gitlab = new GitlabClient();
        $rawFile = $gitlab->getRawFile(GitlabClient::PID_PHP_CRM, "protected/commands/iteration/es_migration/SyncEsDataTask.php", "feature/es-migration");
        $path = "es_migration";
        $task = "SyncEsDataTask.php";
        $classPath = \Yii::app()->getBasePath().'/commands/iteration/';
        if (!is_dir($classPath.$path)){
            mkdir($classPath.$path);
        }
        if (!file_exists($classPath.$path."/".$task)) {
            $fp = fopen($classPath.$path."/".$task, 'x');
            fwrite($fp, $rawFile);
            fclose($fp);
//            file_put_contents($classPath.$path."/".$task, $rawFile);
        }
    }

    public function testParseDoc() {
        $reflectionClass = new \ReflectionClass(GitlabClient::class);
        $doc = $reflectionClass->getDocComment();
        $docLines = explode("\n", $doc);
        $calledMethod = "getMergeRequest";
        foreach ($docLines as $line) {
            if (str_contains($line, $calledMethod)) {
                var_dump($line);
                preg_match_all('/\$(_|[A-Za-z])\w*/', $line, $paramName);
                var_dump($paramName);
            }
        }
    }

    public function testTryCatch() {
        try {
            throw new \RuntimeException("helloworld");
        } catch (\Exception $exception) {
            var_dump($exception->getMessage());
        }
    }

    public function testThirdPartyClientMethod() {
        $method = [
            'get',
            'postJson',
            'postText',
            'post',
            'put',
            'head'
        ];
        $requestName = "postJsonYaml";
        foreach ($method as $validMethod) {
            if (str_starts_with($requestName, $validMethod)) {
                $requestUrl = preg_replace("/{$validMethod}/", "", $requestName, 1);
                $requestUrl = lcfirst($requestUrl);
                var_dump($validMethod);
                var_dump($requestUrl);
                die();
            }
        }
    }

    public function testServerless() {
        $client = new KsOpenApiClient();
        $yaml = YamlHelper::parseIterationScript(75);
        var_dump($client->startService($yaml));
    }

    public function testServerlessStatus() {
        $client = new KsOpenApiClient();
        $yaml = YamlHelper::parseIterationScript(75);
        var_dump($client->getServiceStatus($yaml));
    }

    public function testDeleteServerlessService() {
        $client = new KsOpenApiClient();
        $yaml = YamlHelper::parseIterationScript(75);
        var_dump($client->deleteService($yaml));
    }

    public function testAsyncBatch() {
        $batchId = 1634626563;
        $dispatcher = new BatchDispatcher($batchId);
        $dispatcher->dispatch(1);
    }

    public function testBatchGitInfo() {
        $batchId = 1634626563;
        $batch = new ExecutionBatch($batchId);
        var_dump($batch->getAttributes());
    }

    public function testAsync() {
        $plan_id = 1634513659;

        $db = ScriptPlanModel::model()->getDbConnection();
        $table = ScriptPlanModel::model()->tableName();
        $sql = "UPDATE {$table} t SET t.execute_status = 4 WHERE t.plan_id = {$plan_id}";
        $db->createCommand($sql)->execute();

        $dispatcher = new PlanDispatcher($plan_id);
        $dispatcher->dispatch();

    }

    public function testConsumer() {
//        var_dump(\RedisService::cache()->del([KsOpenApiClient::TOKEN_KEY_PREFIX."test"]));

        $client = new KsOpenApiClient();
        $yaml = YamlHelper::parseIterationScript(36);
        $serviceName = $client->getServiceStatus($yaml);
        var_dump($serviceName);

    }

    public function testRecordPartition() {
        $recordList = new ExecutionBatchRecordList();
//        $recordList->setTaskIds(1634570878);
        $recordList->setBatchIds(1634626616);
        $iterationId = $recordList->getScriptRelationId();
        var_dump($iterationId);
        var_dump(ExecutionBatchRecordModel::model()->getTableName($iterationId));
    }

    public function testRecordUpdate() {
        HelperAlias::updateRecordExecuteStatus([1634651393], 1634626130);
    }

    public function testMergeIterationScript() {
        $api = new \common\modules\prometheus\library\script\task\ScriptTaskApi();
        $api->mergeIterationScript(246, [216]);
    }

    public function testRefreshAssistantTabSettingTask()
    {
        $recordList = [
            [
                'record_id' => 0,
                'client_id' => 351389,
                'mysql_set_id' => 3,
                'pgsql_set_id' => 10,
            ]
        ];
        $task = new \common\commands\iteration\iteration_3_5\RefreshAssistantTabSettingTask();
        $task->setRecordList($recordList);
        $task->run();
    }

    public function testRefreshShopsAddMktFunctionalTask()
    {
        $recordList = [
            [
                'record_id' => 0,
                'client_id' => 351389,
                'mysql_set_id' => 3,
                'pgsql_set_id' => 10,
            ]
        ];
        $task = new \common\commands\iteration\iteration_3_5\RefreshShopsAddMktFunctionalTask();
        $task->setRecordList($recordList);
        $task->run();
    }

    public function testPaasCustomFieldUpdateTask()
    {
        $recordList = [
            [
                'record_id' => 0,
                'client_id' => 14100,
                'mysql_set_id' => 3,
                'pgsql_set_id' => 10,
            ]
        ];
        $task = new \common\commands\iteration\iteration_4_1\PaasCustomFieldUpdateTask();
        $task->setRecordList($recordList);
        $task->run();
    }

    public function testProfessionalFreeUpgradeOpenAPITask()
    {
        $recordList = [
            [
                'record_id' => 0,
                'client_id' => 351352,
                'mysql_set_id' => 3,
                'pgsql_set_id' => 10,
            ]
        ];
        $task = new \common\commands\iteration\iteration_5_3\ProfessionalFreeUpgradeOpenAPITask();
        $task->setRecordList($recordList);
        $task->run();
    }

    public function testConvertConfigUpdateTask()
    {

        $recordList = [
            [
                'record_id' => 0,
                'client_id' => 14367,
                'mysql_set_id' => 3,
                'pgsql_set_id' => 10,
            ]
        ];
        $task = new \common\commands\iteration\iteration_5_3\ConvertConfigUpdateTask();
        $task->setRecordList($recordList);
        $task->run();

    }
}