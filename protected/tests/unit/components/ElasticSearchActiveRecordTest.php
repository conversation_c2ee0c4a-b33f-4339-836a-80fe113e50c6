<?php

use common\library\search\ESProtocol;
use common\models\search\CompanySearch;
use Elasticsearch_5\ClientBuilder;

/**
 * Created by PhpStorm.
 * User: cayley
 * Date: 17/5/8
 * Time: 上午11:32
 */
class ElasticSearchActiveRecordTest extends FunctionalTestCase
{
//
//    public function setUP()
//    {
//        User::setLoginUserById(2);
//    }

    public function test()
    {

//        $hosts = ['*************:9600'];
//
//        $client = ClientBuilder::create()->setHosts($hosts)->build();
//        $params = [
//            'index' => 'andytest',
//            'type' => 'company',
//            'body' => [
//                'company' => [
//                    '_source' => [
//                        'enabled' => true
//                    ],
//                    'properties' => [
//                        'name' => [
//                            'type' => 'string',
//                            'analyzer' => 'standard'
//                        ],
//                        'short_name' => [
//                            'type' => 'string',
//                            'analyzer' => 'standard'
//                        ],
//                        'homepage' => [
//                            'type' => 'string',
////                            'analyzer' => 'uax_url_email'
//                        ],
//                        'tel' => [
//                            'type' => 'string',
//                            'analyzer' => 'standard'
//                        ],
//                    ]
//                ]
//            ]
//        ];

        // Update the index mapping
//        $ret = $client->indices()->putMapping($params);

//        print_r($ret);die;

        $client = \common\models\search\CompanySearch::model()->getDbConnection();
        $params = [
            'index' => 'my_index',
            'body'  => [
//                'settings' => [
////                    'number_of_shards' => 3,
////                    'number_of_replicas' => 2
//                ],
                'mappings' => [
                    'my_type' => [
                        '_source'    => [
                            'enabled' => true
                        ],
                        'properties' => [
                            'first_name' => [
                                'type'     => 'string',
                                'analyzer' => 'standard'
                            ],
                            'age'        => [
                                'type' => 'integer'
                            ]
                        ]
                    ]
                ]
            ]
        ];


// Create the index with mappings and settings now
//        $response = $client->indices()->create($params);
//        die;
//        \common\models\search\CompanySearch::model()->updateMapping();
//die;
//       $result =  \common\models\search\CompanySearch::getDb()->indices()->getSettings(['index' =>'company']);
//        $params = ['index' => 'andytest'];
//        $result = $client->indices()->getMapping($params);

//        $client->indices()->delete($params);


//       var_dump($result);
//        die;
        $search = new  \common\models\search\CompanySearch();
        $search->id = 1;
        $search->client_id = 1;
        $search->user_id = [765, 2];
        $search->name = '深圳市小满科技有限公司';
        $search->short_name = '小满';
        $search->homepage = 'www.xiaoman.cn';
        $search->tel = '18825195240';
        $search->customer = [
            [
                'customer_id' => 1,
                'email'       => '<EMAIL>',
                'name'        => 'cayley',
            ]
        ];

        $search->setIsNewRecord(false);
        $search->delete();


//        var_dump($result);die;
    }

    public function testById()
    {
        $this->loginUser('<EMAIL>');
        $id = 38686795;

        $res = \common\models\search\CompanySearch::model()->findByPk($id)['_source'];
        dd($res);
    }

    public function testFindByFields()
    {
        $id = 1102247961;
        $this->loginAsLora();

        $es = new \common\library\search\ESCriteria();
        $es->addFilter('client_id', static::$clientId);
        $es->addShouldMatch(
            [
                'short_name' => [
                    'query'    => 'Unique Company Name',
                    'operator' => 'and'
                ],
            ]
        );
        $es->addShouldMatch(
            [
                'customer_list.email' => 'cataloginteriors.com',
            ]
        );
//        $es->addMatch(
//            [
//                'customer_list.name' => [
//                    'query' => 'Andre Munoz',
//                    'operator' => 'and'
//                ]
//            ]
//        );
        $es->addShouldMatch([
                'path'  => 'external_field',
                'query' => [
                    'bool' => [
                        'must' => [
                            [
                                'term' => [
                                    'external_field.field' => 35074833,
                                ]
                            ],
                            [
                                'match' => [
                                    'external_field.value' => [
                                        'query'    => '123',
                                        'operator' => 'and'
                                    ]
                                ]
                            ],
                        ],
                    ]
                ]
            ], 'nested');
        $res = \common\models\search\CompanySearch::model()->findAllByCriteria($es);
        dd($res);
    }


    /*
    |--------------------------------------------------------------------------
    |
    |--------------------------------------------------------------------------
    |
    |
    */
    public static function setUpBeforeClass():void
    {
        parent::setUpBeforeClass();
        \Yii::app()->params['env'] = 'unittest';
    }
    /**
     * @return \common\components\ElasticSearchActiveRecord
     */
    public function getCompanySearcher()
    {
        return \common\components\ElasticSearchActiveRecord::model(CompanySearch::class);
//        $companySearcher = new Class extends \common\models\search\CompanySearch {
//
//            public static function index()
//            {
//                return 'customer_debug';
//            }
//
//            public static function type()
//            {
//                return 'company_debug';
//            }
//
//            public static function model($className = __CLASS__)
//            {
//                return parent::model($className);
//            }
//        };
//
//        return CompanySearch::model();
    }

    public function getSearchHandler()
    {
        $handler = (new Class extends \common\library\server\search\CompanyHandler {

            /**
             * @param mixed $searcher
             */
            public function setSearcher($searcher): void
            {
                $this->searcher = $searcher;
            }

            protected $searcher;

            public function handle()
            {
                $params = $this->buildParams();
                /**
                 * @var \Elasticsearch_5\Client  $connection
                 */
                $connection = $this->searcher->getDbConnection();
                switch ( $this->opType )
                {
                    case  \Constants::SEARCH_INDEX_TYPE_UPDATE:
                        if( !empty($params['body']))
                        {
//                    throw  new \Exception('test retry');
                            $ret = $connection->index($params);
                            if (isset($ret['result']) && ($ret['result'] == 'created' || $ret['result'] = 'updated'))
                            {
                            } else
                            {
                                throw  new \Exception('更新失败' . json_encode($ret));
                            }
                        }

                        break;
                    case \Constants::SEARCH_INDEX_TYPE_DELETE:
                        $ret = $connection->delete($params);
                        if (isset($ret['result']) && $ret['result'] == 'deleted')
                        {
                        } else
                        {
                            throw  new \Exception('删除失败' . json_encode($ret));
                        }
                        break;
                    default:
                        break;
                }

                return ['id'=> $this->id, 'result' => $ret['result']??'error'];
            }
        });
        $handler->setSearcher($this->getCompanySearcher());
        return $handler;
    }

    /**
     */
    public function testCreateIndex()
    {
        $companySearch = \common\components\ElasticSearchActiveRecord::model(CompanySearch::class);
        $companySearch->createIndex();
        $this->assertTrue(true);
    }

    public function testDeleteIndex()
    {
        $companySearch = $this->getCompanySearcher();
        $companySearch->setRouting(1);
        $companySearch->deleteMapping();
    }

    public function testCreateIndexAlias()
    {
        $alias = 'customer_smart_v_1_6';
        $ret = $this->getCompanySearcher()->getDbConnection()->indices()->putAlias([
            'index' => 'customer',
            'name' => $alias,
        ]);
        dd($ret);
    }

    public function testUpdateIndex()
    {
        $companySearch = $this->getCompanySearcher();
        $companySearch->updateIndexSettings();
        $companySearch->updateMapping();
        $this->assertTrue(true);
    }

    public function testGetMapping()
    {
        $companySearch = $this->getCompanySearcher();
        dd($companySearch->mapping(), $companySearch->setting());
    }

    public function testIndexOneCompany()
    {
        $this->loginAsJocelyn();
//        $companyId = TestData::getOnePrivateCompany()['company_id'];
        $companyId = ********;
//        $this->loginUser('<EMAIL> ');
        $this->loginAsKk();
        $handler = new \common\library\server\search\CompanyHandler();
        $data = [
            'id'         => $companyId,
            'client_id'  => static::$clientId,
            'user_id'    => static::$userId,
            'company_id' => $companyId,
            'type'       => Constants::SEARCH_INDEX_TYPE_UPDATE,
        ];

        $handler->initData($data);
        $ret = $handler->handle();

        $this->echo($ret);

        $result = $this->getCompanySearcher()->findByPk($companyId);
        $this->echo($result);
    }

    public function testSearchList()
    {
        $this->loginAsJocelyn();
        $es = new \common\library\search\ESCriteria();
        $es->addFilter('client_id', static::$clientId);
        $result = $this->getCompanySearcher()->findAllByCriteria($es);
        $this->echo($result);
    }

    public function testSearchDomain()
    {
        $this->loginAsJocelyn();
        $es = new \common\library\search\ESCriteria(static::$clientId);
        $es->addFilter('client_id', static::$clientId);
//        $es->addMatch(['name' => '测试判重公司'], 'match');
        $es->addMatch([
            'customer_list.email' => 'cataloginteriors'
        ]);
//        dd($es->buildQuery());
        $result = $this->getCompanySearcher()->findAllByCriteria($es);
        $this->echo($result);
    }

    public function testAnalyzer()
    {

//        $this->testUpdateIndex();
        $index = $this->getCompanySearcher()->index();
//        $map = [];
//        foreach ($this->getCompanySearcher()->mapping()['company']['properties'] as $field => $setting){
//            $map[$field] = $setting['analyzer'] ?? '';
//        }

        $analyzer = 'email';
        $value = '<EMAIL>';

        $pattern = '/(\+|-|&|\||!|\(|\)|\{|}|\[|]|\^|"|~|\*|\?|:|;|~|\/|\<)/';
        $replace = '\\\$1';
        $keyword = preg_replace($pattern, $replace, $value);

        $params = [
//            'filter' =>  [
//                "domain_filter",
//                "host_filter",
//                "domain_stop_filter",
//                "lowercase",
//                "unique"
//            ],
//            'tokenizer' => 'uax_url_email',
//            'filter' => [
//                'word_delimiter',
//            ],
//            'analyzer' => 'standard',
//            'analyzer' => 'alphanumeric',
//            'field' => 'customer_list.contact.value.tel',
//            'field' => 'customer_list.name.keyword',
//            'field' => 'external_field.value.keyword',
//            'field' => 'customer_list.tel',
            'field' => 'customer_list.contact.value.tel',
//            'field' => ESProtocol::NUMBER_MATH_ANALYZER,
            'text' => '15180+0614241',
//            'text' => '+86 123 456 78',
//            'text' => '+86 123 456 78',
        ];
        $host = implode(':', Yii::app()->params['elastic_search_v7']['BasicAuthentication']) . '@' . current(Yii::app()->params['elastic_search_v7']['hosts']);
        $index = 'company';

//        $host = current(Yii::app()->params['elastic_search']['hosts']);
//        $index = 'customer_smart_v_1_6';

        $uri = "/$index/_analyze?pretty";
        $data = json_encode($params);

        $res = $this->restCurl($host, $uri, $data);
        $this->echo($res);
    }

    public function testDebugProductIndex()
    {
        $index = 'product_smart_1_7';
        $params = [
//            'filter' =>  [
//                "domain_filter",
//                "host_filter",
//                "domain_stop_filter",
//                "lowercase",
//                "unique"
//            ],
//            'tokenizer' => 'uax_url_email',
//            'filter' => [
//                'domain_filter',
//            ],
//            'analyzer' => 'standard',
            'field' => 'keyword',
//            'field' => ESProtocol::NUMBER_MATH_ANALYZER,
//            'text' => 'http://www.adasd.a1adalght-saltasdad.com/other?params=asd',
            'text' => 'c5*5*C2',
        ];
        $host = current(Yii::app()->params['elastic_search']['hosts']);
        $uri = "/$index/_analyze?pretty";
        $data = json_encode($params);

        $res = $this->restCurl($host, $uri, $data);
        $this->echo($res);
    }

    public function restCurl($host, $uri, $data = null, $auth = null, $method = 'GET'){
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $host.$uri);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json'));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        if ($method == 'POST')
            curl_setopt($ch, CURLOPT_POST, 1);
        if ($auth)
            curl_setopt($ch, CURLOPT_USERPWD, $auth);
        if (strlen($data) > 0)
            curl_setopt($ch, CURLOPT_POSTFIELDS,$data);

        $resp  = curl_exec($ch);
        if(!$resp){
            $resp = (json_encode(array(array("error" => curl_error($ch), "code" => curl_errno($ch)))));
        }
        curl_close($ch);
        return $resp;
    }

    public function testDomain()
    {
        $domain = ['ad','ae','af','ag','ai','al','am','an','ao','aq','ar','as','at','au','aw','az','ba','bb','bd','be','bf','bg','bh','bi','bj','bm','bn','bo','br','bs','bt','bv','bw','by','bz','ca','cc','cf','cg','ch','ci','ck','cl','cm','cn','co','cq','cr','cu','cv','cx','cy','cz','de','dj','dk','dm','do','dz','ec','ee','eg','eh','es','et','ev','fi','fj','fk','fm','fo','fr','ga','gb','gd','ge','gf','gh','gi','gl','gm','gn','gp','gr','gt','gu','gw','gy','hk','hm','hn','hr','ht','hu','id','ie','il','in','io','iq','ir','is','it','jm','jo','jp','ke','kg','kh','ki','km','kn','kp','kr','kw','ky','kz','la','lb','lc','li','lk','lr','ls','lt','lu','lv','ly','ma','mc','md','mg','mh','ml','mm','mn','mo','mp','mq','mr','ms','mt','mv','mw','mx','my','mz','na','nc','ne','nf','ng','ni','nl','no','np','nr','nt','nu','nz','om','pa','pe','pf','pg','ph','pk','pl','pm','pn','pr','pt','pw','py','qa','re','ro','ru','rw','sa','sb','sc','sd','se','sg','sh','si','sj','sk','sl','sm','sn','so','sr','st','su','sy','sz','tc','td','tf','tg','th','tj','tk','tm','tn','to','tp','tr','tt','tv','tw','tz','ua','ug','uk','us','uy','va','vc','ve','vg','vn','vu','wf','ws','ye','yu','za','zm','zr','zw','al','dz','af','ar','ae','aw','om','az','eg','et','ie','ee','ad','ao','ai','ag','at','au','mo','bb','pg','bs','pk','py','ps','bh','pa','br','by','bm','bg','mp','bj','be','is','pr','ba','pl','bo','bz','bw','bt','bf','bi','bv','kp','gq','dk','de','tl','tp','tg','dm','do','ru','ec','er','fr','fo','pf','gf','tf','va','ph','fj','fi','cv','fk','gm','cg','cd','co','cr','gg','gd','gl','ge','cu','gp','gu','gy','kz','ht','kr','nl','an','hm','hn','ki','dj','kg','gn','gw','ca','gh','ga','kh','cz','zw','cm','qa','ky','km','ci','kw','cc','hr','ke','ck','lv','ls','la','lb','lt','lr','ly','li','re','lu','rw','ro','mg','im','mv','mt','mw','my','ml','mk','mh','mq','yt','mu','mr','us','um','as','vi','mn','ms','bd','pe','fm','mm','md','ma','mc','mz','mx','nr','np','ni','ne','ng','nu','no','nf','na','za','aq','gs','eu','pw','pn','pt','jp','se','ch','sv','ws','yu','sl','sn','cy','sc','sa','cx','st','sh','kn','lc','sm','pm','vc','lk','sk','si','sj','sz','sd','sr','sb','so','tj','tw','th','tz','to','tc','tt','tn','tv','tr','tm','tk','wf','vu','gt','ve','bn','ug','ua','uy','uz','es','eh','gr','hk','sg','nc','nz','hu','sy','jm','am','ac','ye','iq','ir','il','it','in','id','uk','vg','io','jo','vn','zm','je','td','gi','cl','cf','cn','yr','com','arpa','edu','gov','int','mil','net','org','biz','info','pro','name','museum','coop','aero','xxx','idv','me','mobi','asia','ax','bl','bq','cat','cw','gb','jobs','mf','rs','su','sx','tel','travel'];
        dd(array_values(array_unique($domain)));
    }

}