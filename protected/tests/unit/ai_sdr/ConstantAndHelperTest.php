<?php

namespace tests\unit\ai_sdr;

use common\library\ai_sdr\Constant;
use common\library\ai_sdr\Helper;

/**
 * 常量和工具类测试
 * 
 * 测试AI SDR的常量定义和工具方法
 */
class ConstantAndHelperTest extends \ProjectTestCase
{
    /**
     * 测试任务状态常量
     */
    public function testTaskStatusConstants_AreDefinedCorrectly()
    {
        // 验证任务状态常量存在且值正确
        $this->assertEquals(0, Constant::AI_SDR_TASK_STATUS_DRAFT, 'Draft status should be 0');
        $this->assertEquals(1, Constant::AI_SDR_TASK_STATUS_PROCESSING, 'Processing status should be 1');
        $this->assertEquals(2, Constant::AI_SDR_TASK_STATUS_PAUSED, 'Paused status should be 2');
        $this->assertEquals(3, Constant::AI_SDR_TASK_STATUS_FINISHED, 'Finished status should be 3');
        
        // 验证所有状态都是整数
        $statuses = [
            Constant::AI_SDR_TASK_STATUS_DRAFT,
            Constant::AI_SDR_TASK_STATUS_PROCESSING,
            Constant::AI_SDR_TASK_STATUS_PAUSED,
            Constant::AI_SDR_TASK_STATUS_FINISHED
        ];
        
        foreach ($statuses as $status) {
            $this->assertIsInt($status, "Status {$status} should be integer");
        }
        
        $this->assertTrue(true, 'Task status constants are defined correctly');
    }
    
    /**
     * 测试任务来源常量
     */
    public function testTaskSourceConstants_AreDefinedCorrectly()
    {
        // 验证任务来源常量存在且值正确
        $this->assertEquals(-1, Constant::TASK_SOURCE_SYSTEM, 'System source should be -1');
        $this->assertEquals(1, Constant::TASK_SOURCE_AI_SDR, 'AI SDR source should be 1');
        $this->assertEquals(2, Constant::TASK_SOURCE_IMPORT, 'Import source should be 2');
        $this->assertEquals(3, Constant::TASK_SOURCE_CRM_EP, 'CRM EP source should be 3');
        
        // 验证所有来源都是整数
        $sources = [
            Constant::TASK_SOURCE_SYSTEM,
            Constant::TASK_SOURCE_AI_SDR,
            Constant::TASK_SOURCE_IMPORT,
            Constant::TASK_SOURCE_CRM_EP
        ];
        
        foreach ($sources as $source) {
            $this->assertIsInt($source, "Source {$source} should be integer");
        }
        
        $this->assertTrue(true, 'Task source constants are defined correctly');
    }
    
    /**
     * 测试阶段常量
     */
    public function testStageConstants_AreDefinedCorrectly()
    {
        // 验证阶段常量存在且值正确
        $this->assertEquals(0, Constant::AI_SDR_STAGE_DIG, 'Dig stage should be 0');
        $this->assertEquals(1, Constant::AI_SDR_STAGE_REACHABLE, 'Reachable stage should be 1');
        $this->assertEquals(2, Constant::AI_SDR_STAGE_MARKETING, 'Marketing stage should be 2');
        $this->assertEquals(3, Constant::AI_SDR_STAGE_EFFECTIVE, 'Effective stage should be 3');
        $this->assertEquals(4, Constant::AI_SDR_STAGE_HIGHVALUE, 'High value stage should be 4');
        
        // 验证阶段顺序是递增的
        $stages = [
            Constant::AI_SDR_STAGE_DIG,
            Constant::AI_SDR_STAGE_REACHABLE,
            Constant::AI_SDR_STAGE_MARKETING,
            Constant::AI_SDR_STAGE_EFFECTIVE,
            Constant::AI_SDR_STAGE_HIGHVALUE
        ];
        
        for ($i = 1; $i < count($stages); $i++) {
            $this->assertGreaterThan($stages[$i-1], $stages[$i], "Stage {$stages[$i]} should be greater than {$stages[$i-1]}");
        }
        
        $this->assertTrue(true, 'Stage constants are defined correctly');
    }
    
    /**
     * 测试详情状态常量
     */
    public function testDetailStatusConstants_AreDefinedCorrectly()
    {
        // 验证详情状态常量存在且值正确
        $this->assertEquals(0, Constant::DETAIL_STATUS_ADD, 'Add status should be 0');
        $this->assertEquals(1, Constant::DETAIL_STATUS_LABEL, 'Label status should be 1');
        $this->assertEquals(2, Constant::DETAIL_STATUS_BACKGROUND_CHECKING, 'Background checking status should be 2');
        $this->assertEquals(3, Constant::DETAIL_STATUS_BACKGROUND_CHECKED, 'Background checked status should be 3');
        $this->assertEquals(4, Constant::DETAIL_STATUS_VALIDATE_CONTACTS, 'Validate contacts status should be 4');
        $this->assertEquals(5, Constant::DETAIL_STATUS_CREATE_MARKETING_PLAN, 'Create marketing plan status should be 5');
        $this->assertEquals(6, Constant::DETAIL_STATUS_EXECUTE_MARKETING_PLAN, 'Execute marketing plan status should be 6');
        $this->assertEquals(-1, Constant::DETAIL_STATUS_ERROR, 'Error status should be -1');
        
        // 验证所有状态都是整数
        $statuses = [
            Constant::DETAIL_STATUS_ADD,
            Constant::DETAIL_STATUS_LABEL,
            Constant::DETAIL_STATUS_BACKGROUND_CHECKING,
            Constant::DETAIL_STATUS_BACKGROUND_CHECKED,
            Constant::DETAIL_STATUS_VALIDATE_CONTACTS,
            Constant::DETAIL_STATUS_CREATE_MARKETING_PLAN,
            Constant::DETAIL_STATUS_EXECUTE_MARKETING_PLAN,
            Constant::DETAIL_STATUS_ERROR
        ];
        
        foreach ($statuses as $status) {
            $this->assertIsInt($status, "Detail status {$status} should be integer");
        }
        
        $this->assertTrue(true, 'Detail status constants are defined correctly');
    }
    
    /**
     * 测试质量常量
     */
    public function testQualityConstants_AreDefinedCorrectly()
    {
        // 验证质量常量存在且值正确
        $this->assertEquals(0, Constant::LEAD_QUALITY_UNKNOWN, 'Unknown quality should be 0');
        $this->assertEquals(1, Constant::LEAD_QUALITY_LOW, 'Low quality should be 1');
        $this->assertEquals(2, Constant::LEAD_QUALITY_MEDIUM, 'Medium quality should be 2');
        $this->assertEquals(3, Constant::LEAD_QUALITY_HIGH, 'High quality should be 3');
        
        // 验证质量等级是递增的
        $qualities = [
            Constant::LEAD_QUALITY_UNKNOWN,
            Constant::LEAD_QUALITY_LOW,
            Constant::LEAD_QUALITY_MEDIUM,
            Constant::LEAD_QUALITY_HIGH
        ];
        
        for ($i = 1; $i < count($qualities); $i++) {
            $this->assertGreaterThan($qualities[$i-1], $qualities[$i], "Quality {$qualities[$i]} should be greater than {$qualities[$i-1]}");
        }
        
        $this->assertTrue(true, 'Quality constants are defined correctly');
    }
    
    /**
     * 测试记录类型常量
     */
    public function testRecordTypeConstants_AreDefinedCorrectly()
    {
        // 验证记录类型常量存在且值正确
        $this->assertEquals(0, Constant::RECORD_TYPE_ADD_LEAD, 'Add lead record type should be 0');
        $this->assertEquals(1, Constant::RECORD_TYPE_BACKGROUND_CHECK, 'Background check record type should be 1');
        $this->assertEquals(2, Constant::RECORD_TYPE_ANALYZE_QUALITY, 'Analyze quality record type should be 2');
        $this->assertEquals(3, Constant::RECORD_TYPE_CHECK_CONTACTS, 'Check contacts record type should be 3');
        $this->assertEquals(4, Constant::RECORD_TYPE_CREATE_MARKET_PLAN, 'Create market plan record type should be 4');
        $this->assertEquals(5, Constant::RECORD_TYPE_EXECUTE_MARKET_PLAN, 'Execute market plan record type should be 5');
        
        // 验证所有记录类型都是整数
        $recordTypes = [
            Constant::RECORD_TYPE_ADD_LEAD,
            Constant::RECORD_TYPE_BACKGROUND_CHECK,
            Constant::RECORD_TYPE_ANALYZE_QUALITY,
            Constant::RECORD_TYPE_CHECK_CONTACTS,
            Constant::RECORD_TYPE_CREATE_MARKET_PLAN,
            Constant::RECORD_TYPE_EXECUTE_MARKET_PLAN
        ];
        
        foreach ($recordTypes as $type) {
            $this->assertIsInt($type, "Record type {$type} should be integer");
            $this->assertGreaterThanOrEqual(0, $type, "Record type {$type} should be >= 0");
        }
        
        $this->assertTrue(true, 'Record type constants are defined correctly');
    }
    
    /**
     * 测试限制常量
     */
    public function testLimitConstants_AreDefinedCorrectly()
    {
        // 验证限制常量存在且值合理
        $this->assertEquals(70, Constant::DAILY_LIMIT, 'Daily limit should be 70');
        $this->assertEquals(-1, Constant::TOTAL_LIMIT_UNLIMITED, 'Unlimited should be -1');
        $this->assertEquals(10000, Constant::AI_SDR_POTENTIAL_CUSTOMER_COUNT_LIMIT, 'Potential customer count limit should be 10000');
        
        // 验证限制值都是整数
        $limits = [
            Constant::DAILY_LIMIT,
            Constant::TOTAL_LIMIT_UNLIMITED,
            Constant::AI_SDR_POTENTIAL_CUSTOMER_COUNT_LIMIT
        ];
        
        foreach ($limits as $limit) {
            $this->assertIsInt($limit, "Limit {$limit} should be integer");
        }
        
        // 验证正数限制是合理的
        $this->assertGreaterThan(0, Constant::DAILY_LIMIT, 'Daily limit should be positive');
        $this->assertGreaterThan(0, Constant::AI_SDR_POTENTIAL_CUSTOMER_COUNT_LIMIT, 'Customer count limit should be positive');
        
        $this->assertTrue(true, 'Limit constants are defined correctly');
    }
    
    /**
     * 测试Redis缓存键常量
     */
    public function testRedisCacheKeyConstants_AreDefinedCorrectly()
    {
        // 验证Redis缓存键常量存在
        $this->assertIsString(Constant::REDIS_CACHE_TASK_KEY, 'Task cache key should be string');
        $this->assertIsString(Constant::TASK_DAILY_LIMIT_CACHE_KEY, 'Daily limit cache key should be string');
        $this->assertIsString(Constant::REDIS_AI_SDR_TASK_DIG_PROCESSING_KEY, 'Dig processing cache key should be string');
        
        // 验证缓存键包含占位符
        $this->assertStringContainsString('{%s}', Constant::REDIS_CACHE_TASK_KEY, 'Task cache key should contain placeholders');
        $this->assertStringContainsString('{%s}', Constant::TASK_DAILY_LIMIT_CACHE_KEY, 'Daily limit cache key should contain placeholders');
        $this->assertStringContainsString('{%s}', Constant::REDIS_AI_SDR_TASK_DIG_PROCESSING_KEY, 'Dig processing cache key should contain placeholders');

        // 验证缓存键前缀
        $this->assertStringStartsWith('sdr:', Constant::REDIS_CACHE_TASK_KEY, 'Task cache key should start with sdr:');
        $this->assertStringStartsWith('sdr:', Constant::TASK_DAILY_LIMIT_CACHE_KEY, 'Daily limit cache key should start with sdr:');
        $this->assertStringStartsWith('sdr:', Constant::REDIS_AI_SDR_TASK_DIG_PROCESSING_KEY, 'Dig processing cache key should start with sdr:');
        
        $this->assertTrue(true, 'Redis cache key constants are defined correctly');
    }
    
    /**
     * 测试Helper类存在性
     */
    public function testHelperClassExists()
    {
        $this->assertTrue(class_exists(Helper::class), 'Helper class should exist');
        $this->assertTrue(true, 'Helper class exists');
    }
    
    /**
     * 测试常量分组逻辑性
     */
    public function testConstantGroupLogic_IsConsistent()
    {
        // 测试状态转换的逻辑性
        // ADD(0) 应该能转换到 LABEL(1) 或 BACKGROUND_CHECKING(2)
        $this->assertLessThan(Constant::DETAIL_STATUS_LABEL, Constant::DETAIL_STATUS_ADD);
        $this->assertLessThan(Constant::DETAIL_STATUS_BACKGROUND_CHECKING, Constant::DETAIL_STATUS_ADD);
        
        // 阶段应该是递增的
        $this->assertLessThan(Constant::AI_SDR_STAGE_REACHABLE, Constant::AI_SDR_STAGE_DIG);
        $this->assertLessThan(Constant::AI_SDR_STAGE_MARKETING, Constant::AI_SDR_STAGE_REACHABLE);
        $this->assertLessThan(Constant::AI_SDR_STAGE_EFFECTIVE, Constant::AI_SDR_STAGE_MARKETING);
        $this->assertLessThan(Constant::AI_SDR_STAGE_HIGHVALUE, Constant::AI_SDR_STAGE_EFFECTIVE);
        
        // 质量等级应该是递增的
        $this->assertLessThan(Constant::LEAD_QUALITY_LOW, Constant::LEAD_QUALITY_UNKNOWN);
        $this->assertLessThan(Constant::LEAD_QUALITY_MEDIUM, Constant::LEAD_QUALITY_LOW);
        $this->assertLessThan(Constant::LEAD_QUALITY_HIGH, Constant::LEAD_QUALITY_MEDIUM);
        
        $this->assertTrue(true, 'Constant group logic is consistent');
    }
    
    /**
     * 测试常量值的唯一性
     */
    public function testConstantUniqueness_WithinGroups()
    {
        // 测试任务状态值的唯一性
        $taskStatuses = [
            Constant::AI_SDR_TASK_STATUS_DRAFT,
            Constant::AI_SDR_TASK_STATUS_PROCESSING,
            Constant::AI_SDR_TASK_STATUS_PAUSED,
            Constant::AI_SDR_TASK_STATUS_FINISHED
        ];
        $this->assertEquals(count($taskStatuses), count(array_unique($taskStatuses)), 'Task status values should be unique');
        
        // 测试阶段值的唯一性
        $stages = [
            Constant::AI_SDR_STAGE_DIG,
            Constant::AI_SDR_STAGE_REACHABLE,
            Constant::AI_SDR_STAGE_MARKETING,
            Constant::AI_SDR_STAGE_EFFECTIVE,
            Constant::AI_SDR_STAGE_HIGHVALUE
        ];
        $this->assertEquals(count($stages), count(array_unique($stages)), 'Stage values should be unique');
        
        // 测试质量值的唯一性
        $qualities = [
            Constant::LEAD_QUALITY_UNKNOWN,
            Constant::LEAD_QUALITY_LOW,
            Constant::LEAD_QUALITY_MEDIUM,
            Constant::LEAD_QUALITY_HIGH
        ];
        $this->assertEquals(count($qualities), count(array_unique($qualities)), 'Quality values should be unique');
        
        $this->assertTrue(true, 'Constant values are unique within groups');
    }
}
