<?php

namespace tests\unit\ai_sdr;

use common\library\ai_sdr\Constant;
use common\library\ai_sdr\task\AiSdrTask;
use common\library\ai_sdr\task\AiSdrTaskFilter;
use common\library\ai_sdr\task_detail\AiSdrTaskDetail;
use common\library\ai_sdr\task_detail\AiSdrTaskDetailFilter;
use common\library\ai_sdr\task_record\AiSdrTaskRecord;
use common\library\ai_sdr\task_record\AiSdrTaskRecordFilter;

/**
 * 数据模型测试
 * 
 * 测试AI SDR相关的数据模型和过滤器
 */
class DataModelTest extends \FunctionalTestCase
{
    protected $testClientId = 333392;
    protected $testUserId = 249519530;
    
    protected function setUp(): void
    {
        parent::setUp();
        $this->loginClient($this->testClientId);
    }
    
    /**
     * 测试AiSdrTask模型实例化
     */
    public function testAiSdrTaskInstantiation_WithValidClientId_CreatesInstance()
    {
        // Act
        $task = new AiSdrTask($this->testClientId);

        // Assert
        $this->assertInstanceOf(AiSdrTask::class, $task, 'Should create AiSdrTask instance');
        $this->assertTrue($task->isNew(), 'New task should be marked as new');

        // 手动设置client_id来测试
        $task->client_id = $this->testClientId;
        $this->assertEquals($this->testClientId, $task->client_id);

        $this->assertTrue(true, 'AiSdrTask instantiation works correctly');
    }
    
    /**
     * 测试AiSdrTaskDetail模型实例化
     */
    public function testAiSdrTaskDetailInstantiation_WithValidClientId_CreatesInstance()
    {
        // Act
        $detail = new AiSdrTaskDetail($this->testClientId);
        
        // Assert
        $this->assertInstanceOf(AiSdrTaskDetail::class, $detail, 'Should create AiSdrTaskDetail instance');
        $this->assertTrue($detail->isNew(), 'New detail should be marked as new');
        
        $this->assertTrue(true, 'AiSdrTaskDetail instantiation works correctly');
    }
    
    /**
     * 测试AiSdrTaskRecord模型实例化
     */
    public function testAiSdrTaskRecordInstantiation_WithValidTaskId_CreatesInstance()
    {
        // Act
        $record = new AiSdrTaskRecord(12345); // 使用任务ID作为参数
        
        // Assert
        $this->assertInstanceOf(AiSdrTaskRecord::class, $record, 'Should create AiSdrTaskRecord instance');
        $this->assertTrue($record->isNew(), 'New record should be marked as new');
        
        $this->assertTrue(true, 'AiSdrTaskRecord instantiation works correctly');
    }
    
    /**
     * 测试AiSdrTaskFilter过滤器
     */
    public function testAiSdrTaskFilter_WithDifferentConditions_FiltersCorrectly()
    {
        try {
            // 基础过滤器测试
            $filter = new AiSdrTaskFilter($this->testClientId);
            $this->assertInstanceOf(AiSdrTaskFilter::class, $filter, 'Should create AiSdrTaskFilter instance');
            
            // 设置过滤条件
            $filter->task_status = Constant::AI_SDR_TASK_STATUS_PROCESSING;
            $filter->source = Constant::TASK_SOURCE_AI_SDR;
            $filter->limit(5);
            $filter->order('create_time', 'desc');
            
            // 执行查询
            $tasks = $filter->find();
            $this->assertNotNull($tasks, 'Filter should return results');
            
            // 测试计数
            $count = $filter->count();
            $this->assertIsInt($count, 'Count should return integer');
            $this->assertGreaterThanOrEqual(0, $count, 'Count should be non-negative');
            
            $this->assertTrue(true, 'AiSdrTaskFilter works correctly');
        } catch (\Exception $e) {
            $this->markTestSkipped('Task filter test failed: ' . $e->getMessage());
        }
    }
    
    /**
     * 测试AiSdrTaskDetailFilter过滤器
     */
    public function testAiSdrTaskDetailFilter_WithDifferentConditions_FiltersCorrectly()
    {
        try {
            // 基础过滤器测试
            $filter = new AiSdrTaskDetailFilter($this->testClientId);
            $this->assertInstanceOf(AiSdrTaskDetailFilter::class, $filter, 'Should create AiSdrTaskDetailFilter instance');
            
            // 设置过滤条件
            $filter->status = Constant::DETAIL_STATUS_ADD;
            $filter->stage = Constant::AI_SDR_STAGE_DIG;
            $filter->enable_flag = 1;
            $filter->limit(10);
            
            // 执行查询
            $details = $filter->find();
            $this->assertNotNull($details, 'Filter should return results');
            
            // 测试计数
            $count = $filter->count();
            $this->assertIsInt($count, 'Count should return integer');
            $this->assertGreaterThanOrEqual(0, $count, 'Count should be non-negative');
            
            $this->assertTrue(true, 'AiSdrTaskDetailFilter works correctly');
        } catch (\Exception $e) {
            $this->markTestSkipped('Task detail filter test failed: ' . $e->getMessage());
        }
    }
    
    /**
     * 测试AiSdrTaskRecordFilter过滤器
     */
    public function testAiSdrTaskRecordFilter_WithDifferentConditions_FiltersCorrectly()
    {
        try {
            // 基础过滤器测试
            $filter = new AiSdrTaskRecordFilter($this->testClientId);
            $this->assertInstanceOf(AiSdrTaskRecordFilter::class, $filter, 'Should create AiSdrTaskRecordFilter instance');
            
            // 设置过滤条件
            $filter->type = Constant::RECORD_TYPE_ADD_LEAD;
            $filter->limit(5);
            $filter->order('create_time', 'desc');
            
            // 执行查询
            $records = $filter->find();
            $this->assertNotNull($records, 'Filter should return results');
            
            // 测试计数
            $count = $filter->count();
            $this->assertIsInt($count, 'Count should return integer');
            $this->assertGreaterThanOrEqual(0, $count, 'Count should be non-negative');
            
            $this->assertTrue(true, 'AiSdrTaskRecordFilter works correctly');
        } catch (\Exception $e) {
            $this->markTestSkipped('Task record filter test failed: ' . $e->getMessage());
        }
    }
    
    /**
     * 测试模型字段验证
     */
    public function testModelFieldValidation_WithDifferentValues_ValidatesCorrectly()
    {
        // 测试任务状态字段
        $validTaskStatuses = [
            Constant::AI_SDR_TASK_STATUS_DRAFT,
            Constant::AI_SDR_TASK_STATUS_PROCESSING,
            Constant::AI_SDR_TASK_STATUS_PAUSED,
            Constant::AI_SDR_TASK_STATUS_FINISHED
        ];
        
        foreach ($validTaskStatuses as $status) {
            $this->assertIsInt($status, "Task status {$status} should be integer");
            $this->assertGreaterThanOrEqual(0, $status, "Task status {$status} should be >= 0");
        }
        
        // 测试详情状态字段
        $validDetailStatuses = [
            Constant::DETAIL_STATUS_ADD,
            Constant::DETAIL_STATUS_LABEL,
            Constant::DETAIL_STATUS_BACKGROUND_CHECKING,
            Constant::DETAIL_STATUS_VALIDATE_CONTACTS,
            Constant::DETAIL_STATUS_CREATE_MARKETING_PLAN,
            Constant::DETAIL_STATUS_EXECUTE_MARKETING_PLAN,
            Constant::DETAIL_STATUS_ERROR
        ];
        
        foreach ($validDetailStatuses as $status) {
            $this->assertIsInt($status, "Detail status {$status} should be integer");
        }
        
        // 测试质量字段
        $validQualities = [
            Constant::LEAD_QUALITY_UNKNOWN,
            Constant::LEAD_QUALITY_LOW,
            Constant::LEAD_QUALITY_MEDIUM,
            Constant::LEAD_QUALITY_HIGH
        ];
        
        foreach ($validQualities as $quality) {
            $this->assertIsInt($quality, "Lead quality {$quality} should be integer");
            $this->assertGreaterThanOrEqual(0, $quality, "Lead quality {$quality} should be >= 0");
        }
        
        $this->assertTrue(true, 'Model field validation works correctly');
    }
    
    /**
     * 测试模型关联关系
     */
    public function testModelRelationships_AreDefinedCorrectly()
    {
        // 测试任务和详情的关系 - 使用正确的client_id
        $taskData = AiSdrTestDataFactory::createTaskData([
            'client_id' => $this->testClientId
        ]);
        $detailData = AiSdrTestDataFactory::createTaskDetailData($taskData['task_id'] ?? 12345);

        $this->assertEquals($taskData['client_id'], $this->testClientId);
        $this->assertEquals($detailData['task_id'], $taskData['task_id'] ?? 12345);

        // 测试详情和记录的关系
        $recordData = AiSdrTestDataFactory::createTaskRecordData(
            $detailData['task_id'],
            $detailData['id'] ?? 67890
        );

        $this->assertEquals($recordData['task_id'], $detailData['task_id']);
        $this->assertEquals($recordData['detail_id'], $detailData['id'] ?? 67890);

        $this->assertTrue(true, 'Model relationships are defined correctly');
    }
    
    /**
     * 测试数组字段处理
     */
    public function testArrayFieldHandling_WithDifferentArrays_HandlesCorrectly()
    {
        // 测试tags字段（数组类型）
        $taskData = AiSdrTestDataFactory::createTaskData([
            'tags' => ['tag1', 'tag2', 'tag3']
        ]);
        
        $this->assertIsArray($taskData['tags'], 'Tags should be array');
        $this->assertCount(3, $taskData['tags'], 'Tags should have 3 elements');
        $this->assertContains('tag1', $taskData['tags'], 'Tags should contain tag1');
        
        // 测试product_ids字段（数组类型）
        $detailData = AiSdrTestDataFactory::createTaskDetailData(12345, [
            'product_ids' => ['Product A', 'Product B'],
            'company_types' => ['Manufacturer', 'Exporter']
        ]);
        
        $this->assertIsArray($detailData['product_ids'], 'Product IDs should be array');
        $this->assertIsArray($detailData['company_types'], 'Company types should be array');
        $this->assertCount(2, $detailData['product_ids'], 'Product IDs should have 2 elements');
        $this->assertCount(2, $detailData['company_types'], 'Company types should have 2 elements');
        
        $this->assertTrue(true, 'Array field handling works correctly');
    }
    
    /**
     * 测试JSON字段处理
     */
    public function testJsonFieldHandling_WithDifferentData_HandlesCorrectly()
    {
        // 测试记录的data字段（JSON类型）
        $recordData = AiSdrTestDataFactory::createTaskRecordData(12345, 67890, [
            'data' => [
                'action' => 'quality_analysis',
                'result' => 'high_quality',
                'confidence' => 0.85,
                'details' => [
                    'reason' => ['Good match', 'High potential'],
                    'score' => 85
                ]
            ]
        ]);
        
        $this->assertIsArray($recordData['data'], 'Record data should be array');
        $this->assertArrayHasKey('action', $recordData['data'], 'Data should have action key');
        $this->assertArrayHasKey('result', $recordData['data'], 'Data should have result key');
        $this->assertArrayHasKey('confidence', $recordData['data'], 'Data should have confidence key');
        $this->assertArrayHasKey('details', $recordData['data'], 'Data should have details key');
        
        $this->assertEquals('quality_analysis', $recordData['data']['action']);
        $this->assertEquals(0.85, $recordData['data']['confidence']);
        $this->assertIsArray($recordData['data']['details'], 'Details should be array');
        
        $this->assertTrue(true, 'JSON field handling works correctly');
    }
    
    /**
     * 测试时间字段处理
     */
    public function testTimeFieldHandling_WithDifferentFormats_HandlesCorrectly()
    {
        // 测试创建时间和更新时间
        $taskData = AiSdrTestDataFactory::createTaskData();
        
        $this->assertArrayHasKey('create_time', $taskData, 'Task should have create_time');
        $this->assertArrayHasKey('update_time', $taskData, 'Task should have update_time');
        
        // 验证时间格式
        $createTime = $taskData['create_time'];
        $updateTime = $taskData['update_time'];
        
        $this->assertIsString($createTime, 'Create time should be string');
        $this->assertIsString($updateTime, 'Update time should be string');
        
        // 验证时间格式是否可以解析
        $createTimestamp = strtotime($createTime);
        $updateTimestamp = strtotime($updateTime);
        
        $this->assertNotFalse($createTimestamp, 'Create time should be valid timestamp');
        $this->assertNotFalse($updateTimestamp, 'Update time should be valid timestamp');
        
        // 测试阶段时间字段
        $detailData = AiSdrTestDataFactory::createTaskDetailData(12345);
        
        $stageTimeFields = [
            'stage_dig_time',
            'stage_reachable_time',
            'stage_marketing_time',
            'stage_effective_time',
            'stage_highvalue_time'
        ];
        
        foreach ($stageTimeFields as $field) {
            $this->assertArrayHasKey($field, $detailData, "Detail should have {$field}");
            $this->assertIsString($detailData[$field], "{$field} should be string");
        }
        
        $this->assertTrue(true, 'Time field handling works correctly');
    }
}
