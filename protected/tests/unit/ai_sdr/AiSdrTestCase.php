<?php

namespace tests\unit\ai_sdr;

use common\library\ai_sdr\Constant;
use common\library\ai_sdr\task\AiSdrTask;
use common\library\ai_sdr\task_detail\AiSdrTaskDetail;
use common\library\ai_sdr\task_record\AiSdrTaskRecord;
use tests\DatabaseTransactions;

/**
 * AI SDR 测试基类
 * 
 * 提供AI SDR模块测试的通用功能和工具方法
 */
abstract class AiSdrTestCase extends \ProjectTestCase
{
    use DatabaseTransactions;
    
    // 测试用的固定客户ID和用户ID
    protected const TEST_CLIENT_ID = 999999;
    protected const TEST_USER_ID = 888888;
    
    protected static $testDataIds = [
        'tasks' => [],
        'details' => [],
        'records' => []
    ];
    
    protected function setUp(): void
    {
        parent::setUp();
        
        // 设置测试环境
        $this->setupTestEnvironment();
    }
    
    protected function tearDown(): void
    {
        // 清理测试数据
        $this->cleanupTestData();
        
        parent::tearDown();
    }
    
    /**
     * 设置测试环境
     */
    protected function setupTestEnvironment(): void
    {
        // 设置测试环境标识
        \Yii::app()->params['env'] = 'test';
        
        // 清理Redis缓存
        $this->clearRedisCache();
    }
    
    /**
     * 清理Redis缓存
     */
    protected function clearRedisCache(): void
    {
        try {
            $redis = \RedisService::cache();
            $keys = $redis->keys("sdr:*:" . self::TEST_CLIENT_ID . ":*");
            if (!empty($keys)) {
                $redis->del($keys);
            }
        } catch (\Exception $e) {
            // 忽略Redis连接错误
        }
    }
    
    /**
     * 清理测试数据
     */
    protected function cleanupTestData(): void
    {
        // 清理记录
        foreach (self::$testDataIds['records'] as $recordId) {
            try {
                $record = new AiSdrTaskRecord(self::TEST_CLIENT_ID, $recordId);
                if (!$record->isNew()) {
                    $record->delete();
                }
            } catch (\Exception $e) {
                // 忽略删除错误
            }
        }
        
        // 清理详情
        foreach (self::$testDataIds['details'] as $detailId) {
            try {
                $detail = new AiSdrTaskDetail(self::TEST_CLIENT_ID, $detailId);
                if (!$detail->isNew()) {
                    $detail->delete();
                }
            } catch (\Exception $e) {
                // 忽略删除错误
            }
        }
        
        // 清理任务
        foreach (self::$testDataIds['tasks'] as $taskId) {
            try {
                $task = new AiSdrTask(self::TEST_CLIENT_ID, $taskId);
                if (!$task->isNew()) {
                    $task->delete();
                }
            } catch (\Exception $e) {
                // 忽略删除错误
            }
        }
        
        // 重置ID数组
        self::$testDataIds = [
            'tasks' => [],
            'details' => [],
            'records' => []
        ];
    }
    
    /**
     * 创建测试任务
     */
    protected function createTestTask(array $attributes = []): AiSdrTask
    {
        $defaults = [
            'client_id' => self::TEST_CLIENT_ID,
            'user_id' => self::TEST_USER_ID,
            'source' => Constant::TASK_SOURCE_AI_SDR,
            'current_stage' => Constant::AI_SDR_STAGE_DIG,
            'end_stage' => Constant::AI_SDR_STAGE_MARKETING,
            'task_status' => Constant::AI_SDR_TASK_STATUS_PROCESSING,
            'email' => '<EMAIL>',
            'tags' => ['test'],
            'enable_flag' => 1,
            'stat_total' => 0,
            'create_time' => date('Y-m-d H:i:s'),
            'update_time' => date('Y-m-d H:i:s')
        ];
        
        $attributes = array_merge($defaults, $attributes);
        
        $task = new AiSdrTask($attributes['client_id']);
        foreach ($attributes as $key => $value) {
            if ($key !== 'client_id') {
                $task->$key = $value;
            }
        }
        
        $success = $task->create();
        if (!$success) {
            throw new \RuntimeException('Failed to create test task');
        }
        
        // 记录测试数据ID用于清理
        self::$testDataIds['tasks'][] = $task->task_id;
        
        return $task;
    }
    
    /**
     * 创建测试任务详情
     */
    protected function createTestTaskDetail(AiSdrTask $task, array $attributes = []): AiSdrTaskDetail
    {
        $defaults = [
            'task_id' => $task->task_id,
            'lead_id' => rand(1000000, 9999999),
            'user_id' => $task->user_id,
            'source' => $task->source,
            'stage' => Constant::AI_SDR_STAGE_DIG,
            'status' => Constant::DETAIL_STATUS_ADD,
            'lead_quality' => Constant::LEAD_QUALITY_UNKNOWN,
            'product_ids' => [],
            'company_types' => [],
            'public_homepage' => [],
            'enable_flag' => 1,
            'stage_dig_time' => '1970-01-01 00:00:01',
            'stage_reachable_time' => '1970-01-01 00:00:01',
            'stage_marketing_time' => '1970-01-01 00:00:01',
            'stage_effective_time' => '1970-01-01 00:00:01',
            'stage_highvalue_time' => '1970-01-01 00:00:01',
            'create_time' => date('Y-m-d H:i:s'),
            'update_time' => date('Y-m-d H:i:s')
        ];
        
        $attributes = array_merge($defaults, $attributes);
        
        $detail = new AiSdrTaskDetail($task->client_id);
        foreach ($attributes as $key => $value) {
            $detail->$key = $value;
        }
        
        $success = $detail->create();
        if (!$success) {
            throw new \RuntimeException('Failed to create test task detail');
        }
        
        // 记录测试数据ID用于清理
        self::$testDataIds['details'][] = $detail->id;
        
        return $detail;
    }
    
    /**
     * 创建测试任务记录
     */
    protected function createTestTaskRecord(AiSdrTaskDetail $detail, array $attributes = []): AiSdrTaskRecord
    {
        $defaults = [
            'task_id' => $detail->task_id,
            'detail_id' => $detail->id,
            'lead_id' => $detail->lead_id,
            'type' => Constant::RECORD_TYPE_ADD_LEAD,
            'data' => [],
            'estimate_time' => date('Y-m-d H:i:s'),
            'executed_time' => null,
            'refer_type' => 0,
            'refer_id' => 0,
            'create_time' => date('Y-m-d H:i:s'),
            'update_time' => date('Y-m-d H:i:s')
        ];
        
        $attributes = array_merge($defaults, $attributes);
        
        $record = new AiSdrTaskRecord($detail->task_id);
        foreach ($attributes as $key => $value) {
            $record->$key = $value;
        }
        
        $success = $record->create();
        if (!$success) {
            throw new \RuntimeException('Failed to create test task record');
        }
        
        // 记录测试数据ID用于清理
        self::$testDataIds['records'][] = $record->record_id;
        
        return $record;
    }
    
    /**
     * 断言任务状态
     */
    protected function assertTaskStatus(int $taskId, int $expectedStatus): void
    {
        $task = new AiSdrTask(self::TEST_CLIENT_ID, $taskId);
        $this->assertEquals(
            $expectedStatus, 
            $task->task_status,
            "Task status should be {$expectedStatus}, got {$task->task_status}"
        );
    }
    
    /**
     * 断言详情状态
     */
    protected function assertDetailStatus(int $detailId, int $expectedStatus): void
    {
        $detail = new AiSdrTaskDetail(self::TEST_CLIENT_ID, $detailId);
        $this->assertEquals(
            $expectedStatus,
            $detail->status,
            "Detail status should be {$expectedStatus}, got {$detail->status}"
        );
    }
    
    /**
     * 断言详情阶段
     */
    protected function assertDetailStage(int $detailId, int $expectedStage): void
    {
        $detail = new AiSdrTaskDetail(self::TEST_CLIENT_ID, $detailId);
        $this->assertEquals(
            $expectedStage,
            $detail->stage,
            "Detail stage should be {$expectedStage}, got {$detail->stage}"
        );
    }
    
    /**
     * 获取测试客户档案
     */
    protected function getTestClientProfile(): array
    {
        return [
            'company_name' => 'Test Company',
            'main_products' => ['Product A', 'Product B'],
            'company_description' => 'This is a test company for AI SDR testing',
            'target_market' => ['US', 'EU'],
            'company_type' => ['Manufacturer'],
            'employees' => '50-100'
        ];
    }
    
    /**
     * 获取测试买家档案
     */
    protected function getTestBuyerProfile(): array
    {
        return [
            'company_name' => 'Buyer Company',
            'main_products' => ['Product X', 'Product Y'],
            'company_type' => ['Distributor'],
            'public_homepage' => ['https://buyer-company.com'],
            'country' => 'US',
            'employees_min' => 10,
            'employees_max' => 50
        ];
    }
    
    /**
     * 模拟时间
     */
    protected function mockTime(string $time = '2024-01-01 10:00:00'): void
    {
        // 这里可以实现时间Mock，如果需要的话
        // 目前使用固定时间字符串
    }
    
    /**
     * 获取测试客户ID
     */
    protected function getTestClientId(): int
    {
        return self::TEST_CLIENT_ID;
    }
    
    /**
     * 获取测试用户ID
     */
    protected function getTestUserId(): int
    {
        return self::TEST_USER_ID;
    }
}
