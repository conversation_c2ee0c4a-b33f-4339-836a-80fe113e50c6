<?php

namespace tests\unit\ai_sdr;

use common\library\ai_sdr\Constant;

/**
 * AI SDR Mock服务集合
 * 
 * 提供各种外部服务的Mock实现
 */
class MockServices
{
    /**
     * Mock时间服务
     */
    public static function createMockTimeService(string $fixedTime = '2024-01-01 10:00:00'): MockTimeService
    {
        return new MockTimeService($fixedTime);
    }
    
    /**
     * Mock缓存服务
     */
    public static function createMockCacheService(): MockCacheService
    {
        return new MockCacheService();
    }
    
    /**
     * Mock队列服务
     */
    public static function createMockQueueService(): MockQueueService
    {
        return new MockQueueService();
    }
    
    /**
     * Mock AI服务
     */
    public static function createMockAiServices(): MockAiServices
    {
        return new MockAiServices();
    }
    
    /**
     * Mock推荐API服务
     */
    public static function createMockRecommendApi(): MockRecommendApi
    {
        return new MockRecommendApi();
    }
    
    /**
     * Mock线索自动归档服务
     */
    public static function createMockLeadAutoArchive(): MockLeadAutoArchive
    {
        return new MockLeadAutoArchive();
    }
}

/**
 * Mock时间服务
 */
class MockTimeService
{
    private string $fixedTime;
    
    public function __construct(string $fixedTime = '2024-01-01 10:00:00')
    {
        $this->fixedTime = $fixedTime;
    }
    
    public function now(): string
    {
        return $this->fixedTime;
    }
    
    public function timestamp(): int
    {
        return strtotime($this->fixedTime);
    }
    
    public function setTime(string $time): void
    {
        $this->fixedTime = $time;
    }
    
    public function addMinutes(int $minutes): void
    {
        $timestamp = strtotime($this->fixedTime) + ($minutes * 60);
        $this->fixedTime = date('Y-m-d H:i:s', $timestamp);
    }
}

/**
 * Mock缓存服务
 */
class MockCacheService
{
    private array $data = [];
    private array $accessLog = [];
    private array $operations = [];
    
    public function set(string $key, $value, int $ttl = 3600): bool
    {
        $this->accessLog[] = ['operation' => 'set', 'key' => $key, 'ttl' => $ttl];
        $this->operations[] = "SET {$key}";
        $this->data[$key] = [
            'value' => $value,
            'expires' => time() + $ttl
        ];
        return true;
    }
    
    public function get(string $key)
    {
        $this->accessLog[] = ['operation' => 'get', 'key' => $key];
        $this->operations[] = "GET {$key}";
        
        if (!isset($this->data[$key])) {
            return null;
        }
        
        if ($this->data[$key]['expires'] < time()) {
            unset($this->data[$key]);
            return null;
        }
        
        return $this->data[$key]['value'];
    }
    
    public function delete(string $key): bool
    {
        $this->accessLog[] = ['operation' => 'delete', 'key' => $key];
        $this->operations[] = "DEL {$key}";
        unset($this->data[$key]);
        return true;
    }
    
    public function setNx(string $key, $value, int $ttl = 3600): bool
    {
        $this->accessLog[] = ['operation' => 'setnx', 'key' => $key, 'ttl' => $ttl];
        $this->operations[] = "SETNX {$key}";
        
        if (isset($this->data[$key]) && $this->data[$key]['expires'] >= time()) {
            return false;
        }
        
        $this->data[$key] = [
            'value' => $value,
            'expires' => time() + $ttl
        ];
        return true;
    }
    
    public function wasKeyAccessed(string $key): bool
    {
        foreach ($this->accessLog as $log) {
            if ($log['key'] === $key) {
                return true;
            }
        }
        return false;
    }
    
    public function getAccessCount(string $key): int
    {
        $count = 0;
        foreach ($this->accessLog as $log) {
            if ($log['key'] === $key) {
                $count++;
            }
        }
        return $count;
    }
    
    public function getOperations(): array
    {
        return $this->operations;
    }
    
    public function clear(): void
    {
        $this->data = [];
        $this->accessLog = [];
        $this->operations = [];
    }
}

/**
 * Mock队列服务
 */
class MockQueueService
{
    private array $dispatchedJobs = [];
    private bool $syncMode = true;
    
    public function dispatch($job): void
    {
        $this->dispatchedJobs[] = [
            'job' => $job,
            'dispatched_at' => time(),
            'class' => get_class($job)
        ];
        
        // 如果是同步模式，立即执行
        if ($this->syncMode && method_exists($job, 'handle')) {
            $job->handle();
        }
    }
    
    public function dispatchSync($job): void
    {
        $this->dispatch($job);
        if (method_exists($job, 'handle')) {
            $job->handle();
        }
    }
    
    public function setSyncMode(bool $sync): void
    {
        $this->syncMode = $sync;
    }
    
    public function getDispatchedJobsCount(): int
    {
        return count($this->dispatchedJobs);
    }
    
    public function getDispatchedJobs(): array
    {
        return $this->dispatchedJobs;
    }
    
    public function getJobsByClass(string $className): array
    {
        return array_filter($this->dispatchedJobs, function($job) use ($className) {
            return $job['class'] === $className;
        });
    }
    
    public function clear(): void
    {
        $this->dispatchedJobs = [];
    }
}

/**
 * Mock AI服务
 */
class MockAiServices
{
    private $qualityAnalysisResult = null;
    private $qualityAnalysisResults = [];
    private $qualityAnalysisException = null;
    private $backgroundCheckResult = null;
    private $marketingContentResult = null;
    
    private $qualityAnalysisCalled = false;
    private $backgroundCheckCalled = false;
    private $marketingContentCalled = false;
    
    public function setQualityAnalysisResult(array $result): void
    {
        $this->qualityAnalysisResult = $result;
    }
    
    public function setQualityAnalysisResults(array $results): void
    {
        $this->qualityAnalysisResults = $results;
    }
    
    public function setQualityAnalysisException(\Exception $exception): void
    {
        $this->qualityAnalysisException = $exception;
    }
    
    public function setBackgroundCheckResult(array $result): void
    {
        $this->backgroundCheckResult = $result;
    }
    
    public function setMarketingContentResult(array $result): void
    {
        $this->marketingContentResult = $result;
    }
    
    public function getQualityAnalysisResult(): array
    {
        $this->qualityAnalysisCalled = true;
        
        if ($this->qualityAnalysisException) {
            throw $this->qualityAnalysisException;
        }
        
        if (!empty($this->qualityAnalysisResults)) {
            return array_shift($this->qualityAnalysisResults);
        }
        
        return $this->qualityAnalysisResult ?? [
            'answer' => [
                'lead_quality' => Constant::LEAD_QUALITY_MEDIUM,
                'confidence' => 0.7,
                'reason' => ['Default mock result']
            ]
        ];
    }
    
    public function getBackgroundCheckResult(): array
    {
        $this->backgroundCheckCalled = true;
        return $this->backgroundCheckResult ?? [
            'task_id' => 12345,
            'status' => 'success'
        ];
    }
    
    public function getMarketingContentResult(): array
    {
        $this->marketingContentCalled = true;
        return $this->marketingContentResult ?? [
            [
                'subject' => 'Default Mock Subject',
                'content' => 'Default mock email content',
                'round' => 1
            ]
        ];
    }
    
    public function wasQualityAnalysisCalled(): bool
    {
        return $this->qualityAnalysisCalled;
    }
    
    public function wasBackgroundCheckCalled(): bool
    {
        return $this->backgroundCheckCalled;
    }
    
    public function wasMarketingContentCalled(): bool
    {
        return $this->marketingContentCalled;
    }
    
    public function reset(): void
    {
        $this->qualityAnalysisCalled = false;
        $this->backgroundCheckCalled = false;
        $this->marketingContentCalled = false;
        $this->qualityAnalysisException = null;
    }
}

/**
 * Mock推荐API
 */
class MockRecommendApi
{
    private array $responses = [];
    private array $callLog = [];
    
    public function setResponse(array $domains, array $response): void
    {
        foreach ($domains as $domain) {
            $this->responses[$domain] = $response;
        }
    }
    
    public function getCompanyProfileByDomains(array $domains): array
    {
        $this->callLog[] = [
            'method' => 'getCompanyProfileByDomains',
            'domains' => $domains,
            'timestamp' => time()
        ];
        
        $result = [];
        foreach ($domains as $domain) {
            if (isset($this->responses[$domain])) {
                $result[$domain] = $this->responses[$domain];
            } else {
                // 默认响应
                $result[$domain] = AiSdrTestDataFactory::createBuyerProfileData([
                    'company_name' => 'Mock Company for ' . $domain,
                    'public_homepage' => ['https://' . $domain]
                ]);
            }
        }
        
        return $result;
    }
    
    public function getCallLog(): array
    {
        return $this->callLog;
    }
    
    public function wasMethodCalled(string $method): bool
    {
        foreach ($this->callLog as $call) {
            if ($call['method'] === $method) {
                return true;
            }
        }
        return false;
    }
}

/**
 * Mock线索自动归档
 */
class MockLeadAutoArchive
{
    private array $leads = [];
    private array $callLog = [];
    
    public function setLeadForDomain(string $domain, $lead): void
    {
        $this->leads[$domain] = $lead;
    }
    
    public function archiveByBatchDomain(array $domains, bool $createIfNotExists = false): array
    {
        $this->callLog[] = [
            'method' => 'archiveByBatchDomain',
            'domains' => $domains,
            'createIfNotExists' => $createIfNotExists,
            'timestamp' => time()
        ];
        
        $result = [];
        foreach ($domains as $domain) {
            if (isset($this->leads[$domain])) {
                $result[$domain] = $this->leads[$domain];
            } elseif ($createIfNotExists) {
                // 创建Mock线索
                $leadData = AiSdrTestDataFactory::createLeadData([
                    'website' => 'https://' . $domain,
                    'company_name' => 'Mock Lead for ' . $domain
                ]);
                $result[$domain] = (object) $leadData;
            }
        }
        
        return $result;
    }
    
    public function getCallLog(): array
    {
        return $this->callLog;
    }
}
