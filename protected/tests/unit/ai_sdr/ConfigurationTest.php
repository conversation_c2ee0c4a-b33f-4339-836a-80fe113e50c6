<?php

namespace tests\unit\ai_sdr;

/**
 * 配置验证测试
 * 
 * 验证AI SDR测试环境配置是否正确
 */
class ConfigurationTest extends AiSdrTestCase
{
    /**
     * 测试基础配置
     */
    public function testBasicConfiguration()
    {
        // 验证测试环境标识
        $this->assertEquals('test', \Yii::app()->params['env']);
        
        // 验证测试客户ID和用户ID
        $this->assertEquals(999999, $this->getTestClientId());
        $this->assertEquals(888888, $this->getTestUserId());
        
        $this->assertTrue(true, 'Basic configuration test passed');
    }
    
    /**
     * 测试数据库连接
     */
    public function testDatabaseConnection()
    {
        try {
            // 测试PostgreSQL连接
            $pgDb = \PgActiveRecord::getDbByClientId($this->getTestClientId());
            $this->assertNotNull($pgDb, 'PostgreSQL connection should not be null');

            // 测试MySQL连接
            $mysqlDb = \ProjectActiveRecord::getDbByClientId($this->getTestClientId());
            $this->assertNotNull($mysqlDb, 'MySQL connection should not be null');
            
            $this->assertTrue(true, 'Database connections are working');
        } catch (\Exception $e) {
            $this->markTestSkipped('Database connection failed: ' . $e->getMessage());
        }
    }
    
    /**
     * 测试Redis连接
     */
    public function testRedisConnection()
    {
        try {
            $redis = \RedisService::cache();
            $testKey = 'ai_sdr_test_' . time();
            $testValue = 'test_value';
            
            // 测试设置和获取
            $redis->set($testKey, $testValue, 10);
            $retrievedValue = $redis->get($testKey);
            
            $this->assertEquals($testValue, $retrievedValue, 'Redis set/get should work');
            
            // 清理测试数据
            $redis->del($testKey);
            
            $this->assertTrue(true, 'Redis connection is working');
        } catch (\Exception $e) {
            $this->markTestSkipped('Redis connection failed: ' . $e->getMessage());
        }
    }
    
    /**
     * 测试测试数据工厂
     */
    public function testDataFactory()
    {
        // 重置序列号
        AiSdrTestDataFactory::resetSequence();
        
        // 测试任务数据创建
        $taskData = AiSdrTestDataFactory::createTaskData();
        $this->assertIsArray($taskData);
        $this->assertArrayHasKey('client_id', $taskData);
        $this->assertArrayHasKey('user_id', $taskData);
        $this->assertEquals(999999, $taskData['client_id']);
        
        // 测试客户档案数据创建
        $clientProfile = AiSdrTestDataFactory::createClientProfileData();
        $this->assertIsArray($clientProfile);
        $this->assertArrayHasKey('company_name', $clientProfile);
        $this->assertArrayHasKey('main_products', $clientProfile);
        
        // 测试买家档案数据创建
        $buyerProfile = AiSdrTestDataFactory::createBuyerProfileData();
        $this->assertIsArray($buyerProfile);
        $this->assertArrayHasKey('company_name', $buyerProfile);
        $this->assertArrayHasKey('public_homepage', $buyerProfile);
        
        $this->assertTrue(true, 'Data factory is working correctly');
    }
    
    /**
     * 测试Mock服务
     */
    public function testMockServices()
    {
        // 测试Mock时间服务
        $timeService = MockServices::createMockTimeService('2024-01-01 10:00:00');
        $this->assertEquals('2024-01-01 10:00:00', $timeService->now());
        $this->assertEquals(strtotime('2024-01-01 10:00:00'), $timeService->timestamp());
        
        // 测试Mock缓存服务
        $cacheService = MockServices::createMockCacheService();
        $cacheService->set('test_key', 'test_value');
        $this->assertEquals('test_value', $cacheService->get('test_key'));
        $this->assertTrue($cacheService->wasKeyAccessed('test_key'));
        
        // 测试Mock队列服务
        $queueService = MockServices::createMockQueueService();
        $mockJob = new \stdClass();
        $queueService->dispatch($mockJob);
        $this->assertEquals(1, $queueService->getDispatchedJobsCount());
        
        // 测试Mock AI服务
        $aiServices = MockServices::createMockAiServices();
        $aiServices->setQualityAnalysisResult(['test' => 'result']);
        $result = $aiServices->getQualityAnalysisResult();
        $this->assertArrayHasKey('test', $result);
        $this->assertTrue($aiServices->wasQualityAnalysisCalled());
        
        $this->assertTrue(true, 'Mock services are working correctly');
    }
    
    /**
     * 测试测试基类功能
     */
    public function testBaseClassFunctionality()
    {
        try {
            // 测试创建测试任务
            $task = $this->createTestTask([
                'email' => '<EMAIL>'
            ]);

            $this->assertNotNull($task);
            $this->assertNotNull($task->task_id);
            $this->assertEquals('<EMAIL>', $task->email);
            $this->assertEquals($this->getTestClientId(), $task->client_id);

            // 测试创建测试详情
            $detail = $this->createTestTaskDetail($task, [
                'lead_quality' => \common\library\ai_sdr\Constant::LEAD_QUALITY_HIGH
            ]);

            $this->assertNotNull($detail);
            $this->assertNotNull($detail->id);
            $this->assertEquals($task->task_id, $detail->task_id);
            $this->assertEquals(\common\library\ai_sdr\Constant::LEAD_QUALITY_HIGH, $detail->lead_quality);

            // 测试断言方法
            $this->assertTaskStatus($task->task_id, \common\library\ai_sdr\Constant::AI_SDR_TASK_STATUS_PROCESSING);
            $this->assertDetailStatus($detail->id, \common\library\ai_sdr\Constant::DETAIL_STATUS_ADD);
            $this->assertDetailStage($detail->id, \common\library\ai_sdr\Constant::AI_SDR_STAGE_DIG);

            $this->assertTrue(true, 'Base class functionality is working correctly');
        } catch (\Exception $e) {
            $this->markTestSkipped('Database operations failed: ' . $e->getMessage());
        }
    }
    
    /**
     * 测试数据清理功能
     */
    public function testDataCleanup()
    {
        try {
            // 创建一些测试数据
            $task1 = $this->createTestTask(['email' => '<EMAIL>']);
            $task2 = $this->createTestTask(['email' => '<EMAIL>']);

            $detail1 = $this->createTestTaskDetail($task1);
            $detail2 = $this->createTestTaskDetail($task2);

            $record1 = $this->createTestTaskRecord($detail1);
            $record2 = $this->createTestTaskRecord($detail2);

            // 验证数据已创建
            $this->assertFalse($task1->isNew());
            $this->assertFalse($detail1->isNew());
            $this->assertFalse($record1->isNew());

            // 数据清理会在tearDown中自动执行
            $this->assertTrue(true, 'Data cleanup test completed');
        } catch (\Exception $e) {
            $this->markTestSkipped('Database operations failed: ' . $e->getMessage());
        }
    }
    
    /**
     * 测试常量定义
     */
    public function testConstants()
    {
        // 验证任务状态常量
        $this->assertEquals(0, \common\library\ai_sdr\Constant::AI_SDR_TASK_STATUS_DRAFT);
        $this->assertEquals(1, \common\library\ai_sdr\Constant::AI_SDR_TASK_STATUS_PROCESSING);
        $this->assertEquals(2, \common\library\ai_sdr\Constant::AI_SDR_TASK_STATUS_PAUSED);
        $this->assertEquals(3, \common\library\ai_sdr\Constant::AI_SDR_TASK_STATUS_FINISHED);
        
        // 验证阶段常量
        $this->assertEquals(0, \common\library\ai_sdr\Constant::AI_SDR_STAGE_DIG);
        $this->assertEquals(1, \common\library\ai_sdr\Constant::AI_SDR_STAGE_REACHABLE);
        $this->assertEquals(2, \common\library\ai_sdr\Constant::AI_SDR_STAGE_MARKETING);
        $this->assertEquals(3, \common\library\ai_sdr\Constant::AI_SDR_STAGE_EFFECTIVE);
        $this->assertEquals(4, \common\library\ai_sdr\Constant::AI_SDR_STAGE_HIGHVALUE);
        
        // 验证详情状态常量
        $this->assertEquals(0, \common\library\ai_sdr\Constant::DETAIL_STATUS_ADD);
        $this->assertEquals(1, \common\library\ai_sdr\Constant::DETAIL_STATUS_LABEL);
        $this->assertEquals(2, \common\library\ai_sdr\Constant::DETAIL_STATUS_BACKGROUND_CHECKING);
        $this->assertEquals(4, \common\library\ai_sdr\Constant::DETAIL_STATUS_VALIDATE_CONTACTS);
        $this->assertEquals(5, \common\library\ai_sdr\Constant::DETAIL_STATUS_CREATE_MARKETING_PLAN);
        $this->assertEquals(6, \common\library\ai_sdr\Constant::DETAIL_STATUS_EXECUTE_MARKETING_PLAN);
        $this->assertEquals(-1, \common\library\ai_sdr\Constant::DETAIL_STATUS_ERROR);
        
        $this->assertTrue(true, 'Constants are properly defined');
    }
}
