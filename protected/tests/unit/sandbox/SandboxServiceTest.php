<?php

namespace tests\unit\sandbox;

use common\library\account\service\DbService;
use common\library\sandbox\DbSync;
use common\library\sandbox\SandboxConstant;
use common\library\sandbox\SandboxDbService;
use common\library\sandbox\Api;

class SandboxServiceTest extends \FunctionalTestCase
{
    public $implementUser = ********;
    public $implementClient = 14059;
    public static $sandboxClient = 14119;
    public $sandboxUser = ********;

    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();
    }

    protected function tearDown(): void
    {
        $db = \Yii::app()->db;
        $db->createCommand("delete from tbl_client_sandbox where client_id = " . self::$sandboxClient)->execute();
        parent::tearDownAfterClass();
    }

    public function testGenerateSandboxId()
    {
        $this->assertEquals(1, 1);
    }

    public function testGetAllSetNoList()
    {
        $setNoList = (new Api())->getAllSetNo();
        $this->assertEquals([1], $setNoList);
    }

    public function testFindExistSandboxByClientAndUser()
    {
        $sb = (new Api())->findOne(static::$sandboxClient, $this->sandboxUser);
        $this->assertNull($sb);
    }

    public function testFindOneSetNo()
    {
        $setNo = (new Api())->assignSetNo(static::$sandboxClient, $this->sandboxUser);
        $this->assertEquals(1, $setNo);
    }

    /**
     * @test
     */
    public function it_can_apply_one_sandbox_by_implement()
    {
        $sandbox = (new Api())->create(static::$sandboxClient, $this->sandboxUser, $this->implementClient, $this->implementUser);
        $this->assertEquals($sandbox->client_id, static::$sandboxClient);
        $this->assertEquals($sandbox->user_id, 0);
        $this->assertEquals($sandbox->implement_client, $this->implementClient);
        $this->assertEquals($sandbox->implement_user, $this->implementUser);
        $this->assertEquals($sandbox->implement_status, SandboxConstant::STATUS_OF_INIT);
        $this->assertEquals($sandbox->apply_status, SandboxConstant::APPLY_STATUS_NONE);
    }

    /**
     * @test
     */
    public function it_can_apply_one_sandbox_by_user()
    {
        $this->loginUser($this->sandboxUser);
        $sandbox = (new Api())->create(static::$sandboxClient);
        $this->assertEquals($sandbox->client_id, static::$sandboxClient);
        $this->assertEquals($sandbox->user_id, 0);
        $this->assertEquals($sandbox->implement_client, 0);
        $this->assertEquals($sandbox->implement_user, 0);
        $this->assertEquals($sandbox->implement_status, SandboxConstant::STATUS_OF_NONE);
        $this->assertEquals($sandbox->apply_status, SandboxConstant::APPLY_STATUS_USER_APPLIED);
    }

    /**
     * @test
     */
    public function implement_user_accept_user_applied()
    {
        $this->loginUser($this->sandboxUser);
        $sandbox = (new Api())->create(static::$sandboxClient);
        $this->assertEquals($sandbox->client_id, static::$sandboxClient);
        $this->assertEquals($sandbox->user_id, 0);
        $this->assertEquals($sandbox->implement_client, 0);
        $this->assertEquals($sandbox->implement_user, 0);
        $this->assertEquals($sandbox->implement_status, SandboxConstant::STATUS_OF_NONE);
        $this->assertEquals($sandbox->apply_status, SandboxConstant::APPLY_STATUS_USER_APPLIED);

        $sandbox = (new Api())->create(static::$sandboxClient, $this->sandboxUser, $this->implementClient, $this->implementUser);
        $this->assertEquals($sandbox->client_id, static::$sandboxClient);
        $this->assertEquals($sandbox->user_id, 0);
        $this->assertEquals($sandbox->implement_client, $this->implementClient);
        $this->assertEquals($sandbox->implement_user, $this->implementUser);
        $this->assertEquals($sandbox->implement_status, SandboxConstant::STATUS_OF_PROCESS);
        $this->assertEquals($sandbox->apply_status, SandboxConstant::APPLY_STATUS_USER_APPLIED);
    }

    /**
     * @test
     */
    public function user_applied_after_implement_apply()
    {
        $this->loginUser($this->sandboxUser);

        $sandbox = (new Api())->create(static::$sandboxClient, $this->sandboxUser, $this->implementClient, $this->implementUser);
        $this->assertEquals($sandbox->client_id, static::$sandboxClient);
        $this->assertEquals($sandbox->user_id, 0);
        $this->assertEquals($sandbox->implement_client, $this->implementClient);
        $this->assertEquals($sandbox->implement_user, $this->implementUser);
        $this->assertEquals($sandbox->implement_status, SandboxConstant::STATUS_OF_INIT);
        $this->assertEquals($sandbox->apply_status, SandboxConstant::STATUS_OF_NONE);

        $sandbox = (new Api())->create(static::$sandboxClient);
        $this->assertEquals($sandbox->client_id, static::$sandboxClient);
        $this->assertEquals($sandbox->user_id, 0);
        $this->assertEquals($sandbox->implement_client, $this->implementClient);
        $this->assertEquals($sandbox->implement_user, $this->implementUser);
        $this->assertEquals($sandbox->implement_status, SandboxConstant::STATUS_OF_PROCESS);
        $this->assertEquals($sandbox->apply_status, SandboxConstant::APPLY_STATUS_USER_APPLIED);
    }

    public function testLoginByImplement()
    {
        $sandbox = (new Api())->create(static::$sandboxClient, $this->sandboxUser, $this->implementClient, $this->implementUser);
        $this->assertEquals($sandbox->client_id, static::$sandboxClient);
        $this->assertEquals($sandbox->user_id, 0);
        $this->assertEquals($sandbox->implement_client, $this->implementClient);
        $this->assertEquals($sandbox->implement_user, $this->implementUser);
        $this->assertEquals($sandbox->implement_status, SandboxConstant::STATUS_OF_INIT);
        $this->assertEquals($sandbox->apply_status, SandboxConstant::STATUS_OF_NONE);


        $sandboxService = new Api();
        $sandboxService->loginSandbox($sandbox->id, $this->implementClient, $this->implementUser);
        $sandboxId = Api::encodeSandboxId(static::$sandboxClient, 1);
        $_SERVER['HTTP_HOST'] = "{$sandboxId}.sandbox.dev.xiaoman.cn";
        $loginData = Api::getLoginData();
        $this->assertEquals(static::$sandboxClient, $loginData['clientId']);
        $this->assertEquals($this->sandboxUser, $loginData['userId']);
    }

}
