<?php
/**
 * User: beenzhang
 * Date: 2022/9/27
 * Email: been<<EMAIL>>
 */

namespace common\tests\unit\version_release;

use common\modules\prometheus\library\jobs\nginx_setting\EnableNginxLuaSettingJob;
use common\modules\prometheus\library\nginx_configuration\apply\apply_batch\NginxApplyBatch;
use common\modules\prometheus\library\nginx_configuration\apply\NginxLuaApply;
use common\modules\prometheus\library\nginx_configuration\Constant;
use common\modules\prometheus\library\nginx_configuration\envoy\EnvoyFilterService;
use common\modules\prometheus\library\nginx_configuration\NginxRuleBuilder;

class NginxApplyTest extends \WebFunctionalTestCase
{
    public function testConfigInfo()
    {
        $dc_id = 1;
        //$dc_id = 2;
        echo json_encode(\common\modules\prometheus\library\nginx_configuration\envoy\EnvoyFilterService::getRemoteEnvoyConfigInfoBySystem($dc_id,'crm'));
    }

    public function TestConfigInfo1()
    {
        $dc_id = 1;
        $system = 'crm';
        $systemReleaseConfig = [
            'crm' => [
                'default_cluster_version' => 'prod',
                'prod' => [],
                'grey' => [],
                'dev' => [],
            ],
        ];

        $envoyConfig = EnvoyFilterService::getRemoteEnvoyConfigInfoBySystem($dc_id, $system);

        $config = [
            'default_cluster_version' => $envoyConfig['defaultClusterVersion'],
            'prod' => 0,
            'grey' => 0,
            'dev' => 0,
        ];

        foreach ($envoyConfig['clusterVersionSelectors'] as $versionSelector)
        {
            $env = $versionSelector['clusterVersion'];
            $config[$env] = 1;
        }

        var_dump($config);
    }

    public function testPreview()
    {
        $dc_id = 1;
        $releaseRule = ['grey_part_client' => ['client_tail_num' => [0,1,2]]];
        $releaseRule = ['prod_all' => 1];
        $releaseRule = ['grey_all' => 1];
        $releaseRule = ['grey_all_prod_part' => ['client_tail_num' => [0,1,2]]];
        $system = 'crm';
        $builder = new NginxRuleBuilder($dc_id, $system);
        $builder->setReleaseRule($releaseRule);
        $builder->setCompareVar(\common\modules\prometheus\library\nginx_configuration\Constant::MATCH_RULE_CLIENT_ID[$system]);
        $builder->isIncrement(true);
        var_dump($builder->preview());
    }

    public function testApply()
    {
        $dcId = 1;
        $systemList = ['crm','callback'];
        $systemList = ['lighthouse'];
        $cluster = 'host';


        //异步执行，提前生成batchId
        $batch = new NginxApplyBatch();
        $batch->batch_union_id = \common\components\PrometheusModel::produceAutoIncrementId();
        $batch->save();

//        $key = array_map(function($v) {
//            return Constant::PREFIX_ENABLE_GREY_SETTING.$v;
//        }, $systemList);
//        $redis = \RedisService::cache();
//        $redis->del($key);
//        die;
        $apply = new NginxLuaApply($dcId, $cluster);
        $apply->setCancelGrey(1);
        $apply->apply($systemList,$batch->batch_id);
    }

    public function testJob()
    {
        $batch = new NginxApplyBatch();
        $batch->batch_union_id = \common\components\PrometheusModel::produceAutoIncrementId();
        $batch->save();

        $job = new EnableNginxLuaSettingJob(
            2,
            'host',
            ['crm'],
            $batch->batch_id,
            1423,
            null
        );
        $job->handle();
    }

    public function testUpdateReleasePlanStatus()
    {
        $dcId = 1;
        $iterationId = 229;
        $releasePlanId = 1377;
        $union_id = \common\components\PrometheusModel::produceAutoIncrementId(); //用户操作维度的唯一标识
        foreach (["host", "k8s-idc-dev-beta"] as $cluster) {
            //异步执行，提前生成batchId
            $batch = new NginxApplyBatch();
            $batch->batch_union_id = $union_id;
            $batch->dc_id = $dcId;
            $batch->cluster = $cluster;
            $batch->iteration_id = $iterationId;
            $batch->release_plan_id = $releasePlanId;

            if ($cluster == "host") {
                $batch->status = NginxApplyBatch::APPLY_STATUS_INIT;
            } else {
                $batch->status = NginxApplyBatch::APPLY_STATUS_FAIL;
            }

            $batch->save();
        }
    }

    public function testDispatch()
    {
        $releasePlanId = 1903;
        $execute_step = \common\modules\prometheus\library\release\Constant::PLAN_DISPATCH_RULE_KEY_ENABLE_NGINX;
        $param = '{"incremental_flag":0,"system":["crm"],"dc_id":[1]}';
        $dispatcher = new \common\modules\prometheus\library\release\dispatcher\ReleasePlanDispatcher($releasePlanId);
        $dispatcher->dispatch($execute_step, $param);
    }
}