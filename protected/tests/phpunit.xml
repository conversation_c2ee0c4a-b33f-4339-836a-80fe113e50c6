<phpunit bootstrap="bootstrap.php"
         colors="true"
         convertErrorsToExceptions="true"
         convertNoticesToExceptions="true"
         convertWarningsToExceptions="true"
         stopOnFailure="true">
    <testsuites>
        <testsuite name="customer">
            <directory>unit/customer</directory>
        </testsuite>
        <testsuite name="group">
            <directory>unit/group</directory>
        </testsuite>
        <testsuite name="product">
            <directory>unit/product</directory>
        </testsuite>
        <testsuite name="invoice">
            <directory>unit/invoice</directory>
        </testsuite>
        <testsuite name="setting">
            <directory>functional/setting</directory>
        </testsuite>
        <testsuite name="ai_sdr_unit">
            <directory>unit/ai_sdr</directory>
        </testsuite>
        <testsuite name="ai_sdr_functional">
            <directory>functional/ai_sdr</directory>
        </testsuite>
        <testsuite name="ai_sdr_all">
            <directory>unit/ai_sdr</directory>
            <directory>functional/ai_sdr</directory>
        </testsuite>
    </testsuites>
    <filter>
        <whitelist processUncoveredFilesFromWhitelist="true">
            <directory suffix=".php">../library/opportunity</directory>
            <directory suffix=".php">../library/ai_sdr</directory>
        </whitelist>
    </filter>
</phpunit>