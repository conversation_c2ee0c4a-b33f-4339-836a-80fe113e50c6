<?php

/**
 * This file is part of xiaoman-crm.
 *
 * Copyright © 2012 - 2019 Xiaoman.All Rights Reserved.
 *
 * Created by <PERSON><PERSON><PERSON><PERSON> <she<PERSON><PERSON><PERSON>@xiaoman.cn>, on 2019/05/30.
 */

use AliyunMNS\Client;
use AliyunMNS\Exception\MnsException;
use AliyunMNS\Queue;
use AliyunMNS\Requests\SendMessageRequest;
use AliyunMNS\Requests\BatchSendMessageRequest;

class MnsReportClient extends CApplicationComponent
{

    public $accessId;

    public $accessSecret;

    public $endPoint;

    public $queueName;

    /** @var Queue */
    protected $queue;

    protected $error = '';

    public function init()
    {
        if ($this->getIsInitialized()) {
            return;
        }
        $client = new Client($this->endPoint, $this->accessId, $this->accessSecret);
        $this->queue = $client->getQueueRef($this->queueName);
    }

    public function getIsInitialized()
    {
        return !is_null($this->queue);
    }

    public function sendMessage($message)
    {
        $request = new SendMessageRequest($message);

        try {
            $this->queue->sendMessage($request);
            return true;
        } catch (MnsException $e) {
            $this->error .= $e;
            // 添加错误上报
            \common\library\report\error\ErrorReport::phpError(new \CExceptionEvent(null, $e), $e->getTrace(), 0);
            LogUtil::error($this->error);
            return false;
        }
    }

    public function receiveMessage()
    {
        try {
            $resp = $this->queue->receiveMessage(30);
            $receiptHandle = $resp->getReceiptHandle();
            $body = $resp->getMessageBody();
            $this->queue->deleteMessage($receiptHandle);
            return $body;
        } catch (MnsException $e) {
            $this->error .= $e;
            return false;
        }
    }

    public function getError()
    {
        return $this->error;
    }

    public function batchSendMessage(array $messages)
    {
        $request = new BatchSendMessageRequest($messages);
        try {
            $this->queue->batchSendMessage($request);
            return true;
        } catch (MnsException $e) {
            $this->error .= $e;
            // 添加错误上报
            \common\library\report\error\ErrorReport::phpError(new \CExceptionEvent(null, $e), $e->getTrace(), 0);
            LogUtil::error($this->error);
            LogUtil::error('没有同步成功的数据:'.implode($messages));
            return false;
        }

    }


}