<?php
/**
 * Created by PhpStorm.
 * User: beenzhang
 * Date: 2021/1/5
 * Time: 7:40 PM
 */

namespace common\components;

use CEvent;
use CException;
use CModelEvent;
use common\library\search\ESCriteria;
use Elasticsearch\ClientBuilder;
use Exception;
use User;
use Yii;

class ElasticSearchActiveRecordV7 extends \CModel
{

    protected static $_connection;
    protected static $cacheElasticSearch = array();
    protected static $_connectionPool;
    protected static $cacheIndexMap;
    private $_id;
    private $_score;
    private $_version;
    private $_highlight;
    private $_explanation;
    protected $_routing;

    private static $_models = array();
    private $_new = false;
    private $_partial = false;
    private $_projected_fields = array();
    private $lastError;
    private $_errors = array();
    private $_attributes = array();

    protected $timeout = 5;
    protected $connect_timeout = 1;

    public function __get($name)
    {
        if (isset($this->_attributes[$name])) {
            return $this->_attributes[$name];
        }

        try {
            return parent::__get($name);
        } catch (CException $e) {
            $getter = 'get' . $name;
            if (method_exists($this, $getter)) {
                throw $e;
            } elseif (strncasecmp($name, 'on', 2) === 0 && method_exists($this, $name)) {
                throw $e;
            }
            return null;
        }
    }

    public function __set($name, $value)
    {
        try {
            return parent::__set($name, $value);
        } catch (CException $e) {
            return $this->setAttribute($name, $value);
        }
    }

    public function __isset($name)
    {
        if (isset($this->_attributes[$name])) {
            return true;
        }

        return parent::__isset($name);
    }

    public function __unset($name)
    {
        if (isset($this->_attributes[$name])) {
            unset($this->_attributes[$name]);
        } else {
            parent::__unset($name);
        }
    }

    public function __call($name, $parameters)
    {
        return parent::__call($name, $parameters);
    }

    public function __construct($scenario = 'insert')
    {

        if ($scenario === null) {
            return;
        }
        $this->setScenario($scenario);
        $this->setIsNewRecord(true);
        $this->init();
        $this->attachBehaviors($this->behaviors());
        $this->afterConstruct();

    }

    public function init()
    {
        return true;
    }

    public function attributeNames()
    {
        $cols = array_keys($this->_attributes);
        return $cols !== null ? $cols : array();
    }

    public function hasAttribute($name)
    {
        $attrs = $this->_attributes;
        $fields = $this->getProjectedFields();
        return isset($attrs[$name]) || isset($fields[$name]) || property_exists($this, $name) ? true : false;
    }

    public function setAttribute($name, $value)
    {
        if (property_exists($this, $name)) {
            $this->$name = $value;
        } else {
            $this->_attributes[$name] = $value;
        }
        return true;
    }

    public function getAttribute($name)
    {
        if (property_exists($this, $name)) {
            return $this->$name;
        }
        if (isset($this->_attributes[$name])) {
            return $this->_attributes[$name];
        }
        return null;
    }

    public function getAttributes($names = true)
    {
        $attributes = $this->_attributes;
        $fields = $this->getProjectedFields();
        if (is_array($fields)) {
            foreach ($fields as $name) {
                $attributes[$name] = $this->$name;
            }
        }
        if (!is_array($names)) {
            return $attributes;
        }
        $attrs = array();
        foreach ($names as $name) {
            if (property_exists($this, $name)) {
                $attrs[$name] = $this->$name;
            } else {
                $attrs[$name] = isset($attributes[$name]) ? $attributes[$name] : null;
            }
        }
        return $attrs;
    }

    public function setAttributes($values, $safeOnly = true)
    {
        if (!is_array($values)) {
            return;
        }
        foreach ($values as $name => $value) {

            $this->$name = $value;
        }
    }

    public function unsetAttributes($names = null)
    {
        if ($names === null) {
            $names = $this->attributeNames();
        }
        foreach ($names as $name) {
            $this->$name = null;
        }
    }


    /**
     * 返回主键key
     * @return string
     */
    public function primaryKey()
    {
        return 'id';
    }

    public function setAttributeErrors($attribute, $errors)
    {
        $this->_errors[$attribute] = $errors;
    }

    public function hasErrors($attribute = null)
    {
        if ($attribute === null) {
            return !empty($this->_errors);
        }
        return isset($this->_errors[$attribute]);
    }


    public function getErrors($attribute = null)
    {
        if ($attribute === null) {
            return $this->_errors;
        }
        $attribute = trim(strtr($attribute, '][', '['), ']');
        if (strpos($attribute, '[') !== false) {
            $prev = null;
            foreach (explode('[', $attribute) as $piece) {
                if ($prev === null && isset($this->errors[$piece])) {
                    $prev = $this->_errors[$piece];
                } elseif (isset($prev[$piece])) {
                    $prev = is_array($prev) ? $prev[$piece] : $prev->$piece;
                }
            }
            return $prev === null ? array() : $prev;
        }
        return isset($this->_errors[$attribute]) ? $this->_errors[$attribute] : array();
    }


    public function getError($attribute)
    {
        $attribute = trim(strtr($attribute, '][', '['), ']');
        if (strpos($attribute, '[') === false) {
            return isset($this->_errors[$attribute]) ? reset($this->_errors[$attribute]) : null;
        }
        $prev = null;
        foreach (explode('[', $attribute) as $piece) {
            if ($prev === null && isset($this->_errors[$piece])) {
                $prev = $this->_errors[$piece];
            } elseif (isset($prev[$piece])) {
                $prev = is_array($prev) ? $prev[$piece] : $prev->$piece;
            }
        }
        return $prev === null ? null : reset($prev);
    }

    public function addError($attribute, $error)
    {
        $this->_errors[$attribute][] = $error;
    }

    public function addErrors($errors)
    {
        foreach ($errors as $attribute => $error) {
            if (is_array($error)) {
                foreach ($error as $e) {
                    $this->addError($attribute, $e);
                }
            } else {
                $this->addError($attribute, $error);
            }
        }
    }

    public function clearErrors($attribute = null)
    {
        if ($attribute === null) {
            $this->_errors = array();
        } else {
            unset($this->_errors[$attribute]);
        }
    }

    public function setIsPartial($partial)
    {
        $this->_partial = $partial;
    }

    public function getIsPartial()
    {
        return $this->_partial;
    }

    /**
     * @return \Elasticsearch\Client
     */
    public static function getDbConnection()
    {
        if (self::$_connection === null) {

            $params = Yii::app()->params['elastic_search_v7'];

            $connection = ClientBuilder::fromConfig($params);
            self::$_connection = new ElasticSearchClient($connection);
        }

        return self::$_connection;
    }

    /**
     * @return bool
     */
    public function clean()
    {
        $this->_attributes = array();

        return true;
    }

    /**
     * 返回主键值
     * @param null $value
     * @return mixed|null
     */
    public function getPrimaryKey($value = null)
    {
        if ($value === null) {
            $value = $this->{$this->primaryKey()};
        }

        return intval($value);
    }

    public function getIsNewRecord()
    {
        return $this->_new;
    }

    public function setIsNewRecord($value)
    {
        $this->_new = (bool)$value;
    }

    public function getProjectedFields()
    {
        return $this->_projected_fields;
    }

    public function setProjectedFields(array $fields)
    {
        $this->_projected_fields = $fields;
    }

    /**
     * @param string $className
     * @return ElasticSearchActiveRecordV7|mixed
     */
    public static function model($className = __CLASS__)
    {
        if (isset(self::$_models[$className])) {
            return self::$_models[$className];
        }

        /** @var self $model */
        $model = self::$_models[$className] = new $className(null);
        $model->attachBehaviors($model->behaviors());
        return $model;
    }

    /**
     * @param $document
     * @return ElasticSearchActiveRecord
     */
    protected function instantiate($document)
    {
        $class = get_class($this);
        return new $class(null);
    }

    /**
     * @param CEvent $event
     */
    public function onBeforeSave($event)
    {
        $this->raiseEvent('onBeforeSave', $event);
    }

    /**
     * @param CEvent $event
     */
    public function onAfterSave($event)
    {
        $this->raiseEvent('onAfterSave', $event);
    }

    /**
     * @param CEvent $event
     */
    public function onBeforeDelete($event)
    {
        $this->raiseEvent('onBeforeDelete', $event);
    }

    /**
     * @param CEvent $event
     */
    public function onAfterDelete($event)
    {
        $this->raiseEvent('onAfterDelete', $event);
    }

    /**
     * @param CEvent $event
     */
    public function onBeforeFind($event)
    {
        $this->raiseEvent('onBeforeFind', $event);
    }

    /**
     * @param CEvent $event
     */
    public function onAfterFind($event)
    {
        $this->raiseEvent('onAfterFind', $event);
    }

    /**
     * @return bool
     */
    protected function beforeSave()
    {
        if ($this->hasEventHandler('onBeforeSave')) {
            $event = new CModelEvent($this);
            $this->onBeforeSave($event);
            return $event->isValid;
        }
        return true;
    }

    protected function afterSave()
    {
        if ($this->hasEventHandler('onAfterSave')) {
            $this->onAfterSave(new CEvent($this));
        }
    }

    /**
     * @return bool
     */
    protected function beforeDelete()
    {
        if ($this->hasEventHandler('onBeforeDelete')) {
            $event = new CModelEvent($this);
            $this->onBeforeDelete($event);
            return $event->isValid;
        }
        return true;
    }

    protected function afterDelete()
    {
        if ($this->hasEventHandler('onAfterDelete')) {
            $this->onAfterDelete(new CEvent($this));
        }
    }

    protected function beforeFind()
    {
        if ($this->hasEventHandler('onBeforeFind')) {
            $event = new CModelEvent($this);
            $this->onBeforeFind($event);
        }
    }

    protected function afterFind()
    {
        if ($this->hasEventHandler('onAfterFind')) {
            $this->onAfterFind(new CEvent($this));
        }
    }

    /**
     * 保存
     * @param bool $runValidation
     * @param null $attributes
     * @return bool
     */
    public function save($runValidation = true, $attributes = null)
    {

        if (!$runValidation || $this->validate($attributes)) {
            return $this->getIsNewRecord() ? $this->insert($attributes) : $this->update($attributes);
        }
        return false;
    }

    /**
     * @param $attributes
     * @return array
     * @throws Exception
     */
    public function saveAttributes($attributes)
    {
        if ($this->getIsNewRecord()) {
            throw new Exception(Yii::t('yii', 'The active record cannot be updated because it is new.'));
        }

        $this->trace(__FUNCTION__);

        $values = array();
        foreach ($attributes as $name => $value) {
            if (is_integer($name)) {
                $v = $this->$value;
                if (is_array($this->$value)) {
                    $v = $this->$value;
                }
                $values[$value] = $v;
            } else {
                $values[$name] = $this->$name = $value;
            }
        }
        if (!isset($this->{$this->primaryKey()}) || $this->getPrimaryKey() === null) {
            throw new Exception(Yii::t('yii', 'The active record cannot be updated because its _id is not set!'));
        }

        $params = [
            'index' => $this->index(),
            'id' => $this->{$this->primaryKey()},
            'body' => [
                'doc' => $values
            ],
        ];

        $result = $this->lastError = $this->getDbConnection()->update($params);

        if (isset($result['_version'])) {
            $this->_version = $result['_version'];
        }

        return $result;

    }


    /**
     * @param null $attributes
     * @return bool
     * @throws Exception
     */
    public function insert($attributes = null)
    {
        if (!$this->getIsNewRecord()) {
            throw new Exception(Yii::t('yii', 'The active record cannot be inserted to database because it is not new.'));
        }
        if (!$this->beforeSave()) {
            return false;
        }
        $this->trace(__FUNCTION__);
        if ($attributes !== null) {
            $body = $this->getAttributes($attributes);
        } else {
            $body = $this->getAttributes();
        }

        if (!isset($this->{$this->primaryKey()})) {
            throw  new Exception('id not empty!');
        }

        $params = [
            'index' => $this->index(),
            'id' => $this->{$this->primaryKey()},
            'body' => $body,
        ];

        $result = $this->lastError = $this->getDbConnection()->index($params);

        if (isset($result['_id'])) {
            $this->_id = $result['_id'];
        }

        if (isset($result['_version'])) {
            $this->_version = $result['_version'];
        }

        $this->_score = null;

        if ($this->lastError) {
            $this->afterSave();
            $this->setIsNewRecord(false);
            $this->setScenario('update');
            return true;
        }
        return false;
    }

    /**
     * 更新
     * @param null $attributes
     * @return bool
     * @throws Exception
     */
    public function update($attributes = null)
    {
        if ($this->getIsNewRecord()) {
            throw new Exception(Yii::t('yii', 'The active record cannot be updated because it is new.'));
        }
        if (!$this->beforeSave()) {
            return false;
        }
        $this->trace(__FUNCTION__);
        if ($this->getPrimaryKey() === null) { // An _id is required
            throw new Exception(Yii::t('yii', 'The active record cannot be updated because it has primary key set.'));
        }
        if ($attributes !== null) {
            $body = $this->getAttributes($attributes);
        } elseif ($this->getIsPartial()) {
            foreach ($this->_projected_fields as $field => $v) {
                $body[$field] = $this->$field;
            }
            $body = $this->getAttributes($attributes);
        } else {
            $body = $this->getAttributes();
        }

        $params = [
            'index' => $this->index(),
            'id' => $this->{$this->primaryKey()},
            'body' => [
                'doc' => $body
            ],
        ];

        $result = $this->lastError = $this->getDbConnection()->update($params);

        if (isset($result['_version'])) {
            $this->_version = $result['_version'];
        }


        $this->afterSave();
        return true;
    }


    public function delete()
    {
        if ($this->getIsNewRecord()) {
            throw new Exception(Yii::t('yii', 'The active record cannot be deleted because it is new.'));
        }
        $this->trace(__FUNCTION__);
        if (!$this->beforeDelete()) {
            return false;
        }
        $result = $this->deleteByPk($this->getPrimaryKey());
        $this->afterDelete();
        return $result;
    }

    /**
     * @param array $filter
     * @return bool
     */
    public function exists($filter = array())
    {
        $this->trace(__FUNCTION__);

        return $this->find($filter) !== null;

    }

    public function equals($record)
    {
        return $this->collectionName() === $record->collectionName() && (string)$this->getPrimaryKey() === (string)$record->getPrimaryKey();
    }

    public function getLatest()
    {
        $c = $this->find(array($this->primaryKey() => $this->getPrimaryKey()));
        if ($c->count() <= 0) {
            return null;
        }
        foreach ($c as $row) {
            return $this->populateRecord($row);
        }
        return null;
    }

    /**
     * @param $pk
     * @return ElasticSearchActiveRecord|null
     */
    public function findByPk($pk)
    {
        $this->trace(__FUNCTION__);
        return $this->find(array($this->primaryKey() => $this->getPrimaryKey($pk)));
    }


    /**
     * @param array $filter
     * @return array|null
     */
    public function findAll($filter = [])
    {
        return $this->getDbConnection()->mget($filter);
    }

    /**
     * @param array $filter
     * @return array|null
     */
    public function findAllByAttributes($filter = [])
    {
        return $this->findAll($filter);
    }

    /**
     * @param $pk
     * @return ElasticSearchActiveRecord[]|ElasticSearchActiveRecord|null
     * @throws Exception
     */
    public function findAllByPk($pk)
    {
        if (is_string($pk) || is_numeric($pk)) {
            return $this->find(array($this->primaryKey() => $this->getPrimaryKey($pk)));
        }
        if (!is_array($pk)) {
            throw new Exception(Yii::t('yii', 'Set an incorrect primary key.'));
        }

        $params = [
            'index' => $this->index(),
            'body' => [
                'ids' => $pk
            ]
        ];

        $result = $this->getDbConnection()->mget($params);

        if ($result === false || $result === null) {
            return null;
        }

        return $this->populateRecords($result);

    }

    /**
     * @param array $filter
     * @return null| ElasticSearchActiveRecord
     */
    public function find($filter = [])
    {
        $this->trace(__FUNCTION__);
        $this->beforeFind(); // Apparently this is applied before even scopes...

        $params = [
            'index' => $this->index(),
            'routing' => $this->routing(),
        ];

        $params = array_merge($filter, $params);
        $result = $this->getDbConnection()->get($params);

        if ($result === false || $result === null) {
            return null;
        }

        return $this->populateRecord($result);

    }

    public function deleteByPk($pk)
    {
        $this->trace(__FUNCTION__);

        $filter[$this->primaryKey()] = $this->getPrimaryKey($pk);

        $params = [
            'index' => $this->index(),
            'id' => $this->getPrimaryKey($pk),
        ];
        $result = $this->getDbConnection()->delete($params);

        return $result;
    }

    public function deleteAll($filter = [])
    {
        $this->trace(__FUNCTION__);

        $params = [
            'index' => $this->index(),
            'body' => $filter
        ];
        $result = $this->getDbConnection()->deleteByQuery($params);
        return $result;
    }

    public function updateByPk($pk, $updateDoc = [])
    {
        $this->trace(__FUNCTION__);

        $params = [
            'index' => $this->index(),
            'id' => $this->getPrimaryKey($pk),
            'body' => [
                'doc' => $updateDoc
            ],
        ];
        $result = $this->getDbConnection()->update($params);

        return $result;
    }

    /**
     * @param array $filter
     * @param array $updateDoc
     * @return array
     */
    public function updateAll($filter = [], $updateDoc = [])
    {
        $this->trace(__FUNCTION__);

        //todo
        $params = [
            'index' => $this->index(),
            'body' => $filter
        ];

        $result = $this->getDbConnection()->updateByQuery($params);
        return $result;
    }

    public function refresh()
    {
        $this->trace(__FUNCTION__);
        if (
            !$this->getIsNewRecord() &&
            ($record = $this->find(array($this->primaryKey() => $this->getPrimaryKey()))) !== null
        ) {
            $this->clean();
            foreach ($record as $name => $column) {
                $this->$name = $record[$name];
            }
            return true;
        }
        return false;
    }

    /**
     * @return mixed
     */
    public function getLastError()
    {
        return $this->lastError;
    }

    public function trace($func)
    {
        Yii::trace(get_class($this) . '.' . $func . '()', 'ElasticSearchRecord');
    }

    /**
     * @param $attributes
     * @param bool $callAfterFind
     * @param bool $partial
     * @return ElasticSearchActiveRecord|null
     */
    public function populateRecord($attributes, $callAfterFind = true, $partial = false)
    {
        if ($attributes === false || $attributes === null) {
            return null;
        }

        $record = $this->instantiate($attributes);
        $record->setScenario('update');
        $record->setIsNewRecord(false);
        $record->init();
        $labels = array();
        foreach ($attributes as $name => $value) {
            $labels[$name] = 1;
            $record->setAttribute($name, $value);
        }
        if ($partial) {
            $record->setIsPartial(true);
            $record->setProjectedFields($labels);
        }
        $record->attachBehaviors($record->behaviors());
        if ($callAfterFind) {
            $record->afterFind();
        }
        return $record;
    }

    /**
     * @param array $data
     * @param bool $callAfterFind
     * @param null $index
     * @return ElasticSearchActiveRecord[]
     */
    public function populateRecords(array $data, $callAfterFind = true, $index = null)
    {
        $records = array();
        foreach ($data as $attributes) {
            if (($record = $this->populateRecord($attributes, $callAfterFind)) !== null) {
                if ($index === null) {
                    $records[] = $record;
                } else {
                    $records[$record->$index] = $record;
                }
            }
        }
        return $records;
    }

    public static function setConnection($connection)
    {
        self::$_connection = $connection;
    }

    /**
     * @return string
     */
    public static function index()
    {
        throw  new Exception('overwrite function index ');
    }

    /**
     * @return boolean|string
     */
    public function routing()
    {

        if (!$this->_routing) {
            $user = User::getLoginUser();
            if ($user) {
                $this->setRouting($user->getClientId());
            }
        }

        return $this->_routing;
    }

    /**
     * @return string
     * @throws Exception
     */
    public function mapping()
    {
        throw  new Exception('not set mapping');
    }

    /**
     * @return string
     * @throws Exception
     */
    public function setting()
    {
        throw  new Exception('not set setting');
    }

    public function updateMapping()
    {
        $params = [
            'index' => $this->index(),
            'body' => $this->mapping(),
        ];

//        var_dump($params);
//        die;

        $ret = $this->getDbConnection()->indices()->putMapping($params);
    }


    public function createIndex()
    {
        $params = [
            'index' => $this->index(),
            'body' => [
                'settings' => $this->setting(),
                'mappings' => $this->mapping(),
                //'warmers' => [ /* ... */ ],
                //'aliases' => [ /* ... */ ],
                //'creation_date' => '...'
            ],
        ];
//        var_dump($this->setting());//,$params);
//        die;
        $this->getDbConnection()->indices()->create($params);
    }

    public function updateIndexSettings()
    {
        $params = [
            'index' => $this->index(),
            'body' => [
                'settings' => $this->setting(),
//                'mappings' => $this->mapping(),
                //'warmers' => [ /* ... */ ],
                //'aliases' => [ /* ... */ ],
                //'creation_date' => '...'
            ],
        ];

        //不能修改主分片了
        unset($params['body']['settings']['number_of_shards']);
        //备份分片可以修改，但不能通过这种方式修改
        unset($params['body']['settings']['number_of_replicas']);

//        $this->getDbConnection()->indices()->delete(['index' => $params['index']]);
//        $this->getDbConnection()->indices()->create($params);
        $this->getDbConnection()->indices()->close(['index' => $this->index()]);
        $this->getDbConnection()->indices()->putSettings($params);
        $this->getDbConnection()->indices()->open(['index' => $this->index()]);
    }

    public function deleteMapping()
    {
        $params = [
            'index' => $this->index(),
        ];
        $this->getDbConnection()->indices()->deleteMapping($params);
    }


    public function search($filter, $option = [])
    {

        $params = [
            'index' => $this->index(),
            'client' => [
                'timeout' => $this->timeout,
                'connect_timeout' => $this->connect_timeout
            ]
        ];

        $params = array_merge($params, $option);
        $params['body'] = $filter;

        $routing = $this->routing();
        if (!isset($params['routing']) && $routing !== false)
            $params['routing'] = $routing;

//        echo "<pre>";
//        echo json_encode($params['body']);
//        die();
    
        $i = 0;
    
        do {
            try {
            
                $i++;
            
                $result = $this->getDbConnection()->search($params);
            
            } catch (\Throwable $e) {
            
                if ($i < 2) {
                
                    continue;
                }
            
                throw $e;
            }
        } while ($i < 2 && !isset($result));
    
        return $result;
    }

    public function count($filter, $option = [])
    {

        $params = [
            'index' => $this->index(),
            'client' => [
                'timeout' => $this->timeout,
                'connect_timeout' => $this->connect_timeout
            ]
        ];

        $params = array_merge($params, $option);
        $params['body'] = $filter;

        $routing = $this->routing();
        if (!isset($params['routing']) && $routing !== false)
            $params['routing'] = $routing;

        return $this->getDbConnection()->count($params);
    }

    /**
     * @param $index
     * @param $type
     * @param $docs
     * [
     *  $id => $doc
     * ]
     * @param $routing
     *
     * @return array
     */
    public function bulk($docs)
    {
        $bulk = [];
        foreach ($docs as $id => $doc) {
            $bulk[] = [
                'index' => [
                    '_index' => static::index(),
                    '_id' => $id,
                    'routing' => $this->routing(),
                ],
            ];
            $bulk[] = $doc;
        }

        $params = [
            'index' => static::index(),
            'body' => $bulk
        ];

        $result = static::getDbConnection()->bulk($params);
//        self::getDbConnection()->indices()->refresh(['index' => $index]);
        return $result;
    }

    public function deleteById($id)
    {
        $params = [
            'index' => static::index(),
            'id' => $id,
            'routing' => $this->routing(),
        ];
        return static::getDbConnection()->delete($params);
    }


    public static function findAllByParams($params)
    {
        return static::formatSearch(static::getDbConnection()->search($params));
    }

    public function findAllByCriteria(ESCriteria $criteria)
    {
        if ($criteria->getRouting()) {
            $this->setRouting($criteria->getRouting());
        }

        $params = [
            'index' => static::index(),
            'body' => [
                'query' => $criteria->buildQuery(),
                'sort' => $criteria->buildSort(),
                'from' => $criteria->offset,
                'size' => $criteria->limit
            ]
        ];

        return static::findAllByParams($params);
    }

    public static function formatSearch($result)
    {
        return array_column($result['hits']['hits'], '_source', '_id');
    }

    public function setRouting($routing)
    {
        $this->_routing = $routing;
    }
}
