<?php

use common\library\marketing\translation\MarketingTranslationService;
use common\library\privilege_v3\PrivilegeService;

/**
 * Created by PhpStorm.
 * Author: <PERSON>(huang<PERSON>)
 * Date: 2023/11/29
 * Time: 17:05
 */

class WebsiteChatReadController extends ShopController {

    public function actionTranslationTexts(int $scene, int $object_id = 0, int $cur_page = 1, int $page_size = 20) {
        $this->validate([
            'scene' => 'required|numeric',
            'object_id' => 'numeric',
            'cur_page' => 'numeric',
            'page_size' => 'numeric',
        ]);
        $user = User::getLoginUser();
        $clientId = $user->getClientId();
        $service = new MarketingTranslationService($clientId);
        [$count, $list] = $service->getTextList($scene, $object_id, $cur_page, $page_size);
        $this->success([
            'count' => $count,
            'list' => $list,
        ]);
    }

    public function actionTranslationSetting() {
        $user = User::getLoginUser();
        $clientId = $user->getClientId();

        $service = new MarketingTranslationService($clientId);
        $setting = $service->getTranslateConfig();

        return $this->success([
            'setting' => $setting,
        ]);
    }

    public function actionCustomFormList($show_flag = '', array $page_type = [], $cur_page = 1, $page_size = 10, $site_type = \common\library\cms\CmsConstants::SITE_TYPE_MKT) {
        $this->validate([
            'page_type' => 'array',
            'site_type' => 'int|in:'.implode(",", \common\library\cms\CmsConstants::SITE_TYPE)
        ]);

        $user = User::getLoginUser();
        $clientId = $user->getClientId();

        $formList = new \common\library\cms\form\FormList($clientId);
        $formList->setOrderBy('create_time');
        $formList->setOrder('DESC');
        $formList->setOffset(($cur_page-1) *  $page_size);
        $formList->setLimit($page_size);
        $formList->setShowFlag($show_flag);
        if ($site_type == \common\library\cms\CmsConstants::SITE_TYPE_MKT) {
            $formList->getFormatter()->listInfoSetting();
        } else {
            $formList->getFormatter()->listShopInfoSetting();
        }
        if (!empty($page_type)) {
            $formList->setPageType($page_type);
        }
        $count = $formList->count();
        $list = [];
        if($count > 0) {
            $list = $formList->find();
        }
        $this->success([
            'list'=> $list,
            'count'=> $count
        ]);

    }

    public function actionCustomFormFieldList() {
        $formFields = \common\library\cms\form\FormFieldService::getFormMapCrm();
        $fieldTypes = \common\library\cms\form\FormFieldService::getFormFieldTypeMapName();
        $this->success([
            'form_fields' => $formFields,
            'field_type' => $fieldTypes
        ]);
    }

    public function actionCustomFormInfo($form_id = 0) {
        $this->validate([
            'form_id' => 'numeric',
        ]);

        $user = User::getLoginUser();
        $clientId = $user->getClientId();

        $form = new \common\library\cms\form\Form($clientId, $form_id);

        if(!$form->hasPrivilege($user)) {
            throw new \RuntimeException(\Yii::t('common','No access'));
        }

        if($form_id > 0 && $form->isNew()) {
            throw new \RuntimeException(\Yii::t('common','Not found'));
        }

        $googleGa = new \common\library\google_ads\ga\GaSiteList($clientId);
        $googleGa->setFields(['site_id','web_site_url']);
        $googleGa->setCmsFlag(false);
        $gaSiteList = $googleGa->find();

        // 获取文案
        $service = new MarketingTranslationService($clientId);
        $texts = $service->getTexts(\common\library\marketing\site\Constants::SCENE_FORM, [$form_id]);

        $data['form_id'] = $form_id;
        $data['form_name'] = $form->form_name ?? '';
        $data['form_name_translated'] = $texts[$form_id]['form_name']['translated'] ?? '';
        $data['show_type'] = (int)($form->show_type ?? \common\library\cms\form\Form::FORM_SHOW_TYPE_VERTICAL);
        $data['layout_type'] = (int)($form->layout_type ?? \common\library\cms\form\Form::FORM_LAYOUT_DOUBLE_TYPE);
        $data['page_type'] = $form->page_type ? (json_decode($form->page_type, true)) : [];
        $data['enable_name_flag'] = (int)($form->enable_name_flag ?? \common\library\cms\form\Form::ENABLE_FLAG_TRUE);
        $data['show_flag'] = (int)$form->show_flag ?? \common\library\cms\form\Form::ENABLE_FLAG_FALSE;
        $data['button_color'] = $form->button_color ?? '';
        $data['web_site_url'] = $form->web_site_url ?? '';
        $data['all_page_list'] = \common\library\cms\CmsConstants::PAGE_TYPES;
        $data['position_list'] = \common\library\cms\CmsConstants::POSITION_PAGE_TYPES;
        $data['web_site_url_list'] = array_column($gaSiteList, 'web_site_url', 'site_id') ?? [];
        $data['field_item'] = $form->isNew() ? [\common\library\cms\form\FormFieldService::getFormMapCrm()['email'],\common\library\cms\form\FormFieldService::getFormMapCrm()['name']] : $form->getFieldItem($texts[$form_id] ?? []);

        $this->success($data);
    }

    public function actionCanDeleteCustomForm($form_id) {
        $this->validate([
            'form_id' => 'required|numeric',
        ]);

        $form = new \common\library\cms\form\Form($this->getLoginUserClientId(), $form_id);

        return $this->success(!$form->isUseInConversation());
    }

    public function actionConversationSettingList($disable_flag = null, array $user_ids = [], $page = 1, $page_size = 20, array $site_type = [\common\library\cms\CmsConstants::SITE_TYPE_MKT]) {
        $this->validate([
            'user_ids' => 'array',
            'user_ids.*' => 'int',
            'page' => 'int',
            'page_size' => 'int',
            'site_type' => 'array',
            'site_type.*' => 'in:'.implode(",", \common\library\cms\CmsConstants::SITE_TYPE)
        ]);
        $user = User::getLoginUser();
        $clientId= $user->getClientId();

        $listObj = new \common\library\cms\conversation\CmsConversationSettingList($clientId);
        $listObj->setDisableFlag(($disable_flag === '' || $disable_flag === null) ? null : $disable_flag);
        $listObj->setSiteType($site_type);
        if (!empty($user_ids)) {
            $listObj->setStaffUserId($user_ids);
        }
        $listObj->setLimit($page_size);
        $listObj->setOffset(((int) $page - 1) * $page_size);
        $listObj->getFormatter()->infoListSetting();
        $result = [
            'list' => $listObj->find(),
            'count' => $listObj->count(),
        ];

        return $this->success($result);
    }

    public function actionConversationSettingInfo($site_id, $site_type = \common\library\cms\CmsConstants::SITE_TYPE_MKT)
    {
        $this->validate([
            'site_id' => 'required|int',
            'site_type' => 'in:'.implode(",", \common\library\cms\CmsConstants::SITE_TYPE)
        ]);
        $user = User::getLoginUser();
        $clientId = $user->getClientId();

        $setting = new \common\library\cms\conversation\CmsConversationSetting($clientId, $site_id);
        if ($setting->isNew()) {
            $cmsClientRelation = CmsCrmClientRelation::model()->find("client_id = :client_id", [':client_id' => $clientId]);
            if (!$cmsClientRelation) {
                throw new RuntimeException('网站设置不存在');
            }
            $gaSite = new \common\library\google_ads\ga\GaSite($cmsClientRelation->cms_client_id, $site_id);
            if ($gaSite->isNew() || $gaSite->cms_site_id != $cmsClientRelation->cms_site_id) {
                throw new RuntimeException('网站设置不存在');
            }
            if ($site_type == \common\library\cms\CmsConstants::SITE_TYPE_SHOP) {
                return $this->success([]);
            }
            PgActiveRecord::setConnection(PgActiveRecord::getDbByClientId($clientId));
            ProjectActiveRecord::setConnection(ProjectActiveRecord::getDbByClientId($clientId));
            $setting = new \common\library\cms\conversation\CmsConversationSetting($cmsClientRelation->cms_client_id, $site_id);
            if ($setting->isNew()) {
                throw new RuntimeException('网站设置不存在');
            }
        }
        $setting->getFormatter()->infoDetailSetting();
        $result = $setting->getAttributes();

		$cmsGreyClientIds = \Yii::app()->params['cms_grey_client_ids'] ?? [];
		//支持尾号灰度
		$tailNumber = intval(substr((string)$clientId, -1));
		LogUtil::info("clientId = {$clientId}, tailNumber = {$tailNumber} cms_grey_client_ids = " . json_encode($cmsGreyClientIds));
		if (empty($cmsGreyClientIds) || in_array($tailNumber, $cmsGreyClientIds) || in_array($clientId, $cmsGreyClientIds)) {
			$result['is_grey'] = true;
		}

        return $this->success($result);
    }

    public function actionGetLeadAndCustomerFields() {
        $user = User::getLoginUser();
        $clientId = $user->getClientId();

        $result = \common\library\cms\inquiry\form_mapfield\FormMapFieldService::getLeadAndCustomerFields($clientId);

        $this->success($result);
    }
}