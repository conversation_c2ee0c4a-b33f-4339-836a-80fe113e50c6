<?php

use common\library\object\field\field_setting\FieldSettingFactory;
use common\library\object\field\FieldConstant;
use common\library\object\field\service\FunctionFieldService;
use common\library\object\field\service\SearchFieldService;
use common\library\object\object_define\Constant as ObjConstant;
use common\library\setting\user\UserSetting;
use common\library\object\field\field_setting\FieldSetting;

class FieldReadController extends Controller
{
    public function actionList(
        $object_name,
        $field_type,
        $cur_page = 1,
        $page_size = 20,
    )
    {
        $params = [];
        $method = new \ReflectionMethod(self::class, 'actionList');
        foreach ($method->getParameters() as $parameter) {
            $params[$parameter->name] = ${$parameter->name} ?? null;
        }

        $user = \User::getLoginUser();
        $clientId = $user->getClientId();
        $api = new \common\library\object\field\Api();
        $count = $api->count($clientId, $params);

        $list = [];
        if ($count) {
            $list = $api->list($clientId, $params);
        }

        $this->success([
            'count' => $count,
            'list' => $list
        ]);

    }

    /**
     * 获取触发字段（关系字段 + 公式因子字段）
     * @param string $object_name
     * @return void
     */
    public function actionTriggerField(string $object_name)
    {
        $this->validate([
            'object_name' => 'required',
        ]);

        $api = new \common\library\object\field_relation\FieldRelationApi();
        $list = $api->triggerField($object_name);
        $this->success($list);
    }

    /**
     * 获取功能字段属性值
     * @param string $object_name
     * @param array $data
     *
     * @return void
     */
    public function actionFunctionFieldValue(string $object_name, array $data)
    {
        $this->validate([
            'object_name' => 'required',
            'data' => 'required',
        ]);

        $user = \User::getLoginUser();
        $clientId = $user->getClientId();

        $api = new \common\library\object\field\Api();
        $list = $api->functionFieldValue($clientId, $object_name, $data);
        $this->success($list);
    }


    /**
     * 批量计算功能字段属性值
     * @param array $data 该参数只能传主从对象的数据，即主对象或从对象或从对象+主对象
     *
     * @return void
     */
    public function actionBatchFunctionFieldValue($data, $modified_fields = [], $mergeFormulaDefaultValueField = true)
    {
        $this->validate([
            'data' => 'required',
        ]);

        $user = \User::getLoginUser();
        $clientId = $user->getClientId();
        $data = json_decode($data, true);
        $modified_fields = json_decode($modified_fields, true);

        $list = FunctionFieldService::batchFunctionFieldValue($clientId, $data, $modified_fields, $mergeFormulaDefaultValueField);
        $this->success($list);
    }

    /**
     * 筛选条件，可搜索字段列表
     * @param $object_name
     * @return void
     */
    public function actionAllSearchField(string $object_name, string $show_field_key = '')
    {
        $this->validate([
            'object_name' => 'required',
        ]);

        $user = \User::getLoginUser();
        $clientId = $user->getClientId();

        $searchFieldService = new SearchFieldService($clientId, $object_name);
        $searchFieldService->setOperateUser($user);
        $list = [];
        if ($object_name == ObjConstant::OBJ_PRODUCT_TRANSFER_PURCHASE) {
            $type = \common\library\object\object_define\Constant::OLD_TYPE_MAP[$object_name] ?? 0;
            $type && $list = $searchFieldService->noFieldAllSearchField($type, $user->getUserId(), 'list');
        } else {
            $list = $searchFieldService->allSearchField($show_field_key);
        }
        $this->success($list);
    }


    /**
     * 用户筛选条件字段列表
     * @param string $object_name
     * @param array $used_fields
     * @param int $search_type
     * @return void
     */
    public function actionSearchFieldList(
        string $object_name,
        array  $used_fields = [],
        int    $search_type = SearchFieldService::COMMON_SEARCH_TYPE,
        string $show_field_key = ''
    )
    {
        $this->validate([
            'object_name' => 'required',
            'search_type' => 'integer|in:1,2',
        ]);

        $user = \User::getLoginUser();
        $clientId = $user->getClientId();
        $userId = $user->getUserId();

        $key = UserSetting::SEARCH_KEY_MAP[$object_name] ?? '';
        if ($object_name == ObjConstant::OBJ_COMPANY && in_array($show_field_key, [UserSetting::PUBLIC_COMPANY_LIST_FIELD, UserSetting::CUSTOMER_PUBLIC_COMMON_SEARCH_FILTER])) {
            $key = UserSetting::CUSTOMER_PUBLIC_COMMON_SEARCH_FILTER_V2;
        }
        if (!$key) {
            throw new \RuntimeException('未查询到对象对应的搜索字段key');
        }
        $setting = new UserSetting($clientId, $userId, $key);
        $searchFieldSetting = $setting->getValue();
        $searchFieldSetting = is_null($searchFieldSetting) ? [] : $searchFieldSetting;

        $userSearchFields = [];
        foreach ($searchFieldSetting as $item) {
            if ($search_type == $item['search_type'] || ($search_type == SearchFieldService::COMMON_SEARCH_TYPE && $item['search_type'] == SearchFieldService::FAST_SEARCH_TYPE)) {
                $userSearchFields[] = $item['field'];
            }
        }

        if (empty($searchFieldSetting)) {
            $fieldSetting = FieldSettingFactory::make($clientId, $object_name);
            $defaultSearchFields = $search_type == SearchFieldService::FAST_SEARCH_TYPE ? $fieldSetting->defaultFastSearchFields() :  $fieldSetting->defaultSearchFields();
            $userSearchFields = array_unique(array_column($defaultSearchFields, 'field'));
        }

        $fields = array_merge(array_diff($used_fields, $userSearchFields), $userSearchFields);

        if (empty($fields)) {
            $this->success([]);
        }

        $searchFieldService = new SearchFieldService($clientId, $object_name);
        $searchFieldService->setSearchType($search_type);
        $searchFieldService->setOperateUser($user);
        $list = [];
        if ($object_name == ObjConstant::OBJ_PRODUCT_TRANSFER_PURCHASE) {
            $type = \common\library\object\object_define\Constant::OLD_TYPE_MAP[$object_name] ?? 0;
            list($list, $_noUse) = $searchFieldService->noFieldBaseSearchField($type, $userId, 'list');
            $type && $list = array_column($list, null, 'field');
        } else {
            $list = array_column($searchFieldService->searchFieldList($show_field_key), null, 'field');
        }

        $componentList = [];
        foreach ($fields as $field) {
            if (isset($list[$field])) {
                $componentList[] = $list[$field];
            }
        }

        $this->success($componentList);
    }

    /**
     * 用户筛选条件字段列表
     * 变更用户保存搜索数据结构，支持同时保存高级搜索字段，优化获取搜索字段组件信息接口，保留历史接口兼容已接模块 ，后续模块均需迁移至该接口
     * @param string $object_name
     * @param array $used_fields
     * @param int $search_type
     * @return void
     */
    public function actionSearchFieldListV2(
        string $object_name,
        array  $used_fields = [],
        int    $search_type = SearchFieldService::COMMON_SEARCH_TYPE,
        string $show_field_key = ''
    )
    {
        $this->validate([
            'object_name' => 'required',
            'search_type' => 'integer|in:1,2,3',
        ]);
        $used_fields = array_filter($used_fields);

        $user = \User::getLoginUser();
        $clientId = $user->getClientId();
        $userId = $user->getUserId();

        $key = UserSetting::SEARCH_KEY_MAP[$object_name] ?? '';
        if ($object_name == ObjConstant::OBJ_COMPANY && in_array($show_field_key, [UserSetting::PUBLIC_COMPANY_LIST_FIELD, UserSetting::CUSTOMER_PUBLIC_COMMON_SEARCH_FILTER])) {
            $key = UserSetting::CUSTOMER_PUBLIC_COMMON_SEARCH_FILTER_V2;
        }
        if (!$key) {
            throw new \RuntimeException('未查询到对象对应的搜索字段key');
        }
        $setting = new UserSetting($clientId, $userId, $key);
        $searchFieldSetting = $setting->getValue();
        $searchFieldSetting = is_null($searchFieldSetting) ? [] : $searchFieldSetting;

        $userSearchFields = [];
        if (!empty($searchFieldSetting)) {
            //用户设置筛选字段
            $searchFieldSetting = array_column($searchFieldSetting, null, 'search_type');
            $userSearchFields = $searchFieldSetting[$search_type]['search_fields'] ?? [];
            if ($search_type == SearchFieldService::COMMON_SEARCH_TYPE) {
                $fastSearchFields = $searchFieldSetting[SearchFieldService::FAST_SEARCH_TYPE]['search_fields'] ?? [];
                $userSearchFields = array_merge($fastSearchFields, $userSearchFields);
            }
        }
        if (!$userSearchFields) {
            //默认筛选字段
            $fieldSetting = FieldSettingFactory::make($clientId, $object_name);
            if ($search_type == SearchFieldService::FAST_SEARCH_TYPE) {
                $userSearchFields = $fieldSetting->defaultFastSearchFields();
            } elseif ($search_type == SearchFieldService::COMMON_SEARCH_TYPE) {
                $userSearchFields = $fieldSetting->defaultSearchFields();
            }
        }

        if (!empty($used_fields)) {
            //前端传递的字段，若object_name,不在搜索字段中，过滤；若字段在用户筛选字段中，筛选顺序保留不变；若字段不在用户筛选字段中，前置放在最前面
            $userSearchFieldMap = [];
            foreach ($userSearchFields as $fieldItem) {
                $userSearchFieldMap[$fieldItem['object_name']][] = $fieldItem['field'];
            }
            foreach ($used_fields as $key => $item) {
                if (!array_key_exists($item['object_name'], $userSearchFieldMap)) {
                    unset($used_fields[$key]);
                    continue;
                }
                if (in_array($item['field'], $userSearchFieldMap[$item['object_name']])) {
                    unset($used_fields[$key]);
                }
            }
            $userSearchFields = array_merge($used_fields, $userSearchFields);
        }

        if (empty($userSearchFields)) {
            $this->success([]);
        }

        $searchFieldService = new SearchFieldService($clientId, $object_name);
        $searchFieldService->setSearchType($search_type);
        $searchFieldService->setOperateUser($user);
        $list = [];
        if (in_array($object_name, array(ObjConstant::OBJ_PRODUCT_TRANSFER_PURCHASE
        , ObjConstant::OBJ_INQUIRY_COLLABORATION, ObjConstant::OBJ_INQUIRY_COLLABORATION_PRODUCT))) {
            $type = \common\library\object\object_define\Constant::OLD_TYPE_MAP[$object_name] ?? 0;
            list($list, $_noUse) = $searchFieldService->noFieldBaseSearchField($type, $userId, 'list');
        } else {
            $list = $searchFieldService->searchFieldList($show_field_key);
        }

        $searchComponentFieldMap = [];
        foreach ($list as $fieldItem) {
            $searchComponentFieldMap[$fieldItem['object_name']][$fieldItem['field']] = $fieldItem;
        }

        $componentList = [];
        foreach ($userSearchFields as $item) {
            if (isset($searchComponentFieldMap[$item['object_name']][$item['field']])) {
                if (isset($item[SearchFieldService::SEARCH_SETTING_EXT_INFO])) {
                    $searchComponentFieldMap[$item['object_name']][$item['field']][SearchFieldService::SEARCH_SETTING_EXT_INFO] = $item[SearchFieldService::SEARCH_SETTING_EXT_INFO];
                }
                $componentList[] = $searchComponentFieldMap[$item['object_name']][$item['field']];
            }
        }

        $this->success($componentList);
    }

    /**
     * 获取关联对象字段列表
     * @param $object_name
     * @param $function_type
     * @return void
     */
    public function actionRelationObjectFieldList(
        $object_name,
        $function_type,
        $field_type = null
    )
    {
        $this->validate([
            'object_name' => 'required',
            'function_type' => 'required|integer|in:1,2,3,4',
        ]);

        $user = \User::getLoginUser();
        $clientId = $user->getClientId();

        $api = new \common\library\object\field\Api();
        $list = $api->relationObjectFieldList($clientId, $object_name, $function_type, $field_type);
        $this->success($list);
    }


    /**
     * 获取默认值关联对象字段列表
     * @param $object_name
     * @param int $function_type
     * @param int|null $field_type
     * @return void
     */
    public function actionDefaultFormulaRelationFieldList(
        string $object_name,
        int    $function_type,
        int    $field_type = null,
    )
    {
        $this->validate([
            'object_name' => 'required',
            'function_type' => 'required|int|in:0,1,2,3,4',
            'field_type' => 'int'
        ]);

        $user = \User::getLoginUser();
        $clientId = $user->getClientId();

        $api = new \common\library\object\field\Api();
        $list = $api->relationObjectFieldList($clientId, $object_name, $function_type, $field_type);

        $this->success($list);
    }

    /**
     * 获取字段属性+字段控件
     * @param string $object_name   主对象(若获取从对象的字段信息，需传主对象object_name, 同时获取主从对象的字段信息，目前未遇到仅获取从对象字段信息的场景，返回主从对象字段后，选择需要使用的对象即可)
     * @param mixed $object_id      主对象记录id(该参数用于在详情、编辑等场景计算记录维度的字段权限，若编辑场景未传递该参数，则返回操作人在用户级别的字段权限，即操作人在角色上的权限并集)
     * @param string $scene
     * @param string $params
     * @param string $show_field_key 表头展示的场景key, 如客户公海表头列表、私海表头列表
     * @return void
     */
    public function actionFieldList(
        string $object_name,
        mixed $object_id = null,
        string $scene = null,
        $params='',
        $show_field_key = ''
    )
    {
        $this->validate([
            'object_name' => 'required',
        ]);

        $user = \User::getLoginUser();
        $clientId = $user->getClientId();

        $api = new \common\library\object\field\Api();

        // 业务场景
        switch ($scene) {
            case FieldConstant::LIST_HEADER_SCENE:      //header
                $list = $api->headerFieldList($clientId, $object_name, false, $show_field_key);
                break;
            case FieldConstant::DETAIL_SCENE:      //detail
            case FieldConstant::DRAWER_SCENE:
            case FieldConstant::CREATE_SCENE:      //create
            case FieldConstant::EDIT_SCENE:      //edit
                $list = $api->fieldList($clientId, $object_name, $scene, [], json_decode($params, true) ?: [], false, $object_id);
                break;
            default:
                $list = [];
                break;
        }
        $this->success($list);

    }

    // 查询指定对象行不可见的字段
    public function actionObjectInvisibleFields($object_name, $functional_id, $object_id){
        $user = \User::getLoginUser();
        $userId = $user->getUserId();
        $clientId = $user->getClientId();
        $targetObjects = \common\library\object\object_relation\Helper::getMainSubObjectNames($object_name);
        $fieldFilter = new \common\library\object\field\FieldFilter($clientId);
        $fieldFilter->object_name = $targetObjects;
        $fieldFilter->show_flag = \common\library\object\field\FieldConstant::SHOW_FLAG;
        $fieldFilter->enable_flag = \Constants::ENABLE_FLAG_TRUE;
        $fieldFilter->allow_setting = \common\library\object\field\FieldConstant::ALLOW_SETTING;
        $fieldFilter->select(['field', 'object_name']);
        $fieldInfos = $duplicateFieldsMap = $duplicateFields = [];

        // 收集主从对象重复字段名
        foreach($fieldFilter->rawData() as $fieldInfo){
            $field = $fieldInfo['field'];
            if(!isset($duplicateFieldsMap[$field])){
                $duplicateFieldsMap[$field] = 0;
            }

            $duplicateFieldsMap[$field]++;
            $fieldInfos[$field] = $fieldInfo['object_name'];

            if($duplicateFieldsMap[$field] > 1){
                unset($fieldInfos[$field]);
                $duplicateFields[] = $field;
            }
        }



        $fieldPrivileges = \common\library\privilege_v3\Helper::getRecordFieldPrivilegeByObjectId($clientId, $object_id, $object_name, $userId, $functional_id);
        $disableFieldPrivileges = [];

        foreach($fieldPrivileges as $fieldPrivilege){
            $realObjectName = ObjConstant::OBJ_MAP[$fieldPrivilege['refer_type']];
            foreach($fieldPrivilege['disable']??[] as $disableField){
                if(!in_array($disableField, $duplicateFields)){
                    if(!isset($fieldInfos[$disableField])){
                        continue;
                    }
                    $realObjectName = $fieldInfos[$disableField];
                }

                if(!isset($disableFieldPrivileges[$realObjectName])){
                    $disableFieldPrivileges[$realObjectName] = [];
                }

                $disableFieldPrivileges[$realObjectName][] = $disableField;
            }

        }

        return $this->success($disableFieldPrivileges);
    }
}
