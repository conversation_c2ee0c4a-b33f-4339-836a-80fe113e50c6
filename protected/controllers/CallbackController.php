<?php

use common\library\google_ads\GoogleOAuth2;
use common\library\google_ads\GoogleConstants;

/**
 * Created by PhpStorm.
 * User: cayley
 * Date: 16/10/10
 * Time: 上午10:54
 */
class CallbackController extends Controller
{
    public function  actionGlobalSources($return_url='')
    {
        $request = Yii::app()->request;
        $token = $request->getParam('token');
        //todo ,可能还要验证是否来自环球资源的302
        if($token)
        {
            $user = User::getLoginUser();
            $api = new \common\library\product_v2\crawl\ProductCrawApi($user->getClientId(),$user->getUserId());
            $api->saveAuthInfo($token);
        }

        unset($_REQUEST['token']);
        unset($_REQUEST['return_url']);

        if(empty($return_url)) $return_url ='/manage/product/import';
        $this->redirect($return_url.'?'.http_build_query($_REQUEST));

    }



    public function actionTokenClean()
    {
        $user = User::getLoginUser();
        $api = new \common\library\product_v2\crawl\ProductCrawApi($user->getClientId(),$user->getUserId());
        $api->cleatAuthInfo();
        $this->success('');
    }


    public function actionGoogleAuth() {

        $redirectPath = GoogleConstants::REDIRECT_URL;

        $env = \Yii::app()->params['env'];

        if(in_array($env, ['test'])) {
            // 当前域名下重定向
            $loactionUri = $redirectPath;
        }elseif ($env== 'beta')
        {
            $loactionUri = 'https://omgk.xiaoman.cn'.$redirectPath;
        }
        else {
            $loactionUri = \Yii::app()->params['host']['crm_url'].$redirectPath;
        }

        $locationUrl = 'Location:'.$loactionUri;
        // 授权失败
        if(isset($_REQUEST['error']) && $_REQUEST['error'] == 'access_denied') {
            $this->success(['access_id' => 0]);
        }

        \LogUtil::info("loactionUrl完整url:{$locationUrl}");

        // 回调信息
        $state = $_REQUEST['state'];
        $code = $_REQUEST['code'];
        $scope = $_REQUEST['scope'];

        // 判断授权scope是否与定义的scope一致，防止用户在屏幕同意界面取消了某些授权
        $googleOauth = new GoogleOAuth2();
        if($scope && !$googleOauth->validateScope($scope)) {
            $query = '?error='.\Yii::t('google','Because cancel some scope, so insufficient authentication scopes, can not oauth, please try again');
            $redirectUrl = $locationUrl.$query;
            \LogUtil::error("scope error:".json_encode($_REQUEST));
            header($redirectUrl,'',302);
            return;
        }

        // 解密
        list($clientId, $userId)  = $googleOauth->decodeState($state);

        \LogUtil::info("解密clientId={$clientId},userId={$userId}");

        // 授权码获取token
        $time1 = microtime(true);
        $authTokenInfo = $googleOauth->getAccessToken($code);
        $userInfo = $googleOauth->getAccessAccountInfo($authTokenInfo['access_token']);
        $time2 = microtime(true);
        LogUtil::error('获取授权时间: '.($time2-$time1));

        $googleAccess = new \common\library\google_ads\GoogleAccessAccount();

        $googleAccess->loadByAccessAccount($clientId,$userInfo['email']);

        $refresh_token = '';

        if(!isset($authTokenInfo['refresh_token']) || empty($authTokenInfo['refresh_token'])) {
            // 不存在，寻找该账号下的存在的最新的有效refresh_token
            list($access_id, $newRefreshToken)= \common\library\google_ads\GoogleAccessAccountService::getRefreshTokenByEmail($userInfo['email']);
            if($newRefreshToken) {
                \LogUtil::info("将access_id={$access_id}的refresh_token设置为最新");
                $googleAccess->refresh_token = $newRefreshToken;
            }else {
                $query = '?error='.\Yii::t('google','Access failed, missing params of refresh_token,please cancel access in gooogle');
                $redirectUrl = $locationUrl.$query;
                \LogUtil::error("refresh_token empty error:".json_encode($_REQUEST));
                header($redirectUrl,'',302);
                return;
            }
        }else {
            $googleAccess->refresh_token = $refresh_token = $authTokenInfo['refresh_token'];
            \LogUtil::info("google有返回refresh_token=".$refresh_token);
        }

        $new_time = date('Y-m-d H:i:s', strtotime('now'));

        if($googleAccess->isNew()) {
            $googleAccess->create_time = $new_time;
            $googleAccess->create_user = $userId;
        }

        $googleAccess->client_id = $clientId;
        $googleAccess->update_user = $userId;
        $googleAccess->access_account = $userInfo['email'] ?? '';
        $googleAccess->access_open_id = $userInfo['id'] ?? '';
        $googleAccess->access_token = $authTokenInfo['access_token'];
        $googleAccess->access_status = GoogleConstants::ACCESS_STATUS_LIVE;
        $googleAccess->token_type = GoogleConstants::ACCESS_TYPE_GOOGLE;
        $googleAccess->full_name = $userInfo['name'] ?? '';
        $googleAccess->access_account_picture = $userInfo['picture'] ?? '';
        $googleAccess->locale = $userInfo['locale'] ?? '';
        $googleAccess->scope = $scope ?? $authTokenInfo['scope'];
        $googleAccess->expires_in = $authTokenInfo['expires_in'];
        $googleAccess->expire_time = date('Y-m-d H:i:s', strtotime('now') + $authTokenInfo['expires_in']);
        $googleAccess->token_refresh_time = $googleAccess->update_time = $new_time;

        $googleAccess->save();

        $accessId = $googleAccess->access_id;

        \LogUtil::info("回调access_id={$accessId},google_account={$userInfo['email']},refresh_token={$refresh_token}");

        $query = '?access_id='.$accessId;
        $redirectUrl = $locationUrl.$query;
        header($redirectUrl,'',302);
    }

}
