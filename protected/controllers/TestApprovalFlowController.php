<?php
/**
 * Author: nuxse
 * Data: 2019/11/12
 */

use common\library\approval_flow\transaction\ApprovalFlowCloseTransaction;
class TestApprovalFlowController extends Controller
{

    //获取发起人上级类型的user_id列表
    public function actionListSponsorSuperior($user_id,$type){

        $userInfo = User::getUserObject($user_id);
        $clientId = $userInfo->getClientId();
        echo "<pre>";

        $result = \common\library\approval_flow\Helper::getSponsorSuperiorList($user_id,$clientId,$type);
        var_dump($result);
        return $this->success($result);
    }
    
    /**
     * 创建
     * @param $title
     * @param $refer_type
     * @param $trigger_type
     * @param int $criteria_type
     * @param string $description
     * @param string $criteria
     * @param array $filters
     * @param array $handlers
     * @return false|string
     */
    public function actionCreate(

    ) {

        echo "<pre>";

        $user     = User::getLoginUser();
        $clientId = $user->getClientId();

        $title = '自定义审批33';
        $refer_type = 0;
        $trigger_type = 0;
        $approval_flow_id = 0;
        $type = 2;
        $criteria_type = \common\library\workflow\WorkflowConstant::CRITERIA_TYPE_NULL;
        $description = '';
        $criteria = '(1)';//
        $filters = [];
        $handlers = '{}';
        $flow     = new \common\library\approval_flow\ApprovalFlowConfig($clientId, $approval_flow_id);

        $filters[0]['filter_no']= 1;
        $filters[0]['field'] =  'user_id';
        $filters[0]['field_label'] = '发件人';
        $filters[0]['field_type'] = 15;
        $filters[0]['operator'] = 'in';
        $filters[0]['value']['last_owner'] = "";
        $filters[0]['value']['user_id'][0] = '11858704';
        $filters[0]['value']['user_id'][1] = '11858529';
        $filters[0]['value']['last_owner_ids'] = '';
        $filters[0]['dateType'] = 0;
        $filters[0]['label'] =  'Five, joliecshi';
        $filters[0]['public_flag'] = 0;
        $filters[0]['update'] = '';

        $flow->create(
            $title,
            $refer_type,
            $trigger_type,
            $criteria_type,
            $description,
            $criteria,
            $filters,
            json_decode($handlers, true) ?: [],
            $type
        );
        $flow->getFormatter();
        $data = $flow->getAttributes();
        return $this->success($data);
    }

    /**
     * 编辑
     * @param $approval_flow_id
     * @param $title
     * @param $refer_type
     * @param $trigger_type
     * @param int $criteria_type
     * @param string $description
     * @param string $criteria
     * @param array $filters
     * @param array $handlers
     * @return false|string
     */
    public function actionEdit(
        $approval_flow_id,
        $title,
        $refer_type,
        $trigger_type,
        $criteria_type = \common\library\workflow\WorkflowConstant::CRITERIA_TYPE_NULL,
        $description = '',
        $criteria = '',
        array $filters = [],
        $handlers = '{}'
    ) {
        $user     = User::getLoginUser();
        $clientId = $user->getClientId();
        $flow     = new \common\library\approval_flow\ApprovalFlowConfig($clientId, $approval_flow_id);
        $flow->edit(
            $title,
            $refer_type,
            $trigger_type,
            $criteria_type,
            $description,
            $criteria,
            $filters,
            json_decode($handlers, true) ?: []
        );
        $flow->getFormatter();

        return $this->success($flow->getAttributes());
    }

    /**
     * @param $approval_flow_id
     * @param int $enable
     * @return false|string
     */
    public function actionSwitch($approval_flow_id, $enable = 1)
    {
        $clientId      = \User::getLoginUser()->getClientId();
        $approvalFlow  = new  \common\library\approval_flow\ApprovalFlowConfig($clientId, $approval_flow_id);
        $operateResult = $enable ? $approvalFlow->open() : (new ApprovalFlowCloseTransaction($approvalFlow))->commit();

        return $this->success(['operate_result' => $operateResult]);
    }

    /**
     * @param $approval_flow_id
     * @return false|string
     */
    public function actionRemove($approval_flow_id)
    {
        $clientId      = \User::getLoginUser()->getClientId();
        $approvalFlow  = new  \common\library\approval_flow\ApprovalFlowConfig($clientId, $approval_flow_id);
        $operateResult = $approvalFlow->remove();

        return $this->success(['operate_result' => $operateResult]);
    }

    public function actionCreateStep(
    ) {

        $approval_flow_id = 1102367605;
        $approval_mode = 4;
        $edit_flag = 0;
        $transfer_flag = 1;
        $title = '自定义步骤名称' ;
        $criteria_type = \common\library\workflow\WorkflowConstant::CRITERIA_TYPE_NULL;
        $criteria = '';
        $filters = [];

        $filters[0]['filter_no']= 1;
        $filters[0]['field'] =  'user_id';
        $filters[0]['field_label'] = '发件人';
        $filters[0]['field_type'] = 15;
        $filters[0]['operator'] = 'in';
        $filters[0]['value']['last_owner'] = "";
        $filters[0]['value']['user_id'][0] = '11858704';
        $filters[0]['value']['user_id'][1] = '11858529';
        $filters[0]['value']['last_owner_ids'] = '';
        $filters[0]['dateType'] = 0;
        $filters[0]['label'] =  'Five, joliecshi';
        $filters[0]['public_flag'] = 0;
        $filters[0]['update'] = '';

        $approver_config[0]['approver_type'] = 4;
        $approver_config[0]['approver'][0] = 1;
        $approver_config[0]['approver'][1] = 2;

        $clientId = \User::getLoginUser()->getClientId();
        $step     = new  \common\library\approval_flow\ApprovalStep($clientId, $approval_flow_id);
        $step->create(
            $title,
            $approval_mode,
            $approver_config,
            $edit_flag,
            $transfer_flag,
            $criteria_type,
            $criteria,
            $filters
        );

        return $this->success($step->getAttributes());
    }

    /**
     * @param $approval_flow_id
     * @param $approval_step_id
     * @return false|string
     */
    public function actionRemoveStep($approval_flow_id, $approval_step_id)
    {
        $clientId      = \User::getLoginUser()->getClientId();
        $step          = new  \common\library\approval_flow\ApprovalStep($clientId, $approval_flow_id, $approval_step_id);
        $operateResult = $step->remove();

        return $this->success(['operate_result' => $operateResult]);
    }

    /**
     * @param $approval_flow_id
     * @param $approval_step_id
     * @param $title
     * @param $approval_mode
     * @param $approver
     * @param $approver_type
     * @param $edit_flag
     * @param $transfer_flag
     * @param int $criteria_type
     * @param string $criteria
     * @param array $filters
     * @param array $handlers
     * @return false|string
     */
    public function actionEditStep(
        $approval_flow_id,
        $approval_step_id,
        $title,
        $approval_mode,
        $edit_flag,
        $transfer_flag,
        array $approver_config = [],
        $criteria_type = \common\library\workflow\WorkflowConstant::CRITERIA_TYPE_NULL,
        $criteria = '',
        array $filters = []
    ) {
        $clientId = \User::getLoginUser()->getClientId();
        $step     = new  \common\library\approval_flow\ApprovalStep($clientId, $approval_flow_id,$approval_step_id);
        $step->edit(
            $title,
            $approval_mode,
            $approver_config,
            $edit_flag,
            $transfer_flag,
            $criteria_type,
            $criteria,
            $filters,
        )->save();
        return $this->success($step->getAttributes());
    }

    /**
     * 审批流排序 不需要校验审批流状态
     * @param array $order_map
     * @return false|string
     */
    public function actionOrderFlow(array $order_map,$page_no = 1,$page_size = 20)
    {
        $clientId = \User::getLoginUser()->getClientId();
        \common\library\approval_flow\Helper::orderFlow($clientId,$order_map,$page_no,$page_size);
        return $this->success([]);
    }

    /**
     * 审批步骤排序
     * @param array $order_map
     * @return false|string
     */
    public function actionOrderStep($approval_flow_id,array $order_map,$page_no = 1,$page_size = 20)
    {
        $clientId = \User::getLoginUser()->getClientId();
        \common\library\approval_flow\Helper::orderStep($clientId,$approval_flow_id,$order_map,$page_no,$page_size);
        return $this->success([]);
    }


}