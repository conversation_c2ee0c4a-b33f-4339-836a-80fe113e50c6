<?php

use common\library\customer_v3\company\orm\Company;

use common\library\customer\rule_config\RuleConfigConstants;
use common\library\exchange_rate\ExchangeRateService;
use common\library\opportunity\Opportunity;
use common\library\custom_field\FieldList;
use common\library\opportunity\form\OpportunityInputForm;
use common\library\opportunity\Helper;
use common\library\privilege_v3\PrivilegeConstants;
use common\library\privilege_v3\PrivilegeFieldV2;
use common\library\setting\library\swarm\SwarmApi;
use common\library\setting\library\swarm\Swarm;
use common\library\setting\user\UserSetting;
use common\library\custom_field\CustomFieldService;
use common\library\opportunity\OpportunityFilter;
use common\library\workflow\WorkflowConstant;

class OpportunityV2ReadController extends Controller
{
    public function actionFullFieldList($type, $scene = '')
    {
        $user = User::getLoginUser();
        $fullFieldList = \common\library\customer\Helper::getFullFieldList($user->getClientId(), $user->getUserId(), $type, false, '', true);

        array_walk($fullFieldList, function (&$item) {

            $item = array_column($item, null, 'id');

        });

        [$type, $functionalId] = \common\library\privilege_v3\Helper::getRealReferAndFunctionalId($type);

        $customFieldList = new FieldList($user->getClientId());
        $customFieldList->setFields(['id','type','is_editable','field_type']);
        $customFieldList->setType($type);
        $customFieldList->setPrivilegeInfo($user->getUserId(), $functionalId);
        $customFieldList->setGroupId(\common\library\custom_field\CustomFieldService::OPPORTUNITY_GROUP_BASIC);
        $customFieldList = $customFieldList->find();
        $customFieldList = array_column($customFieldList,null,'id');

        $isEditableIdList = array_column(array_filter($customFieldList, function($item) {
            return $item['is_editable'] == 1;
        }),'id');

        $transferMap = \common\library\opportunity\Helper::TRANS_FIELD_MAP;
        $transferFlipMap = array_flip(\common\library\opportunity\Helper::TRANS_FIELD_MAP);

        $configInfo = [];

        foreach ($fullFieldList as $k => $item) {

            foreach ($item as $kk => $value) {

                if (!isset($value['sorter'])) {
                    $value['sorter'] = in_array($value['id'], Helper::SORT_FIELDS) ? 1 : 0;
                }

                if (!isset($value['is_editable'])) {
                    $value['is_editable'] = (in_array($value['id'], $transferMap) && in_array($transferFlipMap[$value['id']], $isEditableIdList)) ? 1 : 0;
                }

                if (in_array($value['id'],$transferMap)) {
                    $value['field_type'] = isset($customFieldList[$transferFlipMap[$value['id']]]) ? $customFieldList[$transferFlipMap[$value['id']]]['field_type'] : $value['field_type'];
                }

                if (in_array($value['field_type'], [CustomFieldService::FIELD_TYPE_DATE, CustomFieldService::FIELD_TYPE_DATETIME])) {

                    $configInfo[RuleConfigConstants::RULE_TYPE_DATE][$value['base']][$value['id']] = $value;

                    continue;
                }

                if ($value['base'] == CustomFieldService::FIELD_BASE_OF_CUSTOM_EXTERNAL) {

                    $configInfo[\common\library\customer\rule_config\RuleConfigConstants::REFER_RULE_TYPE_MAP[$value['type']]][$value['base']][$value['id']] = $value;

                    continue;
                }

                //过滤掉引用字段
                if ($value['field_type'] ==  \common\library\custom_field\CustomFieldService::FIELD_TYPE_QUOTE_FIELDS) {
                    continue;
                }


                $configInfo[\common\library\customer\rule_config\RuleConfigConstants::REFER_RULE_TYPE_MAP[$value['type']]][$value['base']][$value['id']] = $value;
            }

        }


        $fieldsSort = \common\library\opportunity\Helper::getFieldsSort();

        foreach ($fieldsSort as $k => $value) {

            if (empty($configInfo[$k] ?? [])) {

                continue;
            }

            $systemConfig = [];

            //只处理base=1的系统字段
            foreach ($value as $item) {


                $info = $configInfo[$k][CustomFieldService::FIELD_BASE_OF_SYSTEM][$item] ?? [];

                if (empty($info)) {

                    continue;
                }



                $systemConfig[] = $info;
            }


            $result[] = [
                'rule_type'   => $k,
                'rule_name'   => Yii::t('opportunity', RuleConfigConstants::RULE_TYPE_NAME[$k]),
                'config_info' => array_merge($systemConfig, $configInfo[$k][CustomFieldService::FIELD_BASE_OF_CUSTOM_EXTERNAL] ?? []),
            ];
        }

        return $this->success($result);
    }

    /**
     * 支持筛选的字段
     * @param $type
     */
    public function actionFieldRuleConfig($type = RuleConfigConstants::RULE_CONFIG_TYPE_OPPORTUNITY_LIST_NORMAL, $scene = '')
    {
        $user = User::getLoginUser();
        $filterFieldList = OpportunityFilter::getWrapRelateConfig($user->getClientId(), $user->getUserId(), $type, [\Constants::TYPE_COMPANY, \Constants::TYPE_OPPORTUNITY],$scene);
        return $this->success($filterFieldList);
    }

    /**
     * 字段支持的操作符配置信息
     */
    public function actionExportFieldOperatorConfig()
    {
        $result = [
            'field_type_operator_map' => [],
            'operators'               => \common\library\customer\Helper::getOperatorsList(),
        ];

        foreach (Helper::getExporetFieldTypeOperatorMap() as $fieldType => $operators) {
            $result['field_type_operator_map'][] = [
                'field_type' => $fieldType,
                'operators'  => $operators,
            ];
        }


        foreach ($result as &$list) {
            $list = array_values($list);
        }

        return $this->success($result);
    }

    /**
     * 商机表单字段列表
     * @param $opportunity_id
     * @param $copy_from_opportunity_id
     * @param array|null $customer_id
     * @param $archive_flag
     * @param $type
     * @param $scene
     */
    public function actionFormFieldList(
        $opportunity_id = 0,
        $copy_from_opportunity_id = 0,
        $archive_flag = 1,
        $scene = 'submit_form',
        $with_fail_field = 0,
        $quotation_id = 0,
        $privilege_scene = \common\library\privilege_v3\object_service\ObjPrivilegeService::OBJECT_SCENE_VIEW,
    )
    {
        $this->validate([
            'opportunity_id' => 'numeric',
            'copy_from_opportunity_id' => 'numeric',
            'archive_flag' => 'numeric',
            'scene' => 'string',
            'with_fail_field' => 'numeric',
            'quotation_id' => 'numeric'
        ]);

        $user = User::getLoginUser();
        
        $scopeUserIds = null;
        if ($opportunity_id > 0){
            $opportunity = new Opportunity($user->getClientId(), $opportunity_id);
            $scopeUserIds = \common\library\util\PgsqlUtil::arrayOrTrimArray($opportunity->scope_user_ids);
        }
        $form = (new OpportunityInputForm($user->getClientId(), $user->getUserId(), PrivilegeConstants::FUNCTIONAL_OPPORTUNITY, $scene, privilegeScene: $privilege_scene, scopeUserIds: $scopeUserIds));

        $opportunityData = $productListData = null;
        $fileList = [];
        if (!empty($opportunity_id)) {
            $opportunity = new Opportunity($user->getClientId(), $opportunity_id);
            $opportunity->setUserId($user->getUserId());

            if (!$opportunity->isExist()) {
                throw new RuntimeException(\Yii::t('opportunity', 'Opportunity does not exist'));
            }

            if ($scene == 'info') {
                $groups = [\common\library\custom_field\CustomFieldService::OPPORTUNITY_GROUP_BASIC, \common\library\custom_field\CustomFieldService::OPPORTUNITY_GROUP_PRODUCT];
                $opportunity->getFormatter()->detailInfoSetting($groups);
                $opportunity->getFormatter()->setShowCaptureCard(true);
                $with_fail_field && $opportunity->getFormatter()->setShowFailStageField(true);
            }

            $opportunity->getFormatter()->setFieldPrivilegeFormatterFlag(true);
            $opportunityData = $opportunity->getAttributes();
            $productListData = $opportunityData['product_list'] ?? [];
            $fileList = $opportunityData['file_list'] ?? [];
        } elseif (!empty($quotation_id)) {
            $quotation = new \common\library\invoice\Quotation($user->getUserId(), $quotation_id);
            $quotation->getFormatter()->setSpecifyFields(['name','company_id','currency','exchange_rate','exchange_rate_usd','amount','customer_id','remark','product_list']);
            $quotationInfo = $quotation->getAttributes();
            $quotationInfo = array_filter($quotationInfo, function ($value, $key) {
                if (in_array($key,['company_id','customer_id']) && empty($value)) {
                    return false;
                }
                return true;
            },ARRAY_FILTER_USE_BOTH);
            //需要回填报价单关联客户的客户来源
            if (!empty($quotationInfo['company_id'])) {
                $company = new Company($user->getClientId(), $quotationInfo['company_id']);
                !empty($company->origin_list) && $quotationInfo['origin_list'] = $company->origin_list;
            }
            //需要按照主币种回填汇率
            $currency = \common\library\account\Client::getClient($user->getClientId())->getMainCurrency();
            if ($currency == ExchangeRateService::USD) {
                $quotationInfo['exchange_rate'] = $quotationInfo['exchange_rate_usd'];
            }

            $quotationInfo['exchange_rate_usd'] = round($quotationInfo['exchange_rate_usd'], 4);
            $quotationInfo['exchange_rate'] = round($quotationInfo['exchange_rate'], 4);

            $formatter = new \common\library\opportunity\OpportunityFormatter($user->getClientId());
            $formatter->setGroupIds([[\common\library\custom_field\CustomFieldService::OPPORTUNITY_GROUP_BASIC, \common\library\custom_field\CustomFieldService::OPPORTUNITY_GROUP_PRODUCT]]);
            $opportunityData = $quotationInfo;
            $opportunityData['data'] = $formatter->formatInfoGroup($quotationInfo);
            $productListData = $quotationInfo['product_list'] ?? [];
        }


        $extraFields = [];
        if ($with_fail_field) {
            $extraFields = [
                'opportunity' => [
                    \common\library\setting\user\process\FormFieldSort::GROUP_GENERAL => [
                        'fail_type',
                        'fail_remark'
                    ],
                ],
            ];
        }
        $list = $form->getInputFields(['opportunity' => $opportunityData, 'product_list' => $productListData], $extraFields);
        $list['file_list'] = $fileList;
        $this->success($list);
    }

    /**
     * 普通筛选字段: opportunity.common.search.filter
     * 高级筛选字段: opportunity.advanced.search.filter
     * @param $key
     */
    public function actionSearchFieldList($key)
    {
        $user = User::getLoginUser();
        $data = [];
        $fieldsMap = [];
        $setting = (new UserSetting($user->getClientId(), $user->getUserId(), $key))->getValue();

        $ruleConfigType = $key == UserSetting::OPPORTUNITY_COMMON_SEARCH_FILTER ? RuleConfigConstants::RULE_CONFIG_TYPE_OPPORTUNITY_LIST_NORMAL : RuleConfigConstants::RULE_CONFIG_TYPE_OPPORTUNITY_LIST_ADVANCED;
        if (!empty($setting) && $groups = ArrayUtil::groupBy($setting, 'type', 'id')) {
            foreach ($groups as $module => $fields) {
                $fieldList = (new FieldList($user->getClientId()));
                $fieldList->setType($module);
                $fieldList->setFields(['id', 'base', 'type', 'field_type', 'name', 'ext_info']);
                $fieldList->setId($fields);
                $fieldList->setIsList(0);
                $fieldList->setScene(PrivilegeFieldV2::SCENE_OF_VIEW);
                $fieldList->setPrivilegeInfo($user->getUserId(), PrivilegeConstants::FUNCTIONAL_OPPORTUNITY);
                $fieldList->setExcludeId(RuleConfigConstants::FILTER_FIELDS_IGNORE[$ruleConfigType][$module] ?? []);
                $fieldList->addExtra(array_merge((SwarmApi::getExtraField()[$module] ?? []), CustomFieldService::getExtraFieldList($module, WorkflowConstant::RULE_TYPE_SWARM, $user->getClientId()) ?? []));
                $list = array_column($fieldList->find(), null, 'id');

                foreach ($list as $item) {
                    $item['type'] = !empty($item['refer_type']) ? $item['refer_type'] : $module;
                    $item['field_type'] = intval($item['field_type']);
                    $fieldsMap[$item['type']][$item['id']] = $item;
                }
            }

            foreach ($setting as $key => $item) {
                $item['type'] = !empty($item['type']) ? $item['type'] : \Constants::TYPE_OPPORTUNITY;
                if (isset($fieldsMap[$item['type']][$item['id']])) {
                    $field = $fieldsMap[$item['type']][$item['id']];
                    if (!empty($field['disable_flag'])) {
                        continue;
                    }
                    $data[$key] = array_replace($field, [
                        'id' => $field['id'],
                        'name' => $field['name'],
                        'base' => $field['base'],
                        'type' => $field['type'],
                        'field_type' => $field['field_type'],
                        'ext_info' => $field['ext_info'] ?? [],
                        'last_select' => $item['last_select'] ?? 0
                    ]);

                    if (isset($item['value_type'])) {
                        $data[$key]['value_type'] = $item['value_type'];
                    }
                }
            }
        }

        return $this->success(array_values($data));
    }

    public function actionGetImportSetting()
    {
        $user = User::getLoginUser();
        $clientId  = $user->getClientId();
        $userId = $user->getUserId();

        $setting = new UserSetting($clientId, $userId, UserSetting::OPPORTUNITY_IMPORT_SETTING);
        $value = $setting->getValue();

        $rule = (new common\library\serial\SerialRule($clientId, Constants::TYPE_OPPORTUNITY, 'name'))->getSnRule();
        if (empty($rule['enable_flag'])) {
            $value['opportunity_name_set_rule'] = 0;
        }

        $value['opportunity_name_rule'] = $rule['enable_flag']??0;

        return $this->success($value);
    }


    public function actionImportFieldList()
    {
        $user = User::getLoginUser();

        $result = \common\library\import\Helper::getOpportunityImportFieldList($user->getClientId(), $user->getUserId());

        $this->success($result);
    }

    public function actionImportMapList($file_id = 0)
    {
        $this->validate([
            'file_id' => 'required|numeric|min:0'
        ]);
        $user = User::getLoginUser();

        $result = \common\library\import\Helper::getImportMapList($file_id, \Constants::TYPE_OPPORTUNITY, $user->getUserId(), $user->getClientId());

        $this->success($result);
    }

}
