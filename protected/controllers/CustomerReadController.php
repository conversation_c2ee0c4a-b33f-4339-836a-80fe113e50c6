<?php

use common\library\account\Client;
use common\library\custom_field\company_field\CompanyField;
use common\library\customer_v3\company\list\CompanyList;
use common\library\customer_v3\company\orm\Company;
use common\library\customer_v3\customer\CustomerList;
use common\library\customer_v3\customer\orm\Customer;
use common\library\duplicate\DuplicateConstants;
use common\library\email_identity\cards\Card;
use common\library\facebook\page\FacebookCompanySearchList;
use common\library\history\customer\CompanyHistoryList;
use common\library\import\ImportConstants;
use common\library\import\ImportReadActions;
use common\library\inquiry\InquiryService;
use common\library\oms\common\OmsConstant;
use common\library\privilege_v3\PrivilegeConstants;
use common\library\privilege_v3\PrivilegeFieldV2;
use common\library\privilege_v3\PrivilegeService;
use common\library\product_v2\ProductConstant;
use common\library\remark\SearchRelateBusinessService;
use common\library\setting\user\UserSetting;
use common\library\trail\CompanyDynamicList;
use common\models\client\CompanyHistoryPg;


// 新版客户SingleObjectV2对象

// 旧版客户BaseObject对象
//use \common\library\customer\Company;


class CustomerReadController extends Controller
{
    use \common\library\customer\FieldUniqueReadActions;


	use ImportReadActions;

	public function initImportRead() {

		$this->importType = Constants::TYPE_CUSTOMER;

		$this->importScene = ImportConstants::IMPORT_SCENE_CUSTOMER_FT;
	}


//    public function actionCompanyList(
//        $scenario=\common\library\customer\CompanyList::SCENARIO_DEFAULT,
//        $search_model=Constants::SEARCH_MODEL_SEARCHER,
//        $keyword = '',
//        $search_field='',
//        $search_duplicate_flag = 0,
//        $pin = 0,
//        array $status_id = [],
//        array $country = [],
//        array $province = [],
//        array $city = [],
//        array $biz_type = [],
//        array $origin = [],
//        array $group_id = [],
//        array $pool_id = [],
//        $start_date = '',
//        $end_date = '',
//        array $tags = [],
//        array $user_num = [1, 2],
//        array $star = [],
//        array $category_ids = [],
//        $curPage = 1,
//        $pageSize = 20,
//        $sort_field = 'recent_time',
//        $sort_type = 'desc',
//        $sort_scene = '',
//        $params_info = 0,
//        $recent_select_flag = 0,
//        $will_public = 0,
//        $receive_period = 0,
//        $get_count = 0,
//        $last_owner = '',
//        $company_field='',
//        $customer_field='',
//        $tag_match_mode = \common\library\customer\CompanyList::TAG_MATCH_MODE_SINGLE,
//        $compare_day = 0,
//        $compare_day_op = \common\library\customer\CompanyList::LESS_THAN,
//        $acquired_company_day = 0,
//        $show_all=0,
//        $show_field_key= '',
//        array $user_id = [],
//        $scenario_data = '',
//        $archive_type = null,
//        array $stage_type = [],
//        array $archive_types = [],
//        $report_item_unique_key ='',
//        array $last_owner_ids = [],
//        $min_success_opportunity_count = '',
//        $max_success_opportunity_count = '',
//        $min_performance_order_count = '',
//        $max_performance_order_count = '',
//        $deal_time_start_date = '',
//        $deal_time_end_date = '',
//        $get_contract_list = 0,
//        $main_customer_flag=0,
//        $filter_ext_field = '',
//        $high_light_flag = 0,
//        array $exclude_user_id = [],
//        array $create_user = [],
//        array $last_edit_user = [],
//        $edit_begin_time = '',
//        $edit_end_time = '',
//        $private_begin_time = '',
//        $private_end_time = '',
//        $public_begin_time = '',
//        $public_end_time = '',
//        $max_release_count = null,
//        $min_release_count = null,
//        $alibaba_company_task_id =0,
//        $send_mail_day = 0,
//        $had_send_mail = 0,
//        array $annual_procurement =[],
//        array $intention_level =[],
//        array $ali_store_id = [],
//        $duplicate_flag = null,
//        $next_follow_up_begin_time = '',
//        $next_follow_up_end_time = '',
//        $recent_follow_up_begin_time = '',
//        $recent_follow_up_end_time = '',
//        $sub_user_id = 0,
//        $swarm_id = 0,
//        array $swarm_list = [],
//        array $filters = [],
//        $criteria_type = 1,
//        $criteria = '',
//        $temp_swarm_id = '',
//        array $company_ids = [],
//	    array $time_filters = [],
//        array $public_type = [],
//        $feed_type = null,
//        array $growth_level = [],
//        array $public_reason_id = [],
//        array $assess = [],
//        array $pin_user_list = [],
//        array $origin_list = null,
//        $filter_disable_fields = 0
//    ) {
//
//        $this->validate([
//            'curPage'               => 'integer',
//            'pageSize'              => 'integer|min:1|max:100',
//            'sort_field'            => 'regex:/^\w{2,64}$/',
//            'sort_type'             => 'string|in:asc,desc',
//            'keyword'               => 'string|max:1000',
//            'company_ids'           => 'array',
//            'company_ids.*'         => 'required|integer',
//            'swarm_list'            => 'array',
//            'swarm_list.*'          => 'integer',
//            'user_id'               => 'array',
//            'user_id.*'             => 'integer',
//            'growth_level'          => 'array',
//            'growth_level.*'        => 'integer',
//            'public_reason_id'      => 'array',
//            'public_reason_id.*'    => 'numeric',
//            'assess'                => 'array',
//            'assess.*'              => 'numeric',
//            'high_light_flag'       => 'integer',
//            'pin_user_list'         => 'array',
//            'pin_user_list.*'       => 'integer',
//            'origin_list'           => 'array',
//            'origin_list.*'         => 'numeric',
//            'filter_disable_fields' => 'numeric|in:0,1',
//            'compare_day'           => 'numeric|max:3650',
//        ]);
//
//        if (!empty(array_filter($public_reason_id)) && empty(array_filter($public_type))) {
//
//            throw new RuntimeException('WRONG SELECT PARAM');
//        }
//
//
//        foreach (($filters ?? []) as $item) {
//
//            if (!isset($item['field_type']) || !isset($item['operator'])) {
//
//                throw new RuntimeException('WRONG SELECT PARAM1');
//            }
//
//            if (in_array($item['field_type'], [\common\library\field\Constant::FIELD_TYPE_DATE, \common\library\field\Constant::FIELD_TYPE_DATETIME])) {
//
//                if (!in_array($item['operator'], [WorkflowConstant::FILTER_OPERATOR_EQUAL, WorkflowConstant::FILTER_OPERATOR_NOT_EQUAL]) && is_array($item['value'] ?? '')) {
//
//                    throw new RuntimeException('WRONG SELECT PARAM2');
//                }
//                if (!in_array($item['operator'], [WorkflowConstant::FILTER_OPERATOR_IS_NULL, WorkflowConstant::FILTER_OPERATOR_NOT_NULL]) && empty($item['value'])) {
//
//                    throw new RuntimeException('WRONG SELECT PARAM3');
//                }
//            }
//        }
//
//
//        $swarm_id && \LogUtil::info("companyList params:" . json_encode($_REQUEST));
//
//        $user = User::getLoginUser();
//        $clientId = $user->getClientId();
//        $userId = $user->getUserId();
//
//        $tags = array_filter($tags);
//        $country = array_filter($country);
//        $province = array_filter($province);
//        $city = array_filter($city);
//        $biz_type = array_filter($biz_type);
//        $stage_type = array_unique(array_filter($stage_type, 'is_numeric'));
//
//        $specialSortField = [
//            'trail_status_name' => 'trail_status',
//        ];
//        if (!empty($sort_field) && isset($specialSortField[$sort_field])) {
//            $sort_field = $specialSortField[$sort_field];
//        }
//
//        $externalFieldSort = is_numeric($sort_field);
//
//        $isPrivate = ($show_field_key == \common\library\setting\user\UserSetting::PRIVATE_COMPANY_LIST_FIELD) || ($user_num != [0]);
//
//	    $checkFieldScene = '';
//	    $showFieldKey = null;
//	    if (!empty($swarm_id)) {
//		    $showFieldKey = $swarm_id;
//
//            $checkFieldScene = $isPrivate ? 'swarm' : 'publicSwarm';
//	    } else {
//		    // 查看需要展示的 key
//		    if (!empty($show_field_key)) {
//			    $showFieldKey = $show_field_key;
//		    }
//	    }
//	    $companyListFieldList = \common\library\customer\Helper::getCompanyListFieldList($clientId, $userId, $showFieldKey, $checkFieldScene);
//	    // 根据表头配置排序
//	    if ($sort_scene == 'setting') {
//		    foreach ($companyListFieldList as $companyListField) {
//			    if (!empty($companyListField['order']) && in_array($companyListField['order'], ['asc', 'desc'])) {
//				    $sort_field = $companyListField['field'];
//				    if (!empty($sort_field) && isset($specialSortField[$sort_field])) {
//					    $sort_field = $specialSortField[$sort_field];
//				    }
//				    $sort_type = $companyListField['order'];
//				    break;
//			    }
//		    }
//	    } elseif($sort_scene == 'search') {
//            foreach ($companyListFieldList as &$companyListField) {
//                unset($companyListField['order']);
//            }
//        }
//
//        /**
//         * 1.不在允许允许范围内的排序字段都降级为order_time字段排序
//         * 2.自定义字段的排序要对值进行默认值和类型转换处理
//         */
//        if (!is_numeric($sort_field) && !in_array($sort_field, \common\library\customer\BaseCompanyList::allowSortFields)) {
//            $sort_field = 'order_time';
//        } elseif (is_numeric($sort_field)) {
//            $fieldInfo = \common\library\custom_field\Helper::getFieldListInfo($clientId, $userId, \Constants::TYPE_COMPANY, 0, [$sort_field]);
//            switch ($fieldInfo[0]['field_type'] ?? 0) {
//                case CustomFieldService::FIELD_TYPE_NUMBER:
//                    $sort_field = "cast(coalesce(nullif(external_field_data->>'{$sort_field}',''),'-9223372036854775807') as decimal)";
//                    break;
//
//                case CustomFieldService::FIELD_TYPE_DATE:
//                    $sort_field = "cast(coalesce(nullif(external_field_data->>'{$sort_field}',''),'1970-01-01') as date)";
//                    break;
//
//                case CustomFieldService::FIELD_TYPE_DATETIME:
//                    $sort_field = "cast(coalesce(nullif(external_field_data->>'{$sort_field}',''),'1970-01-01 00:00:00') as date)";
//                    break;
//
//                case CustomFieldService::FIELD_TYPE_SELECT:
//                    $sort_field = "nullif(external_field_data->>'{$sort_field}','')";
//                    break;
//
//                default:
//                    $sort_field = 'order_time';
//                    break;
//            }
//        }
//
//        if (empty($sort_field) || $sort_field =='order_time' || $sort_field == 'recent_time') {
//            $sort_field = 'order_time';
//        }
//
//        // 高优质客户列表场景外需要鉴权
//        if ($scenario != \common\library\customer\CompanyList::SCENARIO_HIGH_QUALITY_CUSTOMER) {
//            // 鉴权，公海查看
//            if ($show_field_key == \common\library\setting\user\UserSetting::PUBLIC_COMPANY_LIST_FIELD
//                || count(array_intersect([0], $user_num))
//            ) {
//                \common\library\privilege_v3\Helper::checkPermission($clientId,$userId,
//                    PrivilegeConstants::PRIVILEGE_CRM_COMPANY_POOL_VIEW);
//            } else {
//                // 私海查看
//                \common\library\privilege_v3\Helper::checkPermission($clientId,$userId,
//                    PrivilegeConstants::PRIVILEGE_CRM_COMPANY_VIEW);
//            }
//        }
//
//        $params = [];
//        $method = new \ReflectionMethod(self::class, 'actionCompanyList');
//        foreach ($method->getParameters() as $parameter) {
//            $params[$parameter->name] = ${$parameter->name} ?? null;
//        }
//
//        $viewingUserId = $sub_user_id ?: $user->getUserId();
//
//        // 旧版客户列表
////        $list = new \common\library\customer\CompanyList($viewingUserId);
//
//        // 新版客户列表
//        $list = new \common\library\customer_v2\CompanyList($viewingUserId);
//
//        if (in_array($show_field_key, [\common\library\setting\user\UserSetting::PUBLIC_COMPANY_LIST_FIELD,
//                                       \common\library\setting\user\UserSetting::PUBLIC_COMPANY_LIST_FIELD_WEB]) || $user_num == [0]) {
//
//
//            $list->getFormatter()->setFieldFunctionalId(PrivilegeConstants::FUNCTIONAL_COMPANY_POOL);
//
//            $list->getFormatter()->setFieldUserId($viewingUserId);
//        }
//
//
//        $list->getFormatter()->setAssessFlag(true);
//        $list->getFormatter()->setFilterDisableFields($filter_disable_fields);
//
//        // 查看下属视角客户列表
//        if ($sub_user_id && $sub_user_id != $user->getUserId()) {
//            $list->setOpUserId($user->getUserId());
//            $list->getFormatter()->setLoginUserId($user->getUserId());
//        }
//
//        // 如果是工作台跳转需要特殊处理切user_id不设置
//        if (in_array($scenario, [$list::SCENARIO_STATISTIC_CUSTOMER_ADD, $list::SCENARIO_STATISTIC_FOLLOW_CUSTOMER])) {
//            $canViewAll = \common\library\privilege_v3\Helper::getPermissionScope($clientId, $user->getUserId(), PrivilegeConstants::PRIVILEGE_CRM_COMPANY_VIEW) == PrivilegeConstants::PRIVILEGE_SCOPE_DIRECTOR;
//            $filteredUserIds = \common\library\privilege_v3\Helper::filterPermissionScopeUser($clientId, $user->getUserId(), PrivilegeConstants::PRIVILEGE_CRM_COMPANY_VIEW, $user_id);
//            if (!$canViewAll && empty($filteredUserIds)) {
//                if ($show_all) {
//                    $filteredUserIds = \common\library\privilege_v3\Helper::getPermissionScopeUser($clientId, $user->getUserId(), PrivilegeConstants::PRIVILEGE_CRM_COMPANY_VIEW);
//                } else {
//                    $list->setCompanyIds([]);
//                }
//            }
//            $list = \common\library\customer_v2\Helper::handleStatisticScene($scenario, $scenario_data, $list, $clientId, $filteredUserIds);
//            unset($params['user_num'], $params['user_id']);
//        } else {
//            $list->setUserId(empty($user_id) ? null : $user_id);
//            $list->setUserNum($user_num);
//        }
//
//        $list->paramsMapping($params);
//        //开启es缓存
//        $list->setEnableListSearch(true);
//
//
//        if (!empty($keyword) && !empty($swarm_id) && !empty($sort_field) && $sort_scene != 'search') {
//            $list->setSortBySearchScore(false);
//        }
//
//        if (!empty($filters)) {
//            $list->setFilters($filters, $criteria_type, $criteria);
//        }
//
//        if (!empty($time_filters)) {
//            $list->setTimeFilters($time_filters);
//        }
//
//	    if (!empty($temp_swarm_id)) {
//            $list->getFormatter()->listCompanyCustomer($isPrivate ? ItemSettingConstant::ITEM_TYPE_SWARM : ItemSettingConstant::ITEM_TYPE_SWARM_PUBLIC);
//	    }
//
//        if ($sort_field == 'mail_time') // 目前只有老版邮箱的客户邮件会传这个参数和依赖以下的返回
//        {
//            $list->getFormatter()->setShowLastMail(true);
//            $list->setHasMailTime(true);
//        }
//
//
//        if ($showFieldKey) {
//            if (\common\library\customer\Helper::checkIsExistFieldByScene($showFieldKey, 'customer', $checkFieldScene)) {
//                $list->getFormatter()->listCompanyCustomer($isPrivate ? ItemSettingConstant::ITEM_TYPE_SWARM : ItemSettingConstant::ITEM_TYPE_SWARM_PUBLIC);
//            } else {
//                $list->getFormatter()->listCompany($isPrivate ? ItemSettingConstant::ITEM_TYPE_SWARM : ItemSettingConstant::ITEM_TYPE_SWARM_PUBLIC);
//            }
//            //阿里店铺信息默认返回
//            $list->getFormatter()->setShowAlibabaStoreInfo(true);
//            $list->getFormatter()->setShowAlibabaChatInfo(true);
//            $fieldIdList = \common\library\customer\Helper::getCompanyListFieldList($clientId, $userId, $showFieldKey, $checkFieldScene, true);
//            $fieldFormatterMap = [
//                'last_trail' => 'setShowLastTrail',
//                'last_remark_trail' => 'setShowLastRemarkTrial',
//                'customer.image_list' => 'setShowListCustomerImage',
//                'product_group_ids' => 'setShowProductGroupIds',
//                'swarm_list' => 'setShowSwarmInfo',
//                'main_lead_id' => 'setShowLeadInfo',
//            ];
//            foreach (['last_trail', 'last_remark_trail', 'customer.image_list', 'product_group_ids', 'swarm_list'] as $field) {
//                if (isset($fieldFormatterMap[$field])) {
//                    $fieldFormatter = $fieldFormatterMap[$field];
//                    if (in_array($field, $fieldIdList)) {
//                        $list->getFormatter()->{$fieldFormatter}(true);
//                    } else {
//                        $list->getFormatter()->{$fieldFormatter}(false);
//                    }
//                }
//            }
//
//            if ($checkFieldScene == 'swarm') {
//                foreach (['main_lead_id', 'ali_store_id'] as $field) {
//                    if (isset($fieldFormatterMap[$field])) {
//                        if (!in_array($field, $fieldIdList)) {
//                            $fieldFormatter = $fieldFormatterMap[$field];
//                            $list->getFormatter()->{$fieldFormatter}(false);
//                        }
//                    }
//                }
//
//                // 新版客户列表字段跟随表头设置
//                $disableSpecifyFields = array_diff(['origin_name', 'trail_status_name', 'group_name', 'tag', 'pool_name', 'image_list', 'pin_user_list'], $fieldIdList);
//                if (in_array('trail_status_name', $disableSpecifyFields)) {
//                    $disableSpecifyFields[] = 'trail_status';
//                }
//                if ($existSpecifyFields = $list->getFormatter()->getSpecifyFields()) {
//                    $list->getFormatter()->setSpecifyFields(array_values(array_diff($existSpecifyFields, $disableSpecifyFields)));
//                }
//            }
//            //公海客户列表字段跟随表头设置
//            if($showFieldKey == UserSetting::PUBLIC_COMPANY_LIST_FIELD){
//                $disableSpecifyFields = array_diff(['public_reason'], $fieldIdList);
//                if ($existSpecifyFields = $list->getFormatter()->getSpecifyFields()) {
//                    $list->getFormatter()->setSpecifyFields(array_values(array_diff($existSpecifyFields, $disableSpecifyFields)));
//                }
//            }
//        }
//
//        $list->getFormatter()->setShowCustomerContact(true);
//
//        // 我的客户列表里面，加入是否更新标记
//        sort($user_num);
//        if ($user_id == [$userId] && $user_num == [1,2]) {
//            $list->getFormatter()->setShowCompanyMark(true);
//            $list->getFormatter()->setShowCustomerContactMark(true);
//        }
//
//        if ($get_count)
//            return $this->success($list->count());
//
//
//	    if ($show_field_key == \common\library\setting\user\UserSetting::PUBLIC_COMPANY_LIST_FIELD) {
//            $client = \common\library\account\Client::getClient($clientId);
//            $attrs = $client->getExtentAttributes([\common\library\account\Client::EXTERNAL_KEY_CUSTOMER_SHOW_PUBLIC_CONTACT]);
//            if( !$attrs[\common\library\account\Client::EXTERNAL_KEY_CUSTOMER_SHOW_PUBLIC_CONTACT] ) {
//                $list->getFormatter()->setHideCustomerInfo(true);
//            }
//
//	    }
//
//
//        // EDM 有个筛选整合了进来，只返回邮箱联系人列表 （1：邮箱，2：whatsapp）
//        if ($get_contract_list) {
//            $alias = $list->getAlias() ?: \CompanyModel::model()->tableName();
//            $list->setOrderBy(null);
//            $list->setOrder(null); // 商机后来被改了，加了 distinct 关键字，不能排序
//            $list->setFields(["$alias.company_id", "$alias.name"]);
//            $list->setLimit(20000);
//
//            $data = $list->find();
//            $companyIds = array_column($data, 'company_id');
//
//            if (empty($companyIds))
//                return $this->success(['data' => [], 'total' => 0]);
//
//            $list = new \common\library\customer\CustomerList($user->getClientId());
//            $list->setMainCustomerFlag($main_customer_flag);
//            $list->setCompanyId($companyIds);
//            if ($send_mail_day > 0) {
//                $list->setSendMailDayAndHadSendMail($send_mail_day, $had_send_mail);
//            }
//            if($get_contract_list == 2){ //1：邮箱，2：whatsapp
//                $list->setFilterEmptyTel(true);
//                $list->getFormatter()->snsInfoSetting();
//                $list->setContact(['type'=>'whatsapp']);
//                $list->setLimit(10000);
//                $data = $list->find();
//            }else{
//                $list->getFormatter()->edmAddressInfoSetting();
//                $list->setFilterEmptyEmail(true);
//                $list->setLimit(10000);
//                $data = $list->find();
//
//                $validator = new CEmailValidator();
//                $pattern = $validator->pattern;
//                foreach ($data as $i => $item) {
//                    if (!preg_match($pattern, $item['email'])) {
//                        unset($data[$i]);
//                    }
//                }
//            }
//
//
//            $data = array_values($data);
//            $total = count($data);
//            return $this->success(compact('data', 'total'));
//        }
//
//        $list->setOrder($sort_type);
//        $list->setOrderBy($sort_field);
//        $list->setOffset(($curPage - 1) * $pageSize);
//        $list->setLimit($pageSize);
//
//        $total = null;
//        if ($externalFieldSort) {
//            // 自定义字段进行排序，如果查询结果集超出10000条记录时，需要降级成按order_time进行排序，不然会引发查询性能问题
//            $total = $list->count();
//            // 兼容旧版排序
//            if ($total > $list->getOrderLimit() && $swarm_id) {
//                $list->setOrderBy('order_time');
//            }
//        }
//
//        $count = is_null($total) ? $list->count() : $total;
//
//
//        // 公海数量过多时根据company_id排序性能过差
//        if ($isPrivate || ($count < 20000) || !in_array($sort_field, ['order_time', 'public_time'])) {
//
//            $list->setOrderBy(array_values(array_unique(array_merge((array)$sort_field, ['company_id']))));
//        }
//
//        $list->getFormatter()->setShowAiQcInfo(true);
//
//        $result = [
//            'list' => $list->find(),
//            'totalItem' => $count,
//            'search_info' => method_exists($list, 'getSearchInfo') ? $list->getSearchInfo() : [],
//        ];
//
//        if ($params_info)
//        {
////            $listObj = new \common\library\group\GroupList($clientId,Constants::TYPE_COMPANY);
////            $listObj->setIncludeSystemGroup(true);
////            $listObj->getFormatter()->groupConfigListSetting();
////            $group_list = $listObj->find();
////
//            $api = new \common\library\setting\library\group\GroupApi($clientId,Constants::TYPE_COMPANY);
//            $group_list = $api->listAll(true, [
//                \common\library\setting\library\group\Group::External_KEY_PUBLIC_TIME,
//                \common\library\setting\library\group\Group::External_KEY_START_PUBLIC_TIME,
//                \common\library\setting\library\group\Group::External_KEY_PUBLIC_IGNORE_FORZEN_USER,
//                \common\library\setting\library\group\Group::External_KEY_OWNER_ID
//            ]);
//
//            $status_list = CustomerOptionService::getCustomerStatusList($clientId);
//
//            $star_list = [
//                ['star_id' => 0, 'star_name' => \Yii::t('customer', 'star_0')],
//                ['star_id' => 1, 'star_name' => \Yii::t('customer', 'star_1')],
//                ['star_id' => 2, 'star_name' => \Yii::t('customer', 'star_2')],
//                ['star_id' => 3, 'star_name' => \Yii::t('customer', 'star_3')],
//                ['star_id' => 4, 'star_name' => \Yii::t('customer', 'star_4')],
//                ['star_id' => 5, 'star_name' => \Yii::t('customer', 'star_5')],
//            ];
//            $userList = \common\library\department\DepartmentPermission::getInstance($clientId, $userId)->userList();
//            $returnParams = [
//                'group_list' => $group_list,
//                'status_list' => $status_list,
//                'star_list' => $star_list,
//                'user_list' => $userList,
//            ];
//
//            $result['params'] = $returnParams;
//        }
//
//        $result['privilege_field_stats'] = $list->getFormatter()->getPrivilegeFieldStats();
//
//        //成交订单金额的币种字段
//        $result['transaction_order_currency'] = \common\library\performance_v2\Helper::getCurrencyFromDefaultOrderPerformanceRule($clientId);
//
//        $result['field_list'] = $companyListFieldList;
//
//        return $this->success($result);
//    }


    public function actionCompanyList() {

        $user = User::getLoginUser();

        $clientId = $user->getClientId();

        $dto = new \common\library\customer_v3\company\CompanyListDTO($_REQUEST);

        $result = (new \common\library\customer_v3\company\CompanyListProcessor($clientId, $dto))
            ->run();

        return $this->success($result);
    }


    //获取公司成员列表
    public function actionMemberList
    (
        int $page = 1,
        int $page_size = 10
    )
    {
        $user = User::getLoginUser();
        $clientId = $user->getClientId();

        $list = new \common\library\account\UserList();
        $list->setClientId($clientId);
        $list->setOffset(($page - 1) * $page_size);
        $list->setLimit($page_size);
        $list->getFormatter()->setShowUserDepartmentList(true);
        $list->getFormatter()->customerLimitListSetting();
        $userList = $list->find();
        $result = array_map(function ($item){
            $item['department_name'] = implode(';', array_column($item['department'], 'name'));
            unset($item['department']);
            return $item;
        },$userList);

        $total = $list->count();

        return $this->success(['list' => $result, 'total' => $total]);
    }

    //获取隐藏客户查询列表的字段设置
    
    /**
     * @depracated
     * @return void
     */
    public function getShowFields(){
        if ($list = CustomerOptionService::getCustomerShowFields($this->getLoginUserInfo()->client_id)) {
            $this->success($list);
        } else {
            $this->fail(-1, \Yii::t('common', 'No data'));
        }
    }

    //获取最近联系时间参照事件设置
    public function actionGetReference(){
        $clientId = $this->getLoginUserInfo()->client_id;
        if ($list = array_merge(CustomerOptionService::getReference($clientId), CustomerOptionService::getRecentFollowUpReference($clientId))) {
            $this->success($list);
        } else {
            $this->fail(-1, \Yii::t('common', 'No data'));
        }
    }

    public function actionGetRecentFollowUpReference()
    {
        if ($list = CustomerOptionService::getRecentFollowUpReference($this->getLoginUserInfo()->client_id)) {
            $this->success($list);
        } else {
            $this->fail(-1, \Yii::t('common', 'No data'));
        }
    }

    //获取客户资料自定义字段
    public function actionGetCustomFields(){
        if ($list = CustomerOptionService::getCustomFields($this->getLoginUserInfo()->client_id)) {
            $this->success($list);
        } else {
            $this->fail(-1, \Yii::t('common', 'No data'));
        }
    }

    public function actionStatusList()
    {
        $user = User::getLoginUser();
//        $fieldItemSetObj = new \common\library\custom_field\field_item\field_item_list\CustomerStatusList($user->getClientId());
//        $fieldItemSetObj->setEnableFlag(1);
//        $fieldItemSetObj->getFormatter()->simpleStatusSetting();
//        $data = $fieldItemSetObj->find();
        $data = CustomerOptionService::getCustomerStatusList($user->getClientId());

        return $this->success($data);
    }

    //获取黑名单
    public function actionBlacklist(){
        $data = (new \common\library\customer\blacklist\CustomerBlacklistList($this->getLoginUserInfo()->client_id))->find();
        $this->success($data);
    }

    /**
     * @param array $company_ids
     * @param int $exclude_owner
     * 当count(company_ids)==1时：
     * exclude_owner=1 获取所有非跟进人列表
	 * exclude_owner=2 获取非当前company_id跟进人列表
     * exclude_owner=0 仅获取当前company_id跟进人
     * @return string
     */
    public function actionAvailableUserList(array $company_ids = [], $exclude_owner = 1, $selectAllFlag = false, array $params = [])
    {
        $user = User::getLoginUser();
        $clientId = $user->getClientId();
		$userId = $user->getUserId();

        $this->validate([
            'company_ids' => 'array',
            'company_ids.*' => 'numeric',
        ]);

        $userList = new \common\library\account\UserList();
        $userList->setClientId($clientId);
        $userList->setEnableFlag(1);
        $userList->setOrder('ASC');
        $userList->setOrderBy('nickname');
        $userList->setFields(['user_id','nickname','avatar','email']);
        $list = $userList->find();

        $result = array();


        if (count($company_ids) == 1)
        {
            $company_id = $company_ids[0];

            $company = new Company($clientId, $company_id);
            if (!$company->isExist()) {
                throw new RuntimeException(Yii::t('common', 'Not found'), 404);
            }
            $owner = $company->user_id;
            foreach ($list as $elem)
            {
                if ($exclude_owner == 1 && !in_array($elem['user_id'], $owner)) {
                    $result[] = array('user_id' => $elem['user_id'], 'name' => $elem['nickname'], 'avatar' => $elem['avatar'], 'email' => $elem['email']);
                    continue;
                }

				if ($exclude_owner == 2) {
					$result[] = array('user_id' => $elem['user_id'], 'name' => $elem['nickname'], 'avatar' => $elem['avatar'], 'email' => $elem['email']);
					continue;
				}

                if (in_array($elem['user_id'], $owner))
                    $result[] = array('user_id' => $elem['user_id'], 'name' => $elem['nickname'], 'avatar' => $elem['avatar'], 'email' => $elem['email']);
            }
        }
        else
        {
            foreach ($list as $elem)
            {
                $result[] = array('user_id'=>$elem['user_id'], 'name'=>$elem['nickname'], 'avatar'=>$elem['avatar'], 'email'=>$elem['email']);
            }
        }

		$client = Client::getClient($clientId);
		$detectKey = \common\library\duplicate\DuplicateConstants::MODULE_MAP[\Constants::TYPE_COMPANY]['detecting'];
		$ruleTypeKey = DuplicateConstants::MODULE_MAP[\Constants::TYPE_COMPANY]['rule_type'];
		$duplicateRuleType = (($client->getSettingAttributes()[$detectKey] ?? 0) == \common\library\duplicate\DuplicateConstants::DUPLICATE_CONFLICT_DETECTING) ? \common\library\customer\field_unique\DuplicateFlagBuilder::DUPLICATE_ALL_RULE_TYPE : ($client->getSettingAttributes()[$ruleTypeKey] ?? 0);
		if ($duplicateRuleType == \common\library\customer\field_unique\DuplicateFlagBuilder::DUPLICATE_POOL_ID_RULE_TYPE) {
			$temp = [];
			$poolIds = $selectAllFlag ? (\common\library\customer\pool\Helper::getCompanyAllPoolIds($clientId, $userId, $params)) : (\common\library\customer\pool\Helper::getPoolIdsByCompanyIds($userId, $company_ids));
			$userPoolMap = \common\library\customer\pool\Helper::getUserPoolMap($clientId);
			foreach ($result as $item){
				if(empty(array_diff($poolIds, $userPoolMap[$item['user_id']]))){
					$temp[] = $item;
				}
			}

			return $this->success($temp);
		}

        return $this->success($result);
    }

    public function actionCreateUserList($company_id, $directOwner = 1)
    {
        (new \common\library\validation\Validator(
            compact('company_id'),
            ['company_id' => 'required|numeric']
        ))->validate();

        $user = User::getLoginUser();
        $company = new Company($user->getClientId(), $company_id);

        if (!$company->isExist())
            throw new RuntimeException(\Yii::t('customer', 'Customer does not exist'));

        if ($directOwner && !$company->checkOwner($user))
            throw new RuntimeException(\Yii::t('customer', 'Non-own data'));

        $list = new CompanyDynamicList($user->getClientId());
        $list->setOperatorUserId($user->getUserId());
        $list->setCompanyId($company_id);
        $userList = $list->getCreateUserList($company_id);

        $resultUserList = array();
        foreach ($userList as $userIdItem)
        {
            $user = User::getUserObject($userIdItem);
            $resultUserList[] = array(
                'user_id' => $user->getUserId(),
                'name' => $user->getNickname()
            );
        }

        return $this->success($resultUserList);
    }

    public function actionTrailList(
        $company_id,
        $customer_id = '',
        array $customer_ids = [],
        $begin_time = '',
        $end_time = '',
        $module = '',
        array $modules = [],
        $stat_info = 1,
        $create_user = null,
        array $create_users = [],
        $curPage = 1,
        $pageSize = 20,
        $params_info = 0,
        $adjust_email_dynamic = 0,
        $keyword = '',
        $sort = 'desc',
        $scene = '',
        $comment_flag = 0,
        $trail_id = 0,
        $comment_id = 0,
    )
    {


	    $this->validate([
            'curPage'    => 'integer',
		    'pageSize'   => 'integer|min:1|max:100',
		    'company_id' => 'required|numeric',
            'stat_info' => 'integer',
            'params_info' => 'integer',
            'adjust_email_dynamic' => 'integer',
            'comment_flag' => 'integer',
            'trail_id' => 'integer',
            'comment_id' => 'integer'
	    ]);

        $user = User::getLoginUser();

        $list = new CompanyDynamicList($user->getClientId(), $company_id);
        $list->setOperatorUserId($user->getUserId());
        $list->setCustomerId($customer_id);
        $list->setOffset(($curPage - 1) * $pageSize);
        $list->setCommentFlag($comment_flag);
        $list->setLimit($pageSize);
        $list->getFormatter()->setShowCompanyId($company_id);
        $list->getFormatter()->setShowCaptureCard(true);
        $list->getFormatter()->setShowAdjustEmailDynamic($adjust_email_dynamic?true:false);
        $list->getFormatter()->setShowCommentList(true);
        $list->getFormatter()->setShowMarketingAutomationPlan(true);
        $list->getFormatter()->setShowInvalidInquiryFlag(InquiryService::canEvalInquiry($user->getClientId()));

        if (!empty($begin_time)) {
            $list->setBeginTime($begin_time);
        }
        if (!empty($end_time)) {
            $list->setEndTime($end_time);
        }

        if (!empty($modules)) {
            $list->setModuleIds($modules);
        }

        if (!empty($module)) {
            if ($module >= 100) {
                $list->setType($module);
            } else {
                $list->setModuleId($module);
            }
        }

        if (!empty($create_user)) {
            $list->setCreateUser($create_user);
        } elseif (!empty($create_users)) {
            $list->setCreateUser($create_users);
        }
        if (!empty($company_id)) {
            $list->setCompanyId($company_id);
        }
        if (!empty($customer_id)) {
            $list->setCustomerId($customer_id);
        }

        if(!empty($customer_ids)){
            $list->setCustomerId($customer_ids);
        }

        if (!empty($keyword)) {
            $stat_info = 0;
            $list->setKeyword($keyword);
        }

        if ($scene != 'drawer') {
            $list->setExcludeModuleIds(\common\library\trail\TrailConstants::MODULE_CONTACT_MESSAGE);
        }

        if (in_array($sort, ['asc', 'desc'])) {
            $list->setOrder($sort);
        }

        $result['totalItem'] = $list->count();
        $result['tab_list'] = $list->getTabsList();

        if ($stat_info)
        {
            if (!in_array($scene, ['drawer', 'ames'])) {
                $result['stat_info'] = $list->getStatistics($company_id);
            } elseif ($result['totalItem'] >= 10) {
                $result['stat_info'] = $list->getStatistics($company_id);
            }
        }

        if ($trail_id) {
            // 定位到当前trail所在页码
            $list->setLocationTrailId($trail_id);
            $index_info = $list->getTrailLocation($company_id, $trail_id, $pageSize, $result['stat_info']??[]);
            $list->setOffset(($index_info['trail_page']-1) * $pageSize);
        } else if (isset($result['stat_info']) && count($result['stat_info'])) {
            // 根据时间分区展开
            $begin = $result['stat_info'][0]['begin'];
            $end = $result['stat_info'][0]['end'];

            $list->setBeginTime($begin);
            $list->setEndTime($end);
        }
        $ret = $list->find();
        $result['list'] = $ret;

        if ($params_info)
        {
            $customerList = new CustomerList($user->getClientId());
            $customerList->setCompanyId($company_id);
            $customerList->setFields(['customer_id', 'name', 'email']);

            $userList = $list->getCreateUserList($company_id);

            $resultUserList = array();
            foreach ($userList as $userIdItem)
            {
                $user = User::getUserObject($userIdItem);
                $resultUserList[] = array(
                    'user_id' => $user->getUserId(),
                    'name' => $user->getNickname()
                );
            }

            $moduleList = array(
                array('module' => 1, 'name' => '备注'),
                array('module' => 2, 'name' => '邮件'),
                array('module' => 3, 'name' => 'EDM'),
                array('module' => 4, 'name' => '报价单'),
            );

            $result['params_info'] = array(
                'customer_list' => $customerList->find(),
                'create_user_list' => $resultUserList,
                'module_list' => $moduleList
            );
        }

        //对返回结果按照主键进行排序
        if (!empty($result['list']) && in_array($sort, ['asc', 'desc'])) {
            $resultList = $result['list'];
            $sortType = $sort == 'desc' ? SORT_DESC : SORT_ASC;
            array_multisort(array_column($resultList, 'create_time'), $sortType, array_column($resultList, 'trail_id'), $sortType, $resultList);
            $result['list'] = $resultList;
        }

        $company = new Company($user->getClientId(), $company_id);
        $result['refer_obj_priority_user'] = \common\library\at\DefaultAvailableUser::buildReferObjPriorityUser($user->getClientId(), $company->user_id);

        if (isset($index_info)) {
            $result['index_info'] = \common\library\trail\Helper::formatIndexInfo($trail_id, $comment_id, $result['list'], $index_info);
        }
        return $this->success($result);
    }

    //有关联商机的且有邮件动态的客户，返回邮件动态调整功能按钮
    public function actionEmailDynamicAdjustSwitch($company_id){
        $this->validate([
            'company_id' => 'numeric',
        ]);
        $user = User::getLoginUser();
        $userId = $user->getUserId();
        $clientId = $user->getClientId();
        $data =\common\library\customer\Helper::getEmailDynamicAdjustSwitch($company_id,$clientId,$userId);
        $this->success($data);
    }


    /** 返回邮件关联到的所有商机
     * @param $mail_id
     * @param $company_id
     */
    public function actionEmailDynamicOpportunityList($mail_id,$company_id){
        $this->validate([
            'mail_id' => 'numeric',
            'company_id' => 'numeric',
        ]);

        $user = User::getLoginUser();
        $userId = $user->getUserId();
        $clientId = $user->getClientId();

        $opportunityIds = \common\library\opportunity\Helper::getEmailDynamicOpportunityIds($clientId,$userId,$mail_id,$company_id);

        $result = ['opportunity_ids' => $opportunityIds];
        $this->success($result);
    }


    public function actionCustomerEdmStatistics($customer_id = null)
    {
        if ($customer_id == null) {
            throw new  Exception(\Yii::t('customer', 'Please select a valid customer contact'));
        }
        $User = User::getLoginUser();
        $userId = $User->getUserId();
        $sendCount =  GroupMailService::getSendCount($userId,$customer_id);
        $sendSuccessCount = GroupMailService::getSuccessCount($userId,$customer_id);
        $failCount =  GroupMailService::getFailedCount($userId,$customer_id);
        $openedCount = GroupMailService::getOpenedCount($userId,$customer_id);
        $replyCount = GroupMailService::getReplyCountByCustomer($userId,$customer_id);

        $sendRate = ($sendCount != 0 && $sendSuccessCount != 0) ? ($sendSuccessCount / $sendCount * 100) : 0;
        $openRate = ($sendSuccessCount != 0 && $openedCount != 0) ? ($openedCount / $sendSuccessCount * 100) : 0;
        $replyRate = ($replyCount != 0 && $sendSuccessCount != 0) ? ($replyCount / $sendSuccessCount * 100) : 0;

        $data =  array(
            'send_count' => (int)$sendCount,
            'send_rate' => $sendRate,
            'send_success_count' => (int)$sendSuccessCount,
            'send_fail_count'=> (int)$failCount,
            'opened_count' => (int)$openedCount,
            'opened_rate' => $openRate,
            'reply_count' => (int)$replyCount,
            'reply_rate' => $replyRate,
        );

        $this->success($data);
    }

    public function actionOriginList($show_all = 0)
    {
        $user = User::getLoginUser();
        $clientId = $user->getClientId();

        $data = \CustomerOptionService::getOriginTree($clientId, ($show_all > 0 ? 0 : $user->getUserId()));

        $this->success($data);
    }

    public function actionCheckCompany($name, $company_id = null)
    {
        $user = User::getLoginUser();
        $poolDuplicateSwitch = \common\library\customer\pool\Helper::getCheckPoolDuplicateSwitch($user->getClientId(), \Constants::TYPE_COMPANY);
        $company = $poolDuplicateSwitch ? (new Company($user->getClientId()))->loadByNameToPool($name) : (new Company($user->getClientId()))->loadByName($name);
        $company->setOperatorUserId($user->getUserId());

        if ($company->isExist())
        {
            if ($company_id && ($company->company_id == $company_id))
                return $this->success(false);

            $company->getFormatter()->detailInfoSetting();

            $result = $company->getAttributes();
            $result['is_yours'] = in_array($user->getUserId(), $company->user_id);
        }
        else
        {
            $result = false;
        }

        return $this->success($result);
    }


    public function actionSearchList()
    {
        $user = User::getLoginUser();

        $fields = CustomerOptionService::getCustomerShowFields($user->getClientId());

        if (!$fields)
            $fields = CustomerOptionService::$searchFieldsPro;

        $this->success($fields);
    }

    /**
     * @deprecated web前端已弃用该接口,改用fieldUniqueCheck接口
     * @param $email
     * @param null $company_id
     * @param int $customer_id
     * @return false|string|void
     */
    public function actionCheckContact($email, $company_id = null, $customer_id = 0)
    {
        $user = User::getLoginUser();
        $email = strtolower(trim($email));

        if (\common\library\customer\blacklist\Helper::match($user->getClientId(), $email)) {
            throw new RuntimeException($email . \Yii::t('customer', 'In the blacklist'));
        }

        $customer = new Customer($user->getClientId());
        $customer->loadByEmail($email);

        $flag = false;
        if (!$customer->isNew() && $customer->is_archive) {
            if (empty($company_id) || ($customer->company_id != $company_id) ?: ($customer_id != $customer->customer_id)) {
                $flag = true;
            }
        }

        if (!$flag)
            return $this->success(false);

        $company = new Company($user->getClientId(), $customer->company_id);

        $company->getFormatter()->setSpecifyFields(['user_id', 'owner']);

        return $this->success($company->getAttributes());
    }

    /**
     * 判断邮箱是否可以建为客户
     * @param int $email
     * @throws RuntimeException
     */
    public function actionCheckEmailForArchive($email)
    {
        if (!EmailUtil::isEmail($email))
            throw new RuntimeException(\Yii::t('customer', 'E-mail format is incorrect'));

        $email = strtolower(trim($email));
        $user = User::getLoginUser();

        $customer = new Customer($user->getClientId());
        if (!\common\library\customer\Helper::emailCanArchive($user->getClientId(), $email)) {
            throw new RuntimeException(\Yii::t('customer', 'The contact already exists'));
        }

        $this->success('');
    }

    public function actionContactList($company_id, $format_type = 1, $search = '', $filter_disable_fields = 0, $filter_forbidden = 0, $archive_flag = 1, $filter_suspected_invalid = 0)
    {

        $this->validate([
            'company_id'            => 'required|numeric',
            'format_type'           => 'numeric',
            'filter_disable_fields' => 'numeric|in:0,1',
            'archive_flag' => 'numeric|in:0,1',
        ]);

        if(!$company_id){
            $result = array(
                'list' => [],
                'totalItem' => 0
            );

            return $result;
        }
        $user = User::getLoginUser();

        $list = new CustomerList($user->getClientId());
        $list->setCompanyId($company_id);
        $filter_forbidden && $list->setForbiddenFlag(Customer::FORBIDDEN_FLAG_NOT);
        $filter_suspected_invalid && $list->setSuspectedInvalidFlag(Customer::SUSPECTED_INVALID_FLAG_NOT);
	    $list->setSearch(strtolower(trim($search)));
        $list->getFormatter()->setFilterDisableFields($filter_disable_fields);

        if ($format_type == 1)
        {
            $list->setOrderBy('order_rank');
            $list->setOrder('asc');
            $list->getFormatter()->setSpecifyFields(['company_name','customer_id', 'name', 'email', 'tel_list', 'contact', 'main_customer_flag', 'post', 'growth_level','company_id','suspected_invalid_email_flag','forbidden_flag','reach_status','reach_count','reach_status_time','reach_success_count']);
        }
        else if ($format_type == 2)
        {
            $list->setOrderBy('mail_time');
            $list->setOrder('desc');
            $list->getFormatter()->setUserLastMail($user->getUserId());
            $list->getFormatter()->setSpecifyFields(['customer_id', 'name', 'email', 'tel_list', 'contact', 'mail_time', 'post', 'growth_level','reach_status','reach_count','reach_status_time','reach_success_count']);
        }

        if ($archive_flag == 0) {
            $client = \common\library\account\Client::getClient($user->getClientId());
            $attrs = $client->getExtentAttributes([\common\library\account\Client::EXTERNAL_KEY_CUSTOMER_SHOW_PUBLIC_CONTACT]);
            if( !$attrs[\common\library\account\Client::EXTERNAL_KEY_CUSTOMER_SHOW_PUBLIC_CONTACT] ) {
                $list->getFormatter()->setHideInfo(true);
            }
        }

        $count = $list->count();
        $list = $list->find();

        $list = array_map(function($item){
            $telList = $item['tel_list'] ?? [];
            $item['tel'] = '';
            if (count($telList)) {
                $item['tel'] = trim(($telList[0][0] ?? '') . ' ' . ($telList[0][1] ?? ''));
            }
            return $item;
        }, $list);

        $result = array(
            'list' => $list,
            'totalItem' => $count
        );

        return $this->success($result);
    }

    /**
     * 客户导出(type:1) 客户动态导出(type:7)
     * @param int $type
     */
    public function actionExport($type = CustomerExportTask::TYPE_CUSTOMER)
    {
        $this->validate([
            'type' => 'numeric',
        ]);

        $login_user = User::getLoginUser();
        $user_id = $login_user->getUserId();
        $client_id = $login_user->getClientId();

        //客户导出鉴权
        if(in_array($type ,array(CustomerExportTask::TYPE_CUSTOMER,CustomerExportTask::TYPE_COMPANY_TRAIL))){
            \common\library\privilege_v3\Helper::checkPermission($client_id,$user_id,
                \common\library\privilege_v3\PrivilegeConstants::PRIVILEGE_SETTING_CUSTOMER_EXPORT);
        }


        if(!in_array($type,[CustomerExportTask::TYPE_CUSTOMER, CustomerExportTask::TYPE_COMPANY_TRAIL])) {
            throw new RuntimeException(\Yii::t('common', 'Parameter error'));
        }



        $params = http_build_query($_REQUEST);

        if (empty($params)) {
            throw new RuntimeException(\Yii::t('common', 'Parameter error'));
        }

        $task = new CustomerExportTask();
        $res =  $task->init($login_user->getClientId(), $login_user->getUserId(), $params, $type);
        $task_id = null;
        $task_init = 0;
        if($res){
            $task_id = $task->getTaskId();
            $task_init = 1;
        }

        if($type == CustomerExportTask::TYPE_CUSTOMER){
            $log = '/tmp/customer_export.log';
        }

        if($type == CustomerExportTask::TYPE_COMPANY_TRAIL) {
            $log = '/tmp/company_trail_export.log';
        }

        \common\library\CommandRunner::run(
            'export',
            'export',
            [
                'operator_id' => $user_id,
                'task_id' => $task_id,
                'type' => $type,
                'params' => "'{$params}'",
            ],
            $log
        );

        $this->success($task_init);
    }

    /**
     * @param int $cur_page
     * @param int $page_size
     * @param array $type
     * @return string
     * 导出任务列表
     */
    public function actionExportTaskList(array $type, $cur_page = 1, $page_size = 5 )
    {
        $this->validate([
            'cur_page' => 'numeric',
            'page_size' => 'numeric',
            'type' => 'array',
            'type.*' => 'integer'
        ]);

        $user = User::getLoginUser();
        $list = new CustomerExportTaskList($user->getUserId());
        $list->setType($type);
        $list->setOffset(($cur_page - 1) * $page_size);
        $list->setLimit($page_size);
        return $this->success(array(
            'list' => $list->find(),
            'totalItem' => $list->count()
        ));
    }

    /**
     * @param int $company_id
     * @param int $show_group
     * @param string $company_hash_id
     * @param string $company_hash_origin
     * @param int $show_system_info  [dx, ai_recommend]
     * @param int $advice_id
     * @param int $archive_flag
     * @param int $mail_id
     * @param int $directOwner
     * @param int $page
     * @param int $page_size
     * @param string $version
     */
    public function actionCompanyField(
        $company_id = 0,
        $show_group=0,
        $company_hash_id = '',
        $company_hash_origin = '',
        array $include_position = [],
        $show_system_info = 0,
        $advice_id = 0,
        $archive_flag = 1,
        $mail_id = 0,
        $directOwner = 1,
        $show_follow_user_info = 0,
        $page = 1,
        $page_size = 40,
        $version = '',
    )
    {
        $this->validate([
            'company_id' => 'int',
            'advice_id'  => 'int',
            'mail_id'    => 'int',
            'page'    => 'int',
            'page_size'    => 'integer|min:1|max:100',
            'include_position' => "array",
            'include_position.*' => "string"
        ]);
        $user = User::getLoginUser();
        $clientId = $user->getClientId();
        $userId = $user->getUserId();

        $customerFields = [];
        $systemInfo = [];
        $leadFields = [];
        $followUserInfoList = [];

        $fieldFunctionalId = ($archive_flag) ? PrivilegeConstants::FUNCTIONAL_CUSTOMER : PrivilegeConstants::FUNCTIONAL_COMPANY_POOL;

        if ($company_id > 0)
        {
            $company = new Company($user->getClientId(), $company_id);
            $company->setOperatorUserId($user->getUserId());

            if ($company->isExist())
            {
                if ($directOwner && !$company->checkOwner($user)) {
                    if ($company->isPublic()) {
                        throw new RuntimeException(\Yii::t('customer', 'Non group member of the customer high seas'));
                    }
                    throw new RuntimeException(\Yii::t('customer', 'Non-own data'));
                }


                $company->getFormatter()->detailInfoSetting();
                if( $show_system_info )
                {
                    $company->getFormatter()->setShowAlibabaChatInfo(true);
                    $company->getFormatter()->setShowAlibabaStoreInfo(true);
                    $company->getFormatter()->setShowSourceDetail(true);
                    $company->getFormatter()->setShowSwarmInfo(true);
                }

                if ($show_group) {
                    $company->getFormatter()->setShowGroupFiledSerialId(true);
                }
                $data = $company->getAttributes();
                $companyField = $data['company'];
                $customerFields = $data['customers'];
                $leadFields = $data['lead']??[];
                $alibabaStoreInfo = $data['alibaba_store_info']??[];
                $sourceDetail  = $data['source_detail']??null;
                $swarmList = $data['swarm_list']??[];

                $lastOwnerInfo = $data['last_owner_info']??[];

                $company_hash_id = $company->company_hash_id;
            }  else {
                $companyField = (new CompanyField($clientId))->setFieldUserId($userId)->setFieldFunctionalId($fieldFunctionalId)->format();
            }

            if (empty($customerFields))
                $customerFields[] = (new \common\library\custom_field\company_field\CustomerField($clientId))->setFieldUserId($userId)->setFieldFunctionalId($fieldFunctionalId)->format();

            if ($show_system_info)
            {
				$fieldArr = (new CompanyField($clientId))->getSystemFieldArr($userId);
                $systemInfo = $company->getAttributes($fieldArr);

                $userMap = [];
                $selectUserIds = array_filter([$systemInfo['last_edit_user'], $systemInfo['create_user']]);
                if (!empty($selectUserIds)) {
                    $userList = \common\library\account\Helper::getBatchUserInfo($clientId, $selectUserIds);
                    $userInfoList = array_map(function ($item) {
                        return [
                            'user_id' => $item['user_id'],
                            'avatar' => $item['avatar'],
                            'name' => $item['nickname'],
                            'nickname' => $item['nickname'],
                        ];
                    }, $userList ?? []);
                    $userMap = array_column($userInfoList, null, 'user_id');
                }
                $defaultUserInfo = [
                    'user_id' => 0,
                    'nickname' => '',
                    'name' => '',
                    'avatar' => ''
                ];
                $defaultTime = '1970-01-01 00:00:00';
                $defaultDate = '1970-01-01';

                $limitFieldSetting = \CustomerOptionService::getLimitFieldByLimitSetting($clientId, Constants::TYPE_COMPANY);
                $disableFields = $limitFieldSetting['field'] ?? [];

                $systemInfo['alibaba_store_info'] = $alibabaStoreInfo ?? [];
                $systemInfo['source_detail'] = $sourceDetail ?? null;
                $systemInfo['relate_lead_info'] = \common\library\customer\Helper::getOriginLeads($clientId, $company_id);
                $systemInfo['last_owner_info'] = $lastOwnerInfo ?? [];
                $systemInfo['swarm_list'] = $swarmList ?? [];
                foreach ($systemInfo as $key => $value)
                {
                    if (in_array($key, $disableFields)) {
                        unset($systemInfo[$key]);
                        continue;
                    }
                    switch ($key) {
                        case 'create_user':
                        case 'last_edit_user':
                            $systemInfo[$key] = $userMap[$value] ?? $defaultUserInfo;
                            break;
                        case 'edit_time':
                        case 'private_time':
                        case 'public_time':
                        case 'recent_follow_up_time':
                            $systemInfo[$key] = ($value == $defaultTime) ? '' : $value;
                            break;
                        case 'next_move_to_public_date':
                            $systemInfo[$key] = ($value == $defaultDate) ? '' : $value;
                            break;
                        default:
                            break;
                    }
                }
                if (!\common\library\privilege_v3\PrivilegeService::getInstance($clientId, $userId)->checkFunctional(\common\library\privilege_v3\PrivilegeConstants::FUNCTIONAL_OPPORTUNITY)) {
                    unset($systemInfo['success_opportunity_count']);
                }
            }

            if ($show_follow_user_info)
            {
                $userIds = $company->user_id;
                $userList = \common\library\account\Helper::getBatchUserInfo($clientId, $userIds);
                $followUserInfoList = array_map(function ($item) {
                    $mapItemEmail = $item['ames_email'] ?: $item['email'];
                    $mapItemName = $item['nickname'] ?: $mapItemEmail;
                    return [
                        'user_id' => $item['user_id'],
                        'avatar' => $item['avatar'],
                        'name' => $mapItemName,
                        'nickname' => $mapItemName,
                    ];
                }, $userList ?? []);
            }

        } elseif($company_hash_id != '' && $company_hash_origin == 'dx') {

            list($companyField, $customerFields) = \common\library\customer\Helper::getDiscoveryCompanyFiled($clientId, $userId, $company_hash_id, null, null, $include_position, $page, $page_size, true);

            if($version == \common\library\customer\Helper::VERSION_V3){
                $companyUnrelated = new \common\library\user_company_unrelated\UserCompanyUnrelated($clientId, $userId, $company_hash_id);
                $relatedCompanyHashIds = $companyUnrelated->getRelatedCompanyHashIds();

                $contactList = new \common\library\discovery\api\ContactsList($clientId, $userId);
                $contactList->setCompanyHashId($company_hash_id);
                $contactList->setIncludeNotEmail(false);
                if (!empty($include_position)) {
                    $contactList->setIncludePositions($include_position);
                }
                $contactList->setChildCompanyHashIds($relatedCompanyHashIds);
                $okkiLeadsFlag = \common\library\privilege_v3\PrivilegeService::getInstance($clientId, $userId)
                    ->hasFunctional(\common\library\privilege_v3\PrivilegeConstants::FUNCTIONAL_OKKI_LEADS_BASE);
                if($okkiLeadsFlag){   // leads版本和非leads查看数据范围不一致
                    $contactList->setDefaultSortMode('v2');
                    $contactList->setIsNewVersion(true);
                }
                $count = $contactList->count();
                $customerCount = $count['total_count']??0;
                $emailCount = $count['email_count']??0;
            }

        } elseif ($company_hash_id != '' && $company_hash_origin == 'ai_recommend'){

            list($companyField, $customerFields) = \common\library\ai\Helper::getRecommendArchiveDataField($clientId, $userId, $company_hash_id, Constants::TYPE_COMPANY);

        } elseif (!empty($advice_id)) {
            $advice = (new \common\library\ai\classify\customer\advice\AiCustomerAdvice($clientId))->loadById($advice_id);
            $adviceData = $advice->getData();
            $adviceData['customer'] = ArrayUtil::index($adviceData['customer'] ?? [], 'email');
            if ($advice->isExist() && isset($adviceData['customer'][$advice->email])) {
                $companyData = $adviceData['company'];
                $customerData = $adviceData['customer'][$advice->email];
            }

            $companyField = (new CompanyField($clientId))->setFieldUserId($userId)->setFieldFunctionalId($fieldFunctionalId)->format($companyData ?? []);
            $customerFields[] = (new \common\library\custom_field\company_field\CustomerField($clientId))->setFieldUserId($userId)->setFieldFunctionalId($fieldFunctionalId)->format($customerData ?? []);
        } elseif ($mail_id > 0) {
            $mail = new \common\library\mail\Mail($mail_id);
            $extract = new \common\library\ai\classify\extract\NormalExtract($mail);
            $email =  $mail->isSendType() ? $mail->getAllReceiverEmails() : $mail->getSenderEmail();
            $mainEmail = reset($email);

            // 阿里巴巴平台询盘邮件特殊处理，不查询客户资料
            if (! in_array($mainEmail, \common\library\mail\Mail::INQUIRY_ALIBABA_EMAIL) && $mail->isReceiverType()) {
                $option = [
                    'main_email' => $mainEmail,
                    'emails' => $email
                ];
                $adviceData = $extract->extract($option);
                if (isset($adviceData['customer'])) {
                    $companyData = $adviceData['company'];
                    $customerData = $mail->isReceiverType() ? $adviceData['customer'][$mainEmail] : null;
                }
            }

            $companyField = (new CompanyField($clientId))->setFieldUserId($userId)->setFieldFunctionalId($fieldFunctionalId)->format($companyData ?? []);
            $customerFields[] = (new \common\library\custom_field\company_field\CustomerField($clientId))->setFieldUserId($userId)->setFieldFunctionalId($fieldFunctionalId)->format($customerData ?? []);
        }else if($company_hash_origin == 'okki_leads'){   //okki_leads中google\facebook\linkedin转客户默认来源设置为OKKI Leads
            $originId = \common\library\setting\library\origin\Origin::SYS_ORIGIN_ID_OKKI_LEADS;
            $companyData = [
                'origin' => $originId,
            ];
            $companyFormatter = new CompanyField($clientId);
            $companyFormatter->setFieldUserId($userId);
            $companyField = $companyFormatter->format($companyData);

            $customerFields[] = (new \common\library\custom_field\company_field\CustomerField($clientId))->setFieldUserId($userId)->setFieldFunctionalId($fieldFunctionalId)->format();
            $company_hash_id = '';
        } else {
            $companyField = (new CompanyField($clientId))->setFieldUserId($userId)->setFieldFunctionalId($fieldFunctionalId)->format();
            $customerFields[] = (new \common\library\custom_field\company_field\CustomerField($clientId))->setFieldUserId($userId)->setFieldFunctionalId($fieldFunctionalId)->format();
            $company_hash_id = '';
        }

        $data = [
            'company_hash_id' => $company_hash_id,
            'company' => $companyField,
            'customers' => $customerFields,
            'lead' => $leadFields,
            'system_info' => $systemInfo,
            'follow_user_info_list' => $followUserInfoList
        ];

        if(isset($customerCount)){
            $data['customer_count'] = $customerCount;
        }
        if(isset($emailCount)) {
            $data['email_count'] = $emailCount;
        }

	    if( $company_id > 0 && $company->isPublic()){

		    \common\library\privilege_v3\Helper::checkPrivilege($clientId, $userId, \common\library\privilege_v3\PrivilegeConstants::PRIVILEGE_CRM_COMPANY_POOL_VIEW);

		    $client = \common\library\account\Client::getClient($clientId);
		    $attrs = $client->getExtentAttributes([\common\library\account\Client::EXTERNAL_KEY_CUSTOMER_SHOW_PUBLIC_CONTACT]);
		    if( !$attrs[\common\library\account\Client::EXTERNAL_KEY_CUSTOMER_SHOW_PUBLIC_CONTACT] ) {
			    $data['customers'] = [];
		    }
	    }

        $this->success($data);
    }



    public function actionCustomerField(
        $customer_id = 0,
        $advice_id = 0,
        $archive_flag = 1,
        $mail_id = 0
    ) {
        $user = User::getLoginUser();
        $clientId = $user->getClientId();

        $fieldFunctionalId = ($archive_flag) ? PrivilegeConstants::FUNCTIONAL_CUSTOMER : PrivilegeConstants::FUNCTIONAL_COMPANY_POOL;

        if ($customer_id) {
            $customer = new Customer($clientId, $customer_id);
            $customer->setOperatorUserId($user->getUserId());

            if (!$customer->isExist())
                throw new RuntimeException(\Yii::t('customer', 'Contact does not exist or has been deleted'));

            $customer->getFormatter()->detailInfoSetting();
            $data = $customer->getAttributes();
        } elseif ($advice_id) {
            $advice = (new \common\library\ai\classify\customer\advice\AiCustomerAdvice($clientId))->loadById($advice_id);
            $adviceData = $advice->getData();
            $adviceData['customer'] = ArrayUtil::index($adviceData['customer'] ?? [], 'email');
            if ($advice->isExist() && isset($adviceData['customer'][$advice->email])) {
                $customerData = $adviceData['customer'][$advice->email];
            }

            $data = (new \common\library\custom_field\company_field\CustomerField($clientId))->format($customerData ?? []);
        } elseif ($mail_id > 0) {
            $mail = new \common\library\mail\Mail($mail_id);
            if ($mail->isReceiverType()) {
                $extract = new \common\library\ai\classify\extract\NormalExtract($mail);
                $email = $mail->getSenderEmail();
                $mainEmail = reset($email);
                $option = [
                    'main_email' => $mainEmail,
                    'emails' => $email
                ];
                $adviceData = $extract->extract($option);
                if (isset($adviceData['customer'])) {
                    $customerData = $adviceData['customer'][$mainEmail];
                }
            }
            $data = (new \common\library\custom_field\company_field\CustomerField($clientId))->format($customerData ?? []);
        } else {
            $data = (new \common\library\custom_field\company_field\CustomerField($clientId))->setFieldUserId($user->getUserId())->setFieldFunctionalId($fieldFunctionalId)->format();
        }

        return $this->success($data);
    }

    public function actionCompanyDetailInfo($company_id, $directOwner = 1, $filter_disable_fields = 0)
    {
        (new \common\library\validation\Validator(
            compact('company_id'),
            ['company_id' => 'required|numeric']
        ))->validate();

        $user = User::getLoginUser();
        $userId = $user->getUserId();
        $clientId = $user->getClientId();

        $company = new Company($clientId, $company_id);
        $company->setOperatorUserId($userId);
        $company->getFormatter()->setFilterDisableFields($filter_disable_fields);

        if (!$company->isExist())
            throw new RuntimeException(\Yii::t('customer', 'The customer does not exist or has been deleted'));

        if ($directOwner && !$company->checkOwner($user))
            throw new RuntimeException(\Yii::t('customer', 'Non-own data'));

        $company->getFormatter()->companyDetailInfoSetting();
        if( $company->isPublic()){

            $company->getFormatter()->setFieldFunctionalId(PrivilegeConstants::FUNCTIONAL_COMPANY_POOL);

            $company->getFormatter()->setFieldUserId($userId);

            \common\library\privilege_v3\Helper::checkPrivilege($clientId, $userId, \common\library\privilege_v3\PrivilegeConstants::PRIVILEGE_CRM_COMPANY_POOL_VIEW);

            $client = \common\library\account\Client::getClient($clientId);
            $attrs = $client->getExtentAttributes([\common\library\account\Client::EXTERNAL_KEY_CUSTOMER_SHOW_PUBLIC_CONTACT]);
            if( !$attrs[\common\library\account\Client::EXTERNAL_KEY_CUSTOMER_SHOW_PUBLIC_CONTACT] ) {
                $company->getFormatter()->setShowCustomer(false);
            }
        } else {
            \common\library\privilege_v3\Helper::checkPrivilege($clientId, $userId, \common\library\privilege_v3\PrivilegeConstants::PRIVILEGE_CRM_COMPANY_VIEW);
        }
        $data = $company->getAttributes();
        return $this->success($data);
    }

    public function actionCompanyBaseInfo($company_id, $directOwner = 1, $filter_disable_fields = 0, $skip_view_privilege = 0)
    {
        (new \common\library\validation\Validator(
            compact('company_id'),
            ['company_id' => 'required|numeric']
        ))->validate();

        $user = User::getLoginUser();
        $userId = $user->getUserId();
        $clientId = $user->getClientId();
        $company = new Company($clientId, $company_id);

        if (!$company->isExist())
            throw new RuntimeException(\Yii::t('customer', 'Customer does not exist'));
        $company->setOperatorUserId($userId);
        $company->getFormatter()->setAssessFlag(true);
        $company->getFormatter()->setFilterDisableFields($filter_disable_fields);
        $company->getFormatter()->baseInfoSetting();
        $company->getFormatter()->setShowCaptureCard(true);
        $company->getFormatter()->setShowAlibabaChatInfo(true);
        $company->getFormatter()->setShowAlibabaStoreInfo(true);
        $company->getFormatter()->setShowCustomerContact(true);
        $company->getFormatter()->setShowCustomerContactMark(true);

        if (!($company instanceof \xiaoman\orm\common\SingleObjectV2)) {
            $company->getFormatter()->setShowAiQcInfo(true);
        }

        if ($directOwner) {
            $company->checkPrivilege($user);
        }

        if( $company->isPublic()){

            $company->getFormatter()->setFieldFunctionalId(PrivilegeConstants::FUNCTIONAL_COMPANY_POOL);

            $company->getFormatter()->setFieldUserId($userId);

            if (!$skip_view_privilege) {
                \common\library\privilege_v3\Helper::checkPrivilege($clientId, $userId, \common\library\privilege_v3\PrivilegeConstants::PRIVILEGE_CRM_COMPANY_POOL_VIEW);
            }
            $client = \common\library\account\Client::getClient($clientId);
            $attrs = $client->getExtentAttributes([\common\library\account\Client::EXTERNAL_KEY_CUSTOMER_SHOW_PUBLIC_CONTACT]);
            if( !$attrs[\common\library\account\Client::EXTERNAL_KEY_CUSTOMER_SHOW_PUBLIC_CONTACT] )
                $company->getFormatter()->setShowMainCustomer(false);
        } else {
            if (!$skip_view_privilege) {
                \common\library\privilege_v3\Helper::checkPrivilege($clientId, $userId, \common\library\privilege_v3\PrivilegeConstants::PRIVILEGE_CRM_COMPANY_VIEW);
            }
        }
        //客户模块，计算字段权限前，先重算scope_user_ids
        $company->getFormatter()->displayScopeUserIds(true);
        $company->getFormatter()->setFormatterV2Privilege(true, true, PrivilegeFieldV2::SCENE_OF_VIEW);
        $company->getFormatter()->displayOperatePrivilege();
        $data = $company->getAttributes();

        $data['group_flag'] = 0;
        if (!empty($data['group_id'])) {
//            $listObj = new \common\library\group\migration\GroupList($clientId, \Constants::TYPE_COMPANY);
//            $listObj->setGroupId($data['group_id']);
//            $listObj->setUserId($user->getUserId());
//            $listObj->setUserShowFlag(true);
//            $listObj->getFormatter()->listInfoSetting();
//            $listObj->getFormatter()->setResultType(\common\library\setting\item\ItemSettingConstant::RESULT_TYPE_LIST);
//            $group = $listObj->find();

            $api = new \common\library\setting\library\group\GroupApi($clientId, \Constants::TYPE_COMPANY);
            $api->setFilteredByUserDisplay($userId);
            $group = $api->listById($data['group_id'], true);

            $group = ArrayUtil::index($group, 'id');

            if (isset($group[$data['group_id']])) {
                $data['group_flag'] = 1;
            }
        }

        if (!isset($data['privilege_field_stats'])) {
            //销售助手兼容旧插件
            $data['privilege_field_stats'] = $company->getFormatter()->getPrivilegeFieldStats();
        }

        return $this->success($data);
    }


    public function actionCompanyDigestInfo($company_id)
    {
        (new \common\library\validation\Validator(
            compact('company_id'),
            ['company_id' => 'required|numeric']
        ))->validate();

        $user = User::getLoginUser();
        $userId = $user->getUserId();
        $clientId = $user->getClientId();
        $company = new Company($clientId, $company_id);
        if (!$company->isExist())
            throw new RuntimeException(\Yii::t('customer', 'Customer does not exist'));
        $company->getFormatter()->digestSetting();
        $data = $company->getAttributes();

        return $this->success($data);
    }


    /**
     * 获取客户关联的客户通信息
     * @param $company_id
     * @return false|string|void
     */
    public function actionCompanyStoreList($company_id)
    {
        $this->validate([
            'company_id' => 'required'
        ]);

        $user = User::getLoginUser();
        $clientId = $user->getClientId();

        $companyRelationList = new \common\library\alibaba\customer\AlibabaCompanyRelationList($clientId);
        $companyRelationList->setCompanyId([$company_id]);
        $companyRelationList->getFormatter()->companyAssocStoreList();
        $res = $companyRelationList->find();

        return $this->success($res);
    }

    public function actionStatisticsMail($company_id, $year = '')
    {
        $loginUser = User::getLoginUser();
        $user_id = $loginUser->getUserId();
        if ($year === 'recent') {
            $year = '';
        }
        $data = \common\library\customer\Statistics::companyMail($user_id, $company_id, $year);
        return $this->success($data);
    }


    public function actionStatisticsEdm($company_id, $year = '')
    {
        $this->validate([
            'company_id' => 'required|integer',
        ]);
        $loginUser = User::getLoginUser();
        $user_id = $loginUser->getUserId();

        if ($year === 'recent') {
            $year = '';
        }

        $data = \common\library\customer\Statistics::edm($user_id, $company_id, $year);
        return $this->success($data);
    }

    public function actionStatisticsPi($company_id, $year = '')
    {
        $loginUser = User::getLoginUser();
        $user_id = $loginUser->getUserId();
        if ($year === 'recent') {
            $year = '';
        }

        $data = \common\library\customer\Statistics::pi($user_id, $company_id, $year);
        return $this->success($data);
    }

    public function actionStatisticsOrder($company_id, $year = '')
    {
        $loginUser = User::getLoginUser();
        $user_id = $loginUser->getUserId();
        if ($year === 'recent') {
            $year = '';
        }
        $data = \common\library\customer\Statistics::order($user_id, $company_id, $year,true);
        return $this->success($data);
    }

    /**
     * @param $company_id
     * @return string
     * 客户卡片
     */
    public function actionCompanyCard($company_id,$direct_owner=true, $filter_disable_fields = 0)
    {
        (new \common\library\validation\Validator(
            compact('company_id'),
            ['company_id' => 'required|numeric']
        ))->validate();

        $user = User::getLoginUser();
        $card = new Card($user->getUserId());
        $card = $card->companyCard($company_id,$direct_owner);
        return $this->success($card);
    }

    /**
     * @param $mail
     * @return string
     * 数据来源 card_type  1.私海客户 2.同事客户 3.公海客户 4.通讯录 5.陌生人 6.注册账号 7.无权限公海分组客户
     */
    public function actionMailCard($mail, $directOwner=1, $user_id=0)
    {
        $loginUser = User::getLoginUser();
        $clientId = $loginUser->getClientId();
        if($user_id)
        {
            $user = User::getUserObject($user_id);
            if($clientId != $user->getClientId() )
            {
                throw  new RuntimeException(\Yii::t('privilege', 'No Permission'));
            }
        }

        \common\library\util\Speed::log('--------START---------');
        $cardService = new Card(!empty($user_id) ? $user_id : $loginUser->getUserId());
        $card = $cardService->mailCard($mail,$directOwner);
        \common\library\util\Speed::log('--------END---------');

        $card['privilege_field_stats'] = \common\library\privilege_v3\Helper::getPrivilegeFieldStats(
            $clientId,
            $loginUser->getUserId(),
            ($card['is_public'] ?? 0) ? PrivilegeConstants::FUNCTIONAL_COMPANY_POOL : PrivilegeConstants::FUNCTIONAL_CUSTOMER
        );

        return $this->success($card);
    }

    public function actionCompanyDiffList($company_id, $curPage = 1, $pageSize = 5){

	    $this->validate([
		    'pageSize' => 'integer|min:1|max:100',
	    ]);

        $user = User::getLoginUser();
        $clientId = $user->getClientId();

        $list = new CompanyHistoryList($clientId, $company_id, $user->getUserId());
        $list->setOrderBy(['create_time', 'update_type', 'customer_id']);
        $list->setLimit($pageSize);
        $list->setOffset(($curPage - 1) * $pageSize);
        $filterType = [CompanyHistoryPg::TYPE_ADD_TAG,CompanyHistoryPg::TYPE_REMOVE_TAG];

        //没有线索模块权限，过滤[线索转化为客户]记录
        if (!PrivilegeService::getInstance($clientId)->hasFunctional(PrivilegeConstants::FUNCTIONAL_LEAD)) {
            $filterType[] = CompanyHistoryPg::TYPE_LEAD_TO_COMPANY;
        }

        $list->setFilterType($filterType);
        $historyList = $list->find();
        
        if ($historyList) {
            $company = new Company($clientId, $company_id);
            $functionalId = empty($company->user_id) ? PrivilegeConstants::FUNCTIONAL_COMPANY_POOL : PrivilegeConstants::FUNCTIONAL_CUSTOMER;
            $scopeUserIds = is_array($company->scope_user_ids) ? $company->scope_user_ids : \common\library\util\PgsqlUtil::trimArray($company->scope_user_ids);
            $fieldPrivileges = \common\library\privilege_v3\privilege_field\Helper::getToScopeUserFieldPrivileges($clientId, $user->getUserId(), $scopeUserIds, $functionalId);
            $fieldPrivileges = \common\library\custom_field\Helper::addExtraMaskHistoryField($fieldPrivileges);
            \common\library\custom_field\Helper::filterHistoryDisableFields($clientId, $user->getUserId(), $functionalId, [Constants::TYPE_COMPANY, Constants::TYPE_CUSTOMER], $historyList, $fieldPrivileges);
        }

        return $this->success(array(
            'list' => $historyList,
            'totalItem' => $list->count()
        ));
    }

    public function actionCompanyDiffList2($company_id, $curPage = 1, $pageSize = 5)
    {

	    $this->validate([
		    'pageSize' => 'integer|min:1|max:100',
	    ]);

        $user = User::getLoginUser();
        $clientId = $user->getClientId();

        \common\library\history\base\BatchHistory::setMetadataClass(\common\library\history\customer\CompanyHistoryMetadata::class);
        $filter = new \common\library\history\customer\CompanyHistoryAdaptorFilter($clientId);
        $filter->company_id = $company_id;
        $filter->type = new \xiaoman\orm\database\data\NotIn([CompanyHistoryPg::TYPE_ADD_TAG,CompanyHistoryPg::TYPE_REMOVE_TAG]);
        $filter->order('create_time', 'desc');
        //$filter->limit($pageSize,($curPage - 1) * $pageSize);
        $histories = $filter->find();
        $histories->getFormatter()->listSetting();

        return $this->success(array(
            'list' => $histories->getAttributes(),
            'totalItem' => $filter->count()
        ));
    }

    public function actionIsTrailBuilding($company_id)
    {
        (new \common\library\validation\Validator(
            compact('company_id'),
            ['company_id' => 'required|numeric']
        ))->validate();

        $user = User::getLoginUser();

        $company = new Company($user->getClientId(), $company_id);

        if (!$company->isExist())
            throw new RuntimeException(\Yii::t('customer', 'Customer does not exist'));

        $this->success(TrailRebuildTask::isBuildingNew($user->getClientId(),\Constants::TYPE_COMPANY, $company_id));
    }

    public function actionProductList($company_id, $directOwner = 1)
    {
        $this->validate([
            'company_id' => 'required|integer',
            'directOwner' => 'bool',
        ]);

        $user = User::getLoginUser();
        $clientId = $user->getClientId();
        $userId = $user->getUserId();

        $company = new Company($user->getClientId(), $company_id);
        $company->setOperatorUserId($user->getUserId());

        if (!$company->isExist())
            throw new RuntimeException(\Yii::t('customer', 'Customer does not exist'));

        if ($directOwner && !$company->checkOwner($user))
            throw new RuntimeException(\Yii::t('customer', 'Non-own data'));

        $api = new \common\library\customer\follow\CompanyFollowApi($clientId, $userId);
        $data = $api->productList($company_id);
        return $this->success($data);

//        $company->getFormatter()->allProductListSetting();
//        $this->success($company->getAttributes());
    }

    public function actionCheckFieldArchiveInfo($field,$value,$company_id=0){

        $user = User::getLoginUser();
        $data = \common\library\customer\Helper::checkFieldArchiveInfo($user->getClientId(),$field,$value,$company_id);

        $this->success($data);
    }

    public function actionSearchSetting(){
        $user = User::getLoginUser();
        $data = \common\library\customer\Helper::getUserSearchSetting($user->getUserId());
        $this->success($data);
    }

    /**
     * @return string
     * 根据clientId获取对应可使用的跟进类型
     */
    public function actionRemarkTypeList(){
        $user = User::getLoginUser();
        $trailTypeList = \common\library\lead\Helper::trailTypeList($user->getClientId());
        return $this->success($trailTypeList);
    }

    /**
     * @param string $company_hash_id
     * @param string $domain
     * @return string
     * 客户背景接口
     * //TODO Been 根据参数让前端调用actionBackgroundByDomain、actionBackgroundByHashId
     */
    public function actionClientBackground($company_hash_id = '', $domain = '')//e03babb73318bd11;marthatate.com
    {
        if(empty($company_hash_id) && empty($domain)){
            return $this->fail(-1, \Yii::t('common', 'Parameter error'));
        }

        $data = [];

        if (!empty($company_hash_id)){
            $data = \common\library\ai\service\RecommendService::getCompanyById($company_hash_id);
        } elseif (!empty($domain)){
            $data = \common\library\ai\service\RecommendService::getCompanyByDomain($domain);
        }

        empty($data) && $this->fail(-1, \Yii::t('common', 'No data'));
        return $this->success($data);
    }

    public function actionBackgroundByDomain($domain)
    {
        $domain = trim(strtolower($domain));
        if (\common\library\email\CommonDomain::check($domain)) {
            $this->fail(-1, \Yii::t('common', 'No data'));
        }

        $data = \common\library\ai\service\RecommendService::getCompanyByDomain($domain);
        empty($data) && $this->fail(-1, \Yii::t('common', 'No data'));
        return $this->success($data);
    }

    public function actionBackgroundByHashId($company_hash_id)
    {
        $data = \common\library\ai\service\RecommendService::getCompanyById($company_hash_id);
        empty($data) && $this->fail(-1, \Yii::t('common', 'No data'));
        return $this->success($data);
    }


    public function actionLastContactList($is_private = 1, $curPage = 1, $pageSize = 5, $filter_disable_fields = 0)
    {

        $this->validate([
            'pageSize'              => 'integer|min:1|max:100',
            'filter_disable_fields' => 'numeric|in:0,1',
        ]);

        $user = User::getLoginUser();
        $userId = $user->getUserId();

        $result = [
            'list' => [],
            'totalItem' => 0,
        ];

        if ($is_private) {

            $list = new CustomerList($user->getClientId());
            $list->setUserId($userId);
//            $list->getFormatter()->setFilterDisableFields($filter_disable_fields);
            $list->getFormatter()->setSpecifyFields(['name', 'email']);
            $list->setLimit($pageSize, $curPage);
            $list->setOrderBy(['order_time', 'customer_id']);
            $ret = $list->find();

            $ret = array_map(function ($customer) {
                $customer['card_type'] = \common\library\email_identity\EmailIdentity::CARD_TYPE_COMPANY;
                return $customer;
            }, $ret);

            $result['list'] = $ret;
            $result['totalItem'] = $list->count();
        } else {
            //先从队列拿出前20个联系人
            $key = 'linkman_recive_and_send_email_record_' . $userId;
            $redis = \RedisService::getInstance('redis');
            $record = $redis->get($key) ?: [];

            if (!empty($record)) {
                $emails = json_decode($record, true);

                if (empty($emails)) return $this->success($result);

                $emailIdentityList = new  \common\library\email_identity\EmailIdentityList($userId);
                $emailIdentityList->setEmails($emails);
                $emailIdentityList->getFormatter()->setSpecifyFields(['email', 'name', 'card_type']);
                $emailIdentityList = $emailIdentityList->find();
                $data = array_combine(array_column($emailIdentityList, 'email'), $emailIdentityList);
                $list = [];
                foreach ($emails as $email) {

                    if (isset($data[$email]) && $data[$email]['card_type'] == \common\library\email_identity\EmailIdentity::CARD_TYPE_COMPANY) {
                        continue;
                    }
                    $list[] = [
                        'name' => $data[$email]['name'] ?? '',
                        'email' => $email,
                        'card_type' => $data[$email]['card_type'] ?? \common\library\email_identity\EmailIdentity::CARD_TYPE_STRANGER,
                    ];
                }

                $result['list'] = $list;
                $result['totalItem'] = count($list);
            }
        }
        return $this->success($result);
    }

    public function actionCustomerList(
        $search_model = Constants::SEARCH_MODEL_SEARCHER,
        $keyword = '',
        $search_field = '',
        $pin = 0,
        array $status_id = [],
        array $country = [],
        $province = '',
        $city = '',
        array $biz_type = [],
        array $origin = [],
        array $group_id = [],
        array $pool_id = [],
        $start_date = '',
        $end_date = '',
        array $tags = [],
        array $user_num = [1, 2],
        array $star = [],
        array $category_ids = [],
        $curPage = 1,
        $pageSize = 20,
        $params_info = 0,
        $recent_select_flag = 0,
        $will_public = 0,
        $receive_period = 0,
        $get_count = 0,
        $last_owner = '',
        $company_field = '',
        $customer_field = '',
        $tag_match_mode = CompanyList::TAG_MATCH_MODE_SINGLE,
        $compare_day = 0,
        $compare_day_op = CompanyList::LESS_THAN,
        $acquired_company_day = 0,
        $show_all = 0,
        array $user_id = [],
        $archive_type = null,
        array $stage_type = [],
        $report_item_unique_key = '',
        array $last_owner_ids = [],
        $min_success_opportunity_count = '',
        $max_success_opportunity_count = '',
        $min_performance_order_count = '',
        $max_performance_order_count = '',
        $deal_time_start_date = '',
        $deal_time_end_date = '',
        $get_contract_list = 0,
        $main_customer_flag = 0,
        $query_email_flag = 0,
        $send_mail_day = 0,
        $had_send_mail = 0,
        $private_begin_time = '',
        $private_end_time = '',
        $public_begin_time = '',
        $public_end_time = '',
        $max_release_count = null,
        $min_release_count = null,
        array $annual_procurement =[],
        array $intention_level =[],
        $filter_empty_email = 1,
        $next_follow_up_begin_time = '',
        $next_follow_up_end_time = '',
        array $ali_store_id = [],
        $recent_follow_up_begin_time = '',
        $recent_follow_up_end_time = '',
        $gender = 0,
        $post = '',
        array $company_ids = [],
        $show_field_key = '',
        $duplicate_flag = null,
        array $origin_list = null,
        $filter_disable_fields = 0,
        $filter_forbidden = 0,
        $filter_suspected_invalid = 0
    )
    {
        $this->validate([
            'company_ids' => 'array',
            'company_ids.*' => 'integer',
            'curPage' => 'integer',
            'pageSize' => 'integer',
            'filter_disable_fields' => 'numeric|in:0,1',
        ]);

//	    $this->validate([
//		    'pageSize' => 'integer|min:1|max:100',
//	    ]);

        $user = User::getLoginUser();
        $userId = $user->getUserId();
        $clientId = $user->getClientId();
        $result = [
            'list' => [],
            'totalItem' => 0,
        ];

        //构造companyList参数
        $params = [];
        $method = new \ReflectionMethod(self::class, 'actionCustomerList');
        foreach ($method->getParameters() as $parameter) {
            $params[$parameter->name] = ${$parameter->name} ?? null;
        }
       // 鉴权，公海查看
        if ($show_field_key == \common\library\setting\user\UserSetting::PUBLIC_COMPANY_LIST_FIELD
            || $user_num == [0]
        ) {
            if(!\common\library\privilege_v3\Helper::hasPermission($clientId,$userId,
                PrivilegeConstants::PRIVILEGE_CRM_COMPANY_POOL_VIEW)){
                return $this->success($result);
            }
        } else {
            // 私海查看
            if (!\common\library\privilege_v3\Helper::hasPermission($clientId, $userId,
                PrivilegeConstants::PRIVILEGE_CRM_COMPANY_VIEW)) {
                return $this->success($result);
            }
        }
//        $companyList = new \common\library\customer\CompanyList($user->getUserId());
        $companyList = new CompanyList($user->getUserId());
//        $companyList->getFormatter()->setFilterDisableFields($filter_disable_fields);

        if (in_array($show_field_key, [\common\library\setting\user\UserSetting::PUBLIC_COMPANY_LIST_FIELD,
                                       \common\library\setting\user\UserSetting::PUBLIC_COMPANY_LIST_FIELD_WEB]) || $user_num == [0]) {

            $companyList->getFormatter()->setFieldFunctionalId(PrivilegeConstants::FUNCTIONAL_COMPANY_POOL);

            $companyList->getFormatter()->setFieldUserId($user->getUserId());
        }

        if (count($user_id)) {
            $companyList->setUserId($user_id);
        }
        if (count($company_ids)){
            $companyList->setCompanyIds($company_ids);
        }

        //可见范围为全部
        $companyList->setUserNum($user_num);
        $companyList->paramsMapping($params);
        $companyList->showAll(true);
        // 开启es缓存
        $companyList->setEnableListSearch(true);


        if (!$query_email_flag) {

            $result['privilege_field_stats'] = $companyList->getFormatter()->getPrivilegeFieldStats();
        }


        //join tbl_customer tbl_opportunity只能一个 通过Es查询字段也不能join Customer
        if ((empty($stage_type) || count($stage_type) == 4) && empty($keyword) && (empty($search_field) || $search_field == '[]') && (empty($company_field) || $company_field = '[]') && (empty($customer_field) || $customer_field == '[]')){
            //构造客户筛选条件的where join tbl_customer直接出结果
            $list = new CustomerList($user->getClientId());
//            $list->getFormatter()->setFilterDisableFields($filter_disable_fields);
            if (in_array($show_field_key, [\common\library\setting\user\UserSetting::PUBLIC_COMPANY_LIST_FIELD,
                                           \common\library\setting\user\UserSetting::PUBLIC_COMPANY_LIST_FIELD_WEB]) || $user_num == [0]) {

                $list->getFormatter()->setFieldFunctionalId(PrivilegeConstants::FUNCTIONAL_COMPANY_POOL);

                $companyList->getFormatter()->setFieldUserId($user->getUserId());
            }
            $list->setAlias('B');
            $list->setPost($post);
            $list->setGender($gender);
            $list->setMainCustomerFlag($main_customer_flag);
            $list->setFilterEmptyEmail($filter_empty_email ? true : false);
            $filter_forbidden && $list->setForbiddenFlag(Customer::FORBIDDEN_FLAG_NOT);
            $filter_suspected_invalid && $list->setSuspectedInvalidFlag(Customer::SUSPECTED_INVALID_FLAG_NOT);
            if ($send_mail_day > 0) {
                $list->setSendMailDayAndHadSendMail($send_mail_day, $had_send_mail);
            }
            [$customerWhere,$customerParams] = $list->buildParams(true, false);
            $companyList->setAlias('A');
            $companyList->joinCustomer('B', $customerWhere, $customerParams);


            $fields = ['B.customer_id', 'B.company_id', 'B.email', 'B.name', 'A.name as company_name', 'B.main_customer_flag', 'B.post','B.suspected_invalid_email_flag','B.forbidden_flag'];

            $companyList->setFields($fields);
            $companyList->setOrderBy(['A.name', 'B.customer_id']);
            $companyList->setOrder('ASC');
            $count = 0;
            if ($query_email_flag){
            
//                ？？？？？？
//                $companyList->setOffset(0);
//                $companyList->setOffset(30);
            } else {
                if ($filter_empty_email) {
                    $redis = \RedisService::cache();
                    $customerCountCacheKey = md5(json_encode([
                        'customerWhere' => $customerWhere,
                        'customerParams' => $customerParams,
                        'params' => array_diff_key($params, ['curPage' => '', 'pageSize' => ''])
                    ]));
                    $count = RedisService::getCacheData($redis, $customerCountCacheKey, 60, function() use ($companyList) {
                        return $companyList->count();
                    });
                } else {
                    $count = $companyList->count();
                }
                $companyList->setLimit($pageSize, $curPage);
            }
            $data = $companyList->find();
        } else {
            $alias = $companyList->getFormattedAlias();
            $companyList->setOrderBy(["{$alias}name"]);
            $companyList->setOrder('ASC');
            $companyList->setFields(["{$alias}company_id","{$alias}name"]);
            $data = $companyList->find();
            $companyIds = array_column($data, 'company_id');

            if (empty($companyIds)) {
                return $this->success($result);
            }

            $map = array_column($data, 'name', 'company_id');
            //根据companyids查询联系人
            $list = new CustomerList($user->getClientId());
//            $list->getFormatter()->setFilterDisableFields($filter_disable_fields);
            if (in_array($show_field_key, [\common\library\setting\user\UserSetting::PUBLIC_COMPANY_LIST_FIELD,
                                           \common\library\setting\user\UserSetting::PUBLIC_COMPANY_LIST_FIELD_WEB]) || $user_num == [0]) {

                $list->getFormatter()->setFieldFunctionalId(PrivilegeConstants::FUNCTIONAL_COMPANY_POOL);

                $list->getFormatter()->setFieldUserId($user->getUserId());
            }
            $list->setCompanyId($companyIds);
            $list->setMainCustomerFlag($main_customer_flag);
            $list->setLimit($pageSize, $curPage);
            $list->setAlias('c');
            $list->setGender($gender);
            $list->setPost($post);
            $list->joinSearch('x', $companyIds, 'company_id');
            $list->setOrderBy('x.sort,c.customer_id');
            $list->setOrder('asc');
            $list->setFilterEmptyEmail($filter_empty_email ? true : false);
            $filter_forbidden && $list->setForbiddenFlag(Customer::FORBIDDEN_FLAG_NOT);
            $filter_suspected_invalid && $list->setSuspectedInvalidFlag(Customer::SUSPECTED_INVALID_FLAG_NOT);
            $list->getFormatter()->setCompanyNameMap($map);

            $fields = ['customer_id', 'company_id', 'name', 'company_name', 'email','suspected_invalid_email_flag','forbidden_flag'];

            $list->getFormatter()->setSpecifyFields($fields);

            if ($send_mail_day > 0) {
                $list->setSendMailDayAndHadSendMail($send_mail_day, $had_send_mail);
            }
            $count = $list->count();
            if ($count == 0)
                return $this->success($result);

            if ($query_email_flag) {
                $list->setLimit(30, 0);
            }
            $data = $list->find();
        }

        $result['list'] = $query_email_flag == 1 ? array_column($data, 'email') : $data;
        $result['totalItem'] = $query_email_flag == 1 ? count($result['list']) : $count;

        return $this->success($result);
    }

    public function actionImportTaskList($type = Constants::TYPE_CUSTOMER, $curPage = 1, $pageSize = 5)
    {

	    $this->validate([
		    'pageSize' => 'integer|min:1|max:100',
	    ]);

        $user = User::getLoginUser();
        $list = new CustomerImportList($user->getUserId());
        $list->setType($type);
        $list->setOffset(($curPage - 1) * $pageSize);
        $list->setLimit($pageSize);

        return $this->success(array(
            'list' => $list->find(),
            'totalItem' => $list->count()
        ));
    }

    public function actionImportTaskInfo($task_id)
    {
        $model = CustomerImport::model()->find('task_id=:t', array(':t'=>$task_id));

        if (!$model)
        {
            throw new RuntimeException(\Yii::t('common', 'No such task'));
        }

        if ($model->user_id != User::getLoginUser()->getUserId())
        {
            throw new RuntimeException(\Yii::t('common', 'Non-own data'));
        }

        $info = $model->getAttributes();
        $info['succeed_count'] = 0;

        if ($model->status == CustomerImport::STATUS_IMPORT_FINISH)
        {
            $upload = new AliyunUpload();
            $upload->loadByFileId($model->result_file_id, User::getLoginUser()->getUserId());
            $importFile = new AliyunUpload();
            $importFile->loadByFileId($model->file_id, User::getLoginUser()->getUserId());
            $url = $upload->getFileUrl();
            $info['file_name'] = $importFile->getFileName();
            $info['succeed_count'] = $info['total_count'] - $info['fail_count'];
            $info['result_url'] = $url;
            $info['result_line'] = json_decode($info['result_line'], true);
        }

        return $this->success($info);
    }


    public function actionAvailableCount()
    {
        $user = User::getLoginUser();

        $limit = $user->getCustomerLimit();

        if ($limit == 0)
            return $this->success(-1);

        $quota = $user->getCompanyCountData()['total_quota'];

        $ret = $limit - $quota;

        return $this->success($ret < 0 ? 0 : $ret);
    }

	public function actionQuery($word, $search_field = '', $curPage = 1, $pageSize = 10) {

		$this->validate([
			'pageSize' => 'integer|min:1|max:100',
		]);


	    $word = strtolower(trim($word));
        $user = User::getLoginUser();

        $result = \common\library\customer\Helper::query($user->getClientId(), $user->getUserId(), $word,$search_field, $curPage, $pageSize);

        return $this->success($result);
    }

    public function actionCheckCompanyByTel($tel, $company_id)
    {
        $user = User::getLoginUser();

        $company = new Company($user->getClientId(), $tel);

        if($company->isExist())
            $this->success(empty($company_id) || $company->company_id != $company_id);
        else
            $this->success(false);
    }

    public function actionCustomerMatchRules(){
        $user = User::getLoginUser();
        $userRules = CustomerMatchRules::findUserMatchRules($user->getClientId(),$user->getUserId());
        $data = array();
        if(!$userRules){
            $data['biz_type'] = array();
            $data['category_ids'] = array();
            $data['scale_id'] = array();
            $data['country'] = array();
            $data['client_id'] = $user->getClientId();
            $data['user_id'] = $user->getUserId();
        }else{
            $data = $userRules->getAttributes();
            $scaleIds = $data['scale_id'];
            $scaleIds = array_map(function($v) {
                return "$v";
            },$scaleIds);
            $categoryIds = $data['category_ids'];
            $categoryIds = array_map(function($item){
                return is_array($item)? implode(',',$item) :"$item";
            },$categoryIds);

            $data['scale_id'] = $scaleIds;
            $data['category_ids'] = $categoryIds;

        }

        $this->success($data);
    }

    public function actionGroupCompanyList($group_id)
    {
        $user = User::getLoginUser();
        $clientId = $user->getClientId();
        $userId = $user->getUserId();

        $result = ['group_list' => []];

        if ($group_id)
        {
//            $groupList = new \common\library\group\GroupList($clientId, Constants::TYPE_COMPANY);
//            $groupList->setParentId($group_id);
//            $groupList->setIncludeSystemGroup(false);
//            $result['group_list'] = $groupList->find();

            $api = new \common\library\setting\library\group\GroupApi($clientId, Constants::TYPE_COMPANY);
            $result['group_list'] = $api->list($group_id, false);
        }

        $companyList = new CompanyList($userId);
        $companyList->setGroupId($group_id, false);
        $companyList->setUserNum([1, 2]);
        $companyList->setFields(['company_id', 'name']);
        $companyList->setOrderBy('mail_time');
        $companyList->setOrder('desc');
        $result['company_list'] = $companyList->find();

        return $this->success($result);
    }

    public function actionKeywordCompanyList($keyword)
    {
        $user = User::getLoginUser();
        $userId = $user->getUserId();

        $companyList = new CompanyList($userId);
        $companyList->setSearchFields(['name']);
        $companyList->setKeyword($keyword);
        $companyList->setUserNum([1, 2]);
        $companyList->setFields(['company_id', 'name']);
        $companyList->setOrderBy('mail_time');
        $companyList->setOrder('desc');

        return $this->success([
            'company_list' => $companyList->find()
        ]);
    }

    /**
     * @param $company_id
     * @return string
     * 新建日程获取关联客户名称
     */
    public function actionCompanyName($company_id)
    {
        $return = ['company_id' => 0, 'company_name' => ''];

        $user = User::getLoginUser();
        $company = new Company($user->getClientId(), $company_id);
        if ($company->isExist()){
            $return['company_id'] = $company->company_id;
            $return['company_name'] = $company->name ? $company->name : $company->short_name;
        }

        return $this->success($return);
    }


    public function actionExtendInfo($company_id, $directOwner = 1)
    {
        $user = User::getLoginUser();
        $clientId = $user->getClientId();
        $company = new Company($clientId, $company_id);
        if (! $company->isExist()) {
            throw new RuntimeException(\Yii::t('customer', 'No such customer'));
        }

        if ($directOwner && ! $company->checkOwner($user)) {
            throw new RuntimeException(\Yii::t('account', 'No permission'));
        }

        $data = $company->extendAttributes()->getAttributes()??[];
        $this->success($data);
    }

    public function actionAdviceList(
        $show_all = 0,
        array $user_id = [],
        $keyword = '',
        array $not_accept_type = [],
        $start_time = '',
        $end_time = '',
        array $advice_id = [],
        array $emails = [],
        $trust_level = null,
        $curPage = 1,
        $pageSize = 20,
        $sort_field = 'order_time',
        $sort_type = 'desc'
    )
    {

	    $this->validate([
		    'show_all'        => 'bool',
		    'keyword'         => 'string|max:256',
		    'user_id'         => 'array',
		    'user_id.*'       => 'integer',
		    'emails'          => 'array',
		    'not_accept_type' => 'array',
		    'trust_level'     => 'integer',
		    'page'            => 'integer',
		    'pageSize'        => 'integer|min:1|max:100',
		    'sort_field'      => 'string|in:create_time,order_time,update_time',
		    'sort_type'       => 'string|in:asc,desc',
	    ]);

        $user = User::getLoginUser();
        $adviceList = new \common\library\ai\classify\customer\advice\CustomerAdviceList($user->getUserId());
        $adviceList->setShowAll($show_all);
        $adviceList->setUserId(!empty($user_id) ? $user_id : $user->getUserId());
        $adviceList->setNotAcceptType($not_accept_type);
        $adviceList->setKeyword($keyword);
        $adviceList->setStartTime($start_time);
        $adviceList->setEndTime($end_time);
        $adviceList->setAdviceId($advice_id);
        $adviceList->setEmails($emails);
        $adviceList->setTrustLevel($trust_level);
        $adviceList->setOrderBy($sort_field);
        $adviceList->setOrder($sort_type);
        $adviceList->setOffset(($curPage - 1) * $pageSize);
        $adviceList->setLimit($pageSize);

        $return = [
            'list' => $adviceList->find(),
            'totalItem' => $adviceList->count()
        ];

        return $this->success($return);
    }

    public function actionAdviceCount(
        $show_all = 0,
        array $user_ids = [],
        $keyword = '',
        array $not_accept_type = [0],
        $start_time = '',
        $end_time = '')
    {
        $user = User::getLoginUser();
        $adviceList = new \common\library\ai\classify\customer\advice\CustomerAdviceList($user->getUserId());
        $adviceList->setShowAll($show_all);
        $adviceList->setUserId(!empty($user_ids) ? $user_ids : $user->getUserId());
        $adviceList->setNotAcceptType($not_accept_type);
        $adviceList->setKeyword($keyword);
        $adviceList->setStartTime($start_time);
        $adviceList->setEndTime($end_time);

        return $this->success(['count' => intval($adviceList->count())]);
    }

    /**
     * 返回: 是否可以编辑该客户联系人邮箱
     * @param $company_id
     * @param $customer_id
     * @return bool
     */
    public function actionCanEditCustomerEmail($customer_id)
    {
        (new \common\library\validation\Validator(['customer_id' => $customer_id],['customer_id' => 'required|numeric']))->validate();

        $user = User::getLoginUser();
        $clientId = $user->getClientId();
        $customer = new Customer($clientId);
        $customer->loadById($customer_id);
        if ($customer->canEditEmail()) {
            return $this->success(true);
        }
        return $this->success(false);
    }

    public function actionCompanyPoolReceiveLimitRule()
    {
        $user = User::getLoginUser();
        $userId = $user->getUserId();
        $clientId = $user->getClientId();

        $showTips = 0;
        $limits = 0;
        $leftCounts = 0;
        $receiveCounts = 0;

        $reciveRule = \common\library\customer\public_record\Helper::checkUserHasAssigndRule($userId, $clientId);
        if (!empty($reciveRule)) {
            $showTips = 1;
            //获取限制总额度
            $limits = $reciveRule['limits'];
            //查询当前周期内的总个数
            $receiveCounts = \common\library\customer\public_record\Helper::findCompanyCountByPeriod($userId, $clientId, $reciveRule['period']);
            //剩下的额度
            $leftCounts = $limits - $receiveCounts;
        }

        $res = [
            'show_tips' => $showTips,
            'limits' => $limits,
            'left_counts' => $leftCounts,
            'period' => $reciveRule['period']??0,
            'receive_counts' => $receiveCounts
        ];

        return $this->success($res);
    }

    public function actionAddressList($company_id)
    {
        $user = User::getLoginUser();

        $fieldList = \common\library\custom_field\CustomFieldService::getFieldListByType($user->getClientId(),0,Constants::TYPE_COMPANY, \common\library\custom_field\CustomFieldService::FIELD_TYPE_TEXTAREA);
        $fieldMap = array_column($fieldList,null, 'name');

        $addressField = '客户地址';
        if (!isset($fieldMap[$addressField])){
            return $this->success([]);
        }

        $company = new Company($user->getClientId(), $company_id);
        if ($company->isExist()) {
            $externalId = $fieldMap[$addressField]['id'];
            $addressList = $company->getAttributes()['external_field_data'][$externalId] ?? '';
            $addressList = explode("\n", $addressList);
            $addressList = array_merge([$company->address], ($addressList?:[]));
            $addressList = array_values(array_unique(array_filter($addressList)));
        }

        return $this->success($addressList ?? []);
    }

    /**
     *检查是否选择了子分组
     * @param $group_id
     * <AUTHOR>
     */
    public function actionCheckSubGroupSelect($group_id)
    {
        $user = User::getLoginUser();
        \common\library\customer\Helper::checkSubGroupSelect($user->getClientId(), $group_id);
        $this->success([
            'select_flag' => 1
        ]);
    }


    public function actionGetOriginLeads($company_id){
        $user = User::getLoginUser();
        $clientId = $user->getClientId();

        $leadList =  \common\library\customer\Helper::getOriginLeads($clientId,$company_id);
        return $this->success(['list' => $leadList]);
    }

    /**
     * 检查是否有客户查看权限
     * @param $company_id
     * @return false
     */
    public function actionCheckOwner($company_id)
    {
        $user = User::getLoginUser();
        $company = new Company($user->getClientId(), $company_id);
        $result = $company->isExist() && $company->checkOwner($user);

        return $this->success(['is_owner' => $result]);
    }


    /** 客户管理的订单列表
     * @param array $user_type
     * @param array $status 订单状态
     * @param int$company_id 客户id
     * @param int $show_all 默认show_all=1 查看角色范围
     * @param array $create_user_id  创建人
     * @param string $sort_field 排序字段
     * @param string $sort_type  排序类型
     * @param string $settle_start 订单日期开始
     * @param string $settle_end 订单日期结束
     * @param int $page 页码
     * @param int $page_size 条数
     */
    public function actionCustomerOrderList(
        $company_id,
        $show_all = 1,
        array $create_user_id = [],
        array $user_type = [],
        array $status = [],
        $sort_field = '',
        $sort_type = '',
        $settle_start = '',
        $settle_end = '',
        $page = 1,
        $page_size = 20
    ) {


	    $this->validate([
		    'company_id'       => 'required|numeric',
		    'show_all'         => 'numeric',
		    'create_user_id'   => 'array',
		    'create_user_id.*' => 'numeric',
		    'status'           => 'array',
		    'status.*'         => 'numeric',
		    'user_type'        => 'array',
		    'user_type.*'      => 'numeric',
		    'sort_field'       => 'string',
		    'sort_type'        => 'string',
		    'settle_start'     => 'string',
		    'settle_end'       => 'string',
		    'page'             => 'numeric',
		    'page_size'        => 'numeric|min:1|max:100',
	    ]);

        $user = User::getLoginUser();
        $clientId = $user->getClientId();
        $userId = $user->getUserId();
        $status = array_filter($status);
        $listObj = new \common\library\invoice\OrderList($userId);
        $listObj->setClientId($clientId);
        $listObj->setViewingUserId($userId);
        $listObj->setUserType($user_type);
        $listObj->setStatus($status);
        $listObj->setSettleStart($settle_start);
        $listObj->setSettleEnd($settle_end);
        $listObj->setCompanyId($company_id);
        $listObj->setShowAll($show_all);
        $listObj->setAlias('t1');

        $listObj->setCashCollectAlias('cc_status');
        $listObj->setJoinCashCollection();
        if($create_user_id){
            $listObj->setCreateUser($create_user_id);
        }

        if ($sort_field && in_array($sort_type, ['asc', 'desc'])) {
            $listObj->setOrder($sort_type);
            if(in_array($sort_field,['collect_amount_usd','collect_amount_rmb'])){
                $listObj->setOrderBy('cc_status.'.$sort_field);
            }else{
                $listObj->setOrderBy($sort_field);
            }
        }
        $page = is_numeric($page) ? $page : 1;
        $listObj->setOffset(($page - 1) * $page_size);
        $listObj->setLimit($page_size);
        $listObj->formatter->companyOrderListSetting();

        $list = [];
        $count = $listObj->count();
        $summary = [];
        if ($count > 0) {
            $list = $listObj->find();
            $summaryList = $listObj->summary();
            if($summaryList){
                $agvAmountRmb = round($summaryList[0]/$count,2);
                $agvAmountUsd =  round($summaryList[1]/$count,2);
                $summary = [
                    'amount_rmb' => round($summaryList[0],2),
                    'amount_usd' =>  round($summaryList[1],2),
                    'agv_amount_rmb' => $agvAmountRmb,
                    'agv_amount_usd' => $agvAmountUsd,
                    'collect_amount_usd' => round($summaryList[2],2),
                    'collect_amount_rmb' => round($summaryList[3],2),
                ];
            }
        }
        $data = [
            'count' => $count,
            'list' => $list,
            'summary' => $summary
        ];


        $this->success($data);

    }

    /**
     * 客户关联订单，报价单，商机产品列表
     * @param $company_id
     * @param string $sort_field
     * @param string $sort_type
     * @param int $page
     * @param int $page_size
     * @param int $check_type //传1-查看spu 2-查看sku
     * @throws ProcessException
     */
    public function actionRelatedProductList(
        $company_id,
        $sort_field = 'invoice_count',
        $sort_type = 'desc',
        $page = 1,
        $page_size = 5,
        $check_type = ProductConstant::PRODUCT_TYPE_SKU
    ){

	    $this->validate([
		    'company_id' => 'required|integer',
		    'page'       => 'integer',
		    'page_size'  => 'integer|min:1|max:100',
		    'sort_field' => 'regex:/^\w{2,32}$/',
		    'sort_type'  => 'string|in:asc,desc',
		    'check_type' => 'integer',
	    ]);

        $user = User::getLoginUser();
        $clientId = $user->getClientId();

        if (!in_array($check_type, [ProductConstant::PRODUCT_TYPE_SPU, ProductConstant::PRODUCT_TYPE_SKU])) {
            throw new RuntimeException(\Yii::t('common', 'Parameter error'));
        }

        if(!in_array($sort_field,['invoice_count','quotation_count','opportunity_count'])){
            $sortField = 'invoice_count';
        }else{
            $sortField = $sort_field;
        }
        $page = intval($page);
        $page_size = intval($page_size);

        list($list, $count) = \common\library\customer\Helper::getRelatedProductList($clientId, $company_id, $sortField, $sort_type, $page, $page_size, $check_type);
        $data = [
            'count' => $count,
            'list' => $list,
        ];

        $this->success($data);

    }

    public function actionDuplicateList($company_id, $limit = 5, $only_editable = 0)
    {
        $user = User::getLoginUser();
        $match = new \common\library\custom_field\company_field\duplicate\CompanyMatchProcess($user->getClientId());
        $matchedRules = $match->matchById($company_id);
        $companyList = \common\library\customer\Helper::companyFieldConflictList($user->getUserId(),
            $matchedRules['matched_rules']??[], false, [1,2], $limit);

        if ($only_editable) {
            $companyList = array_filter($companyList, fn($it) => isset($it['is_editable']) && empty($it['is_editable']));
        }

        return $this->success($companyList);
    }


	/**
	 * @param int $file_id
	 * @return void
	 * @throws Exception
	 */
	public function actionFtAccountList($file_id = 0) {

		$user = User::getLoginUser();

		if (!$user->isSuperAdmin()) {

			return $this->success([]);
		}

		$result = \common\library\customer\Helper::getFtAccountList($file_id, $user->getUserId());

		$this->success(array_values($result));
	}

    public function actionImportFieldList($public_flag = 1)
    {
        $user = User::getLoginUser();

        $result = \common\library\import\Helper::getImportFieldList(\Constants::TYPE_COMPANY, $user->getUserId(), $user->getClientId(), $public_flag);

        $this->success($result);
    }

    public function actionImportMapList($file_id = 0, $public_flag = 1)
    {
        $user = User::getLoginUser();

        $result = \common\library\import\Helper::getImportMapList($file_id, \Constants::TYPE_COMPANY, $user->getUserId(), $user->getClientId(), $public_flag);

        $this->success($result);
    }

    public function actionExportMapList()
    {
        $user = User::getLoginUser();

        $result = \common\library\customer\Helper::getExportMapList($user->getUserId(), $user->getClientId());

        $this->success($result);
    }

    public function actionQuestionnairePopup($type)
    {
        $this->validate([
            'type' => 'required|integer',
        ]);
        $user = User::getLoginUser();
        $questionnaire = new \common\library\customer\Questionnaire($user->getClientId(), $user->getUserId());
        $result = $questionnaire->easyPopup($type);

        $this->success($result);
    }

    /**
     * 问卷弹窗确认接口
     * @param $type
     */
    public function actionQuestionnairePopupConfirm($type)
    {
        $this->validate([
            'type' => 'required|integer',
        ]);
        $user = User::getLoginUser();
        $questionnaire = new \common\library\customer\Questionnaire($user->getClientId(), $user->getUserId());
        $result = $questionnaire->confirmPopup($type);

        $this->success($result);
    }

    /**
     * 问卷弹窗开关转换接口(不提供给前端)
     * @param $type
     * @param $value 0:开启 1：关闭
     */
    public function actionQuestionnairePopupSwitch($type, $value)
    {
        $this->validate([
            'type' => 'required|integer',
            'value' => 'required|integer|in:0,1',
        ]);
        $user = User::getLoginUser();
        $questionnaire = new \common\library\customer\Questionnaire($user->getClientId(), $user->getUserId());
        $result = $questionnaire->switchPopup($type, $value);

        $this->success($result);
    }

    public function actionQuestionnairePopupReset($type, $clientId, $userId)
    {
        $this->validate([
            'type' => 'required|integer',
            'clientId' => 'required|integer',
            'userId' => 'required|integer',
        ]);
        $user = User::getLoginUser();
        $questionnaire = new \common\library\customer\Questionnaire($user->getClientId(), $user->getUserId());
        $result = $questionnaire->resetPopup($type, $clientId, $userId);

        $this->success($result);
    }

    public function actionProductGroupCompanyCount($product_group_id)
    {
        $this->validate([
            'product_group_id' => 'required|integer',
        ]);
        $user = User::getLoginUser();

        $count = \common\library\customer\Helper::getProductGroupCompanyCount($user->getUserId(), $user->getClientId(), $product_group_id);

        return $this->success($count);
    }

    /**
     * ames客户详情-销售订单列表，数据由底座接口提供
     * @param $company_id
     * @param $sort_field
     * @param $sort_type
     * @param $cur_page
     * @param $page_size
     * @return void
     */
    public function actionCustomerAmesOrderList(
        $company_id = 0,
        $sort_field = 'GMT_CREATE',
        $sort_type = 'desc',
        $cur_page = 1,
        $page_size = 20
    )
    {

	    $this->validate([
		    'cur_page'   => 'integer',
		    'page_size'  => 'integer|min:1|max:100',
		    'sort_field' => 'string|in:GMT_CREATE,ORDER_AMOUNT',
		    'sort_type'  => 'string|in:asc,desc',
		    'company_id' => 'required|numeric',
	    ]);

        $sort_type = $sort_type == 'desc' ? false : true;

        $list = new \common\library\ames\service\AmesOrderList($this->getLoginUserId());
        $list->setCompanyId($company_id);
        $list->setSortType($sort_type);
        $list->setSortField($sort_field);
        $list->setCurPage($cur_page);
        $list->setPageSize($page_size);
        $rsp = $list->find();
        $listData = $rsp['result']['data']['order_base_xm_response'] ?? [];
        $total = $rsp['result']['total'] ?? 0;
        $listData = \common\library\ames\Helper::formatOrderList($listData);

        $this->success(['list'  => $listData, 'count' => $total]);
    }

    /**
     * Facebook沟通卡片 匹配联系人接口
     *
     * @param string $email
     * @param array<string> $tel
     * @return false|string
     */
    public function actionSearchCustomer(string $email = '', array $tel = [])
    {
        $this->validate([
            'email' => 'string|required_if:tel,',
            'tel'  => 'array|required_if:email,',
            'tel.*'  => 'string',
        ]);
        $tel = array_filter($tel);
        $user = User::getLoginUser();
        $searcher = new FacebookCompanySearchList();
        $searcher->setClientId($user->getClientId());
        $searcher->setUserId($user->getUserId());
        $searcher->setLimit(10);
//        $searcher->setKeywordFields(['customer_list.email']);
        $fields = [];
        if ($email) {
            $fields['customer_list.email'] = $email;
        }
        if ($tel) {
            $fields['customer_list.tel'] = $tel;
        }
        $searcher->setQueryFields($fields);
        $result = $searcher->find();
        return $this->success(['companies' => $result]);
    }

    /**
     * 导出业务指标
     * @return void
     */
    public function actionExportBusinessTarget()
    {
        $login_user = User::getLoginUser();
        $user_id = $login_user->getUserId();
        $client_id = $login_user->getClientId();
        //导出近三个月T+1的数据
        $endDate = date('Y-m-d', strtotime('-1 days'));
        $startDate = date('Y-m-01', strtotime('-2 months',strtotime('-1 days')));
        $params = [
            'start_date' => $startDate,
            'end_date' => $endDate,
            'type' => CustomerExportTask::TYPE_BUSINESS_TARTGET
        ];

        $params = http_build_query($params);
        parse_str($params, $lockParams);

        $lockKeyArr = array();
        $lockKeyArr['param'] = $lockParams;
        $lockKeyArr['operator'] = intval($user_id);
        $searchExportKey = md5(serialize($lockKeyArr));
        $searchExportRes = \Yii::app()->redis->get($searchExportKey);
        if ($searchExportRes) {
            throw new RuntimeException(\Yii::t('file', 'Report generation may take a while'));
        }

        //导出任务正在进行中 不可继续导出
        $type = CustomerExportTask::TYPE_BUSINESS_TARTGET;
        $status = implode(',',[CustomerExport::STATUS_INIT, CustomerExport::STATUS_EXPORT_BEGIN]);
        $goingTask = CustomerExport::model()->find("client_id={$client_id} and user_id={$user_id} and type={$type} and status in ({$status})");
        if ($goingTask) {
            throw new RuntimeException(\Yii::t('common', 'The task is going'));
        }

        $task = new CustomerExportTask();
        $res =  $task->init($client_id, $user_id, $params, CustomerExportTask::TYPE_BUSINESS_TARTGET);

        $task_id = null;
        $task_init = 0;
        if($res){
            $task_id = $task->getTaskId();
            $task_init = 1;
            //给导出的内容上锁,避免10分钟内重复导出
            \Yii::app()->redis->set($searchExportKey, $user_id, 600);
        }

        $log = '/tmp/business_target.log';
        \common\library\CommandRunner::run(
            'export',
            'multiExport',
            [
                'task_id' => $task_id,
                'operator_id' => $user_id,
                'type' => CustomerExportTask::TYPE_BUSINESS_TARTGET,
                'params' => "'$params'",
            ],
            $log
        );

        $this->success($task_init);
    }

    public function actionTrailInfo(
        $company_id,
        $trail_id
    )
    {
        (new \common\library\validation\Validator(
            compact('company_id', 'trail_id'),
            [
                'company_id' => 'required|numeric',
                'trail_id' => 'required|numeric'
            ]
        ))->validate();

        $user = User::getLoginUser();

        $trail = new \common\library\trail\entities\DynamicTrail($user->getClientId(), $trail_id);

	    if (empty($trail->trail_id)) {

		    return $this->success([]);
	    }

		$trail->initFormatter();
	    $trail->getFormatter()->setOperator($user->getUserId());
	    $trail->getFormatter()->setShowCaptureCard(true);
        $trail->getFormatter()->setShowOpportunityInfo(true);
        $trail->getFormatter()->setShowCommentList(true);
        $result = $trail->getAttributes();

        return $this->success($result);
    }

    public function actionMailPreviousNext(
        $company_id,
        $mail_id,
        $customer_id = '',
        array $customer_ids = [],
        $begin_time = '',
        $end_time = '',
        $create_user = null,
        $keyword = '',
        $pageSize = 1
    )
    {
        $this->validate([
            'pageSize'   => 'integer|min:1|max:100',
            'company_id' => 'required|numeric',
            'mail_id' => 'required|numeric',
        ]);

        $user = User::getLoginUser();
        $data = [
            'previous' => [],
            'next' => [],
            'total' => 0
        ];

        $list = new CompanyDynamicList($user->getClientId(), $company_id);
        $list->setOperatorUserId($user->getUserId());
        $list->setModuleId(2);
        $list->setOffset(0);
        $list->setLimit($pageSize);

        if (!empty($begin_time)) {
            $list->setBeginTime($begin_time);
        }
        if (!empty($end_time)) {
            $list->setEndTime($end_time);
        }

        if (!empty($create_user)) {
            $list->setCreateUser($create_user);
        }

        if (!empty($customer_id)) {
            $list->setCustomerId($customer_id);
        }

        if(!empty($customer_ids)){
            $list->setCustomerId($customer_ids);
        }

        if (!empty($keyword)) {
            $list->setKeyword($keyword);
        }

        $mail = new \common\library\mail\Mail($mail_id);
        $total = $list->count();
        $list->setMidDateTime($mail->receive_time);
        $data['next'] = array_column($list->find(), 'data');

        $list->setOrder('asc');
        $data['previous'] = array_column($list->find(), 'data');

        $filterRefer = function($data) {
            $refers = [];
            foreach ($data as $item) {
                if (!empty($item) && empty($item['delete_flag']) && !empty($item['user_id'])) {
                    $refers[] = [
                        'mail_id' => $item['mail_id'],
                        'user_id' => $item['user_id']
                    ];
                }
            }
            return $refers;
        };

        $list = [
            'next' => $filterRefer($data['next']),
            'previous' => $filterRefer($data['previous']),
            'total' => $total
        ];

        $this->success($list);
    }

    public function actionCommentList(int $trail_id) {

        $user = User::getLoginUser();

        $list = new  \common\library\trail\comment\DynamicTrailCommentList($user->getClientId());

        $list->setTrailId([$trail_id]);

        $list = $list->find();

        return $this->success($list);
    }

    public function actionCustomerV2Upgrade()
    {
        $user = User::getLoginUser();
        $clientId = $user->getClientId();
        $userId = $user->getUserId();

        $setting = new UserSetting($clientId, $userId, UserSetting::CUSTOMER_V2_UPGRADE_GUIDE);
        if(isset($setting->enable_flag) && $setting->enable_flag == 0) {
            $this->success(0);
        }
        $data = $setting->getValue();
        $attribute = Client::getClient($clientId)->getExtentAttributes([Client::EXTERNAL_KEY_CUSTOMER_INFO, Client::EXTERNAL_KEY_COMPANY_SWARM]);
        $flag = (!$attribute[Client::EXTERNAL_KEY_CUSTOMER_INFO]) && $attribute[Client::EXTERNAL_KEY_COMPANY_SWARM];
        if($data === null){
            $data = (int)$flag;
            $setting->value = $data;
            $setting->save();
            $this->success($data);
        }

        if(strtotime( 'this week Monday' ,time()) > strtotime($setting->update_time) && $flag){
            $data = 1;
            $setting->value = $data;
            $setting->save();
        }

        $this->success($data);
    }

    /**
     * @throws ProcessException
     * @throws CDbException
     * @throws CException
     * @throws CException
     */
    public function actionSearchRemarkRelateBusiness($companyId, $keyword = '', $referType = 0, $sortType = 'desc', $curPage = 1, $pageSize = 20)
    {
        $this->validate([
            'companyId' => 'required|numeric',
        ]);

        $user = User::getLoginUser();
        $clientId = $user->getClientId();
        $userId = $user->getUserId();
        $search = new SearchRelateBusinessService($clientId, $userId, \Constants::TYPE_COMPANY, $companyId);

        if(!empty($keyword)){
            $search->setKeyword($keyword);
        }

        if(!empty($referType)){
            $search->setRelateType($referType);
        }

        if(!empty($sortType)){
            $search->setSortType($sortType);
        }
        $search->setCurPage($curPage);
        $search->setPageSize($pageSize);

        $result = $search->searchAll();

        $this->success($result);


    }

    // 这里传递 $pageType 而不是直接传 layoutId 是为了查询布局配置接口和查询数据的接口并行，减少网络请求时间，加快响应
    public function actionCompanyLayoutInfo($company_id, $page_type = \common\library\layout\LayoutConstants::LAYOUT_PAGE_TYPE_DETAIL, $directOwner = 1, $filter_disable_fields = 0, $skip_view_privilege = 0){
        $user = User::getLoginUser();
        $userId = $user->getUserId();
        $clientId = $user->getClientId();

        // 布局规则控制
        $layoutApi = new \common\library\layout\LayoutApi($clientId, \common\library\object\object_define\Constant::OBJ_COMPANY, $page_type, \Constants::CLIENT_TYPE_WEB);
        $layoutRules = $layoutApi->controlAction($userId, $company_id);
        $data = json_decode('{"3570201741":"A","3570201742":["A","B"],"name":"客户名称","name_info":{"info_value":"客户名称","info_lable":"客户名称"},"3570201741_info":{"info_value":"A","info_lable":"A"},"group_id":1960734798,"group_id_info":{"info_value":1960734798,"info_lable":"分组1"},"image_list":[{"file_id":2342987098},{"file_id":2342987099}],"image_info":[{"info_value":2342987098,"info_lable":"文件名.txt","info_props":{"field_ext":"","file_size":11,"file_url":"","file_preview_url":"","download_url":"","preview_url":"","create_time":"","user_id":*********}}],"tag":[{"tag_color":"#209890","tag_id":"2959706840","tag_name":"test1","user_id":"11858712","system_flag":"0","create_time":"2022-04-0211:22:02","user_info":{"user_id":"11858712","nickname":"qiao"}},{"tag_color":"#E43E3E","tag_id":"1102360153","tag_name":"裕丰国际","user_id":"11858712","system_flag":"0","create_time":"2020-02-2815:16:52","user_info":{"user_id":"11858712","nickname":"qiao"}}],"trail_status":1105890198,"trail_status_info":{"info_value":"1105890198","info_lable":"draft","info_posps":{"color":"#188ae8"}},"user_id":[*********,*********],"user_id_info":[{"info_value":*********,"info_lable":"onegong43"},{"info_value":*********,"info_lable":"onegong43-1"}],"3570201742_info":[{"info_value":"A","info_label":"A"},{"info_value":"B","info_label":"B"}]}');


        return $this->success([
            'layout_rules'=> $layoutRules,
            'data' => $data
        ]);
    }


    /**
     * 根据邮箱 获取联系人地址
     * @param $email string 买家联系人邮箱
     * @param $seller_account_id int 阿里卖家ID
     * @return
     */
    public function actionGetAliContactAddressByEmail($email, $seller_account_id)
    {
        $this->validate([
            'email' => 'required',
            'seller_account_id' => 'required|int',
        ]);

        $user = User::getLoginUser();
        $clientId = $user->getClientId();

        $alibabaAccount = new \common\library\alibaba\oauth\AlibabaAccount($clientId);
        $alibabaAccount->loadBySellAccountId($seller_account_id);
        if ($alibabaAccount->isNew()) {
            throw new RuntimeException('选择的国际站用户不存在');
        }

        if ($alibabaAccount->enable_flag != 1 || !$alibabaAccount->access_token) {
            throw new RuntimeException('选择的国际站用户未授权');
        }

        $shipping = new \common\library\alibaba\services\AlibabaShipping($alibabaAccount->access_token);
        $addresses = $shipping->getAddressListOfBuyer($email);

        return $this->success($addresses);
    }


    /**
     * 获取国家、州、城市
     * @param $store_id
     * @param string $type
     * @param string|null $param
     * @return false|string|null
     */
    public function actionAliCountryRegions($store_id, string $type, string $param = null)
    {
        $this->validate([
            'store_id' => 'required|int',
            'type' => 'required|in:countryList,countryIso,provinceId',
        ]);

        $user = User::getLoginUser();
        $clientId = $user->getClientId();
        $alibabaStore = new \common\library\alibaba\store\AlibabaStore($clientId, $store_id);
        if($alibabaStore->enable_flag != 1 || !$alibabaStore->access_token){
            throw new RuntimeException('店铺没有授权');
        }

        $shipping = new \common\library\alibaba\services\AlibabaShipping($alibabaStore->access_token);
        $data = $shipping->getCountryAndRegions($type, $param);
        return $this->success($data);
    }

    /**
     * 根据国家码获取
     * @param $store_id
     * @param $country_code
     * @return false|string|null
     */
    public function actionAliAddressFormSchema($store_id, $country_code, $language = 'zh_CN')
    {
        $this->validate([
            'store_id' => 'required|int',
            'country_code' => 'required',
            'language' => 'required|in:'. implode(',', OmsConstant::TIP_LANGUAGE_MAP),
        ]);

        $user = User::getLoginUser();
        $clientId = $user->getClientId();
        $alibabaStore = new \common\library\alibaba\store\AlibabaStore($clientId, $store_id);
        if($alibabaStore->enable_flag != 1 || !$alibabaStore->access_token){
            throw new RuntimeException('店铺没有授权');
        }

        $shipping = new \common\library\alibaba\services\AlibabaShipping($alibabaStore->access_token);
        $data = $shipping->getAddressFormSchema($country_code, $language);
        return $this->success($data);
    }

    public function actionGetClientCustomerLimitConfig()
    {
        $user = User::getLoginUser();
        $clientId = $user->getClientId();
        $client = \common\library\account\Client::getClient($clientId);
        $config = $client->getCustomerLimitConfig();

        return $this->success($config);
    }
}
