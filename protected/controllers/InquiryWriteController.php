<?php
/**
 * Created by PhpStorm.
 * User: young
 * Email: <<EMAIL>>
 * Date: 2022/3/30
 * Time: 18:53
 */

class InquiryWriteController extends Controller
{
    /**
     * 保存询盘跟进人
     * @param $site_id
     * @param int $lead_type 跟进类型
     * @param $lead_owner 跟进人ID
     */
    public function actionSaveLead($site_id,  $lead_owner, $lead_type = 0)
    {
        $this->validate([
            'site_id' => 'required|numeric',
            'lead_owner' => 'nullable',
            'lead_type' => 'nullable',
        ]);

        $user = User::getLoginUser();
        $clientId = $user->getClientId();
        $userId = $user->getUserId();
        $gaSite = new \common\library\google_ads\ga\GaSite($clientId, $site_id);

        if( !$gaSite->isExist() )
        {
            throw new RuntimeException(Yii::t('common','Data does not exist or deleted'));
        }

        //没公海权限不允许配置
        if($lead_type == \common\library\google_ads\GoogleConstants::SITE_LEAD_APPLY_TYPE_PUBLIC
            && !\common\library\privilege_v3\PrivilegeService::getInstance($clientId)
            ->hasFunctional(\common\library\privilege_v3\PrivilegeConstants::FUNCTIONAL_LEAD_POOL))
        {
            throw new RuntimeException(Yii::t('privilege', 'Missing permission'));
        }

        $gaSite->saveGaLead($lead_type, $lead_owner);
        LogUtil::error("user_id: {$userId} save lead config  lead_type : {$lead_type} lead_owner: {$lead_owner}");

        $this->success([
            'site_id' => $site_id
        ]);
    }
}