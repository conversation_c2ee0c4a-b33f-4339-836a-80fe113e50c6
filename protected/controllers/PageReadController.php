<?php

/**
 * Created by PhpStorm.
 * User: kai
 * Date: 16/9/9
 * Time: 上午11:02
 */

use common\library\account\Client;
use common\library\account\service\VersionService;
use common\library\mail\setting\general\MailSetting;
use common\library\privilege_v3\PrivilegeConstants;
use common\library\privilege_v3\PrivilegeService;
use common\library\setting\user\UserSetting;
use common\modules\internal\library\account\ClientService;

class PageReadController extends Controller
{
    public function filters()
    {
        return [
            'read'           => 'read',
            'login'          => 'login + nav,user,mail,notice,order,loginInfo,edm,leads,aiRecommend',
            'checkPrivilege' => 'checkPrivilege + user',
            'language'       => 'language',
        ];
    }

    public function actionJsInit()
    {
        $loginUser = User::getLoginUser();

        $data = [
            'fe_version' => '**************', // 前端版本信息，数据长时间未更新，机制已废弃，直接返回常量，保持接口一致,
            'tag'        => []
        ];

        if ($loginUser) {
            $logger = new PageViewLogger(Yii::app()->redis, $loginUser->getUserId(), date('Ymd'));
            $logger->log();

            //client相关
            $client = Client::getClient($loginUser->getClientId());
            $clientExtentAttrs = $client->getExtentAttributes();
            $diskOverLimitBuffer = $clientExtentAttrs[Client::EXTERNAL_KEY_DISK_OVER_LIMIT_BUFFER_DATE] ?? '';
            $version = intval($clientExtentAttrs[Client::EXTERNAL_KEY_CRM_VERSION] ?? 0);

            //如果没有审批功能，不需要审批
            if($client->mysql_set_id > 0 || !\common\library\privilege_v3\Helper::hasFunctional($loginUser->getClientId(),PrivilegeConstants::FUNCTION_REVIEW_BASE)){

                $needReview = (new UserSetting($loginUser->getClientId(), $loginUser->getUserId(),
                    UserSetting::MAIL_REVIEW))->getValue();

            }else{
                $needReview = 0;
            }

            //权限相关
            $privilegeService = PrivilegeService::getInstance($loginUser->getClientId(), $loginUser->getUserId());
            $privileges = [
                'system'     => $privilegeService->getSystemIds(false),
                'client'     => $privilegeService->getFunctionalIds(false),
                'privileges' => $privilegeService->getPrivileges(),
                'admin'      => $privilegeService->getAdminInfo(),
            ];
            $privileges = \common\library\privilege_v3\Helper::formatForOldVersion($loginUser->getClientId(), $privileges);

//            $permission = \common\library\privilege_v2\PrivilegePermission::getInstance($loginUser->getUserId());
//            $permission->checkAccess();
//            $privileges = $permission->getAccessPrivileges();
//            $privileges['admin'] = $permission->getAdminInfo();

            if ($privilegeService->checkFunctional(PrivilegeConstants::FUNCTIONAL_CRM)) {
                // 已开通crm
                $mbCount = 0; // 取tbl_msg_box因为接口已经废弃 所以固定返回0

                $mailSetting = new MailSetting($loginUser->getUserId());
                $mailSetting = $mailSetting->getAttributes();
            } else {
                //处理未开通crm的情况
                $mbCount = 0;
                $mailSetting = [];
            }

            $userExternalAttrs = $loginUser->getInfoObject()->getExtentAttributes([
                \common\library\account\external\UserInfoExternal::EXTERNAL_KEY_DEFAULT_CURRENCY,
                \common\library\account\external\UserInfoExternal::EXTERNAL_KEY_DEFAULT_EXCHANGE,
            ]);

            $user = [
                'user_id'                => $loginUser->getUserId(),
                'department'             => $loginUser->getDepartment(1),
                'client_id'              => $loginUser->getClientId(),
                'client_name'            => $client->name,
                'nickname'               => addslashes($loginUser->getNickname()),
                'avatar'                 => $loginUser->getAvatar(),
                'currency'               => $userExternalAttrs[\common\library\account\external\UserInfoExternal::EXTERNAL_KEY_DEFAULT_CURRENCY],
                'exchange'               => $userExternalAttrs[\common\library\account\external\UserInfoExternal::EXTERNAL_KEY_DEFAULT_EXCHANGE] ?: 'm_buy_pri',
                'disk_over_limit_buffer' => $diskOverLimitBuffer,
                'email'                  => $loginUser->getEmail(),
                'notice_count'           => $mbCount,
                'current_edm_count'      => $loginUser->getInfoObject()->getCurrentCount(),
                'last_login_info'        => $loginUser->getLastLoginEvent(),
                'family_name'            => $loginUser->getFamilyName(),
                'second_name'            => $loginUser->getSecondName(),
                'has_new_update_log'     => VersionService::hasNewUpdateLog($loginUser->getUserId()),
                'mobile_verify'          => $loginUser->getMobileVerify() ? 1 : 0
            ];

            $mail = [
                'is_bind_email'  => $loginUser->isBindEmail(),
                'user_mail'      => $loginUser->getUserMail() ?? [],
                'mail_settings'  => $mailSetting,
                'user_mail_list' => $loginUser->getUserMailIds(),
                'need_review'    => $needReview,
                'version'        => intval($mailSetting['version'] ?? MailSetting::NEW_MAIL_VIEW)
            ];

            $data['setting'] =  array_merge(
                $client->getExtentAttributes(array_merge([
                    Client::EXTERNAL_KEY_TRAIL_TYPE_SWITCH,
                    Client::EXTERNAL_KEY_IMPORT_ADDITION_USER,
                    Client::EXTERNAL_KEY_CUSTOMER_SHOW_PUBLIC_CONTACT,
                ], \common\library\privilege_v3\Helper::getSwitchList($loginUser->getClientId()))),
                [Client::SETTING_KEY_CURRENCY => $client->getMainCurrency()]
            );

            $mkPage = \common\library\marketing\account\AccountPurchasedUsed::getUsedLimit($loginUser->getClientId());

        } else {
            $user = [
                'user_id'                => 0,
                'client_id'              => 0,
                'client_name'            => '',
                'department'             => [],
                'nickname'               => '',
                'avatar'                 => '',
                'currency'               => '',
                'exchange'               => '',
                'disk_over_limit_buffer' => '',
                'notice_count'           => 0,
                'current_edm_count'      => 0,
                'last_login_info'        => [],
                'family_name'            => '',
                'second_name'            => '',
                'has_new_update_log'     => false,
                'mobile_verify'          => 0,
            ];

            $mail = [
                'is_bind_email'  => 0,
                'user_mail'      => [],
                'mail_settings'  => [],
                'user_mail_list' => [],
                'version'        => MailSetting::OLD_MAIL_VIEW
            ];
            $version = 1;
            $privileges = [
                'system'     => [],
                'client'     => [],
                'privileges' => [],
                'admin'      => []
            ];

            $mkPage = [
                'total' => 0,
                'used' => 0,
                'last' => 0
            ];
        }

        $data['user'] = $user;
        $data['mail'] = $mail;
        $data['privilege'] = [
            'version' => $version,
            'admin'   => $privileges['admin'],
            'client'  => $privileges['client'],
            'user'    => $privileges['privileges'],
            'system'  => $privileges['system']
        ];
        $data['mk_page'] = $mkPage;

        $this->success($data);
    }

    /**
     * 绕过检查，返回用户信息
     * - 目前只返回当前登陆态的user_id
     * @return false|string
     */
    public function actionLoginInfo()
    {
        $user = User::getLoginUser();
        return $this->success([
            'user_id' => $user->getUserId()
        ]);
    }

    public function actionUser($system_config = SystemConfig::CRM_FE_VERSION)
    {
        if ($system_config !== SystemConfig::SALES_FE_VERSION) {
            $system_config = SystemConfig::CRM_FE_VERSION;
        }
        $user = User::getLoginUser();
        $userId = $user->getUserId();
        $clientId = $user->getClientId();
        $userType = $user->getType();
        $logger = new PageViewLogger(Yii::app()->redis, $userId, date('Ymd'));
        $logger->log();

        //client相关
        $client = Client::getClient($clientId);
        $clientExtentAttrs = $client->getExtentAttributes();
        $diskOverLimitBuffer = $clientExtentAttrs[Client::EXTERNAL_KEY_DISK_OVER_LIMIT_BUFFER_DATE] ?? '';
        $crmVersion = intval($clientExtentAttrs[Client::EXTERNAL_KEY_CRM_VERSION] ?? 0);

        // shops 后台请求不提示云空间超量
        if ($this->systemModule == \common\library\privilege_v3\PrivilegeConstants::SYSTEM_MODULE_SHOP) {
            $diskOverLimitBuffer = '';
        }

        // okki_leads升级为ames，且为ames访问，返回小满通版本
        if (\common\library\privilege_v3\AmesHelper::isFromAmesWithOkkiLeads($clientId)) {
            $crmVersion = PrivilegeConstants::VERSION_CRM_AMES_BASIC;
        }

        $ftImport = intval($clientExtentAttrs[Client::EXTERNAL_KEY_IMPORTED_FROM_FU_TONG] ?? 0);

        /**
         * 没有DB的情况，需跳过client库查询
         */
        if ($client->mysql_set_id > 0) {
            //邮件使用版本
            $mailVersion = (new MailSetting($userId))->getMailVersion();
            $riskEventInfo = \common\library\risk\Helper::getRiskEventInfo($userId);
        } else {
            $mailVersion = 0;
            $riskEventInfo = [];
        }

        //权限相关
        $privilegeService = PrivilegeService::getInstance($clientId, $userId);

        //手机验证信息
        $mobileVerify = $user->getMobileVerify() ? 1 : 0;
        $env = Yii::app()->params['env'];
        $mobileVerify = $env == 'exp' ? 1 : $mobileVerify;  //exp环境手机特殊处理
        //安全登录相关
        $loginData= $user->getLoginData();
        $loginData['customData']['risk'] = $loginData['customData']['risk']??0;

        //阿里巴巴相关
        $alibabaService = \common\library\alibaba\AlibabaService::getInstance($clientId, $userId);

        $clientSettings = $client->getSettingAttributes([\common\library\account\Client::SETTING_KEY_CUSTOM_LOGIN_PAGE_FIELD]);
        $loginSetting = isset($clientSettings[\common\library\account\Client::SETTING_KEY_CUSTOM_LOGIN_PAGE_FIELD])?json_decode($clientSettings[\common\library\account\Client::SETTING_KEY_CUSTOM_LOGIN_PAGE_FIELD], true):null;

        //user info external 相关数据
        $userInfoExternalMap = $user->getInfoObject()->getExtentAttributes([
            \common\library\account\external\UserInfoExternal::EXTERNAL_KEY_ENTERPRISE_WECHAT_INFO,
        ]);

        $listClass = new \common\library\setting\user\UserSettingList($userId);
        $listClass->setKeys([
            UserSetting::USER_TIMEZONE,
            UserSetting::ASSISTANT_TM_PC_SWITCH,
            UserSetting::CUSTOMER_SWARM_SWITCH,
            UserSetting::CUSTOMER_DRAWER_SWITCH,
            UserSetting::LEADS_FULL_SCREEN_GUIDE,
            UserSetting::KNOWN_LEADS_LEGAL_RISK,
            UserSetting::AI_DEEP_SEEK_SWITCH,
        ]);
        $userSettingMap = $listClass->find();

//        $diskOverLimitBuffer = '';
        $data = [
            'env'                    => $env,
            'fe_version'             => '**************', // 前端版本信息，数据长时间未更新，机制已废弃，直接返回常量，保持接口一致
            'user_id'                => $userId,
            'eid'                    => intval($user->getInfoObject()->eid ?? 0),
            'user_eid'               => intval($user->getInfoObject()->user_eid ?? 0),
            'email'                  => $user->getRealEmail(),
            'nickname'               => addslashes($user->getNickname()),
            'avatar'                 => $user->getAvatar(),
            'family_name'            => $user->getFamilyName(),
            'second_name'            => $user->getSecondName(),
            'is_bind_email'          => $user->isBindEmail(),
            'client_id'              => $clientId,
            'client_name'            => $client->name,
            'type'                   => $user->getType(),
            'disk_over_limit_buffer' => $diskOverLimitBuffer,
            'has_new_update_log'     => VersionService::hasNewUpdateLog($userId),
            'mail_version'           => $mailVersion,
            'admin'                  => $privilegeService->getAdminInfo(),
            'admin_type'             => $user->getAdminType(),
            'user_language'          => \Yii::app()->language,
            'client_type'            => $client->client_type,
            'privilege'              => [
                'version' => $crmVersion,
                'user'    => $privilegeService->getPrivileges(),
                'list'    => array_merge(
                    $privilegeService->getPrivilegeIds(),
                    ($userType==\Constants::TYPE_COORDINATION_ACCOUNT?$privilegeService->getCoordinationFunctionalIds():$privilegeService->getFunctionalIds())
                ),
                'scope_type' => $privilegeService->hasAnyAdminScope() ? 1 : 0,
                'is_ames' => $privilegeService->isAmesUser(),
            ],
            'setting'                => array_merge(
                $client->getExtentAttributes(array_merge([
                    Client::EXTERNAL_KEY_TRAIL_TYPE_SWITCH,
                    Client::EXTERNAL_KEY_IMPORT_ADDITION_USER,
                    Client::EXTERNAL_KEY_CUSTOMER_SHOW_PUBLIC_CONTACT,
                    Client::EXTERNAL_KEY_GROUP_DISABLED,
                    Client::EXTERNAL_KEY_SHUFFLING_FIGURE,
                    Client::EXTERNAL_KEY_COMPANY_SWARM,
                    Client::EXTERNAL_KEY_COMPANY_SWARM_SWITCH,
                    Client::EXTERNAL_KEY_SALES_GUIDE_SWITCH,
                    Client::EXTERNAL_KEY_INIT_SALES_FLOW,
                    Client::EXTERNAL_KEY_PER_COMPANY_MAX_USER_NUM,
                    Client::EXTERNAL_KEY_ALLOW_EDIT_FOLLOWUP_QUICKTEXT,
                    Client::EXTERNAL_KEY_SHOW_PAYPAL,
                    Client::EXTERNAL_KEY_SHOW_YHX_CUSTOMERS,
                    Client::EXTERNAL_KEY_CUSTOMER_DRAWER,
                    Client::EXTERNAL_KEY_ERP_SERVICE_SWITCH,
                    CLient::EXTERNAL_KEY_AMES_SPECIAL_CRM_SHOW_FLAG,
                    Client::EXTERNAL_KEY_CUSTOMER_INFO,
                    Client::EXTERNAL_KEY_CUSTOMER_PAAS_SWITCH,
                    Client::EXTERNAL_KEY_ASSESS_DIMENSION_SWITCH,
                    Client::EXTERNAL_KEY_PERFORMANCE_RULE_LIMIT,
                    Client::EXTERNAL_KEY_LEADS_V2_MSG_SWITCH,
                    Client::EXTERNAL_KEY_AI_WRITE_SWITCH,
                    Client::EXTERNAL_KEY_AI_SUBJECT_SWITCH,
                    Client::EXTERNAL_KEY_AI_REPLY_SWITCH,
                    Client::EXTERNAL_KEY_MAIL_SEARCH_V2_SWITCH,
                    Client::EXTERNAL_KEY_LEADS_V2_MSG_SWITCH,
                    Client::EXTERNAL_KEY_OPPORTUNITY_V2_SWITCH,
                    Client::EXTERNAL_KEY_LEAD_V2_SWITCH,
                    Client::EXTERNAL_KEY_AI_INSIGHT_SWITCH,
                    Client::EXTERNAL_KEY_AI_ASSISTANT_SWITCH,
                    Client::EXTERNAL_KEY_AI_ASSISTANT_TM_SWITCH,
                    Client::EXTERNAL_KEY_CUSTOMER_PUBLIC_V2_SWITCH,
                    Client::EXTERNAL_KEY_MAIL_CONVERSATION_FUNCTION_SWITCH,
                    Client::EXTERNAL_KEY_ALI_MAIL_FLAG,
                    Client::EXTERNAL_KEY_MAIL_INDEXEDDB_SWITCH,
					Client::EXTERNAL_KEY_CUSTOMER_POOL_DUPLICATE_SWITCH,
					Client::EXTERNAL_KEY_CUSTOMER_FIELD_DUPLICATE_SWITCH,
					Client::EXTERNAL_KEY_ORIGIN_SETTING_SORT_MODE,
					Client::EXTERNAL_KEY_LEAD_SWITCH,
					Client::EXTERNAL_KEY_INQUIRY_DATA_SWITCH,
                    Client::EXTERNAL_KEY_AI_COMPANY_QC,
                    Client::EXTERNAL_KEY_AI_MAIL_AUTO_SUMMARY,
                    Client::EXTERNAL_KEY_AI_SDR_LEAD_ACTIVATION_ENABLE,
                    Client::SETTING_KEY_CLIENT_AI_REPORT_GENERATE,
                    Client::EXTERNAL_KEY_PRIVILEGE_SCOPE_USER_SWITCH,
                    Client::EXTERNAL_KEY_AI_WORKBENCH_SWITCH,
                ], \common\library\privilege_v3\Helper::getSwitchList($clientId))),
                [Client::SETTING_KEY_CURRENCY => $client->getMainCurrency()],
                ['show_custom_approval'=>$this->actionShowCustomApproval()],
                ['time_zone'=>$userSettingMap[UserSetting::USER_TIMEZONE]??null],
                [UserSetting::CUSTOMER_SWARM_SWITCH => 0],
                [UserSetting::CUSTOMER_DRAWER_SWITCH => 0],
                [\common\library\account\Client::SETTING_KEY_CUSTOM_LOGIN_PAGE_FIELD => $loginSetting],
                //TM桌面端开关,没有设置时默认开启
                [UserSetting::ASSISTANT_TM_PC_SWITCH => $userSettingMap[UserSetting::ASSISTANT_TM_PC_SWITCH]??1],
                [UserSetting::AI_DEEP_SEEK_SWITCH => json_decode($userSettingMap[UserSetting::AI_DEEP_SEEK_SWITCH] ?? '{}', true)],
                [Client::EXTERNAL_KEY_WORK_JOURNAL_FUNCTION_SWITCH => 1],
                $client->getSettingAttributes(
                    [
                        Client::SETTING_KEY_ORDER_LINK_TAB,
                        Client::SETTING_KEY_LEADS_SWITCH,
                        Client::SETTING_KEY_CASH_COLLECTION_CREATE,
                        Client::SETTING_KEY_TRANSFER_ALI_ORDER,
                    ]
                ),
                // 是否已知晓leads法务风险
                [UserSetting::KNOWN_LEADS_LEGAL_RISK => intval($userSettingMap[UserSetting::KNOWN_LEADS_LEGAL_RISK] ?? 0)],
            ),
            'mobile_verify'          => $mobileVerify,
            'risk_event_info'      => $riskEventInfo,
            'yearly_statistical_flag' => 1,//1代表 年度统计的活动打开。0或者删除是关闭
            'current_edm_count'      => $user->getInfoObject()->getCurrentCount(),
            'login_data' => [
                'loginTime' => $loginData['loginTime']??0,
                'loginIp' => $loginData['loginIp']??'',
                'customData' => $loginData['customData']??[],
            ],
            'is_trial' => $client->isTrial() ? 1 : 0,
            'alibaba_data' => $alibabaService->getAccountList(true),
            'alibaba_store' => $alibabaService->getStoreList(),
            'imported_from_fu_tong' => $ftImport,
            'ames_auth_status' => (int)($clientExtentAttrs[Client::EXTERNAL_KEY_AMES_AUTH_STATUS] ?? -1),
            Client::EXTERNAL_KEY_AMES_CAN_AUTH => (int)($clientExtentAttrs[Client::EXTERNAL_KEY_AMES_CAN_AUTH] ?? 0),//这里是因为在os创建client时，如果匹配不上信用代码，不会调用/internal/client/UpdateClientContractRelation接口更新这个key，所以凡是没更新的可以当成0处理
            'enterprise_wechat_info' => $userInfoExternalMap[\common\library\account\external\UserInfoExternal::EXTERNAL_KEY_ENTERPRISE_WECHAT_INFO],
            // 非独立售卖版leads 返回 0
            'leads_full_screen_guide' => $crmVersion == PrivilegeConstants::VERSION_OKKI_LEADS ? $userSettingMap[UserSetting::LEADS_FULL_SCREEN_GUIDE] : 0, // null 非leads引导二期上线后激活的leads独立售卖版账号 1 leads未完成leads二期全屏引导 0 已完成leads二期全屏引导
            // 安全5期 需要返回主账号
            'master_account' => $client->master_account,
            'whatsapp_cloud_functional_expire_time' => \common\library\whatsapp_cloud\Helper::getFunctionalExpireTime($clientId)
        ];
        // 做个兼容
        if (isset($data['setting'][Client::EXTERNAL_KEY_CUSTOMER_FIELD_DUPLICATE_SWITCH])) {
            $data['setting'][Client::EXTERNAL_KEY_CUSTOMER_FIELD_DUPLICATE_SWITCH] = (string)$data['setting'][Client::EXTERNAL_KEY_CUSTOMER_FIELD_DUPLICATE_SWITCH];
        }

        // TODO: AI客户画像算法查看效果，暂时关闭beta环境的PAAS配置
        $betaNoPaasClients = explode(',',getenv('BETA_NO_PAAS_CLIENTS')?:'') ?? [];
        if (\Yii::app()->params['env'] == 'beta' && in_array($clientId, $betaNoPaasClients)) {
            $data['setting'][Client::EXTERNAL_KEY_CUSTOMER_PAAS_SWITCH] = 0;
        }

        $data['setting'][Client::EXTERNAL_KEY_AI_MAIL_AUTO_SUMMARY] = isset($data['setting'][Client::EXTERNAL_KEY_AI_MAIL_AUTO_SUMMARY]) ?
            intval($data['setting'][Client::EXTERNAL_KEY_AI_MAIL_AUTO_SUMMARY]) : 0;

        $isShowSpecialField = $client->getExtentAttribute(Client::EXTERNAL_KEY_SPECIAL_SETTING_FIELD_SHOW);
        if (!$isShowSpecialField){
            $filterPrivilege = [
                PrivilegeConstants::PRIVILEGE_SETTING_ORIGIN_MANAGE,
                PrivilegeConstants::PRIVILEGE_SETTING_MODULE_SWITCH_MANAGE,
                PrivilegeConstants::PRIVILEGE_CALL_VIEW_SUBORDINATE,
                PrivilegeConstants::PRIVILEGE_DX_ADMIN,
                //TODO MKT相关权限,待确认
                PrivilegeConstants::PRIVILEGE_MARKETING_INQUIRY_VIEW,
                PrivilegeConstants::PRIVILEGE_MARKETING_INQUIRY_DELETE,
                PrivilegeConstants::PRIVILEGE_MARKETING_INQUIRY_REPORT_VIEW,
                PrivilegeConstants::PRIVILEGE_MARKETING_INQUIRY_REPORT_EXPORT,
                PrivilegeConstants::PRIVILEGE_MARKETING_SITE_MANAGE_NOTIFICATION,
                PrivilegeConstants::PRIVILEGE_MARKETING_INQUIRY_RECYCLE,
            ];
            $data['privilege']['list'] = array_values(array_diff($data['privilege']['list'],$filterPrivilege));
        }

        $referer = \Yii::app()->getRequest()->getUrlReferrer();
        if (!empty($referer)){ // shop页面的会话 不显示 AI
            $parsedUrl = parse_url($referer);
            $params = [];
            parse_str($parsedUrl['query'] ?? '', $params);
            if (($params['isShops'] ?? 0) == 1) {
                $filterPrivilege = [PrivilegeConstants::FUNCTIONAL_OKKI_AI];
                $data['privilege']['list'] = array_values(array_diff($data['privilege']['list'],$filterPrivilege));
            }
        }

        if (isset($data['setting'][Client::EXTERNAL_KEY_COMPANY_SWARM]) && !empty($data['setting'][Client::EXTERNAL_KEY_COMPANY_SWARM])) {
            $data['setting'][UserSetting::CUSTOMER_SWARM_SWITCH] = 1;
        }

        if (in_array(PrivilegeConstants::FUNCTIONAL_CUSTOMER, $data['privilege']['list']) && empty($data['setting'][Client::EXTERNAL_KEY_CUSTOMER_PUBLIC_V2_SWITCH])) {
            $data['setting'][Client::EXTERNAL_KEY_CUSTOMER_PUBLIC_V2_SWITCH] = 1;
        }

        $data['setting'][Client::EXTERNAL_KEY_LEADS_V2_MSG_SWITCH] = $client->getExtentAttributes([Client::EXTERNAL_KEY_LEADS_V2_MSG_SWITCH])[Client::EXTERNAL_KEY_LEADS_V2_MSG_SWITCH] ?? 0;
        $leadsV1funcFlag = \common\library\privilege_v3\PrivilegeService::getInstance($clientId, $userId)
            ->hasFunctional(\common\library\privilege_v3\PrivilegeConstants::FUNCTIONAL_OKKI_LEADS_BASE);
        $leadsV2funcFlag = \common\library\privilege_v3\PrivilegeService::getInstance($clientId, $userId)
            ->hasFunctional(\common\library\privilege_v3\PrivilegeConstants::FUNCTIONAL_LEADS_MENU_V2);
        if(!$leadsV1funcFlag || $leadsV2funcFlag ){
            $data['setting'][Client::EXTERNAL_KEY_LEADS_V2_MSG_SWITCH] = 0;
        }

        if (!empty($data['setting'][UserSetting::CUSTOMER_SWARM_SWITCH])) {
            $data['setting'][Client::EXTERNAL_KEY_CUSTOMER_DRAWER] = 1;
            $data['setting'][UserSetting::CUSTOMER_DRAWER_SWITCH] = 1;
//            $data['setting'][Client::EXTERNAL_KEY_CUSTOMER_INFO] = 1; //等全量
        } else {
            $data['setting'][Client::EXTERNAL_KEY_CUSTOMER_INFO] = 0; //没客群，不能开启新版客户详情页
        }

        if ($env == 'exp') {
            $data['setting'][Client::EXTERNAL_KEY_ALI_MAIL_FLAG] = 0;
        }
        if ($user->isSupperPasswordLogin())
            $data['is_supper_password_login'] = true;

        $this->success($data);
    }

    public function actionMail()
    {
        $user = User::getLoginUser();
        $userId = $user->getUserId();
        if ($user->client()->mysql_set_id <= 0 ) {
            throw new RuntimeException(\Yii::t('common', 'No data'));
        }

        $mailSetting = new MailSetting($userId);

        $listClass = new \common\library\setting\user\UserSettingList($userId);
        $listClass->setKeys([UserSetting::MAIL_REVIEW,UserSetting::MAIL_TRANSLATE_SWITCH,UserSetting::MAIL_TIMEZONE,UserSetting::MAIL_ARCHIVE]);
        $userSettingMap = $listClass->find();

        $reviewFlag = 0;//审批标识，默认0，不需要审批
        //如果没有审批功能，不需要审批
        if(\common\library\privilege_v3\Helper::hasFunctional($user->getClientId(),PrivilegeConstants::FUNCTION_REVIEW_BASE)){
            $reviewFlag =  $userSettingMap[UserSetting::MAIL_REVIEW];
        }
        $data = [
            'version'               => $mailSetting->getMailVersion(),
            'mail_settings'         => $mailSetting->getAttributes(),
            'is_bind_email'         => $user->isBindEmail(),
            'user_mail'             => $user->getUserMail() ?? [],
            'user_mail_list'        => $user->getUserMailIds(),
            'need_review'           => $reviewFlag,
            'mail_translate_switch' => $userSettingMap[UserSetting::MAIL_TRANSLATE_SWITCH],
            'mail_timezone'         => $userSettingMap[UserSetting::MAIL_TIMEZONE],
            'mail_archive'         => $userSettingMap[UserSetting::MAIL_ARCHIVE],
        ];

        $this->success($data);
    }


    public function actionOrder()
    {
        $user = User::getLoginUser();
        $userId = $user->getUserId();
        $clientId = $user->getClientId();
        if ($user->client()->mysql_set_id <= 0 ) {
            throw new RuntimeException(\Yii::t('common', 'No data'));
        }
        $data = [
            'ali_order_count'       => \common\library\invoice\Helper::getAliOrderCount($clientId)??0
        ];

        $this->success($data);
    }

    public function actionEdm()
    {
        $user = User::getLoginUser();
        $clientId = $user->getClientId();
        $client = Client::getClient($clientId);
        $key = \common\library\account\Client::SETTING_KEY_EDM_FREEZE_FLAG;
        $values = $client->getSettingAttributes([$key]);
        if(empty($values[$key])) {
            $values[$key] = 0;
        }
        return $this->success($values);
    }

    public function actionLeads()
    {
        $user = User::getLoginUser();
        $clientId = $user->getClientId();
        $userId = $user->getUserId();
        $service = PrivilegeService::getInstance($clientId, $userId);

        //leads续费广告展示
        if(\Yii::app()->params['env'] != 'exp' && $service->getMainSystemId() == PrivilegeConstants::OKKI_LEADS_ID)
        {
            $leadsAds = ClientService::getLeadsAds($clientId, $userId);
        }

        return $this->success([
          'leads_ads' => $leadsAds ?? [],
        ]);
    }

    //ai推荐客户三阶段提醒下线
    public function actionAiRecommend()
    {
        $config = \Yii::app()->params['leads_ads'];
        $stage_1_date = $config['ai_recommend_stage_1'] ?? '2023-11-24';
        $stage_2_date = $config['ai_recommend_stage_2'] ?? '2023-12-01';
        $date = date("Y-m-d H:i:s");
        if($date < $stage_1_date){
            $stage = 1;
        }elseif ($date < $stage_2_date){
            $stage = 2;
        }else{
            $stage = 3;
        }

        return $this->success([
          'stage' => $stage,
        ]);
    }

    public function actionUrlCheck($url,$safety_page_url='')
    {
        LogUtil::info("url: {$url} $safety_page_url: {$safety_page_url} ip: ".Util::getRealIpAddress());

        if( $safety_page_url)
        {
            $safety_page_url = urldecode($safety_page_url);
        }else
        {
            $safety_page_url = '/page/safety/index.html';
        }

        if( strpos($safety_page_url, '?') ===false )
        {
            $safety_page_url .= '?url='.urlencode($url);
        }else
        {
            $safety_page_url .= '&url='.urlencode($url);
        }

        $whiteList = [
            'Asia' => 'https://www.lazada.com.my/',
            'India' => 'https://www.flipkart.com/',
            'America' => 'https://www.amazon.com/',
            'Middle East' => 'https://uae.souq.com/',
            'alibaba' => 'https://www.alibaba.com/',
            '1688' => 'https://s.1688.com/',
            'xmkf' => 'http://xmkf.ewei.com/', //小满客服
            'ossjump' => 'http://t.ossjump.site/', //超大附件
            'xmdcsw' => 'http://t.xmdcsw.site/', //超大附件
            'ossjump_https' => 'https://t.ossjump.site/', //超大附件
            'xmdcsw_https' => 'https://t.xmdcsw.site/', //超大附件
            'xmcdfj_https' => 'https://t.xmcdfj.site/', //超大附件
            'yuque_https' => 'https://www.yuque.com/help.xiaoman', //语雀知识中心
            'xmdcswcom_https' => 'https://t.xmdcsw.com/', //超大附件
        ];

        foreach ($whiteList as $val)
        {
            if (preg_match("/^". str_replace(["/", "?"], ["\/", "\?"], $val) ."/im", $url))
                return $this->redirect($url);

        }

        return $this->redirect($safety_page_url);
    }

    /**
     * 获取Discovery模块后端访问的授权令牌Token
     *
     * return array('token' => $token, 'expires' => $expires)
     */
    public function actionGetFeToken()
    {
        $user = User::getLoginUser();

        if (empty($user)) {
            throw new \RuntimeException(\Yii::t('account', 'Login exception'));
        }

        $data = \common\library\risk\service\SecurityTokenService::generateToken();

        \LogUtil::info("PageRead GetFeToken client_id:".$user->getClientId()."user_id:".$user->getUserId()."   Data: ".json_encode($data));   // TODO 短验签非法验签分析
        $this->success($data);
    }

    /**
     * 用户退出登录，清除正在占用的Discovery模块后端访问的授权令牌Token
     */
    public function actionFlushFeToken()
    {
        $user = User::getLoginUser();

        if (!empty($user)) {
            \common\library\risk\service\SecurityTokenService::flushToken();
        }

        $this->success('success');
    }

    public function actionFeVersion($system_config = SystemConfig::CRM_FE_VERSION)
    {
        if ($system_config !== SystemConfig::SALES_FE_VERSION) {
            $system_config = SystemConfig::CRM_FE_VERSION;
        }
        return $this->success(SystemConfig::config($system_config));
    }

    public function actionGetTime()
    {
        return $this->success([
            'date'      => date('Y-m-d H:i:s'),
            'timestamp' => time()
        ]);
    }

    public function actionGetMainSystemId()
    {
        $user = User::getLoginUser();
        if( !$user )
        {
            return $this->success([
                'system' => '',
                'valid_from' => '',
                'valid_to'   => '',
                'modules' => [],
                'is_trial' => 0,
            ]);
        }

        $clientId = $user->getClientId();
        $userId = $user->getUserId();
        $client = Client::getClient($clientId);
        $service = PrivilegeService::getInstance($clientId, $userId);
        $moduleService = new \common\library\privilege_v3\ClientPrivilegeModuleService($clientId, $userId);
        $mainSystem = $service->getMainSystemId();
        $modules = $moduleService->getModuleIds();
        //todo 现在线上仅有lite版本, 等后续会被marketing_smart取代, 仅有ads权限的为lite
        if( $service->hasFunctional([PrivilegeConstants::FUNCTIONAL_MARKETING_ADS]) )
        {
            $modules[] = 'marketing_lite';
        }

        if ($client->mysql_set_id > 0) {
            // 绩效模块：生成使用记录
            $service = new \common\library\usage_record\UsageRecordService($clientId, $user);
            $service->record();
        }

        return $this->success([
            'system'     => $mainSystem,
            'valid_from' => $client->valid_from,
            'valid_to'   => $client->valid_to,
            'modules'    => $modules,
            'is_trial'   => $client->isTrial() ? 1 : 0,
        ]);
    }

    /**
     * 审批流自定义上线之后需要删除
     * @return bool
     */
    public function actionShowCustomApproval()
    {
        $clients = [
            1,3,9,107,818,1170,1935,2231,2419,2807,3197,3322,3397,3646,3805,4364,4425,4635,4745,5330,5856,5882,6010,6034,6216,6532,6955,7642,7741,7876,8120,8614,9488,9695,9992,10274,11336,11391,11810,11914,11965,12694,13254,13379,13865,14022,14031,14059,14096,14348,14375,15005,15011,15023,15179,15208,15391,15621,15664,15788,16087,16652,16694,16876,17449,17628,18133,18159,18420,18793,19418,19509,23353,23982
        ];

        $clientId = User::getLoginUser()->getClientId();

        return in_array($clientId,$clients);
    }

    /**
     * 给前端开发时生成字典使用，不会用于线上
     *
     * @return string
     */
    public function actionAllSettingConfig()
    {
        $result = [
            'client_setting' => [],
            'user_setting' => [],
            'privilege_switch' => [],
        ];

        $userConstantsMap = array_column(\common\library\util\ClassUtil::getAllConstants(UserSetting::class), 'desc', 'key');
        foreach (UserSetting::KEY_PROCESS_MAP as $k => $v) {
            $result['user_setting'][] = [
                'key' => $k,
                'desc' => $userConstantsMap[$k] ?? '',
            ];
        }
        foreach (PrivilegeConstants::SWITCH_FUNCTIONAL_MAP as $k => $v) {
            $result['privilege_switch'][] = [
                'key' => $k,
                'desc' => Yii::t('privilege', $k),
            ];
        }

        $clientConstants = \common\library\util\ClassUtil::getAllConstants(Client::class);
        foreach ($clientConstants as $constantInfo) {
            if (strpos($constantInfo['name'], 'SETTING_KEY') === 0 || strpos($constantInfo['name'], 'EXTERNAL_KEY') === 0) {
                $result['client_setting'][] = [
                    'key' => $constantInfo['key'],
                    'desc' => $constantInfo['desc'],
                ];
            }
        }
        if (empty($result['client_setting'])) {
            foreach ((new ReflectionClass(Client::class))->getConstants() as $name => $key) {
                if (strpos($name, 'SETTING_KEY') === 0 || strpos($name, 'EXTERNAL_KEY') === 0) {
                    $result['client_setting'][] = [
                        'key' => $key,
                        'desc' => '',
                    ];
                }
            }
        }

        return $this->success($result);
    }

    public function actionCheckUpgrade()
    {
        $user = User::getLoginUser();
        if( empty($user) )
        {
            return $this->success(['login_status' =>  0  ,'upgrade' => false]);
        }

        $client = \common\library\account\Client::getClient($user->getClientId());
        $upgrade = false;
        if ($client->mysql_set_id)
        {
            $dbSet =  \common\library\account\service\DbService::getDbSet($client->client_id, \DbSet::TYPE_MYSQL, true);
            if((isset($dbSet['enable_flag']) && $dbSet['enable_flag'] == 2 )
                || (isset($dbSet['disable_flag']) && $dbSet['disable_flag'] == 1))
            {
                $upgrade = true;
            }
        }

        if ($client->pgsql_set_id)
        {
            $dbSet =  \common\library\account\service\DbService::getDbSet($client->client_id, \DbSet::TYPE_PGSQL, true);
            if(isset($dbSet['enable_flag']) && $dbSet['enable_flag'] == 2
            || (isset($dbSet['disable_flag']) && $dbSet['disable_flag'] == 1))
            {
                $upgrade = true;
            }
        }

        return $this->success(['login_status' =>  1, 'upgrade' => $upgrade ]);

    }

    /**
     * 检查当前登录用户
     *
     * @return array
     */
    public function actionCheckUser()
    {
        $user = User::getLoginUser();

        if (empty($user)) {
            throw new \RuntimeException(\Yii::t('account', 'Login exception'));
        }

        $customData = $user->getLoginData()['customData']??'';

        $sellerAccountId = $customData['seller_account_id'] ?? 0;

        if ($sellerAccountId) {
            $alibabaAccount = new \common\library\alibaba\oauth\AlibabaAccount($user->getClientId());
            $alibabaAccount->loadByClientAndSellerAccountId($sellerAccountId);

            $sellerAccountInfo = [
                'seller_account_id' => $sellerAccountId,
                'seller_email' => $alibabaAccount->seller_email ?? '',
                'seller_nickname' => $alibabaAccount->seller_nickname ?? '',
            ];
        }

        $result = [
            'client_id' => $user->getClientId(),
            'user_id' => $user->getUserId(),
            'eid' => !empty($user->getInfoObject()) ? (int)$user->getInfoObject()->eid : 0,
            'user_eid' => !empty($user->getInfoObject()) ? (int)$user->getInfoObject()->user_eid : 0,
            'email' => $user->getEmail(),
            'nickname' => $user->getNickname(),
            'client_type' => $customData['client_type'] ?? '',
            'login_type' => $customData['method'] ?? '',
            'seller_account_info' => isset($sellerAccountInfo)?json_encode($sellerAccountInfo):'',
        ];

        return $this->success($result);
    }

    public function actionImSetting()
    {
        $setting = [
            'whatsapp_iframe_flag' =>  1,
            'whatsapp_desktop_flag' =>  1,
            'whatsapp_chat_group_flag' =>  1
        ];

        $user = User::getLoginUser();
        if (!empty($user)) {
            $clientId = $user->getClientId();
            $redis = \RedisService::getInstance('redis');
            $key = 'crm:whatsapp:iframe:close';
            $data = $redis->get($key);
            if(!empty($data)) {
                $clientList = json_decode($data, true);
            }else{
                $clientList = [];
            }
            if(in_array($clientId, $clientList)){
                $setting['whatsapp_iframe_flag'] = 0;
            }
        }

        return $this->success($setting);
    }

    public function actionGetIpAddr()
    {
        $ip = Util::getRealIpAddress();
        $address = \common\library\util\IP::getInstance()->find($ip);
        return $this->success(['ip' => $ip, 'address' => $address ?: []]);
    }


    //智齿-根据当前模块跳转客服链接
    public function actionRedirectStaff($module = null,$system = \common\library\privilege_v3\PrivilegeConstants::SYSTEM_MODULE_CRM)
    {
        $user = User::getLoginUser();
        $clientId = $user?$user->getClientId():0;
        $clientInfo = Client::getClient($clientId);
        $useZone = !$clientInfo->isNew() ? $clientInfo->use_zone : '';

        $staffLinkService = new \common\library\staff\StaffLinkService($clientId);
        $staffLinkService->setChannelName($module);
        if(!empty($useZone)&&$useZone=='TW'){
            $staffLinkService->setSystem($staffLinkService::SYSTEM_TW);
        }else{
            $staffLinkService->setSystem($system);
        }
        $staffLinkService->setCurrentUrl($_SERVER['HTTP_REFERER']??'');
        $redirect_url = $staffLinkService->buildStaffLink();

        $result = [
            'redirect_url' => $redirect_url,
        ];
        return $this->success($result);
    }
}
