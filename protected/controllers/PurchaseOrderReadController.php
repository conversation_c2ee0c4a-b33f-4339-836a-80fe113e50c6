<?php

use common\library\APIConstant;
use common\library\approval_flow\ApplyForm;
use common\library\custom_field\CustomFieldReadActions;
use common\library\export_v2\Export;
use common\library\export_v2\ExportConstants;
use common\library\export_v2\ExportReadAction;
use common\library\invoice\export\InvoiceExportFileList;
use common\library\invoice\OrderList;
use common\library\invoice\status\InvoiceStatusService;
use common\library\oms\common\CheckNoExistAction;
use common\library\oms\common\OmsConstant;
use common\library\oms\common\OmsField;
use common\library\oms\common\TransferFilterFactory;
use common\library\oms\inbound_invoice\purchase\PurchaseInboundInvoiceFilter;
use common\library\oms\inbound_invoice\purchase\record\PurchaseInboundRecordFilter;
use common\library\oms\payable_invoice\PayableInvoiceApi;
use common\library\oms\payment_invoice\PaymentInvoiceApi;
use common\library\oms\product_transfer\purchase\PurchaseProductTransferFilter;
use common\library\oms\product_transfer\purchase\record\PurchaseProductTransferRecordFilter;
use common\library\oms\product_transfer\purchase\relation\PurchaseProductTransferRelationFilter;
use common\library\oms\scaffold\ScopeAccess;
use common\library\privilege_v3\Helper;
use common\library\privilege_v3\Helper as PrivilegeHelper;
use common\library\purchase\purchase_order\Helper as PurchaseOrderHelper;
use common\library\privilege_v3\PrivilegeConstants;
use common\library\product_v2\ProductConstant;
use common\library\product_v2\sku\SkuAPI;
use common\library\purchase\purchase_from_order\API;
use common\library\purchase\purchase_order\export\PurchaseOrderExport;
use common\library\purchase\purchase_order\export\PurchaseOrderExportExecutor;
use common\library\purchase\purchase_order\PurchaseOrder;
use common\library\purchase\purchase_order\PurchaseOrderFilter;
use common\library\purchase\purchase_order_product\PurchaseOrderProductFilter;
use common\library\setting\library\fund\relation\CostItemInvoiceRelationReadActions;
use xiaoman\orm\database\data\DateRange;
use xiaoman\orm\database\data\Equal;
use xiaoman\orm\database\data\In;
use xiaoman\orm\database\data\InArray;
use xiaoman\orm\database\data\Like;
use common\library\invoice\export\InvoiceExportActions;
use common\library\object\object_define\Constant as ObjConstant;

/**
 * Created by PhpStorm.
 * User: beenzhang
 * Date: 2021/3/9
 * Time: 3:00 PM
 */
class PurchaseOrderReadController extends Controller
{
    use CustomFieldReadActions, CostItemInvoiceRelationReadActions;
    use ExportReadAction;
    use ScopeAccess;
    use CheckNoExistAction;
    use InvoiceExportActions;

    public function initExportRead()
    {
        $this->exportType = ExportConstants::EXPORT_TYPE_PURCHASE_ORDER;
        $this->exportScene = ExportConstants::EXPORT_TYPE_PURCHASE_ORDER;
        $this->exportPrivilege = PrivilegeConstants::PRIVILEGE_CRM_PURCHASE_ORDER_DOWNLOAD;
        $this->exportExecutor = PurchaseOrderExportExecutor::class;
    }

    public function initCheckNoExistAction()
    {
        $this->checkModule = Constants::TYPE_PURCHASE_ORDER;
    }

    protected function beforeAction($action)
    {
        $this->initCostItemInvoiceRelation(Constants::TYPE_PURCHASE_ORDER);
        $this->initCustomFieldService(Constants::TYPE_PURCHASE_ORDER);
        $this->initInvoiceExportType(Constants::TYPE_PURCHASE_ORDER);
        return parent::beforeAction($action);
    }

    public function actionInfo($purchase_order_id, array $groups = [], $apply_form_id = null, $show_transfer = 0)
    {
        $user = User::getLoginUser();
        $purchaseOrder = new PurchaseOrder($user->getClientId(), $purchase_order_id);
        $purchaseOrder->setDomainHandler($user);
        if (!$purchaseOrder->isViewAble(false, $apply_form_id)) {
            throw new RuntimeException(Yii::t('privilege', 'Missing permission'), ErrorCode::CODE_INVOICE_NOT_PRIVILEGE_VIEW);
        }

        if ($apply_form_id) {
            try {
                $applyForm = new ApplyForm($apply_form_id, $this->getLoginUserId(), true);
                if (!$applyForm->isNew() && $applyForm->isApproving()) {
                    $content = $applyForm->getContent();
                    if (is_array($content)) {
                        $externalFieldData = $purchaseOrder->external_field_data ?? [];
                        foreach ($content as $field => $item) {
                            if (is_numeric($field)) {
                                $externalFieldData[$field] = $item['new'];
                                continue;
                            }
                            $purchaseOrder->$field = $item['new'];
                        }
                        $purchaseOrder->external_field_data = $externalFieldData;

                    }
                    //采购单中的审批 content没有product_list 修改后的数据在context中，需要看变换后的产品需要自行获取
                    $context = $applyForm->getContext();
                    $approvalData = $context['data'] ?? [];
                    //判断数据结构是否新旧数据
                    $isOldData = \common\library\purchase\purchase_order\Helper::isOldPurchaseOrderData($approvalData);
                    if ($isOldData) {
                        $attributes = $purchaseOrder->getFormatter()->unFormatInfoGroup($approvalData);
                    }else{
                        $attributes = $approvalData;
                    }
                    $purchaseOrderField = OmsField::make($user->getClientId(),\Constants::TYPE_PURCHASE_ORDER);
                    $attributes = $purchaseOrderField->unpackFormData($attributes);

                    // 状态变更的审批单是没有product_list，无法同步计算，直接拿宽表数据
                    if (isset($attributes['product_list'])) {
                        $purchaseOrder->setProductList($attributes['product_list']);
                        $purchaseOrder->calculateFunctionalFields();
                        $purchaseOrder->getFormatter()->setApprovalProductList($purchaseOrder->getProductList());
                    }
                    $purchaseOrder->getFormatter()->setShowApprovalFlag(true);
                }
            }catch (\Exception $exception){

            }
        }
        $purchaseOrder->approvalFieldPrivilege($apply_form_id);
        $purchaseOrder->getFormatter()->setIsFieldPrivilegeByUserObject($purchaseOrder->getIsFieldPrivilegeByUserObject());
        $purchaseOrder->getFormatter()->setShowTransferInfo($show_transfer);
        $purchaseOrder->getFormatter()->setShowMasterProductInfo(true);
        $purchaseOrder->getFormatter()->infoSetting();
        $purchaseOrder->getFormatter()->setGroupIds($groups);
        $purchaseOrder->getFormatter()->setDataSimpleFlag(true);
        $data = $purchaseOrder->getAttributes();
        // 拆分下推时，供应商可以不填，前端编辑采购订单直接显示0，为了不直接显示0做下兼容
        if (isset($data['supplier_id']) && $data['supplier_id'] == 0) {
            $data['supplier_id']   = '';
            $data['supplier_info'] = ['supplier_id' => '', 'name' => '', 'supplier_no' => '', 'rate' => '', 'rate_id' => ''];
        }
        $data['can_unlock'] =  $purchaseOrder->canUnlock();
        return $this->success($data);
    }

    public function actionList(
        $purchase_order_ids = '',
        $purchase_order_no = '',
        $supplier_id = 0,
        $supplier_contact_id = 0,
        $supplier_contact_name = '',
        $supplier_name = '',
        $supplier_keyword = '',
        array $status = [],
        array $handler = [],
        $purchase_date_start = null,
        $purchase_date_end = null,
        $delivery_date_start = null,
        $delivery_date_end = null,
        $create_time_start = null,
        $create_time_end = null,
        array $creator = [],
        $update_time_start = null,
        $update_time_end = null,
        array $modifier = [],
        $create_type = null,
        array $payment_status = [],
        $page_no = 1,
        $page_size = 20,
        $sort_field = 'create_time',
        $sort_type = 'desc',
        array $transfer_invoice_ids = [],
        $scene = APIConstant::SCENE_LIST,
        $custom_field = null,
        array $inbound_status = [],
        $product_name_keyword = '',
        $product_no_keyword = '',
        $product_model_keyword = '',
        $skip_permission_check = 0,
        array $currency = [],
        $exchange_rate = null,
        $exchange_rate_usd = null,
        $quote_field=[],       // 引用字段的筛选项和排序项
        $order_no = '',
        array $query_filters = [],
    )
    {
        $this->validate([
            'page_no' => 'integer',
            'page_size' => 'integer',
            'sort_field' => 'regex:/^\w{2,32}$/',
            'sort_type' => 'string|in:asc,desc',
        ]);

        $user = User::getLoginUser();

        if (!Helper::hasPermission($user->getClientId(),$user->getUserId(),
            PrivilegeConstants::PRIVILEGE_CRM_PURCHASE_ORDER_VIEW)
        ){
            throw new RuntimeException(Yii::t('privilege', 'Missing permission'),ErrorCode::CODE_INVOICE_NOT_PRIVILEGE_VIEW);
        }

//        //当前是展示可见范围内所有数据
//        $scopeUserIds = Helper::getPermissionScopeUser($user->getClientId(), $user->getUserId(),
//            PrivilegeConstants::PRIVILEGE_CRM_PURCHASE_ORDER_VIEW);
//
//        if (empty($handler))
//        {
//            //超管
//            if ($scopeUserIds == 0){
//                $handler = null;
//            } else {
//                $handler = array_values(array_unique($scopeUserIds));
//            }
//        } else {
//            if ($scopeUserIds != 0) {
//                $handler = array_intersect($handler, $scopeUserIds);
//            }
//        }
//
//        if (is_array($handler) && empty($handler))
//        {
//            return $this->success([
//                'list' => [],
//                'count' => 0
//            ]);
//        }
//
//        //跳过权限，查询所有信息
//        if ($skip_permission_check) {
//            $handler = [];
//        }

        $params = [];
        $method = new \ReflectionMethod(__METHOD__);
        foreach ($method->getParameters() as $parameter)
        {
            $params[$parameter->name] = ${$parameter->name} ?? null;
        }

        $params['show_quote_fields'] = $scene == APIConstant::SCENE_LIST;
        $req = new \common\library\purchase\purchase_order\PurchaseOrderListReq($params, $user->getClientId());
        $data = \common\library\purchase\purchase_order\API::webList($user->getUserId(), $req);
        return $this->success($data);
    }

    /**
     * 采购订单列表金额统计
     * 筛选条件参数需与列表接口保持一致
     */
    public function actionListStatistics(
        $purchase_order_ids = '',
        $purchase_order_no = '',
        $supplier_id = 0,
        $supplier_contact_id = 0,
        $supplier_contact_name = '',
        $supplier_name = '',
        $supplier_keyword = '',
        array $status = [],
        array $handler = [],
        $purchase_date_start = null,
        $purchase_date_end = null,
        $delivery_date_start = null,
        $delivery_date_end = null,
        $create_time_start = null,
        $create_time_end = null,
        array $creator = [],
        $update_time_start = null,
        $update_time_end = null,
        array $modifier = [],
        $create_type = null,
        array $payment_status = [],
        $page_no = 1,
        $page_size = 20,
        $sort_field = 'create_time',
        $sort_type = 'desc',
        array $transfer_invoice_ids = [],
        $scene = \common\library\purchase\purchase_order\API::SCENE_STATISTICS,
        $custom_field = null,
        array $inbound_status = [],
        $product_name_keyword = '',
        $product_no_keyword = '',
        $product_model_keyword = '',
        $order_no = '',
        $quote_field=[],       // 引用字段的筛选项和排序项
        array $query_filters = [],
    )
    {
        $user = User::getLoginUser();

        if (!Helper::hasPermission($user->getClientId(),$user->getUserId(), PrivilegeConstants::PRIVILEGE_CRM_PURCHASE_ORDER_VIEW)){
            throw new RuntimeException(Yii::t('privilege', 'Missing permission'),ErrorCode::CODE_INVOICE_NOT_PRIVILEGE_VIEW);
        }

        //当前是展示可见范围内所有数据
        $scopeUserIds = Helper::getPermissionScopeUser($user->getClientId(), $user->getUserId(), PrivilegeConstants::PRIVILEGE_CRM_PURCHASE_ORDER_VIEW);

        if (empty($handler))
        {
            //超管
            if ($scopeUserIds == 0){
                $handler = null;
            } else {
                $handler = array_values(array_unique($scopeUserIds));
            }
        } else {
            if ($scopeUserIds != 0) {
                $handler = array_intersect($handler, $scopeUserIds);
            }
        }

        if (is_array($handler) && empty($handler))
        {
            return $this->success([]);
        }

        $params = [];
        $method = new \ReflectionMethod(__METHOD__);
        foreach ($method->getParameters() as $parameter)
        {
            $params[$parameter->name] = ${$parameter->name} ?? null;
        }
        $params['show_quote_fields'] = true;
        $req = new \common\library\purchase\purchase_order\PurchaseOrderListReq($params, $user->getClientId());
        $data = \common\library\purchase\purchase_order\API::webList($user->getUserId(), $req);
        return $this->success($data);
    }

    public function actionAttachmentsList($purchase_order_id, $page = 1, $page_size = 10)
    {
        $user = User::getLoginUser();
        $purchaseOrder = new PurchaseOrder($user->getClientId());
        $purchaseOrder->loadById($purchase_order_id);
        $purchaseOrder->setDomainHandler($user);
        $purchaseOrder->getFormatter()->attachmentListSetting((($page-1)*$page_size), $page_size);
        $data = $purchaseOrder->getAttributes();

        return $this->success($data);
    }

    /**
     * @param string $order_name_keyword
     * @param string $order_no_keyword
     * @param string $product_name_keyword
     * @param string $product_no_keyword
     * @param string $product_model_keyword
     * @param array $order_handler
     * @param string $order_account_date_start
     * @param string $order_account_date_end
     * @param array $transfer_invoice_handler
     * @param null $transfer_invoice_expect_time_start
     * @param null $transfer_invoice_expect_time_end
     * @param string $transfer_invoice_serial_id
     * @param int $page_no
     * @param int $page_size
     * @param string $sort_field
     * @param string $sort_type
     * @param bool $ignore_purchase_count_done 不显示待采购为0的采购需求
     * @return false|string
     * @throws \xiaoman\orm\exception\QueryException
     * 以销定购列表（采购任务的产品明细列表）
     */
    public function actionListProductToBePurchase(
        $order_name_keyword = '',
        $order_no_keyword = '',
        $product_name_keyword = '',
        $product_no_keyword = '',
        $product_model_keyword = '',
        array $order_handler = [],
        $order_account_date_start = '',
        $order_account_date_end = '',
        array $transfer_invoice_handler = [],
        $transfer_invoice_expect_time_start = null,
        $transfer_invoice_expect_time_end = null,
        array $transfer_invoice_id = [],
        $transfer_invoice_serial_id = '',
        $page_no = 1,
        $page_size = 20,
        $sort_field = 'expect_time',
        $sort_type = 'asc',
        $ignore_purchase_count_done = 0
    ){
        $user = User::getLoginUser();

        //可见域设置
        $transferInvoiceHandler = $this->getScopeUserIds($transfer_invoice_handler, PrivilegeConstants::PRIVILEGE_CRM_PURCHASE_BY_ORDER_VIEW);
        if (is_array($transferInvoiceHandler) && empty($transferInvoiceHandler)) {
            array_push($transferInvoiceHandler, $user->getUserId());
        }

        //采购任务
        $transferFilter = new PurchaseProductTransferFilter($user->getClientId());

        //创建人，处理人 为可见的数据
        if (is_array($transferInvoiceHandler)) {
            $transferInvoiceHandlerIds = implode(',', $transferInvoiceHandler);
            $transferFilter->rawWhere(" and (handler && ARRAY [ {$transferInvoiceHandlerIds} ]:: BIGINT []
            or
            create_user in ({$transferInvoiceHandlerIds}) ) ");
        }
        if ($transfer_invoice_handler) {
            $transferFilter->handler = new InArray($transfer_invoice_handler);
        }

        $transferFilter->status = [
            OmsConstant::PRODUCT_TRANSFER_STATUS_TO_HANDLE,
            OmsConstant::PRODUCT_TRANSFER_STATUS_NOT_ACCEPTED,
            OmsConstant::PRODUCT_TRANSFER_STATUS_ACCEPTED,
        ];
        $transferFilter->expect_time = new DateRange($transfer_invoice_expect_time_start, $transfer_invoice_expect_time_end);
        !empty($transfer_invoice_id) && $transferFilter->transfer_invoice_id = $transfer_invoice_id;
        if (!empty($transfer_invoice_serial_id)) {
            $transferFilter->search(['serial_id' => $transfer_invoice_serial_id]);
        }

        $transferFilter->select(['transfer_invoice_id']);
        $transferInvoiceIds = array_column($transferFilter->rawData(), 'transfer_invoice_id');
        if (empty($transferInvoiceIds))
            return $this->success(['list'  => [], 'count' => 0]);

        //订单
        $orderIds = [];
        if (!empty($order_name_keyword) || !empty($order_no_keyword)
            || !empty($order_account_date_start) || !empty($order_account_date_end)
            || !empty($order_handler))
        {
            $orderList = new OrderList($user->getUserId());
            $orderList->setSkipPermissionCheck(true);
            $orderList->setKeyword($order_name_keyword);
            $orderList->setOrderNoKeyWord($order_no_keyword);
            $orderList->setSettleStart($order_account_date_start);
            $orderList->setSettleEnd($order_account_date_end);
            $orderList->setHandlerUser($order_handler);
            $orderList->setFields(['order_id','order_no','name']);
            $orderList = $orderList->find();
            $orderIds = array_column($orderList, 'order_id');

            if (empty($orderIds))
                return $this->success(['list'  => [], 'count' => 0]);
        }

        //产品
        $skuIds = $masterRecordIdMap = $masterSkuIds = [];
        if (!empty($product_name_keyword) || !empty($product_no_keyword) || !empty($product_model_keyword))
        {
            $skuIds = (new SkuAPI($user->getClientId()))->getSkuIds([
                'keyword' => $product_name_keyword,
                'product_no_keyword' => $product_no_keyword,
                'product_model_keyword' => $product_model_keyword,
            ]);
            //未搜索到产品数据
            if (empty($skuIds))
                return $this->success(['list'  => [], 'count' => 0]);

            //如果查询到的是配件产品，需要在列表返回其整采的归属主产品
            $transferRecordFilter = new PurchaseProductTransferRecordFilter($user->getClientId());
            $transferRecordFilter->transfer_invoice_id = $transferInvoiceIds;//任务
            $transferRecordFilter->refer_id = $orderIds;//订单
            $transferRecordFilter->sku_id = $skuIds;//产品
            $transferRecordFilter->delete_flag = 0;
            $transferRecordFilter->is_master_product = 0;
            $transferRecordFilter->master_id = new \xiaoman\orm\database\data\NotEqual(0);
            $transferRecordFilter->select(['transfer_invoice_record_id', 'master_id']);
            $transferRecordList = $transferRecordFilter->rawData();
            $masterTransferRecordIds = \common\library\util\Arr::uniqueFilterValues(array_column($transferRecordList, 'master_id'));
            $masterRecordIdMap = array_column($transferRecordList, 'master_id', 'transfer_invoice_record_id');

            if (!empty($masterTransferRecordIds)) {
                $transferRecordFilter = new PurchaseProductTransferRecordFilter($user->getClientId());
                $transferRecordFilter->transfer_invoice_record_id = $masterTransferRecordIds;//任务
                $transferRecordFilter->select(['sku_id']);
                $masterSkuIds = \common\library\util\Arr::uniqueFilterValues(array_column($transferRecordFilter->rawData(), 'sku_id'));
            }
        }

        //过滤代采购数为0的任务明细
        $transferInvoiceRecordIds = [];
        if ($ignore_purchase_count_done > 0)
        {
            $transferRecordFilter = new PurchaseProductTransferRecordFilter($user->getClientId());
            $transferRecordFilter->transfer_invoice_id = $transferInvoiceIds;//任务
            $transferRecordFilter->refer_id = $orderIds;//订单
            $transferRecordFilter->sku_id = $skuIds;//产品
            $transferRecordFilter->delete_flag = 0;
            $transferRecord = $transferRecordFilter->find();
            $transferRecord->getFormatter()->displayFields(['transfer_invoice_id','transfer_invoice_record_id','reach_count']);
            $transferRecord->getFormatter()->displayProductTransferRecordPurchaseCount();
            $transferRecordVSPurchase = $transferRecord->getListAttributes();

            if (empty($transferRecordVSPurchase))
                return $this->success(['list'  => [], 'count' => 0]);

            foreach ($transferRecordVSPurchase as $item)
            {
                if ($item['not_purchase_order_count'] > 0) {
                    $transferInvoiceRecordIds[] = $item['transfer_invoice_record_id'];

                    //把整采单据的主产品明细ID也加到查询中，否则 待采购数不为0的配件 的 归属主产品 将不会出现在列表中
                    if (isset($masterRecordIdMap[$item['transfer_invoice_record_id']])) {
                        $transferInvoiceRecordIds[] = $masterRecordIdMap[$item['transfer_invoice_record_id']];
                    }
                }
            }

            if (empty($transferInvoiceRecordIds)) {
                return $this->success(['list'  => [], 'count' => 0]);
            }
        }

        //ORM 不支持 join 多个表，此处只能组装 sql 进行查询
        $clientId = $user->getClientId();
        $sql = "select tptr.*,tbo.has_partial_purchase_order from tbl_product_transfer_record as tptr 
                                                            inner join tbl_product as tp on tptr.client_id = tp.client_id and tptr.product_id = tp.product_id
                                                            inner join tbl_order as tbo on tptr.client_id = tbo.client_id and tptr.refer_id = tbo.order_id";
        $countSql = "select count(1) from tbl_product_transfer_record as tptr 
                                                            inner join tbl_product as tp on tptr.client_id = tp.client_id and tptr.product_id = tp.product_id
                                                            inner join tbl_order as tbo on tptr.client_id = tbo.client_id and tptr.refer_id = tbo.order_id";

        $whereSql = " where tptr.client_id={$clientId} and tptr.delete_flag = 0 and tp.enable_flag = 1 and tbo.enable_flag = 1";
        if (!empty($transferInvoiceIds)) $whereSql .= " and tptr.transfer_invoice_id in (" . implode(',', $transferInvoiceIds) . ")";
        if (!empty($transferInvoiceRecordIds)) $whereSql .= " and tptr.transfer_invoice_record_id in (" . implode(',', $transferInvoiceRecordIds) . ")";
        if (!empty($orderIds)) $whereSql .= " and tptr.refer_id in (" . implode(',', $orderIds) . ")";
        if (!empty($skuIds)) {
            //产品筛选时，返回命中的产品和其归属的主产品
            $skuSql = "tptr.sku_id in (" . implode(',', $skuIds) . ")";
            if (!empty($masterSkuIds)) {
                $masterSkuSql = "tptr.sku_id in (" . implode(',', $masterSkuIds) . ") and tbo.has_partial_purchase_order = 0 and tptr.master_id = 0";
                $whereSql .= " and ( ($skuSql) or ($masterSkuSql) )";
            } else {
                $whereSql .= " and $skuSql";
            }
        }

        // 1. 整采的主产品或普通产品（即非配件）
        // 2. 分采过的任何产品
        $whereSql1 = "tbo.has_partial_purchase_order = 0 and tptr.master_id = 0";
        $whereSql2 = "tbo.has_partial_purchase_order = 1";
        $whereSql .= " and ( ($whereSql1) or ($whereSql2) )";

        $orderBySql = " order by tptr.$sort_field $sort_type, tptr.transfer_invoice_record_id asc";
        $limitSql = " limit $page_size offset " . ($page_no - 1) * $page_size;

        $db    = \PgActiveRecord::getDbByClientId($clientId);
        $data  = $db->createCommand($sql . $whereSql . $orderBySql . $limitSql)->queryAll();
        $count = $db->createCommand($countSql . $whereSql)->queryScalar();

        $batch = new \common\library\oms\product_transfer\purchase\record\BatchPurchaseProductTransferRecord($clientId);
        $batch->initFromData($data);
        $batch->getFormatter()->productToBePurchaseSetting();
        $list = $batch->getListAttributes();
        PurchaseOrderHelper::formatSupplierPrice($list);
        return $this->success([
            'count' => $count,
            'list'  => $count ? $list : []
        ]);
    }

    /**
     * @param $invoice_product_id
     * @param int $page_no
     * @param int $page_size
     * @return string
     * 订单产品所在采购单列表
     */
    public function actionProductPurchaseList(
        $invoice_product_id,
        $page_no = 1,
        $page_size = 20,
        $sort_field = 'create_time',
        $sort_type = 'desc'
    ){
        $this->validate([
            'page_no' => 'integer',
            'page_size' => 'integer',
            'sort_field' => 'regex:/^\w{2,32}$/',
            'sort_type' => 'string|in:asc,desc',
        ]);

        $user = User::getLoginUser();

        if(!Helper::hasPermission($user->getClientId(),$user->getUserId(), PrivilegeConstants::PRIVILEGE_CRM_PURCHASE_BY_ORDER_VIEW))
        {
            throw new RuntimeException(Yii::t('privilege', 'Missing permission'),ErrorCode::CODE_INVOICE_NOT_PRIVILEGE_VIEW);
        }

        if (!$invoice_product_id)
        {
            return $this->success([
                'list' => [],
                'count' => 0
            ]);
        }

        //订单产品
        $productFilter = new PurchaseOrderProductFilter($user->getClientId());
        $productFilter->invoice_product_id = new Equal($invoice_product_id);
        $productFilter->enable_flag = 1;
        $productFilter->select(['purchase_order_id','cost_amount','unit_price','order_id','count']);


        $filter = new PurchaseOrderFilter($user->getClientId());
        $filter->enable_flag = new Equal(1);

        $productFilter->initJoin()
            ->leftJoin($filter)
            ->on('purchase_order_id','purchase_order_id');

        $data = $productFilter->rawData(false);
        $purchaseOrderId = array_column($data,'purchase_order_id');
        $purchaseOrderId = array_unique($purchaseOrderId);

        if (empty($purchaseOrderId))
        {
            return $this->success([
                'list' => [],
                'count' => 0
            ]);
        }

        $productMap = array_column($data, null, 'purchase_order_id');

        $filter = new PurchaseOrderFilter($user->getClientId());
        $filter->purchase_order_id = new In($purchaseOrderId);
        $filter->enable_flag = new Equal(1);
        $filter->limit($page_size, $page_no);
        $filter->order($sort_field,$sort_type);

        $list = $filter->find();
        $list->getFormatter()->listSetting();
        $data = $list->getListAttributes();

        if (!empty($data))
        {
            //采购完成状态
            $statusService = new InvoiceStatusService($user->getClientId(), Constants::TYPE_PURCHASE_ORDER);
            $completeStatus = $statusService->list(true);
            $completeStatus = array_column($completeStatus,'id');
            foreach ($data as &$item)
            {
                $item['product_info'] = [
                    'have_purchase_order_count' => $productMap[$item['purchase_order_id']]['count']??0,//已下单采购
                    'purchase_complete_count' => 0,//已完成采购
                    'cost_amount' => $productMap[$item['purchase_order_id']]['cost_amount']??0,//金额小计
                    'unit_price' => $productMap[$item['purchase_order_id']]['unit_price']??0,//采购单价
                ];

                //已完成采购
                if (in_array($item['status'], $completeStatus))
                {
                    $item['product_info']['purchase_complete_count'] = $item['product_info']['have_purchase_order_count'];
                }
            }
        }


        $privilegeFieldStats = Helper::getPrivilegeFieldStats($this->getLoginUserClientId(),
            $this->getLoginUserId(),
            PrivilegeConstants::FUNCTIONAL_PURCHASE_ORDER);

        return $this->success([
            'list' => $data,
            'privilege_field_stats' => $privilegeFieldStats,
            'count' => $filter->count()
        ]);
    }

    /**
     * @param $transfer_invoice_record_id
     * @param int $page_no
     * @param int $page_size
     * @return string
     * 产品流转明细所在采购单列表
     */
    public function actionProductPurchaseByTransferInvoiceRecordIdList(
        $transfer_invoice_record_id,
        $page_no = 1,
        $page_size = 20,
        $sort_field = 'create_time',
        $sort_type = 'desc'
    ){
        $this->validate([
            'page_no' => 'integer',
            'page_size' => 'integer',
            'sort_field' => 'regex:/^\w{2,32}$/',
            'sort_type' => 'string|in:asc,desc',
        ]);

        $user = User::getLoginUser();

        $privilegeFieldStats = Helper::getPrivilegeFieldStats($this->getLoginUserClientId(),
            $this->getLoginUserId(),
            PrivilegeConstants::FUNCTIONAL_PURCHASE_ORDER);


        if (!$transfer_invoice_record_id)
        {
            return $this->success([
                'list' => [],
                'count' => 0,
                'privilege_field_stats' => $privilegeFieldStats
            ]);
        }

        //关系表信息
        $relationFilter = new PurchaseProductTransferRelationFilter($user->getClientId());
        $relationFilter->transfer_invoice_record_id = new Equal($transfer_invoice_record_id);
        $relationFilter->enable_flag = 1;
        $relationFilter->select([
            'transfer_invoice_id',
            'transfer_invoice_record_id',
            'refer_id',
            'sub_refer_id',
        ]);

        $filter = new PurchaseOrderFilter($user->getClientId());
        $filter->enable_flag = new Equal(1);

        $relationFilter->initJoin()
            ->leftJoin($filter)
            ->on('refer_id','purchase_order_id');

        $data = $relationFilter->rawData(false);
        $purchaseOrderProductIds = array_unique(array_column($data, 'sub_refer_id'));
        $purchaseOrderId = array_unique(array_column($data, 'refer_id'));

        if (empty($purchaseOrderId))
        {
            return $this->success([
                'list' => [],
                'count' => 0,
                'privilege_field_stats' => $privilegeFieldStats
            ]);
        }

        $filter = new PurchaseOrderFilter($user->getClientId());
        $filter->purchase_order_id = new In($purchaseOrderId);
        $filter->enable_flag = new Equal(1);
        $filter->limit($page_size, $page_no);
        $filter->order($sort_field,$sort_type);

        $list = $filter->find();
        $list->getFormatter()->listSetting();
        $data = $list->getListAttributes();

        if (!empty($data))
        {
            $purchaseOrderProductFilter = new PurchaseOrderProductFilter($user->getClientId());
            $purchaseOrderProductFilter->purchase_order_product_id = $purchaseOrderProductIds;
            $purchaseOrderProductFilter->enable_flag = 1;
            $purchaseOrderProductList = $purchaseOrderProductFilter->find();
            $purchaseOrderProductList->getFormatter()->recordDetailsSetting();
            $purchaseOrderProductList = $purchaseOrderProductList->getAttributes();
            $purchaseOrderProductList = array_column($purchaseOrderProductList, null, 'purchase_order_id');
            foreach ($data as &$item)
            {
                $item['product_info'] = [
                    'have_purchase_order_count' => $purchaseOrderProductList[$item['purchase_order_id']]['count'] ?? 0,//已下单采购
                    'have_inbound_count' => $purchaseOrderProductList[$item['purchase_order_id']]['inbound_count'] ?? 0,//已入库数量
                    'cost_amount' => $purchaseOrderProductList[$item['purchase_order_id']]['cost_amount'] ?? 0,//金额小计
                    'unit_price' => $purchaseOrderProductList[$item['purchase_order_id']]['unit_price'] ?? 0,//采购单价
                ];
            }
        }

        return $this->success([
            'list' => $data,
            'privilege_field_stats' => $privilegeFieldStats,
            'count' => $filter->count()
        ]);
    }

    /**
     *  采购订单导出历史
     * @param int $purchase_order_id
     * @param int $user_id
     * @param int $type
     * @param int $recent_flag
     * @param int $page
     * @param int $page_size
     * @return false|string
     */
    public function actionExportFileList($purchase_order_id, $user_id = 0, $type = 0, $recent_flag = 0, $page = 1, $page_size = 20)
    {
        if ($purchase_order_id <= 0)
            return $this->success(['total' => 0, 'list' => []]);

        $loginUser = User::getLoginUser();
        $listObj = new InvoiceExportFileList($loginUser->getUserId(), Constants::TYPE_PURCHASE_ORDER);
        $listObj->setReferId($purchase_order_id);
        $listObj->setType($type);
        $listObj->setUserId($user_id);
        if ($recent_flag) {
            $listObj->setRecentFlag($recent_flag);
        }
        $listObj->setOffset(($page - 1) * $page_size);
        $listObj->setLimit($page_size);

        $list = [];
        $count = $listObj->count();
        if($count > 0){
            $list = $listObj->find();
        }

        return $this->success(
            [
                'total' => $count,
                'list' => $list
            ]
        );
    }

    public function actionRecycleInfo($purchase_order_id, array $groups = [])
    {
        $user = User::getLoginUser();
        $purchaseOrder = new PurchaseOrder($user->getClientId());
        $purchaseOrder->loadById($purchase_order_id);
        $purchaseOrder->setDomainHandler($user);
        $purchaseOrder->isViewAble(true);
        $purchaseOrder->getFormatter()->recycleInfoSetting();
        $purchaseOrder->getFormatter()->setGroupIds($groups);
        $data = $purchaseOrder->getAttributes();
        return $this->success($data);
    }


    /**
     * 根据筛选条件导出采购订单excel  (export_v2)
     * @param string $query_params query_params.show_product:1-'详细版' 2-'简洁版';   download_format: 0-同单据下产品行合并为一行  1-每产品占一行
     * @param string $fields
     * @return false|string
     */
    public function actionExportByFilter($query_params='{}', $fields='{}') {
        $queryParams = json_decode($query_params, true);
        $fields = json_decode($fields, true);
        $this->initExportRead();
        if (!PrivilegeHelper::hasPermission(
            $this->getLoginUserClientId(),
            $this->getLoginUserId(),
            $this->exportPrivilege
        )) {
            throw new \RuntimeException(\Yii::t('privilege', 'Missing permission'), \ErrorCode::CODE_FAIL);
        }

        $export = new Export($this->getLoginUserClientId());
        $export->params = [
            'query_params' => $queryParams,
            'fields' => $fields
        ];
        $export->file_type = ExportConstants::EXPORT_FILE_TYPE_EXCEL;
        $export->scene = $this->exportScene;
        $export->type = $this->exportType;
        $export->user_id = $this->getLoginUserId();
        $export->create();

        /**
         * @var PurchaseOrderExportExecutor $exportExecutor
         */
        $exportExecutor = new $this->exportExecutor($export);
        $exportExecutor->runAsync();

        return $this->success(['export_id' => $export->export_id]);

    }

    /**
     * @param $purchase_order_id
     * @param array $purchase_product_id
     * @return false|string
     * @throws \xiaoman\orm\exception\QueryException
     * 获取采购订单产品数据填充到采购单 下推填充使用
     */
    public function actionPurchaseProductList(
        array $purchase_order_id = [],
        array $purchase_product_id = [],
        array $transfer_invoice_id = []
    )
    {
        $this->validate([
            'purchase_order_id' => 'array',
            'purchase_order_id*' => 'numeric',
            'purchase_order_product_id' => 'array',
            'purchase_order_product_id*' => 'numeric',
            'transfer_invoice_id' => 'array',
            'transfer_invoice_id*' => 'numeric',
        ]);

        $user = User::getLoginUser();

        $filter = new PurchaseOrderProductFilter($user->getClientId());
        if (!empty($transfer_invoice_id)) {
            $productTransferFilter = TransferFilterFactory::make(\Constants::TYPE_PRODUCT_TRANSFER_INBOUND, $user->getClientId());
            $productTransferFilter->transfer_invoice_id = $transfer_invoice_id;
            $productTransferFilter->delete_flag = 0;
            $list = $productTransferFilter->rawData();
            $target_purchase_order_id = array_column($list, 'refer_id');
            !empty($purchase_order_id) && $purchase_order_id = array_intersect($purchase_order_id, $target_purchase_order_id);
            $purchase_order_id = $target_purchase_order_id;
            if (empty($purchase_order_id)) {
                return $this->success([
                    'list' => [],
                    'count' => 0
                ]);
            }
        }

        $filter->purchase_order_id = $purchase_order_id;
        if (!empty($purchase_product_id))
            $filter->purchase_order_product_id = new In($purchase_product_id);
        $filter->enable_flag = 1;
        $filter->order('order_num','desc');
        $filter->order('purchase_order_product_id');
        $productList = $filter->find();
        $productList->getFormatter()->purchaseInboundUseSetting();
        $list = $productList->getListAttributes();
        return $this->success([
            'list' => $list,
            'count' => count($list)
        ]);
    }

    /**
     * @param $purchase_order_id
     * @param array $purchase_product_id
     * @return false|string
     * @throws \xiaoman\orm\exception\QueryException
     * 采购订单关联采购入库单明细列表 展示使用
     */
    public function actionPurchaseProductInfoList(
        array $purchase_order_id = [],
        array $purchase_product_id = [],
    )
    {
        $this->validate([
            'purchase_order_id' => 'array',
            'purchase_order_id*' => 'numeric',
            'purchase_order_product_id' => 'array',
            'purchase_order_product_id*' => 'numeric',
        ]);

        $user = User::getLoginUser();

        $filter = new PurchaseOrderProductFilter($user->getClientId());
        $filter->purchase_order_id = $purchase_order_id;
        if (!empty($purchase_product_id))
            $filter->purchase_order_product_id = new In($purchase_product_id);
        $filter->enable_flag = 1;
        $filter->order('order_num','desc');
        $filter->order('purchase_order_product_id');
        $productList = $filter->find();
        $productList->getFormatter()->purchaseInboundUseInfoSetting();
        $list = $productList->getListAttributes();
        return $this->success([
            'list' => $list,
            'count' => count($list)
        ]);
    }

    /**
     * 采购订单查看入库明细
     * @param $purchase_order_id
     */
    public function actionPurchaseInboundRecordList($purchase_order_id,$page_no = 1, $page_size = 20)
    {
        $this->validate([
            'purchase_order_id' => 'numeric|required'
        ]);

        $filter = new PurchaseOrderProductFilter($this->getLoginUserClientId());
        $filter->purchase_order_id = $purchase_order_id;
        $filter->enable_flag = 1;
        $filter->select(['purchase_order_id', 'purchase_order_product_id']);
        $purchaseProductList = $filter->rawData();

        $purchaseOrderProductIds = array_unique(array_column($purchaseProductList,'purchase_order_product_id'));

        $inboundRecordFilter = new PurchaseInboundRecordFilter($this->getLoginUserClientId());
        $inboundRecordFilter->refer_id = $purchase_order_id;
        $inboundRecordFilter->refer_type = Constants::TYPE_PURCHASE_ORDER;
        $inboundRecordFilter->sub_refer_id = new In($purchaseOrderProductIds);
        $inboundRecordFilter->delete_flag = Constants::DELETE_FLAG_FALSE;
        $inboundRecordFilter->limit($page_size, $page_no);

        $inboundRecordList = $inboundRecordFilter->find();
        $inboundRecordList->getFormatter()->purchaseOrderInboundRecordSetting();
        $data = $inboundRecordList->getAttributes();

        return $this->success([
            'list' => $data,
            'count' => $inboundRecordFilter->count()
        ]);
    }

    //采购订单- 入库任务列表
    public function actionInboundProductTransferList(
        string $purchase_order_id = null,
        string $sort_field = 'create_time',
        string $sort_type = 'desc',
        int $page_size = 20,
        int $page_no = 1
    )
    {
        $this->validate([
            'purchase_order_id' => 'numeric|required',
            'sort_type' => 'string|in:desc,asc'
        ]);

        $data = \common\library\oms\product_transfer\inbound\InboundProductTransferAPI::getListByPurchaseOrder(
            $this->getLoginUserClientId(),
            $purchase_order_id,
            $sort_field,
            $sort_type,
            $page_size,
            $page_no
        );
        return $this->success($data);
    }

    /**
     * 采购订单-付款信息统计
     * ！！后期采购订单跟应付款单 关系为1:n ，目前关系是1:1  此时的统计计算方式将要改变
     * @param int $purchase_order_id
     * @return false|string
     */
    public function actionPaymentStatistics(int $purchase_order_id)
    {
        $this->validate([
            'purchase_order_id' => 'numeric|required',
        ]);

        $data = (new PayableInvoiceApi())->getPaymentStatistics($this->getLoginUserClientId(), $purchase_order_id,  Constants::TYPE_PURCHASE_ORDER);
        return $this->success($data);
    }

    //采购订单-付款信息列表
    public function actionPaymentList(
        $purchase_order_id,
        string $sort_field = 'create_time',
        string $sort_type = 'desc',
        $page_no = 1,
        $page_size = 20,
        $skip_privilege = 0,
    )
    {
        $this->validate([
            'purchase_order_id' => 'numeric|required',
            'sort_field' => 'regex:/^\w{2,32}$/',
            'sort_type' => 'string|in:desc,asc'
        ]);

        $data = (new PaymentInvoiceApi())->getPaymentList($this->getLoginUserClientId(), $purchase_order_id, Constants::TYPE_PURCHASE_ORDER, $sort_field, $sort_type, $page_no, $page_size, $skip_privilege);
        return $this->success($data);
    }

    //采购订单申请付款数据展示
    public function actionApplyPaymentList(array $purchase_order_id)
    {
        $this->validate([
            'purchase_order_id' => 'array',
            'purchase_order_id*' => 'numeric',
        ]);

        $filter = new PurchaseOrderFilter($this->getLoginUserClientId());
        $filter->purchase_order_id = $purchase_order_id;
        $filter->enable_flag = \Constants::ENABLE_FLAG_TRUE;

        $list = $filter->find();
        $list->getFormatter()->applyPaymentListSetting();
        $data = $list->getAttributes();
        return $this->success($data);
    }

    //采购订单-关联销售订单列表
    public function actionRelateOrderList(array $purchase_order_id)
    {
        $this->validate([
            'purchase_order_id' => 'array',
            'purchase_order_id.*' => 'numeric',
        ]);

        $user = User::getLoginUser();

        // 查询采购订单相关的销售订单id
        $purchaseProduct = new PurchaseOrderProductFilter($user->getClientId());
        $purchaseProduct->purchase_order_id = $purchase_order_id;
        $purchaseProduct->enable_flag = \Constants::ENABLE_FLAG_TRUE;
        $purchaseProduct->select(['order_id'])->groupBy('order_id');
        $targetOrderIds = array_column($purchaseProduct->rawData(), 'order_id');

        $data = [];
        if(empty($targetOrderIds)){
            return $this->success($data);
        }
        $orderList = new OrderList($user->getUserId());
        $orderList->setSkipPermissionCheck(true);
        $orderList->setOrderIds($targetOrderIds);
        $orderList->setEnableFlag(\Constants::ENABLE_FLAG_TRUE);
        $orderList->getFormatter()->setShowStatusInfo(true);
        $data = $orderList->find();

        return $this->success($data);
    }

    //采购管理模块 展示协同纬度采购订单产品信息
    public function actionListSelectPurchaseProductToInbound(
        $product_no_keyword = "",
        $product_name_keyword = "",
        $product_model_keyword = "",
        $serial_id = "",
        $purchase_order_no = "",
        $order_no = "",
        array $payment_status = [],
        array $status = [],
        $purchase_date_start = null,
        $purchase_date_end = null,
        $delivery_date_start = null,
        $delivery_date_end = null,
        $expect_time_start = null,
        $expect_time_end = null,
        $create_time_start= null,
        $create_time_end= null,
        $update_time_start= null,
        $update_time_end= null,
        array $inbound_status = [],
        array $handler = [],
        array $supplier_id = [],
        $supplier_keyword = '',
        $transfer_invoice_serial_id = "",
        $transfer_invoice_name = "",
        array $create_user = [],
        array $update_user = [],
        array $transfer_invoice_status = [
            OmsConstant::PRODUCT_TRANSFER_STATUS_TO_HANDLE,
            OmsConstant::PRODUCT_TRANSFER_STATUS_NOT_ACCEPTED,
            OmsConstant::PRODUCT_TRANSFER_STATUS_ACCEPTED
        ],
        $page_no = 1,
        $page_size = 20,
        $sort_field = 'update_time',
        $sort_type = 'desc',
    )
    {
        $params = [];
        $method = new \ReflectionMethod(__METHOD__);
        foreach ($method->getParameters() as $parameter)
        {
            $params[$parameter->name] = ${$parameter->name} ?? null;
        }
        $user = User::getLoginUser();

        $data = \common\library\purchase\purchase_order\API::selectPurchaseProductToInboundList($user->getUserId(), $params);
        return $this->success($data);
    }




    /**
     * 获取触发字段（关系字段 + 公式因子字段）
     * @param string $object_name
     * @return void
     */
    public function actionTriggerField()
    {
        $user = \User::getLoginUser();
        $clientId = $user->getClientId();
        $triggerField = \common\library\object\field\service\FunctionFieldService::triggerField($clientId, ObjConstant::OBJ_PURCHASE_ORDER, \common\library\object\field\FieldConstant::RES_TRIGGER_FIELD_TYPE_GROUP);
        foreach ($triggerField as &$item) {
            if ($item['id'] == \common\library\custom_field\CustomFieldService::PURCHASE_ORDER_GROUP_PRODUCT) {
                $item['field'][] =  \common\library\object\field\updator\calculator\SyncCalculator::SUB_OBJECT_INDEX_KEY;
            }
        }
        return $this->success($triggerField);
    }


    /**
     * 获取功能字段属性值
     * @param array $data
     *
     * @return void
     */
    public function actionFunctionFieldValue($data, $modified_fields = null)
    {
        $this->validate([
            'data' => 'required',
        ]);
        $data = json_decode($data,true);
        $modified_fields = is_array($modified_fields) ? $modified_fields : json_decode($modified_fields, true);

        $user = \User::getLoginUser();
        $clientId = $user->getClientId();

        $functionFieldVal = \common\library\purchase\purchase_order\API::functionFieldValue($data, $clientId, $modified_fields);
        $this->success($functionFieldVal);
    }

    public function actionProductList(
        $page_no = 1,
        $page_size = 20,
        $sort_field = '',
        $sort_type = '',
        $product_id = 0,
        $scene = APIConstant::SCENE_LIST,
        array $handler = [],
        $skip_permission_check = 0,
        array $query_filters = [],
    ) {

        $user = \User::getLoginUser();

        $params = [];
        $method = new \ReflectionMethod(__METHOD__);
        foreach ($method->getParameters() as $parameter) {
            $params[$parameter->name] = ${$parameter->name} ?? null;
        }

        $clientId = $user->getClientId();

        if (!PrivilegeHelper::hasPermission($user->getClientId(),$user->getUserId(),
            PrivilegeConstants::PRIVILEGE_CRM_PURCHASE_ORDER_VIEW)
        ){
            throw new \RuntimeException(\Yii::t('privilege', 'Missing permission'), \ErrorCode::CODE_INVOICE_NOT_PRIVILEGE_VIEW);
        }

        $api = new \common\library\purchase\purchase_order_product\PurchaseOrderProductAPI($clientId);
        $data = $api->productList($user->getUserId(), $params);
        $this->success($data);

    }

    public function actionProductStatistics(
        $page_no = 1,
        $page_size = 20,
        $sort_field = '',
        $sort_type = '',
        $product_id = 0,
        $scene = APIConstant::SCENE_LIST,
        array $handler = [],
        $skip_permission_check = 0,
        array $query_filters = [],
    ){
        $user = \User::getLoginUser();

        $params = [];
        $method = new \ReflectionMethod(__METHOD__);
        foreach ($method->getParameters() as $parameter) {
            $params[$parameter->name] = ${$parameter->name} ?? null;
        }

        $clientId = $user->getClientId();

        if (!PrivilegeHelper::hasPermission($user->getClientId(),$user->getUserId(),
            PrivilegeConstants::PRIVILEGE_CRM_PURCHASE_ORDER_VIEW)
        ){
            throw new \RuntimeException(\Yii::t('privilege', 'Missing permission'), \ErrorCode::CODE_INVOICE_NOT_PRIVILEGE_VIEW);
        }

        $params['scene'] = APIConstant::SCENE_STATISTICS;

        $api = new \common\library\purchase\purchase_order_product\PurchaseOrderProductAPI($user->getClientId());
        $data = $api->statistics($user->getUserId(), $params);

        $privilegeFieldStats = Helper::getPrivilegeFieldStats($this->getLoginUserClientId(),
            $this->getLoginUserId(),
            PrivilegeConstants::FUNCTIONAL_PURCHASE_ORDER);

        $data['privilege_field_stats'] = $privilegeFieldStats;
        $this->success($data);

    }

    public function actionProductListStatistics(
        $page_no = 1,
        $page_size = 20,
        $sort_field = '',
        $sort_type = '',
        array $handler = [],
        $skip_permission_check = 0,
        array $query_filters = [],
    ) {
        $params = [];
        $method = new \ReflectionMethod(__METHOD__);
        foreach ($method->getParameters() as $parameter) {
            $params[$parameter->name] = ${$parameter->name} ?? null;
        }

        $user = \User::getLoginUser();
        $clientId = $user->getClientId();
        $params['scene'] = APIConstant::SCENE_STATISTICS;

        if (!PrivilegeHelper::hasPermission($user->getClientId(),$user->getUserId(),
            PrivilegeConstants::PRIVILEGE_CRM_PURCHASE_ORDER_VIEW)
        ){
            throw new \RuntimeException(\Yii::t('privilege', 'Missing permission'), \ErrorCode::CODE_INVOICE_NOT_PRIVILEGE_VIEW);
        }

        $api = new \common\library\purchase\purchase_order_product\PurchaseOrderProductAPI($clientId);
        $data['status_statistics'] = $api->purchaseOrderStatusStatistics($user->getUserId(), $params);

        $this->success($data);
    }
}