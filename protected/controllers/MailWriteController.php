<?php
/**
 * Copyright (c) 2012 - 2018 Xiaoman.All Rights Reserved
 * Author: nuxse
 * Data: 2017/9/13
 */

use common\library\email\Util;
use common\library\external_file\Constants as ExternalFileConstants;
use common\library\external_file\File as ExternalFile;
use common\library\mail\Helper;
use common\library\mail\service\ExposeService;
use common\library\report\sensors\events\EventSendEmail;
use common\library\setting\user\UserSetting;
use common\models\client\MailRemark;

class MailWriteController extends Controller
{
    public function filters()
    {
        return CMap::mergeArray(
            parent::filters(), [
                'duplicateRequest' => 'duplicateRequest + send,sendMail',
                'setSendFrequencyLimit' => 'setSendFrequencyLimit + send',
            ]
        );
    }

    /**
     * 检查头部标识并设置邮件发件频率是否限制
     * @param $filterChain
     */
    public function filterSetSendFrequencyLimit($filterChain)
    {
        // 桌面端不限制腾讯、网易系邮箱发件频率
        \common\library\email\Email::$checkSendFrequencyLimit = true;
        $device_type = Helper::getDeviceType();
        if ($device_type == \common\library\mail\Mail::DEVICE_TYPE_DESKTOP_NEW || $device_type == \common\library\mail\Mail::DEVICE_TYPE_DESKTOP_OLD) {
            \common\library\email\Email::$checkSendFrequencyLimit = false;
        }
        $filterChain->run();
    }

    protected function beforeAction($action)
    {
        //web 应用层接口开启审批流  防止其他业务调用模型触发审批流
        \common\library\mail\Mail::$enableForApprovalFlow = true;

        if(!empty($_GET['approval_flow_confirm']) || !empty($_POST['approval_flow_confirm'])) {
            \common\library\mail\Mail::$matchOnly = false;
        }else{
            \common\library\mail\Mail::$matchOnly = true;
        }
        return true;
    }


    /**
     * 保存草稿
     * @param null $mail_id
     * @param $receiver
     * @param $subject
     * @param $content
     * @param $user_mail_id
     * @param string $cc
     * @param string $bcc
     * @param array $attachment_list
     * @param array $inline_image_list
     * @param int $urgent_flag
     * @param int $receipt_flag
     * @param int $expose_flag
     * @param int $track_flag
     * @param int $sign_id
     * @return string
     */
    public function actionSave(
        $receiver, //接受人
        $subject,  //邮件主题
        $content,   //邮件内容
        $user_mail_id, //发件人的邮箱id
        $mail_id = null, //邮件id
        $alias_id = 0, //别名邮箱id
        $plan_send_time = null, // 当地时间
        $plan_send_timezone = null, // 当地时区
        $cc = '', //抄送
        $bcc = '', //密送
        array $attachment_list = [], //附件列表
        array $inline_image_list = [], //内置图片列表
        $urgent_flag = 0, //是否紧急邮件呢
        $receipt_flag = 0,    //是否回执
        $expose_flag = null,//是否群发单显
        $track_flag = \Mail::TRACK_FLAG_TRUE,//是否追踪
        $sign_id = null,
        $plan_type = 0,//1标识本地定时群发，默认0其他
        array $mail_expose_info = [],//群发单显信息，["user_mail_ids":[123,123,123],"subject":["xxx","xxx","xxx"]]，
        $conference_flag = 0,
        array $conference_info = [],
    )
    {
        try {

            $this->validate([
                'receiver' => 'string',
                'subject' => 'string',
                'content' => 'string',
                'user_mail_id' => 'numeric',
                'mail_id' => 'numeric',
                'alias_id' => 'numeric',
                'expose_flag' => 'numeric',
                'mail_expose_info' => 'array',
                'mail_expose_info.user_mail_map' => 'array',
                'mail_expose_info.user_mail_map.*' => 'array',
                'mail_expose_info.subject' => 'array',
                'mail_expose_info.subject.*' => 'string',
                'plan_type' => 'numeric',
                'mail_expose_info.title' => 'string|max:128',
                'mail_expose_info.fill_nickname' => 'numeric|in:0,1',
                'conference_flag' => 'numeric|in:0,1',
                'conference_info' => 'array',
            ]);

            $user = User::getLoginUser();
            $userId = $user->getUserId();
            $clientId = $user->getClientId();

            $receiver = Util::trimNickname($receiver);

            $cc = Util::trimNickname($cc);

            $bcc = Util::trimNickname($bcc);

            $mail = new \common\library\mail\Mail($mail_id);
            $mail->getAccessService()->checkWriteAccess();
            $mail->receiver = $receiver;
            $mail->subject = $subject;
            $mail->cc = $cc;
            $mail->bcc = $bcc;
            $mail->urgent_flag = $urgent_flag;
            $mail->receipt_flag = $receipt_flag;
            $mail->track_flag = $track_flag;
            $mail->conference_flag= $conference_flag;
            // 用户点级回复的时候自动生成了一份影藏草稿，编辑查看1min之后会自动保存成为一个正常草稿。这里针对这个场景特殊处理
            if ($mail->folder_id == \Mail::FOLDER_HIDDEN_DRAFT_ID) {
                $mail->folder_id = \Mail::FOLDER_DRAFT_ID;
            }
            $mail->setUserMailId($user_mail_id, (int)$alias_id);
            $mail->setContent($content);
            $mail->setAttachmentList($attachment_list);
            $mail->setInlineImageList($inline_image_list);
            $mail->setExposeFlag(intval($expose_flag));
            $mail->setSignId($sign_id);

            $mail->setPlanType(intval($plan_type));
            if (is_numeric($plan_send_timezone)){
                // 允许0时区,不允许null和''
                $mail->setPlanSendTimeZone($plan_send_timezone);
                $mail->setPlanSendTime($plan_send_time);
            }



//          群发单显调整

//          状态为已发送的非定时邮件（包括普邮+群发单显）不可以二次编辑
            if ($mail->mail_type != \Mail::MAIL_TYPE_UNKNOWN && $mail->send_status != \Mail::SEND_STATUS_SEND_UNKNOWN)
            {
                $exposeMailInfo = \MailExternal::findByMailIds([$mail_id], ['plan_send_time', 'plan_send_timezone']);
                if (empty($exposeMailInfo[$mail_id]['plan_send_time'] ?? '')) {
                    throw new RuntimeException(\Yii::t('mail', 'Incorrect message type'), ErrorCode::CODE_FAIL);
                }
                
                // 获取定时发送时间和时区
                $planSendTime = $exposeMailInfo[$mail_id]['plan_send_time'];
                $planSendTimeZone = $exposeMailInfo[$mail_id]['plan_send_timezone'] ?? null;
                
                // 根据时区转换时间进行比较
                if ($planSendTimeZone != null) {
                    // 如果时区不为空，转换为东八区时间再比较
                    $realSendTime = \common\library\mail\Helper::convertToGMT8($planSendTime, $planSendTimeZone);
                    if (strtotime($realSendTime) < time()) {
                        throw new RuntimeException(\Yii::t('mail', 'Incorrect message type'), ErrorCode::CODE_FAIL);
                    }
                } else {
                    // 如果时区为空，直接比较（默认东八区）
                    if (strtotime($planSendTime) < time()) {
                        throw new RuntimeException(\Yii::t('mail', 'Incorrect message type'), ErrorCode::CODE_FAIL);
                    }
                }
            }
            $exposeUserMailIds = array_filter(array_unique(array_column($mail_expose_info['user_mail_map'] ?? [], 'user_mail_id')));
            if ($expose_flag && in_array($mail->folder_id, [\Mail::FOLDER_HIDDEN_DRAFT_ID, \Mail::FOLDER_DRAFT_ID]) && (count($exposeUserMailIds) > 0))
            {
                $signMode = $mail_expose_info['sign_mode'] ?? 0;
                if ($signMode > 2) {
                    throw new RuntimeException(\Yii::t('mail', 'Customer mail parameter error'));
                }

                $subject = $mail_expose_info['subject'] ?? [];
                $userMailMap = $mail_expose_info['user_mail_map'] ?? [];
                $title = $mail_expose_info['title'] ?? '';
                $fillNickname = $mail_expose_info['fill_nickname'] ?? 0;
                $exposeUserMailInfo = [
                    'title' => $title,
                    'user_mail_ids' => $exposeUserMailIds,
                    'subject' => $subject,
                    'sender' => '',
                    'sign_mode' => $signMode,
                    'user_mail_map' => $userMailMap,
                    'fill_nickname' => $fillNickname,
                ];

                $mail->setExposeUserMailInfo($exposeUserMailInfo, true);
            }
            if ($conference_flag == 1) {
                $conference_info['method'] = MailConferenceModel::CONFERENCE_METHOD_REQUEST;
                $mail->setConferenceInfo($conference_info);
            }

            //兼容旧pb发送接口的设备deviceType值设置
            $deviceType = Helper::getDeviceTypeForExposeDesktop();
            if (!empty($deviceType)) $mail->setExposeDeviceType($deviceType);

            $mail->save();
            return $this->success(['mail_id' => $mail->getMailId()]);
        } catch (\Exception $exception) {
            return $this->fail($exception->getCode(),$exception->getMessage());
        }
    }

    public function actionSend(
        $receiver, //接受人
        $subject,  //邮件主题
        $content,   //邮件内容
        $user_mail_id, //发件人的邮箱id
        $mail_id = null, //邮件id
        $alias_id = 0, //别名邮箱id
        $cc = '', //抄送
        $bcc = '', //密送
        array $attachment_list = [], //附件列表
        array $inline_image_list = [], //内置图片列表
        $urgent_flag = 0, //是否紧急邮件呢
        $receipt_flag = 0,    //是否回执
        $expose_flag = null,//是否群发单显
        $plan_send_time = null, // 当地时间
        $plan_send_timezone = null, // 当地时区
        $plan_type = 0,//1标识本地定时群发，默认0其他
        $track_flag = \Mail::TRACK_FLAG_TRUE,//是否追踪
        array $image_src_list = [],
        $sign_id = null,
        $type = null,
        array $mail_expose_info = [],//群发单显信息，["user_mail_ids":[123,123,123],"subject":["xxx","xxx","xxx"]]
        $conference_flag = 0,
        array $conference_info = [],
    )
    {
        $request = $_REQUEST;
        unset($request['content']);
        $http_referer = $_SERVER['HTTP_REFERER'] ?? '';
        \LogUtil::info("mail_send_params: mail_id{$mail_id}" . var_export($request, true).'HTTP_REFERER:'.$http_referer . ' HTTP_X_XIAOMAN_OS:' . ($_SERVER['HTTP_X_XIAOMAN_OS'] ?? ''));

        $this->validate([
            'subject' => 'string',
            'content' => 'string',
            'user_mail_id' => 'required|not_empty|numeric',
            'mail_id' => 'required|not_empty|numeric',
            'alias_id' => 'numeric',
            'expose_flag' => 'numeric',
            'plan_type' => 'numeric',
            'mail_expose_info' => 'array',
            'mail_expose_info.user_mail_map' => 'array',
            'mail_expose_info.user_mail_map.*' => 'array',
            'mail_expose_info.subject' => 'array',
            'mail_expose_info.subject.*' => 'string',
            'mail_expose_info.title' => 'string|max:128',
            'mail_expose_info.fill_nickname' => 'numeric|in:0,1',
            'conference_flag' => 'numeric|in:0,1',
            'conference_info' => 'array',
        ]);

        $user = User::getLoginUser();
        $userId = $user->getUserId();
        $clientId = $user->getClientId();

        $inline_image_list = \common\library\mail\Helper::transferToInlineImage($clientId, $userId, $inline_image_list,
            $image_src_list, $content);

	    $receiver = Util::trimNickname($receiver);

	    $cc = Util::trimNickname($cc);

	    $bcc = Util::trimNickname($bcc);

        $mail = new \common\library\mail\Mail($mail_id);
        $mail->getAccessService()->checkWriteAccess();
        $mail->receiver     = $receiver;
        $mail->subject      = $subject;
        $mail->cc           = $cc;
        $mail->bcc          = $bcc;
        $mail->urgent_flag  = $urgent_flag;
        $mail->receipt_flag = $receipt_flag;
        $mail->track_flag   = $track_flag;
        $mail->conference_flag = $conference_flag;
        $mail->setContent($content);
        $mail->setUserMailId($user_mail_id, (int) $alias_id);
        $mail->setExposeFlag(intval($expose_flag));
        $mail->setPlanType(intval($plan_type));
        $mail->setAttachmentList($attachment_list);
        $mail->setInlineImageList($inline_image_list);
        $mail->setSignId($sign_id);


        if (is_numeric($plan_send_timezone)){
            $mail->setPlanSendTimeZone($plan_send_timezone);
        }

        if (empty($plan_send_time) && is_numeric($plan_send_timezone)){
            // 再次编辑会导致时间为空但是时区信息是上一次的遗留的,本次编辑需要覆盖为东八区否则会导致用户设置了延迟发送的情况下还按旧时区和东八区时间计算
            $mail->setPlanSendTimeZone('8');
        }

        // 这里面会把 plan_send_time 设置到 tbl_mail_external里面去
        $mail->setPlanSendTime($plan_send_time);

        // 如果$plan_send_time为空直接读取用户设置的邮件延迟发送配置，决定是及时发送还是改为定时发送
        $delaySendTime = \common\library\mail\Helper::getPlanSendTimeByUserSetting($userId);
        if (empty($plan_send_time) && !empty($delaySendTime)) {
            $mail->setPlanSendTime($delaySendTime);
            $mail->setDelaySendFlag(1);
        } elseif (empty($plan_send_time) && $mail->getDelaySendFlag()) {
            // 再次编辑点发送需要根据标识位判断是延迟发送邮件（再次延迟时间更新）
            $mail->setPlanSendTime($delaySendTime);
        } elseif (!empty($plan_send_time) && $mail->getDelaySendFlag()) {
            $mail->setDelaySendFlag(0);
        }



//        群发单显调整
        $exposeUserMailIds = array_filter(array_unique(array_column($mail_expose_info['user_mail_map'] ?? [],'user_mail_id')));
        if ($expose_flag == 1 && count($exposeUserMailIds) > 0) {
            $signMode = $mail_expose_info['sign_mode'] ?? 0;
            if ($signMode > 2) {
                throw new RuntimeException(\Yii::t('mail', 'Customer mail parameter error'));
            }

            $userMailMap = $mail_expose_info['user_mail_map'] ?? [];
            $subject = $mail_expose_info['subject'] ?? [];
            $title = $mail_expose_info['title'] ?? '';
            $fillNickname = $mail_expose_info['fill_nickname'] ?? 0;
		    $exposeUserMailInfo = [
                'title'         => $title,
			    'user_mail_ids' => $exposeUserMailIds,
			    'subject'       => $subject,
			    'sender'        => '',
			    'sign_mode'     => $signMode,
                'user_mail_map' => $userMailMap,
                'type'          => $type,
                'fill_nickname' => $fillNickname,
		    ];

		    $mail->setExposeUserMailInfo($exposeUserMailInfo, true);
	    }

        //发送会议邮件
        if ($conference_flag == 1 && count($conference_info) >0 ) {
            $conference_info = Helper::checkConferenceInfo($conference_info);
            //发送会议邮件
            $conference_info['method'] = MailConferenceModel::CONFERENCE_METHOD_REQUEST;
            $mail->setConferenceInfo($conference_info);
        }

        //兼容旧pb发送接口的设备deviceType值设置
        $deviceType = Helper::getDeviceTypeForExposeDesktop();
        if (!empty($deviceType)) $mail->setExposeDeviceType($deviceType);

	    $mail->send();

        // 上报神策
        \common\library\mail\Helper::sensorsSendMailReport($clientId, $userId, EventSendEmail::PLATFORM_WEB, $mail->getAgent(), $mail->getExposeFlag(), $receiver);

        return $this->success([
            'mail_id'       => $mail->getMailId(),
            'time_flag'     => $mail->time_flag,
            'allow_to_send' => $expose_flag == 1 ? 100 : \common\library\email\Email::getEmailObject($mail->getUserMailId(),
                $mail->getOperatorUserId())->allowToSend(),
            'current_send'  => $expose_flag == 1 ? 0 :  \common\library\email\Email::getEmailObject($mail->getUserMailId(),
                $mail->getOperatorUserId())->currentSend(),
            'send_server_type' => \common\library\email\Email::getEmailObject($mail->getUserMailId(), $mail->getOperatorUserId())->getEmailSendServerType()
        ]);
    }

    /**
     * 用于审批发送按钮、草稿发送
     */
    public function actionSendMail($mail_id)
    {
        $mail = new \common\library\mail\Mail($mail_id);

        $mail->getAccessService()->checkSendAccess();

        $mail->send();

        return $this->success([
            'mail_id'       => $mail->getMailId(),
            'time_flag'     => $mail->time_flag,
//            TODO
            'allow_to_send' => $mail->isExpose() ? 100 :  \common\library\email\Email::getEmailObject($mail->getUserMailId(),
                $mail->getOperatorUserId())->allowToSend(),
            'current_send'  => $mail->isExpose() ? 0 :  \common\library\email\Email::getEmailObject($mail->getUserMailId(),
                $mail->getOperatorUserId())->currentSend()
        ]);
    }


    /**
     * 获取邮件回复
     * @param $mail_id
     * @param $user_mail_id
     * @return string
     */
    public function actionReply($mail_id, $user_mail_id = null, $reply_with_attachment=0)
    {
        $v = new Validator($this);
        $v->setInputData([
            'mail_id' => $mail_id
        ]);
        $v->validate('base');

        $mail = new \common\library\mail\Mail($mail_id);
        $mail->getAccessService()->checkReplyAccess();
        $replyMail = $mail->reply($user_mail_id,true,$reply_with_attachment);
        $replyMail->getFormatter()->baseInfoSetting();
        $replyData = $replyMail->getAttributes();

        $extend = [
            'is_phishing' => $mail->isPhishing(),
            //回复邮件是否是自己的
            'is_reply_self' => $mail->getAccessService()->isOwner(),
            'alias_id' => $replyMail->getExtendAttributes()['alias_id'] ?? 0
        ];

        $data = $replyData + $extend;

        return $this->success($data);
    }

    public function actionReplyAll($mail_id, $user_mail_id = null, $reply_with_attachment = 0)
    {
        $mail = new \common\library\mail\Mail($mail_id);
        $mail->getAccessService()->checkReplyAccess();
        $replyAllMail = $mail->replyAll($user_mail_id, $reply_with_attachment);
        $replyAllMail->getFormatter()->baseInfoSetting();

        $replyAllData = $replyAllMail->getAttributes();

        $extend = [
            'is_phishing' => $mail->isPhishing(),
            'is_reply_self'      => $mail->getAccessService()->isOwner(),
            'alias_id' => $replyAllMail->getExtendAttributes()['alias_id'] ?? 0
        ];

        $data = $replyAllData + $extend;

        return $this->success($data);
    }

    /**
     * 快速回复,和普通的回复一样，带上签名和原文
     * @param int $mail_id
     * @param string $content
     * @param null $user_mail_id 如果没有传,默认拿原邮件的user_mail_id
     * @param string $plan_send_time 定时发送
     * @return string
     */
    public function actionFastReply(
        $mail_id,
        $content,
        $user_mail_id = null,
        $plan_send_time = null
    ) {
        $v = new Validator($this);
        $v->validate('require_content');

        $mail = new \common\library\mail\Mail($mail_id);
        $mail->getAccessService()->checkReplyAccess();

        $replyMail = $mail->replyFast($content, $user_mail_id);
        $replyMail->setPlanSendTime(!empty($plan_send_time) ? $plan_send_time : \common\library\mail\Helper::getPlanSendTimeByUserSetting($mail->getUserId())); //读取用户设置的邮件延迟发送配置，决定是及时发送还是改为定时发送
        $replyMail->send();

        $data = [
            'mail_id'       => $replyMail->getMailId(),
            'time_flag'     => $replyMail->time_flag,
            'allow_to_send' => \common\library\email\Email::getEmailObject($replyMail->getUserMailId(),
                $replyMail->getUserId())->allowToSend(),
            'current_send'  => \common\library\email\Email::getEmailObject($replyMail->getUserMailId(),
                $replyMail->getUserId())->currentSend(),
            'is_reply_self'      => $mail->getAccessService()->isOwner(),
        ];

        return $this->success($data);
    }

    /**
     * 邮件作为附件转发
     * @param $mail_id
     * @param null || int  $user_mail_id
     * @return string
     */
    public function actionForwardAsAttach($mail_id, $user_mail_id = null)
    {
        $mail = new \common\library\mail\Mail($mail_id);
        $mail->getAccessService()->checkForwardAccess();
        $forwardAttachmentMail = $mail->forwardAsAttachment($user_mail_id);

        $forwardAttachmentMail->getFormatter()->baseInfoSetting();
        $data = $forwardAttachmentMail->getAttributes();
        return $this->success($data);
    }

    public function actionForward($mail_id, $user_mail_id = null)
    {
        $mail = new \common\library\mail\Mail($mail_id);
        $mail->getAccessService()->checkForwardAccess();
        $forwardMail = $mail->forward($user_mail_id);
        $forwardMail->getFormatter()->baseInfoSetting();
        $replyData = $forwardMail->getAttributes();
        $extend = [
            'alias_id' => $forwardMail->getExtendAttributes()['alias_id'] ?? 0
        ];

        $data = $replyData + $extend;

        return $this->success($data);
    }

    /**
     * 邮件重新发送
     * @param $mail_id
     * @return string
     */
    public function actionResend($mail_id)
    {
        $mail = new \common\library\mail\Mail($mail_id);
        $mail->getAccessService()->checkSendAccess();
        $mail->setPlanSendTime(\common\library\mail\Helper::getPlanSendTimeByUserSetting($mail->getUserId())); //读取用户设置的邮件延迟发送配置，决定是及时发送还是改为定时发送
        $mail->resend();
        return $this->success([
            'result'  => true,
            'mail_id' => $mail_id
        ]);
    }

    /**
     * 将指定邮件标记为已读
     * @param array $mail_ids
     * @return string
     */
    public function actionMarkRead(array $mail_ids)
    {
        $v = new Validator($this);
        $v->validate('require_mail_ids');

        $mailBatchOperator = new \common\library\mail\MailListBatchOperate(\User::getLoginUser()->getUserId());
        $mailBatchOperator->setParams([
            'mail_ids' => $mail_ids
        ]);
        $affectedRows = $mailBatchOperator->read(\Mail::READ_FLAG_TRUE);
        LogUtil::info("markRead - mail_id[" . json_encode($mail_ids) . "] - affectRows[$affectedRows]");

        return $this->success([
            'affected_rows' => $affectedRows
        ]);
    }

    /**
     * 将指定邮件标记为未读
     * @param array $mail_ids
     * @return string
     */
    public function actionMarkUnread(array $mail_ids)
    {
        $v = new Validator($this);
        $v->validate('require_mail_ids');

        $mailBatchOperator = new \common\library\mail\MailListBatchOperate(\User::getLoginUser()->getUserId());
        $mailBatchOperator->setParams([
            'mail_ids' => $mail_ids
        ]);
        $affectedRows = $mailBatchOperator->read(\Mail::READ_FLAG_FALSE);
        LogUtil::info("markUnRead - mail_id[" . json_encode($mail_ids) . "] - affectRows[$affectedRows]");

        return $this->success([
            'affected_rows' => $affectedRows
        ]);
    }

    /**
     * 跨页全选 将指定的文件下或者邮箱下的邮件标记为已读
     * @param null $folder_id
     * @param null $user_mail_id
     * @param int $read_flag
     * @return string
     */
    public function actionUpdateAllReadFlag($folder_id = null, $user_mail_id = null, $read_flag = \Mail::READ_FLAG_TRUE)
    {
        $mailBatchOperator = new \common\library\mail\MailListBatchOperate(\User::getLoginUser()->getUserId());
        $mailBatchOperator->setParams([
            'folder_id'    => $folder_id,
            'user_mail_id' => $user_mail_id,
        ]);
        $affectedRows = $mailBatchOperator->read($read_flag);

        return $this->success([
            'affected_rows' => $affectedRows
        ]);
    }


    /**
     * 旧版邮件分发接口
     * @param array $mail_ids
     * @param $user_id
     * @param $user_mail_id
     */
    public function actionDistribute(array $mail_ids, $user_id, $user_mail_id)
    {
        if (!$user_id) {
            throw new RuntimeException(\Yii::t('mail', 'Distribution failed, please select the correct distributor'));
        }
        $loginUserInfo = $this->getLoginUserInfo();
        if (empty($mail_ids)) {
            throw new RuntimeException(\Yii::t('mail', 'Please select the mail you want to distribute'), ErrorCode::CODE_FAIL);
        }
        $targetUser = User::getUserObject($user_id);
        if (!$targetUser->isBindEmail()) {
            throw new RuntimeException(\Yii::t('mail', 'The user has not yet bound the mailbox'), ErrorCode::CODE_FAIL);
        }
        $targetUserInfo = $targetUser->getInfoObject();
        if ($targetUserInfo->client_id != $loginUserInfo->client_id) {
            throw new RuntimeException(\Yii::t('mail', 'The user does not exist'), ErrorCode::CODE_FAIL);
        }

        $check = $targetUser->hasBindEmailByUserMailId($user_mail_id);
        if (!$check) {
            throw  new RuntimeException(\Yii::t('mail', 'The user is not bound the mailbox, please re-select'));
        }

        $distributeService = new \common\library\mail\service\DistributeService($mail_ids, $user_mail_id);
        $result            = $distributeService->distribute();
        return $this->success(['result' => $result]);
    }


    /**
     * @param array $mail_ids [mailId1,...,mailIdn]
     * @param array $target_user_mail_ids [userMailId1,...,userMailIdn]
     * @return string
     */
    public function actionBatchDistribute(array $mail_ids, array $target_user_mail_ids)
    {
        \LogUtil::info("mailIds[" . json_encode($mail_ids) . "] -- targetUserMailIds[" . json_encode($target_user_mail_ids) . "]");
        //分发邮件
        $distributeService = new \common\library\mail\service\DistributeService($mail_ids, $target_user_mail_ids);
        $result            = $distributeService->distribute();
        return $this->success(['result' => $result]);

    }

    /**
     * 收信
     * @param array $user_mail_id
     * @return string
     */
    public function actionReceive(array $user_mail_id = [])
    {
        $receive = new \common\library\mail\service\ReceiveService($user_mail_id);
        $receive->refresh();
        return $this->success('');
    }

    /**
     * 下属收信
     * @param int $user_id
     * @return string
     */
    public function actionSubordinateReceive($user_id = 0)
    {
        $user = User::getLoginUser();
        $userMailIds = [];

        //判断是否是下属
        if ($user_id) {
            if (!\common\library\department\Helper::canManageAnyUsers(
                $user->getClientId(),
                $user->getUserId(),
                \common\library\privilege_v3\PrivilegeConstants::PRIVILEGE_EMAIL_VIEW,
                $user_id)
            ) {
                throw new RuntimeException(\Yii::t('account', 'No permission operation'), ErrorCode::CODE_FAIL);
            }
        }

        if (empty($user_id)) {
            // 全部下属
            $departmentPermission = new \common\library\department\DepartmentPermission($user->getClientId(), $user->getUserId());
            $departmentPermission->permission(\common\library\privilege_v3\PrivilegeConstants::PRIVILEGE_EMAIL_VIEW);
            $result = $departmentPermission->userList();
            $userIds = array_column($result, 'user_id');
        } else {
            // 指定下属
            $userIds = [$user_id];
        }

        if (!empty($userIds)) {
            $userMailIds = array_column(\UserMail::findAllByUserIds($user->getClientId(), $userIds), 'user_mail_id');
        }

        if (!empty($userMailIds)) {
            $receive = new \common\library\mail\service\ReceiveService($userMailIds);
            $receive->refresh();
        }

        return $this->success('');

    }

    /**
     * 清除所有垃圾邮件
     * @param array $user_mail_id
     * @return string
     */
    public function actionClearAllJunk(array $user_mail_id = [])
    {
        $mailBatchOperator = new \common\library\mail\MailListBatchOperate(\User::getLoginUser()->getUserId());
        $mailBatchOperator->setParams([
            'folder_id'    => [\Mail::FOLDER_JUNK_ID],
            'user_mail_id' => $user_mail_id,
            'type'         => 'junk'
        ]);
        $affectedRows = $mailBatchOperator->cleanJunkMail();

        return $this->success(['affected_rows' => $affectedRows]);
    }

    public function actionBatchDownloadAttach($mail_id)
    {
        $v = new Validator($this);
        $v->validate('require_mail_id');

        $user = User::getLoginUser();

        $mail = new \common\library\mail\Mail($mail_id);
        $mail->getAccessService()->checkReadAccess();       //邮件鉴权

        \common\library\mail\Helper::batchDownloadAttach($mail);
    }

    public function actionExportEmail($mail_id)
    {
        $v = new Validator($this);
        $v->validate('require_mail_id');

        $user = User::getLoginUser();
        $userId = $user->getUserId();

        $mail = new \common\library\mail\Mail($mail_id);
        $mail->setOperatorUser($userId);

        if ($mail->isNew())
            throw new RuntimeException(\Yii::t('mail', 'Mail does not exist'));

        $mail->getAccessService()->checkReadAccess();

        $fileId = \common\library\mail\Helper::transferMailToAttachment($mail);

        $obj = new AliyunUpload();
        $obj->setHttps(true);
        if (!$obj->loadByFileId($fileId, $userId)) {
            LogUtil::error("file id:{$fileId}, user id:{$userId}");
            throw new RuntimeException(\Yii::t('file', 'File does not exist'));
        }
        header('Location: ' . $obj->generatePresignedUrl(true));
        return;
    }

    public function actionNewMail(
        array $ids = [],
        $type = '',
        $user_mail_id = 0
    ) {

        $user = User::getLoginUser();

        //查看产品
        if($type == 'product'){
            \common\library\privilege_v3\Helper::checkPermission($user->getClientId(),$user->getUserId(),
                \common\library\privilege_v3\PrivilegeConstants::PRIVILEGE_CRM_PRODUCT_VIEW);
        }

        if (!$ids) {
            throw new RuntimeException(\Yii::t('mail', 'No documents selected for sending'), 100);
        }

        if (!$user_mail_id) {
            $user_mail_id = $user->getDefaultMailId();

            if (!$user_mail_id) {
                throw  new RuntimeException(\Yii::t('mail', 'Please bind the mailbox first'), 101);
            }
        }

        $uploadFileIds  = array();
        $uploadImageIds = array();
        $callback       = null;

        switch ($type) {

            case 'disk':
                $fileList = DiskFile::findAllByIds($user->getClientId(), $ids);
                if (empty($fileList)) {
                    throw new RuntimeException(\Yii::t('mail', 'No documents selected for sending'), 102);
                }
                $uploadFileIds = array();
                foreach ($fileList as $item) {
                    $uploadFileIds[] = $item->upload_file_id;
                }


                break;
            case 'product':
                //废弃去了, 不没有产品pdf的场景了
                if (empty($uploadFileIds)) {
                    throw new RuntimeException(\Yii::t('mail', 'Please select the product to send'), 103);
                }

                break;
            case 'file':
                $uploadFileIds = $ids;
                break;

            default :
                throw new RuntimeException('type error');

        }
        $content = '';
        $mail    = new \common\library\mail\Mail();
        $mail->setUserMailId($user_mail_id);

        if (!empty($uploadFileIds)) {
            $attachList    = \UploadFile::copyNewList($uploadFileIds);
            $attachFileIds = array();
            foreach ($attachList as $item) {
                $attachFileIds[] = $item->file_id;
            }
            $mail->setAttachmentList($attachFileIds);
        }
        if (!empty($uploadImageIds)) {
            $inlineList    = \UploadFile::copyNewList($uploadImageIds);
            $inlineFileIds = array();
            foreach ($inlineList as $item) {

                $inlineFileIds[] = $item->file_id;

                $random   = str_replace(".", "", microtime(true));
                $file_url = $item->getFileUrl();
                $content  = $content."<div><img id=\"insert_img_{$random}\" class=\"xiaoman_inner_img\" src=\"{$file_url}\" alt=\"\" data-mce-file-status=\"1\" data-mce-file-id=\"{$item->file_id}\" data-mce-file-size=\"{$item->file_size}\" /></div>";
            }
            $mail->setInlineImageList($inlineFileIds);
        }
        $mail->setContent($content);
        $mail->save();

        $mailId = $mail->getMailId();

        $data = [
            'type'    => $type,
            'mail_id' => $mailId,
        ];

        $this->success($data);

    }

    /**
     * 恢复邮件到收件箱  暂且用该接口后续需要重新整理
     * @param array $mail_ids
     */
    public function actionRevertToInbox(array $mail_ids)
    {
        $v = new Validator($this);
        $v->validate('require_mail_ids');

        $mailBatchOperator = new \common\library\mail\MailListBatchOperate(\User::getLoginUser()->getUserId());
        $mailBatchOperator->setParams([
            'mail_ids' => $mail_ids
        ]);
        $affectedRows = $mailBatchOperator->move(Mail::FOLDER_INBOX_ID);

        if (!$affectedRows) {
            throw new RuntimeException(\Yii::t('mail', 'Failed to recover mail to inbox'));
        } else {
            LogUtil::info("type:move, mail_ids:" . json_encode($mail_ids) . ", folder_id:" . Mail::FOLDER_INBOX_ID);
            //返回第一个邮件id的收件箱
            $mailId     = $mail_ids[0];
            $mail       = new \common\library\mail\Mail($mailId);
            $userMailId = $mail->getUserMailId();
            $email      = \common\library\email\Email::getEmailObject($userMailId);
            $this->success([
                'user_mail' => $email->email_address,
                'more'      => $affectedRows > 1 ? true : false
            ]);
        }
    }

    /**
     * 取消定时邮件
     * @param $mail_id
     * @return string
     */
    public function actionCancelTiming($mail_id)
    {
        $mail = new \common\library\mail\Mail($mail_id);
        if (!$mail->isTiming()) {
            throw new \RuntimeException(\Yii::t('mail', 'Incorrect message type'));
        }
        return $this->success($mail->save());
    }

    /**
     * 取消定时群发单显邮件
     * @param $mail_id
     * @return string
     */
    public function actionCancelTimingExpose($mail_id)
    {
        $mail = new \common\library\mail\Mail($mail_id);
        if (!$mail->isExpose() && !$mail->time_flag == \Mail::TIME_FLAG_TRUE ) {
            throw new \RuntimeException(\Yii::t('mail', 'Incorrect message type'));
        }
        return $this->success($mail->save());
    }

    /**
     * 保存上传的外部文件（超大附件）
     *
     * @param $file_id
     */
    public function actionCreateLargeAttach($file_id)
    {
        $user = User::getLoginUser();

        $attach = new ExternalFile($user->getClientId(), $file_id, $user->getUserId());
        $attach->loadByRefer(ExternalFileConstants::EXTERNAL_FILE_MAIL_LARGE_ATTACH);

        if (!$attach->isExistOssFile()) {
            throw new \RuntimeException(Yii::t('file', 'File does not exist'));
        }

        if ($attach->isNew()) {
            $attach->refer_type = ExternalFileConstants::EXTERNAL_FILE_MAIL_LARGE_ATTACH;
            $attach->expire_time = date('Y-m-d H:i:s', strtotime('+30 day'));
        }

        if (!$attach->save()) {
            throw new \RuntimeException(\Yii::t('common', 'Save failed'));
        }
        $this->success(['file_id' => $attach->file_id]);
    }

    /**
     * 超大附件在邮件中使用
     *
     * @param $mail_id
     * @param array $file_ids
     * @return false|string|void
     */
    public function actionUseLargeAttach($mail_id, array $file_ids)
    {
        $user = User::getLoginUser();
        $file_ids = array_unique($file_ids);

        $data = [];

        foreach ($file_ids as $file_id) {
            $file = new ExternalFile($user->getClientId(), $file_id, $user->getUserId());
            $file->loadByRefer(ExternalFileConstants::EXTERNAL_FILE_MAIL_LARGE_ATTACH);
            if ($file->isNew() || !$file->isExistOssFile()) {
                throw new \RuntimeException(Yii::t('common', 'File does not exist') . " file_id: {$file_id}");
            }

            $fileShare = $file->attachFileShare($mail_id, '+30 day');

            if (!$fileShare) {
                throw new \RuntimeException(Yii::t('common', 'Save failed, please try again later'));
            }

            $fileShare->getFormatter()->mailInfoSetting();
            $data[] = $fileShare->getAttributes();
        }

        return $this->success($data);
    }

    public function actionRemoveLargeAttach($mail_id, array $file_ids)
    {
        $user = User::getLoginUser();
        $file_ids = array_unique($file_ids);

        $removeCount = 0;

        foreach ($file_ids as $file_id) {
            $file = new ExternalFile($user->getClientId(), $file_id, $user->getUserId());
            $file->loadByRefer(ExternalFileConstants::EXTERNAL_FILE_MAIL_LARGE_ATTACH);
            if ($file->isNew() || !$file->isExistOssFile()) {
                throw new \RuntimeException(Yii::t('common', 'Not Found'));
            }

            if ($file->detachFileShareByReferId($mail_id)) {
                $removeCount++;
            }
        }

        return $this->success($removeCount);
    }

    /**
     * @param array $mail_ids
     * @param string $process_time
     * @return string
     * 除已删除邮件外的所有邮件，草稿箱中的草稿不是邮件不需要支持
     */
    public function actionSetTodo(array $mail_ids, $process_time = '')
    {
        $user = User::getLoginUser();

        $mailBatchOperator = new \common\library\mail\MailListBatchOperate($user->getUserId());
        $mailBatchOperator->setParams(['mail_ids' => $mail_ids]);
        $res = $mailBatchOperator->setTodo($process_time);
        if($res === false)
        {
            $this->fail(-1, \Yii::t('common', 'Save failed, please try again later'));
        }
        return $this->success('');
    }

    /**
     * @param array $mail_id
     * @param string $content
     */
    public function actionSetRemark(string $mail_id, string $content='')
    {
        if (mb_strlen($content) > 200) {
            return $this->fail(-1, '备注长度超过 200');
        }

        $mail = new \common\library\mail\Mail($mail_id);
        $user = User::getLoginUser();
        $client_id = $user->getClientId();
        if (!$mail->getAccessService()->isOwner()) {
            return $this->fail(-1, '非本人, 无权限操作邮件备注');
        }

        $user_id = $user->getUserId();
        $r = MailRemark::model()->find(
            'mail_id = :mail_id and user_id = :user_id',
            [
                ':mail_id' => $mail_id,
                ':user_id' => $user_id,
            ]
        );

        if ($r !== null) {
            $r->remark = $content;
            $res = $r->save();
            if (!$res) {
                return $this->fail(-1, '设置邮件备注失败, 请重试');
            }
        } else {
            $r_new = new MailRemark();
            $r_new->client_id = $client_id;
            $r_new->mail_id = $mail_id;
            $r_new->user_id = $user_id;
            $r_new->remark = $content;
            $r_new->create_time = date("Y-m-d H:i:s");
            $res = $r_new->save();
            if (!$res) {
                return $this->fail(-1, '设置邮件备注失败, 请重试');
            }
        }

        return $this->success('');
    }

    public function actionRemoveTodo(array $mail_ids){
        $user = User::getLoginUser();

        $mailBatchOperator = new \common\library\mail\MailListBatchOperate($user->getUserId());
        $mailBatchOperator->setParams(['mail_ids' => $mail_ids]);
        $res = $mailBatchOperator->removeTodo();
        if($res === false)
        {
            return $this->fail(-1, \Yii::t('common', 'Save failed, please try again later'));
        }
        return $this->success('');
    }

    public function actionSetTodoCompleted(array $mail_ids){
        $user = User::getLoginUser();
        $mailBatchOperator = new \common\library\mail\MailListBatchOperate($user->getUserId());
        $mailBatchOperator->setParams(['mail_ids' => $mail_ids]);
        $res = $mailBatchOperator->setTodoCompleted();
        if($res === false)
        {
            return $this->fail(-1, \Yii::t('common', 'Save failed, please try again later'));
        }
        return $this->success('');
    }

    public function actionCancelTodoRemind($remind_id){
        $user = User::getLoginUser();

        $key = MailTodo::REMIND_REDIS_PREFIX . $remind_id;
        $mail_ids = \Yii::app()->cache->get($key);
        if(empty($mail_ids))
        {
            return $this->success('');
        }

        $mailBatchOperator = new \common\library\mail\MailListBatchOperate($user->getUserId());
        $mailBatchOperator->setParams(['mail_ids' => $mail_ids]);
        $res = $mailBatchOperator->cancelTodoRemind();
        if($res === false)
        {
            return $this->fail(-1, \Yii::t('common', 'Save failed, please try again later'));
        }
        \Yii::app()->cache->delete($key);
        return $this->success('');
    }

    public function actionAiDiscoverySend()
    {

    }

    public function actionResetNewMailCount()
    {
        $userId = \User::getLoginUser()->getUserId();
        $key = 'new_mail_count_'.$userId;
        \Yii::app()->redis->delete($key);
        return $this->success('');
    }

    public function getValidatorRule()
    {
        return [
            'require_mail_ids' => [
                'mail_ids' => [
                    'type' => 'array',
                    'elem' => ['rules' => ['numerical' => ['integerOnly' => true, 'allowEmpty' => false]]]
                ],
            ],
            'require_mail_id' => [
                'mail_id' => ['rules' => ['numerical' => ['integerOnly' => true, 'allowEmpty' => false]]],
            ],
            'require_content'  => [
                'content' => [
                    'rules' => [
                        'length' => [
                            'allowEmpty' => false,
                            'message'    => '回复内容不能为空'
                        ]
                    ]
                ],
            ],
            'base' => [
                'mail_id' => [
                    'rules' => [
                        'numerical' => [
                            'integerOnly' => true,
                            'allowEmpty' => false,
                            'message' => 'mail_id 参数错误'
                        ]
                    ]
                ],
            ]

        ];
    }

    public function actionRemoveLastTranslateContent($mail_id)
    {
        $user = User::getLoginUser();
        $userId = $user->getUserId();
        $mail = new \common\library\mail\Mail($mail_id);
        $translate = new \common\library\mail\MailTranslate($mail);
        $translate->removeLastTranslateContent($userId);
        return $this->success('');
    }

    //停止发送群发单显
    public function actionStopExposeMailTask(array $mail_ids)
    {

	    $user = User::getLoginUser();

	    $userId = $user->getUserId();

//	    次数覆盖
	    Helper::recoverExposeQuota($userId, $mail_ids);

	    return $this->success('');
    }

    //保存已读回执信息
    public function actionSaveReadReceipt($mail_id, $read_receipt_flag)
    {

        $user = User::getLoginUser();
        $userId = $user->getUserId();

        $mail = new \common\library\mail\Mail($mail_id);
        // 保存回执信息并记录版本号
        $receiptService = new \common\library\mail\service\ReceiptService($mail,$userId);
        $receiptService->readReceipt($read_receipt_flag);

        return $this->success([]);
    }

    // 根据email删除邮箱联想的结果
    public function actionDeleteMatchEmailSuggestion($email)
    {
        $user = User::getLoginUser();
        $userId = $user->getUserId();
        $clientId = $user->getClientId();

	    list($name, $emailAddress) = EmailUtil::getPreFixAndEmail($email);
        $id = $userId . '-' . base64_encode($emailAddress);

        \common\library\server\es_search\SearchQueueService::pushMailQueue($userId, $clientId, [$email => ''], Constants::SEARCH_INDEX_TYPE_DELETE);
        // 已迁移至es7
//        $model = \common\models\search\MailRecord::model();
//        $model->setRouting($clientId);
//        $model->deleteById($id);
        return $this->success([]);
    }


	/**
	 * 删除超大附件
	 *
	 * @return false|string|void
	 * @throws Exception
	 */
	public function actionDeleteLargeAttach($file_id) {

		$this->validate([

			'file_id' => 'required|integer|min:1',
		]);

		$user = User::getLoginUser();

		$clientId = $user->getClientId();

		$userId = $user->getUserId();

		$file = new ExternalFile($clientId, $file_id, $userId);

		$file->loadByFileId($file_id);

		return $this->success($file->delete());
	}

    public function actionFeedbackEmail(array $emails = [])
    {
        if (empty($emails)) {
            return $this->success();
        }
        $result = Helper::UpdateEmailBlackList($emails);
        return $this->success();
    }

    /**
     * 共享转移重新分配客户时支持快捷共享客户邮件
     * @param array $company_id
     * @param array $to_user
     */
    public function actionTransferByCompanyId(array $company_id, array $to_user, $history = 1)
    {
        $this->validate([
            'company_id' => 'required|not_empty|array',
            'to_user' => 'required|not_empty|array',
            'company_id.*' => 'required|not_empty|integer',
            'to_user.*' => 'required|not_empty|integer',
        ]);
        $user = User::getLoginUser();
        \DistributeCustomerMailRule::enableCompanyId($user->getClientId(), $user->getUserId(), $company_id, $to_user, false, boolval($history));
        $this->success(['succeed_count' => count($company_id)]);
    }

    public function actionUpdateMailExposeTitle($mail_id, $title)
    {
        $this->validate([
            'mail_id' => 'required|numeric',
            'title' => 'string|max:128',
        ]);

        $user = User::getLoginUser();

        MailExpose::model()->updateAll(['title' => $title], 'mail_id=:mail_id', [':mail_id' => $mail_id]);

        // 更新es
        \common\library\server\es_search\SearchQueueService::pushMailExpose($user->getUserId(), $user->getClientId(), [$mail_id], \Constants::SEARCH_INDEX_TYPE_UPDATE);

        return $this->success();
    }

    /**
     * @param $mail_id 被回复的会议邮件
     * @param $conference_status
     * @return void
     * @throws Exception
     */
    public function actionReplyConferenceMail(
        $mail_id,
        $conference_status,
        $user_mail_id
    )
    {
        $this->validate([
            'mail_id' => 'required|numeric',
            'conference_status' => 'required|numeric|in:1,2,3',
            'user_mail_id' => 'required|numeric'
        ]);

        $user = User::getLoginUser();
        $clientId = $user->getClientId();

        $mail = new \common\library\mail\Mail($mail_id);
        //判断是否是会议邮件
        if (!$mail->getConferenceFlag()) {
            throw new RuntimeException('该邮件不是会议邮件');
        }

        $mail->getAccessService()->checkReplyAccess();
        $replyMail = $mail->replyConference($conference_status, $user_mail_id);
        $replyMail->send();

        $data = [
            'mail_id' => $replyMail->getMailId(),
        ];

        return $this->success($data);

    }

    public function actionAiWrite($type, $language, $language_en, $tone, $extra_desc, $company_name = '', $product_name = '', $must_contains = '', $mail_id = 0)
    {

        $this->validate([
            'type' => 'required|numeric|in:1,2,3,4',
            'company_name' => 'string|max:100',
            'language' => 'required|string|max:20',
            'language_en' => 'required|string|max:50',
            'product_name' => 'string|max:100',
            'must_contains' => 'string|max:100',
            'extra_desc' => 'required|string|max:500',
            'tone' => 'required|string',
            'mail_id' => 'numeric',
        ]);

        $user = User::getLoginUser();
        $userId = $user->getUserId();
        $clientId = $user->getClientId();

        Helper::checkAiLimit($userId);

        //获取prompt
        $aiWritePrompt = new \common\library\prompt\AiMailWritingPrompt($clientId,$userId);
        $aiWritePrompt->setLanguage($language);
        $aiWritePrompt->setTone($tone);
        $aiWritePrompt->setExtraDesc($extra_desc);
        $aiWritePrompt->setCompanyName($company_name);
        $aiWritePrompt->setProductName($product_name);
        $aiWritePrompt->setMustContains($must_contains);
        $aiWritePrompt->setType($type);
        $aiWritePrompt->setLanguageEn($language_en);
        $aiWritePrompt->setReferId($mail_id);
        $recordId = $aiWritePrompt->savePromptRecord();

        $this->success([
            'record_id' => $recordId
        ]);
    }

    public function actionAiPolish($mail_text, $language, $language_en, $tone, $extra_desc)
    {
        $this->validate([
            'mail_text' => 'required|string|max:1000',
            'language' => 'required|string|max:20',
            'language_en' => 'required|string|max:50',
            'tone' => 'required|string|max:20',
            'extra_desc' => 'string|max:500',
        ]);

        $user = User::getLoginUser();
        $userId = $user->getUserId();
        $clientId = $user->getClientId();

        Helper::checkAiLimit($userId);

        $aiPolishPrompt = new \common\library\prompt\AiMailPolishPrompt($clientId,$userId);
        $aiPolishPrompt->setLanguage($language);
        $aiPolishPrompt->setTone($tone);
        $aiPolishPrompt->setExtraDesc($extra_desc);
        $aiPolishPrompt->setMailText($mail_text);
        $aiPolishPrompt->setLanguageEn($language_en);
        $recordId = $aiPolishPrompt->savePromptRecord();

        $this->success([
            'record_id' => $recordId
        ]);
    }

    // 回信摘要接口
    public function actionAiMailSubjects(array $mail_subject, int $type)
    {
        $this->validate([
            'mail_subject' => 'required|array',
            'mail_subject.*' => 'string|max:1000',
            'type' => 'required|integer|in:31,32'
        ]);

        $user = User::getLoginUser();
        $userId = $user->getUserId();
        $clientId = $user->getClientId();
        $sceneType = $type;

        $aiAgent = \common\library\ai_agent\AiAgentFactory::createAgent($sceneType, $clientId, $userId);
        $aiAgent->requestParams = [
            'subject' => $mail_subject
        ];

        $aiAgent->setQuestion('');
        $processRecord = $aiAgent->saveProcessRecord("");

        $context = [
            'record_id' => $processRecord->record_id,
            'question_history_id' => \PgActiveRecord::produceAutoIncrementId(),
            'answer_history_id' => \PgActiveRecord::produceAutoIncrementId(),
        ];
        $aiAgent->setContext($context);

        $subjectContent = "";
        $params['subject'] = $mail_subject;
        $response = $aiAgent->process($params);
        $subjectContent = $response->answer??'';
        if (!empty($subjectContent)) {
            $subjectContent = json_decode($subjectContent, true);
            //判断格式对不对
            if (!is_array($subjectContent) && isset($subjectContent['subject'])) {
                throw new RuntimeException('生成失败，请重试');
            }
        }

        $this->success($subjectContent);
    }

    public function actionAiExposeMailSubject(array $mail_subject)
    {
        $this->validate([
            'mail_subject' => 'required|array',
            'mail_subject.*' => 'string|max:1000'
        ]);

        $user = User::getLoginUser();
        $userId = $user->getUserId();
        $clientId = $user->getClientId();

        Helper::checkAiLimit($userId);

        $aiExposeMailSubjectPrompt = new \common\library\prompt\AiExposeMailSubjectPrompt($clientId,$userId);
        $aiExposeMailSubjectPrompt->setMailSubject($mail_subject);
        $recordId = $aiExposeMailSubjectPrompt->savePromptRecord();

        $this->success([
            'record_id' => $recordId
        ]);

    }

    public function actionAiMailAbstractAndAdvice($mail_id)
    {
        $this->validate([
            'mail_id' => 'required|integer',
        ]);

        $user = User::getLoginUser();
        $userId = $user->getUserId();
        $clientId = $user->getClientId();

        $replyMailContent = Helper::getOriginPlainText($mail_id,$userId);

        //长度超过1000就抛异常
        if (mb_strlen($replyMailContent) > 1000) {
            throw new RuntimeException('邮件内容长度超过1000');
        }

        Helper::checkAiLimit($userId);


        $aiMailReplySuggest = new \common\library\prompt\AiMailReplySuggestPrompt($clientId,$userId);
        $aiMailReplySuggest->setMailId($mail_id);
        $aiMailReplySuggest->setReferId($mail_id);
        $recordId = $aiMailReplySuggest->savePromptRecord();

        $this->success([
            'record_id' => $recordId
        ]);

    }

    public function actionAiReplyMail($mail_id, $extra_desc, $language, $language_en, $tone)
    {
        $this->validate([
            'mail_id' => 'required|integer',
            'extra_desc' => 'string|max:500',
            'language' => 'required|string|max:20',
            'language_en' => 'required|string|max:50',
            'tone' => 'required|string|max:20',
        ]);

        $user = User::getLoginUser();
        $userId = $user->getUserId();
        $clientId = $user->getClientId();

        $replyMailContent = Helper::getOriginPlainText($mail_id,$userId);

        //长度超过1000就抛异常
        if (mb_strlen($replyMailContent) > 1000) {
            throw new RuntimeException('邮件内容长度超过1000');
        }

        Helper::checkAiLimit($userId);

        $aiMailReply = new \common\library\prompt\AiMailReplyPrompt($clientId,$userId);
        $aiMailReply->setMailId($mail_id);
        $aiMailReply->setExtraDesc($extra_desc);
        $aiMailReply->setLanguage($language);
        $aiMailReply->setReferId($mail_id);
        $aiMailReply->setTone($tone);
        $aiMailReply->setLanguageEn($language_en);
        $recordId = $aiMailReply->savePromptRecord();

        $this->success([
            'record_id' => $recordId
        ]);

    }
}
