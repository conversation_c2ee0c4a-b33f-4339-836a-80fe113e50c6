<?php

use common\library\ai_agent\AiAgentConstants;
use common\library\ai_agent\AiAgentFactory;
use common\library\ai_agent\AiAgentProcessResponse;
use common\library\ai_agent\analysis_record\AnalysisRecordAPI;
use common\library\ai_agent\analysis_record\AnalysisRecordFilter;
use common\library\ai_agent\company_quality_check\AiQualityCheckChatJourneyList;
use common\library\ai_agent\company_quality_check\AiQualityCheckCompanyList;
use common\library\ai_agent\company_quality_check\Helper as QualityCheckHelper;
use common\library\ai_agent\company_quality_check\AiQualityCheckCompanyFormatter;
use common\library\ai_agent\company_quality_check\AiQualityCheckCompanyJourneyList;
use common\library\ai_agent\company_quality_check\AiQualityCheckStickingPointList;
use common\library\ai_agent\exception\AiAgentException;
use common\library\ai_agent\FastArchiveAiAgent;
use common\library\ai_agent\GenerateDataVectorAiAgent;
use common\library\ai_agent\Helper;
use common\library\ai_agent\InsightAiAgent;
use common\library\ai_agent\jobs\AiAsyncJob;
use common\library\ai_agent\knowledge_base\Helper as KnowledgeHelper;
use common\library\ai_agent\proxy\AiAgentProxy;
use common\library\ai_agent\trace\TraceConstant;
use common\library\ai_agent\utils\DslSqlBuilder;
use common\library\ai_service\AiAgentConversationHistory;
use common\library\ai_service\AiAgentConversationHistoryList;
use common\library\ai_service\AiServiceRecordFeedbackList;
use common\library\async_task\AsyncTask;
use common\library\async_task\AsyncTaskConstant;
use common\library\customer_v3\common\BaseList;
use common\library\customer_v3\company\list\CompanyList;
use common\library\customer_v3\company\orm\Company;
use common\library\customer_v3\company\orm\CompanyFilter;
use common\library\customer_v3\customer\CustomerList;
use common\library\privilege_v3\PrivilegeConstants;
use common\library\privilege_v3\PrivilegeService;
use common\library\privilege_v3\UserPrivilegeService;
use common\library\prompt\PromptConstant;
use common\library\setting\item\ItemSettingConstant;
use common\library\sns\customer\CustomerContactService;
use common\library\social_auth\Constant;
use common\library\todo\Feed;
use common\library\todo\TodoConstant;


class AiAgentReadController extends Controller
{
    // AI Agent列表
    public function actionAiAgentList($type = 0, $channel_type = '')
    {
        $user = User::getLoginUser();

        $clientId = $user->getClientId();
        $privilegeService = PrivilegeService::getInstance($clientId);
        $list = [];
        $knowledgeAgent = [];

        // 销售助手请求 agentList 会传递 channel_type 有值附加知识库
        if ($channel_type && $privilegeService->hasFunctional(PrivilegeConstants::FUNCTIONAL_OKKI_AI_KNOWLEDGE)) {
            $knowledgeAgent = json_decode(AiAgentConstants::KNOWLEDGE_BASED_AGENT_JSON, true);
            $knowledgeAgent['name'] = \Yii::t('ai', $knowledgeAgent['name']);
            foreach ($knowledgeAgent['agent_list'] as $index => $agent) {
                $knowledgeAgent['agent_list'][$index]['name'] = \Yii::t('ai', $agent['name']);
                $knowledgeAgent['agent_list'][$index]['desc'] = \Yii::t('ai', $agent['desc']);
            }
        }
        if (!$privilegeService->hasFunctional(PrivilegeConstants::FUNCTIONAL_OKKI_AI)) {
            $knowledgeAgent && $list[] = $knowledgeAgent;
            $this->success($list);
        }

        $systemId = $privilegeService->getMainSystemId();
        $sceneTypeList = Helper::getAgentListBySystemId($systemId);


        if (empty($sceneTypeList)) {
            $this->success([]);
        }

        $aiAgentListPdo = new \common\library\ai_agent\agent\AiAgentList();
        $aiAgentListPdo->setAgentType($type);
        $aiAgentListPdo->setSceneType($sceneTypeList);
        $aiAgentListPdo->setAggregateProxyAgent(true);
        $aiAgentList = $aiAgentListPdo->find();

        $ua = $_SERVER['HTTP_USER_AGENT'];
        $fromDesktop = false;
        if (str_contains($ua, 'OKKI')) {
            $fromDesktop = true;
        }
        if ($fromDesktop) {
            $aiAgentList = array_filter($aiAgentList, function($item) {
                $sceneType = $item['scene_type'];
                return !in_array($sceneType, [AiAgent::AI_AGENT_SCENE_TYPE_MAIL_REPLY, AiAgent::AI_AGENT_SCENE_TYPE_MAIL_SUMMARY]);
            });
        }


        $user = User::getLoginUser();

        foreach ($aiAgentList as $agent) {
            // 校验是否存在权限
            $sceneType = $agent['scene_type'];
            if (!Helper::checkAiAgentPriviledge($user->getClientId(), $user->getUserId(), $sceneType)) {
                continue;
            }

            $itemAgentType = $agent['agent_type'];
            $list[$itemAgentType]['name'] = AiAgentConstants::AI_AGENT_TYPE_MAP[$itemAgentType]['name'] ?? '其他';
            $list[$itemAgentType]['size'] = AiAgentConstants::AI_AGENT_SIZE_MAP[$itemAgentType] ?? 'small';

            $p90CostTime = AiAgentConstants::AI_SCENE_TYPE_P90_MAP[$sceneType] ?? 0;

            $externalFieldData = Helper::getExternalFieldDataBySceneType($sceneType);
            $list[$itemAgentType]['agent_list'][] = [
                'name' => $agent['agent_name'],
                'scene_type' => $agent['scene_type'],
                'agent_id' => $agent['agent_id'],
                'desc' => $agent['desc'],
                'icon' => $agent['icon'],
                'external_field_data' => $externalFieldData,
                'cost_time' => $p90CostTime,
                'group' => Helper::getGroupConfig((int) $sceneType), // 折叠分组
            ];

            if (in_array($sceneType, array_keys(AiAgentConstants::AI_SCENE_TYPE_EXTERNAL_SCENE_NAME_INFO_MAP))) {
                $agentExternalList = AiAgentConstants::AI_SCENE_TYPE_EXTERNAL_SCENE_NAME_INFO_MAP[$sceneType];

                foreach ($agentExternalList as $value) {
                    // 版本控制
                    if (!in_array($systemId, ($value['allowVersion'] ?? []))) continue;

                    // 子列表权限校验
                    $privileges = $value['privileges'] ?? [];
                    $privilegeService = PrivilegeService::getInstance($user->getClientId(), $user->getUserId());
                    if (!$privilegeService->hasPrivilege($privileges, true)) {
                        continue;
                    }

                    $list[$itemAgentType]['agent_list'][] = [
                        'name' => $value['name'],
                        'scene_type' => $agent['scene_type'],
                        'agent_id' => $agent['agent_id'],
                        'desc' => $value['desc'] ?? '',
                        'icon' => $value['icon'] ?? '',
                        'external_field_data' => $value['external_field_data'] ?? [],
                        'cost_time' => $p90CostTime,
                        'order' => PHP_INT_MAX
                    ];
                }
            }
        }

        // 调整agent展示顺序
        foreach ($list as $itemAgentType => $agentList) {
            // 根据 AiAgentConstants::AI_AGENTS_ORDER 中定义的顺序
            if (!empty(AiAgentConstants::AI_AGENT_SCENE_ORDER[$itemAgentType])) {

                if (empty($agentList['agent_list']))
                {
                    $list[$itemAgentType]['agent_list'] = [];
                    continue;
                }

                usort($agentList['agent_list'], function ($a, $b) use ($itemAgentType) {
                    $aIndex = $a['order'] ?? array_search((int) $a['scene_type'], AiAgentConstants::AI_AGENT_SCENE_ORDER[$itemAgentType]);
                    $bIndex = $b['order'] ?? array_search((int) $b['scene_type'], AiAgentConstants::AI_AGENT_SCENE_ORDER[$itemAgentType]);
                    return $aIndex - $bIndex;
                });
                $list[$itemAgentType] = $agentList;
            }
        }
        $list = array_values($list);
        $knowledgeAgent && $list[] = $knowledgeAgent;
        foreach ($list as $listKey => $agentList){
            $list[$listKey]['name'] = Yii::t('ai', $agentList['name']);
            foreach ($agentList['agent_list'] as $agentKey => $agent){
                $name = Yii::t('ai', $agent['name']);
                $agent['name'] = $name;
                $list[$listKey]['agent_list'][$agentKey] = $agent;
                $list[$listKey]['agent_list'][$agentKey]['desc'] = \Yii::t('ai', $agent['desc']);
                if(!empty($agent['group'])){
                    $list[$listKey]['agent_list'][$agentKey]['group']['name'] = \Yii::t('ai', $agent['group']['name']);
                }
            }
        }

        return $this->success($list);
    }

    /** web端已废弃
     * @param int $agent_id
     * @param array $conversation_params
     * @param int $conversation_id
     * @return false|string
     */
    public function actionOpenConversation(int $agent_id, array $conversation_params = [], int $conversation_id = 0)
    {
        $user = User::getLoginUser();
        $data = $this->openConversation($user, $agent_id, $conversation_params, $conversation_id);

        return $this->success($data);
    }

    /** web端已废弃
     * @param User $user
     * @param $agentId
     * @param array $conversationParams
     * @param int $conversationId
     * @return false|string
     * @throws Exception
     */

    protected function openConversation(User $user, $agentId, array $conversationParams = [], int $conversationId = 0)
    {
        $clientId = $user->getClientId();
        $userId = $user->getUserId();
        $aiAgent = AiAgent::model()->findByPk($agentId);
        if (empty($aiAgent)) {
            throw new RuntimeException("不支持的AI Agent");
        }

        $agentParams = $conversationParams['params'] ?? [];
        $agentContext = $conversationParams['context'] ?? [];

        $sceneType = $aiAgent->scene_type;
        switch ($sceneType) {
            case PromptConstant::SCENE_TYPE_AI_SERVICE_SCENE_AI_MAIL_WRITE:
                $presetMessageList = [
                    [
                        'role' => AiAgentConstants::AI_AGENT_ROLE_TYPE_USER,
                        'message_type' => AiAgentConstants::AI_AGENT_MESSAGE_TYPE_TEXT,
                        'context' => [
                            'content' => "使用AI帮我写信"
                        ]
                    ],
                    [
                        'role' => AiAgentConstants::AI_AGENT_ROLE_TYPE_SYSTEM,
                        'message_type' => AiAgentConstants::AI_AGENT_MESSAGE_TYPE_SPECIAL_MAIL_WRITE_FORM,
                        'continue_question' => false,
                        'context' => $agentContext,
                    ]
                ];
                break;
            case PromptConstant::SCENE_TYPE_AI_SERVICE_SCENE_AI_MAIL_POLISH:
                $presetMessageList = [
                    [
                        'role' => AiAgentConstants::AI_AGENT_ROLE_TYPE_USER,
                        'message_type' => AiAgentConstants::AI_AGENT_MESSAGE_TYPE_TEXT,
                        'context' => [
                            'content' => \Yii::t('ai','使用AI帮我润色')
                        ]
                    ],
                    [
                        'role' => AiAgentConstants::AI_AGENT_ROLE_TYPE_SYSTEM,
                        'message_type' => AiAgentConstants::AI_AGENT_MESSAGE_TYPE_SPECIAL_MAIL_POLISH_FORM,
                        'params' => $agentParams,
                        'context' => $agentContext,
                        'continue_question' => false
                    ]
                ];
                break;
            case PromptConstant::SCENE_TYPE_AI_SERVICE_SCENE_AI_DATA_DISTRIBUTION:
                // 报表数据超过阈值时，返回提示文案：当前数据量过大，请筛选数据后再试
                $reportDataLength = $agentParams['report_data_length'] ?? 0;
                $userEmail = $user->getEmail();
                if ($reportDataLength > AiAgentConstants::REPORT_DATE_LENGTH_THRESHOLD_VALUE && (!in_array($userEmail, ['<EMAIL>', '<EMAIL>', '<EMAIL>']))) {
                    $content = "当前数据量过大，请筛选数据后再试。";
                    $continueQuestion = false;
                } else {
                    $content = "基于以上筛选数据，我能为您提供什么帮助？";
                    $continueQuestion = true;
                }

                $presetMessageList = [
                    [
                        'role' => AiAgentConstants::AI_AGENT_ROLE_TYPE_SYSTEM,
                        'message_type' => AiAgentConstants::AI_AGENT_MESSAGE_TYPE_SPECIAL_REPORT_PARAMS,
                        'params' => $agentParams,
                        'context' => $agentContext,
                    ],
                    [
                        'role' => AiAgentConstants::AI_AGENT_ROLE_TYPE_SYSTEM,
                        'message_type' => AiAgentConstants::AI_AGENT_MESSAGE_TYPE_DATA_LIST_CARD,
                        'context' => [
                            "title" => [
                                "text" => $content,
                            ],
                            'list' => [
                                [
                                    "icon" => "",
                                    "name" => "根据该报表可以得出哪些结论？",
                                    "event_name" => "okki_ai_send_chat_content",
                                    "event_params" => [
                                        'content' =>  "根据该报表可以得出哪些结论？",
                                        'params' => $agentParams,
                                    ]
                                ],
                                [
                                    "icon" => "",
                                    "name" => "根据该报表有哪些行动建议？",
                                    "event_name" => "okki_ai_send_chat_content",
                                    "event_params" => [
                                        'content' =>  "根据该报表有哪些行动建议？",
                                        'params' => $agentParams,
                                    ]
                                ]
                            ],

                        ],
                        'ignore_persist' => true,
                        'continue_question' => $continueQuestion
                    ],
                ];
                break;
            case PromptConstant::SCENE_TYPE_AI_SERVICE_SCENE_AI_ARCHIVE:
                $presetMessageList = [
                    [
                        'role' => AiAgentConstants::AI_AGENT_ROLE_TYPE_USER,
                        'message_type' => AiAgentConstants::AI_AGENT_MESSAGE_TYPE_TEXT,
                        'context' => [
                            'content' => AiAgentConstants::AI_ARCHIVE_PRESET_MESSAGE,
                        ],
                        'continue_question' => true
                    ],
                ];
                break;
            case PromptConstant::SCENE_TYPE_AI_SERVICE_SCENE_AI_OPPORTUNITY_FOLLOW_UP:
                $presetMessageList = [
                    [
                        'role' => AiAgentConstants::AI_AGENT_ROLE_TYPE_USER,
                        'message_type' => AiAgentConstants::AI_AGENT_MESSAGE_TYPE_TEXT,
                        'context' => [
                            'content' => \Yii::t('ai',\common\library\ai_agent\AiAgentConstants::AI_OPPORTUNITY_FOLLOW_UP_PRESET_MESSAGE)
                        ],
                        'continue_question' => true
                    ],
                ];
                break;
            case PromptConstant::SCENE_TYPE_AI_SERVICE_SCENE_AI_CUSTOMER_FOLLOW_UP:
                $presetMessageList = [
                    [
                        'role' => AiAgentConstants::AI_AGENT_ROLE_TYPE_USER,
                        'message_type' => AiAgentConstants::AI_AGENT_MESSAGE_TYPE_TEXT,
                        'context' => [
                            'content' => \Yii::t('ai', '使用AI帮我生成客户跟进')
                        ],
                        'continue_question' => true
                    ],
                ];
                break;
            case PromptConstant::SCENE_TYPE_AI_SERVICE_SCENE_AI_GENERATION_DATA:
                $privilegeService = PrivilegeService::getInstance($clientId);
                $adminUser = $privilegeService->getAdminUserId();
                // 获取缓存预设问题
                $presetQuestion = Helper::getPresetQuestionList();

                foreach ($presetQuestion as $value)
                {
                    $question = $value['desc'] ?? '';
                    $greyClientIds = $value['greyClientIds'] ?? '';
                    $greyClientIdsArr = explode(',', $greyClientIds);

                    if (!empty($greyClientIds) && !in_array($clientId, $greyClientIdsArr)) {
                        continue;
                    }

                    $list[] = [
                        "icon" => "",
                        "name" => $question,
                        "event_name" => "okki_ai_send_chat_content",
                        "event_params" => [
                            'content' => $question
                        ]
                    ];
                }

                if (empty($list))
                {
                    $list = [
                        [
                            "icon" => "",
                            "name" => "这个月的销售订单总金额是多少？",
                            "event_name" => "okki_ai_send_chat_content",
                            "event_params" => [
                                'content' => "这个月的销售订单总金额是多少？"
                            ]
                        ],
                        [
                            "icon" => "",
                            "name" => "过去3个月中，每周创建了多少个客户？",
                            "event_name" => "okki_ai_send_chat_content",
                            "event_params" => [
                                'content' => "过去3个月中，每周创建了多少个客户？"
                            ]
                        ],
                        [
                            "icon" => "",
                            "name" => "过去3个月中，不同员工分别创建了多少客户？",
                            "event_name" => "okki_ai_send_chat_content",
                            "event_params" => [
                                'content' => "过去3个月中，不同员工分别创建了多少客户？"
                            ]
                        ],
                        [
                            "icon" => "",
                            "name" => "这个月创建了多少个商机？",
                            "event_name" => "okki_ai_send_chat_content",
                            "event_params" => [
                                'content' => "这个月创建了多少个商机？"
                            ]
                        ],
                    ];
                }

                $agent = new GenerateDataVectorAiAgent($clientId, $userId);
                $useNewVersion = $agent->promptConfig['outSqlConfig']['openOutSql'] ?? 0;
                if (!empty($useNewVersion)) {
                    $list = array_merge([
                        [
                            "icon" => "",
                            "name" => "成交金额最高的top10客户是谁？",
                            "event_name" => "okki_ai_send_chat_content",
                            "event_params" => [
                                'content' => "成交金额最高的top10客户是谁？"
                            ]
                        ],
                        [
                            "icon" => "",
                            "name" => "本月创建的客户有哪些？",
                            "event_name" => "okki_ai_send_chat_content",
                            "event_params" => [
                                'content' => "本月创建的客户有哪些？"
                            ]
                        ],
                        [
                            "icon" => "",
                            "name" => "本月新建客户数最多的员工是谁？",
                            "event_name" => "okki_ai_send_chat_content",
                            "event_params" => [
                                'content' => "本月新建客户数最多的员工是谁？"
                            ]
                        ],
                        [
                            "icon" => "",
                            "name" => "这周跟进了多少客户？",
                            "event_name" => "okki_ai_send_chat_content",
                            "event_params" => [
                                'content' => "这周跟进了多少客户？"
                            ]
                        ],
                    ], $list);
                }

                $presetMessageList = [
                    [
                        'role' => AiAgentConstants::AI_AGENT_ROLE_TYPE_SYSTEM,
                        'message_type' => AiAgentConstants::AI_AGENT_MESSAGE_TYPE_REPORT_GENERATION_CARD,
                        'context' => [
                            "title" => [
                                "text" => "您可以问客户、订单、商机的问题",
                                "icon" => "LogoOkkiaiStar"
                            ],
                            'list' => $list,
                        ],
                        'continue_question' => true
                    ],
                ];

                if (array_key_exists('company_id', $agentParams)) {
                    $company = new Company($clientId, $agentParams['company_id']);
                    $data = $company->getAttributes();
                    $companyName = $data['name'];

                    if (!empty($companyName) && count($presetMessageList) > 0) {
                        $companyQuestionList = [
                            [
                                "icon" => "",
                                "name" => "今年累计成交金额是多少？",
                                "company_name" => $companyName,
                                "event_name" => "okki_ai_send_chat_content",
                                "event_params" => [
                                    'content' =>  "今年累计成交金额是多少？",
                                    'params' => [
                                        "company_id" => $agentParams['company_id']
                                    ]

                                ]
                            ],
                            [
                                "icon" => "",
                                "name" => "平均订单金额是多少？",
                                "company_name" => $companyName,
                                "event_name" => "okki_ai_send_chat_content",
                                "event_params" => [
                                    'content' =>  "平均订单金额是多少？",
                                    'params' => [
                                        "company_id" => $agentParams['company_id']
                                    ]
                                ]
                            ],
                            [
                                "icon" => "",
                                "name" => "今年赢单金额是多少？",
                                "company_name" => $companyName,
                                "event_name" => "okki_ai_send_chat_content",
                                "event_params" => [
                                    'content' =>  "今年赢单金额是多少？",
                                    'params' => [
                                        "company_id" => $agentParams['company_id']
                                    ]
                                ]
                            ],
                            [
                                "icon" => "",
                                "name" => "平均赢单商机金额是多少？",
                                "company_name" => $companyName,
                                "event_name" => "okki_ai_send_chat_content",
                                "event_params" => [
                                    'content' =>  "平均赢单商机金额是多少？",
                                    'params' => [
                                        "company_id" => $agentParams['company_id']
                                    ]
                                ]
                            ],
                        ];

                        $presetMessageList[0]['context']['list'] = array_merge($companyQuestionList, $presetMessageList[0]['context']['list']);

                    }
                }
                break;

            case PromptConstant::SCENE_TYPE_AI_SERVICE_SCENE_AI_CHAT_REPLY:
                $presetMessageList = [
                    [
                        'role' => AiAgentConstants::AI_AGENT_ROLE_TYPE_USER,
                        'message_type' => AiAgentConstants::AI_AGENT_MESSAGE_TYPE_TEXT,
                        'context' => [
                            'content' => AiAgentConstants::AI_CHAT_REPLY_PRESET_MESSAGE,
                        ],
                        'continue_question' => true
                    ],
                ];
                break;
            case PromptConstant::SCENE_TYPE_AI_SERVICE_SCENE_AI_CHAT_COACH:
                $presetMessageList = [
                    [
                        'role' => AiAgentConstants::AI_AGENT_ROLE_TYPE_USER,
                        'message_type' => AiAgentConstants::AI_AGENT_MESSAGE_TYPE_TEXT,
                        'context' => [
                            'content' => AiAgentConstants::AI_CHAT_COACH_PRESET_MESSAGE,
                        ],
                        'continue_question' => true
                    ],
                ];
                break;
            case AiAgent::AI_AGENT_SCENE_TYPE_CHAT_QUALITY:
                $presetMessageList = [
                    [
                        'role' => AiAgentConstants::AI_AGENT_ROLE_TYPE_USER,
                        'message_type' => AiAgentConstants::AI_AGENT_MESSAGE_TYPE_TEXT,
                        'context' => [
                            'content' => \Yii::t('ai', '使用AI帮我检查业务员和客户的沟通质量')
                        ],
                        'continue_question' => true
                    ],
                ];
                break;
                case AiAgent::AI_AGENT_SCENE_TYPE_MAIL_QUALITY:
                    $presetMessageList = [
                        [
                            'role' => AiAgentConstants::AI_AGENT_ROLE_TYPE_USER,
                            'message_type' => AiAgentConstants::AI_AGENT_MESSAGE_TYPE_TEXT,
                            'context' => [
                                'content' => \Yii::t('ai', '使用AI帮我检查业务员和客户的邮件质量')
                            ],
                            'continue_question' => true
                        ],
                    ];
                    break;
            case AiAgent::AI_AGENT_SCENE_TYPE_MAIL_SUMMARY:
                $presetMessageList = [
                    [
                        'role' => AiAgentConstants::AI_AGENT_ROLE_TYPE_USER,
                        'message_type' => AiAgentConstants::AI_AGENT_MESSAGE_TYPE_TEXT,
                        'context' => [
                            'content' => "使用AI帮我总结"
                        ],
                        'continue_question' => true
                    ],
                ];
                break;
            case AiAgent::AI_AGENT_SCENE_TYPE_MAIL_REPLY:
                $presetMessageList = [
                    [
                        'role' => AiAgentConstants::AI_AGENT_ROLE_TYPE_USER,
                        'message_type' => AiAgentConstants::AI_AGENT_MESSAGE_TYPE_TEXT,
                        'context' => [
                            'content' => AiAgentConstants::mailReplyImmediateSendContent
                        ],
                        'continue_question' => true
                    ],
                ];
                break;
            default:
                $presetMessageList = [];
                break;
        }

        // 初始化会话
        if (empty($conversationId)) {
            $agentConversation = new AiAgentConversation();
            $agentConversation->conversation_id = PgActiveRecord::produceAutoIncrementId(1);
            $agentConversation->client_id = $clientId;
            $agentConversation->user_id = $userId;
            $agentConversation->agent_id = $agentId;
            $agentConversation->insert();
            $conversationId = $agentConversation->conversation_id;
        }

        return [
            'conversation_id' => $conversationId,
            'preset_message_list' => $presetMessageList,
        ];
    }

    public function actionAiAgentTipsWordList(int $agent_id)
    {
        $this->validate([
            'agent_id' => 'required|int|not_empty'
        ]);

        $aiAgent = AiAgent::model()->findByPk($agent_id);
        if (empty($aiAgent)) {
            throw new RuntimeException("不支持的AI Agent");
        }
        $sceneType = $aiAgent->scene_type;
        $agentTips = Helper::getTipsWords($sceneType);
        return $this->success($agentTips);
    }

    public function actionAiAgentFeedbackTagList()
    {
        $list = AiAgentConstants::AI_AGENT_FEEDBACK_TAG_LIST;
        foreach ($list as $key => $value){
            $list[$key] = \Yii::t('ai', $value);
        }
        return $this->success([
            'list' => $list
        ]);
    }

    public function actionDownLoadExcel($record_id,$scene_type, $params = '')
    {
        $user = User::getLoginUser();
        $clientId = $user->getClientId();
        $userId = $user->getUserId();

        $aiAgent = AiAgentFactory::createAgent($scene_type, $clientId, $userId);


        $reflector = new ReflectionClass($aiAgent);
        if ($reflector->hasMethod('__call') || $reflector->hasMethod('generateCsv')) {
            [$fileName, $filePath] = $aiAgent->generateCsv($record_id, json_decode($params, true));
        } else {
            throw new \RuntimeException("该场景暂不支持下载");
        }

        $fileKey = UploadService::getFileKey($fileName, $user);
        $upload = UploadService::uploadRealFile($filePath, $fileName, $fileKey);
        $fileUrl = $upload->getFileUrl();
        $this->success([
            'file_url' => $fileUrl
        ]);
    }

    /**
     * 根据 historyId 获取历史聊天记录
     * 兼容超时无法重试问题，所以记录日志，看出错率分布
     */
    public function actionHistoryInfo(int $history_id)
    {
        $this->validate([
            'history_id' => 'required|int'
        ]);

        $user = User::getLoginUser();
        $clientId = $user->getClientId();
        $userId = $user->getUserId();

        // 前端不愿意传 agent_id，这里只能先查一遍获取，然后setModel注入
        $aiAgentConversationHistoryInfo = \AiAgentConversationHistory::model()->findByPk($history_id);

        // 假如查询不出来，返回空，前端要求不要返回错误！
        if (empty($aiAgentConversationHistoryInfo)) {
            $this->success([]);
        }

        \LogUtil::info("OKKiAI_获取历史聊天记录", [
            'clientId' => $clientId,
            'userId' => $userId,
            'agentId' => $aiAgentConversationHistoryInfo->agent_id,
            'historyId' => $history_id
        ]);

        $historyInfoPdo = new AiAgentConversationHistory($clientId, $aiAgentConversationHistoryInfo->agent_id);
        $historyInfoPdo->setModel($aiAgentConversationHistoryInfo);
        $historyInfoPdo->getFormatter()->showFeedbackInfo(true);

        $this->success($historyInfoPdo->getAttributes());
    }

    /**
     * agent_id 和 conversation_id 是为了兼容旧版桌面端存在的
     * @param int $agent_id
     * @param int $conversation_id
     * @param int $scene_type
     * @param array $params
     * @param int $history_id
     * @param int $page
     * @param int $page_size
     * @return void
     */
    public function actionConversationHistory
    (
        int $agent_id = 0,
        int $conversation_id = 0,
        int $scene_type = 0,
        array $params = [],
        int $history_id = 0,
        int $page = 1,
        int $page_size = 50,
        bool $from_assistant = false
    )
    {
        $this->validate([
            'page' => 'int|min:1|max:100',
            'history_id' => 'int', // 只查询当前会话前的历史，不用于分页（可能会漏数据）
            'page_size' => 'int|min:1|max:50',
        ]);

        $user = User::getLoginUser();
        $clientId = $user->getClientId();
        $userId = $user->getUserId();

        $agentId = $agent_id;
        $conversationId = $conversation_id;

        if (empty($scene_type) && empty($agent_id) && !$from_assistant)
        {
            throw new \RuntimeException("缺少必要参数");
        }

        if (empty($agentId)) {
            $aiAgentModel = AiAgent::findBySceneType($scene_type);

        } else {
            $aiAgentModel = AiAgent::model()->findByPk($agentId);
        }

        $agentId = $aiAgentModel->agent_id;
        $scene_type = $aiAgentModel->scene_type;

        if (empty($conversation_id))
        {
            $latestConversationInfo = Helper::getLatestConversationInfoBySceneType($clientId,$userId,$scene_type,$from_assistant);

            if (empty($latestConversationInfo)) {
                $this->success([]);
            }

            $conversationId = $latestConversationInfo['conversation_id'];
        }


        $historyList = new AiAgentConversationHistoryList($clientId, $userId, $agentId);
        $historyList->setLimit($page_size);
        $historyList->setOffset(($page - 1) * $page_size);
        $historyList->getFormatter()->showFeedbackInfo(true);
        $historyList->getFormatter()->withAiSwarmInfo($scene_type == AiAgent::AI_AGENT_SCENE_TYPE_DATA_ASSISTANT_PROXY);
        $history_id && $historyList->setLessThanHistoryId($history_id, true);
        $historyList->setOrderBy(['conversation_id', 'history_id']);
        $historyList->setOrder('desc');
        $historyList->setLessThanConversationId($conversationId); // 必须小于当前会话

        if (in_array($scene_type, PromptConstant::SCENE_TYPE_HISTORY_OWNERBY_COMMUNICATION) && empty($params))
        {
            $extInfoParams = ['user_sns_id','sns_id'];
            $historyList->setLessThanConversationId($conversationId,false);

            if (in_array($scene_type, PromptConstant::SCENE_TYPE_HISTORY_OWNERBY_MAIL_ID)) {
                $extInfoParams = ['mail_id'];
            }
            // 沟通场景 按 [UserID+agentID+沟通渠道账号+会话联系人] 进行区分
            $lastHistory = Helper::getLastHistoryOfConversation($clientId, $userId, $agent_id, $conversationId, $extInfoParams);
            if (!$lastHistory) {
                return $this->success([]);
            }
            $params = json_decode($lastHistory['ext_info'], true) ?? [];
        }


        if (in_array($scene_type, PromptConstant::SCENE_TYPE_HISTORY_OWNERBY_COMMUNICATION))
        {
            //  tm渠道没有sns_id user_sns_id传入，要进行转换。否则查出的聊天记录没有按照渠道和联系人划分
            if(isset($params['channel_type']) && (int)($params['channel_type']) === Constant::CHANNEL_TYPE_TM){
                $params['sns_id'] = ArrayUtil::fallbackByKeys($params, 'sns_id', 'buyer_account_id') ?? 0;
                $params['user_sns_id'] = ArrayUtil::fallbackByKeys($params, 'user_sns_id', 'store_id', 'seller_account_id', 'channel_open_id') ?? 0;
            }
            $historyList->setUserSnsId($params['user_sns_id'] ?? 0);
            $historyList->setSnsId($params['sns_id'] ?? 0);
        }

        if (in_array($scene_type, PromptConstant::SCENE_TYPE_HISTORY_OWNERBY_MAIL_ID)) {
            $historyList->setMailId($params['mail_id'] ?? 0);
        }

        if(in_array($scene_type , [AiAgent::AI_AGENT_SCENE_TYPE_FAST_ARCHIVE, AiAgent::AI_AGENT_SCENE_TYPE_CUSTOMER_FOLLOW_UP, AiAgent::AI_AGENT_SCENE_TYPE_OPPORTUNITY_FOLLOW_UP]) && !empty($params['mail_id'])) {
            $historyList->setMailId($params['mail_id']);
        }

        // 按邮件会话区分历史记录
        if(in_array($scene_type, PromptConstant::SCENE_TYPE_HISTORY_OWNERBY_MAIL_CONVERSATION_ID)){
            $historyList->setMailConversationId($params['conversation_id'] ?? 0);
        }


        $list = $historyList->find();
        $list = array_map(function ($item) use ($scene_type) {
            // 销售助手web端和app端的消息小卡片互相转换（只针对辅助回复、沟通建议、沟通润色、客户跟进、商机跟进）
            return Helper::convertCardTypeForAiAgentConversationHistory($item, $scene_type, false);
        }, $list);

        if ($scene_type == AiAgent::AI_AGENT_SCENE_TYPE_GENERATION_DATA) {
            $list = Helper::reconstructionHistoryList($list, $scene_type);
        }

        // 反转数组 最新的记录应该在最下面
        $list = array_reverse($list);
        $this->success($list);
    }

    public function actionAiAgentChatCompletions (
        int    $agent_id = 0,
        int    $scene_type = 0,
        int    $conversation_id = 0,
        string $question = '',
        /**
         * NOTE: params字段中 格式为
         * params[params] 为业务使用参数
         * params[context] 为前端需要的回显参数
         */
        array  $params = [],
        int $retry_record_id = 0
    ) {
        $user = User::getLoginUser();
        ignore_user_abort(true);

        if (empty($agent_id) && empty($scene_type)) {
            if (empty($conversation_id)) {
                throw new \RuntimeException("缺少必要参数");
            }
            $conversation = AiAgentConversation::model()->findByPk($conversation_id);
            if (empty($conversation)) {
                throw new \RuntimeException("无效的会话");
            }
            $agent_id = $conversation->agent_id;

        }

        if (!empty($agent_id))
        {
            $aiAgentModel = AiAgent::model()->findByPk($agent_id);
            $scene_type = $aiAgentModel->scene_type;
        }

        $this->chatCompletions($user, $conversation_id, $scene_type, $question, $params, false, $retry_record_id);
    }


    protected function chatCompletions(User $user, $conversationId, int $scene_type , string $question = '', array $params = [] , bool $fromAssistant = false, $retryRecordId = 0)
    {
        // 统计时间埋点
        $controllerBeginTime = round(microtime(true) * 1000, 2);
        $startTime = microtime(true);
        $userId = $user->getUserId();
        $clientId = $user->getClientId();


        if (!empty($retryRecordId))
        {
            $record = new \common\library\ai_agent\record\AiServiceProcessRecord($retryRecordId);
            $params = is_array($record->params) ? $record->params : json_decode($record->params, true);
            $params = $record->scene_type == AiAgent::AI_AGENT_SCENE_TYPE_DATA_ANALYSIS ? $params : $params['params'] ?? [];
            $scene_type = $record->scene_type;
        }


        // 兼容逻辑，判断是否是历史版本， 历史版本会直接传conversation_id过来 导致没办法保存预置信息
        $aiAgent = AiAgentFactory::createAgent($scene_type, $clientId, $userId,AiAgentConstants::BUSINESS_TYPE_CRM, $question,$params);
        $aiAgent->timeLog = ['start' => microtime(true)];
        $scene_type = $aiAgent->getAgentSceneType();

        if ($conversationParams = $aiAgent->loadParams($conversationId)) {
            $params['params'] = array_merge($conversationParams['params'] ?? [], $params['params'] ?? []);
            $params['context'] = array_merge($conversationParams['context'] ?? [], $params['context'] ?? []);
        }

        $aiAgent->requestParams = $params;
        $oldVersionFirstQuestionFlag = false;

        if (! isset($params['params']))
        {
            $oldVersionFirstQuestionFlag = empty($question) && !empty($params) && !empty($conversationId);
            $params['params'] = $params;
        }

        if (empty($scene_type) && empty($conversation_id)) {
            throw new \RuntimeException("参数错误");
        }

        if (empty($conversationId) || $oldVersionFirstQuestionFlag) {
            $aiAgentModel = AiAgent::model()::findBySceneType($scene_type);
            $agentId = $aiAgentModel->agent_id;

            $saveHistoryFlag = ($oldVersionFirstQuestionFlag && $scene_type != AiAgent::AI_AGENT_SCENE_TYPE_GENERATION_DATA) || empty($question);

            if ($scene_type == AiAgent::AI_AGENT_SCENE_TYPE_DATA_ANALYSIS && empty($conversationId)) {
                $saveHistoryFlag = true;
            }

            if (empty($conversationId))
            {
                $agentConversation = new AiAgentConversation();
                $agentConversation->conversation_id = PgActiveRecord::produceAutoIncrementId(1);
                $agentConversation->client_id = $clientId;
                $agentConversation->user_id = $userId;
                $agentConversation->agent_id = $aiAgentModel->agent_id;
                $agentConversation->insert();
                $conversationId = $agentConversation->conversation_id;
            }

            $aiAgent->setConversationId($conversationId);

            // 旧版本报表生成不保存历史记录
            if ($saveHistoryFlag) {
                $aiAgent->savePressMessageHistory($params);
            }

            //leads ai 免费版扣除点数
            $privilegeService = PrivilegeService::getInstance($clientId);
            $systemId = $privilegeService->getMainSystemId();
            if (in_array($systemId, [
                PrivilegeConstants::OKKI_PERSONAL_LEADS_AI_BASIC_ID,
                PrivilegeConstants::OKKI_PERSONAL_LEADS_AI_SUBSCRIBE_ID,
                PrivilegeConstants::OKKI_PERSONAL_LEADS_AI_GLOBAL_BASIC_ID,
            ])) {
                $type = $scene_type == 1 ? 8 : 9;
                $api = new \common\library\okki_personal\OkkiIOApi($clientId, $userId);
                $api->consumerBalance($type, 1);
            }
        } else {
            //写信多次“重新编辑”,需要添加conversation_history
            if(in_array($scene_type, [AiAgent::AI_AGENT_SCENE_TYPE_MAIL_WRITE,AiAgent::AI_AGENT_SCENE_TYPE_MAIL_POLISH]) && empty($question)) {
                $aiAgent->setConversationId($conversationId);
                $aiAgent->savePressMessageHistory($params);
            }

            // 追问场景
            $conversation = AiAgentConversation::model()->findByPk($conversationId);

            if (empty($conversation)) {
                throw new \RuntimeException("会话不存在");
            }

            $agentId = $conversation->agent_id;
        }

        $aiAgentModel = AiAgent::model()->findByPk($agentId);

        if (empty($aiAgentModel)) {
            throw new \RuntimeException("不支持的AI Agent");
        }

        $aiAgent->setConversationId($conversationId);
        $aiAgent->setQuestion($question);

        if ($fromAssistant) {
            $aiAgent->setAsync(false);
            $aiAgent instanceof FastArchiveAiAgent && $aiAgent->setResponseFormat(FastArchiveAiAgent::RESPONSE_FORMAT_KEY_LABEL);
        }


        try {
            $processRecord = $aiAgent->saveProcessRecord($question);

            $context = [
                'question_history_id' => empty($retryRecordId) ? \PgActiveRecord::produceAutoIncrementId() : 0,
                'answer_history_id' =>  \PgActiveRecord::produceAutoIncrementId(),
                'record_id' => $processRecord->record_id,
                'retry_record_id' => $retryRecordId
            ];

            $aiAgent->setContext($context);

            // 先保存历史记录用于暂停回写
            $aiAgent->saveConversation(\Yii::t('ai','正在生成记录中'), $question, $processRecord->record_id, AiAgentConstants::AI_AGENT_MESSAGE_TYPE_TEXT, $params);

            // 先返回 record_id 给前端，用于埋点
            $aiAgent->reponseRecordId();


            // 预先计费校验
            $aiAgent->checkUsageRestriction();

            if ($aiAgent->isAsync())
            {
                // 通知前端异步执行
                $aiAgent->sseResponse->writeJson(['protocol' => 'websocket', 'record_id' => $processRecord->record_id]);
                $aiAgent->timeLog['enter_queue'] = microtime(true);
                $jobParams = [
                    'client_id' => $clientId,
                    'user_id' => $userId,
                    'conversation_id' => $conversationId,
                    'process_record_id' => $processRecord->record_id,
                    'scene_type' => $aiAgent->sceneType,
                    'question' => $question,
                    'context' => $context,
                    'params' => $params,
                    'time_log' => $aiAgent->timeLog,
                ];
                $aiAgent->runAsync($jobParams);
            } else {
                $aiAgentProcessResponse = $aiAgent->process($params);
                $aiAgent->sseResponse($aiAgentProcessResponse);
            }
        } catch (\Throwable $throwable) {

            $aiAgentProcessResponse = Helper::handleException($aiAgent, $throwable);

        } finally {
            if (!$aiAgent->isAsync())
            {
                if (empty($aiAgentProcessResponse)) {
                    // 问答
                    $aiAgentProcessResponse = new AiAgentProcessResponse();
                    $aiAgentProcessResponse->setAnswer(AiAgentException::ERROR_MSG);
                    $aiAgentProcessResponse->setMessageType(AiAgentConstants::AI_AGENT_MESSAGE_TYPE_TEXT);
                }

                // 获取历史记录
                $history = $aiAgent->generateHistoryContent($aiAgentProcessResponse);

                // 保存历史聊天记录
                $aiAgent->saveConversation($history, $question, $aiAgentProcessResponse->recordId, $aiAgentProcessResponse->messageType, $params);

                $aiAgent->afterChat($params);

                $timeLog = $aiAgent->timeLog;
                $timeLog['api_end'] = microtime(true);
                $aiAgent->timeLog = $timeLog;

                $aiAgent->logAgentProcessInfo("timeLog", $aiAgent->timeLog);

                if (!empty($processRecord))
                {
                    // 保存各节点耗时
                    $aiAgent->updateAiProcessRecordContext($processRecord->record_id, ['timeLog' => $aiAgent->timeLog]);


                    $controllerEndTime = round(microtime(true) * 1000, 2);
                    $endTime = microtime(true);
                    $aiAgent->addNodeCostTime('request', '请求访问', $controllerBeginTime, $controllerEndTime);
                    $aiAgent->updateAiProcessRecordContext($processRecord->record_id, ['nodeCostTimeList' => $aiAgent->nodeCostTimeList]);
                    $aiAgent->addTraceLog(TraceConstant::AI_AGENT_READ_CHAT_COMPLETION, ['logKeyName' => TraceConstant::TRACE_NAME_MAP[TraceConstant::AI_AGENT_READ_CHAT_COMPLETION], 'costTime' => $endTime - $startTime, 'startTime' => $startTime, 'endTime' => $endTime]);
                }

                if (!empty($aiAgent->traceInfo) || ($aiAgent instanceof AiAgentProxy && !empty($aiAgent->getAgent()->traceInfo)))
                {
                    $traceInfo = empty($aiAgent->traceInfo) ? $aiAgent->getAgent()->traceInfo : $aiAgent->traceInfo;
                    $traceOperator = new \common\library\ai_agent\trace\AiTraceOperator($clientId);
                    $traceOperator->batchAddLogs($traceInfo);
                }

            }
        }
    }


    /**
     * NOTE: 目前只有WhatsApp插件有用到
     *
     * @param int $agent_id
     * @param array $conversation_params
     * @param array $params
     * @param string $question
     * @param int $conversation_id
     * @return void
     * @throws CDbException
     * @throws Exception
     * @throws RuntimeException
     * @throws ProcessException
     */
    public function actionOpenAndChatCompletions(
        int    $scene_type = 0,
        int    $conversation_id = 0,
        string $question = '',
        array  $params = []
    ) {
        $user = User::getLoginUser();


        if (empty($agent_id) && empty($scene_type)) {
            throw new \RuntimeException("缺少必要参数");
        }

        if (!empty($agent_id))
        {
            $aiAgentModel = AiAgent::model()->findByPk($agent_id);
            $scene_type = $aiAgentModel->scene_type;
        }


        $this->chatCompletions($user, $conversation_id, $scene_type, $question, $params, true);
    }

    /**
     * 判断是否有非本人的权限
     * @param int|string $clientId
     * @param int $userId
     * @return bool
     */
    protected function hasAdminScope(int|string $clientId, int $userId): bool
    {
        $userPrivilege = new UserPrivilegeService($clientId, $userId);
        $roles = $userPrivilege->getUserRoleInfoMap($userId);
        foreach (($roles[$userId] ?? []) as $role) {
            if ($role['scope'] != PrivilegeConstants::PRIVILEGE_SCOPE_OWNER) {
                return true;
            }
        }
        return false;
    }

    public function actionUserList($page = 1, $page_size = 100)
    {
        $this->validate(
            [
                'page' => 'integer|max:100',
                'page_size' => 'integer|max:200',
            ]
        );

        $user = User::getLoginUser();

        $userList = new \common\library\account\UserList();
        $userList->setClientId($user->getClientId());
        $userList->getFormatter()->setShowAiBillingInfo(true);
        $userList->getFormatter()->setShowUserDepartmentList(true);
        $list = $userList->find();
        // 因为UserList缓存会拉取全部数据 因此需要手动分页
        $list = array_slice($list, ($page - 1) * $page_size, $page_size);

        $this->success([
            'count' => $userList->count(),
            'list' => $list
        ]);
    }

    public function actionGetKCoinInfo()
    {
        $user = User::getLoginUser();
        $clientId = $user->getClientId();
        $userId = $user->getUserId();

        $this->success([
            'client' => \common\library\ai_agent\billing\Helper::getClientKCoinInfo($clientId),
            'user' => \common\library\ai_agent\billing\Helper::getUserKCoinInfo($clientId, $userId)
        ]);
    }

    public function actionChatUsers(array $company_id, array $channel_type = [Constant::CHANNEL_TYPE_WABA, Constant::CHANNEL_TYPE_TM, Constant::CHANNEL_TYPE_WHATSAPP, Constant::CHANNEL_TYPE_FACEBOOK, Constant::CHANNEL_TYPE_WHATSAPP_CLOUD], array $conversation_time = null)
    {
        $this->validate([
            'company_id' => 'required|array|max:100',
            'company_id.*' => 'required|int',
            'channel_type' => 'array',
            'channel_type.*' => 'required|in:' . implode(',', [Constant::CHANNEL_TYPE_WABA, Constant::CHANNEL_TYPE_TM, Constant::CHANNEL_TYPE_WHATSAPP, Constant::CHANNEL_TYPE_FACEBOOK, Constant::CHANNEL_TYPE_WHATSAPP_CLOUD]),
            'conversation_time' => 'array',
            'conversation_time.begin' => 'date',
            'conversation_time.end' => 'date',
        ]);

        $mailUser = [];

        $this->checkChatQualityPermission();

        $user = User::getLoginUser();
        $clientId = $user->getClientId();

        $company_id = array_filter($company_id);

        // 过滤掉无权限的company
        if ($company_id) {
            $companyList = new CompanyList($user->getUserId());
            $companyList->setShowAll(true);
            $companyList->setCompanyIds($company_id);
            $companyList->setFields(['company_id','mail_time']);
            $companyInfo = $companyList->find();
            $companyIdToInfo = array_column($companyInfo,null,'company_id');
            $company_id = array_keys($companyIdToInfo);
        }
        if (!$company_id) {
            throw new RuntimeException('没有公司权限', 403);
        }

        $limitTime = 86400 * 30;
        $today = strtotime('today');
        $conversation_time['begin'] = empty($conversation_time['begin']) ? date('Y-m-d H:i:s', $today - $limitTime) : date('Y-m-d H:i:s', strtotime($conversation_time['begin']));
        if (empty($conversation_time['end'])) {
            $conversation_time['end'] = date('Y-m-d 23:59:59', $today);
        } else {
            $conversation_time['end'] = date('Y-m-d 23:59:59', strtotime($conversation_time['end']));
        }
        if ($today - strtotime($conversation_time['begin']) > $limitTime) {
            throw new RuntimeException('参数错误：只能选择最近30天');
        }

        $snsTypes = array_map([CustomerContactService::class, 'channelTypeToSnsType'], $channel_type);

        $user = User::getLoginUser();
        $service = new CustomerContactService($user->getClientId());
        $senderUserIds = $service->getSenderIdsByCompany($company_id, $snsTypes, $conversation_time['begin'], $conversation_time['end']);

        // 获取邮件
        $hasMailCompanyInfo = array_filter($companyIdToInfo, function ($v) {
            $mailTime = $v['mail_time'];
            return $mailTime != '1970-01-01 00:00:00';
        });

        if (!empty($hasMailCompanyInfo)) {
            $userMailList = new UserMailList($clientId);
            $userMailIds = array_column($userMailList->find(),'user_mail_id');

            $list = new CustomerList($clientId);
            $list->setCompanyId(array_keys($hasMailCompanyInfo));
            $list->setFields(['email']);
            // 获取到用户到email
            $emails = array_column($list->find(), 'email');

            $mailList = new \common\library\mail\MailList();
            $mailList->setSearchModel(Constants::SEARCH_MODEL_SEARCHER);
            $mailList->searcher->setSearchDate($conversation_time['begin'], $conversation_time['end']);
            $mailList->searcher->setCustomerEmails($emails);
            $mailList->searcher->setUserMailIds($userMailIds);
            $mailList->searcher->setLimit(5000);
            $mailInfoList = $mailList->find();
            $mailUser = array_unique(array_column($mailInfoList,'user_id'));
        }

        $senderUserIds = array_unique(array_merge($senderUserIds,$mailUser));

        $list = [];
        if ($senderUserIds) {
            $userList = new \common\library\account\UserList();
            $userList->setClientId($user->getClientId());
            $userList->setUserIds($senderUserIds);
            $userList->setFields(['nickname', 'email', 'avatar', 'user_id']);
            $list = $userList->find();
        }

        $this->success([
            'users' => $list,
        ]);
    }

    public function actionChatConversations(array $company_id, array $channel_type, array $conversation_time, array $user_id)
    {
        $this->validate([
            'company_id' => 'required|array|max:100',
            'company_id.*' => 'required|int',
            'channel_type' => 'required|array',
            'channel_type.*' => 'required|in:' . implode(',', [Constant::CHANNEL_TYPE_MAIL,Constant::CHANNEL_TYPE_WABA, Constant::CHANNEL_TYPE_TM, Constant::CHANNEL_TYPE_WHATSAPP, Constant::CHANNEL_TYPE_FACEBOOK, Constant::CHANNEL_TYPE_WHATSAPP_CLOUD]),
            'conversation_time' => 'required|array',
            'conversation_time.begin' => 'required|date',
            'conversation_time.end' => 'required|date',
            'user_id' => 'required|array',
            'user_id.*' => 'required|int',
        ]);

        $conversation_time['begin'] = date('Y-m-d H:i:s', strtotime($conversation_time['begin']));
        $conversation_time['end'] = date('Y-m-d 23:59:59', strtotime($conversation_time['end']));
        if (strtotime('today') - strtotime($conversation_time['begin']) > 86400 * 31 * 3) {
            throw new RuntimeException('参数错误：只能选择最近3个月');
        }

        $this->checkChatQualityPermission();

        $user = User::getLoginUser();

        $company_id = array_filter($company_id);

        $companies = [];

        // 过滤掉无权限的company
        if ($company_id) {
            $companyList = new CompanyList($user->getUserId());
            $companyList->setShowAll(true);
            $companyList->setCompanyIds($company_id);
            $companyList->setFields(['company_id', 'name', 'main_customer','mail_time']);
            $companies = array_column($companyList->find(), null, 'company_id');
            $company_id = array_column($companies, 'company_id');
        }
        if (!$company_id) {
            throw new RuntimeException('没有公司权限', 403);
        }

        $snsTypes = array_map([CustomerContactService::class, 'channelTypeToSnsType'], $channel_type);

        $service = new CustomerContactService($user->getClientId());

        // 查询所有user_customer_contact_message
        $messages = $service->getAllMessagesByCompany($company_id, $snsTypes, $conversation_time['begin'], $conversation_time['end'], columns: ['send_time', 'send_id', 'user_contact_id', 'send_type']);
        // NOTE: 拆分消息记录, 过滤并进行汇总
        $conversations = $service::groupMessagesBySender($messages, $user_id);
        $customerIds = array_column($conversations, 'customer_id');
        $customerIds = array_filter(array_merge($customerIds, array_column($companies, 'main_customer')));
        $userIds = array_column($messages, 'send_id');

        // 邮件质检相关
        if (in_array(Constant::CHANNEL_TYPE_MAIL,$channel_type)) {
            $mailConversation = [];
            // 获取邮件
            $hasMailCompanyInfo = array_filter($companies, function ($v) {
                $mailTime = $v['mail_time'];
                return $mailTime != '1970-01-01 00:00:00';
            });

            if (!empty($hasMailCompanyInfo))
            {
                $companyIds = array_keys($hasMailCompanyInfo);
                $mailConversation = Helper::getMailConversationMap($companyIds,$conversation_time['begin'], $conversation_time['end'],$user_id);
                $mailConversations = Helper::groupMessagesByUserCustomerEmail($mailConversation);
                $conversations = array_merge($conversations,$mailConversations);
            }
            $userIds = array_unique(array_merge($userIds,array_keys($mailConversation)));

        }

        $users = [];
        if ($userIds) {
            $userList = new \common\library\account\UserList();
            $userList->setClientId($user->getClientId());
            $userList->setUserIds($userIds);
            $userList->setFields(['nickname', 'email', 'avatar', 'user_id']);
            $users = array_column($userList->find(), null, 'user_id');
        }

        $customers = [];
        if ($customerIds) {
            $customerList = new CustomerList($user->getClientId());
            $customerList->setCustomerId($customerIds);
            $customerList->setAlias('a');
            $customerList->setJoin((new CompanyFilter($user->getClientId(), 'b')), BaseList::JOIN_TYPE_LEFT, ['company_id', 'company_id']);
            $customerList->setFields(['customer_id', 'a.name', 'b.name as company_name']);
            $customers = array_column($customerList->find(), null, 'customer_id');
        }

        foreach ($conversations as $k => $conversation) {
            $userId = $conversation['send_id'] ?? $conversation['user_id'] ?? 0;
            $conversations[$k]['user_info'] = $users[$userId] ?? [];
            $customer = $customers[$conversation['customer_id']] ?? [];
            $company = $companies[$conversation['company_id']] ?? [];
            $mainCustomer = $customers[$company['main_customer'] ?? 0] ?? [];

            $conversations[$k]['customer_name'] = $customer['name'] ?? $mainCustomer['name'] ?? '';
            $conversations[$k]['company_name'] = $customer['company_name'] ?? $company['name'] ?? '';
        }

        $this->success([
            'conversations' => $conversations,
        ]);
    }

    public function actionChatQualityTasks($page = 1, $page_size = 10)
    {
        $user = User::getLoginUser();
        $clientId = $user->getClientId();
        $userId = $user->getUserId();

        $this->checkChatQualityPermission();

        // 只能查看权限范围内的任务
        $userIds = \common\library\privilege_v3\Helper::getPermissionScopeUser($clientId, $userId, \common\library\privilege_v3\PrivilegeConstants::PRIVILEGE_CRM_COMPANY_VIEW, true);

        $taskFilter = new \common\library\async_task\AsyncTaskFilter($clientId);
        $taskFilter->type = \Constants::TYPE_OKKI_AI;
        $taskFilter->update_user = $userIds;
        $taskFilter->scene = AsyncTaskConstant::SCENE_AI_CHAT_QC;
        $taskFilter->order('task_id', 'desc');
        $count = $taskFilter->count();
        $list = [];
        if (!empty($count)) {
            $taskFilter->limit($page_size, $page);
            $taskList = $taskFilter->find();
            $taskList->getFormatter()->chatQualityTasksSetting();
            $list = $taskList->getAttributes();
        }

        $this->success([
            'count' => $count,
            'list' => $list
        ]);
    }

    public function actionChatQualityTaskReport($task_id)
    {
        $this->validate([
            'task_id' => 'required|int',
        ]);

        $user = User::getLoginUser();
        $clientId = $user->getClientId();
        $userId = $user->getUserId();

        $this->checkChatQualityPermission();

        $task = new AsyncTask($clientId, $task_id);
        if ($task->isNew()) {
            throw new RuntimeException('任务不存在');
        }

        if (empty(\common\library\privilege_v3\Helper::filterPermissionScopeUser($clientId, $userId, \common\library\privilege_v3\PrivilegeConstants::PRIVILEGE_CRM_COMPANY_VIEW, $task->update_user, true))) {
            throw new RuntimeException('没有权限查看该任务', 403);
        }

        $api = new AnalysisRecordAPI($clientId, $userId);
        $report = $api->chatQualityReport($task_id);

        $taskParams = $task->ext_info ?? [];
        $this->success([
            'task_name' => $taskParams['task_name'] ?? '',
            'create_time' => $task->create_time,
            'start_time' => $task->start_time,
            'update_time' => $task->update_time,
            'customer_num' => count($taskParams['company_id'] ?? []),
            'conversation_num' => count($taskParams['conversation_key'] ?? []),
            'user_num' => count($taskParams['user_id'] ?? []),
            'team_performance' => $report['team_performance'] ?? [],
            'user_performance' => $report['user_performance'] ?? [],
        ]);
    }

    public function actionChatQualityConversation($task_id, $keyword = '', array $channel_type = null, array $user_id = null, string $quality_params = '', $page = 1, $page_size = 20)
    {
        $this->validate([
            'task_id' => 'required|int',
            'user_id' => 'array',
            'user_id.*' => 'int',
            'channel_type' => 'array',
            'channel_type.*' => 'required|in:' . implode(',', [Constant::CHANNEL_TYPE_MAIL,Constant::CHANNEL_TYPE_WABA, Constant::CHANNEL_TYPE_TM, Constant::CHANNEL_TYPE_WHATSAPP, Constant::CHANNEL_TYPE_FACEBOOK, Constant::CHANNEL_TYPE_WHATSAPP_CLOUD]),
            'page' => 'int',
            'page_size' => 'int',
        ]);

        $quality_params = empty($quality_params) ? [] : json_decode($quality_params, true);
        foreach ($quality_params ?: [] as $key => $ranges) {
            if (!in_array($key, AiAgentConstants::CHAT_QUALITY_FIELD_KEY_MAP)) {
                throw new RuntimeException('Unknown quality key');
            }
            if (!is_array($ranges)) {
                throw new RuntimeException('quality_params.*必须为数组');
            }
            foreach ((array)$ranges as $i => $range) {
                $range = explode('-', $range);
                if (count($range) != 2) {
                    throw new RuntimeException('Err score range');
                }
                $newRange['begin'] = (!isset($range[0]) || $range[0] === '') ? -1 : intval($range[0]);
                $newRange['end'] = (!isset($range[1]) || $range[1] === '') ? 101 : intval($range[1]);
                $quality_params[$key][$i] = $newRange;
            }
        }

        $this->checkChatQualityPermission();

        $user = User::getLoginUser();
        $clientId = $user->getClientId();
        $userId = $user->getUserId();

        $task = new AsyncTask($clientId, $task_id);
        if ($task->isNew()) {
            throw new RuntimeException('任务不存在');
        }

        if (empty(\common\library\privilege_v3\Helper::filterPermissionScopeUser($clientId, $userId, \common\library\privilege_v3\PrivilegeConstants::PRIVILEGE_CRM_COMPANY_VIEW, $task->update_user, true))) {
            throw new RuntimeException('没有权限查看该任务', 403);
        }

        $api = new AnalysisRecordAPI($clientId, $userId);
        $list = $api->chatQualityDetails($task_id, compact('user_id', 'channel_type', 'keyword', 'quality_params', 'page', 'page_size'));
        $list['task_info'] = [
            'task_id' => $task_id,
            'task_name' => $task->ext_info['task_name'] ?? '',
            'create_time' => $task->create_time,
            'start_time' => $task->start_time,
            'update_time' => $task->update_time,
        ];

        $this->success($list);
    }

    public function actionChatQualityDetailUsers($task_id)
    {
        $this->validate([
            'task_id' => 'required|int',
        ]);

        $this->checkChatQualityPermission();

        $user = User::getLoginUser();
        $clientId = $user->getClientId();

        $task = new AsyncTask($clientId, $task_id);
        if ($task->isNew()) {
            throw new RuntimeException('任务不存在');
        }

        $filter = new AnalysisRecordFilter($clientId);
        $filter->task_id = $task_id;
        $filter->select([
            'sender_id' => fn () => "refer_data#>>'{user_id}' as sender_id",
        ]);
        $userIds = array_unique(array_filter(array_column($filter->rawData(), 'sender_id')));
        $users = [];
        if ($userIds) {
            $userList = new \common\library\account\UserList();
            $userList->setClientId($user->getClientId());
            $userList->setUserIds($userIds);
            $userList->setFields(['nickname', 'email', 'avatar', 'user_id']);
            $userList->setExcludeDeleteUserFlag(false);
            $users = $userList->find();
        }

        $this->success(['users' => $users]);
    }

    protected function checkChatQualityPermission()
    {
        $user = User::getLoginUser();
        $privilegeService = PrivilegeService::getInstance($user->getClientId(), $user->getUserId());
        if ($privilegeService->getPrivilegeScope(PrivilegeConstants::PRIVILEGE_CRM_COMPANY_VIEW) <= PrivilegeConstants::PRIVILEGE_SCOPE_OWNER) {
            throw new RuntimeException('没有批量沟通质检的权限', 403);
        }
    }

    public function actionAssetAnalysisList(
        int $scene,
        int $task_id = 0,
        $show_business_data = 0,
        string $data_source_type = AiAgentConstants::COMPANY_ANALYSIS_DATA_SOURCE_TYPE_OPPORTUNITY
    )
    {
        $user = \User::getLoginUser();

        if ($show_business_data) {
            $taskScene = [AsyncTaskConstant::SCENE_AI_ASSET_ANALYSIS_OPERATION];
        } else {
            $taskScene = match ($scene) {
                // 客户资产分析界面需要拉出订阅和手动分析
                AsyncTaskConstant::SCENE_AI_ASSET_ANALYSIS => [AsyncTaskConstant::SCENE_AI_ASSET_ANALYSIS, AsyncTaskConstant::SCENE_AI_SYSTEM_ASSET_ANALYSIS],
                // 团队分析洁面需要拉出订阅和手动分析
                AsyncTaskConstant::SCENE_AI_TEAM_ANALYSIS => [AsyncTaskConstant::SCENE_AI_TEAM_ANALYSIS, AsyncTaskConstant::SCENE_AI_SYSTEM_TEAM_ANALYSIS],
                default => [$scene],
            };

        }

        // 获取最新的一条taskId
        $lastTaskInfo = Helper::getLastTask($user->getClientId(), $user->getUserId(), $taskScene, [AsyncTaskConstant::TASK_STATUS_SUCCEED], $task_id);

        $defaultAnalysisList = [];
        switch ($scene) {
            case AsyncTaskConstant::SCENE_AI_ASSET_ANALYSIS:
                $defaultAnalysisList = \common\library\ai_agent\config\AssetAnalysisConfig::getDefaultAssetAnalysisList($data_source_type);
                break;
            case AsyncTaskConstant::SCENE_AI_TEAM_ANALYSIS:
                $defaultAnalysisList = \common\library\ai_agent\config\TeamAnalysisConfig::getDefaultAssetAnalysisList();
                break;
            default:
                break;
        }


        if (empty($lastTaskInfo)) {
            $result = ['echo_params' => [], 'list' => $defaultAnalysisList, 'analysis_number' => 0, 'finish_analysis_number' => 0, 'template_flag' => 1, 'data_source_type' => $data_source_type];
            $this->success($result);
        }

        $keyToDefaultSettingMap = [];
        // 处理默认配置，如果数据稀疏那么展示默认配置
        foreach ($defaultAnalysisList as $analysisList) {
            // 一级标题
            $key = $analysisList['key'];
            $keyToDefaultSettingMap[$key]['suggestion'] = $analysisList['suggestion'];
            $keyToDefaultSettingMap[$key]['conclusion'] = $analysisList['conclusion'];
            // 三级标题
            $subList = $analysisList['list'];
            foreach ($subList as $subItems) {
                $list = $subItems['list'];
                foreach ($list as $item) {
                    $subKey = $item['sub_key'];
                    $keyToDefaultSettingMap[$subKey]['conclusion'] = $item['conclusion'];

                }
            }
        }

        // 组装返回值
        $sceneAnalysisList = [];
        $analysisList = [];
        $analysisNumber = $lastTaskInfo['ext_info']['analysis_number'] ?? 0;

        // 根据TaskId获取AiAnalysisRecord
        $aiAnalysisRecordListPdo = new \common\library\ai_agent\record\AiAnalysisRecordList($user->getClientId());
        $aiAnalysisRecordListPdo->setTaskId($lastTaskInfo['task_id']);
        $aiAnalysisRecordListPdo->setOrderBy('record_id');
        $aiAnalysisRecordListPdo->setOrder('asc');
        $aiAnalysisRecordList = $aiAnalysisRecordListPdo->find();

        $recordIds = array_column($aiAnalysisRecordList,'process_record_id');
        $recordIds = array_values(array_filter($recordIds));

        // 获取点赞状态
        $feedBackList = new AiServiceRecordFeedbackList($user->getClientId(),$user->getUserId());
        $feedBackList->setRecordId($recordIds);
        $feedbackInfoList = $feedBackList->find();
        $recordIdToFeedbackMap = array_column($feedbackInfoList,'favorite','record_id');

        // 组装列表
        foreach ($aiAnalysisRecordList as $aiAnalysisRecord)
        {
            $referKey = $aiAnalysisRecord['refer_key'] ?? '';
            $analysisResult = $aiAnalysisRecord['analysis_result'] ?? [];
            $analysisResult = json_decode($analysisResult,true);
            $recordId = $aiAnalysisRecord['process_record_id'] ?? 0;
            $analysisRecordId = $aiAnalysisRecord['record_id'] ?? 0;
            // 用户前端判断用什么字段展示数据详情 0是用data 1是用report_detail_data
            $dataType = 0;
            // 根据语言切换，默认是zh-CN
            $result = isset($analysisResult['analysis_result_' . \Yii::app()->language]) ? $analysisResult['analysis_result_' . \Yii::app()->language] : $analysisResult['analysis_result'];
            $result = (empty(json_decode($result))) ? $result : json_decode($result, true);

            // 存在title，证明是总分析结果
            if (isset($analysisResult['title']))
            {
                $conclusion = $scene == AsyncTaskConstant::SCENE_AI_TEAM_ANALYSIS ? $result['SUMMARIES'] ?? '' : $result['INSIGHT'] ?? '';
                $suggestion = $scene == AsyncTaskConstant::SCENE_AI_TEAM_ANALYSIS ? $result['SUGGESTIONS'] ?? '' : $result['SUGGESTION'] ?? '';
                $timeoutFlag = $analysisResult['timeout_flag'] ?? false;

                $sceneAnalysis = [
                    'title' => \Yii::t('ai',$analysisResult['title']),
                    'conclusion' => $timeoutFlag ? Yii::t('ai', '因处理超时导致本次分析失败，请稍后再试。'):$conclusion,
                    'suggestion' => $timeoutFlag ? Yii::t('ai', '因处理超时导致本次分析失败，请稍后再试。') : $suggestion,
                    'key' => $analysisResult['scene_key'] ?? '',
                    'favorite' => $recordIdToFeedbackMap[$recordId] ?? 0,
                    'analysis_record_id' => $analysisRecordId,
                    'richness' => 0,
                    'timeout_flag' => $analysisResult['timeout_flag'] ?? false
                ];
                if (!isset($sceneAnalysisList[$referKey])){
                    $sceneAnalysisList[$referKey] = [];
                }

                $sceneAnalysisList[$referKey] = array_merge($sceneAnalysisList[$referKey], $sceneAnalysis);
            } else {
                $referData = json_decode($aiAnalysisRecord['refer_data'], true);
                // 团队分析是2层结构
                $analysisKey = $scene == AsyncTaskConstant::SCENE_AI_TEAM_ANALYSIS ? $referKey : $analysisResult['analysis_key'];
                $subKey = $analysisResult['sub_key'];

                // 初始化报表数据
                $reportKey = $referData['report_key'] ?? '';
                $reportName = empty($reportKey) ? '' : (new \common\library\statistics\render\report\Report($user->getClientId(), $reportKey))->getReportConfig()->getAttributes()->name;
                $report = [
                    'params' => $referData['params'] ?? [],
                    'report_key' => $referData['report_key'] ?? '',
                    'name' => Yii::t('report', $reportName)
                ];

                $richness = $analysisResult['richness'] ?? 0;
                $conclusion = $result;

                // TODO 后续资产分析修复返回格式时，需要重新修改此处richness赋值
                // 不丰富或者建议为数据稀疏，则展示默认数据
                if ($richness == 0 || $conclusion == AiAgentException::ERROR_CODE_TO_USER_MSG[AiAgentException::ERR_NOT_ENOUGH_ASSET_ANALYSIS]) {
                    $defaultInfo = $keyToDefaultSettingMap[$subKey];
                    $conclusion = $defaultInfo['conclusion'] ?? '';
                    $richness = 0;
                }
                if (!empty($referData['report_detail_data'])) {
                    $dataType = 1;
                }

                // 初始化子分析列表数据
                $subAnalysisList = [
                    'title' => \Yii::t('ai', $analysisResult['sub_title']),
                    'conclusion' => $conclusion,
                    'sub_key' => $subKey,
                    'data' => $referData['data'] ?? [],
                    'report_detail_data' => $referData['report_detail_data'] ?? [],
                    'richness' => $richness,
                    'report' => $report,
                    'data_type' => $dataType,
                    'analysis_record_id' => $analysisRecordId,
                    'timeout_flag' => $analysisResult['timeout_flag'] ?? false
                ];

                if (!isset($analysisList[$analysisKey]['title'])) {
                    $analysisList[$analysisKey]['refer_key'] = $referKey;
                    $analysisList[$analysisKey]['key'] = $analysisResult['analysis_key'];
                    $analysisList[$analysisKey]['title'] = \Yii::t('ai',$analysisResult['analysis_title']);
                }
                $analysisList[$analysisKey]['list'][] = $subAnalysisList;
            }

            // 点赞回显 因为是批量点赞的，只要有非0的状态那么就直接回显
            if (! isset($sceneAnalysisList[$referKey]['favorite']) || $sceneAnalysisList[$referKey]['favorite'] == 0) {
                $sceneAnalysisList[$referKey]['favorite'] = $recordIdToFeedbackMap[$recordId] ?? 0;
            }


            if (!empty($recordId)) {
                $sceneAnalysisList[$referKey]['process_record_ids'][] = $recordId;
            }
        }

        // 组装子列表 & 赋值主场景丰富度
        foreach ($analysisList as $analysis)
        {
            $referKey = $analysis['refer_key'];
            $list = $analysis['list'] ?? [];

            foreach ($list as $subAnalysis)
            {
                $richness = $subAnalysis['richness'];

                // 子分析列表存在一个丰富的，那么主场景就是丰富的
                if ($richness != 0) {
                    $sceneAnalysisList[$referKey]['richness'] = 1;
                }
            }

            // 如果场景不丰富则赋值默认数据
            if ($sceneAnalysisList[$referKey]['richness'] == 0) {
                $defaultMap = $keyToDefaultSettingMap[$referKey] ?? [];
                $defaultConclusion = $defaultMap['conclusion'] ?? '';
                $defaultSuggestion = $defaultMap['suggestion'] ?? '';
                $sceneAnalysisList[$referKey]['conclusion'] = $defaultConclusion;
                $sceneAnalysisList[$referKey]['suggestion'] = $defaultSuggestion;
            }

            $sceneAnalysisList[$referKey]['list'][] = $analysis;
        }

        // 记录ID去重
        $sceneAnalysisList = array_map(function ($sceneAnalysis) {
            $sceneAnalysis['process_record_ids'] = array_values(array_unique($sceneAnalysis['process_record_ids'] ?? []));
            return $sceneAnalysis;
        }, $sceneAnalysisList);

        foreach ($lastTaskInfo['ext_info']['echo_params'] ?? [] as $index =>  $param) {
            $param['label'] = \Yii::t('report',$param['label']);
            $param['value'] = \Yii::t('report',$param['value']);
            $lastTaskInfo['ext_info']['echo_params'][$index] = $param;
        }

        $result = [
            'echo_params' => $lastTaskInfo['ext_info']['echo_params'] ?? [],
            'list' => array_values($sceneAnalysisList),
            'analysis_number' => $analysisNumber,
            'finish_analysis_number' => count($aiAnalysisRecordList),
            'finish_time' => $lastTaskInfo['finish_time'] ?? '',
            'template_flag' => 0,
            'task_id' => $lastTaskInfo['task_id']
        ];
        $this->success($result);
    }


    public function actionGetTaskProgress(int $scene = AsyncTaskConstant::SCENE_AI_ASSET_ANALYSIS)
    {
        $user = \User::getLoginUser();
        $userId = $user->getUserId();
        $clientId = $user->getClientId();
        $analysisNumber = 0;
        $finishCount = 0;
        $taskInfo = [];

        $scene = match ($scene) {
           AsyncTaskConstant::SCENE_AI_ASSET_ANALYSIS => [AsyncTaskConstant::SCENE_AI_ASSET_ANALYSIS],
            AsyncTaskConstant::SCENE_AI_TEAM_ANALYSIS => [AsyncTaskConstant::SCENE_AI_TEAM_ANALYSIS],
            default => [$scene]
        };
        $lastTaskInfo = Helper::getLastTask(
            $clientId,
            $userId,
            $scene,
            []
        );

        if (!empty($lastTaskInfo)) {
            $analysisNumber = $lastTaskInfo['ext_info']['analysis_number'] ?? 0;
            // 根据TaskId获取AiAnalysisRecord
            $aiAnalysisRecordListPdo = new \common\library\ai_agent\record\AiAnalysisRecordList($user->getClientId());
            $aiAnalysisRecordListPdo->setTaskId($lastTaskInfo['task_id']);
            $finishCount = $aiAnalysisRecordListPdo->count();
            $taskInfo['status'] = $lastTaskInfo['status'] ?? 0;
            $taskInfo['start_time'] = $lastTaskInfo['start_time'];
            $taskInfo['task_id'] = $lastTaskInfo['task_id'];
        }

        $this->success(
            [
                'analysis_number' => $analysisNumber,
                'finish_count' => $finishCount,
                'task_info' => $taskInfo
            ]
        );

    }

    public function actionAssetAnalysisTaskList(int $page = 1, int $page_size = 50, int $scene = AsyncTaskConstant::SCENE_AI_ASSET_ANALYSIS)
    {

        $scene = match ($scene) {
            // 客户资产分析界面需要拉出订阅和手动分析
            AsyncTaskConstant::SCENE_AI_ASSET_ANALYSIS => [AsyncTaskConstant::SCENE_AI_ASSET_ANALYSIS, AsyncTaskConstant::SCENE_AI_SYSTEM_ASSET_ANALYSIS],
            AsyncTaskConstant::SCENE_AI_TEAM_ANALYSIS => [AsyncTaskConstant::SCENE_AI_TEAM_ANALYSIS, AsyncTaskConstant::SCENE_AI_SYSTEM_TEAM_ANALYSIS],
            default => [$scene],
        };

        $user = User::getLoginUser();
        $clientId = $user->getClientId();
        $userId = $user->getUserId();
        // 获取最新一条task任务信息
        $taskFilter = new \common\library\async_task\AsyncTaskFilter($clientId);
        $taskFilter->type = \Constants::TYPE_OKKI_AI;
        $taskFilter->update_user = $userId;
        $taskFilter->scene = $scene;
        $taskFilter->status = [AsyncTaskConstant::TASK_STATUS_SUCCEED,AsyncTaskConstant::TASK_STATUS_FAILED];
        $taskFilter->order('task_id', 'desc');
        $taskFilter->limit($page_size, $page);
        $taskList = $taskFilter->find();
        $taskList->getFormatter()->chatQualityTasksSetting();
        $list = $taskList->getAttributes();
        $this->success([
            'list' => $list,
            'count' => $taskFilter->count()
        ]);
    }
    // 回信摘要接口
    public function actionMailReplySummary(array $params = [])
    {

        $user = User::getLoginUser();
        $userId = $user->getUserId();
        $clientId = $user->getClientId();
        $sceneType = AiAgent::AI_AGENT_SCENE_TYPE_MAIL_REPLY;

        $aiAgent = AiAgentFactory::createAgent($sceneType, $clientId, $userId);
        $aiAgent->setHookFlag(true);
        $aiAgent->requestParams = $params;

        $aiAgent->setQuestion('');
        $processRecord = $aiAgent->saveProcessRecord("");
        $context = [
            'record_id' => $processRecord->record_id,
            'question_history_id' => \PgActiveRecord::produceAutoIncrementId(),
            'answer_history_id' => \PgActiveRecord::produceAutoIncrementId(),
        ];
        $aiAgent->setContext($context);
        $summaryContent = "";
        $params['params']['skip_handle_billing'] = true;
        $params['params']['disable_stream'] = true;
        $response = $aiAgent->process($params);

        $messages = $aiAgent->getMessageObj($response);

        $summaryContent = $response->context['content'];
        $ret = [
            "summaryContent" => $summaryContent
        ];

        $this->success($ret);
    }

    public function actionGetSubscription(int $subscribe_type)
    {
        $user = User::getLoginUser();
        $service = new \common\library\statistics\subscribe\AiSubscribeService($user->getUserId());
        $data = $service->getSubscribeconfig($subscribe_type);
        $this->success($data);
    }

    public function actionGetPreMessageList(int $scene_type, array $conversation_params = [] )
    {
        $user = User::getLoginUser();
        $userId = $user->getUserId();
        $clientId = $user->getClientId();

        $aiAgent = AiAgentFactory::createAgent($scene_type, $clientId, $userId,params: $conversation_params);
        $preMessageList = $aiAgent->getPreMessageList($conversation_params);
        $preMessageList = $aiAgent->translatePreMessageList($preMessageList);
        if(!empty($preMessageList) && !empty($conversation_params['params']) && !empty($conversation_params['params']['channel_type'])){
            $preMessageList = array_values($preMessageList);
            $preMessageList[0]['params']['params']['channel_type'] = $conversation_params['params']['channel_type'];
        }
        $this->success([
            'preset_message_list' => $preMessageList
        ]);

    }

    /**
     * 查询客户质检池的聊天阶段数量
     * https://xmkm.yuque.com/armee3/wg6g9f/ahbw733s8vmke7pi#DUUOY
     * @return void
     */
    public function actionCompanyQualityCheckStatistics()
    {
        $user = \User::getLoginUser();
        $clientId = $user->getClientId();

        //有客户查看权限 & 角色可见范围大于本人
        if(!Helper::hasCompanyQualityCheckPermission($clientId)){
            throw new RuntimeException("没有查看谈单监测权限");
        }

//        $privilegeService = PrivilegeService::getInstance($clientId, $user->getUserId());
//        if($privilegeService->getPrivilegeScope(PrivilegeConstants::PRIVILEGE_CRM_COMPANY_VIEW) <= PrivilegeConstants::PRIVILEGE_SCOPE_OWNER){
//            throw new RuntimeException("没有查看客户质检的权限");
//        }

        return $this->success(QualityCheckHelper::getContactStageCounts());
    }

    /**
     * 查询客户质检列表
     * https://xmkm.yuque.com/armee3/wg6g9f/ahbw733s8vmke7pi#Bij3X
     * @param int $page
     * @param int $page_size
     * @param array $params
     * @return void
     */
    public function actionGetCompanyQualityCheckList
    (
        int $page,
        int $page_size,
        array $params = []
    )
    {
        $user = \User::getLoginUser();
        $clientId = $user->getClientId();
        $userId = $user->getUserId();
        $privilegeService = PrivilegeService::getInstance($clientId, $userId);
        $systemId = $privilegeService->getMainSystemId();
        if (!in_array($systemId, AiAgentConstants::AI_QC_SYSTEM_IDS)) {
            throw new RuntimeException("没有查看谈单监测权限");
        }
        if(!$privilegeService->hasFunctional(PrivilegeConstants::FUNCTIONAL_OKKI_AI_QC)){
            throw new RuntimeException("没有查看谈单监测权限");
        }

        //客户质检列表,由于最多只有500个,所以可以一次性查询
        $qcCompanyFinder = new AiQualityCheckCompanyList($clientId, $userId);
        $qcCompanyFinder->setFollowUserId($params['user_ids'] ?? []);
        $qcCompanyFinder->setContactStage($params['contact_stage'] ?? 0);
        $qcCompanyFinder->setRecentContactTimeRange($params['recent_contact_time_range'] ?? []);
        $qcCompanyFinder->setContactProgressUpdateTimeRange($params['contact_progress_update_time_range'] ?? []);

        if (!empty($params['sort_field'])) {
            $qcCompanyFinder->setOrderBy("{$qcCompanyFinder->getAlias()}.{$params['sort_field']}");

            if (!empty($params['sort_type'])) {
                $qcCompanyFinder->setOrder($params['sort_type']);
            }
        } else {
            $qcCompanyFinder->setOrderBy("{$qcCompanyFinder->getAlias()}.report_update_time");
            $qcCompanyFinder->setOrder("desc");
        }
        $qcCompanyFinder->setOffset(($page - 1) * $page_size);
        $qcCompanyFinder->setLimit($page_size);

        $data = $qcCompanyFinder->find();
        /**
         * @var AiQualityCheckCompanyFormatter $formatter
         */
        $formatter = $qcCompanyFinder->getFormatter();
        $formatter->setShowStickingPoint(true);
        $formatter->setListData($data);
        $format = $formatter->result();

        $total = $qcCompanyFinder->count();

        $res = [];
        $res['list'] = $format;
        $res['total'] = $total;

        return $this->success($res);
    }

    /**
     * 查询质检客户详情
     * https://xmkm.yuque.com/armee3/wg6g9f/ahbw733s8vmke7pi#Pnbeq
     * @param int $company_id company id
     * @return void
     */
    public function actionGetQualityCheckCompanyInfo(int $company_id = 0)
    {
        $user = \User::getLoginUser();
        $qcCompanyFinder = new AiQualityCheckCompanyList($user->getClientId() , $user->getUserId());
        $qcCompanyFinder->setCompanyId($company_id);
        $data = $qcCompanyFinder->find();
        $qcCompanyFinder->getFormatter()->setListData($data);
        $formatData = $qcCompanyFinder->getFormatter()->result();
        if(empty($formatData)){
            throw new RuntimeException("质检客户详情不存在");
        }
        return $this->success($formatData[0]);
    }

    /**
     * 查询客户质检详情
     * https://xmkm.yuque.com/armee3/wg6g9f/ahbw733s8vmke7pi#wB9PP
     * @param int $company_id
     * @param int|null $user_id
     */
    public function actionGetCompanyQualityCheckDetail(int $company_id , int $user_id = null, array $contact_keywords = [] , int $page = 1 , int $page_size = 10 , array $journey_ids = [])
    {
        $user = \User::getLoginUser();
        //鉴权
        $privilegeService = PrivilegeService::getInstance($user->getClientId(), $user->getUserId());
        $systemId = $privilegeService->getMainSystemId();
        if (!in_array($systemId,AiAgentConstants::AI_QC_SYSTEM_IDS)) {
            throw new RuntimeException("没有查看谈单监测权限");
        }
        if(!$privilegeService->hasFunctional(PrivilegeConstants::FUNCTIONAL_OKKI_AI_QC)){
            throw new RuntimeException("没有谈单监测权限");
        }
        //qc_company是否被删除
        $qcCompanyFinder = new AiQualityCheckCompanyList($user->getClientId(), $user->getUserId());
        $qcCompanyFinder->setCompanyId($company_id);
        $qcCompanyList = $qcCompanyFinder->find();
        if(empty($qcCompanyList)){
            return [];
        }

        $qcJourneyFinder = new AiQualityCheckCompanyJourneyList($user->getClientId(),$company_id);
        $qcJourneyFinder->setCompanyId($company_id);
        $qcJourneyFinder->setJourneyIds($journey_ids);
        $qcJourneyFinder->setOrderBy('contact_date');
        $qcJourneyFinder->setOrder('desc');
        $qcJourneyFinder->setKeyWords($contact_keywords);
        $total = $qcJourneyFinder->count();
        $qcJourneyFinder->setLimit($page_size);
        $qcJourneyFinder->setOffset(($page - 1) * $page_size);
        $data = $qcJourneyFinder->find();
        if(!empty($data)){
            $format = $qcJourneyFinder->getFormatter()->setListData($data)->result();
        }else{
            $format = [];
        }

        $result = [];
        $result['chat_journey'] = $format;

        $latestAnalyzeInfo = QualityCheckHelper::getLatestAnalyzeInfoByCompanyId($user->getClientId(), $company_id);
        $result['analyze_time'] = $latestAnalyzeInfo['analyze_time'] ?? '';
        $result['analysis'] = $latestAnalyzeInfo['analysis'] ?? '';

        $latestContactTime = QualityCheckHelper::getLatestContactTimeByCompanyId($user->getClientId(), $company_id);
        $result['latest_contact_time'] = $latestContactTime ?? '';

        $result['total'] = $total;
        return $this->success($result);
    }

    /**
     * 查询客户是否加入质检池中(enable_flag)
     * https://xmkm.yuque.com/armee3/wg6g9f/ahbw733s8vmke7pi#vNel9
     * @param array $company_ids
     * @return false|string
     */
    public function actionCompaniesInQualityCheck(array $company_ids = [])
    {
        if (empty($company_ids)) {
            throw new RuntimeException("参数错误");
        }

        $user = \User::getLoginUser();
        $qcCompanyFinder = new AiQualityCheckCompanyList($user->getClientId() , $user->getUserId());
        $qcCompanyFinder->setCompanyId($company_ids);
        $data = $qcCompanyFinder->find();

        //提取每个客户的enable_flag
        $res = array_column($data, 'enable_flag', 'company_id');

        return $this->success($res);
    }

    /**
     * AI数据监测列表
     * @return void
     */
    public function actionDataMonitorList($display_type = '')
    {
        Helper::checkDataMonitorPermission(User::getLoginUser()->getClientId());
        $feedObj = new Feed(TodoConstant::OBJECT_TYPE_STATISTIC,  TodoConstant::TODO_TYPE_STATISTIC_INSIGHT);
        $feedObj->setShowReadSubFeed(true);
        $feedObj->setDisplayType($display_type);
        $feedObj->setShowAll(true);
        $data = $feedObj->readFeed(InsightAiAgent::DEFAULT_PAGE_SIZE, 0);
        $this->success($data);
    }

    /**
     * 获取AI数据监测未读数
     * @return void
     */
    public function actionGetUnReadMonitorCount()
    {
        Helper::checkDataMonitorPermission(User::getLoginUser()->getClientId());
        $feedObj = new Feed(TodoConstant::OBJECT_TYPE_STATISTIC,  TodoConstant::TODO_TYPE_STATISTIC_INSIGHT);
        $this->success(['total_count' => $feedObj->unReadSubFeedCount()]);
    }

    public function actionBillingInfo($scene_type)
    {
        $service = new \common\library\ai\service\AiAgentReadService();
        $result = $service->billingInfo($scene_type);
        $this->success($result);
    }

    /**
     * 查询AI背调反馈接口
     */
    public function actionFeedback(
        int $report_id = 0, // 背调报告ID，用于关联背调任务
    )
    {
        $this->validate([
            'report_id' => 'required|integer|min:1', // leads侧给定，沟通后确认从1开始
        ]);

        $user = User::getLoginUser();
        $result = Helper::getBackgroundCheckFeedback($user->getClientId(), $user->getUserId(), $report_id);

        $this->success($result);
    }

    public function actionAiSwarmList()
    {
        $user = User::getLoginUser();
        $clientId = $user->getClientId();
        $userId = $user->getUserId();

        if (!Helper::checkAiSwarmPrivileges($clientId)) {
            $this->success([]);
        }

        $api = \common\library\swarm\SwarmService::getSwarmApi(ItemSettingConstant::ITEM_TYPE_SWARM_AI, $clientId, $userId);
        $swarmList = $api->swarmList();
        $this->success(array_values($swarmList));
    }

    public function actionGetQcStickingPointList($company_id)
    {
        $this->validate([
            'company_id' => 'required|int'
        ]);

        $user = User::getLoginUser();
        $clientId = $user->getClientId();
        $userId = $user->getUserId();

        $list = new AiQualityCheckStickingPointList($clientId, $userId);
        $list->setCompanyId($company_id);
        $data = $list->find();

        $this->success($data);
    }


    /**
     * 获取分析结果
     * @return void
     */
    public function actionGetAsyncAnalysisResult($scene_type, $company_id = 0, $position = 0)
    {
        $service = new \common\library\ai\service\AiAgentReadService();
        $service->getAsyncAnalysisResult($scene_type, $company_id, $position);
    }

    public function actionGetKnowledgeDocumentExampleQuestions()
    {
        $user = User::getLoginUser();
        $exampleQuestionList = KnowledgeHelper::getExampleQuestions($user->getClientId());
        return $this->success($exampleQuestionList);
    }

    /**
     * Desc : ai年度账单接口
     * User : you name
     * Date : 2024-12-17 10:40
     */
    public function actionGetAiReport()
    {

        $userId = User::getLoginUser()->getUserId();
        $clientId = User::getLoginUser()->getClientId();
        $privilegeService = PrivilegeService::getInstance($clientId);
        $systemId = $privilegeService->getMainSystemId();

        if(!in_array($systemId,array_merge(AiAgentConstants::LITE_SYSTEM_ID_MAP,
            AiAgentConstants::SMART_SYSTEM_ID_MAP,AiAgentConstants::PRO_SYSTEM_ID_MAP))){
            $this->success([]);
        }

        $isManager = common\library\privilege_v3\Helper::hasAdminScope($clientId, $userId, 'crm.company.private.view');
        $hasAiFunctional = $privilegeService->hasFunctional('crm.functional.okki.ai');
        if(!$hasAiFunctional){
            $template = 3;
        }else if(in_array($systemId, array_merge(AiAgentConstants::SMART_SYSTEM_ID_MAP,AiAgentConstants::PRO_SYSTEM_ID_MAP)) && $isManager){
            $template = 2;
        } else{
            $template = 1;
        }

        $result = [];
        $result['template'] = $template;
        $reportData = new \common\library\setting\user\UserSetting($clientId, $userId, \common\library\setting\user\UserSetting::AI_ANNUAL_REPORT_SETTING);

        $result['data'] = $reportData->value ? json_decode($reportData->value) : [];
        if($template == 3){
            $dealSetting = \CustomerOptionService::getDealSetting($clientId);
            $result['deal_setting'] = $dealSetting;
        }

        $this->success($result);


    }


    public function actionQuestionRecommend($record_id, $params = [])
    {

        $user = User::getLoginUser();
        $record = new \common\library\ai_agent\record\AiServiceProcessRecord($record_id);
        if ($record->isNew()) {
            throw new \RuntimeException("record_id不正确");
        }

        $sceneType = $record->scene_type;
        $aiAgent = AiAgentFactory::createAgent($sceneType, $user->getClientId(), $user->getUserId());

        if (! method_exists($aiAgent,'questionRecommend')) {
            throw new \RuntimeException("该场景暂不支持推荐问题");
        }

        $questionList = $aiAgent->questionRecommend($record_id, $params);


        $this->success([
            'question_list' => \common\library\ai_agent\language\Helper::translateRecommendQuestionList($questionList)
        ]);
    }


    /**
     * 获取分析结果
     * @return void
     */
    public function actionSalesAssistantAnalysisResult($task_id = 0)
    {
        $service = new \common\library\ai\service\AiAgentReadService();
        $service->getSalesAssistantAnalysisResult($task_id);
    }

}
