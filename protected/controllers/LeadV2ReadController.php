<?php


use common\library\custom_field\CustomFieldService;
use common\library\custom_field\FieldList;
use common\library\custom_field\lead_field\LeadCustomerField;
use common\library\custom_field\lead_field\LeadField;
use common\library\customer\form\CustomerInputForm;
use common\library\customer\rule_config\RuleConfigConstants;
use common\library\customer_v3\company\list\CompanyList;
use common\library\inquiry\InquiryService;
use common\library\lead\Constant;
use common\library\lead_v3\form\LeadInputForm;
use common\library\privilege_v3\object_service\ObjPrivilegeService;
use common\library\privilege_v3\PrivilegeConstants;
use common\library\privilege_v3\PrivilegeFieldV2;
use common\library\setting\library\swarm\SwarmApi;
use common\library\setting\user\UserSetting;
use common\library\workflow\WorkflowConstant;

/**
 * Created by PhpStorm.
 * User: kevin.zhao
 * Date: 2022-06-06
 * Time: 16:49 PM
 */
class LeadV2ReadController extends Controller
{
    public function actionArchive($lead_id, $company_id = 0, $is_owner = 1)
    {
        $this->validate(['lead_id' => 'required|numeric']);

        $user = User::getLoginUser();
        $clientId = $user->getClientId();
        $userId = $user->getUserId();

        $lead = new \common\library\lead\Lead($clientId, $lead_id);
        $lead->setOperatorUserId($userId);

        if (!$lead->isExist()) {
            if ($lead->isArchive()) {
                throw new RuntimeException('lead ' . $lead_id . \Yii::t('lead', 'Archived'));
            }
            throw new RuntimeException('lead ' . $lead_id . \Yii::t('lead', 'Does not exist'));
        } elseif ($is_owner == 1) {
            if (!$lead->checkOwner($user, true)) {
                throw new RuntimeException('lead ' . $lead_id . \Yii::t('lead', 'Not your own leads'));
            }
        }

        $lead->getFormatter()->setShowCompany(true, $company_id);
        $result = $lead->getAttributes();
        $result['lead_id'] = $lead->lead_id;

        $fieldFunctionalId = ($company_id > 0) ? PrivilegeConstants::FUNCTIONAL_CUSTOMER : PrivilegeConstants::FUNCTIONAL_COMPANY_POOL;
        
        $scopeUserIds = null;
        $privilegeScene = PrivilegeFieldV2::SCENE_OF_CREATE;
        if ($company_id > 0){
            $company = new \common\library\customer_v3\company\orm\Company($user->getClientId(), $company_id);
            $scopeUserIds = \common\library\util\PgsqlUtil::arrayOrTrimArray($company->scope_user_ids);
            $privilegeScene = PrivilegeFieldV2::SCENE_OF_UPDATE;
        }
        $form = (new CustomerInputForm($user->getClientId(), $user->getUserId(), $fieldFunctionalId, 'submit_form', privilegeScene: $privilegeScene, scopeUserIds: $scopeUserIds));

        $companyData = null;
        $customers = $result['customers'] ?? null;
        foreach ($result['company'] ?? [] as $group) {
            $fields = $group['fields'];
            foreach ($fields as $field) {
                $companyData[$field['id']] = $field['value'];
            }
        }

        $formData = $form->getInputFields(['company' => $companyData, 'customers' => $customers]);

        $result['company'] = $formData['company'];
        $result['customers'] = $formData['customers'];
        $this->success($result);
    }

    /**
     * @param $type
     * @return false|string
     */
    public function actionFullFieldList($type, $scene = '') {

        $user = User::getLoginUser();

        $ruleType = $type == \Constants::TYPE_LEAD ? RuleConfigConstants::RULE_CONFIG_TYPE_LEAD_FULL_FIELD : RuleConfigConstants::RULE_CONFIG_TYPE_LEAD_FULL_FIELD_PUBLIC;

        $map = [
            'lead_export' => RuleConfigConstants::RULE_CONFIG_TYPE_LEAD_EXPORT_FIELD,
        ];
		if (!empty($scene)) {
			$ruleType = $map[$scene] ?? $ruleType;
		}
        
        $privilegeScene = $scene == 'lead_export' ? ObjPrivilegeService::OBJECT_SCENE_EXPORT : ObjPrivilegeService::OBJECT_SCENE_VIEW;
        
        $result = \common\library\lead\Helper::getWrapRelateConfig($user->getClientId(), $user->getUserId(), $ruleType, [\Constants::TYPE_LEAD, \Constants::TYPE_LEAD_CUSTOMER], $privilegeScene);

        return $this->success($result);
    }


    public function actionFieldRuleConfig($type = \common\library\customer\rule_config\RuleConfigConstants::RULE_CONFIG_TYPE_LEAD_LIST_NORMAL) {

        $user = User::getLoginUser();

        $filterFieldList = \common\library\lead\Helper::getWrapRelateConfig($user->getClientId(), $user->getUserId(), $type, [\Constants::TYPE_LEAD, \Constants::TYPE_LEAD_CUSTOMER]);

        return $this->success($filterFieldList);
    }

    public function actionFieldOperatorConfig() {

        $result = [
            'field_type_operator_map' => [],
            'operators'               => [],
            'search_fields'           => [],
        ];

        foreach (\common\library\customer\Helper::getSearchFieldTypeOperatorMap() as $fieldType => $operators) {
            $result['field_type_operator_map'][] = [
                'field_type' => $fieldType,
                'operators'  => $operators,
            ];
        }

        $result['operators'] = common\library\customer\Helper::getOperatorsList();

        foreach ($result as &$list) {
            $list = array_values($list);
        }

        return $this->success($result);
    }

    /**
     * 普通筛选字段: lead.common.search.filter
     * 高级筛选字段: lead.advanced.search.filter
     * @param $key
     */
    public function actionSearchFieldList($key)
    {
        $user = User::getLoginUser();
        $data = [];
        $fieldsMap = [];
        $setting = (new UserSetting($user->getClientId(), $user->getUserId(), $key))->getValue();

        $ruleConfigType = $key == UserSetting::LEAD_COMMON_SEARCH_FILTER ? RuleConfigConstants::RULE_CONFIG_TYPE_LEAD_LIST_NORMAL : RuleConfigConstants::RULE_CONFIG_TYPE_LEAD_LIST_ADVANCED;
        if (!empty($setting) && $groups = ArrayUtil::groupBy($setting, 'type', 'id')) {
            foreach ($groups as $module => $fields) {
                $fieldList = (new FieldList($user->getClientId()));
                $fieldList->setType($module);
                $fieldList->setFields(['id', 'base', 'type', 'field_type', 'name', 'ext_info']);
                $fieldList->setId($fields);
                $fieldList->setIsList(0);
                $fieldList->setDisableFlag(0);
                $fieldList->setScene(PrivilegeFieldV2::SCENE_OF_VIEW);
                $fieldList->setPrivilegeInfo($user->getUserId(), PrivilegeConstants::FUNCTIONAL_LEAD);
                $fieldList->setExcludeId(RuleConfigConstants::FILTER_FIELDS_IGNORE[$ruleConfigType][$module] ?? []);
                $fieldList->addExtra(array_merge((SwarmApi::getExtraField()[$module] ?? []), CustomFieldService::getExtraFieldList($module, WorkflowConstant::RULE_TYPE_SWARM, $user->getClientId()) ?? []));
                $list = array_column($fieldList->find(), null, 'id');

                foreach ($list as $item) {
                    switch ($item['id']) {
                        case 'ai_tags':
                            $item['field_type'] = \common\library\field\Constant::FIELD_TYPE_MULTIPLE_SELECT;
                            break;
                        case 'star':
                        case 'archive_type':
                        $item['field_type'] = \common\library\field\Constant::FIELD_TYPE_SELECT;
                            break;
                    }
                    $item['type'] = $module;
                    $item['field_type'] = intval($item['field_type']);
                    $fieldsMap[$module][$item['id']] = $item;
                }
            }

            foreach ($setting as $key => $item) {
                $item['type'] = !empty($item['type']) ? $item['type'] : \Constants::TYPE_LEAD;
                if (isset($fieldsMap[$item['type']][$item['id']])) {
                    $field = $fieldsMap[$item['type']][$item['id']];
                    if (!empty($field['disable_flag'])) {
                        continue;
                    }
                    $data[$key] = array_replace($field, [
                        'id' => $field['id'],
                        'name' => $field['name'],
                        'base' => $field['base'],
                        'type' => $field['type'],
                        'field_type' => $field['field_type'],
                        'ext_info' => $field['ext_info'] ?? [],
                        'last_select' => $item['last_select'] ?? 0
                    ]);
                    
                    

                    if (isset($item['value_type'])) {
                        $data[$key]['value_type'] = $item['value_type'];
                    }
                }
            }
        }

        return $this->success(array_values($data));
    }


    public function actionList(
        $show_all = 0,
        $search_model = Constants::SEARCH_MODEL_SEARCHER,
        $keyword = '',
        $search_field = '',
        $pin = 0,
        array $country = [],
        $province = '',
        $city = '',
        $biz_type = '',
        $group_id = '',
        $archive_start_date = '',
        $archive_end_date = '',
        $follow_up_start_date = '',
        $follow_up_end_date = '',
        $order_start_date = '',
        $order_end_date = '',
        array $user_id = [],
        array $origin = [],
        array $tags = [],
        array $user_num = [1, 2],
        array $star = [],
        array $category_ids = [],
        $curPage = 1,
        $pageSize = 20,
        $sort_field = 'renew_time',
        $sort_type = 'desc',
        $params_info = 0,
        $recent_select_flag = 0,
        $will_public = 0,
        $last_owner = '',
        $lead_field = '',
        $customer_field = '',
        $tag_match_mode = CompanyList::TAG_MATCH_MODE_SINGLE,
        $compare_day = 0,
        $compare_day_op = CompanyList::LESS_THAN,
        $acquired_company_day = 0,
        array $ai_tags = [],
        $ai_tag_match_mode = CompanyList::TAG_MATCH_MODE_SINGLE,
        $ai_status_id = null,
        $report_item_unique_key = '',
        $get_contract_list = 0,
        $has_customer_mail = null,
        $site_id=0,
        $ads_account_id=0,
        $archive_type = null,
        array $status_id = [],
        $status_edit_type = null,
        $create_start_date = '',
        $create_end_date = '',
        $read_flag = null,
        array $create_user = [],
        array $last_edit_user = [],
        array $last_owner_ids = [],
        $edit_begin_time = '',
        $edit_end_time = '',
        $private_begin_time = '',
        $private_end_time = '',
        $public_begin_time = '',
        $public_end_time = '',
        $release_count = null,
        array $lead_id = [],
        $extra_ai_tag = 0,
        array $store_id = [],
        $duplicate_flag = null,
        $next_follow_up_begin_time = '',
        $next_follow_up_end_time = '',
        array $growth_level = [],
        array $assess = [],
        array $pin_user_list = [],
        $edm_begin_time = '',
        $edm_end_time = '',
        array $origin_list = [],
        array $invalid_reason_ids = [],
        array $filters = [],
        array $time_filters = [],
        $criteria_type = 1,
        $criteria = '',
        $filter_disable_fields = 0,
        $high_light_flag = 0,
        $layer_id = 0,
        $open_edm_flag = null,
        $open_mail_flag = null,
        array $auto_filled = [],
        array $latest_inquiry_status = [],
        $only_editable = 0,
    )
    {
        $this->validate([
            'curPage'        => 'integer',
            'pageSize'       => 'integer',
            'sort_field'     => 'regex:/^\w{2,32}$/',
            'sort_type'      => 'string|in:asc,desc',
            'keyword'        => 'string|max:1000',
            'growth_level'   => 'array',
            'growth_level.*' => 'integer',
            'assess'         => 'array',
            'assess.*'       => 'numeric',
            'pin_user_list' => 'array',
            'pin_user_list.*' => 'numeric',
            'country' => 'array',
            'user_id' => 'array',
            'origin' => 'array',
            'tar' => 'array',
            'user_num' => 'array',
            'user_num.*' => 'numeric',
            'star' => 'array',
            'category_ids' => 'array',
            'params_info' => 'integer',
            'recent_select_flag' => 'numeric',
            'will_public' => 'integer',
            'tag_match_mode' => 'integer',
            'compare_day' => 'integer',
            'compare_day_op' => 'numeric',
            'acquired_company_day' => 'integer',
            'ai_tags' => 'array',
            'ai_tag_match_mode' => 'integer',
            'get_contract_list' => 'integer',
            'site_id' => 'integer',
            'ads_account_id' => 'integer',
            'archive_type' => 'integer',
            'create_user' => 'array',
            'create_user.*' => 'integer',
            'last_edit_user' => 'array',
            'last_edit_user.*' => 'integer',
            'last_owner_ids' => 'array',
            'last_owner_ids.*' => 'integer',
            'lead_id' => 'array',
            'lead_id.*' => 'integer',
            'extra_ai_tag' => 'integer',
            'store_id' => 'array',
            'store_id.*' => 'integer',
            'invalid_reason_ids' => 'array',
            'invalid_reason_ids.*' => 'integer',
            'high_light_flag' => 'integer',
            'layer_id' => 'integer|min:0',
            'auto_filled' => 'array',
            'auto_filled.*' => 'integer',
            'latest_inquiry_status' => 'array',
            'only_editable' => 'integer|in:0,1',
        ]);

        !$curPage && $curPage = 1; // 空字符串兼容


        $user = User::getLoginUser();
        $clientId = $user->getClientId();
        $opUserId = $user->getUserId();
        
        foreach (($filters ?? []) as $k =>  $filter) {

            if (!isset($filter['field_type']) || !isset($filter['operator'])) {

                throw new RuntimeException('WRONG SELECT PARAM1');
            }

            if (str_starts_with($filter['field'], 'customer_list')) {

                $filters[$k]['refer_type'] = \Constants::TYPE_LEAD_CUSTOMER;
            }

            if (in_array($filter['field_type'], [\common\library\field\Constant::FIELD_TYPE_DATE, \common\library\field\Constant::FIELD_TYPE_DATETIME])) {

                if (!in_array($filter['operator'], [WorkflowConstant::FILTER_OPERATOR_EQUAL, WorkflowConstant::FILTER_OPERATOR_NOT_EQUAL]) && is_array($filter['value'] ?? '')) {

                    throw new RuntimeException('WRONG SELECT PARAM2');
                }
                if (!in_array($filter['operator'], [WorkflowConstant::FILTER_OPERATOR_IS_NULL, WorkflowConstant::FILTER_OPERATOR_NOT_NULL]) && empty($filter['value'])) {

                    throw new RuntimeException('WRONG SELECT PARAM3');
                }
            }
        }

        \LogUtil::info("leadList params:" . json_encode($_REQUEST));


        //检查公海权限
        if ($isPublicPool = count(array_diff($user_num, [0])) == 0) {
            $needPrivilegeName = PrivilegeConstants::PRIVILEGE_CRM_LEAD_POOL_VIEW;
        } else {
            $needPrivilegeName = PrivilegeConstants::PRIVILEGE_CRM_LEAD_VIEW;
        }
        \common\library\privilege_v3\Helper::checkPermission($clientId, $opUserId, $needPrivilegeName);

        $params = [];
        $method = new \ReflectionMethod(self::class, 'actionList');
        foreach ($method->getParameters() as $parameter) {
            $params[$parameter->name] = ${$parameter->name} ?? null;
        }

        $list = new \common\library\lead_v3\LeadList($user->getUserId());
        $list->paramsMapping($params);
        $list->getFormatter()->setAssessFlag(true);
        $list->getFormatter()->setFilterDisableFields($filter_disable_fields);
        // 开启es缓存
        $list->setEnableListSearch(true);

        if (!empty($keyword)  && !empty($sort_field) ) {

            $list->setSortBySearchScore(false);
        }

        // 强制显示来源详情
        $list->getFormatter()->setShowSourceDetail(true);

        // EDM 有个筛选整合了进来，只返回邮箱联系人列表（1：邮箱，2：whatsapp）
        if ($get_contract_list) {
            $list->setFields(['lead_id', 'name']);
            $companyData = $list->find();

            $leadIds = array_column($companyData, 'lead_id');
            $list = new \common\library\lead\LeadCustomerList($user->getClientId());
            $list->setLeadId($leadIds);
            $list->getFormatter()->setUserId($user->getUserId());
            $list->getFormatter()->setFilterDisableFields($filter_disable_fields);

            if($get_contract_list == 2){ //1：邮箱，2：whatsapp
                $list->getFormatter()->snsInfoSetting();
                $list->setContact(['type'=>'whatsapp']);
                $data = $list->find();
            }else {
                $list->setFilterEmptyEmail(true);
                $list->getFormatter()->edmAddressInfoSetting();
                $data = $list->find();

                $validator = new CEmailValidator();
                $pattern = $validator->pattern;

                $tmp = [];
                foreach ($data as $i => $item) {
                    if (!preg_match($pattern, $item['email']) || in_array($item['email'], $tmp)) {
                        unset($data[$i]);
                        continue;
                    }
                    $tmp[] = $item['email'];
                }
            }

            $data = array_values($data);
            $total = count($data);
            return $this->success(compact('data', 'total'));
        }

        $list->setOffset(($curPage - 1) * $pageSize);
        $list->setLimit($pageSize);
        $list->getFormatter()->setRenderList();
        $list->getFormatter()->setShowOperatePrivilege(true);
        $list->getFormatter()->setListFormatterPrivilege(true, true, \common\library\privilege_v3\PrivilegeFieldV2::SCENE_OF_VIEW);
        $result = [
            'list'        => $list->find(),
            'count'       => $list->count(),
            'search_info' => method_exists($list, 'getSearchInfo') ? $list->getSearchInfo() : [],
        ];

        if ($params_info) {
            $result['params'] = \common\library\lead\Helper::getParamsInfo($user);
        }

        $result['privilege_field_stats'] = $list->getFormatter()->getPrivilegeFieldStats();

        return $this->success($result);
    }

    public function actionGetLeadStatistic(
        $show_all = 0,
        $search_model = Constants::SEARCH_MODEL_SEARCHER,
        $keyword = '',
        $search_field = '',
        $pin = 0,
        array $country = [],
        $province = '',
        $city = '',
        $biz_type = '',
        $group_id = '',
        $archive_start_date = '',
        $archive_end_date = '',
        $follow_up_start_date = '',
        $follow_up_end_date = '',
        $order_start_date = '',
        $order_end_date = '',
        array $user_id = [],
        array $origin = [],
        array $tags = [],
        array $user_num = [1, 2],
        array $star = [],
        array $category_ids = [],
        $curPage = 1,
        $pageSize = 20,
        $sort_field = 'renew_time',
        $sort_type = 'desc',
        $recent_select_flag = 0,
        $will_public = 0,
        $last_owner = '',
        $lead_field = '',
        $customer_field = '',
        $tag_match_mode = CompanyList::TAG_MATCH_MODE_SINGLE,
        $compare_day = 0,
        $compare_day_op = CompanyList::LESS_THAN,
        $acquired_company_day = 0,
        array $ai_tags = [],
        $ai_tag_match_mode = CompanyList::TAG_MATCH_MODE_SINGLE,
        $ai_status_id = null,
        $report_item_unique_key = '',
        $has_customer_mail = null,
        $site_id=0,
        $ads_account_id=0,
        $archive_type = null,
        array $status_id = [],
        $status_edit_type = null,
        $create_start_date = '',
        $create_end_date = '',
        $read_flag = null,
        array $create_user = [],
        array $last_edit_user = [],
        array $last_owner_ids = [],
        $edit_begin_time = '',
        $edit_end_time = '',
        $private_begin_time = '',
        $private_end_time = '',
        $public_begin_time = '',
        $public_end_time = '',
        $release_count = null,
        array $lead_id = [],
        $count_type = 'status',
        array $origin_list = null,
        array $filters = [],
        array $time_filters = [],
        $criteria_type = 1,
        $criteria = '',
    )
    {
        $this->validate([
            'curPage'        => 'integer',
            'pageSize'       => 'integer',
            'sort_field'     => 'regex:/^\w{2,32}$/',
            'sort_type'      => 'string|in:asc,desc',
            'keyword'        => 'string|max:1000',
            'country' => 'array',
            'user_id' => 'array',
            'origin' => 'array',
            'user_num' => 'array',
            'user_num.*' => 'numeric',
            'star' => 'array',
            'category_ids' => 'array',
            'recent_select_flag' => 'numeric',
            'will_public' => 'integer',
            'tag_match_mode' => 'integer',
            'compare_day' => 'integer',
            'compare_day_op' => 'numeric',
            'acquired_company_day' => 'integer',
            'ai_tags' => 'array',
            'ai_tag_match_mode' => 'integer',
            'site_id' => 'integer',
            'ads_account_id' => 'integer',
            'archive_type' => 'integer',
            'create_user' => 'array',
            'create_user.*' => 'integer',
            'last_edit_user' => 'array',
            'last_edit_user.*' => 'integer',
            'last_owner_ids' => 'array',
            'last_owner_ids.*' => 'integer',
            'lead_id' => 'array',
            'lead_id.*' => 'integer',
        ]);

        $user = User::getLoginUser();
        $clientId = $user->getClientId();
        $opUserId = $user->getUserId();

        //检查公海权限
        if ($isPublicPool = count(array_diff($user_num, [0])) == 0) {
            $needPrivilegeName = PrivilegeConstants::PRIVILEGE_CRM_LEAD_POOL_VIEW;
        } else {
            $needPrivilegeName = PrivilegeConstants::PRIVILEGE_CRM_LEAD_VIEW;
        }
        \common\library\privilege_v3\Helper::checkPermission($clientId, $opUserId, $needPrivilegeName);

        $params = [];
        $sqlParams = [];
        $method = new \ReflectionMethod(self::class, 'actionGetLeadStatistic');
        foreach ($method->getParameters() as $parameter) {
            $params[$parameter->name] = ${$parameter->name} ?? null;
        }

        if ($count_type === 'completed_status') {
            $params['status_id'] = [\common\library\setting\library\status\Status::SYS_STATUS_INVALID,\common\library\setting\library\status\Status::SYS_STATUS_CONVERSION];
        }

        $list = new \common\library\lead_v3\LeadList($user->getUserId());
        $list->paramsMapping($params);
        list($where, $sqlParams) = $list->buildParams();

        $db = PgActiveRecord::getDbByClientId($clientId);
        switch ($count_type) {
            case "status":
            case "completed_status":
                $statisticStatusCountSql = "select status,count(1) as count from tbl_lead where {$where} group by status";
                $statusCountInfo = $db->createCommand($statisticStatusCountSql)->queryAll(true, $sqlParams);
                $data = array_column($statusCountInfo, 'count', 'status');
                break;
            case "origin":
                $statisticOriginCountSql = "select origin,count(1) as count from tbl_lead where {$where} group by origin";
                $originCountInfo = $db->createCommand($statisticOriginCountSql)->queryAll(true, $sqlParams);
                $data = array_column($originCountInfo, 'count', 'origin');
                break;
            case "origin_list":
                $statisticOriginCountSql = "select UNNEST(origin_list) AS origin_list,count(1) as count from tbl_lead where {$where} group by UNNEST(origin_list)";
                $originCountInfo = $db->createCommand($statisticOriginCountSql)->queryAll(true, $sqlParams);
                $data = array_column($originCountInfo, 'count', 'origin_list');
                break;

            case "ai_tags":
                $sumSql = ' SUM (open_edm_flag) AS "1",SUM (reply_edm_flag) AS "2",SUM (open_edm_url_flag) AS "3",SUM (open_mail_flag) AS "4",SUM (reply_mail_flag) AS "5" ';
                $aiTagsSql = ' and (open_edm_flag=1 OR reply_edm_flag=1 OR open_edm_url_flag=1 OR open_mail_flag=1 OR reply_mail_flag=1) ';
                $statisticAiTagsCountSql = "SELECT {$sumSql} FROM tbl_lead WHERE {$where} {$aiTagsSql}";
                $data = $db->createCommand($statisticAiTagsCountSql)->queryRow(true, $sqlParams);
                break;
            default:
                $data = [];
                break;
        }
        $params['read_flag'] = 0;
        $list->paramsMapping($params);
        $data['unread'] = $list->count();

        return $this->success($data);
    }

    public function actionFormFieldList(
        $lead_id = 0,
        array $customer_id = [],
        $company_hash_id = '',
        $company_hash_origin = '',
        $archive_flag = 1,
        $type = 'all',
        $scene = 'submit_form',
        $mail_id = null,
        $tab = '',
        $privilege_scene = ObjPrivilegeService::OBJECT_SCENE_VIEW,
    )
    {


        $this->validate([
            'company_id'          => 'numeric',
            'customer_id'         => 'array',
            'customer_id.*'       => 'numeric',
            'company_hash_id'     => 'string',
            'company_hash_origin' => 'string',
            'archive_flag'        => 'numeric',
            'type'                => 'string',
            'scene'               => 'string',
            'lead_id'             => 'numeric',
            'opportunity_id'      => 'numeric',
            'advice_id'           => 'numeric',
            'mail_id'             => 'numeric',
            'need_opportunity'    => 'numeric',
        ]);

        //参数校验
        if (is_null($lead_id) && is_null($customer_id) && empty($company_hash_id) && is_null($mail_id)) {
            throw new RuntimeException("请填入正确的参数");
        }

        $fieldFunctionalId = ($archive_flag) ? PrivilegeConstants::FUNCTIONAL_LEAD : PrivilegeConstants::FUNCTIONAL_LEAD_POOL;
        $user = User::getLoginUser();
        
        $scopeUserIds = null;
        if ($lead_id > 0){
            $lead = new \common\library\lead\Lead($user->getClientId(), $lead_id);
            $scopeUserIds = \common\library\util\PgsqlUtil::arrayOrTrimArray($lead->scope_user_ids);
        }

        // NOTE: Leads查看数据不应该受权限限制，跳过权限校验
        $skipPrivilege = !empty($company_hash_id) && $privilege_scene == ObjPrivilegeService::OBJECT_SCENE_VIEW;

        $form = (new LeadInputForm($user->getClientId(), $user->getUserId(), $fieldFunctionalId, $scene, privilegeScene: $privilege_scene, scopeUserIds: $scopeUserIds, ignorePrivilege: $skipPrivilege));

        $form->setTab($tab);
        $leadData = $customers = null;

        if (!empty($lead_id)) {
            if ($type == 'all' || $type == 'lead') {
                $lead = new \common\library\lead\Lead($user->getClientId(), $lead_id);
                $lead->setOperatorUserId($user->getUserId());
                $lead->getFormatter()->setFieldPrivilegeFormatterFlag(true);

                if (!$lead->isExist()) {
                    throw new RuntimeException(\Yii::t('customer', 'Company does not exist'));
                }

//                if ($scene == 'info') {
//                    $lead->getFormatter()->listCompany();
//                    $lead->getFormatter()->setShowAlibabaChatInfo(true);
//                    $lead->getFormatter()->setShowAlibabaStoreInfo(true);
//                    $lead->getFormatter()->setShowSourceDetail(true);
//                    $lead->getFormatter()->setShowSwarmInfo(true);
//                    $lead->getFormatter()->setAssessFlag(true);
//                    $lead->getFormatter()->setShowOpportunityStatisticsData(true);
//                    $lead->getFormatter()->setShowOpportunityCashCollection(true);
//                    $lead->getFormatter()->setShowOrderCashCollectionData(true);
//                }

                $leadData = $lead->getAttributes();
            }

            if ($type == 'all' || $type == 'customers') {
                $list = new \common\library\lead\LeadCustomerList($user->getClientId());
                $customer_id && $list->setCustomerId($customer_id);
                $list->setLeadId($lead_id);
                $list->setIsArchive(1);
                $list->setOrder('asc');
                $list->setOrderBy('order_rank');
                $customers = $list->find();
            }
        } elseif (!empty($customer_id)) {
            if ($type == 'all' || $type == 'customers') {
                $list = new \common\library\lead\LeadCustomerList($user->getClientId());
                $list->setCustomerId($customer_id);
                $list->setIsArchive(1);
                $list->setOrder('asc');
                $list->setOrderBy('order_rank');
                $customers = $list->find();
            }
        } elseif (!empty($company_hash_id)) {
            if ($company_hash_origin == 'dx') {
                list($companyField, $customerFields, $socialLinks) = \common\library\customer\Helper::getDiscoveryCompanyFiled($user->getClientId(), $user->getUserId(), $company_hash_id, new LeadField($user->getClientId()), new LeadCustomerField($user->getClientId()));
            } elseif ($company_hash_origin == 'ai_recommend') {
                list($companyField, $customerFields, $socialLinks) = \common\library\ai\Helper::getRecommendArchiveDataField($user->getClientId(), $user->getUserId(), $company_hash_id, Constants::TYPE_LEAD);
            }

            if (isset($socialLinks) && !empty($socialLinks)) {
                $leadData['contact'] = $socialLinks;
            }

            foreach ($companyField ?? [] as $group) {
                $fields = $group['fields'];
                foreach ($fields as $field) {
                    $leadData[$field['id']] = $field['value'];
                }
            }

            foreach ($customerFields ?? [] as $groupId => $customerField) {
                $fields = $customerField['fields'];
                foreach ($fields as $field) {
                    $customers[$groupId][$field['id']] = $field['value'];
                }
            }
        } elseif(!empty($mail_id)) {
            $mail = new \common\library\mail\Mail($mail_id);
            $extract = new \common\library\ai\classify\extract\NormalExtract($mail);
            $email =  $mail->isSendType() ? $mail->getAllReceiverEmails() : $mail->getSenderEmail();
            $mainEmail = reset($email);

            // 阿里巴巴平台询盘邮件特殊处理，不查询客户资料
            if (! in_array($mainEmail, \common\library\mail\Mail::INQUIRY_ALIBABA_EMAIL) && $mail->isReceiverType()) {
                $option = [
                    'main_email' => $mainEmail,
                    'emails' => $email
                ];
                $adviceData = $extract->extract($option);
                if (isset($adviceData['customer'])) {
                    $leadData = $adviceData['company'];
                    $customerData = $mail->isReceiverType() ? $adviceData['customer'][$mainEmail] : null;
                }
            }

            $leadField = (new \common\library\custom_field\lead_field\LeadField($user->getClientId()))->setFieldUserId($user->getUserId())->setFieldFunctionalId($fieldFunctionalId)->format($leadData ?? []);
            $customerFields = (new \common\library\custom_field\lead_field\LeadCustomerField($user->getClientId()))->setFieldUserId($user->getUserId())->setFieldFunctionalId($fieldFunctionalId)->format($customerData ?? []);

            foreach ($leadField ?? [] as $group) {
                $fields = $group['fields'];
                foreach ($fields as $field) {
                    $leadData[$field['id']] = $field['value'];
                }
            }

            $customersItem = [];
            foreach ($customerFields as $fieldKey => $fieldItem) {
                if ($fieldKey == 'fields') {
                    foreach ($fieldItem as $field) {
                        if (is_numeric($field['id'])) {
                            $customersItem['external_field_data'] = array_merge($customersItem['external_field_data']??[],[
                                $field['id'] => $field['value']
                            ]);
                        } else {
                            $customersItem[$field['id']] = $field['value'];
                        }
                    }
                } else {
                    $customersItem[$fieldKey] = $fieldItem;
                }
            }
            $customers[] = $customersItem;
        }
        
        // NOTE: 增加type处理添加联系人的字段权限问题
        if ($type == 'add_customer') {
            $customers = null;
        }

        if ($type == 'lead') {
            $list = $form->getInputFields(['lead' => $leadData]);
        } elseif ($type == 'customers') {
            $list = $form->getInputFields(['customers' => $customers]);
        } else {
            $list = $form->getInputFields(['lead' => $leadData, 'customers' => $customers]);
        }


        if ( $lead_id > 0 &&  isset($lead) && $lead->isPool()){
            \common\library\privilege_v3\Helper::checkPrivilege($user->getClientId(), $user->getUserId(), \common\library\privilege_v3\PrivilegeConstants::PRIVILEGE_CRM_LEAD_POOL_VIEW);
        }

        $this->success($list);
    }

    public function actionIsPin($lead_id)
    {
        $this->validate([
            'lead_id' => 'required|numeric'
        ]);

        $user = User::getLoginUser();
        $isPin = Pin::isPin($user->getUserId(),Pin::TYPE_LEAD,$lead_id);

        return $this->success([
            'is_pin' => (int)$isPin,
            'lead_id' => $lead_id
        ]);
    }

	//取合并线索的字段 改造成批量
	/**
	 * @throws ProcessException
	 */
	public function actionLeadFields(array $lead_ids, $scene = ObjPrivilegeService::OBJECT_SCENE_VIEW)
	{

		$this->validate([
			'lead_ids' => 'required|array',
			'lead_ids.*' => 'required|numeric',

		]);

		$user = User::getLoginUser();
		$userId = $user->getUserId();

		$leadList = new \common\library\lead_v3\LeadList($userId);

		$params['lead_id'] = $lead_ids;

		$leadList->paramsMapping($params);
		$leadList->getFormatter()->setAssessFlag(true);
		$leadList->getFormatter()->setRenderDetailWithCustomers(true);
		$leadList->getFormatter()->setShowCaptureCard(true);
		$leadList->getFormatter()->setShowAlibabaInfoFlag(true);
		$leadList->setShowAll(true);
		$leadList->setSkipFilterStatus(true);
// 强制显示来源详情
		$leadList->getFormatter()->setShowSourceDetail(true);
        $leadList->getFormatter()->setListFormatterPrivilege(true, true, $scene);
		$leadList->getFormatter()->setShowOperatePrivilege(true);
		//$leadList->getFormatter()->setLeadFieldFormatter();

		$result = $leadList->find();

		$this->success($result);
	}


    /**
     * 线索自动创建记录列表
     * @param $name
     * @param array $follower_user_id
     * @param $email
     * @param array $origin
     * @param $create_time_start
     * @param $create_time_end
     * @param array $sync_status
     * @param $curPage
     * @param $pageSize
     * @return void
     */
    public function actionLeadAutoCreateRecordList( $keyword="", array $follower_user_id=[], array $origin = [], array $create_time = [], array $sync_status = [], array $sync_company_status = [], array $sync_company_time = [], $time_field='', array $time_value = [], $sort_field = "sync_time", $sort_type="desc", $curPage = 1, $pageSize = 20)
    {
        $this->validate([
            'curPage' => 'integer',
            'pageSize' => 'integer',
            'follower_user_id' => 'array',
            'follower_user_id.*' => 'integer',
            'origin' => 'array',
            'origin.*' => 'integer',
            'sync_status' => 'array',
            'sync_status.*' => 'integer',
        ]);
        $user = User::getLoginUser();
        $list = new \common\library\lead_v3\LeadAutoCreteRecordList($user->getUserId());
        $params = [];
        $method = new \ReflectionMethod(self::class, 'actionLeadAutoCreateRecordList');
        foreach ($method->getParameters() as $parameter) {
            $params[$parameter->name] = ${$parameter->name} ?? null;
        }
        $list->paramsMapping($params);
        $list->setQueryFields(["*"]);
        $list->setOrderBy($sort_field);
        $list->setOrder($sort_type);
        $data = $list->find();
        $total = $list->count();
        $this->success(["list"=>$data, "total"=>$total]);

    }

    /**
     * 线索自动创建记录列表header
     * @return void
     */
    public function actionLeadAutoCreateRecordListHeader(): void
    {
        $data = [
            ["name" => \Yii::t('lead', '线索名称'), "id" => "name", "sortable" => 0, "field_type" => CustomFieldService::FIELD_TYPE_TEXT],
            ["name" => \Yii::t('lead', '关联业务员'), "id" => "follower_info", "sortable" => 0, "field_type" => CustomFieldService::FIELD_TYPE_TEXT],
            ["name" => \Yii::t('lead', '公司邮箱'), "id" => "email", "sortable" => 0, "field_type" => CustomFieldService::FIELD_TYPE_TEXT],
            ["name" => \Yii::t('lead', '线索来源'), "id" => "origin_name", "sortable" => 0, "field_type" => CustomFieldService::FIELD_TYPE_TEXT],
            ["name" => \Yii::t('lead', '来源详情'), "id" => "store_id", "sortable" => 0, "field_type" => CustomFieldService::FIELD_TYPE_TEXT],
            ["name" => \Yii::t('lead', '线索类型'), "id" => "lead_type_text", "sortable" => 0, "field_type" => CustomFieldService::FIELD_TYPE_TEXT],
            ["name" => \Yii::t('lead', '自动创建线索状态'), "id" => "sync_status", "sortable" => 0, "field_type" => CustomFieldService::FIELD_TYPE_TEXT],
            ["name" => \Yii::t('lead', '自动化创建线索时间'), "id" => "sync_time", "sortable" => 1, "field_type" => CustomFieldService::FIELD_TYPE_DATETIME],
            ["name" => \Yii::t('lead', '自动创建客户状态'), "id" => "sync_company_status", "sortable" => 0, "field_type" => CustomFieldService::FIELD_TYPE_TEXT],
            ["name" => \Yii::t('lead', '自动化创建客户时间'), "id" => "sync_company_time", "sortable" => 1, "field_type" => CustomFieldService::FIELD_TYPE_DATETIME],
            ["name" => \Yii::t('lead', '线索跟进人'), "id" => "lead_owner_info", "sortable" => 1, "field_type" => CustomFieldService::FIELD_TYPE_TEXT],
            ["name" => \Yii::t('lead', '客户跟进人'), "id" => "company_owner_info", "sortable" => 1, "field_type" => CustomFieldService::FIELD_TYPE_TEXT],
            ["name" => \Yii::t('lead', '创建人'), "id" => "create_user", "sortable" => 0, "field_type" => CustomFieldService::FIELD_TYPE_TEXT],
        ];
        $this->success($data);
    }


	/**
	 * @throws ProcessException
	 * @throws CDbException
	 */
	public function actionGetLeadAllStatistic(
		$show_all = 0,
		$search_model = Constants::SEARCH_MODEL_SEARCHER,
		$keyword = '',
		$search_field = '',
		$pin = 0,
		array $country = [],
		$province = '',
		$city = '',
		$biz_type = '',
		$group_id = '',
		$archive_start_date = '',
		$archive_end_date = '',
		$follow_up_start_date = '',
		$follow_up_end_date = '',
		$order_start_date = '',
		$order_end_date = '',
		array $user_id = [],
		array $origin = [],
		array $tags = [],
		array $user_num = [1, 2],
		array $star = [],
		array $category_ids = [],
		$curPage = 1,
		$pageSize = 20,
		$sort_field = 'renew_time',
		$sort_type = 'desc',
		$recent_select_flag = 0,
		$will_public = 0,
		$last_owner = '',
		$lead_field = '',
		$customer_field = '',
		$tag_match_mode = CompanyList::TAG_MATCH_MODE_SINGLE,
		$compare_day = 0,
		$compare_day_op = CompanyList::LESS_THAN,
		$acquired_company_day = 0,
		array $ai_tags = [],
		$ai_tag_match_mode = CompanyList::TAG_MATCH_MODE_SINGLE,
		$ai_status_id = null,
		$report_item_unique_key = '',
		$has_customer_mail = null,
		$site_id=0,
		$ads_account_id=0,
		$archive_type = null,
		array $status_id = [],
		$status_edit_type = null,
		$create_start_date = '',
		$create_end_date = '',
		$read_flag = null,
		array $create_user = [],
		array $last_edit_user = [],
		array $last_owner_ids = [],
		$edit_begin_time = '',
		$edit_end_time = '',
		$private_begin_time = '',
		$private_end_time = '',
		$public_begin_time = '',
		$public_end_time = '',
		$release_count = null,
		array $lead_id = [],
		array $origin_list = null,
		array $filters = [],
		array $time_filters = [],
		$criteria_type = 1,
		$criteria = '',
	)
	{
		$this->validate([
			'curPage'        => 'integer',
			'pageSize'       => 'integer',
			'sort_field'     => 'regex:/^\w{2,32}$/',
			'sort_type'      => 'string|in:asc,desc',
			'keyword'        => 'string|max:1000',
			'country' => 'array',
			'user_id' => 'array',
			'origin' => 'array',
			'user_num' => 'array',
			'user_num.*' => 'numeric',
			'star' => 'array',
			'category_ids' => 'array',
			'recent_select_flag' => 'numeric',
			'will_public' => 'integer',
			'tag_match_mode' => 'integer',
			'compare_day' => 'integer',
			'compare_day_op' => 'numeric',
			'acquired_company_day' => 'integer',
			'ai_tags' => 'array',
			'ai_tag_match_mode' => 'integer',
			'site_id' => 'integer',
			'ads_account_id' => 'integer',
			'archive_type' => 'integer',
			'create_user' => 'array',
			'create_user.*' => 'integer',
			'last_edit_user' => 'array',
			'last_edit_user.*' => 'integer',
			'last_owner_ids' => 'array',
			'last_owner_ids.*' => 'integer',
			'lead_id' => 'array',
			'lead_id.*' => 'integer',
		]);

		$user = User::getLoginUser();
		$clientId = $user->getClientId();
		$opUserId = $user->getUserId();

		//检查公海权限
		if ($isPublicPool = count(array_diff($user_num, [0])) == 0) {
			$needPrivilegeName = PrivilegeConstants::PRIVILEGE_CRM_LEAD_POOL_VIEW;
		} else {
			$needPrivilegeName = PrivilegeConstants::PRIVILEGE_CRM_LEAD_VIEW;
		}
		\common\library\privilege_v3\Helper::checkPermission($clientId, $opUserId, $needPrivilegeName);

		$params = [];
		$method = new \ReflectionMethod(self::class, 'actionGetLeadStatistic');
		foreach ($method->getParameters() as $parameter) {
			$params[$parameter->name] = ${$parameter->name} ?? null;
		}

		$result = \common\library\lead\Helper::getLeadAllStatistic($clientId, $opUserId, $params);

		return $this->success($result);
	}

    /**
     * 获取线索分层统计
     * @return void
     * @throws ProcessException
     */
    public function actionGetLeadLayerStatistics($selected_filter_time = '')
    {
        $user = User::getLoginUser();
        $clientId = $user->getClientId();
        $opUserId = $user->getUserId();

        // 是否查询快照数据
        $snapshotSearch = false;
        if (!empty($selected_filter_time)) {
            $snapshotDateTime = new DateTime(date('Y-m-d', strtotime($selected_filter_time)));
            $sixMonthsDateTime = (new DateTime('now'))->sub(new DateInterval('P6M'));

            if ($snapshotDateTime < $sixMonthsDateTime) {
                throw new RuntimeException(\Yii::t('lead', 'Historical data query time cannot exceed 6 months'));
            }

            $snapshotSearch = $snapshotDateTime->format('Y-m-d') != date('Y-m-d');
        }

        if ($snapshotSearch) {
            $result = \common\library\layer\Helper::queryLayerRecordStatistics(
                $clientId,
                $opUserId,
                $selected_filter_time,
                Constants::TYPE_LEAD
            );

            return $this->success($result);
        }

        $list = new \common\library\lead_v3\LeadList($opUserId);
        $list->paramsMapping([
            'show_all' => 1,
            'user_num' => [1],
        ]);
        $list->setOrderBy('layer_id');
        $list->setOrder('asc');

        $result = $list->layerStatistics();

        return $this->success($result);
    }

    /**
     * 获取线索行动建议
     * @return void
     * @throws ProcessException
     */
    public function actionGetLeadActionSuggestion()
    {
        $user = User::getLoginUser();
        $clientId = $user->getClientId();
        $opUserId = $user->getUserId();

        $privilegeService = \common\library\privilege_v3\PrivilegeService::getInstance($clientId);

        $hasMaPermission = $privilegeService->hasFunctional(PrivilegeConstants::FUNCTIONAL_AUTO_MARKETING);

        //判断是否是PRO+AI版本
        $mainSystemId = $privilegeService->getMainSystemId();
        $hasAIPermission = $privilegeService->hasFunctional(PrivilegeConstants::FUNCTIONAL_OKKI_AI);
        $isProAIFlag = false;

        if (in_array($mainSystemId, [\common\library\privilege_v3\PrivilegeConstants::OKKI_PRO_SYSTEM_ID, \common\library\privilege_v3\PrivilegeConstants::CRM_PRO_SYSTEM_ID]) && $hasAIPermission) {
            $isProAIFlag = true;
        }

        $permissionList = [
            'hasMaPermission' => $hasMaPermission,
            'isProAIFlag' => $isProAIFlag
        ];

        $result = [];

        $context = new \common\library\layer\LayerStatisticsContext();

        foreach (\common\library\lead\Constant::LAYER_ID_MAP as $layer) {

            $layerId = $layer;
            $queryParams = [
                'opUserId' => $opUserId
            ];

            if ($layer == Constant::LAYER_ID_PREMIUM) {
                $queryParams['isProAIFlag'] = $isProAIFlag;
            }

            $strategy = \common\library\layer\LayerStatisticsFactory::getStrategy($layerId);
            $context->setQueryParams($queryParams);
            $context->setStrategy($strategy);
            $item = $context->executeStrategy();

            $bestRecommends = \common\library\lead\Helper::getLeadLayerBestRecommends($clientId, $opUserId, $layerId);

            $suggestionActions = \common\library\lead\Helper::getLeadLayerSuggestionActions($permissionList, $layerId, $opUserId);

            $item = array_merge($item, [
                'best_recommend' => $bestRecommends,
                'suggestion_action' => $suggestionActions['actions'],
            ]);

            $result[] = $item;
        }

        return $this->success($result);
    }

    /**
     * 获取分层-智能填充开关
     * @return void
     */
    public function actionGetAutoFilledSwitch()
    {
        $user = User::getLoginUser();
        $clientId = $user->getClientId();
        $userId = $user->getUserId();

        $result = [
            'action' => 'auto_filled_switch',
            'enable_flag'=> false,
        ];

        $setting = new UserSetting($clientId, $userId, UserSetting::LEAD_LAYER_AUTO_FILLED_SWITCH);
        if(isset($setting->enable_flag) && $setting->enable_flag == 0) {
            $this->success($result);
        }
        $value = $setting->getValue();
        if($value === null){
            $this->success($result);
        }

        $result['enable_flag'] = $value == 1;

        return $this->success($result);
    }

    /**
     * 获取智能填充的孵化结果统计

     */
    public function actionGetAutofilledStatistics()
    {
        $user = User::getLoginUser();
        $opUserId = $user->getUserId();

        $list = new \common\library\lead_v3\LeadList($opUserId);
        $list->paramsMapping([
            'show_all' => 1,
            'user_num' => [1],
        ]);

        $list->setIsArchive([\common\library\lead\Lead::LEAD_TYPE_NEW,\common\library\lead\Lead::LEAD_TYPE_ARCHIVE]);
        $list->setStatusId([\common\library\setting\library\status\Status::SYS_STATUS_CONVERSION]);
        $list->setFilterInvalidLeadFlag(false);
        $convertedData =  $list->count();

        $list->setStatusId([\common\library\setting\library\status\Status::SYS_STATUS_INVALID]);
        $convertedInvalidCount =  $list->count();

        $list->setIsArchive(true);
        $list->setStatusId(null);
        $list->setFilterInvalidLeadFlag(true);

        $result = [
            'lead_convert_statistics'   => ['converted_total' => $convertedData, 'converted_invalid_count' => $convertedInvalidCount],
            'lead_replenish_statistics' => $list->leadReplenishStatistics(),
            'lead_marketing_statistics' => $list->leadMarketingStatistics(),
        ];

        return $this->success($result);
    }

}
