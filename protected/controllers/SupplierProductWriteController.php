<?php

use common\library\privilege_v3\PrivilegeConstants;
use common\library\supplier\product\BatchSupplierProduct;
use common\library\supplier\product\Helper;
use common\library\supplier\product\SupplierProduct;
use common\library\supplier\product\SupplierProductFilter;
use common\library\import\ImportWriteActions;
use common\library\supplier\product\import\SupplierProductImportExecutor;
use common\library\supplier\SupplierConstant;
use xiaoman\orm\database\data\In;

/**
 * Created by PhpStorm.
 * User: beenzhang
 * Date: 2021/3/19
 * Time: 4:41 PM
 */

class SupplierProductWriteController extends Controller
{

    use ImportWriteActions;
    use \common\library\privilege_v3\OperationPrivilegeInterceptor;
    
    public static function privilegeOperationMap():array {
        return [
            'create' => PrivilegeConstants::PRIVILEGE_CRM_SUPPLIER_PRODUCT_CREATE,
            'batchCreate' => PrivilegeConstants::PRIVILEGE_CRM_SUPPLIER_PRODUCT_CREATE,
            'edit' => PrivilegeConstants::PRIVILEGE_CRM_SUPPLIER_PRODUCT_EDIT,
            'setMainSupplier' => PrivilegeConstants::PRIVILEGE_CRM_SUPPLIER_PRODUCT_EDIT,
            'delete' => PrivilegeConstants::PRIVILEGE_CRM_SUPPLIER_PRODUCT_REMOVE,
        ];
    }
    
    protected function beforeAction($action)
    {
        $this->checkPrivilegeByOperation($action->id);
        return parent::beforeAction($action);
    }

    public function initImportWrite()
    {
        $this->importType = Constants::TYPE_SUPPLIER_PRODUCT;
        $this->importConfig = [
            'support_file' => ['xls','xlsx','csv'],
            'file_size_limit' => 1024
        ];
        $this->importExecutor = SupplierProductImportExecutor::class;
        $this->importPrivilege = PrivilegeConstants::PRIVILEGE_CRM_SUPPLIER_PRODUCT_IMPORT;
    }

    public function actionCreate(
        $product_id,
        $supplier_id,
        $supplier_product_no,
        $sku_id = 0,
        $supplier_price = 0,
        $currency = '',
        array $gradient_price = [],
        $supplier_product_name = '',
        $remark = '',
        $expire_date = ''
    ){
        $user = User::getLoginUser();

        $supplierProduct = new SupplierProduct($user->getClientId());
        $supplierProduct->loadBySupplierProduct($supplier_id, $product_id, $sku_id);

        //恢复已删除数据 这里需要加上供应商逻辑
        //当产品只关联1个供应商时，自动设置该供应商为此产品的主供应商
        if ($supplierProduct->isExist())
        {
            $enableFlag = $supplierProduct->enable_flag;
            if ($enableFlag)
                throw new \RuntimeException('产品已关联供应商');

            $time = date('Y-m-d H:i:s');
            $expire_date = !empty($expire_date) ? $expire_date : '1970-01-01';
            $data = [
                'supplier_price' => floatval($supplier_price),
                'supplier_product_name' => $supplier_product_name,
                'supplier_product_no' => $supplier_product_no,
                'create_time' => $time,
                'update_time' => $time,
                'enable_flag' => 1,
                'currency' => $currency,
                'gradient_price' => $gradient_price,
                'remark' => $remark,
                'expire_date' => $expire_date,
            ];
            $supplierProduct = new SupplierProduct($user->getClientId(),$supplierProduct->relation_id);
            $supplierProduct->getOperator()->recover();
            $supplierProduct->getOperator()->edit($data);

            return $this->success($supplierProduct->relation_id);
        }

        //新建
        $supplierProduct->sku_id = $sku_id;
        $supplierProduct->product_id = $product_id;
        $supplierProduct->supplier_id = $supplier_id;
        $supplierProduct->supplier_price = $supplier_price;
        $supplierProduct->supplier_product_name = $supplier_product_name;
        $supplierProduct->supplier_product_no = $supplier_product_no;
        $supplierProduct->gradient_price = $gradient_price;
        $supplierProduct->currency = $currency;
        $supplierProduct->remark = $remark;
        $supplierProduct->expire_date = $expire_date;
        $supplierProduct->create();

        return $this->success($supplierProduct->relation_id);
    }

    public function actionEdit(
        $relation_id,
        $supplier_product_name,
        $supplier_product_no,
        $supplier_price = 0,
        $currency = '',
        array $gradient_price = [],
        $remark = '',
        $expire_date = ''
    )
    {
        $user = User::getLoginUser();

        $supplierProduct = new SupplierProduct($user->getClientId(),$relation_id);
        if ($supplierProduct->isNew())
            throw new RuntimeException('关联产品不存在');

        $supplier = new \common\library\supplier\Supplier($user->getClientId(), $supplierProduct->supplier_id);
        if ($supplier->isExist()) {
            if (!\common\library\privilege_v3\Helper::canOperateRecord($this->getLoginUserClientId(), $this->getLoginUserId(), $supplier->scope_user_ids, PrivilegeConstants::PRIVILEGE_CRM_SUPPLIER_PRODUCT_EDIT)) {
                throw new \RuntimeException(\Yii::t('privilege', 'Missing permission'));
            }
        }
        
        $expire_date = !empty($expire_date) ? $expire_date : '1970-01-01';
        $data = [
            'supplier_price' => $supplier_price,
            'supplier_product_name' => $supplier_product_name,
            'supplier_product_no' => $supplier_product_no,
            'update_time' => date('Y-m-d H:i:s'),
            'currency' => $currency,
            'gradient_price' => $gradient_price,
            'remark' => $remark,
            'expire_date' => $expire_date,
        ];
        $supplierProduct->getOperator()->edit($data);

        return $this->success($supplierProduct->relation_id);
    }

    public function actionBatchEdit($data)
    {
        $user = User::getLoginUser();
        $data = json_decode($data, true);
        $relation_ids = array_column($data,'relation_id');
        
        $relation_ids = $this->getPrivilegeRelationIds($this->getLoginUserClientId(), $relation_ids);
        if (empty($relation_ids)) {
            return $this->success([]);
        }
        $filter = new SupplierProductFilter($user->getClientId());
        $filter->enable_flag = \Constants::ENABLE_FLAG_TRUE;
        $filter->relation_id = $relation_ids;
        $filter->select(['client_id','relation_id']);
        $invoiceList = $filter->rawData();
        $editInfo = [];
        $field = ['relation_id', 'supplier_product_no', 'supplier_product_name', 'gradient_price', 'expire_date', 'remark', 'currency'];
        foreach ($data as $datum) {
            $edit = [];
            foreach ($field as $fieldKey) {
                if (isset($datum[$fieldKey])) {
                    $edit[$fieldKey] = $datum[$fieldKey];
                }
            }
            $editInfo[] = $edit;
        }
        $batch = new BatchSupplierProduct($user->getClientId());
        $batch->initFromData($invoiceList);
        $batch->getOperator()->batchEdit($editInfo);
        return $this->success([]);
    }
    
    protected function getPrivilegeRelationIds($clientId, $relationIds)
    {
        $hasPrivilegeIds = [];
        if (empty($relationIds)) {
            return $hasPrivilegeIds;
        }
        $filter = new SupplierProductFilter($clientId);
        $filter->relation_id = new \xiaoman\orm\database\data\In($relationIds);
        $filter->select(['supplier_id', 'relation_id']);
        $supplierProducts = $filter->rawData();
        $supplierIds = $relationIdMap = [];
        foreach ($supplierProducts as $supplierProduct) {
            $supplierIds[] = $supplierProduct['supplier_id'];
            $relationIdMap[$supplierProduct['supplier_id']][] = $supplierProduct['relation_id'];
        }
        $supplierIds = array_filter(array_values($supplierIds));
        if (empty($supplierIds)) {
            return $hasPrivilegeIds;
        }
        $supplierFilter = new \common\library\supplier\SupplierFilter($clientId);
        $supplierFilter->supplier_id = $supplierIds;
        $supplierFilter->is_archive  = \Constants::ENABLE_FLAG_TRUE; 
        $supplierFilter->select(['supplier_id', 'scope_user_ids']); 
        $supplierScopeMap = array_column($supplierFilter->rawData(), null, 'supplier_id');
        $canOperateRelationIds = [];
        foreach ($supplierScopeMap as $item) {
            if (\common\library\privilege_v3\Helper::canOperateRecord($this->getLoginUserClientId(), $this->getLoginUserId(), $item['scope_user_ids'], PrivilegeConstants::PRIVILEGE_CRM_SUPPLIER_PRODUCT_REMOVE)) {
                $canOperateRelationIds = array_merge($canOperateRelationIds, $relationIdMap[$item['supplier_id']] ?? []);
            }
        }
        return $canOperateRelationIds;
    }

    public function actionDelete(array $relation_id)
    {
        $user = User::getLoginUser();
        $relation_id = $this->getPrivilegeRelationIds($this->getLoginUserClientId(), $relation_id);
        if (!empty($relation_id)) {
            $delFilter = new SupplierProductFilter($user->getClientId());
            $delFilter->relation_id = new \xiaoman\orm\database\data\In($relation_id);
            $batchObj = $delFilter->find();
            $batchObj->getOperator()->delete(SupplierConstant::SUPPLIER_PRODUCT_DISABLE_FOR_REFERENCE);
        }

        return $this->success('');
    }

    public function actionSetMainSupplier($relation_id)
    {
        $user = User::getLoginUser();

        $supplierProduct = new SupplierProduct($user->getClientId(),$relation_id);
        if ($supplierProduct->isNew())
            throw new RuntimeException('关联供应商不存在');
        
        $supplier = new \common\library\supplier\Supplier($user->getClientId(), $supplierProduct->supplier_id);
        if ($supplier->isExist()) {
            if (!\common\library\privilege_v3\Helper::canOperateRecord($this->getLoginUserClientId(), $this->getLoginUserId(), $supplier->scope_user_ids, PrivilegeConstants::PRIVILEGE_CRM_SUPPLIER_PRODUCT_EDIT)) {
                throw new \RuntimeException(\Yii::t('privilege', 'Missing permission'));
            }
        }

        $supplierProduct->getOperator()->setMainSupplier();
        return $this->success('');
    }

    public function actionBatchCreate($data, $update_product_flag = 1)
    {
        $user = User::getLoginUser();

        $data = json_decode($data, true);
        //批量编辑不更新
        $ret  = Helper::batchCreateSupplierProduct($data,$update_product_flag);

        $relation_ids = $ret['relation_ids'];

        $filter = new SupplierProductFilter($user->getClientId());
        $filter->enable_flag = \Constants::ENABLE_FLAG_TRUE;
        $filter->relation_id = $relation_ids;
        $filter->select(['client_id','relation_id','sku_id','supplier_id']);
        $invoiceList = $filter->rawData();
        return $this->success([
            'list' => $invoiceList,
            'count' => $filter->count(),
        ]);
    }

    public function actionBatchSetMainSupplier(array $relation_ids)
    {
        $user = User::getLoginUser();
        
        $relationIds = $this->getPrivilegeRelationIds($this->getLoginUserClientId(), $relation_ids);
        if (empty($relationIds)) {
            return $this->success([
                'total_count'   => count($relationIds),
                'succeed_count' => 0,
                'fail_count'    => count($relationIds),
                'fail_info'     => [
                    'message'   => \Yii::t('privilege', 'Missing permission'),
                    'ids'       => $relationIds,
                    'failType'  => common\library\invoice\batch\Constant::MISSING_PERMISSION_TYPE,
                ],
                'success_ids'   => []
            ]);
        }
        
        $filter = new SupplierProductFilter($user->getClientId());
        $filter->enable_flag = \Constants::ENABLE_FLAG_TRUE;
        $filter->relation_id = new In($relationIds);
        $filter->select(['client_id', 'relation_id','main_supplier']);
        $list = $filter->rawData();

        $batch = new BatchSupplierProduct($user->getClientId());
        $batch->initFromData($list);
        $batch->getOperator()->batchSetMainSupplier(1);

        return $this->success([
            'total_count'   => count($relation_ids),
            'succeed_count' => count($batch->getOperator()->getSuccessIds()),
            'fail_count'    => count($batch->getOperator()->getFailIds()) + (count($relation_ids) - count($relationIds)),
            'fail_info'     => array_values($batch->getOperator()->getFailInfo()),
            'success_ids'   => array_values($batch->getOperator()->getSuccessIds())
        ]);
    }

}