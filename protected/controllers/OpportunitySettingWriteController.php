<?php
/**
 * Created by PhpStorm.
 * User: cayley
 * Date: 2018/11/21
 * Time: 下午6:07
 */


use common\library\setting\library\flow\FlowApi;
use common\library\setting\library\stage\Stage;
use common\library\setting\library\stage\StageApi;
use common\library\setting\library\stage\Stage as OpportunityStage;
use common\library\setting\user\UserSetting;

class OpportunitySettingWriteController extends Controller
{
    use \common\library\custom_field\CustomFieldWriteActions;
    use \common\library\serial\actions\SNRuleWriteActions;

    protected function beforeAction($action)
    {
        $this->initCustomFieldService(Constants::TYPE_OPPORTUNITY);
        $this->initSNRuleService(\common\library\serial\CustomRuleConstant::MODULE_OPPORTUNITY);
        return parent::beforeAction($action);
    }

    /** 保存或编辑销售阶段
     * @param int $stage_id
     * @param $type
     * @param $name
     * @param int $success_rate
     * @param $flow_id
     * @param null $remark
     * @param bool $showOpportunity
     * @param string $tip
     * @param int $review_flag
     */
    public function actionSaveStage(
        $type,
        $name,
        $flow_id,
        $stage_id = 0,
        $success_rate = 0,
        $remark = null,
        $showOpportunity = true,
        $tip = '',
        $review_flag = 0
    )
    {
        $this->validate([
            'type' => 'numeric',
            'flow_id' => 'numeric',
            'stage_id' => 'numeric',
            'success_rate' => 'numeric',
            'review_flag' => 'numeric',
        ]);

        $user = User::getLoginUser();
        $clientId = $user->getClientId();
        $userId = $user->getUserId();

        $name = trim($name);
        $success_rate = empty($success_rate) ? 0 : $success_rate;
        $api = new StageApi($clientId);
        $api->setFlowId($flow_id);
        $id = $api->getIdByName($name);

        if ($stage_id) {
            if ($id && $id != $stage_id) {
                throw new RuntimeException(\Yii::t('opportunity', 'Under the same sales process, duplicate names are not allowed in the customer stage'));
            }
            $stageData = $api->edit($stage_id, [
                'field_type' => intval($type),
                'name' => $name,
                'success_rate' => abs($success_rate),
                'flow_id' => intval($flow_id),
                'description' => $remark,
                'review_flag' => intval($review_flag),
                'tip' => $tip,
            ]);
            if (!$stageData) {
                throw new RuntimeException(\Yii::t('opportunity', 'Sales stage information does not exist'));
            }

        } else {
            if ($id) {
                throw new RuntimeException(\Yii::t('opportunity', 'Under the same sales process, duplicate names are not allowed in the customer stage'));
            }

            //新增的情况需要限制赢单阶段数量
            if ($type === OpportunityStage::STAGE_WIN_STATUS) {
                $api->setStageType(OpportunityStage::STAGE_WIN_STATUS);
                $winStageCount = $api->count();
                if ($winStageCount + 1 > ($maxNum = Stage::STAGE_LIST_SAVE_MAX_LENGTH[OpportunityStage::STAGE_WIN_STATUS] ?? 0)){
                    throw new \RuntimeException(\Yii::t('opportunity', 'The number of sales stages cannot exceed') . $maxNum);
                }
            }

            $stageId = $api->setOpUser($userId)->create([
                'field_type' => intval($type),
                'name' => $name,
                'success_rate' => abs($success_rate),
                'flow_id' => intval($flow_id),
                'description' => $remark,
                'review_flag' => intval($review_flag),
                'tip' => $tip,
            ]);
            $stageData = $api->find($stageId);
        }

        if ($showOpportunity == true) {
            if ($stage_id > 0) {
                $opportunityList = new \common\library\opportunity\OpportunityList($clientId);
                $opportunityList->setViewingUserId($userId);
                $opportunityList->setStage($stage_id);
                $opportunityList->setSkipPermissionCheck(true);
                $stageData['count'] = $opportunityList->count();

            } else {
                $stageData['count'] = 0;
            }
        }
        $this->success($stageData);
    }


    /** 销售流程开关
     * @param $stage_id
     * @param $review_flag
     */
    public function actionSaveStageReviewSwitch(
        $stage_id,
        $review_flag = 0
    )
    {
        $user = User::getLoginUser();
        $clientId = $user->getClientId();
//        $userId = $user->getUserId();

        $api = new StageApi($clientId);
        $res = $api->edit($stage_id, [
            'review_flag' => intval($review_flag),
        ]);
//        if (!$stageData) {
//            throw new RuntimeException(\Yii::t('opportunity', 'Sales stage information does not exist'));
//        }
//
//        $stageObj = new \common\library\opportunity\stage\OpportunityStage($clientId, 0, $stage_id);
//        if( $stage_id >0 && $stageObj->isNew()){
//            throw new RuntimeException(\Yii::t('opportunity', 'Sales stage information does not exist'));
//        }
//        //是否需要审批表示 0否 1是
//        $stageObj->review_flag = intval($review_flag);
//        $stageObj->update_time = date("Y-m-d H:i:s");
//        $res = $stageObj->save();
        $this->success($res);
    }

    /**  删除销售阶段
     * @param $stage_id
     * @return string
     */
    public function actionDeleteStage($stage_id)
    {
        $user = User::getLoginUser();
        $userId = $user->getUserId();
        $clientId = $user->getClientId();

        $api = new StageApi($clientId);
        $res = $api->delete($stage_id);
        if (!$res) {
            throw new RuntimeException(\Yii::t('opportunity', 'The sales ID does not exist'));
        }
//        $stage = new \common\library\opportunity\stage\OpportunityStage($clientId, 0,$stage_id);
//        if(!$stage->isExist()){
//            throw new RuntimeException(\Yii::t('opportunity', 'The sales ID does not exist'));
//        }
//
//        $res = $stage->delete();
        return $this->success($res);

    }

    /**
     * 修改对应分类销售阶段的排序
     * @param string $stage_ids 销售阶段id列表
     * @param string $type 新的商机阶段
     * @return array
     * **/
    public function actionArrangeStages($stage_ids, $type)
    {

        $stage_ids = array_map('intval', explode(",", $stage_ids));
        $user = User::getLoginUser();
        $userId = $user->getUserId();
        $clientId = $user->getClientId();

        if (!in_array($type, OpportunityStage::STAGE_LIST)) {
            throw new RuntimeException(\Yii::t('common', 'Non-existent classification'));
        }
        $result = \common\library\opportunity\stage\Helper::modifyStageRank($clientId, $stage_ids, $type);
        $this->success($result);

    }

    /**
     * 输单原因-新增，编辑
     * @paam string $reason_id 输单原因id
     * @param string $name 输单原因名称
     * @param integer $flow_id 流程id
     * @param boolean $showOpportunity 是否展示商机数量
     * @return array
     * **/
    public function actionSaveStageFailReason($name, $reason_id = 0, $flow_id = 0, $showOpportunity = true)
    {
        $user = User::getLoginUser();
        $clientId = $user->getClientId();
        $userId = $user->getUserId();

        $api = (new \common\library\setting\library\reason\ReasonApi($clientId));
        $api->setOpUser($user);
        if ($reason_id) {
            $failReasonData = $api->edit($reason_id, [
                'item_name' => $name,
                'relate_id' => intval($flow_id),
            ]);
            if (!$failReasonData) {
                throw new RuntimeException(\Yii::t('opportunity', 'The reason for losing the order does not exist'));
            }
        } else {
            $reason_id = $api->create([
                'item_name' => $name,
                'relate_id' => intval($flow_id),
            ]);
            if ($reason_id) {
                $failReasonData = $api->find($reason_id);
            }
        }


//        $failReasonObj = new \common\library\opportunity\stage\OpportunityFailReason($clientId, $reason_id);
//        if( $reason_id >0 && $failReasonObj->isNew() )
//            throw new RuntimeException(\Yii::t('opportunity', 'The reason for losing the order does not exist'));
//        //保存
//        $name = trim($name);
//        $failReasonObj->create_user = $userId;
//        $failReasonObj->name = $name;
//        $failReasonObj->update_time = date("Y-m-d H:i:s");
//        $failReasonObj->flow_id = intval($flow_id);
//        $failReasonObj->save();
//        $failReasonData = $failReasonObj->getAttributes();

        if ($showOpportunity == true) {
            if ($reason_id > 0) {
                $opportunityList = new \common\library\opportunity\OpportunityList($clientId);
                $opportunityList->setViewingUserId($userId);
                $opportunityList->setFailType($reason_id);
                $opportunityList->setStageType(OpportunityStage::STAGE_FAIL_STATUS);
                $opportunityList->setSkipPermissionCheck(true);
                $failReasonData['count'] = $opportunityList->count();

            } else {
                $failReasonData['count'] = 0;
            }
        }
        
        $this->success($failReasonData);
    }

    /**
     * 删除输单原因
     * @param string $reason_id 需要删除的输单原因id
     * @param string $new_reason_id 新的输单原因id
     * @return integer
     * **/
    public function actionDeleteStageFailReason($reason_id, $new_reason_id = 0)
    {
        $user = User::getLoginUser();
        $userId = $user->getUserId();
        $clientId = $user->getClientId();

        if ($succeed = (new \common\library\setting\library\reason\ReasonApi($clientId))->delete($reason_id)) {
            //当直接删除输单原因时，不改变原有数据
            //https://xmkm.yuque.com/crn8rp/xpcimb/xc8460dcgne7zdpo#lregk 需求里没写，在相关需求的UI交互稿中
            if ($new_reason_id > 0) {
                \common\library\opportunity\Helper::batchChangeFailType($clientId, $userId, $reason_id, $new_reason_id);
            }
        }
        if ($succeed) {
            return $this->success($succeed);
        } else {
            return $this->fail(-1, \Yii::t('common', 'Failed to delete'));
        }

//
//        $failReasonObj = new \common\library\opportunity\stage\OpportunityFailReason($clientId, $reason_id);
//        $afferRows = $failReasonObj->deleteFailReasonById($new_reason_id,$userId);
//        if($afferRows){
//            return $this->success($afferRows);
//        }else{
//            return $this->fail(-1, \Yii::t('common', 'Failed to delete'));
//        }

    }

    public function actionBatchCreateStageFailReason(array $names= [])
    {
        $this->validate([
            'names'        => 'array',
            'names.*'      => 'required|string',
        ]);
        $user = User::getLoginUser();
        $clientId = $user->getClientId();

        $api = (new \common\library\setting\library\reason\ReasonApi($clientId));
        $api->setOpUser($user);
        $succeed = $api->createBatchByName($names);

        if ($succeed) {
            return $this->success($succeed);
        } else {
            return $this->fail(-1, \Yii::t('common', 'Failed to create'));
        }
    }

    /**
     * 修改输单原因的排序
     * @param string $reasonIds 输单原因id列表
     * @return array
     * **/
    public function actionArrangeStageFailReason($reason_ids)
    {
        $reason_ids = array_map('intval', explode(",", $reason_ids));
        $user = User::getLoginUser();
        $userId = $user->getUserId();
        $clientId = $user->getClientId();

        $result = \common\library\opportunity\stage\Helper::modifyFailReasonRank($clientId, $reason_ids);
        $this->success($result);
    }

    public function actionAutoSettings($enable_create, $handler_add, $handler_role, $amount_model, $amount, $currency, $account_day,
                                       $enable_update, $amount_update, $stage)
    {

        $user = User::getLoginUser();
        $settings = new \common\library\ai\setting\OpportunityAutoSettings($user->getClientId());
        $settings->enable_create = $enable_create;
        $settings->handler_add = $handler_add;
        $settings->handler_role = $handler_role;
        $settings->amount_model = $amount_model;
        $settings->amount = $amount;
        $settings->currency = $currency;
        $settings->account_day = $account_day;
        $settings->enable_update = $enable_update;
        $settings->amount_update = $amount_update;

        $stage = json_decode($stage, true);
        if (empty($stage))
            throw new RuntimeException(\Yii::t('common', 'Parameter cannot be empty'));

        $settings->stage = $stage;
        $settings->save();
        $this->success('');

    }


    /**保存商机销售流程
     * @param int $flow_id
     * @param $name
     * @param int $enable_flag
     */
    public function actionSaveSalesFlow(
        $name,
        $flow_id = 0,
        $enable_flag = 1
    )
    {
        $user = User::getLoginUser();
        $clientId = $user->getClientId();
        $userId = $user->getUserId();

        //新增时候需要判断是否有添加的权限
        if (!$flow_id && !\common\library\privilege_v3\Helper::hasFunctional($clientId, \common\library\privilege_v3\PrivilegeConstants::FUNCTIONAL_OPPORTUNITY_SALES_FLOW)) {
            throw new RuntimeException(\Yii::t('opportunity', 'No permissions to add the sales flow'));
        }

        $api = new FlowApi($clientId);
        $api->setOpUser($userId);
        if ($flow_id) {
            $api->edit($flow_id, [
                'item_name' => trim($name),
            ]);
        } else {
            $flow_id = $api->create([
                'item_name' => trim($name),
                'field_type' => 0, // 非主流程
            ], 0, 1);
        }

        return $this->success(['flow_id' => $flow_id]);
//
//        $opportunitySalesFlow = new OpportunitySalesFlow($clientId, $flow_id);
//        if( $flow_id >0 && $opportunitySalesFlow->isNew() )
//            throw new RuntimeException(\Yii::t('opportunity', 'Sales flow information does not exist'));
//
//        $name = trim($name);
//        $opportunitySalesFlow->flow_id = $flow_id;
//
//        if($opportunitySalesFlow->isNew()){
//            $opportunitySalesFlow->create_user = $userId;
//            $opportunitySalesFlow->main_flag = 0;
//            $opportunitySalesFlow->disable_flag = OpportunitySalesFlow::DISABLE_STATUS; //新增销售流程，默认停用
//        }
//        $opportunitySalesFlow->update_time = date("Y-m-d H:i:s");
//        $opportunitySalesFlow->name = $name;
//        $opportunitySalesFlow->enable_flag = $enable_flag;
//        $opportunitySalesFlow->save();
//        $data = $opportunitySalesFlow->getAttributes();
//
//        $this->success($data);
    }

    /** 删除销售流程
     * @param $flow_id
     * @return string
     * @throws CDbException
     * @throws ProcessException
     */
    public function actionDeleteSalesFlow($flow_id)
    {
        $user = User::getLoginUser();
        $userId = $user->getUserId();
        $clientId = $user->getClientId();

        $api = new FlowApi($clientId);
        $api->setOpUser($userId);
        if (!$res = $api->delete($flow_id)) {
            throw new RuntimeException(\Yii::t('opportunity', 'Sales flow id does not exist'));
        }

//        $opportunitySalesFlow = new OpportunitySalesFlow($clientId, $flow_id);
//        if(!$opportunitySalesFlow->isExist()){
//            throw new RuntimeException(\Yii::t('opportunity', 'Sales flow id does not exist'));
//        }
//
//        $opportunitySalesFlow->setUserId($userId);
//        $res = $opportunitySalesFlow->delete();

        return $this->success($res);

    }

    /** 销售阶段启用，停用开关。enable|disable
     * @param $flow_id
     * @param $type
     * @return string
     */
    public function actionSaveSalesFlowSwitch($flow_id, $type = 'enable')
    {
        $user = \User::getLoginUser();
        $clientId = $user->getClientId();
//        $disableFlag = 'enable' == $type ? 0 : 1; //启用，禁用

        $api = new FlowApi($clientId);

        //主销售流程不允许停用
        if (!empty($flow_id) && $type == 'disable') {
            $flow = $api->find($flow_id);
            if (!empty($flow['main_flag'] ?? 0)) {
                throw  new RuntimeException(\Yii::t('opportunity', 'Main sales process does not support deactivation. If you want to deactivate the main sales process, please first set other sales processes to the main sales process'));
            }
        }

        if ('enable' == $type) {
            $res = $api->enable($flow_id);
        } else {
            $res = $api->disable($flow_id);
        }
//
//        $opportunitySalesFlow = new OpportunitySalesFlow($clientId, $flow_id);
//
//        if(!$opportunitySalesFlow->isExist()){
//            throw new RuntimeException(\Yii::t('opportunity', 'Sales flow id does not exist'));
//        }
//        //主销售流程不允许停用
//        if($disableFlag == OpportunitySalesFlow::NORMAL_FLOW_FLAG &&
//            $opportunitySalesFlow->getAttributes()['main_flag'] == OpportunitySalesFlow::MAIN_FLOW_FLAG){
//            throw  new RuntimeException(\Yii::t('opportunity', 'Main sales process does not support deactivation. If you want to deactivate the main sales process, please first set other sales processes to the main sales process'));
//        }
//        $opportunitySalesFlow->disable_flag = $disableFlag;
//        $res = $opportunitySalesFlow->save();
        return $this->success(boolval($res));
    }

    /** 设置主销售流程
     * @param $flow_id
     * @return string
     * @throws CDbException
     * @throws ProcessException
     */
    public function actionSetMainSalesFlow($flow_id)
    {
        $user = \User::getLoginUser();
        $clientId = $user->getClientId();

        $api = new FlowApi($clientId);
        $flow = $api->find($flow_id);
        if (!empty($flow['disable_flag'] ?? 0)) {
            throw  new RuntimeException(\Yii::t('opportunity', 'Main sales process does not support deactivation. If you want to deactivate the main sales process, please first set other sales processes to the main sales process'));
        }

        $res = $api->updateMainFlag($flow_id);
        if (!$res) {
            throw new RuntimeException(\Yii::t('opportunity', 'Sales flow id does not exist'));
        }

//        $opportunitySalesFlow = new OpportunitySalesFlow($clientId,$flow_id);
//
//        if(!$opportunitySalesFlow->isExist()){
//            throw new RuntimeException(\Yii::t('opportunity', 'Sales flow id does not exist'));
//        }
//        $res = $opportunitySalesFlow->setMainSalesFlow();
        return $this->success($res);
    }


    /** 修改销售流程的排序
     * @param $flow_ids 销售流程id列表
     */
    public function actionArrangeSalesFlow($flow_ids)
    {

        $flow_ids = array_map('intval', explode(",", $flow_ids));
        $user = User::getLoginUser();
        $clientId = $user->getClientId();

        $result = \common\library\opportunity\sales_flow\Helper::modifySalesFlowRank($clientId, $flow_ids);
        $this->success($result);

    }

    /**
     * 商机销售看板配置
     * @param array $config
     * @return false|string
     */
    public function actionDashboardSetting(array $config)
    {
        $user = \User::getLoginUser();
        $dashboard = new \common\library\setting\user\UserSetting($user->getClientId(), $user->getUserId(), \common\library\setting\user\UserSetting::OPPORTUNITY_DASHBOARD_SETTING);
        $dashboard->value = json_encode($config);
        $result = $dashboard->save();
        return $this->success($result);
    }

    public function actionSaveFlowStage(
        $flow_temp_id = 0,
        $flow_id = 0,
        $flow_name = '',
        $enable_flag = 1,
        $require_flag = 0,
        array $scope = [],
        array $require_field = [],
        array $stages = []
    )
    {
        $this->validate([
            'flow_temp_id' => 'numeric',
            'flow_id' => 'numeric',
            'flow_name' => 'required|string',
            'require_field' => 'array',
            'require_flag' => 'required|numeric|between:0,1',
            'enable_flag' => 'numeric|between:0,1',
            'scope.department_list.*' => 'numeric',
            'scope.user_list.*' => 'numeric',
            'stages.*.stage_id' => 'numeric',
            'stages.*.flow_id' => 'numeric',
            'stages.*.name' => 'required|string',
            'stages.*.type' => 'required|numeric|between:1,3',
            'stages.*.success_rate' => 'required|numeric|between:0,100',
            'stages.*.tip' => 'string',
            'stages.*.remark' => 'string',
            'stages.*.review_flag' => 'required|numeric|between:0,1',
            'stages.*.require_field_setting' => 'array',
        ]);


        $user = User::getLoginUser();
        $clientId = $user->getClientId();
        $userId = $user->getUserId();

        //新增时候需要判断是否有添加的权限
        if (!$flow_id && !\common\library\privilege_v3\Helper::hasFunctional($clientId, \common\library\privilege_v3\PrivilegeConstants::FUNCTIONAL_OPPORTUNITY_SALES_FLOW)) {
            throw new RuntimeException(\Yii::t('opportunity', 'No permissions to add the sales flow'));
        }

        $api = new FlowApi($clientId);
        $api->setOpUser($userId);
        if ($flow_id) {
            $api->edit($flow_id, [
                'item_name' => trim($flow_name),
                'disable_flag' => $enable_flag ? 0 : 1,
                'require_field' => $require_field,
                'require_flag' => $require_flag,
            ]);
        } else {
            $flow_id = $api->create([
                'item_name' => trim($flow_name),
                'field_type' => 0, // 非主流程
                'disable_flag' => $enable_flag ? 0 : 1,
                'require_field' => $require_field,
                'require_flag' => $require_flag,
            ]);
        }

        $oldStages = [];
        if ($flow_id) {
            $winStageCount = 0;
            if (!empty($flow_id)) {
                $api = new StageApi($clientId);
                $api->setFlowId($flow_id);
                $api->setStageType(OpportunityStage::STAGE_WIN_STATUS);

                $api = new StageApi($clientId);
                $api->setFlowId($flow_id);
                $oldStages = array_column($api->listAll(), null, 'stage_id');

                // 获取本次提交的有效 stage_id (不为空且大于0)
                $submitStageIds = array_filter(array_column($stages, 'stage_id'), function($id) {
                    return !empty($id) && $id > 0;
                });
                // 提交的赢单阶段中，已存在的赢单阶段数量
                $winStageCount = array_sum(array_map(function($stageId) use ($oldStages) {
                    return isset($oldStages[$stageId]) && $oldStages[$stageId]['type'] == OpportunityStage::STAGE_WIN_STATUS ? 1 : 0;
                }, $submitStageIds));

            }

            // 前置判断是否符合要求
            $stageNameCount = array_count_values(array_column($stages, 'name'));
            foreach ($stages as $key => $stage) {
                if (($stageNameCount[$stage['name']] ?? 0) > 1) {
                    throw new RuntimeException(\Yii::t('opportunity', 'Under the same sales process, duplicate names are not allowed in the customer stage'));
                }

                if ($stage['type'] == Stage::STAGE_WIN_STATUS && empty($stage['stage_id'])) {
                    $winStageCount++;
                }

                $stages[$key]['flow_id'] = $flow_id;
            }

            if ($winStageCount > ($maxNum = Stage::STAGE_LIST_SAVE_MAX_LENGTH[OpportunityStage::STAGE_WIN_STATUS] ?? 0)){
                throw new \RuntimeException(\Yii::t('opportunity', 'The number of sales stages cannot exceed') . $maxNum);
            }

            // 准备保存数据
            $rank = 1;
            $api = new StageApi($clientId);


            // 删除不在提交列表中的阶段
            if (!empty($submitStageIds)) {
                $deleteStageIds = array_column(array_filter($oldStages, function($stage) use ($submitStageIds) {
                    return !in_array($stage['stage_id'], $submitStageIds);
                }), 'stage_id');
            } else {
                $deleteStageIds = array_column($oldStages, 'stage_id');
            }
            if (!empty($deleteStageIds)) {
                $api->delete($deleteStageIds);
            }

            foreach ($stages as $key => $stage) {
                //非输单需要过滤掉fail_type,fail_remark
                $require_field_setting = ($stage['type'] == OpportunityStage::STAGE_FAIL_STATUS) ? array_values(array_unique($stage['require_field_setting'] ?? [])) : array_values(array_unique(array_diff($stage['require_field_setting'] ?? [],['fail_type','fail_remark'])));
                if (!empty($stage['stage_id'])) {
                    $stageData = $api->edit($stage['stage_id'], [
                        'field_type' => intval($stage['type']),
                        'name' => $stage['name'],
                        'success_rate' => abs($stage['success_rate']),
                        'flow_id' => intval($flow_id),
                        'description' => $stage['remark'] ?? '',
                        'review_flag' => intval($stage['review_flag']),
                        'tip' => $stage['tip'] ?? '',
                        'order_rank' => $stage['rank'] ?? $rank,
                        'require_field_setting' => $require_field_setting,
                    ]);

                    if (!$stageData) {
                        throw new RuntimeException(\Yii::t('opportunity', 'Sales stage information does not exist'));
                    }

                    if (isset($oldStages[$stage['stage_id']])) {
                        unset($oldStages[$stage['stage_id']]);
                    }
                } else {
                    $stageId = $api->setOpUser($userId)->create([
                        'field_type' => intval($stage['type']),
                        'name' => $stage['name'],
                        'success_rate' => abs($stage['success_rate']),
                        'flow_id' => intval($flow_id),
                        'description' => $stage['remark'] ?? '',
                        'review_flag' => intval($stage['review_flag']),
                        'tip' => $stage['tip'] ?? '',
                        'order_rank' => $stage['rank'] ?? $rank,
                        'require_field_setting' => $require_field_setting,
                    ]);
                }
                $rank++;
            }

            if (!empty($oldStages)) {
                $api = new StageApi($clientId);
                $res = $api->delete(array_column($oldStages, 'stage_id'));
            }
        }
        
        return $this->success(['flow_id' => $flow_id]);
    }

    public function actionSaveDashFlowField(
        $flow_id,
        array $stages = []
    )
    {
        $this->validate([
            'flow_id' => 'required|numeric',
            'stages.*.stage_id' => 'numeric',
            'stages.*.stage_field' => 'array',
        ]);

        $user = \User::getLoginUser();
        $clientId = $user->getClientId();
        $userId = $user->getUserId();

        $setting = new UserSetting($clientId, $userId,UserSetting::OPPORTUNITY_DASH_FLOW_FIELD);
        $dash_flow_field = $setting->getValue();
        $dash_flow_field = json_decode($dash_flow_field ?? '{}', true);

        foreach ($stages as $stage) {
            $stage_field = array_values(array_unique(array_filter($stage['stage_field'] ?? [], 'is_string')));
            if (count($stage_field) > \common\library\setting\library\stage\Stage::DASHBORAD_STAGE_FIELD_COUNT) {
                throw new RuntimeException(\Yii::t('opportunity', 'Dashboard display field exceeds maximum quantity'));
            }
            $dash_flow_field[$flow_id][$stage['stage_id']] = $stage_field;
        }
        $setting->value = json_encode($dash_flow_field);
        $result = $setting->save();
        return $this->success(['data' => $result]);
    }

    //增加必填字段后，给每个流程的每个阶段都加上该字段
    public function actionUpdateAllStageRequireSetting($fieldId)
    {
        $this->validate([
            'fieldId' => 'required|string'
        ]);

        //输单阶段特殊处理
        $failStageFlag = false;
        if ($fieldId == 'department') {
            return $this->success([]);
        } elseif (in_array($fieldId, ['fail_type','fail_remark'])) {
            $failStageFlag = true;
        }

        $user = \User::getLoginUser();
        $clientId = $user->getClientId();

        $fieldInfo = $this->custom_field_service->field($fieldId);
        if (empty($fieldInfo) || $fieldInfo['require'] != 1) {
            throw new RuntimeException('不是必填字段');
        }

        $api = new StageApi($clientId);
        $stageList = $api->listAll();

        $flowApi = new \common\library\setting\library\flow\FlowApi($clientId);
        $flowList = $flowApi->listAll();
        $requireFlowMap = array_column($flowList,'require_flag','flow_id');

        foreach ($flowList as $flow) {
            //是否启用自定义设置必填
            $require_flag = $requireFlowMap[$flow['flow_id']];
            if (!$require_flag) {
                continue;
            }
            $flowApi->edit($flow['flow_id'],[
                'require_field' => array_values(array_unique(array_merge($flow['require_field'] ?? [],[$fieldId])))
            ]);
        }

        foreach ($stageList as $stage) {
            //是否启用自定义设置必填
            $require_flag = $requireFlowMap[$stage['flow_id']] ?? 0;
            if (!$require_flag) {
                continue;
            }

            //输单才需要添加fail_type,fail_remark
            if ($failStageFlag && $stage['type'] != \common\library\setting\library\stage\Stage::STAGE_FAIL_STATUS) {
                continue;
            }
            
            $inputData = [
                'success_rate' => $stage['success_rate'] ?? 0,
                'review_flag' => $stage['review_flag'] ?? 0,
                'require_field_setting' => array_values(array_unique(array_merge($stage['require_field_setting'] ?? [],[$fieldId])))
            ];
            $api->setSkipEvent();
            $api->edit($stage['stage_id'], $inputData);
        }
        return $this->success([]);
    }
}
