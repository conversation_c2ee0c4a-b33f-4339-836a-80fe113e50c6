<?php
use common\library\custom_field\CustomFieldWriteActions;
use common\library\product_v2\sku\SkuAPI;
use common\library\serial\actions\SNRuleWriteActions;
use common\library\setting\item\BatchItemSetting;
use common\library\setting\item\ItemSetting;
use common\library\setting\item\ItemSettingAPI;
use common\library\setting\library\attributes\AttributesApi;
use common\library\setting\library\group\GroupApi;
use common\library\product_v2\setting\ProductSetting;

/**
 * 产品字段设置
 *
 * <AUTHOR> <<EMAIL>>
 */
class ProductSettingWriteController extends Controller
{

    use CustomFieldWriteActions, SNRuleWriteActions;

    protected function beforeAction($action)
    {
        $this->initCustomFieldService(Constants::TYPE_PRODUCT);
        $this->initSNRuleService(Constants::TYPE_PRODUCT);
        return parent::beforeAction($action);
    }

    /**
     * 设置产品配置
     * @param $product_id 产品ID
     * @param $key 配置key
     * @param $value
     * @return false|string
     */
    public function actionProductSetting($product_id, $key, $value)
    {
        $keyStr = implode(',', array_keys(ProductSetting::KEY_RULE_MAP));
        $this->validate([
            'product_id' => 'required',
            'key'        => 'required|in:'.$keyStr,
            'value'      => 'required|string',
        ]);
        $value = json_decode($value, true);
        if (empty($value) || !is_array($value)) {
            throw new \RuntimeException(\Yii::t('product', '配置值不能为空'));
        }
        $validateRule = ProductSetting::KEY_RULE_MAP[$key]['rule'] ?? [];
        $validateMsg  = ProductSetting::KEY_RULE_MAP[$key]['message'] ?? [];
        if (!empty($validateRule)) {
            $this->validate($validateRule, $value, $validateMsg);
        }
        $user = \User::getLoginUser();
        $settingObject = new ProductSetting($user->getClientId());
        $settingObject->loadByProductKey($product_id, $key);
        $attrbuties = [
            'product_id' => $product_id,
            'key'        => $key,
            'value'      => $value,
        ];
        if ($settingObject->isExist()) {
            if ($value == $settingObject->value) {
                return $this->success($value);
            }
            $attrbuties['update_time'] = date('Y-m-d H:i:s');
            $attrbuties['update_user'] = $user->getUserId();
            $settingObject->bindAttrbuties($attrbuties);
            $settingObject->update();
        } else {
            $attrbuties['create_user'] = $user->getUserId();
            $attrbuties['update_user'] = $user->getUserId();
            $settingObject->bindAttrbuties($attrbuties);
            $settingObject->create();
        }
        return $this->success($value);
    }

    public function actionGroupSave(
        $name,
        $group_id=0,
        $parent_id='',
        $description=null,
        $rank=null,
        $members = null
    ){
        $user = User::getLoginUser();
        $clientId  = $user->getClientId();


//        $this->validate(
//            ['name' => ['required', 'not_regex:[>]','not_regex:[<]', 'max:' . \common\library\group\Group::NAME_MAX_LEN],],
//            [],
//            ['name.not_regex' => \Yii::t('customer', 'Group name cannot be included') . ' > '.\Yii::t('customer', 'and').' < ']
//        );
//
//        $group =new \common\library\group\Group($clientId,Constants::TYPE_PRODUCT,$group_id);
//        if( $group_id >0 && $group->isNew() )
//            throw new RuntimeException(\Yii::t('customer', 'Group does not exist'));
//
//        if($description !== null)
//            $group->description = $description;
//        if($rank !== null)
//            $group->rank = $rank;
//
//        if( $parent_id !=='' ){
//            $group->parent_id = $parent_id;
//        }
//
//        $group->update_user = $user->getUserId();
//        $group->update_time = date("Y-m-d H:i:s");
//        $group->name = $name;
//
//        $group->save();

        $api = new \common\library\product_v2\group\ProductGroupApi($clientId);
        $api->setOpUser($user);
        $data = $api->saveGroup($group_id, [
            'item_name' =>  $name,
            'description' => $description,
            'order_rank' => $rank,
            'parent_id'  => $parent_id === '' ? null : $parent_id,
        ], $members);

        return $this->success($data);
    }

    /**
     * @param $group_id     // 要挪动的分组
     * @param $parent_id    // 挪动后的父分组
     * @param $prev_group_id    // 挪动后，被挪动的分组的前一个分组
     * @return void
     */
    public function actionMove($group_id, $parent_id, $prev_group_id){
        $user = User::getLoginUser();
        $clientId  = $user->getClientId();
        $api = new \common\library\product_v2\group\ProductGroupApi($clientId);
        $api->setOpUser($user);
        $api->moveProductGroup($group_id, $parent_id, $prev_group_id);

        return $this->success();
    }

    public function actionGroupDelete($group_id){
        $this->validate([
            'group_id' => 'required|integer',
        ]);

        $user = User::getLoginUser();
        $clientId  = $user->getClientId();
        $userId = $user->getUserId();

        $groupApi = new GroupApi($clientId, Constants::TYPE_PRODUCT);
        if ($groupApi->isSystemSetting($group_id)) {
            throw  new RuntimeException(\Yii::t('customer', 'System group cannot be deleted'));
        }

        $count = \common\library\customer\Helper::getProductGroupCompanyCount($userId, $clientId, $group_id);
        if ($count > 0) {
            throw  new RuntimeException(\Yii::t('customer', 'group is refer by company, can not delete'));
        }

        $deleted = $groupApi->delete($group_id);

        return $this->success($deleted);
//
//        if( array_key_exists($group_id,\common\library\setting\library\group\GroupMetadata::getExtraDataMap(Constants::TYPE_PRODUCT)))
//            throw  new RuntimeException(\Yii::t('customer', 'System group cannot be deleted'));
//
//        $group =new \common\library\group\Group($clientId,Constants::TYPE_PRODUCT,$group_id);
//        if( $group_id >0 && $group->isNew() )
//            throw new RuntimeException(\Yii::t('customer', 'Group does not exist'));
//
//        $group->delete();

//        return $this->success('');

    }


    public function actionGroupOrderSave($group_ids){
        $user = User::getLoginUser();
        $clientId  = $user->getClientId();

        if(empty($group_ids)){
            throw new RuntimeException(\Yii::t('customer', 'Please submit id'));
        }

        $group_ids = array_map('intval', explode(",", $group_ids));

        \common\library\group\Helper::updateGroupRank($clientId,Constants::TYPE_PRODUCT,$group_ids);

        return $this->success('');

    }




    public function actionGroupBatchDelete($group_ids){
        $user = User::getLoginUser();
        $clientId  = $user->getClientId();
        if(empty($group_ids))
            throw new RuntimeException(\Yii::t('customer', 'Please submit id'));

        $group_ids = array_map('intval', explode(",", $group_ids));

        $rows = (new GroupApi($clientId, Constants::TYPE_PRODUCT))->delete($group_ids);

        return $this->success($rows);

    }


    public function actionSetExportConfig($setting_product_export_language = 'en')
    {
        if (empty($setting_product_export_language)
            || !in_array($setting_product_export_language, [\Constants::LANGUAGE_EN, \Constants::LANGUAGE_ZH])
        ) {
            return $this->fail(-1, \Yii::t('common', 'Parameter error'));
        }

        $user = User::getLoginUser();
        $client = \common\library\account\Client::getClient($user->getClientId());
        $client->setSettingAttributes([\common\library\account\Client::SETTING_KEY_PRODUCT_EXPORT_LANGUAGE => $setting_product_export_language]);
        $client->saveSettingAttributes();

        return $this->success([]);
    }

    /**
     * @param $item_name
     * @param array $node
     * @param int $item_id
     * @throws \xiaoman\orm\exception\OrmException
     * @throws \xiaoman\orm\exception\QueryException
     */
    public function  actionSaveSkuAttribute($item_name,array $node,$attribute_group_id, $item_id=0)
    {
        $this->validate([
            'item_id' => 'integer',
            'item_name' => 'required|string|max:64',
            'attribute_group_id' => 'integer',
            'node' => 'required|array',
            'node.*.item_id' => 'required|integer',
            'node.*.item_name' => 'required|string|max:64'
        ]);

        $user = User::getLoginUser();
        $clientId = $user->getClientId();
        $attributeApi = new AttributesApi($clientId, Constants::TYPE_PRODUCT);

//        $itemSetting = new ItemSetting($clientId, $item_id);
//        $itemSetting->setDomainHandler($user);
//        $itemSetting->module = Constants::TYPE_PRODUCT;
//        $itemSetting->item_type = 1;
//        $itemSetting->field_type = 0;
        $itemSetting = $attributeApi->findOne($item_id);
        if( $itemSetting->isExist() )
        {
            $skuApi = new SkuAPI($clientId, $user->getUserId());

            if ($itemSetting->item_name != $item_name) {
                // 被使用规格不允许改名
                $hasUsed = $skuApi->whetherUsingSkuAttr($item_id);
                if ($hasUsed) {
                    throw new RuntimeException(Yii::t('product', 'The specification has been used and cannot be modified'));
                }
            }

            if (!isset($hasUsed) || true === $hasUsed) {
                // 检查规格值是否被使用
                $existValues = $attributeApi->getSkuValuesOfAttr($item_id, $itemSetting);
                $existValues = $existValues->getAttributes(['item_id', 'item_name']);
                $modifiedValues = ItemSettingAPI::diffNodes($existValues, $node, true);

                if ($skuApi->whetherUsingSkuValues($item_id, array_column($modifiedValues, 'item_id'))) {
                    throw new RuntimeException(Yii::t('product', 'The specification value has been used and cannot be modified or deleted'));
                }
            }

            $attributeApi->edit($item_id, [
                'item_name' => $item_name,
                'relate_id' => $attribute_group_id,
            ], $node);
            $attributeApi->resort($item_id, $node);
        }else
        {
            $attributeApi->create([
                'item_name' => $item_name,
                 'relate_id' => $attribute_group_id,
            ], 0, 0, $node);
        }

        $this->success();
    }

    /**
     * 添加规格值
     * @param $item_id
     * @param array $attributes
     * @return void
     * @throws \xiaoman\orm\exception\OrmException
     * @throws \xiaoman\orm\exception\QueryException
     */
    public function actionAddSkuAttribute($item_id, array $attributes)
    {
        $this->validate([
            'item_id'      => 'integer',
            'attributes'   => 'required|array',
            'attributes.*' => 'required|string|max:64',
        ]);

        $user = User::getLoginUser();
        $clientId = $user->getClientId();
        $attributeApi = new AttributesApi($clientId, Constants::TYPE_PRODUCT);

        $itemSetting = $attributeApi->findOne($item_id);
        if(!$itemSetting->isExist())
        {
            throw new RuntimeException(Yii::t('product', 'The specification has been used and cannot be modified'));
        }

        $attributes = array_unique(array_filter($attributes));
        $existValues = $attributeApi->getSkuValuesExistNames($item_id, $itemSetting, $attributes);
        if (!empty($existValues) && ($existValues = array_intersect($attributes, $existValues))) {
            throw new RuntimeException(Yii::t('product', 'The name already exists') . ': ' . implode(',', array_values($existValues)));
        }

        $attributeApi->addItem($item_id, $attributes, $user->getUserId());

        $this->success();
    }

    /**
     * 删除产品规格，删除是整个规格类型
     * @param $item_id
     * @throws \xiaoman\orm\exception\OrmException
     */
    public function actionDeleteSkuAttribute($item_id)
    {
        $this->validate([
            'item_id' => 'required|integer'
        ]);
        $user = User::getLoginUser();
        $clientId = $user->getClientId();
        $attributesApi = new AttributesApi($clientId, \Constants::TYPE_PRODUCT);
        if (!$attributesApi->exist($item_id)) {
            throw new RuntimeException(Yii::t('product', 'The specifications don\'t exist'));
        }

        $skuApi = new SkuAPI($clientId, $user->getUserId());
        if ($skuApi->whetherUsingSkuAttr($item_id)) {
            throw new \RuntimeException(Yii::t('product', 'Delete failed, the specification has been used, and it is not allowed to delete'));
        }

        $attributesApi->delete($item_id);

        $this->success([]);
    }


    /**
     * 编辑某个规格类型名称，规格值名称
     * @param $item_id
     * @param $item_name
     * @return false|string
     */
    public function actionEditSkuAttribute($item_id, $item_name)
    {
        $this->validate([
            'item_id'   => 'required|integer',
            'item_name' => 'required|string',
        ]);

        $user = User::getLoginUser();
        $clientId = $user->getClientId();
        $attributesApi = new \common\library\setting\item\ProductAttributeApi($clientId);
        $attributesApi->editName($item_id, $item_name, $user->getUserId());
        return $this->success();
    }

    /**
     * 删除某个规格类型、规格值
     * @param $item_id
     * @return false|string
     */
    public function actionDelSkuAttribute($item_id)
    {
        $this->validate([
            'item_id' => 'required|integer'
        ]);
        $user = User::getLoginUser();
        $clientId = $user->getClientId();
        $attributesApi = new \common\library\setting\item\ProductAttributeApi($clientId);
        $attributesApi->deleteItem($item_id, $user->getUserId());
        return $this->success([]);
    }

    /**
     * 设置某个规格值置顶
     * @param $item_id
     * @return false|string
     */
    public function actionTopSkuAttribute($item_id)
    {
        $this->validate([
            'item_id' => 'required|integer'
        ]);
        $user = User::getLoginUser();
        $clientId = $user->getClientId();
        $attributesApi = new \common\library\setting\item\ProductAttributeApi($clientId);
        $attributesApi->topItem($item_id, $user->getUserId());
        return $this->success([]);
    }

    /**
     * 添加规格分组
     * @param $attribute_group_name
     * @return void
     * @throws \xiaoman\orm\exception\OrmException
     * @throws \xiaoman\orm\exception\QueryException
     */
    public function actionAddAttributeGroup($attribute_group_name)
    {
        $this->validate([
            'attribute_group_name' => 'required|string',
        ]);

        $user = User::getLoginUser();
        $clientId = $user->getClientId();
        $attributeGroupApi = new \common\library\product_v2\attribute_group\ProductAttributeGroupApi($clientId);
        if($attributeGroupApi->checkNameExist($attribute_group_name))
        {
            throw new RuntimeException(Yii::t('product', 'The product_attribute_group_name already exists'));
        }
        $attributeGroupApi->createAttributeGroup($attribute_group_name);
        $this->success();
    }

    /**
     * 编辑某个规格分组名称
     * @param $iattribute_group_id
     * @param $attribute_group_name
     * @return false|string
     */
    public function actionEditAttributeGroup($attribute_group_id, $attribute_group_name)
    {
        $this->validate([
            'attribute_group_id'   => 'required|integer',
            'attribute_group_name' => 'required|string',
        ]);
        if($attribute_group_id == Constants::UN_GROUP)
        {
            throw new \RuntimeException(\Yii::t('product', '未分组不能编辑'));
        }
        $user = User::getLoginUser();
        $clientId = $user->getClientId();
        $attributeGroupApi = new \common\library\product_v2\attribute_group\ProductAttributeGroupApi($clientId);
        if($attributeGroupApi->checkNameExist($attribute_group_name))
        {
            throw new RuntimeException(Yii::t('product', 'The product_attribute_group_name already exists'));
        }
        $attributeGroupApi->editAttributeGroup($attribute_group_id,$attribute_group_name);
        return $this->success();
    }

    /**
     * 删除某个规格分组
     * @param $attribute_group_id
     * @return false|string
     */
    public function actionDeleteAttributeGroup($attribute_group_id)
    {
        $this->validate([
            'attribute_group_id' => 'required|integer'
        ]);
        if($attribute_group_id==0){
            throw  new RuntimeException(\Yii::t('product_attribute_group', 'Ungrouped cannot be deleted'));
        }
        $user = User::getLoginUser();
        $clientId = $user->getClientId();
        $attributeGroupApi = new \common\library\product_v2\attribute_group\ProductAttributeGroupApi($clientId);
        $attributeGroupApi->deleteAttributeGroup($attribute_group_id);
        return $this->success([]);
    }

    /**
     * 判断规格分组名称是否存在
     * @param $attribute_group_name
     * @return false|string
     */
    public function actionIsExistAttributeGroupName($attribute_group_name)
    {
        $this->validate([
            'attribute_group_name' => 'required|string',
        ]);
        $attribute_group_name = trim($attribute_group_name);
        $user = User::getLoginUser();
        $clientId = $user->getClientId();
        $attributeGroupApi = new \common\library\product_v2\attribute_group\ProductAttributeGroupApi($clientId);
        $is_exist = $attributeGroupApi->checkNameExist($attribute_group_name);
        $exist_list = [];
        if($is_exist){
            $exist_list[] = $attribute_group_name;
        }
        return $this->success(['is_exist' => $is_exist, 'exist_list' => $exist_list]);
    }

    /**
     * 推拽排序规格分组
     * @param $data
     */
    public function actionSortProductAttribute(array $data)
    {
        $this->validate([
            'data' => 'required|array',
        ]);

        $clientId = $this->getLoginUserClientId();
        \common\library\product_v2\attribute_group\Helper::batchUpdateProductAttributeGroupOrder($clientId,$data);

        return $this->success([]);
    }

    /**
     * 变更规格分组
     * @param $attribute_group_id
     * @return false|string
     */
    public function actionChangeAttributeGroup($attribute_group_id,$item_id)
    {
        $this->validate([
            'attribute_group_id'   => 'required|integer',
            'item_id'   => 'required|integer',
        ]);

        $clientId = $this->getLoginUserClientId();
        $nodeBatchObj = new BatchItemSetting($clientId);
        $nodeBatchObj->getOperator()->updateRelateGroupId($item_id,$attribute_group_id);

        return $this->success([]);
    }

    /**
     * 判断规格类型是否存在
     * @param $item_name
     * @return false|string
     */
    public function actionIsExistItemName($item_name)
    {
        $this->validate([
            'item_name' => 'required|string',
        ]);
        $item_name = trim($item_name);
        $clientId = $this->getLoginUserClientId();
        $attributeGroupApi = new \common\library\product_v2\attribute_group\ProductAttributeGroupApi($clientId);
        $res = $attributeGroupApi->checkItemNameExist($item_name);
        return $this->success($res);
    }

    /**
     * 复制规格类型
     * @param $item_id
     * @return false|string
     */
    public function actionCopyAttributeItem($item_id)
    {
        $this->validate([
            'item_id' => 'required|integer',
        ]);
        $clientId = $this->getLoginUserClientId();
        $attributeGroupApi = new \common\library\product_v2\attribute_group\ProductAttributeGroupApi($clientId);
        $res = $attributeGroupApi->copyAttributeItem($item_id);
        return $this->success($res);
    }

}
