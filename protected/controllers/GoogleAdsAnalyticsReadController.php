<?php

use common\library\google_ads\ads\GoogleAdsAccountList;
use common\library\google_ads\GoogleConstants;
use common\library\google_ads\ads_analytics\date_metrics\AdsDateMetricsList;
use common\library\google_ads\ads\AdsAccountService;
use common\library\google_ads\ads_analytics\geo_country_metrics\GeoCountryMetricsList;
use common\library\google_ads\ads_analytics\search_keywords_metrics\SearchKeywordsMetricsList;
use common\library\google_ads\ads_analytics\device_metrics\DeviceMetricsList;
use common\library\google_ads\ads_analytics\hour_metrics\HourMetricsList;

/**
 * Created by PhpStorm.
 * User: bing
 * Date: 2020/02/15
 * Time: 15:45
 */

class GoogleAdsAnalyticsReadController extends Controller {

    /**
     * 获取ads_account_ids
     */
    public function actionAdsAccountIds() {
        $user = User::getLoginUser();
        $clientId = $user->getClientId();
        $googleAdaList = new GoogleAdsAccountList($clientId);
        $googleAdaList->setJoinFields(['access_status']);
        $googleAdaList->setOrderBy(['is_default', 'bind_time']);
        $googleAdaList->setOrder('desc');
        $googleAdaList->setEnableFlag(GoogleConstants::ADS_ACCOUNT_ENABLE);
        $googleAdaList->getFormatter()->adsAccountIdsSetting();
        $result = $googleAdaList->find();
        $this->success($result);
    }

    /**
     * @param $ads_account_id
     * @param $start_date
     * @param $end_date
     */
    public function actionAdsAnalyticsCharts(
        $ads_account_id,
        $start_date = '',
        $end_date = ''
    ) {
        $user = User::getLoginUser();
        $client_id = $user->getClientId();

        $dateMetrics = new AdsDateMetricsList($client_id, $ads_account_id);
        $dateMetrics->setStartDate($start_date);
        $dateMetrics->setEndDate($end_date);
        $dateMetrics->setOrderBy('analytics_date');
        $dateMetrics->setOrder('ASC');
        $dateMetrics->getFormatter()->adsDateAnalyticsChartSetting();
        $result = $dateMetrics->find();

        $lead_count = \common\library\google_ads\lead\SessionLeadHelper::getAdsLeadCount($client_id, $ads_account_id, $start_date, $end_date);

        if(isset($result['summary'])) {
            $result['summary']['lead_count'] = $lead_count;
        }

        $this->success($result);
    }

    /**
     * @param $ads_account_id
     * @param string $model
     * @param string $start_date
     * @param string $end_date
     * @param string $sort_type
     * @param string $sort_field
     * @param int $cur_page
     * @param int $page_size
     * @throws ProcessException
     */
    public function actionAdsCamaignList(
        $ads_account_id,
        $model = '',
        $start_date = '',
        $end_date = '',
        $sort_type = 'DESC',
        $sort_field = 'cost',
        $cur_page = 1,
        $page_size = 20
    ) {
        $user = User::getLoginUser();
        $client_id = $user->getClientId();

        $dateMetrics = new AdsDateMetricsList($client_id, $ads_account_id);
        $dateMetrics->setFields([
            'campaign_id',
            'campaign_name',
            'SUM(`cost_micros`) AS total_cost_micros',
            'SUM(`impressions`) AS total_impressions',
            'SUM(`clicks`) AS total_clicks',
            'SUM(`conversions`) AS total_conversions',
            'SUM(`interactions`) AS total_interactions',
        ]);
        $dateMetrics->setStartDate($start_date);
        $dateMetrics->setEndDate($end_date);
        $dateMetrics->setGroupByFields('campaign_id');
        $dateMetrics->setOffset(($cur_page-1) * $page_size);
        $dateMetrics->setLimit($page_size);
        $dateMetrics->getFormatter()->adsCampaignListSetting();
        // 自定义统计数据后排序
        $dateMetrics->getFormatter()->setSort($sort_field,$sort_type);

        $dateMetrics->find();

        $this->success([
            'list' => $dateMetrics->find(),
            'count' => (int)$dateMetrics->countGroup()
        ]);
    }

    /**
     * @param $ads_account_id
     * @param string $model
     * @param string $start_date
     * @param string $end_date
     */
    public function actionAdsCampaignGeoCountryDistribute(
        $ads_account_id,
        $model = '',
        $start_date = '',
        $end_date = ''
    ) {
        $user = User::getLoginUser();
        $client_id = $user->getClientId();

        $geoMetrics = new GeoCountryMetricsList($client_id, $ads_account_id);

        $geoMetrics->setFields([
            'country_iso_code',
            'SUM(`cost_micros`) AS total_cost_micros',
        ]);
        $geoMetrics->setStartDate($start_date);
        $geoMetrics->setEndDate($end_date);
        $geoMetrics->setMinCostMicros(0);
        $geoMetrics->setGroupByFields('country_iso_code');
        $geoMetrics->getFormatter()->geoCountryDistributeMetricsSetting();
        $geoMetrics->setOrderBy('total_cost_micros');
        $geoMetrics->setOrder('DESC');

        $result = $geoMetrics->find();

        $this->success($result);

    }

    /**
     * @param $ads_account_id
     * @param string $model
     * @param string $start_date
     * @param string $end_date
     * @param string $sort_type
     * @param string $sort_field
     * @param int $cur_page
     * @param int $page_size
     * @throws ProcessException
     */
    public function actionAdsCampaignGeoCountryList(
        $ads_account_id,
        $model = '',
        $start_date = '',
        $end_date ='',
        $sort_type = 'DESC',
        $sort_field = 'cost',
        $cur_page = 1,
        $page_size = 20
    ) {

        $user = User::getLoginUser();
        $client_id = $user->getClientId();

        $geoMetrics = new GeoCountryMetricsList($client_id, $ads_account_id);

        $geoMetrics->setFields([
            'country_iso_code',
            'SUM(`cost_micros`) AS total_cost_micros',
            'SUM(`impressions`) AS total_impressions',
            'SUM(`clicks`) AS total_clicks',
            'SUM(`conversions`) AS total_conversions',
            'SUM(`interactions`) AS total_interactions',
        ]);

        $geoMetrics->setStartDate($start_date);
        $geoMetrics->setEndDate($end_date);
        // 国家分组
        $geoMetrics->setGroupByFields('country_iso_code');
        $geoMetrics->getFormatter()->geoCountryListMetricsSetting();
        // 自定义统计数据后排序
        $geoMetrics->getFormatter()->setSort($sort_field, $sort_type,($cur_page-1) * $page_size, $page_size);

        $this->success([
            'list' => $geoMetrics->find(),
            'count' => (int)$geoMetrics->countGroup()
        ]);
    }

    /**
     * @param $ads_account_id
     * @param string $model
     * @param string $start_date
     * @param string $end_date
     * @param string $sort_type
     * @param string $sort_field
     * @param int $cur_page
     * @param int $page_size
     * @return false|string
     */
    public function actionAdsSearchKeywordsList(
        $ads_account_id,
        $model = '',
        $start_date = '',
        $end_date = '',
        $sort_type = 'DESC',
        $sort_field = 'cost',
        $cur_page = 1,
        $page_size = 20
    ){
        $user = User::getLoginUser();
        $clientId = $user->getClientId();

        $searchKeywordsList = new SearchKeywordsMetricsList($clientId);

        if(!in_array($sort_field, GoogleConstants::COMMON_METRICS_FIELDS)) {
            $sort_field = GoogleConstants::COST;
        }

        //相同值排序, 分页会混乱的
        if( $sort_field )
        {
            $sort_field = [$sort_field, 'criterion_id'];
        }

        $searchKeywordsList->setFields([
            'criterion_id',
            'campaign_id',
            'ad_group_id',
            'SUM(`cost_micros`)*1.0/1000000 AS cost',
            'SUM(`impressions`) AS impressions',
            'SUM(`clicks`) AS clicks',
            'SUM(`conversions`) AS conversions',
            'SUM(`interactions`) AS interactions',
            'SUM(`clicks`)*1.0/SUM(`impressions`) AS click_rate',
            'SUM(`conversions`)*1.0/SUM(`interactions`) as conversions_rate',
            '(SUM(`cost_micros`)/1000000)/SUM(`clicks`) AS average_cpc',
            'SUM(`cost_micros`)*1.0/SUM(`conversions`)/1000000 AS cost_per_conversion',
            'currency'
        ]);

        $searchKeywordsList->setStartDate($start_date);
        $searchKeywordsList->setEndDate($end_date);
        $searchKeywordsList->setAdsAccountId($ads_account_id);
        $searchKeywordsList->setGroupByFields('criterion_id');
        $searchKeywordsList->setLimit($page_size);
        $searchKeywordsList->setOffset(($cur_page-1) * $page_size);
        $searchKeywordsList->setOrderBy($sort_field);
        $searchKeywordsList->setOrder($sort_type);
        $searchKeywordsList->getFormatter()->showCampaignListFlag(true);
        $searchKeywordsList->getFormatter()->showKeywordListFlag(true);

        $count = $searchKeywordsList->count();
        $list = $count > 0 ? $searchKeywordsList->find() : [];

        return $this->success([
            'list' => $list,
            'count' => $count
        ]);
    }

    /**
     * @param $ads_account_id
     * @param string $model
     * @param string $metrics_type
     * @param string $start_date
     * @param string $end_date
     */
    public function actionAdsDeviceDistribute(
        $ads_account_id,
        $model = '',
        $metrics_type = GoogleConstants::IMPRESSIONS,
        $start_date = '',
        $end_date = ''
    ) {
        $user = User::getLoginUser();
        $client_id = $user->getClientId();

        if(!in_array($metrics_type, [GoogleConstants::IMPRESSIONS, GoogleConstants::CLICKS])) {
            throw new \RuntimeException(\Yii::t('google','Metrics type is wrong'));
        }

        $deviceMetrics = new DeviceMetricsList($client_id, $ads_account_id);

        if($metrics_type == GoogleConstants::IMPRESSIONS) {
            $deviceMetrics->setFields([
                'device_type',
                'SUM(`impressions`) AS impressions'
            ]);

            $deviceMetrics->getFormatter()->impressionMetricsSetting();

        }else if($metrics_type == GoogleConstants::CLICKS) {
            $deviceMetrics->setFields([
                'device_type',
                'SUM(`clicks`) AS clicks'
            ]);

            $deviceMetrics->getFormatter()->clickMetricsSetting();
        }

        $deviceMetrics->setStartDate($start_date);
        $deviceMetrics->setEndDate($end_date);
        $deviceMetrics->setGroupByFields('device_type');
        $result = $deviceMetrics->find();

        $this->success($result);
    }

    /**
     * @param $ads_account_id
     * @param string $model
     * @param string $metrics_type
     * @param string $start_date
     * @param string $end_date
     */
    public function actionAdsHourDistribute(
        $ads_account_id,
        $model = '',
        $metrics_type = GoogleConstants::IMPRESSIONS,
        $start_date = '',
        $end_date = ''
    ) {
        $user = User::getLoginUser();
        $client_id = $user->getClientId();

        if(!in_array($metrics_type, [GoogleConstants::IMPRESSIONS, GoogleConstants::CLICKS])) {
            throw new \RuntimeException(\Yii::t('google','Metrics type is wrong'));
        }

        $hourMetrics = new HourMetricsList($client_id, $ads_account_id);

        if($metrics_type == GoogleConstants::IMPRESSIONS) {
            $hourMetrics->setFields([
                'hour',
                'SUM(`impressions`) AS impressions'
            ]);

            $hourMetrics->getFormatter()->impressionMetricsSetting();

        }else if($metrics_type == GoogleConstants::CLICKS) {
            $hourMetrics->setFields([
                'hour',
                'SUM(`clicks`) AS clicks'
            ]);

            $hourMetrics->getFormatter()->clickMetricsSetting();
        }

        $hourMetrics->setStartDate($start_date);
        $hourMetrics->setEndDate($end_date);
        $hourMetrics->setGroupByFields('hour');
        $result = $hourMetrics->find();

        $this->success($result);
    }

    public function actionDownLoadSearchKeywordList(
        $ads_account_id,
        $start_date = '',
        $end_date = '',
        $sort_type = 'DESC',
        $sort_field = 'cost'
    )
    {
        if(!in_array($sort_field, GoogleConstants::COMMON_METRICS_FIELDS)) {
            $sort_field = GoogleConstants::COST;
        }

        //相同值排序, 分页会混乱的
        if( $sort_field )
        {
            $sort_field = [$sort_field, 'criterion_id'];
        }

        $user = User::getLoginUser();
        $clientId = $user->getClientId();

        $searchKeywordsList = new SearchKeywordsMetricsList($clientId);

        $searchKeywordsList->setFields([
            'criterion_id',
            'campaign_id',
            'ad_group_id',
            'SUM(`cost_micros`)*1.0/1000000 AS cost',
            'SUM(`impressions`) AS impressions',
            'SUM(`clicks`) AS clicks',
            'SUM(`conversions`) AS conversions',
            'SUM(`clicks`)*1.0/SUM(`impressions`) AS click_rate',
            'SUM(`conversions`)*1.0/SUM(`interactions`) as conversions_rate',
            '(SUM(`cost_micros`)/1000000)/SUM(`clicks`) AS average_cpc',
            'SUM(`cost_micros`)*1.0/SUM(`conversions`)/1000000 AS cost_per_conversion'
        ]);

        $searchKeywordsList->setStartDate($start_date);
        $searchKeywordsList->setEndDate($end_date);
        $searchKeywordsList->setAdsAccountId($ads_account_id);
        $searchKeywordsList->setGroupByFields('criterion_id');
        $searchKeywordsList->setOrderBy($sort_field);
        $searchKeywordsList->setOrder($sort_type);
        $searchKeywordsList->getFormatter()->showCampaignListFlag(true);
        $searchKeywordsList->getFormatter()->showKeywordListFlag(true);

        $list = $searchKeywordsList->find();

        $adsService = new \common\library\google_ads\ads_analytics\AdsAnalyticsService($clientId);

        $adsService->downLoadSearchKeywordList($list, $ads_account_id);
    }

    /**
     * @param $ads_account_id
     * @param string $model
     * @param string $start_date
     * @param string $end_date
     * @param string $sort_type
     * @param string $sort_field
     */
    public function actionDownLoadCampaignList(
        $ads_account_id,
        $model = '',
        $start_date = '',
        $end_date = '',
        $sort_type = 'DESC',
        $sort_field = 'cost'
    ) {
        $user = User::getLoginUser();
        $client_id = $user->getClientId();

        $dateMetrics = new AdsDateMetricsList($client_id, $ads_account_id);
        $dateMetrics->setFields([
            'campaign_id',
            'campaign_name',
            'SUM(`cost_micros`) AS total_cost_micros',
            'SUM(`impressions`) AS total_impressions',
            'SUM(`clicks`) AS total_clicks',
            'SUM(`conversions`) AS total_conversions',
            'SUM(`interactions`) AS total_interactions',
        ]);
        $dateMetrics->setStartDate($start_date);
        $dateMetrics->setEndDate($end_date);
        $dateMetrics->setGroupByFields('campaign_id');
        $dateMetrics->getFormatter()->adsCampaignListSetting();
        $dateMetrics->getFormatter()->setSort($sort_field, $sort_type);

        $result = $dateMetrics->find();

        $adsService = new \common\library\google_ads\ads_analytics\AdsAnalyticsService($client_id);

        $adsService->downLoadCampaignList($result,$ads_account_id);

    }

    /**
     * @param $ads_account_id
     * @param string $model
     * @param string $start_date
     * @param string $end_date
     * @param string $sort_type
     * @param string $sort_field
     */
    public function actionDownLoadGeoCountryList(
        $ads_account_id,
        $model = '',
        $start_date = '',
        $end_date ='',
        $sort_type = 'DESC',
        $sort_field = 'cost'
    ) {

        $user = User::getLoginUser();
        $client_id = $user->getClientId();

        $geoMetrics = new GeoCountryMetricsList($client_id, $ads_account_id);

        $geoMetrics->setFields([
            'country_iso_code',
            'SUM(`cost_micros`) AS total_cost_micros',
            'SUM(`impressions`) AS total_impressions',
            'SUM(`clicks`) AS total_clicks',
            'SUM(`conversions`) AS total_conversions',
            'SUM(`interactions`) AS total_interactions',
        ]);

        $geoMetrics->setStartDate($start_date);
        $geoMetrics->setEndDate($end_date);
        // 国家分组
        $geoMetrics->setGroupByFields('country_iso_code');
        $geoMetrics->getFormatter()->geoCountryListMetricsSetting();
        $geoMetrics->getFormatter()->setSort($sort_field, $sort_type);

        $result = $geoMetrics->find();

        $adsService = new \common\library\google_ads\ads_analytics\AdsAnalyticsService($client_id);

        $adsService->downLoadGeoCountryList($result, $ads_account_id);

    }
}
