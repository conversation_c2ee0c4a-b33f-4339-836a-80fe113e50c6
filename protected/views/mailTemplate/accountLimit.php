<div style="background:#fff;margin:auto;border:1px solid #ccc;width:646px;color:#666;font:14px PingHei, Helvetica, Tahoma, Aria<PERSON>, &quot;Microsoft YaHei&quot;, &quot;\5fae\8f6f\96c5\9ed1&quot;, SimSun, &quot;\5b8b\4f53&quot;, STXihei, &quot;\534e\6587\7ec6\9ed1&quot;, Heiti, &quot;\9ed1\4f53&quot;, sans-serif;">
  <div style="padding-left: 24px; background:#3c80f6; height: 35px;"><img src="https://v4client.oss-cn-hangzhou.aliyuncs.com/0_20160822144745_880fb70a02b5b99abcf381ddbf146313.png" style="height: 35px;" /></div>

  <div style="padding:24px 15px 36px;">
    <div>Dear <?=$nickname;?></div>

    <div style="padding:16px 0;line-height: 32px;">贵司 <?php if($day==0){echo '今';}else{ echo $day;}?> 天后（<?=$validDate;?>）将超量使用<?=$count;?>个账号，请您及时处理。
      <span style="color:red">逾期不处理，将导致所有账号被限制登录小满客户管理。</span>
      <div>您可以联系我们的销售专员或客服，沟通续约或追加账号有关事宜。</div>
      <div>或者，您还可以登录快发企业后台（boss端），按照如下操作指引，选择禁用至少<?=$count;?>个账号。</div>
    </div>
    <img src="https://v4client.oss-cn-hangzhou.aliyuncs.com/0_20160725135612_588196e6c64a50a34f8adc54abf28d6d.png" style="width:615px;">

    <div style="font-size:12px;background:#eee;padding:8px 20px; margin-top: 20px; line-height:20px;">
      <div style="font-weight:bold;">小满科技简介：</div>
      <div>深圳市小满科技有限公司（简称小满科技）是一家创新型的互联网公司，旨在通过云计算面向企业提供SaaS服务和生产力工具。我们面向中小型企业，帮助客户提高他们企业的管理水平， 提升销售力，从而带来业绩的持续增长，让企业更上台阶。 小满科技的团队中既有从事外贸B2B行业多年的行家，也有国内顶尖的互联网研发人才和安全专家。我们发现将先进的互联网理念、技术与现代企业管理相结合，可以创造出一些新的企业运作模式。令我们感到激动的是 每一步的创新或改善，都能帮助客户解决一点实际问题，或是带来业绩的增长。</div>
    </div>
  </div>
</div>