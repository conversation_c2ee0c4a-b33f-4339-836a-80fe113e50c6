<?php return array (
    'aliyun_common_key' =>
        array (
            'type' => 'aliyun',
            'host' => '',
            'db' => 'cn-hangzhou',
            'username' => 'LTAI5tABwJoaZbSyGLBru6CC',
            'password' => '******************************',
        ),
    'aliyun_dns_key' =>
        array (
            'type' => 'aliyun',
            'host' => '',
            'db' => 'cn-hangzhou',
            'username' => 'LTAI5tABwJoaZbSyGLBru6CC',
            'password' => '******************************',
        ),
    'aliyun_sts_key' =>
        array (
            'type' => 'aliyun',
            'host' => '',
            'username' => 'LTAI5tABwJoaZbSyGLBru6CC',
            'password' => '******************************',
        ),
    'es_common' =>
        array (
            'type' => 'es',
            'host' =>
                array (
                    0 => 'elastic:<EMAIL>:9200',
                ),
        ),
    'es_common_v7' =>
        array (
            'type' => 'es',
            'host' =>
                array (
                    0 => 'es.dev.hz.xm-idc.cn:9200',
                ),
            'username' => 'elastic',
            'password' => 'Es2xiaoman-inner',
        ),
    'es_mail_00' =>
        array (
            'type' => 'es',
            'host' =>
                array (
                    0 => 'es.dev.hz.xm-idc.cn:9200',
                ),
            'username' => 'elastic',
            'password' => 'Es2xiaoman-inner',
        ),
    'es_mail_01' =>
        array (
            'type' => 'es',
            'host' =>
                array (
                    0 => 'es.dev.hz.xm-idc.cn:9200',
                ),
            'username' => 'elastic',
            'password' => 'Es2xiaoman-inner',
        ),
    'whois-api' =>
        array (
            'type' => 'grpc',
            'host' => 'whois-api-grpc.vpcslb.com',
            'port' => '8080',
        ),
    'ai-automated-keyword' =>
        array (
            'type' => 'grpc',
            'host' => 'ai-automated-keyword-grpc.test.vpcslb.com',
            'port' => '8080',
        ),
    'ai-recommend-v2' =>
        array (
            'type' => 'grpc',
            'host' => 'ai-recommend-v2.test.vpcslb.com',
            'port' => '8080',
        ),
    'infra-ai-domain-seminfoextract' =>
        array (
            'type' => 'grpc',
            'host' => '*********',
            'port' => '8080',
        ),
    'infra-unifiedspider-email' =>
        array (
            'type' => 'grpc',
            'host' => 'spider-email-search.vpcslb.com',
            'port' => '8080',
        ),
    'official-site-api-grpc' =>
        array (
            'type' => 'grpc',
            'host' => 'official-site-api-grpc.test.vpcslb.com',
            'port' => '8080',
        ),
    'cms_im' =>
        array (
            'type' => 'http',
            'host' => 'market-livechat-dev.dev.vpcslb.com',
            'port' => '80',
        ),
    'cms_site_domain' =>
        array (
            'type' => 'http',
            'host' => 'okkishop.xyz',
        ),
    'edm_api' =>
        array (
            'type' => 'http',
            'host' => 'https://edm-api.dev.xiaoman.cn',
            'extra_info' =>
                array (
                    'host_grey' => 'https://edm-api.dev.xiaoman.cn',
                ),
        ),
    'go_ipip' =>
        array (
            'type' => 'http',
            'host' => 'ipip.dev.phpslb.com',
        ),
    'hashids' =>
        array (
            'type' => 'http',
            'host' => '',
            'extra_info' =>
                array (
                    'minLength' => 6,
                    'salt' => 'PtZuMD7sqm',
                    'alphabet' =>
                        array (
                            'default' => 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890',
                            'domain' => 'abcdefghijklmnopqrstuvwxyz1234567890',
                        ),
                ),
        ),
    'java_ciq' =>
        array (
            'type' => 'http',
            'host' => 'http://leads-go-release-dev.dev.phpslb.com/api',
        ),
    'java_sms' =>
        array (
            'type' => 'http',
            'host' => 'core-sms-dev.dev.vpcslb.com',
            'port' => '80',
        ),
    'login_api' =>
        array (
            'type' => 'http',
            'host' => 'https://login-api.dev.xiaoman.cn',
        ),
    'rocketmq' =>
        array (
            'type' => 'http',
            'host' => '',
            'extra_info' =>
                array (
                    'command_job' =>
                        array (
                            'endpoint' => 'http://****************.mqrest.cn-hangzhou-internal.aliyuncs.com',
                            'access_id' => 'LTAI5tABwJoaZbSyGLBru6CC',
                            'access_key' => '******************************',
                            'instance_id' => 'MQ_INST_****************_BYhxJxh3',
                            'default_instance' => false,
                            'region_id' => 'cn-hangzhou',
                            'topic' => 'crm-command-job-test',
                            'default_tag' => \common\library\queue_v2\QueueService::tagName('high'),
                            'wait_seconds' => 0,
                        ),
                    'default_job' =>
                        array (
                            'endpoint' => 'http://****************.mqrest.cn-hangzhou-internal.aliyuncs.com',
                            'access_id' => 'LTAI5tABwJoaZbSyGLBru6CC',
                            'access_key' => '******************************',
                            'instance_id' => 'MQ_INST_****************_BYhxJxh3',
                            'default_instance' => false,
                            'region_id' => 'cn-hangzhou',
                            'topic' => 'crm-default-job-test',
                            'default_tag' => \common\library\queue_v2\QueueService::tagName('high'),
                            'wait_seconds' => 0,
                        ),
                    'crontab_job' =>
                        array (
                            'endpoint' => 'http://****************.mqrest.cn-hangzhou-internal.aliyuncs.com',
                            'access_id' => 'LTAI5tABwJoaZbSyGLBru6CC',
                            'access_key' => '******************************',
                            'instance_id' => 'MQ_INST_****************_BYhxJxh3',
                            'default_instance' => false,
                            'region_id' => 'cn-hangzhou',
                            'topic' => 'crm-crontab-job-test',
                            'default_tag' => \common\library\queue_v2\QueueService::tagName('high'),
                            'wait_seconds' => 0,
                        ),
                    'push_job' =>
                        array (
                            'endpoint' => 'http://****************.mqrest.cn-hangzhou-internal.aliyuncs.com',
                            'access_id' => 'LTAI5tABwJoaZbSyGLBru6CC',
                            'access_key' => '******************************',
                            'instance_id' => 'MQ_INST_****************_BYhxJxh3',
                            'default_instance' => false,
                            'region_id' => 'cn-hangzhou',
                            'topic' => 'crm-push-job-test',
                            'wait_seconds' => 0,
                        ),
                    'slow_job' =>
                        array (
                            'endpoint' => 'http://****************.mqrest.cn-hangzhou-internal.aliyuncs.com',
                            'access_id' => 'LTAI5tABwJoaZbSyGLBru6CC',
                            'access_key' => '******************************',
                            'instance_id' => 'MQ_INST_****************_BYhxJxh3',
                            'default_instance' => false,
                            'region_id' => 'cn-hangzhou',
                            'topic' => 'crm-slow-job-test',
                            'default_tag' => \common\library\queue_v2\QueueService::tagName('high'),
                            'wait_seconds' => 0,
                        ),
                    'tips_workbench_job' =>
                        array (
                            'endpoint' => 'http://****************.mqrest.cn-hangzhou-internal.aliyuncs.com',
                            'access_id' => 'LTAI5tABwJoaZbSyGLBru6CC',
                            'access_key' => '******************************',
                            'instance_id' => 'MQ_INST_****************_BYhxJxh3',
                            'default_instance' => false,
                            'region_id' => 'cn-hangzhou',
                        ),
                    'facebook_feed_job' =>
                        array (
                            'endpoint' => 'http://****************.mqrest.cn-hangzhou-internal.aliyuncs.com',
                            'access_id' => 'LTAI5tABwJoaZbSyGLBru6CC',
                            'access_key' => '******************************',
                            'instance_id' => 'MQ_INST_****************_BYhxJxh3',
                            'default_instance' => false,
                            'region_id' => 'cn-hangzhou',
                            'topic' => 'marketing-inbox-facebook-feed-test',
                        ),
                    'social_media_webhook_job' =>
                        array (
                            'endpoint' => 'http://****************.mqrest.cn-hangzhou-internal.aliyuncs.com',
                            'access_id' => 'LTAI5tABwJoaZbSyGLBru6CC',
                            'access_key' => '******************************',
                            'instance_id' => 'MQ_INST_****************_BYhxJxh3',
                            'default_instance' => false,
                            'region_id' => 'cn-hangzhou',
                            'topic' => 'chat_webhook_account_dev',
                        ),
                    'dataworks_job' =>
                        array (
                            'endpoint' => 'http://****************.mqrest.cn-hangzhou-internal.aliyuncs.com',
                            'access_id' => 'LTAI5tABwJoaZbSyGLBru6CC',
                            'access_key' => '******************************',
                            'instance_id' => 'MQ_INST_****************_BYhxJxh3',
                            'default_instance' => false,
                            'region_id' => 'cn-hangzhou',
                            'topic' => 'crm-dataworks-job-test',
                        ),
                    'marketing_automation_job' =>
                        array (
                            'endpoint' => 'http://****************.mqrest.cn-hangzhou-internal.aliyuncs.com',
                            'access_id' => 'LTAI5tABwJoaZbSyGLBru6CC',
                            'access_key' => '******************************',
                            'instance_id' => 'MQ_INST_****************_BYhxJxh3',
                            'default_instance' => false,
                            'region_id' => 'cn-hangzhou',
                            'topic' => 'marketing_automation_job',
                            'default_tag' => \common\library\queue_v2\QueueService::tagName('high'),
                            'wait_seconds' => 1,
                        ),
                    'customer_job' =>
                        array (
                            'endpoint' => 'http://****************.mqrest.cn-hangzhou-internal.aliyuncs.com',
                            'access_id' => 'LTAI5tABwJoaZbSyGLBru6CC',
                            'access_key' => '******************************',
                            'instance_id' => 'MQ_INST_****************_BYhxJxh3',
                            'default_instance' => false,
                            'region_id' => 'cn-hangzhou',
                            'topic' => 'crm-customer-job-test',
                            'default_tag' => \common\library\queue_v2\QueueService::tagName('high'),
                            'wait_seconds' => 1,
                        ),
                ),
        ),
    'aliyun_oss' =>
        array (
            'type' => 'http',
            'host' => 'oss-cn-hangzhou.aliyuncs.com',
            'db' => 'v4client',
            'extra_info' =>
                array (
                    'internal_domain' => 'oss-cn-hangzhou-internal.aliyuncs.com',
                    'cdn_domain' => 'oss.xiaoman.cn',
                    'risk_domain' => 'v4client-oss-new.xiaoman.cn',
                    'auto_expire_db' => 'crm-export-file',
                ),
        ),
    'crm_domain' =>
        array (
            'type' => 'http',
            'host' => 'xiaoman.cn',
            'extra_info' =>
                array (
                    'use_zone_domain' => array(
                      'okki.com' => array(
                          'use_zones'    =>
                              array (
                                  0 => 'TW',
                                  1 => 'HK',
                              ),
                          'use_zone' => 'TW',
                          'crm_domain' => 'crm.dev.okki.com'
                      ),
                      'xiaoman.cn' => array(
                          'use_zone'   => 'CN',
                          'crm_domain' => 'master.dev.xiaoman.cn'
                      ),
                    ),

                    'default_top_domain' => 'xiaoman.cn',
                    'crm_domain' => 'test.k.xiaoman.cn',
                    'crm_url' => 'https://master.feature.dev.xiaoman.cn',
                    'callback_url' => 'https://kcallback.dev.xiaoman.cn',
                    'internal_url' => 'https://inner-api.dev.xiaoman.cn',
                    'login_url' => 'https://login.dev.xiaoman.cn',
                    'app_url' => 'https://app.dev.xiaoman.cn',
                    'personal_url' => 'https://k.dev.xiaoman.cn/account/information',
                    'enterprise_url' => 'https://test.enterprise.xiaoman.cn',
                    'oss_url' => 'https://oss-api.dev.xiaoman.cn',
                    'lighthouse_url' => 'https://lighthouse-api.dev.xiaoman.cn',
                    'erp_service_url' => 'https://erpapi.xiaoman.cn',
                    'site_domain' => 'test.xiaoman.site',
                    'doc_domain' => 'doc.xiaoman.link',
                    'internal_doc_url' => 'http://internal-0.doc.xiaoman.link',
                    'link_short_domain' => 't.m.tips',
                    'sns_oauth_domain' => 'sns.test.xiaoman.live',
                    'html_printer_url' => 'https://printer.k.xiaoman.cn',
                    'mkt_domain' => 'mtk.dev.xiaoman.cn',
                    'mkt_url' => 'https://mkt.dev.xiaoman.cn',
                    'ames_domain' => 'ames.dev.xiaoman.cn',
                    'ames_url' => 'https://ames.dev.xiaoman.cn',
                    'ames_work_domain' => 'work.okki.com',
                    'ames_work_url' => 'https://work.okki.com',
                    'assistant_domain' => 'customer0331.feature.dev.xiaoman.cn',
                    'assistant_url' => 'https://crm-iteration-7-2-1123661.story.dev.xiaoman.cn',
                    'io_domain' => 'okki-io.dev.xiaoman.cn',
                    'io_url' => 'https://okki-free.dev.xiaoman.cn',
                    'cms_api' => 'https://cms.dev.xiaoman.cn',
                    'okki_leads_service' => 'https://leads-iteration-7-4-1125587-inner.story.dev.xiaoman.cn',
                    'home_url' => 'https://home.dev.xiaoman.cn',
                    'old_erp_service_url' => 'https://erp-api.dev.xiaoman.cn',
                    'old_erp_enterprise_url' => 'https://e-api.dev.xiaoman.cn',
                    'okki_personal_domain' => 'leads-iteration-4-4-1094757.story.dev.xiaoman.cn',
                    'okki_free_domain' => 'okki-free.dev.xiaoman.cn',
                    'okki_free_global_domain' => 'app.dev.okki.com',
                ),
        ),
    'dtc_config' =>
        array (
            'type' => 'http',
            'host' => '',
            'extra_info' =>
                array (
                    'app_key' => '500186',
                    'app_secret' => 'ZJ1ma1C7OmZYX2eYoFeCXeeDXXugfTyz',
                ),
        ),
    'jentian_api' =>
        array (
            'type' => 'http',
            'host' => 'https://discovery-k8s-v2-test.jentian.com',
            'extra_info' =>
                array (
                    'token' => '4b4ea64611f2171a712a6d62a67616c2',
                ),
        ),
    'sns_proxy' =>
        array (
            'type' => 'http',
            'host' => '*************',
            'port' => '3128',
        ),
    'db_proxy_mysql' =>
        array (
            'type' => 'mysql',
            'host' => 'ss-proxy-php.dev.vpcslb.com',
            'db' => 'v5_client_dev',
            'username' => 'root',
            'password' => '123456',
            'extra_info' =>
                array (
                    'enable' => true,
                    'part_enable' => false,
                    'part_service' =>
                        array (
                            0 => 'test',
                            1 => 'dev',
                        ),
                    'part_rules' =>
                        array (
                            'dev' =>
                                array (
                                    'client_id' =>
                                        array (
                                            'prod' =>
                                                array (
                                                    0 => 1,
                                                    1 => 123,
                                                ),
                                            'grey' =>
                                                array (
                                                    0 => 2,
                                                    1 => 14367,
                                                ),
                                            'beta' =>
                                                array (
                                                    0 => 3,
                                                ),
                                        ),
                                    'client_tail' =>
                                        array (
                                            'prod' =>
                                                array (
                                                    0 => 1,
                                                    1 => 5,
                                                    2 => 9,
                                                ),
                                            'grey' =>
                                                array (
                                                    0 => 2,
                                                ),
                                        ),
                                ),
                        ),
                    'dbPorts' =>
                        array (
                            'prod' => 13306,
                            'grey' => 13306,
                            'beta' => 13306,
                        ),
                    'defaultDestination' => 'direct',
                    'routeRules' =>
                        array (
                            'client_id' =>
                                array (
                                    'prod' =>
                                        array (
                                            0 => 1,
                                            1 => 123,
                                        ),
                                    'grey' =>
                                        array (
                                            0 => 2,
                                            1 => 14367,
                                        ),
                                    'beta' =>
                                        array (
                                            0 => 3,
                                        ),
                                ),
                            'client_tail' =>
                                array (
                                    'prod' =>
                                        array (
                                            0 => 1,
                                            1 => 5,
                                            2 => 9,
                                        ),
                                    'grey' =>
                                        array (
                                            0 => 2,
                                        ),
                                ),
                        ),
                ),
        ),
    'db_proxy_pgsql' =>
        array (
            'type' => 'pgsql',
            'host' => 'shardingsphere-xiaoman-proxy-pg.dev.vpcslb.com',
            'db' => 'v5_client_dev',
            'username' => 'root',
            'password' => '123456',
            'extra_info' =>
                array (
                    'enable' => true,
                    'part_enable' => false,
                    'part_service' =>
                        array (
                            0 => 'test',
                            1 => 'dev',
                        ),
                    'part_rules' =>
                        array (
                            'dev' =>
                                array (
                                    'client_id' =>
                                        array (
                                            'prod' =>
                                                array (
                                                    0 => 1,
                                                    1 => 123,
                                                ),
                                            'grey' =>
                                                array (
                                                    0 => 2,
                                                    1 => 14367,
                                                ),
                                            'beta' =>
                                                array (
                                                    0 => 3,
                                                ),
                                        ),
                                    'client_tail' =>
                                        array (
                                            'prod' =>
                                                array (
                                                    0 => 1,
                                                    1 => 5,
                                                    2 => 9,
                                                ),
                                            'grey' =>
                                                array (
                                                    0 => 2,
                                                ),
                                        ),
                                ),
                        ),
                    'dbPorts' =>
                        array (
                            'prod' => 5432,
                            'grey' => 5432,
                            'beta' => 5432,
                        ),
                    'defaultDestination' => 'direct',
                    'routeRules' =>
                        array (
                            'client_id' =>
                                array (
                                    'prod' =>
                                        array (
                                            0 => 1,
                                            1 => 123,
                                        ),
                                    'grey' =>
                                        array (
                                            0 => 2,
                                            1 => 14367,
                                        ),
                                    'beta' =>
                                        array (
                                            0 => 3,
                                        ),
                                ),
                            'client_tail' =>
                                array (
                                    'prod' =>
                                        array (
                                            0 => 1,
                                            1 => 5,
                                            2 => 9,
                                        ),
                                    'grey' =>
                                        array (
                                            0 => 2,
                                        ),
                                ),
                            'set_id' =>
                                array (
                                    'prod' =>
                                        array (
                                            0 => 10,
                                        ),
                                ),
                        ),
                ),
        ),
    'google_ads' =>
        array (
            'type' => 'http',
            'host' => '',
            'username' => '1041364293631-0p1l8sjiu2u672a6sqd6a0pp6vetm44l.apps.googleusercontent.com',
            'password' => '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',
            'extra_info' =>
                array (
                    'developer_key' => 'AIzaSyCfhY-N1YOsRKUHbxBnNwqp10Z-8oQcjlA',
                ),
        ),
    'waba_config' =>
        array (
            'type' => 'http',
            'host' => '',
            'username' => '<EMAIL>',
            'password' => 'Xlz654321',
            'extra_info' =>
                array (
                    'app_id' => '1175616571029701',
                    'solution_id' => '1343901456902930',
                    'config_id' => '1230688101934275',
                    'accessKeyId' => 'LTAI5tQE9MnHk9azdYd9miAv',
                    'accessKeySecret' => '******************************',
                ),
        ),
    'wall_proxy' =>
        array (
            'type' => 'http',
            'host' => '*************',
            'port' => '3128',
        ),
    'google_proxy' =>
        array (
            'type' => 'http',
            'host' => '*************',
            'port' => '3128',
        ),
    'java_ai_word' =>
        array (
            'type' => 'http',
            'host' => 'http://ai-word-cloud.dev.vpcslb.com',
        ),
    'java_app_push' =>
        array (
            'type' => 'http',
            'host' => 'http://apollo-app-server-dev.dev.vpcslb.com',
        ),
    'java_edm_api' =>
        array (
            'type' => 'http',
            'host' => 'http://mail-edm-statistics-dev.dev.vpcslb.com',
        ),
    'alibaba_config' =>
        array (
            'type' => 'http',
            'host' => '',
            'extra_info' =>
                array (
                    'app_key' => '31731043',
                    'app_secret' => 'a86e999d9f9d736fa121550cd4d17668',
                    'service_code' => 'ISVXX2BDEBC',
                    'open_app_key' => '579915',
                    'open_secret_key' => 'oLiP1ZQakU',
                    'gateway_url' => 'http://open-api.alibaba.com/router/rest',
                    'gateway_pre_url' => 'https://open-api.alibaba.com/router/rest',
                    'gateway_ssl_url' => 'https://open-api.alibaba.com/router/rest',
                    'gateway_grey_url' => 'https://pre-gw.api.taobao.com/top/router/rest',
                    'bridge_gateway_url' => 'https://open-api.alibaba.com/router/rest',
                    'bridge_grey_gateway_url' => 'https://open-api.alibaba.com/router/rest',
                    'platform' => 'gop',
                ),
        ),
    'aliyun_log_key' =>
        array (
            'type' => 'http',
            'host' => '',
            'db' => 'cn-hangzhou',
            'username' => 'LTAI5tABwJoaZbSyGLBru6CC',
            'password' => '******************************',
            'extra_info' =>
                array (
                    'role_arn' => 'acs:ram::****************:role/aliyunlogrole',
                    'projects' =>
                        array (
                            0  =>
                                array (
                                    'project_alias' => 'IDC日志',
                                    'project_name' => 'k8s-log-custom-idc-dev',
                                ),
                            2 =>
                                array (
                                    'project_alias' => '收发件服务日志',
                                    'project_name' => 'mail-log',
                                ),
                        ),
                    'trace_config' =>
                        array (
                            'project' => 'k8s-log-custom-idc-dev',
                            'instance' => 'k8s-idc-dev-trace',
                        ),
                ),
        ),
    'dashboard_feed' =>
        array (
            'type' => 'http',
            'host' => 'http://dashboard-feed.dev.vpcslb.com',
        ),
    'java_passport' =>
        array (
            'type' => 'http',
            'host' => 'http://core-passport-dev.dev.vpcslb.com',
        ),
    'java_web_push' =>
        array (
            'type' => 'http',
            'host' => 'http://core-web-push-collector-dev.dev.vpcslb.com',
        ),
    'java_ai_common' =>
        array (
            'type' => 'http',
            'host' => 'http://ai-recommend.test.vpcslb.com',
        ),
    'java_discovery' =>
        array (
            'type' => 'http',
            'host' => 'http://discovery.test.vpcslb.com',
        ),
    'java_mail_send' =>
        array (
            'type' => 'http',
            'host' => 'http://send-mail-dev.dev.vpcslb.com',
        ),
    'java-dx-revise' =>
        array (
            'type' => 'http',
            'host' => 'http://ai-merge-company.vpcslb.com',
        ),
    'erp_service_url' =>
        array (
            'type' => 'http',
            'host' => 'https://erp-api.xiaoman.cn',
        ),
    'shangzhibo_api' =>
        array (
            'type' => 'http',
            'host' => 'https://shangzhibo.tv',
        ),
    'shop_publish_server' =>
        array (
            'type' => 'http',
            'host' => 'http://publish-server.dev.phpslb.com',
        ),
    'tms_pdf_server' =>
        array (
            'type' => 'http',
            'host' => 'http://tms-pdf.dev.phpslb.com',
        ),
    'facebook_config' =>
        array (
            'type' => 'http',
            'host' => '',
            'extra_info' =>
                array (
                    'app_id' => 411343726533758,
                    'app_secret' => '3cdd06a2db5fe4efa5446ac0bb8cfe19',
                ),
        ),
    'html_screenshot' =>
        array (
            'type' => 'http',
            'host' => 'http://html-screenshot.dev.phpslb.com',
        ),
    'java_ai_company' =>
        array (
            'type' => 'http',
            'host' => 'http://**********:9910',
        ),
    'java_link_track' =>
        array (
            'type' => 'http',
            'host' => 'http://market-stats.test.vpcslb.com',
        ),
    'java_ai_language' =>
        array (
            'type' => 'http',
            'host' => 'http://ai-language-classify.dev.vpcslb.com',
        ),
    'java_doc_preview' =>
        array (
            'type' => 'http',
            'host' => 'https://doc-preview.dev.xiaoman.cn',
        ),
    'java_think_tank' =>
        array (
            'type' => 'http',
            'host' => 'http://ai-discovery-v2.dev.vpcslb.com',
        ),
    'rocketmq_config' =>
        array (
            'type' => 'http',
            'host' => '',
            'extra_info' =>
                array (
                    'endpoint' => 'http://****************.mqrest.cn-hangzhou-internal.aliyuncs.com',
                    'access_id' => 'LTAI5tABwJoaZbSyGLBru6CC',
                    'access_key' => '******************************',
                    'instance_id' => 'MQ_INST_****************_BYhxJxh3',
                    'default_instance' => false,
                    'region_id' => 'cn-hangzhou',
                ),
        ),
    'java_ai_recommend' =>
        array (
            'type' => 'http',
            'host' => 'http://ai-recommend.test.vpcslb.com',
        ),
    'java_mail_common' =>
        array (
            'type' => 'http',
            'host' => 'http://receive-mail-dev.dev.vpcslb.com',
        ),
    'java_mail_search' =>
        array (
            'type' => 'http',
            'host' => 'http://mail-search-mail-dev.dev.vpcslb.com',
        ),
    'java_outlook_bind' =>
        array (
            'type' => 'http',
            'host' => 'http://receive-mail-dev.dev.vpcslb.com',
        ),
    'java_desktop_push' =>
        array (
            'type' => 'http',
            'host' => 'http://apollo-traffic-police-dev.dev.vpcslb.com',
        ),
    'java_discovery_v2' =>
        array (
            'type' => 'http',
            'host' => 'http://ai-discovery-v2.dev.vpcslb.com',
        ),
    'java_mail_content' =>
        array (
            'type' => 'http',
            'host' => 'http://mail-content-dev.dev.vpcslb.com',
        ),
    'java_mail_receive' =>
        array (
            'type' => 'http',
            'host' => 'kjava-router-dev.dev.vpcslb.com',
        ),
    'aliyun_log_project' =>
        array (
            'type' => 'http',
            'host' => '',
        ),
    'okki_chat_service' =>
        array (
            'host' => 'https://chat-api-story-leads-iteration-7-2-1117385.dev.xiaoman.cn',
        ),
    'doc_parser' =>
        array (
            'host' => 'http://doc-parser.dev.phpslb.com',
        ),
    'ai_background_check' =>
        array (
            'type' => 'http',
            'host' => 'http://background-check.dev.phpslb.com',
            'extra_info' =>
                array (
                    'qph' => 400,
                    'timeout' => 1200,
                ),
        ),
    'site_build_server' =>
        array (
            'type' => 'http',
            'host' => '**********',
            'port' => '10008',
            'extra_info' =>
                array (
                    'web_render_host' => '**********',
                    'web_render_port' => '9600',
                ),
        ),
    'websocket_gateway' =>
        array (
            'type' => 'http',
            'host' => '**********',
            'port' => '4698',
            'extra_info' =>
                array (
                    'uri' => 'ws://test.andyws.xiaoman.cn:80',
                ),
        ),
    'java_file_classify' =>
        array (
            'type' => 'http',
            'host' => 'http://document-orderform-recognition.dev.vpcslb.com',
        ),
    'java_mail_classify' =>
        array (
            'type' => 'http',
            'host' => 'http://mail-classification-dev.dev.vpcslb.com',
        ),
    'java_nacos_service' =>
        array (
            'type' => 'http',
            'host' => 'sidecar-dashboard-dev.dev.vpcslb.com',
        ),
    'java_passport_skey' =>
        array (
            'type' => 'http',
            'host' => 'http://core-passport-skey-dev.dev.vpcslb.com',
        ),
    'dashboard_feed_read' =>
        array (
            'type' => 'http',
            'host' => 'http://dashboard-feed-reader.dev.vpcslb.com',
        ),
    'java_ciq_deprecated' =>
        array (
            'type' => 'http',
            'host' => 'http://customs-v1-latest.vpcslb.com',
        ),
    'java_invoice_export' =>
        array (
            'type' => 'http',
            'host' => 'http://document-orderform-exporter.dev.vpcslb.com',
        ),
    'lua_setting_server' =>
        array (
            'type' => 'http',
            'host' => 'test.inner.xiaoman.cn',
            'extra_info' =>
                array (
                    'ip' => '**********:7911',
                ),
        ),
    'dashboard_feed_write' =>
        array (
            'type' => 'http',
            'host' => 'http://dashboard-feed-writer.dev.vpcslb.com',
        ),
    'java_ai_tag_recommend' =>
        array (
            'type' => 'http',
            'host' => 'http://ai-tag-recommend.dev.vpcslb.com',
        ),
    'java_discovery_spider' =>
        array (
            'type' => 'http',
            'host' => 'http://official-site-image-spider.test.vpcslb.com',
        ),
    'java_file_classify_v2' =>
        array (
            'type' => 'http',
            'host' => 'http://document-orderform-recognition.dev.vpcslb.com',
        ),
    'spamassassin_server' =>
        array (
            'type' => 'http',
            'host' => '**********',
            'port' => '737',
        ),
    'java_batch_mail_content' =>
        array (
            'type' => 'http',
            'host' => 'http://mail-content-dev.dev.vpcslb.com',
        ),
    'java_mail_classify_v2' =>
        array (
            'type' => 'http',
            'host' => 'http://mail-classification-dev.dev.vpcslb.com',
        ),
    'java_mail_extract_data' =>
        array (
            'type' => 'http',
            'host' => 'http://mail-detail-information.dev.vpcslb.com',
        ),
    'java_product_classify' =>
        array (
            'type' => 'http',
            'host' => 'http://document-orderform-recognition.dev.vpcslb.com',
        ),
    'java_discovery_recommend' =>
        array (
            'type' => 'http',
            'host' => 'http://discovery-recommend.test.vpcslb.com',
        ),
    'java_discovery_v2_latest' =>
        array (
            'type' => 'http',
            'host' => 'http://ai-discovery-v2.dev.vpcslb.com',
        ),
    'java_invoice_export_optimization' =>
        array (
            'type' => 'http',
            'host' => 'document-orderform-exporter.dev.vpcslb.com',
        ),
    'subscribe_statistic_report' =>
        array (
            'type' => 'http',
            'host' => 'https://crm-email-maker.beta.xiaoman.cn',
            'port' => '7000',
        ),
    'aliyun_mns' =>
        array (
            'type' => 'mns',
            'host' => 'https://****************.mns.cn-hangzhou.aliyuncs.com',
            'extra_info' =>
                array (
                    'ai_event_report' => 'PhpCrmEventQueueForTest',
                    'tips_feeds' => 'TestTipsFeeds',
                    'link_visit_report' => 'MarketStatsTest',
                    'tips_company_topic' => 'TestTipsActiveCompany',
                ),
        ),
    'java_envoy_control' =>
        array (
            'type' => 'http',
            'host' => 'http://istio-envoy-dev.dev.vpcslb.com',
        ),
    'java_envoy_rate_limit' =>
        array (
            'type' => 'http',
            'host' => 'http://envoy-api.dev.xiaoman.cn',
        ),
    'mail_content' =>
        array (
            'type' => 'mongo',
            'host' => '**********',
            'port' => '37017',
            'db' => 'v4_mail',
            'username' => 'jinyunMongodb',
            'password' => 'Yy8bXkIUs9penaOoxnwA',
        ),
    'worldsou_config' =>
        array (
            'type' => 'http',
            'host' => 'http://okkicenter.dev.webdemodesign.site',
            'extra_info' =>
                array (
                    'keySecret' => '8M=3Kt043ls>L2+v0601',
                ),
        ),
    'exp_db' =>
        array (
            'type' => 'mysql',
            'host' => 'mysql.dev.hz.xm-idc.cn',
            'port' => '3306',
            'username' => 'crmphp',
            'password' => 'THra2v5gV3mbbj6j5R8VFieh',
            'extra_info' =>
                array (
                    'charset' => 'utf8mb4',
                ),
        ),
    'oss_db' =>
        array (
            'type' => 'mysql',
            'host' => 'mysql.dev.hz.xm-idc.cn',
            'port' => '3306',
            'db' => 'xiaoman',
            'username' => 'crmphp',
            'password' => 'THra2v5gV3mbbj6j5R8VFieh',
            'extra_info' =>
                array (
                    'charset' => 'utf8mb4',
                ),
            'connection_string' => 'mysql:host=mysql.dev.hz.xm-idc.cn:3306;dbname=xiaoman',
        ),
    'xiaoman_oss_db' =>
        array (
            'type' => 'mysql',
            'host' => 'mysql.dev.hz.xm-idc.cn',
            'port' => '3306',
            'db' => 'xiaoman_oss',
            'username' => 'crmphp',
            'password' => 'THra2v5gV3mbbj6j5R8VFieh',
            'extra_info' =>
                array (
                    'charset' => 'utf8mb4',
                ),
            'connection_string' => 'mysql:host=mysql.dev.hz.xm-idc.cn:3306;dbname=xiaoman_oss',
        ),
    'risk_db' =>
        array (
            'type' => 'mysql',
            'host' => 'mysql.dev.hz.xm-idc.cn',
            'port' => '3306',
            'db' => 'risk_management',
            'username' => 'crmphp',
            'password' => 'THra2v5gV3mbbj6j5R8VFieh',
            'extra_info' =>
                array (
                    'charset' => 'utf8mb4',
                ),
            'connection_string' => 'mysql:host=mysql.dev.hz.xm-idc.cn:3306;dbname=risk_management',
        ),
    'shop_admin' =>
        array (
            'type' => 'mysql',
            'host' => 'mysql.dev.hz.xm-idc.cn',
            'port' => '3306',
            'db' => 'shop_admin',
            'username' => 'crmphp',
            'password' => 'THra2v5gV3mbbj6j5R8VFieh',
            'extra_info' =>
                array (
                    'charset' => 'utf8mb4',
                ),
            'connection_string' => 'mysql:host=mysql.dev.hz.xm-idc.cn:3306;dbname=shop_admin',
        ),
    'v4_admin' =>
        array (
            'type' => 'mysql',
            'host' => 'mysql.dev.hz.xm-idc.cn',
            'port' => '3306',
            'db' => 'v4_admin',
            'username' => 'crmphp',
            'password' => 'THra2v5gV3mbbj6j5R8VFieh',
            'extra_info' =>
                array (
                    'charset' => 'utf8mb4',
                ),
            'connection_string' => 'mysql:host=mysql.dev.hz.xm-idc.cn:3306;dbname=v4_admin',
        ),
    'envoy_db' =>
        array (
            'type' => 'mysql',
            'host' => 'mysql.dev.hz.xm-idc.cn',
            'port' => '3306',
            'db' => 'xiaoman_infra_envoy',
            'username' => 'crmjava',
            'password' => 'THra2v5gV3mbbj6j5R8VFieh',
            'extra_info' =>
                array (
                    'charset' => 'utf8mb4',
                ),
            'connection_string' => 'mysql:host=mysql.dev.hz.xm-idc.cn:3306;dbname=xiaoman_infra_envoy',
        ),
    'okki_marketing' =>
        array (
            'type' => 'mysql',
            'host' => 'mysql.dev.hz.xm-idc.cn',
            'port' => '3306',
            'db' => 'okki_marketing',
            'username' => 'crmphp',
            'password' => 'THra2v5gV3mbbj6j5R8VFieh',
            'extra_info' =>
                array (
                    'charset' => 'utf8mb4',
                ),
            'connection_string' => 'mysql:host=mysql.dev.hz.xm-idc.cn:3306;dbname=okki_marketing',
        ),
    'prometheus_db' =>
        array (
            'type' => 'mysql',
            'host' => 'mysql.dev.hz.xm-idc.cn',
            'port' => '3306',
            'db' => 'prometheus',
            'username' => 'crmphp',
            'password' => 'THra2v5gV3mbbj6j5R8VFieh',
            'extra_info' =>
                array (
                    'charset' => 'utf8mb4',
                ),
            'connection_string' => 'mysql:host=mysql.dev.hz.xm-idc.cn:3306;dbname=prometheus',
        ),
    'upload_db' =>
        array (
            'type' => 'mysql',
            'host' => 'mysql.dev.hz.xm-idc.cn',
            'port' => '3306',
            'db' => 'xiaoman_unique_file',
            'username' => 'crmphp',
            'password' => 'THra2v5gV3mbbj6j5R8VFieh',
            'extra_info' =>
                array (
                    'charset' => 'utf8mb4',
                ),
            'connection_string' => 'mysql:host=mysql.dev.hz.xm-idc.cn:3306;dbname=xiaoman_unique_file',
        ),
    'v5_account_base' =>
        array (
            'type' => 'mysql',
            'host' => 'mysql.dev.hz.xm-idc.cn',
            'port' => '3306',
            'db' => 'v5_account_base',
            'username' => 'crmphp',
            'password' => 'THra2v5gV3mbbj6j5R8VFieh',
            'extra_info' =>
                array (
                    'charset' => 'utf8mb4',
                ),
            'connection_string' => 'mysql:host=mysql.dev.hz.xm-idc.cn:3306;dbname=v5_account_base',
        ),
    'alibaba_message_db' =>
        array (
            'type' => 'mysql',
            'host' => 'mysql.dev.hz.xm-idc.cn',
            'port' => '3306',
            'db' => 'alibaba_message',
            'username' => 'crmphp',
            'password' => 'THra2v5gV3mbbj6j5R8VFieh',
            'extra_info' =>
                array (
                    'charset' => 'utf8mb4',
                ),
            'connection_string' => 'mysql:host=mysql.dev.hz.xm-idc.cn:3306;dbname=alibaba_message',
        ),
    'company_match_db' =>
        array (
            'type' => 'mysql',
            'host' => 'mysql.dev.hz.xm-idc.cn',
            'port' => '3306',
            'db' => 'dashboard',
            'username' => 'crmphp',
            'password' => 'THra2v5gV3mbbj6j5R8VFieh',
            'extra_info' =>
                array (
                    'charset' => 'utf8mb4',
                ),
            'connection_string' => 'mysql:host=mysql.dev.hz.xm-idc.cn:3306;dbname=dashboard',
        ),
    'lantern_download_center_db' =>
        array (
            'type' => 'mysql',
            'host' => '**********',
            'port' => '3306',
            'db' => 'download_center',
            'username' => 'jinyuncrm',
            'password' => 'crm123654',
            'extra_info' =>
                array (
                    'charset' => 'utf8mb4',
                ),
            'connection_string' => 'mysql:host=**********:3306;dbname=download_center',
        ),
    'v5_account_base_read' =>
        array (
            'type' => 'mysql',
            'host' => 'mysql.dev.hz.xm-idc.cn',
            'port' => '3306',
            'db' => 'v5_account_base',
            'username' => 'crmphp',
            'password' => 'THra2v5gV3mbbj6j5R8VFieh',
            'extra_info' =>
                array (
                    'charset' => 'utf8mb4',
                ),
            'connection_string' => 'mysql:host=mysql.dev.hz.xm-idc.cn:3306;dbname=v5_account_base',
        ),
    'v5_client_exp_mysql' =>
        array (
            'type' => 'mysql',
            'host' => 'mysql.dev.hz.xm-idc.cn',
            'port' => '3306',
            'username' => 'crmroot',
            'password' => 'HtqY5Q3O6TTvlHIvVLZoHg1d',
        ),
    'redis_cache' =>
        array (
            'type' => 'redis',
            'host' => 'single.redis.dev.hz.xm-idc.cn',
            'port' => '6379',
            'db' => '0',
            'password' => 'pTKk8DNqH3QXAhPrk2BG9w9p',
        ),
    'redis_incr' =>
        array (
            'type' => 'redis',
            'host' => 'single.redis.dev.hz.xm-idc.cn',
            'port' => '6379',
            'db' => '0',
            'username' => 'crmphp',
            'password' => 'pTKk8DNqH3QXAhPrk2BG9w9p',
        ),
    'redis_login' =>
        array (
            'type' => 'redis',
            'host' => 'single.redis.dev.hz.xm-idc.cn',
            'port' => '6379',
            'db' => '0',
            'password' => 'pTKk8DNqH3QXAhPrk2BG9w9p',
        ),
    'redis_passport_session' =>
        array (
            'type' => 'redis',
            'host' => 'single.redis.dev.hz.xm-idc.cn',
            'port' => '6379',
            'db' => '0',
            'password' => 'pTKk8DNqH3QXAhPrk2BG9w9p',
        ),
    'v5_client_exp_pg' =>
        array (
            'type' => 'pgsql',
            'host' => 'pg.dev.hz.xm-idc.cn',
            'port' => '3433',
            'username' => 'crmroot',
            'password' => 'HtqY5Q3O6TTvlHIvVLZoHg1d',
        ),
    'exp_redis_cache' =>
        array (
            'type' => 'redis',
            'host' => 'single.redis.dev.hz.xm-idc.cn',
            'port' => '6379',
            'db' => '1',
            'password' => 'pTKk8DNqH3QXAhPrk2BG9w9p',
        ),
    'exp_redis_incr' =>
        array (
            'type' => 'redis',
            'host' => 'single.redis.dev.hz.xm-idc.cn',
            'port' => '6379',
            'db' => '1',
            'username' => 'crmphp',
            'password' => 'pTKk8DNqH3QXAhPrk2BG9w9p',
        ),
    'exp_redis_login' =>
        array (
            'type' => 'redis',
            'host' => 'single.redis.dev.hz.xm-idc.cn',
            'port' => '6379',
            'db' => '1',
            'password' => 'pTKk8DNqH3QXAhPrk2BG9w9p',
        ),
    'redis_cluster' =>
        array (
            'type' => 'redis',
            'host' =>
                array (
                    0 => 'tcp://cluster.redis.dev.hz.xm-idc.cn:6379',
                ),
            'password' => 'pTKk8DNqH3QXAhPrk2BG9w9p',
        ),
    'redis_cluster_privilege' =>
        array (
            'type' => 'redis',
            'host' => 'tcp://cluster.redis.dev.hz.xm-idc.cn:6379',
            'extra_info' =>
                array (
                    'host' =>
                        array (
                            0 => 'tcp://cluster.redis.dev.hz.xm-idc.cn:6379',
                        ),
                ),
            'password' => 'pTKk8DNqH3QXAhPrk2BG9w9p',
        ),
    'redis_queue' =>
        array (
            'type' => 'redis',
            'host' => 'single.redis.dev.hz.xm-idc.cn',
            'port' => '6379',
            'db' => '0',
            'password' => 'pTKk8DNqH3QXAhPrk2BG9w9p',
        ),
    'exp_redis_mail_summary' =>
        array (
            'type' => 'redis',
            'host' => 'single.redis.dev.hz.xm-idc.cn',
            'port' => '6379',
            'db' => '1',
            'password' => 'pTKk8DNqH3QXAhPrk2BG9w9p',
        ),
    'exp_redis_queue' =>
        array (
            'type' => 'redis',
            'host' => 'single.redis.dev.hz.xm-idc.cn',
            'port' => '6379',
            'db' => '1',
            'password' => 'pTKk8DNqH3QXAhPrk2BG9w9p',
        ),
    'redis_mail_summary' =>
        array (
            'type' => 'redis',
            'host' => 'single.redis.dev.hz.xm-idc.cn',
            'port' => '6379',
            'db' => '0',
            'password' => 'pTKk8DNqH3QXAhPrk2BG9w9p',
        ),
    'redis_mail_translate_cache' =>
        array (
            'type' => 'redis',
            'host' => 'single.redis.dev.hz.xm-idc.cn',
            'port' => '6379',
            'db' => '0',
            'password' => 'pTKk8DNqH3QXAhPrk2BG9w9p',
        ),
    'java_eagle_eye' =>
        array (
            'type' => 'udp',
            'host' => '************',
            'port' => '8080',
        ),
    'log_server' =>
        array (
            'type' => 'udp',
            'host' => '**********',
            'port' => '9502',
        ),
    'mail_unread_server' =>
        array (
            'type' => 'udp',
            'host' => '************',
            'port' => '9604',
        ),
    'redis_openapi_limit_config' =>
        array (
            'type' => 'redis',
            'host' => 'single.redis.dev.hz.xm-idc.cn',
            'port' => '6379',
            'db' => '0',
            'password' => 'pTKk8DNqH3QXAhPrk2BG9w9p',
        ),
    'java_db_proxy_service' =>
        array (
            'type' => 'http',
            'host' => 'shardingsphere-router-sync.dev.vpcslb.com',
        ),
    'java_unique_file_service' =>
        array (
            'type' => 'http',
            'host' => 'xiaoman-oss-interface.dev.vpcslb.com',
        ),
    'whatsapp_unique_file_service' =>
        array (
            'type' => 'http',
            'host' => 'unique-file-new.dev.vpcslb.com',
            'extra_info' =>
                array (
                    'allow_buckets' =>
                        array(
                            0 => 'whatsapp-chat-dev',
                        ),
                )
        ),
    'mail_unread_server_new' =>
        array (
            'type' => 'udp',
            'host' => '************',
            'port' => '9604',
            'extra_info' =>
                array (
                    'new_host' => '************',
                    'grey_flag' => 1,
                    'grey_config' =>
                        array (
                            'client_ids' =>
                                array (
                                    0 => 1,
                                ),
                            'client_num' =>
                                array (
                                ),
                            'all_flag' => 1,
                        ),
                ),
        ),
    'mysql_binlog_server' =>
        array (
            'type' => 'udp',
            'host' => '**********',
            'port' => '9605',
        ),
    'chatgpt_xiaoman' =>
        array (
            'type' => 'http',
            'host' => 'v2.ai-connector.dev.phpslb.com',
        ),
    'nacos_service' =>
        array (
            'type' => 'http',
            'host' => 'mse-9313a770-nacos-ans.mse.aliyuncs.com',
        ),
    'experience_pgsql' =>
        array (
            'host' => 'pg.dev.hz.xm-idc.cn',
            'port' => '5432',
            'db' => 'v5_client',
            'username' => 'crmroot',
            'password' => 'HtqY5Q3O6TTvlHIvVLZoHg1d',
        ),
    'experience_mysql' =>
        array (
            'host' => 'mysql.dev.hz.xm-idc.cn',
            'port' => '3306',
            'db' => 'v5_client',
            'username' => 'crmroot',
            'password' => 'HtqY5Q3O6TTvlHIvVLZoHg1d',
            'connection_string' => 'mysql:host=mysql.dev.hz.xm-idc.cn:3306;dbname=v5_client',
        ),
    'app_custom_config' =>
        array (
            'extra_info' =>
                array (
                    'encrypt_salt' => 'X1a0mAnZbbCg',
                    'tbs_licence' => 'xjnD7UmlgPbHG+a1RyUv+A9OztdAFWpAtygCtUsZr8GCX0Bn7J6sMMv7Yez5NSa6',
                    'key1' =>
                        array (
                            'key' => 'key1_13213',
                            'secret' => 'secret1_xxxxx',
                        ),
                    'key2' =>
                        array (
                            'key' => 'key2_13213',
                            'secret' => 'secret2_xxxxx',
                        ),
                ),
        ),
    'app_sts_key' =>
        array (
            'username' => 'LTAI5tHiYSZebqDeEE1GCrts',
            'password' => '******************************',
        ),
    'mail_unread_server_ks' =>
        array (
            'host' => '**********',
            'port' => '9604',
        ),
    'google_server_config' =>
        array (
            'host' => 'http://google-internal-api.dev.phpslb.com',
        ),
    'admin_read' =>
        array (
            'host' => 'mysql.dev.hz.xm-idc.cn',
            'port' => '3306',
            'db' => 'v4_admin',
            'username' => 'crmphp',
            'password' => 'THra2v5gV3mbbj6j5R8VFieh',
            'connection_string' => 'mysql:host=mysql.dev.hz.xm-idc.cn:3306;dbname=v4_admin',
            'extra_info' =>
                array (
                    'charset' => 'utf8mb4',
                ),
        ),
    'db_change_pgsql' =>
        array (
            'host' => 'pg.dev.hz.xm-idc.cn',
            'db' => 'db_change',
            'port' => '5432',
            'username' => 'crmroot',
            'password' => 'HtqY5Q3O6TTvlHIvVLZoHg1d',
        ),
    'db_change_mysql' =>
        array (
            'host' => 'mysql.dev.hz.xm-idc.cn',
            'port' => '3306',
            'db' => 'db_change',
            'username' => 'crmroot',
            'password' => 'HtqY5Q3O6TTvlHIvVLZoHg1d',
            'connection_string' => 'mysql:host=mysql.dev.hz.xm-idc.cn:3306;dbname=v5_client',
            'extra_info' =>
                array (
                    'charset' => 'utf8mb4',
                ),
        ),
    'login_service_api' =>
        array (
            'host' => 'http://login-service.dev.phpslb.com',
        ),
    'redis_error_queue' =>
        array (
            'host' => 'single.redis.dev.hz.xm-idc.cn',
            'port' => '6379',
            'db' => '0',
            'password' => 'pTKk8DNqH3QXAhPrk2BG9w9p',
        ),
    'redis_ai_queue' =>
        array (
            'host' => 'single.redis.dev.hz.xm-idc.cn',
            'port' => '6379',
            'db' => '0',
            'password' => 'pTKk8DNqH3QXAhPrk2BG9w9p',
        ),
    'leads_ads' =>
        array (
            'extra_info' =>
                array (
                    'ai_recommend_stage_1' => '2023-10-1',
                    'ai_recommend_stage_2' => '2023-11-25',
                    'expire_end_date' => '2099-04-01',
                    'sep_start_date' => '2023-11-14',
                    'sep_end_date' => '2023-12-31',
                    'daily_start_date' => '2023-11-01',
                ),
        ),
    'kms_service' =>
        array (
            'host' => 'kst-hzz6476fdf65szci8onvp.cryptoservice.kms.aliyuncs.com',
            'extra_info' =>
                array (
                    'protocol' => 'https',
                    'client_key_content' => '{"KeyId": "KAAP.8528264b-de01-41bf-9e64-fa5f75c0bb00", "PrivateKeyData": "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******************************/Ek5chtHtU38MWRwq9o/PPhrr5gDBqx1sMlQKxjzgd8Ns6x7jpfIUU2csu4SIXQgaEhbxzk8QjuFVWPmA3XDN7lbZH6XJKTQl3Ch+1GHM7PFrRMXZN9PfXS20h8G/D4pZ5dbEs2id3/Kd0hPPIhHUguOJ/qvbt0MwoAQWkwRJePU9B7CrGe7ApPBblfNQu32ogRGZF8H6HxgzfDnaf8+O3fa401ktdXZnf+OyiCPaJBPcYkKRto/uaPt8iDJVFCPIMrgC+lYs2cQmrqH5gqx2fEpwjuqcEXTRDDJoAVR0Q6MXhTHzElMCMGCSqGSIb3DQEJFTEWBBSMnuZCxLWLqt1T2FTgCxpgLKETWTArMB8wBwYFKw4DAhoEFLLwb/8WidT/ndBLkhYjqG6CeyDGBAhpQH8f162/uQ=="}',
                    'password' => 'xiaoman-php-secret-dev',
                    'key' => 'key-hzz65097451yqh5c2nm0e',
                ),
        ),
    'rocketmq_binlog_config' =>
        array (
            'extra_info' =>
                array (
                    'endpoint' => 'http://****************.mqrest.cn-hangzhou-internal.aliyuncs.com',
                    'access_id' => 'LTAI5tABwJoaZbSyGLBru6CC',
                    'access_key' => '******************************',
                    'instance_id' => 'MQ_INST_****************_BYhxJxh3',
                    'default_instance' => false,
                    'region_id' => 'cn-hangzhou',
                ),
        ),
    'data_work' =>
        array (
            'host' => 'hgpostcn-cn-wwo3a6zgg003-cn-hangzhou-vpc-st.hologres.aliyuncs.com',
            'port' => '80',
            'db' => 'xiaoman_ods_dev',
            'username' => 'LTAI5t7VHFjK1NkdNVgCRvq9',
            'password' => '******************************',
        ),
    'wecom' =>
        array (
            'corp_id' => 'ww5c1968a92f895520',
            'provider_secret' => '7j-PjjSYHgDHe4TiCH9DDVKPIzD1HRgWCYeJX6pp9YQ',
            'suite_id' => 'ww9b594c292a801314',
            'suite_secret' => 'Xbzlsm9asxPiU3_Xfc6MZSE7fRZK-uKh71izmwXNg1Q',
            'token' => 'CKf0If4TFOwn',
            'encoding_aes_key' => 'G5zrdWIfEMqx6qCFKLw5EvyGvzqdqOgOH4UnvMtmymC',
            'call_back_url' => 'https://leads-iteration-1-1-409.story.dev.xiaoman.cn',
            'auth_type' => 0, // 审核临时调整
            'message_public_key_version' => '2',
            'message_public_key' => '-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA7xKpoKVV3dsRv8Ga6pk+
7BePeEWKBGwHPO3+b7EqUov61T2uVz++aCoIYF0ETZHoWf2vlBZLIvRQXrpxddqx
R891zozWEHFUjtjV1QebyDO7dMYaUuj14D4UK6h3kP0G7pJwU2o091rB1sj/e92c
i9wxjDacJdHES99Vp1Snw0QbiJVFdeoJ+oNzO37CpFulxld+6hD0UbpveWZn8Avf
wmYea3r79ff4eoUbw+7ucpjcT8gk/BBk9M9vibLwdedmYNYTwmbe2uLv5W2IvtWk
K+wrF//m2u3nQL3pTp8SUKjRXw4Iu08FRWCvKlblSeM9Oz4828EvUAxY81dQ7coR
oQIDAQAB
-----END PUBLIC KEY-----',
            'message_private_key' => '******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************',
            'crm_pro' => 'spf979e3add20e0853',
            'crm_smart' => 'sp3e8565c78edc82cc',
            'crm_lite' => 'sp4209c77f3d678379',
            'crm_exp' => 'spb4ceb3a7df0acefc',
            'pay_secret' => 'jz2JM5gTWkrXIkd9t95L6J_nY8-e5q0yrr8QWZjRJhk',
            'program_id' => 'progbkLYPxH3Qcrg8nNGZsr8pMpG9xE38wEG',
            'chat_data' =>
                array (
                    'suite_id' => 'ww4352a68c34c57293',
                    'suite_secret' => 'x2vtO5cGTSohh-FGRwrS1KmHOCTdombT9Nb8-8qPjNU',
                    'token' => 'sJE8w5',
                    'encoding_aes_key' => 'ys3ucSHh9vrYz6fT1rCoYokb5z6UgEAkBMGYqW1r2ff',
                ),
            'buyer_userid' => 'jeremyguo',
            'purchase_notify_webhook' => 'https://oapi.dingtalk.com/robot/send?access_token=38487052d68a87cb2819bef6ce83e379b9fd12b348152c293cb65a66b7cf88e6',
            'purchase_notify_user' =>
                array (
                    'purchaser' => '18859236920',
                    'error' => '15917745060'
            ),
            'system_event' =>
                array (
                    'token' => 'c577M2z',
                    'encoding_aes_key' => '41o23BQldEzgIRGH1mwjgi2BrA5kCKWybchOUTEXpMs',
                ),
        ),
    'sensitive_exclude_client' =>
        array (
            'extra_info' =>
                array (
                    'client_ids' =>
                        array (
                            0 => 14399,
                        ),
                ),
        ),
    'login_inner_api' =>
        array (
            'host' => 'http://login-service.dev.phpslb.com',
            'extra_info' =>
                array (
                    'template_id' => 'IA5pH_mUoPttyl20Yakl7qVtSQc96ZXZ1TndBOqrZOM',
                ),
        ),
    'leads_service' =>
        array (
            'host' => 'http://leads-go-release-dev.dev.phpslb.com',
            'extra_info' =>
                array (
                    'enable' => true,
                ),
        ),
    'redis_binlog_mysql' =>
        array (
            'host' => 'single.redis.dev.hz.xm-idc.cn',
            'port' => '6379',
            'db' => '0',
            'password' => 'pTKk8DNqH3QXAhPrk2BG9w9p',
        ),
    'test_center_api' =>
        array (
            'host' => 'https://tester.dev.xiaoman.cn',
        ),
    'okki_leads_service' =>
        array (
            'host' => 'https://okki-leads.dev.xiaoman.cn',
        ),
    'fe_coverage_db' =>
        array (
            'connection_string' => 'mysql:host=mysql.dev.hz.xm-idc.cn:3306;dbname=auto_api',
            'username' => 'crmphp',
            'password' => 'THra2v5gV3mbbj6j5R8VFieh',
            'extra_info' =>
                array (
                    'charset' => 'utf8mb4',
                ),
            'host' => 'mysql.dev.hz.xm-idc.cn',
            'port' => '3306',
            'db' => 'auto_api',
        ),
    'tm_message_db' =>
        array (
            'host' => '**********',
            'port' => '32244',
            'db' => 'ali_ai_reception',
            'username' => 'root',
            'password' => 'okki2024',
            'connection_string' => 'mysql:host=**********:32244;dbname=ali_ai_reception',
            'extra_info' =>
                array (
                    'charset' => 'utf8mb4',
                ),
        ),
    'large_attach_download_host' =>
        array (
            'extra_info' =>
                array (
                    'mail_large_attach_download_host' => 'https:\\t.xmdload.com',
                    'expose_mail_large_attach_download_host' => 'https:\\t.xmdlpro.com',
                ),
        ),
    'mail_track_filter_ip' =>
        array(
            'extra_info' =>
                array(
                    'ips' => [
                        '**************', '*************'
                    ]
                ),
        ),
    'okki-io_inner_api' =>
        array (
            'host' => 'http://okki-io.dev.phpslb.com',
        ),
    'personal_account_api' => [
        'host' => 'http://personal-account.dev.phpslb.com',
    ],
    'exp_client' =>
        array (
            'extra_info' =>
                array (
                    'client_ids' =>
                        array (
                            0 => 111,
                            1 => 222,
                        ),
                ),
        ),
    'redis_pg_binlog' =>
        array (
            'host' => 'single.redis.dev.hz.xm-idc.cn',
            'port' => '6379',
            'db' => '255',
            'password' => 'pTKk8DNqH3QXAhPrk2BG9w9p',
        ),
    'prometheus_git_qyWechat' =>
        array (
            'extra_info' =>
                array (
                    'token' => 'SRbkhFt8SF5Lkplyhl63zERNfQMj39yl',
                    'encodingaeskey' => 'PjIhg8YbrvE5sMhhXZhzZdLpOCGhq7PFGxMTdFrzdLt',
                    'appid' => 'ww5c1968a92f895520',
                    'appsecret' => 'zE0n_K1rnPvwBqCtVMA5GxUC_tRkAOfLuI_N3MqfkCY',
                    'agentid' => 1000007,
                    'debug' => false,
                    '_logcallback' => 'logg',
                ),
        ),
    'juhe' =>
        array (
            'password' => '28cb92556de3c5e34c54ab8fc83a4e53',
            'host' => 'http://web.juhe.cn:8080',
        ),
    'paypal' =>
        array (
            'host' => 'http://*************:3128',
            'username' => 'AYPYSg5Gs21VtGuBvObaQEgjGXFeywRmPiS0YRg_I8Lk3RMYVYml0vYSgbDGWZideOYVNyHMycqBy6Lm',
            'password' => 'ED_uKVlzoLUJB-IQFYeikqlnMAJ9d5EnZ3IAhR9WdvI_javOrf3gRhlE6ebYo8ViuaV7-rKHOAxNzcal',
            'db' => '2V68117308108950T',
        ),
    'prometheus_git_dingTalk' =>
        array (
            'extra_info' =>
                array (
                    'token' => 'SRbkhFt8SF5Lkplyhl63zERNfQMj39yl',
                    'appkey' => 'ding4vcuccdcdf27uldz',
                    'appsecret' => 'QkumB9ltoZ1q5GhXfTRRG_pRgHCTYPZDImdwy78XQavUftoCOB_iyoCsrKOfEADn',
                    'agentid' => 1024746152,
                    'debug' => false,
                    '_logcallback' => 'logg',
                    'robot' =>
                        array (
                            'dev' =>
                                array (
                                    'secret' => 'SEC38be9357c1db6c3d775279fa13519a89c4c8545f447e41b08347c109aa437d21',
                                    'access_token' => 'c70fb5fd40626b48e2eb5a42b34ad320d6efec150c3c2f01062d4fd7fef3ba84',
                                ),
                            'test_center' =>
                                array (
                                    'secret' => 'SEC38be9357c1db6c3d775279fa13519a89c4c8545f447e41b08347c109aa437d21',
                                    'access_token' => 'c70fb5fd40626b48e2eb5a42b34ad320d6efec150c3c2f01062d4fd7fef3ba84',
                                ),
                            'bugfix' =>
                                array (
                                    'secret' => 'SEC38be9357c1db6c3d775279fa13519a89c4c8545f447e41b08347c109aa437d21',
                                    'access_token' => 'c70fb5fd40626b48e2eb5a42b34ad320d6efec150c3c2f01062d4fd7fef3ba84',
                                ),
                            'test-center-ticket' =>
                                array (
                                    'secret' => 'SEC38be9357c1db6c3d775279fa13519a89c4c8545f447e41b08347c109aa437d21',
                                    'access_token' => 'c70fb5fd40626b48e2eb5a42b34ad320d6efec150c3c2f01062d4fd7fef3ba84',
                                ),
                            'front_robot' =>
                                array (
                                    'secret' => 'SEC96937b312c4e579f56aa65419da8cc3728355b292b453aecae2624eee3e12e0b',
                                    'access_token' => '9eff7b3a5391549a89e934c8dd76d894148e5626fa37cfd1c271e828b721960b',
                                ),
                            'php_robot' =>
                                array (
                                    'secret' => 'SEC96937b312c4e579f56aa65419da8cc3728355b292b453aecae2624eee3e12e0b',
                                    'access_token' => '9eff7b3a5391549a89e934c8dd76d894148e5626fa37cfd1c271e828b721960b',
                                ),
                            'app_robot' =>
                                array (
                                    'secret' => 'SEC96937b312c4e579f56aa65419da8cc3728355b292b453aecae2624eee3e12e0b',
                                    'access_token' => '9eff7b3a5391549a89e934c8dd76d894148e5626fa37cfd1c271e828b721960b',
                                ),
                            'java_robot' =>
                                array (
                                    'secret' => 'SEC96937b312c4e579f56aa65419da8cc3728355b292b453aecae2624eee3e12e0b',
                                    'access_token' => '9eff7b3a5391549a89e934c8dd76d894148e5626fa37cfd1c271e828b721960b',
                                ),
                            'shop_product_robot' =>
                                array (
                                    'secret' => 'SEC3c46c2aadaf9fcc99135fb95df46d4617c68300bb43dbcc5f93935d3ad1c4fc2',
                                    'access_token' => '2f68e2c8ba179ea43bc84faf25647dc862fe4b993fee75d01752de9154fe2bb5',
                                ),
                            'grey_robot' =>
                                array (
                                    'secret' => 'SECe84538bd0e59304a8aad4dbba7e78d9c6037d6105eab0e0439aae8df81199e53',
                                    'access_token' => '6c63a4a8ec799fd627ecaac5d2991af3a61f956344b84f005da151d05a90cfaf',
                                ),
                            'ticket_group_robot' =>
                                array (
                                    'secret' => 'SEC0c3963c64f7cf2493094a9608661c9f25c70685d3c0bcd327f623dbca02360e0',
                                    'access_token' => 'ba2c2bf57bbbb02fc1e5c48d70e924ddb90b576e9ba63fd82db30179afce5260',
                                ),
                            'nps_robot' =>
                                array (
                                    'secret' => 'SEC65a2071cc06ccf6663eb3f0f4558c20dec9e64427566a99775f12ce9dec05ce5',
                                    'access_token' => '516a33f02aa24d87065792b2f5574e475d47a4746115cf4a9e12a305d68c0e31',
                                ),
                            'java_rt_robot' =>
                                array (
                                    'secret' => 'SEC96937b312c4e579f56aa65419da8cc3728355b292b453aecae2624eee3e12e0b',
                                    'access_token' => '9eff7b3a5391549a89e934c8dd76d894148e5626fa37cfd1c271e828b721960b',
                                ),
                            'gitlab_push_robot_php' =>
                                array (
                                    'secret' => 'SEC7ceb1d5806f8cf54e984bfc07bc6c21ec996e0c2935fbb3421e7cea01778362c',
                                    'access_token' => '5a2d0fb312c9c4a23b9de4c96881aaaf2f2992c8d5417394dab85edfb44544d2',
                                ),
                            'gitlab_push_robot_fe' =>
                                array (
                                    'secret' => 'SEC29a4b08203f39c3a47d8f8bf9a53fa0b24c17527b5bb9d8bd1c7a64d7783358c',
                                    'access_token' => '2a5acada14384363e025bb21dc7472bd7640e0109f4d34860f4cdb62cf20e7b8',
                                ),
                            'code_merge_robot' =>
                                array (
                                    'secret' => 'SEC4664a5fc3d438fe92b3bbdb85d6f9f417de2d10725dbfe6a37567b630b55d876',
                                    'access_token' => 'b5c89d29bfe7823bfe7bf34605f5a1b7de89eed1cb9e7299cef2731788a1626b',
                                ),
                            'product_group_robot' =>
                                array (
                                    'secret' => '',
                                    'access_token' => '',
                                ),
                            'nginx_lua_robot' =>
                                array (
                                    'secret' => '',
                                    'access_token' => '',
                                ),
                            'init_client_notice_robot' =>
                                array (
                                    'secret' => '',
                                    'access_token' => '',
                                ),
                            'apifox_duplicatePath_notice_robot' =>
                                array (
                                    'secret' => 'SECc86908a6abe91c8490985e0276e4628b53a785887f1265875df6fb08c6614d98',
                                    'access_token' => '1b4574760b94b99adca036fdf59ce0c62b64a14801e4cf2174caf3dd526521bf',
                                ),
                            'mobile_release_notify' =>
                                array (
                                    'secret' => 'SEC0bbf718100562791b3ec4f8f99fcdb7cb325b5247a2a9852b792bb7bc5c048a1',
                                    'access_token' => 'febc95c0019c7bd6cc8f81011c09f7433a84f3affb4f132a6771e41baa827278',
                                )
                        ),
                    'todo_creator' => 'NzQzMStsHiiL1UxCfTbaClAiEiE'
                ),
        ),
    'prometheus_git_token' =>
        array (
            'username' => 'cayley_123456',
            'password' => 'pBXZ246NNkM3aEfz',
        ),
    'ewei' =>
        array (
            'username' => 'ODYy',
            'password' => '4ca5b65c14d44073bdcc61056adc95ca',
            'host' => 'https://xmkf.ewei.com',
        ),
    'third_party_client_gitlab' =>
        array (
            'host' => 'https://gitlab.xiaoman.cc/api/v4',
            'password' => '8NUXKaTwJB2w7x5Jaxws',
        ),
    'third_party_client_gitlab_devops' =>
        array (
            'host' => 'https://gitlab.xiaoman.cc/api/v4',
            'password' => 'xEn-ckSeRgquMnPyJL76',
        ),
    'third_party_client_ks' =>
        array (
            'host' => 'https://ks-apiserver-idc.dev.xiaoman.cn',
            'username' => '<EMAIL>',
            'password' => 'Q1w2e3r4',
        ),
    'tapd' =>
        array (
            'host' => 'https://api.tapd.cn',
            'username' => 'cOG=^GXJ',
            'password' => 'DD043B61-9BFE-19FC-F6C2-C27F4BB51F1D',
            'extra_info' =>
                array (
                    'auth_key' => 'Y09HPV5HWEo6REQwNDNCNjEtOUJGRS0xOUZDLUY2QzItQzI3RjRCQjUxRjFE',
                    'company_id' => 20036231,
                ),
        ),
    'third_party_client_shangzhibo' =>
        array (
            'host' => 'https://live.train.xiaoman.cn',
            'password' => 'a057f190-eb93-11eb-bf05-a14d4bf6af78',
        ),
    'third_party_client_sonar_qube' =>
        array (
            'host' => 'https://sonarqube.xiaoman.cn',
            'password' => 'YWRtaW46eCttQHN6MjJGNSE=',
        ),
    'prometheus_log_notice_qyWechat' =>
        array (
            'extra_info' =>
                array (
                    'token' => '',
                    'encodingaeskey' => '',
                    'appid' => '',
                    'appsecret' => '',
                    'agentid' => 1000007,
                    'debug' => false,
                    '_logcallback' => '',
                ),
        ),
    'send_mail_smtp_config' =>
        array (
            '<EMAIL>' =>
                array (
                    'smtp_host' => 'smtp.qiye.aliyun.com',
                    'smtp_port' => 465,
                    'smtp_ssl' => true,
                    'smtp_username' => '<EMAIL>',
                    'smtp_password' => 'Okki2023',
                    'smtp_from' => '<EMAIL>',
                    'smtp_from_name' => 'OKKI CRM',
                    'domain_options' =>
                        array (
                            'okki.com' =>
                                array (
                                    'smtp_username' => '<EMAIL>',
                                    'smtp_from' => '<EMAIL>',
                                ),
                        ),
                ),
            '<EMAIL>' =>
                array (
                    'smtp_host' => 'smtpdm.aliyun.com',
                    'smtp_port' => 465,
                    'smtp_ssl' => true,
                    'smtp_username' => '<EMAIL>',
                    'smtp_password' => '06ao2OHlmEGFYlH',
                    'smtp_from' => '<EMAIL>',
                    'smtp_from_name' => 'OKKI CRM',
                    'domain_options' =>
                        array (
                            'okki.com' =>
                                array (
                                    'smtp_username' => '<EMAIL>',
                                    'smtp_from' => '<EMAIL>',
                                ),
                        ),
                ),
            '<EMAIL>' =>
                array (
                    'smtp_host' => 'smtp.qiye.aliyun.com',
                    'smtp_port' => 465,
                    'smtp_ssl' => true,
                    'smtp_username' => '<EMAIL>',
                    'smtp_password' => '06ao2OHlmEGFYlH',
                    'smtp_from' => '<EMAIL>',
                    'smtp_from_name' => 'OKKI CRM',
                    'domain_options' =>
                        array (
                            'okki.com' =>
                                array (
                                    'smtp_username' => '<EMAIL>',
                                    'smtp_from' => '<EMAIL>',
                                ),
                        ),
                ),
            '<EMAIL>' =>
                array (
                    'smtp_host' => 'smtpdm.aliyun.com',
                    'smtp_port' => 465,
                    'smtp_ssl' => true,
                    'smtp_username' => '<EMAIL>',
                    'smtp_password' => 'jaq6z8whSMSV',
                    'smtp_from' => '<EMAIL>',
                ),
            '<EMAIL>' =>
                array (
                    'smtp_host' => 'smtpdm-ap-southeast-1.aliyuncs.com',
                    'smtp_port' => 465,
                    'smtp_ssl' => true,
                    'smtp_username' => '<EMAIL>',
                    'smtp_password' => 'jaq6z8whSMSV',
                    'smtp_from' => '<EMAIL>',
                ),
        ),
    'v5_global_sources_agent_url' =>
        array (
            'host' => 'http://**************:30128/',
        ),
    'wechat_client' =>
        array (
            'extra_info' =>
                array (
                    'encodingaeskey' => 'JgDHTEoHFVGSeUE3luyqfNdFpsJNOXJEtA261x2n7ir',
                ),
            'password' => 'a585c335d6aa9ed9f8783c63678b7102',
            'username' => 'wx1218bf019b87d636',
        ),
    'submail' =>
        array (
            'username' => '13507',
            'password' => '7f13a6839d2a652c93756b632b9315eb',
            'extra_info' =>
                array (
                    'sign_type' => 'sha1',
                    'sender' =>
                        array (
                            'name' => 'OKKI CRM MAIL',
                            'address' => '<EMAIL>',
                        ),
                ),
            'domain_options' =>
                array (
                    'okki.com' =>
                        array (
                            'appid' => '18209',
                            'appkey' => 'c69e68211904937428e82f836c8565b8',
                            'address' => '<EMAIL>',
                        ),
                ),
        ),
    'submail_callback_os' =>
        array (
            'username' => '17350',
            'password' => '4f3a6d4c6bfdccb3dc78d8f160124808',
            'extra_info' =>
                array (
                    'sign_type' => 'sha1',
                    'sender' =>
                        array (
                            'name' => 'OKKI CRM MAIL',
                            'address' => '<EMAIL>',
                        ),
                ),
            'domain_options' =>
                array (
                    'okki.com' =>
                        array (
                            'appid' => '18210',
                            'appkey' => 'ab8e12b07f7449bf547c18cc9277cab5',
                            'address' => '<EMAIL>',
                        ),
                ),
        ),
    'gs_host' =>
        array (
            'host' => 'https://message.qa.globalsources.com/ajax/message/replyByThirdParty',
        ),
    'edm_url' =>
        array (
            'delivery_url' => 'http://kcallback.dev.xiaoman.cn/api/track/edmDelivery',
            'track_url' => 'http://kcallback.dev.xiaoman.cn/api/track/edm',
            'url_redirect_host' =>
                array (
                ),
        ),
    'old_large_attach_download_host' =>
        array (
            0 => 'https://t.xmdcsw.site',
        ),
    'mail_assistant_url' =>
        array (
            'host' => 'https://mail-helper.prod.vpcslb.com',
        ),
    'qywechat' =>
        array (
            'extra_info' =>
                array (
                ),
            'username' => 'wwae1e6c735a5fee7a',
            'password' => '6Tp3kI0Q9P1QEkHUW63FsDzx9sDi1JFGJXBSJM4-618',
        ),
    'sobot' =>
        array (
            'host' => 'https://www.sobot.com',
            'username' => 'a4b53b02925242e08f06f8b52f1df24e',
            'password' => 'q8h35Da82cbb',
            'extra_info' =>
                array (
                    'company_id' => '78a42d531fd54320b3e99bda9fa36735',
                    'ticket_typeid' => '879f427754294d82ad43b9b44450d6cd',
                ),
        ),
    'sns_oauth' =>
        array (
            'facebook' =>
                array (
                    'client_id' => '448394841430502',
                    'client_secret' => 'b4ee9af2cf815418045609d6d20826de',
                ),
            'linkedin' =>
                array (
                    'client_id' => '818ngps2zqm8za',
                    'client_secret' => 'tO0yjfpprM8h7VDl',
                ),
            'twitter' =>
                array (
                    'client_id' => 'tkeFDCNPmVn4JDm75wwA0IXeO',
                    'client_secret' => 'hHPyJeuuCMpg7rgsXZeylQpe41HoF3Db3rqyv3kJOKaVegBIlq',
                ),
        ),
    'edm_db' =>
        array (
            'username' => 'crmphp',
            'password' => 'THra2v5gV3mbbj6j5R8VFieh',
            'connection_string' => 'mysql:host=mysql.dev.hz.xm-idc.cn;dbname=edm',
            'db' => 'edm',
            'port' => '3306',
            'host' => 'mysql.dev.hz.xm-idc.cn',
            'extra_info' =>
                array (
                    'charset' => 'utf8mb4',
                ),
        ),
    'tidb' =>
        array (
            'connection_string' => 'mysql:host=mysql.dev.hz.xm-idc.cn;dbname=edm',
            'db' => 'edm',
            'host' => 'mysql.dev.hz.xm-idc.cn',
            'port' => '3306',
            'username' => 'crmphp',
            'password' => 'THra2v5gV3mbbj6j5R8VFieh',
            'extra_info' =>
                array (
                    'charset' => 'utf8mb4',
                ),
        ),
    'common_vector_db' =>
        array (
            'host' => 'pg.dev.hz.xm-idc.cn',
            'username' => 'crmphp',
            'password' => 'reTmxR9aGAj3r4oHsZNjjPtH',
            'port' => '5432',
            'db' => 'common_vector',
            'connection_string' => 'pgsql:host=pg.dev.hz.xm-idc.cn;dbname=common_vector;port=5432',
        ),
    'rocketmq_pgwal_message_job_config' =>
        array (
            'extra_info' =>
                array (
                    'endpoint' => 'http://****************.mqrest.cn-hangzhou-internal.aliyuncs.com',
                    'access_id' => 'LTAI5tABwJoaZbSyGLBru6CC',
                    'access_key' => '******************************',
                    'instance_id' => 'MQ_INST_****************_BYhxJxh3',
                    'default_instance' => false,
                    'region_id' => 'cn-hangzhou',
                ),
        ),
    'app_push' =>
        array (
            'host' => 'http://websocket-api.dev.phpslb.com',
        ),
    'enterprise_db' =>
        array (
            'connection_string' => 'mysql:host=mysql.dev.hz.xm-idc.cn;dbname=erp_client',
            'username' => 'crmphp',
            'password' => 'THra2v5gV3mbbj6j5R8VFieh',
            'extra_info' =>
                array (
                    'charset' => 'utf8mb4',
                ),
            'db' => 'erp_client',
            'host' => 'mysql.dev.hz.xm-idc.cn',
            'port' => '3306',
        ),
    'ali_waihui_api' =>
        array (
            'app_key' => 204581951,
            'app_secret' => 'TemduTSNaUOsQJ8Tlnz6kf5ncT9MheO2',
            'app_code' => '3e4ac1c1c67d4a5cb31637b80808b119',
        ),
    'meServiceHost' =>
        array (
            'host' => 'http://dev.me.xiaoman.cn',
        ),
    'ximServiceHost' =>
        array (
            'host' => 'http://dev.xim-admin.xiaoman.cn',
        ),
    'aliyun_alimail' =>
        array (
            'db' => 'cn-hangzhou',
            'username' => 'LTAI5tRy5qWoXGw5RMy6ypd1',
            'password' => '******************************',
        ),
    'alimail' =>
        array (
            'extra_info' =>
                array (
                    'aes_key' => '9LKg6RqYG+pEo7rrU+ZZyg==',
                    'signature_token' => 'pEo7rrU',
                    'subscribe_url' => 'https://imap-idle.dev.xiaoman.cn/webhook/alimail',
                ),
            'host' => 'https://alimail-cn.aliyuncs.com',
            'username' => 'DkN1Wf6rLTXY9xM3',
            'password' => 'BRZ1JjpvBaMpGvYsV7yBkVTbHO2gImProJH7mwel9wm3JX9jZaVGaCJjp1VpB2ot',
        ),
    'http_type' => 'http',
    'enterprise_oss' =>
        array (
            'oss_bucket' => 'jinyuncrmdevelop',
            'oss_id' => 'LTAI5tAuNvZcUaVMVUSPNutb',
            'oss_key' => '******************************',
            'oss_host' => '.oss-cn-hangzhou.aliyuncs.com',
            'oss_dir' => 'uploader',
        ),
    'apifox_db' =>
        array (
            'db' => 'apifox',
            'host' => 'rm-bp14659f99403t0mh.mysql.rds.aliyuncs.com',
            'port' => '3306',
            'username' => 'apifox',
            'password' => '******************************',
            'extra_info' =>
                array (
                    'charset' => 'utf8mb4',
                ),
            'connection_string' => 'mysql:host=rm-bp14659f99403t0mh.mysql.rds.aliyuncs.com:3306;dbname=apifox',
        ),
    'clientWhiteList' =>
        array (
            'client_ids' =>
                array (
                    0 => 14826,
                ),
        ),
    'gateway_auth' =>
        array (
            'jwt_secret' => 'eui5Bq1NwOIAEFSvye7tVIKExys8myYl',
            'jwt_alg' => 'HS256',
        ),
    'redis_pg_wal_pack_distribute' =>
        array (
            'host' => 'single.redis.dev.hz.xm-idc.cn',
            'port' => '6379',
            'db' => '15',
            'password' => 'pTKk8DNqH3QXAhPrk2BG9w9p',
        ),
    'enterprise_common_redis' =>
        array (
            'host' => 'cluster.redis.dev.hz.xm-idc.cn',
            'port' => '6379',
            'password' => 'pTKk8DNqH3QXAhPrk2BG9w9p',
            'db' => '0',
        ),
    'system_custom_config' =>
        array (
            'extra_info' =>
                array (
                    'encrypt_salt' => 'X1a0mAnZbbCg',
                    'desktop_sts_token' =>
                        array (
                            'access_id' => 'LTAI5t8F7hyitiSAcmfAaEYi',
                            'access_key' => '******************************',
                            'role_arn' => 'acs:ram::****************:role/crm-desktop-client',
                        ),
                ),
        ),
    'tapdGitlabDefaultModule' =>
        array (
            0 =>
                array (
                    'module_name' => '后端PHP-CRM模版',
                    'group_id' => 136,
                    'group_name' => 'php',
                    'project_id' => 186,
                    'project_name' => 'PHP-CRM',
                ),
            1 =>
                array (
                    'module_name' => '前端Galio模版',
                    'group_id' => 9,
                    'group_name' => 'FE',
                    'project_id' => 975,
                    'project_name' => 'Galio',
                ),
        ),
    'dingtalk' =>
        array (
            'app_id' => '3d422a5e-ec1d-4acb-8006-af3db7ad7e54',
            'app_client_id' => 'suitemhbc2miufacmaerd',
            'app_client_secret' => 'nVf9Fs8edJrVCOwFpLoJuqJzQy2TlxBJY3xtjCnZtnxtUWPcCbqoD-JVr31SnMm_',
            'token' => 'Wdycxl8TmBiRM92w3ALTM',
            'aes_key' => 'ckARfmqvAvLfs2REMHJS7CDov7KyCFAdbQO6e6mqc2F',
            'redirect_host' => 'http://ding.dev.xiaoman.cn',
            'message_template' =>
                array (
                    'approval_tips' => 'e241eeaa86d14573b626c161e78b280b',
                    'workflow_notice' => '55d7b7f421914a8fb0fb00be71f18e04',
                    'approval_tips_new' => 'd4473ae9493146e1a16bf7ee2b18b401',
                    'customer_notice' => '702f27018bc04ddd92b7bd1fee3680d3',
                ),
            'dingtalk_scope' =>
                array (
                    'organization_scopes' =>
                        array (
                            0 => 'qyapi_get_department_list',
                            1 => 'qyapi_get_department_member',
                        ),
                    'user_scopes' =>
                        array (
                            0 => 'Calendar.Event.Write',
                            1 => 'Calendar.Event.Read',
                        ),
                    'scope_version' => '1.1.4',
                ),
            'fast_create_group_service' =>
                array(
                    'ding4vcuccdcdf27uldz' => // 小满研发机器人 robotCode
                        array(
                            0 => array(
                                'keyword' => '建群', // @机器人时发送的触发服务的关键词
                                'scene_group_template_id' => '8c070eb8-5f0a-4084-b3e7-9485c1b11764', // 场景群建群模版id
                                'default_group_name' => array(
                                    'cidTOA6dyESTLarI+EyYS57LQ==' => '生产事故处理', // 研发售后群
                                    'cidTOfTjVu+k1bp4T3ekxSDdg==' => '测试快速建群', // 小满研发-测试群
                                ), // 源头群id => 创建群名 映射
                                'default_users' => array(
                                    'cidTOA6dyESTLarI+EyYS57LQ==' => array(
                                        "<EMAIL>",
                                        "<EMAIL>",
                                        "<EMAIL>",
                                        "<EMAIL>",
                                        "<EMAIL>",
                                        "<EMAIL>",
                                        "<EMAIL>",
                                        "<EMAIL>"
                                    ),
                                    'cidTOfTjVu+k1bp4T3ekxSDdg==' => array(
                                        "<EMAIL>",
                                        "<EMAIL>"
                                    ),
                                ), // 源头群id => 快速建群默认拉取的用户 映射
                                'enable_reply' => true, // 完成建群后是否往原群发送通知
                            )
                        ),
                ),
        ),
    'ding_leads' =>
        array(
            'app_id' => '48ad1d02-590b-4b60-8fe2-e7afc4813d50',
            'app_client_id' => 'suite0ux7tnc4wzssvdjj',
            'app_client_secret' => 'bOy4fq5119r53OQX67z1TRe96BDBT7Ftx4ii98R4ASJ-s_W6FZp6dHV2A_czrpAK',
            'token' => 'MXRW8DDmz9zEBKdY0aZOOT1G8NBx2uA8zRI3h7ddK9ATj',
            'aes_key' => 'IWdbBLY8oE6zOQOXKCE1nNVzWcqv5FDWd2KhSCJHUjA',
        ),
    'ding_leads_ai_personal' =>
        array(
            'app_id' => 'f51b1efc-d912-480a-a55c-3d3115ef60f2',
            'app_client_id' => 'suitepayevbnalr1qlncu',
            'app_client_secret' => 'rHfRX9npLY9gUwPAE0VLhGfoNKvHw4c_FKtWy_YnNgq-bi-TO4J14t1_fuEyUq47',
            'token' => '4Hs1TR4T5O8TadanpYr1hgTHEy',
            'aes_key' => '6kh7HRcQwCoP5WHadzxRNiD37K6OcCeLdlLgR7AddEO',
        ),
    'resource_download' =>
        array (
            'proxy' =>
                array (
                    'type' => 'http',
                    'host' => '************',
                    'port' => '80',
                ),
            'white_list' =>
                array (
                    0 => 'cdn.xiaoman.cn',
                    1 => 'oss-cn-hangzhou-internal.aliyuncs.com',
                    2 => 'oss-cn-hangzhou.aliyuncs.com',
                    3 => 'mfile.budding.cc',
                ),
        ),
    'redis_prometheus' =>
        array (
            'host' => 'single.redis.dev.hz.xm-idc.cn',
            'port' => '6379',
            'db' => '0',
            'password' => 'pTKk8DNqH3QXAhPrk2BG9w9p',
        ),
    'redis_assistant' =>
        array (
            'port' => '6379',
            'db' => '0',
            'host' => 'single.redis.dev.hz.xm-idc.cn',
            'password' => 'pTKk8DNqH3QXAhPrk2BG9w9p',
        ),
    'third_party_client_dataworks' =>
        array (
            'host' => 'http://datax-job-processor.dev.vpcslb.com',
        ),
    'waha_db' =>
        array (
            'type' => 'pgsql',
            'db' => 'pg_admin',
            'host' => 'pg.dev.hz.xm-idc.cn',
            'port' => '5432',
            'username' => 'crmroot',
            'password' => 'HtqY5Q3O6TTvlHIvVLZoHg1d',
            'connection_string' => 'pgsql:host=pg.dev.hz.xm-idc.cn;dbname=pg_admin;port=5432',
        ),
    'crm_company' => [
        'type' => 'pgsql',
        'db' => 'crm_company',
        'host' => 'pg.dev.hz.xm-idc.cn',
        'port' => '5432',
        'username' => 'crmroot',
        'password' => 'HtqY5Q3O6TTvlHIvVLZoHg1d',
        'connection_string' => 'pgsql:host=pg.dev.hz.xm-idc.cn;dbname=crm_company;port=5432',
    ],
    'text_in_api' =>
        array(
            'app_id' => '1e7ac4b01211b14c0e33a5009352fb5d',
            'app_secret_code' => '5ee9215f2a4d4b2c31121582eaab4ae9',
        ),
    'center_module_manager_path' =>
        array (
            0 =>
                array (
                    'center' => '创新产品研发中心',
                    'module' => '一站式获客-智能获客',
                    'manager' => 'troy',
                    'path' =>
                        array (
                            0 => '/new_discovery/mining',
                            1 => '/new_discovery/mining-v2/*',
                            2 => '/discovery',
                            3 => '/discovery/*',
                        ),
                ),
            1 =>
                array (
                    'center' => '创新产品研发中心',
                    'module' => '一站式获客-公司详情页，抽屉',
                    'manager' => 'troy',
                    'path' =>
                        array (
                            0 => '/new_discovery/company/*',
                            1 => '/discovery/company/byname/*',
                        ),
                ),
            2 =>
                array (
                    'center' => '创新产品研发中心',
                    'module' => '渠道获客-搜索引擎',
                    'manager' => 'troy',
                    'path' =>
                        array (
                            0 => '/new_discovery/google-data/list',
                            1 => '/new_discovery/search-engine',
                        ),
                ),
            3 =>
                array (
                    'center' => '创新产品研发中心',
                    'module' => '渠道获客-智能贸易数据',
                    'manager' => 'troy',
                    'path' =>
                        array (
                            0 => '/ciq/*',
                            1 => '/discovery/ciq-datum',
                            2 => '/new_discovery/ciq-datum/*',
                            3 => '/new_discovery/ciq-datum',
                            4 => '/new_discovery/ciq-datum/*',
                        ),
                ),
            4 =>
                array (
                    'center' => '创新产品研发中心',
                    'module' => '渠道获客-展会数据',
                    'manager' => 'troy',
                    'path' =>
                        array (
                            0 => '/new_discovery/exhibition-data',
                            1 => '/new_discovery/exhibition-data-v2',
                        ),
                ),
            5 =>
                array (
                    'center' => '创新产品研发中心',
                    'module' => '渠道获客-地图获客',
                    'manager' => 'troy',
                    'path' =>
                        array (
                            0 => '/new_discovery/map-acquisition',
                        ),
                ),
            6 =>
                array (
                    'center' => '创新产品研发中心',
                    'module' => '渠道获客-社媒数据',
                    'manager' => 'troy',
                    'path' =>
                        array (
                            0 => '/new_discovery/social-media-data',
                        ),
                ),
            7 =>
                array (
                    'center' => '创新产品研发中心',
                    'module' => '动态监测-社媒动态',
                    'manager' => 'troy',
                    'path' =>
                        array (
                            0 => '/new_discovery/monitor-v2',
                        ),
                ),
            8 =>
                array (
                    'center' => '创新产品研发中心',
                    'module' => '动态监测-交易动态',
                    'manager' => 'troy',
                    'path' =>
                        array (
                            0 => '/new_discovery/subscribe/*',
                        ),
                ),
            9 =>
                array (
                    'center' => '创新产品研发中心',
                    'module' => '沟通跟进-AI营销跟进',
                    'manager' => 'troy',
                    'path' =>
                        array (
                            0 => '/ai/followup',
                        ),
                ),
            10 =>
                array (
                    'center' => '创新产品研发中心',
                    'module' => '沟通跟进-浏览记录',
                    'manager' => 'troy',
                    'path' =>
                        array (
                            0 => '/discovery/history',
                        ),
                ),
            11 =>
                array (
                    'center' => '创新产品研发中心',
                    'module' => '数据获客（旧版营销）-OKKI 搜索',
                    'manager' => 'troy',
                    'path' =>
                        array (
                            0 => '/discovery/search/results',
                        ),
                ),
            12 =>
                array (
                    'center' => '创新产品研发中心',
                    'module' => '数据获客（旧版营销）-定制推荐',
                    'manager' => 'troy',
                    'path' =>
                        array (
                            0 => '/discovery/myrecommend',
                            1 => '/discovery/recommend/program',
                        ),
                ),
            13 =>
                array (
                    'center' => '创新产品研发中心',
                    'module' => '沟通跟进-Facebook帖子',
                    'manager' => 'robb',
                    'path' =>
                        array (
                            0 => '/tms/facebook-comment',
                        ),
                ),
            14 =>
                array (
                    'center' => '创新产品研发中心',
                    'module' => '沟通跟进-Facebook Lead',
                    'manager' => 'robb',
                    'path' =>
                        array (
                            0 => '/talk/facebook-lead-form',
                        ),
                ),
            15 =>
                array (
                    'center' => '创新产品研发中心',
                    'module' => '企业管理-社媒管理',
                    'manager' => 'robb',
                    'path' =>
                        array (
                            0 => '/talk/socialMediaManage/*',
                        ),
                ),
            16 =>
                array (
                    'center' => '创新产品研发中心',
                    'module' => '一站式获客-推荐广场',
                    'manager' => 'bobo',
                    'path' =>
                        array (
                            0 => '/new_discovery/recommend-square',
                        ),
                ),
            17 =>
                array (
                    'center' => '创新产品研发中心',
                    'module' => '一站式获客-排行榜',
                    'manager' => 'bobo',
                    'path' =>
                        array (
                            0 => '/new_discovery/ranking',
                        ),
                ),
            18 =>
                array (
                    'center' => '创新产品研发中心',
                    'module' => '一站式获客-关注列表',
                    'manager' => 'bobo',
                    'path' =>
                        array (
                            0 => '/new_discovery/favorites-list',
                        ),
                ),
            19 =>
                array (
                    'center' => '创新产品研发中心',
                    'module' => '一站式获客-B2B询盘',
                    'manager' => 'bobo',
                    'path' =>
                        array (
                            0 => '/new_discovery/inquiry',
                        ),
                ),
            20 =>
                array (
                    'center' => '创新产品研发中心',
                    'module' => 'Whatsapp营销 - 企业版（WABA）-WABA营销数据看版 - 管理场景',
                    'manager' => 'bobo',
                    'path' =>
                        array (
                            0 => '/talk/waba-marketing/waba',
                            1 => '/talk/waba-marketing/wa',
                            2 => '/talk/waba-marketing',
                        ),
                ),
            21 =>
                array (
                    'center' => '创新产品研发中心',
                    'module' => 'Whatsapp营销-个人版-WhatsAp营销数据看版-管理场景',
                    'manager' => 'bobo',
                    'path' =>
                        array (
                            0 => '/talk/waba-marketing/personal',
                        ),
                ),
            22 =>
                array (
                    'center' => '创新产品研发中心',
                    'module' => '沟通-WhatsApp个人版-只有群发',
                    'manager' => 'bobo',
                    'path' =>
                        array (
                            0 => '/talk/hub/wa',
                            1 => '/talk/hub/whatsapp/*',
                        ),
                ),
            23 =>
                array (
                    'center' => '创新产品研发中心',
                    'module' => '沟通-WhatsApp企业版（WABA）',
                    'manager' => 'bobo',
                    'path' =>
                        array (
                            0 => '/talk/hub/im',
                        ),
                ),
            24 =>
                array (
                    'center' => '创新产品研发中心',
                    'module' => '沟通-Facebook聊天',
                    'manager' => 'bobo',
                    'path' =>
                        array (
                            0 => '/talk/hub/fb',
                            1 => '/talk/message-tpl-create',
                        ),
                ),
            25 =>
                array (
                    'center' => '创新产品研发中心',
                    'module' => '沟通-INS',
                    'manager' => 'bobo',
                    'path' =>
                        array (
                            0 => '/talk/hub/ins',
                        ),
                ),
            26 =>
                array (
                    'center' => '创新产品研发中心',
                    'module' => '沟通-网站聊天',
                    'manager' => 'bobo',
                    'path' =>
                        array (
                            0 => '/talk/hub/live-chat',
                        ),
                ),
            27 =>
                array (
                    'center' => '创新产品研发中心',
                    'module' => '小满助手',
                    'manager' => 'bobo',
                    'path' =>
                        array (
                            0 => '/new_discovery/xiaoman-assistant-v2',
                            1 => '/new_discovery/xiaoman-assistant-v2/*',
                            2 => '/new_discovery/xiaoman-assistant',
                        ),
                ),
            28 =>
                array (
                    'center' => '创新产品研发中心',
                    'module' => 'linkin、FB',
                    'manager' => 'bobo',
                    'path' =>
                        array (
                            0 => '/new_discovery/okki-io-record',
                            1 => '/new_discovery/okki-message-template',
                        ),
                ),
            29 =>
                array (
                    'center' => '创新产品研发中心',
                    'module' => '智能营销-MA相关',
                    'manager' => 'bobo',
                    'path' =>
                        array (
                            0 => '/new_discovery/marketing-data-view',
                            1 => '/new_discovery/marketing-plan-list',
                        ),
                ),
            30 =>
                array (
                    'center' => '创新产品研发中心',
                    'module' => '营销和线索-邮件营销',
                    'manager' => 'bobo',
                    'path' =>
                        array (
                            0 => '/marketing',
                            1 => '/marketing/edm/*',
                        ),
                ),
            31 =>
                array (
                    'center' => '创新产品研发中心',
                    'module' => '邮件列表',
                    'manager' => 'source',
                    'path' =>
                        array (
                            0 => '/pro/mail/*',
                        ),
                ),
            32 =>
                array (
                    'center' => '创新产品研发中心',
                    'module' => '写信-写新邮件',
                    'manager' => 'source',
                    'path' =>
                        array (
                            0 => '/pro/mail/edit',
                        ),
                ),
            33 =>
                array (
                    'center' => '创新产品研发中心',
                    'module' => '设置',
                    'manager' => 'source',
                    'path' =>
                        array (
                            0 => '/pro/mail/config/*',
                        ),
                ),
            34 =>
                array (
                    'center' => '创新产品研发中心',
                    'module' => '下属邮件',
                    'manager' => 'source',
                    'path' =>
                        array (
                            0 => '/pro/mail/subordinate',
                        ),
                ),
            35 =>
                array (
                    'center' => '创新产品研发中心',
                    'module' => '客户邮件',
                    'manager' => 'source',
                    'path' =>
                        array (
                            0 => '/pro/mail/customer',
                        ),
                ),
            36 =>
                array (
                    'center' => '创新产品研发中心',
                    'module' => '企业管理-邮箱设置',
                    'manager' => 'source',
                    'path' =>
                        array (
                            0 => '/management/manage/email/*',
                        ),
                ),
            37 =>
                array (
                    'center' => '创新产品研发中心',
                    'module' => '邮件营销-发件箱',
                    'manager' => 'source',
                    'path' =>
                        array (
                            0 => '/marketing/edm/publish',
                        ),
                ),
            38 =>
                array (
                    'center' => '创新产品研发中心',
                    'module' => '邮件营销-自动化营销',
                    'manager' => 'source',
                    'path' =>
                        array (
                            0 => '/marketing/edm/automation',
                            1 => '/new_discovery/auto-marketing',
                        ),
                ),
            39 =>
                array (
                    'center' => '创新产品研发中心',
                    'module' => '邮件营销-追踪列表',
                    'manager' => 'source',
                    'path' =>
                        array (
                            0 => '/marketing/edm/trace',
                        ),
                ),
            40 =>
                array (
                    'center' => '创新产品研发中心',
                    'module' => '企业管理-营销设置',
                    'manager' => 'source',
                    'path' =>
                        array (
                            0 => '/management/manage/edm/allocate',
                            1 => '/email-bind-success',
                            2 => '/management/information/*',
                            3 => '/management/manage/edm/*',
                        ),
                ),
            41 =>
                array (
                    'center' => '大客户研发中心',
                    'module' => '客户公海-公海自动化规则',
                    'manager' => 'anhoderai',
                    'path' =>
                        array (
                            0 => '/crm/management/duplicate/*',
                        ),
                ),
            42 =>
                array (
                    'center' => '大客户研发中心',
                    'module' => '客户公海-公海池',
                    'manager' => 'anhoderai',
                    'path' =>
                        array (
                            0 => '/crm/customer/public',
                        ),
                ),
            43 =>
                array (
                    'center' => '大客户研发中心',
                    'module' => '建档建议',
                    'manager' => 'anhoderai',
                    'path' =>
                        array (
                            0 => '/customer/archive-advice',
                            1 => '/customer/recommend',
                        ),
                ),
            44 =>
                array (
                    'center' => '大客户研发中心',
                    'module' => '客户导入',
                    'manager' => 'anhoderai',
                    'path' =>
                        array (
                            0 => '/customer/importstatus',
                        ),
                ),
            45 =>
                array (
                    'center' => '大客户研发中心',
                    'module' => '客群管理-列表',
                    'manager' => 'anhoderai',
                    'path' =>
                        array (
                            0 => '/crm/customer/group',
                        ),
                ),
            46 =>
                array (
                    'center' => '大客户研发中心',
                    'module' => '客群管理-配置',
                    'manager' => 'anhoderai',
                    'path' =>
                        array (
                            0 => '/config',
                            1 => '/config/*',
                            2 => '/crm/customer/config/*',
                        ),
                ),
            47 =>
                array (
                    'center' => '大客户研发中心',
                    'module' => '线索-线索孵化',
                    'manager' => 'anhoderai',
                    'path' =>
                        array (
                            0 => '/crm/segmentation/leads',
                        ),
                ),
            48 =>
                array (
                    'center' => '大客户研发中心',
                    'module' => '外部对接-独立站',
                    'manager' => 'anhoderai',
                    'path' =>
                        array (
                            0 => '/crm/management/independent-station',
                        ),
                ),
            49 =>
                array (
                    'center' => '大客户研发中心',
                    'module' => '外部对接-钉钉',
                    'manager' => 'anhoderai',
                    'path' =>
                        array (
                            0 => '/talk/dingding/manage',
                        ),
                ),
            50 =>
                array (
                    'center' => '大客户研发中心',
                    'module' => '企业管理-自定义字段',
                    'manager' => 'anhoderai',
                    'path' =>
                        array (
                            0 => '/management/manage/fieldsConfig',
                        ),
                ),
            51 =>
                array (
                    'center' => '大客户研发中心',
                    'module' => '企业管理-字段显示',
                    'manager' => 'anhoderai',
                    'path' =>
                        array (
                            0 => '/crm/management/setting-leads/fields-switch',
                            1 => '/management/manage/fieldsswitc',
                        ),
                ),
            52 =>
                array (
                    'center' => '大客户研发中心',
                    'module' => '企业管理-线索设置',
                    'manager' => 'anhoderai',
                    'path' =>
                        array (
                            0 => '/crm/management/setting-leads/cause-of-invalidity',
                        ),
                ),
            53 =>
                array (
                    'center' => '大客户研发中心',
                    'module' => '企业管理-客户设置',
                    'manager' => 'anhoderai',
                    'path' =>
                        array (
                            0 => '/crm/management/customer/group',
                            1 => '/crm/management/customer/*',
                            2 => '/management/manage/customer',
                            3 => '/management/manage/customer/*',
                        ),
                ),
            54 =>
                array (
                    'center' => '大客户研发中心',
                    'module' => '企业管理-商机设置',
                    'manager' => 'anhoderai',
                    'path' =>
                        array (
                            0 => '/crm/management/opportunity/setting/sales',
                            1 => '/crm/management/opportunity/*',
                            2 => '/crm/management/opportunity',
                        ),
                ),
            55 =>
                array (
                    'center' => '大客户研发中心',
                    'module' => '企业管理-来源设置',
                    'manager' => 'anhoderai',
                    'path' =>
                        array (
                            0 => '/crm/management/origins',
                            1 => '/management/manage/origins',
                        ),
                ),
            56 =>
                array (
                    'center' => '大客户研发中心',
                    'module' => '跟单-跟单流程',
                    'manager' => 'skyhe',
                    'path' =>
                        array (
                            0 => '/oms/process',
                        ),
                ),
            57 =>
                array (
                    'center' => '大客户研发中心',
                    'module' => '跟单-跟单协同',
                    'manager' => 'skyhe',
                    'path' =>
                        array (
                            0 => '/omsTask',
                            1 => '/omsTask/*',
                        ),
                ),
            58 =>
                array (
                    'center' => '大客户研发中心',
                    'module' => '订单管理-销售订单',
                    'manager' => 'skyhe',
                    'path' =>
                        array (
                            0 => '/order',
                            1 => '/order/*',
                        ),
                ),
            59 =>
                array (
                    'center' => '大客户研发中心',
                    'module' => '采购管理-供应商',
                    'manager' => 'skyhe',
                    'path' =>
                        array (
                            0 => '/oms/supplier',
                            1 => '/oms/supplierproduct',
                        ),
                ),
            60 =>
                array (
                    'center' => '大客户研发中心',
                    'module' => '采购管理-以销定购',
                    'manager' => 'skyhe',
                    'path' =>
                        array (
                            0 => '/oms/orderBySales',
                        ),
                ),
            61 =>
                array (
                    'center' => '大客户研发中心',
                    'module' => '采购管理-采购订单',
                    'manager' => 'skyhe',
                    'path' =>
                        array (
                            0 => '/oms/purchaseOrde',
                            1 => '/oms/purchaseOrde/*',
                            2 => '/oms/purchaseorder',
                            3 => '/oms/purchaseorder/*',
                        ),
                ),
            62 =>
                array (
                    'center' => '大客户研发中心',
                    'module' => '采购管理-采购退货',
                    'manager' => 'skyhe',
                    'path' =>
                        array (
                            0 => '/oms/inventory/purchaseReturn',
                            1 => '/oms/inventory/purchaseReturn/*',
                        ),
                ),
            63 =>
                array (
                    'center' => '大客户研发中心',
                    'module' => '资金管理-回款登记',
                    'manager' => 'skyhe',
                    'path' =>
                        array (
                            0 => '/oms/cashCollectionRegistration',
                            1 => '/oms/cashCollectionRegistration/*',
                        ),
                ),
            64 =>
                array (
                    'center' => '大客户研发中心',
                    'module' => '资金管理-回款单',
                    'manager' => 'skyhe',
                    'path' =>
                        array (
                            0 => '/management/cash-collections/*',
                        ),
                ),
            65 =>
                array (
                    'center' => '大客户研发中心',
                    'module' => '资金管理-付款单',
                    'manager' => 'skyhe',
                    'path' =>
                        array (
                            0 => '/oms/purchasePayment',
                            1 => '/oms/purchasePayment/*',
                        ),
                ),
            66 =>
                array (
                    'center' => '大客户研发中心',
                    'module' => '资金管理-费用单',
                    'manager' => 'skyhe',
                    'path' =>
                        array (
                            0 => '/oms/costInvoice',
                            1 => '/oms/costInvoice/*',
                        ),
                ),
            67 =>
                array (
                    'center' => '大客户研发中心',
                    'module' => '资金管理-Alibaba.com.Pay',
                    'manager' => 'skyhe',
                    'path' =>
                        array (
                            0 => '/management/aliPayment',
                        ),
                ),
            68 =>
                array (
                    'center' => '大客户研发中心',
                    'module' => '企业管理-销售订单设置',
                    'manager' => 'skyhe',
                    'path' =>
                        array (
                            0 => '/management/manage/order/*',
                        ),
                ),
            69 =>
                array (
                    'center' => '大客户研发中心',
                    'module' => '企业管理-采购设置',
                    'manager' => 'skyhe',
                    'path' =>
                        array (
                            0 => '/management/manage/purchase/status',
                            1 => '/management/manage/purchase',
                            2 => '/management/manage/purchase/*',
                        ),
                ),
            70 =>
                array (
                    'center' => '大客户研发中心',
                    'module' => '企业管理-回款登记设置',
                    'manager' => 'skyhe',
                    'path' =>
                        array (
                            0 => '/management/manage/cashCollectionRegistration/*',
                        ),
                ),
            71 =>
                array (
                    'center' => '大客户研发中心',
                    'module' => '企业管理-回款单设置',
                    'manager' => 'skyhe',
                    'path' =>
                        array (
                            0 => '/management/manage/cash-collections',
                            1 => '/management/manage/cash-collections/*',
                        ),
                ),
            72 =>
                array (
                    'center' => '大客户研发中心',
                    'module' => '企业管理-资金项目',
                    'manager' => 'skyhe',
                    'path' =>
                        array (
                            0 => '/management/manage/capitalAccounts',
                        ),
                ),
            73 =>
                array (
                    'center' => '大客户研发中心',
                    'module' => '企业管理-供应商设置',
                    'manager' => 'skyhe',
                    'path' =>
                        array (
                            0 => '/management/manage/supplier/*',
                        ),
                ),
            74 =>
                array (
                    'center' => '大客户研发中心',
                    'module' => '企业管理-付款单设置',
                    'manager' => 'skyhe',
                    'path' =>
                        array (
                            0 => '/management/manage/paymentOrder/*',
                        ),
                ),
            75 =>
                array (
                    'center' => '大客户研发中心',
                    'module' => '企业管理-费用单设置',
                    'manager' => 'skyhe',
                    'path' =>
                        array (
                            0 => '/management/manage/costInvoice/*',
                        ),
                ),
            76 =>
                array (
                    'center' => '大客户研发中心',
                    'module' => '企业管理-费用设置',
                    'manager' => 'skyhe',
                    'path' =>
                        array (
                            0 => '/management/manage/fund',
                        ),
                ),
            77 =>
                array (
                    'center' => '大客户研发中心',
                    'module' => '产品管理-产品',
                    'manager' => 'tony',
                    'path' =>
                        array (
                            0 => '/product',
                            1 => '/product/*',
                        ),
                ),
            78 =>
                array (
                    'center' => '大客户研发中心',
                    'module' => '产品管理-阿里产品',
                    'manager' => 'tony',
                    'path' =>
                        array (
                            0 => '/platform-product',
                            1 => '/platform-product/*',
                        ),
                ),
            79 =>
                array (
                    'center' => '大客户研发中心',
                    'module' => '报价-报价单',
                    'manager' => 'tony',
                    'path' =>
                        array (
                            0 => '/quotation/*',
                            1 => '/oms/inquirytask',
                            2 => '/oms/inquirytask/*',
                        ),
                ),
            80 =>
                array (
                    'center' => '大客户研发中心',
                    'module' => '出口-货代',
                    'manager' => 'tony',
                    'path' =>
                        array (
                            0 => '/oms/forwarder',
                            1 => '/oms/forwarder/*',
                        ),
                ),
            81 =>
                array (
                    'center' => '大客户研发中心',
                    'module' => '出口-出运单',
                    'manager' => 'tony',
                    'path' =>
                        array (
                            0 => '/oms/shipment',
                            1 => '/oms/shipment/*',
                        ),
                ),
            82 =>
                array (
                    'center' => '大客户研发中心',
                    'module' => '出口-物流运输',
                    'manager' => 'tony',
                    'path' =>
                        array (
                            0 => '/logistics',
                            1 => '/logistics/*',
                            2 => '/customs-declaration/*',
                        ),
                ),
            83 =>
                array (
                    'center' => '大客户研发中心',
                    'module' => '订单管理-销售出库',
                    'manager' => 'tony',
                    'path' =>
                        array (
                            0 => '/oms/inventory/orderOutbound',
                            1 => '/oms/inventory/orderOutbound/*',
                        ),
                ),
            84 =>
                array (
                    'center' => '大客户研发中心',
                    'module' => '资金管理-订单毛利',
                    'manager' => 'tony',
                    'path' =>
                        array (
                            0 => '/oms/orderProfit',
                            1 => '/oms/orderProfit/*',
                        ),
                ),
            85 =>
                array (
                    'center' => '大客户研发中心',
                    'module' => '采购管理-采购入库',
                    'manager' => 'tony',
                    'path' =>
                        array (
                            0 => '/oms/inventory/purchaseInbound',
                            1 => '/oms/inventory/purchaseInbound/*',
                        ),
                ),
            86 =>
                array (
                    'center' => '大客户研发中心',
                    'module' => '库存管理-其他出库',
                    'manager' => 'tony',
                    'path' =>
                        array (
                            0 => '/oms/inventory/otherOutbound',
                            1 => '/oms/inventory/otherOutbound/*',
                        ),
                ),
            87 =>
                array (
                    'center' => '大客户研发中心',
                    'module' => '库存管理-其他入库',
                    'manager' => 'tony',
                    'path' =>
                        array (
                            0 => '/oms/inventory/otherInbound',
                            1 => '/oms/inventory/otherInbound/*',
                        ),
                ),
            88 =>
                array (
                    'center' => '大客户研发中心',
                    'module' => '库存管理-库存查询',
                    'manager' => 'tony',
                    'path' =>
                        array (
                            0 => '/oms/inventory/query',
                            1 => '/management/manage/transaction-parameter-settings/inventory-warning',
                            2 => '/oms/inventory/warning',
                        ),
                ),
            89 =>
                array (
                    'center' => '大客户研发中心',
                    'module' => '库存管理-库存流水',
                    'manager' => 'tony',
                    'path' =>
                        array (
                            0 => '/oms/inventory/flow',
                        ),
                ),
            90 =>
                array (
                    'center' => '大客户研发中心',
                    'module' => '企业管理-产品设置',
                    'manager' => 'tony',
                    'path' =>
                        array (
                            0 => '/management/manage/product',
                            1 => '/management/manage/product/*',
                        ),
                ),
            91 =>
                array (
                    'center' => '大客户研发中心',
                    'module' => '企业管理-报价单设置',
                    'manager' => 'tony',
                    'path' =>
                        array (
                            0 => '/management/manage/quotation',
                            1 => '/management/manage/quotation/*',
                        ),
                ),
            92 =>
                array (
                    'center' => '大客户研发中心',
                    'module' => '企业管理-订单毛利设置',
                    'manager' => 'tony',
                    'path' =>
                        array (
                            0 => '/management/manage/orderProfitFormula',
                            1 => '/management/manage/orderProfitFormula/*',
                        ),
                ),
            93 =>
                array (
                    'center' => '大客户研发中心',
                    'module' => '企业管理-单据模板',
                    'manager' => 'tony',
                    'path' =>
                        array (
                            0 => '/management/invoice-template/*',
                        ),
                ),
            94 =>
                array (
                    'center' => '大客户研发中心',
                    'module' => '企业管理-货代设置',
                    'manager' => 'tony',
                    'path' =>
                        array (
                            0 => '/management/manage/forwarder',
                        ),
                ),
            95 =>
                array (
                    'center' => '大客户研发中心',
                    'module' => '企业管理-出运单设置',
                    'manager' => 'tony',
                    'path' =>
                        array (
                            0 => '/management/manage/shipment',
                        ),
                ),
            96 =>
                array (
                    'center' => '大客户研发中心',
                    'module' => '企业管理-库存设置',
                    'manager' => 'tony',
                    'path' =>
                        array (
                            0 => '/management/manage/inventory/*',
                        ),
                ),
            97 =>
                array (
                    'center' => '大客户研发中心',
                    'module' => '企业管理-Paypal收款',
                    'manager' => 'tony',
                    'path' =>
                        array (
                            0 => '/management/manage/payment',
                        ),
                ),
            98 =>
                array (
                    'center' => '大客户研发中心',
                    'module' => '企业管理-汇率设置',
                    'manager' => 'tony',
                    'path' =>
                        array (
                            0 => '/management/manage/exchange',
                        ),
                ),
            99 =>
                array (
                    'center' => '大客户研发中心',
                    'module' => 'ERP对接',
                    'manager' => 'tony',
                    'path' =>
                        array (
                            0 => '/management/erp/manage',
                        ),
                ),
            100 =>
                array (
                    'center' => '大客户研发中心',
                    'module' => 'API对接',
                    'manager' => 'tony',
                    'path' =>
                        array (
                            0 => '/management/erp',
                        ),
                ),
            101 =>
                array (
                    'center' => '大客户研发中心',
                    'module' => '企业管理-功能开关',
                    'manager' => 'tony',
                    'path' =>
                        array (
                            0 => '/management/manage/modules',
                        ),
                ),
            102 =>
                array (
                    'center' => '大客户研发中心',
                    'module' => '企业管理-角色权限',
                    'manager' => 'tony',
                    'path' =>
                        array (
                            0 => '/management/manage/roles',
                            1 => '/management/manage/roles/*',
                        ),
                ),
            103 =>
                array (
                    'center' => '大客户研发中心',
                    'module' => '企业管理-翻译设置',
                    'manager' => 'tony',
                    'path' =>
                        array (
                            0 => '/crm/management/translate-setting',
                            1 => '/crm/management/translate-setting/*',
                        ),
                ),
            104 =>
                array (
                    'center' => '大客户研发中心',
                    'module' => '企业管理-回收箱',
                    'manager' => 'tony',
                    'path' =>
                        array (
                            0 => '/management/recycle/*',
                        ),
                ),
            105 =>
                array (
                    'center' => '大客户研发中心',
                    'module' => '-数据分析',
                    'manager' => 'tony',
                    'path' =>
                        array (
                            0 => '/statistic/biz/panel',
                            1 => '/statistic/*',
                        ),
                ),
            106 =>
                array (
                    'center' => '大客户研发中心',
                    'module' => '企业管理-工作流管理',
                    'manager' => 'nuxse',
                    'path' =>
                        array (
                            0 => '/management/manage/workflow-edit',
                            1 => '/management/manage/workflow',
                        ),
                ),
            107 =>
                array (
                    'center' => '大客户研发中心',
                    'module' => '企业管理-审批流管理',
                    'manager' => 'nuxse',
                    'path' =>
                        array (
                            0 => '/management/manage/approval-flow',
                            1 => '/management/manage/approval-flow*',
                            2 => '/management/manage/approval-flow-detail',
                            3 => '/management/manage/approval-edit',
                        ),
                ),
            108 =>
                array (
                    'center' => '大客户研发中心',
                    'module' => '企业管理-自定义报表',
                    'manager' => 'nuxse',
                    'path' =>
                        array (
                            0 => '/statistic/setting/custom-report/*',
                        ),
                ),
            109 =>
                array (
                    'center' => '大客户研发中心',
                    'module' => '协同-审批',
                    'manager' => 'nuxse',
                    'path' =>
                        array (
                            0 => '/review',
                            1 => '/review/*',
                        ),
                ),
            110 =>
                array (
                    'center' => '智能CRM研发中心',
                    'module' => '私海客户',
                    'manager' => 'wuyiwai',
                    'path' =>
                        array (
                            0 => '/crm/customer/list',
                            1 => '/customer/personal/*',
                            2 => '/customer/list',
                            3 => '/crm/customer',
                            4 => '/crm/customer/*',
                        ),
                ),
            111 =>
                array (
                    'center' => '智能CRM研发中心',
                    'module' => '客户列表',
                    'manager' => 'wuyiwai',
                    'path' =>
                        array (
                            0 => '/customer/personal',
                            1 => '/crm/customer/personal',
                        ),
                ),
            112 =>
                array (
                    'center' => '智能CRM研发中心',
                    'module' => '公海客户',
                    'manager' => 'wuyiwai',
                    'path' =>
                        array (
                            0 => '/crm/customer/public',
                            1 => '/customer/public',
                            2 => '/customer/public/*',
                            3 => '/crm/customer/public/*',
                        ),
                ),
            113 =>
                array (
                    'center' => '智能CRM研发中心',
                    'module' => '客户查重',
                    'manager' => 'wuyiwai',
                    'path' =>
                        array (
                            0 => '/crm/customer/query',
                            1 => '/customer/query',
                        ),
                ),
            114 =>
                array (
                    'center' => '智能CRM研发中心',
                    'module' => '团队目标-目标完成情况',
                    'manager' => 'wuyiwai',
                    'path' =>
                        array (
                            0 => '/goal/goalCompletion/statistic',
                        ),
                ),
            115 =>
                array (
                    'center' => '智能CRM研发中心',
                    'module' => '团队目标-目标管理',
                    'manager' => 'wuyiwai',
                    'path' =>
                        array (
                            0 => '/goal/goalManage/*',
                            1 => '/management/pdca/rule/*',
                            2 => '/management/pdca/target-split',
                        ),
                ),
            116 =>
                array (
                    'center' => '智能CRM研发中心',
                    'module' => '团队目标-团队墙',
                    'manager' => 'wuyiwai',
                    'path' =>
                        array (
                            0 => '/management/pdca/team-wall',
                        ),
                ),
            117 =>
                array (
                    'center' => '智能CRM研发中心',
                    'module' => 'AI自动化',
                    'manager' => 'wuyiwai',
                    'path' =>
                        array (
                            0 => '/ai/automation',
                            1 => '/ai/automation/*',
                            2 => '/ai/dashboard',
                        ),
                ),
            118 =>
                array (
                    'center' => '智能CRM研发中心',
                    'module' => '阿里相关',
                    'manager' => 'nicolasyan',
                    'path' =>
                        array (
                            0 => '/management/alibaba/store',
                            1 => '/alibaba-bind-success',
                            2 => '/alibaba-im',
                            3 => '/alibaba-view-fail',
                            4 => '/alibaba-view-order/*',
                            5 => '/alibaba-view-logistics',
                            6 => '/management/alibaba/*',
                            7 => '/management/alibaba-auth-result',
                            8 => '/alibaba-view-logistics/logistics_detail_page',
                            9 => '/alibaba-sales-race',
                        ),
                ),
            119 =>
                array (
                    'center' => '智能CRM研发中心',
                    'module' => '谈单指南',
                    'manager' => 'nicolasyan',
                    'path' =>
                        array (
                            0 => '/tms/orderGuideManage',
                            1 => '/template-export',
                            2 => '/tms/foreigntrade/*',
                            3 => '/tms/uploadverbaltrick',
                        ),
                ),
            120 =>
                array (
                    'center' => '智能CRM研发中心',
                    'module' => '',
                    'manager' => 'gavingao',
                    'path' =>
                        array (
                            0 => '/dashboard/*',
                        ),
                ),
            121 =>
                array (
                    'center' => '智能CRM研发中心',
                    'module' => '工作报告模板',
                    'manager' => 'gavingao',
                    'path' =>
                        array (
                            0 => '/goal/workReport/template',
                        ),
                ),
            122 =>
                array (
                    'center' => '智能CRM研发中心',
                    'module' => '工作报告',
                    'manager' => 'gavingao',
                    'path' =>
                        array (
                            0 => '/goal/workReport',
                            1 => '/work-report/*',
                        ),
                ),
            123 =>
                array (
                    'center' => '智能CRM研发中心',
                    'module' => '商机',
                    'manager' => 'gavingao',
                    'path' =>
                        array (
                            0 => '/crm/business/list',
                            1 => '/crm/business/*',
                            2 => '/business/*',
                            3 => '/management/manage/opportunity',
                            4 => '/crm/management/opportunity/*',
                        ),
                ),
            124 =>
                array (
                    'center' => '智能CRM研发中心',
                    'module' => '线索列表',
                    'manager' => 'gavingao',
                    'path' =>
                        array (
                            0 => '/crm/leads/list',
                            1 => '/leads/detail',
                            2 => '/crm/leads/record',
                            3 => '/crm/management/convert',
                            4 => '/leads/list',
                            5 => '/leads/reports',
                        ),
                ),
            125 =>
                array (
                    'center' => '智能CRM研发中心',
                    'module' => '公海线索',
                    'manager' => 'gavingao',
                    'path' =>
                        array (
                            0 => '/leads/public',
                        ),
                ),
            126 =>
                array (
                    'center' => '智能CRM研发中心',
                    'module' => 'AI客户管家-AI批量质检',
                    'manager' => 'gavingao',
                    'path' =>
                        array (
                            0 => '/goal/ai-chat-quality/*',
                        ),
                ),
            127 =>
                array (
                    'center' => '智能CRM研发中心',
                    'module' => '管理-下属单页',
                    'manager' => 'hackicewang',
                    'path' =>
                        array (
                            0 => '/management/sales/*',
                            1 => '/management/subordinate/personal',
                        ),
                ),
            128 =>
                array (
                    'center' => '智能CRM研发中心',
                    'module' => '展会数据-日程',
                    'manager' => 'hackicewang',
                    'path' =>
                        array (
                            0 => '/schedule/list',
                            1 => '/schedule',
                        ),
                ),
            129 =>
                array (
                    'center' => '智能CRM研发中心',
                    'module' => '协同-通讯录',
                    'manager' => 'hackicewang',
                    'path' =>
                        array (
                            0 => '/contacts/list',
                        ),
                ),
            130 =>
                array (
                    'center' => '智能CRM研发中心',
                    'module' => '协同-任务',
                    'manager' => 'hackicewang',
                    'path' =>
                        array (
                            0 => '/task',
                        ),
                ),
            131 =>
                array (
                    'center' => '智能CRM研发中心',
                    'module' => '协同-云盘',
                    'manager' => 'hackicewang',
                    'path' =>
                        array (
                            0 => '/disk',
                        ),
                ),
            132 =>
                array (
                    'center' => '智能CRM研发中心',
                    'module' => '消息中心-消息通知设置页面',
                    'manager' => 'hackicewang',
                    'path' =>
                        array (
                            0 => '/account/settings/desktop-notice',
                            1 => '/notice-new',
                        ),
                ),
            133 =>
                array (
                    'center' => '智能CRM研发中心',
                    'module' => '消息中心-消息列表',
                    'manager' => 'hackicewang',
                    'path' =>
                        array (
                            0 => '/notice-new/notice-new-list',
                        ),
                ),
            134 =>
                array (
                    'center' => '智能CRM研发中心',
                    'module' => '新手教程-新手引导',
                    'manager' => 'hackicewang',
                    'path' =>
                        array (
                            0 => '/novice/guide',
                            1 => '/course/*',
                        ),
                ),
            135 =>
                array (
                    'center' => '智能CRM研发中心',
                    'module' => '企业中心',
                    'manager' => 'hackicewang',
                    'path' =>
                        array (
                            0 => '/management/account/*',
                        ),
                ),
            136 =>
                array (
                    'center' => '智能CRM研发中心',
                    'module' => '个人设置',
                    'manager' => 'hackicewang',
                    'path' =>
                        array (
                            0 => '/account/information',
                            1 => '/account/*',
                            2 => '/account',
                            3 => '/user-setting',
                            4 => '/user-setting/*',
                        ),
                ),
            137 =>
                array (
                    'center' => '智能CRM研发中心',
                    'module' => '安全中心',
                    'manager' => 'hackicewang',
                    'path' =>
                        array (
                            0 => '/security',
                            1 => '/security/*',
                        ),
                ),
            138 =>
                array (
                    'center' => '智能CRM研发中心',
                    'module' => '企业管理-任务设置',
                    'manager' => 'hackicewang',
                    'path' =>
                        array (
                            0 => '/management/task/*',
                        ),
                ),
        ),
    'sls_log_active_api_rule'=>[
        'host_mesh-ingress-log'=>[
            'skip_record_rule'=>[
                'app_rules' => [
                    ['app' => 'login-fpm-default', 'pattern' => 'image', 'desc' => '登录服务的图片不统计'],
                    ['app' => 'canary', 'pattern' => '', 'desc' => '不包含canary']
                ],
                'static_extensions' => [
                    'css', 'js', 'txt', 'yml', 'jpg', 'jpeg', 'png', 'gif', 'ico', 'svg', 'webp', 'bmp'
                ]
            ],
            'url_rules' =>  [
                'app_direct_pass' => ['os-fpm', 'open-api','chat-api','ai-connector-v2'],
                'disable_hosts' => ['m.tips'],
                'prefix_paths' => [
                    '/api/', '/ames/', '/app/', '/assistant/', '/external/',
                    '/lighthouse/', '/marketing/', '/prometheus/', '/resource/',
                    '/shops/', '/tapdplugin/', '/ticketassistant/', '/shop-api/',
                    '/internal/', '/interface/', '/ai/', '/chat/'
                ],
                'app_prefix_paths' => [
                    'login-fpm-default' => ['/coderead/', '/page/', '/read/', '/wechat/', '/write/'],
                    'callback-fpm' => ['/track/'],
                    'enterprise-center-fpm' => ['/account/']
                ],
                'app_exact_paths' => [
                    'enterprise-center-fpm' => ['user-read/info', 'site/white-list', 'application-read/list', 'account/write/pre-join', 'account/write/save-base-info', 'uploader/read/callback', 'uploader/read/get-policy', 'account/read/check-wechat-status', 'account/read/get-password-token', 'account/app-read/get-verify-code', 'account/app-read/login', 'account/write/send-retrieve-password-email', 'account/read/send-reset-password-code', 'account/read/get-reset-email-by-mobile', 'account/write/reset-password-by-token', 'enterprise-read/info']
                ],
                'app_disable_paths' => [
                    'os-fpm' => ['/ping'],
                    'enterprise-center-fpm' => ['/api/accountwrite/', '/api/accountwrite'],
                    'callback-fpm' => ['/interface/mailbouncednotify', '/track/edmdelivery', '/track/mail', '/track/redirects', '/track/edmopen', '/track/visit', '/track/edm', '/track/groupmail', 'interface/receivemail', '/interface/receivemail', '/interface/exposefailsubmailcallback', '/interface/sendmail', '/interface/exposecallback']
                ]
            ],
        ],
        'host_nginx-ingress' => [
            'skip_record_rule'=>[
                'app_rules' => [
                    ['app' => 'canary', 'pattern' => '', 'desc' => '不包含canary']
                ],
                'static_extensions' => ['css', 'js', 'txt', 'yml', 'jpg', 'jpeg', 'png', 'gif', 'ico', 'svg', 'webp', 'bmp'],
                'host_rules' => [
                    ['pattern' => '/\d/', 'desc' => '过滤host包含数字的']
                ],
                'url_rules' => [
                    ['pattern' => '/\./','desc' => '过滤url包含 `.` 的']
                ]
            ],
            'url_rules' => [
                'app_direct_pass' => ['google-server', 'publish-server','os-fpm-pre','edm-fpm-default','okki-io',
                    'crm-email-maker','html-screenshot','personal-account-system','qc-server','doc-parser'],
                'app_direct_not_pass' => ['tms-nginx-lua', 'ks-console','ks-apiserver','waha-node-proxy','crm-email-maker','mail-forward-server','background-check'],
                'disable_hosts' => ['m.tips','sentry.xiaoman.cn'],
                'prefix_paths' => [
                    '/api/', '/ames/', '/app/', '/assistant/', '/external/',
                    '/lighthouse/', '/marketing/', '/prometheus/', '/resource/',
                    '/shops/', '/tapdplugin/', '/ticketassistant/', '/shop-api/',
                    '/internal/', '/interface/', '/ai/', '/chat/'
                ],
                'app_prefix_paths' => [],
                'app_exact_paths' => [],
                'app_disable_paths' => []
            ],
        ],
        'k8s-us_nginx-ingress' => [
            'skip_record_rule'=>[
                'app_rules' => [
                    ['app' => 'canary', 'pattern' => '', 'desc' => '不包含canary']
                ],
                'static_extensions' => ['css', 'js', 'txt', 'yml', 'jpg', 'jpeg', 'png', 'gif', 'ico', 'svg', 'webp', 'bmp'],
                'host_rules' => [
                    ['pattern' => '/\d/', 'desc' => '过滤host包含数字的']
                ],
                'url_rules' => [
                    ['pattern' => '/\./','desc' => '过滤url包含 `.` 的']
                ]
            ],
            'url_rules' => [
                'app_direct_pass' => ['gateway-swoole','shops-hyperf-translate'],
                'app_direct_not_pass' => [],
                'disable_hosts' => ['m.tips'],
                'prefix_paths' => [],
                'app_prefix_paths' => [],
                'app_exact_paths' => [],
                'app_disable_paths' => []
            ],
        ],
    ],
    'selector_config' => '{"messenger":{"MESSENGER_LIST_CONTAINER":"div[id*=\\"mount_0_0_\\"]","MESSENGER_SENDER_NAME":"[data-pagelet=\\"MWCMProfilePhotoMenu\\"] svg.x3ajldb, [aria-label=\\"Settings, help and more\\"] svg.x3ajldb","SEARCH_INPUT_CLASS":"[placeholder=\\"搜索 Messenger\\"], [placeholder=\\"Search Messenger\\"]","RESET_BTN_CLASS":"[aria-label=\\"退出联想输入\\"], [aria-label=\\"Exit typeahead\\"]","CONTENT_INPUT_CLASS":"[aria-label=\\"发消息\\"][contenteditable=\\"true\\"], [aria-label=\\"Message\\"][contenteditable=\\"true\\"]","SEND_MESSAGE_BTN_CLASS":"[aria-label=\\"按 Enter 键发送\\"], [aria-label=\\"Press enter to send\\"]","GO_TO_INBOX_BTN_CLASS":"[aria-label=\\"前往收件箱\\"]"},"ins":{"INS_SENDER_NAME":"img[alt*=\\"的头像\\"], img[alt*=\\"\'s profile picture\\"]","INS_OPEN_NEW_MESSAGE_BTN_CLASS":"[aria-label=\\"对话列表\\"] [aria-label=\\"新消息\\"], [aria-label=\\"Thread list\\"] [aria-label=\\"New message\\"]","INS_CLOSE_NEW_MESSAGE_BTN_CLASS":"[aria-label=\\"关闭\\"], [aria-label=\\"Close\\"]","INS_SEARCH_INPUT_CLASS":"[role=\\"dialog\\"] [placeholder=\\"搜索…\\"], [role=\\"dialog\\"] [placeholder=\\"Search...\\"]","INS_CONTACT_ITEM_CLASS":".xzkaem6 [role=\\"dialog\\"] [role=\\"button\\"].xh8yej3","INS_CHAT_BTN_CLASS":".xzkaem6 [role=\\"dialog\\"] .xktsk01 [role=\\"button\\"]","INS_CONTENT_INPUT_CLASS":"[aria-label=\\"发消息\\"][contenteditable=\\"true\\"], [aria-label=\\"Message\\"][contenteditable=\\"true\\"]","INS_SEND_MESSAGE_BTN_CLASS":"[aria-label*=\\"的对话\\"] [role=\\"button\\"].xqeqjp1, [aria-label*=\\"Conversation with\\"] [role=\\"button\\"].xqeqjp1","INS_INVITE_SENT_CLASS":"[role=\\"alert\\"] .xu06os2.x1ok221b","INS_CLOSE_NOTIFICATIONS_CLASS":"[role=\\"dialog\\"] button"}}',
    'error_detail_url' =>
        array(
            'host' => 'https://master.dev.xiaoman.cn/prometheus/#/prometheus/errorLog/index',
        ),
    'error_nologin_detail_url' =>
        array(
            'host' => 'https://master.dev.xiaoman.cn/prometheus/errorLog/#/prometheus/errorLog/index',
        ),
    'super_login_url' =>
        array (
            'host' => 'https://login-service.login-api.dev.xiaoman.cn/read/superLogin',
        ),
    'dc_config' =>
        array (
            'extra_info' =>
                array (
                    'dc_id' => 1,
                )
        ),
    'crawler_config' =>
        array (
            'host' =>'https://discovery-k8s-test.jentian.com',
            'token'=>'477a19bad88e644d1f181086c06c4729760640754c6d422b697c5e2baaf27c26'
      ),
    'search_engine_config'=>
        array (
            'v2_host' => 'https://discovery-k8s-v2-test.jentian.com',
            'v2_token' => '477a19bad88e644d1f181086c06c4729760640754c6d422b697c5e2baaf27c26',
            'v1_host' => 'https://discovery-k8s-test.jentian.com',
            'v1_token' => '477a19bad88e644d1f181086c06c4729760640754c6d422b697c5e2baaf27c26'
    ),
    'webhook_api' => array (
        'host' => 'https://chat-webhook.dev.xiaoman.cn'
    ),
	'cms_client_ids' =>
		array (
			14598
		),
);
