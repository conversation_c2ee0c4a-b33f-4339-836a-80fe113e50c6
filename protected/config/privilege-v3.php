<?php
/**
 * Created by PhpStorm.
 * User: troy.li
 * Date: 2018-10-19
 * Time: 2:01 PM
 */

/**
 * controller 和 action 请全小写
 * 只对action进行鉴权 （例如 /customer/delete）
 *     'customer' => array(
 *      'functional’ => 'privilege',
 *      'require' =>'privilege',
 *      'except' =>'action',
 *      'action' => array(
 *          'require' => 'privilege'
 *      )
 *   )
 */

use common\library\privilege_v3\PrivilegeConstants;

return [
    'default' => [
        'contactsread'=>[
            'require'=>'crm.setting.contacts.manage'
        ],
        'contactswrite'=>[
            'require'=>'crm.setting.contacts.manage'
        ],

        'dashboardread' => [
            'functional' => 'crm.functional.base',
        ],

        'edmwrite' => [
            'action' => [
                'exportedmmaillist' => ['require' => 'crm.edm.contacts.export'],
                'add' => ['require'=>'crm.setting.edm'],
                'del' => ['require'=>'crm.setting.edm'],
                'send' =>[
                    'trial' => 1,
                ],
            ],
        ],

        'pageread' => [
            'action' => [
                'user' => [
//                    'functional' => 'crm.functional.base',
                ],
            ],
        ],
        'managewrite'=>[
            'action' => [
                'bindinguser' => [
                    'require' => 'crm.setting.account.relation'
                ],
                'unbindinguser' => [
                    'require' => 'crm.setting.account.relation'
                ],
                'joinclient' =>[
                    'require'=>'crm.setting.user.manage',
                ],
                'setadmin' =>[
                    'require'=>'crm.setting.user.manage',
                ],
                'freezeuser' =>[
                    'require'=>'crm.setting.user.manage',
                ],
                'freezedeleteuser' => [
                    'require'=>'crm.setting.user.manage',
                ],
                'cancelinvite' =>[
                    'require'=>'crm.setting.user.manage',
                ],
            ]
        ],

        'manageread' =>[
            'action' =>[
                'clientrelation' => [
                    'require' => 'crm.setting.account.relation'
                ],
                'homeLessuserlistfornotinclient' => [
                    'require' => 'crm.setting.user.manage',
                ],
                'organizationtreefornotinclient' => [
                    'require' => 'crm.setting.user.manage',
                ],
            ]
        ],

        // 线索查看
        'leadread' => [
            'functional' => [
                'crm.functional.lead'
            ],
            'require' => 'crm.lead.private.view',
            'action' => [
                //报表
                'export' => [
                    'require' => 'crm.lead.private.report'
                ],
                'leadlist' => [
                    'trial' => 1,
                ],
            ],
            'except' => [
                'leadlist',
            ],
        ],

        // 线索管理
        'leadwrite' => [
            'functional' => [
                'crm.functional.lead'
            ],
            'require' => 'crm.lead.private.view',
            'action' => [
                // 转移线索
//                'transfer' => [
//                    'require' => 'crm.lead.private.transfer'
//                ],
                'release' => [
                    'require' => 'crm.lead.move.pool'
                ],
                'changestatus' =>[
                    'functional' => 'crm.functional.lead.status',
                ],
                'faillead' =>[
                    'functional' => 'crm.functional.lead.status',
                ],
                'merge' => [
                    'require' => 'crm.lead.private.merge'
                ],
            ],
            'except' => [
                'savelead',
            ],
        ],

        // 阿里询盘，店铺管理
        'alibabawrite' => [
            'functional' => [
                'crm.functional.alibaba.trade'
            ],
            'action' => [
                'savestoremember' => [
                    'require' => 'crm.setting.alibaba.store.manage'
                ],
                'cancelstoreauth' =>[
                    'require' => 'crm.setting.alibaba.store.manage',
                ],
                'syncalibabacustomer' =>[
                    'require' => 'crm.setting.alibaba.store.manage',
                ],
                'deletestore' => [
                    'require' => 'crm.setting.alibaba.store.manage'
                ],
            ],
        ],

        'customerread'=>[
            'action' => [
                'export' => [
                    'require' => 'crm.setting.customer.export'
                ],
                'memberlist' =>[
                    'require' => 'crm.setting.customer.manage',
                ],
                'blacklist' =>[
                    'require' => 'crm.setting.customer.manage',
                ],
//                'searchlist' =>[
//                    'require' => 'crm.setting.customer.manage',
//                ],
            ],
        ],


        //移入公海规则设置
        'customersettingwrite' =>[
            'action' =>[
                'groupmovetopublicsetting' =>[
                    'functional' => 'crm.functional.company.pool.auto.rules',
                ],
                'createstatus' => [
                    'require' => 'crm.setting.customer.manage'
                ],
                'deletestatus' =>[
                    'require' => 'crm.setting.product.manage',
                ],
                'addstatus' =>[
                    'require' => 'crm.setting.product.manage',
                ],
                'setreference' => [
                    'require' => 'crm.setting.customer.manage'
                ],
                'transfer' => [
                    'require' => 'crm.setting.customer.client.tag.manage'
                ],
                'publicruleswitch' => [
                    'require' => 'crm.setting.customer.manage'
                ],
                'publicrulecreate' => [
                    'require' => 'crm.setting.customer.manage'
                ],
                'publicruleedit' => [
                    'require' => 'crm.setting.customer.manage'
                ],
                'publicruledelete' => [
                    'require' => 'crm.setting.customer.manage'
                ],
            ]
        ],

        // 公海分组设置
        'customerpoolwrite' => [
            'functional' => 'crm.functional.company.pool.setting',
        ],
        // 公海分组查看
        'customerpoolread' => [
            'functional' => 'crm.functional.company.pool.setting',
        ],
        //产品
        'productread' =>[
            'action' =>[
                'list' =>[
                    'require' => 'crm.product.view',
                ],
                'info' =>[
                    'require' => 'crm.product.view',
                ],
                'pdf' =>[
                    'require' => 'crm.setting.product.manage',
                ],
                'exportByFilter' => [
                    'require' => 'crm.product.export',
                ]
            ]
        ],

        //产品
        'productwrite' =>[
            'action' =>[
                'create' =>[
                    'require' => 'crm.product.create',
                ],
                'edit' =>[
                    'require' => 'crm.product.edit',
                ],
                'delete' =>[
                    'require' => 'crm.product.delete',
                ],
                'moveproductgroup' =>[
                    'require' => 'crm.product.edit',
                ],
                'importproduct' =>[
                    'require' => 'crm.product.create',
                ],
                'importbyexcel' =>[
                    'require' => 'crm.product.create',
                ],
                'importglobalsourcesproduct' =>[
                    'require' => 'crm.product.create',
                ],
            ]
        ],

        //产品分组设置
        'productsettingwrite' =>[
            'action' =>[
                'groupsave' =>[
                    'require' => 'crm.setting.product.manage',
                ],
                'groupbatchdelete' =>[
                    'require' => 'crm.setting.product.manage',
                ],
                'setexceltemplate' =>[
                    'require' => 'crm.setting.quotation.template',
                ],
                'delexceltemplate' =>[
                    'require' => 'crm.setting.quotation.template',
                ],
            ]
        ],

        'customerwrite'=>[
            'action' => [
                'editgroup' => [
                    'require' => 'crm.setting.customer.manage'
                ],
                'setcustomerlimit' => [
                    'require' => 'crm.setting.customer.manage'
                ],
                'renameorigin' => [
                    'require' => 'crm.setting.customer.manage'
                ],
                'addorigin' => [
                    'require' => 'crm.setting.customer.manage'
                ],
                'deleteorigin' => [
                    'require' => 'crm.setting.customer.manage'
                ],

                'alterstatus' => [
                    'require' => 'crm.setting.customer.manage'
                ],
                'deletestatus' => [
                    'require' => 'crm.setting.customer.manage'
                ],
                'addblacklist' => [
                    'require' => 'crm.setting.customer.manage'
                ],
                'removeblacklist' => [
                    'require' => 'crm.setting.customer.manage'
                ],
                'remark' => [
                    'require' => 'crm.company.private.view'
                ],
                'mergecompany' => [
                    'require' => 'crm.company.private.merge'
                ],
//                'star' => [
//                    'require' => 'crm.company.private.edit'
//                ],
//                'savecustomer' => [
//                    'require' => 'crm.company.private.edit'
//                ],
//                'status' => [
//                    'require' => 'crm.company.private.edit'
//                ],
                'addtag' => [
                    'require' => 'crm.company.private.view'
                ],
//                'setgroup' => [
//                    'require' => 'crm.company.private.edit'
//                ],
//                'mergecompany' => [
//                    'require' => 'crm.company.private.edit'
//                ],
                'movetopublic' => [
                    'require' => 'crm.company.private.move.pool'
                ],
                'remove' => [
                    'require' => 'crm.company.private.release'
                ],
                'share' => [
                    'require' => 'crm.company.private.share'
                ],
                'hold' => [
                    'require' => 'crm.company.pool.hold'
                ],
//                'transfer' => [
//                    'require' => 'crm.company.private.transfer'
//                ],
//                'transferall' => [
//                    'require' => 'crm.company.private.assign'
//                ],
//                'import' => [
//                    'require' => 'crm.company.private.create'
//                ],
                'specifyuserremove' => [
                    'require' => 'crm.company.private.release.specify.user'
                ],

            ],
        ],
        //报价单
        'quotationread' =>[
            'functional' => [
                'crm.functional.quotation'
            ],
            'action' =>[
                'list' =>[
                    'require' => 'crm.quotation.view',
                    'params' => [
                        'scene' => 'quotation'
                    ]
                ],
                'filelist' =>[
                    'require' => 'crm.quotation.view',
                ],
                'exportfilelist' =>[
                    'require' => 'crm.quotation.view',
                ],
                'exportpdf' =>[
                    'require' => 'crm.quotation.view',
                ],
                'exportexcel' =>[
                    'require' => 'crm.quotation.view',
                ],
                'historylist' =>[
                    'require' => 'crm.quotation.view',
                ],
                'preview' =>[
                    'require' => 'crm.quotation.view',
                ],
            ]

        ],
        'quotationwrite' =>[
            'functional' => [
                'crm.functional.quotation'
            ],
            'action' =>[
                'addfile' =>[
                    'require' => 'crm.quotation.view',
                ],
                'del' =>[
                    'require' => 'crm.quotation.remove',
                ],
                'create' =>[
                    'require' => 'crm.quotation.create',
                ],
                'setstatus' =>[
                    'require' => 'crm.quotation.view',
                ],
                'setremark' =>[
                    'require' => 'crm.quotation.view',
                ],
            ]

        ],
        //订单
        'orderread' =>[
            'functional' => [
                'crm.functional.order'
            ],
            'action' =>[
//                'list' =>[
//                    'require' => 'crm.order.view',
//                ],
                'filelist' =>[
                    'require' => 'crm.order.view',
                ],
                'exportfilelist' =>[
                    'require' => 'crm.order.view',
                ],
                'exportpdf' =>[
                    'require' => 'crm.order.export',
                ],
                'exportexcel' =>[
                    'require' => 'crm.order.export',
                ],
                'historylist' =>[
                    'require' => 'crm.order.view',
                ],
                'preview' =>[
                    'require' => 'crm.order.export',
                ],
                'export' =>[
                    'require' => 'crm.setting.order.export',
                ],
            ]

        ],
        'orderwrite' =>[
            'functional' => [
                'crm.functional.order'
            ],
            'action' =>[
                'create' =>[
                    'require' => 'crm.order.create',
                ],
                'del' =>[
                    'require' => 'crm.order.remove',
                ],
                'setstatus' =>[
                    'require' => 'crm.order.view',
                ],
                'setremark' =>[
                    'require' => 'crm.order.view',
                ],
                //todo boqiang
            ]

        ],

        //报关
        'yhxcustomsread' =>[
            'functional' => [
                'crm.functional.customs'
            ],
            'action' =>[
                'checkuserauth' =>[
                    'require' => 'crm.customs.view',
                ],
                'customslist' =>[
                    'require' => 'crm.customs.view',
                ],
                'customspdfinfo' =>[
                    'require' => 'crm.customs.view',
                ],
                'importtasklist' =>[
                    'require' => 'crm.customs.view',
                ],
            ]

        ],
        'yhxcustomswrite' =>[
            'functional' => [
                'crm.functional.customs'
            ],
            'action' =>[
                'synccustoms' =>[
                    'require' => 'crm.customs.view',
                ],
                'activateuserauth' =>[
                    'require' => 'crm.setting.customs.manage',
                ],
                'unbinduserauth' =>[
                    'require' => 'crm.setting.customs.manage',
                ],
            ]

        ],
        // 商机查看
        'opportunityread' => [
            'functional' => 'crm.functional.opportunity',
            'require' => 'crm.opportunity.view',
            'except' => ['recycleinfo', 'list']
        ],
        // 商机操作
        'opportunitywrite' => [
            'functional' => 'crm.functional.opportunity',
            'require' => 'crm.opportunity.view',
            'action' => [
                'create' => [
                    'require' => 'crm.opportunity.create'
                ],
                'edit' => [
                    //'require' => 'crm.opportunity.edit'
                ],
                'delete' => [
                    'require' => 'crm.opportunity.remove'
                ],
                'changestage' => [
                    //'require' => 'crm.opportunity.edit'
                ],
                'succeed' => [
                    //'require' => 'crm.opportunity.edit'
                ],
                'fail' => [
                    //'require' => 'crm.opportunity.edit'
                ],
                'addfile' => [
                    //'require' => 'crm.opportunity.edit'
                ],
                'removefile' => [
                    //'require' => 'crm.opportunity.edit'
                ],
                'enable' => [
                    //'require' => 'crm.opportunity.edit'
                ],
                'disable' => [
                    //'require' => 'crm.opportunity.edit'
                ],
                'removetrail' => [
                    'require' => 'crm.opportunity.trail.create'
                ],
                'remark' => [
                    'require' => 'crm.opportunity.trail.create'
                ],
                'transfer' => [
                    'require' => 'crm.opportunity.transfer'
                ],
                'addhandler' => [
                    'require' => 'crm.opportunity.member.manage'
                ],
                'removehandler' => [
                    'require' => 'crm.opportunity.member.manage'
                ],
            ]
        ],

        // ai自动化设置
        'aisettingwrite' => [
            'action' => [
                'customersettings' => [
                    'functional' => 'crm.functional.ai.classify.customer'
                ],
                'opportunitysettings' => [
                    'functional' => 'crm.functional.ai.classify.opportunity'
                ],
            ]
        ],

        // ai仪表盘
        'aidashboardread' => [
            'functional' => 'crm.functional.ai.dashboard'
        ],

        // ai 智能推荐
        'recommendread' => [
            'action' => [
                'getrecommendcompany' => [
                    'functional' => 'crm.functional.ai.recommend',
                    'trial' => 1,
                ],
            ]
        ],
        // ai 发送开发信
        'recommendwrite' => [
            'action' => [
                'sendrecommendmail' => [
                    'functional' => 'crm.functional.ai.recommend'
                ],
            ]
        ],


        // 业绩
        'performancewrite' => [
            'action' => [
                'savesetting' => [
                    'require' => 'crm.performance.setting',
                ],
                'saveusergoals' => [
                    'require' => 'crm.performance.create',
                ],
                'savedepartmentgoals' => [
                    'require' => 'crm.performance.create',
                ],
                'recalculate' => [
                    'require' => 'crm.performance.setting',
                ],
            ],
        ],
        'performanceread' => [
//            'functional' => 'crm.functional.performance',
            'require' => 'crm.performance.view',
            'except' => ['setting']
        ],
        //paypal账户设置
        'paymentsettingwrite' =>[
            'action' => [
                'binding' => [
                    'require' => 'crm.setting.paypal.manage',
                ],
                'unbinding' => [
                    'require' => 'crm.setting.paypal.manage',
                ],
                'savecompanyinfo' => [
                    'require' => 'crm.setting.paypal.manage',
                ],
            ],
        ],

        //paypal账户读取
        'paymentsettingread' =>[
            'action' => [
                'list' => [
                    'require' => 'crm.setting.paypal.manage',
                ],
                'loginwithpaypal' => [
                    'require' => 'crm.setting.paypal.manage',
                ],
            ],
        ],
        'ciqread' => [
            'action' =>[
                'recordsearch' =>[
                    'trial' => 1,
                ],
                'companysearch' => [
                    'trial' => 1,
                ],
            ]
        ],
//        'ciqwrite' => [
//            'functional' => 'crm.functional.ciq',
//        ],
        'ordersettingread'=>[
            'action' =>[
                'templateinfo' =>[
                    'require'=>'crm.setting.order.template'
                ]
            ]
        ],
        'ordersettingwrite' =>[
            'action' =>[
                'addfield' =>[
                    'require'=>'crm.setting.order.manage'
                ],
                'updatefield' =>[
                    'require'=>'crm.setting.order.manage'
                ],
                'deletefield' =>[
                    'require'=>'crm.setting.order.manage'
                ],
                'addstatus' =>[
                    'require'=>'crm.setting.order.manage'
                ],
                'deletestatus' =>[
                    'require'=>'crm.setting.order.manage'
                ],
                'updatestatus' =>[
                    'require'=>'crm.setting.order.manage'
                ],
                'updateorderperformance' =>[
                    'require'=>'crm.setting.order.manage'
                ],
                'updatefieldexportable' =>[
                    'require'=>'crm.setting.order.manage'
                ],
                'delexceltemplate' =>[
                    'require'=>'crm.setting.order.template'
                ],
                'setexceltemplate' =>[
                    'require'=>'crm.setting.order.template'
                ]
            ],
        ],

        //采购订单
        'purchaseordersettingread'=>[
            'action' =>[
                'templateinfo' =>[
                    'require'=>'crm.setting.purchase.order.template'
                ]
            ]
        ],
        'purchaseordersettingwrite' =>[
            'action' =>[
                'delexceltemplate' =>[
                    'require'=>'crm.setting.purchase.order.template'
                ],
                'setexceltemplate' =>[
                    'require'=>'crm.setting.purchase.order.template'
                ]
            ],
        ],

        // 回收箱
        'recycleread' => [
            'require' => 'crm.setting.recycle.manage'
        ],
        // 回收箱
        'recyclewrite' => [
            'require' => 'crm.setting.recycle.manage'
        ],
        // 云盘
        'diskread' => [
            'action' => [
                'rootfolderlist' => [
                    'require' => 'crm.setting.disk.manage',
                ],
                'clientuser' => [
                    'require' => 'crm.setting.disk.manage',
                ]
            ],
        ],
        // 统计分析
        'statisticsread' => [
            'require' => 'crm.setting.statistic.analyse',
            'except' => [
                'customeroverview',
                'mailoverview',
                'orderoverview',
                'pioverview',
                'quotoverview',
                'edmoverview',
                'productoverview',
                'quotationrecently',
                'barchart',
                'linechart',
                'customerpiechart',
                'pirecently',
                'orderrecently',
                'departmentmemberstatistics',
                'departmentperformancestatistics',
                'userworkstatistics',
                'piechart',
                'usercustomertotal'
            ],
        ],
        'subordinateread' => [
            'functional' => 'crm.functional.base',
        ],
        'exchangeratewrite' => [
            'require'=>'crm.setting.exchange.manage',
        ],
        'mailsettingread' => [
            'action' => [
//                'usermaillist' => [
//                    'require' => 'crm.setting.email.manage',
//                ],
            ]
        ],
        'mailsettingwrite' => [
            'action' => [
                'bindusermail' => [
                    'require' => 'crm.setting.email.manage',
                ],
                'checkbindemail' => [
                    'require' => 'crm.setting.email.manage',
                ],
                'saveusermail' => [
                    'require' => 'crm.setting.email.manage',
                ],
                'deletebindemail' => [
                    'require' => 'crm.setting.email.manage',
                ],
            ]
        ],
        // 报价单
        'quotationsettingread'=>[
            'action' =>[
                'templateinfo' =>[
                    'require'=>'crm.setting.quotation.template'
                ]
            ]
        ],
        'quotationsettingwrite' =>[
            'action' =>[
                'updatestatus' =>[
                    'require' => 'crm.setting.quotation.manage',
                ],
                'deletestatus' =>[
                    'require' => 'crm.setting.quotation.manage',
                ],
                'updateRule' =>[
                    'require' => 'crm.setting.quotation.manage',
                ],
                'addField' =>[
                    'require' => 'crm.setting.quotation.manage',
                ],
                'updatefield' =>[
                    'require' => 'crm.setting.quotation.manage',
                ],
            ]
        ],
        'privilegewrite' => [
            'require' => 'crm.setting.privilege.manage',
            'except' => ['assignsecondroleuser', 'removesecondroleuser', 'savemoduleswitch'],
            'action' => [
                'savemoduleswitch' =>[
                    'require' => 'crm.setting.module.manage',
                ],
                'setfieldscope' => [
                    'require' => 'crm.setting.fields.manage',
                ],
            ]
        ],
        'mailread' => [
            'action' => [
                'info' => [
                    'require' => 'crm.mail.view',
                ],
                'salesinfo' => [
                    'require' => 'crm.mail.view',
                ],
            ]
        ],
        'fieldsettingwrite' => [
            'require' => 'crm.setting.fields.manage',
            'except' => ['fielditemsave', 'fielditemdelete', 'arrangefielditems', 'arrangefieldlistorder'],
        ],
        'cashcollectionread' => [
            'functional' => 'crm.functional.cashCollection',
            'action' => [
                'detail' => [
                    'require' => 'crm.cashCollection.view',
                ],
            ],
        ],
        'workflowRuleWrite' => [
            'functional' => 'crm.functional.workflow',
            'require' => 'crm.setting.workflow.manage',
        ],
        'workflowRuleRead' => [
            'functional' => 'crm.functional.workflow',
        ],
        'approvalflowWrite' => [
            'functional' => 'crm.functional.approvalflow',
            'require' => 'crm.setting.approvalflow.manage',
        ],
        'approvalflowRead' => [
            'functional' => 'crm.functional.approvalflow',
            'require' => 'crm.setting.approvalflow.manage',
        ],
        'transport' => [
            'functional' => 'crm.functional.transport',
            'require' => 'crm.transport.view'
        ],
        'googleadsread' => [
            'functional' => 'crm.functional.ads',
        ],
        'googleadswrite' => [
            'functional' => 'crm.functional.ads',
            'require' => 'crm.ads.setting.manage'
        ],
        'googlegaread' => [
            'functional' => 'crm.functional.ads',
        ],
        'googlegawrite' => [
            'functional' => 'crm.functional.ads',
        ],
        'googlewebsiteanalyticsread' => [
            'functional' => 'crm.functional.ads',
            'require' => 'crm.ads.site.view'
        ],
        'googleadsanalyticsread' => [
            'functional' => 'crm.functional.ads',
            'require' => 'crm.ads.report.view'
        ],
        'erpread' => [
            'action' => [
                'appinfo' => [
                    'functional' => 'crm.functional.okki',
                ],
                'logdigest' => [
                    'functional' => 'crm.functional.okki',
                ],
                'loglist' => [
                    'functional' => 'crm.functional.okki',
                ],
                'userphone' => [
                    'functional' => 'crm.functional.okki',
                ],
                'sendshowappsecretcode' => [
                    'functional' => 'crm.functional.okki',
                ],
            ],
        ],
        //平台产品
        'thirdproductread' => [
            'functional' => 'crm.functional.platform.product',
            'action' => [
                'list' => [
                    'require' => 'crm.platform.product.view'
                ],
            ],
        ],
        'thirdproductwrite' => [
            'functional' => 'crm.functional.platform.product',
            'action' => [
                'matchto' => [
                    'require' => 'crm.platform.product.match'
                ],
                'delete' => [
                    'require' => 'crm.platform.product.delete'
                ],
                'releasematch'=>[
                    'require' => 'crm.platform.product.match'
                ],
                'multimatch'=>[
                    'require' => 'crm.platform.product.match'
                ],
                'matchfororder'=>[
                    'require' => 'crm.platform.product.match'
                ]
            ],
        ],

        // FB社媒管理
        'facebook' => [
            'functional' => 'crm.functional.socialmedia',
            'action' => [
                'unbind' => [
                    'require' => 'crm.socialmedia.remove'
                ],
                'pageList' => [
                    'require' => 'crm.socialmedia.view'
                ],
                'assignPage' => [
                    'require' => 'crm.socialmedia.assign'
                ]
            ],
        ],

        //  FB互动-帖子查看
        'facebookcommentread' => [
            'functional' => 'crm.functional.facebook',
            'action' => [
                'getpagepostcommentlist' => [
                    'require' => 'crm.facebook.post.view'
                ],
                'getcommentchildren' => [
                    'require' => 'crm.facebook.post.view'
                ]
            ],
        ],

        //  FB互动-帖子查看
        'facebookcommentwrite' => [
            'functional' => 'crm.functional.facebook',
            'action' => [
                'replycomment' => [
                    'require' => 'crm.facebook.post.view'
                ],
                'deletecomment' => [
                    'require' => 'crm.facebook.post.view'
                ],
                'createlead' => [
                    'require' => 'crm.facebook.post.view'
                ],
                'batchcreatelead' => [
                    'require' => 'crm.facebook.post.view'
                ],
                'ignorecomments' => [
                    'require' => 'crm.facebook.post.view'
                ]
            ],
        ],

        //FB互动-lead表单
        'facebookleadread' => [
            'functional' => 'crm.functional.facebooklead',
            'action' => [
                'leadFormList' => [
                    'require' => 'crm.facebook.lead.view'
                ],
                'formList' => [
                    'require' => 'crm.facebook.lead.view'
                ],
                'leadAssignUserList' => [
                    'require' => 'crm.facebook.lead.view'
                ],
            ],
        ],

        //FB互动-lead表单
        'facebookleadwrite' => [
            'functional' => 'crm.functional.facebooklead',
            'action' => [
                'leadBatchAssignUser' => [
                    'require' => 'crm.facebook.lead.view'
                ],
                'createLead' => [
                    'require' => 'crm.facebook.lead.view'
                ],
            ],
        ],

        // WhatsApp Business管理-页面查看   号码查看
        'wabachannelread' => [
            'functional' => 'crm.functional.waba',
            'action' => [
                'getchannellist' => [
                    'require' => 'crm.waba.manage.view'
                ]
            ],
        ],

        // WhatsApp Business管理-页面查看   联系人查看
        'wabacontactread' => [
            'functional' => 'crm.functional.waba',
            'action' => [
                'getcontactlist' => [
                    'require' => 'crm.waba.manage.view'
                ]
            ],
        ],

        // WhatsApp Business管理-页面查看   模板查看
        'wabatemplateread' => [
            'functional' => 'crm.functional.waba',
            'action' => [
                'getalltemplate' => [
                    'require' => 'crm.waba.view'
                ],
                'gettemplatelist' => [
                    'require' => 'crm.waba.view'
                ]
            ],
        ],

        // WhatsApp Business管理-模板管理
        'wabatemplatewrite' => [
            'functional' => 'crm.functional.waba',
            'action' => [
                'savetemplate' => [
                    'require' => 'crm.waba.manage.template'
                ],
                'summittemplates' => [
                    'require' => 'crm.waba.manage.template'
                ],
                'deletetemplate' => [
                    'require' => 'crm.waba.manage.template'
                ],
                'deletetemplatelanguage' => [
                    'require' => 'crm.waba.manage.template'
                ],
                'synctemplate' => [
                    'require' => 'crm.waba.manage.template'
                ]
            ],
        ],

        // WhatsApp Business管理-新建号码\分配号码
        'wabachannelwrite' => [
            'functional' => 'crm.functional.waba',
            'action' => [
                'createchannel' => [
                    'require' => 'crm.waba.manage.phone.create'
                ],
                'assignchannel' => [
                    'require' => 'crm.waba.manage.phone.allot'
                ]
            ],
        ],

        // whatsapp 云端版
        'whatsappcloudread' => [
            'action' => [
                'action' => [
                    'getchannellist' => [
                        'require' => 'crm.whatsapp.cloud.manage'
                    ],
                ],
            ],
        ],
        'whatsappcloudwrite' => [
            'action' => [
                'removechannel' => [
                    'require' => 'crm.whatsapp.cloud.remove.account',
                ],
                'savedurationsetting' => [
                    'require' => 'crm.whatsapp.cloud.marketing.setting'
                ],
                'savetask' => [
                    'require' => 'crm.whatsapp.cloud.view'
                ],
                'assignusers' => [
                    'require' => 'crm.whatsapp.cloud.assign.account'
                ],
                'createorgetloginsession' => [
                    'require' => 'crm.whatsapp.cloud.view'
                ],
                'forwardconversation' => [
                    'require' => 'crm.whatsapp.cloud.transfer.conversation'
                ],
            ],
        ],
        'chatbehaviorriskread' => [
            'action' => [
                'getrecordlist' => [
                    'require' => 'crm.whatsapp.cloud.behavior_risk.manage'
                ],
                'getsensitivewordrulelist' => [
                    'require' => 'crm.whatsapp.cloud.behavior_risk.manage'
                ],
            ]
        ],
        'chatbehaviorriskwrite' => [
            'action' => [
                'addsensitiveworldrule' => [
                    'require' => 'crm.whatsapp.cloud.behavior_risk.manage'
                ],
                'editsensitiveworldrule' => [
                    'require' => 'crm.whatsapp.cloud.behavior_risk.manage'
                ],
                'deleterule' => [
                    'require' => 'crm.whatsapp.cloud.behavior_risk.manage'
                ],
            ]
        ],

        // ma 智能营销
        'marketingautomationread' => [
            'functional' => 'crm.functional.auto.marketing',
            'action' => [
                'planlist' => [
                    'require' => 'crm.auto.marketing.view',
                ],
                'planinfo' => [
                    'require' => 'crm.auto.marketing.view'
                ],
                'planstatistics' => [
                    'require' => 'crm.auto.marketing.view'
                ],
                'plancontactlist' => [
                    'require' => 'crm.auto.marketing.view'
                ],
                'plancanvas' => [
                    'require' => 'crm.auto.marketing.view'
                ],
            ],
        ],
        'marketingautomationwrite' => [
            'functional' => 'crm.functional.auto.marketing',
            'action' => [
                'createplan' => [
                    'require' => 'crm.auto.marketing.create',
                ],
                'joinplan' => [
                    'require' => 'crm.auto.marketing.create',
                ],
                'stopplan' => [
                    'require' => 'crm.auto.marketing.create',
                ],
                'deleteplan' => [
                    'require' => 'crm.auto.marketing.delete',
                ],
                'saveplan' => [
                    'require' => 'crm.auto.marketing.create',
                ],
                'startwhatsapp' => [
                    'require' => 'crm.auto.marketing.create'
                ],
                'addplancontact' => [
                    'require' => 'crm.auto.marketing.create',
                ],
                'saveplanconfig' => [
                    'require' => 'crm.auto.marketing.create',
                ],
                'createhandler' => [
                    'require' => 'crm.auto.marketing.create',
                ],
                'savehandler' => [
                    'require' => 'crm.auto.marketing.create',
                ],
                'importcontactlist' => [
                    'require' => 'crm.auto.marketing.create'
                ],
            ],
        ],

        'recommendplazaread' => [
            'action' => [
                'recommendlist' => [
                    'functional' => 'crm.functional.leads.recommend.plaza'
                ],
                'customizedrecommend' => [
                    'functional' => 'crm.functional.leads.recommend.plaza',
                ],
            ],
        ],
        'recommendplazawrite' => [
            'action' => [
                'batchcreatecompany' => [
                    'require' => 'crm.company.private.create'
                ],
                'batchcreatelead' => [
                    'require' => 'crm.lead.private.create'
                ],
            ],
        ],


        // 阿里圈战查看
        'alisalesraceread' => [
            'functional' => 'crm.functional.ali.sales.race'
        ],
        // 阿里圈战管理
        'alisalesracewrite' => [
            'functional' => 'crm.functional.ali.sales.race'
        ],
        //成单指南-话术
        'speechcraftread' => [
            'functional' => [
                'crm.functional.speechcraft'
            ],
        ],
        'speechcraftwrite' => [
            'functional' => [
                'crm.functional.speechcraft'
            ],
        ],

        //成单指南-文档
        'tradedocumentread' => [
            'functional' => [
                'crm.functional.document'
            ],
        ],
        'tradedocumentwrite' => [
            'functional' => [
                'crm.functional.document'
            ],
        ],
        'tmssuggestionread' => [
            'functional' => [
                'crm.functional.suggestion'
            ],
        ],
        'tmssuggestionwrite' => [
            'functional' => [
                'crm.functional.suggestion'
            ],
        ],

        'salesguideread' => [
            'functional' => [
                'crm.functional.speechcraft',
                'crm.functional.document',
                'crm.functional.suggestion'
            ],
        ],
        'salesguidewrite' => [
            'functional' => [
                'crm.functional.speechcraft',
                'crm.functional.document',
                'crm.functional.suggestion'
            ],
        ],

        'discoveryv2read' => [
            'functional' => [
                'dx.functional.base'
            ],
        ],

        'tradedoccatewrite' => [
            'require' => 'crm.setting.sales.guide',
        ],

        'websitechatread' => [
            'functional' => 'mkt.functional.conversation',
            'action' => [
                'translationtexts' => [
                    'require' => 'mkt.setting.i18n'
                ],
                'translationsetting' => [
                    'require' => 'mkt.setting.i18n'
                ],
                'customformlist' => [
                    'functional' => 'mkt.functional.custom.form'
                ],
                'customformfieldlist' => [
                    'functional' => 'mkt.functional.custom.form'
                ],
                'customforminfo' => [
                    'functional' => 'mkt.functional.custom.form'
                ],
                'candeletecustomform' => [
                    'functional' => 'mkt.functional.custom.form'
                ],
            ]
        ],
        'websitechatwrite' => [
            'function' => 'mkt.functional.conversation',
            'require' => 'mkt.conversation.setting.edit',
            'action' => [
                'updatetranslationtext' => [
                    'require' => 'mkt.setting.i18n'
                ],
                'updatetranslationsetting' => [
                    'require' => 'mkt.setting.i18n'
                ],
                'savecustomformtitle' => [
                    'require' => 'mkt.setting.custom.form.setting',
                ],
                'savecustomform' => [
                    'require' => 'mkt.setting.custom.form.setting',
                ],
                'setcustomformshow' => [
                    'require' => 'mkt.setting.custom.form.setting',
                ],
                'deletecustomform' => [
                    'require' => 'mkt.setting.custom.form.setting',
                ],
                'createconversationsetting' => [
                    'require' => PrivilegeConstants::PRIVILEGE_MARKETING_CONVERSATION_SETTING_EDIT
                ],
                'editconversationsetting' => [
                    'require' => PrivilegeConstants::PRIVILEGE_MARKETING_CONVERSATION_SETTING_EDIT
                ],
                'deleteconversationsetting' => [
                    'require' => PrivilegeConstants::PRIVILEGE_MARKETING_CONVERSATION_SETTING_EDIT
                ],
                'switchconversationsetting' => [
                    'require' => PrivilegeConstants::PRIVILEGE_MARKETING_CONVERSATION_SETTING_EDIT,
                ],
                'switchconversationonlineflag' => [
                    'require' => PrivilegeConstants::PRIVILEGE_MARKETING_CONVERSATION_SETTING_EDIT
                ],
            ]
        ],
    ],
    'app' => [
        // 客户
        'customerread'=>[
            'action' => [
                'companyfield' => [
                    'require' => 'crm.company.private.view'
                ],
                'export' => [
                    'require' => 'crm.setting.customer.export'
                ],
            ],
        ],
        'customerwrite'=>[
            'action' => [
                'editgroup' => [
                    'require' => 'crm.setting.customer.manage'
                ],
                'setreference' => [
                    'require' => 'crm.setting.customer.manage'
                ],
                'setcustomerlimit' => [
                    'require' => 'crm.setting.customer.manage'
                ],
                'renameorigin' => [
                    'require' => 'crm.setting.customer.manage'
                ],
                'addorigin' => [
                    'require' => 'crm.setting.customer.manage'
                ],
                'deleteorigin' => [
                    'require' => 'crm.setting.customer.manage'
                ],
                'createstatus' => [
                    'require' => 'crm.setting.customer.manage'
                ],
                'alterstatus' => [
                    'require' => 'crm.setting.customer.manage'
                ],
                'deletestatus' => [
                    'require' => 'crm.setting.customer.manage'
                ],
                'addblacklist' => [
                    'require' => 'crm.setting.customer.manage'
                ],
                'removeblacklist' => [
                    'require' => 'crm.setting.customer.manage'
                ],

                'remark' => [
                    'require' => 'crm.company.private.view'
                ],
//                'star' => [
//                    'require' => 'crm.company.private.edit'
//                ],
//                'savecustomer' => [
//                    'require' => 'crm.company.private.edit'
//                ],
//                'status' => [
//                    'require' => 'crm.company.private.edit'
//                ],
                'addtag' => [
                    'require' => 'crm.company.private.view'
                ],
//                'setgroup' => [
//                    'require' => 'crm.company.private.edit'
//                ],
//                'mergecompany' => [
//                    'require' => 'crm.company.private.edit'
//                ],
                'movetopublic' => [
                    'require' => 'crm.company.private.move.pool'
                ],
                'remove' => [
                    'require' => 'crm.company.private.release'
                ],
                'share' => [
                    'require' => 'crm.company.private.share'
                ],
                'hold' => [
                    'require' => 'crm.company.pool.hold'
                ],
//                'transfer' => [
//                    'require' => 'crm.company.private.transfer'
//                ],
//                'transferall' => [
//                    'require' => 'crm.company.private.assign'
//                ],
//                'import' => [
//                    'require' => 'crm.company.private.create'
//                ],


            ],
        ],
        'salescustomerwrite' => [
            'action' => [
                'share' => [
                    'require' => 'crm.company.private.share'
                ],
                'movetopublic' => [
                    'require' => 'crm.company.private.move.pool'
                ],
            ],
        ],
        //报价单
        'quotationread' =>[
            'action' =>[
                'list' =>[
                    'require' => 'crm.quotation.view',
                ],
                'filelist' =>[
                    'require' => 'crm.quotation.view',
                ],
                'exportfilelist' =>[
                    'require' => 'crm.quotation.view',
                ],
                'historylist' =>[
                    'require' => 'crm.quotation.view',
                ],
                'info' =>[
                    'require' => 'crm.quotation.view',
                ],
            ]

        ],
        'quotationwrite' =>[
            'action' =>[
                'del' =>[
                    'require' => 'crm.quotation.remove',
                ],
                'setstatus' =>[
                    'require' => 'crm.quotation.view',
                ],
                'setremark' =>[
                    'require' => 'crm.quotation.view',
                ],
                'removefile' => [
                    'require' => 'crm.quotation.view',
                ],
                'delexportfile' => [
                    'require' => 'crm.quotation.view',
                ],
            ]

        ],
        //订单
        'orderread' =>[
            'action' =>[
                'list' =>[
                    'require' => 'crm.order.view',
                ],
                'filelist' =>[
                    'require' => 'crm.order.view',
                ],
                'exportfilelist' =>[
                    'require' => 'crm.order.view',
                ],
                'historylist' =>[
                    'require' => 'crm.order.view',
                ],
                'info' =>[
                    'require' => 'crm.order.view',
                ],
            ]

        ],
        'orderwrite' =>[
            'action' =>[
                'del' =>[
                    'require' => 'crm.order.remove',
                ],
                'setstatus' =>[
                    'require' => 'crm.order.view',
                ],
                'sethandler' =>[
                    'require' => 'crm.order.view',
                ],
                'setremark' =>[
                    'require' => 'crm.order.view',
                ],
            ]
        ],

        // ai
        'recommendread' => [
            'action' =>[
                'getrecommendcompany' =>[
                    'require' => 'crm.ai.user',
                    'trial' => 1,
                ],
            ]
        ],
        'fieldsettingwrite' => [
            'require' => 'crm.setting.fields.manage',
            'except' => ['fielditemsave', 'fielditemdelete', 'arrangefielditems', 'arrangefieldlistorder'],
        ],
        'workflowRuleWrite' => [
            'functional' => 'crm.functional.workflow',
            'require' => 'crm.setting.workflow.manage',
        ],
        'workflowRuleRead' => [
            'functional' => 'crm.functional.workflow',
        ],
        'ciqread' => [
            'action' =>[
                'recordsearch' =>[
                    'trial' => 1,
                ],
                'companysearch' => [
                    'trial' => 1,
                ],
            ]
        ],
        'edmwrite' => [
            'action' =>[
                'send' =>[
                    'trial' => 1,
                ],
            ],
        ],
        'edmread' => [
            'action' =>[
                'list' =>[
                    'trial' => 1,
                ],
            ],
        ],
        'leadread' => [
            'action' => [
                'leadlist' => [
                    'trial' => 1,
                ],
            ],
        ],
        'approvalflowWrite' => [
            'functional' => 'crm.functional.approvalflow',
            'require' => 'crm.setting.approvalflow.manage',
        ],
        'approvalflowRead' => [
            'functional' => 'crm.functional.approvalflow',
            'require' => 'crm.setting.approvalflow.manage',
        ],
        'leadwrite' => [
            'functional' => 'crm.functional.lead',
            'require' => 'crm.lead.private.view',
        ],
        // 阿里圈战查看
        'alisalesraceread' => [
            'functional' => 'crm.functional.ali.sales.race'
        ],
        // 阿里圈战管理
        'alisalesracewrite' => [
            'functional' => 'crm.functional.ali.sales.race'
        ],
        'userscheduleread' => [
            'action'     => [
                'list'        => [
                    'functional' => 'crm.functional.schedule',
                ],
            ],
        ]
    ],
    'marketing' => [
        'articleread'              => [
            'functional' => 'mkt.functional.content',
            'require'    => 'mkt.article.view',
            'action'     => [
                'list'        => [
                    'require' => 'mkt.article.view',
                ],
                'articleinfo' => [
                    'require' => 'mkt.article.view',
                ],
            ],
        ],
        'articlewrite'             => [
            'functional' => 'mkt.functional.content',
            'require'    => 'mkt.article.view',
            'action'     => [
                'delete'             => [
                    'require' => 'mkt.article.delete',
                ],
                'enableshow'         => [
                    'require' => 'mkt.article.edit',
                ],
                'articlerank'        => [
                    'require' => 'mkt.article.edit',
                ],
                'updatearticlegroup' => [
                    'require' => 'mkt.article.edit',
                ],
                'groupbatchdelete'   => [
                    'require' => 'mkt.article.delete',
                ],
            ],
        ],
        'conversationsettingread' => [
            'functional' => 'mkt.functional.conversation',
            'action'     => [
                'list' => [
                    'require' => 'mkt.conversation.setting.view',
                ],
            ]
        ],
        'conversationsettingwrite' => [
            'functional' => 'mkt.functional.conversation',
            'require'    => 'mkt.conversation.setting.view',
            'action'     => [
                'edit'          => [
                    'require' => 'mkt.conversation.setting.edit',
                ],
                'switchsetting' => [
                    'require' => 'mkt.conversation.setting.edit',
                ],
                'delete'        => [
                    'require' => 'mkt.conversation.setting.delete',
                ],
            ],
        ],
        'domainread'               => [
            'require' => 'mkt.site.manage.domain',
        ],
        'domainwrite'              => [
            'require' => 'mkt.site.manage.domain',
        ],
        'formread'                 => [
            'functional' => 'mkt.functional.form',
            'require'    => 'mkt.form.view',
        ],
        'formwrite'                => [
            'functional' => 'mkt.functional.form',
            'require'    => 'mkt.form.view',
            'action'     => [
                'formshow' => [
                    'require' => 'mkt.form.edit',
                ],
                'delete'   => [
                    'require' => 'mkt.form.delete',
                ],
            ],
        ],
        'customformread'                 => [
            'functional' => 'mkt.functional.custom.form',
            'require'    => 'mkt.setting.custom.form.setting',
        ],
        'customformwrite'                => [
            'functional' => 'mkt.functional.custom.form',
            'require'    => 'mkt.setting.custom.form.setting',
        ],
        'googleadswrite'           => [
            'action' => [
                'batchsaveadsaccount' => [
                    'require' => 'mkt.setting.google.ads.account',
                ],
                'setdefaultadsaccount' => [
                    'require' => 'mkt.setting.google.ads.account',
                ],
                'unbindadsaccount' => [
                    'require' => 'mkt.setting.google.ads.account',
                ],
                'bindadslinksetting' => [
                    'require' => 'mkt.setting.google.ads.track',
                ],
                'deleteadslinksetting' => [
                    'require' => 'mkt.setting.google.ads.track',
                ],
            ],
        ],

        'googlegawrite'      => [
            'action' => [
                'bindgaaccountwebsite' => [
                    'require' => 'mkt.setting.site.track',
                ],
                'savegalead' => [
                    'require' => 'mkt.site.manage.notification',
                ],
                'editwebsite' => [
                    'require' => 'mkt.setting.site.track',
                ],
                'deletewebsite' => [
                    'require' => 'mkt.setting.site.track',
                ],
            ],
        ],
        'historyoperationread'     => [
            'require' => 'mkt.setting.operate.record',
        ],
        'imagecollectionread'      => [
            'functional' => 'mkt.functional.content',
            'require'    => 'mkt.image_collection.view',
        ],
        'imagecollectionwrite'     => [
            'functional' => 'mkt.functional.content',
            'require'    => 'mkt.image_collection.view',
            'action'     => [
                'delete'              => [
                    'require' => 'mkt.image_collection.delete',
                ],
                'enableshow'          => [
                    'require' => 'mkt.image_collection.edit',
                ],
                'updatecollectgroup'  => [
                    'require' => 'mkt.image_collection.edit',
                ],
                'imagecollectionrank' => [
                    'require' => 'mkt.image_collection.edit',
                ],
            ],
        ],
        'inquiryread'              => [
            'functional' => 'mkt.functional.inquiry',
            'require'    => 'mkt.inquiry.view',
            'except' => [
                'countunread',
            ],
        ],
        'inquirywrite'             => [
            'functional' => 'mkt.functional.inquiry',
            'action'     => [
                'delete' => [
                    'require' => 'mkt.inquiry.delete',
                ],
                'inquirytransfer' => [
                    'require' => 'mkt.site.manage.notification',
                ],
            ],
        ],
        'privilegeread'     => [
            'action' => [
                'allprivilegelist' => [
                    'require' => 'mkt.setting.role.manage'
                ],
                'roleslist' => [
                    'require' => 'mkt.setting.role.manage'
                ],
                'info' => [
                    'require' => 'mkt.setting.role.manage'
                ],
                'userlist' => [
                    'require' => 'mkt.setting.role.manage'
                ],
            ],
        ],
        'privilegewrite'           => [
            'require' => 'mkt.setting.role.manage',
        ],
        'productread' => [
            'functional' => 'mkt.functional.content',
            'action'     => [
                'info'                      => [
                    'require' => 'mkt.product.view',
                ],
                'list'                      => [
                    'require' => 'mkt.product.view',
                ],
                'importdefaultfieldmapping' => [
                    'require' => 'mkt.product.syncing',
                ],
                'importtasklist'            => [
                    'require' => 'mkt.product.syncing',
                ],
                'importlist'                => [
                    'require' => 'mkt.product.syncing',
                ],
                'listcrm'                   => [
                    'require' => 'mkt.product.syncing',
                ],
                'hastaskrunning'            => [
                    'require' => 'mkt.product.syncing',
                ],
            ],
        ],
        'productsettingwrite'      => [
            'action' => [
                'addattributefield' => [
                    'require' => 'mkt.product.setting',
                ],
            ],
        ],
        'productwrite'             => [
            'functional' => 'mkt.functional.content',
            'action'     => [
                'create'                 => [
                    'require' => 'mkt.product.create',
                ],
                'edit'                   => [
                    'require' => 'mkt.product.edit',
                ],
                'delete'                 => [
                    'require' => 'mkt.product.delete',
                ],
                'moveproductgroup'       => [
                    'require' => 'mkt.product.edit',
                ],
                'productordersave'       => [
                    'require' => 'mkt.product.edit',
                ],
                'productswitch'          => [
                    'require' => 'mkt.product.edit',
                ],
                'importbyexcel'          => [
                    'require' => 'mkt.product.syncing',
                ],
                'importproductbyalibaba' => [
                    'require' => 'mkt.product.syncing',
                ],
                'importproductbycrm'     => [
                    'require' => 'mkt.product.syncing',
                ],
                'copy'                   => [
                    'require' => 'mkt.product.create',
                ],
            ],
        ],

        'sectionread'              => [
            'require' => 'mkt.site.manage.section',
        ],
        'sectionwrite'             => [
            'require' => 'mkt.site.manage.section',
        ],
        'sitesettingwrite'      => [
            'action' => [
                'savenoticeemail' => [
                    'require' => 'mkt.site.manage.notification',
                ],
                'setdefaultsite' => [
                    'require' => 'mkt.site.manage.info',
                ],
                'savebaseinfo' => [
                    'require' => 'mkt.site.manage.info',
                ],
                'saveenterpriseinfo' => [
                    'require' => 'mkt.site.manage.info',
                ],
                'savesocial' => [
                    'require' => 'mkt.site.manage.info',
                ],
                'saveshare' => [
                    'require' => 'mkt.site.manage.info',
                ],
                'publishsitemap' => [
                    'require' => 'mkt.site.manage.edit',
                ],

            ],
        ],
        'sitetrackread'            => [
            'action' => [
                'conversationlist' => [
                    'require' => 'mkt.conversation.history.view',
                ],
            ],
        ],
        'subscriberead'            => [
            'functional' => 'mkt.functional.subscribe',
            'require'    => 'mkt.subscription.view',
        ],
        'subscribewrite'           => [
            'functional' => 'mkt.functional.subscribe',
            'require'    => 'mkt.subscription.view',
            'action'     => [
                'save'            => [
                    'require' => 'mkt.subscription.edit',
                ],
                'enablesubscribe' => [
                    'require' => 'mkt.subscription.edit',
                ],
            ],
        ],
        'templateread'             => [
            'require' => 'mkt.site.manage.template',
        ],
        'templatewrite'            => [
            'require' => 'mkt.site.manage.template',
        ],
        'versionread'              => [
            'action' => [
                'versionprogress'      => [
                    'require' => 'mkt.site.manage.deploy',
                ],
                'versionoperationstat' => [
                    'require' => 'mkt.site.manage.deploy',
                ],
            ],
        ],
        'versionwrite'             => [
            'action' => [
                'publish' => [
                    'require' => 'mkt.site.manage.deploy',
                ],
            ],
        ],
        'videolistread'  => [
            'functional' => 'mkt.functional.content',
            'require'    => 'mkt.video.view',
        ],
        'videolistwrite' => [
            'functional' => 'mkt.functional.content',
            'require'    => 'mkt.video.view',
            'action'     =>
                [
                    'delete' =>
                        [
                            'require' => 'mkt.video.delete',
                        ],
                ],
        ],
        'downloadlistread'  => [
            'functional' => 'mkt.functional.content',
            'require'    => 'mkt.download.view',
        ],
        'downloadlistwrite' => [
            'functional' => 'mkt.functional.content',
            'require'    => 'mkt.download.view',
            'action'     =>
                [
                    'delete' =>
                        [
                            'require' => 'mkt.download.delete',
                        ],
                ],
        ],
        // 多语种文案设置
        'translationread' => [
            'functional' => 'mkt.functional.i18n',
            'require'    => 'mkt.setting.i18n',
        ],
        'translationwrite' => [
            'functional' => 'mkt.functional.i18n',
            'require'    => 'mkt.setting.i18n',
        ],



//-------------------erp-----------------------
        'purchaseinboundread' => [
            'action' => [
                'info' => [
                    'require' => PrivilegeConstants::PRIVILEGE_CRM_PURCHASE_INBOUND_VIEW
                ],
                'detail' => [
                    'require' => PrivilegeConstants::PRIVILEGE_CRM_PURCHASE_INBOUND_VIEW
                ],
                'printdata' => [
                    'require' => PrivilegeConstants::PRIVILEGE_CRM_PURCHASE_INBOUND_PRINT
                ]
            ],
        ],
        'purchaseinboundwrite' => [
            'action' => [
                'create' => [
                    'require' => PrivilegeConstants::PRIVILEGE_CRM_PURCHASE_INBOUND_CREATE
                ],
                'edit' => [
                    'require' => PrivilegeConstants::PRIVILEGE_CRM_PURCHASE_INBOUND_EDIT
                ],
                'delete' => [
                    'require' => PrivilegeConstants::PRIVILEGE_CRM_PURCHASE_INBOUND_REMOVE
                ],
                'inwarehouse' => [
                    'require' => PrivilegeConstants::PRIVILEGE_CRM_PURCHASE_INBOUND_CONFIRM
                ],
                'changehandler' => [
                    'require' => PrivilegeConstants::PRIVILEGE_CRM_PURCHASE_INBOUND_EDIT
                ],
            ],
        ],

        'otherinboundread' => [
            'action' => [
                'info' => [
                    'require' => PrivilegeConstants::PRIVILEGE_CRM_OTHER_INBOUND_VIEW
                ],
                'detail' => [
                    'require' => PrivilegeConstants::PRIVILEGE_CRM_OTHER_INBOUND_VIEW
                ],
                'printdata' => [
                    'require' => PrivilegeConstants::PRIVILEGE_CRM_OTHER_INBOUND_PRINT
                ]
            ],
        ],
        'otherinboundwrite' => [
            'action' => [
                'create' => [
                    'require' => PrivilegeConstants::PRIVILEGE_CRM_OTHER_INBOUND_CREATE
                ],
                'edit' => [
                    'require' => PrivilegeConstants::PRIVILEGE_CRM_OTHER_INBOUND_EDIT
                ],
                'delete' => [
                    'require' => PrivilegeConstants::PRIVILEGE_CRM_OTHER_INBOUND_REMOVE
                ],
                'inwarehouse' => [
                    'require' => PrivilegeConstants::PRIVILEGE_CRM_OTHER_INBOUND_CONFIRM
                ],
                'changehandler' => [
                    'require' => PrivilegeConstants::PRIVILEGE_CRM_OTHER_INBOUND_EDIT
                ],
            ],
        ],

        'salesoutboundread' => [
            'action' => [
                'info' => [
                    'require' => PrivilegeConstants::PRIVILEGE_CRM_SALES_OUTBOUND_VIEW
                ],
                'detail' => [
                    'require' => PrivilegeConstants::PRIVILEGE_CRM_SALES_OUTBOUND_VIEW
                ],
                'printdata' => [
                    'require' => PrivilegeConstants::PRIVILEGE_CRM_SALES_OUTBOUND_PRINT
                ]
            ],
        ],
        'salesoutboundwrite' => [
            'action' => [
                'create' => [
                    'require' => PrivilegeConstants::PRIVILEGE_CRM_SALES_OUTBOUND_CREATE
                ],
                'edit' => [
                    'require' => PrivilegeConstants::PRIVILEGE_CRM_SALES_OUTBOUND_EDIT
                ],
                'delete' => [
                    'require' => PrivilegeConstants::PRIVILEGE_CRM_SALES_OUTBOUND_REMOVE
                ],
                'outwarehouse' => [
                    'require' => PrivilegeConstants::PRIVILEGE_CRM_SALES_OUTBOUND_CONFIRM
                ],
                'changehandler' => [
                    'require' => PrivilegeConstants::PRIVILEGE_CRM_SALES_OUTBOUND_EDIT
                ],
            ],
        ],

        'otheroutboundread' => [
            'action' => [
                'info' => [
                    'require' => PrivilegeConstants::PRIVILEGE_CRM_OTHER_OUTBOUND_VIEW
                ],
                'detail' => [
                    'require' => PrivilegeConstants::PRIVILEGE_CRM_OTHER_OUTBOUND_VIEW
                ],
                'printdata' => [
                    'require' => PrivilegeConstants::PRIVILEGE_CRM_OTHER_OUTBOUND_PRINT
                ]
            ],
        ],
        'otheroutboundwrite' => [
            'action' => [
                'create' => [
                    'require' => PrivilegeConstants::PRIVILEGE_CRM_OTHER_OUTBOUND_CREATE
                ],
                'edit' => [
                    'require' => PrivilegeConstants::PRIVILEGE_CRM_OTHER_OUTBOUND_EDIT
                ],
                'delete' => [
                    'require' => PrivilegeConstants::PRIVILEGE_CRM_OTHER_OUTBOUND_REMOVE
                ],
                'outwarehouse' => [
                    'require' => PrivilegeConstants::PRIVILEGE_CRM_OTHER_OUTBOUND_CONFIRM
                ],
                'changehandler' => [
                    'require' => PrivilegeConstants::PRIVILEGE_CRM_OTHER_OUTBOUND_EDIT
                ],
            ],
        ],

        'purchasereturnread' => [
            'action' => [
                'info' => [
                    'require' => PrivilegeConstants::PRIVILEGE_CRM_PURCHASE_RETURN_VIEW
                ],
                'detail' => [
                    'require' => PrivilegeConstants::PRIVILEGE_CRM_PURCHASE_RETURN_VIEW
                ],
                'printdata' => [
                    'require' => PrivilegeConstants::PRIVILEGE_CRM_PURCHASE_RETURN_PRINT
                ]
            ],
        ],
        'purchasereturnwrite' => [
            'action' => [
                'create' => [
                    'require' => PrivilegeConstants::PRIVILEGE_CRM_PURCHASE_RETURN_CREATE
                ],
                'edit' => [
                    'require' => PrivilegeConstants::PRIVILEGE_CRM_PURCHASE_RETURN_EDIT
                ],
                'delete' => [
                    'require' => PrivilegeConstants::PRIVILEGE_CRM_PURCHASE_RETURN_REMOVE
                ],
                'toreturn' => [
                    'require' => PrivilegeConstants::PRIVILEGE_CRM_PURCHASE_RETURN_CONFIRM
                ],
                'changehandler' => [
                    'require' => PrivilegeConstants::PRIVILEGE_CRM_PURCHASE_RETURN_EDIT
                ],
            ],
        ],

        'inventorysettingwrite' => [
            'action' => [
                'SaveReason' => [
                    'require' => PrivilegeConstants::PRIVILEGE_SETTING_INVENTORY_REASON_MANAGE
                ],
                'DeleteReason' => [
                    'require' => PrivilegeConstants::PRIVILEGE_SETTING_INVENTORY_REASON_MANAGE
                ],
            ],
        ]
//---------------erp-end----------------------

    ]
];
