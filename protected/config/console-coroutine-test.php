<?php

if (!isset($_dcConfig))
    throw new Exception('dc config missing!');

// This is the configuration for yiic console application.
// Any writable CConsoleApplication properties can be configured here.
return array_replace_recursive(
    require(dirname(__FILE__) . '/console-test.php'),
    array(
        'behaviors' => array(
            'app' => \common\library\behavior\CoroutineBehavior::class,
        ),

        'components' => array(
            'account_base_db' => array(
                'class' => \common\library\coroutine\connection\DbConnection::class,
                'autoConnect' => false,
            ),
            'db' => array(
                'class' => \common\library\coroutine\connection\DbConnection::class,
                'autoConnect' => false,
            ),
            'prometheus_db' => array(
                'class' => \common\library\coroutine\connection\DbConnection::class,
                'autoConnect' => false,
            ),
            'xiaoman_db' => array(
                'class' => \common\library\coroutine\connection\DbConnection::class,
                'autoConnect' => false,
            ),
            'unique_upload_db' => array(
                'class' => \common\library\coroutine\connection\DbConnection::class,
                'autoConnect' => false,
            ),
            'risk_management_db' => array(
                'class' => \common\library\coroutine\connection\DbConnection::class,
                'autoConnect' => false,
            ),
            'company_match_db' => array(
                'class' => \common\library\coroutine\connection\DbConnection::class,
                'autoConnect' => false,
            ),
        ),
        'params' => array(
            //            'redis_cluster' => [
//                'option' => [
//                    'parameters'=>
//                        [
//                            'timeout'=> 0.001,
//                            'read_write_timeout' => 0.001,
//                        ]
//                ]
//            ],
            'queue' => [
                'debug' => false,
                /*
                 * 进程目录
                 */
                'loggers' => [
                    'default' => [
                        'class' => \common\library\coroutine\client\logger\Logger::class,
                        'options' => [
                            'level' => 100,
                        ],
                    ],
                ],
                'workers' => [
                    'default_job' => [
                        'enable_coroutine' => true,
                        'connection' => 'default_job',
                        'consumer' => [
                            [
                                'queue' => null,
                                'option' => [
                                    'group_id' => \common\library\queue_v2\QueueService::groupID($_dcConfig['queue_gid']['GID_crm_default_job_high_test'] ?? 'GID_crm_default_job_high_test'),
                                    'tag' => \common\library\queue_v2\QueueService::tagName('high'),
                                    'wait_seconds' => 1,
                                ],
                            ],
                            [
                                'queue' => null,
                                'option' => [
                                    'group_id' => \common\library\queue_v2\QueueService::groupID($_dcConfig['queue_gid']['GID_crm_default_job_low_test'] ?? 'GID_crm_default_job_low_test'),
                                    'tag' => \common\library\queue_v2\QueueService::tagName('low'),
                                    'wait_seconds' => 1,
                                ],
                            ],
                            [
                                'queue' => null,
                                'option' => [
                                    'group_id' => \common\library\queue_v2\QueueService::groupID($_dcConfig['queue_gid']['GID_crm_json_job'] ?? 'GID_crm_json_job'),
                                    'tag' => \common\library\queue_v2\QueueService::tagName('json'),
                                    'wait_seconds' => 1,
                                    'job_factory' => \common\library\queue_v2\job\JsonBaseJob::class,
                                ],
                            ],
                        ],
                        'sleep_seconds' => 1,
                        'memory_limit' => 96, // Mb

                        'worker_number' => 4,
                        'max_messages' => 100,
                    ],
                    'command_job' => [
                        'enable_coroutine' => true,
                        'connection' => 'command_job',
                        'consumer' => [
                            [
                                'queue' => null,
                                'option' => [
                                    'group_id' => \common\library\queue_v2\QueueService::groupID($_dcConfig['queue_gid']['GID_crm_command_job_high_test_v2'] ?? 'GID_crm_command_job_high_test_v2'),
                                    'tag' => \common\library\queue_v2\QueueService::tagName('high'),
                                    'wait_seconds' => 1,
                                ],
                            ],
                            [
                                'queue' => null,
                                'option' => [
                                    'group_id' => \common\library\queue_v2\QueueService::groupID($_dcConfig['queue_gid']['GID_crm_command_job_low_test_v2'] ?? 'GID_crm_command_job_low_test_v2'),
                                    'tag' => \common\library\queue_v2\QueueService::tagName('low'),
                                    'wait_seconds' => 1,
                                ],
                            ],
                        ],
                        'sleep_seconds' => 1,
                        'memory_limit' => 96, // Mb
                        'worker_number' => 4,
                        'max_messages' => 1000,
                    ],
                    'crontab_job' => [
                        'enable_coroutine' => true,
                        'connection' => 'crontab_job',
                        'consumer' => [
                            [
                                'queue' => null,
                                'option' => [
                                    'group_id' => \common\library\queue_v2\QueueService::groupID($_dcConfig['queue_gid']['GID_crm_crontab_job_test'] ?? 'GID_crm_crontab_job_test'),
                                    'tag' => \common\library\queue_v2\QueueService::tagName('high'),
                                    'orderly' => true,
                                    'wait_seconds' => 1,
                                ],
                            ],
                        ],
                        'sleep_seconds' => 1,
                        'memory_limit' => 96, // Mb
                        'worker_number' => 4,
                        'max_messages' => 1000,
                    ],
                    'push_job' => [
                        'enable_coroutine' => true,
                        'connection' => 'push_job',
                        'consumer' => [
                            [
                                'queue' => null,
                                'option' => [
                                    'group_id' => \common\library\queue_v2\QueueService::groupID($_dcConfig['queue_gid']['GID_crm_push_job_test'] ?? 'GID_crm_push_job_test'),
                                    'tag' => \common\library\queue_v2\QueueService::tagName('push'),
                                    'orderly' => true,
                                    'wait_seconds' => 1,
                                ],
                            ],
                            [
                                'queue' => null,
                                'option' => [
                                    'group_id' => \common\library\queue_v2\QueueService::groupID($_dcConfig['queue_gid']['GID_crm_default_job_high_test'] ?? 'GID_crm_default_job_high_test'),
                                    'tag' => \common\library\queue_v2\QueueService::tagName('high'),
                                    'orderly' => true,
                                    'wait_seconds' => 1,
                                ],
                                'concurrent' => getenv("SENSOR_JOB_CONCURRENT") ?: 20,
                            ],
                        ],
                        'sleep_seconds' => 1,
                        'memory_limit' => 96, // Mb
                        'worker_number' => 4,
                        'max_messages' => 10000,
                    ],
                    'slow_job' => [
                        'enable_coroutine' => true,
                        'connection' => 'slow_job',
                        'consumer' => [
                            [
                                'queue' => null,
                                'option' => [
                                    'group_id' => \common\library\queue_v2\QueueService::groupID($_dcConfig['queue_gid']['GID_crm_slow_job_high_test'] ?? 'GID_crm_slow_job_high_test'),
                                    'tag' => \common\library\queue_v2\QueueService::tagName('high'),
                                    'orderly' => true,
                                    'wait_seconds' => 1,
                                ],
                                'concurrent' => 1,
                            ],
                            [
                                'queue' => null,
                                'option' => [
                                    'group_id' => \common\library\queue_v2\QueueService::groupID($_dcConfig['queue_gid']['GID_crm_slow_job_low_test'] ?? 'GID_crm_slow_job_low_test'),
                                    'tag' => \common\library\queue_v2\QueueService::tagName('low'),
                                    'orderly' => true,
                                    'wait_seconds' => 1,
                                ],
                            ],
                            [
                                'queue' => null,
                                'option' => [
                                    'group_id' => \common\library\queue_v2\QueueService::groupID($_dcConfig['queue_gid']['GID_crm_swarm_job_cli_test'] ?? 'GID_crm_swarm_job_cli_test'),
                                    'tag' => \common\library\queue_v2\QueueService::tagName('alibaba'),
                                    'orderly' => true,
                                    'wait_seconds' => 1,
                                ],
                                'concurrent' => getenv("SWARM_JOB_ALIBABA_CONCURRENT") ?: 16,
                            ],
                        ],
                        'sleep_seconds' => 5,
                        'memory_limit' => 96, // Mb
                        'worker_number' => 4,
                        'max_messages' => 1000,
                        //                        'finish_check' => 5,
                    ],
                    'tips_workbench_job' => [
                        'enable_coroutine' => true,
                        'connection' => 'tips_workbench_job',
                        'consumer' => [
                            [
                                'queue' => null,
                                'option' => [
                                    'group_id' => $_dcConfig['queue_gid']['GID_crm_tips_workbench_job_test'] ?? 'GID_crm_tips_workbench_job_test',
                                    'tag' => '',
                                    'orderly' => false,
                                    'wait_seconds' => 1,
                                    'job_factory' => \common\library\queue_v2\job\TipsPushTodoJob::class,
                                ],
                            ],
                        ],
                        'sleep_seconds' => 1,
                        'memory_limit' => 128, // Mb
                        'worker_number' => 20,
                        'max_messages' => 1000,
                    ],
                    'facebook_feed_job' => [
                        'enable_coroutine' => true,
                        'connection' => 'facebook_feed_job',
                        'consumer' => [
                            [
                                'queue' => null,
                                'option' => [
                                    'group_id' => $_dcConfig['queue_gid']['GID_facebook_feed_job_test'] ?? 'GID_facebook_feed_job_test',
                                    'tag' => '',
                                    'orderly' => false,
                                    'wait_seconds' => 1,
                                    'job_factory' => \common\library\facebook\page\FacebookFeedJob::class,
                                ],
                            ],
                        ],
                        'sleep_seconds' => 1,
                        'memory_limit' => 128, // Mb
                        'worker_number' => 20,
                        'max_messages' => 1000,
                    ],
                    'dataworks_job' => [
                        'enable_coroutine' => true,
                        'connection' => 'dataworks_job',
                        'consumer' => [
                            [
                                'queue' => null,
                                'option' => [
                                    'group_id' => $_dcConfig['queue_gid']['GID_crm_dataworks_job_test'] ?? 'GID_crm_dataworks_job_test',
                                    'tag' => 'high||mail-space-cal||shop-site-cal',
                                    'orderly' => false,
                                    'wait_seconds' => 1,
                                    'job_factory' => \common\library\queue_v2\job\factory\DataWorksJob::class,
                                ],
                            ],
                            [
                                'queue' => null,
                                'option' => [
                                    'group_id' => $_dcConfig['queue_gid']['GID_email_quality_rating_level_changes_test'] ?? 'GID_email_quality_rating_level_changes_test',
                                    'tag' => 'email_level_change_cal',
                                    'orderly' => false,
                                    'wait_seconds' => 1,
                                    'job_factory' => \common\library\queue_v2\job\factory\DataWorksEmailQualityRatingChangesJob::class,
                                ],
                            ],
                            [
                                'queue' => null,
                                'option' => [
                                    'group_id' => $_dcConfig['queue_gid']['GID_ai_portrait_analysis_test'] ?? 'GID_ai_portrait_analysis_test',
                                    'tag' => 'ai_portrait_analysis_cal',
                                    'orderly' => false,
                                    'wait_seconds' => 1,
                                    'job_factory' => \common\library\queue_v2\job\factory\DataWorksAiPortraitAnalysisJob::class,
                                ],
                            ],
                            [
                                'queue' => null,
                                'option' => [
                                    'group_id' => 'GID_ai_dataworks_job_dev',
                                    'tag' => 'ai_company_check_cal||product_info_change_cal||client_personalized_info_change_cal',
                                    'orderly' => false,
                                    'wait_seconds' => 1,
                                    'job_factory' => \common\library\queue_v2\job\factory\DataWorksAiJob::class,
                                ],
                            ],
                        ],
                        'sleep_seconds' => 1,
                        'memory_limit' => 128, // Mb
                        'worker_number' => 20,
                        'max_messages' => 1000,
                    ],
                    'social_media_webhook_job' => [
                        'enable_coroutine' => true,
                        'connection' => 'social_media_webhook_job',
                        'consumer' => [
                            [
                                'queue' => null,
                                'option' => [
                                    'group_id' => $_dcConfig['queue_gid']['GID_crm_chat_webhook_dev'] ?? 'GID_crm_chat_webhook_dev',
                                    'tag' => '',
                                    'orderly' => false,
                                    'wait_seconds' => 1,
                                    'job_factory' => \common\library\queue_v2\job\SocialMediaWebhookJob::class,
                                ],
                            ],
                        ],
                        'sleep_seconds' => 1,
                        'memory_limit' => 128, // Mb
                        'worker_number' => 20,
                        'max_messages' => 1000,
                    ],
                    'mail_receive' => [
                        'enable_coroutine' => true,
                        'connection' => 'mail_receive',
                        'consumer' => [
                            [
                                'queue' => null,
                                'option' => [
                                    'group_id' => $_dcConfig['queue_gid']['GID_crm_mail_receive_job_test'] ?? 'GID_crm_mail_receive_job_test',
                                    'tag' => '',
                                    'orderly' => false,
                                    'wait_seconds' => 1,
                                ],
                            ]
                        ],
                        'sleep_seconds' => 1,
                        'memory_limit' => 128, // Mb
                        'worker_number' => 16,
                        'max_messages' => 100000,
                    ],
                    'statistic_job' => [
                        'enable_coroutine' => true,
                        'connection' => 'crontab_job',
                        'consumer' => [
                            [
                                'queue' => null,
                                'option' => [
                                    'group_id' => \common\library\queue_v2\QueueService::groupID($_dcConfig['queue_gid']['GID_crm_statistic_job_test'] ?? 'GID_crm_statistic_job_test'),
                                    'tag' => \common\library\queue_v2\QueueService::tagName('statistic'),
                                    'orderly' => true,
                                    'wait_seconds' => 1,
                                ],
                            ],
                        ],
                        'sleep_seconds' => 1,
                        'memory_limit' => 1024, // Mb
                        'worker_number' => 2,
                        'max_messages' => 1,
                        'finish_check' => 300, //s
                    ],
                    'swarm_job' => [
                        'enable_coroutine' => true,
                        'connection' => 'swarm_job',
                        'consumer' => [
                            [
                                'queue' => null,
                                'option' => [
                                    'group_id' => \common\library\queue_v2\QueueService::groupID($_dcConfig['queue_gid']['GID_crm_swarm_job_high_test'] ?? 'GID_crm_swarm_job_high_test'),
                                    'tag' => \common\library\queue_v2\QueueService::tagName('high'),
                                    'orderly' => true,
                                    'wait_seconds' => 1,
                                ],
                                'concurrent' => getenv("SWARM_JOB_HIGH_CONCURRENT") ?: 16,
                            ],
                            [
                                'queue' => null,
                                'option' => [
                                    'group_id' => \common\library\queue_v2\QueueService::groupID($_dcConfig['queue_gid']['GID_crm_swarm_job_low_test'] ?? 'GID_crm_swarm_job_low_test'),
                                    'tag' => \common\library\queue_v2\QueueService::tagName('low'),
                                    'orderly' => true,
                                    'wait_seconds' => 1,
                                ],
                                'concurrent' => getenv("SWARM_JOB_LOW_CONCURRENT") ?: 16,
                            ],
                            [
                                'queue' => null,
                                'option' => [
                                    'group_id' => \common\library\queue_v2\QueueService::groupID($_dcConfig['queue_gid']['GID_crm_swarm_job_cli_test'] ?? 'GID_crm_swarm_job_cli_test'),
                                    'tag' => \common\library\queue_v2\QueueService::tagName('alibaba'),
                                    'orderly' => true,
                                    'wait_seconds' => 1,
                                ],
                                'concurrent' => getenv("SWARM_JOB_ALIBABA_CONCURRENT") ?: 16,
                            ],
                        ],
                        'sleep_seconds' => 1,
                        'memory_limit' => 256, // Mb
                        'worker_number' => 8,
                        'max_messages' => 100000,
                    ],
                    'rule_job' => [
                        'enable_coroutine' => true,
                        'connection' => 'rule_job',
                        'consumer' => [
                            [
                                'queue' => null,
                                'option' => [
                                    'group_id' => \common\library\queue_v2\QueueService::groupID($_dcConfig['queue_gid']['GID_crm_default_job_high_test'] ?? 'GID_crm_default_job_high_test'),
                                    'tag' => \common\library\queue_v2\QueueService::tagName('high'),
                                    'wait_seconds' => 1,
                                ],
                            ],
                            [
                                'queue' => null,
                                'option' => [
                                    'group_id' => \common\library\queue_v2\QueueService::groupID($_dcConfig['queue_gid']['GID_crm_default_job_low_test'] ?? 'GID_crm_default_job_low_test'),
                                    'tag' => \common\library\queue_v2\QueueService::tagName('low'),
                                    'wait_seconds' => 1,
                                ],
                            ],
                            [
                                'queue' => null,
                                'option' => [
                                    'group_id' => \common\library\queue_v2\QueueService::groupID($_dcConfig['queue_gid']['GID_crm_swarm_job_low_test'] ?? 'GID_crm_swarm_job_low_test'),
                                    'tag' => \common\library\queue_v2\QueueService::tagName('swarm'),
                                    'wait_seconds' => 1,
                                ],
                                'concurrent' => getenv("SWARM_RULE_CONCURRENT") ?: 2,
                            ],
                        ],
                    ],
                    'alibaba_command' => [
                        'enable_coroutine' => true,
                        'connection' => 'command_job',
                        'consumer' => [
                            [
                                'queue' => null,
                                'option' => [
                                    'group_id' => \common\library\queue_v2\QueueService::groupID($_dcConfig['queue_gid']['GID_crm_command_job_alibaba_test'] ?? 'GID_crm_command_job_alibaba_test'),
                                    'tag' => \common\library\queue_v2\QueueService::tagName('alibaba'),
                                    'wait_seconds' => 1,
                                ],
                            ],
                        ],
                        'sleep_seconds' => 1,
                        'memory_limit' => 512, // Mb
                        'worker_number' => 4,
                        'max_messages' => 100,
                    ],
                    'delay_job' => [
                        'enable_coroutine' => true,
                        'connection' => 'delay_job',
                        'consumer' => [
                            [
                                'queue' => null,
                                'option' => [
                                    'group_id' => \common\library\queue_v2\QueueService::groupID($_dcConfig['queue_gid']['GID_crm_delay_job_notification_test'] ?? 'GID_crm_delay_job_notification_test'),
                                    'tag' => \common\library\queue_v2\QueueService::tagName('high'),
                                    'wait_seconds' => 1,
                                ],
                            ],
                        ],
                        'sleep_seconds' => 1,
                        'memory_limit' => 256, // Mb
                        'worker_number' => 8,
                        'max_messages' => 1000,
                    ],
                    'tms_delay_job' => [
                        'enable_coroutine' => true,
                        'connection' => 'delay_job',
                        'consumer' => [
                            [
                                'queue' => null,
                                'option' => [
                                    'group_id' => \common\library\queue_v2\QueueService::groupID($_dcConfig['queue_gid']['GID_crm_delay_job_tms_test'] ?? 'GID_crm_delay_job_tms_test'),
                                    'tag' => \common\library\queue_v2\QueueService::tagName('tms'),
                                    'wait_seconds' => 1,
                                ],
                            ],
                        ],
                        'sleep_seconds' => 1,
                        'memory_limit' => 256, // Mb
                        'worker_number' => 8,
                        'max_messages' => 1000,
                    ],
                    'tms_document_job' => [
                        'enable_coroutine' => true,
                        'connection' => 'default_job',
                        'consumer' => [
                            [
                                'queue' => null,
                                'option' => [
                                    'group_id' => \common\library\queue_v2\QueueService::groupID($_dcConfig['queue_gid']['GID_crm_tms_document_test'] ?? 'GID_crm_tms_document_test'),
                                    'tag' => \common\library\queue_v2\QueueService::tagName('tms_document'),
                                    'wait_seconds' => 1,
                                ],
                            ],
                        ],
                        'sleep_seconds' => 1,
                        'memory_limit' => 256, // Mb
                        'worker_number' => 4,
                        'max_messages' => 1000,
                    ],
                    'ames_job' => [
                        'enable_coroutine' => true,
                        'connection' => 'ames_job',
                        'consumer' => [
                            [
                                'queue' => null,
                                'option' => [
                                    'group_id' => \common\library\queue_v2\QueueService::groupID($_dcConfig['queue_gid']['GID_crm_default_job_high_test'] ?? 'GID_crm_default_job_high_test'),
                                    'tag' => \common\library\queue_v2\QueueService::tagName('high'),
                                    'wait_seconds' => 1,
                                ],
                            ]
                        ],
                        'sleep_seconds' => 1,
                        'memory_limit' => 96, // Mb
                        'worker_number' => 4,
                        'max_messages' => 1000,
                    ],
                    'prometheus_job' => [
                        'enable_coroutine' => true,
                        'connection' => 'default_job',
                        'consumer' => [
                            [
                                'queue' => null,
                                'option' => [
                                    'group_id' => \common\library\queue_v2\QueueService::groupID($_dcConfig['queue_gid']['GID_crm_prometheus_job_test'] ?? 'GID_crm_prometheus_job_test'),
                                    'tag' => \common\library\queue_v2\QueueService::tagName('prometheus'),
                                    'wait_seconds' => 1,
                                ],
                            ],
                        ],
                        'sleep_seconds' => 1,
                        'memory_limit' => 256, // Mb
                        'worker_number' => 4,
                        'max_messages' => 1000,
                    ],
                    'binlog_mail_job' => [
                        'enable_coroutine' => true,
                        'connection' => 'binlog_mail_job',
                        'consumer' => [
                            [
                                'queue' => null,
                                'option' => [
                                    'group_id' => \common\library\queue_v2\QueueService::groupID($_dcConfig['queue_gid']['GID_crm_binlog_mail_test'] ?? 'GID_crm_binlog_mail_test'),
                                    'tag' => \common\library\queue_v2\QueueService::tagName('tbl_mail|tbl_mail_tag_assoc||tbl_mail|tbl_mail_tag_assoc|for_grey'),
                                    'wait_seconds' => 0,
                                    'job_factory' => \common\library\swoole\job\MailConversationJob::class,
                                ],
                            ],
                        ],
                        'sleep_seconds' => 1,
                        'memory_limit' => 96, // Mb
                        'worker_number' => 4,
                        'max_messages' => 1000,
                    ],
                    'dev_job' => [
                        'enable_coroutine' => true,
                        'connection' => 'slow_job',
                        'consumer' => [
                            [
                                'queue' => null,
                                'option' => [
                                    'group_id' => \common\library\queue_v2\QueueService::groupID($_dcConfig['queue_gid']['GID_crm_default_job_low_test'] ?? 'GID_crm_default_job_low_test'),
                                    'tag' => \common\library\queue_v2\QueueService::tagName('dev'),
                                    'wait_seconds' => 1,
                                ],
                            ],
                        ],
                        'sleep_seconds' => 1,
                        'memory_limit' => 96, // Mb
                        'worker_number' => 4,
                        'max_messages' => 1000,
                    ],
                    'pg_binlog_to_biz_job' => [
                        'enable_coroutine' => true,
                        'connection' => 'pg_binlog_to_biz_job',
                        'consumer' => [
                            [
                                'queue' => null,
                                'option' => [
                                    'group_id' => \common\library\queue_v2\QueueService::groupID($_dcConfig['queue_gid']['GID_crm_pg_binlog_to_biz_job'] ?? 'GID_crm_pg_binlog_to_biz_job'),
                                    'tag' => \common\library\queue_v2\QueueService::tagName('pg_binlog_to_biz_tag'),
                                    'orderly' => false,
                                    'wait_seconds' => 1,
                                ],
                            ],
                        ],
                    ],
                    'ai_job' => [
                        'enable_coroutine' => true,
                        'connection' => 'ai_job',
                        'consumer' => [
                            [
                                'queue' => null,
                                'option' => [
                                    'group_id' => \common\library\queue_v2\QueueService::groupID($_dcConfig['queue_gid']['GID_crm_ai_job_test'] ?? 'GID_crm_ai_job_test'),
                                    'tag' => \common\library\queue_v2\QueueService::tagName('high'),
                                    'wait_seconds' => 1,
                                ],
                            ],
                            [
                                'queue' => null,
                                'option' => [
                                    'group_id' => \common\library\queue_v2\QueueService::groupID($_dcConfig['queue_gid']['GID_ai_task_job_dev'] ?? 'GID_ai_task_job_dev'),
                                    'tag' => \common\library\queue_v2\QueueService::tagName('okkiai_task'),
                                    'wait_seconds' => 1,
                                ],
                                // 指定并发个数
                                'concurrent' => getenv("OKKIAI_TASK_CONCURRENT") ?: 5,
                            ],
                        ],
                    ],
                    'ai_high_job' => [
                        'enable_coroutine' => true,
                        'connection' => 'ai_high_job',
                        'consumer' => [
                            [
                                'queue' => null,
                                'option' => [
                                    'group_id' => \common\library\queue_v2\QueueService::groupID($_dcConfig['queue_gid']['GID_crm_ai_high_job_test'] ?? 'GID_crm_ai_high_job_test'),
                                    'tag' => \common\library\queue_v2\QueueService::tagName('high'),
                                    'wait_seconds' => 0,
                                ],
                            ],
                        ],
                    ],
                    'marketing_automation_job' => [
                        'enable_coroutine' => true,
                        'connection' => 'marketing_automation_job',
                        'consumer' => [
                            [
                                'queue' => null,
                                'option' => [
                                    'group_id' => \common\library\queue_v2\QueueService::groupID($_dcConfig['queue_gid']['GID_crm_marketing_automation_test'] ?? 'GID_crm_marketing_automation_test'),
                                    'tag' => \common\library\queue_v2\QueueService::tagName('high'),
                                    'wait_seconds' => 1,
                                ],
                            ],
                        ],
                        'sleep_seconds' => 1,
                        'memory_limit' => 256, // Mb
                        'worker_number' => 4,
                        'max_messages' => 1000,
                    ],
                    'customer_job' => [
                        'enable_coroutine' => true,
                        'connection' => 'customer_job',
                        'consumer' => [
                            [
                                'queue' => null,
                                'option' => [
                                    'group_id' => \common\library\queue_v2\QueueService::groupID($_dcConfig['queue_gid']['GID_crm_customer_job_test'] ?? 'GID_crm_customer_job_test'),
                                    'tag' => \common\library\queue_v2\QueueService::tagName('high'),
                                    'wait_seconds' => 1,
                                ],
                            ],
                        ],
                        'memory_limit' => 512, // Mb
                        'worker_number' => 4,
                        'max_messages' => 1000,
                    ],
                    'ai_sdr_job' => [
                        'enable_coroutine' => true,
                        'connection' => 'ai_sdr_job',
                        'consumer' => [
                            [
                                'queue' => null,
                                'option' => [
                                    'group_id' => \common\library\queue_v2\QueueService::groupID($_dcConfig['queue_gid']['GID_crm_ai_sdr_job_test'] ?? 'GID_crm_ai_sdr_job_test'),
                                    'tag' => \common\library\queue_v2\QueueService::tagName('high'),
                                    'wait_seconds' => 1,
                                ],
                            ],
                        ],
                        'memory_limit' => 512, // Mb
                        'worker_number' => 4,
                        'max_messages' => 1000,
                    ],
                ],
            ],
        )
    )
);

