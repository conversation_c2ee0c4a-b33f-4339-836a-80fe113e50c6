<?php
/**
 * controller 和 action 请全小写
 * 只对action进行鉴权 （例如 /customer/delete）
 *     'customer' => array(
 *      'functional’ =>'',新增功能模块的配置
 *      'require' =>'privilege_name',
 *      'except' =>'privilege_name',
 *     'action'=>array(
 *          'require' =>'privilege_name',
 *          'except' =>'privilege_nam',
 *          'delete'=>'privilege_name'
 *      )
 *   )
 */
return array(
    //这下面是前端url-nav鉴权设置，会跟着前端的url改变
    //客户
    'customer' =>[
        'action' =>[
            'meeting' =>[
                'functional' => 'v5client.customer.meeting_timeline',
            ],
        ]

    ],

    //公费电话
    'pay_phone' =>[
        'action' =>[
            'list' =>[
                'functional' => 'v5client.customer.meeting_timeline',
            ]
        ],

    ],
    //多公海客户
    'customerpool' =>[
        'functional' =>[
            'v5client.customer.pool',
        ],
        'action' =>[
            'list' =>[
                'require' => 'v5boss.customer_pool',
            ]
        ],
    ],
    //设置
    'manage' => [
        'action' => [
            //客户管理-客户导出
            'customerexport' =>[
                'require' =>'v5boss.export',
            ],
            //设置-权限管理
            'permission' => [
                'require' => 'v5boss.permission',
            ],
            //设置-账号关联
            'relation' => [
                'require' => 'v5boss.relation',
            ],
            //设置-邮箱管理-邮件模板
            'emailsetmodel' =>[//
                'require' =>'v5boss.email.template',
            ],
            //设置-邮箱管理-邮箱绑定
            'emailbinduser' =>[//
                'require' =>'v5boss.email',
            ],
            //设置-客户设置
            'customer' =>[
                'require' => 'v5boss.customer',
            ],
            //设置-产品设置
            'product' => [
                'require' =>'v5boss.product',
            ],
            //设置-营销分配
            'allocate' =>[
                'require' => 'v5boss.allocate',
            ],
        ],
        'except'=>[],
    ],
    //设置-汇率设置
    'exchange' =>[
        'require' => 'v5boss.exchange',
    ],
    //产品管理
    'product' => [
        'require' => 'v5boss.product',
    ],
    //统计分析
    'statistic' =>[
        'require' => 'v5boss.statistic'
    ],
    //云盘
    'disk' =>[
        'require' => 'v5boss.disk',
    ],
    //通信录
    'contacts' =>[
        'require' => 'v5boss.contacts',
    ],
    //在线商店
    'accountinfo' =>[
        'require' => 'v5boss.accountinfo',
    ],
    //回收箱
    'recycle' =>[
        'require' => 'v5boss.recycle',
    ],
    //这部分是特殊的子界面设置
    //设置-邮箱管理
    'order' =>[
        'action' =>[
            //设置-订单设置-订单导出模板
            'setmodel' =>[
                'require' => 'v5boss.order.template',
            ],
            //设置-订单设置-订单状态设置
            'setstate' =>[
                'require' => 'v5boss.order',
            ],
            //设置-订单设置-订单单号设置
            'setnumber' =>[
                'require' => 'v5boss.order',
            ],
            //设置-订单设置-订单字段设置
            'setfield' =>[
                'require' => 'v5boss.order',
            ],
            //设置-订单设置-订单字段导出控制
            'setexport' =>[
                'require' => 'v5boss.order',
            ],

        ]
    ],

    'quotation' =>[
        'action' =>[
            //设置-报价单设置-报加单导出模板设置
            'setmodel' =>[
                'require' => 'v5boss.quotation.template',
            ],
            //设置-报价单设置-报价单状态设置
            'setstate' =>[
                'require' => 'v5boss.quotation',
            ],
            //设置-报价单设置-报价单单号设置
            'setnumber' =>[
                'require' => 'v5boss.quotation',
            ],
            //设置-报价单设置-报价单字段设置
            'setfield' =>[
                'require' => 'v5boss.quotation',
            ],
            //设置-报价单设置-报价字段导出控制
            'setexport' =>[
                'require' => 'v5boss.quotation',
            ],

        ]
    ],

    //下面是后端控制的权限，请严格控制写权限

    'customerread'=>[
        'action' => [
            'export' => [
                'require' => 'v5boss.export'
            ],
        ],
    ],
    'customerwrite'=>[
        'action' => [
            'editgroup' => [
                'require' => 'v5boss.customer'
            ],
            'setreference' => [
                'require' => 'v5boss.customer'
            ],
            'setcustomerlimit' => [
                'require' => 'v5boss.customer'
            ],
            'renameorigin' => [
                'require' => 'v5boss.customer'
            ],
            'addorigin' => [
                'require' => 'v5boss.customer'
            ],
            'deleteorigin' => [
                'require' => 'v5boss.customer'
            ],
            'createstatus' => [
                'require' => 'v5boss.customer'
            ],
            'alterstatus' => [
                'require' => 'v5boss.customer'
            ],
            'deletestatus' => [
                'require' => 'v5boss.customer'
            ],
            'addblacklist' => [
                'require' => 'v5boss.customer'
            ],
            'removeblacklist' => [
                'require' => 'v5boss.customer'
            ],
            'searchList' => [
                'require' => 'v5boss.customer'
            ],
        ],
    ],
    'companysettingwrite' => [
        'action' => [
            'updatefield' => [
                'require' => 'v5boss.customer'
            ],
            'addfield' =>[
                'require' => 'v5boss.customer'
            ],
            'deletefield' =>[
                'require' => 'v5boss.customer'
            ]
        ]
    ],
    'privilegewrite' => [
        'action' => [
            'setuserrole' =>[
                'require' => 'v5boss.permission'
            ]
        ]
    ],
    'manageread'=>[
        'action' => [
            'clientrelation' => [
                'require' => 'v5boss.relation'
            ],
        ]
    ],
    'managewrite'=>[
        'action' => [
            'bindinguser' => [
                'require' => 'v5boss.relation'
            ],
            'unbindinguser' => [
                'require' => 'v5boss.relation'
            ],
            'joinclient' =>[
                'require'=>'v5boss.organization',
            ],
            'setadmin' =>[
                'require'=>'v5boss.organization',
            ],
            'freezeuser' =>[
                'require'=>'v5boss.organization',
            ],
            'freezedeleteuser' => [
                'require'=>'v5boss.organization',
            ],
            'cancelinvite' =>[
                'require'=>'v5boss.organization',
            ],
        ]
    ],
    'mailtemplateread'=>[
        'require'=>'v5boss.email.template',
    ],
    'mailtemplatewrite' =>[
        'require'=>'v5boss.email.template',
    ],
    'productread' => [
        'require' => 'v5boss.product',
        'except' =>['attrtpl']
    ],
    'productwrite' => [
        'require' => 'v5boss.product',
    ],
    'statisticsread'=>[
        'require'=>'v5boss.statistic',
        'except'=>['customeroverview','mailoverview','orderoverview','pioverview','quotoverview','edmoverview','productoverview',
            'quotationrecently','barchart','linechart','customerpiechart','pirecently','orderrecently','departmentmemberstatistics',
            'departmentperformancestatistics','userworkstatistics','piechart','usercustomertotal'],
    ],
    'allocatewrite'=>[
        'require'=>'v5boss.allocate'
    ],
    'diskread'=>[
        'require'=>'v5boss.disk'
    ],
    'diskwrite'=>[
        'require'=>'v5boss.disk'
    ],
    'contactsread'=>[
        'require'=>'v5boss.contacts'
    ],
    'contactswrite'=>[
        'require'=>'v5boss.contacts'
    ],
    'recycleread'=>[
        'require'=>'v5boss.recycle'
    ],
    'recyclewrite'=>[
        'require'=>'v5boss.recycle'
    ],
    'quotationsettingread'=>[
        'action' =>[
            'templateinfo' =>[
                'require'=>'v5boss.quotation.template'
            ]
        ]
    ],
    'quotationsettingWrite' =>[
        'action' =>[
            'addfield' =>[
                'require'=>'v5boss.quotation'
            ],
            'updatefield' =>[
                'require'=>'v5boss.quotation'
            ],
            'deletefield' =>[
                'require'=>'v5boss.quotation'
            ],
            'updatefield' =>[
                'require'=>'v5boss.quotation'
            ],
            'addstatus' =>[
                'require'=>'v5boss.quotation'
            ],
            'deletestatus' =>[
                'require'=>'v5boss.quotation'
            ],
            'updatestatus' =>[
                'require'=>'v5boss.quotation'
            ],
            'updatefieldexportable' =>[
                'require'=>'v5boss.quotation'
            ],
            'delexceltemplate' =>[
                'require'=>'v5boss.quotation.template'
            ],
            'setexceltemplate' =>[
                'require'=>'v5boss.quotation.template'
            ]
        ],
    ],
    'ordersettingread'=>[
        'action' =>[
            'templateinfo' =>[
                'require'=>'v5boss.order.template'
            ]
        ]
    ],
    'ordersettingWrite' =>[
        'action' =>[
            'addfield' =>[
                'require'=>'v5boss.order'
            ],
            'updatefield' =>[
                'require'=>'v5boss.order'
            ],
            'deletefield' =>[
                'require'=>'v5boss.order'
            ],
            'updatefield' =>[
                'require'=>'v5boss.order'
            ],
            'addstatus' =>[
                'require'=>'v5boss.order'
            ],
            'deletestatus' =>[
                'require'=>'v5boss.order'
            ],
            'updatestatus' =>[
                'require'=>'v5boss.order'
            ],
            'updateorderperformance' =>[
                'require'=>'v5boss.order'
            ],
            'updatefieldexportable' =>[
                'require'=>'v5boss.order'
            ],
            'delexceltemplate' =>[
                'require'=>'v5boss.order.template'
            ],
            'setexceltemplate' =>[
                'require'=>'v5boss.order.template'
            ]
        ],
    ],
    'pitemplateread'=>[
        'require'=>'v5boss.template',
    ],
    'pitemplatewrite' =>[
        'require'=>'v5boss.template',
    ],
    'exchangeratewrite' =>[
        'require'=>'v5boss.exchange',
    ],
    'customerpoolwrite' =>[
        'functional' =>[
            'v5client.customer.pool',
        ]
    ],
    'customerpoolread' =>[
        'functional' =>[
            'v5client.customer.pool',
        ]
    ],
    'edmwrite' => [
        'action' => [
            'exportedmmaillist' => ['require' => 'v5client.edm.contacts_export'],
        ],
    ],

    // 线索查看
    'leadsread' => [
        'require' => 'crm.lead.private.view',
        'action' => [
            // 私海线索
            'info' => [
                'require' => 'crm.lead.private.view'
            ],
            // 公海线索
            'pool' => [
                'require' => 'crm.lead.public.view'
            ]
        ]
    ],

    // 商机查看
    'opportunityread' => [
        'require' => 'crm.opportunity.view',
    ],

    // 商机操作
    'opportunitywrite' => [
        'require' => 'crm.opportunity.view',
        'action' => [
            'create' => [
                'require' => 'crm.opportunity.create'
            ],
            'edit' => [
                //'require' => 'crm.opportunity.edit'
            ],
            'changestage' => [
                //'require' => 'crm.opportunity.edit'
            ],
            'succeed' => [
                //'require' => 'crm.opportunity.edit'
            ],
            'fail' => [
                //'require' => 'crm.opportunity.edit'
            ],
            'addfile' => [
                //'require' => 'crm.opportunity.edit'
            ],
            'removefile' => [
                //'require' => 'crm.opportunity.edit'
            ],
            'enable' => [
                //'require' => 'crm.opportunity.edit'
            ],
            'disable' => [
                //'require' => 'crm.opportunity.edit'
            ],
            'removetrail' => [
                'require' => 'crm.opportunity.trail.create'
            ],
            'transfer' => [
                'require' => 'crm.opportunity.transfer'
            ],
            'addhandler' => [
                'require' => 'crm.opportunity.member.manage'
            ],
            'removehandler' => [
                'require' => 'crm.opportunity.member.manage'
            ],
        ]
    ],



    // 线索管理
    'leadswrite' => [
        'require' => 'crm.lead.private.view',
        'action' => [
            // 创建线索
            'create' => [
                'require' => 'crm.lead.private.create'
            ],
            // 修改线索
            'edit' => [
                'require' => 'crm.lead.private.edit'
            ],
            // 转化线索
            'conversion' => [
                'require' => 'crm.lead.private.conversion'
            ],
            // 转移线索
            'transfer' => [
                'require' => 'crm.lead.private.transfer'
            ],
            // 删除线索
            'remove' => [
                'require' => 'crm.lead.private.remove'
            ],
            // 分配公海线索
            'distribute' => [
                'require' => 'crm.lead.public.distribute'
            ],
            // 领取公海线索
            'receive' => [
                'require' => 'crm.lead.public.receive'
            ],
        ]
    ],
);
