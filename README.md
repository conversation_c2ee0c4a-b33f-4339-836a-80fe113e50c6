# CRM主站

### 开发环境搭建

1. 安装docker
2. 拉取PHP开发环境相关镜像
```shell
$ docker pull xiaoman-registry-vpc.cn-hangzhou.cr.aliyuncs.com/xiaoman-php/pod_agent:release
$ docker pull xiaoman-registry-vpc.cn-hangzhou.cr.aliyuncs.com/xiaoman-php/php8:rockylinux
$ docker pull xiaoman-registry-vpc.cn-hangzhou.cr.aliyuncs.com/xiaoman-php/nginx-proxy:fpm-supported
```
如果没有镜像仓库权限，可以直接下载:
* https://gitlab.xiaoman.cc/vincentzhang/superpower/-/raw/master/release/php8.tar
* https://gitlab.xiaoman.cc/vincentzhang/superpower/-/raw/master/release/nginx-proxy.tar

然后导入镜像：
```shell
$ docker load -i php8.tar
$ docker load -i nginx-proxy.tar
```

3. 启动本地开发环境通用镜像及网络配置
    * $ docker network create nginx-proxy
    * $ docker run -d -p 80:80 --name nginx-proxy --net nginx-proxy --restart always -v /var/run/docker.sock:/tmp/docker.sock xiaoman-registry-vpc.cn-hangzhou.cr.aliyuncs.com/xiaoman-php/nginx-proxy:fpm-supported
4. 拉取代码
    * $ git clone ssh://*********************:50008/php/PHP-CRM.git
    * $ git clone ssh://*********************:50008/php/yii.git
    * $ cd PHP-CRM
    * $ mkdir -p protected/runtime
    * $ docker-compose up --force-recreate --remove-orphans -d # 启动镜像
5. 配置本机host
    * 127.0.0.1 local.xiaoman.dev.cn
6. 测试站点是否正常
    * curl local.xiaoman.dev.cn
7. docker相关命令
    * 一个使用Docker容器的应用，通常由多个容器组成。使用Docker
      Compose，不再需要使用shell脚本来启动容器。在配置文件中，所有的容器通过services来定义，然后使用docker-compose脚本来启动，停止和重启应用，和应用中的服务以及所有依赖服务的容器。完整的命令列表如下：

<pre>
        build 构建或重建服务
        help 命令帮助
        kill 杀掉容器
        logs 显示容器的输出内容
        port 打印绑定的开放端口
        ps 显示容器
        pull 拉取服务镜像
        restart 重启服务
        rm 删除停止的容器
        run 运行一个一次性命令
        scale 设置服务的容器数目
        start 开启服务
        stop 停止服务
        up 创建并启动容器
</pre>

8. 常用docker命令

<pre>
创建容器: docker-compose up --force-recreate --remove-orphans  前台启动(-d 后台启动)
进入容器: docker exec -it local.xiaoman.dev.cn /bin/ash
开启容器: docker-compose start
停止容器: docker-compose stop
删除容器: docker-compose down
</pre>

## Git管理

### 分支管理

#### master 分支

<pre>
master 为主分支，也是用于部署生产环境的分支，确保master分支稳定性
master 分支一般由release以及hotfix分支合并，任何时间都不能直接修改代码
</pre>

#### release/dev

<pre>
release/dev为预发布分支，准上线分支
始终保持最新完成以及bug修复后的代码
一般开发完成并进行过一轮的代码,新功能迭代完成后将要灰度或发版的
多数的feature分支都是基于 release/dev分支下创建的
提交会自动构建到k.dev.xiaoman.cn
当有一组feature开发完成，首先会合并到release/dev分支，进入预发测试
如果测试过程中若存在bug需要修复，则直接由开发者在release分支修复并提交。
当测试完成之后，合并release分支到master，此时master为最新代码，用作上线。
</pre>

#### feature 分支

<pre>
开发新功能时，以release或master为基础创建feature分支
分支命名: feature/ 开头的为特性分支
命名规则: feature/feature_name.k* 例如:hotfix/alibaba-v3.k6
对应后缀有k2-k9 ,提交会自动构建到k*.dev.xiaoman.cn
</pre>

#### hotfix 分支

<pre>
线上出现紧急问题时，需要及时修复，随时可以发版的
以master分支为基线，创建hotfix分支，修复完成后，需要合并到master分支
分支命名: hotfix/ 开头的为修复分支.hotfix  例如:hotfix/alibaba-v3.hotfix
</pre>

#### bugfix 分支

<pre>
主要处理线上工单问题及小需求
以master分支为基线，创建bugfix分支，修复完成后，需要合并到master分支
一般固定每周四发版本
分支命名: bugfix/ 上线日期.bugfix  例如:bugfix/1102.bugfix
</pre>

### 开发流程

* Workflow分支管理：https://xmkm.yuque.com/armee3/dzbqgm/fyfrr8